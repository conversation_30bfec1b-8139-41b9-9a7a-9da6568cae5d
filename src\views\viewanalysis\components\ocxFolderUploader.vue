<template>
    <div class="oxcFolderUplodaer">
        <div class="uploadContainer">
            <div @mouseover="overShow" @mouseout="outHide" class="uploadSelectBtn">
                <Button class="w_80" size="small" type="success">导入</Button>
                <div class="uploadSelect" v-show="uploadSelect">
                    <Button class="w_80" size="small" @click="openFileDlg('video')">文件导入</Button>
                    <Button class="w_80 nopadding" size="small" @click="openFileDlg('folder')">文件夹导入</Button>
                </div>
            </div>
        </div>
        <!-- <object id="UIOCX" class="UIOCXFILEUP" type="applicatin/x-firebreath" align="center" width="100%" style="width:100%">
            <param name="onload" value="pluginLoaded">
        </object> -->
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
    name: "ocxFolderUploader",
    props: {
        ocxFileGet: {
            type: Function,
            default: null
        }
    },
    data() {
        return {
            uploadSelect: false,
            uploadExeId: "",
            pfsFileFolder: {}, // pfs上传文件路径
            filetypeValue: "video",
            fileNameGet: ""
        };
    },
    computed:{
      ...mapGetters({
          userInfo: "userInfo"
      }),
    },
    methods: {
        selectSOH() {
            this.uploadSelect = !this.uploadSelect;
        },
        overShow() {
            this.uploadSelect = true;
        },
        outHide() {
            this.uploadSelect = false;
        },
        openFileDlg(type) {
            if (type === "folder") {
                var openDir = {
                    defpath: window.diskDirectory,
                    getfilelist: true,
                    depth: 7
                };
                this.$H5PlayerAPI.openLocalSelectFolderDlg(openDir, res => {
                    console.log("openLocalSelectFolderDlg", res);
                    console.log("this.serverIps :", window.serverIps);
                    var filelist = [];
                    if (res.filelist.length > 1000) {
                        this.$Message.warning(
                            "上传失败,最大持支持1000文件上传"
                        );
                        return false;
                    } else {
                        if (res.filelist && res.filelist.length > 0) {
                            for (let i = 0; i < res.filelist.length; i++) {
                                //平均分配
                                var serverIp = window.serverIps[
                                    i % window.serverIps.length
                                ];
                                var selUpFilePath = res.filelist[i].filepath;
                                console.log(
                                    `${i}-------${serverIp} -------${selUpFilePath}`
                                );
                                if (selUpFilePath.trim() != "") {
                                    selUpFilePath = selUpFilePath.replace(
                                        /\\/g,
                                        "/"
                                    );
                                } else {
                                    return;
                                }
                                //过滤文件类型
                                var fileNameArray = selUpFilePath.split(".");
                                var fileTypeStrTxt = fileNameArray[fileNameArray.length - 1];
                                var suffix = ".avi.wmv.mbf.mp4.bmp.ts"
                                if (suffix.indexOf(fileTypeStrTxt)  == -1) {
                                    continue ;
                                }
                                
                                var dest = {
                                    ip: serverIp,
                                    port: window.serverPort,
                                    path: `${window.diskDirectory}${this.userInfo.username}/`
                                };
                                filelist.push({
                                    filepath: Toolkits.replacehttpparam(
                                        res.filelist[i].filepath
                                    ),
                                    size: res.filelist[i].size,
                                    dest: dest
                                });
                            }
                        }
                        var filepath = res.path.replace(/\\/g, "/");
                        var fileNameArray = filepath.split("/");
                        var fileNameGet =
                            fileNameArray[fileNameArray.length - 1]; ////视频名称
                        this.fileNameGet = fileNameGet;
                        var uploadDefaultInfo = {
                            src: {
                                filetype: "folder",
                                streamtrun: true,
                                filepath: Toolkits.replacehttpparam(filepath),
                                filelist: filelist
                            },
                            dest: {
                                type: "fileserver",
                                ip: window.serverIps[0],
                                port: window.serverPort,
                                path: `${window.diskDirectory}${this.userInfo.username}/`, //pfsStorageRootDir + "/",
                                storagemode: 0
                            }
                        };
                        console.log("文件夹上传参数==", uploadDefaultInfo);
                        this.startUpload(
                            "folder",
                            uploadDefaultInfo,
                            fileNameGet
                        );
                    }
                });
                return;
            }
            var opnenFileDlg = {
                defext: "",
                filename: "",
                flags: 0x1206,
                filter: window.fileStructureFormat,
                title: "选择上传视频"
            };
            this.$H5PlayerAPI.openSaveFileDlg(opnenFileDlg).then(res => {
                console.log("---------openSaveFileDlg----------", res);
                //调用ocx选择文件
                if (this.uploadExeId != "") {
                    this.$H5PlayerAPI.stopUploadPFSFile(this.uploadExeId, true);
                }
                this.uploadExeId = "";
                console.log(
                    "--------------------------------h5上传 start-----------------------------------"
                );
                try {
                    if (res.error < 0) {
                        return;
                    }
                    //替换路径中的双斜杠
                    var selUpFiles = JSON.parse(res.files);
                    for (let i = 0; i < selUpFiles.length; i++) {
                        //平均分配
                        var serverIp = window.serverIps[i % window.serverIps.length];

                        const file = selUpFiles[i];
                        var selUpFilePath = file.filename;
                        console.log(
                            "OCX选择文件：" + JSON.stringify(selUpFilePath)
                        );
                        if (selUpFilePath.trim() != "") {
                            selUpFilePath = selUpFilePath.replace(/\\/g, "/");
                        } else {
                            return;
                        }
                        var fileNameArray = selUpFilePath.split("/");
                        var fileNameGet =
                            fileNameArray[fileNameArray.length - 1]; ////视频名称
                        this.fileNameGet = fileNameGet;
                        fileNameArray = fileNameGet.substr(
                            0,
                            fileNameGet.lastIndexOf(".")
                        );
                        var fileTypeStrTxt =
                            fileNameArray[fileNameArray.length - 1];
                        var fileNameSuffix = fileTypeStrTxt.toLowerCase();
                        console.log("替换反斜杠后文件路径：" + selUpFilePath); //文件绝对路径
                        var uploadDefaultInfo = {
                            src: {
                                filetype: "video",
                                streamtrun: true,
                                filepath: Toolkits.replacehttpparam(selUpFilePath)
                            },
                            dest: {
                                type: "fileserver",
                                ip: serverIp,
                                port: window.serverPort,
                                path: `${window.diskDirectory}${this.userInfo.username}/`,
                                storagemode: 0
                            }
                        };
                        console.log("单文件上传参数:", uploadDefaultInfo);
                        this.startUpload(
                            fileNameSuffix,
                            uploadDefaultInfo,
                            fileNameGet
                        );
                    }
                } catch (e) {
                    return;
                }
            });
        },
        startUpload(fileNameSuffix, uploadDefaultInfo, fileNameGet) {
            //默认是上传视频
            console.log(
                "---------startUpload--------",
                fileNameSuffix,
                uploadDefaultInfo
            );
            var self = this;
            if (
                fileNameSuffix == "gif" ||
                fileNameSuffix == "png" ||
                fileNameSuffix == "jpg" ||
                fileNameSuffix == "jpeg"
            ) {
                uploadDefaultInfo.src.filetype = "img"; //上传图片
                uploadDefaultInfo.src.streamtrun = false;
                self.filetypeValue = "img";
            } else {
                self.filetypeValue = "video";
            }
            uploadDefaultInfo.dest.path =
                uploadDefaultInfo.dest.path +
                uploadDefaultInfo.src.filetype +
                "/";
            console.log(uploadDefaultInfo);

            Toolkits.jsonpAjaxChar(
                window.startuploadUrl.split("?")[0],
                JSON.stringify(uploadDefaultInfo),
                "GB2312"
            ).then(data => {
                console.log("----------startupload----------", data);
                var resultParam = JSON.parse(data.startupload.result).info;
                // 公开上传资源
                resultParam.fileName = fileNameGet;
                var emitParams = {
                    info: {
                        dest: [resultParam],
                        src: [uploadDefaultInfo.src]
                    }
                };
                console.log("------------resultParam------------", emitParams);

                // this.$emit("ocxFileGet", emitParams);
                //直接调用
                self.ocxFileGet(emitParams);
                self.handleSuceess({
                    path: "PFS:" + resultParam.path,
                    category: uploadDefaultInfo.src.filetype,
                    size: resultParam.totalsize,
                    localCtime: "",
                    fileName: this.fileNameGet
                });
            });
        },
        handleSuceess: function(fileParam) {
            //var length = this.resourceList.length;
            if (fileParam != "") {
                //this.resourceList[length - 1].url = url;
                this.$emit("addResource", fileParam);
            } else {
                this.$Message.warning("上传失败");
                //this.resourceList.pop();
            }
        },
    }
};
</script>
<style>
.UIOCXFILEUP {
    position: absolute;
    left: -999px;
    width: 1px;
}
.uploadSelectBtn {
    display: inline-block;
}
.nopadding {
    padding: 0 !important;
}
.uploadContainer {
    height: 90px;
}
.uploadSelect {
    width: 60px;
    height: 52px;
}
.uploadFile,
.uploadFolder {
    height: 26px;
    min-width: 70px;
    text-align: center;
    line-height: 30px;
    border-radius: 3px;
}
.w_80 {
  width: 80px;
}
</style>
