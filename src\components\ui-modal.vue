<template>
  <div class="ui-modal">
    <Modal
      v-model="modalShow"
      :class="computedClass"
      :title="title"
      :width="width"
      :styles="styles"
      :footer-hide="footerHide"
      :mask-closable="false"
      :transfer="false"
      :transition-names="[]"
      :fullscreen="fullScreen"
      :closable="closable"
      :mask="mask"
      :class-name="className"
      :draggable="draggable"
      sticky
      reset-drag-position
      @on-cancel="onCancel"
      @on-visible-change="onVisibleChange"
    >
      <template #header>
        <div class="title">
          <div class="left"></div>
          <div class="content">{{ title }}</div>
          <div class="right"></div>
        </div>

        <Icon v-if="hasFull" type="md-expand" class="full fr pointer" title="全屏" @click.stop="full" />
        <slot name="header"></slot>
      </template>
      <slot name="input"></slot>
      <slot></slot>
      <template #footer>
        <slot name="footer">
          <Button @click="onCancel" class="plr-30">取 消</Button>
          <Button type="primary" @click="$emit('query')" :loading="loading" class="plr-30">确 定</Button>
        </slot>
      </template>
      <template #close>
        <i class="icon-font icon-guanbi close"></i>
      </template>
    </Modal>
  </div>
</template>
<style lang="less" scoped>
[data-theme='light'] {
  .content {
    background-image: url('../assets/img/modal-title-bg-light.png') !important;
  }
  .left {
    background-image: url('../assets/img/modal-title-left-light.png') !important;
  }
  .right {
    background-image: url('../assets/img/modal-title-right-light.png') !important;
  }
}
[data-theme='deepBlue'] {
  .content {
    background-image: url('../assets/img/modal-title-bg-deep-blue.png') !important;
  }
  .left {
    background-image: url('../assets/img/modal-title-left-deep-blue.png') !important;
  }
  .right {
    background-image: url('../assets/img/modal-title-right-deep-blue.png') !important;
  }
}
.ui-modal {
  .full {
    font-size: 18px;
    margin-right: 30px;
  }
  .drag-modal {
    @{_deep}.ivu-modal-wrap {
      display: inline-block;
      .ivu-modal {
        display: flex;
        justify-content: center;
        width: 100% !important;
        top: 100px;
      }
    }
  }
  @{_deep}.ivu-modal {
    .ivu-modal-content {
      border-radius: 4px;
      opacity: 1;
    }
    .ivu-modal-close {
      position: absolute;
      right: -29px;
      top: -29px;
      width: 24px;
      //有冲突
      .icon-guanbi.close {
        font-size: 24px;
        color: var(--color-modal-close);
      }
    }
    .ivu-modal-header {
      .title {
        text-align: center;
        position: absolute;
        top: -25px;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        z-index: 100;
        .content {
          color: #fff;
          background-image: url('../assets/img/modal-title-bg.png');
          background-size: 100% 100%;
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          text-align: center;
          min-width: 120px;
          font-size: 18px;
          font-weight: 700;
        }
        .left {
          width: 100px;
          height: 40px;
          background-image: url('../assets/img/modal-title-left.png');
          background-size: 100% 100%;
        }
        .right {
          width: 100px;
          height: 40px;
          background-image: url('../assets/img/modal-title-right.png');
          background-size: 100% 100%;
        }
      }
    }

    .ivu-modal-footer {
      padding: 0;
      height: 66px;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      border-top: 1px solid var(--border-modal-footer);
      opacity: 1;
      button + button {
        margin-left: 10px;
      }
    }
  }
  // @{_deep} .ivu-modal-wrap{
  //   z-index: 99999 !important;
  // }
}
</style>
<script>
export default {
  data() {
    return {
      modalShow: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    full() {
      this.$emit('full');
    },
    onCancel() {
      this.modalShow = false;
      this.$emit('onCancel');
    },
    onVisibleChange(val) {
      this.$emit('on-visible-change', val);
    },
  },
  watch: {
    modalShow(val) {
      this.$emit('input', val);
    },
    value: {
      handler(val) {
        this.modalShow = val;
      },
      immediate: true,
    },
  },
  computed: {
    computedClass() {
      return ['modal', this.draggable ? 'drag-modal' : ''];
    },
  },
  props: {
    value: Boolean,
    title: {},
    width: {},
    styles: {},
    closable: {
      default: true,
    },
    footerHide: {
      default: false,
    },
    mask: {
      default: true,
    },
    fullScreen: {
      default: false,
    },
    hasFull: {
      default: false,
    },
    draggable: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: '',
    },
  },
  components: {},
};
</script>
