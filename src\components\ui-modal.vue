<template>
  <div class="ui-modal">
    <!-- :styles="stylesObj"  -->
    <Modal
      ref="modal"
      v-model="modalShow"
      :width="width"
      :styles="stylesObj"
      :loading="loading"
      :z-index="zIndex"
      :ok-text="okText"
      :closable="closable"
      :mask="mask"
      :mask-closable="maskClosable"
      :footer-hide="footerHide"
      :transfer="transfer"
      v-bind="$attrs"
      :class="listContent && 'list-content'"
      class="modal"
      class-name="vertical-center-modal"
      @on-cancel="onCancel"
      @on-ok="onOk"
      @upStepHandle="upStepHandle"
    >
      <p slot="header" class="header">
        <span>{{ title }}</span>
        <slot name="header"></slot>
      </p>
      <slot name="input"></slot>
      <slot></slot>
      <div slot="footer">
        <Button class="btn cancel" type="default" ghost @click="onCancel"
          >取消</Button
        >
        <Button v-if="upStenBtnShow" type="primary" @click="upStepHandle"
          >上一步</Button
        >
        <Button :loading="loading" type="primary" class="btn ok" @click="onOk">
          {{ loading ? "提交中" : okText || confirmText }}
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import { Modal } from "view-design";

export default {
  props: {
    ...Modal.props,
    stepCurrent: {
      default: 0,
    },
    stepList: {
      default: () => [],
    },
    // 次宽度可直接写px单位数值，最终转换为rem
    rWidth: {
      type: [String, Number],
      default: 0,
    },
    // 是否为列表弹框
    listContent: {
      type: Boolean,
      default: false,
    },
    maskClosable: {
      default: false,
    },
    cancelBtn: {
      type: Boolean,
      default: true,
    },
    transfer: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      modalShow: false,
    };
  },
  computed: {
    upStenBtnShow() {
      if (this.stepCurrent === 1 || this.stepCurrent === 0) {
        return false;
      } else {
        return true;
      }
    },
    confirmText() {
      if (this.stepCurrent !== 0) {
        if (this.stepCurrent === this.stepList.length) {
          return "确定";
        } else {
          return "下一步";
        }
      } else {
        return "确定";
      }
    },
    stylesObj() {
      let stylesObj = {};
      if (this.rWidth) {
        const rWidth = parseFloat(this.rWidth);
        if (isNaN(rWidth)) {
          stylesObj.width = "5rem";
        } else {
          stylesObj.width = parseFloat(this.rWidth) / 192 + "rem";
        }
      } else {
        stylesObj = this.styles;
      }
      return stylesObj;
    },
  },
  watch: {
    modalShow(val) {
      this.$emit("input", val);
    },
    value: {
      handler(val) {
        this.modalShow = val;
      },
      immediate: true,
    },
  },
  methods: {
    full() {
      this.$emit("full");
    },
    onCancel() {
      if (this.cancelBtn) {
        this.modalShow = false;
      }
      this.$emit("onCancel");
    },
    onOk() {
      this.$emit("onOk");
    },
    upStepHandle() {
      this.$emit("upStepHandle");
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/i_model";
</style>
