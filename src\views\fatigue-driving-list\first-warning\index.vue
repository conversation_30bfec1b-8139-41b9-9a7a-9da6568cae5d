<template>
    <div class="fatigue-driving">
        <div class="search-bar">
            <searchForm @search="searchHandle"></searchForm>
        </div>
        <div class="fatigue-driving-content">
            <div class="fatigue-driving-list">
                <div class="fatigue-driving-card">
                    <listCard class="listCard" v-for="(item,index) in dataList" :key="index" :itemInfo="item" :showDetail="true"
                        @refresh="queryList" />
                    <ui-empty v-if="(!dataList || !dataList.length) && !loading" />
                </div>
                <ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize"
                    :page-size-opts="[15, 30, 60, 120]" @pageChange="pageChange" @pageSizeChange="pageSizeChange">
                </ui-page>
                <ui-loading v-if="loading" />
            </div>
        </div>
    </div>
</template>

<script>
    import { fatigueDrivingList } from "@/api/monographic/fatigue-driving";
    import searchForm from '../components/search-form.vue'
    import listCard from '../components/list-card.vue'

    export default {
        components: { searchForm, listCard },
        data() {
            return {
                loading: false,
                searchForm: {},
                dataList: [],
                pageInfo: {
                    pageNumber: 1,
                    pageSize: 15,
                },
                total: 0
            }
        },
        methods: {
            searchHandle(searchForm) {
                this.searchForm = searchForm
                this.pageInfo.pageNumber = 1;
                this.queryList();
            },
            /**
             *
             * @param {*} page 详情翻页
             */
            queryList(page = false) {
                this.dataList = [];
                this.loading = true;
                let params = {
                    alarmSt: this.searchForm.startDate,
                    alarmEt: this.searchForm.endDate,
                    alarmCountType: 1,
                    vehicleType: this.searchForm.vehicleType,
                    // associationIdCardStatus: this.searchForm.associationIdCardStatus,
                    // idCardNo: this.searchForm.idCardNo,
                    ...this.pageInfo,
                };
                fatigueDrivingList(params)
                    .then((res) => {
                        this.dataList = res.data.entities;
                        this.total = res.data.total;
                        this.$forceUpdate();
                    })
                    .finally(() => {
                        this.loading = false;
                    });
            },
            // 页数改变
            pageChange(size) {
                this.pageInfo.pageNumber = size;
                this.queryList();
            },
            // 页数量改变
            pageSizeChange(size) {
                this.pageInfo.pageNumber = 1;
                this.pageInfo.pageSize = size;
                this.queryList();
            }
        }
    }
</script>

<style scoped lang="less">
    .fatigue-driving {
        position: relative;
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        padding: 20px 20px 0 20px;

        .fatigue-driving-content {
            flex: 1;
            position: relative;
            overflow: hidden;

            .fatigue-driving-list {
                height: 100%;
                display: flex;
                flex-direction: column;
                overflow: auto;

                .fatigue-driving-card {
                    flex: 1;
                    display: flex;
                    overflow: auto;
                    flex-wrap: wrap;
                    position: relative;
                    align-content: flex-start;
                }

                .listCard {
                    width: calc(~'20% - 10px');
                    margin-right: 10px;
                    margin-bottom: 12px;
                    &:hover {
                        border: 1px solid #2c86f8;
                    }
                }
            }
        }
    }
</style>