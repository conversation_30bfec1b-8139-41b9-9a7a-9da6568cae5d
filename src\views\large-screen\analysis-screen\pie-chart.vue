<!--
    * @FileDescription: 价值数据有效率
    * @Author: H
    * @Date: 2024/04/29
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="chart">
        <p class="chart-title" v-if="title">{{ title }} <span>
            <count-to :start-val="0" :end-val="totalCount" :duration="1000"></count-to>
        </span> </p>
        <div id="pie" class="pie" ref="pie"></div>
        <div class="pie-icon">
            <div class="pie-content">
                <img :src="imgList[urlType]" alt="">
            </div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import CountTo from 'vue-count-to';
export default {
    components: {
        CountTo
    },
    props: {
        urlType: {
            type: Number,
            default: 0
        },
        totalCount: {
            type: Number,
            default: 0
        },
        picData: {
            type: Array,
            default: () => {
                let list = [
                    { value: '75', name: '有效' },
                    { value: '25', name: '无效' },
                ];
                return list
            }
        },
        title: {
            type: String,
            default: ''
        }
    },  
    data() {
        return {
            myEchart: null,
            imgList: {
                0: require(`@/assets/img/screen/jxsj.png`),
                1: require(`@/assets/img/screen/jqxx.png`),
                2: require(`@/assets/img/screen/tjxx.png`),
                3: require(`@/assets/img/screen/dwbk.png`),
                4: require(`@/assets/img/screen/equipment.png`),
            }
        }
    },
    mounted() {
    },
    methods: {
        init(list) {
            this.pieChart(list)
        },
        pieChart(list) {
            this.myEchart = echarts.init(this.$refs.pie)
            let option = {
                legend: {
                    bottom: '1%',
                    left: 'center',
                    icon: 'circle',
                    itemWidth: 10,
                    itemHeight: 10,
                    itemStyle: {
                        borderRadius: 5,
                    },
                    textStyle: {
                        color: '#567BBB'
                    }
                },
                grid: {
                    top: '15px',
                    left: '30px',
                    right: '20px',
                    bottom: '30px'
                },
                series: [
                    {
                        name: '解析数据总量',
                        type: 'pie',
                        radius: [ '60%', '75%' ],
                        data: list,
                        label: {
                            show: true,
                            // formatter: "{d}%\n" + "{b}",
                            formatter: (params) => {
                                const name = params.name;
                                return `{f|${params.data.value}%}\n{t|${name}}`
                            },
                            rich: {
                                t: {
                                    color: '#FFC963',
                                    fontSize: 12,

                                },
                                f: {
                                    color: '#FFFFFF',
                                    fontSize: 16,
                                }
                            }
                        },
                        itemStyle: {
                            
                            normal: {
                                color: (params) => {
                                    let colorList = [
                                        '#00E4EF', '#FFC963', '#4FAEFF'
                                    ]
                                    return colorList[params.dataIndex % colorList.length]
                                }
                            }
                        }
                    }
                ]
            };
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .chart-title{
        position: absolute;
        right: 0;
        font-size: 14px;
        font-weight: 400;
        color: #27B5FF;
        span{
            color: #F1FCFF;
            font-size: 22px;
            font-weight: 700;
            text-shadow: 0 0 10px #0988FF;
        }
    }
    .pie{
        height: 100%;
        width: 100%; 
    }
    .pie-icon{
        position: absolute;
        width: 98px;
        height: 98px;
        border: 1px solid rgba(211,235,255, .2);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: center;
        align-items: center;
        .pie-content{
            width: 80px;
            height: 80px;
            background: rgba(211, 235, 255, .2);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
</style>