<template>
  <div class="area-dom-wrapper" @click="handleItem">
    <div class="header">
      <div v-if="type == 'fatigueDriving'">
        <i class="iconfont icon-jingqing"></i>
        最近预警{{ thematicItem.cdAlarmVehicle ? "连续疲劳驾驶" : "" }}
      </div>
      <div v-if="type == 'compusControl'">
        <i class="iconfont icon-jingqing"></i>
        陌生人员
      </div>
      <i class="iconfont icon-close" @click.stop="handleClose"></i>
    </div>
    <listCard
      v-if="type == 'fatigueDriving'"
      class="listCard"
      :itemInfo="thematicItem"
      theme="transparent"
    />
    <listCard2
      v-if="type == 'compusControl'"
      class="listCard listCard2"
      :itemInfo="thematicItem"
      theme="transparent"
    />
  </div>
</template>
<script>
import listCard from "@/views/fatigue-driving-list/components/list-card.vue";
import listCard2 from "@/views/compus-control-list/components/list-card.vue";
export default {
  props: {
    thematicItem: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: "fatigueDriving",
    },
  },
  components: {
    listCard,
    listCard2,
  },
  data() {
    return {};
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleItem() {
      const path = this.$route.path;
      const noMenu = this.$route.query.noMenu;
      if (this.type == "fatigueDriving") {
        this.$router.push({
          path,
          query: {
            noMenu,
            sectionName: this.thematicItem.cdAlarmVehicle
              ? "secondContent"
              : "firstContent",
          },
        });
      }
      if (this.type == "compusControl") {
        this.$router.push({
          path,
          query: { noMenu, sectionName: "strangersFound" },
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.area-dom-wrapper {
  position: relative;
  padding: 10px 28px 25px 0;
  height: 100%;
}
.listCard {
  width: 300px;
  height: 230px;
  border: 1px solid #ea4a36;
  border-radius: 0;
  box-shadow: inset 0 0 45px 0 #f34336;
  background: rgba(43, 5, 5, 0.4);
}
.listCard2 {
  height: auto;
}
.header {
  width: 300px;
  height: 30px;
  background: linear-gradient(210deg, #ff7b56 0%, #ea4a36 100%);
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  .icon-close {
    cursor: pointer;
  }
}
</style>
