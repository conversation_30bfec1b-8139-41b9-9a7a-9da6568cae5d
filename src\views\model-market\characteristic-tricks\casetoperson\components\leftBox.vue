<!--
    * @FileDescription: 由案到人-搜索条件
    * @Author: H
    * @Date: 2024/04/10
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-09 17:00:33
 -->
<template>
  <div class="leftBox case_leftBox">
    <div class="search_box" :class="{ 'search_box-pack': packUpDown }">
      <div class="title">
        <p class="title_num">1</p>
        <p class="title_text">选择警情、案件</p>
        <div class="switch_point" v-if="selectTable.length > 0">
          <i-switch v-model="pointSwitch" @on-change="handleChangeSwitch" />
          <p>点位</p>
        </div>
      </div>
      <div
        class="search_condition"
        :class="{ 'search_condition-pack': packUpDown }"
      >
        <div
          class="add_case"
          @click="handleCase"
          v-if="selectTable.length == 0"
        >
          <Icon type="md-add-circle" />
          <p class="add_case_text">添加案情、案件</p>
        </div>
        <div class="list_box" v-else>
          <div class="type_title">
            <i class="iconfont icon-jingqing range"></i>
            <p class="type_name">
              警情 ( <span>{{ partSelectData.length }}</span> )
            </p>
          </div>
          <ul class="search_ul">
            <list
              v-for="(item, index) in partSelectData"
              :key="index"
              :rowObj="item"
              :showDel="!searchBox"
              @deleList="handleDele(index, 0)"
            ></list>
          </ul>
          <div class="type_title">
            <i class="iconfont icon-anjian range"></i>
            <p class="type_name">
              案件 ( <span>{{ caseSelectData.length }}</span> )
            </p>
          </div>
          <ul class="search_ul">
            <list
              v-for="(item, index) in caseSelectData"
              :key="index"
              :rowObj="item"
              :showDel="!searchBox"
              @deleList="handleDele(index, 1)"
            ></list>
          </ul>
        </div>
      </div>
      <div class="footer-box" v-if="selectTable.length > 0">
        <div
          class="select-tag-button"
          @click="selectDevice()"
          v-show="!searchBox"
        >
          编辑警情、案件（已选{{ selectTable.length }}）
        </div>
        <div
          class="footer"
          :class="{ packArrow: packUpDown }"
          @click="handlePackup"
        >
          <img :src="packUrl" alt="" />
          <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
        </div>
      </div>
    </div>
    <div class="object-information" v-if="selectTable.length > 0">
      <div class="title">
        <p class="title_num">2</p>
        <p class="title_text">人员筛选条件</p>
      </div>
      <div class="search_form">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          v-show="formDetails"
        >
          <FormItem label="性别:" prop="sex">
            <ui-tag-select
              ref="sex"
              @input="
                (e) => {
                  input(e, 'sex');
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in ipbdFaceCaptureGender"
                :key="$index"
                :name="item.dataKey"
              >
                {{ item.dataValue }}
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
          <FormItem label="民族:" prop="national">
            <Select
              v-model="formData.national"
              clearable
              class="wrapper-select"
            >
              <Option
                v-for="item in nationList"
                :value="item.dataKey"
                :key="item.dataKey"
                placeholder="请选择"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="年龄:" prop="startAge">
            <div class="age_bracket">
              <ul class="age_type_select" v-if="selectAgeType">
                <li
                  class="age_type_li"
                  :class="{ age_li_active: ageIndex == 0 }"
                  @click="handleAge(0)"
                >
                  全部
                </li>
                <li class="age_type_li" @click="handleAge(1)">自定义范围</li>
              </ul>
              <div class="age_num" v-else>
                <Input
                  v-model="formData.startAge"
                  size="small"
                  type="number"
                  number
                  class="age_num_input"
                  placeholder="请选择"
                />
                <span class="age_num_line">-</span>
                <Input
                  v-model="formData.endAge"
                  size="small"
                  type="number"
                  number
                  class="age_num_input"
                  placeholder="请选择"
                />
                <Icon type="ios-close" @click="handledelaAge(true)" />
              </div>
            </div>
          </FormItem>
          <FormItem label="标签:" prop="labelIds">
            <div class="tag">
              <div class="select-tag-button" @click="selectLabelHandle">
                请选择{{
                  formData.labelIds && formData.labelIds.length > 0
                    ? `{已选（${formData.labelIds.length}）}`
                    : ""
                }}
              </div>
              <Select
                v-model="formData.labelRel"
                placeholder="请选择"
                style="width: 80px"
                transfer
              >
                <Option value="2">并集</Option>
                <Option value="1">交集</Option>
              </Select>
            </div>
          </FormItem>
          <FormItem label="轨迹类型:" prop="trajectoryTypes">
            <select-group v-model="formData.trajectoryTypes"> </select-group>
            <!-- <Select
              v-model="formData.trajectoryTypes"
              multiple
              clearable
              class="wrapper-select"
            >
              <Option
                v-for="item in trajectoryTypeList"
                :value="item.dataKey"
                :key="item.dataKey"
                placeholder="请选择"
                >{{ item.dataValue }}</Option
              >
            </Select> -->
          </FormItem>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleNext"
              >下一步</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
        <div class="details-form" v-if="!formDetails">
          <div class="wrapper-box">
            <div class="wrapper-title">性别:</div>
            <div class="wrapper-text" v-if="formData.sex">
              {{ formData.sex | commonFiltering(ipbdFaceCaptureGender) }}
            </div>
            <div class="wrapper-text" v-else>全部</div>
          </div>
          <div class="wrapper-box">
            <div class="wrapper-title">民族:</div>
            <div class="wrapper-text" v-if="formData.national">
              {{ formData.national | commonFiltering(nationList) }}
            </div>
            <div class="wrapper-text" v-else>全部</div>
          </div>
          <div class="wrapper-box">
            <div class="wrapper-title">年龄:</div>
            <div class="wrapper-text">
              {{
                formData.startAge && formData.endAge
                  ? `${formData.startAge} - ${formData.endAge}岁`
                  : "全部"
              }}
            </div>
          </div>
          <div class="wrapper-box">
            <div class="wrapper-title">标签:</div>
            <div class="wrapper-text">
              <ui-tag-poptip
                v-if="formData.labelIds && formData.labelIds.length > 0"
                :data="formData.labelIds"
              />
              <span v-else>全部</span>
            </div>
          </div>
          <div class="wrapper-box">
            <div class="wrapper-title">轨迹类型:</div>
            <div
              class="wrapper-text ellipsis"
              v-if="formData.trajectoryTypes.length > 0"
              :title="
                fmtTrajectoryTypes.join('/')
                  | commonFiltering(trajectoryTypeList)
                  | changeWhitespace
              "
            >
              {{
                fmtTrajectoryTypes.join("/")
                  | commonFiltering(trajectoryTypeList)
                  | changeWhitespace
              }}
            </div>
            <div class="wrapper-text" v-else>全部</div>
          </div>
        </div>
      </div>
    </div>
    <div class="crash-analyse" v-if="searchBox">
      <div class="title">
        <p class="title_num">3</p>
        <p class="title_text">碰撞分析</p>
      </div>
      <div class="crash-form" v-if="resultStatus == 1">
        <div class="crash-box">
          <p class="crash-box-title">
            <span class="required_icon">*</span> 案发前:
          </p>
          <Select v-model="formData.beforeCase" clearable class="wrapper-input">
            <Option
              v-for="item in caseTimeTypeList"
              :value="item.dataKey"
              :key="item.dataKey"
              placeholder="请选择"
              >{{ item.dataValue }}</Option
            >
          </Select>
          <!-- <Input v-model="formData.beforeCase" size="small" placeholder="请输入" ></Input> -->
          <p>内</p>
          <p class="crash-box-end">
            <span class="required_icon">*</span>案发后:
          </p>
          <Select v-model="formData.afterCase" clearable class="wrapper-input">
            <Option
              v-for="item in caseTimeTypeList"
              :value="item.dataKey"
              :key="item.dataKey"
              placeholder="请选择"
              >{{ item.dataValue }}</Option
            >
          </Select>
          <!-- <Input v-model="formData.afterCase" size="small" placeholder="请输入" class="wrapper-input"></Input> -->
          <p>内</p>
        </div>
        <div class="crash-scope">
          <p class="crash-box-title">碰撞范围:</p>
          <div class="slider-content">
            <Slider v-model="formData.collisionRange" :max="1000"></Slider>
            <span>{{ formData.collisionRange }}M</span>
          </div>
        </div>
        <div class="btn-group">
          <Button type="primary" class="btnwidth" @click="handleAnalysis"
            >碰撞分析</Button
          >
          <Button type="default" @click="handleBack(0)">返回</Button>
        </div>
      </div>
      <results-rogress v-if="resultStatus == 2" :percent="percent" />
      <div class="crash-form" v-if="resultStatus == 3">
        <div class="wrapper-box">
          <div class="wrapper-title">案发前:</div>
          <div class="wrapper-text">{{ formData.beforeCase }}小时内</div>
        </div>
        <div class="wrapper-box">
          <div class="wrapper-title">案发后:</div>
          <div class="wrapper-text">{{ formData.afterCase }}小时内</div>
        </div>
        <div class="wrapper-box">
          <div class="wrapper-title">碰撞范围:</div>
          <div class="wrapper-text">{{ formData.collisionRange }}M</div>
        </div>
        <div class="btn-group">
          <Button type="default" class="btnBack" @click="handleBack(1)"
            >返回</Button
          >
        </div>
      </div>
    </div>
    <!-- 选择设备 -->
    <select-case ref="selectCase" showOrganization @selectData="selectData" />
    <!-- 选择标签 -->
    <LabelModal
      :labelType="2"
      @setCheckedLabel="setCheckedLabel"
      ref="labelModal"
    />
  </div>
</template>

<script>
import { mapActions } from "vuex";
import { cloneDeep } from "lodash";
import selectCase from "@/components/select-modal/select-case.vue";
import list from "@/components/model-market/list.vue";
import LabelModal from "@/views/holographic-archives/components/relationship-map/label-add";
import { queryAnalyze, taskView } from "@/api/modelMarket";
import resultsRogress from "../../../components/results-rogress/index.vue";
import selectGroup from "../../../components/select-group";

const defaultFormData = {
  sex: "",
  national: "",
  labelIds: [],
  labelRel: "2",
  collisionRange: 1000,
  startAge: "20",
  endAge: "40",
  trajectoryTypes: [],
  beforeCase: "",
  afterCase: "",
};
export default {
  name: "",
  components: {
    selectCase,
    list,
    LabelModal,
    resultsRogress,
    selectGroup,
  },
  data() {
    return {
      packUpDown: false,
      showSelectCase: false,
      caseSelectData: [],
      partSelectData: [],
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      showPersonCond: false,
      searchBox: false,
      formData: {
        ...defaultFormData,
      },
      pointSwitch: true,
      ruleValidate: {},
      formDetails: true,
      selectAgeType: !defaultFormData.startAge,
      ageIndex: 0,
      analyzeId: "",
      resultStatus: 0,
      percent: 0,
      caseVos: [],
    };
  },
  watch: {
    selectTable() {
      this.$emit(
        "dealPoliceAndCaseTrackData",
        cloneDeep(this.selectTable),
        this.pointSwitch
      );
    },
  },
  filters: {
    changeWhitespace(val) {
      return val.replace(/\//g, "、");
    },
  },
  computed: {
    selectTable() {
      const list = this.dealPoliceAndCaseTrackData();
      return list;
    },
    // 轨迹类型最后一层
    fmtTrajectoryTypes() {
      return this.formData.trajectoryTypes.map((e) => e[e.length - 1]);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 选拔案件警情
    handleCase() {
      this.showSelectCase = true;
      this.$nextTick(() => {
        this.$refs.selectCase.show(this.partSelectData, this.caseSelectData);
      });
    },
    selectDevice() {
      if (this.resultStatus !== 0) {
        return;
      }
      this.handleCase();
    },
    // 下一步
    handleNext() {
      this.resultStatus = 1;
      this.packUpDown = true;
      this.formDetails = false;
      this.searchBox = true;
    },
    // 重置
    handleReset() {
      this.formData = {
        ...defaultFormData,
      };
      this.$refs.sex.clearChecked();
      this.handledelaAge(!defaultFormData.startAge);
    },
    selectData(plist = [], clist = []) {
      this.partSelectData = plist;
      this.caseSelectData = clist;
    },
    getCaseOrAlarmDetail(type, bh) {
      const list = type == 1 ? this.partSelectData : this.caseSelectData;
      const detail =
        list.find((item) => item.jjbh === bh || item.ajbh === bh) || {};
      return { ...detail };
    },
    dealPoliceAndCaseTrackData() {
      let plist = this.partSelectData.map((item) => {
        return {
          bh: item.jjbh,
          caseTime: item.bjdhsj,
          geoPoint: item.geoPoint,
          policeDataType: 1,
          detail: { ...item },
        };
      });
      let clist = this.caseSelectData.map((item) => {
        return {
          bh: item.ajbh,
          caseTime: item.fxsj,
          geoPoint: item.geoPoint,
          policeDataType: 2,
          detail: { ...item },
        };
      });
      return [...plist, ...clist];
    },
    handleAnalysis() {
      if (this.formData.beforeCase == "" || this.formData.afterCase == "") {
        this.$Message.warning("案发前后时间为必填项！");
        return;
      }
      this.resultStatus = 2;
      // let plist = this.partSelectData.map((item) => {
      //   return {
      //     bh: item.jjbh,
      //     caseTime: item.bjdhsj,
      //     geoPoint: item.geoPoint,
      //     type: 1,
      //   };
      // });
      // let clist = this.caseSelectData.map((item) => {
      //   return {
      //     bh: item.ajbh,
      //     caseTime: item.bjsj,
      //     geoPoint: item.geoPoint,
      //     type: 2,
      //   };
      // });
      this.caseVos = this.dealPoliceAndCaseTrackData().map(
        ({ detail, policeDataType, ...item }) => ({
          ...item,
          type: policeDataType,
        })
      );
      let params = {
        ...this.formData,
        labelIds: this.formData.labelIds
          ? this.formData.labelIds.map((item) => item.id)
          : [],
        caseVos: this.caseVos,
      };
      // 传参只需要最后一层内容，显示需要二位数组
      if (params.trajectoryTypes.length) {
        params.trajectoryTypes = this.fmtTrajectoryTypes;
      }
      queryAnalyze(params)
        .then((res) => {
          if (res.data) {
            this.analyzeId = res.data;
            this.progressBar();
          }
        })
        .catch(() => {
          this.showErrMessage();
        });
    },
    showErrMessage() {
      this.resultStatus = 1;
      this.$Message.warning("无碰撞结果，请重新选择合适条件");
    },
    // 获取进度
    progressBar() {
      taskView(this.analyzeId)
        .then((res) => {
          if (res.data.status == 1) {
            this.resultStatus = 3;
            this.percent = 0;
            this.$emit(
              "analysis",
              this.analyzeId,
              this.formData.collisionRange
            );
          } else if (res.data.status == 2) {
            this.resultStatus = 1;
            this.$Message.warning("无碰撞结果，请重新选择合适条件");
            this.percent = 0;
          } else {
            this.percent = res.data.percent;
            setTimeout(() => {
              this.progressBar();
            }, 500);
          }
        })
        .catch(() => {
          this.showErrMessage();
        });
    },
    // 返回
    handleBack(index) {
      if (index == 0) {
        this.resultStatus = 0;
        this.formDetails = true;
        this.searchBox = false;
        this.formData.beforeCase = defaultFormData.beforeCase;
        this.formData.afterCase = defaultFormData.beforeCase;
        this.formData.collisionRange = defaultFormData.collisionRange;
      } else if (index == 1) {
        this.resultStatus = 1;
        this.$emit("back");
      }
    },
    // 删除警情，案情
    handleDele(index, type) {
      if (type == 0) {
        this.partSelectData.splice(index, 1);
      } else {
        this.caseSelectData.splice(index, 1);
      }
      // if (this.partSelectData.length == 0 && this.caseSelectData.length == 0) {
      //   this.pointSwitch = false;
      // }
    },
    // 年龄
    handleAge(index) {
      this.ageIndex = index;
      if (index == 1) {
        this.selectAgeType = false;
      }
    },
    handledelaAge(flag) {
      this.selectAgeType = flag;
      if (flag) {
        this.formData.startAge = "";
        this.formData.endAge = "";
      } else {
        this.formData.startAge = defaultFormData.startAge;
        this.formData.endAge = defaultFormData.endAge;
      }
      this.ageIndex = 0;
    },
    // 选择标签
    selectLabelHandle() {
      this.$refs.labelModal.init(this.formData.labelIds, true);
    },
    // 已选标签
    setCheckedLabel(val) {
      this.formData.labelIds = JSON.parse(JSON.stringify(val));
    },
    // 展开，收起
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    input(e, key) {
      this.formData[key] = e;
      this.$forceUpdate();
    },
    handleChangeSwitch(val) {
      this.$emit("pointBox", val);
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../../components/style/index";

.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;

  .search_box {
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 370px;
    max-height: calc(~"100% - 20px");
    overflow: hidden;

    .switch_point {
      font-weight: 400;
      font-size: 14px;
      color: #3d3d3d;
      display: flex;
      align-items: center;
      margin-left: 100px;

      p {
        margin-left: 10px;
      }
    }

    .search_condition {
      padding: 15px 15px;
      max-height: calc(~"100vh - 620px");
      overflow-y: auto;
      box-sizing: border-box;

      .add_case {
        width: 340px;
        height: 130px;
        background: url("~@/assets/img/model/searchIcon/casebg.png") no-repeat;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        /deep/ .ivu-icon-md-add-circle {
          font-size: 27px;
          color: #2c86f8;
        }

        .add_case_text {
          font-weight: 400;
          font-size: 14px;
          color: #2c86f8;
          margin-top: 12px;
        }
      }

      .list_box {
        .type_title {
          display: flex;
          align-items: center;

          .icon-jingqing {
            color: #ea4a36;
          }

          .icon-anjian {
            color: #4696fc;
          }

          .type_name {
            font-weight: 700;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
            margin-left: 6px;

            span {
              color: #2c86f8;
            }
          }
        }
      }
    }

    .search_condition-pack {
      height: 0px;
      transition: height 0.2s ease-out;
      overflow: hidden;
      padding: 0;
    }

    .footer-box {
      padding-top: 10px;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
      background: #ffffff;
      border-radius: 4px 4px 4px 4px;

      .select-tag-button {
        width: 90%;
        margin: 0 auto;
      }
    }
  }

  .object-information {
    margin-top: 10px;
    width: 370px;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    .search_form {
      padding: 5px;

      .wrapper-select {
        width: 200px;
      }

      .tag {
        display: flex;
        justify-content: space-between;
        width: 80%;
      }

      .details-form {
        padding-bottom: 10px;
        .wrapper-box {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 10px;
          column-gap: 5px;
          .wrapper-title {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.4535);
          }

          .wrapper-text {
            flex: 1;
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);

            .tag-box {
              display: flex;

              .tag-box-li {
                font-weight: 400;
                font-size: 12px;
                color: #1faf8a;
                padding: 2px 6px;
                border: 1px solid;
                margin: 0 2px;
              }
            }
          }

          .wrapper-time {
            width: 100%;
          }
        }
      }
    }
  }

  .crash-analyse {
    margin-top: 10px;
    width: 370px;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    .crash-form {
      padding: 15px;
      display: flex;
      flex-wrap: wrap;

      .crash-box {
        display: flex;
        align-items: center;

        .crash-box-end {
          margin-left: 10px;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
        }

        .required_icon {
          color: #ff3d25;
        }
      }

      .crash-scope {
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;
        width: 100%;

        .slider-content {
          flex: 1;

          /deep/ .ivu-slider {
            width: 100%;
          }
        }
      }

      .crash-box-title {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }

      .wrapper-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 10px;
        column-gap: 5px;
        width: 50%;

        .wrapper-title {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          font-style: normal;
          color: rgba(0, 0, 0, 0.45);
        }

        .wrapper-text {
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 600;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
  }

  .search_box-pack {
    // height: 120px;
    transition: height 0.2s ease-out;
    overflow: hidden;
  }

  .btn-group {
    width: 100%;

    .btnwidth {
      width: 258px;
    }

    .btnBack {
      width: 100%;
      margin-top: 10px;
    }
  }

  .wrapper-input {
    width: 80px;
    margin: 0 5px;
  }
}

/deep/ .ivu-form-item {
  margin-bottom: 10px;
}

.age_bracket {
  .age_type_select {
    display: flex;

    .age_type_li {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
      padding: 0px 5px;
      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #d3d7de;
      cursor: pointer;
      margin-right: 10px;
    }

    .age_li_active {
      background: #2c86f8;
      color: #ffffff;
    }
  }

  .age_num {
    display: flex;
    align-items: center;

    .age_num_input {
      width: 100px;
    }

    .age_num_line {
      margin: 0 5px;
    }

    /deep/ .ivu-icon-ios-close {
      font-size: 24px;
      cursor: pointer;
    }
  }
}
</style>
