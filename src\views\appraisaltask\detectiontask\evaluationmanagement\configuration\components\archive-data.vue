<template>
  <div class="archive-data">
    <!--档案数据 人像/车辆 -->
    <common-form
      ref="commonForm"
      :form-data="formData"
      :form-model="formModel"
      :module-action="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
    </common-form>
    <Form
      v-if="['ARCHIVES_PORTRAIT_ACCURACY', 'ARCHIVES_VEHICLE_ACCURACY'].includes(moduleAction.indexType)"
      ref="formData"
      :model="formData"
      :label-width="160"
    >
      <FormItem label="抽取数据数" class="right-item mb-lg" prop="perPumpQuantity">
        <InputNumber class="width-lg" v-model="formData.perPumpQuantity" placeholder="请输入抽取数据数"> </InputNumber>
      </FormItem>
      <template v-if="moduleAction.indexType == 'ARCHIVES_PORTRAIT_ACCURACY'">
        <FormItem label="相似度阈值" class="right-item mb-lg" prop="threshold">
          <InputNumber class="width-lg" :max="100" v-model="formData.threshold" placeholder="请输入相似度阈值">
          </InputNumber>
          <span class="base-text-color ml-xs">%</span>
        </FormItem>
        <p class="font-warning text">如果有多数算法判定轨迹照片和证件照达到相似度阈值，则人脸抓拍图片准确。</p>
      </template>
      <p v-else class="font-warning text">如果有多数算法判定抓拍图片车牌号和当担车牌号一致，则车辆抓拍图片准确。</p>
    </Form>
  </div>
</template>
<script>
export default {
  components: {
    CommonForm: require('./common-form/index.vue').default,
  },
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        threshold: null,
        detectMode: '1',
        perPumpQuantity: null, //抽取数据
      },
    };
  },
  watch: {
    moduleAction: {
      handler(val) {
        if (val.config) {
          this.formData = {
            ...this.configInfo,
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
          };
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 表单提交校验
    handleSubmit() {
      return this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
  },
};
</script>
<style lang="less" scoped>
.archive-data {
  margin-bottom: 20px;
  .text {
    margin-left: 160px;
  }
}
</style>
