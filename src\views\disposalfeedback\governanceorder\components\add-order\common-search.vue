<template>
  <section class="common-search">
    <Form class="mb-sm" ref="commonSearch" :model="searchForm" :rules="ruleValidate">
      <FormItem label="工单编号" class="inline mr-lg" required>
        <Input class="width-lg" v-model="searchForm.workOrderNum" placeholder="系统自动生成" disabled></Input>
      </FormItem>
      <FormItem label="工单名称" class="inline" required prop="workOrderName">
        <Input
          class="width-lg"
          v-model="searchForm.workOrderName"
          :placeholder="!isSelf ? '系统自动生成' : '请填写工单名称'"
          :disabled="isView || !isSelf"
        ></Input>
      </FormItem>
      <FormItem class="inline ml-lg" label="截止时间">
        <DatePicker
          class="picker-end width-lg"
          :value="searchForm.taskPlannedDate"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          :options="timeOption"
          placeholder="请选择任务截止时间"
          :disabled="isView"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchForm, 'taskPlannedDate')"
        >
        </DatePicker>
      </FormItem>
      <FormItem class="inline ml-lg" label="紧急程度" required>
        <Select class="width-md" v-model="searchForm.workLevel" placeholder="请选择紧急程度">
          <Option
            :label="item.label"
            :value="item.value"
            v-for="(item, index) in workLevelOptions"
            :key="`${item.value}-${index}`"
          >
            <div class="inline work-level-box mr-sm vt-middle" :class="item.style"></div>
            <span>{{ item.label }}</span>
          </Option>
        </Select>
      </FormItem>
    </Form>
    <div class="normal-form">
      <ui-label label="工单指派" class="inline">
        <RadioGroup v-if="!isView" v-model="assignMode" @on-change="onChangeAssignMode">
          <Radio :label="0">自定义指派</Radio>
          <Radio :label="1" v-if="!isSelf && editViewAction.type === 'add'">自动指派</Radio>
        </RadioGroup>
      </ui-label>
      <div
        class="camera inline"
        v-if="assignMode === 0"
        :class="{ 'no-allow': isView }"
        @click="isView ? null : (peopleShow = true)"
      >
        <span class="font-blue" v-if="!searchForm.receiverName">请选择指派人</span>
        <span v-else class="font-blue">{{ searchForm.receiverName }}</span>
      </div>
      <!-- <Select v-model="searchForm.taskContent"
                  placeholder="请选择任务接收人"
                  class="width-slg">
            <Option v-for="(item, index) in cjdwList"
                    :key="index"
                    :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select> -->
      <ui-label v-if="assignMode === 1" class="ml-md inline" label="指派规则">
        <Select class="width-md" v-model="searchForm.assignMode" placeholder="请选择指派规则">
          <Option label="自动指派维护单位人" :value="1"> </Option>
          <Option label="自动指派组织机构人" :value="2"> </Option>
        </Select>
        <span class="ml-sm link-text-box pointer" v-if="searchForm.assignMode === 2" @click="onClickAutoAssignOrg"
          >配置各单位工单接收人</span
        >
      </ui-label>
      <span>
        <span v-if="searchForm.assignMode === 1" class="ml-sm font-D66418"
          >系统自动指派给设备运维单位联系人！（说明：设备类型的工单，根据设备的【维护单位联系人】字段，中文匹配用户表，自动指派给对应的用户。）</span
        >
        <span v-if="searchForm.assignMode === 2" class="ml-sm font-D66418"
          >备注：每个单位选择一个工单接收人，系统设备的所属单位自动将工单指派给工单接收人！</span
        >
      </span>
      <slot></slot>
      <ui-label class="flex-1 mt-sm mb-sm" label="任务说明">
        <Input
          type="textarea"
          class="desc"
          v-model="searchForm.taskContent"
          placeholder="请填写任务说明"
          :disabled="isView"
          :rows="3"
          :maxlength="256"
        ></Input>
      </ui-label>
    </div>
    <select-people ref="selectpeople" title="请选择指派人" v-model="peopleShow" @putPeople="putPeople"></select-people>
    <select-receiver
      ref="selectpeopleConfig"
      v-model="selectPeopleConfigVisible"
      @on-select-receiver="OnSelectReceiver"
    >
    </select-receiver>
  </section>
</template>
<script>
import { workLevelOptions } from '@/views/disposalfeedback/governanceorder/util/enum.js';
export default {
  props: {
    isView: {
      default: true,
    },
    isSelf: {
      default: false,
    },
    defaultForm: {
      default: () => {
        return {
          workOrderNum: null,
          workOrderName: '',
          taskPlannedDate: '',
          receiverName: '',
          taskContent: '',
          assignMode: 0,
        };
      },
    },
    editViewAction: {
      default: () => {
        return {
          type: '',
          row: {},
        };
      },
    },
  },
  data() {
    const validateArray = (rule, value, callback) => {
      if (!this.isSelf) {
        callback();
      } else {
        value ? callback() : callback('请填写工单名称');
      }
    };
    return {
      timeOption: {
        disabledDate: (date) => {
          let time = new Date().getTime() - 24 * 60 * 60 * 1000;
          if (time) {
            return date < time;
          }
          return false;
        },
      },
      ruleValidate: {
        // wordorderName: [
        //   { required: true, message: '请填写工单名称', trigger: 'blur' },
        // ],
        workOrderName: [{ validator: validateArray, trigger: 'blur' }],
      },
      searchForm: {
        workOrderNum: null,
        workOrderName: '',
        taskPlannedDate: '',
        receiverName: '',
        taskContent: '',
        assignMode: 0,
        assignList: [],
        workLevel: '1',
      },
      cjdwList: [],
      assignMode: 0,
      peopleShow: false,
      selectPeopleConfigVisible: false,
    };
  },
  computed: {
    workLevelOptions() {
      return workLevelOptions;
    },
  },
  created() {},
  updated() {
    this.$emit('getSearchForm', this.searchForm);
  },
  methods: {
    radioChange(value) {
      this.$emit('radioChange', value === 1);
    },
    putPeople(item) {
      this.searchForm.receiverName = item.name;
      this.searchForm.receiverId = item.username;
      this.peopleShow = false;
    },
    onClickAutoAssignOrg() {
      this.selectPeopleConfigVisible = true;
    },
    onChangeAssignMode(val) {
      // 后端不存 自动指派
      if (val === 0) {
        this.searchForm.assignMode = 0;
      } else {
        this.searchForm.assignMode = 1;
      }
    },
    OnSelectReceiver(val) {
      this.searchForm.assignList = val.map((item) => {
        return {
          assignId: item.username,
          assignName: item.name,
          orgCode: item.orgCode,
          orgName: item.orgName,
          orgId: item.orgId,
        };
      });
    },
  },
  watch: {
    defaultForm(val) {
      Object.assign(this.searchForm, val);
    },
    isSelf() {
      this.assignMode = 0;
      this.searchForm.assignMode = 0;
    },
  },
  components: {
    SelectPeople: require('@/api-components/select-people/select-people.vue').default,
    // selectReceiver: require('./select-receiver.vue').default,
    selectReceiver: require('../select-receiver.vue').default,
  },
};
</script>
<style lang="less" scoped>
@import '~@/views/disposalfeedback/governanceorder/components/work-level-tag/index.less';

.work-level-box {
  height: 16px;
  width: 16px;
}
.common-search {
  @{_deep}.ivu-form-item {
    margin-bottom: 0;
    .ivu-form-item-content {
      display: flex;
    }
  }
  .no-allow {
    cursor: not-allowed;
  }
  .width-slg {
    width: 380px;
  }
  .pl-16 {
    padding-left: 16px;
  }
  .ui-label {
    display: flex;
  }
  .normal-form {
    padding: 0 10px;
    .inline {
      display: inline-block;
    }
  }
  .section-p {
    display: flex;
    align-items: center;
  }
  .desc {
    width: 1351px;
  }
  .flex-1 {
    flex: 1;
  }
  .assign-desc {
    margin-left: 64px;
  }
}
</style>
