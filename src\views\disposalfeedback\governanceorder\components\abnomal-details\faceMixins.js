import evaluationoverview from '@/config/api/evaluationoverview';
import governancetask from '@/config/api/governancetask';

export default {
  props: {
    editViewAction: {
      default: () => {
        return {
          type: 'view',
          row: {},
        };
      },
    },
  },
  methods: {
    //人脸
    async interface(params) {
      try {
        let data = this.returnInterfaceParams(params);
        const requestUrl =
          this.entryType == 'deviceInfo'
            ? evaluationoverview.getSecondaryPopUpData
            : governancetask.orderFaceDetailGetPopUpData;
        let res = await this.$http.post(requestUrl, data);
        let entities = res.data.data.entities || [];
        let cardList = entities.map((item, index) => {
          // 不同指标，不同的处理
          this.handlerDataByFace(item, index);
          return item;
        });
        return { cardList, total: res.data.data.total };
      } catch (err) {
        console.log(err);
        return { cardList: [], total: 0 };
      }
    },
    returnInterfaceParams(params) {
      let data = {};
      if (this.entryType == 'deviceInfo') {
        const { indexId, batchId, currentTaskReceiverOrgCode, createOrgCode  } = this.editViewAction.row;
        data = {
          indexId: indexId,
          batchId: batchId,
          displayType: 'ORG',
          orgRegionCode: currentTaskReceiverOrgCode || createOrgCode,
          ...params,
          customParameters: {
            ...params.customParameters,
            faceDeviceDetailId: '',
          },
        };
      }
      if (this.entryType == 'table') {
        const { id, deviceId } = this.entryRowObj;
        data = {
          workOrderFlowId: id,
          deviceId: deviceId,
          pageNumber: params.pageNumber,
          pageSize: params.pageSize,
          outcome: params.customParameters.outcome || '2',
          causeErrors: params.customParameters.causeErrors || [],
        };
      }
      return data;
    },
    // 根据 各自的 评测结果页面复制，确保 逻辑一致
    handlerDataByFace(item, index) {
      let { indexId } = this.editViewAction.row;
      let tipColor = item.qualified !== '1' ? '#bc3c19' : '#0e8f0e';
      switch (indexId) {
        case '2002': // 重点人脸卡口设备图片地址可用率
        case '2001': // 人脸卡口设备图片地址可用率
          item._index = index;
          this.$set(item, 'fileList', [
            { value: item.shotTime, iconName: 'icon-shijian' },
            { value: item.address, iconName: 'icon-dizhi' },
            { value: item.resultTip, style: { color: tipColor }, iconName: 'icon-shujujiancegongju-01-01' },
          ]);
          item.imageUrl = item.facePath;
          break;
        case '2004': // 人脸卡口设备及时上传率
        case '2005': // 重点人脸卡口设备及时上传率
          this.$set(item, 'fileList', [
            { label: '抓拍:', value: item.shotTime },
            { label: '接收:', value: item.receiveTime },
            { label: '', value: item.resultTip, style: { color: tipColor } },
          ]);
          item.imageUrl = item.facePath;
          break;
        case '2006': // 人脸卡口设备时钟准确率
          this.$set(item, 'fileList', [
            { label: '抓拍:', value: item.shotTime },
            { label: '接收:', value: item.firstIntoViewTime },
            { label: '', value: item.resultTip, style: { color: tipColor } },
          ]);
          item.imageUrl = item.facePath;
          break;
      }
    },
  },
};
