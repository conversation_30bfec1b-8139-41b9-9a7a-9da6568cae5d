<template>
  <!--  flex-column-center -->
  <div class="height-full width-full">
    <div class="home-container" :class="getFullscreen ? 'full-screen-container' : ''">
      <top-title v-if="getFullscreen"></top-title>
      <!--    排行榜   -->
      <assess-ranking ref="assessRanking" :styles="{ left: '10px' }"></assess-ranking>
      <!-- 左上(设备资产) -->
      <!--      <plant-assets ref="plantAssets" :plant-list="assetList"></plant-assets>-->
      <!-- 左下echarts（关键属性检测）-->
      <determinant-attribute ref="determinantAttribute"></determinant-attribute>
      <!-- 左中（资产分布） -->
      <property-distributio ref="propertyDistributio" :online-visible="true"></property-distributio>
      <!-- 右下echarts（治理趋势） -->
      <govern-tendency ref="governTendency"></govern-tendency>
      <!--      今日值班    -->
      <today-duty ref="todayDuty"></today-duty>
      <!--      安全监测   -->
      <safety-monitoring ref="safety-monitoring"></safety-monitoring>
      <!-- 右上排名列表 -->
      <!--      <assess-ranking ref="assessRanking"></assess-ranking>-->
      <!--      &lt;!&ndash; 右中（治理达标率）&ndash;&gt;-->
      <!--      <standard-govern ref="standardGovern"></standard-govern>-->
      <!--  右下二（视图数据检测）  -->
      <view-detection ref="viewDetection" :queryAccessDataCount="queryAccessDataCount"></view-detection>
      <map-echarts v-if="!isActivated" :query-access-data="queryAccessData"></map-echarts>
      <!--    <view-detection ref="viewDetection"></view-detection>-->
      <!--  左下二（视频流检测）  -->
      <liquid-fill ref="liquid-fill" v-if="!isActivated"></liquid-fill>
      <!--  顶部统计  -->
      <statistics-top ref="statistics-top" :query-access-data="queryAccessData"></statistics-top>
      <full-screen ref="full-screen"></full-screen>
      <div class="location" :class="getFullscreen ? 'full-screen-location' : ''">
        <span class="icon-font icon-dingwei f-14"></span>
        <span class="f-14">
          {{ getHomeConfig.regionName }}
        </span>
        <notice class="ml-llg" ref="noticeRef">
          <span @click="noticeMore" class="pointer view-detail ml-sm">更多公告 >></span>
        </notice>
      </div>
    </div>
  </div>
</template>

<script>
import home from '@/config/api/home';
import jichu1 from '@/assets/img/home/<USER>/video-surveillance.png';
import jichu2 from '@/assets/img/home/<USER>/face-bayonet.png';
import jichu3 from '@/assets/img/home/<USER>/vehicle-bayonet.png';
import jichu4 from '@/assets/img/home/<USER>/class-point.png';
import jichu5 from '@/assets/img/home/<USER>/Jisan-point.png';
import jichu6 from '@/assets/img/home/<USER>/internal-control.png';
import shitu1 from '@/assets/img/home/<USER>/key-equipment.png';
import shitu2 from '@/assets/img/home/<USER>/common-equipment.png';
import { mapGetters } from 'vuex';
export default {
  name: 'home',
  props: {},
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      assetList: [
        {
          name: '视频监控',
          img: jichu1,
          value: 0,
          plantClass: 'f1_color',
        },
        { name: '人脸卡口', img: jichu2, value: 0, plantClass: 'f2_color' },
        {
          name: '车辆卡口',
          img: jichu3,
          value: 0,
          plantClass: 'f3_color',
        },
        { name: '一类点', img: jichu4, value: 0, plantClass: 'f4_color' },
        { name: '二三类点', img: jichu5, value: 0, plantClass: 'f5_color' },
        {
          name: '内部监控',
          img: jichu6,
          value: 0,
          plantClass: 'f6_color',
        },
        { name: '重点设备', img: shitu1, value: 0, plantClass: 'f7_color' },
        {
          name: '普通设备',
          img: shitu2,
          value: 0,
          plantClass: 'f8_color',
        },
      ],
      plantList: {},
      queryAccessDataCount: {},
      isActivated: false,
      queryAccessData: [
        {
          label: '设备总量',
          key: 'deviceTotalAmount',
          deviceTotalAmount: 0,
          color: '#25f3f1',
          icon: 'icon-shitujichushuju',
        },
        {
          label: '今日新增人脸',
          key: 'faceViewTotalTodayAmount',
          faceViewTotalTodayAmount: 0,
          color: '#bf89f7',
          icon: 'icon-renliankakou',
        },
        {
          label: '今日新增过车',
          key: 'vehicleViewTotalTodayAmount',
          vehicleViewTotalTodayAmount: 0,
          color: '#f5af55',
          icon: 'icon-cheliangkakou',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
    }),
  },
  watch: {
    $route: 'getParams',
  },
  filter: {},
  deactivated() {
    this.isActivated = true;
  },
  activated() {
    this.isActivated = false;
  },
  created() {},
  mounted() {
    this.queryIndexDeviceOverview();
    this.initQueryDeviceOfAccessDataCount();
    this.$refs.noticeRef.initFunc();
  },
  methods: {
    async initQueryDeviceOfAccessDataCount() {
      try {
        let {
          data: { data },
        } = await this.$http.get(home.queryDeviceOfAccessDataCount);
        if (!Object.keys(data).length) return false;
        this.queryAccessDataCount = data || {};
        this.queryAccessData = this.queryAccessData.map((item) => {
          item[item.key] = data[item.key];
          return item;
        });
      } catch (e) {
        console.log(e);
      }
    },
    async queryIndexDeviceOverview() {
      try {
        let res = await this.$http.get(home.queryIndexDeviceOverview);
        this.plantList = res.data.data;
        this.assetList[0].value = this.plantList.videoSurveillanceAmount;
        this.assetList[1].value = this.plantList.faceSwanAmount;
        this.assetList[2].value = this.plantList.vehicleBayonetAmount;
        this.assetList[3].value = this.plantList.oneDwAmount;
        this.assetList[4].value = this.plantList.twoDwAmount;
        this.assetList[5].value = this.plantList.innerDwAmount;
        this.assetList[6].value = this.plantList.focusAmount;
        this.assetList[7].value = this.plantList.commonAmount;
      } catch (err) {
        console.log(err);
      }
    },
    noticeMore() {
      this.$router.push({
        name: 'noticeannouncements',
      });
    },
  },
  components: {
    // plantAssets: require('./components/plant-assets.vue').default, //设备资产（左上
    determinantAttribute: require('./components/determinant-attribute.vue').default, //左下echarts
    governTendency: require('./components/govern-tendency.vue').default, //右下echarts
    assessRanking: require('./components/assess-ranking.vue').default, //右上排名列表
    propertyDistributio: require('./components/property-distributio.vue').default, //左中（资产分布）
    // standardGovern: require('./components/standard-govern.vue').default, //右中（治理达标率）
    viewDetection: require('./components/view-detection.vue').default, //右下二（视图数据检测）
    MapEcharts: require('./components/map-echarts.vue').default,
    // StaticsNum: require('./components/statics-num.vue').default,
    liquidFill: require('./components/liquid-fill.vue').default,
    StatisticsTop: require('./components/statistics-top.vue').default,
    FullScreen: require('./components/full-screen').default,
    TopTitle: require('./components/top-title').default,
    Notice: require('./components/notice').default,
    // NoticeAnnouncements: require('@/views/notification/noticeannouncements/index.vue').default,
    TodayDuty: require('./components/today-duty').default,
    SafetyMonitoring: require('./components/safety-monitoring').default,
  },
};
</script>

<style lang="less" scoped>
.home-container {
  background: url('~@/assets/img/home/<USER>') no-repeat;
  background-position: center;
  background-size: cover;
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px;
}
.location {
  position: absolute;
  left: 27%;
  top: 10%;
  color: #2389d0;
  display: flex;
  align-items: center;
}
.ml-llg {
  margin-left: 30px;
}
.view-detail {
  color: var(--color-primary);
}
.full-screen-container {
  padding: 0;
}
.full-screen-location {
  top: 16%;
}
</style>
