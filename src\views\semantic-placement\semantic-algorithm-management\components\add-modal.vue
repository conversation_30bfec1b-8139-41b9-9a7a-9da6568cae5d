<template>
  <ui-modal
    v-model="visible"
    :title="isEdit ? '创建算法' : '编辑算法'"
    :r-width="690"
    :show-message="false"
    @onOk="comfirmHandle"
  >
    <div class="form-box">
      <Form
        ref="algorithmRef"
        :model="algorithmForm"
        :label-width="120"
        :rules="ruleInline"
      >
        <FormItem prop="name" label="算法名称">
          <Input
            v-model="algorithmForm.name"
            :maxlength="20"
            placeholder="请输入算法名称"
          />
        </FormItem>
        <FormItem prop="llmModelName" label="模型名称">
          <Select v-model="algorithmForm.llmModelName" placeholder="请选择模型">
            <Option
              :value="item.modelName"
              :key="item.modelName"
              v-for="item in modalList"
              >{{ item.modelName }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="提示词">
          <div class="call-word-input-boxs">
            <div v-for="(item, index) in algorithmForm.prompts" :key="index">
              <Input
                v-model="algorithmForm.prompts[index]"
                :maxlength="maxCallWordLength"
                :placeholder="`输入提示词内容，最大字数为${maxCallWordLength}字`"
              >
                <div slot="append" @click="deletePrompts(index)">
                  <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
                </div>
              </Input>
            </div>
          </div>
          <div
            class="add-from"
            :class="{
              disabled: algorithmForm.prompts.length > maxCallWordNum,
            }"
            @click="addForm"
          >
            <Icon type="md-add" />
          </div>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>

<script>
import {
  addLLMCompareAlgorithm,
  modifyLLMCompareAlgorithm,
  selectBaseModelList,
} from "@/api/semantic-placement.js";

import { mapGetters } from "vuex";

const maxCallWordLength = 100; // 单条提示词长度

export default {
  name: "AddModal",
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    modalList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      maxCallWordLength,
      visible: false,
      algorithmForm: {
        llmModelName: "",
        prompts: [""],
        name: "",
      },
      ruleInline: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        llmModelName: [
          { required: true, message: "请选择基座算法", trigger: "blur" },
        ],
      },
      alId: "",
    };
  },
  computed: {
    ...mapGetters({
      semanticObj: "systemParam/getSemanticObj",
    }),
    maxCallWordNum() {
      return this.semanticObj?.maxCallWordNum || 10;
    },
  },
  mounted() {},
  methods: {
    init(info) {
      this.visible = true;
      const { name = "", llmModelName = "", prompts = [""], id = "" } = info;
      this.alId = id;
      this.algorithmForm = {
        name,
        llmModelName,
        prompts,
      };
    },
    deletePrompts(index) {
      if (this.algorithmForm.prompts.length === 1) {
        return this.$Message.warning("至少需要一个提示词");
      }
      this.algorithmForm.prompts.splice(index, 1);
    },
    addForm() {
      if (this.algorithmForm.prompts?.length > maxCallWordNum) {
        return this.$Message.warning(`最多添加${maxCallWordNum}个提示词`);
      }
      this.algorithmForm.prompts.push("");
    },
    comfirmHandle() {
      if (this.isEdit) {
        const param = {
          id: this.alId,
          ...this.algorithmForm,
        };
        modifyLLMCompareAlgorithm(param).then((res) => {
          this.visible = false;
          this.$message.success("修改成功");
          this.$emit("complete");
        });
      } else {
        addLLMCompareAlgorithm({ ...this.algorithmForm }).then((res) => {
          this.visible = false;
          this.$message.success("新增成功");
          this.$emit("complete");
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.form-box {
  height: 530px;
}
.call-word-input-boxs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 380px;
  overflow: scroll;
}
.add-from {
  background: #f9f9f9;
  margin-top: 3px;
  border: 1px solid #e8eaec;
  text-align: center;
  cursor: pointer;
  /deep/ .ivu-icon-md-add {
    color: #909399;
  }
}
.disabled {
  cursor: not-allowed;
}
</style>
