import permission from "./util.permission";
import common from "./util.common";
import cookies from "./util.cookies";
import log from "./util.log";
import db from "./util.db";
import { getToken } from "@/libs/configuration/util.common";

import Setting from "./setting";

const util = {
  common,
  permission,
  cookies,
  log,
  db,
};

function tTitle(title = "") {
  if (window && window.$t) {
    if (title.indexOf("$t:") === 0) {
      return window.$t(title.split("$t:")[1]);
    } else {
      return title;
    }
  } else {
    return title;
  }
}

/**
 * @description 更改标题
 * @param {Object} title 标题
 * @param {Object} count 未读消息数提示（可视情况选择使用或不使用）
 */
util.title = function ({ title, count }) {
  title = tTitle(title);
  let fullTitle = title
    ? `${title} - ${Setting.titleSuffix}`
    : Setting.titleSuffix;

  if (count) fullTitle = `(${count}条消息)${fullTitle}`;
  window.document.title = fullTitle;
};

function requestAnimation(task) {
  if ("requestAnimationFrame" in window) {
    return window.requestAnimationFrame(task);
  }
  setTimeout(task, 16);
}

export { requestAnimation };

/**
 * @method tableActionWidth
 * @param { Array } arr -- 表格操作按钮
 * @param { Array } titles -- 表格操作按钮对应数组
 * @description tableActionWidth 表格操作栏自适应宽度
 */
util.tableActionWidth = (arr, titles) => {
  let checkPermission =
    require("@/libs/configuration/util.permission").checkPermission;
  let len = arr.filter((v) => {
    return checkPermission([v]);
  });
  if (len.length) {
    let sum = 20;
    len.forEach((v) => {
      sum = sum + 16 + 24;
    });
    titles[titles.length - 1].width = sum;
    return titles;
  } else return titles.filter((col) => col.slot != "action");
};

/**
 * 打开新窗口跳转页面并携带refreshtoken
 * @method openNewPage
 * @param { string } url -- 跳转路径
 */
util.openNewPage = (url, target) => {
  let toUrl =
    url.indexOf("?") > -1
      ? url + "&refresh_token=" + getToken()
      : url + "?refresh_token=" + getToken();
  const alink = document.createElement("a");
  alink.setAttribute("href", toUrl);
  alink.setAttribute("target", target);
  alink.setAttribute("rel", "noopener noreferrer");
  document.body.appendChild(alink);
  alink.click();
  document.body.removeChild(alink);
};

export default util;
