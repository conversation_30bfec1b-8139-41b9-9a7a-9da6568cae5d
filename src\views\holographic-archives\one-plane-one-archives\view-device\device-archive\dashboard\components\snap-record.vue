<template>
  <div>
    <div class="my-swiper-container" id="mySwiper">
      <swiper ref="mySwiper" v-if="!swiperResize" :options="swiperOption" class="my-swiper">
        <template v-for="(item, index) in list">
          <swiper-slide :key="index">
            <div class="swiper-item">
              <p class="img-content">
                <i class="badge" v-if="type === 3" :class="item.picFlag === 2 ? 'bg-warning' : item.picFlag === 1 ? 'bg-primary' : ''">{{ item.picFlag === 2 ? '视' : item.picFlag === 1 ? '身' : '' }}身</i>
                <img :src="item.picUrl" alt="" />
                <span v-if="type === 3">{{ item.personNmae || '未知' }}</span>
                <span v-else>{{ item.licenseNumber || '未知' }}</span>
              </p>
              <div class="bottom-info">
                <p class="info">{{ item.address }}</p>
                <time>{{ item.snapTime }}</time>
              </div>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div class="swiper-button-prev snap-prev" slot="button-prev"><i class="iconfont icon-caret-right"></i></div>
      <div class="swiper-button-next snap-next" slot="button-next"><i class="iconfont icon-caret-right"></i></div>
    </div>
  </div>
</template>
<script>
import { swiper, swiperSlide } from 'vue-awesome-swiper'
export default {
  components: { swiper, swiperSlide },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    // 3人脸/2车辆
    type: {
      type: Number,
      default: 3
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      swiperResize: false,
      swiperOption: {
        effect: 'coverflow',
        slidesPerView: 2.95,
        centeredSlides: true,
        initialSlide: 2,
        // loop: false,
        // loopAdditionalSlides: 5,
        speed: 1000,
        // autoplay: {
        //   delay: 100000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false
        // },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 50, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 90, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: '.snap-next',
          prevEl: '.snap-prev'
        },
        observer: true,
        observeParents: true
      }
    }
  },
  mounted() {
    let _this = this
    this.$erd.listenTo(document.getElementById('mySwiper'), element => {
      _this.updateSliderHandle()
    })
  },
  methods: {
    updateSliderHandle() {
      const w = document.documentElement.clientWidth
      this.swiperOption.coverflowEffect.stretch = (w / 192) * 5.85
      this.swiperOption.coverflowEffect.depth = (w / 192) * 10
      this.swiperResize = true
      this.$nextTick(() => {
        this.swiperResize = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
.my-swiper-container {
  padding: 0 30px;
  position: relative;
  .my-swiper {
    margin: auto;
    padding: 15px 0;
    .swiper-item {
      width: 100%;
      height: 190px;
      background: #f9f9f9;
      box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      box-sizing: border-box;
      padding: 10px;
      overflow: hidden;
      .img-content {
        width: 100%;
        height: 120px;
        overflow: hidden;
        position: relative;
        img {
          width: 100%;
          height: 100%;
        }
        span {
          width: 100%;
          height: 30px;
          position: absolute;
          left: 0;
          bottom: 0px;
          color: #fff;
          text-align: center;
          line-height: 30px;
          background: rgba(0, 0, 0, 0.5);
        }
        .badge {
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 4px;
          left: 0;
          top: 0;
          color: #fff;
          font-size: 12px;
          line-height: 21px;
          text-align: center;
        }
      }
      .bottom-info {
        padding-top: 10px;
        color: #000;
        font-size: 12px;
        .info,
        time {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  /deep/ .swiper-container-3d {
    .swiper-slide-shadow-left {
      background-image: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    }
    .swiper-slide-shadow-right {
      background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    margin-top: -15px;
    .iconfont {
      color: #fff;
      font-size: 18px;
    }
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    &:active {
      background: rgba(0, 0, 0, 1);
    }
  }
  .swiper-button-prev {
    transform: rotate(180deg);
    left: 12px;
  }
  .swiper-button-next {
    right: 12px;
  }
}
</style>
