<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics></index-statistics>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem" :lengName="lengName">
          <template #rank-title> 按达标率排序 </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart"></line-chart>
    </div>
  </div>
</template>

<script>
export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChart: require('./components/line-chart').default,
  },
  data() {
    return {
      listyle: {
        height: '0.53rem',
        width: '2.006rem',
      },
      lengName: [
        {
          name: '采集区域类型不达标数量',
          key: 'unqualifiedNum',
        },
        {
          name: '采集区域类型达标数量',
          key: 'qualifiedNum',
        },
      ],
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: 100%;
      @{_deep} .index-statistics .information-statistics .statistics-ul li {
        width: calc((100% / 4) - 7.8px) !important;
        &:nth-child(4) {
          margin-right: 0 !important;
        }
      }
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
