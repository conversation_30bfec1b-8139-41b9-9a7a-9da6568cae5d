<template>
  <Tooltip transfer transfer-class-name="tooltip-tip-box" :placement="placement">
    <i class="icon-font icon-wenhao" :class="[iClass]" :style="{ color: 'var(--color-warning)' }"></i>
    <div slot="content" class="content-box">
      <p>视频画面过暗，判定点位异常</p>
      <p>可能因素：夜晚未打开补光灯、摄像机异常...</p>
      <div class="img-box">
        <div>
          <p>合格图片</p>
          <img src="~@/assets/img/evaluationmanagement/hege.jpg" alt="合格图片" />
        </div>
        <div>
          <p>不合格图片</p>
          <img src="~@/assets/img/evaluationmanagement/buhege.png" alt="不合格图片" />
        </div>
      </div>
    </div>
  </Tooltip>
</template>
<script>
export default {
  props: {
    placement: {
      type: String,
      default: 'right',
    },
    iClass: {
      type: String,
      default: 'f-16',
    },
  },
};
</script>
<style lang="less" scoped>
.tooltip-tip-box.ivu-tooltip-popper {
  .content-box {
    line-height: 28px;
  }
  .img-box {
    display: flex;
    flex-direction: row;
    & > div {
      &:not(:last-of-type) {
        margin-right: 20px;
      }
      width: calc((100% - 20px) / 2);
      img {
        width: 100%;
      }
    }
  }
}
</style>
