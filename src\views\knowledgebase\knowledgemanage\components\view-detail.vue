<template>
  <ui-modal v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="item-detail">
      <item-detail :item-data="activeItem"></item-detail>
    </div>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {},
    activeItem: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '7rem',
      },
    };
  },
  created() {},
  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {},
  components: {
    ItemDetail: require('@/views/knowledgebase/components/item-detail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.item-detail {
  min-height: 400px;
  max-height: 600px;
  overflow-y: scroll;
}
.question-list {
  margin: 50px 0;
}
</style>
