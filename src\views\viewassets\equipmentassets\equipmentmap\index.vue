<template>
  <div class="auto-fill equipmentlibrary">
    <DeviceMap ref="deviceMap">地图模式</DeviceMap>
  </div>
</template>
<script>
export default {
  name: 'equipmentmap',
  components: {
    DeviceMap: require('@/views/viewassets/equipmentassets/equipmentlibrary/device-map.vue').default,
  },
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.equipmentlibrary {
  position: relative;
  .tabs {
    position: absolute;
    right: 30px;
    top: 20px;
    z-index: 11;
  }
}
</style>
