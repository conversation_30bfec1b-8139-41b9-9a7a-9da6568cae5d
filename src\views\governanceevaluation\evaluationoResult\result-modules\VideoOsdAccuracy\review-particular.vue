<!-- 时钟准确率 -->
<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      :default-page-data="pageData"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
    >
      <template #search>
        <ui-label class="inline mr-lg mb-sm" label="设备编码">
          <Input v-model="formData.deviceId" clearable placeholder="请输入设备编码" class="width-md"></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="设备名称">
          <Input v-model="formData.deviceName" clearable placeholder="请输入设备名称" class="width-md"></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="检测结果">
          <Select v-model="formData.outcome" placeholder="请选择检测结果" class="width-md" clearable>
            <Option v-for="item in tagList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="不合格原因">
          <Select
            v-model="tempErrorCodes.reason"
            placeholder="请选择不合格原因"
            class="width-md"
            multiple
            clearable
            @on-change="onChangeReason"
          >
            <Option v-for="item in errorCodesOptionsReason" :value="item" :key="item">{{ item }}</Option>
          </Select>
          <span class="base-text-color ml-xs mr-xs">-</span>
          <Select
            v-model="tempErrorCodes.desc"
            placeholder="请选择不合格原因"
            class="width-md"
            multiple
            clearable
            :disabled="tempErrorCodes.reason.length === 0"
          >
            <Option v-for="item in errorCodesOptionsDesc" :value="item.code" :key="item.code">{{ item.desc }}</Option>
          </Select>
        </ui-label>
        <span class="mb-sm">
          <!--   errorCodesMixin.js         -->
          <Button type="primary" class="mr-sm" @click="search">查询</Button>
          <Button type="default" @click="reset">重置</Button>
        </span>
      </template>
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <span class="font-active-color mr-sm pointer" v-if="statisticShow" @click="updateStatistics"
            >更新统计结果</span
          >
          <Button
            v-permission="{
              route: $route.name,
              permission: 'batchrecheck',
            }"
            type="primary"
            class="mr-sm"
            :disabled="isRecheck"
            @click="handleBatchRecheck"
          >
            <i class="icon-font icon-piliangfujian"></i>
            {{ isRecheck ? '复检中...' : '批量复检' }}
          </Button>
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template slot="outcome" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
        <Tooltip
          transfer
          placement="bottom"
          v-if="
            row.detectionMode != null &&
            (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
          "
        >
          <i class="icon-font icon-wenhao ml-xs f-12" :style="{ color: 'var(--color-warning)' }"> </i>
          <div slot="content">
            <json-viewer
              :expand-depth="5"
              v-if="row.dateImageText != null"
              :value="JSON.parse(row.dateImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
            <json-viewer
              :expand-depth="5"
              v-if="row.additionalImageText != null"
              :value="JSON.parse(row.additionalImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
            <json-viewer
              :expand-depth="5"
              v-if="row.areaImageText != null"
              :value="JSON.parse(row.areaImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
          </div>
        </Tooltip>
      </template>
      <template #detectionMode="{ row }">
        {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
      </template>
      <template #phyStatus="{ row }">
        <span>
          {{ !row.phyStatusText ? '--' : row.phyStatusText }}
        </span>
      </template>

      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #reason="{ row }">
        <Tooltip :content="row.reason" transfer max-width="150">
          {{ row.reason }}
        </Tooltip>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
          class="mr-sm"
        ></ui-btn-tip>
        <ui-btn-tip
          class="mr-md"
          icon="icon-chakanxiangqing"
          content="查看详情"
          @handleClick="showModal(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          v-if="$route.name !== 'cascadelist'"
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
          icon="icon-rengongfujian"
          content="人工复核"
          class="vt-middle f-14 mr-sm"
          @click.native="clickArtificialReview(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="vt-middle f-18 mr-sm"
          icon="icon-tianjiabiaoqian"
          content="添加标签"
          @click.native="addTags(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          v-permission="{
            route: $route.name,
            permission: 'recheck',
          }"
          icon="icon-fujian"
          content="复检"
          class="vt-middle f-14"
          @click.native="clickArtificialRecheck(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
    <!--  详情以及人工复核页  -->
    <review-and-detail
      v-model="reviewDetailVisible"
      :active-index-item="activeIndexItem"
      :review-row-data="osdDetailData"
      :page-data="pageData"
      :total-count="totalCount"
      :table-data="tableData"
      :qualified-list="qualifiedList"
      :styles="{ width: '9.5rem', height: '4.4rem' }"
      :title="reviewDetailTitle"
      :review-visible="reviewVisible"
      :is-get-detail-byapi="true"
      :get-detail-info="getDetailInfo"
      :custorm-params="reviewCustormParams"
      :search-parames="formData"
      @closeFn="updateTable"
    ></review-and-detail>
    <video-player
      :playDeviceCode="playDeviceCode"
      :video-loading="videoLoading"
      v-model="videoVisible"
      :video-url="videoUrl"
      @onCancel="videoUrl = ''"
    ></video-player>
    <!-- 添加标签 -->
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <recheck
      v-model="recheckVisible"
      :module-data="moduleData"
      :is-batch-recheck="isBatchRecheck"
      :table-columns="tableColumns"
      @handleUpdate="initAll()"
    ></recheck>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { mapActions, mapGetters } from 'vuex';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import {
  tableColumns,
  iconStaticsList,
  normalFormData,
} from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoOsdAccuracy/util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import taganalysis from '@/config/api/taganalysis';
import vedio from '@/config/api/vedio-threm';
// 【批量复检】按钮逻辑
import batchRecheckBtn from '../mixins/batchRecheckBtn';
import errorCodesMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/errorCodesMixin.js';

export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch, batchRecheckBtn, errorCodesMixin],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      customSearch: false,
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      chooseOne: {
        tagList: [],
      },
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(tableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      currentRow: {},
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      // 1 - 合格、2 - 不合格 3 - 无法检测
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      statisticShow: false,
      videoUrl: '',
      playDeviceCode: '',
      videoVisible: false,
      videoLoading: false,
      recheckVisible: false,
      moduleData: {},
      isBatchRecheck: false,
      reviewDetailVisible: false,
      reviewVisible: false,
      reviewDetailTitle: '',
      osdDetailData: {},
      reviewCustormParams: {},
      qualifiedList: [
        { key: '设备合格', value: '1' },
        { key: '设备不合格', value: '2' },
      ],
      exportLoading: false,
    };
  },
  created() {
    this.getTagList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    initAll() {
      // 获取列表
      this.getTableData();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    showModal(row) {
      // row.detectionMode == 1 ? 'OCR' : 'SDK'
      if (row.qualified != 1 && (row.isScreenShot !== 1 || !row.screenShot)) {
        let text = row.dataMode === '3' ? '人工复核不合格' : row.errorCodeName;
        this.$Message.error(text);
        return;
      }
      this.reviewDetailVisible = true;
      this.reviewVisible = false;
      this.reviewDetailTitle = '查看字幕标注合规率检测详情';
      this.osdDetailData = { ...row };
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableData();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.getBatchRecheckState();
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        this.totalCount = data.total;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 查看截图
    //视频播放
    async clickRow(row) {
      try {
        this.videoLoading = true;
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      } finally {
        this.videoLoading = false;
      }
    },

    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
        this.$Message.success('更新成功');
        this.statisticShow = false;
      } catch (err) {
        console.log(err);
      }
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    // 人工复核
    clickArtificialReview(row) {
      this.reviewDetailVisible = true;
      this.reviewVisible = true;
      this.reviewDetailTitle = '人工复核';
      this.osdDetailData = { ...row };
      this.reviewCustormParams = {
        taskIndexId: row.taskIndexId,
        deviceId: row.deviceId,
        indexType: this.activeIndexItem.indexType,
      };
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    clickArtificialRecheck(row) {
      this.moduleData = { deviceId: row.deviceId, ...this.activeIndexItem };
      this.isBatchRecheck = false;
      this.recheckVisible = true;
    },
    handleBatchRecheck() {
      // isRecheck - 混入js
      if (this.isRecheck) return;
      this.moduleData = { ...this.activeIndexItem };
      this.isBatchRecheck = true;
      this.recheckVisible = true;
    },
    // isReview：复核过      closeIndex：人工复核弹框关闭时 -设备 所在总表中的索引
    updateTable({ isReview, closeIndex }) {
      let { pageNum, pageSize } = this.pageData;
      let newPage = Math.floor(closeIndex / pageSize) + 1;
      this.pageData.pageNum = newPage;
      if (isReview) {
        this.showRecountBtn();
        this.getTableData();
      } else if (newPage !== pageNum) {
        this.getTableData();
      }
    },
    async getDetailInfo(params) {
      try {
        const {
          data: { data },
        } = await this.$http.get(evaluationoverview.queryOsdDetailById + params.id);
        const rowType = [
          { dataKey: 'clockDetail', dataValue: '时钟信息' },
          { dataKey: 'areaInfo', dataValue: '区划和地址' },
          { dataKey: 'deviceInfo', dataValue: '摄像机信息' },
        ];
        let detialTableData = [];
        rowType.forEach((item) => {
          if (!!data[item.dataKey] && !!data[item.dataKey].isDetect) {
            let rowData = {
              colTitle: item.dataKey,
              colTitleText: item.dataValue,
              ...data[item.dataKey],
            };
            detialTableData.push(rowData);
          }
        });
        if (data.qualified) {
          detialTableData.push({ resQualified: data.qualified });
        }
        return {
          detialTableData,
          imageUrl: data.imageUrl,
          info: data,
        };
      } catch (e) {
        console.log(e);
        return {
          detialTableData: [],
          imageUrl: '',
          info: {},
        };
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    VideoPlayer: require('@/views/governanceevaluation/evaluationoResult/components/video-player.vue').default,
    recheck: require('../components/recheck/index.vue').default,
    reviewAndDetail: require('../components/review-and-detail').default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
</style>
