@_deep: ~'>>>';

[data-theme='light'],
[data-theme='deepBlue'] {
  // 评测结果 - 统计图表
  .detection-Result .rule-container,
  .statistical-echarts {
    .history-wrapper,
    .rank,
    .unqualified {
      padding-right: 10px;
    }
  }
}

// 铺满屏幕要减去导航栏的高度
.wrapper-full {
  position: absolute;
  width: 100%;
  top: 53px;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #0b224b;
}

// 高度100%
.height-full {
  height: 100%;
}

// 具有margin右侧的盒子
.margin-wrapper {
  margin: 10px 20px;
  font-size: 12px;
  height: calc(~'100% - 20px');
  position: relative;
}

// 具有margin的右侧盒子且不允许滚动
.margin-wrapper-flow {
  padding: 10px;
  position: relative;
  overflow: hidden;
}

// 自动填充
.auto-fill {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

i {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
}

.icon-operation-color {
  font-size: 14px;
  cursor: pointer;
  color: var(--color-border-card-tab);
  &:hover {
    color: var(--color-primary);
  }
}

// 通用样式
.fl {
  float: left;
}

.fr {
  float: right;
}

.inline {
  display: inline-block;
}

.block {
  display: block;
}

.vt-middle {
  vertical-align: middle;
}
.vt-bottom {
  vertical-align: bottom;
}

.pointer {
  cursor: pointer;
}

.allowed {
  cursor: not-allowed;
  opacity: 0.8;
}

.t-center {
  text-align: center;
}

.t-left {
  text-align: left;
}

.t-right {
  text-align: right;
}

.over-flow {
  overflow: hidden;
}

.scroll-y {
  overflow-x: hidden;
  overflow-y: auto;
}

.font-weight {
  font-weight: 900;
}

.font-orange {
  color: #ff9900;
}

.font-yellow {
  color: #ffd725;
}

.font-blue {
  color: var(--color-active) !important;
}

.font-green {
  color: #19c176;
}

.font-purple {
  color: #aa8fd9;
}

.font-warning {
  color: var(--color-failed);
}

.font-gray {
  color: #828a98;
}

.font-white {
  color: #ffffff;
}

.font-pink {
  color: #ff8c9b;
}

.font-dark-blue {
  color: var(--color-primary);
}

.red-border {
  border: 1px solid #ed4014 !important;
}

.shadow {
  box-shadow: 4px 4px 10px #eee;
}

.f-12 {
  font-size: 12px !important;
}

.f-14 {
  font-size: 14px !important;
}

.f-16 {
  font-size: 16px !important;
}

.f-18 {
  font-size: 18px !important;
}

.f-20 {
  font-size: 20px !important;
}

.pb-sm {
  padding-bottom: 10px;
}

.pt-sm {
  padding-top: 10px;
}
.pl-lg {
  padding-left: 20px;
}
.mb-xs {
  margin-bottom: 5px;
}

.mb-mini {
  margin-bottom: 3px;
}

.mb-sm {
  margin-bottom: 10px;
}

.mb-md {
  margin-bottom: 15px;
}

.mb-lg {
  margin-bottom: 20px;
}

.ml-mini {
  margin-left: 3px;
}

.ml-xs {
  margin-left: 5px;
}

.ml-sm {
  margin-left: 10px;
}

.ml-md {
  margin-left: 15px;
}

.ml-lg {
  margin-left: 30px;
}

.mr-mini {
  margin-right: 3px;
}

.mr-xs {
  margin-right: 5px;
}

.mr-sm {
  margin-right: 10px;
}

.mr-md {
  margin-right: 15px;
}

.mr-lg {
  margin-right: 30px;
}

.mt-xs {
  margin-top: 5px;
}

.mt-sm {
  margin-top: 10px;
}

.mt-md {
  margin-top: 15px;
}

.mt-lg {
  margin-top: 20px;
}

.width-percent {
  width: 100%;
}

.width-name {
  width: 40px;
}

.width-mini {
  width: 80px;
}

.width-xs {
  width: 120px;
}

.width-sm {
  width: 160px;
}

.width-md {
  width: 200px !important;
}

.width-lg {
  width: 230px !important;
}

.width-num {
  width: 71px;
}

.width-input {
  width: 180px;
}

.icon-nan {
  color: #91e0fe;
  font-size: 12px;
}

.icon-nv {
  color: #ffb6c1 !important;
  font-size: 12px;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.word-wrap {
  word-wrap: break-word;
  word-break: normal;
}

// 选择摄像机
.camera {
  text-align: center;
  border-radius: 2px;
  border: 1px solid var(--border-input);
  display: inline-block;
  cursor: pointer;
  padding: 7px 10px;
  line-height: initial;
  &:hover {
    border: 1px solid var(--border-input-hover);
  }
}

.camera-select {
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #2e4e65;

  .camera-circle {
    width: 60px;
    height: 35px;
    background: #0f2f59;
    border: 1px solid var(--color-primary);
    border-radius: 2px;
    cursor: pointer;
  }
}

.flex-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.d_flex {
  display: flex;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.tag-red {
  background: #a5320f !important;
  color: #fff;
}

.tag-green {
  background: #0e8f0e !important;
  color: #fff;
}

.tag-yellow {
  background: #e8bc22;
  color: #fff;
}

// table scroll重置
.no-scroll {
  // .ivu-table-body {
  //     display: block !important;
  //     height: auto !important;
  // }
}

.relative {
  position: relative;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 80px;
  width: 80px;
  .no-data-img {
    font-size: 50px;
    color: var(--color-no-data-img);
  }
  .null-data-text {
    color: var(--color-no-data-img);
    font-size: 16px;
    line-height: 1.5;
    margin-right: 10px;
  }
}

.check-status {
  display: inline-block;
  padding: 0 8px;
  border-radius: 4px;
  background: #979898;
  color: #fff;

  &.bg-color-grey {
    background-color: #979898;
  }

  &.bg-success {
    background-color: var(--color-success);
  }

  &.bg-failed {
    background-color: var(--color-failed);
  }

  &.bg-other {
    background-color: var(--color-primary);
  }
}

.check-status-font {
  display: inline-block;
  padding: 0 8px;
  border-radius: 4px;
  color: var(--color-warning);
  &.font-success {
    color: var(--color-success);
  }

  &.font-failed {
    color: var(--color-failed);
  }
}

//任务追踪头部标题文字
.updata-time {
  height: 30px;
  line-height: 30px;
  margin-top: -20px;
  margin-bottom: 15px;
  label {
    width: 100px;
    margin-right: 20px;
  }
  .search {
    margin-top: 20px;
    width: 530px;
    // float: right;
    // display: flex;
    .ivu-select {
      width: 230px;
      margin-right: 30px !important;
    }
    .ivu-select {
      width: 165px;
    }
  }
}

.title-text {
  margin-bottom: 15px;
  .icon-shishishipinliutongchangjiance1,
  .icon-lishishipinliutongchangjiance,
  .icon-OSDzimubiaozhuheguijiance {
    color: #0f84e9;
    margin-right: 10px;
  }
  span {
    margin-right: 25px;
  }
  span:nth-child(1) {
    i {
      color: var(--color-bluish-green-text);
    }
  }
  span:nth-child(2) {
    i {
      color: var(--color-failed);
    }
  }
}

.hide-input {
  position: relative;
  width: 0;
  opacity: 0;
  input {
    padding: 0;
  }
}

.plr-30 {
  padding: 0 30px 0 30px !important;
}

.dis-select {
  user-select: none;
}

.device-id {
  text-decoration: underline;
}

.clear-b {
  clear: both;
}

.icon-search-input {
  .ivu-input-group-append {
    // background: linear-gradient(180deg, #2b84e2 0%, #083a78 100%);
    background: var(--bg-btn-primary);
    border: none;
    color: #fff;
    &:hover {
      // background: linear-gradient(180deg, #65a7ee 0%, #175cb2 100%);
      background: var(--bg-btn-primary-hover);
      border: none;
    }
  }
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;
  z-index: 2;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
.index-select-dropdown {
  @{_deep}.ivu-dropdown-transfer {
    bottom: 1% !important;
  }
}

// 可点击
.link-text-box {
  color: var(--color-active);
  text-decoration: underline;
}

.flex-aic {
  display: flex;
  align-items: center;
}

.c-green {
  color: #0e8f0e;
}
.c-red {
  color: var(--color-failed);
}

.mt-minus-sm {
  margin-top: -10px;
}

.hide {
  display: none;
}

.base-text-color {
  color: var(--color-content);
}
.base-label-color {
  color: var(--color-label);
}

.bg-success {
  background-color: var(--color-success);
}
.bg-warning {
  background-color: var(--color-warning);
}
.bg-failed {
  background-color: var(--color-failed);
}
.bg-offline {
  background-color: var(--color-offline);
}
.bg-active {
  color: var(--color-active);
}
.color-success {
  color: var(--color-success);
}
.color-warning {
  color: var(--color-warning);
}
.color-failed {
  color: var(--color-failed);
}
.color-offline {
  color: var(--color-offline);
}
.color-active {
  color: var(--color-active);
}
.font-other {
  color: var(--color-active);
}
.color-table-btn-default {
  color: var(--color-table-btn-default);
}
.color-table-btn-more {
  color: var(--color-table-btn-more);
}

// 问号图标
.icon-wenhao {
  background: var(--bg-wenhao) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}

.font-active-color {
  color: var(--color-primary);
  &:hover {
    color: var(--color-btn-dashed-hover);
  }
}

.font-red {
  .color-failed;
}
