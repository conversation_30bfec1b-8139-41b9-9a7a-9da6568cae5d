.container {
  flex: 1;
  background-color: @layout-container-color;
  border-radius: 4px;
  box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
  padding: 16px 20px 0 20px;
}

.btn-group {
  white-space: nowrap;

  button+button {
    margin-left: 10px;
  }
}

.btn-tips {
  &>div {
    margin-right: 20px;
  }

  &>div:last-child {
    margin: 0;
  }
}

.action {
  button {
    margin-right: 30px;
  }

  span {
    color: @primary-color;
    cursor: pointer;

    &:hover {
      color: @link-hover-color;
    }

    &:active {
      color: @link-active-color;
    }
  }
}

// 超出一行显示省略号
.ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

// 超出二行显示省略号
.two-ellipsis {
  width: 100%;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  display: inline-block;
}

.primary {
  color: @primary-color !important;
}

.primary:hover {
  color: @primary-color !important;
}

.warning {
  color: @warning-color;
}

.error {
  color: @error-color;
}

// 卡片边框
.card-border-color {
  border-color: @border-color !important;
}

.card-border-active-color {
  border-color: @primary-color !important;
}

// 卡片背景
.card-bg {
  background-color: #f9f9f9;
}

.card-active-bg {
  background-color: fade(@primary-color, 10);
}

// 卡片投影
.card-box-shadow {
  box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
}

.card-box {
  border: 1px solid @border-color;
  background-color: #f9f9f9;
  box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
}

// 主题色边框
.border-primary {
  border-color: @primary-color !important;
}

// 图片上的标签
.pic-tag-warning,
.pic-tag-warning-small,
.pic-tag-primary,
.pic-tag-primary-small {
  font-size: 12px;
  padding: 3px;
  color: @white;
  background: @warning-color;
  line-height: 12px;
  border-bottom-right-radius: 4px;
  position: absolute;
  top: 0;
  left: 0;
}

.pic-tag-warning,
.pic-tag-primary {
  font-size: 14px;
  line-height: 14px;
  padding: 4px 8px;
  font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
  font-weight: bold;
}

.pic-tag-primary,
.pic-tag-primary-small {
  background-color: @primary-color;
}

// 图片上的标题
.pic-title {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: @white;
  font-size: 14px;
  line-height: 20px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 更多按钮
.more-btn {
  font-size: 14px;
  color: @auxiliary-font-color;
  cursor: pointer;
  line-height: 20px;
  display: flex;
  align-items: center;

  .iconfont {
    color: #888;
    font-size: 12px;
    line-height: 20px;
    margin-left: 5px;
  }
}

// 标题颜色90%
.title-color {
  color: @title-font-color;
}

// 正文颜色80%
.text-color {
  color: @text-font-color;
}

//次要文字60%
.text-secondary {
  color: @secondary-font-color
}

// 输入框颜色75%
.input-color {
  color: @input-font-color;
}

// label颜色45%
.label-color {
  color: fade(@black, 45%);
}

.info-color {
  color: rgba(0, 0, 0, 0.9);
}
.info-color-sub {
  color: rgba(0, 0, 0, 0.8);
}
// 辅助文字35%
.auxiliary-color {
  color: @auxiliary-font-color;
}

.ui-card,
.ui-card-hover {
  width: 100%;
  height: 100%;
  background: #FFFFFF;
  box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
  border-radius: 4px;
  overflow: hidden;

  .card-head {
    width: 100%;
    height: 30px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .title {
      font-size: 16px;
      font-weight: bold;
      position: relative;
      color: #fff;
      line-height: 30px;
      text-align: center;
      background: @linear-color;
      transform: skewX(-18deg);
      padding: 0 23px;
      left: -6px;

      span {
        transform: skewX(18deg);
        display: inline-block;
      }
    }
  }

  .card-content {
    width: 100%;
    height: calc(~'100% - 30px');
    box-sizing: border-box;
    position: relative;
  }
}

.ui-card-hover:hover {
  // cursor: pointer;
  box-shadow: 0 0 2px rgba(255, 255, 255, .2);
}

.flex {
  display: flex;
  width: 100%;
}

.flex-s {
  display: flex;
  justify-content: flex-start;
  align-content: center;
}

.flex-b {
  display: flex;
  justify-content: space-between;
  align-content: center;
}

.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-30 {
  margin-left: 30px;
}

.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-30 {
  margin-right: 30px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-5 {
  margin-bottom: 5px;
}

.mb-20 {
  margin-bottom: 20px;
}

.pb-10 {
  padding-bottom: 10px;
}

// 底部虚线
.border-d {
  border-bottom: 1px dashed #D3D7DE;
}

// 小手
.cursor-p {
  cursor: pointer;
}

// 自打填充
.auto-fill {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

//全息档案卡片hover
.list-card-hover:hover {
  border-radius: 4px;
  border: 2px solid @primary-color;
}

// 推荐信息卡片
.recommended-information-card {
  .content-ul {
    .content-text {
      color: @title-font-color;
    }

    .content-source {
      color: @auxiliary-font-color;

      .source-count {
        color: @primary-color;
      }
    }
  }
}

.recommended-information-card:hover {

  .card-head,
  .content-ul {
    box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
  }
}

// 来源气泡提示
.source-poptip {
  .ivu-poptip-content {
    width: 150px;

    .ivu-poptip-inner {
      box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
    }

    .ivu-poptip-body {
      padding: 5px 10px;

      .source-ul {
        font-size: 12px;

        .source-li {
          color: @secondary-font-color;
          display: flex;
          margin: 5px;

          .source-text {
            font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
            font-weight: bold;
            color: @title-font-color;
            line-height: 18px;
          }
        }
      }
    }
  }
}

// 资产card/人像抓拍card
.list-card {
  .list-card-head {
    border-color: @border-color !important;

    .header-icon {
      color: @white;
    }

    .address-text {
      color: @primary-color;
    }
  }
}

// 车牌
.license-plate-small {
  display: inline-block;
  background: #2379F9;
  border-radius: 2px;
  font-weight: bold;
  font-size: 14px;
  line-height: 14px;
  font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
  padding: 4px 6px;
  color: @white;
  position: relative;
}

.license-plate-small::after {
  content: '';
  border: 1px solid @white;
  position: absolute;
  width: calc(~'100% - 4px');
  height: calc(~'100% - 4px');
  top: 2px;
  left: 2px;
  border-radius: 2px;
}

// 人车同拍轴卡片
.people-car-capture-time-line {
  .ivu-timeline-item {
    display: flex;
    align-items: center;
    padding: 0 0 10px 0;

    .ivu-timeline-item-tail {
      left: 11.5px;
      border-color: @border-color;
    }

    .ivu-timeline-item-head {
      width: 24px;
      height: 24px;
      padding: 0;
      left: 0px;
      transform: none;
      margin-top: 0;
    }
  }

  .ivu-timeline-item:first-child {
    .ivu-timeline-item-tail::before {
      content: '';
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-bottom: 5px solid @border-color;
      position: absolute;
      top: -2px;
      left: 0;
      transform: translate(-50%, 0);
    }
  }

  .ivu-timeline-item:last-child {
    padding-bottom: 0 !important;

    .ivu-timeline-item-tail::after {
      content: '';
      width: 0;
      height: 0;
      border-left: 4px solid transparent;
      border-right: 4px solid transparent;
      border-top: 5px solid @border-color;
      position: absolute;
      bottom: -2px;
      left: 0;
      transform: translate(-50%, 0);
    }
  }

  .ivu-timeline-item-content {
    padding: 0 0 0 34px;
    top: 0;
    width: 100%;
  }

  .ivu-timeline-item:last-child .ivu-timeline-item-tail {
    display: block;
  }
}

// 卡片切换按钮
.card-btn,
.card-active-btn {
  border: 1px solid @border-color !important;
  background: @white !important;
  border-radius: 2px !important;
  padding: 0px 10px !important;
  color: @secondary-font-color !important;
  font-size: 14px !important;
  height: 24px !important;
  display: inline-flex;
  align-items: center;

  &>span {
    line-height: 14px !important;
  }
}

.card-active-btn {
  background: @primary-color !important;
  color: @white !important;
  border: 1px solid @primary-color !important;
}

// warning 标签
.warning-tag-small {
  background: @warning-color;
  color: @white;
  font-size: 12px;
  line-height: 12px;
  padding: 4px;
  border-radius: 4px;
}

// 搜索框
.search {
  width: 100%;
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid @border-color;
  justify-content: space-between;

  .ivu-dropdown-rel {
    a {
      color: @primary-color;
    }
  }

  .ivu-form-item {
    display: inline-flex;
    // margin-bottom: 10px;
    margin-bottom: 16px;
    margin-right: 30px;

    /deep/ .ivu-form-item-label {
      white-space: nowrap;
    }

    .ivu-form-item-content {

      &>.ivu-input-wrapper,
      &>.ivu-select {
        width: 200px;
      }

      .ivu-date-picker {
        width: 300px;
      }
    }
  }

  .ivu-form-item-label {
    color: @label-font-color;
  }

  .advanced-search {
    background: @white;
    box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
  }

  .search-input-430 {
    width: 430px !important;
  }
}

// 请选择标签按钮
.select-tag-button {
  width: 200px;
  height: 34px;
  border: 1px dashed @primary-color;
  color: @primary-color;
  font-size: 14px;
  line-height: 20px;
  background: fade(@primary-color, 10%);
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.select-tag-button:hover {
  background: fade(@primary-color, 20%);
}

// 列表切换渐变按钮
.change-btn {
  background: @linear-color;
  color: @white;
}

.change-btn:hover {
  background: @linear-hover-color;
}

.change-btn:active {
  background: @linear-active-color;
}

// 黄色
.warning-change-btn {
  background: @warning-linear-color;
}

.warning-change-btn:hover {
  background: @warning-linear-hover-color;
}

.warning-change-btn:active {
  background: @warning-linear-active-color;
}

// 标签气泡提示
.tag-poptip {
  .ivu-poptip-inner {
    box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;

    .ivu-poptip-body {
      padding: 6px 5px 6px 10px;

      .ui-tag-item {
        padding: 2px 6px;

        .ui-tag-text {
          font-size: 12px;
        }
      }
    }
  }
}

// 车牌颜色类型的标签
.plain-tag {
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;

  div {
    font-size: 12px;
    color: @text-font-color;
    line-height: 18px;
    padding: 0 8px;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
  }

  .yellow-plate::after {
    content: '';
    width: 8px;
    height: 100%;
    background: #FDEE38;
    position: absolute;
    left: 0;
    top: 0;
  }
}

// 车身颜色类型的标签
.plain-tag-color {
  border-width: 1px;
  border-style: solid;
  border-radius: 2px;

  div {
    width: 18px;
    height: 18px;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
  }

  .yellow-body::after {
    content: '';
    width: 0;
    height: 0;
    border-top: 18px solid #FDEE38;
    border-right: 18px solid transparent;
    position: absolute;
    top: 0;
    left: 0;
  }
}

.ivu-date-picker-360 {
  width: 360px;

  .ivu-input-wrapper {
    width: 100% !important;
  }
}

// tag-select
.ivu-tag-select-checked-option {
  border: 1px solid @primary-color;
}

.bg-primary-event:hover {
  background: fade(@primary-color, 20%);
}

.bg-primary,
.bg-primary:hover {
  background: @primary-color;
}

.bg-success {
  background: @success-color;
}

.bg-warning {
  background: @warning-color;
}

.bg-error {
  background: @error-color;
}

.bg-blue {
  background: @primary-color;
  background: @blue-color;
}

.bg-border-color {
  background: @border-color;
}

.bg-info {
  background: @info-color
}

.bg-f9 {
  background: @table-tr-odd-bg-color
}

.color-primary {
  color: @primary-color;
}

.color-success {
  color: @success-color;
}

.color-warning {
  color: @warning-color;
}

.color-error {
  color: @error-color;
}

.color-blue {
  color: @blue-color;
}

.color-info {
  color: @info-color
}

.m-b20 {
  margin-bottom: 20px;
}

// ----------在线分析角标--------------
.badge-online:before {
  background: @success-color;
}

.badge-offline:before {
  background: @info-color;
}

// 一机一档绿色
.bg-green {
  background: #1FAF8A;
}

.color-green {
  color: #1FAF8A;
}

// 树下拉框
.ivu-select-selection>div {
  max-height: 66px;
  overflow: auto;
  display: flex;
  flex-wrap: wrap;
}

.ivu-select-multiple .ivu-select-input {
  height: 32px;
  top: 0;
}

.select-node-tree {
  .el-checkbox {
    display: none;
  }

  .el-tree-node__content .is-checked+.el-tree-node__label {
    color: @primary-color
  }

  .el-tree-node__content .is-checked+.el-tree-node__label::after {
    content: "\F375";
    font-family: "Ionicons";
    position: absolute;
    right: 10px;
    line-height: 20px;
    text-align: right;
    color: @primary-color;
    font-size: 14px;
    vertical-align: middle;
  }

  .el-tree-node:focus>.el-tree-node__content {
    background-color: transparent;
  }
}

// 搜索树形下拉框
.ivu-dropdown-rel .filter-tree>.ivu-select-dropdown {
  display: none;
}

.filter-tree {
  .ivu-select-dropdown {
    max-height: 200px !important;
    overflow: auto;
    padding: 0 10px !important
  }

  .el-tree {
    width: 100%;
    display: table;
    margin: 4px 0 16px 0;
  }

  .ivu-select-input {
    padding: 0 0 0 6px;
  }

  .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
  .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    padding-left: 6px !important;
    padding-right: 10px !important;
  }

  .el-tree-node:focus>.el-tree-node__content {
    background-color: transparent;
  }

  .el-tree__empty-block {
    min-height: 50px;

    .el-tree__empty-text {
      color: #fff;
    }
  }

  .el-tree-node__expand-icon {
    font-size: 16px;
    padding: 0;
    margin: 0 10px 0 20px;
  }

  .el-tree-node__expand-icon.is-leaf {
    color: transparent;
    cursor: default;
    margin: 0;
    padding: 0 6px;
  }

  .el-tree-node__label {
    line-height: 20px;
  }
}

// 六边形
.hexagon {
  position: relative;
  width: 60px;
  height: 120px;
  margin: 60px auto;
  background-color: rgba(211, 238, 255, 0.2);
}

.hexagon::before {
  content: '';
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  right: 60px;
  border-width: 60px 40px;
  border-style: solid;
  border-color: transparent rgba(211, 238, 255, 0.2) transparent transparent;
}

.hexagon::after {
  content: '';
  display: block;
  position: absolute;
  width: 0;
  height: 0;
  left: 60px;
  border-width: 60px 40px;
  border-style: solid;
  border-color: transparent transparent transparent rgba(211, 238, 255, 0.2);
  top: 0;
}

.font-important {
  font-size: 16px !important;
  font-weight: bold;
  color: @primary-color !important;
}

// 图上作战-普通搜索结果-底部分页
.general-search-footer {
  padding: 0 10px;
  display: flex;
  height: 56px;
  align-items: center;

  .pages {
    height: 56px;

    .total {
      font-size: 12px !important;
    }

    .ivu-page {
      .ivu-page-simple-pager {
        >input {
          font-size: 12px;
          padding: 0px 5px;
          height: 20px;
        }
      }
    }
  }
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nullLabel {
  margin-left: -20px;

  /deep/ .ivu-form-item-label {
    width: 0 !important;
  }
}