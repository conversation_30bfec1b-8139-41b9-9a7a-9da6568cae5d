<template>
  <div>
    <ui-modal ref="modal" title="图像上传及时性配置">
      <!-- 图像上传及时性配置 -->
      <div class="quality">
        重点车辆卡口数据时延：不超过
        <Input type="number" v-model="extraParam.important" placeholder="值" style="width: 100px" size="small" />
        <Select v-model="extraParam.importantTime" style="width: 80px; margin-left: 10px">
          <Option v-for="item in timeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </div>
      <div class="quality">
        普通车辆卡口数据时延：不超过
        <Input type="number" v-model="extraParam.general" placeholder="值" style="width: 100px" size="small" />
        <Select v-model="extraParam.generalTime" style="width: 80px; height: 34px; margin-left: 10px">
          <Option v-for="item in timeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </div>

      <template slot="footer">
        <Button @click="submit" type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>

    <!-- <ui-modal> -->
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  name: 'carDialog',
  props: {},
  data() {
    return {
      extraParam: {
        important: '',
        importantTime: '',
        general: '',
        generalTime: '',
      },
      timeList: [
        { value: 1, label: '时' },
        { value: 2, label: '分' },
        { value: 3, label: '秒' },
      ],
    };
  },
  async created() {},
  computed: {},
  methods: {
    showModal() {
      this.$refs.modal.modalShow = true;
      this.init();
    },

    init() {
      this.$http
        .get(api.queryCarUpload + '28')
        .then((res) => {
          if (res.data.code == 200) {
            this.extraParam = JSON.parse(res.data.data.extraParam);
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    submit() {
      var param = {
        topicComponentId: 28,
        extraParam: JSON.stringify(this.extraParam),
      };
      this.$http
        .post(api.addCarUpload, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.$refs.modal.modalShow = false;
            this.$Message.success('配置成功');
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.ivu-checkbox-group-item {
  padding: 6px 0;
}

.quality {
  padding: 10px 0;
  color: #fff;
}

.explain {
  color: #c76d28;
}
.p {
  margin-top: 10px;
  color: #fff;
}

@{_deep} .ivu-modal-body {
  padding: 20px 51px 37px;
}
</style>
