<template>
  <ui-card :title="title" >
    <div slot="extra" class="more-btn mr-20" @click="peersMoreHandle">更多<i class="iconfont icon-more"></i></div>
    <div class="traffic-offence-container">
      <traffic-offence-detail
        :key='index'
        :data='item'
        :plateNo='item.hphm'
        :column='column'
        v-for='(item,index) in computedTableList'>
      </traffic-offence-detail>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!tableList || !tableList.length) && !loading" />
    <ui-modal v-model="visible" footer-hide :rWidth='1500'>
      <template #header>
        <div class="detail-title">
          <p>{{title}}</p>
        </div>
      </template>
      <div class='more-traffic-offence-container'>
        <traffic-offence-detail
          :key='index'
          :data='item'
          :plateNo='item.hphm'
          :column='column'
          v-for='(item,index) in tableList'>
        </traffic-offence-detail>
      </div>
    </ui-modal>
  </ui-card>
</template>
<script>
import UiListCard from '@/components/ui-list-card.vue'
import TrafficOffenceDetail from '@/views/holographic-archives/components/traffic-offence/components/traffic-offence-detail.vue';
export default {
  name: 'traffic-offence',
  props: {
    data: {},
    title: {},
    loading: {},
  },
  components: {
    TrafficOffenceDetail,
    UiListCard,
  },
  mixins: [],
  data() {
    return {
      tableList: [],
      column: [
        {label: "车主姓名", key: "jdcsyr"},
        // {label: "车牌号码", key: "hphm"},
        {label: "处理时间", key: "clsj"},
        {label: "违法时间", key: "wfsj"},
        {label: "违法地址", key: "wfdz"},
        {label: "违法行为", key: "wfxw"},
        {label: "违法记分", key: "wfjfs"},
        {label: "罚款金额", key: "fkje"},
        {label: "备注", key: "bz"},
      ],
      visible: false,
    };
  },
  computed: {
    computedTableList() {
      if (this.tableList.length > 4){
        return this.tableList.slice(0, 4)
      }
      return this.tableList;
    }
  },
  created() {
  },
  mounted() {
  },
  watch: {
    data:{
      deep: true,
      immediate: true,
      handler(val){
        this.tableList = val || []
      }
    }
  },
  methods: {
    peersMoreHandle(){
      if (!this.tableList || this.tableList.length < 5) return this.$Message.warning("没有更多了")
      this.visible = true
    },
  },
};
</script>

<style scoped lang='less'>
/deep/ .ivu-modal-body {
  overflow-y: auto;
}
/deep/ .card-content {
  padding: 20px 15px !important;
}

.more-traffic-offence-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  /deep/ .traffic-offence-content {
    //calc(~'100% - 149px');
    width: calc(~'(100% - 40px) /4') !important;
    margin-bottom: 10px;
  }
}
.traffic-offence-container {
  display: flex;
  /deep/ .traffic-offence-content {
    width: calc(~'100%/4') !important;
  }
}
</style>
