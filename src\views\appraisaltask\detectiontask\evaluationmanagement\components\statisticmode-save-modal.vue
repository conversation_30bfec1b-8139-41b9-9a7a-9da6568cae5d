<template>
  <ui-modal
    v-model="visible"
    :styles="{ width: '3.5rem' }"
    title="保存提醒"
    @query="handleSave"
    @on-cancel="handlecancel"
  >
    <div class="statistic-mode-save">
      <p class="mb-md font-blue">【统计模式】有变化，请确定要使用该统计模式的指标：</p>
      <div class="collapse-box" v-scroll="200">
        <Collapse v-model="collapseValue" simple>
          <Panel :name="`${indexType.id}`" class="mb-sm" v-for="indexType in indexTypeList" :key="indexType.id">
            <Checkbox
              v-model="indexType.checked"
              @click.native.stop
              @on-change="
                (val) => {
                  changeIndexType(val, indexType.id);
                }
              "
            >
              <span class="title">{{ indexType?.title }} </span>
            </Checkbox>
            <div slot="content">
              <div v-for="indexConfig in returnShowIndexConfig(indexType.id)" :key="indexConfig.id">
                <Checkbox
                  v-model="indexConfig.checked"
                  class="mb-sm"
                  @on-change="
                    (val) => {
                      changeIndexConfig(val, indexConfig.id);
                    }
                  "
                >
                  <span>{{ indexConfig?.indexName }} </span>
                </Checkbox>
              </div>
            </div>
          </Panel>
        </Collapse>
      </div>
    </div>
  </ui-modal>
</template>
<script>
export default {
  name: 'statisticeModal',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    indexConfigData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      visible: false,
      collapseValue: [],
      indexTypeList: [],
      configList: [], //带复选框的列表
    };
  },
  computed: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      this.visible = val;
      if (val) {
        this.collapseValue = [];
        this.setModule();
        this.setIndexConfigData();
      }
    },
  },
  methods: {
    // 设置模块
    setModule() {
      let filterList = this.global.indexTypeList.filter((item) => {
        return this.indexConfigData.find((config) => {
          return item.id == config.indexModule;
        });
      });
      this.indexTypeList = filterList.map((item) => {
        return { ...item, checked: true };
      });
      // this.collapseValue = this.indexTypeList.map((item) => `${item.id}`);
    },
    //设置指标
    setIndexConfigData() {
      this.configList = this.indexConfigData.map((item) => {
        return {
          ...item,
          checked: true,
        };
      });
    },
    returnShowIndexConfig(indexTypeId) {
      return this.configList.filter((item) => item.indexModule == indexTypeId);
    },
    //切换指标大类复选
    changeIndexType(val, indexTypeId) {
      this.configList.forEach((item) => {
        if (item.indexModule == indexTypeId) {
          item.checked = val;
        }
      });
    },
    //切换单个指标复选
    changeIndexConfig(val, indexConfigId) {
      this.configList.forEach((item) => {
        if (item.id == indexConfigId) {
          item.checked = val;
        }
      });
    },
    handleSave() {
      let filterList = this.configList.filter((item) => {
        return item.checked && item.config;
      });
      let indexIdList = filterList.map((item) => item.indexId);
      //需要过滤掉未勾选和未配置的指标
      this.$emit('saveStatisticIndex', indexIdList);
      this.visible = false;
    },
    handlecancel() {
      this.$emit('cancelStatisticIndex');
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.statistic-mode-save {
  .collapse-box {
    min-height: 200px;
    max-height: 600px;
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: #19d5f6;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: var(--bg-collapse-item);
        height: 50px;
        line-height: 50px;
        .title {
          font-size: 14px;
          font-weight: 400;
        }
        .switch {
          top: 50%;
          transform: translateY(-50%);
          margin-right: 20px;
        }
        i {
          float: right;
          color: #fff;
          line-height: 50px;
          font-size: 20px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: #041939;
        .ivu-collapse-content-box {
          padding: 10px 40px;
        }
      }
      &.ivu-collapse-item-active {
        background: #041939;
      }
    }
  }
}
</style>
