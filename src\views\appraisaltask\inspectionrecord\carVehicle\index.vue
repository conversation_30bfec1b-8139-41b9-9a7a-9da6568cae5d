<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />
    <!-- 机构数 ---------------------------------------------------------------------------------------------------- -->
    <!-- <SlideUnitTree
      :treeData="taskObj.treeData"
      @selectOrgCode="selectOrgCode"
      :current-node-key="getDefaultSelectedOrg.orgCode"
      :select-key="selectKey"
    >
    </SlideUnitTree> -->
    <!-- 模式切换 ---------------------------------------------------------------------------------------------------- -->
    <tagView class="tagView" ref="tagView" :list="['图片模式', '设备模式']" @tagChange="tagChange1" />
    <!-- 图片模式 ---------------------------------------------------------------------------------------------------- -->
    <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo" v-if="modelTag == 0">
      <div slot="search" class="hearder-title">
        <SearchCard
          ref="search"
          :cardSearchList="cardSearchList"
          @startSearch="startSearch"
          :taskObj="taskObj"
          :treeData="treeData"
        />
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <UiGatherCard
          v-if="cardInfo.length > 0 && [402, 404, 405, 406].includes(currentTree.id)"
          class="card"
          :list="row"
          :cardInfo="cardInfo"
          @bigImageUrl="bigImageUrl"
        ></UiGatherCard>
        <UiGatherCard1
          v-else-if="cardInfo.length > 0 && [403].includes(currentTree.id)"
          class="card2"
          :list="row"
          :cardInfo="cardInfo"
          @bigImageUrl="bigImageUrl"
        ></UiGatherCard1>
        <InfoCard v-else class="card1" :list="row" :cardInfo="cardInfo" @bigImageUrl="bigImageUrl"> </InfoCard>
      </template>
    </TableCard>
    <!-- 设备模式 ---------------------------------------------------------------------------------------------------- -->
    <TableList
      v-if="modelTag == 1 && columns.length > 0"
      ref="infoList"
      :columns="columns"
      :minusHeight="minusHeight"
      :loadData="loadDataList"
    >
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <SearcList
          :currentTree="currentTree"
          :lable="lable"
          :width="width"
          ref="search"
          :searchList="searchList"
          @startSearch="startSearch"
          :taskObj="taskObj"
          :treeData="treeData"
        />
      </div>
      <!-- 表格操作 -->
      <template #action="{ row }">
        <!-- <span class="font-table-action pointer" @click="bigImageUrl1(row)">查看不合格图片</span> -->
        <ui-btn-tip
          icon="icon-chakanyichangxiangqing"
          content="查看不合格图片"
          @click.native="bigImageUrl1(row)"
        ></ui-btn-tip>
      </template>
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
    </TableList>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    TableList: require('../components/tableList.vue').default,
    TableCard: require('../components/tableCard.vue').default,
    tagView: require('../components/tags').default,
    SearchCard: require('./component/searchCard.vue').default,
    SearcList: require('./component/searchList.vue').default,
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    UiGatherCard: require('./component/ui-gather-card.vue').default,
    UiGatherCard1: require('./component/ui-gather-card1.vue').default,
    InfoCard: require('./component/infoCard.vue').default,
    LookScene: require('@/components/look-scene').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      treeData: [],
      lable: '', // 设备模式数量检索展示
      width: 155, // 设备模式数量检索展示对应label宽度
      bigPictureShow: false, // 打图展示
      imgList: [], // 大图图片
      searchList: [], // 设备模式检索下拉框
      abnormalCount: [
        { title: '检测设备数量', icon: 'icon-exceptionlibrary' },
        { title: '单设备检测图像数量', icon: 'icon-exceptionlibrary' },
        { title: '检测图像总数量', icon: 'icon-exceptionlibrary' },
      ], // 统计展示
      selectKey: '', // 机构树
      modelTag: 0, // 聚档模式,图像模式
      infoObj: {}, // 统计接口返回
      searchData: {}, // 查询参数
      minusHeight: 488, // 表格
      columns: [], // 表头
      // 卡片展示参数，
      cardInfo: [],
      // 卡片下拉框参数
      cardSearchList: [],
      // 卡片接口
      loadDataCard: (parameter) => {
        if (!this.taskObj.batchId) {
          console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        } else {
          return this.$http
            .post(
              inspectionrecord.pageListImageCar,
              Object.assign(
                parameter,
                {
                  batchId: this.taskObj.batchId,
                  indexId: this.taskObj.indexId,
                  // orgCode: this.taskObj.regionCode,
                  taskIndexId: this.taskObj.taskIndexId,
                  // batchId: 28,
                  // indexId: 3010,
                  // orgCode: 370112280000,
                },
                this.searchData,
              ),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
      // 设备模式接口
      loadDataList: (parameter) => {
        if (!this.taskObj.batchId) {
          console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        } else {
          return this.$http
            .post(
              inspectionrecord.vehicleForDeviceModel,
              Object.assign(
                parameter,
                {
                  batchId: this.taskObj.batchId,
                  evaluationIndexId: this.taskObj.indexId,
                  // orgCode: this.taskObj.regionCode,
                  taskIndexId: this.taskObj.taskIndexId,
                  // batchId: 28,
                  // evaluationIndexId: 3010,
                  // orgCode: 370112280000,
                },
                this.searchData,
              ),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
      currentIcon: 'icon-exceptionlibrary',
    };
  },
  computed: {
    ...mapGetters({
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
      orgTreeData: 'common/getOrganizationList',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
      },
    },
  },
  filter: {},
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    if (this.colorType.length == 0) {
      this.getcolorType();
    }
    if (this.vehicleBandType.length == 0) {
      this.getvehicleBandType();
    }
    if (this.vehicleClassType.length == 0) {
      this.getvehicleClassType();
    }
    if (this.plateClassType.length == 0) {
      this.getplateClassType();
    }
  },
  mounted() {
    this.info();
    this.abnormalCountMap();
  },
  methods: {
    ...mapActions({
      getcolorType: 'algorithm/getcolorType',
      getvehicleBandType: 'algorithm/getvehicleBandType',
      getvehicleClassType: 'algorithm/getvehicleClassType',
      getplateClassType: 'algorithm/getplateClassType',
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 初始话
    async info() {
      this.searchData = {};
      this.getRegioncode();
      this.$refs.search.reashfalf();
      if (!this.taskObj.batchId) {
        return;
      }
      // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      // this.selectKey = orgCode || ''
      // if (!orgCode) return this.$Message.error('您没有此行政区划权限')
      // this.treeData =
      //   this.taskObj.orgCodeList && this.taskObj.orgCodeList.length > 0
      //     ? this.$util.common.arrayToJson(
      //         JSON.parse(JSON.stringify(this.taskObj.orgCodeList)),
      //         'id',
      //         'parentId'
      //       )
      //     : []
      // // 初始化参数
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      // 根据当前指标展示不同内容
      await this.infoData();
      // 统计接口
      await this.static();
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    getRegioncode() {
      this.$http
        .get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.taskObj.regionCode },
        })
        .then((res) => {
          this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(res.data.data)), 'id', 'parentId');
        });
    },
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 设备模式查看不合格图片
    bigImageUrl1(row) {
      this.$http
        .post(inspectionrecord.pageListImageCar, {
          batchId: this.taskObj.batchId,
          indexId: this.taskObj.indexId,
          orgCode: this.taskObj.regionCode,
          taskIndexId: this.taskObj.taskIndexId,
          // batchId: 28,
          // indexId: 3010,
          deviceIds: [row.deviceId],
          outcome: 2,
          // indexId: this.currentTree.indexId,
          params: { pageNumber: 1, pageSize: 100000000 },
        })
        .then((res) => {
          this.imgList = [];
          res.data.data.entities.map((val) => {
            if (val.imageUrl) {
              this.imgList.push(val.imageUrl);
            }
          });
          if (this.imgList.length <= 0) {
            this.$Message.warning('暂无不合格图片');
          } else {
            this.bigPictureShow = true;
          }
        });
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 1) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.searchData = Object.assign(this.searchData, searchData);
      if (this.modelTag === 1) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    // 统计
    static() {
      if (this.taskObj.batchId) {
        this.$http
          .post(inspectionrecord.statisticsCar, {
            batchId: this.taskObj.batchId,
            orgCode: this.taskObj.regionCode,
            schemeId: this.taskObj.taskSchemeId,
            indexId: this.currentTree.indexId,
            taskIndexId: this.taskObj.taskIndexId,
            attrErrorTotalCount: 'Y',
            clockErrTotalCount: 'Y',
            missingAttrTotalCount: 'Y',
            missingPlateColorTotalCount: 'Y',
            missingPlateTotalCount: 'Y',
            missingVehicleBrandTotalCount: 'Y',
            missingVehicleClassTotalCount: 'Y',
            missingVehicleColorTotalCount: 'Y',
            missingVehicleModelTotalCount: 'Y',
            plateColorErrTotalCount: 'Y',
            plateErrTotalCount: 'Y',
            testTotalCount: 'Y',
            unableDetectTotalCount: 'Y',
            uploadTimeOutTotalCount: 'Y',
            urlNotUseTotalCount: 'Y',
            vehicleBrandErrTotalCount: 'Y',
            vehicleClassErrTotalCount: 'Y',
          })
          .then((res) => {
            this.infoObj = res.data.data;
            this.abnormalCountMap();
          });
      }
    },
    // 树切换
    // async selectOrgCode(data) {
    //   this.selectKey = data.orgCode
    //   this.static()
    //   this.searchData = {}
    //   if (this.modelTag === 1) {
    //     if (this.$refs.infoList) {
    //       this.$refs.infoList.info(true)
    //     }
    //   } else {
    //     if (this.$refs.infoCard) {
    //       this.$refs.infoCard.info(true)
    //     }
    //   }
    // },
    // 统计参数填充
    abnormalCountMap() {
      const abnormalMap = {
        检测设备数量: 'testDeviceCount',
        单设备检测图像数量: 'oneDeviceTestCount',
        检测图像总数量: 'testTotalCount',
        属性缺失图片总数量: 'missingAttrTotalCount',
        车牌号缺失图片总数量: 'missingPlateTotalCount',
        车牌颜色缺失图片总数量: 'missingPlateColorTotalCount',
        车辆类型缺失图片总数量: 'missingVehicleClassTotalCount',
        车辆品牌缺失图片总数量: 'missingVehicleBrandTotalCount',
        车身颜色缺失图片总数量: 'missingVehicleColorTotalCount',
        车辆型号缺失图片总数量: 'missingVehicleModelTotalCount',
        无法检测图片总数量: 'unableDetectTotalCount',
        属性错误图片总数量: 'missingAttrTotalCount',
        车牌号错误图片总数量: 'plateErrTotalCount',
        车牌颜色错误图片总数量: 'plateColorErrTotalCount',
        车辆类型错误图片总数量: 'vehicleClassErrTotalCount',
        车辆品牌错误图片总数量: 'vehicleBrandErrTotalCount',
        时钟错误图片总数量: 'missingAttrTotalCount',
        上传超时图片总数量: 'missingAttrTotalCount',
        URL不可用图片总数量: 'missingAttrTotalCount',
      };
      this.abnormalCountMa.map((val) => {
        val.count = this.infoObj[abnormalMap[val.title]] || 0;
        val.icon = this.currentIcon;
      });
      this.abnormalCount = this.abnormalCountMa;
    },
    // searchList 车辆表格检索条件 columns 表头  cardSearchList 卡片检索条件  cardInfo 卡片展示 abnormalCount 统计 车辆除检测概况，其余页面一样，通过动态数据来区分展示
    // 判断当前指标改变参数  卡片样式不同分为两个组件处理
    infoData() {
      switch (this.currentTree.id) {
        case 402:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '属性缺失图片总数量' },
            { title: '车牌号缺失图片总数量' },
          ];
          this.currentIcon = 'icon-putongkakoushuxingjiance';
          this.searchList = [
            { dataKey: 'missingPlateCount', dataValue: '车牌号缺失图片数量' },
            { dataKey: 'missingPlateColorCount', dataValue: '车牌颜色缺失图片数量' },
          ];
          // 4__属性缺失 2__属性错误 查询参数在检索组件内部单独处理
          this.cardSearchList = [
            { dataKey: '1', dataValue: '属性完整' },
            { dataKey: '4__plate_no', dataValue: '车牌号缺失' },
            { dataKey: '4__plate_color', dataValue: '车牌颜色缺失' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '车牌号：', value: 'plateNo' },
            { name: '车牌颜色：', value: 'plateColor', algorithm: 'colorType' },
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '抓拍地点：', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '车牌号缺失图片数量', key: 'missingPlateCount', width: 180 },
            { title: '车牌颜色缺失图片数量', key: 'missingPlateColorCount', width: 180 },
            { title: '属性完整图片数量', key: 'attrCompleteCount', width: 180 },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx' }, // 点位类型
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
        case 403:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '属性缺失图片总数量' },
            { title: '车牌号缺失图片总数量' },
            { title: '车牌颜色缺失图片总数量' },
            { title: '车辆类型缺失图片总数量' },
            { title: '车辆品牌缺失图片总数量' },
            { title: '车身颜色缺失图片总数量' },
            { title: '车辆型号缺失图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-zhongdiankakoushuxingjiance';
          this.searchList = [
            { dataKey: 'missingPlateCount', dataValue: '车牌号缺失图片数量' },
            { dataKey: 'missingPlateColorCount', dataValue: '车牌颜色缺失图片数量' },
            { dataKey: 'missingVehicleClassCount', dataValue: '车辆类型缺失图片数量' },
            { dataKey: 'missingVehicleBrandCount', dataValue: '车辆品牌缺失图片数量' },
            { dataKey: 'missingVehicleColorCount', dataValue: '车身颜色缺失图片数量' },
            { dataKey: 'attrCompleteCount', dataValue: '车辆型号缺失图片数量' },
          ];
          this.cardSearchList = [
            { dataKey: '1', dataValue: '属性完整' },
            { dataKey: '4__plate_no', dataValue: '车牌号缺失' },
            { dataKey: '4__plate_color', dataValue: '车牌颜色缺失' },
            { dataKey: '4__vehicle_class', dataValue: '车辆类型缺失' },
            { dataKey: '4__vehicle_brand', dataValue: '车辆品牌缺失' },
            { dataKey: '4__vehicle_color', dataValue: '车身颜色缺失' },
            { dataKey: '4__vehicle_model', dataValue: '车辆型号缺失' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '车牌号：', value: 'plateNo' },
            { name: '车牌颜色：', value: 'plateColor', algorithm: 'colorType' },
            { name: '车辆类型：', value: 'vehicleClass', algorithm: 'vehicleClassType' },
            { name: '车辆品牌：', value: 'vehicleBrand', algorithm: 'vehicleBandType' },
            { name: '车身颜色：', value: 'vehicleColor', algorithm: 'colorType' },
            { name: '车辆型号：', value: 'vehicleModel' },
            { type: 'image', value: 'shotTime' },
            { type: 'image', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName', width: 180 },
            { title: '组织机构', key: 'deviceOrgCodeName', width: 180 },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '车牌号缺失图片数量', key: 'missingPlateCount', width: 180 },
            { title: '车牌颜色缺失图片数量', key: 'missingPlateColorCount', width: 190 },
            { title: '车辆类型缺失图片数量', key: 'missingVehicleClassCount', width: 190 },
            { title: '车辆品牌缺失图片数量', key: 'missingVehicleBrandCount', width: 190 },
            { title: '车身颜色缺失图片数量', key: 'missingVehicleColorCount', width: 190 },
            { title: '车辆型号缺失图片数量', key: 'missingVehicleModelCount', width: 190 },
            { title: '属性完整图片数量', key: 'attrCompleteCount', width: 160 },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 404:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '属性错误图片总数量' },
            { title: '车牌号错误图片总数量' },
            { title: '车牌颜色错误图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-putongkakouzhuyaoshuxingjiance';
          this.searchList = [
            { dataKey: 'plateErrCount', dataValue: '车牌号错误图片数量' },
            { dataKey: 'plateColorErrCount', dataValue: '车牌颜色错误图片数量' },
          ];
          this.cardSearchList = [
            { dataKey: '1', dataValue: '属性正确' },
            { dataKey: '2__plate_no', dataValue: '车牌号错误' },
            { dataKey: '2__plate_color', dataValue: '车牌颜色错误' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '车牌号：', value: 'plateNo', than: 'votePlateNo' },
            {
              name: '车牌颜色：',
              value: 'plateColor',
              than: 'votePlateColor',
              algorithm: 'colorType',
            },
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '抓拍地点：', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '车牌号错误图片数量', key: 'plateErrCount', width: 180 },
            { title: '车牌颜色错误图片数量', key: 'plateColorErrCount', width: 180 },
            { title: '属性正确图片数量', key: 'attrRightCount', width: 180 },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 405:
          this.currentIcon = 'icon-zhongdiankakouzhuyaoshuxingjiance';
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '属性错误图片总数量' },
            { title: '车牌号错误图片总数量' },
            { title: '车牌颜色错误图片总数量' },
            { title: '无法检测图片总数量' },
          ];

          this.searchList = [
            { dataKey: 'plateErrCount', dataValue: '车牌号错误图片数量' },
            { dataKey: 'plateColorErrCount', dataValue: '车牌颜色错误图片数量' },
          ];
          this.cardSearchList = [
            { dataKey: '1', dataValue: '属性正确' },
            { dataKey: '2__plate_no', dataValue: '车牌号错误' },
            { dataKey: '2__plate_color', dataValue: '车牌颜色错误' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '车牌号：', value: 'plateNo', than: 'votePlateNo' },
            {
              name: '车牌颜色：',
              value: 'plateColor',
              than: 'votePlateColor',
              algorithm: 'colorType',
            },
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '抓拍地点：', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '车牌号错误图片数量', key: 'plateErrCount', width: 180 },
            { title: '车牌颜色错误图片数量', key: 'plateColorErrCount', width: 180 },
            { title: '属性正确图片数量', key: 'attrRightCount', width: 180 },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 406:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '属性错误图片总数量' },
            { title: '车辆类型错误图片总数量' },
            { title: '车辆品牌错误图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-zhongdiankakouleixingshuxingjiance';
          this.searchList = [
            { dataKey: 'vehicleClassErrCount', dataValue: '车辆类型错误图片数量' },
            { dataKey: 'vehicleBrandErrCount', dataValue: '车辆品牌错误图片数量' },
          ];
          this.cardSearchList = [
            { dataKey: '1', dataValue: '属性正确' },
            { dataKey: '2__vehicle_class', dataValue: '车辆类型错误' },
            { dataKey: '2__vehicle_brand', dataValue: '车辆品牌错误' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            {
              name: '车辆类型：',
              value: 'vehicleClass',
              than: 'voteVehicleClass',
              algorithm: 'vehicleClassType',
            },
            {
              name: '车辆品牌：',
              value: 'vehicleBrand',
              than: 'voteVehicleBrand',
              algorithm: 'vehicleBandType',
            },
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '抓拍地点：', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '车辆类型错误图片数量', key: 'vehicleClassErrCount', width: 180 },
            { title: '车辆品牌错误图片数量', key: 'vehicleBrandErrCount', width: 180 },
            { title: '属性正确图片数量', key: 'attrRightCount', width: 180 },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 407:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '时钟错误图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-cheliangkakoushebeishizhongjiance';
          this.width = 130;
          this.lable = '时钟错误图片数量';
          this.cardSearchList = [
            { dataKey: '1', dataValue: '时钟准确' },
            { dataKey: '2', dataValue: '时钟错误' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '接收时间：', value: 'firstIntoViewTime' },
            { type: 'image', value: 'timeAccuracyDesc' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '时钟错误图片数量', key: 'clockErrCount' },
            { title: '时钟正确图片数量', key: 'clockSucCount' },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 408:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '上传超时图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-a-putongkakoushangchuanjishixingjiance2';
          this.width = 155;
          this.lable = '上传超时人脸图片数量';
          this.cardSearchList = [
            { dataKey: '1', dataValue: '上传及时' },
            { dataKey: '2', dataValue: '上传超时' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '接收时间：', value: 'firstIntoViewTime' },
            { name: '超时时间：', value: 'delayTimeTrans' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '上传超时图片数量', key: 'uploadTimeOutCount' },
            { title: '上传及时图片数量', key: 'uploadTimelyCount' },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 409:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: '上传超时图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-a-zhongdiankakoushangchuanjishixingjiance2';
          this.width = 155;
          this.lable = '上传超时人脸图片数量';
          this.cardSearchList = [
            { dataKey: '1', dataValue: '上传及时' },
            { dataKey: '2', dataValue: '上传超时' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '抓拍时间：', value: 'shotTime' },
            { name: '接收时间：', value: 'firstIntoViewTime' },
            { name: '超时时间：', value: 'delayTimeTrans' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: '上传超时图片数量', key: 'uploadTimeOutCount' },
            { title: '上传及时图片数量', key: 'uploadTimelyCount' },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 410:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: 'URL不可用图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-putongkakouurljiance';
          this.width = 165;
          this.lable = 'URL不可用人脸图片数量';
          this.cardSearchList = [
            { dataKey: '1', dataValue: 'URL可用' },
            { dataKey: '2', dataValue: 'URL不可用' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { type: 'image', value: 'shotTime' },
            { type: 'image', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: 'URL可用图片数量', key: 'urlCanUseCount' },
            { title: 'URL不可用图片数量', key: 'urlNotUseCount' },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
        case 411:
          this.abnormalCountMa = [
            { title: '检测设备数量' },
            { title: '单设备检测图像数量' },
            { title: '检测图像总数量' },
            { title: 'URL不可用图片总数量' },
            { title: '无法检测图片总数量' },
          ];
          this.currentIcon = 'icon-zhongdiankakouurljiance';
          this.width = 165;
          this.lable = 'URL不可用人脸图片数量';
          this.cardSearchList = [
            { dataKey: '1', dataValue: 'URL可用' },
            { dataKey: '2', dataValue: 'URL不可用' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { type: 'image', value: 'shotTime' },
            { type: 'image', value: 'address' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
            { title: '组织机构', key: 'deviceOrgCodeName' },
            { title: '检测图片数量', key: 'testPictureCount', width: 120 },
            { title: 'URL可用图片数量', key: 'urlCanUseCount' },
            { title: 'URL不可用图片数量', key: 'urlNotUseCount' },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 },
            {
              title: '操作',
              slot: 'action',
              fixed: 'right',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
            },
          ];
          break;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    // position: absolute;
    // right: 20px;
    // top: 134px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
    padding-bottom: 8px;
  }
  .card1 {
    width: calc(calc(100% - 72px) / 7);
    margin: 0 5px 10px;
  }
  .card2 {
    width: calc(calc(100% - 40px) / 3);
    margin: 0 5px 10px;
  }
}
</style>
