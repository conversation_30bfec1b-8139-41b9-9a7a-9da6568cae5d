<template>
  <div class="governanceautomatic-item auto-fill">
    <div class="search-module mb-sm">
      <ui-label class="inline mr-lg" label="任务名称">
        <Input v-model="searchData.taskName" class="width-md" placeholder="请输入任务名称"></Input>
      </ui-label>
      <ui-label v-for="(item, index) in dataSourceDict" :key="index" class="inline mr-lg" :label="item.label">
        <Select class="input-width" :placeholder="'请选择' + item.label" clearable v-model="searchData[item.key]">
          <Option
            v-for="(dictItem, dictIndex) in dicts[item.data]"
            :key="item.key + dictIndex"
            :value="dictItem.code"
            >{{ dictItem.label }}</Option
          >
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </div>
      <Button type="primary" class="ml-sm fr" @click="newTask">
        <i class="icon-font icon-tianjia f-12"></i>
        <span class="vt-middle ml-sm">新增任务</span>
      </Button>
    </div>
    <div class="table-module auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #taskStatusText="{ row }">
          <span class="check-status" :style="{ 'background-color': bgColor[row.taskStatus - 1] }">{{
            row.taskStatusText
          }}</span>
        </template>
        <template #action="{ row }">
          <!-- 任务进行中不可编辑 -->
          <ui-btn-tip
            class="operatbtn mr-sm"
            icon="icon-bianji2"
            content="编辑"
            @click.native="handleEdit(row)"
          ></ui-btn-tip>
          <!--  任务进行中不可删除 -->
          <ui-btn-tip
            class="operatbtn mr-sm"
            icon="icon-shanchu3"
            content="删除"
            @click.native="handleDelete(row)"
          ></ui-btn-tip>
          <!-- 未开始、进行中的可暂停，已暂停的可启动,  已完成、任务异常的可重新执行 -->
          <ui-btn-tip
            class="operatbtn mr-sm"
            :icon="
              row.taskStatus === '1' || row.taskStatus === '2'
                ? 'icon-zanting-ivdg'
                : row.taskStatus === '4'
                ? 'icon-qidong'
                : 'icon-zhongxinzhihang'
            "
            :content="
              row.taskStatus === '1' || row.taskStatus === '2' ? '暂停' : row.taskStatus === '4' ? '启动' : '重新执行'
            "
            @click.native="handleTask(row)"
          ></ui-btn-tip>
          <!-- 实时的任务以及已完成的任务可查看结果 -->
          <ui-btn-tip
            class="operatbtn"
            v-if="!row.governanceTime || row.taskStatus === '3'"
            icon="icon-chakanxiangqing"
            content="查看结果"
            @click.native="viewResult(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
    </div>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page>
    <normal-result v-model="normalResultShow" :active-row="activeRow"></normal-result>
    <newtask
      ref="Newtask"
      :is-edit="isEdit"
      :source-types="dicts.sourceTypeDict"
      :governance-content="governanceContent"
      :source-type="sourceType"
      @tableRender="search"
    ></newtask>
  </div>
</template>

<script>
import governanceautomatic from '@/config/api/governanceautomatic';
export default {
  props: {
    governanceContent: {
      type: String,
    },
    sourceType: {},
    dictsData: {},
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    Newtask: require('./dialog/newtask.vue').default,
    NormalResult: require('./result/normal-result/index.vue').default,
  },
  data() {
    return {
      normalResultShow: false,
      loading: false,
      searchData: {
        taskName: '',
        taskStatus: '',
        sourceType: '',
        governanceContent: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: '任务名称',
          key: 'taskName',
          align: 'left',
          tooltip: true,
          minWidth: 200,
        },
        { title: '资源类型', key: 'sourceTypeText', align: 'left', width: 160 },
        {
          title: '治理内容',
          key: 'governanceContentText',
          align: 'left',
          width: 150,
        },
        { title: '治理时间', key: 'time', align: 'left', width: 170 },
        {
          title: '任务状态',
          slot: 'taskStatusText',
          align: 'left',
          width: 120,
        },
        { title: '任务进度', key: 'process', align: 'left', width: 130 },
        { title: '无法治理', key: 'failHandleNum', align: 'left', width: 130 },
        { title: '耗时', key: 'spendTime', align: 'left', width: 140 },
        { title: '创建人', key: 'creator', align: 'left', width: 120 },
        { title: '创建时间', key: 'createTime', align: 'left', width: 160 },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
          width: 133,
        },
      ],
      tableData: [],
      dataSourceDict: [
        { label: '任务状态', key: 'taskStatus', data: 'taskStatusDict' },
        // { label: '资源类型', key: 'sourceType', data: 'sourceTypeDict' },
        // { label: "所属网络", key: "sourceType", data: "sourceTypeDict" },
      ],
      dicts: {
        taskStatusDict: [],
        sourceTypeDict: [],
      },
      isEdit: false,
      activeRow: {},
      bgColor: [
        'var(--color-offline)',
        '#D9B916',
        'var(--color-success)',
        'var(--color-warning)',
        'var(--color-failed)',
      ],
    };
  },
  created() {},
  watch: {
    governanceContent: {
      handler(val) {
        if (!val) return;
        this.setSearchData();
      },
      deep: true,
      immediate: true,
    },
    dictsData: {
      handler(obj) {
        if (!obj) {
          this.init();
        } else {
          this.dicts = this.$util.common.deepCopy(obj);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async init() {
      this.dicts.taskStatusDict = await this.getDictData('getTaskStatusList'); // 获取任务状态
      this.dicts.sourceTypeDict = await this.getDictData('getSourceTypeList'); // 获取资源类型
    },
    setSearchData() {
      let { pageSize } = this.searchData;
      this.searchData = {
        taskName: '',
        taskStatus: '',
        sourceType: '',
        governanceContent: this.governanceContent,
        pageNumber: 1,
        pageSize: pageSize,
      };
      this.search();
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    clear() {
      this.setSearchData();
    },
    // 新增任务
    newTask() {
      this.isEdit = false;
      this.editData = {};
      this.$refs.Newtask.init();
    },
    // 查看结果
    viewResult(row) {
      this.activeRow = row;
      this.normalResultShow = true;
    },
    // 获取搜索条件字典
    async getDictData(type) {
      try {
        let res = await this.$http.get(governanceautomatic[type]);
        return res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    // 获取任务列表
    async getTableList() {
      try {
        this.loading = true;
        let params = this.searchData;
        let res = await this.$http.post(governanceautomatic.pageList, params);
        const datas = res.data.data;
        this.tableData = datas.entities.map((item) => {
          item.process = item.handleNum + '/' + item.taskNum;
          item.time = item.governanceTime ? item.governanceTime : '实时';
          item.spendTime = item.spendTime ? this.msToTime(item.spendTime) : '0';
          return item;
        });
        this.pageData.totalCount = datas.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },

    // 删除
    async handleDelete(row) {
      this.$UiConfirm({
        content: `您要删除数据：${row.taskName} 这项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteTask(row.id);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteTask(id) {
      try {
        await this.$http.get(governanceautomatic.delete, {
          params: { taskId: id },
        });
        this.$Message.success('删除成功！');
        this.search();
      } catch (error) {
        console.log(error);
      }
    },
    // 4 启动 / 1、2 暂停 / 3、5 重新执行
    async handleTask(row) {
      try {
        let type = '';
        let params = {
          taskId: row.id,
        };
        let successMsg = '';
        if (row.taskStatus == '1' || row.taskStatus == '2') {
          type = 'pause';
          successMsg = '暂停';
        } else if (row.taskStatus === '4') {
          type = 'start';
          params.isRestart = '2'; // 重新执行传1 启动传2
          successMsg = '启动';
        } else {
          type = 'start';
          params.isRestart = '1'; // 重新执行传1 启动传2
          successMsg = '重新执行';
        }
        await this.$http.get(governanceautomatic[type], {
          params,
        });
        this.getTableList();
        this.$Message.success(successMsg + '成功！');
      } catch (error) {
        console.log(error);
      }
    },
    msToTime(duration) {
      if (duration < 1000) {
        return '小于1秒';
      }
      let seconds = Math.floor((duration / 1000) % 60);
      let minutes = Math.floor((duration / (1000 * 60)) % 60);
      let hours = Math.floor((duration / (1000 * 60 * 60)) % 24);
      seconds = seconds ? seconds + '秒' : '';
      minutes = minutes ? minutes + '分' : '';
      hours = hours ? hours + '时' : '';
      return hours + minutes + seconds;
    },
    handleEdit(row) {
      this.isEdit = true;
      this.$refs.Newtask.init(row.id);
    },
    handlePage(val) {
      this.searchData.pageNumber = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
  },
};
</script>

<style lang="less" scoped>
.governanceautomatic-item {
  width: 100%;
  height: 100%;
  // padding: 0 20px;
}
.search-module,
.table-module {
  padding: 0 20px;
}
.input-width {
  width: 230px;
}
</style>
