<!--
    * @FileDescription: 人员档案 - 视频档案
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="container">
        <div class="manufacturer">
            <RadioGroup v-model="maunRecord" type="button" @on-change="handleMaunChange">
                <Radio label="HK">海康聚类档案</Radio>
                <Radio label="GLST">格灵聚类档案</Radio>
            </RadioGroup>
        </div>
        <div class="person">
            <!-- 查询 -->
            <Search @searchForm="searchForm" :searchText="'高级检索'" />
            <div class="card-content">
                <div v-for="(item, index) in list" :key="index" :class="item.type === 'people' ? 'people-card' : 'video-card'" class="card-item">
                    <UiListCard 
                        type="record"
                        :collectIcon="false"
                        :isPercentage="false"
                        :showBar="false" 
                        :data="item" 
                        :index="index" 
                        @archivesDetailHandle="archivesDetailHandle(item)" 
                        @on-change.stop="changeCardHandle(item, index)" 
                        @on-archive='archivesDetailHandle'/>
                </div>
                <ui-empty v-if="list.length === 0 && !loading"></ui-empty>
                <ui-loading v-if="loading"></ui-loading>
            </div>
            <!-- 分页 -->
            <ui-page :current="pageInfo.pageNumber" countTotal :showElevator="false" :total="total" :page-size="pageInfo.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
        </div>
        <details-modal v-if="detailsShow" ref="detailsModal" @close="detailsShow = false"></details-modal>  
    </div>
</template>
<script>
import Search from './components/search';
import UiListCard from '@/components/ui-list-card';
import detailsModal from './components/details-modal';
import { originalProfileQueryList } from '@/api/person-archives-bb';
import { mapGetters, mapActions } from 'vuex';
export default {
    components: {
        Search,
        UiListCard,
        detailsModal
    },
    data() {
        return {
            list: [],
            pageInfo:{
                pageNumber:1,
                pageSize: 20
            },
            total: 0,
            pageForm: {
                dataType: 1,
            },
            loading: false,
            detailsShow: false,
            maunRecord: 'HK'
        }
    },
    created() {
        this.getList();
        this.getDictData();
    },
    methods:{
        ...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
        searchForm() {

        },
        getList() {
            this.loading = true;
            let params = {
                algorithmManufacturer: this.maunRecord,
                keyword: "",
                ...this.pageInfo,
                startTime: "2024-03-05 15:26:02",
                timePoint: "newWeek",
                timeType: 1
            };
            originalProfileQueryList(params)
            .then(res => {
                this.list = res.data.list.map(item => {
                    if( item.idNumber ){
                        if(!item.identityPhoto || !item.identityPhoto.length){
                           item.identityPhoto = [] ;
                        };
                        item.photos = item.identityPhoto;

                    } else {
                        if(!item.identityCoverPhoto || !item.identityCoverPhoto.length) {
                            item.identityCoverPhoto = [];
                        }
                        item.photos = item.identityCoverPhoto;
                    }
                    return item
                })
                this.total = res.data.total;
            })  
            .finally(() => {
                this.loading = false;
            })
        },
        handleMaunChange() {
            this.getList();
            this.list = [];
        },
        // 改变页码
        pageChange(pageNumber) {
            this.pageInfo.pageNumber = pageNumber
            this.getList()
        },
        // 改变分页个数
        pageSizeChange(size) {
            this.pageInfo.pageSize = size;
            this.pageInfo.pageNumber = 1;
            this.getList()
        },
        archivesDetailHandle() {
            this.detailsShow = true;
            this.$nextTick(() => {
                this.$refs.detailsModal.show();
            })
        },
        changeCardHandle() {

        },
        onSearchImage() {

        },
        onControl() {

        },
    }
}
</script>    
<style lang="less" scoped>
.container{
    display: flex;
    flex-direction: column;
}
.manufacturer{
    margin-bottom: 10px;
}
.person{
    flex: 1;
    display: flex;
    flex-direction: column;
    height: calc(~'100% - 42px');
    width: 100%; 
    .card-content{
        display:flex;
        flex-wrap: wrap;
        overflow: auto;
        flex: 1;
        margin: 0 -5px;
        align-content: flex-start;
        position: relative;
        .card-item {
            width: 20%;
            padding: 0 5px;
            box-sizing: border-box;
            margin-bottom: 10px;
            transform-style: preserve-3d;
            transition: transform 0.6s;
            .list-card{
                width: 100%;
                backface-visibility: hidden;
            }
        }
    }
}

</style>