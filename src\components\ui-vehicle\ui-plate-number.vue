<template>
  <!-- 凡是使用了v-html的地方，均是为了适配【全景智搜】高亮的展示效果 -->
  <div class="plateNum" :class="size">
    <div
      class="super-box"
      v-if="size == 'super'"
      :style="{ background: getColorByType(color).color }"
    >
      <div :class="'plate-' + getColorByType(color).class">
        <span
          class="license-plate"
          :style="getColorByType(color).style"
          v-clipboard="plateNo"
          v-html="plateNo || '未识别'"
        ></span>
      </div>
    </div>
    <!-- 新能源 -->
    <div class="new-energy" v-else>
      <span
        v-if="color != 15"
        class="license-plate-small"
        :class="getColorByType(color).class"
        :style="getColorByType(color).style"
        v-clipboard="plateNo"
        v-html="plateNo || '未识别'"
      ></span>
      <!-- 新能源公交车，车牌两种颜色 -->
      <div class="new-energy-num" v-else v-clipboard="plateNo">
        <span
          class="license-plate-left"
          :class="getColorByType(color).class"
          :style="getColorByType(color).style1"
          v-html="fmtPlateNo()[0]"
        >
        </span>
        <span
          class="license-plate-right"
          :class="getColorByType(color).class"
          :style="getColorByType(color).style2"
          v-html="fmtPlateNo()[1]"
        ></span>
      </div>
    </div>
    <!-- <span v-else class="license-plate-small" :class="getColorByType(color).class" :style="getColorByType(color).style">{{ plateNo || '未识别' }}</span> -->
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    color: {
      type: String,
      default: "H",
    },
    plateNo: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },

  methods: {
    // getColorType(color) {
    //     return this.colorType[color] || this.colorType["5"]
    // },
    getColorByType: (color) => {
      switch (color) {
        case "2":
        case "A":
          return {
            name: "白",
            color: "#ffffff",
            class: "white",
            style: {
              background: "#ffffff",
              // border: '1px solid #131313',
              color: "#000",
            },
          };
        case "6":
        case "C":
          return {
            name: "黄",
            color: "#FDEE38",
            class: "yellow",
            style: {
              background: "#FDEE38",
              // border: '1px solid #131313',
              color: "#000",
            },
          };
        case "9":
        case "G":
          return {
            name: "绿",
            color: "#67D28D",
            class: "green",
            style: {
              background: "#67D28D",
              // border: '1px solid #131313',
              color: "#000",
            },
          };
        case "5":
        case "H":
          return {
            name: "蓝",
            color: "#2379F9",
            class: "blue",
            style: {
              background: "#2379F9",
              // border: '1px solid #ffffff',
              color: "#ffffff",
            },
          };
        case "1":
        case "J":
          return {
            name: "黑",
            color: "#000",
            class: "black",
            style: {
              background: "#000",
              // border: '1px solid #ffffff',
              color: "#ffffff",
            },
          };
        case "15":
          return {
            name: "黄绿",
            color: "#000",
            class1: "yellow",
            class2: "green",
            style1: {
              background: "#FDEE38",
              // border: '1px solid #ffffff',
              color: "#000",
            },
            style2: {
              background: "#67D28D",
              // border: '1px solid #ffffff',
              color: "#000",
            },
          };
        case "16":
          return {
            name: "渐变绿",
            color: "#000",
            class: "green",
            style: {
              background: "linear-gradient(to bottom, #fff, #67D28D)",
              // border: '1px solid #ffffff',
              color: "#000",
            },
          };
        case "99":
        case "Z":
          return {
            name: "其他",
            color: "#D9D9D9",
            class: "other",
            style: {
              background: "#D9D9D9",
              // border: '1px solid #ffffff',
              color: "#ffffff",
            },
          };
        default:
          return {
            name: "其他",
            color: "#D9D9D9",
            class: "other",
            style: {
              background: "#D9D9D9",
              // border: '1px solid #ffffff',
              color: "#131313",
            },
          };
      }
    },
    fmtPlateNo() {
      // 没有车牌号
      if (!this.plateNo) {
        return ["未识别", ""];
      }
      // 当车牌号 <= 8位字符，说明没有样式内容
      if (this.plateNo.length <= 8) {
        return [this.plateNo.slice(0, 2), this.plateNo.slice(2)];
      }
      let _a = ["", ""];
      let reg = /<[^>]*>/g;
      let arr = this.plateNo.split(reg); // 此方法获取到的index为奇数的元素为有样式的部分
      // 前2位的情况分为全黑，全红，左红右黑，右黑左红
      if (arr[0].length >= 2) {
        // 表示前2位没样式，全黑
        _a[0] = arr[0].slice(0, 2);
        _a[1] = arr[0].slice(2);
        arr.forEach((item, index) => {
          if (index > 0) {
            _a[1] +=
              index % 2 === 1
                ? `<span style="color: red">${item}</span>`
                : item;
          }
        });
        return _a;
      } else {
        // 3中情况，全红，即arr[0].length = 0, arr[1].length >= 2
        // 左红右黑， arr[0].length = 0, arr[1].length = 1
        // 左黑右红， arr[0].length = 1, arr[1].length >= 1
        if (arr[0].length === 0) {
          if (arr[1].length >= 2) {
            // 全红
            _a[0] = `<span style="color: red">${arr[1].slice(0, 2)}</span>`;
            _a[1] = `<span style="color: red">${arr[1].slice(2)}</span>`;
            arr.forEach((item, index) => {
              if (index > 1) {
                _a[1] +=
                  index % 2 === 1
                    ? `<span style="color: red">${item}</span>`
                    : item;
              }
            });
          } else {
            // 左红右黑
            // arr[1]的长度至少为1
            _a[0] = `<span style="color: red">${arr[1]}</span>${arr[2].slice(
              0,
              1
            )}`;
            _a[1] = arr[2].slice(1);
            arr.forEach((item, index) => {
              if (index > 2) {
                _a[1] +=
                  index % 2 === 1
                    ? `<span style="color: red">${item}</span>`
                    : item;
              }
            });
          }
        } else {
          // 左黑右红
          _a[0] = `${arr[0]}<span style="color: red">${arr[1].slice(
            0,
            1
          )}</span>`;
          _a[1] = `<span style="color: red">${arr[1].slice(1)}</span>`;
          arr.forEach((item, index) => {
            if (index > 1) {
              _a[1] +=
                index % 2 === 1
                  ? `<span style="color: red">${item}</span>`
                  : item;
            }
          });
        }
      }
      return _a;
    },
  },
};
</script>

<style lang='less' scoped>
.plate-white,
.white::after {
  border: 1px solid #131313;
}
.plate-yellow,
.yellow::after {
  border: 1px solid #131313;
}
.plate-green,
.green::after {
  border: 1px solid #131313;
}
.plate-blue,
.blue::after {
  border: 1px solid #ffffff;
}
.plate-black,
.black::after {
  border: 1px solid #ffffff;
}
.plate-other,
.other::after {
  border: 1px solid #131313;
}

.plateNum {
  // bottom: 0;
  // width: 100%;
  display: flex;
  justify-content: center;
}
.mini {
  // width: 88px;
  // height: 22px;
  padding: 2px;
  span {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.small {
}
.medium {
  height: 40px !important;
  bottom: 0 !important;
  span {
    font-size: 16px;
    padding: 8px 12px;
  }
}
.super-box {
  padding: 5px;
  border-radius: 3px;
}
.super {
  padding: 5px;
  span {
    font-size: 30px;
    padding: 0 18px;
    height: 100%;
    line-height: inherit;
  }
}
.new-energy {
  .new-energy-num {
    display: flex;
  }
  // .yellow{
  //     color: #FDEE38;
  // }
  // .green{
  //     color: #67D28D;
  // }
  .license-plate-left {
    display: inline-block;
    background: #2379f9;
    /* border-radius: 0.01042rem; */
    font-weight: bold;
    font-size: 14px;
    line-height: 14px;
    font-family: "MicrosoftYaHei-Bold, MicrosoftYaHei";
    padding: 4px 0px 4px 6px;
    color: #fff;
    position: relative;
    &::after {
      content: "";
      border: 1px solid #131313;
      position: absolute;
      width: calc(~"100% - 4px");
      height: calc(~"100% - 4px");
      top: 2px;
      left: 5px;
      border-right: none;
    }
  }
  .license-plate-right {
    display: inline-block;
    background: #2379f9;
    /* border-radius: 0.01042rem; */
    font-weight: bold;
    font-size: 14px;
    line-height: 14px;
    font-family: "MicrosoftYaHei-Bold, MicrosoftYaHei";
    padding: 4px 6px 4px 0;
    color: #fff;
    position: relative;
    &::after {
      content: "";
      border: 1px solid #131313;
      position: absolute;
      width: calc(~"100% - 4px");
      height: calc(~"100% - 4px");
      top: 2px;
      left: 0px;
      border-left: none;
    }
  }
}
</style>
