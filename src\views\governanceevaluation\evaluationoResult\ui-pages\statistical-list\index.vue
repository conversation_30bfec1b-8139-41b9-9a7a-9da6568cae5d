<template>
  <div class="statistical-list auto-fill">
    <div class="operation-bar">
      <dynamic-condition :formItemData="formItemData" :formData="formData" @search="search" @reset="reset">
        <template #otherButton>
          <slot name="otherButton"></slot>
        </template>
      </dynamic-condition>
      <div class="operation-bar-right">
        <slot name="export"></slot>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      :row-class-name="rowClassName"
      v-on="$listeners"
    >
      <template v-for="(item, index) in tableColumns" :slot="item.slot" slot-scope="{ row }">
        <slot :name="item.slot" :row="row" :item="item" :index="index"></slot>
      </template>
    </ui-table>
    <!-- <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page> -->
  </div>
</template>

<script>
export default {
  name: 'statistical-list',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    resultData: {
      type: Array,
      default: () => {},
    },
    formData: {
      default: () => {
        return {
          qualified: '',
        };
      },
    },
    formItemData: {
      default: () => [
        {
          type: 'select',
          key: 'qualified',
          label: '检测结果',
          placeholder: '请选择检测结果',
          options: [
            {
              value: '1',
              label: '达标',
            },
            {
              value: '2',
              label: '不达标',
            },
          ],
        },
      ],
    },
    rowClassName: {
      type: Function,
      default: () => {
        return '';
      },
    },
  },
  data() {
    return {
      searchData: {
        qualified: '',
      },
      searchConditions: {},
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      paramsList: {},
    };
  },
  methods: {
    init() {
      this.paramsList = this.$route.query;
      this.copySearchDataMx(this.formData);
      this.search();
    },
    search() {
      this.loading = true;
      this.$set(this, 'searchConditions', this.formData);
      this.getTableList();
    },
    reset() {
      this.resetSearchDataMx(this.formData, this.search);
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    getTableList() {
      let params = {
        searchData: this.searchConditions,
        // pageNum: this.pageData.pageNum,
        // pageSize: this.pageData.pageSize,
      };
      this.$emit('startSearch', params);
    },
  },
  watch: {
    resultData: {
      handler(val) {
        this.tableData = val || [];
        // this.pageData.totalCount = val.total
        this.loading = false;
      },
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.statistical-list {
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-wrap: wrap;
    }
  }
}
</style>
