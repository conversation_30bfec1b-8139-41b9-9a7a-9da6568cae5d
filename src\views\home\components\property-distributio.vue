<template>
  <!-- 资产分布 -->
  <div class="property-distributio" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-zichanfenbu" v-model="activeValue" :data="tabData">
      <template #filter>
        <TypeSelect :online-visible="onlineVisible" @getName="getName"></TypeSelect>
      </template>
    </HomeTitle>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <draw-echarts
        v-if="echartList.length != 0"
        :echart-option="propertyEchart"
        :echart-style="ringStyle"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <!--  -->
      <span class="next-echart" v-if="echartList.length > comprehensiveConfig.homeNum">
        <i
          class="icon-font icon-zuojiantou1 f-12"
          @click="scrollRight('propertyChart', echartList, [], comprehensiveConfig.homeNum)"
        ></i>
      </span>
    </div>
  </div>
</template>
<style lang="less" scoped>
.property-distributio {
  position: absolute;
  top: 32.5%;
  width: 400px;
  height: 32%;
  background: rgba(0, 104, 183, 0.13);
  z-index: 2;
  .property-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .echarts-box {
    width: 100%;
    position: relative;
    height: calc(100% - 32px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
.full-screen-container {
  top: 36%;
  height: 31.5%;
  margin-left: 10px;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'property-distributio',
  mixins: [dataZoom],
  data() {
    return {
      ringStyle: {
        width: '99%',
        height: '275px',
      },
      propertyEchart: {},
      echartsLoading: false,
      echartData: [[], [], []],
      colorList: [],
      lengName: [],
      echartList: [],
      xAxis: [],
      series: [],
      activeValue: 'asset',
      tabData: [{ label: '资产分布', id: 'asset' }],
    };
  },
  props: {
    onlineVisible: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {},
  methods: {
    async getIndexDevicePropertyStatistics() {
      this.echartsLoading = true;
      try {
        let res = await this.$http.get(home.getIndexDevicePropertyStatistics);
        this.echartList = res.data.data;
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    // 重点类型 资产质量 功能类型 (上下柱形echarts)
    initRing(val) {
      this.echartData = [[], [], []];
      this.colorList = [];
      this.xAxis = [];
      this.lengName = [];
      if (val == 'important') {
        this.echartList.map((row) => {
          this.echartData[0].push(row.focusAmount); //重点设备
          this.echartData[1].push(row.commonAmount); //普通设备
        });
        this.lengName = ['重点设备', '普通设备'];
        this.colorList = ['rgba(239, 127, 59, 1)', 'rgba(17, 205, 212, 1)'];
      }

      if (val == 'assetsquality') {
        this.echartList.map((row) => {
          this.echartData[0].push(row.qualifiedAmount); //合格设备
          this.echartData[1].push(row.abnormalAmount); //异常设备
        });
        this.lengName = ['合格设备', '异常设备'];
        this.colorList = ['rgba(54, 205, 159, 1)', 'rgba(247, 122, 78, 1)'];
      }
      if (val == 'point') {
        this.echartList.map((row) => {
          this.echartData[0].push(row.oneDwAmount); //一类点
          this.echartData[1].push(row.twoDwAmount); //二三类点
          this.echartData[2].push(row.innerDwAmount); //内部监控
        });
        this.lengName = ['一类点', '二三类点', '内部监控'];
        this.colorList = ['rgba(32, 144, 254, 1)', 'rgba(53, 207, 181, 1)', 'rgba(255, 150, 122, 1)'];
      }
      let series = this.lengName.map((row, index) => {
        return {
          name: this.lengName[index],
          data: this.echartData[index],
          barWidth: this.$util.common.fontSize(8),
          barCategoryGap: '3%',
          type: 'bar',
          stack: '研究生分布情况',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[index] },
        };
      });
      let opts = {
        xAxis: this.echartList.map((row) => row.civilName),
        series: series,
        lengName: this.lengName,
      };
      this.propertyEchart = this.$util.doEcharts.propertyColumn(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], this.comprehensiveConfig.homeNum);
      });
    },
    typeRing(val) {
      this.lengName = [];
      this.echartData = [[], [], []];
      this.colorList = [];
      this.xAxis = [];
      if (val === 'function') {
        this.lengName = ['视频监控', '人脸卡口', '车辆卡口'];
        this.colorList = ['rgba(48, 209, 238, 1)', 'rgba(166, 100, 235, 1)', 'rgba(245, 175, 85, 1)'];
        this.echartList.map((row) => {
          this.echartData[0].push(row.videoSurveillanceAmount); //视频监控
          this.echartData[1].push(row.faceSwanAmount); //人脸卡口
          this.echartData[2].push(row.vehicleBayonetAmount); //车辆卡口
        });
      }
      let series = this.lengName.map((row, index) => {
        return {
          name: this.lengName[index],
          data: this.echartData[index],
          barWidth: this.$util.common.fontSize(8),
          barCategoryGap: '3%',
          type: 'bar',
          stack: '研究生分布情况',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[index] },
        };
      });
      let opts = {
        xAxis: this.echartList.map((row) => row.civilName),
        series: series,
        lengName: this.lengName,
      };
      this.propertyEchart = this.$util.doEcharts.propertColumn(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], this.comprehensiveConfig.homeNum);
      });
    },
    initBar() {
      /**
       * "onlineAmount": 0,
       " offlineAmount": 0,
       " onlineRate": 0.0,
       */
      this.echartData = [[], [], []];
      this.colorList = [];
      this.xAxis = [];
      let series = {
        label: {
          show: true,
          position: 'top',
          distance: 3,
          color: '#fff',
          rotate: 20,
          formatter: (val) => {
            return val.value.toFixed(1) + '%';
          },
        },
        name: '',
        data: this.echartList.map((row) => row.onlineRate),
        barWidth: this.$util.common.fontSize(8),
        barCategoryGap: '3%',
        type: 'bar',
        barGap: 0.3, //柱间距离
        itemStyle: { color: 'rgba(53, 207, 181, 1)' },
      };
      let opts = {
        xAxis: this.echartList.map((row) => row.civilName),
        series: series,
        data: this.echartList,
      };
      this.propertyEchart = this.$util.doEcharts.onlineAmount(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], this.comprehensiveConfig.homeNum);
      });
    },
    getName(val) {
      switch (val) {
        case 'function': // 功能类型
          this.typeRing('function');
          break;
        case 'online': // 在线状态
          this.initBar('online');
          break;
        default:
          this.initRing(val);
          break; // 点位类型 / 重点类型 / 资产质量
      }
    },
  },
  watch: {
    onlineVisible: {
      async handler(val) {
        await this.getIndexDevicePropertyStatistics();
        this.getName(val ? 'online' : 'function');
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
      getFullscreen: 'home/getFullscreen',
    }),
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
    TypeSelect: require('./type-select').default,
  },
};
</script>
