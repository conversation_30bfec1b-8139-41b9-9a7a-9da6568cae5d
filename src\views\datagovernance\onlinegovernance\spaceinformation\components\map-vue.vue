<template>
  <div id="fmap"></div>
</template>

<script>
import common from '@/config/api/common';
export default {
  name: 'MyMap',
  data() {
    return {
      fmapMain: {},
      alldevices: [],
      unqualified: ['0001', '0010', '0100', '0110', '0011', '0101', '0111'], // 不合格类型
    };
  },
  /**
   * vue开发地图气泡容器组件时实现init、getElement、open、close四个方法
   */
  methods: {
    initMap() {
      this.fmapMain.layers = {
        locationLayer: {
          name: 'locationLayer',
          desc: '定位',
          symbol: 'position',
          bubble: 'EditFormBubble',
          bubbleable: true,
          selectable: true,
          editable: true,
          bubbleSkin: 'edit-bubble-box',
          level: [8, 21],
        },
        normalLayer: {
          name: 'normalLayer',
          desc: '合格点位',
          symbol: 'map-camera-normal',
          bubble: 'EditFormBubble',
          bubbleable: true,
          bubbleSkin: 'edit-bubble-box',
          level: [15, 21],
        },
        abnormalLayer: {
          name: 'abnormalLayer',
          desc: '异常点位',

          symbol: 'map-camera-abnormal',
          bubble: 'EditFormBubble',
          bubbleable: true,
          bubbleSkin: 'edit-bubble-box',
          level: [15, 21],
        },
        clustLayer: {
          name: 'clustLayer',
          type: 'Clust',
          animate: true,
          bubbleable: false,
          selectable: false,
          editable: false,
          level: [8, 14],
        },
      };

      const bglayer = FUtil.FSM({
        standard: 'pb',
        debug: true,
        url: 'http://p{digit}.go2map.com/seamless1/0/174/{tile}.png?v=2016820',
      });
      this.fmapMain.fmap = new FMap({
        element: 'fmap',
        bglayer: bglayer,
        center: [113.3667, 23.1254, 10],
        layers: this.fmapMain.layers,
      });
      this.addPoint();
    },
    async addPoint() {
      // const res = await this.$http.post(common.getDeviceList, {
      //   sbgnlxs: [],
      // })
      let res = await this.$http.post(common.getDeviceList, { sbgnlxs: [] });
      this.alldevices = res.data.data.map((item) => {
        item.x = item.longitude;
        item.y = item.latitude;
        return item;
      });
      const normalData = [];
      const abnormalData = [];
      this.alldevices.forEach((item) => {
        if (this.unqualified.includes(item.checkStatus)) {
          abnormalData.push(item);
        } else {
          normalData.push(item);
        }
        return item.id;
      });
      this.fmapMain.fmap.addFeatures(normalData, { layer: 'normalLayer' }); // addFeatures 批量数据上图
      this.fmapMain.fmap.addFeatures(abnormalData, { layer: 'abnormalLayer' });
      this.fmapMain.fmap.addFeatures(this.alldevices, { layer: 'clustLayer' });
      // const count = 80000
      // const extent = this.fmapMain.fmap.getExtent()
      // const data = this.randomData(count, extent)
      // this.fmapMain.fmap.addFeatures(data, { layer: 'testLayer' })
      // for (var i = 0; i < 16; i++) {
      //   this.fmapMain.fmap.addFeatures(
      //     data.slice(parseInt((count / 16) * i), parseInt((count / 16) * (i + 1))),
      //     { layer: `schoolLayer${i}` }
      //   )
      // }
    },
    // randomData(count, extent) {
    //   const result = new Array()
    //   for (let i = 0; i < count; i++) {
    //     const item = {
    //       id: i,
    //       code: i,
    //       name: 'item' + i,
    //       x: this.random(parseFloat(extent[0]), parseFloat(extent[2])),
    //       y: this.random(parseFloat(extent[1]), parseFloat(extent[3])),
    //       text: '广东精一规划科技股份有限公司',
    //       alias: '精一规划',
    //     }
    //     result.push(item)
    //   }
    //   return result
    // },
    // random(min, max) {
    //   const range = max - min
    //   const rand = Math.random()
    //   return min + rand * range
    // },
  },
  mounted() {
    this.initMap();
  },
  beforeDestroy() {
    Object.keys(this.fmapMain.layers).forEach((layer) => {
      this.fmapMain.fmap.removeLayer(layer);
    });
    this.fmapMain = null;
  },
};
</script>
