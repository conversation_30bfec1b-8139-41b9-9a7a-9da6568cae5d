export default function G6DefaultConfig(container) {
  return {
    container: container,
    width: document.getElementById(container).clientWidth,
    height: document.getElementById(container).clientHeight,
    animate: true,
    fitCenter: true,
    // linkCenter: true,
    minZoom: 0.2,
    defaultNode: {
      labelCfg: {
        position: "bottom",
        style: {
          opacity: 0,
        },
      },
      type: "custom",
      size: [60, 60],
      clipCfg: {
        show: true,
        type: "circle",
        r: 30,
      },
    },
    defaultEdge: {
      type: "quadratic",
      style: {
        stroke: "gray",
        cursor: "pointer",
      },
      labelCfg: {
        autoRotate: true,
        style: {
          opacity: 1,
          fill: "#2c86f8",
          fontSize: 14,
          cursor: "pointer",
          background: {
            fill: "#ffffff",
            padding: [2, 2, 2, 2],
            radius: 2,
          },
        },
      },
    },
    // 节点在各状态下的样式
    nodeStateStyles: {
      // hover 状态为 true 时的样式
      hover: {
        stroke: "#409EFF",
        lineWidth: 3,
      },
      // 节点在 selected 状态下的样式，对应内置的 click-select 行为
      selected: {
        stroke: "#409EFF",
        lineWidth: 3,
        shadowColor: "#409EFF",
        shadowBlur: 10,
        opacity: 1,
      },
      analysis: {
        stroke: "#409EFF",
        lineWidth: 3,
        shadowColor: "#409EFF",
        shadowBlur: 10,
        opacity: 1,
        labelCfg: {
          style: {
            opacity: 1,
          },
        },
      },
      secondary: {
        opacity: 0.2,
        labelCfg: {
          style: {
            opacity: 0.2,
          },
        },
      },
    },
    // 边在各状态下的样式
    edgeStateStyles: {
      // hover 状态为 true 时的样式
      hover: {
        stroke: "#409EFF",
        lineWidth: 2,
      },
      connectionHover: {
        stroke: "#409EFF",
        lineWidth: 2,
        opacity: 1,
      },
      // selected 状态为 true 时的样式
      selected: {
        opacity: 1,
        stroke: "#409EFF",
        lineWidth: 2,
        shadowColor: "transparent",
      },
      analysis: {
        opacity: 1,
        stroke: "#409EFF",
        lineWidth: 2,
        labelCfg: {
          style: {
            opacity: 1,
          },
        },
      },
      connectionAnalysis: {
        opacity: 1,
        stroke: "#409EFF",
        lineWidth: 2,
        labelCfg: {
          style: {
            opacity: 1,
          },
        },
      },
      secondary: {
        opacity: 0.2,
        cursor: "unset",
        labelCfg: {
          style: {
            opacity: 0.2,
          },
        },
      },
    },
  };
}
