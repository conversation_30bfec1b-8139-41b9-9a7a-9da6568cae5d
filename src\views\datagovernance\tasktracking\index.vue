<template>
  <div class="indexBg">
    <!-- <div v-if="!componentName">
      <create-tabs
        v-for="(item, index) in componentList"
        :key="index"
        :componentName="item.componentName"
        :tabs-text="item.text"
        @selectModule="selectModule"
      >
        {{ item.text }}
      </create-tabs>
    </div> -->
    <itemsView @selectModule="selectModule" />
    <component :is="componentName" :id="id" :import-total-count="importTotalCount" class="component-class"></component>
  </div>
</template>
<script>
export default {
  name: 'tasktracking',
  props: {},
  data() {
    return {
      componentName: null, // 如果有组件名称则显示组件，否则显示路由本身dom
      id: 1,
      importTotalCount: 0,
    };
  },
  created() {},
  methods: {
    selectModule(name, item) {
      this.componentName = name;
      this.id = item.id;
      this.importTotalCount = item.dataNum;
    },
  },
  watch: {},
  computed: {},
  components: {
    carView: require('./car').default,
    viewprocess: require('@/views/datagovernance/governancetheme/viewprocess/index.vue').default,
    itemsView: require('./components/items').default,
    baseData: require('./basedata').default,
    faceData: require('./facedata').default,
    importantPerson: require('./importantperson').default,
  },
};
</script>
<style lang="less" scoped>
.indexBg {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url('~@/assets/img/thememanagement/traking-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
}
.component-class {
  height: calc(100% - 210px);
  overflow-y: scroll;
  position: relative;
  margin-top: 22px;
}
@{_deep}.process-block-center-content-desc {
  min-height: 0;
}
</style>
