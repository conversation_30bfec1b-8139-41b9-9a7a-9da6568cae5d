export default {
  getExternalLogin: '/ivdg-asset-app/device/getExternalLogin', //获取第三方系统用户名密码
  // 视图数据资产
  getDataSourceStatistic: '/iBDGDataPropertyApp/deviceInfo/getDataImportSourceStatistic', // 数据来源统计
  getDeviceById: '/ivdg-asset-app/device/view', //根据id获取设备信息
  viewDeviceId: '/ivdg-asset-app/device/viewDeviceId', //根据deviceId获取设备信息
  getDeviceRecord: '/ivdg-asset-app/deviceRecord/pageList', //设备操作记录列表
  addDevice: '/ivdg-asset-app/device/add', //添加设备信息
  updateDeviceById: '/ivdg-asset-app/device/update', //根据id更新设备信息
  getPageDeviceList: '/ivdg-asset-app/device/pageList', //分页获取设备信息列表
  getTagAnalysisPageDeviceList: '/ivdg-asset-app/deviceTagLink/pageList', //分页获取设备信息列表
  queryAbnormalDevicePageList: '/ivdg-asset-app/device/queryAbnormalDevicePageList', //分页获取异常设备信息列表
  queryAbnormalDeviceInfoCount: '/ivdg-asset-app/device/queryAbnormalDeviceInfoCount', //分页获取异常设备统计
  queryFaceLibAbnormalPageList: '/ivdg-asset-app/faceLibAbnormal/queryFaceLibAbnormalPageList', //分页查询视图废弃库
  queryFaceLibAbnormalCount: '/ivdg-asset-app/faceLibAbnormal/queryFaceLibAbnormalCount', //查询人脸视图数据统计
  queryErrorMessages: '/ivdg-asset-app/faceLibAbnormalInfoResult/queryErrorMessages', //查询人脸视图异常原因
  queryVehicleResultPageList: '/ivdg-data-governance-service/topic/vehicle/queryVehicleResultPageList', // 查询车辆视图异常库分页数据列表
  statisticsDevice: '/ivdg-asset-app/device/statisticsDevice', //统计合格不合格数据
  batchUpdate: '/ivdg-asset-app/device/batchUpdate', //批量更新设备
  queryUserOperateStatus: '/ivdg-asset-service/websocket/queryUserOperateStatus', //获取状态
  uploadDeleteDevice: '/ivdg-asset-app/device/uploadDeleteDevice', //上传文件删除设备
  deleteDeviceForRedis: '/ivdg-asset-app/device/deleteDeviceForRedis', //删除设备
  deleteDevice: '/ivdg-asset-app/device/remove', //设备信息删除

  // 上传设备信息
  uploadDeviceInfo: '/ivdg-asset-app/device/uploadDeviceInfo', //上传设备信息

  //重点人员
  queryImportantPersonPageList: '/ivdg-asset-app/importantPerson/queryImportantPersonPageList', //获取重点人员分页列表
  queryImportantPersonCount: '/ivdg-asset-app/importantPerson/queryImportantPersonCount', //获取重点人员对应条件总数
  queryImportantPersonErrorMessage:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryImportantPersonErrorMessage', //获取重点人员错误原因列表
  queryDeviantImportantPersonLibListPage: '/ivdg-asset-app/importantPersonLib/queryDeviantImportantPersonLibListPage', //获取不合格的重点人员轨迹数据分页列表
  queryDeviantImportantPersonLibCont: '/ivdg-asset-app/importantPersonLib/queryDeviantImportantPersonLibCont', // 查询不合格的重点人员轨迹总数及当日新增数量
  queryImportantPersonLibErrorMessage:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryImportantPersonLibErrorMessage', // 获取重点人员轨迹错误原因列表

  // 主题
  queryDeviceInfoListByTopic: '/ivdg-asset-app/device/queryDeviceInfoListByTopic', //主题查询设备列表
  queryDeviceInfoPageList: '/ivdg-asset-app/device/pageList', //选择设备接口

  // 重点人员轨迹库
  queryPersonLibPageTaskGather: '/ivdg-asset-app/importantPersonLib/queryPersonLibPageTaskGather', // 人员轨迹异常数据聚档
  queryPersonLibTaskGatherStatistics: '/ivdg-asset-app/importantPersonLib/queryPersonLibTaskGatherStatistics', // 人员轨迹异常数据聚档接口详情统计数据
  importantPersonDetail: '/ivdg-asset-app/importantPerson/view', // 通过id获取人员数据信息
  queryPersonLibTaskGatherPullData: '/ivdg-asset-app/importantPersonLib/queryPersonLibTaskGatherPullData', // 人员轨迹异常数据聚档下拉数据接口(抓拍类型)
  queryPersonLibTaskGatherDetail: '/ivdg-asset-app/importantPersonLib/queryPersonLibTaskGatherDetail', // 人员轨迹异常数据聚档详情数据列表
  queryPersonLibPageGather: '/ivdg-asset-app/importantPersonLib/queryPersonLibPageGather', // 人员轨迹数据聚档接口
  queryPersonLibGatherStatistics: '/ivdg-asset-app/importantPersonLib/queryPersonLibGatherStatistics', // 人员轨迹数据聚档详情统计接口
  queryPersonLibGatherDetail: '/ivdg-asset-app/importantPersonLib/queryPersonLibGatherDetail', // 人员轨迹数据聚档详情统计接口

  importPersonDetails: '/ivdg-data-governance-service/topicComponentPersonResult/importPersonDetails', //聚档详情//聚档详情
  importPersonDetailsCount: '/ivdg-data-governance-service/topicComponentPersonResult/importPersonDetailsCount', //聚档总数

  deviceInfoSync: '/ivdg-asset-app/deviceSync/deviceInfoSync', //同步
  resetOsd: '/ivdg-asset-app/deviceSync/resetOsd', //重设osd字幕
  resetOsdTime: '/ivdg-governance-app/fullInMessage/resetOsdTime', //重设osd时钟

  // 白名单
  getWhiteList: '/ivdg-asset-app/wdc/device/pageList', //白名单列表
  addWhiteList: '/ivdg-asset-app/wdc/device/addByIds', //白名单列表
  getNotInwhitePageList: '/ivdg-asset-app/device/whitePageList', //查询不在白名单列表中的设备信息分页列表
  removewhiteList: '/ivdg-asset-app/wdc/device/remove/', //删除白名单
  placeManagerList: '/ivdg-governance-app/place/manager/pageList', //查询场所管理:地名编码:地名填报字段要求分页列表
  addPlace: '/ivdg-governance-app/place/manager/add', //场所管理:地名编码:地名填报字段要求新增
  updatePlace: '/ivdg-governance-app/place/manager/update', //场所管理:地名编码:地名填报字段要求编辑
  uploadPlace: '/ivdg-governance-app/place/manager/upload', //场所管理:地名编码:地名填报上传
  viewPlace: '/ivdg-governance-app/place/manager/view', //获取场所管理:地名编码:地名填报字段要求详细信息
  removePlace: '/ivdg-governance-app/place/manager/remove', //区域管理:地名编码:地名填报字段要求删除

  reportPageList: '/ivdg-asset-app/device/report/pageList', //信息填报查询接口
  updateDeviceInfo: '/ivdg-governance-app/fullInMessage/updateDeviceInfo', //信息填报更新接口
  queryCheckErrorList: '/ivdg-asset-app/deviceCheckStatusItem/queryCheckErrorList', //查询设备检测状态错误原因接口
  exportDevice: '/ivdg-asset-app/device/exportDeviceZip', //设备库根据勾选字段导出
  queryUnqualifiedList: '/ivdg-asset-app/deviceCheckStatusItem/queryUnqualifiedList', //查看不合格原因列表

  // 数据接入配置
  viewByParamKey: '/qsdi-system-service/system/param/getParamDataByKeys', // 根据参数获取详情
  updateByParamKey: '/qsdi-system-service/system/param/update', // 根据参数编辑

  showDeviceSyncInfo: '/ivdg-governance-app/fullInMessage/showDeviceSyncInfo', //设备信息
  updateDeviceInfo_str: '/ivdg-asset-app/deviceSync/updateDeviceInfo', //设备信息更新
  queryErrorReason: '/ivdg-asset-app/deviceCheckStatus/queryErrorReason', //错误类别

  // 根据设备id或者设备编码查询设备详情
  queryDeviceInfoByIds: '/ivdg-asset-app/device/queryDeviceInfoByIds',
  getDayCaptureStatistics: '/ivdg-asset-app/device/dayCaptureStatistics', //设备历史抓拍分析
  getIsOnlineStatistics: '/ivdg-asset-app/device/isOnlineStatistics', //设备在线情况分析
  getDevicMapPlace: '/ivdg-governance-app/place/manager/mapPlace', //   图上位置
  confidenceList: '/ivdg-evaluation-app/evaluation/RateDetail/pageList', //置信率列表
  getDetailCount: '/ivdg-evaluation-app/evaluation/RateDetail/getDetailCount', //置信率统计
  accuracyList: '/ivdg-evaluation-app/evaluation/AccuracyDetail/pageList', //准确率列表
  accuracyCount: '/ivdg-evaluation-app/evaluation/AccuracyDetail/getDetailCount', //置信率统计
  libInfoDetail: '/ivdg-evaluation-app/evaluation/libInfo/pageList', //车辆准确详情列表

  // 重点设备管理
  removeImportDevices: '/ivdg-asset-app/device/removeImportDevices', //移除重点设备
  addImportDevices: '/ivdg-asset-app/device/addImportDevices', // 重点设备添加
  getNextLevelPerOrgCodeDeviceCount: '/ivdg-asset-app/deviceApp/getNextLevelPerOrgCodeDeviceCount', //获取组织机构下级设备统计
  getDevicListInPolygon: '/ivdg-asset-app/device/getDevicListInPolygon', //获取在多边形区域内的设备
  sbgnlxGroupStatByOrgCode: '/ivdg-asset-app/device/sbgnlxGroupStatByOrgCode', //根据设备功能类型统计
  exportImportDeviceTemplate: '/ivdg-asset-app/device/exportImportDeviceTemplate', //导出重点设备模板
  exportUnlabeledImportDevice: '/ivdg-asset-app/device/exportUnlabeledImportDevice', //导出有问题的设备

  // 同步设备库
  getListByChannelId: '/ivdg-stream-sip-service/stream/videoDeviceChannel/getListByChannelId', //根据目录id获取设备集合
  videoDeviceChannelGetTree: '/ivdg-stream-sip-service/stream/videoDeviceChannel/getTree ', //获取设备目录树
  downloadDeviceByChannelId: '/ivdg-stream-sip-service/stream/videoDeviceChannel/downloadDeviceByChannelId', //导出目录设备列表
  syncYSDeviceInfoList: '/ivdg-asset-app/device/ha/syncYSDeviceInfoList', //同步宇视共享设备信息到设备库
  updateCatalog: '/ivdg-stream-sip-service/stream/media/catalog', //重新从28181中获取设备列表
  catalogAndDeleteListData: '/ivdg-asset-app/assertDeviceAsycNet/catalogAndDeleteListData', //重新从28181中获取设备列表

  // 资产填报
  fillAddCheck: '/ivdg-asset-app/assert/device/fillIn/addCheck', //新增检测
  fillAdd: '/ivdg-asset-app/assert/device/fillIn/add', //新增
  viewFill: '/ivdg-asset-app/assert/device/fillIn/view', //查看
  fillList: '/ivdg-asset-app/assert/device/fillIn/pageList', //列表
  deleteFill: '/ivdg-asset-app/assert/device/fillIn/delete', //删除
  updateFill: '/ivdg-asset-app/assert/device/fillIn/update', //更新
  updateCheckFill: '/ivdg-asset-app/assert/device/fillIn/updateCheck', //更新检测
  queryDifferDetail: '/ivdg-asset-app/assert/device/fillIn/queryDifferDetail', //差异详情
  queryUnqualifiedDetail: '/ivdg-asset-app/assert/device/fillIn/queryUnqualifiedDetail', //错误原因
  checkAndContrast: '/ivdg-asset-app/assert/device/fillIn/checkAndContrast', //检测与比对
  uploadSave: '/ivdg-asset-app/assert/device/fillIn/saveFormRedis', //上传保存
  downloadExistUpload: '/ivdg-asset-app/assert/device/fillIn/downloadDeviceIds', //下载已存在编码列表
  countFillInNum: '/ivdg-asset-app/assert/device/fillIn/countFillInNum', //统计合格不合格数据
  queryExamineListByFillInId: '/ivdg-asset-app/assert/device/fillIn/examine/queryExamineListByFillInId', //根据填报表ID查询审核记录
  getListForAssert: '/ivdg-asset-app/asset/report/getListForAssert', // 获取设备资产库
  exportFill: '/ivdg-asset-app/assert/device/fillIn/exportDeviceZip', //导出填报设备

  // 其他资产
  getAssertOtherDevice: '/ivdg-asset-app/assertOtherDevice/pageList', //获取其他资产列表
  downloadOtherTemplate: '/ivdg-asset-app/assertOtherDevice/downloadTemplate', //模板下载
  assertOtherRemove: '/ivdg-asset-app/assertOtherDevice/remove', //其他设备删除
  queryOtherStatistics: '/ivdg-asset-app/assertOtherDevice/queryDeviceStatistics', //查询统计
  updateOtherDevice: '/ivdg-asset-app/assertOtherDevice/update', //更新其他资产
  viewOtherDevice: '/ivdg-asset-app/assertOtherDevice/view', //查看其他资产

  // 空间信息治理
  saveBatch: '/ivdg-asset-app/deviceInfoSpaceCollect/saveBatch',
  getSpaceDevList: '/ivdg-asset-app/deviceInfoSpaceCollect/pageList',
  spaceDevRemove: '/ivdg-asset-app/deviceInfoSpaceCollect/remove', // 空间信息治理列表设备删除
  querySpaceUnqualifiedList: '/ivdg-asset-app/deviceCheckStatus/querySpaceUnqualifiedList', // 空间信息治理列表 查询单个设备信息下的异常原因
  spaceCollectList: '/ivdg-asset-app/deviceInfoSpaceCollect/spaceCollectList', // 空间信息治理全部设备
  getHyLngLat2Address: '/ivdg-asset-app/deviceInfoSpaceCollect/getHyLngLat2Address', // 通过经纬度获取地址
  devSpaceCollecteUpdate: '/ivdg-asset-app/deviceInfoSpaceCollect/update', // 编辑治理设备
  queryAllSpaceUnqualifiedList: '/ivdg-asset-app/deviceCheckStatus/queryAllSpaceUnqualifiedList', // 空间信息异常所有异常原因
  devSpaceCollecteUpdateV2: '/ivdg-asset-app/deviceInfoSpaceCollect/updateV2', // 编辑治理设备

  // 设备分析
  analysisView: '/ivdg-asset-app/analysis/config/view', //获取设备分析
  analysisSave: '/ivdg-asset-app/analysis/config/add', //保存配置
  queryAnalysis: '/ivdg-asset-app/analysis/config/queryTask', //获取分析任务
  getTaskIndexForAnalysis: '/ivdg-evaluation-app/evaluation/app/taskScheme/getTaskIndexForAnalysis/', //根据检测任务查询指标
  shareDevice: '/ivdg-data-share-service/CHENDU/VIID/API/share', //资产共享
  batchAddTag: '/ivdg-asset-app/deviceTagLink/device/analysis/add/tag', //批量添加标签

  // 检测数据管理
  getManagementData: '/ivdg-asset-app/asset/data/pageList',
  removeManagementData: '/ivdg-asset-app/asset/data/remove',
  editManagementData: '/ivdg-asset-app/asset/data/edit',
  batchInsertManagementData: '/ivdg-asset-app/asset/data/batchInsert',
  dowloadManagementData: '/ivdg-asset-app/asset/data/dowload',

  // 工作汇报
  workReportList: '/ivdg-asset-app/workReport/pageList',
  workReportAdd: '/ivdg-asset-app/workReport/add',
  workReportView: '/ivdg-asset-app/workReport/view',
  workReportUpload: '/ivdg-asset-app/workReport/upload',
  workReportUpdate: '/ivdg-asset-app/workReport/update',
  workReportDelete: '/ivdg-asset-app/workReport/delete',

  // 选点配置
  qualityCivilCodeList: '/ivdg-asset-app/device/quality/result/civilCode/pageList',
  qualitySbcjqyList: '/ivdg-asset-app/device/quality/result/sbcjqy/pageList',
  qualityConfig: '/ivdg-asset-app/device/quality/config/queryList',
  qualityConfigSave: '/ivdg-asset-app/device/quality/config/saveOrUpdate',
  qualityByCivilCode: '/ivdg-asset-app/device/quality/report/byCivilCode',
  qualityBySbcjqy: '/ivdg-asset-app/device/quality/report/bySbcjqy',
  updateDevicePhyStatus: '/ivdg-asset-app/device/quality/manage/updateDevicePhyStatus',
  qualityReportBatch: '/ivdg-asset-app/device/quality/report/batch',
  qualityRefresh: '/ivdg-asset-service/device/quality/result/refresh',
  qualityManageList: '/ivdg-asset-app/device/quality/manage/pageList',
  queryQualityJobCron: '/ivdg-asset-app/device/quality/config/queryQualityJobCron',
  updateQualityJobCron: '/ivdg-asset-app/device/quality/config/updateQualityJobCron',
  updateStanderConfig: '/ivdg-asset-app/device/quality/stander/config',
  getStanderConfig: '/ivdg-asset-app/device/quality/stander/view',
  executeCheck: '/ivdg-asset-service/device/quality/result/executeCheck',
  reunningStatus: '/ivdg-asset-service/device/quality/result/query/running/status',

  // 资产统计
  getDevicePropertyStatistics: '/ivdg-asset-app/assertDeviceStatistics/devicePropertyStatistics', //设备各个属性统计
  postQueryOneAssertDeviceStatistics: '/ivdg-asset-app/assertDeviceStatistics/queryOneAssertDeviceStatistics', //查询key-value统计（头部）
  postQueryPropertyStatistics: '/ivdg-asset-app/assertDeviceStatistics/queryPropertyStatistics', //查询单个属性统计
  postQueryStatisticsByAmount: '/ivdg-asset-app/assertDeviceStatistics/queryStatisticsByAmount', //查询设备资产统计--数量变化趋势
  postQueryStatisticsBySbcjqy: '/ivdg-asset-app/assertDeviceStatistics/queryStatisticsBySbcjqy', //查询设备资产统计--采集类型区域分布
  postQueryStatisticsByCommonCondition: '/ivdg-asset-app/assertDeviceStatistics/queryStatisticsByCommonCondition', //查询设备资产统计--(设备状态区域分布,重点类型区域分布,基础，人，车，视频在线区域分布)
  queryChartPropertyStatisticsList: '/ivdg-asset-app/assertDeviceStatistics/queryChartPropertyStatisticsList',

  // ZDR人像轨迹治理
  queryStatisticsList: '/ivdg-data-share-service/focusGovernDetail/queryStatisticsList',
  queryDetailList: '/ivdg-data-share-service/focusGovernDetail/queryDetailList',
  queryReasonList: '/ivdg-data-share-service/focusGovernDetail/queryReasonList',

  getDeviceListByDeviceIds: '/ivdg-asset-app/device/getDeviceListByDeviceIds', // 根据设备编码获取设备列表

  // 车流量数据治理
  queryStatistics: '/ivdg-governance-app/vehicleGovernDetail/queryStatistics',
  queryImageList: '/ivdg-governance-app/vehicleGovernDetail/queryImageList',
};
