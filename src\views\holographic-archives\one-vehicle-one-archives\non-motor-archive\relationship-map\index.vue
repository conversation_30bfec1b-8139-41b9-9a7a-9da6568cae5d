<template>
  <div class="trajectory">
    <div class="left">
      <BasicInformation
        :labelType="6"
        :baseInfo="basicInfo"
        type="car"
        :imgSrc="imgSrc"
      />
      <!-- <ui-card title="基础信息 " class="m-b20" :padding="20"> -->
      <!-- 信息表格 -->
      <!-- <Card type="car" :baseInfo="vehicleInfo" /> -->
      <!-- </ui-card> -->
    </div>
    <div class="right">
      <ui-card title="" :padding="20">
        <!-- 关系图谱 -->
        <CollapseExpand
          ref="collapseExpand"
          :isHasExpand="true"
          @onNodeClick="onNodeClick"
          @onLineClick="onLineClick"
          :atlasList="atlasList"
          :options="{ allowShowMiniToolBar: false }"
        />
      </ui-card>
      <ui-loading v-if="MixinLoading" />
      <ui-empty v-if="!Object.keys(atlasList).length && !MixinLoading" />
      <div
        class="play-btn mr-20 color-primary cursor-p"
        @click="MixinToNumCube"
        v-if="atlasList.nodes.length > 1"
      >
        数智立方
      </div>
    </div>
    <!-- 关系图谱点击线展示详情 -->
    <RelationshipInfo
      type="relationship"
      v-model="relationshipInfoShow"
      ref="relationshipInfo"
    />
  </div>
</template>
<script>
import "swiper/dist/css/swiper.css";

import BasicInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin.js";

import { getVehicleBaseInfo, personBaseInfo } from "@/api/vehicleArchives";
export default {
  mixins: [relativeGraphMixin],
  components: {
    BasicInformation,
    CollapseExpand: require("@/components/relation-graph/number-cube").default,
    Card: require("@/views/holographic-archives/components/relationship-map/basic-information")
      .default,
    RelationshipInfo:
      require("@/views/number-cube/components/relationship-info").default,
  },
  props: {},
  data() {
    return {
      imgSrc: require("@/assets/img/demo/vehicle.png"),
      relationshipInfoShow: false,
      swiperOption1: {
        effect: "coverflow",
        slidesPerView: 1.7,
        centeredSlides: true,
        loop: true,
        loopAdditionalSlides: 2,
        speed: 500,
        // autoplay: {
        //   delay: 1000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false,
        // },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 20, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 200, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1.2, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: false, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      atlasList: {
        // rootId: '1',
        // nodes: [
        //   { id: '1', label: 'person', name: '胡一菲', img: people1 },
        //   { id: '2', label: 'person', name: '陈赫', img: people2 },
        //   { id: '3', label: 'person', name: '张伟', img: people3 },
        //   { id: '4', label: 'person', name: '关谷', img: people4 },
        //   { id: '5', label: 'person', name: '唐悠悠', img: people5 },
        //   { id: '6', label: 'person', name: '吕子乔', img: people6 },
        //   { id: '7', label: 'person', name: '陈美嘉', img: people7 },
        //   { id: '8', label: 'person', name: '诸葛大力', img: people8 },
        //   { id: '9', label: 'car', name: '苏A 88888', img: car1 },
        //   { id: '10', label: 'car', name: '苏A 66666', img: car2 },
        //   { id: '11', label: 'wifi', name: '42-63-84-72-33' },
        //   { id: '12', label: 'iphone', name: '15555555555' },
        //   { id: '13', label: 'adress', name: '雨花台区花园小学' }
        // ],
        // links: [
        //   { from: '1', to: '2', text: '关系( 2 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '3', text: '关系( 4 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '4', text: '关系( 5 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '5', text: '关系( 2 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '6', text: '关系( 3 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '7', text: '关系( 3 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '8', text: '关系( 3 )', fontColor: '#2C86F8' },
        //   { from: '1', to: '9', text: '乘坐车辆', fontColor: '#2C86F8' },
        //   { from: '1', to: '10', text: '乘坐车辆', fontColor: '#2C86F8' },
        //   { from: '1', to: '11', text: '归属', fontColor: '#2C86F8' },
        //   { from: '1', to: '12', text: '归属', fontColor: '#2C86F8' },
        //   { from: '1', to: '13', text: '归属', fontColor: '#2C86F8' }
        // ]
      },
      vehicleInfo: {}, // 车主信息
      basicInfo: {}, // 车主信息
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {
    var { archiveNo, plateNo, idcardNo } = this.$route.query;
    this.basicInfoFn(JSON.parse(archiveNo));
  },
  methods: {
    // 节点点击返回
    onNodeClick(item) {
      console.log("节点点击", item);
      //this.$Message.success('节点点击')
    },
    // 线点击
    onLineClick(item) {
      console.log("线点击", item);
      //this.$Message.success('线点击打开详情')
      //this.relationshipInfoShow = true
    },
    basicInfoFn(archiveNo) {
      // 基本信息
      getVehicleBaseInfo(archiveNo).then((res) => {
        console.log("基本信息", res);
        this.basicInfo = res.data;

        // if(res.data.idcardNo) {
        //   // 车主信息
        //   var param = {
        //     archiveNo: res.data.idcardNo,
        //     dataType: 1
        //   }
        //   personBaseInfo(param).then(res2 =>{
        //     console.log('车主信息', res2)
        //     this.vehicleInfo = res2.data || {}
        //     this.basicInfo = [...this.basicInfo, ...this.vehicleInfo]
        //     // this.basicInfo = Object.assign(this.basicInfo, res2.data)
        //     console.log('***', this.basicInfo)
        //     if(this.vehicleInfo.photos.length > 0) {
        //       this.vehicleInfo.imgUrl = this.vehicleInfo.photos[0].photoUrl
        //     }
        //   })

        // }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.trajectory {
  display: flex;
  flex: 1;
  height: 100%;
  padding: 10px;
  padding-top: 16px;
  /deep/.card-content {
    // display: flex;
    // flex: 1;
    height: calc(~"(100% - 30px)");
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 0 !important;
  }
  /deep/ .ui-card {
    height: 100%;
  }
  .left {
    margin-right: 10px;
    height: 100%;
    width: 350px;
  }
  .right {
    width: calc(~"100% - 360px");
    // margin-top: -28px;
    .ui-card .card-head {
      height: 0px;
    }
  }
}
/deep/ .rel-node {
  background-color: transparent !important;
  border: 0 !important;
}
</style>
<style lang="less">
.trajectory {
  .right {
    position: relative;
    .ui-card .card-head {
      height: 0px;
    }
    .card-content {
      height: 100%;
      padding: 0px !important;
    }
    .play-btn {
      position: absolute;
      font-size: 14px;
      top: 20px;
      right: 20px;
      z-index: 10;
    }
  }
}
</style>
