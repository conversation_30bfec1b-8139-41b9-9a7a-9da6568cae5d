<template>
  <div class="echarts-div" v-scroll="287">
    <div class="statistics">
      <div class="round">
        <i class="icon-font icon-fuwuqizongliang"></i>
      </div>
      <div class="ml-sm">
        <div class="secondary">服务器总量</div>
        <div class="table-text-content total-camera">
          {{ statistics.count }}
        </div>
      </div>
    </div>
    <div>
      <draw-echarts
        :echart-option="echartOnlineRing"
        :echart-style="ringStyle"
        ref="ringChart"
        class="charts"
      ></draw-echarts>
    </div>
    <div>
      <draw-echarts
        :echart-option="echartOfflineRing"
        :echart-style="ringStyle"
        ref="ringChart"
        class="charts"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import maintain from '@/config/api/maintain';
export default {
  name: 'charts',
  props: {
    // statistics: {
    //   type: Object,
    //   default: () => {}
    // }
  },
  data() {
    return {
      statistics: {
        count: 0,
        onlineCount: 0,
        offlineCount: 0,
      },
      echartOnlineRing: {},
      echartOfflineRing: {},
      ringStyle: {
        width: '100%',
        height: '230px',
      },
    };
  },
  mounted() {
    this.initStatisc();
  },
  methods: {
    async initStatisc() {
      try {
        let res = await this.$http.get(maintain.getServerStatistics);
        Object.assign(this.statistics, res.data.data);
        let params = {
          onlinePercent: Math.round((this.statistics.onlineCount / this.statistics.count) * 100),
          offlinePercent: Math.round((this.statistics.offlineCount / this.statistics.count) * 100),
        };
        this.initRing(params);
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 设备
     * echarts图表必须初始化
     **/
    initRing(params) {
      let onlineOpts = {
        color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: $var('--linear-gradient-green-3-start'),
          },
          {
            offset: 1,
            color: $var('--linear-gradient-green-3-end'),
          },
        ]),
        percent: params.onlinePercent,
        status: '在线',
        statusColor: $var('--color-green-4'),
      };
      let offlineOpts = {
        color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: $var('--linear-gradient-orange-4-start'),
          },
          {
            offset: 1,
            color: $var('--linear-gradient-orange-4-end'),
          },
        ]),
        percent: params.offlinePercent,
        status: '离线',
        statusColor: $var('--color-error'),
      };
      this.echartOnlineRing = this.$util.doEcharts.equipmentMonitoringRing(onlineOpts);
      this.echartOfflineRing = this.$util.doEcharts.equipmentMonitoringRing(offlineOpts);
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts.vue').default,
  },
};
</script>

<style lang="less" scoped>
.echarts-div {
  .statistics {
    display: flex;
    align-items: center;
    padding: 30px 0 0;
    i {
      font-size: 20px;
      color: var(--color-display-text);
    }
  }
  .round {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background: rgba(25, 213, 246, 0.2);
    border-radius: 50%;
  }
  .total-camera {
    font-size: 16px;
    color: var(--color-display-text);
  }
  .secondary {
    font-size: 12px;
    color: var(--color-label);
  }
}
</style>
