<template>
  <div class="menu-list">
    <ui-menu :open-names="openNames" :active="activeRouter" :is-collapsed="isCollapsed">
      <template v-for="(item, index) in menuList">
        <sub-menu
          class="sub-menu-first"
          v-if="item[menuProp.menuChildren] && item[menuProp.menuChildren].length > 0"
          :name="item[menuProp.menuName]"
          :key="`${index}-${item.path}`"
          :title="item[menuProp.menuText]"
        >
          <template #title>
            <i class="iconfontconfigure" :class="`icon-${item[menuProp.menuIcon]}`"></i>
            <span class="inline vt-middle">{{ item[menuProp.menuText] }}</span>
            <div class="triangle"></div>
          </template>
          <template v-for="(itm, ind) in item[menuProp.menuChildren]">
            <sub-menu
              v-if="itm[menuProp.menuChildren] && itm[menuProp.menuChildren].length > 0"
              :name="itm[menuProp.menuName]"
              :key="ind"
              :title="itm[menuProp.menuText]"
            >
              <template #title>
                <span class="inline vt-middle">{{ itm[menuProp.menuText] }}</span>
              </template>
              <menu-item
                v-for="(row, i) in itm[menuProp.menuChildren]"
                :key="i"
                :name="row[menuProp.menuName]"
                @selectMenu="selectMenu"
              >
                <span class="inline vt-middle">{{ row[menuProp.menuText] }}</span>
              </menu-item>
            </sub-menu>
            <menu-item v-else :name="itm[menuProp.menuName]" @selectMenu="selectMenu" :key="ind">
              <span class="inline vt-middle">{{ itm[menuProp.menuText] }}</span>
            </menu-item>
          </template>
        </sub-menu>
        <menu-item
          v-else
          class="menu-first"
          :name="item[menuProp.menuName]"
          :key="index"
          :title="item[menuProp.menuText]"
          @selectMenu="selectMenu"
        >
          <i class="iconfontconfigure" :class="`icon-${item[menuProp.menuIcon]}`"></i>
          <span class="inline vt-middle">{{ item[menuProp.menuText] }}</span>
        </menu-item>
      </template>
    </ui-menu>
  </div>
</template>
<script>
import navExtends from '../utils/extends';
export default {
  extends: navExtends,
  components: {
    SubMenu: require('@/components/ui-menu/sub-menu.vue').default,
    MenuItem: require('@/components/ui-menu/menu-item.vue').default,
    UiMenu: require('@/components/ui-menu/ui-menu.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .menu-list {
    @{_deep}.menu {
      .sub-menu-first {
        .triangle {
          border-right: 9px solid #7893af;
          border-top: 9px solid transparent;
        }
        &.sub-menu-active {
          > .sub-menu-title {
            .triangle {
              border-right-color: #fff;
            }
            &:before {
              background: transparent;
            }
          }
        }
      }
    }
  }
}
.menu-list {
  height: calc(~'100% - 60px');
  overflow-y: auto;
  overflow-x: hidden;
  &::-webkit-scrollbar {
    display: none;
  }
  @{_deep} > .menu {
    > .menu-item {
      padding: 13px 0;
      justify-content: center;
      flex-direction: column;
    }
  }
}
.menu-first {
  position: relative;
  &.menu-item-active {
    &:before {
      content: '';
      width: 4px;
      height: 100%;
      background: var(--bg-menu-item-after-horizontal);
      position: absolute;
      left: 0;
      top: 0;
    }
  }
}
.sub-menu-first {
  .triangle {
    width: 0;
    height: 0;
    border-right: 9px solid #7893af;
    border-top: 9px solid transparent;
    position: absolute;
    bottom: 0;
    right: 0;
  }
  @{_deep} > .menu-vertical {
    padding-left: 10px;
  }
  @{_deep}&.sub-menu-active {
    > .sub-menu-title {
      .triangle {
        border-right-color: #29c9fb;
      }
      &:before {
        content: '';
        width: 4px;
        height: 100%;
        background: #29c9fb;
        position: absolute;
        left: 0;
        top: 0;
      }
    }
  }
}
</style>
