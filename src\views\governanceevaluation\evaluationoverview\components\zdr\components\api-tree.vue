<template>
  <div class="inline api-organization-tree width-md" v-clickoutside="dropHide">
    <span v-if="!!selectTree.title" class="f-12">{{ selectTree.title }}</span>
    <Dropdown trigger="custom" :visible="visible">
      <div
        class="ivu-select ivu-select-single select-width t-left width-md"
        @mouseenter="mouseenter"
        @mouseleave="mouseleave"
      >
        <div class="ivu-select-selection" @click="dropShow">
          <div>
            <span class="ivu-select-placeholder" v-if="!treeText">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="treeText">{{ treeText }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!isClose"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="isClose" />
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <el-tree
          class="tree width-md"
          node-key="id"
          multiple
          :style="treeStyle"
          :data="treeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultKeys"
          @node-click="handleNodeClick"
          @current-change="currentChange"
        >
          <span class="custom-tree-node" slot-scope="{ node }">
            <span :class="{ 'nav-not-selected': node.disabled, allowed: node.disabled }">{{ node.label }}</span>
          </span>
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
.select-width {
  width: 150px;
}
.tree {
  min-width: 150px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 500px;
  margin-right: 10px;
}

/deep/ .width-md,
.width-sm {
  width: 150px !important;
}
</style>
<script>
export default {
  data() {
    return {
      treeText: '',
      visible: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      defaultKeys: [],
      isClose: false, //清空按钮是否显示
    };
  },
  created() {},
  mounted() {
    this.defaultKeys = [this.taskObj.orgCode];
  },
  methods: {
    mouseenter() {
      if (this.treeText) {
        this.isClose = true;
      }
    },
    mouseleave() {
      this.isClose = false;
    },
    // 节点选中触发
    handleNodeClick(data) {
      if (data.disabled) {
        this.$Message.error('您没有此组织机构权限');
        return false;
      }
      this.selectTree.orgCode = data.orgCode;
      this.$emit('selectedTree', data);
    },
    setTreeText() {
      if (this.taskObj && this.taskObj.orgCodeList && this.taskObj.orgCodeList.length > 0) {
        let node = this.taskObj.orgCodeList.find((row) => {
          return row.orgCode === this.selectTree.orgCode;
        });
        this.treeText = node ? node[this.defaultProps.label] : '';
      } else {
        this.treeText = '';
      }
    },
    dropHide() {
      this.visible = false;
    },
    dropShow() {
      this.visible = !this.visible;
    },
    currentChange(data) {
      if (data.disabled) {
        return false;
      }
      this.visible = false;
    },
    clear() {
      this.selectTree.orgCode = null;
      this.isClose = false;
      this.$emit('selectedTree', this.selectTree);
    },
  },
  watch: {
    selectTree: {
      deep: true,
      handler: function () {
        this.$nextTick(() => {
          this.setTreeText();
        });
      },
    },
    treeData(val) {
      if (val && val.length !== 0) {
        // this.defaultKeys = val.map((row) => {
        //   return row.orgCode
        // })
      }
    },
  },
  computed: {
    // treeText() {
    //   let node = this.taskObj.orgCodeList.find((row) => {
    //     console.log(this.selectTree.orgCode, '=======')
    //     return row.orgCode === this.selectTree.orgCode
    //   })
    //   console.log(node, '--------------')
    //   return node ? node[this.defaultProps.label] : ''
    // },
  },
  props: {
    /**
     * selectTree.orgCode: 选中的值
     */
    selectTree: {
      required: true,
    },
    // 树结构style
    treeStyle: {},
    placeholder: {
      type: String,
      default: () => {
        return '请选择';
      },
    },
    treeData: {
      type: Array,
    },
    taskObj: {
      type: Object,
      default() {},
    },
  },
  components: {},
};
</script>
