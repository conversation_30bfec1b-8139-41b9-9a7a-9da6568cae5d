<template>
	<div class="main-container">
		<div class="top-box">
			<div class="top-card" :class="item.bg" v-for="(item,index) in topCardList" :key="index">
				<div>{{ item.cardName }}</div>
				<div class="num">{{ item.total }}</div>
				<img :src="require('@/assets/img/opration-center/'+item.imageUrl)" alt="">
				<img class="bg_img" src="@/assets/img/opration-center/bgLong.png" alt="">
			</div>
		</div>
		<div class="search-bar">
			<searchMf ref="searchMf" @searchInfo="searchInfo"></searchMf>
			<div class="btn-group">
				<Button type="default" icon="md-add" @click="add">新增</Button>
				<Button type="default" @click="removeMore"><i class="iconfont icon-shanchu"></i> 删除</Button>
			</div>
		</div>
		<div class="table-container">
			<div class="table-content">
				<ui-table :columns="columns" :data="tableList" @on-selection-change="selectionChangeHandle">
					<template #system="{row}">
						<div>{{ row.system.name  ||'' }}</div>
					</template>
					<template #cpu="{row}">
						<el-progress :percentage="row.cpu.usePercent" :stroke-width="8" :color="'#2C86F8'"></el-progress>
					</template>
					<template #memory="{row}">
						<el-progress :percentage="row.memory.usePercent" :stroke-width="8" :color="'#1faf81'"></el-progress>
						<div>{{ row.memory.use }}GB/<span class="color_f9">{{ row.memory.total }}GB</span> </div>
					</template>
					<template #disc="{row}">
						<el-progress :percentage="row.disc.totalUsePercent" :stroke-width="8" :color="'#f29f4c'"></el-progress>
						<div>{{ row.disc.totalUse }}GB/<span class="color_f9">{{ row.disc.total }}GB</span></div>
					</template>
					<template #status="{ row }">
						<Button :style="{color:'#fff',border:'none', background: row.status ?'#1faf81' :'#78858e'}" size="small">{{ row.status ?'在线': '离线' }}</Button>
					</template>
					<template #action="{ row }">
						<div class="btn-tips">
							<ui-btn-tip content="编辑" icon="icon-bianji" class="mr-20 primary" @click.native="edit(row)"></ui-btn-tip>
							<ui-btn-tip content="删除" icon="icon-shanchu" class="mr-20 primary" @click.native="remove(row)"></ui-btn-tip>
						</div>
					</template>
				</ui-table>
			</div>
			<!-- <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty> -->
			<ui-loading v-if="loading"></ui-loading>
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[20, 40, 60]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
		</div>
		<addModal ref="addModal" @refreshList="queryList" />

	</div>
</template>
<script>
	import searchMf from './components/search-mf'
	import addModal from './components/add-model'
	import { statusStatistics, pageList, deleteMf } from '@/api/opration-center'
	export default {
		name: 'mf-service',
		components: {
			searchMf,
			addModal
		},
		data() {
			return {
				topCardList: [
					{ imageUrl: 'mfNum.png', total: 19, bg: 'bg1', cardName: '主机数量' },
					{ imageUrl: 'onlineNum.png', total: 18, bg: 'bg2', cardName: '在线' },
					{ imageUrl: 'outlineNum.png', total: 1, bg: 'bg3', cardName: '离线' },
				],
				queryParam: {
				},
				tableList: [
					{ id: 1, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 2, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 3, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 20 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 4, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 20 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 5, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: false },
					{ id: 6, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 30 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: false },
					{ id: 7, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: false },
					{ id: 8, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 9, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 10, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 11, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 12, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
					{ id: 13, serverName: '开发服务器121', ip: '*************', system: { name: 'Linux 3.10.0-862.el7.x86_64', }, cpu: { usePercent: 10 }, memory: { usePercent: 60, use: 72, total: 125 }, disc: { totalUsePercent: 40, totalUse: 27826, total: 63911 }, status: true },
				],
				columns: [
					{ type: 'selection', width: 70, align: 'center' },
					{ title: '编号', align: 'center', width: 90, type: 'index', key: 'index' },
					{ title: '主机名称', key: 'serverName', },
					{ title: '主机IP', key: 'ip', },
					{ title: '操作系统', slot: 'system', width: 220, },
					{ title: 'CPU', slot: 'cpu', },
					{ title: '内存', slot: 'memory', },
					{ title: '硬盘', slot: 'disc', },
					{ title: '连接状态', slot: 'status', },
					{ title: '操作', slot: 'action', width: 100 }

				],
				pageInfo: {
					pageNumber: 1,
					pageSize: 20,
				},
				total: 0,
				loading: false,
				selectionList: []
			}
		},
		mounted() {
			this.queryList()
		},
		methods: {
			//查询主机在线状况
			getStatusStatistics() {
				statusStatistics({}).then(res => {
					if (res.code == 200) {
						this.topCardList[0].total = res.data.count
						this.topCardList[1].total = res.data.onlineCount
						this.topCardList[2].total = res.data.offlineCount
					}
				})
			},
			//查询服务器列表
			queryList() {
				this.getStatusStatistics()

				let formData = this.$refs.searchMf.formData
				this.queryParam = { ...this.queryParam, ...formData }
				this.loading = true;
				this.tableList = []
				pageList({ ...this.queryParam, ...this.pageInfo })
					.then(res => {
						const { total, entities } = res.data
						this.total = total
						this.tableList = entities
					})
					.catch(err => {
						console.error(err)
					})
					.finally(() => {
						this.loading = false;
					})
			},
			searchInfo(obj) {
				this.pageInfo.pageNumber = 1
				this.queryParam = obj
				this.queryList()
			},
			// 页数改变
			pageChange(size) {
				this.pageInfo.pageNumber = size
				this.queryList()
			},
			// 页数量改变
			pageSizeChange(size) {
				this.pageInfo.pageNumber = 1
				this.pageInfo.pageSize = size
				this.queryList()
			},
			selectionChangeHandle(val) {
				this.selectionList = val
			},
			// 新增
			add() {
				this.$refs.addModal.show()
			},
			// 编辑
			edit(row) {
				this.$refs.addModal.show(row)
			},
			// 删除
			remove(row) {
				this.$Modal.confirm({
					title: '提示',
					closable: true,
					content: `确定删除吗？`,
					onOk: () => {
						deleteMf(row.id).then(res => {
							this.queryList()
							this.$Message.success(res.msg)
						})
					}
				})
			},
			removeMore() {
				if (this.selectionList.length > 0) {
					let ids = []
					this.selectionList.forEach(item => {
						ids.push(item.id)
					})
					this.$Modal.confirm({
						title: '提示',
						closable: true,
						content: `确定删除吗？`,
						onOk: () => {
							deleteMf(ids.toString()).then(res => {
								this.queryList()
								this.$Message.success(res.msg)
							})
						}
					})
				} else {
					this.$Message.warning("请选择要删除的主机！");
				}
			}
		}
	}
</script>
<style lang="less" scoped>
	.top-box {
		height: 100px;
		display: flex;
		margin-bottom: 20px;
		margin-top: 1px;
		.top-card {
			flex: 1;
			height: 100%;
			margin-right: 20px;
			border-radius: 4px;
			font-size: 16px;
			color: #fff;
			padding: 20px;
			position: relative;
			img {
				position: absolute;
				right: 20px;
				top: 20px;
				width: 60px;
			}
			.num {
				font-size: 32px;
				font-weight: bold;
			}
			.bg_img {
				width: 100%;
				height: 100%;
				top: 0;
				right: 0;
			}
		}
		.top-card:last-child {
			margin-right: 0;
		}
		.bg1 {
			background: linear-gradient(315deg, #39a1fc 0%, #1c6df4 100%);
			box-shadow: 0 5px 10px 0 rgba(54, 151, 251, 0.5);
		}
		.bg2 {
			background: linear-gradient(141deg, #1ca884 0%, #37d9af 100%);
			box-shadow: 0 5px 10px 0 rgba(34, 180, 143, 0.5);
		}
		.bg3 {
			background: linear-gradient(225deg, #c1c7d2 0%, #808da7 100%);
			box-shadow: 0 5px 10px 0 rgba(133, 145, 171, 0.5);
		}
	}
	.search-bar {
		position: relative;
		.btn-group {
			position: absolute;
			top: 0;
			right: 0;
		}
	}
	.main-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		width: calc(100% - 50px);
		background: #fff;
		padding: 20px 20px 0 20px;
		border-radius: 4px;
		box-shadow: 0 2px 3px 0 rgba(147, 171, 206, 0.7);

		.table-container {
			display: flex;
			flex-direction: column;
			flex: 1;
			overflow: auto;
			position: relative;
			.table-content {
				overflow: auto;
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				justify-content: start;
				// justify-content: space-between;
				align-content: flex-start;
				.ui-table {
					height: 100%;
				}
			}
		}
	}
	.color_f9 {
		color: rgba(0, 0, 0, 0.5);
	}
</style>