/**
 * 1.待指派
 * 2.指派过后 -> 待签收
 * 3.签收过后 -> 进行中(派发下去处理异常了)
 * 4.进行中 -> 如果按时处理就是 ->已完成
 *          -> 如果未按时处理就是 ->已延期
 * 5.检测不通过就直接打回 -> 待签收
 * 6.已完成 -> 可以关闭
 */
export const ALLORDER = 1;
export const MYORDER = 2;

export const UNIT_CREATION = '1'; // 单位创建
export const ASSIGN_UNIT = '2'; //指派给单位

export const deviceTypeList = [
  {
    label: '全部工单',
    value: 1,
  },
  // {
  //   label: '我的工单',
  //   value: 2,
  //   permission: 'myorder',
  // },
];
export const tagList = [
  { label: '单位统计', id: '1', component: 'unitstatistics', permission: 'unitstatistics' },
  { label: '类型统计', id: '2', component: 'typedetails', permission: 'typedetails' },
];
export const unitWorkOrderTagList = [
  { label: '任务模式', id: '4', component: 'all-task-order' },
  { label: '工单模式', id: '3', component: 'all-work-order' },
];

export const stateOptionsMap = {
  //单位创建
  1: [
    {
      label: '全部',
      value: 'qb',
      total: 0,
    },
    {
      label: '未指派',
      value: 'wzp',
      total: 0,
    },
    {
      label: '待签收',
      value: 'dqs',
      total: 0,
    },
    {
      label: '已签收',
      value: 'yqs',
      total: 0,
    },
    {
      label: '超时未签收',
      value: 'cswqs',
      total: 0,
    },
    {
      label: '超时处理',
      value: 'cscl',
      total: 0,
    },
    {
      label: '待处理',
      value: 'dcl',
      total: 0,
    },
    {
      label: '超时未处理',
      value: 'cswcl',
      total: 0,
    },
    // {
    //   label: '按时处理',
    //   value: 'ascl',
    //   total: 0,
    // },
    {
      label: '检测不通过',
      value: 'jcbtg',
      total: 0,
    },
    {
      label: '超时签收',
      value: 'csqs',
      total: 0,
    },
    {
      label: '按时签收',
      value: 'asqs',
      total: 0,
    },
    {
      label: '已处理',
      value: 'ycl',
      total: 0,
    },
    // {
    //   label: '一次处理通过',
    //   value: 'yccltg',
    //   total: 0,
    // },
    {
      label: '多次处理通过',
      value: 'dccltg',
      total: 0,
    },
    {
      label: '待关闭',
      value: 'dgb',
      total: 0,
    },
    {
      label: '已关闭',
      value: 'ygb',
      total: 0,
    },
    {
      label: '已报备',
      value: 'ybb',
      total: 0,
    },
    {
      label: '多次下发',
      value: 'dcxf',
      total: 0,
    },
  ],
  //指派给单位
  2: [
    {
      label: '全部',
      value: 'qb',
      total: 0,
    },
    {
      label: '待签收',
      value: 'dqs',
      total: 0,
    },
    {
      label: '超时未签收',
      value: 'cswqs',
      total: 0,
    },
    {
      label: '待处理',
      value: 'dcl',
      total: 0,
    },
    {
      label: '超时未处理',
      value: 'cswcl',
      total: 0,
    },
    {
      label: '检测不通过',
      value: 'jcbtg',
      total: 0,
    },
    {
      label: '超时签收',
      value: 'csqs',
      total: 0,
    },
    {
      label: '已处理',
      value: 'ycl',
      total: 0,
    },
    {
      label: '超时处理',
      value: 'cscl',
      total: 0,
    },
    {
      label: '多次处理通过',
      value: 'dccltg',
      total: 0,
    },
    {
      label: '待关闭',
      value: 'dgb',
      total: 0,
    },
    {
      label: '已关闭',
      value: 'ygb',
      total: 0,
    },
    {
      label: '已报备',
      value: 'ybb',
      total: 0,
    },
    {
      label: '多次下发',
      value: 'dcxf',
      total: 0,
    },
  ],
  //当前指派给我
  3: [
    {
      label: '全部',
      value: 'qb',
      total: 0,
    },
    {
      label: '待签收',
      value: 'dqs',
      total: 0,
    },
    {
      label: '超时未签收',
      value: 'cswqs',
      total: 0,
    },
    {
      label: '待处理',
      value: 'dcl',
      total: 0,
    },
    {
      label: '超时未处理',
      value: 'cswcl',
      total: 0,
    },
    {
      label: '检测不通过',
      value: 'jcbtg',
      total: 0,
    },
    {
      label: '已报备',
      value: 'ybb',
      total: 0,
    },
  ],
  //我创建的
  4: [
    {
      label: '全部',
      value: 'qb',
      total: 0,
    },
    {
      label: '未指派',
      value: 'wzp',
      total: 0,
    },
    {
      label: '待签收',
      value: 'dqs',
      total: 0,
    },
    {
      label: '超时未签收',
      value: 'cswqs',
      total: 0,
    },
    {
      label: '待处理',
      value: 'dcl',
      total: 0,
    },
    {
      label: '超时未处理',
      value: 'cswcl',
      total: 0,
    },
    {
      label: '检测不通过',
      value: 'jcbtg',
      total: 0,
    },
    {
      label: '超时签收',
      value: 'csqs',
      total: 0,
    },
    {
      label: '已处理',
      value: 'ycl',
      total: 0,
    },
    {
      label: '超时处理',
      value: 'cscl',
      total: 0,
    },
    {
      label: '多次处理通过',
      value: 'dccltg',
      total: 0,
    },
    {
      label: '待关闭',
      value: 'dgb',
      total: 0,
    },
    {
      label: '已关闭',
      value: 'ygb',
      total: 0,
    },
    {
      label: '已报备',
      value: 'ybb',
      total: 0,
    },
  ],
  //我签收的
  5: [
    {
      label: '全部',
      value: 'qb',
      total: 0,
    },
    {
      label: '超时签收',
      value: 'csqs',
      total: 0,
    },
    {
      label: '按时签收',
      value: 'asqs',
      total: 0,
    },
  ],
  //我处理的
  6: [
    {
      label: '全部',
      value: 'qb',
      total: 0,
    },
    {
      label: '超时处理',
      value: 'cscl',
      total: 0,
    },
    // {
    //   label: '按时处理',
    //   value: 'ascl',
    //   total: 0,
    // },
    // {
    //   label: '一次处理通过',
    //   value: 'yccltg',
    //   total: 0,
    // },
    {
      label: '多次处理通过',
      value: 'dccltg',
      total: 0,
    },
  ],
  //我关闭的
  7: [],
};
export const TASKSTATUS = {
  未指派: 0,
  已关闭: 1,
  进行中: 2,
  已完成: 3, // 已完成之前都算没有处理
  检测不通过: 4,
  待签收: 5,
  已延期: 11,
};
export const TASKSTATUSCOLOR = {
  0: 'var(--color-weizhipai)',
  1: 'var(--color-yiguanbi)',
  2: 'var(--color-jinxingzhong)',
  3: 'var(--color-success)', // 已完成之前都算没有处理
  4: 'var(--color-failed)',
  5: 'var(--color-daiqianshou)',
  11: 'var(--color-warning)',
};
export const TASKSTATUSCOLOR_REPLENISH = {
  5: 'var(--color-failed)', //待签收
  3: 'var(--color-warning)', //已完成且需等待系统检测
};
export const abnormalList = [
  {
    key: 'total',
    name: '工单总量',
    value: 0,
    icon: 'icon-gongdanzongliang',
    iconColor: 'icon-bg1',
    liBg: 'li-bg1',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color1',
  },
  {
    key: 'completeAmount',
    name: '按时完成工单数量',
    value: 0,
    icon: 'icon-anshiwanchenggongdanzongshu',
    iconColor: 'icon-bg2',
    liBg: 'li-bg2',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color2',
  },
  {
    key: 'delayAmount',
    name: '延期完成工单数量',
    value: 0,
    icon: 'icon-yanqiwanchenggongdanshuliang',
    iconColor: 'icon-bg6',
    liBg: 'li-bg6',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color6',
  },
  {
    key: 'delaySignAmount',
    name: '延期签收工单数量',
    value: 0,
    icon: 'icon-yanqiqianshougongdanshuliang',
    iconColor: 'icon-bg3',
    liBg: 'li-bg3',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color3',
  },
  {
    key: 'processingAgainAmount',
    name: '多次处理未完成',
    value: 0,
    icon: 'icon-duocichuliweiwancheng',
    iconColor: 'icon-bg4',
    liBg: 'li-bg4',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color4',
  },
  {
    key: 'completeAgainAmount',
    name: '多次处理已完成',
    value: 0,
    icon: 'icon-duocichuliyiwancheng',
    iconColor: 'icon-bg11',
    liBg: 'li-bg11',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color11',
  },
];
export const tableColumns = (showKeys = []) => {
  return [
    {
      type: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left',
      isShow: true,
    },
    {
      title: '序号',
      type: 'index',
      width: 50,
      align: 'center',
      isShow: true,
    },
    {
      width: 120,
      title: '工单编号',
      key: 'workOrderNum',
      align: 'left',
      tooltip: true,
      isShow: showKeys.includes('workOrderNum'),
    },
    {
      minWidth: 320,
      title: '工单名称',
      key: 'workOrderName',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('workOrderName'),
    },
    {
      minWidth: 130,
      title: '关联指标',
      key: 'indexName',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('indexName'),
    },
    {
      minWidth: 120,
      title: '设备组织机构',
      key: 'orgName',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('orgName'),
    },
    {
      minWidth: 120,
      title: '设备所属项目',
      key: 'belongProject',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('belongProject'),
    },
    {
      minWidth: 120,
      title: '设备IP地址',
      key: 'ipAddr',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('ipAddr'),
    },
    {
      minWidth: 120,
      title: '设备承建单位',
      key: 'cjdw',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('cjdw'),
    },
    {
      minWidth: 120,
      title: '设备维护单位',
      key: 'whdw',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('whdw'),
    },
    {
      minWidth: 120,
      title: '处理异常',
      key: 'errorReason',
      tooltip: true,
      align: 'left',
      isShow: showKeys.includes('errorReason'),
    },
    {
      width: 110,
      title: '状态',
      key: 'queryConditionStatus',
      slot: 'queryConditionStatus',
      align: 'left',
      isShow: showKeys.includes('queryConditionStatus'),
    },
    {
      width: 110,
      title: '紧急程度',
      key: 'workLevel',
      slot: 'workLevel',
      align: 'left',
      isShow: showKeys.includes('workLevel'),
    },
    {
      width: 100,
      title: '指派人',
      key: 'assignName',
      align: 'left',
      isShow: showKeys.includes('assignName'),
    },
    {
      width: 110,
      title: '指派给',
      key: 'currentTaskReceiverName',
      align: 'left',
      isShow: showKeys.includes('currentTaskReceiverName'),
    },
    {
      width: 150,
      title: '指派时间',
      key: 'assignDate',
      align: 'left',
      isShow: showKeys.includes('assignDate'),
    },
    {
      width: 150,
      title: '签收时间',
      key: 'signDate',
      align: 'left',
      isShow: showKeys.includes('signDate'),
    },
    {
      width: 120,
      title: '创建人',
      key: 'createName',
      align: 'left',
      isShow: showKeys.includes('createName'),
    },
    {
      width: 150,
      title: '截止时间',
      key: 'taskPlannedDate',
      align: 'left',
      isShow: showKeys.includes('taskPlannedDate'),
    },
    {
      width: 150,
      title: '创建时间',
      key: 'createTime',
      align: 'left',
      isShow: showKeys.includes('createTime'),
    },
    {
      width: 135,
      title: '操作',
      slot: 'action',
      align: 'center',
      fixed: 'right',
      className: 'table-action-padding',
      isShow: true,
    },
  ];
};
export const modelColumns = [
  {
    title: '序号',
    type: 'index',
    width: 50,
    align: 'center',
  },
  {
    width: 180,
    title: '工单编号',
    key: 'workOrderNum',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 200,
    title: '工单名称',
    key: 'workOrderName',
    tooltip: true,
    align: 'left',
  },
  {
    minWidth: 80,
    title: '处理异常',
    key: 'errorReason',
    tooltip: true,
    align: 'left',
  },
  {
    width: 110,
    title: '创建人',
    key: 'senderName',
    align: 'left',
  },
  {
    width: 150,
    title: '创建时间',
    key: 'createTime',
    align: 'left',
  },
  // {
  //   title: '操作',
  //   slot: 'action',
  //   width: 90,
  //   align: 'center',
  //   className: 'table-action-padding',
  // }, // 操作栏列-单元格padding设置
];
export function tableColumnsUnitstatistics(showKeys = [], _this) {
  let that = _this || this;
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
      isShow: true,
    },
    {
      title: '组织机构',
      key: 'orgName',
      align: 'left',
      tooltip: true,
      minWidth: 180,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click-org', row, column, index);
            }}
          >
            {row[column.key]}
          </span>
        );
      },
    },
    {
      title: '工单总量',
      key: 'total',
      queryConditionStatus: 'qb',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '未指派',
      key: 'wzpAmount',
      queryConditionStatus: 'wzp',
      align: 'center',
      tooltip: true,
      minWidth: 80,
      isShow: showKeys.includes('wzpAmount') || false,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '工单签收',
      align: 'center',
      key: 'gdqs',
      children: [
        {
          title: '待签收',
          key: 'dqsAmount',
          queryConditionStatus: 'dqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('dqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-warning'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时未签收',
          key: 'cswqsAmount',
          queryConditionStatus: 'cswqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('cswqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-error'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '已签收',
          key: 'yqsAmount',
          queryConditionStatus: 'yqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('yqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时签收',
          key: 'csqsAmount',
          queryConditionStatus: 'csqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('csqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '工单处理',
      align: 'center',
      key: 'gdcl',
      children: [
        {
          title: '待处理',
          key: 'dclAmount',
          queryConditionStatus: 'dcl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('dclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-warning'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时未处理',
          key: 'cswclAmount',
          queryConditionStatus: 'cswcl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('cswclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-error'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '已处理',
          key: 'yclAmount',
          queryConditionStatus: 'ycl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('yclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时处理',
          key: 'csclAmount',
          queryConditionStatus: 'cscl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('csclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '检测不通过',
          key: 'jcbtgAmount',
          queryConditionStatus: 'jcbtg',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('jcbtgAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-error'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '多次处理通过',
          key: 'dccltgAmount',
          queryConditionStatus: 'dccltg',
          align: 'center',
          tooltip: true,
          minWidth: 120,
          sortable: true,
          isShow: showKeys.includes('dccltgAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '工单关闭',
      align: 'center',
      key: 'gdgb',
      children: [
        {
          title: '待关闭',
          key: 'dgbAmount',
          queryConditionStatus: 'dgb',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('dgbAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '已关闭',
          key: 'ygbAmount',
          queryConditionStatus: 'ygb',
          align: 'center',
          tooltip: true,
          minWidth: 120,
          sortable: true,
          isShow: showKeys.includes('ygbAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '已报备',
      key: 'ybbAmount',
      queryConditionStatus: 'ybb',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: showKeys.includes('ybbAmount'),
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '完成率',
      key: 'rate',
      queryConditionStatus: 'rate',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: showKeys.includes('rate'),
    },
    {
      title: '及时完成率',
      key: 'jsRate',
      queryConditionStatus: 'jsRate',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: showKeys.includes('jsRate'),
    },
    {
      title: '多次下发',
      key: 'dcxfAmount',
      queryConditionStatus: 'dcxf',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
  ];
}
export function tableColumnsTypedetails(showKeys = [], _this) {
  let that = _this || this;
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
      isShow: true,
    },
    {
      title: '工单类型',
      key: 'indexModuleName',
      align: 'left',
      tooltip: true,
      minWidth: 130,
      isShow: true,
    },
    {
      title: '二级分类',
      key: 'indexName',
      align: 'left',
      tooltip: true,
      minWidth: 140,
      isShow: true,
    },
    {
      title: '工单总量',
      key: 'total',
      queryConditionStatus: 'qb',
      align: 'center',
      tooltip: true,
      minWidth: 120,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '未指派',
      key: 'wzpAmount',
      queryConditionStatus: 'wzp',
      align: 'center',
      tooltip: true,
      minWidth: 80,
      isShow: showKeys.includes('wzpAmount') || false,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '工单签收',
      align: 'center',
      key: 'gdqs',
      children: [
        {
          title: '待签收',
          key: 'dqsAmount',
          queryConditionStatus: 'dqs',
          align: 'center',
          tooltip: true,
          minWidth: 120,
          sortable: true,
          isShow: showKeys.includes('dqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-warning'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时未签收',
          key: 'cswqsAmount',
          queryConditionStatus: 'cswqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('cswqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-error'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '已签收',
          key: 'yqsAmount',
          queryConditionStatus: 'yqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('yqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时签收',
          key: 'csqsAmount',
          queryConditionStatus: 'csqs',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('yqsAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '工单处理',
      align: 'center',
      key: 'gdcl',
      children: [
        {
          title: '待处理',
          key: 'dclAmount',
          queryConditionStatus: 'dcl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('dclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-warning'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时未处理',
          key: 'cswclAmount',
          queryConditionStatus: 'cswcl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('cswclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-error'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '已处理',
          key: 'yclAmount',
          queryConditionStatus: 'ycl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('yclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '超时处理',
          key: 'csclAmount',
          queryConditionStatus: 'cscl',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('csclAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '检测不通过',
          key: 'jcbtgAmount',
          queryConditionStatus: 'jcbtg',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('jcbtgAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link-error'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '多次处理通过',
          key: 'dccltgAmount',
          queryConditionStatus: 'dccltg',
          align: 'center',
          tooltip: true,
          minWidth: 120,
          sortable: true,
          isShow: showKeys.includes('dccltgAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '工单关闭',
      align: 'center',
      key: 'gdgb',
      children: [
        {
          title: '待关闭',
          key: 'dgbAmount',
          queryConditionStatus: 'dgb',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('dgbAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
        {
          title: '已关闭',
          key: 'ygbAmount',
          queryConditionStatus: 'ygb',
          align: 'center',
          tooltip: true,
          minWidth: 110,
          sortable: true,
          isShow: showKeys.includes('ygbAmount'),
          render: (h, { row, index, column }) => {
            return (
              <span
                class={'link'}
                onClick={() => {
                  that.$emit('click', row, column, index);
                }}
              >
                {row[column.key] || 0}
              </span>
            );
          },
        },
      ],
    },
    {
      title: '已报备',
      key: 'ybbAmount',
      queryConditionStatus: 'ybb',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: showKeys.includes('ybbAmount'),
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              that.$emit('click', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '完成率',
      key: 'rate',
      queryConditionStatus: 'rate',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: showKeys.includes('rate'),
    },
    {
      title: '及时完成率',
      key: 'jsRate',
      queryConditionStatus: 'jsRate',
      align: 'center',
      tooltip: true,
      minWidth: 100,
      isShow: showKeys.includes('jsRate'),
    },
  ];
}
/**
 * @param indexModeule 对应 work_order_index_type
 * 治理工单-工单类型保留四项: 《1、基础教据处理、2、人脸教据处理 3、车辆数据处理 4、视频流数据处理) -选择指标保留项:(
 * 1、基础教据处理-填报准确率
 * 2、人脸数据处理。人脸卡口设备图片url、人脸卡口设备及时上传率、人脸卡口设备时钟准确率
 * 3、车辆教据处理。车辆卡口设备图片url、车辆卡口设备及时上传率、车辆卡口设备时钟准确率
 * 4、视频流数据处理.普通、重点实时可调阅率、普通、重点历史可调阅率、普通、重点时钟准确率、普通、重点宇募标注合规性)，其余全部删除
 *
 */
export const filterIndexType = [
  'BASIC_ACCURACY',
  'FACE_URL_AVAILABLE','FACE_FOCUS_URL_AVAILABLE','FACE_CAPTURE_PASS','FACE_UPLOAD','FACE_FOCUS_UPLOAD','FACE_CLOCK','FACE_ONLINE_RATE','FACE_CAPTURE_RATIONALITY',
  'VEHICLE_FULL_INFO','VEHICLE_FULL_INFO_IMPORTANT','VEHICLE_INFO_PASS','VEHICLE_CLOCK','VEHICLE_MAIN_PROP','VEHICLE_TYPE_PROP','VEHICLE_UPLOAD','VEHICLE_UPLOAD_IMPORTANT',
  'VEHICLE_URL_AVAILABLE','VEHICLE_URL_AVAILABLE_IMPORTANT','VEHICLE_ONLINE_RATE','VEHICLE_CAPTURE_RATIONALITY',
  'VIDEO_PLAYING_ACCURACY','VIDEO_HISTORY_ACCURACY','VIDEO_OSD_ACCURACY','VIDEO_CLOCK_ACCURACY','VIDEO_GENERAL_CLOCK_ACCURACY','VIDEO_GENERAL_OSD_ACCURACY','VIDEO_GENERAL_HISTORY_ACCURACY','VIDEO_GENERAL_PLAYING_ACCURACY'
]
//组件名称:batchType处理类型
export const BATCH_TYPE_ENUM = {
  Assign: 'assign',
  Deal: 'deal',
  Close: 'close',
  Sign: 'sign',
  Delete: 'delete',
  Report: 'report',
  SignCancel: 'signCancel', //签收撤回
  AssignCancel: 'assignCancel', //指派撤回
};

export const workLevelOptions = [
  { label: '一级', value: '1', style: 'one-leve' },
  { label: '二级', value: '2', style: 'two-leve' },
  { label: '三级', value: '3', style: 'three-leve' },
  { label: '四级', value: '4', style: 'four-leve' },
];

/**
 * 自动指派维护单位人
 * @type {number}
 */
export const AUTO_ASSIGN_UNIT = 1;

/**
 * 自动指派组织机构人
 * @type {number}
 */
export const AUTO_ASSIGN_ORG = 2;

export const ASSIGN_MODE_LIST = [
  { label: '自动指派维护单位人', value: AUTO_ASSIGN_UNIT },
  { label: '自动指派组织机构人', value: AUTO_ASSIGN_ORG },
];

/**
 * 自动生成
 * @type {number}
 */
export const AUTO_CREATE = 1;
/**
 * 不自动生成
 * @type {number}
 */
export const UN_AUTO_CREATE = 0;

/**
 * 自动指派
 * @type {number}
 */
export const AUTO_ASSIGN = 1;
/**
 * 自动指派
 * @type {number}
 */
export const UN_AUTO_ASSIGN = 0;
