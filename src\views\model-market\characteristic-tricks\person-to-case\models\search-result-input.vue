<template>
  <div class="search-result-input">
    <ui-modal width="80%" v-model="modalShow" title="查询结果" @onOk="onOk">
      <div class="header">
        <div class="search-content">
          <div class="input-content">
            <!-- 人员下拉框 -->
            <div class="people-selectDown-box">
              <Select
                v-model="peopleSelect"
                default-label="1"
                style="width: 200px"
              >
                <Option
                  v-for="(item, index) of peopleList"
                  :value="item.id"
                  :key="index"
                  >{{ item.type }}</Option
                >
              </Select>
            </div>
            <Dropdown
              trigger="custom"
              :visible="visible"
              placement="bottom-start"
            >
              <div
                class="pic-box"
                @click="pictureSearchHandle"
                v-if="previewImgList.length > 0"
              >
                <p
                  class="img-preview-content"
                  @click="pictureSearchHandle"
                  v-if="previewImgList.length > 0"
                >
                  <span class="num">{{ previewImgList.length }}</span>
                  <img :src="previewImgList[0].fileUrl" alt="" />
                </p>
              </div>
              <Input
                v-model="picData.searchValue"
                :class="!!previewImgList.length ? 'special-padding' : ''"
                :placeholder="searchPlaceholder"
                class="search-input"
              >
                <i
                  class="iconfont icon-xiangji"
                  slot="suffix"
                  @click="pictureSearchHandle"
                ></i>
                <Button
                  type="primary"
                  class="search-btn"
                  :disabled="isImage"
                  slot="append"
                  @click="toOrdinarySearchHandle"
                  >搜索</Button
                >
              </Input>
              <DropdownMenu slot="list">
                <search-pictures
                  @cancel="closePictureModel"
                  :picData="picData"
                  @search="searchPictures"
                  @imgUrlChange="imgUrlChange"
                />
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
      <Row class="content">
        <!-- <Col :span="4" class="content-left">
        <div>全部实体</div>
        <p v-for="item in lableList" :key="item.Label" @click="lableClick(item)"
          :class="{ active: item.name === currentEntity }" v-if="item.count > 0">
          <span class="title">{{ item.nameCn }}</span>
          <span class="number">{{ item.count }}</span>
        </p>
        </Col> -->
        <Col :span="18" class="content-center auto-fill">
          <UiTablePage
            v-show="tableShow"
            ref="uiTablePage"
            :minusHeight="300"
            :columns="columns"
            :load-data="loadDataList"
            @on-select-cancel="selectCancel"
            @on-selection-change="onSelectionChange"
          >
            <template #photoUrl="{ row }">
              <ui-image class="tablePic" :src="row.photoUrl" alt="静态库" />
            </template>
            <template #icon="{ row }">
              <ui-image :src="row.icon" alt="静态库" />
            </template>
            <template #xbdm="{ row }">
              <span>{{ row.xbdm | commonFiltering(genderList) }}</span>
            </template>
            <!-- <template #photoUrl="{ row }">
            <ui-image :src="row.photoUrl" alt="静态库" />
          </template> -->
          </UiTablePage>
        </Col>
        <Col :span="6" class="content-right">
          <div class="selectContent">
            <div class="allcount">
              <span
                >已选：<span>{{ allSelectCount }}</span
                >个</span
              >
              <span class="delete" @click="clearSelect()">清空</span>
            </div>
            <!-- <Tag class="table-tag" color="primary" v-for="item in selection" :key="item.id"
              >{{ item.name }}
              <Icon type="md-close-circle" class="table-tag-icon" @click="deleteSelection(item)" />
            </Tag> -->
            <div class="selectList">
              <div v-for="(item, index) in selectLableList" :key="index">
                <div class="selectLabel" v-if="item.list.length > 0">
                  <div class="title">
                    {{ item.nameCn }}（{{ item.list.length }}）
                  </div>
                  <div class="list">
                    <div
                      class="item"
                      v-for="(i, $index) in item.list"
                      :key="$index"
                    >
                      <div class="img">
                        <ui-image
                          :src="
                            i.photos && i.photos[0]
                              ? i.photos[0].photoUrl
                              : i.icon
                          "
                          :defaultIcon="i.icon"
                          alt="图片"
                          viewer
                        />
                      </div>
                      <div class="name" :title="i.displayField">{{ i.xm }}</div>
                      <Icon
                        class="delPeople-icon"
                        type="ios-close"
                        @click="selectCancel({}, i)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="footer-raios">
            <RadioGroup v-model="searchData.maxDepth">
              <Radio label="1"> 一度关系 </Radio>
              <Radio label="2"> 二度关系 </Radio>
              <Radio label="3"> 三度关系 </Radio>
            </RadioGroup>
          </div> -->
          </div>
        </Col>
      </Row>
    </ui-modal>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import imgloading from "@/assets/img/car1.webp";
import relationship from "@/assets/img/relationship.png";
import relationshipleft from "@/assets/img/relationship-left.png";
import relationshipright from "@/assets/img/relationship-right.png";
import { getPersonPageList } from "@/api/realNameFile";

const defaultPicData = {
  searchValue: "",
  similarity: 80,
  urlList: [],
  algorithmType: 1,
};
export default {
  components: {
    UiTablePage: require("@/components/ui-table-page").default,
    SearchPictures:
      require("@/views/number-cube/components/search-pictures.vue").default,
  },
  props: {
    page: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Boolean,
      default: false,
    },
    keyWords: {
      type: String,
      default: "",
    },
    selectPeopleList: {
      type: Array,
      default: () => [],
    },
    limitJudgment: {
      type: Function,
      default: () => {},
    },
    maxPeopleNum: {
      type: Number,
    },
  },
  data() {
    return {
      picData: { ...defaultPicData },
      keyWords1: "",
      isImage: false, // 是否以图搜图
      visible: false,
      modalShow: false,
      tableShow: true,
      currentEntity: "real_name_archive", // 当前实体
      imgloading,
      relationship,
      relationshipleft,
      relationshipright,
      lableList: [],
      selectLableList: [],
      previewImgList: [],
      allSelectCount: 0,
      selection: [],
      tableData: [],
      columns: [
        { type: "selection", align: "center", width: 40 },
        { type: "index", width: 70, title: "序号", align: "center" },
        { title: "姓名", key: "xm", minWidth: 70 },
        { title: "性别", key: "xbdm", minWidth: 100, slot: "xbdm" },
        { title: "身份证号", key: "gmsfhm", minWidth: 140 },
        { title: "户籍地址", key: "hjdzDzmc", minWidth: 200 },
      ],
      peopleSelect: 1,
      peopleList: [
        { type: "实名档案", id: 1 },
        { type: "重点人档案", id: 3 },
      ],
      loadDataList: (parameter) => {
        const { searchValue, similarity, urlList } = this.picData;
        var arr = [];
        urlList.forEach((item) => {
          if (item != "") {
            arr.push(item.feature);
          }
        });
        var param = {
          keyWord: searchValue,
          dataType: this.peopleSelect, // 类型传入档案类型
          similarity: similarity / 100,
          features: arr,
        };
        return getPersonPageList(Object.assign(parameter, param)).then(
          (res) => {
            var obj = this.selectLableList.find((item) => {
              return item.id == this.currentEntity;
            });
            var list = [];
            if (obj) list = obj.list;
            this.tableData = res.data.entities;
            res.data.entities = res.data.entities.map((val) => {
              if (list.length > 0) {
                if (
                  list.find((i) => {
                    return i.archiveNo == val.archiveNo;
                  })
                )
                  val._checked = true;
              }
              // let obj = Object.assign(val.entity, val)
              val.id = val.archiveNo;
              // delete obj.entity
              return val;
            });
            return res.data;
          }
        );
      },
      searchPlaceholder: "请输入关键词检索，多个关键词请用空格隔开",
    };
  },
  computed: {
    ...mapGetters({
      genderList: "dictionary/getGenderList",
    }),
  },
  watch: {
    modalShow(val) {
      this.$emit("input", val);
    },
    value(val) {
      this.modalShow = val;
      if (val === true) {
        this.restFromData();
        this.selectLableList = [
          {
            id: this.currentEntity,
            name: this.currentEntity,
            list: [...this.selectPeopleList],
          },
        ];
        this.info();
      }
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    async info() {
      // this.allSelectCount = 0
      // this.selectLableList = []
      this.previewImgList = this.picData.urlList.filter((url) => {
        if (url.fileUrl) return url;
      });
      if (this.previewImgList.length == 0) {
        this.searchPlaceholder = "请输入关键词检索，多个关键词请用空格隔开";
      } else {
        this.searchPlaceholder = "点击上传图片";
      }
      this.setInputStatus(this.picData.urlList);
      this.$refs.uiTablePage.info();
    },

    imgUrlChange(list, urlList) {
      this.picData.searchValue = "";
      this.picData.urlList = urlList || this.picData.urlList;
      this.previewImgList = urlList.filter((url) => {
        if (url.fileUrl) return url;
      });
      if (this.previewImgList.length == 0) {
        this.searchPlaceholder = "请输入关键词检索，多个关键词请用空格隔开";
      } else {
        this.searchPlaceholder = "点击上传图片";
      }
      this.setInputStatus(urlList);
      // if (urlList.length == 0) {
      //   // 清空操作
      //   this.isImage = false
      // } else {
      //   // 添加图片
      //   this.setInputStatus(urlList)
      // }
    },
    /**
     * 设置输入框可用状态
     */
    setInputStatus(urlList) {
      var arr = urlList.filter((item) => {
        return item.fileUrl;
      });
      if (arr.length == 0) {
        this.isImage = false;
      } else {
        this.isImage = true;
      }
    },
    /**
     * 选中实体
     */
    lableClick(item) {
      this.$refs.uiTablePage.pageData.pageNumber = 1;
      this.currentEntity = item.name;
      this.picData.name = item.name;
      var columnList = JSON.parse(item.properties);
      // console.log('table key', columnList)
      this.columns = [
        { type: "selection", align: "center", width: 40 },
        { type: "index", width: 70, title: "序号", align: "center" },
      ];
      columnList.forEach((item) => {
        if (["photoUrl", "icon"].includes(item.name)) {
          var obj = {
            title: item.nameCn,
            slot: item.name,
            minWidth: 200,
          };
        } else {
          var obj = {
            title: item.nameCn,
            key: item.name,
            minWidth: 200,
          };
        }
        this.columns.push(obj);
      });
      this.$forceUpdate();
      this.$nextTick(() => {
        this.$refs.uiTablePage.info();
      });
    },
    /**
     * 表格选中数据
     * @param {*} selection
     */
    onSelectionChange(selection) {
      this.selection = selection;
      this.selectLableList.forEach((item) => {
        if (item.name == this.currentEntity) {
          if (selection.length > 0) {
            selection.forEach((it) => {
              var obj = item.list.find((i) => i.id == it.id);
              if (!obj) item.list.push(it);
            });
          } else {
            this.tableData.forEach((it) => {
              item.list = item.list.filter((i) => i.id != it.id);
            });
          }
          return;
        }
      });
      this.refreshCount();
      this.$forceUpdate();
      setTimeout(() => {}, 200);
    },
    selectCancel(selection, row) {
      this.clearSelect(row);
    },
    refreshCount() {
      var count = 0;
      this.selectLableList.forEach((item) => {
        count += item.list.length;
      });
      this.allSelectCount = count;
    },
    /**
     * 清除已选
     */
    clearSelect(row) {
      if (row)
        this.selectLableList.forEach((item) => {
          if (item.name == this.currentEntity) {
            item.list = item.list.filter((i) => i.archiveNo != row.archiveNo);
          }
        });
      else
        this.selectLableList.forEach((item) => {
          item.list = [];
        });
      this.$refs.uiTablePage.info();
      this.refreshCount();
      this.$forceUpdate();
    },
    onOk() {
      var ids = [];
      this.selectLableList.forEach((item) => {
        item.list.forEach((i) => {
          ids.push(i);
        });
      });
      if (ids.length == 0) {
        this.$Message.warning("请选择人员");
        return;
      }
      if (ids.length > this.maxPeopleNum) {
        this.$Message.warning(`当前最多选择${this.maxPeopleNum}个人员`);
        return;
      }
      this.$emit("onSelectPeople", ids);
      this.modalShow = false;
    },
    pictureSearchHandle() {
      this.visible = !this.visible;
    },
    toOrdinarySearchHandle() {
      // if (this.picData.searchValue == '') {
      //   this.$Message.warning('请输入关键词')
      //   return
      // }
      this.$refs.uiTablePage.pageData.pageNumber = 1;
      this.$refs.uiTablePage.pageData.pageSize = 10;
      this.info();
    },
    searchPictures() {
      var arr = [];
      console.log("this.picData.urlList", this.picData.urlList);
      this.picData.urlList.forEach((item) => {
        if (item != "") {
          arr.push(item.feature);
        }
      });
      if (arr.length == 0) {
        this.$Message.warning("请上传图片以搜索");
        return;
      }
      this.searchPlaceholder = "点击上传图片";
      this.visible = false;
      this.info();
    },
    closePictureModel() {
      this.visible = false;
      this.searchPlaceholder = "请输入关键词检索，多个关键词请用空格隔开";
    },
    restFromData() {
      this.picData = { ...defaultPicData };
    },
  },
};
</script>
<style lang="less" scoped>
.input-content {
  position: relative;
  .people-selectDown-box {
    position: absolute;
    width: fit-content;
    left: 170px;
  }
  .pic-box {
    position: absolute;
    width: 498px;
    height: 38px;
    top: 0px;
    z-index: 101;
    cursor: pointer;
  }
}

.img-preview-content {
  //   position: absolute;
  width: 30px;
  height: 30px;
  //   top: 50%;
  margin-top: -15px;
  border: 1px solid #d3d7de;
  box-sizing: border-box;
  //   left: 5px;
  margin: 2px 0 0 3px;
  z-index: 101;
  cursor: pointer;

  .num {
    background-color: #2c86f8;
    color: #fff;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 25px;
    position: absolute;
    border-radius: 2px;
    transform: scale(0.5);
    left: -4px;
    top: -4px;
  }

  img {
    display: block;
    width: 100%;
    height: auto;
  }
}

.special-padding {
  /deep/.ivu-input {
    padding: 0 0 0 35px;
  }
}

.search-result-input {
  .content {
    height: 582px;
    width: 100%;
    border: 1px solid #d3d7de;
    margin-top: 20px;

    & > .content-left {
      border-right: 1px solid #d3d7de;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);

      & > div {
        border-bottom: 1px solid #d3d7de;
        font-weight: bold;
        height: 40px;
        line-height: 40px;
        padding-left: 16px;
      }

      & > p {
        padding-left: 16px;
        padding-right: 16px;
        cursor: pointer;
        height: 34px;
        line-height: 34px;

        .number {
          color: #2c86f8;
          float: right;
        }

        &:hover {
          background: #2c86f8;
          color: #fff;

          .number {
            color: #fff;
          }
        }
      }

      .active {
        background: #2c86f8;
        color: #fff;

        .number {
          color: #fff;
        }
      }
    }

    & > .content-center {
      border-right: 1px solid #d3d7de;
      padding: 10px;
      height: 600px;
    }

    & > .content-right {
      // display: flex;
      .selectContent {
        width: 100%;
        height: 582px;
        display: flex;
        flex-direction: column;

        & > .allcount {
          border-bottom: 1px solid #d3d7de;
          height: 40px;
          line-height: 40px;
          padding-left: 20px;
          padding-right: 10px;
          margin-bottom: 2px;

          & > .delete {
            float: right;
            cursor: pointer;
          }
        }
      }

      .table-tag {
        margin: 10px 0px 10px 10px;
        padding: 3px 10px;
        height: auto;
        line-height: normal;
        font-size: 14px;
        position: relative;
        overflow: inherit;
        cursor: pointer;

        &:hover {
          .table-tag-icon {
            display: block;
          }
        }

        .table-tag-icon {
          color: #ea4a36;
          position: absolute;
          top: -11px;
          right: -7px;
          font-size: 17px;
          display: none;
        }
      }

      .footer-raios {
        border-bottom: 0px;
        border-top: 1px solid #d3d7de;
        // position: absolute;
        // bottom: 0px;
        width: 100%;
        height: 40px;
        line-height: 40px;
        padding: 0 20px;

        /deep/ .ivu-radio-group {
          display: flex;
          justify-content: space-between;
        }
      }

      .selectList {
        flex: 1;
        overflow: auto;

        // padding-bottom: 50px;
        .selectLabel {
          flex: 1;

          .title {
            position: relative;
            color: #2c86f8;
            padding: 3px 38px;

            &:before {
              content: "";
              width: 8px;
              height: 8px;
              position: absolute;
              left: 20px;
              top: 10px;
              background: #2c86f8;
            }
          }

          .list {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;
            padding: 0 20px;

            .item {
              // width: 65px;
              width: 25%;
              padding: 0 10px;
              height: 80px;
              text-align: center;
              margin-bottom: 10px;
              position: relative;
              .delPeople-icon {
                position: absolute;
                font-size: 16px;
                right: 0;
                top: -7px;
                cursor: pointer;
                color: #fff;
                border-radius: 50%;
                background: red;
              }
              .img {
                width: 60px;
                height: 60px;
                // border: 1px solid #999;
                border-radius: 50%;

                img {
                  width: 100%;
                  height: 100%;
                }
              }

              .name {
                border: 1px solid #d3d7de;
                padding: 0 5px;
                border-radius: 3px;
                margin-top: 2px;
                background: #f9f9f9;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
  }

  .header {
    .search-content {
      text-align: center;

      /deep/ .ivu-dropdown {
        width: 570px;
      }

      /deep/ .ivu-input-suffix {
        right: 72px;
        display: flex;
        align-items: center;
        z-index: 100;
        width: 42px;
        height: 32px;
        top: 1px;
        background: #fff;
        cursor: pointer;

        .iconfont {
          font-size: 18px;
          color: #888;
          margin: 0 auto;
        }
      }

      /deep/ .header .ivu-icon-ios-close {
        top: 20px;
      }
    }
  }
}

.pl20 {
  padding-left: 20px;
}

.pr20 {
  padding-right: 20px;
}

.pr6 {
  padding-right: 6px;
}

.pr30 {
  padding-right: 30px;
}

.inlineb {
  display: inline-block;
}

.tablePic {
  overflow: hidden;
  border-radius: 0;
  width: 100px;
  height: 100px;
  border: 1px solid #d9e4f1;
}

/deep/ .pad10 {
  padding: 0 !important;
}
</style>
