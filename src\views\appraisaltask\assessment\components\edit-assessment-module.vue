<template>
  <div>
    <ui-modal v-model="visible" :title="modalTitle.title" :width="width">
      <Form ref="modalData" :rules="ruleValidate" :model="formData">
        <FormItem label="选择类型" class="right-item mb-0" prop="category">
          <Select
            class="select-width"
            v-model="formData.category"
            placeholder="请选择类型"
            :disabled="!formData.editType"
          >
            <Option :value="index" v-for="(item, index) in categoryList" :key="index">
              {{ item }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="考核内容" class="right-item mb-0" prop="name">
          <Input
            class="ipt-lg"
            v-model="formData.name"
            placeholder="请输入考核内容"
            :maxlength="20"
            :disabled="!formData.editType"
          >
          </Input>
        </FormItem>
        <FormItem label="分值" class="right-item mb-0" prop="score">
          <!-- 有总得分公式的 -->
          <template v-if="formData.scoreDesc">
            <span class="base-text-color">{{ formData.scoreDesc }}</span>
          </template>
          <!-- 由其他分项加起来总得分的 -->
          <template v-else>
            <InputNumber
              class="input-number-list"
              v-model="formData.score"
              :min="0"
              :precision="4"
              :active-change="false"
              :formatter="dealFloat"
              controls-outside
              placeholder="请输入分值"
            >
            </InputNumber>
            <span class="sorce">分</span>
            <span class="ml-sm color-failed">考核内容分值等于各分项分值之和</span>
          </template>
        </FormItem>
        <FormItem label="考核项" class="right-item mb-0" v-if="formData.addType">
          <Button class="ipt-lg" @click="addAssessment">
            <i class="icon-font icon-tianjia f-14" title="添加"> </i>
            <span>新增考核项</span>
          </Button>
        </FormItem>
        <div class="formbox">
          <div class="form" v-for="(item, index) in indexData" :key="index">
            <div class="form-name">
              <FormItem label="考核项" class="mb-0 name-item">
                <Input
                  class="ipt-lg"
                  type="text"
                  v-model="item.name"
                  placeholder="请输入考核项名称"
                  :maxlength="20"
                  :disabled="!item.editType"
                >
                </Input>
                <i
                  @click="show(index)"
                  :class="item.isShow ? 'icon-font icon-xialazhankai arrowrun' : 'icon-font icon-xialazhankai arrow'"
                ></i>
                <i
                  class="icon-font icon-piliangshanchu f-14 ipc icon-color"
                  title="删除"
                  v-if="item.editType"
                  @click="removeRow(index)"
                ></i>
              </FormItem>
            </div>
            <div v-show="item.isShow">
              <FormItem class="right-item mb-0">
                <RadioGroup
                  v-model="item.examItemType"
                  @on-change="
                    ($event) => {
                      onChangeExamItemType($event, item);
                    }
                  "
                >
                  <Radio :label="2">手动录入</Radio>
                  <Radio :label="1">系统考核</Radio>
                </RadioGroup>
              </FormItem>
              <!-- 所有指标判断逻辑根据每个考核项的第一个指标来判断 -->
              <FormItem v-if="item.examItemType !== 2" label="关联指标" class="right-item mb-0">
                <div v-for="(itm, ind) in item.examSchemeContentItemIndexVo" :key="ind" class="box-ipt">
                  <Select
                    filterable
                    class="index-width"
                    v-model="itm.indexId"
                    placeholder="请选择"
                    :disabled="!itm.editType"
                    @on-change="choseIndex(item, itm, ind)"
                  >
                    <Option v-for="(it, i) in indexListData" :value="it.id" :key="i">{{ it.indexName }} </Option>
                  </Select>
                  <Select class="yun-width" v-model="itm.indexOperator" placeholder="请选择">
                    <Option value="*">*</Option>
                  </Select>
                  <i
                    class="icon-font icon-piliangshanchu f-14 icon-color del-index"
                    title="删除"
                    @click="delIndex(item, ind)"
                    v-if="ind !== 0 && item.editType"
                  >
                  </i>
                  <i class="icon-font icon-tree-add icon-color ml-sm" @click="addIndex(index)" v-show="item.editType">
                  </i>
                  <RadioGroup
                    class="ml-sm"
                    v-model="itm.indexDataType"
                    v-show="isQuantityCompliance(item.examSchemeContentItemIndexVo[0].indexId)"
                  >
                    <Radio label="0">全部</Radio>
                    <Radio label="1">视频监控</Radio>
                    <Radio label="2">人脸卡口</Radio>
                    <Radio label="3">车辆卡口</Radio>
                  </RadioGroup>
                </div>
              </FormItem>
              <FormItem
                v-show="canChangedAssessmentMode(item.examSchemeContentItemIndexVo[0].indexId)"
                label="考核模式"
                class="right-item"
              >
                <Select
                  class="index-width"
                  placeholder="请选择"
                  v-model="item.examModel"
                  :disabled="isNumberOfCollection(item.examSchemeContentItemIndexVo[0].indexId)"
                  @on-change="changeExamModel($event, item)"
                >
                  <Option
                    v-for="(row, rowI) in getExamModelList(item.examSchemeContentItemIndexVo[0].indexId)"
                    :value="row.value"
                    :key="rowI"
                  >
                    {{ row.label }}
                  </Option>
                </Select>
                <!-- 
                  hasExcessScoreFlag
                  0 是考核基础达标
                  1 超额得分--基线的超额得分逻辑
                  2 超数量得分--用于判断海南指标的超额建设得分以及显示
                  3 加分项--用于判断海南的加分项指标以显示
                -->
                <!-- 当考试模式为基础模式时可以选择考核基础或者超额 -->
                <RadioGroup
                  class="ml-sm"
                  v-show="
                    item.examModel === '1' && showExamModelRadioGroup(item.examSchemeContentItemIndexVo[0].indexId)
                  "
                  v-model="item.hasExcessScoreFlag"
                  @on-change="changeExcessScoreFlag($event, item)"
                >
                  <Radio label="0">考核基础达标</Radio>
                  <Radio label="2">考核超额情况</Radio>
                </RadioGroup>
              </FormItem>
              <!-- 基线普通指标 -->
              <FormItem
                label="分项分值"
                class="right-item mb-0"
                v-if="item.hasExcessScoreFlag !== '2' && item.hasExcessScoreFlag !== '3'"
              >
                <InputNumber
                  class="input-number-list"
                  v-if="scoreType === '1'"
                  v-model.number="item.baseScoreValue"
                  :min="0"
                  :precision="4"
                  :active-change="false"
                  :formatter="dealFloat"
                  controls-outside
                  placeholder="请输入分值"
                  @on-change="changeBaseScoreValue(item)"
                >
                </InputNumber>
                <InputNumber
                  class="input-number-list"
                  v-if="scoreType === '2'"
                  v-model.number="item.baseScoreValue"
                  :min="0"
                  :max="100"
                  :precision="0"
                  :active-change="false"
                  controls-outside
                  placeholder="请输入百分比"
                  @on-change="changeBaseScoreValue(item)"
                >
                </InputNumber>
                <Select
                  class="select-width-ml"
                  v-model="scoreType"
                  placeholder="请选择单位"
                  @on-change="changeUnit(item)"
                >
                  <Option value="1">分</Option>
                  <Option value="2">%</Option>
                </Select>
              </FormItem>
              <!-- 海南加分项 -->
              <FormItem label="理论得分" class="right-item mb-0" v-if="item.hasExcessScoreFlag === '3'">
                <InputNumber
                  class="input-add-points"
                  v-model.number="item.baseScoreValue"
                  :min="0"
                  controls-outside
                  placeholder="请输入分值"
                >
                </InputNumber>
                <span class="ml-sm base-text-color">*{{ indexName }}/</span>
                <InputNumber
                  class="input-add-points ml-sm"
                  v-model.number="item.excessScoreCoefficient"
                  :min="0"
                  :max="100"
                  controls-outside
                  placeholder="请输入百分比"
                  :precision="0"
                  :active-change="false"
                >
                </InputNumber>
                <span class="ml-sm base-text-color">%</span>
              </FormItem>
              <FormItem
                label="实际得分"
                class="right-item mb-0"
                v-show="item.examItemType !== 2 && item.hasExcessScoreFlag === '0'"
              >
                <!-- 四川宜宾 -->
                <template
                  v-if="
                    siChuanYiBinAssessmentMode(item.examSchemeContentItemIndexVo[0].indexId) && item.examModel === '4'
                  "
                >
                  <span class="base-text-color">
                    -{floor
                    <span>{{ isHasThreeParams(item.examSchemeContentItemIndexVo[0].indexId) ? '[(' : '(' }}</span>
                    <template v-if="isHasThreeParams(item.examSchemeContentItemIndexVo[0].indexId)">
                      <InputNumber
                        class="input-number-list width-80"
                        v-model="item.baseStandard"
                        :min="0"
                        :precision="4"
                        :active-change="true"
                        :formatter="dealFloat"
                        controls-outside
                        placeholder="请输入"
                        @on-change="changeDesc(item)"
                      >
                      </InputNumber>
                      % -
                      {{ item.examSchemeContentItemIndexVo[0].indexName }}
                      )*100
                    </template>
                    <template v-if="isHistoricalVideoRate(item.examSchemeContentItemIndexVo[0].indexId)">
                      单设备累计录像缺失时长
                    </template>
                    <template v-if="isPlatformOnline(item.examSchemeContentItemIndexVo[0].indexId)">
                      离线时长
                    </template>
                    /
                    <InputNumber
                      class="input-number-list width-80"
                      v-model="item.offlineTime"
                      :min="0"
                      :precision="4"
                      :active-change="true"
                      :formatter="dealFloat"
                      controls-outside
                      placeholder="请输入"
                      @on-change="changeDesc(item)"
                    >
                    </InputNumber>
                    <span>{{ isHasThreeParams(item.examSchemeContentItemIndexVo[0].indexId) ? ']}' : ')}' }}</span>
                    *
                    <InputNumber
                      class="input-number-list width-80"
                      v-model="item.scoreDegree"
                      :min="0"
                      :precision="4"
                      :active-change="true"
                      :formatter="dealFloat"
                      controls-outside
                      placeholder="请输入"
                      @on-change="changeDesc(item)"
                    >
                    </InputNumber>
                  </span>
                </template>
                <!-- 有扣分系数的指标 -->
                <template v-else-if="hasDeductionFactor(item.examSchemeContentItemIndexVo[0].indexId)">
                  <span class="base-text-color">
                    <span>分项分值</span>
                    <span v-if="isPlatformOnline(item.examSchemeContentItemIndexVo[0].indexId)">
                      <span> *（1 - [ 本月离线时长 / </span>
                      <Input
                        class="width-xs"
                        placeholder="单位离线时长"
                        v-model="item.offlineTime"
                        @on-change="changeDesc(item)"
                      >
                      </Input>
                      <span> ] * </span>
                    </span>
                    <span v-else-if="isTrajectoryStability(item.examSchemeContentItemIndexVo[0].indexId)">
                      - 报备天数 *
                    </span>
                    <span v-else> - 不合格次数 * </span>
                  </span>
                  <Input
                    class="width-xs"
                    placeholder="请输入扣分系数"
                    v-model="item.scoreDegree"
                    @on-change="changeDesc(item)"
                  >
                  </Input>
                  <span class="base-text-color" v-show="isPlatformOnline(item.examSchemeContentItemIndexVo[0].indexId)">
                    ）
                  </span>
                </template>
                <!-- 无扣分系数的指标 -->
                <template v-else>
                  <!-- 四川考核 -->
                  <span v-if="item.examModel === '2'" class="ml-sm">
                    <span class="base-text-color">分项分值 * </span>
                    <Select class="width-lg" v-model="item.baseScoreType" placeholder="请选择">
                      <Option v-for="(row, rowI) in baseScoreTypeList" :value="row.value" :key="rowI">
                        {{ row.label }}
                      </Option>
                    </Select>
                  </span>
                  <!-- 北京考核 -->
                  <div v-else-if="item.examModel === '3'" class="base-text-color">
                    <span>{{ item.examFormula }}</span>
                    <div>
                      <Checkbox class="mr-lg">指标不达标得0分</Checkbox>
                      <span> 达标值 </span>
                      <Input class="width-mini mr-xs" placeholder="请输入" v-model="item.scoreDegree"> </Input>
                      <span>%</span>
                    </div>
                  </div>
                  <!-- 基础考核 -->
                  <span class="base-text-color" v-else>
                    <!-- 视频监控数量增长率 -->
                    <span v-if="isMonitorGrowthRate(item.examSchemeContentItemIndexVo[0].indexId)">
                      <span>总分 + α【（视频监控数量增长率 / β）】= 总分 + </span>
                      <Input
                        class="width-mini"
                        v-model="item.excessScoreCoefficient"
                        placeholder="请输入"
                        @on-change="changeDesc(item.examSchemeContentItemIndexVo[0].indexId)"
                      >
                      </Input>
                      <span>【（视频监控数量增长率 / </span>
                      <Input
                        class="width-mini"
                        v-model="item.scoreDegree"
                        placeholder="请输入"
                        @on-change="changeDesc(item.examSchemeContentItemIndexVo[0].indexId)"
                      >
                      </Input>
                      <span>）】</span>
                    </span>
                    <!-- 视频监控撤销情况 -->
                    <span v-else-if="isMonitoringCancellation(item.examSchemeContentItemIndexVo[0].indexId)">
                      <span>总分 - α * 视频监控撤销次数 - β * 视频监控撤销数量 / γ = 总分 - </span>
                      <Input
                        class="width-mini"
                        v-model="item.excessScoreCoefficient"
                        placeholder="请输入"
                        @on-change="changeDesc(item.examSchemeContentItemIndexVo[0].indexId)"
                      >
                      </Input>
                      <span> * 视频监控撤销次数 - </span>
                      <Input
                        class="width-mini"
                        v-model="item.scoreDegree"
                        placeholder="请输入"
                        @on-change="changeDesc(item.examSchemeContentItemIndexVo[0].indexId)"
                      >
                      </Input>
                      <span> * 视频监控撤销数量 / </span>
                      <Input
                        class="width-mini"
                        v-model="item.offlineTime"
                        placeholder="请输入"
                        @on-change="changeDesc(item.examSchemeContentItemIndexVo[0].indexId)"
                      >
                      </Input>
                    </span>
                    <!-- 视频监控撤销情况、视频监控撤销情况（人工复核） -->
                    <span v-else-if="isFocusOnCommandingImageQuality(item.examSchemeContentItemIndexVo[0].indexId)">
                      <span>分项分值 - α * 巡检不合格设备数量 = {{ item.baseScoreValue }} - </span>
                      <Input
                        class="width-mini"
                        v-model="item.scoreDegree"
                        placeholder="请输入"
                        @on-change="changeDesc(item.examSchemeContentItemIndexVo[0].indexId)"
                      >
                      </Input>
                      <span> * 巡检不合格设备数量</span>
                    </span>
                    <span v-else>{{ item.examFormula }}</span>
                  </span>
                </template>
              </FormItem>
              <FormItem
                v-if="item.examSchemeContentItemIndexVo.length === 1 && item.hasExcessScoreFlag === '2'"
                label="最大分值"
                class="right-item mb-0"
              >
                <InputNumber
                  class="input-number-list"
                  v-model.number="item.excessMaxScoreValue"
                  :min="0"
                  :precision="scoreType === '1' ? 4 : 0"
                  :active-change="false"
                  :placeholder="`请输入${scoreType === '1' ? '分值' : '百分比'}`"
                  :formatter="dealFloat"
                  controls-outside
                  @on-change="changeBaseScoreValue(item)"
                >
                </InputNumber>
                <Select class="select-width-ml" v-model="scoreType" placeholder="请选择" @on-change="changeUnit(item)">
                  <Option value="1">分</Option>
                  <Option value="2">%</Option>
                </Select>
              </FormItem>
              <!-- 超额情况 -->
              <FormItem label="实际得分" class="right-item mb-0" v-if="item.hasExcessScoreFlag === '2'">
                <!-- 视频图像采集区域数量达标率 超额情况 -->
                <div v-if="isNumberOfCollection(item.examSchemeContentItemIndexVo[0].indexId)">
                  <Input
                    class="ipt-ml"
                    type="text"
                    v-model.number="item.excessScoreCoefficient"
                    :maxlength="20"
                    placeholder="请输入分值"
                    @on-change="changeDesc(item)"
                  >
                  </Input>
                  <span class="remaker"
                    >*（重点采集区域视频监控推送数量/应推送数量-1）*（重点采集区域人脸卡口推送数量/应推送数量-1）*（重点采集区域车辆卡口推送数量/应推送数量-1）</span
                  >
                </div>
                <!-- 其他指标 超额情况 -->
                <template v-else>
                  <div v-for="(row, rindex) in item.extensionData" :key="rindex" class="extension">
                    <Input
                      class="ipt-ml"
                      type="text"
                      v-model.number="row.excessScoreCoefficient"
                      :maxlength="20"
                      placeholder="请输入分值"
                      @on-change="changeDesc(item)"
                    >
                    </Input>
                    <span class="remaker">*（实际建设总量/达标数量-1）</span>
                    <Button type="text" v-if="row.regionCodeList.length" @click="selectExtensionOrg(row, item)"
                      >已选择{{ row.regionCodeList.length }}个</Button
                    >
                    <Button type="primary" v-else @click="selectExtensionOrg(row, item)">适用对象</Button>
                    <i class="icon-font icon-tianjia f-14 font-blue ml-sm" title="添加" @click="addExtension(item)">
                    </i>
                    <i
                      v-if="rindex !== 0"
                      class="icon-font icon-piliangshanchu f-14 font-blue ml-sm"
                      title="删除"
                      @click="delExtension(item, rindex)"
                    >
                    </i>
                  </div>
                </template>
              </FormItem>
              <!-- 海南加分项 -->
              <FormItem label="最大得分" class="right-item mb-0" v-if="item.hasExcessScoreFlag === '3'">
                <span class="base-text-color">{{ formData.scoreDesc }}</span>
              </FormItem>
              <FormItem label="考核方式" class="right-item mb-0">
                <Select
                  class="select-width"
                  v-model="item.examMethodType"
                  placeholder="请选择"
                  @on-change="handleSwitch($event, index)"
                >
                  <Option :value="i.id" v-for="(i, k) in selectList" :key="k">
                    {{ i.label }}
                  </Option>
                </Select>
              </FormItem>
              <!-- 复选框显示条件: 系统考核(1) && 考核周期内取最新一次检测结果('1')-->
              <FormItem label="" class="right-item mb-0" v-if="item.examItemType === 1 && item.examMethodType === '1'">
                <Checkbox v-model="item.showAllResult" true-value="1" false-value="0"
                  >展示考核周期所有检测结果</Checkbox
                >
              </FormItem>
              <FormItem label="考核方法" class="right-item mb-0">
                <Input class="select-width" type="textarea" v-model="item.examWay"></Input>
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
      <template slot="footer">
        <Button type="primary" @click="query" :loading="saveLoading">保 存</Button>
      </template>
    </ui-modal>
    <org-modal
      v-model="orgModalShow"
      :tree-data="regionList"
      :default-checked-keys="defaultCheckedKeys"
      :default-props="{
        label: 'regionName',
        children: 'children',
      }"
      node-key="regionCode"
      modal-title="请选择行政区划"
      @query="queryOrg"
    >
    </org-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    modalTitle: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      visible: false,
      orgModalShow: false,
      saveLoading: false,
      width: 45,
      formData: {
        name: '',
        score: 0,
        category: '',
        editType: true,
        addType: true,
      },
      indexName: '',
      scoreType: '1', //单位
      indexData: [
        {
          name: '',
          isShow: true,
          baseScore: 0,
          baseScoreValue: 0,
          examModel: '1',
          hasExcessScoreFlag: '0',
          examSchemeContentItemIndexVo: [{ indexName: '', indexId: '', indexType: '', indexModule: '' }],
          excessScoreCoefficient: 0,
          scoreType: '1',
          examFormula: '',
          examItemType: 1,
          excessMaxScore: 0,
          excessMaxScoreValue: 0,
          scoreDegree: 0,
          offlineTime: 0,
          baseStandard: 0,
        },
      ],
      selectList: [
        { id: '2', label: '考核周期内取多次检测结果的平均值' },
        { id: '1', label: '考核周期内取最新一次检测结果' },
        { id: '3', label: '考核周期内随机取一次检测结果' },
        { id: '4', label: '考核周期内多次检测结果的累加值' },
      ],
      totalValue: '', // 分数之和
      indexListData: [],
      examModelList: [], //考核模式
      baseScoreTypeList: [], //得分类型
      idx: '',
      schemeId: '',
      ruleValidate: {
        name: [{ required: true, message: '考核内容不能为空', trigger: 'blur' }],
        score: [
          {
            required: true,
            message: '分值不能为空',
            type: 'number',
            trigger: 'blur',
          },
        ],
        category: [
          {
            required: true,
            message: '类型不能为空',
            type: 'string',
            trigger: 'change',
          },
        ],
      },
      categoryList: [],
      defaultCheckedKeys: [],
      selectedExtension: {},
      regionList: [],
    };
  },
  created() {
    this.setAreaList();
    this.getIndexList();
    this.getExamDictConfig();
    this.getType();
  },
  methods: {
    ...mapActions({
      setAreaList: 'common/setAreaList',
    }),
    onChangeExamItemType(val, item) {
      let defaultData = {
        isShow: true,
        editType: true,
        baseScore: 0,
        baseScoreValue: 0,
        examSchemeContentItemIndexVo: [
          {
            indexName: '',
            indexId: '',
            indexType: '',
            indexModule: '',
            indexOperator: '*',
            editType: true,
          },
        ],
        scoreType: this.scoreType,
        examFormula: '',
        examMethodType: '',
        examWay: '',
        examItemType: val,
        excessMaxScore: 0,
        excessMaxScoreValue: 0,
        excessScoreCoefficient: 0,
        showAllResult: '0', //展示考核周期所有检测结果
      };
      for (let defaultDataKey in defaultData) {
        this.$set(item, defaultDataKey, defaultData[defaultDataKey]);
      }
    },
    validateAssessmentItem() {
      return new Promise((resolve, reject) => {
        if (!this.indexData.length) {
          this.$Message.error('请输入考核项');
          return reject();
        }
        for (let i = 0; i < this.indexData.length; i++) {
          let item = this.indexData[i];
          if (!item.name) {
            this.$Message.error('请输入考核项');
            return reject();
          }
          // 四川宜宾模式下，才需要校验
          if (item.examModel === '4') {
            let indexId = item.examSchemeContentItemIndexVo[0].indexId;
            let flag1 =
              (this.isHistoricalVideoRate(indexId) || this.isPlatformOnline(indexId)) &&
              (!item.offlineTime || !item.scoreDegree);
            let flag2 =
              this.isHasThreeParams(indexId) && (!item.baseStandard || !item.offlineTime || !item.scoreDegree);
            if (flag1 || flag2) {
              this.$Message.error('请输入实际得分');
              return reject();
            }
          }
        }
        return resolve();
      });
    },
    async query() {
      try {
        await this.validateAssessmentItem();
        this.$refs['modalData'].validate(async (valid) => {
          try {
            if (valid) {
              let data = {};
              data.examSchemeContentVo = this.formData;
              for (let i of this.indexData) {
                /**
                 *  考核项为手动录入 即 i['examItemType'] === 2 时，
                 *  examSchemeContentItemIndex中的indexName 为考核项名称，即 i['name']
                 */
                if (i['examItemType'] === 2) {
                  i['examSchemeContentItemIndexVo'][0]['indexName'] = i['name'];
                }
              }
              data.list = this.$util.common.deepCopy(this.indexData);
              data.schemeId = this.schemeId;
              // 海南加分项不再计算总分是否正确
              if (!this.formData.scoreDesc) {
                let num = this.formData.score.toFixed(4);
                if (this.scoreType === '1' && Math.abs(this.totalValue - num) >= Number.EPSILON) {
                  this.$Message.error({
                    content: `各考核项分项分值与超额最大得分之和需等于${this.formData.score}分`,
                    duration: 5,
                  });
                  return;
                }
                if (this.scoreType === '2' && Math.abs(this.totalValue) !== 100) {
                  //百分比类型
                  this.$Message.error({
                    content: `各考核项分项分值与超额最大得分之和需等于${this.formData.score}分`,
                    duration: 5,
                  });
                  return;
                }
              }
              this.saveLoading = true;
              if (this.idx === 'add') {
                this.$refs.modalData.validate(async () => {
                  const res = await this.$http.post(governanceevaluation.saveSchemeContent, data);
                  this.$Message.success(res.data.msg);
                  this.visible = false;
                  this.$parent.refalsh();
                });
              } else {
                // 处理数据给java)
                for (let i of data.list) {
                  i.schemeId = data.examSchemeContentVo.schemeId;
                  i.schemeContentCode = data.examSchemeContentVo.schemeContentCode;
                  /**
                   * 后端数据格式为map对象
                   * 前端需要把数组处理为map对象给后端 后端为LiuF
                   */
                  if (i.hasExcessScoreFlag === '2' && i.extensionData) {
                    let tempObj = {};
                    i.extensionData.forEach((row) => {
                      row.regionCodeList.forEach((rw) => {
                        tempObj[rw] = Number(row.excessScoreCoefficient).toFixed(4);
                      });
                    });
                    i.extensionData = Object.keys(tempObj).length ? JSON.stringify(tempObj) : null;
                  }
                }
                const res = await this.$http.post(governanceevaluation.updateSchemeContent, data);
                this.$Message.success(res.data.msg);
                this.visible = false;
                this.$parent.refalsh();
              }
            }
          } catch (err) {
            console.log(err);
          } finally {
            this.saveLoading = false;
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
    addAssessment() {
      this.indexData.push({
        name: '',
        isShow: true,
        editType: true,
        baseScore: 0,
        baseScoreValue: 0,
        examModel: '1',
        hasExcessScoreFlag: '0',
        examSchemeContentItemIndexVo: [
          {
            indexName: '',
            indexId: '',
            indexType: '',
            indexModule: '',
            indexOperator: '*',
            editType: true,
          },
        ],
        scoreType: this.scoreType,
        scoreDesc: '',
        examFormula: '',
        examItemType: 1,
        excessMaxScore: 0,
        excessMaxScoreValue: 0,
        excessScoreCoefficient: 0,
        scoreDegree: 0,
        offlineTime: 0,
        baseStandard: 0,
      });
    },
    removeRow(index) {
      this.indexData.splice(index, 1);
    },
    addIndex(index) {
      if (this.indexData[index].examSchemeContentItemIndexVo.length >= 3) {
        this.$Message.error('最多关联三条指标');
        return;
      }
      this.indexData[index].examSchemeContentItemIndexVo.push({
        indexName: '',
        indexId: '',
        indexType: '',
        indexModule: '',
        indexOperator: '*',
        editType: true,
      });
    },
    delIndex(item, ind) {
      item.examSchemeContentItemIndexVo.splice(ind, 1);
      this.changeDesc(item);
    },
    choseIndex(item, itm, ind) {
      item.hasExcessScoreFlag = '0';
      // 四川宜宾 部分指标
      let indexId = item.examSchemeContentItemIndexVo[0].indexId;
      if (this.siChuanYiBinAssessmentMode(indexId)) {
        item.examModel = '4';
        // 设置 实际得分 的默认值
        ind === 0 ? this.setDefaultValue(indexId, item) : '';
      } else {
        item.examModel = '1';
      }
      if (item.examItemType === 1) {
        const indexData = this.indexListData.find((row) => row.id === itm.indexId);
        Object.assign(itm, indexData);
        //手动录入不需要公式
        this.changeDesc(item);
      }
    },
    changeBaseScoreValue(item) {
      this.updateBaseValue();
      if (item.examItemType === 1) {
        //手动录入不需要公式
        this.changeDesc(item);
      }
    },
    updateBaseValue() {
      if (this.scoreType === '2') {
        for (let i of this.indexData) {
          this.$set(i, 'baseScore', (i.baseScoreValue / 100) * this.formData.score);
          this.$set(i, 'excessMaxScore', (i.excessMaxScoreValue / 100) * this.formData.score);
        }
      } else {
        for (let i of this.indexData) {
          this.$set(i, 'baseScore', i.baseScoreValue);
          this.$set(i, 'excessMaxScore', i.excessMaxScoreValue);
        }
      }
    },
    // 描述由前端组装给后端
    changeDesc(item) {
      let indexDesc = '',
        indexDetailDesc = '';
      item.examSchemeContentItemIndexVo.forEach((row, index) => {
        const indexData = this.indexListData.find((rw) => rw.id === row.indexId);
        if (indexData) {
          if (index === item.examSchemeContentItemIndexVo.length - 1) {
            indexDesc += indexData.indexName;
            indexDetailDesc += indexData.indexDefinition;
          } else {
            indexDesc += indexData.indexName + ' * ';
            indexDetailDesc += indexData.indexDefinition + ' * ';
          }
        }
      });

      if (item.hasExcessScoreFlag === '0') {
        /**
         * 公式描述由前端组装给后端 后端直接保存使用
         * 区分有扣分系数和无扣分系数的描述
         * 扣分系数描述区分以下三种情况
         *  */
        if (this.siChuanYiBinAssessmentMode(item.examSchemeContentItemIndexVo[0].indexId) && item.examModel === '4') {
          if (this.isPlatformOnline(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `-{floor( 离线时长 / ${item.offlineTime})} * ${item.scoreDegree}`;
          } else if (this.isHistoricalVideoRate(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `-{floor( 单设备累计录像缺失时长 / ${item.offlineTime})} * ${item.scoreDegree}`;
          } else if (this.isHasThreeParams(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `-{floor[( ${item.baseStandard}% - ${item.examSchemeContentItemIndexVo[0].indexName} ) * 100 / ${item.offlineTime}]} * ${item.scoreDegree}`;
          }
        } else if (this.hasDeductionFactor(item.examSchemeContentItemIndexVo[0].indexId)) {
          if (this.isPlatformOnline(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `分项分值 *（1 - [ 本月离线时长 / ${item.offlineTime} ] * ${item.scoreDegree} ）`;
          } else if (this.isTrajectoryStability(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `分项分值 - 报备天数 * ${item.scoreDegree}`;
          } else {
            item.examFormula = `分项分值 - 不合格次数 * ${item.scoreDegree}`;
          }
        } else {
          /**
           * 区分视频监控数量增长率， 视频监控撤销情况以及其他指标
           */
          if (this.isMonitorGrowthRate(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `总分 + α【（视频监控数量增长率 / β）】= 总分 + ${item.excessScoreCoefficient}【（视频监控数量增长率 / ${item.scoreDegree}）】`;
          } else if (this.isMonitoringCancellation(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `总分 - α * 视频监控撤销次数 - β * 视频监控撤销数量 / γ = 总分 - ${item.excessScoreCoefficient} * 视频监控撤销次数 - ${item.scoreDegree} * 视频监控撤销数量 / ${item.offlineTime}`;
          } else if (this.isFocusOnCommandingImageQuality(item.examSchemeContentItemIndexVo[0].indexId)) {
            item.examFormula = `分项分值 - α * 巡检不合格设备数量 = ${item.baseScore} - ${item.scoreDegree} * 巡检不合格设备数量`;
          } else {
            item.examFormula = `分项分值  * ${indexDesc} = ${item.baseScore} * ${indexDetailDesc}`;
          }
        }
      } else if (item.hasExcessScoreFlag === '2') {
        if (this.isNumberOfCollection(item.examSchemeContentItemIndexVo[0].indexId)) {
          item.examFormula = `${item.excessScoreCoefficient}*（重点采集区域视频监控推送数量/应推送数量-1）*（重点采集区域人脸卡口推送数量/应推送数量-1）*（重点采集区域车辆卡口推送数量/应推送数量-1）`;
        } else {
          item.examFormula = '';
          if (!item.extensionData) return;
          item.extensionData.forEach((row) => {
            if (!row.regionCodeList.length || !row.excessScoreCoefficient) return;
            let regionList = '';
            row.regionCodeList.forEach((row, index) => {
              const region = this.initialAreaList.find((rw) => rw.regionCode === row);
              regionList += `${index === 0 ? '' : '、'}${region ? region.regionName : row}`;
            });
            item.examFormula += `${row.excessScoreCoefficient} *（实际建设总量/达标数量-1）适用于组织机构${regionList};`;
          });
        }
      }
    },
    open(id, schemeId) {
      this.idx = id;
      this.schemeId = schemeId;
      this.$refs.modalData.resetFields();
      if (id === 'add') {
        this.visible = true;
        this.formData = {
          name: '',
          score: 0,
          category: '',
          editType: true,
          addType: true,
        };
        this.scoreType = '1';
        this.indexData = [
          {
            name: '',
            isShow: true,
            editType: true,
            baseScore: 0,
            baseScoreValue: 0,
            examModel: '1',
            hasExcessScoreFlag: '0',
            examSchemeContentItemIndexVo: [{ indexName: '', editType: true }],
            scoreType: this.scoreType,
            scoreDesc: '',
            examFormula: '',
            examItemType: 1,
            excessMaxScore: 0,
            excessMaxScoreValue: 0,
            excessScoreCoefficient: 0,
            scoreDegree: 0,
            offlineTime: 0,
            baseStandard: 0,
          },
        ];
      } else {
        this.visible = true;
        this.formData = {};
        let data = {};
        data.schemeContentId = id;
        data.schemeId = schemeId;
        this.$http.post(governanceevaluation.querySchemeContent, data).then((res) => {
          this.formData = res.data.data.examSchemeContentVo;
          this.formData.category = this.formData.category.toString();
          this.indexData = res.data.data.list;
          //indexName 加分项展示字段 后端没有加字段前端取0值 by Liuf
          if (this.indexData.length && this.indexData[0].examSchemeContentItemIndexVo.length) {
            this.indexName = this.indexData[0].examSchemeContentItemIndexVo[0].indexName;
          }
          for (let i of this.indexData) {
            i.isShow = true;
            i.examItemType = i.examItemType ? i.examItemType : 1; //考核方式默认为系统考核
            this.scoreType = i.scoreType;
            if (i.examSchemeContentItemIndexVo.length === 0) {
              i.examSchemeContentItemIndexVo.push({
                indexName: null,
                editType: true,
              });
            }
            /**
             * 超额得分
             * 处理后端map对象为数组 前端展示使用数组
             * 将后端返回的相同超额建设得分的组织机构合并为一项
             */
            const tempArr = [];
            if (i.extensionData) {
              i.extensionData = JSON.parse(i.extensionData);
              Object.keys(i.extensionData).forEach((key) => {
                const index = tempArr.findIndex((row) => row.excessScoreCoefficient === i.extensionData[key]);
                if (index === -1) {
                  tempArr.push({
                    regionCodeList: [key],
                    excessScoreCoefficient: i.extensionData[key],
                  });
                } else {
                  tempArr[index].regionCodeList.push(key);
                }
              });
              i.extensionData = tempArr;
            }
          }
        });
      }
    },
    // 获取考核模式和得分类型
    async getExamDictConfig() {
      try {
        const res = await this.$http.get(governanceevaluation.getExamDictConfig);
        this.examModelList = Object.keys(res.data.data.examModelMap).map((key) => {
          return {
            label: String(res.data.data.examModelMap[key]),
            value: key,
          };
        });
        this.baseScoreTypeList = Object.keys(res.data.data.baseScoreTypeMap).map((key) => {
          return {
            label: String(res.data.data.baseScoreTypeMap[key]),
            value: key,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 根据指标调整返回的考核模式
    getExamModelList(indexId) {
      if (!indexId) return;
      let modeList = [this.examModelList.find((row) => row.value === '1')];
      // 这里需要分别判断因为不同得指标在不同得考核模式中有可能都存在
      if (this.siChuanAssessmentMode(indexId)) {
        const list = this.examModelList.filter((row) => row.value === '2');
        modeList = [...modeList, ...list];
      }
      if (this.beijingAssessmentMode(indexId)) {
        const list = this.examModelList.filter((row) => row.value === '3');
        modeList = [...modeList, ...list];
      }
      if (this.siChuanYiBinAssessmentMode(indexId)) {
        const list = this.examModelList.filter((row) => row.value === '4');
        modeList = [...modeList, ...list];
      }

      return modeList;
    },
    // 获取指标接口
    async getIndexList() {
      try {
        const res = await this.$http.post(governanceevaluation.indexList);
        this.indexListData = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 获取分类
    async getType() {
      try {
        const res = await this.$http.post(governanceevaluation.categoryList);
        this.categoryList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    handleSwitch(e, index) {
      this.indexData[index].examMethodType = e;
      this.indexData[index].showAllResult = '0';
    },
    changeUnit(item) {
      // 同步所有分数和单位
      if (this.scoreType === '2') {
        for (let i of this.indexData) {
          this.$set(i, 'scoreType', this.scoreType);
          this.$set(i, 'baseScoreValue', (i.baseScoreValue / this.formData.score).toFixed(4) * 100);
          this.$set(i, 'excessMaxScoreValue', (i.excessMaxScoreValue / this.formData.score).toFixed(4) * 100);
        }
      } else {
        for (let i of this.indexData) {
          this.$set(i, 'scoreType', this.scoreType);
          this.$set(i, 'baseScoreValue', (i.baseScoreValue / 100) * this.formData.score);
          this.$set(i, 'excessMaxScoreValue', (i.excessMaxScoreValue / 100) * this.formData.score);
        }
      }
      this.updateBaseValue();
      this.changeDesc(item);
    },
    show(index) {
      const { isShow } = this.indexData[index];
      let data = this.indexData[index];
      data.isShow = !isShow;
      this.$set(this.indexData, index, data);
    },
    queryOrg(checkedKeys, checkedNodes) {
      // 其他选择过的组织机构不保存，只保存现在可以勾选的
      let keys = [];
      checkedNodes.forEach((node) => {
        !node.disabled && keys.push(node.regionCode);
      });
      this.selectedExtension.regionCodeList = [...keys];
      this.indexData.forEach((item) => {
        this.changeDesc(item);
      });
    },
    addExtension(item) {
      item.extensionData.push({
        regionCodeList: [],
        excessScoreCoefficient: '',
      });
    },
    delExtension(item, index) {
      item.extensionData.splice(index, 1);
      this.changeDesc(item);
    },
    selectExtensionOrg(row, item) {
      // 禁用其他已经选择过的组织机构
      this.regionList = this.$util.common.deepCopy(this.initialAreaList);
      let disabledKeys = [];
      item.extensionData.forEach((rw) => {
        if (row.excessScoreCoefficient !== rw.excessScoreCoefficient) {
          disabledKeys = [...disabledKeys, ...rw.regionCodeList];
        }
        this.defaultCheckedKeys = [...this.defaultCheckedKeys, ...rw.regionCodeList];
      });
      disabledKeys.forEach((regionCode) => {
        const index = this.regionList.findIndex((region) => region.regionCode === regionCode);
        index !== -1 && (this.regionList[index].disabled = true);
      });
      this.regionList = this.$util.common.arrayToJson(this.regionList, 'regionCode', 'parentCode');

      this.orgModalShow = true;
      this.selectedExtension = row;
    },
    /**
     * 处理输入框的小数，用的是inputNumber会导致始终保留小数
     * 去除数字中后面的多余的0
     */
    dealFloat(val) {
      if (val) {
        let numberArr = val.split('');
        let spliceEndNum = 0;
        /**
         * 从数字数组中的后面倒序循环
         * 如果发现是0则增加去掉的位数
         * 直到不是0为止
         */
        for (let i = numberArr.length - 1; i >= 0; i--) {
          if (numberArr[i] === '0') {
            spliceEndNum++;
          } else {
            /**
             * 如果是小数点，则去掉从当前小数点到当前小数点后所有的数字
             * 否则则去掉不为0的后面的所有数字
             */
            if (numberArr[i] === '.') {
              numberArr.splice(i, spliceEndNum + 1);
            } else {
              numberArr.splice(i + 1, spliceEndNum);
            }
            break;
          }
        }
        return numberArr.join('');
      } else {
        return val;
      }
    },
    // 是否是数量达标率，数量达标率才显示人车视频流选择
    isQuantityCompliance(indexId) {
      return indexId === 1004;
    },
    // 是否可以增加指标, 达标率不可添加指标
    canAddIndex(indexId) {
      return [1004, 1006, 1008, 2015, 2020, 3019, 3025, 4018, 4025].includes(indexId);
    },
    /**
     * 达标率才可以选择考核模式(数量达标率， 视频图像采集区域数量达标率除外)
     */
    canChangedAssessmentMode(indexId) {
      return (
        this.siChuanAssessmentMode(indexId) ||
        this.beijingAssessmentMode(indexId) ||
        this.siChuanYiBinAssessmentMode(indexId)
      );
    },
    /**
     * 四川考核模式
     */
    siChuanAssessmentMode(indexId) {
      return [1006, 1008, 2015, 2020, 3019, 3025, 4018, 4025].includes(indexId);
    },
    /**
     * 北京考核模式
     */
    beijingAssessmentMode(indexId) {
      return [3003, 3004, 3008, 3009, 4001, 4002, 4008, 4009, 4019, 4025].includes(indexId);
    },
    /**
     * 是否是视频图像采集区域数量达标率，超额理论得分为固定公式
     */
    isNumberOfCollection(indexId) {
      return indexId === 1008;
    },
    // 是否有显示扣分系数，分布式、人像轨迹、车辆轨迹接口稳定性，共享联网平台在线率，系统报备时长有扣分系数
    hasDeductionFactor(indexId) {
      return [7004, 7007, 7008, 7009, 10].includes(indexId);
    },
    // 是否为分布式稳定性、人像轨迹、车辆轨迹接口稳定性 实际得分计算公式不同
    isTrajectoryStability(indexId) {
      return [7007, 7008, 7009].includes(indexId);
    },
    // 是否是共享联网平台在线率 实际得分计算公式不同
    isPlatformOnline(indexId) {
      return indexId === 7004;
    },
    // 是否是视频监控数量增长率 实际得分计算公式不同
    isMonitorGrowthRate(indexId) {
      return indexId === 4028;
    },
    // 是否是重点指挥图像质量情况 实际得分计算公式不同
    isMonitoringCancellation(indexId) {
      return indexId === 4029;
    },
    // 是否是视频监控撤销情况、视频监控撤销情况（人工复核） 实际得分计算公式不同
    isFocusOnCommandingImageQuality(indexId) {
      return indexId === 20001 || indexId === 20002;
    },
    changeExamModel(val, item) {
      // 当切换考核模式为四川省省厅时，更改为基础模式
      if (val !== '1') {
        item.hasExcessScoreFlag = '0';
        item.excessMaxScoreValue = 0;
        item.extensionData = null;
      }
      this.changeDesc(item);
    },
    changeExcessScoreFlag(val, item) {
      this.changeDesc(item);
      if (val === '2') {
        if (!item.extensionData || item.extensionData.length) {
          item.extensionData = [{ regionCodeList: [], excessScoreCoefficient: '' }];
        }
      } else {
        item.extensionData = null;
      }
    },
    // 是否显示 考核基础或者超额 RadioGroup
    showExamModelRadioGroup(indexId) {
      return [1006, 1008, 2015, 2020, 3019, 3025, 4018, 4025].includes(indexId);
    },
    /**
     * 四川宜宾考核模式
     */
    siChuanYiBinAssessmentMode(indexId) {
      return [4001, 4003, 4004, 4006, 4007, 4009, 4010, 4012, 4019, 4020, 4024, 7004].includes(indexId);
    },
    isHasThreeParams(indexId) {
      return [4003, 4004, 4006, 4007, 4012, 4019, 4024].includes(indexId) || this.isRealTimeVideoRate(indexId);
    },
    // 普通|重点实时视频可调阅率
    isRealTimeVideoRate(indexId) {
      return [4001, 4009].includes(indexId);
    },
    // （重点）历史录像完整率
    isHistoricalVideoRate(indexId) {
      return [4010, 4020].includes(indexId);
    },
    setDefaultValue(indexId, item) {
      if (this.isRealTimeVideoRate(indexId)) {
        item.baseStandard = 98;
        item.offlineTime = 0.3;
        item.scoreDegree = 0.1;
      } else if (this.isHasThreeParams(indexId)) {
        item.baseStandard = 99;
        item.offlineTime = 0.3;
        item.scoreDegree = 0.1;
      } else if (this.isHistoricalVideoRate(indexId)) {
        item.baseStandard = 0;
        item.offlineTime = 5;
        item.scoreDegree = 0.5;
      } else if (this.isPlatformOnline(indexId)) {
        item.baseStandard = 0;
        item.offlineTime = 30;
        item.scoreDegree = 0.5;
      }
    },
  },
  watch: {
    indexData: {
      handler(val) {
        if (val.length !== 0) {
          let totalValue = 0;
          for (let i of val) {
            // 海南超额得分
            if (i.excessMaxScoreValue && i.hasExcessScoreFlag === '2') {
              totalValue += i.excessMaxScoreValue;
              // 海南加分项不计算总分
            } else if (i.excessMaxScoreValue && i.hasExcessScoreFlag !== '3') {
              totalValue += i.excessMaxScoreValue + i.baseScoreValue;
            } else {
              totalValue += i.baseScoreValue;
            }
          }
          this.totalValue = totalValue.toFixed(4);
        }
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      initialAreaList: 'common/getInitialAreaList',
    }),
  },
  components: {
    OrgModal: require('@/components/org-modal.vue').default,
  },
};
</script>
<style lang="less" scoped>
@media screen and (max-width: 1366px) {
  @{_deep}.ivu-modal {
    width: auto !important;
  }
}
.mb-0 {
  margin-bottom: 0;
}
.ivu-form-item {
  margin-bottom: 20px;
}
.icon-color {
  color: var(--color-active);
}
.del-index {
  position: relative;
  cursor: pointer;
  margin-left: 10px;
}
.name-item {
  margin-bottom: 0px;
  @{_deep} .ivu-form-item-label {
    width: 110px;
  }
  @{_deep} .ivu-form-item-error-tip {
    margin-left: 110px;
  }
}
.ipt-lg {
  width: 400px;
}
.ipt-ml {
  width: 60px;
}
.right-item {
  @{_deep} .ivu-form-item-label {
    width: 130px;
  }
  @{_deep} .ivu-form-item-content {
    margin-left: 130px;
  }
}
.formbox {
  height: 450px;
  overflow: auto;
  .form {
    .extension {
      margin-bottom: 25px;
      &:last-of-type {
        margin-bottom: 0;
      }
    }
    .input-add-points {
      width: 50px;
      padding: 0px;
      @{_deep} .ivu-input-number-controls-outside-btn {
        display: none;
      }
    }
    .form-name {
      flex-direction: row;
      margin-bottom: 15px;
      padding: 10px 21px;
      color: var(--color-display-text);
      background-color: var(--bg-collapse-item);
    }
    .select-width {
      width: 400px;
    }
    .index-width {
      width: 340px;
      margin-right: 10px;
    }
    .yun-width {
      width: 50px;
    }
    .select-width-ml {
      width: 50px;
      margin-left: 10px;
    }
    .remaker {
      margin-left: 10px;
      color: var(--color-content);
    }
    .redmark {
      color: var(--color-tips);
      margin-left: 130px;
      margin-bottom: 15px;
    }
  }
}
.box-ipt {
  margin-bottom: 15px;
  &:last-of-type {
    margin-bottom: 0;
  }
}
.selcheng {
  margin-right: 10px;
}
.sorce {
  color: var(--color-content);
  margin-left: 10px;
}
@{_deep}.select-width {
  width: 400px;
}

.ivu-form-item-content {
  display: flex;
}
.arrowrun {
  color: var(--color-el-notification__closeBtn);
  font-size: 12px !important;
  transition: 0.2s;
  transform-origin: center;
  transform: rotateZ(-180deg);
  display: inline-block;
  height: 35px;
  line-height: 35px;
  margin-left: 10px;
}
.arrow {
  color: var(--color-el-notification__closeBtn);
  font-size: 12px !important;
  transition: 0.2s;
  transform-origin: center;
  transform: rotateZ(0deg);
  display: inline-block;
  height: 35px;
  line-height: 35px;
  margin-left: 10px;
}
.ipc {
  margin-left: 10px;
}
.input-number-list {
  padding: 0px;
  width: 400px;
  @{_deep} .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
.width-80 {
  width: 80px;
}
</style>
