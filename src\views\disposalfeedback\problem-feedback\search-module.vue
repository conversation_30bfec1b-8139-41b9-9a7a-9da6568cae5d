<template>
  <div class="search-module-box">
    <section class="search-box">
      <div class="mb-sm">
        <ui-label class="inline mr-lg" label="问题编码">
          <Input class="width-lg" clearable v-model="searchForm.code" placeholder="请输入问题编码"></Input>
        </ui-label>
        <ui-label class="inline mr-lg" label="数据类型">
          <Select
            v-model="searchForm.dataType"
            placeholder="请选择数据类型 "
            :loading="dataTypeEnumLoading"
            class="width-lg"
            clearable
          >
            <Option v-for="(value, key) in dataTypeEnum" :key="key" :value="key">{{ value }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg" label="异常原因">
          <Select
            v-model="searchForm.causeCodes"
            placeholder="请选择异常原因"
            class="width-lg"
            :loading="causeEnumLoading"
            multiple
            :max-tag-count="1"
          >
            <Option v-for="(value, key) in causeEnum" :key="key" :value="key">{{ value }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg" label="有效状态">
          <Select v-model="searchForm.effective" placeholder="请选择有效状态 " class="width-lg" clearable>
            <Option v-for="(value, key) in EFFECTIVE_STATUS" :key="key" :value="value.value">{{
              value.text
            }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg" label="处理状态">
          <Select v-model="searchForm.status" placeholder="请选择处理状态" class="width-lg" clearable>
            <Option v-for="(value, key) in HANDLE_RESULT_STATUS" :key="key" :value="value.value">{{ value.text }}</Option>
          </Select>
        </ui-label>
      </div>
      <div>
        <ui-label class="inline mr-lg" label="提交人">
          <Input class="width-lg" clearable v-model="searchForm.name" placeholder="请输入提交人姓名/电话/警号"></Input>
        </ui-label>
        <ui-label class="inline mr-lg" label="提交时间">
          <DatePicker
            class="width-md"
            v-model="searchForm.startTime"
            type="datetime"
            placeholder="请选择开始时间"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchForm, 'startTime')"
            :options="startTimeOption"
            confirm
          />
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="width-md"
            v-model="searchForm.endTime"
            type="datetime"
            placeholder="请选择结束时间"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchForm, 'endTime')"
            :options="endTimeOption"
            confirm
          />
        </ui-label>
        <ui-label class="inline" label="">
          <Button type="primary" class="mr-sm" @click.stop="startSearch">查询</Button>
          <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchForm, resetSearch)">重置</Button>
        </ui-label>
      </div>

      <slot></slot>
    </section>
  </div>
</template>
<script>
import feedbackApi from '@/config/api/feedback';
import { HANDLE_RESULT_STATUS, EFFECTIVE_STATUS } from './util/enum';
export default {
  data() {
    return {
      dataTypeEnum: {}, //数据类型列表
      dataTypeEnumLoading: false, //加载数据类型loading
      causeEnum: {}, //异常原因列表
      causeEnumLoading: false,
      HANDLE_RESULT_STATUS, //处理状态
      EFFECTIVE_STATUS, //有效状态
      searchForm: {
        code: '', //问题编码
        dataType: '', //数据类型
        causeCodes: [], //异常原因
        checkStatuses: '', //检测状态
        status: '', //处理状态
        name: '', //提交人
        startTime: '', //提交时间
        endTime: '',
        effective: '', //有效状态
      },
      startTimeOption: {
        disabledDate: (date) => {
          //若未选择结束时间，则限制开始时间不大于当前时间
          if (!this.searchForm.endTime) {
            return date.getTime() > Date.now();
          }
          let endTime = new Date(this.searchForm.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          if (!this.searchForm.startTime) {
            return date.getTime() > Date.now() + 24 * 3600 * 1000;
          }
          let startTime = new Date(this.searchForm.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  props: {},
  computed: {},
  watch: {
    'searchForm.dataType': {
      handler() {
        this.searchForm.causeCodes = [];
        this.getCauseEnum();
      },
    },
  },
  methods: {
    //获取数据类型列表
    async getDataTypeEnum() {
      try {
        this.dataTypeEnumLoading = true;
        let { data } = await this.$http.get(feedbackApi.causeGetType);
        this.dataTypeEnum = data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.dataTypeEnumLoading = false;
      }
    },
    //获取异常原因列表
    async getCauseEnum() {
      let params = {
        type: this.searchForm.dataType || 1000,
      };
      try {
        this.causeEnumLoading = true;
        let { data } = await this.$http.get(feedbackApi.getCause, {
          params,
        });
        this.causeEnum = data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.causeEnumLoading = false;
      }
    },
    //查询
    startSearch() {
      this.$emit('startSearch', this.searchForm);
    },
    //重置
    resetSearch() {
      this.$emit('resetSearch', {});
    },

    // DatePicker选择时间后格式化
    changeTimeMx(formatTime, timeType, searchData, timeField) {
      this.$set(searchData, timeField, formatTime);
    },
  },
  created() {},
  async mounted() {
    this.copySearchDataMx(this.searchForm);
    await this.getDataTypeEnum();
    await this.getCauseEnum();
  },
};
</script>
<style lang="less" scoped>
.search-module-box {
  @{_deep} .ivu-select-selection {
    & > div {
      white-space: nowrap;
    }
  }
  @{_deep} .ivu-select-multiple .ivu-tag {
    max-width: 68%;
  }
}
</style>
