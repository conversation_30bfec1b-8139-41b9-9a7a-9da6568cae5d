<template>
  <div class="city-report-chart">
    <div class="ranking-title f-14">
      <i class="icon-font icon-shi mr-xs"></i>
      <span>{{ cityChartTitle }}</span>
      <Checkbox v-model="sortField" class="fr" @on-change="onChangeSortField">
        <slot name="rank-title">按上报数量排列</slot>
      </Checkbox>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: cityChartsData }">
      <draw-echarts :echart-option="echarts" :echart-style="rinStyle" ref="cityDryChart" class="charts"></draw-echarts>
      <span class="next-echart" v-if="cityChartsData.xData.length > 20">
        <i
          class="icon-font icon-zuojiantou1 f-12"
          @click="scrollRight('cityDryChart', cityChartsData.xData, [], 20)"
        ></i>
      </span>
    </div>
  </div>
</template>

<script>
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'city-report-chart',
  mixins: [dataZoom],
  props: {
    cityChartsData: {
      type: Object,
      default: () => {},
    },
    cityChartTitle: {
      type: String,
      default: '地市上报数量',
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sortField: '',
      echarts: {},
      rinStyle: {
        width: '100%',
        height: '100%',
      },
    };
  },
  methods: {
    onChangeSortField(val) {
      this.$emit('changeCitySortField', val);
    },
    initChart() {
      const opts = {
        color: [$var('--color-green-8'), $var('--color-orange-5')],
        legendData: ['达标', '未达标'],
        tooltipFormatter: (data) => {
          let str = '';
          for (let i = 0; i < data.length; i++) {
            if (data[i].data > 0) {
              str = `<span style="display: inline-block;width: 10px;height: 10px;margin-right: 5px;border-radius: 10px;background-color: ${data[i].color}"></span>
                        <span>上报数量：${data[i].data}</span>`;
            }
          }
          return str;
        },
        xData: this.cityChartsData.xData,
        series: [
          {
            name: '达标',
            type: 'bar',
            data: this.cityChartsData.qualifiedData,
            stack: 'value',
            barWidth: '22px',
            barCategoryGap: '3%',
            barGap: 0.3, //柱间距离
          },
          {
            name: '未达标',
            type: 'bar',
            data: this.cityChartsData.unqualifiedData,
            stack: 'value',
            barWidth: '22px',
            barCategoryGap: '3%',
            barGap: 0.3, //柱间距离
          },
        ],
      };
      this.echarts = this.$util.doEcharts.reportChart(opts);
      setTimeout(() => {
        this.setDataZoom('cityDryChart', [], 20);
      });
    },
  },
  watch: {
    cityChartsData: {
      handler() {
        this.initChart();
      },
      deep: true,
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>

<style lang="less" scoped>
.city-report-chart {
  position: relative;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    height: calc(100% - 44.5px) !important;
    width: 100%;
    background: var(--bg-sub-echarts-content);
    padding-top: 10px;
    .charts {
      height: 100% !important;
    }
    .next-echart {
      top: 50%;
      right: 0;
      position: absolute;

      .icon-zuojiantou1 {
        color: rgba(45, 190, 255, 1);
        font-size: 12px;
        vertical-align: top !important;
      }
      &:active {
        .icon-zuojiantou1 {
          color: #4e9ef2;
          font-size: 12px;
          vertical-align: top !important;
        }
      }
      &:hover {
        .icon-zuojiantou1 {
          color: var(--color-primary);
          font-size: 12px;
          vertical-align: top !important;
        }
      }
    }
  }
}
</style>
