import { queryByTypeKey, queryDataByKeyTypes } from "@/api/user";
import { deepCopy } from "@/util/modules/common";

const dictStateValList = {
  DE00683: "vehicleBrandList", // vehicleBrandType 旧字典值
  DE01246: "licensePlateColorList", // licensePlateColor
  licensePlateColor: "licensePlateColor", // 车牌颜色
  drive_status: "driveStatus",
  DE00332: "ellegaList",
  vehicleClassType: "vehicleClassTypeList",
  bodyColor: "bodyColorList",
  card_type: "cardTypeList",
  DE00014: "marriageList",
  // 'marital_status': 'marriageList',
  search_type: "searchTypeList",
  ipbd_deviceType: "ipbdDeviceTypeList",
  point_view: "pointViewList",
  DE00306: "plateClassList", // plate_class
  plate_occlusion: "plateOcclusionList",
  DE00308: "vehicleColorList", // vehicle_color
  //   'DE00308': 'vehicleColor',
  DE00303: "vehicleTypeList",
  roofItems: "roofItemsList",
  markerType: "markerTypeList",
  sunVisorStatus: "sunVisorStatusList",
  gender: "genderList",
  nation: "nationList",
  profession: "professionList",
  education: "educationList",
  political_status: "politicalList",
  B36: "specialVehicleList", // specialVehicle
  driverFlag: "driverFlagList",
  DE00742: "copilotList", //副驾驶
  facialOcclusion: "facialOcclusionList",
  annualInspectionNum: "annualInspectionNumList",
  propertySearch_sbdwlx: "sbdwlxList",
  manufacturer: "manufacturerList",
  propertySearch_sblwzt: "sblwztList",
  propertySearch_isonline: "isonlineList",
  hlxdm: "householdRegistrationTypeList",
  migration_reason: "migrationReasonList",
  ipbd_face_capture_clustering: "ipbdFaceCaptureClustering",
  ipbd_face_capture_gender: "ipbdFaceCaptureGender",
  ipbd_face_capture_age: "ipbdFaceCaptureAge",
  ipbd_face_capture_glasses: "ipbdFaceCaptureGlasses",
  ipbd_face_capture_mask: "ipbdFaceCaptureMask",
  ipbd_face_capture_cap: "ipbdFaceCaptureCap",
  DE00085: "identityTypeList",
  DE00011: "nationTypeList",
  ipbd_table_datasource_type: "datasourceType", //
  ipbd_task_business_type: "businessType",
  ipbd_my_favorite_type: "favoriteType",
  DE00596: "vehicleUseStatus",
  DE00302: "vehicleUseNature",
  B40: "upperBodyTexture",
  B317_1400: "upperBodyType",
  SleeveStyle: "upperSleeveType",
  B34_1400: "recognitionColor",
  B41: "lowerBodyType",
  B321_1400: "shoeCategory",
  B38_1400: "hairStyleList",
  B312_1400: "behaviorList",
  B313_1400: "appendantList",
  B38: "nonmotorVehicleType",
  B42: "mannedSituation",
  NonmotorAngle: "nonmotorVehicleAngle",
  B67: "packetType",
  SkinColour: "skinColor",
  propertySearch_sbgnlx: "sbgnlxList",
  ivcp_stat_dataStatus: "dataStatusList",
  ivcp_stat_dataType: "dataTypeList",
  ivcp_deviceNetworkStatus: "deviceNetworkStatusList",
  ivcp_algorithm_type: "algorithmTypeList",
  ivcp_deviceLastTime_haveDataStatus: "haveDataStatusList",
  ivcp_deviceLastTime_delayStatus: "delayStatusList",
  icbd_case_type: "caseTypeList",
  icbd_policeCase_type: "partiTypeList",
  icbd_trajectory_type: "trajectoryTypeList",
  icbd_caseTime_type: "caseTimeTypeList",
  aois_first_level: "placeFirstLevelList",
  aois_second_level: "placeSecondLevelList",
  yhsd_B40: "yhsdUpperBodyTexture",
  yhsd_B41: "yhsdLowerBodyType",
  yhsd_capture_age: "yhsdFaceCaptureAge",
  yhsd_B312_1400: "yhsdBehaviorList",
  yhsd_capture_mask: "yhsdCaptureMask",
  yhsd_capture_cap: "yhsdCaptureCap",
  fake_status: "fakeStatusList",
  point_place: "pointPlaceList",
  construction_project: "constructionProjectList",
  construction_unit: "constructionUnitList",
  operation_unit: "operationUnitList",
  task_run_state: "taskRunStateList",
  associationId_card_status: "associationIdCardStatusList",
  fake_result: "fakeResultList",
  ivcp_stat_dataType: "ivcpStatDataType",
  ivcp_device_type: "ivcpDeviceType",
  face_lib_biz_type: "faceLibBizList",
  risky_behavior_type: "riskyBehaviorTypeList",
  track_alarm_type: "trackAlarmTypeList",
};

const dictStateValList2 = {
  vehicleClassType_k: "vehicleClassTypeKList",
  vehicleClassType_h: "vehicleClassTypeHList",
  vehicleClassType_q: "vehicleClassTypeQList",
  vehicleClassType_z: "vehicleClassTypeZList",
  vehicleClassType_d: "vehicleClassTypeDList",
  vehicleClassType_m: "vehicleClassTypeMList",
  vehicleClassType_n: "vehicleClassTypeNList",
  vehicleClassType_t: "vehicleClassTypeTList",
  vehicleClassType_j: "vehicleClassTypeJList",
  vehicleClassType_g: "vehicleClassTypeGList",
  vehicleClassType_b: "vehicleClassTypeBList",
  vehicleClassType_x: "vehicleClassTypeXList",
};

const dictStateValList3 = {
  struct_data_type: "structDataType",
  struct_task_status: "structTaskStatus",
  struct_histroytask_status: "structHistroytaskStatus",
  filestrucure_task_status: "filestrucureTaskStatus",
};

export default {
  namespaced: true,
  state: {
    vehicleBrandList: [], // 车辆品牌
    licensePlateColorList: [], //车牌颜色
    licensePlateColor: [], // 车牌颜色
    vehicleClassTypeList: [], //车辆类型、车身类型(枚举精准检索)
    bodyColorList: [], //车辆颜色
    cardTypeList: [], //证件类型
    marriageList: [], //婚姻状态
    genderList: [], //性别
    nationList: [], //民族
    professionList: [], //职业
    educationList: [], //学历
    politicalList: [], //政治面貌
    sbdwlxList: [], //摄像机点位类型
    sblwztList: [], //设备联网状态
    isonlineList: [], //设备在线状态
    manufacturerList: [], //设备厂商
    searchTypeList: [], //检索类型
    ipbdDeviceTypeList: [], // 设备类型
    pointViewList: [], // 角度(枚举精准检索)
    plateClassList: [], // 车牌类型(枚举精准检索)
    plateOcclusionList: [], // 遮挡(枚举精准检索)
    vehicleColorList: [], // 车身颜色(枚举精准检索)
    vehicleColor: [],
    vehicleTypeList: [], // 机动车车辆类型代码
    sunVisorStatusList: [], // 遮阳板
    roofItemsList: [], // 车顶物件
    markerTypeList: [], // 标志物
    specialVehicleList: [], // 特殊车辆
    driverFlagList: [], // 副驾有人
    copilotList: [], // 副驾驶
    facialOcclusionList: [], // 面部遮挡
    annualInspectionNumList: [], // 年检标
    householdRegistrationTypeList: [], //户口类型
    migrationReasonList: [], //迁移原因
    ipbdFaceCaptureClustering: [], // 聚类
    ipbdFaceCaptureGender: [], // 性别
    ipbdFaceCaptureAge: [], // 年龄段
    ipbdFaceCaptureGlasses: [], // 眼镜
    ipbdFaceCaptureMask: [], // 口罩
    ipbdFaceCaptureCap: [], // 帽子

    vehicleClassTypeKList: [], // 客车
    vehicleClassTypeHList: [], // 货车
    vehicleClassTypeQList: [], // 牵引车
    vehicleClassTypeZList: [], // 专项作业车
    vehicleClassTypeDList: [], // 电车
    vehicleClassTypeMList: [], // 摩托车
    vehicleClassTypeNList: [], // 三轮汽车
    vehicleClassTypeTList: [], // 拖拉机
    vehicleClassTypeJList: [], // 轮式机械
    vehicleClassTypeGList: [], // 全挂车
    vehicleClassTypeBList: [], // 半挂车
    vehicleClassTypeXList: [], // 其他

    identityTypeList: [], // 证件类型（人员）
    nationTypeList: [], // 民族类型
    ellegaList: [], // 违法违章
    datasourceType: [], // 数据来源 - 系统配置
    businessType: [], // 数据作业 - 系统配置
    favoriteType: [], // 我的收藏tab
    vehicleUseStatus: [], // 机动车使用状态
    vehicleUseNature: [], // 机动车使用性质
    upperBodyTexture: [], // 上身纹理
    upperBodyType: [], //上身类型
    upperSleeveType: [], //上身袖子类型
    recognitionColor: [], //颜色（人体，非机动车）
    lowerBodyType: [], //下身类型
    shoeCategory: [], //鞋子类别
    hairStyleList: [], //发型
    behaviorList: [], //行为
    appendantList: [], //附属物
    nonmotorVehicleType: [], //非机动车类型
    mannedSituation: [], //载人情况
    nonmotorVehicleAngle: [], //非机动车角度
    packetType: [], //包类型
    skinColor: [], //肤色
    sbgnlxList: [], //设备类型
    dataStatusList: [], // 数据状态
    dataTypeList: [], //数据类型
    deviceNetworkStatusList: [],
    algorithmTypeList: [], //算法厂商
    haveDataStatusList: [], //数据有无状态
    fakeStatusList: [], //套牌分析状态
    fakeResultList: [], // 套牌处理状态
    delayStatusList: [], //数据延迟状态
    caseTypeList: [], //案件类型
    partiTypeList: [], //案件类型
    trajectoryTypeList: [], //轨迹类型
    caseTimeTypeList: [], //案发前后时间
    placeFirstLevelList: [], // 场所一级分类
    placeSecondLevelList: [], // 场所二级分类

    structDataType: [], //视图解析类型
    ivcpStatDataType: [], //视图解析类型
    ivcpDeviceType: [], // 视图解析设备
    structTaskStatus: [], //实时视频解析任务状态
    structHistroytaskStatus: [], //历史视频解析任务状态
    filestrucureTaskStatus: [], //文件解析任务状态

    yhsdUpperBodyTexture: [], //  上身纹理
    yhsdLowerBodyType: [], // 下身类型
    yhsdFaceCaptureAge: [], //  年龄范围
    yhsdBehaviorList: [], // 特征行为类型
    yhsdCaptureMask: [], // 是否戴口罩
    yhsdCaptureCap: [], // 是否戴帽子

    pointPlaceList: [], //点位所在场所
    constructionProjectList: [], //点位建设项目
    constructionUnitList: [], //点位建设单位
    operationUnitList: [], //点位运维单位

    taskRunStateList: [], //战法-首次入城-任务状态
    associationIdCardStatusList: [], //推荐-疑似骑电动车不戴头盔人员推荐-身份关联状态
    driveStatus: [], // 高速入口状态
    faceLibBizList: [], // 人脸库业务库类型
    riskyBehaviorTypeList: [], // 风险行为类型
    trackAlarmTypeList: [], // 轨迹告警类型
  },
  getters: {
    getVehicleBrandList(state) {
      return state.vehicleBrandList;
    },
    getLicensePlateColorList(state) {
      return state.licensePlateColorList;
    },
    getLicensePlateColor(state) {
      return state.licensePlateColor;
    },
    getDriveStatus(state) {
      return state.driveStatus;
    },
    getVehicleClassTypeList(state) {
      return state.vehicleClassTypeList;
    },
    getBodyColorList(state) {
      return state.bodyColorList;
    },
    getCardTypeList(state) {
      return state.cardTypeList;
    },
    getMarriageList(state) {
      return state.marriageList;
    },
    getNationList(state) {
      return state.nationList;
    },
    getProfession(state) {
      return state.professionList;
    },
    getEducation(state) {
      return state.educationList;
    },
    getPolitical(state) {
      return state.politicalList;
    },
    getIpbdDeviceTypeList(state) {
      return state.ipbdDeviceTypeList;
    },
    getSearchTypeList(state) {
      return state.searchTypeList;
    },
    getGenderList(state) {
      return state.genderList;
    },
    getSbdwlxList(state) {
      return state.sbdwlxList;
    },
    getSblwztList(state) {
      return state.sblwztList;
    },
    getIsonlineList(state) {
      return state.isonlineList;
    },
    getManufacturerList(state) {
      return state.manufacturerList;
    },
    // 以下智慧云搜车辆搜索字典
    getPointViewList(state) {
      return state.pointViewList;
    },
    getPlateClassList(state) {
      return state.plateClassList;
    },
    getPlateOcclusionList(state) {
      return state.plateOcclusionList;
    },
    getVehicleColorList(state) {
      return state.vehicleColorList;
    },
    getVehicleColor(state) {
      return state.vehicleColor;
    },
    getVehicleTypeList(state) {
      return state.vehicleTypeList;
    },
    getRoofItemsList(state) {
      return state.roofItemsList;
    },
    getMarkerTypeList(state) {
      return state.markerTypeList;
    },
    getSunVisorStatusList(state) {
      return state.sunVisorStatusList;
    },
    getSpecialVehicleList(state) {
      return state.specialVehicleList;
    },
    getDriverFlagList(state) {
      return state.driverFlagList;
    },
    getCopilotList(state) {
      return state.copilotList;
    },
    getFacialOcclusionList(state) {
      return state.facialOcclusionList;
    },
    getAnnualInspectionNumList(state) {
      return state.annualInspectionNumList;
    },
    getHouseholdRegistrationTypeList(state) {
      return state.householdRegistrationTypeList;
    },
    getMigrationReasonList(state) {
      return state.migrationReasonList;
    },

    getIpbdFaceCaptureClustering(state) {
      return state.ipbdFaceCaptureClustering;
    },
    getIpbdFaceCaptureGender(state) {
      return state.ipbdFaceCaptureGender;
    },
    getIpbdFaceCaptureAge(state) {
      return state.ipbdFaceCaptureAge;
    },
    getFakeStatusList(state) {
      return state.fakeStatusList;
    },
    getFakeResultList(state) {
      return state.fakeResultList;
    },
    getIpbdFaceCaptureGlasses(state) {
      return state.ipbdFaceCaptureGlasses;
    },
    getIpbdFaceCaptureMask(state) {
      return state.ipbdFaceCaptureMask;
    },
    getIpbdFaceCaptureCap(state) {
      return state.ipbdFaceCaptureCap;
    },

    getVehicleClassTypeKList(state) {
      return state.vehicleClassTypeKList;
    },
    getVehicleClassTypeHList(state) {
      return state.vehicleClassTypeHList;
    },
    getVehicleClassTypeQList(state) {
      return state.vehicleClassTypeQList;
    },
    getVehicleClassTypeZList(state) {
      return state.vehicleClassTypeZList;
    },
    getVehicleClassTypeDList(state) {
      return state.vehicleClassTypeDList;
    },
    getVehicleClassTypeMList(state) {
      return state.vehicleClassTypeMList;
    },
    getVehicleClassTypeNList(state) {
      return state.vehicleClassTypeNList;
    },
    getVehicleClassTypeTList(state) {
      return state.vehicleClassTypeTList;
    },
    getVehicleClassTypeJList(state) {
      return state.vehicleClassTypeJList;
    },
    getVehicleClassTypeGList(state) {
      return state.vehicleClassTypeGList;
    },
    getVehicleClassTypeBList(state) {
      return state.vehicleClassTypeBList;
    },
    getVehicleClassTypeXList(state) {
      return state.vehicleClassTypeXList;
    },
    getIdentityTypeList(state) {
      return state.identityTypeList;
    },
    getNationTypeList(state) {
      return state.nationTypeList;
    },
    getEllegaList(state) {
      return state.ellegaList;
    },
    getDatasourceType(state) {
      return state.datasourceType;
    },
    getBusinessType(state) {
      return state.businessType;
    },
    getFavoriteType(state) {
      return state.favoriteType;
    },
    getVehicleUseStatus(state) {
      return state.vehicleUseStatus;
    },
    getVehicleUseNature(state) {
      return state.vehicleUseNature;
    },
    getUpperBodyTexture(state) {
      return state.upperBodyTexture;
    },
    getUpperBodyType(state) {
      return state.upperBodyType;
    },
    getUpperSleeveType(state) {
      return state.upperSleeveType;
    },
    getRecognitionColor(state) {
      return state.recognitionColor;
    },
    getLowerBodyType(state) {
      return state.lowerBodyType;
    },
    getShoeCategory(state) {
      return state.shoeCategory;
    },
    getHairStyleList(state) {
      return state.hairStyleList;
    },
    getBehaviorList(state) {
      return state.behaviorList;
    },
    getAppendantList(state) {
      return state.appendantList;
    },
    getNonmotorVehicleType(state) {
      return state.nonmotorVehicleType;
    },
    getMannedSituation(state) {
      return state.mannedSituation;
    },
    getNonmotorVehicleAngle(state) {
      return state.nonmotorVehicleAngle;
    },
    getPacketType(state) {
      return state.packetType;
    },
    getSkinColor(state) {
      return state.skinColor;
    },
    getSbgnlxList(state) {
      return state.sbgnlxList;
    },
    getDataStatusList(state) {
      return state.dataStatusList;
    },
    getDataTypeList(state) {
      return state.dataTypeList;
    },
    getDeviceNetworkStatusList(state) {
      return state.deviceNetworkStatusList;
    },
    getAlgorithmTypeList(state) {
      return state.algorithmTypeList;
    },
    getHaveDataStatusList(state) {
      return state.haveDataStatusList;
    },
    getDelayStatusList(state) {
      return state.delayStatusList;
    },
    getCaseTypeList(state) {
      return state.caseTypeList;
    },
    getPartiTypeList(state) {
      return state.partiTypeList;
    },
    getTrajectoryTypeList(state) {
      return state.trajectoryTypeList;
    },
    getCaseTimeTypeList(state) {
      return state.caseTimeTypeList;
    },
    getPlaceFirstLevelList(state) {
      return state.placeFirstLevelList;
    },
    getPlaceSecondLevelList(state) {
      return state.placeSecondLevelList;
    },

    getStructDataType(state) {
      return state.structDataType;
    },
    getIvcpStatDataType(state) {
      return state.ivcpStatDataType;
    },
    getIvcpDeviceType(state) {
      return state.ivcpDeviceType;
    },
    getStructTaskStatus(state) {
      return state.structTaskStatus;
    },
    getStructHistroytaskStatus(state) {
      return state.structHistroytaskStatus;
    },
    getFilestrucureTaskStatus(state) {
      return state.filestrucureTaskStatus;
    },
    getYhsdUpperBodyTexture(state) {
      return state.yhsdUpperBodyTexture;
    },
    getYhsdLowerBodyType(state) {
      return state.yhsdLowerBodyType;
    },
    getYhsdFaceCaptureAge(state) {
      return state.yhsdFaceCaptureAge;
    },
    getYhsdBehaviorList(state) {
      return state.yhsdBehaviorList;
    },
    getYhsdCaptureMask(state) {
      return state.yhsdCaptureMask;
    },
    getYhsdCaptureCap(state) {
      return state.yhsdCaptureCap;
    },

    getPointPlaceList(state) {
      return state.pointPlaceList;
    },
    getConstructionProjectList(state) {
      return state.constructionProjectList;
    },
    getConstructionUnitList(state) {
      return state.constructionUnitList;
    },
    getOperationUnitList(state) {
      return state.operationUnitList;
    },
    getTaskRunStateList(state) {
      return state.taskRunStateList;
    },
    getAssociationIdCardStatusList(state) {
      return state.associationIdCardStatusList;
    },
    getFaceLibBizList(state) {
      return state.faceLibBizList;
    },
    getRiskyBehaviorTypeList(state) {
      return state.riskyBehaviorTypeList;
    },
    getTrackAlarmTypeList(state) {
      return state.trackAlarmTypeList;
    },
  },

  mutations: {
    setAllDictData(state, { data }) {
      data.map((val) => {
        const typeKey = Object.keys(val)[0];
        state[dictStateValList[typeKey]] = val[typeKey];
      });
    },
    setAllDictData2(state, { data }) {
      data.map((val) => {
        const typeKey = Object.keys(val)[0];
        state[dictStateValList2[typeKey]] = val[typeKey];
      });
    },
    setAllDictData3(state, { data }) {
      data.map((val) => {
        const typeKey = Object.keys(val)[0];
        state[dictStateValList3[typeKey]] = val[typeKey];
      });
    },
    setDictData(state, { data, typeKey }) {
      state[dictStateValList[typeKey]] = data;
    },
    setDictDataBatch(state, { data }) {
      data.map((val) => {
        const typeKey = Object.keys(val)[0];
        state[dictStateValList[typeKey]] = val[typeKey];
      });
    },
  },
  actions: {
    // 批量获取字典值
    async getDictAllData({ state, commit }) {
      const isRequested = Object.keys(dictStateValList).some(
        (key) => state[dictStateValList[key]].length > 0
      );
      if (isRequested) {
        return false;
      } // 判断如果 请求过 不在进行接口请求
      const dictCodeList = Object.keys(dictStateValList);
      let { data } = await queryDataByKeyTypes(dictCodeList);
      await commit("setAllDictData", { data: data });
      return data;
    },
    // 批量获取车辆字典值
    getDictAllData2({ state, commit }) {
      return new Promise((resolve, reject) => {
        const isRequested = Object.keys(dictStateValList2).some(
          (key) => state[dictStateValList2[key]].length > 0
        );
        if (isRequested) {
          resolve();
          return false;
        } // 判断如果 请求过 不在进行接口请求
        const dictCodeList = Object.keys(dictStateValList2);
        queryDataByKeyTypes(dictCodeList)
          .then((res) => {
            commit("setAllDictData2", { data: res.data });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 批量获取视图解析字典值
    getDictStructData({ state, commit }) {
      return new Promise((resolve, reject) => {
        const isRequested = Object.keys(dictStateValList3).some(
          (key) => state[dictStateValList3[key]].length > 0
        );
        if (isRequested) {
          resolve();
          return false;
        } // 判断如果 请求过 不在进行接口请求
        const dictCodeList = Object.keys(dictStateValList3);
        queryDataByKeyTypes(dictCodeList)
          .then((res) => {
            commit("setAllDictData3", { data: res.data });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 设置单个字典值
    getDictData({ state, commit }, typekey) {
      return new Promise((resolve, reject) => {
        const isRequested = state[dictStateValList[typekey]].length > 0;
        if (isRequested) {
          resolve();
          return false;
        } // 判断如果 请求过 不在进行接口请求
        queryByTypeKey({ typekey })
          .then((res) => {
            commit("setDictData", { data: res.data, typekey });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 更新单个字典值
    updateDictData({ state, commit }, typekey) {
      return new Promise((resolve, reject) => {
        queryByTypeKey({ typekey })
          .then((res) => {
            commit("setDictData", { data: res.data, typeKey: typekey });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 更新多个字典值
    updateBatchDictData({ state, commit }, typeKeys) {
      return new Promise((resolve, reject) => {
        queryDataByKeyTypes(typeKeys)
          .then((res) => {
            commit("setDictDataBatch", { data: res.data });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
  },
};
