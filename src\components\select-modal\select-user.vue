<!--
    * @FileDescription: 选择用户
    * @Author: H
    * @Date: 2023/4/18
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-30 09:46:51
-->
<template>
  <ui-modal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onCancel="handleCancel"
    @onOk="confirmHandle"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="organization" v-if="showOrganization">
        <div class="title">组织机构</div>
        <select-tree
          ref="selectTree"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :treeData="treeData"
          @selectedTree="selectedOrgTree"
          @check="handleCheck"
          placeholder="请选择组织机构"
        >
        </select-tree>
      </div>
      <div class="select-label-content">
        <Form inline ref="formData" :model="formData" class="form">
          <FormItem label="姓名:" prop="name">
            <Input
              v-model="formData.name"
              size="small"
              placeholder="请输入"
            ></Input>
          </FormItem>
          <FormItem label="用户名:" prop="userName">
            <Input
              v-model="formData.userName"
              size="small"
              placeholder="请输入"
            ></Input>
          </FormItem>
          <FormItem label="角色:" prop="roleId">
            <Select placeholder="请选择" v-model="formData.roleId">
              <Option
                v-for="(item, index) in roleSelectList"
                :key="index"
                :value="item.id"
                :label="item.roleName"
              ></Option>
            </Select>
          </FormItem>
          <FormItem>
            <Button class="find" type="primary" @click="handleQuery"
              >查询</Button
            >
            <Button type="default" @click="resetForm">重置</Button>
          </FormItem>
        </Form>
        <Table
          class="auto-fill table"
          ref="table"
          :height="335"
          :columns="columns"
          :data="tableData"
          :loading="loading"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template slot="userName" slot-scope="{ row }">
            <span class="link-btn cursor-p" @click="deviceArchives(row)">{{
              row.username
            }}</span>
          </template>
          <template #labels="{ row }">
            <ui-tag-poptip
              v-if="row.labels && row.labels.length"
              :data="row.labels"
            />
          </template>
        </Table>
        <ui-page
          :current="pageInfo.pageNumber"
          :total="total"
          :page-size="pageInfo.pageSize"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{ selectTableData.length }}</span> 个</span
          >
          <span class="del-btn" @click="removeAllHandle">
            <i class="iconfont icon-shanchu"></i>清空
          </span>
        </div>
        <div class="label-container">
          <ul>
            <li v-for="(item, index) in selectTableData" :key="index">
              <Checkbox
                v-model="item.select"
                :disabled="item.disabled || false"
                @on-change="selectChange(item, index)"
              >
                <!-- 也不知道到底要返回什么 -->
                {{ item.name || item.username || item.userName }}
              </Checkbox>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import {
  userPageList,
  userPageListNew,
  rolePageList,
  queryUserDatascopeListNew,
} from "@/api/user";
import { mapActions, mapMutations, mapGetters } from "vuex";
export default {
  components: {
    selectTree: require("./select-tree.vue").default,
  },
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
    // 设备类型， 1：摄像机， 2：wifi, 3: RFID, 4: 电围
    deviceType: {
      type: Number,
      default: 1,
    },
    // 组织机构显示标识
    showOrganization: {
      type: Boolean,
      default: false,
    },
    // 查询组织用户来源 视频监控查询组织 不允许 查询到其他用户
    orgin: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      modalShow: false,
      loading: false,
      formData: {
        name: "",
        userName: "",
        roleId: "",
        orgIdList: [],
      },
      dialogData: {
        title: "选择用户",
        rWidth: 1600,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 10,
      },
      total: 0,
      columns: [
        { title: "选择", width: 65, type: "selection", key: "index" },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "姓名", key: "name" },
        { title: "用户名", key: "username" },
        { title: "角色", key: "roleNames" },
      ],
      tableData: [],
      selectTableData: [],
      selectOrgTree: {
        orgCode: null,
      },
      treeData: [],
      custormNodeData: {
        label: "未分配组织机构",
        orgCode: "-1",
      },
      roleSelectList: [],
    };
  },
  created() {
    this.setTreeData();
    this.roleList();
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  methods: {
    handleQuery() {
      this.pageInfo.pageNumber = 1;
      this.init();
    },
    init() {
      this.loading = true;
      // 此处 新查询接口 不会考虑当前用户组织 从而返回全部用户
      // 旧查询接口 考虑组织信息 只返回组织权限之内的用户
      if (this.orgin !== "powerInner") {
        this.userPageListNew();
      } else {
        this.userPageList();
      }
    },
    // 用户查询 新接口
    userPageListNew() {
      userPageListNew({
        ...this.formData,
        ...this.pageInfo,
      })
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.tableData = entities;
          this.tableIsSelect();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 用户查询 旧接口
    userPageList() {
      userPageList({
        ...this.formData,
        ...this.pageInfo,
      })
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.tableData = entities;
          this.tableIsSelect();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 组织机构
    handleCheck(list) {
      this.formData.orgIdList = list;
    },
    /**
     * table回显
     */
    tableIsSelect() {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.$refs.table.objData;
        // 清空table选中状态
        if (this.selectTableData.length == 0) {
          Object.keys(obj).forEach((key) => {
            obj[key]._isChecked = false;
          });
          return;
        }
        // 回显
        Object.keys(obj).forEach((key) => {
          var row = this.selectTableData.find((i) => {
            return obj[key].id == i.id;
          });
          if (row) {
            this.$refs.table.objData[key]._isChecked = true;
            this.$refs.table.objData[key]._isDisabled = row.disabled || false;
          }
        });
      }, 20);
    },
    /**
     * 显示model
     */
    show(list = []) {
      this.modalShow = true;
      this.selectTableData = JSON.parse(JSON.stringify(list)); //防止数据浅拷贝，改变父组件
      this.selectTableData = this.selectTableData.map((item) => {
        this.$set(item, "disabled", item.disabled || false);
        return item;
      });
      this.init();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    /**
     * 右侧清空
     */
    removeAllHandle() {
      this.$refs.table.selectAll(false);
      this.selectTableData = [];
      this.$emit("selectData", []);
    },

    /**
     * 确定按钮
     */
    confirmHandle() {
      var ids = [];
      let list = this.selectTableData.map((item) => item);
      if (!list.length) {
        this.$Message.warning("请选择用户");
        return;
      }
      this.modalShow = false;
      this.$emit("selectData", list);
      // this.selectTableData.forEach(item => {
      //   ids.push(item.deviceId)
      // })
      // this.$emit("selectData", ids)
      this.handleCancel();
    },
    // 取消
    handleCancel() {
      this.$refs["formData"].resetFields();
      this.$refs.selectTree.resetCheck();
      // this.$refs.selectTree.init();
    },
    /**
     * table 选中一项
     */
    onSelect(selection, row) {
      var obj = this.selectTableData.find((item) => {
        return item.id == row.id;
      });
      row.select = true;
      if (!obj) {
        this.selectTableData.push(row);
      } else {
        obj.select = true;
      }
    },
    /**
     * table 取消选中一项
     */
    onSelectCancel(selection, row) {
      var num = this.selectTableData.findIndex((item) => {
        return item.id == row.id;
      });
      this.selectTableData.splice(num, 1);
    },
    /**
     * table 全选
     */
    onSelectAll(selection) {
      selection.forEach((item) => {
        item.select = true;
        var obj = this.selectTableData.find((itm) => {
          return itm.id == item.id;
        });
        if (!obj) {
          this.selectTableData.push(item);
        }
      });
    },

    /**
     * table 取消全选
     */
    onSelectAllCancel(selection) {
      this.tableData.forEach((item) => {
        var num = this.selectTableData.findIndex((itm) => {
          return itm.id == item.id;
        });
        if (num != -1) {
          this.selectTableData.splice(num, 1);
        }
      });
      // 用于回显不可取消的项
      selection.map((item) => {
        item.disabled = true;
        item.select = true;
      });
      this.selectTableData = [...selection];
    },
    /**
     * 表格右侧 已选中内容操作
     */
    selectChange(row, index) {
      var obj = this.$refs.table.objData;
      if (row.select) {
        // 选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].id == row.id) {
            obj[key]._isChecked = true;
          }
        });
      } else {
        // 取消选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].id == row.id) {
            obj[key]._isChecked = false;
          }
        });
      }
      this.$nextTick(() => {
        this.selectTableData.splice(index, 1);
      });
    },
    // 组织结构树数据
    setTreeData() {
      queryUserDatascopeListNew({}).then((res) => {
        this.treeData = res.data;
        this.$nextTick(() => {
          // this.$refs.selectTree.init();
        });
      });
    },
    roleList() {
      let params = {
        roleName: "",
        pageNumber: 1,
        pageSize: 10,
      };
      rolePageList(params).then((res) => {
        this.roleSelectList = res.data.entities;
      });
    },
    /**
     * 选择组织机构树
     */
    selectedOrgTree() {},
    /**
     * 重置表单
     */
    resetForm() {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.$refs["formData"].resetFields();
      this.$refs.selectTree.resetCheck();
      this.init();
    },
  },
};
</script>
<style lang="less" scoped>
.find {
  margin-right: 10px;
}

/deep/ .ivu-input-wrapper {
  width: 100% !important;
}
.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 20px 20px;
  display: flex;
  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
  }
  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    height: 400px;
    position: relative;
  }
  .organization {
    width: 240px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 472px;
    overflow: auto;
    border-right: 1px solid #d3d7de;

    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      background: #f9f9f9;
    }
  }
  .preview-select-label-content {
    width: 250px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.del-btn {
  cursor: pointer;
}
.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 20px;
}

/deep/ .ivu-icon {
  // color: #fff;
}

.form {
  width: 100%;
  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }
  /deep/ .ivu-input {
    width: 160px;
  }
  /deep/.ivu-select {
    width: 160px;
  }
  /deep/.ivu-form-item {
    margin-bottom: 0;
    margin-right: 15px;
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
  // /deep/ .ivu-form-item-content{
  //   float: left;
  // }
  // .btn-group {
  //   margin-right: 0;
  // }
}
</style>
