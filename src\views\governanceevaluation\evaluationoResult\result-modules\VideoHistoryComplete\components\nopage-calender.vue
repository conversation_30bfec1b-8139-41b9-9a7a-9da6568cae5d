<template>
  <div class="nopage-calender auto-fill">
    <div class="nopage-calender-header">{{ startDate }}-{{ endDate }}</div>
    <div class="calender-legend">
      <span v-for="(legendItem, legendIndex) in legendData" :key="`legend${legendIndex}`" class="mr-md">
        <span class="disc" :style="{ background: legendItem.color }"></span>
        <span>{{ legendItem.text }}</span>
      </span>
    </div>
    <div class="nopage-calender-content auto-fill">
      <ul class="week-list mb-sm">
        <li v-for="(item, index) in weekList" :key="index" class="square week-square">
          {{ item._label }}
        </li>
      </ul>
      <ul class="day-list">
        <li v-for="(item, index) in dayList" :key="index" class="square mb-sm">
          <div class="day-list-span day-square" :style="{ backgroundColor: getBg(item) }">
            <slot name="calenderDay" :item="item">
              <span>{{ item.day }}</span>
              <span class="month-tip" v-if="item.day === '01'">{{ item.month }}月</span>
            </slot>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'nopage-calender',
  props: {
    startDate: {
      type: String,
      default: '2023-02-01',
    },
    endDate: {
      type: String,
      default: '2023-3-28',
    },
    hiatus: {
      type: Array,
      default: () => [],
    },
    normal: {
      type: Array,
      default: () => [],
    },
    legendData: {
      type: Array,
      default: () => [
        { text: '录像完整', color: 'var(--color-success)', key: 'success' },
        { text: '录像缺失', color: 'var(--color-failed)', key: 'hiatus' },
      ],
    },
  },
  data() {
    return {
      weekList: [
        {
          _label: '一',
          _value: '1',
        },
        {
          _label: '二',
          _value: '2',
        },
        {
          _label: '三',
          _value: '3',
        },
        {
          _label: '四',
          _value: '4',
        },
        {
          _label: '五',
          _value: '5',
        },
        {
          _label: '六',
          _value: '6',
        },
        {
          _label: '日',
          _value: '7',
        },
      ],
      dayList: [],
      startMonth: '',
    };
  },
  mounted() {
    this.handleDate();
  },
  methods: {
    getBg(item) {
      if (this.hiatus.includes(item.date)) {
        return this.legendData.find((row) => row.key === 'hiatus').color;
      } else if (this.normal.includes(item.date)) {
        return this.legendData.find((row) => row.key === 'normal').color;
      } else {
        return this.legendData.find((row) => row.key === 'success').color;
      }
    },
    getDate(datestr) {
      let temp = datestr.split('-');
      if (temp[1] === '01') {
        temp[0] = parseInt(temp[0], 10) - 1;
        temp[1] = '12';
      } else {
        temp[1] = parseInt(temp[1], 10) - 1;
      }
      //new Date()的月份入参实际都是当前值-1
      let date = new Date(temp[0], temp[1], temp[2]);
      return date;
    },
    getDiffDate(start, end) {
      let startTime = this.getDate(start);
      let endTime = this.getDate(end);
      let dateArr = [];
      while (endTime.getTime() - startTime.getTime() >= 0) {
        let year = startTime.getFullYear();
        let month =
          (startTime.getMonth() + 1).toString().length === 1
            ? '0' + (parseInt(startTime.getMonth().toString(), 10) + 1)
            : startTime.getMonth() + 1;
        let day = startTime.getDate().toString().length === 1 ? '0' + startTime.getDate() : startTime.getDate();
        dateArr.push({
          date: year + '-' + month + '-' + day,
          year,
          month,
          day,
        });
        // dateArr.push(year + "-" + month + "-" + day);
        startTime.setDate(startTime.getDate() + 1);
      }
      return dateArr;
    },
    // 获取当前年月有多少天
    getDays(Year, Month) {
      let days = new Date(Year, Month, 0).getDate();
      return days;
    },
    // 获取当前年月日是周几
    getWeekday(Year, Month, Day = 1) {
      const week = new Date(Year, Month - 1, Day).getDay();
      return week;
    },
    // 补全开始日期之前的日期
    getPreviousMonthDay(Year, Month, Day) {
      // 开始日期是星期几
      const firstWeekDay = this.getWeekday(Year, Month, Day);
      // 一周的第一天从周日开始，设置周日的索引值为0
      const fillPreviousDays = firstWeekDay === 0 ? 7 : firstWeekDay;
      // 需要补入的日期是当年还是上一年
      const preYear = Number(Month) === 1 && Number(Day) === 1 ? Year - 1 : Year;
      // 需要补入的日期所在月有多少天
      let preMonthDays = 0;
      let startItem = 0;
      if (Number(Day) > 1) {
        startItem = 1;
        preMonthDays = this.getDays(preYear, Month);
      } else {
        startItem = 0;
        preMonthDays = this.getDays(preYear, Month - 1);
      }
      // 需要补入的日期所在月在当月还是上一月
      let month = 0;
      month = Number(Day) <= preMonthDays && Number(Day) !== 1 ? Month : Month - 1;
      // 开始日期的前一天（当月：开始日期的前一天；  上一月：上一月的最后一天）
      const preStartDay = Number(Day) === 1 ? preMonthDays : Number(Day);
      for (let i = startItem, len = fillPreviousDays - 1 + startItem; i < len; i++) {
        const date = this.$util.common.formatDate(
          new Date(
            `${preYear}`,
            `${Number(month) === 1 ? 11 : month - 1}`, // 参数为月份， 0 到 11 之间的整数值，表示从一月到十二月
            `${preStartDay - i}`,
          ),
          'yyyy-MM-dd',
        );
        const dateArr = date.split('-');
        this.dayList.unshift({
          date,
          year: dateArr[0],
          month: dateArr[1],
          day: dateArr[2],
          _notThisMonth: true,
          _disabled: true,
        });
      }
    },
    // 补全结束日期之后的日期
    getNextMonthDay(Year, Month, Day) {
      // 结束日期是周几的索引值
      const weekIndex = this.getWeekday(Year, Month, Day) - 1;
      // 结束日期后面需要补全日期的天数
      let nextTotalDay = 7 - weekIndex;
      // 获取结束日期所在月的天数
      const endMonthDays = this.getDays(Year, Month);
      // 判断结束日期是否为当月还是下一个月
      let endMonth = 0;
      endMonth = Number(Day) <= endMonthDays ? Month : Month + 1;
      // 若月份为12，且为12月的最后一天，则补全日期在下一年
      const endYear = endMonth === 12 && Day === endMonthDays ? Year + 1 : Year;

      for (let i = 1, len = nextTotalDay; i < len; i++) {
        const date = this.$util.common.formatDate(
          new Date(
            `${endYear}`,
            `${endMonth === 12 ? 0 : Number(Day) < endMonthDays ? endMonth - 1 : endMonth}`, // 参数为月份， 0 到 11 之间的整数值，表示从一月到十二月
            `${Number(Day) < endMonthDays ? Number(Day) + i : i}`,
          ),
          'yyyy-MM-dd',
        );
        const dateArr = date.split('-');
        this.dayList.push({
          date,
          year: dateArr[0],
          month: dateArr[1],
          day: dateArr[2],
          _notThisMonth: true,
          _disabled: true,
        });
      }
    },
    handleDate() {
      this.dayList = this.getDiffDate(this.startDate, this.endDate);
      const startDateArr = this.startDate.split('-');
      const endDateArr = this.endDate.split('-');
      this.startMonth = startDateArr[1];
      this.getPreviousMonthDay(startDateArr[0], startDateArr[1], startDateArr[2]);
      this.getNextMonthDay(endDateArr[0], endDateArr[1], endDateArr[2]);
    },
  },
  watch: {
    startDate() {
      this.handleDate();
    },
    endDate() {
      this.handleDate();
    },
  },
};
</script>

<style lang="less" scoped>
.nopage-calender {
  width: 334px; /*no*/
  max-height: 345px; /*no*/
  //border: 1px solid #2169B9;
  //background: #0B2348;
  overflow-y: hidden !important;

  &-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px;
    padding: 0 20px;
    font-size: 12px;
    font-weight: normal;
    //color: #FFFFFF;
    //border-bottom: 1px solid #2169B9;
  }

  .calender-legend {
    display: flex;
    align-items: center;
    width: 100%;
    height: 36px;
    padding: 0 20px;
    font-size: 12px;

    .disc {
      display: inline-block;
      width: 10px;
      height: 10px;
      margin-right: 6px;
      border-radius: 50%;
    }
  }

  &-content {
    padding: 0 8px;

    .week-list,
    .day-list {
      display: flex;
      flex-wrap: wrap;
      font-size: 12px;

      li {
        width: calc(100% / 7);
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center !important;
      }
    }

    .day-list {
      //flex: 1;
      overflow-y: auto;

      &-span {
        position: relative;
        width: 24px; /*no*/
        height: 24px; /*no*/
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
      }
      .month-tip {
        position: absolute;
        right: -21px; /*no*/
        top: 0; /*no*/
        display: inline-block;
        padding: 0;
        //color: #FFFFFF;
        font-size: 12px;
        //background: #155280;
        border-radius: 4px;
        z-index: 9999;
        //border: 1px solid #2169B9;
      }
    }

    //.no-active {
    //  color: #56789C;
    //}

    .day-square {
      color: #ffffff;
      border-radius: 50%;
    }
  }
}
[data-theme='dark'] {
  .nopage-calender {
    border: 1px solid #2169b9;
    background: #0b2348;
    &-header {
      color: #ffffff;
      border-bottom: 1px solid #2169b9;
    }
    &-content {
      padding: 0 8px;
      .day-list {
        .month-tip {
          color: #ffffff;
          background: #155280;
        }
      }
      .no-active {
        color: #56789c;
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .nopage-calender {
    //border: 1px solid rgba(0, 21, 41, 0.15);
    background: #ffffff;
    &-header {
      color: #001529;
      border-bottom: 1px solid rgba(0, 21, 41, 0.15);
    }
    &-content {
      //padding: 0 8px;
      .week-list {
        color: var(--color-primary);
      }
      .day-list {
        .month-tip {
          color: #ffffff;
          background: var(--color-primary);
        }
      }
      .no-active {
        color: rgba(0, 0, 0, 0.8);
      }
    }
  }
}
</style>
