<template>
  <div class="detail-content auto-fill">
    <Form class="form" ref="form" autocomplete="off" label-position="right" :model="formData" :rules="formRules">
      <FormItem class="form-item" label="检测对象" required>
        <api-area-tree
          class="api-area-tree"
          :disabled="true"
          :select-tree="selectAreaTree"
          placeholder="请选择检测对象"
        ></api-area-tree>
      </FormItem>
      <FormItem class="form-item" label="检测方式" required>
        <Select class="width-lg" v-model="formData.detectMode" placeholder="请选择检测方式" disabled>
          <Option v-for="item in wayList" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem class="form-item" label="检测计划" prop="cronNum" required>
        <p class="check-plan base-text-color">
          <span>每</span>
          <Input v-model="formData.cronNum" @on-blur="handleBlurTime" class="ml-xs mr-xs check-time" />
          <Select class="sign" v-model="formData.cronType" transfer>
            <Option value="4">分</Option>
            <Option value="5">时</Option>
          </Select>
          <span class="ml-xs">检测一次</span>
        </p>
      </FormItem>
      <FormItem class="form-item" label="检测不合格最大复检次数" prop="checkCount">
        <InputNumber v-model.number="formData.checkCount" :min="0" :precision="0" class="mr-sm width-lg"></InputNumber>
      </FormItem>
      <FormItem class="form-item" label="统计范围" required>
        <Select class="width-lg" v-model="formData.statisticsRange" placeholder="请选择统计范围">
          <Option v-for="item in statisticRange" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem class="form-item" label="接口返回最大结果数量" required prop="maxNum">
        <Input v-model="formData.maxNum" class="width-lg" placeholder="请输入接口返回最大结果数量" />
        <span class="base-text-color ml-xs">张</span>
      </FormItem>
      <FormItem class="form-item" label="接口最长等待时长" required prop="waitTime">
        <Input v-model="formData.waitTime" class="width-lg" placeholder="请输入接口最长等待时长" />
        <span class="base-text-color ml-xs">秒</span>
      </FormItem>
    </Form>
    <ui-table class="ui-table auto-fill" :table-columns="tableInfoColumns" :table-data="formData.apis">
      <template #dataList="{ row, index }">
        <template v-if="row.dataList && row.dataList.length">
          <div class="img-box" @click="selectTestData(row, index)" title="点击更改数据">
            <div class="num">{{ row.dataList.length }}</div>
            <ui-image :src="row.dataList[0].url" />
          </div>
        </template>
        <div v-else>
          <Button type="text" @click="selectTestData(row, index)">请选择</Button>
        </div>
      </template>
      <template #action="{ row, index }">
        <Button type="text" :disabled="row.dataList && !row.dataList.length" @click="onClickClear(row, index)"
          >清除</Button
        >
      </template>
    </ui-table>
    <select-data
      v-model="selectDataShow"
      :modal-action="modalAction"
      :search-params="searchTestData"
      :default-checked="defaultChecked"
      @query="selectedData"
      @reset="reset"
    >
      <template #searchModule>
        <ui-label class="inline" label="姓名">
          <Input class="width-sm" v-model="searchTestData.name" placeholder="请输入姓名"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="证件号">
          <Input class="width-sm" v-model="searchTestData.idCard" placeholder="请输入证件号"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="所属地">
          <api-area-tree
            :select-tree="selectTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
      </template>
      <template #message="{ item }">
        <p class="text">
          <span class="inline vt-middle base-text-color ml-xs">{{ item.name }}</span>
        </p>
      </template>
    </select-data>
  </div>
</template>
<script>
import fieldData from '../../common-form/field';
import { mapGetters } from 'vuex';
export default {
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      selectAreaTree: {
        regionCode: '',
      },
      formData: {
        detectMode: '3',
        statisticsRange: '1',
        cronNum: '',
        cronType: '4',
        maxNum: '',
        waitTime: '',
        apis: [],
        checkCount: 0, //最大复检次数
      },
      tableInfoColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          type: 'index',
          title: '序号',
          width: 50,
          align: 'center',
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'center',
        },
        {
          title: '测试数据',
          slot: 'dataList',
          align: 'center',
        },
        {
          title: '操作',
          width: 80,
          align: 'center',
          slot: 'action',
        },
      ],
      formRules: {
        cronNum: [
          {
            required: true,
            message: '请填写检测计划',
          },
        ],
        maxNum: [
          {
            required: true,
            message: '请输入接口返回最大结果数量',
          },
        ],
        waitTime: [
          {
            required: true,
            message: '请输入接口最长等待时长',
          },
        ],
        apis: [
          {
            required: true,
            type: 'array',
            message: '请选择检测接口',
          },
        ],
        checkCount: [{ required: true, type: 'number', message: '请输入检测不合格最大复检次数', trigger: 'blur' }],
      },
      selectDataShow: false,
      modalAction: {
        title: '选择实有人口',
        action: 'add',
      },
      selectTree: {
        regionCode: '',
      },
      searchTestData: {
        name: '',
        idCard: '',
        type: 1,
        civilCode: '',
        pageNumber: 1,
        pageSize: 20,
      },
      defaultChecked: [],
      ...fieldData.data(),
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      return this.$refs.form.validate(async (valid) => {
        if (valid) {
          return true;
        } else {
          this.$Message.error('请填写必须要填写的信息');
          return false;
        }
      });
    },
    handleBlurTime() {
      // 此处为后端接口要求
      this.formData.cronData = [parseInt(this.formData.cronNum)];
    },
    onClickClear(row, index) {
      this.selectDataIndex = index;
      this.selectedData([]);
    },
    selectTestData(row, index) {
      this.selectDataIndex = index;
      this.defaultChecked = row.dataList ? row.dataList.map((row) => row.id) : [];
      const org = this.initialOrgList.find((rw) => rw.orgCode === row.orgCode);
      this.selectTree.regionCode = org.regionCode;
      this.searchTestData.civilCode = org.regionCode;
      this.$nextTick(() => {
        this.selectDataShow = true;
      });
    },
    selectedArea(area) {
      this.searchTestData.civilCode = area.regionCode;
    },
    selectedData(data) {
      const api = this.$util.common.deepCopy(this.formData.apis[this.selectDataIndex]);
      api.dataList = data.map((row) => {
        return {
          id: row.id,
          url: row.url,
        };
      });
      this.formData.apis.splice(this.selectDataIndex, 1, api);
    },
    reset() {
      this.selectTree.regionCode = '';
      this.searchTestData = {
        name: '',
        idCard: '',
        type: 1,
        civilCode: '',
        pageNumber: 1,
        pageSize: 20,
      };
    },
  },
  watch: {
    moduleAction: {
      handler(val) {
        if (!val) return;
        this.selectAreaTree.regionCode = val.regionCode;
      },
      deep: true,
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          Object.assign(this.formData, this.configInfo);
        } else {
          /**
           * 初始化默认加载该组织机构和该组织机构下所有子集 by 景瑞
           */
          const org = this.initialOrgList.find((row) => row.regionCode === this.moduleAction.regionCode);
          const orgList = this.initialOrgList.filter((row) => row.parentId === org.id);
          this.formData.apis.push({
            orgCode: org.orgCode,
            orgName: org.orgName,
            dataList: [],
          });
          orgList.forEach((row) => {
            this.formData.apis.push({
              orgCode: row.orgCode,
              orgName: row.orgName,
              dataList: [],
            });
          });
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    SelectData: require('../components/select-data.vue').default,
    UiImage: require('@/components/ui-image.vue').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 260px;
.form-item {
  @{_deep} .ivu-form-item-error-tip {
    margin-left: @leftMargin;
  }
  @{_deep}.ivu-form-item-label {
    width: @leftMargin;
  }
}
.detail-content {
  height: 700px;
  .check-time {
    width: 60px;
  }
  .sign {
    width: 80px;
  }
  .img-box {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    position: relative;
    padding: 5px 0;
    cursor: pointer;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
    .num {
      position: absolute;
      width: 20px;
      height: 20px;
      background-color: var(--color-primary);
      color: #ffffff;
      z-index: 11;
    }
  }
}
</style>
