<!--
    * @FileDescription: 选择警情、案件
    * @Author: H
    * @Date: 2024/04/10
 * @LastEditors: zhengmingming zhengmingming
 * @LastEditTime: 2024-06-06 11:20:14
 -->
<template>
  <ui-modal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onCancel="handleCancel"
    @onOk="confirmHandle"
  >
    <div class="modal-box">
      <Form
        inline
        ref="formData"
        :model="formData"
        class="form"
        @submit.native.prevent
      >
        <Row>
          <Col span="8">
            <FormItem label="" prop="keys">
              <Input
                placeholder="请输入关键词检索，多个关键词请用空格隔开"
                v-model="formData.keys"
                clearable
                maxlength="50"
                class="search-input-430"
              >
                <Button slot="append" type="primary" @click="handleQuery()"
                  >搜索</Button
                >
              </Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
    <div class="select-label-container modal-list-has-footer-content">
      <div class="organization">
        <div class="title">全部类型</div>
        <div class="all_list">
          <ul class="type_show">
            <li
              class="type_li"
              :class="{ active_type_li: caseIndex == 0 }"
              @click="handleSelectCase(0)"
            >
              <div>警情</div>
              <div class="type_li_num">{{ partiNum }}</div>
            </li>
            <li
              class="type_li"
              :class="{ active_type_li: caseIndex == 1 }"
              @click="handleSelectCase(1)"
            >
              <div>案件</div>
              <div class="type_li_num">{{ caseNum }}</div>
            </li>
          </ul>
        </div>
      </div>
      <div class="select-label-content">
        <Form inline ref="formData2" :model="formData" class="form">
          <Row>
            <Col span="14">
              <FormItem
                :label="caseIndex == 1 ? `发现时间:` : `报警时间:`"
                prop="timeSlot"
              >
                <hl-timerange
                  ref="timerange"
                  @onceChange="handleTimeOnce"
                  @change="handleTimeChange"
                  :reflectValue="formData.timeSlot"
                  :reflectTime="{
                    startDate: formData.startDate,
                    endDate: formData.endDate,
                  }"
                >
                </hl-timerange>
              </FormItem>
            </Col>
            <Col span="8">
              <FormItem
                :label="caseIndex == 1 ? `案件类型:` : `警情类型:`"
                prop="caseType"
              >
                <Select
                  placeholder="请选择"
                  v-model="formData.caseType"
                  @on-change="handleQuery()"
                  filterable
                  clearable
                >
                  <Option
                    v-for="item in typeList"
                    :value="item.dataKey"
                    :key="item.dataKey"
                    placeholder="请选择"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <Table
          class="auto-fill table"
          ref="table"
          :height="380"
          width="930"
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #loading>
            <ui-loading></ui-loading>
          </template>
          <template slot="ajlb" slot-scope="{ row }">
            <span>{{ row.ajlb | commonFiltering(caseTypeList) }}</span>
          </template>
          <template slot="jjlx" slot-scope="{ row }">
            <span>{{ row.jjlx | commonFiltering(partiTypeList) }}</span>
          </template>
          <template #labels="{ row }">
            <ui-tag-poptip
              v-if="row.labels && row.labels.length"
              :data="row.labels"
            />
          </template>
        </Table>
        <ui-page
          :current="pageInfo.pageNumber"
          :total="total"
          :page-size="pageInfo.pageSize"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{
              policeTableData.length + caseTableData.length
            }}</span>
            个</span
          >
          <span class="del-btn" @click="removeAllHandle">
            <i class="iconfont icon-shanchu"></i>清空
          </span>
        </div>
        <div class="label-container">
          <div class="type_title">
            <i class="iconfont icon-jingqing range"></i>
            <p class="type_name">
              警情 ( <span>{{ policeTableData.length }}</span> )
            </p>
          </div>
          <ul>
            <list
              v-for="(item, index) in policeTableData"
              @deleList="handleDele(index, 0)"
              :key="index"
              :rowObj="item"
            >
            </list>
          </ul>
          <div class="type_title">
            <i class="iconfont icon-anjian range"></i>
            <p class="type_name">
              案件 ( <span>{{ caseTableData.length }}</span> )
            </p>
          </div>
          <ul>
            <list
              v-for="(item, index) in caseTableData"
              :key="index"
              @deleList="handleDele(index, 1)"
              :rowObj="item"
            >
            </list>
          </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { caseCount, queryCasePage } from "@/api/modelMarket";
import { mapGetters } from "vuex";
import { commonMixins } from "@/mixins/app.js";
import list from "@/components/model-market/list.vue";
import { caseColumns, alarmColumns } from "./case-alarm-columns";
const defaultColumns = [
  { title: "选择", width: 65, type: "selection", key: "index" },
  { title: "序号", width: 70, type: "index", key: "index" },
];

const defaultformData = {
  keys: "",
  startDate: "", // 抓拍时段 - 开始时间
  endDate: "", // 抓拍时段 - 结束时间
  timeSlot: "近一周",
  caseType: "",
};
export default {
  components: {
    selectTree: require("./select-tree.vue").default,
    list,
  },
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
    // 设备类型， 1：摄像机， 2：wifi, 3: RFID, 4: 电围
    deviceType: {
      type: Number,
      default: 1,
    },
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
  },
  mixins: [commonMixins], //全局的mixin
  data() {
    return {
      modalShow: false,
      formData: {
        ...defaultformData,
      },
      caseFormData: {},
      partiFormData: {},
      caseIndex: 0,
      dialogData: {
        title: "选择警情、案情",
        rWidth: 1600,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 3,
      columns: [],
      tableData: [],
      selectTableData: [],
      caseTableData: [], //案件
      policeTableData: [], //警情
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: {
        label: "未分配组织机构",
        orgCode: "-1",
      },
      isAll: false,
      treeProps: {
        label: "orgName",
        isLeaf: "isLeaf",
        children: "children",
      },
      tableLoading: false,
      caseNum: 0,
      partiNum: 0,
    };
  },
  mounted() {
    this.caseFormData = { ...this.formData };
    this.partiFormData = this.formData;
  },
  computed: {
    typeList() {
      return this.caseIndex === 0 ? this.partiTypeList : this.caseTypeList;
    },
    ...mapGetters({
      caseTypeList: "dictionary/getCaseTypeList", //案件类型
      partiTypeList: "dictionary/getPartiTypeList", // 警情类型
    }),
  },
  methods: {
    initNum() {
      const { keys } = this.formData;
      [1, 2].forEach((type) => {
        const params = type === 1 ? this.partiFormData : this.caseFormData;
        caseCount({ ...params, keys, type }).then((res) => {
          if (type === 1) this.partiNum = res.data || 0;
          else this.caseNum = res.data || 0;
        });
      });
    },
    init() {
      this.tableData = [];
      var param = {
        ...this.formData,
        ...this.pageInfo,
        type: this.caseIndex == 0 ? 1 : 2,
      };
      queryCasePage(param).then((res) => {
        if (res.data) {
          const { total, entities } = res.data;
          this.total = total;
          this.tableData = entities;
        }
        // this.tableIsSelect()
      });
    },
    // 右侧删除
    handleDele(index, type) {
      if (type == 0) {
        this.policeTableData.splice(index, 1);
      } else {
        this.caseTableData.splice(index, 1);
      }
    },
    /**
     * table回显
     */
    tableIsSelect() {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.$refs.table.objData;
        // 清空table选中状态
        if (this.policeTableData.length == 0) {
          Object.keys(obj).forEach((key) => {
            obj[key]._isChecked = false;
          });
          return;
        }
        // 回显
        Object.keys(obj).forEach((key) => {
          var row = this.policeTableData.find((i) => {
            return obj[key].jjbh == i.jjbh || obj[key].ajbh == i.ajbh;
          });
          if (row) {
            this.$refs.table.objData[key]._isChecked = true;
          }
        });
      }, 20);
    },
    /**
     * 显示model
     */
    show(plist = [], clist = []) {
      this.policeTableData = plist.map((item) => ({ ...item }));
      this.caseTableData = clist.map((item) => ({ ...item }));
      this.modalShow = true;
      this.handleQuery();
      this.tableIsSelect();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    handleSelectCase(index) {
      if (this.caseIndex === index) {
        return;
      }
      const keys = this.formData.keys;
      if (this.caseIndex === 0) {
        this.formData = this.caseFormData;
      } else {
        this.formData = this.partiFormData;
      }
      this.formData.keys = keys;
      this.caseIndex = index;
      this.handleQuery(false);
    },
    queryTableData() {
      this.pageInfo.pageNumber = 1;
      this.columns = [
        ...defaultColumns,
        ...(this.caseIndex === 0 ? alarmColumns : caseColumns),
      ];
      this.init();
    },
    handleQuery(loadNum = true) {
      loadNum && this.initNum();
      this.queryTableData();
    },
    // 时间
    handleTimeOnce(obj) {
      this.formData.timeSlot = obj.timeSlot;
      this.formData.startDate = obj.startDate;
      this.formData.endDate = obj.endDate;
    },
    handleTimeChange(obj) {
      this.formData.timeSlot = obj.timeSlot;
      this.formData.startDate = obj.startDate;
      this.formData.endDate = obj.endDate;
      this.handleQuery();
    },
    /**
     * 右侧清空
     */
    removeAllHandle() {
      this.selectTableData = [];
      this.policeTableData = [];
      this.caseTableData = [];
      this.$refs.table.selectAll(false);
      // this.$emit("selectData", []);
    },

    /**
     * 确定按钮
     */
    confirmHandle() {
      this.modalShow = false;
      // 选择的数据
      let plist = this.policeTableData.map((item) => item);
      let cList = this.caseTableData.map((item) => item);
      this.$emit("selectData", plist, cList);
      this.$refs["formData"].resetFields();
    },
    // 取消
    handleCancel() {
      this.$refs["formData"].resetFields();

      this.isAll = false;
    },
    /**
     * table 选中一项
     */
    onSelect(selection, row) {
      if (this.caseIndex == 0) {
        //警情
        var obj = this.policeTableData.find((item) => {
          return item.jjbh == row.jjbh;
        });
        row.select = true;
        if (!obj) {
          this.policeTableData.push(row);
        } else {
          obj.select = true;
        }
      } else {
        // 案件
        var obj = this.caseTableData.find((item) => {
          return item.ajbh == row.ajbh;
        });
        row.select = true;
        if (!obj) {
          this.caseTableData.push(row);
        } else {
          obj.select = true;
        }
      }
    },
    /**
     * table 取消选中一项
     */
    onSelectCancel(selection, row) {
      if (this.caseIndex == 0) {
        //警情
        var num = this.policeTableData.findIndex((item) => {
          return item.jjbh == row.jjbh;
        });
        this.policeTableData.splice(num, 1);
      } else {
        //案件
        var num = this.caseTableData.findIndex((item) => {
          return item.ajbh == row.ajbh;
        });
        this.caseTableData.splice(num, 1);
      }
    },
    /**
     * table 全选
     */
    onSelectAll(selection) {
      selection.forEach((item) => {
        item.select = true;
        if (this.caseIndex == 0) {
          //警情
          var obj = this.policeTableData.find((itm) => {
            return itm.jjbh == item.jjbh;
          });
          if (!obj) {
            this.policeTableData.push(item);
          }
        } else {
          //案件
          var obj = this.caseTableData.find((itm) => {
            return itm.ajbh == item.ajbh;
          });
          if (!obj) {
            this.caseTableData.push(item);
          }
        }
      });
    },

    /**
     * table 取消全选
     */
    onSelectAllCancel(selection) {
      this.tableData.forEach((item) => {
        if (this.caseIndex == 0) {
          //警情
          var num = this.policeTableData.findIndex((itm) => {
            return itm.jjbh == item.jjbh;
          });
          if (num != -1) {
            this.policeTableData.splice(num, 1);
          }
        } else {
          //案件
          var num = this.caseTableData.findIndex((itm) => {
            return itm.ajbh == item.ajbh;
          });
          if (num != -1) {
            this.caseTableData.splice(num, 1);
          }
        }
      });
    },

    /**
     * 表格右侧 已选中内容操作
     */
    selectChange(row, index) {
      var obj = this.$refs.table.objData;
      if (row.select) {
        // 选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].ajbh == row.ajbh) {
            obj[key]._isChecked = true;
          }
        });
      } else {
        // 取消选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].ajbh == row.ajbh) {
            obj[key]._isChecked = false;
          }
        });
      }
      this.$nextTick(() => {
        if (this.caseIndex == 0) {
          //警情
          this.policeTableData.splice(index, 1);
        } else {
          //案件
          this.caseTableData.splice(index, 1);
        }
      });
    },
    /**
     * 重置表单
     */
    resetForm() {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 20,
      };
      // this.$refs["formData"].resetFields();
      this.$refs["formData2"].resetFields();
    },
    // 对比当前已选数据
    comparisonList() {
      this.$nextTick(() => {
        var obj = this.$refs.table.objData;
        let selectList = [];
        if (this.caseIndex == 0) {
          //警情
          selectList = new Map(
            this.policeTableData.map((item) => [item.jjbh, item])
          );
        } else {
          //案件
          selectList = new Map(
            this.caseTableData.map((item) => [item.ajbh, item])
          );
        }
        Object.keys(obj).forEach((key) => {
          if (selectList.get(obj[key].ajbh)) {
            obj[key]._isChecked = true;
          }
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.find {
  margin-right: 10px;
}

/deep/ .ivu-input-wrapper {
  width: 100% !important;
}

.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 0px 0px;
  display: flex;

  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;

    .manual {
      display: flex;
    }
  }

  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;

    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .label-container {
    overflow: auto;
    height: 400px;
    position: relative;

    .type_title {
      display: flex;
      align-items: center;

      .icon-jingqing {
        color: #ea4a36;
      }

      .icon-anjian {
        color: #4696fc;
      }

      .type_name {
        font-weight: 700;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.9);
        margin-left: 6px;

        span {
          color: #2c86f8;
        }
      }
    }
  }

  .organization {
    width: 240px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 472px;
    overflow: auto;
    border-right: 1px solid #d3d7de;

    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      background: #f9f9f9;
    }

    .all_list {
      height: 100%;

      .type_show {
        height: inherit;

        .type_li {
          display: flex;
          justify-content: space-between;
          padding: 7px 16px;
          color: rgba(0, 0, 0, 0.8);
          cursor: pointer;
          font-size: 14px;

          .type_li_num {
            font-weight: 700;
            color: #2c86f8;
          }
        }

        .active_type_li {
          background: #2c86f8;
          color: #fff;

          .type_li_num {
            font-weight: 700;
            color: #fff;
          }
        }
      }
    }
  }

  .preview-select-label-content {
    width: 350px;
    padding: 20px 7px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;

    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;

      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }

    .label-container {
      padding-top: 10px;
    }
  }
}

.del-btn {
  cursor: pointer;
}

.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 10px;
}

.form {
  width: 100%;

  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }

  /deep/.ivu-select {
    width: 100%;
  }

  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");
    display: flex;
    align-items: center;

    .ivu-form-item-content {
      flex: 1;

      .ivu-select {
        width: 100%;
      }
    }
  }

  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }

  /deep/ .ivu-form-item-content {
    display: flex;
  }
}

.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-size: 14px;
    color: #000;

    i {
      color: #23a8f9;
      margin-right: 10px;
    }
  }
}
</style>
