# 安装

- eslint 【js 代码的质量检查工具】
- prettier 【代码风格的约束】
- @babel/eslint-parser 【ESLint 的 Babel 解析器代替 babel-eslint】
- @vue/cli-plugin-eslint 【vue 专门的 ESLint 规则插件】
- eslint-config-prettier 【用 prettier 的规则，覆盖掉 eslint:recommended 的部分规则】
- eslint-plugin-prettier 【将 prettier 的能力集成到 eslint 中】
- eslint-plugin-vue 【用 ESLint 检查.vue 文件的<template>和<script>】

> **Link（参考）：** https://blog.csdn.net/dreamingbaobei3/article/details/124643312
