<template>
  <div>
    <ui-modal
      ref="uiModal"
      v-model="visible"
      :title="formData.indexName"
      :footer-hide="isView"
      :styles="styles"
      @query="commit"
      :loading="loading"
    >
      <component
        ref="formData"
        :is="componentsMap[formData.indexType] || 'CommonForm'"
        :formModel="formModel"
        :formData="form"
      >
      </component>
    </ui-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { componentsMap } from '@/views/appraisaltask/indexmanagement/indexmanagement.js';
export default {
  name: 'common-model',
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {
      //指标
      form: {
        generalParamSettingJson: {},
      },
      //参数设置
      formParams: {},
      visible: false,
      lg: '1',
      time: 's',
      time_num: '',
      styles: {
        width: 'fit-content',
      },
      undisCriterion: ['VIDEO_OSD_CLOCK_ACCURACY', 'VIDEO_GENERAL_OSD_CLOCK_ACCURACY'],
      loading: false,
      indexTypeList: [],
    };
  },
  created() {},
  methods: {
    resetForm() {},
    async commit() {
      try {
        let params = await this.$refs.formData.validate();
        console.log('params', params);
        await this.updateEvaluationIndex(params); //修改指标
        this.visible = false;
        this.$emit('refresh', this.formData);
      } catch (error) {
        console.log(error);
      }
    },
    init() {
      try {
        this.resetForm();
        this.getEvaluationIndexById();
      } catch (error) {
        console.log(error);
      }
    },
    //根据标识符获取指标数据信息
    async getEvaluationIndexById() {
      try {
        let {
          data: { data },
        } = await this.$http.get(`${governanceevaluation.getEvaluationIndexById}/${this.formData.id}`);
        this.form = {
          ...data,
          generalParamSettingJson: JSON.parse(data.generalParamSettingJson || '{}'),
        };
      } catch (error) {
        console.log(error);
      }
    },
    //修改指标  达标值 设备合格率
    async updateEvaluationIndex(formData) {
      try {
        this.loading = true;
        /*
          if(!this.undisCriterion.includes(this.formData.indexType)){
            params.evaluationCriterion = this.form.evaluationCriterion
          }*/
        let params = {
          ...formData,
          generalParamSettingJson: JSON.stringify(formData.generalParamSettingJson),
        };
        let { data } = await this.$http.post(governanceevaluation.updateEvaluationIndex, params);
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) this.init();
    },
  },
  computed: {
    componentsMap() {
      return componentsMap;
    },
    isView() {
      return this.formModel === 'view';
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
    BasicFullDir: require('@/views/appraisaltask/indexmanagement/view/basic-full-dir.vue').default,
    BasicQuantityStandard: require('@/views/appraisaltask/indexmanagement/view/basic-quantity-standard.vue').default,
    BasicEmphasisQuantity: require('@/views/appraisaltask/indexmanagement/view/basic-emphasis-quantity.vue').default,
    FaceUrlAvailable: require('@/views/appraisaltask/indexmanagement/face/face-url-available.vue').default,
    CompositeIndex: require('@/views/appraisaltask/indexmanagement/view/composite-index.vue').default,
    VideoValidSubmitQuantity: require('@/views/appraisaltask/indexmanagement/video/video-valid-submit-quantity.vue')
      .default,
    VideoDeviceRevocation: require('@/views/appraisaltask/indexmanagement/video/video-device-revocation.vue').default,
    PortraitFileConfidenceRate: require('@/views/appraisaltask/indexmanagement/view/portrait-file-confidence-rate.vue')
      .default,
    VehicleFileConfidenceRate: require('@/views/appraisaltask/indexmanagement/view/vehicle-file-confidence-rate.vue')
      .default,
    BasicDeviceHundred: require('@/views/appraisaltask/indexmanagement/view/basic-device-hundred.vue').default,
  },
};
</script>
<style lang="less" scoped>
@import url('../components/common.less');
@{_deep} .ivu-modal-body {
  max-height: 600px;
  overflow-y: auto;
  padding: 0 50px 50px 50px;
}
@{_deep} .ivu-modal-header {
  padding: 50px 0 0 0;
}

/*@{_deep} .ivu-input-type-textarea {
  width: 380px;
}*/
.index-definition {
  display: inline-block;
  line-height: 20px;
  width: 380px;
}
.default-btn {
  padding: 0 30px;
}
//全量目录完整率
.modal-full-dir {
  width: 727px;
}
//数量达标率
.modal-num-standard {
  width: 915px;
}
//视频图像设备位置类型完整率
.modal-video-image {
  width: 698px;
}
//重点位置类型视频图像设备数量达标率
.modal-key-video {
  width: 926px;
}

//人脸卡口设备URL可用率
.face-device-url {
  width: 606px;
}
//重点人脸卡口设备URL可用率
.key-face-device-url {
  width: 606px;
}
//人脸卡口设备抓拍合格率 人脸卡口设备及时上传率	 重点人脸卡口设备及时上传率 人脸卡口设备时钟准确率
.face-device-passrate {
  width: 678px;
}
.margin-right {
  margin-right: 8px;
}
.params-tip {
  line-height: 28px;
  font-size: 14px;
  color: #e44f22;
  margin-bottom: 10px;
}
.params-suffix {
  display: inline-block;
  line-height: 34px;
}
.params-prefix {
  display: inline-block;
  color: #fff;
  line-height: 34px;
  margin-right: 5px;
}
.time-defference {
  @{_deep} .ivu-form-item-label {
    width: 170px !important;
  }
}
.pre-form-item-width {
  width: 330px;
}
</style>
