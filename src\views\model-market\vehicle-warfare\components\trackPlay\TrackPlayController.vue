<template>
    <div class="carfollow-control">
        <div class="map-panel">
            <div class="map-control-left">
                <span class="play-prev controlpanel-prev-icon" :title="prevTitle" @click="prev"></span>
                <span :class="['play-pause', isPlaying?'controlpanel-play-icon':'controlpanel-pause-icon']" :title="isPlaying ? '暂停' : '播放'" @click="toggle(false)"></span>
                <span class="play-next controlpanel-next-icon" :title="nextTitle" @click="next"></span>
            </div>
            <div class="map-slider" ref="slider">
                <div class="slider-container" :style="isKeyDown ? 'cursor: text' : ''">
                    <div class="slider-gauge"></div>
                    <div class="slider-area">
                        <span class="drag-button drag-button-left" @keydown="keepStyle">
                                <span class="slider-tip">
                                    <p></p>
                                </span>
                        </span>
                        <span class="drag-button drag-button-right" @keydown="keepStyle">
                                <span class="slider-tip">
                                    <p></p>
                                </span>
                        </span>
                        <div class="slide-move">
                            <span class="move-handler"></span>
                        </div>
                    </div>
                    <div class="slider-view">
                        <span class="view-handler"></span>
                    </div>         
                </div>
            </div>
            <div class="map-control-right">
                <span :class="['map-control-showall',isShowAll?'controlpanel-checked-icon':'controlpanel-nochecked-icon']"
                @click="onShowAllClick">显示全部</span>
                <span :class="['map-control-info',isShowPassInfo?'controlpanel-checked-icon':'controlpanel-nochecked-icon']"
                @click="onShowPassInfo">通行时间</span>
                <!-- <span class="play-helper controlpanel-helper-icon">
                                <span class="play-helper-info">蓝色区域表示轨迹绘制范围，支持两端调节和拖动</span>
                </span> -->
            </div>
        </div>
    </div>
</template>
<script>
    import Slider from './slider.js'
    import '../style/trackplayercontroller.css'
    export default {
        name: 'TrackPlayController',
        data() {
            return {
                isShowAll:false,
                isPlaying:false,
                isShowPassInfo:true,
                isKeyDown: false,
            }
        },
        props: {
            prevTitle:{
                type:String,
                default:'上一个'
            },
            nextTitle:{
                type:String,
                default:'下一个'
            },
            trackList:{
                type:Array,
                default(){return []}
            },
            currentStep:{
                type:Number,
                default:0
            },
            enterTrackNumber:{
                type:Number,
                default:19
            },
            externalOpts:{
                type:Object,
                default() {return {}}
            }
        },
        computed:{
            maxTime() {
                const list = this.trackList,len = list.length;
                if(list && len) {
                    return this.parseTime(list[len-1].absTime);
                }
                return 0;
            },
            minTime() {
                const list = this.trackList,len = list.length;
                if(list && len) {
                    return this.parseTime(list[0].absTime);
                }
                return 0;
            },
            timePoints() {
                const list = this.trackList,len = list.length;
                if(list && len) {
                    return list.map(item=>this.parseTime(item.absTime))
                }
                return [];
            },
            currentTime() {
                const list = this.trackList,len = list.length;
                if(list && len) {
                    return this.parseTime(len>this.enterTrackNumber?list[this.enterTrackNumber].absTime:list[len-1].absTime);
                }
                return 0;
            }
        },
        methods: {
            keepStyle() {
                this.isKeyDown = true;
            },
            parseTime(str) {
                if(typeof str === 'string') {
                    return Toolkit.str2mills(str);
                }
                return str;
            },
            onShowPassInfo() {
                this.isShowPassInfo = !this.isShowPassInfo;
                this.slider && this.slider.showOrHideTip(this.isShowPassInfo);
            },
            onShowAllClick() {
                this.isShowAll = !this.isShowAll;
                if(this.isShowAll) { 
                    this.slider && this.slider.positionHandle();
                }
                this.$emit('showAllChanged',this.isShowAll);
            },
            toggle(bool) {
                if(this.isShowAll) {
                    this.$Message.warning('显示全部模式下不支持')
                    return;
                }
                this.isPlaying = !this.isPlaying;
                !bool && this.$emit('sliderStateChange',this.isPlaying)
            },
            prev() {
                this.$emit('sliderPrev',this.isPlaying);
            },
            next() {
                this.$emit('sliderNext',this.isPlaying);
            },
            initSlider() {
                this.isPlaying = true;
                let options = {
                    $container: $(this.$refs.slider),
                    $shield: $('<div></div>'),
                    min: this.minTime,
                    values: this.timePoints,
                    isShowTip: true,
                    max: this.maxTime,
                    value: this.currentTime,
                    stop:(res)=> {
                        this.$emit('sliderRangeChange',res,this.isShowAll,this.isPlaying)
                    },
                    stopMove:()=> {

                    },
                    moveDroped:(percent)=> {
                        this.$emit('sliderPositionChange',percent,this.isPlaying)
                    }
                }
                this.slider = new Slider({
                    ...options,
                    ...this.externalOpts
                });
            },
            refresh(){
                this.slider && this.slider.destroy();
                this.initSlider(); 
            }
        },
        watch:{
            trackList() {
                this.refresh()
            },
            currentStep(val) {
                if(this.slider) {
                    this.slider.move(val);
                }
            },
            externalOpts() {
                this.refresh()
            }
        },
        mounted() {
            this.$nextTick(() => {
                this.initSlider();
            })
        },
    }
</script>
