export default {
  data() {
    return {
      ordinaryHistoryVideo: ['VIDEO_GENERAL_HISTORY_ACCURACY', 'VIDEO_HISTORY_COMPLETE_ACCURACY'], // 普通设备历史录像 [普通历史视频可调阅率,历史录像完整率]
      importantHistoryVideo: [
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_HISTORY_COMPLETE_ACCURACY',
      ], // 重点设备历史录像 [重点历史视频可调阅率,普通历史视频可调阅率,历史录像完整率]
      timeDeviation: ['VIDEO_GENERAL_CLOCK_ACCURACY', 'VIDEO_CLOCK_ACCURACY'], // 与标准时间允许的偏差 [普通时钟准确率, 重点时钟准确率]
      checkMethods: [
        'VIDEO_OSD_ACCURACY',
        'VIDEO_CLOCK_ACCURACY',
        'VIDEO_GENERAL_CLOCK_ACCURACY',
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_QUALITY_PASS_RATE',
        'VIDEO_QUALITY_PASS_RATE_RECHECK',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
      ], // [重点字幕标注合规率, 重点时钟准确率, 普通时钟准确率, 普通字幕标注合规率, 频质量合格率,视频流质量合格率（人工复核）, (重点)字幕标注合规性与时钟准确性]
      checkMethodsChildren: [
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_MONITOR_ONLINE_RATE_PROMOTE',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ], // [重点实时视频可调阅率, 重点历史视频可调阅率, 普通实时视频可调阅率, 普通历史视频可调阅率]
      qualityCheckMethods: [
        {
          dataKey: '0',
          dataValue: '人工检测',
        },
        {
          dataKey: '1',
          dataValue: '视频流质量检测算法 (传统模型)',
        },
        {
          dataKey: '2',
          dataValue: '第三方检测结果',
        },
        {
          dataKey: '3',
          dataValue: '视频流质量检测算法 (精简模型)',
        },
      ], //视频流质量合格率检测方法
      videoQualityCheckContent: [
        {
          text: '视频信号：检测视频是否有信号（黑蓝屏等）、视频是否冻结、视频是否剧变剧变（信号时有时无，变化急剧连续）',
          key: 'videoSignal',
        },
        {
          text: '视频清晰度：检测视频是否失焦，画面朦胧',
          key: 'videoClear',
        },
        {
          text: '视频亮度：检测视频亮度是否异常（过亮或过暗）',
          key: 'videoBrightness',
        },
        {
          text: '视频色彩：检测视频色彩是否异常',
          key: 'videoCcolorCast',
        },
        {
          text: '抖动：检测镜头抖动是否异常：往返、连续、急剧抖动',
          key: 'videoJitter',
        },
        {
          text: '干扰：检测视频是否存在噪声、雪花异常',
          key: 'videoInterference',
        },
        {
          text: '视频遮挡：检测视频是否有遮挡',
          key: 'videoOcclusion',
        },
      ],
      wangliVideoQualityCheckContent: [
        {
          text: '颜色异常：全屏幕黑屏、灰屏、绿屏或者半屏杂色覆盖',
          disabled: true,
        },
        {
          text: '视角异常：视角过低导致照射面积过小',
          disabled: true,
        },
        {
          text: '焦距模糊：画面不清晰、有虚影',
          disabled: true,
        },
        {
          text: '脏污模糊：画面局部或者大部分灰蒙蒙',
          disabled: true,
        },
        {
          text: '花屏：画面局部或者大部分有条纹',
          disabled: true,
        },
        {
          text: '视频遮挡：遮挡物挡住摄像机部分或者全部视界',
          disabled: true,
        },
      ],
      customizeAction: {
        title: '新增分析数据字段',
        leftContent: '所有分析数据字段',
        rightContent: '已选择分析数据字段',
        moduleStyle: {
          width: '80%',
        },
      },
      needRuleConfig: ['VIDEO_VALID_SUBMIT_QUANTITY', 'VIDEO_ACCURACY'], // 需要配置规则的指标
      checkResultTips: [
        {
          indexs: ['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'], // 视频流质量合格率、视频流质量合格率（人工复核）
          tipFunc: (time) =>
            `如果设备最近${time}小时（在后台可配置，默认12小时）有实时视频拉取成功记录，则直接取该次视频流截图进行质量检测，不再重新拉流检测`,
        },
        {
          indexs: [
            'VIDEO_GENERAL_OSD_ACCURACY',
            'VIDEO_OSD_ACCURACY',
            'VIDEO_GENERAL_CLOCK_ACCURACY',
            'VIDEO_CLOCK_ACCURACY',
          ], // 普通字幕标注合规率、重点字幕标注合规率、普通时钟准确率、重点时钟准确率
          tip: '如果设备当天有实时视频拉取成功记录，则直接取该次视频流截图进行OCR识别，不再重新拉流检测。',
          hasOcr: true, // 检测方法为OCR识别时，该指标配置显示视频截图取最近的缓存结果
        },
        {
          indexs: ['VIDEO_GENERAL_HISTORY_ACCURACY', 'VIDEO_HISTORY_ACCURACY'], // 普通历史视频可调阅率、重点历史视频可调阅率
          tip: '如果设备当天有检测成功记录，则直接取该次缓存的检测结果，不再重新拉流检测。',
        },
        {
          indexs: [
            'VIDEO_GENERAL_PLAYING_ACCURACY',
            'VIDEO_PLAYING_ACCURACY',
            'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
          ], // 普通实时视频可调阅率、重点实时视频可调阅率、 重点指挥图像在线率
          tip: '如果设备最近X小时（在后台可配置，默认12小时）有检测成功记录，则直接取该次缓存的检测结果，不再重新拉流检测。',
        },
        {
          indexs: ['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'], // (重点)字幕标注合规性与时钟准确性
          tip: '如果设备最近X小时（在后台可配置，默认12小时）有实时视频拉取成功记录，则直接取该次视频流截图进行OCR识别，不再重新拉流检测。',
          hasOcr: true, // 检测方法为OCR识别时，该指标配置显示视频截图取最近的缓存结果
        },
      ],
      // 历史指标 检测内容所有项联动
      historyVideoIndexs: [
        'VIDEO_HISTORY_ACCURACY', // 重点历史视频可调阅率
        'VIDEO_GENERAL_HISTORY_ACCURACY', // 普通历史视频可调阅率
      ],
      osdType: [
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_OSD_ACCURACY',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
      ], // 普通字幕标注合规率、重点字幕标注合规率
      defaultScreenshotIndex: [
        'VIDEO_OSD_ACCURACY',
        'VIDEO_CLOCK_ACCURACY',
        'VIDEO_GENERAL_CLOCK_ACCURACY',
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
      ], // 默认需要视频截图的指标（重点字幕标注合规率, 重点时钟准确率, 普通时钟准确率, 普通字幕标注合规率选择ocr默认选中需要视频截图 , (重点)字幕标注合规性与时钟准确性
      checkContentTooltipIndex: [
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_OSD_ACCURACY',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
      ],
      // 显示 普通设备历史录像 的指标
      videoGeneralHistoryIndex: ['VIDEO_GENERAL_HISTORY_ACCURACY', 'VIDEO_HISTORY_COMPLETE_ACCURACY'],
      // 显示重点设备历史录像 的指标
      videoImportHistoryIndex: [
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_HISTORY_COMPLETE_ACCURACY',
        'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
      ],
      // 不需要指标取值的指标
      unwantedIndexValue: [
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_OSD_ACCURACY',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
      ],
      // 显示 设备时间与标准时间允许偏差 的指标
      deviceTimeDeviationIndex: ['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'],
      comprehensiveQualityIndex: ['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'],
      explainIndex: ['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'],
      needRecheckIndex: [
        'VIDEO_GENERAL_CLOCK_ACCURACY',
        'VIDEO_CLOCK_ACCURACY',
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_OSD_ACCURACY',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_QUALITY_PASS_RATE',
        'VIDEO_QUALITY_PASS_RATE_RECHECK',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ], // 需要复检的指标
      filterCondition: [
        'VIDEO_GENERAL_CLOCK_ACCURACY',
        'VIDEO_CLOCK_ACCURACY',
        'VIDEO_GENERAL_OSD_ACCURACY',
        'VIDEO_OSD_ACCURACY',
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY',
        'VIDEO_QUALITY_PASS_RATE',
        'VIDEO_QUALITY_PASS_RATE_RECHECK',
        'VIDEO_QUANTITY_STANDARD',
      ], // 需要显示过滤条件的指标 普通时钟准确率、重点时钟准确率、普通字幕标注合规率、重点字幕标注合规率、字幕标注合规性与时钟准确性、重点字幕标注合规性与时钟准确性、视频流质量合格率、视频流质量合格率（人工复核）、视频监控数量达标率
      updateStatus: [
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ], // 需要显示检测合格更新设备在线状态的指标 普通实时视频可调阅率、重点实时视频可调阅率、重点指挥图像在线率
      /**
       ** 检测内容和指标取值对应联动，
       ** [重点实时视频可调阅率, 重点历史视频可调阅率, 普通实时视频可调阅率, 普通历史视频可调阅率]
       **/
      indexDetectionAnddetection: [
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ],
    };
  },
};

// 优化中的代码（勿删）
// const videoConfigsData = {
//     ordinaryHistoryVideo: [
//         'VIDEO_GENERAL_HISTORY_ACCURACY',
//         'VIDEO_HISTORY_COMPLETE_ACCURACY',
//     ], // 普通设备历史录像 [普通历史视频可调阅率,历史录像完整率]
//     importantHistoryVideo: [
//         'VIDEO_HISTORY_ACCURACY',
//         'VIDEO_GENERAL_HISTORY_ACCURACY',
//         'VIDEO_HISTORY_COMPLETE_ACCURACY',
//     ], // 重点设备历史录像 [重点历史视频可调阅率,普通历史视频可调阅率,历史录像完整率]
//     timeDeviation: ['VIDEO_GENERAL_CLOCK_ACCURACY', 'VIDEO_CLOCK_ACCURACY'], // 与标准时间允许的偏差 [普通时钟准确率, 重点时钟准确率]
//     checkMethods: [
//         'VIDEO_OSD_ACCURACY',
//         'VIDEO_CLOCK_ACCURACY',
//         'VIDEO_GENERAL_CLOCK_ACCURACY',
//         'VIDEO_GENERAL_OSD_ACCURACY',
//     ], // [重点字幕标注合规率, 重点时钟准确率, 普通时钟准确率, 普通字幕标注合规率]
//     checkMethodsChildren: [
//         'VIDEO_PLAYING_ACCURACY',
//         'VIDEO_HISTORY_ACCURACY',
//         'VIDEO_GENERAL_PLAYING_ACCURACY',
//         'VIDEO_GENERAL_HISTORY_ACCURACY',
//         'VIDEO_MONITOR_ONLINE_RATE_PROMOTE',
//         'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
//     ], // [重点实时视频可调阅率, 重点历史视频可调阅率, 普通实时视频可调阅率, 普通历史视频可调阅率]
//     videoQualityCheckContent: [
//         { text: '视频信号：检测视频信号是否有丢失', key: 'videoSignal' },
//         { text: '视频亮度：检测视频亮度是否异常', key: 'videoBrightness' },
//         { text: '视频偏色：检测视频有无偏色', key: 'videoCcolorCast' },
//         {
//             text: '视频清晰：检测视频是否模糊、有无雪花/条纹',
//             key: 'videoClear',
//         },
//         { text: '视频遮挡：检测视频是否有遮挡', key: 'videoOcclusion' },
//     ],
//     customizeAction: {
//         title: '新增分析数据字段',
//         leftContent: '所有分析数据字段',
//         rightContent: '已选择分析数据字段',
//         moduleStyle: {
//             width: '80%',
//         },
//     },
//     needRuleConfig: ['VIDEO_VALID_SUBMIT_QUANTITY', 'VIDEO_ACCURACY'], // 需要配置规则的指标
//     historyVideoConfig: {
//         visibileIndexType: [
//             'VIDEO_GENERAL_HISTORY_ACCURACY',
//             'VIDEO_HISTORY_COMPLETE_ACCURACY',
//             'VIDEO_HISTORY_ACCURACY',
//             'VIDEO_GENERAL_HISTORY_ACCURACY',
//             'VIDEO_HISTORY_COMPLETE_ACCURACY',
//             'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN'
//         ], // 配置显示设备历史录像的指标名
//         formItemData: [
//             {
//                 label: '普通设备历史录像',
//                 key: 'videoGeneralDay',
//                 indexType: [
//                     'VIDEO_GENERAL_HISTORY_ACCURACY',
//                     'VIDEO_HISTORY_COMPLETE_ACCURACY',
//                 ], // 配置显示普通设备历史录像，配合visibileIndexType一起使用
//             },
//             {
//                 label: '重点设备历史录像',
//                 key: 'videoImportDay',
//                 indexType: [
//                     'VIDEO_HISTORY_ACCURACY',
//                     'VIDEO_GENERAL_HISTORY_ACCURACY',
//                     'VIDEO_HISTORY_COMPLETE_ACCURACY',
//                     'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN'
//                 ] // 配置显示重点设备历史录像，配合visibileIndexType一起使用
//             },
//         ]
//     }, // 配置历史录像
// }
// export default videoConfigsData
