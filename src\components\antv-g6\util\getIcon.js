import fonts from "@/assets/configureIcons/iconfont.json";
import fontIconStyleMap from "@/assets/js/fontIconStyleMap";

export const icons = fonts.glyphs
  .filter((icon) => icon.name.indexOf("图谱-") > -1)
  .map((icon) => {
    return {
      ...icon,
      ...(fontIconStyleMap[icon.icon_id] || {}),
      unicode: String.fromCodePoint(icon.unicode_decimal), // `\\u${icon.unicode}`,
    };
  });
export const getIcon = (type, keyName = "icon_id") => {
  const matchIcon = icons.find((icon) => {
    return icon[keyName] === type;
  }) || { unicode: "", name: "default", bgColor: ["#1BE4F3", "#84B5FF"] };
  return matchIcon;
};
