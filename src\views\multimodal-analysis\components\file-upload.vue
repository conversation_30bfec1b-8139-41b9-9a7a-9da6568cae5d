<template>
  <div class="file-upload-box">
    <div class="upload-list">
      <div
        class="upload-list-item"
        v-for="(item, index) in fileListAll"
        :key="index"
      >
        <div class="file-image">
          <img src="@/assets/img/directory.png" />
        </div>
        <div class="file-title">
          {{ item.structuredDataType == "0" ? "视频" : "图片" }}任务文件
          {{ index + 1 }}
        </div>
        <div class="progress-box">
          <Progress
            :stroke-width="6"
            :percent="((item.successNum + item.errorNum) / item.total) * 100"
            :stroke-color="['#5BCAFF', '#2C86F8']"
            hide-info
          />
        </div>
        <div class="file-number">
          <span class="primary">{{ item.successNum + item.errorNum }}</span>
          <span>/</span>
          <span>{{ item.total }}</span>
        </div>
      </div>
    </div>
    <div class="upload-btn">
      <input
        ref="upLoadInputRef"
        type="file"
        multiple
        :webkitdirectory="isDirectory"
        @change="changeFile"
        style="display: none"
      />
      <div
        @mouseover="showUploadType = true"
        @mouseout="showUploadType = false"
        class="uploadSelectBtn"
        v-if="show"
      >
        <Button size="small" type="primary">
          <ui-icon type="daoru" color="#fff"></ui-icon>
          导入
        </Button>
        <div class="uploadSelect" v-show="showUploadType">
          <Button size="small" @click="handleUpload(false)">文件导入</Button>
          <Button size="small" @click="handleUpload(true)">文件夹导入</Button>
          <slot name="moreLoadAction" />
        </div>
      </div>
      <span>
        点击
        <span class="primary">&nbsp;导入&nbsp;</span>
        文件或者文件夹
      </span>
    </div>
  </div>
</template>

<script>
import { deepCopy } from "@/util/modules/common";
import { fileUpload } from "@/api/config.js";
export default {
  name: "FileUpload",
  props: {
    // 上传文件类型 0.视频 / 1.图片
    fileUploadType: {
      type: Number | String,
      default: 1,
    },
  },
  computed: {
    fileFormat() {
      return this.fileUploadType == "0"
        ? ["avi", "wmv", "mbf", "mp4", "bmp"]
        : ["jpg", "png", "jpeg"];
    },
    fileMaxSize() {
      // 暂时视频的限制还只是20MB,走的系统管理的上传接口，只能接收20M的文件，后面改成H5插件的接口再改
      return this.fileUploadType == "0" ? 20 * 1024 * 1024 : 20 * 1024 * 1024;
    },
  },
  data() {
    return {
      showUploadType: false,
      isDirectory: false,
      show: true,
      fileList: [],
      fileListAll: [],
      oneFileTotal: 0,
      oneFileError: 0,
      oneFileSuccess: 0,
      fileUploadLoading: false,
    };
  },
  watch: {},
  mounted() {},
  methods: {
    handleUpload(value) {
      this.isDirectory = value;
      this.$nextTick(() => {
        this.$refs.upLoadInputRef.click();
      });
    },
    async changeFile(e) {
      let fileList = e.target.files;
      if (fileList.length == 0 || fileList.length > 50) {
        this.$Message.error("上传失败,最大持支持50个文件上传");
        return;
      }
      let fileListAllLength = this.fileListAll.length;
      let needUploadList = [];
      let errorNum = 0;
      for (let i = 0; i < fileList.length; i++) {
        let fileType = fileList[i].name.split(".").pop();
        if (!this.fileFormat.includes(fileType)) {
          this.$Message.error(
            fileList[i].name +
              "格式错误，请上传" +
              this.fileFormat.join(",") +
              "格式文件",
            { duration: 3 }
          );
          errorNum++;
          continue;
        }
        if (fileList[i].size > this.fileMaxSize) {
          this.$Message.error(
            fileList[i].name + `文件大小不能超过${this.fileMaxSize}M`,
            {
              duration: 3,
            }
          );
          errorNum++;
          continue;
        }
        let fileData = new FormData();
        fileData.append("file", fileList[i]);
        let resolution = "";
        let videoDuration = "";
        if (this.fileUploadType == 0) {
          const videoInfo = await this.readVideoDuration(fileList[i]);
          resolution = videoInfo.resolution;
          videoDuration = videoInfo.duration;
        } else {
          const videoInfo = await this.readImageWH(fileList[i]);
          resolution = videoInfo.resolution;
        }

        needUploadList.push({ fileData, resolution, videoDuration });
      }
      if (needUploadList.length == 0) {
        return;
      }
      let oneUpload = {
        fileList,
        successNum: 0,
        errorNum,
        total: fileList.length,
        structuredDataType: this.fileUploadType,
      };
      this.fileListAll.push(oneUpload);
      let promiseList = [];
      needUploadList.forEach((item, index) => {
        const promise = new Promise((resolve, reject) => {
          fileUpload(item.fileData)
            .then((res) => {
              this.fileListAll[fileListAllLength].successNum++;
              // this.$set(this.fileListAll, fileListAllLength, {
              //   ...data,
              //   successNum: data.successNum++,
              // });
              resolve({
                ...res.data,
                resolution: item.resolution,
                videoDuration: item.videoDuration,
              });
            })
            .catch((err) => {
              reject(err);
            });
        });
        promiseList.push(promise);
      });
      if (promiseList.length > 0) {
        // 处理集中处理反参
        Promise.all(promiseList).then((res) => {
          let completeFile = [];
          for (let i = 0; i < res.length; i++) {
            for (
              let j = 0;
              j < this.fileListAll[fileListAllLength].fileList.length;
              j++
            ) {
              if (
                res[i].originalFilename ==
                this.fileListAll[fileListAllLength].fileList[j].name
              ) {
                completeFile.push({
                  ...res[i],
                  file: this.fileListAll[fileListAllLength].fileList[j],
                });
                break;
              }
            }
          }
          this.$emit("fileUploadSuccess", {
            fileList: deepCopy(completeFile),
            structuredDataType: this.fileUploadType,
          });
        });
      }
    },
    // 获取图片分辨率
    readImageWH(file) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        img.onload = () => {
          resolve({ resolution: `${img.naturalWidth}*${img.naturalHeight}` });
          img.remove();
        };
        img.onerror = () => {
          resolve({ resolution: "0*0" });
        };
        img.src = URL.createObjectURL(file);
      });
    },
    // 获取视频文件时长
    readVideoDuration(file) {
      return new Promise((resolve, reject) => {
        const video = document.createElement("video");
        video.preload = "metadata";
        video.addEventListener("loadedmetadata", () => {
          resolve({
            resolution: `${video.videoWidth}*${video.videoHeight}`,
            duration: this.secondToTime(Math.floor(video.duration)),
          });
        });
        video.addEventListener("error", () => {
          resolve({ resolution: "0*0", duration: 0 });
        });
        video.src = URL.createObjectURL(file);
      });
    },
    // 时间处理
    secondToTime(s) {
      const hour = Math.floor(s / 3600);
      const min = Math.floor((s % 3600) / 60);
      const second = Math.round((s % 3600) % 60);
      return `${hour}:${min}:${second}`;
    },
  },
};
</script>

<style lang="less" scoped>
.file-upload-box {
  overflow: scroll;
  flex: 1;
  .upload-list {
    width: 100%;
    .upload-list-item {
      width: 100%;
      padding: 15px 16px;
      display: flex;
      background: #f9f9f9;
      border-radius: 4px 4px 4px 4px;
      margin-top: 10px;
      margin-bottom: 10px;
      .file-image {
        margin-right: 10px;
        img {
          width: 16px;
          height: 16px;
        }
      }
      .file-title {
        width: 137px;
        font-size: 14px;
        color: #3d3d3d;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 25px;
      }
      .progress-box {
        width: 100px;
        margin-right: 20px;
        /deep/ .ivu-progress-inner {
          background: #dde1ea !important;
        }
      }
      .file-number {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
  .upload-btn {
    display: flex;
    align-items: center;
    .uploadSelectBtn {
      position: relative;
      & > button {
        margin-right: 10px;
        height: 34px;
        padding: 0 20px;
        i {
          transform: rotateY(180deg);
        }
      }
      span {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
      .uploadSelect {
        position: absolute;
        top: 34px;
        button {
          width: 93px;
        }
      }
    }
  }
}
</style>
