<template>
  <div class="index-container">
    <Dropdown trigger="click" @on-click="onClickDropdown" transfer>
      <i class="icon-font icon-shaixuan f-16 color-filter"></i>
      <DropdownMenu slot="list">
        <DropdownItem
          v-for="(value, key, index) in data"
          :key="index"
          :selected="selected === key"
          :name="key"
          class="ellipsis"
        >
          <span class="dis-select" :title="getIndexByKey(key)">{{ getIndexByKey(key) }}</span>
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'index-select',
  components: {},
  props: {
    data: {
      required: true,
      default: () => {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {
      selected: '1',
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.selected = val;
      },
    },
  },
  filter: {},
  created() {},
  methods: {
    onClickDropdown(val) {
      this.selected = val;
      this.$emit('input', val);
      this.$emit('change', val);
    },
    getIndexBy<PERSON><PERSON>(key) {
      let result = this.global.indexTypeList.find((item) => `${item.id}` === `${key}`);
      if (result) {
        return result.title;
      }
      return '其他';
    },
    setCheckedKeys(list) {
      Object.keys(this.data).map((key) => {
        list.map((value) => {
          this.data[key].map((item) => {
            if (value === item.indexType) {
              item.check = true;
            }
          });
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.color-filter {
  color: #1d84a0;
  &:hover {
    color: #539aea;
  }
}
.mini-btn {
  width: 48px;
  height: 28px;
  line-height: 28px;
  padding: 0 5px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.index-container {
  position: relative;
  display: inline-block;
  .dropdown {
    width: 300px;
    height: 300px;
    overflow-y: auto;
  }
}
</style>
