<template>
  <div class="player-list" ref="playListRef">
    <EasyPlayer
      class="easy-player"
      :videoUrl="item.videoUrl"
      ref="easyPlay"
      :style="{
        width: activeWidth,
      }"
      v-for="(item, index) in playingList"
      :key="index"
    >
      <component :is="item.render(item)"></component>
    </EasyPlayer>
  </div>
</template>

<script>
import vedio from '@/config/api/vedio-threm';
export default {
  name: 'video-accuracy',
  props: {
    deviceList: {
      default: () => [],
    },
    rowNum: {
      default: 4,
    },
  },
  data() {
    return {
      videoUrl: '',
      playDeviceCode: '',
      playingList: [],
      videoVisible: false,
      videoLoading: false,
    };
  },
  methods: {
    //视频播放
    async clickRow(row) {
      try {
        let data = {
          deviceId: row.deviceId,
          startTime: row.playStartTime,
          endTime: row.playEndTime,
          pullLevel: 3,
          urlEncryption: true,
        };
        let res = await this.$http.post(vedio.playback, data);
        this.playingList.push({
          deviceId: row.deviceId,
          videoUrl: res.data.data.hls,
          playDeviceCode: row.deviceId,
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {
    activeWidth() {
      // let total = this.deviceList?.length ?? 0
      // Math.floor(total/2)
      // `${100/rowNum - 1}%`
      return '20%';
    },
    getAttrs() {
      return {
        ...this.$attrs,
      };
    },
  },
  watch: {
    deviceList: {
      handler(val) {
        if (!val?.length) return;
        this.playingList = [];
        // 加载新的批量视频
        val.forEach((item) => {
          //this.clickRow(item)
          this.playingList.push({
            deviceId: item.deviceId,
            videoUrl: '',
          });
        });
      },
      immediate: true,
    },
  },
  components: {
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>

<style lang="less" scoped>
.player-list {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  height: 560px;
  .easy-player {
    height: 280px;
    margin-right: 10px;
    margin-top: 10px;
  }
  .radio-box {
    height: 2%;
  }
}
</style>
