<template>
  <div class="basic-information-container">
    <img
      v-if="type === 'video' && baseInfo.realNameArchiveNo"
      src="@/assets/img/icons/icon-yzx.png"
      alt=""
      class="bg-yzx"
    />
    <ui-card title="基础信息">
      <template v-if="type === 'people' || type === 'device'">
        <swiper
          ref="mySwiper"
          v-if="
            baseInfo[basicMap[type].photo] &&
            baseInfo[basicMap[type].photo].length
          "
          :options="swiperOption1"
          class="my-swiper"
        >
          <template v-for="(item, $index) in baseInfo[basicMap[type].photo]">
            <swiper-slide :key="$index">
              <div class="swiper-item">
                <p class="img-content">
                  <img :src="item.photoUrl" alt="" />
                </p>
              </div>
            </swiper-slide>
          </template>
          <div class="swiper-button-prev" slot="button-prev"></div>
          <div class="swiper-button-next" slot="button-next"></div>
        </swiper>
      </template>
      <template
        v-if="
          type === 'video' ||
          type === 'car' ||
          type === 'non-motor-archive' ||
          type === 'place'
        "
      >
        <swiper ref="mySwiper" :options="swiperOption1" class="my-swiper">
          <swiper-slide>
            <div class="swiper-item">
              <p class="img-content">
                <ui-image
                  :type="type"
                  :src="
                    baseInfo[basicMap[type].photo] ||
                    baseInfo.bigImageUrl ||
                    baseInfo.traitImg
                  "
                  alt="动态库"
                  objectFit="fill"
                />
              </p>
            </div>
          </swiper-slide>
          <div class="swiper-button-prev" slot="button-prev"></div>
          <div class="swiper-button-next" slot="button-next"></div>
        </swiper>
      </template>
      <div class="basic">
        <div class="basic-header basic-header-people" v-if="type === 'people'">
          身份证号
          <br />
          <div>{{ baseInfo.gmsfhm }}</div>
        </div>
        <div class="basic-header basic-header-video" v-if="type === 'video'">
          视频身份
          <br />
          <div>{{ baseInfo.archiveNo }}</div>
        </div>
        <div
          class="basic-header basic-header-car"
          v-if="type === 'car' || type === 'non-motor-archive'"
        >
          车牌号码
          <br />
          <div>
            <ui-plate-number
              :plateNo="baseInfo.plateNo || plateNo"
              :color="baseInfo.plateColor"
              size="super"
            ></ui-plate-number>
          </div>
        </div>
        <div class="basic-header basic-header-profile" v-if="type === 'device'">
          设备编码
          <br />
          <div>{{ baseInfo.deviceId }}</div>
        </div>
        <!-- 场所 -->
        <div class="basic-header basic-header-place" v-if="type === 'place'">
          场所名称
          <br />
          <div>{{ baseInfo.name }}</div>
        </div>
        <div class="basic-content">
          <div
            class="basic-content-table"
            v-for="(item, key, index) in basicMap[type].field"
            :key="index + item"
            :class="
              index % 2 === 0
                ? 'basic-content-table-item1'
                : 'basic-content-table-item2'
            "
          >
            <div class="table-item-left" :class="widthAll">
              {{ item || "--" }}
            </div>
            <div
              :class="
                key === 'xm' || key === 'clusteringImagesNum'
                  ? 'font-important'
                  : ''
              "
              class="table-item-right"
            >
              <!-- 性别 -->
              <template v-if="key === 'xbdm'">
                {{ baseInfo[key] | commonFiltering(genderList) }}
              </template>
              <!-- 婚姻状况 -->
              <template v-else-if="key === 'hyzkdm'">
                {{ baseInfo[key] | commonFiltering(marriageList) }}
              </template>
              <!-- 籍贯 -->
              <template v-else-if="key === 'jgXzqhdm'">
                {{ baseInfo[key] | commonFiltering(nationList) }}
              </template>
              <!-- 联系电话 -->
              <template v-else-if="key === 'yddh'">
                {{ baseInfo[key] || baseInfo["lxdh"] }}
              </template>
              <!-- 文化程度 -->
              <template v-else-if="key === 'xldm'">
                {{ baseInfo[key] | commonFiltering(educationList) }}
              </template>
              <!-- 政治面貌 -->
              <template v-else-if="key === 'zzmmdm'">
                {{ baseInfo[key] | commonFiltering(politicalList) }}
              </template>
              <!-- 职业类型 -->
              <template v-else-if="key === 'zylbdm'">
                {{ baseInfo[key] || "--" }}
              </template>
              <!-- 点位类型-->
              <template v-else-if="key === 'sbdwlx'">
                {{ baseInfo[key] | commonFiltering(sbdwlxList) }}
              </template>
              <!-- 功能类型 -->
              <template v-else-if="key === 'sbgnlx'">
                {{ baseInfo[key] | commonFiltering(sbgnlxList) }}
              </template>
              <!-- 设备状态 -->
              <template v-else-if="key === 'isOnline'">
                {{ baseInfo[key] | commonFiltering(isonlineList) }}
              </template>
              <!-- 识别代码 -->
              <template v-else-if="key === 'sbdm'">
                {{ baseInfo[key] || "--" }}
              </template>
              <!-- 使用性质 -->
              <template v-else-if="key === 'syxz'">
                {{ baseInfo[key] || "--" }}
              </template>
              <!-- 车牌颜色 -->
              <template v-else-if="key === 'plateColor'">
                {{ baseInfo[key] | commonFiltering(licensePlateColorList) }}
              </template>
              <!-- 车辆类型 -->
              <template v-else-if="key === 'vehicleType'">
                {{
                  baseInfo[key]
                    | commonFiltering(
                      type === "non-motor-archive"
                        ? nonmotorVehicleTypeList
                        : vehicleTypeList
                    )
                }}
              </template>
              <!-- 车辆品牌 -->
              <template v-else-if="key === 'vehicleBrandCN'">
                {{ baseInfo[key] | commonFiltering(vehicleBrandList) }}
              </template>
              <!-- 车辆颜色 -->
              <template v-else-if="key === 'vehicleColor'">
                {{ baseInfo[key] | commonFiltering(vehicleColorList) }}
              </template>
              <!-- 非机动车角度 -->
              <template v-else-if="key === 'vehicleAngle'">
                {{ baseInfo[key] | commonFiltering(nonmotorVehicleAngle) }}
              </template>
              <!-- 面积，加单位 -->
              <template v-else-if="key === 'area'">
                {{ baseInfo[key] }}平方米
              </template>
              <template v-else>
                {{ baseInfo[key] ? baseInfo[key] : "--" }}
              </template>
            </div>
          </div>
        </div>
      </div>
      <!-- 标签信息，非机动车暂时没有标签 -->
      <Label
        v-if="type !== 'non-motor-archive' && labelsObj"
        :labelType="labelType"
        :type="type"
        :archiveNo="archiveNo"
        :labelInfo="baseInfo.labelIds"
        @add="add"
      />
    </ui-card>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { basicMap } from "./basic";
export default {
  components: {
    swiper,
    swiperSlide,
    Label: require("./basic-label").default,
  },
  props: {
    // 标签类型
    labelType: {
      type: Number,
      default: 0,
    },
    // people/人  car/车 non-motor-archive/非机动车 video/视频 device/一机一档 place/场所 other/其它
    type: {
      type: String,
      default: "people",
    },
    // 具体信息，字段对应后期调整
    baseInfo: {
      type: Object | String,
      default() {
        return {};
      },
    },
    imgSrc: {
      type: String,
      default: require("@/assets/img/people1.webp"),
    },
  },
  data() {
    return {
      basicMap,
      swiperOption1: {
        effect: "coverflow",
        slidesPerView: 1.7,
        centeredSlides: true,
        initialSlide: 1,
        // loop: true,
        // loopAdditionalSlides: 2,
        speed: 500,
        // autoplay: {
        //   delay: 1000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false,
        // },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 20, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 200, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1.2, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: false, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        archiveNo: "",
      },
      archiveNo: "",
      plateNo: "",
    };
  },
  computed: {
    widthAll() {
      if (this.type === "video") {
        return "basic-content-video";
      } else {
        return "basic-content-vehicle";
      }
    },
    ...mapGetters({
      marriageList: "dictionary/getMarriageList", //婚姻状态
      genderList: "dictionary/getGenderList", //性别
      nationList: "dictionary/getNationList", //民族
      professionList: "dictionary/getProfession", //职业类型
      educationList: "dictionary/getEducation", //文化程度
      politicalList: "dictionary/getPolitical", //政治面貌
      sbdwlxList: "dictionary/getSbdwlxList", // 摄像机点位类型
      sbgnlxList: "dictionary/getSbgnlxList", //摄像机功能类型
      isonlineList: "dictionary/getIsonlineList", //设备状态
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      nonmotorVehicleTypeList: "dictionary/getNonmotorVehicleType", //非机动车车辆类型
      nonmotorVehicleAngle: "dictionary/getNonmotorVehicleAngle", //非机动车角度
      vehicleBrandList: "dictionary/getVehicleBrandList", //车辆品牌
      vehicleColorList: "dictionary/getVehicleColorList", //车辆颜色
      labelsObj: "systemParam/labelsObj", // 标签配置
    }),
  },
  watch: {},
  filter: {},
  created() {},
  mounted() {
    let { archiveNo, plateNo } = this.$route.query;
    this.archiveNo = archiveNo;
    this.plateNo = plateNo && JSON.parse(plateNo);
  },
  methods: {
    add() {
      this.$emit("add");
    },
  },
};
</script>
<style lang="less" scoped>
.label-title {
  margin-top: 20px;
  font-size: 14px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.9);
  & > span {
    display: inline-block;
    width: 5px;
    height: 5px;
    background: #2c86f8;
    margin-right: 7px;
    position: relative;
    top: -2px;
  }
}
.basic-information-container {
  width: 100%;
  height: 100%;
  position: relative;
  .bg-yzx {
    position: absolute;
    width: 84px;
    height: 70px;
    top: 0;
    right: 0;
  }
  .basic {
    &-header {
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      text-align: center;
    }
    &-header-people {
      & > div {
        display: inline-block;
        border-radius: 25px;
        font-size: 20px;
        font-weight: 500;
        color: #ffffff;
        letter-spacing: 2px;
        margin-top: 3px;
        padding: 6px 18px;
        background: linear-gradient(263deg, #5ba3ff 0%, #2c86f8 100%);
      }
    }
    &-header-video {
      position: relative;
      & > div {
        display: inline-block;
        border-radius: 25px;
        font-size: 20px;
        font-weight: 500;
        color: #ffffff;
        letter-spacing: 2px;
        margin-top: 3px;
        padding: 6px 24px;
        background: linear-gradient(234deg, #ffc652 0%, #ff842e 100%);
      }
    }
    &-header-car {
      //& > div {
      //  background: #2379f9;
      //  display: inline-block;
      //  padding: 3px;
      //  border-radius: 3px;
      //  & > div {
      //    padding: 2px 15px;
      //    border-radius: 3px;
      //    border: 1px solid #ffffff;
      //    color: #fff;
      //    font-size: 26px;
      //    font-weight: bold;
      //  }
      //}
    }
    &-header-profile {
      & > div {
        display: inline-block;
        border-radius: 25px;
        font-size: 20px;
        font-weight: 500;
        color: #ffffff;
        letter-spacing: 2px;
        margin-top: 3px;
        padding: 6px 18px;
        background: linear-gradient(144deg, #3cd2aa 0%, #1faf8a 100%);
      }
    }
    &-header-place {
      & > div {
        width: 100%;
        border-radius: 25px;
        font-size: 22px;
        font-weight: 700;
        color: #ffffff;
        letter-spacing: 2px;
        margin-top: 5px;
        text-align: center;
        line-height: 40px;
        background: linear-gradient(224deg, #5bcaff 0%, #2c86f8 100%);
      }
    }
    .table-item-left,
    .table-item-right {
      display: table-cell;
    }
    .basic-content-video {
      width: 108px !important;
    }
    .basic-content-vehicle {
      width: 86px !important;
    }
    .basic-content {
      margin-top: 20px;
      overflow-y: auto;
      .table-item-left {
        font-size: 14px;
        font-weight: 700;
        color: rgba(0, 0, 0, 0.9);
        padding: 5px 10px;
        text-align: justify;
        text-align-last: justify;
        //vertical-align: middle;
        &:after {
          content: "";
          width: 100%;
        }
      }
      .table-item-right {
        // width: 230px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        padding: 5px 10px;
      }
      .basic-content-table-item1 {
        background: #f9f9f9;
      }
      .basic-content-table-item2 {
        background: #f1f1f1;
      }
    }
  }
  .my-swiper {
    padding: 26px 0 20px 0;
    margin: auto;
    width: 80%;
    // height: 200px;
    .swiper-item {
      width: 158px;
      height: 200px;
      background: #f9f9f9;
      box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      box-sizing: border-box;
      padding: 10px;
      .img-content {
        width: 100%;
        height: 100%;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
    .swiper-button-prev {
      height: 180px;
      top: 10%;
      width: 60px;
      left: 0;
      opacity: 0;
    }
    .swiper-button-next {
      height: 180px;
      top: 10%;
      width: 60px;
      right: 0;
      opacity: 0;
    }
    .swiper-slide-prev::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(237, 240, 240, 0.6);
      z-index: 99;
    }
    .swiper-slide-next::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(237, 240, 240, 0.6);
      z-index: 99;
    }
  }
  /deep/ .ui-image-div {
    cursor: auto;
  }
}
</style>
