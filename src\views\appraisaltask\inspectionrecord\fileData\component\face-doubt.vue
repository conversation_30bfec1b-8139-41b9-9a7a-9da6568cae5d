<template>
  <ui-modal v-model="captureVisible" title="准确性存疑原因" :footerHide="true" width="70%">
    <Row class="face-doubt">
      <Col :span="10" class="content-left">
        <div class="pb8">置信身份</div>
        <Row>
          <Col :span="10" class="img">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <ui-image :src="personObj.personObj.facePath" />
          </Col>
          <Col :span="14" class="img-right">
            <div>
              <span class="lable-left">姓名：</span>
              <span>{{ personObj.personObj.name }}</span>
            </div>
            <!-- <div>
              <span class="lable-left">性别：</span>
                <span>{{personObj.personObj.name}}</span>
            </div> -->
            <div>
              <span class="lable-left">证件号：</span>
              <span>{{ personObj.personObj.idCard }}</span>
            </div>
            <tags-more
              v-if="personObj.personType"
              :tagList="personObj.personType"
              :defaultTags="4"
              placement="left-start"
              bgColor="#2D435F"
            ></tags-more>
          </Col>
        </Row>
      </Col>
      <Col :span="4" class="line">
        <div>
          <div class="line-title">图像对比</div>
          <div class="arrow-left"></div>
          <div class="arrow-right"></div>
        </div>
      </Col>
      <Col :span="10" class="content-right">
        <div class="pb8">抓拍图片</div>
        <Row>
          <Col :span="10" class="img">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
            <ui-image :src="personObj.info.facePath" />
          </Col>
          <Col :span="14" class="img-right">
            <div>
              <span class="lable-left pb8">抓拍时间：</span>
              <span>{{ personObj.info.shotTime }}</span>
            </div>
            <div>
              <span class="lable-left pb8">抓拍地点：</span>
              <span>{{ personObj.info.address }}</span>
            </div>
            <tags-more
              v-if="personObj.personType"
              :tagList="personObj.personType"
              :defaultTags="4"
              placement="left-start"
              bgColor="#2D435F"
            ></tags-more>
          </Col>
        </Row>
      </Col>
    </Row>
    <Table
      stripe
      class="algorithm-table"
      :columns="[
        { title: '检测算法', slot: 'algorithmType' },
        { title: '识别结果', slot: 'score' },
      ]"
      :data="extraResult"
    >
      <template #algorithmType="{ row }">
        <span v-if="row.algorithmType === '综合判定'" class="active"> 综合判定</span>
        <span v-else> {{ row.algorithmType | filterType(algorithmList) }}</span>
      </template>
      <template #score="{ row }">
        <div v-if="row.algorithmType === '综合判定'">
          <i class="icon-font icon-shitongyiren fs50 success"> </i>
          <!-- <i class="icon-font icon-bushitongyiren fs50 error"></i> -->
        </div>
        <div v-else>
          <span v-if="row.flag" class="success">
            {{ row.score }}
          </span>
          <span v-else class="error">
            {{ row.score }}
          </span>
        </div>
      </template>
    </Table>
  </ui-modal>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    uiImage: require('@/components/ui-image').default,
    TagsMore: require('@/components/tags-more').default,
  },
  props: {},
  data() {
    return {
      captureVisible: false,
      personObj: { info: {}, personObj: {} },
      extraResult: [],
    };
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
  },
  watch: {},
  filter: {},
  async created() {
    if (this.algorithmList.length == 0) await this.getAlldicData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    open(info, personObj) {
      this.captureVisible = true;
      this.personObj = { info: info, personObj: personObj };
      this.extraResult = info.extraResult ? JSON.parse(info.extraResult) : [];
    },
  },
};
</script>
<style lang="less" scoped>
.face-doubt {
  color: #fff;
  & > .ivu-col-span-10 {
    background: #0b2a52;
  }
  .content-right,
  .content-left {
    padding: 20px 50px 40px 50px;
    .img {
      padding: 16px;
      height: 190px;
      border: 1px solid var(--color-primary);
      & > span:nth-child(1) {
        position: absolute;
        left: 0;
        top: 0;
        padding: 15px;
        border-style: solid;
        border-color: var(--color-primary);
        border-width: 2px 0 0 2px;
      }
      & > span:nth-child(2) {
        position: absolute;
        right: 0;
        top: 0;
        padding: 15px;
        border-style: solid;
        border-color: var(--color-primary);
        border-width: 2px 2px 0 0;
      }
      & > span:nth-child(3) {
        position: absolute;
        right: 0;
        bottom: 0;
        padding: 15px;
        border-style: solid;
        border-color: var(--color-primary);
        border-width: 0 2px 2px 0;
      }
      & > span:nth-child(4) {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 15px;
        border-style: solid;
        border-color: var(--color-primary);
        border-width: 0 0 2px 2px;
      }
    }
    .img-right {
      padding-left: 24px;
      padding-top: 24px;
      & > div {
        padding-bottom: 16px;
      }
      .lable-left {
        color: #8797ac;
      }
    }
  }
  .line {
    & > div {
      height: 100%;
      padding-top: 50%;
      text-align: center;
    }
    .line-title {
      color: var(--color-primary);
      position: relative;
      top: 18px;
    }
    .arrow-left {
      /*整个箭头容器*/
      // width: 9%;
      height: 10px;
      display: inline-block;
      position: relative; /*相对定位*/
      top: 9px;

      &:before,
      &:after {
        content: '';
        border-color: transparent; /*伪元素边框颜色为透明*/
        position: absolute; /*伪元素绝对定位*/
      }
      &:before {
        /*箭头三角形*/
        border-left-color: #2b84e2;
        border-style: solid;
        border-width: 10px;
        top: -2px;
        left: -18px;
        transform: rotate(-180deg);
      }
    }
    .arrow-right {
      /*整个箭头容器*/
      width: 36%;
      height: 10px;
      display: inline-block;
      position: relative; /*相对定位*/
      top: 9px;

      &:before,
      &:after {
        content: '';
        border-color: transparent; /*伪元素边框颜色为透明*/
        position: absolute; /*伪元素绝对定位*/
      }

      &:after {
        /*箭头尾部的矩形*/
        width: 100%;
        height: 6px;
        background-image: linear-gradient(to right, #2b84e2 100%);
        background-size: 10px 10px;
        background-repeat: y-repeat;
        top: 5px;
        left: 0;
      }

      &:before {
        /*箭头三角形*/
        border-left-color: #2b84e2;
        border-style: solid;
        border-width: 10px;
        left: 100%;
        top: -2px;
      }
    }
  }
}
.algorithm-table {
  margin-top: 32px;
  .active {
    color: var(--color-primary);
  }
  .error {
    color: #bc3c19;
  }
  .success {
    color: #0e8f0e;
  }
  .icon-shitongyiren {
    position: relative;
    &::after {
      content: '是同一人';
      font-size: 12px;
      position: absolute;
      left: 13px;
      transform: rotate(-29deg);
      top: 27px;
    }
  }
  .icon-bushitongyiren {
    position: relative;
    &::after {
      content: '不是同一人';
      font-size: 12px;
      position: absolute;
      left: 8px;
      transform: rotate(-29deg);
      top: 30px;
    }
  }
}
.pb8 {
  padding-bottom: 8px;
}
.fs50 {
  font-size: 50px;
}
</style>
