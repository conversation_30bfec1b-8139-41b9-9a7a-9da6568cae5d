<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-12 10:09:04
 * @Description: 
-->
<template>
  <ui-modal
    v-model="visible"
    title="授权用户"
    :r-width="900"
    @onOk="comfirmHandle"
    @onCancel="onCancel"
    ok-text="修改"
  >
    <div class="content">
      <p class="collect">已授权用户数: {{ userList.length }}</p>
      <ul class="user-list">
        <!-- TODO 这个地方现在不知道拿什么值， 先隐藏 -->
        <!-- <li class="city-name">蚌埠市</li> -->
        <li class="user-edit" v-for="(item, index) in userList" :key="index">
          <i class="iconfont icon-xingming"></i>
          <span class="user-name">{{ item.username }}</span>
        </li>
      </ul>
    </div>
  </ui-modal>
</template>
<script>
import { permissionList } from "@/api/config";
export default {
  data() {
    return {
      visible: false,
      userList: [],
    };
  },
  methods: {
    init(id) {
      this.visible = true;
      permissionList(id).then((res) => {
        this.userList = res.data;
      });
    },
    comfirmHandle() {
      this.visible = false;
      this.$emit("comfirm", this.userList);
    },
    onCancel() {},
  },
};
</script>
<style lang="less" scoped>
.content {
  .collect {
  }
  .user-list {
    padding: 10px 20px;
    display: flex;
    flex-wrap: wrap;
    .city-name {
      width: 100%;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 5px;
    }
    .user-edit {
      display: flex;
      flex-wrap: wrap;
      width: 25%;
      .user-name {
        margin-left: 5px;
      }
    }
  }
}
</style>
