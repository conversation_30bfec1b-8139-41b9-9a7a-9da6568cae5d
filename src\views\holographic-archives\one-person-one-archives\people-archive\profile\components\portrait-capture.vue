<template>
  <ui-card :title="title" class="portrait-capture">
    <!-- <div v-if="type === 'people'" class="video-info">
      <div class="warning-tag-small">视</div>
      <div class="warning">12000123478854124536</div>
    </div> -->
    <div
      slot="extra"
      class="more-btn"
      v-show="list.length"
      @click="portraitCaptureMoreHandle"
    >
      更多<i class="iconfont icon-more"></i>
    </div>
    <div class="portrait-capture-content">
      <PortraitCaptureCard
        v-for="(item, $index) in list"
        :key="$index"
        :type="type"
        :data="item"
        objectFit="fill"
        @detail="viewDetail(item, $index)"
      />
    </div>

    <!-- 抓拍更多弹框 -->
    <ui-modal v-model="showMoreCapture" width="90%" :footerHide="true">
      <component :is="sectionName" :searchFields="searchFields" />
    </ui-modal>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!list || !list.length) && !loading" />

    <!-- 人 -->
    <details-face-modal
      v-if="faceShow"
      ref="faceDetailRef"
      @close="faceShow = false"
    ></details-face-modal>
    <!-- 车 -->
    <details-vehicle-modal
      v-if="vehicleShow"
      ref="vehicleDetailRef"
      @close="vehicleShow = false"
    >
    </details-vehicle-modal>
    <!-- 非机动车 -->
    <details-modal
      v-if="nonMotorVehicleShow"
      ref="nonMotorRef"
      @close="nonMotorVehicleShow = false"
    >
    </details-modal>
  </ui-card>
</template>
<script>
import PortraitCaptureCard from "./card/portrait-capture-card.vue";
import faceContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/face-contents.vue";
import nonmotorVehicleContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/nonmotor-vehicle-content.vue";
import vehicleContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/vehicle-content";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import detailsModal from "@/components/detail/details-modal.vue";
export default {
  components: {
    PortraitCaptureCard,
    faceContent,
    nonmotorVehicleContent,
    vehicleContent,
    detailsFaceModal,
    detailsVehicleModal,
    detailsModal,
  },
  props: {
    baseInfo: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    archiveNo: {
      type: String | Number,
      default: "",
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showMoreCapture: false, // 更多弹框
      faceShow: false, // 人脸抓拍详情
      vehicleShow: false, // 车辆抓拍详情
      nonMotorVehicleShow: false, // 非机动车抓拍详情
      sectionName: "",
      searchFields: {}, // 更多抓拍查询条件
    };
  },
  methods: {
    // 人像抓拍查看更多
    portraitCaptureMoreHandle() {
      let query = Object.keys(this.$route.query).length
        ? this.$route.query
        : JSON.parse(sessionStorage.getItem("query"));
      let fields = {};
      if (this.type === "people") {
        this.sectionName = "faceContent";
        fields = {
          videoIdentity: this.baseInfo.vids
            ? this.baseInfo.vids.toString()
            : this.archiveNo,
        };
      } else if (this.type === "vehicle") {
        this.sectionName = "vehicleContent";
        fields = {
          archiveNo: query.plateNo,
          plateColor: JSON.parse(query.archiveNo).split("_")[1],
        };
      } else if (this.type === "non-motor-archive") {
        this.sectionName = "nonmotorVehicleContent";
        fields = {
          archiveNo: query.plateNo,
        };
      }
      this.searchFields = {
        ...fields,
        noSearch: 1,
      };
      this.showMoreCapture = true;
    },
    viewDetail(row, index) {
      if (this.type === "people") {
        this.faceShow = true;
        this.$nextTick(() => {
          this.$refs.faceDetailRef.init(row, this.list, index, 5, 1);
        });
      } else if (this.type === "non-motor-archive") {
        this.nonMotorVehicleShow = true;
        this.$nextTick(() => {
          this.$refs.nonMotorRef.init(row, this.list, index, 2, 1);
        });
      } else {
        this.vehicleShow = true;
        this.$nextTick(() => {
          this.$refs.vehicleDetailRef.init(row, this.list, index, 1);
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.portrait-capture {
  position: relative;
  /deep/ .card-content {
    padding: 20px 15px !important;
    min-height: 280px;
    box-sizing: border-box;
  }
  /deep/.card-head {
    padding-right: 20px;
  }
  .video-info {
    display: flex;
    align-items: center;
    position: absolute;
    top: -22px;
    left: 140px;
    .warning {
      font-weight: bold;
      font-family: "MicrosoftYaHei-Bold";
      margin-left: 10px;
      font-size: 16px;
      line-height: 22px;
    }
  }
  .portrait-capture-content {
    display: flex;
    margin: 0 -5px;
    .portrait-capture-card {
      margin: 0 5px;
    }
  }
  /deep/ .ivu-modal {
    height: 90%;
    .ivu-modal-content {
      height: 100%;
      .ivu-modal-header {
        height: 42px;
        line-height: 42px;
      }
      .ivu-modal-body {
        max-height: none !important;
        height: calc(~"100% - 42px");
        .main-container {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .search {
    margin-top: 6px;
    .ivu-form {
      display: flex;
      flex: 1;
      justify-content: space-between;
    }
    .custom-date {
      margin-left: -30px;
    }
    .btn-group {
      margin: 0;
    }
  }
  .portrait-capture-list {
    display: flex;
    margin: 0 -5px;
    max-height: 500px;
    overflow: auto;
    flex-wrap: wrap;
    .portrait-capture-card {
      width: calc(~"20% - 10px");
      margin: 0 5px 10px 5px;
    }
  }
}
</style>
