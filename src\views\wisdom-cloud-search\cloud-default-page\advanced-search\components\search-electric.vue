<template>
	<div class="search card-border-color">
		<Form :inline="true" :class="visible ? 'advanced-search-show' : ''">
			<div class="general-search">
				<div class="input-content">
					<!-- <div class="input-content-row">
						<FormItem label="IMSI编码:">
							<Input v-model="formData.imsiCode" placeholder="请输入"></Input>
						</FormItem>
					</div> -->
					<div class="input-content-row">
						<FormItem label="IMSI编码:">
							<Input v-model="formData.imsiCode" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="设备资源:">
							<div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{ formData.selectDeviceList.length }}）</div>
						</FormItem>
						<!-- <FormItem label="感知时间:">
							<DatePicker v-model="formData.perceiveDate" type="datetimerange" format="yyyy-MM-dd HH:mm" placeholder="请选择抓拍时间段" style="width: 300px"></DatePicker>
						</FormItem> -->
						<FormItem label="抓拍时段:">
							<ui-tag-select ref="tagSelect1" :hideCheckAll='true' :value="captureTimePeriod[0].name" @input="e => {input(e, 'timeSlot')}">
								<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
									{{ item.name }}
								</ui-tag-select-option>
							</ui-tag-select>
							<DatePicker v-show="formData.timeSlot == '自定义'" v-model="formData.perceiveDate" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" @on-change="dateChange" placeholder="请选择抓拍时间段" style="width: 330px"></DatePicker>
						</FormItem>
					</div>
				</div>
				<div class="btn-group">
					<Button type="primary" @click="search">查询</Button>
					<Button type="default" @click="resetForm">重置</Button>
				</div>
			</div>
		</Form>
		<!-- 选择设备 -->
		<select-device :deviceType="4" ref="selectDevice" @selectData="selectData" />
	</div>
</template>
<script>
	import { captureTimePeriod } from './search-vehicle.js'
	export default {
		data() {
			return {
				visible: false,
				formData: {
					selectDeviceList: [],
					perceiveDate: [],
					timeSlot: '近一天',
				},
				captureTimePeriod
			}
		},
		created() { },
		methods: {
			search() {
				this.$emit('searchInfo', this.formData)
			},
			input(e, key) {
				this.formData[key] = e
			},
			dateChange(start, end) {
				if (start[1].slice(-8) === '00:00:00') {
					start[1] = start[1].slice(0, -8) + '23:59:59'
				}
				this.formData.perceiveDate = [start[0], start[1]];
			},
			resetForm() {
				this.formData = {
					timeSlot: '近一天',
					imsiCode: '',
					selectDeviceList: [],
					perceiveDate: []
				}
				this.$refs.tagSelect1.clearChecked(false)
				this.$emit('searchInfo', this.formData)
			},
			/**
			 * 选择设备
			 */
			selectDevice() {
				this.$refs.selectDevice.show(this.formData.selectDeviceList)
			},
			/**
			 * 已选择设备数据返回
			 */
			selectData(ids) {
				this.formData.selectDeviceList = ids
			}
		}
	}
</script>
<style lang="less" scoped>
	.btn-group {
		display: flex;
		align-items: flex-end;
		margin-bottom: 16px;
		justify-content: flex-end;
		flex: 1;
	}
	.search {
		padding: 16px 20px 0;
		border-bottom: 1px solid #ffff;
		display: flex;
		width: 100%;
		justify-content: space-between;
		position: relative;
		.ivu-form-inline {
			width: 100%;
		}
		.ivu-form-item {
			margin-bottom: 16px;
			margin-right: 30px;
			display: flex;
			align-items: center;
			/deep/ .ivu-form-item-label {
				white-space: nowrap;
				width: 85px;
			}
			/deep/ .ivu-form-item-content {
				display: flex;
			}
		}
		.general-search {
			display: flex;
			width: 100%;
			.input-content {
				width: 75%;
				display: flex;
				flex-wrap: wrap;
				.input-content-row {
					display: flex;
				}
			}
		}
	}
</style>
