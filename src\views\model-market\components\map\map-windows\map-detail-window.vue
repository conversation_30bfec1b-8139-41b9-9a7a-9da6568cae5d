<template>
  <map-window-container
    :title="`${title}轨迹详情`"
    :close="close"
    class="map-detail-window-container"
  >
    <div class="map-detail-window">
      <div class="body-item" v-for="item in fields" :key="item.value">
        <div class="label" v-if="item.label">{{ item.label }}:</div>
        <div class="value text-ellipsis">
          <render-template
            v-if="item.renderItem"
            :renderItem="item.renderItem"
            :data="detail"
            :item="item"
            :marketData="data"
          />
          <span v-else :title="detail[item.value]">{{
            detail[item.value] || ""
          }}</span>
        </div>
      </div>
    </div>
  </map-window-container>
</template>

<script>
import getPersonTrajectoryDetailHOC from "./getPersonTrajectoryDetailHOC";
import mapWindowContainer from "./map-window-container.vue";
import { getTrackTypeDeatilMap } from "@/views/model-market/characteristic-tricks/casetoperson/components/trackTypeMap.js";
import renderTemplate from "./render-template";
export default {
  components: {
    mapWindowContainer,
    renderTemplate,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    close: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      title: "",
      detail: {},
      fields: [],
    };
  },

  created() {
    this.initData();
  },
  methods: {
    async initData() {
      const { dataType, dataId, detail } = this.data;
      const { name, list = [] } = getTrackTypeDeatilMap(dataType);
      this.title = name;
      this.fields = list;
      // 这字段存在代表数据本身有详情，不需要查询详情接口
      if (detail) this.detail = detail;
      else
        this.detail = await getPersonTrajectoryDetailHOC({ dataId, dataType });
    },
  },
};
</script>

<style lang="less" scoped>
.map-detail-window-container {
  width: 300px;
}

.map-detail-window {
  padding: 5px 20px;

  .body-item {
    display: flex;
    height: 22px;
    align-items: center;
    column-gap: 10px;
    font-weight: 400;
    font-size: 12px;
    overflow: hidden;

    .label {
      color: rgba(0, 0, 0, 0.6);
    }

    .value {
      flex: 1;
      color: rgba(0, 0, 0, 0.9);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .text-ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
