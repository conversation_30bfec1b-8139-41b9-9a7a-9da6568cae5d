<template>
  <div class="switch-wrapper inline">
    <span
      v-for="item in dataList"
      :key="item.value"
      class="switch dis-select f-14 t-center"
      :class="activeValue === item.value ? 'active' : ''"
      @click="handleClick(item)"
    >
      {{ item.label }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'ui-switch',
  components: {},
  props: {
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
    value: {},
  },
  data() {
    return {
      dataList: [],
      activeValue: '',
    };
  },
  computed: {},
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.dataList = val;
      },
    },
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.activeValue = val;
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    handleClick(item) {
      if (this.activeValue === item.value) return;
      this.activeValue = item.value;
      this.$emit('input', this.activeValue);
      this.$emit('on-change', this.activeValue);
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .switch-wrapper {
    .switch {
      display: inline-block;
      padding: 0 22px 0 22px;
      height: 34px;
      line-height: 34px;
      background: #ffffff;
      border: 1px solid var(--color-primary);
      cursor: pointer;
      color: var(--color-primary);
      &:hover {
        .active();
      }
    }

    .active {
      background: var(--color-primary);
      border: 1px solid var(--color-primary);
      opacity: 1;
      color: #ffffff;
    }
  }
}
.switch-wrapper {
  .switch {
    display: inline-block;
    padding: 0 22px 0 22px;
    height: 34px;
    line-height: 34px;
    background: #12294e;
    border: 1px solid #174f98;
    border-radius: 4px;
    cursor: pointer;
    color: rgba(86, 120, 156, 1);
    &:hover {
      .active();
    }
    &:not(:last-of-type){
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &:not(:first-of-type){
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .switch:first-of-type:not(:last-of-type),
  .switch + .switch:not(:last-of-type) {
    border-right-color: transparent;
  }

  .active {
    background: rgba(43, 132, 226, 1);
    border: 1px solid rgba(27, 130, 210, 1);
    opacity: 1;
    color: #ffffff;
  }
}
</style>
