export default class coordHelper {
  pi = 3.14159265358979324; // 圆周率
  ee = 0.00669342162296594323; // WGS 偏心率的平方
  x_pi = (3.14159265358979324 * 3000.0) / 180.0;
  pole = 20037508.34;
  a = 6378245.0; // WGS 长轴半径
  // 84->火星
  transform(lon, lat) {
    let localHashMap = {};
    if (this.outofChina(lat, lon)) {
      localHashMap.lon = lon;
      localHashMap.lat = lat;
      return localHashMap;
    }
    let dLat = this.transformLat(lon - 105.0, lat - 35.0);
    let dLon = this.transformLon(lon - 105.0, lat - 35.0);
    let radLat = (lat / 180.0) * this.pi;
    let magic = Math.sin(radLat);
    magic = 1 - this.ee * magic * magic;
    let sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / (((this.a * (1 - this.ee)) / (magic * sqrtMagic)) * this.pi);
    dLon = (dLon * 180.0) / ((this.a / sqrtMagic) * Math.cos(radLat) * this.pi);
    let mgLat = lat + dLat;
    let mgLon = lon + dLon;
    localHashMap.lon = mgLon;
    localHashMap.lat = mgLat;
    return localHashMap;
  }

  outofChina(lat, lon) {
    if (lon < 72.004 || lon > 137.8347) return true;
    if (lat < 0.8293 || lat > 55.8271) return true;
    return false;
  }

  transformLat(x, y) {
    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
    ret += ((20.0 * Math.sin(6.0 * x * this.pi) + 20.0 * Math.sin(2.0 * x * this.pi)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(y * this.pi) + 40.0 * Math.sin((y / 3.0) * this.pi)) * 2.0) / 3.0;
    ret += ((160.0 * Math.sin((y / 12.0) * this.pi) + 320 * Math.sin((y * this.pi) / 30.0)) * 2.0) / 3.0;
    return ret;
  }

  transformLon(x, y) {
    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
    ret += ((20.0 * Math.sin(6.0 * x * this.pi) + 20.0 * Math.sin(2.0 * x * this.pi)) * 2.0) / 3.0;
    ret += ((20.0 * Math.sin(x * this.pi) + 40.0 * Math.sin((x / 3.0) * this.pi)) * 2.0) / 3.0;
    ret += ((150.0 * Math.sin((x / 12.0) * this.pi) + 300.0 * Math.sin((x / 30.0) * this.pi)) * 2.0) / 3.0;
    return ret;
  }
  // 火星->84
  gcj2wgs(lon, lat) {
    let p = {
      lon: 0,
      lat: 0,
    };
    let lontitude = lon - (this.transform(lon, lat).lon - lon);
    let latitude = lat - (this.transform(lon, lat).lat - lat);
    p.lon = lontitude;
    p.lat = latitude;
    return p;
  }

  // 火星坐标转百度坐标
  bd_encrypt(gg_lon, gg_lat) {
    let x = gg_lon,
      y = gg_lat;
    let z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * this.x_pi);
    let theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * this.x_pi);
    let bd_lon = z * Math.cos(theta) + 0.0065;
    let bd_lat = z * Math.sin(theta) + 0.006;
    return {
      lon: bd_lon,
      lat: bd_lat,
    };
  }

  // 百度坐标转火星坐标
  bd_decrypt(bd_lon, bd_lat) {
    console.log(bd_lon, bd_lat);
    let x = bd_lon - 0.0065,
      y = bd_lat - 0.006;
    let z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * this.x_pi);
    let theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * this.x_pi);
    let gg_lon = z * Math.cos(theta);
    let gg_lat = z * Math.sin(theta);
    return {
      lon: gg_lon,
      lat: gg_lat,
    };
  }

  // 经纬度-> 墨卡托投影转换
  webMoctorJW2PM(lon, lat) {
    let c = {
      lon: 0,
      lat: 0,
    };
    c.lon = (lon / 180.0) * 20037508.34;
    if (lat > 85.05112) {
      lat = 85.05112;
    }
    if (lat < -85.05112) {
      lat = -85.05112;
    }
    lat = (Math.PI / 180.0) * lat;
    let tmp = Math.PI / 4.0 + lat / 2.0;
    c.lat = (20037508.34 * Math.log(Math.tan(tmp))) / Math.PI;
    return c;
  }
  // 墨卡托投影转换-》经纬度
  inverseMercator(lon, lat) {
    lon = (180 * lon) / this.pole;
    lat = (180 / Math.PI) * (2 * Math.atan(Math.exp((lat / this.pole) * Math.PI)) - Math.PI / 2);
    return {
      lon: lon,
      lat: lat,
    };
  }
}
