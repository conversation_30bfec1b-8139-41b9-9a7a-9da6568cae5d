<template>
  <div class="page-evaluationmanagement">
    <div class="content auto-fill">
      <div class="form">
        <ui-label class="inline" label="接口名称" :width="65">
          <Input class="width-md" v-model="searchData.intefaceName" placeholder="请输入接口名称" />
        </ui-label>
        <ui-label class="inline ml-lg" label="注册对象" :width="65">
          <api-organization-tree
            class="tree-style"
            :select-tree="selectOrgTree"
            @selectedTree="selectedOrgTree"
            placeholder="请选择组织机构"
          />
        </ui-label>
        <ui-label class="inline ml-lg" label="接口类型" :width="65">
          <Select v-model="searchData.intefaceType" class="width-md" placeholder="请选择接口类型">
            <Option :value="item.dataKey" v-for="(item, index) in interfaceTypeList" :key="index"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <div class="inline ml-sm">
          <Button type="primary" @click="searchHandle">查询</Button>
          <Button class="ml-sm" @click="resetHandle">重置</Button>
        </div>
        <Button class="fr ml-sm" type="primary" @click="updateOrAddHandle()">
          <i class="icon-font icon-tianjia f-12 mr-sm"></i>
          <span class="inline vt-middle">接口注册</span>
        </Button>
      </div>
      <div class="table-box auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #createTime="{ row }">
            <span>{{ row.createTime || '--' }}</span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip icon="icon-bianji2" content="编辑" @click.native="updateOrAddHandle(row)"></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
    </div>
    <updateOrAdd
      v-if="updateOrAddVisible"
      ref="updateOrAdd"
      :interfaceTypeList="interfaceTypeList"
      @refreshDataList="getTableList"
    />
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import api from '@/config/api/cascadeIntefaceConfig';
import updateOrAdd from './components/update-or-add';
import UiTable from '@/components/ui-table';
import apiOrganizationTree from '@/api-components/api-organization-tree';

export default {
  name: 'supInterfaceConfig',
  components: {
    apiOrganizationTree,
    updateOrAdd,
    UiTable,
  },
  data() {
    return {
      loading: false,
      updateOrAddVisible: false,
      selectOrgTree: {
        orgCode: '',
      },
      interfaceTypeList: [
        { dataValue: '上报接口', dataKey: '1' },
        { dataValue: '查询接口', dataKey: '2' },
        { dataValue: '异常接口', dataKey: '3' },
        { dataValue: '其他', dataKey: '999' },
      ],
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '接口名称', key: 'intefaceName' },
        { title: '注册对象', key: 'orgName' },
        { title: '接口类型', key: 'intefaceTypeName' },
        { title: '接口地址', key: 'intefaceUrl' },
        { title: '授权账户', key: 'username' },
        { title: '注册时间', slot: 'createTime' },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          align: 'center',
          width: 60,
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      searchData: {
        intefaceName: '',
        intefaceType: '',
        orgCode: '',
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmTypeList: 'algorithm/algorithmType',
    }),
  },
  async created() {
    this.getTableList();
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        let res = await this.$http.post(api.subInterfaceList, this.searchData);
        let resultData = res.data.data;
        this.tableData = resultData.entities.map((item) => {
          item.intefaceTypeName = this.interfaceTypeList.find((d) => d.dataKey === item.intefaceType).dataValue;
          return item;
        });
        this.pageData.totalCount = resultData.total;
        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
    },
    // 检索
    searchHandle() {
      this.searchData.pageNum = 1;
      this.getTableList();
    },
    // 重置
    resetHandle() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 10 };
      this.selectOrgTree.orgCode = '';
      this.searchData = {
        intefaceName: '',
        params: { pageNumber: 1, pageSize: 10 },
      };
      this.algorithmVendorType = '';
      this.getTableList();
    },
    // 机构选择
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    // 新增编辑
    updateOrAddHandle(row) {
      this.updateOrAddVisible = true;
      this.$nextTick(() => {
        this.$refs.updateOrAdd.showModal(row);
      });
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      // this.searchData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },
  },
};
</script>
<style lang="less" scoped>
.page-evaluationmanagement {
  .left-content {
    background: var(--bg-content);
    float: left;
    width: 260px;
    padding: 10px;
    background: var(--bg-content);
    height: 100%;

    .record-title {
      padding: 10px 0;

      .add_case {
        .name {
          margin-left: 10px;
          position: relative;
          top: 2px;
        }
      }
    }

    .collapse-content-p {
      border: 1px solid transparent;
      border-radius: 4px;
      padding: 10px;
      color: @font-color-white;
      background: @bg-table-block;
      margin-bottom: 10px;

      &.active {
        border-color: @color-other;
      }
    }

    .assessment-list {
      position: relative;
    }
  }

  .content {
    // float: right;
    // width: calc(~"100% - 270px");
    width: 100%;
    height: 100%;
    // padding-top: 20px;
    background: var(--bg-content);

    .search-wrapper {
      overflow: hidden;
      padding: 0 12px 0 20px;

      .input-width {
        width: 363px;
      }
    }

    .table-box {
      padding: 0 20px;
      position: relative;

      .sucess {
        color: @color-success;
      }

      .error {
        color: @color-failed;
      }

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.left-item {
  @{_deep} .ivu-form-item-label {
    width: 200px;
  }

  @{_deep} .ivu-form-item-error-tip {
    line-height: 1;
  }
}
</style>
<style lang="less" scoped>
.page-indexmanagement {
  .form-content {
    padding: 0 50px;

    .ivu-form-item {
      margin-bottom: 20px;

      .time {
        width: 22%;
      }

      .lag {
        width: 13.5%;
        margin: 0 10px;
      }

      .canshu {
        width: 15%;
      }
    }
  }
}

.form {
  padding: 20px;

  .based-field-label {
    color: #fff;
    font-size: 14px;
    padding-right: 12px;
  }

  button {
    margin-left: 12px;
  }
}
</style>
