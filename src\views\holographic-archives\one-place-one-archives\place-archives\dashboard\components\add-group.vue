<template>
  <Modal
    v-model="groupShow"
    title="添加到我的分组"
    center
    :width="460"
    @on-cancel="onCancel"
  >
    <div class="group-container">
      <div class="content-header">
        <div
          class="grouping-tip"
          v-show="!groupInput"
          @click="groupInput = true"
        >
          <Icon type="md-add" />
          <span>添加到新的分组</span>
        </div>
        <div class="grouping-input" v-show="groupInput">
          <Input
            v-model.trim="groupName"
            :maxlength="20"
            placeholder="请输入新分组名称"
            style="width: 100%"
          >
            <Icon
              class="confirm-icon"
              type="md-checkmark"
              title="确定"
              slot="suffix"
              @click="addNewGroup"
            />
            <Icon
              type="md-close"
              title="取消"
              slot="suffix"
              @click="groupInput = false"
            />
          </Input>
        </div>
      </div>
      <div class="content-box">
        <RadioGroup v-model="selectGroup">
          <Radio
            :label="item.id"
            v-for="(item, index) in groupList"
            :key="index"
            >{{ item.groupName }}</Radio
          >
        </RadioGroup>
        <ui-empty v-if="!groupList.length"></ui-empty>
        <ui-loading v-show="loading"></ui-loading>
      </div>
    </div>
    <div slot="footer">
      <Button class="btn cancel" type="default" @click="onCancel">取消</Button>
      <Button
        :loading="submitLoading"
        type="primary"
        class="btn ok"
        @click="onSubmit"
      >
        {{ submitLoading ? "提交中" : "确定" }}
      </Button>
    </div>
  </Modal>
</template>

<script>
import { queryMyVideoGroupList, addMyVideoGroup } from "@/api/player";
import { batchSave } from "@/api/operationsOnTheMap";
import { mapGetters } from "vuex";
export default {
  name: "AddGroup",
  data() {
    return {
      groupShow: false, //分组弹框
      groupInput: false, // 创建新分组标识
      groupName: "", // 组名
      selectGroup: "", // 选择的组名
      loading: false, // 请求分组列表loading
      submitLoading: false, // 确认loading
      groupList: [], // 分组列表
      deviceId: "", // 要加入分组的设备
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  methods: {
    /**
     * @description: 打开分组弹框
     * @param {string} deviceId 设备id
     */
    show(deviceId) {
      this.groupShow = true;
      this.deviceId = deviceId;
      this.getGroupList();
    },

    /**
     * @description: 获取分组列表
     */
    getGroupList() {
      this.loading = true;
      queryMyVideoGroupList({ userId: this.userInfo.id, searchKey: "" })
        .then((res) => {
          this.groupList = res.data.grouplist;
          if (this.groupName) {
            this.groupList.forEach((item) => {
              if (item.groupName == this.groupName) {
                this.selectGroup = item.id;
              }
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * @description: 确认新建的分组
     */
    addNewGroup() {
      if (!this.groupName) {
        this.$Message.warning("请输入新的分组名称");
        return;
      }
      let params = {
        userId: this.userInfo.id,
        groupName: this.groupName,
      };
      addMyVideoGroup(params).then((res) => {
        if (res.code == 200) {
          this.$Message.success("新增完成");
          this.getGroupList();
        }
      });
    },

    /**
     * @description: 取消添加分组
     */
    onCancel() {
      this.groupShow = false;
      // 重置状态
      this.groupInput = false;
      this.groupName = "";
      this.groupList = [];
      this.selectGroup = "";
    },

    /**
     * @description: 确认添加分组
     */
    onSubmit() {
      this.submitLoading = true;
      batchSave({
        groupId: this.selectGroup,
        deviceIds: [this.deviceId],
      })
        .then(() => {
          this.$Message.success("添加成功");
          this.onCancel();
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.group-container {
  --tip-height: 34px;
  height: 400px;
  .grouping-tip {
    color: #2c86f8;
    font-size: 14px;
    height: var(--tip-height);
    display: flex;
    align-items: center;
    cursor: pointer;
    .ivu-icon-md-add {
      font-size: 20px;
    }
  }
  /deep/ .ivu-input-suffix {
    right: 7px;
    width: 50px;
    i {
      margin-left: 5px;
      font-weight: bold;
      cursor: pointer;
    }
    .confirm-icon {
      color: #2c86f8;
    }
  }
  .content-box {
    position: relative;
    width: 100%;
    height: calc(~"100% - var(--tip-height)");
    padding: 20px;
    overflow: auto;
    /deep/ .ivu-radio-group {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }
}
</style>