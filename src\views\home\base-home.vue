<template>
  <!--  flex-column-center -->
  <div class="height-full width-full" :class="homeStyle">
    <div class="home-container" :class="fullScreen" v-if="!navigationPageShow && !componentName">
      <div class="home-animation" v-if="isStyle1">
        <img class="image" src="@/assets/img/base-home/circle-1.png" />
        <img class="image" src="@/assets/img/base-home/circle-2.png" />
        <img class="image" src="@/assets/img/base-home/circle-3.png" />
        <img class="image" src="@/assets/img/base-home/circle-4.png" />
        <img class="left-circular" src="@/assets/img/base-home/left-circular.png" />
        <img class="right-circular" src="@/assets/img/base-home/right-circular.png" />
      </div>
      <top-title v-if="getFullscreen && isStyle1"></top-title>
      <top-title2 class="top-title2" v-if="getFullscreen && !isStyle1"></top-title2>
      <div class="home-base-container" :class="fullScreen">
        <div v-for="item in componentsPositionList" :key="item.componentName" :style="item.style" class="base-div">
          <empty-occupy
            :component-list="item.componentList"
            :evaluation-index-result="evaluationIndexResultData"
            :table-data="evaluationIndexResultData"
            :loading="loading"
            :is-edit-status="false"
            :home-page-config="homePageConfig"
            :style-type="getHomeStyleType"
            @on-jump="jump"
            @on-index-detail="openDetailsDialog"
          >
          </empty-occupy>
        </div>
      </div>
      <!-- 底部光效 -->
      <div id="bottom-lottie" v-if="isStyle1"></div>
      <map-echarts
        v-if="!isActivated"
        :home-page-config="homePageConfig"
        :query-access-data="queryAccessData"
        :style-type="getHomeStyleType"
        @on-jump="onJumpMap"
      ></map-echarts>
      <!--  顶部统计  -->
      <statistics-top v-if="isStyle1" ref="statistics-top" :query-access-data="queryAccessData">
        <template #tipslot1>
          <map-dom :outer-data="staticTooltipdata" :has-detail="false"></map-dom>
        </template>
      </statistics-top>
      <statistics-top2
        v-else
        ref="statistics-top"
        class="statistics-top"
        :query-access-data="queryAccessData"
      ></statistics-top2>
      <full-screen ref="full-screen" @changeHome="changeHome" @on-change-full-screen="onChangeFullScreen"></full-screen>
      <div class="location" :class="getFullscreen ? 'full-screen-location' : ''">
        <notice
          class="ml-llg"
          ref="noticeRef"
          :key="getFullscreen"
          :width="getFullscreen && isStyle1 ? 300 : 600"
          @click.native="noticeMore"
        >
          <!-- <span @click="noticeMore" class="pointer view-detail ml-sm">更多公告 >></span> -->
        </notice>
      </div>
    </div>
    <navigation-page
      v-else-if="navigationPageShow && !componentName"
      @backHome="() => (navigationPageShow = false)"
    ></navigation-page>
    <keep-alive>
      <component :is="componentName"> </component>
    </keep-alive>
    <create-tabs
      ref="createTabsRef"
      class="inline"
      :componentName="activeComponent"
      :tabs-text="activeTabsName"
      @selectModule="selectModule"
      :tabs-query="activeTabsQuery"
    >
    </create-tabs>
    <!-- 指标配置详情 -->
    <common-model ref="model" v-model="indexVisible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>

<script>
import BaseHomeExtends from '@/views/home/<USER>/utils/baseHome.js';
import { getComponentConfig } from '@/views/home/<USER>/utils';
import { mapGetters, mapActions } from 'vuex';
export default {
  extends: BaseHomeExtends,
  components: {
    EmptyOccupy: require('@/views/home/<USER>/empty-occupy/index.vue').default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
  },
  data() {
    return {
      componentsPositionList: [],
      indexVisible: false,
      currentRow: {},
    };
  },
  async activated() {
    // configFunc 在BaseHomeExtends里面 - 获取首页配置的接口
    let data = await this.configFunc('HOME_PAGE_VISIUAL_CONFIG');
    const { defaultComponentList, baseHomePosition, longEchartPosition, normalEchartPosition } = getComponentConfig(
      `style${this.getHomeStyleType}`,
    );
    let defaultConfig = defaultComponentList;
    let styleIndexObject = {};
    defaultConfig = this.mergeConfig(data, defaultComponentList);
    defaultConfig.forEach((item) => {
      // visiualShow 这个字段为true才会展示到页面上面
      if (item?.visiualShow) {
        // 组装数据
        if (item.styleIndex in styleIndexObject) {
          styleIndexObject[item.styleIndex].componentList.push(item);
        } else {
          let dynamicPosition = item?.isLongEchart ? longEchartPosition : normalEchartPosition;
          let posObj = { ...baseHomePosition, ...dynamicPosition };
          styleIndexObject[item.styleIndex] = {
            style: posObj[item.styleIndex],
            componentList: [item],
          };
        }
      }
    });
    this.componentsPositionList = Object.values(styleIndexObject);
  },
  created() {
    this.setHomeStyleType();
  },
  methods: {
    ...mapActions({
      setHomeStyleType: 'systemconfiguration/setHomeStyleType',
    }),
    openDetailsDialog(row) {
      let { indexType, indexName, indexId } = row;
      this.indexVisible = true;
      this.currentRow = {
        indexType: indexType,
        indexName: indexName,
        id: indexId,
      };
    },
    /**
     * 合并配置
     * @param target defaultComponentList即componentConfig.js的配置
     * @param source 从接口中返回的配置
     * @return {Object} target中有的配置
     */
    mergeConfig(target, source) {
      return target.map((item) => {
        let sourceComponent = source.find((value) => value.componentId === item.componentId);
        return {
          ...sourceComponent,
          ...item,
        };
      });
    },
  },
  computed: {
    ...mapGetters({
      getHomeStyleType: 'systemconfiguration/getHomeStyleType',
    }),
    isStyle1() {
      return this.getHomeStyleType === '1';
    },
    homeStyle() {
      return `home-style${this.getHomeStyleType}`;
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/views/home/<USER>/base-home.less';
</style>
