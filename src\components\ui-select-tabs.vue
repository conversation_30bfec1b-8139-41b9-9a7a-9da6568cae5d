<template>
  <div>
    <div
      v-if="tagList.length !== 0"
      :class="['tabs', needExpand ? 'tabs-bg-color' : '']"
      ref="tabs"
      v-clickoutside="hide"
    >
      <i
        class="icon-font icon-xialazhankai up"
        v-if="needShow"
        :class="{ rotate: !showAll }"
        @click="showAll = !showAll"
      >
      </i>
      <div class="positions" :class="[needExpand ? 'positions-bg-color' : '']">
        <div class="content" :class="{ single: !showAll }">
          <ul class="ul" ref="ul">
            <li
              :class="{ active: multiSelect ? item.select : curIndex == index }"
              v-for="(item, index) in tagList"
              :key="index"
              @click="liClick(item, index)"
            >
              {{ item[defaultProps.name] }}
            </li>

            <div class="clear-b"></div>
          </ul>
        </div>
      </div>
    </div>
    <div class="not-available" v-else>暂无数据</div>
  </div>
</template>
<script>
export default {
  name: 'ui-select-tabs',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    //是否需要展开按钮
    needExpand: {
      type: Boolean,
      default: true,
    },
    // 是否开启多选，默认开启多选
    multiSelect: {
      type: Boolean,
      default: true,
    },
    // 设置默认选中项 index
    defaultSelectIndexs: {
      type: Array,
      default: () => [],
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          name: 'name', // 取值字段
        };
      },
    },
  },
  data() {
    return {
      showAll: false,
      curIndex: 0,
      needShow: true,
      tagList: [],
    };
  },
  mounted() {},
  methods: {
    liClick(item, index) {
      // 多选
      if (this.multiSelect) {
        this.$set(item, 'select', !item.select);
      } else {
        // 单选
        this.curIndex = index;
      }
      this.$emit('selectInfo', this.getSelection());
    },

    // 获取已选择的所有数据
    getSelection() {
      // 多选
      if (this.multiSelect) {
        return this.tagList.filter((item) => {
          return item.select;
        });
      } else {
        // 单选
        return this.tagList[this.curIndex];
      }
    },

    // 重置
    reset() {
      this.tagList.forEach((item) => {
        this.$set(item, 'select', false);
      });
    },
    hide() {
      this.showAll = false;
    },
    init() {
      this.tagList = this.$util.common.deepCopy(this.list || []);
      this.$nextTick(() => {
        if (this.$refs.tabs && this.$refs.ul) {
          const tabsWidth = this.$refs.tabs.offsetWidth;
          const ulWidth = this.$refs.ul.offsetWidth;
          if (tabsWidth - ulWidth - 60 > 0) {
            this.needShow = false;
          } else {
            this.needShow = true;
          }
        }
      });
    },
  },
  watch: {
    list: {
      handler() {
        this.init();
      },
      deep: true,
      immediate: true,
    },
    defaultSelectIndexs: {
      handler(val) {
        if (!val || val.length === 0) {
          return;
        }
        // 多选
        if (this.multiSelect) {
          this.tagList.forEach((item, index) => {
            this.$set(item, 'select', val.includes(index));
          });
        } else {
          // 单选
          this.curIndex = val[0];
        }
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.tabs {
  position: relative;
  // padding: 6px;
  min-height: 48px;
  padding-right: 60px;
  z-index: 10;
  background: var(--bg-nav);
  .positions {
    position: absolute;
    width: 100%;
    padding: 0 10px;
    z-index: 6;
  }
  .positions-bg-color {
    background: var(--bg-nav);
  }

  .up {
    position: absolute;
    right: 10px;
    top: 15px;
    //color: #18599e;
    font-size: 12px;
    z-index: 10;
    transform: rotate(180deg) scale(0.8);
    color: #888888;
  }

  .rotate {
    transform: scale(0.8);
  }

  .content {
    max-height: 129px;
    margin-top: 6px;
    overflow: auto;
    padding-right: 25px;
  }

  .ul {
    display: flex;
    flex-wrap: wrap;
    li {
      border: 1px solid var(--border-nav-tag);
      color: var(--color-nav-tag);
      background: var(--bg-nav-tag);
      //float: left;
      padding: 6px 22px;
      //height: 32px;
      //line-height: 32px;
      //border: 1px solid #085c8a;
      //color: #8797ac;
      border-radius: 4px;
      margin-right: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      &:hover {
        border: 1px solid var(--border-nav-tag-active);
        color: var(--color-nav-tag-active);
        background: var(--bg-nav-tag-active);
      }
    }
    .active {
      border: 1px solid var(--border-nav-tag-active);
      color: var(--color-nav-tag-active);
      background: var(--bg-nav-tag-active);
    }
  }

  .single {
    position: absolute;
    height: 35px;
    overflow: hidden;
    z-index: 8;
  }
}
.not-available {
  width: 100%;
  position: relative;
  min-height: 46px;
  z-index: 10;
  background: var(--bg-nav);
  line-height: 46px;
  text-align: center;
  font-size: 14px;
}
</style>
