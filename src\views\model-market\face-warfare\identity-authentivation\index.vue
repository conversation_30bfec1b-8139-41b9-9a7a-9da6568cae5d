<!--
    * @FileDescription: 身份鉴别
    * @Author: H
    * @Date: 2023/09/08
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-22 15:59:36
 -->
<template>
  <div class="identity">
    <div class="left-search">
      <div class="title">
        <p>身份鉴别</p>
      </div>
      <div class="upload-img">
        <uploadImg
          key="1"
          :algorithmType="1"
          v-model="queryParam.leftImg"
          @imgUrlChange="imgUrlChange"
        ></uploadImg>
      </div>
      <div class="form-box">
        <Form :inline="true" :label-width="75">
          <FormItem label="相似度:">
            <div class="slider-content">
              <Slider v-model="queryParam.similarity"></Slider>
              <span>{{ queryParam.similarity }}%</span>
            </div>
          </FormItem>
          <FormItem label="算法选择:" v-if="queryParam.imageBase">
            <CheckboxGroup
              v-model="queryParam.algorithmVendorType"
              style="width: 259px"
            >
              <Checkbox
                :label="item.dataKey"
                v-for="(item, index) in algorithmTypeList"
                :key="index"
              >
                <span :class="[item.dataKey == 'GLST' ? 'gerling' : 'hk']">{{
                  item.dataValue
                }}</span>
              </Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem label="比对库:">
            <div class="select-tag-button" @click="selectLibrary()">
              选择库/已选（{{ queryParam.faceLibIds.length }}）
            </div>
          </FormItem>
          <FormItem label="姓名:">
            <Input
              v-model="queryParam.name"
              placeholder="请输入"
              style="width: 259px"
            ></Input>
          </FormItem>
          <FormItem label="身份证号:">
            <Input
              v-model="queryParam.idCardNo"
              placeholder="请输入"
              style="width: 259px"
            ></Input>
          </FormItem>
          <FormItem label="性别:">
            <ui-tag-select
              ref="gender"
              @input="
                (e) => {
                  input(e, 'sex');
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in ipbdFaceCaptureGender"
                :key="$index"
                :name="item.dataKey"
              >
                {{ item.dataValue }}
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
          <FormItem label="民族:">
            <Select
              v-model="queryParam.national"
              clearable
              placeholder="请选择"
              style="width: 259px"
            >
              <Option
                :value="item.dataKey"
                :label="item.dataValue"
                v-for="(item, index) in nationList"
                :key="index"
              ></Option>
            </Select>
          </FormItem>
          <FormItem label="籍贯:">
            <Input
              v-model="queryParam.nativePlace"
              placeholder="请输入"
              style="width: 259px"
            ></Input>
          </FormItem>
        </Form>
        <div class="btn-group">
          <Button type="primary" style="width: 248px" @click="searchHandle"
            >查询</Button
          >
          <Button @click="resetHandle">重置</Button>
        </div>
        <p class="search-tips">
          <i class="iconfont icon-tishi"></i> 无照片时需要填写姓名或身份证号码
        </p>
      </div>
    </div>
    <div class="two-list" v-if="moreBox">
      <div class="top-box">
        <div class="title">
          <p>
            {{
              queryParam.algorithmVendorType[0]
                | commonFiltering(algorithmTypeList)
            }}算法比对结果：
          </p>
          <!-- <p>{{ algorithmTypeList[0].dataValue }}算法比对结果：</p> -->
        </div>
        <hk ref="hk" :queryParam="queryParam"></hk>
      </div>
      <div class="bottom-box">
        <div class="title">
          <p>
            {{
              queryParam.algorithmVendorType[1]
                | commonFiltering(algorithmTypeList)
            }}算法比对结果：
          </p>
          <!-- <p>{{ algorithmTypeList[1].dataValue }}算法比对结果：</p> -->
        </div>
        <geling ref="geling" :queryParam="queryParam"></geling>
      </div>
    </div>
    <div class="right-list" v-else>
      <div class="title" v-if="dataList.length !== 0">
        <p>查询结果</p>
      </div>
      <div
        class="data-export"
        v-if="queryParam.imageBase && dataList.length > 0"
      >
        <Button
          class="mr"
          :type="queryParam.order == 'similarity' ? 'primary' : 'default'"
          @click="handleSort('similarity')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
      </div>
      <div class="box-content">
        <listCard
          v-for="(item, ind) in dataList"
          :key="ind"
          :item="item"
          :origin="'SFIdentity'"
        ></listCard>
      </div>
      <div
        class="empty-card-1"
        v-for="(item, index) of 9 - (dataList.length % 9)"
        :key="index + 'demo'"
      ></div>
      <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
      <p class="tips" v-if="dataList.length == 0 && !loading">
        请在左侧操作后查询结果
      </p>
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[27, 54, 81, 108]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>

    <selectLibraty
      ref="selectLibrar"
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    ></selectLibraty>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import uploadImg from "@/components/ui-upload-new-img/face-comparison";
import selectLibraty from "@/components/select-modal/select-libraty.vue";
import { faceIdentity } from "@/api/modelMarket";
import {
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import listCard from "./components/list-card.vue";
import geling from "./components/geling.vue";
import hk from "./components/hk.vue";
export default {
  name: "identity-authentivation",
  components: {
    uploadImg,
    selectLibraty,
    listCard,
    geling,
    hk,
  },
  data() {
    return {
      queryParam: {
        similarity: 75,
        faceLibIds: [],
        algorithmVendorType: [],
        imageBase: "",
        order: "desc",
      },
      similUpDown: false,
      loading: false,
      checkedLabels: [],
      dataList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      total: 0,
      moreBox: false,
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      ipbdFaceCaptureGender: "dictionary/getIpbdFaceCaptureGender", // 性别
      nationList: "dictionary/getNationList", //民族
    }),
  },
  async created() {
    await this.getDictData();
    if (this.$route.query.imgUrl) {
      let params = {};
      if (this.$route.query.selectSquare) {
        // 当又该字段时，直接用大图
        let square = this.$route.query.selectSquare
          .split(",")
          .map((v) => Number(v));
        params = {
          left: square[0],
          right: square[2],
          top: square[1],
          bottom: square[3],
          feature: null,
          imageBase: null,
          imageUrl: this.$route.query.imgUrl,
        };
      } else {
        let fileData = new FormData();
        fileData.append("algorithmType", 1);
        fileData.append("fileUrl", this.$route.query.imgUrl);
        const res = await picturePick(fileData);
        params = res.data?.length ? res.data[0] : {};
        if (res.data?.length == 0) {
          return this.$Message.warning("没有提取到目标");
        }
      }

      const response = await this.getBase64ByImageCoordinate(params);
      let urlList = {
        fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
        feature: response.data.feature,
        imageBase: response.data.imageBase,
      };

      this.queryParam.leftImg = urlList;
    }
    console.log(this.$route.query.imgUrl, "this.$route.query.imgUrl");
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 排序
    handleSort(val) {
      this.similUpDown = !this.similUpDown;
      this.queryParam.order = this.similUpDown ? "asc" : "desc";
      this.getDataList();
    },
    // 查询
    searchHandle() {
      if (
        this.queryParam.imageBase == "" &&
        !this.queryParam.name &&
        !this.queryParam.idCardNo
      ) {
        this.$Message.warning("无照片时需要填写姓名或身份证号码");
        return;
      }
      if (this.queryParam.imageBase && this.queryParam.faceLibIds.length == 0) {
        this.$Message.warning("请选择比对库");
        return;
      }
      if (
        this.queryParam.imageBase &&
        this.queryParam.algorithmVendorType.length == 0
      ) {
        this.$Message.warning("请选择算法");
        return;
      }
      if (this.queryParam.algorithmVendorType.length == 2) {
        this.moreBox = true;
        this.$nextTick(() => {
          this.$refs.hk.queryList();
          this.$refs.geling.queryList();
        });
      } else {
        this.getDataList();
      }
    },
    getDataList() {
      this.moreBox = false;
      this.loading = true;
      let faceLibIds = this.queryParam.faceLibIds.map((item) => item.id);
      let params = {
        idCardNo: this.queryParam.idCardNo,
        imageBase: this.queryParam.imageBase,
        name: this.queryParam.name,
        national: this.queryParam.national,
        sex: this.queryParam.sex,
        similarity: this.queryParam.similarity / 100,
        nativePlace: this.queryParam.nativePlace,
        algorithmVendorType: this.queryParam.algorithmVendorType[0],
        userId: this.userInfo.id,
        ...this.pageInfo,
        faceLibIds: faceLibIds,
        sortField: "similarity",
      };
      faceIdentity(params)
        .then((res) => {
          if (res.data) {
            this.loading = false;
            this.dataList = res.data.entities || [];
            this.total = res.data.total;
          } else {
            this.$Message.warning("没有提取到目标");
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    imgUrlChange(val) {
      this.queryParam.imageBase = val.imageBase || "";
      if (this.queryParam.imageBase) {
        this.queryParam.algorithmVendorType = this.algorithmTypeSelect;
      } else {
        this.queryParam.algorithmVendorType = [];
      }
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
    // 重置
    resetHandle() {
      this.queryParam = {
        imageBase: "",
        leftImg: {},
        similarity: 75,
        algorithmVendorType: this.algorithmTypeSelect[0],
        faceLibIds: [],
        name: "",
        idCardNo: "",
        national: "",
        nativePlace: "",
        gender: "",
      };
      this.moreBox = false;
      this.$refs.gender.clearChecked();
      this.dataList = [];
      this.total = 0;
    },
    // 库
    selectLibrary() {
      this.$refs.selectLibrar.show(this.queryParam.faceLibIds);
    },
    selectData(list) {
      this.queryParam.faceLibIds = list;
    },
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.queryParam[key] = e;
      this.$forceUpdate();
    },
    // 根据图片坐标截取图片base64
    getBase64ByImageCoordinate(data) {
      // 只有人脸有身份核验，所以这里写死face
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },
  },
};
</script>

<style lang="less" scoped>
.identity {
  padding: 0;
  position: relative;
  width: 100%;
  height: inherit;
  display: flex;
  .left-search {
    width: 370px;
    height: inherit;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
    border-radius: 4px 4px 4px 4px;
    margin-right: 10px;
    .upload-img {
      width: 240px;
      height: 300px;
      background: url("~@/assets/img/model/face-box.png") no-repeat;
      display: flex;
      justify-content: center;
      align-items: end;
      font-size: 20px;
      font-weight: 700;
      color: #2c86f8;
      margin: 20px auto 30px;
      /deep/ .upload-btn {
        margin-bottom: 24px;
      }
      /deep/ .img-content {
        width: 210px;
        height: 210px;
        top: 16px;
        left: 16px;
      }
    }
    .form-box {
      padding: 0 5px;
      .gerling {
        color: #f29f4c;
      }
      .btn-group {
        padding: 0 10px;
      }
      .icon-tishi {
        color: #f29f4c;
      }
      .search-tips {
        margin: 5px 10px;
      }
    }
  }
  .right-list {
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
    border-radius: 4px 4px 4px 4px;
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    .box-content {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: start;
      align-content: flex-start;
    }
    .pages {
      padding: 0 20px;
    }
    .data-export {
      margin: 10px 0 0 20px;
    }
  }
  .two-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .top-box {
      height: 49.5%;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
      border-radius: 4px 4px 4px 4px;
      display: flex;
      flex-direction: column;
    }
    .bottom-box {
      height: 49.5%;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
      border-radius: 4px 4px 4px 4px;
      display: flex;
      flex-direction: column;
    }
  }
  /deep/ .ivu-slider {
    width: 220px;
  }
  /deep/.ivu-form-item {
    margin-bottom: 10px;
  }
  /deep/ .ivu-tag-select-option {
    margin-right: 10px;
  }
  .title {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    height: 40px;
    position: relative;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #d3d7de;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 0;
    z-index: 1;
    background: #fff;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:before {
      content: "";
      position: absolute;
      width: 3px;
      height: 20px;
      top: 50%;
      transform: translateY(-50%);
      left: 10px;
      background: #2c86f8;
    }
    span {
      color: #2c86f8;
    }
    /deep/.ivu-icon-ios-close {
      font-size: 30px;
      cursor: pointer;
    }
  }
}
.empty-card-1 {
  width: 10.7%;
}
.tips {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, 50%);
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
}
/deep/ .ivu-tag-select {
  height: 34px;
}
</style>
