<template>
  <div class="search">
    <Form
      ref="form"
      :inline="true"
      :model="formData"
      :class="visible ? 'advanced-search-show' : ''"
    >
      <div class="general-search">
        <div>
          <FormItem label="" prop="idcardNo">
            <Input
              :placeholder="
                ['video', 2].includes(dataType)
                  ? '请输入视频身份号'
                  : '请输入身份证号码'
              "
              clearable
              v-model="formData.idcardNo"
              maxlength="50"
              class="search-input-430"
            />
          </FormItem>
          <FormItem>
            <div
              class="advanced-search-text"
              @click.stop="advancedSearchHandle($event)"
            >
              <img src="@/assets/img/down-circle-icon.png" alt />
              <span class="primary">{{
                visible
                  ? "普通检索"
                  : searchText === "高级检索"
                  ? "高级检索"
                  : "更多条件"
              }}</span>
            </div>
          </FormItem>
        </div>
        <FormItem class="btn-group">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </div>
      <div
        class="advanced-search"
        @click="($event) => $event.stopPropagation()"
      >
        <div class="upload-input-list">
          <uiUploadImg
            ref="uploadImg"
            v-model="images"
            size="small"
            :algorithmType="1"
            @imgUrlChange="imgUrlChange"
          />
        </div>
        <div class="other-search">
          <div
            class="other-search-top card-border-color"
            :class="{
              'none-color': page === 'holographic' || dataType === 'video',
            }"
          >
            <template v-if="['name', 1, 3].includes(dataType)">
              <FormItem label="姓名:" prop="name">
                <Input placeholder="请输入" v-model="formData.name" clearable />
              </FormItem>
            </template>
            <template v-if="['name', 1, 3].includes(dataType)">
              <FormItem label="民族:" prop="nations">
                <Select
                  v-model="formData.nations"
                  ref="select"
                  transfer
                  multiple
                  filterable
                  :max-tag-count="1"
                >
                  <Option
                    v-for="item in nationList"
                    :value="item.dataKey"
                    :key="item.dataKey"
                    placeholder="请选择"
                  >
                    {{ item.dataValue }}
                  </Option>
                </Select>
              </FormItem>
            </template>
            <template v-if="['name', 1, 3].includes(dataType)">
              <FormItem label="性别:" prop="sex">
                <div :style="{ display: 'flex', height: '100%' }">
                  <ui-tag-select
                    ref="sex"
                    @input="
                      (e) => {
                        input(e, 'sex');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureGender"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </div>
              </FormItem>
            </template>
          </div>
          <div class="other-search-bottom">
            <div>
              <FormItem
                label="标签:"
                prop=""
                v-if="page === 'holographic' || dataType === 'video'"
              >
                <div class="select-tag-button" @click="selectLabelHandle">
                  请选择标签{{
                    formData.labelIds && formData.labelIds.length > 0
                      ? `{已选（${formData.labelIds.length}）}`
                      : ""
                  }}
                </div>
              </FormItem>
              <FormItem
                class="nullLabel"
                prop=""
                v-if="page === 'holographic' || dataType === 'video'"
              >
                <Select
                  v-model="formData.labelType"
                  placeholder="请选择"
                  style="width: 80px"
                  transfer
                >
                  <Option value="2">并集</Option>
                  <Option value="1">交集</Option>
                </Select>
              </FormItem>
              <FormItem label="相似度:" prop="similarity">
                <div class="slider-content">
                  <Slider v-model="formData.similarity"></Slider>
                  <span>{{ formData.similarity }}%</span>
                </div>
              </FormItem>
              <FormItem
                label="标签:"
                prop=""
                v-if="page === 'cloudSearch' && dataType == 'name'"
              >
                <div class="select-tag-button" @click="selectLabelHandle">
                  请选择标签{{
                    formData.labelIds && formData.labelIds.length > 0
                      ? `{ 已选（${formData.labelIds.length}）}`
                      : ""
                  }}
                </div>
              </FormItem>
              <FormItem
                class="nullLabel"
                prop=""
                v-if="page === 'cloudSearch' && dataType == 'name'"
              >
                <Select
                  v-model="formData.labelType"
                  placeholder="请选择"
                  style="width: 80px"
                  transfer
                >
                  <Option value="2">并集</Option>
                  <Option value="1">交集</Option>
                </Select>
              </FormItem>
            </div>
          </div>
        </div>
        <!-- 选择标签 -->
        <LabelModal
          :labelType="2"
          @setCheckedLabel="setCheckedLabel"
          ref="labelModal"
        />
        <!-- 选择设备 -->
        <select-device
          ref="selectDevice"
          @selectData="selectData"
          showOrganization
        />
      </div>
    </Form>
    <!-- <slot name="statistics">
      <div>
        <Button type="primary" @click="startSearch" class="mr-20">查询</Button>
        <Button @click="resetHandle">重置</Button>
      </div>
    </slot> -->
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import uiUploadImg from "@/components/ui-upload-img/index";
import LabelModal from "@/views/holographic-archives/components/relationship-map/label-add";
export default {
  components: {
    uiUploadImg,
    LabelModal,
  },
  props: {
    //1实名档案
    dataType: {
      type: [String, Number],
      default: "",
    },
    //检索类型
    searchText: {
      type: String,
      default: "",
    },
    // 1全息页面2云搜页面
    page: {
      type: String,
      default: "holographic",
    },
  },
  data() {
    return {
      visible: false,
      gender: "全部",
      images: ["", "", "", "", ""], //图片列表显示
      selectDeviceList: [],
      formData: {
        perceiveDate: [],
        deviceIds: [],
        nations: [],
        idcardNo: "",
        sex: "",
        name: "",
        isRealName: "",
        labelZh: "",
        startSnapTime: "",
        endSnapTime: "",
        similarity: 80,
        features: [], //图片入参链接
        labelIds: [],
        labelType: "2",
      },
    };
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      //   genderList: 'dictionary/getGenderList', //性别
      ipbdFaceCaptureGender: "dictionary/getIpbdFaceCaptureGender", // 性别
      nationList: "dictionary/getNationList", //民族
    }),
  },
  async created() {
    /**
     * 走到了mounted生命周期中，如果sectionName = faceContent，并且路径有urlList，则警务实名和视频档需要该参数查询
     * 否则，则表示直接从别的页面跳到该页面，并直接定位到当前组件
     */
    let { urlList, sectionName } = this.$route.query;
    if (sectionName === "faceContent" && urlList) {
      this.images = [JSON.parse(urlList), "", "", "", ""];
    }
    //#endregion
    await this.getDictData();
    this.$nextTick(() => {
      if (
        typeof this.globalObj.searchForPicturesDefaultSimilarity === "string"
      ) {
        this.formData.similarity = Number(
          this.globalObj.searchForPicturesDefaultSimilarity
        );
      } else {
        this.formData.similarity =
          this.globalObj.searchForPicturesDefaultSimilarity;
      }
    });
    window.addEventListener("click", (e) => {
      this.visible = false;
    });
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    /** TODO 做完了 接口还没对
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list;
      let ids = [];
      if (list.length > 0) {
        list.forEach((item) => {
          ids.push(item.deviceId);
        });
      }
      this.formData.deviceIds = ids;
    },
    dateChange(start, end) {
      if (start[1].slice(-8) === "00:00:00") {
        start[1] = start[1].slice(0, -8) + "23:59:59";
      }
      this.formData.perceiveDate = [start[0], start[1]];
      this.formData.startSnapTime = start[0];
      this.formData.endSnapTime = start[1];
    },
    // 高级搜索切换
    advancedSearchHandle($event) {
      $event.stopPropagation();
      if (this.visible) {
        this.visible = false;
      } else {
        this.visible = true;
      }
    },
    // 选择标签
    selectLabelHandle() {
      this.$refs.labelModal.init(this.formData.labelIds);
    },
    // 已选标签
    setCheckedLabel(val) {
      this.formData.labelIds = val;
    },
    // 查询
    startSearch() {
      let arr = [];
      this.images.forEach((ele) => {
        if (ele) {
          arr.push(ele.feature);
        }
      });
      this.formData.features = arr;
      this.visible = false;
      this.$emit("searchForm", this.formData);
    },
    // 性别选择
    input(e, key) {
      this.formData[key] = e;
      this.$forceUpdate();
    },
    // 重置
    resetHandle() {
      this.formData.idcardNo = "";
      this.selectDeviceList = [];
      this.formData.deviceIds = [];
      this.formData.startSnapTime = "";
      this.formData.endSnapTime = "";
      this.$refs.form.resetFields();
      this.images = ["", "", "", "", ""];
      this.formData.similarity = Number(
        this.globalObj.searchForPicturesDefaultSimilarity
      );
      this.formData.labelIds = [];
      this.$refs.labelModal.removeAllHandle();
      this.startSearch();
    },
    clearLabel() {
      this.$refs.labelModal.removeAllHandle();
    },
    setData(data) {
      if (["video", 2].includes(this.dataType)) {
      }
      this.formData.idcardNo = "";
      this.formData.similarity = Number(
        this.globalObj.searchForPicturesDefaultSimilarity
      );
      if (data) {
        if (this.dataType == "video") {
          this.formData.idcardNo = data.archiveNo;
        } else {
          if (data.idcardNo) this.formData.idcardNo = data.idcardNo;
        }
        // if(data.archiveNo) this.formData.archiveNo = data.archiveNo
        if (data.nations) this.formData.nations = data.nations;
        if (data.sex) this.formData.sex = data.sex;
        if (data.name) this.formData.name = data.name;
        if (data.similarity) this.formData.similarity = data.similarity * 100;
        if (data.features) this.formData.features = data.features;
        if (data.labelIds) this.formData.labelIds = data.labelIds;
      }
      this.startSearch();
    },
    // 图片上传
    imgUrlChange(list) {
      this.images = list;
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  position: relative;
  form {
    width: 100%;
  }
  .general-search {
    display: flex;
    justify-content: space-between;
    .advanced-search-text {
      height: 34px;
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
  .advanced-search {
    display: flex;
    align-items: center;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    margin-top: 1px;
    z-index: 10;
    max-height: 0px;
    transition: max-height 0.3s;
    overflow: hidden;
    .upload-input-list {
      padding: 10px 0;
      display: flex;
      /deep/.ivu-upload {
        margin-right: 10px;
      }
    }
    .other-search {
      display: flex;
      flex: 1;
      padding: 10px 0 0 10px;
      box-sizing: border-box;
      flex-direction: column;
      .other-search-top {
        display: flex;
        border-bottom: 1px dashed #fff;
        .perceive-data {
          width: 380px;
        }
      }
      .ivu-form-item {
        margin-bottom: 10px;
      }
      .other-search-bottom {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        box-sizing: border-box;
        align-items: flex-end;
        .slider-content {
          height: 34px;
        }
      }
    }
  }

  .advanced-search-show {
    .advanced-search {
      max-height: 400px;
      transition: max-height 0.5s;
    }
    .advanced-search-text {
      /deep/img {
        transform: rotate(0deg);
        transition: transform 0.2s;
      }
    }
  }
  /deep/.ivu-select-item-selected,
  .ivu-select-item-selected:hover {
    background-color: #fff !important;
    color: #2c86f8 !important;
  }
  .none-color {
    border-color: transparent !important;
  }
}
</style>
