<template>
  <!-- <mapDemo /> -->
  <div class="map-box">
    <ui-loading v-if="loading"></ui-loading>
    <div :id="mapId" class="map"></div>
    <Select
      v-model="cityCode"
      style="position: absolute; top: 20px; left: 20px; width: 200px"
      @on-change="cityChange"
    >
      <Option v-for="item in cityList" :value="item.code" :key="item.name">{{
        item.name
      }}</Option>
    </Select>
    <Select
      v-model="layerIndex"
      style="position: absolute; top: 20px; left: 230px; width: 200px"
      @on-change="layerChange"
    >
      <Option
        v-for="(item, index) in layerList"
        :value="index"
        :key="item.name"
        >{{ item.name }}</Option
      >
    </Select>
  </div>
</template>

<script>
import mapDemo from "@/components/map/index.vue";
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
import { getaoi } from "@/api/operationsOnTheMap.js";

let mapMain = null;
export default {
  components: {
    mapDemo,
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      globalObj: "systemParam/globalObj",
    }),
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      layerList: [
        {
          name: "购物服务",
          color: "green",
          data: [],
        },
        {
          name: "交通设施",
          color: "#7B68EE",
          data: [],
        },
        {
          name: "教育培训",
          color: "#FF69B4",
          data: [],
        },
        {
          name: "汽车服务",
          color: "red",
          data: [],
        },
        {
          name: "商圈地标",
          color: "yellow",
          data: [],
        },
        {
          name: "生活服务",
          color: "blue",
          data: [],
        },
        {
          name: "文化服务",
          color: "green",
          data: [],
        },
        {
          name: "行政区划",
          color: "#7B68EE",
          data: [],
        },
        {
          name: "休闲娱乐",
          color: "#FF69B4",
          data: [],
        },
        {
          name: "医疗保健",
          color: "red",
          data: [],
        },
        {
          name: "运动健身",
          color: "yellow",
          data: [],
        },
        {
          name: "政府机构",
          color: "blue",
          data: [],
        },
        {
          name: "住宿服务",
          color: "green",
          data: [],
        },
        {
          name: "自然地物",
          color: "#7B68EE",
          data: [],
        },
        {
          name: "金融设施",
          color: "red",
          data: [],
        },
        {
          name: "旅游景点",
          color: "blue",
          data: [],
        },
        {
          name: "出入口",
          color: "red",
          data: [],
        },
        {
          name: "丽人",
          color: "yellow",
          data: [],
        },
        {
          name: "绿地",
          color: "green",
          data: [],
        },
        {
          name: "美食",
          color: "#7B68EE",
          data: [],
        },
        {
          name: "门址",
          color: "#FF69B4",
          data: [],
        },
        {
          name: "其它",
          color: "green",
          data: [],
        },
        {
          name: "房产小区",
          color: "yellow",
          data: [],
        },
        {
          name: "公司企业",
          color: "blue",
          data: [],
        },
      ],
      layerIndex: 0,
      overlayLayerAoi: null,
      cityList: [
        {
          name: "江宁区",
          code: "320115",
          center: [118.845555491354, 31.9555651141709],
        },
        {
          name: "玄武区",
          code: "320102",
          center: [118.787008557066, 32.0527446945248],
        },
        {
          name: "秦淮区",
          code: "320104",
          center: [118.780889982418, 32.0358728101323],
        },
        {
          name: "建邺区",
          code: "320105",
          center: [118.727474709215, 32.0065688878542],
        },
        {
          name: "鼓楼区",
          code: "320106",
          center: [118.764519868404, 32.0690120701576],
        },
        {
          name: "浦口区",
          code: "320111",
          center: [118.620281039404, 32.0605988554829],
        },
        {
          name: "栖霞区",
          code: "320113",
          center: [118.803536456372, 32.1042509164671],
        },
        {
          name: "雨花台区",
          code: "320114",
          center: [118.766859383557, 31.9979777569052],
        },
        {
          name: "六合区",
          code: "320116",
          center: [118.845549165571, 32.3428633444127],
        },
        {
          name: "溧水区",
          code: "320117",
          center: [119.023416566211, 31.6549398226499],
        },
        {
          name: "高淳区",
          code: "320118",
          center: [118.870919169946, 31.3293210202585],
        },
        {
          name: "南京市",
          code: "320100",
          center: [118.762194492294, 32.043582862096],
        },
      ],
      cityCode: "320115",
      mapCenter: [118.845555491354, 31.9555651141709],
      params: {
        pageSize: 2500,
        pageNumber: 1,
        firstLevelName: "购物服务",
        adcode: "320115",
        citycode: "",
        name: "",
      },
      loading: false,
    };
  },
  async mounted() {
    await this.getMapConfig();
    let polygon = [
      [0, 0],
      [3, 2],
      [3, 3],
      [2, 3],
      [0, 0],
    ];
    // console.log("A", this.isPointInPolygon([-1, -1], polygon));
    // console.log("B", this.isPointInPolygon([2, 2], polygon));
    // console.log("C", this.isPointInPolygon([3, 2.5], polygon));
    // console.log("D", this.isPointInPolygon([1, 2], polygon));
  },
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain.init(mapId, data, style);
        this.configDefaultMap();
        this.getAoiData();
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    // 获取aoi数据
    getAoiData() {
      this.loading = true;
      getaoi(this.params)
        .then((res) => {
          this.layerList[this.layerIndex].data = res.data.entities.map(
            (item) => {
              return {
                properties: { ...item },
                geometry: JSON.parse(item.coord),
              };
            }
          );
          this.setPolygon(this.layerList[this.layerIndex]);
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    //绘制多边形
    setPolygon(aoiData) {
      mapMain.map.setCenter(
        new NPMapLib.Geometry.Point(this.mapCenter[0], this.mapCenter[1]),
        this.cityCode == "320100" ? 12 : 13
      );
      var mapGeometry = new MapPlatForm.Base.MapGeometry(mapMain.map);
      this.overlayLayerAoi = new NPMapLib.Layers.OverlayLayer("polygon", false);
      mapMain.map.addLayer(this.overlayLayerAoi);
      for (var i = 0; i < aoiData.data.length; i++) {
        var polygonItem = mapGeometry.getGeometryByGeoJson(
          aoiData.data[i].geometry,
          mapMain.map
        );
        polygonItem.setStyle({
          color: aoiData.color, //颜色
          fillColor: aoiData.color, //填充颜色
          weight: 2, //宽度，以像素为单位
          opacity: 1, //透明度，取值范围0 - 1
          fillOpacity: 0.5, //填充的透明度，取值范围0 - 1,
          lineStyle: NPMapLib.LINE_TYPE_DASH, //样式
        });
        polygonItem.setData({
          properties: aoiData.data[i].properties,
          center: aoiData.data[i].geometry.coordinates[0][0][0],
        });
        this.overlayLayerAoi.addOverlay(polygonItem);
        let that = this;
        polygonItem.addEventListener(
          NPMapLib.POLYGON_EVENT_CLICK,
          function (point) {
            mapMain.map.setCenter(
              new NPMapLib.Geometry.Point(
                point._data.center[0],
                point._data.center[1]
              ),
              16
            );
          }
        );
      }
    },
    //选择分类
    layerChange(val) {
      this.layerIndex = val;
      mapMain.map.removeOverlay(this.overlayLayerAoi);
      this.overlayLayerAoi.removeAllOverlays();
      this.overlayLayerAoi = null;
      this.layerList.forEach((item, index) => {
        if (index == val) {
          this.params.firstLevelName = item.name;
        }
      });
      this.getAoiData();
    },
    //选择城市
    cityChange(val) {
      this.cityCode = val;
      mapMain.map.removeOverlay(this.overlayLayerAoi);
      this.overlayLayerAoi.removeAllOverlays();
      this.overlayLayerAoi = null;
      this.cityList.forEach((item) => {
        if (item.code == val) {
          this.mapCenter = item.center;
        }
      });
      if (this.cityCode == "320100") {
        //南京市
        this.params.citycode = this.cityCode;
        this.params.adcode = "";
      } else {
        this.params.citycode = "";
        this.params.adcode = this.cityCode;
      }
      this.getAoiData();
    },
    // GIS算法—判断点在面内
    isPointInPolygon(point, polygon) {
      if (!point || point.length < 0) throw new Error("not a point");
      if (
        !polygon ||
        polygon.length < 4 ||
        polygon[0].join("") !== polygon[polygon.length - 1].join("")
      )
        throw new Error("not a polygon");

      const [x, y] = point;
      const xs = polygon.map(([x, y]) => x);
      const ys = polygon.map(([x, y]) => y);
      const xsSort = xs.sort((a, b) => a - b);
      const ysSort = ys.sort((a, b) => a - b);
      // 在四至内
      const inBBOX = () => {
        const [xmin, ymin, xmax, ymax] = [
          xsSort[0],
          ysSort[0],
          xsSort[xsSort.length - 1],
          ysSort[ysSort.length - 1],
        ];
        return x >= xmin && x <= xmax && y >= ymin && y <= ymax;
      };
      // 点在线上
      const onBorder = () => {
        let res = false;
        for (let i = 0; i < polygon.length - 2; i++) {
          const [xi, yi] = polygon[i];
          const [xj, yj] = polygon[i + 1];
          const k1 = (yj - yi) / (xj - xi);
          const k2 = (yj - y) / (xj - x);
          if (k1 === k2) {
            res = true;
            break;
          }
        }
        return res;
      };
      // 点在多边形内部
      const inPolygon = () => {
        let odd = false;
        for (let i = 0, j = polygon.length - 1; i < polygon.length; i++) {
          if (
            polygon[i][1] > y !== polygon[j][1] > y &&
            x <
              ((polygon[j][0] - polygon[i][0]) * (y - polygon[i][1])) /
                (polygon[j][1] - polygon[i][1]) +
                polygon[i][0]
          ) {
            odd = !odd;
          }
          j = i;
        }
        return odd;
      };

      if (!inBBOX()) return false;
      if (onBorder()) return true;
      return inPolygon();
    },
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  height: 100%;
  position: relative;
  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }
}
</style>
