<template>
  <div class="search">
    <Form
      ref="formData"
      :inline="true"
      :model="formData"
      :class="visible ? 'advanced-search-show' : ''"
    >
      <div class="general-search">
        <FormItem prop="plateNo">
          <Input
            placeholder="请输入车牌号码"
            v-model="formData.plateNo"
            clearable
            maxlength="50"
            class="search-input-430"
          >
            <!-- <Button slot="append" type="primary" icon="ios-search" @click="startSearch"></Button> -->
          </Input>
        </FormItem>
        <FormItem>
          <div class="advanced-search-text" @click.stop="advancedSearchHandle">
            <img src="@/assets/img/down-circle-icon.png" alt />
            <span class="primary">{{
              visible
                ? "普通检索"
                : searchText === "高级检索"
                ? "高级检索"
                : "更多条件"
            }}</span>
          </div>
        </FormItem>
        <div class="btn-group">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle('formData')">重置</Button>
        </div>
      </div>
      <div class="advanced-search" @click.stop="">
        <div class="advanced-search-item card-border-color">
          <FormItem label="最近抓拍设备:">
            <div class="select-tag-button" @click="selectDevice()">
              请选择设备{{
                formData.selectDeviceList &&
                formData.selectDeviceList.length > 0
                  ? `/已选(${formData.selectDeviceList.length})`
                  : ""
              }}
            </div>
          </FormItem>
          <FormItem
            label="最新抓拍时间:"
            prop="perceiveDate"
            class="percent-70"
          >
            <DatePicker
              :editable="false"
              v-model="formData.perceiveDate"
              transfer
              type="datetimerange"
              @on-change="dateChange"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择抓拍时间段"
              style="width: 350px"
            ></DatePicker>
          </FormItem>
        </div>
        <div class="advanced-search-item card-border-color">
          <FormItem label="车辆类型:" class="keyWidth">
            <selectTag
              :isChild="true"
              :carColor="true"
              :list="nonmotorVehicleType"
              ref="vehicleType"
              vModel="vehicleType"
              :oneLevel="formData.bodyTypeobj"
              :echo="formData.vehicleType"
              @selectItem="childSelectItem"
            />
            <div class="carVal"></div>
            <!-- <ui-tag-select
                ref="tagSelect3"
                @input="
                    e => {
                    input(e, 'vehicleType')
                    }
                "
                >
                <ui-tag-select-option v-for="(item, $index) in vehicleClassTypeList" :key="$index" :name="item.dataKey">
                    {{ item.dataValue }}
                </ui-tag-select-option>
                </ui-tag-select> -->
          </FormItem>
        </div>
        <div class="advanced-search-item card-border-color">
          <FormItem label="车身颜色:">
            <ui-tag-select
              ref="tagSelect4"
              :echo="formData.vehicleColor"
              @input="
                (e) => {
                  input(e, 'vehicleColor');
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in plateColorIpbdList"
                :key="$index"
                effect="dark"
                :name="item.dataKey"
              >
                <div
                  :style="{
                    borderColor: licenseBodyColors(item.dataKey).borderColor,
                  }"
                  class="plain-tag-color"
                >
                  <div
                    :style="licenseBodyColors(item.dataKey).style"
                    :class="licenseBodyColors(item.dataKey).class"
                  ></div>
                </div>
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
        </div>
      </div>
    </Form>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      @selectData="selectData"
      showOrganization
    />
    <!-- <select-device ref="selectDevice" :checkedLabels="checkedLabels" @selectData="selectData" /> -->
    <slot name="statistics"></slot>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray, vehicleBodyColorArray } from "@/libs/system";
import selectTag from "@/views/wisdom-cloud-search/cloud-default-page/components/select-tag.vue";
export default {
  components: {
    selectTag,
  },
  props: {
    searchText: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      visible: false,
      formData: {
        plateNo: "",
        selectDeviceList: [], // 设备资源
        perceiveDate: [], // 设备资源
        bodyTypeobj: {},
        labelType: "2",
        vehicleType: "",
        endSnapTime: "",
        startSnapTime: "",
        vehicleColor: "",
      },
      brand: [],
      licensePlateTypes: [
        { name: "大型汽车号牌", value: "1" },
        { name: "小型汽车号牌", value: "2" },
        { name: "使馆汽车号牌", value: "3" },
        { name: "领馆汽车号牌", value: "4" },
        { name: "境外汽车号牌", value: "5" },
        { name: "外籍汽车号牌", value: "6" },
        { name: "两三轮摩托车号牌", value: "7" },
        { name: "轻便摩托车号牌", value: "8" },
        { name: "使馆摩托车号牌", value: "9" },
        { name: "领馆摩托车号牌", value: "10" },
        { name: "外籍摩托车号牌", value: "11" },
      ],
      //   licensePlateColorList: [
      //     { name: '绿色', value: '1' },
      //     { name: '黄绿', value: '2' },
      //     { name: '蓝牌', value: '3' },
      //     { name: '黄牌', value: '4' },
      //     { name: '白牌', value: '5' },
      //     { name: '黑牌', value: '6' },
      //     { name: '渐绿牌', value: '7' },
      //     { name: '其他', value: '8' }
      //   ],
      licenseBodyTypes: [
        { name: "轿车", value: "1" },
        { name: "面包车", value: "2" },
        { name: "皮卡", value: "3" },
        { name: "商务车", value: "4" },
        { name: "越野车", value: "5" },
        { name: "中型普通客车", value: "6" },
        { name: "大型普通客车", value: "7" },
        { name: "小型普通客车", value: "8" },
        { name: "中型普通货车", value: "9" },
        { name: "重型普通货车", value: "10" },
        { name: "轻型普通货车", value: "11" },
        { name: "微型普通货车", value: "12" },
        { name: "公交车", value: "13" },
        { name: "校车", value: "14" },
        { name: "大型货车", value: "15" },
        { name: "三轮车", value: "16" },
      ],
      licenseBodyColorList: [
        { name: "黑", value: "1" },
        { name: "白", value: "2" },
        { name: "灰", value: "3" },
        { name: "红", value: "4" },
        { name: "蓝", value: "5" },
        { name: "黄", value: "6" },
        { name: "橙", value: "7" },
        { name: "褐", value: "8" },
      ],
      vehicleBodyColors: [],
      specialVehicles: [
        { name: "危化品", value: "1" },
        { name: "黄标车", value: "2" },
        { name: "渣土车", value: "3" },
        { name: "邮政车", value: "4" },
        { name: "环卫、园林车、市政工程", value: "5" },
        { name: "救护车", value: "6" },
        { name: "工程救险车", value: "7" },
        { name: "警车", value: "8" },
        { name: "安保车 ", value: "9" },
        { name: "消防车", value: "10" },
        { name: "其他车辆", value: "11" },
      ],
    };
  },
  computed: {
    ...mapGetters({
      vehicleColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
      nonmotorVehicleType: "dictionary/getNonmotorVehicleType", // 车辆类型、车身类型(枚举精准检索)
      plateColorIpbdList: "dictionary/getBodyColorList", // 车身颜色
      specialVehicleList: "dictionary/getSpecialVehicleList", // 特殊车辆
    }),
  },
  activated() {
    // 点击遮罩层，不点击window
    let dom = document.querySelectorAll(".vertical-center-modal");
    dom.forEach((item) => {
      item.addEventListener("click", (e) => {
        e.stopPropagation();
      });
    });
    let _that = this;
    document.addEventListener("click", function (e) {
      _that.visible = false;
    });
    document
      .querySelector(".ivu-picker-panel-body")
      .addEventListener("click", function (e) {
        e.stopPropagation();
      });
  },
  deactivated() {
    let dom = document.querySelectorAll(".vertical-center-modal");
    dom.forEach((item) => {
      item.removeEventListener("click", (e) => {
        e.stopPropagation();
      });
    });
    document
      .querySelector(".ivu-picker-panel-body")
      .removeEventListener("click", function (e) {
        e.stopPropagation();
      });
  },
  created() {},
  methods: {
    licensePlateColors(dataKey) {
      return licensePlateColorArray[dataKey] || licensePlateColorArray["99"];
    },
    licenseBodyColors(dataKey) {
      return vehicleBodyColorArray[dataKey] || vehicleBodyColorArray["99"];
    },
    advancedSearchHandle() {
      if (this.visible) {
        this.visible = false;
      } else {
        this.visible = true;
      }
    },
    // 选择品牌
    selectBrandHandle() {
      this.$refs.brandModal.show();
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.formData.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.formData.selectDeviceList = list;
      var ids = [];
      if (list.length > 0) {
        list.forEach((item) => {
          ids.push(item.deviceId);
        });
      }
      this.formData.deviceIds = ids;
    },
    // 选择标签
    selectLabelHandle() {
      this.$refs.labelModal.init(this.formData.labelIds);
    },
    // 已选标签
    setCheckedLabel(val) {
      this.formData.labelIds = val;
    },
    checkBrand(val) {
      this.formData.vehicleBrand = val;
    },
    // 查询
    startSearch() {
      this.visible = false;
      let startSnapTime = "",
        endSnapTime = "";
      if (
        this.formData.perceiveDate &&
        this.formData.perceiveDate.length > 0 &&
        this.formData.perceiveDate[0] != ""
      ) {
        startSnapTime = this.$dayjs(this.formData.perceiveDate[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        endSnapTime = this.$dayjs(this.formData.perceiveDate[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      //   delete this.formData.perceiveDate
      this.$emit("searchForm", {
        ...this.formData,
        startSnapTime,
        endSnapTime,
      });
    },
    dateChange(start, end) {
      if (start[1].slice(-8) === "00:00:00") {
        start[1] = start[1].slice(0, -8) + "23:59:59";
      }
      this.formData.perceiveDate = [start[0], start[1]];
    },
    setData(data) {
      // console.log('一车一档回显查询参数：', data)
      if (data) {
        if (data.plateNo) this.formData.plateNo = data.plateNo;
        if (data.plateClass) this.formData.plateClass = data.plateClass;
        if (data.plateColor) this.formData.plateColor = data.plateColor;
        if (data.vehicleType) this.formData.vehicleType = data.vehicleType;
        if (data.vehicleColor) this.formData.vehicleColor = data.vehicleColor;
        if (data.specialCar) this.formData.specialCar = data.specialCar;
        if (data.bodyTypeobj) this.formData.bodyTypeobj = data.bodyTypeobj;
        if (data.vehicleBrand) this.formData.vehicleBrand = data.vehicleBrand;
        if (data.selectDeviceList)
          this.formData.selectDeviceList = data.selectDeviceList;
        if (data.labelIds) this.formData.labelIds = data.labelIds;
        if (data.startSnapTime) {
          this.formData.perceiveDate = [data.startSnapTime, data.endSnapTime];
        }
        // if(data.specialCar) this.formData.specialCar = data.specialCar
      }
      this.$forceUpdate();
      this.startSearch();
    },
    // 重置
    resetHandle(formData) {
      this.$refs[formData].resetFields();
      this.$refs.tagSelect4.clearChecked(); //车身颜色
      this.$refs.vehicleType.clearChecked(); //车身类型
      this.formData = {
        plateNo: "",
        selectDeviceList: [],
        perceiveDate: [],
        bodyTypeobj: {},
        labelType: "2",
        vehicleType: "",
        endSnapTime: "",
        startSnapTime: "",
        deviceIds: [],
        vehicleColor: "",
      };
      this.startSearch();
    },
    input(e, type) {
      this.formData[type] = e;
    },
    /**
     * 选中tag赋值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.formData[key] = item.dataKey;
      } else {
        // 全部选项，不返回数据到后端
        this.formData[key] = null;
      }
    },

    /**
     * 选中tag赋值
     * oneLevelObj：有子集时一级对象
     */
    childSelectItem(key, item, oneLevelObj) {
      // 具体业务处理逻辑
      if (item) {
        this.formData[key] = item.dataKey;
        this.formData.bodyTypeobj = oneLevelObj;
      } else {
        // 全部选项，不返回数据到后端
        this.formData[key] = null;
        this.formData.bodyTypeobj = {};
      }
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  position: relative;
  .general-search {
    .advanced-search-text {
      height: 34px;
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
  .advanced-search {
    display: flex;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    margin-top: 1px;
    z-index: 10;
    max-height: 0px;
    transition: max-height 0.3s;
    overflow: auto;
    flex-direction: column;
    .advanced-search-item {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px dashed #fff;
      align-items: center;
      .ivu-form-item {
        &.percent-70 {
          width: 68%;
        }
        display: flex;
        margin: 0;
        .text-radio-group {
          margin-left: -10px;
        }
      }
      /deep/ .ivu-form-item-label {
        white-space: nowrap;
        width: 105px;
        // text-align-last: justify;
        // text-align: justify;
        // text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
      }
      /deep/ .ivu-form-item-content {
        display: flex;
        align-items: center;
      }
    }
  }
  .advanced-search-show {
    .advanced-search {
      max-height: 540px;
      transition: max-height 0.5s;
    }
    .advanced-search-text {
      /deep/img {
        transform: rotate(0deg);
        transition: transform 0.2s;
      }
    }
  }
}

.btn-group {
  position: absolute;
  right: 0;
  top: 0;
}

.langStl {
  /deep/ .ivu-form-item-content {
    // margin-left: 30px;
  }
}

.keyWidth {
  /deep/ .ivu-form-item-label {
    // width: 238px !important;
  }
  width: 100%;
  /deep/ .ivu-form-item-content {
    width: calc(~"100% - 105px");
  }
}
</style>
