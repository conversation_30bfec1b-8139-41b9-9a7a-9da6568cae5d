<template>
  <div class="search">
    <div class="inline">
      <ui-label label="组织机构" :width="70" class="inline mr-lg mb-sm mr-md">
        <api-organization-tree
          :treeData="treeData"
          :taskObj="taskObj"
          :select-tree="formValidate"
          @selectedTree="selectedTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label :label="global.filedEnum.deviceName" :width="70" class="inline mb-sm mr-lg">
        <Input
          v-model="formValidate.deviceName"
          class="width-md"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
        ></Input>
      </ui-label>
      <ui-label :label="global.filedEnum.deviceId" :width="70" class="inline mb-sm mr-lg">
        <Input
          v-model="formValidate.deviceId"
          class="width-md"
          :placeholder="`请输入${global.filedEnum.deviceId}`"
        ></Input>
      </ui-label>
      <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="inline mb-sm mr-lg">
        <Select
          clearable
          class="width-md"
          v-model="formValidate.sbdwlx"
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <!-- <ui-label
        label="在线状态"
        :width="70"
        class="inline"
        v-if="currentTree.id == 203 || currentTree.id == 202"
      >
        <Select
          clearable
          class="width-md"
          v-model="formValidate.outcome"
          :max-tag-count="1"
          placeholder="请选择在线状态"
        >
          <Option value="">全部</Option>
          <Option value="1">在线</Option>
          <Option value="2">离线</Option>
          <Option value="3">无法检测</Option>
        </Select>
      </ui-label> -->
      <template v-if="currentTree.id == 202 || currentTree.id == 203 || currentTree.id == 204 || currentTree.id == 205">
        <ui-label label="在线状态" :width="70" class="inline mb-sm mr-lg">
          <Select
            v-model="formValidate.online"
            v-if="currentTree.id == 202 || currentTree.id == 203"
            clearable
            placeholder="请选择在线状态"
            class="width-md"
          >
            <Option :value="1">在线</Option>
            <Option :value="2">离线</Option>
          </Select>
          <Select
            v-model="formValidate.online"
            v-if="currentTree.id == 204 || currentTree.id == 205"
            clearable
            placeholder="请选择在线状态"
            class="width-md"
          >
            <Option :value="1">文件存在</Option>
            <Option :value="2">文件不存在</Option>
          </Select>
        </ui-label>
        <ui-label label="完好状态" :width="70" class="inline mr-lg">
          <Select v-model="formValidate.normal" clearable placeholder="请选择完好状态" class="width-md">
            <Option :value="1">取流及时响应</Option>
            <Option :value="2">取流超时响应</Option>
          </Select>
        </ui-label>
        <ui-label label="可用状态" :width="70" class="inline mr-lg">
          <Select v-model="formValidate.canPlay" clearable placeholder="请选择可用状态" class="width-md">
            <Option :value="1">取流成功</Option>
            <Option :value="2">取流失败</Option>
          </Select>
        </ui-label>
      </template>
      <!-- <ui-label label="设备类型" :width="70" class="inline"
        v-if="currentTree.id == 207 || currentTree.id == 208 ||
              currentTree.id == 205 || currentTree.id == 206">
        <Select class="width-sm" v-model="formValidate.isImportantDevice" clearable placeholder="请选择设备类型">
          <Option :value="1" label="普通标签"></Option>
          <Option :value="2" label="重点标签"></Option>
        </Select>
      </ui-label> -->
      <ui-label label="调阅结果" :width="70" class="inline mr-lg" v-if="currentTree.id == 205 || currentTree.id == 204">
        <Select class="width-sm" v-model="formValidate.outcome" clearable placeholder="请选择检测结果">
          <Option :value="1" label="正常"></Option>
          <Option :value="2" label="异常"></Option>
          <Option :value="3" label="无法调阅"></Option>
        </Select>
      </ui-label>
      <ui-label
        label="检测结果"
        :width="70"
        class="inline mr-lg"
        v-if="
          currentTree.id === 207 ||
          currentTree.id === 206 ||
          currentTree.id === 209 ||
          currentTree.id === 208 ||
          currentTree.id === 210 ||
          currentTree.id === 212
        "
      >
        <Select class="width-sm" v-model="formValidate.outcome" clearable placeholder="请选择检测结果">
          <Option :value="1" label="正常"></Option>
          <Option :value="2" label="异常"></Option>
          <Option :value="3" label="无法检测"></Option>
        </Select>
      </ui-label>
      <ui-label label="异常类型" v-if="currentTree.id === 212" :width="70" class="inline mr-lg">
        <Select v-model="formValidate.exceptionType" clearable placeholder="请选择可用状态" class="width-md">
          <Option :value="1">视频信号</Option>
          <Option :value="2">视频亮度</Option>
          <Option :value="3">视频偏色</Option>
          <Option :value="4">视频清晰</Option>
          <Option :value="5">视频遮挡</Option>
        </Select>
      </ui-label>
      <!-- <ui-label label="录像结果" :width="70" class="inline"
        v-if="currentTree.id == 203 || currentTree.id == 202">
        <Select class="width-sm" v-model="formValidate.outcome" clearable placeholder="请选择检测结果">
          <Option :value="1" label="正常"></Option>
          <Option :value="2" label="异常"></Option>
          <Option :value="3" label="无法检测"></Option>
        </Select>
      </ui-label> -->
    </div>
    <ui-label :width="0" class="inline mb-sm search-button" label="">
      <Button type="primary" @click="search">查询</Button>
      <Button class="ml-sm" @click="resetForm">重置</Button>
    </ui-label>
  </div>
</template>

<script>
import user from '@/config/api/user';
export default {
  name: 'search',
  components: {
    // UiTable: require('@/components/ui-table.vue').default,
    ApiOrganizationTree: require('../components/api-tree.vue').default,
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    treeData: {
      type: Array,
    },
    taskObj: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      dictData: {},
      formValidate: {
        orgCode: '',
        deviceName: '',
        deviceId: '',
        sbdwlx: '',
        outcome: '', // 录像结果
        isImportantDevice: '',
        online: '',
        normal: '',
        canPlay: '',
      },
    };
  },
  mounted() {
    this.getDictData();
  },
  methods: {
    search() {
      this.$emit('searchFn', this.formValidate);
    },
    resetForm() {
      // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      this.formValidate = {
        orgCode: '',
      };
      this.search();
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectedTree(data) {
      this.formValidate.orgCode = data.orgCode;
      this.$emit('selectedTree', this.formValidate);
    },
  },
  computed: {},
  watch: {
    taskObj: {
      handler() {
        this.$nextTick(() => {
          // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled && !!item.orgCode) || {}
          // this.formValidate = {
          //   orgCode: orgCode || ''
          // }
        });
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  margin: 10px 0;
  display: flex;
  justify-content: space-between;
  .search-button {
    white-space: nowrap;
  }
}
/deep/ .width-md,
.width-sm {
  width: 150px !important;
}
</style>
