<template>
  <div class="ui-table">
    <ViewTable
      :class="{
        'no-scroll': loading,
        'no-shadow': tableData && tableData.length === 0,
        'full-border': fullBorder,
      }"
      :columns="tableColumns"
      :height="fullHeight"
      :max-height="maxHeight"
      :data="tableData"
      :row-class-name="rowClassName"
      :size="size"
      :loading="loading"
      :border="border"
      :row-key="rowKey"
      :span-method="spanMethod"
      :indent-size="indentSize"
      :highlight-row="false"
      :disabled-hover="disabledHover"
      :stripe="stripe"
      :no-data-text="!loading && !specialJsx ? emptyJSX : specialJsx"
      :show-summary="showSummary"
      :show-header="showHeader"
      :sum-text="sumText"
      :summary-method="summaryMethod"
      ref="table"
      @on-select="oneSelected"
      @on-selection-change="selected"
      @on-select-cancel="cancelSelected"
      @on-select-all="onSelectAll"
      @on-select-all-cancel="cancelAllSelected"
      @on-row-click="rowClick"
      @on-row-dblclick="rowDbClick"
      @on-expand="onExpand"
      @on-sort-change="onSortChange"
    >
      <template v-for="item in slotList" #[item.slot]="{ row, column, index }">
        <slot :name="item.slot" :column="column" :row="row" :index="index"></slot>
      </template>
      <template v-slot:loading>
        <loading v-if="loading"></loading>
      </template>
    </ViewTable>
  </div>
</template>
<style lang="less" scoped>
.full-border {
  @{_deep}.ivu-table {
    &:before {
      width: 0;
    }

    .ivu-table-header {
      tr {
        th {
          border-right: none !important;
        }
      }
    }
  }

  @{_deep} .ivu-table {
    th,
    td {
      border: 1px solid var(--border-table) !important;
    }

    &:before {
      content: '';
      position: absolute;
      background-color: #0d477d !important;
    }

    .ivu-table-summary {
      td {
        background: #062042;
      }
    }
  }
}
.ui-table {
  overflow: hidden;
  @{_deep}.ivu-table-tip {
    overflow-x: auto;
    position: relative;
  }
  @{_deep}.ivu-table-overflowX {
    & ~ .ivu-table-fixed-right,
    & ~ .ivu-table-fixed {
      height: calc(~'100% - 8px');
      .ivu-table-fixed-body {
        padding-bottom: 8px;
      }
    }
    & ~ .ivu-table-fixed-shadow {
      overflow: hidden;
    }
  }
  @{_deep}.ivu-table-with-summary {
    .ivu-table {
      overflow: auto;
      > div {
        overflow: initial !important;
      }
    }
  }
}

.no-shadow {
  @{_deep}.ivu-table-fixed {
    box-shadow: none !important;
  }
  @{_deep}.ivu-table-fixed-right {
    box-shadow: none !important;
  }
}
</style>
<script>
import Vue from 'vue';
import { mapGetters, mapActions } from 'vuex';
import { emptyJSX } from '@/util/module/common';
import ViewTable from '@/components/view-design/components/table';
import UiTableTips from '@/components/ui-table-tips.vue';
export default {
  data() {
    return {
      fullHeight: null,
      offsetTop: '', //记录table的初始offsetTop值
      slotList: [],
      emptyJSX: Object.freeze(emptyJSX),
      unwatch: null,
      storeSelectTable: [], // 多选存储
      hasScroll: false,
      onlyOneFlag: false,
    };
  },
  created() {
    this.assemblyContent(this.tableColumns);
  },
  mounted() {
    if (this.minusHeight) {
      if (this.maxHeight) return;
      this.offsetTop = this.$refs.table.$el.offsetTop;
      this.fullHeight =
        window.innerHeight -
        this.offsetTop -
        (this.minusHeight * parseFloat(document.documentElement.style.fontSize)) / 192;
    } else {
      if (this.nofill) return;
      this.$refs.table.observer.listenTo(this.$el, this.resize);
    }
  },
  updated() {
    // 控制显示隐藏
    let tipsDom = document.getElementsByClassName('tips-box')[0];
    tipsDom ? (tipsDom.style.display = 'none') : null;
    setTimeout(() => {
      this.handleTips();
    }, 0);
  },
  methods: {
    ...mapActions({
      setTipsShow: 'common/setTipsShow',
    }),
    // 手动关闭温馨提示
    xclose() {
      this.setTipsShow(false);
      let tipsDom = document.getElementsByClassName('tips-box')[0];
      tipsDom.style.display = 'none';
    },
    handleTips() {
      // 有ivu-table-overflowX属性才添加tips
      this.findCurrentOverflowX();
      this.hasScroll = this.$refs.table?.$refs?.body?.classList.contains('ivu-table-overflowX');
      // 如果默认关闭就不执行
      if (!this.hasScroll || !this.tipsShow) return;
      // 添加tips插入搭配table固定栏右侧
      let tipsDom = document.getElementsByClassName('tips-box')[0];
      !tipsDom ? (tipsDom = this.addTips()) : this.controlDisplay();
    },
    findCurrentOverflowX() {
      // 如果当前document有好几个table组件，找到页面上真正展示的
      let uiTableEles = Array.from(document.getElementsByClassName('ui-table'));
      if (!uiTableEles.length) return;
      let realTable = document.getElementsByClassName('ui-table')[0];
      uiTableEles.forEach((item) => {
        if (item.getBoundingClientRect().width) {
          realTable = item;
        }
      });
      let hasScroll = realTable.getElementsByClassName('ivu-table-overflowX')[0];
      this.hasScroll = !!hasScroll;
    },
    addTips() {
      // 有右侧操作固定栏 - 放到操作栏的前面
      let fixDom = this.$refs.table?.$refs?.fixedRightBody;
      let fixDomContentDom = this.$refs.table?.$refs?.fixedRightBody?.parentElement.parentElement;
      let fixRightContentDom = this.$refs.table?.$refs?.fixedRightBody?.parentElement;
      // 没有固定操作栏 -  放到最右边
      let scrollXContentDom = this.$refs.table?.$refs?.body;
      // 没有操作栏，又没有横向滚动条不执行
      let contentDom = null;
      fixDom ? (contentDom = fixDomContentDom) : (contentDom = scrollXContentDom);
      let Profile = Vue.extend(UiTableTips);
      contentDom.appendChild(
        new Profile({
          propsData: {
            xclose: this.xclose,
          },
        }).$mount().$el,
      );
      let tipsDom = document.getElementsByClassName('tips-box')[0];
      tipsDom.style.right = fixDom ? `${(fixRightContentDom.clientWidth + 15) / 192}rem` : `${15 / 192}rem`;
      this.controlDisplay();
    },
    controlDisplay() {
      // 没有数据隐藏
      let tipsDom = document.getElementsByClassName('tips-box')[0];
      this.tableData.length ? (tipsDom.style.display = 'block') : (tipsDom.style.display = 'none');
      // 控制tips显示5s就隐藏
      setTimeout(() => {
        tipsDom.style.display = 'none';
      }, 10000);
    },
    selected(selection) {
      this.$emit('selectTable', selection);
    },
    cancelSelected(selection, row) {
      this.$emit('cancelSelectTable', selection, row);
      if (!this.reserveSelection) return;
      let hasIndex = this.storeSelectTable.findIndex((item) => item[this.rowKey] === row[this.rowKey]);
      this.$set(row, '_checked', false);
      this.storeSelectTable.splice(hasIndex, 1);
      this.$emit('storeSelectList', this.storeSelectTable);
    },
    cancelAllSelected(selection) {
      this.$emit('cacelAllSelectTable', selection);
      if (!this.reserveSelection) return;
      let tableRowKeys = [];
      this.tableData.forEach((item) => {
        this.$set(item, '_checked', false);
        tableRowKeys.push(item[this.rowKey]);
      });
      this.storeSelectTable = this.storeSelectTable.filter((item) => {
        return !tableRowKeys.includes(item[this.rowKey]);
      });
      this.$emit('storeSelectList', this.storeSelectTable);
    },
    onSelectAll(selection) {
      this.$emit('onSelectAllTable', selection);
      if (!this.reserveSelection) return;
      let storeSelectTableKeys = this.storeSelectTable.map((item) => {
        this.$set(item, '_checked', true);
        return item[this.rowKey];
      });
      selection.forEach((item) => {
        if (!storeSelectTableKeys.includes(item[this.rowKey])) {
          this.storeSelectTable.push(item);
        }
      });
      this.$emit('storeSelectList', this.storeSelectTable);
    },
    oneSelected(selection, row) {
      this.$emit('oneSelected', selection, row);

      if (!this.reserveSelection) return;
      this.$set(row, '_checked', true);
      this.storeSelectTable.push(row);
      this.$emit('storeSelectList', this.storeSelectTable);
    },
    rowClick(data, index) {
      this.$emit('rowClick', data, index);
    },
    rowDbClick(data, index) {
      this.$emit('rowDbClick', data, index);
    },
    onExpand(selection, row) {
      this.$emit('onExpand', selection, row);
    },
    onSortChange({ column, key, order }) {
      this.$emit('onSortChange', { column, key, order });
    },
    resize(el) {
      // 如果为有总价则高度要减去总价高度
      if (this.maxHeight) return;
      this.fullHeight = this.showSummary
        ? el.offsetHeight - (48 * parseFloat(document.documentElement.style.fontSize)) / 192
        : el.offsetHeight;
    },
    assemblyContent(list) {
      list.forEach((row) => {
        if (row.slot) {
          this.slotList.push(row);
        }
        if (row.children) {
          this.assemblyContent(row.children);
        }
      });
    },
    // 全选true ,取消全选false
    selectAll(status) {
      this.$refs.table.selectAll(status);
    },
  },
  watch: {
    minusHeight(val) {
      if (this.maxHeight) return;
      this.fullHeight =
        window.innerHeight - this.offsetTop - (val * parseFloat(document.documentElement.style.fontSize)) / 192;
    },
    tableData(val) {
      /**
       * 只有第一次执行添加tips
       * (为了保证第一次表格加载完成后再执行添加tips)
       */
      setTimeout(() => {
        if (this.onlyOneFlag || !val.length) return;
        this.handleTips();
        this.onlyOneFlag = true;
      }, 0);
      // 滚动底部切换自动置顶
      if (this.tableScroll) {
        setTimeout(() => {
          try {
            this.$refs.table.$el.children[0].children[1].scroll(0, 0);
          } catch (err) {
            console.log(err);
          }
        }, 500);
      }

      if (!this.reserveSelection) return;
      if (this.isAll) {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', true);
          this.$set(item, '_disabled', true);
        });
        return;
      }
      let tableObject = {};
      this.tableData.forEach((item) => {
        tableObject[item[this.rowKey]] = item;
        this.$set(item, '_checked', false);
      });
      this.storeSelectTable.forEach((item) => {
        if (item[this.rowKey] in tableObject) {
          this.$set(tableObject[item[this.rowKey]], '_checked', true);
        }
      });
    },
    loading(val) {
      if (val) {
        let tipsDom = document.getElementsByClassName('tips-box')[0];
        tipsDom ? (tipsDom.style.display = 'none') : null;
      } else {
        // 解决列表左右两侧固定列，重新获取数据重置tableData为空时，scrollTop没有和bodyscrollTop保持统一的问题 导致列表错乱
        this.$nextTick(() => {
          !!this.$refs.table.$refs.fixedBody &&
            (this.$refs.table.$refs.fixedBody.scrollTop = this.$refs.table.$refs.body.scrollTop);
          !!this.$refs.table.$refs.fixedRightBody &&
            (this.$refs.table.$refs.fixedRightBody.scrollTop = this.$refs.table.$refs.body.scrollTop);
        });
      }
    },
    // val定值范围数组 || Blooen(控制全选)
    defaultStoreData(val) {
      if (val === this.storeSelectTable) return;
      this.storeSelectTable = this.$util.common.deepCopy(val);
      // 全选全部选中
      if (this.isAll) {
        return;
      }
      // 没有默认存储
      if (!val.length) {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', false);
        });
        return;
      }
      // 有存储选中
      let tableObject = {};
      this.tableData.forEach((item) => {
        tableObject[item[this.rowKey]] = item;
        this.$set(item, '_checked', false);
      });
      this.storeSelectTable.forEach((item) => {
        if (item[this.rowKey] in tableObject) {
          this.$set(tableObject[item[this.rowKey]], '_checked', true);
        }
      });
    },
    tableColumns: {
      handler(val) {
        this.assemblyContent(val);
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      innerHeight: 'common/getInnerHeight',
      tipsShow: 'common/getTipsShow',
    }),
  },
  props: {
    tableScroll: {
      // 是否启用数据改变，表格自动滚动致顶部
      type: Boolean,
      default: true,
    },
    tableColumns: {
      type: Array,
      required: true,
    },
    tableData: {
      required: true,
    },
    border: {
      default: false,
    },
    loading: {
      default: false,
    },
    maxHeight: {
      type: [Number , String],
      default: '',
    },
    // 到底部需要减去的高度
    minusHeight: {},
    nofill: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      default: 'id',
    },
    rowClassName: {
      type: Function,
      default() {
        return '';
      },
    },
    size: {},
    indentSize: {
      type: Number,
      default: 0,
    },
    stripe: {
      type: Boolean,
      default: true,
    },
    disabledHover: {
      type: Boolean,
      default: false,
    },
    spanMethod: {
      type: Function,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    showSummary: {
      type: Boolean,
      default: false,
    },
    sumText: {
      type: String,
      default: '合计',
    },
    summaryMethod: {
      type: Function,
    },
    specialJsx: {
      type: String,
      default: '',
    },
    // 数据更新之后保留之前选中的数据（需指定 row-key）
    reserveSelection: {
      type: Boolean,
      default: false,
    },
    defaultStoreData: {
      // 默认传入store的值
      type: Array,
      default: () => [],
    },
    // 全选，配合defaultStoreData运用
    isAll: {
      type: Boolean,
      default: false,
    },
    /**所有单元格增加边框 参考考核成绩
     * governanceevaluation/examscore/examinationscore
     */
    fullBorder: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    ViewTable,
  },
  beforeDestroy() {
    this.$refs.table.observer.removeListener(this.$el, this.resize);
  },
};
</script>
