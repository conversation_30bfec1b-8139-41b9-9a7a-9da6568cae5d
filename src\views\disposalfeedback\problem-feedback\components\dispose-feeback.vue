<template>
  <ui-modal v-model="visible" title="问题反馈处理" width="738px" @onCancel="onCancel">
    <Form ref="feedbackForm" :model="feedbackForm" :label-width="100" :rules="formRules">
      <FormItem prop="handleResult" label="处理结果" :required="true" class="block">
        <RadioGroup v-model="feedbackForm.handleResult">
          <Radio label="1">已处理</Radio>
          <Radio label="2">不处理</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem prop="handleDetail" label="处理说明" class="block">
        <Input type="textarea" class="long-input" v-model="feedbackForm.handleDetail" placeholder="请输入处理说明">
        </Input>
      </FormItem>
      <FormItem prop="handleImages" label="上传图片" class="block">
        <upload-img @successPut="successPut" ref="upload"></upload-img>
        <p class="upload-desc mt-sm" v-if="!feedbackForm.handleImages?.length">最多上传3张，每张不大于2M</p>
      </FormItem>
    </Form>
    <template #footer>
      <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
      <Button class="plr-30" type="primary" :loading="loading" @click="certainDispose">确定</Button>
    </template>
  </ui-modal>
</template>
<script>
import feedbackApi from '@/config/api/feedback';
export default {
  components: { UploadImg: require('@/components/upload-img.vue').default },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    tableRowObj: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      feedbackForm: {
        handleResult: '1',
        handleDetail: '',
        handleImages: [],
      },
      formRules: {
        handleDetail: [{ required: true, message: '请输入处理说明', trigger: 'blur' }],
      },
      radioList: [
        { value: '1', label: '已处理' },
        { value: '2', label: '不处理' },
      ],
    };
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          this.feedbackForm = {
            handleResult: '1',
            handleDetail: '',
            handleImages: [],
          };
          this.$refs.upload?.clear();
        }
        this.visible = val;
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('changeModalVisible', val);
    },
  },
  methods: {
    //图片添加完成
    successPut(successData) {
      this.feedbackForm.handleImages = successData;
    },
    //关闭
    onCancel() {},
    //点击取消
    cancel() {
      this.visible = false;
    },
    //点击确定
    certainDispose() {
      this.$refs.feedbackForm.validate(async (valid) => {
        if (valid) {
          try {
            const params = {
              id: this.tableRowObj.id,
              ...this.feedbackForm,
            };
            this.loading = true;
            let { data } = await this.$http.post(feedbackApi.handleFeedback, params);
            if (data.code == 200) {
              this.$Message.success(data.msg);
            } else {
              this.$Message.error(data.msg);
            }
            this.visible = false;
            this.$emit('update');
          } catch (error) {
            console.log(error);
          } finally {
            this.loading = false;
          }
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20px 0 40px 50px;
}
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
@{_deep} .ivu-form-item-label:before {
  margin-right: 0;
}
@{_deep} .upload-img {
  justify-content: flex-start;
}
@{_deep} .upload-item {
  width: 100px;
  height: 100px;
}
.mb-sm {
  margin-bottom: 10px !important;
}
.long-input {
  width: 565px;
  max-width: calc(100% - 20px);
}
.upload-desc {
  color: var(--color-warning);
  font-size: 12px;
  line-height: 18px;
}
</style>
