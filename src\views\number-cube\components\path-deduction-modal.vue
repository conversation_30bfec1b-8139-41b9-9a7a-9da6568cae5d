<template>
  <ui-modal
    v-model="visible"
    title="路径推演"
    :r-width="450"
    @onOk="comfirmHandle"
  >
    <Form ref="formData" :model="formData" :rules="rules" :label-width="100">
      <FormItem label="分析方式:" prop="type">
        <RadioGroup v-model="formData.type">
          <Radio :label="1">全部路径</Radio>
          <Radio :label="2">最短路径</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="最大深度:" prop="maxDepth" v-if="formData.type === 1">
        <Select
          v-model="formData.maxDepth"
          placeholder="请选择最大深度"
          class="w250"
        >
          <Option
            v-for="item in maxDepthList"
            :key="item.label"
            :value="item.label"
            >{{ item.labelCn }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="实体类型:" prop="vertexLabel">
        <Select
          v-model="formData.vertexLabel"
          placeholder="请选择实体类型"
          class="w250"
          clearable
        >
          <Option
            v-for="(item, $index) in pathShowObj.entityList"
            :key="$index"
            :value="item.label"
            >{{ item.labelCn }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="实体关系:" prop="edgeLabel">
        <Select
          v-model="formData.edgeLabel"
          clearable
          placeholder="请选择实体关系"
          class="w250"
        >
          <Option
            v-for="(item, $index) in pathShowObj.relationList"
            :key="$index"
            :value="item.label"
            >{{ item.labelCn }}</Option
          >
        </Select>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  props: {
    pathShowObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      visible: false,
      formData: {
        type: 1,
        maxDepth: 1,
        vertexLabel: "",
        edgeLabel: "",
      },
      maxDepthList: [
        {
          label: 1,
          labelCn: "一度",
        },
        {
          label: 2,
          labelCn: "二度",
        },
        {
          label: 3,
          labelCn: "三度",
        },
        {
          label: 4,
          labelCn: "四度",
        },
        {
          label: 5,
          labelCn: "五度",
        },
        {
          label: 6,
          labelCn: "六度",
        },
      ],
      rules: {
        vertexLabel: [{ message: "必选项", trigger: "change" }],
        edgeLabel: [{ message: "必选项", trigger: "change" }],
      },
      entityTypeList: [
        { dataKey: "1", dataValue: "人" },
        { dataKey: "2", dataValue: "车" },
      ],
      entityRelationshipList: [
        { dataKey: "1", dataValue: "同行" },
        { dataKey: "2", dataValue: "同火车" },
      ],
    };
  },
  methods: {
    init() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.formData.resetFields();
      });
    },
    comfirmHandle() {
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          this.visible = false;
          this.$emit("pathShowBack", {
            ...this.formData,
            maxDepth:
              this.formData.type === 1
                ? this.formData.maxDepth
                : this.maxDepthList.slice(-1)[0]?.label,
          });
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.w250 {
  width: 250px;
}
/deep/ .ivu-modal-body {
  padding-left: 38px !important;
}
/deep/ .ivu-form-item {
  margin-bottom: 30px;
}
/deep/ .ivu-radio-wrapper {
  margin-right: 30px;
}
</style>
