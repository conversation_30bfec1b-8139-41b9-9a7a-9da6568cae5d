import { mapGetters } from "vuex";

export default {
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  methods: {
    uploadFileFunc(fileType, filePath, ip, port, callback) {
      var uploadDefaultInfo = {
        src: {
          filetype: fileType,
          streamtrun: true,
          filepath: Toolkits.replacehttpparam(filePath),
        },
        dest: {
          type: "fileserver",
          ip: ip,
          port: port,
          path: `D:/wonder/${this.userInfo.username}/`, //pfsStorageRootDir + "/",
          storagemode: 0,
        },
      };
      Toolkits.jsonpAjaxChar(
        window.startuploadUrl.split("?")[0],
        JSON.stringify(uploadDefaultInfo),
        "GB2312"
      ).then((data) => {
        let resultVal = JSON.parse(data.startupload.result);
        callback && callback(resultVal);
      });
    },
    openFileDlg(type, ocxid, callback) {
      let filePaths,
        params = {
          defext: "",
          filename: "",
          flags: 0x1206,
          filter: window.fileStructureFormat,
          title: "选择上传视频",
        };
      this.$H5PlayerAPI.openSaveFileDlg(params).then((res) => {
        let files = JSON.parse(res.files);
        if (files[0] && files[0].filename) {
          filePaths = files[0].filename.trim().replace(/\\/g, "/");
          this.uploadFiles(filePaths, callback);
        }
      });
    },
    uploadFiles(filePath, callback) {
      this.uploadFileFunc(
        "video",
        filePath,
        window.serverIps[0],
        window.serverPort,
        callback
      );
    },
  },
};
