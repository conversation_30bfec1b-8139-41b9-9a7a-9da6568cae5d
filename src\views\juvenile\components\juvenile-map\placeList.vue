<!--
 * @Date: 2025-01-17 17:16:15
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-30 17:44:32
 * @FilePath: \icbd-view\src\views\juvenile\components\juvenile-map\placeList.vue
-->
<template>
  <div class="dropdown-box">
    <!-- placement="bottom-end" -->
    <Dropdown trigger="custom" :visible="visible">
      <div class="dropdown-btn" @click="() => (visible = !visible)">
        {{ title }}
        <Icon :type="visible ? 'ios-arrow-up' : 'ios-arrow-down'"></Icon>
      </div>
      <slot name="top" slot="list"></slot>
      <DropdownMenu slot="list">
        <DropdownItem>
          <Checkbox
            @on-change="selectAll"
            :value="selectPlace.length == placeList.length"
            >全部场所
          </Checkbox>
        </DropdownItem>
        <div class="line"></div>
        <div class="list">
          <CheckboxGroup v-model="selectPlace" @on-change="changeSelectPlace">
            <DropdownItem v-for="item in placeList" :key="item.key">
              <div class="item-list">
                <Checkbox :label="item.key">
                  <ui-icon :type="item.icon" :color="item.color"></ui-icon>
                  <span style="margin-left: 10px">{{ item.title }}</span>
                </Checkbox>
              </div>
            </DropdownItem>
          </CheckboxGroup>
        </div>
      </DropdownMenu>
      <slot name="bottom" slot="list"></slot>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: "PlaceList",
  props: {
    title: {
      type: String,
      default: "全部图层",
    },
    placeList: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      visible: false,
      selectPlace: [],
    };
  },
  methods: {
    changeSelectPlace(value) {
      this.$emit("changeLayerName", this.selectPlace);
    },
    selectAll(value) {
      let arr = this.placeList.map((item) => item.key);
      if (!value) {
        this.selectPlace = [];
      } else {
        this.selectPlace.push(...arr);
      }
      this.$emit("changeLayerName", this.selectPlace);
    },
    initAllSelectPlace(isWatch = false) {
      this.selectPlace = this.placeList.map((item) => item.key);
      if(isWatch) this.$emit("changeLayerName", this.selectPlace);
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  width: 180px;
  height: 1px;
  background: #d3d7de;
  padding: 0 10px;
}
.dropdown-box {
  .dropdown-btn {
    width: 180px;
    display: flex;
    justify-content: space-between;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.75);
    line-height: 20px;
    margin-right: 5px;
  }

  /deep/ .ivu-dropdown-item {
    padding: 0 0;
  }
  .item-list {
    width: 140px;
  }
  .list {
    max-height: 300px;
    overflow-y: auto;
  }
}
</style>
