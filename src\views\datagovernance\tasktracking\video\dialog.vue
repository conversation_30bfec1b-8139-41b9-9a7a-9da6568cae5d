<template>
  <div>
    <ui-modal ref="modal" :title="title">
      <!-- 实时视频流通畅检测 -->
      <!-- <videoView />   -->

      <!-- 历史视频流通畅检测 -->
      <!-- <histroyView />   -->

      <!-- OSD字幕标注合规检测 -->
      <!-- <odsView />  -->

      <!-- 数据输出 -->
      <!-- <dataOutputView /> -->
    </ui-modal>
  </div>
</template>
<script>
import { weekList } from './video.js';
export default {
  name: 'carDialog',
  props: {},
  data() {
    return {
      title: '',
      curItem: 1,
      curTag: 1,
      weekList: weekList,
    };
  },
  created() {},
  methods: {
    showModal(val) {
      switch (val) {
        case 1:
          this.title = '数据输入';
          break;
        case 2:
          this.title = '历史视频流通畅检测';
          break;
      }
      this.curItem = val;
      this.$refs.modal.modalShow = true;
    },

    tagChange(val) {
      this.curTag = val;
    },
  },
  watch: {},
  components: {
    // videoView: require('./components/video').default,
    // histroyView: require('./components/history').default,
    // odsView: require('./components/ods').default,
    // dataOutputView: require('./components/now-test').default,
  },
};
</script>
<style lang="less" scoped>
.blue {
  color: var(--color-primary);
  margin: 10px 0;
  margin-top: 30px;
}

.ul {
  width: 500px;
  // border: 1px solid #999;
  overflow: hidden;
  // margin-left: 10px;

  li {
    float: left;
    // width: 100px;
    text-align: center;
    color: #fff;
    border: 1px solid #1b82d2;
    padding: 10px 20px;
    margin-right: -1px;
    border-radius: 2px;
    cursor: pointer;
  }
  .active {
    background: var(--color-primary);
    cursor: default;
  }
}

.update {
  margin-top: 30px;
  color: #fff;
  div {
    line-height: 36px;
  }
}

.history {
  color: #fff;
  .ivu-input-wrapper {
    margin: 0 10px;
  }
}
</style>
