<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel">
    <template>
      <FormItem label="检测内容" prop="detectionMode">
        <CheckboxGroup class="check-inline" v-model="formData.detectionMode" @on-change="checkGroupChange">
          <template v-for="(checkItem, checkIndex) in detectionRules">
            <Checkbox :key="'check' + checkIndex" :label="checkItem.value" :disabled="!!checkItem.disabled">
              <span>{{ checkItem.label }}:{{ checkItem.text }}</span>
              <light-tooltip v-if="checkItem.label === '视频亮度'"></light-tooltip>
            </Checkbox>
            <div
              :key="'div' + checkIndex"
              v-if="checkItem.label === '联网质量' && formData.detectionMode && formData.detectionMode.includes('4')"
              class="base-text-color online"
            >
              <div class="mb-sm">
                信令时延超时：
                <InputNumber class="mr-sm" v-model.number="formData.delaySipTimeOut"></InputNumber>
                毫秒
              </div>
              <div class="mb-sm">
                码流时延超时：
                <InputNumber class="mr-sm" v-model.number="formData.delayStreamTimeOut"></InputNumber>
                毫秒
              </div>
              <div>
                关键帧时延超时：
                <InputNumber class="mr-sm" v-model.number="formData.delayIdrTimeOut"></InputNumber>
                毫秒
              </div>
            </div>
          </template>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="指标取值">
        <RadioGroup v-model="formData.indexDetectionMode">
          <Radio
            v-for="(checkItem, checkIndex) in detectionRules"
            :key="'check' + checkIndex"
            :label="(checkIndex + 1).toString()"
            :disabled="!!formData.detectionMode && !formData.detectionMode.includes((checkIndex + 1).toString())"
            >{{ checkItem.label }}
          </Radio>
          <Radio
            v-if="isVideoPlayingAccuracy"
            label="6"
            :disabled="!!formData.detectionMode && !['4', '5'].every((it) => formData.detectionMode.includes(it))"
            >联网质量+视频亮度</Radio
          >
        </RadioGroup>
      </FormItem>
      <FormItem label="需要视频截图">
        <RadioGroup v-model="formData.isScreenshots">
          <Radio label="1">是 </Radio>
          <Radio label="2" :disabled="isVideoPlayingAccuracy && formData.detectionMode.includes('5')">否</Radio>
        </RadioGroup>
      </FormItem>
    </template>
  </basic-recheck>
</template>
<script>
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      detectionRules: [],
      detectionRules_com: [
        { label: '平台状态', text: '设备在国标平台为在线状态', value: '1' },
        { label: '信令响应', text: '设备在线，拉流请求有响应', value: '2' },
        { label: '取流响应', text: '成功接收到实时视频流', value: '3' },
        {
          label: '联网质量',
          text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时',
          value: '4',
        },
      ],
      detectionRules_5: [
        {
          label: '视频亮度',
          text: '检测视频画面亮度是否正常，画面不过暗。',
          value: '5',
        },
      ],
      detectionRules_playingAccuracy: [],
      historyVideoIndexs: [
        'VIDEO_HISTORY_ACCURACY', // 重点历史视频可调阅率
        'VIDEO_GENERAL_HISTORY_ACCURACY', // 普通历史视频可调阅率
      ],
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        detectionMode: [], // 检测内容
        indexDetectionMode: '', // 指标取值
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        isScreenshots: '1', // 需要视频截图
        delaySipTimeOut: '',
        delayStreamTimeOut: '',
        delayIdrTimeOut: '',
        deviceIds: [],
        isUpdatePhyStatus: '',
      },
      oldIndexDetectionMode: '',
    };
  },
  created() {
    this.detectionRules = this.$util.common.deepCopy(this.detectionRules_com);
  },
  methods: {
    checkGroupChange(val) {
      // 检测内容和指标取值对应联动 - 最严格标准
      if (this.isVideoPlayingAccuracy) {
        let findMaxArray = val.sort();
        this.formData.indexDetectionMode = findMaxArray[findMaxArray.length - 1];
      }
      if (!val.length) {
        this.formData.indexDetectionMode = '';
        return false;
      }
      // 先找到检测内容选中的值中的最大的下标
      let maxIndex = null;
      for (let mx = this.detectionRules.length - 1; mx >= 0; mx--) {
        if (val.includes(this.detectionRules[mx].value)) {
          maxIndex = mx;
          break;
        }
      }
      // 通过检测内容中被选中的最大的下标把之前的检测内容选中
      let mode = [];
      for (let i = 0, len = maxIndex; i < len; i++) {
        // 视频亮度 和 联网质量  不联动
        if (i === 0 || (maxIndex === 4 && i == 3 && this.isVideoPlayingAccuracy)) continue;
        mode.push(this.detectionRules[i].value);
      }
      this.$set(this.formData, 'detectionMode', Array.from(new Set([...mode, ...val])));
      if (!this.formData.detectionMode.includes('3')) {
        this.$set(this.formData, 'isScreenshots', '2');
      } else {
        this.$set(this.formData, 'isScreenshots', '1');
      }
      // 视频亮度 和 联网质量 都勾选，则 指标取值 默认勾选 【联网质量+视频亮度】
      if (['4', '5'].every((it) => this.formData.detectionMode.includes(it)) && this.isVideoPlayingAccuracy) {
        this.formData.indexDetectionMode = '6';
      }
      //如果选中的检测内容的最大下标小于指标取值 则取消选中指标取值
      const index = this.detectionRules.findIndex((row) => row.value === this.formData.indexDetectionMode);
      if (index > maxIndex) {
        this.formData.indexDetectionMode = '';
      }
    },
    handleCheckDiabled(val) {
      if (!val.length) {
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b);
      // 1.先取消 禁选
      this.detectionRules.map((item) => {
        this.$set(item, 'disabled', false);
      });
      // 2.再设置 是否禁选
      sortDetectionMode.forEach((item) => {
        let startIndex = 1;
        // 视频亮度 和 联网质量  互不影响
        if (
          item > startIndex &&
          item !== sortDetectionMode[sortDetectionMode.length - 1] &&
          !(item === '4' && sortDetectionMode.includes('5') && this.isVideoPlayingAccuracy)
        ) {
          this.$set(this.detectionRules[item - 1], 'disabled', true);
        } else {
          this.$set(this.detectionRules[item - 1], 'disabled', false);
        }
      });
    },
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        detectionMode: [], // 检测内容
        indexDetectionMode: '', // 指标取值
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        isScreenshots: '1', // 需要视频截图
        delaySipTimeOut: '',
        delayStreamTimeOut: '',
        delayIdrTimeOut: '',
        deviceIds: [],
        isUpdatePhyStatus: '',
      };
    },
  },
  watch: {
    'formData.detectionMode'(val) {
      if (!val) return false;
      this.handleCheckDiabled(val);
    },
    // 重点|普通实时视频可调阅率 才添加 视频亮度
    isVideoPlayingAccuracy(val) {
      if (val) {
        this.detectionRules_playingAccuracy = [
          ...this.$util.common.deepCopy(this.detectionRules_com),
          ...this.$util.common.deepCopy(this.detectionRules_5),
        ];
        this.detectionRules = this.$util.common.deepCopy(this.detectionRules_playingAccuracy);
      } else {
        this.detectionRules = this.$util.common.deepCopy(this.detectionRules_com);
      }
    },
  },
  computed: {
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          detectionMode: this.formData.detectionMode,
          isScreenshots: this.formData.isScreenshots,
          indexDetectionMode: this.formData.indexDetectionMode,
          delaySipTimeOut: this.formData.delaySipTimeOut,
          delayStreamTimeOut: this.formData.delayStreamTimeOut,
          delayIdrTimeOut: this.formData.delayIdrTimeOut,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        },
        ...this.$attrs,
      };
    },
    // 重点|普通实时视频可调阅率、重点指挥图像在线率
    isVideoPlayingAccuracy() {
      return [
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ].includes(this.moduleData?.indexType);
    },
  },
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
    LightTooltip:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/light-tooltip.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.check-inline .online {
  margin-left: 25px;
}
</style>
