<template>
  <div class="place-management height-full">
    <div :class="[loading ? 'mask' : '', 'map-box']">
      <map-base
        ref="deviceMap"
        class="map-base"
        :camera-list="allCameraList"
        :map-position="mapPosition"
        :chooseMapItem="chooseMapItem"
        :needQueryDeviceInfo="true"
        :rightButtonGroup="true"
        @queryDeviceInfo="queryDeviceInfo"
      ></map-base>
    </div>
    <div class="map-search" v-show="!mapOrgTreeVisible" @click="openMapOrgTree">
      <div>
        <i class="icon-font icon-sousuo"></i>
      </div>
    </div>

    <transition name="draw">
      <!--这里的name 和 css 类名第一个字段要一样-->
      <div class="place-box height-full auto-fill" v-show="mapOrgTreeVisible">
        <map-search-tree
          ref="uiSearchTree"
          placeholder="请输入组织机构名称或组织机构编码"
          :scroll="360"
          :check-strictly="true"
          :show-checkbox="false"
          :default-props="defaultProps"
          :node-key="nodeKey"
          @selectDevice="selectDevice"
          @putaway="mapOrgTreeVisible = false"
        >
        </map-search-tree>
      </div>
    </transition>

    <div class="layer-management">
      <el-popover
        popper-class="layer-popover"
        visible-arrow="false"
        placement="bottom-end"
        v-model="layerVisible"
        trigger="click"
      >
        <div class="layer-title">图层管理</div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-equipmentlibrary"></i>
              </span>
              <span>视频监控</span>
            </div>
            <i-switch size="small" true-value="5" @on-change="(e) => layerCondition(e, 'sbgnlx.5')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-renliankakou"></i>
              </span>
              <span>人脸卡口</span>
              <span class="count">
                (<span>{{ allCameraList.filter((e) => e.sbgnlx === '2').length }}</span
                >)
              </span>
            </div>
            <i-switch size="small" true-value="2" @on-change="(e) => layerCondition(e, 'sbgnlx.2')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-cheliangshitushuju"></i>
              </span>
              <span>车辆卡口</span>
              <span class="count">
                (<span>{{ allCameraList.filter((e) => e.sbgnlx === '1').length }}</span
                >)
              </span>
            </div>
            <i-switch size="small" true-value="1" @on-change="(e) => layerCondition(e, 'sbgnlx.1')" />
          </div>
        </div>
        <Divider></Divider>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-yileidian"></i>
              </span>
              <span>一类点</span>
            </div>
            <i-switch size="small" true-value="1" @on-change="(e) => layerCondition(e, 'sbdwlx.1')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-ersanleidian"></i>
              </span>
              <span>二三类点</span>
            </div>
            <i-switch size="small" :true-value="'2,3'" @on-change="(e) => layerCondition(e, 'sbdwlx.2,3')" />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-gonganneibujiankongdian"></i>
              </span>
              <span>公安内部监控点</span>
            </div>
            <i-switch size="small" true-value="4" @on-change="(e) => layerCondition(e, 'sbdwlx.4')" />
          </div>
        </div>
        <Divider></Divider>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-jiancehegedianwei"></i>
              </span>
              <span>检测合格点位</span>
            </div>
            <i-switch
              size="small"
              true-value="0000,1000"
              @on-change="(e) => layerCondition(e, 'checkStatus.0000,1000')"
            />
          </div>
        </div>
        <div class="device-type">
          <div class="device-type-item">
            <div>
              <span class="type-bg">
                <i class="icon-font icon-jianceyichangdianwei"></i>
              </span>
              <span>检测异常点位</span>
            </div>
            <i-switch
              size="small"
              true-value="0100,0001,0010,0011,0101,0110,0111"
              @on-change="(e) => layerCondition(e, 'checkStatus.0100,0001,0010,0011,0101,0110,0111')"
            />
          </div>
        </div>
        <div class="form-content"></div>
        <div :class="[layerVisible ? 'layer-content-active' : '', 'layer-content']" slot="reference">
          <i class="icon-font icon-tucengguanli"></i>
        </div>
      </el-popover>
    </div>
    <div class="layer-description">
      <div>
        <img src="@/assets/img/device-map/layer-description-online.png" />
        在线
      </div>
      <div>
        <img src="@/assets/img/device-map/layer-description-not-online.png" />
        离线
      </div>
      <div>
        <span></span>
        异常
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import common from '@/config/api/common';
export default {
  name: 'deviceMap',
  props: {},
  data() {
    return {
      searchData: {
        keyWord: '',
        regionCode: '',
      },
      defaultTree: {
        regionCode: '',
      },
      saveLoading: false,
      mapPosition: {
        center: {},
      },
      nodeKey: 'id',
      defaultProps: {
        label: 'orgName',
        children: 'children',
        isLeaf: 'leaf',
      },
      layerVisible: false,
      list: [],
      layerParams: {
        checkStatus: [],
        sbdwlx: [],
        sbgnlx: [],
      },
      mapOrgTreeVisible: false,
      loading: true,
      chooseMapItem: {},
    };
  },
  async created() {
    this.loading = true;
    await this.getCameraList();
    this.loading = false;
  },
  methods: {
    ...mapActions({
      getCameraList: 'common/getDeviceMapCameraList',
    }),
    async check(checkedData) {
      console.log(checkedData);
    },
    selectDevice({
      latitude,
      longitude,
      isOnline: checkStatus,
      orgCodeName: orgName,
      tagList = [],
      errorMessage = [],
      ...rest
    }) {
      if (!latitude || !longitude) {
        return this.$Message.error('经纬度信息不全！');
      }
      this.chooseMapItem = {
        ...rest,
        orgName,
        longitude,
        latitude,
        checkStatus,
        tagList: tagList.filter((e) => e.tagName),
        errorMessage: errorMessage.filter((e) => e),
      };
    },
    // 图层过滤
    layersFilter(filterCameraList) {
      this.$refs.deviceMap._initSystemPoints2Map([...filterCameraList]);
    },
    multiFilter(array, filters) {
      const filterKeys = Object.keys(filters);
      return array.filter((item) => {
        return filterKeys.every((key) => {
          if (!filters[key].length) {
            return true;
          } else if (item[key] && item[key].includes('/')) {
            return item[key].split('/').toString().includes(filters[key]);
          } else {
            return !!~filters[key].indexOf(item[key]);
          }
        });
      });
    },
    // 图层筛选条件改变
    layerCondition(e, val) {
      const [type, typeVal] = val.split('.');
      if (e) {
        const list = e.split(',');
        if (list.length > 0) {
          list.forEach((item) => {
            this.layerParams[type].push(item);
          });
        }
      } else {
        typeVal.split(',').forEach((item) => {
          let index = this.layerParams[type].indexOf(item);
          if (index > -1) {
            this.layerParams[type].splice(index, 1);
          }
        });
      }
      const fillerList = this.multiFilter(this.allCameraList, this.layerParams);
      this.layersFilter(fillerList);
    },
    openMapOrgTree() {
      this.mapOrgTreeVisible = true;
    },
    async queryDeviceInfo(deviceId) {
      let res = await this.$http.post(common.getDeviceInfo, { deviceId });
      this.selectDevice(res.data.data);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      allCameraList: 'common/getDeviceMapCameraList',
      mapSelectTreeList: 'common/getMapSelectTreeList',
      checkStatus: 'algorithm/check_status', // 检测状态
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/propertySearch_sbgnlx',
    }),
  },
  components: {
    MapBase: require('@/components/map-base/index.vue').default,
    MapSearchTree: require('./map-search-tree.vue').default,
  },
};
</script>
<style lang="less">
.layer-popover {
  margin-top: 0 !important;
  position: absolute !important;
  top: 220px !important;
  z-index: 99999 !important;
  width: 282px !important;
  padding: 0 !important;
  padding-bottom: 10px !important;
  background: #041d42 !important;
  border: 1px solid #2967c8 !important;
  color: #fff;
  .popper__arrow {
    display: none !important;
  }
  .layer-title {
    height: 44px;
    line-height: 44px;
    font-weight: bold;
    border-bottom: 1px solid #1d4582;
    padding-left: 23px;
  }
  .device-type {
    padding: 11px 13px 4px 23px;
    .device-type-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        display: flex;
        align-items: center;
        .type-bg {
          display: inline-block;
          width: 34px;
          height: 34px;
          margin-right: 10px;
          background-image: url('~@/assets/img/device-map/layer-icon.png');
          background-size: 100% 100%;
          line-height: 34px;
          text-align: center;
          .icon-font {
            background-image: -webkit-linear-gradient(180deg, #5d9be2 0%, #1575f3 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 14px !important;
          }
        }
        .count {
          font-weight: bold;
          color: rgb(187, 112, 57);
          margin-left: 10px;
        }
      }
    }
  }
  .ivu-divider {
    background: #1d4582;
    margin: 8px 0;
  }
}
</style>
<style lang="less" scoped>
.place-management {
  background-color: var(--bg-content);
  .add-place {
    padding: 0 15px;
  }
  .map-box {
    position: relative;
    width: 100%;
    height: 100%;
  }
  .mask {
    position: relative;
  }
  .mask:after {
    content: '设备数据加载中...';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    text-align: center;
    line-height: 25;
    vertical-align: sub;
    font-size: 26px;
    display: inline-block;
  }
  .map-search {
    position: absolute;
    left: 12px;
    top: 20px;
    width: 31px;
    height: 31px;
    background: linear-gradient(180deg, #2b84e2 0%, #1553a1 100%);
    border-radius: 4px;
    color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .draw-enter-active,
  .draw-leave-active {
    transition: all 1s ease;
  }
  .draw-enter, .draw-leave-to /* .fade-leave-active below version 2.1.8 */ {
    height: 0;
  }
  .place-box {
    position: absolute;
    left: 11px;
    top: 0px;
    bottom: 8px;
    .area-tree {
      @{_deep}.ivu-select {
        width: 259px;
      }
      @{_deep}.ivu-select-dropdown {
        width: 259px;
      }
    }
    .btn-div {
      display: flex;
      justify-content: space-between;
    }
    .content-tip {
      color: #c76d28;
    }
    .place-list {
      width: 100%;
      @{_deep} .ivu-scroll-container {
        height: 100% !important;
        overflow-y: auto;
      }
      .place-item {
        color: #fff;
        padding: 5px 10px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:hover {
          background-color: #041129;
        }
        &.active {
          background-color: #184f8d;
        }
      }
    }
  }
  .layer-management {
    position: absolute;
    top: 74px;
    right: 30px !important;
    .layer-content {
      position: relative;
      width: 38px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-image: url('~@/assets/img/device-map/layer.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      .icon-font {
        color: #f5f5f5;
        font-size: 16px;
      }
    }
    .layer-content:hover,
    .layer-content-active {
      background-image: url('~@/assets/img/device-map/layer-active.png') !important;
    }
  }
  .layer-description {
    position: absolute;
    right: 20px;
    bottom: 10px;
    height: 98px;
    width: 72px;
    background: #07295d;
    box-shadow: 0px 3px 10px #000000;
    font-size: 12px;
    color: #f5f5f5;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    > div {
      display: flex;
      align-items: center;
      padding-left: 9px;
      & > span {
        display: inline-block;
        width: 14px;
        height: 14px;
        background: #f15b2d;
        margin-right: 8px;
        margin-left: 4px;
      }
      & > img {
        margin-right: 6px;
      }
    }
  }
}
</style>
