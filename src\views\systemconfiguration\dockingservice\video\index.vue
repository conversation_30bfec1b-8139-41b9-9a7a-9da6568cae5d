<template>
  <div class="page-evaluationmanagement">
    <div class="content auto-fill">
      <div class="table-box auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #manufacturer="{ row }">{{ row.manufacturer | facturer }}</template>
          <template #online="{ row }">
            <span>{{ row.online | status }}</span>
          </template>
          <template #registerTime="{ row }">
            <span>{{ row.registerTime || '--' | filterDateFun }}</span>
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import algorithm from '@/config/api/algorithm';

export default {
  name: 'algorithm',
  data() {
    return {
      loading: false,
      algorithmVendorType: '',
      addorEditModalShow: false,
      evaluationResultShow: false,
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '国标ID', key: 'deviceId', width: '200px' },
        { title: `${this.global.filedEnum.deviceName}`, key: 'name' },
        { title: '生产厂商', slot: 'manufacturer' },
        { title: '型号', key: 'model' },
        { title: '固件版本', key: 'firmware' },
        { title: '传输协议', key: 'transport' },
        { title: '传输模式', key: 'streamMode' },
        { title: `${this.global.filedEnum.ipAddr}`, key: 'ip' },
        { title: '端口号', key: 'port' },
        { title: '在线状态', slot: 'online' },
        { title: '注册时间', slot: 'registerTime', width: 200 },
      ],
      tableData: [],
      addorEditModalAction: {
        title: '新增',
        action: 'add',
        visible: false,
      },
      addorEditModalData: {},
      evaluationlData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      searchData: {
        searchValue: '',
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      selectOrgTree: {
        orgCode: '',
      },
      resultTypeMap: {},
      statusTypeMap: {},
    };
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmTypeList: 'algorithm/algorithmType',
    }),
  },
  async created() {
    if (this.algorithmList.length == 0) await this.getAlldicData();
    this.init();
    // this.getTableList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // async getTableList() {
    //   try {
    //     this.loading = true;
    //     let res = await this.$http.post(algorithm.findByCondition, this.searchData);
    //     this.tableData = res.data.data.data;
    //     this.pageData.totalCount = res.data.data.totalCount;
    //   } catch (err) {
    //     this.loading = false;
    //     console.log(err);
    //   }
    // },
    async init() {
      try {
        this.loading = true;

        this.$http
          .get(algorithm.videoConfig, {
            // algorithmVendorType: this.algorithmVendorType,
            params: {
              algorithmType: '',
              searchValue: '',
            },
            ...this.searchData,
          })
          .then((res) => {
            if (res.data.code == 200) {
              this.tableData = res.data.data.entities;
              this.tableData.forEach((item) => {
                item.algorithmVendorTypeLabel = this.getAlgorithmLabel(item.algorithmVendorType);
                item.algorithmTypeLabel = this.getAlgorithmTypeLabel(item.algorithmType);
              });
              this.loading = false;
              this.pageData.totalCount = res.data.data.total;
            } else {
              console.log(res.data.msg);
            }
          })
          .finally(() => {});
      } catch (error) {
        console.log(error);
      }
    },

    resetSearch() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 10 };
      this.searchData = {
        searchValue: '',
        params: { pageNumber: 1, pageSize: 10 },
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      };

      this.algorithmVendorType = '';
      this.init();
    },

    addPage() {
      this.$refs.addPage.showModal(1);
      this.$refs.addPage.form = {};
    },

    addSuccess() {
      this.init();
    },
    add() {
      this.$refs.addPage.showModal(1);
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      // this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      // this.searchData.pageSize = val;
      this.init();
    },
    // 重置
    resetSearchData() {
      // this.copySearchDataMx(this.searchData);
      this.init();
    },
    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    // 选择组织机构名称
    selectTree({ orgCode }) {
      this.searchData.orgCode = orgCode;
      this.init();
    },
    update() {
      this.init();
    },

    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },

    getAlgorithmTypeLabel(val) {
      var arr = this.algorithmTypeList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
      // var arr = val.split(",");
      // var str = "";
      // arr.forEach(item => {
      //   var obj = this.algorithmTypeList.find(ite => { return ite.value == item});
      //   str += obj.label + ","
      // })

      // return str.substring(0, str.length-1)
    },
  },
};
</script>

<style lang="less" scoped>
.page-evaluationmanagement {
  .left-content {
    background: var(--bg-content);
    float: left;
    width: 260px;
    padding: 10px;
    background: var(--bg-content);
    height: 100%;
    .record-title {
      padding: 10px 0;
      .add_case {
        .name {
          margin-left: 10px;
          position: relative;
          top: 2px;
        }
      }
    }
    .collapse-content-p {
      border: 1px solid transparent;
      border-radius: 4px;
      padding: 10px;
      color: @font-color-white;
      background: @bg-table-block;
      margin-bottom: 10px;
      &.active {
        border-color: @color-other;
      }
    }
    .assessment-list {
      position: relative;
    }
  }
  .content {
    // float: right;
    // width: calc(~"100% - 270px");
    width: 100%;
    height: 100%;
    // padding-top: 20px;
    background: var(--bg-content);
    .search-wrapper {
      overflow: hidden;
      padding: 0 12px 0 20px;
      .input-width {
        width: 363px;
      }
    }
    .table-box {
      padding: 20px;
      position: relative;
      .sucess {
        color: @color-success;
      }
      .error {
        color: @color-failed;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .mr-sm {
      color: #2d8cf0;
    }
  }
}
.left-item {
  @{_deep} .ivu-form-item-label {
    width: 200px;
  }
  @{_deep} .ivu-form-item-error-tip {
    line-height: 1;
  }
}
</style>
<style lang="less" scoped>
.page-indexmanagement {
  .form-content {
    padding: 0 50px;
    .ivu-form-item {
      margin-bottom: 20px;
      .time {
        width: 22%;
      }
      .lag {
        width: 13.5%;
        margin: 0 10px;
      }
      .canshu {
        width: 15%;
      }
    }
  }
}

.form {
  padding: 20px;
  .based-field-label {
    color: #fff;
    font-size: 14px;
    padding-right: 12px;
  }
  button {
    margin-left: 12px;
  }
}
</style>
