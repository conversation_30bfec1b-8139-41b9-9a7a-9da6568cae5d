export default {
  aggregateEnums: {
    accurrency: '图像抓拍时间准确性检测',
    timeliness: '图像上传及时性检测',
    bigPic: '图像URL检测',
    faceStructure: '人脸结构化',
    repeatImage: '重复图像识别处理',
    bigContactSmall: '大小图关联正确检测',
    smallOnly: '小图唯一人脸检测处理',
    dataexport: '数据输出',
  },
  aggregateOptions: [
    {
      addVisible: false,
      top: '1.1rem',
      left: '1.25%',
      datas: [
        {
          // name: "dataaccessPopup",
          icon: 'icon-zu16191',
          title: '数据输入',
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '接入总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '今日增量', num: 0, color: '#F18A37', fileName: 'todayAccessDataCount' },
          ],
          left: '10px',
          iconPass: true,
          iconSetting: false,
        },
      ],
      connectingOptions: {
        width: '3.5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '13.8%',
      },
    },
    {
      addVisible: false,
      top: '0.35rem',
      left: '17.7%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-tuxiangzhuapaishijianzhunquexingjiance',
          title: '图像抓拍时间准确性检测',
          subTitle: '异常数据',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '时间异常', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'imagecapturePopup',
          icon: 'icon-tuxiangshangchuanjishixingjiance',
          title: '图像上传及时性检测',
          subTitle: '异常数据',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '上传超时', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'imagecapturePopup',
          icon: 'icon-datuURLjiance',
          title: '图像URL检测',
          subTitle: '异常数据',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '3.5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '31.5%',
      },
    },
    {
      addVisible: false,
      top: '1.0rem',
      left: '35.5%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-zhongfutuxiangshibiechuli',
          title: '重复图像识别处理',
          subTitle: '异常数据',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
            // {title: '治理优化', num: 0, color: '#F18A37', fileName: ''},
            // {title: '优化占比', num: '0%', color: '#13B13D', fileName: ''},
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '3.5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '48.1%',
      },
    },
    {
      addVisible: false,
      top: '1rem',
      left: '52.1%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-renlianjiegouhua',
          title: '人脸结构化',
          list: [
            { title: '算法数量', num: 0, color: '#05FEF5', fileName: 'algorithmCount' },
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '结构化数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '转化率', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: false,
        },
      ],
      connectingOptions: {
        width: '3.5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '64.7%',
      },
    },
    {
      addVisible: false,
      top: '0.58rem',
      left: '68.8%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-daxiaotuguanlianzhengquejiance',
          title: '大小图关联正确检测',
          subTitle: '异常数据',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'imagecapturePopup',
          icon: 'icon-xiaotuweiyirenlianjiancechuli',
          title: '小图唯一人脸检测处理',
          subTitle: '异常数据',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '3.5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '82.6%',
      },
    },
    {
      addVisible: false,
      top: '0.98rem',
      left: '86.6%',
      datas: [
        {
          name: 'exportDataPopup',
          icon: 'icon-zu1665',
          title: '数据输出',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
            { title: '治理优化', num: 0, color: '#F18A37', fileName: '' },
            { title: '优化占比', num: '0%', color: '#13B13D', fileName: '' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
    },
  ],
};
