<template>
  <div>
    <carModal
      :curIndexShow="false"
      :curIndex="curIndex"
      :infoModalShow="true"
      :bigImgShow="true"
      :tabsList="tabsList"
      modalTitle="数据输出-异常数据"
      titleChild
      ref="carModal"
      :listObj="listObj"
      @abnormalDetail="abnormalDetail"
    >
      <div slot="echarts">
        <div class="based-field-header">
          <div class="border-header"></div>
          <div class="title-header">检测结果统计</div>
        </div>
        <div class="echarts" style="height: 180px">
          <Row :gutter="6">
            <Col span="10">
              <div class="morePie" style="height: 180px">
                <dataPie :pieMoreEchartData="pieMoreEchartData" ref="morePic" />
              </div>
            </Col>
            <Col span="14">
              <div class="singlePie" style="height: 180px">
                <barEchart :barEchartList="barEchartList" ref="barEchart" color="#19C176" />
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </carModal>
    <yichangView v-if="yichangShow" ref="yichang" />
    <zhiliView v-if="zhiliShow" ref="zhili" />
  </div>
</template>
<script>
import api from '@/config/api/user';
import carThrem from '@/config/api/car-threm';
export default {
  name: 'data-output',
  props: {
    curIndex: {
      type: Number,
      default: 1, // 当前展示步骤
    },
  },
  data() {
    return {
      dataObj: {},
      yichangShow: false,
      zhiliShow: false,
      pieMoreEchartData: { title: '评测总数', count: '', data: [] },
      barEchartList: { title: 'title', value: 'count', list: [] },
      tabsList: { '0': [], '1': [] },
      listObj: {
        columns: [
          { type: 'index', width: 70, title: '序号', fixed: 'left' },
          {
            title: `${this.global.filedEnum.deviceId}`,
            key: 'deviceId',
            width: 200,
            fixed: 'left',
          },
          {
            title: `${this.global.filedEnum.deviceName}`,
            key: 'deviceName',
            width: 200,
          },
          { title: '组织机构', key: 'orgCode', width: 200 },
          {
            title: `${this.global.filedEnum.longitude}`,
            key: 'longitude',
            width: 200,
          },
          {
            title: `${this.global.filedEnum.latitude}`,
            key: 'latitude',
            width: 200,
          },
          {
            title: `${this.global.filedEnum.macAddr}`,
            slot: 'macAddr',
            width: 200,
          },
          { title: this.global.filedEnum.ipAddr, slot: 'ipAddr', width: 200 },
          {
            title: this.global.filedEnum.sbgnlx,
            slot: 'sbgnlxText',
            width: 200,
          },
          {
            title: this.global.filedEnum.sbdwlx,
            slot: 'sbdwlxText',
            width: 200,
          },
          { title: '位置类型', slot: 'positionTypeText', width: 200 },
          { title: '安装地址', key: 'address', width: 200 },
          { title: '异常类型数量', key: 'errorPhotoNum', width: 200 },
          { title: '异常图像数量', key: 'errorTypeCount', width: 200 },
          {
            title: '操作',
            slot: 'action',
            width: 140,
            fixed: 'right',
          },
        ],
        key: 'entities',
        loadData: (parameter) => {
          return this.$http
            .post(
              carThrem.queryVehiclePageList,
              Object.assign(parameter, {
                reasonTypes: this.curIndex != 6 ? [this.curIndex] : [],
              }),
            )
            .then((res) => {
              // 把后台数组转为list返回
              return res.data;
            });
        },
      },
    };
  },
  mounted() {
    // this.modalShow = true
  },
  methods: {
    init(item) {
      this.$store.commit('carAndVideo/setExportType', 0);
      if (item) {
        item.list.map((val) => {
          this.$set(this.dataObj, val.fileName, val.num);
        });
      }
      this.pieMoreEchartData.count = String(this.dataObj.accessDataCount);
      this.pieMoreEchartData.data = [
        {
          name: '合格数据',
          value: this.dataObj.accessDataCount - this.dataObj.existingExceptionCount,
        },
        { name: '异常数据', value: this.dataObj.existingExceptionCount },
      ];
      this.barEchartList.list = [];
      this.getList();
      this.$refs.carModal.showModal();
      this.$nextTick(() => {
        this.$router.push({ query: {} });
      });
    },
    async getList() {
      var keyObj = {};
      const that = this;
      that.barEchartList.list = [];
      await this.$http.get(api.queryByTypeKey + '?typekey=vehicle_reason_type').then((res) => {
        var list = res.data.data;
        that.tabsList[1] = [];
        list.forEach((item) => {
          item.name = item.dataValue;
          keyObj[item.dataKey] = item.dataValue;
          if (item.dataDes === '1') {
            that.tabsList[1].push(item);
          }
        });
        that.tabsList[0] = list;
      });
      await this.$http.post(carThrem.queryStatisticsByReasonType).then((res) => {
        if (res.data.code === 200) {
          that.barEchartList.list = res.data.data.map((val) => {
            val.title = keyObj[val.reason_type] || val.reason_type;
            return val;
          });
        }
      });
    },
    abnormalDetail(item, searchData) {
      if (searchData.isOptimize == 0) {
        this.yichangShow = true;
        this.$nextTick(() => {
          this.$refs.yichang.showModal(this.tabsList[0], item);
        });
      } else {
        this.zhiliShow = true;
        this.$nextTick(() => {
          this.$refs.zhili.showModal(this.tabsList[0], item);
        });
      }
    },
  },
  watch: {},
  components: {
    carModal: require('./carmodal/car-modal').default,
    dataPie: require('./carmodal/pie-data-echart').default,
    barEchart: require('./carmodal/bar-echart').default,
    yichangView: require('./carmodal/data-yichang-dialog').default,
    zhiliView: require('./carmodal/data-zhili-dialog').default,
  },
};
</script>
<style lang="less" scoped>
.based-field-header {
  display: flex;
  padding-bottom: 20px;
  .border-header {
    width: 8px;
    height: 30px;
    background: #239df9;
    margin-right: 6px;
  }
  .title-header {
    color: #fff;
    width: 80%;
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
    background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
    font-size: 16px;
  }
}
.echarts {
  width: 100%;
  // height: 150px;
  margin-top: 6px;
  margin-bottom: 20px;
  .morePie {
    position: relative;
    float: left;
    width: 300px;
    width: 100%;
    background: var(--bg-sub-content);
    height: 150px;
    // background: #0b3469;
  }
  .singlePie {
    position: relative;
    float: left;
    width: 100%;
    // width: 150px;
    height: 150px;
    background: var(--bg-sub-content);
  }
}
.mr20 {
  margin-right: 20px;
}
</style>
