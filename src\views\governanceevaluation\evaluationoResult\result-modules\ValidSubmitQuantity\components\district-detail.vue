<template>
  <ui-modal v-model="visible" :title="title" width="50rem" footerHide>
    <div class="detail-content auto-fill">
      <div class="operation-bar">
        <div class="operation-bar-left">
          <ui-label class="inline mr-md mb-sm" label="达标情况">
            <Select
              class="width-sm"
              v-model="qualified"
              placeholder="请选择达标情况"
              clearable
              @on-change="handleChange"
            >
              <Option value="1">达标</Option>
              <Option value="2">不达标</Option>
            </Select>
          </ui-label>
        </div>
        <div class="operation-bar-right">
          <Button type="primary" class="button-export mb-sm" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white mr-xs"></i>
            <span class="ml-xs">导出</span>
          </Button>
        </div>
      </div>
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #qualified="{ row }">
          <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
            {{ row.qualified === '1' ? '达标' : '不达标' }}
          </span>
        </template>
      </ui-table>
    </div>
  </ui-modal>
</template>

<script>
import downLoadTips from '@/mixins/download-tips';
import detectionResult from '@/config/api/detectionResult';
export default {
  name: 'district-detail',
  mixins: [downLoadTips],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    detailObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: '区县详情',
      visible: false,
      qualified: '',
      loading: false,
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
        {
          title: '区县名称',
          key: 'civilName',
          align: 'left',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '行政区划代码',
          key: 'civilCode',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '达标数量',
          key: 'standardNum',
          align: 'left',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '满分数量',
          key: 'fullDeviceNum',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '上报数量',
          key: 'actualNum',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '达标情况',
          slot: 'qualified',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
      ],
      tableData: [],
      exportLoading: false,
      paramsList: {},
      codeKey: '',
    };
  },
  methods: {
    async getStatisticalList() {
      try {
        this.loading = true;
        let params = {
          // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.detailObj[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.qualified,
        };
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.tableData = data.map((item) => {
          item = Object.assign(item, item.detail || {});
          return item;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    handleReset() {},
    save() {},
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.detailObj[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.qualified,
          // pageNumber: this.searchData.pageNum,
          // pageSize: this.searchData.pageSize,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(detectionResult.exportStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    handleChange(val) {
      this.qualified = val;
      this.getStatisticalList();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.paramsList = this.$route.query;
        this.title =
          (this.paramsList.statisticType === 'REGION' ? this.detailObj.civilName : this.detailObj.orgName) +
          '-区县详情';
        this.codeKey = this.paramsList.statisticType === 'REGION' ? 'civilCode' : 'orgCode';
        this.getStatisticalList();
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.detail-content {
  height: 600px;
}
.operation-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
  }
}
</style>
