<!--
    * @FileDescription: 行车轨迹=>列表
    * @Author: H
    * @Date: 2023/09/18
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="detailsBox">
        <div class="details-list" v-show="detailsList">
            <div class="box-hint">
                <Icon type="ios-undo" @click="handleback" />
                <span @click="handleback">行车轨迹 > ""结果</span>
            </div>
            <div class="serach-hint">
                <div class="left-hint">
                    <p class="hint_day">共<span class="count_number">100</span>天</p>
                    <span class="timerange">2023-9-18--2023-09-20</span>
                </div>  
                <Button icon="md-open" size="small">导出</Button>
            </div>
            <!-- 每日时间列表 -->
            <div class="table-box">
                <ul class="vehicle-ul">
                    <li class="vehicle-ul-li" 
                        v-for="(item, index) in vehicleList"
                        :key="index"  
                        @click="handleDetails(item, index)">
                        <p class="select-icon" @click="handleCheched(item, index)" :class="{'checked-icon': index == vehileIndex}">
                            <Icon type="md-checkmark-circle" class="checkmark-circle" />
                        </p>
                        <p class="line-style default">
                            <span class="line"></span>
                            <span class="circle"></span>
                            <span class="line"></span>
                        </p>
                        <p class="time-style">
                        <i class="iconfont icon-time"></i> 
                        <span>2023-09-18</span>
                        </p>
                        <p class="count">48次</p>
                        <p class="arrows">
                            <i class="iconfont icon-jiantou"></i> 
                        </p>
                    </li>
                    <ui-empty v-if="vehicleList.length == 0 && !loading"></ui-empty>
                    <ui-loading v-if="loading"></ui-loading>
                </ul>
                <Page size="small" :total="topTotal" :page-size="topPage.pageSize" transfer class="page" @on-change="handleTopPage" @on-page-size-change="handleTopPageSize" />
            </div>
        </div>
        <div class="details-list" v-show="!detailsList">
            <div class="box-hint">
                <Icon type="ios-undo" @click="handlebackdetails" />
                <span @click="handlebackdetails">行车轨迹 > ""结果</span>
            </div>
            <div class="serach-hint">
                <p class="hint_day">共<span class="count_number">{{  bottomTotal }}</span>条结果</p>
            </div>
            <!-- 列表详情 -->
            <div class="table-box">
                <ul class="path_ul">
                    <li class="path_ul_li"
                        :class="{'path_active': pathIndex == index}" 
                        v-for="(item, index) in dataList"
                        :key="index"
                        @click="handlePath(item, index)">
                        <div class="path_number">{{ index+1 }}</div>
                        <div class="path_right">
                            <div class="path_title">
                                <i class="iconfont icon-time"></i> 
                                <span>{{ item.detailAddress }}</span>
                            </div>
                            <div class="path_time">
                                <i class="iconfont icon-time"></i> 
                                <span>{{ item.absTime }}</span>
                            </div>
                        </div>
                    </li>
                    <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
                    <ui-loading v-if="loading"></ui-loading>
                </ul>
                <Page size="small" :total="bottomTotal" :page-size="bottomPage.pageSize" transfer class="page" @on-change="handleBottomPage" @on-page-size-change="handleBottomPageSize" />
            </div>
        </div>
    </div>
</template>

<script>
import { vehicleRecordSearchEx } from '@/api/wisdom-cloud-search'
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            topTotal: 0,
            topPageSize: 13,
            detailsList: true,
            bottomTotal:0,
            topPage: {
                pageNumber: 1,
                pageSize: 13,
            },
            vehicleList: [{}],
            vehileIndex: 0,
            bottomPage: {
                pageNumber: 1,
                pageSize: 13, 
            },
            dataList: [],
            pathIndex: 0,
            loading: false
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        handleList(list, param) {
            console.log(list, 'list')
            this.vehicleList = list;
        },
        //详情列表
        handleDetails(value) {
            console.log(value, 'params')
            this.detailsList = true;
            this.loading = true;
            let params = {
                ...value,
                ...this.bottomPage
            }
            vehicleRecordSearchEx(params)
            .then(res => {
                this.dataList = res.data.entities;
                this.bottomTotal = res.data.total;
                this.$emit('vehicleDataList', this.dataList)
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleCheched(item, index = 0) {
            this.vehileIndex = index;
        },
        handlePath(item, index){
            this.pathIndex = index;
        },
        handleback() {
            this.$emit('backSearch')
        },
        handlebackdetails() {
            // this.detailsList = true;
        },
        handleTopPage(){

        },
        handleTopPageSize() {

        },
        handleBottomPage() {

        },
        handleBottomPageSize() {

        }
    }
}
</script>

<style lang='less' scoped>
.detailsBox{
    position: absolute;
    top: 10px;
    left: 10px;
    .details-list{
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        width: 370px;
        filter: blur(0px);
        position: relative;
        .box-hint{
            width: 370px;
            height: 40px;
            background: #2c86f8;
            border-bottom: 1px solid #D3D7DE;
            color: #fff;
            font-size: 14px;
            line-height: 40px;
            padding-left: 14px;
            display: flex;
            align-items: center;
            .icon-jiantou{
                transform: rotate(90deg);
                display: inline-block;
                cursor: pointer;
            }
            .ivu-icon-ios-undo{
                font-size: 20px;
                cursor: pointer;
            }
            span{
                font-size: 14px;
                cursor: pointer;
                margin-left: 10px;
            }
        }
        .serach-hint{
            display: flex;
            font-size: 14px;
            justify-content: space-between;
            margin-top: 2px;
            padding: 0 2px 0 4px;
            .left-hint{
                flex: 1;
                display: flex;
                align-items: center;
            }
            .hint_day{
                font-size: 14px;
                color: #a5b0b6;
                display: inline-block;
                .count_number{
                    margin: 0 2px;
                    color: #38aef3;
                }
            }
            .timerange{
                font-size: 14px;
                margin-left: 5px;
                white-space: nowrap;
            }
        }
        .table-box{
            display: flex;
            flex-direction: column;
            padding-bottom: 10px;
        }
        .vehicle-ul{
            padding-bottom: 20px;
            position: relative;
            min-height: 200px;
            .vehicle-ul-li{
                display: flex;
                align-items: center;
                border-bottom: 1px dashed #a5b0b64f;
                padding: 10px 20px;
                cursor: pointer;
                &:hover{
                    background: #EEEEEE;
                }
                .select-icon{
                    cursor: pointer;
                    /deep/.ivu-icon-md-checkmark-circle{
                        font-size: 22px;
                    }
                }
                .checked-icon{
                    color: #2c86f8;
                }
                .line-style{
                    margin-left: 10px;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    .line{
                        background: #a5b0b6;
                        // #38eaf3
                        padding: 2px 12px;
                        border-radius: 3px;
                        display: inline-block;
                    }
                    .circle{
                        padding: 6px;
                        border-radius: 45px;
                        background: #a5b0b6;
                        display: inline-block;
                        margin: 0 2px;
                    }
                }
                .time-style{
                    margin-left: 10px;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    .icon-time{
                        font-size: 18px;
                        margin-right: 4px;
                    }
                }
                .count{
                    width: 55px;
                    margin-left: 10px;
                    font-size: 14px;
                }
                .arrows{
                    font-size: 20px;
                    transform: rotate(-90deg);
                    margin-left: 5px;
                }
            }
        }
        .path_ul{
            padding: 0 10px 20px 10px;
            margin-top: 10px;
            position: relative;
            min-height: 200px;
            .path_ul_li{
                // background: #38aef3;
                // color: #fff;
                font-size: 14px;
                display: flex;
                padding: 5px 10px;
                align-items: baseline;
                cursor: pointer;
                .path_number{
                    background: #f25051;
                    color: #fff;
                    width: 36px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    border-radius: 4px;
                }
                .path_right{
                    margin-left: 10px;
                }
                .path_time{
                    margin-top: 5px;
                }
            }
            .path_active{
                background: #38aef3;
                color: #fff;
                .path_number{
                    color: #38aef3;
                    background: #fff;
                }
            }
        }

    }
    .page{
        text-align: center;
        margin-top: 10px;
    }
}
</style>
