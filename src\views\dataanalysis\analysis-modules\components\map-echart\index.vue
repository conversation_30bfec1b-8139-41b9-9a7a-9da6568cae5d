<template>
  <div class="map-echart-box">
    <div class="influence-title" :class="{ 'influence-title-fullscreen': isFullscreen }">
      <span class="title-rect"></span>
      <span class="ml-sm mr-sm title-span f-16">设备安装位置分布</span>
      <span class="tip-text"></span>
      <full-screen class="full-screen-box"></full-screen>
    </div>
    <!-- @wheel.prevent 阻止该元素上的滚轮事件向上传播 -->
    <div @wheel.prevent class="echarts-box" v-ui-loading="{ loading: loading }">
      <div class="map" :id="mapId"></div>
      <map-dom ref="mapDom" :map-dom-data="mapDomData" class="mapDom" @close="closeAllInfoWindow"></map-dom>
      <right-button-group @pointMap="pointMap"> </right-button-group>
    </div>
  </div>
</template>
<script>
import dataAnalysis from '@/config/api/dataAnalysis.js';
import { NPGisMapMain } from '@/map/map.main';
import { mapGetters, mapActions } from 'vuex';
let mapMain = null;
let infoWindowArr = [];
export default {
  name: 'map-echart',
  components: {
    MapDom: require('./map-dom').default,
    RightButtonGroup: require('./right-button-group.vue').default,
    FullScreen: require('@/views/home/<USER>/full-screen.vue').default,
  },
  props: {
    activeInfluenceItem: {
      type: Object,
      default: () => {},
    },
    activeTabId: {
      type: String,
    },
  },
  data() {
    let _this = this;
    return {
      allCameraList: [],
      mapId: 'mapId' + Math.random(),
      mapDomData: {},
      loading: false,
      // 自定义 图标
      customMarker: {
        width: 33,
        height: 33,
        func(marker) {
          const unqualified = ['0001', '0010', '0100', '0110', '0011', '0101', '0111']; // 不合格类型
          let url = '';
          if (unqualified.includes(marker.ext.isNormal)) {
            url =
              _this.themeType === 'dark'
                ? require('@/assets/img/device-map/map-camera-abnormal.png')
                : require('@/assets/img/device-map/map-camera-abnormal-light.png');
          } else {
            url =
              _this.themeType === 'dark'
                ? require('@/assets/img/device-map/map-camera-normal.png')
                : require('@/assets/img/device-map/map-camera-normal-light.png');
          }
          return url;
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      mapConfig: 'common/getMapConfig',
      mapStyle: 'common/getMapStyle',
      isFullscreen: 'home/getFullscreen',
      themeType: 'common/getThemeType',
    }),
  },
  created() {},
  mounted() {
    this.getMapConfig();
  },
  watch: {
    activeInfluenceItem: {
      handler(val) {
        if (!val) return;
        this.getDeviceList();
      },
      deep: true,
      immediate: true,
    },
    allCameraList: {
      handler(val) {
        if (val.length > 0) {
          this._initSystemPoints2Map(this.allCameraList);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapActions({
      setMapConfig: 'common/setMapConfig',
      setMapStyle: 'common/setMapStyle',
    }),
    async getDeviceList() {
      try {
        this.loading = true;
        let { batchId } = this.$route.query;
        if (!batchId) return;
        let data = {
          batchId: batchId,
          customParameters: {
            inflKey: this.activeTabId,
            fieldKey: this.activeInfluenceItem.key,
          },
        };
        let res = await this.$http.post(dataAnalysis.getGraphScatterInfo, data);
        let arr = res.data.data.scatters || [];
        let resultArr = [];
        arr.forEach((item) => {
          if (!item) return;
          resultArr.push({ ...item, ...item.furtherMap, address: item.fieldValue });
        });
        this.allCameraList = resultArr || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig, this.mapStyle);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      // 配置初始化层级
      mapMain = new NPGisMapMain();
      let mapId = this.mapId;
      mapMain.init(mapId, data, style);
      if (this.allCameraList.length !== 0) {
        this._initSystemPoints2Map(this.allCameraList);
      }
    },
    // 加载点位到地图上
    _initSystemPoints2Map(points) {
      if (mapMain) {
        // 默认第一条设备在正中间
        let point = new NPMapLib.Geometry.Point(points[0].longitude, points[0].latitude);
        mapMain.map.centerAndZoom(point, 17);
        // 加载点位
        mapMain.renderMarkers(
          mapMain.convertSystemPointArr2MapPoint(points),
          this.getMapEvents(),
          false,
          this.customMarker,
        );
      }
    },
    getMapEvents() {
      let opts = {
        click: (marker) => {
          let point = {};
          point.deviceId = marker.ext.ObjectID;
          point.deviceName = marker.ext.Name;
          point.lat = marker.ext.Lat;
          point.lon = marker.ext.Lon;
          point.address = marker.ext.Address;
          this.$nextTick(() => {
            this.selectItem(point);
          });
        },
      };
      return opts;
    },
    closeAllInfoWindow() {
      infoWindowArr.forEach((row) => {
        row.close();
      });
    },

    selectItem(pointItem) {
      // 显示之前先清除其他提示框
      this.closeAllInfoWindow();
      let point = new NPMapLib.Geometry.Point(pointItem.lon, pointItem.lat);
      mapMain.map.centerAndZoom(point, 17);
      // 当弹框比较大的时候需要偏移地图展示完整的弹框
      mapMain.map.panByPixel(150, -80);
      let opts = {
        width: 50, // 信息窗宽度
        height: 100, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(-23, -185), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
        autoSize: true, // 默认true, 窗口大小是否自适应
        isAdaptation: true, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
      };
      let infoWindow = new NPMapLib.Symbols.InfoWindow(point, null, null, opts);
      this.mapDomData = pointItem;
      let dom = this.$refs.mapDom.$el;
      dom.style.display = 'block';
      infoWindow.setContentDom(dom);
      mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      infoWindowArr.push(infoWindow);
    },
    // 地图上的按钮
    pointMap(type) {
      this[`${type}Map`]();
    },
    // 固定放大地图
    enlargeMap() {
      mapMain.map.zoomInFixed();
    },
    // 固定缩小地图
    narrowMap() {
      mapMain.map.zoomOutFixed();
    },
  },
};
</script>
<style lang="less" scoped>
.map-echart-box {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  .influence-title {
    width: 100%;
    height: 46px;
    background: var(--bg-sub-echarts-title);
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    .title-rect {
      display: inline-block;
      width: 5px;
      height: 20px;
      margin-left: 20px;
      background: var(--bg-title-rect);
    }
    .title-span {
      width: fit-content;
      color: var(--color-sub-title-inpage);
    }
    .tip-color {
      color: var(--color-warning);
    }
    .tip-text {
      flex: 1;
    }
    @{_deep}.full-screen-box {
      position: inherit !important;
      margin: 0 20px;
      &.container .icon {
        color: #888888 !important;
        border: 1px solid #888888 !important;
      }
    }
    &.influence-title-fullscreen {
      height: 60px;
      font-size: 16px;
      .tip-text {
        font-size: 16px !important;
      }
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 50px) !important;
    .map {
      height: 100%;
      position: relative;
    }
    @{_deep}.button-group {
      right: 30;
      top: 50%;
      transform: translateY(-50%, -50%);
    }
  }
  @{_deep}.olMapViewport {
    background-color: #ffffff !important;
  }
}
</style>
