<template>
  <div>
    <span class="icon-font standard_icon" :class="[!isSuccess ? 'font-warning' : 'font-green', iconfontClass]">
      <i class="icon_text" :class="!isSuccess ? 'font-warning' : 'font-green'" :style="iconTextStyle">
        {{ !isSuccess ? failText : successText }}
      </i>
    </span>
  </div>
</template>
<script>
export default {
  props: {
    iconfontClass: {
      type: String,
      default: 'icon-zhiliangfen-line',
    },
    isSuccess: {
      type: Boolean,
      default: true,
    },
    successText: {
      type: String,
      default: '达标',
    },
    failText: {
      type: String,
      default: '不达标',
    },
    iconTextStyle: {},
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.standard_icon {
  vertical-align: middle;
  font-size: 120px;
  position: relative;
  .icon_text {
    font-size: 16px;
    position: absolute;
    right: 40%;
    top: 40%;
    transform: rotate(-32deg);
    font-weight: bold;
  }
  .icon_text_succeed {
    font-size: 16px;
    position: absolute;
    right: 44px;
    top: 10px;
    font-weight: bold;
    transform: rotate(-32deg);
  }
}
</style>
