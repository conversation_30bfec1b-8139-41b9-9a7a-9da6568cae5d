<template>
  <ui-modal v-model="visible" :styles="styles" title="比对详情" footerHide class="offline-result-compare">
    <div class="action">
      <Button type="primary" class="fr mb-sm" @click="handleUpdateResult" :loading="loading">
        <i class="icon-font icon-shoudongshuaxin"></i>
        <span class="inline vt-middle ml-xs">更新比对结果</span>
      </Button>
    </div>
    <div class="compare-container">
      <div class="left clear-b">
        <div class="left-header">
          <span class="tag">
            <span class="ml-xs">上级最新检测：{{ superiorStatisticsByDevice.examineTime }}</span>
          </span>
          <div class="pr-md">
            <icon-statics :icon-list="leftIconList" class="mt-md"> </icon-statics>
          </div>
          <div class="export-wrapper base-text-color">
            <div></div>
            <div></div>
          </div>
        </div>
        <div class="echarts-wrapper" v-ui-loading="{ loading: loading }">
          <online-details
            ref="superiorDetail"
            :get-echarts="() => getEcharts('superiorVideoPlatformOnlineDetail', true)"
            v-if="visible"
          >
          </online-details>
        </div>
      </div>
      <div class="right">
        <div class="right-header">
          <span class="tag">
            <span class="ml-xs">本级最新检测：{{ selfStatisticsByDevice.examineTime }}</span>
          </span>
          <icon-statics :icon-list="rightIconList" class="mt-md"> </icon-statics>
          <div class="export-wrapper">
            <div class="base-text-color">
              <span class="legend offline mr-md">离线</span>
              <span class="legend online mr-md">在线</span>
              <span class="legend reported mr-md">报备</span>
            </div>
            <div>
              <Button type="primary" class="ml-sm button-blue" @click="beforeHandleReviews(UNOPPOSE)">
                <i class="icon-font icon-quxiaofuhe f-16"></i>
                <span class="inline vt-middle ml-xs">取消复核</span>
              </Button>
              <Button type="primary" class="ml-sm button-blue" @click="beforeHandleReviews(OPPOSE)">
                <i class="icon-font icon-jieguofuhe delete-icon"></i>
                <span class="inline vt-middle ml-xs">结果复核</span>
              </Button>
            </div>
          </div>
        </div>
        <div class="echarts-wrapper" v-ui-loading="{ loading: loading }">
          <online-details
            ref="selfDetail"
            :get-echarts="() => getEcharts('selfVideoPlatformOnlineDetail', false)"
            v-if="visible"
          >
          </online-details>
        </div>
      </div>
    </div>
    <result-review
      v-model="resultReviewVisible"
      :active-item="activeItem"
      :data="superiorVideoPlatformOnlineDetail"
      :isOppose="oppose"
      :title="title"
      @on-success="handleOnSuccess"
    ></result-review>
  </ui-modal>
</template>
<script>
import { iconStaticsList } from '@/views/disposalfeedback/cascadelist/util/enum.js';
import deviceCompareMixin from '@/views/disposalfeedback/cascadelist/mixins/deviceCompareMixin.js';
import { mapGetters } from 'vuex';
import downLoadTips from '@/mixins/download-tips';
import { OPPOSE } from '../util/enum';

export default {
  name: 'offline-result-compare',
  components: {
    IconStatics: require('@/components/icon-statics.vue').default,
    OnlineDetails: require('@/views/disposalfeedback/cascadelist/components/online-details.vue').default,
    ResultReview: require('@/views/disposalfeedback/cascadelist/components/result-review.vue').default,
  },
  mixins: [deviceCompareMixin, downLoadTips],
  props: {
    value: {},
    activeItem: {},
  },
  data() {
    return {
      visible: false,
      onlineDetailShow: true,
      detail: {},
      styles: {
        width: '9.5rem',
      },
      leftTableLoading: false,
      exportLoading: false,
      resultReviewVisible: false,
      leftIconList: iconStaticsList,
      rightIconList: JSON.parse(JSON.stringify(iconStaticsList)),
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      oppose: null,
      title: '',
    };
  },
  watch: {
    async visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  created() {
    this.visible = true;
  },
  computed: {
    ...mapGetters({
      configData: 'governanceevaluation/configData',
    }),
  },
  methods: {
    async handleOnSuccess() {
      this.$refs.superiorDetail.setEcharts();
      this.$refs.selfDetail.setEcharts();
    },
    async handleUpdateResult() {
      this.pageData.pageNum = 1;
      await this.initList(true);
      this.setStatisticalQuantityList();
    },
    beforeHandleReviews(oppose) {
      if (oppose === OPPOSE) {
        this.title = '复核';
      } else {
        this.title = '取消复核';
      }
      this.oppose = oppose;
      this.resultReviewVisible = true;
    },
    /**
     *
     * @param videoPlatformOnlineDetail 本级或上级数据字典名称
     * @param showOppose 是否显示 异议 坐标轴
     * @returns {Promise<{yAxis: *, data: *, categoryData: *, state: string[], colors: string[]}>}
     */
    async getEcharts(videoPlatformOnlineDetail, showOppose) {
      if (!this.loading) {
        await this.initList(false);
        this.setStatisticalQuantityList();
      }
      try {
        const colors = ['#BC3C19', '#0E8F0E'];
        const state = ['离线', '在线'];
        const params = {
          showOppose,
          colors: colors,
          state: state,
          yAxis: this[videoPlatformOnlineDetail].map((row) => row.checkTimeD || ''),
          optionData: this.getOptionData(videoPlatformOnlineDetail),
          categoryData: this[videoPlatformOnlineDetail].map((row) => {
            return {
              value: row.duration || '',
              rowInfo: { ...row }, // 用于处理 其他业务逻辑
              textStyle: {
                /**
                 * 如果离线时间只有一条数据
                 * 则判断state： 2报备，1在线，0离线
                 * 如果有多条记录则显示离线时间
                 *  */
                color: () => {
                  if (row.timerAxis && row.timerAxis.length === 1) {
                    if (row.timerAxis[0].state === 2) {
                      return '#2B84E2';
                    } else if (row.timerAxis[0].state === 1) {
                      return '#0E8F0E';
                    } else {
                      return '#BC3C19';
                    }
                  } else {
                    return '#BC3C19';
                  }
                },
              },
            };
          }),
          data: this.dealData(colors, state, videoPlatformOnlineDetail),
        };
        return params;
      } catch (err) {
        console.log(err);
      }
    },
    /**
     *
     * @param colors 颜色
     * @param state 状态
     * @param videoPlatformOnlineDetail 本机或上级数据
     * @returns {[]}
     */
    dealData(colors, state, videoPlatformOnlineDetail) {
      let timeData = [];
      this[videoPlatformOnlineDetail].forEach((row, index) => {
        if (!row.timerAxis) return;
        row.timerAxis.forEach((rw) => {
          timeData.push({
            itemStyle: { color: colors[rw.state] },
            name: state[rw.state],
            /**
             * Echarts的x轴只能显示 xxxx-xx-xx xx:xx:xx
             * 这里y轴作为了日期所以年月只需要写死即可
             * 0,1,2代表y轴的索引，后两位代表x轴数据开始和结束
             *  */
            value: [index, `2020-01-01 ${rw.t1}`, `2020-01-01 ${rw.t2}`, rw.state === 0],
          });
        });
      });
      return timeData;
    },
    getOptionData(videoPlatformOnlineDetail) {
      let tempArr = [];
      this[videoPlatformOnlineDetail].forEach((row) => {
        let isOppose = row.oppose === OPPOSE;
        tempArr.push({
          value: isOppose ? '异议' : '',
          textStyle: {
            //改变刻度字体样式
            color: '#fff',
            align: 'center',
            backgroundColor: '#D66418',
            borderRadius: 4,
            height: 22,
            // width: 40,
            width: isOppose ? 40 : 0,
          },
        });
      });
      return tempArr;
    },
    setStatisticalQuantityList() {
      this.leftIconList.forEach((item, index) => {
        this.rightIconList[index]['count'] = this.selfStatisticsByDevice[item.fileName] || 0;
        this.leftIconList[index]['count'] = this.superiorStatisticsByDevice[item.fileName] || 0;
      });
    },
  },
};
</script>
<style scoped lang="less">
@{_deep} .ivu-modal-body {
  height: 730px;
  overflow: hidden;
}
.pr-md {
  padding-right: 15px;
}
.tag {
  position: relative;
  color: var(--color-primary);
  font-weight: bold;
  &::before {
    content: '';
    display: inline-block;
    position: relative;
    width: 5px;
    height: 16px;
    background: var(--color-primary);
    vertical-align: middle;
  }
}
.legend {
  position: relative;
  width: 50px;
  &::before {
    content: '';
    display: inline-block;
    position: relative;
    width: 16px;
    height: 16px;
    vertical-align: middle;
    margin-right: 5px;
  }
}

.offline {
  &::before {
    background: #bc3c19;
  }
}
.online {
  &::before {
    background: #0e8f0e;
  }
}
.reported {
  &::before {
    background: var(--color-primary);
  }
}

.export-wrapper {
  height: 30px;
  margin: 20px 0 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.action {
  height: 34px;
}

.compare-container {
  position: relative;
  height: calc(100% - 34px - 20px);
  width: 100%;
  display: flex;

  @{_deep} .icon-ul {
    width: 100%;
    background: var(--bg-card-1);
    height: 69px;
  }
  .left {
    flex: 1;
    .left-header {
      .export-wrapper {
        padding-right: 15px;
        border-right: 1px solid var(--border-color);
      }
    }
    .echarts-wrapper {
      padding-right: 15px;
      border-right: 1px solid var(--border-color);
    }
  }
  .right {
    flex: 1;
    padding-left: 15px;
    .right-header {
    }
    .echarts-wrapper {
    }
  }
}
</style>
