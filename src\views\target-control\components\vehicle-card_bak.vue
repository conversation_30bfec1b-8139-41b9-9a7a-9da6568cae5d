<template>
  <div class="alarm" :class="'border'+alarmInfo.bgIndex">
    <div class="top" :class="'bg'+alarmInfo.bgIndex">
      <div>
        <ui-btn-tip class="collection-icon" content="取消收藏" icon="icon-jingqing" />
        <span v-if="alarmInfo.taskLevel == 1">一级警报</span>
        <span v-else-if="alarmInfo.taskLevel == 2">二级警报</span>
        <span v-else>三级警报</span>
      </div>
      <i class="ivu-icon ivu-icon-ios-close ivu-tabs-close" @click.stop="$emit('delAlarm')"></i>
    </div>
    <!-- <div class="top" :class="'bg'+alarmInfo.bgIndex">
      <div class="level-title">
        <img v-if="alarmInfo.bgIndex == 1" class="level" src="@/assets/img/target/title1.png" />
        <img v-if="alarmInfo.bgIndex == 2" class="level" src="@/assets/img/target/title2.png" />
        <img v-if="alarmInfo.bgIndex == 3" class="level" src="@/assets/img/target/title3.png" />
        <img v-if="alarmInfo.bgIndex == 4" class="level" src="@/assets/img/target/title4.png" />
        <img v-if="alarmInfo.bgIndex == 5" class="level" src="@/assets/img/target/title5.png" />
        <div class="num">
          {{ 
            alarmInfo.taskLevel == 1 ? '一级' :
            alarmInfo.taskLevel == 2 ? '二级' : '三级'
          }}
        </div>
      </div>
      <div>
        <ui-btn-tip v-if="alarmInfo.myFavorite == 1" class="collection-icon" content="取消收藏" icon="icon-yishoucang" transfer @click.stop.native="collection(2)" />
        <ui-btn-tip v-else class="collection-icon"  content="收藏" icon="icon-shoucang" transfer @click.stop.native="collection(1)" />
      </div>
    </div> -->
    <div class="info">
      <div class="left">
        <div class="vehicle">
          <ui-image :src="alarmInfo.traitImg"></ui-image>
        </div>
        <div class="license-plate-small">{{ alarmInfo.plateNo }}</div>
      </div>
      <div class="right">
        <div class="p">
          <div class="title">布控来源:</div>
          <div class="val">
            {{ alarmInfo.taskType == '1' ? '单体布控' : '库布控' }}
            {{ alarmInfo.taskType == '1' ? '' : '（'+alarmInfo.libName+'）' }}
          </div>
        </div>
        <div class="p">
          <div class="title">车主姓名:</div>
          <div class="val">
            {{ alarmInfo.name || '未知' }}
          </div>
        </div>
        
        <div class="p">
          <div class="title">报警时间:</div>
          <div class="val">{{ alarmInfo.alarmTime }}</div>
        </div>
        <div class="p">
          <div class="title">报警设备:</div>
          <div class="val">{{ alarmInfo.deviceName }}</div>
        </div>
        <div class="p">
          <div class="title">所属任务:</div>
          <div class="val">{{ alarmInfo.taskName }}</div>
        </div>
      </div>
    </div>
    <!-- <div class="status">
      <ui-image v-if="alarmInfo.operationType == 1" class="img" :src="valid"></ui-image>
      <ui-image v-if="alarmInfo.operationType == 2" class="img" :src="invalid"></ui-image>
      <ui-image v-if="alarmInfo.operationType == 0" class="img" :src="unproces"></ui-image>

    </div> -->
    <!-- <div class="btn">
      <div v-if="[0, 2].includes(alarmInfo.operationType)" @click.stop="configStatus(1)">设为有效<span></span></div>
      <div v-if="[0, 1].includes(alarmInfo.operationType)" @click.stop="configStatus(2)">设为无效<span></span></div>
      <div @click.stop="()=>{}" v-if="alarmInfo.operationType != 0">
        <Poptip trigger="hover" transfer word-wrap @on-popper-show="showHistory()">
          历史处理
          <div slot="title"><div class="block"></div><i>历史处理</i></div>
          <div slot="content">
            <Timeline>
              <TimelineItem v-for="item in historyList" :key="item">
                  <div class="time">
                    <div class="timeContent">
                      <div>{{ item.handleTime }}</div>
                      <div>操作人：{{ item.creatorName }}</div>
                    </div>
                  </div>
                  <p class="content">
                    <div class="content1">
                      <div class="p">
                        <span>处理操作：</span>
                        <div>设为 <span v-if="item.operation == 1">"有效"</span><span v-else>"无效"</span></div>
                      </div>
                      <div class="p">
                        <span>处理意见：</span>
                        <div>{{ item.remark || '--' }}</div>
                      </div>
                    </div>
                  </p>
              </TimelineItem>
          </Timeline>
          </div>
        </Poptip>
      </div>
    </div> -->
  </div>
</template>
<script>
import { addCollection, deleteMyFavorite } from '@/api/user'
  import {batchHandleVehicle, queryVehicleAlarmHandleList} from '@/api/target-control'
import round from '@/assets/img/target/round.png'
import valid from '@/assets/img/target/valid.png'
import c1 from '@/assets/img/target/c-one.png'
import c2 from '@/assets/img/target/c-two.png'
import c3 from '@/assets/img/target/c-three.png'
import c4 from '@/assets/img/target/c-four.png'
import c5 from '@/assets/img/target/c-five.png'
import invalid from '@/assets/img/target/invalid.png'
import unproces from '@/assets/img/target/unproces.png'
  export default {
    props: {
      alarmInfo: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        single: false,
        c1, c2, c3, round, valid, invalid, unproces, c4, c5,
        historyList: []
      }
    },
    computed: {},
    activated() {},
    mounted() {},
    methods: {
      collection(num) {
        var param = {
          favoriteObjectId: this.alarmInfo.alarmTopId,
          favoriteObjectType: 14,
        }
        if(num == 1){

          addCollection(param).then(res => {
            this.$Message.success("收藏成功");
            this.$emit('collection')
          })
        }else{
          deleteMyFavorite(param).then(res => {
            this.$Message.success("取消收藏成功");
            this.$emit('collection')
          })
        }
      },
      cardBox (e) {
        this.$emit('singleChecked', e)
      },
      /**
       * 修改状态
       * @param {*} val 
       */
      configStatus(val) {
        var param = {
          alarmRecordSimpleForms: [{alarmTime: this.alarmInfo.alarmTime, alarmId: this.alarmInfo.alarmId}],
          operationType: val
        }
        batchHandleVehicle(param).then(res => {
          this.$Message.success(res.data)
          this.$emit('refresh')
        })
      },
      /**
       * 处理历史
       */
      showHistory() {
        var param = {
          alarmTime: this.alarmInfo.alarmTime, 
          alarmId: this.alarmInfo.alarmId
        }
        queryVehicleAlarmHandleList(param).then(res => {
          this.historyList = res.data
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .alarm {
    // position: relative;
    // width: 340px;
    // height: 190px;
    width: 380px;
    height: 240px;
    box-shadow: 0 1px 3px #d9d9d9;
    position: fixed;
    z-index: 9;
    right: 14px;
    bottom: 14px;
    overflow: hidden;
    border-radius: 3px;
    background: #fff;
    border: 1px solid #ededed;
    .top {
      position: relative;
      display: flex;
      justify-content: space-between;
      padding: 0 0 0 20px;
      height: 40px;
      line-height: 40px;
      margin-bottom: 12px;
      color: #fff;
      .level {
        position: absolute;
        left: 50%;
        margin-left: -46px;
      }
      .level-title {
        width: 98px;
      }
      .num {
        position: absolute;
        width: 88px;
        text-align: center;
        color: #fff;
      }
    }

    .bg1{
      background: linear-gradient(210deg, #FF7B56 0%, #EA4A36 100%);
    }
    .bg2{
      background: linear-gradient(210deg, #FFAF65 0%, #FC770B 100%);
    }
    .bg3{
      background: linear-gradient(210deg, #FFD752 0%, #FFC300 100%);
    }
    .bg4{
      background: linear-gradient(262deg, #27D676 8%, #36BE7F 89%);
    }
    .bg5{
      background: linear-gradient(263deg, #5BCAFF 2%, #2C86F8 97%);
    }

    .contrast {
      height: 100px;
      display: flex;
      justify-content: space-between;
      padding: 0 10px;
      .block {
        position: relative;
        width: 100px;
        .animation {
          /deep/ .ui-image-div {
            border: 0;
            background: transparent;
          }
        }
        .desc {
          position: absolute;
          z-index: 9;
          background: rgba(0,0,0,0.5);
          color: #fff;
          padding: 0 6px;
        }
        .num {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          align-items: center;
          display: flex;
          justify-content: center;
          color: #2c86f8;
        }
        .c1{
          color: #EA4A36;
        }
        .c2{
          color: #E77811;
        }
        .c3{
          color: #EE9F00;
        }
        .c4{
          color: #36BE7F;
        }
        .c5{
          color: #2C86F8;
        }
      }
      .border {
        border: 1px solid #ebebeb;
      }
    }

    .info {
      display: flex;
      padding: 12px;
      .left {
        flex-direction: column;
        .vehicle {
          width: 90px;
          height: 90px;
        }
        .license-plate-small {
          margin-top: 6px;
        }
        width: 92px;
        flex-shrink:0;
        display: flex;
        justify-content: center;
        align-items: center;
        /deep/ .ui-image-div{
            border: none;
        }
        .img {
          width: 92px;
          height: 88px;
        }
      }
      .right {
        flex: 1;
        width: 0;
        z-index: 10;
        .p {
          display: flex;
          height: 26px;
          .title {
            color: #999;
            margin-right: 10px;
            line-height: 22px;
          }
          .val {
            flex: 1;
            overflow: hidden;    
            text-overflow:ellipsis;    
            white-space: nowrap;
          }
        }
      }
    }
    .status {
      position: absolute;
      right: 0;
      bottom: 0;
    }

    .btn {
      position: absolute;
      width: 100%;
      background: #2c86f8;
      color: #fff;
      display: flex;
      text-align: center;
      justify-content: center;
      height: 30px;
      line-height: 30px;
      bottom: -30px;
      transition: 0.3s;
      cursor: pointer;
      div {
        position: relative;
        flex: 1;
        span {
          display: inline-block;
          width: 2px;
          height: 20px;
          border-right: 1px solid #d1cbcb;
          position: absolute;
          right: 1px;
          top: 5px;
        }
      }
    }

    &:hover {
      border: 1px solid #2c86f8;
      .btn {
        bottom: 0;
        transition: 0.3s;
      }
    }
  }

  .border1 {
    border: 1px solid #EA4A36;
  }
  .border2 {
    border: 1px solid #FC770B;
  }
  .border3 {
    border: 1px solid #FFC300;
  }
  .border4 {
    border: 1px solid #36BE7F;
  }
  .border5 {
    border: 1px solid #2C86F8;
  }

  .collection-icon {
    /deep/ .iconfont {
        font-size: 14px;
        color: #fff;
    }
    .ivu-tooltip-rel {
        // margin-top: 3px;
    }
    /deep/ .icon-shoucang {
        color: #f29f4c !important;
        text-shadow: 0px 1px 0px #e1e1e1;
    }
    /deep/ .icon-yishoucang {
        color: #f29f4c !important;
    }
}

</style>
<style lang="less">
  .ivu-poptip-popper {
    width: 450px !important;
    .block {
      width: 3px;
      background: #2c86f8;
      height: 16px;
      float: left;
      margin-top: 3px;
      margin-right: 6px;
    }
  }

  .ivu-timeline-item {
    .timeContent {
      display: flex;
      justify-content: space-between;
    }
    .content1 {
      .p {
        display: flex;
        margin-top: 10px;
        span {
          width: 80px;
        }
        div {
          flex: 1;
        }
      }
    }
  }

  .ivu-poptip-body {
    height: 300px;
    overflow: auto;

    .ivu-timeline-item-head {
      background-color: #e3dada;
      border: 0;
    }

    .ivu-timeline {
      .ivu-timeline-item {
        .ivu-timeline-item-tail {
          left: 10px;
        }

        .ivu-timeline-item-head {
          left: 4px;
        }
        &:first-child {
          .ivu-timeline-item-head {
            background-color: #2d8cf0;
            left: 4px;
          }
          .ivu-timeline-item-head::after {
            content: '';
            position: absolute;
            top: -3px;
            right: -3px;
            width: 19px;
            height: 19px;
            border: 1px solid #2d8cf0 !important;
            border-radius: 50px;
            z-index: 999;
          }
        }
      }
    }
  }

/deep/ .ivu-checkbox-input {
  z-index: 999;
}


</style>