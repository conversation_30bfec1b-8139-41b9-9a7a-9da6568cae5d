<template>
	<div class="main-container">
		<div class="search-bar">
			<div class="btn-group">
				<Button type="default" @click="removeMore"><i class="iconfont icon-shanchu"></i>批量删除</Button>
				<Button type="default" @click="editMore"><i class="iconfont icon-bianji"></i>批量审批</Button>
				<!-- <Button type="default" icon="md-checkmark">标记已读</Button> -->
			</div>
			<search ref="search" @searchInfo="searchInfo"></search>
		</div>
		<div class="table-container">
			<div class="table-content">
				<ui-table :columns="columns" :data="tableList" @on-selection-change="selectionChangeHandle">
					<template #applyContent="{ row }">
						<div>{{ row.applyContent }}
							<span>{{ row.orgName }}</span>
							<span>——</span>
							<span>{{ row.userName }}申请</span>
							<span>{{ row.deviceName }}</span>
							<span>{{ row.startTime }}至</span>
							<span>{{ row.endTime }}的录像。</span>
							<span> 申请理由：{{ row.applyReason  }}</span>
						</div>
					</template>
					<template #handleTime="{ row }">
						<div>{{ row.handleTime==null?'':row.handleTime }}</div>
					</template>
					<template #status="{ row }">
						<div>{{ row.status==1?'同意':row.status==2?'驳回':'未处理' }}</div>
					</template>
					<template #handleReason="{ row }">
						<div>{{ row.handleReason }}</div>
					</template>
					<template #action="{ row }">
						<div class="btn-tips">
							<ui-btn-tip :content="row.status==0 ? '审批' : '详情'" :icon="row.status==0 ? 'icon-bianji' : 'icon-xiangqing'" class="mr-20 primary" @click.native="openDetail(row)"></ui-btn-tip>
							<ui-btn-tip v-if="row.status!=0" content="删除" icon="icon-shanchu" class="mr-20 primary" @click.native="remove(row)"></ui-btn-tip>
						</div>
					</template>
				</ui-table>
			</div>
			<!-- <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty> -->
			<ui-loading v-if="loading"></ui-loading>
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[20, 40, 60]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
		</div>
		<applyDetail ref="detailModal" @comfirmHandle="queryList"/>
	</div>
</template>
<script>
	import search from './components/search'
	import applyDetail from './components/apply-detail'
	import { applyPageList, applyDelete, applyUpdate } from '@/api/videoApply.js'

	export default {
		name: 'mydownload',
		components: {
			search,
			applyDetail
		},
		data() {
			return {
				queryParam: {
				},
				tableList: [],
				columns: [
					{ type: 'selection', width: 70, align: 'center' },
					{ title: '消息内容', slot: 'applyContent', },
					{ title: '申请时间', key: 'applyTime', width: 200 },
					{ title: '审核时间', slot: 'handleTime', width: 200 },
					{ title: '状态', slot: 'status', width: 150 },
					{ title: '审批描述', slot: 'handleReason', width: 200 },
					{ title: '操作', slot: 'action', width: 100 }

				],
				pageInfo: {
					pageNumber: 1,
					pageSize: 20,
				},
				total: 0,
				loading: false,
				selectionList: [],
				result: '1',
				applyId: ''
			}
		},
		mounted() {
			this.queryList()
		},
		watch: {
			'$route': {
				handler(val) {
					if (val.name == 'mydownload') {
						this.queryList()
					}
				},
				immediate: true
			}
		},
		methods: {
			//查询服务器列表
			queryList() {
				this.loading = true;
				this.tableList = []
				applyPageList({ ...this.queryParam, ...this.pageInfo })
					.then(res => {
						const { total, entities } = res.data
						this.total = total
						this.tableList = entities
					})
					.catch(err => {
						console.error(err)
					})
					.finally(() => {
						this.loading = false;
					})
			},
			searchInfo(obj) {
				this.pageInfo.pageNumber = 1
				this.queryParam = obj
				this.queryList()
			},
			// 页数改变
			pageChange(size) {
				this.pageInfo.pageNumber = size
				this.queryList()
			},
			// 页数量改变
			pageSizeChange(size) {
				this.pageInfo.pageNumber = 1
				this.pageInfo.pageSize = size
				this.queryList()
			},
			selectionChangeHandle(val) {
				this.selectionList = val
			},
			// 删除
			remove(row) {
				this.$Modal.confirm({
					title: '提示',
					closable: true,
					content: `确定删除吗？`,
					onOk: () => {
						applyDelete(row.id).then(res => {
							this.queryList()
							this.$Message.success(res.msg)
						})
					}
				})
			},
			removeMore() {
				if (this.selectionList.length > 0) {
					let ids = []
					let flag = this.selectionList.some(v => v.status == 0)
					if (flag) {
						this.$Message.warning("未处理的消息不能删除！");
						return
					}
					this.selectionList.forEach(item => {
						ids.push(item.id)
					})
					this.$Modal.confirm({
						title: '提示',
						closable: true,
						content: `确定删除吗？`,
						onOk: () => {
							applyDelete(ids.toString()).then(res => {
								this.queryList()
								this.$Message.success(res.msg)
							})
						}
					})
				} else {
					this.$Message.warning("请选择要删除的消息！");
				}
			},
			editMore() {
				if (this.selectionList.length > 0) {
					let ids = []
					let flag = this.selectionList.some(v => v.status != 0)
					if (flag) {
						this.$Message.warning("已审批消息不能再次审批！");
						return
					}
					this.selectionList.forEach(item => {
						ids.push(item.id)
					})
					this.$Modal.confirm({
						title: '提示',
						closable: true,
						content: `确定批量审核通过吗？`,
						onOk: () => {
							applyUpdate({ ids, status: '1', handleReason:'审核通过' }).then(res => {
								this.queryList()
								this.$Message.success(res.msg)
							})
						}
					})
				} else {
					this.$Message.warning("请选择要审批的消息！");
				}
			},
			openDetail(row) {
				this.$refs.detailModal.show(row, row.status==0)
			}
		}
	}
</script>
<style lang="less" scoped>
	.search-bar {
		display: flex;
		.btn-group {
			border-bottom: 1px solid #d3d7de;
			margin-bottom: 16px;
		}
	}
	.main-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		width: calc(100% - 50px);
		background: #fff;
		padding: 20px 20px 0 20px;
		border-radius: 4px;
		box-shadow: 0 2px 3px 0 rgba(147, 171, 206, 0.7);

		.table-container {
			display: flex;
			flex-direction: column;
			flex: 1;
			overflow: auto;
			position: relative;
			.table-content {
				overflow: auto;
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				justify-content: start;
				// justify-content: space-between;
				align-content: flex-start;
				.ui-table {
					height: 100%;
				}
			}
		}
	}
	.color_f9 {
		color: rgba(0, 0, 0, 0.5);
	}
	.content {
		margin-top: 20px;
	}
</style>