import { queryDeviceList } from "@/api/player";
export default {
  data() {
    return {
      currentInsoectNode: [],
      insoectNodes: [],
    };
  },
  computed: {
    checkedIndexs() {
      let arr = [];
      this.insoectNodes.forEach((i) => {
        arr = [...arr, ...i.checkedIndexs];
      });
      return arr;
    },
  },
  beforeDestroy() {
    this.insoectNodes.forEach((v) => {
      clearInterval(v.timer);
      v.timer = null;
    });
    this.$emit("closeAll");
  },
  methods: {
    startInspect(node) {
      this.cancleInspect();
      this.currentInsoectNode = node;
      this.$emit("inspecting");
    },
    parseInspect(node) {
      let item = this.insoectNodes.find((v) => v.node.id == node.id);
      item.parse = !item.parse;
    },
    stopInspect(node) {
      let item = this.insoectNodes.find((v) => v.node.id == node.id);
      let index = this.insoectNodes.findIndex((v) => v.node.id == node.id);
      clearInterval(item.timer);
      this.insoectNodes.splice(index, 1);
      this.$emit("closeAll");
    },
    cancleInspect() {
      this.insoectNodes.forEach((v) => {
        clearInterval(v.timer);
        v.timer = null;
      });
      this.insoectNodes = [];
      this.$emit("closeAll");
    },
    async inspectStart(options) {
      let _this = this;
      let devicesList = await this.getDevicesByNode(this.currentInsoectNode);
      let item = {
        node: this.currentInsoectNode,
        children: devicesList,
        timer: null,
        parse: false,
        currentIndex: 0,
      };
      const timerFn = function () {
        if (!item.parse) {
          let checkedIndexs = options.checkedIndexs;
          let devices = item.children
            .slice(item.currentIndex, item.currentIndex + checkedIndexs.length)
            .map((v) => {
              let obj = { ...v };
              return { ...obj, devicetype: liveType, playType: "live" };
            });
          item.currentIndex += checkedIndexs.length;
          // 超出后从头开始
          if (item.currentIndex >= item.children.length) {
            item.currentIndex = item.currentIndex - item.children.length;
            let deviceAdd = item.children
              .slice(0, item.currentIndex)
              .map((v) => {
                let obj = { ...v };
                return { ...obj, devicetype: liveType, playType: "live" };
              });
            devices = [...devices, ...deviceAdd];
          }
          _this.$emit("inspectPlay", { devices, indexs: checkedIndexs });
          console.log({ devices, indexs: checkedIndexs });
        }
      };
      timerFn();
      item.timer = setInterval(timerFn, options.inspectTime * 1000);
      item.checkedIndexs = options.checkedIndexs;
      this.insoectNodes.push(item);
      this.currentInsoectNode = null;
    },
    getDevicesByNode(node) {
      return new Promise((resolve) => {
        let flag = node.children && node.children.every((v) => v.isLeaf);
        if (flag) {
          resolve(node.children);
        } else {
          queryDeviceList({
            orgCodes: [node.orgCode],
            deviceType: 1,
            pageNumber: 1,
            pageSize: 999999,
          }).then((res) => {
            resolve(res.data.entities);
          });
        }
      });
    },
  },
};
