const isImportantDict = [
  { value: '0', label: '普通设备' },
  { value: '1', label: '重点设备' },
];

export const formItemData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
    width: 140,
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
    width: 140,
  },
  {
    type: 'select',
    key: 'isImportant',
    label: '设备重点类型',
    placeholder: '请选择设备重点类型',
    options: isImportantDict,
    width: 165,
  },
  {
    type: 'start-end-num',
    label: '累计离线天数',
    startKey: 'offerTotalMin',
    endKey: 'offerTotalMax',
  },
  {
    type: 'start-end-num',
    label: '最大连续离线天数',
    startKey: 'serialOffMin',
    endKey: 'serialOffMax',
  },
];

export const tableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '设备编码',
    key: 'deviceId',
    align: 'left',
    tooltip: true,
    // sortable: 'custom',
    minWidth: 200,
  },
  {
    title: '设备名称',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    tooltip: true,
    minWidth: 200,
  },
  {
    title: '设备状态',
    slot: 'phyStatus',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '本月累计离线天数',
    slot: 'offLineTotalOfM',
    tooltip: true,
    // sortable: 'custom',
    minWidth: 120,
  },
  {
    title: '本月最大连续离线天数',
    slot: 'serialOffLineOfM',
    tooltip: true,
    // sortable: 'custom',
    minWidth: 120,
  },
  {
    title: '设备标签',
    slot: 'tagNames',
    tooltip: true,
    width: 200,
  },
];
