// 水印指令
import store from '@/store'
export default function (Vue) {
  Vue.directive('watermark', (el, binding) => {
    function addWaterMarker (str, parentNode, font, textColor) {// 水印文字，父元素，字体，文字颜色
      var can = document.createElement('canvas');
      parentNode.appendChild(can);
      can.width = 300;
      can.height = 160;
      can.style.display = 'none';
      var cans = can.getContext('2d');
      cans.rotate(-20 * Math.PI / 180);
      cans.font = font || "16px Microsoft JhengHei";
      cans.fillStyle = textColor || "rgba(180, 180, 180, 0.3)";
      cans.textAlign = 'left';
      cans.textBaseline = 'Middle';
      const name = store.getters.userInfo.name
      cans.fillText(str || name, can.width / 3, can.height / 2);
      parentNode.style.backgroundImage = "url(" + can.toDataURL("image/png") + ")";
    }
    if (binding.value) {
      addWaterMarker(binding.value.text, el, binding.value.font, binding.value.textColor)
    } else {
      addWaterMarker('', el, '', '')
    }
  })
}
