export default function (Vue) {
  // 传进来的value为要减去的下面的值
  Vue.directive('dragParent', {
    bind (el, bind, vnode) {
      const dom = el
      // dom.style.userSelect = 'none';
      dom.ondrop = (e) => {
        e.stopPropagation()
        e.preventDefault()
        const data = e.dataTransfer.getData('Text')
        dom.parentNode.appendChild(document.getElementById(data))
        dom.style.backgroundColor = 'transparent'
      }
      dom.ondragover = (e) => {
        e.preventDefault()
      }
      dom.ondragend = (e) => {
        dom.style.backgroundColor = 'transparent'
      }
      dom.ondragleave = () => {
        dom.style.backgroundColor = 'transparent'
      }
      dom.ondragenter = (e) => {
        dom.style.backgroundColor = 'blue'
      }
    },
    inserted (el, bind, vnode) {

    },
    update (el, bind, vnode) {

    },
    unbind (el, bind, vnode) {

    },
    runs (el, bind) {
    }
  })
}
