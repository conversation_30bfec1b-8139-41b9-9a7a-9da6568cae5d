<template>
  <div class="echart-wrap">
    <div v-show="title.show" class="echart-text title-color">
      {{ title.text }}
    </div>
    <div v-show="title.show" class="echart-subtext auxiliary-color">
      {{ title.subtext }}
    </div>
    <div ref="echart" class="echart-content"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  props: {
    title: {
      type: Object,
      default: () => {
        return {
          show: true,
          text: "",
          subtext: "单位：抓拍次数",
          textStyle: {
            color: "rgba(0, 0, 0, 0.9)",
            fontSize: 14,
            fontFamily: "MicrosoftYaHei-Bold",
            fontWeight: "bold",
          },
          subtextStyle: {
            color: "rgba(0, 0, 0, 0.35)",
            fontSize: 12,
            fontFamily: "MicrosoftYaHei",
          },
          textAlign: "left",
        };
      },
    },
    legend: {
      type: Object,
      default: () => {
        return {
          type: "scroll",
          show: false,
          right: "0%",
          itemGap: 24,
          itemWidth: 8,
          itemHeight: 8,
          icon: "rect",
          textStyle: {
            color: "rgba(0, 0, 0, 0.35)",
            // lineHeight: 18,
            padding: [0, 0, 0, 3],
          },
        };
      },
    },
    grid: {
      type: Object,
      default: () => {
        return {
          left: "2%",
          top: "10%",
          right: "0.1%",
          bottom: "0%",
          containLabel: true,
        };
      },
    },
    xAxis: {
      type: Object,
      default: () => {
        return {
          type: "category",
          data: [
            1, 2, 3, 4, 5, 6, 7, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16,
            17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
          ],
          axisLine: {
            lineStyle: {
              color: "#D3D7DE",
            },
          },
          boundaryGap: true,
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "rgba(0, 0, 0, 0.35)",
          },
        };
      },
    },
    yAxis: {
      type: Object,
      default: () => {
        return {
          name: "单位：抓拍次数",
          axisLine: {
            show: true,
            lineStyle: {
              color: "#D3D7DE",
            },
          },
          axisLabel: {
            color: "rgba(0, 0, 0, 0.35)",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: "dashed",
              color: "#D3D7DE",
            },
          },
        };
      },
    },
    tooltip: {
      type: Object,
      default: () => {
        return {
          trigger: "axis",
        };
      },
    },
    series: {
      type: Array,
      default: () => {
        return [
          {
            type: "line",
            data: [
              30, 35, 38, 39, 50, 61, 70, 75, 78, 79, 50, 52, 55, 56, 58, 65,
              66, 67, 68, 69, 70, 72,
            ],
            symbol: "circle", // 默认是空心圆（中间是白色的），改成实心圆
            showAllSymbol: true,
            symbolSize: 0,
            smooth: true,
            lineStyle: {
              width: 2,
              color: "#2C86F8", // 线条颜色
            },
            areaStyle: {
              //区域填充样式
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(44, 134, 248, 0.2)",
                  },
                  {
                    offset: 1,
                    color: "rgba(91, 163, 255, 0)",
                  },
                ],
                false
              ),
            },
          },
        ];
      },
    },
    theme: {
      type: String,
      default: "light",
    },
  },
  data() {
    return {
      myEchart: null,
    };
  },
  mounted() {
    this.init();
  },
  deactivated() {
    this.removeResizeFun();
  },
  beforeDestroy() {
    this.removeResizeFun();
  },
  watch: {
    series: {
      deep: true,
      handler(val) {
        this.init();
      },
    },
  },
  methods: {
    init() {
      this.myEchart = echarts.init(this.$refs.echart, this.theme);
      let option = {
        // title: this.title,
        legend: this.legend,
        grid: this.grid,
        xAxis: this.xAxis,
        yAxis: this.yAxis,
        tooltip: this.tooltip,
        series: this.series,
      };
      this.myEchart.setOption(option);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
    removeResizeFun() {
      window.removeEventListener("resize", () => this.myEchart.resize());
    },
    resizeEchart() {
      this.removeResizeFun();
      this.init();
    },
  },
};
</script>
<style lang="less" scoped>
.echart-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .echart-text {
    font-size: 14px;
    font-family: "MicrosoftYaHei-Bold";
    font-weight: bold;
    line-height: 20px;
    text-align: center;
  }
  .echart-subtext {
    margin-top: 8px;
    font-size: 12px;
    line-height: 18px;
  }
  .echart-content {
    flex: 1;
  }
}
</style>
