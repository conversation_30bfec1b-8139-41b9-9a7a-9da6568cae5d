<template>
  <div class="search">
    <Form ref="form" :model="queryParam" class="form" inline>
      <FormItem label="场所名称:" prop="name">
        <Input
          clearable
          v-model="queryParam.name"
          maxlength="50"
          placeholder="请输入"
        />
      </FormItem>
      <FormItem label="一级分类:" prop="firstLevel">
        <Select
          v-model="queryParam.firstLevel"
          clearable
          @on-change="changeFirstLevelPlace('secondLevelList')"
        >
          <Option
            v-for="item in placeFirstLevelList"
            :value="item.typeCode"
            :key="item.typeCode"
            >{{ item.typeName }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="二级分类:" prop="secondLevelList">
        <Select
          v-model="queryParam.secondLevelList"
          clearable
          multiple
          :max-tag-count="1"
        >
          <Option
            v-for="item in secondLevelPlaceInFirst"
            :value="item.typeCode"
            :key="item.typeCode"
            >{{ item.typeName }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="场所地址:" prop="address">
        <Input
          clearable
          v-model="queryParam.address"
          maxlength="50"
          placeholder="请输入"
        />
      </FormItem>
      <br />
      <FormItem label="所属区县:" prop="adcode">
        <Select v-model="queryParam.adcode" clearable @on-change="changeCity">
          <Option
            v-for="item in cityList"
            :value="item.regionCode"
            :key="item.regionCode"
            >{{ item.regionName }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="所属街道:" prop="towncode">
        <Select
          v-model="queryParam.towncode"
          clearable
          multiple
          :max-tag-count="1"
        >
          <Option
            v-for="item in streetList"
            :value="item.regionCode"
            :key="item.regionCode"
            >{{ item.regionName }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="标  签:" prop="">
        <div class="select-tag-button" @click="selectLabelHandle">
          请选择标签{{
            queryParam.labelIds.length
              ? `/已选(${queryParam.labelIds.length})`
              : ""
          }}
        </div>
      </FormItem>
      <FormItem class="nullLabel" prop="">
        <Select v-model="queryParam.labelType" style="width: 80px" transfer>
          <Option value="2">并集</Option>
          <Option value="1">交集</Option>
        </Select>
      </FormItem>
      <FormItem class="btn-group">
        <Button type="primary" @click="search">查询</Button>
        <Button @click="reset">重置</Button>
      </FormItem>
    </Form>
    <!-- 选择标签，场所为5 -->
    <LabelModal
      :labelType="5"
      @setCheckedLabel="setCheckedLabel"
      ref="labelModal"
    />
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import LabelModal from "@/views/holographic-archives/components/relationship-map/label-add";
import { getCityByParentCodeAPI } from "@/api/placeArchive";
import { getPlaceTypeList } from "@/api/placeArchive.js";
export default {
  name: "Search",
  components: {
    LabelModal,
  },
  data() {
    return {
      // 查询参数
      queryParam: {
        name: "", // 场所名称
        firstLevel: "", // 一级类
        secondLevelList: [], // 二级类，多选，
        address: "", // 场所地址
        adcode: "", // 所属区县
        towncode: [], // 所属街道，多选
        labelIds: [], // 标签
        labelType: "2", // 标签方式，1：交集，2：并集
      },
      selectedLabels: [], // 已选择的标签，回显用
      cityList: [], // 区县列表
      streetList: [], // 街道列表
      placeFirstLevelList: [],
      placeLevelAll: [],
    };
  },
  computed: {
    // 一级场所下的二级场所
    secondLevelPlaceInFirst() {
      if (this.queryParam.firstLevel) {
        let firstLevel = this.placeFirstLevelList.find(
          (item) => item.typeCode == this.queryParam.firstLevel
        );
        if (!firstLevel) {
          return [];
        }
        let arr = this.placeLevelAll.filter(
          (item) => item.parentId == firstLevel.id
        );
        return arr;
      }
      return [];
    },
  },
  async created() {
    this.initPlaceTypeList();
    this.getCityList();
  },
  methods: {
    async initPlaceTypeList() {
      let { data } = await getPlaceTypeList({});
      this.placeLevelAll = data;
      this.placeFirstLevelList = data.filter((item) => item.parentId == -1);
    },
    /**
     * @description: 改变一级分类，清空已选择的二级分类
     */
    changeFirstLevelPlace() {
      if (this.queryParam.secondLevelList.length) {
        this.queryParam.secondLevelList = [];
      }
    },

    /**
     * @description: 获取区县
     */
    getCityList() {
      // 不需要参数，后端已配置
      getCityByParentCodeAPI().then((res) => {
        this.cityList = res.data || [];
      });
    },

    /**
     * @description: 获取街道，清空选中的街道数据
     */
    changeCity() {
      if (this.queryParam.towncode.length) {
        this.queryParam.towncode = [];
      }
      if (!this.queryParam.adcode) {
        // 当没有code时，表示清空了区县，清空街道列表，不需要重新调用接口
        this.streetList = [];
        return;
      }
      getCityByParentCodeAPI({
        parentCode: this.queryParam.adcode,
      }).then((res) => {
        this.streetList = res.data || [];
      });
    },

    /**
     * @description: 打开标签弹框
     */
    selectLabelHandle() {
      // 回显已选择的标签
      this.$refs.labelModal.init(this.selectedLabels);
    },

    /**
     * @description: 选择标签
     * @param {array} val 已选择的标签
     */
    setCheckedLabel(val) {
      this.selectedLabels = val;
      this.queryParam.labelIds = val.map((v) => v.id);
    },

    /**
     * @description: 查询
     */
    search() {
      this.$emit("search");
    },

    /**
     * @description: 重置
     */
    reset() {
      this.$refs.form.resetFields();
      this.queryParam.labelIds = [];
      this.$refs.labelModal.removeAllHandle();
      this.search();
    },

    /**
     * @description: 获取所有查询参数，暴露给父组件
     * @return {object} 查询参数
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  border-bottom: 1px solid #d3d7de;
  width: 100%;
  .form {
    width: 100%;
    /deep/.ivu-form-item {
      margin-bottom: 16px;
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input,
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    .btn-group {
      float: right;
      margin-right: 0;
    }
  }
}
</style>
