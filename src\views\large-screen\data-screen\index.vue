<!--
    * @FileDescription: 数据专题大屏
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="screen">
        <component :is="sectionName"></component>
        <div class="large-screen-footer">
            <div class="btn" :class="{'btn-active': btnIndex == 0}" @click="handleBtn(0)"> <span>数据态势</span> </div>
            <div class="btn" :class="{'btn-active': btnIndex == 1}" @click="handleBtn(1)"> <span>布控态势</span> </div>
            <div class="btn" :class="{'btn-active': btnIndex == 2}" @click="handleBtn(2)"> <span>运营态势</span> </div>
        </div>
    </div>
</template>
<script>
import dataSituation from './components/data-situation/index.vue';
import deploySituation from './components/deploy-situation/index.vue';
import operationSituation from './components/operation-situation/index.vue';
export default {
    components: {
        dataSituation,
        deploySituation,
        operationSituation
    },
    data() {
        return {
            btnIndex: 0,
            sectionName: '' 
        }
    },
    created() {
        this.sectionName = 'dataSituation';
    },
    methods: {
        handleBtn(index) {
            this.btnIndex = index;
            switch(index) {
                case 0: 
                    this.sectionName = 'dataSituation';
                    break;
                case 1: 
                    this.sectionName = 'deploySituation';
                    break;
                case 2: 
                    this.sectionName = 'operationSituation';
                    break;
            }
        },
    },
}
</script>
<style lang='less' scoped>
.screen{
    height:calc( ~'100% - 110px');
    display: flex;
    flex: 1;
    padding: 0 40px 20px;
    .large-screen-footer{
        display: flex;
        justify-content: center;
        position: absolute;
        bottom: 50px;
        left: 50%;
        transform: translateX(-50%);
        .btn{
            width: 115px;
            height: 38px;
            font-size: 18px;
            color: #fff;
            font-weight: 400;
            margin: 0 30px; 
            background: url('~@/assets/img/screen/btnbg_normal.png') no-repeat;
            text-align: center;
            padding-top: 4px;
            cursor: pointer;
            span{
                font-family: 'PangMenZhengDao';
                text-transform: none;
                letter-spacing: 1px;
                text-shadow: 0px 1px 3px rgba(2, 20, 63, 0.1);
                background: linear-gradient(90deg, #ffffff 0%, #ffffff 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }
            
        }
        .btn-active{ 
            background: url('~@/assets/img/screen/btnbg_active.png') no-repeat;
        }
    }
}
</style>