<template>
  <div class="container">
    <!-- 查询 -->
    <Search
      ref="searchRef"
      :subTaskType="subTaskType"
      @searchForm="searchForm"
    />
    <div class="table-container">
      <div class="data-above">
        <div class="left-operater">
          <Button size="small" @click="handleAdd">
            <ui-icon type="jia" color="#2C86F8"></ui-icon>
            新增任务
          </Button>
          <Button size="small" @click="handleDelJobs">
            <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
            批量删除
          </Button>
        </div>
        <div class="right-operater">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #videoTime="{ row }">
            <div>
              {{
                row.taskType == 5
                  ? getVideoAllLength(row.taskResourceList)
                  : "--"
              }}
            </div>
          </template>
          <template #taskType="{ row }">
            <div>
              {{ taskTypeList[row.taskType] }}
            </div>
          </template>
          <template #fileNum="{ row }">
            <div>
              {{ getFileNum(row.taskResourceList) }}
            </div>
          </template>
          <template #status="{ row }">
            <div>
              {{ taskStatusList.find((item) => item.key == row.status).label }}
            </div>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <fileAddModal
      v-model="isShowAdd"
      ref="fileAddModalRef"
      :subTaskId="subTaskId"
      :subDeviceId="subDeviceId"
      @updated="jobUpdated"
    ></fileAddModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import Search from "../components/search.vue";
import fileAddModal from "../components/file-add-modal.vue";
import TableAction from "../components/table-action.vue";
import expandRow from "../components/expandRow.vue";
import {
  multiModalTaskPageList,
  downloadFileByTaskId,
} from "@/api/multimodal-analysis.js";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import { taskTypeList, taskStatusList } from "../enums/index.js";
import TaskHandler from "@/views/multimodal-analysis/mixins/taskHandler.js";
export default {
  name: "FileAnalysis",
  components: {
    Search,
    fileAddModal,
    adjustPosition,
  },
  props: {},
  mixins: [TaskHandler],
  data() {
    return {
      taskTypeList,
      taskStatusList,
      subTaskType: "file",
      list: [],
      childList: [],
      loading: false,
      pageForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        structuredParsingType: 5, // 结构化解析类型: 5文件解析
        sortType: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, { row, index }) => {
            const resourceList = row.taskResourceList.map((item) => {
              const { resourceInfo, ...otherParam } = item;
              return {
                ...resourceInfo,
                ...otherParam,
                createTime: row.createTime,
              };
            });
            return h(expandRow, {
              props: {
                tableList: resourceList,
                columns: this.childColumns,
                currentJob: this.list[index],
                subTaskType: this.subTaskType,
                subTaskStatus: row.status,
                switchLoading: this.switchLoading,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(row);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    ...val,
                    structuredParsingType: row.structuredParsingType,
                  });
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val) => {
                  this.deleteTasks([val]);
                },
                fileDownload: (val) => {
                  this.fileDownload(val);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "taskName", width: 260 },
        { title: "解析类型", key: "structureAlgorithmName", width: 140 },
        { title: "文件类型", slot: "taskType", width: 150 },
        { title: "大小/数量", slot: "fileNum", width: 130 },
        { title: "时长", slot: "videoTime", width: 180 },
        { title: "任务状态", slot: "status" },
        { title: "创建时间", key: "createTime", width: 190 },
        { title: "创建人", key: "creator" },
        {
          title: "操作",
          width: 120,
          render: (h, { row, index }) => {
            return h(TableAction, {
              props: {
                row,
                subTaskType: this.subTaskType,
              },
              on: {
                taskStatusHandler: (val) => {
                  this.taskStatusHandler(val);
                },
                handleEdit: (val) => {
                  this.handleEdit(val);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    taskId: val.id,
                    ...val,
                  });
                },
                mapLoaction: (val) => {
                  this.mapLoaction(val);
                },
                handleDel: (val) => {
                  this.handleDel(val);
                },
                fileDownload: (val) => {
                  this.fileDownloadByTask(val);
                },
              },
            });
          },
        },
      ],
      childColumns: [
        { title: "文件名称", key: "resourceName", align: "center" },
        { title: "大小/时长", slot: "fileSizeNum", align: "center" },
        { title: "分辨率", key: "resolution", align: "center" },
        { title: "任务状态", slot: "fileResourceStatus", align: "center" },
        { title: "处理时长", slot: "analysisTime", align: "center" },
        { title: "创建时间", key: "createTime", align: "center" },
        { title: "校准时间", key: "absTime", align: "center" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {},
  mounted() {
    const { addTask = false } = this.$route.query;
    if (addTask) {
      // 搜索中心跳转 添加文件解析 内容塞在session中，用完即丢
      const imageList = JSON.parse(
        window.sessionStorage.getItem("fileAnalysisImageList") || "[]"
      );
      if (imageList.length > 0) {
        window.sessionStorage.removeItem("fileAnalysisImageList");
        this.subTaskId = "";
        this.subDeviceId = "";
        this.isShowAdd = true;
        this.$nextTick(() => {
          this.$refs.fileAddModalRef.fileAddInit(imageList);
        });
      }
    }

    this.getList();
  },
  beforeDestroy() {},
  methods: {
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    // 查询列表
    getList(otherParam = {}) {
      this.loading = true;
      let param = {
        ...this.$refs.searchRef.getSearchParam(),
        ...this.params,
        ...otherParam,
      };
      multiModalTaskPageList(param)
        .then((res) => {
          this.list = res?.data?.entities || [];
          this.total = res?.data?.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    jobUpdated() {
      this.isShowAdd = false;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm(form) {
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
        getStructureRealTaskList({
          structureJobId: row.jobId,
          structureJobType: this.params.structureJobType,
          sort: this.params.sort,
          pageNumber: 0,
          pageSize: 999,
        }).then((res) => {
          const { list } = res.data;
          this.$set(this.childList, index, list || []);
          if (!list.length) this.getList();
        });
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.fileAddModalRef.init({});
      });
    },
    handleEdit(row) {
      this.subTaskId = row.id;
      this.subDeviceId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.fileAddModalRef.init({ ...row });
      });
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      const resourceInfoList = item.taskResourceList.map((item) => {
        return item.resourceInfo;
      });
      var arrayP = [];
      resourceInfoList &&
        resourceInfoList.length > 0 &&
        resourceInfoList.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: item?.resourceName || "",
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      } else {
        this.$Message.error("该视频文件无点位信息，无法定位");
      }
    },
    fileDownload(row) {
      const {
        taskResourceList = [],
        resourceFilePath = "",
        resourceName,
      } = row;
      if (taskResourceList.length > 0) {
        // 任务文件全量下载
      }
      if (resourceFilePath) {
        fetch(resourceFilePath).then(async (res) => {
          const blob = await res.blob();
          // 单个任务文件下载
          const a = document.createElement("a");
          a.setAttribute("download", resourceName);
          a.style.display = "none";
          a.href = URL.createObjectURL(blob);
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        });
      }
    },
    // 文件下载任务中所有的文件
    async fileDownloadByTask(row) {
      downloadFileByTaskId(row.id).then((res) => {
        //下载后文件名
        let fileName = new Date().getTime() + ".zip";
        let a = document.createElement("a");
        let href = URL.createObjectURL(res);
        a.href = href;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      });
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },

    // 任务开启/关闭
    async taskStatusHandler(value) {
      // 开启
      if (value.status == 1) {
        const bool = await this.stopJobs([value]);
        value.status = bool ? 2 : value.status;
      } else {
        const bool = await this.startJobs([value]);
        value.status = bool ? 1 : value.status;
      }
    },
    // 获取任务中文件资源的总大小和数量
    getFileNum(taskResourceList) {
      const fileLength = taskResourceList?.length || 0;
      if (fileLength == 0) {
        return "--/--";
      }
      let fileSize = 0;
      taskResourceList.forEach((item) => {
        fileSize += item?.resourceInfo?.fileSize || 0;
      });
      return (fileSize / 1024 / 1024).toFixed(2) + "MB/" + fileLength;
    },
    // 获取任务中视频时长
    getVideoAllLength(taskResourceList) {
      const fileLength = taskResourceList?.length || 0;
      if (fileLength == 0) {
        return "--";
      }
      // 时间相加回头处理
      let allSecond = 0;
      taskResourceList.forEach((item) => {
        const videoDuration = item?.resourceInfo?.videoDuration?.split(":") || [
          0, 0, 0,
        ];
        const second =
          videoDuration[0] * 3600 +
          videoDuration[1] * 60 +
          videoDuration[2] * 1;
        allSecond += second;
      });
      const hour = Math.floor(allSecond / 3600);
      const min = Math.floor((allSecond % 3600) / 60);
      const second = Math.round((allSecond % 3600) % 60);
      return `${hour}:${min}:${second}`;
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .data-above {
      .left-operater {
        display: flex;
      }
      .right-operater {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        .flex-center {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .rotate {
          transform: rotate(180deg);
        }
      }

      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      button {
        margin-right: 10px;
      }
    }
    .table-content {
      flex: 1;
      overflow: scroll;
      .ui-table {
        height: 100%;
      }
    }
  }
}
.progress-box {
  /deep/ .ivu-progress-inner {
    background: #dde1ea !important;
  }
  /deep/ .ivu-progress-text-inner {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
  }
}
</style>
