<template>
  <div class="basis" ref="mains">
    <div style="height: 100%" v-if="!componentName">
      <!-- <Row class="basis-header">
				<Col :span="12" class="title">
					<div class="border-header"></div>
					<div class="title-header">基础表管理</div>
				</Col>
				<Col :span="12" align="right">
					<span class="back" @click="back()">
						<Icon type="ios-arrow-back" />返回
					</span>
				</Col>
			</Row>
			<Divider />-->
      <div class="basis-content">
        <div class="basis-content-left">
          <div>
            <div class="basis-title">
              <span>基础表</span>
              <create-tabs
                style="display: inline"
                :component-name="componentList.componentName"
                :tabs-text="componentList.text"
                :tabs-query="{}"
                @selectModule="selectModule"
                v-permission="{ route: 'basis', permission: 'basisAdd' }"
              >
                <Button type="primary" class="fr">
                  <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"></i>
                  <span class="vt-middle">新建基础表</span>
                </Button>
              </create-tabs>
            </div>
            <div class="search-input">
              <Input
                v-model="searchData.searchValue"
                :clearable="true"
                @on-clear="infoList"
                @on-click="infoList"
                @on-enter="infoList"
                placeholder="请输入搜索内容"
              >
                <Icon type="ios-search" @on-click="infoList" slot="suffix" />
              </Input>
              <div></div>
            </div>
            <!-- sdfsdf -->
            <Collapse simple class="basis-collapse mr20 ml20">
              <Panel :name="item.name" v-for="(item, index) in colimns" :key="item.name + index">
                {{ metaTableType[item.name] }}
                <div slot="content">
                  <div
                    v-for="itemChilren in item.children"
                    :key="item.name + itemChilren.id"
                    @click="selected(itemChilren)"
                    :class="tableSelected.id === itemChilren.id ? 'basis-collapse-list-active' : ''"
                    class="basis-collapse-list"
                  >
                    <div class="basis-collapse-title">
                      {{ itemChilren.tableName }}
                    </div>
                    <div class="basis-collapse-icon">
                      <create-tabs
                        :ref="'createTabs' + itemChilren.id"
                        :component-name="componentList1.componentName"
                        :tabs-text="componentList1.text"
                        :tabs-query="{ id: itemChilren.id }"
                        @selectModule="selectModule"
                      >
                        <i
                          class="icon-bianji icon-font fs14"
                          title="编辑"
                          @click="editClose(itemChilren)"
                          v-permission="{
                            route: 'basis',
                            permission: 'basisEdit',
                          }"
                        ></i>
                      </create-tabs>
                      <i
                        class="icon-shanchu icon-font fs14"
                        title="删除"
                        @click.stop="deleteItem(itemChilren)"
                        v-permission="{
                          route: 'basis',
                          permission: 'basisDel',
                        }"
                      ></i>
                    </div>
                  </div>
                </div>
              </Panel>
            </Collapse>
          </div>
        </div>
        <div class="basis-content-right">
          <div class="basis-introduction">
            <ui-label label="表名称:" :width="56">{{ tableSelected.tableName || '暂无' }}</ui-label>
            <ui-label label="创建人:" :width="56">{{ tableSelected.creator || '暂无' }}</ui-label>
            <ui-label label="创建时间:" :width="70">{{ tableSelected.createTime || '暂无' }}</ui-label>
            <ui-label label="维护人:" :width="56">{{ tableSelected.maintenancer || '暂无' }}</ui-label>
            <ui-label label="联系方式:" :width="70">{{ tableSelected.maintenancerPhone || '暂无' }}</ui-label>
          </div>
          <div class="left-div">
            <div class="pr20 pl20">
              <ui-table
                class="ui-table"
                :loading="loading"
                :table-columns="tableColumns"
                :table-data="tableData"
                ref="table"
                :minus-height="220"
              >
                <template slot-scope="{ row }" slot="fieldType">
                  {{ metaTableType[row.fieldType] }}
                </template>
              </ui-table>
            </div>
            <!-- <ui-page
							class="page"
							:page-data="pageData"
							@changePage="changePage"
							@changePageSize="changePageSize"
						></ui-page>-->
            <loading v-if="loading"></loading>
          </div>
        </div>
      </div>
    </div>
    <keep-alive :include="cacheRouterName">
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import metadatamanagement from '@/config/api/metadatamanagement';
import algorithm from '@/config/api/algorithm';
export default {
  name: 'basis',
  props: {},
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentList: { componentName: 'basisAdd', text: '新增表' },
      componentList1: { componentName: 'basisEdit', text: '编辑表' },
      tableSelected: {},
      tableHeight: '',
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      copySearch: {},
      searchData: {
        searchValue: '',
      },
      tableColumns: [
        { title: '字段名', key: 'fieldName' },
        { title: '注释', key: 'fieldComment' },
        { title: '类型', slot: 'fieldType' },
        { title: '长度', key: 'fieldLength' },
        // { title: "主键", key: "roleName2" }
      ],
      colimns: [],
      metaTableType: {},
      metaTableTypeList: [],
      tableData: [],
      componentLevel: 0,
      activatedLoad: true,
    };
  },
  created() {
    this.getParams();
  },
  async activated() {
    this.loading = true;
    if (!this.$route.query.componentName) {
      this.$nextTick(() => {
        // 获取table距顶部高度 - 公共占用高度
        var tableOffsetTop = this.$refs.table.$el.offsetTop;
        // 获取当前页总高度
        var pageHeight = this.$refs.mains.scrollHeight;
        this.tableHeight = pageHeight - tableOffsetTop - 54;
      });
      if (this.activatedLoad) {
        await this.getAlgorithmList();
        await this.infoList();
      }
    }
  },

  methods: {
    ...mapActions({
      closeCacheRouter: 'tabs/closeCacheRouter',
    }),
    async getAlgorithmList() {
      await this.$http.get(algorithm.dictData + 'metaTableType').then((res) => {
        if (res.data.code === 200) {
          res.data.data.map((val) => {
            this.metaTableType[val.dataKey] = val.dataValue;
          });
          this.metaTableTypeList = res.data.data || [];
        }
      });
    },
    // 获取列表
    async infoList() {
      this.loading = true;
      try {
        let res = await this.$http.post(metadatamanagement.mangeTableList, this.searchData);
        this.colimns = res.data.data;
        this.loading = false;
      } catch (err) {
        this.loading = false;
        console.log(err);
      }
    },
    // 删除数据
    deleteItem(item) {
      this.$UiConfirm({
        content: `您将要删除表 ${item.tableName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.remove(item);
        })
        .catch((res) => {
          console.log(res);
        });
      // this.$Modal.confirm({
      //   title: '警告',
      //   content: `您将要删除表 ${item.tableName}，是否确认?`,
      //   onOk: () => {
      //     this.remove(item)
      //   },
      // })
    },
    // 删除
    async remove(item) {
      try {
        let res = await this.$http.delete(metadatamanagement.mangeRemove + item.id);
        if (res.data.code === 200) {
          this.$Message.success('删除成功');
          this.infoList();
        } else {
          this.$Message.error('删除失败');
        }
      } catch (err) {
        console.log(err);
      }
    },
    selectModule(name) {
      // setTimeout(() => {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
      // }, 20)
    },
    async editClose() {
      // 每次编辑需要清缓存重新定一id
      if (this.cacheRouterName.includes('basisEdit')) {
        await this.closeCacheRouter('basisEdit');
      }
      // await this.$refs['createTabs' + itemChilren.id][0].create()
    },
    async getParams() {
      if (!this.$route.query.componentName) {
        this.activatedLoad = false;
        await this.getAlgorithmList();
        await this.infoList();
        if (this.tableSelected.id) {
          await this.selected(this.tableSelected);
        }
      }
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    // 返回
    back() {
      this.$emit('back');
    },
    // 选中基础表
    async selected(item) {
      this.tableSelected.id = item.id;
      this.$forceUpdate();
      this.loading = true;
      try {
        let res = await this.$http.get(metadatamanagement.mangeView + item.id);
        if (res.data.code === 200) {
          this.tableData = res.data.data.fieldList || [];
          this.tableSelected = res.data.data;
        } else {
          this.tableData = [];
          this.tableSelected = {};
        }
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.tableData = [];
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
    },
  },
  watch: {
    $route: 'getParams',
  },
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
    cacheRouterName() {
      let cacheList = [];
      this.cacheRouterList.forEach((row) => {
        let arr = row.path.split('/');
        // 去除首个空路由
        arr.splice(0, 1);
        arr.push(row.name);
        // 缓存路由为标签路由列表与当前所有路由
        cacheList = [...cacheList, ...arr];
      });
      cacheList = Array.from(new Set(cacheList));
      return cacheList;
    },
  },
  // 目前新开窗无法支持同一组件打开两次，故区分新增与编辑
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    basisAdd: require('./basis-add.vue').default,
    basisEdit: require('./basis-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
.basis {
  height: 100%;
  background: var(--bg-content);
  .left-div {
    position: relative;
  }
  .basis-introduction {
    display: flex;
    padding: 8px 20px;
    & > div {
      padding-right: 40px;
      color: var(--color-content);
      font-size: 14px;
    }
    /deep/ .ui-label .label {
      color: var(--color-label);
    }
  }
  .ui-table {
    // padding: 0 20px;
  }
  .basis-header {
    padding: 20px;
    .title {
      display: flex;
      .border-header {
        width: 8px;
        height: 30px;
        background: #239df9;
        margin-right: 6px;
      }
      .title-header {
        color: #fff;
        width: 394px;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        font-size: 16px;
      }
    }
    .back {
      color: #2d8cf0;
      font-size: 16px;
      cursor: pointer;
      height: 30px;
      line-height: 30px;
      &:hover {
        color: #57a3f3;
      }
    }
  }
  /deep/ .ivu-divider-horizontal {
    margin: 0;
    background: #1e3f61;
  }
  .mr30 {
    margin-right: 30px !important;
  }
  .basis-content {
    display: flex;
    white-space: nowrap;
    height: 100%;
    overflow: hidden;
    .basis-content-left {
      display: inline-block;
      width: 320px;
      height: calc(100vh - 130px);
      overflow-y: hidden;
      overflow-x: hidden;
      border-right: 1px solid var(--border-color);
      .basis-title {
        padding: 8px 20px;
        line-height: 34px;
        & > span {
          font-size: 14px;
          color: var(--color-content);
        }
      }
      & > div {
        height: 100%;
        overflow: auto;
      }
      .search-input {
        padding: 0px 0 10px 20px;
        display: flex;
        .ivu-input-wrapper {
          width: calc(100% - 20px);
        }
        .ivu-icon-ios-search {
          color: var(--color-active);
          font-size: 22px;
        }
      }
      // 折叠面板样式
      .basis-collapse {
        font-size: 14px;
        background: transparent;
        border: none;

        /deep/ .ivu-collapse-header {
          border: none;
          background: transparent;
          color: var(--color-dropdown-item);
          height: 30px;
          line-height: 30px;
          padding-left: 0px;
          i {
            // margin-right: 10px;
            position: relative;
            top: 2px;
          }
        }
        /deep/ .ivu-collapse-item {
          border: none;
        }
        /deep/ .ivu-collapse-content {
          background: transparent;
          border: none;
          padding-right: 0px;
          padding-left: 0px;
          .ivu-collapse-content-box {
            padding-bottom: 10px;
          }
        }
        /deep/ .ivu-icon-ios-arrow-forward:before {
          content: '\F341';
          font-size: 20px;

          color: var(--color-btn-text);
        }
        .basis-collapse-list-active {
          background: var(--bg-el-tree-node);
          color: #fff !important;
          .basis-collapse-icon {
            display: flex !important;
            i {
              color: #fff;
            }
          }
        }
        .basis-collapse-list {
          color: rgba(255, 255, 255, 0.75);
          height: 30px;
          line-height: 30px;
          font-size: 14px;
          padding-left: 50px;
          cursor: pointer;
          position: relative;
          &:hover {
            background: var(--bg-select-item-hover);
            color: #fff;
            .basis-collapse-icon {
              display: flex;
              i {
                color: #fff;
                &:hover {
                  color: var(--color-primary);
                }
              }
            }
          }
          display: flex;
          flex-wrap: nowrap;
          .basis-collapse-title {
            // width: ;
          }
          .basis-collapse-icon {
            position: absolute;
            right: 10px;
            display: none;
            .icon-bianji {
              position: relative;
              top: -1px;
            }
            i {
              margin-right: 10px;
            }
          }
        }
      }
    }
    .basis-content-right {
      flex: 1 1;
      text-align: right;
      white-space: nowrap;
    }
  }
  .pl20 {
    padding-left: 20px;
  }
  .pr20 {
    padding-right: 20px;
  }
  .mr20 {
    margin-right: 20px;
  }
  .ml20 {
    margin-left: 20px;
  }
  .fs14 {
    font-size: 14px;
  }
}
</style>
