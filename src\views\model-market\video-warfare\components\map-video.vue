// 视频战法-地图
<template>
  <div class="map-box" ref="mapBox">
    <div :id="mapId" class="map"></div>
    <!-- 鼠标浮动 -->
    <mouse-title ref="mouseDom" :name="mouseName"></mouse-title>
    <!-- 重叠点位列表 -->
    <mapPoleList
      ref="poleListDom"
      :polePoints="polePoints"
      @polePointClick="polePointClick"
    ></mapPoleList>
    <!-- 取消框选 -->
    <div
      class="remove_route"
      v-show="isShowRemoveBar"
      id="roadSelectRemove"
      ref="trailRemove"
      @click="removeRoute"
    ></div>
    <Modal
      ref="deviceModalRef"
      class-name="domModal"
      :title="modalOption.title"
      v-model="modalOption.open"
      reset-drag-position
      footer-hide
      draggable
      sticky
      :width="modalOption.isShowModelDetail ? 650 + 532 : 532"
      :styles="{ top: 0, width: 'auto' }"
      center
      :mask="false"
      @on-cancel="closeDeviceModal()"
    >
      <device
        ref="deviceRef"
        :isopen="modalOption.open"
        @isModelDetail="changeModelDetail($event)"
      ></device>
    </Modal>
    <slot></slot>
  </div>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
import mouseTitle from "@/components/map/mouse-title.vue";
import mapPoleList from "@/components/map/map-pole-list.vue";
import { LayerType } from "@/map/core/enum/LayerType.js";
import { throttle } from "lodash";
import { trailSelectMixin } from "./trail_select_mixin.js";
import device from "@/views/operations-on-the-map/map-default-page/components/map-dom/device-map-dom.vue";

let mapMain = null;
const mouseInfoWindow = [];
const poleInfoWindow = [];
//线样式
let _lineStyle = {
  strokeColor: "#2D87F9", //绘制过程中边框的颜色
  color: "#2D87F9",
  weight: 2,
  opacity: 1,
  virtualStyle: {
    //绘制时的圆点样式
    pointRadius: 6,
    fillColor: "#FFFFFF",
    fillOpacity: 1,
    strokeOpacity: 1,
    strokeColor: "#2c9eff",
    strokeWidth: 2,
  },
};

export default {
  components: {
    mouseTitle,
    mapPoleList,
    device,
  },
  mixins: [trailSelectMixin],
  props: {
    // 图层点位信息
    siteList: {
      type: Array,
      default: () => [],
    },
    // 撒点
    siteListFlat: {
      type: Array,
      default: () => [],
    },
    // 地图图层配置信息
    mapLayerConfig: {
      type: Object,
      default: () => {
        return {
          tracing: false, // 是否需要刻画轨迹
          showStartPoint: false, // 是否显示起点终点图标
          mapToolVisible: false, // 框选操作栏
          selectionResult: true, // 是否显示框选结果弹框
          resultOrderIndex: false, // 搜索结果排序,
          showLatestLocation: false, // 显示地图最新位置
        };
      },
    },
    idlerWheel: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // 设备
    siteList: {
      handler(val) {
        if (val.length) {
          this._initSystemPoints3Map(val);
        }
      },
    },
    siteListFlat: {
      handler(val) {
        if (val.length) {
          let obj = val.map((arr, index) => {
            let item = {};
            item.deviceGbId = arr[0];
            item.deviceName = arr[1];
            item.deviceType = arr[2] + "";
            item.deviceChildType = arr[3] + "";
            item.sbgnlx = !!arr[4] ? arr[4] + "" : ""; // 一二三类点位
            item.isOnline =
              !!arr[5] || arr[5] === 0 || arr[5] === "0" ? arr[5] + "" : ""; // 点位状态
            item.longitude = arr[6] + "";
            item.latitude = arr[7] + "";
            item.deviceId = arr[8];
            item.isCommunity = arr[9] + ""; //是否社会资源点位
            item.sx_place =
              (!!arr[11] || arr[11] === 0 || arr[11] === "0") && arr[11] != 99
                ? arr[11] + ""
                : ""; //点位所在场所
            item.sx_project =
              (!!arr[12] || arr[12] === 0 || arr[12] === "0") && arr[12] != 99
                ? arr[12] + ""
                : ""; //点位建设项目
            item.sx_construction_unit =
              (!!arr[13] || arr[13] === 0 || arr[13] === "0") && arr[13] != 99
                ? arr[13] + ""
                : ""; //点位建设单位
            item.sx_warranty_period =
              (!!arr[14] || arr[14] === 0 || arr[14] === "0") && arr[14] != 99
                ? arr[14] + ""
                : ""; //是否在保
            item.sx_operation_unit =
              (!!arr[15] || arr[15] === 0 || arr[15] === "0") && arr[15] != 99
                ? arr[15] + ""
                : ""; //点位运维单位
            item.geoPoint = { lat: item.latitude, lon: item.longitude };
            item.mapType = item.deviceType;
            item.ptzType = item.deviceChildType
              ? item.deviceChildType
              : item.ptzType;
            return item;
          });
          this._initSystemPoints3Map(obj);
        } else {
          mapMain && mapMain.renderMarkers([]);
          this.resetMarker();
        }
      },
    },
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      globalObj: "systemParam/globalObj",
    }),
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      mouseName: "",
      polePoints: [],
      isShowRemoveBar: false,
      modalOption: {
        open: false,
        title: "设备详情",
        isShowModelDetail: false,
      },
    };
  },
  deactivated() {},
  async mounted() {
    await this.getMapConfig();
    this.loading = false;
    if (!this.idlerWheel) {
      this.mapidlerWheel(); //防止地图与滚动条 滚轮冲突
    }
  },
  methods: {
    mapidlerWheel() {
      this.$nextTick(() => {
        let box = document.querySelector(".map");
        box.onmousewheel = (event) => {
          event = event || window.event;
          box.style.height = box.clientHeight;
          //取消火狐浏览器默认行为（因为是用addEventListener,所以必须用此方法来取消）
          event.preventDefault && event.preventDefault();
          //取消浏览器默认行为
          return false;
        };
        //为火狐浏览器绑定鼠标
        this.bind(box, "DOMMouseScroll", box.onmousewheel);
      });
    },
    bind(obj, eventStr, callback) {
      if (obj.addEventListener) {
        obj.addEventListener(eventStr, callback, false);
      } else {
        obj.attachEvent("on" + eventStr, function () {
          callback.call(obj);
        });
      }
    },
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig, this.mapStyle);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        // 暗色背景？
        // mapMain.init(mapId, data, style)
        mapMain.init(mapId, data);
        // 禁止滚动条
        if (this.disableScroll) {
          mapMain.map.disableScrollWheelZoom();
        }
        //比例尺控件
        var ctrl = new NPMapLib.Controls.ScaleControl();
        mapMain.map.addControl(ctrl);
        this.configDefaultMap();
        document.addEventListener("click", this.mouseOverClickFn, false);
        this._mapGeometry = new MapPlatForm.Base.MapGeometry(mapMain.map);
        this.$emit("inited", mapMain, this);
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    // 加载点位到地图上(资源图层/设备数据)
    _initSystemPoints3Map(points) {
      this.$nextTick(() => {
        setTimeout(() => {
          // 加载点位
          mapMain.renderMarkers(
            mapMain.convertSystemPointArr3MapPoint(points),
            this.getMapEvents()
          );
        }, 1000);
      });
    },
    // 点击 设备
    getMapEvents() {
      const opts = {
        click: (marker) => {
          if (marker.ext.isPoleGroupPoint) {
            // 重叠聚合点位，列表超过2个不需要点击
            let polePoints = mapMain.poleGroupPoints.filter(
              (v) =>
                !v.isHide &&
                !v.isGnlxHide &&
                v.Lat == marker.ext.Lat &&
                v.Lon == marker.ext.Lon
            );
            if (polePoints.length != 1) return;
          }
          this.handleMarkerClick(marker);
        },
        mouseover: (marker) => {
          if (marker.ext.isPoleGroupPoint) {
            marker.changeStyle &&
              marker.changeStyle(
                {
                  externalGraphic: LayerType["PoleGroupPoint"].hoverUrl,
                },
                true
              );
            const {
              ext: { Lat, Lon },
            } = marker;
            this.showPoleList({ Lat, Lon });
          } else {
            if (marker.changeStyle) {
              marker.changeStyle(
                {
                  externalGraphic: LayerType[marker.markType].hoverUrl,
                },
                true
              );
            } else {
              marker.getIcon().setImageUrl(LayerType[marker.markType].hoverUrl);
              marker.refresh();
            }
            poleInfoWindow.forEach((row) => {
              row.close();
            });
            poleInfoWindow.length = 0;
            this.showMouseDom(marker);
          }
        },
        mouseout: (marker) => {
          if (marker.ext.isPoleGroupPoint) {
            marker.changeStyle &&
              marker.changeStyle(
                {
                  externalGraphic: LayerType["PoleGroupPoint"].url,
                },
                true
              );
          } else {
            if (!marker.ext.clicked) {
              if (marker.changeStyle) {
                marker.changeStyle(
                  {
                    externalGraphic: marker.normalUrl,
                  },
                  true
                );
              } else {
                marker.getIcon().setImageUrl(LayerType[marker.markType].url);
                marker.refresh();
              }
            }
          }
          this.closeMouseInfoWindow();
        },
      };
      return opts;
    },
    polePointClick(item) {
      this.handleMarkerClick({ ext: item });
    },
    closeMouseInfoWindow() {
      mouseInfoWindow.forEach((row) => {
        row.close();
      });
    },
    // 鼠标浮动在资源图层图标上
    showMouseDom(marker) {
      let { Lat, Lon } = marker.ext;
      this.mouseName =
        marker.ext.placeName ||
        marker.ext.deviceName ||
        marker.ext.name ||
        marker.ext.address;
      const point = new NPMapLib.Geometry.Point(Lon, Lat);
      this.$nextTick(() => {
        const dom = this.$refs["mouseDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (60 / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 15;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        mouseInfoWindow.push(infoWindow);
      });
    },
    // 显示重合点位列表
    showPoleList({ Lat, Lon }) {
      // let polePoints = mapMain.poleGroupPoints.filter(v => !v.isHide && !v.isGnlxHide && v.Lat.substring(0, 7) == Lat.substring(0, 7) && v.Lon.substring(0, 8) == Lon.substring(0, 8))
      let polePoints = mapMain.poleGroupPoints.filter(
        (v) => !v.isHide && !v.isGnlxHide && v.Lat == Lat && v.Lon == Lon
      );
      polePoints.sort((a, b) => a.deviceName.localeCompare(b.deviceName));
      this.polePoints = polePoints;
      const point = new NPMapLib.Geometry.Point(Lon, Lat);
      this.$nextTick(() => {
        let h =
          10 +
          (this.polePoints.length > 6 ? 6 * 32 : this.polePoints.length * 32);
        const dom = this.$refs["poleListDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (h / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 35;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: true, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          panMapIfOutOfView: true,
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        poleInfoWindow.push(infoWindow);
      });
    },
    showCameraInfowindow(point) {
      this.selectDeviceItem(point);
      this.$nextTick(() => {
        let { longitude, latitude } = point;
        this.$refs.deviceRef
          .init(
            point,
            { ...point, geoPoint: { lon: longitude, lat: latitude } },
            "",
            false,
            true
          )
          .then((res) => {
            if (!res.data) {
              this.$Message.warning("暂无数据");
            }
          });
      });
    },
    selectDeviceItem(pointItem, noZoom = false) {
      if (!noZoom) {
        // 获取图层，计算弹框向下偏移量
        let zoomLat = this.moveModal();
        mapMain.map.centerAndZoom(
          new NPMapLib.Geometry.Point(
            pointItem.lon ||
              pointItem.Lon ||
              pointItem.longitude ||
              pointItem.geoPoint.lon,
            (pointItem.lat ||
              pointItem.Lat ||
              pointItem.latitude ||
              pointItem.geoPoint.lat) + zoomLat
          ),
          mapMain.map.getMaxZoom()
        );
      }

      this.modalOption.title = pointItem.deviceName;
      this.modalOption.open = true;

      // 修改弹框位置
      setTimeout(() => {
        let dragDom = this.$refs.deviceModalRef.$el.querySelector(
          ".ivu-modal-content-drag"
        );
        if (dragDom) {
          let left = window.innerWidth / 2 - dragDom.offsetWidth / 2;
          let top = window.innerHeight / 2 - dragDom.offsetHeight;
          dragDom.style.left = left + "px";
          dragDom.style.top = (top < 90 ? 90 : top) + "px";
        }
      });
    },
    changeModelDetail(val, index) {
      this.modalOption.isShowModelDetail = val;
    },
    closeDeviceModal() {
      this.$refs.deviceRef.stopVideo();
    },
    moveModal() {
      let zoomLat = 0.0005;
      let zoom = mapMain.map.getMaxZoom();
      // 获取不同弹框位置偏移量
      if (zoom == 19) {
        zoomLat = 0.0001;
      } else if (zoom == 18) {
        zoomLat = 0.0002;
      } else if (zoom == 17) {
        zoomLat = 0.0005;
      }
      return zoomLat;
    },
    handleMarkerClick: throttle(
      function (marker) {
        this.$emit("markerClick", marker);
      },
      500,
      { trailing: false }
    ),
    mouseOverClickFn() {
      if (poleInfoWindow.length) {
        poleInfoWindow.forEach((row) => {
          row.close();
        });
        poleInfoWindow.length = 0;
      }
    },
    // 重置marker
    resetMarker() {
      if (mapMain) {
        // if (_wonderLayer) {
        // 		mapMain.map.removeOverlay(_wonderLayer)
        // 		_wonderLayer.removeAllOverlays()
        // 		_wonderLayer = null
        // }
      }
    },
    setCenter(center, zoom) {
      return mapMain.map.setCenter(center, zoom);
    },
    getCenter() {
      return mapMain.map.getCenter();
    },
    getZoom() {
      return mapMain.map.getZoom();
    },
  },
  beforeDestroy() {
    if (mapMain) {
      this.resetMarker();
      mapMain.destroy();
      mapMain = null;
      document.removeEventListener("click", this.mouseOverClickFn, false);
    }
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }
}
/deep/ #npgis_GroupDiv {
  overflow: inherit !important;
}
/deep/.olPopupContent {
  overflow: inherit !important;
}
.remove_route {
  display: inline-block;
  height: 30px;
  width: 91px;
  cursor: pointer;
  background: url("~@/assets/img/map/track/remove-route.png");
}
/deep/.domModal {
  .ivu-modal-body {
    padding: 0 !important;
  }
  .modalHeader {
    color: #fff;
    font-weight: 700;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    padding-right: 30px;
    .right {
      display: flex;
    }
    .operter {
      display: flex;
      i {
        cursor: pointer;
        margin: 0 5px;
        font-weight: normal;
      }
    }
  }
}
</style>
