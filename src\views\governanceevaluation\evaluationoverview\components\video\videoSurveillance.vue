<template>
  <!-- 视频监控联网率 -->
  <div class="videoSubtitle" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="true" :isIconBg="true"></statistics>
      <!-- <div
        class="information-echart"
        v-ui-loading="{ loading: echartsLoading, tableData: echartData }"
      >
        <draw-echarts
          v-if="echartData.length != 0"
          :echart-option="determinantEchart"
          :echart-style="ringStyle"
          ref="attributeChart"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div> -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                  item.rank
                }}</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                  <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                  <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="item.rank == 1">{{ item.regionName }}</span>
                  <span v-if="item.rank == 2">{{ item.regionName }}</span>
                  <span v-if="item.rank == 3">{{ item.regionName }}</span>
                  <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                    item.regionName
                  }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.standardsValue }}%</span>
              </div>

              <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                  item.rankRise || 0
                }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-yichangshujuliebiao f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <Button type="primary" class="btn_search" :loading="exportLoading" @click="getExport">
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <div class="search-wrapper">
        <ui-label class="mr-lg" label="设备编码" width="70">
          <Input class="input-width" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label label="设备名称" :width="70" class="mr-lg">
          <Input class="input-width" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
        </ui-label>
        <!--        <ui-label label="检测结果" :width="70" class="mr-lg">-->
        <!--          <Select-->
        <!--            v-model="searchData.qualified"-->
        <!--            clearable-->
        <!--            placeholder="请选择检测结果"-->
        <!--            class="width-input"-->
        <!--          >-->
        <!--            <Option value="1">合格</Option>-->
        <!--            <Option value="2">不合格</Option>-->
        <!--            <Option value="3">无法检测</Option>-->
        <!--          </Select>-->
        <!--        </ui-label> -->
        <ui-label label="设备联网状态" :width="95" class="mr-lg">
          <Select v-model="searchData.lwzt" clearable placeholder="请选择设备联网状态" class="width-input">
            <Option value="1">已联网</Option>
            <Option value="2">未联网</Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="fl btns" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button type="default" class="mr-sm" @click="reset"> 重置 </Button>
        </ui-label>
      </div>
      <div class="list">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
          <!-- 点位类型 -->
          <template #sbdwlx="{ row }">
            <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
          </template>
          <template #lwzt="{ row }">
            {{ row.lwzt == 1 ? '已联网' : '未联网' }}
          </template>
          <template #qualified="{ row }">
            <span
              :class="{
                color_qualified: row.qualified === '1',
                color_unqualified: row.qualified === '2',
              }"
              >{{ row.qualified === '1' ? '合格' : row.qualified === '2' ? '不合格' : '无法检测' }}</span
            >
            <Tooltip
              transfer
              placement="bottom"
              v-if="
                row.detectionMode != null &&
                (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
              "
            >
              <i class="icon-font icon-wenhao ml-xs f-12" :style="{ color: 'var(--color-warning)' }"> </i>
              <div slot="content">
                <json-viewer
                  :expand-depth="5"
                  v-if="row.dateImageText != null"
                  :value="JSON.parse(row.dateImageText)"
                  theme="my-awesome-json-theme"
                ></json-viewer>
                <json-viewer
                  :expand-depth="5"
                  v-if="row.additionalImageText != null"
                  :value="JSON.parse(row.additionalImageText)"
                  theme="my-awesome-json-theme"
                ></json-viewer>
                <json-viewer
                  :expand-depth="5"
                  v-if="row.areaImageText != null"
                  :value="JSON.parse(row.areaImageText)"
                  theme="my-awesome-json-theme"
                ></json-viewer>
              </div>
            </Tooltip>
          </template>
          <template #option="{ row }">
            <div class="boxCenter">
              <ui-btn-tip
                class="mr-md"
                icon="icon-bofangshipin"
                content="播放视频"
                @click.native="clickRow(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                class="mr-md"
                icon="icon-tianjiabiaoqian"
                content="添加标签"
                @click.native="addTags(row)"
              ></ui-btn-tip>
              <!-- <ui-btn-tip class="mr-md" icon="icon-chakanxiangqing" content="查看详情" @handleClick="showModal(row)"></ui-btn-tip> -->
            </div>
          </template>
        </ui-table>
        <!-- <loading v-if="loading"></loading> -->
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
    <osdModal ref="osdModal" />
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="margin-top: -15px">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less">
.ivu-tooltip-popper {
  .ivu-tooltip-content {
    .ivu-tooltip-inner {
      max-height: 300px;
      overflow: auto;
    }
  }
}
</style>
<style lang="less" scoped>
.videoSubtitle {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      flex: 1;
      // width: 755px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 20px;
    }
    .information-echart {
      display: inline-block;
      width: 650px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .echarts-box {
        width: 100%;
        height: 100% !important;
        .charts {
          width: 100%;
          height: 100% !important;
        }
      }
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      padding-right: 2px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
  .search-wrapper {
    margin-top: 10px;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .input-width {
      width: 176px;
    }
    .ui-label {
      margin-bottom: 10px;
    }
  }
  /deep/.conter-center {
    padding: 0;
    width: 100%;
  }
  /deep/.statistics-ul {
    li {
      height: 210px !important;
      width: 19.4% !important;
    }
  }
  /deep/.f-55 {
    font-size: 55px !important;
  }
  .boxCenter {
    display: flex;
    justify-content: center;
  }
  .color_qualified {
    color: #13b13d;
  }
  .color_unqualified {
    color: #e44f22;
  }
}
</style>

<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import vedio from '@/config/api/vedio-threm';
import downLoadTips from '@/mixins/download-tips';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [downLoadTips],
  name: 'video-subtitle',
  data() {
    return {
      videoUrl: '',
      videoStyles: {
        width: '5rem',
      },
      videoVisible: false,
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticsList: [
        {
          name: '视频监控设备总数',
          value: 0,
          icon: 'icon-shipinjiankongshebeizongshu-011',
          iconColor: 'icon-bg1 aaa',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'deviceCount',
        },
        {
          name: '实际测设备数量',
          value: 0,
          icon: 'icon-shijiceshebeishuliang-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'evaluatingCount',
        },
        {
          name: '已联网设备',
          value: 0,
          icon: 'icon-OSDzimubuheguishuliang-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'evaluatingSuccessCount',
        },
        {
          name: '未联网设备',
          value: 0,
          icon: 'icon-wufajianceshebeishuliang-01',
          iconColor: 'icon-bg7',
          liBg: 'li-bg7',
          type: 'percentage',
          textColor: 'color7',
          key: 'evaluatingFailedCount',
        },
        {
          name: '视频监控联网率',
          value: 0,
          icon: 'icon-zhongdianputongzimubiaozhuhe-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        { title: '经度', key: 'longitude', minWidth: 150, tooltip: true },
        { title: '纬度', key: 'latitude', minWidth: 150, tooltip: true },
        {
          title: '监控点位类型',
          key: 'sbdwlx',
          slot: 'sbdwlx',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '设备联网状态',
          slot: 'lwzt',
          minWidth: 150,
          tooltip: true,
        },
        { title: '检测时间', key: 'videoStartTime', minWidth: 150, tooltip: true },
        // { title: '异常原因', key: 'reason', minWidth: 150, tooltip: 'true' },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          width: 100,
          tooltip: true,
          fixed: 'right',
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        deviceId: '',
        deviceName: '',
        customParameters: {
          qualified: '',
        },
      },
      reasonTableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      errorMessages: [],
      // exportList: [
      //   { name: '导出设备总表', type: false },
      //   { name: '按异常原因导出分表', type: true },
      // ],
      exportName: '',
      contentClientHeight: 0,
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    this.getTagList();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    // onClickIndex(val) {
    //   this.exportList.map((item) => {
    //     if (val === item.name) {
    //       this.exportName = item.type
    //     }
    //   })

    //   this.getExport()
    // },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        multiSheet: this.exportName,
        errorMessages: this.errorMessages,
        orgRegionCode: this.paramsList.orgRegionCode,
        displayType: this.paramsList.displayType,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 柱状图统计
    async getGraphsInfo() {
      let data = {
        regionCode: this.paramsList.regionCode,
        rootResultIds: this.paramsList.rootResultIds,
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data;
        this.initRing();
      } catch (err) {
        console.log(err);
      }
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          orgRegionCode: this.paramsList.orgRegionCode,
          displayType: this.paramsList.displayType,
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          customParameters: {
            deviceName: this.searchData.deviceName,
            deviceId: this.searchData.deviceId,
            lwzt: this.searchData.lwzt,
          },
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        this.tableData = res.data.data.entities || [];
        this.searchData.totalCount = res.data.data.total;
      } finally {
        this.loading = false;
      }
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
    async clickRow(row) {
      try {
        // this.isLive = true
        // this.loadingVideo = true
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        // this.loadingVideo = false
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
    showModal(row) {
      this.$refs.osdModal.show(row);
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    //统计
    // async getChartsData() {
    //   try {
    //     let params = {
    //       indexId: this.paramsList.indexId,
    //       rootId: this.paramsList.resultId,
    //       orgCode: this.paramsList.regionCode,
    //     }
    //     let res = await this.$http.post(api.queryEvaluatingVideoCount, params)
    //     this.statisticalList = res.data.data
    //     this.statisticsList[0].value = this.statisticalList.deviceCount
    //     this.statisticsList[1].value = this.statisticalList.evaluatingCount
    //     this.statisticsList[2].value = this.statisticalList.evaluatingFailedCount
    //     this.statisticsList[3].value = this.statisticalList.evaluatingSuccessCount
    //     this.statisticsList[4].value = this.statisticalList.unableEvaluatingCount
    //     if (this.paramsList.indexId === 21) {
    //       this.statisticsList[8].name = '重点实时视频可调阅率'
    //     }
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    // 查看结果
    viewResult() {},
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
        deviceInfoId: this.deviceInfoId,
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },

    // selectInfo(val) {
    //   this.errorMessages = val.map((item) => {
    //     return item.name
    //   })
    //   this.getTableData()
    // },
    selectInfo(infoList) {
      this.searchData.pageNumber = 1;
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.getTableData();
      //   this.searchData.tagIds = infoList.map((row) => {
      //     return row.id;
      //   });
      //   this.search();
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
    },
    search() {
      this.searchData = { ...this.searchData, pageNum: 1 };
      this.getTableData();
    },
    reset() {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        deviceId: '',
        deviceName: '',
        lwzt: '',
      };
      this.getTableData();
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          // if (this.paramsList.indexId == 4003) {
          //   this.statisticsList[4].name = '重点字幕标注合规率'
          // } else {
          //   this.statisticsList[4].name = '普通字幕标注合规率'
          // }
          this.getTableData(); //表格
          // this.getSelectTabs()
          // this.getChartsData()
          // this.getGraphsInfo() //柱状图
          // this.queryDeviceCheckColumnReports() //头部中间echarts
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    // DrawEcharts: require('@/components/draw-echarts').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    EasyPlayer: require('@/components/EasyPlayer').default,
    osdModal: require('@/views/appraisaltask/inspectionrecord/video/osd-modal.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
