<template>
  <ui-modal class="bookbuilding" v-model="visible" v-if="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-ui-loading="{ tableData: indexList }">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button>
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left" :class="indexList.fullCatalogueSecondLevelCount != null ? '' : 'ech-style'">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <div class="sta_item_left" v-if="indexList.fullCatalogueSecondLevelCount != null">
              <draw-echarts
                :echart-option="ctChartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>

            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="未上报目录"></line-title>
        <div class="list auto-fill">
          <ui-search-tree
            class="auto-fill"
            placeholder="请输入管辖单位名称"
            node-key="id"
            :no-search="false"
            :max-height="488"
            :tree-data="treeData"
            :default-props="defaultProps"
          >
          </ui-search-tree>
        </div>
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.bookbuilding {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }

    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  // .no-data_str {
  //   transform: translate(-24%, -50%);
  // }
  .no-box {
    width: 1822px;
    min-height: 800px;
    max-height: 800px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 800px;
    max-height: 800px;
    border-radius: 4px;
    position: relative;

    .container {
      .btns {
        margin-top: 10px;

        button {
          color: #a5a5a5;
          background-color: #12294e;
          border-color: #1375a7;
        }

        .active {
          color: #fff;
          background-color: #2d8cf0;
        }

        button:hover {
          color: #fff;
          background-color: #2d8cf0;
        }
      }

      .list_item {
        margin-top: 10px;
      }

      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }

        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }

      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;

        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }

        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }

      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }

      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .sta_item_left {
          display: flex;
          justify-content: center;
        }

        .ech-style {
          width: 50%;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }

    .list {
      position: relative;
      margin-top: 10px;
      height: 542px;

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      treeData: [],
      echartRing: {},
      ctChartRing: {},
      ringStyle: {
        width: '500px',
        height: '180px',
      },
      moduleData: {
        rate: '全量目录完整率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      zdryChartObj: {
        xAxisData: ['已上报地市', '未上报地市'],
        showData: [
          { name: '已上报地市', value: 0 },
          { name: '未上报地市', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      ctChartObj: {
        xAxisData: ['已上报区县', '未上报区县'],
        showData: [
          { name: '已上报区县', value: 0 },
          { name: '未上报区县', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      styles: {
        width: '9.5rem',
      },
      visible: true,
      loading: false,
      detailInfo: {},
      detailList: [],
      indexList: {},
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      exportLoading: false,
    };
  },
  async mounted() {
    await this.getEvaluationRecord();
    await this.initUnUploadCity();
    await this.initUnUploadCT();
  },
  methods: {
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.get(governanceevaluation.exportFullDirIndex, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },

    async getEvaluationRecord() {
      try {
        let data = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(governanceevaluation.queryEvaluationBasicRecord, data);
        if (!res.data.data) return;
        this.indexList = res.data.data;
        this.moduleData.rateValue = this.indexList.resultValue; //建档率
        this.moduleData.priceValue = this.indexList.standardsValue; //达标值
        this.moduleData.resultValue = this.indexList.qualifiedDesc; //考核结果
        this.moduleData.remarkValue = this.indexList.remark;
        this.treeData = this.$util.common.arrayToJson(
          this.$util.common.deepCopy(this.indexList.fullCatalogueOrgList),
          'id',
          'parentId',
        );
      } catch (err) {
        console.log(err);
      }
    },
    initUnUploadCity() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '已上报地市') {
          item.value = this.indexList.fullCatalogueFirstLevelReportCount || 0;
          item.name = `已上报${this.indexList.fullCatalogueFirstLevelRegionText || '地市'}`;
        } else {
          item.value =
            this.indexList.fullCatalogueFirstLevelCount - this.indexList.fullCatalogueFirstLevelReportCount || 0;
          item.name = `未上报${this.indexList.fullCatalogueFirstLevelRegionText || '地市'}`;
        }
      });
      let showData = this.zdryChartObj.showData;
      this.zdryChartObj.count = this.indexList.fullCatalogueFirstLevelCount || 0;
      let formatData = {
        seriesName: '应上报地市',
        xAxisData: xAxisData,
        showData: showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    initUnUploadCT() {
      let xAxisData = this.ctChartObj.xAxisData;
      this.ctChartObj.showData.map((item) => {
        if (item.name === '已上报区县') {
          item.value = this.indexList.fullCatalogueSecondLevelReportCount || 0;
          item.name = `已上报${this.indexList.fullCatalogueSecondLevelRegionText || '区县'}`;
        } else {
          item.value =
            this.indexList.fullCatalogueSecondLevelCount - this.indexList.fullCatalogueSecondLevelReportCount || 0;
          item.name = `未上报${this.indexList.fullCatalogueSecondLevelRegionText || '区县'}`;
        }
      });
      let showData = this.ctChartObj.showData;
      this.ctChartObj.count = this.indexList.fullCatalogueSecondLevelCount || 0;
      let formatData = {
        seriesName: '应上报区县',
        xAxisData: xAxisData,
        showData: showData,
        count: this.ctChartObj.count,
        color: this.ctChartObj.color,
      };
      this.ctChartObj.formatData = formatData;
      this.ctChartRing = this.$util.doEcharts.evaluationPageRin(this.ctChartObj.formatData);
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    lineTitle: require('@/components/line-title').default,
  },
};
</script>
