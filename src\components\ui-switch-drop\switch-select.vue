<template>
  <div
    class="asset-select-container inline"
    :class="currentMenu[tabProps.label] ? 'select' : ''"
    v-if="dropMenuData.length > 0"
  >
    <Dropdown @on-click="onClickMenu">
      <span class="f-14 pointer">
        {{
          Object.keys(currentMenu).length === 0
            ? '更多'
            : `${currentMenu[tabProps.label]}(${currentMenu[tabProps.total] || 0})`
        }}
      </span>
      <i class="icon-font icon-sanjiao f-12"></i>
      <DropdownMenu slot="list">
        <DropdownItem
          v-for="(item, index) in dropMenuData"
          :key="index"
          :name="item[tabProps.value]"
          :selected="item.active"
          >{{ item[tabProps.label] }}</DropdownItem
        >
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'type-select',
  components: {},
  props: {
    tabList: {},
    tabProps: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value',
          total: 'total',
        };
      },
    },
    maxTagCount: {
      default: 3,
    },
    value: {},
  },
  data() {
    return {
      dropMenuData: [
        // { name: '功能类型', key: 'function' },
        // { name: '点位类型', key: 'point' },
        // { name: '重点类型',key: 'important' },
        // { name: '资产质量',key: 'assetsquality' },
      ],
      currentMenu: {},
    };
  },
  computed: {},
  filter: {},
  methods: {
    onClickMenu(val) {
      let dropMenu = this.dropMenuData.find((item) => item[this.tabProps.value] === val);
      this.currentMenu = dropMenu;
      this.$set(dropMenu, 'active', true);
      this.$emit('on-change', dropMenu);
    },
    reset() {
      this.currentMenu = {};
    },
    setActive() {
      this.dropMenuData.forEach((row) => {
        if (Array.isArray(this.value)) {
          let index = this.value.findIndex((rw) => rw === row[this.tabProps.value]);
          this.$set(row, 'active', index === -1);
        } else {
          this.$set(row, 'active', this.value === row[this.tabProps.value]);
          if (this.value === row[this.tabProps.value]) {
            this.currentMenu = row;
            this.$emit('on-change', row);
          }
        }
      });
    },
  },
  watch: {
    tabList: {
      handler(val) {
        if (!val) return;
        this.dropMenuData = val.slice(this.maxTagCount, val.length);
        //避免dropMenuData已经全部置为false,但currentMenu有属性导致上次的搜索条件依旧显示
        let findActiveIndex = this.dropMenuData.findIndex((row) => {
          return row.active === true;
        });
        findActiveIndex === -1 ? (this.currentMenu = {}) : '';
      },
      deep: true,
      immediate: true,
    },
    value: {
      handler() {
        this.setActive();
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .asset-select-container {
    color: var(--color-primary);
    &:hover {
      .select();
    }
  }
  .select {
    background: #17497e;
    border: 1px solid var(--color-primary);
    border-radius: 4px;
    color: #ffffff;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .asset-select-container {
    color: var(--color-primary);
    &:hover {
      .select();
    }
  }
  .select {
    border: 1px solid var(--color-primary);
    background: rgba(26, 116, 231, 0.1);
    color: var(--color-primary);
  }
}

.asset-select-container {
  position: relative;
  height: 34px;
  color: var(--color-primary);
  padding: 0 20px;
  &:hover {
    .select();
  }
}
.select {
  // background: #17497E;
  // border: 1px solid var(--color-primary);
  border-radius: 4px;
  color: #ffffff;
}
.color-filter {
  color: #1d84a0;
  &:hover {
    color: #539aea;
  }
}
</style>
