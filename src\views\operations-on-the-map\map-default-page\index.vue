<template>
  <div class="map-wrap" ref="mapWrap">
    <mapBase
      ref="mapBase"
      :idlerWheel="true"
      :siteListFlat="siteListFlat"
      :needFilterLayer="true"
      :positionPoints.sync="positionPoints"
      :currentSelectDevice.sync="currentSelectDevice"
      :mapLayerConfig="{ ...mapLayerConfig }"
      :disableScroll="false"
      :sectionName="sectionName"
      :layerCheckedNames="layerCheckedNames"
      :layerAttrUnCheckeds="layerAttrUnCheckeds"
      :coveragePoint="coveragePoint"
      :currentClickIndex.sync="currentClickIndex"
      :timeAnalys="true"
      @selectionResult="selectionResult"
      @closeMapTool="mapToolVisibleHandlebar"
      @updateLayerCheckedNames="updateLayerCheckedNames"
      @setPlayingIds="setPlayingIds"
      @chooseMapItem="chooseMapItem"
    />
    <!-- 右上角-操作工具栏 -->
    <div class="map-tab" v-if="!isTrackModel">
      <div
        class="location"
        @click="showOrHideLocationPanel()"
        :title="regionName"
      >
        <span>{{ regionName }}</span>
        <ui-icon
          class="toggle-icon"
          type="jiantou"
          :class="{ open: isShowLocationPanel }"
        />
      </div>
      <div
        v-for="(e, index) in tabTitle"
        :class="[cur == index ? 'active-tab' : '']"
        :key="index"
      >
        <div v-if="index === 0">
          <div class="tab-title">
            <span class="layer" @click.stop="check(index)">
              <ui-icon :type="e.icon" />
              {{ e.name }}
            </span>
          </div>
          <div v-if="cur === 0" class="layer-container">
            <!-- 全选慢死了，隐藏，不确定后期还要不要，不删先 -->
            <!-- <div class="layer-all">
                        <Checkbox
                            v-model="layerCheckAll"
                            :indeterminate="allIndeter"
                            @on-change="checkAllGroupChange"
                            >全部图层</Checkbox
                        >
                        </div> -->
            <div
              class="layer-item"
              v-for="(layerItem, layerIndex) in layerList"
              :key="layerIndex"
            >
              <div class="layer-item-header">
                <Checkbox
                  v-model="layerItem.isCheckAll"
                  :indeterminate="layerItem.typeIndeter"
                  @on-change="(e) => checkChange(e, layerItem.name)"
                  >{{ layerItem.name }}</Checkbox
                >
                <ui-icon
                  type="jiantou"
                  @click.native="layerItemHandler(layerIndex)"
                  :class="{
                    arrowTransform: layerItem.isShow,
                    arrowTransformReturn: !layerItem.isShow,
                  }"
                />
              </div>
              <transition-group name="draw">
                <div
                  class="layer-item-container"
                  v-show="layerItem.isShow"
                  :key="layerItem.name"
                >
                  <CheckboxGroup
                    v-model="layerItem.checkedList"
                    @on-change="(e) => checkGroupChange(e, layerItem.name)"
                  >
                    <template v-for="(checkItem, checkIndex) in layerItem.list">
                      <Checkbox :label="checkItem.label" :key="checkIndex">
                        <ui-icon
                          :type="checkItem.icon"
                          :color="checkItem.color"
                          :size="14"
                        />
                        <span>{{ checkItem.name }}</span>
                      </Checkbox>
                    </template>
                  </CheckboxGroup>
                </div>
              </transition-group>
            </div>
            <div
              class="layer-item"
              v-for="(layerItem, layerIndex) in pointAttrList"
              :key="layerIndex + 1"
            >
              <div class="layer-item-header">
                <span>{{ layerItem.name }}</span>
                <ui-icon
                  type="jiantou"
                  @click.native="attrItemHandler(layerIndex)"
                  :class="{
                    arrowTransform: layerItem.isShow,
                    arrowTransformReturn: !layerItem.isShow,
                  }"
                />
              </div>
              <transition-group name="draw">
                <div
                  class="layer-item-container"
                  v-show="layerItem.isShow"
                  :key="layerItem.name"
                >
                  <CheckboxGroup v-model="layerItem.checkedList">
                    <template v-for="(checkItem, checkIndex) in layerItem.list">
                      <Checkbox :label="checkItem.label" :key="checkIndex">
                        <span>{{ checkItem.name }}</span>
                      </Checkbox>
                    </template>
                  </CheckboxGroup>
                </div>
              </transition-group>
            </div>
            <div class="layer-item">
              <div class="layer-item-header">
                <span>场所类型</span>
                <ui-icon
                  type="jiantou"
                  @click.native="placeArrow = !placeArrow"
                  :class="{
                    arrowTransform: placeArrow,
                    arrowTransformReturn: !placeArrow,
                  }"
                />
              </div>
              <ul class="classes-point-list">
                <li class="place-li">
                  <div class="place-box" v-show="placeArrow">
                    <Checkbox
                      v-model="item.checked"
                      v-for="(item, index) in placeLayerList"
                      :value="index"
                      :key="item.name"
                      :label="item.name"
                      @on-change="placeLayerChange(item.checked, item.name)"
                    >
                      <ui-icon
                        :type="item.icon"
                        :color="item.color"
                        :size="14"
                      />
                      <span>{{ item.name }}</span>
                    </Checkbox>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div v-if="index === 1" class="map-tap-dropdown1">
          <ui-icon :type="e.icon" />
          <span @click="check(index)">
            {{ e.name }}
          </span>
          <Dropdown v-if="index === 1" trigger="custom" :visible="cur === 1">
            <DropdownMenu slot="list">
              <DropdownItem @click.native="toolSet('measuringDistance')">
                <ui-icon type="ceju" />
                <span>测距</span>
              </DropdownItem>
              <DropdownItem
                @click.native="mapToolVisibleHandlebar(true)"
                :selected="mapLayerConfig.mapToolVisible"
              >
                <ui-icon type="gateway" />
                <span>框选</span>
              </DropdownItem>
              <DropdownItem @click.native="toolSet('clearDraw')">
                <ui-icon type="xiangpi1" />
                <span>清除</span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>

        <div v-if="index === 2" class="map-tap-dropdown2">
          <ui-icon :type="e.icon" />
          <span @click="check(index)">
            {{ e.name }}
          </span>
          <Dropdown v-if="index === 2" :visible="cur === 2" trigger="custom">
            <DropdownMenu slot="list">
              <DropdownItem>
                <ui-icon type="tubiao_ditu" />
                <span>2D</span>
              </DropdownItem>
              <DropdownItem>
                <ui-icon type="d" />
                <span> 2.5D </span>
              </DropdownItem>
              <DropdownItem>
                <ui-icon type="weixing" />
                <span> 卫星 </span>
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>

        <div v-if="index > 2">
          <div class="tab-title" v-if="e.permission" v-permission="[e.permission]">
            <span class="layer" :title="e.name" @click.stop="check(index)">
              <ui-icon :type="e.icon" />
            </span>
          </div>
          <div class="tab-title" v-else>
            <span class="layer" :title="e.name" @click.stop="check(index)">
              <ui-icon :type="e.icon" />
            </span>
          </div>
          <div v-if="cur === 5" class="layer-shuqian">
            <div class="sq_title">地图书签</div>
            <div class="sq_name">
              <Input
                class="mr_10"
                placeholder="请输入地图书签名称"
                v-model="mapBookMarkName"
              />
              <Button type="primary" @click="saveBookMark">添加</Button>
            </div>
            <div
              v-if="mapBookList?.length > 0"
              class="sq_list"
              v-scroll
              style="max-height: 300px"
            >
              <div
                v-for="item in mapBookList"
                :key="item.id"
                style="width: 100%"
              >
                <div
                  class="book-mark-item"
                  v-if="curEditMapBookMark.id !== item.id"
                >
                  <div
                    class="map-book-name"
                    :title="item.name"
                    @click="showMapBookmark(item)"
                  >
                    <i class="iconfont icon-xingzhuangjiehe"></i>
                    <i
                      class="iconfont i-moren"
                      v-if="item.mapSetting === 1"
                    ></i>
                    {{ item.name }}
                  </div>
                  <span class="toolbar">
                    <i
                      class="iconfont icon-bianji"
                      title="重命名"
                      @click="editMapBook(item, true)"
                    ></i>
                    <i
                      class="iconfont icon-shanchu"
                      title="删除"
                      @click="deleteMapBookMark(item)"
                    ></i>
                    <i
                      class="default"
                      :title="[item.mapSetting === 1 ? '取消默认' : '设为默认']"
                      @click="setMapBookmark(item)"
                      >{{ item.mapSetting === 1 ? "取消默认" : "设为默认" }}</i
                    >
                  </span>
                </div>

                <div
                  v-if="curEditMapBookMark.id === item.id"
                  class="edit-container"
                >
                  <i class="iconfont icon-xingzhuangjiehe"></i>
                  <Input v-model="editBookmarkName" />
                  <i
                    class="iconfont icon-baocun"
                    title="保存"
                    @click="saveBookMark(item)"
                  ></i>
                  <i
                    class="iconfont icon-guanbi"
                    title="取消"
                    @click="editMapBook(item, false)"
                  ></i>
                </div>
              </div>
            </div>
            <div v-else class="no-result">暂无数据</div>
          </div>
        </div>
      </div>
      <!--区域选择-->
      <div class="locationPanel" v-if="isShowLocationPanel">
        <div class="city-name" @click="changeRegionCity">
          {{ regionData.name }}
        </div>
        <ul class="region-list">
          <li
            class="list-item"
            :title="item.name"
            :class="{ active: index === regionIndex }"
            v-for="(item, index) in regionData.districts"
            :key="index"
            @click="changeRegion(item, index)"
          >
            {{ item.name }}
          </li>
        </ul>
      </div>
    </div>
    <!-- 左上角-搜索 -->
    <div class="general-search" v-if="!isTrackModel">
      <Dropdown
        trigger="custom"
        placement="bottom-start"
        :visible="showPictures"
        @on-clickoutside="
          () => {
            showPictures = false;
            $refs['searchInput'].blur();
          }
        "
      >
        <Dropdown
          class="search-type"
          trigger="custom"
          :visible="searchTypeVisible"
          @on-clickoutside="
            () => {
              searchTypeVisible = false;
            }
          "
        >
          <span
            class="ml-10"
            @click.stop="
              () => {
                searchTypeVisible = !searchTypeVisible;
              }
            "
          >
            <i class="iconfont" :class="searchTypeObj.iconName"></i>
            {{ searchTypeObj.label }}
            <Icon type="ios-arrow-down"></Icon>
          </span>
          <template #list>
            <DropdownMenu>
              <DropdownItem
                v-for="item in menuList"
                v-permission="item.permission"
                :key="item.label"
                @click.native="changeType(item)"
                ><i :class="item.iconName"></i>{{ item.label }}</DropdownItem
              >
            </DropdownMenu>
          </template>
        </Dropdown>
        <Input
          v-if="showPlaceBox === 'place'"
          class="search-input"
          ref="searchInput"
          placeholder="输入场所资源"
          v-model.trim="searchPrams.keyWords"
          :maxlength="50"
          clearable
          @on-enter="placeSearchHandler"
          @on-clear="resetPlaceSearchHandler"
        >
          <Button
            slot="append"
            icon="ios-search"
            @click="placeSearchHandler"
          ></Button>
        </Input>
        <Input
          v-if="showPlaceBox === 'device'"
          class="search-input"
          ref="searchInput"
          :placeholder="searchTypeObj.placeholder"
          v-model.trim="searchPrams.keyWords"
          :maxlength="50"
          clearable
          @on-enter="mapSearchHandler(true)"
          @on-clear="clearAll"
        >
          <Button
            slot="append"
            icon="ios-search"
            @click="mapSearchHandler(true)"
          ></Button>
        </Input>
        <template v-if="showPlaceBox === 'resource'">
          <Input
            class="search-input"
            ref="searchInput"
            :placeholder="
              normalSearchImageList.length > 0 ? '' : searchTypeObj.placeholder
            "
            v-model.trim="searchPrams2.keyWords"
            :maxlength="50"
            clearable
            @on-enter="getResourceSearchHandler"
            @on-clear="clearResourceHandler"
          >
            <Button
              slot="append"
              icon="ios-search"
              @click="getResourceSearchHandler"
            ></Button>
          </Input>
          <div
            class="pic-box"
            @click.prevent="pictureSearchHandle"
            v-if="!!normalSearchImageList.length"
          >
            <div class="pic-small">
              <img :src="normalSearchImageList[0].imageUrl" alt />
              <i
                class="pic-small-after"
                v-if="normalSearchImageList.length > 1"
                >{{ normalSearchImageList.length }}</i
              >
            </div>
          </div>
          <i
            class="iconfont icon-xiangji"
            @click.prevent="pictureSearchHandle"
          ></i>
          <DropdownMenu slot="list">
            <search-pictures
              class="resource-search-pictures"
              ref="searchPictures"
              style="width: 410px"
              @clearPreview="clearPreview"
              @clearUrl="clearUrlPictures"
              @imgUrlChange="imgUrlChange"
              @cancel="cancelSearchPic"
              :pic-data="picData"
              fight
              lookPictures
              @deleteImgUrl="deleteImgUrl"
              @search="uploadImgSearchHandle"
              size="small"
            />
          </DropdownMenu>
        </template>
      </Dropdown>
    </div>
    <!-- 普通搜索结果 -->
    <div
      class="search-result"
      v-if="!isTrackModel"
      :style="{
        width: searchType !== 'resource' ? resultWidth + 'px' : '410px',
      }"
    >
      <template v-if="searchType === 'device'">
        <device
          class="search-result-content"
          ref="device"
          :isOpen.sync="isGeneralOpen"
          :searchPrams="searchPrams"
          :playingIds="playingIds"
          @openDeviceDom="openDeviceDom"
          @mapSelectDevice="mapSelectDevice"
        ></device>
      </template>
      <template v-if="searchType === 'place'">
        <place
          class="search-result-content"
          ref="place"
          :isOpen.sync="isGeneralOpen"
          :searchPrams="searchPrams"
          :adcode="aoiParams.adcode"
          :citycode="aoiParams.citycode"
          @setPolygon="setPolygon"
          @aoiModelShow="aoiModelShow"
          @removePlaceLayer="removePlaceLayer"
          @mapPanTo="mapPanTo"
        ></place>
      </template>
      <template v-if="searchType === 'points'">
        <policePoint
          class="search-result-content"
          ref="points"
          type="points"
          :isOpen.sync="isGeneralOpen"
          :searchPrams="searchPrams"
        ></policePoint>
      </template>
      <template v-if="searchType === 'areas'">
        <areaContent
          class="search-result-content"
          ref="areas"
          type="points"
          :isOpen.sync="isGeneralOpen"
          :searchPrams="searchPrams"
        ></areaContent>
      </template>
      <template v-if="searchType === 'resource' && showResourceContent">
        <Menu
          :active-name="sectionName"
          width="50px"
          @on-select="selectItemHandle"
          :style="{ 'pointer-events': 'auto' }"
          class="search-menu"
        >
          <MenuItem
            v-for="(item, index) in resourceMenuList"
            :key="index"
            :name="item.name"
            v-permission="item.permission"
            :class="!!item.num ? 'active-search' : ''"
          >
            <Tooltip :content="item.label" placement="right" theme="light">
              <i class="iconfont" :class="item.iconName"></i>
            </Tooltip>
          </MenuItem>
        </Menu>
        <component
          class="resource-result-content"
          :ref="sectionName"
          :is="sectionName"
          :currentClickIndex="currentClickIndex"
          :searchPrams="searchPrams2"
          @chooseMapItem="chooseMapItem"
          @mapResultHandler="mapResultHandler"
        ></component>
      </template>
      <toggle-button
        v-if="['device', 'place'].includes(searchTypeObj.name) && isGeneralOpen"
        class="toggle-w"
        :boxWidth.sync="resultWidth"
        :defaultWidth="410"
      ></toggle-button>
    </div>
    <ui-loading v-if="loading"></ui-loading>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapGetters } from "vuex";
import {
  getSimpleDeviceList,
  bookmarkAdd,
  enableMapSetting,
  bookmarkList,
  bookmarkDel,
  bookmarkUpdate,
  getDataTotal,
  getaoi,
} from "@/api/operationsOnTheMap";
import mapBase from "@/components/map/index.vue";
import searchPictures from "@/views/wisdom-cloud-search/cloud-default-page/components/search-pictures.vue";
import device from "./components/general-search-content/device-content.vue";
import place from "./components/general-search-content/place-content.vue";
import policePoint from "./components/general-search-content/police-point-content.vue";
import areaContent from "./components/general-search-content/area-content.vue";
import toggleButton from "@/components/toggle-button.vue";
import face from "./components/general-search-content/face-content.vue";
import wifi from "./components/general-search-content/wifi-content.vue";
import vehicle from "./components/general-search-content/vehicle-content.vue";
import humanbody from "./components/general-search-content/humanBody-content.vue";
import nonmotorVehicle from "./components/general-search-content/nonmotorVehicle-content.vue";
import electric from "./components/general-search-content/electric-content.vue";
import rfid from "./components/general-search-content/rfid-content.vue";
import gps from "./components/general-search-content/gps-content.vue";
import etc from "./components/general-search-content/etc-content.vue";

import { tajectoryTypeMap } from "@/components/gpt/enum";

export default {
  name: "map-default-page",
  components: {
    mapBase,
    device, //设备模块
    toggleButton,
    place,
    policePoint,
    areaContent,
    face, //人脸模块
    // policeServiceContent, //警务模块
    vehicle, //车辆模块
    wifi, //wifi模块
    humanbody, // 人体模块
    rfid, // rfid模块
    nonmotorVehicle,
    electric, //电围模块
    searchPictures,
    gps,
    etc,
  },
  computed: {
    // 资源图层-选中的图层名称
    layerCheckedNames() {
      const { layerList } = this;
      let list = [];
      layerList.forEach((e) => {
        list = list.concat(e.checkedList);
      });
      return list;
    },
    // 资源图层- 属性筛选 - 未勾选属性
    layerAttrUnCheckeds() {
      const { pointAttrList } = this;
      let list = [];
      pointAttrList.forEach((e) => {
        let allList = e.list.map((v) => v.label);
        const unCheckedList = allList.filter((v) => !e.checkedList.includes(v));
        unCheckedList.length &&
          list.push({
            attrKey: e.attrKey,
            unCheckedList,
          });
      });
      return list;
    },
    ...mapGetters({
      userInfo: "userInfo",
      individuation: "individuation",
      pointPlaceList: "dictionary/getPointPlaceList", //点位所在场所
      constructionProjectList: "dictionary/getConstructionProjectList", //点位建设项目
      constructionUnitList: "dictionary/getConstructionUnitList", //点位建设单位
      operationUnitList: "dictionary/getOperationUnitList", //点位运维单位
      globalObj: "systemParam/globalObj",
      trajectoryParams: "gpt/getTrajectoryParams",
    }),
  },
  data() {
    return {
      // 工具栏
      tabTitle: [
        { name: "资源图层", icon: "tuceng" },
        { name: "工具合集", icon: "gongjuxiang" },
        { name: "地图模式", icon: "ditu" },
        { name: "保存图片", icon: "baocun1" },
        { name: "全屏", icon: "quanping_xian" },
        { name: "地图书签", icon: "xingzhuangjiehe", permission:'map-book' },
      ],
      cur: -1, //默认选中第一个tab
      coveragePoint: {
        level1: true,
        level2: true,
        level3: true,
        nolevel: true,
        isCommunity: true,
      },
      layerList: [
        {
          name: "设备",
          isShow: true,
          list: [
            {
              name: "枪机",
              icon: "shebeizichan",
              color: "#187AE4",
              label: "Camera_QiangJi",
            },
            {
              name: "球机",
              icon: "qiuji",
              color: "#187AE4",
              label: "Camera_QiuJi",
            },
            {
              name: "人脸卡口",
              icon: "renlian1",
              color: "#48BAFF ",
              label: "Camera_Face",
            },
            {
              name: "车辆卡口",
              icon: "qiche",
              color: "#1faf8a",
              label: "Camera_Vehicle",
            },
            {
              name: "Wi-Fi设备",
              icon: "wifi",
              color: "#914FFF",
              label: "Camera_Wifi",
            },
            {
              name: "RFID设备",
              icon: "RFID",
              color: "#8173FF ",
              label: "Camera_RFID",
            },
            {
              name: "电围设备",
              icon: "ZM-dianwei",
              color: "#614FFF",
              label: "Camera_Electric",
            },
            {
              name: "ETC设备",
              icon: "a-ETC1x",
              color: "#48BAFF",
              label: "Camera_ETC",
            },
          ],
          // 默认选中设备
          checkedList: [
            "Camera_QiangJi",
            "Camera_QiuJi",
            "Camera_Face",
            "Camera_Vehicle",
            "Camera_ETC",
          ],
          isCheckAll: false,
          typeIndeter: true,
        },
        // {
        //   name: "场所",
        //   isShow: true,
        //   list: [
        //     {
        //       name: "酒店",
        //       icon: "hotel",
        //       color: "#EB8A5D",
        //       label: "Place_Hotel",
        //     },
        //     {
        //       name: "网吧",
        //       icon: "diannao",
        //       color: "#EB6C6C",
        //       label: "Place_InterBar",
        //     },
        //     {
        //       name: "政府机关",
        //       icon: "gejizhengfu",
        //       color: "#187AE4",
        //       label: "Place_Government",
        //     },
        //     {
        //       name: "学校",
        //       icon: "xuexiao",
        //       color: "#1faf8a",
        //       label: "Place_School",
        //     },
        //     {
        //       name: "重点场所",
        //       icon: "yishoucang",
        //       color: "#EA4A36",
        //       label: "Place_Key",
        //     },
        //   ],
        //   checkedList: [
        //     "Place_Hotel",
        //     "Place_InterBar",
        //     "Place_Government",
        //     "Place_School",
        //     "Place_Key",
        //   ],
        //   isCheckAll: true,
        //   typeIndeter: false,
        // },
      ],
      // 地图配置信息
      mapLayerConfig: {
        mapToolVisible: false, // 底部框选操作栏
        resultOrderIndex: true, //搜索结果排序
      },
      // 资源搜索
      menuList: [
        {
          label: "设备",
          value: 7,
          iconName: "iconfont icon-shebeizichan mr-5",
          name: "device",
          numName: "deviceRecord",
          placeholder: "输入设备资源",
          permission: "yes",
        },
        {
          label: "场所",
          value: 8,
          iconName: "iconfontconfigure icon-changsuo mr-5",
          name: "place",
          numName: "placeRecord",
          placeholder: "输入场所资源",
          permission: "yes",
        },
        {
          label: "资源",
          value: 9,
          iconName: "iconfontconfigure icon-ziyuanmingxi mr-5",
          name: "resource",
          numName: "resourceRecord",
          placeholder: "搜资源",
          permission: "yes",
        },
        // 1.1 兴趣点兴趣面 暂时关闭
        // {  label: "兴趣点",  value: 9,  iconName: "iconfont icon-gejizhengfu  mr-5",  name: "points",  numName: "pointsRecord",  placeholder: "输入关键字",  permission: "yes",},
        // {  label: "兴趣面",  value: 10,  iconName: "iconfont icon-ditu  mr-5",  name: "areas",  numName: "areasRecord",  placeholder: "输入关键字",  permission: "yes",},
      ],
      // 资源数据类型
      resourceMenuList: [
        {
          label: "人像",
          value: 2,
          algorithmType: "1",
          iconName: "icon-yonghu",
          name: "face",
          numName: "face",
          order: "desc",
          permission: ["map-face"],
        },
        {
          label: "车辆",
          value: 3,
          algorithmType: "2",
          iconName: "icon-cheliang",
          name: "vehicle",
          numName: "vehicleRecord",
          order: "desc",
          permission: ["map-vehicle"],
        },
        {
          label: "人体",
          value: 9,
          algorithmType: "3",
          iconName: "icon-renti",
          name: "humanbody",
          numName: "humanRecord",
          order: "desc",
          permission: ["map-humanBody"],
        },
        {
          label: "非机动车",
          value: 10,
          algorithmType: "4",
          iconName: "icon-feijidongche",
          name: "nonmotorVehicle",
          numName: "nonmotorRecord",
          order: "desc",
          permission: ["map-nonmotorVehicle"],
        },
        {
          label: "Wi-Fi",
          value: 4,
          iconName: "icon-wifi",
          name: "wifi",
          numName: "wifiRecord",
          order: "desc",
          permission: ["map-mac"],
        },
        {
          label: "RFID",
          value: 5,
          iconName: "icon-RFID",
          name: "rfid",
          numName: "rfidRecord",
          order: "desc",
          permission: ["map-rfid"],
        },
        {
          label: "电围",
          value: 6,
          iconName: "icon-ZM-dianwei",
          name: "electric",
          numName: "electricCircumferenceRecord",
          order: "desc",
          permission: ["map-electric"],
        },
        {
          label: "GPS",
          value: 7,
          iconName: "icon-gps",
          name: "gps",
          numName: "gpsRecord",
          order: "desc",
          permission: ["map-gps"],
        },
        {
          label: "ETC",
          value: 8,
          iconName: "icon-a-ETC1x",
          name: "etc",
          numName: "etcRecord",
          order: "desc",
          permission: ["map-etc"],
        },
      ],
      // 场所类型展开
      placeArrow: true,
      positionPoints: [],
      sectionName: "",
      updateLayerId: "",
      currentClickIndex: -1,
      searchType: "device",
      searchPrams: {
        keyWords: "",
      },
      searchPrams2: {
        // 以图搜索参数
        algorithmType: "1",
        similarity: 85,
        urlList: [],
        keyWords: "",
      },
      siteListFlat: [],
      allIndeter: true,
      layerCheckAll: false,
      regionName: "",
      regionIndex: -1,
      regionData: {
        name: "",
        districts: [{ name: "" }],
      },
      isShowLocationPanel: false,
      searchTypeVisible: false,
      searchTypeObj: {},
      currentSelectDevice: {},
      isGeneralOpen: false,
      resultWidth: 410,
      playingIds: [],

      //场所类型
      loading: false,
      placeLayerList: [
        {
          name: "购物服务",
          color: "#F37A7A",
          data: [],
          icon: "shop",
          checked: false,
          layerType: "placeShop",
          firstLevel: "67021",
        },
        {
          name: "教育培训",
          color: "#1FAF8A",
          data: [],
          icon: "education",
          checked: false,
          layerType: "placeEdu",
          firstLevel: "67012",
        },
        {
          name: "政府机构",
          color: "#2C86F8",
          data: [],
          icon: "gejizhengfu",
          checked: false,
          layerType: "placeGovernment",
          firstLevel: "67011",
        },
        {
          name: "房产小区",
          color: "#2AB2F4",
          data: [],
          icon: "community",
          checked: false,
          layerType: "placeHouse",
          firstLevel: "67010",
        },
        {
          name: "医疗保健",
          color: "#EA4A36",
          data: [],
          icon: "medical",
          checked: false,
          layerType: "placeMedical",
          firstLevel: "67008",
        },
        {
          name: "公司企业",
          color: "#8C80FB",
          data: [],
          icon: "company",
          checked: false,
          layerType: "placeCompany",
          firstLevel: "67005",
        },
        {
          name: "住宿服务",
          color: "#C832DC",
          data: [],
          icon: "hotel1",
          checked: false,
          layerType: "placeHotel",
          firstLevel: "67004",
        },
        {
          name: "交通设施",
          color: "#D82B84",
          data: [],
          icon: "traffic",
          checked: false,
          layerType: "placeTraffic",
          firstLevel: "67002",
        },
      ],
      placeLayerIndex: -1,
      // 当前选中的场所
      nowSelectPlaceType: "",
      aoiParams: {
        pageSize: 500,
        pageNumber: 1,
        firstLevel: "",
        adcode: "",
        citycode: "320100",
        name: "",
        queryLableType: "1",
      },
      // 点位类型
      pointAttrList: [
        {
          name: "点位状态",
          isShow: true,
          attrKey: "Status",
          list: [
            { name: "在线", label: "0" },
            { name: "离线", label: "1" },
            { name: "异常", label: "" },
          ],
          checkedList: ["0", "1", ""],
        },
        {
          name: "点位所在场所",
          isShow: true,
          attrKey: "sx_place",
          dictionary: "pointPlaceList",
          list: [],
          checkedList: [],
        },
        {
          name: "点位建设项目",
          isShow: false,
          attrKey: "sx_project",
          dictionary: "constructionProjectList",
          list: [],
          checkedList: [],
        },
        {
          name: "点位建设单位",
          isShow: false,
          attrKey: "sx_construction_unit",
          dictionary: "constructionUnitList",
          list: [],
          checkedList: [],
        },
        {
          name: "点位维保",
          isShow: false,
          attrKey: "sx_warranty_period",
          list: [
            { name: "在保", label: "1" },
            { name: "过保", label: "0" },
            { name: "其他", label: "" },
          ],
          checkedList: ["1", "0", ""],
        },
        {
          name: "点位运维单位",
          isShow: false,
          attrKey: "sx_operation_unit",
          dictionary: "operationUnitList",
          list: [],
          checkedList: [],
        },
        {
          name: "点位类型",
          isShow: true,
          attrKey: "sbgnlx",
          list: [
            { name: "一类点位", label: "1" },
            { name: "二类点位", label: "2" },
            { name: "三类点位", label: "3" },
            { name: "未启用点位", label: "" },
          ],
          checkedList: ["1", "2", "3", ""],
        },
        {
          name: "社会资源整合点位",
          isShow: true,
          attrKey: "isCommunity",
          list: [
            { name: "是", label: "1" },
            { name: "否", label: "0" },
          ],
          checkedList: ["1", "0"],
        },
      ],
      showPlaceBox: "device",
      isTrackModel: false, // 轨迹模式，隐藏掉其他操作
      mapBookList: [], // 地图书签
      mapBookMarkName: "",
      editBookmarkName: "",
      curEditMapBookMark: {},
      showResourceContent: false,
      resultInfo: {},
      showPictures: false,
      normalSearchImageList: [],
      resourcePlaceholder: "输入资源",
      picData: {
        algorithmType: "1",
        keyWords: "",
        similarity: 85,
        urlList: ["", "", "", "", ""],
      },
      isFullscreen: false,
    };
  },
  async created() {
    switch (developmentEnvironment) {
      case "320100":
        this.regionName = "南京市";
        break;
      default:
        this.regionName = "蚌埠市";
        break;
    }
    // 权限
    const { searchForPicturesDefaultSimilarity } = this.globalObj;
    this.picData.similarity = Number(searchForPicturesDefaultSimilarity);
    this.searchPrams2.similarity = Number(searchForPicturesDefaultSimilarity);

    let permissionList = this.menuList.filter((d) => this.$_has(d.permission));
    this.searchTypeObj = permissionList.length > 0 ? permissionList[0] : {};
    this.getRegionData();
    this.getMapLayerByTypeSite();
    if (this.$route.query !== null) {
      this.$nextTick(() => {
        this.mapSelectDevice(this.$route.query);
      });
    }
  },
  activated() {
    this.$store.commit("common/setPageType", 1);
    this.setLayoutNoPadding(true);
  },
  async mounted() {
    await this.getDictData();
    // 初始化点位属性过滤列表
    this.initPointAttrList();
  },
  watch: {
    trajectoryParams() {
      if (this.$route.query.from === "gpt") this.initResourceSearchHandler();
    },
    normalSearchImageList: {
      handler(val) {
        if (val.length > 0) {
          this.resourcePlaceholder = "";
        } else {
          this.resourcePlaceholder = "输入场所资源";
        }
      },
    },
    "searchTypeObj.name": {
      // 切换 选择类型时 清除选择的场所内容
      handler(val) {
        if (val !== "place" && this.nowSelectPlaceType) {
          this.$refs.mapBase.removeSetPolygon(this.nowSelectPlaceType);
          this.$refs["mapBase"].closeplaceInfoWindow(); //清除弹框
        }
      },
    },
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false);
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    ...mapMutations({ setResourceCoverage: "map/setResourceCoverage" }),
    ...mapMutations("common", ["setWisdomCloudSearchData"]),
    ...mapMutations("common", ["setNoMenu"]),
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /*==============资源搜索start============*/
    initResourceSearchHandler() {
      const { type, info } = this.trajectoryParams;
      const { urlObj, ...params } = info;
      const { menuName, algorithmType, searchKeyWord } = tajectoryTypeMap[type];
      const keyWords = info?.[searchKeyWord];
      this.clearResourceHandler();
      this.sectionName = menuName;
      this.$nextTick(() => {
        this.searchPrams2 = {
          ...this.searchPrams2,
          ...params,
          algorithmType,
          keyWords,
        };
        if (urlObj) {
          const imgContent = {
            feature: urlObj.feature,
            imageUrl: urlObj.imageUrl || urlObj.fileUrl,
          };
          this.searchPrams2.features = [urlObj.feature];
          this.searchPrams2.urlList[0] = imgContent;
          this.picData.urlList[0] = imgContent;
          this.normalSearchImageList = [imgContent];
        }
        this.mapResourceSearchHandler(true);
        this.changeType(this.menuList[2], false);
      });
    },
    selectItemHandle(sectionName) {
      this.$refs["mapBase"].resetMarker();
      this.sectionName = sectionName;
      this.currentClickIndex = -1;
    },
    // 左侧选中项 点击普通搜索列表
    chooseMapItem(index, tab = "") {
      if (tab == "device") {
        //设备中的tab切换
        this.$refs["mapBase"].resetMarker();
      }
      this.currentClickIndex = index;
    },
    mapResultHandler(list, type) {
      // 获取撒点数据
      if (list.length > 0) {
        this.positionPoints = list
          .filter((e) => e.geoPoint && e.geoPoint.lat && e.geoPoint.lon)
          .map((e) => {
            return {
              ...e,
              lat: e.geoPoint.lat,
              lon: e.geoPoint.lon,
              iconType: type,
            };
          });
      } else {
        this.positionPoints = [];
      }
    },
    getResourceSearchHandler() {
      this.searchPrams2.algorithmType = "1";
      this.searchPrams2.startDate = "";
      this.searchPrams2.endDate = "";

      this.mapResourceSearchHandler();
    },
    mapResourceSearchHandler(isJump = false) {
      // this.resetToolParamsHandle(); //清空数据
      // this.checkAllGroupChange (false);
      this.setResourceCoverage(true);
      const {
        urlList,
        keyWords,
        algorithmType,
        features = [],
        similarity,
      } = this.searchPrams2;

      const { length } = urlList.filter((e) => e);
      if (!isJump && !length && !keyWords) {
        return this.$Message.warning("请输入关键词");
      }

      // this.sectionName =
      //   algorithmType == "1"
      //     ? "face"
      //     : algorithmType == "2"
      //     ? "vehicle"
      //     : algorithmType == "3"
      //     ? "humanbody"
      //     : "nonmotorVehicle";
      // this.dispTime();
      const params = {
        ...this.searchPrams2,
        keyWords,
        similarity: similarity / 100,
        features: features.filter((e) => e),
        // startDate: startDate ? this.$dayjs(startDate).format('YYYY-MM-DD HH:mm:ss') : '',
        // endDate: endDate ? this.$dayjs(endDate).format('YYYY-MM-DD HH:mm:ss') : '',
        startDate: this.searchPrams2.startDate,
        endDate: this.searchPrams2.endDate,
        // 特征值类型 1:人脸 2:车辆 3:人体 4: 非机动车 [一堆神奇数据。。并不知道为什么]
        // featureType: algorithmType !== '2' ? '1' : '2',
        featureType:
          algorithmType == "1"
            ? "1"
            : algorithmType == "2"
            ? "2"
            : algorithmType == "3"
            ? "3"
            : "4",
      };
      // 获取返回结果数量
      getDataTotal(params).then((res) => {
        if (res.code === 200) {
          //const { total, time } = res.data
          this.resultInfo = res.data;
          // this.clearLayerData(); //关闭作战记录图层
          this.handleRedCircle();
        }
      });
      if (this.sectionName === "face") {
      }
      this.$nextTick(() => {
        this.$refs[this.sectionName] && this.$refs[this.sectionName].init();
      });
      this.showResourceContent = true;
      this.showPictures = false;
    },
    // 判断目录是否显示红点
    handleRedCircle() {
      this.resourceMenuList.forEach((item) => {
        if (item.numName == "deviceRecord") {
          let num = 0;
          num =
            parseInt(this.resultInfo["deviceRecord"] || 0) +
            parseInt(this.resultInfo["wifiDevice"] || 0) +
            parseInt(this.resultInfo["rfidDevice"] || 0) +
            parseInt(this.resultInfo["electricCircumferenceDevice"] || 0);
          this.$set(item, "num", num);
        } else {
          this.$set(item, "num", parseInt(this.resultInfo[item.numName] || 0));
        }
      });
    },
    // 打开关闭以图搜图
    pictureSearchHandle() {
      this.showPictures = !this.showPictures;
    },
    clearPreview() {
      this.searchPrams2.urlList = ["", "", "", "", ""];
      this.normalSearchImageList = this.searchPrams2.urlList.filter(
        (item) => !!item
      );
      this.searchPlaceholder = "搜资源、查设备、找位置";
    },
    imgUrlChange(urlList) {
      this.searchPrams2.urlList = [...urlList];
      this.searchPrams2.keyWords = "";
      //   this.searchPrams = {
      //     ...this.searchPrams,
      //     urlList: [...urlList],
      //     keyWords: ''
      //   }
    },
    deleteImgUrl(urlList) {
      this.normalSearchImageList = urlList.filter((item) => !!item);
    },
    // 以图搜图-清空
    clearUrlPictures() {
      const { searchForPicturesDefaultSimilarity } = this.globalObj;
      this.setWisdomCloudSearchData({
        keyWords: "",
        algorithmType: "1",
        similarity: searchForPicturesDefaultSimilarity,
        urlList: [],
      });
      this.picData = {
        keyWords: "",
        algorithmType: "1",
        similarity: searchForPicturesDefaultSimilarity,
        urlList: ["", "", "", "", ""],
      };
      this.searchPrams2 = {
        ...this.picData,
      };
      this.normalSearchImageList = this.searchPrams2.urlList.filter(
        (item) => !!item
      );
      this.clearResourceHandler();
    },
    cancelSearchPic() {
      this.showPictures = false;
    },
    uploadImgSearchHandle(prams) {
      const features = prams.urlList.map((e) => e && e.feature);
      const list = features.filter((item) => item);
      if (!list.length) {
        return this.$Message.warning("请上传图片");
      }
      this.searchPlaceholder = "点击上传图片";
      this.normalSearchImageList = this.searchPrams2.urlList.filter(
        (item) => !!item
      );
      this.searchPrams2 = {
        ...this.searchPrams,
        ...prams,
        features,
      };
      this.searchPrams2.urlList = this.searchPrams2.urlList.filter(
        (item) => !!item
      );
      this.searchPrams2.features = this.searchPrams2.features.filter(
        (item) => !!item
      );
      //     this.sectionName =
      // algorithmType == "1"
      //   ? "face"
      //   : algorithmType == "2"
      //   ? "vehicle"
      //   : algorithmType == "3"
      //   ? "humanbody"
      //   : "nonmotorVehicle";

      this.sectionName =
        this.resourceMenuList.find(
          (el) => el.algorithmType === prams.algorithmType
        )?.name || resourceMenuList[0].name;
      this.setWisdomCloudSearchData(prams);
      this.$nextTick(() => {
        this.mapResourceSearchHandler();
      });
    },
    clearResourceHandler() {
      const { searchForPicturesDefaultSimilarity } = this.globalObj;
      this.picData = {
        keyWords: "",
        algorithmType: "1",
        similarity: searchForPicturesDefaultSimilarity,
        urlList: ["", "", "", "", ""],
        startDate: "",
        endDate: "",
      };
      this.searchPrams2 = {
        ...this.picData,
      };
      this.normalSearchImageList = this.searchPrams2.urlList.filter(
        (item) => !!item
      );
      this.sectionName = "face";
      this.selectDevice = [];
      this.positionPoints = [];
      this.$refs["mapBase"].resetMarker();
      this.setResourceCoverage(false);
    },
    /*==============资源搜索end============*/
    // 初始化点位属性过滤列表
    initPointAttrList() {
      this.pointAttrList.forEach((v) => {
        if (v.dictionary) {
          v.list = this[v.dictionary].map((i) => {
            i.name = i.dataValue;
            i.label = i.dataKey;
            return i;
          });
          v.list.push({ name: "其他", label: "" });
          v.checkedList = this[v.dictionary].map((i) => i.dataKey);
          v.checkedList.push("");
        }
      });
    },
    // 获取区划
    getRegionData() {
      let _mapService = new MapPlatForm.Base.MapService();
      _mapService.searchDistrictsByID(developmentEnvironment, (result) => {
        console.log(result, "result");
        this.regionName = result.name;
        this.regionData = result;
      });
    },
    clearAll() {
      if (this.searchTypeObj.name == "device") {
        this.$refs.device && this.$refs.device.searchName(true);
      } else {
        this.searchType = "";
      }
      this.clearAllData();
    },
    clearAllData() {
      this.setResourceCoverage(false);
    },
    // 撒点数据
    async getMapLayerByTypeSite() {
      let deviceTypes = [1, 2, 3, 4, 5, 11, 16];
      this.siteListFlat = [];
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
        socialResources: this.individuation.hideCommunity ? 0 : 1,
        excludeDeviceTypes: this.individuation.hideDeviceTypes
          ? this.individuation.hideDeviceTypes
          : [],
      };
      let { data } = await getSimpleDeviceList(roleParam);
      data = data.filter((v) => deviceTypes.includes(v[2]));
      this.siteListFlat = [...data];
    },
    async check(index) {
      this.isShowLocationPanel = false;
      if (this.cur === index) {
        this.cur = -1;
      } else {
        this.cur = index;
        switch (index) {
          case 3:
            // 保存图片
            await this.$refs.mapBase.mapToImage();
            this.cur = -1;
            break;
          case 4:
            // 全屏
            this.isFullscreen = !this.isFullscreen;
            this.handleFullScreen();
            this.tabTitle[4].name = this.isFullscreen ? "退出全屏" : "全屏";
            this.tabTitle[4].icon = this.isFullscreen
              ? "tuichuquanping"
              : "quanping_xian";
            this.cur = -1;
            break;
          case 5:
            // 书签
            this.getBookMark();
            break;
        }
      }
    },
    // 框选
    toolSet(operateType) {
      this.mapLayerConfig.mapToolVisible = false;
      this.$refs["mapBase"][operateType]();
      if (operateType === "clearDraw") {
        this.$refs["mapBase"].cancelMeasure();
      }
      this.cur = -1;
    },
    // 控制底部工具栏显示与隐藏
    mapToolVisibleHandlebar(val) {
      this.$refs["mapBase"].cancelMeasure();
      const { mapToolVisible } = this.mapLayerConfig;
      this.mapLayerConfig = {
        ...this.mapLayerConfig,
        mapToolVisible: !mapToolVisible,
        selectionResult: val,
      };
      this.cur = -1;
    },
    layerItemHandler(layerIndex) {
      const { isShow } = this.layerList[layerIndex];
      this.layerList[layerIndex].isShow = !isShow;
    },
    // 点击搜索
    async mapSearchHandler() {
      this.setResourceCoverage(true);

      if (this.searchType == "device") {
        // 二次搜索时触发
        if (this.searchTypeObj.name === "device") {
          this.$refs[this.searchTypeObj.name] &&
            this.$refs[this.searchTypeObj.name].searchName(true);
        }
      }
      this.searchType = "device";
      if (this.searchTypeObj.name === "device") this.isGeneralOpen = true;
    },
    // 地图中当前打开的设备
    openDeviceDom(item) {
      this.$refs["mapBase"].openDeviceDom({ ext: item });
    },
    // 选择设备，地图中只高亮该点位
    mapSelectDevice(item) {
      console.log("item地图信息点击", item);
      this.currentSelectDevice = item;
    },
    setPlayingIds(playingIds) {
      this.playingIds = playingIds;
    },
    // 搜索工具-框选结果
    selectionResult(list) {
      this.mapLayerConfig.selectionResult = true;
    },
    // 全部图层
    checkAllGroupChange(val) {
      if (this.allIndeter) {
        this.layerCheckAll = false;
      } else {
        this.layerCheckAll = val;
      }
      this.allIndeter = false;
      const { layerList } = this;
      this.layerList = layerList.map((e) => {
        return {
          ...e,
          isCheckAll: val,
          typeIndeter: false,
          checkedList: val ? e.list.map((e) => e.label) : [],
        };
      });
    },
    // 图层 每一项
    checkChange(val, name) {
      const { layerList } = this;
      this.resourceCkeck(layerList);
      for (let key in layerList) {
        if (layerList[key].name == name) {
          val
            ? (layerList[key].checkedList = layerList[key].list.map(
                (e) => e.label
              ))
            : (layerList[key].checkedList = []);
          break;
        }
      }
    },
    // 图层管理-单选
    checkGroupChange(checkArray, name) {
      const { layerList } = this;
      for (let key in layerList) {
        if (layerList[key].name == name) {
          checkArray.length === layerList[key].list.length
            ? (layerList[key].isCheckAll = true)
            : (layerList[key].isCheckAll = false);
          checkArray.length === layerList[key].list.length ||
          checkArray.length === 0
            ? (layerList[key].typeIndeter = false)
            : (layerList[key].typeIndeter = true);
          break;
        }
      }
      this.resourceCkeck(layerList);
    },
    // 资源图层-全选效果
    resourceCkeck(layerList) {
      let child = layerList.filter((item) => item.isCheckAll);
      if (child.length === 2) {
        this.allIndeter = false;
        this.layerCheckAll = true;
      } else if (child.length > 0) {
        this.allIndeter = true;
        this.layerCheckAll = false;
      } else {
        this.allIndeter = false;
        this.layerCheckAll = false;
      }
    },
    // 更新图层信息
    updateLayerCheckedNames(layerCheckedNames) {
      const { layerList } = this;
      let layerNameMap = {
        Camera: { name: "设备", checkedList: [] },
        Place: { name: "场所", checkedList: [] },
        Police: { name: "警务信息", checkedList: [] },
      };
      layerCheckedNames.forEach((e) => {
        if (e.includes("Camera")) {
          layerNameMap.Camera.checkedList.push(e);
        } else if (e.includes("Place")) {
          layerNameMap.Place.checkedList.push(e);
        } else {
          layerNameMap.Police.checkedList.push(e);
        }
      });
      for (let key in layerList) {
        if (layerList[key].name == "设备") {
          layerList[key].checkedList = layerNameMap.Camera.checkedList;
        } else if (layerList[key].name == "场所") {
          layerList[key].checkedList = layerNameMap.Place.checkedList;
        } else {
          layerList[key].checkedList = layerNameMap.Police.checkedList;
        }
        layerList[key].checkedList.length === layerList[key].list.length
          ? (layerList[key].isCheckAll = true)
          : (layerList[key].isCheckAll = false);
      }
    },
    showOrHideLocationPanel() {
      this.cur = -1;
      this.isShowLocationPanel = !this.isShowLocationPanel;
    },
    // 区域选择
    changeRegionCity() {
      this.regionIndex = -1;
      this.regionName = this.regionData.name;
      this.isShowLocationPanel = false;
      this.$refs["mapBase"].hideAreaOnMap();
      this.$refs["mapBase"].configDefaultMap();
      //场所范围地区参数更换
      this.aoiParams.citycode = this.regionData.code;
      this.aoiParams.adcode = "";
      this.removePlaceLayer();
    },
    changeRegion(item, index) {
      this.regionIndex = index;
      this.regionName = item.name;
      this.isShowLocationPanel = false;
      this.$refs["mapBase"].showAreaOnMap(item.geometry);
      //场所范围地区参数更换
      this.aoiParams.citycode = "";
      this.aoiParams.adcode = item.code;
      this.removePlaceLayer();
    },
    // 搜索类型切换
    changeType(item, isClear = true) {
      this.searchPrams.keyWords = "";
      this.searchTypeObj = item;
      this.showPlaceBox = item.name;
      if (isClear) this.clearResourceHandler();
      if (item.name == "resource") {
        this.searchTypeVisible = false;
        this.searchType = item.name;
      } else if (item.name == "place") {
        this.searchTypeVisible = false;
        this.searchType = "place";
        this.showResourceContent = false;
      } else {
        this.showResourceContent = false;
        this.searchTypeVisible = false;
        // 清空搜索
        this.clearAll();
        // 清空兴趣面
        this.$refs.mapBase.clearDraw();
        this.$refs.mapBase.clearSelectDraw();
        if (this.searchTypeObj.name == "device") {
          this.searchType = "device";
        } else {
          // 兴趣点/兴趣面
          this.searchType = item.name;
        }
      }
    },
    //左上角场所搜索
    placeSearchHandler() {
      this.isGeneralOpen = true;
      // 防抖 多次点击导致发送请求次数过多卡顿
      Toolkits.FnByShake(500, () => {
        this.$refs["place"].init();
      });
    },
    //清除场所图层
    removePlaceLayer() {
      this.$refs["mapBase"].removeSetPolygonAll(); //清除所有场所图层
      this.$refs["mapBase"].closeplaceInfoWindow(); //清除弹框
      //重置右侧场所资源图层复选条件
      this.aoiParams.firstLevel = "";
      this.placeLayerList.forEach((item) => {
        if (item.checked) {
          item.checked = false;
        }
      });
    },
    //重置场所搜索
    resetPlaceSearchHandler() {
      this.searchPrams.keyWords = "";
      this.placeSearchHandler();
    },
    //点击场所列表打开弹框
    aoiModelShow(item) {
      this.$refs["mapBase"].aoiModelShow(item);
    },
    // 绘制多边形
    setPolygon(data, type, arg) {
      this.nowSelectPlaceType = type;
      this.$refs["mapBase"].setPolygon(data, type, arg);
    },
    mapPanTo(center) {
      this.$refs["mapBase"].mapPanTo(center);
    },
    // 获取场所aoi数据
    getAoiData(placeLayerIndex) {
      const aoiParams = {
        ...this.aoiParams,
      };
      const recursiveAoiData = () => {
        getaoi(aoiParams).then((res) => {
          this.placeLayerList[placeLayerIndex].data = res.data.entities.map(
            (item) => {
              return {
                properties: { ...item },
                geometry: JSON.parse(item.coord),
              };
            }
          );
          // if (this.loading) {
          // this.$refs["mapBase"].addAoiMarkers(
          //   this.placeLayerList[this.placeLayerIndex],
          //   this.placeLayerList[this.placeLayerIndex].layerType
          // );
          this.setPolygon(
            this.placeLayerList[placeLayerIndex],
            this.placeLayerList[placeLayerIndex].layerType
          );
          // }
          if (!res.data.lastPage) {
            aoiParams.pageNumber = aoiParams.pageNumber + 1;
            recursiveAoiData();
          } else {
            aoiParams.pageNumber = 1;
            //   this.loading = false;
          }
        });
      };
      recursiveAoiData();

      // .catch((err) => {
      //   console.error(err);
      // })
      // .finally(() => {
      //   this.loading = false;
      // });
    },
    //场所选择分类
    placeLayerChange(isChecked, name) {
      this.$refs["mapBase"].closeplaceInfoWindow(); //清除弹框
      if (isChecked) {
        this.placeLayerList.forEach((item, index) => {
          if (item.name == name) {
            item.checked = isChecked;
            this.aoiParams.firstLevel = item.firstLevel;
            this.placeLayerIndex = index;
          }
        });
        // this.loading = true;
        const notFrist = this.placeLayerList[this.placeLayerIndex].notFrist;
        if (notFrist)
          this.$refs["mapBase"].showOrHidePlaceLayer(
            this.placeLayerList[this.placeLayerIndex].layerType,
            true
          );
        else {
          this.placeLayerList[this.placeLayerIndex].notFrist = true;
          this.getAoiData(this.placeLayerIndex);
        }
      } else {
        this.placeLayerList.forEach((item, index) => {
          if (item.name == name) {
            item.checked = isChecked;
            this.aoiParams.firstLevel = "";
            this.placeLayerIndex = index;
          }
        });
        //清楚场所图层
        this.$refs["mapBase"].showOrHidePlaceLayer(
          this.placeLayerList[this.placeLayerIndex].layerType,
          false
        );
        // this.loading = false;
      }
    },
    // 点位类型内容展开
    attrItemHandler(layerIndex) {
      const { isShow } = this.pointAttrList[layerIndex];
      this.pointAttrList[layerIndex].isShow = !isShow;
    },
    // 全屏
    handleFullScreen() {
      // const element = this.$refs.mapWrap;
      // 仅放大地图组件 会影响其他组件的显示
      const element = document.body;
      if (document.fullscreenElement || document.fullscreen) {
        this.setNoMenu(false);
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        this.setNoMenu(true);
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen();
        }
      }
    },
    // 地图书签获取
    getBookMark() {
      bookmarkList().then((res) => {
        this.mapBookList = res.data.sort((a, b) => {
          return b.mapSetting - a.mapSetting;
        });
      });
    },
    // 地图书签编辑
    editMapBook(item, bool) {
      this.curEditMapBookMark = bool ? item : {};
      this.editBookmarkName = item.name;
    },
    //删除地图书签
    deleteMapBookMark(item) {
      bookmarkDel(item.id).then((res) => {
        this.mapBookList.some((bookMark, index) => {
          //局部刷新列表，不再请求接口
          if (item.id === bookMark.id) {
            this.mapBookList.splice(index, 1);
            return true;
          }
        });
      });
    },
    //设置书签为默认
    setMapBookmark(item) {
      enableMapSetting({
        id: item.id,
        mapSetting: Number(!item.mapSetting),
      }).then((res) => {
        Number(!item.mapSetting) === 1
          ? this.$Message.success(`设置"${item.name}"书签为默认地图书签`)
          : this.$Message.success(`取消"${item.name}"书签为默认地图书签`);
        this.mapBookList.forEach((bookMark) => {
          //局部刷新列表，不再请求接口
          bookMark.mapSetting =
            bookMark.id === item.id ? Number(!item.mapSetting) : 0;
        });
        this.mapBookList.sort((a, b) => {
          return b.mapSetting - a.mapSetting;
        });
      });
    },
    //定位书签对应的地图位置
    showMapBookmark(item) {
      let centerPoint = item.centerPoint.split(",");
      let point = new NPMap.Geometry.Point(centerPoint[0], centerPoint[1]);
      this.$refs.mapBase.setCenter(point, item.zoom);
      let customParam = JSON.parse(item.customParam);
      this.updateLayerCheckedNames(customParam.layerCheckedNames);
      this.updateLayerAttrChecked(customParam.layerAttrUnCheckeds);
    },
    // 图层点位更新
    updateLayerAttrChecked(layerAttrUnCheckeds) {
      layerAttrUnCheckeds.forEach((e) => {
        this.pointAttrList.forEach((v) => {
          if (v.attrKey == e.attrKey) {
            v.checkedList = v.list
              .map((i) => i.label)
              .filter((v) => !e.unCheckedList.includes(v));
          }
        });
      });
    },
    // 保存地图书签
    saveBookMark(item) {
      let param;
      if (item && item.id) {
        //书签编辑状态
        param = { ...item, name: this.editBookmarkName };
      } else {
        //书签添加状态
        let centerPoint = this.$refs.mapBase.getCenter();
        let customParam = JSON.stringify({
          layerCheckedNames: this.layerCheckedNames,
          layerAttrUnCheckeds: this.layerAttrUnCheckeds,
        });
        param = {
          name: this.mapBookMarkName,
          centerPoint: `${centerPoint.lon},${centerPoint.lat}`,
          zoom: this.$refs.mapBase.getZoom(),
          customParam,
          mapSetting: 0,
        };
      }
      if (param.name === "") {
        this.$Message.warning("地图书签名称不能为空,请重新输入");
        return false;
      }
      let checkNameParam = {
        name: item.id ? this.editBookmarkName : this.mapBookMarkName,
        id: item && item.id,
      };
      // checkBookmarkName(checkNameParam).then(res => {
      // 		if(res.data) {
      if (!item.id) {
        bookmarkAdd(param).then((res) => {
          this.mapBookMarkName = "";
          this.curEditMapBookMark = {};
          this.$Message.success("保存成功");
          this.getBookMark();
        });
      } else {
        bookmarkUpdate(param).then((res) => {
          this.mapBookMarkName = "";
          this.curEditMapBookMark = {};
          this.$Message.success("保存成功");
          item.name = this.editBookmarkName;
        });
      }
      // 		} else {
      // 				this.$Message.warning("书签名称不可重复,请重新输入");
      // 		}
      // })
    },
  },
};
</script>

<style lang="less" scoped>
.map-wrap {
  width: 100%;
  position: relative;

  .active-search {
    position: relative;

    &::before {
      position: absolute;
      right: 3px;
      top: 5px;
      content: "";
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background: #f8775c;
    }
  }

  .map-tab {
    position: absolute;
    right: 20px;
    top: 20px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    display: flex;

    .location {
      margin-left: 10px;

      .toggle-icon {
        &.open {
          transform: rotate(180deg);
        }
      }
    }

    > div {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
      margin: 0 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      .layer {
        position: relative;
      }

      .layer-container {
        position: absolute;
        top: 46px;
        left: 0;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
        border-radius: 2px;
        z-index: 10;
        max-height: calc(~"100vh - 164px");
        overflow-y: auto;

        // 全选慢死了，隐藏，不确定后期还要不要，不删先
        /* .layer-all {
            height: 43px;
            line-height: 43px;
            border-bottom: 1px solid #d3d7de;
            padding: 0 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 14px;
            font-weight: bold;
            } */

        .layer-item {
          height: 100%;

          .layer-item-header {
            padding: 0 16px;
            height: 40px;
            line-height: 40px;
            background: #f1f1f1;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: rgba(0, 0, 0, 0.85);

            .arrowTransform {
              transition: 0.2s;
              transform-origin: center;
              transform: rotateZ(180deg);
            }

            .arrowTransformReturn {
              transition: 0.2s;
              transform-origin: center;
              transform: rotateZ(0deg);
            }
          }

          .layer-item-container {
            /deep/.ivu-checkbox-group {
              display: flex;
              flex-wrap: wrap;
              justify-content: start;
              padding: 16px 16px 0;

              .ivu-checkbox-group-item {
                width: 120px;
                display: flex;
                align-items: center;
                padding-bottom: 16px;
                font-size: 12px;

                .ivu-checkbox {
                  .ivu-checkbox-inner {
                    margin-right: 10px;
                  }
                }

                .iconfont {
                  margin-right: 10px;
                }
              }
            }
          }

          .classes-point-list {
            display: flex;
            flex-wrap: wrap;

            .point-li {
              width: 50%;
              font-size: 12px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.8);
              padding: 8px 0;

              span {
                margin-left: 10px;
              }
            }

            .place-li {
              font-size: 12px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.8);
              padding: 8px 0;
              width: 100%;
              display: flex;

              .place-box {
                display: flex;
                flex-wrap: wrap;
                margin-left: 15px;

                .ivu-checkbox-wrapper {
                  width: 120px;
                  display: flex;
                  align-items: center;
                  padding-bottom: 16px;
                  font-size: 12px;
                }

                .iconfont {
                  margin-right: 10px;
                  margin-left: 5px;
                }
              }
            }
          }

          .draw-enter-active,
          .draw-leave-active {
            transition: all 1s ease;
          }

          .draw-enter,
            .draw-leave-to

            /* .fade-leave-active below version 2.1.8 */ {
            display: none;
          }
        }

        .classes-point {
          border-top: 1px solid #d3d7de;
          color: #000000;
          padding: 10px 16px 12px;

          .tab-title {
            font-weight: 700;
            color: rgba(0, 0, 0, 0.9);
          }

          .classes-point-list {
            display: flex;
            flex-wrap: wrap;

            .point-li {
              width: 50%;
              font-size: 12px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.8);
              padding: 8px 0;

              span {
                margin-left: 10px;
              }
            }

            .place-li {
              font-size: 12px;
              font-weight: 400;
              color: rgba(0, 0, 0, 0.8);
              padding: 8px 0;
              width: 100%;
              display: flex;

              .place-box {
                display: flex;
                flex-wrap: wrap;

                .ivu-checkbox-wrapper {
                  width: 120px;
                  display: flex;
                  align-items: center;
                  padding-bottom: 16px;
                  font-size: 12px;
                }

                .iconfont {
                  margin-right: 10px;
                  margin-left: 5px;
                }
              }
            }
          }
        }

        .pb0 {
          padding-bottom: 0;
        }
      }

      .layer-shuqian {
        position: absolute;
        top: 46px;
        left: 0;
        width: 100%;
        background: #ffffff;
        box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
        border-radius: 2px;
        z-index: 10;
        padding: 10px;

        .mr_10 {
          margin-right: 10px;
        }

        .sq_title {
          font-weight: bold;
          line-height: 30px;
          color: black;
        }

        .sq_name {
          display: flex;
        }

        .sq_list {
          display: flex;
          flex-wrap: wrap;
          margin-top: 10px;

          .toolbar {
            display: none;

            .iconfont {
              color: #2d87f9;
              font-size: 14px;
              margin-left: 5px;
            }
          }

          .map-book-name {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
            height: 100%;
            color: #666666;
          }

          .book-mark-item {
            height: 100%;
            width: 100%;
            cursor: pointer;

            &:hover {
              display: flex;
              align-items: center;
              background: rgba(64, 151, 250, 0.1);

              .toolbar {
                display: flex;
                flex: 0 0 auto;
                align-items: center;
              }
            }

            .icon-xingzhuangjiehe {
              color: #2c86f8;
            }
          }

          .i-moren {
            color: #ff9d46;
            vertical-align: text-bottom;

            &::before {
              content: "默认";
              color: #fff;
              background: #ff9d46;
              margin-left: 5px;
              font-size: 12px;
            }
          }

          .default {
            margin-left: 5px;
            color: #2d87f9;
          }

          .edit-container {
            position: relative;
            display: flex;
            align-items: center;

            .iconfont {
              cursor: pointer;
            }

            .ivu-input-wrapper {
              flex: 1;
              margin: 0 5px;

              &.xui-input-style .xui-input-inner {
                font-size: 12px;
                padding: 0 50px 0 5px;
              }
            }

            .icon-baocun {
              font-size: 14px;
              color: #2d87f9;
            }

            .icon-guanbi {
              color: #a3a7b4;
              margin-left: 5px;
            }
          }
        }

        .no-result {
          height: 100px;
          color: black;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    > li:last-of-type {
      border-right: none;
    }

    .active-tab {
      color: #2c86f8;

      .tab-title {
        .iconfont {
          color: #2c86f8 !important;
        }
      }

      /deep/.ivu-dropdown {
        .ivu-dropdown-rel {
          .iconfont {
            color: #2c86f8 !important;
          }
        }
      }
    }

    .locationPanel {
      position: absolute;
      width: 100%;
      top: 50px;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      padding: 10px;

      .city-name {
        border-bottom: 1px solid rgba(165, 176, 182, 0.31);
        font-weight: bold;
        padding-bottom: 8px;
        cursor: pointer;
      }

      .region-list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 8px;

        .list-item {
          cursor: pointer;
          margin-right: 8px;

          &.active {
            color: #2c86f7;
          }
        }
      }
    }

    .layer-item {
      height: 100%;

      .layer-item-header {
        padding: 0 16px;
        height: 40px;
        line-height: 40px;
        background: #f1f1f1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.85);

        span {
          color: rgba(0, 0, 0, 0.9);
          font-size: 14px;
        }

        .arrowTransform {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(180deg);
        }

        .arrowTransformReturn {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(0deg);
        }
      }

      .layer-item-container {
        /deep/.ivu-checkbox-group {
          display: flex;
          flex-wrap: wrap;
          justify-content: start;
          padding: 10px 16px 0;

          .ivu-checkbox-group-item {
            width: 33.3%;
            display: flex;
            align-items: center;
            padding-bottom: 10px;
            font-size: 12px;
            margin: 0;

            .ivu-checkbox {
              .ivu-checkbox-inner {
                margin-right: 10px;
              }
            }

            .iconfont {
              margin-right: 10px;
            }
          }
        }
      }

      .draw-enter-active,
      .draw-leave-active {
        transition: all 1s ease;
      }

      .draw-enter,
        .draw-leave-to

        /* .fade-leave-active below version 2.1.8 */ {
        display: none;
      }
    }
  }

  .general-search {
    position: absolute;
    left: 20px;
    top: 20px;
    width: auto;
    height: 40px;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    z-index: 999;

    .search-type {
      position: absolute;
      height: 40px;
      top: 0px;
      left: 0;
      z-index: 100;
      cursor: pointer;
      width: 95px;
      line-height: 40px;

      /deep/ .ivu-select-dropdown {
        width: 110px;
        z-index: 999;
      }
    }

    .icon-xiangji {
      position: absolute;
      right: 71px;
      top: 6px;
      z-index: 100;
      font-size: 20px;
      cursor: pointer;
    }

    .pic-box {
      position: absolute;
      height: 40px;
      width: 350px;
      top: 0px;
      left: 80px;
      z-index: 2;
      cursor: pointer;
    }

    .pic-small {
      //   position: absolute;
      width: 30px;
      height: 30px;
      margin: 4px 0 0 3px;
      //   z-index: 2;
      //   top: 5px;
      //   left: 2px;

      img {
        width: 100%;
        height: 100%;
      }

      &-after {
        position: absolute;
        top: 0px;
        left: -2px;
        background: #2c86f7;
        width: 15px;
        height: 15px;
        color: #fff;
        text-align: center;
        line-height: 12px;
      }
    }

    /deep/.ivu-dropdown {
      border-radius: 4px;
    }

    /deep/ .search-input {
      width: 410px;
      height: 40px;

      .ivu-btn {
        width: 100%;
        height: 100%;
        border-radius: 0;
        background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(#5ba3ff),
          to(#2c86f8)
        );
        background: -webkit-linear-gradient(top, #5ba3ff 0%, #2c86f8 100%);
        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
        border: none;
        color: #fff;
        margin: 0;
      }

      .ivu-input {
        height: 100%;
        font-size: 14px;
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        padding-left: 100px;
        padding-right: 50px;
        border: 1px solid #e8eaef;

        &:hover,
        &:focus {
          border-color: #9cbae6;
        }
      }

      .ivu-input[disabled] {
        background-color: #fff !important;
      }

      .ivu-icon {
        height: 40px;
        line-height: 40px;
        right: 30px;
      }

      .ivu-icon-ios-close-circle {
        color: #888888;
      }
    }

    /deep/ .ivu-input-suffix {
      right: 60px;
      display: flex;
      align-items: center;
      z-index: 100;
      cursor: pointer;

      .iconfont {
        font-size: 18px;
        color: #888;
      }
    }

    /deep/.ivu-dropdown {
      .ivu-select-dropdown {
        top: 42px !important;
        z-index: -1 !important;
      }

      .ivu-dropdown-rel {
        .search-input {
          .ivu-input-group-append {
            width: 60px;

            .ivu-icon {
              font-size: 29px;
              color: #ffffff !important;
            }

            .ivu-btn {
              padding: 0;

              .ivu-icon-gaoji:before {
                content: "\F2A8" !important;
                font-size: 0.10417rem;
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }

  .search-result {
    position: absolute;
    left: 20px;
    top: 66px;
    height: calc(~"100% - 120px");
    width: 410px;
    display: flex;
    pointer-events: none;

    .toggle-w {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
      pointer-events: auto;
    }

    &-content {
      flex: 1;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
      border-radius: 0px 0px 4px 4px;
      pointer-events: auto;
      padding-top: 1px;
      flex-shrink: 0;
      position: relative;
    }
  }
}

.map-tap-dropdown2 {
  /deep/ .ivu-select-dropdown {
    top: 40px !important;
    left: 250px !important;
  }

  .ivu-dropdown-menu {
    .ivu-dropdown-item {
      > span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        margin-left: 10px !important;
      }
    }

    .ivu-dropdown-item:hover,
    .ivu-dropdown-item-selected,
    .ivu-dropdown-item-selected:hover {
      background: #2c86f8;

      .iconfont {
        color: #ffffff !important;
      }

      > span {
        color: #ffffff;
      }
    }
  }

  .ivu-dropdown {
    .ivu-dropdown-rel {
      .iconfont {
        color: #888888 !important;
      }
    }
  }
}

.map-tap-dropdown1 {
  /deep/ .ivu-select-dropdown {
    top: 40px !important;
    left: 158px !important;
  }

  .ivu-dropdown-menu {
    .ivu-dropdown-item {
      > span {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        margin-left: 10px !important;
      }
    }

    .ivu-dropdown-item:hover,
    .ivu-dropdown-item-selected,
    .ivu-dropdown-item-selected:hover {
      background: #2c86f8;

      .iconfont {
        color: #ffffff !important;
      }

      > span {
        color: #ffffff;
      }
    }
  }

  .ivu-dropdown {
    .ivu-dropdown-rel {
      .iconfont {
        color: #888888 !important;
      }
    }
  }
}
.resource-search-pictures {
  /deep/ .body {
    .ivu-upload {
      width: 64px !important;
      height: 64px !important;
    }
    .img-content {
      width: 64px !important;
      height: 64px !important;
    }
  }
  /deep/ .footer {
    .ivu-slider {
      width: 100px !important;
    }
  }
}
.resource-result-content {
  width: 350px;
  flex: 1;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
  border-radius: 0px 0px 4px 4px;
  pointer-events: auto;
  padding-top: 1px;
  flex-shrink: 0;
  position: relative;
}
</style>
