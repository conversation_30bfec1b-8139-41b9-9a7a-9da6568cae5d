<template>
  <div class="switch-wrapper inline">
    <span
      v-for="item in dataList"
      :key="item.value"
      class="switch dis-select f-14 t-center"
      :class="activeValue === item.value ? 'active' : ''"
      @click="handleClick(item)"
    >
      {{ item.label }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'ui-switch',
  components: {},
  props: {
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
    value: {},
  },
  data() {
    return {
      dataList: [],
      activeValue: '',
    };
  },
  computed: {},
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.dataList = val;
      },
    },
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.activeValue = val;
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    handleClick(item) {
      if (this.activeValue === item.value) return;
      this.activeValue = item.value;
      this.$emit('input', this.activeValue);
      this.$emit('on-change', this.activeValue);
    },
  },
};
</script>

<style lang="less" scoped>
.switch-wrapper {
  .switch {
    display: inline-block;
    padding: 0 22px 0 22px;
    height: 34px;
    line-height: 34px;
    border: 1px solid var(--border-switch-tag-tab);
    cursor: pointer;
    color: var(--color-switch-tag-tab);
    &:hover {
      .active();
    }
  }
  .switch:first-of-type:not(:last-of-type),
  .switch + .switch:not(:last-of-type) {
    border-right-color: transparent;
  }

  .active {
    background: var(--color-switch-tab-active);
    border: 1px solid var(--color-switch-tab-active);
    opacity: 1;
    color: #ffffff;
  }
}
</style>
