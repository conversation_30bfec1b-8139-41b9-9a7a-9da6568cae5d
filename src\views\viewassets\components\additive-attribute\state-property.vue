<template>
  <div class="state-property">
    <Form class="form" ref="form" autocomplete="off" label-position="right" :model="formCustom">
      <FormItem :required="isRequired('phyStatus')" class="left-item" :label="global.filedEnum.phyStatus">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.phyStatus"
          clearable
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
        >
          <Option v-for="(item, index) in phyList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.phyStatusText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.phyStatus">
          {{ errorData.phyStatus }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('phyStatusExt')" class="left-item" :label="global.filedEnum.phyStatusExt">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.phyStatusExt"
          clearable
          :placeholder="`请选择${global.filedEnum.phyStatusExt}`"
        >
          <Option
            v-for="(item, index) in phyStatusExtList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.phyStatusExtText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.phyStatusExt">
          {{ errorData.phyStatusExt }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sblwzt')" class="left-item" label="设备联网状态">
        <Select v-if="!isView" class="width-lg" v-model="formCustom.sblwzt" clearable placeholder="请选择设备联网状态">
          <Option v-for="(item, index) in sblwztList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sblwztText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sblwzt">
          {{ errorData.sblwzt }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('isOnline')" class="left-item" label="设备是否在线状态">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.isOnline"
          clearable
          placeholder="请选择设备是否在线状态"
        >
          <Option v-for="(item, index) in onlineList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.isOnlineText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.isOnline">
          {{ errorData.isOnline }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('dataStatus')" class="left-item" label="数据采集上传状态">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.dataStatus"
          clearable
          placeholder="请选择数据采集上传状态"
        >
          <Option
            v-for="(item, index) in dataStatusList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.dataStatusText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.dataStatus">
          {{ errorData.dataStatus }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('ptzStatus')" class="left-item" label="设备PTZ控制状态">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.ptzStatus"
          clearable
          placeholder="请选择设备PTZ控制状态"
        >
          <Option
            v-for="(item, index) in ptzStatusList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.ptzStatusText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ptzStatus">
          {{ errorData.ptzStatus }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('monitorTime')" class="left-item" label="当前状态监测时间">
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.monitorTime"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'monitorTime')"
          placeholder="请选择当前状态监测时间"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.monitorTime || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.monitorTime">
          {{ errorData.monitorTime }}
        </div>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    defaultForm: {
      type: Object,
    },
    modalAction: {
      type: String,
    },
    errorData: {
      type: Object,
    },
    allDicData: {
      type: Object,
    },
    // 校验必填字段
    isRequired: {
      type: Function,
    },
  },
  data() {
    return {
      phyList: [
        { label: '在用', value: '1' },
        { label: '维修', value: '2' },
        { label: '拆除', value: '3' },
      ],
      phyStatusExtList: [],
      sblwztList: [
        { label: '在建', value: '0' },
        { label: '已联网', value: '1' },
        { label: '未联网', value: '2' },
        { label: '未知', value: '3' },
      ],
      onlineList: [
        { label: '离线', value: '0' },
        { label: '在线', value: '1' },
      ],
      dataStatusList: [
        { label: '无视频', value: '0' },
        { label: '无图片输出', value: '1' },
        { label: '正常', value: '2' },
      ],
      ptzStatusList: [
        { label: '不可控-故障', value: '0' },
        { label: '可控', value: '1' },
      ],
      formCustom: {
        phyStatus: '',
        phyStatusText: '',
        phyStatusExt: '',
        phyStatusExtText: '',
        sblwzt: '',
        sblwztText: '',
        isOnline: '',
        isOnlineText: '',
        dataStatus: '',
        dataStatusText: '',
        ptzStatus: '',
        ptzStatusText: '',
        monitorTime: '',
      },
      formError: {},
    };
  },
  created() {},
  methods: {
    validate() {
      // this.$refs.form.validate((valid)=>{
      //   if (valid) {

      //   } else {

      //   }
      // })
      this.$emit('putData', this.formCustom);
      return true;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.formCustom = {
        phyStatus: '',
        phyStatusText: '',
        phyStatusExt: '',
        phyStatusExtText: '',
        sblwzt: '',
        sblwztText: '',
        isOnline: '',
        isOnlineText: '',
        dataStatus: '',
        dataStatusText: '',
        ptzStatus: '',
        ptzStatusText: '',
        monitorTime: '',
      };
    },
  },
  watch: {
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          if (length > 0) {
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                this.formCustom[key] = val[key];
              }
            });
          }
        });
      },
      immediate: true,
    },
    errorData: {
      handler(val) {
        let length = Object.keys(val).length;
        if (length > 0) {
          Object.keys(val).forEach((key) => {
            if (this.formCustom.hasOwnProperty(key)) {
              this.formError[key] = val[key];
            }
          });
        } else {
          this.formError = {};
        }
      },
      immediate: true,
      deep: true,
    },
    allDicData: {
      handler(val) {
        for (let i in val) {
          if (this.hasOwnProperty(i)) {
            this[i] = val[i];
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    isView() {
      return this.modalAction === 'view';
    },
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 400px;
@inputWidth: 240px;
.state-property {
  width: 823px;
  height: 586px;
  margin-top: 20px;
  .left-item {
    @{_deep} .ivu-form-item-error-tip {
      margin-left: @leftMargin;
    }
    @{_deep}.ivu-form-item-label {
      float: left;
      width: 400px;
    }
  }
  .error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1;
    padding-top: 5px;
    color: #ed4014;
    width: @inputWidth;
    margin-left: @leftMargin;
  }
}
</style>
