<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      :default-page-data="pageData"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
      @changeMode="changeMode"
      @handleBigImgView="(row) => clickArtificialReviewImage(row, 'viewOnly')"
      @artificialReview="(row) => clickArtificialReviewImage(row)"
      @algorithmsReview="(row) => clickAlgorithmsReview(row)"
    >
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <span
            class="font-active-color mr-sm pointer"
            v-if="activeMode === 'device' && statisticShow"
            @click="updateStatistics"
            >更新统计结果</span
          >
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template slot="checkStatus" slot-scope="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #qualifiedRate="{ row }">
        <span>{{ row.qualifiedRate || 0 }}%</span>
      </template>
      <template #unqualifiedNum="{ row }">
        <span class="font-red">{{ row.unqualifiedNum }}</span>
      </template>
      <template #urlNotUseCount="{ row }">
        <span class="font-red">{{ row.urlNotUseCount }}</span>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-chakantupian"
          content="查看图片"
          @click.native="clickRow(row)"
          class="mr-sm"
        ></ui-btn-tip>
        <ui-btn-tip
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
          v-if="$route.name !== 'cascadelist'"
          icon="icon-rengongfujian"
          content="人工复核"
          class="vt-middle f-14 mr-sm"
          @click.native="clickArtificialReview(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
    <CheckPicture
      v-model="checkPicture"
      class="picture"
      :list="currentRow"
      :resultId="CheckPictureParams"
      :active-index-item="activeIndexItem"
      :tagList="tagList"
      :getParams="getParams"
      :interface="interfaceFunc"
      :filed-name-map="filedNameMap"
      :custom-value-field="customValueField"
      @algorithmsReview="(row) => clickAlgorithmsReview(row)"
    ></CheckPicture>
    <review-and-detail
      v-model="reviewDetailVisible"
      :mode="activeMode"
      :active-index-item="activeIndexItem"
      :review-row-data="detailData"
      :page-data="pageData"
      :total-count="totalCount"
      :table-data="tableData"
      :qualified-list="qualifiedList"
      :review-visible="true"
      :error-code-list="errorCodeList"
      :custorm-params="reviewCustormParams"
      :filed-name-map="filedNameMap"
      :title="reviewModalType==='viewOnly'? '查看图片' : '人工复核'"
      :review-modal-type="reviewModalType"
      :search-parames="formData"
      :custom-value-field="customValueField"
      @closeFn="updateTable"
      @algorithmsReview="clickAlgorithmsReview"
    ></review-and-detail>
    <ArtificialReview
      v-model="artificialVisible"
      :artificial-row="artificialRow"
      @update="showRecountBtn"
    ></ArtificialReview>
    <carPropertyDialog ref="carPropertyDialog"></carPropertyDialog>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
// 外层公共配置
import {
  qualifiedColorConfig,
  iconStaticsImgList,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { VehicleTableColumns, normalFormData, imgFormData, iconStaticsList } from './ReviewParticular.js';
import { mapActions, mapGetters } from 'vuex';
import { getAlgorithmColumns } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/VehicleProperties.js';
export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 格式：  自定义字段：对应的取值字段
      customValueField: {
        qualified: 'outcome', // 是否合格字段
        reason: 'errorCodeName', // 不合格原因取值字段
      },
      tabList: ['设备模式', '图片模式'],
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: [],
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(VehicleTableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      currentRow: {},
      checkPicture: false, //查看图片
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      // 1 - 合格、2 - 不合格 3 - 无法检测
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      cardList: [],
      statisticShow: false,
      activeMode: 'device',
      artificialVisible: false,
      artificialRow: {},
      CheckPictureParams: {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
      },
      exportLoading: false,
      // 人工复核
      detailData: {},
      reviewDetailVisible: false,
      qualifiedList: [
        { key: '设备合格', value: '1' },
        { key: '设备不合格', value: '2' },
      ],
      reviewCustormParams: {},
      errorCodeList: [], //图片模式错误原因
      filedNameMap: Object.freeze({
        smallPicName: 'smallImagePath', // 小图
        bigPicName: 'bigImagePath', // 大图
        qualified: 'outcome', // 不合格
        description: 'description', // 设备备注
        resultTip: 'description', // 图片备注
        deviceDetailId: 'evaluationDeviceDetailId', // 单个图片的id
      }),
      isReview: false, // 人工复核 过后
      reviewModalType: 'review', //review 复核，viewOnly 查看图片
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.pageData.pageNum = 1;
        this.initAll();
      },
      { deep: true, immediate: true },
    );
    if (this.colorType.length === 0) {
      this.getcolorType();
    }
    if (this.vehicleBandType.length === 0) {
      this.getvehicleBandType();
    }
    if (this.vehicleClassType.length === 0) {
      this.getvehicleClassType();
    }
    if (this.plateClassType.length === 0) {
      this.getplateClassType();
    }
  },
  methods: {
    ...mapActions({
      getcolorType: 'algorithm/getcolorType',
      getvehicleBandType: 'algorithm/getvehicleBandType',
      getvehicleClassType: 'algorithm/getvehicleClassType',
      getplateClassType: 'algorithm/getplateClassType',
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    getParams(detail) {
      return {
        qualified: detail.outcome || '1',
        reason: detail.errorCodeName || '',
      };
    },
    //图片模式卡片列表
    getCardList(entities) {
      return entities.map((item, index) => {
        // item.hasAlgorithms = true;
        item.algorithmsImgView = true; // 查看大图打开带算法的大图
        item.hasArtificial = true;
        item._index = index;
        // 处理图片模式字段展示
        item.fileList = [
          {
            label: '车牌号：',
            value: item.plateNo,
            style: this.getErrorField(item.errorFields, 'plateNo', item.plateNo).style,
            title: this.getErrorField(item.errorFields, 'plateNo', item.plateNo).desc,
          },
          {
            label: '车牌颜色：',
            value: this.$options.filters.filterType(item.plateColor, this.colorType, 'dictKey', 'dictValue', '缺失'),
            style: this.getErrorField(item.errorFields, 'colorType', item.plateColor).style,
            title: this.getErrorField(item.errorFields, 'colorType', item.plateColor).desc,
          },
          { label: '抓拍时间：', value: item.shotTime },
          { label: '抓拍地点：', value: item.address },
        ];
        return item;
      });
    },
    // isReview：复核过      closeIndex：人工复核弹框关闭时 -设备 所在总表中的索引
    updateTable({ isReview, closeIndex }) {
      let { pageNum, pageSize } = this.pageData;
      let newPage = Math.floor(closeIndex / pageSize) + 1;
      this.pageData.pageNum = newPage;
      if (isReview) {
        this.showRecountBtn();
        this.selfConfigGetList();
        this.selfConfigGetListTotal();
      } else if (newPage !== pageNum) {
        this.selfConfigGetList();
        this.selfConfigGetListTotal();
      }
    },
    async interfaceFunc(params) {
      try {
        const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          ...params,
        };
        let res = await this.$http.post(evaluationoverview.getPolyData, data);
        return { cardList: this.getCardList(res.data.data.entities || []), total: res.data.data.total, apiReturn: res };
      } catch (err) {
        console.log(err);
      }
    },
    initAll() {
      // 获取列表
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
        // 图片模式统计赋值
        iconStaticsImgList.forEach((item) => {
          return (item.count = data[item.fileName]);
        });
      });
    },
    // 获取不合格原因下拉列表
    // mode 1:设备模式，2:图片模式
    getQualificationList(mode = 1) {
      // 异常原因
      this.MixinDisQualificationList(mode).then((data) => {
        if (mode === 1) {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
          findErrorCodes.options = data.map((item) => {
            // 嘉鹏说: 设备模式查询需转换数字模式
            return { value: Number(item.key), label: item.value };
          });
        } else {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'causeErrors');
          findErrorCodes.options = data.map((item) => {
            return { value: item.key, label: item.value };
          });
        }
      });
    },
    // 人工复核 设备模式
    clickArtificialReview(row) {
      this.reviewModalType = 'review';
      this.detailData = { ...row };
      this.reviewDetailVisible = true;
      this.qualifiedList = [
        { key: '设备合格', value: '1' },
        { key: '设备不合格', value: '2' },
      ];
      this.reviewCustormParams = {
        type: 'device',
        taskIndexId: row.taskIndexId,
        deviceId: row.deviceId,
        indexType: this.activeIndexItem.indexType,
      };
    },
    //人工复核 图片模式
    clickArtificialReviewImage(row, viewType) {
      this.reviewModalType = viewType || 'review';
      this.detailData = {
        ...row,
        qualified: row.outcome || '1',
        reason: row.errorCodeName || '',
      };
      this.reviewDetailVisible = true;
      this.tableData = this.cardList;
      let findErrorCodes = this.formItemData.find((item) => item.key === 'causeErrors');
      this.errorCodeList = findErrorCodes.options;
      this.qualifiedList = [
        { key: '图片可用', value: '1' },
        { key: '图片不可用', value: '2' },
      ];
      this.reviewCustormParams = {
        deviceDetailId: row.evaluationDeviceDetailId,
        deviceId: row.deviceId,
        errorCode: (row.causeError && row.causeError.split(',')) || [],
        type: 'detail',
        taskIndexId: row.taskIndexId,
      };
    },
    clickAlgorithmsReview(row) {
      this.$refs.carPropertyDialog.init(row.id, row.indexId, getAlgorithmColumns({ indexId: row.indexId }));
    },
    // 切换模式
    changeMode(type) {
      this.activeMode = type;
      this.totalCount = 0;
      this.pageData.pageNum = 1;
      if (type === 'device') {
        this.iconList = iconStaticsList;
        this.formItemData = normalFormData;
        this.formData = {
          deviceId: '',
          deviceName: '',
          outcome: '',
          errorCodes: [],
        };
        this.getQualificationList();
      } else {
        this.formItemData = imgFormData;
        this.iconList = iconStaticsImgList;
        this.formData = {
          startTime: '',
          endTime: '',
          outcome: '',
          causeErrors: [],
          deviceIds: [],
        };
        // 图片模式获取不合格原因传2
        this.getQualificationList(2);
      }
      this.startSearch(this.formData);
    },
    // 获取列表[mixin的方法]
    selfConfigGetList() {
      this.activeMode === 'device' ? this.getTableData() : this.getImageList();
    },
    // 获取列表[mixin的方法]
    selfConfigGetListTotal() {
      this.activeMode === 'device' ? this.getTableDataTotal() : '';
    },
    getImageList() {
      // 处理图片模式列表数据
      this.MixinGetImageList().then((data) => {
        this.cardList = this.getCardList(data.entities);
        this.tableData = this.cardList;
        this.totalCount = data.total;
      });
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      if (this.activeMode === 'device') {
        this.formData = {
          deviceId: params.deviceId,
          deviceName: params.deviceName,
          outcome: params.outcome,
          errorCodes: params.errorCodes,
        };
      } else {
        this.formData = {
          startTime: params.startTime,
          endTime: params.endTime,
          outcome: params.outcome,
          deviceIds: params.deviceIds,
          causeErrors: params.causeErrors,
        };
      }
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities.map((item) => {
          return {
            qualified: item.outcome || '1',
            reason: item.errorCodeName || '',
            ...item,
          };
        });
        // this.totalCount = data.total;
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    // 导出
    onExport: function () {
      if (this.activeMode === 'device') {
        // 设备模式
        this.$refs.exportModule.init(this.$route.query.batchId);
      } else if (this.activeMode === 'image') {
        // 图片模式
        this.MixinGetSecondExport();
      }
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
        iconStaticsImgList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
        this.$Message.success('更新成功');
        this.statisticShow = false;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        cardList: this.cardList,
        tabList: this.tabList,
        needResetCopySearch: this.activeMode, // 设备、图片模式切换时，重新 拷贝搜索条件
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    CheckPicture: require('@/views/governanceevaluation/evaluationoResult/common-pages/check-picture/index.vue')
      .default,
    ArtificialReview: require('@/views/governanceevaluation/evaluationoResult/common-pages/artificial-review.vue')
      .default,
    exportData: require('../components/export-data').default,
    carPropertyDialog: require('@/views/governanceevaluation/evaluationoResult/components/car-property-dialog.vue')
      .default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/index.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
</style>
