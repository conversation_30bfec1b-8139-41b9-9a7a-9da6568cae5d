<template>
  <base-review v-bind="getAttrs" v-on="$listeners">
    <template #detailcontent="{ detailInfo }">
      <div class="detail-content">
        <div class="detail-img mr-md">
          <ui-image :src="detailInfo.imageUrl" class="image" />
        </div>
        <div class="detail-info">
          <div class="detail-table auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :stripe="false"
              :table-columns="detialTableColumns"
              :table-data="detailInfo.detialTableData"
              :loading="false"
              :span-method="handleSpan"
            >
              <template #colTitle="{ row }">
                <p>{{ row.colTitleText }}</p>
              </template>
              <template #detectionInfo="{ row }">
                <div>
                  <p class="left-text" v-if="!!row && row.colTitle === 'clockDetail'">
                    <!-- {{row.isQualified  ? '有' : '无'}} -->
                    {{ row?.ocrDate ?? '—' }}
                  </p>
                  <div class="left-text" v-if="!!row && row.colTitle === 'areaInfo'">
                    <p>{{ !!row.ocrRegion && row.isDetectRegion ? `行政区划：${row.ocrRegion}` : '' }}</p>
                    <p>{{ !!row.ocrAddress ? `安装地址：${row.ocrAddress}` : '' }}</p>
                    <p v-if="(!row.ocrRegion || !row.isDetectRegion) && !row.ocrAddress">—</p>
                  </div>
                  <p class="left-text" v-if="!!row && row.colTitle === 'deviceInfo'">
                    {{ row.ocrDeviceInfo || '' }}
                  </p>
                </div>
              </template>
              <template #archivesInfo="{ row }">
                <div v-if="row.hasOwnProperty('resQualified')" class="seal-wrap">
                  <span>最终检测结果：</span>
                  <span class="seal-img">
                    <i
                      class="icon-font icon-renlianyinzhang f-80"
                      :class="row.resQualified === '1' ? 'font-green' : 'font-red'"
                    ></i>
                    <span v-if="row.resQualified === '1'" class="font-green seal-text">合&nbsp;&nbsp;&nbsp;格</span>
                    <span v-else class="font-red seal-text">不合格</span>
                  </span>
                </div>
                <div v-else class="left-text">
                  <p v-if="!!row && row.colTitle === 'clockDetail'">{{ `标准时间：${row?.ntpDate ?? '—'}` }}</p>
                  <div v-if="!!row && row.colTitle === 'areaInfo'">
                    <p>{{ !!row.deviceRegion ? `行政区划：${row.deviceRegion}` : '' }}</p>
                    <p>{{ !!row.deviceAddress ? `安装地址：${row.deviceAddress}` : '' }}</p>
                    <p v-if="!row.deviceRegion || !row.deviceAddress">—</p>
                  </div>
                  <div v-if="!!row && row.colTitle === 'deviceInfo'" class="left-text">
                    <p>设备PTZ控制状态：{{ row.ptzStatusText || '' }}</p>
                    <p>摄像机类型扩展：{{ row.ptzTypeText || '' }}</p>
                    <p>安装位置室内外：{{ row.roomTypeText || '' }}</p>
                    <p>摄像机用途：{{ row.useTypeText || '' }}</p>
                    <p>监视方位：{{ row.directionTypeText || '' }}</p>
                  </div>
                </div>
              </template>
              <template #matching="{ row }">
                <p v-if="!!row && row.colTitle === 'clockDetail'">时间误差：{{ row?.formatClockSkew ?? '—' }}</p>
                <p v-if="!!row && row.colTitle === 'areaInfo'" class="left-text">
                  内容匹配度：{{ row.formatSimilarity }}
                </p>
                <p v-if="!!row && row.colTitle === 'deviceInfo'" class="left-text">
                  内容匹配度：{{ row.formatSimilarity }}
                </p>
              </template>
              <template #isqualified="{ row }">
                <p v-if="row.hasOwnProperty('isQualified')">{{ row.isQualified ? '是' : '否' }}</p>
              </template>
              <template #reason="{ row }">
                <div>
                  <p class="left-text" v-if="!!row && row.colTitle === 'clockDetail'">
                    {{ row.errorMessage || '—' }}
                  </p>
                  <p class="left-text" v-if="!!row && row.colTitle === 'areaInfo'">
                    {{ row.errorMessage || '—' }}
                  </p>
                  <p class="left-text" v-if="!!row && row.colTitle === 'deviceInfo'">
                    {{ row.errorMessage || '—' }}
                  </p>
                </div>
              </template>
            </ui-table>
          </div>
        </div>
      </div>
    </template>
  </base-review>
</template>

<script>
export default {
  name: 'video-osd-clock',
  data() {
    return {
      detialTableColumns: [
        {
          title: ' ',
          slot: 'colTitle',
          align: 'center',
          minWidth: 100,
          fixed: 'left',
          className: 'customColStyle',
          renderHeader: (h) => {
            return h(
              'div',
              {
                attrs: {
                  class: 'type',
                },
              },
              [
                h(
                  'span',
                  {
                    attrs: {
                      class: 'detection-time',
                    },
                  },
                  '检测项',
                ),
                h(
                  'span',
                  {
                    attrs: {
                      class: 'detection-indicator',
                    },
                  },
                  '检测结果',
                ),
              ],
            );
          },
        },
        {
          title: '检测信息',
          slot: 'detectionInfo',
          align: 'center',
          minWidth: 150,
        },
        {
          title: '档案（设备）信息',
          slot: 'archivesInfo',
          align: 'center',
          minWidth: 220,
        },
        {
          title: '匹配情况',
          slot: 'matching',
          align: 'center',
          width: 130,
        },
        {
          title: '是否合格',
          slot: 'isqualified',
          align: 'center',
          width: 80,
        },
        {
          title: '不合格原因',
          slot: 'reason',
          align: 'center',
          width: 120,
          className: 'noborderright',
        },
      ],
      detialTableData: [],
    };
  },
  methods: {
    handleSpan({ row, columnIndex }) {
      if (row.hasOwnProperty('resQualified')) {
        if (columnIndex === 2) {
          return {
            rowspan: 1,
            colspan: 6,
          };
        } else if (columnIndex < 3) {
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      }
    },
  },
  computed: {
    getAttrs() {
      return {
        ...this.$attrs,
      };
    },
  },
  components: {
    BaseReview: require('./base-review').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.detail-content {
  display: flex;
  width: 100%;
  padding: 20px;
  background: #041129;
  border-radius: 4px;
  .detail-img {
    width: 590px;
    max-height: 500px;
    object-fit: contain;
  }
  .left-text {
    text-align: left;
  }
  .detail-info {
    flex: 1;
  }
  .detail-table {
    height: 500px;
    background-color: #041129;

    &-footer {
      height: 70px;
      border: 1px solid var(--border-color);
    }

    .isqualified {
      text-align: center !important;
    }

    .seal-wrap {
      color: #ffffff;

      span {
        display: inline-block;
      }

      .seal-img {
        position: relative;
      }

      .seal-text {
        position: absolute;
        left: 23%;
        top: 35%;
        transform: rotate(-33deg);
      }
    }

    .f-80 {
      font-size: 55px;
    }
  }
  @{_deep} .ivu-table {
    background-color: #041129 !important;
    &-header th {
      border-right: 1px solid var(--border-color);
      border-bottom: 1px solid var(--border-color);
      border-top: 1px solid var(--border-color);
      box-shadow: none;
    }
    .ivu-table-tbody td {
      padding: 10px;
      border: 1px solid var(--border-color);
      background-color: #041129;
    }
    .ivu-table-tbody tr {
      &:first-child td {
        border-top: none;
      }
    }
  }
}
@{_deep} .ivu-table-fixed-header {
  .customColStyle {
    padding: 0;
    width: 100%;
    height: 100%;
    border: 1px solid var(--border-color);
    .ivu-table-cell {
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 !important;
      width: 100%;
      height: 100%;
    }
    .type {
      position: relative;
      background-color: #0d477d;
      width: 100%;
      height: 100%;
      z-index: 0;
      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        clip-path: polygon(100% calc(100% - 0.4px), 100% 0px, 0px -0.4px);
        position: absolute;
        top: 0;
        background-color: #092955;
      }
      &:before {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        clip-path: polygon(0px 0.5px, 0px 100%, calc(100% - 0.5px) calc(100% + 0.5px));
        position: absolute;
        top: 0;
        background-color: #092955;
      }
      .detection-time {
        position: absolute;
        left: 6px;
        bottom: 5px;
        z-index: 1;
      }
      .detection-indicator {
        position: absolute;
        right: 6px;
        top: 5px;
        z-index: 1;
      }
    }
  }
}
</style>
