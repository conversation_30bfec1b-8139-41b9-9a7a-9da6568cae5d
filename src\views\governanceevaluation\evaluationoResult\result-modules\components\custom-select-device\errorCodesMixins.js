import detectionResult from '@/config/api/detectionResult';
export default {
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {
    // 获取不合格原因
    this.getQualificationList();
  },
  methods: {
    // 获取不合格原因下拉列表
    async getQualificationList() {
      try {
        let { indexId, batchId, displayType, regionCode, orgCode } = this.moduleData;
        let params = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: displayType,
          orgRegionCode: displayType === 'REGION' ? regionCode : orgCode,
        };
        let res = await this.$http.post(detectionResult.getVideoUnqualifiedInfo, params);
        let options = res.data.data || [];
        this.formItemData.forEach((opt) => {
          if (opt.key === 'errorCodes') {
            opt.options = options.map((item) => {
              return { value: item.code, label: item.reason };
            });
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
};
