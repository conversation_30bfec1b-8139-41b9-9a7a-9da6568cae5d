<template>
  <div class="face-data-governance auto-fill">
    <div class="search-module">
      <div class="search-type">
        <tag-view :list="tagList" :default-active="1" @tagChange="changeStatus" ref="tagView"></tag-view>
        <span class="remarks ml-lg">( 备注：只展示最近3天治理结果 )</span>
      </div>
      <div>
        <i-switch
          class="mr-sm"
          v-model="jobState"
          :before-change="changeState"
          :true-value="1"
          :false-value="0"
          :loading="jobLoading"
        >
        </i-switch>
        <span class="mr-sm job-text">{{ jobState ? '治理中...' : '未治理' }}</span>
        <Button type="text" class="config" @click="governanceConfigShow">
          <i class="icon-font icon-canshupeizhi mr-xs"></i>
          <span class="inline vt-middle">治理配置</span>
        </Button>
      </div>
    </div>
    <div class="search-criteria" v-if="imageStatus !== '1'">
      <ui-label class="inline mr-lg" label="异常原因">
        <Select
          class="width-lg"
          v-model="searchData.errorCodeList"
          placeholder="请选择异常原因"
          clearable
          multiple
          filterable
          :max-tag-count="1"
        >
          <Option
            v-for="(item, index) in errorTypeList"
            :key="index"
            :label="item.dataValue"
            :value="item.dataKey"
          ></Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="auto-fill table-module" v-ui-loading="{ loading: loading, tableData: tableData }">
      <pic-mode
        :card-list="tableData"
        :pic-mode-count="10"
        small-img-key="originalFacePath"
        img-key="originalScenePath"
      >
        <template #cardInfo="{ item }">
          <div class="mt-sm">
            <div title="抓拍时间">
              <i class="icon-font icon-zhuapaishijian mr-xs f-14"></i>
              <span>{{ item.shotTime || '未知' }}</span>
            </div>
            <div title="入库时间">
              <i class="icon-font icon-jieshoushijian mr-xs f-16"></i>
              <span>{{ item.receiveTime || '未知' }}</span>
            </div>
            <div>
              <i class="icon-font icon-zhuapaididian mr-xs"></i>
              <span class="address ellipsis" :title="item.address">{{ item.address || '未知' }}</span>
            </div>
            <i class="icon-font icon-jiancejieguo mr-xs f-14"></i>
            <span class="tips ellipsis" :title="getErrorMessageText(item)">
              {{ getErrorMessageText(item) }}
            </span>
          </div>
        </template>
      </pic-mode>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <governance-config v-model="governanceConfigVisible"></governance-config>
  </div>
</template>
<script>
import imageDataGovernance from '@/config/api/image-data-governance';
import user from '@/config/api/user';
export default {
  props: {},
  data() {
    return {
      loading: false,
      governanceConfigVisible: false,
      jobState: 0,
      jobLoading: false,
      tagList: [],
      typeList: [
        {
          label: '合格图像',
          value: '1',
        },
        {
          label: '不合格图像',
          value: '2',
        },
      ],
      errorTypeList: [],
      imageStatus: '2',
      searchData: {
        queryType: '1',
        errorCodeList: [],
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
    };
  },
  created() {
    this.tagList = this.typeList.map((row) => row.label);
    this.copySearchDataMx(this.searchData);
    this.getDictData();
    this.init();
    this.getDataTask();
  },
  methods: {
    getErrorMessageText(item) {
      let errorMessageTextList = item.detail.filter((row) => row.result !== 1);
      return errorMessageTextList.map((row) => row.errorMessageText).join('、');
    },
    async getDictData() {
      try {
        const params = {
          typekey: 'face_check_rule_error_code',
        };
        let { data } = await this.$http.get(user.queryByTypeKey, { params });
        this.errorTypeList = data.data.map((item) => {
          return {
            dataValue: item.dataValue,
            dataKey: item.dataKey,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(imageDataGovernance.queryList, {
          imageStatus: this.imageStatus,
          ...this.searchData,
        });
        this.tableData = res.data.data.list.map((row) => {
          Object.keys(row.imageInfo).forEach((key) => {
            row[key] = row.imageInfo[key];
          });
          delete row.imageInfo;
          return row;
        });
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async getDataTask() {
      try {
        const res = await this.$http.get(imageDataGovernance.getDataTask, {
          params: {
            id: 1,
          },
        });
        this.jobState = res.data.data.state;
      } catch (err) {
        console.log(err);
      }
    },
    changeStatus(index) {
      this.imageStatus = this.typeList[index].value;
      this.searchData.errorCodeList = [];
      this.search();
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    governanceConfigShow() {
      this.governanceConfigVisible = true;
    },
    changeState() {
      return new Promise(async (resolve, reject) => {
        try {
          if (this.jobLoading) {
            reject(false);
          }
          this.jobLoading = true;
          await this.$http.post(imageDataGovernance.startOrPauseDataTask, {
            id: 1,
            state: this.jobState === 1 ? 0 : 1,
          });
          const message = this.jobState === 1 ? '任务已停止' : '任务已开启';
          this.$Message.success(message);
          resolve(true);
        } catch (err) {
          console.log(err);
          reject(false);
        } finally {
          this.jobLoading = false;
        }
      });
    },
  },
  watch: {},
  components: {
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    TagView: require('@/components/tag-view.vue').default,
    GovernanceConfig: require('./governance-config.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .face-data-governance {
    .tips {
      color: #e44f22;
    }
    .remarks {
      color: #c76d28;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .face-data-governance {
    .tips {
      color: var(--color-failed);
    }
    .remarks {
      color: var(--color-warning);
    }
  }
}

.face-data-governance {
  .tips,
  .address {
    vertical-align: middle;
    display: inline-block;
    width: 115px;
  }

  .tips-content {
    width: 200px;
    white-space: normal;
  }
  .search-module {
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .job-text {
      width: 60px;
      display: inline-block;
    }
    .search-type {
      display: flex;
      align-items: center;
    }
    .config {
      color: var(--color-active);
      cursor: pointer;
      span {
        text-decoration: underline;
      }
    }
  }
  .search-criteria {
    margin-top: 10px;
    padding: 10px 20px 0 20px;
    border-top: 1px solid var(--devider-line);
  }
  .table-module {
    margin-top: 10px;
    padding-left: 20px;
    @{_deep}.pic-mode-item {
      width: calc((100% - (10 * 9px)) / 10);
    }
  }
}
</style>
