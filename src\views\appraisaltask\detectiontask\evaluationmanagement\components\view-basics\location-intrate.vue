<template>
  <div class="basic-information">
    <ui-modal title="查看详情" :footer-hide="true" v-model="visible" v-if="visible" :styles="styles">
      <!--  -->
      <div class="content auto-fill" v-if="!!videoList">
        <div class="container auto-fill">
          <div class="title_text">
            <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
            <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
            <div class="export fr">
              <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
                <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
                <span class="inline ml-xs">导出</span>
              </Button>
            </div>
          </div>
          <line-title title-name="检测结果统计"></line-title>
          <div class="statistics_list">
            <div class="statistics">
              <div class="sta_item_left">
                <draw-echarts
                  :echart-option="echartRing"
                  :echart-style="ringStyle"
                  ref="zdryChart"
                  class="charts"
                ></draw-echarts>
              </div>
              <div class="line"></div>
              <no-standard :module-data="moduleData"></no-standard>
            </div>
          </div>
          <fold
            v-if="this.$parent.row.remark != 'deviceGather'"
            title="应有重点场所类型"
            :list="allDeviceTagList"
            bgColor="#2B84E2"
          ></fold>
          <fold-tree
            v-if="this.$parent.row.remark === 'deviceGather'"
            title="应有重点场所类型"
            :list="allDeviceGatherList"
            bgColor="#2B84E2"
          ></fold-tree>
          <fold
            v-if="this.$parent.row.remark != 'deviceGather'"
            title="未上报重点场所类型"
            :list="notReportTagIds"
            bgColor="#bc3c19"
          ></fold>
          <fold-tree
            v-if="this.$parent.row.remark === 'deviceGather'"
            title="未上报重点场所类型"
            :list="reportDeviceGatherList"
            bgColor="#bc3c19"
          ></fold-tree>
        </div>
      </div>
      <div class="no-box" v-else>
        <div class="no-data">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {},
  data() {
    return {
      visible: true,
      loading: false,
      styles: { width: '9rem' },
      echartRing: {},
      ringStyle: { width: '700px', height: '180px' },
      zdryChartObj: {
        xAxisData: ['已上报重点场所类型', '未上报重点场所类型'],
        showData: [
          { name: '已上报重点场所类型', value: 0 },
          { name: '未上报重点场所类型', value: 0 },
        ],
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '填报准确率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      selectTabs: [],
      list: ['重点路线', '维稳敏感地'],
      allDeviceTagList: [],
      notReportTagIds: [],
      allDeviceGatherList: [],
      reportDeviceGatherList: [],
      videoList: {},
      exportLoading: false,
    };
  },
  created() {},
  async mounted() {
    await this.getChartsData();
    await this.initRing();
  },
  methods: {
    // 统计
    async getChartsData() {
      try {
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(governanceevaluation.queryEvaluationBasicRecord, params);
        if (!res.data.data) return;
        this.videoList = res.data.data;
        this.moduleData.rateValue = res.data.data.resultValue; //建档率
        this.moduleData.priceValue = res.data.data.standardsValue; //达标值
        this.moduleData.resultValue = res.data.data.qualifiedDesc; //考核结果
        this.moduleData.remarkValue = res.data.data.remark; //提示
        this.allDeviceTagList = res.data.data.emphasisLocationAllDeviceTagList || [];
        this.allDeviceGatherList = this.$util.common.arrayToJson(
          res.data.data.emphasisLocationAllDeviceGatherList || [],
          'code',
          'parentCode',
        );
        this.notReportTagIds = res.data.data.emphasisLocationNotReportDeviceTagList || [];
        this.reportDeviceGatherList = this.$util.common.arrayToJson(
          this.videoList.emphasisLocationNotReportDeviceGatherList || [],
          'code',
          'parentCode',
        );
      } catch (error) {
        console.log(error);
      }
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.get(governanceevaluation.exportEmphasisLocationIndex, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },

    initRing() {
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '已上报重点场所类型') {
          item.value = this.videoList.emphasisLocationReportCount || 0;
        } else {
          item.value = this.videoList.emphasisLocationCount - this.videoList.emphasisLocationReportCount || 0;
        }
      });
      let showData = this.zdryChartObj.showData;
      this.zdryChartObj.count = this.videoList.emphasisLocationCount || 0;
      let formatData = {
        seriesName: '应有重点场\n所类型',
        showData: showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    fold: require('@/views/governanceevaluation/detectiontask/evaluationmanagement/components/fold.vue').default,
    foldTree: require('@/views/governanceevaluation/detectiontask/evaluationmanagement/components/fold-tree.vue')
      .default,
  },
};
</script>
<style lang="less" scoped>
.basic-information {
  @{_deep} .ivu-modal-body {
    padding: 0 20px !important;
  }
  .no-box {
    width: 1726px;
    min-height: 840px;
    max-height: 840px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 840px;
    max-height: 840px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        margin-bottom: 10px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
  }

  .fold-panel {
    margin-bottom: 10px !important;
  }
}
</style>
