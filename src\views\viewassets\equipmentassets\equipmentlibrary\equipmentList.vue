<template>
  <div class="equipmentlibrary-list auto-fill">
    <div class="auto-fill" v-show="!componentName">
      <!--    <slide-unit-tree @selectOrgCode="selectOrgCode" :select-key="selectKey"></slide-unit-tree>-->
      <!-- 顶部统计 -->
      <chartsContainer :abnormal-count="countList" class="charts" />
      <div class="search-module">
        <div class="right-btn">
          <UiSearch class="ui-search mr-lg" v-model="searchModel">
            <template #content>
              <div class="search-content">
                <ui-label class="inline mb-10" label="数据来源域">
                  <Select class="width-md" v-model="searchData.sourceDomain" placeholder="请选择数据来源域" clearable>
                    <Option v-for="(item, index) in sourceDomainList" :key="index" :value="item.dataKey">
                      {{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>
                <ui-label class="inline mb-10" label="是否检测">
                  <Select class="width-md" v-model="searchData.isCheck" placeholder="请选择" clearable>
                    <Option value="1">是</Option>
                    <Option value="0">否</Option>
                  </Select>
                </ui-label>

                <!-- 基础信息状态 -->
                <ui-label class="inline mb-10" label="基础信息状态">
                  <Select
                    :disabled="searchData.isCheck != '1'"
                    class="width-md left-select"
                    v-model="searchData.baseCheckStatus"
                    placeholder="请选择是否合格"
                    clearable
                  >
                    <Option value="0">合格</Option>
                    <Option value="1">不合格</Option>
                  </Select>
                  <span class="split">-</span>
                  <Select
                    class="width-md right-select"
                    :disabled="searchData.baseCheckStatus != '1'"
                    v-model="searchData.baseErrorMessageList"
                    multiple
                    placeholder="请选择错误类别"
                    clearable
                    :max-tag-count="1"
                  >
                    <Option v-for="(val, key, index) in baseErrorMessageList" :value="key" :key="index"
                      >{{ val }}
                    </Option>
                  </Select>
                </ui-label>

                <!-- 视图数据状态 -->
                <ui-label class="inline mb-10" label="视图数据状态">
                  <Select
                    :disabled="searchData.isCheck != '1'"
                    class="width-md left-select"
                    v-model="searchData.viewCheckStatus"
                    placeholder="请选择是否合格"
                    clearable
                  >
                    <Option value="0">合格</Option>
                    <Option value="1">不合格</Option>
                  </Select>
                  <span class="split">-</span>
                  <Select
                    :disabled="searchData.viewCheckStatus != '1'"
                    class="width-md right-select"
                    v-model="searchData.viewErrorMessageList"
                    multiple
                    placeholder="请选择错误类别"
                    clearable
                    :max-tag-count="1"
                  >
                    <Option :value="key" v-for="(val, key, index) in viewErrorMessageList" :key="index"
                      >{{ val }}
                    </Option>
                  </Select>
                </ui-label>

                <!-- 视频流数据状态 -->
                <ui-label class="inline mb-10" label="视频流数据状态">
                  <Select
                    :disabled="searchData.isCheck != '1'"
                    class="width-md left-select"
                    v-model="searchData.videoCheckStatus"
                    placeholder="请选择是否合格"
                    clearable
                  >
                    <Option value="0">合格</Option>
                    <Option value="1">不合格</Option>
                  </Select>
                  <span class="split">-</span>
                  <Select
                    :disabled="searchData.videoCheckStatus != '1'"
                    class="width-md right-select"
                    v-model="searchData.videoErrorMessageList"
                    multiple
                    placeholder="请选择错误类别"
                    clearable
                    :max-tag-count="1"
                  >
                    <Option :value="key" v-for="(val, key, index) in videoErrorMessageList" :key="index"
                      >{{ val }}
                    </Option>
                  </Select>
                </ui-label>

                <ui-label class="inline mb-10" label="数据来源">
                  <Select class="width-md" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
                    <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey"
                      >{{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>

                <!-- 监控点位类型 -->
                <ui-label class="inline mb-10" :label="global.filedEnum.sbdwlx">
                  <Select
                    class="width-md"
                    v-model="searchData.sbdwlx"
                    :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                    clearable
                  >
                    <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
                      >{{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>

                <!-- 摄像机功能类型 -->
                <ui-label class="inline mb-10" :label="global.filedEnum.sbgnlx">
                  <Select
                    class="width-md"
                    v-model="searchData.sbgnlx"
                    :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                    clearable
                  >
                    <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
                      >{{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>
                <ui-label class="inline" label="设备重点类型">
                  <Select class="width-md" v-model="searchData.isImportant" clearable placeholder="请选择设备重点类型">
                    <Option label="普通设备" :value="0"></Option>
                    <Option label="重点设备" :value="1"></Option>
                  </Select>
                </ui-label>

                <!-- 摄像机采集区域 -->
                <ui-label class="inline mb-10" :label="`${global.filedEnum.sbcjqy}列表`">
                  <Button type="dashed" class="area-btn" @click="clickArea"
                    >请选择采集区域 {{ `已选择 ${searchData.sbcjqyList.length}个` }}
                  </Button>
                </ui-label>
                <ui-label class="inline mb-10" label="维护单位">
                  <Input v-model="searchData.whdw" class="width-md" placeholder="维护单位"></Input>
                </ui-label>
                <ui-label class="inline mb-10" label="更新时间">
                  <DatePicker
                    class="width-md"
                    v-model="searchData.startModifyTime"
                    type="datetime"
                    placeholder="请选择开始时间"
                    @on-change="
                      (formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startModifyTime')
                    "
                    :options="startTimeOption"
                    confirm
                  />
                  <span class="ml-sm mr-sm">--</span>
                  <DatePicker
                    class="width-md"
                    v-model="searchData.endModifyTime"
                    type="datetime"
                    placeholder="请选择结束时间"
                    @on-change="
                      (formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endModifyTime')
                    "
                    :options="endTimeOption"
                    confirm
                  />
                </ui-label>
                <ui-label class="inline mb-sm mr-lg" label="在线状态">
                  <Select class="width-md" v-model="searchData.isOnline" clearable placeholder="请选择设备在线状态">
                    <Option v-for="(item, index) in propertySearch_isonline" :key="index" :value="item.dataKey"
                      >{{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>
                <ui-label label="上报状态" class="inline mr-lg">
                  <Select
                    class="width-md"
                    v-model="searchData.cascadeReportStatus"
                    placeholder="请选择上报状态"
                    clearable
                  >
                    <Option value="0">未上报</Option>
                    <Option value="1">已上报</Option>
                  </Select>
                </ui-label>
                <ui-label label="报备状态" class="inline mr-lg">
                  <Select class="width-md" v-model="searchData.examReportStatus" placeholder="请选择报备状态" clearable>
                    <Option value="0">未报备</Option>
                    <Option value="1">已报备</Option>
                  </Select>
                </ui-label>
                <ui-label label="设备功能类型扩展" class="inline mr-lg">
                  <Select
                    class="width-md"
                    v-model="searchData.sbgnlxExtList"
                    placeholder="请选择设备功能类型扩展"
                    clearable
                    multiple
                    filterable
                    :max-tag-count="1"
                  >
                    <Option v-for="(item, index) in sbgnlxExtList" :key="index" :value="item.dataKey">
                      {{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>
                <ui-label label="设备状态扩展" class="inline mr-lg">
                  <Select
                    class="width-md"
                    v-model="searchData.phyStatusExtList"
                    placeholder="请选择设备状态扩展"
                    clearable
                    multiple
                    filterable
                    :max-tag-count="1"
                  >
                    <Option v-for="(item, index) in phyStatusExtList" :key="index" :value="item.dataKey">
                      {{ item.dataValue }}
                    </Option>
                  </Select>
                </ui-label>
                <ui-select-tabs class="ui-select-tabs" :list="tagList" @selectInfo="selectInfo" ref="uiSelectTabs">
                </ui-select-tabs>
              </div>
            </template>
          </UiSearch>
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="reset">重置</Button>
        </div>
        <ui-label class="inline mb-10" label="组织机构">
          <api-organization-tree
            ref="orgTree"
            :select-tree="selectOrgTree"
            :custorm-node="true"
            :custorm-node-data="custormNodeData"
            @selectedTree="selectedOrgTree"
            placeholder="请选择组织机构"
          >
          </api-organization-tree>
        </ui-label>
        <ui-label class="inline mb-10" label="行政区划">
          <api-area-tree
            :select-tree="selectTree"
            :custorm-node="true"
            :custorm-node-data="custormAreaNodeData"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
        <ui-label class="inline mb-10" :label="`${global.filedEnum.deviceId}`">
          <Input v-model="searchData.deviceId" class="width-md" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label class="inline mb-10" :label="`${global.filedEnum.deviceName}`">
          <Input v-model="searchData.deviceName" class="width-md" placeholder="请输入设备名称"></Input>
        </ui-label>

        <!-- 设备状态 -->
        <ui-label class="inline mb-10" :label="global.filedEnum.phyStatus">
          <Select
            class="width-md"
            v-model="searchData.phyStatus"
            :placeholder="`请选择${global.filedEnum.phyStatus}`"
            clearable
          >
            <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
      </div>

      <div class="btns">
        <Button
          type="primary"
          class="fr ml-sm"
          @click="addDevice"
          v-permission="{
            route: 'equipmentlibrary',
            permission: 'addDevice',
          }"
        >
          <i class="icon-font icon-tianjia f-14"></i>
          <span class="vt-middle ml-sm">新增入库设备</span>
        </Button>
        <Button type="primary" class="fr ml-sm" @click="exportModule">
          <i class="icon-font icon-daochu f-14"></i>
          <span class="vt-middle ml-sm">导出</span>
        </Button>
        <Upload
          action="/ivdg-asset-app/device/importDevice"
          ref="upload"
          class="inline fr"
          :show-upload-list="false"
          :headers="headers"
          :data="uploadParams"
          :before-upload="beforeUpload"
          :on-success="importSuccess"
          :on-error="importError"
        >
          <Button type="primary" class="ml-sm" :loading="importLoading">
            <i class="icon-font icon-daoruwentishebei f-12"></i>
            <span class="vt-middle ml-sm">导入设备</span>
          </Button>
        </Upload>
        <Dropdown trigger="click" class="dropdown fr">
          <Button>
            <span>删除设备</span>
            <Icon type="ios-arrow-down"></Icon>
          </Button>
          <DropdownMenu
            slot="list"
            v-permission="{
              route: 'equipmentlibrary',
              permission: 'deviceRemove',
            }"
          >
            <DropdownItem @click.native="batchDelete" :disabled="checkedData.length === 0">
              <i class="icon-font icon-piliangshanchu"></i>
              <span>删除选中设备</span>
            </DropdownItem>
            <DropdownItem
              v-permission="{
                route: 'equipmentlibrary',
                permission: 'uploadDeleteDevice',
              }"
            >
              <Upload
                action="/ivdg-asset-app/device/uploadDeleteDevice"
                ref="upload"
                class="inline fr"
                :show-upload-list="false"
                :headers="headers"
                :before-upload="beforeDeleteUpload"
                :on-success="deleteForFile"
                :on-error="importDeleteError"
              >
                <Button class="import-device-delete" type="text" :loading="deleteLoading">
                  <i class="icon-font icon-daoruwentishebei"></i>
                  <span>删除导入设备</span>
                </Button>
              </Upload>
              <Button class="import-device-delete" type="text" @click="downLoadTemplate">
                <span class="device-id ml-md">(下载模板)</span>
              </Button>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <div class="table-module auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          @selectTable="selectTable"
        >
          <template #Index="{ row, index }">
            <div :class="row.rowClass">
              <span>{{ index + 1 }}</span>
            </div>
          </template>
          <template #deviceId="{ row }">
            <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">
              {{ row.deviceId }}
            </span>
          </template>
          <template #deviceName="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
              {{ row.deviceName }}
            </div>
          </template>
          <template #address="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.address">
              {{ row.address }}
            </div>
          </template>
          <!-- 经纬度保留8位小数-->
          <template #longitude="{ row }">
            <span>{{ row.longitude | filterLngLat }}</span>
          </template>
          <template #latitude="{ row }">
            <span>{{ row.latitude | filterLngLat }}</span>
          </template>
          <template #phyStatus="{ row }">
            <span
              :style="{
                color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-failed)',
              }"
              >{{ row.phyStatus | filterType(phystatusList) }}
            </span>
          </template>
          <template #orgName="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
              {{ row.orgName }}
            </div>
          </template>
          <template #isOnline="{ row }">
            <div>
              <span
                class="check-status-font"
                :class="row.isOnline === '1' ? 'font-success' : row.isOnline === '2' ? 'font-failed' : ''"
              >
                {{ row.isOnlineText }}
              </span>
            </div>
          </template>
          <template #checkStatus="{ row }">
            <div :class="row.rowClass">
              <span
                class="check-status"
                :class="
                  row.checkStatus === '0000'
                    ? 'bg-success'
                    : row.checkStatus === '1000'
                    ? 'bg-other'
                    : row.checkStatus
                    ? 'bg-failed'
                    : ''
                "
              >
                {{ handleCheckStatus(row.checkStatus) }}
              </span>
            </div>
          </template>
          <template #baseCheckStatus="{ row }">
            <div :class="row.rowClass">
              <span
                class="check-status"
                :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
              >
                {{ row.baseCheckStatus == 1 ? '不合格' : row.baseCheckStatus == 0 ? '合格' : '--' }}
              </span>
            </div>
          </template>
          <template #viewCheckStatus="{ row }">
            <div :class="row.rowClass">
              <span
                class="check-status"
                :class="row.viewCheckStatus === '0' ? 'bg-success' : row.viewCheckStatus === '1' ? 'bg-failed' : ''"
              >
                {{ row.viewCheckStatus == 1 ? '不合格' : row.viewCheckStatus == 0 ? '合格' : '--' }}
              </span>
            </div>
          </template>
          <template #videoCheckStatus="{ row }">
            <div :class="row.rowClass">
              <span
                class="check-status"
                :class="row.videoCheckStatus === '0' ? 'bg-success' : row.videoCheckStatus === '1' ? 'bg-failed' : ''"
              >
                {{ row.videoCheckStatus == 1 ? '不合格' : row.videoCheckStatus == 0 ? '合格' : '--' }}
              </span>
            </div>
          </template>
          <template #sbdwlx="{ row }">
            <span>{{ row.sbdwlxText }}</span>
          </template>
          <template #sbgnlx="{ row }">
            <Tooltip placement="top" :content="row.sbgnlxText" :disabled="row.sbgnlxText.split('/').length < 2">
              <div class="tooltip-type">
                {{ row.sbgnlxText }}
              </div>
            </Tooltip>
          </template>
          <template #azsj="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.azsj">
              {{ row.azsj }}
            </div>
          </template>
          <template #ipAddr="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
              {{ row.ipAddr }}
            </div>
          </template>
          <template #macAddr="{ row }">
            <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
              {{ row.macAddr }}
            </div>
          </template>
          <template #action="{ row }">
            <div>
              <ui-btn-tip
                class="mr-md"
                icon="icon-bianji2"
                content="编辑"
                @click.native="deviceModalShow(row)"
                v-permission="{
                  route: 'equipmentlibrary',
                  permission: 'updateDeviceById',
                }"
              >
              </ui-btn-tip>
              <ui-btn-tip
                class="mr-md"
                :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
                icon="icon-shebeidangan"
                content="设备档案"
                @click.native="deviceArchives(row)"
              >
              </ui-btn-tip>
              <create-tabs
                class="inline"
                :component-name="resultData.componentName"
                :tabs-text="resultData.text"
                :tabs-query="{
                  deviceId: row.deviceId,
                }"
                @selectModule="selectModule"
              >
                <ui-btn-tip
                  class="mr-md"
                  :styles="{ color: 'var(--color-table-btn-default)', 'font-size': '14px' }"
                  icon="icon-a-ditu2"
                  content="地图"
                  @click.native="checkLonLat($event, row)"
                >
                </ui-btn-tip>
              </create-tabs>

              <ui-btn-tip
                :disabled="row.checkStatus === '1000' || row.checkStatus === '0000'"
                :styles="{ color: 'var(--color-failed)', 'font-size': '14px' }"
                icon="icon-yichangyuanyin"
                content="不合格原因"
                @handleClick="viewRecord(row)"
              >
              </ui-btn-tip>
              <!-- <Button type="text" class="mr-lg" ></Button> -->
              <!-- <Button type="text" @click="deviceArchives(row)">设备档案</Button> -->
            </div>
          </template>
        </ui-table>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
      <device-detail
        v-model="deviceDetailShow"
        :choosed-org="choosedOrg"
        :modal-title="deviceDetailTitle"
        :modal-action="deviceDetailAction"
        :view-device-id="viewDeviceId"
        :device-code="deviceCode"
        :unqualified="deviceUnqualified"
        @update="search"
      >
      </device-detail>
      <customize-filter
        v-model="customFilter"
        :customize-action="customizeAction"
        :content-style="contentStyle"
        :field-name="fieldName"
        :checkbox-list="allPropertyList"
        :default-checked-list="defaultCheckedList"
        :left-disabled="leftDisabled"
        :right-disabled="rightDisabled"
      >
        <template #footer="{ tagList }">
          <Tooltip content="只会导出选中字段的模板，不会导出设备数据" placement="top" max-width="350">
            <Button
              type="primary"
              class="plr-30"
              @click="exportExcel(tagList, 'module')"
              :loading="exportModuleLoading"
            >
              {{ exportModuleLoading ? '下载中' : '模板导出' }}
            </Button>
          </Tooltip>
          <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
            <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading">
              {{ exportDataLoading ? '下载中' : '数据导出' }}
            </Button>
          </Tooltip>
        </template>
      </customize-filter>
      <view-detection-field
        v-model="recordShow"
        :view-data="recordData"
        :need-option="true"
        @recordModalShow="deviceModalShow"
      >
      </view-detection-field>
      <upload-error v-model="uploadErrorVisible" :error-data="errorData" :error-columns="errorColumns"> </upload-error>
      <area-select v-model="areaSelectModalVisible" @confirm="confirmArea" :checkedTreeDataList="checkedTreeData">
      </area-select>
    </div>

    <keep-alive v-show="componentName">
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'equipmentlibraryList',
  mixins: [dealWatch, downLoadTips],
  props: {},
  data() {
    return {
      componentName: null,
      componentLevel: 0,
      resultData: {
        componentName: 'EquipmentMap', // 需要跳转的组件名
        text: '资产地图', // 跳转页面标题
        title: '资产地图',
        type: 'view',
      },
      uploadErrorVisible: false,
      loading: false,
      exportDataLoading: false,
      exportModuleLoading: false,
      deleteLoading: false,
      // selectKey: '0',
      deviceUnqualified: null,
      selectTree: {
        regionCode: '',
      },
      tagList: [],
      searchData: {
        orgCode: '',
        civilCode: '',
        deviceId: '',
        deviceName: '',
        sbdwlx: '',
        sbgnlx: '',
        sourceId: '',
        errorType: '',
        checkStatuses: [],
        errorMessageList: [],
        isImportant: '',
        whdw: '',
        sbcjqyList: [],
        pageNumber: 1,
        pageSize: 20,
        isCheck: '',
        baseCheckStatus: '',
        viewCheckStatus: '',
        videoCheckStatus: '',
        startModifyTime: '',
        endModifyTime: '',
        tagIds: [],
        isOnline: '',
        cascadeReportStatus: '',
        sbgnlxExtList: [],
        phyStatus: '',
        phyStatusExtList: [],
        sourceDomain: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      choosedOrg: {},
      checkedData: [],
      tableData: [],
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        { minWidth: 120, title: '维护单位', key: 'whdw', tooltip: true },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        { width: 120, title: '数据来源', key: 'sourceIdText' },
        {
          width: 100,
          title: '基础信息状态',
          slot: 'baseCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视图数据状态',
          slot: 'viewCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视频数据状态',
          slot: 'videoCheckStatus',
          align: 'left',
        },
        // { width: 90, title: '检测状态', slot: 'checkStatus', align: 'left' },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 110,
          title: `${this.global.filedEnum.sbcjqy}`,
          key: 'sbcjqyText',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        {
          width: 100,
          title: '在线状态',
          slot: 'isOnline',
          align: 'left',
          // fixed: 'right',
        },
        {
          width: 250,
          title: '设备安装地址',
          key: 'address',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: '设备安装时间',
          key: 'azsj',
          align: 'left',
          tooltip: true,
        },
        { width: 160, title: '入库时间', key: 'createTime' },
        { width: 160, title: '更新时间', key: 'modifyTime' },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      // allDicData: {},
      // dicDataEnum: Object.freeze({
      //   check_status_sq: 'checkStatusList',
      //   propertySearch_sbdwlx: 'sbdwlxTypeList',
      //   sxjgnlx_receive: 'sbgnlxTypeList',
      // }),
      deviceDetailShow: false,
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      viewDeviceId: 0,
      customFilter: false,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: `3.125rem`,
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
      countList: [
        { title: '设备总量', count: '0', icon: 'icon-equipmentlibrary' },
        { title: '视频监控', count: '0', icon: 'icon-ivdg-shipinjiankong' },
        { title: '人脸卡口', count: '0', icon: 'icon-renliankakou' },
        { title: '车辆卡口', countKey: '0', icon: 'icon-cheliangkakou' },
      ],
      recordShow: false,
      recordData: {},
      deviceCode: '',
      selectOrgTree: {
        orgCode: null,
      },
      messageList: [],
      importLoading: false,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      uploadParams: {
        execInsert: '1', //区别治理中心信息填报导入
      },
      errorData: [],
      errorColumns: [],
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
        {
          label: '组织机构匹配异常',
          orgCode: '-2',
        },
      ],
      custormAreaNodeData: [
        {
          label: '未分配行政区划',
          regionCode: '-1',
        },
        {
          label: '行政区划匹配异常',
          regionCode: '-2',
        },
      ],
      areaSelectModalVisible: false,
      checkedTreeData: [],
      baseErrorMessageList: [],
      viewErrorMessageList: [],
      videoErrorMessageList: [],
      searchModel: false,
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endModifyTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startModifyTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  async created() {
    // this.dictTypeListGroupByType()
    if (this.propertySearchLbdwlx.length === 0) await this.getAlldicData();
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  mounted() {
    this.getPropertyList();
    this.getTagList();
    // this.selectKey = this.defaultSelectedOrg.orgCode
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.init();
    this.queryDeviceCount();
    this.queryErrorList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagList = res.data.data.map((row) => {
          return {
            name: row.tagName,
            id: row.tagId,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((item) => item.id);
      this.search();
    },
    checkLonLat(e, row) {
      const valid = this.$util.common.checkLonLat(row.longitude, row.latitude);
      if (!valid) {
        e.stopPropagation();
      }
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    clickArea() {
      this.areaSelectModalVisible = true;
      this.checkedTreeData = this.searchData.sbcjqyList || [];
    },
    confirmArea(data) {
      this.searchData.sbcjqyList = data;
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        if (res.data.failCount === 0) {
          this.$Message.success(res.data.tip);
        } else {
          this.$Message.warning({
            closable: true,
            content: res.data.tip,
          });
        }
        this.search();
      } else if (res.code === 81598) {
        this.dealError(res.msg);
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    dealError(error) {
      this.errorData = JSON.parse(error);
      this.uploadErrorVisible = true;
      this.errorColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'left',
        },
        {
          title: '设备编码',
          key: 'deviceId',
          align: 'left',
        },
        {
          title: '错误原因',
          key: 'errorReason',
          align: 'left',
        },
      ];
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.search();
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // selectOrgCode(data) {
    //   this.searchData.orgCode = data.orgCode
    //   this.choosedOrg = data
    //   this.init()
    //   this.queryDeviceCount()
    // },
    selectSource(val) {
      let sourceId = val ? val.sourceId : '';
      this.searchData.sourceId = sourceId;
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.getPageDeviceList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.loading = false;
      }
    },
    async queryDeviceCount() {
      try {
        let res = await this.$http.post(taganalysis.deviceStatistics, this.searchData);
        let obj = res.data.data;
        this.countList[0].count = obj.deviceTotal;
        this.countList[1].count = obj.videoMonitor;
        this.countList[2].count = obj.faceCount;
        this.countList[3].count = obj.vehicleCount;
      } catch (err) {
        console.log('err', err);
      } finally {
        // this.loading = false
      }
    },
    choseStuses(val) {
      //清空
      if (val.indexOf('3') === -1) {
        this.searchData.errorCategory = '';
        this.searchData.errorMessageList = [];
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
      this.queryDeviceCount();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    handleCheckStatus(row) {
      const flag = {
        1000: '待检测',
        '0000': '合格',
      };
      let msg = '';
      if (row) {
        msg = flag[row] ? flag[row] : '不合格';
      }
      return msg;
    },
    addDevice() {
      this.deviceDetailTitle = '新增入库设备';
      this.deviceDetailAction = 'add';
      this.deviceDetailShow = true;
    },
    deviceModalShow(row, unqualified) {
      this.deviceDetailTitle = '修改入库设备';
      this.deviceDetailAction = 'edit';
      this.deviceUnqualified = unqualified || null;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    exportModule() {
      this.customFilter = true;
    },
    async exportExcel(propertyList, type) {
      try {
        if (type === 'module') {
          this.exportModuleLoading = true;
        } else {
          this.$_openDownloadTip();
          this.exportDataLoading = true;
        }
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          isTemplate: type === 'module',
          ids: this.checkedData.map((item) => item.id),
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(equipmentassets.exportDevice, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportModuleLoading = false;
        this.exportDataLoading = false;
      }
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        this.defaultCheckedList = [
          'deviceId',
          'id',
          'orgCode',
          'civilCode',
          'deviceName',
          'sbgnlx',
          'sbdwlx',
          'ipAddr',
          'macAddr',
          'longitude',
          'latitude',
          'sbcjqy',
          'phyStatus',
          'cascadeReportStatus',
        ];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId' || item.propertyName === 'id';
        };
        this.rightDisabled = (item) => {
          return !(item.propertyName === 'deviceId' || item.propertyName === 'id');
        };
      } catch (err) {
        console.log(err);
      }
    },

    viewRecord(row) {
      this.recordShow = true;
      this.recordData = row;
    },
    reset() {
      this.$refs.orgTree.reset();
      this.$refs.uiSelectTabs && this.$refs.uiSelectTabs.reset();
      this.checkStatuses = [];
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    async choseType(val) {
      try {
        this.searchData.errorMessageList = [];
        let res = await this.$http.get(equipmentassets.queryErrorReason, {
          params: { errorType: val },
        });
        if (val === 'BASICS') {
          this.messageList = res.data.data;
        } else if (val === 'IMAGE') {
          this.messageList = res.data.data;
        } else if (val === 'VIDEO') {
          this.messageList = res.data.data;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.baseErrorMessageList = res.data.data;

      let res2 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'IMAGE' },
      });
      this.viewErrorMessageList = res2.data.data;

      let res3 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'VIDEO' },
      });
      this.videoErrorMessageList = res3.data.data;
    },
    // async dictTypeListGroupByType() {
    //   try {
    //     let { data } = await this.$http.post(
    //       user.queryDataByKeyTypes,
    //       Object.keys(this.dicDataEnum)
    //     )
    //     const dataObject = data.data
    //     Object.keys(this.dicDataEnum).forEach((key) => {
    //       let obj = dataObject.find((row) => {
    //         return !!row[key]
    //       })
    //       this.$set(this.allDicData, this.dicDataEnum[key], obj[key])
    //     })
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    beforeDeleteUpload() {
      this.deleteLoading = true;
    },
    importDeleteError(res) {
      this.deleteLoading = false;
      this.$Message.error(res.msg);
    },
    async downLoadTemplate() {
      try {
        const res = await this.$http.post(equipmentassets.exportImportDeviceTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.error(err, 'err');
      }
    },
    async batchDelete() {
      try {
        if (this.checkedData.length === 0) return;
        const res = await this.$http.post(equipmentassets.deleteDevice, {
          ids: this.checkedData.map((row) => row.id),
        });
        this.$Message.success(res.data.msg);
        this.init();
      } catch (err) {
        console.error(err, 'err');
      }
    },
    async deleteForFile(res) {
      try {
        this.deleteLoading = false;
        if (res.code === 5001) {
          await this.$UiConfirm({ content: res.msg });
          const deleteDeviceRedisRes = await this.$http.post(equipmentassets.deleteDeviceForRedis, {
            deviceBatchId: res.data,
          });
          this.$Message.success(deleteDeviceRedisRes.data.msg);
          this.init();
        } else {
          this.$Message.error(res.msg);
        }
      } catch (err) {
        console.error(err, 'err');
      }
    },
  },
  watch: {
    'searchData.isCheck'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.baseCheckStatus = '';
        this.searchData.viewCheckStatus = '';
        this.searchData.videoCheckStatus = '';
        this.searchData.baseErrorMessageList = [];
        this.searchData.viewErrorMessageList = [];
        this.searchData.videoErrorMessageList = [];
      }
    },
    'searchData.baseCheckStatus'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.baseErrorMessageList = [];
      }
    },
    'searchData.viewCheckStatus'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.viewErrorMessageList = [];
      }
    },
    'searchData.videoCheckStatus'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.videoErrorMessageList = [];
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
      propertySearch_isonline: 'algorithm/propertySearch_isonline', // 在线状态
      sbgnlxExtList: 'algorithm/getSbgnlxExt', // 设备功能类型扩展
      phyStatusExtList: 'algorithm/getPhyStatusExt', // 设备状态扩展
      sourceDomainList: 'algorithm/propertySearch_sourceDomain', //数据来源域
    }),
    // hasUnqualified() {
    //   return this.searchData.checkStatuses.findIndex((row) => row === '3') !== -1
    // },
  },
  components: {
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    DataSource: require('@/api-components/data-source.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    ChartsContainer: require('@/views/viewassets/components/chartsContainer').default,
    ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UploadError: require('@/views/datagovernance/onlinegovernance/information/components/upload-error.vue').default,
    AreaSelect: require('@/components/area-select').default,
    UiSearch: require('@/components/ui-search').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    EquipmentMap: require('@/views/viewassets/equipmentassets/equipmentmap/index.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.equipmentlibrary-list {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .ui-select-tabs {
    @{_deep} .single {
      left: 0;
    }
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .charts {
    margin: 20px 0 0 20px;
  }
  .ui-label {
    margin-right: 30px;
  }
  .search-module {
    position: relative;
    margin: 10px 20px 0px;
    .keyword-input {
      width: 300px;
    }
    .ui-label {
      margin-right: 30px;
    }

    .right-btn {
      float: right;
    }
  }
  .ui-table {
    padding: 0 20px;
  }
  //@{_deep} .select-width {
  //  width: 160px;
  //}
}
.tooltip-type {
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tree-select {
  width: 300px !important;
}

.btns {
  padding-right: 20px;
  margin-bottom: 12px;
}

.left-select {
  width: 150px !important;
}
.right-select {
  width: 240px !important;
}

.rotate {
  transition: all 0.2s ease-in-out;
  display: inline-block;
  transform: rotate(180deg);
}
.norotate {
  transition: all 0.2s ease-in-out;
  display: inline-block;
  transform: rotate(0deg);
}

.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}

@{_deep} .ivu-poptip-inner {
  white-space: initial;
}
@{_deep} .dropdown {
  .ivu-dropdown-item {
    color: var(--color-select-item);
    display: flex;
    align-items: center;
    .icon-font {
      margin-right: 5px;
    }
  }
  .ivu-select-dropdown {
    background-color: var(--bg-select-dropdown);
  }
  .ivu-dropdown-item:hover {
    background: var(--bg-select-item-active);
    color: var(--color-select-item-active);
    .ivu-btn-text {
      color: var(--color-select-item-active);
    }
  }
  .import-device-delete {
    line-height: normal;
    height: auto;
    color: var(--color-select-item);
    &:hover {
      color: var(--color-primary);
    }
  }
}
</style>
