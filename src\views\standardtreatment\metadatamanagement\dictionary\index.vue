<template>
  <!-- 基础表管理 -->
  <div class="dictionary" ref="mains">
    <div class="form">
      <label for="关键词" class="based-field-label">关键词</label>
      <Input v-model="searchData.searchValue" placeholder="内部标识代码/名称" style="width: 200px" />
      <div class="inline ml-sm">
        <Button type="primary" @click="search(searchData.searchValue)">查询</Button>
        <Button class="ml-sm" @click="resetSearchDataMx1(searchData, search)">重置</Button>
      </div>
      <Button
        type="primary"
        @click="add()"
        class="fr"
        v-permission="{ route: 'dictionary', permission: 'dictionaryAdd' }"
      >
        <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"></i>
        <span class="vt-middle">新增字典</span>
      </Button>
    </div>

    <div class="left-div">
      <ui-table
        class="ui-table"
        :loading="loading"
        :table-columns="tableColumns"
        :table-data="tableData"
        ref="table"
        :minus-height="240"
      >
        <template slot-scope="{ row }" slot="fieldType">{{ metaFieldType[row.fieldType] }}</template>
        <template slot-scope="{ row }" slot="fieldTypeValue">{{ metaFieldTypeValue[row.fieldType] }}</template>

        <template slot-scope="{ row }" slot="action">
          <ui-btn-tip
            icon="icon-daimabiao"
            class="mr-md"
            content="代码表"
            :row="row"
            @click.native="codeOpen(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            icon="icon-bianji2"
            class="mr-md"
            content="编辑"
            :row="row"
            @click.native="edit(row)"
            v-permission="{ route: 'dictionary', permission: 'dictionaryEdit' }"
          ></ui-btn-tip>
          <ui-btn-tip
            icon="icon-shanchu3"
            content="删除"
            :row="row"
            @click.native="deleteItem(row)"
            v-permission="{ route: 'dictionary', permission: 'dictionaryDel' }"
          ></ui-btn-tip>
          <!-- <a @click="codeOpen(row)" class="mr30">代码表</a> -->
          <!-- v-if="row.haveCode == 1" -->
          <!-- <span v-else class="mr30" style="color:rgba(255,255,255,0.25);cursor: no-drop;">代码表</span> -->
          <!-- <a @click="edit(row)" class="mr30">编辑</a>
          <a @click="deleteItem(row)">删除</a> -->
        </template>
      </ui-table>
      <loading v-if="loading"></loading>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>

    <!-- 新增数据 -->
    <dictionaryAddModal :metaFieldTypeList="metaFieldTypeList" @search="search" ref="dictionaryAddModal" />
    <!-- 编辑数据 -->
    <dictionaryEditModal :metaFieldTypeList="metaFieldTypeList" @search="search" ref="dictionaryEditModal" />
    <!-- 代码表 -->
    <codeTable ref="codeTable" />
  </div>
</template>
<script>
import metadatamanagement from '@/config/api/metadatamanagement';
import algorithm from '@/config/api/algorithm';
export default {
  name: 'dictionary',
  data() {
    return {
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      copySearch: {},
      searchData: {
        searchValue: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '内部标识代码', key: 'identCode' },
        { title: '中文名称', key: 'identName' },
        { title: '标识符', key: 'tag' },
        { title: '字符类型', slot: 'fieldType' },
        { title: '字符格式', slot: 'fieldTypeValue' },
        { title: '字符长度', key: 'fieldLength' },
        {
          title: '操作',
          slot: 'action',
          align: 'left',
          width: 124,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      tableData: [],
      metaFieldType: {},
      metaFieldTypeList: [],
      metaFieldTypeValue: {},
    };
  },
  created() {},
  activated() {
    this.loading = true;
    this.$nextTick(() => {
      this.info();
    });
  },
  methods: {
    async info() {
      await this.getAlgorithmList();
      await this.infoList();
    },
    async infoList() {
      this.loading = true;
      try {
        let res = await this.$http.post(metadatamanagement.dictPageList, this.searchData);
        this.tableData = res.data.data.entities || [];
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        this.loading = false;
        console.log(err);
      }
    },
    async getAlgorithmList() {
      await this.$http.get(algorithm.dictData + 'metaFieldType').then((res) => {
        if (res.data.code === 200) {
          res.data.data.map((val) => {
            this.metaFieldType[val.dataKey] = val.dataDes;
            this.metaFieldTypeValue[val.dataKey] = val.dataValue;
          });
          this.metaFieldTypeList = res.data.data;
          this.$forceUpdate();
        }
      });
    },
    // 重置（ui-page与后台参数不统一，单独制空pageNumber）
    resetSearchDataMx1() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        searchValue: '',
        params: { pageNumber: 1, pageSize: 20 },
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.infoList();
    },
    // 检索
    search() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.infoList();
    },
    // 返回
    back() {
      this.$emit('back');
    },
    // 删除数据
    deleteItem(item) {
      // this.$Modal.confirm({
      // 	title: "警告",
      // 	content: `您将要删除字典 ${item.identName}，是否确认?`,
      // 	onOk: () => {
      // 		this.remove(item)
      // 	}
      // });
      this.$UiConfirm({
        content: `您将要删除字典 ${item.identName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.remove(item);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    // 删除
    async remove(item) {
      try {
        let res = await this.$http.delete(metadatamanagement.dictRemove + item.id);
        if (res.data.code === 200) {
          this.$Message.success('删除成功');
          this.infoList();
        } else {
          this.$Message.error('删除失败');
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 新增
    add() {
      this.$refs.dictionaryAddModal.open('新增字典');
    },
    // 编辑
    edit(row) {
      this.$refs.dictionaryEditModal.open(row);
    },
    // 代码表
    codeOpen(row) {
      this.$refs.codeTable.open(row);
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    dictionaryAddModal: require('./dictionary-addmodal.vue').default,
    dictionaryEditModal: require('./dictionary-editmodal.vue').default,
    codeTable: require('./code-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.dictionary {
  height: 100%;
  background: var(--bg-content);
  .dictionary-header {
    padding: 20px;
    .title {
      display: flex;
      .border-header {
        width: 8px;
        height: 30px;
        background: #239df9;
        margin-right: 6px;
      }
      .title-header {
        color: #fff;
        width: 394px;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        font-size: 16px;
      }
    }
    .back {
      color: #2d8cf0;
      font-size: 16px;
      cursor: pointer;
      height: 30px;
      line-height: 30px;
      &:hover {
        color: #57a3f3;
      }
    }
  }
  /deep/ .ivu-divider-horizontal {
    margin: 0;
    background: #1e3f61;
  }
  .dictionary-introduction {
    display: flex;
    background: var(--bg-content);
    padding: 16px 20px 0 20px;
    & > div {
      padding-right: 40px;
      color: #fff;
      font-size: 14px;
    }
    /deep/ .ui-label .label {
      color: rgba(255, 255, 255, 0.75);
    }
  }
  .left-div {
    padding: 0 20px;
    position: relative;
  }
  .form {
    padding: 20px;
    .based-field-label {
      color: var(--color-label);
      font-size: 14px;
      padding-right: 12px;
    }
    button {
      margin-left: 12px;
    }
  }
  .mr30 {
    margin-right: 30px !important;
  }
}
</style>
