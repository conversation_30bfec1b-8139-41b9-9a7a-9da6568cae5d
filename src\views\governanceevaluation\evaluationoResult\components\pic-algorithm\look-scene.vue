<template>
  <div ref="lookSceneView">
    <ui-modal v-model="visible" :styles="modalStyle" :title="title" :footer-hide="true">
      <PicCarousel
        :visible="visible"
        :img-list="imgList"
        :view-index="viewIndex"
        :filed-name-map="filedNameMap"
        @getAlgorithmInfo="getAlgorithmInfo"
      >
        <template #other="{ currentItem, showLeftBox, showRightBox }">
          <div
            v-if="
              currentItem[filedNameMap.qualified] === '2' && !showLeftBox && !showRightBox
            "
            class="error-info"
          >
            <p class="p-text"><span>异常原因：</span></p>
            <span class="red-text">{{ currentItem[filedNameMap.resultTip] }}</span>
          </div>
        </template>
      </PicCarousel>
    </ui-modal>
  </div>
</template>

<script>
export default {
  components: {
    PicCarousel: require('./pic-carousel.vue').default,
  },
  props: {
    value: {},
    viewIndex: {
      default: 0,
    },
    imgList: {
      type: Array,
      required: true,
    },
    title: {
      type: String,
      default: '查看大图',
    },
    // 自定义字段名称 [兼容取值字段不一致问题]
    filedNameMap: {
      default: () => {
        return {
          smallPicName: 'imageUrl', // 小图
          bigPicName: 'imageUrl', // 大图
          resultTip: 'resultTip', // 图片备注
          qualified: 'qualified', // 不合格
          // ...
        };
      },
    },
  },
  data() {
    return {
      visible: false,
      modalStyle: {
        width: '9.5rem',
        height: '4.4rem',
      },
      index: 0,
    };
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  methods: {
    getAlgorithmInfo(algorithmInfo, currentItem) {
      let el = this.$refs.lookSceneView;
      if (!algorithmInfo.showLeftBox && !algorithmInfo.showRightBox) {
        this.modalStyle.width = '6.5rem';
        this.modalStyle.height = '4rem';
        el && currentItem[this.filedNameMap.qualified] === '2' ? el.classList.add('look-scene-view') : '';
      } else {
        this.modalStyle.width = '9.5rem';
        this.modalStyle.height = '4.4rem';
        el ? el.classList.remove('look-scene-view') : '';
      }
    },
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-header {
  padding: 0;
}
@{_deep} .ivu-modal-body {
  height: 100%;
  padding: 5px 0 0;
}
@{_deep} .ivu-modal-content {
  width: 100%;
  height: 100%;
}
.look-scene-view {
  @{_deep} .img-list .content {
    height: calc(100% - 80px) !important;
  }
  .error-info {
    color: var(--color-content);
    padding: 20px 0;
    .red-text {
      color: var(--color-failed);
    }
    .p-text {
      position: relative;
      display: inline-block;
      padding-left: 18px;
      &::before {
        content: ' ';
        width: 11px;
        height: 11px;
        display: inline-block;
        background: rgba(188, 60, 25, 0.3);
        transform: rotate(45deg);
        position: absolute;
        top: 5px;
        left: 0;
      }
      &::after {
        content: ' ';
        width: 5px;
        height: 5px;
        display: inline-block;
        background: var(--color-failed);
        transform: rotate(45deg);
        position: absolute;
        top: 8px;
        left: 3px;
      }
    }
  }
}
</style>
