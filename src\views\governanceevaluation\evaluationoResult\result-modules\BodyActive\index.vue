<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      @on-change-code="onChangeOrgRegion"
    >
      <tag-view
        slot="mid"
        :list="reallyTagList"
        :default-active="defaultActive"
        @tagChange="changeStatus"
        ref="tagView"
        class="tag-view"
      ></tag-view>
    </result-title>
    <component :is="componentName" v-bind="handleProps()" @viewDetail="viewDetail"></component>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'BayonetOnlineRate',
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalEcharts',
      iconList: [],
      tagList: Object.freeze([
        {
          label: '统计图表',
          value: 'StatisticalEcharts',
        },
        {
          label: '统计列表',
          value: 'StatisticalResult',
        },
        {
          label: '检测明细',
          value: 'ReviewParticular',
        },
      ]),
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
    };
  },
  created() {
    this.reallyTagList = this.tagList.map((item) => item.label);
    this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    changeStatus(index) {
      this.defaultActive = index;
      this.componentName = this.tagList[index].value;
    },
    // 动态组件 - 动态传参
    handleProps() {
      // 检测明细组件需要的参数 review-particular
      let props = {
        activeIndexItem: this.activeIndexItem,
      };
      return props;
    },
    async viewDetail(row) {
      this.defaultActive = 2;
      const queryParams = this.$route.query;
      this.defaultCodeKey = queryParams.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.defaultCode = queryParams.statisticType === 'REGION' ? row.civilCode : row.orgCode;
      await this.$nextTick();
      this.$set(this, 'componentName', 'ReviewParticular');
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
    }),
  },
  watch: {},
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ResultTitle: require('../../components/result-title.vue').default,
    ReviewParticular: require('./review-particular.vue').default,
    StatisticalEcharts: require('./statistical-echarts.vue').default,
    StatisticalResult: require('../../common-pages/statistical-results/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
}
</style>
