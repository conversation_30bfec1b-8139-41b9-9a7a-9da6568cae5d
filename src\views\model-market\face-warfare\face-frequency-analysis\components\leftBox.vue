<!--
    * @FileDescription: 
    * @Author: H
    * @Date: 2024/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="leftBox">
    <searchBox
      ref="searchBox"
      @seleArea="seleArea"
      @searchList="querySearch"
      @reset="$emit('reset')"
      @packBox="handlePackup"
    ></searchBox>
    <div
      class="object-information"
      :style="{ height: `calc( 100% - ${top + 10}px )` }"
      v-if="showlist"
    >
      <div class="title">
        <p>{{ title }}</p>
        <Button
          v-if="fuseShow"
          type="primary"
          class="btnwidth"
          @click="handleMoreAnalysis"
          >融合频次分析</Button
        >
        <Icon type="ios-close" @click="handleCancel" />
      </div>
      <div class="box-modal">
        <ul
          class="box-content"
          :class="{ 'overflow-box': objectMsgList.length > 3 }"
          v-infinite-scroll="load"
          :infinite-scroll-distance="10"
        >
          <li
            class="box-list"
            :class="{ 'active-box-list': objectIndex == index }"
            v-for="(item, index) in objectMsgList"
            :key="index"
          >
            <div class="content-top">
              <div class="content-top-img">
                <template v-if="item.photos && item.photos.length > 0">
                  <img-list :dataObj="item" :index="index"></img-list>
                </template>
                <ui-image v-else viewer :src="item.photo" />
                <div v-if="item.score" class="similarity">
                  <span>{{ getScore(item.score) }}%</span>
                </div>
              </div>
              <div class="content-top-right">
                <span class="ellipsis">
                  <ui-icon type="xingming" :size="14"></ui-icon>
                  <span class="block">{{ item.xm || "--" }}</span>
                </span>
                <span class="ellipsis">
                  <ui-icon type="shenfenzheng" :size="14"></ui-icon>
                  <span class="bule" :class="{ block: !item.gmsfhm }">{{
                    item.gmsfhm || "--"
                  }}</span>
                </span>
                <!-- <span class="ellipsis">
                  <ui-icon type="camera" :size="14"></ui-icon>
                  <span class="orange" :class="{ block: !item.archiveNo }">{{
                    item.archiveNo || "--"
                  }}</span>
                </span> -->
                <div class="checkbox-box">
                  <Checkbox v-model="item.select"></Checkbox>
                </div>
              </div>
            </div>
            <div class="content-bottom">
              <div class="iconList">
                <!-- <opera-floor iconSec="icon-dangan2"></opera-floor> -->
              </div>
              <div
                class="analyseIcon"
                :class="{ activeAnaly: objectIndex === index }"
                @click="handleAnalyse($event, item, index)"
              >
                <ui-icon type="duoren" :size="14"></ui-icon>
                <span>频次分析</span>
              </div>
            </div>
          </li>
          <ui-empty
            v-if="objectMsgList.length === 0 && loading == false"
          ></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
          <p class="loading" v-if="loadingText">加载中...</p>
          <p class="endlist" v-if="noMore && objectMsgList.length !== 0">
            没有更多了
          </p>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { personTargetPageList, associatedObjects } from "@/api/modelMarket";
import searchBox from "./searchBox.vue";
import operaFloor from "../../../components/operat-floor/index.vue";
import imgList from "../../components/imgList/index.vue";
export default {
  name: "",
  components: {
    searchBox,
    operaFloor,
    imgList,
  },
  props: {
    title: {
      type: String,
      default: "对象信息",
    },
  },
  data() {
    return {
      objectMsgList: [],
      loading: false,
      listIcon: ["xingming", "shenfenzheng", "camera"],
      field: {
        0: ["xm", "gmsfhm", "archiveNo"],
        1: ["plateNo", "ownerName", "idcardNo"],
      },
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      objectIndex: -1,
      loadingText: false,
      noMore: false,
      total: 0,
      searchInfo: {},
      typaTag: {},
      showlist: false,
      top: 0,
      searchParams: {},
    };
  },
  watch: {},
  computed: {
    fuseShow() {
      let list = this.objectMsgList.filter((item) => item.select);
      if (list.length > 1) {
        return true;
      } else {
        return false;
      }
    },
  },
  created() {},
  mounted() {
    this.objHeight();
  },
  methods: {
    // 高度
    objHeight() {
      setTimeout(() => {
        this.top = document.querySelector(".search_box").scrollHeight + 20;
      }, 210);
    },
    //
    handlePackup() {
      this.objHeight();
    },
    /**
     * tab:0: 人员， 1：车辆， 2：RFID 3：WI-Fi 4: 电围
     * secTab 0：身份证号， 1：人脸照片
     */
    querySearch(item) {
      this.showlist = true;
      this.objHeight();
      this.$emit("reset");
      this.init(item);
    },
    seleArea() {
      this.$emit("seleArea");
    },
    init(item, isFirst = true) {
      if (isFirst) {
        this.objectMsgList = [];
        this.loading = true;
        this.page.pageNumber = 1;
      } else {
        this.loadingText = true;
      }
      this.noMore = false;
      let params = {};
      this.searchInfo = item;
      params = {
        features: item.features,
        searchContent: item.searchContent,
        similarity: item.similarity / 100,
        dateType: item.dateType,
        deviceIds: item.deviceIds,
        endDate: item.endDate,
        startDate: item.startDate,
      };
      this.searchParams = params;
      personTargetPageList({ ...params, ...this.page })
        .then((res) => {
          let list = (res.data && res.data.entities) || [];
          this.objectMsgList = this.objectMsgList.concat(...list);
          this.total = res.data.total || 0;
        })
        .finally(() => {
          this.loading = false;
          this.loadingText = false;
          this.noMore = false;
        });
    },
    // 频次分析
    handleAnalyse(event, item, index) {
      this.objectIndex = index;
      //   this.$emit('cutAnalyse', { ...this.searchParams, vidList: [item.archiveNo] })
      let params = {
        archiveNo: item.archiveNo,
        dataType: this.searchParams.searchContent ? 1 : 2,
      };
      console.log(this.searchParams);
      associatedObjects(params).then((res) => {
        this.$emit("cutAnalyse", {
          ...this.searchParams,
          vidList: res.data.vids,
        });
      });
    },
    // 融合频次分析
    handleMoreAnalysis() {
      let list = this.objectMsgList.filter((item) => item.select);
      let vidList = list.map((item) => item.archiveNo);
      this.$emit("cutAnalyse", { ...this.searchParams, vidList });
    },
    // 加载
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        return;
      } else {
        this.noMore = false;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.init(this.searchInfo, false);
      }
    },
    // 反显设备
    showDevice(list) {
      list.map((item) => {
        item.select = true;
      });
      this.$refs.searchBox.selectData(list);
    },
    // 关闭
    handleCancel() {
      this.showlist = false;
      this.$emit("reset");
    },
    getScore(val) {
      let a = (val * 100).toFixed(2);
      return a;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
@import "../../components/style/boxContent";
.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;
  height: calc(~"100% - 20px");
}
</style>
