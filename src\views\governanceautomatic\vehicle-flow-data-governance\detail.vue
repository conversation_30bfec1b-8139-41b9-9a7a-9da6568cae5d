<template>
  <ui-modal v-model="visible" title="查看图片" :styles="styles" footer-hide>
    <div class="auto-fill table-module" v-ui-loading="{ loading: loading, tableData: tableData }">
      <!-- 属性缺少 -->
      <pic-row-mode
        class="pic-row-mode-box"
        v-if="this.detailData.imageType === '1'"
        :card-list="tableData"
        small-img-key="plateImagePath"
        img-key="targetPicUrl"
      >
        <template #default="{ cardData }">
          <div class="pic-mode-card-state f-14">
            <p class="mr-md pic-mode-card-state-p">
              <i class="icon-font icon-shijian mr-xs"></i>
              <span
                class="inline ellipsis"
                :class="!cardData.captureTime ? 'color-failed' : 'base-text-color'"
                :title="cardData.captureTime"
                >{{ cardData.captureTime || '抓拍时间缺失' }}</span
              >
            </p>
            <p class="pic-mode-card-state-p f-14">
              <i class="icon-font icon-dizhi mr-xs"></i>
              <span
                class="ellipsis inline"
                :class="!cardData.crossingIndexCode ? 'color-failed' : 'base-text-color'"
                :title="cardData.crossingIndexCode"
                >{{ cardData.crossingIndexCode || '抓拍地址缺失' }}</span
              >
            </p>
          </div>
        </template>
      </pic-row-mode>

      <!-- 时间倒挂 -->
      <pic-mode
        v-if="this.detailData.imageType === '2'"
        :card-list="tableData"
        small-img-key="plateImagePath"
        img-key="targetPicUrl"
        :pic-mode-count="7"
      >
        <template #cardInfo="{ item }">
          <div class="mt-sm">
            <div class="pic-text-item">
              <span class="info-label">抓拍：</span>
              <span
                class="info-value ellipsis"
                :title="item.captureTime"
                :class="!item.captureTime ? 'color-failed' : 'base-text-color'"
                >{{ item.captureTime || '未知' }}</span
              >
            </div>
            <div class="pic-text-item">
              <span class="info-label">接收：</span>
              <span
                class="info-value ellipsis"
                :title="item.createTime"
                :class="!item.createTime ? 'color-failed' : 'base-text-color'"
                >{{ item.createTime || '未知' }}</span
              >
            </div>
            <div class="pic-text-item">
              <span class="ellipsis color-failed">抓拍时间-接收时间>{{ item.betweenTime }}s</span>
            </div>
            <div class="pic-text-item">
              <span class="ellipsis color-failed">时间倒挂</span>
            </div>
          </div>
        </template>
      </pic-mode>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    detailData: {},
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      styles: {
        width: '9.5rem',
      },
      searchData: {
        imageType: '',
        pageNumber: 1,
        pageSize: 20,
      },
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      paramsList: [
        {
          propertyName: '车牌号',
          propertyValue: 'plateNo',
        },
        {
          propertyName: '车道号',
          propertyValue: 'laneNo',
        },
        // {
        //   propertyName: '车辆行驶方向',
        //   propertyValue: 'direction',
        // },
        {
          propertyName: '监控方向',
          propertyValue: 'directionName',
        },
        {
          propertyName: '监控点编号',
          propertyValue: 'cameraIndexCode',
        },
      ],
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(equipmentassets.queryImageList, this.searchData);
        this.tableData = res.data.data.entities.map((item) => {
          const imgItem = {
            ...item,
          };
          imgItem.fileList = this.paramsList.map((one) => {
            return {
              value: item[one.propertyValue] || null,
              label: one.propertyName,
            };
          });
          return imgItem;
        });
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        // （imageType：1、属性字段缺失 2、时间倒挂)
        let { imageType, beginTime, endTime } = this.detailData;
        this.searchData.imageType = imageType;
        this.searchData.beginTime = beginTime;
        this.searchData.endTime = endTime;
        this.init();
      }
      this.visible = val;
    },
  },
  components: {
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    PicRowMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-row-mode/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .tips {
    color: #e44f22;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .tips {
    color: var(--color-failed);
  }
}
.tips {
  vertical-align: middle;
  display: inline-block;
  width: 120px;
}
@{_deep}.ivu-modal-body {
  height: 750px;
  display: flex;
  flex-direction: column;
}
.pic-text-item {
  font-size: 14px;
  display: flex;
  align-items: center;
}
@{_deep}.ivu-select-selection {
  > div {
    display: flex;
  }
}

@{_deep}.pic-row-mode-box {
  .pic-mode-card-info p {
    margin-top: 0;
    margin-bottom: 6px;
  }
  .pic-mode-card-img {
    height: inherit;
  }

  .pic-mode-item {
    width: calc((100% - 40px) / 4);
    .card-info p {
      display: flex;
      width: 100%;
    }
  }
}

@{_deep}.info-label {
  width: fit-content;
}
@{_deep}.info-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
@{_deep}.ivu-modal-body {
  padding: 16px 50px;
}
</style>
