<template>
  <Modal
    ref="modal"
    title="请校准时间"
    v-model="isShow"
    class="modalExtendClass"
    @on-cancel="modalClosed"
  >
    <template>
      <DatePicker
        v-model="time"
        :clearable="false"
        type="datetime"
        format="yyyy-MM-dd HH:mm:ss"
        placeholder="请输入校准时间"
        transfer
      ></DatePicker>
    </template>
    <div
      slot="footer"
      style="padding: 10px; text-align: center"
      class="nui-border"
    >
      <Button color="ghost" @click="saveTime">保存</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: "adjustTime",
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    adjustProps: {
      type: Array,
      require: () => [],
    },
    adjustTimeKey: {
      type: String,
      default: "baseTime",
    },
  },
  watch: {
    value: {
      handler(val) {
        this.isShow = val;
        if (val) this.time = new Date(this.adjustProps[0][this.adjustTimeKey]);
      },
      immediate: true,
    },
  },
  data() {
    return {
      isShow: false,
      time: "",
    };
  },
  methods: {
    modalClosed() {
      this.$emit("adjustTimeClose");
    },
    saveTime() {
      this.adjustProps.forEach((item, index) => {
        item[this.adjustTimeKey] = new Date(this.time).getTime();
        item.adjustTime = Toolkits.mills2datetime(this.time);
      });
      this.$emit("adjustTimeClose", this.adjustProps);
    },
  },
};
</script>

<style lang="less">
.modalExtendClass {
  text-align: center;
  .xui-modal-box {
    width: 410px !important;
    .xui-modal-header {
      height: 46px;
    }
    .xui-modal-content {
      padding: 28px 50px 28px 50px;
      .np-date-picker-panel {
        width: 100%;
      }
    }
    .xui-modal-footer {
      height: 46px;
      width: 100%;
      div {
        text-align: right !important;
        border: 0 !important;
        padding-right: 16px !important;
      }
    }
  }
}
</style>
