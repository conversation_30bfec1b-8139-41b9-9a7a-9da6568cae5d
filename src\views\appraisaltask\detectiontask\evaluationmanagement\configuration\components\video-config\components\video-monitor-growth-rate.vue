<template>
  <div class="config-container">
    <common-form
      ref="commonForm"
      :label-width="getWidth"
      :form-data="formData"
      :form-model="formModel"
      :module-action="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <div slot="waycondiction" class="mt-xs">
        <p>
          <span class="base-text-color">检测条件：</span>
          <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
            >设备可用
          </Checkbox>
        </p>
        <p class="color-failed">说明：系统只检测满足过滤条件的设备，未选择过滤条件，检测全部设备。</p>
      </div>
    </common-form>
    <div class="">
      <batch-import-num
        ref="BatchImportNum"
        :tree-data="areaTreeData"
        :assessment-items="assessmentItems"
        assessment-item-size="width-md"
        @updateTreeData="handleUpdateTreeData"
      >
        <template #filtermodule>
          <div class="tree-select-num mb-md">
            <div class="tree-select-num-left">
              <ui-label required class="inline ml-lg" label="去年年末设备数量" :width="70"></ui-label>
            </div>
            <div class="tree-select-num-right">
              <ui-label required class="inline ml-lg" label="设备数量" :width="70">
                <InputNumber v-model="batchImportNum" class="width-md" placeholder="请输入达标数量"> </InputNumber>
              </ui-label>
              <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
            </div>
          </div>
        </template>
      </batch-import-num>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'video-monitor-growth-rate',
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
      batchImportNum: 1,
      assessmentItems: [{ title: '去年年末设备数量', key: 'lastYearNum', optionLabel: '' }],
      // 1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所
      // regionTypeData: [
      //   { type: 'province', value: ['1'] },
      //   { type: 'city', value: ['2', '3', '4', '5'] },
      //   { type: 'region', value: ['6', '7', '8'] },
      //   { type: 'policeStation', value: ['9'] },
      // ],
      filterTreeData: [],
      areaTreeData: [],
    };
  },
  created() {
    this.getModalWidth();
  },
  methods: {
    getModalWidth() {
      this.screenWidth = window.screen.width;
      const modalWidth = this.screenWidth > 1890 ? '4.2rem' : '5rem';
      this.$emit('changeModalWidth', modalWidth);
    },
    updateFormData(val) {
      this.formData = {
        ...val,
      };
      if (this.formData.detectMode !== '3') {
        this.formData.deviceQueryForm = {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus || '0',
        };
      }
    },
    clickBatchInput() {
      if (!this.batchImportNum) {
        return this.$Message.error('请输入设备数量');
      }
      this.handleBatchChildInputVal(this.areaTreeData);
    },
    handleBatchChildInputVal(data) {
      data.map((item) => {
        if (!!item.check && this.assessmentItems.length) {
          this.assessmentItems.map((assessItem) => {
            this.$set(item, assessItem.key, this.batchImportNum);
          });
        }
        if (item.children) this.handleBatchChildInputVal(item.children);
      });
    },
    handleSubmit() {
      const batchImportData = this.$refs.BatchImportNum.validateCheckedNodes();
      if (batchImportData) {
        const regionConfigData = batchImportData.map((item) => {
          return {
            regionCode: item.regionCode,
            lastYearNum: item.lastYearNum,
          };
        });
        this.$set(this.formData, 'regionConfigData', regionConfigData);
      }
      return !!batchImportData && this.$refs['commonForm'].handleSubmit();
    },
    getTreeData(regionData) {
      regionData.forEach((regionItem) => {
        this.filterTreeData.forEach((item) => {
          if (item.regionCode === regionItem.regionCode) {
            this.$set(item, 'check', true);
            if (!this.assessmentItems.length) return false;
            this.assessmentItems.forEach((assessItem) => {
              this.$set(item, assessItem.key, regionItem[assessItem.key]);
            });
          }
        });
      });
      const regionTreeData = this.$util.common.deepCopy(this.filterTreeData);
      this.areaTreeData = this.$util.common.arrayToJson(regionTreeData, 'regionCode', 'parentCode');
    },
    handleUpdateTreeData(data) {
      this.areaTreeData = data;
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
    }),
    getWidth() {
      return 180;
    },
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = { ...this.configInfo };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
          };
        }
      },
      immediate: true,
    },
    'moduleAction.regionCode': {
      handler(val) {
        if (!val) {
          this.filterTreeData = JSON.parse(JSON.stringify(this.treeData));
        } else {
          let treeArr = this.treeData.filter((item) => item.regionCode === val || item.parentCode === val);
          this.filterTreeData = this.$util.common.deepCopy(treeArr);
        }
      },
      immediate: true,
    },
    'configInfo.regionConfigData': {
      handler(val) {
        const regionData = this.formModel === 'add' ? [] : val || [];
        this.getTreeData(regionData);
      },
      immediate: true,
    },
  },
  components: {
    CommonForm: require('../../common-form/index.vue').default,
    BatchImportNum: require('./batch-import-num').default,
  },
};
</script>

<style lang="less" scoped>
.tree-select-num {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
