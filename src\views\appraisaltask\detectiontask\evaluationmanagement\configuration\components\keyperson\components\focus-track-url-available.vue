<template>
  <div class="keyperson">
    <Form ref="modalData" class="form-content edit-form" :model="formData" :label-width="200" :rules="ruleCustom">
      <Collapse simple v-model="collapse">
        <Panel name="1">
          基础信息
          <div slot="content" class="mt-md">
            <common-form
              ref="commonForm"
              :label-width="200"
              :form-data="formData"
              :form-model="formModel"
              :module-action="moduleAction"
              :task-index-config="taskIndexConfig"
              @updateFormData="updateFormData"
            >
              <template #extract>
                <FormItem
                  prop="deviceQueryForm.dayByCapture"
                  label="图片抽取范围"
                  :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
                  v-if="['2', '3'].includes(formData.detectMode)"
                >
                  <span class="base-text-color mr-xs">近</span>
                  <InputNumber
                    v-model.number="formData.dayByCapture"
                    :min="0"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color">天，</span>
                  <InputNumber
                    v-model.number="formData.startByCapture"
                    :min="0"
                    :max="23"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color mr-sm">点至</span>
                  <InputNumber
                    v-model.number="formData.endByCapture"
                    :min="0"
                    :max="23"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color mr-sm">点</span>
                </FormItem>
              </template>
            </common-form>
          </div>
        </Panel>
        <Panel name="2">
          检测参数
          <div slot="content" class="mt-md">
            <FormItem label="最大等待时长">
              <InputNumber
                v-model.number="formData.visitTimeout"
                :min="0"
                :max="Number.MAX_SAFE_INTEGER"
                :precision="0"
                placeholder="请输入最大等待时长"
                class="mr-sm width-lg"
              ></InputNumber>
              <span class="base-text-color">秒</span>
            </FormItem>
            <FormItem label="检测大图标注抓拍时间和地点">
              <RadioGroup v-model="formData.snap" @on-change="onChangeSnap">
                <Radio :label="1">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              v-if="formData.snap !== 0 && !!formData.snap"
              label="优先算法"
              prop="ocrModel"
              key="'isShowOcrModel'"
              class="mb-md"
            >
              <Select
                v-model="customFormData.optimizationAlgorithm"
                placeholder="请选择算法"
                transfer
                class="input-width-number"
                @on-change="onChangeOcrModel($event, 'optimizationAlgorithm')"
              >
                <template v-for="(algorithmItem, index) in algorithmVendorData">
                  <Option
                    :value="`${algorithmItem.algorithmType},${algorithmItem.algorithmVendorType}`"
                    :key="`algorithmItem${index}`"
                    :data="algorithmItem.algorithmType"
                  >
                    {{ algorithmItem.algorithmVendorTypeName }}-{{ algorithmItem.algorithmTypeName }}
                  </Option>
                </template>
              </Select>
            </FormItem>
            <!--     深网 才支持751       -->
            <FormItem class="mb-xs" v-if="customFormData.optimizationAlgorithm">
              <Checkbox
                v-model="customFormData.optimizationAlgorithm751"
                @on-change="onChange751($event, 'optimizationAlgorithm')"
                :true-value="1"
                :false-value="0"
                >按照《GAT-751-2008视频图像文字标注规范》要求检测</Checkbox
              >
            </FormItem>
            <FormItem
              class="mb-md"
              v-if="customFormData.optimizationAlgorithm && customFormData.optimizationAlgorithm751"
            >
              <template #label>
                <Tooltip placement="top-start" class="tooltip-sample-graph">
                  <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                  <div slot="content">
                    <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
                  </div>
                  检测内容
                </Tooltip>
              </template>
            </FormItem>
            <detect-content
              ref="optimizationAlgorithmDetectContent"
              v-bind="$props"
              :config-info="formData.optimizationAlgorithm"
              :detect-content-list="formData.optimizationAlgorithm.detectContent"
              v-if="customFormData.optimizationAlgorithm && customFormData.optimizationAlgorithm751"
            >
            </detect-content>
            <FormItem v-if="formData.snap !== 0 && !!formData.snap" label="备用算法" class="mb-lg">
              <Select
                v-model="customFormData.standbyAlgorithm"
                placeholder="请选择备用算法"
                transfer
                clearable
                class="input-width-number"
                @on-change="onChangeOcrModel($event, 'standbyAlgorithm')"
              >
                <template v-for="(algorithmItem, index) in algorithmVendorData">
                  <Option
                    :value="`${algorithmItem.algorithmType},${algorithmItem.algorithmVendorType}`"
                    :key="`algorithmItem${index}2`"
                    :data="algorithmItem.algorithmType"
                  >
                    {{ algorithmItem.algorithmVendorTypeName }}-{{ algorithmItem.algorithmTypeName }}
                  </Option>
                </template>
              </Select>
            </FormItem>
            <FormItem class="mb-lg" v-if="customFormData.standbyAlgorithm">
              <Checkbox
                v-model="customFormData.standbyAlgorithm751"
                @on-change="onChange751($event, 'standbyAlgorithm')"
                :true-value="1"
                :false-value="0"
                >按照《GAT-751-2008视频图像文字标注规范》要求检测</Checkbox
              >
            </FormItem>
            <FormItem class="mb-md" v-if="customFormData.standbyAlgorithm && customFormData.standbyAlgorithm751">
              <template #label>
                <Tooltip placement="top-start" class="tooltip-sample-graph">
                  <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                  <div slot="content">
                    <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
                  </div>
                  检测内容
                </Tooltip>
              </template>
            </FormItem>
            <detect-content
              ref="standbyAlgorithmDetectContent"
              v-bind="$props"
              :config-info="formData.standbyAlgorithm"
              :detect-content-list="formData.standbyAlgorithm.detectContent"
              v-if="customFormData.standbyAlgorithm && customFormData.standbyAlgorithm751"
            >
            </detect-content>
          </div>
        </Panel>
        <Panel name="3">
          高级参数
          <div slot="content" class="mt-md">
            <FormItem label="是否需要复检" :label-width="200" class="mb-md">
              <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
                <Radio label="1" class="mr-lg">是</Radio>
                <Radio label="2">否</Radio>
              </RadioGroup>
              <span class="font-red vt-middle">(备注：复检时不会取缓存结果！)</span>
            </FormItem>
            <FormItem label="复检设备" v-if="isRecheck === '1'" :label-width="200" class="mb-md">
              <RadioGroup v-model="formData.reinspect.model">
                <Radio label="UNQUALIFIED">检测不合格设备</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="复检次数" prop="maxCount" :label-width="200" v-if="isRecheck === '1'">
              <InputNumber
                v-model.number="formData.reinspect.maxCount"
                class="mr-xs"
                :max="5"
                :min="1"
                :precision="0"
              ></InputNumber>
              <span class="font-white">次</span>
            </FormItem>
            <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType" :label-width="200" class="mb-md">
              <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
                <Radio label="INTERVAL" class="mr-lg">时间间隔</Radio>
                <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
              </RadioGroup>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
                <span class="font-white">检测结束</span>
                <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
                <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="1">时</Option>
                  <Option :value="2">分</Option>
                </Select>
                <span class="font-white ml-md">后，开始复检</span>
              </div>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
                <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="3">当天</Option>
                  <Option :value="4">第二天</Option>
                  <Option :value="5">第三天</Option>
                </Select>
                <TimePicker
                  :value="formData.reinspect.scheduleValue"
                  transfer
                  format="HH:mm"
                  placeholder="请选择"
                  class="width-xs"
                  @on-change="handleChangeTime"
                ></TimePicker>
              </div>
            </FormItem>
          </div>
        </Panel>
      </Collapse>
    </Form>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import { defaultDetectContent } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    DetectContent:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content.vue')
        .default,
  },
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateOcrModel = (rule, value, callback) => {
      if (this.formData.snap && !this.customFormData.optimizationAlgorithm) {
        callback(new Error('请选择优先算法'));
      }
      callback();
    };
    const validateOptimizationAlgorithmDetectContent = ({ triggerElement }, value, callback) => {
      if (this.customFormData[`${triggerElement}751`] && this.formData[triggerElement].detectContent.length === 0) {
        callback(new Error('请选择检测内容'));
      }
      callback();
    };
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.reinspect.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      indexRuleList: [],
      formData: {
        reinspect: null,
        timeDelay: null,
        format: 'h',
        threshold: null,
        visitTimeout: null,
        dataType: '0',
        detectMode: '1',
        dayByCapture: 2,
        startByCapture: 0,
        endByCapture: 23,
        optimizationAlgorithm: {
          //优先算法
          ocrModel: '', //算法
          algorithmType: '', //算法类型
          detectContent: [], //检测内容
        },
        standbyAlgorithm: {
          //备用算法
          ocrModel: '',
          algorithmType: '',
          detectContent: [],
        },
      },
      customFormData: {
        optimizationAlgorithm: '',
        standbyAlgorithm: '',
        optimizationAlgorithm751: '',
        standbyAlgorithm751: '',
      },
      detectionRules: [
        {
          label: '',
          text: '(右上角) 时间信息是否正确',
          value: 'timeLocation',
        },
        {
          label: '',
          text: '(右下角) 区划与地址信息是否正确',
          value: 'areaLocation',
        },
        {
          label: '',
          text: '(左下角) 摄像机信息是否正确',
          value: 'cameraInfo',
        },
      ],
      ruleCustom: {
        ocrModel: {
          validator: validateOcrModel,
          required: true,
          message: '请选择优先算法',
          trigger: 'change',
        },
        optimizationAlgorithmDetectContent: {
          validator: validateOptimizationAlgorithmDetectContent,
          required: true,
          trigger: 'change',
          triggerElement: 'optimizationAlgorithm',
        },
        standbyAlgorithmDetectContent: {
          validator: validateOptimizationAlgorithmDetectContent,
          required: true,
          trigger: 'change',
          triggerElement: 'standbyAlgorithm',
        },
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      collapse: ['1', '2', '3'],
      isRecheck: '2',
    };
  },
  async created() {},
  watch: {
    moduleAction: {
      handler(val) {
        if (val.config) {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
            optimizationAlgorithm: {
              ...this.formData.optimizationAlgorithm,
              ...this.configInfo.optimizationAlgorithm,
            },
            standbyAlgorithm: {
              ...this.formData.standbyAlgorithm,
              ...this.configInfo.standbyAlgorithm,
            },
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
          };
        }
        this.setOcrModel();
        this.formData.snap = !!this.configInfo && [0, 1].includes(this.configInfo.snap) ? this.configInfo.snap : 1;
        this.formData.captureNum = !!this.configInfo && !!this.configInfo.captureNum ? this.configInfo.captureNum : '';

        if (this.formData.reinspect) {
          this.formData.reinspect.maxCount = this.formData.reinspect.maxCount || null;
          this.isRecheck = '1';
        } else {
          this.isRecheck = '2';
        }
      },
      immediate: true,
    },
  },
  methods: {
    ...mapActions({}),
    onChange751(val, key) {
      this.formData[key] = {
        ...this.formData[key],
        detectContent: val ? ['timeLocation', 'areaLocation'] : [],
        ...defaultDetectContent,
      };
    },
    setOcrModel() {
      // 删除无用字段 ocrModel ocrModel2
      delete this.formData.ocrModel;
      delete this.formData.ocrModel2;
      if (this.formData.optimizationAlgorithm) {
        let { ocrModel, algorithmType, detectContent } = this.formData.optimizationAlgorithm;
        let algorithmVendor = this.algorithmVendorData.find(
          (item) => item.algorithmVendorType === ocrModel && item.algorithmType === algorithmType,
        );
        this.customFormData.optimizationAlgorithm = algorithmVendor
          ? `${algorithmVendor.algorithmType},${algorithmVendor.algorithmVendorType}`
          : '';
        this.customFormData.optimizationAlgorithm751 = detectContent.length > 0 ? 1 : 0;
      }
      if (this.formData.standbyAlgorithm) {
        let { ocrModel, algorithmType, detectContent } = this.formData.standbyAlgorithm;
        let algorithmVendor = this.algorithmVendorData.find(
          (item) => item.algorithmVendorType === ocrModel && item.algorithmType === algorithmType,
        );
        this.customFormData.standbyAlgorithm = algorithmVendor
          ? `${algorithmVendor.algorithmType},${algorithmVendor.algorithmVendorType}`
          : '';
        this.customFormData.standbyAlgorithm751 = detectContent.length > 0 ? 1 : 0;
      }
    },
    /**
     *
     * @param val 算法值 "7,SW"
     * @param key 'optimizationAlgorithm' || 'standbyAlgorithm'
     */
    onChangeOcrModel(val, key) {
      let valList = val ? val.split(',') : [];
      let type = valList[0];
      let vendorType = valList[1];
      this.customFormData[`${key}751`] = 0;
      let { algorithmVendorType, algorithmType } =
        this.algorithmVendorData.find(
          (item) => item.algorithmType === type && item.algorithmVendorType === vendorType,
        ) || {};
      this.formData[key] = {
        ocrModel: algorithmVendorType || '',
        algorithmType: algorithmType || '',
        detectContent: [],
        ...this.defaultDetectContent,
      };
    },
    onChangeSnap() {
      this.formData.optimizationAlgorithm = {
        //优先算法
        ocrModel: '', //算法
        algorithmType: '', //算法类型
        detectContent: [], //检测内容
      };
      this.formData.standbyAlgorithm = {
        //备用算法
        ocrModel: '',
        algorithmType: '',
        detectContent: [],
      };
      this.customFormData = {
        optimizationAlgorithm: '',
        standbyAlgorithm: '',
        optimizationAlgorithm751: '',
        standbyAlgorithm751: '',
      };
    },
    // 表单提交校验
    async handleSubmit() {
      let modalDataValid = await this.$refs['modalData'].validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      if (modalDataValid && commonFormValid) {
        let standbyAlgorithmDetectContent =
          this.$refs.standbyAlgorithmDetectContent && this.$refs.standbyAlgorithmDetectContent.getFormData();
        let optimizationAlgorithmDetectContent =
          this.$refs.optimizationAlgorithmDetectContent && this.$refs.optimizationAlgorithmDetectContent.getFormData();
        if (optimizationAlgorithmDetectContent) {
          this.formData.optimizationAlgorithm = {
            ...this.formData.optimizationAlgorithm,
            ...optimizationAlgorithmDetectContent.formData,
            detectContent: optimizationAlgorithmDetectContent.detectContent,
          };
        }
        if (standbyAlgorithmDetectContent) {
          this.formData.standbyAlgorithm = {
            ...this.formData.standbyAlgorithm,
            ...standbyAlgorithmDetectContent.formData,
            detectContent: standbyAlgorithmDetectContent.detectContent,
          };
        }
      }
      let detectContentValid = true;
      if (this.$refs.optimizationAlgorithmDetectContent) {
        detectContentValid = await this.$refs.optimizationAlgorithmDetectContent.handleSubmit();
      }
      let standDetectContentValid = true;
      if (this.$refs.standbyAlgorithmDetectContent) {
        standDetectContentValid = await this.$refs.standbyAlgorithmDetectContent.handleSubmit();
      }
      return modalDataValid && commonFormValid && detectContentValid && standDetectContentValid;
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
          maxCount: 1,
          scheduleValue: null,
        };
      }
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
  },
  computed: {
    ...mapGetters({
      algorithmVendorData: 'algorithm/getAlgorithmList',
    }),
  },
};
</script>
<style lang="less" scoped>
.keyperson {
  .input-width-number {
    width: 380px;
  }

  @{_deep} .ivu-collapse {
    border: none;
    background: transparent;
    .ivu-collapse-item {
      margin-bottom: 10px;
      border: none;
      .ivu-collapse-header {
        padding-left: 20px;
        background: var(--bg-collapse-item);
        border: none;
        color: var(--color-primary);
        font-size: 16px;
        .ivu-icon,
        ivu-icon-ios-arrow-forward {
          float: right;
          margin-top: 10px;
          color: var(--color-collapse-arrow);
        }
      }
      .ivu-collapse-content {
        background: transparent;
        padding: 0;
        .ivu-collapse-content-box {
          padding: 0;
        }
      }
    }
  }
}
</style>
