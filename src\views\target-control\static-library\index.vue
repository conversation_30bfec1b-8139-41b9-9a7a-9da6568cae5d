<template>
  <div class="static-library">
    <div class="container library-list">
      <div
        v-for="(item, $index) in libraryList"
        :key="$index"
        :class="
          libraryType === item.value ? 'library-item-active bg-primary' : ''
        "
        class="library-item bg-primary-event"
        @click="libraryItemHandle(item)"
      >
        <div class="library-item-title">
          <i class="iconfont icon-shujuyuan primary"></i
          ><span class="name text-color ellipsis">{{ item.name }}</span>
        </div>
        <div class="count primary">{{ item.num }}</div>
      </div>
    </div>
    <div class="container library-content">
      <div class="search">
        <Form ref="searchData" :model="searchData" inline>
          <FormItem prop="libName" label="名称:">
            <Input v-model="searchData.libName" placeholder="请输入名称" />
          </FormItem>
          <FormItem class="btn-group">
            <Button type="primary" @click="searchHandle">查询</Button>
            <Button @click="resetHandle">重置</Button>
          </FormItem>
        </Form>
        <div class="btn-group">
          <Button @click="addStaticLibrary"
            ><i class="iconfont icon-jia" v-permission="['staticLibr-add']"></i
            >新增</Button
          >
          <!-- <Button @click="configureHandle"><i class="iconfont icon-shezhi3"></i>配置</Button> -->
        </div>
      </div>
      <div class="archives-list">
        <div
          v-for="(item, $index) in archivesList"
          :key="$index"
          class="archives-item"
        >
          <div class="archives-item-body" @click="archivesItemHandle(item)">
            <div class="archives-item-body-con card-box">
              <img
                v-if="libraryType === '1'"
                src="@/assets/img/default-img/people_archives_default.png"
                class="archives-img"
                alt
              />
              <img
                v-else
                src="@/assets/img/default-img/vehicle_archives_default.png"
                class="archives-img"
                alt
              />
              <div class="archives-item-info">
                <div class="name primary ellipsis" :title="item.libName">
                  {{ item.libName }}
                </div>
                <div class="switch" @click.stop="">
                  <i-switch
                    :title="
                      item.status == 2
                        ? '已过期'
                        : item.status == 1
                        ? '停用'
                        : '启用'
                    "
                    :disabled="item.status == 2"
                    :value="item.status == 1 ? true : false"
                    @on-change="handleSwitch($event, item)"
                  />
                </div>
                <div class="item">
                  <span class="label title-color">{{
                    libraryType === "1"
                      ? "人员数量："
                      : libraryType === "2"
                      ? "车辆数量："
                      : "设备数量："
                  }}</span>
                  <count-to
                    :start-val="0"
                    :end-val="item.libCount"
                    :duration="1000"
                    class="value text-color"
                  ></count-to>
                </div>
                <div class="item">
                  <span class="label title-color">更新时间：</span>
                  <span class="value text-color">{{ item.modifyTime }}</span>
                </div>
                <div class="item">
                  <span class="label title-color">创建人：</span>
                  <span class="value text-color">{{ item.creatorName }}</span>
                </div>
                <div class="item">
                  <span class="label title-color">所在组织：</span>
                  <span class="value text-color">{{
                    item.creatorOrgName
                  }}</span>
                </div>
              </div>
              <div class="operate-bar" @click.stop="">
                <p class="operate-content">
                  <ui-btn-tip
                    content="编辑"
                    icon="icon-bianji"
                    @click.native="editHandle(item)"
                    v-permission="['staticLibr-update']"
                  />
                  <ui-btn-tip
                    transfer
                    content="删除"
                    icon="icon-shanchu"
                    @click.native="delHandle(item)"
                    v-permission="['staticLibr-dele']"
                  />
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <!-- 新增/编辑静态库 -->
    <AddModal ref="addModal" :libraryType="libraryType" />
    <!-- 配置 -->
    <ConfigureModal ref="configureModal" />
  </div>
</template>
<script>
import {
  personnelQuery,
  personnelDel,
  personnelDetail,
  queryVehicleLibageList,
  deleteVehicleLib,
  sensoryList,
  sensoryDel,
  enableFaceLib,
  disableFaceLib,
  enableVehicleLib,
  disableVehicleLib,
  enableSensoryLib,
  disableSensoryLib,
} from "@/api/data-warehouse";
import { personnelList, vehicleList } from "./data";
import CountTo from "vue-count-to";
import AddModal from "./components/add-modal.vue";
import ConfigureModal from "./components/configure-modal.vue";
export default {
  components: {
    CountTo,
    AddModal,
    ConfigureModal,
  },
  data() {
    return {
      libraryList: [
        { name: "人员布控库", value: "1", num: 0 },
        { name: "车辆布控库", value: "2", num: 0 },
        { name: "WIFI布控库", value: "3", num: 0 },
        { name: "RFID布控库", value: "5", num: 0 },
        { name: "电围布控库", value: "4", num: 0 },
        { name: "ETC布控库", value: "6", num: 0 },
      ],
      libraryType: "1",
      searchData: {},
      archivesList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
    };
  },
  async mounted() {
    // this.init()
    await this.libraryItemHandle(this.libraryList[0]);
    await this.vehicleSearch(false);
    await this.sensorySearch("3", false);
    await this.sensorySearch("4", false);
    await this.sensorySearch("5", false);
    await this.sensorySearch("6", false);
  },
  methods: {
    init() {
      this.libraryItemHandle(
        this.libraryList.find((item) => item.value == this.libraryType)
      );
    },
    async libraryItemHandle(item) {
      this.libraryType = item.value;
      this.archivesList = [];
      this.searchData.name = "";
      this.searchData.libType = 1;
      if (item.value === "1") {
        this.personnelSearch();
      } else if (item.value === "2") {
        // this.archivesList = vehicleList
        this.vehicleSearch();
      } else {
        this.sensorySearch(item.value);
      }
    },
    async personnelSearch() {
      await personnelQuery({
        ...this.pageInfo,
        ...this.searchData,
        libSource: "2",
      }).then((res) => {
        this.archivesList = res.data.entities;
        this.total = res.data.total;
        this.libraryList[0].num = res.data.total;
      });
    },
    async vehicleSearch(first = true) {
      await queryVehicleLibageList({
        ...this.pageInfo,
        ...this.searchData,
        libSource: "2",
      }).then((res) => {
        if (first) {
          this.archivesList = res.data.entities;
          this.total = res.data.total;
        }
        this.libraryList[1].num = res.data.total;
      });
    },
    async sensorySearch(libCategory = "3", first = true) {
      await sensoryList({
        ...this.pageInfo,
        ...this.searchData,
        libCategory,
        libSource: "2",
      }).then((res) => {
        if (first) {
          this.archivesList = res.data.entities;
          this.total = res.data.total;
        }
        let item = this.libraryList.find((v) => v.value == libCategory);
        item.num = res.data.total;
      });
    },
    // 静态库详情
    archivesItemHandle(item) {
      if (this.libraryType === "1") {
        // 人员
        this.$router.push({
          name: "personnel-thematic-database",
          query: {
            archiveStatus: item.archiveStatus,
            featureLibId: item.featureLibId,
            id: item.id,
            libCount: item.libCount,
            libName: item.libName,
            libSource: item.libSource,
            libType: item.libType,
            modifyTime: item.modifyTime,
            remark: item.remark,
            bizType: item.bizType,
          },
        });
      } else if (this.libraryType === "2") {
        // 车辆
        this.$router.push({
          name: "vehicle-thematic-database",
          query: item,
        });
      } else {
        this.$router.push({
          name: "sensory-thematic-database",
          query: item,
        });
      }
    },
    // 查询
    searchHandle() {
      this.pageInfo.pageNumber = 1;
      this.init();
    },
    // 重置
    resetHandle() {
      this.$refs.searchData.resetFields();
      this.init();
    },
    // 新增静态库
    addStaticLibrary() {
      this.$refs.addModal.show(1);
    },
    // 编辑
    editHandle(item) {
      this.$refs.addModal.show(2, item);
    },
    // 删除
    delHandle(item) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该静态库？`,
        onOk: () => {
          if (this.libraryType === "1") {
            personnelDel({ id: item.id, featureLibId: item.featureLibId }).then(
              (res) => {
                this.$Message.success("删除成功");
                this.init();
              }
            );
          } else if (this.libraryType === "2") {
            deleteVehicleLib({
              id: item.id,
              featureLibId: item.featureLibId,
            }).then((res) => {
              this.$Message.success("删除成功");
              this.init();
            });
          } else {
            sensoryDel(item.id).then((res) => {
              this.$Message.success("删除成功");
              this.init();
            });
          }
        },
      });
    },
    // 配置
    configureHandle() {
      this.$refs.configureModal.show();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    // 启用、停用
    handleSwitch(status, row) {
      if (status) {
        if (this.libraryType === "1") {
          enableFaceLib({ ids: [row.id] }).then((res) => {
            this.$set(row, "status", 1);
          });
        } else if (this.libraryType === "2") {
          enableVehicleLib({ featureLibIdList: [row.featureLibId] }).then(
            (res) => {
              this.$set(row, "status", 1);
            }
          );
        } else {
          enableSensoryLib({
            ids: [row.id],
            libCategory: this.libraryType,
          }).then((res) => {
            this.$set(row, "status", 1);
          });
        }
      } else {
        if (this.libraryType === "1") {
          disableFaceLib({ ids: [row.id] }).then((res) => {
            this.$set(row, "status", 0);
          });
        } else if (this.libraryType === "2") {
          disableVehicleLib({ featureLibIdList: [row.featureLibId] }).then(
            (res) => {
              this.$set(row, "status", 0);
            }
          );
        } else {
          disableSensoryLib({
            ids: [row.id],
            libCategory: this.libraryType,
          }).then((res) => {
            this.$set(row, "status", 0);
          });
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.static-library {
  display: flex;
  flex: 1;
  .library-list {
    width: 280px;
    flex: none;
    padding: 10px 0;
    overflow: auto;
    .library-item {
      padding: 5px 20px 5px 16px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      .library-item-title {
        display: flex;
        align-items: center;
        flex: 1;
        overflow: hidden;
        padding-right: 10px;
        box-sizing: border-box;
        .iconfont {
          font-size: 16px;
          margin-right: 10px;
        }
      }
      .name,
      .count {
        font-family: "MicrosoftYaHei-Bold";
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
      }
    }
    .library-item-active {
      .iconfont,
      .name,
      .count {
        color: #fff !important;
      }
    }
  }
  .library-content {
    display: flex;
    flex: 1;
    flex-direction: column;
    margin-left: 10px;
    .archives-list {
      display: flex;
      flex: 1;
      flex-wrap: wrap;
      align-content: flex-start;
      margin: 0 -5px;
      overflow: auto;
      .archives-item {
        width: 25%;
        padding: 0 4px;
        margin-bottom: 10px;
        box-sizing: border-box;
        .archives-item-body {
          display: flex;
          box-sizing: border-box;
          flex: 1;
          border: 1px solid transparent;
          // border-radius: 4px;
          .archives-item-body-con {
            display: flex;
            padding: 10px;
            box-sizing: border-box;
            border-radius: 4px;
            flex: 1;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            .archives-img {
              width: 115px;
              height: 118px;
              object-fit: contain;
              border-radius: 4px;
            }
            .archives-item-info {
              flex: 1;
              padding-left: 10px;
              box-sizing: border-box;
              overflow: hidden;
              .name {
                font-family: "MicrosoftYaHei-Bold";
                font-size: 16px;
                line-height: 22px;
                font-weight: bold;
                margin-bottom: 10px;
                width: calc(~"100% - 40px");
              }
              .switch {
                position: absolute;
                right: 10px;
                top: 10px;
              }
              .item {
                display: flex;
                margin-bottom: 5px;
                flex: 1;
                .label {
                  font-size: 12px;
                  font-family: "MicrosoftYaHei-Bold";
                  font-weight: bold;
                  line-height: 20px;
                  white-space: nowrap;
                }
                .value {
                  flex: 1;
                  font-size: 14px;
                  line-height: 20px;
                }
              }
            }
            .operate-bar {
              height: 30px;
              background: linear-gradient(
                90deg,
                rgba(87, 187, 252, 0.8) 0%,
                #2c86f8 100%
              );
              border-radius: 0px 0px 4px 0px;
              position: absolute;
              right: -100%;
              transition: all 0.3s;
              bottom: 0;
              transform: skewX(-20deg);
              .operate-content {
                padding: 0 5px;
                transform: skewX(20deg);
                height: 100%;
                display: flex;
                align-items: center;
                color: #fff;
                /deep/ .ivu-tooltip-rel {
                  padding: 6px;
                }
              }
            }
          }
        }
      }
      .archives-item-body:hover {
        border: 2px solid #2c86f8;
        border-radius: 4px;
        .archives-item-body-con {
          border: none;
          border-radius: 0;
          .operate-bar {
            right: -6px;
            bottom: -1px;
          }
        }
      }
    }
  }
}
</style>
