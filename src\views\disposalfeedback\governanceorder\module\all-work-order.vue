<template>
  <div class="height-full">
    <div class="governanceorder auto-fill">
      <search-and-button
        v-bind="$props"
        ref="searchButtonRef"
        :is-not-batch-sign="isNotBatchSign"
        :batchLoadingObj="batchLoadingObj"
        :show-search-keys="showSearchKeys"
        :is-show-more-search="isShowMoreSearch"
        @search="search"
        @exportExcel="exportExcel"
        @addData="addData"
        @clearAll="clearAll"
        @handleBatchOptions="handleBatchOptions"
        @assingSearchData="assingSearchData"
      >
        <template #checkAll>
          <Checkbox class="vt-middle" v-model="isCheckAll" @on-change="changeCheckAll" :disabled="!tableData.length"
            >全选</Checkbox
          >
          <span class="base-text-color f-14" v-show="isCheckAll || selectData.length">
            <span class="ml-md">已选中</span>
            <span>
              <span class="check-num">
                {{ (isCheckAll ? pageData.totalCount : selectData.length) | formatNum }}
              </span>
              <span>条</span>
            </span>
          </span>
        </template>
      </search-and-button>
      <div class="table-module auto-fill">
        <ui-table
          ref="tableref"
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="tableLoading"
          :reserveSelection="true"
          :is-all="isCheckAll"
          row-key="id"
          :default-store-data="defaultStoreData"
          @storeSelectList="storeSelectList"
        >
          <template #queryConditionStatus="{ row }">
            <div class="status-tag" :style="{ 'background-color': bgColor[row.finishStatus] }">
              <span>{{ row.finishStatusText }}</span>
            </div>
            <div v-if="row.reportId && row.examReportReviewVo && Object.keys(row.examReportReviewVo).length">
              <Tooltip transfer placement="right-start">
                <span
                  class="pointer font-sign"
                  style="color: #de990f"
                  @click.stop="handleBatchOptions('toViewReportDetail', false, row)"
                  >(报备中)</span
                >
                <div slot="content">
                  <p>报备原因：{{ row.examReportReviewVo.reportReasonText || '' }}</p>
                  <p>开始时间：{{ row.examReportReviewVo.beginTime || '' }}</p>
                  <p>结束时间：{{ row.examReportReviewVo.endTime || '' }}</p>
                </div>
              </Tooltip>
            </div>
            <!-- 待签收\检测不通过有特殊样式 -->
            <p
              v-else
              class="font-sign"
              :style="{ color: taskStatusColorReplenish[row.finishStatus] || 'var(--color-failed)' }"
            >
              {{ row.replenishStatusText || '' }}
            </p>
          </template>
          <template #workLevel="{ row }">
            <work-level-tag :value="row.workLevel">{{ row.workLevelText }}</work-level-tag>
          </template>
          <template slot="action" slot-scope="{ row }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-chakangongdan"
              content="查看工单"
              @click.native="showEditViewOrder(row, 'view')"
            ></ui-btn-tip>
            <template v-if="row.operationList?.length">
              <ui-btn-tip
                v-for="(item, index) in row.operationList.slice(0, 2)"
                :key="index"
                class="operatbtn"
                :icon="item.iconName"
                :content="item.name"
                @click.native="item.func(row)"
              ></ui-btn-tip>
            </template>
            <Poptip
              v-if="row.operationList?.length && row.operationList.length > 2"
              placement="bottom-end"
              transfer
              popper-class="custom-transfer-poptip"
              :offset="18"
            >
              <Button type="text" class="operatbtn ellipsisbtn">...</Button>
              <ul class="operation" slot="content">
                <li v-for="(item, index) in row.operationList.slice(2)" :key="index" @click="item.func(row)">
                  {{ item.name }}
                </li>
              </ul>
            </Poptip>
          </template>
        </ui-table>
        <ui-page
          class="page menu-content-background"
          transfer
          :page-data="pageData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </div>
      <ui-modal
        class="order-option"
        v-model="orderOptionShow"
        :title="modalTitle"
        :styles="{ width: isBatch ? '7rem' : '3.5rem' }"
        @onCancel="cancel"
      >
        <div class="order-option-content auto-fill" :style="{ height: isBatch ? '3.2rem' : '' }">
          <component
            :is="componentName"
            :ref="componentName"
            :isBatch="isBatch"
            @updateDisabled="updateDisabled"
          ></component>
          <div v-if="componentName === 'Assign'">
            <div class="mb-md mt-md">
              <span class="base-text-color">短信通知：</span>
              <RadioGroup v-model="messageNotice" @on-change="changeNoticeType">
                <Radio label="1" class="mr-lg">是</Radio>
                <Radio label="0">否</Radio>
              </RadioGroup>
            </div>
            <div class="mb-md" v-if="messageNotice === '1'">
              <span class="base-text-color">联系电话：</span>
              <Input placeholder="请输入联系电话" class="width-md" v-model="tellphoneNumber" />
            </div>
          </div>
          <div class="select-table auto-fill" v-if="isBatch">
            <div class="select-title">待{{ titles[componentName] }}工单：</div>
            <p
              v-if="componentName === 'Assign' && batchStatisticsObj.total !== batchStatisticsObj.useCount"
              class="tips"
            >
              共{{ batchStatisticsObj.total - batchStatisticsObj.useCount }}条工单无需（或无权）指派
            </p>
            <p v-if="componentName === 'Deal' && batchStatisticsObj.total !== batchStatisticsObj.useCount" class="tips">
              共{{ batchStatisticsObj.total - batchStatisticsObj.useCount }}条工单无需（或无权）处理
            </p>
            <p
              v-if="componentName === 'Close' && batchStatisticsObj.total !== batchStatisticsObj.useCount"
              class="tips"
            >
              共{{ batchStatisticsObj.total - batchStatisticsObj.useCount }}条工单无需（或无权）关闭
            </p>
            <ui-table
              class="auto-fill"
              :tableColumns="modelColumns"
              :tableData="sucessData"
              :loading="batchTableLoading"
            >
              <!-- <template slot="action" slot-scope="{ row }">
                <ui-btn-tip
                  class="operatbtn"
                  icon="icon-yichu1"
                  content="移除"
                  @click.native="remove(row)"
                ></ui-btn-tip>
              </template> -->
            </ui-table>
            <ui-page
              class="page menu-content-background"
              :page-data="batchTablePageData"
              transfer
              @changePage="changebatchTablePage"
              @changePageSize="changebatchTablePageSize"
            >
            </ui-page>
          </div>
        </div>
        <template #footer>
          <div v-if="!isBatch">
            <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
            <Button type="primary" :loading="orderLoading" :disabled="isDisabled" @click="submit" class="plr-30"
              >确 认</Button
            >
          </div>
          <Button
            v-else
            :loading="orderLoading"
            type="primary"
            :disabled="!sucessData.length || isDisabled"
            @click="submit"
            class="plr-30"
            >确 认</Button
          >
        </template>
      </ui-modal>
      <add-order
        v-if="addOrderShow"
        :edit-view-action="editViewAction"
        v-model="addOrderShow"
        @updateOrders="updateOrders('addOrderShow')"
      ></add-order>
      <edit-view-order
        v-model="editViewOrderShow"
        :edit-view-action="editViewAction"
        @updateOrders="updateOrders('editViewOrderShow')"
      ></edit-view-order>
    </div>
    <select-people
      ref="selectpeople"
      v-model="peopleShow"
      :title="peopleTitle"
      :filed-name="filedName"
      @putPeople="putPeople"
    ></select-people>
    <device-detail
      v-model="deviceDetailShow"
      modal-title="修改入库数据"
      modal-action="edit"
      view-url="/ivdg-asset-app/device/viewDeviceId"
      unqualified="null"
      :has-check-result="true"
      :device-code="editViewAction.row.deviceId"
      save-url="/ivdg-asset-app/assert/device/fillIn/update"
      save-method="post"
      :check-fun="MixinCheckFun"
      :custom-parameters="customParameters"
      :has-multiView="false"
      @update="getTableData"
    >
    </device-detail>
    <upload-error
      v-model="uploadErrorVisible"
      :error-data="errorData"
      :error-columns="errorColumns"
      :footer-hide="footerHide"
      @cancel="MixinCancelCompulsory"
    >
      <template #footer>
        <Button @click="MixinCancelCompulsory" class="mr-lg"> 取 消</Button>
        <Button type="primary" :loading="false" @click="MixinCompulsoryStorage"> 强制保存 </Button>
      </template>
    </upload-error>
  </div>
</template>
<script>
import governancetask from '@/config/api/governancetask';
import fillReportMixin from '@/views/disposalfeedback/governanceorder/util/fillReportMixin';
import batchMethodsMixin from '@/views/disposalfeedback/governanceorder/util/batchMethodsMixin';
import {
  TASKSTATUS,
  abnormalList,
  tableColumns,
  modelColumns,
  TASKSTATUSCOLOR,
  TASKSTATUSCOLOR_REPLENISH,
  BATCH_TYPE_ENUM,
  ALLORDER,
  ASSIGN_UNIT,
} from '../util/enum.js';
import { mapGetters } from 'vuex';
export default {
  name: 'governanceorder',
  mixins: [fillReportMixin, batchMethodsMixin],
  props: {
    commonSearchData: {},
    queryConditionStatus: {},
    statisticsDetail: {}, //单位统计类型统计详情
    showSearchKeys: {
      //所展示的搜索条件
      type: Array,
      default: () => {
        return [
          'indexId',
          'workOrderNum',
          'workOrderName',
          'taskName',
          'signDictKey',
          'orgCode', //组织机构
          'belongProject',
          'ipAddr',
          'cjdw',
          'whdw',
          'workLevel',
        ];
      },
    },
    isShowMoreSearch: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      abnormalList: abnormalList,
      loading: false,
      searchData: {
        workOrderNum: '',
        workOrderName: '',
        createName: '',
        currentTaskReceiverName: '',
        currentTaskReceiverUnit: '',
        signDictKey: '',
        belongProject: '',
        ipAddr: '',
        cjdw: '',
        whdw: '',
        orgCode: null,
      },
      errorReasonItem: '',
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      visibleMoreTab: false,
      addOrderShow: false,
      editViewOrderShow: false,
      peopleTitle: '',
      tableColumns: tableColumns(),
      peopleShow: false,
      tableData: [],
      selectData: [],
      orderOptionShow: false,
      modalTitle: '',
      componentName: '',
      editViewAction: {
        type: 'edit',
        row: {},
      },
      // queryConditionStatus: '100',
      checkIds: [],
      isBatch: false,
      deviceDetailShow: false,
      tableLoading: false,
      isDisabled: true,
      modelColumns: modelColumns,
      sucessData: [],
      failData: [],
      closeData: [],
      notHandleData: [],
      notOwnersData: [],
      titles: {
        Assign: '指派',
        Deal: '处理',
        Close: '关闭',
      },
      bgColor: TASKSTATUSCOLOR,
      taskStatusColorReplenish: TASKSTATUSCOLOR_REPLENISH, //状态描述文字颜色
      messageNotice: '0',
      tellphoneNumber: '',
      filedName: '',
      defaultStoreData: [],
      scrollnum: 0,
      orderLoading: false,
      customParameters: {},
      compulsoryResolve: null,
      compulsoryReject: null,
      // 上传检测
      uploadErrorVisible: false,
      footerHide: false,
      errorData: [],
      errorColumns: [],
      conditionStatus: '',
      isCheckAll: false, //table全选
      bathTypeEnum: BATCH_TYPE_ENUM, //批量处理类型
      batchStatisticsObj: {
        //批量统计数据
        total: 0,
        useCount: 0,
      },
      batchLoadingObj: {
        //控制批量按钮的loading
        Deal: false,
        Assign: false,
        Close: false,
        Sign: false,
        Delete: false,
        Report: false,
      },
      batchTableLoading: false,
      batchTablePageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      handleRowObj: {},
      TYPE_ALLORDER: ALLORDER,
      QUERYTYPE_ASSIGN_UNIT: ASSIGN_UNIT, //指派给单位
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    putPeople(item, name) {
      this.peopleShow = false;
      this.searchData[name] = item;
    },
    // 获取表格数据 getWorkOrderList
    async getTableData(isClearChcekAll = true) {
      try {
        this.tableLoading = true;
        let { type, queryType, isOwnOrgCode } = this.commonSearchData;
        const params = {
          type: type,
          queryType: queryType,
          queryOrgCode: this.commonSearchData.orgCode === '-1' ? '' : this.commonSearchData.orgCode,
          beginTime: this.commonSearchData.beginTime,
          endTime: this.commonSearchData.endTime,
          queryConditionStatus: this.queryConditionStatus,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          isOwnOrgCode: type === this.TYPE_ALLORDER && queryType === this.QUERYTYPE_ASSIGN_UNIT ? isOwnOrgCode : '',
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(governancetask.getWorkOrderList, params);
        this.tableData = data.entities;
        this.handleData();
        this.pageData.totalCount = data.total;
        this.$emit('on-change-table-data', data);
      } catch (error) {
        console.log(error);
      } finally {
        //是否清空选中内容
        if (isClearChcekAll) {
          this.clearCheckAll();
        }
        this.tableLoading = false;
      }
    },
    async exportExcel() {
      try {
        let { type, queryType, isOwnOrgCode } = this.commonSearchData;
        let res = await this.$http.post(
          governancetask.pageListExport,
          {
            type: this.commonSearchData.type,
            queryType: this.commonSearchData.queryType,
            queryOrgCode: this.commonSearchData.orgCode === '-1' ? '' : this.commonSearchData.orgCode,
            beginTime: this.commonSearchData.beginTime,
            endTime: this.commonSearchData.endTime,
            queryConditionStatus: this.queryConditionStatus,
            isOwnOrgCode: type === this.TYPE_ALLORDER && queryType === this.QUERYTYPE_ASSIGN_UNIT ? isOwnOrgCode : '',
            ...this.searchData,
          },
          {
            responseType: 'blob',
          },
        );
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    assingSearchData(searchData) {
      Object.assign(this.searchData, searchData);
    },
    async search(searchData) {
      Object.assign(this.searchData, searchData);
      this.pageData.pageNum = 1;
      await this.getTableData();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableData(false);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableData(false);
    },
    addData() {
      this.editViewAction = {
        type: 'add',
        row: {},
      };
      this.addOrderShow = true;
    },
    showEditViewOrder(row, type) {
      this.editViewAction.type = type;
      this.editViewAction.row = row;
      this.editViewOrderShow = true;
    },
    storeSelectList(data) {
      this.selectData = this.$util.common.deepCopy(data);
    },
    handleData() {
      let selectDataObject = {};
      this.selectData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      this.handleOptionList();
    },
    handleOptionList() {
      let optionList = {
        // reportId字段有值则为报备中,报备中状态大部分按钮都不支持操作
        edit: {
          name: '编辑',
          func: (row) => this.showEditViewOrder(row, 'edit'),
          iconName: 'icon-bianjigongdan',
          isShow: (row) => {
            //编辑： 非关闭状态 && isOwner && 非报备中
            return ![TASKSTATUS['已关闭']].includes(row.finishStatus) && row.isOwner && !row.reportId;
          },
        },
        assign: {
          name: '指派',
          func: (row) => this.handleBatchOptions('Assign', false, row),
          iconName: 'icon-zhipai',
          isShow: (row, permissionObj) => {
            //指派 judge_0:按钮权限，~1:非(已完成、已关闭、进行中)状态&&isOwner，~2: 待签收&&被指派人
            let judge_0 = permissionObj[`${this.$route.name}-assign`];
            let judge_1 =
              ![TASKSTATUS['已完成'], TASKSTATUS['已关闭'], TASKSTATUS['进行中']].includes(row.finishStatus) &&
              row.isOwner;
            let judge_2 = [TASKSTATUS['待签收']].includes(row.finishStatus) && row.isReceiver;
            return judge_0 && (judge_1 || judge_2) && !row.reportId;
          },
        },
        handle: {
          name: '处理',
          func: (row) => this.handleBatchOptions('Deal', false, row),
          iconName: 'icon-chuli',
          isShow: (row) => {
            //处理：judge_0:非(已完成、已关闭、待签收、未指派)状态，judge_1:isOwner||被指派人
            let judge_0 = ![
              TASKSTATUS['已完成'],
              TASKSTATUS['已关闭'],
              TASKSTATUS['待签收'],
              TASKSTATUS['未指派'],
            ].includes(row.finishStatus);
            let judge_1 = row.isOwner || row.isReceiver;
            return judge_0 && judge_1 && !row.reportId;
          },
        },
        close: {
          name: '关闭',
          func: (row) => this.handleBatchOptions('Close', false, row),
          iconName: 'icon-guanbigongdan',
          isShow: (row) => {
            //关闭：isOwner && ((已完成、检测不通过)状态 || 报备中)
            return (
              row.isOwner &&
              ([TASKSTATUS['已完成'], TASKSTATUS['检测不通过']].includes(row.finishStatus) || row.reportId)
            );
          },
        },
        delete: {
          name: '删除',
          func: (row) => this.handleBatchOptions('singleDelete', false, row),
          iconName: 'icon-shanchu3',
          isShow: (row) => {
            //删除：isOwner && (非(已关闭)状态 || 报备中)。已关闭不支持删除，报备中允许删除
            return row.isOwner && (![TASKSTATUS['已关闭']].includes(row.finishStatus) || row.reportId);
          },
        },
        signed: {
          name: '签收',
          func: (row) => this.handleBatchOptions('singleSignTips', false, row),
          iconName: 'icon-piliangqianshou',
          isShow: (row) => {
            // 签收：待签收 && 接收人 && 非报备
            return [TASKSTATUS['待签收']].includes(row.finishStatus) && row.isReceiver && !row.reportId;
          },
        },
        fillReport: {
          name: '去填报',
          func: (row) => this.toFillReport(row),
          iconName: 'icon-tianbao',
          isShow: (row) => {
            //填报：judge_0:非(已完成、已关闭、待签收、未指派)状态，judge_1:isOwner||被指派人，judge_2:（能处理并关联填报准确率[1001]的工单展示zzh）
            let judge_0 = ![
              TASKSTATUS['已完成'],
              TASKSTATUS['已关闭'],
              TASKSTATUS['待签收'],
              TASKSTATUS['未指派'],
            ].includes(row.finishStatus);
            let judge_1 = row.isOwner || row.isReceiver;
            let judge_2 = row.indexId === '1001';
            return judge_0 && judge_1 && judge_2 && !row.reportId;
          },
        },
        signCancel: {
          name: '签收撤回',
          func: (row) => this.handleBatchOptions('signCancel', false, row),
          iconName: 'icon-qianshouchehui',
          isShow: (row) => {
            // 签收撤回: 状态为2并且指派人为当前用户或者创建者
            let judge_0 = row.signName == this.getUserInfo?.name || row.isOwner;
            let judge_1 = [TASKSTATUS['进行中']].includes(row.finishStatus);
            return judge_0 && judge_1 && !row.reportId;
          },
        },
        assignCancel: {
          name: '指派撤回',
          func: (row) => this.handleBatchOptions('assignCancel', false, row),
          iconName: 'icon-zhipaichehui',
          isShow: (row) => {
            // 指派撤回: 状态为5待签收
            return [TASKSTATUS['待签收']].includes(row.finishStatus) && !row.reportId;
          },
        },
      };
      //获取权限列表
      let permissionObj = {
        [`${this.$route.name}-assign`]: false, //行内已有判定逻辑。要求暂时先给指派权限。
      };
      Object.keys(permissionObj).forEach((item) => {
        if (this.permisstionsList.includes(item)) {
          permissionObj[item] = true;
        }
      });
      //判断按钮权限
      this.tableData.forEach((row) => {
        this.$set(row, 'operationList', []);
        for (let key in optionList) {
          optionList[key].isShow(row, permissionObj) && row.operationList.push(optionList[key]);
        }
      });
    },
    // 去填报
    toFillReport(row) {
      this.editViewAction.row = row;
      // 治理工单 - 填报的都是已入库的设备
      this.customParameters = {
        workOrderId: row.id,
      };
      this.deviceDetailShow = true;
    },
    submit() {
      let params;
      let url = '';
      let text = '';
      let form = {};
      this.checkIds = [];
      let checkAllNeedParams = {};
      if (this.isBatch && this.isCheckAll) {
        checkAllNeedParams = this.returnHandleBatchParams();
      } else if (this.isBatch) {
        this.checkIds = this.selectData.map((item) => item.id);
      } else {
        this.checkIds = this.sucessData.map((item) => item.id);
      }
      //若全选需要带上搜索条件
      switch (this.componentName) {
        case 'Deal': // 工单处理
          form = this.$refs[this.componentName].form;
          params = {
            ids: this.checkIds, // 工单id
            batchType: 'deal',
            finishResult: form.finishResult, // 办理结果
            finishSituation: form.finishSituation, // 办理情况说明
            feedbackResult: form.feedbackResult, // 原因反馈
            ...checkAllNeedParams,
          };
          url = 'batchHandle';
          text = '工单处理';
          break;
        case 'Close': //  工单关闭
          form = this.$refs[this.componentName].form;
          params = {
            ids: this.checkIds,
            batchType: 'close',
            finishSituation: form.finishSituation,
            ...checkAllNeedParams,
          };
          url = 'batchClose';
          text = '工单关闭';
          break;
        case 'Assign': // 工单指派
          if (this.messageNotice === '1') {
            if (!this.$util.common.checkPhone(this.tellphoneNumber)) {
              this.$Message.warning('请输入正确的手机号码');
              return;
            }
          }
          form = this.$refs[this.componentName].searchForm;
          params = {
            ids: this.checkIds, // 工单id
            batchType: 'assign',
            receiverId: form.assignMode === 0 ? form.people.username : '', // 指派人ID
            receiverName: form.assignMode === 0 ? form.people.name : '', // 指派人名称
            phoneNumber: this.tellphoneNumber,
            sendStatus: '1',
            assignMode: form.assignMode,
            assignList: form.assignList,
            ...checkAllNeedParams,
          };
          url = 'batchAssign';
          text = '工单指派';
          break;
      }
      this.handleOrder(params, url, text);
    },
    async handleOrder(params, url, text) {
      try {
        this.orderLoading = true;
        let { data } = await this.$http.post(governancetask[url], params);
        let { success = 0, failure = 0 } = data.data || {};
        this.orderOptionShow = false;
        this.$refs[this.componentName].reset();
        if (this.componentName === 'Assign') {
          this.$Message.success({
            content: `共创建${Number.parseInt(success) + Number.parseInt(failure)}条工单，
          成功指派${success}条工单！${
              failure
                ? `有${failure}条工单无法匹配 ${
                    params.assignMode === 1 ? '维护单位联系人' : '对应单位的工单接收人'
                  }，无法指派！请在工单列表页面手动指派！`
                : ''
            } `,
            duration: 5,
          });
        } else if (this.componentName === 'Deal') {
          this.$Message.success('提交成功！请稍后查看系统检测结果');
        } else if (this.componentName === 'Close' && data.data.deviceNum && data.data.deviceNum * 1 > 0) {
          this.$Message.error(`${data.data.msg}`);
        } else {
          this.$Message.success(
            `${success ? `有${success}条${text}成功` : ''}${failure ? ` 有${failure}条${text}失败！` : ''}`,
          );
        }
        this.pageData.pageNum = 1;
        this.pageData.totalCount = 0;
        this.getTableData();
        // this.queryWorkorderStatistics()
        this.selectDataReset(); // 清空数据
      } catch (error) {
        console.log(error);
      } finally {
        this.orderLoading = false;
      }
    },
    clearAll(searchData) {
      Object.assign(this.searchData, searchData);
      this.pageData.pageNum = 1;
      this.selectDataReset();
      this.getTableData();
    },
    editDevice() {
      this.deviceDetailShow = true;
    },
    closeAddOrder() {
      this.addOrderShow = false;
      this.getTableData();
    },
    updateOrders(type) {
      this[type] = false;
      this.getTableData();
      // this.queryWorkorderStatistics()
    },
    updateDisabled(datas) {
      this.isDisabled = !datas;
      if (datas) {
        this.tellphoneNumber = datas.phoneNumber || '';
      }
    },
    changeNoticeType(val) {
      if (val === '1' && !!this.$refs[this.componentName].people) {
        this.tellphoneNumber = this.$refs[this.componentName].people.phoneNumber || '';
      }
    },
    cancel() {
      this.orderOptionShow = false;
      this.$refs[this.componentName].reset();
      this.messageNotice = '0';
      this.tellphoneNumber = '';
    },
    selectDataReset() {
      this.checkIds = [];
      this.sucessData = [];
      this.failData = [];
      this.closeData = [];
      this.notOwnersData = [];
      this.notHandleData = [];
      this.selectData = [];
      this.defaultStoreData = [];
      this.clearCheckAll();
    },
    //选择全部
    changeCheckAll() {
      this.selectData = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.isCheckAll);
        this.$set(item, '_disabled', this.isCheckAll);
        return item;
      });
    },
    //更换搜索条件时清空全选
    clearCheckAll() {
      this.isCheckAll = false;
      this.$refs.tableref?.$refs.table?.selectAll(false);
      this.changeCheckAll();
    },
    //返回批量操作的参数
    returnHandleBatchParams(ortherParams = {}) {
      //注意：batchType是批量操作的类型，若特殊处理的批量操作需要重新传一个batchType覆盖掉原本的
      let { type, queryType, orgCode, beginTime, endTime, isOwnOrgCode } = this.commonSearchData;
      let params = {
        type: type,
        queryType: queryType,
        queryOrgCode: orgCode === '-1' ? '' : orgCode,
        beginTime: beginTime,
        endTime: endTime,
        queryConditionStatus: this.queryConditionStatus,
        pageNumber: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        isOwnOrgCode: type === this.TYPE_ALLORDER && queryType === this.QUERYTYPE_ASSIGN_UNIT ? isOwnOrgCode : '',
        ...this.searchData,
        batchType: this.bathTypeEnum[this.componentName], //componentName：Assign => batchType:assign。
        ids: this.isCheckAll ? [] : this.selectData.map((item) => item.id),
        ...ortherParams,
      };
      return params;
    },

    //获取批量工单分页列表
    async getBatchPageList() {
      let params = this.returnHandleBatchParams({
        pageNumber: this.batchTablePageData.pageNum,
        pageSize: this.batchTablePageData.pageSize,
      });
      this.batchTableLoading = true;
      try {
        const { data } = await this.$http.post(governancetask.batchPageList, params);
        this.sucessData = data.data.entities;
        this.batchTablePageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.batchTableLoading = false;
      }
    },
    //批量操作表格分页
    changebatchTablePage() {
      this.getBatchPageList();
    },
    //批量操作表格分页
    changebatchTablePageSize() {
      this.getBatchPageList();
    },

    //对批量操作进行转发
    handleBatchOptions(name, isBatch, row) {
      const handleOptions = {
        //下列方法挪到混入了 ./util/batchMethodsMixin.js
        Assign: () => this.handleBatch(name, isBatch, row), //指派、处理、关闭
        Deal: () => this.handleBatch(name, isBatch, row), //指派、处理、关闭
        Close: () => this.handleBatch(name, isBatch, row), //指派、处理、关闭
        batchReport: () => this.batchToReport(), //批量报备
        toViewReportDetail: () => this.handleToViewReportDetail(row), //查看报备
        signCancel: () => this.handleSignAndAssignCancel(name, row), // 签收撤回
        assignCancel: () => this.handleSignAndAssignCancel(name, row), // 指派撤回
        singleSignTips: () => this.singleSignTips(row), //单条签收
        batchSignTips: () => this.batchSignTips(), //批量签收
        singleDelete: () => this.singleDelete(row), //单条删除
        batchDelete: () => this.batchDelete(), //批量删除
      };
      handleOptions[name] && handleOptions[name]();
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      routerList: 'permission/getRouterList',
      getUserInfo: 'user/getUserInfo',
      permisstionsList: 'permission/getPermisstionsList',
    }),
    isNotBatchSign() {
      let canSignData = [];
      this.selectData.findIndex((item) => {
        let canSignFlag = item.finishStatus === TASKSTATUS['待签收'] && item.isReceiver;
        canSignFlag ? canSignData.push(item) : null;
      });
      return !canSignData.length;
    },
  },
  components: {
    IconStatistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    Close: require('@/views/disposalfeedback/governanceorder/components/close.vue').default,
    Deal: require('@/views/disposalfeedback/governanceorder/components/deal.vue').default,
    Assign: require('@/views/disposalfeedback/governanceorder/components/assign.vue').default,
    AddOrder: require('@/views/disposalfeedback/governanceorder/components/add-order/index.vue').default,
    EditViewOrder: require('@/views/disposalfeedback/governanceorder/components/edit-view-order.vue').default,
    SelectPeople: require('@/api-components/select-people/select-people.vue').default,
    SearchAndButton: require('@/views/disposalfeedback/governanceorder/module/search-and-button.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    UploadError: require('@/views/datagovernance/onlinegovernance/information/components/upload-error.vue').default,
    WorkLevelTag: require('@/views/disposalfeedback/governanceorder/components/work-level-tag/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.governanceorder {
  overflow: hidden;
  position: relative;
  height: 100%;
  //padding: 20px 20px 0;
  background-color: var(--bg-content);
  .static-box {
    height: 100px;
    transition: height 2s;
  }
  .font-sign {
    margin-top: 2px;
    font-size: 12px;
    line-height: 12px;
  }
  .order-option {
    @{_deep} > .modal {
      > .ivu-modal-wrap {
        > .ivu-modal {
          > .ivu-modal-content {
            > .ivu-modal-body {
              display: flex;
              justify-content: center;
            }
          }
        }
      }
    }
    &-content {
      width: 100%;
      // height: 620px;
      padding: 0 20px;
      .select-table {
        width: 100%;
        // height: 550px;
        display: flex;
        flex-direction: column;
        .removebtn {
          color: #ffffff;
          cursor: pointer;
          &:hover {
            color: var(--color-primary) !important;
          }
          &:active {
            color: #4e9ef2 !important;
          }
        }
        .tips {
          // padding: 0 20px;
          color: var(--color-tips);
          text-align: right;
        }
        .select-title {
          // padding: 0 20px;
          width: 110px;
          color: #fff;
          text-align: left;
        }
      }
    }
  }
  .operatbtn:not(:last-child) {
    margin-right: 15px;
  }
}
.operation {
  li {
    padding: 6px 18px;
    color: var(--color-content);
    cursor: pointer;
    &:hover {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active);
    }
  }
}
.table-module {
  @{_deep} .ivu-table-wrapper {
    height: 100% !important;
  }
}
.status-tag {
  display: inline-block;
  padding: 3px 10px;
  font-size: 14px;
  border-radius: 4px;
  color: #ffffff;
}
.ellipsisbtn {
  font-weight: bold;
}
.check-num {
  color: var(--color-failed);
}
</style>
