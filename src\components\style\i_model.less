/deep/ .ivu-modal-wrap {
  .ivu-modal {
    .ivu-modal-content {
      background: #FFFFFF;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
      border-radius: 0;

      .ivu-modal-header {
        padding: 10px 16px;
        color: rgba(0, 0, 0, 0.9);
        background: rgba(211, 215, 222, .29);

        .header {
          font-size: 16px;
          position: relative;
          font-weight: 700;

          span {
            height: 100%;
            min-width: 143px;
            max-width: 280px;
            overflow: hidden;
            text-overflow: clip;
            background-size: auto 100%;
          }
        }
      }

      .ivu-modal-body {
        min-height: 120px;
        max-height: 680px;
        // overflow-y: auto;
        padding: 20px;
      }

      .ivu-modal-footer {
        padding-bottom: 16px;
        padding-top: 16px;
        border-top: 1px solid #D3D7DE;
        text-align: center;

        .ivu-btn {
          height: 34px;
          padding: 7px 21px;
          font-size: 14px;
        }

        .ivu-btn-text {
          border: 1px solid #2C86F8;
          color: #2C86F8;
          box-sizing: border-box;

          &:hover {
            color: #4597FF;
            border-color: #4597FF;
          }

          &:active {
            border-color: #1A74E7;
            color: #1A74E7;
          }

          &:focus {
            box-shadow: none;
          }
        }
      }
    }
  }
}

.ok {
  margin-left: 10px;
  border: none;
}

.cancel {
  border: 1px solid #2C86F8;
  color: #2C86F8;
  box-sizing: border-box;

  &:hover {
    color: #4597FF;
    border-color: #4597FF;
  }

  &:active {
    border-color: #1A74E7;
    color: #1A74E7;
  }
}



/deep/ .vertical-center-modal {
  width: 100%;
  min-width: 1124px;
  display: flex;
  align-items: center;

  .ivu-modal {
    top: 0;
  }
}