<template>
  <ui-modal
    v-model="osdVisible"
    :title="title"
    :styles="currentObj.detectionMode == 1 ? osdStyles : sdkStyles"
    @cancel="hide"
  >
    <div class="content" v-if="osdVisible">
      <div class="ocr" v-if="currentObj.detectionMode == 1">
        <div class="ocr-left mr-lg">
          <ui-image :src="currentObj.screenShot" class="image" />
        </div>
        <div class="ocr-center auto-fill">
          <ui-label align="right" class="block mb-sm font-blue" label="" :width="0">
            <i class="ml-xs icon-font icon-shujujiancegongju font-blue"></i>检测信息
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="摄像机信息:" :width="80">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.detectionDeviceName" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="时间信息:" :width="80">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.detectionDate" type="text"></Input>
          </ui-label>
          <div
            class="custom-label"
            v-if="!!currentObj.areaRecognizeResultList && currentObj.areaRecognizeResultList.length"
          >
            <div class="custom-label-left">地点信息:</div>
            <div class="custom-label-right">
              <Input
                v-for="item in currentObj.areaRecognizeResultList"
                :key="item"
                disabled
                :value="item"
                class="width-lg ml-sm mb-xs"
                type="text"
              ></Input>
            </div>
          </div>
        </div>
        <div class="ocr-right ml-lg">
          <ui-label align="right" class="block mb-sm font-blue" label="" :width="0">
            <i class="ml-md icon-font icon-shebeidanganxinxi-01 font-blue"></i>设备档案信息
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="行政区划:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.civilCode" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="设备PTZ控制状态:" :width="120">
            <Input disabled class="width-lg ml-sm" type="text">{{
              currentObj.ptzStatus == 0 ? '不可控-故障' : '可控'
            }}</Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="摄像机类型扩展:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="ptztypeObj[currentObj.ptzType]" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="安装位置室内外:" :width="120">
            <Input disabled class="width-lg ml-sm" type="text">{{ currentObj.roomType == 1 ? '室外' : '室内' }}</Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="摄像机用途:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="usetypeObj[currentObj.useType]" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="监视方位:" :width="120">
            <Input
              disabled
              class="width-lg ml-sm"
              v-model="directiontypeObj[currentObj.directionType]"
              type="text"
            ></Input>
          </ui-label>
          <ui-label align="right" class="block" label="安装地址:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.address" type="text"></Input>
          </ui-label>
        </div>
      </div>
      <div class="sdk" v-else>
        <div class="sdk-left auto-fill">
          <ui-label align="right" class="block mb-sm" label="摄像机信息:" :width="80">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.detectionDeviceName" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="时间信息:" :width="80">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.detectionDate" type="text"></Input>
          </ui-label>
          <div
            class="custom-label"
            v-if="!!currentObj.areaRecognizeResultList && currentObj.areaRecognizeResultList.length"
          >
            <div class="custom-label-left">地点信息:</div>
            <div class="custom-label-right">
              <p v-for="item in currentObj.areaRecognizeResultList" :key="item">
                <Input disabled :value="item" class="width-lg ml-sm mb-xs" type="text"></Input>
              </p>
            </div>
          </div>
        </div>
        <div class="sdk-right">
          <ui-label align="right" class="block mb-sm" label="行政区划:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.civilCode" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="设备PTZ控制状态:" :width="120">
            <Input disabled class="width-lg ml-sm" type="text">{{
              currentObj.ptzStatus == 0 ? '不可控-故障' : '可控'
            }}</Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="摄像机类型扩展:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="ptztypeObj[currentObj.ptzType]" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="安装位置室内外:" :width="120">
            <Input disabled class="width-lg ml-sm" type="text">{{ currentObj.roomType == 1 ? '室外' : '室内' }}</Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="摄像机用途:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="usetypeObj[currentObj.useType]" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="监视方位:" :width="120">
            <Input
              disabled
              class="width-lg ml-sm"
              v-model="directiontypeObj[currentObj.directionType]"
              type="text"
            ></Input>
          </ui-label>
          <ui-label align="right" class="block" label="安装地址:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.address" type="text"></Input>
          </ui-label>
        </div>
      </div>
      <slot name="search"></slot>
    </div>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </ui-modal>
</template>

<script>
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import api from '@/config/api/inspectionrecord';
export default {
  name: 'captureDetail',
  props: {
    title: {
      type: String,
      default: '查看OSD字幕检测详情',
    },
    artificialRow: {
      type: Object,
      default: () => {},
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      current: 'ocr',
      osdStyles: { width: '8rem' },
      sdkStyles: { width: '6rem' },
      osdVisible: false,
      currentObj: {
        areaRecognizeResultList: [],
      },
      imgUrl: '',
      directiontypeObj: { 1: '东', 2: '南', 3: '西', 4: '北', 5: '东南', 6: '东北' },
      ptztypeObj: { 1: '球机', 2: '半球', 3: '固定枪机', 4: '遥控枪机' },
      usetypeObj: { 1: '治安', 2: '交通', 3: '重点' },
    };
  },
  async created() {
    // this.width = 800;
  },
  computed: {},
  methods: {
    async init(item) {
      try {
        var param = {
          id: item.id,
        };
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          api.queryEvaluationVideoById,
          param,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // this.$http.post(api.queryEvaluationVideoById, param).then((res) => {
        //   this.currentObj = res.data.data
        // })
        this.currentObj = res.data.data;
      } catch (e) {
        console.log(e);
      }
    },
    show(item) {
      this.osdVisible = true;
      item ? this.init(item) : '';
    },
    hide() {
      this.osdVisible = false;
    },
    /*
     * 格式化时间数据
     */
    formatData(str) {
      var s =
        '{"date":"","dateFormat":"","enable":true,"locationX":640,"locationY":0,"showWeek":false,"timeFormat":"TIME_FORMAT_ONE"}';
      var newStr = JSON.parse(str) || JSON.parse(s);
      return newStr.date.replaceAll('T', ' ');
    },
  },
  watch: {
    osdVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.osdVisible = val;
    },
    artificialRow: {
      handler(val) {
        if (!val) return false;
        this.init(val);
      },
      deep: true,
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.content {
  color: #fff;
  padding: 20px;
  background-color: #041129;

  ul {
    li {
      word-break: break-all;
      margin-top: 10px;
      span {
        color: #afafaf;
        margin-right: 10px;
      }
    }
  }

  .ocr {
    display: flex;
    height: 400px;
    .image {
      width: 100%;
      height: 100%;
    }
    &-left {
      width: 40%;
      height: 100%;
    }
    &-center {
      width: 28%;
      height: 100%;
      border-right: 1px solid var(--border-color);
    }
    &-right {
      width: 32%;
    }
  }
  .width-lg {
    color: #fff !important;
  }
  .sdk {
    padding-left: 50px;
    width: 100%;
    display: flex;
    &-left {
      flex: 1;
    }
    &-right {
      flex: 1;
    }
  }
}
.custom-label {
  width: 100%;
  flex: 1;
  display: flex;
  overflow: hidden;
  &-left {
    width: 80px;
    text-align: right;
  }
  &-right {
    flex: 1;
    overflow-y: auto;
  }
}
</style>
