<template>
  <div>
    <ui-modal v-model="visible" :title="title" :width="90" footerHide class="ys-detail">
      <div class="over-flow mb-sm">
        <ui-label class="inline" label="行政区划">
          <api-area-tree
            :select-tree="selectTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
        <ui-label class="ml-lg inline" label="考核时间">
          <template>
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              placeholder="请选择考核时间"
              v-model="searchData.time"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'time')"
            ></DatePicker>
          </template>
        </ui-label>
        <Button type="primary" class="ml-lg" @click="search">查询</Button>
        <Button type="primary" class="fr" @click="exportHandle" :loading="btnLoading">
          <i class="icon-font icon-daochu f-12 mr-sm vt-middle"></i>
          <span class="vt-middle">导出</span>
        </Button>
      </div>
      <div class="content" v-ui-loading="{ loading: loading, tableData: tableData }">
        <div class="table-box" v-for="(item, index) in tableData" :key="index">
          <ui-table
            ref="tableContent"
            :table-columns="tableColumns"
            :table-data="item"
            :stripe="false"
            :disabledHover="true"
            :span-method="
              ({ row, column, rowIndex, columnIndex }) => handleSpan({ row, column, rowIndex, columnIndex }, index)
            "
          >
            <template #action="{ row }">
              <div>
                <ui-btn-tip
                  class="mr-md"
                  icon="icon-chakanxiangqing"
                  content="详情"
                  :disabled="
                    (row.vendorType === 'UYUN' &&
                      row.indexType !== 'VIDEO_GENERAL_PLAYING_ACCURACY' &&
                      row.indexType !== 'VIDEO_HISTORY_COMPLETE_ACCURACY') ||
                    row.examItemType === EXAM_ITEM_TYPE.MANUAL
                  "
                  @handleClick="indexScoreDetail(row)"
                >
                </ui-btn-tip>
                <ui-btn-tip
                  icon="icon-jisuangongshi"
                  content="计算公式"
                  :styles="{ color: '#CC4242', 'font-size': '14px' }"
                  @click.native="formulaDetail(row)"
                >
                </ui-btn-tip>
              </div>
            </template>
          </ui-table>
        </div>
      </div>
    </ui-modal>
    <formula v-model="formulaShow" :title="formulaTitle" :formula-data="formulaData"></formula>
  </div>
</template>
<script>
import scAsync from '@/config/api/sc-async';
import { EXAM_ITEM_TYPE } from '@/views/governanceevaluation/resultExaminationSC/modules';
export default {
  props: {
    title: {},
    value: {
      type: Boolean,
    },
    indexDetailParams: {},
  },
  data() {
    return {
      EXAM_ITEM_TYPE,
      visible: false,
      loading: false,
      btnLoading: false,
      countObj: {},
      countChildObj: {},
      searchData: {
        time: '',
        regionCode: '',
        regionName: '',
      },
      selectTree: {
        regionCode: '',
      },
      tableColumns: [
        {
          title: '考核分类',
          key: 'categoryName',
          className: 'table-cell-pl',
          minWidth: 80,
        },
        {
          title: '考核内容',
          key: 'examContentName',
          className: 'table-cell-pl',
          minWidth: 90,
        },
        {
          title: '考核项',
          key: 'itemName',
          className: 'table-cell-pl',
          minWidth: 160,
        },
        {
          title: '得分',
          className: 'table-cell-pl',
          key: 'contentScore',
          maxWidth: 80,
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
          width: 80,
        },
      ],
      tableData: [],
      formulaShow: false,
      formulaTitle: '',
      formulaData: {},
    };
  },
  created() {},
  methods: {
    selectedArea(area) {
      this.searchData.regionCode = area.regionCode;
      this.searchData.regionName = area.regionName;
    },
    init() {
      this.searchData.time = this.indexDetailParams.examDate;
      this.searchData.regionCode = this.indexDetailParams.orgRegeionCode;
      this.selectTree.regionCode = this.indexDetailParams.orgRegeionCode;
      this.search();
    },
    async search() {
      try {
        this.loading = true;
        this.tableData = [];
        const data = await this.$http.post(scAsync.queryExamContentByDay, {
          ...this.indexDetailParams,
          regionCode: this.searchData.regionCode,
          examDate: this.$util.common.formatDate(this.searchData.time, 'yyyy-MM-dd'),
          // 四川考核专属
          examModel: 'SICHUAN',
        });
        this.tableData = data.data.data.map((row, index) => {
          if (!this.countObj[index]) {
            this.countObj[index] = {};
          }
          if (!this.countChildObj[index]) {
            this.countChildObj[index] = {};
          }
          return row.map((rw) => {
            // 根据标识符统计相同类型数据
            if (rw.categoryCode) {
              if (this.countObj[index].hasOwnProperty(rw.categoryCode)) {
                this.countObj[index][rw.categoryCode]++;
              } else {
                this.countObj[index][rw.categoryCode] = 0;
                this.countObj[index][rw.categoryCode]++;
              }
            }

            if (rw.examContentCode) {
              if (this.countChildObj[index].hasOwnProperty(rw.examContentCode)) {
                this.countChildObj[index][rw.examContentCode]++;
              } else {
                this.countChildObj[index][rw.examContentCode] = 0;
                this.countChildObj[index][rw.examContentCode]++;
              }
            }
            return rw;
          });
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleSpan({ row, rowIndex, columnIndex }, index) {
      //针对第一列
      //第一行数据直接向下合并
      if (rowIndex === 0 && columnIndex === 1) {
        return [Object.values(this.countObj[index])[0], 1];
      }
      /*
        之后的数据发现标识符相同的则置空, 标识符与上一列不符则开始向下合并
      */
      if (rowIndex !== 0 && columnIndex === 0) {
        if (row.categoryCode === this.tableData[index][rowIndex - 1]['categoryCode']) {
          return [0, 0];
        } else if (row.categoryCode !== this.tableData[index][rowIndex - 1]['categoryCode']) {
          return [this.countObj[index][row.categoryCode], 1];
        }
      }
      //针对第二列
      //第一行数据直接向下合并
      if (rowIndex === 0 && columnIndex === 1) {
        return [Object.values(this.countChildObj[index])[0], 1];
      }
      if (rowIndex === 0 && columnIndex === 0) {
        return [Object.values(this.countChildObj[index])[0], 1];
      }
      /*
        之后的数据发现标识符相同的则置空, 标识符与上一列不符则开始向下合并
      */
      if (rowIndex !== 0 && columnIndex === 1) {
        if (row.examContentCode === this.tableData[index][rowIndex - 1]['examContentCode']) {
          return [0, 0];
        } else if (row.examContentCode !== this.tableData[index][rowIndex - 1]['examContentCode']) {
          return [this.countChildObj[index][row.examContentCode], 1];
        }
      }
      // if (rowIndex === 0 && columnIndex === 8) {
      //   return [this.tableData.length, 1]
      // } else if (rowIndex !== 0 && columnIndex === 8) {
      //   return [0, 0]
      // }
    },
    async exportHandle() {
      try {
        this.btnLoading = true;
        const res = await this.$http.post(scAsync.exportExamContentByDayToExcel, {
          ...this.indexDetailParams,
          regionCode: this.searchData.regionCode,
          examDate: this.$util.common.formatDate(this.searchData.time, 'yyyy-MM-dd'),
          // 四川考核专属
          examModel: 'SICHUAN',
        });
        await this.$util.common.transformBlob(res.data.data);
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    indexScoreDetail(row) {
      if (row.vendorType === 'HIK') {
        this.$emit('showHkDetail', {
          regionCode: this.searchData.regionCode,
          regionName: this.searchData.regionName,
          time: this.$util.common.formatDate(this.searchData.time, 'yyyy-MM-dd'),
          ...row,
        });
      } else {
        // 优云的视频可调阅率、历史录像完整率才可以展示详情
        if (
          row.vendorType === 'UYUN' &&
          row.indexType !== 'VIDEO_GENERAL_PLAYING_ACCURACY' &&
          row.indexType !== 'VIDEO_HISTORY_COMPLETE_ACCURACY'
        ) {
          return;
        }
        this.$emit('showYsDetail', {
          regionCode: this.searchData.regionCode,
          regionName: this.searchData.regionName,
          time: this.$util.common.formatDate(this.searchData.time, 'yyyy-MM-dd'),
          ...row,
        });
      }
    },
    formulaDetail(row) {
      this.formulaTitle = `${row.itemName}-计算公式`;
      this.formulaData.examFormula = row.examFormula;
      this.formulaShow = true;
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    Formula: require('./formula.vue').default,
  },
};
</script>
<style lang="less" scoped>
.ys-detail {
  @{_deep} .ivu-modal {
    height: 93%;
    > .ivu-modal-content {
      height: 100%;
      > .ivu-modal-body {
        height: 96.5%;
        display: flex;
        flex-direction: column;
      }
    }
  }
}
.content {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  @{_deep}.table-cell-pl {
    padding-left: 10px;
  }
  @{_deep} .ui-table {
    .ivu-table td,
    .ivu-table th {
      box-shadow: none;
      border-left: 1px solid var(--border-table);
      border-bottom: 1px solid var(--border-table);
    }
  }
  .table-box {
    width: 49%;
    margin-bottom: 10px;
    &:nth-child(2n) {
      margin-left: 20px;
    }
  }
}
</style>
