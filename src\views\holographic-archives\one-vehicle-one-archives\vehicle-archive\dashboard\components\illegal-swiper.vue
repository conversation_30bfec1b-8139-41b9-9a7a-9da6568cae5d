<template>
  <div class="my-swiper-container" id="mySwiper">
    <swiper
      v-if="!swiperResize"
      ref="mySwiper"
      :options="swiperOption"
      class="my-swiper"
    >
      <template v-for="(item, index) in dataList">
        <swiper-slide :key="index">
          <div class="swiper-item-container">
            <h3
              class="title color-primary text-overflow"
              :title="item.illegalType | commonFiltering(ellegaList)"
            >
              <!-- 使用过滤器会导致先展示key，再展示翻译内容 -->
              {{ fmtType(item.illegalType) }}
            </h3>
            <div class="content">
              <p>
                <b class="label">处罚</b
                ><span class="color-warning">{{ item.punishmentWay }}</span>
              </p>
              <p>
                <b class="label">地点</b><span>{{ item.illegalAddress }}</span>
              </p>
              <p>
                <b class="label">时间</b><span>{{ item.illegalTime }}</span>
              </p>
            </div>
          </div>
        </swiper-slide>
      </template>
    </swiper>
    <div class="swiper-button-prev bnap-prev" slot="button-prev">
      <i class="iconfont icon-caret-right"></i>
    </div>
    <div class="swiper-button-next bnap-next" slot="button-next">
      <i class="iconfont icon-caret-right"></i>
    </div>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { mapGetters } from "vuex";

export default {
  components: { swiper, swiperSlide },
  props: {
    // 卡片标题
    title: {
      type: String,
      default: "",
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
    // 违法违章数据
    dataList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      swiperResize: false,
      list: ["a1", "a2", "a3", "a4", "a5"],
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 1.46,
        centeredSlides: true,
        loop: true,
        loopAdditionalSlides: 5,
        speed: 1000,
        autoplay: {
          delay: 100000,
          stopOnLastSlide: false,
          disableOnInteraction: false,
          autoplayDisableOnInteraction: false,
        },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 180, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 280, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".bnap-next",
          prevEl: ".bnap-prev",
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      ellegaList: "dictionary/getEllegaList", //违法违章
    }),
  },
  mounted() {
    let _this = this;
    this.$erd.listenTo(document.getElementById("mySwiper"), (element) => {
      _this.updateSliderHandle();
    });
  },
  methods: {
    updateSliderHandle() {
      const w = document.documentElement.clientWidth;
      this.swiperOption.coverflowEffect.stretch = (w / 192) * 18;
      this.swiperOption.coverflowEffect.depth = (w / 192) * 28;
      this.swiperResize = true;
      this.$nextTick(() => {
        this.swiperResize = false;
      });
    },

    /**
     * @description: 违章翻译
     * @param {string} type 违章类型
     */
    fmtType(type) {
      return this.ellegaList.find((v) => v.dataKey == type)?.dataValue;
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .swiper-container-3d {
  .swiper-slide-shadow-left {
    background-image: linear-gradient(
      to left,
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0)
    );
  }
  .swiper-slide-shadow-right {
    background-image: linear-gradient(
      to right,
      rgba(255, 255, 255, 1),
      rgba(255, 255, 255, 0)
    );
  }
}
.my-swiper-container {
  padding: 0 10px;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  .my-swiper {
    padding: 10px 0;
    .swiper-item-container {
      width: 100%;
      height: 160px;
      background: #f9f9f9;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      padding: 20px;
      box-sizing: border-box;
      .title {
        font-size: 20px;
        text-align: center;
        margin-bottom: 10px;
      }
      .content {
        color: rgba(0, 0, 0, 0.8);
        p {
          display: flex;
          align-items: center;
          white-space: nowrap;
          .label {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
            margin-right: 10px;
          }
          span {
            font-size: 16px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    margin-top: -15px;
    .iconfont {
      color: #fff;
      font-size: 18px;
    }
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    &:active {
      background: rgba(0, 0, 0, 1);
    }
  }
  .swiper-button-prev {
    transform: rotate(180deg);
    left: 12px;
  }
  .swiper-button-next {
    right: 12px;
  }
}
</style>
