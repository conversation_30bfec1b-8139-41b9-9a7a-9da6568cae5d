<template>
  <div class="active-theme">
    <div v-if="!componentName" class="active-theme-wrap">
      <div class="active-theme-wrap-item" v-for="(item, index) in themeData" :key="index">
        <div
          class="active-theme-wrap-item-title"
          :style="{
            background: 'url(' + require('@/assets/img/thememanagement/theme' + (index + 1) + '.png') + ') no-repeat',
            'background-size': '100%',
            'text-shadow': '2px 2px 1px ' + colors[index],
          }"
        >
          {{ item.topicName }}
        </div>
        <Poptip trigger="hover" placement="right-start" word-wrap width="200" :content="item.topicDesc">
          <div class="active-theme-wrap-item-introduce">
            {{ item.topicDesc }}
          </div>
        </Poptip>
        <div class="active-theme-wrap-item-chart">
          <draw-echarts
            :echart-option="optionsArr[index]"
            :echart-style="ringStyle"
            ref="zdryChart"
            class="charts"
            @echartClick="task(item.id, index, true)"
          ></draw-echarts>
        </div>
        <ul class="active-theme-wrap-item-legend">
          <!-- <li v-for="(item, index) in lists" :key="index"> -->
          <li>
            <!-- <span :style="{ 'background-color': `${listItem.color}` }"></span>
            <span> {{ listItem.name }} </span>
            <span :style="{ color: `${listItem.color}` }">
              {{ listItem.value }}
            </span> -->
            <div>
              <span class="block bluebg"></span> <span>检测合格：</span>
              <span class="blue">{{ lists[index].checkQualifiedCount }}</span>
            </div>
            <div>
              <span class="block bluebg"></span> <span>合格占比：</span>
              <span class="blue"
                >{{ lists[index].checkQualifiedRate ? (lists[index].checkQualifiedRate * 100).toFixed(2) : 0 }}%</span
              >
            </div>
            <div>
              <span class="block greenbg"></span> <span>治理优化：</span>
              <span class="green">{{ lists[index].governanceOptimizationCount }}</span>
            </div>
            <div>
              <span class="block greenbg"></span> <span>优化占比：</span>
              <span class="green"
                >{{
                  lists[index].governanceOptimizationRate
                    ? (lists[index].governanceOptimizationRate * 100).toFixed(2)
                    : 0
                }}%</span
              >
            </div>
            <div>
              <span class="block redbg"></span> <span>异常数据：</span>
              <span class="red">{{ lists[index].existingExceptionCount }}</span>
            </div>
            <div>
              <span class="block redbg"></span> <span>异常占比：</span>
              <span class="red"
                >{{
                  lists[index].existingExceptionRate ? (lists[index].existingExceptionRate * 100).toFixed(2) : 0
                }}%</span
              >
            </div>
          </li>
        </ul>
        <div class="active-theme-wrap-item-foot">
          <create-tabs
            :componentName="chartDatas[index].componentName"
            :tabs-query="{ tab: item.id }"
            :tabs-text="item.topicName"
            @selectModule="selectModule"
          >
            <Poptip trigger="hover" :content="item.topicType === '1' ? '主题管理' : '主题配置'" placement="top">
              <i :class="['icon-font', item.topicType === '1' ? 'icon-zhutiguanli' : 'icon-peizhi-1']"></i
            ></Poptip>
          </create-tabs>
          <Poptip trigger="hover" content="任务追踪" placement="top">
            <i v-if="index" class="icon-font icon-renwuzhuizong" @click="task(item.id, index)"></i
          ></Poptip>
        </div>
      </div>
    </div>
    <component v-if="componentName" :is="componentName"></component>
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
import governancetheme from '@/config/api/governancetheme';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'active-theme',
  mixins: [dealWatch],
  props: {
    // themeData: {
    //   type: Array,
    //   default: () => [],
    // },
  },
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '100%',
        height: '472px',
      },
      componentName: null,
      colors: ['#0E6177', '#134585', '#09705F', '#575827', '#32137A'],
      chartDatas: [
        {
          componentName: 'SubTheme', // 需要跳转的组件名
          type: 'view',
          opts: {
            titleName: '接入数据',
            subtitleName: '0',
            color: [
              [
                { offset: 0, color: '#57D3F3' },
                { offset: 1, color: '#06AEC9' },
              ],
              [
                { offset: 0, color: '#18C80A' },
                { offset: 1, color: '#089216' },
              ],
              [
                { offset: 0, color: '#94270A' },
                { offset: 1, color: '#F86842' },
              ],
            ],
            showData: [
              { name: '检测合格', value: 0 },
              { name: '治理优化', value: 0 },
              { name: '异常数据', value: 0 },
            ],
          },
        },
        {
          componentName: 'Faceprocess',
          opts: {
            titleName: '接入数据',
            subtitleName: '0',
            color: [
              [
                { offset: 0, color: '#57D3F3' },
                { offset: 1, color: '#06AEC9' },
              ],
              [
                { offset: 0, color: '#18C80A' },
                { offset: 1, color: '#089216' },
              ],
              [
                { offset: 0, color: '#94270A' },
                { offset: 1, color: '#F86842' },
              ],
            ],
            showData: [
              { name: '检测合格', value: 0 },
              { name: '治理优化', value: 0 },
              { name: '异常数据', value: 0 },
            ],
          },
        },
        {
          componentName: 'carView',
          opts: {
            titleName: '接入数据',
            subtitleName: '0',
            color: [
              [
                { offset: 0, color: '#57D3F3' },
                { offset: 1, color: '#06AEC9' },
              ],
              [
                { offset: 0, color: '#18C80A' },
                { offset: 1, color: '#089216' },
              ],
              [
                { offset: 0, color: '#94270A' },
                { offset: 1, color: '#F86842' },
              ],
            ],
            showData: [
              { name: '检测合格', value: 0 },
              { name: '治理优化', value: 0 },
              { name: '异常数据', value: 0 },
            ],
          },
        },
        {
          componentName: 'videoView',
          opts: {
            titleName: '接入数据',
            subtitleName: '0',
            color: [
              [
                { offset: 0, color: '#57D3F3' },
                { offset: 1, color: '#06AEC9' },
              ],
              [
                { offset: 0, color: '#18C80A' },
                { offset: 1, color: '#089216' },
              ],
              [
                { offset: 0, color: '#94270A' },
                { offset: 1, color: '#F86842' },
              ],
            ],
            showData: [
              { name: '检测合格', value: 0 },
              { name: '治理优化', value: 0 },
              { name: '异常数据', value: 0 },
            ],
          },
        },
        {
          componentName: 'personnelprocess',
          opts: {
            titleName: '接入数据',
            subtitleName: '0',
            color: [
              [
                { offset: 0, color: '#57D3F3' },
                { offset: 1, color: '#06AEC9' },
              ],
              [
                { offset: 0, color: '#18C80A' },
                { offset: 1, color: '#089216' },
              ],
              [
                { offset: 0, color: '#94270A' },
                { offset: 1, color: '#F86842' },
              ],
            ],
            showData: [
              { name: '检测合格', value: 0 },
              { name: '治理优化', value: 0 },
              { name: '异常数据', value: 0 },
            ],
          },
        },
      ],
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      opts: {},
      optionsArr: [],
      lists: [
        { name: '检测合格：', value: '0', color: '#12B8D5' },
        { name: '合格占比：', value: '0', color: '#12B8D5' },
        { name: '治理优化：', value: '0', color: '#0F9015' },
        { name: '优化占比：', value: '0', color: '#0F9015' },
        { name: '异常数据：', value: '0', color: '#BC3C19' },
        { name: '异常占比：', value: '0', color: '#BC3C19' },
      ],
      themeData: [],
    };
  },
  created() {},
  mounted() {},
  async activated() {
    this.startWatch('$route', () => {
      this.getParams();
    });
    this.getParams();
    await this.getThemeList();
    this.init();
  },
  // deactivated() {
  //   // this.componentName = null;
  // },
  methods: {
    async init() {
      await this.$http.get(api.queryThremList).then((res) => {
        if (res.data.code == 200) {
          var list = res.data.data;
          // 处理echart图表
          list.forEach((item, index) => {
            if (index <= 4) {
              this.chartDatas[index].opts.subtitleName = item.accessDataCount ? item.accessDataCount : 0 + '';
              this.chartDatas[index].opts.showData[0].value = item.checkQualifiedCount | 0;
              this.chartDatas[index].opts.showData[1].value = item.governanceOptimizationCount | 0;
              this.chartDatas[index].opts.showData[2].value = item.existingExceptionCount | 0;
            }
          });
          this.lists = list;
          this.initChart();
        } else {
          console.log(res.data.msg);
        }
      });
    },

    async getThemeList() {
      try {
        let params = {
          topicName: '',
        };
        let res = await this.$http.post(governancetheme.themeList, params);
        this.themeData = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    process(item) {
      if (item.type == 'view') {
        this.$router.push({ path: '/subtheme' });
      } else {
        this.$router.push({ path: '/viewprocess' });
      }
    },
    initChart() {
      this.optionsArr = [];
      for (let i = 0; i < this.chartDatas.length; i++) {
        this.optionsArr.push(this.$util.doEcharts.themeRingChart(this.chartDatas[i].opts));
      }
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    task(id, taskTab, isChart = false) {
      let querys = {
        id: id,
        taskTab: taskTab,
      };
      querys.isExport = isChart && taskTab !== 0 && taskTab !== 4 ? true : false; // 是否打开数据输出弹框
      this.$router.push({
        name: 'tasktracking',
        query: querys,
      });
    },
  },
  watch: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    SubTheme: require('@/views/datagovernance/governancetheme/sub-theme/index.vue').default,
    carView: require('../car/car.vue').default,
    videoView: require('../video/video.vue').default,
    Faceprocess: require('@/views/datagovernance/governancetheme/faceprocess/index.vue').default,
    personnelprocess: require('@/views/datagovernance/governancetheme/personnelprocess/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.align-flex {
  display: flex;
  align-items: center;
}
.justify-flex {
  display: flex;
  justify-content: center;
}
.active-theme {
  width: 100%;
  height: 100%;
  // padding: 20px 10px;
  background: var(--bg-content);
  &-wrap {
    width: 100%;
    height: 776px;
    padding: 0 10px;
    display: flex;
    //justify-content: space-around;
    &-item {
      // flex: 1;
      display: flex;
      flex-direction: column;
      width: 19%;
      height: 100%;
      margin: 20px 10px;
      background: #0f2f59;
      &-title {
        .flex;
        width: 100%;
        height: 130px;
        font-size: 18px;
        font-weight: bold;
        color: #ffffff;
        letter-spacing: 3px;
      }
      &-introduce {
        // height: 60px;
        margin: 26px 20px 42px;
        display: -webkit-box;
        align-items: center;
        text-align: justify;
        font-size: 13px;
        color: #ffffff;
        opacity: 0.8;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      &-chart {
        border-top: 1px dashed #576e8b;
        height: 268px;
        margin: 0 20px;
      }
      &-legend {
        margin: 0 20px;
        height: 197px;
      }
      li {
        div {
          margin-bottom: 8px;
        }
        .block {
          display: inline-block;
          width: 8px;
          height: 8px;
          // background: #12B8D5;
        }
      }

      .bluebg {
        background: #12b8d5;
      }
      .greenbg {
        background: #0f9015;
      }
      .redbg {
        background: #bc3c19;
      }
      .blue {
        font-size: 14px;
        color: #12b8d5;
      }
      .green {
        font-size: 14px;
        color: #0f9015;
      }
      .red {
        font-size: 14px;
        color: #bc3c19;
      }

      li span:nth-child(2) {
        margin: 0 5px 0 8px;
        font-size: 14px;
        color: #ffffff;
      }
      &-foot {
        .align-flex;
        justify-content: flex-end;
        width: 100%;
        height: 52px;
        padding: 20px 10px;
        border-top: 1px solid #0d4a81;
        i {
          margin: 0 7px;
          font-size: 16px;
          color: #56789c;
          &:hover {
            color: var(--color-primary);
          }
          &:active {
            color: #4e9ef2;
          }
        }
      }
    }
  }
}
#iptdatachart0 {
  width: 100%;
  height: 472px;
}
@{_deep} .ivu {
  &-poptip {
    position: relative;
    &-arrow {
      border-top-color: transparent !important;
    }
    &-arrow:after {
      border-top-color: transparent !important;
    }
    &-inner {
      border: 1px solid #0d4a81 !important;
    }
    &-body-content-inner {
      color: #ffffff !important;
      background-color: #0d3560 !important;
    }
    &-popper {
      min-width: 80px !important;
      &[x-placement^='right'] {
        top: 60px !important;
        left: 94% !important;
        .ivu-poptip-arrow {
          border-right-color: #0d4a81 !important;
          &:after {
            border-right-color: #0d3560 !important;
            left: 1px !important; /*no*/
          }
        }
      }
      &[x-placement^='left'] {
        top: 60px !important;
        .ivu-poptip-arrow {
          border-left-color: #0d4a81 !important;
          &:after {
            border-left-color: #0d3560 !important;
          }
        }
      }
      &[x-placement^='top'] {
        .ivu-poptip-arrow {
          border-top-color: #0d4a81 !important;
          &:after {
            border-top-color: #0d3560 !important;
            bottom: 1px; /*no*/
          }
        }
      }
    }
  }
  &-tooltip {
    &-inner {
      color: #ffffff;
      background: #0f2f59;
      border: 1px solid #0d4a81 !important;
    }
    &-popper[x-placement^='top'] .ivu-tooltip-arrow {
      border-top-color: #0d4a81;
      &:after {
        border-left-color: #0d3560 !important;
      }
    }
  }
}
</style>
