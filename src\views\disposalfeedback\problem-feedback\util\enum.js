//问题反馈表格的列
export const FEEDBACK_TABLE_COLUMNS = [
  { type: 'selection', align: 'center', width: 50 },
  { title: '序号', type: 'index', align: 'left', width: 50 },
  { title: '问题编号', key: 'code', align: 'left', width: 263 },
  { title: '数据类型', key: 'dataTypeText', align: 'left', width: 169, tooltip: true },
  { title: '异常原因', key: 'causeCodesText', slot: 'causeCodesText', align: 'left', width: 200, tooltip: true },
  { title: ' ', key: '', width: 10, align: 'center' },
  { title: '问题详情 ', key: 'issueDetail', align: 'left', width: 305, tooltip: true },
  { title: '有效状态 ', key: 'effective', slot: 'effective', align: 'left', width: 122 },
  { title: '提交时间 ', key: 'submitTime', slot: 'submitTime', align: 'left', width: 205 },
  { title: '提交人姓名 ', key: 'name', align: 'left', width: 160, tooltip: true },
  { title: '提交人电话 ', key: 'phone', align: 'left', width: 160, tooltip: true },
  { title: '提交人警号 ', key: 'police', align: 'left', width: 160, tooltip: true },
  { title: ' ', key: '', width: 10, align: 'center', fixed: 'right' },
  { title: '处理状态 ', key: 'status', slot: 'handleStatus', align: 'left', width: 150, fixed: 'right' },
  {
    title: '操作 ',
    slot: 'action',
    align: 'left',
    width: 110,
    fixed: 'right',
  },
];

//处理结果
export const HANDLE_STATUS_ENUM = {
  UNHANDLED: 1, // 未处理
  PROCESSING: 2, // 处理中
  PROCESSED: 3, // 已处理
};
export const HANDLE_RESULT_STATUS = {
  [HANDLE_STATUS_ENUM.UNHANDLED]: {
    color: 'var(--color-failed)',
    text: '未处理',
    value: 1,
  },
  [HANDLE_STATUS_ENUM.PROCESSING]: {
    color: 'var(--color-warning)',
    text: '处理中',
    supplementText: '（工单流程）',
    value: 2,
  },
  [HANDLE_STATUS_ENUM.PROCESSED]: {
    color: 'var(--color-success)',
    text: '已处理',
    value: 3,
  },
};

// 有效状态
export const EFFECTIVE_ENUM = {
  UNCONFIRMED: 0, // 待确认
  EFFECTIVE: 1, // 有效
  UNEFFECTIVE: 2, // 无效
};
export const EFFECTIVE_STATUS = {
  [EFFECTIVE_ENUM.UNCONFIRMED]: {
    colorOfTable: 'var(--color-content)',
    color: 'var(--color-content)',
    text: '待确认',
    value: 0,
  },
  [EFFECTIVE_ENUM.EFFECTIVE]: {
    colorOfTable: 'var(--color-success)',
    color: 'var(--color-success)',
    text: '有效',
    value: 1,
  },
  [EFFECTIVE_ENUM.UNEFFECTIVE]: {
    colorOfTable: 'var(--color-failed)',
    color: 'var(--color-failed)',
    text: '无效',
    value: 2,
  },
};
