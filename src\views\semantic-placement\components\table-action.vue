<template>
  <div class="opreate">
    <i-switch
      title="开启/停止"
      v-if="subTaskType == 'real'"
      :disabled="row.status == 4"
      :value="row.status == 1 ? true : false"
      :loading="row?.loading"
      @on-change="taskStatusRealHandler(row)"
    />
    <div class="tools">
      <Poptip
        trigger="hover"
        placement="left"
        transfer
        popper-class="expand-row-poptip-box"
      >
        <i class="iconfont icon-gengduo"></i>
        <div class="mark-poptip" slot="content">
          <!-- <p
            @click="taskStatusHandler(row)"
            v-if="subTaskType == 'file'"
            :class="{ 'disabled-p': row.status == 3 }"
          >
            <i class="iconfont icon-pause2"></i
            >{{ row.status == 1 ? "停止" : "启动" }}
          </p> -->
          <!-- 想用include 但是不能确定status是number还是String  -->
          <p
            @click="handleEdit(row)"
            :class="{
              'disabled-p':
                row.status == 1 || row.status == 4 || row.status == 3,
            }"
          >
            <i class="iconfont icon-bianji"></i>编辑
          </p>
          <p @click="handleSearch(row)">
            <i class="iconfont icon-gaojisousuo"></i>资源检索
          </p>
          <p @click="mapLoaction(row)">
            <i class="iconfont icon-xunjianguiji"></i>地图定位
          </p>
          <p @click="fileDownload(row)" v-if="subTaskType == 'file'">
            <i class="iconfont icon-download"></i>文件下载
          </p>
          <p @click="handleDel(row)">
            <i class="iconfont icon-shanchu1"></i>删除
          </p>
        </div>
      </Poptip>
    </div>
  </div>
</template>

<script>
export default {
  name: "TableAction",
  props: {
    row: {
      type: Object,
      default: () => {},
    },
    subTaskType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  methods: {
    taskStatusRealHandler(row) {
      this.$emit("taskStatusRealHandler", row);
    },
    taskStatusHandler(row) {
      if (row.status == 3) return;
      this.$emit("taskStatusHandler", row);
    },
    handleEdit(row) {
      // 运行中,暂停和已完成不能编辑
      if (row.status == 1 || row.status == 4 || row.status == 3) {
        return;
      }
      this.$emit("handleEdit", row);
    },
    handleSearch(row) {
      this.$emit("handleSearch", row);
    },
    mapLoaction(row) {
      this.$emit("mapLoaction", row);
    },
    handleDel(row) {
      this.$emit("handleDel", row);
    },
    fileDownload(row) {
      this.$emit("fileDownload", row);
    },
  },
};
</script>

<style lang="less" scoped>
.opreate {
  display: flex;
  .tools {
    color: #2c86f8;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    margin-left: 5px;
    .icon-gengduo {
      transform: rotate(90deg);
      transition: 0.1s;
      display: inline-block;
    }
    p:hover {
      color: #2c86f8;
    }
    &:hover {
      background: #2c86f8;
      color: #fff;

      .icon-gengduo {
        transform: rotate(0deg);
        transition: 0.1s;
      }
      border-radius: 10px;
    }
    /deep/ .ivu-poptip-body {
      padding: 0;
    }
  }
}
.mark-poptip {
  width: 128px;
  // height: 140px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
  padding: 10px 0px;
  border-radius: 4px;
  p {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.8);
    padding: 0 20px;
    text-align: left;
    cursor: pointer;
    i {
      color: #2c86f8;
      margin-right: 10px;
    }
    &:hover {
      background: rgba(44, 134, 248, 0.1028);
    }
  }
}
.disabled-p {
  color: #e9e9e9 !important;
  i {
    color: #e9e9e9 !important;
  }
  cursor: not-allowed !important;
}
/deep/ .ivu-poptip-popper {
  width: auto !important;
  min-width: unset;
}
</style>
<style lang="less">
.expand-row-poptip-box.ivu-poptip-popper {
  width: auto !important;
  min-width: unset;

  .ivu-poptip-body {
    padding: 0;
  }
}
</style>
