<template>
  <ui-modal v-model="visible" title="轨迹详情" :footerHide="true" width="95.1rem">
    <div class="capture-details">
      <capture-details-left :personinfo="personinfo" :tagList="tagList"></capture-details-left>
      <div class="capture-details-right auto-fill">
        <div class="capture-details-right-top">
          <label
            >抓拍总量：<span :style="{ color: 'var(--color-bluish-green-text)' }">{{
              countDatas ? countDatas.count : 0
            }}</span></label
          >
          <label
            >准确性存疑数量：<span style="color: #f34009">{{ countDatas ? countDatas.impeachAmount : 0 }}</span>
          </label>
          <label
            >上传超时：<span style="color: #f34009">{{ countDatas ? countDatas.delayAmount : 0 }}</span>
          </label>
        </div>
        <div class="capture-details-right-search">
          <ui-label class="inline" label="抓拍时间" :width="70">
            <DatePicker
              class="width-md"
              type="datetime"
              v-model="searchData.startTime"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
              placeholder="请选择开始时间"
            ></DatePicker>
            <span class="horizontalbar">—</span>
            <DatePicker
              class="width-md"
              type="datetime"
              v-model="searchData.endTime"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
              placeholder="请选择结束时间"
            ></DatePicker>
          </ui-label>
          <ui-label class="inline ml-lg vt-middle" label="抓拍设备" :width="70">
            <select-camera
              @pushCamera="pushCamera"
              :device-ids="searchData.deviceIds"
              :camera-type="[2]"
            ></select-camera>
          </ui-label>
          <ui-label class="inline ml-lg" label="抓拍类型" :width="70">
            <Select class="width-md" placeholder="请选择" clearable v-model="searchData.componentCode">
              <Option v-for="(typeItem, tyIndex) in captureTypes" :key="tyIndex" :value="typeItem.componentCode">{{
                typeItem.msg
              }}</Option>
            </Select>
          </ui-label>
          <div class="inline ml-lg">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="clear">重置</Button>
          </div>
        </div>
        <div class="capture-details-right-content auto-fill">
          <div v-if="tableData.length > 0" class="group-unaggregated">
            <loading v-if="loading"></loading>
            <div class="group-item" v-for="(item, index) in tableData" :key="index">
              <div class="group-left">
                <div class="group-img" @click="lookScence(index)">
                  <ui-image :src="item.facePath" />
                  <span class="percent">{{ item.similarity ? item.similarity + '%' : 0 }}</span>
                  <p class="shadow-box" title="查看检测结果">
                    <i
                      class="icon-font icon-yichang search-icon mr-xs base-text-color"
                      @click.stop="checkReason(item)"
                    ></i>
                  </p>
                  <i v-if="item.errIcon" :class="['icon-font', 'seal-icon', item.errIcon]"></i>
                </div>
                <div class="group-message">
                  <p class="mb-sm">
                    <i class="icon-font icon-shijian"></i>
                    <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{ item.logTime || '未知' }}</span>
                  </p>
                  <p>
                    <i class="icon-font icon-dizhi"></i>
                    <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                      item.deviceName || '未知'
                    }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="no-data" v-if="tableData.length <= 0">
            <i class="no-data-img icon-font icon-zanwushuju1"></i>
          </div>
        </div>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
    <export-disqualify
      v-model="disqualifyShow"
      :disqualify-item="disqualifyItem"
      :activeBtn="activeBtn"
      :istasktracking="false"
      title="异常原因"
      ref="exportDisqualify"
    >
      <!-- <template #unqualifiedheader>
        <div class="header">
          <RadioGroup
            v-if="errLength.length > 1"
            v-model="activeBtn"
            type="button"
            @on-change="handleChange"
            style="display: flex"
          >
            <Radio :class="activeBtn === '8009' ? 'active' : ''" label="8009"
              >上传超时</Radio
            >
            <Radio :class="activeBtn === '10001' ? 'active' : ''" label="10001"
              >轨迹照片准确性存疑</Radio
            >
          </RadioGroup>
          <p v-else>
            {{ activeBtn === "10001" ? "轨迹照片准确性存疑" : "上传超时" }}
          </p>
        </div>
      </template> -->
    </export-disqualify>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {},
  data() {
    return {
      visible: false,
      tableData: [],
      searchData: {
        deviceIds: [],
        componentCode: '',
        startTime: null,
        endTime: null,
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      idCard: '',
      personinfo: {},
      otherList: {},
      captureTypes: [
        { msg: '合格', componentCode: 1 },
        { msg: '图片上传及时性检测', componentCode: 8009 },
        { msg: '人员轨迹准确性检测优化', componentCode: 10001 },
      ],
      disqualifyShow: false,
      disqualifyItem: {},
      activeBtn: '',
      isLibrary: true,
      countDatas: {},
      tagList: [],
      errLength: 0,
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init(id, list, com_id) {
      (this.searchData = {
        deviceIds: [],
        componentCode: '',
        startTime: null,
        endTime: null,
        pageNumber: 1,
        pageSize: 20,
      }),
        (this.visible = true);
      this.idCard = id;
      this.personinfo = list;
      this.tagList = list.personTypes;
      this.copySearchDataMx(this.searchData);
      await this.getPersonIfo();
      await this.getPersonCounts(com_id);
      // await this.getCaptureType();
      await this.initList();
    },
    // 预览图片
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
    // 查看异常原因
    checkReason(item) {
      this.otherList = item;
      this.$refs.exportDisqualify.showModal(this.otherList, this.personinfo);
      // this.disqualifyShow = true;
      // if (item.errorMsgList.length < 2) {
      //   this.activeBtn = item.errorMsgList[0]["componentCode"];
      // } else {
      //    this.activeBtn = '8009'
      // }
      // this.disqualifyItem = item;
      // this.errLength = this.disqualifyItem.errorMsgList;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.initList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    // 选择设备
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    // 获取重点人员信息
    async getPersonIfo() {
      // try {
      //   let res = await this.$http.post(equipmentassets.importPersonDetails, { idCard: this.idCard });
      //   console.log(res)
      //   this.personinfo = res.data.data;
      //   // const personTypes = this.personinfo.personTypeText.split(",");
      //   // this.tagList = persontype.filter((item) => {
      //   //   return personTypes.includes(item.tagName);
      //   // });
      // } catch (err) {
      //   console.log(err);
      // }
    },
    // 获取重点人员统计数据
    async getPersonCounts() {
      try {
        // const url = !this.isLibrary
        // ? "queryPersonLibTaskGatherStatistics"
        // : "queryPersonLibGatherStatistics";
        let res = await this.$http.post(equipmentassets.importPersonDetailsCount, {
          idCard: this.idCard,
        });
        this.countDatas = res.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 获取抓拍类型
    // async getCaptureType() {
    //   try {
    //     let res = await this.$http.get(
    //       equipmentassets.queryPersonLibTaskGatherPullData
    //     );
    //     this.captureTypes = res.data.data;
    //   } catch (err) {
    //     console.log(err);
    //   }
    // },
    search() {
      this.searchData.pageNumber = 1;
      this.initList();
    },
    //详情列表
    async initList() {
      this.loading = true;
      try {
        // const url = !this.isLibrary
        //   ? "queryPersonLibTaskGatherDetail"
        //   : "queryPersonLibGatherDetail";
        console.log(this.searchData);
        this.searchData.idCard = this.idCard;
        let res = await this.$http.post(equipmentassets.importPersonDetails, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
        // if (!this.isLibrary) {
        this.tableData = res.data.data.entities.map((item) => {
          item.similarity = (item.similarity * 100).toFixed(2);
          return item;
        });
        this.imgList = this.tableData.map((row) => row.facePath);
        //   return;
        // }
        // this.tableData = res.data.data.entities.map((item) => {
        //   const errs = item.errorMsgList.map((errItem) => errItem.errorMsg);
        //   if (errs.length) {
        //     if (errs.length > 1) {
        //       item.errIcon = "icon-cunyichaoshi";
        //     } else {
        //       item.errIcon =
        //         errs[0] === "上传超时" ? "icon-chaoshi" : "icon-cunyi";
        //     }
        //   } else {
        //     item.errIcon = "";
        //   }
        //   item.similarity = (item.similarity * 100).toFixed(2);
        //   return item;
        // });
        // this.imgList = this.tableData.map((row) => row.scenePath);
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    handleChange(val) {
      this.activeBtn = val;
    },
  },
  watch: {},
  components: {
    // TagsMore: require("@/components/tags-more").default,
    uiImage: require('@/components/ui-image').default,
    SelectCamera: require('@/components/select-camera.vue').default,
    CaptureDetailsLeft: require('./capture-details-left.vue').default,
    ExportDisqualify: require('./abnormal.vue').default,
  },
};
</script>
<style lang="less" scoped>
.capture-details {
  display: flex;
  width: 100%;
  height: 800px;
  &-right {
    flex: 1;
    height: 100%;
    padding: 20px 0 0;
    &-top {
      padding: 50px 20px 18px;
      font-size: 14px;
      color: #ffffff;
      border-bottom: 1px solid var(--border-color);
      label {
        margin-right: 58px;
      }
    }
    &-search {
      padding: 20px;
    }
    &-content {
      position: relative;
      padding: 0 20px;
      .group-unaggregated {
        overflow-y: auto;
        padding-top: 10px;
        padding-right: 10px;
        display: flex;
        flex-wrap: wrap;
        .group-item {
          width: calc(calc(100% - 70px) / 7);
          margin: 0 5px 10px;
          padding: 10px 0;
          overflow: hidden;
          position: relative;
          background: #0f2f59;
        }
        .group-left {
          .group-img {
            position: relative;
            width: 167px;
            height: 167px;
            cursor: pointer;
            margin: 0 auto 10px;
            img {
              width: 100%;
              height: 100%;
            }
            .shadow-box {
              height: 28px;
              width: 100%;
              background: rgba(0, 0, 0, 0.3);
              position: absolute;
              bottom: 0;
              display: none;
              padding-left: 10px;
              z-index: 10;
              > i:hover {
                color: var(--color-primary);
              }
            }
            &:hover {
              .shadow-box {
                display: block;
              }
            }
            .percent {
              position: absolute;
              top: 1px;
              left: 1px;
              display: inline-block;
              padding: 0 2px;
              min-width: 32px;
              text-align: center;
              background: #ea800f;
              color: #ffffff;
              z-index: 99;
            }
            .seal-icon {
              position: absolute;
              bottom: -14px;
              right: 1px;
              font-size: 60px;
              color: #bc3c19;
              z-index: 99;
            }
          }
          .group-message {
            margin: 0 auto;
            padding: 0 15px;
            color: #afbcd4;
            .group-text {
              width: 124px;
            }
            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
.horizontalbar {
  margin: 0 9px;
  color: #1b82d2;
}
@{_deep} .ivu-modal {
  &-body,
  &-header {
    padding: 0;
  }
}
// @{_deep} .ivu {
//   &-tag {
//     &:hover {
//       background: var(--color-primary) !important;
//     }
//   }
// }
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header {
  padding: 20px 0 0;
  margin-bottom: 20px;
  p {
    font-size: 16px;
    color: #ffffff;
  }
}
.onlys {
  width: 80%;
}
.cover {
  z-index: 101;
}
.active {
  color: #ffffff !important;
  background: var(--color-primary) !important;
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
  .flex;
  min-width: 96px;
  width: auto !important;
  height: 36px;
  background: transparent;
  color: #56789c;
  border: 1px solid #1b82d2;
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
  background: var(--color-primary) !important;
}
</style>
