<template>
  <div class="in-stock">
    <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.deviceId}`">
      <Input v-model="searchData.deviceId" class="width-md" placeholder="请输入设备编码"></Input>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.deviceName}`">
      <Input v-model="searchData.deviceName" class="width-md" placeholder="请输入设备名称"></Input>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.ipAddr}`">
      <Input v-model="searchData.ipAddr" class="width-sm" placeholder="请输入IP地址"></Input>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.macAddr}`">
      <Input v-model="searchData.macAddr" class="width-sm" placeholder="请输入MAC地址"></Input>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="是否检测">
      <Select class="width-md" v-model="searchData.isCheck" placeholder="请选择是否检测" clearable>
        <Option value="">请选择</Option>
        <Option value="1">是</Option>
        <Option value="0">否</Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="基础信息检测状态">
      <Select
        class="width-sm"
        :disabled="searchData.isCheck !== '1'"
        v-model="searchData.baseCheckStatus"
        placeholder="请选择基础信息检测状态"
        clearable
      >
        <Option value="">请选择</Option>
        <Option value="0">合格</Option>
        <Option value="1">不合格</Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="异常原因">
      <Select
        class="width-sm"
        :disabled="searchData.baseCheckStatus !== '1'"
        v-model="searchData.baseErrorMessageList"
        placeholder="请选择异常原因"
        clearable
        multiple
        :max-tag-count="1"
      >
        <Option v-for="(val, key, index) in baseErrorMessageList" :value="key" :key="index">{{ val }} </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="审核状态">
      <Select class="width-sm" v-model="searchData.examineStatus" placeholder="请选择审核状态" clearable>
        <Option v-for="(item, index) in examineStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="入库标识">
      <Input v-model="searchData.putMark" class="width-sm" placeholder="请输入入库标识"></Input>
    </ui-label>

    <UiSearch class="mr-lg" v-model="searchMore">
      <template #content>
        <div class="search-content">
          <ui-label class="inline mr-lg mb-sm" label="填  报  人">
            <Input v-model="searchData.modifier" class="width-sm" placeholder="请输入填报人姓名"></Input>
          </ui-label>
          <ui-label class="inline mb-sm mr-lg" label="填报时间">
            <DatePicker
              class="width-md"
              v-model="searchData.startModifyTime"
              type="datetime"
              placeholder="请选择开始时间"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startModifyTime')"
              :options="startTimeOption"
              confirm
            />
            <span class="ml-sm mr-sm">--</span>
            <DatePicker
              class="width-md"
              v-model="searchData.endModifyTime"
              type="datetime"
              placeholder="请选择结束时间"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endModifyTime')"
              :options="endTimeOption"
              confirm
            />
          </ui-label>
          <ui-label class="inline mb-sm mr-lg" label="数据来源域">
            <Select class="width-md" v-model="searchData.sourceDomain" placeholder="请选择数据来源域" clearable>
              <Option v-for="(item, index) in sourceDomainList" :key="index" :value="item.dataKey">
                {{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <!-- 摄像机功能类型 -->
          <ui-label class="inline mb-sm mr-lg" :label="global.filedEnum.sbgnlx">
            <Select
              class="width-md"
              v-model="searchData.sbgnlx"
              :placeholder="`请选择${global.filedEnum.sbgnlx}`"
              clearable
            >
              <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <!-- 监控点位类型 -->
          <ui-label class="inline mb-sm mr-lg" :label="global.filedEnum.sbdwlx">
            <Select
              class="width-md"
              v-model="searchData.sbdwlx"
              :placeholder="`请选择${global.filedEnum.sbdwlx}`"
              clearable
            >
              <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <ui-label class="inline mb-sm mr-lg" label="设备重点类型">
            <Select class="width-md" v-model="searchData.isImportant" clearable placeholder="请选择设备重点类型">
              <Option label="普通设备" :value="0"> </Option>
              <Option label="重点设备" :value="1"> </Option>
            </Select>
          </ui-label>
          <ui-label class="inline mb-sm mr-lg" label="在线状态">
            <Select class="width-md" v-model="searchData.isOnline" clearable placeholder="请选择设备在线状态">
              <Option v-for="(item, index) in propertySearch_isonline" :key="index" :value="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <!-- 摄像机采集区域 -->
          <ui-label class="inline mb-sm mr-lg" :label="`${global.filedEnum.sbcjqy}列表`">
            <Button type="dashed" class="area-btn" @click="clickArea"
              >请选择采集区域 {{ `已选择 ${sbcjqyList.length}个` }}
            </Button>
          </ui-label>
          <ui-label label="上报状态" class="inline mb-sm mr-lg">
            <Select class="width-md" v-model="searchData.cascadeReportStatus" placeholder="请选择上报状态" clearable>
              <Option value="0">未上报</Option>
              <Option value="1">已上报</Option>
            </Select>
          </ui-label>
          <ui-label label="设备功能类型扩展" class="inline mb-sm mr-lg">
            <Select
              class="width-md"
              v-model="searchData.sbgnlxExtList"
              placeholder="请选择设备功能类型扩展"
              clearable
              multiple
              filterable
              :max-tag-count="1"
            >
              <Option v-for="(item, index) in sbgnlxExtList" :key="index" :value="item.dataKey">
                {{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <!-- 设备状态 -->
          <ui-label class="inline mb-sm mr-lg" :label="global.filedEnum.phyStatus">
            <Select
              class="width-md"
              v-model="searchData.phyStatus"
              :placeholder="`请选择${global.filedEnum.phyStatus}`"
              clearable
            >
              <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <ui-label label="设备状态扩展" class="inline mb-sm mr-lg">
            <Select
              class="width-md"
              v-model="searchData.phyStatusExtList"
              placeholder="请选择设备状态扩展"
              clearable
              multiple
              filterable
              :max-tag-count="1"
            >
              <Option v-for="(item, index) in phyStatusExtList" :key="index" :value="item.dataKey">
                {{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <ui-select-tabs class="ui-select-tabs" :list="tagList" @selectInfo="selectInfo" ref="uiSelectTabs">
          </ui-select-tabs>
        </div>
      </template>
    </UiSearch>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    sbcjqyList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tagList: [],
      searchMore: false,
      baseErrorMessageList: [],
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endModifyTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startModifyTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      examineStatusList: [
        {
          dataKey: 1,
          dataValue: '审核通过',
        },
        {
          dataKey: 2,
          dataValue: '审核通过已修改',
        },
      ],
      searchData: {
        deviceId: '',
        deviceName: '',
        ipAddr: '',
        macAddr: '',
        phyStatus: '',
        modifier: '',
        putMark: '',
        isCheck: '',
        baseCheckStatus: '',
        sbgnlx: '',
        sbdwlx: '',
        isImportant: '',
        startModifyTime: '',
        endModifyTime: '',
        needExamineStatus: '1',
        isOnline: '',
        cascadeReportStatus: '',
        tagIds: [],
        sbgnlxExtList: [],
        phyStatusExtList: [],
        sourceDomain: '',
      },
    };
  },
  created() {
    this.queryErrorList();
    this.getTagList();
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagList = res.data.data.map((row) => {
          return {
            name: row.tagName,
            id: row.tagId,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.baseErrorMessageList = res.data.data;
    },
    clickArea() {
      this.$emit('clickArea');
    },
    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((item) => item.id);
    },
    reset() {
      this.$refs.uiSelectTabs && this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData);
    },
  },
  watch: {
    'searchData.baseCheckStatus'(newValue) {
      if (newValue === '0' || newValue == '') {
        this.searchData.baseErrorMessageList = [];
      }
    },
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      propertySearch_isonline: 'algorithm/propertySearch_isonline', // 在线状态
      phystatusList: 'algorithm/propertySearch_phystatus',
      sbgnlxExtList: 'algorithm/getSbgnlxExt', // 设备功能类型扩展
      phyStatusExtList: 'algorithm/getPhyStatusExt', // 设备状态扩展
      sourceDomainList: 'algorithm/propertySearch_sourceDomain', //数据来源域
    }),
  },
  components: {
    UiSearch: require('@/components/ui-search').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.in-stock {
  display: inline;
  .shouqi {
    color: var(--color-primary);
    cursor: pointer;
    margin-right: 20px;
    .icon-xiala {
      margin-left: 3px;
      font-size: 12px;
    }
  }

  .ui-search {
    // position: absolute;
    // @{_deep} .search-model {
    //   top: 0 !important;
    //   padding: 0;
    //   .search-content {
    //     transition: all 1s ease-in-out;
    //     background: #0b2348;
    //     padding: 20px 20px 10px 20px;
    //     border: 1px solid #2169b9;
    //   }
    //   &:before {
    //     right: 375px;
    //   }
    //   &:after {
    //     right: 374px;
    //   }
    // }
  }
}
</style>
