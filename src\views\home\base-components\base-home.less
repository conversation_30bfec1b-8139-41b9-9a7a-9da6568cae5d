.home-container {
  #bottom-lottie {
    position: absolute;
    bottom: 0;
  }

  background:
    url('~@/assets/img/base-home/left.png') no-repeat left/contain,
    url('~@/assets/img/base-home/right.png') no-repeat right/contain,
    url('~@/assets/img/base-home/bg.png') no-repeat center/cover;
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px;
  overflow: hidden;

  &.full-screen-container {
    background:
      url('~@/assets/img/base-home/left.png') no-repeat left/contain,
      url('~@/assets/img/base-home/right.png') no-repeat right/contain,
      url('~@/assets/img/base-home/bg.png') no-repeat center/cover !important;
    padding: 0;
  }

  .home-animation {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -6%;

    .image {
      position: absolute;
      aspect-ratio: 5/5;

      &:nth-child(0n + 1) {
        height: 56.5vh;
        animation: spin 10s linear infinite;
      }

      &:nth-child(0n + 2) {
        height: 52.78vh;
        animation: spin 8s linear infinite;
      }

      &:nth-child(0n + 3) {
        height: 83.33vh;
        // animation: spin 8s linear infinite;
      }

      &:nth-child(0n + 4) {
        height: 71.85vh;
        animation: spin 8s linear infinite;
      }
    }

    .left-circular {
      position: absolute;
      height: 63%;
      left: 23%;
      left: 400px + 30px;
    }

    .right-circular {
      position: absolute;
      height: 63%;
      right: 400px + 30px;
    }
  }
  .home-bottom {
    display: flex;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 800px - 40px - 40px);
    height: 33.5%;
    bottom: 3.5%;
    padding: 20px 20px 0 20px;
    background: rgba(1, 9, 34, 0.3);
    overflow: hidden;

    &.full-screen-container {
      bottom: 2.5%;
      height: 31.5%;
    }
  }
  #bottom-lottie {
    height: 100%;
    width: 100%;
  }
}
.home-base-container {
  position: absolute;
  height: calc(100% - 40px);
  width: 100%;
  top: 20px;
  &.full-screen-container {
    top: 7%;
    height: 92%;
  }
  .base-div {
    z-index: 2;
    position: absolute;
    padding: 0px 20px;
    background: rgba(1, 9, 34, 0.3);
    overflow: hidden;
  }
}
@media screen and (min-height: 1900px) and (max-height: 1945px) {
  .location:not(.full-screen-location) {
    top: 12% !important;
    @{_deep} .notice-box-div {
      width: 600px !important;
    }
  }
}
.location {
  position: absolute;
  left: 32%;
  top: 8%;
  color: #2389d0;
  display: flex;
  align-items: center;
  z-index: 2;
}

.ml-llg {
  margin-left: 30px;
}

.view-detail {
  color: var(--color-primary);
}

.full-screen-location {
  top: 25px;
  left: 70.5%;
}

@{_deep} .sign-box {
  top: 10px;
  right: 10px;
  left: unset;
  z-index: 3;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.home-style2 {
  > .home-container {
    padding: 0;
    @{_deep} .sign-box {
      right: 22.5%;
      top: 10px;
    }
    &.full-screen-container {
      .sign-box {
        top: 10px;
        right: 10px;
        left: unset;
        z-index: 3;
      }
      .top-title2 {
        position: absolute;
        width: 100%;
        height: 80px;
        z-index: 1;
      }
      .full-screen-location {
        left: 50%;
        transform: translateX(-50%);
        top: 13.8%;
        z-index: 999;
      }
      .statistics-top {
        top: 8.3%;
      }
      .full-screen {
        top: 10px;
      }
      .home-base-container {
        height: 94%;
        top: 7%;
      }
      .base-div {
        background: rgba(0, 104, 183, 0.13);
      }
    }
    background: url('~@/assets/img/home/<USER>') no-repeat center !important;
    .home-base-container {
      height: 100%;
      top: 10px;
    }
    .base-div {
      padding: 0;
      background: rgba(0, 104, 183, 0.13);
    }
  }
}

@media screen and (min-width: 1921px) {
  .home-style2 > .home-container {
    background: url('~@/assets/img/home/<USER>') center !important;
    background-size: 100% 100% !important;
  }
}

.visual-config.home-style2 > .home-container.full-screen-container {
  .top-title2:not(.full-screen-container) {
    height: 45px !important;
    padding-top: 8px;
  }
  .statistics-top:not(.full-screen-container) {
    top: 7% !important;
  }
}
