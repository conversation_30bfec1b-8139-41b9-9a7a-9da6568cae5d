<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
      img-key="scenePath"
    >
      <template #otherButton>
        <div class="other-button mb-sm inline ml-lg">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <template #outcome="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
      </template>
      <template #qualified="{ row }">
        <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
          {{ row.qualified === '1' ? '合格' : '不合格' }}
        </span>
      </template>
      <template #useUrl="{ row }">
        <ui-image class="monitor-img" :src="row.useUrl || ''" />
      </template>
      <template #result="{ row }">
        <ui-image
          class="monitor-img"
          v-for="(item, index) in getResultList(row.result)"
          :key="index"
          :src="item.url"
          @click.native="$emit('viewDetail', row, getResultList(row.result))"
        />
      </template>
    </Particular>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
// 外层公共配置
import {
  qualifiedColorConfig,
  iconStaticsImgList,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { iconStaticsList, normalFormData, tableColumns } from './util/enum/ReviewParticular.js';
export default {
  mixins: [particularMixin, dealWatch],
  props: {},
  data() {
    return {
      defaultCheckedList: [],
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        outcome: '',
        errorCodes: [],
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(tableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      currentRow: {},
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      // 1 - 合格、2 - 不合格
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      statisticShow: false,
      exportLoading: false,
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({}),
    //返回结果，逗号分割 转为数组
    getResultList(result) {
      return result.filter((item, index) => index < 5);
    },
    initAll() {
      // 获取列表
      this.selfConfigGetList();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
        // 图片模式统计赋值
        iconStaticsImgList.forEach((item) => {
          return (item.count = data[item.fileName]);
        });
      });
    },
    // 获取不合格原因下拉列表
    // mode 1:设备模式，2:图片模式
    getQualificationList(mode = 1) {
      // 异常原因
      this.MixinDisQualificationList(mode).then((data) => {
        if (mode === 1) {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
          findErrorCodes.options = data.map((item) => {
            // 嘉鹏说: 设备模式查询需转换数字模式
            return { value: Number(item.key), label: item.value };
          });
        } else {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'causeErrors');
          findErrorCodes.options = data.map((item) => {
            return { value: item.key, label: item.value };
          });
        }
      });
    },
    // 获取列表[mixin的方法]
    selfConfigGetList() {
      this.getTableData();
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    startSearch(params) {
      this.formData = {};
      this.pageData.pageNum = 1;
      this.formData = {
        deviceId: params.deviceId,
        deviceName: params.deviceName,
        outcome: params.outcome,
        errorCodes: params.errorCodes,
      };
      this.selfConfigGetList();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities.map((item) => {
          return {
            ...item,
            result: JSON.parse(item.result || '[]'),
          };
        });
        this.totalCount = data.total;
      });
    },
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
    },
    // 导出
    onExport() {
      const params = {
        displayType: this.$route.query.statisticType,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      this.MixinGetExport(params);
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
        iconStaticsImgList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
.monitor-img {
  display: inline-block;
  position: relative;
  width: 56px;
  height: 56px;
  margin: 5px 5px 5px 0;
  cursor: pointer;
  z-index: 0 !important;
}
</style>
