<!--
    * @FileDescription: 昼伏夜出
 -->
<template>
    <div class="search_condition">
        <div class="search_form">
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">开始时间</p>
                </div>
                <div class="search_content daterange">
                    <DatePicker v-model="queryParams.startDate" type="date" format="yyyy-MM-dd" placeholder="开始时间" transfer></DatePicker>
                </div>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">结束时间</p>
                </div>
                <div class="search_content daterange">
                    <DatePicker v-model="queryParams.endDate" type="date" format="yyyy-MM-dd" placeholder="结束时间" transfer></DatePicker>
                </div>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">夜出时段</p>
                </div>
                <div class="search_content flex">
                    <Select v-model="queryParams.startTime" placeholder="请选择">
                        <Option v-for="(item, $index) in startTimeList" :key="$index" :value="item">{{item}}</Option>
                    </Select>
                    <span class="ml-5 mr-5">-</span>
                    <Select v-model="queryParams.endTime" placeholder="请选择">
                        <Option v-for="(item, $index) in endTimeList" :key="$index" :value="item">{{item}}</Option>
                    </Select>
                </div>
            </div>
            <div class="btn-group">
                <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    data () {
        return {
            queryParams:{
                startDate: '',
                endDate: '',
                startTime: '21:00',
                endTime: '05:00'
            },
            startTimeList: ['21:00', '22:00', '23:00', '24:00'],
            endTimeList: ['03:00', '04:00', '05:00', '06:00']
        }
    },
    watch:{ 
        
    },
    mounted(){
            
    },
    methods: {
        // 查询
        handleSearch() {
            if (!this.queryParams.startDate || !this.queryParams.endDate){
                this.$Message.error("请输入开始时间和结束时间")
                return
            }
            this.queryParams.startDate = this.$dayjs(this.queryParams.startDate).format('YYYY-MM-DD');
            this.queryParams.endDate = this.$dayjs(this.queryParams.endDate).format('YYYY-MM-DD');
            this.$emit('search', this.queryParams)
        }
    }
}
</script>

<style lang='less' scoped>
@import './style/index';
</style>
