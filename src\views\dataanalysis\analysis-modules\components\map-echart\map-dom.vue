<template>
  <div class="dom-wrapper">
    <i class="icon-font icon-guanbi close" @click="() => $emit('close')"></i>
    <div class="dom">
      <section class="dom-content">
        <p class="dom-content-p">
          <span class="label">设备名称：</span>
          <span class="message" :title="mapDomData.deviceName">{{ mapDomData.deviceName || '未知' }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">设备编码：</span>
          <span class="message" :title="mapDomData.deviceId">{{ mapDomData.deviceId || '未知' }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">安装位置：</span>
          <span class="message" :title="mapDomData.address">{{ mapDomData.address || '未知' }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">经度：</span>
          <span class="message">{{ mapDomData.lon || '未知' }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">纬度：</span>
          <span class="message">{{ mapDomData.lat || '未知' }}</span>
        </p>
      </section>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    mapDomData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  display: none;
  position: relative;
  padding: 10px 28px 25px 5px;
  height: 100%;
  .close {
    position: absolute;
    right: 5px;
    top: 0px;
    width: 20px;
    font-size: 20px;
    color: #768192;
  }
}

.dom {
  background: #ffffff;
  width: 330px;
  border-radius: 5px;
  box-shadow: 0 0px 7px -2px rgba(147, 171, 206, 0.7016);
  position: relative;
  padding: 5px;
  .dom-content {
    padding-bottom: 5px;
    font-size: 14px;
    .dom-content-p {
      margin-top: 6px;
      vertical-align: text-top;
      display: flex;
      position: relative;
      .label {
        display: inline-block;
        color: rgba(0, 0, 0, 0.75);
        white-space: nowrap;
        margin-left: 11px;
      }
      .message {
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 12px;
      }
    }
  }
}
.dom:before,
.dom:after {
  content: '';
  display: block;
  border-width: 8px;
  position: absolute;
  bottom: -16px;
  left: 11px;
  border-style: solid dashed dashed;
  border-color: #ffffff transparent transparent;
  font-size: 0;
  line-height: 0;
}
</style>
