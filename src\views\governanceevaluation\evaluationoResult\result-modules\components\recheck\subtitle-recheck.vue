<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel">
    <template>
      <FormItem label="检测方法" prop="osdModel">
        <Select v-model="formData.osdModel" placeholder="请选择" transfer class="width-lg">
          <Option v-for="e in odsCheckModelList" :key="e.dataKey" :value="e.dataKey">{{ e.dataValue }} </Option>
        </Select>
      </FormItem>
      <FormItem label="检测内容" prop="detectContent">
        <CheckboxGroup class="check-inline" v-model="formData.detectContent">
          <Checkbox
            v-for="(checkItem, checkIndex) in detectionRules"
            :key="'check' + checkIndex"
            :label="checkItem.value"
          >
            <span>{{ checkItem.text }}</span>
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
    </template>
  </basic-recheck>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      detectionRules: [
        {
          label: '',
          text: '(右上角) 时间信息位置是否正确',
          value: 'timeLocation',
        },
        {
          label: '',
          text: '(右下角) 辖区和地址信息是否正确',
          value: 'areaLocation',
        },
        { label: '', text: '(左下角) 摄像机信息是否正确', value: 'cameraInfo' },
      ],
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        osdModel: '',
        detectContent: [], // 检测内容
        scheduletime: '', // 自定义时间
        deviceIds: [],
        isUpdatePhyStatus: '',
      },
    };
  },
  async created() {
    if (this.odsCheckModelList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        osdModel: '',
        detectContent: [], // 检测内容
        scheduletime: '', // 自定义时间
        deviceIds: [],
        isUpdatePhyStatus: '',
      };
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
    }),
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          osdModel: this.formData.osdModel,
          detectContent: this.formData.detectContent,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        },
        ...this.$attrs,
      };
    },
  },
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
