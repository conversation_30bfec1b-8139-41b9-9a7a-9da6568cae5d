<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <index-statistics :listyle="listyle"></index-statistics>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem" :columns="columns">
          <template #rank-title>
            <span>按达标率排序</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart"></line-chart>
    </div>
  </div>
</template>

<script>
export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChart: require('@/views/governanceevaluation/evaluationoResult/components/line-chart.vue').default,
  },
  data() {
    return {
      listyle: {
        height: '0.53rem',
        width: '24.3%',
      },
      columns: [
        {
          key: '',
          title: '',
        },
        {
          key: 'value',
          title: this.activeIndexItem.indexName,
          render: (item) => {
            return `${item.data.value}%`;
          },
        },
        {
          key: 'actualNum',
          title: '去年同期抓拍数量',
        },
        {
          key: 'qualifiedNum',
          title: '本月抓拍数量',
        },
      ],
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 235px;
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
