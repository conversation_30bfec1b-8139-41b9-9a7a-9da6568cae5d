<template>
  <div class="videoQuality auto-fill">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
      <!-- <div
        class="information-echart"
        v-ui-loading="{ loading: echartsLoading, tableData: echartData }"
      >
        <draw-echarts
          v-if="echartData.length != 0"
          :echart-option="determinantEchart"
          :echart-style="ringStyle"
          ref="attributeChart"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div> -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                  item.rank
                }}</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                  <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                  <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="item.rank == 1">{{ item.regionName }}</span>
                  <span v-if="item.rank == 2">{{ item.regionName }}</span>
                  <span v-if="item.rank == 3">{{ item.regionName }}</span>
                  <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                    item.regionName
                  }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.standardsValue }}分</span>
              </div>

              <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                  item.rankRise || 0
                }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <!-- <i class="icon-font icon-yichangshujuliebiao f-16 color-filter"></i> -->
        <span class="f-16 color-filter ml-sm">检测结果详情</span>
        <Button type="primary" class="btn_search" :loading="exportLoading" @click="getExport">
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <div class="search-wrapper">
        <ui-label class="mr-lg" label="设备编码" width="70">
          <Input class="input-width" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label label="设备名称" :width="70" class="mr-lg">
          <Input class="input-width" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
        </ui-label>
        <ui-label label="检测结果" :width="70" class="mr-lg">
          <Select v-model="searchData.outcome" clearable placeholder="请选择" class="width-input">
            <Option :value="1">合格</Option>
            <Option :value="2">不合格</Option>
            <Option :value="3">无法检测</Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="fl" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button type="default" class="mr-sm" @click="reast"> 重置 </Button>
        </ui-label>
      </div>
      <div class="list auto-fill">
        <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <!-- 点位类型 -->
          <template #sbdwlx="{ row }">
            <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
          </template>
          <template #description="{ row }">
            <span
              :class="[
                row.description === '合格' ? 'font-green' : '',
                row.description === '不合格' ? 'font-red' : '',
                row.description === '无法检测' ? 'font-D66418' : '',
              ]"
              >{{ row.description }}</span
            >
          </template>
          <template #videoSignal="{ row }">
            <span
              :class="[
                row.videoSignal === '1' ? 'font-green' : '',
                row.videoSignal === '2' ? 'font-red' : '',
                row.videoSignal === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoSignal === '1'
                  ? '正常'
                  : row.videoSignal === '2'
                  ? '异常'
                  : row.videoSignal === '0'
                  ? '无法检测'
                  : '--'
              }}</span
            >
          </template>
          <template #videoBrightness="{ row }">
            <span
              :class="[
                row.videoBrightness === '1' ? 'font-green' : '',
                row.videoBrightness === '2' ? 'font-red' : '',
                row.videoBrightness === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoBrightness === '1'
                  ? '正常'
                  : row.videoBrightness === '2'
                  ? '异常'
                  : row.videoBrightness === '0'
                  ? '无法检测'
                  : '--'
              }}</span
            >
          </template>
          <template #videoColorCast="{ row }">
            <span
              :class="[
                row.videoColorCast === '1' ? 'font-green' : '',
                row.videoColorCast === '2' ? 'font-red' : '',
                row.videoColorCast === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoColorCast === '1'
                  ? '正常'
                  : row.videoColorCast === '2'
                  ? '异常'
                  : row.videoColorCast === '0'
                  ? '无法检测'
                  : '--'
              }}</span
            >
          </template>
          <template #videoClear="{ row }">
            <span
              :class="[
                row.videoClear === '1' ? 'font-green' : '',
                row.videoClear === '2' ? 'font-red' : '',
                row.videoClear === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoClear === '1'
                  ? '正常'
                  : row.videoClear === '2'
                  ? '异常'
                  : row.videoClear === '0'
                  ? '无法检测'
                  : '--'
              }}</span
            >
          </template>
          <template #videoOcclusion="{ row }">
            <span
              :class="[
                row.videoOcclusion === '1' ? 'font-green' : '',
                row.videoOcclusion === '2' ? 'font-red' : '',
                row.videoOcclusion === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoOcclusion === '1'
                  ? '正常'
                  : row.videoOcclusion === '2'
                  ? '异常'
                  : row.videoOcclusion === '0'
                  ? '无法检测'
                  : '--'
              }}</span
            >
          </template>
          <template #option="{ row }">
            <div class="boxCenter">
              <ui-btn-tip
                icon="icon-chakanjietu"
                content="查看截图"
                disabled
                v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
              ></ui-btn-tip>
              <ui-btn-tip
                v-else
                icon="icon-chakanjietu"
                content="查看截图"
                @click.native="showResult(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
        <loading v-if="loading"></loading>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
  </div>
</template>

<style lang="less" scoped>
.videoQuality {
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      // width: 755px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
    }
    .information-echart {
      display: inline-block;
      width: 650px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .echarts-box {
        width: 100%;
        height: 100% !important;
        .charts {
          width: 100%;
          height: 100% !important;
        }
      }
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;
              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    height: calc(100% - 252px);
    .abnormal-title {
      margin-top: 10px;
      padding-right: 2px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
  .search-wrapper {
    margin-top: 10px;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .input-width {
      width: 176px;
    }
    .ui-label {
      margin-bottom: 10px;
    }
  }
  /deep/.conter-center {
    padding: 0;
    width: 100%;
  }
  /deep/.statistics-ul {
    position: relative;
    li {
      width: 280px !important;
    }
    .f55 {
      font-size: 55px;
    }
  }
  /deep/.f-55 {
    font-size: 55px !important;
  }
  .boxCenter {
    display: flex;
    justify-content: center;
  }
  .color_qualified {
    color: #13b13d;
  }
  .color_unqualified {
    color: #e44f22;
  }
}
</style>

<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'basic-information',
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticsList: [
        {
          name: '视频监控设备总数',
          value: 0,
          icon: 'icon-shipinjiankongshebeizongshu-011',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
        },
        {
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-shijiceshebeishuliang-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
        },
        {
          name: '视频质量合格设备',
          value: 0,
          icon: 'icon-shipinzhilianghegeshebei-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          textColor: 'color5',
        },
        {
          name: '视频质量异常设备',
          value: 0,
          icon: 'icon-shipinzhiliangyichangshebei-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
        {
          name: '视频遮挡',
          value: 0,
          icon: 'icon-shipinzhedang-01',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          textColor: 'color6',
        },
        {
          name: '视频信号异常',
          value: 0,
          icon: 'icon-shipinxinhaoyichang-01',
          iconColor: 'icon-bg7',
          liBg: 'li-bg7',
          type: 'percentage',
          textColor: 'color7',
        },
        {
          name: '视频亮度异常',
          value: 0,
          icon: 'icon-shipinliangduyichang-01',
          iconColor: 'icon-bg10',
          liBg: 'li-bg10',
          type: 'percentage',
          textColor: 'color10',
        },
        {
          name: '视频偏色',
          value: 0,
          icon: 'icon-shipinpianse-01',
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          type: 'percentage',
          textColor: 'color8',
        },
        {
          name: '视频清晰度异常',
          value: 0,
          icon: 'icon-shipinqingxiduyichang-01',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'percentage',
          textColor: 'color1',
        },
        {
          name: '视频质量合规率',
          value: 0,
          icon: 'icon-shipinzhilianghegeshuai-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        {
          title: '监控点位类型',
          key: 'sbdwlx',
          slot: 'sbdwlx',
          minWidth: 150,
          tooltip: true,
        },
        { title: '检测结果', slot: 'description', width: 120 },
        { title: '视频信号', slot: 'videoSignal', width: 120 },
        { title: '视频亮度', slot: 'videoBrightness', width: 120 },
        { title: '视频偏色', slot: 'videoColorCast', width: 120 },
        { title: '视频清晰', slot: 'videoClear', width: 120 },
        { title: '视频遮挡', slot: 'videoOcclusion', width: 120 },
        { title: '检测时间', key: 'videoStartTime', align: 'left', width: 160 },
        {
          title: '操作',
          slot: 'option',
          width: 100,
          tooltip: true,
          fixed: 'right',
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        deviceId: '',
        deviceName: '',
        outcome: '',
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      errorMessages: [],
      // exportList: [
      //   { name: '导出设备总表', type: false },
      //   { name: '按异常原因导出分表', type: true },
      // ],
      exportName: '',
    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  async mounted() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    console.log(this.getAlldicData());
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    // onClickIndex(val) {
    //   this.exportList.map((item) => {
    //     if (val === item.name) {
    //       this.exportName = item.type
    //     }
    //   })

    //   this.getExport()
    // },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        multiSheet: this.exportName,
        errorMessages: this.errorMessages,
        orgRegionCode: this.paramsList.orgRegionCode,
        displayType: this.paramsList.displayType,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 柱状图统计
    async getGraphsInfo() {
      let data = {
        regionCode: this.paramsList.regionCode,
        rootResultIds: this.paramsList.rootResultIds,
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data;
        this.initRing();
      } catch (err) {
        console.log(err);
      }
    },
    // 表格
    async getTableData() {
      try {
        // this.loading = true
        this.tableData = [];
        let params = {
          regionCode: this.paramsList.orgRegionCode,
          displayType: this.paramsList.displayType,
          indexId: this.paramsList.indexId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          customParameters: {
            deviceName: this.searchData.deviceName,
            deviceId: this.searchData.deviceId,
            outcome: this.searchData.outcome,
          },
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);

        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;

        // this.loading = false
      } catch (error) {
        console.log(error);
      }
    },
    statistical() {
      this.statisticsList[0].value = this.statisticalList[0].desc;
      this.statisticsList[1].value = this.statisticalList[1].desc;
      this.statisticsList[2].value = this.statisticalList[2].desc;
      this.statisticsList[3].value = this.statisticalList[3].desc;
      this.statisticsList[4].value = this.statisticalList[5].desc;
      this.statisticsList[5].value = this.statisticalList[6].desc;
      this.statisticsList[6].value = this.statisticalList[7].desc;
      this.statisticsList[7].value = this.statisticalList[8].desc;
      this.statisticsList[8].value = this.statisticalList[9].desc;
      this.statisticsList[9].value = this.statisticalList[4].desc;
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    //查看截图
    showResult() {},
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
        deviceInfoId: this.deviceInfoId,
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },

    // selectInfo(val) {
    //   this.errorMessages = val.map((item) => {
    //     return item.name
    //   })
    //   this.getTableData()
    // },
    selectInfo(infoList) {
      this.searchData.pageNumber = 1;
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.getTableData();
      //   this.searchData.tagIds = infoList.map((row) => {
      //     return row.id;
      //   });
      //   this.search();
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
    },
    search() {
      this.searchData = { ...this.searchData, pageNum: 1 };
      this.getTableData();
    },
    reast() {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        deviceId: '',
        deviceName: '',
        outcome: '',
      };
      this.getTableData();
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = JSON.parse(val.indexJson);
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.regionCode) {
          this.paramsList = val;
          this.getTableData(); //表格
          // this.getGraphsInfo() //柱状图
          // this.queryDeviceCheckColumnReports() //头部中间echarts
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
  },
};
</script>
