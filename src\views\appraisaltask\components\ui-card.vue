<template>
  <div>
    <ul>
      <li
        @click="clickCard(item, index)"
        class="card"
        :class="{ 'card-active': activeKey === item.id }"
        v-for="(item, index) in data"
        :key="item.id + '-' + index"
      >
        <span class="icon-style ml-lg"><i class="icon-font" :class="item.icon"></i></span>
        <span class="ml-md">{{ item.title }}</span>
        <span class="count" v-if="countShow">{{ item.count || 0 }}</span>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'card',
  props: {
    countShow: false,
    /**
     * [
     {
                    id: "sdfaaf12323",
                    title:"视图基础数据指标",
                    icon: "icon-jiankong",
                },
     {
                    id: "sdfaaf12323",
                    title:"人脸视图数据指标",
                    icon: "icon-renliantuxiangshuju",
                },
     ]
     */
    data: {
      type: Array,
      default: () => [],
    },
    value: {},
  },
  data() {
    return {
      activeKey: '',
      list: [],
    };
  },
  // created() {
  //   this.initCard()
  // },
  methods: {
    clickCard(item) {
      this.activeKey = item.id;
      this.$emit('input', this.activeKey);
      this.$emit('on-change', item);
    },
    initCard() {
      if (this.data && this.data.length) {
        let row = this.data.find((item) => item.id === this.value);
        this.activeKey = this.value;
        this.$emit('on-change', row);
      }
    },
  },
  watch: {
    value: {
      immediate: true,
      handler: function () {
        this.initCard();
      },
    },
  },
};
</script>
<style lang="less" scoped>
.card {
  width: 261px;
  height: 50px;
  font-size: 14px;
  // line-height: 50px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
  border: 1px solid var(--border-vertical-tab);
  opacity: 1;
  border-radius: 2px;
  color: var(--color-vertical-tab);
  position: relative;
  .count {
    position: absolute;
    right: 20px;
    color: var(--color-primary);
  }
}
.icon-style {
  display: inline-block;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-vertical-tab-icon);
}
.card-active {
  background: var(--bg-vertical-tab-active);
  color: var(--color-vertical-tab-active);
  .count {
    color: var(--color-vertical-tab-active);
  }
  .icon-style {
    color: var(--color-vertical-tab-active);
  }
}
</style>
