<template>
  <div class="pie-echart-box">
    <div class="influence-title" :class="{ 'influence-title-fullscreen': isFullscreen }">
      <span class="title-rect"></span>
      <span class="ml-sm mr-sm title-span f-16">异常设备分布占比</span>
      <i class="icon-font icon-jinggao mr-xs tip-color" v-if="echartInfo.result"></i>
      <div class="f-14 tip-color tip-text ellipsis">
        <Tooltip v-if="echartInfo.result" placement="bottom" :content="echartInfo.result" :disabled="disabledTooltip">
          <span class="ellipsis" @mouseenter="handleTooltip">
            {{ echartInfo.result }}
          </span>
        </Tooltip>
      </div>
      <full-screen class="full-screen-box"></full-screen>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartLoading, tableData: echartData }">
      <draw-echarts
        v-if="echartData.length"
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartLoading"
      ></draw-echarts>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import dataAnalysis from '@/config/api/dataAnalysis.js';
export default {
  name: 'pie-echart',
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    FullScreen: require('@/views/home/<USER>/full-screen.vue').default,
  },
  props: {
    activeInfluenceItem: {
      type: Object,
      default: () => {},
    },
    activeTabId: {
      type: String,
    },
  },
  data() {
    return {
      propertyEchart: {},
      echartData: [],
      series: [],
      echartLoading: false,
      colorList: [
        $var('--color-orange-3'),
        $var('--color-yellow-4'),
        $var('--color-blue-7'),
        $var('--color-green-6'),
        $var('--color-pink-4'),
        $var('--color-cyan-2'),
        $var('--color-purple-12'),
        $var('--color-purple-13'),
        $var('--color-blue-23'),
        $var('--color-orange-8'),
        $var('--color-offline'),
      ],
      echartInfo: {},
      disabledTooltip: false,
    };
  },
  computed: {
    ...mapGetters({
      isFullscreen: 'home/getFullscreen',
    }),
  },
  mounted() {},
  watch: {
    activeInfluenceItem: {
      handler(val) {
        if (!val || !val.key) return;
        this.getEchartData();
      },
      deep: true,
      immediate: true,
    },
    isFullscreen() {
      this.initRing();
    },
  },
  methods: {
    handleTooltip(e) {
      this.disabledTooltip = e.target.scrollWidth > e.target.clientWidth ? false : true;
    },
    async getEchartData() {
      let { batchId } = this.$route.query;
      if (!batchId) return;
      try {
        this.echartLoading = true;
        let data = {
          batchId: batchId,
          customParameters: {
            inflKey: this.activeTabId,
            fieldKey: this.activeInfluenceItem.key,
          },
        };
        let res = await this.$http.post(dataAnalysis.getGraphPieInfo, data);
        this.echartInfo = res.data.data || {};
        this.echartData = res.data.data.categoryStatList || [];
        this.initRing();
      } catch (error) {
        console.log(error);
      } finally {
        this.echartLoading = false;
      }
    },
    initRing() {
      let legendList = [];
      let data = this.echartData.map((item, index) => {
        legendList.push({ name: item.dimeName });
        return {
          ...item,
          value: item.unqualifiedNum,
          name: `${item.dimeName}`,
          itemStyle: {
            color: this.colorList[index % 11],
          },
        };
      });
      let labelLine = this.isFullscreen
        ? {
            length: 30,
          }
        : {};
      let opts = {
        legend: {
          orient: 'vertical',
          right: 5,
          top: 'middle',
          type: 'scroll',
          itemHeight: 8,
          itemWidth: 8,
          itemGap: 16,
          data: legendList,
          pageIconColor: $var('--color-blue-24'), // 翻页按钮的颜色
          pageIconInactiveColor: $var('--color-gray-5'), // 翻页按钮不激活时的颜色
          pageTextStyle: {
            color: $var('--color-base-text'), // 修改页码颜色为红色
          },
          formatter: (name) => {
            let dataItem = data.filter((item) => item.dimeName === name);
            if (dataItem.length) {
              let arr = [
                `{a|${dataItem[0]?.dimeName}}\n`,
                `{b|${dataItem[0]?.unqualifiedNum}台, ${dataItem[0]?.resultValue}%}`,
              ];
              return arr.join('');
            } else {
              return '';
            }
          },
          textStyle: {
            color: 'auto',
            overflow: 'truncate',
            width: this.isFullscreen ? 300 : 145,
            align: 'left',
            rich: {
              a: {
                color: 'auto',
                padding: [16, 0, 0, 0],
                fontSize: this.isFullscreen ? 16 : 12,
              },
              b: {
                color: $var('--color-base-text'),
                padding: [5, 0, 0, 0],
                fontSize: this.isFullscreen ? 16 : 12,
              },
            },
          },
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            left: -150,
            bottom: 5,
            radius: ['40%', '70%'],
            avoidLabelOverlap: true,
            label: {
              formatter: '{a|{b}} \n {b|{c}}',
              width: this.isFullscreen ? 300 : 150,
              overflow: 'break',
              rich: {
                a: {
                  color: $var('--color-base-text'),
                  fontSize: this.isFullscreen ? 16 : 12,
                },
                b: {
                  color: 'auto',
                  padding: [3, 0, 0, 0],
                  fontSize: this.isFullscreen ? 16 : 12,
                },
              },
              // 高亮状态 label也有放大效果
              emphasis: {
                show: true,
                formatter: '{a|{b}} \n {b|{c}}',
                rich: {
                  a: {
                    fontSize: this.isFullscreen ? 17 : 13,
                  },
                  b: {
                    fontSize: this.isFullscreen ? 17 : 13,
                  },
                },
              },
            },
            emphasis: {
              // disabled: true, // 是否关闭高亮状态。
            },
            labelLine: {
              // show: false,
              ...labelLine,
              // 高亮状态 labelLine也有放大效果
              emphasis: {
                show: true,
                lineStyle: {
                  width: 1.5,
                },
              },
            },
            data: data,
          },
          // 中间
          {
            name: 'Access From',
            type: 'pie',
            left: -150,
            bottom: 5,
            radius: ['34.5%', '35%'],
            emphasis: {
              disabled: true, // 是否关闭高亮状态。
            },
            label: {
              position: 'center',
              formatter: (params) => {
                return `{a|${params.value}台}\n{b|${params.name}}`;
              },
              rich: {
                a: {
                  color: $var('--color-red-2'),
                  fontSize: this.isFullscreen ? 20 : 16,
                },
                b: {
                  color: $var('--color-label-text'),
                  padding: [8, 0, 0, 0],
                  fontSize: this.isFullscreen ? 16 : 12,
                },
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: this.echartInfo.abDeviceNum,
                name: '异常设备',
                itemStyle: {
                  color: $var('--color-gray-4'),
                },
              },
            ],
          },
        ],
      };
      this.propertyEchart = this.$util.doEcharts.AbnormalDeviceScale(opts);
    },
  },
};
</script>
<style lang="less" scoped>
.pie-echart-box {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  .influence-title {
    width: 100%;
    height: 46px;
    background: var(--bg-sub-echarts-title);
    display: flex;
    align-items: center;
    .title-rect {
      display: inline-block;
      width: 5px;
      height: 20px;
      margin-left: 20px;
      background: var(--bg-title-rect);
    }
    .title-span {
      width: fit-content;
      color: var(--color-sub-title-inpage);
    }
    .tip-color {
      color: var(--color-warning);
    }
    .tip-text {
      flex: 1;
      @{_deep}.ivu-tooltip {
        max-width: 100%;
        .ivu-tooltip-rel {
          display: flex;
        }
        .ivu-tooltip-inner {
          white-space: initial;
          max-width: 700px;
          max-height: 400px;
          overflow: hidden;
          overflow-y: auto;
        }
      }
    }
    @{_deep}.full-screen-box {
      position: inherit !important;
      margin: 0 20px;
      &.container .icon {
        color: #888888 !important;
        border: 1px solid #888888 !important;
      }
    }
    &.influence-title-fullscreen {
      height: 60px;
      font-size: 16px;
      .tip-text {
        font-size: 16px !important;
      }
    }
  }
  .echarts-box {
    width: 100%;
    padding-top: 5px;
    height: calc(100% - 46px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
