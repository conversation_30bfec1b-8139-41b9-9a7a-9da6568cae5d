<template>
  <span :class="{ 'i-layout-header-logo-stick': !isMobile }" class="i-layout-header-logo" to="/">
      <img class="img-logo" v-if="applicationInfo.logoUrl" :src="applicationInfo.logoUrl" alt=""/>
      <img class="img-logo" v-else src="@/assets/img/logo-img.png" alt=""/>
      <span class="title">{{applicationInfo.applicationName || "视综平台"}}</span>
    <!-- <img src="@/assets/img/logo-img.png" class="img-logo" />
    <span class="title">启数感知大数据平台</span>-->
  </span>
</template>
<script>
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  name: `iHeaderLogo`,
  computed: {
    ...mapState('admin/layout', ['isMobile', 'headerTheme']),
    ...mapGetters({
      applicationInfo: 'applicationInfo'
    }),
  },
  async mounted () {
    this.appInfo()
    // console.log('页面title', document.title)
  },
  methods: {
    ...mapActions({
      appInfo: 'appInfo'
    }),
  }
}
</script>
