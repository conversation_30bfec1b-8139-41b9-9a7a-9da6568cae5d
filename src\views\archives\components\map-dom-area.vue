<template>
  <div class="area-dom-wrapper">
    <div class="dom">
      <section class="dom-content">
        <p class="dom-content-p">
          <i class="icon-font icon-changsuobianhao f-12 font-green"></i>
          <span class="label">场所编号：</span>
          <span class="message">
            {{ mapDomAreaData.placeInternalCode || '未知' }}
          </span>
        </p>
        <p class="dom-content-p">
          <i class="icon-font icon-changsuomingcheng font-yellow"></i>
          <span class="label">场所名称：</span>
          <span class="message">
            {{ mapDomAreaData.placeName || '未知' }}
          </span>
        </p>
        <p class="dom-content-p">
          <i class="icon-font icon-changsuoleixing font-orange"></i>
          <span class="label">场所类型：</span>
          <span class="message">
            {{ mapDomAreaData.placeTypeName || '未知' }}
          </span>
        </p>
      </section>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    mapDomAreaData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .dom {
    background: #ffffff;
    border: 0;
    box-shadow: 0px 0px 6px 0px rgba(11, 51, 94, 0.15);
    margin-left: 3px;
    .dom-content {
      .dom-content-p {
        .label {
          color: rgba(0, 0, 0, 0.35);
        }
        .message {
          color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }
  .dom:before,
  .dom:after {
    border-color: #ffffff transparent transparent;
    filter: drop-shadow(0px 0px 0px rgba(147, 171, 206, 0.7016));
  }
}
.area-dom-wrapper {
  position: relative;
  padding: 10px 28px 25px 0;
  height: 100%;
}
.dom {
  background: rgba(0, 41, 97, 0.9);
  border: 1px solid #0068b7;
  width: 308x;
  border-radius: 5px;
  box-shadow: 0 0 20px #0769ce inset;
  .dom-content {
    padding-bottom: 5px;
    .dom-content-p {
      margin-top: 6px;
      width: 308px;
      height: 24px;
      line-height: 24px;
      display: flex;
      position: relative;
      font-size: 14px;
      i {
        margin-left: 15px;
        margin-right: 10px;
      }
      .label {
        display: inline-block;
        color: #ffffff;
        white-space: nowrap;
        width: 54px;
      }
      .message {
        display: inline-block;
        color: #b4ceef;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 18px;
      }
      .font-green {
        color: #19a33e;
      }
      .font-yellow {
        color: #bfa631;
      }
      .font-orange {
        color: #f5852a;
      }
    }
  }
}
.dom:before,
.dom:after {
  content: '';
  display: block;
  border-width: 8px;
  position: absolute;
  bottom: 9px;
  left: 11px;
  border-style: solid dashed dashed;
  border-color: #0254a7 transparent transparent;
  font-size: 0;
  line-height: 0;
}
</style>
