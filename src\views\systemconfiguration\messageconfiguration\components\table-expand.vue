<template>
  <div>
    <div class="expand-row" v-if="row.peopleList && row.peopleList.length">
      <div class="notice">
        <span class="expand-key header">通知对象</span>
      </div>
      <div class="phone">
        <span class="header">联系电话</span>
      </div>
    </div>
    <div v-else class="t-center">暂无数据</div>
    <template v-for="(item, index) in row.peopleList">
      <div class="expand-row" v-if="!item.isDelete" :key="index">
        <div class="notice">
          <span class="expand-key">{{ item.name }}</span>
        </div>
        <div class="phone">
          <Input
            v-if="isEdit"
            class="width-md"
            placeholder="请输入联系电话"
            v-model="item.phone"
            @on-change="changePhone(item)"
          ></Input>
          <span v-else>{{ item.phone }}</span>
        </div>
        <div>
          <ui-btn-tip
            v-if="isEdit"
            icon="icon-yichu1"
            content="移除"
            @handleClick="deleteRow(index, item)"
          ></ui-btn-tip>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
export default {
  props: {
    row: Object,
    isEdit: Boolean,
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    deleteRow(index, item) {
      this.row.peopleList.splice(index, 1);
      this.$emit('deleteRow', item);
    },
    changePhone(item) {
      this.$emit('changePhone', item);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.header {
  color: var(--color-display-text);
}
.expand-row {
  display: flex;
  align-items: center;
  &:nth-child(n + 2) {
    margin-top: 25px;
  }
  .expand-key {
    margin-left: 160px;
  }
  .notice {
    width: 610px;
  }
  .phone {
    width: 560px;
  }
}
</style>
