<template>
  <div :class="getFullscreen ? 'full-screen-container' : ''" class="container" @click="setScreenFull">
    <i :class="getIcon" class="iconfont f-24 icon"></i>
  </div>
</template>

<script>
import screenfull from 'screenfull'
import { mapActions, mapGetters } from 'vuex'

export default {
  name: 'FullScreen',
  components: {},
  props: {},
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen'
    }),
    getIcon() {
      if (this.getFullscreen) {
        return 'icon-quanpingshouqi'
      } else {
        return 'icon-quanping'
      }
    }
  },
  watch: {},
  filter: {},
  mounted() {
    this.DetectFullscreenChange()
  },
  methods: {
    ...mapActions({
      setFullscreen: 'home/setFullscreen'
    }),
    DetectFullscreenChange() {
      if (screenfull.isEnabled) {
        screenfull.on('change', () => {
          if (screenfull.isFullscreen) {
            this.setFullscreen(true)
          } else {
            this.setFullscreen(false)
          }
        })
      }
    },
    setScreenFull() {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit()
        this.setFullscreen(false)
      } else {
        screenfull.toggle(this.$parent.$el)
        this.setFullscreen(true)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  width: 40px;
  position: absolute;
  right: 0px;
  top: 5px;
  .icon {
    line-height: 12px;
    color: #fff;
    padding: 5px;
  }
}
.full-screen-container {
  right: 35px;
  top: 25px;
}
</style>
