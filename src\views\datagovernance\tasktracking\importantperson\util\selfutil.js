import importantEnum from './enum';
export function overTimeFunc(element, isImportantDevice, disqualifyItem) {
  let nomalLabelList = [
    { name: '设备类型', value: isImportantDevice ? '重点人脸卡口' : '普通人脸卡口' },
    { name: '抓拍时间', value: disqualifyItem.logTime },
    { name: '入库时间', value: element.createTime },
  ];
  let logTimeItem = messageText.nomalLabelList.find((item) => item.name === '抓拍时间');
  let createTimeItem = messageText.nomalLabelList.find((item) => item.name === '入库时间');
  let timeStamp = Math.abs(new Date(logTimeItem.value).getTime() - new Date(createTimeItem.value).getTime());
  let minusTimer = this.$util.common.timeFn(timeStamp);
  // 时延赋值
  if (!element.extraParamObj) return;
  // if (isImportantDevice) {
  nomalLabelList.push({
    name: '结论',
    value:
      `${minusTimer},超过${element.extraParamObj.important + importantEnum.timeEnum[element.extraParamObj.importantTime]}` ||
      '暂无',
  });
  // } else {
  //     nomalLabelList.push(
  //         {
  //             name: '结论',
  //             value: (`${minusTimer},超过${element.extraParamObj.important + importantEnum.timeEnum[element.extraParamObj.importantTime]}`) || '暂无'
  //         }
  //     )
  // }
  return nomalLabelList;
}
