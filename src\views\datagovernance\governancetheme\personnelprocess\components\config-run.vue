<template>
  <ui-modal v-model="visible" title="配置运行" width="550px" @onCancel="reset">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="120">
      <FormItem label="选择数据对象" prop="check">
        <Select v-model="formValidate.check" placeholder="请选择数据对象">
          <Option value="importantPersonBaseCheck">重点人员库基础数据检测</Option>
          <Option value="importantPersonCheck">重点人员人像轨迹数据检测</Option>
        </Select>
      </FormItem>
    </Form>
    <template #footer>
      <Button type="primary" @click="start" class="plr-30">确 定</Button>
      <Button @click="reset" class="plr-30">取 消</Button>
    </template>
    <loading v-if="loading"></loading>
  </ui-modal>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {},
  data() {
    return {
      visible: false,
      formValidate: {
        check: '',
      },
      ruleValidate: {
        check: [
          {
            required: true,
            message: '请选择数据对象',
            trigger: 'change',
          },
        ],
      },
      loading: false,
    };
  },
  created() {},
  methods: {
    init() {
      this.visible = true;
    },
    // 检测重点人员轨迹(开始运行)
    async start() {
      const valid = await this.$refs['formValidate'].validate((valid) => valid);
      if (valid) {
        try {
          this.loading = true;
          let res = await this.$http.post(governancetheme[this.formValidate.check]);
          if (res.data.code == '200') {
            this.$Message.success('运行成功！');
            this.reset();
          }
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      } else {
        this.$Message.error('请选择!');
      }
    },
    reset() {
      this.$refs['formValidate'].resetFields();
      this.visible = false;
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 50px;
}
@{_deep} .ivu-modal .title {
  z-index: 15;
}
</style>
