<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles" :footer-hide="isView" @query="query">
    <Form
      class="form"
      ref="form"
      inline
      autocomplete="off"
      label-position="right"
      :rules="ruleCustom"
      :model="formCustom"
    >
      <FormItem required class="left-item" :label="`${global.filedEnum.deviceId}：`">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.deviceId"
          placeholder="请输入设备编号"
          :maxlength="20"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.deviceId || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.deviceId">
          {{ errorData.deviceId }}
        </div>
      </FormItem>
      <FormItem required class="left-item" :label="`${global.filedEnum.deviceName}：`">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.deviceName"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.deviceName || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.deviceName">
          {{ errorData.deviceName }}
        </div>
      </FormItem>
      <FormItem required class="left-item" label="设备类型：">
        <Select v-if="!isView" class="width-md" v-model="formCustom.deviceType" placeholder="请选择设备类型" clearable>
          <Option v-for="(item, index) in other_device_type" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.deviceType | filterType(other_device_type) }}</span>
        <div class="error-tip ellipsis" :title="errorData.deviceType">
          {{ errorData.deviceType }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="品牌：">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.brand"
          placeholder="请输入品牌"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.brand || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.brand">
          {{ errorData.brand }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="型号：">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.model"
          placeholder="请输入型号"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.model || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.model">
          {{ errorData.model }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="IP地址：">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.ipAddr"
          placeholder="请输入IP地址"
          :maxlength="32"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.ipAddr || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ipAddr">
          {{ errorData.ipAddr }}
        </div>
      </FormItem>
      <FormItem class="left-item" :label="`${global.filedEnum.macAddr}：`">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.macAddr"
          :placeholder="`请输入${global.filedEnum.macAddr}`"
          :maxlength="32"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.macAddr || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.macAddr">
          {{ errorData.macAddr }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="设备状态：">
        <Select
          v-if="!isView"
          class="width-md"
          v-model="formCustom.deviceStatus"
          clearable
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
        >
          <Option
            v-for="(item, index) in phystatusList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.deviceStatus | filterType(phystatusList) }}</span>
        <div class="error-tip ellipsis" :title="errorData.deviceStatus">
          {{ errorData.deviceStatus }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="所属组织机构：">
        <api-organization-tree
          v-if="!isView"
          class="org-tree"
          placeholder="请选择所属组织机构"
          :select-tree="selectOrgTree"
          @selectedTree="selectedOrgTree"
        ></api-organization-tree>
        <span v-else class="base-text-color">{{ formCustom.orgName || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.orgCode">
          {{ errorData.orgCode }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="所属项目：">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.belongProject"
          placeholder="请输入所属项目"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.belongProject || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.belongProject">
          {{ errorData.belongProject }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="维护单位：">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.whdw"
          placeholder="请输入维护单位"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.whdw || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.whdw">
          {{ errorData.whdw }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="维护单位联系人：">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.whdwlxr"
          placeholder="请输入维护单位联系人"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.whdwlxr || '--' }}</span>
        <div class="error-tip ellipsis" :title="errorData.whdwlxr">
          {{ errorData.whdwlxr }}
        </div>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {},
    modalAction: {},
    defaultForm: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '5rem',
      },
      selectOrgTree: {
        orgCode: '',
      },
      formCustom: {
        id: '',
        deviceId: '',
        deviceName: '',
        deviceType: '',
        brand: '',
        model: '',
        ipAddr: '',
        macAddr: '',
        deviceStatus: '',
        orgCode: '',
        orgName: '',
        belongProject: '',
        whdw: '',
        whdwlxr: '',
      },
      errorData: {},
      ruleCustom: {},
    };
  },
  created() {},
  methods: {
    selectedOrgTree(val) {
      this.formCustom.orgCode = val.orgCode;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.formCustom = {
        id: '',
        deviceId: '',
        deviceName: '',
        deviceType: '',
        brand: '',
        model: '',
        ipAddr: '',
        macAddr: '',
        deviceStatus: '',
        orgCode: '',
        orgName: '',
        belongProject: '',
        whdw: '',
        whdwlxr: '',
      };
      this.selectOrgTree.orgCode = '';
    },
    async query() {
      try {
        let url = equipmentassets.updateOtherDevice;
        this.submitLoading = true;
        const res = await this.$http.post(url, this.formCustom);
        this.$Message.success(res.data.msg);
        this.visible = false;
        this.$emit('update');
      } catch (err) {
        console.log(err);
      } finally {
        this.submitLoading = false;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        this.resetFields();
      }
      this.visible = val;
    },
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          this.selectOrgTree.orgCode = '';
          if (length > 0) {
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                switch (key) {
                  default:
                    this.formCustom[key] = val[key];
                    break;
                }
              }
            });
            this.selectOrgTree.orgCode = val.orgCode;
          }
        });
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      phystatusList: 'algorithm/propertySearch_phystatus',
      other_device_type: 'algorithm/other_device_type',
    }),
    isAdd() {
      return this.modalAction.action === 'add';
    },
    isView() {
      return this.modalAction.action === 'view';
    },
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 180px;
@inputWidth: 200px;
@{_deep} .ivu-modal-body {
  height: 400px;
}
@{_deep}.ivu-form-item {
  width: 100%;
  margin-right: 0;
  &-label {
    color: var(--color-label) !important;
  }
}
.left-item {
  width: 50%;
  @{_deep} .ivu-form-item-error-tip {
    margin-left: @leftMargin;
  }
  @{_deep}.ivu-form-item-label {
    width: @leftMargin;
  }
}
.error-tip {
  position: absolute;
  top: 100%;
  left: 0;
  line-height: 1;
  padding-top: 5px;
  color: #ed4014;
  margin-left: @leftMargin;
  width: @inputWidth;
}
</style>
