/**
// * 同行列表
 */
<template>
  <div class="peer-list" :class="{'peer-list-pack': packUpDown}">
    <div class="peer-hint">
      <i class="iconfont icon-jiantou" @click="handleback"></i>
      <span @click="handleback">返回检索结果</span>
    </div>
    <div class="peer-content">
      <div class="title">
        <p>同行 <span> {{ total }} </span> 次</p>
        <Icon type="ios-close" @click="handleback" />
      </div>
      <ul class="peer-content-box" v-infinite-scroll="load" :infinite-scroll-immediate='false'>
        <li class="content-box-li" v-for="(item, index) in peerList" :key='index'>
          <div class="box-li-icon"><div class="box-li-text">{{ index + 1 }}</div></div>
          <div class="box-li-right">
            <p class="box-li-right-title">{{ item.deviceName || '--' }}</p>
            <ul class="box-li-right-content">
              <li>
                <div class="right-img-list">
                  <img v-lazy="item.ferriteDetailVo.traitImg" alt="" @click="handlePeer(item, index, 'ferriteDetailVo')">
                  <!-- <ui-image :src="typeIndex.tab==0?parentData.photo : parentData.traitImg" viewer /> -->
                  <!-- <template v-if="parentData.photos && parentData.photos.length > 0">
                                        <img-list :dataObj="parentData" :index="index"></img-list>
                                    </template> -->
                  <!-- <ui-image v-else viewer :src="typeIndex.tab==0 ? parentData.photo : parentData.traitImg" /> -->
                  <p class="right-img-list-tag tag-bule">对象</p>
                </div>
                <p class="box-li-right-time">{{ item.ferriteDetailVo.absTime || '--'}}</p>
              </li>
              <li>
                <div class="right-img-list">
                  <!-- <ui-image :src="item.ferriteDetailVo.traitImg" viewer /> -->
                  <img v-lazy="item[peerDetailVo[typeIndex.tab]].traitImg" alt="" @click="handlePeer(item, index, peerDetailVo[typeIndex.tab])">
                  <p class="right-img-list-tag tag-yellow">同行</p>
                  <div class="plateNumber">{{ showtext(item) }}</div>
                </div>
                <p class="box-li-right-time">{{ item[peerDetailVo[typeIndex.tab]].absTime || '--'}}</p>
              </li>
            </ul>
          </div>
        </li>
        <ui-empty v-if="peerList.length === 0 && loading== false"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
        <p class="loading" v-if="loadingText">加载中...</p>
        <p class="loading" v-if="noMore && peerList.length != 0">没有更多了</p>
      </ul>
    </div>
    <div class="footer" :class="{packArrow: packUpDown}" @click="handlePackup">
      <img :src="packUrl" alt="">
      <p>{{ packUpDown ? '展开' : '收起'}} </p>
    </div>
  </div>
</template>

<script>
import { queryFaceCapturePageList, queryVehicleCapturePageList } from '@/api/modelMarket';
import imgList from '../../components/imgList/index.vue';
import { mapMutations } from 'vuex';
export default {
  name: '',
  components: {
    imgList
  },
  data () {
    return {
      imgUrl: require('@/assets/img/model/pic7.png'),
      packUrl: require('@/assets/img/model/icon/arrow.png'),
      peerList: [

      ],
      page: {
        "pageNumber": 1,
        "pageSize": 10,
      },
      loading: false,
      detailRequest: {
        0: queryFaceCapturePageList, // 人脸
        1: queryVehicleCapturePageList, // 车辆
      },
      peerDetailVo: {
        0: 'faceCapturePeerDetailVo',
        1: 'vehiclePeerDetailVo',
      },
      parentData: {},
      typeIndex: {
        tab: 0,
        secTab: 0
      },
      total: 0,
      loadingText: false,
      noMore: false,
      paramsInfo: {},
      packUpDown: false
    }
  },
  watch: {

  },
  computed: {

  },
  created () {

  },
  mounted () {

  },
  methods: {
    ...mapMutations({
      setPeerList: 'map/setPeerList',
      setPeerData: 'map/setPeerData',
    }),
    // 查询同行次数
    init (val, tabIndex, item) {
      this.typeIndex = tabIndex; //用于判断tab类型
      this.loading = true;
      this.page = {
        "pageNumber": 1,
        "pageSize": 10,
      };
      this.peerList = [];
      this.paramsInfo = val;
      this.parentData = item;
      this.rightList(val);
    },
    rightList (val) {
      let params = {
        ...this.page,
        ...val
      };
      this.detailRequest[this.typeIndex.tab](params)
        .then(res => {
          /**
           * 统一 同行字段 
           * 人脸： faceCapturePeerDetailVo
           * 车辆： vehiclePeerDetailVo
           */
          res.data && res.data.entities.map(item => {
            item.peerDetailVo = this.typeIndex.tab == 0 ? item.faceCapturePeerDetailVo : item.vehiclePeerDetailVo;
          })
          this.peerList = this.peerList.concat(...res.data.entities);
          this.total = res.data.total;
          let maplist = []
          this.peerList.map((item, index) => {
            item.ferriteDetailVo.geoPoint = item.geoPoint;
            maplist.push({ ...item, 'num': index + 1 })
          });
          this.setPeerList(maplist); //用于弹出框顶部相同点位数据
          this.$emit('coincide', maplist)
        })
        .finally(() => {
          this.loading = false;
          this.loadingText = false;
        })
    },
    load () {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return
      };
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        setTimeout(() => {
          this.noMore = false;
        }, 1000)
        return
      } else {
        this.noMore = false;
        this.loadingText = true;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.rightList(this.paramsInfo)
      }
    },
    handleback () {
      this.$emit('back')
    },
    handlePeer (item, index, type) {
      // this.$emit('peer', item, index, type)
      this.setPeerData({
        'peerIndex': index,
        'type': type,
        'menuType': 'peer'
      })
      let maplist = [];
      this.peerList.map((item, index) => {
        item.peerDetailVo = item[type]
        item.ferriteDetailVo.geoPoint = item.geoPoint;
        item.ferriteDetailVo.captureAddress = item.captureAddress;
        maplist.push({ ...item, 'num': index + 1 })
      });
      this.setPeerList(maplist); //用于弹出框顶部相同点位数据
    },
    handlePackup () {
      this.packUpDown = !this.packUpDown;
    },
    showtext (item) {
      let val = '--'
      if (this.typeIndex.tab == 0) {
        val = item[this.peerDetailVo[this.typeIndex.tab]].name || '--';
      } else if (this.typeIndex.tab == 1) {
        val = item[this.peerDetailVo[this.typeIndex.tab]].plateNo;
      }
      return val
    },
  }
}
</script>

<style lang='less' scoped>
@import "./style/index";
.peer-list {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 10px;
  height: calc(~"100% - 30px");
  transition: height 1s ease-out;
  .peer-hint {
    width: 370px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    filter: blur(0px);
    color: #2c86f8;
    font-size: 14px;
    line-height: 40px;
    padding-left: 14px;
    .icon-jiantou {
      transform: rotate(90deg);
      display: inline-block;
      cursor: pointer;
    }
    span {
      font-size: 14px;
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .peer-content {
    margin-top: 10px;
    background: #fff;
    height: calc(~"100% - 80px");
    // height: 100%;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    .peer-content-box {
      margin-top: 10px;
      padding: 0 23px 0px 19px;
      min-height: 400px;
      height: calc(~"100% - 50px");
      overflow: auto;
      position: relative;
      .content-box-li {
        margin-top: 10px;
        margin-bottom: 13px;
        display: flex;
        .box-li-icon {
          width: 30px;
          background: url("../../../../../assets/img/archives/mark-red-new.png")
            no-repeat;
          background-size: 30px 34px;
          display: flex;
          justify-content: center;
          height: 34px;
          color: #ea4a36;
          .box-li-text{
            margin-top: 4px;
            width: 30px;
            height: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: white;
            border: 2px solid #ea4a36;
            border-radius: 8px;
          }
        }
        .box-li-right {
          flex: 1;
          margin-left: 12px;
          &-title {
            font-size: 12px;
            font-weight: bold;
            color: #181818;
          }
          &-content {
            background: #f9f9f9;
            border-radius: 4px;
            padding: 10px 9px 12px;
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            li {
              display: flex;
              flex-direction: column;
              align-items: center;
            }
            .right-img-list {
              position: relative;
              width: 100px;
              margin-bottom: 10px;
              height: 100px;
              img {
                width: 100px;
                height: 100px;
                cursor: pointer;
              }
              &-tag {
                // width: 40px;
                // height: 20px;
                border-radius: 0px 0px 4px 0px;
                position: absolute;
                top: 0;
                color: #fff;
                font-size: 12px;
                padding: 1px 3px;
                z-index: 20;
              }
              .tag-bule {
                background: #2c86f8;
              }
              .tag-yellow {
                background: #f29f4c;
              }
              .plateNumber {
                position: absolute;
                bottom: 0px;
                left: 1px;
                background: rgba(0, 0, 0, 0.6);
                width: 98px;
                text-align: center;
                // opacity: 0.6;
                color: #ffffff;
                font-size: 14px;
              }
            }
          }
          .box-li-right-time {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.6);
          }
        }
      }
    }
  }
}
.peer-list-pack {
  height: 120px;
  transition: height 1s ease-out;
  overflow: hidden;
}
.footer {
  color: #000000;
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translate(-50%, 0px);
  background: #fff;
  width: 100%;
  z-index: 30;
}
</style>
