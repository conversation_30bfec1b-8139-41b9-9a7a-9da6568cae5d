<template>
  <div class="quantitative-trend-container">
    <tab-title v-model="activeValue" :data="tabData" @on-change="onChangeTitle">
      <template #filter>
        <DatePicker
          placement="left"
          class="mr-xs"
          :open="open"
          :value="year"
          confirm
          :options="options"
          type="year"
          format="yyyy"
          placeholder="请选择年"
          @on-change="handleChange"
          @on-ok="handleOk"
        >
          <i @click="handleClick" class="year-color">{{ year }}年</i>
          <Icon type="ios-arrow-down" class="year-color vt-sub" @click="handleClick" />
        </DatePicker>
        <drop-select
          v-model="searchData.sbdwlxList"
          multiple
          :data="dropData"
          @on-change="handleChangeSelect"
        ></drop-select>
      </template>
    </tab-title>
    <div class="echarts-container" v-ui-loading="{ loading }">
      <draw-echarts :echart-option="chartsOption"></draw-echarts>
    </div>
  </div>
</template>

<script>
import commonToolTipConfig from '@/views/viewassets/assetstatistics/utils/config-toolTip.js';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'quantitative-trend',
  components: {
    TabTitle: require('../components/tab-title.vue').default,
    DropSelect: require('../components/drop-select.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    homeConfig: {
      default: () => ({}),
    },
    activeTab: {
      default: '1',
    },
  },
  data() {
    return {
      echartsData: [],
      loading: false,
      open: false,
      year: `${new Date().getFullYear()}`,
      options: {
        disabledDate: function (date) {
          let now = new Date().getFullYear();
          return date.getFullYear() > now;
        },
      },
      activeValue: 'deviceStatus',
      dropValue: '1',
      tabData: [{ label: '数量变化趋势', id: 'deviceStatus' }],
      dropData: [
        { label: '一类点', id: '1' },
        { label: '二三类点', id: '2' },
        { label: '内部监控', id: '4' },
      ],
      searchData: {
        sbdwlxList: [],
      },
      chartsOption: {},
    };
  },
  computed: {},
  watch: {
    activeTab: {
      async handler() {
        await this.postQueryStatisticsByAmount();
        this.initChart();
      },
    },
    homeConfig: {
      async handler(val) {
        if (Object.keys(val).length > 0) {
          await this.postQueryStatisticsByAmount();
          this.initChart();
        }
      },
      deep: true,
    },
  },
  filter: {},
  methods: {
    handleClick() {
      this.open = !this.open;
    },
    handleChange(date) {
      this.year = date;
    },
    async handleOk() {
      this.open = false;
      this.postQueryStatisticsByAmount();
    },
    onChangeTitle() {},
    async handleChangeSelect() {
      await this.postQueryStatisticsByAmount();
      this.initChart();
    },
    initChart() {
      let options = {
        series: [],
        commonToolTipConfig: commonToolTipConfig,
        toolTipItem: this.toolTipItem,
      };
      let data = [];
      for (let month = 1; month < 13; month++) {
        let index = this.echartsData.findIndex((item) => item.month === month);
        if (index === -1) {
          data.push({
            videoAmount: 0,
            faceAmount: 0,
            vehicleAmount: 0,
            month: month,
          });
        } else {
          data.push(this.echartsData[index]);
        }
      }
      let legend = [
        { label: '视频监控', key: 'videoAmount', lineStyle: $var('--color-purple-4'), trend: 'videoAmountRange' },
        { label: '人脸卡口', key: 'faceAmount', lineStyle: $var('--color-yellow-5'), trend: 'faceAmountRange' },
        { label: '车辆卡口', key: 'vehicleAmount', lineStyle: $var('--color-blue-8'), trend: 'vehicleAmountRange' },
      ];
      legend.forEach((item) => {
        options.series.push({
          symbol: 'none',
          name: item.label,
          type: 'line',
          data: data.map((value) => {
            return {
              value: value[item.key],
              trend: value[item.trend] || 0,
              data: value,
            };
          }),
          lineStyle: {
            color: item.lineStyle, //线条颜色
          },
          itemStyle: {
            color: item.lineStyle, //线条颜色
          },
        });
      });
      this.chartsOption = this.$util.doEcharts.QuantitativeTrend(options);
    },
    async postQueryStatisticsByAmount() {
      try {
        this.loading = true;
        let { regionCode } = this.homeConfig;
        let params = {
          civilCode: regionCode,
          type: this.activeTab,
          year: this.year,
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.postQueryStatisticsByAmount, params);
        this.echartsData = data || { amount: 0, unAmount: 0 };
        this.initChart();
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    toolTipItem(params) {
      return {
        nameIcon: '',
        name: `${this.year}年${params[0]['axisValue']}月`,
        list: params.map((item, index) => {
          return {
            label: item.seriesName,
            color: item.color,
            num: item.value,
            slotName: `slotA-${index}`,
            slotRender: () => (
              <span>
                <span>{item.data.trend > 0 ? `+${item.data.trend}` : item.data.trend}</span>
                <span
                  class={
                    item.data.trend >= 0
                      ? 'icon-shangsheng icon-font  icon-up f-12'
                      : 'icon-xiajiang icon-font icon-down  f-12'
                  }
                ></span>
              </span>
            ),
          };
        }),
      };
    },
  },
};
</script>

<style lang="less" scoped>
@{_deep}.tooltip-dom {
  .icon-up {
    color: var(--color-success);
  }
}
@{_deep}.tooltip-dom {
  .icon-down {
    color: var(--color-warning);
  }
}
@{_deep}.ivu-picker-confirm {
  .ivu-btn-default {
    display: none;
  }
  display: flex;
  justify-content: center;
  align-items: center;
}

.quantitative-trend-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .echarts-container {
    height: calc(100% - 41px);
    width: 100%;
    @{_deep} .echarts {
      height: 100% !important;
    }
  }
  .year-color {
    color: var(--color-label);
  }
  .vt-sub {
    vertical-align: sub;
  }
}
</style>
