<template>
  <span class="i-layout-header-trigger i-layout-header-trigger-min">
    <!-- <author-poptip
      v-if="effectiveDays <= noticeDay"
      :effective-days="effectiveDays"
    ></author-poptip> -->
    <Dropdown class="i-layout-header-user" @on-click="handleClick" transfer>
      <Avatar
        v-if="userInfo && !avatarImgError"
        :src="
          userInfo.avatar ||
          require('@/assets/img/user-center/default_user_avatar.png')
        "
        size="32"
        @on-error="avatarImgErrorHandler"
      />
      <Avatar
        v-else
        :src="require('@/assets/img/user-center/default_user_avatar.png')"
        size="32"
      />
      <span class="i-layout-header-user-name">{{ userInfo.name }}</span>
      <!-- <DropdownMenu
        v-for="item in otherSysApplicationVoList"
        slot="list"
        :key="item.id"
      >
        <i-link
          :to="item.address + '?refresh_token=' + jumpToken"
          target="_blank"
        >
          <DropdownItem>
            <span>{{ item.applicationName }}</span>
          </DropdownItem>
        </i-link>
      </DropdownMenu> -->
      <DropdownMenu slot="list">
        <template v-for="(e, i) in userCenterList2">
          <DropdownItem
            :key="i"
            @click.native="link(e.resourceUrl, e.resourceCname)"
          >
            {{ e.resourceCname }}
          </DropdownItem>
        </template>
        <a href="/product.html">
          <DropdownItem> 工具下载 </DropdownItem>
        </a>
        <DropdownItem @click.native="handleOut"> 退出系统 </DropdownItem>
      </DropdownMenu>
      <!-- <DropdownMenu slot="list">
				<template v-for="(e, i) in userCenterList">
					<DropdownItem :key="i" @click.native="link(e.path)">
						{{ e.name }}
					</DropdownItem>
				</template>
			</DropdownMenu> -->
    </Dropdown>
  </span>
</template>
<script>
import { mapGetters } from "vuex";
import { getToken } from "@/libs/configuration/util.common";
import Setting from "@/libs/configuration/setting";
import { getAuthorNoticeDay } from "@/api/user";
export default {
  name: `iHeaderUser`,
  components: {
    AuthorPoptip: require("./author-poptip").default,
  },
  data() {
    return {
      userCenterList: [
        { name: "个人中心", path: "userCenter" },
        { name: "我的收藏", path: "collect" },
        { name: "下载审核", path: "mydownload" },
      ],
      userCenterList2: this.$store.getters.myMenu,
      effectiveDays: 0,
      noticeDay: 0,
      avatarImgError: false,
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
    ...mapGetters("permission", "myMenu"),
    otherSysApplicationVoList() {
      return this.userInfo.sysApplicationVoList.filter(
        (item) => item.applicationCode !== applicationCode && !!item.address
      );
    },
    jumpToken() {
      return getToken();
    },
  },
  mounted() {
    getAuthorNoticeDay()
      .then((res) => {
        if (res.data) {
          this.noticeDay = res.data.paramValue;
        }
      })
      .catch(() => {})
      .finally(() => {});
    this.effectiveDays = this.$store.getters["common/getAuthorInfo"]
      ? this.$store.getters["common/getAuthorInfo"].effectiveDays
      : 0;
  },
  methods: {
    handleClick(name) {
      if (name === "logout") {
        this.logout({
          confirm: this.logoutConfirm,
          vm: this,
        });
      }
    },
    link(path, name) {
      let url = "";
      if (name == "我的收藏") {
        url = `/user/${path}?dataKey=1&curName=我的收藏`;
      } else {
        url = `/user/${path}`;
      }

      this.$router.push({
        path: url,
      });
      // this.$router.push({
      // 	name: path,
      // });
    },
    handleOut() {
      this.$Modal.confirm({
        title: "友情提示",
        width: 450,
        closable: true,
        content: `确定退出登录吗？`,
        onOk: () => {
          this.confirmLoading = this.$Message.loading({
            content: "退出中...",
            duration: 0,
          });
          this.$store
            .dispatch("handleLogOut")
            .then((res) => {
              this.$router.push("/login");
            })
            .finally(() => {
              this.confirmLoading();
            });
        },
      });
    },
    avatarImgErrorHandler() {
      this.avatarImgError = true;
    },
  },
};
// 就是这么feel 倍爽
</script>
<style lang="less" scoped>
.i-layout-header-trigger {
  display: flex;
}
</style>
