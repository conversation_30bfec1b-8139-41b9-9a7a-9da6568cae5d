<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <statistical-bar
          :statics-list="statisticsList"
          :qualified-val="qualifiedVal"
          :is-equal="true"
          :equal-num="4"
        ></statistical-bar>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :active-index-item="activeIndexItem" :leng-name="lengName" :is-take-detail="true">
          <template #rank-title>
            <span>{{ sortText }}</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart" :active-index-item="activeIndexItem"></line-chart>
    </div>
  </div>
</template>

<script>
import AbnormalListConfig from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AbnormalListConfig';
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'statistical-echarts',
  mixins: [dealWatch],
  props: {
    activeIndexItem: {},
  },
  data() {
    return {
      statisticsList: [],
      qualifiedVal: '1',
      paramsList: {},
      codeKey: '',
      sortText: '按在线率排名',
      lengName: [
        {
          name: '本月报备天数',
          key: 'reportDayNumOfM',
          color: `${$var('--color-orange-5')}`,
        },
        {
          name: '本月离线天数',
          key: 'unOnlineNumOfM',
          color: `${$var('--color-yellow-12')}`,
        },
        {
          name: '本月在线天数',
          key: 'onlineNumOfM',
          color: `${$var('--color-green-8')}`,
        },
      ],
    };
  },
  created() {
    let informationStatistics = AbnormalListConfig.find((item) => item.indexId === this.activeIndexItem.indexId) || {};
    this.statisticsList = informationStatistics.abnormalList || [];
  },
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    async getStatInfo() {
      try {
        const params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          // access: "REPORT_MODE",
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
          // sortField: "ACTUAL_NUM"
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        this.statisticsList = this.statisticsList.map((item) => {
          item[item.key] = data[item.key];
          return item;
        });
        this.qualifiedVal = data.qualified;
      } catch (e) {
        console.log(e);
      }
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.getStatInfo();
    },
  },
  computed: {},
  watch: {},
  components: {
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChart: require('./components/line-chart.vue').default,
    StatisticalBar: require('./components/statistical-bar').default,
  },
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      flex: 1;
      //width: calc(100% - 488px);
    }
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
  @{_deep} .info-statics-list ul {
    li {
      width: calc(calc(100% - 30px) / 4) !important;
      height: 102px;
    }
  }
}
</style>
