<!--
    * @FileDescription: 疑似卖淫人员
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="analyse-wrap">
      <div class="search_box" v-if="!isHideLeft">
        <div class="title">
          <p>疑似卖淫人员</p>
        </div>
        <div class="form-box" v-if="isForm">
          <Form
            ref="form"
            :rules="ruleValidate"
            :model="formData"
            :label-width="80"
          >
            <FormItem label="开始日期:">
              <DatePicker
                style="width: 100%"
                v-model="formData.st"
                type="date"
                format="yyyy-MM-dd"
                placeholder="日期"
                transfer
              ></DatePicker>
            </FormItem>
            <FormItem label="结束日期:">
              <DatePicker
                style="width: 100%"
                v-model="formData.et"
                type="date"
                format="yyyy-MM-dd"
                placeholder="日期"
                transfer
              ></DatePicker>
            </FormItem>
            <FormItem label="出现时段:">
              <div class="flex">
                  <TimePicker
                    v-model="formData.timePeriodSt"
                    format="HH:mm:ss"
                    placeholder="出现时段"
                    transfer
                    :clearable="false"
                  ></TimePicker>
                  <span class="ml-5 mr-5">-</span>
                  <TimePicker
                    v-model="formData.timePeriodEt"
                    format="HH:mm:ss"
                    placeholder="出现时段"
                    transfer
                    :clearable="false"
                  ></TimePicker>
              </div>
            </FormItem>
            <FormItem label="出现区域:">
              <div class="flex">
                <Input v-model="formData.appearRegionCount" placeholder="请输入"></Input>
                <span class="ml-5">个</span>
              </div>
            </FormItem>
            <FormItem label="年龄段:">
              <div class="flex">
                  <span class="mr-5">{{ formData.ageupdown[0] }}</span>
                  <Slider v-model="formData.ageupdown" range :max="60"></Slider>
                  <span>{{ formData.ageupdown[1] }}</span>
              </div>
            </FormItem>
  
            <div class="btn-group">
              <Button type="primary" @click="handleAddTask"
                >创建任务</Button
              >
              <Button type="primary" @click="handleTaskList"
                >查看任务</Button
              >
              <Button type="default" @click="handleReset">重置</Button>
            </div>
          </Form>
        </div>
        <div class="task-list" v-else>
          <p class="result-header">
              <span>
                <Button v-if="!taskParams.taskResult" class="ml-10" type="primary" size="small" @click="goback()">返回</Button>
                <Button class="ml-10" type="primary" size="small" @click="refreshTaskList()">刷新</Button>
              </span>
              <span>共 <span class="t-blue-color">{{taskList.length}}</span> 条任务</span>
          </p>
          <div class="result-list">
              <div v-scroll style="height:100%;">
                <div class="result-item" :class="currentTaskId == item.id ? 'actived': ''" v-for="item in taskList" :key="item.id" @click="handleView(item)">
                  <ul class="result-item-info">
                    <li class="ellipsis">
                        <span class="label">任务名称:</span>
                        <span class="value" :title="item.taskName">
                            {{item.taskName}}
                        </span>
                    </li>
                    <li class="ellipsis">
                        <span class="label">任务状态:</span>
                        <span class="value" :title="item.taskRunState | commonFiltering(taskRunStateList)">
                            {{item.taskRunState | commonFiltering(taskRunStateList)}}
                        </span>
                    </li>
                    <li class="ellipsis">
                        <span class="label">运行时间:</span>
                        <span class="value" :title="item.taskStartTime">
                            {{item.taskStartTime}}
                        </span>
                    </li>
                    <transition name="fade">
                      <div v-if="!!item.isShowDetail">
                        <li class="ellipsis">
                            <span class="label">开始时间:</span>
                            <span class="value" :title="item.taskParam.st">
                                {{item.taskParam.st}}
                            </span>
                        </li>
                        <li class="ellipsis">
                            <span class="label">结束时间:</span>
                            <span class="value" :title="item.taskParam.et">
                                {{item.taskParam.et}}
                            </span>
                        </li>
                        <li class="ellipsis">
                            <span class="label">出现时段:</span>
                            <span class="value">
                                {{item.taskParam.timePeriodSt}} 
                                - {{item.taskParam.timePeriodEt}}
                            </span>
                        </li>
                        <li class="ellipsis">
                            <span class="label">出现区域:</span>
                            <span class="value">
                                {{item.taskParam.appearRegionCount}} 个
                            </span>
                        </li>
                        <li class="ellipsis">
                            <span class="label">年龄段:</span>
                            <span class="value">
                                {{item.taskParam.ageMin}} - {{item.taskParam.ageMax}}
                            </span>
                        </li>
                      </div>
                    </transition>
                  </ul>
                  <div class="operate">
                    <i class="iconfont icon-jiantou" :class="{up: item.isShowDetail}" title="参数详情" @click.stop="toggleItem(item)"></i>
                    <i class="iconfont icon-shanchu1" title="删除" @click.stop="deleteTask(item)"></i>
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
      <div class="table_box">
        <div class="search-bar">
          <searchForm ref="searchForm" @searchInfo="searchInfo"></searchForm>
        </div>
        <div class="table-content">
          <div v-for="(item, index) in dataList" :key="index" class="card-item" :style="{width: isHideLeft ? '20%' : '25%'}">
            <UiListCard type="prostitute" :showBar="false" :data="item" :collectIcon="false" @titleBar="handleTitleBar(item)" @archivesDetailHandle="archivesDetailHandle(item)" />
          </div>
          <ui-empty v-if="dataList.length === 0 && !loading"></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
        </div>
        <!-- 分页 -->
        <ui-page
          :current="pageInfo.pageNumber"
          :total="pageInfo.total" 
          :page-size="pageInfo.pageSize"
          :page-size-opts="[20, 40, 80, 160]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
  
      <ui-modal v-model="captureShow" title="抓拍详情" :r-width="1008">
        <div class="portrait-capture-list">
          <PortraitCaptureCard
            v-for="(item, $index) in captureList"
            :key="$index"
            type="people"
            :data="item"
            @faceDetailFn="faceDetailFn(item, $index)"
          />
          <ui-empty v-if="(!captureList || !captureList.length) && !loading" />
        </div>
        <ui-page
          :current="capturePageInfo.pageNumber"
          :total="capturePageInfo.total"
          countTotal
          :page-size="capturePageInfo.pageSize"
          :page-size-opts="[10, 20, 40, 80]"
          @pageChange="capturePageChange"
          @pageSizeChange="capturePageSizeChange"
        >
        </ui-page>
        <ui-loading v-if="loading" />
      </ui-modal>
  
      <details-face-modal
          v-show="videoShow" 
          ref='faceDetail' 
          @prePage="prePage"
          @nextPage="nextPage" 
          @close="videoShow = false">
      </details-face-modal>
    </div>
  </template>
  <script>
  import { mapActions,mapGetters } from "vuex";
  import { licensePlateColorArray } from '@/libs/system';
  import { addProstituteTask, getProstituteTaskList, delProstituteTask, getProstituteDetail, getProstituteCaptureList } from "@/api/modelMarket";
  import plateNumber from '@/views/wisdom-cloud-search/cloud-default-page/advanced-search/components/plate-number.vue'
  import detailsFaceModal from "@/components/detail/details-face-modal.vue";
  import searchForm from './search-form'
  import UiListCard from '@/components/ui-list-card'
  import PortraitCaptureCard from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/card/portrait-capture-card.vue";
  export default {
    components: {
      plateNumber,
      detailsFaceModal,
      searchForm,
      UiListCard,
      PortraitCaptureCard
    },
    props: {
      taskParams: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        currentIndex: 0,
        videoShow: false,
        formData: {
          st: "",
          et: "",
          timePeriodSt: "18:00:00", //
          timePeriodEt: "23:00:00",
          appearRegionCount: 1,
          ageupdown: [18, 40]
        },
        queryParam: {},
        pageInfo: {
          pageNumber: 1,
          pageSize: 20,
          total: 0
        },
        dataList: [],
        loading: false,
        ruleValidate: {},
        checkedLabels: [], // 已选择的标签
        licensePlateColorArray,
        afterKey: undefined,
        isForm: true,
        taskList: [],
        currentTaskId: null,
        isHideLeft: false,
        captureShow: false,
        captureList: [],
        capturePageInfo: {
          pageNumber: 1,
          pageSize: 10,
          total: 0
        },
        captureParam: {}
      };
    },
    computed: {
      ...mapGetters({
        taskRunStateList: "dictionary/getTaskRunStateList", //任务状态
      }),
    },
    async created() {
        console.log(232424)
      await this.getDictData();
      this.$nextTick(() => {
          this.formData.st = this.$dayjs().subtract(7, 'day').format('YYYY-MM-DD');
          this.formData.et = this.$dayjs(new Date()).format('YYYY-MM-DD');
          // 推荐中心查看
          console.log('taskParams: ', this.taskParams)
          if (!Toolkits.isEmptyObject(this.taskParams)) {
            if (this.taskParams.taskResult) {
              this.isHideLeft = true
              this.currentTaskId = this.taskParams.taskResult.tacticTaskId
              this.$refs.searchForm.search()
            }
          }
      })
    },
    methods: {
      ...mapActions({
        getDictData: "dictionary/getDictAllData",
      }),
      // 创建任务
      handleAddTask() {
        if(this.formData.st > this.formData.et) {
            this.$Message.wraning('结束时间不能大于开始时间！')
            return;
        }
        this.loading = true;
        let params = {
            ...this.formData,
            st: this.$dayjs(this.formData.st).format('YYYY-MM-DD HH:mm:ss'),
            et: this.$dayjs(this.formData.et).format('YYYY-MM-DD HH:mm:ss'),
        };
        params.ageMin = params.ageupdown[0]
        params.ageMax = params.ageupdown[1]
        delete params.ageupdown
        addProstituteTask(params).then(res => {
  
        }).finally(() => {
            this.loading = false;
            this.handleTaskList()
        })
      },
      // 查看任务列表
      handleTaskList() {
        this.isForm = false;
        this.currentTaskId = null
        this.dataList = []
        this.loading = true;
        getProstituteTaskList().then(res => {
          res.data = res.data.map(v => {
            v.taskParam = JSON.parse(v.taskParam)
            return v
          })
          this.taskList = res.data
        }).finally(() => {
            this.loading = false;
        })
      },
      goback() {
        this.isForm = true;
        this.dataList = []
      },
      refreshTaskList() {
        this.handleTaskList()
      },
      deleteTask(item) {
        this.$Modal.confirm({
            title: '提示',
            closable: true,
            content: `确认删除${item.taskName}?`,
            onOk: () => {
                delProstituteTask(item.id).then(res => {
                  this.refreshTaskList()
                })
            }
        })
      },
      handleView(item) {
        this.currentTaskId = item.id
        this.queryParam = {}
        this.dataList = []
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 20,
          total: 0
        }
        this.$refs.searchForm.resetForm()
      },
      getList(isDetailNext) {
        this.loading = true;
        getProstituteDetail({tacticTaskId: this.currentTaskId, ...this.pageInfo, ...this.queryParam}).then(res => {
            this.dataList = res.data.entities;
            this.pageInfo.total = res.data.total
            if (isDetailNext) this.$refs.faceDetail.nextPage(this.dataList);
        })
        .finally(() => {
            this.loading = false;
        })
      },
      searchInfo(obj) {
        if (!this.currentTaskId) {
          this.$Message.error("请先选择需要查询的任务")
          return
        }
        this.pageInfo.pageNumber = 1
        this.queryParam = obj
        this.getList()
      },
      // 重置
      handleReset() {
        this.formData = {
          st: "",
          et: "",
          timePeriodSt: "18:00:00", //18:00:00
          timePeriodEt: "23:00:00",
          appearRegionCount: 1,
          ageupdown: [18, 40]
        }
      },
      pageChange(page) {
        console.log(page, "page");
        this.pageInfo.pageNumber = page
        this.getList();
      },
      pageSizeChange(size) {
        console.log(size, "size");
        this.pageInfo.pageSize = size
        this.getList();
      },
      toggleItem(row) {
        this.$set(row, 'isShowDetail', !row.isShowDetail)
      },
      // 档案详情
      handleTitleBar(item) {
          let query = {
              archiveNo: item.vid,
              source: 'video',
              initialArchiveNo: item.vid,
              noMenu:1
          }
          const { href } = this.$router.resolve({
              name: 'video-archive',
              query
          })
          // 防止因为Anchor锚点导致的路由query参数丢失
          sessionStorage.setItem('query', JSON.stringify(query))
          // window.open(href, '_blank')
          this.$util.openNewPage(href, "_blank");
      },
      archivesDetailHandle(item) {
        this.captureParam = {tacticTaskId: this.currentTaskId, vid: item.vid}
        this.capturePageInfo.pageNumber = 1
        this.getCaptureList()
      },
      getCaptureList(isDetailNext) {
        this.loading = true;
        getProstituteCaptureList({...this.captureParam, ...this.capturePageInfo}).then(res => {
            this.captureList = res.data.entities || []
            this.capturePageInfo.total = res.data.total
            this.captureShow = true
            if (isDetailNext) this.$refs.faceDetail.nextPage(this.captureList);
        })
        .finally(() => {
            this.loading = false;
        })
      },
      capturePageChange(page) {
        console.log(page, "page");
        this.capturePageInfo.pageNumber = page
        this.getCaptureList();
      },
      capturePageSizeChange(size) {
        console.log(size, "size");
        this.capturePageInfo.pageSize = size
        this.getCaptureList();
      },
      prePage(pageNum) {
        this.capturePageInfo.pageNumber = pageNum
        console.log(pageNum, "pageNumber");
        this.getCaptureList(true)
      },
      /**
       * 下一个
       */
      nextPage(pageNum) {
        this.capturePageInfo.pageNumber = pageNum
        console.log(pageNum, "pageNumber");
        this.getCaptureList(true)
      },
      faceDetailFn(row, index) {
        this.currentIndex = index;
        this.videoShow = true;
        this.$nextTick(() => {
          let collectionType = 5;
          this.$refs.faceDetail.init(
            row,
            this.captureList,
            index,
            collectionType,
            this.capturePageInfo.pageNumber
          );
        });
      },
    },
  };
  </script>
  <style lang="less" scoped>
  @import "../../style/index";
  @import "../../style/vehicle";
  /deep/ .ivu-tag-select-option{
      margin-right: 5px !important;
  }
  .flex {
    display: flex;
    align-items: center;
  }
  .page-button {
    text-align: right;
    padding-bottom: 10px;
  }
  .btn-group {
    display: flex;
    justify-content: space-between;
    button {
      flex: 1;
    }
  }
  .task-list {
    height: calc(~'100% - 40px');
    .result-header {
        height: 40px;
        line-height: 40px;
        margin-right: 10px;
        text-align: right;
        // color: #989cad;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .result-list {
      height: calc(~'100% - 40px');
      .result-item {
        display: flex;
        .result-item-info {
          padding: 10px;
          border-bottom: 1px solid #d3d7de;
          cursor: pointer;
          overflow: hidden;
        }
        &:hover {
          background: rgba(44,134,248,0.2);
          .iconfont {
            opacity: 1;
            color: #2C86F8;
          }
        }
        &.actived {
          background: rgba(44,134,248,0.2);
        }
        .operate {
          position: relative;
        }
        .icon-shanchu1 {
          opacity: 0;
          padding: 5px;
          cursor: pointer;
        }
        .icon-jiantou {
          cursor: pointer;
          position: absolute;
          bottom: 5px;
          &.up {
            transform: rotate(180deg);
          }
        }
        .active-area-sele{
            width: 100px;
            height: 22px;
            border-radius: 4px;
            text-align: center;
            line-height: 22px;
            cursor: pointer;
            border: 1px dashed #2C86F8;
            background: rgba(44, 134, 248, 0.10);
            color: rgba(44, 134, 248, 1);
            display: inline-block;
            margin-left: 5px;
        }
      }
    }
  }
  .table-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    padding: 10px;
    .card-item {
      width: 25%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      overflow: hidden;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        overflow: hidden;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
  .portrait-capture-list {
    height: 500px;
    flex: 1;
    display: flex;
    margin: 0 -5px;
    overflow: auto;
    flex-wrap: wrap;
    position: relative;
    align-content: flex-start;
    .portrait-capture-card {
      width: calc(~"20% - 10px");
      margin: 0 5px 10px 5px;
    }
  }
  .dom-wrapper {
    z-index: 2999;
  }
  </style>