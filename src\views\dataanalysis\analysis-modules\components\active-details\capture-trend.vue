<template>
  <!-- 近X日抓拍趋势 -->
  <div class="body-container">
    <div class="statistics-box">
      <span>
        <i class="icon-font icon-rijunzhuapai"></i>
        日均抓拍：
        <span class="color-1">{{ getText(currentRow.avgNum) }}</span>
      </span>
      <span>
        <i class="icon-font icon-fujinshebeiripingzhuapai"></i>
        附近设备日平均抓拍：
        <span class="color-2">{{ getText(currentRow.nearbyAvgNum) }}</span>
      </span>
      <span>
        <i class="icon-font icon-rijunzhanbi"></i>
        日均占比：
        <span class="color-3">{{ getText(currentRow.avgPercent) }}%</span>
      </span>
    </div>
    <draw-echarts
      :echart-style="echartStyle"
      :echart-option="echartOption"
      ref="captureTrendEchart"
      v-ui-loading="{ loading: echartsLoading, tableData: tendencyEchartData || [] }"
    ></draw-echarts>
    <span class="next-echart" v-if="tendencyEchartData.length > echartNum">
      <i class="icon-font icon-zuojiantou1 f-12" @click="changeShowData"></i>
    </span>
  </div>
</template>
<script>
import Vue from 'vue';
import CaptureTrendTooltip from './capture-trend-tooltip.vue';
import dataAnalysis from '@/config/api/dataAnalysis.js';
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'CaptureTrend',
  props: {
    currentRow: {
      type: Object,
      default: () => {},
    },
  },
  mixins: [dataZoom],
  data() {
    return {
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      echartsLoading: false,
      echartOption: {},
      tendencyEchartData: [],
      echartNum: 25,
      xAxisData: [],
      legendList: [
        { name: '当前设备抓拍', key: 'shotNum' },
        { name: '附近设备平均抓拍', key: 'avgByDayNum' },
      ],
      colorList: [$var('--color-blue-9'), $var('--color-warn')],
      tooltipFormatter: (data) => {
        let captureTrendTooltip = Vue.extend(CaptureTrendTooltip);
        let _this = new captureTrendTooltip({
          el: document.createElement('div'),
          data() {
            return {
              trendData: data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  computed: {},
  watch: {
    currentRow: {
      handler() {
        this.initEchartsOption();
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  methods: {
    getText(num) {
      return num || num === 0 ? num : '--';
    },
    changeShowData() {
      this.scrollRight('captureTrendEchart', this.tendencyEchartData, [], this.echartNum);
    },
    // 趋势图 数据
    async initEchartsOption() {
      try {
        this.echartsLoading = true;
        const { batchId } = this.$route.query;
        let data = {
          batchId: batchId,
          customParameters: {
            deviceId: this.currentRow.deviceId || '',
          },
        };
        let res = await this.$http.post(dataAnalysis.getGraphTrendInfo, data);
        this.tendencyEchartData = res.data.data || [];
        await this.handlerData();
        this.setEchartOption();
      } catch (error) {
        console.log(error);
      } finally {
        this.echartsLoading = false;
      }
    },
    /**
     * x轴数据为：配置的抓拍范围，会存在 跨月 或 跨年 的情况， 这种情况下，x轴的 需要显示对应的年份，月份
     */
    handlerData() {
      this.xAxisData = []; // x轴
      this.tendencyEchartData.map((item, index) => {
        let time = new Date(item.date),
          year = time.getFullYear(),
          month = time.getMonth() + 1,
          day = time.getDate();

        let dayVal = `${month}-${this.diffDay(day)}`;
        if (index === 0) {
          this.xAxisData.push({
            value: dayVal,
          });
        } else {
          let preTime = new Date(this.tendencyEchartData[index - 1].date),
            preYear = preTime.getFullYear();
          // preMonth = preTime.getMonth() + 1;

          this.xAxisData.push({
            value: preYear !== year ? `${dayVal}\n{b|(${year}年)}` : `${dayVal}`,
            textStyle: {
              rich: {
                b: {
                  color: $var('--color-axis-lable'),
                  padding: [5, 0, 0, 0],
                },
              },
            },
          });
        }
      });
    },
    diffDay(num) {
      if (num < 10) {
        num = `0${num}`;
      }
      return num;
    },
    // 处理 趋势图的 options
    setEchartOption() {
      let data = this.legendList.map((item, index) => {
        let seriesData = [];
        this.tendencyEchartData.forEach((tendencyItem) => {
          seriesData.push({
            ...tendencyItem,
            value: tendencyItem?.[item.key],
          });
        });
        return {
          name: item.name,
          type: 'line',
          data: seriesData,
          showSymbol: false,
          lineStyle: {
            width: 2,
            color: this.colorList[index % this.colorList.length], //线条颜色
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 1,
            shadowOffsetY: 1,
          },
          itemStyle: {
            color: this.colorList[index % this.colorList.length],
            borderColor: this.colorList[index % this.colorList.length],
            borderWidth: 2,
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 5,
            shadowOffsetX: 0,
            shadowOffsetXY: 0,
          },
        };
      });
      let opts = {
        data: data,
        xAxisData: this.xAxisData,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.echartOption = this.$util.doEcharts.DataAnalysisCaptureTrend(opts);
      setTimeout(() => {
        this.setDataZoom('captureTrendEchart', [], this.echartNum);
      });
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.body-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .echarts {
    height: calc(100% - 30px) !important;
    width: 100% !important;
  }
  .statistics-box {
    color: var(--color-content);
    display: flex;
    align-items: center;
    justify-content: center;
    & > span {
      margin-right: 30px;
    }
    .color-1 {
      color: #48baff;
    }
    .color-2 {
      color: #a786ff;
    }
    .color-3 {
      color: #1faf8a;
    }
  }
}
</style>
