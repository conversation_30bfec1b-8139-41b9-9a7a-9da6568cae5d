export const iconStaticsList = {
  Published_Stability: [
    {
      name: '本月检测总次数:',
      count: '0',
      countStyle: {
        color: 'var(--color-bluish-green-text)',
      },
      iconName: 'icon-yingjianceshebeishuliang',
      fileName: 'baDetectionShouldCount',
    },
    {
      name: '检测合格次数:',
      count: '0',
      countStyle: {
        color: '#269F26',
      },
      iconName: 'icon-jiancehegeshebeishu',
      fileName: 'baDetectionQualifiedCount',
    },
    {
      name: '检测不合格次数:',
      count: '0',
      countStyle: {
        color: '#269F26',
      },
      iconName: 'icon-jiancebuhegeshebeishu',
      fileName: 'baDetectionUnQualifiedCount',
    },
    {
      name: '分布式身份确认接口稳定性:',
      count: '0',
      countStyle: { color: 'var(--color-bluish-green-text)' },
      style: { color: 'var(--color-bluish-green-text)' },
      iconName: 'icon-tianbaozhunqueshuai',
      fileName: 'resultValue',
      type: 'percent', // 百分比
    },
  ],
  Portrait_Trajectory_Stability: [
    {
      name: '本月检测总次数:',
      count: '0',
      countStyle: {
        color: 'var(--color-bluish-green-text)',
      },
      iconName: 'icon-yingjianceshebeishuliang',
      fileName: 'baDetectionShouldCount',
    },
    {
      name: '检测合格次数:',
      count: '0',
      countStyle: {
        color: '#269F26',
      },
      iconName: 'icon-jiancehegeshebeishu',
      fileName: 'baDetectionQualifiedCount',
    },
    {
      name: '检测不合格次数:',
      count: '0',
      countStyle: {
        color: '#269F26',
      },
      iconName: 'icon-jiancebuhegeshebeishu',
      fileName: 'baDetectionUnQualifiedCount',
    },
    {
      name: '人像轨迹查询接口稳定性:',
      count: '0',
      countStyle: { color: 'var(--color-bluish-green-text)' },
      style: { color: 'var(--color-bluish-green-text)' },
      iconName: 'icon-tianbaozhunqueshuai',
      fileName: 'resultValue',
    },
  ],
  Car_Trajectory_Stability: [
    {
      name: '本月检测总次数:',
      count: '0',
      countStyle: {
        color: 'var(--color-bluish-green-text)',
      },
      iconName: 'icon-yingjianceshebeishuliang',
      fileName: 'baDetectionShouldCount',
    },
    {
      name: '检测合格次数:',
      count: '0',
      countStyle: {
        color: '#269F26',
      },
      iconName: 'icon-jiancehegeshebeishu',
      fileName: 'baDetectionQualifiedCount',
    },
    {
      name: '检测不合格次数:',
      count: '0',
      countStyle: {
        color: '#269F26',
      },
      iconName: 'icon-jiancebuhegeshebeishu',
      fileName: 'baDetectionUnQualifiedCount',
    },
    {
      name: '车辆轨迹查询接口稳定性:',
      count: '0',
      countStyle: { color: 'var(--color-bluish-green-text)' },
      style: { color: 'var(--color-bluish-green-text)' },
      iconName: 'icon-tianbaozhunqueshuai',
      fileName: 'resultValue',
    },
  ],
};

export const tableColumn = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: '行政区划',
    key: 'civilName',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '行政区划代码',
    key: 'civilCode',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '接口名称',
    key: '',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '检测时间',
    key: '',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '使用图片',
    key: '',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '返回结果',
    key: '',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '检测结果',
    slot: '',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '不合格原因',
    slot: '',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
];

export const formItemData = [
  {
    type: 'select',
    key: 'key1',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: [
      { value: '1', label: '合格' },
      { value: '2', label: '不合格' },
    ],
  },
  {
    type: 'select',
    key: 'kew2',
    label: '不合格原因',
    placeholder: '请选择不合格原因',
    options: [
      { value: '1', label: '设备无抓拍图片' },
      { value: '2', label: '合格图片占比不达标' },
      { value: '3', label: '其他' },
    ],
  },
];
