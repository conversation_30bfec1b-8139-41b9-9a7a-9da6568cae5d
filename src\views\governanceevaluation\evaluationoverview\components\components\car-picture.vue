<template>
  <ui-modal class="look-pictute" v-model="visible" v-if="visible" :title="title" :styles="styles" :footer-hide="true">
    <div class="content auto-fill">
      <div class="table-data auto-fill">
        <div class="fail-picture" v-ui-loading="{ loading: reasonLoading, tableData: tableData }">
          <div class="fail-picture-container" v-for="(item, index) in tableData" :key="index">
            <div class="fail-picture-item" @click="lookScence(index)">
              <ui-image :src="item.smallImagePath" />
            </div>
            <div class="fail-picture-date">
              <span class="icon-font icon-shijian f-12 mr-xs vt-middle"></span>
              <Tooltip class="text vt-middle" placement="bottom" :content="item.shotTime || '未知'">
                <div class="vt-middle ellipsis">
                  {{ item.shotTime || '未知' }}
                </div>
              </Tooltip>
            </div>
            <div class="fail-picture-address">
              <span class="icon-font icon-dizhi f-12 mr-xs vt-middle"></span>
              <Tooltip class="text vt-middle" placement="bottom" :content="item.address || '未知'">
                <div class="checkbox-item ellipsis">
                  {{ item.address || '未知' }}
                </div>
              </Tooltip>
            </div>
          </div>
        </div>

        <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
      </div>
      <ui-page class="page" :page-data="reasonPage" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
  .ivu-modal-body {
    padding: 0 20px;
  }
}
.look-pictute {
  .content {
    height: 700px;
    .table-data {
      height: 100%;
      .fail-picture {
        display: flex;
        flex-wrap: wrap;
        height: 100%;
        overflow-y: auto;
        .fail-picture-container {
          height: 236px;
          width: 191px;
          background: #0f2f59;
          color: #8797ac;
          padding: 15px;
          margin-right: 13px;
          margin-bottom: 15px;
          &:nth-child(7n) {
            margin-right: 0px;
          }
          .fail-picture-item {
            height: 162px;
            width: 162px;
            margin-bottom: 10px;
            cursor: pointer;
          }
          .fail-picture-date {
            line-height: 18px;
            font-size: 12px;
            width: 160px;
          }
          .fail-picture-address {
            line-height: 18px;
            font-size: 12px;
            width: 160px;
            .result-icon {
              font-size: 20px;
            }
            .text {
              width: 122px;
              display: inline-block;
              cursor: pointer;
            }
          }
        }
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .img-text {
          color: #385074;
        }
      }
    }
  }
}
.text {
  width: 122px;
  display: inline-block;
  cursor: pointer;
}
.no-box {
  width: 1726px;
  min-height: 860px;
  max-height: 860px;
}
</style>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    reasonLoading: {
      type: Boolean,
      default: false,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    reasonPage: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      styles: { width: '7.7rem' },
      visible: true,
      loading: false,
    };
  },
  mounted() {},

  methods: {
    changePage(val) {
      this.$emit('handlePageChange', val);
    },
    changePageSize(val) {
      this.$emit('handlePageSizeChange', val);
    },
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {
    tableData: {
      handler() {
        this.imgList = this.tableData.map((row) => row.bigImagePath);
      },
      immediate: true,
      deep: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {},
};
</script>
