import axios from 'axios';

import dataAnalysis from '@/config/api/dataAnalysis';
export default {
  namespaced: true,
  state: {
    factorList: [], //分析内容列表
    influenceList: [], // 影响因素列表
  },

  getters: {
    getFactorList(state) {
      return state.factorList;
    },
    getInfluenceList(state) {
      return state.influenceList;
    },
  },

  mutations: {
    setFactorList(state, data) {
      state.factorList = data;
    },
    setInfluenceList(state, data) {
      state.influenceList = data;
    },
  },
  actions: {
    // 分析内容
    async getFactorInfo({ commit }) {
      try {
        let res = await axios.get(dataAnalysis.getFactorList);
        commit('setFactorList', res.data.data || []);
      } catch (error) {
        console.log(error);
      }
    },
    // 影响因素
    async getInfluenceInfo({ commit }, data) {
      try {
        let res = await axios.get(`${dataAnalysis.getDictData}?typeKey=${data ? data.typeKey : ''}`);
        commit('setInfluenceList', res.data.data || []);
      } catch (error) {
        console.log(error);
      }
    },
  },
};
