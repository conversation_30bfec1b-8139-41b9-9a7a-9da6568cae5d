<template>
  <ui-modal v-model="visible" title="信息自动同步" width="54" @onCancel="handleReset">
    <div class="tab">
      <div class="table-header">
        <table>
          <tr>
            <td>设备信息</td>
            <RadioGroup v-model="choseNumber" @on-change="changes">
              <td><Radio label="1" title="原始值">原始值</Radio></td>
              <td>
                <Radio label="2" title="国标平台获取值">国标平台获取值</Radio>
              </td>
              <td><Radio label="3" title="直连SDK取值">直连SDK取值</Radio></td>
              <td>
                <Radio label="4" title="视频流检测结果">视频流检测结果</Radio>
              </td>
            </RadioGroup>
          </tr>
        </table>
      </div>
      <div class="table-body">
        <table v-if="this.msg.length != 0">
          <!-- <tr>
          <td>设备信息</td>
          <RadioGroup v-model="choseNumber" @on-change="changes">
            <td><Radio label="1" title="原始值">原始值</Radio></td>
            <td><Radio label="2" title="国标平台获取值">国标平台获取值</Radio></td>
            <td><Radio label="3" title="直连SDK取值">直连SDK取值</Radio></td>
            <td><Radio label="4" title="视频流检测结果">视频流检测结果</Radio></td>
          </RadioGroup>
        </tr> -->
          <tr>
            <td>行政区划</td>
            <RadioGroup v-model="civicode" @on-change="changeCivicode">
              <td>
                <Radio label="1">{{ this.msg.civicode.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2">{{ this.msg.civicode.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3">{{ this.msg.civicode.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4">{{ this.msg.civicode.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>IPV4地址</td>
            <RadioGroup v-model="ipAddr" @on-change="changeIpAddr">
              <td>
                <Radio label="1" :title="msg.ipAddr.orginal">{{ this.msg.ipAddr.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2" :title="msg.ipAddr.GBPlatform">{{ this.msg.ipAddr.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3" :title="msg.ipAddr.SDK">{{ this.msg.ipAddr.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4" :title="msg.ipAddr.CHECK">{{ this.msg.ipAddr.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>设备规格型号</td>
            <RadioGroup v-model="model" @on-change="changeModel">
              <td>
                <Radio label="1" :title="msg.model.orginal">{{ this.msg.model.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2" :title="msg.model.GBPlatform">{{ this.msg.model.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3" :title="msg.model.SDK">{{ this.msg.model.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4" :title="msg.model.CHECK">{{ this.msg.model.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>设备厂商名称</td>
            <RadioGroup v-model="manfacturer" @on-change="changeManfacturer">
              <td>
                <Radio label="1">{{ this.msg.manfacturer.orginal | manufacturer(that) }}</Radio>
              </td>
              <td>
                <Radio label="2">{{ this.msg.manfacturer.GBPlatform | manufacturer(that) }}</Radio>
              </td>
              <td>
                <Radio label="3">{{ this.msg.manfacturer.SDK | manufacturer(that) }}</Radio>
              </td>
              <td>
                <Radio label="4">{{ this.msg.manfacturer.CHECK | manufacturer(that) }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>经度</td>
            <RadioGroup v-model="lng" @on-change="changeLng">
              <td>
                <Radio label="1" :title="msg.lng.orginal">{{ this.msg.lng.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2" :title="msg.lng.GBPlatform">{{ this.msg.lng.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3" :title="msg.lng.SDK">{{ this.msg.lng.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4" :title="msg.lng.CHECK">{{ this.msg.lng.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>纬度</td>
            <RadioGroup v-model="lat" @on-change="changeLat">
              <td>
                <Radio label="1" :title="msg.lat.orginal">{{ this.msg.lat.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2" :title="msg.lat.GBPlatform">{{ this.msg.lat.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3" :title="msg.lat.SDK">{{ this.msg.lat.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4" :title="msg.lat.CHECK">{{ this.msg.lat.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>设备是否在线</td>
            <RadioGroup v-model="isOnline" @on-change="changeIsOnline">
              <td>
                <Radio label="1">{{ this.msg.isOnline.orginal | status }}</Radio>
              </td>
              <td>
                <Radio label="2">{{ this.msg.isOnline.GBPlatform | status }}</Radio>
              </td>
              <td>
                <Radio label="3">{{ this.msg.isOnline.SDK | status }}</Radio>
              </td>
              <td>
                <Radio label="4">{{ this.msg.isOnline.CHECK | status }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>{{ `${global.filedEnum.macAddr}` }}</td>
            <RadioGroup v-model="macAddr" @on-change="changeMacAddr">
              <td>
                <Radio label="1" :title="msg.macAddr.orginal">{{ this.msg.macAddr.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2" :title="msg.macAddr.GBPlatform">{{ this.msg.macAddr.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3" :title="msg.macAddr.SDK">{{ this.msg.macAddr.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4" :title="msg.macAddr.CHECK">{{ this.msg.macAddr.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>设备软件版本</td>
            <RadioGroup v-model="version" @on-change="changeVersion">
              <td>
                <Radio label="1" :title="msg.version.orginal">{{ this.msg.version.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2" :title="msg.version.GBPlatform">{{ this.msg.version.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3" :title="msg.version.SDK">{{ this.msg.version.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4" :title="msg.version.CHECK">{{ this.msg.version.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>视频流主码流编码格式</td>
            <RadioGroup v-model="mainCodeStream" @on-change="changeMainCodeStream">
              <td>
                <Radio label="1">{{ this.msg.mainCodeStream.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2">{{ this.msg.mainCodeStream.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3">{{ this.msg.mainCodeStream.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4">{{ this.msg.mainCodeStream.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>视频流子码流编码格式</td>
            <RadioGroup v-model="childCodeStream" @on-change="changeChildCodeStream">
              <td>
                <Radio label="1">{{ this.msg.childCodeStream.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2">{{ this.msg.childCodeStream.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3">{{ this.msg.childCodeStream.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4">{{ this.msg.childCodeStream.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
          <tr>
            <td>摄像机支持的分辨率</td>
            <RadioGroup v-model="resolution" @on-change="changeResolution">
              <td>
                <Radio label="1">{{ this.msg.resolution.orginal }}</Radio>
              </td>
              <td>
                <Radio label="2">{{ this.msg.resolution.GBPlatform }}</Radio>
              </td>
              <td>
                <Radio label="3">{{ this.msg.resolution.SDK }}</Radio>
              </td>
              <td>
                <Radio label="4">{{ this.msg.resolution.CHECK }}</Radio>
              </td>
            </RadioGroup>
          </tr>
        </table>
      </div>
      <loading v-if="loading"></loading>
    </div>
    <template #footer>
      <Button type="primary" @click="query" class="plr-30">确 定</Button>
      <Button type="default" @click="cancel" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>
<script>
import algorithm from '@/config/api/algorithm';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    // 请求后端方法
    saveFun: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      visible: false,
      single: false,
      choseNumber: '',
      facturer: [],
      that: this,
      civicode: '',
      ipAddr: '',
      model: '',
      manfacturer: '',
      lng: '',
      lat: '',
      isOnline: '',
      macAddr: '',
      version: '',
      mainCodeStream: '',
      childCodeStream: '',
      resolution: '',
      msg: [],
      form: {
        civicode: '',
        ipAddr: '',
        model: '',
        manfacturer: '',
        lng: '',
        lat: '',
        isOnline: '',
        macAddr: '',
        softVersion: '',
        videoCodingM: '',
        videoCodingS: '',
        resolution: '',
        deviceId: '',
      },
      loading: false,
    };
  },
  created() {
    this.$http.get(algorithm.dictData + 'manufacturer').then((res) => {
      this.facturer = res.data.data;
    });
  },
  filters: {
    status(value) {
      if (!value) return '';
      // if (value == '0') {
      //   return '离线'
      // }
      if (value == '1') {
        return '在线';
      }
      if (value == '2') {
        return '离线';
      }
    },
    manufacturer(value, that) {
      if (!value) return '';
      for (let i of that.facturer) {
        if (value == i.dataKey) {
          return i.dataValue;
        }
      }
    },
  },
  methods: {
    handleReset() {},
    reset() {
      (this.choseNumber = ''),
        (this.civicode = ''),
        (this.ipAddr = ''),
        (this.model = ''),
        (this.manfacturer = ''),
        (this.lng = ''),
        (this.lat = ''),
        (this.isOnline = ''),
        (this.macAddr = ''),
        (this.softVersion = ''),
        (this.videoCodingM = ''),
        (this.childCodeStream = ''),
        (this.resolution = '');
      (this.civicode = ''),
        (this.ipAddr = ''),
        (this.model = ''),
        (this.manfacturer = ''),
        (this.lng = ''),
        (this.lat = ''),
        (this.isOnline = ''),
        (this.macAddr = ''),
        (this.version = ''),
        (this.mainCodeStream = ''),
        (this.childCodeStream = ''),
        (this.resolution = '');
    },
    init(row) {
      this.reset();
      this.form.deviceId = row.deviceId;
      this.visible = true;
      this.loading = true;
      this.$http
        .get(equipmentassets.showDeviceSyncInfo, {
          params: { device_id: row.deviceId },
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.loading = false;
            this.msg = res.data.data;
            if (this.msg.civicode.orginal) {
              this.civicode = '1';
              this.form.civicode = this.msg.civicode.orginal;
            }
            if (this.msg.ipAddr.orginal) {
              this.ipAddr = '1';
              this.form.ipAddr = this.msg.ipAddr.orginal;
            }
            if (this.msg.model.orginal) {
              this.model = '1';
              this.form.model = this.msg.model.orginal;
            }
            if (this.msg.manfacturer.orginal) {
              this.manfacturer = '1';
              this.form.manfacturer = this.msg.manfacturer.orginal;
            }
            if (this.msg.lng.orginal) {
              this.lng = '1';
              this.form.lng = this.msg.lng.orginal;
            }
            if (this.msg.lat.orginal) {
              this.lat = '1';
              this.form.lat = this.msg.lat.orginal;
            }
            if (this.msg.isOnline.orginal) {
              this.isOnline = '1';
              this.form.isOnline = this.msg.isOnline.orginal;
            }
            if (this.msg.macAddr.orginal) {
              this.macAddr = '1';
              this.form.macAddr = this.msg.macAddr.orginal;
            }
            if (this.msg.version.orginal) {
              this.version = '1';
              this.form.softVersion = this.msg.version.orginal;
            }
            if (this.msg.mainCodeStream.orginal) {
              this.mainCodeStream = '1';
              this.form.videoCodingM = this.msg.mainCodeStream.orginal;
            }
            if (this.msg.childCodeStream.orginal) {
              this.childCodeStream = '1';
              this.form.videoCodingS = this.msg.childCodeStream.orginal;
            }
            if (this.msg.resolution.orginal) {
              this.resolution = '1';
              this.form.resolution = this.msg.resolution.orginal;
            }
            //设备规格型号原始值为空且SDK有值
            if (!this.msg.model.orginal && this.msg.model.SDK) {
              this.isOnline = '3';
              this.form.model = this.msg.model.SDK;
            }
            //在线状态原始值为空且视频检测有值时
            if (!this.msg.isOnline.orginal && this.msg.isOnline.CHECK) {
              this.isOnline = '4';
              this.form.isOnline = this.msg.isOnline.CHECK;
            }
          }
        });
    },
    async query() {
      try {
        if (this.saveFun) {
          await this.saveFun(this.form);
        } else {
          const res = await this.$http.post(equipmentassets.updateDeviceInfo_str, this.form);
          this.$Message.success(res.data.data);
        }
        this.visible = false;
      } catch (err) {
        console.log(err);
      }
    },
    cancel() {
      this.visible = false;
    },
    //全选
    changes(val) {
      //原始值
      if (val == '1') {
        (this.civicode = '1'),
          (this.ipAddr = '1'),
          (this.model = '1'),
          (this.manfacturer = '1'),
          (this.lng = '1'),
          (this.lat = '1'),
          (this.isOnline = '1'),
          (this.macAddr = '1'),
          (this.version = '1'),
          (this.mainCodeStream = '1'),
          (this.childCodeStream = '1'),
          (this.resolution = '1');

        this.form.civicode = this.msg.civicode.orginal;
        this.form.ipAddr = this.msg.ipAddr.orginal;
        this.form.model = this.msg.model.orginal;
        this.form.manfacturer = this.msg.manfacturer.orginal;
        this.form.lng = this.msg.lng.orginal;
        this.form.lat = this.msg.lat.orginal;
        this.form.isOnline = this.msg.isOnline.orginal;
        this.form.macAddr = this.msg.macAddr.orginal;
        this.form.softVersion = this.msg.version.orginal;
        this.form.videoCodingM = this.msg.mainCodeStream.orginal;
        this.form.videoCodingS = this.msg.childCodeStream.orginal;
        this.form.resolution = this.msg.resolution.orginal;
      }
      if (val == '2') {
        (this.civicode = '2'),
          (this.ipAddr = '2'),
          (this.model = '2'),
          (this.manfacturer = '2'),
          (this.lng = '2'),
          (this.lat = '2'),
          (this.isOnline = '2'),
          (this.macAddr = '2'),
          (this.version = '2'),
          (this.mainCodeStream = '2'),
          (this.childCodeStream = '2'),
          (this.resolution = '2');

        this.form.civicode = this.msg.civicode.GBPlatform;
        this.form.ipAddr = this.msg.ipAddr.GBPlatform;
        this.form.model = this.msg.model.GBPlatform;
        this.form.manfacturer = this.msg.manfacturer.GBPlatform;
        this.form.lng = this.msg.lng.GBPlatform;
        this.form.lat = this.msg.lat.GBPlatform;
        this.form.isOnline = this.msg.isOnline.GBPlatform;
        this.form.macAddr = this.msg.macAddr.GBPlatform;
        this.form.softVersion = this.msg.version.GBPlatform;
        this.form.videoCodingM = this.msg.mainCodeStream.GBPlatform;
        this.form.videoCodingS = this.msg.childCodeStream.GBPlatform;
        this.form.resolution = this.msg.resolution.GBPlatform;
      }
      if (val == '3') {
        (this.civicode = '3'),
          (this.ipAddr = '3'),
          (this.model = '3'),
          (this.manfacturer = '3'),
          (this.lng = '3'),
          (this.lat = '3'),
          (this.isOnline = '3'),
          (this.macAddr = '3'),
          (this.version = '3'),
          (this.mainCodeStream = '3'),
          (this.childCodeStream = '3'),
          (this.resolution = '3');

        this.form.civicode = this.msg.civicode.SDK;
        this.form.ipAddr = this.msg.ipAddr.SDK;
        this.form.model = this.msg.model.SDK;
        this.form.manfacturer = this.msg.manfacturer.SDK;
        this.form.lng = this.msg.lng.SDK;
        this.form.lat = this.msg.lat.SDK;
        this.form.isOnline = this.msg.isOnline.SDK;
        this.form.macAddr = this.msg.macAddr.SDK;
        this.form.softVersion = this.msg.version.SDK;
        this.form.videoCodingM = this.msg.mainCodeStream.SDK;
        this.form.videoCodingS = this.msg.childCodeStream.SDK;
        this.form.resolution = this.msg.resolution.SDK;
      }
      if (val == '4') {
        (this.civicode = '4'),
          (this.ipAddr = '4'),
          (this.model = '4'),
          (this.manfacturer = '4'),
          (this.lng = '4'),
          (this.lat = '4'),
          (this.isOnline = '4'),
          (this.macAddr = '4'),
          (this.version = '4'),
          (this.mainCodeStream = '4'),
          (this.childCodeStream = '4'),
          (this.resolution = '4');

        this.form.civicode = this.msg.civicode.CHECK;
        this.form.ipAddr = this.msg.ipAddr.CHECK;
        this.form.model = this.msg.model.CHECK;
        this.form.manfacturer = this.msg.manfacturer.CHECK;
        this.form.lng = this.msg.lng.CHECK;
        this.form.lat = this.msg.lat.CHECK;
        this.form.isOnline = this.msg.isOnline.CHECK;
        this.form.macAddr = this.msg.macAddr.CHECK;
        this.form.softVersion = this.msg.version.CHECK;
        this.form.videoCodingM = this.msg.mainCodeStream.CHECK;
        this.form.videoCodingS = this.msg.childCodeStream.CHECK;
        this.form.resolution = this.msg.resolution.CHECK;
      }
    },
    //行政区划
    changeCivicode(val) {
      if (val == '1') {
        this.form.civicode = this.msg.civicode.orginal;
      }
      if (val == '2') {
        this.form.civicode = this.msg.civicode.GBPlatform;
      }
      if (val == '3') {
        this.form.civicode = this.msg.civicode.SDK;
      }
      if (val == '4') {
        this.form.civicode = this.msg.civicode.CHECK;
      }
    },
    //ip
    changeIpAddr(val) {
      if (val == '1') {
        this.form.ipAddr = this.msg.ipAddr.orginal;
      }
      if (val == '2') {
        this.form.ipAddr = this.msg.ipAddr.GBPlatform;
      }
      if (val == '3') {
        this.form.ipAddr = this.msg.ipAddr.SDK;
      }
      if (val == '4') {
        this.form.ipAddr = this.msg.ipAddr.CHECK;
      }
    },
    //设备规格型号
    changeModel(val) {
      if (val == '1') {
        this.form.model = this.msg.model.orginal;
      }
      if (val == '2') {
        this.form.model = this.msg.model.GBPlatform;
      }
      if (val == '3') {
        this.form.model = this.msg.model.SDK;
      }
      if (val == '4') {
        this.form.model = this.msg.model.CHECK;
      }
    },
    //设备厂商名称
    changeManfacturer(val) {
      if (val == '1') {
        this.form.manfacturer = this.msg.manfacturer.orginal;
      }
      if (val == '2') {
        this.form.manfacturer = this.msg.manfacturer.GBPlatform;
      }
      if (val == '3') {
        this.form.manfacturer = this.msg.manfacturer.SDK;
      }
      if (val == '4') {
        this.form.manfacturer = this.msg.manfacturer.CHECK;
      }
    },
    //经度
    changeLng(val) {
      if (val == '1') {
        this.form.lng = this.msg.lng.orginal;
      }
      if (val == '2') {
        this.form.lng = this.msg.lng.GBPlatform;
      }
      if (val == '3') {
        this.form.lng = this.msg.lng.SDK;
      }
      if (val == '4') {
        this.form.lng = this.msg.lng.CHECK;
      }
    },
    //纬度
    changeLat(val) {
      if (val == '1') {
        this.form.lat = this.msg.lat.orginal;
      }
      if (val == '2') {
        this.form.lat = this.msg.lat.GBPlatform;
      }
      if (val == '3') {
        this.form.lat = this.msg.lat.SDK;
      }
      if (val == '4') {
        this.form.lat = this.msg.lat.CHECK;
      }
    },
    //设备是否在线状态
    changeIsOnline(val) {
      if (val == '1') {
        this.form.isOnline = this.msg.isOnline.orginal;
      }
      if (val == '2') {
        this.form.isOnline = this.msg.isOnline.GBPlatform;
      }
      if (val == '3') {
        this.form.isOnline = this.msg.isOnline.SDK;
      }
      if (val == '4') {
        this.form.isOnline = this.msg.isOnline.CHECK;
      }
    },
    //mac地址
    changeMacAddr(val) {
      if (val == '1') {
        this.form.macAddr = this.msg.macAddr.orginal;
      }
      if (val == '2') {
        this.form.macAddr = this.msg.macAddr.GBPlatform;
      }
      if (val == '3') {
        this.form.macAddr = this.msg.macAddr.SDK;
      }
      if (val == '4') {
        this.form.macAddr = this.msg.macAddr.CHECK;
      }
    },
    //设备软件版本
    changeVersion(val) {
      if (val == '1') {
        this.form.softVersion = this.msg.version.orginal;
      }
      if (val == '2') {
        this.form.softVersion = this.msg.version.GBPlatform;
      }
      if (val == '3') {
        this.form.softVersion = this.msg.version.SDK;
      }
      if (val == '4') {
        this.form.softVersion = this.msg.version.CHECK;
      }
    },
    //视频流主码流编码格式
    changeMainCodeStream(val) {
      if (val == '1') {
        this.form.videoCodingM = this.msg.mainCodeStream.orginal;
      }
      if (val == '2') {
        this.form.videoCodingM = this.msg.mainCodeStream.GBPlatform;
      }
      if (val == '3') {
        this.form.videoCodingM = this.msg.mainCodeStream.SDK;
      }
      if (val == '4') {
        this.form.videoCodingM = this.msg.mainCodeStream.CHECK;
      }
    },
    //视频流子码流编码格式
    changeChildCodeStream(val) {
      if (val == '1') {
        this.form.videoCodingS = this.msg.childCodeStream.orginal;
      }
      if (val == '2') {
        this.form.videoCodingS = this.msg.childCodeStream.GBPlatform;
      }
      if (val == '3') {
        this.form.videoCodingS = this.msg.childCodeStream.SDK;
      }
      if (val == '4') {
        this.form.videoCodingS = this.msg.childCodeStream.CHECK;
      }
    },
    //摄像机支持的分辨率
    changeResolution(val) {
      if (val == '1') {
        this.form.resolution = this.msg.resolution.orginal;
      }
      if (val == '2') {
        this.form.resolution = this.msg.resolution.GBPlatform;
      }
      if (val == '3') {
        this.form.resolution = this.msg.resolution.SDK;
      }
      if (val == '4') {
        this.form.resolution = this.msg.resolution.CHECK;
      }
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
.tab {
  display: inline-block;
  padding: 8px;
  border: 1px solid var(--border-color);

  .table-header {
    tr:first-child {
      background-color: var(--bg-table-header-th);
    }
  }
  .table-body {
    height: 460px;
    overflow-y: auto;
  }
  table {
    color: var(--color-content);
    tr {
      td {
        padding: 14px 25px;
        display: inline-block;
        width: 170px;
        // border: 1px solid #000;
      }
      td:nth-child(2) {
        width: 260px;
      }
      .ivu-radio-group {
        td:nth-child(1) {
          width: 200px;
        }
      }
    }
    tr:nth-child(2n) {
      background-color: var(--bg-table-body-td);
    }
    tr:nth-child(2n + 1) {
      background-color: var(--bg-table-body-td-2n);
    }
  }
  .ivu-radio-wrapper {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .ivu-checkbox {
    margin-right: 2px;
  }
}
</style>
