<!--
    * @FileDescription: 大屏 card
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="card">
    <div class="card-head">
      <p v-if="title" class="title linear-gradient-color">
        <span>{{ title }}</span>
      </p>
      <slot v-else name="title"></slot>
      <slot name="content"></slot>
      <slot name="extra"></slot>
    </div>
    <div :style="{ padding: paddingValue }" class="card-content">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 卡片标题
    title: {
      type: String,
      default: "",
    },
    // 卡片内部间距，单位 px
    padding: {
      type: [Number, String],
      default: 10,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
    // 展示更多内容
    show: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    paddingValue() {
      if (this.padding.constructor === Number) {
        return this.padding / 192 + "rem";
      } else {
        let padding = this.padding.split(",");
        return padding[0] / 192 + "rem" + " " + padding[1] / 192 + "rem";
      }
    },
  },
};
</script>
<style lang="less" scoped>
.card {
  background: url("~@/assets/img/screen/box-btm.png") no-repeat,
    rgba(1, 9, 34, 0.3);
  background-position: bottom left;
  background-size: auto;
  position: relative;
  .card-head {
    height: 35px;
    background: url("~@/assets/img/screen/titlebg.png") no-repeat;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      display: inline-block;
      font-size: 20px;
      color: #ffffff;
      margin-left: 34px;
      line-height: 35px;
      span {
        font-family: "PangMenZhengDao";
        text-transform: none;
        letter-spacing: 1px;
        color: #ffffff;
        font-style: normal;
        font-weight: 400;
        text-shadow: 0px 1px 3px rgba(2, 20, 63, 0.1);
        background: linear-gradient(90deg, #ffffff 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
  .card-content {
    padding: 10px 10px 0 10px;
    height: calc(~"100% - 35px");
  }
}
</style>
