<!--
    * @FileDescription: 目标管控地图
    * @Author: H
    * @Date: 2023/6/25
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="map-boxs">
        <div :id="mapId" class="map"></div>
    </div>
</template>

<script>
import { NPGisMapMain } from '@/map/map.main';
import { mapGetters, mapActions } from 'vuex';
let mapMain = null
const infoWindowArr = []
export default {
    name: '',
    props: {
        // 档案-轨迹
        positionPoints: {
            type: Array,
            default: () => []
        },
        // 地图图层配置信息
        mapLayerConfig: {
            type: Object,
            default: () => {
                return {
                    tracing: false, // 是否需要刻画轨迹
                    showStartPoint: false, // 是否显示起点终点图标
                    mapToolVisible: false, // 框选操作栏
                    selectionResult: true, // 是否显示框选结果弹框
                    resultOrderIndex: false, // 搜索结果排序,
                    showLatestLocation: false // 显示地图最新位置
                }
            }
        },
        // 是否禁止地图的滚动条事件
        disableScroll: {
            type: Boolean,
            default: false
        },
    },
    components:{
            
    },
    data () {
        return {
            mapId: 'mapId' + Math.random(),
            markers: [], // 运动轨迹图层
            traceLayer: null, // 运动轨迹
            dotimg:null, //列表选择撒点
        }
    },
    watch:{
        positionPoints: {
            handler (newVal) {
                if (newVal.length && !this.fightModel) {
                    newVal.map((item, index) => {
                        item.Index = index+ 1
                    });
                    this.points = [...newVal];
                    this.closeAllInfoWindow() //关闭弹窗
                    this.resetMarker() //重置
                    this.sprinkleHandler(this.points) //撒点
                    setTimeout(() => {
                        this.rePosition(this.points)
                    }, 200)
                } else {
                    this.points = [];
                    this.closeAllInfoWindow() //关闭弹窗
                    this.resetMarker() //重置
                }
            },
            immediate: true
        },    
    },
    computed:{
        ...mapGetters({
            mapConfig: 'common/getMapConfig',
            mapStyle: 'common/getMapStyle',
            mapObj: 'systemParam/mapObj',
            globalObj: 'systemParam/globalObj',
        })   
    },
    created() {
            
    },
    async mounted(){
        await this.getMapConfig()
        this.loading = false;
        if(!this.idlerWheel){
            this.mapidlerWheel(); //防止地图与滚动条 滚轮冲突
        }     
    },
    methods: {
        ...mapActions({
            setMapConfig: 'common/setMapConfig',
            setMapStyle: 'common/setMapStyle'
        }),
        async getMapConfig () {
            try {
                await Promise.all([this.setMapConfig(), this.setMapStyle()])
                this._initMap(this.mapConfig)
            } catch (err) {
                console.log(err)
            }
        },
        _initMap (data, style) {
            this.$nextTick( () =>{
                // 配置初始化层级
                mapMain = new NPGisMapMain()
                const mapId = this.mapId
                mapMain.init(mapId, data, style)
                // 禁止滚动条
                if (this.disableScroll) {
                    mapMain.map.disableScrollWheelZoom()
                }
                // 档案详情轨迹撒点
                this.sprinkleHandler(this.points)
                this.configDefaultMap();
            })
        },
        /**
         * 系统配置的中心点和层级设置
         */
        configDefaultMap () {
            let mapCenterPoint = this.globalObj.mapCenterPoint
            let mapCenterPointArray = !!mapCenterPoint ? this.globalObj.mapCenterPoint.split('_') : ''
            let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14
            let point = mapMain.map.getCenter()
            if (!!mapCenterPointArray.length) {
                point = new NPMapLib.Geometry.Point(parseFloat(mapCenterPointArray[0]), parseFloat(mapCenterPointArray[1]));
            }
            mapMain.map.centerAndZoom(point, mapLayerLevel)
        },
        // 配置最大层级
        addlimitLayerNum () {
            if (!!Number(this.mapObj.maxNumberOfLayer) || 600) {
                let allLayers = mapMain.map.getAllLayers.length;
                if(allLayers >= this.limitLayerNum) {
                    this.$Message.error('已超过配置最大图层数量')
                    return false
                }
            }
            return true
        },
        mapidlerWheel(){
            this.$nextTick(() => {
                let box = document.querySelector('.map');
                box.onmousewheel = (event) => {
                    event = event || window.event;
                    if (event.wheelDelta > 0 || event.detail < 0) {
                        box.style.height = box.clientHeight 
                    } else {
                        box.style.height = box.clientHeight 
                    }
                    //取消火狐浏览器默认行为（因为是用addEventListener,所以必须用此方法来取消）
                    event.preventDefault && event.preventDefault();
                    //取消浏览器默认行为
                    return false;
                };
                //为火狐浏览器绑定鼠标
                this.bind(box, "DOMMouseScroll", box.onmousewheel);
            })
        },
        bind(obj, eventStr, callback) {
            if (obj.addEventListener) {
                obj.addEventListener(eventStr, callback, false);
            } else {
                obj.attachEvent("on" + eventStr, function () {
                    callback.call(obj);
                });
            }
        },
        // 普通搜索和轨迹撒点
        sprinkleHandler (points, listTab = false) {
            if ((!points || points.length === 0)) {
                return false
            }
            // 判断某坐标是否多个
            let bothcoord =  this.repetition(points);
            let markers = []
            let temp = {};
            let multipleIcon = {};
            for (let index = points.length - 1; index >= 0; index--) {
                let item = points[index]
                let k = (item.lon || item.geoPoint.lon) + '_' + (item.lat || item.geoPoint.lat)
                temp[k] == null ? (temp[k] = 0) : temp[k]++
                let imgUrl = '',
                label = null,
                icon = null,
                iconbg = null,
                size = null;
                if(item.iconType) { //人像or车辆
                    size = new NPMapLib.Geometry.Size(40, 40);
                }else{
                    size = new NPMapLib.Geometry.Size(35, 35);
                }
                item.lon = item.lon ? item.lon : item.geoPoint.lon;
                item.lat = item.lat ? item.lat : item.geoPoint.lat;
                let marker = new NPMapLib.Symbols.Marker(item)
                // 用来判断当前点位上是否有多个图片
                if(multipleIcon[k]) {
                    multipleIcon[k] = multipleIcon[k] + 1
                }else{
                    multipleIcon[k] = 1
                }
                if (this.mapLayerConfig.resultOrderIndex) {
                    // 文本标记
                    label = new NPMapLib.Symbols.Label(`${item.Index ? item.Index : (index + 1)}`);
                    // 多数同一点、 一点一数
                    this.dotimg = new NPMapLib.Layers.OverlayLayer('dynamicDotImg');
                    if(item.iconType) { // 人像or车辆
                        imgUrl = item.traitImg;
                        label.setOffset(new NPMapLib.Geometry.Size(-13, 30))
                        label.setStyle({
                            fontSize: 12, //文字大小
                            fontFamily: 'MicrosoftYaHei-Bold, MicrosoftYaHei', //字体
                            color: '#fff', //文字前景色
                            align: 'cm', //对方方式
                            isBold: true //是否粗体
                        })
                    }else{
                        imgUrl = require(`@/assets/img/map/trajectory-${item.active ? 'blue' : 'red'}.png`)
                        label.setOffset(new NPMapLib.Geometry.Size(-0.5, 23))
                        label.setStyle({
                            fontSize: 14, //文字大小
                            fontFamily: 'MicrosoftYaHei-Bold, MicrosoftYaHei', //字体
                            color: !item.active ? '#EA4A36' : '#2C86F8', //文字前景色
                            align: 'cm', //对方方式
                            isBold: true //是否粗体
                        })
                        marker.setLabel(label)
                    }
                    // 设置图片
                    icon = new NPMapLib.Symbols.Icon(imgUrl, size)
                    icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height))
                    marker.setIcon(icon)
                    marker.setLabel(label)
                    marker.k = k
                    mapMain.map.addLayer(this.dotimg)
                    this.dotimg.addOverlay(marker);
                    // marker.flash()
                    // marker._layer.setZIndex(501)
                    let markerBg = new NPMapLib.Symbols.Marker(item)
                    if(item.iconType) {
                        if(!bothcoord.both[k] && multipleIcon[k] == 1) {  //单个
                            let imgSize = new NPMapLib.Geometry.Size(44, 52);
                            let imgTitle = listTab ? 'blue' : item.active ?'blue' : 'unRed'
                            imgUrl = require(`@/assets/img/map/point_${imgTitle}.png`)
                            iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize)
                            iconbg.setAnchor(new NPMapLib.Geometry.Size((-size.width / 2) -2, -size.height - 2))
                            label.setOffset(new NPMapLib.Geometry.Size(-13, 30))
                            markerBg.setIcon(iconbg)
                            markerBg.setLabel(label)
                            mapMain.map.addLayer(this.dotimg)
                            this.dotimg.addOverlay(markerBg);
                            // markerBg.flash()
                        }else if(multipleIcon[k] >= 2){ //多个
                            if(bothcoord.single[k] == multipleIcon[k]){
                                imgUrl = require(`@/assets/img/map/morePoint_${item.active ? 'blue' : 'unRed'}.png`)
                                let moreSize = new NPMapLib.Geometry.Size(52, 56);
                                iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize)
                                iconbg.setAnchor(new NPMapLib.Geometry.Size((-size.width / 2)-7, -size.height-6))
                                label.setOffset(new NPMapLib.Geometry.Size(-14, 30))
                                if(multipleIcon[k] >= 2){
                                    multipleIcon[k] = multipleIcon[k] + 1
                                }
                                markerBg.setIcon(iconbg)
                                markerBg.setLabel(label)
                                mapMain.map.addLayer(this.dotimg)
                                this.dotimg.addOverlay(markerBg);
                                // markerBg.flash()
                            }
                        }
                    }
                    this.dotimg.setZIndex(600)
                    markerBg.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
                        this.modalShow = false;
                        setTimeout(()=> {
                            this.modalShow = true
                        }, 200)
                        this.clicktype= 'search';
                        if(this.points[index].placeTypeCode) { //场所
                            this.visible = true;
                            this.placeObj = this.points[index];
                            return
                        }else{
                            this.selectItem(this.points[index], this.sectionName, true, true)
                        }
                        this.$emit('chooseMapItem', index)
                    })
                    marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
                        this.clicktype= 'search';
                        if(this.points[index].placeTypeCode) { //场所
                            this.visible = true;
                            this.placeObj = this.points[index];
                            return
                        } else {
                            this.selectItem(this.points[index], this.sectionName, true, true)
                        }
                        this.$emit('chooseMapItem', index)
                    })
                    markers.push(marker)
                } else { //人像档案，车辆档案
                    imgUrl = item.traitImg;
                    let recordSize = new NPMapLib.Geometry.Size(40, 40);
                    // imgUrl = require(`@/assets/img/map/${item.dataType == 0 ? 'face' : item.dataType == 1 ? 'vehicle' : 'imsi' }${item.active ? '-active' : ''}.png`)
                    icon = new NPMapLib.Symbols.Icon(imgUrl, recordSize)
                    icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height))
                    marker.setIcon(icon)
                    this.traceLayer = new NPMapLib.Layers.OverlayLayer('trail')
                    if (!this.addlimitLayerNum) return
                    if(mapMain){
                        mapMain.map.addLayer(this.traceLayer)
                    }
                    this.traceLayer && this.traceLayer.addOverlay(marker)
                    let markerBg = new NPMapLib.Symbols.Marker(item)
                    if(!bothcoord.both[k] && multipleIcon[k] == 1) {  //单个
                        let imgSize = new NPMapLib.Geometry.Size(44, 52);
                        let imgTitle = listTab ? 'blue' : item.active ?'blue' : 'unRed'
                        imgUrl = require(`@/assets/img/map/record_${imgTitle}.png`)
                        iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize)
                        iconbg.setAnchor(new NPMapLib.Geometry.Size((-size.width / 2) -2, -size.height - 2))
                        markerBg.setIcon(iconbg)
                        if(mapMain) {
                            mapMain.map.addLayer(this.traceLayer)
                        }
                        this.traceLayer.addOverlay(markerBg);
                        // markerBg.flash()
                    }else if(multipleIcon[k] >= 2){ //多个
                        if(bothcoord.single[k] == multipleIcon[k]){
                            imgUrl = require(`@/assets/img/map/moreRecord_${item.active ? 'blue' : 'unRed'}.png`)
                            let moreSize = new NPMapLib.Geometry.Size(52, 56);
                            iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize)
                            iconbg.setAnchor(new NPMapLib.Geometry.Size((-size.width / 2)-7, -size.height-6))
                            if(multipleIcon[k] >= 2){
                                multipleIcon[k] = multipleIcon[k] + 1
                            }
                            markerBg.setIcon(iconbg)
                            if(mapMain) {
                                mapMain.map.addLayer(this.traceLayer)
                            }
                            this.traceLayer.addOverlay(markerBg);
                            // markerBg.flash()
                        }
                    }
                    markers.push(marker)
                }
            }
            this.markers = markers
        },
        //轨迹回到可视区域
        rePosition (arrList) {
            let minLat, minLon, maxLon, maxLat, sortLat, sortLon
            if (!arrList || arrList.length === 0) {
                return false
            }
            sortLat = arrList.map(e => e.lat)
            sortLon = arrList.map(e => e.lon)
            minLat = Math.min(...sortLat)
            maxLat = Math.max(...sortLat)
            minLon = Math.min(...sortLon)
            maxLon = Math.max(...sortLon)
            let extent = new NPMapLib.Geometry.Extent(minLon, minLat, maxLon, maxLat)
            if(mapMain) {
                mapMain.zoomToExtend(extent)
            }
        },
        // 用于对背景图渲染做判断
        repetition(item) {
            let single = {};//计算每个出现的次数
            let both = {}; //有重复的数据
            item.forEach((item, index) => {
                let k = (item.lon || item.geoPoint.lon) + '_' + (item.lat || item.geoPoint.lat);
                if(!single[k]){
                    single[k] = 1
                }else{
                    single[k] = single[k] += 1
                }
                if(single[k] > 1){
                    both[k] = true
                }
            })
            return {single, both};
        },
        // 关闭多个弹框
        closeAllInfoWindow () {
            infoWindowArr.forEach(row => {
                row.close()
            })
        },
        // 重置marker
        resetMarker () {
            // this.$nextTick(( )=> {
            if(mapMain){
                mapMain.map.removeOverlays(this.markers)
                // 分页换切换tab 去除旧的点位
                mapMain.map.removeOverlay(this.dotimg)
                mapMain.map.removeOverlay(this.traceLayer)
                if(this.dotimg){
                    this.dotimg.removeAllOverlays();
                }
                if(this.traceLayer){
                    this.traceLayer.removeAllOverlays();
                }
                this.dotimg = null;
                this.traceLayer = null;
            }
            // })
            this.closeAllInfoWindow() //关闭弹窗
        },
    }
}
</script>

<style lang='less' scoped>
.map-boxs{
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    .map {
        height: 100%;
        width: 100%;
        position: relative;
    }
    .dianji{
        position: absolute;
        top: 0;
        font-size: 20px;
    }
    .dianji2{
        position: absolute;
        top: 20px;
        font-size: 20px;
    }
}
</style>
