<template>
  <div :class="'based-field' + value" ref="mains">
    <div class="form">
      <Button type="primary" v-if="importApi" class="exportBtn mr-sm" @click="exportFn">
        <i class="icon-font icon-daochu" />导出
      </Button>
      <ui-label class="fl" label="关键词" :width="70">
        <Input v-model="searchData.keyWord" placeholder="请输入字段名称" style="width: 200px" />
      </ui-label>
      <ui-label class="fl ml-lg" label="异常数量" :width="70">
        <InputNumber
          class="input-number-list"
          v-model="searchData.downLimit"
          controls-outside
          :min="1"
          @on-blur="blurLimit(searchData.downLimit)"
          placeholder="请填写数量"
        ></InputNumber>
        <span class="ml-sm mr-sm">--</span>
        <InputNumber
          class="input-number-list"
          v-model="searchData.upperLimit"
          :min="1"
          @on-blur="blurLimit(searchData.upperLimit)"
          controls-outside
          placeholder="请填写数量"
        ></InputNumber>
      </ui-label>
      <ui-label :width="30" class="fl" label=" ">
        <Button type="primary" class="mr-sm" @click="search">查询</Button>
        <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, search)"> 重置</Button>
      </ui-label>
    </div>
    <div class="left-div">
      <ui-table
        class="ui-table"
        :loading="loading"
        :table-columns="listObj.columns"
        :table-data="tableData"
        v-if="listObj.columns"
        ref="table"
        :minus-height="minusHeight"
      >
        <template #macAddr="{ row }">
          <span>{{ row.macAddr || '--' }}</span>
        </template>
        <template #ipAddr="{ row }">
          <span>{{ row.ipAddr || '--' }}</span>
        </template>
        <template #sbgnlxText="{ row }">
          <span>{{ row.sbgnlx || '--' }}</span>
        </template>
        <template #sbdwlxText="{ row }">
          <span>{{ row.sbdwlx || '--' }}</span>
        </template>
        <template #positionTypeText="{ row }">
          <span>{{ row.positionType || '--' }}</span>
        </template>
        <template #action="{ row }">
          <a class="mr30" @click="unqualified(row)">不合格图片</a>
        </template>
      </ui-table>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
  </div>
</template>
<script>
import tasktracking from '@/config/api/tasktracking';

export default {
  name: 'basedField',
  data() {
    return {
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        keyWord: '',
        downLimit: null,
        upperLimit: null,
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      tableData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 此方法在父组件调用表格方法
    async info(blooen) {
      this.loading = true;
      if (blooen) {
        await this.reset();
      }
      await this.listObj
        .loadData(this.searchData)
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data[this.listObj.key];
            this.pageData.totalCount = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 重置（ui-page与后台参数不统一，单独制空pageNumber）
    async resetSearchDataMx1() {
      await this.reset();
      await this.info();
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        keyWord: '',
        downLimit: null,
        upperLimit: null,
        params: { pageNumber: 1, pageSize: 20 },
      };
    },
    // 检索
    search() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.info();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.info();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.info();
    },
    blurLimit() {
      if (this.searchData.downLimit) {
        this.searchData.downLimit = Math.round(Number(this.searchData.downLimit));
      }
      if (this.searchData.upperLimit) {
        this.searchData.upperLimit = Math.round(Number(this.searchData.upperLimit));
      }
      this.$forceUpdate();
    },
    // 不合格图片返回
    unqualified(row) {
      this.$emit('unqualified', row);
    },
    /**
     * responseType: 'blob'， 若有乱码问题用这个
     */
    exportFn() {
      var type = this.$store.getters['carAndVideo/exportType'];
      this.searchData.type = type == 0 ? '' : type;
      var url = this.importApi.api;
      this.$http.post(url, this.searchData, { responseType: 'arraybuffer' }).then((res) => {
        if (res.status == 200) {
          let a = document.createElement('a');

          //ArrayBuffer 转为 Blob
          let blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel',
          });

          let objectUrl = URL.createObjectURL(blob);
          a.setAttribute('href', objectUrl);
          var fileName = '导出数据';

          switch (type) {
            case 0:
              fileName = '数据输出';
              break;
            case 1:
              fileName = '图像抓拍时间准确性检测';
              break;
            case 2:
              fileName = '图像上传及时性检测';
              break;
            case 3:
              fileName = '大图URL检测';
              break;
            case 4:
              fileName = '车牌识别准确性检测优化';
              break;
            case 5:
              fileName = '车辆结构化属性准确检性测优化';
              break;
            default:
              break;
          }

          let now = new Date(),
            year = now.getFullYear(),
            mon = now.getMonth() + 1,
            day = now.getDate(),
            hours = now.getHours(),
            min = now.getMinutes(),
            sec = now.getSeconds();
          var dataStr =
            '' +
            year +
            (mon < 10 ? '0' + mon : mon) +
            (day < 10 ? '0' + day : day) +
            '-' +
            (hours < 10 ? '0' + hours : hours) +
            (min < 10 ? '0' + min : min) +
            (sec < 10 ? '0' + sec : sec);

          if (this.importApi.api === tasktracking.downloadFaceLibAbnormalDev) {
            //获取heads中的filename文件名
            let temp = res.headers['content-disposition'].split(';')[1].split('filename=')[1];
            let hasfileName = decodeURIComponent(temp);
            a.setAttribute('download', hasfileName);
          } else {
            a.setAttribute('download', '车辆设备数据 - ' + fileName + ' - [iVDG] - [' + dataStr + '].xls');
          }
          a.click();
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
  },
  watch: {},
  computed: {},
  props: {
    curIndex: {
      type: Number,
      default: 1, // 当前展示步骤
    },
    minusHeight: {
      type: Number,
      default: 373, // 当前展示步骤
    },
    listObj: {
      type: Object,
      default() {},
    },
    importApi: {
      type: [Boolean, Object],
      default: false,
    },
    value: {
      // 用于优化设备模式表格用v-show导致页面加载表格样式问题
      type: Number,
      default: 0,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.based-field0 {
  height: 0px;
  overflow: hidden;
}
.based-field1 {
  height: 100%;
  width: 100%;
  background: var(--bg-content);
  .based-field-label {
    color: #fff;
    font-size: 14px;
    padding-right: 12px;
  }
  .based-field-header {
    .title {
      display: flex;
      .border-header {
        width: 8px;
        height: 30px;
        background: #239df9;
        margin-right: 6px;
      }
      .title-header {
        color: #fff;
        width: 394px;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        font-size: 16px;
      }
    }
    .back {
      color: #2d8cf0;
      font-size: 16px;
      cursor: pointer;
      height: 30px;
      line-height: 30px;
      &:hover {
        color: #57a3f3;
      }
    }
  }
  .form {
    height: 79px;
    padding-top: 15px;
    padding-bottom: 30px;
    .input-number-list {
      padding: 0px;
      /deep/ .ivu-input-number-controls-outside-btn {
        display: none;
      }
    }
  }
  .left-div {
    position: relative;
    width: 100%;
    min-height: 1px;
  }
  .ui-table {
    // padding: 0 20px;
    width: 100%;
  }
  /deep/ .ivu-divider-horizontal {
    margin: 0;
    background: #1e3f61;
  }
  .mr30 {
    margin-right: 30px !important;
  }
  .exportBtn {
    float: right;
    margin-top: 3px;

    .icon-daochu {
      font-size: 10px;
      margin-right: 5px;
      margin-top: -2px;
    }
  }
}
</style>
