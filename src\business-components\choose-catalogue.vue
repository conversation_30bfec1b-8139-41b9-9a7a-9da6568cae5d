<template>
  <api-tree
    :list="list"
    :tree-props="treeProps"
    :has-cascader="true"
    :default-props="defaultProps"
    :is-custom="true"
    :check-ids="checkIds"
    :custom-area-names="customAreaNames"
    @selected="handleSelectTree"
    @handleSaveCategory="handleSaveCategory"
    @resetCategory="resetCategory"
    @clearTree="resetCategory"
    @handleTreeShow="handleTreeShow"
  >
    <div slot="right-table">
      <!-- <Collapse v-model="collapseName" simple accordion >
        <Panel name="1">
          重点场所目录
          <CheckboxGroup
            slot="content"
            v-model="importantAreaIds"
            v-if="importantPlaceCategory.length"
            @on-change="categoryChange"
          >
            <Checkbox
              v-for="(importantItem, importantIndex) in importantPlaceCategory"
              :key="importantIndex"
              :label="importantItem.id"
              >{{ importantItem.deviceTagName }}</Checkbox
            >
          </CheckboxGroup>
        </Panel>
      </Collapse> -->
      <el-tree
        ref="categorytree"
        :data="categoryList"
        show-checkbox
        node-key="id"
        :check-strictly="true"
        :props="{
          children: 'childList',
          label: 'directoryName',
        }"
        :default-checked-keys="checkIds"
        :default-expanded-keys="checkIds"
        @check="handleCheck"
      >
        <div slot-scope="{ node, data }" class="slot-class">
          <span class="width-xs ellipsis">{{ data.directoryName }}</span>
          <span
            class="color-active custom-tree-slot-text"
            v-if="data.parentId == -1"
            @click.stop="checkOneCate(node, data)"
            >{{ `${checkIds.includes(data.id) ? '取消' : '全选'}` }}</span
          >
        </div>
      </el-tree>
    </div>
  </api-tree>
</template>
<script>
import { mapGetters } from 'vuex';
import category from '@/config/api/catalogmanagement';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    categoryObj: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    regionCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      treeProps: {
        treeValue: '',
        id: '',
        nodeKey: '',
      },
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      categoryList: [],
      checkNodes: [],
      checkIds: [],
      importantAreaIds: [], // 重点场所目录ID
      customAreaNames: [], // 已选目录名称
      importantPlaceCategory: [],
      importantObj: {},
      importantName: [],
      collapseName: '1',
      categoryIds: [],
      list: [],
    };
  },
  mounted() {
    this.importantAreaIds =
      !!this.categoryObj.importantAreaIds && this.categoryObj.importantAreaIds.length
        ? this.categoryObj.importantAreaIds
        : [];
    this.checkIds =
      !!this.categoryObj.customAreaIds && this.categoryObj.customAreaIds.length ? this.categoryObj.customAreaIds : [];
    this.importantAreaIds = this.importantAreaIds.filter((item) => item !== 0);
    this.checkIds = this.checkIds.filter((item) => item !== 0);
    this.customAreaNames =
      !!this.categoryObj.customAreaNames && this.categoryObj.customAreaNames.length
        ? this.categoryObj.customAreaNames
        : [];
    this.categoryIds = this.checkIds.concat(this.importantAreaIds);
    this.checkNodes = this.checkIds.map((item, checkedIndex) => {
      let obj = {};
      let importantLen = this.importantAreaIds.length;
      obj.directoryName = this.customAreaNames[importantLen + checkedIndex];
      return obj;
    });
  },
  methods: {
    checkOneCate(node, data) {
      this.$set(data, 'checkAll', !data.checkAll);
      this.$refs.categorytree.setChecked(data, data.checkAll, true);
      this.checkIds = this.$refs.categorytree.getCheckedKeys();
      this.checkNodes = this.$refs.categorytree.getCheckedNodes();
    },
    async handleSelectTree(data) {
      this.collapseName = '';
      // await this.initImportantPlace(data.orgCode)
      await this.getAllCustomAreaTree(data.orgCode);
    },
    async getAllCustomAreaTree(orgCode) {
      try {
        let { data } = await this.$http.get(category.getCustomAreaTree, {
          params: { orgCode: orgCode },
        });
        // data.data.map((item) => {
        //   item.disabled = true
        //   item.directoryName = item.directoryName
        // })
        this.categoryList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async initImportantPlace(orgCode) {
      try {
        let { data } = await this.$http.get(category.getAllImportantArea, {
          params: { orgCode: orgCode },
        });
        this.importantPlaceCategory = data.data.map((item) => {
          this.importantObj[item.id] = item.deviceTagName;
          return item;
        });
      } catch (err) {
        console.log(err);
      }
    },
    handleCheck() {
      this.checkIds = this.$refs.categorytree.getCheckedKeys();
      this.checkNodes = this.$refs.categorytree.getCheckedNodes();
    },
    handleSaveCategory() {
      let realCheckNodes = this.checkNodes.filter((item) => !item.disabled);
      this.customAreaNames = realCheckNodes.map((item) => {
        return item.directoryName;
      });
      this.categoryIds = realCheckNodes.map((item) => item.id).concat(this.importantAreaIds);
      let categoryObj = {
        customAreaNames: this.customAreaNames,
        checkIds: realCheckNodes.map((item) => item.id), // 自定义标签id
        importantAreaIds: this.importantAreaIds, //重点目录id
        categoryIds: this.categoryIds,
      };
      this.$emit(
        'getDevCategoryData',
        categoryObj,
        this.importantObj,
        this.checkNodes.filter((item) => !item.disabled),
      );
      // this.$refs.formValidate.validateField('categoryIds')
    },
    resetCategory() {
      if (!this.checkIds.length && !this.importantAreaIds.length) {
        return false;
      }
      this.checkIds = [];
      this.checkNodes = [];
      this.importantAreaIds = [];
      this.categoryIds = [];
      this.customAreaNames = [];
      let categoryObj = {
        customAreaNames: this.customAreaNames,
        checkIds: this.checkIds,
        importantAreaIds: this.importantAreaIds,
        categoryIds: this.categoryIds,
      };
      this.$refs.categorytree.setCheckedKeys([]);
      this.$emit('getDevCategoryData', categoryObj);
      // this.$refs.formValidate.validateField('categoryIds')
    },
    categoryChange(data) {
      this.categoryIds = data;
    },
    handleTreeShow() {
      // this.list = []
    },
    async getOrgList(code) {
      try {
        const res = await this.$http.get(governanceevaluation.getOrglistByRegioncodeWithDataScope, {
          params: {
            regioncode: code,
          },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async setTreeData() {
      try {
        let orgList = [];
        if (this.regionCode) {
          orgList = await this.getOrgList(this.regionCode);
        } else {
          orgList = this.$util.common.deepCopy(this.initialOrgList);
        }
        this.list = this.$util.common.arrayToJson(orgList, 'id', 'parentId');
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {
    ...mapGetters({
      initialOrgList: 'common/getInitialOrgList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {
    initialOrgList: {
      handler(val) {
        if (val && val.length) {
          this.setTreeData();
        }
      },
      immediate: true,
    },
    regionCode() {
      this.setTreeData();
    },
  },
  components: {
    apiTree: require('@/components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.slot-class {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-right: 20px;
}
@{_deep} .select-width {
  width: 100% !important;
}
@{_deep} .api-organization-tree {
  width: 100% !important;
}
@{_deep} .ivu-collapse {
  background-color: transparent;
  border: none;
  &-item {
    border-top: none;
    &-active {
      .ivu-collapse-header > i {
        font-size: 12px !important;
        color: #239df9 !important;
        transform: rotate(0deg) translate(-6px, 3px) scale(0.45) !important;
      }
    }
  }
  .ivu-collapse-header {
    display: flex;
    height: 0.135417rem;
    line-height: 0.135417rem;
    padding: 0 6px;
    font-size: 12px !important;
    color: #ffffff;
    border-bottom: none !important;
    opacity: 0.9;
    word-break: break-all;
    i {
      margin-right: 0 !important;
    }
    .ivu-icon-ios-arrow-forward {
      font-size: 12px !important;
      color: #239df9;
      transform: rotate(-90deg) scale(0.4);
      &:before {
        font-family: 'icon-font';
        content: '\e7a3';
      }
    }
  }
  &-content {
    background: transparent;
    padding: 0;
    &-box {
      padding-bottom: 0 !important;
    }
  }
}
@{_deep} .ivu-checkbox-group {
  padding: 0 24px;
  &-item {
    width: 100%;
    height: 0.135417rem;
    line-height: 0.135417rem;
  }
}
@{_deep} .ivu-checkbox-inner {
  width: 0.072917rem;
  height: 0.072917rem;
}
@{_deep} .ivu-form-item-content {
  line-height: normal;
}
@{_deep} .el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep} .el-tree-node__expand-icon.is-leaf {
  display: none;
  color: transparent;
}
@{_deep}.el-checkbox {
  display: inline-block;
  &.is-disabled {
    display: none;
  }
}
@{_deep}.drop-tree {
  width: 560px;
}
</style>
