<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <ui-icon
          type="close"
          :size="14"
          @click.native="() => $emit('close', $event)"
        ></ui-icon>
      </header>
      <section class="dom-content">
        <div class="face-info">
          <div class="face-info-left">
            <img v-lazy="faceInfo.traitImg" alt="小图" />
            <span class="similarity" v-if="faceInfo.score">{{
              faceInfo.score
            }}</span>
            <div class="title" @click="tabsChange">
              <span :class="checkStatus ? 'active' : ''">抓拍记录</span>
              <span :class="!checkStatus ? 'active' : ''">人员档案</span>
            </div>
            <div class="traffic-record" v-if="checkStatus">
              <div class="dom-content-p">
                <span class="label">年龄段</span>：
                <span class="message">{{ faceInfo.ageName || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">戴眼镜</span>：
                <span class="message">{{
                  faceInfo.eyeglass === 0 ? "未戴" : "戴"
                }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">戴帽子</span>：
                <span class="message">{{
                  faceInfo.isCap === 0
                    ? "未戴"
                    : faceInfo.isCap === 1
                    ? "戴"
                    : "其他"
                }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">口罩</span>：
                <span class="message">{{
                  faceInfo.mask === 0 ? "未戴" : "戴"
                }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">抓拍时间</span>：
                <span class="message">{{ faceInfo.absTime || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">设备名称</span>：
                <span class="message">{{ faceInfo.deviceName || "--" }}</span>
              </div>
            </div>
            <div class="personnel-files" v-else>
              <!-- <div class="complete-file" @click="toDetail">
                <ui-icon type="record" :color="'#2C86F8'"></ui-icon>
                <span style="color: #2c86f8">完整档案</span>
              </div> -->
              <div class="dom-content-p">
                <span class="label">视频身份</span>：
                <span
                  class="message identity"
                  @click="toDetail(faceInfo.vid)"
                  >{{ faceInfo.vid }}</span
                >
              </div>
              <div class="dom-content-p">
                <span class="label">姓名</span>：
                <span class="message">{{ faceArchives.xm }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">联系方式</span>：
                <span class="message">{{ faceArchives.lxdh || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">身份证号</span>：
                <span class="message" :title="faceArchives.gmsfhm">{{
                  faceArchives.gmsfhm || "--"
                }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">家庭住址</span>：
                <span class="address">{{
                  faceArchives.xzz_mlxxdz || "--"
                }}</span>
              </div>
            </div>
          </div>
          <div class="face-info-right">
            <!-- <div class="right-header">
              <div class="tablist">
                <div v-for="(item, index) in tabList" :key="index" :class="{ active: currentTabIndex == index }" class="ivu-tabs-tab" @click="tabClick(index)">
                  <span>{{ item.name }}</span>
                </div>
              </div>
              <operate-bar></operate-bar>
            </div> -->
            <div class="right-content">
              <ui-image :src="faceInfo.sceneImg" alt="静态库" viewer />
              <!-- <div class="complete-face" v-if="currentTabIndex === 0">
                <img :style="imgStyle" v-lazy="faceInfo.sceneImg" alt="" @mousedown="handleMouseDown" ref="img" />
                <span>
                  <ui-icon type="zoomin" @click.native="handleActions('zoomIn')"></ui-icon>
                  <ui-icon type="zoomout" @click.native="handleActions('zoomOut')"></ui-icon>
                  <ui-icon type="download" @click.native="handleDownload"></ui-icon>
                </span>
              </div>
              <div class="video" v-else>
                <easyPlayer />
              </div> -->
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
// import easyPlayer from '@/components/easy-player.vue'

export default {
  components: {
    // operateBar,
    // easyPlayer,
  },
  props: {},
  computed: {
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? "transform .3s" : "",
        "margin-left": `${offsetX}px`,
        "margin-top": `${offsetY}px`,
      };
      style.maxWidth = style.maxHeight = "100%";
      return style;
    },
  },
  data() {
    return {
      tabList: [
        {
          name: "场景大图",
        },
        // {
        //   name: '历史视频'
        // }
      ],
      currentTabIndex: 0,
      faceInfo: {},
      collectionList: [{ count: 3 }, { count: 4 }, { count: 5 }, { count: 6 }],
      currentCollectionIndex: 0,
      isChoose: false,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
      checkStatus: true,
      imgUrl: "",
      isRequest: false,
      faceArchives: {},
    };
  },
  watch: {
    // checkStatus (newV, oldV) {
    //   // console.log(newV, oldV)
    //   if (!newV && !this.isRequest) {
    //     this.archives()
    //   }
    // }
  },
  methods: {
    init(row) {
      this.checkStatus = true;
      this.faceInfo = { ...row };
      // return getPersonInfoByPersonId({vid: row.vid}).then(res => {
      //   this.transform = {
      //     scale: 1,
      //     deg: 0,
      //     offsetX: 0,
      //     offsetY: 0,
      //     enableTransition: false
      //   }
      //   this.faceInfo = { ...row }
      //   console.log('this.faceInfo', this.faceInfo)
      //   this.faceArchives = res.data
      //   return res
      // })
    },
    tabsChange() {
      this.checkStatus = !this.checkStatus;
      if (!this.checkStatus) {
        this.archives();
      }
    },
    tabClick(index) {
      this.currentTabIndex = index;
    },
    selectCollectionHandler(index) {
      this.currentCollectionIndex = index;
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case "zoomOut":
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case "zoomIn":
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case "clocelise":
          transform.deg += rotateDeg;
          break;
        case "anticlocelise":
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    },
    handleMouseDown(e) {
      const { scale } = this.transform;
      if (scale === 1 || e.button !== 0) return;
      const { rafThrottle, on, off } = this.$util.common;
      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      const dom = this.$refs["img"];
      on(dom, "mousemove", this._dragHandler);
      dom.addEventListener("mouseup", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      dom.addEventListener("mouseleave", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      e.preventDefault();
    },
    handleDownload(name) {
      this.imgUrl = this.faceInfo.sceneImg;
      //下载图片地址和图片名
      let image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        let url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        let a = document.createElement("a"); // 生成一个a元素
        let event = new MouseEvent("click"); // 创建一个单击事件
        a.download = "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = this.imgUrl;
    },
    toDetail(id) {
      if (!this.faceArchives.archiveNo) {
        this.$Message.warning("暂无该人员档案信息");
        return;
      }
      const { href } = this.$router.resolve({
        name: "video-archive",
        query: {
          archiveNo: id,
          source: "video",
          initialArchiveNo: id,
        },
      });
      window.open(href, "_blank");
    },
    archives() {
      if (!this.faceInfo.vid) {
        this.$Message.warning("暂无该人员档案信息");
        return;
      }
      this.isRequest = true;
      getPersonInfoByPersonId({ vid: this.faceInfo.vid }).then((res) => {
        this.transform = {
          scale: 1,
          deg: 0,
          offsetX: 0,
          offsetY: 0,
          enableTransition: false,
        };
        this.faceArchives = res.data || {};
      });
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  // padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}

.dom {
  width: 840px;
  height: 600px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;
  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }
  .dom-content {
    padding: 10px 20px;
    font-size: 14px;
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .face-list {
      display: flex;
      margin-bottom: 5px;
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border: 1px solid #d3d7de;
        margin-right: 8px;
        cursor: pointer;
        position: relative;
        > img {
          width: 100%;
          height: 100%;
        }
        > span {
          display: inline-block;
          width: 25px;
          height: 16px;
          background: #2c86f8;
          text-align: center;
          border-radius: 0px 0px 4px 0px;
          font-size: 12px;
          color: #ffffff;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .active {
        border: 3px solid rgba(44, 134, 248, 1);
      }
    }
    .face-info {
      margin-top: 7px;
      display: flex;
      &-left {
        position: relative;
        > img {
          width: 200px;
          height: 200px;
          border: 1px solid #d3d7de;
        }
        .similarity {
          position: absolute;
          left: 4px;
          top: 4px;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
          border-radius: 4px;
          font-size: 18px;
          color: #fff;
          text-align: center;
        }
        .traffic-record {
          margin-top: 10px;
        }
        .personnel-files {
          margin-top: 10px;
          .complete-file {
            height: 18px;
            line-height: 18px;
            margin: 15px 0 17px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            > span {
              margin-left: 5px;
              font-size: 12px;
            }
          }
        }
      }
      &-right {
        width: 100%;
        height: 484px;
        .right-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .tablist {
            height: 28px;
            line-height: 28px;
            width: 400px !important;
            margin: 0;
            .ivu-tabs-tab {
              float: left;
              border: 1px solid #2c86f8;
              border-right: none;
              padding: 0 15px;
              color: #2c86f8;
              &:hover {
                background: #2c86f8;
                color: #ffffff;
              }
              &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-right: none;
              }
              &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border-right: 1px solid #2c86f8;
              }
            }
            .active {
              background: #2c86f8;
              color: #fff;
            }
          }
        }
        .right-content {
          height: 100%;
          // margin-top: 6px;
          border: 1px solid #d3d7de;
          background: #f9f9f9;
          .complete-face {
            width: 580px;
            height: 430px;
            position: relative;
            text-align: center;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;

            > img {
              // width: 100%;
              // height: 100%;
            }

            > span {
              display: inline-block;
              width: 100%;
              height: 30px;
              line-height: 30px;
              position: absolute;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.5);
              .iconfont {
                color: #fff !important;
                padding: 0 12px;
                cursor: pointer;
              }
            }
          }
          .video {
            /deep/.easy-player {
              margin-top: 60px;
            }
          }
        }

        margin-left: 15px;
      }
    }
    .dom-content-p {
      margin-top: 6px;
      width: 200px;
      display: flex;
      height: 16px;
      line-height: 16px;
      .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 61px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
      }
      .message {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .identity {
        cursor: pointer;
        color: #f29f4c;
      }
      .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
  > footer {
    border-top: 1px solid #d3d7de;
    padding: 0 20px;
    height: 55px;
    line-height: 55px;
    > span {
      color: #2c86f8;
      margin-right: 20px;
      cursor: pointer;
    }
  }
}
.dom:before,
.dom:after {
  // content: '';
  // display: block;
  // border-width: 8px;
  // position: absolute;
  // bottom: -16px;
  // left: 420px;
  // border-style: solid dashed dashed;
  // border-color: #ffffff transparent transparent;
  // font-size: 0;
  // line-height: 0;
}
.title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 10px;
  cursor: pointer;
  > span {
    color: rgba(0, 0, 0, 0.6);
    position: relative;
    margin-right: 34px;
  }
  .active {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
  }
  .active:before {
    content: "";
    position: absolute;
    width: 56px;
    height: 3px;
    bottom: -3px;
    background: #2c86f8;
  }
  .num {
    font-weight: normal;
    > span {
      font-weight: normal;
      color: #2c86f8;
      position: relative;
    }
  }
  .more {
    color: rgba(0, 0, 0, 0.35);
  }
}
</style>
