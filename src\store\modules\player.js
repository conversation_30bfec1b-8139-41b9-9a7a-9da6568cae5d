import { getShieldResourcePool } from "@/api/config.js";

export default {
  namespaced: true,
  state: {
    lockedWin: [], // 锁定的窗口
    vodSearchTime: [], // 录像检索时间
    ignoreDevices: [], // 屏蔽设备
  },
  mutations: {
    setLockedWin(state, array) {
      state.lockedWin = array;
    },
    addLockedWin(state, index) {
      state.lockedWin.push(index);
    },
    delLockedWin(state, index) {
      state.lockedWin = state.lockedWin.filter((v) => v != index);
    },
    setVodSearchTime(state, array) {
      state.vodSearchTime = array;
    },
    setIgnoreDevices(state, array) {
      state.ignoreDevices = array;
    },
  },
  getters: {
    getLockedWin(state) {
      return state.lockedWin;
    },
    getVodSearchTime(state) {
      return state.vodSearchTime;
    },
    getIgnoreDevices(state) {
      return state.ignoreDevices;
    },
  },
  actions: {
    fetchShieldResourcePool({ state, commit }) {
      return new Promise((resolve, reject) => {
        getShieldResourcePool()
          .then((res) => {
            commit("setIgnoreDevices", res.data || []);
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
  },
};
