<template>
  <div class="ui-tabs">
    <span class="option slider-left pointer" @click="scrollLeft">
      <i class="icon-font icon-zuojiantou"></i>
    </span>
    <span class="option slider-right pointer" @click="scrollRight">
      <i class="icon-font icon-youjiantou"></i>
    </span>
    <div class="ui-tabs-contanier">
      <div class="ui-tabs-view" ref="uiTabsView">
        <div class="ui-tabs-scroll" ref="uiTabsScroll" :style="styles">
          <slot></slot>
        </div>
      </div>
      <div ref="contextmenuRef" class="contextmenu" v-if="contextmenuShow" :style="contextmenuStyles">
        <slot name="contextmenu"></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ui-tabs',
  provide() {
    return {
      rootTabs: this,
    };
  },
  props: {
    value: {
      type: String,
      default: () => {},
    },
    closable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tabsValue: null,
      contextmenuShow: false,
      contextmenuStyles: {},
      styles: {},
      translateX: 0,
      rightClickName: null,
    };
  },
  created() {},
  mounted() {},
  methods: {
    scrollLeft() {
      const bool = this.translateX >= this.$refs['uiTabsView'].clientWidth;
      if (bool) {
        this.translateX += this.$refs['uiTabsView'].clientWidth;
        this.styles = {
          transform: `translateX(${this.translateX}px)`,
        };
      } else {
        if (this.translateX === 0) {
          this.$Message.warning('已经滑到尽头了');
        } else {
          this.translateX = 0;
          this.styles = {};
        }
      }
    },
    scrollRight() {
      /**
       * 1.滑动距离translateX + 可视窗口宽度 uiTabsView clientWidth
       * 2.滑块总宽度 uiTabsScroll offsetWidth
       * 如果2 > 1 说明还可以滑动 否则已经滑动到尽头
       */
      const bool = -this.translateX + this.$refs['uiTabsView'].clientWidth < this.$refs['uiTabsScroll'].offsetWidth;
      if (bool) {
        /**
         * 1.可滑动距离 scrollDistance
         * 2.可视窗口距离 uiTabsView clientWidth
         * 如果 1 > 2 则滑动整个可视窗口的距离 否则滑动可滑动距离
         */
        const scrollDistance =
          this.$refs['uiTabsScroll'].offsetWidth - (-this.translateX + this.$refs['uiTabsView'].clientWidth);
        if (scrollDistance >= this.$refs['uiTabsView'].clientWidth) {
          this.translateX -= this.$refs['uiTabsView'].clientWidth;
        } else {
          this.translateX -= scrollDistance;
        }
        this.styles = {
          transform: `translateX(${this.translateX}px)`,
        };
      } else {
        this.$Message.warning('已经滑到尽头了');
      }
    },
    getActiveDom() {
      let activeDom = null;
      this.$refs.uiTabsScroll.childNodes.forEach((row) => {
        row.classList.forEach((rw) => {
          if (rw === 'active') {
            activeDom = row;
          }
        });
      });
      return activeDom;
    },
    scrollView() {
      const activeDom = this.getActiveDom();
      if (!activeDom) return;
      const leftDistance = activeDom.offsetLeft;
      const viewWidth = this.$refs['uiTabsView'].offsetWidth;
      if (leftDistance + this.translateX < 0) {
        this.translateX = -leftDistance;
      } else if (leftDistance + this.translateX >= viewWidth) {
        const scrollDistance = this.$refs['uiTabsScroll'].offsetWidth - (leftDistance + viewWidth);
        if (scrollDistance > 0) {
          this.translateX = leftDistance;
        } else {
          this.translateX = -(this.$refs['uiTabsScroll'].offsetWidth - viewWidth);
        }
      }
      this.styles = {
        transform: `translateX(${this.translateX}px)`,
      };
    },
    openMenu(name, e) {
      this.rightClickName = name;
      this.contextmenuShow = true;
      this.$nextTick(() => {
        const contextmenuWidth = this.$refs.contextmenuRef.clientWidth;
        // 判断鼠标右侧是否宽度足够放下右击菜单 如果不够则显示到鼠标左侧
        const collision = window.innerWidth - e.clientX < contextmenuWidth;
        this.contextmenuStyles = {
          top: `${e.clientY}px`,
          left: collision ? `${e.clientX - contextmenuWidth}px` : `${e.clientX}px`,
        };
      });
    },
    closeMenu() {
      this.contextmenuShow = false;
    },
  },
  watch: {
    value: {
      handler(val) {
        this.$nextTick(() => {
          this.scrollView();
        });
        this.tabsValue = val;
      },
      immediate: true,
    },
    tabsValue(val) {
      this.$emit('input', val);
    },
    contextmenuShow(val) {
      if (val) {
        document.body.addEventListener('click', this.closeMenu);
      } else {
        document.body.removeEventListener('click', this.closeMenu);
      }
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.ui-tabs {
  height: 36px;
  line-height: 36px;
  color: var(--color-tab-pane);
  position: relative;
  padding: 0 36px;
  .option {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    &:hover {
      background-color: var(--bg-tab-pane-active);
      color: var(--color-tab-pane-active);
    }
    &.slider-left {
      left: 0;
      border-right: 1px solid;
      border-color: var(--color-tab-icon);
    }
    &.slider-right {
      right: 0;
      border-left: 1px solid;
      border-color: var(--color-tab-icon);
    }
    i {
      font-size: 12px;
    }
  }
  .ui-tabs-contanier {
    position: relative;
    z-index: 850;
    .ui-tabs-view {
      white-space: nowrap;
      overflow: hidden;
      position: relative;
      .ui-tabs-scroll {
        float: left;
        position: relative;
        transition: transform 0.5s ease-in-out;
        padding-left: 0;
        margin: 0;
      }
    }
    .contextmenu {
      width: auto;
      max-height: 200px;
      overflow: auto;
      margin: 5px 0;
      padding: 5px 0;
      background-color: var(--bg-darkblue-block);
      box-sizing: border-box;
      border-radius: 4px;
      position: fixed;
      box-shadow: var(--shadow-tab-contextmenu);
      z-index: 999;
    }
  }
}
</style>
