<template>
  <div class="information-statistics">
    <ul class="statistics-ul">
      <li v-for="(item, index) in statisticsList" :key="index" :class="item.liBg">
        <div class="monitoring-data">
          <span class="icon" :class="item.iconColor">
            <i class="icon-font f-30 base-text-color" :class="[item.icon]"></i>
          </span>
          <div class="information-data">
            <span>{{ item.name }}</span>
            <span class="statistic-num">
              <countTo
                :class="item.textColor"
                class="f-18 ml-xs"
                :startVal="0"
                :endVal="item.value || 0"
                :duration="3000"
              ></countTo>
            </span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'statistic-card',
  props: {
    statisticsList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      cardObj: {},
    };
  },
  methods: {},
  watch: {},
  components: {
    countTo: require('vue-count-to').default,
  },
};
</script>

<style scoped lang="less">
.information-statistics {
  display: flex;
  width: 100%;
  height: 100%;
  .icon-bg1 {
    background: linear-gradient(to bottom, #1bafd5, #0a9f90);
  }
  .icon-bg2 {
    background: linear-gradient(to bottom, #4ba0f5, #0c44a7);
  }
  .color7 {
    color: #19d5f6 !important;
  }
  .li-bg7 {
    background: var(--bg-sub-content) !important;
  }
  .statistics-ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    li {
      flex: 1;
      height: 100px;
      width: 100%;
      display: flex;
      margin-right: 10px;
      align-items: center;
      // &:nth-child(2) {
      //   margin-right: 0;
      // }
      .monitoring-data {
        width: 100%;
        height: 100px;
        line-height: 100px;
        display: flex;
        align-items: center;

        .icon {
          display: inline-block;
          height: 50px;
          width: 50px;
          line-height: 50px;
          text-align: center;
          margin-left: 40px;
          border-radius: 50%;
          .f-30 {
            font-size: 28px;
          }
        }
        .information-data {
          height: 60px;
          line-height: 60px;
          margin-left: 14px;
          text-align: left;
          flex: 1;
          span {
            white-space: nowrap;
            font-style: normal;
            height: 30px;
            line-height: 30px;
            color: #f5f5f5;
            font-size: 12px;
          }
          .statistic-num {
            font-size: 18px;
            font-family: Microsoft YaHei;
            vertical-align: sub;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
