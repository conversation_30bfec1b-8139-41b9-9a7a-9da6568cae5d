<template>
  <div class="search-list">
    <template v-for="(item, index) in formItemData">
      <ui-label v-if="item.type === 'org'" class="inline mb-lg mr-lg" :key="item.key + '-' + index" :label="item.label">
        <api-organization-tree
          v-if="!item.slot"
          :select-tree="selectOrgTree"
          :placeholder="'请选择' + item.label"
          @selectedTree="selectedOrgTree"
        >
        </api-organization-tree>
        <slot v-else :name="item.slot"></slot>
      </ui-label>
      <ui-label
        v-if="item.type === 'region'"
        class="inline mb-lg mr-lg"
        :key="item.key + '-' + index"
        :label="item.label"
      >
        <api-area-tree
          v-if="!item.slot"
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          :placeholder="'请选择' + item.label"
        ></api-area-tree>
        <slot v-else :name="item.slot"></slot>
      </ui-label>
      <ui-label
        v-if="item.type === 'input'"
        class="inline mb-lg mr-lg"
        :key="item.key + '-' + index"
        :label="item.label"
      >
        <Input
          v-model="searchData[item.key]"
          class="width-md"
          clearable
          :placeholder="'请输入' + item.label"
          v-if="!item.slot"
        >
        </Input>
        <slot v-else :name="item.slot"></slot>
      </ui-label>
      <ui-label
        v-if="item.type === 'select'"
        class="inline mb-lg mr-lg"
        :key="item.key + '-' + index"
        :label="item.label"
      >
        <Select
          class="width-md"
          v-model="searchData[item.key]"
          :placeholder="'请选择' + item.label"
          :disabled="item.disabled"
          :multiple="!!item.selectMutiple"
          :max-tag-count="1"
          clearable
          v-if="!item.slot"
        >
          <Option
            v-for="(opt, ind) in item.options || []"
            :value="opt.value"
            :label="opt.label"
            :key="`${ind}${opt.value}`"
          ></Option>
        </Select>
        <slot v-else :name="item.slot"></slot>
      </ui-label>
      <ui-label
        v-if="item.type === 'start-end-num'"
        class="inline mb-lg mr-lg"
        :key="item.key + '-' + index"
        :label="item.label"
      >
        <div v-if="!item.slot" class="flex-aic">
          <InputNumber
            :precision="0"
            :max="Number.MAX_SAFE_INTEGER"
            v-model.number="searchData[item.startKey]"
            class="width-mini"
            :class="{ 'reset-width': item.width }"
            :style="{ '--reset-width': `${item.width / 192}rem` }"
            clearable
          >
          </InputNumber>
          <span class="ml-sm mr-sm">--</span>
          <InputNumber
            :precision="0"
            :max="Number.MAX_SAFE_INTEGER"
            v-model.number="searchData[item.endKey]"
            class="width-mini"
            :class="{ 'reset-width': item.width }"
            :style="{ '--reset-width': `${item.width / 192}rem` }"
            clearable
          >
          </InputNumber>
        </div>
        <slot v-else :name="item.slot"></slot>
      </ui-label>
    </template>
    <div :class="['mb-lg', selectTabsVisible ? 'data-list' : 'inline']">
      <label v-if="selectTabsVisible" class="select-tabs mr-lg">
        <span class="mr-sm">标签类型</span>
        <ui-select-tabs
          class="ui-select-tabs"
          :list="errorList"
          @selectInfo="selectInfo"
          ref="uiSelectTabs"
        ></ui-select-tabs>
      </label>
      <div class="inline">
        <Button type="primary" class="mr-sm" @click="search">查询</Button>
        <Button type="default" @click="reset">重置</Button>
      </div>
    </div>
  </div>
</template>

<script>
// import equipmentassets from '@/config/api/equipmentassets'
import taganalysis from '@/config/api/taganalysis';
import { mapGetters } from 'vuex';

export default {
  name: 'search-list',
  props: {
    dictData: {
      type: Object,
      default: () => {},
    },
    searchConditions: {
      type: Object,
      default: () => {},
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchData: {
        // deviceId: '', // 设备编码
        // deviceName: '', // 设备名称
        // tagIds: [],
        // orgCode: '',
        // regionCode: ''
      },
      choosedOrg: '',
      selectOrgTree: {
        orgCode: null,
      },
      selectTree: {
        regionCode: '',
      },
      errorList: [], // 标签类型
    };
  },
  created() {
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.getTagList(); // 获取标签
  },
  async mounted() {
    await this.copySearchDataMx(this.searchData);
  },
  methods: {
    search() {
      this.$emit('search', this.searchData);
    },
    reset() {
      this.selectOrgTree.orgCode = '';
      this.selectTree.regionCode = '';
      this.$refs.uiSelectTabs && this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('reset', this.searchData);
      });
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.choosedOrg = val;
      // this.$emit('startSearch', this.searchData, this.choosedOrg)
    },
    // 行政区划
    selectedArea(data) {
      this.searchData.regionCode = data.regionCode;
    },
    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
    },
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.allTagList = res.data.data;
        this.errorList = this.$util.common.deepCopy(
          this.allTagList.map((row) => {
            return {
              name: row.tagName,
              id: row.tagId,
            };
          }),
        );
        // 已选择标签回显选中状态
        const { tagIds = [] } = this.searchData;
        if (tagIds.length > 0) {
          this.setTagsStatus(tagIds);
        }
      } catch (err) {
        console.log(err);
      }
    },
    // // 标签类型选中状态回显
    // setTagsStatus(tagids) {
    //   this.tagList = this.tagList.map((item) => {
    //     if (tagids.includes(item.id)) {
    //       this.$set(item, 'select', true)
    //     }
    //     return item
    //   })
    // },
    // // 查询所有标签
    // async getTagList() {
    //   try {
    //     let res = await this.$http.post(taganalysis.getDeviceTag, {
    //       isPage: false,
    //     })
    //     this.allTagList = res.data.data
    //     this.tagList = this.$util.common.deepCopy(
    //       this.allTagList.map((row) => {
    //         return {
    //           name: row.tagName,
    //           id: row.tagId,
    //         }
    //       })
    //     )
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
  },
  computed: {
    selectTabsVisible() {
      const selectTabsArr = this.formItemData.filter((item) => item.type === 'selecttabs') || [];
      return selectTabsArr.length;
    },
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {
    searchConditions: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val) {
          this.searchData = val;
        }
      },
    },
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>

<style lang="less" scoped>
.right-margin {
  margin-right: 30px;
}

.search-list {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.right-margin {
  margin-right: 15px;
}

.input-width {
  width: 140px;
}

.select-input-width {
  width: 163px;
}

.align-flex {
  display: flex;
  align-items: center;
}

.leftSelect {
  width: 120px !important;
}

.rightSelect {
  width: 240px !important;
}

.data-list {
  display: flex;
  align-items: center;
  width: 100%;
  color: var(--color-content);
  font-size: 14px;
}

.select-tabs {
  flex: 1;
  display: flex;
  align-items: center;
}
.ui-select-tabs {
  flex: 1;
}
.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}
</style>
