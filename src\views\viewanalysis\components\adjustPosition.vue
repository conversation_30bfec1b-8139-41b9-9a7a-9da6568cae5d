<template>
  <Modal
    ref="modal"
    title="定位"
    v-model="isShow"
    width="1000"
    @on-cancel="cancleOperation"
  >
    <template>
      <div class="drag-dialog-container-file" v-if="isShow">
        <mapPosition
          ref="map"
          @inited="onInited"
          @mouseClickPoint="showPoint"
        ></mapPosition>
      </div>
    </template>
    <div
      slot="footer"
      style="padding: 10px; text-align: center"
      class="nui-border"
    >
      <Button v-if="!noEdit" class="mr-20" @click="cancleOperation"
        >取消</Button
      >
      <Button v-if="!noEdit" type="primary" @click="savePosition">保存</Button>
    </div>
  </Modal>
</template>
<script>
import mapPosition from "@/components/map/map-position.vue";
export default {
  name: "adjustPosition",
  props: ["value", "pointsData", "noEdit"],
  data: function () {
    return {
      isShow: false,
      isShowDragDialog: false,
      isShowMarkerDetail: false,
      isShowFaceOrBodyDetail: false,
      mapData: [],
      pointLon: 0,
      opt: {
        isShowLocationBar: true,
      },
      pointLat: 0,
      realTimeData: {
        structureType: {
          vehicle: false,
          face: false,
          name: "",
          human: false,
        },
      },
      nameCache: [],
      modalOptions: {
        title: "",
        maskClose: false,
        top: 40,
      },
      activePoint: {},
    };
  },
  watch: {
    value: {
      handler(val) {
        this.isShow = val;
      },
      immediate: true,
    },
    isShow(val) {
      this.$emit("input", val);
    },
  },
  components: {
    mapPosition,
  },
  methods: {
    savePosition() {
      let points = [];
      let longitude = this.activePoint.lon;
      let latitude = this.activePoint.lat;
      let position = `${longitude} ${latitude}`;
      this.pointsData.forEach((item) => {
        item.x = item.longitude = longitude;
        item.y = item.latitude = latitude;
        item.position = position;
      });
      this.isShowDragDialog = false;
      this.$emit("closeDragDialog", this.pointsData);
    },
    cancleOperation() {
      let root = this;
      root.isShowDragDialog = false;
      root.isShowMarkerDetail = false;
      root.isShowFaceOrBodyDetail = false;
      root.mapData = [];
      root.pointLon = 0;
      root.pointLat = 0;
      root.realTimeData = {
        structureType: {
          vehicle: false,
          face: false,
          name: "",
          human: false,
        },
      };
      root.nameCache = [];
      root.$emit("closeDragDialog");
    },
    onInited() {
      if (this.pointsData) {
        this.showPoint(this.pointsData, true);
      }
    },
    showPoint(points, flag) {
      if (!flag && this.noEdit) return;
      if (this.markers) {
        this.$refs.map.removeAllMarkers("POSITION", this.markers);
      }
      var markersParam = [];
      if (points.constructor != Array) {
        points.longitude = points.lon ? points.lon : 0;
        points.latitude = points.lat ? points.lat : 0;
        this.activePoint = points;
        if (
          points.longitude === undefined ||
          points.longitude === 0 ||
          points.latitude === undefined ||
          points.latitude === 0
        ) {
          return;
        }
        markersParam.push({
          longitude: points.longitude,
          latitude: points.latitude,
          markerType: "DEFAULT",
          iconNormal: null,
          iconHover: null,
          label: {},
          zIndex: 10,
          data: points.data,
        });
      } else {
        for (let i = 0; i < points.length; i++) {
          const p = points[i];
          if (
            p.longitude === undefined ||
            p.longitude === 0 ||
            p.latitude === undefined ||
            p.latitude === 0
          ) {
            continue;
          }
          markersParam.push({
            longitude: p.longitude,
            latitude: p.latitude,
            markerType: "DEFAULT",
            iconNormal: null,
            iconHover: null,
            label: {},
            zIndex: 10,
            data: p.data,
          });
        }
      }
      this.markers = this.$refs.map.addMarkers("POSITION", markersParam, {});
    },
  },
};
</script>
<style lang="less">
.drag-dialog-fileStructure-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(102, 102, 102, 0.7);
  z-index: 999;
  display: table-cell;
  text-align: center;
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-active {
  opacity: 0;
}
.drag-dialog-fileStructure-head {
  height: 40px;
  line-height: 40px;
  .drag-dialog-fileStructure-Title {
    display: inline-block;
    position: relative;
    left: 0;
  }
  .closeBtn {
    position: relative;
    right: 0;
    display: inline-block;
    cursor: pointer;
    z-index: 616;
  }
}

.drag-dialog-container-file {
  width: 100%;
  height: 600px;
  background-color: #fff;
  z-index: 999;
  cursor: move;
  position: relative;
  .drag-dialog-content-file {
    width: 100%;
    height: 100%;
  }
  .tabMap {
    position: relative;
  }
}
</style>
