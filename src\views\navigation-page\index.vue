<template>
  <div class="margin-wrapper-flow height-full">
    <top-title @backHome="() => $emit('backHome')"></top-title>
    <div class="homepage height-full">
      <div class="layout-left fl">
        <div class="box1 custom-shadow">
          <span class="box-desc">第三方系统接入</span>
        </div>
        <div class="box2 custom-shadow">
          <span class="box-desc">数据治理过程</span>
        </div>
      </div>
      <div class="layout-right">
        <div class="third-party">
          <!--   视图采集 -->
          <div class="third-party-view-wrapper">
            <div class="view-title custom-shadow flex-col">
              <span class="font-size-16 text-shadow-blue title-color">视图采集</span>
            </div>
            <div class="third-party-view-inner">
              <div class="view-item">
                <p class="view-item-title text-shadow-blue title-color">下级上报</p>
                <div class="view-item-img">
                  <img src="~@/assets/img/navigation-page/shangxiaji.png" alt="" />
                </div>
              </div>
              <div class="view-item">
                <p class="view-item-title text-shadow-blue title-color">视频监控</p>
                <div class="view-item-img">
                  <img src="~@/assets/img/navigation-page/video.png" alt="" />
                </div>
              </div>
              <div class="view-item">
                <p class="view-item-title text-shadow-blue title-color">人脸抓拍机</p>
                <div class="view-item-img">
                  <img src="~@/assets/img/navigation-page/face.png" alt="" />
                </div>
              </div>
              <div class="view-item">
                <p class="view-item-title text-shadow-blue title-color">车辆卡口</p>
                <div class="view-item-img">
                  <img src="~@/assets/img/navigation-page/car.png" alt="" />
                </div>
              </div>
            </div>
          </div>
          <div class="third-party-view-keyframe">
            <keyframe-arrow long="4.5%" translate="440%"></keyframe-arrow>
          </div>
          <!--  视图汇聚 -->
          <div class="third-party-converge-wrapper">
            <div class="title">
              <div class="converge-title custom-shadow converge-title-left flex-col">
                <span class="font-size-16 text-shadow-blue title-color">视图汇聚</span>
              </div>
              <div class="converge-keyframe">
                <keyframe-arrow long="5.5%" translate="560%"></keyframe-arrow>
              </div>
              <div class="converge-title custom-shadow converge-title-right flex-col">
                <span class="font-size-16 text-shadow-blue title-color">解析聚档</span>
              </div>
              <div class="converge-keyframe2">
                <keyframe-arrow long="4.5%" translate="440%"></keyframe-arrow>
              </div>
            </div>
            <div class="third-party-converge-inner">
              <div class="converge-item">
                <p class="converge-item-title text-shadow-blue title-color">联网共享</p>
                <div class="converge-item-img">
                  <img src="~@/assets/img/navigation-page/lianwang.png" />
                </div>
              </div>
              <div class="converge-item">
                <p class="converge-item-title text-shadow-blue title-color">一机一档</p>
                <div class="converge-item-img">
                  <img src="~@/assets/img/navigation-page/yijiyidang.png" />
                </div>
              </div>
              <div class="converge-item">
                <p class="converge-item-title text-shadow-blue title-color">解析系统</p>
                <div class="converge-item-img">
                  <img src="~@/assets/img/navigation-page/jiexi.png" />
                </div>
              </div>
            </div>
          </div>
          <!--  存储分析 -->
          <div class="third-party-save-wrapper">
            <div class="title">
              <div class="save-title custom-shadow save-title-left flex-col">
                <span class="font-size-16 text-shadow-blue title-color">存储分析</span>
              </div>
              <div class="save-keyframe">
                <keyframe-arrow long="4.5%" translate="450%"></keyframe-arrow>
              </div>
              <div class="save-title custom-shadow save-title-right flex-col">
                <span class="font-size-16 text-shadow-blue title-color">数据服务</span>
              </div>
              <div class="save-keyframe2">
                <keyframe-arrow long="4.5%" translate="450%"></keyframe-arrow>
              </div>
            </div>
            <div class="third-party-save-inner">
              <div class="save-inner-title">
                <span class="text-shadow-blue title-color">视图库</span>
              </div>
              <div class="save-inner-wrapper">
                <div class="save-item">
                  <p class="save-item-title text-shadow-blue title-color">原始库</p>
                  <div class="save-item-img">
                    <img src="~@/assets/img/navigation-page/yuanshi.png" />
                  </div>
                </div>
                <div class="save-item">
                  <p class="save-item-title text-shadow-blue title-color">主题库</p>
                  <div class="save-item-img">
                    <img src="~@/assets/img/navigation-page/zhuti.png" />
                  </div>
                </div>
                <div class="save-item">
                  <p class="save-item-title text-shadow-blue title-color">专属库</p>
                  <div class="save-item-img">
                    <img src="~@/assets/img/navigation-page/zhuanshuku.png" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--  实战应用 -->
          <div class="third-party-practice-wrapper">
            <div class="practice-title custom-shadow flex-col">
              <span class="font-size-16 text-shadow-blue title-color">实战应用</span>
            </div>
            <div class="third-party-practice-inner">
              <div class="practice-item">
                <p class="practice-item-title text-shadow-blue title-color">视综系统</p>
                <div class="practice-item-img">
                  <img src="~@/assets/img/navigation-page/shizongxitong.png" />
                </div>
              </div>
              <div class="practice-item">
                <p class="practice-item-title text-shadow-blue title-color">公安大数据</p>
                <div class="practice-item-img">
                  <img src="~@/assets/img/navigation-page/gongandashuju.png" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- line-->
        <div class="line">
          <div class="line-line">
            <p class="text-shadow-blue title-color">数据网关</p>
            <line-star class="line-star"></line-star>
          </div>
          <div class="line-keyframe1">
            <keyframe-arrow long="4.5vh" :direction="90" translate="3.5vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe2">
            <keyframe-arrow long="3.8vh" :direction="270" translate="2.7vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe3">
            <keyframe-arrow long="10vh" :direction="90" translate="9vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe4">
            <keyframe-arrow long="3.8vh" :direction="90" translate="2.7vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe5">
            <keyframe-arrow long="3.7vh" :direction="270" translate="2.7vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe6">
            <keyframe-arrow long="3.7vh" :direction="90" translate="2.7vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe7">
            <keyframe-arrow long="3.7vh" :direction="270" translate="2.7vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe8">
            <keyframe-arrow long="3.8vh" :direction="90" translate="2.8vh"></keyframe-arrow>
          </div>
          <div class="line-keyframe9">
            <keyframe-arrow long="3.7vh" :direction="270" translate="2.7vh"></keyframe-arrow>
          </div>
        </div>

        <!-- 数据质量检测-->
        <div class="data">
          <div class="data-item">
            <div class="item-desc custom-shadow">
              <p class="text-shadow-blue title-color">数据质量检测</p>
              <p class="item-title2">20+质量检测模型，多算法交叉验证</p>
              <p>
                <span class="item-label">检测总量：</span>
                <span class="item-value-green">{{ commaNumber(allData?.totalByCheck ?? 0) }}</span>
              </p>
              <p>
                <span class="item-label">异常数据：</span>
                <span class="item-value-red">{{ commaNumber(allData?.unqualifiedByCheck ?? 0) }}</span>
              </p>
            </div>
            <div class="item-img">
              <img src="~@/assets/img/navigation-page/shujuzhiliang.png" alt="" />
            </div>
            <div class="angle-dashed dashed"></div>
            <div class="shujuzhiliang-keyframe">
              <keyframe-arrow long="16.9%" translate="500%"></keyframe-arrow>
            </div>
          </div>
          <div class="data-item">
            <div class="item-desc custom-shadow">
              <p class="text-shadow-blue title-color">数据优化校准</p>
              <p class="item-title2">20+优化校准模型，多算法推荐选举</p>
              <p>
                <span class="item-label">优化总量：</span>
                <span class="item-value-green">{{ commaNumber(allData?.totalByOptimize ?? 0) }}</span>
              </p>
              <!-- <p>
                <span class="item-label">异常数据：</span>
                <span class="item-value-red">{{
                  commaNumber(optimizeObj.existingExceptionCount)
                }}</span>
              </p> -->
            </div>
            <div class="item-img item-img-align">
              <img src="~@/assets/img/navigation-page/shujuyouhua.png" alt="" />
            </div>
            <!-- <div class="data-keyframe">
              <keyframe-arrow
                long="15%"
                :direction="90"
                translate="500%"
              ></keyframe-arrow>
            </div> -->
            <!-- 备存库  -->
            <!-- <div class="item-package">
              <span class="package-title">备存库</span>
              <img src="~@/assets/img/navigation-page/beicunku.png" alt="" />
            </div> -->
            <div class="angle-dashed dashed"></div>
            <div class="shujuzhiliang-keyframe">
              <keyframe-arrow long="16.9%" translate="500%"></keyframe-arrow>
            </div>
          </div>
          <div class="data-item">
            <div class="item-desc custom-shadow">
              <p class="text-shadow-blue title-color">治理评价考核</p>
              <p class="item-title2">多维治理评测指标模型，自定义考核方案</p>
              <p>
                <span class="item-label">评价指标：</span>
                <span class="item-value-green">{{ commaNumber(allData?.totalByExamineIndex ?? 0) }}</span>
              </p>
              <p>
                <span class="item-label">不达标项：</span>
                <span class="item-value-red">{{ commaNumber(allData?.unqualifiedByExamineIndex ?? 0) }}</span>
              </p>
            </div>
            <div class="item-img">
              <img src="~@/assets/img/navigation-page/zhilipingjia.png" alt="" />
            </div>
            <div class="angle-dashed dashed"></div>
          </div>
        </div>
        <div class="vertical-dashed-container">
          <div class="vertical-dashed dashed left-dashed"></div>
          <div class="vertical-dashed dashed medium-dashed"></div>
          <div class="vertical-dashed dashed right-dashed"></div>
        </div>

        <div class="table">
          <div class="data-table custom-shadow">
            <div class="data-container">
              <div class="table-row">
                <div class="row-title"></div>
                <div class="row-data">
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">设备总量</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">检测数量</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">合格数量</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">不合格数量</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">合格率</span>
                  </div>
                </div>
              </div>
              <!-- 数据质量检测统计 -->
              <div class="table-row" v-for="(item, index) in tableData" :key="index">
                <div class="row-title">
                  <i class="icon-font icon-bg font-size-25" :class="tableData[index].icon"></i>
                  <p class="row-name ellipsis text-shadow-blue">
                    {{ tableData[index].moduleName }}
                  </p>
                </div>
                <div class="row-data">
                  <!-- 第一行 -->
                  <p class="cell cell-1 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.total ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <!-- 第二行 -->
                  <p class="cell cell-1 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.checkTotal ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>

                  <p class="cell cell-2 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.qualified ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-5 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.unqualified ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-4 ellipsis">
                    {{ toPercent(allData?.[item.filedName]?.qualifiedPercentage ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="data-table custom-shadow">
            <div class="data-container">
              <!-- <div class="table-row">
                <div class="row-title"></div>
                <div class="row-data">
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label ">优化总量</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">优化成功</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">无法优化</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">优化占比</span>
                  </div>
                </div>
              </div> -->
              <div class="table-row table-row_flex">
                <div class="row-title" v-for="(item, index) in optimizeList" :key="index">
                  <p class="icon-font icon-bg font-size-25" :class="item.icon"></p>
                  <p class="row-name ellipsis">
                    {{ item.moduleName }}
                  </p>
                  <p class="row-num ellipsis mt-lg">
                    {{ commaNumber(allData?.[item.filedName] ?? 0) }}
                  </p>
                  <bottom-star class="bottom-star"></bottom-star>
                </div>
                <!--
                <div class="row-data">
                  <p class="cell cell-1 ellipsis">
                    {{ commaNumber(item.existingExceptionCount) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-2 ellipsis">
                    {{ commaNumber(item.governanceOptimizationCount) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-4 ellipsis">
                    {{ commaNumber(item.unableOptimize) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-5 ellipsis">
                    {{ item.governanceOptimizationRate }}%
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                </div>-->
              </div>
            </div>
          </div>
          <div class="data-table custom-shadow">
            <div class="data-container">
              <div class="table-row">
                <div class="row-title"></div>
                <div class="row-data">
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">评测指标</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">达标指标</span>
                  </div>
                  <div class="row-label ellipsis">
                    <green-circle></green-circle>
                    <span class="data-label">不达标指标</span>
                  </div>
                </div>
              </div>
              <div class="table-row" v-for="(item, index) in otherTableData" :key="index">
                <div class="row-title">
                  <p class="icon-font icon-bg font-size-25" :class="otherTableData[index].icon"></p>
                  <p class="row-name ellipsis text-shadow-blue title-color">
                    {{ otherTableData[index].moduleName }}
                  </p>
                </div>
                <div class="row-data">
                  <p class="cell cell-1 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.total ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-2 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.qualified ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                  <p class="cell cell-3 ellipsis">
                    {{ commaNumber(allData?.[item.filedName]?.unqualified ?? 0) }}
                    <bottom-star class="bottom-star"></bottom-star>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
@import 'index';
</style>
<script>
import { mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'navigation-page',
  data() {
    return {
      evaluationData: {},
      IndexSumCheckCount: {},
      IndexTopicCheckCount: [],
      tableData: Object.freeze([
        { icon: 'icon-shitujichushuju', moduleName: '设备资产', filedName: 'basicCheckDetail' },
        { icon: 'icon-zhongfutuxiangshibiechuli', moduleName: '人卡/车辆', filedName: 'imageCheckDetail' },
        { icon: 'icon-shipinliushuju', moduleName: '视频监控', filedName: 'videoCheckDetail' },
        // { icon: 'icon-shipinliushuju', moduleName: '视频流数据' },
        // { icon: 'icon-zhongdianrenyuanshuju', moduleName: '重点人员数据' },
      ]),
      otherTableData: Object.freeze([
        { icon: 'icon-shitujichushuju', moduleName: '视图基础数据', filedName: 'basicExamineIndex' },
        { icon: 'icon-zhongfutuxiangshibiechuli', moduleName: '图像数据', filedName: 'imageExamineIndex' },
        { icon: 'icon-shipinliushuju', moduleName: '视频流数据', filedName: 'videoExamineIndex' },
        { icon: 'icon-zhongdianrenyuanshuju', moduleName: '重点人员数据', filedName: 'focusExamineIndex' },
      ]),
      optimizeObj: {},
      optimizeList: Object.freeze([
        { icon: 'icon-shizhongzhongshe', moduleName: '时钟重设', filedName: 'optimizeByClock' },
        { icon: 'icon-zimuzhongshe', moduleName: '字幕重设', filedName: 'optimizeByOsd' },
        { icon: 'icon-gongnengleixingzhili', moduleName: '功能类型治理', filedName: 'optimizeBySbgnlx' },
        { icon: 'icon-dianweileixingzhili', moduleName: '点位类型治理', filedName: 'optimizeBySbdwlx' },
        { icon: 'icon-MACdizhizhili', moduleName: 'MAC地址治理', filedName: 'optimizeByMac' },
        { icon: 'icon-kongjianxinxizhili', moduleName: '空间信息治理', filedName: 'optimizeBySpace' },
        { icon: 'icon-caijiquyuguanli', moduleName: '采集区域管理', filedName: 'optimizeBySbcjqy' },
        { icon: 'icon-governanceorder', moduleName: '治理工单', filedName: 'optimizeByWorkOrder' },
      ]),
      allData: {},
    };
  },
  created() {
    this.getFlowChart();
  },
  methods: {
    async getFlowChart() {
      try {
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getFlowChart);
        this.allData = data;
      } catch (error) {
        console.log(error);
      }
    },
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
    toPercent(point) {
      if (point) {
        let str = Number(point * 100).toFixed(2);
        return `${str}%`;
      }
      return 0;
    },
    //查厂商
    // factory_list() {
    //   this.$http.get(algorithm.dictData + 'algorithmVendorType').then((res) => {
    //     let a = JSON.stringify(res.data.data);
    //     sessionStorage.setItem('getfacturer', a);
    //   });
    // },
  },
  watch: {},
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  props: {},
  components: {
    TopTitle: require('./top-title').default,
    BottomStar: require('./bottom-star').default,
    KeyframeArrow: require('./keyframe-arrow.vue').default,
    LineStar: require('./line-star').default,
    GreenCircle: require('./green-circle').default,
  },
};
</script>
