<template>
  <div class="operate-bar">
    <!-- <div class="operate-item"><i class="iconfont icon-shoucang color-warning"></i> <span>收藏</span></div> -->
    <div class="operate-item" v-if="myFavorite == '1'" @click="collection(2)">
      <i class="iconfont icon-yishoucang color-warning"></i> <span>收藏</span>
    </div>
    <div class="operate-item" v-else @click="collection(1)">
      <i class="iconfont icon-shoucang color-warning"></i> <span>收藏</span>
    </div>
    <!-- <div class="operate-item">
      <ui-btn-tip class="collection-icon" v-if="myFavorite == '1'" content="收藏" icon="icon-yishoucang" transfer @click.native="collection(2)" />
      <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(1)" />
    </div> -->
    <!-- <div class="operate-item"><i class="iconfont icon-fenxi color-primary"></i> <span>分析</span></div>
    <div class="operate-item"><i class="iconfont icon-dunpai color-error"></i> <span>布控</span></div>
    <div class="operate-item">
      <i class="iconfont icon-eye"></i> <span>浏览：{{ this.$parent.$parent.views }}</span>
    </div> -->
  </div>
</template>
<script>
import { addCollection, deleteMyFavorite } from "@/api/user";
export default {
  name: `operateBar`,
  props: {
    baseInfo: {
      type: Object,
      default: {},
    },
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    hideIcon: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      myFavorite: "0",
    };
  },
  watch: {
    baseInfo: {
      handler(val) {
        console.log("---- 信息更新", val);
        if (val) {
          this.myFavorite = val.myFavorite;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    // console.log('路由', this.$route.query, this.baseInfo)
  },
  methods: {
    /**
     * 收藏
     */
    collection(flag) {
      var { source, archiveNo, deviceId } = Object.keys(this.$route.query)
        .length
        ? this.$route.query
        : JSON.parse(sessionStorage.getItem("query"));
      var type = null;
      var id = null;
      switch (source) {
        case "people":
          type = 1;
          id = archiveNo;
          break;
        case "video":
          type = 2;
          id = archiveNo;
          break;
        case "car":
          type = 3;
          id = JSON.parse(archiveNo);
          break;
        case "vehicle":
          type = 3;
          id = archiveNo;
          break;
        case "device":
          type = 4;
          id = deviceId;
          break;
        case "zdr":
          type = 19;
          id = archiveNo;
          break;
        case "non-motor-vehicle":
          type = 18;
          id = JSON.parse(archiveNo);
          break;
        case "place":
          type = 20;
          id = archiveNo;
          break;
        // 目前设备档案路由并没有携带source，且设备id使用的是archiveNo，所以走到default
        default:
          type = 4;
          id = archiveNo;
          break;
      }
      var param = {
        favoriteObjectId: id,
        favoriteObjectType: type,
      };

      if (flag == 1) {
        addCollection(param).then((res) => {
          this.myFavorite = 1;
          this.$Message.success("收藏成功");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.myFavorite = 0;
          this.$Message.success("取消收藏成功");
        });
      }
    },
  },
};
</script>
