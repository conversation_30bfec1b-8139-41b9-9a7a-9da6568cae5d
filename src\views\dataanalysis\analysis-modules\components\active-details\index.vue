<template>
  <div class="auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #phyStatus="{ row }">
        <span
          :class="{
            'qualified-color': row.phyStatus === '1',
            'unqualified-color': row.phyStatus === '2',
          }"
        >
          {{ row.phyStatusText || '--' }}
        </span>
      </template>
      <template #shotTotalNum="{ row }">
        <span
          v-if="row.shotTotalNum || row.shotTotalNum === 0"
          :class="{ 'underline-text': row.shotTotalNum !== 0 }"
          @click="openCaptureEchart(row, 'shotTotalNum')"
        >
          {{ row.shotTotalNum }}
        </span>
        <span v-else>--</span>
      </template>
      <template #avgNum="{ row }">
        <span
          v-if="row.avgNum || row.avgNum === 0"
          :class="{ 'underline-text': row.avgNum !== 0 }"
          class="unqualified-color"
          @click="openCaptureEchart(row, 'avgNum')"
        >
          {{ row.avgNum }}
        </span>
        <span v-else>--</span>
      </template>
      <template #nearbyAvgNum="{ row }">
        <span
          v-if="row.nearbyAvgNum || row.nearbyAvgNum === 0"
          :class="{ 'underline-text': row.nearbyAvgNum !== 0 }"
          @click="openCaptureEchart(row, 'nearbyAvgNum')"
        >
          {{ row.nearbyAvgNum }}
        </span>
        <span v-else>--</span>
      </template>
      <template #avgPercent="{ row }">
        <span
          v-if="row.avgPercent || row.avgPercent === 0"
          :class="{ 'underline-text': row.avgPercent !== 0 }"
          class="unqualified-color"
          @click="openCaptureEchart(row, 'avgPercent')"
        >
          {{ row.avgPercent }}%
        </span>
        <span v-else>--</span>
      </template>
      <template #shotDownCount="{ row }">
        <span
          :class="{ 'underline-text': row.shotDownCount !== 0 }"
          class="unqualified-color"
          @click="openCaptureDownDetailts(row, 'shotDownCount')"
        >
          {{ row.shotDownCount }}
        </span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
    </ui-table>
    <slot name="page"></slot>

    <!-- 抓拍趋势图表 -->
    <ui-modal
      v-model="showCaptureEchart"
      class="echarts-modal"
      :title="captureEchartTitle"
      :styles="stylesCaptureTrend"
      footer-hide
    >
      <CaptureTrend v-if="showCaptureEchart" :current-row="currentRow"></CaptureTrend>
    </ui-modal>

    <!-- 抓拍突降详情 -->
    <ui-modal v-model="showCaptureDown" class="capture-modal" title="抓拍突降详情" footer-hide>
      <ui-table
        class="vertical-table"
        :stripe="false"
        :table-columns="captureTableColumns"
        :table-data="captureTableData"
        :loading="captureLoading"
        :row-class-name="rowClassName"
      >
      </ui-table>
    </ui-modal>
  </div>
</template>
<script>
import dataAnalysis from '@/config/api/dataAnalysis.js';
export default {
  name: 'active-details',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      styles: {
        width: '9.45rem',
      },
      formData: {},
      // 抓拍趋势图表
      stylesCaptureTrend: {
        width: '7.45rem',
      },
      showCaptureEchart: false,
      currentRow: {},
      captureEchartTitle: '近X日抓拍趋势',
      // 抓拍突降详情
      showCaptureDown: false,
      captureTableColumns: [],
      captureTableData: [],
      captureLoading: false,
    };
  },
  methods: {
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
    // 近X日抓拍趋势 图表
    openCaptureEchart(row, str = '') {
      if (row[str] === 0) return;
      this.showCaptureEchart = true;
      this.currentRow = { ...row };
      this.captureEchartTitle = `近${this.rowData?.detail?.staDayNum || 'x'}日抓拍趋势`;
    },
    // 抓拍突降 详情
    openCaptureDownDetailts(row, str = '') {
      if (row[str] === 0) return;
      this.showCaptureDown = true;
      this.currentRow = { ...row };
      this.getCaptureDownData();
    },
    async getCaptureDownData() {
      try {
        this.captureLoading = true;
        let { batchId } = this.$route.query;
        let data = {
          batchId: batchId,
          customParameters: {
            deviceId: this.currentRow.deviceId || '',
          },
        };
        let res = await this.$http.post(dataAnalysis.getTableInfo, data);
        this.handlerTable(res.data.data || {});
      } catch (error) {
        console.log(error);
      } finally {
        this.captureLoading = false;
      }
    },
    // 处理表格信息
    handlerTable(data) {
      let columns = [];
      let fileds = {};
      // 1. 处理表头
      data.headers.forEach((item, index) => {
        let tableData = data.body.filter((bodyItem) => bodyItem.code === item.code);
        fileds[item.code] = tableData[0] ? { ...tableData[0] } : {};

        if (index === 0) {
          columns.push({
            title: '  ',
            key: `${item.code}`,
            fixed: 'left',
            align: 'center',
            width: 180,
            className: 'custom-col-style',
            renderHeader: (h) => {
              return h(
                'div',
                {
                  attrs: {
                    class: 'type',
                  },
                },
                [
                  h(
                    'span',
                    {
                      attrs: {
                        class: 'category-span',
                      },
                    },
                    '类别',
                  ),
                  h(
                    'span',
                    {
                      attrs: {
                        class: 'time-span',
                      },
                    },
                    '时间',
                  ),
                ],
              );
            },
            render: (h, { row }) => {
              return <span>{row.name || ''}</span>;
            },
          });
        } else {
          columns.push({
            title: '2024-04-15（星期一）',
            key: `${item.code}`,
            minWidth: 120,
            align: 'center',
            renderHeader: (h) => {
              return h('div', [h('p', item.code), h('p', `( ${item.desc} )`)]);
            },
            render: (h, { row }) => {
              return <span>{row[item.code]?.[row.key] || ''}</span>;
            },
          });
        }
      });
      this.captureTableColumns = columns;

      /* 
        2. 处理表格内容   
            前后端定义 --- data.body[0]: 第一项作为  表格第一列 -- 表格行显示的具体内容，对象中有多少个属性（排除code），就代表有多少行；
                          其中  key 为取值字段、   value 为具体值；
                          排除 code：对应的是 表头headers 中的code值，为该列表头的取值字段
      */
      let table = [];
      if (data.body.length > 0) {
        Object.keys(data.body[0]).forEach((key) => {
          if (key !== 'code') {
            table.push({
              key: key,
              name: data.body[0][key],
              ...fileds,
            });
          }
        });
      }
      this.captureTableData = table;
    },
    rowClassName(row, index) {
      if (index === this.captureTableData.length - 1) {
        return 'red-row';
      }
      return '';
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    CaptureTrend: require('./capture-trend.vue').default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}
.underline-text {
  text-decoration: underline;
  cursor: pointer;
}
.unqualified-color {
  color: var(--color-failed);
}
.qualified-color {
  color: var(--color-success);
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
@{_deep}.result-span {
  .ivu-table-cell-tooltip-content {
    .unqualified-color;
  }
}
@{_deep}.echarts-modal {
  .ivu-modal-body {
    height: 550px;
  }
}

@{_deep}.capture-modal {
  .ivu-modal {
    width: 6.45rem !important;
  }
}

@{_deep} .ivu-table-fixed-header {
  .custom-col-style {
    padding: 0;
    width: 100%;
    height: 100%;
    .ivu-table-cell {
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 !important;
      width: 100%;
      height: 100%;
    }
    .type {
      position: relative;
      width: 100%;
      height: 100%;
      z-index: 0;
      background-color: var(--border-table);
      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        background-color: var(--bg-table-header-th);
        clip-path: polygon(100% calc(100% - 0.4px), 100% 0px, 0px -0.4px);
      }
      &:before {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        background-color: var(--bg-table-header-th);
        clip-path: polygon(0px 0.5px, 0px 100%, calc(100% - 0.5px) calc(100% + 0.5px));
      }
      .category-span {
        position: absolute;
        left: 35px;
        bottom: 15px;
        z-index: 1;
      }
      .time-span {
        position: absolute;
        right: 30px;
        top: 21px;
        z-index: 1;
      }
    }
  }
}
@{_deep} .vertical-table {
  height: 340px;
  .ivu-table {
    background-color: var(--bg-table) !important;
    td,
    th {
      height: 81px;
      color: var(--color-table-header-th);
    }
    .ivu-table-header th {
      border-right: 1px solid var(--border-table);
      border-left: 1px solid var(--border-table);
      border-top: 1px solid var(--border-table);
      color: var(--color-table-header-th);
    }
    .ivu-table-tbody td {
      border: 1px solid var(--border-table);
      background-color: var(--bg-table);
      color: var(--color-table-header-th);
    }
    .ivu-table-fixed-shadow {
      box-shadow: none !important;
    }
    th.custom-col-style {
      border-top: 1px solid var(--border-table);
      border-left: 1px solid var(--border-table);
      color: var(--color-table-header-th);
    }
    td.custom-col-style {
      background: var(--bg-sub-content) !important;
      color: var(--color-table-header-th);
    }
  }

  .red-row .ivu-table-column-center:not(.custom-col-style) {
    .unqualified-color;
  }
}
</style>
