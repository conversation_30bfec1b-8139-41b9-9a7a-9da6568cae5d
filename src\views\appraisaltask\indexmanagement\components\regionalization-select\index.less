//@{_deep} .ivu-modal {
//  width: 1563px !important;
//}
@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
.w100 {
  width: 100px;
}
.w160 {
  width: 160px;
}
.lh-34 {
  line-height: 34px;
}
.area-container {
  border: 1px solid var(--border-modal-footer);
  .tree-filter {
    line-height: 34px;
    margin-bottom: 30px;
  }
  .tree-title {
    display: flex;
    width: 100%;
    height: 48px;
    line-height: 48px;
    padding: 0 15px;
    background: var(--bg-table-header-th);
    span {
      display: inline-block;
    }
  }

  .tree-wrapper {
    height: 500px;
    overflow: auto;
    .ui-search-tree {
      height: 100%;
    }
    @{_deep} .el-tree {
      .el-tree-node {
        .el-tree-node__content {
          height: 40px;
          &.has-child-panel {
            margin-bottom: 5px;
          }
          .custom-tree-node {
            line-height: 34px;
          }
          .el-tree-node__expand-icon {
            margin-left: 20px;
            font-size: 16px;
            margin-top: -2px;
          }
        }
      }
    }
  }
}

@{_deep} .ivu-modal-body {
  max-height: 100% !important;
}
.flex {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.right-item {
  display: flex;
  align-items: center;
}
.org-name,
.left-item {
  flex: 1;
}
.is-all,
.is-checked,
.switch-width,
.check-width {
  width: 100px;
}
.ipt-item {
  display: flex;
  justify-content: left;
}
.mr-50 {
  margin-right: 50px;
}

.mr-100 {
  margin-right: 100px;
}
