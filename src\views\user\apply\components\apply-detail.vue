<template>
	<ui-modal v-model="visible" :title="'申请详情'" :r-width="500" :footer-hide="true">
		<div class="apply-detail">
			<div>申请书：</div>
			<ui-image :src="imgUrl" viewer />
			<div>申请理由：</div>
			<Input v-model="applyMsg" type="textarea" disabled></Input>
			<div>审批描述：</div>
			<Input v-model="handleReason" type="textarea" disabled></Input>
		</div>
	</ui-modal>
</template>
  <script>

	export default {
		data() {
			return {
				visible: false,
				applyMsg: '案件取证',
				imgUrl: '',
				handleReason: ''
			}
		},
		methods: {
			// 初始化
			show(item) {
				this.imgUrl = item.applyUrl
				this.applyMsg = item.applyReason
				this.handleReason = item.handleReason
				this.visible = true
			},
		}
	}
  </script>
  <style lang="less" scoped>
	.apply-detail {
		.ui-image {
			width: 100%;
			height: 300px;
		}
		/deep/ .ui-image .ui-image-div img {
			width: auto;
		}
		/deep/.ivu-input {
			height: 100px;
		}
	}
</style>
  