<template>
  <div class="collapse-expand">
    <SeeksRelationGraph ref="seeksRelationGraph" :options="graphOptions" :on-node-click="onNodeClick" :on-line-click="onLineClick" />
    <CheckSelect @checkSelect="checkSelect" v-if="graphOptions.checkSelect" :atlasListChilren="atlasList[0] && atlasList[0].children ? atlasList[0].children : []" />
  </div>
</template>

<script>
import SeeksRelationGraph from './src/index.js'
import imgloading from '@/assets/img/car1.webp'

// http://relation-graph.com/#/docs/graph 官方api地址
export default {
  name: 'Demo',
  components: {
    SeeksRelationGraph,
    CheckSelect: require('./check-select').default
  },

  props: {
    // 关系图谱数据
    atlasList: {
      type: Array,
      default() {
        return []
      }
    },
    // 图谱配置项，默认graphOptions配置，Object.assign方式覆盖原配置
    options: {
      type: Object,
      default() {
        return {}
      }
    },
    // 圈的大小样式,按层级区分{0：{width:90,height:90,button:false},1:{}}
    styles: {
      type: Object,
      default() {
        return {
          0: { width: 100, height: 100, button: true },
          1: { width: 60, height: 60 },
          2: { width: 60, height: 60 }
        }
      }
    }
  },
  data() {
    return {
      imgloading,
      isShowCodePanel: false,
      graphOptions: {
        layouts: [
          {
            label: '中心',
            layoutName: 'center',
            layoutClassName: 'seeks-layout-center',
            // 节点间距
            distance_coefficient: 0.7
          }
        ],
        // 线条
        defaultLineMarker: {
          markerWidth: 12,
          markerHeight: 12,
          refX: 6,
          refY: 6,
          data: 'M2,2 L10,6 L2,10 L6,6 L2,2'
        },
        moveToCenterWhenResize: true, // 当图谱的大小发生变化时，是否重新让图谱的内容看起来居中
        allowShowMiniToolBar: true, // 工具栏
        allowSwitchLineShape: true, // 是否在工具栏中显示切换线条形状的按钮
        allowShowMiniNameFilter: false, // 搜索框
        hideNodeContentByZoom: false, // 是否根据缩放比例隐藏节点内容
        defaultLineShape: 1, // 默认的线条样式（1:直线/2:样式2/3:样式3/4:折线/5:样式5/6:样式6
        defaultExpandHolderPosition: 'right', // 默认的节点展开/关闭按钮位置（left/top/right/bottom）
        checkSelect: false, // 控制上方筛选展示隐藏
        fontSize: 14 // 默认所有文字字体大小
      },
      // 显示节点，2级为维度
      isHidenList: []
    }
  },
  methods: {
    // 生成关系图谱
    async showSeeksGraph() {
      var data = await this.atlasListMap(this.atlasList, 0)
      // data.links.push({
      //   from: "2",
      //   to: "22",
      //   text: "ceshi1",
      //   fontColor: "#2C86F8",
      //   isHideArrow: true,
      // });
      setTimeout(() => {
        this.$refs.seeksRelationGraph.setJsonData(data, seeksRGGraph => {
          setTimeout(() => {
            // 修改中心点展开关闭样式，使其覆盖节点
            let buttonNode = []
            for (let key in document.getElementsByClassName('c-expand-positon-right')) {
              buttonNode.push(document.getElementsByClassName('c-expand-positon-right')[key])
            }
            buttonNode.forEach((val, index) => {
              if (index == 0) {
                if (val.style) {
                  val.style = 'top:-18px!important'
                }
                if (val.childNodes) {
                  val.childNodes[0].style = 'width: 100px!important;height: 100px!important; border-radius: 50px!important'
                }
              }
            })
          }, 5)
          //  刷新定位
          this.$refs.seeksRelationGraph.refresh()
          // 这些写上当图谱初始化完成后需要执行的代码
        })
      }, 100)
    },
    // 点击节点
    onNodeClick(nodeObject, $event) {
      this.$emit('onNodeClick', nodeObject)
    },
    // 点击线（文字）
    onLineClick(lineObject, $event) {
      this.$emit('onLineClick', lineObject)
    },
    // 处理原生html-类别(2级，颜色根据分类区分，目前无数据，强制写死)
    nodeHtml(node) {
      let backgroundColor = 'background: linear-gradient(135deg, #50CED0 0%, #3EA6E5 100%)'
      switch (node.text) {
        case '同车主':
          backgroundColor = 'background: linear-gradient(135deg, #50CED0 0%, #3EA6E5 100%)'
          break
        case '同抓拍':
          backgroundColor = 'background: linear-gradient(237deg, #EC9240 0%, #F7B93D 100%)'
          break
        case '同涉案':
          backgroundColor = 'background: linear-gradient(144deg, #FF8C5B 0%, #D5210F 100%)'
          break
        case '驾乘人员':
          backgroundColor = 'background: linear-gradient(144deg, #3CD2AA 0%, #1FAF8A 100%)'
          break
      }

      return `<div title="${node.text}" style="width: ${this.styles[1].width}px;height: ${this.styles[1].height}px;${backgroundColor};box-shadow: rgba(68, 179, 223, 0.2) 0px 7px 10px 0px;color: rgb(255, 255, 255);border-radius:  ${this.styles[1].width / 2}px;font-size: ${
        this.graphOptions.fontSize
      }px;text-align: center;line-height:  ${this.styles[1].height}px;overflow: hidden;text-overflow: ellipsis; white-space: nowrap;">
    <div style="width: 22px;height: 22px;background: rgb(255, 255, 255);box-shadow: rgba(0, 0, 0, 0.2) 0px 3px 5px 0px;border-radius: 12px;color: rgb(44, 134, 248);margin-left: 38px;font-size: ${this.graphOptions.fontSize + 2}px;font-weight: bold;position: absolute;line-height: 22px;">
    ${node.num}
    </div>
    ${node.text}
  </div>`
    },
    // 处理原生html-类别下具体信息
    nodeHtmlChildren(node) {
      switch (node.text) {
        case '同车主':
          break
        case '同抓拍':
          break
        case '同涉案':
          break
        case '驾乘人员':
          break
      }
      return `<div style="text-align: center;width: ${this.styles[2].width + 40}px;">
      <div>
        <img style="width: ${this.styles[2].width}px;height: ${this.styles[2].height}px;border-radius: ${this.styles[2].width / 2}px;" src="${node.img}" alt="#">
      </div>
      <div style="background: rgb(249, 249, 249);border-radius: 4px;border: 1px solid rgb(211, 215, 222);display: inline-block;padding: 2px 6px;font-size: ${this.graphOptions.fontSize}px;color: rgba(0, 0, 0, 0.8);margin-top: 10px;">
       ${node.text}
      </div>
    </div>`
    },
    // 处理原生html中心点
    nodeHtmlCenter(val) {
      const node = ` <div style="text-align: center; width: ${this.styles[0].width}px;">
      <div style="width: ${this.styles[0].width}px;height: ${this.styles[0].height}px;background: #ffffff;border: 5px solid rgba(91, 163, 255, 1);border-radius: ${this.styles[0].width}px;display: flex;align-items: center;">
        <img style="width: ${this.styles[0].width * 0.74}px; height: ${this.styles[0].height * 0.74}px; border-radius: ${(this.styles[0].width * 0.74) / 2}px;margin: 0 auto;" src="${val.img}" alt="#" />
      </div>
      <div style="background: rgb(249, 249, 249);border-radius: 4px;border: 1px solid rgb(211, 215, 222);display: ${this.styles[0].button ? 'inline-block' : 'none'};padding: 2px 6px;font-size: ${this.graphOptions.fontSize + 2}px;color: rgba(0, 0, 0, 0.8);margin-top: 10px;">
        ${val.text}
      </div>
    </div>`
      return node
    },
    // 筛选
    checkSelect(isHidenList) {
      this.isHidenList = isHidenList
      this.$nextTick(() => {
        this.showSeeksGraph()
      })
    },
    // 处理关系图谱json数据list:数据 level：层级, line：线开始节点,newList:返回值 isHidden :控制节点隐藏（子集不关联，手动）
    atlasListMap(list, level, line, newList) {
      if (!newList) {
        newList = {
          rootId: '',
          nodes: [],
          links: []
        }
      }
      list.map(val => {
        if (level === 0) {
          newList.rootId = String(val.id)
          newList.nodes.push({
            id: String(val.id),
            html: this.nodeHtmlCenter(val)
          })
        } else if (level === 1) {
          newList.nodes.push({
            id: String(val.id),
            html: this.nodeHtml(val),
            isHide: this.isHidenList.includes(val.id) ? false : true
          })
        } else if (level === 2) {
          console.log(line, this.isHidenList)
          newList.nodes.push({
            id: String(val.id),
            html: this.nodeHtmlChildren(val),
            isHide: this.isHidenList.includes(line) ? false : true
          })
        }

        if (line) {
          let links = {}
          if (val.line) {
            links = {
              from: String(line),
              to: String(val.id),
              text: val.line,
              fontColor: '#2C86F8',
              isHideArrow: true
            }
          } else {
            links = {
              from: String(line),
              to: String(val.id),
              isHideArrow: true
            }
          }
          newList.links.push(links)
        }
        if (val.children && val.children.length > 0) {
          this.atlasListMap(val.children, level + 1, val.id, newList)
        }
      })
      return newList
    }
  },
  watch: {
    atlasList: {
      deep: true,
      handler(val) {
        this.graphOptions = Object.assign(this.graphOptions, this.options)
        if (this.atlasList && this.atlasList[0]) {
          this.isHidenList = this.atlasList[0].children.map(val => {
            return val.id
          })
        }
        this.$nextTick(() => {
          this.showSeeksGraph()
        })
      },
      immediate: true
    },
    // 离开初始化组件
    $route(to, from) {
      if (this.$refs.seeksRelationGraph) {
        this.$refs.seeksRelationGraph.refresh()
      }
    }
  }
}
</script>

<style lang="less">
// 节点样式
.collapse-expand {
  .c-expand-positon-right {
    // left: 0px !important;
    // top: 0px !important;
    // & > span {
    //   display: block !important;
    //   width: 60px !important;
    //   height: 60px !important;
    //   padding: 10px !important;
    //   margin: 0 !important;
    //   border-radius: 30px !important;
    //   opacity: 0;
    // }
  }
  text {
    cursor: pointer;
  }
}
</style>

<style lang="less" scoped>
.collapse-expand {
  height: 100%;
  width: 100%;
}
</style>
