<template>
  <div class="notice-detail">
    <h3>{{ noticeDetail.title }}</h3>
    <div class="scroll-box">
      <section class="content-section">
        <p class="f-12 font-color t-center mb-lg">
          <span class="mr-lg">发布时间：{{ noticeDetail.createTime }}</span>
          <span>浏览次数：{{ noticeDetail.views }}</span>
        </p>
        <span class="f-14">
          {{ noticeDetail.content }}
        </span>
      </section>
      <section class="file-list f-14" v-if="!!noticeDetail.fileVos.length">
        <p class="uploadclass"><i class="icon-font icon-fujianxiazai mr-sm"></i>附件下载</p>
        <ul class="file-list-ul">
          <li class="mt-md flex-aic" v-for="item of noticeDetail.fileVos" :key="item.id">
            <span class="file-name inline ellipsis" :title="item.originalName">{{ item.originalName }}</span>
            <span class="pointer font-active-color" @click="download(item)">下载</span>
          </li>
        </ul>
      </section>
    </div>
  </div>
</template>
<script>
import systemconfig from '@/config/api/systemconfig';
export default {
  name: 'NoticeDetail',
  props: {},
  data() {
    return {
      noticeDetail: {
        title: '',
        createTime: '',
        content: '',
        fileVos: [],
      },
    };
  },
  created() {
    this.getEfficientNotify();
  },
  methods: {
    async getEfficientNotify() {
      try {
        let { data } = await this.$http.get(systemconfig.getEfficientNotifyDetail, {
          params: { views: true, id: this.$route.query.id },
        });
        this.noticeDetail = data.data;
        if ([null, undefined].includes(this.noticeDetail.fileVos)) {
          this.noticeDetail.fileVos = [];
        }
      } catch (err) {
        console.log(err);
      }
    },
    download(item) {
      let url = item.fileUrl;
      let aLink = document.createElement('a');
      aLink.setAttribute('download', item.originalName);
      // aLink.setAttribute('target', '_blank')
      aLink.style.display = 'none';
      aLink.href = url;
      document.body.appendChild(aLink);
      aLink.click();
      document.body.removeChild(aLink);
      window.URL.revokeObjectURL(url);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .notice-detail {
    > h3 {
      border-bottom: 1px solid #507faa;
    }
    .content-section {
      border-bottom: 1px solid var(--devider-line);
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .notice-detail {
    > h3 {
      border-bottom: 1px solid #d3d7de;
    }
    .content-section {
      border-bottom: 1px solid #d3d7de;
    }
  }
}
.notice-detail {
  margin: 10px;
  background-color: var(--bg-content);
  height: 100%;
  > h3 {
    height: 70px;
    line-height: 70px;
    font-size: 20px;
    text-align: center;
    color: var(--color-content);
  }
  .scroll-box {
    height: calc(100% - 100px);
    overflow-y: scroll;
  }
  .content-section {
    padding: 20px;
    text-indent: 2em;
    color: var(--color-content);
  }
  .file-list {
    padding: 20px 10px;
    .file-list-ul {
      > li {
        .file-name {
          color: var(--color-content);
          max-width: 300px;
          margin-right: 30px;
        }
      }
    }
    .uploadclass {
      color: var(--color-display-title);
      i {
        color: var(--color-display-text);
      }
    }
  }
}
.font-color {
  color: var(--color-content);
}
</style>
