<!--
    * @FileDescription: 人体 海康
    * @Author: H
    * @Date: 2023/08/30
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-10 17:20:58
-->
<template>
  <div class="box">
    <div class="data-export">
      <Checkbox @on-change="checkAllHandler" v-model="glCheckAll"
        >全选</Checkbox
      >
      <div class="export-box">
        <Button
          class="mr"
          @click="handleExport($event)"
          size="small"
          v-if="!mapOnData"
        >
          <ui-icon type="daoru" color="#2C86F8"></ui-icon>
          导出
        </Button>
        <exportBox
          ref="exportbox"
          v-if="exportShow"
          @confirm="confirm"
          @cancel="exportShow = false"
        ></exportBox>
      </div>
      <Button
        class="mr"
        :type="hkParams.sortField == 'similarity' ? 'primary' : 'default'"
        @click="handleGlSort('similarity')"
        size="small"
      >
        <Icon type="md-arrow-round-down" v-if="!similUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        相似度排序
      </Button>
      <Button
        class="mr"
        :type="hkParams.sortField == 'absTime' ? 'primary' : 'default'"
        @click="handleGlSort('absTime')"
        size="small"
      >
        <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        时间排序
      </Button>
      <Button @click="dataAboveMapHandler" size="small" style="float: right">
        <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
        数据上图
      </Button>
    </div>
    <div class="table-content">
      <div
        class="list-card box-1"
        :class="{ checked: item.isChecked }"
        v-for="(item, index) in HkDataList"
        :key="index"
      >
        <div class="collection paddingIcon">
          <div class="bg"></div>
          <ui-btn-tip
            class="collection-icon"
            v-if="item.myFavorite == '1'"
            content="取消收藏"
            icon="icon-yishoucang"
            transfer
            @click.native="collection(item, 2)"
          />
          <ui-btn-tip
            class="collection-icon"
            v-else
            content="收藏"
            icon="icon-shoucang"
            transfer
            @click.native="collection(item, 1)"
          />
        </div>
        <Checkbox
          class="check-box"
          v-model="item.isChecked"
          @on-change="(e) => checkHandler(e, index)"
        ></Checkbox>
        <div class="img-content">
          <div class="similarity" v-if="item.similarity">
            <span class="num" v-if="item.similarity"
              >{{ item.similarity }}%</span
            >
          </div>
          <template v-if="childDataSourceVal == 2">
            <ui-image
              :src="item.traitImg"
              alt="动态库"
              @click.native="faceDetailFn(item, index)"
            />
          </template>
        </div>
        <!-- 动态库 -->
        <div class="bottom-info">
          <time>
            <Tooltip
              content="抓拍时间"
              placement="right"
              transfer
              theme="light"
            >
              <i class="iconfont icon-time"></i>
            </Tooltip>
            {{ item.absTime }}
          </time>
          <p>
            <Tooltip
              content="抓拍地点"
              placement="right"
              transfer
              theme="light"
            >
              <i class="iconfont icon-location"></i>
            </Tooltip>
            <!-- <span class="ellipsis" v-show-tips>{{item.detailAddress}}</span> -->
            <ui-textOver-tips
              refName="detailAddress"
              :content="item.deviceName"
            ></ui-textOver-tips>
          </p>
        </div>
        <div class="fast-operation-bar">
          <Poptip trigger="hover" placement="bottom-end">
            <i class="iconfont icon-gengduo"></i>
            <div class="mark-poptip" slot="content">
              <p @click="archivesPage(item, 1)">
                <i class="iconfont icon-renlian1"></i>以图搜图
              </p>
              <p @click="openDirectModel(item)">
                <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
              </p>
              <p @click="handleTargetAdd(item)">
                <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
              </p>
              <p @click="handleGaitPage(item)">
                <i class="iconfont icon-a-lianhe322"></i>人体搜步态
              </p>
            </div>
          </Poptip>
        </div>
      </div>
      <div
        class="empty-card-1"
        v-for="(item, index) of 9 - (HkDataList.length % 9)"
        :key="index + 'demo'"
      ></div>
      <ui-empty v-if="HkDataList.length === 0 && !listLoading"></ui-empty>
      <ui-loading v-if="listLoading"></ui-loading>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="pageInfo.pageNumber"
      :total="total"
      countTotal
      :page-size="pageInfo.pageSize"
      :page-size-opts="[30, 60, 90, 120]"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    >
    </ui-page>
    <details-modal
      v-show="humanShow"
      ref="humanbody"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="humanShow = false"
    >
    </details-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="loadCancel"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="wranModalShow"
      title="提示"
      :r-width="500"
      @onCancel="onCancel"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
  </div>
</template>

<script>
import {
  queryHumanRecordSearchEx,
  humanDownload,
  taskView,
} from "@/api/wisdom-cloud-search";
import detailsModal from "@/components/detail/details-modal.vue";
import exportBox from "../export/export-box.vue";
import hlModal from "@/components/modal/index.vue";
export default {
  name: "",
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    detailsModal,
    exportBox,
    hlModal,
  },
  data() {
    return {
      HkDataList: [],
      currentIndex: 0,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 30,
      },
      glCheckAll: false,
      childDataSourceVal: 2, // 子组件数据资源值
      listLoading: false,
      timeUpDown: false,
      similUpDown: false,
      humanShow: false,
      exportShow: false,
      modalShow: false,
      wranModalShow: false,
      downTaskId: "",
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0,
    };
  },
  watch: {
    exportShow(val) {
      //点击空白处隐藏
      if (val) {
        document.addEventListener("click", () => {
          this.exportShow = false;
        });
      } else {
        document.addEventListener("click", () => {});
      }
    },
  },
  computed: {
    hkParams() {
      return JSON.parse(JSON.stringify(this.queryParam));
    },
  },
  created() {},
  mounted() {},
  activated() {},
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
  },
  methods: {
    loadCancel() {
      this.modalShow = false;
      this.wranModalShow = true;
    },
    onCancel() {
      this.modalShow = true;
      this.wranModalShow = false;
    },
    onOk() {
      this.modalShow = false;
      this.wranModalShow = false;
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {})
          .finally(() => {});
      }, 2000);
    },
    init() {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 30,
      };
      this.queryListData();
    },
    // 导出
    handleExport($event) {
      $event.stopPropagation();
      this.exportShow = !this.exportShow;
    },
    confirm(param) {
      let funparams = {
        ...this.hkParams,
        algorithmVendorType: this.hkParams.algorithmSelect[1],
      };
      let params = {};
      if (param.type == "1") {
        let list = this.HkDataList.filter((e) => e.isChecked);
        if (list.length > 0) {
          let ids = list.map((item) => item.recordId);
          params = {
            ids,
            downloadPics: param.downloadPics,
            downloadSize: null,
            ...funparams,
          };
          this.exportShow = false;
          this.modalShow = true;
        } else {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params = {
          ids: [],
          downloadPics: param.downloadPics,
          downloadSize: param.downloadSize,
          ...funparams,
        };
        this.exportShow = false;
        this.modalShow = true;
      }
      delete params.algorithmSelect;
      delete params.timeSlot;
      delete params.urlList;
      humanDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {});
    },
    // 排序
    handleGlSort(val) {
      if (val != this.hkParams.sortField) {
        this.hkParams.sortField = val;
        if (val == "similarity") {
          this.hkParams.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.hkParams.order = this.timeUpDown ? "asc" : "desc";
        }
      } else {
        this.hkParams.sortField = val;
        if (val == "similarity") {
          this.similUpDown = !this.similUpDown;
          this.hkParams.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.timeUpDown = !this.timeUpDown;
          this.hkParams.order = this.timeUpDown ? "asc" : "desc";
        }
      }
      this.queryListData();
    },
    async queryListData(page = 0) {
      this.glCheckAll = false;
      this.listLoading = true;
      this.HkDataList = [];
      let params = {
        ...this.hkParams,
        ...this.pageInfo,
        algorithmVendorType: this.hkParams.algorithmSelect[1],
      };
      delete params.algorithmSelect;
      delete params.timeSlot;
      delete params.urlList;
      delete params.selectDeviceList;
      await queryHumanRecordSearchEx(params)
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.HkDataList = entities || [];
          if (page == 1) {
            this.$refs.humanbody.prePage(this.HkDataList);
          } else if (page == 2) {
            this.$refs.humanbody.nextPage(this.HkDataList);
          }
          this.$forceUpdate();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryListData();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryListData();
    },
    // 全选
    checkAllHandler(val) {
      this.HkDataList = this.HkDataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    // 档案
    archivesPage(item, index) {
      this.$emit("archivesPage", item, index);
    },
    handleTargetAdd(item) {
      this.$emit("targetAdd", item);
    },
    // 收藏
    collection(item, flag) {
      this.$emit("collection", item, flag);
    },
    // 单选
    checkHandler(e, i) {
      this.HkDataList[i].isChecked = e;
      this.glCheckAll =
        this.HkDataList.filter((e) => e.isChecked).length ===
        this.HkDataList.length
          ? true
          : false;
    },
    // 详情
    faceDetailFn(row, index) {
      this.currentIndex = index;
      this.humanShow = true;
      this.$nextTick(() => {
        this.$refs.humanbody.init(
          row,
          this.HkDataList,
          index,
          1,
          this.pageInfo.pageNumber
        );
      });
    },
    prePage(pageNum) {
      if (pageNum < 1) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        this.queryListData(1);
      }
    },
    /**
     * 下一个
     */
    async nextPage(pageNum) {
      this.pageInfo.pageNumber = pageNum;
      let num = this.pageInfo.pageNumber;
      let size = this.pageInfo.pageSize;
      if (this.total <= num * size) {
        this.$Message.warning("已经是最后一个了");
        return;
      } else {
        this.queryListData(2);
      }
    },
    dataAboveMapHandler() {
      this.$emit("dataCograph", this.HkDataList);
    },
    openDirectModel(row) {
      this.$emit("openDirectModel", row);
    },
    // 人体搜步态
    handleGaitPage(row) {
      this.$emit("handleGaitPage", row);
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../advanced-search/pages/style/index";
@import "../style/index";
</style>
