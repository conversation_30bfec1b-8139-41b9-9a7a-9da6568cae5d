.dom-wrapper{
    // position: relative;
    // padding: 10px 28px 25px 0;
    padding: 10px 28px 0px 0;
    // height: 100%;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}
.dom {
    width: 1570px;
    height: 855px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    position: relative;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    > header {
        height: 36px;
        line-height: 36px;
        background: rgba(211, 215, 222, 0.3);
        box-shadow: inset 0px -1px 0px 0px #d3d7de;
        border-radius: 4px 4px 0px 0px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: bold;
        font-size: 14px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .dom-content {
        padding: 10px 30px;
        font-size: 14px;
        flex: 1;
        overflow: hidden;
        .info-box{
            display: flex;
            justify-content: space-between;
            height: 100%;
            .info-box-left{
                width: 210px;
                // display: flex;
                // flex-direction: column;
                overflow-y: auto;
                .thumbnail{
                    position: relative;
                    border: 1px solid #d3d7de;
                    width: 200px;
                    height: 200px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img{
                        // width: auto;
                        // height: 200px;
                        max-width: 100%;
                        max-height: 100%;
                    }
                    .similarity {
                        position: absolute;
                        left: 0px;
                        top: 0px;
                        padding: 0 9px;
                        height: 30px;
                        line-height: 30px;
                        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
                        border-radius: 4px;
                        font-size: 18px;
                        color: #fff;
                        text-align: center;
                    }
                    .plateNum{
                        position: absolute;
                        height: 40px;
                        width: 100%;
                        background: rgba(0, 0, 0, 0.7);
                        bottom: 5px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        span{
                            font-size: 16px;
                            padding: 8px 12px;
                        }
                    }
                }
                .record-title{
                    font-size: 14px;
                    // color: rgba(0, 0, 0, 0.9);
                    height: 20px;
                    width: 100%;
                    display: flex;
                    align-items: center;
                    margin-top: 10px;
                    color: rgba(0,0,0,0.6);
                    .record-right{
                        margin-left: 36px;
                    }
                    .active{
                        font-weight: bold;
                        color: rgba(0, 0, 0, 0.9);
                        border-bottom:3px solid #2c86f8;
                    }
                }
                .through-record{
                    padding-bottom: 10px;
                    margin-top: 10px;
                    .wrapper-content{
                        margin-top: 6px;
                        width: 200px;
                        display: flex;
                        .label {
                            font-size: 12px;
                            display: inline-block;
                            color: rgba(0, 0, 0, 0.6);
                            white-space: nowrap;
                            width: 55px;
                            text-align: justify;
                            text-align-last: justify;
                            text-justify: inter-ideograph;
                        }
                        .maxLabel{
                            width: auto;
                        }
                        .message {
                            font-size: 12px;
                            font-weight: bold;
                            width: 140px;
                            display: inline-block;
                            color: rgba(0, 0, 0, 0.9);
                        }
                        .identity{
                            cursor: pointer;
                            color: #F29F4C;
                        }
                        .plateNo{
                            cursor: pointer;
                            color: #2C86F8;
                        }
                    }
                }
                .structuring{
                    margin-top: 10px;
                    flex: 1;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    &-title{
                        font-weight: bold;
                        font-size: 12px;
                        color: #2C86F8;
                    }
                    .struct-box-ul{
                        height: 327px;
                        margin-top: 5px;
                        overflow: auto;
                        flex: 1;
                    }
                    .struct-box-list{
                        display: flex;
                        font-size: 12px;
                        margin-bottom: 5px;
                        .struct-title{
                            color: rgba(0,0,0,0.6);
                            width: 72px;
                            // text-align: justify;
                            // text-align-last: justify;
                            // text-justify: inter-ideograph;
                            text-align: right;
                        }
                        .struct-content{
                            font-weight: bold;
                            color: rgba(0,0,0,0.8);
                            margin-left: 9px;
                        }
                    }
                }
            }
            .info-box-right{
                width: 1280px;
                height: 660px;
                // border: 1px solid #d3d7de;
                // background: #f9f9f9;
                position: relative;
                // overflow: hidden;
                display: flex;
                align-items: center;
                justify-content: center;
                .largeImg{
                    // width: 100%;
                    // height: 100%;
                    max-width: 100%;
                    max-height: 100%;
                }
                .operation-box{
                    position: absolute;
                    height: 50px;
                    bottom: 0;
                    background: rgba(0, 0, 0, .5);
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    .color-wihte{
                        font-size: 20px;
                        color: #fff;
                        margin: 0 17px;
                        cursor: pointer;
                    }
                }
                .prev{
                    font-size: 30px;
                    position: absolute;
                    left: 0;
                    top: 290px;
                    z-index:666;
                    display: none;
                }
                .next{
                    position: absolute;
                    font-size: 30px;
                    right: 0;
                    top: 290px;
                    z-index: 666;
                    display: none;
                }
            }
            .info-box-right:hover{
                .next,.prev{
                    display: block;
                }

            }
            // .overlay{
            //     background-color: #000;
            //     opacity: 1.0;
            //     filter: alpha(opacity=100);
            //     position: fixed;
            //     top: 0;
            //     left: 0;
            //     width: 100%;
            //     height: 100%;
            //     z-index: 10;
            //     overflow: hidden;
            //     .largeImg{
            //         cursor: pointer;
            //     }
            //     .operation-box{
            //         display: none;
            //     }
            // }
        }
    }
    > footer {
        border-top: 1px solid #d3d7de;
        padding: 20px 0 16px 0px;
        height: 140px;
        // line-height: 55px;
        display: flex;
        justify-content: center;
        align-items: center;
        > span {
            color: #2c86f8;
            margin-right: 20px;
            cursor: pointer;
        }
        .arrows{
            margin: 0 19px 0 14px;
            font-size: 32px;
        }
        .box-wrapper{
            // width: 1480px;
            // overflow: hidden;
            width: 1440px;
            height: 100px;
            overflow: hidden;
            position: relative;
            .present{
                width: 100px;
                height: 100px;
                border: 2px rgba(44, 134, 248, 1) solid;
                position: absolute;
                z-index: 1;
                left: 10px;
                // transition: all 0.2s;
            }
        }
        .list-wrapper{
            // display: flex;
            height: 104px;
            align-items: center;
            // margin-top: 2px;
            width: 2000px;
            position: absolute;
            transition: all 0.01s ease;
            .list-box{
                width: 120px;
                height: 100px;
                padding: 0 10px;
                cursor: pointer;
                box-sizing: border-box;
                // width: 100px;
                // height: 100px;
                float: left;
                text-align: center;
                font-size: 20px;
                position: relative;
                .img-box{
                    width: 100px;
                    height: 100px;
                    // width: 100%;
                    // height: 100%;
                    border: 1px solid #D3D7DE;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    position: absolute;
                    left: 50%;
                    transform: translate(-50%, 0px);
                    img{
                        max-width: 100%;
                        width: auto;
                        max-height: 100%;
                        // height: 100%;
                    }
                }
            }
            .list-active{
                // width: 104px;
                // height: 104px;
                .img-box{
                    width: 100%;
                    height: 100%;
                    border: 2px solid rgba(44, 134, 248, 1);
                    img{
                        max-width: 100%;
                        width: auto;
                        max-height: 100%;
                    }
                }
            }
        }
    }
}
/deep/.ivu-icon-md-close{
    cursor: pointer;
}