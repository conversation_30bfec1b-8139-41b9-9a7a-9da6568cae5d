// 首页接口
export default {
  queryIndexDeviceOverview: '/ivdg-asset-app/device/queryIndexDeviceOverview', //设备资产（左上
  queryDeviceCheckColumnReports: '/ivdg-asset-app/deviceCheckStatus/queryDeviceCheckColumnReports', //关键属性检测（左下
  getIndexDevicePropertyStatistics: '/ivdg-asset-app/device/getIndexDevicePropertyStatistics', //首页资产区域分布统计(左中)
  getIndexEvaluationResultStatistics:
    '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getIndexEvaluationResultStatistics', //首页治理达标率(右中)
  oldGetIndexMapStatistics: '/ivdg-asset-app/device/getIndexMapStatistics', // 首页地图统计 - 旧版本
  getIndexMapStatistics: '/ivdg-evaluation-app/evaluation/app/home/<USER>/getHomeMapDataV2', // 首页地图统计

  viewByParamKey: '/qsdi-system-service/system/param/getParamDataByKeys', //查询首页系统配置
  updateByParamKey: '/qsdi-system-service/system/param/update', //修改首页系统配置
  getAllEvaluationIndex: '/ivdg-evaluation-app/evaluation/app/index/getAllEvaluationIndex', //获取所有指标
  getRankStatistics: '/ivdg-examination-app/examination/task/getRankStatistics', //获取所有指标
  queryGovernanceTrends: '/ivdg-evaluation-app/evaluation/app/home/<USER>', //治理趋势图
  getHomeGovernanceResultList: '/ivdg-evaluation-app/evaluation/app/home/<USER>/getHomeGovernanceResultList', //治理成效
  queryVideoDataErrorCount: '/ivdg-evaluation-app/evaluation/app/home/<USER>', //获取视频流检测错误数据对象 Get请求 没有请求参数
  queryViewDataCount: '/ivdg-evaluation-app/evaluation/app/home/<USER>', //获取视图数据检测数据对象（人、车、ZDR）
  queryDeviceOfAccessDataCount: '/ivdg-asset-app/device/queryDeviceOfAccessDataCount', //首页统计
  refreshIndexDeviceByConfig: '/ivdg-asset-app/device/refreshIndexDeviceByConfig', //首页统计

  // 江苏省厅
  queryHomePageResultIndex: '/ivdg-evaluation-app/homePage/queryHomePageResultIndex', //  获取数据检测列表（基础数据、人、车、ZDR）
  queryHomePageResultIndexMapStatistics: '/ivdg-evaluation-app/homePage/queryHomePageResultIndexMapStatistics', // 获取首页地图数据检测问题列表
  getHomePageArchives: '/ivdg-evaluation-app/homePage/getHomePageArchives', // 江苏省厅首页-档案数据检测

  // 内江
  getSafetyInspection: '/qsdi-data-receiver-service/device/getSafetyInspection', //首页安全监测
  getTodayDutyInfo: '/qsdi-data-receiver-service/device/getTodayDutyInfo', //首页今日值班
  exportSafetyInspection: '/qsdi-data-receiver-service/device/exportSafetyInspection', //导出安全检测

  queryAllCaptureStatistics: '/ivdg-evaluation-app/evaluation/app/home/<USER>', // 人脸||车辆抓拍数量

  updateJob: '/ivdg-evaluation-app/xxl-job/updateJob', // 数据备份
  queryFaceVehicleOnlineQuantity: '/ivdg-evaluation-app/evaluation/app/home/<USER>', // 卡口在线数量
  queryLatestBatchId: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/queryLatestBatchId', // 获取最后完成的批次号
};
