<template>
  <ui-modal
    v-model="visible"
    :title="`请选择${modalTitle}`"
    width="96rem"
    @query="handleSubmit"
    @onCancel="handleReset"
  >
    <div class="choose-device-wrap">
      <div class="search-box content-title">
        <div class="search-top">
          <div class="search-module">
            <slot name="search-module"></slot>
          </div>
        </div>
        <div class="search-bottom mb-lg">
          <div class="align-flex">
            <Checkbox class="checks mr-lg align-flex" v-model="isAll" @on-change="handleAllCheck"> 全选 </Checkbox>
          </div>
          <p class="devicenumtext base-text-color">
            已选择{{ modalTitle }}
            <span class="font-red">&nbsp;{{ isAll ? pageData.totalCount : chooseTableData.length }}&nbsp;</span>条
            <span>点击查看</span>
          </p>
        </div>
      </div>
      <ui-table
        reserveSelection
        class="ui-table auto-fill"
        :row-key="rowKey"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :is-all="isAll"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template v-for="(item, index) in tableColumns" :slot="item.slot" slot-scope="{ row }">
          <slot :name="item.slot" :row="row" :index="index">
            <span :key="'longitude' + index" v-if="item.slot === 'longitude'">
              {{ row.longitude | filterLngLat }}
            </span>
            <span :key="'latitude' + index" v-if="item.slot === 'latitude'">
              {{ row.latitude | filterLngLat }}
            </span>
          </slot>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        transfer
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
  </ui-modal>
</template>

<script>
export default {
  name: 'choose-device',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: String,
      default: 'id',
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    loadTableData: {
      // 接口
      type: Function,
      default() {},
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
  },
  data() {
    return {
      visible: false,
      modalTitle: '设备',
      isAll: false,
      loading: false,
      tableData: [],
      defaultStoreData: [], // 存储勾选的设备
      chooseTableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchData: {},
    };
  },
  methods: {
    async getTableData() {
      try {
        this.loading = true;
        this.searchData = { ...this.pageData };
        const result = await this.loadTableData(this.searchData);
        this.tableData = result.data[this.listKey];
        this.pageData.totalCount = result.data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    startSearch() {
      this.pageData.pageNum = 1;
      this.getTableData();
    },
    storeSelectList(selection) {
      this.chooseTableData = selection;
    },
    handleAllCheck(val) {
      this.chooseTableData = [];
      if (!val) {
        this.defaultStoreData = [];
      }
      this.handleDataChecked();
    },
    // 点击全选或者排除时，对表格复选框进行操作
    handleDataChecked() {
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.isAll);
        this.$set(item, '_disabled', this.isAll);
        return item;
      });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableData();
    },
    handleSubmit() {
      const selects = {
        selectedDevNum: this.isAll ? this.pageData.totalCount : this.chooseTableData.length || 0,
        chooseIds: this.chooseTableData.map((item) => item.id),
        chooseDeviceIds: this.chooseTableData.map((item) => item.deviceId),
        checkDeviceFlag: this.isAll ? '2' : '0',
      };
      this.$emit('getDeviceIdList', selects);
      this.visible = false;
    },
    handleReset() {},
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.getTableData();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.choose-device-wrap {
  display: flex;
  flex-direction: column;
  height: 760px;
  background-color: var(--bg-content);
  //border-top: 1px solid var(--border-color);
  .search-top {
    width: 100%;
    .align-flex;
  }
  .search-bottom {
    width: 100%;
    height: 15px;
    .align-flex;
    .devicenumtext {
      flex: 1;
      display: flex;
      justify-content: flex-end;
    }
  }
  .search-box {
    width: 100%;
    .search-header {
      flex: 1;
      .align-flex;
      flex-wrap: wrap;
    }
  }
}

@{_deep} .ivu-modal-body {
  max-height: none !important;
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
</style>
