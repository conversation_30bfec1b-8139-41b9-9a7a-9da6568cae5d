<template>
  <ui-modal
    width="450"
    v-model="modalShow"
    title="连接分析"
    class="relation-graph-lianjiefenxi"
    @onOk="onOK"
  >
    <Form
      ref="formCustom"
      :model="formData"
      :label-width="88"
      label-position="right"
    >
      <FormItem label="分析程度:" prop="name">
        <RadioGroup v-model="formData.maxDepth">
          <Radio :label="1"> 一度 </Radio>
          <Radio :label="2"> 二度 </Radio>
          <Radio :label="3"> 三度 </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="实体类型:" prop="vertexLabel">
        <Select
          v-model="formData.entityLabel"
          placeholder="请选择实体类型"
          class="w250"
          clearable
        >
          <Option
            v-for="(item, $index) in pathShowObj.entityList"
            :key="$index"
            :value="item.label"
            >{{ item.labelCn }}</Option
          >
        </Select>
      </FormItem>
      <FormItem label="实体关系:" prop="edgeLabel">
        <Select
          v-model="formData.relationLabel"
          placeholder="请选择实体关系"
          class="w250"
          clearable
        >
          <Option
            v-for="(item, $index) in pathShowObj.relationList"
            :key="$index"
            :value="item.label"
            >{{ item.labelCn }}</Option
          >
        </Select>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    pathShowObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      modalShow: false,
      formData: { entityLabel: "", relationLabel: "", maxDepth: 1 },
    };
  },
  computed: {},
  watch: {
    modalShow(val) {
      this.formData = { entityLabel: "", relationLabel: "", maxDepth: 1 };
      this.$emit("input", val);
    },
    value(val) {
      this.modalShow = val;
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    onOK() {
      this.$emit("connectionAnalysis", this.formData);
      this.modalShow = false;
    },
  },
};
</script>
<style lang="less" scoped>
.relation-graph-lianjiefenxi {
  /deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
    min-height: 0;
    padding-bottom: 0;
  }
}
.w250 {
  width: 250px;
}
.w100 {
  width: 100px;
}
.w60 {
  width: 60px;
}
.w78 {
  width: 78px;
}
.mr6 {
  margin-right: 6px;
}
</style>
