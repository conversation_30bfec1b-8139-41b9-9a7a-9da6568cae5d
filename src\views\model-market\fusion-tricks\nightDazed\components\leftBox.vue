<!--
    * @FileDescription: 昼伏夜出搜索，对象信息
    * @Author: H
    * @Date: 2023/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <div class="search-box">
            <div class="title">
                <p>昼伏夜出</p>
            </div>
            <div class="search_condition" :class="{'search_condition-pack': packUpDown}">
                <div class="search_form">
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">时间</p>:
                        </div>
                        <div class="search_content">
                            <Dateoption :timeList='timeList' @dataRangeHandler="dataRangeHandler"></Dateoption>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">区域</p>:
                        </div>
                        <ul class="search_content">
                            <li class="active-area-sele" @click="handleSelemodel">
                                {{ formData.deviceIds.length == 0 ? '请选择' : `已选(${formData.deviceIds.length})`}} 
                            </li>
                            <li class="area-list" v-for="(item, index) in toolMap" :key="index"
                                :class="{'active-area-list': index == regionIndex}"
                                @click="handleSeleArea(index, item)"> 
                                <img :src="item.icon" alt="">
                            </li>
                        </ul>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">最少昼伏夜出天数:</div>
                        <div class="search_content">
                            <Input v-model="formData.peerSecond" placeholder="请输入" class="wrapper-input"></Input>
                        </div>
                        <p class="search_text">天</p>
                    </div>
                    <div class="search_wrapper search_special">
                        <div class="search_title">分析数据:</div>
                        <ul class="search_content">
                            <li v-for="(item, index) in analyzeData" :key="index" 
                                :class="{'active_analyze_list': item.check}"
                                @click="handleAnly(item,index)"
                                class="analyze_list" >
                                <div>{{ item.name }}</div>
                                <div class="active_gradient">
                                    <Icon type="ios-checkmark" />
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="btn-group">
                        <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
                        <Button type="default" @click="handleReset">重置</Button>
                    </div>
                </div>
            </div>
            <div class="footer" :class="{packArrow: packUpDown}" @click="handlePackup">
                <img :src="packUrl" alt="">
                <p>{{ packUpDown ? '展开条件' : '收起条件'}}</p>
            </div>
        </div>
        <select-device :deviceType="deviceType()" ref="selectDevice" @selectData="selectData" />
    </div>
</template>

<script>
import Dateoption from '../../components/date-option/index.vue';
import selectDevice from '@/views/operations-on-the-map/map-default-page/components/select-device.vue';
export default {
    name: '',
    components:{
        Dateoption,
        selectDevice  
    },
    data () {
        return {
            packUpDown: false, 
            ruleValidate: {

            },
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            formData:{ 
                deviceIds: [],
                startDate: '',
                endDate: '',
                dateType: 2,
            },
            analyzeData: [
                { 'name': '全部', 'label': 0, 'check': false },
                { 'name': '人脸', 'label': 1, 'check': false },
                { 'name': '车辆', 'label': 2, 'check': false },
                { 'name': 'RFID', 'label': 3, 'check': false },
                { 'name': '电围', 'label': 4, 'check': false },
                { 'name': 'WiFi', 'label': 5, 'check': false },
            ],
            timeList: [
                { value: '近7天', key: 2 },
                { value: '近30天', key: 3 },
                { value: '自定义', key: 4 }
            ],
            toolMap: [
                {
                    title: '圆形框选',
                    icon: require('@/assets/img/model/icon/circle.png'),
                    fun: this.selectDraw,
                    value: 'circle',
                    funName: 'selectCircle'
                },
                {
                    title: '矩形框选',
                    icon: require('@/assets/img/model/icon/rectangle.png'),
                    fun: this.selectDraw,
                    value: 'rectangle',
                    funName: 'selectRectangle'
                },
                {
                    title: '多边形框选',
                    icon: require('@/assets/img/model/icon/polygon.png'),
                    fun: this.selectDraw,
                    value: 'polygon',
                    funName: 'selectPolygon'
                },
            ],
            regionIndex: '-1',
            selectDevice: [],
            sectionName: 'face'
        }
    },
    watch:{
        packUpDown:{
            handler(val) {
                this.$nextTick(() => {
                    let box = document.querySelector('.search_condition');
                    if(val){
                        box.style.overflow = 'hidden';
                    }else{
                        setTimeout(()=>{
                            box.style.overflow = 'inherit';
                        },200)
                    }
                })
            },  
            immediate:true
        }   
    },
    computed:{
            
    },
    created() {
           
    },
    mounted(){
            
    },
    methods: {
        // 分析数据
        handleAnly(item, index) {
            item.check = !item.check;
            this.$set(this.analyzeData, index, item)
        },
        // 查询
        handleSearch() {
            this.$emit('search', this.formData)
        },
        // 时间选择
        dataRangeHandler(dataRange, times = []) {
            if (dataRange == 4 && times.length == 0) {
                return
            };
            const [startDate = '', endDate = ''] = times;
            this.formData.startDate = startDate;
            this.formData.endDate = endDate;
            this.formData.dateType = dataRange;
        },
        // 请选择
        handleSelemodel() {
            this.$refs.selectDevice.show(this.selectDevice)
        },
        // 返回的数据
        selectData(ids) {
            this.selectDevice = ids;
            this.formData.deviceIds = ids.map(e => e.deviceGbId);
        },
        goSearch(value, type) {
            type ? this.formData.deviceIds = [...this.formData.deviceIds, ...value] : this.formData.deviceIds = [...value];
        },
        // 选择区域
        handleSeleArea(index, item) {
            // rectangle 矩形
            // circle    圆形
            // polygon   多边形
            this.$emit('selectDraw', item.funName);
            this.regionIndex = index;
        },
        // 展开、收起
        handlePackup() {
            this.packUpDown = !this.packUpDown;
        },
        // 设备类型
        deviceType () {
            // 人脸和车辆都是一样的1，所以自定义人脸为0
            const deviceTypeMap = {
                face: 1,
                vehicle: 1,
                wifi: 2,
                rfid: 3,
                electric: 4
            }
            return deviceTypeMap[this.sectionName]
        },
        // 重置
        handleReset() {
            this.formData = { 
                deviceIds: [],
                peerSecond: '',
            };
            this.analyzeData.map(item => {
                item.check = false;
            })
        },
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.leftBox{
    position: absolute;
    top: 10px;
    left: 10px;
    .search-box{
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        width: 370px;
        filter: blur(0px);
        .search_condition{
            max-height: 420px;
            transition: max-height 0.2s ease-out;
            // overflow: hidden;
            .search_form{
                padding: 10px 15px 0px 20px;
                .search_wrapper{
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    margin-bottom: 15px;
                    .search_title{
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.45);
                        margin-right: 10px;
                        display: flex;
                    }
                    .search_strut{
                        text-align: justify;
                        width: 50px;
                        text-align-last: justify;
                    }
                    .search_content{
                        display: flex;
                        flex-wrap: wrap;
                        flex: 1;
                        .active-area-sele{
                            width: 120px;
                            height: 34px;
                            border-radius: 4px;
                            font-size: 14px;
                            text-align: center;
                            line-height: 34px;
                            cursor: pointer;
                            border: 1px dashed #2C86F8;
                            background: rgba(44, 134, 248, 0.10);
                            color: rgba(44, 134, 248, 1);
                        }
                        .area-sele{
                            border: 1px solid #D3D7DE;
                            color: rgba(0,0,0,0.6);
                            background: none;
                        }
                        .area-list{
                            width: 34px;
                            height: 34px;
                            background: #FFFFFF;
                            border-radius: 4px;
                            border: 1px solid #D3D7DE;
                            cursor: pointer;
                            color: rgba(0,0,0,0.6);
                            margin-left: 10px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            img{
                                opacity: 1;
                            }
                        }
                        .active-area-list{
                            border: 1px dashed #2C86F8;
                            background: rgba(44, 134, 248, 0.10);
                            color: rgba(44, 134, 248, 1);
                            img{
                                opacity: .6;
                            }
                        }
                        .analyze_list{
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.6);
                            padding: 2px 10px;
                            border-radius: 2px;
                            border: 1px solid #D3D7DE;
                            margin-right: 10px;
                            margin-bottom: 10px;
                            cursor: pointer;
                            .active_gradient{
                                display: none;
                            }
                        }
                        .active_analyze_list{
                            color: rgba(44, 134, 248, 1);
                            border: 1px solid rgba(44, 134, 248, 1);
                            position: relative;
                            .active_gradient{
                                position: absolute;
                                width: 12px;
                                height: 12px;
                                background: linear-gradient(315deg, #2C86F8, #2C86F8 50%, transparent 50%, transparent 100%);
                                bottom: 0;
                                right: 0;
                                display: block;
                            }
                            .ivu-icon-ios-checkmark{
                                position: absolute;
                                bottom: -3px;
                                right: -4px;
                                color: #fff;
                             }
                        }
                    }
                    .search_text{
                        color: rgba(0,0,0,0.8);
                        margin-left: 5px;
                        font-size: 14px;
                    }
                }
                .search_special{
                    align-items: flex-start;
                }
                .btn-group{
                    .btnwidth{
                        width: 258px;
                    }
                }
            }
        }
        .search_condition-pack{
            max-height: 0px;
            transition: max-height 0.2s ease-out;
        }
    }
    .packArrow{
        img{
            transform: rotate(180deg);
            transition: transform 0.2s;
        }
    }
}
/deep/ .ivu-select-dropdown{
    left: 0 !important;
}
</style>

 