<template>
  <div class="addBox">
    <div class="addBox_header">
      <Icon type="ios-undo" @click="handleback" />
      <span @click="handleback">警卫路线 > 编辑路线</span>
    </div>
    <div class="addBox_form">
      <div class="search_form">
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut"><span>*</span>路线名称:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.description"
              placeholder="请输入"
              class="wrapper-input"
              clearable
            ></Input>
          </div>
        </div>
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut">搜索范围:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.buffDis"
              placeholder="请输入"
              class="wrapper-input"
              clearable
            >
              <span slot="append">米</span>
            </Input>
          </div>
        </div>
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut">起点:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.startPointName"
              placeholder="请输入"
              class="wrapper-input"
              clearable
            ></Input>
          </div>
        </div>
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut">终点:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.endPointName"
              placeholder="请输入"
              class="wrapper-input"
              clearable
            ></Input>
          </div>
        </div>
        <div class="search_wrapper device">
          <span class="num"
            >已选设备&nbsp;{{ value.devices.length }}&nbsp;个</span
          >
          <span @click="showMoreDevice" class="more-device">
            <i
              class="iconfont"
              :class="[isShowDevice ? 'icon-outdent' : 'icon-indent']"
            ></i>
            <span>{{ isShowDevice ? "收起列表" : "展开列表" }}</span>
          </span>
        </div>
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut">GPS编号:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.gpsId"
              placeholder="请输入"
              class="wrapper-input"
              clearable
            ></Input>
          </div>
        </div>
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut">车队代号:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.vehicleCode"
              placeholder="请输入"
              class="wrapper-input"
              clearable
            ></Input>
          </div>
        </div>
        <div class="search_wrapper">
          <div class="search_title">
            <p class="search_strut">行驶速度:</p>
          </div>
          <div class="search_content">
            <Input
              v-model="value.gpsSpeed"
              placeholder="请输入"
              class="wrapper-input"
              clearable
              maxlength="3"
              :number="true"
            >
              <span slot="append" style="font-size: 12px">千米/小时</span>
            </Input>
          </div>
        </div>
        <div class="btn-group">
          <Button class="btnwidth" @click="manualDraw">绘制路线</Button>
          <Button type="primary" class="btnwidth" @click="handleSave"
            >保存</Button
          >
        </div>
      </div>
    </div>
    <transition name="devicelist-fade-panel">
      <!-- 设备列表面板 -->
      <div
        class="devicelist-panel nui-map-panel-bg nui-border"
        v-if="isShowDevice && value.hasRoute"
      >
        <div class="device-select form-item">
          <div class="select-tag-button" @click="handleSelectDevice">
            设备选择
          </div>
        </div>
        <div class="cameralistpanel-header nui-font">
          <span
            >所选设备<span class="nui-font">{{ value.devices.length }}</span
            >个</span
          >
          <span class="move-explain">(长按摄像机列表可拖拽进行手动排序)</span>
        </div>
        <div class="cameralist-panel">
          <div class="route-start">
            <i class="iconfont i-qd"></i>
            <span class="nui-font" :title="value.startPointName">{{
              value.startPointName || "暂无"
            }}</span>
          </div>
          <div class="camera-list-wrap" v-if="value.devices.length > 0">
            <div
              v-for="(item, index) in value.devices"
              :key="index"
              class="device"
              draggable
              @mousemove="mousemoveItem(item)"
              @mouseout="mouseoutItem(item)"
              @dragstart="dragstart(item)"
              @dragenter="handleDragEnter($event, item)"
              @dragover.prevent="handleDragOver($event, item)"
              @dragend="dragend(item)"
            >
              <i class="iconfont icon" :class="getIconType(item)"></i>
              <span class="name">{{ item.deviceName }}</span>
              <i
                class="iconfont icon-shanchu"
                title="删除"
                @click.stop="removeItem(index)"
              ></i>
            </div>
          </div>
          <div class="no-result" v-show="value.devices.length === 0">
            <ui-empty></ui-empty>
          </div>

          <div class="route-end">
            <i class="iconfont i-zd"></i>
            <span class="nui-font" :title="value.endPointName">{{
              value.endPointName || "暂无"
            }}</span>
          </div>
        </div>
      </div>
    </transition>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      @selectData="handleSelectData"
      :showOrganization="true"
    ></select-device>
  </div>
</template>

<script>
export default {
  model: {
    prop: "value",
    event: "input",
  },
  props: {
    value: {},
    isEdit: {},
  },
  data() {
    return {
      isShowDevice: false,
    };
  },
  methods: {
    mousemoveItem(item) {
      this.$emit("mousemoveItem", item);
    },
    mouseoutItem(item) {
      this.$emit("mouseoutItem", item);
    },
    handleback() {
      this.$emit("back");
    },
    handleSave() {
      if (!this.value.hasRoute) {
        this.$Message.warning("请先选择路径");
        return;
      }
      let reg = /^[0-9]*[1-9][0-9]*$/
      if(!reg.test(this.value.gpsSpeed)) {
        this.$Message.warning("行驶速度必须大于等于0");
        return;
      }
      this.$emit("save");
    },
    manualDraw() {
      this.$emit("draw");
    },
    showMoreDevice() {
      if (!this.isShowDevice) {
        if (!this.value.hasRoute) {
          this.$Message.warning("请先选择路径");
          return;
        }
      }
      this.isShowDevice = !this.isShowDevice;
    },
    // 图标
    getIconType(camera) {
      return Toolkits.getDeviceIconType(camera);
    },
    // 选择设备
    handleSelectDevice() {
      this.value.devices.forEach((v) => {
        v.select = true;
      });
      this.$refs.selectDevice.show(this.value.devices);
    },
    // 选择设备数据
    handleSelectData(list) {
      let array = [...list];
      array.forEach((v) => {
        v.latitude = v.latitude || v.geoPoint.lat;
        v.longitude = v.longitude || v.geoPoint.lon;
      });
      this.value.devices = array;
      this.$emit("refreshDeviceMaker");
    },
    removeItem(index) {
      this.value.devices.splice(index, 1);
      this.$emit("refreshDeviceMaker");
    },
    dragstart(item) {
      this.dragging = item;
    },
    dragend(item) {
      this.dragging = null;
    },
    handleDragEnter(e, item) {
      e.dataTransfer.effectAllowed = "move";
      if (item === this.dragging) {
        return;
      }
      const newCameras = [...this.value.devices];
      const src = newCameras.indexOf(this.dragging);
      const dst = newCameras.indexOf(item);
      newCameras.splice(dst, 0, ...newCameras.splice(src, 1));
      this.value.devices = newCameras;
    },
    handleDragOver(e, item) {
      e.dataTransfer.dropEffect = "move";
    },
  },
  watch: {
    // 修改范围后 重绘线路
    "value.buffDis": {
      handler(val) {
        // 新建路线 不绘制
        // if (!this.isEdit) {
        //   return;
        // }
        Toolkits.FnByShake("500", () => {
          if (val < 0 || val > 500) {
            this.$Message.warning("设备检测范围在0-500米之内");
            return;
          }
          this.$parent.changeBuff(val);
        });
      },
    },
  },
};
</script>

<style scoped lang="less">
.addBox {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  max-height: calc(~"100% - 20px");
  .addBox_header {
    height: 40px;
    background: #2c86f8;
    border-bottom: 1px solid #d3d7de;
    color: #fff;
    font-size: 14px;
    line-height: 40px;
    padding-left: 14px;
    display: flex;
    align-items: center;
    .icon-jiantou {
      transform: rotate(90deg);
      display: inline-block;
      cursor: pointer;
    }
    .ivu-icon-ios-undo {
      font-size: 20px;
      cursor: pointer;
    }
    span {
      font-size: 14px;
      cursor: pointer;
      margin-left: 10px;
    }
  }
  .addBox_form {
    .search_form {
      padding: 20px;
      .search_wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 15px;
        &.device {
          height: 40px;
          line-height: 40px;
          border-top: 1px dashed rgba(167, 172, 184, 0.3);
          border-bottom: 1px dashed rgba(167, 172, 184, 0.3);
          .num {
            flex: 1;
          }
          .icon-indent,
          .icon-outdent {
            margin-right: 5px;
          }
        }
        .more-device {
          cursor: pointer;
        }
        .search_title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-right: 10px;
          display: flex;
        }
        .search_strut {
          // text-align: justify;
          width: 66px;
          // text-align-last: justify;
          span {
            color: red;
          }
        }
        .search_content {
          display: flex;
          flex-wrap: wrap;
          flex: 1;
          .active-area-sele {
            width: 120px;
            height: 34px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
            line-height: 34px;
            cursor: pointer;
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
          }
          .area-sele {
            border: 1px solid #d3d7de;
            color: rgba(0, 0, 0, 0.6);
            background: none;
          }
          .area-list {
            width: 34px;
            height: 34px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #d3d7de;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.6);
            margin-left: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              opacity: 1;
            }
          }
          .active-area-list {
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
            img {
              opacity: 0.6;
            }
          }
          .analyze_list {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            padding: 2px 10px;
            border-radius: 2px;
            border: 1px solid #d3d7de;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            .active_gradient {
              display: none;
            }
          }
          .active_analyze_list {
            color: rgba(44, 134, 248, 1);
            border: 1px solid rgba(44, 134, 248, 1);
            position: relative;
            .active_gradient {
              position: absolute;
              width: 12px;
              height: 12px;
              background: linear-gradient(
                315deg,
                #2c86f8,
                #2c86f8 50%,
                transparent 50%,
                transparent 100%
              );
              bottom: 0;
              right: 0;
              display: block;
            }
            .ivu-icon-ios-checkmark {
              position: absolute;
              bottom: -3px;
              right: -4px;
              color: #fff;
            }
          }
        }
        .search_text {
          color: rgba(0, 0, 0, 0.8);
          margin-left: 5px;
          font-size: 14px;
        }
      }
      .search_special {
        align-items: flex-start;
      }
      .btn-group {
        .btnwidth {
          width: 160px;
        }
      }
    }
  }
  .devicelist-fade-panel-enter {
    opacity: 0;
    transform: translateX(-310px);
    -webkit-transform: translateX(-310px);
  }
  .devicelist-fade-panel-enter-active {
    opacity: 1;
    transition: all 0.5s ease;
  }
  .devicelist-fade-panel-leave {
    opacity: 1;
  }
  .devicelist-fade-panel-leave-active {
    opacity: 1;
    transition: all 0.5s ease;
    transform: translateX(-310px);
    -webkit-transform: translateX(-310px);
  }
  .devicelist-panel {
    position: absolute;
    top: 0;
    left: 372px;
    width: 310px;
    // height: 471px;
    z-index: 600;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    padding: 12px 12px;
    background: #fff;
    .more-device {
      float: right;
      cursor: pointer;
    }
    .device-select {
      line-height: 30px;
      height: 30px;
      margin-bottom: 10px;
    }
    .cameralistpanel-header {
      margin-bottom: 10px;
      .move-explain {
        color: #b1b5bf;
        float: right;
      }
    }
    .cameralist-panel {
      position: relative;
      border: 1px solid rgba(167, 172, 184, 0.3);
      .route-end,
      .route-start {
        display: inline-block;
        width: 100%;
        line-height: 30px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        i {
          position: relative;
          top: -1px;
          left: 5px;
          font-size: 24px;
        }
      }
      .route-start {
        display: inline-block;
        width: 100%;
        border-bottom: 1px solid rgba(167, 172, 184, 0.3);
        .i-qd {
          height: 32px;
          width: 23px;
          display: inline-block;
          background: url("~@/assets/img/map/track/blue-start.png");
          margin-top: 5px;
        }
        span {
          margin-left: 7px;
        }
      }
      .route-end {
        display: inline-block;
        border-top: 1px solid rgba(167, 172, 184, 0.3);
        .i-zd {
          height: 32px;
          width: 23px;
          display: inline-block;
          background: url("~@/assets/img/map/track/red-end.png");
          margin-top: 5px;
        }
        span {
          margin-left: 7px;
        }
      }
      .camera-list-wrap {
        max-height: 310px;
        padding: 10px;
        overflow-y: auto;
        .device {
          display: flex;
          align-items: center;
          padding: 3px;
          &:hover {
            cursor: pointer;
            background: rgba(44, 134, 248, 0.2);
            .icon-shanchu {
              opacity: 1;
            }
          }
          .icon {
            margin-right: 5px;
            color: #2c86f8;
          }
          .icon-shanchu {
            margin: 0 5px;
            color: #2c86f8;
            opacity: 0;
          }
          .name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex: 1;
          }
          &.playing {
            .icon::before {
              content: "";
              display: block;
              color: #ff8d33;
              width: 18px;
              height: 18px;
              background: url("~@/assets/img/player/playing.gif") 50% no-repeat;
            }
          }
        }
      }
      .no-result {
        height: 310px;
      }
    }
  }
}
</style>
