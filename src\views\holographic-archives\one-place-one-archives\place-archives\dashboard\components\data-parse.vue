<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-07-05 11:29:12
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-23 15:42:48
-->
<template>
  <ui-module
    :title="cardTitles"
    :title-model="curTitle"
    @tab-click="changeTitle"
  >
    <div slot="extra" class="btn-group">
      <Button
        v-for="(item, index) in timeList"
        :key="index"
        :type="item.value === dateType ? 'primary' : 'default'"
        size="small"
        @click="changeDateType(item.value)"
        >{{ item.label }}
      </Button>
    </div>
    <div class="data-parse-container">
      <!-- 统计数量 -->
      <div class="parse-count">
        <div class="count-in">
          <div class="label">{{ this.label }}进：</div>
          <div class="value color-primary">{{ totalIn }}</div>
        </div>
        <div class="count-out">
          <div class="label">{{ this.label }}出：</div>
          <div class="value warning">{{ totalOut }}</div>
        </div>
      </div>
      <!-- 统计图 -->
      <div class="parse-chart">
        <!-- <template v-if="isPerson"> -->
        <div class="left-chart">
          <PieEchart
            :tooltip="tooltip"
            :title="title1"
            :legend="legend1"
            :series="series1"
            :color="color"
          />
        </div>
        <div class="right-chart">
          <PieEchart
            :tooltip="tooltip"
            :title="title2"
            :legend="legend2"
            :series="series2"
            :color="color"
          />
        </div>
      </div>
      <ui-loading v-show="loading" />
    </div>
  </ui-module>
</template>

<script>
import PieEchart from "@/components/echarts/pie-echart.vue";
import UiModule from "../../components/ui-module.vue";
import { getPersonParseAPI, getVehicleParseAPI } from "@/api/placeArchive";
import { vehicleClassTypeList } from "./common";

const commonSeriesOption = {
  type: "pie",
  radius: [35, 55],
  center: ["50%", 65],
  label: {
    position: "center",
    show: false,
  },
  emphasis: {
    label: {
      show: true,
      fontWeight: "bold",
      formatter: "{c}\n{num|{b}}",
      color: "inherit",
      rich: {
        num: {
          color: "rgba(0, 0, 0, 0.6)",
        },
      },
    },
  },
};

const commonLegendOption = {
  show: true,
  pageButtonGap: 2,
  pageIconSize: 8,
  pageTextStyle: {
    fontSize: 10,
  },
  top: 130, // 130 = 饼图外半径55*2+上下边距10*2
  itemWidth: 8,
  icon: "circle",
};

export default {
  name: "DataParse",
  components: { PieEchart, UiModule },
  props: {
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    //  1：一周（近7天）  2：一月（近30天） 3: 三个月  4.一天=当天  0：自定义
    this.timeList = Object.freeze([
      { value: 4, label: "一天" },
      { value: 1, label: "一周" },
      { value: 2, label: "一月" },
    ]);
    this.color = Object.freeze([
      "#F0BA4C",
      "#1E64D1",
      "#22559F",
      "#15A9FF",
      "#0A82C6",
      "#A09AFF",
      "#015E96",
      "#8179F3",
      "#4F48C8",
      "#879DC5",
      "#2C86F8",
      "#EF824C",
      "#79E1ED",
    ]);

    return {
      loading: false,
      // 卡片title列表
      cardTitles: [
        { label: "人员统计", value: "person" },
        { label: "车辆统计", value: "vehicle" },
      ],
      curTitle: "person", // 当前选中的title
      dateType: 1, // 时间类型
      totalIn: 0, // 进总量
      totalOut: 0, // 出总量
      //#region 饼图配置相关
      tooltip: { show: false },
      legend1: {
        ...commonLegendOption,
        type: "plain",
      },
      legend2: {
        ...commonLegendOption,
        type: "plain",
      },
      series1: [
        {
          ...commonSeriesOption,
          data: [],
        },
      ],
      series2: [
        {
          ...commonSeriesOption,
          data: [],
        },
      ],
      //#endregion
    };
  },
  computed: {
    // 人 or 车
    isPerson() {
      return this.curTitle === "person";
    },
    // 统计名称
    label() {
      return this.isPerson ? "人员" : "车辆";
    },
    // 请求
    req() {
      return this.isPerson ? getPersonParseAPI : getVehicleParseAPI;
    },
    // 左侧饼图标题
    title1() {
      return {
        show: true,
        subtext: this.isPerson ? "性别" : "来源",
      };
    },
    // 右侧饼图标题
    title2() {
      return {
        show: true,
        subtext: this.isPerson ? "年龄" : "类型",
      };
    },
  },
  created() {
    this.getParseData();
  },
  methods: {
    /**
     * @description: 切换标题
     * @param {string | number} val 标题value
     */
    changeTitle(val) {
      this.curTitle = val;
      this.getParseData();
    },

    /**
     * @description: 切换时间
     * @param {number} val 时间值
     */
    changeDateType(val) {
      if (val === this.dateType) return;
      this.dateType = val;
      this.getParseData();
    },

    /**
     * @description: 获取数据
     */
    getParseData() {
      this.loading = true;
      let params = {
        archiveNo: this.$route.query.archiveNo,
        type: this.dateType,
      };
      this.req(params)
        .then((res) => {
          if (res.code === 200 && res.data) {
            let data = res.data.placeCaptureStatics || [];
            this.totalIn = res.data.in;
            this.totalOut = res.data.out;
            // res.data.placeCaptureStatics为多个包含type的对象组成的数组，需要根据type来筛选出相应的数组
            let leftType = "",
              rightType = "";
            if (this.isPerson) {
              leftType = "gender";
              rightType = "age";
            } else {
              leftType = "localAndOutVehicle";
              rightType = "vehicleClassType";
            }

            let d1 = data.find((v) => v.type === leftType)?.data;
            let d2 = data.find((v) => v.type === rightType)?.data;

            this.series1[0].data = d1?.map((item) => {
              return {
                name: item.type,
                value: item.num,
              };
            });
            if (this.isPerson) {
              this.series2[0].data = d2?.map((item) => {
                return {
                  name: item.type,
                  value: item.num,
                };
              });
            } else {
              // 车辆类型需要翻译，后端返回的是key
              this.series2[0].data = d2?.map((item) => {
                return {
                  name: vehicleClassTypeList.find((v) => v.value === item.type)
                    .dataValue,
                  value: item.num,
                };
              });
              // 后端类型较多，太多legend滚动
              if (d2.length > 6) {
                this.legend2.type = "scroll";
              }
            }
          } else {
            // 当没有数据时，后端返回的res.data为null
            this.totalIn = 0;
            this.totalOut = 0;
            this.series1[0].data = [];
            this.series2[0].data = [];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.btn-group {
  padding-top: 10px;
  padding-right: 20px;
  button + button {
    margin-left: 10px;
  }
  .ivu-btn-small {
    border-color: #d3d7de;
    height: 24px;
    padding: 0 10px;
    color: rgba(0, 0, 0, 0.6);
    border-radius: 2px;
    &.ivu-btn-primary {
      color: #fff;
      border-color: #4597ff;
    }
  }
}
.data-parse-container {
  height: 100%;
  display: flex;
  .parse-count {
    width: 80px;
    min-width: 80px;
    border-right: 1px dashed #d3d7de;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    .label {
      font-weight: 400;
      font-size: 12px;
      color: #3d3d3d;
      line-height: 18px;
    }
    .value {
      margin-top: 8px;
      font-weight: 700;
      font-size: 18px;
      line-height: 18px;
    }
  }
  .parse-chart {
    flex: 1;
    display: flex;
    & > div {
      flex: 1;
    }
    /deep/ .echart-subtext {
      text-align: center;
      margin-top: 0;
    }
  }
}
</style>