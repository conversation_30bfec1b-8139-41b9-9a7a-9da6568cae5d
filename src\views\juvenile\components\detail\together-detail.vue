<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <div
        v-if="showNext"
        :class="{ grey: tableList.length == 1 }"
        class="leftIcon icon"
        @click="prev()"
      >
        <Icon type="md-arrow-dropleft" />
      </div>
      <div
        v-if="showNext"
        :class="{ grey: tableList.length == 1 }"
        class="rightIcon icon"
        @click="next()"
      >
        <Icon type="md-arrow-dropright" />
      </div>
      <header>
        <span>报警详情</span>
        <ui-icon
          type="close"
          :size="14"
          @click.native="() => $emit('close', $event)"
        ></ui-icon>
      </header>
      <div class="level">
        <div class="level-title">
          <img
            v-if="bgIndex == 1"
            class="level"
            src="@/assets/img/target/bg1.png"
            alt
          />
          <img
            v-else-if="bgIndex == 2"
            class="level"
            src="@/assets/img/target/bg2.png"
            alt
          />
          <img
            v-else-if="bgIndex == 3"
            class="level"
            src="@/assets/img/target/bg3.png"
            alt
          />
          <img
            v-else-if="bgIndex == 4"
            class="level"
            src="@/assets/img/target/bg4.png"
            alt
          />
          <img v-else class="level" src="@/assets/img/target/bg5.png" alt />
          <div class="num">
            {{
              alarmInfo.taskLevel == 1
                ? "一级"
                : alarmInfo.taskLevel == 2
                ? "二级"
                : "三级"
            }}
          </div>
        </div>
        <!-- <div class="favorite">
          <ui-btn-tip
            v-if="alarmInfo.myFavorite == 1"
            class="collection-icon"
            content="取消收藏"
            icon="icon-yishoucang"
            transfer
            @click.native="collection(2)"
          />
          <ui-btn-tip
            v-else
            class="collection-icon"
            content="收藏"
            icon="icon-shoucang"
            transfer
            @click.native="collection(1)"
          />
        </div> -->
      </div>
      <section class="dom-content" v-if="alarmInfo">
        <div class="face-info">
          <div class="face-info-left">
            <div class="right-content">
              <div class="desc">抓拍照片</div>
              <!-- <img :src="alarmInfo.sceneImg" v-viewer="{inline: true}"/> -->
              <details-largeimg
                :algorithmType="1"
                :acrossAppJump="true"
                boxSeleType="rect"
                :info="detailImgInfo"
                @collection="collection($event, 1)"
                :collectionType="14"
                :btnJur="[
                  'tp',
                  'rl',
                  'ytst',
                  'ss',
                  'lx',
                  'fd',
                  'sx',
                  'xz',
                  // 'sc',
                ]"
              >
              </details-largeimg>
              <!-- <ui-image :src="alarmInfo.sceneImg" /> -->
              <!-- <ui-image src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fc-ssl.duitang.com%2Fuploads%2Fblog%2F202104%2F12%2F20210412065010_25e75.jpeg&refer=http%3A%2F%2Fc-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1682156782&t=2e9aaa823b61915751e754aef32265d8" alt="静态库" viewer /> -->
            </div>
          </div>
          <div class="face-info-right">
            <div class="contrast">
              <div
                class="block"
                :class="{ 'out-border': isSelectObject }"
                @click="selectObjectHandler"
              >
                <div class="desc obj">对象</div>
                <ui-image
                  :src="alarmInfo.traitImg"
                  class="inner-border"
                ></ui-image>
                <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
              </div>
              <div class="mid-block">
                <ui-image
                  v-if="bgIndex == 1"
                  class="animation"
                  :src="c1"
                ></ui-image>
                <ui-image
                  v-else-if="bgIndex == 2"
                  class="animation"
                  :src="c2"
                ></ui-image>
                <ui-image
                  v-else-if="bgIndex == 3"
                  class="animation"
                  :src="c3"
                ></ui-image>
                <ui-image
                  v-else-if="bgIndex == 4"
                  class="animation"
                  :src="c4"
                ></ui-image>
                <ui-image v-else class="animation" :src="c5"></ui-image>
                <div class="num" style="color: #ea4a36">同行</div>
              </div>
              <div
                class="block"
                :class="{ 'out-border': !isSelectObject }"
                @click="selectWithHandler"
              >
                <div class="desc together">同行</div>
                <ui-image
                  :src="criminalFaceCaptureVo.traitImg"
                  class="warning-border"
                ></ui-image>
                <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
              </div>
            </div>

            <div class="control-info">
              <div class="left">
                <div class="title">
                  <div class="block"></div>
                  人员详情
                </div>
                <div class="detail-info">
                  <!-- 对象信息 -->
                  <div class="traffic-record left-box">
                    <div class="row">
                      <span class="label">姓名</span>:
                      <span class="message">{{ srcPersonInfo.name }}</span>
                    </div>
                    <div class="row">
                      <span class="label">年龄</span>:
                      <span class="message">{{ srcPersonInfo.age }}</span>
                    </div>
                    <div class="row">
                      <span class="label">性别</span>:
                      <span class="message">
                        {{ srcPersonInfo.sex | commonFiltering(genderList) }}
                      </span>
                    </div>
                    <div class="row">
                      <span class="label">身份证号</span>:
                      <span
                        class="message"
                        :class="srcPersonInfo.idCardNo ? 'herf' : ''"
                        @click="goArchiveHandler(srcPersonInfo.idCardNo)"
                        :title="srcPersonInfo.idCardNo"
                      >
                        {{ srcPersonInfo.idCardNo }}
                      </span>
                    </div>
                    <div class="row">
                      <span class="label">民族</span>:
                      <span class="message">{{
                        srcPersonInfo.national | commonFiltering(nationTypeList)
                      }}</span>
                    </div>
                    <div class="row">
                      <span class="label">联系方式</span>:
                      <span class="message" :title="srcPersonInfo.phoneNo">{{
                        srcPersonInfo.phoneNo
                      }}</span>
                    </div>
                    <div class="row" v-if="showLeftLabel">
                      <span class="label">前科类型</span>:
                      <span
                        class="message waring-color"
                        :title="srcPersonInfo?.bizLabels?.join(',')"
                      >
                        {{ srcPersonInfo?.bizLabels?.join(",") || "--" }}
                      </span>
                    </div>
                  </div>
                  <!-- 前科人信息 -->
                  <div class="traffic-record right-box">
                    <div class="row">
                      <span class="label">姓名</span>:
                      <span class="message">{{ criminalPersonInfo.name }}</span>
                    </div>
                    <div class="row">
                      <span class="label">年龄</span>:
                      <span class="message">{{ criminalPersonInfo.age }}</span>
                    </div>
                    <div class="row">
                      <span class="label">性别</span>:
                      <span class="message">{{
                        criminalPersonInfo.sex | commonFiltering(genderList)
                      }}</span>
                    </div>
                    <div class="row">
                      <span class="label">身份证号</span>:
                      <span
                        class="message"
                        :class="criminalPersonInfo.idCardNo ? 'herf' : ''"
                        @click="goArchiveHandler(criminalPersonInfo.idCardNo)"
                        :title="criminalPersonInfo.idCardNo"
                        >{{ criminalPersonInfo.idCardNo }}</span
                      >
                    </div>
                    <div class="row">
                      <span class="label">民族</span>:
                      <span class="message">{{
                        criminalPersonInfo.national
                          | commonFiltering(nationTypeList)
                      }}</span>
                    </div>
                    <div class="row">
                      <span class="label">联系方式</span>:
                      <span
                        class="message"
                        :title="criminalPersonInfo.phoneNo"
                        >{{ criminalPersonInfo.phoneNo || "--" }}</span
                      >
                    </div>
                    <div class="row">
                      <span class="label">前科类型</span>:
                      <span
                        class="message waring-color"
                        :title="criminalPersonInfo?.bizLabels?.join(',')"
                      >
                        {{ criminalPersonInfo?.bizLabels?.join(",") || "--" }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="title">
                  <div class="block"></div>
                  抓拍详情
                </div>
                <div class="traffic-record">
                  <div class="row">
                    <span class="label">报警时间</span>:
                    <span class="flex-1 message">
                      {{ alarmInfo.absTime }}
                    </span>
                  </div>
                  <div class="row">
                    <span class="label">报警设备</span>:
                    <span class="flex-1 message">
                      {{ baseInfo.deviceName || "--" }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 地图 -->
            <div class="map">
              <mapBase
                v-if="baseInfo.geoPoint"
                :mapLayerConfig="{ showLatestLocation: true }"
                :positionPoints="[
                  { ...detailImgInfo, geoPoint: baseInfo.geoPoint },
                ]"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { addCollection, deleteMyFavorite } from "@/api/user";
import c1 from "@/assets/img/target/c-one.png";
import c2 from "@/assets/img/target/c-two.png";
import c3 from "@/assets/img/target/c-three.png";
import c4 from "@/assets/img/target/c-four.png";
import c5 from "@/assets/img/target/c-five.png";
import valid from "@/assets/img/target/valid.png";
import mapBase from "./map.vue";
import detailsLargeimg from "@/components/detail/details-largeimg.vue";

export default {
  components: {
    mapBase,
    detailsLargeimg,
  },
  props: {
    showNext: {
      type: Boolean,
      default: true,
    },
    showLeftLabel: {
      type: Boolean,
      default: false,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
    tableIndex: {
      type: Number,
      default: 0,
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  watch: {
    tableIndex: {
      handler(row) {
        if (this.first) {
          this.setBaseInfo(this.tableIndex);
          this.first = false;
        }
      },
      deep: true,
      immediate: true,
    },
    // alarmInfo: {
    //   handler(row) {
    //     let info = this.alarmConfigInfo.alarmLevelConfig.find(
    //       (ite) => ite.alarmLevel == row.taskLevel
    //     );
    //     this.bgIndex = Number(info.alarmColour);
    //   },
    // },
  },
  computed: {
    ...mapGetters({
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      genderList: "dictionary/getGenderList", //民族类型
    }),
  },
  data() {
    return {
      c1,
      c2,
      c3,
      c4,
      c5,
      valid,
      formData: {},
      preview: true,
      tabList: [
        {
          name: "场景大图",
        },
        // {
        //   name: '历史视频'
        // }
      ],
      currentTabIndex: 0,
      faceInfo: {},
      collectionList: [{ count: 3 }, { count: 4 }, { count: 5 }, { count: 6 }],
      currentCollectionIndex: 0,
      isChoose: false,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
      checkStatus: true,
      imgUrl: "",
      isRequest: false,
      faceArchives: {},
      status: "",
      alarmInfo: {},
      collectionType: 0,
      pageIndex: 0,
      historyList: [],
      first: true,
      secondSimilatity: "--",
      alarmConfigInfo: {},
      bgIndex: 1,
      srcPersonInfo: {},
      criminalPersonInfo: {},
      criminalFaceCaptureVo: {},
      isSelectObject: true,
      detailImgInfo: {},
      baseInfo: {},
    };
  },
  methods: {
    init(row) {
      this.checkStatus = true;
      this.faceInfo = { ...row };
    },
    /**
     * 收藏
     */
    collection(flag, type = 0) {
      if (type) {
        this.$set(this.alarmInfo, "myFavorite", flag); // 用于不让页面刷新
        this.$emit("collection", flag);
        return;
      }
      var param = {
        favoriteObjectId: this.alarmInfo.alarmTopId,
        favoriteObjectType: 14,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$set(this.alarmInfo, "myFavorite", "1"); // 用于不让页面刷新
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$set(this.alarmInfo, "myFavorite", "0");
          this.$emit("collection", flag);
        });
      }
    },
    // 切换左侧场景图展示前科人和同行人
    selectObjectHandler() {
      if (!this.isSelectObject) {
        this.isSelectObject = true;
        this.detailImgInfo = this.tableList[this.tableIndex].srcFaceCaptureVo;
      }
    },
    selectWithHandler() {
      if (this.isSelectObject) {
        this.isSelectObject = false;
        this.detailImgInfo =
          this.tableList[this.tableIndex].criminalFaceCaptureVo;
      }
    },
    prev() {
      this.pageIndex -= 1;
      if (this.pageIndex == -1) {
        this.pageIndex = this.tableList.length - 1;
      }
      this.setBaseInfo(this.pageIndex);
    },
    next() {
      this.pageIndex += 1;
      if (this.pageIndex == this.tableList.length) {
        this.pageIndex = 0;
      }
      this.setBaseInfo(this.pageIndex);
    },
    setBaseInfo(index) {
      // 所有信息
      this.baseInfo = { ...this.tableList[index] };
      this.tableIndex = index;
      this.isSelectObject = true;
      // 对象抓拍信息
      this.detailImgInfo = this.tableList[index].srcFaceCaptureVo;
      this.alarmInfo = this.tableList[index].srcFaceCaptureVo;
      // 对象人员信息详情
      this.srcPersonInfo = this.tableList[index].srcPersonInfo;
      // 前科人信息详情
      this.criminalPersonInfo = this.tableList[index].criminalPersonInfo;
      // 前科人抓拍信息
      this.criminalFaceCaptureVo = this.tableList[index].criminalFaceCaptureVo;
    },
    goArchiveHandler(idCardNo) {
      if (!idCardNo) return;
      const { href } = this.$router.resolve({
        path: this.$route.path.split("-")?.[0] + "-archive/people-dashboard",
        query: {
          archiveNo: idCardNo,
          source: "people",
          initialArchiveNo: idCardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.waring-color {
  color: #ea4a36;
}

.dom-wrapper {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.dom {
  width: 80%;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;

  .icon {
    position: absolute;
    top: 46%;
    z-index: 999;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    /* text-align: center; */
    background: rgba(0, 0, 0, 0.5);
    /* line-height: 50px; */
    font-size: 30px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .leftIcon {
    left: -70px;
    z-index: 9;
  }

  .rightIcon {
    right: -70px;
    z-index: 9;
  }

  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }

  .level {
    display: flex;
    justify-content: space-between;

    // height: 30px;
    .level-title {
      position: relative;

      .num {
        position: absolute;
        top: 6px;
        color: #fff;
        left: 20px;
        font-weight: 800;
      }
    }

    .favorite {
      margin-right: 20px;
      margin-top: 10px;
    }
  }

  .dom-content {
    position: relative;
    padding: 0 22px 10px 16px;
    font-size: 14px;
    padding-top: 0;

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .face-list {
      display: flex;
      margin-bottom: 5px;

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border: 1px solid #d3d7de;
        margin-right: 8px;
        cursor: pointer;
        position: relative;

        > img {
          width: 100%;
          height: 100%;
        }

        > span {
          display: inline-block;
          width: 25px;
          height: 16px;
          background: #2c86f8;
          text-align: center;
          border-radius: 0px 0px 4px 0px;
          font-size: 12px;
          color: #ffffff;
          position: absolute;
          left: 0;
          top: 0;
        }
      }

      .active {
        border: 3px solid rgba(44, 134, 248, 1);
      }
    }

    .face-info {
      margin-top: 7px;
      display: flex;

      &-left {
        flex: 1;
        width: calc(~" 100% - 435px ");
        height: 660px;

        .right-header {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .tablist {
            height: 28px;
            line-height: 28px;
            width: 400px !important;
            margin: 0;

            .ivu-tabs-tab {
              float: left;
              border: 1px solid #2c86f8;
              border-right: none;
              padding: 0 15px;
              color: #2c86f8;

              &:hover {
                background: #2c86f8;
                color: #ffffff;
              }

              &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-right: none;
              }

              &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border-right: 1px solid #2c86f8;
              }
            }

            .active {
              background: #2c86f8;
              color: #fff;
            }
          }
        }

        .right-content {
          height: 100%;
          // margin-top: 6px;
          // border: 1px solid #d3d7de;
          background: #f9f9f9;

          .desc {
            position: absolute;
            z-index: 9;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            padding: 0 6px;
          }
          .complete-face {
            width: 580px;
            height: 430px;
            position: relative;
            text-align: center;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;

            // > img {
            // width: 100%;
            // height: 100%;
            // }

            > span {
              display: inline-block;
              width: 100%;
              height: 30px;
              line-height: 30px;
              position: absolute;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.5);

              .iconfont {
                color: #fff !important;
                padding: 0 12px;
                cursor: pointer;
              }
            }
          }

          .video {
            /deep/.easy-player {
              margin-top: 60px;
            }
          }
        }
      }

      &-right {
        position: relative;
        width: 420px;
        margin-left: 15px;

        > img {
          width: 200px;
          height: 200px;
          border: 1px solid #d3d7de;
        }

        .contrast {
          height: 150px;
          display: flex;
          align-items: center;
          gap: 10px;

          .block {
            position: relative;
            width: 150px;
            height: 100%;

            .desc {
              position: absolute;
              z-index: 9;
              // background: rgba(0, 0, 0, 0.5);
              color: #fff;
              padding: 0 6px;
              border-radius: 0 0 8px 0;
            }
          }

          .mid-block {
            width: 100px;
            height: 100px;
            position: relative;

            .num {
              position: absolute;
              width: 100%;
              height: 100%;
              top: 0;
              left: 0;
              align-items: center;
              display: flex;
              justify-content: center;
              color: #2c86f8;
            }

            .animation {
              animation: rotation 30s linear infinite;

              /deep/ .ui-image-div {
                border: 0;
                background: transparent;
              }
            }

            @keyframes rotation {
              from {
                transform: rotateZ(360deg);
              }

              to {
                transform: rotateZ(0deg);
              }
            }

            .c1 {
              color: #ea4a36;
            }

            .c2 {
              color: #e77811;
            }

            .c3 {
              color: #ee9f00;
            }

            .c4 {
              color: #36be7f;
            }

            .c5 {
              color: #2c86f8;
            }
          }

          .border {
            border: 1px solid #ebebeb;
          }
        }

        .control-info {
          display: flex;

          // display: none;
          .left {
            flex: 1;
            width: 0;
            z-index: 9;

            .title {
              margin-top: 20px;
              font-weight: 600;

              .block {
                width: 3px;
                background: #2c86f8;
                height: 16px;
                float: left;
                margin-top: 3px;
                margin-right: 6px;
              }
            }

            .row {
              display: flex;
              margin-top: 6px;

              .label {
                color: #999;
                width: 60px;
                // text-align: right;
                text-align-last: justify;
                text-align: justify;
              }

              .message {
                width: 130px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-left: 10px;
              }

              .flex-1 {
                width: unset;
                flex: 1;
              }
            }

            .detail-info {
              display: flex;
              gap: 20px;
            }
          }
        }

        .map {
          margin-top: 10px;
          height: 170px;
          border: 1px solid #f7f7f7;
        }
      }
    }

    .p {
      margin-top: 6px;
      width: 200px;
      display: flex;
      height: 16px;
      line-height: 16px;

      .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 61px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
      }

      .message {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .identity {
        cursor: pointer;
        color: #f29f4c;
      }

      .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
}

.out-border {
  border: 3px solid #2c86f8;
}

.inner-border {
  border: 3px solid #1faf8a;
}

.warning-border {
  border: 3px solid #ea4a36;
}

.title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 10px;
  cursor: pointer;

  > span {
    color: rgba(0, 0, 0, 0.6);
    position: relative;
    margin-right: 34px;
  }

  .active {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
  }

  .active:before {
    content: "";
    position: absolute;
    width: 56px;
    height: 3px;
    bottom: -3px;
    background: #2c86f8;
  }

  .num {
    font-weight: normal;

    > span {
      font-weight: normal;
      color: #2c86f8;
      position: relative;
    }
  }

  .more {
    color: rgba(0, 0, 0, 0.35);
  }
}

.collection-icon {
  /deep/ .iconfont {
    font-size: 14px;
    color: #fff;
  }

  // .ivu-tooltip-rel {
  // margin-top: 3px;
  // }
  /deep/ .icon-shoucang {
    color: #f29f4c !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }

  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}

.herf {
  color: #2c86f8;
  cursor: pointer;
}

/deep/ .ui-image-div {
  cursor: default;
}

/deep/ .map {
  cursor: default !important;
}

.ivu-icon {
  font-size: 60px;
}

.ivu-icon-md-arrow-dropleft:before {
  font-size: 60px;
}

.grey {
  /deep/ .ivu-icon {
    color: #5e5e5e;
  }
}

/deep/ .ivu-icon-md-arrow-dropleft {
  margin-left: -6px;
}

/deep/ .ivu-icon-md-arrow-dropright {
  margin-left: 6px;
}

.status2 {
  width: 160px;
}
.obj {
  background: #1faf8a;
}
.together {
  background: #ea4a36;
}
</style>
