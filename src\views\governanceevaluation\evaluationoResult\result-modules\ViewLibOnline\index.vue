<!-- 轨迹准确率 -->
<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      @on-change-code="onChangeOrgRegion"
    >
      <tag-view
        slot="mid"
        :list="reallyTagList"
        :default-active="defaultActive"
        @tagChange="changeStatus"
        ref="tagView"
        class="tag-view"
      ></tag-view>
    </result-title>
    <component :is="componentName" v-bind="handleProps()" @viewDetail="viewDetail">
      <template #customcontent="{ row }">
        <span
          :style="{
            color:
              row.isOnline === '1' ? 'var(--color-success)' : row.isOnline === '2' ? 'var(--color-failed)' : '#2B84E2',
          }"
          >{{ row.isOnline === '1' ? '在线' : row.isOnline === '2' ? '离线' : '报备' }}</span
        >
      </template>
    </component>
    <online-details v-model="onlineDetailShow" :code="code"></online-details>
  </div>
</template>
<script>
export default {
  name: 'ViewLibOnline',
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalEcharts',
      tagList: Object.freeze([
        {
          label: '统计图表',
          value: 'StatisticalEcharts',
        },
        {
          label: '统计列表',
          value: 'StatisticalResult',
        },
      ]),
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
      onlineDetailShow: false,
      code: null,
    };
  },
  created() {
    this.reallyTagList = this.tagList.map((item) => item.label);
  },
  methods: {
    changeStatus(index) {
      this.defaultActive = index;
      this.componentName = this.tagList[index].value;
    },
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    // 动态组件 - 动态传参
    handleProps() {
      // 检测明细组件需要的参数 review-particular
      let props = {
        activeIndexItem: this.activeIndexItem,
        formItemData: [],
      };
      return props;
    },
    async viewDetail(row) {
      try {
        this.code = row.civilCode;
        this.onlineDetailShow = true;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ResultTitle: require('../../components/result-title.vue').default,
    StatisticalEcharts: require('./statistical-echarts.vue').default,
    StatisticalResult: require('../../common-pages/statistical-results/index.vue').default,
    OnlineDetails: require('./components/online-details.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
  @{_deep}.active-blue td {
    color: var(--color-bluish-green-text) !important;
  }
}
</style>
