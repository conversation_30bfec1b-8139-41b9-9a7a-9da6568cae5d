let tableColumns = [
  { type: 'selection', align: 'center', width: 50 },
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '设备编码', key: 'deviceId', align: 'left', width: 170 },
  { title: '设备名称', key: 'deviceName', align: 'left', minWidth: 200, tooltip: true },
  { title: '所属单位', key: 'orgName', align: 'left', minWidth: 200, tooltip: true },
  { title: '行政区划', key: 'civilName', align: 'left', width: 120, tooltip: true },
  { title: 'IP地址', key: 'ipAddr', align: 'left', minWidth: 130, tooltip: true },
  {
    title: '经度',
    slot: 'longitude',
    align: 'left',
    width: 110,
    tooltip: true,
  },
  {
    title: '纬度',
    slot: 'latitude',
    align: 'left',
    width: 110,
    tooltip: true,
  },
  { title: '点位类型', key: 'sbdwlxText', align: 'left', width: 130, tooltip: true },
  { title: '摄像机功能类型', key: 'sbgnlxText', align: 'left', width: 130, tooltip: true },
  {
    title: '检测状态',
    slot: 'checkStatusText',
    align: 'left',
    width: 90,
    minWidth: 200,
    tooltip: true,
  },
];
// 图层信息
const layers = {
  locationLayer: {
    name: 'locationLayer',
    desc: '定位',
    symbol: 'position',
    bubble: 'EditFormBubble',
    bubbleable: true,
    selectable: true,
    editable: true,
    bubbleSkin: 'edit-bubble-box',
    level: [6, 21],
  },
  normalLayer: {
    name: 'normalLayer',
    desc: '合格点位',
    symbol: 'map-camera-normal',
    bubble: 'EditFormBubble',
    bubbleable: true,
    bubbleSkin: 'edit-bubble-box',
    level: [15, 21],
  },
  abnormalLayer: {
    name: 'abnormalLayer',
    desc: '异常点位',
    symbol: 'map-camera-abnormal',
    bubble: 'EditFormBubble',
    bubbleable: true,
    bubbleSkin: 'edit-bubble-box',
    level: [15, 21],
  },
  clustLayer: {
    name: 'clustLayer',
    type: 'Clust',
    animate: true,
    bubbleable: false,
    selectable: false,
    editable: false,
    level: [6, 14],
  },
};
export default {
  tableColumns,
  layers,
};
