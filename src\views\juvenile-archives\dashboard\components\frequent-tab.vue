<template>
  <div class="alarm">
    <div v-if="list.length > 0" class="my-swiper-container" id="mySwiper2">
      <swiper
        ref="mySwiper"
        :options="swiperOption"
        class="my-swiper"
        :class="{ 'swiper-center': list.length < 3 }"
        id="frequent-swiper"
      >
        <template v-for="(item, index) in list">
          <swiper-slide :key="index">
            <div class="swiper-item" @click="() => handleDetailFn(item, index)">
              <div>
                <div class="primary place-box">{{ item.placeName }}</div>
                <FrequentAlarm :data="item"></FrequentAlarm>
              </div>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="frequentLeft"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="frequentRight"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail ref="videoDetail"></CaptureDetail>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import FrequentAlarm from "./collect/frequent-alarm.vue";
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
import {
  getAppearInRecreationPageList,
  getConfigPlaceSecondLevels,
} from "@/api/monographic/juvenile.js";
export default {
  name: "frequentTab",
  components: {
    swiper,
    swiperSlide,
    FrequentAlarm,
    CaptureDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      list: [],
      swiperOption: {
        slidesPerView: "3",
        // slidesPerGroup: 3,
        speed: 1000,
        navigation: {
          nextEl: "#frequentLeft",
          prevEl: "#frequentRight",
        },
        observer: true,
        observeParents: true,
      },
      total: 0,
      detailShow: false,
      placeList: [],
    };
  },
  watch: {},
  computed: {},
  async created() {},
  async mounted() {
    await this.getPlaceInfo();
    this.placeList.forEach((item) => {
      this.getInfo(item);
    });
  },
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.queryInfo(1, item.idCardNo);
    },
    getInfo({ key, value }) {
      this.loading = true;
      let param = {
        pageNumber: 1,
        pageSize: 1,
        idCardNo: this.idCardNo,
        placeSecondLevelCodes: key ? [key] : [],
      };
      getAppearInRecreationPageList(param)
        .then(({ data }) => {
          let arr =
            data.entities.map((item) => {
              return {
                ...item,
                placeName: value,
                placeKey: key,
              };
            }) || [];
          this.list.push(...arr);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async getPlaceInfo() {
      let { data = [] } = await getConfigPlaceSecondLevels();
      this.placeList = data.map((item) => {
        return { key: item.typeCode, value: item.typeName };
      });
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;

  span {
    color: #2c86f8;
  }
}

.my-swiper-container {
  padding: 0 30px;
  position: relative;
  .my-swiper {
    margin: auto;

    .swiper-item {
      width: 100%;
      display: flex;
      justify-content: center;
      .place-box {
        width: 100%;
        text-align: center;
        font-size: 16px;
        margin-bottom: 10px;
      }
    }
  }
}
.swiper-center {
  /deep/ .swiper-wrapper {
    justify-content: center;
  }
}
.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;

  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 20px;
}

.swiper-button-next {
  right: 20px;
}
</style>
