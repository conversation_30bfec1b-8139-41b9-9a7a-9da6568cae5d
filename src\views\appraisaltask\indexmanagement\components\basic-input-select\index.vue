<template>
  <ui-modal v-model="visible" title="设置上级建档数量" :styles="styles" class="ui-modal" @query="query">
    <div class="area-container">
      <div class="tree-title">
        <span class="org-name">组织机构名称</span>
        <span class="video">本单位上级视频监控建档数量</span>
        <span class="face">本单位上级人脸卡口建档数量</span>
        <span class="car">本单位上级车辆卡口建档数量</span>
        <span class="is-checked">是否检测</span>
        <span class="is-all">全部</span>
      </div>
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          :highlight-current="false"
          node-key="orgCode"
          :tree-data="areaTreeData"
          :default-props="defaultProps"
          expandAll
          checkStrictly
        >
          <template #label="{ node, data }">
            <span class="tree-name ellipsis vt-middle" :title="data.orgName">{{ data.orgName }}</span>
            <div class="options fr" :class="data.children && 'is_parent'">
              <Input
                v-show="data.check && edit"
                v-model="data.sxjValue"
                maxlength="8"
                class="width-sm mr-100 vt-middle"
                placeholder="请输入本单位上级视频监控建档数量"
              ></Input>
              <Input
                v-show="data.check && edit"
                v-model="data.rlkkValue"
                maxlength="8"
                class="width-sm mr-100 vt-middle"
                placeholder="请输入本单位上级人脸卡口建档数量"
              ></Input>
              <Input
                v-show="data.check && edit"
                v-model="data.clkkValue"
                maxlength="8"
                class="width-sm mr-100 vt-middle"
                placeholder="请输入本单位上级车辆卡口建档数量"
              ></Input>
              <i-switch
                v-model="data.check"
                size="small"
                class="mr-100 vt-middle"
                @on-change="check($event, node, data)"
              ></i-switch>
              <Checkbox
                v-model="data.checkAll"
                class="mr-50 vt-middle"
                @on-change="checkAll($event, node, data)"
                :style="{ visibility: !data.children ? 'hidden' : '' }"
                >{{ `${data.checkAll ? '取消' : '全部'} ` }}</Checkbox
              >
            </div>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'basic-input-select',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'orgCode',
    },
    data: {
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      styles: {
        width: '5rem',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
      areaTreeData: [],
      form: {
        regionType: 'province',
        numberType: 'sxjValue',
        number: 1000,
      },
    };
  },
  created() {
    this.visible = true;
  },
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialOrgList',
    }),
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        // this.init()
      } else {
        this.areaTreeData = [];
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler: function () {
        this.init();
      },
    },
  },
  methods: {
    clickBatchInput() {
      if (!this.form.number) {
        return this.$Message.error('请输入达标数量');
      }
      this.batchInput(this.areaTreeData);
    },
    /*
    1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所
    * */
    batchInput(data) {
      data.map((item) => {
        if (item.regionType === '1' && this.form.regionType === 'province') {
          this.$set(item, this.form.numberType, this.form.number);
        } else if (['2', '3', '4', '5'].includes(item.regionType) && this.form.regionType === 'city') {
          this.$set(item, this.form.numberType, this.form.number);
        } else if (['6', '7', '8'].includes(item.regionType) && this.form.regionType === 'region') {
          this.$set(item, this.form.numberType, this.form.number);
        } else if (item.regionType === '9' && this.form.regionType === 'policeStation') {
          this.$set(item, this.form.numberType, this.form.number);
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },
    setSourceDataToTreeData(source, target) {
      target.map((item) => {
        source.map((value) => {
          if (item.orgCode === value.key) {
            this.$set(item, 'check', true);
            this.$set(item, 'orgCode', value.key);
            this.$set(item, 'orgName', value.name);
            this.$set(item, 'sxjValue', value.sxjValue);
            this.$set(item, 'rlkkValue', value.rlkkValue);
            this.$set(item, 'clkkValue', value.clkkValue);
          }
        });
      });
    },
    init() {
      let treeData = JSON.parse(JSON.stringify(this.treeData));
      this.setSourceDataToTreeData(this.data, treeData);
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'id', 'parentId');
    },
    query() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.areaTreeData);
      let result = this.validateCheckedNodes(this.checkedTreeData);
      if (result) {
        this.$emit('query', this.checkedTreeData);
        this.visible = false;
      }
    },
    getCheckedNodes(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push({
            key: item.orgCode,
            name: item.orgName,
            sxjValue: item.sxjValue || '',
            rlkkValue: item.rlkkValue || '',
            clkkValue: item.clkkValue || '',
          });
        }
        if (item.children) {
          this.getCheckedNodes(item.children);
        }
      });
    },
    validateCheckedNodes(data) {
      if (!data.length) {
        this.$Message.error('请选择组织机构并设置上级建档数量');
        return false;
      }
      for (let i = 0; i < data.length; i++) {
        let { sxjValue, rlkkValue, clkkValue, name } = data[i];
        let reg = /^[1-9]\d*$/;
        if (sxjValue && !reg.test(sxjValue)) {
          this.$Message.error(`${name}本单位上级视频监控建档数量格式不正确`);
          return false;
        }
        if (rlkkValue && !reg.test(rlkkValue)) {
          this.$Message.error(`${name}本单位上级人脸卡口建档数量格式不正确`);
          return false;
        }
        if (clkkValue && !reg.test(clkkValue)) {
          this.$Message.error(`${name}本单位上级车辆卡口建档数量格式不正确`);
          return false;
        }
        // if (!sxjValue || !rlkkValue || !clkkValue) {
        //    this.$Message.error(`${name}上级建档数量不能为空`)
        //   return false
        // }
      }
      return true;
    },
    checkAll(val, node, data) {
      this.check(val, node, data);
      if (node.childNodes) {
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node, data) {
      this.$set(data, 'check', val);
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
@{_deep} .ivu-modal {
  width: 1563px !important;
}
.w100 {
  width: 100px;
}
.w230 {
  width: 230px;
}
.w160 {
  width: 160px;
}
.area-container {
  .area-filter {
    line-height: 34px;
  }
  .tree-wrapper {
    .ui-search-tree {
      .options {
        display: inline-block;
      }
      .tree-name {
        display: inline-block;
        max-width: 350px;
      }
    }
  }
}
</style>
