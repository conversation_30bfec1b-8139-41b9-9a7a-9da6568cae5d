<template>
  <ui-card :title="title" class="recommended-information">
    <div class="recommended-information-content">
      <div v-for="(item, $index) in list" :key="$index" class="recommended-information-card">
        <div :style="{background: item.headerBg}" class="card-head">
          <div class="head-info">
            <div :class="item.icon" class="iconfont"></div>
            <div class="head-title">{{item.typeName}}</div>
          </div> 
          <div class="list-count">{{item.data.length}}</div>
        </div>
        <div class="card-content">
          <div class="content-ul card-border-color card-bg"> 
            <div v-for="(site, $siteIndex) in item.data" :key="$siteIndex" class="content-li">
              <div class="recommended-content-info">
                <div class="content-tag recommended-tag">推荐</div>
                <div class="content-text ellipsis">{{site.recommendInfo}}</div>
              </div>
              <!-- 关联IMSI显示明细 -->
              <div v-if="item.type === '3'" class="content-source" @click="detailHandle">
                明细<span class="source-count">{{site.sources.length}}</span>
              </div>
              <!-- 其他显示来源 -->
              <Poptip v-else trigger="hover" transfer placement="bottom-start" popper-class="source-poptip">
                <div class="content-source">来源<span class="source-count">{{site.sources.length}}</span></div>
                <div slot="content" class="source-ul">
                  <div v-for="(source, $sourceIndex) in site.sources" :key="$sourceIndex" class="source-li">
                    来源{{$sourceIndex+1}}：<div class="source-text">{{source}}</div>
                  </div>
                </div>
              </Poptip>
            </div>
            <div v-if="item.data.length > 1" class="triangle-icon"></div>
          </div>
        </div>
      </div>
    </div>
    <ui-loading v-if="loading"/>
    <ui-empty v-if="(!list || !list.length) && !loading"/>
    <!-- IMSI 明细 -->
    <ui-modal 
      v-model="visible" 
      title="明细"
      :r-width="686">
        <div ref="detailContainer" class="detail-container">
          <Timeline>
            <TimelineItem v-for="(item, $index) in 10" :key="$index">
              <div class="line-card-ul">
                <div class="line-card card-border-color card-bg">
                  <ui-image viewer type="people" src="@/assets/img/demo/face/04.jpg" class="line-card-img card-border-color"/>
                  <div class="line-card-content card-border-color">
                    <div class="line-card-time">
                      <i class="iconfont icon-time"></i>
                      <div class="text-color">2022-02-17 12:01:02</div>
                    </div>
                    <div class="line-card-address">
                      <i class="iconfont icon-location"></i>
                      <div class="text-color two-ellipsis">浙江省温州市泰顺县建设大道690号</div>
                    </div>
                  </div>
                </div>
                <div class="line-card card-border-color card-bg">
                  <div class="imsi-card card-border-color">
                    <i class="iconfont icon-imsi primary"></i>
                  </div>
                  <div class="line-card-content card-border-color">
                    <div class="line-card-time">
                      <i class="iconfont icon-time"></i>
                      <div class="text-color">2022-02-17 12:01:02</div>
                    </div>
                    <div class="line-card-address">
                      <i class="iconfont icon-location"></i>
                      <div class="text-color two-ellipsis">浙江省温州市泰顺县建设大道690号</div>
                    </div>
                  </div>
                </div>
              </div>
            </TimelineItem>
          </Timeline>
        </div>
        <div slot="footer"></div>
    </ui-modal>
  </ui-card>
</template>
<script>
  export default {
    props: {
      title: {
        type: String,
        default: ''
      },
      list: {
        type: Array,
        default: () => []
      },
      loading: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false
      }
    },
    created() {},
    methods: {
      // 查看明细列表
      detailHandle() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.detailContainer.scrollTop = 0
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .recommended-information {
    /deep/.card-content {
      padding: 20px 15px !important;
      min-height: 118px;
      box-sizing: border-box;
    }
    .recommended-information-content {
      display: flex;
      margin: 0 -5px;
      .recommended-information-card {
        width: 25%;
        border-radius: 4px;
        padding: 0 5px;
        .card-head {
          width: 100%;
          height: 38px;
          // background: linear-gradient(144deg, #3CD2AA 0%, #1FAF8A 100%);
          padding: 0 15px;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          .head-info {
            display: flex;
            align-items: center; 
            .iconfont {
              font-size: 20px;
              margin-right: 12px;
              line-height: 20px;
            }
            .head-title {
              font-size: 14px;
              font-weight: bold;
              font-family: 'MicrosoftYaHei-Bold';
              line-height: 20px;
            }
          }
          .list-count {
            font-size: 24px;
            font-weight: bold;
            font-family: 'MicrosoftYaHei-Bold';
          }
        }
        .card-content {
          cursor: pointer;
          min-height: 40px;
          position: relative;
          .content-ul {
            width: 100%;
            max-height: 40px;
            box-sizing: border-box;
            border-top: none;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
            overflow: hidden;
            transition: max-height 0.5s;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 10;
            border: 1px solid #fff;
            .triangle-icon {
              position: absolute;
              bottom: 2px;
              right: 2px;
              border-radius: 2px;
              overflow: hidden;
              width: 8px;
              height: 8px;
              transform: rotate(0deg);
              // transition: transform 0.1s;
            }
            .triangle-icon::after {
              content: '';
              width: 0;
              height: 0;
              position: absolute;
              bottom: 0;
              right: 0;
              border-bottom: 8px solid #D8D8D8;
              border-left: 8px solid transparent;
            }
          }
          .content-ul:hover {
            max-height: 500px;
            transition: max-height 1s;
            // overflow-y: auto;
            .triangle-icon {
              transform: rotate(180deg);
              // transition: transform 0.1s;
            }
          }
          .content-li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // margin-bottom: 10px;
            height: 38px;
            padding: 0 15px;
            .content-info, .recommended-content-info {
              display: flex;
              align-items: center;
              flex: 1;
              overflow: hidden;
              .content-tag {
                margin-right: 10px;
                font-size: 12px;
                color: #fff;
                line-height: 20px;
                padding: 1px 6px;
                border-radius: 2px;
              }
              .recommended-tag {
                background: #F29F4C;
              }
              .other-tag {
                background: #D3D7DE;
                color: rgba(0, 0, 0, 0.35);
              }
              .content-text {
                font-size: 14px;
                line-height: 20px;
                flex: 1;
              }
            }
            .recommended-content-info {
              .content-tag, .content-text {
                font-family: 'MicrosoftYaHei-Bold';
                font-weight: bold;
              }
            }
            .content-source {
              font-size: 12px;
              display: flex;
              align-items: center;
              .source-count {
                margin-left: 2px;
                font-weight: bold;
              }
            }
          }
        }
      }
    }
    /deep/ .ivu-modal-body {
      padding-right: 10px !important;
    }
    /deep/ .ivu-modal-footer {
      display: none;
    }
    .detail-container {
      max-height: 600px;
      overflow: auto;
      .line-card-ul {
        display: flex;
        padding-top: 2px;
        .line-card {
          width: 300px;
          height: 90px;
          box-sizing: border-box;
          border: 1px solid transparent;
          display: flex;
          position: relative;
          padding: 10px;
          border-radius: 4px;
          .line-card-img {
            width: 70px;
            height: 100%;
            box-sizing: border-box;
            border: 1px solid transparent;
          }
          .imsi-card {
            width: 70px;
            height: 100%;
            box-sizing: border-box;
            border: 1px solid transparent;
            display: flex;
            justify-content: center;
            align-content: center;
            .iconfont {
              font-size: 32px;
              line-height: 70px;
            }
          }
          .line-card-content {
            display: flex;
            flex: 1;
            padding-left: 10px;
            flex-direction: column;
            overflow: hidden;
            border-left: none;
            .line-card-time {
              display: flex;
              font-size: 14px;
              line-height: 20px;
              .iconfont {
                font-size: 14px;
                color: #888;
                margin-right: 6px;
              }
            }
            .line-card-address {
              display: flex;
              font-size: 14px;
              line-height: 20px;
              margin-top: 8px;
              .iconfont {
                font-size: 14px;
                color: #888;
                margin-right: 6px;
              }
            }
          }
        }
        &>div:first-child {
          margin-right: 10px;
        }
      }
      /deep/ .ivu-timeline {
        .ivu-timeline-item {
          padding-bottom: 0;
          .ivu-timeline-item-head {
            width: 13px;
            height: 13px;
            background: #2C86F8;
            border: 3px solid #fff;
          }
          .ivu-timeline-item-content {
            padding-bottom: 18px;
          }
        }
        &>li:last-child {
          .ivu-timeline-item-content {
            padding-bottom: 0;
          }
        }
      }
    }
  }

</style>