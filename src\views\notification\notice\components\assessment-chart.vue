<template>
  <div class="assessmentChart" v-ui-loading="{ loading: echartsLoading, tableData: dataY }">
    <draw-echarts v-if="dataY.length" ref="drawEcharts" class="charts" :echartOption="echartOption"></draw-echarts>
    <span class="next-echart" v-if="dataY.length > 13">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('drawEcharts', dataY, [], 13)"></i>
    </span>
  </div>
</template>

<script>
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'assessmentChart',
  mixins: [dataZoom],
  props: {
    monthRank: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      echartsLoading: false,
      echartOption: {},
      dataX: [],
      dataY: [],
    };
  },
  methods: {
    echartInfo() {
      let option = {
        tooltip: {
          show: true,
        },
        title: {
          text: '考核得分',
          textStyle: {
            fontSize: 12,
          },
        },
        grid: {
          top: 30,
          right: '3%',
          left: '1%',
          bottom: '2%',
          containLabel: true,
        },
        xAxis: [
          {
            data: this.dataX,
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              formatter: function (params) {
                let newName = '';
                if (params.length > 3) {
                  newName = params.substring(0, 3) + '...';
                } else {
                  newName = params;
                }
                return newName;
              },
            },
          },
        ],
        yAxis: {
          type: 'value',
          min: 0,
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            fontSize: 12,
            formatter: function (value) {
              return value;
            },
          },
        },
        series: [
          {
            type: 'bar',
            data: this.dataY,
            barWidth: 33,
            z: 10,
            zlevel: 0,
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: $var('--linear-gradient-blue-5-end'),
                },
                {
                  offset: 1,
                  color: $var('--linear-gradient-blue-5-start'),
                },
              ]),
            },
          },
        ],
      };
      this.echartOption = option;
      this.echartsLoading = false;
      setTimeout(() => {
        this.setDataZoom('drawEcharts', [], 13);
      });
    },
  },
  watch: {
    monthRank(val) {
      if (!val) return false;
      this.echartsLoading = true;
      let chartList = JSON.parse(val);
      this.dataY = [];
      this.dataX = chartList.map((item) => {
        this.dataY.push(item.standardsValue);
        return item.regionName;
      });
      this.echartInfo();
      this.echartsLoading = false;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>

<style lang="less" scoped>
.assessmentChart {
  min-height: 200px;
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
</style>
