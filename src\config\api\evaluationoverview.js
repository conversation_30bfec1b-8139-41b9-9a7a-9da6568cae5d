// 评测报表接口
export default {
  getIndexOverviewReportModeData:
    '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getIndexOverviewReportModeData', //根据组织机构查任务
  exportIndexOverviewReportData:
    '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/exportIndexOverviewReportData', //根据组织机构查任务
  exportIndexOverviewReportModeDataByAvg:
    '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/exportIndexOverviewReportModeDataByAvg', // 新版评测概览导出
  // 详情
  // getDropdownInfo:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getDropdownInfo', //下拉筛选
  // getStatInfo:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getStatInfo', //数量统计
  // getGraphsInfo:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getGraphsInfo', //柱状图统计
  // getRankInfo:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getRankInfo', //排名
  // getDetailData:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getDetailData', //详情列表
  // getAbnormalLabel:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getAbnormalLabel', //异常列表
  // exportDetailData:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/exportDetailData', //导出
  exportAccuracy: '/ivdg-evaluation-app/evaluationDeviceResult/exportAccuracy', //填报准确率导出
  // getPolyData:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getPolyData', //详情列表聚档模式
  // getSecondaryPopUpData:
  //   '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getSecondaryPopUpData', //详情列表聚档模式抓拍详情
  //场所填报准确率
  queryErrorMessageList: '/ivdg-evaluation-app/evaluationDeviceResult/queryErrorMessageList', //场所填报正确率 异常数据列表
  getPlaceStatInfo: '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getStatInfo', //场所填报正确率 统计
  queryEvaluationPlaceResult: '/ivdg-evaluation-app/evaluationPlaceResult/queryEvaluationPlaceResult', //场所填报正确率 查看不合格原因
  getPlaceDetailData: '/ivdg-evaluation-app/evaluationPlaceResult/getDetailData', //场所填报正确率 表格
  // exportPlaceDetailData: '/ivdg-evaluation-app/evaluationPlaceResult/exportDetailData', //场所填报正确率 导出
  getPlaceGraphsInfo: '/ivdg-evaluation-app/evaluation/resultIndex/overviewDetail/getGraphsInfo', //场所填报正确率 echarts
  getNavigationInfo: '/ivdg-evaluation-app/evaluation/app/taskResult/getNavigationInfo', // 获取左侧导航栏信息
  // 1.1版本
  getDropdownInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getDropdownInfo', //检测结果详情-下拉筛选
  getStatInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getStatInfo', //检测结果详情-统计
  getRankInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getRankInfo', //检测结果详情-排名
  getGraphsInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getGraphsInfo', //检测结果详情-柱状图统计
  getAbnormalLabel: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getAbnormalLabel', //检测结果详情-异常原因标签
  getDetailData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getFirstModelData', //检测结果详情-图形模式
  getPolyData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getSecondModelData', //检测结果详情-聚档模式
  getSecondaryPopUpData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getPopUpData', //检测结果详情-弹框查询数据
  getVideoDetail: '/ivdg-evaluation-app/evaluation/app/index/result/pageList', // 视频流指标详情
  getCurrentUserSysOrgListByTaskId:
    '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getCurrentUserSysOrgList', //填报准确率：获取当前登录用户对应任务的本级和下级组织机构 echarts
  getIndexStatistics: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getIndexStatistics', //环形指标统计
  getHistogramIndexDetail: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getHistogramIndexDetail', //下级指标评价统计
  exportDeviceDetailData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/exportFirstModelData', // 导出人脸指标数据
  exportSecondModelData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/exportSecondModelData', // 导出人脸指标数据

  queryChildByCode: '/qsdi-system-service/system/region/queryChildByCode', // 行政区划获取下级
  // getOrgDataByOrgCode: '/qsdi-system-service/system/org/getOrgDataByOrgCode', // 组织机构获取下级
  getChildRegionByCode: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getChildRegionByCode', // 行政区划获取下级
  getAllChildOrgByOrgCode: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getAllChildOrgByOrgCode', // 组织机构获取下级

  // 概览统计
  getAccessDataCount: '/ivdg-asset-app/deviceStatistics/getAccessDataCount',
  getDeviceOverview: '/ivdg-asset-app/deviceStatistics/getDeviceOverview',
  getThirdModelData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getThirdModelData', //检测结果详情-聚档模式
  exportOnlineRatePromoteDetailData:
    '/ivdg-evaluation-app/evaluation/app/index/result/exportOnlineRatePromoteDetailData', // 查看在线/离线数量导出
  exportOnlineRatePromotePopData: '/ivdg-evaluation-app/evaluation/app/index/result/exportOnlineRatePromotePopData', // 本年度视频历史最高在线情况数据导出

  // 人工复核
  manualRecheckBatch: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/manualRecheckBatch', // 批量人工复核
  manualRecheck: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/manualRecheck', // 人工复核
  pushRecheckQueue: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/pushRecheckQueue', // 更新统计
  getUrlIndexErrorCode: '/ivdg-evaluation-app/evaluation/app/index/getUrlIndexErrorCode', // 获取错误原因
  showRecountBtn: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/showRecountBtn', // 更新统计(2)
  getVideoQualityPassIndexErrorCode: '/ivdg-evaluation-app/evaluation/app/index/getVideoQualityPassIndexErrorCode', // 获取视频流质量合格率指标的错误编码

  //备存库
  getUsableIndexList: '/ivdg-evaluation-app/evaluation/app/device/keep/library/getUsableIndexList', //1.备存库-查询可前端显示使用的指标集合
  getFaceDetailData: '/ivdg-evaluation-app/evaluation/app/device/keep/library/getFaceDetailData', //备存库-查人脸流水结果数据-分页查询
  getVehicleDetailData: '/ivdg-evaluation-app/evaluation/app/device/keep/library/getVehicleDetailData', //备存库-查车辆流水结果数据-分页查询

  getListTaskSchemes: '/ivdg-evaluation-app/evaluation/app/taskScheme/listTaskSchemes', //全量的任务
  getHistoryBatchInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getHistoryBatchInfo', //历史任务
  sharePushResult: '/ivdg-data-share-service/VIID/API/pushResult', //一键推送

  programRecheck: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/programRecheck', // 复检（批量复检）
  terminateRunningReinspectJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/terminateRunningReinspectJob', // 复检终止
  // triggerIndexJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/triggerIndexJob', // 复检终止

  queryOsdDetailById: '/ivdg-evaluation-app/evaluation/videoVideo/queryOsdDetailById/', // osd字幕详情 根据ID查询详情信息
  // 评测概览 - 报表模式（新）
  getIndexOverviewReportModeDataByAvg:
    '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getIndexOverviewReportModeDataByAvg',

  // 专题考核
  getHistoryBatchInfoByCustom: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getHistoryBatchInfoByCustom', // 检测结果详情-检测时间-下拉选项 自定义选择，返回第N轮
  getCustomTrees: '/ivdg-evaluation-app/evaluation/app/taskScheme/getCustomTrees', // 获取自定义任务菜单树
  getStatInfoList: '/ivdg-evaluation-app/sq/getStatInfoList', // 专题考核列表
  getSpecialDetailByMonth: '/ivdg-evaluation-app/sq/getSpecialDetailByMonth', // 专题考核--月平均的检测明细
  getSpecialDetailByDay: '/ivdg-evaluation-app/sq/getSpecialDetailByDay', // 专题考核--日平均时的检测明细
  exportStatInfoList: '/ivdg-evaluation-app/sq/exportStatInfoList', // 专题考核列表导出
  exportSecondStatInfoList: '/ivdg-evaluation-app/sq/exportSecondStatInfoList', // 日平均时的检测明细导出
  getUnqualifiedInfo: '/ivdg-evaluation-app/sq/getUnqualifiedInfo', // 检测明细-不合格原因下拉数据
  exportSpecialDetailByMonth: '/ivdg-evaluation-app/sq/exportSpecialDetailByMonth', // 专题考核--月平均的检测明细导出
  getStatisticalModel: '/ivdg-evaluation-app/sq/getStatisticalModel', // 专题考核--获取统计项

  // 批量复检 按钮
  getReinspectStatusByBatchId: '/ivdg-evaluation-app/evaluation/app/taskResult/getReinspectStatusByBatchId',

  // 结果比对
  getContrastResultData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getContrastResultData',
  getContrastResultDetail: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getContrastResultDetail',
  queryOnlineChangeDetail: '/ivdg-evaluation-app/evaluation/app/home/<USER>', // 在线变化 --详情
  exportContrastResultData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/exportContrastResultData',
  exportContrastResultDetail: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/exportContrastResultDetail',

  queryReasonList: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/queryReasonList', // 联网共享平台在线查询错误原因列表
  pageListExport: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/pageListExport',
  //工单查询
  queryOsdDetailByBatchIdAndDeviceId: '/ivdg-evaluation-app/evaluation/videoVideo/queryOsdDetailByBatchIdAndDeviceId', //get请求：batchId deviceId
  queryVideoResultByBatchIdAndDeviceId:
    '/ivdg-evaluation-app/evaluation/videoVideo/queryVideoResultByBatchIdAndDeviceId', //post请求：传：batchId， deviceId

  // get 查询 是否需要显示 【建档匹配率】和乘积
  getConfigByKeys: '/ivdg-evaluation-app/config/getConfigByKeys',

  // 每日任务运行情况
  taskIndexPageListByDay: '/ivdg-evaluation-app/evaluation/app/taskScheme/taskIndexPageListByDay',
  getHideBatchList: '/ivdg-evaluation-app/evaluation/hide/task/getHideBatchList', // 上下级对账批次列表
  updateConfigByVideoDeviceRevocation: '/ivdg-evaluation-app/evaluation/hide/task/updateConfigByVideoDeviceRevocation', // 更新资产对账配置

  // 获取评测结果总数
  getFirstModelDataTotal: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getFirstModelDataTotal', // 更新资产对账配置
};
