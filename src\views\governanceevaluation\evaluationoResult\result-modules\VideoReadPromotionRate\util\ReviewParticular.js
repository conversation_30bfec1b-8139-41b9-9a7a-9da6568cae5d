import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';

export const iconStaticsList = [
  {
    name: '检测地市数量:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'detectionCityOrCountyCount',
  },
  {
    name: '达标地市数量:',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'detectionCityOrCountyQualifiedCount',
  },
  {
    name: '不达标地市数量:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'detectionCityOrCountyUnQualifiedCount',
  },
  {
    name: '治理前可调阅率:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'beforeRate',
    type: 'percent', // 百分比
  },
  {
    name: '治理后可调阅率:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'afterRate',
    type: 'percent', // 百分比
  },
  {
    name: '可调阅提升率:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    style: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-xinxichayishebei',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
  {
    type: 'select',
    key: 'online',
    label: '在线状态',
    placeholder: '请选择在线状态',
    options: [
      {
        value: '1',
        label: '在线',
      },
      {
        value: '2',
        label: '离线',
      },
    ],
  },
  {
    type: 'select',
    key: 'normal',
    label: '完好状态',
    placeholder: '请选择完好状态',
    options: [
      {
        value: '1',
        label: '取流及时响应',
      },
      {
        value: '2',
        label: '取流超时响应',
      },
    ],
  },
  {
    type: 'select',
    key: 'canPlay',
    label: '可用状态',
    placeholder: '请选择可用状态',
    options: [
      {
        value: '1',
        label: '取流成功',
      },
      {
        value: '2',
        label: '取流失败',
      },
    ],
  },
];
const indexDetectionModeMap = {
  '1': '在线状态',
  '2': '完好状态',
  '3': '可用状态',
  '4': '联网质量',
  null: '',
};
export const tableColumns = (columnParams) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '设备编码',
      key: 'deviceId',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '组织机构',
      key: 'orgName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '设备物理状态 ',
      slot: 'phyStatus',
      minWidth: 120,
    },
    {
      title: '在线状态',
      key: '',
      slot: 'online',
      width: 100,
      renderHeader: (h) => {
        return h('div', [
          h('span', '在线状态'),
          h(
            'Tooltip',
            {
              props: {
                transfer: true,
                placement: 'bottom',
              },
              style: {
                verticalAlign: 'middle',
              },
            },
            [
              h('i', {
                class: 'icon-font icon-wenhao ml-xs f-12',
                style: {
                  width: '13px',
                  verticalAlign: 'top',
                  color: 'var(--color-warning)',
                },
              }),
              h(
                'span',
                {
                  slot: 'content',
                },
                '设备在国标平台为在线状态',
              ),
            ],
          ),
        ]);
      },
    },
    {
      title: '完好状态',
      key: 'normal',
      slot: 'normal',
      width: 100,
      renderHeader: (h) => {
        return h('div', [
          h('span', '完好状态'),
          h(
            'Tooltip',
            {
              props: {
                transfer: true,
                placement: 'bottom',
              },
              style: {
                verticalAlign: 'middle',
              },
            },
            [
              h('i', {
                class: 'icon-font icon-wenhao ml-xs f-12',
                style: {
                  width: '13px',
                  verticalAlign: 'top',
                  color: 'var(--color-warning)',
                },
              }),
              h(
                'span',
                {
                  slot: 'content',
                },
                '设备在线，拉流请求有响应',
              ),
            ],
          ),
        ]);
      },
    },
    {
      title: '可用状态',
      key: 'canPlay',
      slot: 'canPlay',
      width: 100,
      renderHeader: (h) => {
        return h('div', [
          h('span', '可用状态'),
          h(
            'Tooltip',
            {
              props: {
                transfer: true,
                placement: 'bottom',
              },
              style: {
                verticalAlign: 'middle',
              },
            },
            [
              h('i', {
                class: 'icon-font icon-wenhao ml-xs f-12',
                style: {
                  width: '13px',
                  verticalAlign: 'top',
                  color: 'var(--color-warning)',
                },
              }),
              h(
                'span',
                {
                  slot: 'content',
                },
                '成功接收到实时视频流',
              ),
            ],
          ),
        ]);
      },
    },
    {
      title: '检测结果',
      key: 'qualified',
      slot: 'qualified',
      width: 120,
      renderHeader: (h) => {
        return h('div', [
          h('span', '检测结果'),
          h(
            'Tooltip',
            {
              props: {
                transfer: true,
                placement: 'bottom',
              },
              style: {
                verticalAlign: 'middle',
              },
            },
            [
              h('i', {
                class: 'icon-font icon-wenhao ml-xs f-12',
                style: {
                  width: '13px',
                  verticalAlign: 'top',
                  color: 'var(--color-warning)',
                },
              }),
              h(
                'span',
                {
                  slot: 'content',
                  style: {
                    whiteSpace: 'normal',
                    wordBreak: 'normal',
                    maxWidth: '300px',
                  },
                },
                `设备的【${indexDetectionModeMap[columnParams.statisticalList.indexDetectionMode]}】作为指标计算结果`,
              ),
            ],
          ),
        ]);
      },
    },
    {
      title: '原因',
      key: 'errorCodeName',
      minWidth: 120,
      tooltip: true,
    },
    {
      title: '备注',
      key: 'reason',
      minWidth: 120,
      tooltip: true,
    },
    {
      title: '信令时延(毫秒)',
      slot: 'delaySipMillSecond',
      width: 130,
    },
    {
      title: '视频流时延(毫秒)',
      slot: 'delayStreamMillSecond',
      width: 130,
    },
    {
      title: '关键帧时延(毫秒)',
      slot: 'delayIdrMillSecond',
      width: 130,
    },
    {
      title: '检测时间',
      slot: 'videoStartTime',
      width: 160,
    },
    {
      minWidth: 150,
      title: '设备标签',
      slot: 'tagNames',
    },
    {
      title: '操作',
      slot: 'option',
      align: 'center',
      tooltip: true,
      minWidth: 150,
      fixed: 'right',
      className: 'table-action-padding', // 操作栏列-单元格padding设置
    },
  ];
};
