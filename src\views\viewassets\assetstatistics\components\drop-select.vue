<template>
  <div class="index-container">
    <Dropdown trigger="click" placement="bottom-end" @on-click="onClickDropdown">
      <i class="base-text-color mr-xs" v-if="!multiple">{{ getLabel() }}</i>
      <i class="base-text-color mr-xs" v-else>{{ checkAll ? '全部' : '' }}</i>
      <i class="icon-font icon-shaixuan f-16 color-filter"></i>
      <DropdownMenu slot="list">
        <Checkbox
          v-show="multiple"
          class="ml-20"
          :indeterminate="indeterminate"
          v-model="checkAll"
          @on-change="handleCheckAll"
          >全部</Checkbox
        >
        <template v-if="multiple">
          <CheckboxGroup
            class="ml-20"
            v-model="selected"
            @on-change="checkAllGroupChange"
            v-for="(item, index) in data"
            :key="`multiple${index}`"
          >
            <Checkbox :title="item.label" :label="item.id" class="block ellipsis dis-select">
              {{ item.label }}</Checkbox
            >
          </CheckboxGroup>
        </template>
        <div
          v-for="(item, index) in data"
          :key="`single ${index}`"
          :class="selected === item.id ? 'link-text-active' : ''"
          class="ellipsis dropdown-item f-14 base-text-color"
          @click="onClickDropdown(item.id)"
          v-show="!multiple"
        >
          <span class="dis-select ml-20" :title="item.label">{{ item.label }}</span>
        </div>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'index-select',
  components: {},
  props: {
    data: {
      required: true,
      default: () => [],
    },
    value: {},
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selected: '',

      indeterminate: false,
      checkAll: false,
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.selected = val;
      },
    },
  },
  filter: {},
  created() {},
  methods: {
    onClickDropdown(val) {
      let data = this.getDataById(this.selected);
      this.selected = val;
      this.$emit('input', val);
      this.$emit('on-change', val, data);
    },
    setCheckedKeys(list) {
      Object.keys(this.data).map((key) => {
        list.map((value) => {
          this.data[key].map((item) => {
            if (value === item.indexType) {
              item.check = true;
            }
          });
        });
      });
    },
    getLabel() {
      if (!this.multiple) {
        let data = this.getDataById(this.selected);
        return data[0]['label'];
      }
    },
    getDataById(id) {
      return this.data.filter((item) => item.id === id);
    },

    handleCheckAll(val) {
      if (val) {
        this.selected = this.data.map((item) => item.id);
      } else {
        this.selected = [];
      }
      this.$emit('input', this.selected);
      this.$emit('on-change', this.selected);
      this.indeterminate = false;
    },

    checkAllGroupChange(data) {
      this.$emit('input', data);
      this.$emit('on-change', data);

      if (data.length === this.data.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.color-filter {
  color: var(--color-filter-funnel);
  /*  &:hover {
    color: #539aea;
  }*/
}
.ml-20 {
  margin-left: 20px;
}
.dropdown-item {
  max-width: 200px;
  cursor: pointer;
  height: 26px;
  line-height: 26px;
  background: var(--bg-select-dropdown);
}
.ivu-dropdown-menu {
  display: flex;
  flex-direction: column;
}
.link-text-active {
  background: var(--bg-select-item-active);
  color: var(--color-select-item-active);
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.index-container {
  display: inline-block;
  .dropdown {
    width: 300px;
    height: 300px;
    overflow-y: auto;
  }
}
.ivu-select-dropdown {
  right: 0px !important;
}
.ivu-checkbox-wrapper {
  height: 25px;
  line-height: 25px;
}
</style>
