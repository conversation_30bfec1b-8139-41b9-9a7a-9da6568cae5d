<template>
  <div
    class="big-screen-layout"
    :class="theme === 'light' ? 'light-theme' : 'dark-theme'"
  >
    <div class="data-warehouse-overview">
      <div class="data-warehouse-left">
        <component
          :is="themeComponent"
          title="事件统计分析"
          :padding="0"
          class="first-card resource-classification relationship-map m-b10"
        >
          <ul class="dataShow">
            <li class="dataList" v-for="(item, index) in dataList" :key="index">
              <div class="data-msg" v-if="!item.type">
                <img :src="item.imgUrl" alt="" />
                <p class="data-total">
                  <count-to
                    :start-val="0"
                    :end-val="item.num"
                    :duration="1000"
                    class="h1"
                  ></count-to>
                </p>
                <p class="data-name">
                  {{ item.name }}
                </p>
              </div>
              <div v-else class="line"></div>
            </li>
          </ul>
        </component>
        <component
          :is="themeComponent"
          title="疲劳驾驶首次预警"
          :padding="0"
          class="second-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <div class="card-content-box">
            <div v-if="firstList.length !== 0">
              <div class="my-swiper-container" id="mySwiper">
                <swiper
                  ref="mySwiper"
                  :options="swiperOption"
                  class="my-swiper"
                >
                  <template v-for="(item, index) in firstList">
                    <swiper-slide :key="index">
                      <div class="swiper-item">
                        <div class="angle">
                          <div></div>
                          <div></div>
                          <div></div>
                          <div></div>
                        </div>
                        <listCard
                          class="listCard"
                          :itemInfo="item"
                          :theme="theme"
                        />
                      </div>
                    </swiper-slide>
                  </template>
                </swiper>
                <div
                  class="swiper-button-prev snap-prev-record"
                  slot="button-prev"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
                <div
                  class="swiper-button-next snap-next-record"
                  slot="button-next"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
              </div>
            </div>
            <ui-loading v-if="loading" />
            <ui-empty v-if="firstList.length === 0"></ui-empty>
          </div>
        </component>
        <component
          :is="themeComponent"
          title="疲劳驾驶首次预警分析"
          :padding="0"
          class="second-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <ul class="fight_ul">
            <li class="flexLi" v-for="(item, index) in firstList" :key="index">
              <div class="left">
                {{ item.name }}
              </div>
              <div>{{ item.idNumber }}</div>
              <plateNumber
                class="plateNo"
                :plateNo="item.plateNo"
                :color="item.plateColor"
                size="mini"
              ></plateNumber>
              <div class="right">
                {{ item.firstAlarmTime }}
              </div>
            </li>
            <ui-loading v-if="loading" />
            <ui-empty v-if="firstList.length === 0"></ui-empty>
          </ul>
        </component>
      </div>
      <div class="data-warehouse-main">
        <div class="main">
          <div class="map">
            <mapBase ref="map" :siteList="siteList" @inited="mapInited" />
          </div>
          <component
            :is="themeComponent"
            title="疲劳驾驶年龄段"
            :padding="0"
            class="age"
          >
            <div class="age-content">
              <div class="center">
                <count-to
                  :start-val="0"
                  :end-val="ageTotal"
                  :duration="1000"
                ></count-to>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageObj['0-20']"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">0-20</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageObj['21-25']"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">21-25</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageObj['26-30']"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">26-30</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageObj['31-35']"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">31-35</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageObj['36-40']"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">36-40</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageObj['41及以上']"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">41及以上</span>
              </div>
            </div>
          </component>
        </div>
      </div>
      <div class="data-warehouse-left">
        <component
          :is="themeComponent"
          title="疲劳驾驶统计"
          :padding="0"
          class="first-card resource-classification relationship-map m-b10"
        >
          <div class="line-echart">
            <LineEchart
              ref="lineEchartRef"
              :title="dataAccess.title"
              :legend="dataAccess.legend"
              :grid="dataAccess.grid"
              :xAxis="dataAccess.xAxis"
              :yAxis="dataAccess.yAxis"
              :series="dataAccess.series"
              :theme="theme"
            />
          </div>
        </component>
        <component
          :is="themeComponent"
          title="连续疲劳驾驶预警"
          :padding="0"
          class="second-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <div class="card-content-box">
            <div v-if="secendList.length !== 0">
              <div class="my-swiper-container" id="mySwiper2">
                <swiper
                  ref="mySwiper2"
                  :options="swiperOption"
                  class="my-swiper"
                >
                  <template v-for="(item, index) in secendList">
                    <swiper-slide :key="index">
                      <div class="swiper-item">
                        <div class="angle">
                          <div></div>
                          <div></div>
                          <div></div>
                          <div></div>
                        </div>
                        <listCard
                          class="listCard"
                          :itemInfo="item"
                          :theme="theme"
                        />
                      </div>
                    </swiper-slide>
                  </template>
                </swiper>
                <div
                  class="swiper-button-prev snap-prev-record"
                  slot="button-prev"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
                <div
                  class="swiper-button-next snap-next-record"
                  slot="button-next"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
              </div>
            </div>
            <ui-loading v-if="loading" />
            <ui-empty v-if="secendList.length === 0"></ui-empty>
          </div>
        </component>
        <component
          :is="themeComponent"
          title="连续疲劳驾驶预警分析"
          :padding="0"
          class="second-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <ul class="fight_ul">
            <li class="flexLi" v-for="(item, index) in secendList" :key="index">
              <div class="left">
                {{ item.name }}
              </div>
              <div>{{ item.idNumber }}</div>
              <plateNumber
                class="plateNo"
                :plateNo="item.plateNo"
                :color="item.plateColor"
                size="mini"
              ></plateNumber>
              <div class="right">
                {{ item.firstAlarmTime }}
              </div>
            </li>
            <ui-loading v-if="loading" />
            <ui-empty v-if="secendList.length === 0"></ui-empty>
          </ul>
        </component>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters } from "vuex";
import * as echarts from "echarts";
import CountTo from "vue-count-to";
import RadarEchart from "@/components/echarts/radar-echart";
import PieEchart from "@/components/echarts/pie-echart";
import LineEchart from "@/components/echarts/line-echart";
import listCard from "@/views/fatigue-driving-list/components/list-card.vue";
import {
  fatigueDrivingList,
  fdNotSleepEnoughAlarmAgeList,
  alarmNumDayStat,
} from "@/api/monographic/fatigue-driving";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import plateNumber from "@/components/ui-vehicle/index.vue";
import mapBase from "./components/fatigue-driving-map.vue";
import card from "@/components/screen/srceen-card.vue";
import UiCard from "@/components/ui-card.vue";
export default {
  name: "perception-site",
  components: {
    RadarEchart,
    PieEchart,
    LineEchart,
    CountTo,
    listCard,
    swiper,
    swiperSlide,
    mapBase,
    plateNumber,
    // card,
    // UiCard,
    // card: window.systemTheme === "light" ? UiCard : card,
  },
  data() {
    return {
      dataList: [
        {
          imgUrl: require("@/assets/img/fatigue-driving/vidicon.png"),
          num: 0,
          name: "主副驾抓拍设备",
        },
        { type: "line" },
        {
          imgUrl: require("@/assets/img/fatigue-driving/fatigue-driving.png"),
          num: 245,
          name: "疲劳驾驶首次预警",
        },
        { type: "line" },
        {
          imgUrl: require("@/assets/img/fatigue-driving/fatigue-driving2.png"),
          num: 5234,
          name: "连续疲劳驾驶预警",
        },
      ],
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 1.85,
        centeredSlides: true,
        initialSlide: 2,
        speed: 1000,
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 50, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 90, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".snap-next-record",
          prevEl: ".snap-prev-record",
        },
        observer: true,
        observeParents: true,
      },
      dataAccess: {
        title: {
          show: false,
        },
        grid: {
          left: "0",
          top: "20%",
          right: "0.1%",
          bottom: "10%",
          containLabel: true,
        },
        legend: {
          type: "scroll",
          data: ["高发段首次发现", "高发段连续驾驶", "全天连续驾驶"],
          top: "-3%",
          itemGap: 24,
          itemWidth: 12,
          itemHeight: 2,
          icon: "rect",
          textStyle: {
            color: "inherit",
            padding: [0, 0, 0, 3],
          },
        },
        xAxis: {
          type: "category",
          data: [
            "11-21",
            "11-22",
            "11-23",
            "11-24",
            "11-25",
            "11-26",
            "11-27",
            "11-28",
          ],
          boundaryGap: true,
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              type: "dashed",
            },
          },
        },
        series: [
          {
            type: "line",
            name: "高发段首次发现",
            data: [30, 35, 38, 39, 50, 61, 50, 75],
            symbolSize: 0,
            smooth: true,
            color: "#2C86F8",
            lineStyle: {
              width: 2,
            },
            areaStyle: {
              //区域填充样式
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(44, 134, 248, 0.6)",
                  },
                  {
                    offset: 1,
                    color: "rgba(91, 163, 255, 0)",
                  },
                ],
                false
              ),
            },
          },
          {
            type: "line",
            name: "高发段连续驾驶",
            data: [50, 35, 38, 39, 50, 61, 100, 75],
            symbolSize: 0,
            smooth: true,
            color: "#4DC7D4",
            lineStyle: {
              width: 2,
            },
            areaStyle: {
              //区域填充样式
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(44, 134, 248, 0.6)",
                  },
                  {
                    offset: 1,
                    color: "rgba(91, 163, 255, 0)",
                  },
                ],
                false
              ),
            },
          },
          {
            type: "line",
            name: "全天连续驾驶",
            data: [80, 35, 38, 89, 50, 61, 70, 100],
            symbolSize: 0,
            smooth: true,
            color: "#F1BA4D",
            lineStyle: {
              width: 2,
            },
            areaStyle: {
              //区域填充样式
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(241, 186, 77, 0.6)",
                  },
                  {
                    offset: 1,
                    color: "rgba(241, 186, 77, 0)",
                  },
                ],
                false
              ),
            },
          },
        ],
      },
      loading: false,
      firstList: [],
      secendList: [],
      ageObj: {},
      ageTotal: 0,
      siteList: [],
      inited: false,
      timer: null,
      lastAlarmTime: "",
      themeComponent: UiCard,
    };
  },
  computed: {
    ...mapState("common", ["theme"]),
  },
  watch: {
    theme(val) {
      this.themeComponent = val === "light" ? UiCard : card;
      this.dataAccess.legend.textStyle.color =
        val === "light" ? "inherit" : "rgba(255, 255, 255, 0.6)";
      this.$refs.lineEchartRef.resizeEchart();
    },
  },
  created() {},
  mounted() {
    this.getDevice();
    this.getFirst(true);
    this.getSecend(true);
    this.getAgeList();
    this.getAlarmNumDayStat();
    this.timer = setInterval(() => {
      this.getFirst();
      this.getSecend();
    }, 60000);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    getDevice() {
      // 主副驾设备
      queryDeviceList({
        orgCodes: ["34032"],
        pageNumber: 1,
        pageSize: 9999,
      }).then((res) => {
        const { entities } = res.data;
        this.siteList = entities.map((v) => {
          v.longitude = v.geoPoint.lon;
          v.latitude = v.geoPoint.lat;
          return v;
        });
        this.$set(this.dataList[0], "num", this.siteList.length);
      });
    },
    getFirst(isFirst) {
      if (isFirst) this.loading = true;
      let params = {
        alarmCountType: 1,
      };
      fatigueDrivingList(params)
        .then((res) => {
          this.firstList = res.data.entities;
          this.$set(this.dataList[2], "num", res.data.total);
        })
        .finally(() => {
          this.loading = false;
          if (this.inited) this.addMapInfoWindow({ ...this.firstList[0] });
        });
    },
    getSecend(isFirst) {
      if (isFirst) this.loading = true;
      let params = {
        alarmCountType: 2,
      };
      fatigueDrivingList(params)
        .then((res) => {
          this.secendList = res.data.entities;
          this.$set(this.dataList[4], "num", res.data.total);
        })
        .finally(() => {
          this.loading = false;
          if (this.inited) this.addMapInfoWindow({ ...this.secendList[0] });
        });
    },
    getAgeList() {
      fdNotSleepEnoughAlarmAgeList({}).then((res) => {
        this.ageObj = res.data;
        this.ageTotal = Object.values(res.data).reduce(
          (sum, value) => sum + value,
          0
        );
      });
    },
    getAlarmNumDayStat() {
      alarmNumDayStat({}).then((res) => {
        this.dataAccess.xAxis.data = res.data.map((v) => v.day.slice(5));
        this.dataAccess.series[0].data = res.data.map(
          (v) => v.notSleepEnoughFirstAlarmNum
        );
        this.dataAccess.series[1].data = res.data.map(
          (v) => v.notSleepEnoughSecondAlarmNum
        );
        this.dataAccess.series[2].data = res.data.map(
          (v) => v.continuousDrivingAlarmNum
        );
      });
    },
    handleMore() {
      const path = this.$route.path;
      const noMenu = this.$route.query.noMenu;
      this.$router.push({
        path,
        query: { noMenu, sectionName: "firstContent" },
      });
    },
    mapInited() {
      this.inited = true;
      if (this.firstList.length)
        this.addMapInfoWindow({ ...this.firstList[0] });
    },
    addMapInfoWindow(item) {
      let alarmTime = item.secondAlarmTime || item.firstAlarmTime;
      if (!alarmTime) return;
      if (
        !this.lastAlarmTime ||
        new Date(alarmTime) > new Date(this.lastAlarmTime)
      ) {
        item.lon = item.cdAlarmVehicle
          ? item.cdAlarmVehicle.geoPoint.lon
          : item.firstFace.geoPoint.lon;
        item.lat = item.cdAlarmVehicle
          ? item.cdAlarmVehicle.geoPoint.lat
          : item.firstFace.geoPoint.lat;
        if (!item.lon || !item.lat) return;
        this.lastAlarmTime = alarmTime;
        this.$refs.map.thematicAlert(item, "fatigueDriving");
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "~@/views/fatigue-driving/style/theme.less";
.m-b10 {
  margin-bottom: 10px;
}

.big-screen-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.data-warehouse-overview {
  overflow: hidden;
  display: flex;
  flex: 1;
  position: relative;
  .data-warehouse-left {
    width: 500px;
    height: 100%;
    display: flex;
    flex-direction: column;

    /deep/.card {
      width: 100%;
      flex: unset;
      height: 38%;
      .card-head .title span {
        font-size: 18px;
        letter-spacing: 0;
      }
      .card-content {
        display: flex;
        height: calc(~"100% - 30px");
      }
    }
    .first-card,
    .fourth-card {
      height: 30%;
    }
    .second-card {
      height: 38%;
    }
    .dataShow {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 42px;
      justify-content: space-around;
      .dataList {
        display: flex;
        .data-msg {
          display: flex;
          flex-direction: column;
          align-items: center;
          img {
            width: 90px;
            height: 90px;
          }
          .data-total {
            font-size: 20px;
            font-weight: bold;
            color: #2ecbff;
            margin: 5px 0;
          }
          .data-name {
            font-size: 12px;
          }
        }
        .line {
          height: 60px;
          border-left: 1px solid #012a63;
        }
      }
    }
    .resource-classification {
      /deep/ .card-content {
        padding: 0 !important;
      }
    }

    .relationship-map {
      .relationship-map-head {
        display: flex;
        margin-right: 10px;
        cursor: pointer;
        align-items: center;
        .more {
          margin-right: 6px;
          font-size: 14px;
        }
        .total-entity,
        .relationship-entity {
          display: flex;
          align-items: center;
          margin-right: 40px;
          .name {
            font-size: 12px;
            line-height: 18px;
          }
          .number {
            font-size: 14px;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            margin-left: 4px;
            line-height: 20px;
          }
        }
        .relationship-entity {
          margin-right: 30px;
        }
      }
      /deep/ .card-content {
        //   padding: 0 !important;
        height: calc(~"100% - 70px");
        margin-top: 20px;
      }
    }
    .fight_ul {
      overflow-y: auto;
      width: 100%;
      padding: 0 20px;
      padding-bottom: 10px;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      height: 100%;
      position: relative;
      .iconfont {
        line-height: normal;
      }
      .flexLi {
        color: #fff;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(
          90deg,
          rgba(6, 55, 131, 0) 0%,
          rgba(6, 55, 131, 0.5) 51%,
          rgba(6, 55, 131, 0) 100%
        );
        border: 1px solid;
        border-image: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0),
            rgba(121, 173, 255, 0.5),
            rgba(121, 173, 255, 0)
          )
          1 1;
        padding: 7px;
        margin-bottom: 7px;
        .left {
          width: 42px;
          font-weight: bold;
          font-size: 14px;
          /* cursor: pointer; */
          .iconfont {
            margin-right: 6px;
          }
        }
        /* .right {
						font-size: 14px;
					} */
      }
    }
    .my-swiper-container {
      padding: 0 30px;
      position: relative;
      .my-swiper {
        margin: auto;
        padding: 15px 0;
        .swiper-item {
          width: 100%;
          height: 210px;
          box-sizing: border-box;
          overflow: hidden;
          .angle {
            div {
              width: 10px;
              height: 10px;
              background: url("~@/assets/img/screen/angle.png") no-repeat;
              z-index: 1;
              &:nth-child(1) {
                position: absolute;
                top: 0;
                left: 0;
                background-position: 0 0;
              }
              &:nth-child(2) {
                position: absolute;
                top: 0;
                right: 0;
                background-position: -11px 0px;
              }
              &:nth-child(3) {
                position: absolute;
                bottom: 0;
                left: 0;
                background-position: 0 -11px;
              }
              &:nth-child(4) {
                position: absolute;
                bottom: 0;
                right: 0;
                background-position: -11px -11px;
              }
            }
          }
        }
      }
      /* /deep/ .swiper-container-3d {
                    .swiper-slide-shadow-left {
                    background-image: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                    }
                    .swiper-slide-shadow-right {
                    background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                    }
                } */
      .swiper-button-prev,
      .swiper-button-next {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        margin-top: -15px;
        .iconfont {
          font-size: 18px;
        }
      }
      .swiper-button-prev {
        transform: rotate(180deg);
        left: 12px;
      }
      .swiper-button-next {
        right: 12px;
      }
    }
    .line-echart {
      height: 100%;
      width: 100%;
      padding: 0 10px;
    }
  }
  .data-warehouse-main {
    flex: 1;
    position: relative;
    height: 100%;
    .main {
      height: 100%;
      display: flex;
      flex-direction: column;
      .map {
        flex: 1;
        height: 100%;
        width: 100%;
        position: relative;
        padding: 0 10px;
      }
      .age {
        margin: 0 10px 10px 10px;
        width: calc(~"100% - 20px");
        .age-content {
          position: relative;
          height: 250px;
          background: url("~@/assets/img/thematic/bg.png") no-repeat 0 0;
          background-size: cover;
          .center {
            padding-top: 54px;
            color: #fff;
            font-size: 20px;
            text-align: center;
            width: 300px;
            height: 200px;
            background: url("~@/assets/img/thematic/center.png") no-repeat 0 0;
            background-size: cover;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
          .item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100px;
            height: 100px;
            background: url("~@/assets/img/thematic/item-bg.png") no-repeat 0 0;
            background-size: cover;
            .num {
              color: #2ecbff;
              font-size: 20px;
            }
            .label {
              font-size: 14px;
            }
            &:nth-of-type(2) {
              position: absolute;
              left: 5%;
              top: 20%;
            }
            &:nth-of-type(3) {
              position: absolute;
              left: 20%;
              top: 0;
            }
            &:nth-of-type(4) {
              position: absolute;
              left: 15%;
              bottom: 7%;
            }
            &:nth-of-type(5) {
              position: absolute;
              right: 15%;
              bottom: 7%;
            }
            &:nth-of-type(6) {
              position: absolute;
              right: 20%;
              top: 0;
            }
            &:last-child {
              position: absolute;
              right: 5%;
              top: 20%;
            }
          }
        }
      }
    }
  }
}
.relationship-map-head {
  padding-right: 10px;
  display: flex;
  color: rgba(0, 0, 0, 0.35);
  cursor: pointer;
  align-items: center;
  .more {
    margin-right: 6px;
    font-size: 14px;
  }
}
</style>
