<template>
  <div class="people-archive-container">
    <div class="content">
      <!-- 基础信息 -->
      <BasicInformation :labelType="1" type="device" v-if="baseInfo" :baseInfo="baseInfo" />
      <div class="main-information">
        <ui-card :title="item.title" class="owner-information m-b10" :id="item.id" v-for="(item, index) in attrList" :key="index">
          <div class="owner-information-content card-border-color">
            <div class="owner-item" v-for="(e, $index) in item.list" :key="$index">
              <div class="owner-label card-bg title-color card-border-color">{{ e.value }}</div>
              <!-- <div class="owner-value text-color card-border-color" v-if="['latitude', 'longitude'].includes(e.key)">
                {{ info[e.key] | filterLngLat }}
              </div> -->
              <div v-if="e.key === 'civilCode'" class="owner-value text-color card-border-color">
                {{ info[e.key] }}<span class="ml-10">({{ info.civilName }})</span>
              </div>
              <div v-else-if="e.key === 'sbdwlx'" class="owner-value text-color card-border-color">
                {{ info.sbdwlx | commonFiltering(sbdwlxList) }}
              </div>
              <div v-else-if="e.key === 'sbgnlx'" class="owner-value text-color card-border-color">
                {{ info.sbgnlx | commonFiltering(sbgnlxList) }}
              </div>
              <div v-else-if="e.key === 'manufacturer'" class="owner-value text-color card-border-color">
                {{ info.manufacturer | commonFiltering(manufacturerList) }}
              </div>
              <div v-else-if="e.key === 'sblwzt'" class="owner-value text-color card-border-color">
                {{ info.sblwzt | commonFiltering(sblwztList) }}
              </div>
              <div v-else-if="e.key === 'isonline'" class="owner-value text-color card-border-color">
                {{ info.isonline | commonFiltering(isonlineList) }}
              </div>
              <div v-else-if="e.key === 'longitude'" class="owner-value text-color card-border-color">
                {{ info.geoPoint.lon }}
              </div>
              <div v-else-if="e.key === 'latitude'" class="owner-value text-color card-border-color">
                {{ info.geoPoint.lat  }}
              </div>
              <div v-else class="owner-value text-color card-border-color">{{ info[e.key] }}</div>
            </div>
          </div>
        </ui-card>
        <ui-loading v-if="loading" />
      </div>
      <div class="anchor-point-infomation">
        <!-- 锚点 -->
        <UiAnchor :anchorLinkList="anchorLinkList" />
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import BasicInformation from '@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information'
import UiAnchor from '@/components/ui-anchor'
import { getDeviceArchivesInfo } from '@/api/device'
export default {
  components: {
    BasicInformation,
    UiAnchor
  },
  props: {
    // 基础信息
    baseInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {
      loading: '',
      info: {}, //设备资料
      anchorLinkList: [
        { href: '#basic-properties', title: '基础属性' },
        { href: '#state-properties', title: '状态属性' },
        { href: '#manage-properties', title: '管理属性' },
        { href: '#lable-properties', title: '标签属性' },
        { href: '#safe-properties', title: '安全属性' }
      ],
      attrList: [
        {
          id: 'basic-properties',
          title: '基 础 属 性',
          size: '20',
          name: 'base'
        },
        {
          id: 'state-properties',
          title: '状 态 属 性',
          size: '20',
          name: 'staus'
        },

        {
          id: 'manage-properties',
          title: '管 理 属 性',
          size: '16.5',
          name: 'manage'
        },
        {
          id: 'lable-properties',
          title: '标 签 属 性',
          size: '17',
          name: 'label'
        },
        {
          id: 'safe-properties',
          title: '安 全 属 性',
          size: '20',
          name: 'safe'
        }
      ]
    }
  },
  computed: {
    ...mapGetters({
      sbdwlxList: 'dictionary/getSbdwlxList', // 摄像机点位类型
      sbgnlxList: 'dictionary/getSbgnlxList', //摄像机功能类型
      manufacturerList: 'dictionary/getManufacturerList', //厂商
      sblwztList: 'dictionary/getSblwztList', //联网属性
      isonlineList: 'dictionary/getIsonlineList' //设备状态
    })
  },
  watch: {},
  filters: {
    // filterLngLat(value) {
    //   let str = ''
    //   str = value ? String(value).replace(/^(.*\..{8}).*$/, '$1') : ''
    //   return str
    // }
  },
  created() {
    this.getDictData()
    this.getDeviceArchivesInfo()
  },
  methods: {
    ...mapActions({
      getDictData: 'dictionary/getDictAllData'
    }),
    // 获取设备属性
    getDeviceArchivesInfo() {
      this.loading = true
      let data = {
        deviceId: this.$route.query.archiveNo
      }
      getDeviceArchivesInfo(data)
        .then(res => {
          this.info = res.data
          const { deviceColumnJson } = res.data
          const { base = [], staus = [], manage = [], label = [], safe = [] } = JSON.parse(deviceColumnJson)
          let attributeMap = { base, staus, manage, label, safe }
          this.attrList.forEach(e => {
            e.list = attributeMap[e.name] || []
          })
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}
/deep/ .ui-card {
  overflow: unset !important;
  .card-head {
    overflow: hidden;
    border-top-left-radius: 4px;
  }
}
.people-archive-container {
  padding: 16px 10px 0 10px;
  display: flex;
  flex: 1;
  overflow: hidden;
  .content {
    display: flex;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    flex: 1;
    .main-information {
      width: 1442px;
      height: min-content;
      padding: 0 10px;
      margin-left: 350px;
      .owner-information-content {
        display: flex;
        flex-wrap: wrap;
        border-left: 1px solid #fff;
        border-top: 1px solid #fff;
        .owner-item {
          display: flex;
          justify-content: flex-start;
          .owner-label {
            width: 180px;
            padding: 9px 10px;
            box-sizing: border-box;
            font-size: 14px;
            font-weight: bold;
            line-height: 20px;
            font-family: 'MicrosoftYaHei-Bold';
            border-right: 1px solid #fff;
            border-bottom: 1px solid #fff;
          }
          .owner-value {
            width: 280px;
            padding: 9px 10px;
            box-sizing: border-box;
            font-size: 14px;
            line-height: 20px;
            border-right: 1px solid #fff;
            border-bottom: 1px solid #fff;
          }
          .address {
            width: 750px;
          }
        }
        .owner-border-bottom {
          .owner-label,
          .owner-value {
            border-bottom: 1px solid #fff;
          }
        }
      }
    }
    .anchor-point-infomation {
      width: 100px;
      position: fixed;
      top: 78px;
      right: 18px;
      z-index: 9;
      .export-btn {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
