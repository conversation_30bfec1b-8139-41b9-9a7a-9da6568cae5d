<template>
  <div class="outside-wrapper">
    <div class="category-management height-full">
      <slide-unit-tree
        ref="slideUnitTree"
        :no-shadow="true"
        :defaultHide="false"
        :style="{ height: `100%` }"
        nodeKey="orgCode"
        :selectKey="selectKey"
        @selectOrgCode="selectOrgCode"
        :tree-data="treeData"
      />
      <div class="category-content">
        <Button type="primary" class="addCategoryButton" @click="addEditCategory('add')">
          <i class="icon-font icon-tianjia f-14"></i>
          <span class="inline vt-middle ml-xs">新增目录</span>
        </Button>
        <div class="category-box f-14">
          <category-tree
            ref="categoryTree"
            class="category-tree"
            :tree-data-object="selfCategoryList"
            :chooseOrgCode="chooseOrgCode"
            @updateTree="getAllCustomAreaTree"
            @changeSelfTreeNode="changeSelfTreeNode"
          />
        </div>
      </div>
      <device-table
        class="device-table"
        :table-data="tableData"
        :loading="deviceLoading"
        :total-count="totalCount"
        :active-catogory-object="activeCatogoryObject"
        @addRelativeDeviceShow="addRelativeDeviceShow"
        @removeInit="removeDeviceForCategory"
        @startSearch="startSearch"
      />
    </div>
    <ui-modal v-model="addEditCategoryShow" :title="addEditCategoryAction.title" :width="560" class="add-modal">
      <ui-label label="目录名称" class="content-title">
        <div class="inline">
          <Input class="width-lg" v-model="newCategoryName" placeholder="请输入目录名称"></Input>
        </div>
      </ui-label>
      <template slot="footer">
        <Button class="plr-30" plain type="default" @click="addEditCategoryShow = false"> 取 消 </Button>
        <Button class="plr-30" type="primary" :loading="confirmLoading" @click="confirmAddCategory">
          {{ addEditCategoryAction.action === 'add' ? '确 定' : '更 新' }}
        </Button>
      </template>
    </ui-modal>
    <add-device
      v-model="addDeviceShow"
      :modalAction="modalAction"
      :choosed-category="activeCatogoryObject"
      @addDeviceToCategory="addDeviceToCategory"
    />
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import category from '@/config/api/catalogmanagement';

export default {
  name: 'catalogmanagement',
  components: {
    DeviceTable: require('./modules/device-table.vue').default,
    CategoryTree: require('./modules/category-tree.vue').default,
    AddDevice: require('./modules/add-device.vue').default,
    SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
  },
  data() {
    return {
      newCategoryName: '',
      chooseOrgCode: '',
      confirmLoading: false,
      searchData: {
        directoryId: '',
        deviceId: '',
        deviceName: '',
        pageNumber: 1,
        pageSize: 20,
      },
      // 选中的目录
      activeCatogoryName: '',
      activeCatogoryObject: {},
      tableData: [],
      importantPlaceCategory: [],
      addEditCategoryShow: false,
      addEditCategoryAction: {},
      chooseTagType: null,
      importantChooseShow: false,
      selfCategoryList: [{ id: -1, name: '自定义目录' }],
      deviceLoading: false,
      addDeviceShow: false,
      modalAction: {
        title: '关联设备',
        action: 'add',
      },
      totalCount: 0,
      tagTypeList: [],
      newTreeData: [],
      selectKey: '',
    };
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  async activated() {
    this.selectKey = this.getDefaultSelectedOrg.orgCode;
  },
  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    handleOndragover(e) {
      e.preventDefault();
    },
    handleOndrop(e) {
      e.preventDefault();
    },
    selectOrgCode(data) {
      this.activeCatogoryObject = {};
      this.chooseOrgCode = data.orgCode;
      this.newTreeData = [data];
      this.tableData = [];
      this.searchData.pageNumber = 1;
      this.totalCount = 0;
      if (this.$route.name === 'catalogmanagement') {
        this.getAllCustomAreaTree();
      }
    },
    // 新增 修改 目录
    confirmAddCategory() {
      if (!this.newCategoryName) {
        this.$Message.warning('请输入目录名称');
        return false;
      }
      let params = {
        directoryName: this.newCategoryName,
        orgCode: this.chooseOrgCode,
      };
      let interfaces = 'deviceDirectoryAdd';
      let requestWay = 'post';
      if (this.addEditCategoryAction.action === 'edit') {
        params.id = this.addEditCategoryAction.id;
        interfaces = 'deviceDirectoryUpdate';
        requestWay = 'put';
      }
      this.confirmLoading = true;
      this.$http[requestWay](category[interfaces], params)
        .then((res) => {
          let { data } = res;
          this.$Message.success(data.msg);
          this.getAllCustomAreaTree();
        })
        .finally(() => {
          this.addEditCategoryShow = false;
          this.confirmLoading = false;
        });
    },
    addEditCategory(type, item) {
      if (type === 'add') {
        this.addEditCategoryAction = {
          title: '新增目录类型',
          action: 'add',
        };
      } else {
        this.addEditCategoryAction = {
          title: '修改目录',
          action: 'edit',
          id: item.id,
        };
      }
      if (item) this.newCategoryName = item.directoryName;
      this.addEditCategoryShow = true;
    },
    // 添加设备
    addDeviceToCategory(devices, allDeviceFlag, orgCode) {
      this.addCustomAreaTreeDevice(devices, allDeviceFlag, orgCode);
    },
    startSearch(searchData) {
      Object.assign(this.searchData, searchData);
      // 组织机构目录
      this.getCustomAreaTreeDevicePage();
    },
    // 移除相关目录设备
    async removeDeviceForCategory(devices) {
      let params = { ids: devices };
      params.id = this.activeCatogoryObject.id;
      try {
        let res = await this.$http.delete(category.dirDeviceRemove + devices);
        this.$Message.success(res.data.msg);
        // 组织机构目录
        this.getCustomAreaTreeDevicePage();
      } catch (err) {
        console.error(err);
      }
    },
    /*---------------------------自定义目录树------------------------------*/
    // 查询所有自定义目录列表
    async getAllCustomAreaTree() {
      try {
        let { data } = await this.$http.get(category.deviceDirectoryTree, {
          params: { orgCode: this.chooseOrgCode },
        });
        this.selfCategoryList = data.data;
        this.selfCategoryList.forEach((item) => {
          this.$set(item, 'inputShow', false);
          this.$set(item, 'inputValue', '');
        });
        if (this.selfCategoryList.length > 0) {
          const currentData = this.selfCategoryList[0];
          this.changeSelfTreeNode(currentData);
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
    // 自定义目录节点点击
    changeSelfTreeNode(data) {
      this.$refs.categoryTree.setCurrentKeyHandle(data.id);
      this.activeCatogoryName = '';
      this.searchData.pageNumber = 1;
      this.activeCatogoryObject = data;
      this.getCustomAreaTreeDevicePage();
    },
    // 新增自定义目录树节点
    async handleInput(item) {
      try {
        const params = {
          id: item.id,
          directoryName: item.inputValue,
          orgCode: this.chooseOrgCode,
          parentId: item.id,
        };
        let { data } = await this.$http.post(category.deviceDirectoryAdd, params);
        this.$Message.success(data.msg);
        this.getAllCustomAreaTree();
      } catch (err) {
        console.log(err);
      }
    },
    // 获取自定义目录下的设备
    async getCustomAreaTreeDevicePage() {
      try {
        this.deviceLoading = true;
        delete this.searchData.orgCode;
        let params = Object.assign({}, this.searchData);
        params.directoryId = this.activeCatogoryObject.id;
        let { data } = await this.$http.post(category.dirDevicePageList, params);
        this.tableData = data.data.entities;
        this.totalCount = data.data.total;
      } catch (err) {
        console.error(err);
      } finally {
        this.deviceLoading = false;
      }
    },
    // 删除自定义目录
    deleteCategory(item) {
      this.$UiConfirm({
        content: `您将删除${item.directoryName}目录，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$http.delete(category.deviceDirectoryDelete + item.id).then((res) => {
            this.getAllCustomAreaTree();
            this.$Message.success(res.data.msg);
          });
        })
        .catch((res) => {
          console.error(res);
        });
    },
    // 关联设备
    addRelativeDeviceShow() {
      if (Object.keys(this.activeCatogoryObject).length) {
        this.addDeviceShow = true;
      } else {
        this.$Message.warning('请先选择目录再关联设备');
      }
    },
    // 关联设备到自定义目录下
    async addCustomAreaTreeDevice(devices, allDeviceFlag, orgCode) {
      try {
        let ids = devices.map((item) => {
          return item.id;
        });
        const params = {
          directoryId: this.activeCatogoryObject.id,
          deviceInfoIds: ids,
          checkAll: allDeviceFlag,
          orgCode: orgCode,
        };
        this.loading = true;
        let { data } = await this.$http.post(category.dirDeviceLink, params);
        this.addDeviceShow = false;
        this.$Message.success(data.msg);
        this.getCustomAreaTreeDevicePage();
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.outside-wrapper {
  overflow: hidden;
  position: relative;
}
.slide-unit-tree,
@{_deep}.tree-div {
  position: relative !important;
}
.slide-unit-tree {
  border-right: 0.005208rem solid var(--border-color);
  @{_deep}.hide-btn {
    display: none !important;
  }
}
.category-management {
  background-color: var(--bg-content);
  display: flex;
  .self-tree-box {
    position: relative;
    .icon-box {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .content-ul {
    padding-top: 10px;
    .content-li {
      height: 25px;
      line-height: 25px;
      padding: 0 10px;
      border: 1px solid transparent;
      &:hover {
        background: @bg-darkblue-block;
      }
      &.active {
        border-color: #1a82be;
        background: transparent;
      }
      .icon-p {
        position: relative;
        .icon-shanchu {
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }
  }
  .addCategoryButton {
    width: 100%;
    margin-bottom: 10px;
  }
  .category-content {
    background-color: var(--bg-content);
    width: 320px;
    border-right: 0.005208rem solid var(--border-color);
    height: 100%;
    padding: 20px;
    .category-box {
      height: 100%;
      width: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      @{_deep}.ivu-collapse {
        border: 0;
      }
      @{_deep}.ivu-collapse-header {
        background-color: var(--bg-content);
        color: #fff;
        border: 0;
        padding-left: 0px;
      }
      @{_deep}.ivu-collapse-content {
        border: 0;
        background-color: var(--bg-content);
        padding: 0;
        width: 100%;
        color: #fff;
        overflow: initial !important;
        .ivu-collapse-content-box {
          padding-top: 0;
          padding-bottom: 10px;
        }
      }
      @{_deep}.ivu-collapse-item {
        border: 0;
      }
      @{_deep}.el-tree-node__content {
        border: 1px solid transparent;
        //background: #1b3b65;
      }
      @{_deep}.active-tree {
        .el-tree-node {
          &.is-current {
            > .el-tree-node__content {
              border-color: #1a82be;
              background: #1b3b65;
            }
          }
        }
      }
      .category-tree {
      }
    }
  }

  .device-table {
    // margin-left: 20px;
    height: 100%;
    padding: 20px;
    width: calc(100% - 610px);
  }
  .special-input {
    margin-left: 20px;
    @{_deep}.ivu-input {
      height: 25px;
      line-height: 25px;
      width: 253px;
      &&::-webkit-input-placeholder {
        font-size: 12px !important;
      }
    }
  }
}
.content-title {
  margin: 0 auto;
  width: 350px;
}
</style>
