<template>
  <div class="graph-box">
    <div style="height: 100%">
      <!-- 关系图谱 -->
      <AntVG6
        class="antv-g6"
        ref="antVG6Ref"
        :container="container"
        :graph-data="graphData"
        :layout-data="defaultLayoutData"
        @wheelzoom="wheelzoom"
        :relation-can-operate="true"
      />
    </div>
    <div class="play-btn mr-20 color-primary cursor-p" @click="linkToNumCube">
      数智立方
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { relationDetail_v2 } from "@/api/number-cube";
import AntVG6 from "@/components/antv-g6/index.vue";
import { entitySearchMixins } from "../mixins/getEntitySearch";
export default {
  props: {
    numCodeParams: {},
    hasData: {
      type: Boolean,
      default: false,
    },
    relationRes: {},
  },
  data() {
    return {
      container: "",
      data: {},
      graphData: {}, // 图谱数据
      mainNode: {},
      hasRelations: false, // 是否有关系，控制是否可以跳转数智立方
      defaultLayoutData: {
        type: "force2",
        linkDistance: 200,
        preventOverlap: true, // 可选，必须配合 nodeSize
        nodeSize: 30, // 可选
      },
      params: {},
    };
  },
  mixins: [entitySearchMixins],
  created() {
    this.container = `xjG6-${Math.random().toString(16).substr(2)}`;
  },
  mounted() {
    if (this.hasData) {
      const newData = this.assemblyDataByBackend(this.relationRes);
      this.graphData = newData;
    } else {
      this.getRelationMap();
    }
  },
  methods: {
    async getRelationMap() {
      try {
        const { type, picSerch, info } = this.numCodeParams;
        const algorithmTypeMap = {
          vid: "1",
          person: "1",
          vehicle: "2",
        };
        if (info) {
          const { relationNames, feature, keyword } = info;
          let data = {};
          if (picSerch) {
            // 照片检索
            data = {
              algorithmType: algorithmTypeMap[type],
              features: [feature],
              similarity: 0.8,
              maxDepth: 1,
            };
          } else {
            // 关键字检索
            data = {
              searchValue: keyword,
              maxDepth: 1,
            };
          }
          this.$refs.antVG6Ref.loadingShow({ visible: true });
          const entityData = await this.getEntityList(data);
          const entityIds = [];
          const entityIdInstanceIdMap = {};
          entityData.forEach((item) => {
            entityIds.push(item.id);
            entityIdInstanceIdMap[item.id] = item.graphInstanceId;
          });
          let param = {
            entityIds,
            entityIdInstanceIdMap,
            relationCnNames: relationNames,
            maxDepth: 1,
          };
          this.params = param;
          const relationRes = await relationDetail_v2(param);

          const newData = this.assemblyDataByBackend(relationRes);
          this.graphData = newData;

          this.$nextTick(() => {
            requestIdleCallback(() => {
              newData?.nodes?.length > 1 && this.$refs.antVG6Ref.fitView(10);
            });
          });
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
    },
    assemblyDataByBackend(res) {
      const entitys = res.data.entitys || [];
      const relations = res.data.relations || [];
      const nodes = entitys.map((row) => {
        return {
          id: row.entityId,
          label: row.displayField,
          img: row.propertyIcon || row.icon,
          group: row.label,
          groupCn: row.labelCn,
          ext: row,
        };
      });
      const edges = relations.map((row) => {
        const info = row.detail || row;
        return {
          source: row.sourceId,
          target: row.targetId,
          id: row.id,
          label: `${info.labelCn} ${row.items ? `${row.items}次` : ""}`,
          style: {
            stroke: info.color,
            ...JSON.parse(info.lineStyle || "{}"),
          },
          //   labelCfg: {
          //     style: {
          //       fill: "#2c86f8",
          //     },
          //   },
          ext: row.detail ? row : { detail: row },
        };
      });
      return {
        nodes,
        edges,
      };
    },
    wheelzoom(zoom) {
      if (!this.hasToolbar) return;
      this.$refs.toolbarRightRef.zoom = zoom.toFixed(1) * 100;
    },
    fitView() {
      this.$refs.antVG6Ref.fitView();
    },
    linkToNumCube() {
      const { entityIds, entityIdInstanceIdMap, relationNames } = this.params;
      let query = {
        ids: JSON.stringify(entityIds.join(",")),
        entityIdInstanceIdMap: JSON.stringify(entityIdInstanceIdMap),
        relationNames: JSON.stringify(relationNames),
        maxDepth: 1,
        type: "add",
      };
      this.$router.push({ name: "number-cube-info", query: query });
    },
  },
  computed: {
    ...mapGetters({
      xzLLMObj: "systemParam/getXzLLMObj",
    }),
  },
  components: {
    AntVG6,
  },
};
</script>
<style lang="less" scoped>
.graph-box {
  width: 20vw;
  height: 400px;

  .play-btn {
    position: absolute;
    font-size: 14px;
    top: 20px;
    right: 20px;
    z-index: 10;
  }
}
</style>
