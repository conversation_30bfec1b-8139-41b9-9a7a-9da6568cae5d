<template>
  <ui-modal v-model="visible" :title="modelTitle" :styles="styles" footer-hide @onCancel="onCancel">
    <div class="detail-content auto-fill">
      <component
        v-if="visible"
        :is="componentName"
        :table-columns="tableColumns"
        :form-item-data="formItems"
        :table-data="tableData"
        :loading="loading"
        :search-data="searchData"
        :row-data="rowData"
        @startSearch="startSearch"
        @sortChange="sortChange"
      >
        <Button slot="btnslot" type="default" class="button-export" @click="onExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white mr-xs f-14"></i>
          <span class="ml-xs">导出</span>
        </Button>
        <ui-page slot="page" :page-data="pageData" @changePage="handlePage" @changePageSize="handlePageSize"> </ui-page>
      </component>
    </div>
  </ui-modal>
</template>
<script>
import dataAnalysis from '@/config/api/dataAnalysis.js';
import { menuConfig } from '@/views/dataanalysis/utils/menuConfig.js';
import {
  formItemData,
  initFormData,
  getDetailsTableColumns,
} from '@/views/dataanalysis/analysis-modules/utils/tableConfig.js';

export default {
  name: 'details-modal',
  props: {
    value: {
      type: Boolean,
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.45rem',
      },
      modelTitle: '设备明细',
      componentName: null,
      tableColumns: [],
      tableData: [],
      formItems: [],
      searchData: {},
      loading: false,
      exportLoading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      sortData: {
        sortField: null, // 排序字段
        sort: null, // 排序方式: ASC("升序") | DESC("降序")
      },
    };
  },
  computed: {},
  methods: {
    initData() {
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      const { code } = this.$route.query;
      this.formItems = formItemData()[code];
      this.searchData = initFormData[code];
      // 活跃分析
      this.searchData.analyseResult = this.rowData.analyseResult || '';
      // 离线分析
      this.searchData.accOffline1 = this.rowData.accOffline1 || null;
      this.searchData.serialOffline1 = this.rowData.serialOffline1 || null;
      const menuItem = menuConfig.find((item) => item.factorCode.includes(code));
      this.tableColumns = getDetailsTableColumns({ dayNum: this.rowData?.detail?.staDayNum || 'x' })[code];
      this.componentName = menuItem.detailsComponent;
      this.sortData = {
        sortField: null,
        sort: null, // 默认设备编码升序
      };
      this.getAnalyseEnumResult();
      this.getTableList();
    },
    sortChange({ key, order }) {
      if (order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: key,
          sort: order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    startSearch(data) {
      this.pageData.pageNum = 1;
      this.searchData = data;
      this.getTableList();
    },
    getApiParams() {
      let { pageNum, pageSize } = this.pageData;
      let { batchId } = this.$route.query;
      let { civilCode, matchType } = this.rowData;
      let data = {
        batchId: batchId,
        orgRegionCode: civilCode,
        matchType: matchType,
        pageNumber: pageNum,
        pageSize: pageSize,
        customParameters: {
          ...this.searchData,
        },
        ...this.sortData,
      };
      return data;
    },
    async getTableList() {
      try {
        this.loading = true;
        let data = this.getApiParams();
        let res = await this.$http.post(dataAnalysis.getFirstModelData, data);
        this.tableData = res.data.data.entities || [];
        this.pageData.totalCount = res.data.data.total || 0;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    async onExport() {
      try {
        this.exportLoading = true;
        let data = this.getApiParams();
        const res = await this.$http.post(dataAnalysis.exportFirstModelData, data);
        await this.$util.common.transformBlob(res.data.data);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
    // 分析结果 列表
    async getAnalyseEnumResult() {
      try {
        const { batchId, code } = this.$route.query;
        let data = {
          batchId: batchId,
          orgRegionCode: this.rowData.civilCode,
        };
        const res = await this.$http.post(dataAnalysis.getAnalyseEnumResult, data);
        if (res.data.data && res.data.data.length > 0) {
          let arr = res.data.data.map((item) => {
            return { value: item.code, label: item.desc };
          });
          this.formItems = formItemData({ resultList: arr })[code];
        }
      } catch (error) {
        console.log(error);
      }
    },
    onCancel() {
      this.searchData = {};
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (!val) return false;
      this.visible = val;
      this.initData();
    },
  },
  components: {
    ActiveDetails: require('./active-details/index.vue').default,
    OfflineDetails: require('./offline-details/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.detail-content {
  width: 100%;
  height: 800px;
  .icon-daochu {
    color: var(--color-btn-default);
  }
}
</style>
