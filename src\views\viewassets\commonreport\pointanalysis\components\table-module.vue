<template>
  <ui-table
    class="ui-table auto-fill"
    :table-columns="tableColumns"
    :table-data="tableData"
    :loading="loading"
    @onSortChange="onSortChange"
  >
    <template #status="{ row }">
      <span class="check-status" :class="row.status === '1' ? 'bg-success' : row.status === '2' ? 'bg-failed' : ''">
        {{ row.status === '2' ? '不合格' : row.status === '1' ? '合格' : '--' }}
      </span>
    </template>
    <template #reportAndUnQualified="{ row }">
      <span class="font-report" @click="deviceDetails(row, '1', '2')">
        {{ row.reportAndUnQualified }}
      </span>
    </template>
    <template #reportAndQualified="{ row }">
      <span
        :class="['font-report', row.status === '2' ? 'font-report-error' : '']"
        @click="deviceDetails(row, '1', '1')"
      >
        {{ row.reportAndQualified }}
      </span>
    </template>
    <template #unReportAndQualified="{ row }">
      <span
        :class="['font-report', row.unReportAndQualified > 0 ? 'font-report-success' : '']"
        @click="deviceDetails(row, '0', '1')"
      >
        {{ row.unReportAndQualified }}
      </span>
    </template>
    <template #unQualifiedNum="{ row }">
      <span :class="['font-report', row.unQualifiedNum > 0 ? 'font-report-error' : '']" @click="areaDetails(row, '2')">
        {{ row.unQualifiedNum }}
      </span>
    </template>
    <template #action="{ row }">
      <div>
        <ui-btn-tip
          class="mr-md"
          :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
          icon="icon-shebeimingxi"
          content="设备明细"
          @handleClick="deviceDetails(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="mr-md"
          :styles="{ color: 'var(--color-primary)', 'font-size': '14px' }"
          icon="icon-piliangshangbao"
          content="一键上报"
          @handleClick="report(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          v-if="active === 'zoningReport' && hasChildArea(row)"
          :styles="{ color: '#269F26', 'font-size': '14px' }"
          icon="icon-chakanxiangqing"
          content="区县详情"
          @handleClick="areaDetails(row)"
        ></ui-btn-tip>
      </div>
    </template>
  </ui-table>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    active: {
      type: String,
    },
    searchData: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      tableData: [],
      tableColumns: [],
      tableBasicColumns: [
        {
          minWidth: 100,
          title: `达标数量`,
          key: 'configQualifiedNum',
        },
        {
          minWidth: 150,
          title: `已上报质量合格`,
          slot: 'reportAndQualified',
        },
        {
          minWidth: 150,
          title: `已上报质量不合格`,
          slot: 'reportAndUnQualified',
        },
        {
          minWidth: 150,
          title: `未上报质量合格`,
          slot: 'unReportAndQualified',
        },
      ],
      listApi: equipmentassets.qualityCivilCodeList,
      sortValue: {},
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(this.listApi, { ...this.searchData, ...this.sortValue });
        this.tableData = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    deviceDetails(row, cascadeReportStatus = '', qualityStatus = '') {
      this.$emit('showDetails', row, cascadeReportStatus, qualityStatus);
    },
    report(row) {
      this.$emit('report', row);
    },
    areaDetails(row, status = '') {
      this.$emit('showArea', row, status);
    },
    // 是否有下级区域
    hasChildArea(row) {
      return ['1', '2', '3'].includes(row.regionType);
    },
    onSortChange(obj) {
      let { order } = obj;
      let val = order.toUpperCase();
      if (val !== 'NORMAL') {
        this.sortValue = {
          sortField: 'civil_code',
          sort: val,
        };
      } else {
        this.sortValue = {};
      }
      this.init();
    },
  },
  watch: {
    active: {
      handler(val) {
        if (!val) return;
        this.tableColumns = [];
        this.tableData = [];
        let columns = [];
        let options = [];
        switch (val) {
          case 'zoningReport':
            columns = [
              {
                title: '序号',
                type: 'index',
                align: 'center',
                width: 50,
              },
              {
                minWidth: 100,
                key: 'name',
                title: '行政区划',
                sortable: 'custom',
              },
            ];
            options = [
              {
                minWidth: 100,
                title: `不合格区县数`,
                slot: 'unQualifiedNum',
              },
              {
                minWidth: 150,
                title: `达标情况`,
                slot: 'status',
              },
              {
                width: 100,
                title: '操作',
                slot: 'action',
              },
            ];
            this.listApi = equipmentassets.qualityCivilCodeList;
            break;
          case 'collectionArea':
            columns = [
              {
                title: '序号',
                type: 'index',
                align: 'center',
                width: 50,
              },
              {
                width: 100,
                title: '采集区域编码',
                key: 'code',
              },
              {
                minWidth: 700,
                title: '采集区域名称',
                key: 'name',
              },
            ];
            options = [
              {
                width: 150,
                title: `达标情况`,
                slot: 'status',
              },
              {
                width: 80,
                title: '操作',
                slot: 'action',
              },
            ];
            this.listApi = equipmentassets.qualitySbcjqyList;
            break;
        }
        this.tableColumns = [...columns, ...this.tableBasicColumns, ...options];
      },
      immediate: true,
    },
    searchData: {
      handler() {
        this.init();
      },
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.ui-table {
  padding: 0 20px;
}
.font-report {
  color: var(--color-content);
  text-decoration: underline;
  cursor: pointer;
  &.font-report-success {
    color: var(--color-success);
  }
  &.font-report-error {
    color: var(--color-failed);
  }
}
</style>
