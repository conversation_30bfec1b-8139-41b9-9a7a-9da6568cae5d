export class ViewOperations {
  graph = null;
  constructor({ graph }) {
    this.graph = graph;
  }

  selectAllNode() {
    this.graph.getNodes().forEach((node) => {
      const model = node.get('model');
      this.graph.updateItem(node, {
        stateStyles: {
          selected: {
            fill: model.fill,
            stroke: 'green',
            lineWidth: 2,
            shadowColor: 'green',
            shadowBlur: 10,
          },
        },
      });
      this.graph.setItemState(node, 'selected', true);
    });
  }

  invertNode() {
    this.graph.getNodes().forEach((node) => {
      const model = node.get('model');
      if (node.hasState('selected')) {
        this.graph.setItemState(node, 'selected', false);
      } else {
        this.graph.updateItem(node, {
          stateStyles: {
            selected: {
              fill: model.fill,
              stroke: 'green',
              lineWidth: 2,
              shadowColor: 'green',
              shadowBlur: 10,
            },
          },
        });
        this.graph.setItemState(node, 'selected', true);
      }
    });
  }

  showSelected() {
    this.graph.getNodes().forEach((node) => {
      if (!node.hasState('selected')) {
        this.graph.hideItem(node);
      }
    });
  }

  hideSelected() {
    this.graph.getNodes().forEach((node) => {
      if (node.hasState('selected')) {
        this.graph.hideItem(node);
      }
    });
  }

  showAll() {
    this.graph.getNodes().forEach((node) => {
      this.graph.showItem(node);
    });
  }

  deleteSelected() {
    this.graph.getNodes().forEach((node) => {
      if (node.hasState('selected')) {
        this.graph.removeItem(node);
      }
    });
  }

  deleteOrphanPoints() {
    this.graph.getNodes().forEach((node) => {
      if (node.getNeighbors().length === 0) {
        this.graph.hideItem(node);
      }
    });
  }

  clear() {
    this.graph.clear();
  }

  removeHull() {
    this.graph.removeHulls();
  }
}
