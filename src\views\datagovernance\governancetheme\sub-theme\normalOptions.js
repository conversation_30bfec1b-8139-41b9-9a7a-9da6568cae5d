let normalData = [
  {
    addVisible: false,
    top: '2.0rem',
    left: '1.2%',
    datas: [
      {
        name: 'dataInput',
        icon: 'icon-zu16191',
        title: '数据输入',
        desc: '选择待治理数据',
        left: '10px',
        iconPass: true,
        iconSetting: false,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '1.6%',
      height: '0.04rem',
      top: '2.28rem',
      left: '15.5%',
    },
  },
  {
    addVisible: false,
    top: '1.48rem',
    left: '17.2%',
    datas: [
      {
        name: 'fieldMapping',
        icon: 'icon-ziduanyingshe',
        title: '字段映射',
        desc: '设置原始数据与标准数据的字段映射关系',
        left: '10px',
        iconSetting: true,
        switch: false,
      },
      {
        name: 'dictionarymapping',
        icon: 'icon-zidianyingshe',
        title: '字典映射',
        desc: '设置原始数据与标准数据的数据字典的映射关系',
        left: '10px',
        iconSetting: true,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '1.6%',
      height: '0.04rem',
      top: '1.8rem',
      left: '32.9%',
    },
  },
  {
    addVisible: false,
    top: '1.48rem',
    left: '34.6%',
    datas: [
      {
        name: 'formawarehouse',
        icon: 'icon-zhengshiruku',
        title: '正式入库',
        desc: '标准转换后的数据存入系统标准库。',
        left: '10px',
        iconSetting: true,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '1.6%',
      height: '0.04rem',
      top: '2.28rem',
      left: '40.6%',
      angle: 90,
    },
  },
  {
    addVisible: false,
    top: '2.39rem',
    left: '34.6%',
    datas: [
      {
        name: 'DataDetection',
        icon: 'icon-quedingzhilishuju',
        title: '数据范围选择',
        desc: '确定要检测的设备数据。',
        left: '10px',
        iconSetting: true,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '1.6%',
      height: '0.04rem',
      top: '2.7rem',
      left: '48.9%',
    },
  },
  {
    addVisible: false,
    top: '1.48rem',
    left: '50.6%',
    datas: [
      {
        name: 'Empty',
        icon: 'icon-kongzhijiance',
        title: '空值检测',
        desc: '检测字段是否为空',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
      {
        name: 'Repeat',
        icon: 'icon-zhongfujiance',
        title: '重复检测',
        desc: '检测字段是否重复',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '1.6%',
      height: '0.04rem',
      top: '2.28rem',
      left: '66.4%',
    },
  },
  {
    addVisible: false,
    top: '0.4rem',
    left: '68.1%',
    datas: [
      {
        name: 'IPdizhi',
        icon: 'icon-IPdizhigeshijiance',
        title: 'IP地址格式检测',
        desc: '检测IP地址格式是否正确',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
      {
        name: 'MACdizhi',
        icon: 'icon-MACdizhigeshijiance',
        title: 'MAC地址格式检测',
        desc: '检测MAC地址格式是否正确',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
      {
        name: 'hangzheng',
        icon: 'icon-hangzhengquhuageshijiance',
        title: '行政区划格式检测',
        desc: '需符合《GB/T 2260 中华人民共和国行政区划代码》规定',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
      {
        name: 'shebeibianma',
        icon: 'icon-shebeibianmageshijiance',
        title: '设备编码格式检测',
        desc: '需符合《GB/T 28181 2016》中关于设备编码的规定',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
      {
        name: 'testing',
        icon: 'icon-kongjianxinxijiance',
        title: '空间信息检测',
        desc: '经纬度与地址大量重复、经纬度越界、经纬度偏移检测',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '1.6%',
      height: '0.04rem',
      top: '2.28rem',
      left: '83.8%',
      angle: 0,
    },
  },
  {
    addVisible: false,
    top: '2.08rem',
    left: '85.3%',
    datas: [
      {
        name: 'Timetest',
        icon: 'icon-shizhongxinxijianceshezhi',
        title: '时钟信息检测设置',
        desc: '检测设备时钟是否准确，并支持重设时钟。',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '0.2rem',
      height: '0.04rem',
      top: '3.03rem',
      left: '90.3%',
      angle: 90,
    },
  },
  {
    addVisible: false,
    top: '3.2rem',
    left: '85.3%',
    datas: [
      {
        name: 'output',
        icon: 'icon-zu1665',
        title: '数据输出',
        desc: '追踪查阅数据最终检测结果',
        left: '10px',
        iconSetting: false,
        switch: false,
      },
    ],
  },
];
export { normalData };
