<template>
  <ui-modal
    v-model="visible"
    :title="title"
    :styles="styles"
    :footer-hide="!reviewVisible"
    :class="{ 'no-footer': !reviewVisible }"
  >
    <div
      class="detail-wrap"
      v-ui-loading="isShowOnlyLoading ? { loading: loading } : { loading: loading, tableData: cacheDeviceList }"
    >
      <slot name="detailcontent" :detail-info="detailInfo" :visible="visible">
        <!-- 注意： detailInfo 当isGetDetailByapi为true时，是前端组装的数据，则接口返回数据存储在 detailInfo.info字段中 -->
        <PicCarouselVideo
          class="pic-carousel-box"
          :visible="visible"
          :imgList="[detailInfo.info ? detailInfo.info : detailInfo]"
          :filed-name-map="filedNameMap"
        ></PicCarouselVideo>
      </slot>
      <div class="recheck-box" v-if="reviewVisible">
        <div class="new-status-box">
          <span class="base-text-color inline mr-lg vt-middle new-status-box-one">
            最新状态：
            <span :class="[getNewStatus(currentReviewItem.qualified).className]">
              {{ getNewStatus(currentReviewItem.qualified).text }}
              {{ currentReviewItem.dataMode === '3' ? `(人工)` : '' }}
            </span>
          </span>
          <span
            class="base-text-color inline mr-lg vt-middle ellipsis new-status-box-item-top"
            :title="currentReviewItem.reason"
          >
            备注：
            <span
              class="ellipsis inline error-description"
              :class="[getNewStatus(currentReviewItem.qualified).className]"
            >
              {{ currentReviewItem.reason }}
            </span>
          </span>
        </div>
        <Form :model="reviewFormData" :label-width="80" class="review-form">
          <FormItem label="人工复核:" class="form-item-box">
            <RadioGroup v-model="reviewFormData.qualified" class="radio-box">
              <Radio class="mr-lg" :label="item.value" v-for="(item, index) in qualifiedList" :key="index">
                {{ item.key }}
              </Radio>
            </RadioGroup>
            <Input
              type="textarea"
              class="desc"
              v-model="reviewFormData.reason"
              placeholder="请输入备注信息"
              :rows="2"
              :maxlength="256"
            ></Input>
          </FormItem>
          <slot name="customitem" :review-form-data="reviewFormData"></slot>
        </Form>
      </div>
    </div>
    <template slot="footer" v-if="reviewVisible">
      <Button v-if="showPreButton" type="primary" :loading="deviceBtnloading.preBtn" @click="preReview">上一条</Button>
      <Button type="primary" class="ml-lg" @click="artificial('cur')" :loading="saveLoading">确定复核结果</Button>
      <Button type="primary" class="ml-lg" @click="artificial('next')" v-if="showNextButton" :loading="saveLoading">
        确定复核结果并下一条
      </Button>
      <Button
        v-if="showNextButton"
        type="primary"
        class="ml-lg"
        :loading="deviceBtnloading.nextBtn"
        @click="nextReview"
      >
        下一条
      </Button>
    </template>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'base-review',
  components: {
    PicCarouselVideo:
      require('@/views/governanceevaluation/evaluationoResult/components/pic-algorithm/pic-carousel-video.vue').default,
  },
  props: {
    title: {
      type: String,
      default: '',
    },
    value: {
      type: Boolean,
      default: false,
    },
    styles: {
      type: Object,
      default: () => {
        return {
          width: '9.5rem',
          height: '4.4rem',
        };
      },
    },
    reviewRowData: {
      type: Object,
      default: () => {},
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    pageData: {
      type: Object,
      default: () => {
        return {
          pageNum: 1,
          pageSize: 20,
        };
      },
    },
    totalCount: {
      type: Number,
      default: 0,
    },
    reviewVisible: {
      type: Boolean,
      default: true,
    },
    qualifiedList: {
      type: Array,
      default: () => [],
    },
    isGetDetailByapi: {
      type: Boolean,
      default: false,
    },
    getDetailInfo: {
      type: Function,
      default() {},
    },
    custormParams: {
      type: Object,
      default: () => {},
    },
    // v-ui-loading 只展示loading，不显示 nodata图片
    isShowOnlyLoading: {
      type: Boolean,
      default: false,
    },
    // 自定义字段名称 [兼容取值字段不一致问题]
    filedNameMap: {
      default: () => {
        return {
          smallPicName: 'imageUrl', // 小图
          bigPicName: 'imageUrl', // 大图
          resultTip: 'resultTip', // 图片备注
          qualified: 'qualified', // 不合格
          // ...
        };
      },
    },
    // 设备列表搜索条件
    searchParames: {
      type: Object,
    },
    // 获取列表的接口 --- 若调用接口不一样，则需要调用方自行处理
    getListApi: {
      type: Function,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      reviewFormData: {
        qualified: '',
        reason: '',
      },
      currentReviewItem: {
        // qualified: '',
        // reason: '',
        // ...
      },
      detailInfo: {},
      saveLoading: false,
      deviceBtnloading: {
        preBtn: false,
        nextBtn: false,
      },

      // 一页一条数据的请求方案： 需要维护一个  isUnExistTable: Boolean， 代表该数据是否已经不符合当前筛选条件下的数据了  true: 与条件不符合，已不存在   false: 满足条件，存在
      // currentDeviceIndex: 0,
      cacheDeviceList: [], // 只有请求接口才会 缓存起来
      currentIndexByCache: 0,
      currentPageNum: 1, // 当前页码（基于总数算，对于一页一条数据去请求，该字段其实也代表了在总数据中第几条数据）
      isReview: false, // 是否复核过
      devicePageData: {
        pageSize: 50,
      },
      deviceListLoading: false,
    };
  },
  computed: {
    // 最新状态
    getNewStatus() {
      return (str) => {
        return {
          text: str == '1' ? '合格' : '不合格',
          className: str == '1' ? 'c-green' : 'c-red',
        };
      };
    },
    showPreButton() {
      return this.cacheDeviceList.length && !(this.currentIndexByCache === 0 && this.currentPageNum === 1);
    },
    showNextButton() {
      // 最后一页，并且是最后一条设备才不展示
      return this.cacheDeviceList.length && !this.currentReviewItem.isLastPage;
    },
  },
  methods: {
    async initFn() {
      this.isReview = false;

      this.loading = true;
      // 该条数据 在 外部表格中的索引
      let index = this.tableData.findIndex((item) => item.id === this.reviewRowData.id);
      // 该条数据 在 总数中的索引
      this.currentPageNum = (this.pageData.pageNum - 1) * this.pageData.pageSize + index + 1;

      // 默认查询 该条数据所在页码对应的 devicePageData.pageSize条数据
      this.cacheDeviceList = [];
      if (this.reviewVisible) {
        let res = await this.getDeviceList();
        this.cacheDeviceList = res.entities;
      } else {
        this.cacheDeviceList = this.tableData;
      }
      this.currentIndexByCache = this.cacheDeviceList.findIndex((item) => item.id === this.reviewRowData.id);

      let deviceItem = this.cacheDeviceList.filter(
        (item) => item.deviceId === this.reviewRowData.deviceId && item.id === this.reviewRowData.id,
      );
      this.currentReviewItem = { ...deviceItem[0] };

      if (this.isGetDetailByapi) {
        this.detailInfo = await this.getDetailInfo(this.currentReviewItem);
      } else {
        this.detailInfo = this.currentReviewItem || {};
      }
      this.$emit('changeDevice', this.detailInfo);

      this.loading = false;
    },
    async preReview() {
      let { nextBtn } = this.deviceBtnloading;
      if (this.saveLoading || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }

      try {
        this.loading = true;
        this.deviceBtnloading.preBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.currentIndexByCache - 1]?.isUnExistTable) {
          this.currentPageNum--;
        }

        if (this.currentIndexByCache === 0) {
          // 已经不存在上一条，则调接口
          let res = await this.getDeviceList();
          let endIndex = res.entities.length - 1;
          this.currentReviewItem = res.entities[endIndex];
          // 注意： 要添加到数组前面
          this.cacheDeviceList = [...res.entities, ...this.cacheDeviceList];
          this.currentIndexByCache = endIndex;
        } else {
          this.currentIndexByCache--;
          this.currentReviewItem = this.cacheDeviceList[this.currentIndexByCache];
        }

        if (this.isGetDetailByapi) {
          this.detailInfo = await this.getDetailInfo(this.currentReviewItem);
        } else {
          this.detailInfo = this.currentReviewItem || {};
        }
        this.$emit('changeDevice', this.detailInfo);
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.preBtn = false;
        this.loading = false;
      }
    },
    async nextReview() {
      let { preBtn } = this.deviceBtnloading;
      if (this.saveLoading || preBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      try {
        this.loading = true;
        this.deviceBtnloading.nextBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.currentIndexByCache].isUnExistTable) {
          this.currentPageNum++;
        }
        this.currentIndexByCache++;

        if (this.cacheDeviceList[this.currentIndexByCache]) {
          this.currentReviewItem = this.cacheDeviceList[this.currentIndexByCache];
        } else {
          // 缓存中不存在下一条数据，则调接口
          let res = await this.getDeviceList();
          this.currentReviewItem = { ...res.entities[0] };
          // 注意： 要添加到数组后面
          this.cacheDeviceList = [...this.cacheDeviceList, ...res.entities];
        }

        if (this.isGetDetailByapi) {
          this.detailInfo = await this.getDetailInfo(this.currentReviewItem);
        } else {
          this.detailInfo = this.currentReviewItem || {};
        }
        this.$emit('changeDevice', this.detailInfo);
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.nextBtn = false;
        this.loading = false;
      }
    },
    async artificial(str = '') {
      let { preBtn, nextBtn } = this.deviceBtnloading;
      if (preBtn || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      this.saveLoading = true;
      let data = {
        data: {
          id: this.currentReviewItem.id,
          qualified: this.reviewFormData.qualified,
          reason: this.reviewFormData.reason,
          ...this.custormParams,
        },
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      let isSuccess = false;
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.$Message.success(res.data.msg);
        this.isReview = true;
        isSuccess = true;

        // 复核成功后，需要 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
        this.updateInfo(str);
      } catch (err) {
        console.log(err);
      } finally {
        this.saveLoading = false;
        // 确定复核并下一条  触发
        if (str === 'next' && isSuccess) {
          this.nextReview();
        }
      }
    },
    // 获取设备数据
    async getDeviceList(customParams, isUpdate = false) {
      try {
        if (!isUpdate) {
          this.deviceListLoading = true;
        }
        let res = {};
        let { pageSize } = this.devicePageData;
        // 使用调用方参入的函数
        if (this.getListApi) {
          let params = {
            pageSize: pageSize,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            isUpdate: isUpdate,
            currentRow: { ...this.currentReviewItem },
          };
          res = await this.getListApi(params);
        } else {
          let { regionCode, orgCode, statisticType, indexId, batchId } = this.$route.query;
          let params = {
            indexId: indexId,
            batchId: batchId,
            access: 'TASK_RESULT',
            displayType: statisticType,
            orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
            customParameters: this.searchParames,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            pageSize: pageSize,
          };
          // 以传入参数为主
          if (customParams) {
            params = { ...params, ...customParams };
          }
          res = await this.$http.post(evaluationoverview.getDetailData, params);
        }

        let { entities, lastPage } = res.data.data;

        if (!isUpdate) {
          // 注意： 如果中间存在 isUnExistTable=true , 那此时查询到的列表数据，存在部分数据已缓存过了 --- 每页一条查询是不会的，但现在支持根据 devicePageData.pageSize可配置
          entities = entities.filter(
            (item) =>
              !this.cacheDeviceList.some(
                (cacheItem) => cacheItem.id === item.id && cacheItem.deviceId === item.deviceId,
              ),
          );
        }

        entities = entities.map((item, index) => {
          return {
            ...item,
            isLastPage: lastPage && index === entities.length - 1, // 是否为最后一条数据
          };
        });

        res.data.data.entities = entities;
        return res.data.data;
      } catch (error) {
        console.log(error);
        throw new Error();
      } finally {
        this.deviceListLoading = false;
      }
    },
    // 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
    async updateInfo(str = '') {
      let { qualified, reason } = this.reviewFormData;
      if (this.searchParames?.outcome && this.searchParames?.outcome !== qualified) {
        // 【检查结果】 刷选条件有值，并且与  当前设备复核结果不同
        this.cacheDeviceList[this.currentIndexByCache].isUnExistTable = true;
        this.currentReviewItem.isUnExistTable = true;

        // 主要是 下面调接口更新需要时间， 接口完成前 先前端替换， 这样就不会影响到上一条下一条显示数据没有更新问题 ， 且也不会影响到 关闭弹框时所做的判断
        // 其实就是 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
        this.cacheDeviceList[this.currentIndexByCache].qualified = qualified;
        this.cacheDeviceList[this.currentIndexByCache].reason = reason;
        this.cacheDeviceList[this.currentIndexByCache].dataMode = '3';
        this.currentReviewItem.qualified = qualified;
        this.currentReviewItem.reason = reason;
        this.currentReviewItem.dataMode = '3';
      }
      let { deviceId, isLastPage } = this.currentReviewItem;
      let params = {
        customParameters: {
          deviceId: deviceId,
        },
        pageNumber: 1,
      };
      let res = await this.getDeviceList(params, true);
      let obj = res.entities[0];
      let currentIndexByCache = this.cacheDeviceList.findIndex(
        (item) => item.deviceId === obj.deviceId && item.id === obj.id,
      );
      let info = {
        ...this.cacheDeviceList[currentIndexByCache],
        ...obj,
        isLastPage: isLastPage, // 保留之前的状态值
      };
      this.cacheDeviceList[currentIndexByCache] = info;
      if (str === 'cur' && this.currentReviewItem.id === obj.id && this.currentReviewItem.deviceId === obj.deviceId) {
        this.currentReviewItem = info;
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.initFn();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        let closeIndex = this.currentPageNum - 1;
        // 特殊处理，关闭弹框时，如果是 最后一条数据 且 复核 的情况
        let { isLastPage } = this.currentReviewItem;
        if (
          isLastPage &&
          this.searchParames?.outcome &&
          this.searchParames?.outcome !== this.currentReviewItem.qualified
        ) {
          closeIndex = closeIndex - 1;
        }
        this.$emit('closeFn', {
          isReview: this.isReview,
          closeIndex: closeIndex,
        });
      }
    },
    currentReviewItem: {
      handler(val) {
        this.reviewFormData.qualified = val.qualified === '1' ? '2' : '1';
        this.reviewFormData.reason = '';
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.detail-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .recheck-box {
    width: 100%;
    height: 125px;
    padding: 10px 20px;
    border-top: 1px solid var(--border-modal-footer);
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 28px;
    .new-status-box {
      display: flex;
      .new-status-box-item-top {
        width: calc(100% - 230px);
        display: flex;
      }
      .error-description {
        width: calc(100% - 50px);
      }
      .new-status-box-one {
        max-width: 180px;
      }
    }
    .review-form .ivu-form-item-content {
      display: flex;
    }
    .radio-box {
      width: 270px;
    }
    .desc {
      width: calc(100% - 270px);
      @{_deep}textarea {
        resize: none;
      }
    }
    @{_deep}.ivu-form-item {
      margin: 0;
    }
  }
}

@{_deep} .form-item-box .ivu-form-item-content {
  display: flex;
}
@{_deep}.ivu-form .ivu-form-item-label {
  text-align: left;
}
@{_deep}.ivu-btn.ivu-btn-loading {
  .ivu-icon-ios-loading {
    position: absolute;
    left: 5px;
    top: 5px;
  }
  .ivu-icon + span {
    margin-left: 0 !important;
  }
}
@{_deep}.ivu-modal-content {
  height: 100%;
}
@{_deep}.ivu-modal-header {
  padding: 0;
}
@{_deep}.ivu-modal-body {
  height: calc(100% - 66px);
  padding: 5px 0 0;
}
.no-footer @{_deep}.ivu-modal-body {
  height: 100% !important;
}
.pic-carousel-box {
  width: 100%;
  height: 100%;
}
</style>
