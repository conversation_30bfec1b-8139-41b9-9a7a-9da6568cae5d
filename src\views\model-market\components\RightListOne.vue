<template>
    <div class="right-list-one" :style="{width: pageName.indexOf('frequence')>=0?'250px':'300px'}">
        <template v-if="pageName=='together'">
            <div class="together-people-box">
                <p class="people-header">
                    共<span> {{appearList.length}} </span>条同行记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                            v-for="(item,$index) in appearList" :key="$index" @click="showPic(item.mainPerson)">
                                <div class="result-item-li-box">
                                    <p class="result-item-li-deviceName ellipsis m-left10" :title="item.mainPerson.deviceName"><span>{{$index+1}}</span> {{item.mainPerson.deviceName}}</p>
                                    <div class="main-people f-fl">
                                        <div class="result-item-img">
                                            <img :src="item.mainPerson.traitImg" v-viewer @click.stop="">
                                        </div>
                                        <p>{{item.mainPerson.absTime}}</p>
                                    </div>
                                    
                                    <div class="main-people f-fl">
                                        <div class="result-item-img">
                                            <img :src="item.alongPerson.traitImg" v-viewer @click.stop="">
                                        </div>
                                        <p>{{item.alongPerson.absTime}}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='vehicle-together'">
            <div class="together-people-box">
                <p class="people-header">
                    共<span> {{appearList.length}} </span>条同行记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                            v-for="(item,$index) in appearList" :key="$index" @click="showPic(item.mainVehicle)">
                                <div class="result-item-li-box">
                                    <p class="result-item-li-deviceName ellipsis m-left10" :title="item.mainVehicle.deviceName"><span>{{$index+1}}</span> {{item.mainVehicle.deviceName}}</p>
                                    <div class="main-people f-fl">
                                        <div class="result-item-img">
                                            <img :src="item.mainVehicle.sceneImg" v-viewer @click.stop="">
                                        </div>
                                        <p>{{item.mainVehicle.absTime}}</p>
                                    </div>
                                    
                                    <div class="main-people f-fl">
                                        <div class="result-item-img">
                                            <img :src="item.alongVehicle.sceneImg" v-viewer @click.stop="">
                                        </div>
                                        <p>{{item.alongVehicle.absTime}}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='frequence'">
            <div class="together-people-box" style="width: 230px;right:0;">
                <div style="width:100%;height:100%;">
                    <p class="people-header">
                        共<span> {{appearList.length}} </span>条记录
                    </p>
                    <div class="result-item">
                        <div v-scroll style="height:100%;">
                            <ul>
                                <li class="result-item-li" v-for="(item,$index) in appearList" :key="$index">
                                    <div class="result-item-li-box">
                                        <p class="result-item-li-deviceName ellipsis m-left10" :title="item.deviceName">
                                            <span>{{$index+1}}</span> {{item.deviceName || '位置未知'}}
                                        </p>
                                        <div class="main-people f-fl" style="width:100%;">
                                            <div class="result-item-img" >
                                                <img :src="item.traitImg" v-viewer @click.stop="">
                                            </div>
                                            <p>{{item.absTime}}</p>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='imei-frequence'">
            <div class="together-people-box" style="width: 230px;right:0;">
                <div style="width:100%;height:100%;">
                    <p class="people-header">
                        共<span> {{appearList.length}} </span>条记录
                    </p>
                    <div class="result-item">
                        <div v-scroll style="height:100%;">
                            <ul>
                                <li class="result-item-li" v-for="(item,$index) in appearList" :key="$index">
                                    <div class="result-item-li-box">
                                        <p class="result-item-li-deviceName ellipsis m-left10" :title="item.deviceName">
                                            <span>{{$index+1}}</span> {{item.deviceName || '位置未知'}}
                                        </p>
                                        <div class="main-people f-fl" style="width:100%;">
                                            <p style="margin-left:-44px;">{{item.absTime}}</p>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='vehicle-frequence'">
            <div class="together-people-box" style="width: 230px;right:0;">
                <div style="width:100%;height:100%;">
                    <p class="people-header">
                        共<span> {{appearList.length}} </span>条记录
                    </p>
                    <div class="result-item">
                        <div v-scroll style="height:100%;">
                            <ul>
                                <li class="result-item-li" v-for="(item,$index) in appearList" :key="$index"  @click="showPic({sceneImg:item.sceneImg})">
                                    <div class="result-item-li-box">
                                        <p class="result-item-li-deviceName ellipsis m-left10" :title="item.deviceName">
                                            <span>{{$index+1}}</span> {{item.deviceName || '位置未知'}}
                                        </p>
                                        <div class="main-people f-fl" style="width:100%;">
                                            <div class="result-item-img" >
                                                <img :src="item.sceneImg" v-viewer @click.stop="">
                                            </div>
                                            <p>{{item.absTime}}</p>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='vehicle-vespertine'">
            <div class="left-search-content" style="top:0;">
                <p class="result-header">
                    共 <span class="t-blue-color">{{appearList.length}}</span> 条记录
                </p>
                <div class="result-item"  style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                                v-for="(item,index) in appearList"
                                :key="index"
                                @click="showPic(item)">
                                <div class="result-item-li-box">
                                    <div class="result-item-img">
                                        <img :src="item.sceneImg" alt="" v-viewer @click.stop="">
                                    </div>
                                    <ul class="result-item-info">
                                        <li class="ellipsis">
                                            <span class="iconfont icon-chepai t-blue-color mr-5" title="车牌号码"></span>
                                            <span class="t-blue-color" :title="item.plateNo">
                                                {{item.plateNo}}
                                            </span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-time t-blue-color mr-5"></span>
                                            <span>{{item.absTime}}</span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-location t-blue-color mr-5"></span>
                                            <span :title="item.deviceName">{{item.deviceName}}</span>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='imei-vespertine'">
            <div class="left-search-content" style="top:0;">
                <p class="result-header">
                    共 <span class="t-blue-color">{{appearList.length}}</span> 条记录
                </p>
                <div class="result-item"  style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                                v-for="(item,index) in appearList"
                                :key="index">
                                <div class="result-item-li-box">
                                    <ul class="result-item-info" style="margin-left:0;">
                                        <li class="ellipsis">
                                            <span class="iconfont icon-dianwei1 t-blue-color mr-5" title="电围编号"></span>
                                            <span class="t-blue-color" :title="item.imsi">
                                                {{item.imsi}}
                                            </span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-time t-blue-color mr-5"></span>
                                            <span>{{item.absTime}}</span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-location t-blue-color mr-5"></span>
                                            <span :title="item.deviceName">{{item.deviceName}}</span>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='imei-together'">
            <div class="together-people-box">
                <p class="people-header">
                    共<span> {{appearList.length}} </span>条同行记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                            v-for="(item,$index) in appearList" :key="$index">
                                <div class="result-item-li-box">
                                    <p class="result-item-li-deviceName ellipsis m-left10" :title="item.mainBaseStation.deviceName"><span>{{$index+1}}</span> {{item.mainBaseStation.deviceName}}</p>
                                    <div class="main-people f-fl">
                                        <p>{{item.mainBaseStation.imsi}}</p>
                                        <p>{{item.mainBaseStation.absTime}}</p>
                                    </div>
                                    
                                    <div class="main-people f-fl">
                                        <p>{{item.alongBaseStation.imsi}}</p>
                                        <p>{{item.alongBaseStation.absTime}}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='vid-imsi'">
            <div class="together-people-box">
                <p class="people-header">
                    共<span> {{appearList.length}} </span>条记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                            v-for="(item,$index) in appearList" :key="$index" @click="showPic(item, $index)">
                                <div class="result-item-li-box" :class="{'active': $index == currentIndex}">
                                    <p class="result-item-li-deviceName ellipsis m-left10" :title="item.deviceName"><span>{{$index+1}}</span> {{item.deviceName}}</p>
                                    <div class="main-people" style="width:100%;">
                                        <img :src="item.traitImg" alt="" style="width:100px;height:100px;" v-viewer @click.stop="">
                                        <p>{{item.absTime}}</p>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='vidbasestation'">
            <div class="together-people-box">
                <p class="people-header">
                    共<span> {{appearList.length}} </span>条记录</p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                            v-for="(item,$index) in appearList" :key="$index">
                                <div class="result-item-li-box">
                                    <template v-if="modeType == '1'">
                                        <p class="result-item-li-deviceName ellipsis m-left10" :title="item.vidTrail.deviceName"><span>{{$index+1}}</span> {{item.vidTrail.deviceName}}</p>
                                        <div class="main-people f-fl" style="margin-left:70px;">
                                            <div class="result-item-img" @click="showPic(item.vidTrail)">
                                                <img :src="item.vidTrail.traitImg" v-viewer @click.stop="">
                                            </div>
                                            <p>{{item.vidTrail.absTime}}</p>
                                        </div>
                                    </template>
                                    <template v-else-if="modeType == '2'">
                                        <p class="result-item-li-deviceName ellipsis m-left10" :title="item.bsTrail.deviceId"><span>{{$index+1}}</span> {{item.bsTrail.deviceId}}</p>
                                        <div class="main-people f-fl" style="margin-left:70px;">
                                            <div class="result-item-img">
                                                <img src="@/assets/img/card-bg/imsi.png" style="width: auto">
                                            </div>
                                            <p>{{item.bsTrail.absTime}}</p>
                                        </div>
                                    </template>
                                    <template v-else>
                                        <span v-if="item.distance" class="item-distance">{{Math.ceil(item.distance)}}米</span>
                                        <span class="look-location" :class="{'active-item': $index == currentIndex}" @click="lookLocation(item.vidTrail, $index)">查看位置</span>
                                        <p class="result-item-li-deviceName ellipsis m-left10" :title="item.vidTrail.deviceName"><span>{{$index+1}}</span> {{item.vidTrail.deviceName}}</p>
                                        <div class="main-people f-fl">
                                            <div class="result-item-img">
                                                <img :src="item.vidTrail.traitImg || item.vidTrail.sceneImg" v-viewer @click.stop="">
                                            </div>
                                            <p>{{item.vidTrail.absTime}}</p>
                                        </div>
                                        
                                        <div class="main-people f-fl">
                                            <div class="result-item-img">
                                                <img src="@/assets/img/card-bg/imsi.png" style="width: auto">
                                            </div>
                                            <p>{{item.bsTrail.absTime}}</p>
                                        </div>
                                    </template>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </template>
        <template v-else-if="pageName=='fixedTerminal'">
            <div class="left-search-content" style="top:0;">
                <p class="result-header">
                    共 <span class="t-blue-color">{{appearList.length}}</span> 条记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                                v-for="(item,index) in appearList"
                                :key="index"
                                @click="showPic(item)">
                                <div class="result-item-li-box">
<!--                                    <div class="result-item-img">-->
<!--                                        <img :src="item.traitImg" alt="" @click="showPic(item)">-->
<!--                                    </div>-->
                                    <ul class="result-item-info" style="margin-left: 0">
                                        <li class="ellipsis">
                                            <span class="iconfont icon-chakanshipin t-blue-color mr-5" title="视频身份"></span>
                                            <span class="t-blue-color" :title="item.imsi">
                                                {{item.imsi}}
                                            </span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-time t-blue-color mr-5"></span>
                                            <span>{{item.absTime}}</span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-location t-blue-color mr-5"></span>
                                            <span :title="item.deviceName">{{item.deviceName}}</span>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>    
            </div>
        </template>
        <template v-else-if="pageName=='frequencyCom'">
            <div class="left-search-content" style="top:0;">
                <p class="result-header">
                    共 <span class="t-blue-color">{{total}}</span> 条记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <Scroll :on-reach-bottom="handleReachBottom" height="100%" :loading-text="loadingText" :distance-to-edge="10">
                        <li class="result-item-li"
                            v-for="(item,index) in appearList"
                            :key="index"
                            @click="showPic(item,index)">
                            <div class="result-item-li-box">
                                <div class="result-item-img">
                                    <img :src="item.faceImg" v-viewer="{ url: 'data-src'}" :data-src="item.illegalImg" alt="" @click.stop="">
                                </div>
                                <ul class="result-item-info">
                                    <li class="ellipsis">
                                        <span class="iconfont icon-time t-blue-color mr-5"></span>
                                        <span>{{item.absTime}}</span>
                                    </li>

                                    <li class="ellipsis">
                                        <span class="iconfont icon-location t-blue-color mr-5"></span>
                                        <span :title="item.deviceName">{{item.deviceName}}</span>
                                    </li>
                                </ul>
                            </div>
                        </li>
                    </Scroll>
                    <ui-empty v-if="appearList.length == 0 && !loading"></ui-empty>
                    <ui-loading v-if="loading"></ui-loading>
                </div>    
            </div>
        </template>
        <template v-else>
            <div class="left-search-content" style="top:0;">
                <p class="result-header">
                    共 <span class="t-blue-color">{{appearList.length}}</span> 条记录
                </p>
                <div class="result-item" style="bottom:10px;">
                    <div v-scroll style="height:100%;">
                        <ul>
                            <li class="result-item-li"
                                v-for="(item,index) in appearList"
                                :key="index"
                                @click="showPic(item)">
                                <div class="result-item-li-box">
                                    <div class="result-item-img">
                                        <img :src="item.traitImg" alt="" v-viewer @click.stop="">
                                    </div>
                                    <ul class="result-item-info">
                                        <li class="ellipsis">
                                            <span class="iconfont icon-chakanshipin t-blue-color mr-5" title="视频身份"></span>
                                            <span class="t-blue-color" :title="item.vid" @click="goVidDetailFun($event,item)">
                                                {{item.vid}}
                                            </span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-time t-blue-color mr-5"></span>
                                            <span>{{item.absTime}}</span>
                                        </li>

                                        <li class="ellipsis">
                                            <span class="iconfont icon-location t-blue-color mr-5"></span>
                                            <span :title="item.deviceName">{{item.deviceName}}</span>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>    
            </div>
        </template>
    </div>
</template>

<script>
export default {
    data(){
        return {
            currentIndex: null,
        }
    },
    props: {
        appearList: {
            type: Array,
            default: []
        },
        pageName: {
            type: String,
            default: ''
        },
        modeType: {
            type: String,
            default: '3'
        },
        loading: {
            type: Boolean,
            default: false
        },
        loadingText: {
            type: String,
            default: '加载中'
        },
        total: {
            type: Number,
            default: 0
        }
    },
    methods: {
        showPic(item, index){
            if(index || index == 0){
                this.currentIndex = index;
            }
            this.$emit('show-pic', item, index);
        },
        changeMode(){
            this.$emit('change-mode')
        },
        lookLocation(item, index){
            this.currentIndex = index;
            this.$emit('look-location', item);
        },
        handleReachBottom () {
            this.$emit('handleReachBottom');
        },
    }
}
</script>

<style lang="less" scoped>
    .t-blue-color {
        color: #2c86f8;
    }
    .f-fl {
        float: left;
    }
    .f-fr {
        float: right;
    }
    .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: default;
    }
    .right-list-one{
        position: absolute;
        width: 300px;
        height: auto;
        top: 10px;
        bottom: 10px;
        right: 10px;
        // color: #666;
        border-radius: 5px;
        // background-color: #fff;
        background: #fff;
        opacity: 0.95;
        overflow: hidden;
        z-index: 999;
        .left-search-content {
            position: absolute;
            top: 40px;
            bottom: 0;
            width: 100%;
            margin-top: 0;
            .result-header{
                height: 40px;
                line-height: 40px;
                border-bottom: 1px #d3d7de solid;
                margin-right: 10px;
                text-align: right;
            }
            .result-item {
                position: absolute;
                top: 40px;
                left: 0;
                right: 0;
                bottom: 90px;
                min-height: 62px;
                /deep/ .ivu-scroll-wrapper {
                    height: 100%;
                    width: 100%;
                    position: initial;
                    .ivu-scroll-container {
                        overflow: auto;
                        height: 100%;
                    }
                }
                .result-item-li {
                    padding: 10px;
                    border-bottom: 1px solid #d3d7de;
                    cursor: pointer;
                    overflow: hidden;
                    list-style: none;
                    .result-item-li-box {
                        // padding: 10px;
                        overflow: hidden;
                        &:hover{
                            background-color: #f3f3f3;
                        }
                        .result-item-img {
                            float: left;
                            position: relative;
                            z-index: 1;
                            img{
                                width: 80px;
                                height: 100px;
                            }
                        }
                        .result-item-info {
                            margin-left: 90px;
                            line-height: 20px;
                            position: relative;
                            z-index: 1;
                            .ellipsis{
                                cursor: pointer;
                            }
                            span{
                                vertical-align: middle;
                            }
                        }
                    }
                }
            }
        }

        .together-people-box {
            position: absolute;
            width: 280px;
            height: auto;
            top: 10px;
            bottom: 10px;
            right: 10px;
            // color: #666;
            // background-color: #fff;
            border-radius: 5px;
            overflow: hidden;
            z-index: 999;
            .people-header{
                height: 40px;
                line-height: 40px;
                padding-left: 20px;
                border-bottom: 1px solid #d3d7de;
                span{
                    color: #47a3ff;
                }
            }
            .result-item {
                position: absolute;
                top: 40px;
                bottom: 0;
                left: 0;
                right: 0;
                .result-item-li{
                    overflow: hidden;
                    border-bottom: 1px solid #d3d7de;
                    .result-item-li-box {
                        overflow: hidden;
                        position: relative;
                        &:hover{
                            background-color: #f3f3f3;
                        }
                        .item-distance{
                            position: absolute;
                            left: 45%;
                            top: 30%;
                            color: #47a3ff;
                            font-weight: bold;
                        }
                        .look-location{
                            position: absolute;
                            left: 41%;
                            top: 68%;
                            font-weight: bold;
                            cursor: pointer;
                            text-decoration: underline;
                            font-size: 12px;
                        }
                        .active-item{
                            color: #47a3ff;
                        }
                        .result-item-li-deviceName {
                            height: 30px;
                            line-height: 30px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            cursor: default;
                            span{
                                display: inline-block;
                                width: 26px;
                                height: 26px;
                                line-height: 17px;
                                text-align: center;
                                background: url("~@/assets/img/mark-blue.png") no-repeat;
                                background-size: cover;
                            }
                        }
                        .main-people {
                            width: 50%;
                            text-align: center;
                            .result-item-img{
                                img{
                                    width: 80px;
                                    height: 100px;
                                    cursor: pointer;
                                }
                            }
                            p{
                                height: 30px;
                                line-height: 30px;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
