<!--
    * @FileDescription: 隐匿车
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-13 17:34:31
 -->
<template>
  <div class="leftBox">
    <div class="search-box">
      <div class="title">
        <p>隐匿车分析</p>
      </div>
      <div class="search_condition">
        <div class="search_form">
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车牌号码:</p>
            </div>
            <div class="search_content">
              <Input
                v-model="queryParams.plateNo"
                placeholder="请输入车牌号码"
                class="wrapper-input"
              ></Input>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车牌颜色:</p>
            </div>
            <div class="search_content">
              <ui-tag-select
                ref="bodyColor"
                @input="
                  (e) => {
                    input(e, 'plateColor');
                  }
                "
              >
                <ui-tag-select-option
                  v-for="(item, $index) in licensePlateColorList"
                  :key="$index"
                  effect="dark"
                  :name="item.dataKey"
                >
                  <div
                    v-if="licensePlateColorArray[item.dataKey]"
                    :title="item.dataValue"
                    :style="{
                      borderColor:
                        licensePlateColorArray[item.dataKey].borderColor,
                    }"
                    class="plain-tag-color"
                  >
                    <div
                      :style="licensePlateColorArray[item.dataKey].style"
                    ></div>
                  </div>
                </ui-tag-select-option>
              </ui-tag-select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车牌类型:</p>
            </div>
            <div class="search_content">
              <Select v-model="queryParams.plateClass" placeholder="请选择">
                <Option
                  v-for="(item, $index) in plateClassList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆颜色:</p>
            </div>
            <div class="search_content">
              <Select v-model="queryParams.vehicleColor" placeholder="请选择">
                <Option
                  v-for="(item, $index) in bodyColorList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆类型:</p>
            </div>
            <div class="search_content">
              <Select v-model="queryParams.vehicleType" placeholder="请选择">
                <Option
                  v-for="(item, $index) in vehicleClassTypeList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">案发时间:</p>
            </div>
            <div class="search_content">
              <DatePicker
                v-model="queryParams.date"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="案发时间"
                transfer
              ></DatePicker>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">追查时长:</p>
            </div>
            <div class="search_content">
              <InputNumber
                v-model="queryParams.dayNum"
                class="wrapper-input"
              ></InputNumber>
              <span class="ml-10">天</span>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">选择范围:</p>
            </div>
            <ul class="search_content">
              <li class="active-area-sele" @click="selectDevice()">
                选择设备/已选({{ deviceList.length }})
              </li>
              <li class="area-list" @click="handleSeleArea()">
                <ui-icon type="gateway" />
              </li>
            </ul>
            <!-- <div class="search_content">
              <li
                class="area-list"
                v-for="(item, index) in toolMap"
                :key="index"
                @click="handleSeleArea(item)"
              >
                <img :src="item.icon" alt="" />
              </li>
              <li class="area-list" @click="selectDevice()">
                <i class="iconfont icon-ziyuanmulu" title="树选" />
              </li>
              <span class="result" v-if="deviceList.length"
                >已选<span class="num">{{ deviceList.length }}</span
                >个设备</span
              >
            </div> -->
          </div>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
          </div>
        </div>
      </div>
    </div>

    <!-- 选择设备 -->
    <select-device ref="selectDevice" @selectData="selectData" />
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
export default {
  name: "",
  props: {
    deviceList: {
      type: Array,
      default: () => [],
    },
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      queryParams: {
        dayNum: 3,
        plateNo: "", //皖C82550
        vehicleColor: "",
        plateColor: "",
        vehicleType: "",
        plateClass: "",
        deviceGbIdList: [],
      },
      licensePlateColorArray, // 车牌颜色
      toolMap: [
        {
          title: "圆形框选",
          icon: require("@/assets/img/model/icon/circle.png"),
          value: "circle",
          funName: "selectDrawCircleByDiameter",
        },
        {
          title: "矩形框选",
          icon: require("@/assets/img/model/icon/rectangle.png"),
          value: "rectangle",
          funName: "selectRectangle",
        },
        {
          title: "多边形框选",
          icon: require("@/assets/img/model/icon/polygon.png"),
          value: "polygon",
          funName: "selectPolygon",
        },
      ],
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      plateClassList: "dictionary/getPlateClassList", // 车牌类型(枚举精准检索)
    }),
  },
  async created() {
    this.queryParams.date = this.getAgoDay(0);
    await this.getDictData();
    this.$nextTick(() => {
      // 推荐中心查看
      console.log("taskParams: ", this.taskParams);
      if (!Toolkits.isEmptyObject(this.taskParams)) {
        if (this.taskParams.queryStartTime)
          this.queryParams.date = this.taskParams.queryStartTime;
        if (this.taskParams.params.dayNum)
          this.queryParams.dayNum = this.taskParams.params.dayNum;
        if (this.taskParams.taskResult) this.handleSearch();
      }
    });
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    input(e, key) {
      this.queryParams[key] = e;
    },
    // 查询
    handleSearch() {
      this.queryParams.date = this.$dayjs(this.queryParams.date).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.queryParams.deviceGbIdList = this.deviceList.map(
        (v) => v.deviceGbId
      );
      this.$emit("search", this.queryParams);
    },
    // 时间判断
    getAgoDay(n) {
      let date = new Date();
      let newDate = new Date(date.getTime() - n * 24 * 60 * 60 * 1000);
      var Y = newDate.getFullYear() + "-";
      var M =
        (newDate.getMonth() + 1 < 10
          ? "0" + (newDate.getMonth() + 1)
          : newDate.getMonth() + 1) + "-";
      var D =
        newDate.getDate() < 10
          ? "0" + newDate.getDate() + " "
          : newDate.getDate() + " ";
      var h =
        newDate.getHours() < 10
          ? "0" + newDate.getHours() + ":"
          : newDate.getHours() + ":";
      var m =
        newDate.getMinutes() < 10
          ? "0" + newDate.getMinutes() + ":"
          : newDate.getMinutes() + ":";
      var s =
        newDate.getSeconds() < 10
          ? "0" + newDate.getSeconds()
          : newDate.getSeconds();
      return Y + M + D + h + m + s;
    },
    // 选择区域
    handleSeleArea(item) {
      this.$emit("handleSeleArea");
      // rectangle 矩形
      // circle    圆形
      // polygon   多边形
      // this.$emit("selectDraw", item.funName);
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(
        this.deviceList.map((v) => {
          v.select = true;
          return v;
        })
      );
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.$emit("selectDevice", list);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";

/deep/ .ivu-date-picker {
  width: 100%;
}
/deep/ .ivu-tag-select-option {
  margin-right: 5px !important;
}
</style>
