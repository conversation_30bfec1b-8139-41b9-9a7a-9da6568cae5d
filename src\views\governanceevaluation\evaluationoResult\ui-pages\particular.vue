<template>
  <div class="review-particular auto-fill">
    <slot name="iconstatics">
      <div class="icon-statics-wrapper">
        <icon-statics :icon-list="iconList"> </icon-statics>
        <slot name="iconWrapperRight" class="icon-statics-wrapper-right">
          <tag-view
            v-if="tabList.length > 1 /*设备模式 默认不显示*/"
            :list="tabList"
            @tagChange="changeMode"
            ref="tagView"
            class="tag-view mt-xs"
          ></tag-view>
        </slot>
      </div>
    </slot>
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      :form-item-size="formItemSize"
      :need-reset-copy-search="needResetCopySearch"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
      <template #otherButton>
        <slot name="otherButton"></slot>
      </template>
      <template #search>
        <slot name="search"></slot>
      </template>
    </dynamic-condition>
    <slot name="selectTabs"></slot>
    <div class="auto-fill" v-ui-loading="{ loading: tableLoading }">
      <ui-table
        v-show="modeType === 'device'"
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
      >
        <template v-for="item of tableSlotList" #[item]="{ row, index }">
          <slot :name="item" :row="row" :index="index"> </slot>
        </template>
      </ui-table>
      <pic-mode
        v-show="modeType === 'image' && !showPicAlgorithm"
        v-ui-loading="{ tableData: cardList }"
        :card-list="cardList"
        :imgKey="imgKey"
        @handleBigImgView="(cardData) => $emit('handleBigImgView', cardData)"
        @artificialReview="(cardData) => $emit('artificialReview', cardData)"
        @algorithmsReview="(cardData) => $emit('algorithmsReview', cardData)"
      ></pic-mode>
      <PicAlgorithm
        v-show="modeType === 'image' && showPicAlgorithm"
        v-ui-loading="{ tableData: cardList }"
        :card-list="cardList"
        :imgKey="imgKey"
        :filed-name-map="filedNameMap"
        @artificialReview="(cardData) => $emit('artificialReview', cardData)"
        @algorithmsReview="(cardData) => $emit('algorithmsReview', cardData)"
      ></PicAlgorithm>
      <pic-row-mode
        v-if="modeType === 'imageRow'"
        v-ui-loading="{ tableData: cardList }"
        :card-list="cardList"
        :imgKey="imgKey"
        @artificialReview="(cardData) => $emit('artificialReview', cardData)"
        @algorithmsReview="(cardData) => $emit('algorithmsReview', cardData)"
      ></pic-row-mode>
      <ui-page
        v-if="isPage"
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="handlePage"
        @changePageSize="handlePageSize"
      >
      </ui-page>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Particular',
  props: {
    isPage: {
      default: true,
    },
    formData: {
      default: () => ({}),
    },
    formItemData: {
      default: () => [],
    },
    formItemSize: {
      type: String,
      default: 'width-md',
    },
    iconList: {
      default: () => [],
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    tableLoading: {
      default: false,
    },
    totalCount: {
      default: 0,
    },
    defaultPageData: {
      type: Object,
      default: () => {},
    },
    tabList: {
      type: Array,
      default: () => ['设备模式'],
    },
    cardList: {
      default: () => [],
    },
    imgKey: {},
    needResetCopySearch: {},
    // 查看图片 [兼容取值字段不一致问题]
    filedNameMap: {
      default: () => {
        return {
          smallPicName: 'facePath', // 小图
          bigPicName: 'scenePath', // 大图
          qualified: 'qualified', // 不合格
          description: 'reason', // 设备备注
          resultTip: 'resultTip', // 图片备注
        };
      },
    },
  },
  data() {
    return {
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableSlotList: [],
      modeType: 'device',
    };
  },
  created() {},
  methods: {
    changeMode(index, item) {
      // 0：设备模式，1：竖版的最初图片模式，2：后面加的横版图片模式
      let value = typeof item === 'object' ? item.value : index;
      // this.modeType = !item ? 'device' : 'image'
      switch (value) {
        case 0:
          this.modeType = 'device';
          break;
        case 1:
          this.modeType = 'image';
          break;
        case 2:
          this.modeType = 'imageRow';
          break;
        default:
          this.modeType = 'device';
      }
      this.$emit('changeMode', this.modeType);
    },
    handlePage(val) {
      if (this.isPage) {
        this.pageData.pageNum = val;
        this.$emit('handlePage', this.pageData);
      }
    },
    handlePageSize(val) {
      if (this.isPage) {
        this.pageData.pageNum = 1;
        this.pageData.pageSize = val;
        this.$emit('handlePageSize', this.pageData);
      }
    },
    assemblyContent(list) {
      list.forEach((row) => {
        if (row.slot) {
          this.tableSlotList.push(row.slot);
        }
        if (row.children) {
          this.assemblyContent(row.children);
        }
      });
    },
  },
  watch: {
    totalCount(val) {
      this.pageData.totalCount = val;
    },
    tableColumns: {
      handler(val) {
        this.assemblyContent(val);
      },
      immediate: true,
      deep: true,
    },
    defaultPageData: {
      handler(val) {
        this.pageData = Object.assign(this.pageData, val);
        this.pageData.totalCount = this.totalCount;
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    showPicAlgorithm() {
      let { indexType } = this.$route.query;
      return [
        'VEHICLE_URL_AVAILABLE',
        'VEHICLE_URL_AVAILABLE_IMPORTANT',
        'FACE_URL_AVAILABLE',
        'FACE_FOCUS_URL_AVAILABLE',
      ].includes(indexType);
    },
  },
  components: {
    IconStatics: require('@/components/icon-statics.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    PicRowMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-row-mode/index.vue').default,
    TagView: require('@/components/tag-view.vue').default,
    PicAlgorithm: require('@/views/governanceevaluation/evaluationoResult/components/pic-algorithm/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.icon-statics-wrapper {
  display: flex;
  justify-content: space-between;
  height: 50px;
  line-height: 50px;
  margin-bottom: 5px;
}
@{_deep} .other-button {
  align-items: center;
}
</style>
