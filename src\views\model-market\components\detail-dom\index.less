.dom-wrapper {
    position: relative;
    // padding: 10px 28px 25px 0;
    padding: 10px 28px 0px 0;
    height: 100%;
    .dom-box {
        width: 840px;
        height: 600px;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
        position: relative;
        &:before,
        &:after {
        content: '';
            display: block;
            border-width: 8px;
            position: absolute;
            bottom: -16px;
            left: 420px;
            border-style: solid dashed dashed;
            border-color: #ffffff transparent transparent;
            font-size: 0;
            line-height: 0;
        }
        > header {
            height: 36px;
            line-height: 36px;
            background: rgba(211, 215, 222, 0.3);
            box-shadow: inset 0px -1px 0px 0px #d3d7de;
            border-radius: 4px 4px 0px 0px;
            color: rgba(0, 0, 0, 0.9);
            font-weight: bold;
            font-size: 14px;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
        }

        .dom-content {
            padding: 10px 20px 0;
            font-size: 14px;
            height: calc(~'100% - 160px');
            &-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .dom-info {
                display: flex;
                margin-top: 10px;
                &-left {
                    position: relative;
                    width: 200px;
                    height: 200px;
                    >img {
                        width: 200px;
                        height: 200px;
                    }
                    .smallImg {
                        width: 200px;
                        height: 200px;
                        border: 1px solid #d3d7de;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-bottom: 10px;
                        background: #f9f9f9;
                        > img {
                            width: auto;
                            height: auto;
                            max-height: 200px;
                            max-width: 200px;
                        }
                    }
                    .similarity {
                        position: absolute;
                        left: 4px;
                        top: 4px;
                        padding: 0 10px;
                        height: 30px;
                        line-height: 30px;
                        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
                        border-radius: 4px;
                        font-size: 18px;
                        color: #fff;
                        text-align: center;
                    }
                    .traffic-record {
                        margin-top: 10px;
                    }
                    .personnel-files {
                        margin-top: 10px;
                        .complete-file {
                            height: 18px;
                            line-height: 18px;
                            margin: 15px 0 17px 0;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            >span {
                                margin-left: 5px;
                                font-size: 12px;
                            }
                        }
                    }
                }
                &-right {
                    width: calc( ~'100% - 240px' );
                    height: 400px;
                    border: 1px solid #d3d7de;
                    background: #f9f9f9;
                    .right-header {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        .tablist {
                            height: 28px;
                            line-height: 28px;
                            width: 400px !important;
                            margin: 0;

                            .ivu-tabs-tab {
                                float: left;
                                border: 1px solid #2c86f8;
                                border-right: none;
                                padding: 0 15px;
                                color: #2c86f8;
                                &:hover {
                                    background: #2c86f8;
                                    color: #ffffff;
                                }
                                &:first-child {
                                    border-top-left-radius: 4px;
                                    border-bottom-left-radius: 4px;
                                    border-right: none;
                                }
                                &:last-child {
                                    border-top-right-radius: 4px;
                                    border-bottom-right-radius: 4px;
                                    border-right: 1px solid #2c86f8;
                                }
                            }
                            .active {
                                background: #2c86f8;
                                color: #fff;
                            }
                        }
                    }
                    .right-content {
                        margin-top: 6px;
                        max-width: 580px;
                        height: 370px;
                        .complete-face {
                            width: 580px;
                            height: 430px;
                            position: relative;
                            text-align: center;
                            overflow: hidden;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            >img {
                                // width: 100%;
                                // height: 100%;
                            }
                            >span {
                                display: inline-block;
                                width: 100%;
                                height: 30px;
                                line-height: 30px;
                                position: absolute;
                                right: 0;
                                bottom: 0;
                                background: rgba(0, 0, 0, 0.5);

                                .iconfont {
                                    color: #fff !important;
                                    padding: 0 12px;
                                    cursor: pointer;
                                }
                            }
                        }
                        .video {
                            /deep/.easy-player {
                                margin-top: 60px;
                            }
                        }
                    }
                    margin-left: 15px;
                }
            }
        }

        > footer {
            border-top: 1px solid #d3d7de;
            padding: 0 20px;
            // height: 55px;
            // line-height: 55px;

            > span {
            color: #2c86f8;
            margin-right: 20px;
            cursor: pointer;
            }
        }
    }
}

.dom-content-p {
    margin-top: 6px;
    width: 100%;
    display: flex;
    height: 16px;
    line-height: 16px;
    .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 55px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
    }
    .message {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        // margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        // margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
    }
    .plateNo{
        cursor: pointer;
        color: #2C86F8;
    }
    .identity{
        cursor: pointer;
        color: #F29F4C; 
    }
}
.title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    height: 20px;
    width: 100%;
    display: flex;
    align-items: center;
    cursor: pointer;
    > span {
        color: rgba(0, 0, 0, 0.6);
        position: relative;
        margin-right: 34px;
    }
    .active {
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
    }
    .active:before {
        content: '';
        position: absolute;
        width: 56px;
        height: 3px;
        bottom: -3px;
        background: #2c86f8;
    }
    .num {
        font-weight: normal;
        > span {
        font-weight: normal;
        color: #2c86f8;
        }
    }
    .more {
        color: rgba(0, 0, 0, 0.35);
    }
}