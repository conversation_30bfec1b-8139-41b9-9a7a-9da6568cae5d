<template>
  <ui-modal ref="modal"
      v-model="visible"
      title="资源分类配置"
      class="modal"
      @onCancel="onCancel"
      @onOk="onOK">
    <div class="resource_layout">
      <div class="left">
        <ul v-if="menuList.length > 0">
          <li v-for="(item, index) in menuList" :key="index" :class="{active: currentIndex == index}" @click="selectMenu(item, index)">
            <div>{{item.title}}</div>
            <div>{{item.size}}</div>
          </li>
        </ul>
      </div>
      <div class="right">
        <Search @searchForm="searchForm" ref="search" />
        <div class="count">
          <div class="cancle" @click="cancleSelect()">取消选择</div>
          <div v-if="menuList.length > 0">
            <span class="m-r18">已选<span class="blue">（ {{menuList[currentIndex].sourceTableIds.length}} ）</span> 个</span>
          </div>
        </div>
        <Table 
          v-if="tableData.length > 0"
          class="auto-fill table" 
          :columns="columns" 
          :loading="loading" 
          :data="tableData" 
          @on-select="selectTable"
          @on-select-cancel="selectTableCancel"
          @on-select-all="selectTableAll"
          @on-select-all-cancel="selectTableAllCancel"
          max-height="380">
          <template #fieldNameCn="{ index }">
            <Input placeholder="请输入" v-model="tableData[index].fieldNameCn" maxlength="50" class="input-wid"></Input>
          </template>
        </Table>
        <!-- 分页 -->
        <ui-page 
          :current="pageInfo.pageNumber" 
          :total="pageInfo.total" 
          :page-size="pageInfo.pageSize" 
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
          >
          </ui-page>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { queryTableList, paramUpdate } from '@/api/config'
import { getSelectCatalogList } from '@/api/dataGovernance'
import Search from './search'
export default {
  name: 'UiConfirm',
  components: {
    Search
  },
  props: {
    
  },
  data () {
    return {
      visible: false,
      loading: false,
      currentIndex: 0,
      menuList: [
        // {title: '人', size: 10},
        // {title: '物', size: 12},
        // {title: '事', size: 20},
        // {title: '组织', size: 30},
        // {title: '地', size: 50},
        // {title: '其它', size: 20},
      ],
      tableData: [],
      selectTableData: [],
      allselectData: [],
      columns: [
        { align: 'center', width: 60, type: 'selection'},
        { title: '序号', align: 'center', width: 70, type: 'index', key: 'index' },
        { title: '表名称', key: 'sourceTable' },
        { title: '表注释', key: 'sourceTableCn' },
      ],
      pageInfo: {
        pageNumber: 1,
        pageSize: 10,
        total: 0
      },
      single: false,
    }
  },
  watch: {
    selectTableData(newV, oldV) {
      console.log('监听已选中的表格', newV, oldV)
      var row = this.menuList[this.currentIndex]
      // row.size = newV.length
      row.size = this.menuList[this.currentIndex].sourceTableIds.length
      this.$forceUpdate()
    }
  },
  computed: {
    ...mapGetters({
      searchTypeList: 'dictionary/getSearchTypeList', //检索类型
    })
  },
  created () { },
  methods: {
    ...mapActions({
    }),
    // 初始化
    async show(row) {
      if (row.paramValue) {
        this.allselectData = JSON.parse(row.paramValue).tableResourceCatalogConfig || []
        // console.log('已有数据', this.allselectData)
      }
      // var list = JSON.parse(row.paramValue).tableResourceCatalogConfig
      // console.log('this.oldData', list)
      await this.queryTableList()
      await this.getSelectCatalogList(this.allselectData)
      this.visible = true
    },
    /**
     * 左侧目录
     * @param {*} arr 
     */
    getSelectCatalogList(arr) {
      this.menuList = []
      var _this = this
      getSelectCatalogList({}).then(res => {
        // console.log('目录列表', res, arr)
        let list = res.data
        list.forEach(item => {
          let num = 0
          let ids = []
          if (arr.length > 0) {
            var row = arr.find(it => { return it.catalogId == item.id})
            if (row) {
              num = row.size
              ids = row.sourceTableIds
            }
          }
          var obj = {
            catalogId: item.id,
            title: item.catalogName,
            size: num,
            sourceTableIds: ids
          }
          _this.menuList.push(obj)
        })
        // console.log('初始化后menuList', _this.menuList)
        _this.selectMenu(this.menuList[0], 0)
      })
    },
    /**
     * 右侧表格
     */
    async queryTableList() {
      var param = {
        ...this.pageInfo, 
        ...this.$refs.search.form,
        sourceType: '1'
      };

      await queryTableList(param).then(res => {
        // console.log('数据表查询结果', res)
        this.tableData = res.data.entities
        this.pageInfo.total = res.data.total
        this.tableSelectStatus()

      })
    },
    init () {
      this.visible = true
    },
    onCancel () {
      this.tableData = []
      this.selectTableData = []
      this.visible = false

    },
    onOK () {
      // this.visible = false
      // console.log('table数据', this.tableData, this.menuList)
      var param = {
        paramKey: 'ICBD_RESOURCE_CATALOG_CONFIG',
        paramType: "icbd",
        paramValue: JSON.stringify({tableResourceCatalogConfig: this.menuList})
      }
      paramUpdate(param).then(res => {
        this.$Message.success('修改成功')
        this.visible = false
        this.$emit('refresh');
      })
    },
    /**
     * 表格选中其中一项
     */
    selectTable(selection, row) {
      // console.log('表格选中其中一项', selection, row)
      this.selectTableData = selection
      // this.menuList[this.currentIndex].sourceTableIds = selection.map(item => { return item.sourceTableId})
      // console.log('表格选中其中一项', this.menuList[this.currentIndex])
      this.menuList[this.currentIndex].sourceTableIds.push(row.sourceTableId)
    },
    /**
     * 表格选中其中一项取消
     */
    selectTableCancel(selection, row) {
      // console.log('表格选中其中一项', selection, row)
      this.selectTableData = selection
      // this.menuList[this.currentIndex].sourceTableIds = selection.map(item => { return item.sourceTableId})
      this.menuList[this.currentIndex].sourceTableIds = this.menuList[this.currentIndex].sourceTableIds.filter(item => {return item != row.sourceTableId})
    },
    /**
     * 表格全选
     */
    selectTableAll(selection) {
      // console.log('表格全选', selection)
      this.selectTableData = selection
      // this.menuList[this.currentIndex].sourceTableIds = selection.map(item => { return item.sourceTableId})
      var ids = this.menuList[this.currentIndex].sourceTableIds
      selection.forEach(item => {
        if (!ids.includes(item.sourceTableId)) {
          this.menuList[this.currentIndex].sourceTableIds.push(item.sourceTableId)
        }
      })
    },
    /**
     * 表格全选取消
     */
    selectTableAllCancel(selection) {
      console.log('表格取消全选', selection)
      this.selectTableData = selection
      // this.menuList[this.currentIndex].sourceTableIds = []
      this.tableData.forEach(item => {
        this.menuList[this.currentIndex].sourceTableIds = this.menuList[this.currentIndex].sourceTableIds.filter(i => {return i != item.sourceTableId})
      })
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size
      this.queryTableList()
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.queryTableList()
    },
    /**
     * 切换左侧菜单
     */
    selectMenu (row, index) {
      this.currentIndex = index;
      // console.log('左侧菜单变化', row, index)
      this.$nextTick(() => {
        this.selectTableData = []
        var list = JSON.parse(JSON.stringify(this.tableData))
        this.tableData = []
        if (row.sourceTableIds.length > 0) {
          list.forEach(item => {
            if (row.sourceTableIds.includes(item.sourceTableId)) {
              item._checked = true;
              this.selectTableData.push(item);
            }else{
              item._checked = false;
            }
          })
        }else{
          list.forEach(item => {
            item._checked = false;
          })
        }
        this.$forceUpdate()
        this.tableData = list
  
        // console.log('this.tableData', this.tableData)

      })
    },
    /**
     * 查询
     * @param {*} search 
     */
    searchForm(search) {
      // console.log('查询', search)
      this.queryTableList()
    },
    /**
     * 设置table选中状态
     */
    tableSelectStatus() {
      if (this.menuList.length == 0) {
        return
      }
      var ids = this.menuList[this.currentIndex].sourceTableIds
      this.tableData.forEach(item => {
        if (ids.includes(item.sourceTableId)) {
          item._checked = true;
        }else{
          item._checked = false;
        }
      })
    },
    cancleSelect() {
      this.menuList.forEach(item => {
        item.sourceTableIds = []
        item.size = 0
      })
      this.tableSelectStatus()
    }
  },
  beforeDestroy () {
  }
}
</script>

<style lang="less" scoped>
.modal {
  /deep/.ivu-modal {
    width: 1300px !important;
  }
  .content-wrapper {
    height: 600px;
    width: 1270px;
    .map {
      width: 100%;
      height: 100%;
    }
  }
  .search-input {
    width: 400px;
    margin-bottom: 10px;
    /deep/.ivu-input {
      width: 400px;
    }
    /deep/.ivu-icon-ios-search {
      color: #fff;
    }
  }
}

.resource_layout {
  display: flex;
  .left {
    width: 200px;
    border: 1px solid #ddd;
    ul {
      li {
        display: flex;
        justify-content: space-between;
        padding: 6px 20px;
        cursor: pointer;
      }
      .active {
        background: #2c86f8;
        color: #fff;
      }
    }
  }
  .right {
    flex: 1;
    border: 1px solid #ddd;
    margin-left: -1px;
    padding: 10px;
  }
}
.count {
  border: 1px solid #2C86F8;
  background: #e8f2ff;
  margin-bottom: 20px;
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  .cancle{
    float: right;
    height: 36px;
    color: #2C86F8;
    cursor: pointer;
  }
  .m-r18{
    margin-right: 18px;
  }
  .blue {
    color: #2C86F8;
  }
}
</style>
