import request from "@/libs/request";
import { monographic } from "../Microservice";
import { compus as domainName } from "./base";

// 校园管控-陌生人员发现分页列表
export function strangerPageList (data) {
    return request({
      url: monographic + domainName + '/schoolControl/strangerPageList',
      method: 'POST',
      data: data
    })
  }
  
  // 校园管控-获取频繁出没学校的陌生人列表
  export function getFrequentStrangerPersonList (data) {
    return request({
      url: monographic + domainName + '/schoolControl/getFrequentStrangerPersonList',
      method: 'POST',
      data: data
    })
  }
  
  // 校园管控-获取频繁出没学校的陌生人详情
  export function getFrequentStrangerPersonDetailList (data) {
    return request({
      url: monographic + domainName + '/schoolControl/getFrequentStrangerPersonDetailList',
      method: 'POST',
      data: data
    })
  }