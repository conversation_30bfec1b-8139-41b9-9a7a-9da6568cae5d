<template>
  <Modal v-model="visible" title="详情" width="1010" @onCancel="onCancel">
    <div ref="personnelThematic" class="personnel-thematic-database">
      <Form
        ref="personnelForm"
        :model="personnelForm"
        inline
        class="personnel-form"
      >
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">基本信息</div>
        </div>
        <div class="information-form essential-form">
          <FormItem prop="idPhotoList" class="img-formitem">
            <div class="essential-information-img">
              <div class="avatar-img card-border-color">
                <img
                  v-if="!previewImg"
                  src="@/assets/img/empty-page/null_img_icon.png"
                  class="avatar-null-img"
                  alt
                />
                <img v-else v-viewer :src="previewImg.photoUrl" alt />
              </div>
              <!-- <UploadImg
                choosed
                :isEdit="true"
                :choosedIndex="avatarIndex"
                :defaultList="personnelForm.photoUrlList"
                class="upload-img"
                @on-choose="chooseHandle"
              /> -->
            </div>
          </FormItem>
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="userName">
                <div slot="label"><span class="label-text">姓名</span>:</div>
                <div class="label-value input-200">
                  {{ personnelForm.name }}
                </div>
              </FormItem>
              <FormItem prop="gender">
                <div slot="label"><span class="label-text">性别</span>:</div>
                <div class="label-value input-200">
                  {{ personnelForm.sex === "1" ? "男" : "女" }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="idCardNoType" label="证件类型:">
                <div class="label-value input-200">
                  {{
                    personnelForm.idCardNoType
                      | commonFiltering(identityTypeList)
                  }}
                </div>
              </FormItem>
              <FormItem prop="identificationNumber" label="证件号码:">
                <div class="label-value input-200">
                  {{ personnelForm.idCardNo }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="contactNumber" label="联系电话:">
                <div class="label-value input-200">
                  {{ personnelForm.phoneNo }}
                </div>
              </FormItem>
              <FormItem prop="nation">
                <div slot="label"><span class="label-text">民族</span>:</div>
                <div class="label-value input-200">
                  {{ personnelForm.national | commonFiltering(nationTypeList) }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="religiousBelief" label="宗教信仰:">
                <div class="label-value input-200">
                  {{ personnelForm.religious }}
                </div>
              </FormItem>
              <FormItem prop="nativePlace">
                <div slot="label"><span class="label-text">籍贯</span>:</div>
                <div class="label-value input-200">
                  {{ personnelForm.nativePlace }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="marriageStatus" label="婚姻状态:">
                <div class="label-value input-200">
                  {{
                    personnelForm.marriageStatus | commonFiltering(marriageList)
                  }}
                </div>
              </FormItem>
              <FormItem prop="bloodType">
                <div slot="label"><span class="label-text">血型</span>:</div>
                <div class="label-value input-200">
                  {{ personnelForm.bloodType }}
                </div>
              </FormItem>
            </div>
            <!-- <div class="info-item">
              <FormItem prop="fugitiveNo" label="在逃编号:">
                <div class="label-value input-200">{{personnelForm.fugitiveNo}}</div>
              </FormItem>
              <FormItem prop="degreeEducation" label="文化程度:">
                <div class="label-value input-200">{{personnelForm.degreeEducation | commonFiltering([])}}</div>
              </FormItem>
            </div> -->
            <div class="info-item">
              <FormItem prop="position">
                <div slot="label"><span class="label-text">职位</span>:</div>
                <div class="label-value input-200">
                  {{ personnelForm.professional }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="registeredResidenceAddress" label="户籍地址:">
                <div class="label-value input-520">
                  {{ personnelForm.registeredResidence }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="residentialAddress" label="居住地址:">
                <div class="label-value input-520">
                  {{ personnelForm.address }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="bizLabelVos">
                <div slot="label"><span class="label-text">标签</span>:</div>
                <div class="label-value input-520">
                  <LabelList :labelList="personnelForm.bizLabelVos"></LabelList>
                </div>
              </FormItem>
            </div>
          </div>
        </div>
        <!-- <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">车辆信息</div>
        </div>
        <div class="information-form vehicle-form">
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="licensePlate" label="车牌号码:" class="license-plate-number">
                <div class="label-value input-200">{{personnelForm.licensePlate}}</div>
              </FormItem>
              <FormItem prop="licensePlateColor" label="车牌颜色:" class="license-plate-color">
                <div class="label-value input-200">{{personnelForm.licensePlateColor | commonFiltering(licensePlateColorList)}}</div>
              </FormItem>
              <FormItem prop="vehicleColor" label="车辆颜色:">
                <div class="label-value input-200">{{personnelForm.vehicleColor | commonFiltering(bodyColorList)}}</div>
              </FormItem>
            </div>
          </div>
        </div> -->
        <!-- <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">其他信息</div>
        </div> -->
        <!-- <div class="information-form other-form">
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="RFIDNumber" label="RFID号码:" class="rfid-number">
                <div class="label-value input-200">{{personnelForm.RFIDNumber}}</div>
              </FormItem>
              <FormItem prop="MACNumber" label="MAC地址:" class="mac-number">
                <div class="label-value input-200">{{personnelForm.MACNumber}}</div>
              </FormItem>
              <FormItem prop="IMEINumber" label="IMEI号码:">
                <div class="label-value input-200">{{personnelForm.IMEINumber}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="IMSINumber" label="IMSI号码:" class="rfid-number">
                <div class="label-value input-200">{{personnelForm.IMSINumber}}</div>
              </FormItem>
              <FormItem prop="remark">
                <div slot="label"><span class="label-text">备注</span>:</div>
                <div class="label-value input-520">{{personnelForm.remark}}</div>
              </FormItem>
            </div>
          </div>
        </div> -->
      </Form>
    </div>
    <template #footer>
      <div class="footer-btn">
        <Button type="primary" @click="onCancel">关闭</Button>
      </div>
    </template>
  </Modal>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import UploadImg from "@/components/ui-upload-img-static-library";
import LabelList from "@/views/juvenile-data-storage/special-library/personnel-thematic-database/components/label-list.vue";

export default {
  components: {
    UploadImg,
    LabelList,
  },
  props: {
    // 当前库对象
    currentRow: {
      type: Object,
      default: {},
    },
    // 证件类型字典
    identityTypeList: {
      type: Array,
      default: [],
    },
    // 民族类型字典
    nationTypeList: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      visible: false,
      avatarIndex: "",
      personnelForm: {
        idPhotoList: [],
        userName: "",
        gender: "",
        identificationType: "",
        identificationNumber: "",
        contactNumber: "",
        nation: "",
        religiousBelief: "",
        nativePlace: "",
        maritalStatus: "",
        bloodType: "",
        fugitiveNo: "",
        degreeEducation: "",
        position: "",
        registeredResidenceAddress: "",
        residentialAddress: "",
        licensePlate: "",
        licensePlateColor: "",
        vehicleColor: "",
        RFIDNumber: "",
        MACNumber: "",
        IMEINumber: "",
        IMSINumber: "",
        remark: "",
      },
      previewImg: "",
    };
  },
  computed: {
    ...mapGetters({
      cardTypeList: "dictionary/getCardTypeList", //证件类型
      marriageList: "dictionary/getMarriageList", //婚姻状态
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
    }),
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    show(item) {
      this.visible = true;
      this.previewImg = "";
      this.avatarIndex = "";
      this.$nextTick(() => {
        this.$refs.personnelThematic.scrollTop = 0;
        this.$refs.personnelForm.resetFields();
        this.avatarIndex = 0;
        this.personnelForm = JSON.parse(JSON.stringify(item));
        this.previewImg = item.photoUrlList ? item.photoUrlList[0] : "";
        this.$forceUpdate();
      });
    },
    // 选择证件照
    chooseHandle(item) {
      this.previewImg = item;
    },
    // 确认
    onCancel() {
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.input-200 {
  width: 200px;
}
.input-520 {
  width: 520px;
}
/deep/ .ivu-modal-body {
  padding: 0 !important;
}
.personnel-thematic-database {
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  // height: 680px;
  .personnel-form {
    padding: 0 30px;
    box-sizing: border-box;
    .form-item-title {
      display: flex;
      align-items: center;
      padding-bottom: 4px;
      border-bottom: 1px solid #fff;
      .title-line {
        width: 3px;
        height: 16px;
        margin-right: 6px;
      }
      .title-text {
        font-size: 14px;
        line-height: 20px;
        font-weight: bold;
        font-family: "MicrosoftYaHei-Bold";
      }
    }
    .information-form {
      display: flex;
      justify-content: space-between;
      margin: 20px 0 30px 0;
      .essential-information-img {
        width: 240px;
        margin-right: 64px;
        display: flex;
        flex-direction: column;
        .avatar-img {
          width: 240px;
          height: 320px;
          border: 1px solid #fff;
          & > img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: pointer;
          }
          .avatar-null-img {
            cursor: unset;
          }
        }
        .upload-img {
          margin-top: 10px;
          justify-content: flex-start;
          /deep/ .upload-item {
            width: 54px;
            height: 54px;
            .ivu-icon-ios-add {
              font-size: 30px;
              font-weight: bold;
            }
            .upload-text {
              line-height: 18px;
              display: none;
            }
          }
        }
      }
      .information-body {
        flex: 1;
        .info-item {
          display: flex;
          justify-content: space-between;
          /deep/ .ivu-form-item {
            display: inline-flex;
            margin-right: 0;
            margin-bottom: 10px;
            .ivu-form-item-label {
              display: flex;
              align-items: center;
              justify-content: end;
              padding-right: 10px;
              white-space: nowrap;
              .label-text {
                text-align-last: justify;
                display: inline-block;
              }
            }
            .label-value {
              height: 34px;
              display: flex;
              align-items: center;
            }
            .ivu-form-item-label::before {
              margin: 0;
            }
            .ivu-radio-wrapper {
              margin-right: 30px;
            }
          }
        }
      }
    }
    .essential-form {
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 80px !important;
          .label-text {
            width: 58px;
          }
        }
      }
      .img-formitem {
        margin: 0 !important;
      }
    }
    .vehicle-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
        }
      }
      .info-item {
        justify-content: unset !important;
        .license-plate-number {
          margin-right: 36px !important;
        }
        .license-plate-color {
          margin-right: 46px !important;
        }
      }
    }
    .other-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
          .label-text {
            width: 58px;
          }
        }
      }
      .info-item {
        justify-content: unset !important;
        .rfid-number {
          margin-right: 36px !important;
        }
        .mac-number {
          margin-right: 46px !important;
        }
      }
    }
  }
}
.footer-btn {
  text-align: center;
}
/deep/ .ivu-modal-header {
  background: rgba(211, 215, 222, 0.29);
}
</style>
