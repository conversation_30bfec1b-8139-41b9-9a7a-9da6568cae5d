<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template #isImportant="{ row }">
        <span>{{ isImportant(row) }}</span>
      </template>
      <template #qualified="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template slot="option" slot-scope="{ row }">
        <ui-btn-tip
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
          class="mr-sm"
        ></ui-btn-tip>
      </template>
    </Particular>
    <!-- 播放视频   -->
    <video-player
      v-model="videoVisible"
      :video-url="videoUrl"
      :video-loading="videoLoading"
      :playDeviceCode="playDeviceCode"
      @onCancel="videoUrl = ''"
    ></video-player>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import vedio from '@/config/api/vedio-threm';
import { normalFormData, FaceTableColumns, iconStaticsList } from './util/enum/ReviewParticular.js';

export default {
  mixins: [particularMixin, dealWatch],
  inheritAttrs: false,
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      iconList: iconStaticsList,
      formData: {
        deviceId: '',
        deviceName: '',
        qualified: '',
        checkStatus: null,
        isImportant: null,
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(FaceTableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      exportLoading: false,
      // 播放视频
      videoVisible: false,
      videoLoading: false,
      playDeviceCode: '',
      videoUrl: '',
    };
  },
  created() {
    // 异常原因下拉列表获取
    this.MixinDisQualificationList().then((data) => {
      let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
      findErrorCodes.options = data.map((item) => {
        return { value: Number(item.key), label: item.value };
      });
    });
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    isImportant(row) {
      let val = Number(row.isImportant);
      return val === 1 ? '重点设备' : '普通设备';
    },
    initAll() {
      // 列表
      this.getTableData();
      // 统计获取
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      Object.assign(this.formData, params);
      this.getTableData();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        this.totalCount = data.total;
      });
    },
    //视频播放
    async clickRow(row) {
      try {
        this.videoLoading = true;
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {
          deviceId: row.deviceId,
        };
        let res = await this.$http.post(vedio.getplay, data);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      } finally {
        this.videoLoading = false;
      }
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
  computed: {
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    ExportData: require('../components/export-data').default,
    VideoPlayer: require('@/views/governanceevaluation/evaluationoResult/components/video-player.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}

.sucess {
  color: @color-success;
}

.error {
  color: @color-failed;
}

.calender-tip {
  width: 334px !important;
}
</style>
<style lang="less">
.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }
}
</style>
