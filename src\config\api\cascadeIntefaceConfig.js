const basePre = '/ivdg-asset-app';
export default {
  // 上级级联接口
  supInterfaceAdd: basePre + '/reportSuperiorIntefaceConfig/add',
  supInterfaceList: basePre + '/reportSuperiorIntefaceConfig/pageList',
  supInterfaceDel: basePre + '/reportSuperiorIntefaceConfig/remove/',
  supInterfaceUpdate: basePre + '/reportSuperiorIntefaceConfig/update',
  supInterfaceInfo: basePre + '/reportSuperiorIntefaceConfig/view/',
  // 下级级联接口
  subInterfaceAdd: basePre + '/receiveSubordinateIntefaceConfig/add',
  subInterfaceList: basePre + '/receiveSubordinateIntefaceConfig/pageList',
  subInterfaceDel: basePre + '/receiveSubordinateIntefaceConfig/remove/',
  subInterfaceUpdate: basePre + '/receiveSubordinateIntefaceConfig/update',
  subInterfaceInfo: basePre + '/receiveSubordinateIntefaceConfig/view/',
};
