<!--
    * @FileDescription: 格灵
    * @Author: H
    * @Date: 2023/09/8
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="box">
        <div class="data-export" v-if="queryParam.imageBase">
            <Button class="mr" :type="paramOrder == 'similarity' ? 'primary' : 'default'" @click="handleSort('similarity')" size="small">
                <Icon type="md-arrow-round-down" v-if="!similUpDown"/> 
                <Icon type="md-arrow-round-up" v-else/>
                相似度排序
            </Button>
        </div>
        <div class="list-geling">
            <listCard v-for="(item, index) in dataList" :key="index" :item="item"></listCard>
            <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
            <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
            <ui-loading v-if="loading"></ui-loading>
        </div>
        <!-- 分页 -->
        <ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[8, 16, 24, 32]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
    </div>
</template>

<script>
import listCard from './list-card.vue';
import { faceIdentity } from '@/api/modelMarket';
export default {
    name: '',
    components:{
        listCard    
    },
    props:{
        queryParam: {
            type: Object,
            default: () =>{
                return {}
            }
        }
    },
    data () {
        return {
            dataList: [],
            pageInfo: {
                pageNumber: 1,
                pageSize: 8,
            },
            total: 0,
            loading: false, 
            similUpDown: false,
            paramOrder: 'desc'
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        // 排序
        handleSort(val){
            this.similUpDown = !this.similUpDown
            this.paramOrder = this.similUpDown ? 'asc' : 'desc';
            this.queryList()
        },
        queryList() {
            this.moreBox = true;
            this.loading = true;
            let faceLibIds = this.queryParam.faceLibIds.map(item => item.id);
            let params = {
                idCardNo: this.queryParam.idCardNo ,
                imageBase: this.queryParam.imageBase,
                name: this.queryParam.name,
                national: this.queryParam.national,
                sex: this.queryParam.sex,
                similarity: this.queryParam.similarity /100,
                nativePlace: this.queryParam.nativePlace,
                userId: this.userInfo.id,
                ...this.pageInfo,
                faceLibIds:faceLibIds,
                algorithmVendorType: this.queryParam.algorithmVendorType[1],
                order: this.paramOrder,
                sortField: 'similarity'
            }
            faceIdentity(params)
            .then(res => {
                this.loading = false;
                this.moreBox = false;
                this.dataList = res.data.entities || [];
                this.total = res.data.total || 0;
            })
        },
        // 页数改变
        pageChange(size) {
            this.pageInfo.pageNumber = size
            this.queryList()
        },
        // 页数量改变
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1
            this.pageInfo.pageSize = size
            this.queryList()
        },
    }
}
</script>

<style lang='less' scoped>
.box{
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .list-geling{
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-wrap: wrap;
        justify-content: start;
        align-content: flex-start;
        position: relative;
        /deep/.gerling{
            background: #f29f4c;
        }
    }
    .pages{
        padding: 0 20px;
    }
    .data-export{
        margin: 5px 10px;
    }
}
</style>
