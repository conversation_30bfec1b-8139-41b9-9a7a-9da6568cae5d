<template>
  <!-- 建档 -->
  <div class="bookbuilding" ref="contentScroll">
    <div class="information-header">
      <div class="information-echart">
        <div class="sxj-echart">
          <draw-echarts
            :echart-option="sxjEchart"
            :echart-style="sxjStyle"
            ref="sxjChart"
            class="sxj-chart"
          ></draw-echarts>
          <div class="monitoring-data">
            <i class="icon-font f-16 icon-shitujichushuju monitoring-icon sxj-color"></i>
            <div class="monitoring-content">
              <div class="superior">
                上级视频监控建档：<span>{{ statisticalList.biParentVideoCount }}</span>
              </div>
              <div class="same-level">
                本级视频监控建档：<span>{{ statisticalList.biVideoCount }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="ride"><span class="mb_">×</span></div>
        <div class="rl-echart">
          <draw-echarts
            :echart-option="rlEchart"
            :echart-style="sxjStyle"
            ref="sxjChart"
            class="sxj-chart"
          ></draw-echarts>
          <div class="monitoring-data">
            <i class="icon-font f-16 icon-renliankakou monitoring-icon rl-color"></i>
            <div class="monitoring-content">
              <div class="superior">
                上级人脸卡口建档：<span>{{ statisticalList.biParentFaceCount }}</span>
              </div>
              <div class="same-level">
                本级人脸卡口建档：<span>{{ statisticalList.biFaceCount }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="ride"><span class="mb_">×</span></div>
        <div class="cl-echart">
          <draw-echarts
            :echart-option="clEchart"
            :echart-style="sxjStyle"
            ref="sxjChart"
            class="sxj-chart"
          ></draw-echarts>
          <div class="monitoring-data">
            <i class="icon-font f-16 icon-cheliangkakou monitoring-icon cl-color"></i>
            <div class="monitoring-content">
              <div class="superior">
                上级车辆卡口建档：<span>{{ statisticalList.biParentVehicleCount }}</span>
              </div>
              <div class="same-level">
                本级车辆卡口建档：<span>{{ statisticalList.biVehicleCount }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="ride"><span>=</span></div>

        <div class="filing-content">
          <div class="filing-rate">
            <i class="icon-font f-50 icon-jiandangshuai filing-icon"></i>
            <div class="filing-content">
              <div class="superior">建档率</div>
              <div class="same-level">{{ statisticalList.resultValueFormat }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="btns fl">
          <Button class="btn_jk" :class="{ active: sbgnlx === 1 }" @click="changeBtn(1)"> 未建档视频监控 </Button>
          <Button class="btn_kk" :class="{ active: sbgnlx === 2 }" @click="changeBtn(2)"> 未建档车辆卡口库 </Button>
          <Button class="btn_k" :class="{ active: sbgnlx === 3 }" @click="changeBtn(3)"> 未建档人脸卡口 </Button>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <!-- <ui-select-tabs
        class="list_item"
        :list="selectTabs"
        @selectInfo="selectInfo"
      ></ui-select-tabs> -->
      <div class="list">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
          <template #longitude="{ row }">
            <span>{{ row.longitude | filterLngLat }}</span>
          </template>
          <template #latitude="{ row }">
            <span>{{ row.latitude | filterLngLat }}</span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip
              icon="icon-shebeidangan"
              content="设备档案"
              class="mr-sm"
              @click.native="deviceArchives(row)"
            ></ui-btn-tip>
            <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page"
        :page-data="searchData"
        @changePage="changePage"
        @changePageSize="changePageSize"
        :pageShow="false"
      ></ui-page>
    </div>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less" scoped>
.bookbuilding {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .navBarWrap {
    position: sticky !important;
    top: 100px;
    width: 100%;
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      //min-height: 350px !important;
      min-height: calc(100vh - 580px) !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;

    .information-echart {
      display: flex;
      width: calc(100% - 350px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 10px;

      .sxj-color {
        color: #6ab0de;
      }
      .rl-color {
        color: #a46ade;
      }
      .cl-color {
        color: #e97575;
      }
      .monitoring-data {
        .monitoring-icon {
          display: inline-block;
          height: 40px;
          width: 40px;
          line-height: 40px;
          text-align: center;
          background: #0f3668;
          vertical-align: middle;
        }
        .monitoring-content {
          display: inline-block;
          height: 40px;
          flex: 1;
          margin-left: 7px;
          text-align: left;
          vertical-align: middle;
          .superior {
            font-size: 12px;
            height: 20px;
            line-height: 20px;
            color: #f5f5f5;
            span {
              color: #19d5f6;
            }
          }
          .same-level {
            color: #f5f5f5;
            height: 20px;
            line-height: 20px;
            font-size: 12px;
            span {
              color: #19d5f6;
            }
          }
        }
      }
      .ride {
        width: 100px !important; /*no*/
        // flex: 1;
        height: 100%;
        position: relative;
        // text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        // line-height: 160px;
        .mb_ {
          margin-bottom: 50px;
        }
        span {
          font-size: 50px;
          color: var(--color-primary);
          // position: absolute;
          // top: 88px !important; /*no*/
          // left: 70px !important; /*no*/
        }
      }

      .sxj-echart {
        width: 270px !important; /*no*/
        text-align: -webkit-center;
        margin-left: 40px;
      }

      .rl-echart {
        width: 270px !important; /*no*/
        text-align: -webkit-center;
      }
      .cl-echart {
        width: 270px !important; /*no*/
        text-align: -webkit-center;
      }
      @media screen and (max-width: 1366px) {
        .monitoring-data {
          .monitoring-icon {
            display: inline-block;
            height: 40px; /*no*/
            width: 40px; /*no*/
            line-height: 40px; /*no*/
            text-align: center;
            background: #0f3668;
            vertical-align: middle;
          }
          .monitoring-content {
            display: inline-block;
            height: 40px;
            flex: 1;
            margin-left: 7px;
            text-align: left;
            vertical-align: middle;
            .superior {
              font-size: 12px;
              height: 20px;
              line-height: 20px;
              color: #f5f5f5;
              span {
                color: #19d5f6;
              }
            }
            .same-level {
              color: #f5f5f5;
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              span {
                color: #19d5f6;
              }
            }
          }
        }
        .sxj-echart {
          width: 230px !important; /*no*/
          text-align: -webkit-center;
        }

        .rl-echart {
          width: 225px !important; /*no*/
          text-align: -webkit-center;
        }
        .cl-echart {
          width: 225px !important; /*no*/
          text-align: -webkit-center;
        }

        .ride {
          width: 50px !important; /*no*/
          height: 100%;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          span {
            font-size: 50px;
            color: var(--color-primary);
          }
        }
      }
      .filing-content {
        display: flex;
        justify-content: center;
        align-items: center;
        .filing-rate {
          vertical-align: middle;
          width: 238px;
          height: 112px;
          line-height: 112px;
          background: #05316b;
          display: inline-block;
          justify-content: center;
          align-items: center;

          .filing-icon {
            display: inline-block;
            height: 50px;
            width: 50px;
            font-size: 40px;
            line-height: 50px;
            text-align: center;
            color: #19c176;
            vertical-align: middle;
            margin-left: 42px;
          }
          .filing-content {
            display: inline-block;
            height: 50px;
            flex: 1;
            margin-left: 7px;
            text-align: left;
            vertical-align: middle;
            .superior {
              font-size: 14px;
              height: 20px;
              line-height: 20px;
              color: #f5f5f5;
            }
            .same-level {
              color: #19c176;
              height: 20px;
              line-height: 20px;
              font-size: 18px;
              font-family: Microsoft YaHei;
              font-weight: bold;
            }
          }
        }
      }
    }
    .information-ranking {
      width: 350px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .btns {
        .btn_jk {
          border-right: none !important;
        }
        .btn_kk {
          border-right: none !important;
        }

        button {
          color: #56789c;
          background-color: #12294e;
          border-radius: 0;
          &:hover {
            color: #fff !important;
            background-color: rgba(43, 132, 226, 1);
          }
        }
        .active {
          color: #fff;
          background-color: rgba(43, 132, 226, 1);
        }
      }
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [downLoadTips],
  name: 'bookbuilding',
  data() {
    return {
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          width: 150,
        },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 120 },
        {
          title: '数据来源',
          key: 'sourceIdText',
          minWidth: 150,
          tooltip: true,
        },
        { title: '安装地址', key: 'address', minWidth: 150, tooltip: true },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        pageAction: 1,
        firstId: null,
        lastId: null,
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      sbgnlx: 1,
      sxjEchart: {},
      rlEchart: {},
      clEchart: {},
      sxjStyle: {
        width: '180px',
        height: '180px',
      },
      sxjChartDatas: [
        {
          indexName: '视频监控建档率',
          resultValue: '0',
        },
      ],
      rlChartDatas: [
        {
          indexName: '人脸卡口建档率',
          resultValue: '0',
        },
      ],
      clChartDatas: [
        {
          indexName: '车辆卡口建档率',
          resultValue: '0',
        },
      ],
      statisticalList: {},
      rankData: [],
      paramsList: {},
      ranLoading: false,
      contentClientHeight: 0,
      copyPage: 0,
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  activated() {},
  created() {},
  async mounted() {
    this.getTagList();
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 100 * proportion : 0;
    await this.sxjInitChart(); //sxj echarts
    await this.rlInitChart(); //rl echarts
    await this.clInitChart(); //cl echarts},
  },

  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: { sbgnlx: this.sbgnlx },
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 头部echarts
    sxjInitChart() {
      this.sxjChartDatas[0].resultValue = this.statisticalList.biVideoResultValue || '0%';
      const colorEnum = ['#6AB0DE', '#204972'];
      this.sxjChartDatas.map((row) => {
        let one = {
          title: row.indexName,
          subTitle: row.resultValue,
          data: [{ value: row.resultValue }],
          color: colorEnum,
        };
        this.sxjEchart = this.$util.doEcharts.evaluationoverrRate(one);
      });
    },
    rlInitChart() {
      this.rlChartDatas[0].resultValue = this.statisticalList.biFaceResultValue || '0%';
      const colorEnum = ['#A46ADE', '#2F3772'];
      this.rlChartDatas.map((row) => {
        let one = {
          title: row.indexName,
          subTitle: row.resultValue,
          data: [{ value: row.resultValue }],
          color: colorEnum,
        };
        this.rlEchart = this.$util.doEcharts.evaluationoverrRate(one);
      });
    },
    clInitChart() {
      this.clChartDatas[0].resultValue = this.statisticalList.biVehicleResultValue || '0%';
      const colorEnum = ['#E97575', '#413a57'];
      this.clChartDatas.map((row) => {
        let one = {
          title: row.indexName,
          subTitle: row.resultValue,
          data: [{ value: row.resultValue }],
          color: colorEnum,
        };
        this.clEchart = this.$util.doEcharts.evaluationoverrRate(one);
      });
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        let data = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: { sbgnlx: this.sbgnlx },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          pageAction: this.searchData.pageAction,
          firstId: this.searchData.firstId,
          lastId: this.searchData.lastId,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, data);
        const datas = res.data.data || [];
        this.tableData = datas.entities || [];
        if (this.tableData.length != 0) {
          this.searchData.firstId = this.tableData[0].id;
          this.searchData.lastId = this.tableData[this.tableData.length - 1].id;
        }
        this.searchData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },

    changeBtn(val) {
      this.sbgnlx = val;
      this.searchData.pageNum = 1;
      this.searchData.pageSize = 20;
      this.searchData.firstId = null;
      this.searchData.lastId = null;
      this.searchData.pageAction = 1;
      this.getTableData();
    },
    changePage(val) {
      if (this.copyPage > val) {
        this.searchData.pageAction = 0;
      } else {
        this.searchData.pageAction = 1;
      }
      this.searchData.pageNum = val;
      this.copyPage = this.searchData.pageNum;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    // 排名
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
    rankList: {
      type: Array,
      default: () => [],
    },
    rankLoading: {
      type: Boolean,
    },
  },
  watch: {
    rankLoading: {
      handler(val) {
        this.ranLoading = val;
      },
      deep: true,
      immediate: true,
    },
    statisticalData: {
      handler(val) {
        if (val.resultValue) {
          this.statisticalList = val;
          this.sxjInitChart(); //sxj echarts
          this.rlInitChart(); //rl echarts
          this.clInitChart(); //cl echarts},
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.searchData.pageNum = 1;
          this.searchData.pageSize = 20;
          this.searchData.firstId = null;
          this.searchData.lastId = null;
          this.searchData.pageAction = 1;
          this.getTableData(); //表格
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
