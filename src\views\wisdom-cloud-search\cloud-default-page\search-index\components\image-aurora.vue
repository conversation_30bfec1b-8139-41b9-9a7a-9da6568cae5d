<template>
  <div class="image-auroras">
    <div v-for="(item, $index) in imgAuroraList" :key="$index" :class="'img-aurora'+$index" class="img-aurora-item">
      <img :src="item" alt />
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        imgAuroraList: [
          require('@/assets/img/wisdom-cloud-search/animation/vehicle.png'),
          require('@/assets/img/wisdom-cloud-search/animation/wifi.png'),
          require('@/assets/img/wisdom-cloud-search/animation/people.png'),
          require('@/assets/img/wisdom-cloud-search/animation/police-work.png'),
          require('@/assets/img/wisdom-cloud-search/animation/camera.png')
        ]
      }
    }
  }
</script>
<style lang="less" scoped>
  .image-auroras {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 2;
    .img-aurora-item {
      position: absolute;
      &>img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .img-aurora0 {
      width: 184px;
      height: 272px;
      left: 13%;
      bottom: 12%;
      animation: imgAuroraMove0 1.8s infinite;
    }
    @keyframes imgAuroraMove0 {
      0% {
        bottom: 12%;
      }
      50% {
        bottom: 16%;
      }
      100% {
        bottom: 12%;
      }
    }
    .img-aurora1 {
      width: 144px;
      height: 182px;
      left: 29%;
      bottom: 29%;
      animation: imgAuroraMove1 1.8s infinite;
    }
    @keyframes imgAuroraMove1 {
      0% {
        bottom: 29%;
      }
      50% {
        bottom: 31%;
      }
      100% {
        bottom: 29%;
      }
    }
    .img-aurora2 {
      width: 206px;
      height: 330px;
      left: 44%;
      bottom: 0;
      animation: imgAuroraMove2 1.8s infinite;
    }
    @keyframes imgAuroraMove2 {
      0% {
        bottom: 0;
      }
      50% {
        bottom: 4%;
      }
      100% {
        bottom: 0;
      }
    }
    .img-aurora3 {
      width: 80px;
      height: 132px;
      left: 61%;
      bottom: 28%;
      animation: imgAuroraMove3 1.8s infinite;
    }
    @keyframes imgAuroraMove3 {
      0% {
        bottom: 28%;
      }
      50% {
        bottom: 30%;
      }
      100% {
        bottom: 28%;
      }
    }
    .img-aurora4 {
      width: 148px;
      height: 246px;
      left: 78%;
      bottom: 13%;
      animation: imgAuroraMove4 1.8s infinite;
    }
    @keyframes imgAuroraMove4 {
      0% {
        bottom: 13%;
      }
      50% {
        bottom: 17%;
      }
      100% {
        bottom: 13%;
      }
    }
  }
</style>