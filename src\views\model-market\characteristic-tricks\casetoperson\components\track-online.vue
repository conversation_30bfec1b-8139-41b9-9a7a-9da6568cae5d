<!--
    * @FileDescription: 由案到人-轨迹详情-连线数据
    * @Author: H
    * @Date: 2024/04/12
 * @LastEditors: zhengmingming zhengmingming
 * @LastEditTime: 2024-05-20 18:14:35
 -->
<template>
  <div class="track_list">
    <div class="sortord" @click="handlesort">
      <ui-icon type="time" :size="14"></ui-icon>
      <p>时间排序</p>
      <Icon v-show="!sort" type="md-arrow-round-down" />
      <Icon v-show="sort" type="md-arrow-round-up" />
    </div>
    <ul class="box-ul">
      <li
        class="box-li"
        v-for="(item, index) in dataList"
        :key="index"
        @click="handleListTrack(item)"
      >
        <div class="box_li-title">
          <indexPoint :index="index + 1"></indexPoint>
          <!-- <p>{{ item.detail.deviceId }}</p> -->
        </div>
        <div class="content-top">
          <div class="content-top-img">
            <img v-lazy="getDefaultImg(item.traitImg, item.dataType)" alt="" />
          </div>
          <div class="content-top-right">
            <span class="ellipsis label-content">
              <ui-icon type="leixing1" :size="14"></ui-icon>
              <span>{{
                item.dataType | commonFiltering(trajectoryTypeList)
              }}</span>
              <ui-tag
                v-if="showLabel(item.dataType) && item.label"
                :color="item.label | getLabelInfo(item.dataType, 'color')"
                class="label"
                >{{ item.label | getLabelInfo(item.dataType, "label") }}</ui-tag
              >
            </span>
            <span class="ellipsis">
              <ui-icon type="time" :size="14"></ui-icon>
              <span>{{ item.trajectoryTime }}</span>
            </span>
            <span class="ellipsis">
              <ui-icon type="location" :size="14"></ui-icon>
              <span>{{ item.address }}</span>
            </span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import indexPoint from "@/components/model-market/indexPoint.vue";
import { baseTrackTypeMap } from "./trackTypeMap";
import UiTag from "@/components/ui-tag";
export default {
  name: "",
  components: {
    indexPoint,
    UiTag,
  },
  props: {
    dataList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      sort: false,
    };
  },
  filters: {
    getLabelInfo(code, dataType, key) {
      if (!code) return "";
      const showLabel = baseTrackTypeMap[dataType].showLabel;
      return showLabel[code][key];
    },
  },
  methods: {
    getDefaultImg(img, dataType = "face") {
      if (img) return img;
      return baseTrackTypeMap[dataType].defaultImg;
    },
    showLabel(dataType) {
      return !!baseTrackTypeMap[dataType].showLabel;
    },
    // 排序
    handlesort() {
      this.sort = !this.sort;
      this.$emit("onSort");
    },
    handleListTrack(row) {
      this.$emit("openPositionTheWindow", row);
    },
  },
};
</script>

<style lang='less' scoped>
.track_list {
  height: calc(~"100% - 50px");
  .sortord {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    display: flex;
    padding: 10px;
    align-items: center;
    margin-left: 5px;
    cursor: pointer;
    display: flex;
    column-gap: 5px;
    align-items: center;
  }

  .box-ul {
    height: calc(~"100% - 50px");
    overflow-y: auto;
    .box-li {
      display: flex;
      background: #f9f9f9;
      margin: 10px 0;
      padding: 10px;

      .box_li-title {
        display: flex;
        align-items: center;

        p {
          font-weight: 700;
          font-size: 14px;
          color: #f29f4c;
          margin-left: 15px;
        }
      }

      .content-top {
        flex: 1;
        display: flex;
        // overflow: auto;
        cursor: pointer;
        margin-top: 10px;
        overflow: hidden;

        img {
          width: 80px;
          height: 80px;
          border: 1px solid #cfd6e6;
          position: relative;

          .similarity {
            position: absolute;
            left: 0;
            top: 0;

            span {
              padding: 2px 5px;
              background: #4597ff;
              color: #fff;
              border-radius: 4px;
            }
          }
        }

        .content-top-right {
          margin-left: 11px;
          // margin-top: 3px;
          font-size: 14px;
          flex: 1;
          width: calc(~"100% - 60px");
          overflow: hidden;
          .label-content {
            position: relative;
            .label {
              position: absolute;
              right: 5px;
              margin: 0;
              height: 20px;
              margin-left: 5px;
            }
          }
          /deep/ .iconfont {
            margin-right: 5px;
          }

          .block {
            color: #000000;
          }
        }
      }
    }
  }
}
</style>