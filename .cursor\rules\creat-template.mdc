---
description: 
globs: 
alwaysApply: true
---
Description:
  统一 Vue 组件生成规范，确保组件目录结构、命名、props 类型、文档输出等一致性和可维护性。

Globs:
  src/components/**/index.vue

## 组件生成规范
- 组件目录采用 PascalCase 命名（如 MyComponent）
- 每个组件目录下必须有 index.vue 作为主入口
- props 必须定义类型和默认值，支持 iview 组件参数透传，将透传参数都放到iviewConfig 对象中
- 组件需包含必要的文档（如 README.md 或 index.md），说明用法、参数、示例
- 推荐每个组件目录下有独立的样式文件（如 style.less/scss）
- 组件应支持按需引入和 tree-shaking
- 组件代码需包含基础注释，便于维护
- 组件导出时需通过 index.js 统一出口（如有多文件）

## 目录结构示例
MyComponent/
  ├─ index.vue
  ├─ index.js
  ├─ style.less
  └─ README.md

## 适用范围
- 仅适用于 src/components 目录下新建的业务组件