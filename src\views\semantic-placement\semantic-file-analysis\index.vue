<template>
  <div class="container">
    <!-- 查询 -->
    <Search
      ref="searchRef"
      :subTaskType="subTaskType"
      :algorithmList="algorithmList"
      @searchForm="searchForm"
    />
    <div class="table-container">
      <div class="data-above">
        <div class="left-operater">
          <Button size="small" @click="handleAdd">
            <ui-icon type="jia" color="#2C86F8"></ui-icon>
            新增任务
          </Button>
          <Button size="small" @click="handleDelJobs">
            <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
            批量删除
          </Button>
        </div>
        <div class="right-operater">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-selection-change="selectChangeHandler"
        >
          <template #videoTime="{ row }">
            <div>
              {{
                row.taskType == 5
                  ? getVideoAllLength(row.taskResourceList)
                  : "--"
              }}
            </div>
          </template>
          <template #fileNum="{ row }">
            <div>
              {{ getFileNum(row.taskResourceList) }}
            </div>
          </template>
          <template #taskType="{ row }">
            <div>
              {{ taskTypeList[row.taskType] }}
            </div>
          </template>
          <template #algorithmName="{ row }">
            <AlgorithmTagPop
              :data="row.compareAlgorithmNames"
            ></AlgorithmTagPop>
          </template>
          <template #status="{ row }">
            <span>{{
              taskStatusList.find((item) => item.key == row.status).label
            }}</span>
          </template>
          <template #alarmLevel="{ row }">
            <AlarmLevel :alarmLevel="row.taskLevel"></AlarmLevel>
          </template>
          <template #action="{ row }">
            <TableAction
              :row="row"
              :subTaskType="subTaskType"
              @taskStatusHandler="taskStatusHandler"
              @handleEdit="handleEdit"
              @handleSearch="
                (val) =>
                  toDetailByTask({
                    taskId: val.id,
                    taskType: params.taskParsingType,
                  })
              "
              @mapLoaction="mapLoaction"
              @handleDel="handleDel"
              @fileDownload="fileDownloadByTask"
            ></TableAction>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <fileAddModal
      v-model="isShowAdd"
      ref="fileAddModalRef"
      :subTaskId="subTaskId"
      :algorithmList="algorithmList"
      @updated="jobUpdated"
    ></fileAddModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
    <VideoFileDetail ref="videoFileDetailRef"></VideoFileDetail>
  </div>
</template>
<script>
import Search from "../components/search.vue";
import fileAddModal from "@/views/semantic-placement/components/file-add-modal.vue";
import TableAction from "../components/table-action.vue";
import VideoFileDetail from "../components/video-file-detail.vue";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import TaskHandler from "@/views/semantic-placement/mixins/taskHandler.js";
import AlgorithmTagPop from "../components/algorithm-tag-pop.vue";
import AlarmLevel from "../components/alarm-level.vue";
import ExpandRow from "@/views/semantic-placement/components/expandRow.vue";
import {
  getLLMCompareTaskPageList,
  downloadFileByTaskId,
} from "@/api/semantic-placement.js";
import { taskTypeList } from "../enums/index.js";
export default {
  name: "FileAnalysis",
  components: {
    Search,
    fileAddModal,
    adjustPosition,
    AlgorithmTagPop,
    AlarmLevel,
    TableAction,
    VideoFileDetail,
  },
  props: {},
  mixins: [TaskHandler],
  data() {
    return {
      taskTypeList,
      subTaskType: "file",
      list: [],
      childList: [],
      loading: false,
      tagList: [],
      pageForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        taskParsingType: 5, // 结构化解析类型: 5文件解析
        sortType: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, { row, index }) => {
            const resourceList = row?.taskResourceList?.map((item) => {
              const { resourceInfo, ...otherParam } = item;
              return {
                ...resourceInfo,
                ...otherParam,
                createTime: row.createTime,
              };
            }) || [{}, {}, {}];
            return h(ExpandRow, {
              props: {
                tableList: resourceList,
                columns: this.childColumns,
                currentJob: this.list[index],
                subTaskType: this.subTaskType,
                subTaskStatus: row.status,
                switchLoading: this.switchLoading,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(row);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    ...val,
                    taskType: this.params.taskParsingType,
                  });
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val) => {
                  this.deleteTasks([val]);
                },
                fileDownload: (val) => {
                  this.fileDownload(val);
                },
                fileDetail: (val) => {
                  this.fileDetail(val);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "taskName", width: 260 },
        { title: "算法名称", slot: "algorithmName", width: 160 },
        { title: "报警级别", slot: "alarmLevel" },
        { title: "文件类型", slot: "taskType", width: 150 },
        { title: "大小/数量", slot: "fileNum", width: 130 },
        { title: "时长", slot: "videoTime", align: "center" },
        { title: "任务状态", slot: "status", align: "center" },
        { title: "处理时长", slot: "handlerTime" },
        { title: "创建时间", key: "createTime", width: 190 },
        { title: "创建人", key: "creator" },
        { title: "操作", width: 120, slot: "action" },
      ],
      childColumns: [
        { title: "文件名称", slot: "fileName", align: "center" },
        { title: "大小/时长", slot: "fileSizeNum", align: "center" },
        { title: "分辨率", key: "resolution", align: "center" },
        { title: "任务状态", slot: "status", align: "center" },
        { title: "处理时长", slot: "analysisTime", align: "center" },
        { title: "创建时间", key: "createTime", align: "center" },
        { title: "校准时间", key: "absTime", align: "center" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {},
  mounted() {
    this.getLLMCompareAlgorithm();
    this.getList();
  },
  beforeDestroy() {},
  methods: {
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    // 查询列表
    async getList(otherParam = {}) {
      this.loading = true;
      try {
        let param = {
          ...this.$refs.searchRef.getSearchParam(),
          ...this.params,
          ...otherParam,
        };
        const { data } = await getLLMCompareTaskPageList(param);
        this.list = data?.entities || [];
        this.total = data?.total || 0;
      } catch (e) {
      } finally {
        this.loading = false;
      }
    },
    jobUpdated() {
      this.isShowAdd = false;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm(form) {
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
        getStructureRealTaskList({
          structureJobId: row.jobId,
          structureJobType: this.params.structureJobType,
          sort: this.params.sort,
          pageNumber: 0,
          pageSize: 999,
        }).then((res) => {
          const { list } = res.data;
          this.$set(this.childList, index, list || []);
          if (!list.length) this.getList();
        });
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.fileAddModalRef.init({});
      });
    },
    handleEdit(row) {
      this.subTaskId = row.id;
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.fileAddModalRef.init({ ...row });
      });
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      const resourceInfoList = item.taskResourceList.map((item) => {
        return item.resourceInfo;
      });
      var arrayP = [];
      resourceInfoList &&
        resourceInfoList.length > 0 &&
        resourceInfoList.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: item?.resourceName || "",
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      } else {
        this.$Message.error("该视频文件无点位信息，无法定位");
      }
    },
    fileDownload(row) {
      const {
        taskResourceList = [],
        resourceFilePath = "",
        resourceName,
      } = row;
      if (taskResourceList.length > 0) {
        // 任务文件全量下载
      }
      if (resourceFilePath) {
        fetch(resourceFilePath).then(async (res) => {
          const blob = await res.blob();
          // 单个任务文件下载
          const a = document.createElement("a");
          a.setAttribute("download", resourceName);
          a.style.display = "none";
          a.href = URL.createObjectURL(blob);
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        });
      }
    },
    // 文件下载任务中所有的文件
    async fileDownloadByTask(row) {
      downloadFileByTaskId(row.id).then((res) => {
        //下载后文件名
        let fileName = new Date().getTime() + ".zip";
        let a = document.createElement("a");
        let href = URL.createObjectURL(res);
        a.href = href;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      });
    },
    // 查看视频文件照片内容
    fileDetail({ taskId, resourceId, resourceName }) {
      this.$refs.videoFileDetailRef.show({ taskId, resourceId, resourceName });
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },

    // 任务开启/关闭
    async taskStatusHandler(value) {
      // 开启
      if (value.status == 1) {
        const bool = await this.stopJobs([value]);
        value.status = bool ? 2 : value.status;
      } else {
        const bool = await this.startJobs([value]);
        value.status = bool ? 1 : value.status;
      }
    },
    // 获取任务中文件资源的总大小和数量
    getFileNum(taskResourceList) {
      const fileLength = taskResourceList?.length || 0;
      if (fileLength == 0) {
        return "--/--";
      }
      let fileSize = 0;
      taskResourceList.forEach((item) => {
        fileSize += item?.resourceInfo?.fileSize || 0;
      });
      return (fileSize / 1024 / 1024).toFixed(2) + "MB/" + fileLength;
    },
    // 获取任务中视频时长
    getVideoAllLength(taskResourceList) {
      const fileLength = taskResourceList?.length || 0;
      if (fileLength == 0) {
        return "--";
      }
      try {
        // 时间相加回头处理
        let allSecond = 0;
        taskResourceList.forEach((item) => {
          let videoDuration =
            item?.resourceInfo?.videoDuration?.split(":") || [];
          videoDuration.length < 3 && (videoDuration = [0, 0, 0]);
          const second =
            videoDuration[0] * 3600 +
            videoDuration[1] * 60 +
            videoDuration[2] * 1;
          allSecond += second;
        });
        let hour = Math.floor(allSecond / 3600) || 0;
        let min = Math.floor((allSecond % 3600) / 60) || 0;
        let second = Math.round((allSecond % 3600) % 60) || 0;
        hour < 10 && (hour = "0" + hour);
        min < 10 && (min = "0" + min);
        second < 10 && (second = "0" + second);
        return `${hour}:${min}:${second}`;
      } catch (e) {
        return "00:00:00";
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "../style/index.less";
</style>
