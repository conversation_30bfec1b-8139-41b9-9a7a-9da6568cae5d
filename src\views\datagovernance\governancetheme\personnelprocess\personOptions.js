let personData = [
  {
    addVisible: false,
    top: '1.3rem',
    left: '1.7%',
    datas: [
      {
        name: 'DataInput',
        icon: 'icon-zu16191',
        title: '数据输入',
        desc: '选择待治理数据',
        left: '10px',
        iconPass: true,
        iconSetting: false,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '6.7%',
      height: '0.04rem',
      top: '1.52rem',
      left: '16%',
    },
  },
  {
    addVisible: false,
    top: '0.78rem',
    left: '22.95%',
    datas: [
      {
        name: 'Fieldmap',
        icon: 'icon-ziduanyingshe',
        title: '字段映射',
        desc: '设置原始数据与标准数据的字段映射关系',
        left: '10px',
        iconSetting: true,
        switch: false,
      },
      {
        name: 'Dictionarymapping',
        icon: 'icon-zidianyingshe',
        title: '字典映射',
        desc: '设置原始数据与标准数据的数据字典的映射关系',
        left: '10px',
        iconSetting: true,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '4.6%',
      height: '0.04rem',
      top: '1.52rem',
      left: '38.7%',
    },
  },
  {
    addVisible: false,
    top: '1.3rem',
    left: '43.5%',
    datas: [
      {
        name: 'Formawarehouse',
        icon: 'icon-linshiruku',
        title: '正式入库',
        desc: '标准转换后的数据存入系统设备标准库。',
        left: '10px',
        iconPass: true,
        iconSetting: true,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '4.6%',
      height: '0.04rem',
      top: '1.52rem',
      left: '57.8%',
    },
  },
  {
    addVisible: false,
    top: '0.7rem',
    left: '62.8%',
    datas: [
      {
        name: 'Empty',
        icon: 'icon-kongzhijiance',
        title: '空值检测',
        desc: '检测字段是否为空',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
      {
        name: 'Repeat',
        icon: 'icon-zhongfujiance',
        title: '重复检测',
        desc: '检测字段是否重复',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '4.6%',
      height: '0.04rem',
      top: '1.52rem',
      left: '78.6%',
    },
  },
  {
    addVisible: false,
    top: '1.3rem',
    left: '83.6%',
    datas: [
      {
        name: 'output',
        icon: 'icon-zu1665',
        title: '数据输出',
        desc: '追踪查阅数据最终检测结果',
        left: '10px',
        iconSetting: false,
        switch: false,
      },
    ],
    connectingOptions: {
      width: '0.8rem',
      height: '0.04rem',
      top: '2.36rem',
      left: '3%',
      angle: 90,
    },
  },
  {
    addVisible: false,
    top: '2.83rem',
    left: '1.7%',
    datas: [
      {
        name: 'Imgtest',
        icon: 'icon-tuxiangshangchuanjishixingjiance',
        title: '图像上传及时性检测',
        desc: '检测图像上传是否及时',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '12.2%',
      height: '0.04rem',
      top: '3.03rem',
      left: '16.2%',
    },
  },
  {
    addVisible: false,
    top: '2.83rem',
    left: '28.8% ',
    datas: [
      {
        name: 'FaceStructure',
        icon: 'icon-renlianjiegouhua',
        title: '人脸结构化',
        desc: '选择多家算法对人脸图像进行结构化',
        left: '10px',
        iconPass: true,
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '12.2%',
      height: '0.04rem',
      top: '3.03rem',
      left: '43.4%',
    },
  },
  {
    addVisible: false,
    top: '2.83rem',
    // left: "28.8% ",
    left: '56.1%',
    datas: [
      {
        name: 'Trajectory',
        icon: 'icon-renyuanguijizhunquexingjianceyouhua',
        title: '人员轨迹准确性检测优化',
        desc: '多算法检测人员活动轨迹是否准确',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '12.2%',
      height: '0.04rem',
      top: '3.03rem',
      left: '70.8%',
      // left: "43.4%",
    },
  },
  {
    addVisible: false,
    top: '2.89rem',
    // left: "56.1%",
    left: '83.6%',
    datas: [
      {
        name: 'output2',
        icon: 'icon-zu1665',
        title: '数据输出',
        desc: '追踪查阅数据最终检测结果',
        left: '10px',
        iconSetting: false,
      },
    ],
    // connectingOptions: {
    //     width: "0.8rem",
    //     height: "0.04rem",
    //     top: "2.36rem",
    //     left: "3%",
    //     angle: 90,
    // },
  },
  // {
  // connectingOptions: {
  //     width: "0.8rem",
  //     height: "0.04rem",
  //     top: "2.98rem",
  //     left: "6.26rem",
  //     angle: -90,
  // },
  // },
];
export { personData };
