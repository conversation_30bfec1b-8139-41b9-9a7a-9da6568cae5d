<template>
  <div class="ui-table">
    <Table
      ref="table"
      :columns="columns"
      :loading="loading"
      :width="width"
      :height="fullHeight"
      :data="data"
      :border="border"
      v-bind="$attrs"
      class="scroll-y"
      v-on="$listeners"
    >
      <template #loading>
        <ui-loading></ui-loading>
      </template>
      <template
        v-for="column in columns"
        slot-scope="params"
        :slot="column.slot ? column.slot : ''"
      >
        <slot :name="column.slot ? column.slot : ''" v-bind="params"></slot>
      </template>
    </Table>
  </div>
</template>
<script>
import { Table } from "view-design";
import { throttle } from "lodash";
import { scrollTo } from "./scroll-to";

export default {
  props: {
    ...Table.props,
    // 到底部需要减去的高度
    minusHeight: {
      default: false,
    },
    pageNumber: {
      type: [Number, String],
      default: 1,
    },
  },
  data() {
    return {
      screenHeight: 0,
      fullHeight: "",
      offsetTop: "", // 记录table的初始offsetTop值
    };
  },
  watch: {
    height: {
      handler() {
        this.fullHeight = this.height;
      },
      immediate: true,
    },
  },
  deactivated() {
    this.removeResizeFun();
  },
  beforeDestroy() {
    this.removeResizeFun();
  },
  mounted() {
    if (this.height) {
      this.fullHeight = this.height;
    } else {
      this.$refs.table.observer.listenTo(this.$el, this.handleIViewTableResize);
    }
  },
  methods: {
    // 执行动画滚动顶部
    scrollTopHandle() {
      const el = this.$refs.table.$el.children[0].children[1];
      scrollTo(0, 800, el);
    },
    // 改变函数 使用节流
    handleIViewTableResize: throttle(function (el) {
      this.fullHeight = el.clientHeight;
    }, 100),
    removeResizeFun() {
      if (!this.height) {
        this.$refs.table.observer.removeListener(
          this.$el,
          this.handleIViewTableResize
        );
      }
    },
    getTabelRef() {
      return this.$refs.table;
    },
  },
};
</script>

<style scoped lang="less">
.ui-table {
  position: relative;
  width: 100%;
  flex: 1;
  overflow: hidden;
  .ivu-table-tip {
    overflow: hidden;
  }
}

/deep/ .ivu-table-tip {
  span:before {
    content: "";
    display: block;
    background: url("../../assets/img/empty-page/null_main_icon.png") no-repeat
      center center;
    background-size: 100% auto;
    width: 150px;
    height: 150px;
    margin: 0 auto 0;
    transform: translateX(5px);
  }
}
</style>
