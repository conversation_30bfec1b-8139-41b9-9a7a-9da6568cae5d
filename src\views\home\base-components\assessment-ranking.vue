<template>
  <div class="body-container" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
    <div class="rank-item mb-xs" v-for="(item, index) in rankData" :key="index">
      <p class="name-wrapper ellipsis">
        <span class="inline" :class="`rank-${item.rank}`" v-if="item.rank < 4 && item.rank > 0"></span>
        <span class="rank-num inline" v-else>{{ item.rank }}</span>
        <span class="rank-name f-14 font-white ellipsis ml-md">{{ item.regionName }}</span>
      </p>
      <p class="num-wrapper">
        <span class="rank-score f-16">{{ `${item.standardsValue}分` }}</span>
        <span class="down" v-if="item.rankType === 'DOWN'">
          <span>{{ item.rankRise || 0 }}</span>
          <span class="down-image inline ml-sm"></span>
        </span>
        <span class="up" v-else>
          <span>{{ item.rankRise || 0 }}</span>
          <span class="up-image inline ml-sm"></span>
        </span>
      </p>
    </div>
  </div>
</template>

<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'assessment-ranking',
  components: {},
  props: {},
  data() {
    return {
      tabData: [{ label: '月考核排行', id: 'rank' }],
      activeValue: 'rank',
      rankData: [],
      rankLoading: false,
      taskType: '',
      score: '',
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  watch: {},
  filter: {},
  async created() {
    await this.viewByParamKey();
    await this.getRankStatistics();
  },
  methods: {
    async viewByParamKey() {
      try {
        let params = {
          key: 'HOME_PAGE_CONFIG',
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = JSON.parse(data.paramValue);
        this.taskType = paramValue.assessTask;
      } catch (e) {
        console.log(e);
      }
    },
    async getRankStatistics() {
      this.rankLoading = true;
      let params = {
        examTaskId: this.taskType,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(home.getRankStatistics, params);
        this.rankData = data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.rankLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  position: relative;
  height: calc(100% - 40px);
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  .rank-item {
    background: rgba(24, 137, 255, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 32px;
    > p {
      display: flex;
      align-items: center;
      &.name-wrapper {
        width: 50%;
      }
      &.num-wrapper {
        flex: 1;
        justify-content: flex-end;
      }
    }
    .rank-num {
      text-align: center;
      width: 30px;
      font-weight: bold;
      color: #8abafb;
    }
    .rank-name {
      max-width: 100px;
    }
    .rank-1 {
      height: 30px;
      width: 30px;
      background: url('~@/assets/img/base-home/assessment-ranking/no1.png') no-repeat;
      background-size: cover;
    }
    .rank-2 {
      height: 30px;
      width: 30px;
      background: url('~@/assets/img/base-home/assessment-ranking/no2.png') no-repeat;
      background-size: cover;
    }
    .rank-3 {
      height: 30px;
      width: 30px;
      background: url('~@/assets/img/base-home/assessment-ranking/no3.png') no-repeat;
      background-size: cover;
    }
    .rank-score {
      font-weight: bold;
      color: #00cdf7;
    }
    .down {
      font-weight: 400;
      color: #c60235;
      width: 60px;
      text-align: right;

      .down-image {
        width: 10px;
        height: 14px;
        background: url('~@/assets/img/base-home/assessment-ranking/down.png') no-repeat;
        background-size: cover;
      }
    }
    .up {
      font-weight: 400;
      color: #01ef77;
      width: 60px;
      text-align: right;

      .up-image {
        width: 10px;
        height: 14px;
        background: url('~@/assets/img/base-home/assessment-ranking/up.png') no-repeat;
        background-size: cover;
      }
    }
  }
}
</style>
