<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      :icon-list="iconList"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      :table-loading="tableLoading"
      :form-item-data="formItemData"
      :form-data="formData"
      :card-list="[]"
      :tabList="tabList"
      @startSearch="startSearch"
      @handlePage="handlePage"
      @handlePageSize="handlePageSize"
    >
      <template #otherButton>
        <div class="other-button fr mt-sm">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <template slot="outcome" slot-scope="{ row }">
        <Tag :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
      </template>
      <!-- 表格插槽 -->
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #phyStatusText="{ row }">
        <span :class="row.phyStatusText === '可用' ? 'font-green' : 'font-red'">{{ row.phyStatusText }}</span>
      </template>
      <template slot="action" slot-scope="{ row }">
        <ui-btn-tip
          icon="icon-chakanyichangxiangqing"
          class="mr-sm"
          content="查看不合格原因"
          @click.native="checkReason(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
  </div>
</template>
<script>
/**
 * 本文件属于处理数据的中间层
 */
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { iconStaticsList, tableColumn, formItemData } from './util/enum/ReviewParticular.js';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  mixins: [downLoadTips, particularMixin, dealWatch],
  props: {},
  data() {
    return {
      tabList: ['设备模式', '图片模式'],
      iconList: [],
      tableColumns: tableColumn,
      formItemData: formItemData,
      formData: {
        deviceId: '',
        deviceName: '',
        phyStatus: '',
      },
      pageData: {
        pageNumber: 1,
        pageSize: 50,
      },
      totalCount: 0,
      tableLoading: false,
      tableData: [
        {
          'civilCode': '23030000',
          'civilName': '鸡西市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23110000',
          'civilName': '黑河市公安局',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23040000',
          'civilName': '鹤岗市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23080000',
          'civilName': '佳木斯市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23090000',
          'civilName': '七台河市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23270000',
          'civilName': '大兴安岭地区',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23060000',
          'civilName': '大庆市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23070000',
          'civilName': '伊春市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23120000',
          'civilName': '绥化市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23100000',
          'civilName': '牡丹江',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23010000',
          'civilName': '哈尔滨',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23050000',
          'civilName': '双鸭山市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'civilCode': '23020000',
          'civilName': '齐齐哈尔市公安局',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
      ],
      selectTabs: [],
      exportLoading: false,
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
    };
  },
  created() {
    this.totalCount = this.tableData.length;
    this.startWatch(
      '$route.query',
      () => {
        const paramList = this.$route.query;
        this.iconList = iconStaticsList[paramList.indexType];
        // this.initAll()
      },
      { deep: true, immediate: true },
    );
    this.getSelectTabs();
  },
  methods: {
    initAll() {
      this.getTableList();
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    startSearch(params) {
      this.pageData.pageNumber = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableList();
    },
    getTableList() {
      return false;
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities || [];
        this.totalCount = data.total;
      });
    },
    handlePage(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    handlePageSize(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getAbnormalLabel,params, 'post',this.$route.query.cascadeId, {})
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = data.map((item) => {
          return {
            name: item,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectInfo(infoList) {
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.formData.errorMessages = this.errorMessages;
    },
    // 导出
    onExport() {
      const params = {
        displayType: this.$route.query.statisticType,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      this.MixinGetExport(params);
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
  .operation-bar {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .tabs {
    flex: 1;
  }
}
</style>
