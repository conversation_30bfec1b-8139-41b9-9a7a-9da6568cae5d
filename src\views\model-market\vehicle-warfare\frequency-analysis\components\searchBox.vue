<!--
    * @FileDescription: 搜索条件
    * @Author: H
    * @Date: 2024/01/31
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-27 11:04:36
 -->
<template>
  <div class="search_box">
    <div class="title">
      <p>{{ title }}</p>
    </div>
    <div
      class="search_condition"
      :class="{ 'search_condition-pack': packUpDown }"
    >
      <div class="search_form">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="80"
        >
          <FormItem label="车牌号码:" prop="plateNo">
            <Input v-model="formData.plateNo" placeholder="请输入"></Input>
          </FormItem>
          <FormItem label="车牌颜色:" prop="">
            <ui-tag-select
              style="position: relative; top: 5px"
              ref="plateColor"
              @input="
                (e) => {
                  input(e, 'plateColor');
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in licensePlateColorList"
                :key="$index"
                effect="dark"
                :name="item.dataKey"
              >
                <div
                  v-if="licensePlateColorArray[item.dataKey]"
                  :title="item.dataValue"
                  :style="{
                    borderColor:
                      licensePlateColorArray[item.dataKey].borderColor,
                  }"
                  class="plain-tag-color"
                >
                  <div
                    :style="licensePlateColorArray[item.dataKey].style"
                  ></div>
                </div>
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
          <FormItem label="车辆颜色:" prop="">
            <Select v-model="formData.vehicleColor" placeholder="请选择">
              <Option
                v-for="(item, $index) in bodyColorList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆品牌:" prop="">
            <div class="select-tag-button" @click="selectBrandHandle">
              选择车辆品牌/已选（{{ formData.vehicleBrand.length }}）
            </div>
          </FormItem>
          <FormItem label="车辆类型:" prop="">
            <Select
              v-model="formData.vehicleType"
              filterable
              placeholder="请选择车辆类型"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleTypeList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="设备资源:" prop="">
            <ul class="search_content">
              <li class="active-area-sele" @click="handleSelemodel">
                选择设备/已选({{ formData.deivceList.length }})
              </li>
              <li class="area-list" @click="handleSeleArea">
                <ui-icon type="gateway" />
              </li>
            </ul>
            <!-- <div class="select-tag-button" @click="selectBrandHandle">选择设备/已选（{{formData.deivceList.length}}）</div> -->
          </FormItem>
          <FormItem label="时  间:" prop="time">
            <ui-quick-date
              style="width: 100%"
              ref="quickDateRef"
              v-model="formData.dateType"
              type="month"
              border
              @change="dataRangeHandler"
            />
          </FormItem>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div
      class="footer"
      :class="{ packArrow: packUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />
    <!-- 选择品牌 -->
    <BrandModal ref="brandModal" @on-change="selectBrand" />
  </div>
</template>

<script>
import uiUploadImg from "@/components/ui-upload-img/index";
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import BrandModal from "@/components/ui-brand-modal.vue";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "车辆频次分析",
    },
  },
  components: {
    uiUploadImg,
    BrandModal,
  },
  data() {
    const validateTime = (rule, value, callback) => {
      callback();
    };
    return {
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      formData: {
        plateNo: "",
        deivceList: [],
        startDate: "",
        endDate: "",
        dateType: 2,
        vehicleBrand: [],
        plateColor: "",
        vehicleColor: "",
        vehicleType: "",
      },
      ruleValidate: {
        plateNo: [{ required: true, message: "请输入", trigger: "blur" }],
        time: [{ required: true, validator: validateTime, trigger: "change" }],
      },
      packUpDown: false,
      similarity: 85,
      algorithmType: "1",
      urlList: [],
      selectDeviceList: [],
      checkedLabels: [], // 已选择的标签
      licensePlateColorArray,
    };
  },
  watch: {
    packUpDown: {
      handler(val) {
        this.$nextTick(() => {
          let box = document.querySelector(".search_condition");
          if (val) {
            box.style.overflow = "hidden";
          } else {
            setTimeout(() => {
              box.style.overflow = "inherit";
            }, 200);
          }
        });
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
    }),
  },
  created() {},
  mounted() {
    this.dataRangeHandler(this.$refs.quickDateRef.getDate());
  },
  methods: {
    handlePackup() {
      this.packUpDown = !this.packUpDown;
      this.$emit("packBox");
      // console.log(document.querySelector('.search_box').scrollHeight, 222)
    },
    handleSearch() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.packUpDown = true;
          let params = { ...this.formData };
          this.$emit("searchList", params);
        }
      });
    },
    /**
     * 选择设备
     */
    handleSelemodel() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list;
      this.formData.deivceList = list.map((item) => item.deviceGbId);
    },
    handleSeleArea() {
      this.$emit("seleArea");
    },
    /**
     * 车辆品牌已选择，返回数据
     */
    selectBrand(list) {
      this.formData.vehicleBrand = list;
    },
    // 选择品牌
    selectBrandHandle() {
      this.$refs.brandModal.show();
    },
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.formData[key] = e;
      this.$forceUpdate();
    },
    /**
     * @description: 活动轨迹 - 切换时间类型
     * @param {object} val 起止时间
     */
    dataRangeHandler(val) {
      this.formData.startDate = val.startDate;
      this.formData.endDate = val.endDate;
    },
    // 重置
    handleReset() {
      this.formData = {
        plateNo: "",
        searchContent: "",
        dateType: 2,
        vehicleBrand: [],
        deivceList: [],
        plateColor: "",
        vehicleType: "",
        vehicleColor: "",
      };
      this.$nextTick(() => {
        this.dataRangeHandler(this.$refs.quickDateRef.getDate());
        this.$refs.quickDateRef.setDefaultDate(); // 清空日期组件的自定义时间，在下次选择自定义时回显为空
      });
      this.$refs.plateColor.clearChecked();
      // 清空选择的品牌
      this.$refs.brandModal.clearCheckAll();
      // 清空选择的设备
      this.$refs.selectDevice.removeAllHandle();
      this.urlList = [];
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.search_box {
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  // min-height: 254px;
  // overflow: hidden;
  // filter: blur(0px);
  .search_condition {
    // padding-bottom: 10px;
    max-height: 420px;
    transition: max-height 0.2s ease-out;
    // overflow: hidden;
    .search_form {
      padding: 0 10px;
      margin-top: 10px;
      /deep/ .custom {
        width: 250px;
      }
      /deep/.ivu-date-picker {
        width: 250px !important;
      }
      &_type {
        display: flex;
        border: 1px solid #2c86f8;
        border-radius: 4px;
        margin-bottom: 15px;
        .typeTab {
          background: #ffffff;
          color: #2c86f8;
          width: 175px;
          height: 34px;
          text-align: center;
          line-height: 34px;
          font-size: 14px;
          cursor: pointer;
        }
        .typeActive {
          background: #2c86f8;
          color: #fff;
        }
      }
      .btn-group {
        .btnwidth {
          width: 258px;
        }
      }
      .search_wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 15px;
        .search_title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-right: 10px;
          display: flex;
        }
        .search_strut {
          text-align: right;
          width: 75px;
          font-weight: bold;
        }
        .search_content {
          display: flex;
          flex-wrap: wrap;
          flex: 1;
          .active-area-sele {
            width: 200px;
            height: 34px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
            line-height: 34px;
            cursor: pointer;
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
          }
          .area-sele {
            border: 1px solid #d3d7de;
            color: rgba(0, 0, 0, 0.6);
            background: none;
          }
          .area-list {
            width: 34px;
            height: 34px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #d3d7de;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.6);
            margin-left: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
          }
          .active-area-list {
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
            img {
              opacity: 0.6;
            }
          }
          .analyze_list {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            padding: 2px 10px;
            border-radius: 2px;
            border: 1px solid #d3d7de;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            .active_gradient {
              display: none;
            }
          }
          .active_analyze_list {
            color: rgba(44, 134, 248, 1);
            border: 1px solid rgba(44, 134, 248, 1);
            position: relative;
            .active_gradient {
              position: absolute;
              width: 12px;
              height: 12px;
              background: linear-gradient(
                315deg,
                #2c86f8,
                #2c86f8 50%,
                transparent 50%,
                transparent 100%
              );
              bottom: 0;
              right: 0;
              display: block;
            }
            .ivu-icon-ios-checkmark {
              position: absolute;
              bottom: -3px;
              right: -4px;
              color: #fff;
            }
          }
        }
        .search_text {
          color: rgba(0, 0, 0, 0.8);
          margin-left: 5px;
          font-size: 14px;
        }
      }
      .search_content {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        .active-area-sele {
          width: 200px;
          height: 34px;
          border-radius: 4px;
          font-size: 14px;
          text-align: center;
          line-height: 34px;
          cursor: pointer;
          border: 1px dashed #2c86f8;
          background: rgba(44, 134, 248, 0.1);
          color: rgba(44, 134, 248, 1);
        }
        .area-sele {
          border: 1px solid #d3d7de;
          color: rgba(0, 0, 0, 0.6);
          background: none;
        }
        .area-list {
          width: 34px;
          height: 34px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #d3d7de;
          cursor: pointer;
          color: rgba(0, 0, 0, 0.6);
          margin-left: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 20px;
        }
      }
    }
    .slider-content {
      margin: 15px 0;
      .similarity {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .search_condition-pack {
    max-height: 0px;
    transition: max-height 0.2s ease-out;
  }
  .footer {
    display: flex;
    justify-content: center;
    cursor: pointer;
    // margin: 10px 0;
    img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
  .packArrow {
    img {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
  /deep/ .ivu-form-item {
    margin-bottom: 20px;
  }
  /deep/ .ivu-form-item-label {
    width: 85px !important;
  }
  /deep/ .ivu-form-item-content {
    margin-left: 85px !important;
  }
  /deep/ .ivu-slider {
    flex: 1;
  }
  /deep/ .ivu-select-dropdown {
    left: 0 !important;
  }
  /deep/ .ivu-tag-select-option {
    margin-right: 5px;
  }
  /deep/.ivu-date-picker {
    width: 280px !important;
  }
}
</style>
