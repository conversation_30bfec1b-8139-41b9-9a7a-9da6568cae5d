<template>
  <section class="main-container">
    <div class="search-bar">
      <searchGps ref="searchGps" @searchInfo="searchInfo"></searchGps>
    </div>
    <div class="table-container">
      <div class="data-above" v-if="mapOnData">
        <!-- <Checkbox @on-change="checkAllHandler" v-model="checkAll">全选</Checkbox> -->
        <Button class="mr" @click="handleSort('absTime')" size="small">
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button @click="dataAboveMapHandler" size="small">
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="tableList"
          @on-selection-change="selectionChangeHandle"
        >
          <template #objectType="{ row }">
            <span>{{ tranDictType(row.objectType) }}</span>
          </template>
          <template #lon="{ row }">
            <div>{{ row.geoPoint.lon }}</div>
          </template>
          <template #lat="{ row }">
            <div>{{ row.geoPoint.lat }}</div>
          </template>
          <template #state="{ row }">
            <span>{{ row.state == "1" ? "在线" : "离线" }}</span>
          </template>
        </ui-table>
      </div>
      <!-- <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty> -->
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[40, 80, 120]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
  </section>
</template>
<script>
import searchGps from "../components/search-gps";
import { gpsSearch } from "@/api/wisdom-cloud-search";
import { myMixins } from "../../components/mixin/index.js";
import { queryDataByKeyTypes } from "@/api/user.js";
import { mapMutations, mapGetters } from "vuex";

export default {
  mixins: [myMixins], //全局的mixin
  name: "gpsContent",
  components: {
    searchGps,
  },
  props: {
    // 是否显示点位上图
    mapOnData: {
      type: Boolean,
      default: false,
    },
    mapList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    mapList: {
      handler(val) {
        if (val && val.length > 0) {
          val.forEach((e) => {
            if (e.position && e.position.length) {
              e.position.forEach((item) => {
                this.hasBeenOnTheMapList.push(item.deviceId);
              });
            }
          });
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      queryParam: {
        sortField: "absTime",
        order: "desc",
      },
      tableList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 40,
      },
      total: 0,
      loading: false,
      timeUpDown: false,
      columns: [
        { type: "selection", width: 70, align: "center" },
        {
          title: "编号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "设备编号", key: "deviceId" },
        { title: "所属类型", slot: "objectType" },
        { title: "经度", slot: "lon" },
        { title: "纬度", slot: "lat" },
        // { title: '方向', key: 'direction', },
        { title: "状态", slot: "state" },
        { title: "采集时间", key: "absTime" },
      ],
      gpsTypeList: [],
      selectionList: [],
      hasBeenOnTheMapList: [],
    };
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.recordId);
    },
  },
  activated() {
    if (this.$route.query.deviceInfo) {
      this.$nextTick(() => {
        let deviceInfo = JSON.parse(this.$route.query.deviceInfo);
        this.$refs.searchGps.selectData([{ ...deviceInfo, select: true }]);
        this.queryParam.devices = [deviceInfo.deviceId];
        this.queryList();
      });
    } else {
      this.queryList();
    }
  },
  mounted() {
    this.getDictDataPageList();
  },
  methods: {
    //所属类型字典数据
    getDictDataPageList() {
      queryDataByKeyTypes(["ivcp_gps_type"]).then((res) => {
        this.gpsTypeList = res.data[0].ivcp_gps_type;
      });
    },
    //所属类型字典数据转换
    tranDictType(type) {
      let typeName = "";
      this.gpsTypeList.forEach((item) => {
        if (item.dataKey == type) {
          typeName = item.dataValue;
        }
      });
      return typeName;
    },
    // 排序
    handleSort(val) {
      this.queryParam.sortField = val;
      this.timeUpDown = !this.timeUpDown;
      this.queryParam.order = this.timeUpDown ? "asc" : "desc";
      this.queryList();
    },
    queryList() {
      let formData = this.$refs.searchGps.formData;
      this.queryParam = { ...this.queryParam, ...formData };
      if (this.queryParam.timeSlot != "自定义") {
        this.dispTime();
      } else {
        this.queryParam.startDate = this.$dayjs(
          formData.perceiveDate[0]
        ).format("YYYY-MM-DD HH:mm:ss");
        this.queryParam.endDate = this.$dayjs(formData.perceiveDate[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      this.loading = true;
      this.tableList = [];
      let params = {
        startDate: this.queryParam.startDate,
        endDate: this.queryParam.endDate,
        devices: this.queryParam.devices,
        gpsCode: this.queryParam.gpsCode,
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
      };
      gpsSearch({ ...params, ...this.pageInfo })
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.tableList = entities || [];
          if (this.tableList.length) {
            this.tableList.forEach((e, idx) => {
              if (
                this.alreadyUpImageIds.length &&
                this.alreadyUpImageIds.includes(e.recordId)
              ) {
                e.isChecked = true;
                e._checked = true; // iview table的手动选中
                this.selectionList.push(e);
                this.$set(this.tableList, idx, e);
              }
            });
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchInfo(obj) {
      this.pageInfo.pageNumber = 1;
      if (obj.selectDeviceList) {
        // 处理已选择设备
        var ids = [];
        obj.selectDeviceList.forEach((item) => {
          ids.push(item.deviceId);
        });
        obj.devices = ids;
      }
      this.queryParam = obj;
      this.queryList();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    selectionChangeHandle(val) {
      this.selectionList = val;
    },
    dataAboveMapHandler() {
      let seleNum = this.selectionList.filter(
        (e) => !this.alreadyUpImageIds.includes(e.recordId)
      );
      if (!seleNum.length) {
        this.$Message.warning("请选择上图数据");
        return;
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        seleNum.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = JSON.stringify(this.getListNum);
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      seleNum.map((item) => {
        item.delePoints = true;
        item.deleType = "gps";
      });
      let list = seleNum.filter(
        (e) => !this.hasBeenOnTheMapList.includes(e.deviceId)
      );
      this.$emit("dataAboveMapHandler", {
        type: "gps",
        deleIdent: "gps-" + this.getListNum,
        list,
      });
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
</style>
