<template>
  <div class="container">
    <div class="person">
      <!-- 查询 -->
      <Search
        v-show="!$route.query.noSearch"
        @searchForm="searchForm"
        :dataType="pageForm.dataType"
        :page="'holographic'"
        :searchText="'高级检索'"
      />
      <div class="card-content">
        <div
          v-for="(item, index) in list"
          :key="index"
          :class="item.type === 'people' ? 'people-card' : 'video-card'"
          class="card-item"
        >
          <UiListCard
            type="people"
            :showBar="false"
            :data="item"
            :index="index"
            @archivesDetailHandle="archivesDetailHandle(item)"
            @on-change.stop="changeCardHandle(item, index)"
            @collection="getList"
            @on-search-image="onSearchImage"
            @on-archive="archivesDetailHandle"
            @on-control="onControl"
          />
        </div>
        <ui-empty v-if="list.length === 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="params.pageNumber"
        countTotal
        :showElevator="true"
        :total="total"
        :page-size="params.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
    <search-image ref="searchImage" @on-submit="onSubmit"></search-image>
  </div>
</template>
<script>
import { mapActions, mapMutations } from "vuex";
import Search from "./components/search.vue";
import UiListCard from "@/components/ui-list-card";
import { getPersonPageList } from "@/api/realNameFile";
import SearchImage from "@/components/search-image/index.vue";
export default {
  components: { Search, UiListCard, SearchImage },
  props: {},
  data() {
    return {
      list: [],
      loading: false,
      pageForm: {
        dataType: 1,
      },
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      keyWords: "", // 关键字，从全景智搜跳转过来
    };
  },
  created() {
    this.keyWords = this.$route.query.keyWords || "";
    this.getList();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("common", ["setWisdomCloudSearchData"]),
    ...mapMutations("control", ["setPersonControl"]),
    async onControl(row) {
      if (row.photos && !row.photos.length)
        return this.$Message.error("图片不存在");
      this.setPersonControl({
        ...row,
        photoUrl: row.photos[0]["photoUrl"],
      });
      this.$router.push("/target-control/control-task/add");
    },
    async onSearchImage(src, row, currentIndex) {
      if (row.photos && !row.photos.length)
        return this.$Message.error("图片不存在");
      let picData = {
        algorithmType: "1", //1人脸 2 车辆 3 人体 4 非机动车
        similarity: 75, // 相似度
      };
      this.$refs.searchImage.searchImage(picData, row.photos[0]["photoUrl"]);
    },
    onSubmit({ algorithmType, similarity, urlList }) {
      let params = {
        keyWords: "",
        algorithmType: algorithmType,
        urlList: [urlList],
      };
      this.$router.push({
        path: "/wisdom-cloud-search/cloud-default-page",
        query: {
          params: params,
          type: 2,
        },
      });
    },
    /**
     * @description: 查询列表
     */
    getList() {
      this.loading = true;
      var param = {
        ...this.pageForm,
        ...this.params,
        searchValue: this.keyWords || undefined,
      };
      var labels = [];
      if (param.labels && param.labels.length > 0) {
        param.labels.forEach((item) => {
          labels.push(item.id);
        });
        param.labelIds = labels;
      }
      getPersonPageList(param)
        .then((res) => {
          const { entities, pageNumber, pageSize, total } = res.data;
          this.list = entities ? entities : [];
          this.params = {
            pageNumber: pageNumber,
            pageSize: pageSize,
          };
          this.total = total;
          // 重新获取页码后回到顶部
          document.querySelector(".card-content").scrollTop = 0;
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 档案详情
    archivesDetailHandle(item) {
      let query = {
        archiveNo: item.archiveNo,
        source: "people",
        initialArchiveNo: item.archiveNo,
      };
      const { href } = this.$router.resolve({
        name: "people-archive",
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
    // 卡片切换
    changeCardHandle(item, index) {},
    // 查询
    searchForm(form) {
      this.keyWords = ""; // 只要用户点击了查询，则之后的查询不再携带keyWords参数
      var labelIds = [];
      let formData = { ...form };
      if (form.labelIds && form.labelIds.length > 0) {
        form.labelIds.forEach((item) => {
          if (item && item != undefined) {
            labelIds.push(item.id);
          }
        });
        formData.labelIds = labelIds;
      }
      this.pageForm = {
        ...formData,
        dataType: 1,
        similarity: (form.similarity / 100).toFixed(2),
      };
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
// @import "~@/views/holographic-archives/style/page-hide.less";
.person {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
          background: #fff;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
}
</style>
