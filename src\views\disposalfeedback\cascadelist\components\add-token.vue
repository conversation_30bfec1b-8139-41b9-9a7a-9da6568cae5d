<template>
  <ui-modal v-model="visible" title="新增token" @onCancel="reset" :styles="styles">
    <div class="question-handle">
      <Form :label-width="80">
        <FormItem label="组织机构" prop="orgCode">
          <select-organization-tree :defaultCheckedKeys="defaultCheckedKeys" @check="check"></select-organization-tree>
        </FormItem>
        <FormItem label="行政区划" class="left-item" prop="regionCode">
          <api-area-tree
            class="api-area-tree"
            :select-tree="defaultTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区域"
          ></api-area-tree>
          <!-- <span v-else class="base-text-color">{{ selectPlace.regionName }}</span> -->
        </FormItem>
        <!-- <FormItem label="处理结果"
                  required>
          <Select v-model="form.timedtaskId"
                  placeholder="请选择指标"
                  class="width-slg"
                  @on-change="changeTaskName">
            <Option v-for="(item, index) in taskList"
                    :key="index"
                    :value="item.id">{{
              item.name
            }}</Option>
          </Select>
        </FormItem> -->
        <FormItem label="有效时间" required>
          <!-- <TimePicker
            type="time"
            :value="expiresTime"
            placeholder="Select time"
            style="width: 168px"
          ></TimePicker> -->
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
import cascadeList from '@/config/api/cascadeList';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      expiresTime: '',
      defaultTree: {
        regionCode: '',
      },
      defaultCheckedKeys: [],
      taskList: [],
      form: {
        finishResult: '已处理',
        finishSituation: '',
      },
      visible: false,
      styles: {
        width: '5.2rem',
      },
    };
  },
  created() {},
  updated() {},
  methods: {
    async addToken() {
      try {
        await this.$http.addToken(`${cascadeList.removeToken}`, { ids: row.id });
        this.initList();
      } catch (error) {
        console.log(error);
      }
    },
    selectedArea() {},
    changeTaskName() {},
    reset() {
      this.form.finishResult = '已处理';
      this.form.finishSituation = '';
    },
    check() {},
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    SelectOrganizationTree: require('@/api-components/select-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.question-handle {
  width: 100%;
}
.remark {
  width: 100%;
}
</style>
