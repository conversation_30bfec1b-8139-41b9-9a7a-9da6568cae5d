<!--
    * @FileDescription: 频繁夜出
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-27 11:07:23
 -->
<template>
  <div class="leftBox">
    <div class="search-box">
      <div class="title">
        <p>频繁夜出</p>
      </div>
      <div class="search_condition">
        <div class="search_form">
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">夜出天数:</p>
            </div>
            <div class="search_content">
              <InputNumber
                v-model="queryParams.dayNum"
                class="wrapper-input"
              ></InputNumber>
              <span class="ml-10">天及以上</span>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">开始时间:</p>
            </div>
            <div class="search_content">
              <DatePicker
                v-model="queryParams.st"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="开始时间"
                transfer
              ></DatePicker>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">结束时间:</p>
            </div>
            <div class="search_content">
              <DatePicker
                v-model="queryParams.et"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="结束时间"
                transfer
              ></DatePicker>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">夜间时段:</p>
            </div>
            <div class="search_content">
              <TimePicker
                v-model="queryParams.nightStartTime"
                format="HH:mm:ss"
              ></TimePicker>
              <span class="ml-5 mr-5">-</span>
              <TimePicker
                v-model="queryParams.nightEndTime"
                format="HH:mm:ss"
              ></TimePicker>
            </div>
          </div>
          <!-- <FormItem label="夜间时段:" prop="">
            <div class="flex">
              <TimePicker
                v-model="formData.nightStartTime"
                format="HH:mm:ss"
              ></TimePicker>
              <span class="ml-5 mr-5">-</span>
              <TimePicker
                v-model="formData.nightEndTime"
                format="HH:mm:ss"
              ></TimePicker>
            </div>
          </FormItem> -->
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车牌号码:</p>
            </div>
            <div class="search_content">
              <Input
                v-model="queryParams.plateNo"
                placeholder="请输入车牌号码"
                class="wrapper-input"
              ></Input>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车牌颜色:</p>
            </div>
            <div class="search_content">
              <ui-tag-select
                ref="bodyColor"
                @input="
                  (e) => {
                    input(e, 'plateColor');
                  }
                "
              >
                <ui-tag-select-option
                  v-for="(item, $index) in licensePlateColorList"
                  :key="$index"
                  effect="dark"
                  :name="item.dataKey"
                >
                  <div
                    v-if="licensePlateColorArray[item.dataKey]"
                    :title="item.dataValue"
                    :style="{
                      borderColor:
                        licensePlateColorArray[item.dataKey].borderColor,
                    }"
                    class="plain-tag-color"
                  >
                    <div
                      :style="licensePlateColorArray[item.dataKey].style"
                    ></div>
                  </div>
                </ui-tag-select-option>
              </ui-tag-select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆颜色:</p>
            </div>
            <div class="search_content">
              <Select v-model="queryParams.vehicleColor" placeholder="请选择">
                <Option
                  v-for="(item, $index) in bodyColorList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆类型:</p>
            </div>
            <div class="search_content">
              <Select v-model="queryParams.vehicleType" placeholder="请选择">
                <Option
                  v-for="(item, $index) in vehicleClassTypeList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆品牌:</p>
            </div>
            <div class="search_content">
              <Select v-model="queryParams.vehicleBrand" placeholder="请选择">
                <Option
                  v-for="(item, $index) in vehicleBrandList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆型号:</p>
            </div>
            <div class="search_content">
              <Input
                v-model="queryParams.vehicleSubBrand"
                placeholder="请输入车辆型号"
                class="wrapper-input"
              ></Input>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">选择设备:</p>
            </div>
            <div class="search_content">
              <div class="select-tag-button" @click="selectDevice()">
                选择设备/已选（{{ queryParams.selectDeviceList.length }}）
              </div>
            </div>
          </div>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
          </div>
        </div>
      </div>
    </div>

    <select-device
      ref="selectDevice"
      :showOrganization="true"
      @selectData="selectData"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import { getDaysBetween } from "@/util/modules/common";
export default {
  name: "",
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      queryParams: {
        plateNo: "", //皖C82550
        vehicleColor: "",
        plateColor: "",
        vehicleType: "",
        vehicleBrand: "",
        vehicleSubBrand: "",
        dayNum: 7,
        nightStartTime: "20:00:00", // 夜间时段 - 开始
        nightEndTime: "05:00:00", // 夜间时段 - 结束
        // plateBrands: []
        selectDeviceList: [],
      },
      licensePlateColorArray, // 车牌颜色
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
    }),
  },
  async created() {
    this.queryParams.st = this.getAgoDay(30);
    this.queryParams.et = this.getAgoDay(0);
    await this.getDictData();
    // 推荐中心查看
    console.log("taskParams: ", this.taskParams);
    if (!Toolkits.isEmptyObject(this.taskParams)) {
      this.queryParams.selectDeviceList =
        this.taskParams.params.selectDeviceList || [];
      if (this.taskParams.queryStartTime)
        this.queryParams.st = this.taskParams.queryStartTime;
      if (this.taskParams.queryEndTime)
        this.queryParams.et = this.taskParams.queryEndTime;
      if (this.taskParams.params)
        this.queryParams = { ...this.queryParams, ...this.taskParams.params };
      this.$nextTick(() => {
        if (this.taskParams.taskResult) this.handleSearch();
      });
    }
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    input(e, key) {
      this.queryParams[key] = e;
    },
    // 查询
    handleSearch() {
      this.queryParams.st = this.$dayjs(this.queryParams.st).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.queryParams.et = this.$dayjs(this.queryParams.et).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      let num = getDaysBetween(this.queryParams.st, this.queryParams.et);
      if (num < this.queryParams.dayNum) {
        this.$Message.error("夜出天数必须大于等于开始时间减去结束时间");
        return;
      }
      this.queryParams.deviceIds = this.queryParams.selectDeviceList.map(
        (v) => v.deviceGbId
      );
      this.$emit("search", this.queryParams);
    },
    // 时间判断
    getAgoDay(n) {
      let date = new Date();
      let newDate = new Date(date.getTime() - n * 24 * 60 * 60 * 1000);
      var Y = newDate.getFullYear() + "-";
      var M =
        (newDate.getMonth() + 1 < 10
          ? "0" + (newDate.getMonth() + 1)
          : newDate.getMonth() + 1) + "-";
      var D =
        newDate.getDate() < 10
          ? "0" + newDate.getDate() + " "
          : newDate.getDate() + " ";
      var h =
        newDate.getHours() < 10
          ? "0" + newDate.getHours() + ":"
          : newDate.getHours() + ":";
      var m =
        newDate.getMinutes() < 10
          ? "0" + newDate.getMinutes() + ":"
          : newDate.getMinutes() + ":";
      var s =
        newDate.getSeconds() < 10
          ? "0" + newDate.getSeconds()
          : newDate.getSeconds();
      return Y + M + D + h + m + s;
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.queryParams.selectDeviceList, "");
    },
    selectData(arr) {
      this.queryParams.selectDeviceList = arr;
      this.$forceUpdate();
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";

/deep/ .ivu-date-picker {
  width: 100%;
}
/deep/ .ivu-tag-select-option {
  margin-right: 5px !important;
}

.search_content {
  flex-wrap: nowrap !important;
}
</style>
