import { fontSize } from '@/util/module/common.js';
let fontSize12 = fontSize(12);
let fontSize14 = fontSize(14);
let lineHeight = fontSize(13);

export default {
  style1: {
    colorEnumObject: {
      blue: {
        color1: '#0087F9',
        color2: '#04A3FF',
        innerCircleBorder: '#035381',
        outerShadowColor: 'rgba(2, 123, 207, .3)',
        shadowColor: [
          {
            offset: 0,
            color: 'rgba(28,140,244,0)',
          },
          {
            offset: 0.5,
            color: 'rgba(28,140,244,0.8)',
          },
          {
            offset: 1,
            color: 'rgba(67,169,255,1)',
          },
        ],
      },
      red: {
        color1: '#C60235',
        color2: '#D80135',
        innerCircleBorder: '#83031D',
        outerShadowColor: 'rgba(247, 5, 52, .3)',
        shadowColor: [
          {
            offset: 0,
            color: 'rgba(218, 5, 44, 0)',
          },
          {
            offset: 0.3,
            color: 'rgba(218, 5, 44, 0)',
          },
          {
            offset: 0.5,
            color: 'rgba(218, 5, 44, 0.8)',
          },
          {
            offset: 1,
            color: 'rgba(255, 2, 110, 1)',
          },
        ],
      },
    },
    rich: {
      blue: {
        color: '#07C5FD',
        fontSize: fontSize14,
        fontWeight: 'bold',
        lineHeight: lineHeight,
      },
      red: {
        color: '#CF0131',
        fontSize: fontSize14,
        fontWeight: 'bold',
      },
      yellow: {
        color: '#FFE804',
        fontSize: fontSize14,
        fontWeight: 'bold',
      },
      green: {
        color: '#03EB7B',
        fontSize: fontSize14,
        fontWeight: 'bold',
      },
      normal: {
        color: '#89B9FA',
        fontSize: fontSize12,
        lineHeight: lineHeight,
      },
    },
    qualified: {
      color: ['rgba(0,186,255,1)', 'rgba(0,90,254,0.5)'],
      borderColor: ['rgba(157, 242, 255, 1)', 'rgba(0, 131, 253, 1)'],
      tooltipColor: '#005AFE',
    },
    unqualified: {
      color: ['rgba(241, 65, 111, .5)', 'rgba(198, 2, 53, 1)'],
      borderColor: ['rgba(255, 146, 176, 1)', 'rgba(251, 20, 79, 1)'],
      tooltipColor: '#F1416F',
    },
  },
  style2: {
    colorEnumObject: {
      blue: {
        color1: 'rgba(54, 205, 159, 1)',
        color2: 'rgba(54, 205, 159, 1)',
        innerCircleBorder: 'rgba(54, 205, 159, 0.5)',
        outerShadowColor: 'rgba(54, 205, 159, 0.3)',
        shadowColor: [
          {
            offset: 0,
            color: 'rgba(28,140,244,0)',
          },
          {
            offset: 0.5,
            color: 'rgba(54, 205, 159, 0.7)',
          },
          {
            offset: 1,
            color: 'rgba(54, 205, 159, 1)',
          },
        ],
      },
      red: {
        color1: 'rgba(247, 122, 78, 1)',
        color2: 'rgba(247, 122, 78, 1)',
        innerCircleBorder: 'rgba(247, 122, 78, 0.5)',
        outerShadowColor: 'rgba(247, 122, 78, 0.3)',
        shadowColor: [
          {
            offset: 0,
            color: 'rgba(218, 5, 44, 0)',
          },
          {
            offset: 0.3,
            color: 'rgba(218, 5, 44, 0)',
          },
          {
            offset: 0.5,
            color: 'rgba(247, 122, 78, 0.7)',
          },
          {
            offset: 1,
            color: 'rgba(247, 122, 78, 1)',
          },
        ],
      },
    },
    rich: {
      blue: {
        color: 'rgba(7, 198, 254, 1)',
        fontSize: fontSize14,
        fontWeight: 'bold',
        lineHeight: lineHeight,
      },
      red: {
        color: 'rgba(247, 122, 78, 1)',
        fontSize: fontSize14,
        fontWeight: 'bold',
      },
      yellow: {
        color: '#FFE804',
        fontSize: fontSize14,
        fontWeight: 'bold',
      },
      green: {
        color: 'rgba(54, 205, 159, 1)',
        fontSize: fontSize14,
        fontWeight: 'bold',
      },
      normal: {
        color: '#89B9FA',
        fontSize: fontSize12,
        lineHeight: lineHeight,
      },
    },
    qualified: {
      color: ['rgba(63, 239, 237, 1)', 'rgba(63, 239, 237, .5)'],
      borderColor: ['rgba(63, 239, 237, 1)', 'rgba(63, 239, 237, .5)'],
      tooltipColor: 'rgba(63, 239, 237, 1)',
    },
    unqualified: {
      color: ['rgba(251, 193, 131, 1)', 'rgba(251, 193, 131, .5)'],
      borderColor: ['rgba(251, 193, 131, 1)', 'rgba(251, 193, 131, .5)'],
      tooltipColor: 'rgba(251, 193, 131, 1)',
    },
  },
};
