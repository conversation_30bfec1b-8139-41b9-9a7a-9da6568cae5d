<template>
  <div class="tooltip-dom" v-if="!!toolTipItem">
    <slot></slot>
    <p class="tooltip-dom-title mb-xs">
      {{ toolTipItem?.name ?? '未知' }}
    </p>
    <slot></slot>
    <p class="tooltip-dom-title_assistant flex-row mb-xs" v-if="toolTipItem?.title">
      <span class="label inline ellipsis mr-sm">{{ toolTipItem?.title }}</span>
      <span class="percent inline ellipsis" v-if="toolTipItem?.titleNum">{{ toolTipItem?.titleNum }}</span>
    </p>
    <!--  v-for="(item, index) in toolTipData?.list ?? []" :key="index" -->
    <div class="tooltip-dom-content">
      <p class="tooltip-dom-content-p flex-row" v-for="(item, index) in toolTipItem.list" :key="index">
        <span class="label inline ellipsis" title="">
          <i class="point" :style="{ background: item?.color }"></i>
          {{ item.label }}
        </span>
        <span class="num inline ellipsis" title="" :style="{ color: item?.numColor ?? item?.color }"
          >{{ item.num }}
        </span>
        <span v-if="!!item.selfMes" class="percent inline ellipsis" title="" :style="{ color: item?.selfColor }"
          >{{ item.selfMes }}
        </span>
      </p>
    </div>
  </div>
</template>
<script>
export default {
  name: 'tooltipss-dom',
  props: {},
  data() {
    return {
      toolTipItem: null,
    };
  },
  mounted() {},
  methods: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tooltip-dom {
  background: rgba(0, 10, 41, 0.5);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  .tooltip-dom-title {
    font-size: 16px;
    line-height: 25px;
    height: 25px;
    font-weight: bold;
    padding: 0 4px;
  }
  .percent {
    font-weight: bold;
    font-size: 14px;
  }
  .tooltip-dom-title_assistant {
    flex: 1;
    padding: 0 4px;
    .percent {
      color: #2dfdff;
    }
  }
  .tooltip-dom-content {
    max-height: 80px;
    overflow-y: scroll;
    .point {
      width: 6px;
      height: 6px;
      background: #1b85ff;
    }
    .tooltip-dom-content-p {
      justify-content: space-between;
      // .percent {
      //   width: 80px
      // }
    }
  }
}
</style>
