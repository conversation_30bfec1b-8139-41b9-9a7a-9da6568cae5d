<!--
    * @FileDescription: 轨迹详情
    * @Author: H
    * @Date: 2023/1/6
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-10-15 15:42:52
 -->
<template>
  <div class="dom-wrapper">
    <div class="dom-box">
      <header>
        <span>抓拍详情</span>
        <ui-icon
          type="close"
          :size="14"
          @click.native="() => $emit('close')"
        ></ui-icon>
      </header>
      <carousel
        ref="carousel"
        :same-position-point="samePositionPoint"
        @changeVid="changeVid"
      ></carousel>
      <section class="dom-content">
        <div class="dom-info">
          <div class="dom-info-left" style="width: 200px">
            <div class="smallImg" style="width: 200px; height: inherit">
              <img v-lazy="trackInfo.traitImg" alt="" />
            </div>
            <span class="license-plate-small similarity" v-if="trackInfo.score"
              >{{ trackInfo.score }}%</span
            >
            <div class="title">
              <span
                :class="checkStatus ? 'active' : ''"
                @click="tabsChange(true)"
                >{{
                  trackInfo.headType == "face" ? "抓拍记录" : "通行记录"
                }}</span
              >
              <span
                :class="!checkStatus ? 'active' : ''"
                @click="tabsChange(false)"
                >{{ tabName[trackInfo.headType].right }}</span
              >
            </div>
            <div class="traffic-record" v-if="checkStatus">
              <div
                class="dom-content-p"
                v-for="(val, key, index) in captureRecord"
                :key="index"
              >
                <span class="label">{{ key }}</span
                ><span>：</span>
                <!-- <span class="message" v-show-tips>{{ trackInfo[val[0]] || trackInfo[val[1]] ||  '--' }}</span> -->
                <!-- <div v-if="val.includes('sbgnlx')" v-show-tips>
                  <span v-if="!Array.isArray(facilitySplit(trackInfo.sbgnlx))">
                    {{
                      trackInfo.sbgnlx
                        | commonFiltering(translate("sbgnlxList"))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(trackInfo.sbgnlx)"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate("sbgnlxList"))
                    }}{{
                      inde + 1 < facilitySplit(trackInfo.sbgnlx).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </div>
                <ui-textOver-tips
                  v-else
                  class="message"
                  :content="trackInfo[val[0]] || trackInfo[val[1]] || '--'"
                ></ui-textOver-tips> -->
                <ui-textOver-tips
                  class="message"
                  :content="trackInfo[val[0]] || trackInfo[val[1]] || '--'"
                ></ui-textOver-tips>
              </div>
              <structuredmsg
                :info="
                  trackInfo.headType == 'face'
                    ? trackInfo
                    : trackInfo.vehicleStructuralVo
                "
                :type="trackInfo.headType == 'face' ? '1' : '2'"
              ></structuredmsg>
            </div>
            <div class="personnel-files" v-else>
              <div class="dom-content-p">
                <span class="label">{{
                  trackInfo.headType == "face" ? "视频身份" : "车牌号码"
                }}</span
                ><span>：</span>
                <span
                  class="address"
                  v-if="trackInfo.headType == 'face'"
                  :class="{ identity: trackInfo.vid }"
                  @click="toDetail(trackInfo, 1)"
                  >{{ trackInfo.vid || "--" }}</span
                >
                <span
                  class="address"
                  v-else
                  :class="{ plateNo: trackInfo.plateNo }"
                  @click="toDetail(trackInfo, 2)"
                  >{{ trackInfo.plateNo || "--" }}</span
                >
              </div>
              <div
                class="dom-content-p"
                v-for="(item, index) in requestList"
                :key="index"
              >
                <span class="label">{{ item.name }}</span
                ><span>：</span>
                <!-- <span :class="item.calssname ? item.calssname : 'message'">
                                    {{ item.dic ? (trackArchives[item.field] | commonFiltering(item.dic)) : (trackArchives[item.field] || '--')}}
                                </span> -->
                <span class="message" v-if="item.field == 'vehicleType'">
                  {{
                    trackArchives.vehicleType | commonFiltering(vehicleTypeList)
                  }}
                </span>
                <span
                  class="message"
                  v-else-if="item.field == 'vehicleBrandCN'"
                >
                  {{
                    trackArchives.vehicleBrandCN
                      | commonFiltering(vehicleBrandList)
                  }}
                </span>
                <span
                  :class="item.calssname ? item.calssname : 'message'"
                  v-else
                >
                  {{ trackArchives[item.field] || "--" }}
                </span>
              </div>
              <!-- <div class="dom-content-p">
                                <span class="label">车辆类型</span><span>：</span>
                                <span class="message">
                                    
                                </span>
                            </div>
                            <div class="dom-content-p">
                                <span class="label">车辆品牌</span><span>：</span>
                                <span class="message">{{ vehicleArchives.vehicleBrandCN || '--' }}
                                </span>
                            </div>
                            <div class="dom-content-p">
                                <span class="label">机动车主</span><span>：</span>
                                <span class="message">{{ vehicleArchives.motorists || '--' }}</span>
                            </div>
                            <div class="dom-content-p">
                                <span class="label">身份证号</span><span>：</span>
                                <span class="address">{{ vehicleArchives.idcardNo || '--' }}</span>
                            </div>
                            <div class="dom-content-p">
                                <span class="label">联系电话</span><span>：</span>
                                <span class="message">{{ vehicleArchives.phone || '--' }}</span>
                            </div>
                            <div class="dom-content-p">
                                <span class="label">登记地址</span><span>：</span>
                                <span class="address">{{ vehicleArchives.djdz || '--' }}</span>
                            </div> -->
            </div>
          </div>
          <div class="dom-info-right">
            <ui-image :src="trackInfo.sceneImg" alt="静态库" viewer />
          </div>
        </div>
      </section>
    </div>
    <footer>
      <!-- <search-around v-if="cutIcon" @preDetial="preDetial" @nextDetail="nextDetail"></search-around> -->
    </footer>
  </div>
</template>

<script>
import {
  getCarInfoByCarNo,
  getPersonInfoByPersonId,
} from "@/api/operationsOnTheMap";
import { mapActions, mapGetters, mapMutations } from "vuex";
import carousel from "./carousel.vue";
import structuredmsg from "@/components/mapdom/structuredmsg.vue";
// import { commonMixins } from "@/mixins/app.js";
export default {
  name: "",
  // mixins: [commonMixins], //全局的mixin
  components: {
    carousel,
    structuredmsg,
  },
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      trackInfo: {
        headType: "face",
      },
      vehicleArchives: {},
      trackArchives: {},
      captureRecord: {},
      face: {
        抓拍地点: ["deviceName", "captureAddress"],
        抓拍时间: ["absTime", "captureTime"],
        // 设备类型: ["sbgnlx"],
      },
      vehicle: {
        通过地点: ["deviceName", "captureAddress"],
        通过时间: ["absTime", "captureTime"],
        // 设备类型: ["sbgnlx"],
      },
      requestList: [],
      faceRight: [
        {
          name: "姓名",
          field: "xm",
        },
        {
          name: "联系方式",
          field: "lxdh",
        },
        {
          name: "身份证号",
          field: "gmsfhm",
          calssname: "address",
        },
        {
          name: "家庭住址",
          field: "xzz_mlxxdz",
          calssname: "address",
        },
      ],
      vehicleRight: [
        {
          name: "车辆类型",
          field: "vehicleType",
          dic: "vehicleTypeList",
        },
        {
          name: "车辆品牌",
          field: "vehicleBrandCN",
        },
        {
          name: "机动车主",
          field: "motorists",
        },
        {
          name: "身份证号",
          field: "idcardNo",
          calssname: "address",
        },
        {
          name: "联系电话",
          field: "phone",
        },
        {
          name: "登记地址",
          field: "djdz",
          calssname: "address",
        },
      ],
      tabName: {
        face: { right: "人员档案" },
        vehicle: { right: "车辆档案" },
      },
      checkStatus: true, // 抓拍记录，人员档案tab切换
      samePositionPoint: [],
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      getPeerList: "map/getPeerList",
      getPeerData: "map/getPeerData",
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * pointItem: 本条数据
     * item：地图撒点数据集合（对象信息，检索结果）
     * type：是否点击列表
     */
    init(pointItem, item = [], type = "", menuType = "") {
      return new Promise((resolve) => {
        this.checkStatus = true;
        this.trackInfo = { ...pointItem };
        this.headList();
        if (this.trackInfo.headType == "face") {
          this.captureRecord = this.face;
        } else {
          this.captureRecord = this.vehicle;
        }
        resolve({ data: true });
      });
    },
    // 头部展示数据处理
    headList() {
      this.samePositionPoint = this.getPeerList.filter((ite, index) => {
        if (ite.deviceId === this.trackInfo.deviceId) {
          ite.Index = index + 1;
        }
        return ite.deviceId === this.trackInfo.deviceId;
      });
      let tabIndex = 0;
      this.samePositionPoint.map((item, index) => {
        if (item.recordId == this.trackInfo.recordId) {
          tabIndex = index;
        }
      });
      this.$refs.carousel.init(tabIndex);
    },
    // 通行记录/车辆档案 切换
    async tabsChange(state) {
      if (this.checkStatus == state) {
        return;
      }
      this.checkStatus = state;
      if (!this.checkStatus) {
        if (this.trackInfo.headType == "face") {
          this.requestList = this.faceRight;
          if (!this.trackInfo.vid) {
            this.$Message.warning("档案不存在！");
            return;
          }
          try {
            let res = await getPersonInfoByPersonId({
              vid: this.trackInfo.vid,
            });
            if (res.code === 200) {
              this.trackArchives = res.data || {};
            }
          } catch (error) {
            this.$Message.warning("档案暂无数据");
          }
        } else {
          this.requestList = this.vehicleRight;
          if (!this.trackInfo.plateNo) {
            this.$Message.warning("档案不存在！");
            return;
          }
          try {
            let res = await getCarInfoByCarNo({
              // 这里需要携带车牌颜色
              carNo: this.trackInfo.plateNo + "_" + this.trackInfo.plateColor,
            });
            if (res.code === 200 && !!res.data) {
              this.trackArchives = res.data;
            } else {
              throw error;
            }
          } catch (error) {
            this.$Message.warning("档案暂无数据");
          }
        }
      }
    },
    toDetail(item, type) {
      if (type === 1) {
        if (!item.vid) {
          return;
        }
        let query = {
          archiveNo: item.vid,
          source: "video",
          initialArchiveNo: item.vid,
        };
        const { href } = this.$router.resolve({
          name: "video-archive",
          query,
        });
        sessionStorage.setItem("query", JSON.stringify(query));
        window.open(href, "_blank");
      } else {
        if (!item.plateNo) {
          return;
        }
        const { href } = this.$router.resolve({
          name: "vehicle-archive",
          query: {
            archiveNo: JSON.stringify(this.trackArchives.archiveNo),
            plateNo: JSON.stringify(item.plateNo),
            source: "car",
            idcardNo: item.idcardNo,
          },
        });
        window.open(href, "_blank");
      }
    },
    // 顶部切换
    changeVid(item) {
      this.$emit("changeListTab", { ...item });
      this.trackInfo = { ...item };
      this.checkStatus = true;
      if (this.trackInfo.headType == "face") {
        this.captureRecord = this.face;
      } else {
        this.captureRecord = this.vehicle;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import "./index";
</style>
