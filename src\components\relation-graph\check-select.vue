<template>
  <Collapse simple class="check-select" v-model="value1">
    <Panel name="1">
      <Checkbox @click.prevent.stop.native="handleCheckAll" :value="checkAll">全部类型</Checkbox>
      <div slot="content">
        <CheckboxGroup v-model="checkAllGroup" @on-change="changeCheckAll">
          <div v-for="item in atlasListChilren" :key="item.id" class="check-select-item">
            <Checkbox :label="item.id">{{ item.text }} ( {{ item.num }} )</Checkbox>
          </div>
        </CheckboxGroup>
      </div>
    </Panel>
  </Collapse>
</template>
<script>
export default {
  components: {},
  props: {
    atlasListChilren: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      checkAll: true,
      checkAllGroup: [],
      value1: '1'
    }
  },
  computed: {},
  watch: {
    atlasListChilren: {
      deep: true,
      handler(val) {
        this.checkAllGroup = []
        this.checkAllGroup = this.atlasListChilren.map(val => {
          return val.id
        })
      },
      immediate: true
    }
  },
  filter: {},
  created() {},
  mounted() {
    this.init()
  },
  methods: {
    // 获取
    init() {},
    // 全选
    handleCheckAll() {
      this.checkAll = !this.checkAll
      if (!this.checkAll) {
        this.checkAllGroup = []
      } else {
        this.checkAllGroup = this.atlasListChilren.map(val => {
          return val.id
        })
      }
      this.$emit('checkSelect', this.checkAllGroup)
    },
    // 单选
    changeCheckAll(checkArray) {
      if (checkArray.length === this.atlasListChilren.length) {
        this.checkAll = true
      } else {
        this.checkAll = false
      }
      this.$emit('checkSelect', this.checkAllGroup)
    }
  }
}
</script>
<style lang="less" scoped>
.check-select {
  position: absolute;
  top: 10px;
  width: 230px;
  right: 10px;
  z-index: 11;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  .check-select-item {
    margin-top: 20px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
  }
  /deep/ .ivu-collapse-header {
    border-bottom: 1px solid #d3d7de !important;
    .ivu-icon {
      float: right;
      position: relative;
      top: 9px;
      font-size: 20px;
      &::before {
        content: '\F33D';
      }
    }
  }
  /deep/ .ivu-collapse-item-active > .ivu-collapse-header > i {
    transform: rotate(180deg);
  }
}
</style>
