<template>
  <div class="zdr-container">
    <Dropdown
      trigger="custom"
      :visible="visible"
      transfer
      transfer-class-name="zdr-filter-dropdown"
      ref="zdrFilterRef"
      @on-click="onClick"
    >
      <div @click="openFn" class="pointer down-text" id="dropdown-text">
        <span id="dropdown-text-item1">{{ getTitle }}</span>
        <Icon id="dropdown-text-item2" type="md-arrow-dropdown" />
      </div>
      <DropdownMenu slot="list" v-clickoutside="clickoutsideFn">
        <DropdownItem
          v-for="(item, index) in list"
          :key="index"
          :selected="timeInfo.value === item.value"
          :name="item.value"
          class="ellipsis"
        >
          <!-- 自定义 -->
          <div v-if="item.value === 'defined'">
            <span class="span-label">{{ item.label }}</span>
            <div v-if="isDefined" class="mt-sm" @click.stop>
              <DatePicker
                v-model="timeInfo.startTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="开始时间"
                :options="startTime"
                :class="{ 'error-tip': isSubmit && !timeInfo.startTime && !timeInfo.endTime }"
                @on-change="startChangeTime"
              >
              </DatePicker>
              <span class="mr-xs ml-xs font-blue">--</span>
              <DatePicker
                v-model="timeInfo.endTime"
                format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="结束时间"
                :options="endTime"
                :class="{ 'error-tip': isSubmit && !timeInfo.startTime && !timeInfo.endTime }"
                @on-change="endChangeTime"
              >
              </DatePicker>
              <div class="btn-box">
                <Button class="mini-btn" @click="clearTime">清空</Button>
                <Button class="mini-btn ml-sm" type="primary" @click="submitTime">确定</Button>
              </div>
            </div>
          </div>
          <span v-else class="span-label">{{ item.label }}</span>
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'zdr-filter',
  components: {},
  props: {
    defaultTimeInfo: {
      type: Object,
      default: () => {
        return {
          value: 'all',
          startTime: '',
          endTime: '',
        };
      },
    },
  },
  data() {
    return {
      visible: false,
      list: [
        { label: '全部', value: 'all' },
        { label: '今日', value: 'today' },
        { label: '7天', value: 'week' },
        { label: '30天', value: 'month' },
        { label: '自定义', value: 'defined' },
      ],
      currenSelected: 'all',
      timeInfo: {
        value: 'all',
        startTime: '',
        endTime: '',
      },
      isDefined: false,
      startTime: {
        disabledDate: (date) => {
          let endTime = new Date(this.timeInfo.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTime: {
        disabledDate: (date) => {
          let startTime = new Date(this.timeInfo.startTime);
          if (startTime) {
            return date < startTime.getTime();
          }
          return false;
        },
      },
      isSubmit: false,
    };
  },
  computed: {
    getTitle() {
      let arr = this.list.filter((item) => item.value === this.currenSelected);
      return arr[0]?.label;
    },
  },
  mounted() {
    this.$nextTick(() => {
      // 解决全屏screenfull下，一些transfer弹框不展示问题
      let bigParent = this.$refs.zdrFilterRef?.$parent?.$parent?.$parent?.$parent?.$el;
      let dropdownTransfer = document.querySelectorAll('.zdr-filter-dropdown');
      if (!bigParent || !dropdownTransfer) return;
      dropdownTransfer.forEach((dom) => {
        bigParent.appendChild(dom);
      });
    });
  },
  watch: {
    defaultTimeInfo: {
      handler(val) {
        this.isDefined = false;
        if (val.value === 'defined') {
          this.isDefined = true;
        }
        this.currenSelected = val.value;
        this.timeInfo = this.$util.common.deepCopy(val);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    clickoutsideFn(event) {
      if (['dropdown-text', 'dropdown-text-item1', 'dropdown-text-item2'].includes(event?.srcElement?.id)) {
        this.visible = true;
        return;
      }
      this.closeFn();
    },
    openFn() {
      this.visible = !this.visible;
      this.timeInfo.value = this.currenSelected;
      this.isDefined = false;
      if (this.timeInfo.value === 'defined') {
        this.isDefined = true;
      }
      this.isSubmit = false;
      this.updatePosition();
    },
    closeFn() {
      this.visible = false;
    },
    onClick(name) {
      this.isDefined = false;
      this.updatePosition();
      this.timeInfo.value = name;
      if (name === 'defined') {
        this.timeInfo.startTime = '';
        this.timeInfo.endTime = '';
        this.isDefined = true;
        return;
      } else {
        this.$util.common.quickDate(this.timeInfo);
      }
      this.currenSelected = name;
      this.$emit('zdrSelectedFn', this.timeInfo);
      this.closeFn();
    },
    startChangeTime(time) {
      this.timeInfo.startTime = time;
    },
    endChangeTime(time) {
      this.timeInfo.endTime = time;
    },
    clearTime() {
      this.timeInfo.startTime = '';
      this.timeInfo.endTime = '';
      this.isSubmit = false;
    },
    submitTime() {
      this.isSubmit = false;
      if (!this.timeInfo.startTime && !this.timeInfo.endTime) {
        this.isSubmit = true;
        return;
      }
      this.timeInfo.value = 'defined';
      this.currenSelected = 'defined';
      this.$emit('zdrSelectedFn', this.timeInfo);
      this.closeFn();
    },
    // 更新定位
    updatePosition() {
      this.$refs.zdrFilterRef?.$refs?.drop?.['_events']?.['on-update-popper'][0]();
    },
  },
};
</script>
<style lang="less">
.zdr-filter-dropdown.ivu-select-dropdown {
  max-height: 700px !important;
  overflow: initial !important;
}
</style>
<style lang="less" scoped>
.zdr-container {
  position: relative;
  display: inline-block;
}
.zdr-filter-dropdown {
  .span-label {
    font-size: 12px;
    color: #ffffff;
  }
  .ivu-date-picker {
    width: 175px !important;
  }
}
.down-text {
  color: #63ccfc;
  font-size: 12px;
}
.btn-box {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 8px 0;
  .mini-btn {
    height: 28px;
    line-height: 28px;
    font-size: 12px;
  }
}
@{_deep}.error-tip .ivu-input {
  border: 1px solid #ed4014;
}
</style>
