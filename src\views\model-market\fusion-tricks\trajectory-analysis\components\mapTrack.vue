<!--
    * @FileDescription: 地图轨迹
    * @Author: H
    * @Date: 2022/12/19
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="map-track" :class="{'map-track-pack': packUpDown}">
         <div class="title">
            <p>地图轨迹</p>
            <img :class="{packArrow: packUpDown}" :src="packUrl" alt="" @click="handlePackup">
        </div>
        <div class="map-select">
            <div class="check-all">
                <Checkbox :indeterminate="isIndeterminate" :value="checkAll" @click.prevent.native="handleCheckAllChange">全选</Checkbox>
            </div>
            <ul class="check-ul">
                <li class="check-li" 
                    :class="{'check-li-active': activeIndex == index}" 
                    v-for="(item, index) in types" 
                    :key="index">
                    <Checkbox v-model="checkedType[index]" @on-change="handleCheckedChange" ></Checkbox>
                    <div class="check-title" @click="handleType(item,index)">
                        <p class="line" :style="{'background': item.color}"></p>
                        <p class="map-title-name">{{ item.name }}</p>
                        <p class="triangle" :class="{'active-triangle': index == activeIndex}"></p>
                    </div>
                </li>
            </ul>
        </div>
        <!-- v-if="typeModal" -->
        <div class="suspend" v-if="typeModal">
            <el-checkbox-group v-model="checkedList" @change="handleCheckedCitiesChange">
                <el-checkbox v-for="(item, index) in mapline[typeList[activeIndex]].list" :label="item" :key="index">
                </el-checkbox>
            </el-checkbox-group>
        </div>
    </div>
</template>

<script>
import { trajectoryData } from '@/api/modelMarket';
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            isIndeterminate: false,
            checkAll: true,
            checkedCities:[],
            types: [
                { 'name': '人脸', 'color':'#2C86F8', 'type': 'vids' }, 
                { 'name': '车辆', 'color':'#1FAF81', 'type': 'plateNos' }, 
                { 'name': 'RFID', 'color':'#F29F4C', 'type': 'RFID' }, 
                { 'name': 'MAC', 'color':'#48BAFF', 'type': 'MAC' }, 
                { 'name': 'ISMI', 'color':'#AD48FF', 'type': 'IMSI' },
            ],
            typeList:['vids', 'plateNos', 'RFID', 'MAC', 'IMSI'],
            packUpDown: false,
            triIndex: 1,
            activeIndex: -1,
            single: false,
            mapline: {
                'vids':{
                    'checkedlist': [], 'list': []
                },
                'plateNos':{
                    'checkedlist': [], 'list': []
                },
                'RFID':{
                    'checkedlist': [], 'list': []
                },
                'MAC':{
                    'checkedlist': [], 'list': []
                },
                'IMSI':{
                    'checkedlist': [], 'list': []
                }
            },
            checkedList:[],
            typeModal: false,
            checkedType:[true,true,true,true,true],
            queryFrom: {},
            querySearch: {}
        }
    },
    watch:{
            
    },
    computed:{
        // checkedList() {
        //     return this.mapline[this.types[this.activeIndex].type].list
        // }  
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        // 获取选择的关联对象
        init(val, search) {
            this.queryFrom = val;
            this.querySearch = search;
            this.types.forEach((item, index) => {
                let type = {
                    'checkedlist':val[item.type],
                    'list': val[item.type]
                }
                this.mapline[item.type] = type;
            })
            if(this.activeIndex > -1) {
                this.checkedList = this.mapline[this.typeList[this.activeIndex]].checkedlist;
            }
            // this.typeModal = true;
            // 获取地图数据
            this.handleTrack()
        },
        // 点位上图 地图数据
        handleTrack() {
            let params = {
                dataRange: this.querySearch.dateType,
                endDate:  this.querySearch.endDate,
                startDate: this.querySearch.startDate,
                ...this.queryFrom
            };
            trajectoryData(params)
            .then(res => {
                this.$emit('mapline', res.data || {})
            })
        },
        // 选择类型
        handleType(item, index) {
            // 未选择不可点击
            if(!this.checkedType[index]) {
                return;
            }
            // 数据为空不可点击
            if(this.mapline[this.typeList[index]].checkedlist.length == 0) {
                this.typeModal = false;
                return;
            }
            if(this.activeIndex == index) {
                this.typeModal = !this.typeModal;
            }else{
                this.typeModal = true;
            }
            this.activeIndex = index;
            
            this.checkedList = [];
            let list = this.mapline[this.typeList[this.activeIndex]].checkedlist;
            list.map((item, index) => {
                this.$set(this.checkedList, index, item)
            })
        },
        // 全选
        handleCheckAllChange(val) {
            if (this.isIndeterminate) {
                this.checkAll = false;
            } else {
                this.checkAll = !this.checkAll;
            }
            this.isIndeterminate = false;
            if (this.checkAll) { //全选
                this.checkedType = [true, true, true, true, true];
                this.dataTreating()
                this.handleTrack();
            } else { //全不选
                this.checkedType = [false, false, false, false, false];
                this.$emit('mapline', {})
            }
        },
        // 单选
        handleCheckedChange(val) {
            this.dataTreating()
            let data = this.checkedType.filter(item => item);
            if (data.length === 5) { //全选
                this.isIndeterminate = false;
                this.checkAll = true;
                this.handleTrack();
            } else if (data.length > 0) { 
                this.isIndeterminate = true;
                this.checkAll = false;
                this.handleTrack();
            } else { // 全不选
                this.isIndeterminate = false;
                this.checkAll = false;
                this.$emit('mapline', {})
            }
        },
        // 单选、多选后处理传参
        dataTreating (){
            this.checkedType.forEach((item, index) => {
                if(!item) {
                    this.mapline[this.typeList[index]].checkedlist = [];
                }else {
                    this.mapline[this.typeList[index]].checkedlist = this.mapline[this.typeList[index]].list;
                }
                this.queryFrom[this.typeList[index]] = this.mapline[this.typeList[index]].checkedlist;
            })
        },
        // 设置选择撒点(人脸， 车辆，)
        handleCheckedCitiesChange(data) {
            if(this.checkedList.length == 0) {
                this.checkedType[this.activeIndex] = false;
                this.typeModal = false;
            }
            this.mapline[this.typeList[this.activeIndex]].checkedlist = this.checkedList;
            let search = {};
            for(let key in this.mapline) {
                search[key] = this.mapline[key].checkedlist;
            }
            this.queryFrom = search;
            this.handleTrack();
        },
        // 展开、收起
        handlePackup() {
            this.packUpDown = !this.packUpDown;
        }
    }
}
</script>

<style lang='less' scoped>
.map-track{
    position: absolute;
    top: 10px;
    left: 390px;
    background: #fff;
    width: 130px;
    height: 230px;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    transition: height 0.2s ease-out;
    // overflow: hidden;
    .title{
        font-size: 14px;
        font-weight: bold;
        color: rgba(0,0,0,0.9);
        display: flex;
        padding: 10px 15px;
        justify-content: space-between;
        img{
            width: 18px;
            height: 20px;
            cursor: pointer;
            transform: rotate(0deg);
            transition: transform 0.2s;
        }
        .packArrow{
            transform: rotate(180deg);
            transition: transform 0.2s;
        }
    }
    .map-select{
        // padding: 0 0 0 15px;
        height: 188px;
        transition: height 0.2s ease-out;
        overflow: hidden;
        .check-all{
            padding-left: 15px;
        }
        .check-ul{
            .check-li{
                display: flex;
                width: 100%;
                // background: rgba(35, 168, 249, .1);
                align-items: center;
                padding: 6px 0;
                padding-left: 15px;
                .check-title{
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    .line{
                        width: 20px;
                        height: 3px;
                        border-radius: 2px; 
                        margin-right:10px;
                    }
                    .map-title-name{
                        width: 32px;
                        font-size: 14px;
                        color: rgba(0,0,0,0.6);
                    }
                    .triangle {
                        width: 0;
                        height: 0;
                        border-left: 5px solid #888888;
                        border-top: 5px solid transparent;
                        border-bottom: 5px solid transparent;
                        margin-left: 10px;
                        display: none;
                    }
                    .active-triangle{
                        display: block;
                    }
                }
            }
            .check-li-active{
                background: rgba(35, 168, 249, .1);
            }
        }
    }
    .suspend{
        width: 210px;
        // height: 88px;
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        filter: blur(0px);
        position: absolute;
        left: 140px;
        top: 80px;
        padding: 10px 15px;
    }
    .suspendShow{
        display: block;
    }
}
/deep/ .ivu-checkbox-wrapper{
    // display: flex;
    // align-items: center;
    // margin-bottom: 10px;
}
.map-title{
    display: flex;
    align-items: center;
    
    
}
.map-track-pack{
    height: 40px;
    overflow: hidden;
    transition: height 0.2s ease-out;
    .map-select{
        height: 0px;
        transition: height 0.2s ease-out;
    }
}
</style>
