<!--
    * @FileDescription: 大屏
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="large-screen">
        <Dropdown class="i-layout-header-user" @on-click="handleClick" transfer >
            <ui-icon type="daping" :size="20" :color="'#fff'"></ui-icon>
            <DropdownMenu slot="list">
                <template v-for="(item, index) in screenList">
					<DropdownItem :key="index" @click.native="handleScreen(item, index)">
                        <ui-icon :type="item.iconType" :size="20" :color="'#a9abad'"></ui-icon>
						{{ item.name }}
					</DropdownItem>
				</template>
                <!-- <DropdownItem>
                    <span @click="handleScreen">数据专题大屏</span>
                    <span @click="handleScreen">设备专题大屏</span>
                    <span @click="handleScreen">解析专题大屏</span>
                    <span @click="handleScreen">建设态势大屏</span>
                </DropdownItem> -->
            </DropdownMenu>
        </Dropdown>
    </div>
</template>
<script>
export default{
    data() {
        return{
            screenList: [
                {'name': '数据专题大屏', label: '0', 'pageName': 'data-screen', iconType: 'shujuzhuanti'},
                {'name': '设备专题大屏', label: '1', 'pageName': 'device-screen', iconType: 'shebeizhuanti'},
                {'name': '解析专题大屏', label: '2', 'pageName': 'analysis-screen', iconType: 'jiexizhuanti'},
                {'name': '建设态势大屏', label: '3', 'pageName': 'construction-screen', iconType: 'jianshetaishi'},
            ]
        }
    },
    methods:{
        handleClick() {

        },
        handleScreen(item, index) {
            let pageName = item.pageName;
            const { href } = this.$router.resolve({
                name: pageName,
                query: {
                    screen: index+1,
                },
            });
            this.$util.openNewPage(href, "_blank");
        },
    }
}
</script>
<style lang="less" scoped>

</style>