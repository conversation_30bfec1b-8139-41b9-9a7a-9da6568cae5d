<template>
  <div ref="poolCloud" class="d3-cloud">
    <div :style="{transform: 'translate('+transLeft+'px, '+transTop+'px)'}" class="cloud-main"></div>
    <div v-show="labelName" :style="{left: labelLeft+'px', top: labelTop+'px'}" class="label-tip">{{ labelName }}</div>
  </div>
</template>
<script>
  import * as d3 from 'd3'
  import { cloud } from './d3-layout-cloud'
  export default {
    props: {
      labelList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        height: 0,
        labelName: '',
        labelLeft: 0,
        labelTop: 0,
        cloudWidth: 0,
        cloudHeight: 0,
        transLeft: 0,
        transTop: 0
      }
    },
    mounted () {
      this.$nextTick(() => {
        this.cloudWidth = this.$refs.poolCloud.clientWidth
        this.cloudHeight = this.$refs.poolCloud.clientHeight - 10
        this.height = document.body.clientHeight
        this.transLeft = this.cloudWidth / 2 - ((this.cloudWidth / 2) * 0.1)
        this.transTop = this.cloudHeight / 2 - ((this.cloudHeight / 2) * 0.07)
        let padding = 40
        if (this.height<800) {
          padding = 10
        }
        var layout = cloud()
          .size([this.cloudWidth, this.cloudHeight]) // size([x,y]) 词云显示的大小
          .words(this.labelList)
          .padding(padding)
          .rotate(function () { return ~~(Math.random() * 2) * 0 })
          .fontSize(function (d) { return 20 })
          .on('end', this.draw)
          layout.start()
      })
    },
    methods: {
      draw (words) {
        let fontSize = 16
        let lineHeight = 24
        let rectWidth = 3
        let rectHeight = 14
        let rectLeft = -1.5
        let rectRight = -2.5
        let rectTop = 5
        if (this.height<800) {
          fontSize = 13
          lineHeight = 20
          rectWidth = 2
          rectHeight = 12
          rectLeft = -1
          rectRight = -1
          rectTop = 4
        }
        const _that = this
        const entry = d3.select('.cloud-main')
          .selectAll('div')
          .data(words)
          .enter().append('div')
          .attr('ref', function (d) { return 'label' + d.value })
          .style('color', '#fff')
          .style('background', function (d) { return _that.$util.common.colorRgb(d.color, 0.15) })
          .style('font-size', fontSize + 'px')
          .style('line-height', lineHeight + 'px')
          .style('position', 'absolute')
          .style('top', function (d) { return d.y + 'px' })
          .style('left', function (d) { return d.x - 2 + 'px' })
          .style('border', function (d) { return `1px solid ${d.color}` })
          .style('border-radius', '2px')
          .style('padding', '0px 16px')
          .style('cursor', 'pointer')
          .text(function (d) { return d.name })
          .on('click', function ($event, d) { _that.labelClick($event, d) })
          .on('mouseover', function ($event, d, i) { _that.mouseoverLabel($event, d) })
          .on('mouseout', function ($event, d) { _that.mouseoutLabel($event, d) })
          entry.append('div')
          .style('width', rectWidth + 'px')
          .style('height', rectHeight + 'px')
          .style('background', function (d) { return d.color })
          .style('position', 'absolute')
          .style('left', rectLeft + 'px')
          .style('top', rectTop + 'px')
          entry.append('div')
          .style('width', rectWidth + 'px')
          .style('height', rectHeight + 'px')
          .style('background', function (d) { return d.color })
          .style('position', 'absolute')
          .style('right', rectRight + 'px')
          .style('top', rectTop + 'px')
      },
      labelClick (e, item) {
        this.$router.push({
          path: '/label-management/label-info',
          query: {
            id: item.labelId,
            curName: item.name
          }
        })
      },
      mouseoverLabel (e, item) {
        if (item.name && item.name.length > 8) {
          this.labelLeft = e.clientX
          this.labelTop = e.clientY
          this.labelName = item.labelName
        } else {
          this.labelName = ''
        }
      },
      mouseoutLabel () {
        this.labelName = ''
      }
    }
  }
</script>
<style lang="less" scoped>
 .d3-cloud {
   width: 100%;
   height: 100%;
 }
 .label-tip {
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    padding: 6px 20px;
    color: #fff;
    font-size: 16px;
    border-radius: 4px;
    line-height: 24px;
    z-index: 10;
  }
</style>