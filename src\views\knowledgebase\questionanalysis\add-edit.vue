<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles" :loading="submitLoading">
    <Form
      class="add-edit-form"
      ref="formRef"
      inline
      autocomplete="off"
      label-position="right"
      :label-width="80"
      :rules="ruleCustom"
      :model="formCustom"
    >
      <FormItem required prop="indexModule" :label="`数据类型`">
        <Select class="input-width" v-model="formCustom.indexModule" placeholder="请选择数据类型">
          <Option v-for="(item, index) in dataTypeList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem required prop="errorCode" :label="`异常编码`">
        <Input
          type="text"
          class="input-width"
          v-model="formCustom.errorCode"
          placeholder="请输入异常编码"
          :maxlength="20"
        ></Input>
      </FormItem>
      <FormItem required prop="errorMessage" :label="`异常原因`">
        <Input
          type="text"
          class="input-width"
          v-model="formCustom.errorMessage"
          placeholder="请输入异常原因"
          :maxlength="20"
        ></Input>
      </FormItem>
      <FormItem :label="`治理工具`">
        <Select
          class="input-width"
          v-model="formCustom.governmentList"
          placeholder="请选择治理工具"
          multiple
          @on-change="changeGovernment($event)"
        >
          <Option v-for="item in governanceTool" :key="item.code" :value="item.code" :label="item.label">
            <span @click="goToolPage(item)">{{ item.label }}</span>
          </Option>
        </Select>
      </FormItem>
      <FormItem label="描述" prop="remark">
        <Input type="textarea" class="input-width" v-model="formCustom.remark" :maxlength="500"></Input>
      </FormItem>
    </Form>
    <template #footer>
      <Button class="plr-30" @click="cancel">取 消</Button>
      <Button type="primary" @click="saveQuetion" class="plr-30">保存</Button>
    </template>
  </ui-modal>
</template>
<script>
import knowledgebase from '@/config/api/knowledgebase';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    modalAction: {},
    // 数据类型
    dataTypeList: {},
  },
  data() {
    return {
      dropdownVisible: false,
      defaultProps: {
        id: 'id',
        children: 'children',
      },
      questionShow: false,
      visible: false,
      styles: {
        width: '3rem',
      },
      selectOrgTree: {
        orgCode: '',
      },
      governanceTool: [],
      governanceToolObject: {},
      formCustom: {
        errorCode: '',
        errorMessage: '',
        governmentList: [],
        indexModule: '',
        remark: '',
      },
      errorData: {},
      ruleCustom: {
        indexModule: [
          {
            required: true,
            type: 'string',
            message: '请输入数据类型',
            trigger: 'blur',
          },
        ],
        errorCode: [
          {
            required: true,
            type: 'string',
            message: '请输入异常编码',
            trigger: 'blur',
          },
        ],
        errorMessage: [
          {
            required: true,
            type: 'string',
            message: '请输入异常原因',
            trigger: 'blur',
          },
        ],
      },
      submitLoading: false,
      selectTree: {
        title: 'sadsad',
        regionCode: 'sadsda',
      },
    };
  },
  created() {
    this.setSourceTypeList();
    this.getGovernmentToolsList();
  },
  methods: {
    ...mapActions({
      setCatalogTreeData: 'knowledgebase/setCatalogTreeData',
      setSourceTypeList: 'knowledgebase/setSourceTypeList',
    }),
    // 【处置反馈-治理工单】
    // 【工具集】
    // 【视图治理-设备信息自动获取】
    // 【视图治理-重设设备时钟】
    // 【视图治理-功能类型治理】
    // 【视图治理-点位类型治理】
    // 【视图治理-MAC地址治理】
    // 【视图治理-OSD字幕自动重设】
    // 【视图治理-区域管理】
    // 【视图治理-空间信息治理】
    goToolPage(item) {
      let name = 'route' in item ? item.route : 'governanceautomatic';
      let query = name === 'governanceautomatic' ? { tab: item.code } : {};
      this.$router.push({
        name: name,
        query: query,
      });
    },
    changeGovernment() {},
    cancel() {
      this.visible = false;
    },
    async saveQuetion() {
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) return;
        try {
          let url = this.isAdd ? knowledgebase.addQuestion : knowledgebase.updateQuestion;
          this.submitLoading = true;
          let params = {
            ...this.formCustom,
            governmentNameList: this.formCustom.governmentList.map((item) => this.governanceToolObject[item]),
          };
          const res = await this.$http.post(url, params);
          this.$Message.success(res.data.msg);
          this.visible = false;
          this.$emit('update');
        } catch (err) {
          console.log(err);
        } finally {
          this.submitLoading = false;
        }
      });
    },
    release() {},
    resetFields() {
      // this.$refs.form.resetFields()
      this.formCustom = {
        errorCode: '',
        errorMessage: '',
        governmentList: [],
        indexModule: '',
        remark: '',
      };
    },
    addQuestion() {
      this.questionShow = true;
    },
    async getGovernmentToolsList() {
      // 自定义的跳转
      const selfRoute = {
        10: 'governanceorder', // 治理工单
        11: 'governancetoolset', // 系统工具集
        0: 'governanceautomatic', // 剩余工具全都跳转这个路由
      };
      try {
        let url = knowledgebase.getGovernmentToolsList;
        const { data } = await this.$http.get(url);
        this.governanceToolObject = data.data;
        this.governanceTool = Object.keys(data.data).map((key) => {
          return {
            label: data.data[key],
            route: key in selfRoute ? selfRoute[key] : selfRoute[0],
            code: key,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      this.resetFields();
      if (val && !this.isAdd) {
        let row = this.modalAction.row;
        this.formCustom = {
          id: row.id,
          errorCode: row.errorCode,
          errorMessage: row.errorMessage,
          governmentList: row.governmentList || [],
          indexModule: row.indexModule,
          remark: row.remark,
        };
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {
    ...mapGetters({
      getCatalogTreeData: 'knowledgebase/getCatalogTreeData',
      getSourceTypeList: 'knowledgebase/getSourceTypeList',
      phystatusList: 'algorithm/propertySearch_phystatus',
      other_device_type: 'algorithm/other_device_type',
    }),
    isAdd() {
      return this.modalAction.action === 'add';
    },
  },
  components: {
    // DropUiSearchTree: require("./drop-ui-search-tree.vue").default,
    // Editor: require('./editor.vue').default,
    // AddRetiveQuestion: require('./add-retive-question.vue').default,
  },
};
</script>
<style lang="less" scoped>
.add-edit-form {
  padding: 0 30px;
  .input-width {
    width: 380px;
  }
  .quesition-ul {
    display: flex;
    > li {
      color: var(--color-primary);
    }
  }
}
</style>
