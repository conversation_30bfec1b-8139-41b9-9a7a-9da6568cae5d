<template>
  <div class="place-content" :class="{ isClose: !isOpen }">
    <ui-loading v-if="loading && isOpen" />
    <div class="place-content-header">
      <div class="label">
        <div class="label-text">资源列表：</div>
      </div>
      <div
        class="operator"
        @click="
          () => {
            $emit('update:isOpen', !isOpen);
          }
        "
      >
        {{ isOpen ? "收起面板" : "点击展开" }}
      </div>
    </div>
    <div class="point-content">
      <template v-if="searchPrams.keyWords">
        <div v-for="(item, i) in placeList" :key="i">
          <div
            class="point-item"
            :class="{ active: currentIndex == i }"
            @dblclick="placeTreeDetail(item.properties, i)"
            @click="placeTreeNodeClick(item.properties)"
          >
            <div class="header">
              <div class="header-left">
                <span
                  class="serialNumber"
                  :class="{ activeNumber: currentIndex == i }"
                >
                  <span>{{ i + 1 }}</span>
                </span>
                <span
                  class="pointName ellipsis"
                  :title="item.properties.name"
                  >{{ item.properties.name }}</span
                >
              </div>
              <div class="header-right">
                <span
                  class="tag"
                  :style="{
                    borderColor: transData(item.properties.firstLevel).color,
                    color: transData(item.properties.firstLevel).color,
                  }"
                  >{{ item.properties.firstLevelName }}</span
                >
              </div>
            </div>
            <div class="content">
              <div class="content-right">
                <ui-icon type="location" :size="16"></ui-icon>
                <span class="ellipsis" :title="item.properties.address">{{
                  item.properties.address
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <el-tree ref="treeRef" :props="defaultProps" :load="loadNode" lazy>
          <template slot-scope="{ data }">
            <template v-if="data.detailInfo">
              <div class="device-item">
                <div
                  class="device-label ellipsis"
                  @dblclick="placeTreeDetail(data.detailInfo)"
                  @click="placeTreeNodeClick(data.detailInfo)"
                  :title="data.label"
                >
                  <ui-icon
                    :type="transData(data.detailInfo.firstLevel).icon"
                    :color="transData(data.detailInfo.firstLevel).color"
                  ></ui-icon>
                  <span class="device-name">{{ data.label }}</span>
                </div>
                <div class="more-operate">
                  <ui-icon class="more-icon" type="gengduo"></ui-icon>
                  <ul class="more-list">
                    <li @click="placeTreeDetail(data.detailInfo)">查看详情</li>
                    <li @click="viewArchive(data.detailInfo)">查看档案</li>
                    <li @click="viewCatalog(data.detailInfo)">查看目录</li>
                    <li @click="copyName(data.label)">复制名称</li>
                  </ul>
                </div>
              </div>
            </template>
            <template v-else>
              <span class="tree-label"
                >{{ data.label }}(<span>{{ data.allTotal }}</span
                >)
              </span>
            </template>
          </template>
        </el-tree>
      </template>
    </div>
    <div class="general-search-footer" v-if="isOpen && searchPrams.keyWords">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="total"
        countTotal
        :current="aoiParams.pageNumber"
        :page-size="aoiParams.pageSize"
        @pageChange="pageChange"
        size="small"
        show-total
      >
      </ui-page>
    </div>
  </div>
</template>

<script>
import {
  getaoi,
  queryPlaceTreeList,
  placeDetail,
  queryPlaceAndKeys,
} from "@/api/operationsOnTheMap.js";
import { copyText } from "@/util/modules/common";
import { index } from "d3";
import log from "@/libs/configuration/util.log";
import data from "@/components/map/data";

export default {
  props: {
    //搜索条件
    searchPrams: {
      type: Object,
      default: () => {},
    },
    isOpen: {
      type: Boolean,
      default: () => false,
    },
    adcode: {
      type: String,
      default: () => "",
    },
    citycode: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      placeLayerList: [
        {
          name: "购物服务",
          color: "#F37A7A",
          data: [],
          icon: "shop",
          checked: false,
          layerType: "placeShop",
          firstLevel: "67021",
        },
        {
          name: "教育培训",
          color: "#1FAF8A",
          data: [],
          icon: "education",
          checked: false,
          layerType: "placeEdu",
          firstLevel: "67012",
        },
        {
          name: "政府机构",
          color: "#2C86F8",
          data: [],
          icon: "gejizhengfu",
          checked: false,
          layerType: "placeGovernment",
          firstLevel: "67011",
        },
        {
          name: "房产小区",
          color: "#2AB2F4",
          data: [],
          icon: "community",
          checked: false,
          layerType: "placeHouse",
          firstLevel: "67010",
        },
        {
          name: "医疗保健",
          color: "#EA4A36",
          data: [],
          icon: "medical",
          checked: false,
          layerType: "placeMedical",
          firstLevel: "67008",
        },
        {
          name: "公司企业",
          color: "#8C80FB",
          data: [],
          icon: "company",
          checked: false,
          layerType: "placeCompany",
          firstLevel: "67005",
        },
        {
          name: "住宿服务",
          color: "#C832DC",
          data: [],
          icon: "hotel1",
          checked: false,
          layerType: "placeHotel",
          firstLevel: "67004",
        },
        {
          name: "交通设施",
          color: "#D82B84",
          data: [],
          icon: "traffic",
          checked: false,
          layerType: "placeTraffic",
          firstLevel: "67002",
        },
      ],
      aoiParams: {
        pageSize: 500,
        pageNumber: 1,
        name: "",
        firstLevelList: [],
        adcode: "",
        citycode: "",
      },
      firstLevelNameList: [],
      total: 0,
      loading: false,
      placeList: [],
      currentIndex: -1,
      isPlaceFilter: false,
      checkAll: false,
      indeterminate: false,
      defaultProps: {
        children: "children",
        label: "label",
        isLeaf: "leaf",
      },
      placeParams: {
        queryFlag: "",
        regionCode: "",
        firstLevel: "",
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    loadNode(node, resolve) {
      this.loading = true;
      if (node.level > 2) {
        this.placeParams.queryFlag = node.data.nextTreeFlag;
        this.placeParams.firstLevel = node.data.regionCode;
        this.placeParams.regionCode = node.parent.data.regionCode;
        queryPlaceTreeList(this.placeParams).then((res) => {
          let placeRegionList = [];
          placeRegionList = res.data.placeDetailList.map((item) => {
            return {
              id: item.id,
              label: item.name,
              leaf: true,
              detailInfo: { ...item },
            };
          });
          this.loading = false;
          return resolve(placeRegionList);
        });
      } else {
        this.placeParams.queryFlag = node.data ? node.data.nextTreeFlag : "";
        this.placeParams.regionCode = node.data ? node.data.regionCode : "";
        this.placeParams.firstLevel = node.data ? node.data.firstLevel : "";
        queryPlaceTreeList(this.placeParams).then((res) => {
          let placeRegionList = [];
          placeRegionList = res.data.placeRegionList.map((item) => {
            return {
              id: item.id,
              label: item.regionName,
              regionCode: item.regionCode,
              nextTreeFlag: item.nextTreeFlag,
              firstLevel: item.firstLevel,
              allTotal: item.allTotal,
              leaf: false,
            };
          });
          this.loading = false;
          return resolve(placeRegionList);
        });
      }
    },
    viewArchive(info) {
      const { href } = this.$router.resolve({
        name: "place-dashboard",
        query: { archiveNo: info.id, source: "place" },
      });
      window.open(href, "_blank");
    },
    viewCatalog(info) {
      // 和设备保持一致 不带本身名称
      // `${info.cityname}/${info.adname}/${info.firstLevelName}/${info.name}`
      this.$Message.info(
        `${info.cityname}/${info.adname}/${info.firstLevelName}`
      );
    },
    copyName(name) {
      copyText(name);
    },
    transData(val) {
      let color = "";
      let icon = "";
      this.placeLayerList.forEach((item) => {
        if (item.firstLevel == val) {
          color = item.color;
          icon = item.icon;
        }
      });
      return { color, icon };
    },
    // 列表点击
    chooseMapItem(item, index) {
      //场所弹框
      this.currentIndex = index;
      let pointData = {
        center: JSON.parse(item.properties.centerPoint),
        properties: item.properties,
      };
      this.$emit("aoiModelShow", pointData);
      //场所图层
      this.$emit("removePlaceLayer");
      let polygonObj = {};
      this.placeLayerList.forEach((e) => {
        if (e.firstLevel == item.properties.firstLevel) {
          polygonObj = {
            name: item.properties.name,
            color: e.color,
            data: [item],
            icon: e.icon,
            checked: e.checked,
            layerType: e.layerType,
            firstLevel: e.firstLevel,
          };
        }
      });
      this.$emit("setPolygon", polygonObj, polygonObj.layerType, true);
    },
    //场所搜索、场所树查看详情及双击事件
    placeTreeDetail(data, index) {
      placeDetail(data.id).then((res) => {
        let mapItem = {
          properties: { ...res.data },
          geometry: JSON.parse(res.data.coord),
        };
        this.chooseMapItem(mapItem, index);
      });
    },
    //场所树单击事件
    placeTreeNodeClick(data) {
      placeDetail(data.id).then((res) => {
        let mapItem = {
          properties: { ...res.data },
          geometry: JSON.parse(res.data.coord),
        };
        this.$emit("removePlaceLayer");
        let polygonObj = {};
        this.placeLayerList.forEach((e) => {
          if (e.firstLevel == mapItem.properties.firstLevel) {
            polygonObj = {
              name: mapItem.properties.name,
              color: e.color,
              data: [mapItem],
              icon: e.icon,
              checked: e.checked,
              layerType: e.layerType,
              firstLevel: e.firstLevel,
            };
          }
        });
        this.$emit("setPolygon", polygonObj, polygonObj.layerType);
        this.$emit("mapPanTo", JSON.parse(res.data.centerPoint));
      });
    },
    // 初始化加载列表
    init() {
      this.$emit("removePlaceLayer");
      this.aoiParams.pageNumber = 1;
      this.aoiParams.pageSize = 200;
      this.firstLevelNameList = [];
      this.checkAll = false;
      this.indeterminate = false;
      if (this.searchPrams.keyWords) {
        // 场所搜索
        this.getAoiData();
      } else {
        //场所树
        // console.log("aaa");
      }
    },
    // 获取场所aoi数据
    getAoiData() {
      this.currentIndex = -1;
      this.placeList = [];
      this.loading = true;
      this.aoiParams.name = this.searchPrams.keyWords;
      this.aoiParams.adcode = this.adcode;
      this.aoiParams.citycode = this.citycode;
      this.aoiParams.firstLevelList = [];
      if (this.firstLevelNameList.length > 0) {
        this.firstLevelNameList.forEach((item) => {
          this.placeLayerList.forEach((ite) => {
            if (item == ite.name) {
              this.aoiParams.firstLevelList.push(ite.firstLevel);
            }
          });
        });
      }
      let params = {
        searchKey: this.aoiParams.name,
        pageSize: this.aoiParams.pageSize,
        pageNumber: this.aoiParams.pageNumber,
      };
      queryPlaceAndKeys(params)
        .then((res) => {
          this.total = res.data.total;
          for (var i = 0; i < this.placeLayerList.length; i++) {
            this.placeLayerList[i].data = [];
            for (var j = 0; j < res.data.entities.length; j++) {
              if (
                res.data.entities[j].firstLevelName ===
                this.placeLayerList[i].name
              ) {
                this.placeLayerList[i].data.push({
                  properties: { ...res.data.entities[j] },
                  geometry: JSON.parse(res.data.entities[j].coord),
                });
              }
            }
          }
          this.placeLayerList.forEach((item) => {
            this.placeList.push(...item.data);
          });
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    pageChange(pageNumber) {
      this.aoiParams.pageNumber = pageNumber;
      this.getAoiData();
    },
    // 展示筛选列表
    showTreeFilter() {
      this.isPlaceFilter = true;
    },
    // 隐藏筛选列表
    hideTreeFilter() {
      this.isPlaceFilter = false;
    },
    handleCheckAll() {
      this.indeterminate = false;
      if (this.checkAll) {
        this.placeLayerList.forEach((item) => {
          this.firstLevelNameList.push(item.name);
        });
      } else {
        this.firstLevelNameList = [];
      }
      this.$emit("removePlaceLayer");
      this.aoiParams.pageNumber = 1;
      this.aoiParams.pageSize = 200;
      //   this.getAoiData();
    },
    checkAllGroupChange(data) {
      if (data.length === 8) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
      this.$emit("removePlaceLayer");
      this.aoiParams.pageNumber = 1;
      this.aoiParams.pageSize = 200;
      //   this.getAoiData();
    },
  },
};
</script>

<style lang="less" scope>
.place-content {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 10px;
  padding-top: 10px !important;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  &.isClose {
    height: 40px !important;
  }
  &-header {
    height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    .label {
      font: 14px;
      color: #333333;
      display: flex;
      align-items: center;
      .label-text {
        font-weight: 600;
      }
    }
    .operator {
      cursor: pointer;
      &:hover {
        color: #2c86f8;
      }
    }
    .tree-operators-icon-wrap {
      .iconfont {
        vertical-align: -webkit-baseline-middle;
      }
      .tree-filter-wrap {
        position: absolute;
        top: 35px;
        left: 0;
        z-index: 9;
        width: 100%;
        background: rgba(44, 48, 51, 0.9);
        border-radius: 3px;
        color: var(--font-white-color);
        padding: 5px 10px;
        .triangle {
          width: 0;
          height: 0;
          overflow: hidden;
          font-size: 0;
          line-height: 0;
          border-width: 5px;
          border-style: solid;
          border-color: transparent transparent rgba(44, 48, 51, 0.9)
            transparent;
          position: absolute;
          left: 75px;
          top: -10px;
        }
        .tree-filter-wrap-content {
          .tree-filter-wrap-title {
            color: #fff;
            height: 22px;
            font-size: 14px;
            line-height: 25px;
          }
          .ivu-checkbox-wrapper {
            color: #fff;
          }
          .tree-filter-check-wrap {
            display: flex;
            margin-left: 70px;
          }
        }
      }
    }
  }
}
.point-content {
  // #region
  .el-tree {
    background: inherit;
    .el-tree-node__content {
      height: 32px;
      line-height: 32px;
    }
    .el-tree-node__expand-icon {
      font-size: 16px !important;
    }
    .el-tree-node__expand-icon.expanded {
      color: #000;
    }
    .el-icon-loading {
      display: none;
    }
    .el-tree-node > .el-tree-node__children {
      overflow: unset;
    }
  }

  .device-item {
    width: calc(~"100% - 26px");
    display: flex;
    justify-content: space-between;
    &:hover {
      background: var(--active-bg);
      // 鼠标划入，显示.more-operate
      .more-operate {
        display: block;
      }
    }
    .device-label {
      width: 100%;
      .device-name {
        margin-left: 6px;
        font-size: 14px;
        color: #000;
      }
    }
    .more-operate {
      display: none;
      position: absolute;
      right: 5px;
      &:hover {
        .more-list {
          display: block;
        }
      }
      .more-icon {
        display: inline-block;
        color: #2c86f8 !important;
        transform: rotate(90deg);
      }
      .more-list {
        display: none;
        position: absolute;
        background-color: #fff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        z-index: 99;
        transform: translate(-75%, -3px);
        li {
          padding: 0 10px;
          cursor: pointer;
          &:hover {
            background: rgba(44, 134, 248, 0.2);
          }
        }
      }
    }
  }

  // #endregion

  width: 100%;
  height: calc(~"100% - 56px");
  overflow: hidden;
  overflow-y: auto;
  margin-top: 5px;
  .point-item {
    height: 70px;
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    cursor: pointer;
    .header {
      display: flex;
      justify-content: space-between;
      height: 42px;
      line-height: 42px;
      padding: 0 10px;
      &-left {
        display: flex;
        align-items: center;
        width: 245px;
        .serialNumber {
          position: relative;
          display: inline-block;
          width: 32px;
          height: 32px;
          margin-right: 14px;
          color: black;
          background: url("~@/assets/img/map/trajectory-red.png") no-repeat;
          > span {
            position: absolute;
            top: -10px;
            width: 32px;
            color: #ea4a36;
            text-align: center;
          }
        }
        .pointName {
          font-size: 14px;
          font-weight: bold;
          color: rgba(0, 0, 0, 0.9);
          margin-left: -10px;
        }
        .activeNumber {
          background: url("~@/assets/img/map/trajectory-blue.png") no-repeat !important;
          > span {
            color: #2c86f8 !important;
          }
        }
      }
      &-right {
        display: flex;
        align-items: center;
        justify-content: center;

        > span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 20px;
          background: #f9f9f9;
          border-radius: 4px;
          border: 1px solid #d3d7de;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.35);
        }
      }
    }
    .content {
      display: flex;
      padding: 0 10px;
      &-left {
        > img {
          width: 60px;
          height: 60px;
          border: 1px solid #d3d7de;
        }
      }
      &-right {
        margin-left: 10px;
        display: flex;
        align-items: center;
        width: 100%;
        .iconfont {
          margin-right: 10px;
        }
      }
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.1);
  }

  .tree-label {
    color: #000;
    font-size: 14px;
    span {
      color: #2c86f8;
    }
  }
}
</style>
