<template>
  <div class="charts-container">
    <div
      :class="{ 'charts-item': true, [`bg-${index % 5}`]: true }"
      v-for="(item, index) in abnormalCount"
      :key="index + item.title"
    >
      <!-- 不传icon，展示默认图标 -->
      <!--      <span v-if="!item.icon" class="icon-font icon-exceptionlibrary f-32 icon-2"></span>-->
      <span
        :class="{
          'icon-font': true,
          'f-40': true,
          [item.icon]: true,
          [`icon-${index % 5}`]: true,
        }"
      ></span>
      <div class="number-wrapper">
        <p class="f-12 base-text-color">{{ item.title }}</p>
        <p class="f-18 color-num">{{ commaNumber(item.count || 0) }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    abnormalCount: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
  },
};
</script>
<style lang="less" scoped>
.charts-container {
  margin-top: 10px;
  display: flex;
  overflow-x: auto;
  justify-content: flex-start;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 2px;
  .charts-item {
    flex-shrink: 0;
    margin-right: 10px;
    width: 304px;
    min-width: 200px;
    height: 88px;
    background: var(--bg-info-card);
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    .number-wrapper {
      display: inline-block;
      margin-left: 16px;
    }
  }
  .f-40 {
    font-size: 40px;
  }
  .color-white {
    color: #fff;
  }
  .color-num {
    // color: #19d5f6;
    // color: inherit;
  }
  .icon-0 {
    background: var(--icon-card-gradient-cyan);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-1 {
    background: var(--icon-card-gradient-green);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-2 {
    background: var(--icon-card-gradient-purple);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-3 {
    background: var(--icon-card-gradient-pink);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-4 {
    background: var(--icon-card-gradient-orange);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .bg-0 {
    background: var(--bg-card-cyan);
    box-shadow: var(--bg-card-cyan-shadow);
    .color-num {
      color: var(--font-card-cyan);
    }
  }
  .bg-1 {
    background: var(--bg-card-green);
    box-shadow: var(--bg-card-green-shadow);
    .color-num {
      color: var(--font-card-green);
    }
  }
  .bg-2 {
    background: var(--bg-card-purple);
    box-shadow: var(--bg-card-purple-shadow);
    .color-num {
      color: var(--font-card-purple);
    }
  }
  .bg-3 {
    background: var(--bg-card-pink);
    box-shadow: var(--bg-card-pink-shadow);
    .color-num {
      color: var(--font-card-pink);
    }
  }
  .bg-4 {
    background: var(--bg-card-orange);
    box-shadow: var(--bg-card-orange-shadow);
    .color-num {
      color: var(--font-card-orange);
    }
  }
}
</style>
