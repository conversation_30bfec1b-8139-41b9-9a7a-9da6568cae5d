<template>
  <div class="base-search">
    <!--    <ui-label class="inline mr-lg " label="行政区划">
      <api-area-tree
        class="area-tree mb-sm"
        placeholder="请选择行政区划"
        :select-tree="searchData"
        @selectedTree="selectedAreaTree"
      ></api-area-tree>
    </ui-label>-->
    <ui-label class="inline mr-lg mb-sm" label="地名名称">
      <Input v-model="searchData.placeName" class="width-lg" placeholder="请输入地名名称" clearable></Input>
    </ui-label>
    <ui-label class="inline mr-lg" label="地名俗称">
      <Input v-model="searchData.placeAlias" class="width-lg" placeholder="请输入地名俗称" clearable></Input>
    </ui-label>
    <ui-label class="inline mr-lg" label="检测结果">
      <Select class="width-sm" v-model="searchData.checkStatus" clearable placeholder="请选择检测结果">
        <Option :value="1" label="合格"></Option>
        <Option :value="2" label="不合格"></Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg" label="采集区域类型">
      <Button type="dashed" class="area-btn" @click="areaSelectModalVisible = true"
        >请选择采集区域 {{ `已选择 ${searchData.sbcjqyList.length} 个` }}</Button
      >
    </ui-label>
    <ui-label class="inline" :width="0" label="">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetForm">重置</Button>
    </ui-label>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="searchData.sbcjqyList"
    ></area-select>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  components: {
    AreaSelect: require('@/components/area-select').default,
  },
  props: {},
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  data() {
    return {
      areaSelectModalVisible: false,
      searchData: {
        placeName: '',
        placeAlias: '',
        checkStatus: '',
        sbcjqyList: [],
      },
    };
  },
  methods: {
    resetForm() {
      this.resetSearchDataMx(this.searchData);
      this.$emit('startSearch', this.searchData);
    },
    confirmArea(val) {
      this.searchData.sbcjqyList = val;
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    selectedAreaTree(val) {
      this.searchData.regionCode = val.regionCode;
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultSelectedArea: 'common/getDefaultSelectedArea',
    }),
  },
};
</script>
<style lang="less" scoped>
.base-search {
  position: relative;
}
</style>
