<template>
    <div class="point-content-wrapper" :class="{'isClose': !isOpen}">
        <div class="point-content-wrapper-header">
            <div class="label">
                <div class="label-text">资源列表：</div>
            </div>
            <div class="operator" @click="()=>{$emit('update:isOpen', !isOpen)}">{{isOpen ? '收起面板' : '点击展开'}}</div>
        </div>
        <div class="point-content">
            <div v-for="(item, i) in dataList" :key="i">
                <div class="point-item" :class="{ active: currentId == item.id }" @click="chooseMapItem(item)">
                    <div class="content">
                        <i class="iconfont" :class="type == 'police' ? 'icon-jingli' : 'icon-gejizhengfu'"/>
                        <span>{{ item.name }}</span>
                    </div>
                    <div class="operate" v-if="type == 'police'">
                        <i class="iconfont icon-huodongguilv" title="轨迹分析" @click.stop="handleTrack(item)"/>
                    </div>
                </div>
            </div>
            <ui-loading v-if="loading && isOpen" />
            <ui-empty v-if="!loading && !dataList.length" />
        </div>
        <div class="general-search-footer" v-if="type != 'police'">
            <ui-page :simple="true" :show-elevator="false" countTotal :show-sizer="false" :total="total" :current="pageInfo.pageNumber" :page-size="pageInfo.pageSize" size="small" show-total @pageChange="handleCurrentChange"> </ui-page>
        </div>
    </div>
</template>

<script>
import {  getPosterityGps, getPointList, getGpsRealTime } from '@/api/operationsOnTheMap';
import { LayerType, siteType } from '@/map/core/enum/LayerType.js'
export default {
    props: {
        //搜索条件
        searchPrams: {
            type: Object,
            default: () => { }
        },
        isOpen: {
            type: Boolean,
            default: () => false
        },
        type: {
            type: String,
            default: () => 'police'
        }
    },
    data() {
        return {
            dataList: [],
            dataListDefault: [],
            currentIndex: -1,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20
            },
            loading: false,
            activeIndex:0,
            markers: [],
            currentId: ''
        }
    },
    watch: {
        type(val) {
            this.init()
        }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            if (this.loading) return
            this.pageInfo.pageNumber = 1
            this.querySearch()
        },
        searchName() {
            if (this.loading) return
            this.pageInfo.pageNumber = 1
            if (this.type == 'police') {
                this.dataList = this.dataListDefault.filter(v => v.name.includes(this.searchPrams.keyWords))
            } else {
                this.querySearch()
            }
        },
        querySearch() {
            this.loading = true;
            let params = {
                ...this.pageInfo,
                'keyword': this.searchPrams.keyWords,
            }
            this.dataList = []
            this.dataListDefault = []
            if (this.type == 'police') {
                getPosterityGps().then(res => {
                    if (res.code === 200) {
                        this.dataList = res.data || [];
                        this.dataListDefault = res.data || [];
                        this.total = res.data.length;
                        this.clearMarkers()
                    }
                }).catch(() => {
                    this.dataList = []
                    this.dataListDefault = []
                    this.clearMarkers()
                })
                .finally(() => {
                    this.loading = false
                })
            } else {
                getPointList(params)
                .then(res => {
                    if (res.code === 200) {
                        const { data: { entities = [], total = 0 } } = res
                        this.dataList = entities || [];
                        this.total = total;
                        this.addMarkers(this.dataList)
                    }
                })
                .catch(() => {
                    this.dataList = []
                    this.addMarkers(this.dataList)
                })
                .finally(() => {
                    this.loading = false
                })
            } 
        },
        handleCurrentChange(val){
            if(this.pageInfo.pageNumber == val) return;
            this.pageInfo.pageNumber = val
            this.querySearch()
        },
        async chooseMapItem(item) {
            this.currentId = item.id
            if (this.type == 'police') {
                let res = await getGpsRealTime(item.id)
                this.addMarkers([res.data[0]], true)
            } else {
                this.resetIcons()
                let marker = this.markers.find(v => v.ext.id == item.id)
                marker.ext.clicked = true
                marker.getIcon().setImageUrl(LayerType[marker.markType].hoverUrl)
                marker.refresh();
                this.$parent.$refs['mapBase'].setCenter(marker.getCentroid(), 17)
                this.$parent.$refs['mapBase'].showPOIDom(marker)
            }
        },
        addMarkers(points, isSelected) {
            this.clearMarkers()
            this.$parent.$refs['mapBase'].addDeviceMarkers('pointLayer', points.map(v => {
                v.LayerType = this.type == 'police' ? 'Point_Police' : 'Point_POI'
                v.Lon = v.longitude || v.geoPoint.lon
                v.Lat = v.latitude || v.geoPoint.lat
                v.imgUrl = isSelected ? LayerType[v.LayerType].hoverUrl : LayerType[v.LayerType].url
                return v
            }), (markers) => {
                this.markers = markers
            }, {
                click: marker => {
                    this.resetIcons()
                    marker.ext.clicked = true
                    marker.getIcon().setImageUrl(LayerType[marker.markType].hoverUrl)
                    marker.refresh();
                    if (this.type == 'points') this.$parent.$refs['mapBase'].showPOIDom(marker)
                }
            })
        },
        resetIcons() {
            this.markers.forEach(v => {
                if (v.ext.clicked) {
                    v.ext.clicked = false
                    v.getIcon().setImageUrl(LayerType[v.markType].url)
                    v.refresh();
                }
            })
        },
        clearMarkers() {
            this.currentId = ''
            this.$parent.$refs['mapBase'] && this.$parent.$refs['mapBase'].resetMarker()
        },
        handleTrack(item) {
            this.$emit('gpsTrack', item)
        }
    }
}
</script>

<style lang="less" scoped>
.point-content-wrapper{
    height: 100%;
    position: relative;
    padding: 10px;
    padding-top: 10px!important;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    &.isClose {
        height: 40px!important;
    }
    &-header {
        height: 22px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .label {
            font-size: 14px;
            color: #333333;
            display: flex;
            align-items: center;
        .label-text {
            font-weight: 600;
        }
        }
        .operator {
            cursor: pointer;
            &:hover {
                color: #2c86f8;
            }
        }
    }
}
.point-content {
  position: relative;
  overflow: hidden;
  overflow-y: auto;
  flex: 1;
  .point-item {
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    .content {
      flex: 1;
      display: flex;
      padding: 0 10px;
      i {
        color: #2c86f8;
        margin-right: 5px;
      }
    }
    .operate {
        opacity: 0;
        i {
            color: #2c86f8;
            margin-right: 5px;
        }
    }
    &:hover {
        background: #f5f7fa;
        .operate {
            opacity: 1;
        }
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.2);
  }
}
</style>
