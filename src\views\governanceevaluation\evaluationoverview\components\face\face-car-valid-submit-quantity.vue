<template>
  <!-- 人脸/车辆卡口有效报送数量达标率（海南） 公用一个组件-->
  <div class="county-quantity-reach auto-fill">
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="abnormal-title">
          <div class="fl">
            <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
            <span class="f-16 color-filter ml-sm">检测结果统计</span>
          </div>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs f-14">导出</span>
            </Button>
          </div>
        </div>
        <div class="auto-fill">
          <ui-table class="auto-fill" :tableColumns="tableColumns" :table-data="tableData" :loading="loading">
            <template #validReportRate="{ row }">
              <create-tabs
                :component-name="themData.componentName"
                :tabs-text="themData.text"
                :tabs-query="{
                  ...paramsList,
                  orgRegionName: paramsList.displayType === 'REGION' ? row.civilName : row.orgName,
                  examineTime,
                  orgRegionCode: paramsList.displayType === 'REGION' ? row.civilCode : row.orgCode,
                }"
              >
                <span class="link">{{ row.validReportRate || 0 }}% </span>
              </create-tabs>
            </template>
            <template #resultValue="{ row }">
              <span>{{ row.resultValue || 0 }}% </span>
            </template>
          </ui-table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.county-quantity-reach {
  .content {
    color: #fff;
    background: @bg-blue-block;
    border-radius: 4px;
    position: relative;
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
}
.link {
  color: var(--color-primary);
  cursor: pointer;
  text-decoration: underline;
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
export default {
  mixins: [downLoadTips, dealWatch],
  name: 'face-car-valid-submit-quantity',
  data() {
    return {
      tableData: [],
      loading: false,
      exportLoading: false,
      paramsList: {},
      componentLevel: 2, //组件标签层级 如果是标签组件套用标签组件需要此参数
      themData: {
        componentName: 'FaceCarReportRate', // 需要跳转的组件名
        text: '有效上报率1', // 跳转页面标题
        title: '有效上报率2',
        type: 'view',
      },
    };
  },
  computed: {
    tableColumns() {
      let columns = { validSubmitNum: '', title: '' };
      let { indexId, displayType } = this.paramsList;
      if (Number.parseInt(indexId) === 2015) {
        columns = {
          validSubmitNum: '基础信息填报准确且本月有人脸抓拍',
          title: '人脸',
        };
      } else if (Number.parseInt(indexId) === 3019) {
        columns = { validSubmitNum: '基础信息填报准确', title: '车辆' };
      } else if (Number.parseInt(indexId) === 4018) {
        columns = { validSubmitNum: '基础信息填报准确', title: '视频监控' };
      }
      return [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '所在市县',
          key: displayType === 'REGION' ? 'civilName' : 'orgName',
          align: 'center',
          minWidth: 150,
        },
        {
          title: '区域类型',
          key: 'civilTypeName',
          align: 'center',
          minWidth: 150,
        },
        {
          title: `${columns.title}卡口建设总量`,
          key: 'buildNum',
          align: 'center',
          width: 200,
        },
        {
          title: '检测数量',
          key: 'actualNum',
          align: 'center',
          width: 200,
        },
        {
          title: '达标数量',
          key: 'configNum',
          align: 'center',
          width: 200,
        },
        {
          title: '超额数量',
          key: 'aboveNum',
          align: 'center',
          width: 200,
        },
        {
          title: `有效报送数量(${columns.validSubmitNum})`,
          key: 'validSubmitNum',
          align: 'center',
          width: 200,
        },
        {
          title: '有效上报率',
          slot: 'validReportRate',
          align: 'center',
          width: 200,
        },
        {
          title: `${columns.title}卡口有效报送数量达标率`,
          slot: 'resultValue',
          align: 'center',
          width: 200,
        },
      ];
    },
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: {
          exportType: 'second', //value：first、second、third
        },
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    async getEvaluationRecord() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getThirdModelData, params);
        this.tableData = data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    examineTime: {},
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getEvaluationRecord();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
