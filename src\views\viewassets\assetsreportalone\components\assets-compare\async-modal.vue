<template>
  <div class="async-modal">
    <ui-modal v-model="visible" title="资产同步" width="32.5rem">
      <p class="base-text-color mb-lg">
        {{ description }}
        <span class="font-active-color">{{ chooseListLength }}</span>
        条设备，将
      </p>
      <div class="mt-lg mb-lg base-text-color">
        <Select class="width-input" v-model="leftData" :placeholder="`请选择库`" clearable :disabled="true">
          <Option :value="0">设备资产库 </Option>
          <Option :value="1">上报库 </Option>
        </Select>
        <span class="ml-sm mr-sm">同步到</span>
        <Select class="width-input" v-model="rightData" :placeholder="`请选择库`" clearable :disabled="true">
          <Option :value="0">设备资产库 </Option>
          <Option :value="1">上报库 </Option>
        </Select>
      </div>

      <template #footer>
        <Button class="ml-sm plr-30" @click="visible = false">取 消</Button>
        <Button class="plr-30" type="primary" @click="confirmAsync">确 认</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped></style>
<script>
import viewassets from '@/config/api/viewassets';

export default {
  data() {
    return {
      visible: false,
      leftData: 0,
      rightData: 1,
    };
  },
  created() {},
  mounted() {},
  methods: {
    async confirmAsync() {
      try {
        await this.$http.post(viewassets.syncDeviceForResult, this.params);
        this.$Message.success('同步成功');
        this.$emit('confirmAsync');
      } catch (error) {
        console.log(error);
      }
    },
  },
  computed: {},
  props: {
    value: {},
    description: {},
    isDisabled: {
      default: false,
    },
    chooseListLength: {},
    params: {},
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
