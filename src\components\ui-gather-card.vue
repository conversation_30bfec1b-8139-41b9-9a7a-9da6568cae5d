<template>
  <div class="ui-gather-card" @click="detail">
    <div class="ui-gather-card-left">
      <ui-image :src="list.identityPhoto" />
      <!-- <img :src="list.identityPhoto ? list.identityPhoto : errimg" alt="" /> -->
    </div>
    <div class="ui-gather-card-right">
      <p class="ui-gather-card-right-item" v-for="(item, index) in cardInfo" :key="index">
        <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
        <span
          class="ui-gather-card-right-item-value"
          :style="{ color: item.color ? item.color : 'var(--color-content)' }"
        >
          {{ list[item.value] || '暂无' }}
        </span>
      </p>
      <tags-more
        :tag-list="list.personTypes"
        :default-tags="defaultTags"
        placement="left-start"
        bg-color="#2D435F"
      ></tags-more>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    defaultTags: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  created() {},
  methods: {
    detail() {
      this.$emit('detail', this.list.id, this.list);
    },
  },
  watch: {
    defaultTags() {
      return this.defaultTags;
    },
  },
  components: {
    TagsMore: require('@/components/tags-more').default,
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  // min-height: 158px;
  margin-bottom: 10px;
  padding: 10px;
  background: var(--bg-info-card);
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid var(--border-info-card);
  &-left {
    width: 138px;
    height: 138px;
    margin-right: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: var(--color-info-card-label);
      }
      &-value {
        color: var(--color-info-card-content);
      }
    }
  }
  .tag {
    margin-right: 10px;
    padding: 3px 10px;
    font-size: 12px;
    background-color: var(--color-success);
    color: #ffffff;
    border-radius: 4px;
  }
}
.ui-gather-card:hover {
  border: 1px solid var(--color-primary);
}
// @{_deep} .ivu {
//   &-tag{
//     &:hover{
//       background: var(--color-primary) !important;
//     }
//   }
// }
</style>
