<template>
  <div>
    <Table
      class="auto-fill table"
      :columns="columns"
      border
      :loading="loading"
      :data="tableData"
      max-height="580"
    >
      <template #name="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].name"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #idCardNo="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].idCardNo"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #national="{ index }">
        <Select
          placeholder="请选择"
          v-model="tableData[index].national"
          transfer
          :disabled="
            tableData[index].idCardNo == '' ||
            tableData[index].idCardNo == null ||
            tableData[index].idCardNo.trim() == ''
          "
        >
          <Option
            v-for="item in nationList"
            :value="item.dataKey"
            :key="item.dataKey"
            placeholder="请选择"
          >
            {{ item.dataValue }}
          </Option>
        </Select>
      </template>
      <template #sex="{ index }">
        <Select
          placeholder="请选择"
          v-model="tableData[index].sex"
          transfer
          :disabled="
            tableData[index].idCardNo == '' ||
            tableData[index].idCardNo == null ||
            tableData[index].idCardNo.trim() == ''
          "
        >
          <Option
            v-for="item in genderList"
            :value="item.dataKey"
            :key="item.dataKey"
            placeholder="请选择"
          >
            {{ item.dataValue }}
          </Option>
        </Select>
      </template>
      <template #photoList="{ index }">
        <uiUploadImg
          ref="uploadImg"
          uploadUlr
          v-model="tableData[index].images"
          size="mini2"
          :algorithmType="1"
          @imgUrlChange="imgUrlChange($event, index)"
        />
      </template>
      <template #dictionaryCode="{ row, index }">
        <div class="btn-tips">
          <ui-btn-tip
            content="删除"
            icon="icon-shanchu"
            class="primary"
            @click.native="handleDele(row, index)"
          />
        </div>
      </template>
    </Table>
    <div class="add-from" @click="addForm()" v-if="tableData.length < 10">
      <Icon type="md-add" />
    </div>
  </div>
</template>
<script>
import { resourceConfigur, queryResourceConfigur } from "@/api/dataGovernance";
import { imgUpload } from "@/api/number-cube";
import uiUploadImg from "@/components/ui-upload-img-control-task/index";
import { mapGetters, mapActions } from "vuex";
export default {
  components: {
    uiUploadImg,
  },
  props: {
    //对应字典
    dictTypedata: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      resourceId: "",
      columns: [
        { title: "姓名", slot: "name", width: 160 },
        { title: "身份证号（必填）", slot: "idCardNo" },
        { title: "民族", slot: "national", width: 140 },
        { title: "性别", slot: "sex", width: 160 },
        { title: "上传照片（必填）", slot: "photoList", width: 240 },
        { title: "操作", slot: "dictionaryCode", width: 70 },
      ],
      tableData: [{}],
      images: [],
    };
  },
  computed: {
    ...mapGetters({
      searchTypeList: "dictionary/getSearchTypeList", //检索类型
      genderList: "dictionary/getGenderList", //性别
      nationList: "dictionary/getNationList", //民族
    }),
  },
  watch: {},
  async created() {
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 删除
    handleDele(row, index) {
      if (this.tableData.length == 1) {
        this.$Message.warning("最少保留一条数据");
        return;
      }
      this.tableData.splice(index, 1);
    },
    addForm() {
      this.tableData.push({});
    },
    handleDis(index, item) {
      if (this.tableData[index].sex === 1) {
        if (this.tableData[index].name === "ccrq") {
          if (item.dataKey == "6") {
            return false;
          } else {
            return true;
          }
        }
        return false;
      } else {
        return true;
      }
    },
    // 初始化
    show(val) {
      this.visible = true;
      this.resourceId = val.id;
      this.$nextTick(() => {
        this.loading = true;
        let data = {
          resourceId: val.id,
          resourceName: val.resourceName,
        };
        queryResourceConfigur(data).then((res) => {
          let data = res.data;
          // 默认值为否---0否/1是
          this.tableData = data.map((item) => {
            let obj = {
              ...item,
              resourceId: val.id,
              national: item.national || 0,
              sex: item.sex || 0,
              photoList: item.photoList || 0,
              dictionaryCode: item.dictionaryCode || "",
              cloudSearch: item.cloudSearch || 0,
            };
            return obj;
          });
          this.loading = false;
        });
      });
    },
    // 确认提交
    confirmHandle() {
      let data = {
        resourceId: this.resourceId,
        configEntityList: this.tableData,
      };
      resourceConfigur(data).then((res) => {
        this.visible = false;
        this.$Message.success(res.msg);
        this.$emit("refreshDataList");
      });
    },
    imgUrlChange(event, index) {},
  },
};
</script>
<style lang="less" scoped>
.config {
  /deep/ .ivu-table-body {
    min-height: 220px;
  }
  /deep/.ivu-table-tbody tr td {
    background-color: #f9f9f9 !important;
  }
  /deep/ .ivu-table-border th {
    border-right: 1px solid #d3d7de !important;
  }
  /deep/.ivu-table td,
  .ivu-table th {
    border-bottom: 1px solid #d3d7de;
  }
  /deep/.ivu-input,
  .ivu-select {
    width: 110px;
  }
}
.add-from {
  background: #f9f9f9;
  margin-top: 3px;
  border: 1px solid #e8eaec;
  text-align: center;
  cursor: pointer;
  /deep/ .ivu-icon-md-add {
    color: #909399;
  }
}
.btn-tips {
  text-align: center;
}
</style>
