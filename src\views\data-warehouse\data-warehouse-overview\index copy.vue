<template>
  <div class="data-warehouse-overview">
    <div class="data-warehouse-overview-bg"></div>
    <div class="data-warehouse-left">
      <ui-card title="资源目录" class="resource-directory m-b10">
        <div slot="extra" class="resource-directory-head">
          <div class="resource-table">
            <i class="icon bg-primary"></i><span class="name auxiliary-color">资源表数量</span>
          </div>
          <div class="data-number">
            <i class="icon bg-border-color"></i><span class="name auxiliary-color">数据量</span>
          </div>
        </div>
        <div class="resource-directory-ul">
          <div v-for="(item, $index) in resourceDirectoryList" class="resource-directory-li" :key="$index">
            <img :src="item.img" class="resource-directory-li-img" alt />
            <div class="resource-directory-li-info">
              <div class="li-name input-color">{{item.name}}</div>
              <div class="resource-table">
                <i class="icon bg-primary"></i>
                <count-to :start-val="0" :end-val="item.resourceTableNumber" :duration="1000" class="number primary"></count-to>
              </div>
              <div class="data-number">
                <i class="icon bg-border-color"></i>
                <count-to :start-val="0" :end-val="item.dataNumber" :duration="1000" class="number text-color"></count-to>
              </div>
            </div>
          </div>
        </div>
      </ui-card>
      <ui-card title="资源分类" class="resource-classification m-b10">
        <div slot="extra" class="share-service-tabs">
          <div 
            :class="resourceType === 1 ? 'tab-item-active primary':'tab-item'" 
            class="label-color" 
            @click="typeChange('RESOURCE_CLASSIFY',1)">
            资源表数量
          </div>
          <div 
            :class="resourceType === 2 ? 'tab-item-active primary':'tab-item'" 
            class="label-color" 
            @click="typeChange('RESOURCE_CLASSIFY',2)">
            数据量
          </div>
        </div>
        <PieEchart 
          :title="{title: { show: false }}" 
          :legend="resourceClassification.legend"
          :graphic="resourceClassification.graphic" 
          :series="resourceClassification.series"/>
      </ui-card>
      <ui-card title="数据来源" class="data-source m-b10">
        <div slot="extra" class="share-service-tabs">
          <div 
            :class="dataResource === 1 ? 'tab-item-active primary':'tab-item'" 
            class="label-color" 
            @click="typeChange('RESOURCE_SOURCE',1)">
            资源表数量
          </div>
          <div 
            :class="dataResource === 2 ? 'tab-item-active primary':'tab-item'" 
            class="label-color" 
            @click="typeChange('RESOURCE_SOURCE',2)">
            数据量
          </div>
        </div>
        <PieEchart 
          :title="{title: { show: false }}" 
          :color="dataSource.color" 
          :legend="dataSource.legend"
          :graphic="dataSource.graphic"
          :series="dataSource.series"/>
      </ui-card>
      <relateAtlas :statisticsList="relateAtlasList"/>
      <!-- <ui-card title="关系图谱" class="relationship-map">
        <div slot="extra" class="relationship-map-head">
          <div class="total-entity">
            <span class="name auxiliary-color">实体总量</span>
            <count-to :start-val="0" :end-val="1852" :duration="1000" class="number primary"></count-to>
          </div>
          <div class="relationship-entity">
            <span class="name auxiliary-color">关系总量</span>
            <count-to :start-val="0" :end-val="52" :duration="1000" class="number primary"></count-to>
          </div>
        </div>
        <PieEchart 
          :title="{title: { show: false }}" 
          :color="relationshipMap.color"
          :legend="relationshipMap.legend"
          :graphic="relationshipMap.graphic"
          :tooltip="relationshipMap.tooltip"
          :series="relationshipMap.series"/>
      </ui-card> -->
    </div>
    <div class="data-warehouse-main">
      <div class="data-warehouse-statistics">
        <div class="data-total">
          <span class="label secondary-color">数据总量</span>
          <count-to :start-val="0" :end-val="dataAll.allCount" :duration="1000" class="number primary"></count-to>
        </div>
        <div class="new-today">
          <span class="label secondary-color">今日新增</span>
          <count-to :start-val="0" :end-val="dataAll.todayCount" :duration="1000" class="number color-green"></count-to>
        </div>
      </div>
      <div id="dataWareHouseGif" class="data-warehouse-main-img"></div>
      <div class="business-layer layer-box card-box-shadow">
        <div class="layer-title primary">业务层</div>
        <div class="layer-info">
          <span class="layer-label input-color">作业数</span>
          <span class="layer-num color-green">{{dataJob.run3}}</span>
          <span class="layer-num primary">/{{dataJob.data3}}</span>
        </div>
        <div class="layer-box-circle"></div>
        <div class="layer-connect"></div>
      </div>
      <div class="archive-layer layer-box card-box-shadow">
        <div class="layer-title primary">归档层</div>
        <div class="layer-info">
          <span class="layer-label input-color">作业数</span>
          <span class="layer-num color-green">{{dataJob.run2}}</span>
          <span class="layer-num primary">/{{dataJob.data2}}</span>
        </div>
        <div class="layer-box-circle"></div>
        <div class="layer-connect"></div>
      </div>
      <div class="resource-layer layer-box card-box-shadow">
        <div class="layer-title primary">资源层</div>
        <div class="layer-info">
          <span class="layer-label input-color">作业数</span>
          <span class="layer-num color-green">{{dataJob.run1}}</span>
          <span class="layer-num primary">/{{dataJob.data1}}</span>
        </div>
        <div class="layer-box-circle"></div>
        <div class="layer-connect"></div>
      </div>
    </div>
    <div class="data-warehouse-left">
      <ui-card title="数据接入" class="data-access m-b10">
        <LineEchart 
          :title="dataAccess.title"
          :legend="dataAccess.legend"
          :grid="dataAccess.grid"
          :xAxis="dataAccess.xAxis"
          :yAxis="dataAccess.yAxis"
          :series="dataAccess.series"/>
      </ui-card>
      <ui-card title="共享服务" class="data-access m-b10">
        <div slot="extra" class="share-service-tabs">
          <div 
            :class="dataServiceActive === 1 ? 'tab-item-active primary':'tab-item'" 
            class="label-color" 
            @click="shareServiceTabHandle(1)">
            数据共享
          </div>
          <div 
            :class="dataServiceActive === 2 ? 'tab-item-active primary':'tab-item'" 
            class="label-color" 
            @click="shareServiceTabHandle(2)">
            数据服务
          </div>
        </div>
        <LineEchart
          v-if="dataServiceActive === 1"
          :key="1"
          :title="dataShare.title"
          :legend="dataShare.legend"
          :grid="dataShare.grid"
          :xAxis="dataShare.xAxis"
          :yAxis="dataShare.yAxis"
          :series="dataShare.series"/>
        <LineEchart
          v-else
          :key="2"
          :title="dataService.title"
          :legend="dataService.legend"
          :grid="dataService.grid"
          :xAxis="dataService.xAxis"
          :yAxis="dataService.yAxis"
          :series="dataService.series"/>
      </ui-card>
      <ui-card title="数据质量" class="data-quality m-b10">
        <div class="radar-echart">
          <RadarEchart :title="{title: { show: false }}"/>
        </div>
        <div class="data-quality-ul">
          <div class="data-quality-li">
            <span class="name title-color">稽核模板</span>
            <count-to :start-val="0" :end-val="52" :duration="1000" class="number primary"></count-to>
          </div>
          <div class="data-quality-li">
            <span class="name title-color">稽核任务</span>
            <count-to :start-val="0" :end-val="543" :duration="1000" class="number primary"></count-to>
          </div>
          <div class="data-quality-li">
            <span class="name title-color">周均问题</span>
            <count-to :start-val="0" :end-val="432" :duration="1000" class="number primary"></count-to>
          </div>
          <div class="data-quality-li">
            <span class="name title-color">昨日问题</span>
            <count-to :start-val="0" :end-val="876" :duration="1000" class="number primary"></count-to>
          </div>
        </div>
      </ui-card>
      <ui-card title="数据作业" class="data-job">
        <div class="data-job-table">
          <div class="data-job-table-head input-color">
            <span>作业类型</span>
            <span>作业总数</span>
            <span>运行中</span>
          </div>
          <div class="data-job-table-body">
            <div v-for="(item, $index) in dataJobList" :key="$index" class="data-job-table-tr text-color">
              <span>{{item.description}}</span>
              <count-to :start-val="0" :end-val="item.defCount" :duration="1000"></count-to>
              <count-to :start-val="0" :end-val="item.instRunningCount" :duration="1000"></count-to>
            </div>
          </div>
        </div>
      </ui-card>
    </div>
  </div>
</template>
<script>
  import { mapGetters, mapActions } from 'vuex'
  import { getSelectCatalogList } from '@/api/dataGovernance'
  import { queryDataByKeys } from '@/api/data-warehouse'
  import * as echarts from 'echarts'
  import { dataWarehouseJson } from '@/libs/system';
  import dataSourceIcon from '@/assets/img/data-warehouse/data-source-icon.png'
  import dataSourceGraphic  from '@/assets/img/data-warehouse/data-source-graphic.png'
  import CountTo from 'vue-count-to'
  import RadarEchart from '@/components/echarts/radar-echart'
  import PieEchart from '@/components/echarts/pie-echart'
  import LineEchart from '@/components/echarts/line-echart'
  import relateAtlas from './relate-atlas.vue'
import { queryDSTableList } from '@/api/config'
  export default {
    components: {
      RadarEchart,
      PieEchart,
      LineEchart,
      relateAtlas,
      CountTo
    },
    data() {
      let self = this
      return {
        resourceDirectoryList: [
          {
            img: require('@/assets/img/data-warehouse/original-library-icon.png'),
            name: '原始库',
            resourceTableNumber: 0,
            dataNumber: 0
          },
          {
            img: require('@/assets/img/data-warehouse/theme-library-icon.png'),
            name: '主题库',
            resourceTableNumber: 0,
            dataNumber: 0
          },
          {
            img: require('@/assets/img/data-warehouse/thematic-library-icon.png'),
            name: '专题库',
            resourceTableNumber: 0,
            dataNumber: 0
          },
          {
            img: require('@/assets/img/data-warehouse/query-library-icon.png'),
            name: '查询库',
            resourceTableNumber: 0,
            dataNumber: 0
          }
        ],
        resourceClassification: {
          list: [],
          leftLegend:[],
          rightLegend:[],
          allCount: 0,
          legend: [{
            type: 'scroll',
            show: true,
            orient: 'vertical',
            top: 'center',
            right: '35%',
            itemGap: 20,
            itemWidth: 8,
            itemHeight: 8,
            icon: 'circle',
            formatter: function(params) {
              return [`{name|${params}}{value|${self.getNum(params)}}`]
            },
            textStyle: {
              padding: [0, 0, 0, 3],
              rich: {
                name: {
                  color: 'rgba(0, 0, 0, 0.35)',
                  width: 28
                },
                value: {
                  color: 'rgba(0, 0, 0, 0.9)',
                  fontFamily: 'MicrosoftYaHei-Bold',
                  fontWeight: 'bold'
                }
              }
            },
            data: ['人', '事', '地']
          },{
            type: 'scroll',
            show: true,
            orient: 'vertical',
            top: 'center',
            right: '5%',
            itemGap: 20,
            itemWidth: 8,
            itemHeight: 8,
            icon: 'circle',
            formatter: function(params) {
              return [`{name|${params}}{value|${self.getNum(params)}}`]
            },
            textStyle: {
              padding: [0, 0, 0, 3],
              rich: {
                name: {
                  color: 'rgba(0, 0, 0, 0.35)',
                  width: 40
                },
                value: {
                  color: 'rgba(0, 0, 0, 0.9)',
                  fontFamily: 'MicrosoftYaHei-Bold',
                  fontWeight: 'bold'
                }
              }
            },
            data: ['物', '组织', '其他']
          }],
          graphic: {
            elements: [
              {
                type: 'circle',
                shape: {
                  r: 30
                },
                style: {
                  fill: '#fff',
                  shadowBlur: 3,
                  shadowColor: 'rgba(44, 134, 248, 0.2)',
                  shadowOffsetY: 5
                },
                left: '12.5%',
                top: 'middle'
              }
            ]
          },
          series: [
            {
              type: 'pie',
              radius: ['42%', '62%'],
              center: ['20%', '50%'],
              label: {
                position: 'center',
                show: true,
                width: 60,
                height: 60,
                borderRadius: 30,
                backgroundColor: '#fff',
                formatter: function(params) {
                  return [`{percent|${self.resourceTypeCount}}`, '{line|}', `{name|总数}`].join('\n')
                },
                rich: {
                  percent: {
                    color: '#2C86F8',
                    fontSize: 14,
                    fontWeight: 'bold',
                    fontFamily: 'MicrosoftYaHei-Bold',
                    padding: [16, 0, 1, 0]
                  },
                  line: {
                    width: 10,
                    height: 2,
                    backgroundColor: '#D3D7DE'
                  },
                  name: {
                    color: 'rgba(0, 0, 0, 0.6)',
                    fontSize: 12,
                    fontFamily: 'MicrosoftYaHei',
                    padding: [5, 0, 0, 0]
                  }
                }
              },
              emphasis: {
                label: {
                  show: true,
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: '#fff',
                  formatter: function(params) {
                    return [`{percent|${params.percent}%}`, '{line|}', `{name|${params.name}}`].join('\n')
                  },
                  rich: {
                    percent: {
                      color: '#2C86F8',
                      fontSize: 14,
                      fontWeight: 'bold',
                      fontFamily: 'MicrosoftYaHei-Bold',
                      padding: [16, 0, 1, 0]
                    },
                    line: {
                      width: 10,
                      height: 2,
                      backgroundColor: '#D3D7DE'
                    },
                    name: {
                      color: 'rgba(0, 0, 0, 0.6)',
                      fontSize: 12,
                      fontFamily: 'MicrosoftYaHei',
                      padding: [5, 0, 0, 0]
                    }
                  }
                }
              },
              data: [
                { value: 1048, name: '人' },
                { value: 735, name: '事' },
                { value: 580, name: '地' },
                { value: 484, name: '物' },
                { value: 300, name: '组织' },
                { value: 300, name: '其他' }
              ]
            }
          ]
        },
        dataSource: {
          list: [],
          color: ['#879DC6', '#4DC7D4', '#2C86F8'],
          graphic: {
            elements: [
              {
                type: 'image',
                style: {
                  image: dataSourceGraphic, // 这里添加图片地址
                  width: 64,
                  height: 64
                },
                left: 'center',
                top: 'center'
              },
              {
                type: 'image',
                style: {
                  image: dataSourceIcon, // 这里添加图片地址
                  width: 28,
                  height: 28
                },
                left: 'center',
                top: 'center'
              }
            ]
          },
          series: [
            {
              type: 'pie',
              radius: ['42%', '54%'],
              center: ['50%', '50%'],
              label: {
                position: 'outer',
                // alignTo: 'edge',
                // margin: 10,
                formatter: function (params) {
                  if (params.data.dataKey === '1') {
                    return `{realtime|${params.percent}%}\n{name|${params.name}}`
                  } else {
                    return `{cycle|${params.percent}%}\n{name|${params.name}}`
                  }
                },
                rich: {
                  cycle: {
                    fontSize: 14,
                    color: 'rgba(0, 0, 0, 0.9)',
                    fontWeight: 'bold',
                    lineHeight: 20
                  },
                  realtime: {
                    fontSize: 14,
                    fontWeight: 'bold',
                    lineHeight: 24,
                    color: '#E99E53'
                  },
                  name: {
                    fontSize: 12,
                    color: 'rgba(0, 0, 0, 0.35)',
                    lineHeight: 16
                  }
                }
              },
              labelLine: {
                length: 10,
                length2: 50,
                maxSurfaceAngle: 0,
                lineStyle: {
                  color: '#D3D7DE',
                  type: 'dashed'
                }
              },
              data: [
                { value: 148, name: '警务数据' },
                { value: 75, name: '感知数据' },
                { value: 250, name: '视图数据' }
              ]
            }
          ]
        },
        relationshipMap: {
          color: ['#2C86F8', '#F0824C', '#F29F4C', '#4DC7D4', '#9089FF', '#D997FF', '#8CCFFF'],
          tooltip: {
            show: false
          },
          graphic: {
            elements: [
              {
                type: 'image',
                style: {
                  image: dataSourceGraphic, // 这里添加图片地址
                  width: 64,
                  height: 64
                },
                left: 'center',
                top: 'center'
              }
            ]
          },
          series: [
            {
              type: 'pie',
              radius: ['42%', '54%'],
              center: ['50%', '50%'],
              zlevel: 2,
              label: {
                position: 'center',
                show: true,
                width: 60,
                height: 60,
                borderRadius: 30,
                backgroundColor: '#fff',
                formatter: function(params) {
                  return [`{percent|${1852}}`, '{line|}', `{name|总数}`].join('\n')
                },
                rich: {
                  percent: {
                    color: '#2C86F8',
                    fontSize: 14,
                    fontWeight: 'bold',
                    fontFamily: 'MicrosoftYaHei-Bold',
                    padding: [16, 0, 1, 0]
                  },
                  line: {
                    width: 10,
                    height: 2,
                    backgroundColor: '#D3D7DE'
                  },
                  name: {
                    color: 'rgba(0, 0, 0, 0.6)',
                    fontSize: 12,
                    fontFamily: 'MicrosoftYaHei',
                    padding: [5, 0, 0, 0]
                  }
                }
              },
              emphasis: {
                label: {
                  show: true,
                  width: 60,
                  height: 60,
                  borderRadius: 30,
                  backgroundColor: '#fff',
                  formatter: function(params) {
                    return [`{percent|${params.value}}`, '{line|}', `{name|${params.name}}`].join('\n')
                  },
                  rich: {
                    percent: {
                      color: '#2C86F8',
                      fontSize: 14,
                      fontWeight: 'bold',
                      fontFamily: 'MicrosoftYaHei-Bold',
                      padding: [16, 0, 1, 0]
                    },
                    line: {
                      width: 10,
                      height: 2,
                      backgroundColor: '#D3D7DE'
                    },
                    name: {
                      color: 'rgba(0, 0, 0, 0.6)',
                      fontSize: 12,
                      fontFamily: 'MicrosoftYaHei',
                      padding: [5, 0, 0, 0]
                    }
                  }
                }
              },
              data: [
                { value: 148, name: '人' },
                { value: 735, name: '车' },
                { value: 580, name: '案件' },
                { value: 148, name: '手机号' },
                { value: 735, name: 'IMSI' },
                { value: 50, name: 'RFID' },
                { value: 80, name: 'MAC' }
              ]
            },
            {
              type: 'pie',
              radius: ['46%', '46%'],
              center: ['50%', '50%'],
              zlevel: 1,
              label: {
                position: 'outer',
                formatter: function (params) {
                  if (params.data.dataKey === '1') {
                    return `{name|${params.name}:}{realtime|${params.percent}%}`
                  } else {
                    return `{name|${params.name}:}{cycle|${params.percent}%}`
                  }
                },
                rich: {
                  cycle: {
                    fontSize: 14,
                    color: 'rgba(0, 0, 0, 0.9)',
                    fontWeight: 'bold',
                    lineHeight: 20
                  },
                  realtime: {
                    fontSize: 14,
                    fontWeight: 'bold',
                    lineHeight: 24,
                    color: '#E99E53'
                  },
                  name: {
                    fontSize: 12,
                    color: 'rgba(0, 0, 0, 0.35)',
                    lineHeight: 16
                  }
                }
              },
              labelLine: {
                length: 10,
                length2: 30,
                maxSurfaceAngle: 0,
                lineStyle: {
                  color: '#D3D7DE',
                  type: 'dashed'
                }
              },
              data: [
                { value: 1048, name: '人' },
                { value: 735, name: '车' },
                { value: 580, name: '案件' },
                { value: 1048, name: '手机号' },
                { value: 735, name: 'IMSI' },
                { value: 580, name: 'RFID' },
                { value: 580, name: 'MAC' }
              ]
            }
          ]
        },
        dataAccess: {
          title: {
            show: true,
            subtext: '单位：个'
          },
          grid: {
            left: '0',
            top: '10%',
            right: '0.1%',
            bottom: '20%',
            containLabel: true
          },
          legend: {
            type: 'scroll',
            data: ['总量', '视图数据', '感知数据', '警务数据'],
            bottom: '3%',
            itemGap: 24,
            itemWidth: 12,
            itemHeight: 2,
            icon: 'rect',
            textStyle: {
              color: 'rgba(0, 0, 0, 0.35)',
              padding: [0, 0, 0, 3]
            }
          },
          xAxis: {
            type: 'category',
            data: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7', '1-8'],
            axisLine: {
              lineStyle: {
                color: '#D3D7DE'
              }
            },
            boundaryGap: true,
            axisTick: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.6)'
            }
          },
          yAxis: {
            axisLine: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.6)'
            },
            axisTick: {
              show: false
            },
            splitNumber: 5,
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#D3D7DE'
              }
            }
          },
          series: [
            {
              type: 'line',
              name: '总量',
              data: [30, 35, 38, 39, 50, 61, 50, 75],
              symbolSize: 0,
              smooth: true,
              color: '#2C86F8',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(44, 134, 248, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(91, 163, 255, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '视图数据',
              data: [50, 35, 38, 39, 50, 61, 100, 75],
              symbolSize: 0,
              smooth: true,
              color: '#4DC7D4',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(44, 134, 248, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(91, 163, 255, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '感知数据',
              data: [80, 35, 38, 89, 50, 61, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#F1BA4D',
              lineStyle: {
                width: 2,
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(241, 186, 77, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(241, 186, 77, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '警务数据',
              data: [80, 100, 38, 2, 50, 61, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#6760D9',
              lineStyle: {
                width: 2,
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(103, 96, 217, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(103, 96, 217, 0)'
                    }
                  ],
                  false
                )
              }
            }
          ]
        },
        dataShare: {
          title: {
            show: true,
            subtext: '单位：万'
          },
          grid: {
            left: '0',
            top: '10%',
            right: '0.1%',
            bottom: '20%',
            containLabel: true
          },
          legend: {
            type: 'scroll',
            data: ['总量', 'ES', 'Kafka', 'mysql', '接口'],
            bottom: '3%',
            itemGap: 24,
            itemWidth: 12,
            itemHeight: 2,
            icon: 'rect',
            textStyle: {
              color: 'rgba(0, 0, 0, 0.35)',
              // lineHeight: 18,
              padding: [0, 0, 0, 3]
            }
          },
          xAxis: {
            type: 'category',
            data: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7', '1-8'],
            axisLine: {
              lineStyle: {
                color: '#D3D7DE'
              }
            },
            boundaryGap: true,
            axisTick: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.6)'
            }
          },
          yAxis: {
            axisLine: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.6)'
            },
            axisTick: {
              show: false
            },
            splitNumber: 5,
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#D3D7DE'
              }
            }
          },
          series: [
            {
              type: 'line',
              name: '总量',
              data: [30, 35, 38, 39, 50, 81, 50, 75],
              symbolSize: 0,
              smooth: true,
              color: '#2C86F8',
              lineStyle: {
                width: 2,
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(44, 134, 248, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(91, 163, 255, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: 'ES',
              data: [50, 35, 38, 39, 30, 61, 100, 75],
              symbolSize: 0,
              smooth: true,
              color: '#6760D9',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(103, 96, 217, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(103, 96, 217, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: 'Kafka',
              data: [80, 35, 38, 89, 50, 61, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#879DC6',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(135, 157, 198, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(135, 157, 198, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: 'mysql',
              data: [80, 100, 38, 2, 50, 61, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#4DC7D4',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(44, 134, 248, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(91, 163, 255, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '接口',
              data: [10, 10, 38, 2, 50, 61, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#F1BA4D',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(241, 186, 77, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(241, 186, 77, 0)'
                    }
                  ],
                  false
                )
              }
            }
          ]
        },
        dataService: {
          title: {
            show: true,
            subtext: '单位：万'
          },
          grid: {
            left: '0',
            top: '10%',
            right: '0.1%',
            bottom: '20%',
            containLabel: true
          },
          legend: {
            type: 'scroll',
            data: ['总量', '对比', '查询', '聚类'],
            bottom: '3%',
            itemGap: 24,
            itemWidth: 12,
            itemHeight: 2,
            icon: 'rect',
            textStyle: {
              color: 'rgba(0, 0, 0, 0.35)',
              // lineHeight: 18,
              padding: [0, 0, 0, 3]
            }
          },
          xAxis: {
            type: 'category',
            data: ['1-1', '1-2', '1-3', '1-4', '1-5', '1-6', '1-7', '1-8'],
            axisLine: {
              lineStyle: {
                color: '#D3D7DE'
              }
            },
            boundaryGap: true,
            axisTick: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.6)'
            }
          },
          yAxis: {
            axisLine: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.6)'
            },
            axisTick: {
              show: false
            },
            splitNumber: 5,
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#D3D7DE'
              }
            }
          },
          series: [
            {
              type: 'line',
              name: '总量',
              data: [30, 35, 300, 39, 50, 150, 50, 300],
              symbolSize: 0,
              smooth: true,
              color: '#2C86F8',
              lineStyle: {
                width: 2,
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(44, 134, 248, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(91, 163, 255, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '对比',
              data: [50, 35, 38, 250, 200, 61, 100, 75],
              symbolSize: 0,
              smooth: true,
              color: '#6760D9',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(103, 96, 217, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(103, 96, 217, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '查询',
              data: [80, 35, 150, 89, 220, 200, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#879DC6',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(135, 157, 198, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(135, 157, 198, 0)'
                    }
                  ],
                  false
                )
              }
            },
            {
              type: 'line',
              name: '聚类',
              data: [80, 100, 200, 2, 50, 240, 70, 100],
              symbolSize: 0,
              smooth: true,
              color: '#4DC7D4',
              lineStyle: {
                width: 2
              },
              areaStyle: {
                //区域填充样式
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(44, 134, 248, 0.2)'
                    },
                    {
                      offset: 1,
                      color: 'rgba(91, 163, 255, 0)'
                    }
                  ],
                  false
                )
              }
            }
          ]
        },
        dataJobList: [
          // {
          //   description: '资源作业',
          //   jobTotal: 54,
          //   inOperation: 3214
          // },
          // {
          //   description: '归档作业',
          //   jobTotal: 32,
          //   inOperation: 54332
          // },
          // {
          //   description: '业务作业',
          //   jobTotal: 12,
          //   inOperation: 4343
          // },
          // {
          //   description: '质量稽核',
          //   jobTotal: 43,
          //   inOperation: 32
          // },
          // {
          //   description: '其他作业',
          //   jobTotal: 98,
          //   inOperation: 5465
          // }
        ],
        dataServiceActive: 1,   // 共享服务：1-数据共享, 2-数据服务
        resourceType: 1, // 资源分类：1-资源表数量，2-数据量
        resourceTypeList: [], // 资源分类
        resourceTypeCount: 0, // 总数
        dataResourceList: [],
        dataResource: 1,      // 数据来源
        setIn: null,
        dataWareHouseIndex: 0,
        dataWareHouseGif: '',
        dataWareHouseGifSize: 40,
        dataJob: {},
        dataAll: {},
        relateAtlasList: {}
      }
    },
    computed: {
    ...mapGetters({
      businessTypeList: 'dictionary/getBusinessType', //数据作业
      datasourceTypeList: 'dictionary/getDatasourceType', //数据来源
    })
  },
    mounted() {
      this.getDictData()
      // 初始化渲染数仓中间gift图
      var params = {
          container: document.getElementById('dataWareHouseGif'),
          renderer: 'svg',
          loop: true,
          autoplay: true,
          animationData: dataWarehouseJson
      };
      lottie.loadAnimation(params);

      this.init()
    },
    methods: {
      ...mapActions({
        getDictData: 'dictionary/getDictAllData',
        getSystemAllData: 'systemParam/getSystemAllData'
      }),
      async init () {
        // 资源分类类型数据
        await getSelectCatalogList({}).then(res => {
          console.log('目录列表', res)
          let list = res.data
          this.resourceTypeList = list.map((item, index) => {
            // if (index%2 == 0) {
            //   this.resourceClassification.leftLegend.push(item.catalogName)
            // }else{
            //   this.resourceClassification.rightLegend.push(item.catalogName)
            // }
            return { 
              id: item.id, 
              name: item.catalogName
            }
          })
        })
        var param = {
          dwTypes: ['RESOURCE_DIRECTORY', 'RESOURCE_CLASSIFY', 'RESOURCE_SOURCE', 'RELATION_GRAPH', 'DATA_ACCESS', 'DATA_WORK', 'DATA_STATISTIC']
        }
        queryDataByKeys(param).then(res => {
          console.log('静态库统计数据', res)
          var list = res.data
          list.forEach(item => {
            // 资源目录
            if (item.dwType == 'RESOURCE_DIRECTORY') {
              console.log('资源目录数据', JSON.parse(item.dwValue))
              var arr = JSON.parse(item.dwValue)
              arr.map(ite => {
                if (ite.type == '1') {
                  this.resourceDirectoryList[0].resourceTableNumber = ite.tablesCount
                  this.resourceDirectoryList[0].dataNumber = ite.dataCount
                }
                if (ite.type == '2') {
                  this.resourceDirectoryList[1].resourceTableNumber = ite.tablesCount
                  this.resourceDirectoryList[1].dataNumber = ite.dataCount
                }
                if (ite.type == '3') {
                  this.resourceDirectoryList[2].resourceTableNumber = ite.tablesCount
                  this.resourceDirectoryList[2].dataNumber = ite.dataCount
                }
                if (ite.type == '4') {
                  this.resourceDirectoryList[3].resourceTableNumber = ite.tablesCount
                  this.resourceDirectoryList[3].dataNumber = ite.dataCount
                }
              })
            }
            
            // 资源分类
            if (item.dwType == 'RESOURCE_CLASSIFY' && JSON.parse(item.dwValue)) {
              console.log('资源分类数据', JSON.parse(item.dwValue))
              // if(item.dwValue) this.resourceClassification.show = true
              this.resourceClassification.list = JSON.parse(item.dwValue) || []
              this.typeChange('RESOURCE_CLASSIFY', 1)
            }

            // 数据来源
            if (item.dwType == 'RESOURCE_SOURCE') {
              console.log('数据来源数据', JSON.parse(item.dwValue))
              this.dataSource.list = JSON.parse(item.dwValue) || []
              this.typeChange('RESOURCE_SOURCE', 1)
            }

            // 关系图谱
            if (item.dwType == 'RELATION_GRAPH') {
              console.log('关系图谱数据', JSON.parse(item.dwValue))
              this.relateAtlasList = JSON.parse(item.dwValue) || []
            }

            // 数据接入
            if (item.dwType == 'DATA_ACCESS') {
              console.log('数据接入数据', JSON.parse(item.dwValue))
              var list = JSON.parse(item.dwValue) || []
              list.forEach((ite, index) => {
                if (index == 0) {
                  var xArr = []
                  ite.data.forEach(i => {
                    xArr.push(i.date.substr(5))
                  })
                  this.dataAccess.xAxis.data = xArr
                }

                var arr = []
                ite.data.forEach(i => {
                  arr.push(i.dataCount)
                })

                if(ite.type == '1') this.dataAccess.series[3].data = arr;
                if(ite.type == '2') this.dataAccess.series[2].data = arr;
                if(ite.type == '3') this.dataAccess.series[1].data = arr;
                if(!ite.type) this.dataAccess.series[0].data = arr;

              })
              this.$forceUpdate()
            }

            // 数据作业(中间)
            if (item.dwType == 'DATA_WORK') {
              var list = JSON.parse(item.dwValue)
              this.dataJob = {}
              list.forEach(item => {
                this.dataJob['data'+item.type] = item.dataCount
                this.dataJob['run'+item.type] = item.runCount
              })
              console.log('数据作业数据(中间)', JSON.parse(item.dwValue))
            }

            // 数据总量统计
            if (item.dwType == 'DATA_STATISTIC') {
              var {odsAllCount, odsTodayCount} = JSON.parse(item.dwValue)
              this.dataAll.allCount = odsAllCount
              this.dataAll.todayCount = odsTodayCount
              console.log('数据总量统计数据', JSON.parse(item.dwValue))
            }

          })
        })

        this.queryDataJobList()
      },
      // 切换共享服务
      shareServiceTabHandle(val) {
        this.dataServiceActive = val
      },
      /**
       * 类型切换
       */
      typeChange(type, val) {
        var _this = this
        if (type == 'RESOURCE_CLASSIFY') {
          this.resourceType = val
          this.resourceTypeCount = 0
          this.resourceClassification.legend[0].data = []
          this.resourceTypeList.forEach((item, index) => {
            var obj = _this.resourceClassification.list.find(i => { return i.type == item.id})
            item.value = obj ? val == 1 ? obj.tablesCount : obj.dataCount : 0
            this.resourceTypeCount += item.value
            if (index%2 == 0) {
              this.resourceClassification.leftLegend.push(item)
            }else{
              this.resourceClassification.rightLegend.push(item)
            }
          })

          this.resourceClassification.legend[0].data = this.resourceClassification.leftLegend
          this.resourceClassification.legend[1].data = this.resourceClassification.rightLegend

          var arr = [];
          arr = this.resourceTypeList.map(item => {
            return {
              name: item.name,
              value: item.value
            }
          })
          this.resourceClassification.series[0].data = arr
        }

        if (type == 'RESOURCE_SOURCE') {
          this.dataResource = val
          console.log('数据来源字典', this.datasourceTypeList)
          this.dataResourceList = [];
          this.datasourceTypeList.forEach(item => {
            var obj = this.dataSource.list.find(i => { return i.type == item.dataKey})
            if (obj) {
              obj.name = item.dataValue
              obj.value = val == 1 ? obj.tablesCount : obj.dataCount
              this.dataResourceList.push(obj)
            }
          })
          this.dataSource.series[0].data = this.dataResourceList
        }

      },
      getNum(name) {
        var obj = this.resourceTypeList.find(item => { return item.name == name})
        if (obj) {
          return obj.value
        } else {
          return 0
        }
      },
      queryDataJobList () {
        var param = {
          pageNumber: 1,
          ageSize: 200,
        }
        queryDSTableList(param).then(res => {
          console.log('数据作业', res)
          this.dataJobList = res.data.entities
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .m-b10 {
    margin-bottom: 10px;
  }
  /deep/.ui-card {
        width: 100%;
        flex: 1;
        .card-content {
          // display: flex;
          height: calc(~'100% - 30px');
        }
        .radar-echart {
          width: 50%;
          height: 100%;
        }
      }
  .data-warehouse-overview {
    display: flex;
    flex: 1;
    background: url('~@/assets/img/archives/bg_point.png') 5px 5px;
    position: relative;
    .data-warehouse-overview-bg {
      width: 45%;
      height: 80%;
      background: #fff;
      filter: blur(50px);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .data-warehouse-left {
      width: 400px;
      height: 100%;
      display: flex;
      flex-direction: column;
      /deep/.ui-card {
        width: 100%;
        flex: 1;
        .card-content {
          // display: flex;
          height: calc(~'100% - 30px');
        }
        .radar-echart {
          width: 50%;
          height: 100%;
        }
      }
      .resource-directory {
        .resource-directory-head {
          display: flex;
          margin-top: 10px;
          .resource-table, .data-number {
            display: flex;
            align-items: center;
          }
          .data-number {
            margin: 0 34px 0 20px;
            .icon{
              background: #1FAF8A;
            }
          }
          .icon {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
          }
          .name {
            font-size: 12px;
            line-height: 18px;
          }
        }
        /deep/ .card-content {
          padding: 20px 0 !important;
        }
        .resource-directory-ul {
          display: flex;
          flex-wrap: wrap;
          align-content: space-between;
          .resource-directory-li {
            display: flex;
            width: 50%;
            padding-left: 20px;
            .resource-directory-li-img {
              width: 68px;
              height: 68px;
              object-fit: contain;
            }
            .resource-directory-li-info {
              margin-left: 12px;
              flex: 1;
              .li-name {
                font-size: 14px;
                font-family: 'MicrosoftYaHei-Bold';
                font-weight: bold;
                line-height: 20px;
              }
              .resource-table, .data-number {
                display: flex;
                align-items: center;
                margin-top: 2px;
                .icon {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  display: inline-block;
                  margin-right: 6px;
                }
                .number {
                  font-size: 14px;
                  line-height: 20px;
                }
              }
              .data-number {
                margin-top: 4px;
                .icon{
                  background: #1FAF8A;
                }
                .number{
                  color: #1FAF8A;
                }
              }
            }
          }
        }
      }
      .resource-classification {
        /deep/ .card-content {
          padding: 0 !important;
        }
      }
      .data-source {
        /deep/ .card-content {
          padding: 0 !important;
          height: 100%;
          // height: calc( 100% - 30px );
          margin-top: -30px;
        }
      }
      .relationship-map {
        .relationship-map-head {
          display: flex;
          .total-entity, .relationship-entity {
            display: flex;
            align-items: center;
            margin-right: 40px;
            .name {
              font-size: 12px;
              line-height: 18px;
            }
            .number {
              font-size: 14px;
              font-family: 'MicrosoftYaHei-Bold';
              font-weight: bold;
              margin-left: 4px;
              line-height: 20px;
            }
          }
          .relationship-entity {
            margin-right: 30px;
          }
        }
        /deep/ .card-content {
          padding: 0 !important;
          height: 100%;
          margin-top: -30px;
        }
      }
      .data-access {
        overflow: unset;
        /deep/ .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
        /deep/ .card-content {
          height: 167px;
          padding: 0 20px !important;
        }
      }
      .share-service-tabs {
        display: flex;
        z-index: 99;
        .tab-item, .tab-item-active {
          font-size: 14px;
          line-height: 20px;
          margin-right: 20px;
          cursor: pointer;
        }
        .tab-item-active {
          font-family: 'MicrosoftYaHei-Bold';
          font-weight: bold;
          position: relative;
        }
        .tab-item-active::after {
          content: '';
          width: 100%;
          height: 2px;
          background: #2C86F8;
          position: absolute;
          left: 0;
          bottom: -1px;
        }
      }
      .data-quality {
        overflow: unset;
        /deep/ .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
        /deep/ .card-content {
          padding: 0 20px 0 10px !important;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .data-quality-ul {
          .data-quality-li {
            background: #EBEDF1;
            width: 154px;
            height: 30px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            box-sizing: border-box;
            margin-top: 10px;
            .name {
              font-size: 14px;
              line-height: 20px;
            }
            .number {
              font-size: 16px;
              line-height: 22px;
            }
          }
          &>div:first-child {
            margin: 0;
          }
        }
      }
      .data-job {
        /deep/ .card-content {
          height: 170px;
          padding: 20px 16px !important;
        }
        .data-job-table {
          display: flex;
          flex-direction: column;
          flex: 1;
          .data-job-table-head {
            background: #EBEDF1;
            height: 30px;
            border-radius: 4px;
            display: flex;
            &>span {
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: bold;
              font-family: 'MicrosoftYaHei-Bold';
              font-size: 14px;
              line-height: 20px;
            }
            &>span:last-child {
              flex: 2;
            }
          }
          .data-job-table-body {
            // flex: 1;
            height: 100px;
            // overflow-y: auto;
            overflow: hidden;
            .data-job-table-tr {
              height: 32px;
              display: flex;
              background: #fff;
              &>span {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 14px;
                line-height: 20px;
              }
              &>span:last-child {
                flex: 2;
              }
            }
            .data-job-table-tr:nth-child(2n+1) {
              background: #F9F9F9;
            }
          }
          .data-job-table-body:hover {
            overflow: auto;
          }
        }
      }
    }
    .data-warehouse-main {
      flex: 1;
      position: relative;
      .data-warehouse-statistics {
        display: flex;
        margin-top: 30px;
        justify-content: center;
        align-items: center;
        .data-total, .new-today {
          display: flex;
          align-items: center;
        }
        .new-today {
          margin-left: 100px;
        }
        .label {
          margin-right: 20px;
          font-size: 14px;
          line-height: 20px;
        }
        .number {
          font-size: 35px;
          font-family: 'MicrosoftYaHei-Bold';
          font-weight: bold;
          line-height: 35px;
        }
      }
      .data-warehouse-main-img {
        width: 87%;
        height: 89%;
        // background: url('~@/assets/img/data-warehouse/data-warehouse-overview.png') no-repeat center/contain;
        position: absolute;
        right: 10px;
        bottom: 0;
        z-index: 2;
        &>img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .layer-box {
        background: #fff;
        width: 134px;
        height: 78px;
        border-radius: 4px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 3;
        .layer-title {
          font-size: 20px;
          font-family: 'MicrosoftYaHei-Bold';
          line-height: 24px;
          font-weight: bold;
        }
        .layer-info {
          display: flex;
          align-items: center;
          margin-top: 8px;
          .layer-label {
            font-size: 14px;
            line-height: 20px;
            margin-right: 10px;
          }
          .layer-num {
            font-size: 18px;
            line-height: 24px;
          }
        }
        .layer-box-circle {
          width: 14px;
          height: 14px;
          background: #fff;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          right: 0;
          transform: translate(50%, -50%);
          box-shadow: 0px 0px 7px 0px #48BAFF;
          border: 1px solid #48BAFF;
        }
        .layer-box-circle::before {
          content: '';
          width: 8px;
          height: 8px;
          background: #48BAFF;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
      }
      .business-layer {
        position: absolute;
        top: 14%;
        left: 20px;
        z-index: 1;
        .layer-connect {
          border: 1px dashed #48BAFF;
          width: 120px;
          height: 1px;
          position: absolute;
          top: 50%;
          right: -120px;
          transform: translate(0, -50%);
        }
        .layer-connect::before {
          content: '';
          border: 1px dashed #48BAFF;
          width: 100px;
          height: 1px;
          position: absolute;
          transform: rotate(54deg);
          top: 40px;
          left: 100px;
        }
      }
      .archive-layer {
        position: absolute;
        top: 30%;
        left: 20px;
        z-index: 1;
        .layer-connect {
          border: 1px dashed #48BAFF;
          width: 50px;
          height: 1px;
          position: absolute;
          top: 50%;
          right: -50px;
          transform: translate(0, -50%);
        }
        .layer-connect::before {
          content: '';
          border: 1px dashed #48BAFF;
          width: 100px;
          height: 1px;
          position: absolute;
          transform: rotate(54deg);
          top: 40px;
          left: 30px;
        }
      }
      .resource-layer {
        position: absolute;
        top: 46%;
        left: 20px;
        z-index: 1;
        .layer-connect {
          border: 1px dashed #48BAFF;
          width: 50px;
          height: 1px;
          position: absolute;
          top: 50%;
          right: -50px;
          transform: translate(0, -50%);
        }
        .layer-connect::before {
          content: '';
          border: 1px dashed #48BAFF;
          width: 100px;
          height: 1px;
          position: absolute;
          transform: rotate(54deg);
          top: 40px;
          left: 30px;
        }
      }
    }
    // .pie-echart {
    //   width: 140px;
    //   height: 140px;
    //   background: linear-gradient(150deg, #EAF7FF 0%, #F9FCFF 100%);
    //   box-shadow: inset 0px 5px 6px 0px rgba(151, 197, 255, 0.2);
    //   border-radius: 50%; 
    // }
  }
</style>