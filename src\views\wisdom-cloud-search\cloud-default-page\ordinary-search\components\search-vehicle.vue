<template>
  <div class="search  card-border-color">
    <Form :inline="true" :class="visible ? 'advanced-search-show':''">
      <div class="general-search">
        <div class="input-content">
          <FormItem label="车牌号码:">
            <Input v-model="queryParam.plateNo" placeholder="请输入"></Input>
          </FormItem>
          <FormItem label="抓拍时段:">
            <ui-tag-select
              ref="tagSelect1"
              :hideCheckAll='true'
              :value="captureTimePeriod[0].name"
              @input="e => { input(e, 'timeSlot') }"
            >
              <ui-tag-select-option
                v-for="(item, $index) in captureTimePeriod"
                :key="$index"
                :name="item.name">
                {{ item.name }}
              </ui-tag-select-option>
            </ui-tag-select>
            <DatePicker 
                v-if="queryParam.timeSlot == '自定义'" 
                v-model="queryParam.timeSlotArr" 
                type="datetimerange" 
                format="yyyy-MM-dd HH:mm:ss" 
                placeholder="请选择抓拍时间段"
                @on-change="dateChange"
                style="width: 330px"></DatePicker>
          </FormItem>
        </div>
        <div class="btn-group">
          <span class="advanced-search-text primary" @click="advancedSearchHandle($event)">
            更多条件 <i class="iconfont icon-jiantou"></i>
          </span>
          <Button type="primary" @click="searchHandle">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </div>
      </div>
      <!--更多搜索条件-->
      <div class="advanced-search">
        <section class="search-container">
          <div class="search-item-container" id="searchItemContainer">
            <div class="classify-content">
              <span class="classify-name">常用</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="设备资源:">
                    <div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{queryParam.selectDeviceList.length}}）</div>
                  </FormItem>
                  <FormItem label="角度:" class="percent-60">
                    <selectTag 
                      ref="pointOfView"
                      :list="pointViewList" 
                      vModel="pointOfView"
                      @selectItem="selectItem"/>
                    <!-- <ui-tag-select>
                      <ui-tag-select-option
                        v-for="(item, $index) in angleList"
                        :key="$index"
                        :name="item.name">
                        {{ item.name }}
                      </ui-tag-select-option>
                    </ui-tag-select> -->
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">车牌</span>
              <div class="items">
                <div class="advanced-search-item card-border-color" id="vehicleCP">
                  <FormItem label="车牌类型:">
                    <selectTag 
                      ref="plateClass"
                      :moreBtn="true"
                      :list="plateClassList" 
                      vModel="plateClass"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="车牌颜色:">
                    <ui-tag-select
                      ref="plateColor"
                      @input="
                        e => {
                          input(e, 'plateColor')
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in licensePlateColorList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey">
                        <div :style="{borderColor: licensePlateColorArray[item.dataKey].borderColor}" class="plain-tag">
                          <div :style="licensePlateColorArray[item.dataKey].style" :class="licensePlateColorArray[item.dataKey].class">{{ item.dataValue }}</div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="遮挡:">
                    <selectTag 
                      ref="cover"
                      :list="plateOcclusionList" 
                      vModel="cover"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">车身</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="车身类型:">
                    <selectTag 
                      :isChild="true"
                      :carColor="true"
                      :list="vehicleClassTypeList" 
                      ref="bodyType"
                      vModel="bodyType"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color" id="vehicleCSYS">
                  <FormItem label="车身颜色:">
                    <ui-tag-select
                      ref="bodyColor"
                      @input="
                        e => {
                          input(e, 'bodyColor')
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in plateColorIpbdList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey">
                        <div v-if="vehicleBodyColorArray[item.dataKey]" :style="{borderColor: vehicleBodyColorArray[item.dataKey].borderColor}" class="plain-tag-color">
                          <div :style="vehicleBodyColorArray[item.dataKey].style"></div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color" id="vehiclePP">
                  <FormItem label="车辆品牌:">
                    <div class="select-tag-button" @click="selectBrandHandle">选择车辆品牌/已选（{{queryParam.plateBrands.length}}）</div>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="特殊车辆:">
                    <selectTag 
                      :list="specialVehicleList" 
                      ref="specialPlate"
                      vModel="specialPlate"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color" id="markerTop">
                  <FormItem label="车顶物件:">
                    <selectTag 
                      :list="roofItemsList" 
                      ref="carRoof"
                      vModel="carRoof"
                      @selectItem="selectItem"/>
                  </FormItem>
                  <FormItem label="年检标:" class="percent-60">
                    <selectTag 
                      :list="annualInspectionNumList" 
                      ref="inspectAnnually"
                      vModel="inspectAnnually"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color" id="marker">
                  <FormItem label="标志物:">
                    <selectTagMore 
                      :list="markerTypeList" 
                      ref="marker"
                      vModel="markerList"
                      @selectItemMore="selectItemMore"/>
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">行为</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="副驾有人:">
                    <selectTag 
                      :list="getCopilotList" 
                      ref="ifCoDriverPeople"
                      vModel="ifCoDriverPeople"
                      @selectItem="selectItem"/>
                  </FormItem>
                  <FormItem label="面部遮挡:" class="percent-60">
                    <selectTag 
                      :list="facialOcclusionList" 
                      ref="facialOcclusion"
                      vModel="facialOcclusion"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div>
                <!-- <div class="advanced-search-item card-border-color" id="vehicleZYB">
                  <FormItem label="遮阳板:">
                    <selectTag 
                      :list="sunVisorStatusList" 
                      ref="sunVisorStatus"
                      vModel="sunVisorStatus"
                      @selectItem="selectItem"/>
                  </FormItem>
                </div> -->
              </div>
            </div>
          </div>
          <div class="img-search" @click="clickTagVehicleHandle">
            <div class="vehicle-front">
              <span v-for="(item, index) in anchorFrontList" :data-id="item.dataId" :class="item.class" :key="index">{{item.name}}</span>
              <!-- <span data-id="vehicleCP" class="vehicle-cp">车牌</span>
              <span data-id="vehiclePP" class="vehicle-pp">品牌</span>
              <span data-id="vehicleCSPZ" class="vehicle-cspz">车身喷字</span>
              <span data-id="vehicleZJH" class="vehicle-zjh">纸巾盒</span>
              <span data-id="vehicleBJ" class="vehicle-bj">摆件</span>
              <span data-id="vehicleGJ" class="vehicle-gj">挂件</span>
              <span data-id="vehicleTC" class="vehicle-tc">天窗</span>
              <span data-id="vehicleZYB" class="vehicle-zyb">遮阳板</span>
              <span data-id="vehicleHS" class="vehicle-hs">红绳</span>
              <span data-id="vehicleCSYS" class="vehicle-csys">车身颜色</span> -->
              <img src="@/assets/img/wisdom-cloud-search/vehicle-back.png" alt="">
            </div>
            <div class="vehicle-back">
              <span v-for="(item, index) in anchorAfterList" :data-id="item.dataId" :class="item.class" :key="index">{{item.name}}</span>
              <!-- <span data-id="vehicleCSPZ" class="vehicle-back-cspz">车身喷字</span>
              <span data-id="vehicleCP" class="vehicle-back-cp">车牌</span>
              <span data-id="vehiclePP" class="vehicle-back-pp">品牌</span>
              <span data-id="vehicleCHG" class="vehicle-back-chg">车后盖</span>
              <span data-id="vehicleTX" class="vehicle-back-tx">天线</span>
              <span data-id="vehicleXLJ" class="vehicle-back-xlj">行李架</span>
              <span data-id="vehicleHS" class="vehicle-back-hs">红绳</span> -->
              <img src="@/assets/img/wisdom-cloud-search/vehicle-front.png" alt="">
            </div>
          </div>
        </section>
        <!-- <section class="operate-bottom btn-group">
          <div class="advanced-search-text primary" @click="advancedSearchHandle">
            收起条件 <i class="iconfont icon-jiantou"></i>
          </div>
          <Button type="primary" @click="searchHandle">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </section> -->
      </div>
      <!-- 选择品牌 -->
      <BrandModal ref="brandModal" @on-change="selectBrand"/>
      <!-- 选择设备 -->
      <select-device ref="selectDevice" :checkedLabels="checkedLabels"  @selectData="selectData"/>
    </Form>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex' 
import BrandModal from '@/components/ui-brand-modal.vue'
import selectTag from '../../components/select-tag.vue'
import selectTagMore from '../../components/select-tag-more.vue'
import { licensePlateColorArray, vehicleBodyColorArray } from '@/libs/system'
import {
  captureTimePeriod,
  angleList,
  shelterList,
  vehiclePlateTypes,
  vehiclePlateColors,
  vehicleBodyTypes,
  vehicleBodyColors,
  colors,
  bodyColors,
  anchorFrontList,
  anchorAfterList,
  specialVehicles,
  coDriverList,
  vehicleClassTypeList
} from './search-vehicle.js'
export default {
    components: {
        BrandModal,
        selectTag,
        selectTagMore
    },
    data () {
        return {
            visible: false,
            curSearchItemId: '',
            queryParam: {
                timeSlot: '近一天',
                selectDeviceList:[],       // 设备资源
                plateBrands:[],   // 车辆品牌
            },
            captureTimePeriod,
            angleList,
            shelterList,
            vehiclePlateTypes,
            vehiclePlateColors,
            vehicleBodyTypes,
            vehicleBodyColors,
            colors,
            bodyColors,
            specialVehicles,
            coDriverList,
            licensePlateColorArray,  // 车牌颜色
            vehicleBodyColorArray,   // 车身颜色
            anchorFrontList,
            anchorAfterList,
            checkedLabels: [],        // 已选择的标签
            vehicleClassTypeList: vehicleClassTypeList, // 车身类型
        }
    },
    async created () {
        await this.getDictData()
        // console.log(licensePlateColorArray, vehicleBodyColorArray)
        window.addEventListener("click", (e)=> {
            this.visible = false;
        })
    },
    computed: {
        ...mapGetters({
            pointViewList: 'dictionary/getPointViewList',               // 角度(枚举精准检索)
            plateClassList: 'dictionary/getPlateClassList',             // 车牌类型(枚举精准检索)
            plateOcclusionList: 'dictionary/getPlateOcclusionList',     // 遮挡(枚举精准检索) 
            licensePlateColorList: 'dictionary/getLicensePlateColorList',         // 车牌颜色
            plateColorIpbdList: 'dictionary/getVehicleColorList',       // 车身颜色
            roofItemsList: 'dictionary/getRoofItemsList',               // 车顶物件
            markerTypeList: 'dictionary/getMarkerTypeList',             // 标志物
            //   vehicleClassTypeList: 'dictionary/getVehicleClassTypeList', // 车辆类型、车身类型(枚举精准检索) 
            sunVisorStatusList: 'dictionary/getSunVisorStatusList',     // 遮阳板
            specialVehicleList: 'dictionary/getSpecialVehicleList',     // 特殊车辆
            driverFlagList: 'dictionary/getDriverFlagList',             // 副驾有人
            getCopilotList: 'dictionary/getCopilotList',                  //副驾驶
            facialOcclusionList: 'dictionary/getFacialOcclusionList',             // 面部遮挡
            annualInspectionNumList: 'dictionary/getAnnualInspectionNumList',     // 年检标
        })
    },
    methods: {
        ...mapActions({
            getDictData: 'dictionary/getDictAllData'
        }),
        advancedSearchHandle ($event) {
            $event.stopPropagation()
            if (this.visible) {
                this.visible = false
            } else {
                this.visible = true
            }
        },
        checkedHandle (arr) {
            console.log(arr)
        },
        // 选择品牌
        selectBrandHandle () {
            this.$refs.brandModal.show()
        },
        /**
         * 选择设备
         */
        selectDevice () {
            this.$refs.selectDevice.show(this.queryParam.selectDeviceList)
        },
        /**
         * 已选择设备数据返回
         */
        selectData(list) {
            this.queryParam.selectDeviceList = list
        },
        // 搜索函数
        searchHandle(){
            this.$parent.pageInfo.pageNumber = 1;
            this.$emit('search',this.queryParam)
        },
    
        // 点击右边车指示高亮左侧搜索项  因这里数据无明显规律，so这里为原生js操作dom
        clickTagVehicleHandle (e) {
            const target = e.target
            if (target.nodeName === 'SPAN') {
                let PChildren = target.parentNode.parentNode.childNodes
                PChildren.forEach(pChild => {
                    pChild.childNodes.forEach(child => {
                        child.classList.remove('cur')
                    })
                })
                target.classList.add('cur')
                const dataId = target.getAttribute('data-id')
                const leftItem = document.getElementById(dataId)
                if (!leftItem) return false
                const leftPChildren = leftItem.parentNode.parentNode.parentNode.childNodes
                leftPChildren.forEach(p => {
                    p.childNodes.forEach(pp => {
                        if (pp.nodeName === 'DIV') {
                            pp.childNodes.forEach(ppp => {
                                ppp.classList.remove('cur')
                            })
                        }
                    })
                })
                leftItem.classList.add('cur')
                document.getElementById('searchItemContainer').scrollTop=leftItem.offsetTop
                this.curSearchItemId = dataId
            }
        },
        dateChange(start, end) {
            if(start[1].slice(-8) === '00:00:00') {
                start[1] = start[1].slice(0,-8) + '23:59:59'
            }
            this.queryParam.timeSlotArr = [start[0], start[1]];
        },
        /**
         * 选中tag赋值
         */
        selectItem (key, item) {
            // 具体业务处理逻辑
            if (item) {
                this.queryParam[key] = item.dataKey
            }else { 
                // 全部选项，不返回数据到后端
                this.queryParam[key] = null
            }
        },
        /**
         * 多选tag赋值
         */
        selectItemMore (key, item) {
            // 具体业务处理逻辑
            if (item) {
                this.queryParam[key] = item
            }else { 
                // 全部选项，不返回数据到后端
                this.queryParam[key] = null
            }
        },
        /**
         * 车辆品牌已选择，返回数据
         */
        selectBrand (list) {
            this.queryParam.plateBrands = list
        },
        /**
         * 选择接口返回数据
         */
        input(e, key) {
            this.queryParam[key] = e
        },
        /**
         * 重置，清空数据
         */
        resetHandle(){
            this.queryParam = {
                plateNo: '',
                timeSlot: '近一天',
                selectDeviceList:[],       // 设备资源
                plateBrands:[],   // 车辆品牌
                markerList: [],
                plateClass: '',
                plateColor: '',
                bodyType: '',
                bodyColor: '',
                specialPlate: '',
                carRoof: '',
                inspectAnnually: '',
                marker: '',
                ifCoDriverPeople: '',
                facialOcclusion: '',
                // sunVisorStatus: '',
            }

            // 清空组件选中状态
            this.$refs.tagSelect1.clearChecked(false)
            this.$refs.pointOfView.clearChecked()
            this.$refs.plateClass.clearChecked()
            this.$refs.plateColor.clearChecked()
            this.$refs.cover.clearChecked()
            this.$refs.bodyType.clearChecked()
            this.$refs.marker.clearChecked()
            this.$refs.bodyColor.clearChecked()
            this.$refs.specialPlate.clearChecked()
            this.$refs.carRoof.clearChecked()
            this.$refs.inspectAnnually.clearChecked()
            this.$refs.ifCoDriverPeople.clearChecked()
            this.$refs.facialOcclusion.clearChecked()
            // this.$refs.sunVisorStatus.clearChecked()
            this.$refs.brandModal.clearCheckAll()
            this.visible = false
            this.$emit("reset")
        },
  }
}
</script>
<style lang="less" scoped>
@import "./search-vehicle.less";
</style>
