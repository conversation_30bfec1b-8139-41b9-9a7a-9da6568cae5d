<template>
  <div class="number-divisions">
    <div class="search-container">
      <index-config @on-change="onChangeConfig" :filter-index="filterIndex"></index-config>
    </div>
    <div class="search-container mb-sm">
      <span class="f-16 base-text-color">区划数量</span>
      <div class="area-filter ml-sm">
        <ui-label required class="inline" label="行政区划" :width="70">
          <Select class="width-md" v-model="form.regionType" placeholder="请选择行政区划">
            <Option value="province">省</Option>
            <Option value="city">市</Option>
            <Option value="region">区县</Option>
            <Option value="policeStation">派出所</Option>
          </Select>
        </ui-label>
        <ui-label required class="inline ml-lg" label="数据类型" :width="70">
          <Select class="width-md" v-model="form.numberType" placeholder="请选择数据类型">
            <Option v-for="(opItem, opIndex) in options" :key="'opIndex' + opIndex" :value="opItem.key">{{
              opItem.optionLabel
            }}</Option>
          </Select>
        </ui-label>
        <ui-label required class="inline ml-lg" label="达标数量" :width="70">
          <InputNumber
            v-model="form.number"
            :min="0"
            :max="Number.MAX_SAFE_INTEGER"
            class="width-md"
            placeholder="请输入达标数量"
          >
          </InputNumber>
        </ui-label>
        <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
      </div>
    </div>
    <div class="config-container" v-ui-loading="{ loading }">
      <common-capture-area-select class="clear-b" ref="captureAreaSelect" :data="areaTreeData">
        <template #configName>
          <div class="width-md">视频监控建设数量</div>
          <div class="width-md">人脸卡口建设数量</div>
          <div class="width-md">车辆卡口建设数量</div>
        </template>
        <template #label="{ data }">
          <div class="width-md">
            <InputNumber
              v-show="data.check"
              class="width-xs"
              :min="0"
              :max="Number.MAX_SAFE_INTEGER"
              v-model.number="data.videoNum"
              placeholder="请输入视频监控建设数量"
            ></InputNumber>
          </div>
          <div class="width-md">
            <InputNumber
              v-show="data.check"
              class="width-xs"
              :min="0"
              :max="Number.MAX_SAFE_INTEGER"
              v-model.number="data.faceNum"
              placeholder="请输入人脸卡口建设数量"
            ></InputNumber>
          </div>
          <div class="width-md">
            <InputNumber
              v-show="data.check"
              class="width-xs"
              :min="0"
              :max="Number.MAX_SAFE_INTEGER"
              v-model.number="data.vehicleNum"
              placeholder="请输入车辆卡口建设数量"
            ></InputNumber>
          </div>
        </template>
      </common-capture-area-select>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  props: {},
  data() {
    return {
      areaTreeData: [],
      indexConfigAreaData: [],
      loading: false,
      form: {
        regionType: 'province',
        number: null,
      },
      options: [
        { title: '全部', key: 'all', optionLabel: '全部' },
        { title: '视频监控数量', key: 'videoNum', optionLabel: '视频监控' },
        { title: '人脸卡口数量', key: 'faceNum', optionLabel: '人脸卡口' },
        { title: '车辆卡口数量', key: 'vehicleNum', optionLabel: '车辆卡口' },
      ],
      filterIndex: [
        'BASIC_QUANTITY_STANDARD',
        'BASIC_EMPHASIS_QUANTITY',
        'VIDEO_QUANTITY_STANDARD',
        'VEHICLE_QUANTITY_STANDARD',
        'FACE_QUANTITY_STANDARD',
      ],
    };
  },
  async created() {
    await this.getStanderConfig();
    this.setTreeData();
  },
  methods: {
    clickBatchInput() {
      if (!this.form.number) {
        return this.$Message.error('请输入达标数量');
      }
      this.batchInput(this.areaTreeData);
    },
    /*
    1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所
    * */
    batchInput(data) {
      data.map((item) => {
        if (
          (item.regionType === '1' && this.form.regionType === 'province') ||
          (['2', '3', '4', '5'].includes(item.regionType) && this.form.regionType === 'city') ||
          (['6', '7', '8'].includes(item.regionType) && this.form.regionType === 'region') ||
          (item.regionType === '9' && this.form.regionType === 'policeStation')
        ) {
          if (this.form.numberType === 'all') {
            this.$set(item, 'videoNum', this.form.number);
            this.$set(item, 'faceNum', this.form.number);
            this.$set(item, 'vehicleNum', this.form.number);
          } else {
            this.$set(item, this.form.numberType, this.form.number);
          }
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },
    onChangeConfig(val) {
      // 载入数量达标率 中的配置
      let {
        indexConfig: { quantityData = [] },
      } = val;
      if (!quantityData.length) {
        return this.$Message.error('所选指标未配置');
      }
      this.setTreeDataFormIndexConfig(quantityData);
    },
    validate(val) {
      if (!val.length) {
        this.$Message.error(`区划数量不能为空`);
        throw new Error(`区划数量不能为空`);
      }
      val.forEach((item) => {
        if (!item.videoNum || !item.faceNum || !item.vehicleNum) {
          let info = `${item.regionName}区划数量不能为空`;
          this.$Message.error(info);
          throw new Error(info);
        }
      });
    },
    save() {
      let checkedTree = this.$refs.captureAreaSelect.commit();
      this.validate(checkedTree);
      this.formData = checkedTree.map((item) => {
        return {
          code: item.regionCode,
          name: item.regionName,
          type: 1,
          videoNum: item.videoNum,
          faceNum: item.faceNum,
          vehicleNum: item.vehicleNum,
        };
      });
      const configInfo = {
        type: 1,
        data: this.formData,
      };
      this.$emit('save', configInfo);
    },
    async getStanderConfig() {
      try {
        this.loading = true;
        //type =1 行政区划  2 采集区域
        const {
          data: { data },
        } = await this.$http.get(`${equipmentassets.getStanderConfig}/?type=1`);
        this.indexConfigAreaData = data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    setTreeData() {
      let treeData = this.$util.common.deepCopy(
        this.$util.common.getTreeChildren(this.treeData, this.getHomeConfig.regionCode),
      );
      treeData.forEach((item) => {
        let index = this.indexConfigAreaData.findIndex((value) => value.code === item.regionCode);
        item.videoNum = index !== -1 ? this.indexConfigAreaData[index]['videoNum'] : null;
        item.faceNum = index !== -1 ? this.indexConfigAreaData[index]['faceNum'] : null;
        item.vehicleNum = index !== -1 ? this.indexConfigAreaData[index]['vehicleNum'] : null;
        item.check = index !== -1;
      });
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
    },
    setTreeDataFormIndexConfig(quantityData) {
      try {
        this.loading = true;
        let treeData = this.$util.common.deepCopy(
          this.$util.common.getTreeChildren(this.treeData, this.getHomeConfig.regionCode),
        );
        treeData.forEach((item) => {
          let index = quantityData.findIndex((value) => value.key === item.regionCode);
          item.videoNum = index !== -1 ? Number.parseFloat(quantityData[index]['sxjValue'] || 0) : null;
          item.faceNum = index !== -1 ? Number.parseFloat(quantityData[index]['rlkkValue'] || 0) : null;
          item.vehicleNum = index !== -1 ? Number.parseFloat(quantityData[index]['clkkValue'] || 0) : null;
          item.check = index !== -1;
        });
        this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
      getHomeConfig: 'home/getHomeConfig',
    }),
  },
  components: {
    CommonCaptureAreaSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-capture-area-select.vue')
        .default,
    IndexConfig: require('@/views/viewassets/commonreport/pointanalysis/components/index-config.vue').default,
  },
};
</script>
<style lang="less" scoped>
.number-divisions {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

@{_deep} .mr-100 {
  margin-right: 100px;
}
@{_deep} .mr-50 {
  margin-right: 50px;
}
</style>
