/**
 * 全局权限处理指令 用来控制操作按钮显示隐藏
 * 获取用户信息时返回一个权限列表permissions
 * 根据指令传入参数可以进行判断菜单列表中是否有该接口权限
 * 指令传入参数分别为
 * @param {string} route
 * 接口所在菜单路由 必传 route必传是因为不同路由可能调用相同接口 但是在不同页面可能有不同权限
 * @param {string} permission：权限标识 必传
 * @param {Boolean} mutually:
 * 是否互斥 可传 此参数用来控制如有有权限不显示互斥 如果没权限则显示
 * 例：如果有编辑权限则显示编辑按钮如果没有编辑权限则显示查看按钮 因为查看权限是不需要赋予的
 * @param {Boolean} isShow:
 * 为了解决有权限 && mutually是true 的情况不展示（为了能外部控制是否展示）
 * 例：查看按钮： 有权限，mutually：true是不展示的情况
 * 但是权限需要再进一步控制，需要手动让这个查看展示
 */
import store from '@/store';
export default function (Vue) {
  Vue.directive('permission', {
    inserted(el, bind) {
      if (!Vue.prototype.$_has(bind.value)) {
        el.parentNode.removeChild(el);
      }
    },
  });
  Vue.prototype.$_has = (value) => {
    let isExist = false;
    let buttonpermsStr = store.state.permission.permissions;
    if (buttonpermsStr === undefined || buttonpermsStr === null) {
      return false;
    }
    let matchedStr = 'route' in value ? `${value.route}-${value.permission}` : value.permission;
    let index = buttonpermsStr.findIndex((row) => row === matchedStr);
    // 1. 有权限 && mutually是false, 展示
    if (index !== -1 && !value.mutually) {
      isExist = true;
      // 2. 无权限 && mutually是true, 展示
    } else if (value.mutually && index === -1) {
      isExist = true;
    }
    // 3. 有权限 && isShow是true ,展示
    if (index !== -1 && !!value.isShow) {
      isExist = true;
    }
    return isExist;
  };
}
