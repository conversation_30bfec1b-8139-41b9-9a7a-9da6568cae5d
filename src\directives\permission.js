/**
 * 全局权限处理指令 用来控制操作按钮显示隐藏
 * 登陆时后端会返回一个菜单列表 每个菜单列表对应一个接口权限数组
 * 根据指令传入参数可以进行判断菜单列表中是否有该接口权限
 * 指令传入参数分别为
 * route：接口所在菜单路由 必传 route必传是因为不同路由可能调用相同接口 但是在不同页面可能有不同权限
 * api：接口具体地址 必传
 * mutually: 是否互斥 可传 此参数用来控制如有有权限不显示互斥 如果没权限则显示
 * 例：如果有编辑权限则显示编辑按钮如果没有编辑权限则显示查看按钮 因为查看权限是不需要赋予的
 */
import store from '@/store'

export default function (Vue) {
  Vue.directive('permission', {
    inserted (el, bind, vnode) {
      if (!Vue.prototype.$_has(bind.value)) {
        el.parentNode.removeChild(el)
      }
    }
  })
  Vue.prototype.$_has = (value) => {
    const permissionRoles = value
    if (value == 'yes') {
      return true
    }else if (value && value instanceof Array && value.length > 0) {
      let hasPermission = false
      const buttonPerms = store.getters.btnPermission
      hasPermission = buttonPerms.some(role => {
        return permissionRoles.includes(role)
      })
      return hasPermission
    } else {
      throw new Error(`need roles! Like v-permission="['sys:delete','sys:add']"`)
    }
  }
}
