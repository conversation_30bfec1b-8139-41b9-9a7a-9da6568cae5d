<template>
  <div class="vehicle height-full auto-fill">
    <div class="search-module" :style="searchWidth">
      <div class="mb-lg">
        <ui-label class="inline" label="抓拍时间" :width="70">
          <DatePicker
            class="width-md"
            type="datetime"
            v-model="searchData.startTime"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
            placeholder="请选择开始时间"
          ></DatePicker>
          <span> — </span>
          <DatePicker
            class="width-md"
            type="datetime"
            v-model="searchData.endTime"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
            placeholder="请选择结束时间"
          ></DatePicker>
        </ui-label>
        <ui-label class="inline ml-lg vt-middle" label="请选择摄像机" :width="100">
          <select-camera
            :cameraType="[1]"
            @pushCamera="pushCamera"
            :device-ids="searchData.deviceIds"
            :camera-type="[1]"
          ></select-camera>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="reset()">重置</Button>
        </div>
      </div>
      <ui-select-tabs
        ref="selectTabs"
        :list="dictTypeList"
        :need-expand="false"
        @selectInfo="selectInfo"
      ></ui-select-tabs>
    </div>

    <div class="statistic" ref="statistic">
      <p class="statistic-title">
        {{ statistic.title }}
      </p>
      <p class="statistic-total">
        {{ statistic.totalNum | formatNum }}
      </p>
      <p class="statistic-title">今日新增</p>
      <p class="statistic-today">
        {{ statistic.todayNum | formatNum }}
      </p>
    </div>
    <div class="table-module auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
      <div v-if="tableData.length > 0" class="group-unaggregated">
        <div class="group-item" v-for="(item, index) in tableData" :key="index">
          <div class="group-left">
            <div class="group-img" @click="lookScence(index)">
              <ui-image :src="item.smallImagePath" />
              <!-- <img :src="item.smallImagePath" alt="" /> -->
              <p class="shadow-box" style="z-index: 11" title="查看检测结果">
                <i
                  class="icon-font icon-yichang search-icon mr-xs base-text-color"
                  @click.stop="abnormalDetail(item)"
                ></i>
              </p>
            </div>
            <div class="group-message">
              <p class="mb-sm" :title="`最后抓拍时间：${item.logTime}`">
                <i class="icon-font icon-shijian"></i>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{ item.bigImageShotTime }}</span>
              </p>
              <p :title="`最后抓拍地址：${item.reason}`">
                <i class="icon-font icon-dizhi"></i>
                <span class="group-text inline vt-middle ml-xs ellipsis">
                  {{ item.deviceName || '未知' }}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div class="empty-item" v-for="(item, index) in 8" :key="index"></div>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
    <yichangView v-if="yichangShow" ref="yichang" />
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import api from '@/config/api/user';
import api2 from '@/config/api/car-threm';
export default {
  props: {},
  data() {
    return {
      loading: false,
      errorList: [],
      dictTypeList: [],
      searchWidth: 0,
      statistic: {
        title: '车辆视图数据',
        totalNum: 0,
        todayNum: 0,
      },
      searchData: {
        orgCode: null,
        errorMessages: [],
        reasonTypes: [],
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      pageList: [
        { label: '14 条/页', value: 14 },
        { label: '28 条/页', value: 28 },
        { label: '56 条/页', value: 56 },
        { label: '112 条/页', value: 112 },
      ],
      tableData: [],
      yichangShow: false,
      viewIndex: 0,
      visibleScence: false,
      imgList: [],
      zhiliShow: false,
    };
  },
  created() {
    // this.initErrorList();
    // this.initStatistic();
    this.getDictType();
    this.search();
    this.queryIndexInfo();
  },
  mounted() {},
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    selectInfo(list) {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.reasonTypes = [];
      list.forEach((item) => {
        this.searchData.reasonTypes.push(item.dataKey);
      });
      // this.searchData.errorMessages = infoList.map(row=>{
      //     return row.name;
      // });
      this.init();
    },
    initSearchWidth() {
      this.$nextTick(() => {
        this.searchWidth = `width: calc(100% - ${this.$refs.statistic.clientWidth}px)`;
      });
    },
    async initStatistic() {
      try {
        let res = await this.$http.post(equipmentassets.queryFaceLibAbnormalCount, {
          orgCode: this.searchData.orgCode,
        });
        this.statistic.totalNum = res.data.data.allCount;
        this.statistic.todayNum = res.data.data.curCount;
        this.initSearchWidth();
      } catch (err) {
        console.log(err);
      }
    },
    async initErrorList() {
      try {
        let res = await this.$http.get(equipmentassets.queryFaceLibAbnormalReasonEnum);
        this.errorList = res.data.data.map((row) => {
          return {
            name: row.reason,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    getDictType() {
      this.dictTypeList = [];
      this.$http.get(api.queryByTypeKey + '?typekey=vehicle_reason_type').then((res) => {
        console.log(res);
        var list = res.data.data;
        list.forEach((item) => {
          item.name = item.dataValue;
          this.dictTypeList.push(item);
        });
      });
    },
    async init() {
      this.loading = true;
      this.tableData = [];
      try {
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.queryVehicleResultPageList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
        this.imgList = this.tableData.map((row) => row.bigImagePath);
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },

    queryIndexInfo() {
      this.$http.post(api2.queryTotalAndNewlyAdd).then((res) => {
        console.log(res);
        this.statistic.totalNum = res.data.data.total;
        this.statistic.todayNum = res.data.data.newlyAdded;
      });
    },

    reset() {
      this.searchData = {
        orgCode: null,
        errorMessages: [],
        reasonTypes: [],
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      };

      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;

      this.$refs.selectTabs.reset();
      this.init();
    },
    search() {
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = 20;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.init();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.init();
    },
    abnormalDetail(item) {
      this.yichangShow = true;
      this.$nextTick(() => {
        this.$refs.yichang.showModal(this.dictTypeList, item);
      });
    },
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {},
  computed: {},
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    SelectCamera: require('@/components/select-camera.vue').default,
    uiImage: require('@/components/ui-image').default,
    yichangView: require('@/views/datagovernance/tasktracking/car/components/carmodal/data-yichang-dialog').default,
  },
};
</script>
<style lang="less" scoped>
.search-module {
  float: left;
  padding: 20px 20px 10px 20px;
  .keyword-input {
    width: 300px;
  }
}
.statistic {
  float: right;
  padding-right: 20px;
  padding-top: 10px;
  position: absolute;
  top: 0;
  right: 0;
  .statistic-title {
    color: #fff;
  }
  .statistic-total {
    color: var(--color-bluish-green-text);
    font-size: 20px;
    margin-bottom: 15px;
  }
  .statistic-today {
    color: #f18a37;
    font-size: 20px;
  }
}
.table-module {
  clear: both;
  margin-left: 20px;
  position: relative;
  border-top: 1px solid var(--border-color);
  overflow-x: hidden;
  overflow-y: auto;
  .group-unaggregated {
    overflow: hidden;
    position: absolute;
    padding-top: 10px;
    padding-right: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .group-item {
      width: 177px;
      height: 236px;
      margin-bottom: 14px;
      padding: 10px 0;
      overflow: hidden;
      position: relative;
      background: #0f2f59;
      border-radius: 4px;
      border: 2px solid transparent;
      &:hover {
        border: 2px solid var(--color-primary);
      }
    }
    .empty-item {
      width: 177px;
    }
    .group-left {
      .group-img {
        position: relative;
        width: 150px;
        height: 150px;
        cursor: pointer;
        margin: 0 auto 10px;
        img {
          width: 100%;
          // height: 100%;
        }
        .shadow-box {
          height: 28px;
          width: 100%;
          background: rgba(0, 0, 0, 0.3);
          position: absolute;
          bottom: 0;
          display: none;
          padding-left: 10px;
          > i:hover {
            color: var(--color-primary);
          }
        }
        &:hover {
          .shadow-box {
            display: block;
          }
        }
      }
      .group-message {
        margin: 0 auto;
        padding: 0 15px;
        color: #afbcd4;
        .group-text {
          width: 124px;
        }
        i {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
