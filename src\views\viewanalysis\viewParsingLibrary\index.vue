<!--
    * @FileDescription: 视图解析库
    * @Author: H
    * @Date: 2024/2/26
    * @LastEditors: view-parsing-library viewParsingLibrary
    * @LastEditTime: 
 -->
<template>
  <section class="my-container">
    <div class="result-content">
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
      >
        <MenuItem
          v-for="(item, index) in menuList"
          :key="index"
          :name="item.name"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
        </MenuItem>
      </Menu>
      <keep-alive>
        <component :is="sectionName"></component>
      </keep-alive>
    </div>
  </section>
</template>
<script>
import wholeContent from "./pages/whole-content";
import faceContent from "./pages/face-content";
import humanBodyContent from "./pages/human-body-content.vue";
import nonmotorVehicleContent from "./pages/nonmotor-vehicle-content.vue";
import vehicleContent from "./pages/vehicle-content";
import { mapActions, mapGetters, mapMutations } from "vuex";
export default {
  data() {
    return {
      visible: false,
      sectionName: "",
      menuList: [
        {
          label: "全部",
          value: 1,
          iconName: "icon-sifenping",
          name: "wholeContent",
        },
        {
          label: "人像",
          value: 2,
          iconName: "icon-renlian",
          name: "faceContent",
          permission: ["search-face"],
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicleContent",
          permission: ["search-vehicle"],
        },
        {
          label: "人体",
          value: 8,
          iconName: "icon-renti",
          name: "humanBodyContent",
          permission: ["search-humanBody"],
        },
        {
          label: "非机动车",
          value: 9,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicleContent",
          permission: ["search-nonmotorVehicle"],
        },
      ],
    };
  },
  components: {
    wholeContent,
    faceContent,
    humanBodyContent,
    nonmotorVehicleContent,
    vehicleContent,
  },
  watch: {
    $route: {
      handler(to, from) {
        let { sectionName } = to.query;
        // let permissionList = this.menuList.filter(d => this.$_has(d.permission))
        // let hasSectionName = permissionList.some(d => d.name === sectionName)
        // this.sectionName = hasSectionName ? sectionName : permissionList.length > 0 ? permissionList[0].name : ''
        let hasSectionName = this.menuList.some((d) => d.name === sectionName);
        this.sectionName = hasSectionName
          ? sectionName
          : this.menuList.length > 0
          ? this.menuList[0].name
          : "";
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      picData: "common/getWisdomCloudSearchData",
    }),
  },
  created() {
    this.setLayoutNoPadding(true);
    this.getDictData();
  },
  destroyed() {
    if (this.$route.name != "viewParsingLibrary") {
      this.setLayoutNoPadding(false);
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    selectItemHandle(sectionName) {
      const path = this.$route.path;
      const noMenu = this.$route.query.noMenu;
      this.$router.push({ path, query: { noMenu, sectionName } });
    },
  },
};
</script>
<style lang="less" scoped>
.my-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  .result-content {
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    //   overflow: hidden;
    flex: 1;
    height: calc(~"100% - 34px");
    display: flex;
  }
}
</style>
