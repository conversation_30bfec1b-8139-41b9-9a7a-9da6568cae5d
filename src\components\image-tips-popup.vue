<template>
  <ui-modal :title="popUpTitle" v-model="visible" :styles="styles" :footer-hide="true">
    <ul class="image-tips-ul">
      <li v-for="(item, index) of nomalLabelList" :key="index">
        <span class="label mr-xs">{{ item.name }}: </span>
        <span
          class="content"
          :class="{
            'font-table-action': index !== nomalLabelList.length - 1,
            'font-red': index === nomalLabelList.length - 1,
          }"
        >
          {{ item.value }}
        </span>
      </li>
      <li>
        <!-- <loading v-if=''><loading> -->
      </li>
    </ul>
  </ui-modal>
</template>
<style lang="less" scoped>
.image-tips-ul {
  display: flex;
  flex-direction: column;
  color: #fff;
  margin-left: 150px;
  > li {
    height: 40px;
    line-height: 40px;
    .label {
      display: inline-block;
      min-width: 60px;
      text-align: left;
    }
  }
}
</style>
<script>
export default {
  props: {
    popUpTitle: {
      default: '抓拍时间异常',
    },
    nomalLabelList: {
      type: Array,
    },
    value: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        //top: "1.2rem",
        width: '30%',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {},
};
</script>
