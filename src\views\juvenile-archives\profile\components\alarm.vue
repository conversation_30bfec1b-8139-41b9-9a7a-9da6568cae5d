<template>
  <ui-card :title="title" class="alarm">
    <!-- <div slot="extra" class="more-btn" @click="portraitCaptureMoreHandle">更多<i class="iconfont icon-more"></i></div> -->
    <div class="portrait-capture-content">
      <template v-for="(item, index) in tableList">
        <div
          @click="handleDetailFn(item, index)"
          :key="index"
          class="content-item"
        >
          <personnel-alarm
            @collection="collection"
            v-if="type == 'people'"
            :data="item"
          ></personnel-alarm>
          <vehicle-alarm :data="item" v-else></vehicle-alarm>
        </div>
      </template>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!tableList || !tableList.length) && !loading" />
    <!-- 人员布控 -->
    <peopleAlarmDetail
      v-if="detailShow"
      :tableList="tableList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="alarmDetail"
      @close="detailShow = false"
    />
    <!-- 车辆布控 -->
    <vehicleAlarmDetail
      v-if="vehicleAlarmShow"
      :tableList="tableList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="vehicleDetail"
      @close="vehicleAlarmShow = false"
    />
  </ui-card>
</template>

<script>
import personnelAlarm from "@/views/perception-home/perception-site/components/collect/personnelAlarm.vue";
import vehicleAlarm from "@/views/perception-home/perception-site/components/collect/vehicleAlarm.vue";
import peopleAlarmDetail from "@/views/target-control/alarm-manager/people/components/alarm-detail.vue";
import vehicleAlarmDetail from "@/views/target-control/alarm-manager/vehicle/components/vehicle-detail.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { alarmPageList, vehiclePageList } from "@/api/target-control";
import { queryParamDataByKeys } from "@/api/config";
export default {
  components: {
    personnelAlarm,
    vehicleAlarm,
    peopleAlarmDetail,
    vehicleAlarmDetail,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    archiveNo: {
      type: String | Number,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      alarmConfigInfo: {},
      tableList: [],
      detailShow: false,
      vehicleAlarmShow: false,
      alarmInfo: {},
      tableIndex: 0,
    };
  },
  async created() {
    await this.init();
    await this.handleTabList();
  },
  methods: {
    // 查看更多
    portraitCaptureMoreHandle() {
      if (this.type === "people") {
        const routeData = this.$router.resolve({
          name: "alarm-manager",
          query: {
            page: "people",
          },
        });
        window.open(routeData.href, "_blank");
      } else if (this.type === "vehicle") {
        const routeData = this.$router.resolve({
          name: "alarm-manager",
          query: {
            page: "vehicle",
          },
        });
        window.open(routeData.href, "_blank");
      }
    },
    init() {
      var param = ["ICBD_TARGET_CONTROL"];
      queryParamDataByKeys(param).then((res) => {
        if (res.data.length > 0) {
          this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
        }
      });
    },
    // 详情
    handleDetailFn(item, index) {
      this.tableIndex = index;
      if (this.type == "people") {
        this.detailShow = true;
      } else {
        this.vehicleAlarmShow = true;
      }
      this.alarmInfo = item;
    },
    handleTabList() {
      if (this.type == "people") {
        this.personnelList();
      } else {
        this.vehicleList();
      }
    },
    // 人员报警
    personnelList() {
      this.tableList = [];
      let params = {
        pageNumber: 1,
        pageSize: 4,
        simScore: 0.8,
        idCardNo:
          this.$route.query.source === "people" ||
          this.$route.query.source === "zdr"
            ? this.archiveNo
            : null,
        vid: this.$route.query.source === "video" ? this.archiveNo : null,
        operationType: null,
      };
      alarmPageList(params).then((res) => {
        this.tableList = res.data.entities;
        this.tableList.forEach((item) => {
          var info = this.alarmConfigInfo.alarmLevelConfig.find(
            (ite) => ite.alarmLevel == item.taskLevel
          );
          item.bgIndex = Number(info.alarmColour);
        });
      });
    },
    vehicleList() {
      this.tableList = [];
      let params = {
        pageNumber: 1,
        pageSize: 4,
        plateNo: JSON.parse(this.$route.query.plateNo),
        simScore: 0.8,
        operationType: null,
      };
      vehiclePageList(params).then((res) => {
        this.tableList = res.data.entities;
        this.tableList.forEach((item) => {
          var info = this.alarmConfigInfo.alarmLevelConfig.find(
            (ite) => ite.alarmLevel == item.taskLevel
          );
          item.bgIndex = Number(info.alarmColour);
        });
        this.$forceUpdate();
      });
    },
    collection(params, flag) {
      if (flag == 1) {
        addCollection(params).then((res) => {
          this.$Message.success("收藏成功");
          if (this.type == "people") {
            this.personnelList();
          } else {
            this.vehicleList();
          }
        });
      } else {
        deleteMyFavorite(params).then((res) => {
          this.$Message.success("取消收藏成功");
          if (this.type == "people") {
            this.personnelList();
          } else {
            this.vehicleList();
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.alarm {
  .portrait-capture-content {
    display: flex;
    align-items: center;
  }
  .content-item {
    width: 340px;
    margin-left: 10px;
    &:nth-child(1) {
      margin-left: 0;
    }
  }
  /deep/ .card-content {
    padding: 20px 15px !important;
    min-height: 240px;
    box-sizing: border-box;
  }
  /deep/ .card-head {
    overflow: hidden;
    border-top-left-radius: 4px;
    padding-right: 20px;
  }
}
</style>