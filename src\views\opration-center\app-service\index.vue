<template>
  <div class="container">
    <div class="top-box">
      <div
        class="top-card"
        :class="item.bg"
        v-for="(item, index) in topCardList"
        :key="index"
      >
        <div>{{ item.cardName }}</div>
        <div class="num">{{ item.total }}</div>
        <img
          :src="require('@/assets/img/opration-center/' + item.imageUrl)"
          alt=""
        />
        <img
          class="bg_img"
          src="@/assets/img/opration-center/bgShort.png"
          alt=""
        />
      </div>
    </div>
    <div class="search-bar">
      <searchApp ref="searchApp" @searchInfo="searchInfo"></searchApp>
    </div>
    <div class="card-container">
      <div class="card-content">
        <div class="card-item" v-for="(item, index) in tableList" :key="index">
          <div style="display: flex">
            <img
              v-if="item.instanceStatus == 'UP'"
              src="@/assets/img/opration-center/app-success.png"
              alt=""
            />
            <img
              v-else-if="item.instanceStatus == 'DOWN'"
              src="@/assets/img/opration-center/app-warning.png"
              alt=""
            />
            <img
              v-else-if="item.instanceStatus == 'UNREGISTERED'"
              src="@/assets/img/opration-center/app-unregister.png"
              alt=""
            />
            <img
              v-else
              src="@/assets/img/opration-center/app-outline.png"
              alt=""
            />
            <div class="card-item-info">
              <div class="cnName">{{ item.applicationNameDesc }}</div>
              <div class="enName">{{ item.applicationName }}</div>
              <div class="info-label">
                节点名称：<span>{{ item.serverName }}</span>
              </div>
              <div class="info-label">
                节&nbsp; 点&nbsp;IP：<span>{{ item.instanceIP }}</span>
              </div>
              <div class="info-label">
                端&nbsp; 口&nbsp;号：<span>{{ item.instancePort }}</span>
              </div>
            </div>
          </div>
          <div class="card-bottom">
            <div class="runtime">
              运行时长：<span>{{ item.lastDurationTime }}</span
              >分钟
            </div>
            <div class="runtime">
              运行时间：<span>{{ item.instanceStatusTime }}</span>
            </div>
          </div>
        </div>
      </div>
      <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty>
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[20, 40, 60]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
  </div>
</template>
<script>
import searchApp from "./components/search-app";
import { queryPageByConditions } from "@/api/opration-center";

export default {
  name: "app-service",
  components: {
    searchApp,
  },
  data() {
    return {
      topCardList: [
        {
          imageUrl: "serviceNum.png",
          total: 0,
          bg: "bg1",
          cardName: "服务数量",
        },
        { imageUrl: "onlineNum.png", total: 0, bg: "bg2", cardName: "在线" },
        { imageUrl: "outlineNum.png", total: 0, bg: "bg3", cardName: "离线" },
        { imageUrl: "errorNum.png", total: 0, bg: "bg4", cardName: "异常" },
        { imageUrl: "unregister.png", total: 0, bg: "bg5", cardName: "未注册" },
      ],
      queryParam: {},
      tableList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
      loading: false,
    };
  },
  mounted() {
    this.queryList();
  },
  methods: {
    queryList() {
      let formData = this.$refs.searchApp.formData;
      this.queryParam = { ...this.queryParam, ...formData };
      this.loading = true;
      this.tableList = [];
      queryPageByConditions({ ...this.queryParam, ...this.pageInfo })
        .then((res) => {
          const {
            total,
            entities,
            totalCount,
            onlineTotalCount,
            offlineTotalCount,
            downTotalCount,
            unRegisteredCount,
          } = res.data;
          this.total = total;
          this.tableList = entities;
          this.topCardList[0].total = totalCount;
          this.topCardList[1].total = onlineTotalCount;
          this.topCardList[2].total = offlineTotalCount;
          this.topCardList[3].total = downTotalCount;
          this.topCardList[4].total = unRegisteredCount;
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchInfo(obj) {
      this.pageInfo.pageNumber = 1;
      this.queryParam = obj;
      this.queryList();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
  },
};
</script>
<style lang="less" scoped>
.top-box {
  height: 100px;
  display: flex;
  margin: 5px 0 20px 0;
  .top-card {
    flex: 1;
    height: 100%;
    margin-right: 20px;
    border-radius: 4px;
    font-size: 16px;
    color: #fff;
    padding: 20px;
    position: relative;
    img {
      position: absolute;
      right: 20px;
      top: 20px;
      width: 60px;
    }
    .num {
      font-size: 32px;
      font-weight: bold;
    }
    .bg_img {
      width: 100%;
      height: 100%;
      top: 0;
      right: 0;
    }
  }
  .top-card:last-child {
    margin-right: 0;
  }
  .bg1 {
    background: linear-gradient(315deg, #39a1fc 0%, #1c6df4 100%);
    box-shadow: 0 5px 10px 0 rgba(54, 151, 251, 0.5);
  }
  .bg2 {
    background: linear-gradient(141deg, #1ca884 0%, #37d9af 100%);
    box-shadow: 0 5px 10px 0 rgba(34, 180, 143, 0.5);
  }
  .bg3 {
    background: linear-gradient(225deg, #c1c7d2 0%, #808da7 100%);
    box-shadow: 0 5px 10px 0 rgba(133, 145, 171, 0.5);
  }
  .bg4 {
    background: linear-gradient(223deg, #ff7d56 0%, #db5234 100%);
    box-shadow: 0 5px 10px 0 rgba(248, 167, 79, 0.5);
  }
  .bg5 {
    background: linear-gradient(253deg, #9bbae2 12%, #7395c1 97%);
    box-shadow: 0px 5px 10px 0px rgba(115, 149, 193, 0.5);
  }
}
.card-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: auto;
  position: relative;
  height: calc(~"100% - 190px");
  .card-content {
    overflow: auto;
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    align-content: flex-start;
    .card-item {
      width: calc(~"25% - 7.5px");
      height: 185px;
      border: 1px solid #d3d7de;
      margin: 0 10px 10px 0;
      background: #f9f9f9;
      box-shadow: 0 3px 5px 0 rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      img {
        width: 120px;
        height: 120px;
        margin: 15px;
      }
      .card-item-info {
        padding: 10px 0;
        .cnName {
          font-size: 20px;
          font-weight: bolder;
          color: #2c86f8;
        }
        .enName {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.75);
          opacity: 0.6;
          margin-bottom: 5px;
        }
        .info-label {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.75);
          font-weight: bold;
          margin-bottom: 5px;
          span {
            font-weight: normal;
          }
        }
      }
      .card-bottom {
        display: flex;
        justify-content: space-between;
        padding: 0 15px;
        .runtime {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.6);
          span {
            color: rgba(0, 0, 0, 0.8);
          }
        }
      }
    }
    .card-item:nth-child(4n) {
      margin-right: 0;
    }
  }
}
</style>
