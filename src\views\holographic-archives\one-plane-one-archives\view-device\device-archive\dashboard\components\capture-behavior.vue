<template>
  <div class="capture-behavior-content">
    <template v-if="typeBtn === 1">
      <LineEchart
        :title="{}"
        v-if="monthSeries[0].data.length !== 0"
        :xAxis="monthXAxis"
        :grid="monthGrid"
        :series="monthSeries"
      ></LineEchart>
      <ui-loading v-if="lineLoading" />
      <ui-empty v-if="monthSeries[0].data.length === 0"></ui-empty>
      <div
        class="data-time"
        :style="{
          left:
            sbgnlxType.includes('3') && sbgnlxType.includes('2') ? '13%' : '5%',
        }"
      >
        <p class="total">
          <b>抓拍总量:</b
          ><span class="color-blue">{{ analysisMonth.snapTotal }}</span>
          <b>{{ monthIndex }}月份抓拍量:</b
          ><span class="warning">{{ analysisMonth.snapMonthTotal }}</span>
        </p>
      </div>
    </template>

    <template v-if="typeBtn === 2">
      <HeatmapEchart
        :title="{}"
        v-if="HeatSeries[0].data.length !== 0"
        :visualMap="dayVisualMap"
        :yAxis="HeatYAxis"
        :series="HeatSeries"
      ></HeatmapEchart>
      <ui-loading v-if="heatLoading" />
      <ui-empty v-if="HeatSeries[0].data.length === 0"></ui-empty>
      <div class="day-status">
        <span class="bg-warning"></span>
        <label class="auxiliary-color mr-20">0次</label>
        <span class="card-box"></span>
        <label class="auxiliary-color">1-2000次</label>
        <span class="bg-blue"></span>
        <label class="auxiliary-color">>2000次</label>
      </div>
    </template>
    <div class="btns">
      <DatePicker
        type="month"
        placeholder="请选择"
        format="yyyy年MM月"
        placement="top"
        transfer
        :open="open"
        :value="month"
        @on-change="handleChange"
      >
        <slot>
          <Button
            class="card-btn time-btn"
            :class="typeBtn === 1 ? ' card-active-btn' : ''"
            @click="handleClick(1)"
            >{{ month }}</Button
          >
        </slot>
      </DatePicker>
      <Button
        class="card-btn ml-10"
        :class="typeBtn === 2 ? ' card-active-btn' : ''"
        @click="overviewBtn(2)"
        >近7日</Button
      >
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import LineEchart from "@/components/echarts/line-echart";
import HeatmapEchart from "@/components/echarts/heatmap-echart";
export default {
  components: {
    LineEchart,
    HeatmapEchart,
  },
  props: {
    lineLoading: {
      type: Boolean,
      default: false,
    },
    heatLoading: {
      type: Boolean,
      default: false,
    },
    sbgnlxType: {
      type: Array,
      default: () => [],
    },
    // 抓拍月数据
    analysisMonth: {
      type: Object,
      default: () => {},
    },
    // 抓拍日分析
    analysisDay: {
      type: Object,
      default: () => {},
    },
    HeatYAxis: {
      type: Object,
      default: () => {},
    },
    // 抓拍日分析
    HeatSeries: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      typeBtn: 1,
      open: false,
      month: "",
      monthIndex: "",
      monthGrid: {
        left: "2%",
        top: "14%",
        right: "3%",
        bottom: "0%",
        containLabel: true,
      },
      dayVisualMap: [
        {
          show: false,
          calculable: false,
          type: "piecewise",
          pieces: [
            { gte: 2001, color: "#48BAFF" },
            { gte: 1, lt: 2000, color: "#F9F9F9" },
            {
              value: 0,

              color: "#F29F4C",
            },
          ],
        },
      ],
      // 抓拍分析值
      monthSeries: [
        {
          type: "line",
          data: [],
          symbol: "circle", // 默认是空心圆（中间是白色的），改成实心圆
          showAllSymbol: true,
          symbolSize: 0,
          smooth: true,
          lineStyle: {
            width: 2,
            color: "#2C86F8", // 线条颜色
          },
          areaStyle: {
            //区域填充样式
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(44, 134, 248, 0.2)",
                },
                {
                  offset: 1,
                  color: "rgba(91, 163, 255, 0)",
                },
              ],
              false
            ),
          },
        },
      ],
      // 抓拍分析x轴
      monthXAxis: {
        name: "日期",
        type: "category",
        data: [],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        boundaryGap: true,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
      },
    };
  },
  watch: {
    analysisMonth: {
      handler(val) {
        this.monthSeries[0].data = [];

        if (val.Y && val.Y.length !== 0) {
          this.monthXAxis.data = val.X;
          this.monthSeries[0].data = val.Y;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.month = this.getDate();
  },
  methods: {
    // 获取当前月份
    getDate() {
      let year = new Date().getFullYear();
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 9 ? month : `0${month}`;
      return `${year}年${this.monthIndex}月`;
    },
    overviewBtn(val) {
      this.typeBtn = val;
      this.$emit("overviewBtn", this.typeBtn);
    },
    // 日期面板点击关闭收起
    handleClick(val) {
      if (this.typeBtn === 2) {
        this.open = false;
        this.typeBtn = val;
        this.$emit("handleMonth", this.month);
      } else {
        this.open = !this.open;
      }
    },
    // 月份切换
    handleChange(time) {
      this.month = time;
      this.monthIndex = time.slice(5, 7);
      this.open = !this.open;
      this.$emit("handleMonth", this.month);
    },
  },
};
</script>
<style lang="less" scoped>
.capture-behavior-content {
  height: 100%;
  width: 100%;
  position: relative;
  .activity-count {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
  }
  .data-time {
    font-size: 14px;
    display: flex;
    align-items: center;
    position: absolute;
    top: -40px;
    left: 5%;
    /deep/.ivu-input {
      width: 200px;
      height: 34px;
    }
    .total {
      b {
        margin-left: 50px;
        margin-right: 5px;
      }
      span {
        font-weight: bold;
      }
    }
  }
  .day-status {
    font-size: 12px;
    display: flex;
    align-items: center;
    position: absolute;
    top: -40px;
    left: 40%;
    span {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 5px;
      box-shadow: none;
    }
    label {
      margin-right: 20px;
    }
  }
  .btns {
    position: absolute;
    top: -40px;
    right: 0px;
    span {
      z-index: 3;
    }
    .time-btn::after {
      content: "";
      width: 0;
      height: 0;
      position: absolute;
      bottom: 2px;
      right: 2px;
      border-left: 4px solid transparent;
      border-top: 4px solid transparent;
      border-right: 4px solid #fff;
      border-bottom: 4px solid #fff;
    }
  }
}
</style>
