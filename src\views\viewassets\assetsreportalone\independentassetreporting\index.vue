<template>
  <div class="assetsreport auto-fill">
    <div v-if="!componentName" class="auto-fill">
      <div class="select-header">
        <Badge :count="countStatic">
          <i class="icon-font icon-xiaoxi2" @click="searchNow" title="未读信息"></i>
        </Badge>
      </div>
      <!-- 顶部统计 -->
      <ChartsContainer :abnormalCount="countList" class="charts" />
      <!-- 表格 -->
      <TableList ref="tableList" :columns="tableColumns" :loadData="loadDataList" @selectAction="selectAction">
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <Search ref="search" @startSearch="startSearch" @startStatic="startStatic"></Search>
          <!-- 操作按钮 -->
          <div class="operation">
            <Checkbox v-model="isAll" @on-change="checkedAll">全选</Checkbox>
            <div class="right-btn fr">
              <create-tabs
                class="governancetoolset-component-card inline pointer"
                component-name="AssetsCompare"
                tabs-text="资产比对结果"
                :tabs-query="{}"
                @selectModule="selectModule"
              >
                <span class="font-active-color text-under-line">查看上次比对结果</span>
              </create-tabs>
              <Button type="primary" @click="assetsCompare">
                <i class="icon-font icon-piliangshangbao"></i> 资产比对
              </Button>
              <Button type="primary" @click="assetsGet">
                <i class="icon-font icon-piliangshangbao"></i> 资产获取
              </Button>
              <Button type="primary" @click="assetsSync">
                <i class="icon-font icon-piliangshangbao"></i> 资产同步
              </Button>
              <Button type="primary" @click="deleteBulk">
                <i class="icon-font icon-piliangshangbao"></i> 批量删除
              </Button>
              <Button type="primary" @click="report()">
                <i class="icon-font icon-piliangshangbao"></i> 批量上报
              </Button>
            </div>
          </div>
        </div>
        <!-- 表格操作 -->
        <template #action="{ row }">
          <ui-btn-tip
            class="mr-md"
            icon="icon-bianji2"
            content="编辑"
            @click.native="deviceModalShow(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="mr-md"
            :styles="{ color: 'var(--color-active)', 'font-size': '14px' }"
            icon="icon-shujushangbao"
            content="数据上报"
            @click.native="report(row)"
          ></ui-btn-tip>
          <!-- <ui-btn-tip :styles="{ color: '#269F26', 'font-size': '14px' }"
                      class="mr-md"
                      icon="icon-shebeidangan"
                      @click.native="deviceArchives(row)"
                      content="设备档案"></ui-btn-tip> -->
          <ui-btn-tip
            :disabled="row.cascadeReportStatus != 1"
            :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
            @click.native="reportError(row)"
            class="mr-md"
            icon="icon-shebeijiucuo"
            content="上报纠错"
          ></ui-btn-tip>
          <!-- <ui-btn-tip class="mr-md"
                      :disabled="row.deviceCheckStatus !== 3"
                      :styles="{ color: '#CC4242', 'font-size': '14px' }"
                      @click.native="viewRecord(row)"
                      icon="icon-yichangyuanyin"
                      content="异常原因"></ui-btn-tip> -->
          <ui-btn-tip
            :styles="{ color: 'var(--color-failed)', 'font-size': '14px' }"
            @click.native="deleteItem({ ids: [row.id] })"
            icon="icon-shanchu"
            content="删除"
          ></ui-btn-tip>
        </template>
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <template #cascadeReportStatus="{ row }">
          <span
            :style="{
              color:
                row.cascadeReportStatus == 1
                  ? 'var(--color-success)'
                  : row.cascadeReportStatus == 0
                    ? 'var(--color-failed)'
                    : '',
            }"
            >{{ row.cascadeReportStatus == 1 ? '已上报' : row.cascadeReportStatus == 0 ? '未上报' : '--' }}</span
          >
        </template>
        <template #recentlyReportStatus="{ row }">
          <span
            :style="{
              color:
                row.recentlyReportStatus == 1
                  ? 'var(--color-success)'
                  : row.recentlyReportStatus == 0
                    ? 'var(--color-failed)'
                    : '',
            }"
            >{{ row.recentlyReportStatus == 1 ? '上报成功' : row.recentlyReportStatus == 0 ? '上报失败' : '--' }}</span
          >
        </template>
        <template #isOnline="{ row }">
          <div>
            <span
              class="check-status-font"
              :class="row.isOnline === '1' ? 'font-success' : row.isOnline === '2' ? 'font-failed' : ''"
            >
              {{ row.isOnlineText }}
            </span>
          </div>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltipType">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #baseCheckStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
            >
              {{ row.baseCheckStatus == 1 ? '不合格' : row.baseCheckStatus == 0 ? '合格' : '--' }}
            </span>
          </div>
        </template>
        <template #viewCheckStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="row.viewCheckStatus === '0' ? 'bg-success' : row.viewCheckStatus === '1' ? 'bg-failed' : ''"
            >
              {{ row.viewCheckStatus == 1 ? '不合格' : row.viewCheckStatus == 0 ? '合格' : '--' }}
            </span>
          </div>
        </template>
        <template #videoCheckStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="row.videoCheckStatus === '0' ? 'bg-success' : row.videoCheckStatus === '1' ? 'bg-failed' : ''"
            >
              {{ row.videoCheckStatus == 1 ? '不合格' : row.videoCheckStatus == 0 ? '合格' : '--' }}
            </span>
          </div>
        </template>
        <template #checkStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="
                row.checkStatus === '0000'
                  ? 'bg-success'
                  : row.checkStatus === '1000'
                    ? 'bg-other'
                    : row.checkStatus
                      ? 'bg-failed'
                      : ''
              "
            >
              {{ handleCheckStatus(row.checkStatus) }}
            </span>
          </div>
        </template>
        <template #phyStatus="{ row }">
          <span :style="{ color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-failed)' }">{{
            row.phyStatus | filterType(phystatusList)
          }}</span>
        </template>
        <template #recentlyReportTime="{ row }">
          <span>{{ row.recentlyReportTime == '1970-01-01 08:00:01' ? '--' : row.recentlyReportTime }}</span>
        </template>
        <template #reportSuccessTime="{ row }">
          <span>{{ row.reportSuccessTime == '1970-01-01 08:00:01' ? '--' : row.reportSuccessTime }}</span>
        </template>
      </TableList>
      <!-- 不合格数据 -->
      <UnqualifiedModal ref="unqualifiedModal" :reportPlatformId="reportPlatformId" :intefaceList="intefaceList" />
      <device-detail
        v-model="deviceDetailShow"
        :choosed-org="choosedOrg"
        :modal-title="deviceDetailTitle"
        :modal-action="deviceDetailAction"
        :view-device-id="viewDeviceId"
        :view-url="viewUrl"
        :save-url="saveUrl"
        :device-code="deviceCode"
        :unqualified="deviceUnqualified"
        @update="init"
      >
      </device-detail>
      <view-detection-field
        v-model="recordShow"
        :view-data="recordData"
        :need-option="true"
        @recordModalShow="deviceModalShow"
      ></view-detection-field>
      <getAssetsModal ref="getAssetsModalRef" @init="init"></getAssetsModal>
      <AssetsAsync v-model="assetsAsyncShow"></AssetsAsync>
    </div>
    <keep-alive>
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import viewassets from '@/config/api/viewassets';

export default {
  name: 'independentassetreporting',
  components: {
    SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    TableList: require('../components/tableList').default,
    Search: require('../components/search').default,
    UnqualifiedModal: require('../components/unqualifiedModal').default,
    ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    GetAssetsModal: require('../components/getAssetsModal').default,
    CreateTabs: require('@/components/create-tabs/create-tabs').default,
    AssetsCompare: require('../components/assets-compare/index.vue').default,
    AssetsAsync: require('../components/assetsAsync.vue').default,
  },
  props: {},
  data() {
    return {
      viewUrl: viewassets.reportDeviceView,
      saveUrl: viewassets.reportUpdateView,
      assetsAsyncShow: false,
      countStatic: 0,
      reportPlatformId: '',
      intefaceList: [],
      choosedOrg: {},
      isAll: false,
      searchData: {},
      selectRows: [],
      countList: [
        { title: '设备总量', count: '0', icon: 'icon-equipmentlibrary' },
        { title: '已上报总量', count: '0', icon: 'icon-assetsreport' },
        { title: '未上报总量', count: '0', icon: 'icon-weishangbaoshuliang' },
        {
          title: '最近一次失败数量',
          countKey: '0',
          icon: 'icon-zuijinyicishangbaoshibaishuliang',
        },
      ],
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          fixed: 'left',
        },
        {
          width: 100,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
        },
        { width: 120, title: '组织机构', key: 'orgName', tooltip: true },
        { width: 100, title: '行政区划', key: 'civilName', tooltip: true },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        { minWidth: 100, title: '采集区域', key: 'sbcjqyText', tooltip: true },
        {
          width: 100,
          title: '在线状态',
          slot: 'isOnline',
          align: 'left',
          // fixed: 'right',
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        {
          width: 100,
          title: '基础信息状态',
          slot: 'baseCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视图数据状态',
          slot: 'viewCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视频数据状态',
          slot: 'videoCheckStatus',
          align: 'left',
        },
        { width: 90, title: '检测状态', slot: 'checkStatus' },
        {
          minWidth: 90,
          title: '上报状态',
          slot: 'cascadeReportStatus',
          tooltip: true,
        },
        {
          width: 150,
          title: '最近一次上报状态',
          slot: 'recentlyReportStatus',
          tooltip: true,
        },
        {
          width: 150,
          title: '最近一次上报时间',
          slot: 'recentlyReportTime',
          tooltip: true,
        },
        { width: 150, title: '最近成功上报时间', slot: 'reportSuccessTime' },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      recordShow: false,
      recordData: {},
      deviceUnqualified: null,
      deviceCode: '',
      viewDeviceId: 0,
      deviceDetailShow: false,
      deviceDetailAction: 'edit',
      deviceDetailTitle: '修改入库数据',
      compareLoading: false,
      componentName: '',
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  watch: {
    $route: 'getParams',
  },
  filter: {},
  async created() {
    this.getParams();
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData();
    this.unreadStatistics();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    // 资产比对
    assetsCompare() {
      this.compareLoading = true;
      this.$UiConfirm({
        content: '确定要比对【设备资产库】和【资产上报库】差异情况吗？',
      }).then(async () => {
        try {
          // 未读信息接口统计
          const res = await this.$http.get(viewassets.allCompareData);
          this.compareLoading = false;
          this.$Message.success(res.data.msg);
        } catch (err) {
          console.log(err);
        }
      });
    },
    // 资产获取
    assetsGet() {
      this.$refs.getAssetsModalRef.open();
    },
    // 资产同步
    assetsSync() {
      this.assetsAsyncShow = true;
    },
    startSearch(searchData, choosedOrg) {
      this.choosedOrg = choosedOrg;
      let params = Object.assign(this.searchData, searchData);
      if (params.recentlyReportTimeStart) {
        params.recentlyReportTimeStart = this.$util.common.formatDate(this.searchData.recentlyReportTimeStart);
      }
      if (params.recentlyReportTimeEnd) {
        params.recentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.recentlyReportTimeEnd);
      }
      this.startStatic();
      this.$refs.tableList.search();
    },
    // 获取统计
    startStatic() {
      this.$http.post(viewassets.reportQueryDeviceInfoStatistics, this.searchData).then((res) => {
        this.countList = [
          {
            title: '设备总量',
            count: res.data.data.deviceInfoCount || '0',
            icon: 'icon-equipmentlibrary',
          },
          {
            title: '已上报总量',
            count: res.data.data.reportedDeviceCount || '0',
            icon: 'icon-assetsreport',
          },
          {
            title: '未上报总量',
            count: res.data.data.unReportDeviceCount || '0',
            icon: 'icon-weishangbaoshuliang',
          },
          {
            title: '最近一次失败数量',
            count: res.data.data.lastReportFailedCount || '0',
            icon: 'icon-zuijinyicishangbaoshibaishuliang',
          },
        ];
      });
    },
    init() {
      this.$refs.tableList.init();
    },
    loadDataList(searchData) {
      let params = Object.assign(this.searchData, searchData);
      return this.$http.post(viewassets.reportQueryDeviceInfoPageList, params).then((res) => {
        return res.data.data;
      });
    },
    unreadStatistics() {
      // 未读信息接口统计
      this.$http.post(viewassets.unreadStatistics).then((res) => {
        this.countStatic = res.data.data;
      });
    },
    // 最新信息查询
    searchNow() {
      this.$http.post(viewassets.updateAllMessageStatus).then(() => {
        this.$refs.unqualifiedModal.open();
        this.countStatic = 0;
      });
    },
    // 表格选中
    selectAction(rows) {
      this.selectRows = rows;
    },
    // 上报 || 批量上报
    report(rows) {
      let content = '';
      if (rows) {
        content = '确定上报吗？';
        this.reportAll(content, rows);
      } else if (this.isAll) {
        content = '确定上报吗？';
        this.reportAll(content);
      } else {
        content = '已选择' + this.selectRows.length + '条设备，确定上报吗？';
        if (this.selectRows.length == 0) {
          this.$Message.warning('请勾选待上报设备');
        } else {
          this.reportAll(content);
        }
      }
    },
    reportAll(content, rows) {
      this.$UiConfirm({
        content: content,
        title: '数据上报',
      }).then(() => {
        this.searchData.isAll = this.isAll ? 1 : 0;
        let params = JSON.parse(JSON.stringify(this.searchData));
        if (rows) {
          params.deviceIds = [rows.deviceId];
        } else if (!this.isAll) {
          params.deviceIds = this.selectRows.map((val) => {
            return val.deviceId;
          });
        }
        // params.reportPlatformId = this.reportPlatformId
        this.$http.post(viewassets.assetBatchReport, params).then(() => {
          this.init();
          this.startStatic();
          this.selectRows = [];
          this.$Message.success('提交成功');
        });
      });
    },
    // 设备档案
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    // 上报纠错
    reportError(row) {
      if (row.cascadeReportStatus == 1) {
        this.$UiConfirm({
          content: '该设备确定未成功上报吗？纠错后将删除全部历史上报记录！',
          title: '上报纠错',
        }).then(() => {
          this.$http.get(viewassets.reportErrorCorrecting + row.id).then(() => {
            this.init();
            this.startStatic();
            this.$Message.success('纠错成功');
          });
        });
      }
    },
    viewRecord(row) {
      this.recordShow = true;
      this.recordData = row;
    },
    deleteItem(params) {
      console.log(params, 'parans');
      this.$UiConfirm({
        content: '删除后设备将从上报库移除（不影响资产库），确定删除吗？',
        title: '删除设备',
      }).then(() => {
        this.$http.delete(viewassets.deleteReport, { data: params }).then(() => {
          this.$refs.tableList.search();
          this.startStatic();
          this.$Message.success('删除成功');
        });
      });
    },
    deleteBulk() {
      let params = {};
      if (this.isAll) {
        params = { ...this.searchData };
      } else {
        if (!this.selectRows.length) {
          this.$Message.warning('请选择设备');
          return;
        }
        params.ids = this.selectRows.map((item) => item.id);
      }
      this.deleteItem(params);
    },
    checkedAll() {},
    handleCheckStatus(row) {
      const flag = {
        1000: '待检测',
        '0000': '合格',
      };
      let msg = '';
      if (row) {
        msg = flag[row] ? flag[row] : '不合格';
      }
      return msg;
    },
    deviceModalShow(row, unqualified) {
      this.deviceDetailTitle = '修改入库设备';
      this.deviceDetailAction = 'edit';
      this.deviceUnqualified = unqualified || null;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[0];
      } else {
        this.componentName = null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue']{
  .select-header .icon-font{
    color: #888888;
    &:hover {
      color: var(--color-switch-tab-active);
    }
  }
}
.assetsreport {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .tables {
    @{_deep} .ui-table {
      padding: 0 20px;
    }
  }
  .charts {
    margin: 10px 0 0 20px;
  }
  .operation {
    margin: 2px 0 12px 0;
    height: 34px;
    line-height: 34px;
    .right-btn {
      button {
        margin-left: 12px;
      }
    }
  }
  .select-header {
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 11;
    color: var(--color-switch-tab);
    &:hover {
      color: var(--color-switch-tab-active);
    }
    .ivu-badge {
      @{_deep} .ivu-badge-count {
        height: 16px;
        line-height: 16px;
        border-radius: 14px;
      }
    }
  }
  .icon-xiaoxi2 {
    font-size: 19px;
    position: relative;
    left: -5px;
  }
  .icon-piliangshangbao {
    font-size: 12px;
    margin-right: 10px;
    position: relative;
    top: -1px;
  }
  .tooltipType {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .text-under-line {
    text-decoration: underline;
  }
}
</style>
