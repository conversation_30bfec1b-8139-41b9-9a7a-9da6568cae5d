<template>
  <div class="dom-wrapper" v-if="isShow">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>报警详情</span>
        <Icon type="md-close" size="14" @click.native="isShow = false" />
      </header>
      <section class="dom-content">
        <div class="left">
          <div class="left-top">
            <div class="title-header">场所信息</div>
            <div class="base-info-wrapper">
              <BaseInfo :info="baseInfo" />
              <ui-tag-poptip
                class="tags"
                v-if="baseInfo.labels && baseInfo.labels.length !== 0"
                :data="baseInfo.labels"
              />
            </div>
          </div>
          <div class="left-bottom">
            <div class="title-header">图上位置</div>
            <div class="box-content">
              <MapBase
                ref="mapRef"
                class="map"
                v-if="baseInfo.coord"
                :place-fence="placeFence"
              />
            </div>
          </div>
        </div>
        <div class="right">
          <img v-lazy="baseInfo.image" alt="" />
        </div>
      </section>
      <footer>
        <div class="footer-header">
          <div class="title-header">聚集人员</div>
          <div class="footer-header-right">
            <span><span class="fontB">报警时间: </span><span style="margin-right: 5px">{{detailList[0]?.alarmTime}}</span></span>
            <span><span class="fontB">聚集人员: </span><span class="primary fontB">{{ detailList[0]?.gatheredPersonTotal }}</span></span>
          </div>
        </div>
        <div class="footer-content">
          <swiper
            ref="mySwiper"
            :options="swiperOption"
            class="my-swiper"
            id="frequent-swiper"
          >
            <template>
              <swiper-slide v-for="(item, index) in detailList" :key="index">
                <div class="swiper-item" @click="() => handleDetailFn(item, index)">
                  <div class="frequent-box">
                    <div class="head-img-box">
                      <div class="score-box">{{ item.appearTotalNumber || 0 }}</div>
                      <div class="img-box">
                        <img :src="item.traitImg" />
                      </div>
                    </div>
                    <div class="text-box">
                      <div class="sub-sub-text">
                        <span class="primary fontB">{{ item.name }}</span>
                        <span style="color: #000">（{{item.age || '--'}}岁）</span>
                      </div>
                      <div class="sub-sub-text">（最后一次出现）</div>
                      <div class="sub-sub-text">{{ item.lastCaptureTime }}</div>
                      <div v-if="item.bizLabels && item.bizLabels.length" class="labels-list">
                        <div
                          class="label-item"
                          v-for="item in item.bizLabels"
                          :key="item"
                        >
                          {{ item }}
                        </div>
                      </div>
                      <div v-else style="height: 22px"></div>
                    </div>
                  </div>
                </div>
              </swiper-slide>
            </template>
          </swiper>
          <div>
            <div
              class="swiper-button-prev snap-prev"
              slot="button-prev"
              id="frequentRight"
            >
              <i class="iconfont icon-caret-right"></i>
            </div>
            <div
              class="swiper-button-next snap-next"
              slot="button-next"
              id="frequentLeft"
            >
              <i class="iconfont icon-caret-right"></i>
            </div>
          </div>
        </div>
      </footer>
    </div>

    <details-face-modal
      v-if="faceShow"
      ref="faceDetailRef"
      @close="faceShow = false"
    ></details-face-modal>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import MapBase from "@/components/map/index";
import BaseInfo from "@/views/holographic-archives/place-archives/dashboard/components/base-info.vue";
import {
  queryEmphasisPersonGatheredPlaceAlarmDetail,
  queryEmphasisPersonGatheredPlaceCaptureDetail,
} from "@/api/monographic/place.js";
import { getPlaceArchiveDetailInfoAPI } from "@/api/placeArchive";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
export default {
  name: "",
  components: {
    MapBase,
    BaseInfo,
    swiper,
    swiperSlide,
    detailsFaceModal
  },
  data() {
    return {
      isShow: false,
      baseInfo: {},
      detailList: [],
      swiperOption: {
        slidesPerView: "7",
        // slidesPerGroup: 3,
        speed: 1000,
        navigation: {
          nextEl: "#frequentLeft",
          prevEl: "#frequentRight",
        },
        observer: true,
        observeParents: true,
      },
      faceShow: false
    };
  },
  watch: {},
  computed: {
    // 场所围栏
    placeFence() {
      if (this.baseInfo.coord) {
        let placeFence = {
          code: "67021",
          name: "购物服务",
          color: "#F37A7A",
          layerType: "LayerType_" + this.baseInfo.secondLevel,
        };
        placeFence.data = [
          {
            properties: { ...this.baseInfo },
            geometry: JSON.parse(this.baseInfo.coord),
          },
        ];
        return placeFence;
      }
    },
  },
  mounted() {},
  methods: {
    // 不查询使用提供的列表
    showList(item) {
      this.detailList = []
      this.getPersonBaseInfoRequest(item.placeId).then(res => {
        this.isShow = true
      })
      queryEmphasisPersonGatheredPlaceAlarmDetail({ id: item.id }).then((res) => {
        this.detailList = res.data?.entities || []
      });
    },
    getPersonBaseInfoRequest(archiveNo) {
      return new Promise(resolve => {
        getPlaceArchiveDetailInfoAPI(archiveNo).then((res) => {
          this.baseInfo = res.data;
          if (
            this.baseInfo.labelIds &&
            typeof this.baseInfo.labelIds === "string"
          ) {
            // 场所档案返回的labelIds为labelId以,拼接的字符串，需要转为数组
            this.baseInfo.labelIds = this.baseInfo.labelIds.split(",");
          }
          resolve()
        });
      })
    },
    handleDetailFn(item) {
      let {vids, idCardNo, firstCaptureTime, lastCaptureTime} = {...item}
      queryEmphasisPersonGatheredPlaceCaptureDetail({vids, idCardNo, firstCaptureTime, lastCaptureTime}).then(res => {
        this.faceShow = true;
        this.$nextTick(() => {
          this.$refs.faceDetailRef.init(res.data?.entities[0] || {}, res.data?.entities || [], 0, 5, 1);
        });
      })
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.primary {
  color: #2c86f8;
}
.fontB {
  font-weight: 700;
}
.title-header {
  font-weight: 700;
  font-size: 16px;
  color: rgba(0,0,0,0.9);
  display: flex;
  position: relative;
  margin-left: 5px;
  &::before {
    content: '';
    display: block;
    width: 3px;
    height: 20px;
    background: #2C86F8;
    position: absolute;
    left: -5px;
    top: 2px;
  }
}
.dom {
  width: 1200px !important;
}
.dom-content {
  display: flex;
  padding: 30px 60px!important;
  .left {
    width: 30%;
    margin-right: 30px;
    display: flex;
    flex-direction: column;
    .left-top {
      /deep/ .base-info-wrapper {
        .ui-module {
          box-shadow: none;
        }
        .module-head {
          display: none;
        }
        .module-content {
          padding: 15px 0 0 0 !important;
          .info-wrapper {
            margin: 0;
          }
        }
        .tags {
          margin: -10px 0 10px 0;
        }
      }
    }
    .left-bottom {
      flex: 1;
      overflow: hidden;
      .box-content {
        height: calc(~"100% - 39px");
        margin-top: 15px;
      }
    }
  }
  .right {
    flex: 1;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
footer {
  height: 250px!important;
  margin: 0 60px;
  flex-direction: column;
  align-items: flex-start;
  overflow: hidden;
  .footer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }
  .footer-content {
    flex: 1;
    padding: 0 15px;
    width: 100%;
    overflow: hidden;
    position: relative;
    .my-swiper {
      margin: auto;
      height: 100%;
      overflow: hidden;
      .swiper-item {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.frequent-box {
  width: 126px;
  height: 100%;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  &::after {
    content: "";
    width: 100%;
    height: 90px;
    display: block;
    position: absolute;
    top: 25%;
    z-index: -1;
    background: linear-gradient(180deg, #eeeeee 0%, #ffffff 100%);
    border-radius: 4px 4px 4px 4px;
  }
  .head-img-box {
    width: 80px;
    height: 80px;
    display: flex;
    margin: 10px auto;
    justify-content: center;
    align-items: center;
    position: relative;
    .score-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 30px;
      background: linear-gradient(237deg, #ec9240 0%, #f7b93d 100%);
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      color: white;
      font-weight: bold;
    }

    .img-box {
      margin: 0 auto;
      width: 100%;
      height: 100%;
      overflow: hidden;
      border: 1px solid #45e8ff;

      border-radius: 50%;
      /deep/ img {
        width: 100%;
        max-height: 100%;
      }
    }
  }
  
  .text-box {
    width: 100%;
    text-align: center;
    text-wrap: nowrap;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    flex: 1;
    .sub-sub-text {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.35);
    }
  }
}

.labels-list {
  display: flex;
  justify-content: center;
  flex-flow: wrap;
  gap: 5px;
  .label-item {
    background: #EA4A36;
    border-radius: 2px;
    font-size: 12px;
    color: #fff;
    padding: 0 3px;
    height: 22px;
    line-height: 22px;
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;
  z-index: 999;
  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 0;
}

.swiper-button-next {
  right: 0;
}
</style>
