<template>
  <div
    class="process-block"
    :style="{
      top: processOptions.top || '0',
      left: processOptions.left || '0',
    }"
  >
    <div v-if="processOptions.isConfigure === '1' && processOptions.iconSetting" class="process-block-top">
      <i class="icon-font icon-chenggong"></i>
    </div>
    <div class="process-block-center">
      <div class="process-block-center-icon">
        <i class="icon-font" :class="processOptions.icon"></i>
      </div>
      <div class="process-block-center-content">
        <Poptip trigger="hover" :disabled="isDisabledTooltip" placement="top" word-wrap :content="processOptions.title">
          <p @mouseover="onMouseOverHeight(processOptions.name)">
            <span :ref="processOptions.name">{{ processOptions.title }}</span>
          </p>
        </Poptip>
        <ul class="static-ul" v-if="!!processOptions.list && !!processOptions.list.length">
          <li class="static-li f-14" v-for="(item, index) of processOptions.list" :key="index">
            <span class="static-li-label">{{ item.title }}: </span>
            <span class="static-li-num ml-xs" :style="{ color: item.color }">{{ item.num }}</span>
          </li>
        </ul>
        <div v-if="processOptions.desc" class="process-block-center-content-wrap">
          <!-- 左侧描述+开关 -->
          <div class="process-block-center-content-wrap-left">
            <div class="process-block-center-content-desc">
              <!-- 描述 -->
              <Poptip
                trigger="hover"
                :disabled="isDisabledTooltip"
                placement="top"
                word-wrap
                :content="processOptions.desc"
              >
                <div
                  class="process-block-center-content-desc-title"
                  @mouseover="onMouseOverHeight('desc' + processOptions.name)"
                >
                  <span :ref="'desc' + processOptions.name">{{ processOptions.desc }}</span>
                </div>
              </Poptip>
            </div>
            <!-- 开关按钮 -->
            <div v-if="processOptions.switch" class="switch-box fl-end">
              <i-switch
                class="iswitch"
                size="small"
                v-if="showSwitch"
                v-model="switch1"
                @on-change="handleSwitch($event, processOptions)"
              />
            </div>
          </div>
          <!-- 右侧按钮 -->
          <div v-if="processOptions.iconSetting" class="process-block-center-content-wrap-setting">
            <Poptip trigger="hover" content="参数配置" placement="top">
              <i
                class="icon-font"
                :class="!!processOptions.iconView ? `${processOptions.iconView}` : 'icon-systemmanagement'"
                @click="setting(processOptions.name, processOptions)"
              ></i>
            </Poptip>
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="processOptions.iconSetting && !!processOptions.list && !!processOptions.list.length"
      class="process-block-center-content-wrap-setting fl-end"
    >
      <Poptip trigger="hover" content="查看检测结果" placement="top" class="t-right pop-width">
        <i
          class="icon-font"
          :class="!!processOptions.iconView ? `${processOptions.iconView}` : 'icon-systemmanagement'"
          @click="setting(processOptions.name, processOptions)"
        ></i>
      </Poptip>
    </div>
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  props: {
    processOptions: {
      type: Object,
      default: () => {},
    },
  },
  // 调用祖先组件传递过来打开弹窗的函数
  inject: ['openTypeModal'],
  data() {
    return {
      switch1: false,
      showSwitch: false,
      isDisabledTooltip: true, // 是否需要禁止提示
    };
  },
  created() {
    this.showSwitch = false;
    if (this.processOptions.status == '1') {
      this.switch1 = true;
      this.showSwitch = true;
    } else {
      this.switch1 = false;
      this.showSwitch = true;
    }
  },
  methods: {
    setting(name, processOptions) {
      this.openTypeModal(name, processOptions);
    },
    // 移入事件: 判断内容的宽度contentWidth是否大于父级的宽度
    // onMouseOver(str) {
    //   let parentWidth = this.$refs[str].parentNode.offsetWidth;
    //   let contentWidth = this.$refs[str].offsetWidth;
    //   // 判断是否禁用tooltip功能s
    //   this.isDisabledTooltip = contentWidth < parentWidth;
    // },
    onMouseOverHeight(str) {
      let parentHeight = this.$refs[str].parentNode.offsetHeight;
      let contentHeight = this.$refs[str].offsetHeight;
      // 判断是否禁用tooltip功能
      this.isDisabledTooltip = contentHeight < parentHeight;
    },
    handleSwitch(val, item) {
      var param = {
        id: item.topicComponentId,
      };
      this.$http
        .put(api.updateStatus, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.switch1 = val;
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
            console.log(res.data.msg);
          }
        })
        .catch(() => {});
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.process-block {
  // position: absolute;
  width: 100%;
  padding: 10px;
  background: #0f2f59;
  border: 1px solid var(--color-primary);
  border-radius: 4px;
  &-top {
    width: 100%;
    text-align: right;
    i {
      margin-right: 2px;
      font-size: 15px;
      color: #43bf00;
    }
  }
  &-center {
    width: 100%;
    display: flex;
    &-icon {
      margin-right: 12px;
      i {
        // color: var(--color-primary);
        background: linear-gradient(to bottom, #12a4c9, #0084f6);
        -webkit-background-clip: text;
        color: transparent;
        -webkit-text-fill-color: transparent;
        -webkit-text-fill-color: transparent;
        font-size: 30px;
      }
    }
    &-content {
      // flex: 1;
      width: 100%;
      &-wrap {
        display: flex;
        &-left {
          flex: 1;
        }
        &-setting {
          display: flex;
          align-items: flex-end;
          i {
            font-size: 10px;
            color: #56789c;
            &:hover {
              color: var(--color-primary);
            }
            &:active {
              color: #4e9ef2;
            }
          }
        }
        .switch-box {
          width: 100%;
          padding-right: 10px;
          display: flex;
        }
      }
      p {
        width: 99.9%;
        color: #ffffff;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        span {
          font-size: 14px;
          color: #ffffff;
          cursor: pointer;
        }
      }
      &-desc {
        width: 100%;
        min-height: 52px;

        &-title {
          flex: 1;
          padding-right: 3px;
          display: -webkit-box;
          font-size: 12px;
          color: #99a4af;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
          span {
            font-size: 14px;
            color: #ffffff;
            cursor: pointer;
          }
        }
        @media screen and (max-width: 1366px) {
          &-title {
            -webkit-line-clamp: 2;
          }
        }
      }
    }
  }
}
.static-ul {
  color: #99a4af;
  .static-li {
    margin-top: 5px;
    width: 100%;
    display: flex;
  }
}
@{_deep} .ivu {
  &-poptip {
    position: relative;
    width: 100%;
    &-rel {
      width: 100%;
    }
    &-arrow:after {
      border-top-color: transparent !important;
    }
    &-inner {
      border: 1px solid #0d4a81 !important;
    }
    &-body-content-inner {
      color: #ffffff !important;
      // background-color: #8c9399 !important;
    }
    &-popper {
      &[x-placement^='top'] {
        .ivu-poptip-arrow {
          border-top-color: var(--color-primary) !important;
          &:after {
            border-top-color: #0d3560 !important;
            bottom: 1px;
          }
        }
      }
      &[x-placement^='left'] {
        .ivu-poptip-arrow {
          border-left-color: #0d4a81 !important;
          &:after {
            border-left-color: #0d3560 !important;
          }
        }
      }
    }
  }
  &-poptip-popper[x-placement^='top'] .ivu-poptip-arrow {
    border-top-color: #0d4a81 !important;
  }
}
.fl-end {
  justify-content: flex-end !important;
}
.pop-width {
  width: 20px;
  /deep/ .ivu-poptip-popper {
    width: auto;
    min-width: auto;
    left: -37px;
  }
}

// .iswitch {
//   position: absolute;
//   right: 30px;
//   bottom: 20px;
// }
.nullConfig {
  right: 10px;
}
</style>
