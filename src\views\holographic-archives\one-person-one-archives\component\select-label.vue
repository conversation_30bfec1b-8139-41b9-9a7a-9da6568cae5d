<template>
  <uiModal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onOk="confirmHandle"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="select-label-content">
        <div>
          <Form ref="queryParam" :model="queryParam" inline class="search">
            <FormItem label="标签名称" prop="name" class="search-input">
              <Input
                v-model="queryParam.name"
                maxlength="50"
                type="text"
                placeholder="请输入"
              ></Input>
            </FormItem>
            <FormItem label="标签类型" prop="type" class="search-input">
              <Select v-model="queryParam.type" placeholder="请选择">
                <Option
                  v-for="item in labelTypeList"
                  :value="item.dataKey"
                  :key="item.dataKey"
                >
                  {{ item.dataValue }}
                </Option>
              </Select>
            </FormItem>
            <FormItem
              label="字母筛选"
              prop="pinyin"
              class="search-input not-mb"
            >
              <div class="letter-ul">
                <span
                  v-for="(item, $index) in letterArray"
                  :key="$index"
                  :class="[
                    'letter-li',
                    queryParam.pinyin === item ? 'letter-li-active' : '',
                  ]"
                  @click="changeLabelLetter(item)"
                  >{{ item }}</span
                >
              </div>
            </FormItem>
            <FormItem class="btn-group">
              <Button type="primary" @click="labelSearchHandle">查询</Button>
              <Button type="default" @click="labelResetHandle">重置</Button>
            </FormItem>
          </Form>
        </div>
        <div class="label-head">{{ queryParam.pinyin || "A" }}</div>
        <div class="label-container">
          <div ref="labelUl" class="label-ul">
            <ui-tag
              v-for="item in labelList"
              :key="item.id"
              :color="item.color"
              :id="item.id"
              :checked="item.checked"
              :data="item"
              checkable
              multiple
              @on-change="tagChangeHandle"
              >{{ item.name }}
            </ui-tag>
          </div>
          <ui-empty v-if="!labelList.length && !labelLoading"></ui-empty>
          <ui-loading v-if="labelLoading"></ui-loading>
        </div>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{ checkedLabelList.length }}</span> 个</span
          >
          <span class="cursor-p" @click="removeAllHandle"
            ><Icon type="ios-trash-outline" class="icon-shanchu" />清空</span
          >
        </div>
        <div class="label-container">
          <ui-tag
            v-for="(item, $index) in checkedLabelList"
            :key="$index"
            :color="item.color"
            :id="item.id"
            closable
            @on-close="closePreviewLabelHandle(item)"
            >{{ item.name }}
          </ui-tag>
        </div>
      </div>
    </div>
  </uiModal>
</template>
<script>
// import { getLabelAllList, getLabelGroupAllList } from '@/api/labelPool'
import { mapActions, mapGetters } from "vuex";
export default {
  components: {
    // "ui-tag-poptip": require("@/components/ui-tag-poptip").default,
  },
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      modalShow: true,
      dialogData: {
        title: "选择标签",
        rWidth: 1060,
      },
      tabs: ["标签", "标签组"],
      tabIndex: 0,
      queryParam: {
        level: "",
        name: "",
        pinyin: "A",
        property: "",
        type: "",
      },
      labelGroupAllList: [],
      letterArray: [
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
      ],
      labelList: [],
      labelLoading: false,
      checkedLabelList: [],
      labelObj: "",
      labelTypeList: [],
    };
  },
  // computed: {
  //   ...mapGetters({
  //     labelTypeList: "dictionary/getLabelTypeList",
  //     labelLevelList: "dictionary/getLabelLevelList",
  //     labelPropertyList: "dictionary/getLabelPropertyList",
  //   }),
  // },
  watch: {
    checkedLabels(val) {
      this.checkedLabelList = val;
    },
    "queryParam.pinyin"(val) {
      this.requestLabelList();
    },
    checkedLabelList: {
      handler(n) {
        this.labelList = this.labelList.map((item) => {
          item.checked = this.checkedLabelList.some((d) => d.id === item.id);
          return item;
        });
      },
      immediate: true,
    },
  },
  created() {
    // this.getDictData();
  },
  methods: {
    // ...mapActions({
    //   getDictData: "dictionary/getDictAllData",
    // }),
    init(code) {
      this.checkedLabelList = [];
      this.ObjCode = code || "";
      this.modalShow = true;
      this.requestLabelList();
      this.getLabelGroupList();
      if (this.checkedLabels.length === 0) {
        this.queryParam.pinyin = "A";
      }
    },
    // 获取标签列表
    requestLabelList() {
      this.$nextTick(() => {
        this.labelLoading = true;
        this.$refs.labelUl.scrollTop = 0;
        const resultQueryParam = { ...this.queryParam };
        resultQueryParam.codeNotContain = [this.ObjCode];
        resultQueryParam.pinyin =
          this.queryParam.pinyin === "A" ? "" : resultQueryParam.pinyin;
        // getLabelAllList(resultQueryParam)
        //   .then((res) => {
        //     // TODO:此处可能出现性能问题，需优化
        //     this.labelList = res.data.map((item) => {
        //       item.checked = this.checkedLabelList.some(
        //         (d) => d.id === item.id
        //       );
        //       return item;
        //     });
        //   })
        //   .catch((res) => {
        //     console.error(res);
        //   })
        //   .finally(() => {
        //     this.labelLoading = false;
        //   });
      });
    },
    // 获取标签组列表
    getLabelGroupList() {
      // return new Promise(resolve => {
      //   getLabelGroupAllList().then(res => {
      //     this.labelGroupAllList = res.data
      //     resolve(res.data)
      //   })
      // })
    },
    changeLabelLetter(name) {
      this.queryParam.pinyin = name;
    },
    changeLabelGroupLetter(name) {
      this.labelGroupSearchData.pinyin = name;
    },
    // 控制标签组标签列表显示

    // 标签查询
    labelSearchHandle() {
      this.requestLabelList();
    },
    // 标签重置
    labelResetHandle() {
      this.$refs.queryParam.resetFields();
      this.requestLabelList();
    },
    tagChangeHandle(isChecked, row) {
      if (isChecked) {
        const isSelected = this.checkedLabelList.some((d) => d.id === row.id);
        if (!isSelected) {
          this.checkedLabelList.push(row);
        }
      } else {
        const itemIndex = this.checkedLabelList.findIndex(
          (d) => d.id === row.id
        );
        this.checkedLabelList.splice(itemIndex, 1);
      }
    },
    // 关闭标签
    closePreviewLabelHandle(item) {
      const itemIndex = this.checkedLabelList.findIndex(
        (d) => d.id === item.id
      );
      this.checkedLabelList.splice(itemIndex, 1);
    },
    removeAllHandle() {
      this.checkedLabelList = [];
    },
    confirmHandle() {
      this.$emit("setCheckedLabel", this.checkedLabelList);
      this.modalShow = false;
    },
  },
};
</script>
<style lang="less" scoped>
.select-label-container {
  border: 1px solid #d3d7de;
  display: flex;
  .select-label-content {
    flex: 1;
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .search {
    padding: 0;
    flex-wrap: wrap;
    border-bottom: 0;
    margin-bottom: 0;
    flex: 1;
    .search-input {
      display: inline-flex;
      margin-right: 0;
      &.not-mb {
        margin-bottom: 10px;
      }
      /deep/ .ivu-form-item-label {
        width: 70px;
        white-space: nowrap;
      }
    }
    .letter-ul {
      font-size: 16px;
      font-weight: bold;
      color: #000;
      height: 34px;
      display: flex;
      align-items: center;
      .letter-li {
        cursor: pointer;
        padding: 5px;
        white-space: nowrap;
        line-height: 12px;
      }
      .letter-li-active {
        color: #2b84e2;
      }
    }
  }
  .label-head {
    height: 20px;
    color: #2c86f8;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    flex: 1;
    height: 100%;
    position: relative;
  }
  .preview-select-label-content {
    height: 100%;
    width: 400px;
    color: #000;
    border-left: 1px solid #d3d7de;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .info-bar {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      font {
        color: #2c86f8;
      }
      .icon-shanchu {
        color: #2c86f8;
        font-size: 16px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.del-btn {
  cursor: pointer;
}
</style>
