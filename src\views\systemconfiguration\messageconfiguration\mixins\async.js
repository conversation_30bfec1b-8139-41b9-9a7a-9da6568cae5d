/**
 * 此文件为强关联其他模块无法使用
 */
import { mapGetters } from 'vuex';
import notification from '@/config/api/notification';
import expandRow from '../components/table-expand.vue';
const mixin = {
  data() {
    return {
      initializedDataMx: {},
      initializedReceiveDataMx: {},
      taskIdsInfoMx: {}, // 在更改||移除||清空输入框前，存储 当前的检测任务信息
      orgByTaskMx: {}, // 已选择的检测任务---》对应的本级+下级组织
    };
  },
  methods: {
    async initMx(types = []) {
      try {
        /**
         * 0000：通用参数
         * 0101：外部对接异常
         * 0201：系统运维异常
         * 0301：接口异常-人脸相关接口
         * 0302：接口异常-车辆相关接口
         * 0401：平台离线-联网平台离线
         * 0402：平台离线-人脸视图库离线
         * 0403：平台离线-车辆视图库离线
         * 0501：设备离线-视频监控设备离线
         * 0502：设备离线-人脸卡口设备离线
         * 0503：设备离线-车辆卡口设备离线
         * 0601： 资产审核不通过
         * 0701： 工单处理
         * 0801: 指标不达标
         */
        const res = await this.$http.post(notification.getConfig, {
          types: types,
        });
        this.formData = this.$util.common.deepCopy(res.data.data);
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 由于people-select-module默认回显的参数需要传入的是org的id集合
     * 这里需要查询出orgCode对应的id传入组件中
     */
    getDefaultPeopleMx(defaultData) {
      return defaultData.map((row) => {
        const orgCodeList = row.orgCode.split(',');
        let orgList = [];
        orgCodeList.forEach((orgCode) => {
          const org = this.initialOrgList.find((org) => org.orgCode === orgCode);
          org && orgList.push(org.id);
        });
        this.$set(row, 'orgList', orgList);
        return row;
      });
    },
    /**
     * 根据行政区划查询组织机构以及子组织机构
     * @param {*} regionCode
     */
    getOrgCodeMx(regionCode) {
      const org = this.initialOrgList.find((row) => row.regionCode === regionCode);
      if (org) {
        const orgChildList = this.initialOrgList.filter((row) => row.parentId === org.id);
        return [org, ...orgChildList];
      } else {
        console.error(`找不到行政区划编码${regionCode}对应的组织机构`);
      }
    },
    /**
     * 选中任务后将任务关联的行政区划查询出组织机构
     * 如果接收人中不存在该组织机构则加入
     * @param {*} taskId 检测任务id
     * @param {*} type 消息类型
     */
    setReceiveOrgMx(taskId, type) {
      const task = this.listTaskSchemes.find((row) => row.id === taskId);
      const orgList = this.getOrgCodeMx(task.regionCode);
      this.$set(this.orgByTaskMx, taskId, orgList);
      if (orgList) {
        orgList.forEach((org) => {
          const index = this.receiveData[type].receiveConfig.findIndex((row) => row.orgCode === org.orgCode);
          if (index === -1) {
            this.receiveData[type].receiveConfig.push({ orgCode: org.orgCode, orgName: org.orgName, peopleList: [] });
          }
        });
      }
    },
    /**
     * 移除或清空或更改任务后，将该任务关联的组织机构删除
     * 如何其他任务关联的组织机构 也存在， 则不删除
     * @param {*} type 消息类型
     * @param {*} data taskIds实际渲染的 formData
     */
    delReceiveOrgMx(type, data) {
      // 全都清空的情况
      if (data[type].taskIds.every((item) => !item)) {
        this.receiveData[type].receiveConfig = [];
        return;
      }

      // 获取 对应的组织列表
      this.getReceiveOrgByTask(type);

      let delIndex = this.taskIdsInfoMx[type].delIndex;
      let delTaskId = this.taskIdsInfoMx[type].taskIds[delIndex];
      let delOrgs = this.orgByTaskMx[delTaskId] || [];
      if (delOrgs.length > 0) {
        let allOrgCodes = [];
        data[type].taskIds.forEach((item) => {
          if (item && this.orgByTaskMx[item]) {
            this.orgByTaskMx[item].forEach((orgItem) => {
              if (!allOrgCodes.includes(orgItem.orgCode)) {
                allOrgCodes.push(orgItem.orgCode);
              }
            });
          }
        });
        let receiveConfig = this.$util.common.deepCopy(this.receiveData[type].receiveConfig);
        this.receiveData[type].receiveConfig = receiveConfig.filter((item) => allOrgCodes.includes(item.orgCode));
      }
    },
    // 根据已选择的 检测任务，获取到 组织列表
    getReceiveOrgByTask(type) {
      this.taskIdsInfoMx[type].taskIds.forEach((item) => {
        if (item && (!this.orgByTaskMx[item] || this.orgByTaskMx[item].length === 0)) {
          const task = this.listTaskSchemes.find((row) => row.id === item);
          if (task) {
            const orgList = this.getOrgCodeMx(task.regionCode);
            this.$set(this.orgByTaskMx, item, orgList);
          } else {
            this.$set(this.orgByTaskMx, item, null);
          }
        } else if (!item) {
          this.$set(this.orgByTaskMx, item, null);
        }
      });
    },
    /**
     * 移除或清空或更改任务前，存储 相关信息
     * @param {*} type 消息类型
     * @param {*} taskIds 检测任务列表
     * @param {*} index 当前操作对应的检测任务列表索引
     *
     */
    setTaskIdsInfoMx(type, taskIds, index) {
      this.$set(this.taskIdsInfoMx, type, {
        taskIds: this.$util.common.deepCopy(taskIds), // 移除前 的 检测任务列表
        delIndex: index, // 当前要移除的 检测任务 索引
      });
    },
    /**
     * 由于后端返回的receiveConfig是由每个人为维度的一维数组
     * 前端页面则是由每个组织机构为维度的数组
     * 所以 {保存} 时要单独处理此字段
     */
    dealReceiveCofingSaveMx() {
      Object.keys(this.receiveData).forEach((key) => {
        this.formData[key].receiveConfig = [];
        this.deletePeople(key);
        this.receiveData[key].receiveConfig.forEach((row) => {
          row.peopleList.forEach(({ username, name, id, phone, orgCode, orgName }) => {
            this.formData[key].receiveConfig.push({
              username,
              name,
              id,
              phone,
              orgCode: orgCode,
              orgName: orgName,
              orgCodeByAttach: row.orgCode,
              orgCodeByAttachName: row.orgName,
            });
          });
        });
      });
    },
    /**
     * 当后端返回的taskIds 为   ---》 检测任务id@指标id1_指标id2 ，即 ['192a08e67e4b11ec81330242ac11000e@indexId1_indexId2', '192a08e67e4b11ec81330242ac11000e@indexId1_indexId2', ...]
     * 所以 {保存} 时要单独处理此字段
     */
    dealTaskIdsSaveMx() {
      Object.keys(this.receiveData).forEach((key) => {
        let taskArr = [];
        this.receiveData[key].taskIds.forEach((item) => {
          if (item) {
            taskArr.push(
              `${item}${this.receiveData[key].indexIds?.[item] ? `@${this.receiveData[key].indexIds[item]}` : ''}`,
            );
          }
        });
        this.formData[key].taskIds = taskArr;
      });
    },
    /**
     * 过滤标记删除的数据重新赋值
     * @param {*} type
     */
    deletePeople(type) {
      this.receiveData[type].receiveConfig.forEach((row) => {
        const peopleList = row.peopleList.filter((rw) => {
          return !rw.isDelete;
        });
        row.peopleList = peopleList;
      });
    },
    /**
     * 由于后端返回的receiveConfig是由每个人为维度的一维数组
     * 前端页面则是由每个组织机构为维度的数组
     * 所以 {回显} 时要单独处理此字段
     */
    dealReceiveCofingEchoMx() {
      Object.keys(this.formData).forEach((key) => {
        this.formData[key].receiveConfig.forEach((row) => {
          const receiveOrg = this.receiveData[key].receiveConfig.find((rw) => rw.orgCode === row.orgCodeByAttach);
          if (receiveOrg) {
            receiveOrg.peopleList.push(row);
          } else {
            this.receiveData[key].receiveConfig.push({
              orgCode: row.orgCodeByAttach,
              orgName: row.orgCodeByAttachName,
              peopleList: [row],
            });
          }
        });
        if (this.formData[key].taskIds.length === 0) {
          this.formData[key].taskIds.push('');
        }
      });
      this.initializedDataMx = this.$util.common.deepCopy(this.formData);
      this.initializedReceiveDataMx = this.$util.common.deepCopy(this.receiveData);
    },
    /**
     * 当后端返回的taskIds 为   ---》 检测任务id@指标id1_指标id2 ，即 ['192a08e67e4b11ec81330242ac11000e@indexId_indexId2', '192a08e67e4b11ec81330242ac11000e@indexId_indexId2', ...]
     * 所以 {回显} 时要单独处理此字段
     */
    dealTaskIdsMx() {
      Object.keys(this.formData).forEach((key) => {
        // 检测任务
        this.formData[key].taskIds.forEach((item) => {
          if (item) {
            let taskArr = item.split('@');
            this.receiveData[key].taskIds.push(taskArr[0]);
            this.$set(this.receiveData[key].indexIds, taskArr[0], taskArr[1] || '');
          }
        });
        if (this.receiveData[key].taskIds.length === 0) {
          this.receiveData[key].taskIds.push('');
        }
      });
      this.initializedDataMx = this.$util.common.deepCopy(this.formData);
      this.initializedReceiveDataMx = this.$util.common.deepCopy(this.receiveData);
    },
    /**
     * 动态生成tableColumns中的展开项
     * 动态控制列表中的是否可以编辑状态
     * @param {*} type
     */
    setTableColumns(type) {
      const tableColumns = this.$util.common.deepCopy(this.tableColumns);
      const column = tableColumns.find((row) => row.type === 'expand');
      const isEdit = this.formData[type].markEnable === 1 && this.isEdit;
      this.$set(column, 'render', (h, params) => {
        return h(expandRow, {
          props: {
            row: params.row,
            isEdit: isEdit,
          },
          on: {
            /**
             * 由于iview table type为expand的column无法实现双向数据绑定
             * 这里由expand render中的dom更改数据后抛出事件
             * 找到接收人中的对应的数据重新赋值
             */
            changePhone: (item) => {
              const org = this.receiveData[type].receiveConfig.find((row) => {
                return row.orgCode === item.orgCodeByAttach;
              });
              const index = org.peopleList.findIndex((row) => {
                return row.id === item.id;
              });
              org.peopleList[index] = item;
            },
            /**
             * 删除时如果直接改变receiveData中的值会导致dom更新从而expand展开的数据收起
             * 这里需要增加isDelete字段来标记删除数据
             * 当调用dealReceiveCofingSaveMx方法时过滤掉标记删除的数据保存
             * @param {*} item
             */
            deleteRow: (item) => {
              const org = this.receiveData[type].receiveConfig.find((row) => {
                return row.orgCode === item.orgCodeByAttach;
              });
              const index = org.peopleList.findIndex((row) => {
                return row.id === item.id;
              });
              org.peopleList[index].isDelete = true;
            },
          },
        });
      });
      return tableColumns;
    },
    async saveMx() {
      //联系人未输入手机号码，禁止提交
      if (!this.checkSmsReceiveHasPhoneMx()) {
        this.$Message.error('您选择的通知对象未填入联系电话，无法以短信方式通知');
        return;
      }
      try {
        const res = await this.$http.post(notification.updateConfig, this.formData);
        this.$Message.success(res.data.msg);
        this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
    resetMx() {
      this.formData = this.$util.common.deepCopy(this.initializedDataMx);
    },
    updateInitialMx() {
      this.initializedDataMx = this.$util.common.deepCopy(this.formData);
    },
    /**
     * 提交前检测
     * 通知类型勾选了短信通知，但联系人未输入手机号码，则禁止提交
     * 此formData是字典类型数据，如{0000:{},1111:{}},单个对象类型则另作校验
     * return 全有手机号true 一个或多个没有false
     */
    checkSmsReceiveHasPhoneMx() {
      //检查通知对象是否都有手机号
      const inner_allhavePhone = () => {
        //没有手机号的收件人个数，最终计算出的数量为0代表全都有手机号或者不用管有没有手机号
        let noPhoneCount = 0;
        if (!Object.keys(this.formData)) {
          return true;
        }
        Object.keys(this.formData).forEach((item) => {
          //不是通用参数(通用参数template属性为空)&&通知类型没有选中短信通知,则不校验
          if (item != '0000' && !this.formData[item].template.includes('sms')) {
            return;
          }
          //状态为未激活状态，则不校验
          if (this.formData[item].markEnable == 0) {
            return;
          }
          //不需要选择联系人或联系人为空时候，则不校验
          if (!this.formData[item].receiveConfig.length) {
            return;
          }
          //发现一个没有手机号的,计数器增加
          this.formData[item].receiveConfig.forEach((receiver) => {
            if (!receiver.phone) {
              noPhoneCount++;
            }
          });
        });
        //收件人是否全都有手机号，若noPhoneCount === 0全有则返回true
        return noPhoneCount === 0 ? true : false;
      };
      let bool = inner_allhavePhone();
      if (!bool) {
        return false;
      } else {
        return true;
      }
    },
  },
  computed: {
    ...mapGetters({
      initialOrgList: 'common/getInitialOrgList',
    }),
    /**
     * 检测任务 已存在则禁用
     * @param {*} id 检测任务id
     * @param {*} type 消息类型
     * @param {*} dataName taskIds实际渲染的formData 字段名
     */
    getTaskIdsDisabled() {
      return (id, type, dataName) => {
        return this[dataName][type].taskIds ? this[dataName][type].taskIds.includes(id) : false;
      };
    },
  },
};
export default mixin;
