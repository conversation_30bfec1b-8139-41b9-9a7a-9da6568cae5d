<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles">
    <div class="model-wrapper">
      <div class="left-content">
        <p class="content-title base-text-color mb-sm">
          所有场所列表（
          <span class="font-red">{{ tableData.length }}</span> )条
        </p>
        <ui-search-tree
          class="padding20"
          placeholder="请输入行政区域名称或行政区域编码"
          node-key="orgCode"
          :max-height="650"
          :tree-data="treeData"
          :default-props="defaultProps"
          :default-keys="defaultExpandedKeys"
          :current-node-key="getDefaultSelectedOrg.orgCode"
          @selectTree="selectTree"
        >
        </ui-search-tree>
      </div>
      <div class="middle-content">
        <div class="search-box content-title">
          <ui-label label="关键词" :width="60" class="inline search-input">
            <Input
              class="width-lg"
              suffix="ios-search"
              v-model="searchData.keyWord"
              placeholder="请输入地名名称/编码/俗称"
              @on-enter.native="searchkeyword"
              >></Input
            >
            <div class="ml-lg">
              <Button type="primary" class="mr-sm" @click="searchkeyword"> 查询 </Button>
              <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, searchkeyword)"> 重置 </Button>
            </div>
          </ui-label>
          <!-- <Checkbox v-model="allDeviceChecked" class="mr-sm" @on-change="checkAll">
            &nbsp;全部
          </Checkbox> -->
        </div>
        <ui-table
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :minus-height="minusTable"
          :loading="loading"
          @selectTable="selectLeftTable"
          @oneSelected="oneSelected"
          @cancelSelectTable="cancelSelectTable"
          @onSelectAllTable="onSelectAllTable"
          @cacelAllSelectTable="cacelAllSelectTable"
        >
        </ui-table>
        <ui-page
          class="page menu-content-background"
          :page-data="pageData"
          :simple-page="true"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </div>
      <div class="arrow-box">
        <double-arrow></double-arrow>
      </div>
      <div class="right-content">
        <div class="over-flow select-title">
          <div class="base-text-color fr">
            已选择场所（<span class="font-red">{{ chooseTableData.length }}</span
            >）
            <Button type="primary" @click="removeAllDevice">全部移除</Button>
          </div>
        </div>
        <ui-table
          class="ui-table"
          :table-columns="rightTableColumns"
          :table-data="chooseTableData"
          :minus-height="minusTable"
          :special-jsx="specialJSX"
          @selectTable="selectTable"
        >
          <template #actionSlot="{ row, index }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-yichu1"
              content="移除"
              @click.native="removeOneDevice(row, index)"
            ></ui-btn-tip>
            <!-- <i
              class="icon-font f-14 icon-yichu-01 icon-operation-color mr-sm"
              @click="removeOneDevice(row, index)"
            ></i> -->
          </template>
        </ui-table>
      </div>
    </div>
    <template #footer>
      <Button type="primary" @click="query" class="plr-30">确 定</Button>
      <Button @click="visible = false" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>
<style lang="less" scoped>
[data-theme='dark'] {
  .model-wrapper {
    .left-content,
    .middle-content,
    .right-content {
      border: 1px solid #0068b7;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .model-wrapper {
    .left-content,
    .middle-content,
    .right-content {
      border: 1px solid #d8d8d8;
    }
  }
}

.model-wrapper {
  display: flex;
  background-color: var(--bg-content);
  .content-title {
    height: 65px;
    line-height: 65px;
    // border: 1px solid #0068B7;
    // border-top-color: transparent;
    // border-left-color: transparent;
    padding-left: 20px;
  }
  .left-content {
    width: 350px;
    border-right-color: transparent;
  }
  .middle-content,
  .right-content {
    width: 715px;
    position: relative;
  }
  .middle-content {
    .search-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .search-input {
      position: relative;
      display: flex;
      .icon-sousuo {
        position: absolute;
        right: 10px;
      }
    }
    .ui-table {
      // width: 100%;
    }
  }
  .right-content {
    .select-title {
      padding: 16px 20px;
    }
  }
  .padding20 {
    padding: 0 20px;
  }
  .font-green-color {
    color: var(--color-bluish-green-text);
  }
  .arrow-box {
    height: 600px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
}
</style>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters, mapActions } from 'vuex';
const tableColumns = [
  { type: 'selection', width: 50, align: 'center' },
  { title: '序号', width: 50, type: 'index', align: 'center' },
  { title: '场所名称', key: 'placeName', align: 'left', tooltip: true },
  { title: '场所类别', key: 'placeTypeName', align: 'left' },
  { title: '状态', key: 'statusName', align: 'left' },
  { title: '所属区域', key: 'regionName', align: 'left', tooltip: true },
];
const rightTableColumns = [
  { title: '序号', width: 50, type: 'index', align: 'center' },
  { title: '场所名称', key: 'placeName', align: 'left' },
  { title: '场所类别', key: 'placeTypeName', align: 'left' },
  { title: '所属区域', key: 'regionName', align: 'left', tooltip: true },
  {
    title: '操作',
    width: 60,
    key: 'action',
    slot: 'actionSlot',
    align: 'center',
    className: 'table-action-padding',
  },
];
let specialJSX = '<span class="font-active-color f-14">已选择全部数据</span>';
export default {
  data() {
    return {
      specialJSX: null,
      defaultProps: {
        label: 'regionName',
        children: 'children',
      },
      tableColumns: Object.freeze(tableColumns),
      rightTableColumns: Object.freeze(rightTableColumns),
      tableData: [],
      chooseTableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      visible: false,
      styles: {
        width: '9rem',
      },
      loading: false,
      minusTable: 400,
      searchData: {
        keyWord: '',
        regionCode: null,
        pageNumber: 1,
        pageSize: 20,
      },
      connectingOptions: {
        width: '1.2%',
        height: '0.04rem',
        top: '50%',
        left: '13.6%',
      },
      allDeviceChecked: false,
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
    }),
    checkAll(val) {
      if (val) {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', true);
          this.$set(item, '_disabled', true);
        });
        this.chooseTableData = [];
        this.specialJSX = specialJSX;
      } else {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', false);
          this.$set(item, '_disabled', false);
        });
        this.specialJSX = null;
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    removeOneDevice(row, index) {
      this.chooseTableData.splice(index, 1);
      let deleteIndex = this.tableData.findIndex((item) => {
        return item.id === row.id;
      });
      this.tableData[deleteIndex]._checked = false;
    },
    removeAllDevice() {
      this.chooseTableData = [];
      this.tableData.forEach((item) => {
        item._checked = false;
      });
    },
    selectLeftTable() {},
    handleData() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        if (selectDataObject.hasOwnProperty(row.id)) {
          this.$set(row, '_checked', true);
        } else {
          this.$set(row, '_checked', false);
        }
      });
    },
    oneSelected(selection, row) {
      this.chooseTableData.push(row);
      row._checked = true;
    },
    cancelSelectTable(selection, row) {
      let rowId = this.tableData.find((item) => {
        return item.id === row.id;
      });
      this.chooseTableData = this.chooseTableData.filter((item) => item.id !== rowId.id);
    },
    onSelectAllTable() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        //this.$set(item, "_checked", true);
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        // 没有就push进去
        if (!selectDataObject.hasOwnProperty(row.id)) {
          this.chooseTableData.push(row);
        }
      });
    },
    cacelAllSelectTable() {
      let tableDataIds = this.tableData.map((item) => {
        return item.id;
      });
      this.chooseTableData = this.chooseTableData.filter((row) => {
        return !tableDataIds.includes(row.id);
      });
    },
    selectTable(selection) {
      this.checkedData = selection.map((row) => {
        return row.id;
      });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.pageNumber = val;
      this.initAllData();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.pageSize = val;
      this.initAllData();
    },
    selectTree(data) {
      this.searchData.regionCode = data.regionCode;
      this.initAllData();
    },
    handleRelativeName() {
      if (!this.choosedCategory) return;
      if (this.choosedCategory.hasOwnProperty('name')) {
        return this.choosedCategory.name;
      } else if (this.choosedCategory.hasOwnProperty('deviceTagName')) {
        return this.choosedCategory.deviceTagName;
      } else {
        return this.choosedCategory.areaTreeName;
      }
    },
    searchkeyword() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initAllData();
    },
    // 查询设备数据列表
    async initAllData() {
      this.loading = true;
      this.tableData = [];
      try {
        let res = await this.$http.post(equipmentassets.placeManagerList, this.searchData);
        this.tableData = res.data.data.entities;
        this.handleData();
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    query() {
      this.$emit('putChoose', this.chooseTableData);
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        this.allDeviceChecked = false;
        this.searchData = {
          keyWord: '',
          regionCode: null,
          pageNumber: 1,
          pageSize: 20,
        };
        this.pageData = {
          pageNum: 1,
          pageSize: 20,
          totalCount: 0,
        };
        this.chooseTableData = [];
        await this.setOrganizationList();
        this.selectTree(this.getDefaultSelectedOrg);
      }
    },
    choosedCategory() {
      //console.log('我选中的目录需要关联设备', val)
    },
  },
  computed: {
    isView() {
      return this.modalAction.action === 'view';
    },
    isAdd() {
      return this.modalAction.action === 'add';
    },
    isEdit() {
      return this.modalAction.action === 'edit';
    },
    ...mapGetters({
      treeData: 'common/getAreaList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedArea',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    modalAction: {
      default: () => {
        return { title: '上传场所', action: 'add' };
      },
    },
    choosedCategory: {
      default: () => {},
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DoubleArrow: require('@/components/double-arrow.vue').default,
  },
};
</script>
