<template>
  <div class="tabs">
    <Tabs :value="activeTab" @on-click="tabsChange">
      <TabPane :label="item.label" :name="item.value" v-for="(item, index) of tabsList" :key="index"></TabPane>
    </Tabs>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    tabsList: {
      default: () => [],
    },
  },
  data() {
    return {
      activeTab: null,
    };
  },
  mounted() {
    this.tabsChange(this.tabsList[0].value);
  },
  methods: {
    tabsChange(name) {
      this.$emit('tabsChange', name);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .tabs {
    background: #f9f9f9;
    @{_deep}.ivu-tabs-nav {
      color: rgba(0, 0, 0, 0.8);
    }
  }
}
.tabs {
  background: #0a2754;
  height: 55px;
  line-height: 55px;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  @{_deep}.ivu-tabs-nav {
    color: #56789c;
    font-size: 16px;
    height: 55px;
    line-height: 40px;
    .ivu-tabs-ink-bar {
      height: 4px;
    }
  }
  @{_deep}.ivu-tabs-bar {
    margin-bottom: 0;
    border: 0;
  }
}
</style>
