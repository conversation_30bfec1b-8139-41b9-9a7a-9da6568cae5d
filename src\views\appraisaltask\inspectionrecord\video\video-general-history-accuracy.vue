<template>
  <div class="height-full">
    <listPage :listObj="listObj" :taskObj="taskObj" :currentTree="currentTree" @refeshCount="refeshCount">
      <template #storageType="{ row }">
        {{ storageTypeVal(row) }}
      </template>
      <template #outcome="{ row }">
        {{ outcomeVal(row) }}
      </template>
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
    </listPage>
  </div>
</template>

<script>
import api from '@/config/api/inspectionrecord';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'commonPage',
  components: {
    listPage: require('./list').default,
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {},
    },
    countInfo: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      bigPictureShow: false,
      imgList: [],
      listObj: {
        abnormalCount: [
          {
            title: '采集监控设备总数',
            count: '0',
            icon: 'icon-lishishipinluxiangwanzhengxingjiance',
          },
          { title: '检测数量', count: '0', icon: 'icon-lishishipinluxiangwanzhengxingjiance' },
          {
            title: '历史视频录像完整设备数量',
            count: '0',
            icon: 'icon-lishishipinluxiangwanzhengxingjiance',
          },
          {
            title: '无法检测设备数量',
            countKey: '0',
            icon: 'icon-lishishipinluxiangwanzhengxingjiance',
          },
        ],
        columns: [
          { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 150 },
          { title: '存储类型', slot: 'storageType' },
          { title: '检测结果', slot: 'outcome' },
          { title: '录像完整天数', key: 'completeDay', width: '170' },
          { title: '录像缺失天数', key: 'hiatusDay', tooltip: 'true' },
          { title: '检测时间', key: 'startTime', width: '170' },
        ],
        loadData: (parameter) => {
          const { indexId, batchId, regionCode: orgCode } = this.taskObj;
          const params = {
            indexId,
            batchId,
            ...parameter,
            orgCode: parameter.orgCode ? parameter.orgCode : orgCode,
          };
          return this.$http.post(api.queryEvaluationVideoPageList, params).then((res) => {
            return res.data;
          });
        },
      },
      videoStyles: {
        width: '5rem',
      },
      videoOptions: {},
      videoVisible: false,
      videoUrl: '',
      playDeviceCode: '',
    };
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    storageTypeVal(row) {
      switch (row.storageType) {
        case 1:
          return '设备存储';
        case 2:
          return '中心存储';
        default:
          return '未知';
      }
    },
    outcomeVal(row) {
      switch (row.outcome) {
        case '1':
          return '正常';
        case '2':
          return '异常';
        default:
          return '无法检测';
      }
    },
    refeshCount(orgCode) {
      this.$emit('refeshCount', orgCode);
    },
  },
  async mounted() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    setTimeout(() => {
      this.listObj.abnormalCount[0].count = this.countInfo.deviceCount;
      this.listObj.abnormalCount[1].count = this.countInfo.evaluatingCount;
      this.listObj.abnormalCount[2].count = this.countInfo.evaluatingFailedCount;
      this.listObj.abnormalCount[3].count = this.countInfo.unableEvaluatingCount;
    }, 500);
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    countInfo() {
      this.listObj.abnormalCount[0].count = this.countInfo.deviceCount;
      this.listObj.abnormalCount[1].count = this.countInfo.evaluatingCount;
      this.listObj.abnormalCount[2].count = this.countInfo.evaluatingFailedCount;
      this.listObj.abnormalCount[3].count = this.countInfo.unableEvaluatingCount;
    },
  },
};
</script>

<style lang="less" scoped>
.btn {
  margin: 0 6px;
}
.auto-fill {
  height: 100%;
}
.video-player {
  @{_deep} .ivu-modal-body {
    height: 530px;
    display: flex;
    flex-direction: column;
  }
}
</style>
