import request from "@/libs/request";
import {
  mapService,
  service,
  icbdService,
  holographicArchives,
  manager,
} from "./Microservice";
let qs = require("qs");

// 历史记录-新增
export function addRecord(data) {
  return request({
    url: mapService + "/historical/record/add",
    method: "post",
    data,
  });
}
// 历史记录-查询全部
export function getAllSearchRecord(data) {
  return request({
    url: mapService + "/historical/record/getAll",
    method: "post",
    data,
  });
}
// 历史记录-删除
export function deleteRecord(data) {
  return request({
    url: mapService + "/historical/record/removeByUserId",
    method: "delete",
    data,
  });
}
// 作战记录-新增
export function addCombatRecord(data) {
  return request({
    url: mapService + "/combat/record/add",
    method: "post",
    data,
  });
}
// 作战记录-查询
export function getAllCombatRecord(data) {
  return request({
    url: mapService + "/combat/record/getAll",
    method: "post",
    data,
  });
}
// 作战记录-删除
export function deleteCombat({ id }) {
  return request({
    url: mapService + `/combat/record/remove/${id}`,
    method: "delete",
  });
}

// 作战记录-编辑
export function updateCombat(data) {
  return request({
    url: mapService + "/combat/record/update",
    method: "put",
    data,
  });
}
// 作战记录-详细信息
export function wifiRecordSearch({ id }) {
  return request({
    url: mapService + `/combat/record/view/${id}`,
    method: "get",
  });
}
// 普通搜索结果统计
export function getDataTotal(data) {
  return request({
    url: mapService + `/business/map/getDataTotal`,
    method: "post",
    data,
  });
}
// 人像档案搜索
export function queryFaceRecordSearch(data) {
  return request({
    url: mapService + `/business/map/queryFaceRecordSearch`,
    method: "post",
    data,
  });
}
// 车辆档案搜索
export function queryVehicleRecordSearch(data) {
  return request({
    url: mapService + `/business/map/queryVehicleRecordSearch`,
    method: "post",
    data,
  });
}
// wifi档案搜索
export function queryWifiRecordSearch(data) {
  return request({
    url: mapService + `/business/map/queryWifiRecordSearch`,
    method: "post",
    data,
  });
}
// RFID档案搜索
export function queryRfidRecordSearch(data) {
  return request({
    url: mapService + `/business/map/queryRfidRecordSearch`,
    method: "post",
    data,
  });
}
// 电围档案查询
export function queryElectricCircumferenceRecordSearch(data) {
  return request({
    url: mapService + `/business/map/queryElectricCircumferenceRecordSearch`,
    method: "post",
    data,
  });
}
// 设备查询
export function queryDeviceRecordSearch(data) {
  return request({
    url: mapService + `/business/map/queryDeviceRecordSearch`,
    method: "post",
    data,
  });
}
// 查询人员档案详情
export function getPersonInfoByPersonId(vid) {
  let data = qs.stringify(vid);
  return request({
    url: mapService + `/business/map/getPersonInfoByPersonId`,
    method: "post",
    data,
  });
}
// 查询车辆档案详情
export function getCarInfoByCarNo(carNo) {
  let data = qs.stringify(carNo);
  return request({
    url: mapService + `/business/map/getCarInfoByCarNo`,
    method: "post",
    data,
  });
}
// 查询电围档案详情
export function getBaseStationInfoBCarNo(device) {
  let data = qs.stringify(device);
  return request({
    url: mapService + `/business/map/getBaseStationInfoBCarNo`,
    method: "post",
    data,
  });
}
// 查询rfdi档案
export function getRfdiInfoByDeviceId(device) {
  let data = qs.stringify(device);
  return request({
    url: mapService + `/business/map/getRfdiInfoByDeviceId`,
    method: "post",
    data,
  });
}
// 查询wifi档案
export function getWifiInfoByDeviceId(device) {
  let data = qs.stringify(device);
  return request({
    url: mapService + `/business/map/getWifiInfoByDeviceId`,
    method: "post",
    data,
  });
}
// 根据deviceId查询电围详情
export function getBaseStationDetailByDeviceId(data) {
  return request({
    url: mapService + `/business/map/getBaseStationDetailByDeviceId`,
    method: "post",
    data,
  });
}
// 根据deviceId查询设备详情
export function getDeviceDetailByDeviceId(data) {
  return request({
    url: mapService + `/business/map/getDeviceDetailByDeviceId`,
    method: "post",
    data,
  });
}

//
export function getDeviceByIds(ids) {
  return request({
    url: mapService + `/business/map/getDeviceByIds/${ids}`,
    method: "get",
  });
}

// 根据deviceId查询RFID详情
export function getRfdiDetailByDeviceId(data) {
  return request({
    url: mapService + `/business/map/getRfdiDetailByDeviceId`,
    method: "post",
    data,
  });
}
// 根据deviceId查询WIFI详情
export function getWifiDetailByDeviceId(data) {
  return request({
    url: mapService + `/business/map/getWifiDetailByDeviceId`,
    method: "post",
    data,
  });
}

// 框选搜索
export function frameSelectionSearch(data) {
  return request({
    url: mapService + `/business/map/search`,
    method: "post",
    data,
  });
}
// 中心点搜索
export function searchByCenter(data) {
  return request({
    url: mapService + `/business/map/searchByCenter`,
    method: "post",
    data,
  });
}
// 根据 图层类型查询图层点位信息(设备)
export function getMapLayerByType(data) {
  return request({
    url: mapService + `/business/map/getMapLayerByType`,
    method: "post",
    data,
  });
}
// 根据 图层类型查询图层点位信息(场所)
export function queryPlaceInfoPageList(data) {
  return request({
    url: mapService + `/placeInfo/queryPlaceInfoPageList`,
    method: "post",
    data,
  });
}
// 根据查询地图图层点位信息(设备)
export function queryDeviceList(data) {
  return request({
    url: service + `/device/queryDeviceList`,
    method: "post",
    data,
  });
}

// 根据查询地图图层点位信息(设备)--role
export function getSimpleDeviceList(data) {
  return request({
    url: service + `/device/getSimpleDeviceList`,
    method: "post",
    data,
  });
}
// 新增分组
export function batchSave(data) {
  return request({
    url: icbdService + `/group/videoDeviceGroup/batchAddVideoGroupInfo`,
    method: "post",
    data,
  });
}

// 南京市AOI
export function getaoi(data) {
  return request({
    url: holographicArchives + `/placeArchive/pageList`,
    method: "post",
    data,
  });
}
// 场所详情
export function placeDetail(id) {
  return request({
    url: holographicArchives + "/placeArchive/view/" + id,
    method: "get",
  });
}
// 根据场景id获取周边设备信息
export function getPlaceDeviceInfo(id) {
  return request({
    url: holographicArchives + "/placeArchive/getPlaceDeviceInfo/" + id,
    method: "get",
  });
}

// 根据场所id获取周边设备信息 v2版本
export function getPlaceDeviceInfoV2(id) {
  return request({
    url: holographicArchives + "/placeArchive/v2/getDeviceById/" + id,
    method: "get",
  });
}

// 修改场所
export function updatePlaceArchiveModify(data) {
  return request({
    url: holographicArchives + "/placeArchive/modify",
    method: "post",
    data,
  });
}

// 新增场所
export function addPlaceArchiveModify(data) {
  return request({
    url: holographicArchives + "/placeArchive/add",
    method: "post",
    data,
  });
}

// 删除场所
export function deletePlaceArchiveModify(ids) {
  return request({
    url: holographicArchives + "/placeArchive/remove/" + ids,
    method: "delete",
  });
}
// 查询场所树列表
export function queryPlaceTreeList(data) {
  return request({
    url: holographicArchives + `/placeArchive/queryPlaceTreeList`,
    method: "post",
    data,
  });
}

// 根据二级分类统计场所数量
export function countBySecondLevel() {
  return request({
    url: holographicArchives + `/placeArchive/countBySecondLevel`,
    method: "get",
  });
}
// 条件查询场所资源列表
export function queryPlaceAndKeys(data) {
  return request({
    url: mapService + `/business/map/queryPlaceAndKeys`,
    method: "post",
    data,
  });
}

// 时空分析分享
export function receordShare(data) {
  return request({
    url: mapService + `/combat/record/share`,
    method: "post",
    data,
  });
}

// 时空分析导出
export function exportExcel(data) {
  return request({
    url: mapService + `/combat/record/exportExcel`,
    method: "post",
    data,
  });
}
// 地图书签
// 时空分析地图书签信息配置新增
export function bookmarkAdd(data) {
  return request({
    url: manager + `/bookmark/add`,
    method: "post",
    data,
  });
}
// 设置/取消默认
export function enableMapSetting(data) {
  return request({
    url: manager + `/bookmark/enableMapSetting`,
    method: "post",
    data,
  });
}
// 查询默认
export function getMapSetting(data) {
  return request({
    url: manager + `/bookmark/getMapSetting`,
    method: "get",
  });
}
// 查询地图书签列表
export function bookmarkList(data) {
  return request({
    url: manager + `/bookmark/queryList`,
    method: "get",
  });
}
// 时空分析地图书签信息配置删除
export function bookmarkDel(ids) {
  return request({
    url: manager + `/bookmark/remove/${ids}`,
    method: "delete",
  });
}
// 时空分析地图书签信息配置编辑
export function bookmarkUpdate(data) {
  return request({
    url: manager + `/bookmark/update`,
    method: "put",
    data,
  });
}
// 获取时空分析地图书签信息配置详细信息
export function bookmarkView(id) {
  return request({
    url: manager + `/bookmark/view/${id}`,
    method: "get",
  });
}
// 查询兴趣点分页列表
export function getPointList(data) {
  return request({
    url: manager + `/interest/point/pageList`,
    method: "post",
    data,
  });
}
// 实时轨迹检索
export function getGpsRealTime(gpsId) {
  return request({
    url: service + `/gps/realTimeSearch/${gpsId}`,
    method: "get",
  });
}

// 根据组织ID获取子孙级别GPS数据
export function getPosterityGps() {
  return request({
    url: service + `/gps/getPosterityGps/0`,
    method: "get",
  });
}
