<template>
  <div class="faceofflinestatistics-container auto-fill">
    <statistics indexId="2008" :switchData="switchData">
      <template #title> 人脸卡口离线情况统计 </template>
    </statistics>
  </div>
</template>

<script>
export default {
  name: 'faceofflinestatistics',
  components: {
    statistics: require('@/views/governanceevaluation/videostreamstatistics/components/statistics.vue').default,
  },
  props: {},
  data() {
    return {
      switchData: [
        { label: '全量人脸卡口', value: 0 },
        { label: '重点人脸卡口', value: 1 },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.faceofflinestatistics-container {
  width: 100%;
  height: 100%;
  background: var(--bg-content);
  padding: 10px 20px 20px 20px;
}
</style>
