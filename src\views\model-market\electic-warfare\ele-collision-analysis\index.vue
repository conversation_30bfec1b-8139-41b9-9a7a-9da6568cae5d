<!--
    * @FileDescription: 碰撞分析
    * @Author: H
    * @Date: 2024/06/27
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-29 16:32:05
 -->
<template>
  <!-- <div class="container">碰撞分析</div> -->
  <div class="time-space-colli">
    <ui-loading v-if="loading"></ui-loading>
    <mapCustom
      ref="mapBase"
      :sectionName="mapType.sectionName"
      :allCameraList="allCameraList"
      @selectData="selectData"
      :mapType="mapType.seleType"
      :crashType="crashType"
      :selectionBox="true"
      cutIcon="map"
      :isEleCrash="true"
      @chooseMapItemObj="chooseMapItemObj"
    />

    <!-- 左侧搜索 -->
    <leftBox
      ref="leftBox"
      @search="handleSearch"
      @areaClick="areaClick"
      @selectDraw="selectDraw"
      @checkChange="checkChange"
      @deleDraw="deleDraw"
      @cancelDraw="cancelDraw"
      :typeName="mapType.mapName"
    ></leftBox>
    <!-- 右侧列表 -->
    <!-- <rightBox 
            ref="rightBox" 
            v-show="rightBoxShow" 
            :tablist="tablists"
            @spaceTime="handleSpaceTime" 
            @cancel="handleCancel"></rightBox> -->
    <!-- 对象详情 -->
    <detailsBox
      ref="detailsBox"
      :detailList="detailList"
      :pageParams="pageParams"
      @change-page="changePage"
      @changeItem="changeItem"
      @cancel="handleCancel"
      v-if="detailsShow"
    >
    </detailsBox>
    <!-- <detailsModal
      ref="detailsModal"
      v-if="detailsModalShow"
      :captureList="captureList"
      @close="close"
    >
    </detailsModal> -->
    <informationCollection
      ref="informationCollection"
      v-if="detailsModalShow"
      :captureList="captureList"
      @close="close"
    >
    </informationCollection>
    <!-- <detailsModalMul
      ref="detailsModalMul"
      v-if="tracePointVisible"
      :listData="listData"
      @close="close2"
    >
    </detailsModalMul> -->
    <informationCollectionMul
      ref="informationCollectionMul"
      v-if="tracePointVisible"
      :listData="listData"
      @close="close2"
    >
    </informationCollectionMul>
    <frame-selection-movebox
      id="selection-movebox"
      @mousedown="handleMouseDown($event)"
      v-if="selectMoveBox"
      :selectDeviceList="selectDeviceList"
      @boxSelect="boxSelect"
      @saveSelect="handleSaveSelect"
      ref="frameSelectionMovebox"
    >
    </frame-selection-movebox>

    <!-- 进度条 -->
    <search-progress
      :progress="progress"
      :percent="percent"
      v-show="showProgress"
      :isBigProgress="true"
    ></search-progress>
  </div>
</template>
<script>
import mapCustom from "@/views/model-market/components/map/index.vue";
import { mapMutations } from "vuex";
import leftBox from "./components/leftBox.vue";
import rightBox from "./components/rightBox.vue";
import detailsBox from "./components/detailsBox.vue";
import detailsModal from "./components/detailsModal.vue";
import detailsModalMul from "./components/detailsModalMul.vue";
import informationCollectionMul from "./components/informationCollectionMul.vue";
import informationCollection from "./components/informationCollection.vue";
import {
  deviceRegion,
  baseCollideDetail,
  baseCollideList,
  getProgress,
  baseTrailSearch,
} from "@/api/modelMarket";
import frameSelectionMovebox from "../../components/map/frame-selection-movebox.vue";
import SearchProgress from "@/components/SearchProgress.vue";
import { getUUID } from "@/util/modules/common.js";
// import { myMixins } from "../../mixins/index.js";
export default {
  name: "ele-collision-analysis",
  // mixins: [myMixins],
  components: {
    mapCustom,
    leftBox,
    rightBox,
    detailsBox,
    frameSelectionMovebox,
    detailsModal,
    detailsModalMul,
    SearchProgress,
    informationCollection,
    informationCollectionMul,
  },
  props: {
    mapType: {
      type: Object,
      default: () => {
        return {
          mapName: "face",
          sectionName: "faceMap", // 模态框类型
          seleType: "Camera_Face", //框选类型
        };
      },
    },
  },
  data() {
    return {
      deviceList: [],
      drawStyle: {
        color: "#2C86F8", //颜色
        fillColor: "#2C86F8", //填充颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
        lineStyle: "#2C86F8",
        strokeColor: "#2C86F8",
        showRadius: true,
      },
      drawColor: ["#2C86F8", "#1FAF81", "#F29F4C", "#A786FF", "#48BAFF"],
      tablists: [],
      detailsList: [],
      rightBoxShow: false,
      detailsShow: false,
      mousel: 0,
      mouseT: 0,
      mouseX: 0,
      mouseY: 0,
      selectMoveBox: false,
      allCameraList: [],
      selectDeviceList: [],
      detailsModalShow: false,
      loading: false,
      showProgress: false,
      progress: 0,
      percent: 0,
      currentUUID: "",
      pageParams: {
        pageNumber: 1,
        totalCount: 0,
        pageSize: 10,
      },
      params: {
        timeSpaces: "",
      },
      detailList: [],
      currentInterval: null,
      capturePage: {
        pageNumber: 1,
        totalCount: 0,
        pageSize: 999,
      },
      captureList: [],
      listData: [],
      tracePointVisible: false,
      // 碰撞设备 暂时只碰撞电围设备 后面有更多的感知设备用 空格 往后加
      crashType: "Camera_Electric",
    };
  },
  async created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  mounted() {
    this.init();
    this.getMapLayerByTypeSite();
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    chooseMapItemObj(obj) {
      let vm = this;
      let timeSpaces = JSON.parse(this.params.timeSpaces);
      let timeSpace = timeSpaces.filter((t) => {
        return t.deviceIds.contains(obj.deviceId + "");
      });
      if (timeSpace && timeSpace.length == 1) {
        this.showCaptureList1(obj);
      } else if (timeSpace && timeSpace.length >= 2) {
        let length1 = obj.areas[0].recordNum;
        let length2 = obj.areas[1].recordNum;
        let length3 = (obj.areas[2] && obj.areas[2].recordNum) || 0;
        if (timeSpace.length == 2) {
          if (length1 == 0 || length2 == 0) {
            //只有一个区域有数据
            if (length1 == 0) {
              vm.showCaptureList1(obj, timeSpace[1]);
            } else {
              vm.showCaptureList1(obj, timeSpace[0]);
            }
          } else {
            //两个区域都有数据
            vm.showCaptureList2(obj);
          }
        } else {
          if (length1 == 0 && length2 == 0) {
            vm.showCaptureList1(obj, timeSpace[2]);
          } else if (length1 == 0 && length3 == 0) {
            vm.showCaptureList1(obj, timeSpace[1]);
          } else if (length2 == 0 && length3 == 0) {
            vm.showCaptureList1(obj, timeSpace[0]);
          } else {
            vm.showCaptureList2(obj);
          }
        }
      }
    },
    showCaptureList1(p, obj) {
      let vm = this;
      p.pageNumber = vm.capturePage.pageNumber;
      p.pageSize = vm.capturePage.pageSize;
      if (!!obj) {
        p.startTime = obj.startTime;
        p.endTime = obj.endTime;
      } else {
        let timeSpaces = JSON.parse(vm.params.timeSpaces);
        let timeSpace = timeSpaces.filter((t) => {
          return t.deviceIds.contains(p.deviceId + "");
        });
        if (timeSpace && timeSpace.length > 0) {
          p.startTime = timeSpace[0].startAbsTime;
          p.endTime = timeSpace[0].endAbsTime;
        }
      }
      delete p.areas;
      delete p.geoPoint;
      delete p.recordNum;
      delete p.lon;
      delete p.lat;
      console.log(p, "ppppppppppppp");
      baseTrailSearch(p).then(function (resp) {
        if (resp.code == 200) {
          vm.capturePage.totalCount = resp.data.total;
          vm.captureList = resp.data.entities;
          vm.detailsModalShow = true;
        }
      });
    },
    showCaptureList2(p) {
      let vm = this;
      let timeSpaces = JSON.parse(vm.params.timeSpaces);
      let group = [];
      timeSpaces.map((res, index) => {
        let obj = {
          plateNo: p.plateNo,
          startTime: res.startAbsTime,
          endTime: res.endAbsTime,
          deviceId: p.deviceId,
          pageNumber: 1,
          pageSize: 999,
        };
        group.push(obj);
      });
      Promise.all(
        group.map((node) => {
          return baseTrailSearch(node);
        })
      ).then((res) => {
        vm.listData = res.map((item, index) => {
          item.params = group[index];
          item.index = index;
          return item;
        });
        vm.tracePointVisible = true;
      });
    },
    changeItem(item) {
      this.loading = true;
      let self = this;
      let param = {
        timeSpaces: this.params.timeSpaces,
        deviceId: item.deviceId,
        search: true,
      };
      this.detailList.forEach((t) => {
        t.active = false;
      });
      item.active = true;
      baseCollideDetail(param)
        .then(function (resp) {
          if (resp.code == 200) {
            let list = resp.data;
            list.forEach((v) => {
              v.plateNo = item.plateNo;
              let node = self.allCameraList.find((p) => {
                return p.deviceGbId == v.deviceId;
              });
              if (node && node.geoPoint) {
                v.geoPoint = node.geoPoint;
              }
            });
            list = list.filter((v) => v.geoPoint);
            self.$refs.mapBase.handlerCollision2(list);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 撒点数据
    async getMapLayerByTypeSite() {
      this.siteListFlat = [];
      let roleParam = {
        // 暂时只碰撞电围设备
        dataTypeList: [8],
      };
      let { data } = await deviceRegion(roleParam);
      data.entities.forEach((item) => {
        item.mapType = item.deviceType;
      });
      this.allCameraList = data.entities;
      // 仅当撒点内容为空时，也要进行撒点初始化，不然会报错
      if(this.allCameraList) {
        this.$refs.mapBase._initSystemPoints2Map([])
      }
    },
    // 搜索
    handleSearch(params) {
      this.params.timeSpaces = params;
      this.detailsShow = true;
      this.clearMap();
      this.queryList(true);
    },
    // 取消绘制
    cancelDraw() {
      this.$refs.mapBase.cancelDraw();
    },
    //查询列表
    queryList(isSearch) {
      this.currentUUID = getUUID();
      let params = {
        timeSpaces: this.params.timeSpaces,
        search: !!isSearch,
        key: "",
        pageNumber: this.pageParams.pageNumber,
        pageSize: this.pageParams.pageSize,
      };
      if (!isSearch) params.key = this.params.key;
      params.progressId = this.currentUUID;
      this.detailList = [];
      this.progress = 0;
      this.percent = 0;
      this.showProgress = true;
      baseCollideList(params)
        .then((res) => {
          if (res.code == 200) {
            this.detailList = res.data.entities || [];
            this.pageParams.totalCount = res.data.total;
            this.params.key = res.data.key;
          }
        })
        .catch((error) => {})
        .finally(() => {
          this.showProgress = false;
          this.progress = 0;
          if (this.currentInterval) {
            clearInterval(this.currentInterval);
            this.currentInterval = null;
          }
        });
      if (this.showProgress) {
        this.queryProgress();
      }
    },
    //查询当前任务的进度
    queryProgress() {
      let that = this;
      clearInterval(that.currentInterval);
      that.currentInterval = null;
      that.currentInterval = setInterval(() => {
        getProgress({ progressId: that.currentUUID }).then((resp) => {
          if (resp.code == 200) {
            that.progress = resp.data;
            that.percent = (resp.data / 100) * 360;
          }
        });
      }, 200);
    },
    changePage(val) {
      this.pageParams.pageNumber = val;
      this.queryList();
    },
    handleReset() {
      this.detailsShow = false;
      this.rightBoxShow = false;
      this.$refs.mapBase.clearAll([1, 2]);
      this.clearMap();
    },
    checkChange(event, row, index) {
      if (event) {
        //展示
        this.$refs.mapBase.showDraw(index);
      } else {
        //隐藏
        this.$refs.mapBase.hiddenDraw(index);
      }
    },
    areaClick(item, index) {
      this.selectMoveBox = true;
      this.$nextTick(() => {
        this.$refs.frameSelectionMovebox.index = index;
        this.$refs.frameSelectionMovebox.setTime({
          startDate: item.startAbsTime,
          endDate: item.endAbsTime,
        });
        this.selectDeviceList = JSON.parse(JSON.stringify(item.deviceList));
      });
    },
    handleSaveSelect(data) {
      this.$refs.leftBox.handleUpdate(
        data,
        this.$refs.frameSelectionMovebox.index
      );
      this.selectMoveBox = false;
    },
    selectDraw(type, index, list) {
      // this.clearMap()
      list.map((item, index) => {
        this.$set(this.drawColor, index, item.color);
      });
      this.$refs.mapBase.selectDraw(type, true, index, this.drawStyle);
    },
    deleDraw(index) {
      this.$refs.mapBase.clearModelDraw(index);
    },
    selectData(value) {
      this.$refs.leftBox.handleAdd(value);
    },
    handleCancel() {
      this.detailsShow = false;
      this.$refs.mapBase.resetCollMarker();
    },
    handleSpaceTime(params, type) {
      this.detailsShow = true;
      this.rightBoxShow = false;
      this.$nextTick(() => {
        this.$refs.detailsBox.init(params, type);
      });
    },
    // 清除地图上控件
    clearMap() {
      this.$refs.mapBase.resetCollMarker();
      this.$refs.mapBase.closeMapDom();
    },
    // 拖拽
    init() {
      let box = document.querySelector(".time-space-colli");
      box.onmousemove = (event) => {
        let box = document.getElementById("selection-movebox");
        if (this.isClick) {
          let divX = event.clientX - (this.mouseX - this.mousel);
          let divY = event.clientY - (this.mouseY - this.mouseT);
          box.style.top = divY + "px";
          box.style.left = divX + "px";
        }
      };
      box.onmouseup = (event) => {
        this.isClick = false;
      };
    },
    handleMouseDown(event) {
      let box = document.getElementById("selection-movebox");
      this.isClick = true;
      this.mouseX = event.clientX;
      this.mouseY = event.clientY;
      this.mousel = box.offsetLeft;
      this.mouseT = box.offsetTop;
    },
    handleMouseup(event) {
      this.isClick = false;
    },
    boxSelect() {
      this.selectMoveBox = false;
    },
    close() {
      this.detailsModalShow = false;
    },
    close2() {
      this.tracePointVisible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.time-space-colli {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
.move-div {
  width: 100px;
  height: 100px;
  background: #fff;
  position: absolute;
  left: 0;
  top: 0;
  user-select: none;
  border: 1px solid #000;
}
.move-box {
  position: absolute;
  top: 13px;
  left: 385px;
  z-index: 1000;
  /deep/.ocx-masker {
    display: none;
  }
}
</style>
