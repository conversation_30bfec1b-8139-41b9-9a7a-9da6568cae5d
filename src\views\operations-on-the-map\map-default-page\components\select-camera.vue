<template>
  <ui-modal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onOk="confirmHandle"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="organization">
        <div class="title">组织机构</div>
        <ui-tree
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </ui-tree>
      </div>
      <div class="select-label-content">
          <Form inline ref="form" :model="formData" class="form">
            <FormItem label="设备名称:">
              <Input v-model="formData.input1" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="设备编码:">
              <Input v-model="formData.input1" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="安装地址:">
              <Input v-model="formData.input1" placeholder="请输入"></Input>
            </FormItem>
          </Form>
        <Table 
          class="auto-fill table" 
          :height="335" 
          :columns="columns" 
          :data="tableData"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
          >
          <template slot="deviceId" slot-scope="{ row }">
            <span class="link-btn cursor-p" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
          <template #labels="{ row }">
            <ui-tag-poptip v-if="row.labels && row.labels.length" :data="row.labels" />
          </template>
        </Table>
        <ui-page :total="100" show-sizer show-elevator show-total></ui-page>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{ checkedCameraList.length }}</span> 个</span
          >
          <span class="del-btn" @click="removeAllHandle"
            ><span class="iconfont icon-shanchu"></span>清空</span
          >
        </div>
        <div class="label-container">
         <ul>
           <li v-for="(item,index) in selectTableData" :key="index">
             <Checkbox v-model="item.select">{{item.deviceName}}</Checkbox>
           </li>
         </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { getLabelAllList, getLabelGroupAllList } from "@/api/labelPool";
import { mapActions, mapGetters } from "vuex";

export default {
  components: {
    uiTree: require('@/components/ui-tree.vue').default,
  },
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      modalShow: false,
      formData: {},
      dialogData: {
        title: "选择摄像头",
        rWidth: 1500,
      },
      columns: [
        { title: '选择', width: 40, type: 'selection', key: 'index' },
        { title: '序号', width: 50, type: 'index', key: 'index' },
        { title: '设备名称', key: 'deviceName' },
        { title: '设备编码', key: 'deviceNumber' },
        { title: '安装地址', key: 'address' },
      ],
      tableData: [
        {
          id: 1,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 2,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 3,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 4,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 5,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 6,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 7,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 8,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
        {
          id: 9,
          deviceName: '高清摄像头',
          deviceNumber: '56256212545',
          address: '甘肃省庆阳市西峰区桃园大道975号',
        },
      ],
      selectTableData: [],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: {
        label: '未分配组织机构',
        orgCode: '-1',
      },
      checkedCameraList: []
    };
  },
  computed: {
    ...mapGetters({
      // labelTypeList: "dictionary/getLabelTypeList",
      // labelLevelList: "dictionary/getLabelLevelList",
      // labelPropertyList: "dictionary/getLabelPropertyList",
    }),
  },
  watch: {
    // checkedLabels(val) {
    //   this.checkedCameraList = val;
    // },
    // "queryParam.pinyin"(val) {
    //   this.requestLabelList();
    // },
    
  },
  created() {},
  methods: {
    ...mapActions({
      // getDictData: "dictionary/getDictAllData",
    }),
    init(code) {
      this.ObjCode = code || "";
      this.modalShow = true;
     
    },
    removeAllHandle() {
      this.checkedCameraList = [];
    },
    confirmHandle() {
      // this.$emit("setCheckedLabel", this.checkedCameraList);
      this.modalShow = false;
    },
    SearchHandler () {

    },
    selectedOrgTree(val) {
      // this.searchData.orgCode = val.orgCode
      // this.search()
    },
    onSelect (selection, row) {
      console.log(selection, row)
      row.select = true
      this.selectTableData.push(row)
    },
    onSelectCancel (selection, row) {
      console.log(selection, row)
      var num = this.selectTableData.findIndex(item => { return item.id == row.id})
      console.log(num, this.selectTableData)
      this.selectTableData.splice(num,1)
      console.log(num, this.selectTableData)
    },
    onSelectAll (selection) {
      console.log(selection)
      selection.forEach(item => {
        item.select = true
      })
      this.selectTableData = selection
    },
    onSelectAllCancel (selection) {
      console.log(selection)
      this.selectTableData = []
    }
  },
};
</script>
<style lang="less" scoped>
.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 20px 20px;
  display: flex;
  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
  }
  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    height: 400px;
    position: relative;
  }
  .organization {
    width: 200px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 472px;;
    overflow: auto;
    border-right: 1px solid #d3d7de;

    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      background: #F9F9F9;
    }
  }
  .preview-select-label-content {
    width: 300px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.del-btn {
  cursor: pointer;
}
.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 20px;
}

/deep/ .ivu-icon {
  color: #fff;
}

.form {
    width: 100%;
    .search-input {
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input {
        width: 200px;
      }
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/.ivu-form-item {
      margin-bottom: 16px;
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    /deep/ .ivu-form-item-content{
      float: left;
    }
    .btn-group {
      margin-right: 0;
    }
  }
</style>
