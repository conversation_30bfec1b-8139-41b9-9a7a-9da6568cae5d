<template>
  <div class="model-left">
    <div class="model-left-header">
      <div>已映射数据表</div>
      <Button type="primary" @click="add">+&nbsp;新建映射</Button>
    </div>
    <div class="model-left-content">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {
    add() {
      this.$emit('newMapping');
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.align-flex {
  display: flex;
  align-items: center;
}
.justify-flex {
  display: flex;
  justify-content: center;
}
.model-left {
  width: 311px;
  // height: 100%;
  border-right: 1px solid var(--border-color);
  // padding: 0 20px 23px;
  &-header {
    padding: 55px 20px 13px;
    .align-flex;
    justify-content: space-between;
    font-size: 14px;
    color: #ffffff;
    border-bottom: 1px solid var(--border-color);
  }
  &-content {
    height: calc(100% - 103px);
    padding: 20px 0;
    overflow-y: auto;
  }
}
</style>
