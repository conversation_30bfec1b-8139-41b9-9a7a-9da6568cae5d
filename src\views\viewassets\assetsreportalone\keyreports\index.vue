<template>
  <div class="key-equipment-report auto-fill">
    <div class="config" @click="configFn()">
      <i class="icon-font icon-jichushuxing"></i>
    </div>
    <!-- 顶部统计 -->
    <chartsContainer :abnormalCount="countList" class="charts" />
    <!--搜索参数-->
    <div class="search-module">
      <ui-label class="inline mr-md" label="组织机构" :width="65">
        <api-organization-tree
          class="tree-style"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree1"
          placeholder="请选择组织机构"
        />
      </ui-label>
      <ui-label class="inline mr-md" label="行政区划" :width="65">
        <api-area-tree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </ui-label>
      <ui-label class="inline mr-md" :label="global.filedEnum.deviceId" :width="65">
        <Input v-model="searchData.deviceId" class="width-md" :placeholder="`${global.filedEnum.deviceId}`" />
      </ui-label>
      <ui-label class="inline mr-md" :label="global.filedEnum.deviceName" :width="65">
        <Input v-model="searchData.deviceName" class="width-md" :placeholder="global.filedEnum.deviceName" />
      </ui-label>
      <ui-label label="上报状态" :width="65" class="inline mr-md">
        <Select
          class="width-sm"
          v-model="searchData.keyDeviceReportStatus"
          placeholder="请选择上报状态"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in keyDeviceReportStatusList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="fr">
        <UiSearch class="ui-search mr-lg" v-model="searchModel">
          <template #content>
            <div class="search-content">
              <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="inline mr-md">
                <Select
                  class="width-md"
                  v-model="searchData.sbdwlx"
                  :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey">{{
                    item.dataValue
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.sbgnlx" :width="110" class="inline mr-md">
                <Select
                  class="width-md"
                  v-model="searchData.sbgnlx"
                  :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">{{
                    item.dataValue
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label label="重点采集区域类型" :width="120" class="inline mr-md">
                <Select
                  class="width-md"
                  v-model="searchData.sbcjqy"
                  placeholder="请选择重点采集区域类型"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in keyDeviceGatherArea" :key="index" :value="item.code">{{
                    item.name
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.phyStatus" :width="65" class="inline mr-md" style="margin-top: 12px">
                <Select
                  class="width-sm"
                  v-model="searchData.phyStatus"
                  :placeholder="`请选择${global.filedEnum.phyStatus}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="inline mr-md" label="上报时间" :width="65" style="margin-top: 12px">
                <DatePicker
                  class="width-md"
                  v-model="searchData.keyRecentlyReportTimeStart"
                  type="datetime"
                  placeholder="请选择开始时间"
                  :options="startTimeOption"
                  confirm
                ></DatePicker>
                <span class="ml-sm mr-sm">--</span>
                <DatePicker
                  class="width-md"
                  v-model="searchData.keyRecentlyReportTimeEnd"
                  type="datetime"
                  placeholder="请选择结束时间"
                  :options="endTimeOption"
                  confirm
                ></DatePicker>
              </ui-label>
              <ui-label class="inline mb-sm mt-sm" label="在线状态">
                <Select class="width-md" v-model="searchData.isOnline" clearable placeholder="请选择设备在线状态">
                  <Option v-for="(item, index) in propertySearch_isonline" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-select-tabs
                class="ui-select-tabs"
                :list="MixinTagList"
                @selectInfo="MixinSelectInfo"
                ref="uiSelectTabs"
              >
              </ui-select-tabs>
            </div>
          </template>
        </UiSearch>
        <Button type="primary" @click="searchHandle">查询</Button>
        <Button class="ml-sm" @click="resetHandle">重置</Button>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="operation">
      <Checkbox v-model="allCheck" @on-change="handleAllCheck">全选</Checkbox>
      <div class="right-btn fr">
        <Button type="primary" @click="keyEquipmentReportHandle" :loading="isReportLoading"
          ><i class="btn icon-font icon-keyequipmentreportBtn"></i>重点设备上报
        </Button>
      </div>
    </div>
    <!-- 表格 -->
    <div class="table-module auto-fill">
      <ui-table
        reserveSelection
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :is-all="allCheck"
        :default-store-data="selectTable"
        @selectTable="selected"
      >
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltipType">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <template #isOnline="{ row }">
          <div>
            <span
              class="check-status-font"
              :class="row.isOnline === '1' ? 'font-success' : row.isOnline === '2' ? 'font-failed' : ''"
            >
              {{ row.isOnlineText }}
            </span>
          </div>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #keyDeviceReportStatus="{ row }">
          <div>
            <span
              class="check-status"
              :class="[
                row.keyDeviceReportStatus === '2' ? 'bg-failed' : '',
                row.keyDeviceReportStatus === '1' ? 'bg-success' : '',
              ]"
            >
              {{ row.keyDeviceReportStatus | filterType(keyDeviceReportStatusList) }}
            </span>
          </div>
        </template>
        <template #keyDeviceReportDate="{ row }">
          <span>{{ row.keyDeviceReportDate || '--' }}</span>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize" />
    </div>
    <!-- 配置 -->
    <config-page ref="config" v-model="configShow" @on-handle-success="queryDeviceCount" />
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import viewassets from '@/config/api/viewassets';
import getTagMixin from '@/views/viewassets/mixins/getTagMixin';

export default {
  mixins: [getTagMixin],
  name: 'keyreports',
  components: {
    apiOrganizationTree: require('@/api-components/api-organization-tree').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    chartsContainer: require('@/views/viewassets/commonreport/keyequipmentreport/chartsContainer').default,
    configPage: require('@/views/viewassets/commonreport/keyequipmentreport/config/configpage').default,
    UiSearch: require('@/components/ui-search').default,
  },
  data() {
    return {
      searchModel: false,
      allCheck: false,
      selectOrgTree: {
        orgCode: '',
      },
      selectTree: {
        regionCode: '',
      },
      keyDeviceGatherArea: [],
      keyDeviceReportStatusList: [
        { dataKey: '0', dataValue: '未上报' },
        { dataKey: '1', dataValue: '已上报' },
        { dataKey: '2', dataValue: '上报失败' },
      ], //重点设备上报状态
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      searchData: {
        orgCode: '',
        configOrgCode: '',
        civilCode: '',
        deviceId: '',
        deviceName: '',
        keyDeviceReportStatus: '',
        sbdwlx: '',
        sbgnlx: '',
        sbcjqy: '',
        phyStatus: '',
        keyRecentlyReportTimeStart: '',
        keyRecentlyReportTimeEnd: '',
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
        isOnline: '',
        isImportant: 1,
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.keyRecentlyReportTimeEnd);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.keyRecentlyReportTimeStart);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
          align: 'left',
        },
        {
          minWidth: 100,
          title: '采集区域',
          key: 'sbcjqyText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '在线状态',
          slot: 'isOnline',
          align: 'left',
          // fixed: 'right',
        },
        {
          width: 120,
          title: this.global.filedEnum.phyStatus,
          slot: 'phyStatus',
        },
        { width: 120, title: '上报状态', slot: 'keyDeviceReportStatus' },
        { width: 150, title: '上报时间', slot: 'keyDeviceReportDate' },
      ],
      tableData: [],
      loading: false,
      countList: [
        { title: '重点设备数量', total: '0', icon: 'icon-keyequipment' },
        {
          title: '上报数量',
          total: '0',
          icon: 'icon-zichanshangbao',
        },
        {
          title: '上报人脸卡口',
          total: '0',
          icon: 'icon-shangbaorenliankakou',
        },
        {
          title: '上报车辆卡口',
          total: '0',
          icon: 'icon-shangbaocheliangkakou',
        },
        {
          title: '上报视频监控',
          total: '0',
          icon: 'icon-shangbaoshipinjiankong',
        },
      ],
      configShow: false,
      selectTable: [],
      isReportLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus',
      propertySearch_isonline: 'algorithm/propertySearch_isonline', // 在线状态
    }),
    isRepeatRecord() {
      return this.activeKey === 5;
    },
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.getKeyAreaList();
    this.queryDeviceCount();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 全选
    handleAllCheck() {
      this.selectTable = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.allCheck);
        this.$set(item, '_disabled', this.allCheck);
        return item;
      });
    },
    //获取重点采集区域类型
    getKeyAreaList() {
      this.loading = true;
      this.$http
        .post(viewassets.getKeyEquipmentConfig, {})
        .then((res) => {
          this.keyDeviceGatherArea = res.data.data;
          this.init();
        })
        .catch(() => {
          this.loading = false;
        })
        .finally(() => {});
    },
    init() {
      this.loading = true;
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      params.cascadeReportStatus = 1;
      if (!params.sbcjqy) {
        params.sbcjqy = this.keyDeviceGatherArea.map((v) => {
          return v.code;
        });
      } else {
        params.sbcjqy = [params.sbcjqy];
      }
      if (!params.sbgnlx) {
        params.sbgnlx = this.propertySearchSxjgnlx.map((v) => {
          return v.dataKey;
        });
      } else {
        params.sbgnlx = [params.sbgnlx];
      }
      if (params.keyRecentlyReportTimeStart) {
        params.keyRecentlyReportTimeStart = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeStart);
      }
      if (params.keyRecentlyReportTimeEnd) {
        params.keyRecentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeEnd);
      }
      this.$http
        .post(viewassets.reportQueryDeviceInfoPageList, params)
        .then((res) => {
          let { entities, total } = res.data.data.data;
          this.pageData.totalCount = total;
          this.tableData = entities;
          this.selectTable = [];
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取头部统计信息
    async queryDeviceCount() {
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      params.cascadeReportStatus = 1;
      if (!params.sbcjqy) {
        params.sbcjqy = this.keyDeviceGatherArea.map((v) => {
          return v.code;
        });
      } else {
        params.sbcjqy = [params.sbcjqy];
      }
      if (!params.sbgnlx) {
        params.sbgnlx = this.propertySearchSxjgnlx.map((v) => {
          return v.dataKey;
        });
      } else {
        params.sbgnlx = [params.sbgnlx];
      }
      if (params.keyRecentlyReportTimeStart) {
        params.keyRecentlyReportTimeStart = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeStart);
      }
      if (params.keyRecentlyReportTimeEnd) {
        params.keyRecentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeEnd);
      }
      try {
        let res = await this.$http.post(
          `${viewassets.getKeyEquipmentReportStatistics}?orgCode=${this.searchData.orgCode}`,
          params,
        );
        let obj = res.data.data;
        this.countList[0].total = obj.total;
        this.countList[1].total = obj.reportedKeyDevice.total;
        this.countList[1].thresholdReached = obj.reportedKeyDevice.thresholdReached;
        this.countList[1].gatherArea = obj.reportedKeyDevice.gatherArea;

        this.countList[2].total = obj.reportedFaceBayonet.total;
        this.countList[2].thresholdReached = obj.reportedFaceBayonet.thresholdReached;
        this.countList[2].gatherArea = obj.reportedFaceBayonet.gatherArea;

        this.countList[3].total = obj.reportedVehicleBayonet.total;
        this.countList[3].thresholdReached = obj.reportedVehicleBayonet.thresholdReached;
        this.countList[3].gatherArea = obj.reportedVehicleBayonet.gatherArea;

        this.countList[4].total = obj.reportedVideoMonitor.total;
        this.countList[4].thresholdReached = obj.reportedVideoMonitor.thresholdReached;
        this.countList[4].gatherArea = obj.reportedVideoMonitor.gatherArea;
      } catch (err) {
        console.error('err', err);
      } finally {
        // this.loading = false
      }
    },
    // 配置
    configFn() {
      this.$refs.config.show();
    },
    // 选择组织机构
    selectedOrgTree1(val) {
      this.searchData.orgCode = val.orgCode;
      this.searchData.configOrgCode = val.orgCode;
      this.searchHandle();
    },
    // 选择行政区划
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // 查询
    searchHandle() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
      this.queryDeviceCount();
    },
    // 重置
    resetHandle() {
      this.selectOrgTree.orgCode = '';
      this.selectTree.regionCode = '';
      this.$refs.uiSelectTabs && this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.searchHandle);
    },
    // table选中方法
    selected(selection) {
      this.selectTable = selection;
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.params.pageSize = val;
      this.searchHandle();
    },
    // 设备档案
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    // 重点设备上报
    keyEquipmentReportHandle() {
      let selectedDataLen = this.selectTable.length;
      if (!selectedDataLen && !this.allCheck) {
        this.$Message.warning('请选择上报设备');
        return false;
      } else {
        this.$UiConfirm({
          content: '确定将该批设备进行上报操作吗？',
          title: '重点设备上报',
        })
          .then(() => {
            this.isReportLoading = true;
            let data = this.allCheck ? [] : this.selectTable;
            let params = {
              deviceInfoSummaries: data,
              orgCode: this.searchData.orgCode,
            };
            this.$http
              .post(viewassets.keyDeviceReport, Object.assign(params, this.allCheck ? this.searchDataParams() : {}))
              .then(() => {
                this.$Message.success('重点设备上报成功');
              })
              .catch(() => {})
              .finally(() => {
                this.isReportLoading = false;
                this.selectTable = [];
                this.allCheck = false;
                this.init();
                this.queryDeviceCount();
              });
          })
          .catch(() => {});
      }
    },
    // 参数处理
    searchDataParams() {
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      params.cascadeReportStatus = 1;
      if (!params.sbcjqy) {
        params.sbcjqy = this.keyDeviceGatherArea.map((v) => {
          return v.code;
        });
      } else {
        params.sbcjqy = [params.sbcjqy];
      }
      if (!params.sbgnlx) {
        params.sbgnlx = this.propertySearchSxjgnlx.map((v) => {
          return v.dataKey;
        });
      } else {
        params.sbgnlx = [params.sbgnlx];
      }
      if (params.keyRecentlyReportTimeStart) {
        params.keyRecentlyReportTimeStart = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeStart);
      }
      if (params.keyRecentlyReportTimeEnd) {
        params.keyRecentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeEnd);
      }
      return params;
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue']{
  .config .icon-font{
    color: #888888;
    &:hover {
      color: var(--color-switch-tab-active);
    }
  }
}
.key-equipment-report {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);

  .charts {
    margin: 20px 0 0 20px;
  }

  .search-module {
    margin: 10px 20px;
    position: relative;
    .keyword-input {
      width: 300px;
    }
  }

  .ui-table {
    padding: 0 20px;
  }
  .tooltipType {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.operation {
  margin: 2px 20px 12px 20px;
  line-height: 34px;

  .right-btn {
    button {
      margin-left: 12px;
    }
  }
}

.config {
  position: absolute;
  right: 20px;
  top: 20px;
  color: var(--color-switch-tab);
  cursor: pointer;

  .icon-font {
    font-size: 20px;
  }

  &:hover {
    color: var(--color-switch-tab-active);
  }
}

@{_deep} .ivu-badge-dot {
  top: 1px;
  right: -15px;
  border: 0;
  z-index: 3;
}

@{_deep} .icon-font {
  font-size: 15px;
}

.btn {
  color: #f5f5f5 !important;
  margin-right: 10px;
}
</style>
