<template>
  <div class="operate-tree">
    <Collapse
      v-for="(item, index) in mappedData"
      :key="index"
      simple
      accordion
      v-model="name"
      @on-change="handleChange"
    >
      <Panel :name="index.toString()">
        <div class="align-flex">
          <Spin v-if="loading" class="right-marin">
            <Icon type="ios-loading" size="14" class="demo-spin-icon-load"></Icon>
          </Spin>
          {{ item.kafkaTopicName }}
        </div>

        <ul class="collapsepanel" slot="content">
          <li
            :class="['collapsepanel-li', active == index + '-' + distIndex ? 'active' : '']"
            v-for="(distItem, distIndex) in distData"
            :key="distIndex"
          >
            <div class="flex-1">{{ distItem.sourcePropertyColumn }}</div>
            <div class="tree-operate">
              <!-- 编辑 -->
              <i class="icon-font icon-bianji" @click="setDictData(distItem, index + '-' + distIndex)"></i>
              <!-- 删除 -->
              <i class="icon-font icon-shanchu2" @click="deleteDict(distItem)"></i>
            </div>
          </li>
        </ul>
      </Panel>
    </Collapse>
  </div>
</template>
<script>
export default {
  props: {
    mappedData: {
      type: Array,
      default: () => [],
    },
    distData: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      active: '',
      name: '',
    };
  },
  created() {},
  methods: {
    handleChange(val) {
      if (!val.length) return false;
      const id = this.mappedData[val[0]]['id'];
      const kafkaName = this.mappedData[val[0]]['kafkaTopicName'];
      this.$emit('getDictList', id + ',' + kafkaName);
    },
    setDictData(item, key) {
      this.active = key;
      this.$emit('setData', item);
    },
    deleteDict(item) {
      this.$emit('delete', item);
    },
  },
  watch: {
    isEdit() {
      if (!this.isEdit) {
        this.name = '';
        this.active = '';
      } else {
        this.$emit('updatamapData');
      }
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.flex-1 {
  flex: 1;
}
.right-marin {
  margin-right: 5px;
}
.demo-tree-render .ivu-tree-title {
  width: 100%;
}
.collapsepanel {
  // padding-top: 10px;
  &-li {
    width: 100%;
    // height: 30px;
    padding: 5px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    color: #ffffff;
    cursor: pointer;
    &:hover {
      background: #184f8d;
      i {
        color: #ffffff;
      }
    }
    i {
      font-size: 12px;
      color: #174f98;
      &:hover {
        color: var(--color-primary);
      }
      &:active {
        color: #4e9ef2;
      }
    }
  }
  .active {
    background: #184f8d;
    i {
      color: #ffffff;
      font-size: 12px;
    }
  }
}
@{_deep} .ivu-tree-title {
  display: 'initial' !important;
}
@{_deep} .ivu-collapse {
  background-color: transparent;
  border: none;
  &-item {
    border-top: none;
    margin-bottom: 15px;
    &-active {
      .ivu-collapse-header > i {
        font-size: 12px !important;
        color: #239df9 !important;
        transform: rotate(0deg) translate(-8px, 8px) scale(0.6) !important;
      }
    }
  }
  .ivu-collapse-header {
    display: flex;
    padding: 0 16px;
    font-size: 14px;
    color: #ffffff;
    border-bottom: none !important;
    line-height: 20px;
    opacity: 0.9;
    word-break: break-all;
    i {
      margin-right: 0 !important;
    }
  }
  &-content {
    background: transparent;
    padding: 0;
    &-box {
      // padding-top: 10px !important;
      padding-bottom: 0 !important;
    }
  }
}
@{_deep}.ivu-icon-ios-arrow-forward {
  font-size: 12px;
  color: #239df9;
  transform: rotate(-90deg) scale(0.6);
  &:before {
    font-family: 'icon-font';
    content: '\e7a3';
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}
.tree-operate {
  width: 40px;
  margin-left: 10px;
  display: flex;
  justify-content: space-between;
}
</style>
<style lang="less">
.operate-tree {
  .ivu-tree-title {
    display: initial !important;
    font-size: 14px;
    color: #fff;
    &:hover {
      background-color: var(--bg-content) !important;
    }
    &-selected,
    &-selected:hover {
      background-color: #184f8d !important;
    }
  }
}
</style>
