<template>
  <ui-card :title="title" class="background-information">
    <div class="background-information-content">
      <Tabs
        type="card"
        :animated="false"
        class="btn-tabs"
        @on-click="tabsClick"
      >
        <TabPane
          v-for="(item, index) in tabList"
          :key="index"
          :label="item.resourceNameCn"
          :name="index + ''"
        ></TabPane>
      </Tabs>
      <ui-table
        ref="violationTable"
        :columns="columns"
        :data="tableList"
        :loading="violationLoading"
      >
        <template #dealStatus="{ row }">
          <Tag checked color="#1FAF81">已结案</Tag>
        </template>
        <template #action="{ row }">
          <ui-btn-tip content="档案" icon="icon-dangan2" class="primary" />
        </template>
      </ui-table>
      <ui-page
        :current="pageInfo.pageNumber"
        :total="pageInfo.total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
  </ui-card>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import {
  getManageResourceList,
  getResourceConfigur,
  policeDataPageList,
} from "@/api/vehicleArchives";
import uiBtnTip from "../../../../../../components/ui-btn-tip.vue";
export default {
  components: { uiBtnTip },
  props: {
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    // 车牌
    plateNo: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapGetters({
      archiveObj: "systemParam/archiveObj",
    }),
  },
  data() {
    return {
      involvedLoading: false,
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      involvedSearchData: {
        isRequest: false,
        time: "",
        startTime: "",
        endTime: "",
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
      involvedTotal: 0,
      involvedColumns: [
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "案件编号", key: "number" },
        { title: "案件名称", key: "name" },
        { title: "案件类别", key: "type", width: 120 },
        { title: "案发地点", key: "crimeAddress", tooltip: true },
        { title: "案发时间", key: "caseTime" },
        { title: "案件状态", slot: "status", width: 120 },
        { title: "操作", slot: "action", width: 66 },
      ],
      involvedTableData: [],
      violationLoading: false,
      violationSearchData: {
        isRequest: false,
        time: "",
        startTime: "",
        endTime: "",
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
      violationTotal: 0,
      violationColumns: [
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "车牌号码", key: "jdchphm" },
        { title: "违法时间", key: "wfsj" },
        { title: "违法地址", key: "wfdzJyqk", tooltip: true },
        { title: "处理状态", slot: "dealStatus", width: 120 },
        { title: "处理时间", key: "clsj" },
      ],
      violationTableData: [],
      tabList: [],
      columns: [],
      tableList: [],
      currentRow: null,
      routeQuery: null,
    };
  },
  async created() {
    await this.getSystemAllData();
    this.routeQuery = this.$route.query;
    this.init();
  },
  methods: {
    ...mapActions({
      getSystemAllData: "systemParam/getSystemAllData",
    }),
    init() {
      var arr = this.archiveObj.realNameArcBackground;
      if (this.type == "vehicle") {
        arr = this.archiveObj.vehicleArcBackground;
      }
      var param = {
        resourceIds: arr,
      };
      // 获取动态上部列表
      getManageResourceList(param).then((res) => {
        // console.log('获取已添加管理资源',res)
        this.tabList = res.data || [];
        this.tableHeader(this.tabList[0]);
        this.currentRow = this.tabList[0];
      });
    },
    tableHeader(row) {
      var param = {
        resourceId: row?.id || "",
        isShow: 1,
      };
      getResourceConfigur(param).then((res) => {
        // console.log('表头信息',res)
        var list = res.data;
        this.columns = [
          { type: "index", title: "序号", width: 68, align: "center" },
        ];
        list.forEach((item) => {
          var obj = {
            title: item.fieldNameCn,
            key: item.fieldName,
            minWidth: 120,
          };

          this.columns.push(obj);
        });
        // this.columns.push({ title: '操作', slot: 'action', width: 66 })
        this.tableList = [];
        setTimeout(() => {
          this.tableBody(row.id);
        }, 20);
      });
    },
    tableBody(id) {
      var str =
        this.type == "vehicle" ? this.plateNo : this.$route.query.archiveNo;
      var param = {
        keyWords: str,
        resourceId: id,
        treeResourceName: this.currentRow.resourceName,
        // keyWords: this.routeQuery.archiveNo,
        ...this.pageInfo,
      };
      policeDataPageList(param).then((res) => {
        res.data.entities.map((item) => {
          if (item.hphm) {
            //交通事故基本信息 号牌号码类型转换
            item.hphm = item.hphm.join(",");
          }
        });
        this.tableList = res.data.entities || [];
        this.pageInfo.total = res.data.total;
      });
    },
    tabsClick(index) {
      // this.tableHeader(this.tabList[index])
      this.currentRow = this.tabList[index];
      this.pageSizeChange(20);
      // if(this.type == 'vehicle'){
      //   // this.currentRow = this.tabList.find(item => {return item.id == this.archiveObj.vehicleArcBackground[index]})
      //   // this.tableHeader(this.archiveObj.vehicleArcBackground[index])
      //   this.tableHeader(this.tabList[index].id)
      // }else {
      //   // this.currentRow = this.tabList.find(item => {return item.id == this.archiveObj.realNameArcBackground[index]})
      //   // this.tableHeader(this.archiveObj.realNameArcBackground[index])
      //   this.tableHeader(this.tabList[index].id)
      // }
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.tableHeader(this.currentRow);
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.tableHeader(this.currentRow);
    },
  },
};
</script>
<style lang="less" scoped>
.background-information {
  /deep/ .card-content {
    padding-bottom: 0 !important;
  }
  /deep/ .ui-table {
    height: 296px;
  }
  .background-information-content {
    width: 100%;
    position: relative;
    .time-frame {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 14px;
      z-index: 10;
      .time-frame-label {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}
</style>
