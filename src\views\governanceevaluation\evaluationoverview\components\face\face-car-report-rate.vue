<template>
  <div class="place-accuracy height-full auto-fill">
    <div class="evaluation-header">
      <div class="filtrate">
        <span class="f-16 color-filter ml-sm">{{ paramsList.orgRegionName }}-有效上报率</span>
      </div>
      <div>
        <span class="evaluation-time">
          <i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：{{ paramsList.examineTime || '未知' }}
        </span>
      </div>
    </div>
    <div ref="contentScroll">
      <div class="statistics-container">
        <div class="statistics">
          <icon-statistics :statistics-list="abnormalCount" :isflexfix="false">
            <template #validReportRate="{ row }">
              <span
                class="icon-font position f-14"
                :class="row.qualified === '2' ? 'icon-budabiao warning' : 'icon-dabiao success'"
              ></span>
            </template>
          </icon-statistics>
        </div>
      </div>
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-xiugaijilu f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">异常数据列表</span>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="clickExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="base-text-color mb-sm mt-sm">
        <ui-select-tabs :list="selectTabs" @selectInfo="selectInfo" ref="uiSelectTabs"></ui-select-tabs>
      </div>
    </div>
    <div class="auto-fill">
      <ui-table :table-columns="columns" :table-data="tableData" :loading="loading" class="auto-fill">
        <template #checkStatus="{ row }">
          <span
            class="tag"
            :style="{
              background: row.checkStatus === 1 ? '#0E8F0E' : '#BC3C19',
            }"
            >{{ checkStatusList(row.checkStatus) }}</span
          >
        </template>
        <template #tagNames="{ row }">
          <tags-more :tag-list="row.tagList || []"></tags-more>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            icon="icon-chakanyichangxiangqing"
            content="不合格原因"
            class="mr-sm"
            :disabled="row.checkStatus === '1'"
            @click.native="checkReason(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="mr-sm"
            icon="icon-tianjiabiaoqian"
            content="添加标签"
            @click.native="addTags(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      <nonconformance
        :isPage="false"
        ref="nonconformance"
        title="检测不合格原因"
        :tableColumns="reasonTableColumns"
        :tableData="reasonTableData"
        :reasonPage="reasonPage"
        :reasonLoading="reasonLoading"
        @handlePageChange="handlePageChange"
        @handlePageSizeChange="handlePageSizeChange"
      ></nonconformance>
      <export-data ref="exportModule" @exportAdd="getExport" :exportLoading="exportLoading"></export-data>
      <customize-filter
        v-model="customSearch"
        :customize-action="customizeAction"
        :content-style="contentStyle"
        :field-name="fieldName"
        :checkbox-list="deviceTagData"
        :default-checked-list="defaultCheckedList"
        @confirmFilter="confirmFilter"
      >
      </customize-filter>
    </div>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';

export default {
  mixins: [downLoadTips],
  name: 'face-car-report-rate',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    IconStatistics: require('@/components/icon-statistics').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    Nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    exportData: require('@/views/governanceevaluation/evaluationoverview/components/basics/export-data.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
  data() {
    return {
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      treeData: [],
      getDefaultSelectedOrg: '',
      currentOrgObj: {}, //机构树
      loading: false,
      width: 155, // 设备模式数量检索展示对应label宽度
      bigPictureShow: false, // 打图展示
      imgList: [], // 大图图片
      abnormalCount: [
        {
          key: 'buildNum',
          name: '卡口建设总量',
          value: 0,
          icon: 'icon-renliankakoujianshezongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
        {
          key: 'checkCount',
          name: '卡口检测数量',
          value: 0,
          icon: 'icon-jianceshuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
        },
        {
          key: 'qualifiedCount',
          name: '检测合格设备数',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color3',
        },
        {
          key: 'unqualifiedCount',
          name: '检测不合格设备数',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color4',
        },
        {
          key: 'validReportRate',
          name: '有效上报率',
          value: 0,
          icon: 'icon-youxiaoshangbaoshuai',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage', // number数字 percentage 百分比
          textColor: 'color5',
        },
      ],
      columns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
          minWidth: 150,
        },
        { title: '所属单位', key: 'orgName', tooltip: true, minWidth: 150 },
        { title: '行政区划', key: 'civilName', tooltip: true, minWidth: 150 },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.sbdwlx}`,
          key: 'sbdwlxText',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.sbcjqy}`,
          key: 'sbcjqyText',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.phyStatus}`,
          key: 'phyStatusText',
          tooltip: true,
          minWidth: 150,
        },
        { title: '安装地址', key: 'address', tooltip: true, minWidth: 150 },
        {
          title: '数据来源',
          key: 'sourceIdText',
          tooltip: true,
          minWidth: 150,
        },
        { title: '检测时间', key: 'createTime', tooltip: true, minWidth: 150 },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 100,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      tableData: [],
      echartsLoading: false,
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      barData: [],
      currentRow: {},
      exportLoading: false,
      selectTabsList: [],
      selectTabs: [],
      errorMessageList: [],
      reasonTableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      reasonLoading: false,
      deviceInfoId: '',
      statisticalList: {},
      paramsList: {},
      contentClientHeight: 0,
      navbarTitle: '',
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  activated() {
    this.paramsList = this.$route.query;
    this.getTitle();
    this.getStatInfo();
    this.initList();
    this.getSelectTabs();
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 370 * proportion : 0;
    this.getTagList();
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.initList();
      } catch (err) {
        console.log(err);
      }
    },
    getTitle() {
      let { indexId } = this.paramsList;
      if (Number.parseInt(indexId) === 2015) {
        this.navbarTitle = '人脸';
      } else if (Number.parseInt(indexId) === 3019) {
        this.navbarTitle = '车辆';
      } else if (Number.parseInt(indexId) === 4018) {
        this.navbarTitle = '视频监控';
      }
    },
    async getStatInfo() {
      let { indexId, batchId, access, displayType, orgRegionCode } = this.paramsList;
      this.statisticalList = {};
      let data = {
        indexId,
        batchId,
        access: access || 'REPORT_MODE',
        displayType,
        orgRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getStatInfo, data);
        this.statisticalList = res.data.data || {};
        this.statistical();
      } catch (err) {
        console.log(err);
      }
    },
    statistical() {
      let { name, icon } = this.getStatisticalTitle();
      Object.keys(this.statisticalList).map((key) => {
        this.abnormalCount.map((item) => {
          if (key === item.key) {
            item.value = this.statisticalList[key];
          }
          if (key === item.key && key === 'validReportRate') {
            item.value = `${this.statisticalList[key]}%`;
          }
          if (item.key === 'buildNum') {
            item.name = `${name}卡口建设总量`;
            item.icon = icon;
          }
          if (item.key === 'checkCount') {
            item.name = `${name}卡口检测数量`;
            item.icon = icon;
          }
        });
      });
    },
    getStatisticalTitle() {
      let indexId = Number.parseInt(this.paramsList.indexId);
      if (indexId === 2015) {
        return { name: '人脸', icon: 'icon-renliankakoujianshezongliang' };
      } else if (indexId === 3019) {
        return { name: '车辆', icon: 'icon-cheliangkakoujianshezongliang' };
      } else if (indexId === 4018) {
        return { name: '视频监控', icon: 'icon-shipinliujianshezongliang' };
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: {
          deviceInfoId: this.deviceInfoId,
        },
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getPopUpData, params);
        this.reasonTableData = data || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    // 不合格原因
    checkReason(row) {
      this.deviceInfoId = row.deviceInfoId;
      this.getReason();
      this.$refs.nonconformance.init();
    },
    selectInfo(val) {
      this.errorMessageList =
        val &&
        val.map((item) => {
          if (item.select) {
            return item.name;
          }
        });
      this.startSearch();
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = res.data.data.map((item) => {
          let obj = {};
          obj.name = item;
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    clickExport() {
      this.$refs.exportModule.init(this.paramsList.batchId);
    },
    async getExport(val) {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          multiSheet: JSON.parse(val.multiSheet),
          orgCodes: val.orgCodes,
          exportZip: true,
          customParameters: {
            errorMessages: this.errorMessageList,
          },
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            errorMessages: this.errorMessageList,
          },
          pageSize: this.pageData.pageSize,
          pageNumber: this.pageData.pageNum,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getDetailData, params);
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total || 0;
      } catch (e) {
        // console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 检索
    async startSearch() {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.$nextTick(async () => {
        this.initList();
      });
    },
    // 统计参数填充
    checkStatusList(checkStatus) {
      return checkStatus === 1 ? '合格' : '不合格';
    },
  },
};
</script>
<style lang="less" scoped>
.place-accuracy {
  position: relative;
  overflow-y: auto;
  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }

    .evaluation-time {
      color: #a9bed9;
      font-size: 14px;
      margin-left: 10px;

      .mr-x {
        margin-right: 3px;
      }
    }

    .active {
      background: rgba(2, 57, 96, 1);
    }
  }
}

.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.statistics-container {
  position: relative;
  width: 100%;
  .statistics {
    margin-top: 10px;
    @{_deep} .information-statistics {
      background: var(--bg-sub-content);
      .statistics-ul {
        padding: 20px 20px 10px 20px;
        .mb-sm {
          flex: 1;
        }
      }
    }
  }

  .information-echart {
    flex: 1;
    height: 252px;
    background: var(--bg-sub-content);
    margin-top: 10px;

    .echarts-box {
      width: 100%;
      height: 100% !important;

      .charts {
        width: 100%;
        height: 100% !important;
      }
    }
  }
}

.abnormal-title {
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(7, 66, 119, 1);

  .color-filter {
    color: rgba(43, 132, 226, 1);
    vertical-align: middle;
  }
}

.success {
  color: #0e8f0e;
}

.warning {
  color: #bc3c19;
}

.position {
  position: absolute;
  right: 10px;
  top: 10px;
}
</style>
