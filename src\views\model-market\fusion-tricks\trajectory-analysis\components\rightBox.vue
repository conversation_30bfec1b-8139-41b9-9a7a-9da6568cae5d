<!--
    * @FileDescription: 轨迹信息
    * @Author: H
    * @Date: 2022/12/20
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="track-box" :class="{'track-box-pack': footerUpDown}">
        <div class="title">
            <p>轨迹信息</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <ul class="tab-name">
            <li class="tab-li" 
                @click="handleClick(index, item)"
                :class="{'active-tab': index == tabIndex}" 
                v-for='(item, index) in tabList' 
                :key="index"> 
                {{ item.name }}
            </li>
        </ul>
        <div class="hint_title">
            共<span> {{ timeList.length }} </span> 条检索结果
        </div>
        <div class="box-content">
            <ul class="box-ul">
                <li class="box-li" :class="{'box-li-pack': packUpDown[index], 'box-li-packend': index == (timeList.length- 1)&&packUpDown[index]}"  
                    v-for='(item, index) in timeList' :key="index">
                    <!-- <i class="iconfont md-radio-button-on"></i> -->
                    <div class="box-li-top">
                        <Icon type="md-radio-button-on"></Icon>
                    </div>
                    <div class="box-li-bottom">
                        <div class="time-title" @click="handletimelist(item, index)">
                            <p><span class="time-date">{{ item.date }}</span> <span class="time-num">{{item.times}}次</span></p>
                            <p class="triangle" :class="{'active-triangle': !packUpDown[index]}"></p>
                        </div>
                        <div class="child_list" v-for="(it, ind) in item.children" :key="ind">
                            <p class="sec-radio"></p>
                            <div class="content-top" @click="handleListTrack(item.children, ind)">
                                <div class="content-top-img">
                                    <img v-lazy="it.traitImg" alt="">
                                </div>
                                <div class="content-top-right">
                                    <span class="ellipsis">
                                        <ui-icon type="time" :size="14"></ui-icon>
                                        <span>{{ it.captureTime }}</span>
                                    </span>
                                    <span class="ellipsis">
                                        <ui-icon type="location" :size="14"></ui-icon>
                                        <span>{{ it.captureAddress }}</span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
                <ui-empty v-if="timeList.length === 0 && loading == false"></ui-empty>
                <ui-loading v-if="loading"></ui-loading>
            </ul>
        </div>
        <div class="footer" :class="{packArrow: footerUpDown}" @click="handlePackup">
            <img :src="packUrl" alt="">
            <p>{{ footerUpDown ? '展开' : '收起'}} </p>
        </div>
    </div>
</template>

<script>
import { trajectoryData, trajectoryStatisticsData } from '@/api/modelMarket';
import { mapMutations } from 'vuex';
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            tabList:[
                { 'name': '人脸', 'type': 'vidTrajectoryStatistics', 'childType': 'vidTrajectory'},
                { 'name': '车辆', 'type': 'carTrajectoryStatistics', 'childType': 'carTrajectory'},
                { 'name': 'RFID', 'type': 'vidTrajectory'},
                { 'name': 'IMSI', 'type': 'vidTrajectory'},
                { 'name': 'MAC', 'type': 'vidTrajectory'},
            ],
            timeList:[],
            packUpDown: [ ],
            relevanceCond:{},
            tabIndex:0,
            tabType: 'vidTrajectoryStatistics',
            footerUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            loading: false
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        ...mapMutations({
            setTrackList:'map/setTrackList',
            setPeerData: 'map/setPeerData',
        }),
        handleClick(index, item) {
            this.tabIndex = index;
            this.tabType = item.type;
            this.timeList = this.allList[this.tabType] || [];
            this.packUpDown = Array.apply(null, { length: this.timeList.length }).map(()=>{
                return true;
            });
            this.$emit('tabClick', index)
        },
        init(item, search) {
            this.loading = true;
            this.relevanceCond = item;
            let params = {
                dataRange: search.dateType,
                endDate:  search.endDate,
                startDate: search.startDate,
                // vid:["46879558199805839", '38515615592349709'],
                // plateNos: ["苏AD67L8"],
                ...item
            };
            // this.handleTrack(search, 0, true);
            trajectoryStatisticsData(params)
            .then(res => {
                this.allList = res.data;
                this.timeList = this.allList[this.tabType] || [];
                this.packUpDown = Array.apply(null, { length: this.timeList.length }).map(()=>{
                    return true;
                });
            })
            .finally(()=>{
                this.loading = false;
                // 车辆进来轨迹信息直接展示车辆的
                if(this.$route.path.includes('car')) {
                    this.handleClick(1, this.tabList[1])
                }
            })
        },
        // 展开轨迹信息
        handletimelist(item, index) {
            this.$set(this.packUpDown, index, !this.packUpDown[index]);
            if(!this.packUpDown[index] && !this.timeList[index].children) {
                this.handleTrack(item, index);
            }
        },
        // 轨迹信息
        handleTrack(item, index, fistr = false){
            let params = {
                dataRange: fistr? item.dateType : 4,
                endDate:  fistr? item.endDate : item.date,
                startDate: fistr?  item.startDate : item.date,
                ...this.relevanceCond
            };
            trajectoryData(params)
            .then(res => {
                if(fistr) {
                    // this.$emit('mapline', res.data || {})
                }else {
                    this.$set(this.timeList[index], 'children', res.data[this.tabList[this.tabIndex].childType])
                }
            })
        },
        handleCancel() {
            this.$emit('cancel')
        },
        // 点击列表
        handleListTrack(item, index) {
            let type = this.tabIndex == 0 ? 'face' : 'vehicle';
            item.map(ite => {
                ite.headType = type;
            })
            this.setTrackList(item);
            this.setPeerData({
                'peerIndex': index,
                'type': 'trackmodal',
                'menuType': 'track'
            })
        },
        handlePackup() {
            this.footerUpDown = !this.footerUpDown;
        },
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.track-box{
    width: 370px;
    position: absolute;
    right: 10px;
    top: 10px;
    background: #fff;
    height: calc( ~'100% - 20px' );
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    filter: blur(0px);
    transition: height 0.2s ease-out;
    .tab-name{
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #D3D7DE;
        .tab-li{
            width: 20%;
            text-align: center;
            padding: 7px 0;
            cursor: pointer;
            font-size: 14px;
        }
        .active-tab{
            color: #2C86F8;
            border-bottom: 3px solid #2C86F8;
        }
    }
    .hint_title{
        margin: 10px 0 0 15px;
        font-size: 12px;
        color: rgba(0,0,0,0.6);
        span{
            color: rgba(44, 134, 248, 1);
        }
    }
    .box-content{
        padding: 10px 20px;
        height: calc( ~'100% - 140px' );
        overflow-y: auto;   
        .box-ul{
            height: 100%;
            position: relative;
            .box-li{ 
                transition: height 1s ease-out;
                margin-left: 7px;
                border-left: 1px solid #D3D7DE;
                position: relative;
                padding-bottom: 10px;
                .box-li-top{
                    display: flex;
                    color: #2C86F8;
                }
                .box-li-bottom{
                    .time-title{
                        flex: 1;
                        height: 20px;
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 0 10px;
                        background: rgba(211, 215, 222, .3);
                        margin-left: 11px;
                        cursor: pointer;
                        .time-date{
                            color: #2C86F8;
                            font-weight: bold;
                            font-size: 12px;
                        }
                        .time-num{
                            color: #F29F4C;
                            font-size: 12px;
                            margin-left: 10px;
                        }
                        .triangle {
                            width: 0;
                            height: 0;
                            border-left: 5px solid transparent;
                            border-right: 5px solid transparent;
                            border-top: 5px solid #888888;
                            margin-left: 10px;
                            transform: rotate(0deg);
                            transition: transform 0.2s;
                        }
                        .active-triangle{
                            transform: rotate(180deg);
                            transition: transform 0.2s;
                        }
                    }
                    .sec-radio{
                        width: 5px;
                        height: 5px;
                        background: #D3D7DE;
                        border-radius: 50%;
                        position: absolute;
                        left: -3px;
                    }
                    .child_list{
                        padding-left: 20px;
                        margin-top: 11px;
                        .content-top{
                            display: flex;
                            // overflow: auto;
                            cursor: pointer;
                            img{
                                width: 60px;
                                height: 60px;
                                border: 1px solid #CFD6E6;
                                position: relative;
                                .similarity {
                                    position: absolute;
                                    left: 0;
                                    top: 0;
                                    span {
                                        padding: 2px 5px;
                                        background: #4597ff;
                                        color: #fff;
                                        border-radius: 4px;
                                    }
                                }
                            }
                            .content-top-right{
                                margin-left: 11px;
                                // margin-top: 3px;
                                font-size: 14px;
                                flex: 1;
                                width: calc(  ~'100% - 60px' );
                                /deep/ .iconfont{
                                    margin-right: 5px;
                                }
                                .block{
                                    color: #000000;
                                }
                            }
                        }
                    }
                }
            }
            .box-li-pack{
                .box-li-bottom{
                    height: 20px;
                    overflow: hidden;
                    transition: height 1s ease-out;
                    .sec-radio{
                        display: none;
                    }
                }
            }
            .box-li-packend{
                border-left: none;
            }
        }

        .haed-dot{
            width: 15px;
            height: 15px;
            border-radius: 50%;
            // background: #2C86F8;
            border: 2px solid #2C86F8;
            display: flex;
            justify-content: center;
            align-items: center;
            div{
                width: 5px;
                height: 5px;
                background: #2C86F8;
                border-radius: 5px;
            }
        }
        /deep/.ivu-icon-md-radio-button-on{
            font-size: 20px;
            position: absolute;
            left: -10px;
            top: -2px;
            z-index: 36;
        }
        /deep/.ivu-icon-ios-radio-button-on{
            font-size: 10px;
            color: #D3D7DE;
        }
        /deep/ .ivu-timeline-item-head-custom{
            padding: 0;
        }
    }
    .footer{
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translate(-50%, 0px);
        background: #fff;
        width: 100%;
    }
}
.track-box-pack{
    height: 80px;
    transition: height 0.2s ease-out;
    overflow: hidden; 
}
</style>
