<!--
    * @FileDescription: 时间选择器
    * @Author: H
    * @Date: 2023/9/23
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-07-29 18:26:01
-->
<template>
  <div class="hl-daterange">
    <div class="hl-datePicker">
      <!-- <div class="hl-datepicker-input"> -->
      <Poptip
        placement="bottom"
        v-model="visible"
        width="200"
        :transfer="transfer"
        :popper-class="transfer ? 'hl-datepicker-transfer' : ''"
      >
        <div class="hl-datepicker-input">
          <input
            type="text"
            class=""
            readonly
            v-model="inputValue"
            :placeholder="placeholder"
            @input="handleInput($event.target.value)"
          />
          <Icon
            :class="{ dateIcon: inputValue }"
            type="ios-calendar-outline"
            class="icon-pos"
          />
          <!-- <i :class="{'dateIcon': inputValue}" class="iconfont el-icon-date icon-pos"></i> -->
          <Icon
            v-show="inputValue"
            @click.stop="handleDele"
            class="icon-pos el-icon-error"
            type="md-close-circle"
          />
          <!-- <i v-show="inputValue" @click.stop="handleDele" class="iconfont el-icon-error icon-pos"></i> -->
        </div>

        <div class="picker-write-container" slot="content">
          <div class="picker-write-main">
            <div class="picker-write-input">
              <div class="date-item">
                <input
                  type="number"
                  v-model="date.year"
                  min="0"
                  max="9999"
                  @wheel="handleWheel($event, 'year')"
                />
                <label>年</label>
              </div>
              <div class="date-item">
                <input
                  type="number"
                  v-model="date.month"
                  min="1"
                  max="12"
                  @wheel="handleWheel($event, 'month')"
                />
                <label>月</label>
              </div>
              <div class="date-item">
                <input
                  type="number"
                  v-model="date.day"
                  min="1"
                  max="31"
                  @wheel="handleWheel($event, 'day')"
                />
                <label>日</label>
              </div>
            </div>
            <div class="picker-write-input">
              <div class="date-item">
                <input
                  type="number"
                  v-model="date.hours"
                  min="0"
                  max="23"
                  @wheel="handleWheel($event, 'hours')"
                />
                <label>时</label>
              </div>
              <div class="date-item">
                <input
                  type="number"
                  v-model="date.minutes"
                  min="0"
                  max="59"
                  @wheel="handleWheel($event, 'minutes')"
                />
                <label>分</label>
              </div>
              <div class="date-item">
                <input
                  type="number"
                  v-model="date.seconds"
                  min="0"
                  max="59"
                  @wheel="handleWheel($event, 'seconds')"
                />
                <label>秒</label>
              </div>
            </div>
          </div>
          <div class="picker-footer">
            <div class="present-date" @click="handlePresent('')">
              <!-- <i class="iconfont el-icon-date"></i> -->
              <Icon type="ios-calendar-outline" class="fontsize" />
              <span>当前时间</span>
            </div>
            <p class="hl-btn" @click="handleAffirmTime">确定</p>
          </div>
        </div>
      </Poptip>
      <!-- </div> -->
    </div>
  </div>
</template>

<script>
import { getConfigDate } from "@/util/modules/common";
export default {
  name: "",
  props: {
    placeholder: {
      type: String,
      default: "请输入",
    },
    value: {
      type: String,
      default: "",
    },
    transfer: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
  data() {
    return {
      inputValue: "",
      date: {
        year: "",
        month: "",
        day: "",
        hours: "",
        minutes: "",
        seconds: "",
      },
      visible: false,
    };
  },
  watch: {
    inputValue(val) {
      this.$emit("input", val);
    },
    value: {
      handler(val) {
        this.inputValue = val;
        this.factorsTime(val);
      },
      immediate: true,
    },
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleWheel(event, type) {
      event.preventDefault();
      let input = event.target;
      let delta = event.deltaY || event.datail || event.wheelDelta;
      if (delta > 0) {
        input.stepDown();
        this.date[type] = input.value;
      } else {
        input.stepUp();
        this.date[type] = input.value;
      }
    },
    // 确认
    handleAffirmTime() {
      this.visible = false;
      let emptyValue = true;
      for (let key in this.date) {
        if (!this.date[key]) {
          emptyValue = false;
        }
      }
      if (!emptyValue) {
        this.handlePresent();
      }
      let dayLength = this.getMonthDays(this.date.year, this.date.month);
      if (this.date.day > dayLength) {
        this.date.day = dayLength;
      }
      if (this.date.month > 12) {
        this.date.month = 12;
      }
      if (this.date.hours > 23) {
        this.date.hours = 23;
      }
      if (this.date.minutes > 59) {
        this.date.minutes = 59;
      }
      if (this.date.seconds > 59) {
        this.date.seconds = 59;
      }
      let time = this.date;
      let M = this.timeJudge(time.month);
      let D = this.timeJudge(time.day);
      let h = this.timeJudge(time.hours);
      let m = this.timeJudge(time.minutes);
      let s = this.timeJudge(time.seconds);
      this.inputValue = `${time.year}-${M}-${D} ${h}:${m}:${s}`;
      this.$emit("affirm", this.inputValue);
    },
    timeJudge(time) {
      let dismsg = time;
      if (time < 10 && time.length == 1) {
        dismsg = "0" + time;
      }
      return dismsg;
    },
    // 当前时间
    handlePresent() {
      let time = this.dateTime();
      this.date = {
        year: time[0],
        month: time[1],
        day: time[2],
        hours: time[3],
        minutes: time[4],
        seconds: time[5],
      };
    },
    //
    factorsTime(val) {
      let time = this.dateTime(val);
      this.date = {
        year: time[0],
        month: time[1],
        day: time[2],
        hours: time[3],
        minutes: time[4],
        seconds: time[5],
      };
    },
    handleDele() {
      this.inputValue = "";
      this.$emit("affirm", this.inputValue);
    },
    handleInput(value) {},
    // 获取当前时间
    dateTime(timestamp = "") {
      let date = new Date();
      if (timestamp) {
        date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      }
      var Y = date.getFullYear();
      var M =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      var D = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      var h = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      var m =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      var s =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      return [Y, M, D, h, m, s];
    },
    // 根据年份判断天数
    getMonthDays(year, month) {
      return (
        [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][Number(month) - 1] ||
        (this.isLeapYear(year) ? 29 : 28)
      );
    },
    // 是否是闰年
    isLeapYear(year) {
      if (year % 4 == 0 && year % 100 != 0) {
        return true;
      } else if (year % 400 == 0) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>

<style lang='less' scoped>
.hl-daterange {
  .hl-datePicker {
    width: 100%;
    position: relative;
    /deep/ .ivu-poptip-rel {
      width: 100%;
    }
    .hl-datePicker-content();
  }
}
.hl-datePicker-content {
  .picker-write-container {
    min-width: 200px;
    .picker-write-main {
      .picker-write-input {
        display: flex;
        margin: 10px 0;
        .date-item {
          display: flex;
          align-items: center;
          justify-content: space-around;
          flex: 1;
          input {
            text-align: center;
            height: 22px;
            border: 1px solid #a5b0b6;
            width: calc(~" 100% - 24px ");
          }
          input[type="number"] {
            outline: none;
          }
        }
      }
    }
    .picker-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .present-date {
        cursor: pointer;
        color: #a5b0b6;
        span {
          margin-left: 4px;
        }
        &:active {
          color: #2c86f8;
        }
      }
      .hl-btn {
        min-width: 44px;
        height: 26px;
        background: #2c86f8;
        color: #fff;
        border: none;
        border-radius: 2px;
        cursor: pointer;
        text-align: center;
        line-height: 26px;
      }
    }
  }
  .hl-datepicker-input {
    width: 100%;
    input {
      width: 100%;
      padding: 0 24px 0 10px;
      border: 1px solid gainsboro;
      border-radius: 4px;
      color: #515a6e;
    }
    input:focus {
      // border: none;
      outline: none;
      border-color: #2c86f8;
    }
    input::-webkit-input-placeholder {
      color: rgba(0, 0, 0, 0.35);
    }
    input:-moz-placeholder,
    input::-moz-placeholder {
      color: rgba(0, 0, 0, 0.35);
    }
    input:-ms-input-placeholder {
      color: rgba(0, 0, 0, 0.35);
    }
    &:hover {
      .el-icon-error {
        display: block;
      }
      .dateIcon {
        display: none;
      }
    }
  }
  .icon-pos {
    position: absolute;
    right: 0px;
    top: 50%;
    transform: translate(-50%, -50%);
    color: #a5b0b6;
    cursor: pointer;
    font-size: 22px;
  }
  .el-icon-error {
    font-size: 22px;
    display: none;
  }
  .el-icon-date {
    display: inline-block;
  }
  .ivu-poptip {
    width: 100%;
    .ivu-poptip-rel {
      width: 100%;
    }
  }
}
.hl-datepicker-transfer {
  .hl-datePicker-content();
}
/deep/.ivu-poptip-popper {
  width: 240px !important;
}
/deep/.ivu-poptip-body {
  overflow: hidden !important;
  height: 120px !important;
  .ivu-poptip-body-content {
    height: inherit;
    overflow: hidden !important;
  }
}
/*在Chrome 浏览器下 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
/*在 Firefox 浏览器下 */
input[type="number"] {
  -moz-appearance: textfield;
}
.fontsize {
  font-size: 22px;
}
</style>
<style lang="less">
.hl-datepicker-transfer {
  .ivu-poptip-content {
    width: 240px !important;
    .ivu-poptip-body {
      overflow: hidden;
      height: 120px;
      .ivu-poptip-body-content {
        height: inherit;
        overflow: hidden;
      }
    }
  }
}
</style>