<!--
    * @FileDescription: 搜索结果
    * @Author: H
    * @Date: 2024/01/31
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-11 15:16:15
 -->
<template>
  <div
    class="rightBox"
    :style="{ top: top + 'px' }"
    :class="{ 'rightBox-pack': packUpDown }"
  >
    <div class="rightBox-page">
      <div class="title">
        <p>{{ title }}</p>
        <Icon type="ios-close" @click="handleCancel" />
      </div>
      <div class="hint_title">
        共<span> {{ total }} </span> 条检索结果
      </div>
      <ul
        class="box-content"
        v-infinite-scroll="load"
        :infinite-scroll-distance="10"
        infinite-scroll-disabled="loadingText"
      >
        <li
          class="box-list"
          v-for="(item, index) in facePeerCount"
          :key="index"
        >
          <div class="box-number">
            {{ index + 1 }}
          </div>
          <div class="box-right">
            <div class="content-top">
              <div class="content-top-img">
                <!-- <ui-image viewer :src="item.sceneImg" /> -->
                <img v-lazy="item.traitImg" alt="" />
              </div>
              <div class="content-top-right">
                <div class="content-top-right-name">
                  <span class="ellipsis flex">
                    <ui-icon type="xingming" :size="14"></ui-icon>
                    <span class="block">{{ item.xm || "--" }}</span>
                  </span>
                  <p class="list_title" @click="handlePeer($event, item)">
                    出现<span>{{ item.appearCount }}</span
                    >次
                  </p>
                </div>
                <span class="ellipsis">
                  <ui-icon type="shenfenzheng" :size="14"></ui-icon>
                  <span class="bule" :class="{ block: !item.idCardNo }">{{
                    item.idCardNo || "--"
                  }}</span>
                </span>
                <span class="ellipsis">
                  <ui-icon type="camera" :size="14"></ui-icon>
                  <span class="orange" :class="{ block: !item.vid }">{{
                    item.vid || "--"
                  }}</span>
                </span>
              </div>
            </div>
          </div>
        </li>
        <ui-empty
          v-if="facePeerCount.length === 0 && loading == false"
        ></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </ul>
      <div
        class="footer"
        :class="{ packArrow: packUpDown }"
        @click="handlePackup"
      >
        <img :src="packUrl" alt="" />
        <p>{{ packUpDown ? "展开" : "收起" }}</p>
      </div>
      <p class="loading" v-if="loadingText">加载中...</p>
      <p class="loading" v-if="noMore && facePeerCount.length != 0">
        没有更多了
      </p>
    </div>
  </div>
</template>

<script>
import operaFloor from "../../../components/operat-floor/index.vue";
import { analysisPageList } from "@/api/modelMarket";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "检索结果",
    },
    marginTop: {
      type: Number,
      default: 0,
    },
  },
  components: {
    operaFloor,
  },
  data() {
    return {
      // title: '对象信息',
      facePeerCount: [],
      removableTop: 0,
      objModal: false,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      loading: false,
      // loadingText: '加载中',
      total: 0,
      loadingText: false,
      noMore: false,
      packUpDown: false,
      searchInfo: {},
    };
  },
  watch: {
    marginTop: {
      handler(val) {},
      immediate: true,
    },
  },
  computed: {
    top() {
      return this.marginTop + 20;
    },
    scrollHeight() {
      let htmlFontSize = parseFloat(
        window.document.documentElement.style.fontSize
      );
      if (!!htmlFontSize) {
        return htmlFontSize * (450 / 192);
      }
      return 450;
    },
  },
  created() {},
  mounted() {},
  methods: {
    init(val) {
      this.packUpDown = false;
      this.loading = true;
      this.page = {
        pageNumber: 1,
        pageSize: 10,
      };
      this.searchInfo = val;
      this.facePeerCount = [];
      this.rightList();
    },
    rightList() {
      let params = {
        ...this.searchInfo,
        ...this.page,
      };
      analysisPageList(params)
        .then((res) => {
          let list = (res.data && res.data.entities) || [];
          this.facePeerCount = this.facePeerCount.concat(list);
          this.facePeerCount.sort((a, b) => b.appearCount - a.appearCount);
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
          this.noMore = false;
          this.loadingText = false;
        });
    },
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= this.page.pageSize) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        setTimeout(() => {
          this.noMore = false;
        }, 1000);
        return;
      } else {
        this.loadingText = true;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.rightList();
      }
    },
    handleCancel() {
      this.$emit("cancel");
    },
    // 详情
    handlePeer($event, item) {
      $event.stopPropagation();
      this.$emit("details", item);
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    // 加密身份证
    hiddenId(str, frontLen, endLen) {
      var len = str.length - frontLen - endLen;
      var xing = "";
      for (var i = 0; i < len; i++) {
        xing += "*";
      }
      return (
        str.substring(0, frontLen) + xing + str.substring(str.length - endLen)
      );
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.rightBox {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 0;
  background: #fff;
  height: calc(~"100% - 40px");
  transition: height 0.2s ease-out;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  &-page {
    height: 100%;
    width: 100%;
  }
  .hint_title {
    margin: 10px 0 0 15px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    span {
      color: rgba(44, 134, 248, 1);
    }
  }
  .box-content {
    min-height: 240px;
    // height: 400px;
    height: calc(~"100% - 110px");
    padding: 0px 15px 0px 15px;
    margin: 10px 0 0px;
    position: relative;
    overflow-y: auto;
    .box-list {
      margin-bottom: 10px;
      display: flex;
      // cursor: pointer;
      .box-number {
        width: 24px;
        height: 24px;
        color: #f29f4c;
        border-radius: 50%;
        border: 2px solid #f29f4c;
        line-height: 20px;
        text-align: center;
        margin-top: 34px;
        margin-right: 8px;
      }

      .box-right {
        background: #f9f9f9;
        flex: 1;
        padding: 5px 10px 0px 5px;
        &:hover {
          background: rgba(44, 134, 248, 0.1);
        }
        .content-top {
          display: flex;
          &-img {
            width: 80px;
            height: 80px;
            background: #f9f9f9;
            border: 1px solid #d3d7de;
            display: flex;
            justify-content: center;
            align-items: center;
            img {
              width: auto;
              height: auto;
              max-height: 80px;
              max-width: 80px;
            }
          }
          .content-top-right {
            margin-top: 3px;
            margin-left: 11px;
            font-size: 14px;
            width: calc(~"100% - 91px");
            /deep/ .iconfont {
              margin-right: 5px;
            }
            .bule {
              color: #2c86f8;
            }
            .orange {
              color: #f29f4c;
            }
            .block {
              color: #000000;
            }
            &-name {
              display: flex;
              margin-bottom: 8px;
              .flex {
                flex: 1;
              }
              .list_title {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.6);
                cursor: pointer;
                span {
                  color: rgba(44, 134, 248, 1);
                }
              }
            }
          }
        }
        .content-bottom {
          display: flex;
          justify-content: space-between;
          margin-top: 5px;
          .iconList {
            width: 80px;
          }
          .analyseIcon {
            font-size: 12px;
            color: #5584ff;
            cursor: pointer;
          }
        }
      }
    }
  }
  .footer {
    // color: #000000;
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 96%;
  }
}
.rightBox-pack {
  height: 80px;
  transition: height 0.2s ease-out;
  overflow: hidden;
}
</style>
