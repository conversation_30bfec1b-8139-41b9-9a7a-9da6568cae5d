<template functional>
  <div class="ui-frame">
    <div class="corner leftTop"></div>
    <div class="corner rightTop"></div>
    <div class="corner bottomLeft"></div>
    <div class="corner bottomRight"></div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {},
};
</script>
<style lang="less" scoped>
.ui-frame {
  position: relative;
  display: inline-block;
  padding: 10px;
  .corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-primary);
  }
  .leftTop {
    top: 0;
    left: 0;
    border-right: 0;
    border-bottom: 0;
  }
  .rightTop {
    top: 0;
    right: 0;
    border-left: 0;
    border-bottom: 0;
  }
  .bottomLeft {
    bottom: 0;
    left: 0;
    border-top: 0;
    border-right: 0;
  }
  .bottomRight {
    bottom: 0;
    right: 0;
    border-left: 0;
    border-top: 0;
  }
}
</style>
