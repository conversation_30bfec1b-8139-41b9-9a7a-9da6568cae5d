<template>
  <div class="area-dom-wrapper">
    <div class="dom" v-if="mapDomAreaData">
      <section class="dom-content">
        <p class="dom-content-p">
          <span class="label">纬度:</span>
          <span class="message">{{ mapDomAreaData.lat }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">经度:</span>
          <span class="message">{{ mapDomAreaData.lon }}</span>
        </p>
        <p class="dom-content-p">
          <span class="label">地址:</span>
          <span class="message">{{ mapDomAreaData.address }}</span>
        </p>
      </section>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    mapDomAreaData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  }
}
</script>
<style lang="less" scoped>
.area-dom-wrapper {
  position: relative;
  padding: 10px 28px 25px 0;
  height: 100%;
}
.dom {
  background: #fff;
  width: 250px;
  border-radius: 5px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  .dom-content {
    padding: 10px 20px;
    .dom-content-p {
      margin-top: 6px;

      height: 24px;
      line-height: 24px;
      display: flex;
      position: relative;
      font-size: 14px;

      .label {
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 40px;
        font-size: 14px;
      }
      .message {
        width: 180px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
.dom:before,
.dom:after {
  content: '';
  display: block;
  border-width: 8px;
  position: absolute;
  bottom: 9px;
  left: 118px;
  border-style: solid dashed dashed;
  border-color: #fff transparent transparent;
  font-size: 0;
  line-height: 0;
}
</style>
