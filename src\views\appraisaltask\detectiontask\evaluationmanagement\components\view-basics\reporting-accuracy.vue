<template>
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content">
      <div class="container">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="异常数据列表"></line-title>
        <ui-select-tabs class="list_item"></ui-select-tabs>
        <div class="list">
          <ui-table
            class="ui-table"
            :table-columns="tableColumns"
            :table-data="tableData"
            :minus-height="minusTable"
            :loading="loading"
          >
          </ui-table>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }

  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 860px;
    max-height: 860px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
    .page {
      position: absolute;
      bottom: 5px;
    }
  }
}
</style>
<script>
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '600px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['', ''],
        showData: [
          { name: '合格设备', value: 0 },
          { name: '不合格设备', value: 0 },
        ],
        zdryTimer: null,
        totalNum: 20000,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '填报准确率',
        rateValue: '66%',
        price: '达标值',
        priceValue: '100%',
        result: '考核结果',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: `${this.global.filedEnum.deviceId}`, key: 'indexName' },
        { title: `${this.global.filedEnum.deviceName}`, key: 'indexType' },
        { title: '组织机构', key: 'calculateMethod' },
        { title: `${this.global.filedEnum.longitude}`, key: 'criterion' },
        { title: `${this.global.filedEnum.latitude}`, key: 'standardsValue' },
        { title: `${this.global.filedEnum.macAddr}`, key: 'examineResult' },
        { title: this.global.filedEnum.ipAddr, key: 'examineResult' },
        { title: '数据来源', key: 'examineResult' },
        { title: '安装地址', key: 'examineResult' },
        { title: '操作', slot: 'option' },
      ],
      tableData: [],
      minusTable: 500,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '8rem',
      },
      visible: false,
      loading: false,
      detailInfo: {},
      detailList: [],
      modalId: { title: '测评结果' },
      indexList: {},
    };
  },
  mounted() {
    this.initRing();
  },

  methods: {
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      let showData = this.zdryChartObj.showData;
      let formatData = {
        seriesName: '测评',
        xAxisData: xAxisData,
        showData: showData,
        totalNum: this.zdryChartObj.totalNum,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    // getExport(id) {
    //   this.$http
    //     .get(
    //       governanceevaluation.getExportResultByIds,

    //       { responseType: "blob", params: { id: id } }
    //     )
    //     .then((res) => {
    //       let blob = new Blob([res.data], {
    //         type: "application/vnd.ms-excel;charset=UTF-8",
    //       });
    //       let url = window.URL.createObjectURL(blob);
    //       let aLink = document.createElement("a");
    //       //获取heads中的filename文件名
    //       let temp = res.headers["content-disposition"]
    //         .split(";")[1]
    //         .split("filename=")[1];
    //       let fileName = decodeURIComponent(temp);
    //       aLink.setAttribute("download", fileName);
    //       aLink.style.display = "none";
    //       aLink.href = url;
    //       document.body.appendChild(aLink);
    //       aLink.click();
    //       document.body.removeChild(aLink);
    //       window.URL.revokeObjectURL(url);
    //     })
    //     .catch((error) => {
    //       console.log(error);
    //     });
    // },
    // init(row) {
    //   try {
    //     this.loading = true;
    //     this.$http
    //       .get(governanceevaluation.getPageResults, {
    //         params: {
    //           orgCode: row.orgCode,
    //           pageNumber: this.searchData.pageNum,
    //           pageSize: this.searchData.pageSize,
    //         },
    //       })
    //       .then((res) => {
    //         this.detailInfo = {
    //           ...res.data.data,
    //         };
    //         const { data } = res.data;
    //         this.tableData = data.entities.map((e) => {
    //           return {
    //             ...e,
    //             isShow: false,
    //           };
    //         });
    //         this.searchData.totalCount = data.total;
    //       })

    //       .finally(() => {
    //         this.loading = false;
    //       });
    //   } catch (err) {
    //     console.log(err);
    //   }
    // },

    // getIndexList(row) {
    //   console.log(row, "row55555555");
    //   try {
    //     this.loading = true;
    //     this.$http
    //       .get(governanceevaluation.getOrgStatResult, { params: { orgCode: row.orgCode } })
    //       .then((res) => {
    //         this.indexList = res.data.data;
    //       })

    //       .finally(() => {
    //         this.loading = false;
    //       });
    //   } catch (err) {
    //     console.log(err);
    //   }
    // },
    // show_detail(index) {
    //   console.log(index, "444444444");
    //   const { isShow } = this.tableData[index];
    //   this.tableData[index].isShow = !isShow;
    // },
    changePage(val) {
      this.searchData.pageNum = val;
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
    },
    operationTask() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    uiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    lineTitle: require('@/components/line-title').default,
  },
};
</script>
