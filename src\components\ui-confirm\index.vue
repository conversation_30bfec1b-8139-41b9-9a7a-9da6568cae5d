<script type="jsx">
export default {
  name: 'ui-confirm',
  props: {},
  data() {
    return {
      visible: false,
      title: '',
      content: '',
      loading: false,
      okText: '',
      callback: null,
    };
  },
  created() {},
  render() {
    const contentDom = this.renderDom ? this.renderDom() : <span><i class="icon-font icon-jinggao warning"></i>
        <span class="desc">{this.content}</span></span>
    return <ui-modal
      vModel={this.visible}
      loading={this.loading}
      title= {this.title}
      ref="modal"
      {...{class: ['modal']}}
    >
      <div class="content-wrapper">{contentDom}</div>
      <div slot="footer">
        <Button {...{
          on: {
            'click': ()=> {
              this.visible = false
              this.onCancel()
            }
          }
        }} class="plr-30">取 消</Button>
        <Button
          type="primary"
          {...{
            on: {
              'click': this.onOK
            }
          }}
          loading={this.loading}
          class="plr-30">
          <span>{this.okText}</span>
          </Button>
      </div>
    </ui-modal>
  },
  methods: {
    onCancel() {
      this.callback(false);
    },
    onOK() {
      this.callback(true);
    },
  },
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.modal {
  text-align: center;

  @{_deep} .ivu-modal-header {
    padding: 0;
  }

  .content-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80px;

    .warning {
      color: var(--color-warning);
    }

    .desc {
      margin-left: 10px;
      color: var(--color-table-header-th);
    }
  }
}
</style>
