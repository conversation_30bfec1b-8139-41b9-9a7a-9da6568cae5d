<template>
  <div>
    <ui-modal
      ref="modal"
      v-model="visible"
      :title="title"
      :loading="loading"
      class="modal"
      @onCancel="onCancel"
      @onOk="onOK"
    >
      <div class="content-wrapper">
        <i class="iconfont icon-yunhangchucuo warning"></i>
        <span class="desc">{{ content }}</span>
      </div>
    </ui-modal>
  </div>
</template>

<script>
export default {
  name: 'UiConfirm',
  components: {},
  props: {},
  data () {
    return {
      visible: false,
      title: '',
      content: '',
      loading: false
    }
  },
  watch: {},
  created () {},
  methods: {
    onCancel () {
      this.callback(false)
    },
    onOK () {
      this.callback(true)
    }
  }
}
</script>

<style lang="less" scoped>
.modal {
  // text-align: center;

  // @{_deep} .ivu-modal-header {
  //   padding: 0;
  // }
  /deep/ .ivu-modal-body {
    min-height: 20px !important;
    padding-top: 24px !important;
    padding-bottom: 48px !important;
  }
  .content-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    // height: 80px;

    .warning {
      color: #BC3C19;
    }

    .desc {
      margin-left: 10px;
      color: #ffffff;
    }
  }
}
</style>
