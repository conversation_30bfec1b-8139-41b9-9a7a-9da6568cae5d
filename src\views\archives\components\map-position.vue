<template>
  <div class="map-box">
    <div class="map" :id="mapId"></div>
    <map-dom ref="mapDom" :map-dom-data="mapDomData" class="mapDom"></map-dom>
    <template v-for="(e, i) in positionsList">
      <map-dom-area ref="mapDomArea" :key="i" :mapDomAreaData="{ ...e }"></map-dom-area>
    </template>
    <right-button-group @pointMap="pointMap"> </right-button-group>
    <full-screen ref="full-screen"></full-screen>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
import { NPGisMapMain } from '@/map/map.main';
import { mapGetters, mapActions } from 'vuex';
let mapMain = null;
let infoWindowArr = [];
export default {
  components: {
    MapDom: require('./map-dom').default,
    RightButtonGroup: require('@/components/map-base/right-button-group.vue').default,
    MapDomArea: require('./map-dom-area').default,
    FullScreen: require('./full-screen').default,
  },
  props: {
    id: {
      type: String,
      default: '',
    },
    chooseMapItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  watch: {
    chooseMapItem: {
      handler(val) {
        let point = {};
        point.lat = val.latitude;
        point.lon = val.longitude;
        this.selectItem(point);
      },
    },
  },
  data() {
    return {
      allCameraList: [],
      mapId: 'mapId' + Math.random(),
      mapDomData: {},
      positionsList: [],
      position: {},
    };
  },
  computed: {
    ...mapGetters({
      mapConfig: 'common/getMapConfig',
      mapStyle: 'common/getMapStyle',
    }),
  },
  async created() {
    this.allCameraList.push({
      ...this.chooseMapItem,
      active: true,
    });
  },
  async mounted() {
    await this.getDeviceMapPlace();
    await this.getMapConfig();
    this.loading = false;
  },

  methods: {
    ...mapActions({
      setMapConfig: 'common/setMapConfig',
      setMapStyle: 'common/setMapStyle',
    }),
    async getDeviceMapPlace() {
      try {
        const { id } = this;
        let res = await this.$http.get(equipmentassets.getDevicMapPlace, {
          params: { id },
        });
        const { data } = res.data;
        this.positionsList = [...data];
        let cameraListArray = [];
        if (this.positionsList && this.positionsList.length) {
          this.positionsList.forEach((e) => {
            let { deviceInfoVoList } = e;
            if (deviceInfoVoList && deviceInfoVoList.length) {
              deviceInfoVoList.forEach((item) => {
                cameraListArray.push({
                  ...item,
                  sbgnlx: undefined,
                });
              });
            }
          });
          this.allCameraList = [...this.allCameraList, ...cameraListArray];
        }
      } catch (err) {
        console.log(err);
      }
    },
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig, this.mapStyle);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      // 配置初始化层级
      mapMain = new NPGisMapMain();
      let mapId = this.mapId;
      mapMain.init(mapId, data, style);
      // 初始化自定义区域框选
      if (this.positionsList.length !== 0) {
        this.positionsList.forEach((e) => {
          const { position } = e;
          this.drawPosition(JSON.parse(position));
          this.position = JSON.parse(position);
        });
      }
      if (this.allCameraList.length !== 0) {
        this._initSystemPoints2Map(this.allCameraList);
      }
      // 当前设备信息弹框 展开
      this.selectPoint();
      // 添加区域弹框
      this.addAreaLayer();
    },
    // 加载点位到地图上
    _initSystemPoints2Map(points) {
      if (mapMain) {
        // 加载点位
        mapMain.renderMarkers(mapMain.convertSystemPointArr2MapPoint(points), this.getMapEvents());
      }
    },
    getMapEvents() {
      let opts = {
        click: (marker) => {
          let point = {};
          point.lat = marker.ext.Lat;
          point.lon = marker.ext.Lon;
          this.selectItem(point);
        },
      };
      return opts;
    },
    closeAllInfoWindow() {
      infoWindowArr.forEach((row) => {
        row.close();
      });
    },

    selectItem(pointItem) {
      // 显示之前先清除其他提示框
      this.closeAllInfoWindow();
      let point = new NPMapLib.Geometry.Point(pointItem.lon, pointItem.lat);
      mapMain.map.centerAndZoom(point, 17);
      let opts = {
        width: 50, // 信息窗宽度
        height: 100, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(-18, -95), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      let infoWindow = new NPMapLib.Symbols.InfoWindow(point, null, null, opts);
      this.mapDomData = pointItem;
      let dom = this.$refs.mapDom.$el;
      infoWindow.setContentDom(dom);
      mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      infoWindowArr.push(infoWindow);
    },
    drawPosition(position) {
      switch (position.type) {
        case 'rectangle':
          mapMain.drawRectangle(position);
          break;
        case 'polygon':
          mapMain.drawPolygon(position);
          break;
        case 'circle':
          mapMain.drawCircle(position);
          break;
        default:
          console.log('还不支持此图形');
          break;
      }
    },
    // 当前设备弹框信息内容
    selectPoint() {
      const { longitude: lon, latitude: lat } = this.chooseMapItem;
      this.selectItem({ lon, lat }, false);
    },
    pointMap(type) {
      this[`${type}Map`]();
    },
    homingMap() {
      this.selectPoint();
    },
    // 固定放大地图
    enlargeMap() {
      mapMain.map.zoomInFixed();
    },
    // 固定缩小地图
    narrowMap() {
      mapMain.map.zoomOutFixed();
    },

    // 计算区域弹框位置
    calculationLocation(points) {
      return points.reduce((prev, current) => (prev.lat > current.lat ? prev : current));
    },

    // 添加区域图层
    addAreaLayer() {
      const infoWindowArr = [];
      this.positionsList.forEach((e, i) => {
        const { points } = JSON.parse(e.position);
        const pointItem = this.calculationLocation(points);
        let point = new NPMapLib.Geometry.Point(pointItem.lon, pointItem.lat);
        let opts = {
          width: 50,
          height: 100,
          offset: new NPMapLib.Geometry.Size(-5, -115),
          iscommon: true,
          enableCloseOnClick: false,
        };
        let infoWindow = new NPMapLib.Symbols.InfoWindow(point, null, null, opts);
        let mapDomAreaDomArray = this.$refs['mapDomArea'];
        let dom = mapDomAreaDomArray[i].$el;
        infoWindow.setContentDom(dom);
        infoWindowArr.push(infoWindow);
      });
      mapMain.map.addOverlays(infoWindowArr);
    },
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  padding: 0 18px;
  margin-bottom: 18px;
  height: 454px;
  overflow: hidden;
  position: relative;
  .map {
    height: 100%;
    position: relative;
  }
  @{_deep}.button-group {
    top: 285px;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
