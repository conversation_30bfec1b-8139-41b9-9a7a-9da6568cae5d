<template>
  <div class="title-center">
    <div class="conter-center">
      <span>{{ title }}</span>
    </div>
  </div>
</template>
<style lang="less" scoped>
.title-center {
  height: 100%;
  .conter-center {
    height: 30px;
    padding: 0 100px;
    font-size: 16px;
    font-weight: 400;
    display: inline-block;
    vertical-align: middle;
    background: linear-gradient(to right, #08264d 0%, #114076 50%, #08264d 100%);
    opacity: 1;
    span {
      line-height: 30px;
      height: 100%;
      color: #fff;
      display: inline-block;
    }
  }
}
</style>
<script>
export default {
  data() {
    return {};
  },
  props: {
    title: {},
  },
};
</script>
