<template>
  <div class="ui-card m-b20">
    <div class="card-head">
      <p class="capture-title face-capture capture-active">
        <span>人员统计</span>
      </p>
    </div>
    <div class="card-content">
      <div class="statistics-box">
        <div class="left-main-box">
          <div class="item-statistics-box">
            <p class="count-num">{{ activeTotal }}</p>
            <p class="data-name">活动人员</p>
          </div>
        </div>
        <div class="right-sub-box">
          <Row :gutter="18" :style="{ rowGap: '14px' }" :justify="center">
            <Col
              :span="12"
              v-for="(item, index) of statisticsData"
              :key="index"
            >
              <div class="item-statistics-box">
                <p class="count-num">{{ item.num }}</p>
                <p class="data-name">{{ item.title }}</p>
              </div>
            </Col>
          </Row>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getStatisticsPageList,
  personLabelStatistics,
} from "@/api/monographic/community-management.js";
export default {
  name: "PlacePeopleStatistics",
  props: {},
  data() {
    return {
      activeTotal: 0,
      statisticsData: [
        {
          title: "已实名",
          num: 0,
        },
        {
          title: "未实名",
          num: 0,
        },
        {
          title: "孤寡老人",
          num: 0,
          key: "3000",
        },
        {
          title: "重点人员",
          num: 0,
          key: "3005",
        },
      ],
    };
  },
  mounted() {
    this.getNowDateStatistics();
  },
  methods: {
    // 社区实有人口统计
    getNowDateStatistics() {
      getStatisticsPageList({ placeId: this.$route.query.archiveNo }).then(
        (res) => {
          this.activeTotal = res?.data?.total || 0;
          this.statisticsData[0].num = res?.data?.realNameNumber || 0;
          this.statisticsData[1].num = res?.data?.noRealNameNumber || 0;
        }
      );
      personLabelStatistics(this.$route.query.archiveNo).then((res) => {
        this.statisticsData[2].num = res.data[this.statisticsData[2].key];
        this.statisticsData[3].num = res.data[this.statisticsData[3].key];
      });
    },
  },
};
</script>

<style lang="less" scoped>
.capture-title {
  background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
  color: #fff;
  font-weight: bold !important;
  font-size: 16px;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  transform: skewX(-18deg);
  padding: 0 23px;
  left: -6px;
  position: relative;
  span {
    transform: skewX(18deg);
    display: inline-block;
  }
}
.statistics-box {
  width: 100%;
  display: flex;

  padding: 20px;
  .left-main-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .right-sub-box {
    margin-left: 20px;
  }
  .item-statistics-box {
    margin: 0 auto;
    width: 126px;
    height: 70px;
    background: url("~@/assets/img/importantPerson-management/analysis-item-bg.png")
      no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .count-num {
      font-weight: 700;
      font-size: 25px;
      color: #2c86f8;
      margin-bottom: 5px;
    }
    .data-name {
      font-weight: 400;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }
  }
}
</style>
