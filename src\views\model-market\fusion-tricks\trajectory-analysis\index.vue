<!--
    * @FileDescription: 轨迹分析
    * @Author: H
    * @Date: 2022/12/19
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="trajectory-analysis">
    <!-- 地图 -->
    <mapCustom ref="mapBase" :sectionName='mapName' mapType="trajectory" :trackList='trackPointList' />

    <!-- 地图轨迹 -->
    <mapTrack ref="mapTrack" v-if="trackSearch" @mapline="handleMapline"></mapTrack>
    <leftBox ref='leftbox' :selectTabIndex="selectTabIndex" @reset="handleReset" @searchTrack="handleSearchTrack"></leftBox>
    <!-- 出行轨迹 -->
    <travelPath v-if="travelPage" @cancel="handleTravelCancel"></travelPath>
    <!-- 轨迹信息 -->
    <rightBox ref='rightBox' v-if="rightShowList" @tabClick="handleTabClick" @cancel="handleRightCancel"></rightBox>
    <!-- 时序图 -->
    <sequenceChart v-if="false"></sequenceChart>
    <!-- <div id="echarts" class="echarts" ref="echart"> </div> -->
  </div>
</template>

<script>
import mapTrack from './components/mapTrack.vue';
import leftBox from './components/leftBox.vue';
import rightBox from './components/rightBox.vue';
import sequenceChart from './components/sequenceChart.vue';
import travelPath from './components/travelPath.vue';
import { mapMutations } from 'vuex';
import mapCustom from '../../components/map/index.vue';
export default {
  name: 'trajectory-analysis',
  components: {
    mapTrack,
    leftBox,
    rightBox,
    sequenceChart,
    travelPath,
    mapCustom
  },
  props: {
    // 默认选中的类型
    selectTabIndex: {
      type: Number,
      default: -1
    }
  },
  data () {
    return {
      rightShowList: false,
      travelPage: false,
      trackSearch: false,
      typaTag: {},
      searchData: {}, //搜索条件
      trackPointList: {},
      mapName: '',
    }
  },
  watch: {

  },
  computed: {
  },
  created () {
  },
  mounted () {
  },
  activated () {
    this.setLayoutNoPadding(true)
  },
  deactivated () {
    this.setLayoutNoPadding(false)
  },
  methods: {
    ...mapMutations({
      setPeerList: 'map/setPeerList',
    }),
    ...mapMutations('admin/layout', ['setLayoutNoPadding']),
    // 左侧对象信息box
    handleReset () {
      this.resetPoint();
      this.rightShowList = false;
      this.trackSearch = false;
      this.mapName = ''
    },
    // 
    handleRightCancel () {
      this.rightShowList = false;
    },
    handleTabClick (index) {
      // this.$emit("tabName", index);
      // 修改地图放一起
      switch (index) {
        case 0:
          this.mapName = 'face';
          break;
        case 1:
          this.mapName = 'vehicle'
          break;
      }
    },
    // 轨迹信息
    handleSearchTrack (val, item, tabIndex, searchInfo) {
      this.typaTag = tabIndex;
      this.rightShowList = true;
      this.trackSearch = true;
      this.searchData = {
        dateType: searchInfo.dateType,
        endDate: searchInfo.endDate,
        startDate: searchInfo.startDate
      }
      this.resetPoint();
      this.$nextTick(() => {
        this.$refs.mapTrack.init(val, this.searchData);
        this.$refs.rightBox.init(val, this.searchData);
      })
    },
    handleTravelCancel () {
      this.travelPage = false;
    },
    // 地图绘制路线数据
    handleMapline (item) {
      this.resetPoint()
      // 将所有类型数据整合
      let list = [];
      for (let key in item) {
        if (item[key]) {
          item[key].map(item => {
            item.headType = key == 'carTrajectory' ? 'vehicle' : 'face';
          })
          list = [...list, ...item[key]]
        } else {
          item[key] = []
        }
      };
      this.setPeerList(list);
      // this.$emit('trackline', item);
      // 修改地图放一起
      this.trackPointList = item;

    },
    // 清除点位
    resetPoint () {
      // this.$emit('resetMapTrack')
      // 修改地图放一起
      this.$refs.mapBase.resetMarkerTrack();
    },
  }
}
</script>

<style lang='less' scoped>
.trajectory-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  .echarts {
    height: 100%;
    width: 100%;
  }
}
// /deep/ .ivu-select-dropdown{
//     left: 0 !important;
// }
</style>
