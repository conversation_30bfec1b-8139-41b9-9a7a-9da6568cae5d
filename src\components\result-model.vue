<template>
  <div>
    <ui-modal ref="modal" title="不合格详情" :styles="styles" footer-hide>
      <div class="content" v-if="resDetail != '{}'">
        <!-- <div class="echarts-title" v-if="row.areaImage">
          <i></i>
          <div class="titles">位置图片信息</div>
        </div>
        <div class="img" v-if="row.areaImage">
          <ui-image :src="row.areaImage" v-if="row.areaImage" class="image" />
        </div>
        <div class="echarts-title" v-if="row.dateImage">
          <i></i>
          <div class="titles">时间图片信息</div>
        </div>
        <div class="img" v-if="row.dateImage">
          <ui-image :src="row.dateImage" class="image" v-if="row.dateImage" />
        </div>
        <div class="echarts-title" v-if="row.additionalImage">
          <i></i>
          <div class="titles" v-if="row.additionalImage">
            <span class="sp-title">附加图片信息</span>
          </div>
        </div>
        <div class="img" style="display: flex;" v-if="row.additionalImage">
          <ui-image :src="this.row.additionalImage" v-if="row.additionalImage" class="image" />
        </div> -->
        <!--        <div class="echarts-title">-->
        <!--          <div class="titles">截图信息</div>-->
        <!--        </div>-->
        <!--        <ul class="img-list">-->
        <!--          <div>-->
        <!--            <ui-image :src="resDetail.areaImage" class="image" />-->
        <!--            <span class="font-blue mb-md mt-xs">位置图片信息</span>-->
        <!--          </div>-->
        <!--          <div>-->
        <!--            <ui-image :src="resDetail.dateImage" class="image" />-->
        <!--            <span class="font-blue mb-md mt-xs">时间图片信息</span>-->
        <!--          </div>-->
        <!--          <div>-->
        <!--            <ui-image :src="resDetail.additionalImage" class="image" />-->
        <!--            <span class="font-blue mb-md mt-xs">附加图片信息</span>-->
        <!--          </div>-->
        <!--        </ul>-->
        <!--        <div class="echarts-title mt-lg mb-md" v-if="resDetail.screenShot">-->
        <!--          <div class="titles">大图信息</div>-->
        <!--        </div>-->
        <div class="img big" v-if="resDetail.screenShot">
          <ui-image :src="resDetail.screenShot" v-if="resDetail.screenShot" class="image" />
        </div>
      </div>
      <div class="content" v-else>
        <div class="no-data">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      styles: {
        width: '6rem',
      },
      // row: {},
      // obj: {},
      name: {},
      resDetail: {},
    };
  },
  methods: {
    showModal(data) {
      this.resDetail = data;
      this.name = JSON.parse(data.additionalImageText);
      this.$refs.modal.modalShow = true;
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  //height: 800px;
  overflow-y: auto;
}
.echarts-title {
  width: 100%;
  display: flex;
  height: 30px;
  line-height: 30px;
  i {
    width: 8px;
    height: 100%;
    background: #239df9;
    margin-right: 6px;
  }
  .titles {
    display: flex;
    width: 100%;
    padding-left: 5px;
    color: #fff;
    background-image: linear-gradient(to right, #0a4f8d, #09284d);
    .text {
      margin-left: 100px;
      width: 1000px;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.img-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 180px;
  margin-bottom: 20px;
  div {
    flex: 1;
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-right: 10px;
    height: 150px;
    width: 350px;
    &:nth-child(3) {
      margin-right: 0;
    }
    @{_deep}.ui-image {
      z-index: initial;
      .ui-image-div {
        .tileImage {
          height: 150px !important; /*no*/
          width: 350px !important; /*no*/
        }
      }
    }
  }
}

.img {
  // width: 600px;
  width: 8rem;
  height: 524px;
  @{_deep}.ui-image {
    z-index: initial;
    .ui-image-div {
      .tileImage {
        width: 8rem !important; /*no*/
        height: 100% !important; /*no*/
      }
    }
  }
}
.big {
  width: 100%;
}
</style>
