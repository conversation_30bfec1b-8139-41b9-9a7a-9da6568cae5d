<template>
  <ui-modal
    v-model="visible"
    :title="!!isBatchRecheck ? '批量复检' : '复检'"
    :styles="styles"
    @query="handleSubmit"
    @onCancel="handleCancel"
  >
    <Form ref="formData" :model="formData" :rules="ruleValidate" :label-width="200">
      <FormItem label="复检设备" v-if="isBatchRecheck">
        <RadioGroup v-model="formData.model">
          <Radio v-for="(modelItem, modelIndex) in getModelList" :key="modelIndex" :label="modelItem.dataKey">
            <span>{{ modelItem.dataValue }}</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="" class="custom-form-item" v-if="formData.model === 'CUSTOM'">
        <custom-select-device
          :table-columns="tableColumns"
          :form-item-data="formItemData"
          :region-code="paramsList[this.codeKey]"
          :params-list="paramsList"
          :module-data="moduleData"
          @getDeviceQueryForm="getDeviceQueryForm"
        >
        </custom-select-device>
      </FormItem>
      <FormItem
        v-show="formData.model === 'CUSTOM'"
        label=""
        prop="deviceIds"
        :class="formData.model === 'CUSTOM' ? '' : 'custom-form-item'"
        :rules="[
          {
            required: formData.model === 'CUSTOM',
            message: '请选择设备',
            trigger: 'change',
            type: 'array',
          },
        ]"
      >
      </FormItem>
      <FormItem label="复检计划" v-if="isBatchRecheck">
        <RadioGroup v-model="formData.plan">
          <Radio v-for="(planItem, planIndex) in planList" :key="planIndex" :label="planItem.dataKey">
            <span>{{ planItem.dataValue }}</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="isBatchRecheck && formData.plan === 2"
        label=""
        prop="scheduletime"
        :rules="[
          {
            required: formData.plan === 2,
            message: '请选择复检时间',
            trigger: 'change',
            type: 'date',
          },
        ]"
      >
        <DatePicker
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          v-model="formData.scheduletime"
          placeholder="请选择复检时间"
          class="width-lg"
          :options="startTimeOption"
          @on-change="handleChangeTime"
        ></DatePicker>
      </FormItem>
      <FormItem label="复检次数" prop="maxCount" v-if="isBatchRecheck && needRecheckNumIndex">
        <InputNumber v-model.number="formData.maxCount" class="mr-xs" :max="5" :min="1" :precision="0"></InputNumber>
        <span class="font-white">次</span>
      </FormItem>
      <slot :form-data="formData"></slot>
    </Form>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { mapActions } from 'vuex';

export default {
  name: 'recheck',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    moduleData: {
      type: Object,
      default: () => {},
    },
    isBatchRecheck: {
      type: Boolean,
      default: false,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formData: {},
    // 不同指标传入的不同配置
    specificConfig: {},
    // 是否使用路由中的参数
    isUseRouterParams: {
      type: Boolean,
      default: true,
    },
    //选择设备的搜索条件
    formItemData: {},
    //自定义校验
    ruleCustom: {},
  },
  data() {
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      visible: false,
      title: '复检',
      styles: {
        width: '4rem',
      },
      paramsList: {},
      codeKey: '',
      ruleValidate: {
        ...this.ruleCustom,
      },
      modelList: [
        { dataKey: 'ALL', dataValue: '全部已检测设备' },
        { dataKey: 'UNQUALIFIED', dataValue: '检测不合格设备' },
        { dataKey: 'CUSTOM', dataValue: '自定义' },
      ],
      focusModelList: [
        { dataKey: 'ALL', dataValue: '本次已检测轨迹' },
        { dataKey: 'UNQUALIFIED', dataValue: '检测本次不合格轨迹' },
      ],
      schedule: '',
      planList: [
        { dataKey: 1, dataValue: '立即复检  ' },
        { dataKey: 2, dataValue: '自定义时间' },
      ],
      parameters: {}, // 设备选择组件回显参数,
      deviceQueryForm: {},
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      ids: [],
      taskIndexId: '',
      startTimeOption: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      ruleValidateMaxCount: {
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
    };
  },
  computed: {
    getModelList() {
      let arr = this.modelList;
      // 轨迹图片可访问率
      if (this.moduleData.indexType === 'FOCUS_TRACK_URL_AVAILABLE') {
        arr = this.focusModelList;
      }
      return arr;
    },
    // 需要显示 【复检次数】的指标
    needRecheckNumIndex() {
      return [
        'VIDEO_HISTORY_COMPLETE_ACCURACY', // 历史录像完整率
        'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN', // 重点历史录像完整率
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY', // 字幕标注合规性与时钟准确性
        'VIDEO_OSD_CLOCK_ACCURACY', // 重点字幕标注合规性与时钟准确性
        'VIDEO_OSD_ACCURACY', // 重点字幕标注合规率
        'VIDEO_GENERAL_OSD_ACCURACY', // 普通字幕标注合规率
        'VIDEO_PLAYING_ACCURACY', // 重点实时视频可调阅率
        'VIDEO_GENERAL_PLAYING_ACCURACY', // 普通实时视频可调阅率
        'VIDEO_HISTORY_ACCURACY', // 重点历史视频可调阅率
        'VIDEO_GENERAL_HISTORY_ACCURACY', // 普通历史视频可调阅率
        'VIDEO_CLOCK_ACCURACY', // 重点时钟准确率
        'VIDEO_GENERAL_CLOCK_ACCURACY', // 普通时钟准确率
        'VIDEO_QUALITY_PASS_RATE', // 视频流质量合格率
        'VIDEO_QUALITY_PASS_RATE_RECHECK', // 视频流质量合格率（人工复核）
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN', // 重点指挥图像在线率
      ].includes(this.moduleData.indexType);
    },
  },
  created() {},
  methods: {
    ...mapActions({
      getAlgorithmList: 'algorithm/getAlgorithmList',
    }),
    async getConfig() {
      try {
        const {
          data: { data },
        } = await this.$http.get(governanceevaluation.getTaskIndexConfig + `/${this.taskIndexId}`);
        const { extensionData } = data;
        const { indexConfig } = JSON.parse(extensionData);
        // 复检计划没有值就默认为立即复检
        !indexConfig['plan'] && (indexConfig['plan'] = 1);
        Object.keys(indexConfig).forEach((key) => {
          if (this.formData.hasOwnProperty(key)) {
            this.formData[key] = indexConfig[key];
          }
        });
        this.$emit('handleGetConfig');
      } catch (e) {
        console.log(e);
      }
    },
    getDeviceQueryForm({ ids, totalCount, params, ...deviceQueryForm }) {
      this.deviceQueryForm = deviceQueryForm;
      this.ids = ids;
      if (deviceQueryForm.checkDeviceFlag === '2') {
        this.formData.deviceIds.push(this.ids);
      } else {
        this.$set(this.formData, 'deviceIds', ids);
      }
      this.$nextTick(() => {
        this.$refs.formData.validateField('deviceIds');
      });
    },
    handleChangeTime(date) {
      this.schedule = date;
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs['formData'].validate((valid) => valid);
        if (!valid) {
          this.$Message.error('请将信息填写完整！');
          return false;
        }
        const params = {
          batchId: this.paramsList.batchId,
          extensionData: {
            indexConfig: {
              regionCode: this.paramsList[this.codeKey],
              ...this.specificConfig,
            },
            reinspect: {
              reinspectCustomConfig: this.deviceQueryForm,
              model: this.isBatchRecheck ? this.formData.model || 'CUSTOM' : 'CUSTOM',
              plan: this.isBatchRecheck ? this.formData.plan || 1 : 1, //如果是单个复检那就立即执行
              type: !this.isBatchRecheck ? 'PROGRAM_SINGLE' : 'PROGRAM_BATCH', // MANUAL,PROGRAM_SINGLE,PROGRAM_BATCH
              ids: this.isBatchRecheck ? this.ids : [this.moduleData.deviceId],
              maxCount: this.isBatchRecheck ? this.formData.maxCount : null, // 批量复检 才显示 【复检次数】
            },
          },
        };
        if (this.isBatchRecheck) {
          params.extensionData.reinspect.plan = this.formData.plan;
          params.extensionData.reinspect.scheduleKey = 6;
          params.extensionData.reinspect.scheduleValue = this.schedule;
          params.extensionData.reinspect.ids = this.ids;
        }
        await this.$http.post(evaluationoverview.programRecheck, params);
        this.$Message.success('复检操作成功！');
        this.$emit('handleUpdate');
        this.handleCancel();
      } catch (e) {
        console.log(e);
      }
    },
    handleCancel() {
      this.schedule = '';
      this.visible = false;
      this.$emit('handleCancel');
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val && this.needRecheckNumIndex) {
        this.ruleValidate = { ...this.ruleValidate, ...this.ruleValidateMaxCount };
      } else {
        this.ruleValidate = { ...this.ruleValidate };
      }
      // if (!!val) {
      //   if(this.isUseRouterParams) this.paramsList = this.$route.query
      //   if (!this.paramsList || !this.paramsList.batchId) return false
      //   this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode'
      // }
    },
    visible(val) {
      this.$emit('input', val);
    },
    moduleData: {
      async handler(val) {
        if (this.isUseRouterParams) {
          this.paramsList = this.$route.query;
        } else {
          this.paramsList = {
            indexId: val.indexId,
            batchId: val.batchId,
            statisticType: val.displayType,
            indexType: val.indexType,
            regionCode: val.regionCode,
            orgCode: val.orgCode,
          };
        }
        this.taskIndexId = val.taskIndexId;
        if (!this.paramsList || !this.paramsList.batchId) return false;
        this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
        if (val !== '') {
          await this.getAlgorithmList();
          await this.getConfig();
        }
      },
      deep: true,
    },
  },
  components: {
    CustomSelectDevice: require('../custom-select-device/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.online {
  margin-left: 25px;
}

.check-inline {
  display: inline-block !important;
}
.custom-form-item {
  margin-bottom: 0 !important;
}
@{_deep} .ivu-form-item {
  .ivu-form-item-content {
    .ivu-checkbox-group {
      display: flex;
      flex-direction: column;

      .ivu-checkbox-wrapper {
        width: fit-content;
        display: flex;
        align-items: center;

        > span {
          margin-right: 10px;
        }
      }
    }

    .ivu-radio-group {
      .ivu-radio-group-item {
        margin-right: 30px;

        > span {
          margin-right: 10px;
        }
      }
    }
  }

  .camera {
    margin: 0 !important;
  }
}
</style>
