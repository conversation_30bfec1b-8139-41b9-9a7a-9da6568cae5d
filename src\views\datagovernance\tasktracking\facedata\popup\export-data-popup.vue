<template>
  <ui-modal title="数据输出" v-model="visible" :styles="styles" :footer-hide="true">
    <div class="top-wrapper">
      <span>最新更新时间：{{ modifyTime }}</span>
      <p class="search-box">
        <slot name="searchSlot"></slot>
      </p>
    </div>
    <line-title title-name="检测结果统计"></line-title>
    <div class="echarts-wrapper">
      <div class="ring-box">
        <draw-echarts
          :echart-option="ringEchartsOption"
          :echart-style="ringStyle"
          ref="zdryChart1"
          class=""
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
      <div class="column-box">
        <slot name="columnTabSlot"></slot>
        <draw-echarts
          :echart-option="columnEchartsOption"
          :echart-style="columnStyle"
          ref="zdryChart2"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
    </div>
    <!-- <line-title title-name="异常人脸图像" class="mb-sm"></line-title> -->
    <tagView class="mt10" ref="tagView" :list="['图像模式', '设备模式']" @tagChange="tagChange1" />
    <div v-show="modelTag === 0">
      <face-device-search class="base-search" @startSearch="startSearch">
        <p class="search-box fr">
          <Button type="primary" class="ml-lg button-blue" @click="exportExcel">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </p>
      </face-device-search>
      <ui-select-tabs class="tabs-ui" v-if="tabsList.length" @selectInfo="selectInfo" :list="tabsList"></ui-select-tabs>
      <div class="table-wrapper">
        <face-list :faceLoading="faceLoading" :face-list="faceList" height="1.6rem" @uploadTips="uploadTips">
        </face-list>
        <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      </div>
      <export-disqualify v-model="disqualifyShow" :disqualify-item="disqualifyItem"></export-disqualify>
    </div>
    <div v-if="visible">
      <faceShebeiList
        ref="faceShebeiList"
        :minusHeight="646"
        v-show="modelTag == 1"
        v-model="modelTag"
        :listObj="listObj"
        @unqualified="unqualified"
        :import-api="importApi"
      />
    </div>
  </ui-modal>
</template>
<script>
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    value: {},
    echartsLoading: {
      type: Boolean,
    },
    totalCount: {
      default: 0,
    },
    modifyTime: {
      default: '',
    },
    importTotalCount: {},
  },
  data() {
    return {
      importApi: { api: tasktracking.downloadFaceLibAbnormalDev },
      visible: false,
      styles: {
        //top: "0.2rem",
        width: '95%',
      },
      modelTag: 0,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      ringStyle: {
        height: '180px',
        width: '400px',
      },
      columnStyle: {
        height: '180px',
        width: '1100px',
      },
      tabsList: [],
      listObj: {
        columns: [
          { type: 'index', width: 70, title: '序号', fixed: 'left' },
          {
            title: `${this.global.filedEnum.deviceId}`,
            key: 'deviceId',
            width: 200,
            fixed: 'left',
          },
          {
            title: `${this.global.filedEnum.deviceName}`,
            key: 'deviceName',
            width: 200,
          },
          { title: '组织机构', key: 'orgCode', width: 200 },
          {
            title: `${this.global.filedEnum.longitude}`,
            key: 'longitude',
            width: 200,
          },
          {
            title: `${this.global.filedEnum.latitude}`,
            key: 'latitude',
            width: 200,
          },
          { title: 'MAC地址', slot: 'macAddr', width: 200 },
          { title: this.global.filedEnum.ipAddr, slot: 'ipAddr', width: 200 },
          {
            title: this.global.filedEnum.sbgnlx,
            slot: 'sbgnlxText',
            width: 200,
          },
          {
            title: this.global.filedEnum.sbdwlx,
            slot: 'sbdwlxText',
            width: 200,
          },
          { title: '位置类型', slot: 'positionTypeText', width: 200 },
          { title: '安装地址', key: 'address', width: 200 },
          { title: '异常类型数量', key: 'typeCount', width: 200 },
          { title: '异常图像数量', key: 'count', width: 200 },
          { title: '操作', slot: 'action', width: 140, fixed: 'right' },
        ],
        key: 'entities',
        loadData: (parameter) => {
          return this.$http
            .post(tasktracking.queryFaceLibAbnormalTaskPageDev, Object.assign(parameter, {}))
            .then((res) => {
              // 把后台数组转为list返回
              return res.data;
            });
        },
      },
      ringEchartsOption: {},
      columnEchartsOption: {},
      searchData: {
        startTime: '',
        endTime: '',
        reasons: [],
        deviceIds: [],
        pageNumber: 1,
        pageSize: 20,
      },
      faceList: [],
      faceLoading: false,
      disqualifyShow: false,
      disqualifyItem: {},
      popUpOption: {},
    };
  },
  methods: {
    init(option) {
      this.visible = true;
      if (this.$refs.tagView) {
        this.$refs.tagView.curTag = 0;
      }
      this.popUpOption = option;
      this.modelTag = 0;
      this.getEchartsData();
      this.geExceptList();
      this.querySumStatistics();
      this.$nextTick(() => {
        this.$refs.faceShebeiList.info(true);
        this.$emit('closeOpenModal');
      });
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.geExceptList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.geExceptList();
    },
    selectInfo(val) {
      this.searchData.reasons = val.map((item) => item.name);
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.geExceptList();
    },
    uploadTips(item) {
      this.disqualifyShow = true;
      this.disqualifyItem = item;
    },
    startSearch(val) {
      Object.assign(this.searchData, val);
      this.geExceptList();
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.modelTag = val;
      if (this.modelTag == 1) {
        //   this.$nextTick(() => {
        this.searchData.deviceIds = [];
        //     this.$refs.faceShebeiList.info(true)
        //   })
      } else {
        //   this.$nextTick(() => {
        //     this.geExceptList()
        //   })
      }
    },
    // 不合格图片改变图像模式参数
    async unqualified(val) {
      this.modelTag = 0;
      this.$refs.tagView.curTag = 0;
      this.$nextTick(() => {
        this.searchData.deviceIds = [val.deviceId];
        this.geExceptList();
      });
    },
    async getEchartsData() {
      try {
        this.viewLoading = true;
        let { data } = await this.$http.get(tasktracking.queryFaceTaskStatisticsChartDetail);
        this.handleEchartsData(data.data);
      } catch (err) {
        console.log(err);
      }
    },
    async geExceptList() {
      try {
        this.faceLoading = true;
        let { data } = await this.$http.post(tasktracking.queryFaceLibAbnormalPageList, this.searchData);
        this.faceList = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.faceLoading = false;
      } catch (err) {
        this.faceLoading = false;
        console.log(err);
      }
    },
    async querySumStatistics() {
      try {
        let params = {
          topicComponentId: this.popUpOption.filedData.topicComponentId,
        };
        let { data } = await this.$http.get(tasktracking.querySumStatistics, {
          params: params,
        });
        this.handleRingEcharts(data.data);
      } catch (err) {
        console.log(err);
      }
    },
    handleRingEcharts(data) {
      let ringOpts = {
        text: '数据总量',
        subtext: `${data.accessDataCount}`,
        data: [
          {
            name: '合格数据',
            value: data.accessDataCount - data.existingExceptionCount,
            color: 'greenColor',
          },
          {
            name: '不合格数据',
            value: data.existingExceptionCount,
            color: 'redColor',
          },
        ],
        legendData: ['合格数据', '不合格数据'],
      };
      this.ringEchartsOption = this.$util.doEcharts.taskTrackingRing(ringOpts);
    },
    handleEchartsData(data) {
      this.modifyTime = data.lastCheckDate;
      this.tabsList = data.list.map((item) => {
        return {
          select: false,
          name: item.errorMessage,
        };
      });
      let columnOpts = {
        xAxisData: data.list.map((item) => item.errorMessage || '未知'),
        data: data.list.map((item) => item.count || 0),
      };
      this.columnEchartsOption = this.$util.doEcharts.taskTrackingColumn(columnOpts);
    },
    async exportExcel() {
      try {
        let params = {
          // topicComponentId: this.popUpOption.filedData.topicComponentId
        };
        let res = await this.$http.post(tasktracking.downloadFaceLibAbnormalDev, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        this.pageData = {
          pageNum: 1,
          pageSize: 20,
          totalCount: 0,
        };
      }
    },
    value(val) {
      this.visible = val;
    },
    totalCount(val) {
      this.pageData.totalCount = val;
    },
  },
  components: {
    FaceDeviceSearch: require('@/components/face-device-search.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    FaceList: require('@/components/face-list.vue').default,
    ExportDisqualify: require('./export-disqualify.vue').default,
    tagView: require('@/components/tag-view').default,
    faceShebeiList: require('../../components/face-shebei-list').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  margin: 10px 0 0;
  .ring-box {
    width: 570px;
    margin-right: 10px;
    .echarts {
      width: 540px !important;
    }
  }
  .column-box {
    flex: 1;
    position: relative;
  }
  .ring-box,
  .column-box {
    background: var(--bg-sub-content);
  }
  .charts {
    flex: 1;
    width: 300px;
  }
}
.table-wrapper {
  position: relative;
  .no-data {
    top: 60%;
  }
}
.top-wrapper {
  display: flex;
  justify-content: space-between;
  color: #fff;
  .search-box {
    display: flex;
    .input-width {
      width: 230px;
    }
  }
}
.tabs-ui {
  color: #fff;
  margin: 10px 0;
}
.base-search {
  margin-top: 15px;
  margin-bottom: 0px;
}
.mt10 {
  margin-top: 10px;
}
</style>
