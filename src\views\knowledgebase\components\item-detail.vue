<template>
  <div class="item-detail">
    <h5 class="t-center f-16 base-text-color mt-sm mb-sm">
      {{ itemData.catalogueName || '暂无' }}
      <Button type="text" class="fr" @click="$emit('onBack')" v-if="$route.name === 'knowledgesearch'"
        >&lt;&lt; 返回</Button
      >
    </h5>
    <section class="flex-column-center f-14">
      <p class="mb-sm user-message">
        <i class="iconfont icon-zhishijiansuo f-16"></i>
        <span>{{ itemData.createTime }} </span>
        <i class="iconfont icon-zhishijiansuo f-16"></i>
        <span>{{ itemData.creator }} {{ itemData.orgCode }}</span>
      </p>
    </section>
    <section class="content-wrapper base-text-color f-14" v-html="itemData.description"></section>
    <div class="question-list font-active-color">
      <span v-for="(item, index) in itemData.questionList" :key="index" class="mr-sm">
        <Tooltip transfer>
          #{{ item.errorMessage }}#
          <div slot="content">
            <template v-if="item.governmentNameList">
              <p v-for="(value, index) in item.governmentNameList || []" :key="index + '-' + value">
                <span class="ivu-btn ivu-btn-text" @click="goToolpage(item.governmentList[index], value)">{{
                  value
                }}</span>
              </p>
            </template>
            <span v-else>暂无数据</span>
          </div>
        </Tooltip>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    itemData: {
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    goToolpage(key) {
      if (!key) return;
      let name = '';
      switch (key) {
        case '10':
          name = 'governanceorder';
          break;
        case '11':
          name = 'governancetoolset';
          break;
        default:
          name = 'governanceautomatic';
      }
      this.$router.push({
        name: name,
        query: name === 'governanceautomatic' ? { tab: key } : {},
      });
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.item-detail {
  .user-message {
    color: #838d9c;
  }
}
.content-wrapper {
  @{_deep} img {
    //max-width: 500px;
  }
}
.question-list {
  margin: 50px 0;
}
</style>
