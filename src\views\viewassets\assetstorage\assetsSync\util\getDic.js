/**
 * 本文件作用
 * 1.给字典的值添加颜色
 * 2.并可以根据datakey去获取相应的颜色
 *
 */
import store from '@/store';
import { allThemeList } from '@/style/theme';
const themeType = store.state.common.themeType;
const theme = allThemeList.find((row) => row.theme === themeType);
// 检测状态：0 未检测  ,1 合格，2 不合格，3 检测中
export const handleCheckStatusColor = (checkStatusList) => {
  return baseFunc(checkStatusList, {
    '0': theme.variables['--color-offline'],
    '1': theme.variables['--color-success'],
    '2': theme.variables['--color-failed'],
    '3': theme.variables['--color-warning'],
  });
};
// 入库状态 （0 未入库 ，1 已入库）
export const handleStorageStatusColor = (storageStatusList) => {
  return baseFunc(storageStatusList, {
    '0': theme.variables['--color-failed'],
    '1': theme.variables['--color-success'],
  });
};
// 比对状态：0 未比对  ,1 已比对，2 比对中
export const handleCompareStatusColor = (compareStatusList) => {
  return baseFunc(compareStatusList, {
    '0': theme.variables['--color-failed'],
    '1': theme.variables['--color-success'],
    '2': theme.variables['--color-warning'],
  });
};

function baseFunc(list, colorStragety) {
  const listObject = {};
  list.forEach((item) => {
    item.color = colorStragety[item.dataKey];
    listObject[item.dataKey] = item;
  });
  return listObject;
}
// function getAlldicData() {
//   // 同步检测状态        aysc_check_status: checkStatusList
//   // 同步入库状态        aysc_storage_status: storageStatusList
//   // 同步比对状态        aysc_compare_status: compareStatusList
//   // 同步入库策略        aysc_storage_type: addTypeEnum
//   return store.dispatch('assets/getAlldicData').then(() => {
//     console.log(store.state.assets, 'ksajdskajd')
//     let addTypeEnum = [...store.state.assets.aysc_storage_type]
//     let checkStatusList = [...store.state.assets.aysc_check_status]
//     let storageStatusList = [...store.state.assets.aysc_storage_status]
//     let compareStatusList = [...store.state.assets.aysc_compare_status]

//     const checkStatusListObject = handleCheckStatusColor(checkStatusList)
//     const storageStatusListObject = handleStorageStatusColor(storageStatusList)
//     const compareStatusListObject = handleCompareStatusColor(compareStatusList)
//     return {
//       // 入库策略
//       addTypeEnum,
//       // 检测状态：0 未检测  ,1 合格，2 不合格，3 检测中
//       checkStatusList,
//       // 入库状态 （0 未入库 ，1 已入库）
//       storageStatusList,
//       // 比对状态：0 未比对  ,1 已比对，2 比对中
//       compareStatusList,

//       checkStatusListObject,
//       storageStatusListObject,
//       compareStatusListObject,
//     }
//   })
// }
