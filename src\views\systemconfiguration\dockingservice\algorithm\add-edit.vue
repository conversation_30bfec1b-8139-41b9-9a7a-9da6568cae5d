<template>
  <ui-modal ref="modal" v-model="visible" :title="action.title" @query="add">
    <Form ref="form" :model="formData" :rules="rules" :label-width="80">
      <FormItem label="算法厂商" prop="algorithmVendorType">
        <Select v-model="formData.algorithmVendorType" placeholder="请选择算法厂商">
          <Option v-for="(item, index) in algorithmList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </FormItem>
      <FormItem label="算法类型" prop="algorithmType">
        <Select v-model="formData.algorithmType" placeholder="请选择算法类型">
          <Option v-for="(item, index) in algorithmTypeList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </FormItem>
      <FormItem label="IP" prop="ip">
        <Input v-model="formData.ip" placeholder="请输入IP"></Input>
      </FormItem>
      <FormItem label="端口" prop="port">
        <Input v-model="formData.port" placeholder="请输入port"></Input>
      </FormItem>
      <div v-if="formData.algorithmVendorType == 1">
        <FormItem label="key" prop="key">
          <Input v-model="formData.key" placeholder="key"></Input>
        </FormItem>
        <FormItem label="secret" prop="secret">
          <Input v-model="formData.secret" placeholder="secret"></Input>
        </FormItem>
      </div>
      <div v-if="formData.algorithmVendorType == 2">
        <FormItem label="key1" prop="key">
          <Input v-model="formData.key" placeholder="key"></Input>
        </FormItem>
        <FormItem label="secret1" prop="secret">
          <Input v-model="formData.secret" placeholder="secret"></Input>
        </FormItem>
      </div>
    </Form>
  </ui-modal>
</template>
<script>
import algorithm from '@/config/api/algorithm';
export default {
  name: 'add',
  props: ['algorithmList', 'algorithmTypeList', 'editData', 'action', 'value'],
  data() {
    return {
      visible: false,
      formData: {
        algorithmVendorType: '',
        algorithmType: '',
        ip: '',
        port: '',
        key: '',
        secret: '',
        config: '',
      },
      rules: {
        algorithmVendorType: [{ required: true, message: '请选择算法厂商', trigger: 'change' }],
        algorithmType: [{ required: true, message: '请选择算法类型', trigger: 'change' }],
        ip: [{ required: true, message: '请输入IP', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
      },
    };
  },
  created() {},
  methods: {
    add() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.isAdd) {
            this.$http.post(algorithm.addAlgorithm, this.formData).then(() => {
              this.visible = false;
              this.$emit('addSuccess');
            });
          } else {
            this.$http.put(algorithm.updateAlgorithm, this.formData).then(() => {
              this.visible = false;
              this.$emit('addSuccess');
            });
          }
        } else {
          // this.$Message.error('Fail!')
        }
      });
    },
  },
  watch: {
    value(val) {
      if (val) {
        Object.assign(this.formData, this.editData);
      } else {
        this.$refs.form.resetFields();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    isAdd() {
      return this.action.value === 'add';
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20px 50px;
}
</style>
