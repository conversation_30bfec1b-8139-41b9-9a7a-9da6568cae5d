<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />
    <!-- 机构数 ---------------------------------------------------------------------------------------------------- -->
    <!-- <SlideUnitTree
      :treeData="taskObj.treeData"
      @selectOrgCode="selectOrgCode"
      :current-node-key="getDefaultSelectedOrg.orgCode"
      :select-key="selectKey"
    >
    </SlideUnitTree> -->
    <!-- 模式切换 ---------------------------------------------------------------------------------------------------- -->
    <tagView class="tagView" ref="tagView" :list="['图像模式', '聚档模式']" @tagChange="tagChange1" />

    <!-- 图像模式 ---------------------------------------------------------------------------------------------------- -->
    <TableList
      v-if="modelTag == 0 && columns.length > 0"
      ref="infoList"
      :columns="columns"
      :minusHeight="minusHeight"
      :loadData="loadDataList"
    >
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <SearchList ref="searchList" @startSearch="startSearch" :taskObj="taskObj" :treeData="treeData" />
      </div>
      <!-- 表格操作 -->
      <template #trackImage="{ row }">
        <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
          <ui-image :src="row.trackImage" />
        </div>
      </template>
      <template #identityPhoto="{ row }">
        <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
          <ui-image :src="row.identityPhoto" alt="" />
        </div>
      </template>
      <template #synthesisResult="{ row }">
        <span style="color: #19c176" v-if="row.synthesisResult == 1">准确性合格</span>
        <span style="color: #c43d2c" v-else-if="row.synthesisResult == 0">准确性存疑</span>
        <span style="color: #c43d2c" v-else>无法判定</span>
      </template>
      <template #similarity="{ row }">
        <span>{{ (row.similarity * 100).toFixed(2) }}<span v-if="row.similarity">%</span></span>
      </template>
      <template #algResult="{ row }">
        <span v-if="row.algResult">
          <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
            <span>{{ item.algorithmType }}:</span>
            <span>{{ item.score }}%</span>
          </div>
        </span>
        <span v-else>--</span>
      </template>
    </TableList>
    <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
    <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo" v-if="modelTag == 1">
      <div slot="search" class="hearder-title">
        <SearchCard ref="searchCard" @startSearch="startSearch" :taskObj="taskObj" :treeData="treeData" />
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <UiGatherCard
          class="card"
          :list="row"
          :personTypeList="personTypeList"
          :cardInfo="cardInfo"
          @detail="detailInfo(row)"
        ></UiGatherCard>
      </template>
    </TableCard>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <captureDetail ref="captureDetail" :taskObj="taskObj" :orgCode="selectKey" />
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    UiGatherCard: require('../components/ui-gather-card.vue').default,
    SearchCard: require('./component/searchCard').default,
    SearchList: require('./component/searchList').default,
    captureDetail: require('./component/capture-detail.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    tagView: require('../components/tags').default,
    LookScene: require('@/components/look-scene').default,
    TableList: require('../components/tableList.vue').default,
    TableCard: require('../components/tableCard.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      modelTag: 0, // 聚档模式,图像模式
      bigPictureShow: false,
      treeData: [],
      imgList: [],
      selectKey: '', // 机构树
      abnormalCount: [
        { title: '重点人员数量', icon: 'icon-a-guijizhunqueshuaijiance2' },
        { title: 'ZDR人像轨迹总量', icon: 'icon-a-guijizhunqueshuaijiance2' },
        { title: '准确性存疑人像轨迹数量', icon: 'icon-a-guijizhunqueshuaijiance2' },
      ],
      columns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '人脸抓拍', key: 'trackImage', slot: 'trackImage' },
        { title: '抓拍时间', key: 'shotTime', width: 170 },
        { title: '抓拍点位', key: 'catchPlace' },
        { title: '证件照', key: 'identityPhoto', slot: 'identityPhoto' },
        { title: '姓名', key: 'name' },
        { title: '证件号', key: 'idCard' },
        { title: '原始算法', key: 'similarity', slot: 'similarity' },
        { title: '算法识别结果', key: 'algResult', slot: 'algResult' },
        { title: '综合判定', key: 'synthesisResult', slot: 'synthesisResult' },
      ],
      loadDataList: (parameter) => {
        return this.$http
          .post(
            inspectionrecord.pageListImage,
            Object.assign(
              parameter,
              {
                batchId: this.indexResult().batchId,
                taskIndexId: this.indexResult().taskIndexId,
                orgCode: this.selectKey,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      minusHeight: 440,
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'total' },
        { name: '异常轨迹：', value: 'abnormal', color: '#BC3C19' },
      ],
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            inspectionrecord.pageListGroup,
            Object.assign(
              parameter,
              {
                batchId: this.indexResult().batchId,
                taskIndexId: this.indexResult().taskIndexId,
                orgCode: this.selectKey,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
      // getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      // orgTreeData: 'common/getOrganizationList',
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
      },
    },
  },
  filter: {},
  created() {},
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
    this.info();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 详情
    detailInfo(info) {
      this.$refs.captureDetail.show(1, info);
    },
    async info() {
      await this.static();
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    indexResult() {
      if (this.taskObj.indexResults && this.taskObj.indexResults.length) {
        return this.taskObj.indexResults[0];
      }
      return {};
    },
    async static() {
      this.abnormalCount = [
        { title: '重点人员数量', count: 0, icon: 'icon-a-guijizhunqueshuaijiance2' },
        { title: 'ZDR人像轨迹总量', count: 0, icon: 'icon-a-guijizhunqueshuaijiance2' },
        { title: '准确性存疑人像轨迹数量', count: 0, icon: 'icon-a-guijizhunqueshuaijiance2' },
      ];
      let params = {
        batchId: this.indexResult().batchId,
        taskIndexId: this.indexResult().taskIndexId,
        orgCode: this.selectKey,
      };
      let {
        data: { data },
      } = await this.$http.post(inspectionrecord.resultId, params);
      this.abnormalCount = [
        {
          title: '重点人员数量',
          count: data.personAmount || 0,
          icon: 'icon-a-guijizhunqueshuaijiance2',
        },
        {
          title: 'ZDR人像轨迹总量',
          count: data.detectionAmount || 0,
          icon: 'icon-a-guijizhunqueshuaijiance2',
        },
        {
          title: '准确性存疑人像轨迹数量',
          count: data.impeachAmount || 0,
          icon: 'icon-a-guijizhunqueshuaijiance2',
        },
      ];
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.selectKey = searchData.orgCodeList;
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.ui-images {
  width: 56px;
  height: 56px;
  margin: 5px 0;
  .ui-image {
    min-height: 56px !important;
    /deep/ .ivu-spin-text {
      img {
        width: 56px;
        height: 56px;
        margin-top: 5px;
      }
    }
  }
}
</style>
