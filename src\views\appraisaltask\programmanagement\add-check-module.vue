<template>
  <div>
    <ui-modal v-model="visible" :title="modalAction.title" :styles="styles" @query="query">
      <Form ref="formData" :model="formData" :rules="ruleCustom" @submit.native.prevent>
        <FormItem label="方案名称" class="mb-0" prop="name" :label-width="80">
          <Input type="text" v-model="formData.name" :maxlength="20" placeholder="请填写方案名称"></Input>
        </FormItem>
      </Form>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.mb-0 {
  margin-bottom: 0;
}
@{_deep} .ivu-modal-body {
  padding: 20px 50px 50px;
}
.remarks {
  width: 565px;
}
.action {
  cursor: pointer;
  font-size: 24px;
  &:hover {
    background: #ccc;
  }
}
.facePath {
  width: 110px;
  height: 110px;
}
.left-item {
  @{_deep} .ivu-form-item-label {
    width: 150px;
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    modalAction: {
      required: true,
      type: Object,
    },
    modalId: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      formData: {},
      ruleCustom: {
        name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    async query() {
      try {
        let validate = await this.$refs.formData.validate();
        if (!validate) return;
        let program = {
          name: this.formData.name,
          id: this.formData.id,
        };
        let res = await this.$http.post(governanceevaluation.tEvaluatingProgram, program);
        this.$Message.success(res.data.msg);
        this.visible = false;
        this.$emit('update', !this.formData.id ? res.data : null);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      if (val && !this.isAdd) {
        const data = JSON.parse(JSON.stringify(this.$parent.edit_item));
        this.formData = data;
      } else {
        this.formData = {};
      }
      this.$refs.formData.resetFields();
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {
    isAdd() {
      return this.modalAction.action === 'add';
    },
  },
  components: {},
};
</script>
