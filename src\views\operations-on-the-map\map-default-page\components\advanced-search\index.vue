<template>
  <div
    :class="['advanced-search-result', { 'expand-menu': !expand }]"
    ref="advanced"
  >
    <div class="search-result-inner">
      <!-- 50 / 192 + 'rem' -->
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
      >
        <MenuItem
          v-for="(item, index) in advancedMenuList"
          :key="index"
          :name="item.name"
          v-permission="item.permission"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
          <div
            class="search-active"
            v-if="sectionName === item.name && !expand"
          ></div>
        </MenuItem>
      </Menu>
      <transition :name="!expand ? 'draw' : ''">
        <keep-alive>
          <component
            @dataAboveMapHandler="dataAboveMapHandler"
            :mapList="mapList"
            :mapOnData="true"
            :is="sectionName"
            class="search-result-content"
            v-show="expand"
          ></component>
        </keep-alive>
      </transition>
      <div class="switch" @click="switchHandle">
        <img v-if="!expand" src="@/assets/img/expand.png" alt="" />
        <img v-else src="@/assets/img/stow.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
// import policeServiceContent from '@/views/wisdom-cloud-search/cloud-default-page/advanced-search/pages/police-service-content'

import face from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/face-content";
import faces from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/face-contents";
import vehicle from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/vehicle-content";
import humanBodyContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/human-body-content.vue";
import nonmotorVehicleContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/nonmotor-vehicle-content.vue";
import etc from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/etc-content";
import wifi from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/wifi-content";
import rfid from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/RFID-content";
import electric from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/electric-content";
import gps from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/gps-content";

/* import wifi from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/pages/wifi-content";
import rfid from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/pages/RFID-content";
import electric from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/pages/electric-content";
import gps from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/pages/gps-content"; */
// import device from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/pages/device-content";
export default {
  name: "OperationOnMapSearch",
  components: {
    wifi,
    face: developmentEnvironment == "binhai" ? face : faces, //人脸模块
    // policeServiceContent, //警务模块,
    humanBodyContent,
    nonmotorVehicleContent,
    vehicle,
    etc,
    rfid,
    electric,
    gps,
    // device,
  },
  props: {
    layerManageMap: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      advancedMenuList: [
        // { label: '警务', value: 1, iconName: 'icon-anjian', name: 'policeServiceContent' },
        {
          label: "人像",
          value: 2,
          iconName: "icon-renlian",
          name: "face",
          permission: ["track-face"],
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicle",
          permission: ["track-vehicle"],
        },
        {
          label: "人体",
          value: 8,
          iconName: "icon-renti",
          name: "humanBodyContent",
          permission: ["track-humanBody"],
        },
        {
          label: "非机动车",
          value: 9,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicleContent",
          permission: ["track-nonmotorVehicle"],
        },
        {
          label: "Wi-Fi",
          value: 4,
          iconName: "icon-wifi",
          name: "wifi",
          permission: ["track-wifi"],
        },
        {
          label: "RFID",
          value: 5,
          iconName: "icon-RFID",
          name: "rfid",
          permission: ["track-rfid"],
        },
        {
          label: "电围",
          value: 6,
          iconName: "icon-ZM-dianwei",
          name: "electric",
          permission: ["track-electric"],
        },
        {
          label: "GPS",
          value: 7,
          iconName: "icon-gps",
          name: "gps",
          permission: ["track-gps"],
        },
        {
          label: "ETC",
          value: 8,
          iconName: "icon-a-ETC1x",
          name: "etc",
          permission: ["track-etc"],
        },
        // { label: '设备', value: 7, iconName: 'icon-shebeizichan', name: 'device' }
      ],
      sectionName: "",
      expand: true,
      mapList: [], // 已上图数据
    };
  },
  mounted() {
    let permissionList = this.advancedMenuList.filter((d) =>
      this.$_has(d.permission)
    );
    this.sectionName = permissionList.length > 0 ? permissionList[0].name : "";
  },
  methods: {
    // 点击-切换
    selectItemHandle(sectionName) {
      this.expand = true;
      this.sectionName = sectionName;
      this.currentClickIndex = -1;
      this.mapList = this.layerManageMap[sectionName] || [];
    },
    // 收起-展开
    switchHandle() {
      this.expand = !this.expand;
    },
    // 数据上图
    dataAboveMapHandler(mapData) {
      this.switchHandle();
      this.$emit("updateLayerManager", mapData);
    },
  },
};
</script>

<style lang="less" scoped>
.advanced-search-result {
  position: absolute;
  left: 20px;
  top: 66px;
  height: calc(~"100% - 88px");
  width: calc(~"100% - 90px");
  .draw-enter-active,
  .draw-leave-active {
    transition: transform 0.8s;
  }
  .draw-enter,
  .draw-leave-to {
    transform: translateX(-100%);
  }
  .search-result-inner {
    height: 100%;
    width: 100%;
    position: relative;
    .search-active {
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background: #f8775c;
    }
  }
  .switch {
    width: 18px;
    height: 90px;
    position: absolute;
    bottom: 10px;
    top: 50%;
    right: -68px;
    transform: translateY(-50%);
    cursor: pointer;
    > img {
      width: 100%;
    }
  }
  .search-result-content {
    position: absolute;
    left: 50px;
    top: 0px;
    height: 100%;
    width: 100%;
    background: rgb(255, 255, 255);
    box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
    border-radius: 0px 0px 4px 4px;
    display: flex;
    // z-index: 999;
    // overflow: hidden;
  }
  /deep/.ivu-menu {
    z-index: 11 !important;
    width: 100%;
    position: relative;
    .ivu-menu-item-active {
      position: relative;
      .search-active {
        position: absolute;
        top: 3px;
        right: 3px;
      }
    }
  }
}
.expand-menu {
  width: 0px;
}
</style>
