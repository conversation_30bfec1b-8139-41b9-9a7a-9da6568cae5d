<template>
  <ui-modal v-model="visible" :title="type === 'add' ? '新增资源' : '编辑资源'" :r-width="450" @onOk="comfirmHandle" class="add-resource">
    <Form ref="form" :model="form" :rules="ruleForm">
      <FormItem label="所属目录" :label-width="110" prop="catalogId" v-if="visible">
        <ui-treeSelect class="width-md select-node-tree filter-tree" multiple v-model="form.catalogId" filterable check-strictly placeholder="请选择" default-expand-all show-checkbox :expand-on-click-node="false" check-on-click-node node-key="id" :treeData="list"> </ui-treeSelect>
      </FormItem>
      <FormItem label="资源数据" :label-width="110" prop="resourceName">
        <Select v-model="form.resourceName" transfer filterable class="width-md">
          <Option v-for="item in resourceAll" :value="item" :key="item" placeholder="请选择">{{ item }}</Option>
        </Select>
      </FormItem>
      <FormItem label="中文名称" :label-width="110" prop="resourceNameCn">
        <Input v-model="form.resourceNameCn" :maxlength="50" placeholder="请输入" class="width-md" />
      </FormItem>
      <FormItem label="描述" :label-width="110" prop="describeCn">
        <Input v-model="form.describeCn" :maxlength="500" :rows="4" type="textarea" class="width-md" placeholder="请输入" />
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import { updateResource, getResourceAll, addResource } from '@/api/dataGovernance'
import UiTreeSelect from '@/components/ui-tree-select'
export default {
  components: {
    UiTreeSelect
  },
  props: {
    // 所有目录
    list: {
      type: Array,
      default: () => []
    },
    // 所有资源
    // resourceAll: {
    //   type: Array,
    //   default: () => []
    // }
  },
  data() {
    return {
      type: 'add', //新增/编辑
      visible: false,
      form: {
        catalogId: [],
        resourceName: '',
        resourceNameCn: '',
        describeCn: ''
      },
      ruleForm: {
        catalogId: [{ required: true, type: 'array', message: '请选择', trigger: 'change' }],
        resourceName: [{ required: true, message: '请选择', trigger: 'change' }],
        resourceNameCn: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      resourceAll: []
    }
  },
  methods: {
    // 初始化
    async show(item) {
      await this.getResourceAll()
      // var index = this.resourceAll.findIndex(ite => {return ite == item.resourceName})
      // if (index == -1)  this.resourceAll.push(item.resourceName)
      if (item) {
        this.resourceAll.push(item.resourceName)
      }
      this.type = item ? 'edit' : 'add'
      this.visible = true
      this.form.catalogId = []
      this.$nextTick(() => {
        this.$refs.form.resetFields()
        if (this.type === 'edit') {
          // 编辑字段回显
          const { catalogId, resourceName, resourceNameCn, describeCn, id } = item
          this.form = {
            id: id,
            catalogId: catalogId.split(','),
            resourceName: resourceName,
            resourceNameCn: resourceNameCn,
            describeCn: describeCn
          }
        }
      })
    },
    // 获取所有资源
    async getResourceAll() {
      this.resourceAll = []
      await getResourceAll().then(res => {
        this.resourceAll = res.data
      })
    },
    // 新增/编辑
    comfirmHandle() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 新增
          if (this.type === 'add') {
            addResource(this.form).then(res => {
              this.visible = false
              this.$Message.success('新增成功')
              this.$emit('refreshDataList')
            })
          } else {
            //编辑
            updateResource(this.form).then(res => {
              this.visible = false
              this.$Message.success('修改成功')
              this.$emit('refreshDataList')
            })
          }
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.add-resource {
  .width-md {
    width: 250px;
  }
  /deep/ .ivu-tag .ivu-icon {
    top: 4px !important;
    font-size: 16px !important;
  }
  /deep/.ivu-select-dropdown .ivu-select-item:hover,
  .ivu-select-dropdown .ivu-select-item-selected:hover {
    background-color: #f3f3f3;
    color: #000;
  }
  /deep/.ivu-select-dropdown .ivu-select-item-selected {
    background-color: #fff;
    color: #2c86f8;
  }
  /deep/ .filter-tree .el-tree-node__content {
    margin-top: 0px;
  }
  /deep/ .ivu-btn {
    padding: 7px 21px !important;
  }
}
</style>
