<template>
  <div class="statistical-results auto-fill">
    <statistical-List
      ref="StatisticalList"
      :formItemData="formItemData"
      :formData="formData"
      :tableColumns="tableColumns"
      :result-data="resultData"
      :row-class-name="rowClassName"
      @startSearch="startSearch"
      @onSortChange="onSortChange"
    >
      <template #export>
        <Checkbox
          v-if="isShowHideCheckbox"
          v-model="otherSearchData.isHide"
          class="mr-md"
          :true-value="1"
          :false-value="0"
          @on-change="getStatisticalList(true)"
        >
          {{ getCheckLabel }}
        </Checkbox>
        <Button type="primary" class="button-export mb-sm" @click="getExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white mr-xs"></i>
          <span class="ml-xs">导出</span>
        </Button>
      </template>

      <template #monitorUrls="{ row }">
        <div class="monitor-img">
          <ui-image :src="row.detail.monitorUrls[0]" @click.native="$emit('viewDetail', row)" />
          <div class="percentage ellipsis">
            {{ row.detail.monitorUrls.length }}
          </div>
        </div>
      </template>
      <template #qualified="{ row }">
        <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
          {{ row.qualifiedDesc }}
        </span>
      </template>
      <!-- 建档匹配率 -->
      <template #accuracyRate="{ row }">
        <span :class="{ 'span-link': getAccuracyRate(row) }" @click="clickJump(row)">
          {{ getAccuracyRate(row) ? `${row?.detail?.onlineRateVO?.accuracyRate}%` : '--' }}
        </span>
      </template>
      <template #customcol="{ row }">
        <slot name="customcontent" :row="row"> </slot>
      </template>
      <template #action="{ row, item }">
        <div class="action">
          <slot name="customBtn" :row="row" :btn-info="item">
            <ui-btn-tip
              v-for="(btnItem, btnIndex) in item.btnArr"
              :key="btnIndex"
              class="mr-sm"
              :icon="btnItem.icon"
              :content="btnItem.text"
              @handleClick="$emit(btnItem.emitFun, row)"
            ></ui-btn-tip>
          </slot>
        </div>
      </template>
    </statistical-List>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import downLoadTips from '@/mixins/download-tips';
import tableColumns from './util/tableColumns.js';
import detectionResult from '@/config/api/detectionResult';
import { HIDE_UNDETECTED_VALUE } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AddHideUndetectedBox.js';

export default {
  name: 'statistical-results',
  mixins: [dealWatch, downLoadTips],
  props: {
    formItemData: {},
    formData: {},
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    filterColumsMethod: {
      type: Function,
    },
    showColRegion: {
      type: Boolean,
    },
  },
  data() {
    return {
      exportLoading: false,
      tableColumns: [],
      resultData: [],
      searchData: {},
      paramsList: {},
      codeKey: '',
      sortValue: {},
      otherSearchData: {
        isHide: undefined, // 是否隐藏  1 是 0 否  默认选中
      },
    };
  },
  computed: {
    getCheckLabel() {
      return this.paramsList.statisticType === 'REGION' ? '隐藏未检测区划' : '隐藏未检测单位';
    },
    isShowHideCheckbox() {
      return HIDE_UNDETECTED_VALUE.includes(this.paramsList.indexType);
    },
  },
  created() {
    this.paramsList = this.$route.query;
    this.getTableColumns();
    if (this.isShowHideCheckbox) {
      this.otherSearchData.isHide = 1;
    }
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    // 获取统计列表数据
    async getStatisticalList(isSort = false) {
      try {
        if (isSort) {
          this.$refs.StatisticalList.loading = true;
        }
        let params = {
          // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          ...this.otherSearchData,
          ...this.sortValue,
        };
        if (this.paramsList.canPlay && this.paramsList.canPlay !== '2') {
          Object.assign(params, {
            customParameters: {
              canPlay: this.paramsList.canPlay,
            },
          });
        }
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        const rationalityIndexId = [
          '2014',
          '3018',
          '2020',
          '3025',
          '4025',
          '4028',
          '5002',
          '5003',
          '5005',
          '7002',
          '7003',
          '7004',
          '2019',
          '3023',
        ]; // 人车合理性指标的表格相对其他指标会多几个字段
        this.resultData = this.$util.common.deepCopy(data);
        if (!rationalityIndexId.includes(this.paramsList.indexId) || !this.resultData.length) return false;
        this.resultData = data.map((item) => {
          item = Object.assign(item, item.detail || {});
          return item;
        });
        this.getTableColumns();
      } catch (e) {
        console.log(e);
      }
    },
    startSearch(searchParams) {
      this.$set(this, 'searchData', searchParams);
      this.getStatisticalList();
    },
    onSortChange(obj) {
      let { order, key } = obj;
      let val = order.toUpperCase();
      if (val !== 'NORMAL') {
        this.sortValue = {
          sortField: key === 'civilName' ? 'civil_code' : key === 'orgName' ? 'org_code' : '',
          sort: val,
        };
      } else {
        this.sortValue = {};
      }
      this.getStatisticalList(true);
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          ...this.otherSearchData,
        };
        if (this.paramsList.canPlay && this.paramsList.canPlay !== '2') {
          Object.assign(params, {
            customParameters: {
              canPlay: this.paramsList.canPlay,
            },
          });
        }
        this.$_openDownloadTip();
        const res = await this.$http.post(detectionResult.exportStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    handleViewDetail(row) {
      this.$emit('viewDetail', row);
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.$refs.StatisticalList.init();
    },
    rowClassName(row) {
      const rowCodeKey = this.paramsList.statisticType === 'REGION' ? 'civilCode' : 'orgCode';
      if (row[rowCodeKey] === this.paramsList[this.codeKey] && row.dataType !== '3') {
        return 'active-blue';
      }
      return '';
    },
    getTableColumns() {
      //根据指标获取columns
      let obj = { ...this.paramsList, showColRegion: this.showColRegion, indexName: this.activeIndexItem.indexName };
      this.tableColumns = tableColumns(obj)[this.paramsList.indexType];
      this.tableColumns = this.tableColumns.map((item) => {
        if (item.isModifyName) {
          item.title = this.activeIndexItem.indexName;
        }
        return item;
      });
      //使用props传入的过滤列的方法
      if (this.filterColumsMethod) {
        this.tableColumns = this.filterColumsMethod(this.tableColumns, this.resultData);
      }
    },
    // 跳转到 填报准确率
    clickJump(row) {
      let obj = row?.detail?.onlineRateVO;
      if (!obj) return;
      let { batchId, indexId, indexType, taskSchemeId, regionCode, orgCode, statisticType } = row.detail.onlineRateVO;
      if (!batchId) return;
      this.$router.push({
        name: 'evaluationoResult',
        query: {
          orgCode: orgCode,
          regionCode: regionCode,
          statisticType: statisticType,
          taskSchemeId: taskSchemeId,
          indexId: `${indexId}`,
          indexType: indexType,
          batchId: batchId,
        },
      });
    },
    getAccuracyRate(row) {
      return row?.detail?.onlineRateVO?.accuracyRate || row?.detail?.onlineRateVO?.accuracyRate === 0;
    },
  },
  components: {
    StatisticalList: require('../../ui-pages/statistical-list/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-results {
  padding: 10px 12px 0;
}
.monitor-img {
  position: relative;
  width: 56px;
  height: 56px;
  margin: 5px 0;
  cursor: pointer;
}
.span-link {
  color: rgb(43, 132, 226);
  cursor: pointer;
  text-decoration: underline;
}
//@{_deep}.active-blue td {
//  color: #19d5f6 !important;
//}
</style>
