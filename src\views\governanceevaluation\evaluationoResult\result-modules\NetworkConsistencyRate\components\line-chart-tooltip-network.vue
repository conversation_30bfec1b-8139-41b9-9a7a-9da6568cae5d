<template>
  <div class="tooltip-container">
    <div v-for="(item, index) in data" :key="index">
      <div class="t-div">
        <span>{{ formatDateTime(item.data.startTime).ymd }}</span>
        <span>{{ formatDateTime(item.data.startTime).hms }}</span>
      </div>
      <div class="t-div">
        <span> {{ item.data.indexName }}： </span>
        <span> {{ item.data.value || 0 }}% </span>
      </div>
      <div class="t-div">
        <span>已上报视图库人脸卡口总量：</span>
        <span>
          {{ item.data.actualNum || 0 }}
        </span>
      </div>
      <div class="t-div">
        <span>有图片未上报视图库人脸卡口数量：</span>
        <span>
          {{ item.data.qualifiedNum || 0 }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'line-chart-tooltip-network.vue',
  components: {},
  props: {},
  data() {
    return {
      tipTitle: {
        title1: '',
        title2: '',
      },
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) {
        return { ymd: '', hms: '' };
      }
      let dateObject = new Date(dateTime);
      let year = dateObject.getFullYear();
      let month = dateObject.getMonth() + 1 < 10 ? '0' + (dateObject.getMonth() + 1) : dateObject.getMonth() + 1;
      let date = dateObject.getDate() < 10 ? `0${dateObject.getDate()}` : dateObject.getDate();
      let hours = dateObject.getHours() < 10 ? `0${dateObject.getHours()}` : dateObject.getHours();
      let minutes = dateObject.getMinutes() < 10 ? `0${dateObject.getMinutes()}` : dateObject.getMinutes();
      let seconds = dateObject.getSeconds() < 10 ? `0${dateObject.getSeconds()}` : dateObject.getSeconds();
      return {
        ymd: `${year}年${month}月${date}日`,
        hms: `${hours}:${minutes}:${seconds}`,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.tooltip-container {
  background: rgba(13, 53, 96, 0);
  border: none;
  opacity: 1;
  .block {
    display: inline-block;
    height: 6px;
    width: 6px;
    line-height: 14px;
  }
  .t-div {
    display: flex;
    justify-content: space-between;
  }
}
</style>
