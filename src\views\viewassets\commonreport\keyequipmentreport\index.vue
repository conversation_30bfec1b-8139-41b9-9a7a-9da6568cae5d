<template>
  <div class="key-equipment-report auto-fill">
    <div class="config" @click="configFn()">
      <i class="icon-font icon-jichushuxing"></i>
    </div>
    <!-- 顶部统计 -->
    <chartsContainer :abnormalCount="countList" class="charts" />
    <!--搜索参数-->
    <div class="search-module">
      <ui-label class="inline mr-lg" label="组织机构">
        <api-organization-tree
          class="tree-style"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree1"
          placeholder="请选择组织机构"
        />
      </ui-label>
      <ui-label class="inline mr-lg" label="行政区划">
        <api-area-tree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </ui-label>
      <ui-label class="inline mr-lg" :label="global.filedEnum.deviceId">
        <Input v-model="searchData.deviceId" class="width-md" :placeholder="`${global.filedEnum.deviceId}`" />
      </ui-label>
      <ui-label class="inline mr-lg" :label="global.filedEnum.deviceName">
        <Input v-model="searchData.deviceName" class="width-md" :placeholder="global.filedEnum.deviceName" />
      </ui-label>
      <ui-label label="上报状态" class="inline mr-lg">
        <Select class="width-sm" v-model="searchData.keyDeviceReportStatus" placeholder="请选择上报状态" clearable>
          <Option v-for="(item, index) in keyDeviceReportStatusList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="fr">
        <UiSearch class="ui-search mr-lg" v-model="searchModel">
          <template #content>
            <div class="search-content">
              <ui-label :label="global.filedEnum.sbdwlx" class="inline mr-lg mb-sm">
                <Select
                  class="width-md"
                  v-model="searchData.sbdwlx"
                  :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey">{{
                    item.dataValue
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.sbgnlx" class="inline mr-lg mb-sm">
                <Select
                  class="width-md"
                  v-model="searchData.sbgnlx"
                  clearable
                  multiple
                  :max-tag-count="1"
                  :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                >
                  <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">{{
                    item.dataValue
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label label="重点采集区域类型" class="inline mr-lg mb-sm">
                <Select
                  class="width-lg"
                  v-model="searchData.sbcjqy"
                  placeholder="请选择重点采集区域类型"
                  clearable
                  multiple
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in keyDeviceGatherArea" :key="index" :value="item.code">{{
                    item.name
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.phyStatus" class="inline mr-lg mb-sm">
                <Select
                  class="width-sm"
                  v-model="searchData.phyStatus"
                  :placeholder="`请选择${global.filedEnum.phyStatus}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="inline mr-lg mb-sm" label="在线状态">
                <Select class="width-md" v-model="searchData.isOnline" clearable placeholder="请选择设备在线状态">
                  <Option v-for="(item, index) in propertySearch_isonline" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="inline mr-lg mb-sm" label="上报时间">
                <DatePicker
                  class="width-md"
                  v-model="searchData.keyRecentlyReportTimeStart"
                  type="datetime"
                  placeholder="请选择开始时间"
                  :options="startTimeOption"
                  confirm
                ></DatePicker>
                <span class="ml-sm mr-sm">--</span>
                <DatePicker
                  class="width-md"
                  v-model="searchData.keyRecentlyReportTimeEnd"
                  type="datetime"
                  placeholder="请选择结束时间"
                  :options="endTimeOption"
                  confirm
                ></DatePicker>
              </ui-label>

              <ui-select-tabs
                class="ui-select-tabs"
                :list="MixinTagList"
                @selectInfo="MixinSelectInfo"
                ref="uiSelectTabs"
              >
              </ui-select-tabs>
            </div>
          </template>
        </UiSearch>
        <Button type="primary" @click="searchHandle">查询</Button>
        <Button class="ml-sm" @click="resetHandle">重置</Button>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="operation">
      <Checkbox v-model="allCheck" @on-change="handleAllCheck">全选</Checkbox>
      <div class="right-btn fr">
        <Button type="primary" @click="exportDevice">
          <i class="icon-font icon-daochu mr-xs f-12"></i>
          <span>导出</span>
        </Button>
        <Button type="primary" @click="keyEquipmentReportHandle" :loading="isReportLoading">
          <i class="icon-font icon-keyequipmentreportBtn"></i>
          重点设备上报
        </Button>
      </div>
    </div>
    <!-- 表格 -->
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        reserveSelection
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :is-all="allCheck"
        :default-store-data="selectTable"
        @storeSelectList="storeSelectList"
      >
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltipType">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #keyDeviceReportStatus="{ row }">
          <div>
            <span
              class="check-status"
              :class="[
                row.keyDeviceReportStatus === '2' ? 'bg-failed' : '',
                row.keyDeviceReportStatus === '1' ? 'bg-success' : '',
              ]"
            >
              {{ row.keyDeviceReportStatus | filterType(keyDeviceReportStatusList) }}
            </span>
          </div>
        </template>
        <template #keyDeviceReportDate="{ row }">
          <span>{{ row.keyDeviceReportDate || '--' }}</span>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize" />
    </div>
    <!-- 配置 -->
    <config-page ref="config" v-model="configShow" @on-handle-success="queryDeviceCount" />
    <customize-filter
      v-model="customFilter"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading"
            >{{ exportDataLoading ? '下载中' : '数据导出' }}
          </Button>
        </Tooltip>
      </template>
    </customize-filter>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import viewassets from '@/config/api/viewassets';
import taganalysis from '@/config/api/taganalysis';
import getTagMixin from '@/views/viewassets/mixins/getTagMixin';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [getTagMixin, downLoadTips],
  name: 'keyequipmentreport',
  components: {
    apiOrganizationTree: require('@/api-components/api-organization-tree').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    chartsContainer: require('./chartsContainer').default,
    configPage: require('./config/configpage').default,
    UiSearch: require('@/components/ui-search').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
  data() {
    return {
      searchModel: false,
      allCheck: false,
      selectOrgTree: {
        orgCode: '',
      },
      selectTree: {
        regionCode: '',
      },
      keyDeviceGatherArea: [],
      keyDeviceReportStatusList: [
        { dataKey: '0', dataValue: '未上报' },
        { dataKey: '1', dataValue: '已上报' },
        { dataKey: '2', dataValue: '上报失败' },
      ], //重点设备上报状态
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      searchData: {
        isOnline: '',
        orgCode: '',
        configOrgCode: '',
        civilCode: '',
        deviceId: '',
        deviceName: '',
        keyDeviceReportStatus: '',
        sbdwlx: '',
        sbgnlx: [],
        sbcjqy: [],
        phyStatus: '',
        keyRecentlyReportTimeStart: '',
        keyRecentlyReportTimeEnd: '',
        isImportant: 1,
        cascadeReportStatus: 1,
        pageNumber: 1,
        pageSize: 20,
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.keyRecentlyReportTimeEnd);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.keyRecentlyReportTimeStart);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
          align: 'left',
        },
        {
          minWidth: 100,
          title: '采集区域',
          key: 'sbcjqyText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '在线状态',
          slot: 'isOnline',
          align: 'left',
          // fixed: 'right',
        },
        {
          width: 120,
          title: this.global.filedEnum.phyStatus,
          slot: 'phyStatus',
        },
        { width: 120, title: '上报状态', slot: 'keyDeviceReportStatus' },
        { width: 150, title: '上报时间', slot: 'keyDeviceReportDate' },
      ],
      tableData: [],
      loading: false,
      countList: [
        { title: '重点设备数量', total: '0', icon: 'icon-keyequipment' },
        {
          title: '上报数量',
          total: '0',
          icon: 'icon-zichanshangbao',
        },
        {
          title: '上报人脸卡口',
          total: '0',
          icon: 'icon-shangbaorenliankakou',
        },
        {
          title: '上报车辆卡口',
          total: '0',
          icon: 'icon-shangbaocheliangkakou',
        },
        {
          title: '上报视频监控',
          total: '0',
          icon: 'icon-shangbaoshipinjiankong',
        },
      ],
      configShow: false,
      selectTable: [],
      isReportLoading: false,
      // 下载模板、导出
      customFilter: false,
      exportDataLoading: false,
      exportUrl: viewassets.asertCenterExportReportDevice,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: `3.125rem`,
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus',
      propertySearch_isonline: 'algorithm/propertySearch_isonline', // 在线状态
    }),
    isRepeatRecord() {
      return this.activeKey === 5;
    },
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    this.getKeyAreaList();
    this.queryDeviceCount();
    this.getPropertyList();
  },
  mounted() {
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 全选
    handleAllCheck() {
      this.selectTable = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.checkAll);
        this.$set(item, '_disabled', this.checkAll);
        return item;
      });
    },
    storeSelectList(selection) {
      this.selectTable = selection;
    },
    //获取重点采集区域类型
    getKeyAreaList() {
      this.loading = true;
      this.$http
        .post(viewassets.getKeyEquipmentConfig, {})
        .then((res) => {
          this.keyDeviceGatherArea = res.data.data;
          this.init();
        })
        .catch(() => {
          this.loading = false;
        })
        .finally(() => {});
    },
    init() {
      this.loading = true;
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      if (params.keyRecentlyReportTimeStart) {
        params.keyRecentlyReportTimeStart = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeStart);
      }
      if (params.keyRecentlyReportTimeEnd) {
        params.keyRecentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeEnd);
      }
      this.$http
        .post(viewassets.queryDeviceInfoPageList, params)
        .then((res) => {
          let { entities, total } = res.data.data;
          this.pageData.totalCount = total;
          this.tableData = entities;
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 获取头部统计信息
    async queryDeviceCount() {
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      if (params.keyRecentlyReportTimeStart) {
        params.keyRecentlyReportTimeStart = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeStart);
      }
      if (params.keyRecentlyReportTimeEnd) {
        params.keyRecentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeEnd);
      }
      try {
        let res = await this.$http.post(viewassets.queryKeyDeviceInfoStatistics, params);
        let obj = res.data.data;
        this.countList[0].total = obj.total;
        this.countList[1].total = obj.reportedKeyDevice.total;
        this.countList[1].thresholdReached = obj.reportedKeyDevice.thresholdReached;
        this.countList[1].gatherArea = obj.reportedKeyDevice.gatherArea;

        this.countList[2].total = obj.reportedFaceBayonet.total;
        this.countList[2].thresholdReached = obj.reportedFaceBayonet.thresholdReached;
        this.countList[2].gatherArea = obj.reportedFaceBayonet.gatherArea;

        this.countList[3].total = obj.reportedVehicleBayonet.total;
        this.countList[3].thresholdReached = obj.reportedVehicleBayonet.thresholdReached;
        this.countList[3].gatherArea = obj.reportedVehicleBayonet.gatherArea;

        this.countList[4].total = obj.reportedVideoMonitor.total;
        this.countList[4].thresholdReached = obj.reportedVideoMonitor.thresholdReached;
        this.countList[4].gatherArea = obj.reportedVideoMonitor.gatherArea;
      } catch (err) {
        console.error('err', err);
      } finally {
        // this.loading = false
      }
    },
    // 配置
    configFn() {
      this.$refs.config.show();
    },
    // 选择组织机构
    selectedOrgTree1(val) {
      this.searchData.orgCode = val.orgCode;
      this.searchData.configOrgCode = val.orgCode;
      this.searchHandle();
    },
    // 选择行政区划
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // 查询
    searchHandle() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
      this.queryDeviceCount();
    },
    // 重置
    resetHandle() {
      this.selectOrgTree.orgCode = '';
      this.selectTree.regionCode = '';
      this.selectTable = [];
      this.$refs.uiSelectTabs && this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.searchHandle);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.searchHandle();
    },
    // 设备档案
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    // 重点设备上报
    keyEquipmentReportHandle() {
      let selectedDataLen = this.selectTable.length;
      if (!selectedDataLen && !this.allCheck) {
        this.$Message.warning('请选择上报设备');
        return false;
      } else {
        this.$UiConfirm({
          content: `已选择${this.allCheck ? this.pageData.totalCount : selectedDataLen}条设备，确定上报吗？`,
          title: '重点设备上报',
        })
          .then(() => {
            this.isReportLoading = true;
            let data = this.allCheck ? [] : this.selectTable;
            let params = {
              deviceInfoSummaries: data,
              orgCode: this.searchData.orgCode,
            };
            this.$http
              .post(
                viewassets.postKeyEquipmentReport,
                Object.assign(params, this.allCheck ? this.searchDataParams() : {}),
              )
              .then(() => {
                this.$Message.success('重点设备上报成功');
              })
              .catch(() => {})
              .finally(() => {
                this.isReportLoading = false;
                this.selectTable = [];
                this.allCheck = false;
                this.init();
                this.queryDeviceCount();
              });
          })
          .catch(() => {});
      }
    },
    // 参数处理
    searchDataParams() {
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      params.cascadeReportStatus = 1;
      if (params.keyRecentlyReportTimeStart) {
        params.keyRecentlyReportTimeStart = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeStart);
      }
      if (params.keyRecentlyReportTimeEnd) {
        params.keyRecentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.keyRecentlyReportTimeEnd);
      }
      return params;
    },
    exportDevice() {
      this.customFilter = true;
      this.defaultCheckedList = [
        'deviceId',
        'id',
        'cascadeReportStatus',
        'recentlyReportStatus',
        'recentlyReportTime',
        'reportSuccessTime',
        'orgCode',
        'civilCode',
        'deviceName',
        'sbgnlx',
        'sbdwlx',
        'ipAddr',
        'macAddr',
        'longitude',
        'latitude',
        'sbcjqy',
        'phyStatus',
      ];
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = [
          ...data.data,
          ...[
            { propertyName: 'cascadeReportStatus', propertyColumn: '上报状态' },
            { propertyName: 'recentlyReportStatus', propertyColumn: '最近一次上报状态' },
            { propertyName: 'recentlyReportTime', propertyColumn: '最近一次上报时间' },
            { propertyName: 'reportSuccessTime', propertyColumn: '最近成功上报时间' },
          ],
        ];
        this.leftDisabled = (item) => {
          return (
            item.propertyName === 'deviceId' ||
            item.propertyName === 'id' ||
            item.propertyName === 'cascadeReportStatus' ||
            item.propertyName === 'recentlyReportStatus' ||
            item.propertyName === 'recentlyReportTime' ||
            item.propertyName === 'reportSuccessTime'
          );
        };
        this.rightDisabled = (item) => {
          return !(
            item.propertyName === 'deviceId' ||
            item.propertyName === 'id' ||
            item.propertyName === 'cascadeReportStatus' ||
            item.propertyName === 'recentlyReportStatus' ||
            item.propertyName === 'recentlyReportTime' ||
            item.propertyName === 'reportSuccessTime'
          );
        };
      } catch (err) {
        console.log(err);
      }
    },
    async exportExcel(propertyList) {
      try {
        this.$_openDownloadTip();
        this.exportDataLoading = true;
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          ids: this.selectTable.map((item) => item.id),
          isImportant: 1,
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(this.exportUrl, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportDataLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue']{
  .config .icon-font{
    color: #888888;
    &:hover {
      color: var(--color-switch-tab-active);
    }
  }
}
.key-equipment-report {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);

  .charts {
    margin: 20px 0 0 20px;
  }

  .search-module {
    margin: 10px 20px;
    position: relative;
    @{_deep} .ui-select-tabs {
      .single {
        left: 0;
      }
    }
    .keyword-input {
      width: 300px;
    }
  }

  .ui-table {
    padding: 0 20px;
  }
  .tooltipType {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.operation {
  margin: 0 20px 10px 20px;
  line-height: 34px;

  .right-btn {
    button {
      margin-left: 12px;
    }
  }
}

.config {
  position: absolute;
  right: 20px;
  top: 20px;
  color: var(--color-switch-tab);
  cursor: pointer;

  .icon-font {
    font-size: 20px;
  }

  &:hover {
    color: var(--color-switch-tab-active);
  }
}

@{_deep} .ivu-badge-dot {
  top: 1px;
  right: -15px;
  border: 0;
  z-index: 3;
}

@{_deep} .icon-font {
  font-size: 15px;
}
</style>
