<template>
  <div>
    <ui-modal ref="modal" :title="title" :styles="styles">
      <!-- <div class="updata-time">
				<div class="search">
					<label for>组织机构</label>
					<api-organization-tree
						:select-tree="selectOrgTree"
						@selectedTree="selectedOrgTree"
						placeholder="请选择"
					></api-organization-tree>
					<Button type="primary" class="mr-sm" @click="search">查询</Button>
					<Button type="default" class="mr-sm" @click="clear">重置</Button>
				</div>
			</div> -->
      <searchView :isOutput="true" @startSearch="search"></searchView>

      <div class="echarts-title">
        <i></i>
        <div class="titles">检测结果统计</div>
      </div>
      <div class="echarts" v-if="$parent.list && $parent.list.length > 0">
        <Row :gutter="10">
          <Col span="10">
            <div class="morePie">
              <dataPie ref="morePic" :ListNum="$parent.list" />
            </div>
          </Col>
          <Col span="14">
            <div class="singlePie">
              <!-- <ul class="ul-echart">
                <li :class="{active: curTag == 1}" @click="curTag = 1">异常数据</li>
                <li :class="{active: curTag == 2}" @click="curTag = 2">治理优化</li>
							</ul>-->
              <barEchart ref="singlePic1" :dataNum="222" color="#19C176" refName="echart-1" :ListNum="$parent.list" />
            </div>
          </Col>
        </Row>
      </div>
      <div class="echarts-title">
        <i></i>
        <div class="titles">问题数据列表</div>
      </div>
      <div class="tab-page">
        <span
          @click="check(index, item)"
          :class="item.ischeck == true ? 'cur' : ''"
          v-for="(item, index) in titles"
          :key="index"
          >{{ item.dataValue }}</span
        >
      </div>
      <ui-table
        class="ui-table"
        :table-columns="columns"
        :table-data="tableData"
        :loading="loading"
        :minus-height="minusTable"
      >
        <template #deviceId="{ row }">
          <div :class="row.rowClass">
            <Button type="text" @click="deviceArchives(row)">
              {{ row.deviceId }}
            </Button>
          </div>
        </template>
        <template #no="{ row }">
          <!-- <span class="red">{{row.errorMessage}}</span> -->
          <span @click="errorDitail(row)" class="red" style="cursor: pointer">查看异常原因</span>
        </template>
        <template #macAddr="{ row }">
          <span>{{ row.macAddr || '--' }}</span>
        </template>
        <template #ipAddr="{ row }">
          <span>{{ row.ipAddr || '--' }}</span>
        </template>
      </ui-table>
      <!-- <Row :gutter="16">
        <Col class="carItem" span="3" v-for="(item, index) in 16" :key="index">
          <div class="item">
            <div class="img">
                <viewer :images="images">
                  <img v-for="src in images" :src="src" :key="src">
                </viewer>
              <div class="btn">
                <img src="@/assets/img/map/map-camera-body.png" alt="">
                <img style="filter: sepia(100%);" src="@/assets/img/map/map-camera-body.png" alt="" @click.stop="abnormalDetail">
              </div>
            </div>

            <div class="text">
              <Icon class="config" type="ios-settings-outline" />
              {{time}}
            </div>
            <div class="address" :title="address">
              <Icon class="config" type="ios-settings-outline" />
              {{address}}
            </div>
          </div>
        </Col>
			</Row>-->
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>

      <div slot="footer"></div>
    </ui-modal>

    <errorView ref="errorView" :tableDate="errorList" />
  </div>
</template>
<script>
import api from '@/config/api/vedio-threm.js';
import dictData from '@/config/api/user.js';
export default {
  name: 'outputView',
  props: {},
  data() {
    return {
      titles: [],
      title_list: [],
      selectOrgTree: {
        orgCode: null,
      },
      model1: '',
      loading: false,
      title: '数据输出',
      showEchart: false,
      styles: {
        width: '95%',
        'padding-bottom': '20px',
      },
      minusTable: 620,
      time: '2021-05-05 12:30:30',
      address: '手动阀手动阀手动阀手动阀史蒂夫罗杰斯独领风骚砥砺奋进杀了对方士大夫',
      images: [require('@/assets/img/navigation-page/systemmanagement.png')],
      curItem: 1,
      timeList: [
        { value: 1, label: '时' },
        { value: 2, label: '分' },
        { value: 3, label: '秒' },
      ],
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
        orgCode: null,
        reasonTypes: [6, 7, 8], // 异常选项数组
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      columns: [
        { type: 'index', width: 70, title: '序号' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          width: 220,
        },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
        { title: '组织机构', key: 'orgCode' },
        { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
        { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
        { title: `${this.global.filedEnum.macAddr}`, slot: 'macAddr' },
        { title: this.global.filedEnum.ipAddr, slot: 'ipAddr' },
        { title: '安装地址', key: 'address' },
        { title: '不合格原因', key: 'errorMessage', slot: 'no' },
      ],
      tableData: [],
      curTag: 1,
      echartSingle: [],
      errorList: [],
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  mounted() {
    this.init();
  },
  methods: {
    check(index, item) {
      let arr = [...this.titles];
      let selarr = [...this.title_list];
      if (arr[index].ischeck == false) {
        arr[index].ischeck = true;
        selarr.push(item);
      } else {
        arr[index].ischeck = false;
        var index11 = selarr.indexOf(index);
        selarr.splice(index11, 1);
      }
      this.titles = arr;
      this.title_list = selarr;
      let list = [];
      this.titles.forEach((item) => {
        if (item.ischeck) {
          list.push(item.dataKey);
        }
      });

      this.searchData.reasonTypes = list;
      this.getVedioPage(list);
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.getVedioPage();
    },
    init() {
      this.echartSingle = [26598, 65986, 69853];
      this.getDictData();
      // this.$refs.morePic.resizeFn();
      // this.$refs.morePic.init();
      // this.$refs.singlePic1.init();
      // this.$refs.singlePic2.init();
      // this.$refs.singlePic3.init();
    },
    // 获取字典表接口
    getDictData() {
      this.$http
        .get(dictData.queryByTypeKey, {
          params: { typekey: 'video_reason_type' },
        })
        .then((res) => {
          if (res.data.code == 200) {
            this.titles = res.data.data;
            this.titles.forEach((item) => {
              item.ischeck = false;
            });
            console.log(this.titles);
          }
        });
    },
    clear() {
      this.selectOrgTree.orgCode = null;
      this.pageData.pageNum = 1;
      for (let i of this.titles) {
        i.ischeck = false;
      }
      this.resetSearchDataMx(this.searchData, this.getVedioPage(this.cur));
      // this.searchData = {
      //   name: "",
      //   params:{
      //     pageNumber: 1,
      //     pageSize: 20,
      //   },
      //   totalCount: 0,
      //   orgCode: null,
      // }
      // this.pageData.pageNum = 1;
      // this.getVedioPage(this.cur);
    },
    search() {
      this.getVedioPage(this.cur);
    },
    getVedioPage() {
      this.loading = true;
      this.$http.post(api.queryVideoResultPageListToGroup, this.searchData).then((res) => {
        if (res.data.code == 200) {
          this.loading = false;
          this.tableData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        }
      });
    },
    showModal() {
      this.$refs.modal.modalShow = true;
      this.getVedioPage([6, 7, 8]);
      this.$nextTick(() => {
        this.$router.push({ query: {} });
      });
    },

    abnormalDetail() {
      this.$refs.yichang.showModal();
    },

    showImg() {
      var url =
        'http://file.baiyh.info/public/baiyh/08c06ca18f81dcbe410d296c35a520fe73a24a4284806e7e0c72623a113f616e.png';
      this.images = [];
      this.images.push(url);
    },

    tabClick() {},
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.getVedioPage(this.cur);
    },

    changePageSize(val) {
      this.searchData.params.pageSize = val;
      this.searchData.params.pageNumber = 1;
      this.pageData.pageSize = val;
      this.getVedioPage(this.cur);
    },
    errorDitail(row) {
      this.$refs.errorView.showModal({
        deviceIds: [row.deviceId],
        reasonTypes: this.searchData.reasonTypes,
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
  },
  watch: {},
  components: {
    dataPie: require('./pie-data-echart').default,
    barEchart: require('./bar-echart').default,
    UiTable: require('@/components/ui-table.vue').default,
    // tagView: require('./tags').default,
    // ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    // UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    searchView: require('./search.vue').default,
    errorView: require('./error.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-title {
  width: 100%;
  display: flex;
  margin-top: 10px;
  height: 30px;
  margin-bottom: 10px;
  i {
    width: 8px;
    height: 30px;
    background: #239df9;
    margin-right: 6px;
  }
  .titles {
    height: 30px;
    line-height: 30px;
    width: 100%;
    padding-left: 5px;
    background-image: linear-gradient(to right, #0a4f8d, #09284d);
  }
}
.tab-page {
  display: flex;
  align-items: center;
  height: 48px;
  background-color: #0f2f53;
  padding: 7px 10px;
  margin-bottom: 10px;
  span {
    padding: 8px 22px;
    color: #8797ac;
    border: 1px solid #0375b4;
    border-radius: 4px;
    margin-right: 10px;
    cursor: pointer;
  }
  .cur {
    background: #17497e;
    border: 1px solid var(--color-primary);
    color: #fff;
  }
}
.echarts {
  width: 100%;
  height: 150px;
  .morePie {
    width: 100%;
    background: var(--bg-sub-content);
    height: 150px;
    // background: #0b3469;
  }
  .singlePie {
    width: 100%;
    height: 150px;
    background: var(--bg-sub-content);
  }
}

.ivu-checkbox-group-item {
  padding: 6px 0;
}

.quality {
  padding: 10px 0;
  color: #fff;
}

.explain {
  color: #c76d28;
}
.p {
  margin-top: 10px;
  color: #fff;
}
.ul {
  overflow: hidden;
  li {
    position: relative;
    float: left;
    font-size: 14px;
    padding: 6px 16px;
    background: var(--color-primary);
    color: #fff;
    border-radius: 4px;
    margin-right: 10px;
    margin-top: 10px;
  }

  .add {
    border: 1px solid #037cbe;
    background: transparent;
    padding: 6px 16px;
    cursor: pointer;
  }

  .close {
    position: absolute;
    right: -3px;
    top: -3px;
    opacity: 0.8;
    cursor: pointer;
  }
  .close:hover {
    opacity: 1;
  }
}

.search-wrapper {
  width: 100%;
  height: 50px;
}

/deep/ .ivu-modal-body {
  color: #fff;
}

/deep/ .ivu-modal-footer {
  height: 0 !important;
  padding: 0 !important;
}

.carItem {
  height: 200px;
  padding: 10px;
  .item {
    height: 100%;
    background: #22416f;
    .img {
      position: relative;
      width: 100%;
      height: 130px;
      .btn {
        position: absolute;
        width: 100%;
        height: 30px;
        background: rgba(0, 0, 0, 0.2);
        bottom: 0;
        display: none;
        img {
          margin-left: 10px;
          width: 26px;
          height: 26px;
          margin-top: 2px;
          cursor: pointer;
        }
      }
    }
    .img:hover {
      .btn {
        display: block;
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      height: 130px;
      max-height: 160px;
      background: #999;
    }
    .text {
      padding: 0 6px;
      font-size: 12px;
      overflow: hidden; /*超出部分隐藏*/
      white-space: nowrap; /*禁止换行*/
      text-overflow: ellipsis; /*省略号*/
    }
    .address {
      padding: 0 6px;
      font-size: 12px;
      overflow: hidden; /*超出部分隐藏*/
      white-space: nowrap; /*禁止换行*/
      text-overflow: ellipsis; /*省略号*/
    }
  }
}

.ul2 {
  width: 500px;
  // border: 1px solid #999;
  overflow: hidden;
  // margin-left: 10px;

  li {
    float: left;
    // width: 100px;
    text-align: center;
    color: #fff;
    border: 1px solid #1b82d2;
    padding: 10px 20px;
    margin-right: -1px;
    border-radius: 2px;
    cursor: pointer;
  }
  .active {
    background: var(--color-primary);
    cursor: default;
  }
}

.ul-echart {
  position: absolute;
  // width: 500px;
  // border: 1px solid #999;
  // overflow: hidden;
  // margin-left: 10px;
  top: 6px;
  right: 20px;
  z-index: 10;
  border: 1px solid #1b82d2;
  li {
    float: left;
    text-align: center;
    color: #fff;
    padding: 5px 20px 2px;
    margin-right: -1px;
    border-radius: 2px;
    cursor: pointer;
  }
  .active {
    background: var(--color-primary);
    cursor: default;
  }
}

.blue {
  color: var(--color-primary);
}
.red {
  display: inline-block;
  color: #e44f22;
  width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cyan {
  color: var(--color-bluish-green-text);
}

.topImg {
  // position: fixed;
  // width: 100%;
  // height: 100%;
  // top: 0;
}

/deep/ .ivu-tabs {
  color: #fff;
}
.updata-time {
  label {
    margin-right: 10px;
  }
}
</style>
