/**
 * 此mixin针对治理工单 - 去填报修改入库数据
 * 修改前进行入库检测的情况（与资产填报已入库设备的修改入库检测一样）
 */
import equipmentassets from '@/config/api/equipmentassets';
import user from '@/config/api/user';
import governanceevaluation from '@/config/api/governanceevaluation';
import {filterIndexType} from '@/views/disposalfeedback/governanceorder/util/enum';
const mixin = {
  data() {
    return {
      compulsoryLoading: false,
      compulsoryResolve: null,
      compulsoryReject: null,
      // 上传检测
      uploadErrorVisible: false,
      footerHide: false,
      errorData: [],
      errorColumns: [],
      indexModules: [],
      allIndexData: [],
      filterIndexData: [],
    };
  },
  methods: {
    // 检测方法
    MixinCheckFun(deviceData) {
      return new Promise(async (resolve, reject) => {
        try {
          this.compulsoryResolve = resolve;
          this.compulsoryReject = reject;
          await this.fillUploadCheck(deviceData);
        } catch (err) {
          reject(false);
        }
      });
    },
    // 原始库更新检测
    async fillUploadCheck(deviceData) {
      // 如果为已入库设备，则需传入deviceInfoId并且将id为空
      let params = {
        id: null,
        deviceInfoId: deviceData.id,
      };
      try {
        // step 调用updateCheck使用，1- 校验 设备编码 2-校验检测规则。修改原始库设备信息时，两次调用updateCheck接口，第一次传1 第二次传2
        await this.$http.post(equipmentassets.updateCheckFill, Object.assign({ step: 1 }, deviceData, params));
        await this.$http.post(equipmentassets.updateCheckFill, Object.assign({ step: 2 }, deviceData, params));
        this.compulsoryResolve(true);
      } catch (err) {
        console.log(err, 'err');
        switch (err.data.code) {
          // 检测出错误
          case 9006400:
            this.checkError(err);
            break;
          // 库中已有该数据
          case 9006402:
            this.$UiConfirm({
              content: err.data.msg,
              title: '警告',
            })
              .then(async () => {
                try {
                  await this.$http.post(
                    equipmentassets.updateCheckFill,
                    Object.assign({ step: 2 }, deviceData, params),
                  );
                  this.compulsoryResolve(true);
                } catch (err) {
                  console.log(err);
                  switch (err.data.code) {
                    case 9006400:
                      this.checkError(err);
                      break;
                    default:
                      this.compulsoryReject(false);
                      break;
                  }
                }
              })
              .catch((res) => {
                console.log(res);
                this.compulsoryReject(false);
              });
            break;
          default:
            this.compulsoryReject(false);
            break;
        }
      }
    }, // 检测错误信息
    checkError(err) {
      this.checkData = err.data.data;
      this.errorData = JSON.parse(this.checkData.checkResultJson).filter((row) => !row.isSuccess);
      this.errorColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '不合格原因',
          key: 'errorMessage',
          tooltip: true,
        },
        {
          title: '检测规则名称',
          key: 'checkRuleName',
          tooltip: true,
        },
        {
          title: '不合格字段',
          key: 'propertyName',
          tooltip: true,
        },
        { title: '异常类型', key: 'errorType', tooltip: true },
        {
          title: '实际结果',
          key: 'propertyValue',
          tooltip: true,
        },
      ];
      this.uploadErrorVisible = true;
      this.footerHide = false;
    },
    // 强制入库
    MixinCompulsoryStorage() {
      this.uploadErrorVisible = false;
      this.compulsoryResolve(true);
    },
    MixinCancelCompulsory() {
      this.uploadErrorVisible = false;
      this.compulsoryReject(false);
    },
    async getDictData() {
      try {
        let {
          data: { data },
        } = await this.$http.get(user.queryByTypeKey, {
          params: { typekey: 'work_order_index_type' },
        });
        this.indexModules = data || [];
      } catch (err) {
        console.log(err);
      }
    },
    async getEvaluationIndex() {
      try {
        this.loading = true;
        let params = {
          indexModule: '',
          pageNumber: 1,
          pageSize: 200,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getEvaluationIndexByPage, params);
        this.allIndexData = data.entities;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    /**
     * @param indexModeule 对应 work_order_index_type
     * 治理工单-工单类型保留四项: 《1、基础教据处理、2、人脸教据处理 3、车辆数据处理 4、视频流数据处理) -选择指标保留项:(
     * 1、基础教据处理-填报准确率
     * 2、人脸数据处理。人脸卡口设备图片url、人脸卡口设备及时上传率、人脸卡口设备时钟准确率
     * 3、车辆教据处理。车辆卡口设备图片url、车辆卡口设备及时上传率、车辆卡口设备时钟准确率
     * 4、视频流数据处理.普通、重点实时可调阅率、普通、重点历史可调阅率、普通、重点时钟准确率、普通、重点宇募标注合规性)，其余全部删除
     *  2024/9/20 和后端同步，新增部分指标
     */
    getEvaluationIndexByIndexModule(indexModeule) {
      this.filterIndexData = this.allIndexData.filter((item) => item.indexModule === indexModeule && filterIndexType.includes(item.indexType));
    },
  },
};
export default mixin;
