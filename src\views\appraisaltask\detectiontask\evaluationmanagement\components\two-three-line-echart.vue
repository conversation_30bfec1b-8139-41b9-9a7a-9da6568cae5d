<template>
  <div style="width: 100%; height: 400px" id="echart_line"></div>
</template>
<script>
export default {
  name: 'barEchart',
  props: {
    lineEchartList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  mounted() {
    setTimeout(() => {
      this.init();
    }, 50);
  },
  watch: {
    // 'lineEchartList': {
    // 	deep: true,
    // 	handler () {
    // 		if (this.lineEchartList.list.length > 0) {
    // 			this.init();
    // 		}
    // 	}
    // }
  },
  methods: {
    init() {
      let myChart = this.$echarts.init(document.getElementById('echart_line'));
      var option = {
        tooltip: {
          trigger: 'axis',
          padding: [2, 10],
          textStyle: {
            fontSize: 16,
          },
        },
        grid: {
          top: '20%',
          left: '6%',
          right: '2%',
          bottom: '10%',
          // containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            axisLine: {
              show: true,
              lineStyle: {
                color: '#0375B4',
              },
            },
            splitArea: {
              // show: true,
              color: '#f00',
              lineStyle: {
                color: '#f00',
              },
            },
            axisLabel: {
              // rotate: 45,
              color: '#fff',
            },
            splitLine: {
              show: false,
            },

            boundaryGap: false,
            data: this.lineEchartList[0],
            // data: ["2017-09-11", "2017-09-12", "2017-09-13", "2017-09-14", "2017-09-15", "2017-09-16"]
          },
        ],

        yAxis: [
          {
            type: 'value',
            min: 0,
            name: '单位：百分比',
            nameTextStyle: {
              color: '#fff',
            },
            // max: 140,
            splitNumber: 4,
            splitLine: {
              show: false,
              lineStyle: {
                color: 'rgba(255,255,255,0.1)',
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: '#0375B4',
              },
            },
            axisLabel: {
              show: true,
              margin: 20,
              color: '#d1e6eb',
            },
            axisTick: {
              show: false,
            },
          },
        ],
        dataZoom: [
          {
            show: false,
            type: 'inside',
            start: 0,
            end: 100,
          },
          {
            show: false,
            start: 0,
            end: 100,
          },
        ],
        series: [
          {
            name: '百分比',
            type: 'line',
            smooth: true, //是否平滑
            showAllSymbol: true,
            // symbol: 'image://./static/images/guang-circle.png',

            itemStyle: {
              color: '#239DF9',
              borderColor: '#fff',
              borderWidth: 3,
              shadowColor: 'rgba(0, 0, 0, .3)',
              shadowBlur: 0,
              shadowOffsetY: 2,
              shadowOffsetX: 2,
            },
            tooltip: {
              show: true,
            },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: 'rgba(26, 117, 203,0.3)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(26, 117, 203,0)',
                  },
                ],
                false,
              ),
              shadowColor: 'rgba(0,179,244, 0.9)',
              shadowBlur: 20,
            },
            data: this.lineEchartList[1],
            // data: [502.84, 205.97, 332.79, 281.55, 398.35, 214.02, ]
          },
        ],
      };

      myChart.setOption(option, true);
    },
    resizeFn() {
      this.init();
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
