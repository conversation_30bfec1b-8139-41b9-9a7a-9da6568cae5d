<template>
  <div class="customtag-wrapper auto-fill">
    <search-module
      @clear="clear"
      @search="startSearch"
      @manualLink="manualLink"
      @analysisConfig="analysisConfig"
      @selectedOrgTree="selectedOrgTree"
    ></search-module>
    <div class="select-nav">
      <div class="data-list mr-lg">
        <span class="fl mt-md mr-sm">标签类型</span>
        <ui-select-tabs class="ui-select-tabs" :list="errorList" @selectInfo="selectInfo" ref="uiSelectTabs">
        </ui-select-tabs>
      </div>
      <Dropdown trigger="click" class="dropdown">
        <Button
          >删除标签
          <Icon type="ios-arrow-down"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem @click.native="importEquipment('batchDelete')">
            <i class="icon-font icon-piliangshanchu"></i>
            删除选中设备标签
          </DropdownItem>
          <DropdownItem @click.native="importEquipment('deleteForFile')">
            <i class="icon-font icon-daoruwentishebei"></i>
            删除导入设备标签
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Dropdown trigger="click" class="dropdown ml-sm">
        <Button>
          关联设备
          <Icon type="ios-arrow-down"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem @click.native="importEquipment('link')">
            <i class="icon-font icon-daoruwentishebei"></i>
            导入设备关联
          </DropdownItem>
          <DropdownItem @click.native="deviceDirectoryConnectHandle">
            <i class="icon-font icon-shebeimulu"></i>
            设备目录关联
          </DropdownItem>
          <DropdownItem @click.native="manualLink">
            <i class="icon-font icon-shoudongguanlian"></i>
            系统设备关联
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
      <Button type="primary" class="ml-sm" @click="analysisConfig">
        <i class="icon-font icon-zidongfenxipeizhi"></i>
        <span class="inilne vt-middle ml-sm">自动分析配置</span>
      </Button>
      <Button type="primary" class="ml-sm" @click="download">
        <i class="icon-font icon-xiazaimoban"></i>
        <span class="inilne vt-middle ml-sm">下载模板</span>
      </Button>
      <Button type="primary" class="ml-sm" @click="exportModule">
        <i class="icon-font icon-daochu"></i>
        <span class="inilne vt-middle ml-sm">导出</span>
      </Button>
    </div>
    <div class="device-message auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selectTable"
      >
        <template slot="deviceId" slot-scope="{ row }">
          <span class="font-active-color pointer device-id" @click="deviceArchives(row)">{{ row.deviceId }} </span>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltip-type">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template slot="action" slot-scope="{ row }">
          <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
          <!-- <Button type="text" @click.stop="addTags(row)">添加标签</Button> -->
        </template>
        <template slot="tagNames" slot-scope="{ row }">
          <tags-more :tag-list="row.tagList"></tags-more>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
    <!-- <slide-unit-tree :default-hide="false" @selectOrgCode="selectOrgCode"></slide-unit-tree> -->
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allTagList"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <customize-filter
      v-model="exportModuleFilter"
      :customize-action="exportCustomizeAction"
      :content-style="exportStyle"
      :field-name="exportFieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="exportdefaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <!-- <Tooltip content="只会导出选中字段的模板，不会导出设备数据" placement="top" max-width="350">
          <Button
            type="primary"
            class="plr-30"
            @click="exportExcel(tagList, 'module')"
            :loading="exportModuleLoading"
            >{{ exportModuleLoading ? '下载中' : '模板导出' }}</Button
          >
        </Tooltip> -->
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading"
            >{{ exportDataLoading ? '下载中' : '数据导出' }}
          </Button>
        </Tooltip>
      </template>
    </customize-filter>
    <analysis-config v-model="configVisible"></analysis-config>
    <associated-label v-model="associatedVisible" @update="search"></associated-label>
    <import-data
      ref="importData"
      v-model="importShow"
      :fetch-url="importFetchUrl"
      :title="importTitle"
      :upload-label="importLabel"
      :action="importAction"
      :delete-params="searchData"
      :delete-total="pageData.totalCount"
      :checked-device-data="checkedData"
      @update="search"
      @importData="importFailData"
    ></import-data>
    <import-fail v-model="importFailShow" :table-data="failData"></import-fail>
    <!--设备目录管理标签-->
    <device-directory-connect
      ref="deviceDirectoryConnect"
      v-if="deviceDirectoryConnectVisible"
      @refreshList="startSearch"
    />
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';
export default {
  mixins: [downLoadTips],
  name: 'labelanalysis',
  data() {
    return {
      deviceDirectoryConnectVisible: false,
      importFailShow: false,
      failData: [],
      loading: false,
      checkboxVisible: false,
      customSearch: false,
      errorList: [],
      searchData: {
        deviceId: '',
        deviceName: '',
        ipAddr: '',
        longitude: '',
        latitude: '',
        beginTime: '',
        endTime: '',
        orgCode: '',
        needTag: true, //需要返回标签信息
        tagIds: [],
        pageNumber: 1,
        pageSize: 20,
      },
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          title: '序号',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
        },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
          align: 'left',
          fixed: 'left',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
        },
        {
          minWidth: 200,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
        },
        {
          width: 110,
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
        },
        {
          width: 110,
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        // { width: 200, title: '数据来源', key: 'sourceName', align: 'left', tooltip: true, },
        { width: 160, title: '安装时间', key: 'azsj', align: 'left' },
        {
          minWidth: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 250,
          title: '安装地址',
          key: 'address',
          align: 'left',
          tooltip: true,
        },
        {
          width: 60,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      choosedOrg: {},
      checkedData: [],
      exportModuleData: [],
      questionList: [],
      allTagList: [],
      defaultCheckedList: [],
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },

      contentStyle: {
        height: `${500 / 192}rem`,
      },

      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },

      chooseOne: {
        tagList: [],
      },
      configVisible: false,
      associatedVisible: false,
      exportModuleFilter: false,
      allPropertyList: [],
      exportCustomizeAction: {
        title: '选择导出设备',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '80%',
        },
      },
      exportStyle: {
        height: `${500 / 192}rem`,
      },
      exportFieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      exportdefaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
      exportModuleLoading: false,
      exportDataLoading: false,
      importShow: false,
      importFetchUrl: '',
      importTitle: '',
      importLabel: '',
      importAction: '',
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
    }),
  },
  async created() {
    if (this.propertySearchLbdwlx.length === 0) await this.getAlldicData();
  },
  activated() {
    this.getTagList();
    this.getPropertyList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    importFailData(data) {
      if (data.length !== 0) {
        this.failData = data;
        this.importFailShow = true;
      }
    },
    // 模板下载
    async download() {
      try {
        const res = await this.$http.get(taganalysis.downloadTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      }
    },
    // 导入设备
    importEquipment(type) {
      if (type === 'link') {
        this.importFetchUrl = taganalysis.deviceLinkedList;
        this.importTitle = '导入设备关联标签';
        this.importLabel = '上传待关联设备';
      } else if (type === 'deleteForFile') {
        this.importFetchUrl = taganalysis.removeDeviceLinkedForFile;
        this.importTitle = '删除导入设备标签';
        this.importLabel = '上传待删除标签设备';
      } else {
        this.importFetchUrl = taganalysis.batchRemoveDeviceLinked;
        this.importTitle = '删除选中设备标签';
        this.importLabel = '待删除标签的设备';
      }
      this.importAction = type;
      this.importShow = true;
    },
    // 设备目录关联标签
    deviceDirectoryConnectHandle() {
      this.deviceDirectoryConnectVisible = true;
      this.$nextTick(() => {
        this.$refs.deviceDirectoryConnect.init();
      });
    },
    async exportExcel(propertyList, type) {
      try {
        if (type === 'module') {
          this.exportModuleLoading = true;
        } else {
          this.$_openDownloadTip();
          this.exportDataLoading = true;
        }
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          isTemplate: type === 'module',
          ids: this.exportModuleData.map((item) => item.id),
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(taganalysis.getExportDevice, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportModuleFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportModuleLoading = false;
        this.exportDataLoading = false;
      }
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        this.exportdefaultCheckedList = [
          'deviceId',
          'id',
          'deviceName',
          'sbgnlx',
          'sbdwlx',
          'ipAddr',
          'macAddr',
          'longitude',
          'latitude',
          'sbcjqy',
          'phyStatus',
        ];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId' || item.propertyName === 'id';
        };
        this.rightDisabled = (item) => {
          return !(item.propertyName === 'deviceId' || item.propertyName === 'id');
        };
      } catch (err) {
        console.log(err);
      }
    },
    exportModule() {
      this.exportModuleFilter = true;
    },
    manualLink() {
      this.associatedVisible = true;
    },
    analysisConfig() {
      this.configVisible = true;
    },
    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
      this.search();
    },
    search() {
      this.pageData.pageNum = 1;
      this.searchData.pageNumber = 1;
      this.init();
    },
    startSearch(searchData) {
      Object.assign(this.searchData, searchData);
      this.search();
    },
    selectTable(selection) {
      this.checkedData = selection.map((row) => {
        return row.id;
      });
      this.exportModuleData = selection;
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      if (!row.tagList || !row.tagList.length) {
        this.defaultCheckedList = [];
        return;
      }
      this.defaultCheckedList = row.tagList.map((item) => {
        return item.tagId;
      });
    },
    // 查询设备数据列表
    async init() {
      this.loading = true;
      this.tableData = [];
      this.checkedData = [];
      this.copySearchDataMx(this.searchData);
      let params = Object.assign({}, this.searchData);
      this.searchData.sourceIds ? (params.sourceIds = this.searchData.sourceIds) : (params.sourceIds = []);
      try {
        let res = await this.$http.post(equipmentassets.getTagAnalysisPageDeviceList, params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.allTagList = res.data.data;
        // this.allPropertyList = res.data.data
        this.errorList = this.$util.common.deepCopy(
          this.allTagList.map((row) => {
            return {
              name: row.tagName,
              id: row.tagId,
            };
          }),
        );
      } catch (err) {
        console.log(err);
      }
    },
    async getDataSource() {
      try {
        let res = await this.$http.get(equipmentassets.getDataSourceStatistic, {
          params: { orgCode: this.choosedOrg.orgCode },
        });
        this.questionList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    clear() {
      this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.search);
    },
    selectedOrgTree(data) {
      this.choosedOrg = data;
    },
    // selectOrgCode(data) {
    //   this.searchData.orgCode = data.orgCode
    //   this.choosedOrg = data
    //   // this.getDataSource();
    //   this.search()
    // },
    confirmFilter(val, isFirst) {
      !isFirst ? (this.customSearch = false) : null;
      if (this.customizeAction.title === '添加设备标签') {
        const tagIds = val.map((item) => {
          return item.tagId;
        });
        const params = {
          id: this.chooseOne.id,
          tagIds: tagIds,
        };
        this.addTagsFunc(params);
      } else {
        this.filterTagList = val;
      }
    },
    async addTagsFunc(params) {
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.init();
      } catch (err) {
        console.log(err);
      }
    },
  },
  components: {
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    SearchModule: require('./search-module.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    AnalysisConfig: require('./analysis-config.vue').default,
    AssociatedLabel: require('./associated-label.vue').default,
    importData: require('./import-data.vue').default,
    deviceDirectoryConnect: require('./device-directory-connect').default,
    ImportFail: require('@/views/viewassets/equipmentassets/labelanalysis/import-fail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.customtag-wrapper {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .data-list {
    flex: 1;
    color: var(--color-label);
    font-size: 14px;
    .ui-select-tabs {
      margin-left: 70px;
    }
  }
  .ui-table {
    padding: 0 20px;
  }
  .device-message {
    position: relative;
  }
  .device-tags {
    position: absolute;
    bottom: 99px;
    right: 20px;
  }
}
.select-nav {
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding: 0 20px;
  // .btn-div {
  //   display: flex;
  //   justify-content: flex-end;
  // }
}
@{_deep} .dropdown {
  .ivu-dropdown-item {
    color: var(--color-select-item);
    display: flex;
    align-items: center;
    .icon-font {
      margin-right: 5px;
    }
  }
  .ivu-select-dropdown {
    background-color: var(--bg-select-dropdown);
  }
  .ivu-dropdown-item:hover {
    background: var(--bg-select-item-active);
    color: var(--color-select-item-active);
  }
}
</style>
