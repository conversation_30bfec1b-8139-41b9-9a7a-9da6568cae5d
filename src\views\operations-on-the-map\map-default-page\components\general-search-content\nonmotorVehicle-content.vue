<!--
    * @FileDescription: 非机动车
    * @Author: H
    * @Date: 2023/5/12
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-14 10:14:06
-->
<template>
  <div class="content-warpper">
    <div class="warpper-box">
      <div v-for="(item, index) in pageList" :key="index" class="warpper-ul">
        <div
          class="box-item"
          :class="{ active: currentClickIndex == index }"
          @click="chooseMapItem($event, index)"
        >
          <div class="header">
            <div class="header-left">
              <span
                class="serialNumber"
                :class="{ activeNumber: currentClickIndex == index }"
              >
                <span>{{ index + 1 }}</span>
              </span>
            </div>
            <div class="header-name" v-show-tips>
              {{ item.recordId || "--" }}
            </div>
            <operate-bar
              :list="item"
              :tabType="{ type: 17 }"
              @collection="collection($event, item, index)"
            ></operate-bar>
          </div>
          <div class="content">
            <div class="content-left">
              <img v-lazy="item.traitImg" alt="" />
            </div>
            <div class="content-right">
              <div class="ellipsis">
                <ui-icon type="time" :size="14"></ui-icon>
                <span>{{ item.absTime || "--" }}</span>
              </div>
              <span class="ellipsis">
                <ui-icon type="location" :size="14"></ui-icon>
                <span>{{ item.deviceName || "--" }}</span>
              </span>
              <span v-if="item.similarity">
                <ui-icon type="wenjianxiangsidupeizhi" :size="14"></ui-icon>
                <span>相似度：</span>
                <span class="score">{{ `${item.similarity}%` }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="!loading && !pageList.length" />
    <div class="general-search-footer">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="total"
        countTotal
        :current="pageInfo.pageNumber"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        size="small"
        show-total
      >
      </ui-page>
    </div>
  </div>
</template>

<script>
import operateBar from "@/components/mapdom/operate-bar.vue";
import { queryNonmotorRecordSearch } from "@/api/wisdom-cloud-search";
export default {
  components: {
    operateBar,
  },
  props: {
    //搜索条件
    searchPrams: {
      type: Object,
      default: () => {},
    },
    // 当前点击顺序
    currentClickIndex: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      pageList: [],
      loading: false,
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
    };
  },
  watch: {
    currentClickIndex: {
      handler(newVal) {
        if (newVal > -1) {
          let list = document.querySelectorAll(".box-item");
          list[newVal].scrollIntoView(false);
        }
      },
    },
  },
  computed: {},
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.queryNonmotorSearch();
    },
    chooseMapItem($event, index) {
      $event.stopPropagation();
      this.$emit("chooseMapItem", index);
    },
    queryNonmotorSearch() {
      const { pageInfo, searchPrams } = this;
      const params = { ...pageInfo, ...searchPrams };
      params.similarity = params.similarity / 100;
      if (!searchPrams.keyWords) {
        params.keyWords = "";
      }
      this.loading = true;
      queryNonmotorRecordSearch(params)
        .then((res) => {
          if (res.code === 200) {
            const {
              data: { entities = [], total = 0 },
            } = res;
            this.pageList = entities || [];
            this.total = total;
            this.$emit("mapResultHandler", this.pageList, 1);
          }
        })
        .catch(() => {
          this.pageList = [];
          this.$emit("mapResultHandler", []);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    collection($event, item, index) {
      this.$set(this.pageList[index], "myFavorite", $event);
    },
    // 分页
    pageChange(page) {
      this.pageInfo.pageNumber = page;
      this.queryNonmotorSearch();
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
</style>
