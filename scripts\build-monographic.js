#! /usr/bin/env node

// 使用node 拷贝dist文件夹下的index.html 到monographic文件夹下
const fs = require("fs");
const path = require("path");
const { baseList } = require("./base.js");

// 创建文件夹
const createDir = (targetPath) => {
  if (!fs.existsSync(targetPath)) {
    fs.mkdirSync(targetPath);
  }
  return targetPath;
};
// 读取文件
const readFile = (path) => {
  return fs.readFileSync(path, "utf8");
};
// 写入文件
const writeFile = (path, content) => {
  fs.writeFileSync(path, content, "utf8");
};

const creatFileSync = (
  sourcePath,
  targetPath,
  data
) => {
  const  { BASE_URL } = data;
  const htmlContent = readFile(path.resolve(sourcePath, "index.html"));
  // const configContent = readFile(path.resolve(sourcePath, "config.js"));
  createDir(targetPath);
  // writeFile(
  //   path.resolve(targetPath, "index.html"),
  //   htmlContent.replace(`src=/config.js`, `src=/${BASE_URL}/config.js`)
  // );
  // 往html 中插入script标签
  writeFile(
    path.resolve(targetPath, "index.html"),
    htmlContent.replace(
      `<script src=/config.js></script>`,
      `<script src=/config.js></script><script src=${BASE_URL}/config.js></script>`
    )
  );
  // 使用正则表达式动态替换配置内容
  // const configContentModified = configContent
  //   .replace(/BASE_URL\s*=\s*"[^"]*"/, `BASE_URL = "/${BASE_URL}"`)
  //   .replace(/applicationCode\s*=\s*"[^"]*"/, `applicationCode = "${applicationCode}"`);
  const configContentModified = Object.keys(data).map(key => `${key} = "${data[key]}"`).join('\n');
  
  writeFile(path.resolve(targetPath, "config.js"), configContentModified);
  console.log(`==========创建应用${BASE_URL}成功===============`);
};
const run = () => {
  const sourcePath = path.resolve(__dirname, "../dist");
  for (let i = 0; i < baseList.length; i++) {
    const { BASE_URL } = baseList[i];
    const targetPath = path.resolve(sourcePath, `${BASE_URL.replace("/", "")}`);
    creatFileSync(sourcePath, targetPath, { ...baseList[i] });
  }
};

run();
