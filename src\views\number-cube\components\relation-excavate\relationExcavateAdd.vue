<template>
  <ui-modal
    width="1200"
    v-model="visible"
    title="关系挖掘"
    class="relation-excavate-add"
    footer-hide
    :closable="false"
    :cancelBtn="false"
    @onCancel="close"
    :style="{ display: modelDisplay }"
  >
    <template slot="header">
      <Icon
        type="md-close"
        :style="{ float: 'right', cursor: 'pointer' }"
        @click="close"
      />
    </template>
    <stepsLine :current="current" />
    <div class="content">
      <firstStep
        v-show="current == 0"
        :sourceNode="sourceNode"
        :targetNodeData="targetNodeData"
        @getFirstData="getFirstData"
        :visible="visible"
      />
      <secondStep
        v-show="current == 1"
        :firstStepData="firstStepData"
        :targetNodeData="targetNodeData"
        @getSecondData="getSecondData"
      />
      <thirdStep
        ref="thirdRef"
        v-show="current == 2"
        :firstStepData="firstStepData"
        :targetNodeData="targetNodeData"
        :secondStepData="secondStepData"
        :relationSelectListZhijie="relationSelectListZhijie"
        :relationSelectListJianjie="relationSelectListJianjie"
        @getThirdData="getThirdData"
      />
      <fourStep
        v-show="current == 3"
        :firstStepData="firstStepData"
        :thirdStepData="thirdStepData"
        @getFourData="getFourData"
      />
    </div>
    <div class="footer">
      <Button @click="close">取消</Button>
      <Button type="primary" v-if="current > 0" @click="prev">上一步</Button>
      <Button type="primary" v-if="current < 3" @click="next">下一步</Button>
      <Button
        type="primary"
        v-if="current == 3"
        @click="save"
        :disabled="saveDisabled"
        >执行</Button
      >
    </div>
    <ui-loading v-if="excavateLoading" />
  </ui-modal>
</template>
<script>
import stepsLine from "./steps-line.vue";
import firstStep from "./first-step.vue";
import secondStep from "./second-step.vue";
import thirdStep from "./third-step.vue";
import fourStep from "./four-step.vue";
import log from "@/libs/configuration/util.log";
import { mapGetters } from "vuex";

import { saveMiningModel, exec } from "@/api/number-cube.js";
export default {
  components: { stepsLine, firstStep, secondStep, thirdStep, fourStep },
  props: {
    isExcavate: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      current: 0,
      sourceNode: {},
      targetNodeData: {},
      firstStepData: {},
      secondStepData: {},
      relationSelectListZhijie: [],
      relationSelectListJianjie: [],
      thirdStepData: {},
      secondIsNext: false,
      fourGraphData: {},
      fourConfigData: {},
      excavateLoading: false,
      saveDisabled: false,
      modelDisplay: "none",
      relationNameExists: 1,
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  mounted() {},
  methods: {
    clearModelData() {
      this.visible = false;
    },
    init(node, targetNodeData, excavating) {
      this.modelDisplay = "block";
      this.saveDisabled = false;

      if (excavating) {
        this.current = 3;
        this.visible = true;
      } else {
        this.sourceNode = { ...node };
        this.targetNodeData = targetNodeData;
        this.current = 0;
        this.visible = true;
      }
    },
    //下一步
    next() {
      if (this.current === 0) {
        if (this.firstStepData.nodes[1].id !== "0") {
          this.current += 1;
        } else {
          this.$Message.warning("请选择目标实体类型！");
        }
      } else if (this.current === 1) {
        if (this.secondIsNext) {
          this.current += 1;
        } else {
          this.$Message.warning("请选择基础关系！");
        }
      } else {
        this.$refs.thirdRef.next();
      }
    },
    //上一步
    prev() {
      this.saveDisabled = false;
      this.current -= 1;
    },
    //执行
    save() {
      //   console.log(this.fourGraphData, "this.fourGraphData");
      //   console.log(this.fourConfigData, "this.fourConfigData");
      if (this.fourGraphData.edges && this.fourGraphData.edges[0].label) {
        let params = {
          graphInstanceId:
            this.firstStepData.nodes[1].targetNodeInfo.graphInstanceId,
          lineStyle: JSON.stringify(this.fourGraphData.edges[0].style),
          ownerIdentifier: this.userInfo.username,
          relationColor: this.fourGraphData.edges[0].style.stroke,
          relationNameCn: this.fourGraphData.edges[0].label,
          rules: [
            {
              startEventTime: this.fourConfigData.startTime,
              endEventTime: this.fourConfigData.endTime,
              andRelationRules: [
                //交集
                {
                  startWeek: 0,
                  endWeek: 0,
                  periodType: 1,
                  timePeriod: 1,

                  times: 0,
                  statisticsRuleOps: 0,

                  relationName: "string", //关系英文label
                  targetEntityName: "string",
                  targetEntityProperties: [
                    {
                      propertyName: "string",
                      propertyOps: 5, // =    （场所类型 标签 11 集合操作：包含任意一个元素 || 12 集合操作：包含全部元素 |）
                      values: [{}],
                    },
                  ],
                },
              ],
              orRelationRules: [
                //并集
                {
                  endWeek: 0,
                  periodType: 0,
                  relationName: "string",
                  startWeek: 0,
                  statisticsRuleOps: 0,
                  targetEntityName: "string",
                  targetEntityProperties: [
                    {
                      propertyName: "string",
                      propertyOps: 0,
                      values: [{}],
                    },
                  ],
                  timePeriod: 1,
                  times: 0,
                },
              ],
            },
          ],
          sourceEntityName: this.fourGraphData.nodes[0].ext.label,
          status: 0,
          targetEntityName: this.fourGraphData.nodes[1].nodeType,
        };
        let rules = [];
        let timePeriod = "";
        let timePeriodCom = "";
        for (let key in this.fourConfigData.dataForms) {
          timePeriod = this.fourConfigData.dataForms[
            key
          ].ruleList[0].labelValue.slice(1, 2);
          timePeriodCom = this.fourConfigData.dataForms[
            key
          ].ruleList[0].labelValue.slice(0, 1);
          rules.push({
            startWeek: 0,
            endWeek: 0,
            periodType: 1,
            timePeriod: Number(timePeriod),
            timePeriodComplianceFrequency: Number(timePeriodCom),
            times: this.fourConfigData.dataForms[key].ruleList[2].labelValue,
            statisticsRuleOps:
              this.fourConfigData.dataForms[key].ruleList[1].labelValue,
            relationName: this.fourConfigData.dataForms[key].ruleNameEn,
            targetEntityName: this.fourConfigData.dataForms[key].archiveName,
            targetEntityProperties: this.targetEntityProperties(
              this.fourConfigData.dataForms[key].ruleList
            ),
          });
        }
        if (this.fourConfigData.isIntersection == "1") {
          params.rules[0].orRelationRules = [];
          params.rules[0].andRelationRules = [...rules];
        } else {
          params.rules[0].andRelationRules = [];
          params.rules[0].orRelationRules = [...rules];
        }
        // console.log(params, "params");
        let execParams = {
          relationMiningModelId: "",
          sourceEntityId: this.fourGraphData.nodes[0].id,
        };

        saveMiningModel(params).then((res) => {
          if (res.data) {
            execParams.relationMiningModelId = res.data;
            this.excavateLoading = true;
            this.saveDisabled = true;
            exec(execParams)
              .then((e) => {
                if (e.code == 200) {
                  this.excavateLoading = false;
                  if (e.data.vertexExes.length > 0) {
                    this.modelDisplay = "none";
                    this.saveDisabled = false;
                    this.$emit(
                      "getRelationExcavateData",
                      e.data,
                      this.fourGraphData,
                      res.data
                    );
                  } else {
                    this.modelDisplay = "block";
                    this.saveDisabled = true;
                    this.$Message.info("该对象暂未挖掘到数据！");
                  }
                }
              })
              .finally(() => {
                this.excavateLoading = false;
              });
          }
        });
      } else {
        if (this.relationNameExists == 1) {
          this.$Message.warning("请输入关系名称！");
        } else {
          this.$Message.warning("关系名称已经存在！");
        }
      }
    },
    targetEntityProperties(data) {
      let targetEntityProperties = [];
      if (data.length > 3) {
        data.slice(3, data.length).forEach((item) => {
          if (item.labelValue) {
            if (Array.isArray(item.labelValue)) {
              let arr = [];
              item.labelValue.forEach((it) => {
                arr.push(it.toString());
              });
              targetEntityProperties.push({
                propertyName: item.labelKey,
                propertyOps:
                  item.isIntersection && item.multiple
                    ? Number(item.isIntersection)
                    : 5,
                values: arr,
              });
            } else {
              targetEntityProperties.push({
                propertyName: item.labelKey,
                propertyOps:
                  item.isIntersection && item.multiple
                    ? Number(item.isIntersection)
                    : 5,
                values: [item.labelValue],
              });
            }
          }
        });
      }
      return targetEntityProperties;
    },
    //关闭
    close() {
      if (this.isExcavate) {
        this.esc();
      } else {
        this.current = 0;
        this.visible = false;
      }
    },
    //
    esc() {
      this.modelDisplay = "none";
    },
    //获取第一步数据
    getFirstData(data) {
      this.firstStepData = { ...data };
    },
    //获取第二步数据
    getSecondData(data, selectData1, selectData2) {
      this.secondStepData = { ...data };
      this.secondIsNext =
        selectData1.length > 0 || selectData2.length > 0 ? true : false;
      this.relationSelectListZhijie = [...selectData1];
      this.relationSelectListJianjie = [...selectData2];
    },
    //获取第三步数据
    getThirdData(data) {
      this.thirdStepData = { ...data };
      this.current += 1;
    },
    //获取第四步数据
    getFourData(graphData, fourConfigData, relationNameExists) {
      this.fourGraphData = { ...graphData };
      this.fourConfigData = { ...fourConfigData };
      this.relationNameExists = relationNameExists;
    },
  },
};
</script>
<style lang="less" scoped>
.relation-excavate-add {
  /deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
    height: 800px;
    padding: 0;
  }
  .content {
    height: calc(~"100% - 170px");
    padding: 0 60px 30px 60px;
  }
  .footer {
    text-align: center;
    height: 60px;
    line-height: 60px;
    border-top: 1px solid #d3d7de;
    button {
      margin-right: 10px;
    }
  }
}
</style>
