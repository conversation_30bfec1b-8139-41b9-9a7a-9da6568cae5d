<template>
  <div class="H5Player">
    <div ref="videoDiv" class="video-div"></div>
    <!-- 云台 -->
    <ptz-control
      v-model="isShowPTZ"
      :videoObj="videoObj"
      :winList="winList"
      @setPtzControl="setPtzControl"
    ></ptz-control>
    <!-- 帧标记 -->
    <add-framemark-modal
      :options="frameMarkOption"
      @added-frameMark="handleSubmitFrameMark"
    ></add-framemark-modal>
    <!-- 截图、连拍 -->
    <print-screen-modal :options="printScreenOption"></print-screen-modal>
    <!-- 历史调阅 -->
    <video-search-modal
      ref="videoSearchRef"
      :videoObj="videoObj"
      :options="videoSearchOption"
      @playvod="playvod"
      @downloadVod="downloadVod"
    ></video-search-modal>
    <!-- 预置位 -->
    <video-present-position
      :options="presentPositionOption"
    ></video-present-position>
  </div>
</template>
<script>
import Vue from "vue";
import {
  fetchPTZControl,
  addPlayback,
  queryCameraDeviceList,
  controlVod,
  getChannel,
  getCipherChannel,
  getCipherChannelNew,
  getLockInfo,
  queryDeviceOrgTree,
} from "@/api/player";
import { fetchVod, startVod, fetchLive, stopLive, stopVod } from "@/api/player";
import controbarScenes from "./tool-bar/commonScence";
import ptzControl from "./components/ptz-control.vue";
import addFramemarkModal from "./components/add-framemark-modal.vue";
import printScreenModal from "./components/print-screen-modal.vue";
import videoSearchModal from "./components/video-search-modal.vue";
import videoFilterTool from "./components/video-filter-tool.vue";
import videoPresentPosition from "./components/video-present-position.vue";
import { mapGetters } from "vuex";
import { frameAdd } from "@/api/frame.js";

export default {
  name: "H5Player",
  components: {
    ptzControl,
    addFramemarkModal,
    printScreenModal,
    videoSearchModal,
    videoPresentPosition,
  },
  props: {
    options: {
      type: Object,
      default: () => {},
    },
    // 时间轴
    isShowTimershaft: {
      type: Boolean,
      default: false,
    },
    // 工具栏
    isShowToolbar: {
      type: Boolean,
      default: true,
    },
    // 工具栏一直显示
    showToolbarAllways: {
      type: Boolean,
      default: false,
    },
    // 是否需要记录历史
    addHistory: {
      type: Boolean,
      default: false,
    },
    deviceObj: {
      type: Object,
      default: () => {
        return {
          // "playType": "vod",
          // "deviceId": "",
          // "deviceGbId": "",
          // "deviceName": "",
          // "devicetype": "rtsp"
        };
      },
    },
    // 帧标记
    iframes: {
      type: Array,
      default: () => [],
    },
    sceneFrom: {
      type: String,
      default: "monitorVideo",
    },
    // 支持双击全屏
    dbFullScreen: {
      type: Boolean,
      default: false,
    },
    // 通道未查询时 是否展示报错信息
    noVideoMsg: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      orgInfo: {},
      videoObj: null,
      isAutoNext: false, // 是否自动切换下一个窗口
      printScreenOption: {
        title: "截图",
        open: false,
        imgUrls: [],
      },
      videoSearchOption: {
        title: "历史调阅",
        open: false,
        deviceId: "",
        deviceName: "",
      },
      frameMarkOption: {
        open: false,
        src: "",
        name: "",
        absTime: null,
        color: "red",
      },
      frameMarkAddOption: {
        width: 320,
        height: 180,
        list: [
          // {
          //   absTime: 1686612247000,
          //   color: 'red',
          //   class: '',
          //   template: '<img src="http://image.cn.made-in-china.com/prod/698-3437625.jpg"/>',
          //   hoverFn: options => {
          //     console.log(options)
          //   }
          // }
        ],
      },
      isShowPTZ: false,
      winList: [],
      lastPtzParamStr: "",
      presentPositionOption: {
        title: "预置位设置",
        open: false,
        index: 0,
        H5Player: null,
        cameraInfo: {},
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      individuation: "individuation",
      ignoreDevices: "player/getIgnoreDevices",
    }),
  },
  watch: {
    isShowTimershaft: {
      handler(val) {
        this.changeRcdtimershaft(val);
      },
      immediate: true,
    },
    deviceObj: {
      handler(val) {
        // 自动播放
        if (val["deviceId"] || val["deviceGbId"]) {
          if (this.videoObj) {
            this.playStream(this.deviceObj, this.deviceObj.playType, 0);
          }
        } else {
          this.stopStream(0);
        }
      },
    },
    winList: {
      handler(val) {
        this.$emit("changeWinList", val);
      },
      immediate: true,
    },
    "individuation.videoRatio": {
      handler(val) {
        if (this.videoObj) {
          this.videoObj.setPlayerRatio(val);
          let count = this.videoObj.layoutCount;
          let i = 0;
          if (val) {
            while (i < count) {
              this.videoObj.setRatio(i, val);
              i++;
            }
          }
        }
      },
    },
  },
  mounted() {
    // 初始化播放器
    this.$nextTick(() => {
      this.initPlayer().then(() => {
        this.initWin();
        this.handleEvent(this.attachEventEx);
        this.attachDropEvent();
        if (
          this.deviceObj &&
          (this.deviceObj["deviceId"] || this.deviceObj["deviceGbId"])
        ) {
          this.playStream(this.deviceObj, this.deviceObj.playType, 0);
        }
        let localSpeed = localStorage.getItem("ptzSpeed");
        if (localSpeed)
          this.videoObj.windowPtzSpeed = Math.ceil((localSpeed / 255) * 15);
        this.$emit("inited");
      });
    });
  },
  beforeDestroy() {
    if (this.videoObj) {
      for (let i = 0; i < this.winList.length; i++) {
        this.stopStream(i);
      }
      this.handleEvent(this.detachEventEx);
      this.detachDropEvent();
      this.videoObj.destroy();
      this.videoObj = null;
    }
  },
  activated() {
    if (this.videoObj) {
      let count = this.videoObj.layoutCount;
      let i = 0;
      while (i < count) {
        this.videoObj.pause(i, false);
        i++;
      }
    }
  },
  methods: {
    initPlayer() {
      return new Promise((resolve) => {
        if (this.videoObj) {
          this.videoObj.destroy();
          this.videoObj = null;
        }
        let defaultOptions = {
          ele: this.$refs.videoDiv,
          layout: "2*2",
          ratio: this.individuation.videoRatio || "origin",
          border: {
            borderWidth: "4px",
            borderColor: "#2c3033",
            focusBorderColor: "#2C86F8",
          },
          layoutmargin: {
            marginX: 0,
            marginY: 0,
          },
          showerrorColor: "#a5b0b6",
          rcdtimershaft: {
            backcolor: "rgba(72, 72, 71, .9)",
            novodcolor: "rgba(255, 255, 255, .2)",
            thumbpos: "50%",
            thumbcolor: "#FDEE38",
          },
          selectShape: {
            lineColor: "rgba(255, 234, 75, 1)",
            fillColor: "rgba(255,234,75,1)",
          },
          playHisVideoHotKeys: {
            forward: 39,
            backward: 37,
            pause: 32,
          },
          // defBgClass: "h5vp-bg-img0",
          loadingTimeout: -1,
          reconnectCount: 3,
          enablercdtimershaft: false,
          rcdtimershaftshowtype: 3,
          playConfig: {
            enableWorkerFmp4: true,
            autoSpeedMaxDurationForLive: 1,
            autoSpeedMinDurationForLive: 1,
            // autoSeekDurationForLive: 4
          },
        };
        this.videoObj = new H5Player({ ...defaultOptions, ...this.options });
        resolve();
      });
    },
    initWin() {
      let winCount = this.videoObj.layoutCount;
      if (this.winList.length < winCount) {
        for (let i = this.winList.length; i < winCount; i++) {
          let row = {};
          this.winList.push(row);
        }
      } else {
        this.winList = this.winList.slice(0, winCount);
      }
    },
    cleanWin(index) {
      this.isShowPTZ = false;
      this.printScreenOption.open = false;
      this.videoObj.removeToolbar(index);
      this.videoObj.SetWindowPtz(index, false);
      this.videoObj.stopDrawing(index, true);
      // 停止流
      if (playerType == "pvg") {
        if (this.winList[index]["stream"])
          this.videoObj.stopPreOpenRealStream(this.winList[index]["stream"]);
      } else {
        if (
          this.winList[index]["stream"] &&
          this.winList[index]["stream"].streamtype == "live" &&
          this.winList[index]["stream"].streamid
        ) {
          stopLive({ streamid: this.winList[index]["stream"].streamid });
        } else if (
          this.winList[index]["stream"] &&
          this.winList[index]["stream"].streamtype == "vod" &&
          this.winList[index]["stream"].streamid
        ) {
          stopVod({ streamid: this.winList[index]["stream"].streamid });
        }
      }
      this.$set(this.winList, index, {});
    },
    getPvgStream(obj, isVod) {
      return new Promise((resolve) => {
        let stream = {};
        let requestUrl = desencode == 1 ? getCipherChannel : getChannel;
        requestUrl(obj.deviceGbId ? obj.deviceGbId : obj.deviceId).then(
          (res) => {
            if (res.code != 200 || !res.data) {
              if (!this.noVideoMsg) {
                this.$Message.error("未查询到通道信息");
              }
              resolve("");
            }
            let { channelNo, ip, port, userName, password } = res.data;
            stream.channel = channelNo;
            stream.desencode = desencode;
            stream.ip = ip;
            stream.port = Number(port);
            stream.user = userName;
            stream.password = password;
            if (isVod) {
              stream.begintime = this.$dayjs(obj.begintime).format(
                "YYYY-MM-DD HH:mm:ss:SSS"
              );
              stream.endtime = this.$dayjs(obj.endtime).format(
                "YYYY-MM-DD HH:mm:ss:SSS"
              );
            }
            resolve(stream);
          }
        );
      });
    },
    async objToStream(obj, streamtype, streamObj) {
      let { devicetype, deviceId, deviceGbId, deviceName, videoUrl, ptzType } =
        { ...obj };
      let gbId = deviceGbId ? deviceGbId : deviceId;
      let stream = { title: deviceName, streamtype, devicetype, deviceId };
      if (videoUrl) {
        // 历史
        stream = JSON.parse(videoUrl);
      } else if (streamObj) {
        // 从录像检索播放
        stream = { ...stream, ...streamObj };
      } else if (streamtype == "live") {
        // 实时
        stream.streamtype = "live";
        stream.devicetype = obj.devicetype || liveType;
        let result, id;
        switch (stream.devicetype) {
          case "flv":
            result = await fetchLive({
              channel: gbId,
              device: lwptDevice ? lwptDevice : gbId,
              substream: false,
            });
            stream.url = result["WebSocket"];
            id = result["WebSocket"].split("?id=")[1];
            stream.streamid = `live/${gbId}/${id}`;
            break;
          case "rtsp":
            result = await fetchLive({
              channel: gbId,
              device: lwptDevice ? lwptDevice : gbId,
              substream: false,
            });
            stream.url = result["RTSP"];
            id = result["RTSP"].split("?id=")[1];
            stream.streamid = `live/${gbId}/${id}`;
            break;
          case "pvg67":
          case "pvgplus":
            let pvgStream = await this.getPvgStream(obj);
            if (!pvgStream) return;
            stream = { ...stream, ...pvgStream };
            stream.isptz = ptzType == "1" || ptzType == "2" ? true : false;
            break;
        }
      } else {
        // 录像
        stream.streamtype = "vod";
        stream.vod = vodStorage == "device" ? 1 : 0;
        stream.devicetype = vodType;
        switch (stream.devicetype) {
          case "pvg67":
          case "pvgplus":
            let vodStream = await this.getPvgStream(obj, true);
            if (!vodStream) return;
            stream = { ...stream, ...vodStream };
            break;
          default:
            let res = await startVod({
              channel: deviceGbId ? deviceGbId : deviceId,
              starttime: obj.begintime,
              endtime: obj.endtime,
              hls: true,
              storage: vodStorage,
            });
            stream.url = res[vodType.toUpperCase()];
            stream.duration =
              new Date(obj.endtime).getTime() -
              new Date(obj.begintime).getTime();
            stream.begintime =
              new Date(obj.begintime).format("yyyyMMddhhmmss") + "000";
            stream.endtime =
              new Date(obj.endtime).format("yyyyMMddhhmmss") + "000";
            stream.streamid = res.streamid;
            break;
        }
      }
      return stream;
    },
    // 流播放
    async playStream(obj, streamtype, winIndex, streamObj) {
      let _this = this;
      if (this.videoObj) {
        let index =
          !!winIndex || winIndex == 0 ? winIndex : this.getAutoNextIndex();
        this.stopStream(index);
        let callback = {
          onerror: function onerror(type, detail, info) {
            console.error("onerror:", type, detail, info);
          },
          onnotify: function onnotify(type, info) {
            console.log(type);
            _this.$emit("sucess", type, info);
            if (type == "custom-control") {
              // 历史流控制
              let item = _this.winList[info.index];
              let params = {
                streamid: item.stream.streamid,
                command: info.command,
              };
              info.range && (params.range = info.range); // seek
              info.scale && (params.scale = info.scale); // scale
              controlVod(params);
            } else if (type == "custom-ptzControl") {
              // 云台控制
              _this.setPtzControl(info.command, info.speed, info.index);
            }
          },
        };
        let stream = await this.objToStream(obj, streamtype, streamObj);
        // 云台锁定状态
        let lockInfo = await getLockInfo(obj.deviceId);
        const lockStatus = lockInfo.data ? !!lockInfo.data.lockStatus : false;
        const isPtzLocked = lockStatus
          ? Number(this.userInfo.shortNumber) < lockInfo.data.userLevel
            ? true
            : false
          : false;
        // 权限控制
        let streamParam = {
          ...stream,
          isptz: stream.isptz && this.$_has(["video-control"]) && !isPtzLocked,
          ptzLockStatus: lockStatus,
        };
        // if (streamParam.streamtype == 'live' && !this.$_has(['video-realTime']) ) {
        // 	this.$Message.error("无实时视频查看权限！")
        // 	return
        // }
        if (streamParam.streamtype == "vod" && !this.$_has(["video-history"])) {
          this.$Message.error("无历史视频查看权限！");
          return;
        }
        if (this.ignoreDevices.includes(streamParam.deviceId)) {
          this.$Message.error("该点位已被屏蔽，暂时无法查看！");
          return;
        }

        this.videoObj.play(index, streamParam, callback);
        // 新增播放历史
        if (!obj.videoUrl && this.addHistory) {
          let { deviceId, deviceName, ptzType } = { ...obj };
          addPlayback({
            userId: this.userInfo.id,
            deviceId,
            deviceName,
            ptzType,
            videoType: streamtype,
            palayTime: this.$dayjs().toISOString(),
            videoUrl: JSON.stringify(stream),
          });
        } else {
          obj.videoUrl = null;
        }
        // 工具条
        this.initToolBars(streamtype, index, streamParam.isptz, this.iframes);
        this.$set(this.winList, index, { ...obj, stream: streamParam });

        // 云台控制
        if (streamParam.isptz) {
          this.videoObj.setWindowPtz(index, true);
        }

        let idx = this.videoObj.focusIndex;
        this.orgInfo = this.winList[idx];
      }
    },
    // 视频告警检测轮巡的流播放
    playAlarmStream(obj, streamtype, winIndex, tabelSuffix) {
      return new Promise(async (resolve) => {
        if (this.videoObj) {
          let index =
            !!winIndex || winIndex === 0 ? winIndex : this.getAutoNextIndex();
          this.stopStream(index);
          let callback = {
            onerror: function onerror(type, detail, info) {
              console.error("onerror:", type, detail, info);
            },
            onPlayResult: function onerror(index, type, text) {
              console.log("onPlayResult:", index, type, text);
              resolve({ index, type, text });
            },
          };
          let res = await getCipherChannelNew(
            obj.deviceGbId ? obj.deviceGbId : obj.deviceId,
            tabelSuffix
          );
          if (res.code != 200 || !res.data) {
            if (!this.noVideoMsg) {
              this.$Message.error("未查询到通道信息");
            }
            resolve("");
          }
          let { channelNo, ip, port, userName, password } = res.data;
          let stream = {
            channel: channelNo,
            ip,
            port: Number(port),
            user: userName,
            password,
            desencode: 1,
            streamtype,
            title: obj.deviceName,
            devicetype: "pvgplus",
            isptz: obj.ptzType == "1" || obj.ptzType == "2" ? true : false,
          };
          this.videoObj.play(index, stream, callback);
          obj.videoUrl = null;
          // 工具条
          this.initToolBars(streamtype, index, stream.isptz, this.iframes);
          this.$set(this.winList, index, { ...obj, stream });
          // 云台控制
          if (this.$_has(["video-control"]) && stream.isptz) {
            this.videoObj.setWindowPtz(index, true);
          }
          let idx = this.videoObj.focusIndex;
          this.orgInfo = this.winList[idx];
        }
      });
    },
    // 播放录像
    playvod(val) {
      let index = this.videoObj.focusIndex;
      let obj = this.winList[index];
      if (!val) {
        this.videoObj.stop(index);
        this.$Message.info("当前查询时间段暂无录像");
      } else {
        this.playStream(obj, "vod", index, val);
        this.queryLog({
          muen: "视频中心",
          name: "录像检索",
          type: "4",
          remark: `查看【${obj.deviceName}】,【${this.$dayjs(
            val.begintime
          ).format("YYYY-MM-DD HH:mm:ss")}-${this.$dayjs(val.endtime).format(
            "YYYY-MM-DD HH:mm:ss"
          )}】的历史视频`,
        });
      }
    },
    // 打开录像检索
    async openVideoSearch(item, isFirst = false) {
      this.videoSearchOption.deviceId = item.deviceGbId
        ? item.deviceGbId
        : item.deviceId;
      this.videoSearchOption.deviceName = item.deviceName;
      if (isFirst) {
        let index = this.videoObj.focusIndex;
        let stream = await this.objToStream(item, "live");
        this.$set(this.winList, index, { ...item, stream });
      }
      this.videoSearchOption.stream =
        this.winList[this.videoObj.focusIndex].stream;
      this.$refs.videoSearchRef.show(true);
    },
    // 下载录像
    downloadVod(val) {
      let index = this.videoObj.focusIndex;
      let obj = this.winList[index];
      if (val && val.length) {
        //调用下载接口
        switch (obj.devicetype) {
          case "pvg67":
          case "pvgplus":
            val.forEach((v) => {
              let fileData = {
                beginTime: this.$dayjs(v.StartTime).format(
                  "YYYY-MM-DD HH:mm:ss:SSS"
                ),
                endTime: this.$dayjs(v.EndTime).format(
                  "YYYY-MM-DD HH:mm:ss:SSS"
                ),
                ip: obj.stream.ip,
                password: obj.stream.password,
                port: obj.stream.port,
                username: obj.stream.user,
                vodType: vodStorage == "device" ? 1 : 0,
              };
              console.log(fileData, obj.stream.channel);
              Toolkits.ocxUpDownHttp(
                obj.devicetype == "pvgplus" ? "pvgplus" : "pvg",
                "video",
                obj.stream.channel,
                `${obj.deviceName}(${this.$dayjs(v.StartTime).format(
                  "YYYYMMDDHHmmss"
                )}至${this.$dayjs(v.EndTime).format("YYYYMMDDHHmmss")}).mp4`,
                fileData
              );
            });
            break;
          default:
            val.forEach((v) => {
              Toolkits.ocxUpDownHttp(
                "lis",
                "video",
                v.DownloadUrl,
                `${obj.deviceName}(${this.$dayjs(v.StartTime).format(
                  "YYYYMMDDHHmmss"
                )}至${this.$dayjs(v.EndTime).format("YYYYMMDDHHmmss")}).mp4`
              );
            });
            break;
        }
      }
    },
    // 添加工具条
    async initToolBars(streamtype, index, isptz, iframes) {
      if (!this.isShowToolbar) return;
      let res = await controbarScenes(this, isptz, iframes, index);
      let sceneToolBars = res[this.sceneFrom];
      let toolbar = {
        topBar: {
          items: [],
        },
        bottomBar: {
          items: [],
        },
      };
      switch (streamtype) {
        case "live":
          toolbar.topBar.items = sceneToolBars[0];
          toolbar.bottomBar.items = sceneToolBars[1];
          break;
        case "vod":
          toolbar.topBar.items = sceneToolBars[2];
          toolbar.bottomBar.items = isCustomControl
            ? sceneToolBars[4]
            : sceneToolBars[3];
          break;
      }
      this.videoObj.createToolbar(index, toolbar);
      if (this.showToolbarAllways) this.videoObj.showOrHideToolbar(index, true)
    },
    // 自动切换下一窗口
    getAutoNextIndex() {
      if (this.isAutoNext && this.videoObj.maxScreenIndex == -1) {
        let index = this.videoObj.focusIndex + 1;
        index = index > this.videoObj.layoutcount - 1 ? 0 : index;
        this.videoObj.focusIndex = index;
      }
      this.isAutoNext = true;
      return this.videoObj.focusIndex;
    },
    stopStream(index) {
      this.videoObj.stop(index);
      this.cleanWin(index);
    },
    closeAll() {
      if (this.videoObj) {
        for (
          let winIndex = 0, winCount = this.videoObj.layoutcount;
          winIndex < winCount;
          winIndex++
        ) {
          this.stopStream(winIndex);
        }
      }
    },
    deleteAllWaterMarks() {
      this.winList.forEach((i, index) => {
        this.videoObj.delWaterMarkById(index);
      });
    },
    // 事件绑定
    handleEvent(handleMethod) {
      const _this = this;
      handleMethod("OnSwitchWindow", function H5_SwitchWindow(srcpos, dstpos) {
        srcpos = parseInt(srcpos);
        dstpos = parseInt(dstpos);
        let param = _this.winList[srcpos];
        _this.$set(_this.winList, srcpos, _this.winList[dstpos]);
        _this.$set(_this.winList, dstpos, param);
      });
      handleMethod("OnFocusChange", function H5_FocusChange(index) {
        _this.isShowPTZ = false;
        _this.$refs.videoSearchRef.show(false);
        _this.printScreenOption.open = false;
        _this.$emit("update:isShowTimershaft", false);
      });
      handleMethod("OnVideoClose", function H5_VideoClose(index) {
        _this.cleanWin(index);
      });
      handleMethod("OnDrawingObject", function H5_DrawingObject(obj) {
        if (obj.eventype == "add") {
          let array = _this.videoObj.getAllDrawingObject(
            _this.videoObj.focusIndex
          );
          if (array.length > 1) {
            // 只保留一个框选
            array.forEach((i) => {
              if (i.id != obj.id) {
                _this.videoObj.deleteDrawingById(
                  _this.videoObj.focusIndex,
                  i.id
                );
              }
            });
          }
          const rectGs = document.querySelectorAll('g[type="rect"]');
          rectGs.forEach((rectG) => {
            rectG.removeEventListener("dblclick", _this.finishDrawingCb);
            rectG.addEventListener("dblclick", _this.finishDrawingCb);
          });
        }
      });
      handleMethod("OnWndClick", function H5_WndClick(index) {
        _this.isAutoNext = false;
      });
      handleMethod("OnWndDClik", (index, event) => {
        if (!this.dbFullScreen) return;
        if (event.currentTarget.style.position == "fixed") {
          event.currentTarget.style.position = "relative";
        } else {
          event.currentTarget.style.position = "fixed";
          event.currentTarget.style.zIndex = 1;
        }
        this.videoObj.videos[0].setPosition(
          0,
          0,
          event.currentTarget.clientWidth + "px",
          event.currentTarget.clientHeight + "px"
        );
      });
    },
    attachEventEx(evtstr, evtfun) {
      if (this.videoObj) {
        this.videoObj.addEventListener(evtstr, evtfun);
      }
    },
    detachEventEx(evtstr, evtfun) {
      if (this.videoObj) {
        this.videoObj.removeEventListener(evtstr);
      }
    },
    // 拖拽播放
    async H5_DropVideo(event) {
      console.log("drop", event.dataTransfer.getData("text/plain"));
      try {
        let index = event.currentTarget.getAttribute("index");
        let obj = JSON.parse(event.dataTransfer.getData("text/plain"));
        if (!obj.orgCode) {
          let result = await queryCameraDeviceList({ deviceId: obj.deviceId });
          let { deviceGbId, geoPoint, ptzType, orgCode } = {
            ...result.data[0],
          };
          obj = { ...obj, deviceGbId, geoPoint, ptzType, orgCode };
        }
        this.videoObj._setFocusIndex(parseInt(index));
        this.playStream(obj, obj.playType, index);
      } catch (e) {}
    },
    attachDropEvent() {
      if (this.videoObj) {
        let doms = document.getElementsByClassName("h5vp-video-containerEx");
        Array.prototype.forEach.call(doms, (dom) => {
          dom.addEventListener("drop", this.H5_DropVideo);
        });
      }
    },
    detachDropEvent() {
      if (this.videoObj) {
        let doms = document.getElementsByClassName("h5vp-video-containerEx");
        Array.prototype.forEach.call(doms, (dom) => {
          dom.removeEventListener("drop", this.H5_DropVideo);
        });
      }
    },
    // 切换布局
    changeLayout(layout) {
      if (this.videoObj) {
        this.detachDropEvent();
        this.videoObj.layout = layout;
        this.initWin();
        this.attachDropEvent();
      }
    },
    // 扩展屏
    moveExtendedScreen() {
      console.log("扩展屏");
      if (!window.screen) return;
      if (this.videoObj) {
        this.videoObj.getExpandScreenInfo((result) => {
          let info = JSON.parse(result.info);
          let expandScreen = info.find((v) => v.isPrimary == 0);
          if (!expandScreen) {
            alert("未找到扩展屏");
            return;
          }
          this.videoObj.moveProcessWindow({
            process: "chrome.exe",
            title: document.title,
            showtype: 1,
            left: expandScreen.curRect.left,
            top: expandScreen.curRect.top,
          });
        });
      }
    },
    // 时间轴
    changeRcdtimershaft(command) {
      if (this.videoObj) {
        this.videoObj.enablercdtimershaft = command;
      }
    },
    // 全屏
    setFullScreen() {
      if (this.videoObj) {
        this.videoObj.setFullScreen(false);
      }
    },
    /** 工具栏相关 begin */
    // 色彩调节组件
    createFilterComponent() {
      const videoFilterComponent = new Vue({
        render: (h) =>
          h(videoFilterTool, {
            props: {
              filterObj:
                this.videoObj &&
                this.videoObj.getFilter(this.videoObj.focusIndex),
            },
            on: {
              changefilter: (type, obj) => {
                this.videoObj.setFilter(this.videoObj.focusIndex, {
                  [type]: parseInt(obj[type]),
                });
              },
            },
          }),
      });
      const element = document.createElement("div");
      videoFilterComponent.$mount(element);
      return videoFilterComponent;
    },
    // 双击框选范围并以图搜图
    finishDrawingCb() {
      let array = this.videoObj.getAllDrawingObject(this.videoObj.focusIndex);
      let imgUrl = this.videoObj.capturePicture(
        this.videoObj.focusIndex,
        "jpeg"
      );
      this.clipImg(array[0], imgUrl);
    },
    // 根据框选裁剪图片
    clipImg(info, imgUrl) {
      var image = new Image();
      image.src = imgUrl;
      image.onload = function () {
        const myCanvas = document.createElement("canvas");
        const ctx = myCanvas.getContext("2d");
        myCanvas.width = info.videoWidth;
        myCanvas.height = info.videoHeight;
        let point1 = {
          x: info.posinfo[0].x * info.videoWidth,
          y: info.posinfo[0].y * info.videoHeight,
        };
        let point2 = {
          x: info.posinfo[1].x * info.videoWidth,
          y: info.posinfo[1].y * info.videoHeight,
        };
        ctx.drawImage(image, 0, 0);
        var croppedImage = ctx.getImageData(
          point1.x,
          point1.y,
          point2.x - point1.x,
          point2.y - point1.y
        );
        var croppedCanvas = document.createElement("canvas");
        var croppedContext = croppedCanvas.getContext("2d");
        croppedCanvas.width = point2.x - point1.x;
        croppedCanvas.height = point2.y - point1.y;
        croppedContext.putImageData(croppedImage, 0, 0);
        console.log(croppedCanvas.toDataURL());
      };
    },
    // 云台控制-海涛平台
    setPtzControl(cmd, speed, inx) {
      let index = !!inx || inx == 0 ? inx : this.videoObj.focusIndex;
      let obj = this.winList[index];
      if (
        !(
          obj.deviceChildType == "1" ||
          obj.deviceChildType == "2" ||
          obj.ptzType == "1" ||
          obj.ptzType == "2"
        )
      )
        return;
      let param = {
        channel: obj.deviceGbId,
        command: cmd,
        speed: speed,
      };
      if (cmd == "preset-goto") param.preset = 1;

      if (this.lastPtzParamStr != JSON.stringify(param)) {
        this.lastPtzParamStr = JSON.stringify(param);
        fetchPTZControl(param);
      }
    },
    // 添加帧标记
    handleSubmitFrameMark(form) {
      if (this.videoObj) {
        let index = this.videoObj.focusIndex;
        let obj = this.winList[index];
        let item = {
          absTime: this.frameMarkOption.absTime,
          color: form.color,
          class: "",
          template: `<img src="${this.frameMarkOption.src}"/>`,
          hoverFn: (options) => {
            console.log(options);
          },
        };
        this.videoObj.addFrameMark(index, {
          ...this.frameMarkAddOption,
          list: [item],
        });
        this.frameMarkOption.open = false;

        let addpParams = {
          deviceId: obj.deviceId,
          deviceName: obj.deviceName,
          isDel: 0,
          markColor:
            form.markColor == "#EB4B4B"
              ? "1"
              : form.markColor == "#F29F4C"
              ? "2"
              : form.markColor == "#FDEE38"
              ? "3"
              : form.markColor == "#67D28D"
              ? "4"
              : "5",
          markImageBase64: form.src,
          markTime: form.markTime,
          markTitle: form.markTitle,
          remark: form.remark,
          orgCode: this.orgInfo.orgCode,
          orgName: this.orgInfo.orgName,
          userId: this.userInfo.id,
        };
        let roleParam = {
          roleId: this.userInfo.roleVoList[0].id,
          filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
          socialResources: this.individuation.hideCommunity ? 0 : 1,
          excludeDeviceTypes: this.individuation.hideDeviceTypes
            ? this.individuation.hideDeviceTypes
            : [],
        };
        let params = {
          searchKey: addpParams.deviceName,
          isOnlineStatus: [],
          ...roleParam,
          pageNumber: 1,
          pageSize: 19,
        };
        queryDeviceOrgTree(params).then((res) => {
          if (res.data.deviceList) {
            addpParams.orgName = res.data.deviceList[0].orgName;
            addpParams.orgCode = res.data.deviceList[0].orgCode;
            if (!addpParams.deviceId) {
              addpParams.deviceId = res.data.deviceList[0].deviceId;
            }
            // console.log(addpParams, 'addpParams');
            frameAdd(addpParams).then((res) => {
              if (res.code == 200) {
                this.$Message.success("添加成功");
              } else {
                this.$Message.warning(res.msg);
              }
            });
          }
        });
      }
    },
    /** 工具栏相关 end */
    playPfs(index, params) {
      let streamParam = this.parseCameraInfoPFS(params);
      this.videoObj.play(index, streamParam);
      this.initToolBars("vod", index, false);
    },
    // 处理PFS流
    parseCameraInfoPFS: function (result) {
      let pfsParams = window.pfsInfo;
      //获取相关参数
      let tempPath = result.path.substring(
          result.path.indexOf(":") + 1,
          result.path.length
        ),
        server = result.server,
        pfsIp = (server && server.ip) || pfsParams.server.ip, //pfs文件服务 IP
        pfsPort = (server && server.port) || pfsParams.server.port, //pfs文件服务 端口
        pfsUsername = (server && server.userName) || pfsParams.server.userName, //pfs文件服务 用户名
        pfsPassword = (server && server.password) || pfsParams.server.password, //pfs文件服务 密码
        params = {
          devicetype: "pfsfile",
          streamtype: "vod",
          desencode: 0,
          url: `NPFS:${pfsIp}:${pfsPort}/username=${pfsUsername}&password=${pfsPassword}#${tempPath}`,
          title: result.title,
        };
      return params;
    },
  },
};
</script>
<style lang="less" scoped>
@import "./style/player";
.H5Player,
.video-div {
  width: 100%;
  height: 100%;
}
</style>
