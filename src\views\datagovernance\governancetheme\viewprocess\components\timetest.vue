<template>
  <div class="time">
    <ui-modal v-model="visible" title="时钟信息检测设置配置" width="39.06rem">
      <div>
        <Form ref="form" :model="form" :rules="ruleValidate" :label-width="200">
          <FormItem label="时间偏差：不大于" prop="timeDeviation">
            <Input v-model="form.timeDeviation" number style="width: 118px; margin: 0 10px 0 2px"></Input>
            <span class="text">秒</span>
          </FormItem>
          <FormItem label="重设设备时钟：" prop="isReset">
            <RadioGroup v-model="form.isReset">
              <Radio label="1">是</Radio>
              <Radio label="0">否</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </div>
      <template slot="footer">
        <Button type="primary" @click="handleSave">保存&nbsp;</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {},
  data() {
    return {
      visible: false,
      topicComponentId: '',
      form: {
        timeDeviation: '111',
        isReset: '1',
      },
      ruleValidate: {
        timeDeviation: [
          {
            required: true,
            message: '请输入时间偏差',
            trigger: 'blur',
            type: 'number',
          },
        ],
        isReset: [{ required: true, message: '请选择设备时钟', trigger: 'change' }],
      },
    };
  },
  created() {},
  methods: {
    init(processOptions) {
      this.topicComponentId = processOptions.topicComponentId;
      this.visible = true;
      this.$nextTick(() => {
        this.getTimeData();
      });
    },
    handleSave() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          try {
            let params = {
              topicComponentId: this.topicComponentId,
              timeDeviation: this.form.timeDeviation,
              isReset: this.form.isReset,
            };
            await this.$http.post(governancetheme.updateTopicComponentOfDeviceTime, params);
            this.visible = false;
            this.$emit('render');
            this.$Message.success('时钟信息检测配置成功！');
            // this.$Message["success"]({
            //   background: true,
            //   content: "时钟信息检测配置成功！",
            // });
          } catch (error) {
            console.log(error);
          }
        } else {
          this.$Message.error('请将信息填写完整！');
        }
      });
    },
    async getTimeData() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicComponentOfDeviceTime, { params });
        const datas = res.data.data;
        this.form.timeDeviation = datas.timeDeviation;
        this.form.isReset = datas.isReset;
      } catch (error) {
        console.log(error);
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.text {
  font-size: 14px;
  color: #ffffff;
}
@{_deep} .ivu-modal-body {
  padding: 20px 50px !important;
}
@{_deep} .ivu-radio-wrapper {
  font-size: 14px;
  margin-right: 30px;
}
@{_deep} .ivu-radio-inner {
  width: 14px;
  height: 14px;
  margin-right: 5px;
  background: transparent;
  border: 1px solid var(--color-primary);
}
@{_deep} .ivu-radio-inner:after {
  width: 8px;
  height: 8px;
  background: 2b84e2;
}
@{_deep} .ivu-select .ivu-select-selection {
  height: 34px;
  line-height: 34px;
  border-color: var(--color-primary);
  border: 1px solid var(--color-primary);
  border-radius: 4px;
}
</style>
