<template>
  <div class="page-srogramme auto-fill">
    <div class="content auto-fill">
      <div class="search-wrapper">
        <ui-label class="inline mr-md" label="指标类型" :width="66">
          <Select class="input-width" v-model="searchData.indexModule" placeholder="请选择指标类型" clearable>
            <Option v-for="(item, index) in global.indexTypeList" :key="index" :value="item.id"
              >{{ item.title }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-md" label="检测时间" :width="66">
          <DatePicker
            class="width-md"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            v-model="searchData.startTime"
            :options="startTimeOption"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
            placeholder="请选择开始时间"
          ></DatePicker>
          <span class="horizontalbar"> — </span>
          <DatePicker
            class="width-md"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            v-model="searchData.endTime"
            :options="endTimeOption"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
            placeholder="请选择结束时间"
          ></DatePicker>
        </ui-label>
        <ui-label class="inline" label="关键词">
          <Input v-model="searchData.searchValue" class="width-lg" placeholder="请输入关键词"></Input>
        </ui-label>

        <ui-label :width="30" class="inline ml-md" label="">
          <Button type="primary" class="" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </ui-label>
      </div>
      <div class="table-box auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #indexName="{ row }">
            <div class="d_flex d_flex_width">
              <Tooltip max-width="300" class="tooltip-cell-box" :disabled="isTooltipDisabled(row)">
                <span class="ellipsis inline width-indexName vt-middle">{{ row.indexName }}</span>
                <i
                  class="icon-font icon-wenhao vt-middle icon-warning ml-sm icon-details"
                  @click="openDetailsDialog(row)"
                ></i>
                <template #content>
                  {{ row.indexName }}
                </template>
              </Tooltip>
            </div>
          </template>
          <template #formatResultValue="{ row }">
            <span style="color: #de990f" v-if="row.reinspectStatus === '1'"> 复检中... </span>
            <span
              v-else
              class="check-statu"
              :class="[
                row.qualified == 1 ? 'font-green' : '',
                row.qualified == 2 ? 'color-failed' : '',
                row.qualified == 3 ? 'font-D66418' : '',
                row.qualified == 0 ? 'color-failed' : '',
              ]"
            >
              {{ row.formatResultValue }}
            </span>
          </template>
          <template #showResult="{ row }">
            <i-switch
              class="iswitch"
              :value="row.showResult"
              :true-value="1"
              :false-value="0"
              @on-change="onChange($event, row)"
            />
          </template>
          <template #reinspectProgressPercent="{ row }">
            <div class="progress-task-box">
              <div class="progress">
                <Progress
                  :percent="row.reinspectProgressPercent || 0"
                  stroke-color="var(--color-progress-success)"
                  :hide-info="true"
                ></Progress>
                <span class="text font-green">{{ row.reinspectProgressPercent || 0 }}%</span>
              </div>
            </div>
          </template>
          <template #option="{ row }">
            <ui-btn-tip
              v-if="isExistIndex(row.indexType)"
              icon="icon-chakanxiangqing"
              content="结果详情"
              @click.native="handleClickJump(row)"
            ></ui-btn-tip>
            <create-tabs
              v-else
              :componentName="themData.componentName"
              :tabs-text="themData.text"
              @selectModule="selectModule"
              class="inline"
              :tabs-query="{
                indexId: row.indexId,
                code: row.regionCode,
                access: 'TASK_RESULT',
                batchId: row.batchId,
                startTime: row.startTime,
              }"
            >
              <ui-btn-tip icon="icon-chakanxiangqing" content="结果详情"></ui-btn-tip>
            </create-tabs>
            <!-- <ui-btn-tip
              icon="icon-peizhi ml-md"
              content="管理检测计划"
              @click.native="getConfiguration('configuration', row, index)"
            ></ui-btn-tip> -->
            <ui-btn-tip
              v-if="recheckBtnVisible(row)"
              icon="icon-fujian"
              content="复检"
              v-permission="{
                route: $route.name,
                permission: 'recheck',
              }"
              class="operatbtn f-14 ml-sm"
              @click.native="clickRecheck(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              v-if="terminationBtnVisible(row)"
              icon="icon-renwuzhongzhi"
              content="终止"
              class="operatbtn f-14 ml-sm"
              :styles="{ color: '#CF3939' }"
              @click.native="clickTermination(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="operatbtn rmargin ml-sm"
              icon="icon-shanchu3"
              content="删除"
              v-permission="{
                route: $route.name,
                permission: 'deletehistorytask',
              }"
              @click.native="handleDeleteHistoryTask(row)"
            ></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <ui-page class="ui-page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>

    <BasicInformation ref="BasicInformation" :module-action="moduleAction" :modalData="modalData" />
    <recheck
      v-model="recheckVisible"
      :module-data="moduleData"
      :is-batch-recheck="true"
      :is-use-router-params="isUseRouterParams"
      @handleUpdate="init"
    >
    </recheck>
    <!-- 指标配置详情 -->
    <common-model ref="model" v-model="indexVisible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>

<script>
import evaluationreport from '@/config/api/evaluationreport';
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import expandRow from './table-expand.vue';
import indexRecheckConfig from './utils/indexRecheckConfig.js';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
export default {
  props: {
    activeModuleMessageId: {
      required: true,
      type: String,
    },
    taskSchemeId: {
      required: true,
      type: String,
    },
  },
  mixins: [evaluationoResultMixin],
  data() {
    return {
      currentRow: {},
      indexVisible: false,
      themData: {
        componentName: 'detectionToOverview', // 需要跳转的组件名
        text: '评测结果', // 跳转页面标题
        title: '评测结果',
        type: 'view',
      },
      loading: false,
      searchData: {
        indexModule: null,
        startTime: null,
        endTime: null,
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
        searchValue: null,
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center', className: 'column-index' },
        {
          type: 'expand',
          width: 50,
          render: (h, params) => {
            // this.expandRow(params)
            return h(expandRow, {
              props: {
                row: {
                  ...params.row,
                  indexModule: this.searchData.indexModule,
                  startTime: this.searchData.startTime,
                  endTime: this.searchData.endTime,
                },
                tableColumns: this.tableColumns,
              },
              on: {
                refresh: () => {
                  // 后欣要求：删除不刷新
                },
                handleClickRecheck: this.clickRecheck,
              },
            });
          },
          className: 'column-expand',
        },
        {
          title: '指标名称',
          key: 'indexName',
          slot: 'indexName',
          tooltip: true,
          minWidth: 150,
          className: 'column-indexName', // 方便 expandRow 组件计算宽度，列对齐
        },
        { title: '执行时间', key: 'detectTime', minWidth: 100, className: 'column-detectTime' },
        { title: '检测数据量', key: 'detectDataCount', className: 'column-detectDataCount' },
        {
          title: '检测进度',
          key: 'reinspectProgressPercent',
          slot: 'reinspectProgressPercent',
          align: 'left',
          className: 'column-reinspectProgressPercent',
        },
        { title: '耗时', key: 'spendTime', className: 'column-spendTime', minWidth: 80 },
        {
          title: '检测结果',
          key: 'formatResultValue',
          slot: 'formatResultValue',
          align: 'left',
          className: 'column-formatResultValue',
        },
        {
          title: '发布结果',
          key: 'showResult',
          slot: 'showResult',
          align: 'center',
          className: 'column-showResult',
        },
        {
          title: '操作',
          slot: 'option',
          width: 110,
          className: 'column-option',
        },
      ],
      tableData: [],
      modalData: {},
      moduleAction: {},
      expandList: [],
      startTimeOption: {
        disabledDate: (date) => {
          if (this.searchData.endTime) {
            let endTime = new Date(this.searchData.endTime).getTime();
            return date > endTime || date < endTime - 30 * 24 * 60 * 60 * 1000;
          } else {
            return false;
          }
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          if (this.searchData.startTime) {
            let startTime = new Date(this.searchData.startTime).getTime();
            return date < startTime || date > startTime + 30 * 24 * 60 * 60 * 1000;
          } else {
            return false;
          }
        },
      },
      moduleData: {},
      recheckVisible: false,
      isUseRouterParams: false, // 是否使用路由参数
      // statisticalList: {},
    };
  },
  created() {
    this.searchData.taskSchemeId = this.activeModuleMessageId;
    this.search();
  },
  methods: {
    handleClickJump({ batchId, indexId, indexType, taskSchemeId, regionCode, orgCode }) {
      this.jump({
        orgCode: orgCode,
        regionCode: regionCode,
        statisticType: 'REGION',
        taskSchemeId: taskSchemeId,
        indexId: `${indexId}`,
        indexType: indexType,
        batchId: batchId,
      });
    },
    handleDeleteHistoryTask({ batchId }) {
      this.$UiConfirm({ content: '删除后检测记录不可恢复，确定删除吗？' }).then(async () => {
        try {
          let params = {
            batchId,
          };
          let { data } = await this.$http.post(governanceevaluation.postDeleteIndexResult, params);
          // this.init()
          // 后欣要求: 删除后不刷新
          this.selfControlDelete(batchId);
          this.$Message.success(data.msg);
        } catch (e) {
          console.log(e);
        }
      });
    },
    selfControlDelete(batchId) {
      let Index = this.tableData.findIndex((item) => {
        return item.batchId === batchId;
      });
      this.tableData.splice(Index, 1);
    },
    async init() {
      try {
        this.loading = true;
        let params = {
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          taskSchemeId: this.taskSchemeId,
          indexModule: this.searchData.indexModule,
          startTime: this.searchData.startTime,
          endTime: this.searchData.endTime,
          searchValue: this.searchData.searchValue,
        };

        let res = await this.$http.post(governanceevaluation.taskIndexResultPageList, params);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = 20;
      this.init();
    },
    clear() {
      this.searchData.indexModule = null;
      this.searchData.startTime = null;
      this.searchData.endTime = null;
      this.searchData.searchValue = null;
      this.search();
    },
    changePage(val) {
      this.searchData.pageNum = 1;
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = 20;
      this.searchData.pageSize = val;
      this.init();
    },
    selectModule(name) {
      this.$emit('selectModule', name);
    },
    async clickTermination(row) {
      try {
        const params = {
          id: row.id,
          batchId: row.batchId,
        };
        await this.$http.post(evaluationoverview.terminateRunningReinspectJob, params);
        this.$Message.success('终止成功！');
        this.$emit('refresh');
      } catch (e) {
        console.log(e);
      }
    },
    async clickRecheck(row) {
      this.moduleData = { ...row };
      const rowKeys = Object.keys(row);
      this.moduleData.displayType = rowKeys.includes('regionCode') ? 'REGION' : 'ORG';
      this.recheckVisible = true;
    },
    recheckBtnVisible(row) {
      return Object.keys(indexRecheckConfig).includes(row.indexType) && row.reinspectStatus !== '1';
    },
    terminationBtnVisible(row) {
      return Object.keys(indexRecheckConfig).includes(row.indexType) && row.reinspectStatus === '1';
    },
    // 指标详情
    openDetailsDialog(row) {
      let { indexType, indexName, indexId } = row;
      this.indexVisible = true;
      this.currentRow = {
        indexType: indexType,
        indexName: indexName,
        id: indexId,
      };
    },
    async onChange(val, row) {
      try {
        let data = {
          batchId: row.batchId,
          showResult: val,
        };
        await this.$http.post(evaluationreport.operationIndexResultByBatchId, data);
        this.$Message.success('成功');
      } catch (error) {
        console.log(error);
      }
    },
  },
  computed: {
    // ... 时才提示
    isTooltipDisabled() {
      return (row) => {
        let el = document.querySelectorAll('.d_flex_width');
        let flag = true;
        if (el?.length) {
          flag = el[0]?.offsetWidth - 30 > (row.indexName?.length * 14 || 0) ? true : false;
        }
        return flag;
      };
    },
  },
  watch: {
    activeModuleMessageId(val) {
      this.searchData.taskSchemeId = val;
      this.search();
    },
  },
  components: {
    expandRow,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    BasicInformation:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/basics/basic-information.vue')
        .default,
    recheck: require('@/views/governanceevaluation/evaluationoResult/result-modules/components/recheck/index.vue')
      .default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
  },
};
</script>
<style lang="less" scoped>
.text_hid {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.horizontalbar {
  color: #1b82d2;
}
.check-statu {
  min-width: 70px;
  height: 23px;
  line-height: 23px;
  text-align: left;
  display: inline-block;
  border-radius: 4px;
  font-size: 14px;
}
.page-srogramme {
  position: relative;
  height: 100%;
  background: var(--bg-content);
  .content {
    position: relative;
    height: 100%;
    background: var(--bg-content);
  }
  .search-wrapper {
    overflow: hidden;

    .input-width {
      width: 230px;
    }
  }

  .table-box {
    position: relative;
    height: 85%;
    overflow: hidden;
    margin-top: 20px;

    @{_deep}.standard {
      padding: 10px 0;
    }

    .ui-table {
      @{_deep} .ivu-table-body {
        overflow-x: hidden !important;
        overflow-y: auto !important;
        td {
          padding: 10px 0 10px 0;
        }
      }
      @{_deep} .ivu-table-fixed-right .ivu-table-fixed-body {
        td {
          padding: 10px 0 10px 0;
        }
      }

      @{_deep} .ivu-table-cell {
        .ivu-tooltip-rel {
          .text_hid();
        }
      }
    }

    .evaluation-criterion {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2; /*超出3行部分显示省略号，去掉该属性 显示全部*/
      -webkit-box-orient: vertical;
    }

    .ui-page {
      position: relative;
      bottom: 0;
    }
    @{_deep}.ivu-table-cell-expand {
      i {
        color: rgba(43, 132, 226, 1);
      }
    }
  }
}
@{_deep}.ivu-table-cell {
  .icon-chakanxiangqing {
    color: #de990f !important;
  }
}

.icon-peizhi {
  color: rgba(38, 159, 38, 1);
}
.icon-lishijilu-01 {
  color: rgba(67, 140, 255, 1);
}
.btn-text-default {
  cursor: pointer;
  font-size: 14px;
  color: var(--color-primary);
}
// 更改进度条
@{_deep} .progress-task-box {
  display: flex;
  .ivu-progress {
    .ivu-progress-outer {
      .ivu-progress-inner {
        border: 1px solid var(--color-progress-success);
        background: transparent;
        padding: 1px;
        .ivu-progress-bg {
          height: 8px !important;
        }
      }
    }
  }
  .text {
    color: var(--color-progress-success);
  }
}
.progress {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.text {
  width: 60px; /*no*/
  margin-left: 2px; /*no*/
}
.tooltip-cell-box {
  width: 100%;
  /deep/ .ivu-tooltip-popper {
    left: 80px !important;
  }
  /deep/ .ivu-tooltip-rel {
    display: flex !important;
    align-items: center;
    flex-direction: row;
  }
  .width-indexName {
    max-width: calc(100% - 30px);
  }
  .icon-details {
    width: 18px;
    display: none;
  }
}
/deep/ .ivu-table-row:hover {
  .icon-details {
    display: block;
  }
}
</style>
<style lang="less">
// 弹窗列表样式
.diffent-tab {
  .ivu-table-wrapper {
    height: 400px !important;
  }
  .ivu-table-body {
    height: 400px !important;
  }
}
.ml-65 {
  margin-left: 65px;
}
</style>
