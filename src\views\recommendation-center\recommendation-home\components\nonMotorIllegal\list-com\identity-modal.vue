<template>
    <hlModal
        v-model="modalShow"
        :r-width="dialogData.rWidth"
        :title="dialogData.title"
        list-content
    >
        <div class="content-box">
            <div class="form-box">
                <Form :inline="true" :label-width="75">
                    <FormItem label="相似度:">
						<div class="slider-content">
                            <Slider v-model="paramCofig.similarity"></Slider>
                            <span>{{ paramCofig.similarity }}%</span>
                        </div>
					</FormItem>
                </Form>
                <div class="btn-group">
                    <Button type="primary" @click="searchHandle">查询</Button>
                </div>
            </div>
            <Table
                class="auto-fill table" 
                ref="table"
                :height="515" 
                :columns="columns" 
                :data="tableData"
                :loading="loading"
            >
                <template #loading>
                    <ui-loading></ui-loading>
                </template>
                <template #faceImg="{ }">
                    <img class="table-img" :src="archivesInfo.faceImg" v-viewer alt=""/>
                </template>
                <template #absTime="{ }">
                    <div>{{ archivesInfo.absTime }}</div>
                </template>
                <template #photoUrlList="{ row }">
                    <img class="table-img" :src="row.photoUrlList[0].photoUrl" v-viewer alt=""/>
                </template>
                <template #score="{ row }">
                    <div>{{ row.score }}%</div>
                </template>
                <template #action="{ row }">
                    <div class="btn-tips">
                        <ui-btn-tip content="关联" icon="icon-guanlian" class="primary" @click.native="handleAssociat(row)"/>
                    </div>
                </template>
            </Table>
            <ui-page
                :current="pageInfo.pageNumber"
                :total="total"
                :page-size="pageInfo.pageSize"
                @pageChange="pageChange"
                @pageSizeChange="pageSizeChange"
            >
            </ui-page>
        </div>
    </hlModal>
</template>
<script>
import hlModal from '@/components/modal/index.vue'; 
import { faceIdentity } from '@/api/modelMarket';
import { queryFaceLibList } from '@/api/target-control'
import { picturePick } from '@/api/wisdom-cloud-search';
import { associationIdentity } from '@/api/recommend';

export default{
    components: { hlModal },
    data() {
        return {
            dialogData: {
                title: "身份关联",
                rWidth: 1600,
            },
            modalShow: false,
            dataList: [],
            loading: false,
            columns: [
                { title: "序号", width: 70, type: "index", key: "index" },
                { title: "抓拍照片", slot: "faceImg" },
                { title: "抓拍时间", slot: "absTime" },
                { title: "比对照片", slot: "photoUrlList" },
                { title: "姓名", key: "name" },
                { title: "身份证号", key: "idCardNo" },
                { title: "相似度", slot: "score" },
                { title: "比对库", key: "libName" },
                { title: "操作", slot: "action" },
            ],
            tableData: [],
            pageInfo: {
                pageNumber: 1,
                pageSize: 10,
            },
            total: 0,
            archivesInfo: {
                absTime: '',
                faceImg: '',
                imageBase: ''
            },
            paramCofig: {
                similarity: 75,
                algorithmVendorType: 'HK',
                sortField: 'similarity',
                faceLibIds: []
            }
        }
    },
    mounted() {

    },
    methods: {
        async show(item) {
            this.modalShow = true;
            this.archivesInfo = item
            this.archivesInfo.imageBase = await this.getImageBase(item.faceImg)
            await this.queryFaceLib()
            this.queryList()
        },
        // 页数改变
        pageChange(size) {
            this.pageInfo.pageNumber = size;
            this.queryList();
        },
        // 页数量改变
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1;
            this.pageInfo.pageSize = size;
            this.queryList();
        },
        getImageBase(imgUrl) {
            return new Promise(resolve => {
                let fileData = new FormData()
				fileData.append('algorithmType', 1)
                fileData.append('fileUrl', imgUrl)
                picturePick(fileData)
                .then(res =>{
                    let params = res.data[0];
                    resolve(params.imageBase)
                })
            })
        },
        // 比对库
        queryFaceLib() {
            return new Promise(resolve => {
                let param = {
                    libSource: '2',
                    libName: '',
                    userId: this.userInfo.id
                }
                queryFaceLibList(param).then(res => {
                    let list =  res.data || [];
                    let faceLibIds = list.map(item => item.id);
                    this.paramCofig.faceLibIds = faceLibIds
                    resolve()
                })
            })
        },
        queryList() {
            this.loading = true;
            this.tableData = [];
            let params = {
                imageBase: this.archivesInfo.imageBase,
                userId: this.userInfo.id,
                ...this.pageInfo,
                ...this.paramCofig,
                similarity: this.paramCofig.similarity / 100
            }
            faceIdentity(params)
            .then(res => {
                this.loading = false;
                this.tableData = res.data.entities || [];
                this.total = res.data.total;
            }).catch(err => {
                this.loading = false;
                this.tableData = [];
                this.total = 0;
            })
        },
        searchHandle() {
            this.pageInfo.pageNumber = 1;
            this.queryList();
        },
        handleAssociat(row) {
            let param = {
                recordId: this.archivesInfo.recordId,
                idCardNo: row.idCardNo,
                name: row.name,
                similarity: row.score/100,
                indexName: this.archivesInfo.indexName,
                associationIdCardStatus: 3,
                photo: row.photoUrlList[0].photoUrl
            }
            associationIdentity(param).then(res => {
                if (res.code == 200) {
                    this.$Message.success("关联成功！")
                    this.modalShow = false;
                    setTimeout(() => this.$emit('refresh'), 1000)
                }
            })
        }
    }
}
</script>
<style lang="less" scoped>
.content-box{
    height: 640px;
    display: flex;
    flex-direction: column;
    .table-img {
        width: 80px;
    }
    .form-box {
        display: flex;
        justify-content: space-between;
    }
}
</style>