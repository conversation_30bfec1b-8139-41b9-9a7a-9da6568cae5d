import OverlayLayerOpts = NPMapLib.Layers.OverlayLayerOpts;
import {MapConfigModel} from "./core/model/map.config.model";
import InfoWindowOpts = NPMapLib.Symbols.InfoWindowOpts;
import _PolylineStyle = NPMapLib.Geometry._PolylineStyle;
import _PolygonStyle = NPMapLib.Geometry._PolygonStyle;
import _CircleStyle = NPMapLib.Geometry._CircleStyle;
import _AnimationLineOpts = NPMapLib.Symbols._AnimationLineOpts;
import _AnimationLinePreDrawEvent = NPMapLib.Symbols._AnimationLinePreDrawEvent;
import _AnimationLineAfterDrawEvent = NPMapLib.Symbols._AnimationLineAfterDrawEvent;
import _AnimationLineAfterStepEvent = NPMapLib.Symbols._AnimationLineAfterStepEvent;
import {MapPointModel} from "./map.main";
import {CameraEx} from "../../../core/entity/ex/CameraEx";
import {SystemPointEx} from "../../../core/entity/ex/SystemPointEx";
import Point = NPMapLib.Geometry.Point;
import _LabelStyle = NPMapLib.Symbols._LabelStyle;

export interface InfoWindowEvent {
    open?: Function,
    close?: Function,
    hide?: Function
}

/**
 * 图形的样式
 * 此interface不使用, 只做属性释义
 */
// export params GeometryStyle {
//     color: string; //颜色
//     fillColor: string; //填充颜色
//     weight: number; //宽度，以像素为单位
//     opacity: number; //透明度，取值范围0 - 1
//     fillOpacity: number; //填充的透明度，取值范围0 - 1,
//     strokeColor: string; // 'blue'
//     strokeDashstyle: string; // 'dash'
//     lineStyle: string; //样式 NPMapLib.LINE_ARROW_TYPE_BOTH, 在map.d.ts中搜索LINE_ARROW_TYPE_BOTH可以看其他可用值
// }

interface TraceAnalyzeEventOpts{
    afterDraw: ()=>void;
}

export interface TraceAnalyzePreDrawEvent extends _AnimationLinePreDrawEvent{

}
export interface TraceAnalyzeAfterDrawEvent extends _AnimationLineAfterDrawEvent{

}
export interface TraceAnalyzeAfterStepEvent extends _AnimationLineAfterStepEvent{

}

export interface TraceAnalyzeOpts{
    speed: number; // 设置播放速度
    color: string; // 设置线条颜色,默认red
    opacity: number; // 设置线条透明度, 默认1
    weight: number; // 线条权重
    afterDraw: (evt: TraceAnalyzeAfterDrawEvent)=>void; // 绘制新的轨迹结束触发
    preDraw: (evt: TraceAnalyzePreDrawEvent)=>void; // 绘制新的轨迹之前触发
    afterStep: (evt: TraceAnalyzeAfterStepEvent)=>void; // 每一步结束以后
    start: Function; // 轨迹绘制开始
    pause: Function; // 轨迹绘制暂停
    stop: Function; // 轨迹绘制停止
    moving: Function; // 轨迹移动中
    moved: Function; // 轨迹移动完成
}

// 这里ClusterMarkerEx里的参数需要和ClusterMarkerParamsNameType对应上, 用于一些业务操作
export interface ClusterMarkerEx extends NPMapLib.Symbols.ClusterMarker{
    id: string;
    objectId: string;
    titleName: string;
    // 当前聚合marker图层分类类型
    markType: string;
    // 建个点位model放在此处
    ext: MapPointModel;
}
export interface CircleContainTextParams{
    MapPointLat: number;
    MapPointLon: number;

    labelContent:string;
    labelColor:string;
    labelFontSize:number;

    circleFillColor:string;
    circleRadius:number;
}
export interface PolylineStyle extends _PolylineStyle{
}

export interface PolygonStyle extends _PolygonStyle{
}

export interface CircleStyle extends _CircleStyle {
}

export interface LabelStyle extends _LabelStyle {
}

export interface OverlayLayerOptsEx extends  OverlayLayerOpts {
    mouseover?: (marker: ClusterMarkerEx)=>void;
    mouseout?: (marker: ClusterMarkerEx)=>void;
    click?: (marker: ClusterMarkerEx)=>void;
    clusteronmouseover?: Function;
    getUrl?: (count: number, marker: ClusterMarkerEx)=>string;
    getImageSize?: (count: number, marker: ClusterMarkerEx)=>{width: number,height: number};
    getContent?: Function;
    getRotation?: (count: number, marker: ClusterMarkerEx)=>number;
    maxZoom?: number;
    minZoom?: number;
    fontColor?: string;
    clusterClickModel?: string;
    selectZoom?: number;
    distance?: number;
    minClusterCount?: number;
    getBackGroundColor?: (marker: ClusterMarkerEx)=> string;
    getCustomLabelOffset?: (marker: ClusterMarkerEx)=> {width: number, height: number};
    clusterclick?: Function;
}

export interface INPGisMapMain {
    /**
     * 初始化地图
     * @param mapId 地图对象所在dom的id属性(id应为唯一)
     * @param mapConfig 地图初始化参数 
     */
    init(mapId: string, mapConfig: MapConfigModel): void;
    /**
     * 销毁地图
     */
    destroy(): any;
    /**
     * 开始布点
     * @param markerId
     */
    startLocate(msg: string): Promise<NPMapLib.Geometry.Point>;
    /**
     * 定位
     * @param markerId
     */
    locationMarker(objectId: string, zoom?: number): boolean;

    /**
     * 渲染点位到地图上
     * @param points
     */
    renderMarkers(points: Array<MapPointModel>, opts?: OverlayLayerOptsEx, notCluster?: boolean): any;

    /**
     * 渲染单个单位到地图中
     * @param point
     */
    renderMarker(point: MapPointModel): any;

    /**
     * 渲染点位到地图上
     * @param points
     */
    renderAddMarkers(points: Array<MapPointModel>, opts?: OverlayLayerOptsEx, notCluster?: boolean): any;
    /**
     * 渲染点位到地图上
     * @param points
     */
    addMarkers(points: Array<MapPointModel>, opts?: OverlayLayerOptsEx): any;

    /**
     * 移除指定的点位
     * 实际上是使用的ObjectID字段来匹配点位，再移除
     * @param point
     */
    removeMarker(objectId: string): any;
    /**
     * 移除指定的点位
     * 实际上是使用的ObjectID字段来匹配点位，再移除
     * @param point
     */
    removeMarker1(objectId: string): any;


    /**
     * 创建infoWindow
     */
    createInfoWindow(lon: number, lat: number, infoWindowOpt?: InfoWindowOpts): string;

    /**
     * 根据坐标打开地图弹出框
     * @param winId win编号
     * @param domHtml 弹出的内容的html
     * @param eventOpt 窗口的事件回调(open,close,hide)
     * @return 返回infoWindow的唯一编码
     */
    openInfoWindow(winId: string, domHtml: HTMLDocument, eventOpt?: InfoWindowEvent): void;

    /**
     * 关闭悬浮窗口
     * @param winId
     */
    closeInfoWindow(winId: string): void;

    /*--------地图基本操作---------*/
    /**
     * 量距
     */
    measureDistance(): void;

    /**
     * 量面
     */
    measureArea(): void;

    /**
     * 清除量算
     */
    cancelMeasure(): void;

    /**
     * 画线选择点位
     * @params callBackMethod: (points)=>void 返回选中的地图点位数据 TODO 暂无实现此回调函数
     * @params style 绘制完成后线段在地图上的展示样式
     */
    selectLine(callBackMethod?: (points: Array<MapPointModel>)=>void, style?: PolylineStyle): void;

    /**
     * 画多边形选择点位
     * @params callBackMethod: (points)=>void 返回选中的地图点位数据
     * @params style 绘制完成后多边形在地图上的展示样式
     */
    selectPolygon(callBackMethod?: (points: Array<MapPointModel>)=>void, style?: PolygonStyle): void;

    /**
     * 画矩形选择点位
     * @params callBackMethod: (points)=>void 返回选中的地图点位数据
     * @params style 绘制完成后矩形在地图上的展示样式
     */
    selectRectangle(callBackMethod?: (points: Array<MapPointModel>)=>void, style?: PolygonStyle): void;

    /**
     * 画圆选择点位
     * @params callBackMethod: (points)=>void 返回选中的地图点位数据
     * @params style 绘制完成后圆形在地图上的展示样式
     */
    selectCircle(callBackMethod?: (points: Array<MapPointModel>)=>void, style?: CircleStyle): void;
    /**
     * 画线
     * @param callBackMethod: 返回点位数组
     * @params style 绘制完成后圆形在地图上的展示样式
     */
    drawLine(callBackMethod?: (points: Array<NPMapLib.Geometry.Point>)=>void, style?: PolylineStyle): void;
    /**
     * 画矩形
     * @param callBackMethod 返回点位数组
     * @params style 绘制完成后圆形在地图上的展示样式
     */
    drawRectangle(callBackMethod?: (points: Array<NPMapLib.Geometry.Point>)=>void, style?: PolygonStyle): void;
    /**
     * 画圆
     * @param callBackMethod 返回点位数组
     * @params style 绘制完成后圆形在地图上的展示样式
     */
    drawCircle(callBackMethod?: (center: NPMapLib.Geometry.Point, radius: number)=>void, style?: CircleStyle): void;

    /**
     * 画多边形
     * @param callBackMethod 返回点位数组
     * @params style 绘制完成后圆形在地图上的展示样式
     */
    drawPolygon(callBackMethod?: (points: Array<NPMapLib.Geometry.Point>)=>void, style?: PolygonStyle): void;

    /**
     * 直径画圆
     * TODO 未实现
     */
    drawCircleByDiameter(callBackMethod?: Function): void;

    /**
     * 取消绘制1
     */
    cancelDraw(): void;

    /**
     * 清除所有在地图上绘制的图形
     */
    clearDraw(): void;

    /**
     * 获取所有在地图上绘制的图形
     */
    getSelectGeoJson(): any;

    /**
     * 绘制区域
     */
    setSelectGeoJson(json:string): any;

    /**
     * 圆搜索
     */
    addCircleSearchControl(): void;

    /**
     * 取消圆搜索
     */
    removeCircleSearchControl(): void;

    /**
     * 根据聚合点位类型, 设置点位的隐藏和显示
     * @param layerType
     * @param visible
     */
    setClusterMarkerVisibleByLayerType(layerType: string, visible: boolean): void;

    /**
     * 创建轨迹分析实例
     * @param points 轨迹线的点位
     * @param opts 一些初始化或者事件绑定函数
     * @return 轨迹分析线段对应唯一id
     */
    createTraceAnalyze(points: Array<MapPointModel>, opts?: TraceAnalyzeOpts): string;

    /**
     * 启动轨迹分析
     * @param lineId
     */
    startTraceAnalyze(lineId: string):void;

    /**
     * 停止轨迹分析
     * @param lineId
     */
    stopTraceAnalyze(lineId: string):void;

    /**
     * 暂停轨迹分析
     * @param lineId
     * @param flag true(暂停)/false(继续)
     */
    pauseTraceAnalyze(lineId: string, flag: boolean):void;

    /**
     * 重新启动轨迹分析实例
     * 对于已经clear的轨迹分析对象无效
     * @param lineId
     */
    restartTraceAnalyze(lineId: string):void;

    /**
     * 清除轨迹分析线段
     * @param lineId
     */
    clearTraceAnalyze(lineId: string):void;

    /**
     * 设置轨迹分析线速度
     * @param lineId
     * @param speed
     */
    setTraceAnalyzeSpeed(lineId: string, speed: number):void;

    /**
     * 将摄像机类型数组转换为地图点位类型
     */
    convertCameraArr2MapPoint(datas: Array<CameraEx>): Array<MapPointModel>;
    /**
     * 将摄像机类型转换为地图点位类型
     */
    convertCamera2MapPoint(data: CameraEx): MapPointModel;

    /**
     * 用于将systempoint转换为地图点位model
     * @param data
     * @param getNameFunc
     */
    convertSystemPoint2MapPoint(data: SystemPointEx, getNameFunc?: (model: SystemPointEx)=>string): MapPointModel;
    /**
     * 用于将systempoint数组转换为地图点位model
     * @param data
     * @param getNameFunc: 由于转换时需要获取Name, 故通过此方法在业务层返回Name
     */
    convertSystemPointArr2MapPoint(datas: Array<SystemPointEx>, Func?: (model: SystemPointEx)=>string): Array<MapPointModel>;
    /**
     * 用于将systempoint数组转换为地图点位model(场所)
     * @param data
     * @param getNameFunc: 由于转换时需要获取Name, 故通过此方法在业务层返回Name
     */
    convertSystemPointArr3MapPoint(datas: Array<SystemPointEx>, Func?: (model: SystemPointEx)=>string): Array<MapPointModel>;
    /**
     * 转换地图点位为摄像机对象
     * @param origins
     */
    convertMapPointArr2Camera(origins: Array<MapPointModel>): Array<CameraEx>;
    /**
     * 转换地图点位为摄像机对象
     * @param origin
     */
    convertMapPoint2Camera(origin: MapPointModel): CameraEx;

    test(point1: Point, point2: Point): void;
    /**
     * 矢量影像切换，显示影像图出层
     */
    showSattilateLayer(): void;
    /**
     * 矢量影像切换，显示矢量图出层
     */
    showVectorLayer(): void;
    /**
     * 像素坐标转点位坐标
     * @param pixel
     */
    pixelToPoint(pixel: NPMapLib.Geometry.Pixel): NPMapLib.Geometry.Point;


    /** 渲染圈画 包含文字 圆区域
     *
     * @time: 2017-11-14 15:50:49
     * @params:
     * @return: Array<NPMapLib.Overlay> 已渲染 内容
     */
    renderCircleContainTexts(circleContainTexts:Array<CircleContainTextParams>):void;
    /** 手动移除
     *
     * @time: 2017-11-14 15:50:49
     * @params: Array<NPMapLib.Overlay> 已渲染 内容
     */
    removeCircleContainTexts():void;

    /**
     *  init/render heatMap
     * @time: 2017-11-27 13:46:58
     * @params:
     * @return:
     */
    renderHeatMap(dataList:Array<{lon:number,lat:number,count:number}>,radius:number):void
    /**
     *  show heat map that has  rendered
     * @time: 2017-11-27 13:46:58
     */
    showHeatMap():void;
    /**
     *  hide heat map that has rendered
     * @time: 2017-11-27 13:46:58
     */
    hideHeatMap():void;

    updateSize(): void;

    getSize(): NPMapLib.Geometry.Pixel;

    zoomToExtend(extent: NPMapLib.Geometry.Extent): void;
    zoomToPoints(points: Array<MapPointModel>): void;
}