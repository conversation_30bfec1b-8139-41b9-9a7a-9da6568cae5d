<template>
  <ui-modal v-model="visible" title="关联标签" :styles="styles">
    <div class="content-wrap">
      <div class="right-module">
        <div class="search-module betw-flex">
          <span class="base-text-color">
            标签列表
            <span class="font-red">{{ tagData.length }}</span>
            条
          </span>
          <Input
            v-model="tagName"
            placeholder="请输入标签名称"
            search
            class="width-md ml-lg"
            @on-search="searchTag"
          ></Input>
        </div>
        <div class="tag-list" v-ui-loading="{ loading: tagLoading, tableData: tagData }">
          <div>
            <Checkbox
              class="ml-sm mb-sm"
              v-for="(item, index) in tagData"
              :key="index"
              :label="item.tagId"
              v-model="item.checked"
              @on-change="checkTag($event, item)"
            >
              <span class="tag-item ellipsis" :title="item.tagName">
                {{ item.tagName }}
              </span>
            </Checkbox>
          </div>
        </div>
        <div class="preview-tag">
          <ui-tag v-for="(item, index) in checkedTagData" @close="handleCloseTag(index)" :closeable="true" :key="index">
            {{ item.tagName }}
          </ui-tag>
        </div>
      </div>
    </div>
    <template #footer>
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" @click="query" :loading="queryloading" class="plr-30"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import taganalysis from '@/config/api/taganalysis';
export default {
  props: {
    value: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '5rem',
      },
      queryloading: false,
      tagName: '',
      initTagData: [],
      tagData: [],
      tagLoading: false,
      checkedTagData: [],
    };
  },
  created() {},
  methods: {
    // 查询所有标签
    async initTagList() {
      try {
        this.tagLoading = true;
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagData = res.data.data.map((row) => {
          this.$set(row, 'checked', false);
          return row;
        });
        this.initTagData = this.tagData;
      } catch (err) {
        console.log(err);
      } finally {
        this.tagLoading = false;
      }
    },
    searchTag() {
      this.tagLoading = true;
      let searchArr = [];
      if (this.tagName !== '') {
        for (let i = 0, len = this.initTagData.length; i < len; i++) {
          let str = this.initTagData[i].tagName;
          if (str.indexOf(this.tagName) !== -1) {
            searchArr.push(this.initTagData[i]);
          }
        }
        this.tagData = searchArr;
      } else {
        this.tagData = this.initTagData;
      }
      this.tagLoading = false;
    },
    checkTag(isCheck, item) {
      if (isCheck) {
        this.checkedTagData.push(item);
      } else {
        const index = this.checkedTagData.findIndex((row) => row.tagId === item.tagId);
        this.checkedTagData.splice(index, 1);
      }
    },
    handleCloseTag(index) {
      let tag = this.tagData.find((row) => row.tagId === this.checkedTagData[index].tagId);
      this.$set(tag, 'checked', false);
      this.checkedTagData.splice(index, 1);
    },
    query() {
      if (!this.checkedTagData.length) {
        this.$Message.error('请选择标签');
        return;
      }
      this.$emit('query', this.queryloading, this.checkedTagData);
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.initTagList();
        this.checkedTagData = [];
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiModal: require('@/components/ui-modal.vue').default,
    UiTag: require('@/components/ui-tag').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 20px 0 0 0;
}
.mr-tp20 {
  margin-top: 20px;
}
.flex-1 {
  flex: 1;
}
.betw-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content-wrap {
  width: 100%;
  height: 750px;
  display: flex;
}
.right-module {
  border-top: 1px solid var(--border-modal-footer);
  width: 100%;
  .search-module {
    padding: 20px;
    @{_deep} .ivu-input-icon {
      color: var(--color-active) !important;
    }
  }
  .tag-list {
    width: 100%;
    height: 300px;
    padding: 0 20px 20px;
    overflow-x: hidden;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-modal-footer);
    .tag-item {
      display: inline-block;
      vertical-align: middle;
      width: 80px;
    }
  }
  .preview-tag {
    padding: 20px;
    height: 325px;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
</style>
