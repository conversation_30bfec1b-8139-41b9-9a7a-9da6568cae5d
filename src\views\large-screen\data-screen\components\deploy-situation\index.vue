<!--
    * @FileDescription: 数据专题大屏-布控态势
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="screen-content">
        <div class="screen-left">
            <card title="布控应用频次排名" class="screen-box">
                <div class="ranking">
                    <div class="ranking-no2">
                        <img :src="getImgUrl('icon-no2.png')" alt="">
                        <p class="ranking-name">{{ frequencyList[1].projectName }}</p>
                        <count-to :start-val="0" :end-val="frequencyList[1].projectValue" :duration="1000" class="ranking-num"></count-to>
                    </div>
                    <div class="ranking-no1">
                        <img :src="getImgUrl('icon-no1.png')" alt="">
                        <p class="ranking-name">{{ frequencyList[0].projectName }}</p>
                        <count-to :start-val="0" :end-val="frequencyList[0].projectValue" :duration="1000" class="ranking-num"></count-to>
                    </div>
                    <div class="ranking-no3">
                        <img :src="getImgUrl('icon-no3.png')" alt="">
                        <p class="ranking-name">{{ frequencyList[2].projectName }}</p>
                        <count-to :start-val="0" :end-val="frequencyList[2].projectValue " :duration="1000" class="ranking-num"></count-to>
                    </div>
                </div>
                <div class="table-list">
                    <div class="td-li" v-for="(item, index) in frequencyList.slice(3,8)" :key="index">
                        <div class="td-index">{{ item.rankIndex }}</div>
                        <div class="td-name">{{ item.projectName }}</div>
                        <div class="td-num">
                            <count-to :start-val="0" :end-val="item.projectValue" :duration="1000" class="data-box-num"></count-to>
                        </div>
                    </div>
                </div>
            </card>
            <card title="布控任务统计" class="screen-box">
                <statis-chart ref="statisChart"></statis-chart>
            </card>
            <card title="布控库统计" class="screen-box">
                <ul class="bank-ul">
                    <li class="bank-li" v-for="(item, index) in bankList" :key="index">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <p class="bank-li-name">{{ item.name }}</p>
                        <p class="bank-li-num">
                            <count-to :start-val="0" :end-val="item.bankNum" :duration="1000" class="bank-num"></count-to>
                            <span>个</span></p>
                        <div class="bank-li-line"></div>
                        <count-to :start-val="0" :end-val="item.num" :duration="1000" class="bank-li-total dinpromini"></count-to>
                    </li>
                </ul>
            </card>
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        布控战果转化
                    </div>
                    <div class="view-box-contnet">
                        <div class="box-content-left">
                            <p class="box-title">布控战果总量</p>
                            <count-to :start-val="0" :end-val="deployData.totalResultNum" :duration="1000" class="bank-li-total dinpro"></count-to>
                        </div>
                        <div class="box-content-center">
                            <p class="box-title">布控任务总量</p>
                            <count-to :start-val="0" :end-val="deployData.totalTaskNum" :duration="1000" class="bank-li-total dinpro"></count-to>
                        </div>
                        <div class="box-content-right">
                            <div class="data-percent">{{deployData.convertRate || 0}}%</div>
                            <p>战果转化率</p>
                        </div>
                    </div>
                </div>
                <div class="content-box-right view-box">
                    <div class="title-img bigtitle">
                        布控战果统计
                    </div>
                    <month-chart ref="monthchart"></month-chart>
                </div>
            </div>
            <map-chart ref="mapChart" class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="多维布控占比" class="screen-box">
                <pie-chart ref="pieChart" :picData="costList" :urlType="3"></pie-chart>
            </card>
            <card title="布控时段排名" class="screen-box" :padding="0">
                <rangking-chart ref="rangkingChart" :rangData="rangData"></rangking-chart>
            </card>
            <card title="警情响应及时度" class="screen-box">
                <div class="table">
                    <div class="table-header">
                        <div class="table-column-pm">排名</div>
                        <div class="table-column-fxj">分县局</div>
                        <div class="table-column-xysc">平均响应时长(小时)</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in timeList" :key="index">
                            <div class="table-column-pm">
                                <img v-if="index<3" :src="getImgUrl('ranking-'+index+'.png') " alt="">
                                <span v-else>{{ item.ranking }}</span>
                            </div>
                            <div class="table-column-fxj">{{ item.projectName }}</div>
                            <div class="table-column-xysc">
                                <p class="time time-red" v-if="item.type == 1">{{ item.time }}h</p>
                                <p class="time time-greed" v-if="item.type == 2">{{ item.time }}h</p>
                                <img :src="item.type == 1 ?getImgUrl('down.png') : getImgUrl('up.png')" alt="">
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
        </div>
    </div>
</template>
<script> 
import { controlAppFrequency, 
    controlLibraryStat,
    controlResultStat,
    controlTaskStat, 
    regionStat,
    alarmResponseTimeStat } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import statisChart from './statis-chart.vue';
import monthChart from './month-tendency-chart.vue';
import pieChart from './pie-chart.vue';
import mapChart from './map-chart.vue';
import timeFrameChart from './time-frame-chart.vue';
import rangkingChart from './rangking-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        statisChart,
        monthChart,
        pieChart,
        mapChart,
        timeFrameChart,
        rangkingChart,
        CountTo
    },
    data() {
        return {
            perIndex: 0,
            dataList: [],
            frequencyList: [
                { index: 4, projectName: 'XXX分局', projectValue: 100234 },
                { index: 5, projectName: 'XXX分局', projectValue: 93234 },
                { index: 6, projectName: 'XXX分局', projectValue: 83234 },
                { index: 7, projectName: 'XXX分局', projectValue: 73234 },
                { index: 8, projectName: 'XXX分局', projectValue: 63234 },
            ],
            bankList: [
                { name: '人员布控库', bankNum: 8, num: 24671, imgUrl: 'icon_face.png', type: 'face' },
                { name: '车辆布控库', bankNum: 4, num: 4671, imgUrl: 'icon_vehicle.png', type: 'vehicle' },
                { name: 'ETC布控库', bankNum: 2, num: 3671, imgUrl: 'icon_etc.png', type: 'etc'},
                { name: 'WIFI布控库', bankNum: 2, num: 3622, imgUrl: 'icon_wifi.png', type: 'wifi' },
                { name: 'RFID布控库', bankNum: 5, num: 55671, imgUrl: 'icon_rfid.png', type: 'rfid' },
                { name: '电磁布控库', bankNum: 3, num: 671, imgUrl: 'icon_electr.png', type: 'electric' },
            ],
            costList: [
                // { value: '34', name: '人像布控' },
                // { value: '29', name: '机动车布控' },
                // { value: '12', name: '感知数据布控' },
                // { value: '25', name: '多维数据布控' },
            ],
            rangData: [],
            particulList: [
                { value: '42', name: '有效' },
                { value: '25', name: '无效' },
                { value: '33', name: '未处理' },
            ],
            recomList: [
                { value: '26', name: '有效' },
                { value: '38', name: '无效' },
                { value: '36', name: '未处理' },
            ],
            timeList: [
                { ranking: 1, projectName: 'XXX分局', time: '0.4', type: 1, rankingUrl: 'ranking-0.png'},
                { ranking: 2, projectName: 'XXX分局', time: '0.8', type: 2, rankingUrl: 'ranking-1.png'},
                { ranking: 3, projectName: 'XXX分局', time: '1', type: 1, rankingUrl: 'ranking-2.png'},
                { ranking: 4, projectName: 'XXX分局', time: '1.2', type: 2},
                { ranking: 5, projectName: 'XXX分局', time: '1.5', type: 2},
                { ranking: 6, projectName: 'XXX分局', time: '2', type: 1},
                { ranking: 7, projectName: 'XXX分局', time: '1.9', type: 2},
            ],
            deployData: { }
        }
    },
    created() {
        this.init();
        this.queryMap();
    },
    methods: {
        init() {
            controlAppFrequency()
            .then(res => {
                // { index: 4, name: 'XXX分局', num: 100234 },
                this.frequencyList = [];
                res.data.forEach((item, index) => {
                    this.frequencyList.push(
                        { ...item, rankIndex: index+1, projectValue: Number(item.projectValue) }
                    )
                })
            })
            controlLibraryStat()
            .then(res => {
                this.bankList.forEach(item => {
                    if(res.data[item.type]) {
                        item.num = res.data[item.type].dataCount;
                        item.bankNum = res.data[item.type].libCount;
                    }
                })
            })
            controlTaskStat()
            .then(res => {
                this.costList = [];
                res.data.taskTypeRank.forEach(item => {
                    this.costList.push({
                        value: item.rate, name: item.taskTypeDesc
                    })
                })
                this.$refs.statisChart.init(res.data)
                this.$refs.pieChart.init(this.costList);
                this.rangData = res.data.timeTypeRank;
                this.$refs.rangkingChart.init(res.data.timeTypeRank);
                this.$forceUpdate();
            })
            alarmResponseTimeStat()
            .then(res => {
                res.data.forEach((item,  index) => {
                    this.timeList[index].projectName = item.projectName;
                    this.timeList[index].time = item.projectValue
                })
                this.$forceUpdate();
            })
            controlResultStat()
            .then(res => {
                this.deployData = res.data;
                this.$refs.monthchart.init(res.data.controlResultStat)
            })
        },
        queryMap() {
            regionStat(2)
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        },
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    }
}
</script>
<style lang='less' scoped>
@import "../common/style.less";
.screen-content{
    display: flex;
    width: 100%;
    height: 100%;
    .screen-left, 
    .screen-right{
        width: 420px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .screen-box{
            height: calc( ~'33% - 5px');
            position: relative;
            .bank-ul{
                height: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                .bank-li{
                    width: 33%;
                    height: 49%;
                    // background: rgba(24, 98, 187, 0.1);
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    &:nth-child(1){
                        background-position: 5px -10px;
                        background:url('~@/assets/img/screen/icon_face.png') no-repeat, rgba(24, 98, 187, 0.1);
                    }
                    &:nth-child(2){
                        background-position: 6px -8px;
                        background:url('~@/assets/img/screen/icon_vehicle.png') no-repeat, rgba(24, 98, 187, 0.1);
                    }
                    &:nth-child(3){
                        background-position: 4px -13px;
                        background:url('~@/assets/img/screen/icon_etc.png') no-repeat, rgba(24, 98, 187, 0.1);
                    }
                    &:nth-child(4){
                        background-position: 5px -16px;
                        background:url('~@/assets/img/screen/icon_wifi.png') no-repeat, rgba(24, 98, 187, 0.1);
                    }
                    &:nth-child(5){
                        background-position: 6px -8px;
                        background:url('~@/assets/img/screen/icon_rfid.png') no-repeat, rgba(24, 98, 187, 0.1);
                    }
                    &:nth-child(6){
                        background-position:5px -16px;
                        background:url('~@/assets/img/screen/icon_electr.png') no-repeat, rgba(24, 98, 187, 0.1);
                    }
                    .bank-li-name{
                        font-weight: 400;
                        color: #ffffff;
                        font-size: 14px;
                        margin: 6px 0 0px;
                    }
                    .bank-li-num{
                        .bank-num{
                            font-weight: 700;
                            color: #F1FCFF;
                            font-size: 30px;
                            font-family: 'DINPro';
                            text-shadow: 0px 0px 10px #0988FF;
                        }
                        span{
                            font-weight: 400;
                            color: #B6CBD5;
                            font-size: 12px; 
                        }
                    }
                    .bank-li-line{
                        width: 90px;
                        height: 1px;
                        background: linear-gradient(90deg, rgba(255,255,255,0) 1%, #FFFFFF 49%, rgba(255,255,255,0) 100%);
                    }
                    .bank-li-total{
                        // font-weight: 500;
                        font-size: 20px;
                        color: #03A9FF;
                        // font-family: 'DINPro';
                        // font-style: normal;
                        // text-transform: none;
                    }
                } 
            }
            .data-box-ul{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                height: 100%;
                overflow-y: auto;
            }
            .data-box-ul ::-webkit-scrollbar {
                width: 1px;
            }
            .data-box{
                background: rgba(24, 98, 187, 0.1);
                width: 195px;
                height: calc( ~'50% - 5px');
                padding: 10px 10px 5px 10px;
                margin-bottom: 10px;
                .data-box-top{
                    display: flex;
                    justify-content: space-between;
                    .box-top-left{
                        .data-box-name{
                            font-size: 14px;
                            font-weight: 400;
                            color: #ffffff;
                        }
                        .data-box-num{
                            font-family: 'DINPro';
                            font-size: 20px;
                            color: #03A9FF;
                        }
                    }
                    .icon-type{
                        width: 30px;
                        height: 30px;
                    }
                }
                .data-box-add{
                    font-size: 12px;
                    font-weight: 400;
                    color: #ffffff;
                    margin-top: 3%;
                }
                .box-add{
                    display: flex;
                    .box-add-left{
                        width: 100px;
                        font-size: 14px;
                        color: #2DDF5C;
                    }
                    .box-add-right{
                        font-size: 14px;
                        color: #2DDF5C;
                        display: flex;
                        align-items: center;
                        img{
                            width: 12px;
                            height: 14px;
                            margin-left: 10px;
                        }
                    }
                }
            }
            .data-box:last-child{
                margin-bottom: 0;
            }
            .data-box:nth-last-child(2){
                margin-bottom: 0;
            }
            .table{

                .table-header{
                    display: flex;
                    font-weight: 400;
                    color: #ffffff;
                    font-size: 14px;
                    justify-content: space-between;
                    padding: 0 15px;
                    background: linear-gradient(180deg, rgba(7, 32, 70, 0) 0%, #064096 100%); 
                    .table-column-pm{
                        width: 50px;
                    }
                    .table-column-fxj{
                        width: 40%;
                    }
                    .table-column-xysc{
                        width: 40%;
                    }
                }
                .table-content{
                    
                    .table-row{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        // border-bottom: ;
                        &:nth-child(odd){
                            padding: 3px 0;
                            background: linear-gradient(90deg, rgba(6,55,131, 0) 0%, rgba(6,55,131,0.5) 51%, rgba(6,55,131,0) 100%);
                        }
                        .table-column-pm{
                            width: 50px;
                            text-align: center;
                            font-size: 14px;
                            font-weight: 700;
                            color: #03ECF6;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .table-column-fxj{
                            width: 40%;
                            font-size: 14px;
                            font-weight: 400;
                            color: #D0DDE7;
                            
                        }
                        .table-column-xysc{
                            width: 40%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            .time{
                                width: 40px;
                                margin-right: 10px;
                                font-size: 16px;
                                font-weight: 700;
                            }
                            .time-red{
                                color: #F76E38;
                            }
                            .time-greed{
                                color: #2DDF6C;
                            }
                            img{
                                width: 12px;
                                height: 14px;
                            }
                        }
                    }
                    
                }
            }
        }
    }
    .screen-main{
        flex: 1;
        position: relative;
        padding: 10px 20px 0 20px;
        height: 100%;
        background: url('~@/assets/img/screen/screen-quan.png') no-repeat;
        background-position: center;
        background-size: auto;
        display: flex;
        flex-direction: column;
        .content-top{
            display: flex;
            justify-content: space-around;
            .view-box{
                width: 411px;
                height: 170px;
                background: url('~@/assets/img/screen/midboxbg.png') no-repeat;
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 17px;
                .title-img{
                    width: 150px;
                    height: 40px;
                    position: absolute;
                    background: url('~@/assets/img/screen/midtitle.png') no-repeat;
                    left: 50%;
                    transform: translateX(-50%);
                    top: -20px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 20px;
                    color: #ffffff;
                }
                .view-box-contnet{
                    width: 100%;
                    height: 100%;
                    display: flex;
                    padding: 0 24px;
                    align-items: center;
                    justify-content: space-between;
                    .box-content-left{
                        .box-title{
                            font-weight: 400;
                            font-size: 14px;
                            color: #ffffff;
                            margin-bottom: 13px;
                        }
                        .bank-li-total{
                            font-size: 28px;
                            font-weight: 700;
                            color: #F1FCFF;
                            text-shadow: 0px 0px 10px #0988FF;
                            font-family: 'DINPro';
                        }
                    }
                    .box-content-center{
                        .box-title{
                            font-weight: 400;
                            font-size: 14px;
                            color: #ffffff;
                            margin-bottom: 13px;
                        }
                        .bank-li-total{
                            font-size: 28px;
                            font-weight: 700;
                            color: #F1FCFF;
                            text-shadow: 0px 0px 10px #0988FF;
                            font-family: 'DINPro';
                        }
                    }
                    .box-content-right{
                        width: 110px;
                        height: 110px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        background: url('~@/assets/img/screen/blue-ball.png') no-repeat;
                        .data-percent{
                            font-weight: 700;
                            font-size: 24px;
                            color: #ffffff;
                            font-family: 'DINPro';
                            font-style: normal;
                            text-transform: none;
                        }
                        p{
                            font-weight: 400;
                            font-size: 14px;
                            color: #ffffff;
                        }
                    }
                }
            }
        }
        .map-chart{
            flex:1;
        }
    }
}
</style>