<template>
  <Input
    type="text"
    v-model="searchValue"
    :placeholder="placeholder"
    class="icon-search-input big-input"
    @on-click="$emit('startSearch')"
    @on-enter="$emit('startSearch')"
  >
    <template slot="append">
      <i class="icon-font icon-sousuo f-20" @click="$emit('startSearch')"></i>
    </template>
  </Input>
</template>
<script>
export default {
  props: {
    value: {},
    placeholder: {
      default: '请输入',
    },
  },
  data() {
    return {
      searchValue: '',
    };
  },
  created() {},
  methods: {},
  watch: {
    searchValue(val) {
      this.$emit('input', val);
    },
    value: {
      immediate: true,
      handler(val) {
        this.searchValue = val;
      },
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.big-input {
  border-radius: 15px;
  @{_deep}.ivu-input {
    height: 50px;
    line-height: 50px;
  }
  @{_deep}.ivu-input-group-append {
    width: 10%;
    font-size: 16px;
  }
}
</style>
