<template>
  <div>
    <ui-label class="inline mb-sm mr-lg" label="操作类型">
      <Select class="width-md" v-model="searchData.type" placeholder="请选择操作类型" clearable>
        <Option v-for="(item, index) in optionTypeList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mb-sm mr-md" label="填报/同步人">
      <Input class="width-lg" v-model="searchData.userName" placeholder="请输入填报/同步人姓名"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-md" label="填报同步时间">
      <DatePicker
        class="width-md"
        v-model="searchData.modifyStartTime"
        type="datetime"
        placeholder="请选择开始时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'modifyStartTime')"
        :options="startTimeOption"
        confirm
      />
      <span class="ml-sm mr-sm">--</span>
      <DatePicker
        class="width-md"
        v-model="searchData.modifyEndTime"
        type="datetime"
        placeholder="请选择结束时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'modifyEndTime')"
        :options="endTimeOption"
        confirm
      />
    </ui-label>
    <ui-label class="inline mb-sm mr-md" label="审核/入库人">
      <Input class="width-lg" v-model="searchData.examineUser" placeholder="请输入审核/入库人姓名"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="审核/入库时间">
      <DatePicker
        class="width-md"
        v-model="searchData.examineStartTime"
        type="datetime"
        placeholder="请选择开始时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'examineStartTime')"
        :options="examineStartTimeOption"
        confirm
      />
      <span class="ml-sm mr-sm">--</span>
      <DatePicker
        class="width-md"
        v-model="searchData.examineEndTime"
        type="datetime"
        placeholder="请选择结束时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'examineEndTime')"
        :options="examineEndTimeOption"
        confirm
      />
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.modifyEndTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.modifyStartTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      examineStartTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.examineEndTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      examineEndTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.examineStartTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      optionTypeList: [
        { dataKey: '1', dataValue: '新增' },
        { dataKey: '2', dataValue: '修改' },
        { dataKey: '3', dataValue: '一机一档同步' },
        { dataKey: '4', dataValue: '国标同步' },
        { dataKey: '5', dataValue: '视图库同步' },
      ],
      searchData: {},
    };
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
