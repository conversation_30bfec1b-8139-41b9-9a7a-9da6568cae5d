<template>
  <div class="play-history">
    <ui-loading v-if="loading" />
    <div class="history-list">
      <Scroll
        :on-reach-bottom="handleReachBottom"
        height="100%"
        :loading-text="loadingText"
        :distance-to-edge="10"
      >
        <div
          v-for="(item, index) in historyList"
          class="history-list-item"
          :key="index"
          @dblclick="handleNodeClick(item)"
        >
          <div class="main">
            <div class="top">
              <div class="name ellipsis" :title="item.name">
                {{ item.name }}
              </div>
              <span @click.stop="">
                <Dropdown @on-click="showMore($event, item)">
                  <i class="action iconfont icon-gengduo"></i>
                  <DropdownMenu slot="list">
                    <DropdownItem :name="1">查看预案</DropdownItem>
                    <DropdownItem :name="3" v-if="item.permission == 'write'"
                      >分享预案</DropdownItem
                    >
                    <DropdownItem :name="2" v-if="item.permission == 'write'"
                      >删除预案</DropdownItem
                    >
                  </DropdownMenu>
                </Dropdown>
              </span>
            </div>
            <!-- <div class="time">{{ item.videoWallId }}</div> -->
          </div>
        </div>
      </Scroll>
    </div>

    <!-- 选择用户 -->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
    ></select-user>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { queryPlanList, deletePlan, videoWallShare } from "@/api/player";
import SelectUser from "@/components/select-modal/select-user.vue";
export default {
  components: { SelectUser },
  data() {
    return {
      historyList: [],
      loadingText: "加载中",
      pageInfo: {
        pageNumber: 1,
        pageSize: 10,
      },
      total: 0,
      loading: false,
      currentItem: "",
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      this.pageInfo.pageNumber = 1;
      queryPlanList({ ...this.pageInfo }).then((res) => {
        this.historyList = res.data;
        this.total = res.data.length;
        this.loading = false;
      });
    },
    handleReachBottom() {
      let totalPage = Math.ceil(this.total / this.pageInfo.pageSize);
      this.loadingText = "加载中";
      if (this.pageInfo.pageNumber >= totalPage) {
        this.loadingText = "已经是最后一页了";
        return;
      }
      this.pageInfo.pageNumber = this.pageInfo.pageNumber + 1;
      return queryPlanList({ ...this.pageInfo }).then((res) => {
        let list = res.data.entities;
        this.historyList = this.historyList.concat(list);
      });
    },
    showMore(name, item) {
      switch (name) {
        // 查看预案
        case 1:
          this.handleNodeClick(item);
          break;
        // 删除预案
        case 2:
          this.$Modal.confirm({
            title: "提示",
            closable: true,
            content: `确定删除该预案吗？`,
            onOk: () => {
              deletePlan(item.id).then((res) => {
                this.pageInfo.pageNumber = 1;
                this.getList();
                this.$Message.success(res.msg);
              });
            },
          });
          break;
        // 分享预案
        case 3:
          this.share(item);
          break;
      }
    },
    handleNodeClick(item) {
      this.$emit("setPlan", { ...item });
    },
    // 共享
    share(data) {
      this.currentItem = data;
      let sharedUserList = data.sharedUserList || [];
      sharedUserList.forEach((item) => {
        item.select = true;
        item.id = item.id || item.userId;
      });
      this.$refs.selectUser.show(sharedUserList);
    },
    handleUserData(list) {
      if (this.currentItem) {
        videoWallShare({
          id: this.currentItem.id,
          sharedUserIds: list.map((v) => v.id),
        }).then((res) => {
          if (res.code == 200) {
            this.currentItem.sharedUserList = list;
            this.$Message.success("分享成功");
          }
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.play-history {
  height: 100%;
  position: relative;
  .history-list {
    height: 100%;
    overflow: hidden;
    &-item {
      margin: 0 15px 10px 15px;
      display: flex;
      background-color: #f9f9f9;
      cursor: pointer;
      &.playing {
        background-color: rgba(44, 134, 248, 0.1);
      }
      &:hover {
        background-color: rgba(44, 134, 248, 0.1);
      }
      .live,
      .vod {
        color: #fff;
        writing-mode: vertical-rl;
        padding: 3px;
        letter-spacing: 3px;
      }
      .live {
        background: #2c86f8;
      }
      .vod {
        background: #f29f4c;
      }
      .color-bule {
        color: #2c86f8;
      }
      .main {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        padding: 5px;
        .top {
          display: flex;
          margin-bottom: 5px;
          font-size: 14px;
          font-weight: 400;
          i.playing-icon {
            width: 18px;
            height: 18px;
            background: url("~@/assets/img/player/playing.gif") 50% no-repeat;
          }
          .icon {
            margin: 0 10px;
          }
          .name {
            flex: 1;
            color: rgba(0, 0, 0, 0.8);
          }
          .action {
            margin: 0 10px 0 5px;
            cursor: pointer;
            color: rgba(136, 136, 136, 0.6);
          }
        }
        .time {
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
  }
  /deep/ .ivu-scroll-wrapper {
    height: 100%;
    position: initial;
    .ivu-scroll-container {
      overflow: auto;
      height: 100%;
    }
  }
}
</style>
