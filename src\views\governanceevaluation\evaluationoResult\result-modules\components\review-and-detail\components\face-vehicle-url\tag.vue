<template>
  <div class="tag" :style="{ background: tagBgColor }" :class="{ 'small': size === 'small' }">
    <span>{{ tagText }}</span>
  </div>
</template>
<script>
export default {
  props: {
    size: {
      default: 'normal',
    },
    tagBgColor: {
      default: 'var(--color-failed)',
    },
    tagText: {
      default: '不合格',
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tag {
  width: 100%;
  height: 6%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-failed);
  position: absolute;
  right: -45%;
  top: 2%;
  color: #fff;
  transform: rotate(30deg);
  font-size: 16px;
  z-index: 10;
  &.small {
    font-size: 12px;
    height: 15%;
    right: -40%;
    top: 3%;
  }
}
</style>
