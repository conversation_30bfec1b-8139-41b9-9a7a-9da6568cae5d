<template>
  <ui-modal class="look-pictute" v-model="visible" :title="title" :styles="styles" :footer-hide="true">
    <div class="content auto-fill">
      <div class="table-data auto-fill">
        <div class="fail-picture">
          <div class="fail-picture-container" v-for="(item, index) in reasonTableData" :key="index">
            <div class="fail-picture-item" @click="lookScence(index)">
              <ui-image :src="item.smallImagePath" />
            </div>
            <div class="fail-picture-date">
              <span class="icon-font icon-shijian f-12 mr-xs vt-middle"></span>
              <Tooltip class="text vt-middle ellipsis" placement="bottom" :content="item.shotTime">
                <div class="vt-middle">
                  {{ item.shotTime || '未知' }}
                </div>
              </Tooltip>
            </div>
            <div class="fail-picture-address">
              <span class="icon-font icon-dizhi f-12 mr-xs vt-middle"></span>
              <Tooltip class="text vt-middle ellipsis" placement="bottom" :content="item.address">
                <div class="checkbox-item">{{ item.address || '未知' }}</div>
              </Tooltip>
            </div>
          </div>
        </div>
        <div class="t-center" v-if="reasonTableData.length === 0">
          <div class="no-data">
            <img src="@/assets/img/equipmentfiles/look-picture.png" alt="" />
            <span class="img-text">图片已清理</span>
          </div>
        </div>
        <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
      </div>
      <ui-page class="page" transfer :page-data="reasonPage" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  components: {
    LookScene: require('@/components/look-scene').default,
  },
  data() {
    return {
      reasonTableData: [],
      visibleScence: false,
      infoObj: {},
      imgList: [],
      viewIndex: 0,
      styles: { width: '7.7rem' },
      visible: false,
      loading: false,
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
    };
  },
  methods: {
    // 初始化
    info(row) {
      this.infoObj = row || {};
      this.getReason(row);
    },
    // 过车图片接口
    async getReason() {
      try {
        let params = {
          deviceId: this.infoObj.deviceId,
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        this.loading = true;
        // let res = await this.$http.post(
        //   governanceevaluation.carForEvaluation,
        //   params
        // )
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          governanceevaluation.carForEvaluation,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
          this.global.SERVER.asset,
        );
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.imgList = this.reasonTableData.map((row) => row.bigImagePath);
        this.loading = false;
        this.visible = true;
      } catch (error) {
        console.log(error);
      }
    },
    changePage(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    changePageSize(val) {
      this.reasonPage.pageNum = 1;
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    // 查看大图
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
  .ivu-modal-body {
    padding: 0 20px;
  }
}
.look-pictute {
  .content {
    height: 700px;
    .table-data {
      height: 100%;
      .fail-picture {
        display: flex;
        flex-wrap: wrap;
        height: 100%;
        overflow-y: auto;
        .fail-picture-container {
          height: 254px;
          width: 191px;
          background: #0f2f59;
          color: #8797ac;
          padding: 15px;
          margin-right: 13px;
          margin-bottom: 15px;
          &:nth-child(7n) {
            margin-right: 0px;
          }
          .fail-picture-item {
            height: 162px;
            width: 162px;
            margin-bottom: 10px;
            cursor: pointer;
          }
          .fail-picture-date {
            line-height: 18px;
            font-size: 12px;
          }
          .fail-picture-address {
            line-height: 18px;
            font-size: 12px;
            .result-icon {
              font-size: 20px;
            }
            .text {
              width: 122px;
              display: inline-block;
              cursor: pointer;
            }
          }
        }
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .img-text {
          color: #385074;
        }
      }
    }
  }
}
.text {
  width: 122px;
  display: inline-block;
  cursor: pointer;
}
</style>
