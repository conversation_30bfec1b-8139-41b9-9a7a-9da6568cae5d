<template>
  <div class="over-flow">
    <div class="fl over-flow" v-for="(num, index) in numLength" :key="index">
      <div class="num-div number fl">
        <countTo ref="countTo" :start-val="startVal" :end-val="parseInt(numString[index])" :duration="duration">
        </countTo>
      </div>
      <span class="split fl" v-if="numLength !== index + 1 && index % 3 === (numLength - 1) % 3">,</span>
    </div>
  </div>
</template>
<style lang="less" scoped>
.num-div {
  width: 26px;
  line-height: 36px;
  border: 1px solid rgba(102, 255, 255, 0.3);
  background-image: linear-gradient(0deg, #1b4776 0%, rgba(24, 59, 96, 0.69) 100%);
  text-align: center;
  font-size: 36px;
  font-family: UniDreamLED;
  color: var(--color-bluish-green-text);
  margin-right: 4px;
}
.split {
  color: #fff;
  font-weight: 700;
  font-size: 30px;
  margin-right: 4px;
}
</style>
<script>
import countTo from 'vue-count-to';
export default {
  data() {
    return {
      numString: '0000000000',
      // 需要滚动的时间
      duration: 1000,
      // 初始值
      startVal: 0,
    };
  },
  mounted() {},
  methods: {},
  watch: {
    staticCount: {
      handler(val) {
        this.numString = val.newNum;
      },
      deep: true,
    },
  },
  computed: {
    numLength() {
      return this.staticCount.num.length;
    },
  },
  props: {
    staticCount: {
      required: true,
    },
  },
  components: {
    countTo,
  },
};
</script>
