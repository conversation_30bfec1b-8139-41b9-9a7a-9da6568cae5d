<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #search>
        <ui-label class="inline mr-lg mb-sm" label="设备编码">
          <Input v-model="formData.deviceId" clearable placeholder="请输入设备编码" class="width-md"></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="设备名称">
          <Input v-model="formData.deviceName" clearable placeholder="请输入设备名称" class="width-md"></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="检测结果">
          <Select v-model="formData.outcome" placeholder="请选择检测结果" class="width-md" clearable>
            <Option v-for="item in qualifiedList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="录像缺失时长">
          <InputNumber
            :precision="0"
            :max="Number.MAX_SAFE_INTEGER"
            v-model.number="timeFormData.hiatusTimeBegin"
            class="width-mini"
            clearable
          >
          </InputNumber>
          <span class="ml-sm mr-sm">--</span>
          <InputNumber
            :precision="0"
            :max="Number.MAX_SAFE_INTEGER"
            v-model.number="timeFormData.hiatusTimeEnd"
            class="width-mini"
            clearable
          >
          </InputNumber>
          <Select v-model="timeFormData.unit" class="width-mini ml-sm">
            <Option v-for="item in unitList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg mb-sm" label="当日录像缺失时长">
          <InputNumber
            :precision="0"
            :max="Number.MAX_SAFE_INTEGER"
            v-model.number="timeFormData.hiatusTimeCurBegin"
            class="width-mini"
            clearable
          >
          </InputNumber>
          <span class="ml-sm mr-sm">--</span>
          <InputNumber
            :precision="0"
            :max="Number.MAX_SAFE_INTEGER"
            v-model.number="timeFormData.hiatusTimeCurEnd"
            class="width-mini"
            clearable
          >
          </InputNumber>
          <Select v-model="timeFormData.unit" class="width-mini ml-sm">
            <Option v-for="item in unitList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </ui-label>
        <span class="mb-sm">
          <Button type="primary" class="mr-sm" @click="search">查询</Button>
          <Button type="default" @click="reset">重置</Button>
        </span>
      </template>
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <span class="font-active-color mr-sm pointer update-btn f-14" v-if="statisticShow" @click="updateStatistics"
            >更新统计结果</span
          >
          <Button type="primary" class="mr-sm" :disabled="isRecheck" @click="handleBatchRecheck">
            <i class="icon-font icon-piliangfujian"></i>
            {{ isRecheck ? '复检中...' : '批量复检' }}
          </Button>
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #description="{ row }">
        <span :class="row.qualified === '1' ? 'sucess' : 'error'">{{ row.description }}</span>
      </template>
      <template #reason="{ row }">
        <span class="deletion" @click="viewDetail(row)">{{ row.reason }}</span>
      </template>
      <template slot="outcome" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template slot="option" slot-scope="{ row }">
        <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
        <ui-btn-tip
          icon="icon-fujian"
          content="复检"
          class="vt-middle f-14 ml-sm"
          @click.native="clickArtificialRecheck(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
    <online-details v-model="onlineDetailShow" :get-echarts="getEcharts">
      <template #header>
        <div>
          <span class="ml-lg base-text-color">设备编码：</span>
          <span class="base-text-color">{{ deviceData.deviceId }}</span>
          <span class="ml-lg base-text-color">设备名称：</span>
          <span class="base-text-color">{{ deviceData.deviceName }}</span>
        </div>
        <div class="mt-sm">
          <span class="ml-lg base-text-color">{{ missingDate }}</span>
          <span class="ml-lg base-text-color">
            录像累计缺失
            <span class="font-warning">{{ missingTime }}</span>
          </span>
        </div>
      </template>
    </online-details>
    <recheck
      v-model="recheckVisible"
      :module-data="moduleData"
      :is-batch-recheck="isBatchRecheck"
      @handleUpdate="initAll()"
    ></recheck>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import taganalysis from '@/config/api/taganalysis';
import { normalFormData, tableColumns, iconStaticsList } from './util/enum/ReviewParticular.js';
import batchRecheckBtn from '../mixins/batchRecheckBtn';
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  mixins: [particularMixin, dealWatch, batchRecheckBtn],
  inheritAttrs: false,
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      customSearch: false,
      defaultCheckedList: [],
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      iconList: iconStaticsList,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        checkStatus: null,
        hiatusTimeCurBegin: null,
        hiatusTimeCurEnd: null,
        hiatusTimeBegin: null,
        hiatusTimeEnd: null,
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(tableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      capturePictureVisible: false,
      exportLoading: false,
      // 离线详情
      deviceData: {},
      onlineDetailShow: false,
      code: null,
      extensionData: {},
      missingDate: null,
      missingTime: null,
      statisticShow: false,
      recheckVisible: false,
      isBatchRecheck: false,
      moduleData: {},
      timeFormData: {
        unit: 's',
        hiatusTimeCurBegin: null,
        hiatusTimeCurEnd: null,
        hiatusTimeBegin: null,
        hiatusTimeEnd: null,
      },
      unitList: [
        { value: 'h', label: '时' },
        { value: 'm', label: '分' },
        { value: 's', label: '秒' },
      ],
    };
  },
  created() {
    this.copySearchDataMx(this.formData);
    this.iconList[this.iconList.length - 1].name = this.activeIndexItem.indexName;
    // 异常原因下拉列表获取
    this.MixinDisQualificationList().then((data) => {
      let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
      findErrorCodes.options = data.map((item) => {
        // 嘉鹏说: 设备模式查询需转换数字模式
        return { value: Number(item.key), label: item.value };
      });
    });
    this.getTagList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    /**
     * 格式化录像缺失时长 时间
     * 后端统一为秒 s
     * @param unit {string} 单位 h m s
     * @param time {number} 如123
     */
    formatTime(unit, time) {
      const formatMap = {
        h: (hours) => {
          if (!hours) return null;
          return hours * 3600;
        },
        m: (minutes) => {
          if (!minutes) return null;
          return minutes * 60;
        },
        s: (seconds) => {
          if (!seconds) return null;
          return seconds;
        },
      };
      return formatMap[unit](time);
    },
    initAll() {
      // 列表
      this.getTableData();
      this.getBatchRecheckState();
      // 统计获取
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    search() {
      this.startSearch(this.formData);
    },
    reset() {
      //1手动重置
      this.timeFormData = {
        unit: 's',
        hiatusTimeCurBegin: null,
        hiatusTimeCurEnd: null,
        hiatusTimeBegin: null,
        hiatusTimeEnd: null,
      };
      this.resetSearchDataMx(this.formData, this.search);
    },

    startSearch(params) {
      let { unit, hiatusTimeCurBegin, hiatusTimeCurEnd, hiatusTimeBegin, hiatusTimeEnd } = this.timeFormData;
      //2处理时间单位
      const hiatusTime = {
        hiatusTimeCurBegin: this.formatTime(unit, hiatusTimeCurBegin),
        hiatusTimeCurEnd: this.formatTime(unit, hiatusTimeCurEnd),
        hiatusTimeBegin: this.formatTime(unit, hiatusTimeBegin),
        hiatusTimeEnd: this.formatTime(unit, hiatusTimeEnd),
      };
      Object.assign(this.formData, params, hiatusTime);
      this.getTableData();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        this.totalCount = data.total;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    async viewDetail(row) {
      try {
        this.deviceData = row;
        this.code = row.civilCode;
        this.missingTime = row.reason;
        this.extensionData = JSON.parse(row.extensionData);
        this.missingDate = `${this.extensionData.slice(0, 1)[0].checkDate} 至 ${
          this.extensionData.slice(-1)[0].checkDate
        }`;
        this.onlineDetailShow = true;
      } catch (err) {
        console.log(err);
      }
    },
    async getEcharts() {
      try {
        const colors = ['var(--color-failed)', 'var(--color-success)'];
        const state = ['离线', '在线'];
        const params = {
          colors: colors,
          state: state,
          yAxis: this.extensionData.map((row) => row.checkDate),
          categoryData: this.extensionData.map((row) => {
            return {
              value: row.duration,
              rowInfo: { ...row }, // 用于处理 其他业务逻辑
              textStyle: {
                /**
                 * 如果离线时间只有一条数据
                 * 则判断state： 2报备，1在线，0离线
                 * 如果有多条记录则显示离线时间
                 *  */
                color: () => {
                  if (row.timerAxis.length === 1) {
                    if (row.timerAxis[0].state === 2) {
                      return '#2B84E2';
                    } else if (row.timerAxis[0].state === 1) {
                      return 'var(--color-success)';
                    } else {
                      return 'var(--color-failed)';
                    }
                  } else {
                    return 'var(--color-failed)';
                  }
                },
              },
            };
          }),
          data: this.dealData(colors, state),
        };
        return params;
      } catch (err) {
        console.log(err);
      }
    },
    dealData(colors, state) {
      let timeData = [];
      this.extensionData.forEach((row, index) => {
        row.timerAxis.forEach((rw) => {
          timeData.push({
            itemStyle: { color: colors[rw.state] },
            name: state[rw.state],
            /**
             * Echarts的x轴只能显示 xxxx-xx-xx xx:xx:xx
             * 这里y轴作为了日期所以年月只需要写死即可
             * 0,1,2代表y轴的索引，后两位代表x轴数据开始和结束
             *  */
            value: [
              index,
              `2020-01-01 ${rw.startTime.split(' ')[1]}`,
              `2020-01-01 ${rw.endTime.split(' ')[1]}`,
              rw.state === 0,
            ],
          });
        });
      });
      return timeData;
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
        this.$Message.success('更新成功');
        this.statisticShow = false;
      } catch (err) {
        console.log(err);
      }
    },
    handleBatchRecheck() {
      // isRecheck - 混入js
      if (this.isRecheck) return;
      this.moduleData = { ...this.activeIndexItem };
      this.isBatchRecheck = true;
      this.recheckVisible = true;
    },
    clickArtificialRecheck(row) {
      this.moduleData = { deviceId: row.deviceId, ...this.activeIndexItem };
      this.isBatchRecheck = false;
      this.recheckVisible = true;
    },
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: [],
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
    qualifiedList() {
      return Object.keys(qualifiedColorConfig).map((key) => {
        return {
          value: key,
          label: qualifiedColorConfig[key].dataValue,
        };
      });
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    exportData: require('../components/export-data').default,
    OnlineDetails:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/PlatformResult/components/online-details.vue')
        .default,
    recheck: require('../components/recheck/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
.deletion {
  text-decoration: underline;
  cursor: pointer;
  color: var(--color-failed);
}

.sucess {
  color: @color-success;
}

.error {
  color: @color-failed;
}

.calender-tip {
  width: 334px !important;
}
</style>
<style lang="less">
.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }
}
</style>
