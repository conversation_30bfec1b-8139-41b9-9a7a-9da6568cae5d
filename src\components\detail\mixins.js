import { mapMutations, mapActions, mapGetters } from "vuex";
import { queryFusionDetails } from "@/api/viewParsingLibrary";
export const cutMixins = {
  data() {
    return {
      imageNumWidth: 120,
      num: 0,
      waiting: false,
    };
  },
  computed: {
    ...mapGetters({
      getPageInfo: "countCoverage/getPageInfo",
    }),
  },
  methods: {
    ...mapMutations({ setPageInfo: "countCoverage/setPageInfo" }),
    locationPlay(index) {
      let small_pic = document.getElementById("scroll-ul");
      let present = document.querySelector("#present");
      if (index >= 11) {
        present.style.left = 11 * this.imgBoxWidth + 10 + "px";
        small_pic.style.left =
          11 * this.imgBoxWidth - this.imageNumWidth * index + "px";
      } else {
        present.style.left = 10 + index * this.imgBoxWidth + "px";
      }
    },
    // isWhole 是否是视图解析库 - 全部，默认false
    handleLeft(e, isWhole = false) {
      // 避免重复点击造成的位置计算错误
      if (this.waiting) return;
      this.checkIndex = 0; // 设置为第一个tab
      this.resetData();
      this.play(this.num - 1, "left", isWhole);
    },
    handleRight(e, isWhole = false) {
      // 避免重复点击造成的位置计算错误
      if (this.waiting) return;
      this.checkIndex = 0; // 设置为第一个tab
      this.resetData();
      this.play(this.num + 1, "right", isWhole);
    },
    // 上一页
    prePage(list) {
      let small_pic = document.getElementById("scroll-ul");
      let small_pic_left = parseInt(small_pic.offsetLeft);
      this.cutList = [...list, ...this.cutList];
      small_pic.style.left =
        small_pic_left - this.imageNumWidth * (list.length - 1) + "px";
      this.num += list.length - 1;
      this.$forceUpdate();
      this.$nextTick(() => {
        this.play(list.length - 1);
      });
    },
    // 下一页
    nextPage(list) {
      this.activeIndex = this.cutList.length - 1;
      this.cutList.push(...list);
      this.$forceUpdate();
      this.$nextTick(() => {
        this.play(this.cutList.length - list.length);
      });
    },
    // 数据重置
    resetData() {
      this.checkStatus = true;
      this.recordList = {};
    },
    play(i, arrow, isWhole) {
      this.waiting = true;
      setTimeout(() => {
        if (i >= this.cutList.length) {
          this.setPageInfo({
            startPage: this.getPageInfo.startPage,
            endPage: this.getPageInfo.endPage + 1,
          });
          this.$emit("nextPage", this.getPageInfo.endPage);
          // 全景智搜展示直接进入 -- 暂时解决方案
          if (!this.$parent.hasOwnProperty("total")) {
            this.$Message.warning("已经是最后一个了");
            this.stopWaiting();
          } else {
            // 针对分页，如分类搜索
            if (
              this.$parent.total <=
              this.getPageInfo.endPage * this.$parent.pageInfo.pageSize
            ) {
              // 右到头了，父组件的到头提示应该在这里提示，但是如果这样做，目前要改的文件很多，暂不处理
              this.stopWaiting();
            }
          }
          return;
        } else if (i < 0) {
          this.setPageInfo({
            startPage: this.getPageInfo.startPage - 1,
            endPage: this.getPageInfo.endPage,
          });
          this.$emit("prePage", this.getPageInfo.startPage);
          // 全景智搜展示直接进入 -- 暂时解决方案
          if (!this.$parent.hasOwnProperty("total")) {
            this.$Message.warning("已经是第一个了");
            this.stopWaiting();
          }
          // 针对分页，如分类搜索
          if (this.getPageInfo.startPage < 1) {
            // 左到头了，父组件的到头提示应该在这里提示，但是如果这样做，目前要改的文件很多，暂不处理
            this.stopWaiting();
          }
          return;
        }
        // 数据列表偏移量
        let small_pic = document.getElementById("scroll-ul");
        let small_pic_left = parseInt(small_pic.offsetLeft);
        let present = document.querySelector("#present");
        // 选择框偏移量
        let present_left = parseInt(present.offsetLeft);
        // this.cutList.forEach((item, index) => {
        //     small_pic.getElementsByTagName('li')[index].className = 'list-box';
        // })
        if (arrow) {
          this.activeIndex = this.activeIndex + (arrow === "left" ? -1 : 1);
        }
        if (i > this.num) {
          // 选择框到达最右边，保持不动
          // parseInt(present.offsetLeft)>= 1320
          if (parseInt(present.offsetLeft) >= this.imgBoxWidth * 11) {
            // 活动框到了最右边
            // 小图片向左
            if (
              parseInt(small_pic.offsetLeft) >
              small_pic_left - this.imageNumWidth * (i - this.num)
            ) {
              small_pic.style.left =
                parseInt(small_pic.offsetLeft) - this.imgBoxWidth + "px";
              this.num = i;
            } else {
              small_pic.style.left =
                small_pic_left - this.imageNumWidth * (i - this.num) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          } else {
            // 活动框向右
            if (
              parseInt(present.offsetLeft) <
              present_left + this.imageNumWidth * (i - this.num)
            ) {
              present.style.left =
                parseInt(present.offsetLeft) +
                (i - this.num) * this.imgBoxWidth +
                "px";
              this.num = i;
            } else {
              present.style.left =
                present_left + this.imageNumWidth * (i - this.num) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          }
        } else if (i < this.num) {
          // 选择框到达最左边，保持不动
          if (parseInt(present.offsetLeft) <= 10) {
            // 小图片整体向右
            if (
              parseInt(small_pic.offsetLeft) <
              small_pic_left - this.imageNumWidth * (i - this.num)
            ) {
              small_pic.style.left =
                parseInt(small_pic.offsetLeft) + this.imgBoxWidth + "px";
              this.num = i;
            } else {
              small_pic.style.left =
                small_pic_left - this.imageNumWidth * (i - this.num) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          } else {
            if (
              parseInt(present.offsetLeft) >
              present_left - this.imageNumWidth * (this.num - i)
            ) {
              present.style.left =
                parseInt(present.offsetLeft) -
                (this.num - i) * this.imgBoxWidth +
                "px";
              this.num = i;
            } else {
              present.style.left =
                present_left - this.imageNumWidth * (this.num - i) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          }
        }
        this.$nextTick(async () => {
          if (isWhole) {
            let res = await queryFusionDetails({
              id: this.cutList[i].id,
              indexName: this.cutList[i].indexName,
            });
            this.datailsInfo = res.data || {};
            this.datailsInfo.dataType = this.cutList[i].dataType;
          } else {
            this.datailsInfo = this.cutList[i];
          }

          // 人车关联
          this.fetchVehicleCaptureLink && this.fetchVehicleCaptureLink();
          this.fetchNonmotorCaptureLink && this.fetchNonmotorCaptureLink();
          this.stopWaiting();
        });
      }, 20);
    },
    // 防止到第一个或者最后一个的逻辑是不再触发play，导致waiting一直为true，不能再切换
    stopWaiting() {
      this.waiting = false;
    },
  },
};
