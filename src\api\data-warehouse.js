import request from "@/libs/request";
import { manager, datacenterService } from "./Microservice";

// 获取视综通用统计详细信息
export function statisticView(dataType) {
  return request({
    url: datacenterService + `/datacenter/statistic/view/${dataType}`,
    method: "get",
  });
}
//统计不同类型当日抓拍的总数top10
export function deviceAccessTop(data) {
  return request({
    url: datacenterService + `/datacenter/statistic/deviceAccessTop`,
    method: "post",
    data,
  });
}
// 实时获取抓拍类今日统计值
export function capture() {
  return request({
    url: datacenterService + `/datacenter/statistic/today/capture`,
    method: "get",
  });
}

// 人像静态库列表
export function personnelQuery(data) {
  return request({
    url: manager + "/lib/face/queryFaceLibPageList",
    method: "post",
    data,
  });
}
// 新增人像库
export function personnelAdd(data) {
  return request({
    url: manager + "/lib/face/addFaceLibInfo",
    method: "post",
    data,
  });
}
// 修改人像库
export function personnelEdit(data) {
  return request({
    url: manager + "/lib/face/motifyFaceLibInfo",
    method: "post",
    data,
  });
}
// 删除人像库
export function personnelDel(data) {
  return request({
    url: manager + "/lib/face/deleteFaceLibInfo",
    method: "post",
    data,
  });
}

// 人像库详情
export function personnelDetail(id) {
  return request({
    url: manager + "/lib/face/view/" + id,
    method: "get",
  });
}

// 数据仓库统计
export function queryDataByKeys(data) {
  return request({
    url: manager + "/dw/queryDataByKeys",
    method: "post",
    data,
  });
}

// 同步人像静态库
export function syncPersonnelLibraryApi() {
  return request({
    url: manager + "/lib/face/syncFaceLibCount",
    method: "post",
  });
}

// 启用库
export function enableFaceLib(data) {
  return request({
    url: manager + `/lib/face/enableLib`,
    method: "post",
    data,
  });
}
// 停用库
export function disableFaceLib(data) {
  return request({
    url: manager + `/lib/face/disableLib`,
    method: "post",
    data,
  });
}

/**
 * 车辆静态库
 */
// 车辆静态库列表
export function queryVehicleLibageList(data) {
  return request({
    url: manager + "/lib/vehicle/queryVehicleLibageList",
    method: "post",
    data,
  });
}
// 车辆静态库添加
export function addVehicleLib(data) {
  return request({
    url: manager + "/lib/vehicle/addVehicleLib",
    method: "post",
    data,
  });
}
// 车辆静态库修改
export function motifyVehicleLib(data) {
  return request({
    url: manager + "/lib/vehicle/motifyVehicleLib",
    method: "post",
    data,
  });
}
// 车辆静态库删除
export function deleteVehicleLib(data) {
  return request({
    url: manager + "/lib/vehicle/deleteVehicleLib",
    method: "post",
    data,
  });
}
// 车辆库详情
export function vehicleDetail(id) {
  return request({
    url: manager + "/lib/vehicle/view/" + id,
    method: "get",
  });
}

// 同步车辆静态库
export function syncVehicleLibraryApi() {
  return request({
    url: manager + "/lib/vehicle/syncVehicleLibCount",
    method: "post",
  });
}

// 启用库
export function enableVehicleLib(data) {
  return request({
    url: manager + `/lib/vehicle/enableLib`,
    method: "post",
    data,
  });
}
// 停用库
export function disableVehicleLib(data) {
  return request({
    url: manager + `/lib/vehicle/disableLib`,
    method: "post",
    data,
  });
}

/**
 * 车辆静态库类型
 */
// 类型列表
export function queryVehicleLibInfoPageList(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/queryVehicleLibInfoPageList",
    method: "post",
    data,
  });
}
// 类型列表 - 添加
export function addVehicleLibInfo(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/addVehicleLibInfo",
    method: "post",
    data,
  });
}
// 类型列表 - 编辑
export function motifyVehicleLibInfo(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/motifyVehicleLibInfo",
    method: "post",
    data,
  });
}
// 类型列表 - 删除
export function deleteVehicleLibInfo(id) {
  return request({
    url: manager + "/lib/vehicleLibInfo/deleteVehicleLibInfo/" + id,
    method: "delete",
  });
}
// 类型列表 - 批量删除
export function batchDeleteVehicleLibInfo(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/batchDeleteVehicleLibInfo",
    method: "delete",
    data,
  });
}
// 类型列表 - 详情
export function queryVehicleLibInfoList(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/queryVehicleLibInfoList",
    method: "post",
    data,
  });
}
// 类型列表 - 根据库ID删除车辆信息和特征值信息
export function deleteVehicleByLibId(id) {
  return request({
    url: manager + "/lib/vehicleLibInfo/deleteVehicleByLibId/" + id,
    method: "post",
    data,
  });
}

// 类型列表 - 导出
export function doExportVehicleData(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/doExportVehicleData",
    method: "post",
    data,
  });
}

// 类型列表 - 导入
export function importVehicleData(data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/importVehicleData",
    method: "post",
    data,
  });
}
// 设备总数
export function deviceTotal(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataStat/deviceTotal",
    method: "post",
    data,
  });
}
//数据通道数
export function dataTypeTotal(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataStat/dataTypeTotal",
    method: "post",
    data,
  });
}
// 按照数据状态分组统计
export function deviceCountGroupByDataStatus(data) {
  return request({
    url:
      datacenterService +
      "/datacenter/deviceDataStat/deviceCountGroupByDataStatus",
    method: "post",
    data,
  });
}
// 查询设备接入数据量日统计分页列表
export function deviceDataStatList(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataStat/pageList",
    method: "post",
    data,
  });
}
// 设备数据接入量下载
export function deviceDataStatDown(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataStat/down",
    method: "post",
    data,
  });
}
// 按照数据有无状态分组统计设备数
export function deviceCountGroupByHaveDataStatus(data) {
  return request({
    url:
      datacenterService +
      "/datacenter/deviceDataLastTime/deviceCountGroupByHaveDataStatus",
    method: "post",
    data,
  });
}
// 活动设备数量
export function liveDeviceNum(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataLastTime/liveDeviceNum",
    method: "post",
    data,
  });
}
// 查询设备数据最新接入时间分页列表
export function pageList(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataLastTime/pageList",
    method: "post",
    data,
  });
}

// 时间超前设备数
export function timeOverDeviceCount(data) {
  return request({
    url:
      datacenterService + "/datacenter/deviceDataLastTime/timeOverDeviceCount",
    method: "post",
    data,
  });
}

// 感知库-新增
export function sensoryAdd(data) {
  return request({
    url: manager + "/lib/sensory/add",
    method: "post",
    data,
  });
}
// 感知库-列表
export function sensoryList(data) {
  return request({
    url: manager + "/lib/sensory/pageList",
    method: "post",
    data,
  });
}
// 感知库-列表
export function sensoryQueryList(data) {
  return request({
    url: manager + "/lib/sensory/queryList",
    method: "post",
    data,
  });
}
// 感知库-更新
export function sensoryUpdate(data) {
  return request({
    url: manager + "/lib/sensory/update",
    method: "post",
    data,
  });
}
// 感知库-删除
export function sensoryDel(ids) {
  return request({
    url: manager + `/lib/sensory/remove/${ids}`,
    method: "delete",
  });
}
// 感知库-根据id查询感知库
export function getSensoryById(ids) {
  return request({
    url: manager + `/lib/sensory/view/${ids}`,
    method: "get",
  });
}
// 感知库-数据新增
export function sensoryDataAdd(data) {
  return request({
    url: manager + "/lib/sensoryData/add",
    method: "post",
    data,
  });
}
// 感知库-数据列表
export function sensoryDataList(data) {
  return request({
    url: manager + "/lib/sensoryData/pageList",
    method: "post",
    data,
  });
}
// 感知库-数据列表
export function sensoryDataQueryList(data) {
  return request({
    url: manager + "/lib/sensoryData/queryList",
    method: "post",
    data,
  });
}
// 感知库-数据更新
export function sensoryDataUpdate(data) {
  return request({
    url: manager + "/lib/sensoryData/update",
    method: "post",
    data,
  });
}
// 感知库-数据删除
export function sensoryDataDel(data) {
  return request({
    url: manager + `/lib/sensoryData/remove`,
    method: "post",
    data,
  });
}
// 启用库
export function enableSensoryLib(data) {
  return request({
    url: manager + `/lib/sensory/enableLib`,
    method: "post",
    data,
  });
}
// 停用库
export function disableSensoryLib(data) {
  return request({
    url: manager + `/lib/sensory/disableLib`,
    method: "post",
    data,
  });
}
// 获取人脸库相关的业务库库类型信息
export function getBizTypes(data) {
  return request({
    url: manager + "/lib/face/getBizTypes",
    method: "post",
    data,
  });
}
// 据人脸库业务类型查询标签信息
export function getBizLabels(bizTypeId = "") {
  return request({
    url: manager + "/lib/face/getLabel/" + bizTypeId,
    method: "get",
    data,
  });
}
// 按照结构化数据量变化状态分组统计设备数
export function deviceCountGroupByStructureDataStatus(data) {
  return request({
    url:
      datacenterService +
      "/datacenter/deviceDataStat/deviceCountGroupByStructureDataStatus",
    method: "post",
    data,
  });
}
// 设备数据接入量下载
export function deviceDataLastTimeDown(data) {
  return request({
    url: datacenterService + "/datacenter/deviceDataLastTime/down",
    method: "post",
    data,
  });
}
