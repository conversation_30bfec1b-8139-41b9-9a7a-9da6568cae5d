<template>
  <div>
    <ui-modal title="查看关联表" v-model="visible" :width="1080" :footerHide="true">
      <div class="left-div">
        <ui-table
          class="ui-table"
          :loading="loading"
          :table-columns="tableColumns"
          :table-data="tableData"
          ref="table"
          :minus-height="400"
        ></ui-table>
        <!-- <ui-page
					class="page"
					:page-data="pageData"
					@changePage="changePage"
					@changePageSize="changePageSize"
				></ui-page>-->
      </div>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped></style>
<script>
import metadatamanagement from '@/config/api/metadatamanagement';
export default {
  data() {
    return {
      visible: false,
      loading: false,
      tableColumns: [
        { type: 'index', title: '序号', width: 318 },
        { title: '表名称', key: 'tableName', width: 375 },
        { title: '表注释', key: 'remark' },
      ],
      tableData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    open(row) {
      this.visible = true;
      this.tableData = [];
      this.loading = true;
      this.infoList(row);
    },
    async infoList(row) {
      try {
        let res = await this.$http.get(metadatamanagement.managementRelateTableInfo + row.id);
        this.tableData = res.data.data || [];
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
  },
  computed: {},
  props: {},
  components: { UiTable: require('@/components/ui-table.vue').default },
};
</script>
