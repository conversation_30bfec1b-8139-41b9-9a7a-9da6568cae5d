const mixin = {
  data() {
    return {
      copySearch: null,
    };
  },
  methods: {
    // 拷贝搜索条件
    copySearchDataMx(searchData) {
      if (!this.copySearch) {
        this.copySearch = this.$util.common.deepCopy(searchData);
      }
    },
    // 重置搜索条件
    resetSearchDataMx(searchData, callback, pageData = 'pageData') {
      let copyData = this.$util.common.deepCopy(this.copySearch);
      Object.assign(searchData, copyData);
      if (searchData.hasOwnProperty('pageNumber') && searchData.hasOwnProperty('pageSize') && this[pageData]) {
        this[pageData].pageNum = searchData.pageNumber;
        this[pageData].pageSize = searchData.pageSize;
      }
      if (callback) callback();
    },
    // DatePicker选择时间后格式化
    changeTimeMx(formatTime, timeType, searchData, timeField) {
      this.$set(searchData, timeField, formatTime);
    },
  },
  computed: {},
};
export default mixin;
