export default  {
  'context': {
    'err_code': 0,
    'err_msg': 'ok'
  },
  'motor': [
    {
      'box': {
        'x': 528,
        'y': 131,
        'width': 243,
        'height': 252
      },
      'box_confidence': 0.9986220598220825,
      'box_ratio': 1,
      'box_overlap_ratio': 0,
      'headend': '车尾',
      'headend_confidence': 0.9999958276748657,
      'type': '轿车',
      'type_confidence': 1,
      'color': 'car_color_yellow',
      'color_confidence': 0.7333568930625916,
      'brand': {
        'brand': '日产',
        'brand_confidence': 0.999956488609314,
        'sub_brand': '日产-骐达',
        'sub_brand_confidence': 0.9995622038841248,
        'year': '日产-骐达-2005_2006',
        'year_confidence': 0.9971321821212769
      },
      'beltphone': [
        {
          'name': 'driver_zhu',
          'confidence': 0.7564339637756348,
          'trueValue': true,
          'value': 0
        },
        {
          'name': 'belt_zhu',
          'confidence': 0.8386519551277161,
          'trueValue': true,
          'value': 0
        },
        {
          'name': 'belt_fu',
          'confidence': 0.8036746382713318,
          'trueValue': true,
          'value': 0
        }
      ],
      'facecover': [
        {
          'name': 'drivercover',
          'confidence': 0.6577343344688416,
          'trueValue': true,
          'value': 0
        },
        {
          'name': 'others',
          'confidence': 0.6159088015556335,
          'trueValue': true,
          'value': 0
        }
      ],
      'slag': {
        'name': 'other',
        'confidence': 1.1920928955078125e-7,
        'trueValue': false,
        'value': 0
      },
      'special_type': {
        'name': 'other',
        'confidence': 0.9998949766159058,
        'trueValue': false,
        'value': 0
      },
      'feat_float': '3nvnPdBBjb0Q5wY8S+oKPZXQUr3SIXm99MgWPbe7vj08J489RxHAvdt/O729tUU+G7isvfxQWzwFOGw9mGZNPdInQL4mAjS9YyzPPeKp7L28YbK6dQi6vRZHCr52AtC80omkvXdhyrwXqmA9Pi72PT9wPL6oyXS7nCDDOpY5jjzC9c+9RuDKPCSqzz0DFNy9E6z4vcT2nDpsulY8dqGGPYgf6b3bm6Y9nxCzvUw0i73nCI2+9hVFvryvvT3KMzq9K0dgPiaxsL0u2OU9EtZZvaxBP77On+G8XhtfPabsPL4e0La7/ikLvR9FsL26oVE+nKwGvsj6f72kB7G9Qtd8Pdp3BT3qcb08BX9yvVD+a71YeGs96u/QvcrEh7xtVSO9eY1tvU0VdL2OZ+m8nMKQPT6Tn7w2pQS++Bw7uxAtrTzX2/K98IYHvUGB+z3sHmI8BgN/PUzFx7t8xVC9pt/pvaTiczyMEBE9AnmAvSdyjr2XNy894J2evS+GRTwljAo94KYUOr1pf70IA907o5AlvREXCLxd5fU9i6pCvf3Anr0oIY473MOsu8NOvTzfUSI+yx+nvWRkkj2I7Tk9UsFDPCZwsb3Yv+s7J/anvXjj1L3PHAY+biG9PUTOgT1ofP694X2YPXwSGL5ZmZk9kTIuvcavoz1TeZI95LDIPXwlZDw=',
      'feat_int8': 'HO8CCPPxCRcR6fUx6wMODNH1GeMA6d767PoNHtIAAATnBhnl4gADEOMU6u+6zxf1N+oc89H5DdH/+Os03/HqDwgF8fIO5vz28vH5EvzfAAXi+B8DD//04wMJ8e8K7QMIAPEB9v4e9O0B/wUo7BILA+oB7OYhFxDhEtsT9hQSGAOmXnpD',
      'quality': 0.9333188533782959,
      'angle': -1
    },
    {
      'box': {
        'x': 945,
        'y': 12,
        'width': 195,
        'height': 157
      },
      'box_confidence': 0.9982631206512451,
      'box_ratio': 1,
      'box_overlap_ratio': 0,
      'headend': '车头',
      'headend_confidence': 1,
      'type': '轿车',
      'type_confidence': 0.9964339733123779,
      'color': 'car_color_black',
      'color_confidence': 0.40455982089042664,
      'brand': {
        'brand': '铃木',
        'brand_confidence': 0.9244245290756226,
        'sub_brand': '铃木-吉姆尼(进口)',
        'sub_brand_confidence': 0.8956332802772522,
        'year': '铃木-吉姆尼(进口)-2012',
        'year_confidence': 0.30640944838523865
      },
      'slag': {
        'name': 'other',
        'confidence': 0.0043487548828125,
        'trueValue': false,
        'value': 0
      },
      'special_type': {
        'name': 'other',
        'confidence': 0.9995179176330566,
        'trueValue': false,
        'value': 0
      },
      'feat_float': 'Ph2tPaETPDwPvC29bPtfvZeDYr2slam8CpDhvec8gr2xALi9YnPBPbnrGb3eJCC8JinPvPYowjy2fTy9jJ+UvZzGqTvHqni9TG2uvcbmEL3pGzQ9YdmYvZkQ0L2BLva7K2RUPVqMirw9dmA9CG65PfjFET6dtgA9opmtvdx/AL1HmyC8R+fGPDqhQr6aNB49wGHDvSZUVzw4MyO+3wvlOydNAT5Ism+9+4+nPRftkL22Aba9+dySvWeOm72I4jk9UnmXPFAeTL2Qes89D4o+vSm7Kr3UJUu+yoSKPdsPvz3SpQ4+6vywvPWoYb2MCIo8Log1vvVzzrwiUc+9Wu+gvdabNr1dCYS9FhIgvi7sL73T7hS8StB2Ps07Eb5Wpx26bcz+vI+0nTyKGiQ+7vA7vHIFwLulxKc9uWHTvEzUyLtwApq9F8GAvfGjUD4+8Hm9cbRnPsztTj6s5HI9NvMAPfgADT7YbM88xztFPYas4b1iifS985kEPp2ZyjwmgfY84GOIvY413L0Cta69I8TVPLibsD2VDG+8Juj0vOL5iz2KgFE+4mk3vgwKtjzD5ja+8ZJHvOHIlDs9Z869aLSjO69jaT2jvxY9i6fWPQCemzzzRdg83Igzvf7xDb3ya2U9GZmCvSe0Er5xytM9KZAavbHADD6trvS8gb6BPSm9zjw=',
      'feat_int8': 'FQL28/L75PDqGPf++gb17gHx6/cL7ef/DfwNFyQI6/n+BtAJ6APYASDyFO7q7u0LBPQZ9fbOERcj+/IE0/rn7PXw2fb+PdwA+QQo/v8U+v/t8DPxOTMPCCMGDOTiIQYH8OXrBhX9+RE00wXT/QHnAQ4JGgQG9fgO8Nwa9yP5EAbYrXpD',
      'quality': 0.9081414937973022,
      'angle': -1
    },
    {
      'box': {
        'x': 531,
        'y': 24,
        'width': 192,
        'height': 175
      },
      'box_confidence': 0.9968745708465576,
      'box_ratio': 1,
      'box_overlap_ratio': 0.38857144117355347,
      'headend': '车头',
      'headend_confidence': 0.9549060463905334,
      'type': '其它',
      'type_confidence': 0.9344472885131836,
      'color': 'car_color_white',
      'color_confidence': 0.7100291848182678,
      'brand': {
        'brand': '其它',
        'brand_confidence': 0.9676721096038818,
        'sub_brand': '其它',
        'sub_brand_confidence': 0.9470590949058533,
        'year': '无效车头',
        'year_confidence': 0.8305933475494385
      },
      'slag': {
        'name': 'other',
        'confidence': 0.0002378225326538086,
        'trueValue': false,
        'value': 0
      },
      'special_type': {
        'name': 'other',
        'confidence': 0.9995952248573303,
        'trueValue': false,
        'value': 0
      },
      'feat_float': '8aHGPVU1aj3AbKi9okizvOMZBr20PpU9Fz7qveKdCL7Adeq9oBcrPQ7hmjyAutm5cK+Tu8YOH73C7k48AHYRu4V/wz0pgiQ+xCm9O1wzN71FLDi9C/ybvGezcb0MxQk+P58OPt0acz2KQvi9tClsvbrErj0e3Xe9KqU1vkXXY71hoLG9VFDQvW6qDr6PbxI+XdHNvPthBr0EpX09A+QjvYgY/D3q26I7bMuYPZiyST2wJho94EL4uXSvcj02jsk8XTnIvEUolL3cMLe8gPq2Oz8bFT645qk9+1rHPbqr4L2tT7m9PgDPPRTUEb7AgLk9uAVDO8jsED50jqA8QVyBPC1csL1N2cw9pBugvLSD/rzvfha+jp7OvUSqPr4MDVs98GmVvQJDQL0egRi9KrkqvkGkjr35MR69tjgNPToUPj1AEAq7JmArvlWo3b1RD3a9eA7ZPLL2YL0w0Pk9PwDkvaBZNbxwMm077D1wvQ8iArx9TKI9a2iuvZCzUT3TF9S97BxyvoNMv71hLQS9BG/mvdix+DwyE989RopOveXOjT35bjM9tPPrvVSk670c68S844pHvVpc7z0ZVQM+wbzEPYzj8jxHnKc9yHg9vnwHZT1YA5o6CsaEPYK5ZL1mO14+4ZILvtI4pr3ri0U9Bb6NPWAi+j3O9oS9S/MmPG1eOz0=',
      'feat_int8': 'GA7s+/gS497jCgQA//cDABgoAfX1/PEiIw/i8hXx0/Lq590k+vgP9h8BEwwJAA8G+u77ASUVGOXpGdwXACQEBOsZ/Pnb59EN7vX31u/3CAsA1uXxBvIf5P4A8v4U6w3mxOn45Acb9BEL4+P69B0gGAcU0Q4AEPI33uwMER/wAgv2g3pD',
      'quality': 0.5640779137611389,
      'angle': -1
    },
    {
      'box': {
        'x': 797,
        'y': 1,
        'width': 125,
        'height': 53
      },
      'box_confidence': 0.7279434204101562,
      'box_ratio': 1,
      'box_overlap_ratio': 0,
      'headend': '车头',
      'headend_confidence': 0.9999986886978149,
      'type': '轻型货车',
      'type_confidence': 0.9354537725448608,
      'color': 'car_color_white',
      'color_confidence': 0.8658122420310974,
      'brand': {
        'brand': '五十铃',
        'brand_confidence': 0.4693419635295868,
        'sub_brand': '其它',
        'sub_brand_confidence': 0.7373511791229248,
        'year': '无效车头',
        'year_confidence': 0.6935337781906128
      },
      'slag': {
        'name': 'other',
        'confidence': 0.0006880760192871094,
        'trueValue': false,
        'value': 0
      },
      'special_type': {
        'name': 'other',
        'confidence': 0.9996008276939392,
        'trueValue': false,
        'value': 0
      },
      'feat_float': 'UomHPdlEJ7xclR49bE23u4gvmD2kIrK92iIGPV6CnjxI0Gs+qE+Mu0F2tT3epoc9u5kgPkMXF76yBzC9B46KPCidtLuUTZC9P2wTvlAuqr3zKvw8k2t6vfXii703MCE+qPrfO0CIgb0ij9e8+Sj9vZ926D3bqE0848+XvE087z28iU09oiQWPPJDAr0ypK08F/nhvatsOj6WICA+gPpzvZg0MD50XCy+92ZjvfOzhT2JWEm+u9hPvbHvOL5r6Uk92BEVPt2UDD2RT4k9i3HzPUgAkb0OZA495MiyPTwaBz7J8YO77qMQPfOF9zzHBQG+ncYGPaXm6L3yHXA9L5mWvKzApzwbrk+9TxcTvQtUAD4foa09JoGwPdYQuTy0kdS9qwmSvV67Qb7qiDe+TjkQPogazjtjgAk9DYOHvSRsjDy8beK8OiJBPfFmGD5sVlC9rY2NvdpUpr0EqHu9hwsfPUsDWD4y0588hjSTPCu+6L0cnxG9xRKjPVED1j2CWC+99RhbPS8WNb0nZU69sCS7PAhLhTzsBdS8ykRevaI77zpeJj69Y9cevRp1071YLh09PKXpPVsWgb1WyHq9A7kQviuKJr2Qfaw9NLw4PIv7kD0SkcS7e3sKvcRYWDwm9Ss+GpmIPPdXYb02x1w8YW/kPDsGFj7XyqC9tK9bvPAO770=',
      'feat_int8': 'EP4J/xLqCAQ6/xYQJ9v2BP/v3OsH8e8oAfD64RwD/B0MAvgF5C4n8SvW8hDO9NIMJQgRHu4IFiH/CQfgCOQO/AX09x8VFQXm7tDTIwEI8AT5DCX07+zxCTUEBOT3FBr2DfX0BQT68wD19+YJHfDx3PYVAhL/+AMqBPIDByXs/eNcD3pD',
      'quality': 0.42311400175094604,
      'angle': -1
    },
    {
      'box': {
        'x': 550,
        'y': 3,
        'width': 189,
        'height': 70
      },
      'box_confidence': 0.6866140961647034,
      'box_ratio': 1,
      'box_overlap_ratio': 0.6407407522201538,
      'headend': '车头',
      'headend_confidence': 0.99456387758255,
      'type': '其它',
      'type_confidence': 0.9683531522750854,
      'color': 'car_color_white',
      'color_confidence': 0.9454137682914734,
      'brand': {
        'brand': '其它',
        'brand_confidence': 0.9584099054336548,
        'sub_brand': '其它',
        'sub_brand_confidence': 0.9410547614097595,
        'year': '无效车头',
        'year_confidence': 0.3702370822429657
      },
      'slag': {
        'name': 'other',
        'confidence': 0.0022430419921875,
        'trueValue': false,
        'value': 0
      },
      'special_type': {
        'name': 'other',
        'confidence': 0.9998608827590942,
        'trueValue': false,
        'value': 0
      },
      'feat_float': 'ZjwDPq1P3z3Stji9agIhPlDquLzS6Sc8G/OQvfDLYL7DdsQ9rTq9vC5qBz633l69qLrjPOUxA75sy6u97opbvP6jQbpLXwG+rJYevnTWmTxzW1a9abCrvI/QGb0ivlm9ihgkPLPyq72Ql6e8r8ZZvXfFij3lp4s9M4pZPYorO77Sphw+P4livTBX/rzJB+i8ZF0SPHNeUr09KO290Cz2PJS+ZT3OsbS9/S5/vZu+oj24vwq+BaKwPGH7m70sNVK99VcmPVEdcr0/3fQ8v/oAvTy+eb11MNO9iJqKvctl/ryuem+7XuuYvCwWAz7Rvhi+FEQFOhTDEjv3A5o9odehvchNS71zdxm92lFnvbjAFz2SF948zZOCPiRZlT04jbm9BKGTvNJMVL0+9JS9m7CHvIS9eT0Y9Ek9vsgEPoCq47wWa5k8IpCJPf+cXj57Hxu+Nup9vTrTeD1d5fs9CC5Zvbo3Ej1wSlG90oWyPcg3hb6km6y9IqKrvIZxbz1GboO9KQQUPhs7hby00SS9Goc4vUQqD70q46Y99eZyvVDZIDz0qEU+eC0kPBT6P7wMtn+9LUj9PQFJlLytagc+t/DQvIoei7tSgpM9OVrgPXRvbL0DAYq7G4UAvrOefL0rTfI9zaQ9PqMJcL0wv3Y8mgrVvcuSFD52Xeq8DIGJveN9nb0=',
      'feat_int8': 'IBv1KPsC7skY+yHzB+Dr/QDg2QTz+/fzAuv78xERDdIn8vn5AvPjBw7q8RTeBe3zCvEH+PHm7/kA/CDaAAAT7PT38gkGQRLp/PPu/A8MIfkEETfa8Q8f8wnzFr7r+w7wJPz29fgU8QIxAv7xH/wh+v8SG/L/4PEeL/ID5iT57+2hBntD',
      'quality': 0.21057499945163727,
      'angle': -1
    }
  ]
}
