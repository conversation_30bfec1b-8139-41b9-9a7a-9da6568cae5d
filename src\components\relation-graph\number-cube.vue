<template>
  <div ref="seeksRelationGrapBoxRef" class="number-cube-grahp" @click="isShowNodeMenuPanel = false">
    <SeeksRelationGraph ref="seeksRelationGraph" :options="graphOptions" :on-node-click="onNodeClick"
      :on-line-click="onLineClick" @mouseListener="mouseListener" @base64Info="base64Info"
      :on-node-expand="onNodeExpand" :on-node-collapse="onNodeCollapse">
      <!-- 自定义节点样式 -->
      <div slot="node" slot-scope="{ node }"  v-if="node.data.groupFlag">
          <!-- 关系视图 组 -->
          <div :class="{ seleced: activeNode(node) }" v-if="node.data.groupFlag">
            <!-- 车辆，人员 -->
            <div class="number-cube-grahp-node2">
              <div class="count">{{node.data.count}}</div>
              <div class="number-cube-grahp-image node-style">
                {{ node.data.text }}
              </div>
            </div>
          </div>
      </div>
      <div slot="node" slot-scope="{ node }" v-else>
        <div @contextmenu.prevent.stop="showNodeMenus(node, $event)" @click.stop="mouseDown($event, node)" :class="{
          seleced: selectNode
            .map(val => {
              return val.id
            })
            .includes(node.data.id)
        }">
          <!-- 正常实体信息 -->
          <div :class="{ seleced: activeNode(node) }">
            <!-- 车辆，人员 -->
            <div class="number-cube-grahp-node">
              <div class="number-cube-grahp-image">
                <!-- <img style="width: 100%; height: 100%; border-radius: 50%" :src="node.data.icon" alt=" " /> -->
                <ui-image style="width: 100%; height: 100%; border-radius: 50%"
                  :src="node.data.propertyIcon ? node.data.propertyIcon : node.data.icon" alt="图片" />
              </div>
              <div :title="node.data.displayField" class="number-cube-grahp-button">
                {{ node.data.displayField }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div slot="node" slot-scope="{ node }">
        <div
          @contextmenu.prevent.stop="showNodeMenus(node, $event)"
          @click.stop="mouseDown($event, node)"
          :class="{
            seleced: selectNode
              .map(val => {
                return val.id
              })
              .includes(node.data.id)
          }"
        >
          <div v-if="node.data.disableDragNode" class="suoding">
            <i class="iconfont icon-suoding"></i>
          </div>
          车辆，人员
          <div v-if="node.data.label === 'person' || node.data.label === 'car'" class="number-cube-grahp-node">
            <div class="number-cube-grahp-image">
              <img style="width: 100%; height: 100%; border-radius: 50%" :src="node.data.icon" alt="#" />
            </div>
            <div :title="node.data.name" class="number-cube-grahp-button">
              {{ node.data.name }}
            </div>
          </div>
          wifi
          <div v-else-if="node.data.label === 'wifi'" class="number-cube-grahp-node">
            <div class="number-cube-grahp-image pd2">
              <img :src="wifi" alt="#" class="img-other" />
            </div>
            <div :title="node.data.name" class="number-cube-grahp-button">
              {{ node.data.name }}
            </div>
          </div>
          联系方式
          <div v-else-if="node.data.label === 'iphone'" class="number-cube-grahp-node">
            <div class="number-cube-grahp-image pd2">
              <img :src="iphone" alt="#" class="img-other" />
            </div>
            <div :title="node.data.name" class="number-cube-grahp-button">
              {{ node.data.name }}
            </div>
          </div>
          居住地址
          <div v-else-if="node.data.label === 'adress'" class="number-cube-grahp-node">
            <div class="number-cube-grahp-image pd2">
              <img :src="adress" alt="#" class="img-other" />
            </div>
            <div :title="node.data.name" class="number-cube-grahp-button">
              {{ node.data.name }}
            </div>
          </div>
        </div>
      </div> -->
    </SeeksRelationGraph>
    <div v-show="isShowNodeMenuPanel" v-if="rightMenus.length > 0"
      :style="{ left: nodeMenuPanelPosition.x + 'px', top: nodeMenuPanelPosition.y + 'px' }"
      style="z-index: 999; position: absolute">
      <div class="graph-info-menu">
        <div v-for="item in rightMenus" :key="item.title" @click="menuClick(item.clickName)">{{ item.title }}</div>
      </div>
    </div>
    <!-- 右侧导航 -->
    <div class="tootlbar-right">
      <i class="iconfont icon-zoomin tootlbar-right-icon mb10" @click="enlarge"></i>
      <el-slider v-model="proportion" vertical height="200px" :step="2" :min="2" :format-tooltip="formatTooltip"
        @input="inputStep"> </el-slider>
      <i class="iconfont icon-zoomout tootlbar-right-icon mt10" @click="narrow"></i>
      <i class="iconfont icon-location1 tootlbar-right-icon mt6" @click="refresh"></i>
      <!-- <i class="iconfont icon-expend tootlbar-right-icon mt6"></i> -->
    </div>
  </div>
</template>

<script>
import SeeksRelationGraph from './src/index.js'
import iphone from './img/iphone.png'
import adress from './img/adress.png'
import wifi from './img/wifi.png'
// http://relation-graph.com/#/docs/graph 官方api地址
export default {
  name: 'numberCube',
  components: {
    SeeksRelationGraph
  },

  props: {
    // 关系图谱数据
    atlasList: {
      type: Object,
      default () {
        return {}
      }
    },
    rightMenus: {
      type: Array,
      default () {
        return []
      }
    },
    // 图谱配置项，默认graphOptions配置，Object.assign方式覆盖原配置
    options: {
      type: Object,
      default () {
        return {}
      }
    },
    // 是否展开收缩节点
    isHasExpand: {
      default: false
    }
  },
  data () {
    return {
      graphWidth: 0,
      graphHeight: 0,
      nodeData: {},
      type: '',
      proportion: 10,
      isShowNodeMenuPanel: false,
      nodeMenuPanelPosition: { x: 0, y: 0 }, // 定位
      currentNode: {}, // 当前右键点击节点
      iphone,
      adress,
      wifi,
      isShowCodePanel: false,
      graphOptions: {
        debug: true,
        allowSwitchLineShape: true,
        allowSwitchJunctionPoint: true,
        // 多个布局样式时默认第一个，绝大部分参数配置需要在布局方式内部设置
        'layouts': [
          {
            "label": "中心",
            "layoutName": "center",
            "layoutClassName": "seeks-layout-center",
            "distance_coefficient": .7,
          },
          {
            "label": "树状",
            "layoutName": "tree",
            "layoutClassName": "seeks-layout-center",
            "defaultJunctionPoint": "border",
            "defaultNodeShape": 0,
            "defaultLineShape": 1,
            from: 'top',
            'defaultNodeWidth': '30',
            'defaultNodeHeight': '100',
            'defaultNodeBorderWidth': 0,
            'defaultLineColor': 'rgba(0, 186, 189, 1)',
            'defaultNodeColor': 'rgba(0, 206, 209, 1)',
            'min_per_width': 40,
            'max_per_width': 70,
            'min_per_height': 100
          },
          {
            "label": "树状2",
            "layoutName": "tree",
            "layoutClassName": "seeks-layout-center",
            "defaultJunctionPoint": "border",
            "defaultNodeShape": 0,
            "defaultLineShape": 1,
            from: 'left',
            'defaultNodeWidth': '100',
            'defaultNodeHeight': '30',
            'defaultNodeBorderWidth': 0,
            'defaultLineColor': '#cccccc',
            'defaultNodeColor': '#43a2f1',
            'min_per_width': 200,
            'max_per_width': 400,
            'min_per_height': 40,
            'max_per_height': 70
          },
          {
            "label": "自动",
            "layoutName": "force",
            "layoutClassName": "seeks-layout-center",
            "defaultExpandHolderPosition": "hide",
            "defaultJunctionPoint": "border"
          }
        ],
        // "defaultExpandHolderPosition": "right",
        'defaultJunctionPoint': 'border',
        "defaultLineMarker": {
          "markerWidth": 12,
          "markerHeight": 12,
          "refX": 6,
          "refY": 6,
          "data": "M2,2 L10,6 L2,10 L6,6 L2,2"
        },
        "defaultLineWidth": 1
      },
      selectNode: [],
      selectAllNode: false, // 是否全选， ctrl+alt复选标识
    }
  },
  mounted () {
    // 监听ctrl + Alt 事件
    const that = this
    document.onkeydown = function (e) {
      if (e.ctrlKey && e.key === 'Alt') {
        that.ctrlAlt()
      }
    }
  },
  beforeDestroy () {
    document.onkeydown = ''
  },
  methods: {
    // 生成关系图谱
    async showSeeksGraph () {
      var data = this.atlasList
      data.nodes = await this.inhtmlData(this.atlasList.nodes)
      this.$refs.seeksRelationGraph.setJsonData(data, seeksRGGraph => {
        // 这些写上当图谱初始化完成后需要执行的代码
        // 获取根节点的子节点，即可获得图谱第一层中的节点
        // isHasExpand为true会有收缩展开效果
        if(this.isHasExpand){
          var level_1_nodes = seeksRGGraph.getNodeById(data.rootId).lot.childs
          level_1_nodes.forEach(thisLevel1Node => {
            this.applyCollapseStyle2Node(thisLevel1Node)
          })
          this.$refs.seeksRelationGraph.refresh()
        }
      })
    },
    applyCollapseStyle2Node (_node) { // _node的子节点将被隐藏，同时让_node右侧显示一个加号，点击后可以展开子节点
      if (_node.lot.childs.length > 0) {
        _node.lot.childs.forEach(thisChildNode => {
          thisChildNode.isShow = false
          this.applyCollapseStyle2Node(thisChildNode)
        })
        _node.expanded = false
        _node.expandHolderPosition = 'right'
      }
    },
    // 点击节点
    onNodeClick (nodeObject, $event) {
      this.selectNode = [nodeObject]
      this.selectAllNode = false
      console.log('-------------', nodeObject, $event, this.selectAllNode)
      this.$emit('onNodeClick', nodeObject)
    },
    // 点击线（文字）
    onLineClick (lineObject, $event) {
      this.$emit('onLineClick', lineObject)
    },
    // 右键点击
    showNodeMenus (nodeObject, $event) {
      this.currentNode = nodeObject
      this.isShowNodeMenuPanel = true
      this.nodeMenuPanelPosition.x = $event.clientX
      this.nodeMenuPanelPosition.y = $event.clientY
    },
    // 右键菜单点击
    menuClick (item) {
      this.$emit(item, { currentNode: this.currentNode })
    },
    // 导出图片
    downloadAsImage (format) {
      this.$refs.seeksRelationGraph.downloadAsImage(format)
    },
    // 获取图片base64图片 format :格式jpg,png
    getBase64 (format) {
      return this.$refs.seeksRelationGraph.downloadAsImage(format, true)
    },
    // 定位节点 - 唯一标识符id
    location (id) {
      this.$refs.seeksRelationGraph.focusNodeById(id)
      // 获取当前定位元素
      this.selectNode = [this.$refs.seeksRelationGraph.getNodeById(id).data]
    },
    // ----------- 工具栏右侧
    // 滑块展示
    formatTooltip (val) {
      return val * 10 + '%'
    },
    // 放大
    enlarge () {
      this.proportion += 2
      this.type = ''
      // this.$nextTick(() => {
      //   this.$refs.seeksRelationGraph.zoom(20)
      // })
    },
    // 缩小
    narrow () {
      this.proportion -= 2
      this.type = ''
      // this.$nextTick(() => {
      //   this.$refs.seeksRelationGraph.zoom(-20)
      // })
    },
    // 拖拽
    inputStep (val) {
      this.type = ''
    },
    // 刷新定位
    refresh () {
      this.proportion = 10
      this.$nextTick(() => {
        this.$refs.seeksRelationGraph.refresh()
      })
    },
    // 鼠标滚轮返回(滚轮放大缩小)
    mouseListener (e) {
      this.type = 'mouseListener'
      this.proportion = e / 10
    },
    // ctrl + 鼠标左键点击 /
    mouseDown ($event, node) {
      if ($event.button === 0 && $event.ctrlKey) {
        if (
          this.selectNode
            .map(val => {
              return val.id
            })
            .includes(node.data.id)
        ) {
          this.selectNode.map((val, index) => {
            if (val.id === node.data.id) {
              this.selectNode.splice(index, 1)
            }
          })
        } else {
          this.selectNode.push(node.data)
        }
      } else {
        this.selectNode = [node.data]
      }
      this.$emit('selectNode', this.selectNode)
    },
    //  ctrl + Alt
    ctrlAlt ($event) {
      this.selectNode = JSON.parse(JSON.stringify(this.atlasList.nodes))
      if(this.selectAllNode) {
        this.selectAllNode = false
      }else{
        this.selectNode = []
        this.selectAllNode = true
      }
      this.$emit('selectNode', this.selectNode)
    },
    // 锁定-解锁
    suoding (boolen) {
      this.$refs.seeksRelationGraph.getNodes().map(val => {
        if (
          this.selectNode
            .map(val => {
              return val.id
            })
            .includes(val.id)
        ) {
          val.disableDrag = boolen
          val.data.disableDragNode = boolen
        }
      })
      this.selectNode = []
      // this.showSeeksGraph()
    },
    // 连接分析
    lianjifenxi (data) {
      let dataId = data.map(val => {
        return val.id
      })
      var _all_lines = this.$refs.seeksRelationGraph.getLines()
      this.hiddenNodes(dataId)
      _all_lines.forEach(thisLine => {
        // 注意这里的line和json数据中link不一样，一条线（line）上可以附着多条关系(link),可以通过line.relations获取到这条线上所有的关系数据(link)
        if (!dataId.includes(thisLine.fromNode.id) || !dataId.includes(thisLine.toNode.id)) {
          thisLine.relations.map(val => {
            val.fontColor = 'rgba(44, 134, 248, 0.2)'
            val.color = 'rgba(211, 215, 222, 0.2)'
            val.lineWidth = 1
          })
        } else {
          thisLine.relations.map(val => {
            val.color = '#2c86f8'
            val.fontColor = '#2c86f8'
            val.lineWidth = 2
          })
        }
      })
    },
    // 传入需展示的node
    hiddenNodes(highLightNodeIds){
      let _all_nodes = this.getNodes()
      _all_nodes.forEach(thisNode => {
        let _isHideThisLine = false
        if (!highLightNodeIds.includes(thisNode.data['id'])) {
          _isHideThisLine = true
          thisNode.disableDrag = true
          thisNode.data.disableDragNode = true
          this.$set(thisNode,'_hightLight',true)
          thisNode._hightLight = true
        } else {
          thisNode.disableDrag = false
          thisNode.data.disableDragNode = false
        }
        thisNode.opacity = _isHideThisLine ? 0.2 : 1
      })
    },
    // 部分节点高亮, 连线全部隐藏: 传入需高亮的id数组
    highLightNode(highLightNodeIds){
      let _all_lines = this.getLines()
      this.hiddenNodes(highLightNodeIds)
      _all_lines.forEach(thisLine => {
        // 注意这里的line和json数据中link不一样，一条线（line）上可以附着多条关系(link),可以通过line.relations获取到这条线上所有的关系数据(link)
        thisLine.relations.map(val => {
          val.color = 'rgba(211, 215, 222, 0.2)'
          val.fontColor = 'rgba(44, 134, 248, 0.2)'
        })
      })
    },
    // 布局
    buju (index) {
      this.$refs.seeksRelationGraph.buju(this.graphOptions.layouts[index])
      this.$forceUpdate()
    },
    // 删除实体
    deleteNode (id) {
      var findId = null
      if (!id) {
        if (this.selectNode.length <= 0) {
          this.$Message.warning('请选择要删除的实体！')
          return
        }
        this.selectNode.map(val => {
          this.$refs.seeksRelationGraph.removeNodeById(val.id)
          findId = val.id
        })
        this.selectNode = []
      } else {
        this.$refs.seeksRelationGraph.removeNodeById(id)
      }
      return findId
      // return this.$refs.seeksRelationGraph.getGraphJsonData()
    },
    // 数据格式处理
    inhtmlData (data) {
      return data.map(val => {
        let data = JSON.parse(JSON.stringify(val))
        val.data = data
        return val
      })
    },
    // 添加实体
    loadNextLevelData (__graph_json_data) {
      let data = { rootId: __graph_json_data.rootId, nodes: [], links: [] }
      // 去除已存在线
      this.inhtmlData(__graph_json_data.nodes).map(val => {
        if (
          !this.nodeData.nodes
            .map(val => {
              return val.id
            })
            .includes(val.id)
        ) {
          data.nodes.push(val)
          this.nodeData.nodes.push(val)
        }
      })
      let links = []
      // 去除已存在线
      this.nodeData.links.map(val => {
        links.push('form:' + val.from + 'to:' + val.to)
      })
      __graph_json_data.links.map(val => {
        if (!links.includes('form:' + val.from + 'to:' + val.to)) {
          data.links.push(val)
          this.nodeData.links.push(val)
        }
      })
      if (data.nodes.length > 0 || data.links.length > 0) {
        this.$refs.seeksRelationGraph.appendJsonData(data, seeksRGGraph => {
          this.$refs.seeksRelationGraph.focusNodeById(__graph_json_data.rootId)
          return this.$refs.seeksRelationGraph.getGraphJsonData()
        })
      } else {
        return this.$refs.seeksRelationGraph.getGraphJsonData()
      }
    },
    // 控制数据展示隐藏  list:Array[包含则展示] key:String 具体包含字段 组件属性当前节点隐藏，关联子节点会同步隐藏
    isHidens (list, key) {
      this.$refs.seeksRelationGraph.getNodes().map(val => {
        val.isHide = list.includes(val.data[key]) ? false : true
      })
    },
    // 获取图谱中所有的节点对象，可以直接修改该对象的属性，这些对象不能用于JSON序列化
    getNodes () {
      return this.$refs.seeksRelationGraph.getNodes()
    },
    // 获取图谱中所有的连线对象，可以直接修改该对象的属性，这些对象不能用于JSON序列化
    getLines () {
      return this.$refs.seeksRelationGraph.getLines()
    },
    // 获取当前图谱的节点、关系数据的json数据
    getGraphJsonData () {
      return this.$refs.seeksRelationGraph.getGraphJsonData()
    },
    // 获取当前图谱的完整的配置信息
    getGraphJsonOptions () {
      return this.$refs.seeksRelationGraph.getGraphJsonOptions()
    },
    activeNode (node) {
      let flag = this.selectNode
        .map(val => {
          return val.id
        }).includes(node.data.id)
        if(!!node._hightLight){
            flag = true
        }
    //   !!node._hightLight ? flag = true : null
      return flag
    },
    base64Info (base) {
      this.$emit('base64Info', base)
    },
    onNodeCollapse (node, e) {
      // 当有一些节点被显示或隐藏起来时，会让图谱看着很难看，需要布局器重新为节点分配位置，所以这里需要调用refresh方法来重新布局
      this.$refs.seeksRelationGraph.refresh()
    },
    onNodeExpand (node, e) {
      // 当有一些节点被显示或隐藏起来时，会让图谱看着很难看，需要布局器重新为节点分配位置，所以这里需要调用refresh方法来重新布局
      this.$refs.seeksRelationGraph.refresh()
    }
  },
  watch: {
    atlasList: {
      handler (val) {
        this.graphOptions = Object.assign(this.graphOptions, this.options)
        if (this.atlasList && this.atlasList.nodes && this.atlasList.nodes.length > 0) {
          this.$nextTick(() => {
            this.showSeeksGraph()
          })
          this.nodeData = this.atlasList
        }
      },
      // deep: true,
      immediate: true
    },
    proportion: {
      handler (oldVal, newVal) {
        // 鼠标滚动放大禁止执行
        if (this.type === 'mouseListener') {
          return
        }
        let buff = (oldVal - newVal) * 10
        this.$nextTick(() => {
          this.$refs.seeksRelationGraph.zoom(buff)
        })
      }
    },
    // 离开初始化组件
    $route (to, from) {
      if (this.$refs.seeksRelationGraph) {
        this.$refs.seeksRelationGraph.refresh()
      }
    }
  }
}
</script>

<style lang="less">
// 节点样式
.number-cube-grahp {
  text {
    cursor: pointer;
  }
}
</style>

<style lang="less" scoped>
.number-cube-grahp {
  height: 100%;
  width: 100%;

  .number-cube-grahp-node2 {
    position: relative;

    .count {
      position: absolute;
      right: 0px;
      top: -12px;
      background: #fff;
      padding: 2px 8px;
      border-radius: 50%;
      color: #0da2db;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.2);
    }

    .number-cube-grahp-image {
      width: 60px;
      height: 60px;
      border-radius: 30px;
      overflow: hidden;
      margin: 0 auto;
      background: #f9f9f9;
      border: 1px solid #d3d7de;
      display: flex;
      justify-content: center;
      align-items: center;

      .img-other {
        margin: 0 auto;
        align-items: center;
        margin-top: 13px;
      }
    }
  }

  .number-cube-grahp-node {
    // overflow: hidden;
    width: 60px;
    height: 60px;
    position: relative;

    .number-cube-grahp-image {
      width: 60px;
      height: 60px;
      border-radius: 30px;
      overflow: hidden;
      margin: 0 auto;
      background: #f9f9f9;
      border: 1px solid #d3d7de;

      .img-other {
        margin: 0 auto;
        align-items: center;
        margin-top: 13px;
      }
    }

    .pd2 {
      padding: 2px;
    }

    .number-cube-grahp-button {
      background: rgb(249, 249, 249);
      border-radius: 4px;
      border: 1px solid rgb(211, 215, 222);
      display: inline-block;
      padding: 2px 6px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
      margin-top: 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 110px;
      margin-left: -26px;
    }
  }

  .graph-info-menu {
    padding: 10px 0;
    width: 120px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    &>div {
      height: 30px;
      padding-left: 20px;
      line-height: 30px;
      cursor: pointer;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);

      &:hover {
        background: #2c86f8;
        color: #fff;
      }
    }
  }

  .tootlbar-right {
    position: absolute;
    right: 30px;
    top: 340px;
    z-index: 12;

    /deep/ .el-slider__runway>.el-slider__bar {
      background: transparent;
    }

    /deep/ .el-slider__runway {
      width: 2px;
    }

    /deep/ .el-slider__button {
      width: 14px;
      height: 14px;
      margin-right: 3px;
    }

    .tootlbar-right-icon {
      font-size: 19px;
      color: #888888;
      cursor: pointer;
      position: relative;
      right: -8px;
      display: block;
    }
  }

  // 节点选中
  .seleced>.number-cube-grahp-node {
    .number-cube-grahp-image {
      border-radius: 50%;
      box-shadow: 0px 0px 15px 0px rgba(44, 134, 248, 0.7);
      border: 3px solid #2c86f8;
      padding: 0;
    }

    .pd2 {
      padding: 0px;
    }
  }

  // 节点多顶
  .suoding {
    position: absolute;
    color: red;
    margin-left: 67px;

    i {
      font-size: 20px;
    }
  }

  .mt6 {
    margin-top: 6px;
  }

  .mb10 {
    margin-bottom: 10px;
  }

  .mt10 {
    margin-top: 10px;
  }

  .line-color {
    color: #2c86f8;
  }
}

.node-style {
  background: linear-gradient(135deg, #5BA3FF 0%, #2C86F8 100%) !important;
  color: #fff;
}

/deep/ .ui-image-div {
  border: 0;
}

/deep/ .c-expand-positon-right {
  right: -20px !important;
}
</style>
