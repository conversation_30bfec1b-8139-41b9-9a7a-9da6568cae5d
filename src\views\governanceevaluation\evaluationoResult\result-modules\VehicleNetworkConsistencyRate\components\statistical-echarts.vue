<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics :listyle="listyle"></index-statistics>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem" :lengName="lengName" :isTakeDetail="true">
          <template #rank-title>
            <span>按一致率排序</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart" :tooltipFormatter="defaultTooltipFormatter"></line-chart>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import lineChartTooltipNetwork from './line-chart-tooltip-network.vue';

export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChart: require('@/views/governanceevaluation/evaluationoResult/components/line-chart.vue').default,
  },
  data() {
    return {
      lengName: [
        {
          name: '有图片未上报视图库车辆卡口数量',
          key: 'unReportedNum',
          color: 'rgba(213, 94, 41, 1)',
        },
        {
          name: '视图库车辆卡口总量',
          key: 'beanReportedNum',
          color: 'rgba(22, 174, 22, 1)',
        },
      ],
      defaultTooltipFormatter: (data) => {
        let lineChartTooltipConstructor = Vue.extend(lineChartTooltipNetwork);
        let _this = new lineChartTooltipConstructor({
          el: document.createElement('div'),
          data() {
            return {
              data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      listyle: {
        height: '0.53rem',
        width: `calc((100% - ${30 / 192}rem) / 3)`,
      },
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: 100%;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
