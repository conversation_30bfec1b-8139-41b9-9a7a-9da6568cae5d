<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-07-04 13:31:57
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-01 13:34:32
-->
<template>
  <ui-module title="基本信息">
    <div class="info-wrapper">
      <el-row>
        <el-col :span="24" class="long-text">
          <span class="label">场所名称</span>
          <span class="text place-name" :title="info.name">{{
            info.name
          }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <span class="label">一级分类</span>
          <span class="text">{{ info.firstLevelName }}</span>
        </el-col>
        <el-col :span="12">
          <span class="label">二级分类</span>
          <span class="text">{{ info.secondLevelName }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <span class="label">所属区县</span>
          <span class="text">{{ info.adname }}</span>
        </el-col>
        <el-col :span="12">
          <span class="label">所属街道</span>
          <span class="text">{{ info.townname }}</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <span class="label">场所面积</span>
          <span class="text">{{ info.area }}平方米</span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" class="long-text">
          <span class="label">场所地址</span>
          <span class="text" :title="info.address">{{ info.address }}</span>
        </el-col>
      </el-row>
    </div>
  </ui-module>
</template>
<script>
import UiModule from "../../components/ui-module.vue";
export default {
  name: "PlaceBaseInfo",
  props: {
    info: {
      required: true,
      type: Object,
      default: () => {},
    },
  },
  components: { UiModule },
};
</script>
<style lang="less" scoped>
.info-wrapper {
  margin: 0 60px 0 10px;
  .el-row {
    margin-bottom: 20px;
    span {
      font-size: 14px;
      line-height: 20px;
    }
    .label {
      font-weight: 700;
      color: #2c86f8;
      margin-right: 14px;
      min-width: fit-content;
    }
    .text {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .place-name {
      font-weight: 700;
      font-size: 20px;
      line-height: 26px;
    }
  }
  .long-text {
    display: flex;
    align-items: center;
  }
}
</style>
