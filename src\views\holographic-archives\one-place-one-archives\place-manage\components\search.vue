<!--
 * @Date: 2025-01-07 14:34:06
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-13 18:14:12
 * @FilePath: \icbd-view\src\views\holographic-archives\one-place-one-archives\place-manage\components\search.vue
-->
<template>
  <div class="search">
    <Form ref="form" :model="queryParam" class="form" inline>
      <FormItem label="场所名称:" prop="name">
        <Input
          clearable
          v-model="queryParam.name"
          maxlength="50"
          placeholder="请输入"
        />
      </FormItem>
      <FormItem label="场所地址:" prop="address">
        <Input
          clearable
          v-model="queryParam.address"
          maxlength="50"
          placeholder="请输入"
        />
      </FormItem>
      <FormItem>
        <Button type="primary" @click="search">查询</Button>
        <Button @click="reset">重置</Button>
      </FormItem>
    </Form>
    <div class="btn-group">
      <!-- v-permission="['']" -->
      <Button @click="addHandle">
        <i class="iconfont icon-jia"></i>新增
      </Button>
      <Button @click="deleteHandle">
        <i class="iconfont icon-shanchu"></i>删除
      </Button>
    </div>
  </div>
</template>
<script>
export default {
  name: "Search",
  components: {},
  data() {
    return {
      // 查询参数
      queryParam: {
        name: "", // 场所名称
        address: "", // 场所地址
      },
    };
  },
  computed: {},
  methods: {
    /**
     * @description: 查询
     */
    search() {
      this.$emit("search", { ...this.queryParam });
    },

    /**
     * @description: 重置
     */
    reset() {
      this.$refs.form.resetFields();
      this.search();
    },

    /**
     * @description: 获取所有查询参数，暴露给父组件
     * @return {object} 查询参数
     */
    getQueryParams() {
      return this.queryParam;
    },
    addHandle() {
      this.$emit("add");
    },
    deleteHandle() {
      this.$emit("delete");
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  border-bottom: 1px solid #d3d7de;
  width: 100%;
  .form {
    width: 100%;
    /deep/.ivu-form-item {
      margin-bottom: 16px;
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input,
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    .btn-group {
      float: right;
      margin-right: 0;
    }
    button {
      margin-left: 10px;
    }
  }
}
</style>
