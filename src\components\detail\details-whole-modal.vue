<!--
    * @FileDescription: 抓拍详情
    * @Author: H
    * @Date: 2023/5/17
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-10-31 10:41:52
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left" v-if="datailsInfo.dataType == 'face'">
            <div class="thumbnail">
              <img v-lazy="datailsInfo.traitImg" alt="" />
              <span class="similarity" v-if="datailsInfo.score"
                >{{ datailsInfo.score }}%</span
              >
            </div>
            <div class="record-title">
              <span class="active">通过记录</span>
              <!-- <span :class="{'active': checkIndex == 0}" @click="tabsChange(0)">抓拍记录</span>
                            <span class="record-right" :class="{'active': checkIndex == 1}" @click="tabsChange(1)">人员档案</span> -->
            </div>
            <div class="through-record" v-if="checkStatus">
              <div class="wrapper-content">
                <span class="label">通过地点</span>：
                <ui-textOver-tips
                  @click="handlePlace(datailsInfo)"
                  class="message"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  refName="deviceName"
                  :content="datailsInfo.deviceName"
                ></ui-textOver-tips>
              </div>
              <div class="wrapper-content">
                <span class="label">通过时间</span>：
                <span class="message">{{ datailsInfo.absTime || "--" }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in faceList"
                :key="index"
              >
                <span class="label">{{ item.title }}</span
                ><span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
                <span class="message" v-else-if="item.type == 'wear'">{{
                  handleWear(datailsInfo[item.key])
                }}</span>
                <span class="message" v-else>{{
                  datailsInfo[item.key] || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else>
              <div class="wrapper-content">
                <span class="label">视频身份</span>：
                <span
                  class="message identity"
                  v-show-tips
                  v-if="datailsInfo.vid"
                  @click="toFaceDetail"
                  >{{ datailsInfo.vid }}</span
                >
                <span class="message" v-else>{{ "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">姓名</span>：
                <span class="message">{{ recordList.xm || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">联系方式</span>：
                <span class="message">{{ recordList.lxdh || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">身份证号</span>：
                <span class="message">{{ recordList.gmsfhm || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">家庭住址</span>：
                <span class="message" v-show-tips>{{
                  recordList.xzz_mlxxdz || "--"
                }}</span>
              </div>
            </div>
          </div>
          <div
            class="info-box-left"
            v-else-if="datailsInfo.dataType == 'vehicle'"
          >
            <div class="thumbnail">
              <img v-lazy="datailsInfo.traitImg" alt="" />
              <span class="similarity" v-if="datailsInfo.similarity"
                >{{ datailsInfo.similarity }} %</span
              >
              <!-- <div class="plateNum">
                                <span class="license-plate-small">{{ datailsInfo.plateNo }}</span>
                            </div> -->
              <!-- <ui-plate-number :plateNo="datailsInfo.plateNo" :color="datailsInfo.plateColor" size='medium'></ui-plate-number> -->
              <!-- <b class="shade vehicle"> -->
              <plateNumber
                :plateNo="datailsInfo.plateNo"
                :color="datailsInfo.plateColor"
                size="medium"
              ></plateNumber>
              <!-- </b> -->
            </div>
            <div class="record-title">
              <span class="active">通过记录</span>
              <!-- <span :class="{'active': checkIndex == 0}" @click="tabsChange(0)">通过记录</span> -->
              <!-- <span class="record-right" :class="{'active': checkIndex == 1}" @click="tabsChange(1)">车辆档案</span> -->
            </div>
            <div class="through-record" v-if="checkStatus">
              <div class="wrapper-content">
                <span class="label">车牌号码</span><span>：</span>
                <span class="message">{{
                  datailsInfo.plateNo || "未识别"
                }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">通过地点</span><span>：</span>
                <!-- <span class="message" v-show-tips>{{ datailsInfo.deviceName || '--' }}</span> -->
                <ui-textOver-tips
                  @click="handlePlace(datailsInfo)"
                  class="message"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  refName="deviceName"
                  :content="datailsInfo.deviceName"
                ></ui-textOver-tips>
              </div>
              <div class="wrapper-content">
                <span class="label">通过时间</span><span>：</span>
                <span class="message">{{ datailsInfo.absTime }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in veicleList"
                :key="index"
              >
                <span
                  class="label"
                  :class="{ maxLabel: item.title.length > 4 }"
                  >{{ item.title }}</span
                ><span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
                <span class="message" v-else-if="item.type == 'wear'">{{
                  handleWear(datailsInfo[item.key])
                }}</span>
                <span class="message" v-else>{{
                  datailsInfo[item.key] || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else>
              <div class="wrapper-content">
                <span class="label">车牌号码</span><span>：</span>
                <span class="message" v-if="showSkip">{{
                  datailsInfo.plateNo || "--"
                }}</span>
                <span
                  class="message"
                  v-else
                  :class="{ plateNo: datailsInfo.plateNo }"
                  @click="toVehicleDetail(datailsInfo)"
                  >{{ datailsInfo.plateNo || "--" }}</span
                >
              </div>
              <div class="wrapper-content">
                <span class="label">车辆类型</span><span>：</span>
                <span class="message">
                  {{
                    recordList.vehicleType | commonFiltering(vehicleTypeList)
                  }}
                </span>
              </div>
              <div class="wrapper-content">
                <span class="label">车辆品牌</span><span>：</span>
                <span class="message"
                  >{{ recordList.vehicleBrandCN || "--" }}
                </span>
              </div>
              <div class="wrapper-content">
                <span class="label">机动车主</span><span>：</span>
                <span class="message">{{ recordList.motorists || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">身份证号</span><span>：</span>
                <span class="message">{{ recordList.idcardNo || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">联系电话</span><span>：</span>
                <span class="message">{{ recordList.phone || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">登记地址</span><span>：</span>
                <span class="message" v-show-tips>{{
                  recordList.djdz || "--"
                }}</span>
              </div>
            </div>
          </div>
          <div class="info-box-left" v-else>
            <div class="thumbnail">
              <img v-lazy="datailsInfo.traitImg" alt="" />
              <span class="similarity" v-if="datailsInfo.similarity"
                >{{ datailsInfo.similarity }}%</span
              >
            </div>
            <div class="record-title">
              <span class="active">通过记录</span>
            </div>
            <div class="through-record">
              <div class="wrapper-content">
                <span class="label">通过地点</span>：
                <span
                  @click="handlePlace(datailsInfo)"
                  class="message"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  v-show-tips
                  >{{ datailsInfo.deviceName || "--" }}</span
                >
              </div>
              <div class="wrapper-content">
                <span class="label">通过时间</span>：
                <span class="message">{{ datailsInfo.absTime || "--" }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in showList"
                :key="index"
              >
                <span
                  class="label"
                  :class="{ maxLabel: item.title.length > 4 }"
                  >{{ item.title }}</span
                ><span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
                <span class="message" v-else-if="item.type == 'wear'">{{
                  handleWear(datailsInfo[item.key])
                }}</span>
                <span class="message" v-else>{{
                  datailsInfo[item.key] || "--"
                }}</span>
              </div>
            </div>
          </div>
          <div class="info-box-right">
            <details-largeimg
              boxSeleType="rect"
              :info="datailsInfo"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                'sc',
                'hy',
              ]"
              @collection="collection"
              :collectionType="collectionType"
              :algorithmType="1"
            ></details-largeimg>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleLeft($event, true)"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="index"
            >
              <div class="img-box">
                <img v-lazy="item.traitImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleRight($event, true)"
        ></i>
      </footer>
    </div>
  </div>
</template>

<script>
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
import { getCarInfoByCarNo } from "@/api/operationsOnTheMap";
import { mapActions, mapGetters, mapMutations } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import detailsLargeimg from "./details-largeimg.vue";
import {
  faceList,
  veicleList,
  homanBodyList,
  nonmotorList,
} from "./structuring.js";
import { cutMixins } from "./mixins.js";
import { commonMixins } from "@/mixins/app.js";
import plateNumber from "@/components/ui-vehicle/index.vue";
import { queryFusionDetails } from "@/api/viewParsingLibrary";
export default {
  name: "",
  mixins: [commonMixins, cutMixins], //全局的mixin
  components: {
    swiper,
    swiperSlide,
    detailsLargeimg,
    plateNumber,
  },
  data() {
    return {
      cutList: [],
      activeIndex: 0,
      faceList,
      veicleList,
      showList: {},
      homanBodyList,
      nonmotorList,
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("../../assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      checkStatus: true,
      recordList: {},
      overlay: false,
      collectionType: 0,
      checkIndex: 0,
      imgBoxWidth: "",
      showSkip: false,
    };
  },
  watch: {
    datailsInfo: {
      handler(row) {
        if (row.dataType == "face") {
          this.showList = this.faceList;
          this.collectionType = 24;
        } else if (row.dataType == "vehicle") {
          this.showList = this.nonmotorList;
          this.collectionType = 25;
        } else if (row.dataType == "human") {
          this.showList = this.homanBodyList;
          this.collectionType = 26;
        } else {
          this.showList = this.nonmotorList;
          this.collectionType = 27;
        }
      },
      deep: true,
    },
  },
  computed: {
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
    ...mapGetters({
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      getCollectJudge: "map/getCollectJudge",
    }),
    numLength() {
      return this.cutList.length;
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    // 开始数据
    async init(row, list, index, type, page) {
      this.checkStatus = true;
      this.checkIndex = 0;
      this.cutList = list;
      this.activeIndex = index;
      let res = await queryFusionDetails({
        id: row.id,
        indexName: row.indexName,
      });
      this.datailsInfo = res.data || {};
      this.datailsInfo.dataType = row.dataType;
      this.collectionType =
        row.dataType == "face"
          ? 24
          : row.dataType == "vehicle"
          ? 25
          : row.dataType == "human"
          ? 26
          : 27;
      // this.showList =
      //   row.dataType == 'face' ? this.faceList : row.dataType == "human" ? this.homanBodyList : this.nonmotorList;
      this.$forceUpdate();
      let small_pic = document.getElementById("scroll-ul");
      small_pic.style.left = 0;
      this.setPageInfo({
        startPage: page,
        endPage: page,
      });
      this.num = index;
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(index);
      });
    },
    // 切换
    async handleTab(item, index) {
      this.checkIndex = 0;
      this.resetData();
      let res = await queryFusionDetails({
        id: item.id,
        indexName: item.indexName,
      });
      this.datailsInfo = res.data || {};
      this.activeIndex = index;
      this.play(index);
    },
    scrollIntoViews() {
      this.$nextTick(() => {
        let ul = document.querySelector(".list-wrapper");
        let li = [...ul.childNodes][this.activeIndex];
        li.scrollIntoView({
          behavior: "smooth",
        });
      });
    },
    handlePlace(row) {
      if (row.taskType === "3") return;
      let sectionName =
        row.dataType == "face"
          ? "faceContent"
          : row.dataType == "vehicle"
          ? "vehicleContent"
          : row.dataType == "human"
          ? "humanBodyContent"
          : "nonmotorVehicleContent";
      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center?sectionName=${sectionName}&noMenu=1`,
        query: {
          deviceInfo: JSON.stringify({ ...row, deviceGbId: row.deviceId }),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    collection(flag) {
      this.$set(this.cutList[this.activeIndex], "myFavorite", flag);
    },
    toFaceDetail() {
      if (!this.datailsInfo.vid) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "video-archive",
        query: {
          archiveNo: this.datailsInfo.vid,
          source: "video",
          initialArchiveNo: this.datailsInfo.vid,
        },
      });
      window.open(href, "_blank");
    },
    toVehicleDetail(item) {
      if (!item.plateNo) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query: {
          archiveNo: JSON.stringify(this.recordList.archiveNo),
          plateNo: JSON.stringify(this.recordList.plateNo),
          source: "car",
          idcardNo: this.recordList.idcardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.device-click {
  cursor: pointer;
  text-decoration: underline;
}
</style>
