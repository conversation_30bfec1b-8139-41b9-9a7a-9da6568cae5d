<template>
  <div class="formawarehouse">
    <ui-modal v-model="visible" :title="title" width="38.5rem">
      <!-- <warehouse-setting v-if="isSet" :warehousData="warehousData"></warehouse-setting> -->
      <data-warehouse v-if="isSet" :warehousData="warehousData" @selectChange="selectChange"></data-warehouse>
      <warehousing v-else :warehousData="warehousData" @selectChange="selectChange"></warehousing>
      <template slot="footer">
        <Button type="primary" @click="update">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    warehousData: {
      type: Object,
      default: () => {},
    },
    isSet: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '1',
    };
  },
  methods: {
    init() {
      this.visible = true;
    },
    selectChange(type) {
      this.type = type;
    },
    async update() {
      try {
        let paramData = this.warehousData;
        paramData.dataHandle = this.type;
        let params = paramData;
        await this.$http.post(governancetheme.updateTheme, params);
        this.visible = false;
        this.$emit('render');
        this.$Message.success('数据配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    warehousData() {
      this.title = this.warehousData.isDefault === '1' ? '正式入库' : '临时入库';
    },
  },
  components: {
    Warehousing: require('../../components/warehousing.vue').default,
    // WarehouseSetting: require('./warehouse-setting.vue').default,
    DataWarehouse: require('./data-warehouse.vue').default,
  },
};
</script>
<style lang="less" scoped>
.formawarehouse {
  @{_deep} .ivu-modal-body {
    padding: 26px 50px 36px;
  }
}
</style>
