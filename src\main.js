import Vue from 'vue';
import App from './App.vue';
import router from '@/config/router.config';
import store from './store';
import axios from '@/config/http/http';
import VueAxios from 'vue-axios';
import ViewUI from 'view-design';
import 'view-design/dist/styles/iview.css';
import '@/style/index.less';
import util from '@/util/index';
import * as filters from '@/config/filter';
import 'element-ui/lib/theme-chalk/index.css';
import {
  Input,
  Button,
  Tree,
  Tag,
  Carousel,
  CarouselItem,
  Transfer,
  Table,
  TableColumn,
  TimePicker,
  DatePicker,
  Popover,
  Loading,
  Notification,
  Menu,
  MenuItem,
  MenuItemGroup,
  Submenu,
} from 'element-ui';
import infiniteScroll from 'vue-infinite-scroll';
import VueLazyload from 'vue-lazyload';
import dealSearch from './mixins/deal-search';
import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
import <PERSON><PERSON>Viewer from 'vue-json-viewer';
// 引入全局变量
import global from './util/global';
// 引入echarts
import * as echarts from 'echarts';
import 'echarts-liquidfill';
import 'amfe-flexible';

Vue.prototype.$echarts = echarts;
Vue.prototype.$util = util;
Vue.prototype.global = global;
Vue.prototype.$loading = Loading.service;
Vue.prototype.$notify = Notification;
Vue.config.productionTip = false;

Vue.use(VueAxios, axios);
Vue.use(ViewUI);
Vue.use(Input);
Vue.use(Menu);
Vue.use(Submenu);
Vue.use(MenuItem);
Vue.use(MenuItemGroup);
Vue.use(Button);
Vue.use(Tree);
Vue.use(Tag);
Vue.use(Carousel);
Vue.use(CarouselItem);
Vue.use(Transfer);
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(TimePicker);
Vue.use(DatePicker);
Vue.use(Popover);
Vue.use(infiniteScroll);
Vue.use(Loading.directive);
Vue.use(JsonViewer);
Vue.use(VueLazyload, {
  preLoad: 1.3,
  error: require('@/assets/img/imgerror.png'),
  loading: require('@/assets/img/imgloading.png'),
  attempt: 1,
});
Vue.use(Viewer);
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: 'data-source',
  },
});

Vue.mixin(dealSearch);

Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Vue.component('ui-modal', require('@/components/ui-modal.vue').default);
Vue.component('ui-label', require('@/components/ui-label.vue').default);
Vue.component('ui-page', require('@/components/ui-page.vue').default);
Vue.component('ui-image', require('@/components/ui-image.vue').default);
Vue.component('ui-slider', require('@/components/ui-slider.vue').default);
Vue.component('loading', require('@/components/loading.vue').default);
Vue.component('look-scene', require('@/components/look-scene.vue').default);
Vue.component('ui-btn-tip', require('@/components/ui-btn-tip.vue').default);
Vue.component('tree-table', require('@/components/tree-table/index').default);

import UiConfirm from '@/components/ui-confirm';
Vue.use(UiConfirm);

const directives = require.context('./directives');
directives.keys().forEach((key) => {
  const directive = directives(key);
  directive.default(Vue);
});

// 清空授权信息，每次进入项目重新请求授权信息
window.sessionStorage.setItem('authorInfo', '');

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app');
