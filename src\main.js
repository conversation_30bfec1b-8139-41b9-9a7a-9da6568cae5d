import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import microApp from "@micro-zoe/micro-app";
// import { asyncRoutes, constantRoutes } from '@/router/index'
import { asyncRoutes } from "@/router/router.config";
import store from "./store";
import ViewUI from "view-design";
import "view-design/dist/styles/iview.css";
import util from "@/libs/configuration/util.js";
import "amfe-flexible";
import VueLazyload from "vue-lazyload";
import dayjs from "dayjs";
import "@/style/index.less";
import "./router/router.permission";
import * as filters from "@/util/filters";
import "swiper/dist/css/swiper.css";
import ElementResizeDetectorMaker from "element-resize-detector";
import Viewer from "v-viewer";
import "viewerjs/dist/viewer.css";
import "element-ui/lib/theme-chalk/index.css";
import ElementUI from "element-ui";
import { directive as clickOutside } from "v-click-outside-x";
import "@/util/modules/toolkit.js";
import ziPlayer from "@/components/ziplayer/index.vue";
import { commonMixins } from "@/mixins/app.js";
import { parse as stackTraceParser } from "stacktrace-parser";
import H5Player from "@/components/H5Player/index.vue";
import h5PlayerObj from "@/util/modules/H5BussinessAPI.js";
Vue.prototype.$H5PlayerAPI = h5PlayerObj;
Vue.component("h5-player", H5Player);

if (process.env.VUE_APP_CODE) window.applicationCode = process.env.VUE_APP_CODE;

Vue.mixin(commonMixins);

Vue.component("zi-player", ziPlayer);

Vue.use(ElementUI, { size: "mini" });

// 图片放大
Vue.use(Viewer, {
  defaultOptions: {
    zIndex: 20000,
    navbar: 0,
    toolbar: {
      zoomIn: 1,
      zoomOut: 1,
      oneToOne: 1,
      reset: 0,
      prev: 1,
      next: 1,
      play: {
        show: 1,
        size: "large",
      },
      rotateLeft: 1,
      rotateRight: 1,
      flipHorizontal: 1,
      flipVertical: 1,
    },
  },
});

// 换肤默认浅色版
import setTheme from "@/libs/theme";

setTheme("light");
// mock
// import './mock/index'
// 导航配置方法
import {
  getHeaderName,
  getSiderSubmenuName,
  getMenuSider,
} from "@/libs/system";
// 配置
import Setting from "./libs/configuration/setting";

const imgProxyToHttps = filters.imgProxyToHttps;

Vue.prototype.$dayjs = dayjs; // 时间处理库
Vue.prototype.$util = util; // 自定义工具库
Vue.prototype.checkPermission = util.permission.checkPermission; // 权限严重函数
Vue.prototype.$erd = ElementResizeDetectorMaker();
Vue.prototype.$eventBus = new Vue(); //eventbus
Vue.prototype.$imgProxyToHttps = imgProxyToHttps;

// 方法
Vue.config.productionTip = false;
// viewUI 初始化配置
Vue.use(ViewUI, {
  capture: false,
  // datePicker: {
  //   customIcon: 'ivu-icon-ios-time-outline',
  //   iconSize: 16
  // },
  select: {
    arrowSize: 16,
  },
  menu: {
    arrow: "md-arrow-dropdown",
    arrowSize: 20,
  },
});
// 图片懒加载配置
Vue.use(VueLazyload, {
  preLoad: 1.3,
  error: require("@/assets/img/default-img/image_error.png"),
  loading: require("@/assets/img/default-img/image_loading.gif"),
  attempt: 1,
  filter: {
    webp(listener, options) {
      if (!options.supportWebp) return;
      listener.src = imgProxyToHttps(listener.src);
    },
  },
});

Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Vue.component("ui-modal", require("@/components/ui-modal.vue").default);
Vue.component(
  "ui-table",
  require("@/components/ui-table/ui-table.vue").default
);
Vue.component("ui-page", require("@/components/ui-page.vue").default);
Vue.component("ui-tag", require("@/components/ui-tag.vue").default);
Vue.component("ui-loading", require("@/components/ui-loading.vue").default);
Vue.component("ui-empty", require("@/components/ui-empty.vue").default);
Vue.component("i-link", require("@/components/link/index.vue").default);
Vue.component("ui-btn-tip", require("@/components/ui-btn-tip.vue").default);
Vue.component("ui-card", require("@/components/ui-card.vue").default);
Vue.component("ui-tag-poptip", require("@/components/ui-tag-poptip").default);
Vue.component("ui-image", require("@/components/ui-image.vue").default);
Vue.component("ui-icon", require("@/components/ui-icon.vue").default);
Vue.component(
  "select-device",
  require("@/components/select-modal/select-device.vue").default
);

Vue.component(
  "select-task",
  require("@/components/select-modal/select-task.vue").default
);
Vue.component(
  "select-file",
  require("@/components/select-modal/select-file.vue").default
);
Vue.component(
  "ui-tag-select",
  require("@/components/ui-tag-select/tag-select").default
);
Vue.component(
  "ui-tag-select-option",
  require("@/components/ui-tag-select/tag-select-option").default
);
Vue.component(
  "ui-license-plate",
  require("@/components/ui-license-plate").default
);
Vue.component(
  "ui-plate-number",
  require("@/components/ui-vehicle/ui-plate-number").default
);
Vue.component(
  "ui-textOver-tips",
  require("@/components/textOverTooltip/ui-textOver-tips").default
);
Vue.component(
  "hl-timerange",
  require("@/components/hl-timerange/index.vue").default
);
Vue.component("ui-quick-date", require("@/components/ui-quick-date").default);
import UiConfirm from "@/components/ui-confirm";

Vue.use(UiConfirm);

// 指令配置
const directives = require.context("./directives");
directives.keys().forEach((key) => {
  const directive = directives(key);
  directive.default(Vue);
});
Vue.directive("clickOutside", clickOutside);

// 清空授权信息，每次进入项目重新请求授权信息
window.localStorage.setItem("authorInfo", "");
const constantRoutes = [];
const menuSider = [...constantRoutes, ...asyncRoutes];
new Vue({
  router,
  store,
  watch: {
    // 监听路由 控制侧边栏显示 标记当前顶栏菜单（如需要）,静态新开，parentName强制判断（待优化定位二级/三级菜单）
    $route(to, from) {
      // 404与登录无需判断
      if (to.name === "Login" || to.name === "error_404") {
        return;
      }
      const menuList = this.$store.getters.routers;
      let path = to.matched[to.matched.length - 1].path;
      if (!Setting.dynamicSiderMenu) {
        let header = getHeaderName(path, menuList);
        let headerName = header.headerName;
        if (headerName === null) {
          path = to.path;
          header = getHeaderName(path, menuList);
          headerName = header.headerName;
        }
        // 在 404 时，是没有 headerName 的
        if (headerName !== null) {
          this.$store.commit(
            "admin/menu/setMenuSider",
            this.$store.getters.addRouters
          );
          let menushow = false;
          if (path == "/device-archives/device-dashboard") {
            menushow = true;
          } else {
            menushow = false;
          }
          const filterMenuSider = getMenuSider(menuList, header, menushow);
          this.$store.commit("admin/menu/setSider", filterMenuSider);
          this.$store.commit("admin/menu/setActivePath", to.name);
          let openNames = "";
          // 静态路由打开新tab页(tabShow,parentName必穿)
          if (header.parentName) {
            this.$store.commit(
              "admin/menu/setHeaderName",
              filterMenuSider[0] ? filterMenuSider[0].meta.title : ""
            );
            if (header.parentName.indexOf("/") !== -1) {
              openNames = header.parentName.split("/").reverse();
            } else {
              openNames = [header.parentName];
            }
          } else {
            this.$store.commit("admin/menu/setHeaderName", headerName);
            openNames = getSiderSubmenuName(to.name, filterMenuSider);
          }
          this.$store.commit("admin/menu/setOpenNames", openNames);
        }
      }
    },
  },
  render: (h) => h(App),
}).$mount("#app");
if (process.env.NODE_ENV === "production") {
  Vue.config.errorHandler = function (err) {
    // 处理错误
    const errInfo = stackTraceParser(err.stack);
    console.error("====Vue错误捕获===:", errInfo[0]);
  };
}

microApp.start();
