<template>
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: deviceList }">
    <draw-echarts
      class="charts"
      :echart-option="propertyEchart"
      :echart-style="ringStyle"
      ref="devieceNumChartRef"
      :echarts-loading="echartsLoading"
      @echartClick="echartClickJump"
    ></draw-echarts>
    <span class="next-echart">
      <i
        class="icon-font icon-zuojiantou1 f-12"
        @click="scrollRight('devieceNumChartRef', deviceList, [], comprehensiveConfig.homeNum)"
      ></i>
    </span>
  </div>
</template>

<script>
import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import dataZoom from '@/mixins/data-zoom';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
import deviceNumberStyle from '@/views/home/<USER>/module/device-number';

export default {
  name: 'device-number',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    evaluationIndexResult: {},
    loading: {},
    styleType: {},
  },
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      echartsLoading: false,
      deviceList: [], // 设备数量
      isIndexIdItem: {},
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
    homeStyle() {
      return deviceNumberStyle[`style${this.styleType}`] || deviceNumberStyle.style1;
    },
  },
  watch: {
    loading: {
      handler() {
        this.echartsLoading = this.loading;
      },
      immediate: true,
    },
    evaluationIndexResult: {
      async handler(val) {
        if (!val?.length) return;
        await this.getEvaluationRecord(); // 设备数量
        this.handleDeviceCharts();
      },
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    getIndexItem(indexId) {
      return this.evaluationIndexResult.find((item) => item.indexId === indexId);
    },
    echartClickJump(item) {
      this.$emit('on-jump', item.data.originData);
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('devieceNumChartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    // 设备数量
    async getEvaluationRecord() {
      if (!this.getIndexItem(1004)) return;
      // 找到 数量达标率1004的数据，然后再请求接口
      let isIndexIdItem = this.getIndexItem(1004);
      try {
        let params = {
          batchId: isIndexIdItem?.batchId,
          displayType: 'REGION',
          orgRegionCode: isIndexIdItem?.civilCode,
        };
        let { data } = await this.$http.post(governanceevaluation.getHomeBasicQuantityStandardDataV2, params);
        this.deviceList = data?.data || [];
      } catch (err) {
        console.log(err);
      }
    },
    handleDeviceCharts() {
      if (!this.getIndexItem(1004)) return;
      let { indexId, batchId, indexType } = this.getIndexItem(1004);
      let xAxisData = [];
      let allSeriesData = {
        sxjCountList: [],
        rlkkCountList: [],
        clkkCountList: [],
      };
      let baseSeries = [
        {
          dataName: 'sxjCountList',
          filedName: 'sxjReportCount',
          color: this.homeStyle.video,
          name: '视频监控',
          selfFiledName: 'sxjStandardsValue',
        },
        {
          dataName: 'rlkkCountList',
          color: this.homeStyle.face,
          name: '人脸卡口',
          filedName: 'rlkkReportCount',
          selfFiledName: 'rlkkStandardsValue',
        },
        {
          dataName: 'clkkCountList',
          color: this.homeStyle.vehicle,
          name: '车辆卡口',
          filedName: 'clkkReportCount',
          selfFiledName: 'clkkStandardsValue',
        },
      ];
      this.deviceList.forEach((item) => {
        let detailData = {
          name: item.civilName,
          // title: '视频图像采集区域数量达标率',
          // titleNum: `${titleNum.toFixed(2)}%`,
          list: baseSeries.map((one) => {
            return {
              label: one.name,
              color: one.color[0],
              numColor: '#fff',
              num: item[one.filedName] ?? 0,
              selfMes: item?.[one.selfFiledName] == 1 ? '达标' : '不达标',
              selfColor: item?.[one.selfFiledName] == 1 ? '#01EF77' : '#C90D39',
            };
          }),
        };
        allSeriesData.sxjCountList.push({
          value: item.sxjReportCount,
          data: detailData,
          originData: {
            ...item,
            indexId: indexId,
            batchId: batchId,
            regionCode: item.civilCode,
            indexType: indexType,
            displayType: 'REGION',
          },
        });
        allSeriesData.rlkkCountList.push({
          value: item.rlkkReportCount,
          data: detailData,
          originData: {
            ...item,
            indexId: indexId,
            batchId: batchId,
            regionCode: item.civilCode,
            indexType: indexType,
            displayType: 'REGION',
          },
        });
        allSeriesData.clkkCountList.push({
          value: item.clkkReportCount,
          data: detailData,
          originData: {
            ...item,
            indexId: indexId,
            batchId: batchId,
            regionCode: item.civilCode,
            indexType: indexType,
            displayType: 'REGION',
          },
        });
        xAxisData.push({
          value: item.civilName,
        });
      });
      this.propertyEchart = this.$util.doEcharts.getHomeDeviceBar({
        toolTipDom: TooltipDom,
        title: '设备数量',
        xAxisData: xAxisData,
        seriesData: baseSeries.map((item) => {
          return {
            name: item.name,
            color: item.color,
            data: allSeriesData[item.dataName],
          };
        }),
      });
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  flex: 1;
  .charts {
    width: 100%;
    height: 100% !important;
  }
}
</style>
