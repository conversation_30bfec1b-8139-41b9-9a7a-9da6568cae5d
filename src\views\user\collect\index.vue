<template>
  <div class="collect-page">
    <div class="tree-left">
      <div class="search-top">
        <Input placeholder="输入关键词搜索" v-model="catalogName">
          <Icon
            type="ios-search"
            class="font-16 cursor-p"
            slot="suffix"
            maxlength="50"
            @click.prevent="searchName(catalogName)"
          />
        </Input>
      </div>
      <el-tree
        ref="treeList"
        :data="treeData"
        class="tree-box"
        accordion
        :highlight-current="true"
        default-expand-all
        :expand-on-click-node="true"
        :current-node-key="resourceType"
        node-key="dataKey"
        @node-click="handleNodeClick"
      >
        <div slot-scope="{ data }" class="slot-node">
          <span class="node-title">
            <span>
              <i class="iconfont color-bule mr-5" :class="data.icon"></i>
              <span :title="data.dataValue" class="ellipsis name-width">{{
                data.dataValue
              }}</span>
            </span>
          </span>
        </div>
      </el-tree>
    </div>
    <div class="collect">
      <div class="search" v-show="![4, 13, 14, 15, 16].includes(activeIndex)">
        <div class="search_btn">
          <Form ref="form" :inline="true" :model="formData">
            <FormItem label="" prop="idcardNo">
              <Input
                :placeholder="markedWords.placeholder || '请输入'"
                v-model="formData.idcardNo"
                maxlength="50"
                class="search-input-430"
              >
                <Button
                  slot="append"
                  type="primary"
                  icon="ios-search"
                  @click="startSearch"
                ></Button>
              </Input>
            </FormItem>
          </Form>
        </div>
      </div>
      <div class="card-content" v-if="sectionName != 'cloudPage'">
        <!-- <div v-for="(item, index) in list" :key="index" class="card-item">
                    <UiListCard :type="searchList[activeIndex].type" :data="item" :index="index" @archivesDetailHandle="archivesDetailHandle(item)" @on-change.stop="changeCardHandle(item, index)" @collection="getList"/>
                    
                </div> -->
        <div v-for="(item, index) in list" :key="index" :class="componentClass">
          <component
            :type="markedWords.type"
            :is="sectionName"
            :data="item"
            :collType="activeIndex + 1"
            @archivesDetailHandle="archivesDetailHandle(item)"
            @collection="collection"
            @grabDetailFn="grabDetailFn($event, index)"
            @personnelAlarm="personnelAlarm(item, index)"
            @vehicleAlarm="vehicleAlarm(item, index)"
            @facePhoto="facePhoto(item, index)"
            @vehicPhoto="vehicPhoto(item, index)"
            :childSource="childSource"
            :deviceType="deviceType"
            :index="index"
          ></component>
          <!-- <faceVehice :dataList="item"></faceVehice> -->
        </div>
        <ui-empty v-if="list.length === 0 && loading == false"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <div class="card-content" v-else>
        <cloudPage
          :tableData="tableData"
          @deleteRow="deleteRow"
          @cloudView="handleCloudView"
          ref="cloudPage"
        ></cloudPage>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="page.pageNumber"
        :total="total"
        :page-size="page.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
    <!-- 抓拍详情 -->
    <details-modal
      v-if="humanShow"
      ref="humanbody"
      @collection="detailsCollection"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="humanShow = false"
    ></details-modal>
    <!-- 人像抓拍 -->
    <details-face-modal
      v-if="videoShow"
      ref="videoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="videoShow = false"
    ></details-face-modal>
    <!-- 车辆抓拍 -->
    <details-vehicle-modal
      v-if="vehicleShow"
      ref="vehiclePhotoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="vehicleShow = false"
    ></details-vehicle-modal>
    <!-- 人员布控 -->
    <peopleAlarmDetail
      @collection="detailsCollection"
      v-if="detailShow"
      :tableList="list"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="alarmDetail"
      @close="detailShow = false"
    />
    <!-- 车辆布控 -->
    <vehicleAlarmDetail
      @collection="detailsCollection"
      v-if="vehicleAlarmShow"
      :tableList="list"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="vehicleDetail"
      @close="vehicleAlarmShow = false"
    />
    <!-- 我的云盘 -->
    <details-cloud-modal
      v-if="cloudShow"
      ref="cloudDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="cloudShow = false"
    ></details-cloud-modal>
  </div>
</template>

<script>
import UiListCard from "@/components/ui-list-card";
import { queryMyFavoritePageList, diskPageList } from "@/api/home";
import facePhotograph from "./components/facePhotograph.vue";
import vehicPhotograph from "./components/vehicPhotograph.vue";
import deviceShoot from "./components/deviceShoot.vue";
import deviceContent from "./components/deviceContent.vue";
import alarmBox from "./components/alarmBox.vue";
import personnelAlarm from "./components/personnelAlarm.vue";
import vehicleAlarm from "./components/vehicleAlarm.vue";
import humanBody from "./components/humanBody.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { mapActions, mapGetters } from "vuex";
import { queryParamDataByKeys } from "@/api/config";
import detailsModal from "@/components/detail/details-modal.vue";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import peopleAlarmDetail from "@/views/target-control/alarm-manager/people/components/alarm-detail.vue";
import vehicleAlarmDetail from "@/views/target-control/alarm-manager/vehicle/components/vehicle-detail.vue";
import detailsCloudModal from "@/components/detail/details-cloud-modal.vue";
import cloudPage from "./components/cloudPage.vue";
export default {
  name: "collect",
  components: {
    UiListCard,
    facePhotograph,
    vehicPhotograph,
    deviceShoot,
    deviceContent,
    alarmBox,
    humanBody,
    personnelAlarm,
    vehicleAlarm,
    detailsModal,
    detailsFaceModal,
    detailsVehicleModal,
    peopleAlarmDetail,
    vehicleAlarmDetail,
    cloudPage,
    detailsCloudModal,
  },
  data() {
    return {
      treeData: [],
      catalogName: "",
      resourceType: 0,
      humanShow: false,
      videoShow: false,
      vehicleShow: false,
      detailShow: false,
      vehicleAlarmShow: false,
      cloudShow: false,
      alarmInfo: {},
      tableIndex: 0,
      treeList: [
        {
          dataValue: "我的云盘",
          dataKey: 22,
          type: 22,
          placeholder: "搜索你的文件",
        },
        {
          dataKey: "p0",
          dataValue: "我的收藏",
          children: [
            {
              dataKey: "p0-1",
              dataValue: "档案",
              icon: "icon-anjian",
              children: [
                {
                  dataValue: "实名人档",
                  dataKey: 1,
                  type: "people",
                  placeholder: "请输入身份证号",
                },
                {
                  dataValue: "视频人档",
                  dataKey: 2,
                  type: "video",
                  placeholder: "请输入视频身份证号",
                },
                {
                  dataValue: "一车一档",
                  dataKey: 3,
                  type: "vehicle",
                  placeholder: "请输入车牌号码",
                },
                {
                  dataValue: "一机一档",
                  dataKey: 4,
                  type: "device",
                  placeholder: "请输入设备编码",
                },
                {
                  dataValue: "重点人档",
                  dataKey: 19,
                  type: "zdr",
                  placeholder: "请输入身份证号",
                },

                {
                  dataValue: "非机动车档",
                  dataKey: 18,
                  type: "non-motor-vehicle",
                  placeholder: "请输入车牌号码",
                },
                {
                  dataValue: "场所档案",
                  dataKey: 20,
                  type: "place",
                  placeholder: "请输入场所名称",
                },
              ],
            },
            {
              dataKey: "p0-2",
              dataValue: "感知设备",
              icon: "icon-shebeizichan",
              children: [
                {
                  dataValue: "Wi-Fi设备",
                  dataKey: 7,
                  type: 7,
                  placeholder: "请输入设备编码",
                },
                {
                  dataValue: "RFID设备",
                  dataKey: 8,
                  type: 8,
                  placeholder: "请输入设备编码",
                },
                {
                  dataValue: "电围设备",
                  dataKey: 9,
                  type: 9,
                  placeholder: "请输入设备编码",
                },
              ],
            },
            {
              dataKey: "p0-3",
              dataValue: "感知数据",
              icon: "icon-shujuyuan",
              children: [
                {
                  dataValue: "人像抓拍",
                  dataKey: 5,
                  type: 5,
                  placeholder: "请输入视频身份",
                },
                {
                  dataValue: "车辆抓拍",
                  dataKey: 6,
                  type: 6,
                  placeholder: "请输入车牌号码",
                },
                {
                  dataValue: "人体抓拍",
                  dataKey: 16,
                  type: 15,
                  placeholder: "请输入姓名",
                },
                {
                  dataValue: "非机动车抓拍",
                  dataKey: 17,
                  type: 16,
                  placeholder: "请输入视频身份",
                },
                {
                  dataValue: "Wi-Fi设备抓拍",
                  dataKey: 10,
                  type: 10,
                  placeholder: "请输入MAC地址",
                },
                {
                  dataValue: "RFID设备抓拍",
                  dataKey: 11,
                  type: 11,
                  placeholder: "请输入RFDI编码",
                },
                {
                  dataValue: "电围设备抓拍",
                  dataKey: 12,
                  type: 12,
                  placeholder: "请输入IMSI编码",
                },
              ],
            },
            {
              dataKey: "p0-4",
              dataValue: "管控报警",
              icon: "icon-mubiaoguankong",
              children: [
                {
                  dataValue: "人员布控",
                  dataKey: 14,
                  type: 13,
                  placeholder: "请输入姓名",
                },
                {
                  dataValue: "车辆布控",
                  dataKey: 15,
                  type: 14,
                  placeholder: "请输入车牌号",
                },
              ],
            },
            {
              dataKey: "p0-5",
              dataValue: "视图解析库",
              icon: "icon-mubiaoguankong",
              children: [
                {
                  dataValue: "人像",
                  dataKey: 24,
                  type: 24,
                  placeholder: "请输入视频身份",
                },
                {
                  dataValue: "车辆",
                  dataKey: 25,
                  type: 25,
                  placeholder: "请输入车牌号码",
                },
                {
                  dataValue: "人体",
                  dataKey: 26,
                  type: 26,
                  placeholder: "请输入姓名",
                },
                {
                  dataValue: "非机动车",
                  dataKey: 27,
                  type: 27,
                  placeholder: "请输入视频身份",
                },
              ],
            },
          ],
        },
      ],
      activeIndex: 0,
      list: [],
      pageForm: {
        dataType: 1,
      },
      loading: false,
      total: 0,
      page: {
        pageNumber: 1,
        pageSize: 20,
      },
      formData: {
        idcardNo: "",
      },
      sectionName: "UiListCard",
      componentClass: "card-item",
      childSource: 2,
      deviceType: 1,
      alarmConfigInfo: {},
      markedWords: {},
      tableData: [],
    };
  },
  computed: {
    ...mapGetters({
      favoriteType: "dictionary/getFavoriteType",
      userInfo: "userInfo",
    }),
  },
  async created() {
    await this.getDictData();
    this.disposeTreeList();
    var param = ["ICBD_TARGET_CONTROL"];
    let res = await queryParamDataByKeys(param);
    if (res.data.length > 0) {
      this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
    }
    let flatTreeList = this.flatTreeData(this.treeData);

    // 从工作台跳转
    if (this.$route.query.type) {
      let currentTreeNode = flatTreeList.find(
        (item) => item.dataKey == this.$route.query.type
      );
      this.$nextTick(() => {
        this.$refs.treeList.setCurrentKey(currentTreeNode.dataKey);
      });
      this.markedWords = currentTreeNode;
      this.handleTab(currentTreeNode.dataKey - 1);
    } else if (this.$route.query.dataKey) {
      this.$nextTick(() => {
        this.$refs.treeList.setCurrentKey(this.$route.query.dataKey);
      });
      this.markedWords = flatTreeList.find(
        (item) => item.dataKey == this.$route.query.dataKey
      );
      this.handleTab(this.markedWords.dataKey);
    } else {
      let currentTreeNode = flatTreeList.filter(
        (item) => typeof item.dataKey == "number"
      )[0];
      this.$nextTick(() => {
        this.$refs.treeList.setCurrentKey(currentTreeNode.dataKey);
      });
      this.markedWords = currentTreeNode;
      if (currentTreeNode.dataKey === 22) {
        this.handleTab(currentTreeNode.dataKey);
      } else {
        this.handleTab(currentTreeNode.dataKey - 1);
      }
    }
  },
  methods: {
    disposeTreeList(list = []) {
      this.treeData = [];
      let mapApps = {};
      if (list.length > 0) {
        mapApps = new Map(list.map((item) => [item.dataKey, item]));
      } else {
        mapApps = new Map(
          this.favoriteType.map((item) => [item.dataKey, item])
        );
      }
      this.treeData = this.formatTreeData(this.treeList, mapApps);

      this.treeData = this.treeData.filter((item) => {
        if (!item.children || item.children.length > 0) {
          return item;
        }
      });
    },

    flatTreeData(list) {
      let array = [];
      list.forEach((item) => {
        array.push({ ...item });
        if (item.children?.length) {
          array = array.concat(this.flatTreeData(item.children));
        }
      });
      return array;
    },

    /**
     * @description: 格式化树数据
     * @param {array} list 格式前的数据
     * @param {map} mapApps 收藏数据
     */
    formatTreeData(list, mapApps) {
      let data = []; // 存放当前格式化的数据
      list.forEach((item) => {
        let obj = {
          ...item,
        };
        let inFavorite = mapApps.get(String(item.dataKey));
        if (inFavorite) {
          obj.dataValue = inFavorite.dataValue;
        }
        if (item.children?.length) {
          obj.children = this.formatTreeData(item.children, mapApps);
        }
        data.push(obj);
      });
      return data;
    },

    // 根据名称搜索树
    searchName(value) {
      if (value == "") {
        this.disposeTreeList();
      } else {
        let list = this.favoriteType.filter((item, index) => {
          if (item.dataValue.indexOf(value.trim()) > -1) {
            return item;
          }
        });
        this.disposeTreeList(list);
      }
    },
    // 选中左侧树节点
    handleNodeClick(data) {
      if (data.children && data.children.length > 0) {
        // 判断点击存在子节点的父节点无效果
        this.$nextTick(() => {
          this.$refs.treeList.setCurrentKey(this.currentNodeKey);
        });
        return;
      }
      this.page = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.formData.idcardNo = "";
      this.currentNodeKey = data.dataKey;
      this.markedWords = data;
      if (data.type == 22) {
        let nodes = this.$refs.treeList.store.nodesMap;
        // nodes["p0"].expanded = false;
        this.handleTab(22);
      } else {
        this.handleTab(data.dataKey - 1);
      }
    },
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    handleTab(index) {
      this.activeIndex = index;
      if ([0, 1, 2, 3, 18, 17, 19].includes(index)) {
        this.sectionName = "UiListCard";
        this.componentClass = "card-item";
      } else if ([4, 12, 23].includes(index)) {
        this.sectionName = "facePhotograph";
        this.componentClass = "card-face";
        this.childSource = index == 4 || index == 23 ? 2 : 1;
      } else if ([5, 24].includes(index)) {
        this.sectionName = "vehicPhotograph";
        this.componentClass = "card-face";
      } else if ([6, 7, 8].includes(index)) {
        this.sectionName = "deviceContent";
        this.componentClass = "card-device";
        this.deviceType = index == 6 ? 2 : index == 7 ? 3 : 4;
      } else if ([9, 10, 11].includes(index)) {
        this.sectionName = "deviceShoot";
        this.componentClass = "card-device";
        this.deviceType = index == 9 ? 1 : index == 10 ? 2 : 3;
      } else if (index == 13) {
        this.sectionName = "personnelAlarm";
        this.componentClass = "card-alarm";
      } else if (index == 14) {
        this.sectionName = "vehicleAlarm";
        this.componentClass = "card-alarm";
      } else if ([15, 16, 25, 26].includes(index)) {
        this.sectionName = "humanBody";
        this.componentClass = "card-human";
      } else if (index == 22) {
        this.sectionName = "cloudPage";
        this.$nextTick(() => {
          this.cloudList();
        });
        return;
      }
      this.getList();
    },
    deleteRow() {
      this.cloudList();
    },
    handleCloudView(row, index) {
      this.cloudShow = true;
      this.$nextTick(() => {
        this.$refs.cloudDetail.init(
          row,
          this.tableData,
          index,
          1,
          this.page.pageNumber
        );
      });
    },
    cloudList(page = 0) {
      this.$refs.cloudPage.loading = true;
      this.tableData = [];
      let params = {
        fileName: this.formData.idcardNo,
        userId: this.userInfo.id,
        ...this.page,
      };
      diskPageList(params)
        .then((res) => {
          let { entities, total } = res.data;
          this.tableData = entities;
          this.total = total;
          if (page == 1) {
            this.$refs.cloudDetail.prePage(this.tableData);
          } else if (page == 2) {
            this.$refs.cloudDetail.nextPage(this.tableData);
          }
        })
        .finally(() => {
          this.$refs.cloudPage.loading = false;
        });
    },
    searchForm() {},
    /**
     * 上一个
     */
    prePage() {
      if (this.page.pageNumber == 1) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageChange(this.page.pageNumber - 1, 1);
      }
    },
    /**
     * 下一个
     */
    async nextPage() {
      let num = this.page.pageNumber;
      let size = this.page.pageSize;
      if (this.total <= num * size) {
        this.$Message.warning("已经是最后一个了");
        return;
      } else {
        await this.pageChange(this.page.pageNumber + 1, 2);
      }
    },
    // 人像抓拍
    facePhoto(row, index) {
      this.videoShow = true;
      this.$nextTick(() => {
        this.$refs.videoDetail.init(
          row,
          this.list,
          index,
          5,
          this.page.pageNumber
        );
      });
    },
    // 车辆抓拍
    vehicPhoto(row, index) {
      this.vehicleShow = true;
      this.$nextTick(() => {
        this.$refs.vehiclePhotoDetail.init(
          row,
          this.list,
          index,
          this.page.pageNumber
        );
      });
    },
    // 抓拍详情
    grabDetailFn(row, index) {
      this.humanShow = true;
      let type = this.activeIndex == 15 ? 1 : 2;
      this.$nextTick(() => {
        this.$refs.humanbody.init(
          row,
          this.list,
          index,
          type,
          this.page.pageNumber
        );
      });
    },
    // 管控报警详情
    personnelAlarm(row, index) {
      this.alarmInfo = row;
      this.tableIndex = index;
      this.detailShow = true;
    },
    vehicleAlarm(row, index) {
      this.alarmInfo = row;
      this.tableIndex = index;
      this.vehicleAlarmShow = true;
    },
    // 跳转详情
    archivesDetailHandle(item) {
      let params = {};
      if (item.type === "device") {
        params = {
          archiveNo: item.deviceId,
        };
      } else if (item.type === "vehicle") {
        params = {
          archiveNo: JSON.stringify(item.archiveNo),
          source: "car",
          plateNo: JSON.stringify(item.plateNo),
          idcardNo: item.idcardNo,
        };
      } else if (item.type === "non-motor-vehicle") {
        params = {
          archiveNo: JSON.stringify(item.archiveNo),
          plateNo: JSON.stringify(item.plateNo),
          source: "non-motor-vehicle",
          idcardNo: item.idcardNo,
        };
      } else if (item.type === "place") {
        params = {
          archiveNo: item.id,
          source: "place",
        };
      } else {
        params = {
          archiveNo: item.archiveNo,
          source: item.type,
          initialArchiveNo: item.archiveNo,
        };
      }
      const { href } = this.$router.resolve({
        name: item.openName,
        query: params,
      });
      window.open(href, "_blank");
    },
    startSearch() {
      if (this.activeIndex == 22) {
        this.cloudList();
      } else {
        this.getList();
      }
    },
    getList(page = 0) {
      this.list = [];
      this.total = 0;
      this.loading = true;
      let params = {
        ...this.page,
        favoriteObjectId: this.formData.idcardNo,
        favoriteObjectType: this.markedWords.dataKey,
      };
      queryMyFavoritePageList(params)
        .then((res) => {
          res.data.entities.map((item) => {
            let type = "",
              openName = "";
            switch (this.markedWords.dataKey) {
              case 1:
                type = "people";
                openName = "people-archive";
                break;
              case 2:
                type = "video";
                openName = "video-archive";
                break;
              case 3:
                type = "vehicle";
                openName = "vehicle-archive";
                break;
              case 4:
                type = "device";
                openName = "device-archive";
                break;
              case 19:
                type = "zdr";
                openName = "people-archive";
                break;
              case 18:
                type = "non-motor-vehicle";
                openName = "non-motor-archive";
                break;
              case 20:
                type = "place";
                openName = "place-dashboard";
                break;
            }
            item.type = type;
            item.openName = openName;
          });
          if ([14, 15].includes(this.markedWords.dataKey)) {
            res.data.entities.forEach((item) => {
              var info = this.alarmConfigInfo.alarmLevelConfig.find(
                (ite) => ite.alarmLevel == item.taskLevel
              );
              item.bgIndex = Number(info.alarmColour);
            });
          }
          this.list = res.data.entities;
          this.total = res.data.total;
          this.loading = false;
          if (page == 1) {
            this.$refs.humanbody.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.humanbody.nextPage(this.dataList);
          }
        })
        .catch()
        .finally(() => {
          this.loading = false;
        });
    },
    // 收藏/取消收藏
    collection(params, flag) {
      if (this.activeIndex > 3) {
        if (flag == 1) {
          addCollection(params).then((res) => {
            this.$Message.success("收藏成功");
            this.getList();
          });
        } else {
          deleteMyFavorite(params).then((res) => {
            this.$Message.success("取消收藏成功");
            this.getList();
          });
        }
      } else {
        this.getList();
      }
    },
    // 人体抓拍，非机动车抓拍 取消收藏
    detailsCollection() {
      this.vehicleAlarmShow = false;
      this.detailShow = false;
      this.humanShow = false;
      this.getList();
    },
    pageChange(page = 0) {
      this.page.pageNumber = page;
      if (this.activeIndex == 22) {
        this.cloudList(page);
      } else {
        this.getList(page);
      }
    },
    pageSizeChange(size) {
      this.page.pageSize = size;
      this.page.pageNumber = 1;
      if (this.activeIndex == 22) {
        this.cloudList();
      } else {
        this.getList();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.collect-page {
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: inherit;
  .tree-left {
    width: 280px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px 4px 4px 4px;
    margin-right: 10px;
    .search-top {
      padding: 16px 15px;
    }
    .tree-box {
      overflow: auto;
      height: calc(~"100% - 74px");
    }
    /deep/ .el-tree-node__content {
      height: 34px;
      font-size: 14px;
      .color-bule {
        color: #2c86f8;
      }
      .color-white {
        color: #fff;
      }
    }
    /deep/.el-tree-node.is-current > .el-tree-node__content {
      background: #2c86f8 !important;
      color: #fff;
      .color-bule {
        color: #fff;
      }
    }
    /deep/ .el-tree-node__content:hover {
      background-color: #2c86f8;
      color: #fff;
      .color-bule {
        color: #fff;
      }
    }
    .slot-node {
      width: 100%;
      .node-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .name-width {
          display: block;
          width: 140px;
          // color: rgba(0,0,0,0.8);
          // font-weight: 400;
        }
        span {
          display: flex;
          align-items: center;
        }
      }
      .node-action {
        display: inline-block;
        margin-right: 10px;
        i {
          font-size: 14px;
        }
      }
    }
    .color-bule {
      color: #2c86f8;
    }
  }
  .collect {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px 4px 4px 4px;
    padding: 16px 20px 0;
    .search {
      display: flex;
      justify-content: space-between;
      .search-ul {
        border: 1px solid #2c86f8;
        display: flex;
        border-radius: 4px;
        .search-li {
          font-size: 14px;
          background: #ffffff;
          color: #2c86f8;
          padding: 7px 15px;
          border-right: 1px solid #2c86f8;
          cursor: pointer;
          &:nth-child(1) {
            border-radius: 3px 0 0 3px;
          }
          &:nth-last-child(1) {
            border-right: none;
            border-radius: 0 3px 3px 0;
          }
        }
        .tabActive {
          background: #2c86f8;
          color: #ffffff;
        }
      }
    }
    .card-content {
      display: flex;
      flex-wrap: wrap;
      overflow: auto;
      flex: 1;
      // margin: 0 -5px;
      align-content: flex-start;
      position: relative;
      // margin: 0 10px;
      .card-item {
        padding: 5px;
        margin-bottom: 10px;
        width: 25%;
        .list-card {
          width: 100%;
          height: 198px;
        }
      }
      .card-face {
        width: 12%;
        height: 242px;
        overflow: hidden;
        border-radius: 4px;
        border: 1px solid #d3d7de;
        box-shadow: 1px 1px 7px #cdcdcd;
        margin-left: 6px;
        margin-bottom: 10px;
        &:hover {
          border: 1px solid #2c86f8;
        }
      }
      .card-device {
        width: 20%;
        // height: 120px;
        padding: 3px;
        margin-bottom: 10px;
      }
      .card-alarm {
        padding: 5px;
        width: 25%;
      }
      .card-human {
        width: 12%;
        height: 242px;
        overflow: hidden;
        border-radius: 4px;
        border: 1px solid #d3d7de;
        box-shadow: 1px 1px 7px #cdcdcd;
        margin-left: 6px;
        margin-bottom: 10px;
        &:hover {
          border: 1px solid #2c86f8;
        }
        .img-content {
          height: 167px !important;
        }
      }
    }
  }
}
</style>
