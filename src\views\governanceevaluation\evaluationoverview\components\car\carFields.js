const carFieldData = [
  // 车辆卡口设备抓拍数据完整率 VEHICLE_FULL_INFO 3001
  {
    indexType: ['VEHICLE_FULL_INFO'],
    indexId: [3001],
    abnormalCountMa: [
      {
        name: '属性完整图片数量',
        value: 0,
        icon: 'icon-shuxingwanzhengtupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '属性缺失图片数量',
        value: 0,
        icon: 'icon-shuxingqueshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '车辆卡口设备抓拍数据完整率',
        value: 0,
        icon: 'icon-cheliangkakoushebeizhuapaishujuwanzhengshuai',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '属性完整' },
      { dataKey: '4__plate_no', dataValue: '车牌号缺失' },
      { dataKey: '4__plate_color', dataValue: '车牌颜色缺失' },
      { dataKey: '2__plate_no', dataValue: '车牌号格式错误' },
      { dataKey: '2__plate_color', dataValue: '车牌颜色格式错误' },
      { dataKey: '3', dataValue: '无法检测' },
    ],

    cardInfo: [
      { name: '车牌号', value: 'plateNo' },
      { name: '车牌颜色', value: 'plateColor', algorithm: 'colorType' },
      { name: '抓拍时间', value: 'shotTime' },
      { name: '抓拍地点', value: 'address' },
    ],
    addColumns: [
      {
        title: '属性缺失图片数量',
        key: 'attrMissingCount',
        className: 'font-red',
        width: 170,
      },
      {
        title: '车牌号缺失图片数量',
        key: 'missingPlateCount',
        className: 'font-red',
        width: 170,
      },
      {
        title: '车牌颜色缺失图片数量',
        key: 'missingPlateColorCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车牌号格式错误数量',
        key: 'unqualifiedPlateNoCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车牌颜色格式错误数量',
        key: 'unqualifiedPlateColorCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
        width: 160,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 重点车辆卡口设备抓拍数据完整率 VEHICLE_FULL_INFO_IMPORTANT 3002
  {
    indexType: ['VEHICLE_FULL_INFO_IMPORTANT'],
    indexId: [3002],
    abnormalCountMa: [
      {
        name: '属性完整图片数量',
        value: 0,
        icon: 'icon-shuxingwanzhengtupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '属性缺失图片数量',
        value: 0,
        icon: 'icon-shuxingqueshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '重点车辆卡口设备抓拍数据完整率',
        value: 0,
        icon: 'icon-cheliangkakoushebeizhuapaishujuwanzhengshuai',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        // qualified: true,
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '属性完整' },
      { dataKey: '4__plate_no', dataValue: '车牌号缺失' },
      { dataKey: '4__plate_color', dataValue: '车牌颜色缺失' },
      { dataKey: '4__vehicle_class', dataValue: '车辆类型缺失' },
      { dataKey: '4__vehicle_brand', dataValue: '车辆品牌缺失' },
      { dataKey: '4__vehicle_color', dataValue: '车身颜色缺失' },
      { dataKey: '4__vehicle_model', dataValue: '车辆型号缺失' },
      { dataKey: '2__plate_no', dataValue: '车牌号格式错误' },
      { dataKey: '2__plate_color', dataValue: '车牌颜色格式错误' },
      { dataKey: '2__vehicle_class', dataValue: '车辆类型格式错误' },
      { dataKey: '2__vehicle_brand', dataValue: '车辆品牌格式错误' },
      { dataKey: '2__vehicle_color', dataValue: '车身颜色格式错误' },
      { dataKey: '2__vehicle_model', dataValue: '车辆型号格式错误' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      { name: '车牌号：', value: 'plateNo' },
      { name: '车牌颜色：', value: 'plateColor', algorithm: 'colorType' },
      {
        name: '车辆类型：',
        value: 'vehicleClass',
        algorithm: 'vehicleClassType',
      },
      {
        name: '车辆品牌：',
        value: 'vehicleBrand',
        algorithm: 'vehicleBandType',
      },
      {
        name: '车身颜色：',
        value: 'vehicleColor',
        algorithm: 'colorType',
      },
      { name: '车辆型号：', value: 'vehicleModel' },
      { type: 'image', value: 'shotTime' },
      { type: 'image', value: 'address' },
    ],
    addColumns: [
      {
        title: '属性缺失图片数量',
        key: 'attrMissingCount',
        className: 'font-red',
        width: 170,
      },
      {
        title: '车牌号缺失图片数量',
        key: 'missingPlateCount',
        className: 'font-red',
        width: 170,
      },
      {
        title: '车牌颜色缺失图片数量',
        key: 'missingPlateColorCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车辆类型缺失图片数量',
        key: 'missingVehicleClassCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车辆品牌缺失图片数量',
        key: 'missingVehicleBrandCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车身颜色缺失图片数量',
        key: 'missingVehicleColorCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车辆型号缺失图片数量',
        key: 'missingVehicleModelCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车牌颜色格式错误数量',
        key: 'unqualifiedPlateColorCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车牌号格式错误数量',
        key: 'unqualifiedPlateNoCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车辆类型格式错误',
        key: 'unqualifiedVehicleClassCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车辆品牌格式错误',
        key: 'unqualifiedVehicleBrandCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车身颜色格式错误',
        key: 'unqualifiedVehicleColorCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '车辆型号格式错误',
        key: 'unqualifiedVehicleModelCount',
        className: 'font-red',
        width: 200,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 车辆卡口设备过车数据准确率 VEHICLE_INFO_PASS 3003
  {
    indexType: ['VEHICLE_INFO_PASS'],
    indexId: [3003],
    abnormalCountMa: [
      {
        name: '属性正确图片数量',
        value: 0,
        icon: 'icon-shuxingwanzhengtupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '属性错误图片数量',
        value: 0,
        icon: 'icon-shuxingqueshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '车辆卡口设备过车数据准确率',
        value: 0,
        icon: 'icon-cheliangjiegouhuashuxingyuzhunquexingyouhua',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        // qualified: true,
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '属性正确' },
      { dataKey: '2__plate_no', dataValue: '车牌号错误' },
      { dataKey: '2__plate_color', dataValue: '车牌颜色错误' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      { name: '车牌号', value: 'plateNo', than: 'votePlateNo' },
      {
        name: '车牌颜色',
        value: 'plateColor',
        than: 'votePlateColor',
        algorithm: 'colorType',
      },
      { name: '抓拍时间', value: 'shotTime' },
      { name: '抓拍地点', value: 'address' },
    ],
    addColumns: [
      {
        title: '属性错误图片数量',
        key: 'attrErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '车牌号错误图片数量',
        key: 'plateErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '车牌颜色错误图片数量',
        key: 'plateColorErrCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
        width: 160,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 车辆卡口设备时钟准确率 VEHICLE_CLOCK 3005
  {
    indexType: ['VEHICLE_CLOCK'],
    indexId: [3005],
    abnormalCountMa: [
      {
        name: '时钟准确图片数量',
        value: 0,
        icon: 'icon-shizhongzhunquetupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '时钟异常图片数量',
        value: 0,
        icon: 'icon-shizhongyichangtupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '车辆卡口设备时钟准确率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongjiance',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        // qualified: true,
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    // width: 130,
    // lable: '时钟错误图片数量',
    cardSearchList: [
      { dataKey: '1', dataValue: '时钟准确' },
      { dataKey: '2', dataValue: '时钟错误' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      { name: '抓拍时间：', value: 'shotTime' },
      { name: '接收时间：', value: 'firstIntoViewTime' },
      {
        type: 'image',
        value: 'timeAccuracyDesc',
        outcomeType: 'outcome',
      },
    ],
    addColumns: [
      { title: '时钟准确图片数量', key: 'clockSucCount' },
      {
        title: '时钟异常图片数量',
        key: 'clockErrCount',
        className: 'font-red',
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 重点车辆卡口设备主要属性准确率 VEHICLE_MAIN_PROP 3006
  {
    indexType: ['VEHICLE_MAIN_PROP'],
    indexId: [3006],
    abnormalCountMa: [
      {
        name: '属性正确图片数量',
        value: 0,
        icon: 'icon-shuxingwanzhengtupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '属性错误图片数量',
        value: 0,
        icon: 'icon-shuxingqueshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '重点车辆卡口设备主要属性准确率',
        value: 0,
        icon: 'icon-putongkakouzhuyaoshuxingjiance',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        // qualified: true,
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '属性正确' },
      { dataKey: '2__plate_no', dataValue: '车牌号错误' },
      { dataKey: '2__plate_color', dataValue: '车牌颜色错误' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      { name: '车牌号', value: 'plateNo', than: 'votePlateNo' },
      {
        name: '车牌颜色',
        value: 'plateColor',
        than: 'votePlateColor',
        algorithm: 'colorType',
      },
      { name: '抓拍时间', value: 'shotTime' },
      { name: '抓拍地点', value: 'address' },
    ],
    addColumns: [
      {
        title: '属性错误图片数量',
        key: 'attrErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '车牌号错误图片数量',
        key: 'plateErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '车牌颜色错误图片数量',
        key: 'plateColorErrCount',
        className: 'font-red',
        width: 200,
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
        width: 160,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 重点车辆卡口设备类型属性识别准确率 VEHICLE_TYPE_PROP 3007
  {
    indexType: ['VEHICLE_TYPE_PROP'],
    indexId: [3007],
    abnormalCountMa: [
      {
        name: '属性正确图片数量',
        value: 0,
        icon: 'icon-shuxingwanzhengtupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '属性错误图片数量',
        value: 0,
        icon: 'icon-shuxingqueshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '重点车辆卡口设备类型属性识别准确率',
        value: 0,
        icon: 'icon-zhongdiancheliangkakoushebeileixingshuxingshibiezhunqueshuai1',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '属性正确' },
      { dataKey: '2__vehicle_class', dataValue: '车辆类型错误' },
      { dataKey: '2__vehicle_brand', dataValue: '车辆品牌错误' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      {
        name: '车辆类型',
        value: 'vehicleClass',
        than: 'voteVehicleClass',
        algorithm: 'vehicleClassType',
      },
      {
        name: '车辆品牌',
        value: 'vehicleBrand',
        than: 'voteVehicleBrand',
        algorithm: 'vehicleBandType',
      },
      { name: '抓拍时间', value: 'shotTime' },
      { name: '抓拍地点', value: 'address' },
    ],
    addColumns: [
      {
        title: '属性错误图片数量',
        key: 'attrErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '车辆类型错误图片数量',
        key: 'vehicleClassErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '车辆品牌错误图片数量',
        key: 'vehicleBrandErrCount',
        className: 'font-red',
        width: 180,
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
        width: 160,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 车辆卡口设备及时上传率 VEHICLE_UPLOAD 3008
  {
    indexType: ['VEHICLE_UPLOAD'],
    indexId: [3008],
    abnormalCountMa: [
      {
        name: '上传及时图片数量',
        value: 0,
        icon: 'icon-shangchuanjishitupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '上传超时图片数量',
        value: 0,
        icon: 'icon-shangchuanchaoshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '车辆卡口设备及时上传率',
        value: 0,
        icon: 'icon-putongkakoushangchuanjishixingjiance',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '上传及时' },
      { dataKey: '2', dataValue: '上传超时' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      { name: '抓拍时间：', value: 'shotTime' },
      { name: '接收时间：', value: 'firstIntoViewTime' },
      { name: '延时：', value: 'delayTimeTrans', outcomeType: 'outcome' },
    ],
    addColumns: [
      {
        title: '上传及时图片数量',
        key: 'uploadTimelyCount',
        minWidth: 100,
      },
      {
        // title: `上传超时(${this.time || 0})图片数量`,
        key: 'uploadTimeOutCount',
        className: 'font-red',
        minWidth: 140,
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
        minWidth: 100,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 重点车辆卡口设备及时上传率 VEHICLE_UPLOAD_IMPORTANT 3009
  {
    indexType: ['VEHICLE_UPLOAD_IMPORTANT'],
    indexId: [3009],
    abnormalCountMa: [
      {
        name: '上传及时图片数量',
        value: 0,
        icon: 'icon-shangchuanjishitupianshuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color7',
      },
      {
        name: '上传超时图片数量',
        value: 0,
        icon: 'icon-shangchuanchaoshitupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: '无法检测图片数量',
        value: 0,
        icon: 'icon-wufajiancetupianshuliang',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'canNotDetectFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '重点车辆卡口设备及时上传率',
        value: 0,
        icon: 'icon-putongkakoushangchuanjishixingjiance',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: '上传及时' },
      { dataKey: '2', dataValue: '上传超时' },
      { dataKey: '3', dataValue: '无法检测' },
    ],
    cardInfo: [
      { name: '抓拍时间：', value: 'shotTime' },
      { name: '接收时间：', value: 'firstIntoViewTime' },
      { name: '延时：', value: 'delayTimeTrans', outcomeType: 'outcome' },
    ],
    addColumns: [
      {
        title: '上传及时图片数量',
        key: 'uploadTimelyCount',
        minWidth: 100,
      },
      {
        // title: `上传超时(${this.time || 0})图片数量`,
        key: 'uploadTimeOutCount',
        className: 'font-red',
        minWidth: 140,
      },
      {
        title: '无法检测图片数量',
        key: 'canNotCheckCount',
        className: 'font-red',
        minWidth: 100,
      },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  // 重点车辆卡口设备过车图片地址可用率 VEHICLE_URL_AVAILABLE_IMPORTANT 3011
  {
    indexType: ['VEHICLE_URL_AVAILABLE_IMPORTANT'],
    indexId: [3011],
    abnormalCountMa: [
      {
        name: 'URL可用图片数量',
        value: 0,
        icon: 'icon-URLkeyongtupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: 'URL不可用图片数量',
        value: 0,
        icon: 'icon-URLbukeyongtupianshu',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '重点车辆卡口设备过车图片地址可用率',
        value: 0,
        icon: 'icon-cheliangkakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        // qualified: true,
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: 'URL可用' },
      { dataKey: '2', dataValue: 'URL不可用', className: 'font-red' },
    ],
    cardInfo: [
      { type: 'image', value: 'shotTime' },
      { type: 'image', value: 'address' },
      { type: 'reason', value: 'reason' },
    ],
    addColumns: [
      { title: '可用图片数量', key: 'urlCanUseCount', tooltip: true, width: 140 },
      {
        title: '不可用图片数量',
        key: 'urlNotUseCount',
        className: 'font-red',
        tooltip: true,
        width: 140,
      },
      {
        title: '小图URL地址不可访问',
        key: 'urlThumbnailPathErrorTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      {
        title: '大图URL地址不可访问',
        key: 'urlLargePathErrorTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      {
        title: '小图URL地址为空',
        key: 'urlThumbnailPathNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 140,
      },
      {
        title: '大图URL地址为空',
        key: 'urlLargePathNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 140,
      },
      {
        title: '大图时间未标注数量',
        key: 'urlLargePathTimeNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      {
        title: '大图地址未标注数量',
        key: 'urlLargePathAddressNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      { title: '检测时间', key: 'startTime', tooltip: true, width: 200 },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '原因',
        key: 'description',
        tooltip: true,
        minWidth: 120,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
  /**
   * 车辆卡口设备过车图片地址可用率 3010
   * 车辆卡口设备过车图片地址可用率（人工复核）3024
   */
  {
    indexType: ['VEHICLE_URL_AVAILABLE', 'VEHICLE_URL_AVAILABLE_RECHECK'],
    indexId: [3010, 3024],
    abnormalCountMa: [
      {
        name: 'URL可用图片数量',
        value: 0,
        icon: 'icon-URLkeyongtupianshuliang',
        iconColor: 'icon-bg8',
        liBg: 'li-bg8',
        type: 'number',
        key: 'passFaceDataTotal',
        textColor: 'color8',
      },
      {
        name: 'URL不可用图片数量',
        value: 0,
        icon: 'icon-URLbukeyongtupianshu',
        iconColor: 'icon-bg10',
        liBg: 'li-bg10',
        type: 'number',
        key: 'notPassFaceDataTotal',
        textColor: 'color10',
      },
      {
        name: '车辆卡口设备过车图片地址可用率',
        value: 0,
        icon: 'icon-cheliangkakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage',
        key: 'resultValue',
        textColor: 'color3',
      },
    ],
    cardSearchList: [
      { dataKey: '1', dataValue: 'URL可用' },
      { dataKey: '2', dataValue: 'URL不可用' },
    ],
    cardInfo: [
      { name: '抓拍时间：', value: 'shotTime' },
      { name: '抓拍地址：', value: 'address' },
      { name: '访问时间：', type: 'time', value: 'imageUrlVisitTime' },
      { name: '检测结果：', type: 'reason', value: 'reason' },
    ],
    addColumns: [
      { title: '可用图片数量', key: 'urlCanUseCount', tooltip: true, width: 140 },
      {
        title: '不可用图片数量',
        key: 'urlNotUseCount',
        className: 'font-red',
        tooltip: true,
        width: 140,
      },
      {
        title: '小图URL地址不可访问',
        key: 'urlThumbnailPathErrorTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      {
        title: '大图URL地址不可访问',
        key: 'urlLargePathErrorTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      {
        title: '小图URL地址为空',
        key: 'urlThumbnailPathNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 140,
      },
      {
        title: '大图URL地址为空',
        key: 'urlLargePathNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 140,
      },
      {
        title: '大图时间未标注数量',
        key: 'urlLargePathTimeNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      {
        title: '大图地址未标注数量',
        key: 'urlLargePathAddressNullTotal',
        className: 'font-red',
        tooltip: true,
        width: 160,
      },
      { title: '检测时间', key: 'startTime', tooltip: true, width: 200 },
      { title: '合格率', slot: 'qualifiedRate', width: 120 },
      { title: '设备状态', slot: 'outcome', width: 120 },
      {
        title: '原因',
        key: 'description',
        tooltip: true,
        minWidth: 120,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 100,
        className: 'table-action-padding',
        fixed: 'right',
      },
    ],
  },
];
export default carFieldData;
