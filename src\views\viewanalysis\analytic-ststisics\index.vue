<!--
    * @FileDescription: 解析统计
    * @Author: H
    * @Date: 2024/07/22
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="anaylytic-box">
        <div class="title">
            <p>解析统计</p>
            <div class="select-time">
                <p class="statis-date">统计日期:</p>
                <DatePicker v-model="statisDate" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" @on-change="handleOnchange" @on-clear="handleClearchange" placeholder="请选择日期" style="width: 350px"></DatePicker>
            </div>
        </div>
        <div class="main-box">
            <ui-card title="解析任务统计" class="top-card" :padding="10">
                <div class="task-tital">
                    <p class="task-name">任务总数：</p>
                    <p class="task-num">{{ analysisTotal || 0 }}</p>
                </div>
                <div class="main-box-card">
                    <div class="top-box-list">
                        <img class="icon-img" :src="getImgUrl('realtime.png')" alt="">
                        <div class="box-list-right">
                            <!-- <div class="data-top">
                                <p class="data-state-name">实时结构化 <span>12</span> </p>
                            </div>
                            <div class="data-bottoms">
                                <div class="data-bottom-left">
                                    <div class="second-title">
                                        <div class="title-square"></div>
                                        <span>解析实例</span>
                                    </div>
                                    <span class="number">53</span>
                                </div>
                                <div class="data-bottom-line"></div>
                                <div class="data-bottom-right">
                                    <div class="data-living-top">
                                        <div class="state-list">
                                            <div class="data-state">人脸</div>
                                            <p class="data-number">0</p>
                                        </div>
                                        <div class="state-list">
                                            <div class="data-state">车辆</div>
                                            <p class="data-number">0</p>
                                        </div>
                                        <div class="state-list">
                                            <div class="data-state">人体</div>
                                            <p class="data-number">0</p>
                                        </div>
                                        <div class="state-list">
                                            <div class="data-state">非机动车</div>
                                            <p class="data-number">0</p>
                                        </div>
                                    </div>
                                    <div class="data-living-bottom">
                                        <div class="state-list operation">
                                            <div class="data-state">
                                                <div class="data-circle"></div>
                                                <p>运行中</p>
                                            </div>
                                            <p class="data-number">{{ analysisRealTime.running || 0 }}</p>
                                        </div>
                                        <div class="state-list stop">
                                            <div class="data-state">
                                                <div class="data-circle"></div>
                                                <p>已停止</p>
                                            </div>
                                            <p class="data-number">{{ analysisRealTime.stopped || 0 }}</p>
                                        </div>
                                        <div class="state-list layoff">
                                            <div class="data-state">
                                                <div class="data-circle"></div>
                                                <p>已完成</p>
                                            </div>
                                            <p class="data-number">{{ analysisRealTime.completed || 0 }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div> -->
                            <div class="data-top">
                                <p class="data-state-name">实时结构化</p>
                                <p>{{ analysisRealTime.count || 0 }}</p>
                            </div>
                            <div class="data-bottom">
                                <div class="state-list operation">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>运行中</p>
                                    </div>
                                    <p class="data-number">{{ analysisRealTime.running || 0 }}</p>
                                </div>
                                <div class="state-list stop">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>已停止</p>
                                    </div>
                                    <p class="data-number">{{ analysisRealTime.stopped || 0 }}</p>
                                </div>
                                <div class="state-list layoff">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>已完成</p>
                                    </div>
                                    <p class="data-number">{{ analysisRealTime.completed || 0 }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="top-box-list">
                        <img class="icon-img" :src="getImgUrl('history.png')" alt="">
                        <div class="box-list-right">
                            <div class="data-top">
                                <p class="data-state-name">历史结构化</p>
                                <p>{{ analysisHistory.count || 0 }}</p>
                            </div>
                            <div class="data-bottom">
                                <div class="state-list operation">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>运行中</p>
                                    </div>
                                    <p class="data-number">{{ analysisHistory.running || 0 }}</p>
                                </div>
                                <div class="state-list stop">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>已停止</p>
                                    </div>
                                    <p class="data-number">{{ analysisHistory.stopped || 0 }}</p>
                                </div>
                                <div class="state-list layoff">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>已完成</p>
                                    </div>
                                    <p class="data-number">{{ analysisHistory.completed || 0 }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="top-box-list">
                        <img class="icon-img" :src="getImgUrl('file.png')" alt="">
                        <div class="box-list-right">
                            <div class="data-top">
                                <p class="data-state-name">文件结构化</p>
                                <p>{{ analysisFile.count || 0 }}</p>
                            </div>
                            <div class="data-bottom">
                                <div class="state-list operation">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>运行中</p>
                                    </div>
                                    <p class="data-number">{{ analysisFile.running || 0 }}</p>
                                </div>
                                <div class="state-list stop">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>已停止</p>
                                    </div>
                                    <p class="data-number">{{ analysisFile.stopped || 0 }}</p>
                                </div>
                                <div class="state-list layoff">
                                    <div class="data-state">
                                        <div class="data-circle"></div>
                                        <p>已完成</p>
                                    </div>
                                    <p class="data-number">{{ analysisFile.completed || 0 }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </ui-card>
            <ui-card title="任务审批状态统计" class="center-card" :padding="5">
                <div class="main-box-card">
                    <div class="center-box-list">
                        <pie-chart ref="reviewRealTimeChart" title="实时结构化" :picData="costList" :urlType="0"></pie-chart>
                    </div>
                    <div class="box-line"></div>
                    <div class="center-box-list">
                        <pie-chart ref="reviewHistoryChart" title="历史结构化" :picData="costList" :urlType="1"></pie-chart>
                    </div>
                    <div class="box-line"></div>
                    <div class="center-box-list">
                        <pie-chart ref="reviewFileChart" title="文件结构化" :picData="costList" :urlType="2"></pie-chart>
                    </div>
                </div>
            </ui-card>
            <ui-card title="任务发起人TOP3统计" class="bottom-card" :padding="5">
                <div class="main-box-card">
                    <div class="center-box-list">
                        <rangking-chart ref="teskRealTimeChart" title="实时结构化" :rangData="rangData"></rangking-chart>  
                    </div>
                    <div class="box-line"></div>
                    <div class="center-box-list">
                        <rangking-chart ref="taskHistoryChart" title="历史结构化" :rangData="rangData"></rangking-chart>  
                    </div>
                    <div class="box-line"></div>
                    <div class="center-box-list">
                        <rangking-chart ref="taskFileChart" title="文件结构化" :rangData="rangData"></rangking-chart>  
                    </div>
                </div>
            </ui-card>
        </div>
    </div>
</template>
<script>
import pieChart from './components/pie-chart.vue';
import rangkingChart from './components/rangking-chart.vue';
import { structureJobCount, structureJobStatusCount, taskInitiationStatistics, taskReviewStatusStatistics } from '@/api/viewAnalysis';
export default{
    components: {
        pieChart,
        rangkingChart
    },
    data() {
        return{
            costList: [
                { value: '31.35', name: '未审核', type: 'running' },
                { value: '33.85', name: '驳回', type: 'stopped' },
                { value: '34.83', name: '通过', type: 'completed' },
            ],
            analysisTotal: 0,
            rangData: [],
            analysisRealTime: {},
            analysisHistory: {},
            analysisFile: {},
            taskRealTime: {},
            taskHistory: {},
            taskFile: {},
            statisDate: []
        }
    },
    created() {
        this.init();
        this.queryStructure()
    },
    methods: {
        init(startTime= '', endTime= '') {
            let params = {
                "startTime": startTime,
	            "endTime": endTime
            }
            // analysisTaskStatistics(params)
            // .then(res => {
            //     this.analysisTotal = res.data.count;
            //     this.analysisRealTime = res.data.realTimeStructure;
            //     this.analysisHistory = res.data.historyStructure;
            //     this.analysisFile = res.data.fileStructure;
            // })
            taskInitiationStatistics(params)
            .then(res => {
                this.$refs.teskRealTimeChart.init(res.data.realTimeStructure);
                this.$refs.taskHistoryChart.init(res.data.historyStructure);
                this.$refs.taskFileChart.init(res.data.fileStructure);
            })
            taskReviewStatusStatistics(params)
            .then(res => {
                this.$refs.reviewRealTimeChart.init(res.data.realTimeStructure);
                this.$refs.reviewHistoryChart.init(res.data.historyStructure);
                this.$refs.reviewFileChart.init(res.data.fileStructure);
            })
        },
        queryStructure(startTime= '', endTime= '', type = 1) {
            if(type > 3) {
                this.analysisTotal = this.analysisRealTime.count + this.analysisHistory.count + this.analysisFile.count;
                return
            } 
            let params = {
                "startTime": startTime,
	            "endTime": endTime,
                "structureJobType": type
            }
            Promise.all([
                structureJobCount(params)
                .then(res => {
                    if(type == 1) {
                        this.analysisRealTime.count = res.data; 
                    } else if(type == 2) {
                        this.analysisHistory.count = res.data; 
                    } else if(type == 3) {
                        this.analysisFile.count = res.data; 
                    }
                }),
                structureJobStatusCount(params)
                .then(res => {
                    if(type == 1) {
                        this.analysisRealTime = {...this.analysisRealTime, ...res.data}; 
                    } else if(type == 2) {
                        this.analysisHistory = {...this.analysisHistory, ...res.data};
                    } else if(type == 3) {
                        this.analysisFile = {...this.analysisFile, ...res.data};
                    }
                })
            ]).then(results => {
                this.queryStructure(startTime, endTime, type+1)
            })
        },
        handleOnchange() {
            let startTime = new Date(this.statisDate[0]).getTime();
            let endTime = new Date(this.statisDate[1]).getTime();
            this.init(startTime, endTime);
            this.queryStructure(startTime, endTime, 1)
        },
        handleClearchange() {
            this.init('', '')
            this.queryStructure('', '', 1)
        },
        getImgUrl(val) {
            return require(`@/assets/img/card/${val}`)
        },
    }
}
</script>
<style lang="less" scoped>
.anaylytic-box{
    height: 100%;
    width: 100%;
    position: relative;
    .title{
        font-size: 16px;
        font-weight: bold;
        // color: rgba(0,0,0,0.9);
        height: 40px;
        position: relative;
        line-height: 40px;
        padding-left: 10px;
        border-bottom: 1px solid #D3D7DE;
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        z-index: 1;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 20px;
            top: 50%;
            transform: translateY(-50%);
            left: 0px;
            background: #2c86f8;
        }
        span{
            color: #2C86F8; 
        }
        /deep/.ivu-icon-ios-close{
            font-size: 30px;
            cursor: pointer;
        }
        .select-time{
            display: flex;
            color: rgba(0, 0, 0, 0.4);
            align-items: center;
            .statis-date{
                font-size: 14px;
                font-weight: 400;
                margin-right: 10px;
            }
        }
    }
    .main-box{
        width: 100%;
        height: calc(~"100% - 40px");
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        /deep/.ui-card {
            width: 100%;
            flex: unset;
            .card-content {
                display: flex;
                height: calc(~"100% - 30px");
            }
            .radar-echart {
                width: 50%;
                height: 100%;
            }
        }
        .top-card{
            height: 27%;
            .task-tital{
                position: absolute;
                top: -28px;
                right: 10px;
                display: flex;
                align-items: center;
                .task-name{
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0,0,0,0.75);
                }
                .task-num{
                    font-size: 20px;
                    font-weight: 700;
                    color: #2C86F8;
                }
            }
        }
        .center-card{
            height: 39%;
        }
        .bottom-card{
            height: 33%;
        }
        .main-box-card{
            width: 100%;
            display: flex;
            justify-content: space-around;
            .top-box-list{
                width: 33%;
                display: flex;
                background: #F9F9F9;
                border-radius: 4px 4px 4px 4px;
                align-items: center;
                padding-left: 40px;
                .icon-img{
                    width: 140px;
                    height: 140px;
                }
                .box-list-right{
                    margin-left: 40px;
                    flex: 1;
                    .data-top{
                        color: #2C86F8;
                        font-size: 32px;
                        font-weight: 700;
                        .data-state-name{
                            color: rgba(0, 0, 0, 0.8);
                            font-size: 16px;
                            font-weight: 700;
                            span{
                                color: #2C86F8;
                                font-size: 18px;
                            }
                        }
                    }
                    .data-bottoms {
                        margin-top: 10px;
                        .data-bottom-left{
                            font-size: 15px;
                            display: flex;
                            .second-title{
                                display: flex;
                                align-items: center;
                                .title-square{
                                    width: 10px;
                                    height: 10px;
                                    background: #27B5FF;
                                    margin-right: 10px;
                                }
                            }
                            .number{
                                margin-left: 20px;
                                color: #08E0FF;
                                font-weight: 700;
                            }
                        }
                        .data-bottom-line{
                            width: 100%;
                            height: 1px;
                            border: 1px dashed #D3D7DE;
                        }
                        .data-bottom-right{
                            .data-living-top{
                                display: flex;
                                justify-content: space-between;
                                padding: 0 10px;
                                .state-list{
                                    .data-state{
                                        // color: #ffffff;
                                        font-size: 14px;
                                    }
                                    .data-number{
                                        font-size: 15px;
                                        color: #F29F4C;
                                        font-weight: 700;
                                    }
                                }
                            }
                            .data-living-bottom{
                                display: flex;
                                justify-content: space-between;
                                padding: 3px 10px;
                                .state-list{
                                    .data-state{
                                        display: flex;
                                        align-items: center;
                                        color: rgba(0, 0, 0, 0.8);
                                        font-size: 14px;
                                        .data-circle{
                                            width: 10px;
                                            height: 10px;
                                            border-radius: 50%;
                                            margin-right: 5px;
                                        }
                                    }
                                    .data-number{
                                        font-size: 20px;
                                        font-weight: 700;
                                        text-align: center;
                                    }
                                }
                                .operation{
                                    color: #48BAFF;
                                    .data-circle{
                                        background: #48BAFF;
                                    }
                                }
                                .stop{
                                    color: #EA4A36;
                                    .data-circle{
                                        background: #EA4A36;
                                    }
                                }
                                .layoff{
                                    color: #1FAF81;
                                    .data-circle{
                                        background: #1FAF81;
                                    }
                                }
                            }
                        }
                    }
                    .data-bottom{
                        display: flex;
                        justify-content: space-between;
                        padding-right: 40px;
                        .state-list{
                            .data-state{
                                display: flex;
                                align-items: center;
                                color: rgba(0, 0, 0, 0.8);
                                font-size: 14px;
                                .data-circle{
                                    width: 10px;
                                    height: 10px;
                                    border-radius: 50%;
                                    margin-right: 5px;
                                }
                            }
                            .data-number{
                                font-size: 24px;
                                font-weight: 700;
                                text-align: center;
                            }
                        }
                        .operation{
                            color: #48BAFF;
                            .data-circle{
                                background: #48BAFF;
                            }
                        }
                        .stop{
                            color: #EA4A36;
                            .data-circle{
                                background: #EA4A36;
                            }
                        }
                        .layoff{
                            color: #1FAF81;
                            .data-circle{
                                background: #1FAF81;
                            }
                        }
                    }
                }
            }
            .box-line{
                width: 1px;
                height: 100%;
                border: 1px dashed #D3D7DE;
            }
            .center-box-list{
                width: 33%;
                height: 100%;
            }
        }
        
    }
}
</style>