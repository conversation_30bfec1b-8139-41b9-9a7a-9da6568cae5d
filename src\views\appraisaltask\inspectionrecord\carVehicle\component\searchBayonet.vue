<template>
  <div class="base-search">
    <div>
      <ui-label class="inline mb10" label="组织机构" :width="70">
        <ApiOrganizationTree
          style="width: auto !important"
          :taskObj="taskObj"
          :select-tree="searchData"
          :treeData="treeData"
          placeholder="请选择组织机构"
        >
        </ApiOrganizationTree>
      </ui-label>
      <ui-label class="inline ml-lg" label="设备编码" :width="70">
        <Input v-model="searchData.deviceId" class="width-md" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="设备名称" :width="70">
        <Input v-model="searchData.deviceName" class="width-md" placeholder="请输入设备名称"></Input>
      </ui-label>
      <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="inline ml-lg">
        <Select
          class="width-sm"
          v-model="searchData.sbdwlx"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>

      <ui-label class="inline ml-lg" label="设备联网状态" :width="100" v-if="currentTree.id === 414">
        <Select v-model="searchData.deviceStatus" clearable placeholder="请选择" class="input-width">
          <Option :value="1">已联网</Option>
          <Option :value="2">未联网</Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg" :label="global.filedEnum.phyStatus" :width="70" v-else>
        <Select v-model="searchData.deviceStatus" clearable placeholder="请选择" class="input-width">
          <Option :value="1">在线</Option>
          <Option :value="2">离线</Option>
        </Select>
      </ui-label>
    </div>
    <div>
      <!-- <ui-label class="inline" label=" "> -->
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
      <!-- </ui-label> -->
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        idCard: '',
        name: '',
        orgCode: '',
      },
    };
  },
  created() {},
  mounted() {
    // this.$http
    //   .get(governanceevaluation.getOrgDataByRegioncode, {
    //     params: { regioncode: this.taskObj.regionCode },
    //   })
    //   .then((res) => {
    //       this.treeData = this.$util.common.arrayToJson(
    //       JSON.parse(JSON.stringify(res.data.data)),
    //       'id',
    //       'parentId'
    //     )
    //   })
    // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
    // this.searchData.orgCode = orgCode || ''
  },
  methods: {
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        name: '',
        idCard: '',
        orgCode: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  components: {
    ApiOrganizationTree: require('../../components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  padding-bottom: 16px;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
}
.mt4 {
  margin-top: 4px;
}
.exportBtn {
  float: right;
  margin-top: 3px;

  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
.width-md {
  width: 150px !important;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
