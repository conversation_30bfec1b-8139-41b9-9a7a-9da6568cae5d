/**
 * 图上作战/ 模型集市
 * 搜索条件一地图联动
 * 轨迹中心-上图数据
 */
export default {
  namespaced: true,
  state: {
    resourceCoverage: false, //判断资源图层是否可以点击
    collectJudge: "", // 普通搜索列表收藏和地图模态框收藏联动
    clickObj: {}, //当前模态框显示的数据
    peerList: [], // 查出同行的所有数据
    trackList: [], //轨迹数据
    peerData: {
      peerIndex: -1,
      type: "",
      menuType: "",
    },
    upImageData: [], // 已经上图的数据
    machineInfo: {
      // 小智轨迹查询
      type: 0, // 1: 人员轨迹， 2：车辆轨迹
      info: {}, // 传参
    },
  },
  getters: {
    getResourceCoverage(state) {
      return state.resourceCoverage;
    },
    getCollectJudge(state) {
      return state.collectJudge;
    },
    getClickObj(state) {
      return state.clickObj;
    },
    getPeerList(state) {
      return state.peerList;
    },
    getPeerData(state) {
      return state.peerData;
    },
    getTrackList(state) {
      return state.trackList;
    },
    getUpImageData(state) {
      return state.upImageData;
    },
    getMachineInfo(state) {
      return state.machineInfo;
    },
  },
  mutations: {
    setResourceCoverage(state, resourceCoverage) {
      state.resourceCoverage = resourceCoverage;
    },
    setCollectJudge(state, collectJudge) {
      state.collectJudge = collectJudge;
    },
    setClickObj(state, clickObj) {
      state.clickObj = clickObj;
    },
    setPeerList(state, peerList) {
      state.peerList = peerList;
    },
    setPeerData(state, peerData) {
      state.peerData = peerData;
    },
    setTrackList(state, trackList) {
      state.trackList = trackList;
    },
    setUpImageData(state, data = []) {
      // 在map-track.vue中会直接使用splice操作赋的值，所以这里需要deepClone一下
      state.upImageData = JSON.parse(JSON.stringify(data));
    },
    setMachineInfo(state, info) {
      state.machineInfo = info;
    },
  },
  actions: {},
};
