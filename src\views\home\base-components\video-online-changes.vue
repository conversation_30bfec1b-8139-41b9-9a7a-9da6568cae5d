<template>
  <!-- 视频监控||人卡||车卡 在线变化 -->
  <div
    class="body-container"
    v-ui-loading="{ loading: echartsLoading, tableData: showBack ? barDetailsData : tendencyEchartData }"
  >
    <!-- 在线变化趋势图 -->
    <draw-echarts
      v-show="!showBack && tendencyEchartData.length"
      :echart-style="echartStyle"
      :echart-option="echartOption"
      ref="onlineTrendEchart"
    ></draw-echarts>
    <!-- 详情 - 柱状图 -->
    <draw-echarts
      v-show="showBack"
      class="echarts"
      :echart-style="echartStyle"
      :echart-option="detailBarEchartOption"
      ref="detailBarEchartRef"
    ></draw-echarts>
    <span class="next-echart" v-if="showNextIcon">
      <i class="icon-font icon-zuojiantou1 f-12" @click="changeShowData"></i>
    </span>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import dataZoom from '@/mixins/data-zoom';
import xaxisLabelWorkNum from '@/mixins/xaxis-label-work-num';
import { mapGetters } from 'vuex';

export default {
  name: 'OnlineChanges',
  mixins: [dataZoom, xaxisLabelWorkNum],
  props: {
    year: {
      default: '',
    },
    evaluationIndexResult: {},
    componentName: {
      default: '',
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  data() {
    return {
      echartStyle: {
        width: '1000%',
        height: '1000%',
      },
      echartsLoading: false,
      showBack: false, // 返回
      echartOption: {},
      detailBarEchartOption: {},
      tendencyEchartData: [],
      barDetailsData: [],
      legendList: [
        { name: '新增离线', key: 'newOfflineQuantity', color: ['#D00131', 'rgba(208, 1, 49, 0.3)'] },
        { name: '恢复在线', key: 'restoreOnlineQuantity', color: ['#03EC7B', 'rgba(3, 236, 123, 0.3)'] },
      ],
      colorListGradient: [
        ['rgba(208, 1, 49, 0.8)', 'rgba(208, 1, 49, 0.1)'],
        ['rgba(3, 236, 123, 0.8)', 'rgba(3, 236, 123, 0.1)'],
      ],
      colorList: ['#D00131', '#03EC7B'],
      xAxisData: [],
      componentIndexId: {
        VideoOnlineChanges: 4009, //视频监控在线变化  ---> 普通实时视频可调阅率 4009
        FaceOnlineChanges: 2008, //人卡在线变化
        VehicleOnlineChanges: 3013, //车卡在线变化
      },
      echartNum: 31,
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
    showNextIcon() {
      return (
        (!this.showBack && this.tendencyEchartData.length > this.echartNum) ||
        (this.showBack && this.barDetailsData.length > this.echartNum)
      );
    },
  },
  created() {},
  watch: {
    showBack(val) {
      if (!val) {
        this.initEchartsOption();
      }
    },
    evaluationIndexResult: {
      handler(val) {
        if (!val?.length) return;
        this.initAll();
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    changeShowData() {
      if (this.showBack) {
        this.scrollRight('detailBarEchartRef', this.barDetailsData, [], this.echartNum);
      } else {
        this.scrollRight('onlineTrendEchart', this.tendencyEchartData, [], this.echartNum);
      }
    },
    getIndexItem(indexId) {
      return this.evaluationIndexResult.find((item) => item.indexId === indexId);
    },
    initAll() {
      this.showBack = false;
      this.initEchartsOption();
    },
    // 趋势图 数据
    async initEchartsOption() {
      try {
        let indexId = this.componentIndexId[this.componentName];
        if (!this.getIndexItem(indexId)) return;
        let isIndexIdItem = this.getIndexItem(indexId);
        this.echartsLoading = true;
        this.tendencyEchartData = [];
        let data = {
          indexId: indexId,
          batchId: isIndexIdItem?.batchId,
          access: 'TASK_RESULT',
          displayType: 'REGION',
          yearAndMonth: this.year,
          orgRegionCode: isIndexIdItem?.civilCode,
          form: 'home', // 正序，不会拼上“最终变化”的数据
        };
        let res = await this.$http.post(evaluationoverview.getContrastResultData, data);
        let arr = res?.data?.data || [];

        // 处理 series、 x轴 数据
        let seriesData = this.handlerData(arr);

        this.tendencyEchartData = seriesData; // 单项的数据，不同的 项，取值不同而已，都是从同一个对象取值
        this.setEchartOption();
      } catch (e) {
        // console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    /**
     * x轴数据为：   所有对比的时间，若同一天存在 多个检测批次，则 显示为  day.1   day.2 ...
     * series中的data， 特殊情况为： 比对时间中 可能存在  该时间并没有检测，那么要 设置为 null
     */
    handlerData(arr) {
      let allDay = []; // 存放 所有比对时间
      let detectDay = []; // 存放 检测批次的时间
      let setObj = {}; // 记录 某天检测的次数
      arr.forEach((item) => {
        detectDay.push(item.day);
        allDay.push(item.day);
        if (setObj[item.day]) {
          setObj[item.day] = setObj[item.day] + 1;
          item.seftIndex = setObj[item.day];
        } else {
          setObj[item.day] = 1;
          item.seftIndex = 1;
        }
        // 2023.7.10  去除 被比对时间
        // if (item?.contrastExtDataBoList?.length > 0) {
        //   item.contrastExtDataBoList.forEach((c) => {
        //     allDay.push(c.day);
        //   });
        // }
      });
      allDay = [...new Set(allDay)].sort((a, b) => {
        return a - b;
      });
      this.xAxisData = []; // x轴
      allDay.forEach((t) => {
        if (setObj[t] > 1) {
          for (let i = 1; i <= setObj[t]; i++) {
            this.xAxisData.push({
              value: `${t}{b|(${i})}`,
              day: t,
              index: i,
              textStyle: {
                rich: {
                  b: {
                    color: '#ffa700',
                  },
                },
              },
            });
          }
        } else {
          this.xAxisData.push({ value: t, day: t, index: 1 });
        }
      });

      //  根据 x轴 设置每项 series.data 的数据，最后返回 格式为： [{...}, {...}, null, ...]
      let seriesData = [];
      this.xAxisData.forEach((x) => {
        if (detectDay.includes(x.day)) {
          arr.forEach((a) => {
            if (a.seftIndex === x.index && a.day === x.day) {
              seriesData.push(a);
            }
          });
        } else {
          seriesData.push(null);
        }
      });
      return seriesData;
    },
    // 处理 趋势图的 options
    setEchartOption() {
      let series = this.legendList.map((item, index) => {
        let seriesData = [];
        this.tendencyEchartData.forEach((t) => {
          seriesData.push({
            ...t,
            value: t?.[item.key],
          });
        });
        return {
          name: item.name,
          type: 'line',
          data: seriesData,
          // showSymbol: false,
          // smooth: true,
          lineStyle: {
            width: this.$util.common.fontSize(2),
            color: this.colorList[index % this.colorList.length], //线条颜色
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 1,
            shadowOffsetY: 1,
          },
          areaStyle: {
            //区域填充样式
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: this.colorListGradient[index][0],
                },
                {
                  offset: 1,
                  color: this.colorListGradient[index][1],
                },
              ],
              false,
            ),
          },
          itemStyle: {
            color: this.colorList[index % this.colorList.length],
            borderColor: this.colorList[index % this.colorList.length],
            borderWidth: 2,
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 5,
            shadowOffsetX: 0,
            shadowOffsetXY: 0,
          },
        };
      });

      let opts = {
        data: series,
        xAxisData: this.xAxisData,
        filterTime: this.year,
      };

      /**
       * tooltip  【详情】 点击时触发
       * @param {String} filterTime  格式：年月--'2023-06'
       * @param {Array} data  formatter 返回的数据集  其中： data[i].axisValue 为x轴
       */
      let callback = (data, filterTime) => {
        this.initBarDetailsData(data, filterTime);
      };

      this.echartOption = this.$util.doEcharts.baseHomeVideoOnlineChanges(opts, callback);
      setTimeout(() => {
        this.setDataZoom('onlineTrendEchart', [], this.echartNum);
      });
    },
    // 详情 -- 柱状图数据
    async initBarDetailsData(itemData, filterTime) {
      try {
        this.showBack = true;
        this.echartsLoading = true;
        this.barDetailsData = [];

        let indexId = this.componentIndexId[this.componentName];
        let isIndexIdItem = this.getIndexItem(indexId);
        let data = {
          indexId: indexId,
          batchId: itemData[0].data.batchId,
          access: 'TASK_RESULT',
          displayType: 'REGION',
          orgRegionCode: isIndexIdItem?.civilCode,
        };
        let res = await this.$http.post(evaluationoverview.queryOnlineChangeDetail, data);
        this.barDetailsData = res?.data?.data || [];
        this.setBarDetailsEchartOption(itemData, filterTime);
      } catch (error) {
        // console.log(error);
      } finally {
        this.echartsLoading = false;
      }
    },
    setBarDetailsEchartOption(data) {
      try {
        let xAxisData = [];
        let allSeriesData = {
          newOfflineQuantity: [], // 新增离线
          restoreOnlineQuantity: [], // 恢复在线
        };
        this.barDetailsData.forEach((item) => {
          allSeriesData.newOfflineQuantity.push({
            name: item.orgRegionName,
            value: item.newOfflineQuantity,
            // 动态设置边框
            itemStyle: {
              borderWidth: item.newOfflineQuantity > 0 ? 1 : 0,
              borderColor: '#D00131',
            },
          });
          allSeriesData.restoreOnlineQuantity.push({
            name: item.orgRegionName,
            value: item.restoreOnlineQuantity,
            itemStyle: {
              borderWidth: item.restoreOnlineQuantity > 0 ? 1 : 0,
              borderColor: '#03EC7B',
            },
          });
          xAxisData.push(item.orgRegionName);
        });
        let opts = {
          xAxisData: xAxisData,
          seriesData: this.legendList.map((item) => {
            return {
              name: item.name,
              color: item.color,
              data: allSeriesData[item.key],
            };
          }),
          startTime: this.getDate('left', data),
          endTime: this.getDate('right', data),
        };

        this.detailBarEchartOption = this.$util.doEcharts.baseHomeVideoOnlineChangesDetails(opts);
        setTimeout(() => {
          this.setDataZoom('detailBarEchartRef', [], this.echartNum);
          this.resetXaxisLabelNumFn(
            'detailBarEchartRef',
            this.detailBarEchartOption,
            this.barDetailsData.length > this.echartNum ? this.echartNum : this.barDetailsData.length,
          );
        });
      } catch (error) {
        // console.log(error);
      }
    },
    getDate(str = '', data) {
      let index = str === 'left' ? 0 : 1;
      let text = data[0].data?.contrastExtDataBoList?.[index].detectDate;
      return text;
    },
  },
};
</script>
<style lang="less" scoped>
.body-container {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .echarts {
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
