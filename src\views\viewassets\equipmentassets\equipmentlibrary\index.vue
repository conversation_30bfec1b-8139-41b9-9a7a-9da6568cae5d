<template>
  <div class="auto-fill equipmentlibrary">
    <EquipmentList />
  </div>
</template>
<script>
export default {
  name: 'equipmentlibrary',
  components: {
    EquipmentList: require('./equipmentList').default,
  },
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.equipmentlibrary {
  position: relative;
  .tabs {
    position: absolute;
    right: 30px;
    top: 20px;
    z-index: 11;
  }
}
</style>
