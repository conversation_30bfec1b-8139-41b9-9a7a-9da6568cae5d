<template>
  <div class="inline api-organization-tree" v-clickoutside="dropHide">
    <span v-if="!!selectTree.title" class="f-12">{{ selectTree.title }}</span>
    <Dropdown trigger="custom" :visible="visible">
      <div class="ivu-select ivu-select-single select-width t-left" @mouseenter="mouseenter" @mouseleave="mouseleave">
        <div class="ivu-select-selection" @click="dropShow">
          <div>
            <span class="ivu-select-placeholder" v-if="!treeText">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="treeText">{{ treeText }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!isClose || (isClose && !needClearable)"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="isClose && needClearable" />
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <template v-if="custormNode">
          <div
            v-for="(item, index) in custormNodeData"
            :key="index"
            :class="['custorm-tree-node', selectTree.orgCode === item.orgCode ? 'custom-selected' : '']"
            @click="handleCustormNode(item)"
          >
            {{ item.label }}
          </div>
        </template>
        <el-tree
          class="tree"
          node-key="id"
          ref="treeRef"
          :style="treeStyle"
          :data="newTreeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultKeys"
          @node-click="handleNodeClick"
          @current-change="currentChange"
        >
          <span class="custom-tree-node" slot-scope="{ node }">
            <span
              :class="{
                'nav-not-selected': node.disabled,
                allowed: node.disabled,
              }"
              >{{ node.label }}</span
            >
          </span>
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
.select-width {
  width: 200px;
}
.tree {
  min-width: 200px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 500px;
}
.custorm-tree-node {
  height: 34px;
  width: 100%;
  padding: 0 6px;
  color: var(--color-select-item);
  cursor: pointer;
  background: var(--bg-el-tree-node-is-expanded);
  &:hover {
    background: var(--bg-select-item-hover);
  }
  &.custom-selected {
    background: var(--bg-select-item-active);
  }
}
@{_deep} .el-tree-node__content {
  height: 34px;
}
</style>
<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      visible: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      defaultKeys: [],
      isClose: false, //清空按钮是否显示
      defaultedLabel: '',
      newTreeData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    mouseenter() {
      if (this.treeText) {
        this.isClose = true;
      }
    },
    mouseleave() {
      this.isClose = false;
    },
    // 节点选中触发
    handleNodeClick(data) {
      if (data.disabled) {
        this.$Message.error('您没有此组织机构权限');
        return false;
      }
      this.selectTree.orgCode = data.orgCode;
      this.$emit('selectedTree', data);
    },
    dropHide() {
      this.visible = false;
    },
    dropShow() {
      this.visible = !this.visible;
    },
    currentChange(data) {
      if (data.disabled) {
        return false;
      }
      this.visible = false;
    },
    clear() {
      this.reset();
      this.$emit('selectedTree', this.selectTree);
    },
    reset() {
      this.selectTree.orgCode = null;
      this.isClose = false;
    },
    handleCustormNode(data) {
      this.selectTree.orgCode = data.orgCode;
      this.$refs.treeRef.setCurrentKey(null);
      this.$emit('selectedTree', data);
      this.visible = false;
    },
    // 根据传入的code获取下级code
    filterTree() {
      if (!this.filterTreeCode) {
        return (this.newTreeData = this.treeData);
      }
      //1、找到id
      let org = this.initialOrgList.find((item) => this.filterTreeCode === item.orgCode);
      if (!org) return;
      let treeData = this.initialOrgList.filter((item) => {
        return item.id === org.id || item.parentId === org.id;
      });
      this.newTreeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(treeData)), 'id', 'parentId');
    },
  },
  watch: {
    treeData(val) {
      if (val.length !== 0) {
        this.defaultKeys = val.map((row) => {
          return row.orgCode;
        });
        this.filterTree();
      }
    },
    filterTreeCode: {
      handler() {
        if (this.treeData.length) {
          this.filterTree();
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      initialOrgList: 'common/getInitialOrgList',
    }),
    treeText() {
      let value = '';
      const node = this.custormNodeData.find((node) => node.orgCode === this.selectTree.orgCode);
      if (this.custormNode && node) {
        value = node.label;
      } else {
        let node = this.initialOrgList.find((row) => {
          return row.orgCode === this.selectTree.orgCode;
        });
        value = node ? node[this.defaultProps.label] : '';
      }
      return value;
    },
  },
  props: {
    /**
     * selectTree.orgCode: 选中的值
     */
    selectTree: {
      required: true,
    },
    // 树结构style
    treeStyle: {},
    placeholder: {
      type: String,
      default: () => {
        return '请选择';
      },
    },
    // 是否自定节点
    custormNode: {
      type: Boolean,
      default: false,
    },
    // 自定义节点数据
    custormNodeData: {
      type: Array,
      default: () => [],
    },
    // 根据filterTreeCode截取部分行政区划树展示
    filterTreeCode: {},
    needClearable: {
      type: Boolean,
      default: true,
    },
  },
  components: {},
};
</script>
