<template>
    <Modal ref="modal" title="pfs视频" v-model="isShow" class="modalExtendClass" width="1000" @on-cancel="modalClosed">
        <div class="H5Player">
            <h5-player ref="H5Player"
                sceneFrom="singleVideo"
                :options="{layout: '1*1'}">
            </h5-player>
        </div>
        <div slot="footer" style="padding:10px;text-align:center;" class="nui-border">
            <Button color="ghost" @click="saveTime">保存</Button>
        </div>
    </Modal>
</template>

<script>
    export default {
        name: "filePlayer",
        props: {
            value: {
                type: Boolean,
                require: true
            },
            editVideoValue: {
                type: Object,
                require: () => {}
            }
        },
        watch: {
            value: {
                handler(val) {
                    this.isShow = val;
                    if (val) this.initPlayer();
                },
                immediate: true
            },
            isShow(val) {
                this.$emit('input', val)
            },
        },
        data() {
            return  {
                isShow: false,
            }
        },
        methods: {
            initPlayer() {
                console.log(
                    "PFSvideoData=",
                    this.editVideoValue.decladdedFilePfsPath
                );
                if (!this.editVideoValue.decladdedFilePfsPath) {
                    console.log("PFSvideoData=无PFS路径");
                    return;
                }
                this.$nextTick(() => {
                    var pfsIp = this.editVideoValue.pfsIp;
                    this.$refs.H5Player.playPfs(0, {
                        path: this.editVideoValue.decladdedFilePfsPath,
                        title: this.editVideoValue.name,
                        server:{
                            ip:pfsIp
                        }
                    });
                    this.$refs.H5Player.videoObj.setRatio(0, "4:3");
                })
            },
            modalClosed() {
                this.$emit('playerClose');
            },
            saveTime() {
                this.$emit("filePlaySave", this.editVideoValue)
            }
        }
    }
</script>

<style lang="less">
.H5Player {
    width: 100%;
    height: 600px;
}
.player-watermk {
    left: 0;
}
</style>
