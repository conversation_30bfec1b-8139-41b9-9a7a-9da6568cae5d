<template>
  <div class="statistics-list auto-fill">
    <title-bar
      v-bind="$attrs"
      :index-data="indexData"
      :tab-list="tabList"
      :sort-data="sortData"
      @handleChangeTab="handleChangeTab"
      @onDateTypeChange="onDateTypeChange"
    ></title-bar>
    <div class="statistics-list-content auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :row-class-name="rowClassName"
        @onSortChange="onSortChange"
      >
        <template #civilName="{ row, column }">
          <span
            :class="['font-highlight', paramsList.dateType === '1' ? 'font-active-color underline-text pointer' : '']"
            @click="viewMonthDetail(row)"
          >
            {{ row[column.key] }}
          </span>
        </template>
        <template #detection="{ row }">
          <span :class="[paramsList.dateType === '2' ? 'underline-text pointer' : '']" @click="viewDayDetail(row, '')">
            {{ row.detection }}
          </span>
        </template>
        <template #qualified="{ row }">
          <span :class="[paramsList.dateType === '2' ? 'underline-text pointer' : '']" @click="viewDayDetail(row, '1')">
            {{ row.qualified }}
          </span>
        </template>
        <template #unqualified="{ row }">
          <span
            :class="['unqualified-color', paramsList.dateType === '2' ? 'underline-text pointer' : '']"
            @click="viewDayDetail(row, '2')"
          >
            {{ row.unqualified }}
          </span>
        </template>
        <template #rate="{ row }">
          <span :class="row.qual === '1' ? '' : 'unqualified-color'">{{ row.rateFormat }}</span>
        </template>
        <!-- 建档匹配率 -->
        <template #accuracyRate="{ row }">
          <span :class="{ 'span-link': getAccuracyRate(row) }" @click="clickJump(row)">
            {{ getAccuracyRate(row) ? `${row?.detail[0]?.onlineRateVO?.accuracyRate}%` : '--' }}
          </span>
        </template>
      </ui-table>
    </div>
    <day-review-details
      v-model="dayReviewDetailVisible"
      v-bind="$attrs"
      :data-dimension-enum="dataDimensionEnum"
      :index-data="indexData"
      :row-data="rowData"
    ></day-review-details>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import { tableColumns } from './tableColumns.js';
import { rateFormatFields } from '@/views/specialassessment/utils/menuConfig.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters } from 'vuex';
import { DAY_STATISTICS } from '@/views/specialassessment/utils/common';

export default {
  name: 'VideoOnline',
  mixins: [dealWatch],
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      tableColumns: [],
      tableData: [],
      dayReviewDetailVisible: false,
      dataDimensionEnum: 'DEVICE_ALL',
      rowData: {},
      tabList: [],
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
    };
  },
  computed: {
    ...mapGetters({
      showColRegion: 'governanceevaluation/getShowColRegion',
    }),
  },
  created() {
    if (!this.showColRegion) {
      this.getConfigByKeys();
    }
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        let { dateType, batchIds } = this.$route.query;
        if (dateType === DAY_STATISTICS && !batchIds) return;
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  watch: {
    showColRegion: {
      handler() {
        this.getTableColumns();
      },
      deep: true,
    },
  },
  methods: {
    ...mapActions({
      getConfigByKeys: 'governanceevaluation/getConfigByKeys',
    }),
    async getTableList() {
      try {
        this.loading = true;
        const queryParams = this.$route.query;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: queryParams.examineTime,
          type: queryParams.dateType || '2',
          dataDimensionEnum: this.dataDimensionEnum,
          nodeEnum: queryParams.indexType,
          paramForm: {},
          statisticalModel: queryParams.statisticsCode ? Number(queryParams.statisticsCode) : null,
          displayType:
            queryParams.statisticsCode === '1'
              ? 'ORG'
              : queryParams.statisticsCode === '2'
                ? 'REGION'
                : queryParams.statisticsCode === '3'
                  ? 'TAG'
                  : '',
        };
        if (queryParams.dateType === '2') {
          params.batchIds = queryParams.batchIds ? queryParams.batchIds.split(',') : [];
        }
        if (this.sortData.sortField) {
          params.paramForm = {
            indexId: this.tableData[0].detail[0].indexId,
            sortField: this.sortData.sortField,
            sort: this.sortData.sort,
          };
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfoList, params);
        if (!data || !data.length) {
          this.tableData = [];
          return false;
        }
        this.tabList = data[0].dataDimensionsDisplay || [];
        this.tableData = data.map((item) => {
          if (item.detail) {
            return {
              ...item,
              ...item.detail[0],
            };
          }
          return item;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    rowClassName(row, index) {
      if (index === this.tableData.length - 1) {
        return 'active-blue';
      }
      return '';
    },
    onSortChange(column) {
      if (column.order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          indexId: this.tableData[0].detail[0].indexId,
          sortField: column.key,
          sort: column.order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    handleChangeTab(data) {
      this.dataDimensionEnum = data.value;
      this.getTableList();
    },
    viewMonthDetail(row) {
      if (this.paramsList.dateType === '2') return false;
      this.$emit('changeComponentName', [
        'MonthReviewDetail',
        {
          dataDimensionEnum: this.dataDimensionEnum,
          ...row,
        },
      ]);
    },
    // qualVal 控制检测详情筛选条件-检测结果默认值
    viewDayDetail(row, qualVal) {
      if (this.paramsList.dateType === '1') return false;
      this.dayReviewDetailVisible = true;
      this.rowData = {
        qualVal,
        ...row,
        ...row.detail[0],
      };
    },
    onDateTypeChange() {
      this.sortData = {};
    },
    getParams() {
      this.paramsList = this.$route.query;
      this.getTableColumns();
      this.tableData = [];
      this.getTableList();
    },
    getTableColumns() {
      let { dateType, indexType, statisticsCode } = this.$route.query;
      let obj = {
        showColRegion: dateType === '1' ? false : this.showColRegion, // 月统计都不显示
        indexType: indexType,
        statisticsCode: statisticsCode,
      };
      let columns = this.$util.common.deepCopy(tableColumns(obj)[indexType]);
      columns.forEach((item) => {
        if (item.key === 'rate') {
          let name = rateFormatFields[indexType];
          item.title = dateType === '1' ? `${name}（月平均）` : name;
        }
      });
      this.tableColumns = columns;
    },
    // 跳转到 填报准确率
    clickJump(row) {
      // 跳转到 资产考核-资产质量
      // let { batchId, examineTime, taskIndexId, treeId } = row?.detail[0].onlineRateVO
      // if (!batchId) return;
      // let data = {
      //     batchId: batchId,
      //     examineTime: examineTime,
      //     taskIndexId: taskIndexId,
      //     treeId: treeId,
      //     componentName: 'VideoOnline',
      //   }
      // this.$emit('resetCurrentNode', data)

      // 跳转到 评测结果
      let { batchId, indexId, indexType, taskSchemeId, regionCode, orgCode, statisticType } =
        row?.detail[0].onlineRateVO || {};
      if (!batchId) return;
      this.$router.push({
        name: 'evaluationoResult',
        query: {
          orgCode: orgCode,
          regionCode: regionCode,
          statisticType: statisticType,
          taskSchemeId: taskSchemeId,
          indexId: `${indexId}`,
          indexType: indexType,
          batchId: batchId,
        },
      });
    },
    getAccuracyRate(row) {
      return row?.detail[0]?.onlineRateVO?.accuracyRate || row?.detail[0]?.onlineRateVO?.accuracyRate === 0;
    },
  },
  components: {
    TitleBar: require('@/views/specialassessment/components/title-bar.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DayReviewDetails: require('@/views/specialassessment/special-modules/components/day-review-details/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.statistics-list {
  &-content {
    padding: 0 20px;
  }
  .underline-text {
    text-decoration: underline;
  }
  .unqualified-color {
    color: var(--color-failed);
  }
  .qualified-color {
    color: var(--color-success);
  }
  @{_deep} .active-blue {
    td:nth-child(2) {
      .font-highlight {
        color: var(--color-warning) !important;
      }
    }
  }
  .span-link {
    color: rgb(43, 132, 226);
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
