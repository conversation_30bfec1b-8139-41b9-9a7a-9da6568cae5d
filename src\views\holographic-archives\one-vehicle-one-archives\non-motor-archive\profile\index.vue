<template>
  <div class="people-archive-container">
    <div class="content">
      <!-- 基础信息 -->
      <BasicInformation
        :labelType="6"
        :baseInfo="basicInfo"
        type="non-motor-archive"
        :imgSrc="imgSrc"
      />
      <div class="main-information">
        <!-- 车主信息 -->
        <!-- <OwnerInformationCard :vehicleInfo="vehicleInfo" :id="'owner_information' + routeParams" title="车主信息" class="m-b10" /> -->
        <!-- 车辆抓拍 -->
        <PortraitCapture
          :id="'portrait_capture' + routeParams"
          title="车辆抓拍"
          type="non-motor-archive"
          :list="vehicleCaptureList"
          :loading="vehicleCaptureLoading"
          class="m-b10"
        />
        <Peers
          class="m-b10"
          id="peers"
          title="同行车辆"
          type="nonMotorVehicle"
          :get-data-list="getPeerList"
        ></Peers>
        <!--        <Alarm
          class="m-b10"
          :id="'latest_alarm' + routeParams"
          title="最新报警"
          type="vehicle"
          :baseInfo="basicInfo"
          :archiveNo="archiveNo">
        </Alarm>-->
        <!-- 人车同拍 -->
        <!-- <PeopleCarCapture
          :id="'people_car_capture' + routeParams"
          title="人车同拍"
          type="vehicle"
          :list="peopleCarCaptureList"
          :loading="peopleCarCaptureLoading"
          class="m-b10" /> -->
        <!-- 位置信息 -->
        <PositionInformation
          :id="'position_information' + routeParams"
          title="位置信息"
          :list="latestLocationList"
          :latestLocationLoading="latestLocationLoading"
          :oftenGoList="oftenGoList"
          :oftenGoLoading="oftenGoLoading"
          :positionPoints="positionPoints"
          :heatData="heatData"
          modelType="vehicle"
          class="m-b10"
          @on-change="frequentedList"
          type="non-motor-archive"
        />
        <!-- 行为规律 -->
        <LawBehavior
          :id="'law_behavior' + routeParams"
          title="行为规律"
          :timeSlotSeries="timeSlotSeries"
          :activeNumXAxis="activeNumXAxis"
          :activeNumSeries="activeNumSeries"
          :timeSlotLoading="timeSlotLoading"
          :activeNumLoading="activeNumLoading"
          class="m-b10"
          @on-change="lawBehavior"
        />
        <!-- 背景信息 -->
        <!-- <BackgroundInformation
          v-if="plateNo"
          :id="'background_information' + routeParams"
          type="vehicle"
          :plateNo="plateNo"
          title="背景信息"
          class="m-b10" /> -->
        <!-- 关系信息 -->
        <!-- <RelationshipInfomation
          :id="'relationship_information' + routeParams"
          type="vehicle"
          title="关系信息"
          class="m-b10"
          :baseInfo="basicInfo"
          :loading1="vehicleFollowingLoading"
          :list1="vehicleFollowingList"
          :loading2="drivingLoading"
          :list2="drivingList" /> -->
      </div>
      <div class="anchor-point-infomation">
        <!-- <Button type="primary" class="export-btn">导出</Button> -->
        <!-- 锚点 -->
        <UiAnchor :anchorLinkList="anchorLinkList" />
      </div>
    </div>
    <search-image ref="searchImage" @on-submit="onSubmit"></search-image>
  </div>
</template>
<script>
import mixinWidget from "@/mixins/mixinWidget.js";
import SearchImage from "@/components/search-image/index.vue";
import BasicInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information";
import OwnerInformationCard from "@/views/holographic-archives/one-vehicle-one-archives/vehicle-archive/profile/components/owner-information";
import PortraitCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/portrait-capture";
import PeopleCarCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/people-car-capture";
import PositionInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/position-information";
import LawBehavior from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/law-behavior";
import BackgroundInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/background-information.vue";
import RelationshipInfomation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/relationship-infomation.vue";
import Alarm from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/alarm.vue";
import Peers from "@/views/holographic-archives/one-vehicle-one-archives/non-motor-archive/profile/components/peers.vue";
import UiAnchor from "@/components/ui-anchor";
import {
  queryPeopleAndVehicleSnapList,
  getFollowTheVehicleList,
  getVehicleBaseInfo,
  archiveInfo,
  queryDrivingPerson,
  behavioralRulesStaticsNonMotor,
  activitiesNumStaticsNonMotor,
  nonMotorFrequented,
  nonMotorLatestLocation,
  getNonMotorVehicleCaptureList,
} from "@/api/vehicleArchives";
import { mapActions } from "vuex";
import {
  queryFacePeerCountPageListByTimeliness,
  queryNonMotorPeerAnalysisPageList,
} from "@/api/modelMarket";
export default {
  components: {
    SearchImage,
    BasicInformation,
    OwnerInformationCard,
    PortraitCapture,
    PeopleCarCapture,
    PositionInformation,
    LawBehavior,
    BackgroundInformation,
    RelationshipInfomation,
    UiAnchor,
    Alarm,
    Peers,
  },
  props: {},
  mixins: [mixinWidget],
  data() {
    return {
      routeParams: "",
      imgSrc: require("@/assets/img/demo/vehicle.png"),
      anchorLinkList: [
        // { href: '#owner_information', title: '车主信息' },
        { href: "#portrait_capture", title: "车辆抓拍" },
        { href: "#peers", title: "同行车辆" },
        // { href: '#latest_alarm', title: '最新报警' },
        // { href: '#people_car_capture', title: '人车同拍' },
        { href: "#position_information", title: "位置信息" },
        { href: "#law_behavior", title: "行为规律" },
        // { href: '#background_information', title: '背景信息' },
        // { href: '#relationship_information', title: '关系信息' }
      ],
      vehicleCaptureList: [],
      vehicleCaptureLoading: false,
      peopleCarCaptureList: [],
      peopleCarCaptureLoading: false,
      latestLocationList: [],
      latestLocationLoading: false,
      timeSlotSeries: [
        {
          type: "bar",
          data: [],
          coordinateSystem: "polar",
          stack: "a",
          barCategoryGap: "0%",
        },
      ],
      timeSlotLoading: false,
      activeNumXAxis: {
        data: [1, 2, 3, 4, 5, 6, 7],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
          // rotate: 40
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      activeNumSeries: [
        {
          name: "白天",
          type: "bar",
          stack: "one",
          data: [5, 2, 3, 4, 5, 6, 7],
          barWidth: "30%",
          itemStyle: {
            color: "#2C86F8",
          },
        },
        {
          name: "晚上",
          type: "bar",
          stack: "one",
          data: [1, 2, 3, 4, 5, 6, 7],
          barWidth: "30%",
          itemStyle: {
            color: "#F29F4C",
          },
        },
      ],
      activeNumLoading: false,
      oftenGoList: [],
      oftenGoLoading: false,
      positionPoints: [],
      heatData: [],
      vehicleFollowingList: [],
      vehicleFollowingLoading: false,
      drivingList: [],
      drivingLoading: false,
      archiveNo: null,
      plateNo: null,
      idcardNo: null,
      basicInfo: [], // 基本信息
      vehicleInfo: {}, // 车主信息
    };
  },
  async created() {
    var { archiveNo, plateNo, idcardNo } = Object.keys(this.$route.query).length
      ? this.$route.query
      : JSON.parse(sessionStorage.getItem("query"));
    this.archiveNo = JSON.parse(archiveNo);
    this.plateNo = JSON.parse(plateNo);
    this.idcardNo = idcardNo;
    this.routeParams = "?" + this.$route.fullPath.split("?")[1];
    this.anchorLinkList.forEach((v) => {
      v.href = v.href + this.routeParams;
    });
    //基本信息
    await this.basicInfoFn();
    // 最新位置
    await this.newLocation();
    // 常去地
    await this.frequentedList(1);
    // 车辆抓拍
    this.getVehicleCaptureListFn();
    // 人车同拍
    // this.getPeopleCar()
    // 行为规律
    this.lawBehavior(1);
    // 关系信息
    // this.relationship()
  },
  methods: {
    async getPeerList(params) {
      try {
        return await queryNonMotorPeerAnalysisPageList({
          ...params,
          plateNo: this.plateNo,
        });
      } catch (e) {
        console.log(e);
      } finally {
      }
    },
    async basicInfoFn() {
      try {
        archiveInfo({ archiveNo: this.archiveNo }).then((res) => {
          this.basicInfo = res.data || [];
        });
      } catch (e) {
        console.log(e);
      }
    },
    /**
     * 常去地
     */
    async frequentedList(val) {
      this.oftenGoLoading = true;
      await nonMotorFrequented({
        archiveNo: this.archivesId,
        dataRange: val,
        plateNo: this.plateNo,
        dataSize: 10,
      })
        .then((res) => {
          let { locationList, heatmapList } = res.data;
          this.oftenGoList = locationList;
          // 常去地点位
          let positionPoints = this.oftenGoList.map((v) => {
            if (v.type === 1) {
              // 人脸抓拍位置
              return {
                ...v,
                type: "face",
                markerIconUrl: require("@/assets/img/archives/marker_face.png"),
              };
            } else if (v.type === 2) {
              // 车辆抓拍位置
              return {
                ...v,
                type: "vehicle",
                markerIconUrl: require("@/assets/img/archives/marker_vehicle.png"),
              };
            } else if (v.type === 3) {
              // IMSI感知数据位置
              return {
                ...v,
                type: "imsi",
                markerIconUrl: require("@/assets/img/archives/marker_imsi.png"),
              };
            }
          });
          // 将最新位置点位和常去地点位合并
          this.positionPoints = [...this.latestLocationList, ...positionPoints];
          // 常去地热力图
          this.heatData = heatmapList.map((v) => {
            return {
              ...v.geoPoint,
              numCount: v.times,
            };
          });
        })
        .finally(() => {
          this.oftenGoLoading = false;
        });
    },
    // 车辆抓拍
    getVehicleCaptureListFn() {
      this.vehicleCaptureLoading = true;
      getNonMotorVehicleCaptureList({
        // plateNo: this.plateNo,
        plateNo: this.plateNo,
        // plateNo: '川KZL012',
        dataSize: 7,
      })
        .then((res) => {
          this.vehicleCaptureList = res.data;
        })
        .finally(() => {
          this.vehicleCaptureLoading = false;
        });
    },

    // 行为规律
    lawBehavior(val, startDate, endDate) {
      // 活动时间段
      this.timeSlotLoading = true;
      behavioralRulesStaticsNonMotor({
        plateNo: this.plateNo,
        archiveNo: this.archivesId,
        // gmsfhm: '12345678952',
        type: val,
        // vid: ''
        startDate,
        endDate,
      })
        .then((res) => {
          let daytimeRange = res.data.daytimeRange;
          // 后端返回白天、晚上开始时间格式为"白天开始时间-晚上开始时间"
          let dayStart = daytimeRange.split("-")[0];
          let dayEnd = daytimeRange.split("-")[1];
          let timeList = res.data.x;
          let dataList = res.data.y;
          this.timeSlotSeries[0].data = [];
          dataList.forEach((v, i) => {
            // 当前时间》=白天开始时间且《晚上开始时间则为白天，否则为晚上
            if (timeList[i] >= dayStart && timeList[i] <= dayEnd) {
              this.timeSlotSeries[0].data.push({
                name: `白天-${i + 1}点`,
                value: v,
                itemStyle: {
                  color: "#2C86F8",
                },
              });
            } else {
              this.timeSlotSeries[0].data.push({
                name: `晚上-${i + 1}点`,
                value: v,
                itemStyle: {
                  color: "#F29F4C",
                },
              });
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          this.timeSlotLoading = false;
        });
      // 活动次数
      this.activeNumLoading = true;
      activitiesNumStaticsNonMotor({
        plateNo: this.plateNo,
        archiveNo: this.archivesId,
        gmsfhm: "12345678952",
        type: val,
        vid: "",
        startDate,
        endDate,
      })
        .then((res) => {
          if (val === 2) {
            this.activeNumXAxis.axisLabel.rotate = 0;
          } else {
            this.activeNumXAxis.axisLabel.rotate = 40;
          }
          this.activeNumXAxis.data = res.data.x;
          this.activeNumSeries[0].data = res.data.day;
          this.activeNumSeries[1].data = res.data.night;
        })
        .catch(() => {})
        .finally(() => {
          this.activeNumLoading = false;
        });
    },
    /**
     * 人车同拍
     */
    getPeopleCar() {
      this.peopleCarCaptureLoading = true;
      queryPeopleAndVehicleSnapList(this.plateNo)
        .then((res) => {
          // queryPeopleAndVehicleSnapList('川KSG711').then((res) => {
          this.peopleCarCaptureList = res.data;
        })
        .catch(() => {})
        .finally(() => {
          this.peopleCarCaptureLoading = false;
        });
    },
    /**
     * 最新位置
     */
    async newLocation() {
      this.latestLocationLoading = true;
      await nonMotorLatestLocation({
        archiveNo: this.archiveNo,
        idcardNo: this.archivesId,
        plateNo: this.plateNo,
        dataRange: 2,
        dataSize: 2,
        vid: "",
      })
        .then((res) => {
          this.latestLocationList = res.data.map((v) => {
            return {
              ...v,
              ...v.geoPoint,
            };
          });
          // 将最新位置点位和常去地点位合并
          // this.positionPoints = this.positionPoints.concat(this.latestLocationList)
        })
        .catch(() => {})
        .finally(() => {
          this.latestLocationLoading = false;
        });
    },
    /**
     * 关系信息
     */
    relationship() {
      // 车辆跟随
      this.vehicleFollowingLoading = true;
      getFollowTheVehicleList({
        params: {
          pageNumber: 1,
          pageSize: 3,
        },
      })
        .then(() => {
          let res = {
            data: [
              {
                myselfPic: require("@/assets/img/demo/vehicle.png"),
                licensePlate: "苏A88888",
                tigetherPic: require("@/assets/img/demo/vehicle1.png"),
                times: 2,
              },
            ],
          };
          this.vehicleFollowingList = res.data;
        })
        .catch(() => {})
        .finally(() => {
          this.vehicleFollowingLoading = false;
        });

      // 驾乘人员
      this.drivingLoading = true;
      queryDrivingPerson("川KSG711")
        .then((res) => {
          // queryDrivingPerson(plateNo).then(res =>{

          this.drivingList = res.data;
        })
        .catch(() => {})
        .finally(() => {
          this.drivingLoading = false;
        });
    },
    beforeFaceControl() {
      if (!this.basicInfo.length) return this.$Message.error("图片不存在");
      let row = this.basicInfo[0];
      let photoUrl = row.photoUrl || row.bigImageUrl;
      if (!photoUrl) return this.$Message.error("图片不存在");
      this.onPeopleControl(row, photoUrl);
    },
    beforeFaceSearchImage() {
      if (!this.basicInfo.length) return this.$Message.error("图片不存在");
      let row = this.basicInfo[0];
      let photoUrl = row.photoUrl || row.bigImageUrl;
      if (!photoUrl) return this.$Message.error("图片不存在");
      this.onSearchImage(row, photoUrl, "2");
    },
  },
};
</script>
<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}
.people-archive-container {
  padding: 16px 10px 0 10px;
  display: flex;
  flex: 1;
  overflow: hidden;
  .content {
    display: flex;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    flex: 1;
    .main-information {
      width: 1442px;
      height: min-content;
      padding: 0 10px;
      margin-left: 350px;
      /deep/ .ui-card {
        overflow: unset !important;
        .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
      }
    }
    .anchor-point-infomation {
      width: 100px;
      position: fixed;
      top: 78px;
      right: 18px;
      z-index: 9;
      .export-btn {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
