<template>
  <div id="chart-panel" ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=xyqMtbahqc
 */
export default {
  props: {
    labelList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      myEchart: null
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      const resultData = {
        status: 'success',
        data: this.labelList,
        msg: 'exercitation in'
      }

      // 生成随机坐标的不重叠圆
      function drawCircles (maxLength, radius, max_x, max_y) {
        const circles = []
        let anti_crash_count = 0
        while (circles.length < maxLength) {
          const circle = {
            x: getRandomNumber(radius, max_x - radius),
            y: getRandomNumber(radius, max_y - radius),
            radius
          }

          let isOverlapping = false

          for (let j = 0; j < circles.length; j++) {
            const previous_circle = circles[j]
            const distance = Math.hypot(circle.x - previous_circle.x, circle.y - previous_circle.y)

            if (distance < circle.radius + previous_circle.radius) {
              isOverlapping = true
              break
            }
          }

          if (!isOverlapping) {
            circles.push(circle)
          }
          anti_crash_count++
          if (anti_crash_count > 10000) {
            break
          }
        }
        return circles
      }

      // 生成随机数坐标
      function getRandomNumber (min, max) {
        return Math.floor(Math.random() * (max - min) + min)
      }
      // 数据数组
      const countList = resultData.data
      var sum = 0
      var max = 0
      countList.forEach((e, i) => {
        e.id = i
        sum += e.value
        if (e.value >= max) max = e.value
      })
      // 放大规则
      var number = Math.round(max * 0.5)
      const graphCanvas = document.getElementById('chart-panel').getBoundingClientRect()
      const randomCircleArr = drawCircles(
        countList.length,
        ((max + number) / max) * 40,
        graphCanvas.width,
        graphCanvas.height
      )
      console.log(randomCircleArr)
      countList.forEach((e, i) => {
        if (randomCircleArr[i]) {
          e.x = randomCircleArr[i].x
          e.y = randomCircleArr[i].y
        } else {
          e.x = 0
          e.y = 0
        }
      })
      console.log(countList)
      const option = {
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          formatter: '{b}',
          textStyle: {
            color: '#fff'
          }
        },
        series: [
          {
            name: '词条分布',
            type: 'graph',
            layout: 'none',
            symbol: '',
            label: {
              show: true
            },
            data: countList
          }
        ]
      }
      this.myEchart.setOption(option)
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
