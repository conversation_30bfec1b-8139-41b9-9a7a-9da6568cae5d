<template>
    <div class="plan-list-wrap">
        <ul class="search_tab" v-if="!isEdit">
            <li v-for="(item, index) in tablist" :key="index" class="tabslist" @click="changeHandle(item.value)" :class="{ active: planType == item.value }">
                {{ item.name }}
            </li>
        </ul>
        <div class="plan-list-content">
            <Input search v-model="searchPlan" placeholder="请输入关键字" v-if="!isEdit"></Input>
            <div class="group-header" v-if="!isEdit">
                <div class="total-count">
                    共<span>&nbsp;{{planList.length || 0}}&nbsp;</span>个预案
                </div>
                <Button type="primary" icon="ios-add" size="small" @click="addPlan()">新增预案</Button>
            </div>
            <div v-scroll style="flex: 1">
                <div class="plan-child" v-for="(item, index) in isEdit ? planList :tarPlanList" :key="index">
                    <div class="plan-child-header"  :class="{selectChild: currentPlan.id === item.id}">
                        <div class="plan-child-name" @click.stop="selectPlan(item)">
                            <div class="sanjiao" :class="{select: currentPlan.id === item.id && openPlan}"></div>
                            <div class="plan-child-name-info" :title="item.name">{{item.name}}</div>
                        </div>
                        <div class="plan-child-operate" v-if="isEdit">
                            <!-- <i class="iconfont icon-lunxunkaishi plan-icon" title="轮巡预览" @click.stop="getSecondExtend" ></i> -->
                        </div>
                        <div class="plan-child-operate" v-if="!isEdit">
                            <i class="iconfont icon-lunxunzanting plan-icon plan-child-operate-red"
                               @click.stop="pausePlan(item)"
                               v-if="operateStatus === 1 && loopPlanList.id === item.id"
                               title="暂停">

                            </i>&nbsp;&nbsp;
                            <i class="iconfont icon-lunxunkaishi plan-icon plan-child-operate-red"
                               @click.stop="containuePlan(item)"
                               v-if="operateStatus === 2 && loopPlanList.id === item.id"
                               title="继续">

                            </i>&nbsp;&nbsp;
                            <i class="iconfont icon-lunxuntingzhi plan-icon plan-child-operate-red"
                               @click.stop="stopPlan(item)"
                               v-if="((operateStatus === 1 && loopPlanList.id === item.id) || (operateStatus === 2  && loopPlanList.id === item.id))"
                               title="停止">

                            </i>&nbsp;&nbsp;
                            <i class="iconfont icon-lunxunkaishi plan-icon"
                               title="启动预案"
                               @click.stop="loopPlan(item)"
                               v-if="operateStatus === 0 ? true : loopPlanList.id !== item.id">

                            </i>&nbsp;&nbsp;
                            <i class="iconfont icon-bianji plan-icon"
                               title="编辑预案"
                               @click.stop="addPlan(item)"
                               v-if="item.userId == userInfo.id && (loopPlanList.id === item.id ? operateStatus === 0 : true)">

                            </i>&nbsp;&nbsp;
                            <i class="iconfont icon-shanchu plan-icon"
                               title="删除预案"
                               @click.stop="delPlan(item.id)"
                               v-if="item.userId == userInfo.id && (loopPlanList.id === item.id ? operateStatus === 0 : true)">

                            </i>
                        </div>
                    </div>
                    <transition name="fadedown">
                        <div class="plan-child-wrap"  v-if="currentPlan.id === item.id && openPlan">
                            <div class="plan-child-group-wrap">
                                <div class="plan-child-group-title">
                                    <div class="plan-child-group-set">
                                        <div class="line"></div>
                                        <div>分组设置</div>
                                    </div>
                                    <div class="plan-child-group-total">共<span>&nbsp;{{item.groupList ? item.groupList.length : 0}}&nbsp;</span>个分组</div>
                                </div>
                                <div class="plan-child-group" v-for="(group, index2) in item.groupList" :key="index2">
                                    <div class="plan-child-group-header" :class="{'plan-child-group-select': group.checked}" @click.stop="selectGroup(group)">
                                        <div class="plan-child-group-info">
                                            <div class="sanjiao" :class="{select: group.checked}"></div>
                                            <div class="plan-child-group-info-name"  :title="group.name">&nbsp;{{index2 + 1}}&nbsp;{{group.name}}</div>
                                        </div>
                                        <div class="plan-child-group-time">
                                            间隔{{group.stopTime || 0}}秒
                                        </div>
                                    </div>
                                    <transition name="fadedown">
                                        <div class="plan-child-device-wrap" v-if="group.checked">
                                            <div class="plan-child-device" :class="{'device-ignore': device.ignore}" v-for="(device, index3) in isEdit ? group.resourceData : group.cameraData" :key="index3">
                                                <div class="plan-child-device-info" :style="{width:  isEdit ? '180px' : '240px'}">
                                                    <div class="plan-child-device-count">{{index3 + 1}}</div>
                                                    <div class="plan-child-device-icon">
                                                       <i class="iconfont" :class="cacluateStyle(device)"></i>
                                                    </div>
                                                    <div class="plan-child-device-name" :style="{width: isEdit ? '120px' : '180px'}" :title="device.deviceName || device.name">{{device.deviceName || device.name}}</div>
                                                </div>
                                                <div class="plan-child-device-operate" v-if="isEdit">
                                                    <i title="上移" @click="updateOrder(group, device, 1)">↑</i>&nbsp;&nbsp;
                                                    <i title="下移"  @click="updateOrder(group, device, 0)">↓</i>&nbsp;&nbsp;
                                                    <i class="iconfont icon-shanchu plan-icon" title="删除" @click="delDevice(group, device)"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                            <div class="plan-child-time-wrap"  v-if="!isEdit">
                                <div class="plan-child-time-title">
                                    <div class="plan-child-time-set">
                                        <div class="line"></div>
                                        <div>按时自启动</div>
                                    </div>
                                    <div class="plan-child-time-edit">
                                        <i-switch v-if="item.tourTime.length && item.tourTime[0].week" v-model="item.autoStart" @on-change="changeAutoStart($event, item)"/>
                                        <i class="iconfont icon-bianji plan-icon" title="编辑" v-if="item.userId == userInfo.id" @click="editPlan(item)"></i>
                                    </div>
                                </div>
                                <div style="margin-left: 7px;color: red;" v-if="!item.tourTime.length || !item.tourTime[0].week">
                                    未设置自启动时间
                                </div>
                                <template v-if="item.tourTime.length && item.tourTime[0].week">
                                    <div class="plan-child-time-info" v-for="(time, index4) in item.tourTime" :key="index4" >
                                        <div class="plan-child-time-info-time">{{time.startTime}}</div><!--&nbsp;-&nbsp;{{time.endTime}}-->
                                        <div class="plan-child-time-date-wrap">
                                            <div class="plan-child-time-data" :class="{select: time.one}">一</div>
                                            <div class="plan-child-time-data" :class="{select: time.two}">二</div>
                                            <div class="plan-child-time-data" :class="{select: time.three}">三</div>
                                            <div class="plan-child-time-data" :class="{select: time.four}">四</div>
                                            <div class="plan-child-time-data" :class="{select: time.five}">五</div>
                                            <div class="plan-child-time-data" :class="{select: time.six}">六</div>
                                            <div class="plan-child-time-data" :class="{select: time.seven}">日</div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </transition>
                </div>
                <div v-if="!isEdit && !tarPlanList.length" class="no-result-wrap">
                    <noresult class="no-group" size="small" :mainText="searchPlan ? '暂无预案' : '还未添加任何预案'" :offsetTop="30" :subText="'点击添加'"  @subTextFun="addPlan"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import noresult from "./noresult.vue";
    import { deletePlan } from "@/api/inspectionTour.js"
    import { mapGetters } from "vuex";
    export default {
        name: "planList",
        props: {
            isEdit: {
                type: String,
                default(){
                    return '';
                },
            },
            planList: {
                type: Array,
                default(){
                    return [];
                },
            },
            lockedCameras: {
                type: Object,
                default(){
                    return {};
                },
            },
        },
        data(){
            return{
                tablist: [
                    {
                        name: "我的预案",
                        value: "own"
                    },
                    {
                        name: "公共预案",
                        value: "public"
                    }
                ],
                planType: 'own', // 预案类型
                searchPlan: '', //搜索预案
                currentPlan: {id: ''}, //选中的预案
                loopPlanList: {id: ''}, //轮巡的预案
                openPlan: false, //是否展开预案
                operateStatus: 0, //操作状态 0 停止状态 1 启动 2暂停
            }
        },
        methods:{
            // 改变预案
            changeHandle(v){
                this.planType = v
                const type = v === 'own' ? 0 : 1;
                this.$emit('refreshList', this.searchPlan, type);
            },
            //新增预案
            addPlan(item = {id: '', planType: this.planType}){
                this.$emit('addPlan', item);
            },
            // 修改时间预案
            editPlan(item = {id: ''}){
                this.$emit('addPlan', item, true);
                this.$emit('planNext');
                this.$emit('planNext');
            },
            //选择预案
            selectPlan(plan, type){
                if(!this.currentPlan.id){ //没有选择分组时
                    this.currentPlan = plan;
                    this.openPlan = true;
                }else {
                    if(plan.id === this.currentPlan.id){ //点击同一分组时
                        if(type){
                            return;
                        }
                        this.openPlan = !this.openPlan;
                    }else { // 点击非同一分组时
                        if(this.operateStatus != 0){

                        }
                        this.currentPlan = plan;
                        this.openPlan = true;
                    }
                }
                if(this.operateStatus === 0){
                    this.addOSD(plan);
                }
            },
            // 清除已选择
            clearSelect(){
                this.currentPlan = {id: ''};
            },
            //选择分组
            selectGroup(group){
                this.$set(group, 'checked', !group.checked);
            },
            //删除预案
            delPlan(id){
                this.$Modal.confirm({
                    title: '友情提示',
                    width: 450,
                    closable: true,
                    content: `确定要删除该预案?`,
                    onOk: () => {
                        deletePlan(id).then(res => {
                            const type = this.planType === 'own' ? 0 : 1;
                            this.$emit('refreshList', this.searchPlan, type);
                            this.$Message.success('删除成功！');
                        });
                    }
                })
            },
            //删除设备
            delDevice(group, device){
                this.$emit('handleDeleteSelected', group, device);
            },
            // 上移下移
            updateOrder(group, device, type){
                this.$emit('updateOrder', group, device, type);
            },
            // 预案选择
            addOSD(item){
                this.$emit('addOSD', item);
            },
            // 轮巡
            loopPlan(item){
                this.stopPlan(this.loopPlanList, true);// 停止原来的轮巡
                setTimeout(_ => {
                    this.loopPlanList = item;
                    this.selectPlan(item, true);
                    this.addOSD(item);
                    setTimeout(_ => {
                        this.$emit('loopPlan', item);
                    }, 100);
                }, 100);
            },
            startLoopPlan(){
                this.operateStatus = 1;
            },
            // 暂停
            pausePlan(item){
                this.operateStatus = 2;
                this.$emit('pausePlan', item.id);
            },
            // 继续
            containuePlan(item){
                this.operateStatus = 1;
                this.$emit('containuePlan', item.id);
            },
            // 停止
            stopPlan(item, type){
                this.operateStatus = 0;
                this.$emit('returnPlanList');
                setTimeout(_ => {
                    this.$emit('stopPlan', item.id, type);
                }, 100);
            },
            // 发送扩展平后停止轮巡
            extendScreen(){
                this.operateStatus = 0;
            },
            // 新增时预览
            getSecondExtend(){
                this.$emit('getSecondExtend');
            },
            // 按时自启动
            changeAutoStart(e, item){
                this.$emit('changeAutoStart', item);
            },
            // 计算相机样式
            cacluateStyle(camera) {
                let lockedCameras = Object.values(this.lockedCameras);
                let lockedCamera = lockedCameras.find(i => i.cameraId === camera.id);
                if (lockedCamera) {
                    this.$set(camera, 'isLocking', lockedCamera.isLock);
                } else {
                    this.$set(camera, 'isLocking', false);
                }
                if((camera.isPlaying || camera.isLocking) && !camera.ignore) {
                    return "playing-style";
                }
                camera.nodeType = 'camera';
                if(camera.cameraDefinitionType === undefined){
                    camera.cameraDefinitionType = 1;
                }
                return Toolkits.getDeviceIconType(camera);
            },
        },
        mounted(){},
        computed:{
            ...mapGetters({ 
                userInfo: "userInfo"
            }),
            tarPlanList(){
                if(!this.planList || !this.planList.length){
                    return  [];
                }
                this.planList.map(item => {
                    item.tourTime.map(val => {
                        val.one = val.week.includes('一');
                        val.two = val.week.includes('二');
                        val.three = val.week.includes('三');
                        val.four = val.week.includes('四');
                        val.five = val.week.includes('五');
                        val.six = val.week.includes('六');
                        val.seven = val.week.includes('日');
                    });
                });
                return this.planList;
            }
        },
        watch: {
            searchPlan(val){
                const type = this.planType === 'own' ? 0 : 1;
                this.$emit('refreshList', this.searchPlan, type);
            },
        },
        components: {noresult},
    };
</script>

<style lang="less">
    @blueColor: #2c86f8;
    .epl(@width){
        width: @width;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .search_tab {
      display: flex;
      justify-content: space-evenly;
      border-bottom: 1px solid #d3d7de;
      .tabslist {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        width: 25%;
        text-align: center;
        padding: 7px 0;
        cursor: pointer;
      }
      .active {
        color: #2c86f8;
        border-bottom: 3px solid #2c86f8;
      }
    }
    .plan-list-wrap{
        height: 100%;
        .xui-tabs{
            border-bottom: 0 solid red;
            box-shadow: 5px 5px 0 rgba(34, 34, 34, 0.1);
            z-index: 0;
        }
        .xui-tabs-nav{
            display: flex;
            justify-content: space-around;
        }
        .plan-list-content{
            height: calc(~'100% - 40px');
            width: 100%;
            box-sizing: border-box;
            padding: 13px;
            display: flex;
            flex-direction: column;
            .no-result-wrap {
                height: 100%;
                width: 100%;
                display: flex;
                align-items: center;
            }
            .group-header {
                height: 40px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                .total-count {
                    color: #2c3033;
                    span{
                        color: #2C86F8;
                        font-size: 16px;
                    }
                }
                .create-group{
                    cursor: pointer;
                    color:  #2C86F8;
                    i{
                        font-size: 12px;
                    }
                }
            }
            .plan-child{
                width: 100%;
                margin-bottom: 10px;
                .plan-child-header{
                    width: 100%;
                    height: 40px;
                    box-sizing: border-box;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    border: 1px solid rgba(165, 176, 182, .7);
                    padding: 0 10px;
                    user-select: none;
                }
                .plan-child-header:hover{
                    background: rgba(70,121,250,0.1);
                    color: @blueColor;
                    font-weight: bold;
                }
                .selectChild{
                    background: #f6f7f8;
                }
                .fadedown-enter-active,
                .fadedown-leave-active {
                    transition: opacity 0.5s;
                }
                .fadedown-enter,
                .fadedown-leave-to {
                    opacity: 0;
                }
                .plan-icon{
                    font-weight:normal;
                    font-size:14px;
                    cursor:pointer
                }
                .plan-child-name{
                    display: flex;
                    height: 100%;
                    line-height: 40px;
                    .sanjiao{
                        width:0;
                        height:0;
                        transform-origin:10% 50%;
                        overflow:hidden;
                        font-size: 0;     /*是因为, 虽然宽高度为0, 但在IE6下会具有默认的 */
                        line-height: 0;  /* 字体大小和行高, 导致盒子呈现被撑开的长矩形 */
                        border-width:5px;
                        border-style:solid;  /*ie6下会出现不透明的兼容问题*/
                        border-color:transparent transparent transparent @blueColor!important;
                        margin-top: 15px;
                    }
                    .select{
                        transform: rotateZ(90deg);
                    }
                    .plan-child-name-info{
                        .epl(170px)
                    }
                }
                .plan-child-operate{
                    display: flex;
                    align-items: center;
                    .plan-child-operate-red{
                        color: red;
                    }
                }
                .plan-child-group-title{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 5px;
                    height: 16px;
                    .plan-child-group-set{
                        color: @blueColor;
                        display: flex;
                        align-items: center;
                        .line{
                            width: 3px;
                            height: 12px;
                            background: @blueColor;
                            margin-right: 4px;
                        }
                    }
                    .plan-child-group-total{
                        color: #666666;
                        span{
                            font-size: 16px;
                            color: @blueColor;
                        }
                    }
                }
                .plan-child-wrap{
                    background: #ffffff;
                    box-sizing: border-box;
                    padding: 10px;
                    border: 1px solid rgba(165, 176, 182, .7);
                    border-top: 0;
                    .plan-child-group-wrap{
                        .plan-child-group{
                            user-select: none;
                            padding: 10px 3px;
                            .plan-child-group-header{
                                display: flex;
                                justify-content: space-between;
                                .plan-child-group-info{
                                    display: flex;
                                    align-items: center;
                                    .sanjiao{
                                        width:0;
                                        height:0;
                                        transform-origin:10% 50%;
                                        overflow:hidden;
                                        font-size: 0;     /*是因为, 虽然宽高度为0, 但在IE6下会具有默认的 */
                                        line-height: 0;  /* 字体大小和行高, 导致盒子呈现被撑开的长矩形 */
                                        border-width:5px;
                                        border-style:solid;  /*ie6下会出现不透明的兼容问题*/
                                        border-color:transparent transparent transparent black;
                                    }
                                    .select{
                                        transform: rotateZ(90deg);
                                    }
                                    .plan-child-group-info-name{
                                        .epl(160px);
                                        height: 21px;
                                    }
                                }
                                .plan-child-group-time{
                                    color: #666666;
                                }
                            }
                            .plan-child-group-select{
                                font-weight: bold;
                            }
                            .plan-child-device-wrap{
                                margin-top: 5px;
                                .plan-child-device{
                                    padding: 8px 3px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: space-between;
                                    .plan-child-device-info{
                                        overflow: hidden;
                                        display: flex;
                                        align-items: center;
                                        div{
                                            margin-right: 8px;
                                        }
                                        .playing-style {
                                            display: inline-block;
                                            width: 16px;
                                            height: 16px;
                                            background-image: url('~@/assets/img/player/playing.gif');
                                        }
                                    }
                                    .plan-child-device-operate{
                                        display: flex;
                                        color: #2C86F8;
                                        i{
                                            width: 15px;
                                            text-align: center;
                                        }
                                    }
                                    .plan-child-device-count{
                                        border: 1px solid #2C86F8;
                                        border-radius: 8px;
                                        color: #2C86F8;
                                        font-size: 12px;
                                        font-weight: 400;
                                        height: 17px;
                                        line-height: 14px;
                                        text-align: center;
                                        width: 30px;
                                        .epl(30px);
                                    }
                                    .plan-child-device-icon{
                                        color: @blueColor;
                                    }
                                    .plan-child-device-name{
                                        .epl(200px);
                                        line-height: 15px;
                                    }
                                    &.device-ignore {
                                        cursor: not-allowed;
                                        color: #a5b0b6;
                                        .plan-child-device-icon{
                                            color: #a5b0b6;
                                        }
                                    }
                                }
                                .plan-child-device:hover{
                                    background: rgba(70,121,250,0.1);
                                }
                            }
                            .fadedown-enter-active,
                            .fadedown-leave-active {
                                transition: opacity 0.5s;
                            }
                            .fadedown-enter,
                            .fadedown-leave-to {
                                opacity: 0;
                            }
                        }
                    }
                    .plan-child-time-wrap{
                        border-top: 1px dashed rgba(165, 176, 182, .7);
                        .plan-child-time-title{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 5px;
                            height: 30px;
                            .plan-child-time-set{
                                color: @blueColor;
                                display: flex;
                                align-items: center;
                                .line{
                                    width: 3px;
                                    height: 12px;
                                    background: @blueColor;
                                    margin-right: 4px;
                                }
                            }
                            .plan-child-time-edit{
                                display: flex;
                                align-items: center;
                                i{
                                    margin-left: 5px;
                                    color: @blueColor;
                                }
                            }
                        }
                        .plan-child-time-info{
                            margin-left: 5px;
                            .plan-child-time-info-time{
                                height: 30px;
                                line-height: 30px;
                            }
                            .plan-child-time-date-wrap{
                                display: flex;
                                .plan-child-time-data{
                                    margin-right: 7px;
                                    border-radius: 50%;
                                    overflow: hidden;
                                    height: 20px;
                                    width: 20px;
                                    text-align: center;
                                    line-height: 20px;
                                }
                                .select{
                                    color: #2C86F8;
                                    background: rgba(56, 174, 243, 0.1);
                                }
                            }
                        }
                    }
                }

            }
        }
    }
</style>
