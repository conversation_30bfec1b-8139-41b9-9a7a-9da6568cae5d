import { renderHeaderStatistics } from '@/views/specialassessment/utils/menuConfig.js';

// 重点/实时视频可调阅率、人脸卡口在线率、车辆卡口在线率
let matchingRateArr = [
  {
    title: '资产质量',
    minWidth: 80,
    slot: 'accuracyRate',
  },
  {
    title: '卡口在线率*资产质量',
    minWidth: 100,
    key: 'comRate',
    render: (h, { row }) => {
      return (
        <span>
          {row?.detail[0]?.onlineRateVO?.comRate || row?.detail[0]?.onlineRateVO?.comRate === 0
            ? `${row?.detail[0]?.onlineRateVO?.comRate}%`
            : '--'}
        </span>
      );
    },
  },
];

const defaultTableColumns = (params) => {
  let matchingRate = [];
  if (params.showColRegion && params.indexType !== 'BODY_ONLINE_RATE') {
    matchingRate = matchingRateArr;
  }
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'civilName',
      slot: 'civilName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
      renderHeader: (h, { column, index }) => {
        return renderHeaderStatistics(h, { column, index, params });
      },
    },
    // {
    //     title: '卡口总量',
    //     key: 'total',
    //     align: 'left',
    //     tooltip: true,
    //     sortable: true,
    //     minWidth: 120
    // },
    {
      title: '检测数量',
      key: 'detection',
      slot: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '在线数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '离线数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '卡口在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    ...matchingRate,
  ];
};
export { defaultTableColumns };
