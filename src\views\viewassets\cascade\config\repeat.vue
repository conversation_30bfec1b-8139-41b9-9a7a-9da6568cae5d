<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" title="重复检测" width="66.14rem" @onCancel="reset" @query="handleSave">
      <transfer-table
        :left-table-columns="columns1"
        :right-table-columns="columns2"
        :left-table-data="propertyList"
        :right-table-data="targetList"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
        @onLeftToRight="selectionChange"
      >
        <template #left-title>
          <div class="mb-sm">
            <span class="base-text-color">字段拾取</span>
          </div>
        </template>
        <template #right-title>
          <div class="mb-sm">
            <span class="base-text-color">字段名列表</span><span class="font-red ml-sm">(注:不能为空)</span>
          </div>
        </template>
      </transfer-table>
    </ui-modal>
  </div>
</template>
<script>
import cascade from '@/config/api/cascade';

export default {
  data() {
    return {
      formData: {
        list: [],
        ruleId: '',
      },
      visible: false,
      columns1: [
        { title: '字段名', key: 'propertyName' },
        { title: '注释', key: 'propertyColumn' },
      ],
      propertyList: [],
      columns2: [
        { title: '字段名', key: 'checkColumnName' },
        { title: '注释', key: 'checkColumnValue' },
        {
          title: '重复次数',
          key: 'address',
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            return h('i-input', {
              props: {
                readonly: row._read,
                value: row.repeatCount,
                number: true,
              },
              style: { 'font-size': '14px' },
              on: {
                input: (val) => {
                  row.repeatCount = val;
                  this.targetList.splice(index, 1, row);
                },
              },
            });
          },
        },
      ],
      targetList: [],
      indexRuleId: '',
      leftLoading: false,
      rightLoading: false,
    };
  },
  methods: {
    async init(processOptions) {
      this.indexRuleId = processOptions.indexRuleId;
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      this.getInitData();
    },
    // 获取原始数据
    async getInitData() {
      try {
        let params = { ruleId: this.indexRuleId };
        let {
          data: { data },
        } = await this.$http.post(cascade.configQueryAllData, params);
        let leftMap = data.leftMap;
        let dataRight = data.right;
        this.formData.list = dataRight;
        this.targetList = dataRight.filter((item) => item.id);
        this.propertyList = Object.keys(leftMap).map((key) => {
          let _checked = dataRight.some((item) => item.checkColumnName === key);
          return {
            propertyName: key,
            _checked,
            propertyColumn: leftMap[key],
          };
        });
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.error(error);
      }
    },
    selectionChange(selection) {
      this.targetList = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        obj.repeatCount = '';
        this.targetList.forEach((d) => {
          if (d.checkColumnName === item.propertyName) {
            obj.repeatCount = d.repeatCount;
          }
        });
        return obj;
      });
    },
    // 保存
    async handleSave() {
      try {
        let valid = this.targetList.filter((item, index) => {
          let reg = /^[1-9]\d*$/;
          if (item.repeatCount === '') {
            this.$Message.error(`第${index + 1}行，请输入重复次数`);
            return item;
          }
          if (!reg.test(item.repeatCount)) {
            this.$Message.error(`第${index + 1}行，重复次数仅支持输入大于0整数!`);
            return item;
          }
        });
        if (valid.length) {
          return false;
        }
        let resultFromData = { ...this.formData };
        resultFromData.list = this.targetList;
        resultFromData.ruleId = this.indexRuleId;
        await this.$http.post(cascade.configUpdateAllData, resultFromData);
        this.$Message.success('重复值检测配置成功');
        this.reset();
        this.$emit('render');
      } catch (error) {
        console.error(error);
      }
    },
    reset() {
      this.visible = false;
      this.propertyList = [];
      this.targetList = [];
    },
  },
  watch: {},
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.repeat-empty {
  @{_deep} .ivu-modal-body {
    padding: 0 50px 16px 50px;
    height: 500px;
  }
}

@{_deep} .transfer-table-wrapper {
  .left-table,
  .right-table {
    height: calc(~'100% - 35px') !important;
    margin-top: 10px;
  }
}
</style>
