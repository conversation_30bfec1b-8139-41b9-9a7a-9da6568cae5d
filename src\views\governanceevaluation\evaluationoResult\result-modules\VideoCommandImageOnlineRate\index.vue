<!-- 重点指挥图像在线率 -->
<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      :isShowDatePicker="isShowDatePicker"
      @on-change-code="onChangeOrgRegion"
    >
      <tag-view
        slot="mid"
        :list="reallyTagList"
        :default-active="defaultActive"
        @tagChange="changeStatus"
        ref="tagView"
        class="tag-view"
      ></tag-view>
    </result-title>
    <component
      :is="componentName"
      :show-col-region="showColRegion"
      v-bind="handleProps()"
      @viewDetail="viewDetail"
    ></component>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'VideoCommandImageOnlineRate',
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalEcharts',
      iconList: [],
      tagList: Object.freeze([
        {
          label: '统计图表',
          value: 'StatisticalEcharts',
        },
        {
          label: '统计列表',
          value: 'StatisticalResult',
        },
        {
          label: '检测明细',
          value: 'ReviewParticular',
        },
        {
          label: '结果比对',
          value: 'ComparisonResults',
        },
      ]),
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
      isShowDatePicker: false,
    };
  },
  created() {
    this.reallyTagList = this.tagList.map((item) => item.label);
    this.getAlldicData();
    if (!this.showColRegion) {
      this.getConfigByKeys();
    }
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
      getConfigByKeys: 'governanceevaluation/getConfigByKeys',
    }),
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    changeStatus(index) {
      this.defaultActive = index;
      this.componentName = this.tagList[index].value;
      // 结果比对 页面 检测时间 需要显示为 时间组件
      this.isShowDatePicker = index === 3 ? true : false;
    },
    // 动态组件 - 动态传参
    handleProps() {
      // 检测明细组件需要的参数 review-particular
      let props = {
        activeIndexItem: this.activeIndexItem,
      };
      // const stragetyObject = {
      //   'ReviewParticular': () => {

      //   },
      //   'StatisticalList': () => {
      //   },
      //   'StatisticalEcharts': () => {
      //   },
      // }
      // stragetyObject[this.componentName]()
      return props;
    },
    async viewDetail(row) {
      this.defaultActive = 2;
      const queryParams = this.$route.query;
      this.defaultCodeKey = queryParams.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.defaultCode = queryParams.statisticType === 'REGION' ? row.civilCode : row.orgCode;
      await this.$nextTick();
      this.$set(this, 'componentName', 'ReviewParticular');
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
      showColRegion: 'governanceevaluation/getShowColRegion',
    }),
  },
  watch: {},
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ResultTitle: require('../../components/result-title.vue').default,
    ReviewParticular: require('./review-particular.vue').default,
    StatisticalEcharts: require('@/views/governanceevaluation/evaluationoResult/common-pages/ReadabledRate/index.vue')
      .default,
    StatisticalResult: require('../../common-pages/statistical-results/index.vue').default,
    ComparisonResults: require('../../common-pages/comparison-results/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
}
</style>
