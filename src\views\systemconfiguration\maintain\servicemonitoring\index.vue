<template>
  <div class="servicemonitoring">
    <div class="left-div">
      <charts></charts>
    </div>
    <div class="right-div auto-fill">
      <search-list class="search-module" @startSearch="startSearch"></search-list>
      <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #status="{ row }">
          <div
            class="status-tag"
            :style="`background: ${!!row.status ? 'var(--color-success)' : 'var(--color-failed)'}`"
          >
            {{ !!row.status ? '在线' : '离线' }}
          </div>
        </template>
        <!--        <template #action="{ row }">-->
        <!--          <ui-btn-tip-->
        <!--              class="operatbtn"-->
        <!--              icon="icon-refresh"-->
        <!--              content="重启"-->
        <!--              @click.native="refresh(row)"-->
        <!--          ></ui-btn-tip>-->
        <!--        </template>-->
      </ui-table>
    </div>
  </div>
</template>
<script>
import maintain from '@/config/api/maintain';
export default {
  name: 'servicemonitoring',
  data() {
    return {
      loading: false,
      searchData: {},
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '服务名称', key: 'serviceName' },
        {
          title: '部署节点名称',
          render: (h, column) => {
            const row = column.row;
            return h('div', row.serverInfo.serverName);
          },
        },
        {
          title: '部署节点IP',
          render: (h, column) => {
            const row = column.row;
            return h('div', row.serverInfo.ip);
          },
        },
        { title: '端口号', key: 'port', width: 200 },
        {
          title: '状态',
          slot: 'status',
          width: 100,
        },
        // {
        //   title: '操作',
        //   slot: 'action',
        //   width: 60,
        //   fixed: 'right',
        //   align: 'center',
        //   className: 'table-action-padding',
        // },
      ],
      tableData: [],
    };
  },
  activated() {
    this.initList();
  },
  methods: {
    startSearch(data) {
      this.searchData = data;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let res = await this.$http.post(maintain.getServiceList, this.searchData);
        this.tableData = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async refresh(row) {
      this.$Modal.confirm({
        title: '警告',
        content: `您将要重启服务 ${row.serviceName}，是否确认?`,
        onOk: async () => {
          try {
            await this.$http.post(maintain.serviceRestart, { serviceId: row.serviceId });
            this.$Message.success('重启成功, 请等待');
            this.initService();
          } catch (err) {
            console.log(err);
          }
        },
      });
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchList: require('./components/search-list.vue').default,
    Charts: require('./components/charts.vue').default,
  },
};
</script>
<style lang="less" scoped>
.servicemonitoring {
  display: flex;
  padding: 0 10px 0 20px;
  height: 100%;
  background: var(--bg-content);
  overflow: hidden;
}
.left-div {
  width: 250px;
  height: 100%;
  border-right: 1px solid var(--border-color);
  .option-div {
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2px 10px;
  }
  .button-div {
    margin-top: 30px;
  }
  .offline {
    margin-top: 30px;
  }
}
.right-div {
  flex: 1;
  height: 100%;
  padding: 20px 0 20px 20px;
}
.status-tag {
  width: 60px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
}
.search-module {
  margin-bottom: 20px;
}
</style>
