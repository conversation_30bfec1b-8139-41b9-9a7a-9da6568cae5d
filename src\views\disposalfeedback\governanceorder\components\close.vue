<template>
  <div class="close-box">
    <ui-label class="flex mb-md" label="备注" required :width="40">
      <Input
        v-model="form.finishSituation"
        class="remark"
        type="textarea"
        :autosize="{ minRows: 3, maxRows: 3 }"
        placeholder="请填写关闭备注信息"
      ></Input>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      form: {
        finishSituation: '',
      },
    };
  },
  created() {},
  updated() {
    this.$emit('updateDisabled', this.form.finishSituation);
  },
  methods: {
    reset() {
      this.form.finishSituation = '';
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;
}
.close-box {
  width: 100%;
}
.remark {
  width: 100%;
}
</style>
