<template>
  <div class="dom-wrapper">
    <div
      class="dom"
      :class="{ 'no-page-height': isNoPage }"
      @click="($event) => $event.stopPropagation()"
    >
      <header>
        <span>解析详情</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="main-info-box">
              <div
                class="main-item"
                v-for="(item, index) in mainLabel"
                :key="index"
              >
                <div class="title">
                  <span>{{ item.label }}</span
                  ><span>&nbsp;：</span>
                </div>
                <div class="content" :title="item.rule == null
                      ? detailsInfo[item.key]
                      : item.rule(detailsInfo[item.key], detailsInfo)">
                  {{
                    item.rule == null
                      ? detailsInfo[item.key]
                      : item.rule(detailsInfo[item.key], detailsInfo)
                  }}
                </div>
              </div>
            </div>
          </div>
          <div class="info-box-right">
            <i
              v-if="!isNoPage"
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleTab('left')"
            ></i>
            <details-largeimg
            ref="detailsLargeimg"
              boxSeleType="rect"
              :info="{ ...detailsInfo }"
              :btnJur="['tp', 'rl', 'ytst',  'fd', 'sx', 'xz']"
              sceneImgKey="picUrl"
            >
            </details-largeimg>
            <i
              v-if="!isNoPage"
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleTab('right')"
            ></i>
          </div>
        </div>
      </section>
      <footer v-if="!isNoPage">
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleTab('left')"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="index"
            >
              <div class="img-box">
                <img v-lazy="item.picUrl" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleTab('right')"
        ></i>
      </footer>
    </div>
  </div>
</template>

<script>
import { cutMixins } from "@/views/juvenile/components/detail/mixins";
import detailsLargeimg from "./details-largeimg.vue";
export default {
  name: "everySearchDetailMutiAnalysis",
  mixins: [cutMixins], //全局的mixin
  // 这边递归调用了
  components: { detailsLargeimg },
  props: {
    tableList: {
      type: Array,
      default: () => [],
    },
    isNoPage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mainLabel: [
        { label: "时间", key: "absTime"},
        {
          label: "地点",
          key: "deviceName",
        },{
          label: "得分",
          key: "score",
        }
      ],
      cutList: [],
      detailsInfo: {},
    };
  },
  methods: {
    // 展开内容初始化
    showList(nowIndex) {
      this.cutList = this.tableList.map((item) => ({
        ...item,
      }));
      this.activeIndex = nowIndex;
      let info = this.cutList[nowIndex];
      this.detailsInfo = {
        ...info,
      };
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(nowIndex);
      });
    },
    // 切换
    handleTab(item, index) {
      if (item == "left") {
        // 到第一页了
        if (this.activeIndex == 0) {
          return this.$Message.warning("已经是第一页");
        }
        index = this.activeIndex - 1;
        item = this.cutList[index];
      }
      if (item == "right") {
        // 到最后一页了
        if (this.activeIndex + 1 == this.cutList.length) {
          return this.$Message.warning("已经是最后一页");
        }
        index = this.activeIndex + 1;
        item = this.cutList[index];
      }

      this.detailsInfo = {
        ...item,
      };
      this.activeIndex = index;
      this.play(index);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.no-page-height {
  height: fit-content !important;
}
.dom-content {
  padding: 20px 20px 10px 20px !important;
}
.info-box-left {
  width: 260px !important;
  .main-info-box {
    border-bottom: 1px dashed #d3d7de;
    .main-item {
      display: flex;
      padding-bottom: 12px;
    }
  }
  .label-info-box {
    padding: 15px 0 10px 0;
    border-bottom: 1px dashed #d3d7de;
    .labels-list {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      padding-top: 10px;
      .label-item {
        background: #ecf4ff;
        border-radius: 2px 2px 2px 2px;
        font-size: 12px;
        color: #2c86f8;
        padding: 1px 6px;
      }
    }
  }
  .image-text-box {
    padding: 15px 0 10px 0;
    .texts-list {
      .text-item {
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.9);
        padding-bottom: 6px;
      }
    }
  }
  .primary-title {
    display: flex;
    align-items: center;
    .point {
      width: 5px;
      height: 5px;
      background: #2c86f8;
      margin-right: 8px;
    }
    span {
      font-weight: bold;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
.info-box-right {
  width: 1260px;
  .description-box {
    width: 100%;
    height: 100px;
    background: #ebedf1;
    padding: 13px 20px;
    overflow: auto;
    display: flex;
    border: 1px solid #d3d7de;
    border-top: none;
    span {
      flex: 1;
      display: inline-block;
      white-space: nowrap;
      height: 100%;
      font-weight: 700;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
    }
    .desc-content {
      word-break: break-all;
    }
  }
}
.title {
  white-space: nowrap;
  span:first-child {
    font-size: 14px;
    width: 67px;
    color: rgba(0, 0, 0, 0.6);
    display: inline-block;
    white-space: nowrap;
    text-align: justify;
    text-align-last: justify;
    text-justify: inter-ideograph;
  }
}
.content {
  font-weight: bold;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
