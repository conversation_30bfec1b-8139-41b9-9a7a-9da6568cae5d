<template>
  <ButtonGroup>
    <Button
      v-for="(item, index) in tabList"
      :key="index"
      :type="activeTab === item.value ? 'primary' : 'default'"
      @click="changeTab(item)"
    >
      <i v-if="item.icon" :class="['icon-font mr-xs', item.icon]"></i>
      <span>{{ item.label }}</span>
    </Button>
  </ButtonGroup>
</template>
<script>
export default {
  name: 'icontab-group',
  props: {
    tabList: {
      type: Array,
      default: () => [],
    },
    defaultTab: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      activeTab: '',
    };
  },
  mounted() {
    this.activeTab = this.defaultTab;
  },
  methods: {
    changeTab(item) {
      this.activeTab = item.value;
      this.$emit('onChangeTab', item);
    },
  },
  watch: {
    defaultTab: {
      handler(val) {
        this.activeTag = val;
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped></style>
