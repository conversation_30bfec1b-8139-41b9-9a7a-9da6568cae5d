<template>
  <advancedSearch></advancedSearch>
</template>
<script>
import { mapActions } from "vuex";
import advancedSearch from "./advanced-search/index";
export default {
  name: "search-center",
  components: { advancedSearch },
  data() {
    return {};
  },
  async created() {
    await this.getDictData();
  },
  watch: {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
  },
};
</script>
