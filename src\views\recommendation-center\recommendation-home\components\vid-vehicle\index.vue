<!--
    * @FileDescription: 人-车推荐
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
  <div class="out-person">
    <div class="left-search">
      <div class="title">
        <p>疑似人-车辆关系绑定推荐</p>
      </div>
      <div class="form-box">
        <Form :inline="true" :label-width="75">
          <FormItem label="视频身份:">
            <Input
              v-model="queryParam.vid"
              placeholder="请输入"
              maxlength="50"
            ></Input>
          </FormItem>
          <FormItem label="开始时间:">
            <DatePicker
              v-model="queryParam.startTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              :clearable="false"
              placeholder="开始时间"
              transfer
              style="width: 100%"
            ></DatePicker>
          </FormItem>
          <FormItem label="结束时间:">
            <DatePicker
              v-model="queryParam.endTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              :clearable="false"
              placeholder="结束时间"
              transfer
              style="width: 100%"
            ></DatePicker>
          </FormItem>
        </Form>
        <div class="btn-group">
          <Button type="primary" @click="searchHandle"
            >查询</Button
          >
          <Button @click="resetHandle">重置</Button>
        </div>
      </div>
    </div>
    <div class="right-list">
      <div class="two-list">
        <div class="top-box">
          <div class="title">
            <p>主驾车辆：</p>
          </div>
          <div class="table-content">
            <div v-for="(item, index) in dataList1" :key="index" class="card-item">
              <UiListCard type="vehicle" :showBar="false" :data="item" :collectIcon="false" @archivesDetailHandle="archivesDetailHandle(item, 1)" />
            </div>
            <ui-empty v-if="dataList1.length === 0 && !loading"></ui-empty>
            <ui-loading v-if="loading"></ui-loading>
          </div>
        </div>
        <div class="bottom-box">
          <div class="title">
            <p>副驾车辆：</p>
          </div>
          <div class="table-content">
            <div v-for="(item, index) in dataList2" :key="index" class="card-item">
              <UiListCard type="vehicle" :showBar="false" :data="item" :collectIcon="false" @archivesDetailHandle="archivesDetailHandle(item, 2)" />
            </div>
            <ui-empty v-if="dataList2.length === 0 && !loading"></ui-empty>
            <ui-loading v-if="loading"></ui-loading>
          </div>
        </div>
      </div>
    </div>

    <peopleCar ref="peopleCar" />
  </div>
</template>
<script>
import { vidVehicleBindingList } from "@/api/recommend";
import UiListCard from '@/components/ui-list-card'
import peopleCar from './components/people-car-model.vue'
export default {
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  components: { UiListCard, peopleCar },
  data() {
    return {
      queryParam: {
        startTime: "",
        endTime: "",
        vid: "",
      },
      loading: false,
      dataList: [],
      dataList1: [],
      dataList2: [],
    };
  },
  mounted() {
    // 推荐中心查看
    console.log("taskParams: ", this.taskParams);
    if (!Toolkits.isEmptyObject(this.taskParams)) {
      this.$nextTick(() => {
        if (this.taskParams.queryStartTime)
          this.queryParam.startTime = this.taskParams.queryStartTime;
        if (this.taskParams.queryEndTime)
          this.queryParam.endTime = this.taskParams.queryEndTime;
        if (this.taskParams.params.vid)
          this.queryParam.vid = this.taskParams.params.vid
        if (this.taskParams.taskResult) this.searchHandle();
      });
    }
  },
  methods: {
    // 查询
    searchHandle() {
      if (this.queryParam.startTime == "" && this.queryParam.endTime == "") {
        this.$Message.warning("开始时间或结束时间不能为空");
        return;
      }
      if (this.queryParam.startTime > this.queryParam.endTime) {
        this.$Message.warning("开始时间不能大于结束时间！");
        return;
      }
      this.getDataList();
    },
    getDataList() {
      this.loading = true;
      let startTime = this.$dayjs(this.queryParam.startTime).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      let endTime = this.$dayjs(this.queryParam.endTime).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      let params = {
        endTime: endTime,
        startTime: startTime,
        vid: this.queryParam.vid,
      };
      vidVehicleBindingList(params)
        .then((res) => {
          this.dataList = res.data || [];
          this.dataList1 = this.dataList.filter(v => v.driverFlag === 1)
          this.dataList2 = this.dataList.filter(v => v.driverFlag === 2)
        })
        .finally(() => {
          this.loading = false;
        });
    },
    resetHandle() {
      this.dataList = [];
      this.dataList1 = [];
      this.dataList2 = [];
      this.queryParam = {
        startTime: "",
        endTime: "",
        vid: "",
      };
    },
    // 档案详情
    archivesDetailHandle(row, driverFlag) {
      let startTime = this.$dayjs(this.queryParam.startTime).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      let endTime = this.$dayjs(this.queryParam.endTime).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      let params = {
        endTime: endTime,
        startTime: startTime,
        vid: row.vid,
      };
      this.$refs.peopleCar.show(params, driverFlag);
    },
  },
};
</script>
<style lang='less' scoped>
.out-person {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  .left-search {
    width: 370px;
    height: inherit;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
    border-radius: 4px 4px 4px 4px;
    margin-right: 10px;
    .form-box {
      padding: 10px;
      .btn-group {
        display: flex;
        button {
          flex: 1;
        }
      }
    }
  }
  .right-list {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    .two-list {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      .top-box {
        height: 49.5%;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
        border-radius: 4px 4px 4px 4px;
        display: flex;
        flex-direction: column;
        position: relative;
        margin-bottom: 10px;
      }
      .bottom-box {
        height: 49.5%;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
        border-radius: 4px 4px 4px 4px;
        display: flex;
        flex-direction: column;
        position: relative;
      }
      .table-content {
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        flex: 1;
        margin: 0 -5px;
        align-content: flex-start;
        position: relative;
        padding: 10px;
        .card-item {
          width: 25%;
          padding: 0 5px;
          box-sizing: border-box;
          margin-bottom: 10px;
          transform-style: preserve-3d;
          transition: transform 0.6s;
          overflow: hidden;
          .list-card {
            width: 100%;
            backface-visibility: hidden;
            height: 198px;
            overflow: hidden;
            /deep/.list-card-content-body {
              height: 115px;
            }
            /deep/.content-img {
              width: 115px;
              height: 115px;
            }
            /deep/.tag-wrap {
              margin-top: 7px;
              .ui-tag {
                margin: 0 5px 0 0 !important;
              }
            }
          }
        }
      }
    }
  }
  /deep/.ivu-form-item {
    margin-bottom: 10px;
    width: 100%;
  }
  .title {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    height: 40px;
    position: relative;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #d3d7de;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 0;
    z-index: 1;
    background: #fff;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:before {
      content: "";
      position: absolute;
      width: 3px;
      height: 20px;
      top: 50%;
      transform: translateY(-50%);
      left: 10px;
      background: #2c86f8;
    }
    span {
      color: #2c86f8;
    }
    /deep/.ivu-icon-ios-close {
      font-size: 30px;
      cursor: pointer;
    }
  }
}
</style>