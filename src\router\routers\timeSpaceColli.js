import BasicLayout from '@/layouts/basic-layout';
export default [
    {
        path: '/model-market1',
        name: 'model-market1',
        component: BasicLayout,
        children: [
            {
                path: '/model-market/fusion-tricks/timeSpaceColli/face',
                name: 'timeSpaceColli-face',
                tabShow: true,
                parentName: 'model-market/fusion-tricks/timeSpaceColli',
                component: (resolve) => require(['@/views/model-market/fusion-tricks/timeSpaceColli/face.vue'], resolve),
                meta: {
                    title: '时空碰撞-人脸碰撞'
                }
            },
            {
                path: '/model-market/fusion-tricks/timeSpaceColli/vehicle',
                name: 'timeSpaceColli-vehicle',
                tabShow: true,
                parentName: 'model-market/fusion-tricks/timeSpaceColli',
                component: (resolve) => require(['@/views/model-market/fusion-tricks/timeSpaceColli/vehicle.vue'], resolve),
                meta: {
                    title: '时空碰撞-车辆碰撞'
                }
            },
        ]
    },
]