<template>
  <div class="index-statistics" v-ui-loading="{ loading: abnormalListLoading, tableData: abnormalList }">
    <icon-statistics :statistics-list="abnormalList" :isflexfix="false" :listyle="listyle"></icon-statistics>
  </div>
</template>

<script>
import AbnormalListConfig from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AbnormalListConfig';
import evaluationoverview from '@/config/api/evaluationoverview';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'index-statistics',
  components: {
    IconStatistics: require('@/components/icon-statistics').default,
  },
  props: {
    customParameters: {},
    listyle: {
      default: () => {
        return {
          height: '0.53rem',
          width: '1.846rem',
        };
      },
    },
  },
  mixins: [dealWatch],
  data() {
    return {
      abnormalList: [],
      abnormalListLoading: false,
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getStatInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    /**
     * actualNum        检测总数
     * qualifiedNum        合格数
     * unqualifiedNum        不合格数
     * deviceNum        设备总数
     * resultValue        **率
     * detectionCityOrCountyCount        检测地市数量
     * detectionCityOrCountyUnQualifiedCount        不达标地市数量
     * @returns {Promise<void>}
     */
    async getStatInfo() {
      const { regionCode, orgCode, statisticType, indexId, access, batchId, canPlay } = this.$route.query;
      let informationStatistics = AbnormalListConfig.find((item) => item.indexId === `${indexId}`) || {};
      let abnormalList = this.$util.common.deepCopy(informationStatistics.abnormalList || []);
      const importantIdList = [4001, 4002, 4003, 4004];
      let params = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        customParameters: this.customParameters,
        isImportant: importantIdList.includes(indexId) ? '1' : undefined,
      };
      if (canPlay && canPlay !== '2') {
        Object.assign(params, {
          customParameters: {
            ...this.customParameters,
            canPlay: canPlay,
          },
        });
      }
      this.abnormalListLoading = true;
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        let statInfo = data || {};
        if (Object.keys(statInfo).length === 0) {
          return (this.abnormalList = abnormalList);
        }
        const resultKeys = ['resultValue', 'resultValueFormat'];
        abnormalList.forEach((value, index) => {
          value.name = value.name.replace('地市', this.getOrgName(data.dataType));
          value.value = statInfo[value.key] || 0;
          if (resultKeys.includes(value.key) && statInfo.qualified === '1') {
            value.qualified = true;
          } else if (resultKeys.includes(value.key) && statInfo.qualified !== '1') {
            value.qualified = false;
          }
          abnormalList[index] = { ...statInfo, ...value };
        });
        this.abnormalList = abnormalList;
      } catch (err) {
        console.log(err);
      } finally {
        this.abnormalListLoading = false;
      }
    },
    getOrgName(type) {
      //1省 2市 3区县
      switch (type) {
        case '1':
          return '地市';
        case '2':
        case '3':
          return '区县';
        default:
          return '地市';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.index-statistics {
  position: relative;
  height: 100%;
  width: 100%;
}
</style>
