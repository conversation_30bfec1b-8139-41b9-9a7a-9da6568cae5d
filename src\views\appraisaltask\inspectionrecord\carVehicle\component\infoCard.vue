<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card">
    <div style="width: 100%">
      <div class="img">
        <ui-image :src="list.imageUrl" class="ui-image-card" />
        <p class="shadow-box" style="z-index: 11" title="查看大图">
          <i class="icon-font icon-yichang search-icon mr-xs base-text-color" @click="viewBigPic(list.imageUrl)"></i>
        </p>
      </div>

      <div class="ui-gather-card-image-item" v-for="(item, index) in cardInfo" :key="index">
        <p v-if="!item.type" style="display: flex; flex-wrap: nowrap">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-value"
            :style="{ color: item.color ? item.color : '#ffffff' }"
            >{{ list[item.value] || '缺失' }}</span
          >
        </p>
        <p v-else style="display: flex; flex-wrap: nowrap">
          <span
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-type"
            :style="{ color: item.color ? item.color : '#ffffff' }"
            >{{ list[item.value] || '缺失' }}</span
          >
        </p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      imgList: [],
      bigPictureShow: false,
    };
  },
  computed: {},
  created() {},
  methods: {
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
  },
  watch: {},
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  // min-height: 158px;
  margin-bottom: 10px;
  padding: 10px;
  background: #0f2f59;
  &-left {
    width: 138px;
    height: 138px;
    margin-right: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
    }
  }
  .tag {
    margin-right: 10px;
    padding: 3px 10px;
    font-size: 12px;
    background-color: #0e8f0e;
    color: #ffffff;
    border-radius: 4px;
  }
}
.ui-gather-card-image-item {
  font-size: 14px;
  margin: 4px 0 4px 0;
}
.ui-image-card {
  height: 150px;
  cursor: pointer;
}
.ui-gather-card-right-item-type {
  white-space: nowrap;
  width: 200px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
}
.shadow-box {
  height: 28px;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  bottom: 0;
  display: none;
  padding-left: 10px;
  > i:hover {
    color: var(--color-primary);
  }
}
.img:hover {
  position: relative;
  .shadow-box {
    display: block;
  }
}
.ui-gather-card-right-item-label {
  display: inline-block;
  color: #8797ac;
}
.ui-gather-card-right-item-value {
  display: inline-block;
  width: 62%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
// @{_deep} .ivu {
//   &-tag{
//     &:hover{
//       background: var(--color-primary) !important;
//     }
//   }
// }
</style>
