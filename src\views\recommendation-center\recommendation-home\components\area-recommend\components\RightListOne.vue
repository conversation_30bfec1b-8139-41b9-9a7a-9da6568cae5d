<template>
    <div class="right-list-one" style="width: 300px">
        <div class="left-search-content" style="top:0;">
            <p class="result-header">
                共 <span class="t-blue-color">{{total}}</span> 条记录
            </p>
            <div class="result-item" style="bottom:10px;">
                <Scroll :on-reach-bottom="handleReachBottom" height="100%" :loading-text="loadingText" :distance-to-edge="10">
                    <li class="result-item-li"
                        v-for="(item,index) in appearList"
                        :key="index"
                        @click="showPic(item,index)">
                        <div class="result-item-li-box">
                            <div class="result-item-img">
                                <img :src="item.traitImg" v-viewer="{ url: 'data-src'}" :data-src="item.sceneImg" alt="" @click.stop="">
                            </div>
                            <ul class="result-item-info">
                                <li class="ellipsis">
                                    <span class="iconfont icon-time t-blue-color mr-5"></span>
                                    <span>{{item.absTime}}</span>
                                </li>

                                <li class="ellipsis">
                                    <span class="iconfont icon-location t-blue-color mr-5"></span>
                                    <span :title="item.deviceName">{{item.deviceName}}</span>
                                </li>
                            </ul>
                        </div>
                    </li>
                </Scroll>
                <ui-empty v-if="appearList.length == 0 && !loading"></ui-empty>
                <ui-loading v-if="loading"></ui-loading>
            </div>    
        </div>
    </div>
</template>

<script>
export default {
    data(){
        return {
            currentIndex: null,
        }
    },
    props: {
        appearList: {
            type: Array,
            default: []
        },
        loading: {
            type: Boolean,
            default: false
        },
        loadingText: {
            type: String,
            default: '加载中'
        },
        total: {
            type: Number,
            default: 0
        }
    },
    methods: {
        showPic(item, index){
            if(index || index == 0){
                this.currentIndex = index;
            }
            this.$emit('show-pic', item, index);
        },
        handleReachBottom () {
            this.$emit('handleReachBottom');
        },
    }
}
</script>

<style lang="less" scoped>
    .t-blue-color {
        color: #2c86f8;
    }
    .f-fl {
        float: left;
    }
    .f-fr {
        float: right;
    }
    .ellipsis {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        cursor: default;
    }
    .right-list-one{
        position: absolute;
        width: 300px;
        height: auto;
        top: 10px;
        bottom: 10px;
        right: 10px;
        // color: #666;
        border-radius: 5px;
        // background-color: #fff;
        background: #fff;
        opacity: 0.95;
        overflow: hidden;
        z-index: 999;
        .left-search-content {
            position: absolute;
            top: 40px;
            bottom: 0;
            width: 100%;
            margin-top: 0;
            .result-header{
                height: 40px;
                line-height: 40px;
                border-bottom: 1px #d3d7de solid;
                margin-right: 10px;
                text-align: right;
            }
            .result-item {
                position: absolute;
                top: 40px;
                left: 0;
                right: 0;
                bottom: 90px;
                min-height: 62px;
                /deep/ .ivu-scroll-wrapper {
                    height: 100%;
                    width: 100%;
                    position: initial;
                    .ivu-scroll-container {
                        overflow: auto;
                        height: 100%;
                    }
                }
                .result-item-li {
                    padding: 10px;
                    border-bottom: 1px solid #d3d7de;
                    cursor: pointer;
                    overflow: hidden;
                    list-style: none;
                    .result-item-li-box {
                        // padding: 10px;
                        overflow: hidden;
                        &:hover{
                            background-color: #f3f3f3;
                        }
                        .result-item-img {
                            float: left;
                            position: relative;
                            z-index: 1;
                            img{
                                width: 80px;
                                height: 100px;
                            }
                        }
                        .result-item-info {
                            margin-left: 90px;
                            line-height: 20px;
                            position: relative;
                            z-index: 1;
                            .ellipsis{
                                cursor: pointer;
                            }
                            span{
                                vertical-align: middle;
                            }
                        }
                    }
                }
            }
        }

        .together-people-box {
            position: absolute;
            width: 280px;
            height: auto;
            top: 10px;
            bottom: 10px;
            right: 10px;
            // color: #666;
            // background-color: #fff;
            border-radius: 5px;
            overflow: hidden;
            z-index: 999;
            .people-header{
                height: 40px;
                line-height: 40px;
                padding-left: 20px;
                border-bottom: 1px solid #d3d7de;
                span{
                    color: #47a3ff;
                }
            }
            .result-item {
                position: absolute;
                top: 40px;
                bottom: 0;
                left: 0;
                right: 0;
                .result-item-li{
                    overflow: hidden;
                    border-bottom: 1px solid #d3d7de;
                    .result-item-li-box {
                        overflow: hidden;
                        position: relative;
                        &:hover{
                            background-color: #f3f3f3;
                        }
                        .item-distance{
                            position: absolute;
                            left: 45%;
                            top: 30%;
                            color: #47a3ff;
                            font-weight: bold;
                        }
                        .look-location{
                            position: absolute;
                            left: 41%;
                            top: 68%;
                            font-weight: bold;
                            cursor: pointer;
                            text-decoration: underline;
                            font-size: 12px;
                        }
                        .active-item{
                            color: #47a3ff;
                        }
                        .result-item-li-deviceName {
                            height: 30px;
                            line-height: 30px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            cursor: default;
                            span{
                                display: inline-block;
                                width: 26px;
                                height: 26px;
                                line-height: 17px;
                                text-align: center;
                                background: url("~@/assets/img/mark-blue.png") no-repeat;
                                background-size: cover;
                            }
                        }
                        .main-people {
                            width: 50%;
                            text-align: center;
                            .result-item-img{
                                img{
                                    width: 80px;
                                    height: 100px;
                                    cursor: pointer;
                                }
                            }
                            p{
                                height: 30px;
                                line-height: 30px;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
