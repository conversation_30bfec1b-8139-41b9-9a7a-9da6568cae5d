var defaultOptions = {
        $container: null,
        $shield: null,
        min: 0,
        max: 1,
        value: 1,
        isShowTip: false,
        values: [],
        slide: null,
        start: null,
        stop: null,
        stopMove: null,
        moveDroped: null
    },
    DOWNUPTIME = 200,
    TIPTOP = '30px',
    TIPDOWN = '-13px';
var Slider = function (options) {
    this.options = $.extend({}, defaultOptions, options);
    this.$container = this.options.$container;
    this.events = {'currentMousemove': ''};
    this.moving = true;
    this.downTime = 0;
    this.upTime = 0;
    // this.valuesIndex = 0;
    this.position = {left: this.options.min, right: this.value ? this.value: this.max};
    this.init();
};
Slider.prototype = {
    init: function () {
        var self = this;
        self.$sliderArea = $('.slider-area', self.$container);
        self.$sliderContainer = $('.slider-container', self.$container);
        self.$leftHandle = $('.drag-button-left', self.$container);
        self.$rightHandle = $('.drag-button-right', self.$container);
        self.$leftTip = $('.slider-tip', self.$leftHandle);
        self.$rightTip = $('.slider-tip', self.$rightHandle);
        self.$leftTipP = $('p', self.$leftTip);
        self.$rightTipP = $('p', self.$rightTip);
        self.$slideMove = $('.slide-move', self.$container);
        self.$moveMessage = $('.move-message p', self.$container);
        self.$sliderView = $('.slider-view', self.$container);
        self.$sliderViewTime = $('.view-message p', self.$container);
        self.$sliderArea.css({
            width: self.options.min == self.options.max ? '100%' : self.$sliderContainer.width() * (self.options.value - self.options.min) / (self.options.max - self.options.min) / self.$sliderContainer.width() * 100 + '%'
        });
        self.$moveMessage.text(new Date(self.options.min).format('yyyy-MM-dd hh:mm:ss')); //临时
        self.bindEvents();
        self.showOrHideTip(self.options.isShowTip);
        self._stop({left: self.options.min, right: self.options.value});
    },
    bindEvents: function () {
        var self = this;
        this.$sliderArea.on('mousedown', function (event) {
                try {
                    if (document.selection) {
                        document.selection.empty();
                    } else {
                        document.getSelection().removeAllRanges();
                    }
                } catch (e) {}
                $(document).off('mousemove');
                self.options.$shield && self.options.$shield.show();
                self.downTime = new Date();
                // self.unSelect(true);
                var option = {
                    leftBegin: event.screenX,
                    leftOffset: $(this).position().left,
                    controlAreaWidth: $(this).width(),
                    controlTimeWidth: self.$sliderContainer.width()
                };
                var isMoveUp = false;
                if ($(event.target).hasClass('drag-button-left') || $(event.target).closest('.drag-button-left').length) {
                    self.$drag = $(event.target);
                    $(document).on('mousemove', self._bind(self._leftMouseoverHandler, self, option, 'leftMouseoverHandler'));
                    $(document).on('mouseup', self._bind(self._mouseup, self, option, 'mouseup'));
                    // self.events.currentMousemove = 'leftMouseoverHandler';
                } else if ($(event.target).hasClass('drag-button-right') || $(event.target).closest('.drag-button-right').length) {
                    $(document).on('mousemove', self._bind(self._rightMouseoverHandler, self, option, 'rightMouseoverHandler'));
                    $(document).on('mouseup', self._bind(self._mouseup, self, option, 'mouseup'));
                    // self.events.currentMousemove = 'rightMouseoverHandler';
                } else if ($(event.target).hasClass('.slide-move') || $(event.target).closest('.slide-move').length) {
                    self.moving = false;
                    self.options.stopMove && self.options.stopMove();
                    isMoveUp = true;
                    var moveOption = $.extend({}, option, {leftOffset: $('.slide-move').position().left});
                    $(document).on('mousemove', self._bind(self._moveMousemove, self, moveOption, 'moveMousemove'));
                    $(document).on('mouseup', self._bind(self._moveMouseup, self, option, 'moveMouseup'));
                    // self.events.currentMousemove = 'moveMousemove';
                } else {
                    $(document).on('mousemove', self._bind(self._bothMouseoverHandler, self, option, 'bothMouseoverHandler'));
                    $(document).on('mouseup', self._bind(self._mouseup, self, option, 'mouseup'));
                    // self.events.currentMousemove = 'bothMouseoverHandler';
                }
                self.$sliderContainer.trigger('mouseout');
                self.$sliderContainer.off('mousemove');
            });
        self.$sliderContainer.on('mousemove', self._bind(self._viewMousemove, self, {}, 'viewMousemove'));
        self.$sliderContainer.on('mouseout', function (event) {
                self.$sliderView.css({left: '-9999px'});
            });
        self.$sliderContainer.on('click', function (event) {
                var $this = $(event.target);
                if ($this.hasClass('slider-tip') || $this.closest('.slider-tip').length || $this.hasClass('drag-button')) {
                    return;
                }
                var controlTimeWidth = self.$sliderContainer.width(),
                    controlAreaWidth = self.$sliderArea.width(),
                    moveLeft = event.clientX - self.$sliderContainer.offset().left,
                    offsetLeft = self.$sliderArea.position().left;
                if (offsetLeft >= moveLeft) {
                    self.$sliderArea.css({
                            left: self._pixel2percent(moveLeft, controlTimeWidth),
                            width: self._pixel2percent(controlAreaWidth - moveLeft + offsetLeft, controlTimeWidth)
                        });
                    self._stop(self.position = self._formartResult({
                        left: moveLeft,
                        right: controlAreaWidth + offsetLeft
                    }));
                } else if (offsetLeft + controlAreaWidth <= moveLeft) {
                    self.$sliderArea.css({
                        width: self._pixel2percent(moveLeft - offsetLeft, controlTimeWidth)
                    });
                    self._stop(self.position = self._formartResult({left: offsetLeft, right: moveLeft}));
                }
            });

    },
    _moveMouseup: function (event) {
        // this.unSelect();
        this.options.$shield && this.options.$shield.hide();
        $(document).off('mousemove');
        $(document).off('mouseup', this.events.moveMouseup);
        this.$sliderContainer.on('mousemove', this._bind(this._viewMousemove, this, {}, 'mousemove'));
        var left = this.$slideMove.position().left,
            width = this.$sliderArea.width(),
            percent = Math.abs(left - width) < 1 ? 1 : left / width;
        this.options.moveDroped && this.options.moveDroped(percent);
        this.moving = true;
        return false;
    },
    _mouseup: function (event, options) {
        var self = this;
        // this.unSelect();
        this.options.$shield && this.options.$shield.hide();
        $(document).off('mousemove');
        $(document).off('mouseup', this.events.mouseup);
        this.$sliderContainer.on('mousemove', this._bind(this._viewMousemove, this, {}));
        this.upTime = new Date();
        if (this.upTime - this.downTime < DOWNUPTIME) {
            var moveLeft = event.clientX - self.$sliderContainer.offset().left,
                middleLeft = moveLeft - options.leftOffset;
            if (middleLeft < 0) {
                middleLeft = 0;
            } else if (middleLeft >= options.controlAreaWidth) {
                middleLeft = options.controlAreaWidth;
            }
            self.$slideMove.css({
                left: middleLeft / options.controlAreaWidth * 100 + '%'
            });
            var percent = middleLeft / options.controlAreaWidth;
            this.options.moveDroped && this.options.moveDroped(percent);
            this.moving = true;
        } else {
            var left = this.$sliderArea.position().left,
                right = left + this.$sliderArea.width(),
                position = self.position = this._formartResult({left: left, right: right});
            this.$slideMove.css({left: 0});
            // self.status = false;
            this._stop(position);
        }
    },
    _viewMousemove: function (event) {
        if ($(event.target).hasClass('slider-tip') || $(event.target).closest('.slider-tip').length || $(event.target).hasClass('slide-move') || $(event.target).closest('.slide-move').length) {
            this.$sliderContainer.trigger('mouseout');
            return;
        }
        var self = this,
            containerWidth = self.$sliderContainer.width(),
            offsetLeft = event.clientX - self.$sliderContainer.offset().left,
            percentage = (self.options.max - self.options.min) / containerWidth,
            mills = Math.ceil(percentage * offsetLeft) + self.options.min;
        self.$sliderView.css({
            left: offsetLeft / containerWidth * 100 + '%'
        });
        self.$sliderViewTime.text(new Date(mills).format('yyyy-MM-dd hh:mm:ss'));
    },
    _moveMousemove: function (event, options) {
        var moveLeft = event.screenX - options.leftBegin + options.leftOffset;
        if (moveLeft < 0) {
            moveLeft = 0;
        } else if (moveLeft >= options.controlAreaWidth) {
            moveLeft = options.controlAreaWidth;
        }
        this.$slideMove.css({
            left: moveLeft / options.controlAreaWidth * 100 + '%'
        });
        return false;
    },
    _leftMouseoverHandler: function (event, options) {
        var moveLeft = event.screenX - options.leftBegin + options.leftOffset;
        if (moveLeft <= 0) {
            moveLeft = 0;
        }
        if (options.leftOffset + options.controlAreaWidth <= moveLeft) {
            moveLeft = options.leftOffset + options.controlAreaWidth;
        }
        this.$sliderArea.css({
            left: this._pixel2percent(moveLeft, options.controlTimeWidth),
            width: this._pixel2percent(options.controlAreaWidth - moveLeft + options.leftOffset, options.controlTimeWidth)
        });
        this.options.slide && this.options.slide(this._formartResult({
            left: moveLeft,
            right: options.controlAreaWidth + options.leftOffset
        }));
    },
    _rightMouseoverHandler: function (event, options) {
        var moveRight = event.screenX - options.leftBegin + options.leftOffset + options.controlAreaWidth;
        if (moveRight >= options.controlTimeWidth) {
            moveRight = options.controlTimeWidth;
        }
        this.$sliderArea.css({
            width: this._pixel2percent(moveRight - options.leftOffset, options.controlTimeWidth)
        });
        this.options.slide && this.options.slide(this._formartResult({left: options.leftOffset, right: moveRight}));
    },
    _bothMouseoverHandler: function (event, options) {
        var moveLeft = event.screenX - options.leftBegin + options.leftOffset;
        if (moveLeft <= 0) {
            moveLeft = 0;
        }
        if (moveLeft + options.controlAreaWidth >= options.controlTimeWidth) {
            moveLeft = options.controlTimeWidth - options.controlAreaWidth;
        }
        this.$sliderArea.css({
            left: this._pixel2percent(moveLeft, options.controlTimeWidth)
        });
        this.options.slide && this.options.slide(this._formartResult({
            left: moveLeft,
            right: options.controlAreaWidth + moveLeft
        }));
    },
    _pixel2percent: function (pixel, controlTimeWidth) {
        return pixel / controlTimeWidth * 100 + '%';
    },

    _formartResult: function (options) {
        var percentage = (this.options.max - this.options.min) / this.$sliderContainer.width();
        return {
            left: Math.ceil(percentage * options.left) + this.options.min,
            right: Math.ceil(percentage * options.right) + this.options.min
        };
    },
    _bind: function (fn, context) {
        var args = Array.prototype.slice.call(arguments, 2);
        return (this.events[args[1]] = function () {
            var innerArgs = Array.prototype.slice.call(arguments),
                concatArgs = innerArgs.concat(args);
            fn.apply(context, concatArgs);
        });
    },
    _stop: function (position) {
        this.$leftTipP.text(new Date(position.left).format('yyyy-MM-dd hh:mm:ss'));
        this.$rightTipP.text(new Date(position.right).format('yyyy-MM-dd hh:mm:ss'));
        if (this.$sliderContainer.width() * (position.right - position.left) / (this.options.max - this.options.min) < 110) {
            this.$rightTip.css({bottom: TIPTOP});
        } else {
            this.$rightTip.css({bottom: TIPDOWN});
        }
        this.options.stop && this.options.stop(position);
    },
    move: function (index) {
        if (!this.moving) {
            return;
        }
        index = index > 1 ? 1 : index;
        this.$slideMove.css({
                left: index * 100 + '%'
            });
        var mills = Math.ceil((this.options.max - this.options.min) / this.$sliderContainer.width() * (this.$sliderArea.position().left + index * this.$sliderArea.width())) + this.options.min;
        this.$moveMessage.text(new Date(mills).format('yyyy-MM-dd hh:mm:ss'));
    },
    positionHandle: function (left, right) {
        if (!left) {
            this.$sliderArea.css({left: 0});
        }
        if (!right) {
            this.$sliderArea.css({width: '100%'});
        }
        left = this.$sliderArea.position().left;
        right = left + this.$sliderArea.width();
        var position = this.position = this._formartResult({left: left, right: right});
        this.$slideMove.css({left: '100%'});
        // self.status = false;
        this._stop(position);
    },
    showOrHideTip: function (isShow) {
        if (isShow) {
            this.$leftTip.css({left: '-60px'});
            this.$rightTip.css({left: '-60px'});
        } else {
            this.$leftTip.css({left: '-9999px'});
            this.$rightTip.css({left: '-9999px'});
        }
        this.options.isShowTip = !!isShow;
    },
    destroy:function() {
        $(document).off('mousemove');
        $(document).off('mouseup');
        this.$sliderArea.off('mousedown');
        this.$sliderContainer.off('mousemove')
        this.$sliderContainer.off('mouseout');
        this.$sliderContainer.off('click');
    }
};
export default Slider;
