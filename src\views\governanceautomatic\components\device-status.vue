<template>
  <div class="base-text-color">
    <Form :label-width="100" ref="formValidateRef" class="formInfo" :rules="ruleValidate" :model="formValidate">
      <FormItem class="mr-40" label="治理方式：" prop="checkedArr">
        <CheckboxGroup v-model="formValidate.checkedArr" @on-change="onChange">
          <div class="checkbox-box">
            <Checkbox label="vehicleDay"> 近 </Checkbox>
            <Input
              class="width-xs mr-xs ml-xs"
              :disabled="!formValidate.checkedArr.includes('vehicleDay')"
              type="number"
              placeholder=""
              v-model="formData.vehicleDay"
            ></Input>
            天，有抓拍车辆，设备设置为可用状态；
          </div>

          <div class="checkbox-box">
            <Checkbox label="faceDay"> 近 </Checkbox>
            <Input
              class="width-xs mr-xs ml-xs"
              :disabled="!formValidate.checkedArr.includes('faceDay')"
              type="number"
              placeholder=""
              v-model="formData.faceDay"
            ></Input>
            天，有抓拍人脸，设备设置为可用状态；
          </div>

          <div class="checkbox-box">
            <Checkbox label="videoDay"> 近 </Checkbox>
            <Input
              class="width-xs mr-xs ml-xs"
              :disabled="!formValidate.checkedArr.includes('videoDay')"
              type="number"
              placeholder=""
              v-model="formData.videoDay"
            ></Input>
            天，有成功调阅实时视频流记录，设备设置为可用状态。
          </div>
        </CheckboxGroup>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    editInfos: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const validateType = (rule, value, callback) => {
      if (
        value.length > 0 &&
        ((value.includes('vehicleDay') && !this.formData.vehicleDay) ||
          (value.includes('faceDay') && !this.formData.faceDay) ||
          (value.includes('videoDay') && !this.formData.videoDay))
      ) {
        callback('请输入天数');
      } else if (value.length === 0) {
        callback(new Error('请选择治理方式'));
      } else if (value.length > 0) {
        const regx = /^\+?[1-9][0-9]*$/;
        let flag =
          (this.formData.vehicleDay && !regx.test(this.formData.vehicleDay)) ||
          (this.formData.faceDay && !regx.test(this.formData.faceDay)) ||
          (this.formData.videoDay && !regx.test(this.formData.videoDay));
        flag ? callback(new Error('请输入正整数')) : '';
      }
      callback();
    };
    return {
      formValidate: {
        checkedArr: [],
      },
      formData: {
        vehicleDay: null,
        faceDay: null,
        videoDay: null,
      },
      ruleValidate: {
        checkedArr: [
          {
            required: true,
            validator: validateType,
            trigger: 'change',
          },
        ],
      },
    };
  },
  created() {},
  updated() {
    this.emitForm();
  },
  methods: {
    setFormValidate() {
      this.formValidate.checkedArr = [];
      Object.keys(this.formData).map((key) => {
        this.formData[key] = null;
      });

      if (this.editInfos.propertyJson) {
        const propertyJson = JSON.parse(this.editInfos.propertyJson);
        Object.keys(propertyJson).map((key) => {
          if (propertyJson[key]) {
            this.formValidate.checkedArr.push(key);
            this.formData[key] = propertyJson[key];
          }
        });
      }
      this.emitForm();

      this.$nextTick(() => {
        this.$refs.formValidateRef.validateField('checkedArr');
      });
    },
    emitForm() {
      let data = {};
      data.propertyJson = JSON.stringify(this.formData);
      this.$emit('getInfos', data);
    },
    onChange(val) {
      let arr = ['vehicleDay', 'faceDay', 'videoDay'];
      arr.map((item) => {
        if (!val.includes(item)) {
          this.formData[item] = null;
        }
      });
    },
  },
  watch: {
    editInfos: {
      handler() {
        this.setFormValidate();
      },
      deep: true,
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.checkbox-box {
  display: block;
  margin-bottom: 10px;
}
</style>
