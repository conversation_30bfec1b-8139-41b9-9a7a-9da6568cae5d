<!--
 * @Date: 2025-01-13 18:35:00
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-27 17:43:15
 * @FilePath: \icbd-view\src\views\juvenile\index.vue
 * @description: 重点人管控专题首页
-->
<template>
  <div class="juvenile-home-page">
    <div class="juvenile-left-box">
      <ui-card
        title="今日事件统计分析"
        padding="10,0"
        class="analysis-card flex-1 m-b10 h-520"
      >
        <ul class="analysis-list-box">
          <Row :gutter="18" :style="{ rowGap: '14px' }">
            <Col :span="8" v-for="(item, index) in analysisList" :key="index">
              <li class="analysis-item" :style="{
                background: `url(${require(`@/assets/img/importantPerson-management/analysis-item-bg-${index + 1}.png`)})`,
              }">
                <p class="count-num">{{ item.statisticsQuantity }}</p>
                <p class="data-name">{{ item.statisticsDesc }}</p>
              </li></Col
            >
          </Row>
        </ul>
      </ui-card>
      <ui-card
        title="重点人员发现报警"
        padding="20,0"
        class="criminal-record-card flex-1 m-b10"
      >
        <div slot="extra" class="card-extra">
          <div class="extra-left">
            <span
              >报警总数：<span class="primary-color">{{
                alarmTotal
              }}</span></span
            >
          </div>
          <div class="extra-right">
            <span class="more" @click="handleMore(1)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <AlarmTab
          ref="alarmTab"
          :list="alarmList"
          :loading="alarmListLoading"
        ></AlarmTab>
      </ui-card>
      <ui-card
        title="高危人员风险行为"
        padding="15,20"
        class="active-card"
        :style="{ flex: 1.2 }"
      >
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(6)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <RiskPersonnel
          :list="riskPersonnelList"
          :loading="riskPersonnelLoading"
        ></RiskPersonnel>
      </ui-card>
    </div>
    <div class="juvenile-main-box">
      <div class="map-box">
        <!-- <div class="title">
          <img src="@/assets/img/home/<USER>" alt="" srcset="" />
          <h2>未成年人保护专题</h2>
        </div> -->
        <importantPerson-map :alarmList="alarmList"></importantPerson-map>
      </div>
      <ui-card title="重点人员专题库" padding="0,20" class="main-ui-card">
        <div class="center-image">
          <div class="center-text" @click="goToLibFace">
            <div class="num">{{ allRecordNum }}</div>
            <div class="title">重点人员专题库</div>
          </div>
          <div
            class="six-start-box position-one"
            v-for="(item, index) in criminalRecord"
            :class="item.class"
            :key="index"
            v-show="item.show"
            @click="goToLibFace"
          >
            <div class="num">{{ item.num }}</div>
            <div class="title">{{ item.title }}</div>
          </div>
        </div>
      </ui-card>
    </div>
    <div class="juvenile-right-box">
      <ui-card title="重点场所人员活动" padding="0,10" class="flex-1 m-b10">
        <div slot="extra" class="card-extra">
          <div class="extra-right">
            <span class="more" @click="handleMore(3)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <FrequentTab
          ref="FrequentTab"
          :list="frequentList"
          :loading="frequentListLoading"
        ></FrequentTab>
      </ui-card>
      <ui-card title="人员轨迹异常报警" :padding="10" class="flex-1 m-b10">
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left"></div>
          <div class="extra-right">
            <span class="more" @click="handleMore(2)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <NightOutTab
          :list="schoolOutList"
          :loading="schoolOutListLoading"
        ></NightOutTab>
      </ui-card>
      <ui-card
        title="人员跨域流动入侵"
        :padding="10"
        class="darktime-card flex-1 m-b10"
      >
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left"></div>
          <div class="extra-right">
            <span class="more" @click="handleMore(5)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <NightOutTab
          :type="2"
          :list="nightOutList"
          :loading="nightOutListLoading"
        ></NightOutTab>
      </ui-card>
      <ui-card
        title="与其他重点人同行"
        :padding="0"
        class="together-card flex-1"
      >
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left"></div>
          <div class="extra-right">
            <span class="more" @click="handleMore(4)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <TogetherTab
          ref="togetherTab"
          :list="recordWithList"
          :loading="recordWithListLoading"
        ></TogetherTab>
      </ui-card>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import AlarmTab from "@/views/juvenile/components/alarm-tab.vue";
import FrequentTab from "./components/frequent-tab.vue";
import TogetherTab from "./components/together-tab.vue";
import NightOutTab from "./components/night-out.vue";
import RiskPersonnel from "./components/risk-personnel.vue";
import ImportantPersonMap from "./components/importantPerson-map";
import UiImage from "@/components/ui-image.vue";
import { MenuItem } from "view-design";
import {
  personBizLabelStatistics,
  queryTodayStatistic,
  faceAlarmPageList,
  trackAbnormalAlarmPageList,
  abnormalActivityPageList,
  intrusionPageList,
  travelAlongDetailPageList,
  riskBehaviorPageList,
} from "@/api/monographic/importantPerson-management.js";
import { getConfigDate } from "@/util/modules/common.js";
export default {
  name: "importantPerson-management",
  components: {
    AlarmTab,
    FrequentTab,
    TogetherTab,
    NightOutTab,
    RiskPersonnel,
    ImportantPersonMap,
    MenuItem,
  },
  data() {
    return {
      analysisList: [
        // {
        //   num: 0,
        //   title: "重点人员报警",
        // },
        // {
        //   num: 0,
        //   title: "高危人员风险行为",
        // },
        // {
        //   num: 0,
        //   title: "重点场所人员活动",
        // },
        // {
        //   num: 0,
        //   title: "与其他重点人同行",
        // },
        // {
        //   num: 0,
        //   title: "人员跨域流动入侵",
        // },
        // {
        //   num: 0,
        //   title: "人员轨迹异常报警",
        // },
      ],
      alarmTotal: 0,
      // type: "index",
      columns: [
        {
          title: "排名",
          key: "sort",
          align: "center",
          width: "40",
          render: (h, { index }) => {
            if (index > 2) {
              return h("div", index + 1);
            }
            let richList = ["one", "two", "three"];
            let image = require(`@/assets/img/rich-${richList[index]}.png`);
            if (index <= 2) {
              return h(
                "div",
                {
                  style: {
                    width: "20px",
                    height: "20px",
                    margin: "0 auto",
                  },
                },
                [
                  h(UiImage, {
                    props: {
                      alt: index,
                      src: image,
                      viewer: false,
                    },
                  }),
                ]
              );
            }
          },
        },
        {
          title: "姓名",
          key: "name",
          align: "center",
          width: "80",
          ellipsis: true,
        },
        { title: "年龄", key: "age", align: "center", width: "60" },
        {
          title: "活跃度",
          key: "activeScore",
          align: "center",
          width: "80",
          render: (h, { row, index }) => {
            let colorArr = ["#F29F4C", "#2C86F8"];
            if (index < 3) {
              return h(
                "div",
                { style: { color: colorArr[0], fontWeight: "bold" } },
                row.activeScore
              );
            } else {
              return h(
                "div",
                { style: { color: colorArr[1], fontWeight: "bold" } },
                row.activeScore
              );
            }
          },
        },
        {
          title: "最后一次出现时间",
          key: "lastActiveTime",
          align: "center",
          tooltip: "light",
        },
      ],
      criminalRecord: [
        { num: "0", title: "", class: "position-one", show: false },
        { num: "0", title: "", class: "position-two", show: false },
        { num: "0", title: "", class: "position-three", show: false },
        { num: "0", title: "", class: "position-four", show: false },
        { num: "0", title: "", class: "position-five", show: false },
        { num: "0", title: "", class: "position-six", show: false },
      ],
      allRecordNum: 0,
      entertainmentList: [],
      showEnterMore: false,
      alarmList: [],
      alarmListLoading: false,
      frequentList: [],
      frequentListLoading: false,
      schoolOutList: [],
      schoolOutListLoading: false,
      nightOutList: [],
      nightOutListLoading: false,
      recordWithList: [],
      recordWithListLoading: false,
      alarmConfigInfo: {},
      riskPersonnelLoading: false,
      riskPersonnelList: [],
      dateRange: {},
    };
  },
  computed: {
    ...mapGetters({
      placeSecondLevelList: "dictionary/getPlaceSecondLevelList", // 场所二级分类
      targetObj: "systemParam/targetObj",
    }),
    // 后四个放到更多
    moreEntertainmentList() {
      return this.entertainmentList.slice(4);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.initDate();
    this.getAlarmList();
    this.getNowDateStatistics();
    this.getRecordStatistics();
    this.getSchoolOutList();
    // 高危人员风险行为
    this.getRiskPersonnelList();
    // 重点场所人员活动
    this.getFrequentList();
    this.getNightOutList();
    this.getRecordWithList();
  },
  watch: {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    initDate() {
      const [startTime, endTime] = getConfigDate(-7);
      this.dateRange = {
        startTime: startTime + " 00:00:00",
        endTime: endTime + " 23:59:59",
      };
    },
    async getNowDateStatistics() {
      const { data } = await queryTodayStatistic();
      this.analysisList = data || [];
    },
    getRecordStatistics() {
      personBizLabelStatistics().then(({ data }) => {
        // 不足就能展示几个就几个，超过就取top6
        if (!data) {
          data = [];
        }
        this.allRecordNum = data.count;
        delete data.count;
        if (Object.keys(data).length <= 6) {
          Object.keys(data).forEach((key, index) => {
            this.criminalRecord[index].title = key;
            this.criminalRecord[index].num = data[key];
            this.criminalRecord[index].show = true;
          });
        } else {
          this.allRecordNum = 0;
          Object.keys(data).forEach((key, index) => {
            if (index > 5) {
              return;
            }
            this.criminalRecord[index].title = key;
            this.criminalRecord[index].num = data[key];
            this.criminalRecord[index].show = true;
            this.allRecordNum += data[key];
          });
        }
      });
    },
    getAlarmList() {
      this.alarmListLoading = true;
      const date = new Date();
      let param = {
        pageNumber: 1,
        pageSize: 3,
        alarmTimeB: "2000-01-01 00:00:00",
        alarmTimeE: date.format("yyyy-MM-dd") + " 23:59:59",
      };
      faceAlarmPageList(param)
        .then(({ data }) => {
          const arr = data?.entities || [];
          arr?.forEach((item) => {
            let info = this.targetObj.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
          this.alarmList = arr;
          this.alarmTotal = data.total || 0;
        })
        .finally(() => {
          this.alarmListLoading = false;
        });
    },
    getFrequentList() {
      this.frequentListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 9,
        ...this.dateRange,
      };
      abnormalActivityPageList(param)
        .then(({ data }) => {
          this.frequentList =
            data?.entities?.map(
              ({ faceCapture, emphasisArchive, ...item }) => ({
                ...item,
                ...faceCapture,
                ...emphasisArchive,
              })
            ) || [];
        })
        .finally(() => {
          this.frequentListLoading = false;
        });
    },
    getSchoolOutList() {
      this.schoolOutListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      trackAbnormalAlarmPageList(param)
        .then(({ data }) => {
          this.schoolOutList = data?.entities || [];
        })
        .finally(() => {
          this.schoolOutListLoading = false;
        });
    },
    // 高危人员风险行为
    getRiskPersonnelList() {
      this.riskPersonnelLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      riskBehaviorPageList(param)
        .then(({ data }) => {
          this.riskPersonnelList =
            data?.entities?.map(({ faceCapture, ...item }) => ({
              ...item,
              ...faceCapture,
            })) || [];
        })
        .finally(() => {
          this.riskPersonnelLoading = false;
        });
    },
    getNightOutList() {
      this.nightOutListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      intrusionPageList(param)
        .then(({ data }) => {
          this.nightOutList =
            data?.entities?.map(
              ({ faceCapture, emphasisArchive, ...item }) => ({
                ...item,
                ...faceCapture,
                ...emphasisArchive,
              })
            ) || [];
        })
        .finally(() => {
          this.nightOutListLoading = false;
        });
    },
    getRecordWithList() {
      this.recordWithListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        ...this.dateRange,
      };
      travelAlongDetailPageList(param)
        .then(({ data }) => {
          this.recordWithList =
            data.entities.map(
              ({
                emphasisFaceCaptureVo,
                emphasisPersonInfo,
                srcFaceCaptureVo,
                srcPersonInfo,
                ...item
              }) => ({
                srcFaceCaptureVo,
                srcPersonInfo,
                criminalFaceCaptureVo: emphasisFaceCaptureVo,
                criminalPersonInfo: emphasisPersonInfo,
                ...item,
              })
            ) || [];
        })
        .finally(() => {
          this.recordWithListLoading = false;
        });
    },
    // 更多跳转到目标管控
    handleMore(value) {
      this.$router.push({
        name: "alarm-manager",
        query: {
          // 直接跳到人员报警
          compareType: value,
        },
      });
    },
    // 跳转人脸档案
    goToLibFace() {
      this.$router.push({
        name: "special-library",
        query: {},
      });
    },
  },
};
</script>

<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}

.h-520 {
  height: 520px;
}

.flex-1 {
  flex: 1;
}

.primary-color {
  color: #2c86f8;
}

.juvenile-home-page {
  display: flex;
  flex: 1;
  background: #dde1ea;
  position: relative;

  /deep/ .card-head .title {
    text-wrap: nowrap;
  }

  .card-extra {
    display: flex;
    color: #515a6e;
    justify-content: space-between;
    font-size: 14px;
    height: 30px;
    line-height: 30px;

    .more {
      margin-right: 6px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.35);
      cursor: pointer;
    }

    .extra-left {
      width: 200px;
    }

    .entertainment {
      width: unset;
      flex: 1;
      padding-right: 15px;
      display: flex;

      /deep/ .ivu-menu-horizontal {
        height: 100%;
        line-height: unset;
        display: flex;
        gap: 5px;
      }

      /deep/ .ivu-menu {
        &::after {
          background: none;
        }

        .ivu-menu-item {
          padding: 0;
          text-wrap: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 40px;
        }
      }

      .more-list {
        position: relative;

        .icon {
          cursor: pointer;
        }

        .more-list-menu {
          position: absolute;
          box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          width: 80px;

          /deep/ .ivu-menu {
            flex-direction: column;
            width: 100%;
            padding: 5px;

            .ivu-menu-item {
              width: 100%;
            }
          }
        }
      }
    }

    .extra-right {
      margin-right: 10px;
      width: 50px;
      text-wrap: nowrap;
      flex: 1;
    }

    .active-analysis {
      width: 150px;
      margin-right: 40px;

      .tabs-ui {
        display: flex;
        justify-content: space-between;
        font-weight: bold;

        .tab-item {
          color: #515a6e;
          cursor: pointer;
        }

        .active-item {
          color: #2c86f8;
          border-bottom: 2px solid #2c86f8;
        }
      }
    }
  }

  .juvenile-left-box {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-right: 10px;

    .analysis-card {
      flex: 0.75;
    }

    .analysis-list-box {
      padding: 0 18px;
      .analysis-item {
        display: flex;
        height: 70px;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        background: url("~@/assets/img/importantPerson-management/analysis-item-bg.png")
          no-repeat;
        background-size: 100% 100%;

        .count-num {
          font-weight: 700;
          font-size: 25px;
          color: #2c86f8;
          margin-bottom: 5px;
        }
        .data-name {
          font-weight: 400;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }

    .criminal-record-card {
      .criminal-record-extra {
        display: flex;
        color: black;
        width: 40%;
        justify-content: space-between;
      }
    }

    .active-card {
      .table-content {
        /deep/ .ivu-table-header {
          border-radius: 4px 4px 4px 4px;

          .ivu-table-cell {
            padding-left: 0;
            padding-right: 0;
            text-wrap: nowrap;
            text-overflow: ellipsis;
          }
        }

        /deep/ .ivu-table-body {
          td {
            padding: 6px 0px !important;
            height: 20px;
            line-height: 20px;

            .ivu-table-cell {
              padding-left: 0;
              padding-right: 0;
            }
          }
        }
      }

      .table-small-padding {
        /deep/ th {
          padding: 5px 0 6px 0 !important;
        }
      }
    }
  }

  .juvenile-main-box {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .map-box {
      width: 1000px;
      height: calc(~"100% - 280px");
      position: relative;

      .title {
        text-align: center;
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 50;

        h2 {
          margin-top: -42px;
        }
      }
    }

    .main-ui-card {
      height: 280px;
      background-image: url("../../assets/img/juvenile/juvenile-bg.png");
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 100% 70%;

      .center-image {
        width: 100%;
        height: 100%;
        background-image: url("../../assets/img/juvenile/juvenile-center-bg.png");
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        .center-text {
          position: absolute;
          width: 12%;
          position: absolute;
          top: 20.7%;
          text-align: center;
          color: #fff;

          .num {
            font-size: 24px;
            font-weight: bold;
          }

          .title {
            font-size: 14px;
            margin-top: 5px;
          }
        }

        .six-start-box {
          width: 108px;
          height: 108px;
          clip-path: polygon(
            50% 0%,
            100% 25%,
            100% 75%,
            50% 100%,
            0% 75%,
            0% 25%
          );
          background-image: url("../../assets/img/juvenile/six-ploy-bg.png");
          background-repeat: no-repeat;
          background-size: contain;
          background-position: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          .num {
            font-size: 24px;
            font-weight: bold;
            color: #2c86f8;
          }

          .title {
            font-size: 14px;
            margin-top: 5px;
            color: rgba(0, 0, 0, 0.6);
          }
        }

        .position-one {
          position: absolute;
          top: 40px;
          left: 62px;
        }

        .position-two {
          position: absolute;
          top: 0;
          left: 220px;
        }

        .position-three {
          position: absolute;
          top: 130px;
          left: 180px;
        }

        .position-four {
          position: absolute;
          top: 0;
          left: 652px;
        }

        .position-five {
          position: absolute;
          top: 130px;
          left: 692px;
        }

        .position-six {
          position: absolute;
          top: 40px;
          left: 810px;
        }
      }
    }
  }

  .juvenile-right-box {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 10px;

    .together-card {
      flex: 1.25;
    }
  }
}
/deep/.ui-card {
  .card-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
