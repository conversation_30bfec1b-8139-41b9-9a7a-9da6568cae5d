<template>
  <div class="situationreporting auto-fill" v-if="tabList.length">
    <div class="tag-bar mb-sm">
      <tagView
        ref="tagView"
        :default-cur-tag="defaultCurTag"
        :list="tabList"
        :no-active="tabList.length === 0"
        @tagChange="tagChange"
      />
    </div>
    <div class="info-statics mb-sm">
      <info-statics-list :staticsList="statisticsList"></info-statics-list>
    </div>
    <search-module ref="searchModule" :query-type="queryType" @startSearch="startSearch">
      <btn-bar
        slot="btnbar"
        :query-type="queryType"
        :exportLoading="exportLoading"
        :batchIds="batchIds"
        :canHandleIds="canHandleIds"
        @handleBtnClick="handleBtnClick"
      ></btn-bar>
    </search-module>
    <div class="table-module auto-fill mt-sm">
      <ui-table
        class="ui-table auto-fill"
        reserveSelection
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :defaultStoreData="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template #beginEndTime="{ row }">
          <span v-if="row.beginTime">{{ row.beginTime + '至' }}</span
          ><span>{{ row.endTime }}</span>
        </template>
        <template #status="{ row }">
          <span :style="statusColor(row.status)">{{ statusText(row.status) }}</span>
        </template>
        <template slot="action" slot-scope="{ row }">
          <ui-btn-tip
            v-if="queryType === '1'"
            class="operatbtn mr-md"
            icon="icon-bianji2"
            content="编辑"
            @click.native="handleEdit(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="operatbtn mr-md"
            icon="icon-chakanxiangqing"
            content="查看详情"
            @click.native="viewDetail(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="operatbtn mr-md"
            v-if="row.status !== '1'"
            icon="icon-fuhejieguo"
            content="审核结果"
            @click.native="viewResult(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="queryType === '2'"
            class="operatbtn mr-md"
            icon="icon-shenhe"
            :content="row.status === '1' ? '审核' : '重审'"
            @click.native="handleReport(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="row.status === '1'"
            class="operatbtn mr-md"
            icon="icon-shanchu3"
            content="删除"
            @click.native="handleDelete(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="handlePage"
        @changePageSize="handlePageSize"
      >
      </ui-page>
    </div>
    <update-report
      ref="UpdateReport"
      :isEdit="isEdit"
      :detailData="detailData"
      @updateInfo="handleUpdateInfo"
    ></update-report>
    <batch-delete-modal
      ref="BatchDeleteModal"
      :canHandleIds="canHandleIds"
      :cantHandleIds="cantHandleIds"
      @updateInfo="handleUpdateInfo"
    ></batch-delete-modal>
    <detail-info ref="DetailInfo"></detail-info>
    <report-result ref="ReportResult"></report-result>
    <report-modal
      ref="ReportModal"
      :detailData="detailData"
      :isBatch="isBatch"
      :batchIds="batchIds"
      :userCivilCode="regionCode"
      @updateInfo="handleUpdateInfo"
    ></report-modal>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import examination from '@/config/api/examination.js';
import downLoadTips from '@/mixins/download-tips';
import {
  tabListArr,
  statisticsList,
  reportTableColumns,
  auditTableColumns,
  statusArr,
} from './situationreporting.js';

export default {
  name: 'situationreporting',
  mixins: [downLoadTips],
  data() {
    return {
      loading: false,
      defaultCurTag: 0,
      tabList: [],
      queryType: '1', // 1: 系统报备（默认）  2：报备审核
      statisticsList: [],
      searchData: {},
      tableColumns: [],
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      regionCode: '', // 行政区划
      orgCode: '',
      statusObj: {},
      color: ['var(--color-warning)', 'var(--color-success)', 'var(--color-failed)'],
      batchIdsObj: [],
      isEdit: false, // 是否为编辑
      detailData: {},
      isBatch: false, // 是否为批量操作
      exportLoading: false,
      batchIds: [],
      cantHandleIds: [], // 已勾选不可操作的id集合
      canHandleIds: [], // 已勾选可操作的id集合
      defaultStoreData: [],
    };
  },
  async created() {
    let buttonpermsStr = this.$store.state.permission.permissions;
    const list = tabListArr.filter((item) => buttonpermsStr.includes(`${this.$route.name}-${item.permission}`)) || [];
    if (!list.length) {
      this.$Message.error('该用户暂无权限！');
      return false;
    }
    this.tabList = list.map((item) => item.text);
    this.queryType = list[0].type;
    this.tableColumns = this.queryType === '1' ? reportTableColumns : auditTableColumns;
    this.statisticsList = statisticsList;
    await this.setAreaList();
    this.regionCode = this.getDefaultSelectedArea.regionCode;
    await this.$refs.searchModule.startSearch();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (Object.keys(to.params).length) {
        vm.receiveParamsFromOtherPages(to.params);
      }
    });
  },
  methods: {
    ...mapActions({
      setAreaList: 'common/setAreaList',
    }),
    async tagChange(index, item) {
      this.defaultStoreData = [];
      this.queryType = tabListArr.find((tabItem) => tabItem.text === item).type;
      if (this.queryType === '1') {
        this.statisticsList[0].name = '报备总数';
        this.statisticsList[0].icon = 'icon-baobeizongshu';
        this.tableColumns = reportTableColumns;
      } else {
        this.statisticsList[0].name = '报备审核总数';
        this.statisticsList[0].icon = 'icon-baobeishenhezongshu';
        this.tableColumns = auditTableColumns;
      }
    },
    async getStatisticsListData() {
      try {
        const params = {
          queryType: this.queryType,
          userCivilCode: this.regionCode,
        };
        let {
          data: { data },
        } = await this.$http.get(examination.getStatInfo, { params });
        this.statisticsList = this.statisticsList.map((item) => {
          item[item.key] = data[item.key];
          return item;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getTableList() {
      this.loading = true;
      try {
        let params = {
          queryType: this.queryType,
          userCivilCode: this.regionCode,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        params = Object.assign(params, this.searchData);
        let {
          data: { data },
        } = await this.$http.post(examination.pageList, params);
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
        this.loading = false;
      } catch (error) {
        this.loading = false;
        console.log(error);
      }
    },
    handlePage(val) {
      this.pageData.pageNumber = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNumber = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    storeSelectList(selection) {
      this.canHandleIds = [];
      this.cantHandleIds = [];
      this.batchIds = selection.map((item) => {
        if (this.queryType === '1') {
          item.status === '1' ? this.canHandleIds.push(item.id) : this.cantHandleIds.push(item.id);
        }
        return item.id;
      });
    },
    startSearch(data) {
      this.searchData = data;
      this.handleUpdateInfo();
    },
    handleBtnClick(type) {
      const handleList = {
        add: 'handleAdd',
        audit: 'handleBatchAudit',
        export: 'getExport',
        delete: 'handleBatchDelete',
      };
      this[handleList[type]]();
    },
    handleAdd() {
      this.isEdit = false;
      this.detailData = {};
      this.$refs.UpdateReport.init();
    },
    handleBatchAudit() {
      if (!this.batchIds.length) return false;
      this.isBatch = true;
      this.$refs.ReportModal.init();
    },
    handleBatchDelete() {
      if (!this.canHandleIds.length) return false;
      this.isBatch = true;
      this.$refs.BatchDeleteModal.init();
    },
    async getExport() {
      this.exportLoading = true;
      try {
        let params = {
          queryType: this.queryType,
          userCivilCode: this.regionCode,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(examination.reportExport, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    /**
     *  表格操作条件
     *  编辑： 都可以编辑
     *  查看详情： 都可以查看详情
     *  删除： 当审核状态为 待审核
     *  审核结果： 当审核状态为 审核通过、审核不通过
     */
    viewDetail(row) {
      this.$refs.DetailInfo.init(row.id);
    },
    viewResult(row) {
      this.isBatch = false;
      this.$refs.ReportResult.init(row);
    },
    handleReport(row) {
      this.isBatch = false;
      this.detailData = this.$util.common.deepCopy(row);
      this.$refs.ReportModal.init(row.id);
    },
    handleEdit(row) {
      this.isEdit = true;
      this.detailData = this.$util.common.deepCopy(row);
      this.$refs.UpdateReport.init(row.id);
    },
    handleDelete(row) {
      this.$UiConfirm({
        content: `您要删除数据：${row.reportName}  这项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteReport([row.id]);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteReport(ids) {
      try {
        let { data } = await this.$http.delete(examination.remove + `/${ids}`);
        this.$Message.success(data.msg);
        this.handleUpdateInfo();
      } catch (e) {
        console.log(e);
      }
    },
    handleUpdateInfo() {
      this.pageData.pageNum = 1;
      this.canHandleIds = [];
      this.cantHandleIds = [];
      this.batchIds = [];
      this.defaultStoreData = [];
      this.getStatisticsListData();
      this.getTableList();
    },
    statusText(status) {
      if (!status) return '';
      const obj = statusArr.find((item) => item.value === status);
      return obj.text;
    },
    statusColor(status) {
      const obj = statusArr.find((item) => item.value === status);
      return { color: obj.color };
    },
    //接收从治理工单页面批量报备过来的数据
    receiveParamsFromOtherPages(params) {
      if (!params.purpose) {
        return;
      }
      //所有路由跳转的操作
      switch (params.purpose) {
        case 'autoChooseAndCreateByDatas':
          this.autoChooseAndCreateByDatas(params);
          break;
        case 'openReportDetailModalById':
          this.$refs.DetailInfo.init(params.detailId);
          break;
      }
    },
    // 打开新建报备并自动选中对应设备
    autoChooseAndCreateByDatas(params) {
      // 跳转到【情况报备-系统报备】节点，打开新建报备页面，自动选中以上设备进行报备
      this.queryType = '1'; //系统报备
      this.isEdit = false;
      this.$refs.UpdateReport.init();
      this.$refs.UpdateReport.formData.reportMethod = '2';
      this.$refs.UpdateReport.formData.deviceList = params.datas;
    },
  },
  computed: {
    ...mapGetters({
      getDefaultSelectedArea: 'common/getDefaultSelectedArea',
    }),
  },
  components: {
    TagView: require('@/components/tag-view').default,
    InfoStaticsList: require('./components/info-statics-list').default,
    SearchModule: require('./components/search-module.vue').default,
    BtnBar: require('./components/btn-bar.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    UpdateReport: require('./components/update-report.vue').default,
    BatchDeleteModal: require('./components/batch-delete-modal.vue').default,
    DetailInfo: require('./components/detail-info.vue').default,
    ReportResult: require('./components/report-result.vue').default,
    ReportModal: require('./components/report-modal.vue').default,
  },
};
</script>

<style lang="less" scoped>
.situationreporting {
  position: relative;
  overflow-y: auto;
  padding: 20px 20px 0;
  background: var(--bg-content);

  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }

  .tag-bar {
    width: 100%;
  }

  .info-statics {
    width: 100%;
    height: 100px;
  }

  .btn-module {
    width: 100%;
    display: flex;
    justify-content: flex-end;
  }

  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      //min-height: calc(100vh - 426px) !important;
    }
  }

  @{_deep} .statuspadding {
    padding-left: 10px;
  }
}
</style>
