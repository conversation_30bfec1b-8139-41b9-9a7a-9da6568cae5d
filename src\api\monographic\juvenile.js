/*
 * @Date: 2025-01-22 15:47:21
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-02 18:50:22
 * @FilePath: \icbd-view\src\api\monographic + domainName.js
 */
import request from "@/libs/request";
import { monographic } from "../Microservice";
import { juvenile as domainName, getRouteDomain } from "./base";

// 查询未成年人配置信息
export function getJuvenilesConfig() {
  return request({
    url: monographic + domainName + "/sysConfig/getJuvenilesConfig",
    method: "post",
  });
}

// 修改未成年人配置信息
export function updateJuvenilesConfig(data) {
  return request({
    url: monographic + domainName + "/sysConfig/updateJuvenilesConfig",
    method: "post",
    data,
  });
}

// 获取系统中配置的场所信息
export function getConfigPlaces() {
  return request({
    url: monographic + domainName + "/sysConfig/getConfigPlaces",
    method: "post",
  });
}

// 获取系统中配置的二级场所类型
export function getConfigPlaceSecondLevels() {
  return request({
    url: monographic + domainName + "/sysConfig/getConfigPlaceSecondLevels",
    method: "post",
  });
}

// 业务报警详情分页查询
export function getAlarmDetailPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/alarmDetailPageList",
    method: "post",
    data,
  });
}

// 深夜出现
export function getAppearInNightPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/appearInNightPageList",
    method: "post",
    data,
  });
}

// 娱乐场所出现次数
export function getAppearInRecreationPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/appearInRecreationPageList",
    method: "post",
    data,
  });
}

// 上课时间校外出现
export function getClassTimeOutsidePageList(data) {
  return request({
    url: monographic + domainName + "/alarm/classTimeOutsidePageList",
    method: "post",
    data,
  });
}

// 人脸告警记录分页列表
export function getFaceAlarmPageList(data) {
  return request({
    url: monographic + getRouteDomain() + "/alarm/faceAlarmPageList",
    method: "post",
    data,
  });
}

// 活跃人员分析
export function getOverviewPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/overviewPageList",
    method: "post",
    data,
  });
}

// 与前科同行
export function getTravelAlongPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/travelAlongPageList",
    method: "post",
    data,
  });
}

// 与前科同行明细
export function travelAlongDetailPageList(data) {
  return request({
    url: monographic + getRouteDomain() + "/alarm/travelAlongDetailPageList",
    method: "post",
    data,
  });
}

// 布控任务分页查询
export function getTaskPageList(data) {
  return request({
    url: monographic + getRouteDomain() + "/compare/task/pageList",
    method: "post",
    data,
  });
}

// 获取控比务主详细信息
export function getTaskInfo(id) {
  return request({
    url: monographic + getRouteDomain() + `/compare/task/view/${id}`,
    method: "get",
  });
}

// 布控任务新增
export function addTask(data) {
  return request({
    url: monographic + getRouteDomain() + "/compare/task/add",
    method: "post",
    data,
  });
}

// 布控任务更新
export function updateTask(data) {
  return request({
    url: monographic + getRouteDomain() + "/compare/task/update",
    method: "post",
    data,
  });
}

// 布控比对任务主删除
export function deleteTask(ids) {
  return request({
    url: monographic + getRouteDomain() + `/compare/task/remove/${ids}`,
    method: "post",
  });
}

// 查询人像静态库列表
export function queryFaceLibList(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/queryFaceLibList`,
    method: "post",
    data,
  });
}

// 新增人像静态库
export function addFaceLib(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/add`,
    method: "post",
    data,
  });
}

// 删除人像静态库
export function deleteFaceLib(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/delete`,
    method: "post",
    data,
  });
}

// 停用库
export function disableLib(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/disableLib`,
    method: "post",
    data,
  });
}

// 启用库
export function enableLib(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/enableLib`,
    method: "post",
    data,
  });
}
// 更新人像静态库
export function updateFaceLib(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/modify`,
    method: "post",
    data,
  });
}

// 查询人像静态库分页列表
export function getFaceLib(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/pageList`,
    method: "post",
    data,
  });
}

// 新增人像静态库人员信息
export function addLibPerson(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/person/add`,
    method: "post",
    data,
  });
}

// 批量删除人像静态库人员信息
export function deleteBatchLibPerson(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/person/batchDelete`,
    method: "post",
    data,
  });
}

// 删除人像静态库人员信息
export function deleteLibPerson(id) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/person/delete/${id}`,
    method: "post",
  });
}
// 导出
export function exportLibPerson(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/person/export`,
    method: "post",
    data,
  });
}

// 编辑人像静态库人员信息
export function updateLibPerson(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/person/modify`,
    method: "post",
    data,
  });
}

// 查询人像静态库人员信息分页列表
export function getLibPersonPageList(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/person/pageList`,
    method: "post",
    data,
  });
}

// 导入
export function uploadBatchLibPersonApi() {
  return monographic + getRouteDomain() + `/face/lib/person/upload`;
}

// 查询人像静态库列表
export function getLibPerson(data) {
  return request({
    url: monographic + getRouteDomain() + `/face/lib/queryFaceLibList`,
    method: "post",
    data,
  });
}

/**
 * 未成年人档案相关
 */
// 指定人员娱乐场所出现次数，按照二级分类统计
export function appearInRecreationStatisticsArchives(idCardNo) {
  return request({
    url:
      monographic +
      domainName +
      `/person/archives/appearInRecreationStatistics/${idCardNo}`,
    method: "get",
  });
}

// 未成年人异常行为按照小时统计
export function faceTrajectoryAbnormalStatisticsByHour(data) {
  return request({
    url:
      monographic +
      domainName +
      `/person/archives/faceTrajectoryAbnormalStatisticsByHour`,
    method: "post",
    data,
  });
}

// 查询人员案件信息
export function getPersonCase(idCardNo) {
  return request({
    url:
      monographic + domainName + `/person/archives/getPersonCase/${idCardNo}`,
    method: "get",
  });
}

// 查询人员通联信息
export function getPersonCommunication(idCardNo) {
  return request({
    url:
      monographic +
      domainName +
      `/person/archives/getPersonCommunication/${idCardNo}`,
    method: "get",
  });
}

// 查询人员警情信息
export function getPersonIncidents(idCardNo) {
  return request({
    url:
      monographic +
      domainName +
      `/person/archives/getPersonIncidents/${idCardNo}`,
    method: "get",
  });
}

// 获取最近业务报警详情
export function queryJuvenileAlarmDetail(data) {
  return request({
    url: monographic + domainName + `/person/archives/queryJuvenileAlarmDetail`,
    method: "post",
    data,
  });
}

// 获取最近与前科同行详情
export function queryTravelAlongWithCriminalDetail(data) {
  return request({
    url:
      monographic +
      domainName +
      "/person/archives/queryTravelAlongWithCriminalDetail",
    method: "post",
    data,
  });
}
/**
 * end
 */

/**
 * 统计信息
 */

// 获取系统中频繁出没娱乐场所数量
export function appearInRecreationPersonCount(data) {
  return request({
    url: monographic + domainName + "/statistics/appearInRecreationPersonCount",
    method: "post",
    data,
  });
}

// 获取系统中未成年专题布控报警的数量
export function compareAlarmCount(data) {
  return request({
    url: monographic + domainName + "/statistics/compareAlarmCount",
    method: "post",
    data,
  });
}

// 人员前科类型统计
export function personBizLabelStatistics(data) {
  return request({
    url:
      monographic + getRouteDomain() + "/statistics/personBizLabelStatistics",
    method: "post",
    data,
  });
}

// 获取同行违法前科人员数量
export function travelAlongPersonCount(data) {
  return request({
    url: monographic + domainName + "/statistics/travelAlongPersonCount",
    method: "post",
    data,
  });
}
/**
 * end
 */
