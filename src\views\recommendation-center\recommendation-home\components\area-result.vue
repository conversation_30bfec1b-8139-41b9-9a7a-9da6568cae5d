<template>
<div class="areaconfig-manger">
    <div class="areaconfig-mange-container">
        <!-- 左侧资源列表 -->
        <div class="left-resource">
            <div class="total-add">
                <span>共<span class="total">{{dataList.length || 0}}</span>个区域</span>
            </div>
            <div v-scroll class="ignorelist-wrap" v-show="dataList.length > 0">
                <li v-for="(item,index) in dataList" :key="index" @click="handleDetail(item)" class="ignorelist" :class="{'active':current.id===item.id}">
                    <img :src="item.coverUrl"/>
                    <div class="right ml-5">
                      <div class="content">
                        <span class="ignore-name ellipsis" :title="item.name">区域名称：{{item.name}} </span>
                        <span class="ignore-name ellipsis" :title="item.name">出现频次：{{item.frequencyNum || 0}} </span>
                      </div>
                    </div>
                </li>
            </div>
            <div class="empty" v-show="dataList.length < 1">暂无数据</div>
        </div>
        <div class="main-wrap">
            <!-- 地图模式 -->
            <mapBase ref="mapBase" 
              :disableScroll="false" 
              frameSelectionResultName="AreaFrameSelectionResult"
            />
        </div>
    </div>
</div>
</template>

<script>
import mapBase from '@/components/map/index.vue'
import { mapGetters } from 'vuex'
import { viewRegionalScope } from '@/api/config'

export default {
    name: "areaconfig",
    components: {
        mapBase
    },
    props: {
      taskParams: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
        return {
            dataList: [], //列表
            current: {},
        }
    },
    computed: {
      ...mapGetters({
				userInfo: "userInfo"
			}),
    },
    async created() {
      // 推荐中心查看
      console.log('taskParams: ', this.taskParams)
      if (!Toolkits.isEmptyObject(this.taskParams)) {
        let region = this.taskParams.taskResult && this.taskParams.taskResult.region ? JSON.parse(this.taskParams.taskResult.region) : []
        this.dataList = region.map(v => {
          let obj = v.region
          obj.deviceParam = JSON.parse(obj.deviceParam)
          obj.frequencyNum = v.frequencyNum
          return obj
        })
      }
		},
    methods: {
        handleDetail(record) {
          this.current = record
          this.$refs.mapBase.showSelectDraw(record, true)
        }
    }
};
</script>

<style lang="less">
.areaconfig-manger {
    height: 100%;
    width: 100%;
    position: relative;
}
.areaconfig-mange-container {
    display: flex;
    height: 100%;

    .left-resource {
        width: 350px;
        height: 100%;
        padding: 10px;
        background: #ffffff;
        position: relative;
        margin-right: 10px;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        flex-shrink: 0;
        .empty {
            text-align: center;
            margin-top: 50px;
            color: #b1b5bf;
        }
    }

    .total-add {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px 10px;

        .total {
            color: #3f73f7;
            margin: 0 2px;
        }
    }
    .ignorelist-wrap {
        width: 100%;
        height: calc(~'100% - 70px');
    }
    .right {
      height: 64px;
      flex: 1;
      .content {
        width: calc(~'100% - 30px');
        .ignore-name {
            max-width: 100%;
            display: inline-block;
            vertical-align: middle;
            font-size: 13px;
            font-weight: bold;
        }
      }
    }
    .ignorelist {
        padding-right: 5px;
        display: flex;
        align-items: center;
        img{
          width: 100px;
          height: 64px;
        }
    }
    .main-wrap {
        position: relative;
        flex:1 1 auto;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
    }

    li {
        position: relative;
        cursor: pointer;
        border-bottom: 1px dashed rgba(167,172,184,0.3);
        padding: 5px;
    }

    li:hover {
        background: rgba(70,121,250,0.1);
        color: #666666;
        .toolbar {
            display: inline-block;
        }
    }

    li.active {
        background: rgba(70,121,250,0.1);
    }

    .toolbar {
        display: inline-block;
        position: absolute;
        top: 0;
        right: 8px;
        display: none;
        i {
          color: #2C86F8;
          margin: 0 5px;
        }
    }
    .ellipsis {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
</style>
