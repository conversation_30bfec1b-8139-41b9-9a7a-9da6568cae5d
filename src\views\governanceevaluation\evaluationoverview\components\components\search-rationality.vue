<template>
  <div class="base-search">
    <ui-label class="inline" :label="global.filedEnum.deviceId" :width="70">
      <Input v-model="searchData.deviceId" class="width-lg" :placeholder="`请输入${global.filedEnum.deviceId}`"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName" :width="70">
      <Input
        v-model="searchData.deviceName"
        class="width-lg"
        :placeholder="`请输入${global.filedEnum.deviceName}`"
      ></Input>
    </ui-label>
    <ui-label class="inline ml-lg" label="检测结果" :width="70">
      <Select class="width-sm" v-model="searchData.checkStatus" clearable placeholder="请选择检测结果">
        <Option :value="1" label="正常"></Option>
        <Option :value="2" label="异常"></Option>
      </Select>
    </ui-label>
    <ui-label class="inline ml-lg" label="异常原因" :width="70">
      <Select class="width-lg" v-model="searchData.messages" placeholder="请选择异常原因" multiple :max-tag-count="1">
        <Option value="ERROR_TODAY_NO_DATA" label="昨日无抓拍"></Option>
        <Option value="ERROR_NO_DATA" label="无抓拍数据"></Option>
        <Option value="ERROR_TOO_LESS_DATA" label="抓拍数据过少"></Option>
        <Option value="ERROR_DATA_SWOOP" label="抓拍数量突降"></Option>
      </Select>
    </ui-label>
    <ui-label class="inline ml-lg" :width="0" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    width: {
      type: Number,
      default: 155,
    },
    treeData: {
      type: Array,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        messages: [],
        checkStatus: '',
      },
      dictData: {},
    };
  },
  methods: {
    getDefaultOrgObj() {
      this.searchData.orgCode = this.taskObj.orgCode;
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx1(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        deviceId: '',
        deviceName: '',
        messages: [],
        checkStatus: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: function () {
        this.getDefaultOrgObj();
        this.copySearchDataMx(this.searchData);
      },
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
}
.mt4 {
  margin-top: 4px;
}
.w200 {
  width: 200px;
}
.input-number-list {
  padding: 0px;
  width: 88px;
  /deep/ .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
</style>
