export const MODALTAG_COLUMN = [
  { title: '序号', type: 'index', align: 'center', width: 68 },
  { title: '模型名称', key: 'name', align: 'left', tooltip: true },
  { title: '主体类型', slot: 'type', align: 'left', tooltip: true },
  { title: '关联标签', slot: 'tag', align: 'left', tooltip: true },
  { title: '运行状态', slot: 'status', align: 'left', width: 160 },
  {
    title: '操作',
    slot: 'action',
    align: 'center',
    fixed: 'right',
    className: 'table-action-padding',
    width: 100,
  },
];

//主体类型下拉
export const PRINCIPAL_TYPES = [{ key: 'device', value: 1, label: '设备' }];
