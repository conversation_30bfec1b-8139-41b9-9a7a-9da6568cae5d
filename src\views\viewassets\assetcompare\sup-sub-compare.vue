<template>
  <div class="sup-sub-compare-container">
    <div class="sup-sub-compare-wrapper height-full">
      <div class="title mb-sm">
        <i class="icon-font icon-shujujiance f-18 vt-middle"></i>
        <span class="ml-sm font-white f-16">联网平台对账</span>
      </div>
      <div class="statistics-wrapper mb-md">
        <div class="statistics">
          <sup-sub-statistics :data="tableData" :loading="tableLoading"></sup-sub-statistics>
        </div>
      </div>
      <div class="title mb-md">
        <i class="icon-font icon-liebiaomoshi f-16 vt-bottom"></i>
        <span class="ml-sm font-white f-16">对账列表</span>
      </div>
      <div class="table-wrapper">
        <ui-table
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="tableLoading"
          :maxHeight="3000"
        >
          <template #total="{ row, index }">
            <div>
              <div class="mb-md">{{ row.detail.allRevocationCount || 0 }}</div>
              <div class="mb-md">
                <span class="icon-font icon-shangsheng f-16 arrow"></span>
                <span class="diff">{{ row.detail.differenceAllRevocationCount || 0 }}</span>
              </div>
              <div>{{ row.detail.beginAllRevocationCount || 0 }}</div>
            </div>
          </template>
          <template #undo="{ row, index }">
            <div class="mb-md">
              <span class="f-12 label">变化次数:</span>
              <span class="ml-sm f-14 modify">{{ row.detail.revocationCount || 0 }}</span>
            </div>
            <div class="mb-md">
              <span class="f-12 label">累加变化:</span>
              <span class="ml-sm f-14 font-white">{{ row.detail.addUpRevocationNumber || 0 }}</span>
            </div>
            <div>
              <span class="f-12 label">最终变化:</span>
              <span class="ml-sm f-14 font-white">{{ row.detail.finalRevocationNumber || 0 }}</span>
            </div>
          </template>
          <template #reconciliationCount="{ row, index }">
            <span>{{ row.detail.reconciliationCount || 0 }}</span>
          </template>
          <template #add="{ row, index }">
            <div class="mb-md add">
              {{ row.detail.addedCount || 0 }}
            </div>
            <div class="mb-md">
              {{ row.detail.addedNumber || 0 }}
            </div>
            <div>
              {{ row.detail.finalAddedNumber || 0 }}
            </div>
          </template>
          <template #modify="{ row, index }">
            <div class="mb-md modify">
              {{ row.detail.updatedCount || 0 }}
            </div>
            <div class="mb-md">
              {{ row.detail.updatedNumber || 0 }}
            </div>
            <div>
              {{ row.detail.finalUpdatedNumber || 0 }}
            </div>
          </template>
          <template #advice="{ row, index }">
            <div class="card-wrapper">
              <ui-venn :options="vennOptions(row, index)"></ui-venn>
            </div>
          </template>
          <template #action="{ row, index }">
            <create-tabs
              :componentName="themData.componentName"
              :importantTabName="themData.title"
              :tabs-text="themData.text"
              @selectModule="selectModule"
              :tabs-query="{
                id: row.id,
                batchId,
                civilCode: row.civilCode,
                type,
              }"
              class="inline mr-md"
            >
              <ui-btn-tip icon="icon-lishijilu-01" content="历史"></ui-btn-tip>
            </create-tabs>
          </template>
        </ui-table>
      </div>
    </div>
  </div>
</template>

<script>
import detectionResult from '@/config/api/detectionResult';
import {
  allIndexTypeObject,
  allIndexType,
} from '@/views/governanceevaluation/evaluationoResult/util/IndexTypeConfig.js';
import {
  VIDEO_DEVICE_REVOCATION,
  SUPSUB_BY_GB28181,
} from '@/views/viewassets/assetcompare/modules/enum.js';
import { mapGetters } from 'vuex';
import { supSubCompareOptions } from '@/views/viewassets/assetcompare/modules/vennOptions';

export default {
  name: 'sup-sub-compare',
  props: {
    batchId: {
      type: String,
    },
  },
  data() {
    return {
      type: SUPSUB_BY_GB28181,

      //create-tab
      componentName: null,
      componentLevel: 0,
      themData: {
        componentName: 'SupSubLevelCompareHistory', // 需要跳转的组件名
        text: '对账历史', // 跳转页面标题
        title: '上下级对账-对账历史',
        type: 'view',
      },

      defaultCurTag: 0,
      tabList: ['撤销情况', '新增情况', '修改情况'],

      tableLoading: false,
      tableData: [],
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'left' },
        { title: '比对对象', key: 'civilName', minWidth: 150, tooltip: true, align: 'left' },
        { title: '对账次数', slot: 'reconciliationCount', minWidth: 100, tooltip: true, align: 'left' },
        { title: '设备总量变化', slot: 'total', minWidth: 100, tooltip: true, align: 'left', className: 'compare' },
        { title: '撤销情况', slot: 'undo', minWidth: 100, tooltip: true, align: 'left', className: 'compare' },
        { title: '新增情况', slot: 'add', minWidth: 100, tooltip: true, align: 'left', className: 'compare' },
        { title: '修改情况', slot: 'modify', minWidth: 100, tooltip: true, align: 'left', className: 'compare' },
        { title: '最终变化', slot: 'advice', minWidth: 100, tooltip: true, align: 'left', className: 'compare' },
        {
          title: '操作',
          slot: 'action',
          width: 70,
          align: 'center',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      configInfo: 'assetcompare/getConfigInfo',
    }),
  },
  watch: {
    batchId: {
      deep: true,
      immediate: true,
      handler(val) {
        if (!val) return;
        this.getStatInfoList();
      },
    },
  },
  created() {},
  methods: {
    vennOptions(row, index) {
      return supSubCompareOptions({
        data: row.detail,
      })
    },
    selectModule(name) {
      if (!!this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
      this.$emit('on-change-component', this.componentName);
    },

    async getStatInfoList() {
      try {
        this.tableLoading = true;
        let params = {
          // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
          batchId: this.batchId, // 任务执行批次id
          displayType: 'REGION', // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: VIDEO_DEVICE_REVOCATION.indexId,
          orgRegionCode: this.configInfo[SUPSUB_BY_GB28181]['regionCode'],
        };
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.tableData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
  },
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    SupSubStatistics: require('@/views/viewassets/assetcompare/components/sup-sub-statistics.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    tagView: require('@/components/tag-view').default,
    UiVenn: require('@/components/ui-venn.vue').default,
  },
};
</script>
<style scoped lang="less">
.sup-sub-compare-container {
  position: relative;
  height: 100%;
  background: #071b39;

  .sup-sub-compare-wrapper {
    .title {
      padding-bottom: 15px;
      border-bottom: 1px solid #074277;

      .icon-font {
        color: #2b84e2;
      }
    }
    .statistics-wrapper {
      position: relative;
      height: 301px;
      width: 100%;
      background: #08264d;
      @{_deep} .tag {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
      }
      .statistics {
        position: relative;
        height: calc(100% - 40px);
        width: 100%;
      }
    }
    .table-wrapper {
      @{_deep} .compare {
        padding: 15px 0;
      }

      @{_deep} .icon-lishijilu-01 {
        font-size: 16px !important;
      }

      @{_deep} .target-only {
        color: #d66418;
      }

      @{_deep} .modify {
        color: #f7863b;
      }
      @{_deep} .add {
        color: #0e8f0e;
      }

      @{_deep} .source-only {
        color: #b1b836;
      }
      @{_deep} .diff {
        color: #bc3c19;
      }
      @{_deep} .arrow {
        color: #2b84e2;
      }
      @{_deep} .label {
        color: #8390a1;
      }
      @{_deep} .card-wrapper {
        height: 120px;
        width: 120px;
        .g2-tooltip {
          padding: 0 !important;
          color: #ffffff !important;
          background: #114781 !important;
          box-shadow: none !important;
        }
      }
      @{_deep}.ivu-table-tip {
        height: 200px
      }
      @{_deep} .ivu-table-wrapper {
        min-height: 200px;
      }
    }
  }
}
</style>
