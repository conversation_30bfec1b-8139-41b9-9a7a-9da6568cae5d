<template>
  <ui-modal v-model="visible" title="设备编码格式检测" width="35%" @query="handleSubmit('extraParam')">
    <div class="ip-container">
      <Form ref="extraParam" :model="extraParam" :label-width="0">
        <p class="mb-md base-text-color">1、设备编码允许长度。</p>
        <FormItem
          v-for="(item, index) in extraParam.deviceIdLength"
          :key="'deviceIdLength' + index"
          class="flex-content"
          :rules="[
            {
              required: true,
              type: 'number',
              message: '设备编码长度不能为空',
              trigger: 'blur',
            },
          ]"
          :prop="'deviceIdLength.' + index"
        >
          <InputNumber
            class="w160"
            v-model="extraParam.deviceIdLength[index]"
            placeholder="请输入设备编码长度"
          ></InputNumber>
          <i @click="handleAdd('deviceIdLength')" class="icon-font icon-tree-add f-16 ml-sm color-primary"></i>
          <i
            v-if="index !== 0"
            @click="handleRemove('deviceIdLength', index)"
            class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
          ></i>
        </FormItem>
        <p class="mb-md base-text-color">2、设备编码11-13位允许数字。</p>
        <FormItem
          v-for="(item, index) in extraParam.specialStringList"
          :key="'specialStringList' + index"
          class="flex-content"
          :rules="[{ validator: validate, trigger: 'blur' }]"
          :prop="'specialStringList.' + index"
        >
          <Input class="w160" v-model="extraParam.specialStringList[index]" placeholder="请输入指定的3位数字"></Input>
          <i @click="handleAdd('specialStringList')" class="icon-font icon-tree-add f-16 ml-sm color-primary"></i>
          <i
            v-if="index !== 0"
            @click="handleRemove('specialStringList', index)"
            class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
          ></i>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'device-code',
  components: {},
  props: {},
  data() {
    return {
      validate: (rule, value, callback) => {
        if (value) {
          let reg = /^[0-9]*$/;
          let result = reg.test(value);
          if (value.length > 3) {
            callback(new Error('长度不大于3位数字'));
          } else {
            result ? callback() : callback(new Error('格式不正确'));
          }
        } else {
          callback();
        }
      },
      indexConfig: [],
      visible: false,
      extraParam: {
        deviceIdLength: [],
        specialStringList: [],
      },
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    async handleSubmit(name) {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let result = await this.$refs[name].validate();
        if (!result) return;
        // 去除空值
        let extraParam = {
          deviceIdLength: this.extraParam.deviceIdLength.filter(Boolean),
          specialStringList: this.extraParam.specialStringList.filter(Boolean),
        };
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify(extraParam),
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    handleAdd(name) {
      this.extraParam[name].push(null);
    },
    handleRemove(name, index) {
      if (this.extraParam[name].length < 2) {
        return this.$Message.error('至少保留一个检测规则');
      }
      this.extraParam[name].splice(index, 1);
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.handleReset('extraParam');
      this.getDeviceCodeList();
    },
    async getDeviceCodeList() {
      let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
      try {
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        //基础数据检测规则-空值检测，默认加上资产库的10个字段 2984
        this.extraParam = JSON.parse(data.extraParam || '{"deviceIdLength":[20],"specialStringList":[null]}');
        // 始终新增一个空值
        !this.extraParam.deviceIdLength.length &&
          (this.extraParam.deviceIdLength = [...this.extraParam.deviceIdLength, null]);
        !this.extraParam.specialStringList.length &&
          (this.extraParam.specialStringList = [...this.extraParam.specialStringList, null]);
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 0 50px 50px 50px !important;
}
.flex-content {
  margin-bottom: 24px;
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}
.ip-container {
  max-height: 600px;
  overflow-y: auto;
}
.color-primary {
  color: var(--color-primary);
}
.w160 {
  width: 160px;
}
</style>
