<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="前科类型:" prop="recordKind">
      <Select
        v-model="queryParam.bizLabels"
        placeholder="请选择"
        transfer
        multiple
        :max-tag-count="2"
      >
        <Option
          v-for="item in faceLibBizList"
          :key="item.id"
          :value="item.id.toString()"
          >&nbsp;&nbsp;{{ item.labelName }}</Option
        >
      </Select>
    </FormItem>
    <FormItem label="年龄:">
      <div class="flex-box">
        <Input
          v-model="queryParam.minAge"
          placeholder="请输入"
          style="width: 80px"
        ></Input>
        <div class="separtor"></div>
        <Input
          v-model="queryParam.maxAge"
          placeholder="请输入"
          style="width: 80px"
        ></Input>
      </div>
    </FormItem>
    <FormItem label="民族:" prop="nation">
      <Select
        v-model="queryParam.nation"
        placeholder="请选择"
        transfer
        style="width: 80px"
      >
        <Option
          :value="item.dataKey"
          v-for="item in nationTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { getBizTypes } from "@/api/data-warehouse.js";
import { max } from "d3";
export default {
  data() {
    return {
      queryParam: {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        bizLabels: [], // 前科类型 业务标签
        minAge: "",
        maxAge: "",
        nation: "",
      },
      faceLibBizList: [],
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
  },
  async created() {
    await this.getDictData();
    this.getBizLabelList();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 重置
     */
    reset() {
      this.queryParam = {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        bizLabels: [],
        minAge: "",
        maxAge: "",
        nation: "",
      };
    },
    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },

    async getBizLabelList() {
      const resp = await getBizTypes({ bizTypeIds: ["1"] });
      const labelArrs = resp?.data || [];
      this.faceLibBizList = labelArrs.flatMap((item) => item.labels);
    },
  },
};
</script>
<style lang="less" scoped>
.separtor {
  margin: 0 10px;
  width: 16px;
  height: 1px;
  background: rgba(0, 0, 0, 0.45);
}
.flex-box {
  display: flex;
  align-items: center;
}
.select-record-kind {
  /deep/.ivu-select-item {
    width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
