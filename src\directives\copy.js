export default function (Vue) {
  Vue.directive('copy', {
    bind: function (el) {
      el.handle = () => {
        el.$value = el.innerText;
        el.$rs = false;
        var tipComponent = Vue.extend({
          render() {
            return (
              <div>
                <ui-btn-tip
                  btnText={el.$value}
                  placement={'top'}
                  styles={{ 'cursor': 'pointer' }}
                  contentclass={['font-active-color']}
                >
                  <template slot={'content'}>
                    <div
                      {...{
                        style: [{ 'cursor': 'pointer', 'display': 'flex', 'align-items': 'center' }],
                        on: {
                          'click': (ev) => {
                            if (el.$rs) return false;
                            let res = copyfun(el.$value);
                            if (res) {
                              const styles =
                                'display: flex;align-items: center;justify-content: center;width: 20px;height: 20px;margin-right: 5px;background-color: #0e8f0e;border-radius: 50%;';
                              ev.target.innerHTML = `<span style=\"${styles}\"><i class=\"icon-font icon-gou\" style=\"margin-right: 5px; font-size: 14px;transform: scale(0.6) translateX(3px);\"></i></span>复制成功！`;
                            }
                            el.$rs = true;
                          },
                          'mouseleave': (ev) => {
                            el.$rs = false;
                            ev.target.innerHTML = `<i class="['icon-font','icon-fuzhi', 'font-active-color']" style="margin-right: 5px; font-size: 14px}"></i>复制`;
                          },
                        },
                      }}
                    >
                      <i
                        {...{
                          class: ['icon-font', 'icon-fuzhi', 'font-active-color'],
                          style: [{ 'margin-right': '5px', 'font-size': '14px' }],
                        }}
                      ></i>
                      复制
                    </div>
                  </template>
                </ui-btn-tip>
              </div>
            );
          },
        }); //生成组件的dom
        const component = new tipComponent().$mount();
        var DomContent = component.$el; //将vue结构转化成dom
        el.parentElement.replaceChild(DomContent, el);
      };
      el.addEventListener('mouseover', el.handle);
    },
    unbind: function (el) {
      el.removeEventListener('mouseleave', el.handel);
    },
  });
}
// 内容复制
function copyfun(text) {
  // 动态创建 textarea 标签
  const textarea = document.createElement('textarea');
  // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
  textarea.readOnly = 'readonly';
  textarea.style.position = 'absolute';
  textarea.style.left = '-9999px';
  // 将要 copy 的值赋给 textarea 标签的 value 属性
  textarea.value = text;
  // 将 textarea 插入到 body 中
  document.body.appendChild(textarea);
  // 选中值并复制
  textarea.select();
  const result = document.execCommand('Copy');
  return result;
}
