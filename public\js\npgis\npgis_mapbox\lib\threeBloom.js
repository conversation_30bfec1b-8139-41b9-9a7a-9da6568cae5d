(function() {
    var jsFiles = null;
    jsFiles = [
        "CopyShader.js",
        "EffectComposer.js",
        "FXAAShader.js",
        "LuminosityHighPassShader.js",
        "RenderPass.js",
        "ShaderPass.js",
        "UnrealBloomPass.js"
    ];
    var scriptName = "threeBloom.js";
    var r = new RegExp("(^|(.*?\\/))(" + scriptName + ")(\\?|$)"),
        s = document.getElementsByTagName('script'),
        src, m, host = "";
    for (var i = 0, len = s.length; i < len; i++) {
        src = s[i].getAttribute('src');
        if (src) {
            m = src.match(r);
            if (m) {
                host = m[1];
                break;
            }
        }
    }
    if (!jsFiles || jsFiles.length <= 0)
        return;
    var scriptTags = new Array(jsFiles.length);
    for (var i = 0, len = jsFiles.length; i < len; i++) {
        scriptTags[i] = "<script type='text/javascript' src='" + host + "/threeBloom/" + jsFiles[i] + "'></script>";
    }
    if (scriptTags.length > 0) {
        document.write(scriptTags.join(""));
    }
})();