/**
 * 此mixin针对echarts x轴数据量过多时无法直接展示所有数据
 */
const mixin = {
  data() {
    return {
      viewEchartNumber: 8,
      viewEchartIndex: 0,
    };
  },
  methods: {
    /**
     *
     * @param {滑动的echarts} echartRef
     * @param {滑动的echarts中series对应的data数组} echartList
     * @param {滑动的echarts中 x轴的每一项中所包含的数量， viewNum会无效} xAxisData
     * @param {每次滑动的数量} viewNum
     */
    scrollRight(echartRef, echartList, xAxisData, viewNum) {
      if (echartList.length < this.viewEchartNumber) {
        this.$Message.warning('没有可滑动展示的数据');
        return false;
      }
      const num = Math.ceil(echartList.length / this.viewEchartNumber) - 1;
      if (this.viewEchartIndex === num) {
        this.viewEchartIndex = 0;
      } else {
        this.viewEchartIndex++;
      }
      this.setDataZoom(echartRef, xAxisData, viewNum);
    },
    /**
     *
     * @param {需要操作echarts} echartRef
     * @param {x坐标轴每项数据所包含的数组，如果传入该数组则根据每项数据中所包含的数量计算每次滑动展示的数据数量， viewNum会无效} xAxisData
     * @param {每次滑动可以看到的数量} viewNum
     */
    setDataZoom(echartRef, xAxisData = [], viewNum) {
      const xAxisDataLen = xAxisData.length || 0;
      if (xAxisDataLen === 0) {
        this.viewEchartNumber = viewNum;
      } else if (xAxisDataLen <= 15) {
        this.viewEchartNumber = 8;
      } else if (xAxisDataLen <= 20) {
        this.viewEchartNumber = 5;
      } else {
        this.viewEchartNumber = 3;
      }
      const zoomStart = this.viewEchartIndex * this.viewEchartNumber;
      // 注意： 配置每页显示条数  可能  大于 数据接口返回条数，因此需要 特殊处理下
      let seriesDataLen = this.$refs[echartRef]?.echartOption?.series?.[0]?.data?.length || 0;
      let endNum = (this.viewEchartIndex + 1) * this.viewEchartNumber - 1;
      const zoomEnd = seriesDataLen - 1 > endNum ? endNum : seriesDataLen - 1;
      this.$refs[echartRef].setOption({
        dataZoom: [
          {
            type: 'slider',
            show: false,
            startValue: zoomStart,
            endValue: zoomEnd, //默认显示条柱数
          },
        ],
      });
    },
  },
  computed: {},
};
export default mixin;
