<template>
  <!-- 下级地市情况 -->
  <div class="subordinate-chart">
    <div class="ranking-title f-14">
      <span class="icon-font icon-xiajidishiqingkuang mr-xs"></span>
      <span>下级{{ getDataType() }}情况</span>
      <slot name="sort" :sort="sort">
        <Checkbox
          v-model="sort.sortField"
          :true-value="'RESULT_VALUE'"
          :false-value="null"
          class="fr"
          @on-change="onChangeSortField"
        >
          <slot name="rank-title">按准确率排序</slot>
        </Checkbox>
        <Checkbox v-if="getShowHideUndetected" v-model="noApiSort.sortField" class="fr" @on-change="onChangeSortField">
          {{ getCheckLabel }}
        </Checkbox>
      </slot>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: rankInfoLoading, tableData: rankInfoList }">
      <draw-echarts
        v-if="rankInfoList.length"
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <span class="next-echart" v-if="rankInfoList.length > 20">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('propertyChart', rankInfoList, [], 20)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import dataZoom from '@/mixins/data-zoom';
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
import { HIDE_UNDETECTED_VALUE } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AddHideUndetectedBox.js';

export default {
  name: 'subordinate-chart',
  mixins: [dataZoom, dealWatch],
  data() {
    return {
      propertyEchart: {},
      echartsLoading: false,
      echartData: [[], [], []],
      colorList: ['rgba(213, 94, 41, 1)', 'rgba(22, 174, 22, 1)', 'rgba(255,174,69,1)'],
      echartList: [],
      xAxis: [],
      series: [],
      sort: {
        sortField: undefined,
      },
      rankInfoLoading: false,
      rankInfoList: [],
      // 前端过滤 字段
      noApiSort: {
        sortField: undefined,
      },
    };
  },
  props: {
    customParameters: {},
    activeIndexItem: {},
    tooltipFormatter: {
      type: Function,
      default: (params) => {
        let { name } = params[0];
        let formatter = `<span class="icon-font icon-dingwei1 f-14 vt-middle mr-xs f-14" style="background: var(--color-primary);-webkit-background-clip: text;-webkit-text-fill-color: transparent;"></span>${name}<br>`;
        params.forEach((item) => {
          let icon = `<span style="display: inline-block;width: 8px;height: 8px; margin-left: 20px; background-color: ${item.color};color: ${item.color}" class="vt-middle mr-xs"></span>`;
          formatter += `${icon}<span class="f-12">${item.seriesName}  ${item.value || 0}${'%'}<br></span>`;
        });
        return formatter;
      },
    },
    lengName: {
      default: () => {
        return [
          {
            name: '治理前',
            key: 'beforeRate',
            color: 'rgba(213, 94, 41, 1)',
          },
          {
            name: '治理后',
            key: 'afterRate',
            color: 'rgba(22, 174, 22, 1)',
          },
        ];
      },
    },
  },
  created() {
    if (this.getShowHideUndetected) {
      this.noApiSort.sortField = true;
    }
  },
  async mounted() {
    this.startWatch(
      '$route.query',
      async () => {
        await this.getNumRankInfo();
        this.typeRing();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    async onChangeSortField() {
      await this.getNumRankInfo();
      this.typeRing();
    },
    async getNumRankInfo() {
      this.rankInfoLoading = true;
      const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
      let data = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        customParameters: this.customParameters,
        ...this.sort,
      };
      try {
        let res = await this.$http.post(governanceevaluation.getNumRankInfo, data);

        let arr = res.data.data || [];
        if (this.noApiSort.sortField) {
          arr = arr.filter((item) => {
            return item.actualNum;
          });
        }
        this.rankInfoList = arr;
      } catch (err) {
        console.log(err);
      } finally {
        this.rankInfoLoading = false;
      }
    },
    typeRing() {
      this.echartData = [];
      let series = [];
      this.lengName.forEach((items) => {
        series.push({
          name: items.name,
          data: this.rankInfoList.map((list) => {
            return (list.detail && list.detail[items.key]) || 0;
          }),
          barWidth: '22px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          itemStyle: { color: items.color },
        });
      });
      let line = [
        {
          name: this.activeIndexItem.indexName,
          type: 'line',
          itemStyle: {
            color: '#B8F162',
          },
          symbol: 'circle',
          yAxisIndex: 0,
          data: this.rankInfoList.map((item) => item.resultValue),
        },
      ];
      let opts = {
        xAxis: this.rankInfoList.map((item) => item.regionName),
        series: [...series, ...line],
        lengName: [...this.lengName.map((item) => item.name), this.activeIndexItem.indexName],
        tooltipFormatter: this.tooltipFormatter,
      };
      this.propertyEchart = this.$util.doEcharts.VideoReadPromotionRateSubordinateChart(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], 20);
      });
    },
    getDataType() {
      /**
       *  NONE("-1","默认值"),
       *     PROVINCE("1","省"),
       *     CITY("2","市"),
       *     COUNTY("3","区县");
       */
      if (this.rankInfoList.length > 0) {
        let { dataType } = this.rankInfoList[0];
        switch (dataType) {
          case 'PROVINCE':
          case 'CITY':
            return '地市';
          case 'COUNTY':
            return '区县';
          default:
            return '--';
        }
      }
    },
  },
  computed: {
    getCheckLabel() {
      const queryParams = this.$route.query;
      return queryParams.statisticType === 'REGION' ? '隐藏未检测区划' : '隐藏未检测单位';
    },
    getShowHideUndetected() {
      const queryParams = this.$route.query;
      return HIDE_UNDETECTED_VALUE.includes(queryParams.indexType);
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.subordinate-chart {
  position: relative;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
</style>
