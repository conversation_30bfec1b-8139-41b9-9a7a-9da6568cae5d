import store from "@/store";
import router from "@/router";
import { LoadingBar, Notice } from "view-design";
import { getAsyncRoutes } from "@/router/asyncRouter";
const loginRoutePath = "/login";
/**
 * @param {Array} value
 * @returns {Boolean}
 * @example see @/views/permission/directive.vue
 */
export function checkPermission(value) {
  if (value && value instanceof Array && value.length > 0) {
    const roles = store.getters && store.getters.btnPermission;
    const permissionRoles = value;
    try {
      const hasPermission = roles.some((role) => {
        return permissionRoles.includes(role);
      });
      if (!hasPermission) {
        return false;
      }
    } catch (err) {
      console.error(err);
    }
    return true;
  } else {
    console.error(`need roles! Like v-if="checkPermission(['a','b'])"`);
    return false;
  }
}

export function getUserInfo(to, from, next) {
  store
    .dispatch("getUserInfo")
    .then((sysApplication) => {
      if (sysApplication && sysApplication.children.length > 0) {
        // generate dynamic router
        store.dispatch("GenerateRoutes", sysApplication).then((data) => {
          // 动态添加可访问路由表\
          let a = getAsyncRoutes(store.getters.addRouters);
          a.forEach((route) => router.addRoute(route));
          // 配置 / 路由的定向地址
          router.addRoute({
            path: "/",
            name: a[0].name,
            redirect: a[0].redirect || a[0].path,
            hidden: true,
          });
          if (to === "from-login") {
            const redirect = decodeURIComponent(from.query.redirect);
            if (redirect) {
              let url = data.find((v) => {
                return "/" + v.redirect === redirect;
              });
              if (url) {
                router.push("/" + url.redirect);
              } else {
                router.push(
                  {
                    name: store.getters.addRouters[0].name,
                  },
                  () => {}
                );
                // router.push('/home')
              }
            } else {
              // router.push('/' + data[0].redirect)
              // router.push('/home')
              router.push(
                {
                  name: store.getters.addRouters[0].name,
                },
                () => {}
              );
            }
          } else if (to === "information") {
            router.push(
              {
                path: "/user/information",
                query: {
                  firstTime: true,
                },
              },
              () => {}
            );
          } else {
            //请求带有 redirect 重定向时，登录自动重定向到该地址
            const redirect = decodeURIComponent(from.query.redirect || to.path);
            if (to.path === redirect) {
              // set the replace: true so the navigation will not leave a history record
              next({ ...to, replace: true });
            } else {
              // 跳转到目的路由
              next({ path: redirect });
            }
          }
        });
      } else {
        Notice.warning({
          title: "友情提示",
          desc: "该用户未分配当前应用资源,请联系管理员分配！",
        });
        store.dispatch("handleLogOut").then(() => {
          router.replace({
            name: "Login",
            // query: {
            //     redirect: router.currentRoute.name
            // } // 登录成功后跳入浏览的当前页面
          });
          // next( { path: loginRoutePath } )
        });
      }
    })
    .catch((err) => {
      Notice.warning({
        title: "友情提示",
        desc: "请求用户信息失败，请登录重试",
      });

      /*  // 失败时，获取用户信息失败时，调用登出，来清空历史保留信息
      store.dispatch('handleLogOut').then(() => {
        LoadingBar.finish()
        next({ path: loginRoutePath })
      })*/
    });
}

export default {
  checkPermission,
  getUserInfo,
};
