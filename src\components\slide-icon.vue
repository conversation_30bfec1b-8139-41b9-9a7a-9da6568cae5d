<template>
  <div
    class="hide-btn pointer"
    :class="{ 'hidden': hidden }"
    v-slide="{ width: width, direction: direction, callBack: func }"
  >
    <div class="login-wrapper-triangle triangle-left-top"></div>
    <div class="box2 f-18">
      <Icon type="ios-arrow-forward" />
    </div>
    <div class="login-wrapper-triangle2 triangle-left-top2"></div>
  </div>
</template>

<script>
export default {
  props: {
    width: {
      default: 0,
    },
    direction: {
      default: 'right',
    },
    // 第一次先隐藏
    hidden: {
      default: true,
    },
  },
  data() {
    return {};
  },

  components: {},

  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    func(val) {
      this.$emit('isHidden', val);
    },
    /* ----------------------------------------- 自定义方法 ----------------------------------------- */
  },
};
</script>
<style lang="less" scoped>
.hide-btn {
  position: absolute;
  width: 16px;
  height: 57px;
  line-height: 57px;
  text-align: center;
  color: #71aeef;
  background: #0c2a57;
  left: 0px;
  top: 50%;
  transform: translateY(-50%);
  border-radius: 0 4px 4px 0;
  box-shadow: 2px 0 10px var(--bg-info-card-option);
  .box2 {
    transform: rotateZ(180deg);
  }
  &.hidden {
    .box2 {
      transform: rotateZ(0);
    }
  }
}
</style>
