<template>
  <Modal v-model="visible" title="数据预览" :transfer="false" :width="width" :styles="stylesObj">
    <List size="large" :split="false">
      <ListItem v-for="(item, index) in data" :key="index">
        <p v-for="(fieldKey, key) in fields" :key="key" style="display:inline">
          <b> {{ fieldKey }}:</b>{{ item[fieldKey] }}&nbsp;&nbsp;
        </p>
        <hr>
      </ListItem>
    </List>
    <div slot="footer"></div>
  </Modal>
</template>

<script>
import { dataPreview } from '@/api/dataGovernance'
export default {
  props: {},
  data () {
    return {
      visible: false,
      width: '1200',
      title: '数据预览',
      data: [],
      fields: []
    }
  },
  methods: {
    show (resourceId) {
      this.fields = []
      dataPreview(resourceId).then(res => {
        this.data = res.data
        res.data.forEach(item => {
          Object.keys(item).forEach(key => {
            if (!this.fields.includes(key)) {
              this.fields.push(key)
            }
          })
        })
        this.visible = true
      })
    },
    stylesObj () {
      let stylesObj = {}
      if (this.rWidth) {
        const rWidth = parseFloat(this.rWidth)
        if (isNaN(rWidth)) {
          stylesObj.width = '5rem'
        } else {
          stylesObj.width = parseFloat(this.rWidth) / 192 + 'rem'
        }
      } else {
        stylesObj = this.styles
      }
      return stylesObj
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-modal-body {
  .ivu-list-item {
    display: inline;
    padding: 5px 0;
  }
}
</style>