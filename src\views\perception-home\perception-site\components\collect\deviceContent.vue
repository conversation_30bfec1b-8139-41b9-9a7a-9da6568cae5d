/**
* wifi设备, RFID设备,电围设备
 */
<template>
  <div class="list-card box-2">
    <div class="content">
      <div class="top-info">
        <p class="identifier-content">
          <img
            v-show="deviceType == 2"
            class="img-icon"
            src="@/assets/img/icons/icon-wifi.png"
            alt=""
          />
          <img
            v-show="deviceType == 3"
            class="img-icon"
            src="@/assets/img/icons/icon-RFID.png"
            alt=""
          />
          <img
            v-show="deviceType == 4"
            class="img-icon"
            src="@/assets/img/icons/icon-electric.png"
            alt=""
          />
          <Tooltip
            class="device-code"
            :content="data.deviceId"
            placement="top"
            transfer
          >
            <span class="ellipsis">{{ data.deviceId }}</span>
          </Tooltip>
        </p>
        <div class="collection">
          <!-- <div class="bg"></div> -->
          <ui-btn-tip
            class="collection-icon"
            v-if="data.myFavorite == '1'"
            content="取消收藏"
            icon="icon-yishoucang"
            transfer
            @click.native="collection(data, 2)"
          />
          <ui-btn-tip
            class="collection-icon"
            v-else
            content="收藏"
            icon="icon-shoucang"
            transfer
            @click.native="collection(data, 1)"
          />
        </div>
      </div>

      <div class="bottom-info">
        <time>
          <Tooltip content="设备名称" placement="right" transfer theme="light">
            <i class="iconfont icon-leixing"></i>
          </Tooltip>
          {{ data.deviceName }}
        </time>
        <p>
          <Tooltip content="位置" placement="right" transfer theme="light">
            <i class="iconfont icon-location" title="123"></i>
          </Tooltip>
          {{ data.detailAddress }}
        </p>
      </div>
      <!-- <div class="operate-bar">
                <p class="operate-content">
                    <ui-btn-tip content="分析" icon="icon-fenxi"/>
                    <ui-btn-tip content="布控" icon="icon-dunpai" transfer/>
                </p>
            </div> -->
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    deviceType: {
      type: [String, Number],
      default: 1,
    },
  },
  data() {
    return {
      swiperWidth: "50%",
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    collection(data, flag) {
      var val = 7;
      if (this.deviceType == 3) val = 8;
      if (this.deviceType == 4) val = 9;

      var param = {
        favoriteObjectId: data.deviceId,
        favoriteObjectType: val,
      };
      this.$emit("collection", param, flag);
    },
  },
};
</script>

<style lang='less' scoped>
// @swiper-width: e(`window.swiperWidth`);
@import "style/index";
/deep/ .swiper-slide {
  width: 25% !important;
  height: 180px;
}
.top-info {
  display: flex;
  .identifier-content {
    flex: 1;
    .device-code {
      flex: 1;
      width: 0;
      /deep/ .ivu-tooltip-rel {
        width: 100%;
      }
    }
  }
}
</style>
