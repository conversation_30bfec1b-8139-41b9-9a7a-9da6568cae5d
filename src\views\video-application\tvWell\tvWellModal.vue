<template>
    <div class="tv-well-dialog" v-if="isShow" :style="{top: `${offsetTop}px`, left: `${offsetLeft}px`, width: `calc(100% - ${offsetLeft}px)`, height: `calc(100% - ${offsetTop}px)`}">
        <header class="tv-well-header">
            <div style="display: flex;">
                <div class="tv-well-header-back" @click="close">
                    <i class="iconfont icon-return"></i>
                </div>
                <div class="tv-well-header-info">
                    <div class="tv-well-header-text">电视墙</div>
                </div>
            </div>
        </header>
        <div class="content">
          <tv-well ref="tvWell" :showPlan="false" @changeFocus="changeFocus"></tv-well>  
        </div>
        <!-- <div class="footer">
            <div class="footer-btn btn-tog">确定</div>
            <div class="footer-btn btn-sta">取消</div>
        </div> -->
    </div>
</template>

<script>
import tvWell from './components/tv-well.vue'
export default {
    name: "tvWellModal",
    components: {
      tvWell
    },
    data(){
        return{
          isShow: false,
          offsetTop: 0,
          offsetLeft: 0,
          currentDevice: null
        }
    },
    methods: {
        open(deviceInfo){
          this.setStyle()
          this.isShow = true
          this.currentDevice = deviceInfo
        },
        close() {
            this.isShow = false
        },
        setStyle() {
          let mainDom = document.querySelector('.i-layout-content-main')
          this.offsetTop = mainDom.offsetTop + (mainDom.clientHeight - mainDom.childNodes[0].clientHeight)/2
          this.offsetLeft = mainDom.offsetLeft + (mainDom.clientWidth - mainDom.childNodes[0].clientWidth)/2
        },
        changeFocus(index) {
          if (!this.currentDevice) return
          this.$refs.tvWell.handleNodeClick({...this.currentDevice, playType: 'live'}, index)
          this.currentDevice = null
        }
    },
    activated() {
      
    },
    mounted () {
      
    }
};
</script>

<style lang="less">
@blueColor: #2c86f8;
.epl(@width){
    width: @width;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.tv-well-dialog{
    font-size: 14px;
    width: 100%;
    height: 100%;
    background: white;
    position: fixed;
    border-radius: 5px;
    z-index: 9999;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
        flex: 1;
        padding: 10px;
        overflow: hidden;
    }
    .tv-well-header{
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 40px;
        border-bottom: 1px solid #d3d7de;
        background: #ffffff;
        box-sizing: border-box;
        user-select: none;
        position: relative;
        .tv-well-header-back{
            cursor: pointer;
            width: 70px;
            height: 100%;
            background-color: #2C86F8;
            color: white;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        .tv-well-header-info{
            display: flex;
            align-items: center;
            margin-left: 20px;
        }
    }
    .footer{
        width: 100%;
        display: flex;
        justify-content: center;
        background: #ffffff;
        padding-bottom: 10px;
        user-select: none;
        height: 50px;
        padding-top: 5px;
        .footer-btn{
            width: 80px;
            height: 30px;
            box-sizing: border-box;
            text-align: center;
            line-height: 30px;
            border-radius: 3px;
            margin-right: 20px;
            cursor: pointer;
        }
        .btn-tog{
            background: @blueColor;
            color: #fff;
        }
        .btn-sta{
            border: 1px solid @blueColor;
            color: @blueColor;
            background: #ffffff;
        }
    }
}
</style>
