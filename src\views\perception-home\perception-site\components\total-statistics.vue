<template>
  <div class="statistics-ul">
    <div v-for="(item, $index) in totalStatisticsList" :key="$index" class="statistics-item">
      <img :src="item.img" class="statistics-item-icon" alt="" />
      <div class="statistics-item-con">
        <count-to
          :start-val="0"
          :end-val="item.value || 0"
          :duration="3000"
          class="statistics-item-num"
        ></count-to>
        <div class="statistics-item-name">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<script>
  import CountTo from 'vue-count-to'
  export default {
    components: {
      CountTo
    },
    props: {
      statisticsList: {
        type: Object,
        default: () => {}
      }
    },
    data () {
      return {
        totalStatisticsList: [
          {
            img: require('@/assets/img/home/<USER>'),
            name: '标签总量',
            value: 0
          },
          {
            img: require('@/assets/img/home/<USER>'),
            name: '数据总量',
            value: 0
          },
          {
            img: require('@/assets/img/home/<USER>'),
            name: '任务总量',
            value: 0
          },
          {
            img: require('@/assets/img/home/<USER>'),
            name: '模型总量',
            value: 0
          }
        ]
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.totalStatisticsList[0].value = val.labelTotal | 0
          this.totalStatisticsList[1].value = val.objectTotal | 0
          this.totalStatisticsList[2].value = val.taskTotal | 0
          this.totalStatisticsList[3].value = val.modelTotal | 0
        },
        immediate: true
      }
    }
  }
</script>
<style lang="less" scoped>
  .statistics-ul {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    .statistics-item {
      width: 220px;
      height: 80px;
      display: flex;
      align-items: center;
      background: rgba(7, 27, 57, 0.7);
      position: relative;
      .statistics-item-icon {
        width: 36px;
        height: 36px;
        margin: 0 20px;
      }
      .statistics-item-con {
        .statistics-item-num {
          font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
          font-size: 20px;
          font-weight: bold;
          line-height: 26px;
          color: #E99E53;
        }
        .statistics-item-name {
          font-size: 16px;
          color: #fff;
          line-height: 22px;
          margin-top: 2px;
        }
      }
    }
    .statistics-item::before {
      content: '';
      width: 220px;
      height: 20px;
      background: url('../../../assets/img/home/<USER>') no-repeat center/cover;
      position: absolute;
      top: -10px;
      left: 0;
      animation: flash 2s infinite linear;
    }
    @keyframes flash {
      0% {
        opacity: 0;
      }
      50% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }
  }
</style>