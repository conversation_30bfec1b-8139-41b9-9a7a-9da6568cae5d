<template>
  <div class="map-box">
    <div :id="mapId" class="map"></div>
  </div>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
let mapMain = null;
export default {
  props: {
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      overlayers: {},
    };
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      globalObj: "systemParam/globalObj",
    }),
  },
  async mounted() {
    await this.getMapConfig();
  },

  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        // this._initMap(this.mapConfig, this.mapStyle);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      // 配置初始化层级
      mapMain = new NPGisMapMain();
      const mapId = this.mapId;
      mapMain.init(mapId, data, style);
      this.mapGeometry = mapMain.map._mapGeometry; // 获取地图依赖类
      this.map = mapMain.map;
      this.configDefaultMap();
      this.$emit("inited");
      mapMain.map.addEventListener(NPMapLib.MAP_EVENT_CLICK, (point) => {
        console.warn(
          "您左键单击了地图！点击的经纬度为：" + point.lon + "," + point.lat
        );
        this.$emit("mouseClickPoint", point);
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    /**
     * 添加标注
     *
     */
    addMarkers(overlayerName, markerData, options, toCenter, isOnlyHover) {
      options = options || {};
      var overlayer = this.getOverlayer(overlayerName);
      var map = this.map;
      var points = [];
      var markers = [];
      var { removeOverlays = true } = options;
      removeOverlays && this.removeAllMarkers(overlayerName);
      markerData = markerData.filter((item, index) => {
        if (!item.longitude || !item.latitude) {
          return false;
        }
        item.index = index;
        return true;
      });
      if (!markerData.length) {
        this.$Message.warning("暂无经纬度信息");
        return;
      }
      markerData.forEach((item, index) => {
        var point = new NPMapLib.Geometry.Point(item.longitude, item.latitude);
        points.push(point);
        var marker = new NPMapLib.Symbols.Marker(point);
        marker.setData(item);
        let size = new NPMapLib.Geometry.Size(29, 29);
        let imgUrl = require(`@/assets/img/map/red-locate-icon.png`);
        let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        markers.push(marker);
      });
      overlayer.addOverlays(markers);
      if (toCenter !== false) {
        setTimeout(() => {
          // points.length == 1 || $map.isnpgis() $map是啥不知道也没有定义，会报错
          if (points.length == 1) {
            map.centerAndZoom(
              points[0],
              options.centerZoom || map.getMaxZoom() - 3
            );
          } else if (points.length > 1) {
            this.flyByPoints(points);
          }
        }, 0);
      }
      return markers;
    },
    /**
     * 移除图层上的所有覆盖物
     */
    removeAllMarkers(layerName) {
      var layer = this.getOverlayer(layerName);
      layer.removeAllOverlays();
    },
    //获取图层
    getOverlayer(overlayerName, zIndex) {
      if (!overlayerName) {
        return this.map.getDefaultLayer();
      }
      let overlayer = this.map && this.map.getLayerByName(overlayerName);
      if (!overlayer) {
        overlayer = new NPMapLib.Layers.OverlayLayer(overlayerName);
        this.map.addLayer(overlayer);
        // overlayer.setZIndex(zIndex || 600);
        this.overlayers[overlayerName] = overlayer;
      }
      return overlayer;
    },
    //计算最大最小经纬度
    getZoomExtent(lon, lat, nowExtent) {
      if (lon < nowExtent.minLon) {
        nowExtent.minLon = lon;
      }
      if (lon > nowExtent.maxLon) {
        nowExtent.maxLon = lon;
      }
      if (lat < nowExtent.minLat) {
        nowExtent.minLat = lat;
      }
      if (lat > nowExtent.maxLat) {
        nowExtent.maxLat = lat;
      }
      return nowExtent;
    },
    /**
     * 根据多个点位定位地图视窗
     */
    flyByPoints(points) {
      let zoomExtent = {
        minLon: points[0].lon,
        maxLon: points[0].lon,
        minLat: points[0].lat,
        maxLat: points[0].lat,
      };
      for (let i = 0; i < points.length; i++) {
        const p = points[i];
        if (
          this.map
            .getRestrictedExtent()
            .containsPoint({ lat: p.lat, lon: p.lon })
        ) {
          zoomExtent = this.getZoomExtent(p.lon, p.lat, zoomExtent);
        }
      }
      var ext = new NPMap.Geometry.Extent(
        zoomExtent.minLon,
        zoomExtent.minLat,
        zoomExtent.maxLon,
        zoomExtent.maxLat
      );
      this.map.setZoom(this.map.getZoom() - 1);
      this.map.zoomToExtent(ext, false, true);
    },
  },
};
</script>

<style lang="less" scoped>
.map-box {
  background-color: #000;
  width: 100%;
  // padding: 0 18px;
  margin-bottom: 18px;
  height: 100%;
  overflow: hidden;
  position: relative;
  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }
}
</style>
