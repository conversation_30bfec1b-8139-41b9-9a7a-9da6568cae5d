<template>
  <div class="governinfo">
    <Form ref="formValidateRef" class="formInfo" :rules="ruleValidate" :model="formValidate">
      <FormItem label="治理方式：" class="source-type" prop="governType">
        <div class="pl-10" v-for="(item, index) in formValidate.propertyFormList" :key="index">
          <div class="pl-8 mb-xs">
            <Select transfer placeholder="请选择" clearable class="width-sm" v-model="item.governanceWay">
              <Option v-for="(item, index) in anto_governance_way" :key="index" :value="item.dataKey">{{
                item.dataValue
              }}</Option>
            </Select>
            <span class="mr-sm base-text-color"> 中包含</span>
            <Input class="width-sm mr-xs" v-model="item.keyWord" placeholder="请输入关键词"></Input>
            <span class="mr-sm base-text-color"> {{ `，${global.filedEnum.sbdwlx}设置为` }}</span>
            <Select transfer placeholder="请选择" clearable class="mr-sm width-sm" v-model="item.sbdwlx">
              <Option v-for="(item, index) in propertySearch_sbdwlx" :key="index" :value="item.dataKey">{{
                item.dataValue
              }}</Option>
            </Select>
            <span class="">
              <span class="addition ml-sm" v-if="formValidate.propertyFormList.length - 1 === index" @click="toAdd"
                ><i class="icon-font icon-tree-add f-16 font-other" title="添加"></i
              ></span>
              <span class="cancel ml-sm" v-if="index != 0" @click="toDel(index)"
                ><i class="icon-font icon-shanchu1 f-14 font-other" title="删除"></i></span
            ></span>
          </div>
        </div>
      </FormItem>
      <FormItem label="更新策略：" prop="updateStragty" class="mt-lg">
        <FormItem class="inline">
          <div class="synchro-item mb-15 inline">
            <span>{{ `1、${global.filedEnum.sbdwlx}原始值为空时，自动填充：` }}</span>
            <RadioGroup v-model="formValidate.nullValueSyncStrategy" class="align-flex">
              <Radio label="1">是</Radio>
              <Radio label="2">否</Radio>
            </RadioGroup>
          </div>
        </FormItem>
        <FormItem label="" class="inline pl-9">
          <div class="synchro-item mb-15 inline">
            <span>{{ `2、${global.filedEnum.sbdwlx}原始值为空不为空时：` }}</span>
            <RadioGroup v-model="formValidate.syncStrategy" class="align-flex">
              <Radio label="1">覆盖原始值</Radio>
              <Radio label="2">保留原始值</Radio>
            </RadioGroup>
          </div>
        </FormItem>
      </FormItem>
    </Form>
  </div>
</template>
<script>
import user from '@/config/api/user';
export default {
  props: {
    editInfos: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const governTypeValidator = (rule, value, callback) => {
      let passFlag = true;
      this.formValidate.propertyFormList.forEach((item) => {
        let hasNo = Object.values(item).findIndex((one) => {
          return !one;
        });
        hasNo !== -1 ? (passFlag = false) : null;
      });
      passFlag ? callback() : callback(new Error('请填写详细的治理方式'));
    };
    const updateStragtyValidator = (rule, value, callback) => {
      if (!this.formValidate.nullValueSyncStrategy || !this.formValidate.syncStrategy) {
        callback(new Error('请选择更新策略'));
      } else {
        callback();
      }
    };
    return {
      formValidate: {
        nullValueSyncStrategy: '1', // 字段为空是否自动填充 1-是 2-否
        syncStrategy: '1', // 1-覆盖原始值 2-保留原始值
        propertyFormList: [{ governanceWay: '', sbdwlx: '', keyWord: '' }], //
      },
      propertyFields: [
        // {label: '摄像机功能类型', value: 'sbgnlx'},
        { label: '行政区划', value: 'civilCode' },
        { label: '设备厂商名称', value: 'manufacturer' },
        { label: '设备规格型号', value: 'model' },
        { label: 'IPV4地址', value: 'ipAddr' },
        { label: '经度', value: 'longitude' },
        { label: '纬度', value: 'latitude' },
        { label: '设备是否在线状态', value: 'isOnline' },
      ],
      connectFields: [
        { label: `${this.global.filedEnum.macAddr}`, value: 'macAddr' },
        { label: '设备规格型号', value: 'model' },
        { label: '设备软件版本', value: 'softVersion' },
        { label: '视频主码流编码格式', value: 'videoCodingM' },
        { label: '视频子码流编码格式', value: 'videoCodings' },
        { label: '摄像机支持的分辨率', value: 'resolution' },
      ],
      modelDisabled: true,
      onlineDisabled: true,
      gbObtain: false,
      sdkObtain: false,
      ruleValidate: {
        updateStragty: [
          {
            required: true,
            trigger: 'change',
            validator: updateStragtyValidator,
          },
        ],
        governType: [
          {
            required: true,
            validator: governTypeValidator,
            trigger: 'change',
          },
        ],
      },
      propertySearch_sbdwlx: [],
      anto_governance_way: [],
    };
  },
  created() {
    this.getDictDatas();
    if (!!this.editInfos.propertyJson && this.editInfos.governanceContent === '5') {
      const propertyJson = JSON.parse(this.editInfos.propertyJson);
      this.formValidate.syncStrategy = propertyJson.syncStrategy || '2';
      this.formValidate.nullValueSyncStrategy = propertyJson.nullValueSyncStrategy || '1';
      this.formValidate.propertyFormList = propertyJson.propertyFormList;
    }
  },
  updated() {
    let data = JSON.parse(JSON.stringify(this.formValidate));
    data.propertyJson = JSON.stringify(this.formValidate);
    this.$emit('getInfos', data);
  },
  methods: {
    // // 获取字典数据
    // async getDictData(dictType) {
    //   try {
    //     const params = {
    //       typekey: 'propertySearch_sbdwlx',
    //     }
    //     let res = await this.$http.get(user.queryByTypeKey, { params })
    //     this.pointype = res.data.data
    //     console.log(res.data.data)
    //   } catch (error) {
    //     console.log(error)
    //   }
    // },
    async getDictDatas() {
      try {
        const params = ['propertySearch_sbdwlx', 'anto_governance_way'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    toAdd() {
      this.formValidate.propertyFormList.push({
        governanceWay: '',
        sbdwlx: '',
        keyWord: '',
      });
    },
    toDel(index) {
      this.formValidate.propertyFormList.splice(index, 1);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .remark {
    color: #e44f22 !important;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .remark {
    color: var(--color-tips) !important;
  }
}
.align-flex {
  display: flex;
  align-items: center;
}
.flex-3 {
  width: 30%;
}
.flex-4 {
  width: 23%;
}
.radio-flex3 {
  width: calc(calc(100% - 210px) / 3);
}
.mb-15 {
  margin-bottom: 15px;
}
.wrap {
  flex-wrap: wrap;
}
.governinfo {
  width: 100%;
  padding: 20px 8px;
  display: flex;
  font-size: 14px;
  background: var(--bg-sub-content);
  &-title {
    color: var(--color-active);
  }
  .checkdesc {
    margin-right: 0 !important;
  }
  .synchro-item {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--color-content);
  }
  p {
    // width: 100%;
    font-size: 14px;
    color: #0caaaf;
    span {
      display: inline-block;
    }
  }
  .synchro {
    &-header {
      display: flex;
      height: 30px;
      margin: 10px 0;
      &-bar {
        width: 8px;
        margin-right: 6px;
        background: #239df9;
      }
      &-title {
        .align-flex;
        flex: 1;
        padding-left: 10px;
        font-size: 14px;
        font-weight: bold;
        color: #ffffff;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
      }
    }
  }
  @{_deep}.ivu-radio-group {
    padding: 0;
  }
}
@{_deep} .ivu {
  &-radio-wrapper {
    margin-right: 50px;
  }
  &-checkbox {
    margin-right: 10px;
    &-group {
      padding: 0 23px;
      &-item {
        display: flex !important;
        .mb-15;
      }
    }
  }
  &-radio {
    margin-right: 10px;
    &-group {
      padding: 0 23px;
    }
  }
  &-form-item {
    margin-bottom: 5px !important;
  }
}
.formInfo {
  @{_deep} .ivu-form-item {
    margin-bottom: 0 !important;
  }
}
.source-type {
  margin-bottom: 20px !important;
  @{_deep}.ivu-form-item-content {
    margin-left: 85px;
    max-height: 180px;
    overflow-y: auto;
  }
}
.item-padding {
  padding: 0 23px !important;
}
.pl-9 {
  padding-left: 92px;
}
.pl-8 {
  padding-left: 8px;
}
@{_deep} .ivu-radio-wrapper .ivu-radio.ivu-radio-checked.ivu-radio-disabled .ivu-radio-inner {
  border-color: var(--color-primary) !important;
  &:after {
    background-color: var(--color-primary) !important;
  }
}
</style>
