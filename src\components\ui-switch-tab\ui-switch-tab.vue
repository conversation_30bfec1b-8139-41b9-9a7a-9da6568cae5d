<template>
  <div class="ui-switch-tab">
    <ul class="list">
      <li
        v-for="(item, index) in list"
        :key="index"
        :class="{ active: !!item.active }"
        class="ivu-tabs-tab"
        @click="tabClick(item)"
      >
        <span>{{ item[tabProps.label] }}</span>
        <div v-if="!!item[tabProps.total]" class="total">
          <span>{{ filterTotal(item[tabProps.total]) }}</span>
        </div>
      </li>
      <slot name="right-content"></slot>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'ui-switch-tab',
  props: {
    /**
     * {label:'', value:''}
     */
    tabList: {
      required: true,
    },
    tabProps: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value',
          total: 'total',
        };
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [Array, String, Number],
    },
    // filterTotal 支持>999后省略 @济南现场
    filterTotal: {
      type: Function,
      default(val) {
        return val > 99 ? '···' : val || 0;
      },
    },
  },
  data() {
    return {
      list: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    tabClick(item) {
      // 抛出改变之前选中的数值
      this.$emit('beforeChangeTab', this.value, item);
      // 更新绑定的数值
      if (this.multiple) {
        this.$set(item, 'active', !item.active);
        let selectList = [];
        this.list.forEach((row) => {
          if (row.active) {
            selectList.push(row[this.tabProps.value]);
          }
        });
        this.$emit('input', selectList);
      } else {
        this.$emit('input', item[this.tabProps.value]);
      }
      // 抛出更改之后的数值
      this.$emit('changeTab', item[this.tabProps.value]);
    },
    setActive() {
      this.list.forEach((row) => {
        if (Array.isArray(this.value)) {
          let index = this.value.findIndex((rw) => rw === row[this.tabProps.value]);
          if (index === -1) {
            this.$set(row, 'active', false);
          } else {
            this.$set(row, 'active', true);
          }
        } else {
          if (this.value === row[this.tabProps.value]) {
            this.$set(row, 'active', true);
          } else {
            this.$set(row, 'active', false);
          }
        }
      });
    },
  },
  watch: {
    tabList: {
      handler(val) {
        if (!val) return;
        this.list = this.$util.common.deepCopy(this.tabList);
        this.setActive();
      },
      immediate: true,
    },
    value() {
      this.setActive();
    },
  },
  computed: {},
  components: {},
};
</script>
<style lang="less" scoped>
.ui-switch-tab {
  line-height: 34px;
  font-size: 14px;
  .list {
    display: inline-block;
    li {
      cursor: pointer;
      display: inline-block;
      padding: 0 15px;
      position: relative;
      background: var(--bg-switch-tag-tab);
      color: var(--color-switch-tag-tab);
      border: 1px solid var(--border-switch-tag-tab);
      &:not(&:first-child) {
        border-left: none;
      }
      &:hover {
        background: var(--color-primary);
        color: #fff;
      }
      &:first-child {
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
      }
      &:last-child {
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
      }
      .total {
        position: absolute;
        top: -5px;
        right: -10px;
        border-radius: 50%;
        height: 16px;
        width: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 5px;
        background-color: #bc3c19;
        color: #fff;
        span {
          transform: scale(0.8);
          display: inline-block;
          vertical-align: middle;
        }
      }
    }
  }
  li.active {
    border: 1px solid var(--color-primary);
    background: var(--color-primary);
    color: #fff;
  }
}
</style>
