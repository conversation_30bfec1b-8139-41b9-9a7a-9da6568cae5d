<template>
    <div class="video-page-container">
        <div class="header"><i :class="['icon-title','iconfont',getDeviceIcon(deviceInfo)]"></i> {{deviceInfo.deviceName||"暂无名称"}}
            <!-- <span class="view-more" @click="viewMore">查看更多</span> -->
        </div>
        <div class="video-container">
            <img :src="deviceInfo.sceneImg" alt="" v-show="!isVideo">
            <h5-player :options="h5Options" sceneFrom="singleVideo" :deviceObj="deviceObj" ref="h5Playerx" v-show="isVideo"></h5-player>
        </div>
    </div>
</template>
<script>
import { getRelationCameraByGbId } from "@/api/wisdom-cloud-search";
import { getFrontBackDate } from "@/util/modules/common";

let trackWindow = 0;

export default {
    name: "videoComponent",
    data() {
        return {
            h5Options: {
                layout: "1*1"
            },
            isVideo: false,
            deviceObj: {}
        };
    },
    props: ["deviceInfo","range"],
    methods: {
        /**
         * 获取图标
         */
        getDeviceIcon(item) {
            console.log("device",item)
            return Toolkits.getDeviceIconType(item);
        },
        viewMore() {
            //  如果存在窗口则先关闭
            if (trackWindow && trackWindow.opener) {
                trackWindow.close();
            }
            trackWindow = window.open("bigscreen.html#/videoPage", "videoPage");
            trackWindow.onload = () => {
                let params = {
                    origin: "videoPage",
                    resource: JSON.stringify({
                        deviceInfo:this.deviceInfo,
                        range:this.range
                    })
                };
                trackWindow.postMessage(params, "*");
            };
        },
    },
    watch:{
        deviceInfo:{
            handler:function(val){
                if(val){
                    getRelationCameraByGbId(val.deviceId).then((res) => {
                        if (res.data) {
                            this.isVideo = true;
                            const { gbId, name, latitude, longitude } = res.data;
                            let params = {
                                deviceId: gbId,
                                deviceName: name,
                                geoPoint: {
                                    lon: longitude,
                                    lat: latitude,
                                },
                                devicetype: vodType,
                                playType: "vod",
                            };
                            let begintime = getFrontBackDate(
                                val.absTime || val.alarmTime,
                                15000,
                                1
                            );
                            let endtime = getFrontBackDate(
                                val.absTime || val.alarmTime,
                                15000,
                                0
                            );
                            this.deviceObj = { ...params, begintime, endtime };
                        } else {
                            this.isVideo = false;
                            this.deviceObj = {}
                        }
                    });
                }
                console.log(val)
            },
            deep:true//对象内部的属性监听，也叫深度监听
        }
    },
    mounted() {
        
    },
    components: {
        
    }
};
</script>
<style lang="less">
.video-page-container {
    position: relative;
    border-radius: 2px;
    background: #fff;
    .header {
        width: 100%;
        height: 40px;
        line-height: 40px;
        // background: #2C86F8;
        color: #333333;
        padding: 0px 20px;
        font-size: 14px;
        .view-more {
            position: absolute;
            right: 10px;
            color: #2C86F8;
            cursor: pointer;
            font-size: 12px;
        }
        .icon-title {
            color:#336ff3;
            margin-right: 5px;
        }
    }
    .video-container {
        margin: 10px;
        text-align: center;
        height: 310px;
        z-index: 999;
        .player-watermk {
            left: 0px;
        }
        &>img{
            height: 100%;
            max-width: 100%;
        }
        .xui-h5player .h5vp-content .h5vp-video-container.h5vp-bg-img{
            border-color: transparent!important;
        }
        
    }
}
</style>
