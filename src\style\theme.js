import { darkTheme, lightTheme, deepBlueTheme } from './variable';
import {
  darkThemeChart,
  lightThemeChart,
  darkThemeChartGroup,
  lightThemeChartGroup,
} from '@/style/echarts-variable.js';
import cssVars from 'css-vars-ponyfill';
import store from '@/store';

export const allThemeList = [
  { theme: 'dark', variables: darkTheme, text: '暗底炫酷蓝' },
  { theme: 'light', variables: lightTheme, text: '白底晴天蓝' },
  { theme: 'deepBlue', variables: deepBlueTheme, text: '白底深海蓝' },
];

export const initTheme = (theme = 'dark') => {
  let themeItem = allThemeList.find((item) => item.theme === theme);
  document.documentElement.setAttribute('data-theme', theme);
  store.dispatch('common/setThemeType', theme);
  setThemeChart();
  cssVars({
    variables: themeItem.variables, // variables 自定义属性名/值对的集合
    onlyLegacy: true, // false  默认将css变量编译为浏览器识别的css样式  true 当浏览器不支持css变量的时候将css变量编译为识别的css
  });
};

/**
 * @example $var('--color-base-text')
 */
function setThemeChart() {
  window.$var = (property) => {
    const darkTheme = { ...darkThemeChart, ...darkThemeChartGroup };
    const lightTheme = { ...lightThemeChart, ...lightThemeChartGroup };
    return store.state.common.themeType === 'dark' ? darkTheme[property] : lightTheme[property];
  };
  window.$cssVar = (property) => {
    return document.documentElement.style.getPropertyValue(property);
  };
}
