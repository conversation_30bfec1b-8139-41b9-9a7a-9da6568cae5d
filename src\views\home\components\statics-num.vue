<template>
  <div class="statics-num">
    <ul class="statics-num-ul">
      <li class="statics-num-li">
        <div class="border-line"></div>
        <div class="statics-num-li-div">sddsfsd</div>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.statics-num {
  position: absolute;
  top: 50px;
  left: 50%;
  transform: translateX(-50%);
  .statics-num-ul {
    display: flex;
    .statics-num-li {
      background: #064b9f;
      padding: 20px;
      .statics-num-li-div {
        background: #0b2758;
        width: 210px;
        height: 76px;
      }
      .border-line {
        height: 5px;
        width: 210px;
        background: linear-gradient(
          to right,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0) 10%,
          rgba(46, 224, 255, 0) 20%,
          rgba(46, 224, 255, 0) 30%,
          rgba(46, 224, 255, 0.9) 40%,
          rgba(255, 255, 255, 1) 50%,
          rgba(46, 224, 255, 0.9) 60%,
          rgba(46, 224, 255, 0) 70%,
          rgba(46, 224, 255, 0) 80%,
          rgba(0, 0, 0, 0) 90%,
          rgba(0, 0, 0, 0) 100%
        );
      }
    }
  }
}
</style>
