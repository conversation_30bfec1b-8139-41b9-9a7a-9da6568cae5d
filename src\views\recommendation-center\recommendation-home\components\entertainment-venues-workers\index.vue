<!--
    * @FileDescription: 疑似娱乐场所从业人员推荐
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="high-frequency-violations container">
        <!-- 地图 -->
        <mapCustom ref="mapBase" mapType="high-frequency-violations" sectionName="capture"/>
        <!-- 左面信息展示框 -->
        <leftBox ref="searchBar" @search="handleSearch" v-show="searchBox"></leftBox>
        <detailsBox ref="detailBox" 
            @backSearch="handleBackSearch"
            v-show="!searchBox"
            @cutAnalyse="handleCutAnalyse"></detailsBox>
        <!-- 右侧结果 -->
        <right-list-one :appearList="appearList" :total="total" :loading="loading" :loadingText="loadingText" v-if="rightShowList" @handleReachBottom="handleReachBottom" @show-pic="showPic"></right-list-one>
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import detailsBox from './components/detailsBox.vue';
import RightListOne from '@/views/model-market/components/RightListOne.vue';
import mapCustom from '@/views/model-market/components/map/index.vue';
import { getSuspectedEntertainmentVenuesPersonTrail } from "@/api/recommend";
export default {
    name: 'high-frequency-violations',
    components:{
        mapCustom,
        leftBox,
        detailsBox,
        RightListOne
    },
    props: {
      taskParams: {
        type: Object,
        default: () => ({})
      }
    },
    data () {
        return {
            searchBox: true,
            rightShowList: false,
            appearList: [],
            loadingText: '加载中',
            isLast: false,
            loading: false,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20,
            },
            detailsParams: {}
        }
    },
    async created() {
        this.$nextTick(() => {
            // 推荐中心查看
            if (!Toolkits.isEmptyObject(this.taskParams)) {
                let params = {...this.taskParams.params}
                delete params.dayNum
                delete params.deviceGbIdList
                delete params.selectDeviceList
                if (this.taskParams.queryStartTime) this.$refs.searchBar.queryParams.startTime = this.taskParams.queryStartTime
                if (this.taskParams.queryEndTime) this.$refs.searchBar.queryParams.endTime = this.taskParams.queryEndTime
                this.$refs.searchBar.queryParams = { ...this.$refs.searchBar.queryParams, ...params }
                this.$refs.searchBar.handleSearch()
            }
        })
	},
    methods: {
        handleSearch(params){
            this.searchBox = false;
            this.$nextTick(() => {
                this.$refs.detailBox.handleList({...params})
            })
        },
        handleBackSearch(){
            this.searchBox = true;
            this.handleCancel()
        },
        handleCutAnalyse(data) {
            this.rightShowList = true;
            this.pageInfo.pageNumber = 1
            this.appearList = []
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            this.detailsParams = {
                endTime: data.endTime,
                startTime: data.startTime,
                vid: data.archiveNo
            }
            this.getRightList()
        },
        getRightList(){
            this.loading = true;
            let params = {...this.detailsParams}
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            getSuspectedEntertainmentVenuesPersonTrail(params)
            .then(res => {
                let list = res.data || []
                this.appearList = this.appearList.concat(list)
                this.basicPoints = this.appearList.map((item) => {
                    item.showIconBorder = true;
                    return item;
                });
                this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, true);
                this.$refs.mapBase.setCenter(this.basicPoints[0]);
                if (list && list.length) {
                    this.pageInfo.pageNumber += 1
                    this.total = res.data.length
                } else {
                    this.isLast = true
                }
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleReachBottom () {
            this.loadingText = '加载中'
            if (this.isLast) {
                this.loadingText = '已经是最后一页了'
                return
            }
            return this.getRightList()
        },
        handleCancel() {
            this.rightShowList = false;
            this.$refs.mapBase.resetMarkerbasicPoint();
            this.$refs.mapBase.clearPoint()
        },
        showPic(item,index){
          this.$refs.mapBase.chooseNormalPoint(
                this.basicPoints,
                "capture",
                index
            );
        },
    }
}
</script>

<style lang='less' scoped>
.high-frequency-violations{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
