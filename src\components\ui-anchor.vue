<template>
  <Anchor show-ink :container="container">
    <AnchorLink v-for="(item, $index) in anchorLinkList" :key="$index" :href="item.href" :title="item.title" />
  </Anchor>
</template>
<script>
  export default {
    props: {
      container: {
        type: String,
        default: '.content'
      },
      anchorLinkList: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
    data() {
      return {

      }
    }
  }
</script>