// 点击空白区域消失该区域
export default function (Vue) {
  Vue.directive('clickoutside', {
    bind (el, bind, vnode) {
      function documentHandler (e) {
        if (el.contains(e.target)) {
          return false
        }
        if (bind.expression) {
          bind.value(e)
        }
      }
      el._vueClickOutside_ = documentHandler
      document.addEventListener('click', documentHandler)
    },
    inserted (el, bind, vnode) {

    },
    update (el, bind, vnode) {

    },
    unbind (el, bind, vnode) {
      document.removeEventListener('click', el._vueClickOutside_)
      delete el._vueClickOutside_
    },
    runs (el, bind) {
    }
  })
}
