<template>
  <ui-modal title="数据输出" v-model="visible" :styles="styles" :footer-hide="true">
    <span class="modify-time base-text-color">最新更新时间：{{ modifyTime || '未知' }}</span>
    <div class="top-wrapper mt-sm">
      <div class="search-box">
        <ui-label class="fl" label="组织机构" :width="70">
          <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择">
          </api-organization-tree>
        </ui-label>
        <ui-label class="fl ml-lg" label="关键词" :width="50">
          <Input v-model="searchData.keyWord" class="input-width" placeholder="请输入设备名称或编码"></Input>
        </ui-label>
        <ui-label class="fl ml-lg" :label="global.filedEnum.sbdwlx" :width="100">
          <Select
            v-model="searchData.sbdwlxList"
            multiple
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
            class="input-width"
          >
            <Option
              v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
              :key="'sbdwlx' + bdindex"
              :value="sbdwlxItem.dataKey"
              >{{ sbdwlxItem.dataValue }}</Option
            >
          </Select>
        </ui-label>
        <ui-label class="fl ml-lg" label="摄像机功能类型" :width="110">
          <Select v-model="searchData.sbgnlxList" placeholder="请选择摄像机功能类型" multiple class="input-width">
            <Option
              v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
              :key="'sbgnlx' + bdindex"
              :value="sbgnlxItem.dataKey"
              >{{ sbgnlxItem.dataValue }}</Option
            >
          </Select>
        </ui-label>
        <Dropdown trigger="custom" :visible="visibleMoreTab" placement="bottom-start">
          <div class="inline more-class pointer ml-lg" @click.stop="showMoreTab">
            <span class="inline vt-middle mr-xs">更多</span>
            <i class="icon-font icon-xiala f-12"></i>
          </div>
          <DropdownMenu slot="list">
            <div v-clickoutside="hideMoreTab" class="more-tab">
              <div
                class="tab-item"
                v-for="(item, index) in moreList"
                :key="index"
                @click="selectMoreItem(item)"
                :class="{ active: !!item.active }"
              >
                <span>{{ item.tagName }}</span>
                <!-- <div class="total">
                                    <span>{{ item.total }}</span>
                                </div> -->
              </div>
            </div>
          </DropdownMenu>
        </Dropdown>
        <Button type="primary" class="ml-lg mr-sm" @click="getTopicDetailList"> 查询 </Button>
        <Button type="default" class="mr-lg" @click="resetInitial"> 重置 </Button>
      </div>
      <Button type="primary" class="button-blue export-class" @click="exportExcel" :loading="exportLoading">
        <i class="icon-font icon-daochu delete-icon"></i>
        <span class="inline vt-middle ml-xs" v-if="!exportLoading">导出</span>
        <span v-else>Loading...</span>
      </Button>
    </div>
    <line-title title-name="检测结果统计"></line-title>
    <div class="echarts-wrapper">
      <div class="ring-box">
        <draw-echarts
          :echart-option="ringEchartsOption"
          :echart-style="ringStyle"
          ref="zdryChart1"
          class=""
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
      <div class="column-box">
        <draw-echarts
          :echart-option="columnEchartsOption"
          :echart-style="columnStyle"
          ref="zdryChart2"
          class=""
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
    </div>
    <line-title title-name="问题数据列表" class="mb-sm"></line-title>
    <ui-select-tabs class="tabs-ui" @selectInfo="selectInfo" :list="tabsList" v-if="!!tabsList.length"></ui-select-tabs>
    <div class="table-wrapper mt-sm">
      <ui-table
        class="ui-table"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #action="{ row }">
          <span class="font-table-action pointer" @click="openUnquatifiyReason(row)">查看不合格原因</span>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <unquatifiyreason-popup v-model="unquatifiyReasonPopup" :table-data="unquatifiyData" :loading="loading">
    </unquatifiyreason-popup>
  </ui-modal>
</template>
<script>
import user from '@/config/api/user';
import taganalysis from '@/config/api/taganalysis';
export default {
  props: {
    value: {},
    tasktrackingInter: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        // top: "0.4rem",
        width: '95%',
      },
      unquatifiyReasonPopup: false,
      unquatifiyData: [],
      tableColumns: [
        { type: 'index', width: 70, title: '序号', fixed: 'left' },
        { title: '设备编码', key: 'deviceId', width: 200, fixed: 'left' },
        { title: '设备名称', key: 'deviceName', width: 200, fixed: 'left' },
        { title: '组织机构', key: 'orgName', width: 120 },
        { title: '经度', key: 'longitude', width: 150 },
        { title: '纬度', key: 'latitude', width: 150 },
        { title: 'MAC地址', key: 'macAddr', width: 180 },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 180 },
        { title: '数据来源', key: 'sourceId', width: 180 },
        { title: '安装地址', key: 'address', width: 200 },
        { title: '监控点位类型', key: 'sbdwlxText', width: 130 },
        { title: '摄像机功能类型', key: 'sbgnlxText', width: 150 },
        { title: '摄像机位置类型', key: 'positionTypeText', width: 150 },
        { title: '设备状态', key: 'sblwztText', width: 180 },
        { title: '操作', slot: 'action', fixed: 'right', width: 150 },
      ],
      minusTable: 620,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      ringStyle: {
        height: '180px',
        width: '400px',
      },
      columnStyle: {
        height: '180px',
        width: '1200px',
      },
      modifyTime: '',
      permission: '',
      // tabsList: [
      //     { name: '字段1为空', select: true },
      //     { name: '字段2为空', select: false },
      //     { name: '字段3为空', select: false },
      // ],
      ringEchartsOption: {},
      columnEchartsOption: {},
      tableData: [],
      tabsList: [],
      loading: false,
      echartsLoading: false,
      selectOrgTree: {
        orgCode: '',
      },
      dictData: {},
      searchData: {
        keyWord: '',
        sbgnlxList: '',
        sbdwlxList: '',
        pageNumber: 1,
        pageSize: 20,
        tagIds: [],
      },
      errorSelectList: [],
      visibleMoreTab: false,
      moreList: [],
      exportLoading: false,
    };
  },
  methods: {
    init() {
      this.visible = true;
      this.ringEchartsOption = {};
      this.columnEchartsOption = {};
      this.errorSelectList = [];
      this.tabsList = [];
      this.tableData = [];
      this.selectOrgTree = {
        orgCode: '',
      };
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.getDictData();
      this.strategiesFunc();
      this.getTagList();
    },
    strategiesFunc() {
      this.getTopicStatisticsCount().then((data) => {
        this.handleRingData(data);
      });
      this.getTopicStatisticsList().then((data) => {
        this.handleColumnData(data);
        this.handleTabsList(data.map((item) => item.errorMessage));
      });
      this.getTopicDetailList();
    },
    resetInitial() {
      this.selectOrgTree.orgCode = null;
      this.searchData.sbgnlxList = [];
      this.searchData.sbdwlxList = [];
      this.searchData.tagIds = [];
      this.searchData.keyWord = '';
      this.moreList.forEach((item) => {
        item.active = false;
      });
      this.resetSearchDataMx(this.searchData, this.getTopicDetailList);
    },
    openUnquatifiyReason(row) {
      this.unquatifiyReasonPopup = true;
      this.getErrorMessgae(row);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.getTopicDetailList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.getTopicDetailList();
    },
    selectMoreItem(item) {
      this.$set(item, 'active', !item.active);
      // [this.moreList.filter( item => !!item.active)].map( one => one.tagIds)
      let activeList = this.moreList.filter((item) => !!item.active);
      this.searchData.tagIds = activeList.map((one) => one.tagId);
      //this.getTopicDetailList()
    },
    selectedOrgTree(val) {
      this.selectOrgTree.orgCode = val.orgCode;
      this.strategiesFunc();
    },
    handleTabsList(data) {
      this.tabsList = data.map((item) => {
        let one = {
          select: false,
          name: item,
        };
        return one;
      });
    },
    handleColumnData(data) {
      let opts = {
        xAxisData: data.map((item) => item.errorMessage || '未知'),
        data: data.map((item) => item.count || 0),
      };
      this.columnEchartsOption = this.$util.doEcharts.taskTrackingColumn(opts);
    },
    handleRingData(data) {
      this.tabsList = data.errorMessageList.map((item) => {
        let one = {
          select: false,
          name: item,
        };
        return one;
      });
      let opts = {
        text: '数据总量',
        subtext: `${data.deviceCount}`,
        data: [
          { name: '合格数据', value: data.goodCount, color: 'greenColor' },
          { name: '不合格数据', value: data.failCount, color: 'redColor' },
        ],
        legendData: ['合格数据', '不合格数据'],
      };
      this.ringEchartsOption = this.$util.doEcharts.taskTrackingRing(opts);
    },
    selectInfo(val) {
      this.errorSelectList = val.map((item) => item.name);
      this.searchData.pageNumber = 1;
      this.getTopicDetailList();
    },
    showMoreTab() {
      this.visibleMoreTab = true;
    },
    hideMoreTab() {
      this.visibleMoreTab = false;
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.moreList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getTopicDetailList() {
      try {
        this.loading = true;
        let params = {};
        Object.assign(params, this.searchData);
        params.errorMessageList = this.errorSelectList;
        params.orgCode = this.selectOrgTree.orgCode;
        let { data } = await this.$http.post(this.tasktrackingInter.queryListByTaskTracker, params);
        this.tableData = [];
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 查询问题列表
    async getTopicStatisticsList() {
      this.echartsLoading = true;
      let params = {
        checkStatus: 2,
      };
      params.orgCode = this.selectOrgTree.orgCode;
      try {
        let { data } = await this.$http.post(this.tasktrackingInter.queryTopicStatisticsList, params);
        return data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    async getTopicStatisticsCount() {
      this.echartsLoading = true;
      let params = {
        checkStatus: 2,
      };
      params.orgCode = this.selectOrgTree.orgCode;
      try {
        let { data } = await this.$http.post(this.tasktrackingInter.queryCountStatistics, params);
        this.modifyTime = data.data.lastCheckDate;
        return data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    exportExcel() {
      this.exportLoading = true;
      let data = {};
      Object.assign(data, this.searchData);
      data.errorMessageList = this.errorSelectList;
      data.orgCode = this.selectOrgTree.orgCode;
      this.$http
        .post(taganalysis.importAllDeviceInfo, data, {
          responseType: 'arraybuffer',
        })
        .then((res) => {
          if (res.status == 200) {
            this.exportLoading = false;
            let a = document.createElement('a');
            let blob = new Blob([res.data], {
              type: 'application/vnd.ms-excel',
            });
            let objectUrl = URL.createObjectURL(blob);
            a.setAttribute('href', objectUrl);
            let now = new Date(),
              year = now.getFullYear(),
              mon = now.getMonth() + 1,
              day = now.getDate(),
              hours = now.getHours(),
              min = now.getMinutes(),
              sec = now.getSeconds();
            var dataStr =
              '' +
              year +
              (mon < 10 ? '0' + mon : mon) +
              (day < 10 ? '0' + day : day) +
              '-' +
              (hours < 10 ? '0' + hours : hours) +
              (min < 10 ? '0' + min : min) +
              (sec < 10 ? '0' + sec : sec);
            a.setAttribute('download', '视图基础数据治 - 结果导出' + ' - [iVDG] - [' + dataStr + '].xls');
            a.click();
          } else {
            this.exportLoading = false;
          }
        });
    },
    // 查看不合格原因
    async getErrorMessgae(row) {
      this.unquatifiyData = [];
      let params = {
        //topicComponentId: this.popUpOption.filedData.id,
        deviceInfoId: row.id,
      };
      try {
        let { data } = await this.$http.post(this.tasktrackingInter.queryPropertyDetailList, params);
        this.unquatifiyData = data.data;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        this.pageData = {
          pageNum: 1,
          pageSize: 20,
          totalCount: 0,
        };
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    unquatifiyreasonPopup: require('./unquatifiyreason-popup.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  margin: 10px 0 0;
  .ring-box {
    width: 570px;
    margin-right: 10px;
    .echarts {
      width: 540px !important;
    }
  }
  .column-box {
    flex: 1;
    position: relative;
  }
  .ring-box,
  .column-box {
    background: var(--bg-sub-content);
  }
  .charts {
    flex: 1;
    width: 300px;
  }
}
.table-wrapper {
  position: relative;
  .no-data {
    top: 60%;
  }
}
.top-wrapper {
  display: flex;
  height: 30px;
  line-height: 30px;
  color: #fff;
  justify-content: space-between;
  .search-box {
    display: flex;
    flex-wrap: wrap;
    .input-width {
      width: 230px;
    }
  }
}
.more-class {
  color: var(--color-primary);
}
.more-tab {
  height: 300px;
  overflow-y: scroll;
  color: var(--color-primary);
  .active {
    background-color: var(--color-primary);
    color: #fff;
  }
  .tab-item {
    width: 200px;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    padding: 0 18px;
    color: #fff;
    &:hover {
      background-color: var(--color-primary);
    }
  }
}

.tabs-ui {
  color: #fff;
  margin: 10px 0;
}
.keypersonlibrary-content-wrap {
  height: 270px !important;
  overflow-y: auto !important;
}
</style>
