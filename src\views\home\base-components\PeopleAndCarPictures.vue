<template>
  <div class="body-container" v-ui-loading="{ loading: echartLoading, tableData: evaluationIndexResultData }">
    <div class="card">
      <div class="header f-20">人</div>
      <div class="list">
        <div class="item" v-for="item in currentFaceResultData" :key="item.indexId">
          <div class="bg-top f-14"><span class="text" v-html="getBr(item.indexName)"></span></div>
          <div class="bg-bottom">{{(item?.resultValue).toFixed(1)}}%</div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="header f-18">车</div>
      <div class="list">
        <div class="item" v-for="item in currentVehicleResultData" :key="item.indexId">
          <div class="bg-top f-14"><span class="text" v-html="getBr(item.indexName)"></span></div>
          <div class="bg-bottom  num-color">{{item?.resultValue}}%</div>
        </div>
      </div>
    </div>
    <span class="next-echart">
      <i
        class="icon-font icon-zuojiantou1 f-12"
        @click="scrollAllData()"
      ></i>
    </span>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  name: 'PeopleAndCarPictures',
  props: {
    tableData: {},
    loading: {},
    styleType: {},
  },
  data() {
    return {
      echartLoading:false,
      evaluationIndexResultData:[],
      faceActiveTab: '2', // 脸
      faceResultData:[],
      currentFaceResultData:[],
      vehicleActiveTab: '3', // 车
      vehicleResultData:[],
      currentVehicleResultData:[],
      currentIndex: 0,
      pageSize:7,
      maxPage:1
    };
  },
  watch: {
    // loading(val) {
    //   this.echartLoading = val;
    // },
    tableData: {
      handler(val) {
        this.evaluationIndexResultData = val || [];
        if (val) {
          this.initList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    getBr(name){
      if(!name) return name;
      const index  = Math.ceil(name.length/2);
      const toInsert = '<br />';
      // 字符串插入
      return name.substring(0, index) + toInsert + name.substring(index);
    },
    async initList() {
      this.faceResultData = this.evaluationIndexResultData.filter((item) => {
        item.resultValue = item?.resultValue ?? 0;
        return item.indexModule === this.faceActiveTab;
      });
      this.vehicleResultData = this.evaluationIndexResultData.filter((item) => {
        item.resultValue = item?.resultValue ?? 0;
        return item.indexModule === this.vehicleActiveTab;
      });
      this.maxPage = Math.max(Math.ceil(this.faceResultData.length / this.pageSize),Math.ceil(this.vehicleResultData.length / this.pageSize));
     this.setData();
    },
    setData(){
      this.currentFaceResultData = this.faceResultData.slice(this.currentIndex * this.pageSize, (this.currentIndex + 1) * this.pageSize);
      this.currentVehicleResultData = this.vehicleResultData.slice(this.currentIndex * this.pageSize, (this.currentIndex + 1) * this.pageSize);
    },
    scrollAllData(){
      if(this.maxPage === 1) return;
      this.currentIndex ++;
      if(this.currentIndex >= this.maxPage)  this.currentIndex = 0;
      this.setData();
    }
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  flex: 1;
  .card {
    height: 50%;
    .header {
      position: relative;
      width: 131px;
      height: 24px;
      margin: 0 auto;
      text-align: center;
      font-weight: 400;
      line-height: 24px;
      background: linear-gradient(0deg, #1a80f6 0%, rgba(26, 128, 246, 0) 100%);
      color: #ffffff;
    }
    .list {
      display: flex;
      justify-content: space-around;
      margin-top: 10px;
      .item {
        .bg-top {
          position: relative;
          width: 124px;
          height: 40px;
          background: url(~@/assets/img/base-home/people-car-pictures/item-bg-top.png);
          background-size: cover;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #ffffff;
          text-shadow: 0px 2px 3px rgba(27, 33, 65, 0.81);
          text-align: center;
          .text{
            position: absolute;
            width: 124px;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
          }
        }
        .bg-bottom {
          width: 114px;
          height: 52px;
          background: url(~@/assets/img/base-home/people-car-pictures/item-bg-bottom.png);
          background-size: cover;
          font-family: Microsoft YaHei;
          font-weight: bold;
          color: #baf5ff;
          text-align: center;
          font-size: 24px;
          margin-top: -5px;
          &.num-color {
            color: #ffbb87;
          }
        }
      }
    }
  }
}
</style>
