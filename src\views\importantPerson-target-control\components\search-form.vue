<template>
  <div>
  <div class="top" :class="visible ? 'more-search-show' : ''">
    <RadioGroup
      v-model="queryParam.operationType"
      type="button"
      @on-change="radioChange"
    >
      <Radio
        v-for="(item, index) in radioList"
        :key="index"
        :label="item.key"
        >{{ item.value }}</Radio
      >
    </RadioGroup>
    <div class="right">
      <div class="inline-box" v-show="type == 'frequent'">
        <div class="title">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title">次数≥</div>
          <Input v-model="queryParam.minCount" placeholder="请输入"></Input>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'active'">
        <div class="title">最后一次活跃时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title">活跃度≥</div>
          <Input
            v-model="queryParam.minActiveScore"
            placeholder="请输入"
          ></Input>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'alarm'">
        <div class="title wid100">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title wid100">任务名称：</div>
          <Select
            v-model="queryParam.taskId"
            filterable
            clearable
            placeholder="请选择任务名称"
          >
            <Option
              v-for="(item, index) in taskList"
              :key="index"
              :value="item.taskId"
              >{{ item.taskName }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'task'">
        <div class="title wid100">时间类型：</div>
        <selectTag
          class="selectTag"
          :list="timeList"
          ref="taskTimeType"
          vModel="taskTimeType"
          @selectItem="selectItem"
        />
        <div class="item fixWidth">
          <div class="title wid100">任务名称：</div>
          <Input
            v-model="queryParam.taskName"
            placeholder="请输入任务名称"
          ></Input>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'cirminalWith'">
        <div class="title wid100">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title wid100">身份证号：</div>
          <Input
            v-model="queryParam.idCardNo"
            placeholder="请输入身份证号"
          ></Input>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'unusualActivity'">
        <div class="title wid100">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title wid100">报警设备：</div>
          <div class="select-tag-button" @click="selectDevice()">
            选择设备/已选（{{ queryParam.deviceIds.length }}）
          </div>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'riskyBehavior'">
        <div class="title wid100">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title wid100">报警设备：</div>
          <div class="select-tag-button" @click="selectDevice()">
            选择设备/已选（{{ queryParam.deviceIds.length }}）
          </div>
        </div>
        <div class="item fixWidth">
          <div class="title wid100">行为类型：</div>
          <Select
            v-model="queryParam.riskyBehaviorType"
            filterable
            clearable
            placeholder="请选择行为类型"
          >
            <Option
              :value="item.dataKey"
              v-for="item in riskyBehaviorTypeList"
              :key="item.dataKey"
              >{{ item.dataValue }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="inline-box" v-if="type == 'wanderingInvasion'">
        <div class="title wid100">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          type="month"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title wid100">报警设备：</div>
          <div class="select-tag-button" @click="selectDevice()">
            选择设备/已选（{{ queryParam.deviceIds.length }}）
          </div>
        </div>
        <div class="item fixWidth">
          <div class="title wid100">告警类型：</div>
          <Select
            v-model="queryParam.trackAlarmType"
            filterable
            clearable
            placeholder="请选择行为类型"
          >
            <Option
              :value="item.dataKey"
              v-for="item in trackAlarmTypeList"
              :key="item.dataKey"
              >{{ item.dataValue }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="btn">
        <Button class="margin" type="primary" @click="query()">查询</Button>
        <Button @click="reset()">重置</Button>
      </div>
      <div class="btn-group" @click="toggleSearch(!visible)" v-if="!noMore">
        <img src="@/assets/img/down-circle-icon.png" alt />
        <div class="more more-search-text">
          {{ visible ? "普通检索" : "高级检索" }}
        </div>
      </div>
    </div>
    <div class="more-search">
      <slot></slot>
    </div>

  </div>
  <select-device
    ref="selectDevice"
    :showOrganization="true"
    @selectData="selectData"
  />
</div>
</template>
<script>
import { deepCopy } from "@/util/modules/common";
import selectTag from "./select-tag.vue";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
import { mapActions, mapGetters } from "vuex";
export default {
  props: {
    radioList: {
      type: Array,
      default: () => [],
    },
    taskList: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "",
    },
    // 区分tab
    compareType: {
      type: [Number, String],
      default: "",
    },
    noMore: {
      type: Boolean,
      default: false,
    },
  },
  components: { selectTag },
  data() {
    return {
      dateType: 1,
      queryParam: {
        operationType: 99,
        endAlarmTime: "", // 结束时间
        minActiveScore: "", // 最小活跃度
        taskName: "",
        taskId: "",
        alarmTimeB: "",
        alarmTimeE: "",
        startAlarmTime: "",
        endAlarmTime: "",
        minCount: "", // 最小次数
        idCardNo: "",
        startTime: "", // 开始时间
        endTime: "", // 结束时间
        deviceIds: [], // 选中设备的Id
        riskyBehaviorType: "", // 行为类型
        trackAlarmType: "", // 告警类型
      },
      timeList: [
        { name: "永久", value: 0 },
        // { name: '周期', value: '2' },
        { name: "自定义", value: 1 },
      ],
      visible: false,
      selectDeviceList: [], // 选中的设备
    };
  },
  computed: {
    keyWords() {
      return this.$route.query.keyWords || "";
    },
    ...mapGetters({
      riskyBehaviorTypeList: "dictionary/getRiskyBehaviorTypeList", // 行为类型
      trackAlarmTypeList: "dictionary/getTrackAlarmTypeList", // 告警类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  async mounted() {
    if (this.keyWords) {
      let params = {
        deviceName: this.keyWords,
        pageNumber: 1,
        pageSize: 100,
        filter: this.judgeUser,
        orgCodes: [],
      };
      let { data } = await queryDeviceList(params);
      data.entities.map((item) => {
        item.select = true;
      });
      this.selectData(data.entities);
    }
    let query = this.$route.query;
    if (query.taskId) {
      this.queryParam.taskId = query.taskId;
    }
    if (query.dateType) {
      this.dateType = Number(query.dateType);
      if (this.dateType === 4) {
        // 自定义时间，当为自定义时间时，query必定携带dateRange起止时间
        let { startDate, endDate } = query;
        this.changeDateType({ startDate, endDate });
        this.$nextTick(() => {
          this.$refs.quickDateRef.setDefaultDate([startDate, endDate]);
        });
      } else {
        this.$nextTick(() => {
          this.changeDateType(this.$refs.quickDateRef.getDate());
        });
      }
    } else {
      this.changeDateType(this.$refs.quickDateRef.getDate());
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 切换状态
     */
    radioChange() {
      this.$emit("query");
    },

    /**
     * @description: 切换日期
     * @param {object} value 当前选中的时间区间
     */
    changeDateType(value) {
      this.queryParam.alarmTimeB = value.startDate;
      this.queryParam.alarmTimeE = value.endDate;
      this.queryParam.endAlarmTime = value.endDate;
      this.queryParam.startAlarmTime = value.startDate;
      this.queryParam.startTime = value.startDate;
      this.queryParam.endTime = value.endDate;
    },

    /**
     * @description: 查询
     */
    query() {
      this.visible = false;
      this.$emit("query");
    },

    /**
     * @description: 重置
     */
    reset() {
      this.selectDeviceList = [];
      this.dateType = 1;
      this.queryParam.operationType = 99;
      this.queryParam.taskId = null;
      this.queryParam.taskName = "";
      this.queryParam.taskTimeType = null;
      this.queryParam.deviceIds = [];
      this.queryParam.riskyBehaviorType = ""; // 行为类型
      this.queryParam.trackAlarmType = ""; // 告警类型
      this.queryParam.idCardNo = ""; // 身份证号

      // 如果重置前选了自定义时间，需要置空
      this.$refs.quickDateRef.setDefaultDate();
      if (this.type == "task") {
        this.$refs.taskTimeType.currentIndex = -1;
      }
      this.$nextTick(() => {
        this.changeDateType(this.$refs.quickDateRef.getDate());
        this.$emit("reset");
      });
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 获取查询参数，暴露给父组件的方法
     * @return {object}
     */
    getQueryParams() {
      let query = {
        ...this.queryParam,
        operationType:
          this.queryParam.operationType == 99
            ? ""
            : this.queryParam.operationType,
      };
      return query;
    },

    /**
     * @description: 收起 | 展开高级搜索
     * @param {boolean} flag 状态
     */
    toggleSearch(flag = false) {
      this.visible = flag;
    },
    /**
     * @description: 选择设备，打开弹框
     */
     selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList, this.keyWords);
    },

    /**
     * @description: 初始化已选择的设备
     * @param {array} arr 已选择的设备
     */
    selectData(arr) {
      this.selectDeviceList = arr;
      this.queryParam.deviceIds = arr.map((e) => e.deviceGbId);
    },
  },
};
</script>
<style lang="less" scoped>
.top {
  position: relative;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dfdfdf;
  padding-bottom: 16px;
  /deep/ .ivu-radio {
    margin-right: 0 !important;
  }
  /deep/ .ivu-radio-wrapper-checked {
    background: rgba(44, 134, 248, 0.1) !important;
  }
  .right {
    display: flex;
    .inline-box {
      display: inline-flex;
      align-items: center;
    }
    .quick-date-wrap {
      display: flex;
      align-items: center;
    }
    .item {
      display: flex;
      .title {
        line-height: 36px;
        font-size: 14px;
        text-wrap: nowrap;
      }
      .wid100 {
        width: 100px;
        min-width: 70px;
      }
    }
    .fixWidth {
      width: 250px;
      margin-left: 26px;
    }
    .time-form {
      display: flex;
      align-items: center;
    }
    .right20 {
      margin-right: 20px;
    }
    .btn {
      padding: 0 30px;
      .margin {
        margin-right: 12px;
      }
    }
    .more {
      line-height: 36px;
      color: #1678f5;
      cursor: pointer;
    }
  }
}

.more-search {
  display: flex;
  position: absolute;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  background: #fff;
  z-index: 20;
  max-height: 0px;
  top: 100%;
  left: 0;
  transition: 0.3s;
  overflow: hidden;
  flex-direction: column;
  box-shadow: 0 2px 3px #b7cbe5;
}

.btn-group {
  display: flex;
  align-items: end;
  .more-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    // margin-right: 30px;
    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
.more-search-show {
  .more-search {
    max-height: 300px;
    transition: 0.7s;
    padding-top: 20px;
    margin-top: 1px;
  }
  .more-search-text {
    /deep/ .icon-jiantou {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
}
.selectTag {
  display: flex;
  align-items: center;
}

.btn-group {
  height: 34px;
  display: flex;
  align-items: center;
  cursor: pointer;
  img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    transform: rotate(180deg);
    transition: transform 0.2s;
  }
}
.more-search-show {
  .advanced-search {
    max-height: 400px;
    transition: max-height 0.5s;
  }
  .btn-group {
    /deep/img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
</style>
