<template>
  <div class="auto-fill">
    <div class="search-container">
      <ChartsContainer :abnormalCount="abnormalCount" />
      <search-bar
        ref="search"
        @search="search"
        :current-tree="currentTree"
        :tree-data="treeData"
        :taskObj="taskObj"
        @params-change="paramsChange"
      ></search-bar>
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" @click="deviceArchives(row)">{{ row.deviceId }}</span>
      </template>
      <template #longitude="{ row }">
        <div class="width-percent inline ellipsis" :title="row.longitude">
          {{ row.longitude | filterLngLat }}
        </div>
      </template>
      <template #latitude="{ row }">
        <div class="width-percent inline ellipsis" :title="row.latitude">
          {{ row.latitude | filterLngLat }}
        </div>
      </template>
      <template #action="{ row }">
        <ui-btn-tip icon="icon-shebeidangan" content="查看档案" @click.native="clickRow(row)"></ui-btn-tip>
      </template>
      <template #checkStatus="{ row }">
        <span
          class="tag"
          :style="{
            background: row.checkStatus === '1' ? '#0E8F0E' : '#BC3C19',
          }"
          >{{ checkStatusList(row.checkStatus) }}</span
        >
      </template>
      <template #sbcjqyName="{ row }">
        <Tooltip placement="top" transfer max-width="200">
          <span>{{ row.sbcjqyName }}</span>
          <div slot="content">
            <span>{{ row.sbcjqyName }}</span>
          </div>
        </Tooltip>
      </template>
      <template #address="{ row }">
        <Tooltip placement="top" transfer max-width="200">
          <span>{{ row.address }}</span>
          <div slot="content">
            <span>{{ row.address }}</span>
          </div>
        </Tooltip>
      </template>
      <template #deviceName="{ row }">
        <Tooltip placement="top" transfer max-width="200">
          <span>{{ row.deviceName }}</span>
          <div slot="content">
            <span>{{ row.deviceName }}</span>
          </div>
        </Tooltip>
      </template>
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { icons } from '../components/common';
export default {
  name: 'International-encoding-detection',
  components: {
    SearchBar: require('./components/search-bar').default,
    UiTable: require('@/components/ui-table.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
  },
  props: {
    taskObj: {
      default: () => {},
    },
    currentTree: {},
  },
  data() {
    return {
      treeData: [],
      tableColumns: [],
      columnsStart: [
        {
          title: '序号',
          type: 'index',
          width: 50,
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 180,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          slot: 'deviceName',
          ellipsis: true,
          tooltip: true,
        },
        { title: '组织机构', key: 'civilName' },
      ],
      columnsEnd: [
        {
          title: '检测结果',
          key: 'checkStatus',
          width: 95,
          fixed: 'right',
          align: 'center',
          slot: 'checkStatus',
        },
        { title: '异常原因', key: 'errorMessage', width: 150, fixed: 'right' },
        {
          width: 60,
          title: '操作',
          slot: 'action',
          align: 'left',
          fixed: 'right',
        },
      ],
      columnsLongLat: [
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          width: 150,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          width: 150,
        },
        {
          title: '安装地址',
          key: 'address',
          width: 200,
          slot: 'address',
          ellipsis: true,
          tooltip: true,
        },
      ],
      columnsIP: [{ title: `${this.global.filedEnum.ipAddr}`, key: 'ipAddr', width: 150 }],
      columnsFunctionType: [{ title: this.global.filedEnum.sbgnlx, key: 'sbgnlxName', width: 150 }],
      columnsMac: [
        {
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          width: 150,
        },
      ],
      columnsArea: [
        {
          title: '采集区域',
          key: 'sbcjqyName',
          slot: 'sbcjqyName',
          ellipsis: true,
          tooltip: true,
        },
      ],
      columnsDeviceStatus: [
        {
          title: `${this.global.filedEnum.phyStatus}`,
          key: 'phyStatusName',
          width: 150,
        },
      ],
      columnSpointType: [{ title: this.global.filedEnum.sbdwlx, key: 'sbdwlxName', width: 150 }],
      tableData: [],
      //分页
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      selectKey: '0',
      loading: false,
      abnormalCount: [],
      currentIcon: 'icon-exceptionlibrary',
      searchData: {},
    };
  },
  methods: {
    paramsChange(val) {
      this.searchData = val;
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    getStatisticsData(data) {
      this.abnormalCount = [];
      this.abnormalCount.push({
        title: '采集设备总数',
        count: data.allDeviceCount ? data.allDeviceCount : 0,
        icon: this.currentIcon,
      });
      if (!data.abnormalCount.length) return false;
      data.abnormalCount.map((item) => {
        this.abnormalCount.push({
          title: item.errorMessage,
          count: item.count,
          icon: this.currentIcon,
        });
      });
    },
    async getEvaluationStatisticsCount() {
      try {
        let params = {
          recordTypeId: this.$parent.currentTree.id,
          orgCode: this.searchData.orgCode,
          batchId: this.taskObj.indexResults.resultId,
          taskIndexId: this.taskObj.taskIndexId,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getBasicStatisticsCount, params);
        this.getStatisticsData(data);
      } catch (e) {
        console.log(e);
      }
    },
    checkStatusList(checkStatus) {
      return checkStatus === '1' ? '合格' : '不合格';
    },
    clickRow(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    search(val) {
      this.searchData = val;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      if (!this.searchData.orgCode) {
        return false;
        // return this.$Message.error("请选择组织机构")
      }
      this.init();
      if (this.taskObj.indexResults) {
        this.getEvaluationStatisticsCount();
      } else {
        this.abnormalCount = [];
        this.getStatisticsData([]);
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.init();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.init();
    },
    async init() {
      try {
        if (!this.taskObj.indexResults || !this.searchData.orgCode) {
          return false;
          // return this.$Message.error("请选择组织机构")
        }
        this.loading = true;
        this.tableData = [];
        let params = {
          recordTypeId: this.$parent.currentTree.id,
          batchId: this.taskObj.resultId,
          taskIndexId: this.taskObj.taskIndexId,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getBasicRecordPageList, params);
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total || 0;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
    getIcon() {
      this.currentIcon = icons[this.$parent.currentTree.id] || 'icon-exceptionlibrary';
      this.abnormalCount = [{ title: '采集设备总数', icon: this.currentIcon }];
    },
    getColumnByTypeID(id) {
      switch (id) {
        case 102:
          this.tableColumns = [...this.columnsStart, ...this.columnsEnd];
          break;
        case 103:
          this.tableColumns = [...this.columnsStart, ...this.columnsEnd];
          break;
        case 104:
          this.tableColumns = [...this.columnsStart, ...this.columnsLongLat, ...this.columnsEnd];
          break;
        case 105:
          this.tableColumns = [...this.columnsStart, ...this.columnsFunctionType, ...this.columnsEnd];
          break;
        case 106:
          this.tableColumns = [...this.columnsStart, ...this.columnsEnd];
          break;
        case 107:
          this.tableColumns = [...this.columnsStart, ...this.columnsArea, ...this.columnsEnd];
          break;
        case 108:
          this.tableColumns = [...this.columnsStart, ...this.columnSpointType, ...this.columnsEnd];
          break;
        case 109:
          this.tableColumns = [...this.columnsStart, ...this.columnsMac, ...this.columnsEnd];
          break;
        case 110:
          this.tableColumns = [...this.columnsStart, ...this.columnsIP, ...this.columnsEnd];
          break;
        case 111:
          this.tableColumns = [...this.columnsStart, ...this.columnsDeviceStatus, ...this.columnsEnd];
          break;
        default:
          this.tableColumns = [...this.columnsStart, ...this.columnsEnd];
      }
    },
  },
  created() {
    this.getColumnByTypeID(this.$parent.currentTree.id);
    this.getIcon();
  },
  computed: {},
};
</script>

<style lang="less" scoped>
.f-32 {
  font-size: 32px;
}
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}
.search-container {
  .charts-container {
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 2px;
    .charts-item {
      margin-right: 10px;
      width: 304px;
      height: 88px;
      background: #0f2f59;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      .number-wrapper {
        display: inline-block;
        margin-left: 16px;
      }
    }
  }
  .search {
    margin: 10px 0 10px 0;
  }
}
.page {
  padding-right: 0;
}
</style>
