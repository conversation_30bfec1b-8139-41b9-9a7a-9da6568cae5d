<template>
  <div class="ui-add">
    <Modal
      v-model="modalShow"
      class="modal"
      @on-cancel="onCancel"
      :width="width"
      :styles="styles"
      :footer-hide="footerHide"
      :mask-closable="false"
      :transfer="false"
      :transition-names="['fade', 'fade']"
      :fullscreen="fullScreen"
      :mask="mask"
    >
      <div class="add-title">
        <img src="@/assets/img/equipmentassets/collectionlibrary/add-title.png" alt="" />
        <span class="add-text">{{ title }}</span>
      </div>
      <div class="add-header">
        <div
          class="tabFont"
          v-for="item of stateOptions"
          :value="item.value"
          :key="item.value"
          :class="{ tableContent: state == item.value }"
          @click="changeState(item)"
        >
          {{ item.label }}
        </div>
        <div class="tab-content">
          <basics-property ref="BasicsProperty" v-show="state == 0"></basics-property>
          <state-property ref="StateProperty" v-show="state == 1"></state-property>
          <administration-property ref="AdministrationProperty" v-show="state == 2"></administration-property>
          <label-property ref="LabelProperty" v-show="state == 3"></label-property>
          <safety-property ref="SafetyProperty" v-show="state == 4"></safety-property>
          <photo-gallery ref="PhotoGallery" v-show="state == 5"></photo-gallery>
        </div>
      </div>
    </Modal>
  </div>
</template>
<style lang="less" scoped>
.ui-add {
  position: relative;
  .add-title {
    position: absolute;
    left: 315px;
    top: -25px;
    width: 224px;
    height: 66px;

    img {
      width: 224px;
      height: 66px;
    }
    .add-text {
      font-size: 20px;
      z-index: 999;
      color: #fff;
      position: absolute;
      top: 20px;
      left: 90px;
    }
  }
  .add-header {
    text-align: center;
    height: 34px;
    margin-top: 70px;
    display: flex;
    background-color: #1b3b65;
    align-items: center;
    justify-content: space-around;
    flex-wrap: nowrap;
    .table-head-state {
      color: #fff;
      font-size: 14px;
    }
    .tabFont {
      color: #fff;
      font-size: 14px;
      line-height: 34px;
      height: 34px;
      width: 100%;
    }
    .tableContent {
      height: 34px;
      line-height: 34px;
      display: inline-block;
      background: var(--color-primary);
      opacity: 1;
      font-size: 14px;
      background-color: var(--color-primary);
      color: #fff;
      cursor: pointer;
    }
  }
  .tab-footer {
    text-align: center;
    .btn-margin {
      margin: 0 20px !important;
    }
  }
  @{_deep}.ivu-modal-content {
    width: 854px;
    height: 790px;
    background-color: #1c325a;
  }
  @{_deep}.ivu-modal {
    width: 854px !important;
  }
}
</style>
<script>
export default {
  data() {
    return {
      modalShow: false,
      state: 0,
      stateOptions: [
        {
          label: '基础属性',
          value: 0,
        },
        {
          label: '状态属性',
          value: 1,
        },
        {
          label: '管理属性',
          value: 2,
        },
        {
          label: '标签属性',
          value: 3,
        },
        {
          label: '安全属性',
          value: 4,
        },
        {
          label: '图片库',
          value: 5,
        },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    //选择状态
    changeState(item) {
      this.list = [];
      this.pageNum = 1;
      this.state = item.value;
      this.init(true);
    },
    full() {
      this.$emit('full');
    },
    onCancel() {
      this.$emit('onCancel');
    },
  },
  watch: {
    modalShow(val) {
      this.$emit('input', val);
    },
    value: {
      handler(val) {
        this.modalShow = val;
      },
      immediate: true,
    },
  },
  computed: {},
  props: {
    value: Boolean,
    title: {},
    width: {},
    styles: {},
    footerHide: {
      default: false,
    },
    mask: {
      default: true,
    },
    fullScreen: {
      default: false,
    },
    hasFull: {
      default: false,
    },
  },
  components: {
    BasicsProperty: (resolve) =>
      require(['@/views/equipmentassets/components/additive-attribute/basics-property.vue'], resolve),
    StateProperty: (resolve) =>
      require(['@/views/equipmentassets/components/additive-attribute/state-property.vue'], resolve),
    AdministrationProperty: (resolve) =>
      require(['@/views/equipmentassets/components/additive-attribute/administration-property.vue'], resolve),
    LabelProperty: (resolve) =>
      require(['@/views/equipmentassets/components/additive-attribute/label-property.vue'], resolve),
    SafetyProperty: (resolve) =>
      require(['@/views/equipmentassets/components/additive-attribute/safety-property.vue'], resolve),
    PhotoGallery: (resolve) =>
      require(['@/views/equipmentassets/components/additive-attribute/photo-gallery.vue'], resolve),
  },
};
</script>
