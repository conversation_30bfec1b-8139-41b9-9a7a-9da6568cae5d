/*
 * @Date: 2025-01-22 16:16:44
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-27 19:40:22
 * @FilePath: \icbd-view\src\views\ipbd-system\basicsConfig\juenileForm.js
 */
export const communityFormData = {
  // 报警配置
  alarmLevelConfig: [{}, {}, {}],
  // 夜间出入多小区配置
  appearMultipleCommunityInNightConfig: {
    minAppearCommunityCount: 0,
    nightTime: [],
  },
  // 社区消失配置
  disappearConfig: {
    disappearDay: 0,
    labelIds: [],
  },
  // 场所配置
  placeIds: [],
  // 社区陌生人频繁出入配置
  strangerFreqConfig: {
    appearedDayInPastWeek: 0,
    enterFaceLib: 0,
  },
  // 同行配置
  travelAlongConfig: {
    maxIntervalSeconds: 0,
    minCount: 0,
  },
};
