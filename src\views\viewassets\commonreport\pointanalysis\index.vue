<template>
  <div class="point-analysis auto-fill">
    <search-module
      :active="active"
      :device-type-list="deviceTypeList"
      :device-status-list="deviceStatusList"
      @changeType="changeType"
      @showConfig="showConfig"
      @search="search"
      @report="report"
    ></search-module>
    <table-module
      :active="active"
      :search-data="searchData"
      @showDetails="showDetails"
      @showArea="showArea"
      @report="report"
    ></table-module>
    <equipment-details v-model="detailShow" :detail-data="detailData" @edit="edit"></equipment-details>
    <area-details v-model="areaShow" :area-data="areaData" @showDetails="showDetails" @report="report"></area-details>
    <configuration v-model="configShow"></configuration>
    <device-detail
      v-model="deviceDetailShow"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      @update="search"
    >
    </device-detail>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'pointanalysis',
  props: {},
  data() {
    return {
      active: 'zoningReport',
      deviceTypeList: [
        {
          label: '区划上报分析',
          value: 'zoningReport',
        },
        {
          label: '采集区域上报分析',
          value: 'collectionArea',
        },
      ],
      deviceStatusList: [
        {
          label: '视频监控',
          value: '1',
        },
        {
          label: '人脸卡口',
          value: '3',
        },
        {
          label: '车辆卡口',
          value: '2',
        },
      ],
      detailShow: false,
      detailData: {},
      configShow: false,
      areaShow: false,
      areaData: {},
      searchData: {},
      deviceDetailShow: false,
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      viewDeviceId: 0,
      deviceCode: '',
    };
  },
  created() {},
  methods: {
    changeType(item) {
      this.active = item.value;
    },
    showDetails(row, cascadeReportStatus, qualityStatus) {
      const codeType = this.active === 'zoningReport' ? 'civilCode' : 'sbcjqyList';
      const code = this.active === 'zoningReport' ? row.code : [row.code];
      this.detailData = {
        [codeType]: code,
        sbgnlx: this.searchData.sbgnlx,
        cascadeReportStatus: cascadeReportStatus,
        qualityStatus: qualityStatus,
      };
      this.detailShow = true;
    },
    showArea(area, status) {
      this.areaShow = true;
      this.areaData = {
        parentCode: area.code,
        parentName: area.name,
        sbgnlx: area.sbgnlx,
        sbdwlx: this.searchData.sbdwlx,
        status: status,
      };
    },
    showConfig() {
      this.configShow = true;
    },
    search(searchData) {
      this.searchData = Object.assign({}, searchData);
    },
    edit(device) {
      this.viewDeviceId = device.id;
      this.deviceCode = device.deviceId;
      this.deviceDetailTitle = '修改入库设备';
      this.deviceDetailAction = 'edit';
      this.deviceDetailShow = true;
    },
    report(row) {
      this.$UiConfirm({
        content: `您将要一键上报${row ? row.name : '所有数据'}，是否确认?`,
        title: '警告',
      }).then(async () => {
        try {
          let res = null;
          if (this.active === 'zoningReport') {
            res = await this.$http.get(equipmentassets.qualityByCivilCode, {
              params: {
                civilCode: row ? row.code : undefined,
                sbgnlx: this.searchData.sbgnlx,
              },
            });
          } else {
            res = await this.$http.get(equipmentassets.qualityBySbcjqy, {
              params: {
                sbcjqy: row ? row.code : undefined,
                sbgnlx: this.searchData.sbgnlx,
              },
            });
          }
          this.$Message.success(res.data.msg);
        } catch (err) {
          console.log(err, 'err');
        }
      });
    },
  },
  watch: {},
  components: {
    SearchModule: require('./components/search-module.vue').default,
    TableModule: require('./components/table-module.vue').default,
    EquipmentDetails: require('./components/equipment-details.vue').default,
    AreaDetails: require('./components/area-details.vue').default,
    Configuration: require('./components/config/index.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.point-analysis {
  position: relative;
  background-color: var(--bg-content);
}
</style>
