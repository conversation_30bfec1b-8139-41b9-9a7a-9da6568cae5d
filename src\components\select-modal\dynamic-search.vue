<template>
  <Form
    inline
    ref="formData"
    :model="formData"
    class="form"
    @submit.native.prevent
  >
    <Row>
      <Col
        v-for="(item, index) in formItemData"
        :span="item.colSpan || '5'"
        :key="index"
      >
        <FormItem :label="`${item.label}:`" :prop="item.key">
          <Input
            v-if="item.type === 'input'"
            v-model="formData[item.key]"
            :size="item.inputSize || 'small'"
            :placeholder="item.placeholder || '请输入'"
          ></Input>

          <Select
            v-if="item.type === 'select'"
            :placeholder="item.placeholder || '请选择'"
            v-model="formData[item.key]"
            :disabled="item.disabled"
            :max-tag-count="2"
            :multiple="item.multiple"
            clearable
          >
            <Option
              v-for="opt in item.options"
              :value="opt.value"
              :key="opt.label"
              placeholder="请选择"
            >
              {{ opt.label }}
            </Option>
          </Select>
        </FormItem>
      </Col>
      <Col span="4">
        <FormItem>
          <Button class="find" type="primary" @click="handleQuery">查询</Button>
          <Button type="default" @click="resetForm">重置</Button>
        </FormItem>
      </Col>
    </Row>
  </Form>
</template>
<script>
export default {
  props: {
    formItemData: {
      required: true,
      type: Array,
      default: () => [],
    },
    /**
     * 和formItemData中的key对应
     */
    formData: {
      required: true,
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  created() {},
  computed: {},
  methods: {
    getFormRef() {
      return this.$refs["formData"];
    },
    handleQuery() {
      this.$emit("handleQuery", this.formData);
    },
    /**
     * 重置表单
     */
    resetForm() {
      this.$refs["formData"].resetFields();
      this.$emit("resetForm");
    },
  },
};
</script>
<style lang="less" scoped>
.find {
  margin-right: 10px;
}
/deep/ .ivu-input-wrapper {
  width: 100% !important;
}
.form {
  width: 100%;
  /deep/.ivu-select {
    width: 100%;
  }
  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");
    .ivu-form-item-content {
      flex: 1;
      .ivu-select {
        width: 100%;
      }
    }
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}
</style>
