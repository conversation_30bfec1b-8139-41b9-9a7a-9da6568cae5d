<!--
    * @FileDescription: 时间选择器
    * @Author: H
    * @Date: 2023/9/23
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
  <div class="select-time" @click.stop>
    <ui-tag-select
      v-if="queryParam.timeSlot !== '自定义'"
      ref="tagSelect1"
      :hideCheckAll="true"
      :value="queryParam.timeSlot"
      @input="
        (e) => {
          input(e, 'timeSlot');
        }
      "
    >
      <ui-tag-select-option
        v-for="(item, $index) in captureTimePeriod"
        :key="$index"
        :name="item.name"
      >
        {{ item.name }}
      </ui-tag-select-option>
    </ui-tag-select>
    <div class="datepicker-wrap" v-else>
      <hl-daterange
        v-model="queryTime.startDate"
        @affirm="handleStartChange"
        key="1"
        :transfer="transfer"
      ></hl-daterange>
      <div class="line"></div>
      <hl-daterange
        v-model="queryTime.endDate"
        @affirm="handleEndChange"
        key="2"
        :transfer="transfer"
      ></hl-daterange>
      <p class="hl-btn" @click="handleCancel">取消</p>
    </div>
  </div>
</template>

<script>
import hlDaterange from "@/components/hl-daterange/index.vue";
import { getConfigDate } from "@/util/modules/common";
export default {
  name: "",
  components: {
    hlDaterange,
  },
  props: {
    reflectValue: {
      type: String,
      default: "近一天",
    },
    reflectTime: {
      type: Object,
      default: () => {
        return {};
      },
    },
    transfer: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      captureTimePeriod: [
        { name: "近一天", value: "1" },
        { name: "近三天", value: "2" },
        { name: "近一周", value: "3" },
        { name: "自定义", value: "4" },
      ],
      queryParam: {
        timeSlot: "近一天",
      },
      queryTime: {
        startDate: "",
        endDate: "",
      },
    };
  },
  watch: {
    reflectValue: {
      handler(value) {
        this.queryParam.timeSlot = value;
        if (value == "自定义") {
          this.queryTime.startDate = this.reflectTime.startDate;
          this.queryTime.endDate = this.reflectTime.endDate;
        }
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  computed: {},
  created() {
    // 临时方案
    if (this.$route.query.videoIdentity) {
      this.queryParam.timeSlot = "自定义";
      this.queryTime.startDate = getConfigDate(-29)[0] + " 00:00:00";
      this.queryTime.endDate = this.$dayjs(new Date()).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
  },
  mounted() {
    this.handleInit();
  },
  methods: {
    handleInit(noOnce = false) {
        if(this.queryParam.timeSlot == '自定义') {
                return
            };
      let date = this.timeTransition(this.queryParam.timeSlot);
      this.queryTime.startDate = date.startDate;
      this.queryTime.endDate = date.endDate;
      if (!noOnce){
            this.$emit("onceChange", {
            ...this.queryTime,
            timeSlot: this.queryParam.timeSlot,
        });
      }
      this.$forceUpdate();
    },
    handleCancel() {
      this.queryParam.timeSlot = "近一天";
      let date = this.timeTransition(this.queryParam.timeSlot);
      this.handleInit(true)
      this.$emit("change", {
        startDate: date.startDate,
        endDate: date.endDate,
        timeSlot: this.queryParam.timeSlot,
      });
    },
    // 开始时间
    handleStartChange(val) {
      this.$emit("change", {
        startDate: val,
        endDate: this.queryTime.endDate,
        timeSlot: this.queryParam.timeSlot,
      });
    },
    // 结束时间
    handleEndChange(val) {
      this.$emit("change", {
        startDate: this.queryTime.startDate,
        endDate: val,
        timeSlot: this.queryParam.timeSlot,
      });
    },
    // 清除时间
    clearChecked() {
      // 临时方案
      if (this.$route.query.videoIdentity) {
        this.queryParam.timeSlot = "自定义";
        this.queryTime.startDate = getConfigDate(-29)[0] + " 00:00:00";
        this.queryTime.endDate = this.$dayjs(new Date()).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.$emit("onceChange", {
          ...this.queryTime,
          timeSlot: this.queryParam.timeSlot,
        });
      } else {
        this.queryParam.timeSlot = "近一天";
        let date = this.timeTransition(this.queryParam.timeSlot);
        this.$emit("onceChange", {
          startDate: date.startDate,
          endDate: date.endDate,
          timeSlot: this.queryParam.timeSlot,
        });
      }
    },
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      let date = {};
      if (e == "自定义") {
        date = this.timeTransition(this.queryParam.timeSlot);
      }
      this.queryParam[key] = e;
      if (e !== "自定义") {
        date = this.timeTransition(this.queryParam.timeSlot);
      }
      this.queryTime.startDate = date.startDate;
      this.queryTime.endDate = date.endDate;
      this.$emit("change", {
        ...this.queryTime,
        timeSlot: this.queryParam.timeSlot,
      });
      this.$forceUpdate();
    },
    timeTransition(timeText) {
      let startDate = "",
        endDate = "";
      switch (timeText) {
        case "近一天":
          var arr = getConfigDate(-1);
          startDate = arr[1] + " 00:00:00";
          endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
          break;
        case "近三天":
          var arr = getConfigDate(-2);
          startDate = arr[0] + " 00:00:00";
          endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
          break;
        case "近一周":
          var arr = getConfigDate(-6);
          startDate = arr[0] + " 00:00:00";
          endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
          break;
        default:
          break;
      }
      return { startDate, endDate };
    },
  },
};
</script>

<style lang='less' scoped>
.select-time {
  .datepicker-wrap {
    display: flex;
    align-items: center;
    .line {
      height: 3px;
      width: 20px;
      background: #d2d8db;
      margin: 0 5px;
    }
    .hl-btn {
      width: 40px;
      color: #2c86f8;
      margin-left: 10px;
      cursor: pointer;
    }
  }
}
</style>
