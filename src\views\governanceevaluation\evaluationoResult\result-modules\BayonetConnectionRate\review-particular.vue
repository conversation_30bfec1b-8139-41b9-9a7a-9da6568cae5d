<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template #deviceId="{ row }">
        <span>{{ row.deviceId }}</span>
        <span class="more-span" v-if="row.multiDevice">多目</span>
      </template>
      <template #description="{ row }">
        <span :class="row.outcome == '1' ? 'sucess' : 'error'">{{ row.description }}</span>
      </template>
      <template slot="outcome" slot-scope="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template slot="option" slot-scope="{ row }">
        <ui-btn-tip icon="icon-chakanjietu" content="查看抓拍图片" class="mr-md" @click.native="checkReason(row)">
        </ui-btn-tip>
        <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
      </template>
    </Particular>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <look-pictute
      v-model="capturePictureVisible"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      :active-index-item="activeIndexItem"
      :current-row="currentRow"
      @handlePageChange="handleImgPageChange"
      @handlePageSizeChange="handleImgPageSizeChange"
      @onChangeDeviceIds="onChangeDeviceIds"
      title="抓拍图片"
    ></look-pictute>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import taganalysis from '@/config/api/taganalysis';
import { normalFormData, FaceTableColumns, iconStaticsList } from './util/enum/ReviewParticular.js';
export default {
  mixins: [particularMixin, dealWatch],
  inheritAttrs: false,
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      customSearch: false,
      defaultCheckedList: [],
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      iconList: iconStaticsList,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        checkStatus: null,
      },
      formItemData: normalFormData,
      tableColumns: [],
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      capturePictureVisible: false,
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      currentRow: {},
      reasonLoading: false,
      exportLoading: false,
      // 对应 该指标配置页面的【时间检测】枚举值
      timeDelayConfig: Object.freeze({
        '1': '当天',
        '2': '最近两天',
        '3': '最近一周',
        '4': '最近30天',
        '5': '昨天',
        '6': '自定义',
      }),
      picDeviceIds: [],
    };
  },
  created() {
    this.tableColumns = FaceTableColumns();
    this.iconList[this.iconList.length - 1].name = this.activeIndexItem.indexName;
    // 异常原因下拉列表获取
    this.MixinDisQualificationList().then((data) => {
      let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
      findErrorCodes.options = data.map((item) => {
        return { value: Number(item.key), label: item.value };
      });
    });
    this.getTagList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    initAll() {
      // 列表
      this.getTableData();
      this.getTableDataTotal();
      // 统计获取
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });

        // 每个检测时间，当时配置检测的 时间范围 都可能不一样的，因此需要重新获取 表头 -- 【xxx抓拍数量】
        // 枚举对应 配置页面的 【时间范围】
        let text = '近x小时抓拍数量';
        let timeDelay = data.timeDelay ? String(data.timeDelay) : '';
        if (timeDelay) {
          let timeDelay_value = this.timeDelayConfig[timeDelay];
          text = timeDelay === '6' ? `近${data.lastHours}小时抓拍数量` : `${timeDelay_value}抓拍数量`;
        }
        this.tableColumns = FaceTableColumns({ totalTitle: text });
      });
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.getTableData();
      this.getTableDataTotal();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        // this.totalCount = data.total;
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    checkReason(row) {
      this.reasonPage = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.currentRow = row;
      this.capturePictureVisible = true;
      this.picDeviceIds = [row.deviceId];
      this.getReason(row);
    },
    handleImgPageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handleImgPageSizeChange(val) {
      this.reasonPage.pageNum = 1;
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    onChangeDeviceIds(deviceIds) {
      this.reasonPage.pageNum = 1;
      this.picDeviceIds = deviceIds;
      this.getFaceReason();
    },
    getReason() {
      this.getFaceReason();
    },
    async getFaceReason() {
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
          customParameters: {
            deviceIds: this.picDeviceIds,
            detailJson: this.currentRow.detailJson,
            deviceType: this.currentRow.deviceType,
          },
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        params.displayType === 'REGION'
          ? (params.orgRegionCode = this.$route.query.regionCode)
          : (params.orgRegionCode = this.$route.query.orgCode);
        this.reasonLoading = true;
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
        this.reasonPage.totalCount = datas.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
        this.getTableDataTotal();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        defaultPageData: this.pageData,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    LookPictute: require('./components/look-picture.vue').default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
.sucess {
  color: @color-success;
}
.error {
  color: @color-failed;
}
.more-span {
  display: inline-block;
  padding: 0 4px;
  margin-left: 10px;
  color: var(--color-primary);
  border-radius: 4px;
  background: rgba(44, 134, 248, 0.2);
  border: 1px solid var(--color-primary);
  font-size: 12px;
}
</style>
