<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-26 11:22:02
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-13 15:51:05
-->
<template>
  <div class="place-relation-ship-map-container">
    <div class="left">
      <Card type="place" :labelType="5" :baseInfo="baseInfo" />
    </div>
    <div class="right">
      <graph
        v-if="graphObj && hasGraphData"
        class="graph"
        :has-link="true"
        :has-toolbar="true"
        :layout-data="{
          linkDistance: 200,
        }"
      ></graph>
      <ui-empty v-else></ui-empty>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import relativeGraphMixin2 from "@/views/holographic-archives/mixins/relativeGraphMixin2";
export default {
  name: "PlaceRelationShipMap",
  mixins: [relativeGraphMixin2],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    Card: require("@/views/holographic-archives/components/relationship-map/basic-information")
      .default,
  },
  props: {
    baseInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
  },
};
</script>
<style lang="less" scoped>
.place-relation-ship-map-container {
  display: flex;
  flex: 1;
  height: 100%;
  padding: 10px;
  padding-top: 16px;
  /deep/.card-content {
    height: calc(~"(100% - 30px)");
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 0 !important;
  }
  /deep/ .ui-card {
    height: 100%;
  }
  .left {
    margin-right: 10px;
    height: 100%;
    width: 350px;
  }
  .right {
    width: calc(~"100% - 360px");
    background: #fff;
    position: relative;
    .graph {
      position: relative;
    }
  }
}
/deep/ .rel-node {
  background-color: transparent !important;
  border: 0 !important;
}
</style>
