<template>
  <div class="search-module">
    <div class="tab">
      <div
        v-for="(item, index) in deviceTypeList"
        :key="index"
        :class="['tab-item', active === item.value ? 'active' : '']"
        @click="changeType(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="btn-list">
      <span class="analysis-time">
        <i class="icon-font icon-shijian1 mr-xs"></i>
        <span class="inline vt-middle">最后分析时间：{{ analysisTime || '暂无' }}</span>
        <span class="ml-sm mr-sm delimiter"></span>
      </span>
      <Tooltip content='"更新数据" 按钮为操作后需要及时进行人工干预手动刷新' placement="bottom">
        <i class="icon-font icon-wenhao mr-sm"></i>
      </Tooltip>
      <Button type="primary" @click="qualityRefresh" :loading="refreshLoading">
        <i class="icon-font icon-shoudongshuaxin"></i>
        <span class="ml-xs">更新数据</span>
      </Button>
      <Button type="primary" class="ml-sm" @click="analysis" :loading="analysisLoading">
        <i class="icon-font icon-lijijiance-01"></i>
        <span class="ml-xs">{{ analysisLoading ? '分析中' : '立即分析' }}</span>
      </Button>
      <Button type="text" class="ml-sm" @click="configPoint">
        <i class="icon-font icon-canshupeizhi"></i>
        <span class="ml-xs inline vt-middle link">选点配置</span>
      </Button>
    </div>
    <div class="status over-flow mt-sm">
      <tag-view :list="tagList" @tagChange="changeStatus" ref="tagView"></tag-view>
    </div>
    <div class="mt-sm">
      <ui-label class="inline mr-lg" :label="global.filedEnum.sbdwlx">
        <Select
          class="width-md"
          v-model="searchData.sbdwlx"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          clearable
        >
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg" label="达标情况">
        <Select class="width-md" v-model="searchData.status" placeholder="请选择达标情况" clearable>
          <Option v-for="(item, index) in reachStandardList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
      <div class="fr">
        <Button type="primary" @click="report" :loading="reportLoading">
          <i class="icon-font icon-piliangshangbao"></i>
          <span class="inline vt-middle ml-xs">一键上报</span>
        </Button>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    active: {
      type: String,
      default: '',
    },
    deviceTypeList: {
      type: Array,
      defualt: [],
    },
    deviceStatusList: {
      type: Array,
      defualt: [],
    },
  },
  data() {
    return {
      analysisLoading: false,
      analysisTime: null,
      refreshLoading: false,
      reportLoading: false,
      tagList: [],
      reachStandardList: [
        {
          dataKey: '2',
          dataValue: '不达标',
        },
        {
          dataKey: '1',
          dataValue: '达标',
        },
      ],
      searchData: {
        sbgnlx: '1',
        sbdwlx: '',
        status: '',
      },
      timer: null,
    };
  },
  async created() {
    if (this.propertySearchLbdwlx.length === 0) await this.getAlldicData();
    this.tagList = this.deviceStatusList.map((row) => row.label);
    this.copySearchDataMx(this.searchData);
    this.init();
    this.getRunningStatus();
  },
  activated() {
    this.timer = setInterval(() => {
      this.getRunningStatus();
    }, 5000);
  },
  deactivated() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    changeType(item) {
      this.$emit('changeType', item);
      this.$nextTick(() => {
        this.reset();
      });
    },
    init() {
      this.search();
    },
    async getRunningStatus() {
      try {
        const res = await this.$http.get(equipmentassets.reunningStatus);
        this.analysisTime = res.data.data.runningByDeviceQualityTime;
        this.analysisLoading = res.data.data.isRunningByDeviceQuality;
        this.refreshLoading = res.data.data.isRunningStatistics;
      } catch (err) {
        console.log(err);
      }
    },
    search() {
      this.$emit('search', this.searchData);
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
      this.$refs.tagView.reset();
    },
    configPoint() {
      this.$emit('showConfig');
    },
    changeStatus(index) {
      this.searchData.sbgnlx = this.deviceStatusList[index].value;
      this.search();
    },
    analysis() {
      this.$UiConfirm({
        content: `您将要立即分析，是否确认?`,
        title: '警告',
      }).then(async () => {
        try {
          this.analysisLoading = true;
          const res = await this.$http.get(equipmentassets.executeCheck);
          this.$Message.success(res.data.msg);
        } catch (err) {
          console.log(err);
        } finally {
          this.analysisLoading = false;
        }
      });
    },
    async qualityRefresh() {
      try {
        this.refreshLoading = true;
        await this.$http.get(equipmentassets.qualityRefresh);
        this.$Message.info('正在重新统计中...');
        this.getRunningStatus();
      } catch (err) {
        console.log(err);
      }
    },
    report() {
      this.$emit('report');
    },
  },
  watch: {
    refreshLoading(val) {
      if (!val) {
        this.init();
      }
    },
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .btn-list .analysis-time {
    color: #a9bed9;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .btn-list .analysis-time {
    color: #888888;
  }
}
.search-module {
  margin: 10px 20px;
  position: relative;
  .icon-wenhao {
    color: var(--color-warning);
  }
  .tab {
    display: flex;
    border-bottom: 1px solid var(--devider-line);
    margin-top: 10px;
    .tab-item {
      cursor: pointer;
      height: 34px;
      line-height: 34px;
      color: var(--color-switch-tab);
      font-size: 14px;
      position: relative;
      text-align: center;
      padding: 3px 15px 0 15px;
      &.active {
        color: var(--color-switch-tab-active);
        border-left: 1px solid var(--devider-line);
        border-right: 1px solid var(--devider-line);
        &::before {
          position: absolute;
          left: 0;
          top: 0;
          content: '';
          display: inline-block;
          width: 100%;
          height: 3px;
          background-color: var(--color-switch-tab-active);
        }
        &::after {
          position: absolute;
          left: 0;
          bottom: -1px;
          content: '';
          display: inline-block;
          width: 100%;
          height: 1px;
          background-color: var(--bg-nav-tag);
        }
      }
    }
  }
  .btn-list {
    position: absolute;
    right: 0;
    top: 0;
    .icon-lijijiance-01 {
      color: #fff;
      font-size: 20px !important;
    }
    .analysis-time {
      .delimiter {
        display: inline-block;
        width: 1px;
        height: 18px;
        background-color: var(--devider-line);
        vertical-align: middle;
      }
    }
    .line {
      height: 18px;
      width: 2px;
      display: inline-block;
      background-color: var(--devider-line);
      vertical-align: middle;
    }
    .link {
      text-decoration: underline;
    }
  }
  .icon-piliangshangbao {
    font-size: 12px;
  }
}
</style>
