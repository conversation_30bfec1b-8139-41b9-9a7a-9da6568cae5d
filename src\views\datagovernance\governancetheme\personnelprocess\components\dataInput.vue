<template>
  <div class="dataInput">
    <ui-modal v-model="visible" title="数据输入" width="750px">
      <div class="header">
        <RadioGroup v-model="activeBtn" @on-change="handleChange" type="button" style="display: flex">
          <Radio :class="activeBtn == 'Datasource' ? 'active' : ''" label="Datasource">数据源</Radio>
          <Radio :class="activeBtn == 'Iptsynchro' ? 'active' : ''" label="Iptsynchro">同步方式</Radio>
        </RadioGroup>
      </div>
      <component :is="componentId" label="数据表"></component>
      <template slot="footer">
        <Button type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      visible: false,
      activeBtn: 'Datasource',
      componentId: 'Datasource',
    };
  },
  created() {},
  methods: {
    init() {
      this.visible = true;
    },
    handleChange(val) {
      this.componentId = val;
    },
  },
  watch: {},
  components: {
    Iptsynchro: require('../../components/iptsynchro.vue').default,
    Datasource: require('./datasource.vue').default,
  },
};
</script>
<style lang="less" scoped>
.dataInput {
  .header {
    margin-bottom: 17px;
  }
  .active {
    color: #ffffff !important;
    background: var(--color-primary) !important;
  }
  @{_deep} .ivu-modal-body {
    padding: 9px 51px;
  }
  @{_deep} .ivu-btn-primary {
    width: 90px;
    height: 34px;
    font-size: 14px;
    -webkit-text-stroke: 1 rgba(0, 0, 0, 0);
    text-stroke: 1 rgba(0, 0, 0, 0);
    border: none;
    background: linear-gradient(180deg, #2b84e2 0%, #083a78 100%);
    opacity: 1;
  }
}
</style>
<style lang="less">
.dataInput {
  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .ivu-radio-group-button .ivu-radio-wrapper {
    .flex;
    width: 96px;
    height: 36px;
    background: transparent;
    color: #56789c;
    border: 1px solid #1b82d2;
  }
  .ivu-radio-group-button .ivu-radio-wrapper:before {
    background: var(--color-primary) !important;
  }
}
</style>
