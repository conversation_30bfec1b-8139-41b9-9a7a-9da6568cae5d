<template>
  <ui-modal
    class="basic-information"
    v-model="visible"
    v-if="visible"
    title="查看详情"
    :styles="styles"
    :footer-hide="true"
  >
    <div class="carInfo auto-fill">
      <div class="title_text">
        <div>
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
        </div>
        <!-- <div class="export fr">
          <Button type="primary" class="btn_search">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs">导出</span>
          </Button>
        </div> -->
      </div>
      <line-title title-name="检测结果统计"></line-title>
      <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
      <ChartsContainer :abnormalCount="abnormalCount" />
      <line-title title-name="检测结果详情">
        <template slot="content">
          <slot name="content">
            <!-- <tagView
              class="tagView fr"
              ref="tagView"
              :list="['图片模式']"
              @tagChange="tagChange1"
            /> -->
          </slot>
        </template>
      </line-title>
      <!-- 图片模式 ------------------------------------------------------------------------------------------------------>
      <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo" v-if="modelTag === 0">
        <div slot="search" class="hearder-title">
          <SearchCard @startSearch="searchHandle"></SearchCard>
        </div>
        <!-- 卡片 -->
        <template #card="{ row }">
          <InfoCard class="card1" :list="row" :cardInfo="cardInfo" @bigImageUrl="bigImageUrl"> </InfoCard>
        </template>
      </TableCard>
      <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
      <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {},
  },
  components: {
    ChartsContainer: require('@/views/governanceevaluation/inspectionrecord/components/chartsContainer').default,
    TableCard: require('@/views/appraisaltask/inspectionrecord/components/tableCard.vue').default,
    InfoCard: require('./uploadIntegrityComponent/infoCard.vue').default,
    SearchCard: require('./uploadIntegrityComponent/search-card.vue').default,
    LookScene: require('@/components/look-scene').default,
    lineTitle: require('@/components/line-title').default,
    // tagView: require('../emphasis/components/tags.vue').default,
  },
  data() {
    return {
      visible: true,
      styles: { width: '8rem' },
      modelTag: 0,
      abnormalCount: [
        {
          title: '检测设备数量',
          icon: 'icon-jianceshebeishuliang',
          count: 0,
          key: 'evaluationDeviceNum',
        },
        {
          title: '单设备检测图像数量',
          icon: 'icon-danshebeijiancetuxiangshuliang',
          count: 0,
          key: 'perPumpQuantity',
        },
        {
          title: '实际检测图片总量',
          icon: 'icon-shijijiancetupianzongliang',
          count: 0,
          key: 'dataTotalNum',
        },
        {
          title: '字段完整图片数量',
          icon: 'icon-ziduanwanzhengtupianshuliang',
          count: 0,
          key: 'passDataNum',
        },
        {
          title: '字段缺失图片数量',
          icon: 'icon-ziduanqueshitupianshuliang',
          count: 0,
          key: 'notPassDataNum',
        },
        {
          title: '人脸抓拍数据上传完整率',
          icon: 'icon-renlianzhuapaishujushangchuanwanzhengshuai',
          qualified: true,
          count: '0%',
          key: 'integrityRate',
        },
      ],
      searchData: {},
      loadDataCard: (parameter) => {
        document.getElementsByClassName('keypersonlibrary-content')[0].scroll(0, 0);
        return this.$http
          .post(
            governanceevaluation.getFacePageListForCompletenessOfRate,
            Object.assign(
              parameter,
              {
                notSearchTotal: true,
                resultId: this.$parent.row.resultId,
                indexId: this.$parent.row.indexId,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      // 卡片展示参数，
      cardInfo: [],
      bigPictureShow: false, // 打图展示
      imgList: [], // 大图图片
    };
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  async mounted() {
    await this.init();
    await this.getChartsData();
  },
  methods: {
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoCard.info(true);
        } else {
          this.$refs.infoList.info(true);
        }
      });
    },
    infoData() {
      this.cardInfo = [
        { name: '抓拍：', value: 'shotTime' },
        { name: '接收：', value: 'receiveTime' },
        {
          type: 'fieldDefect',
          value: 'qualified',
          color: 'red',
          num_field: 'causeError',
        },
      ];
    },
    async init() {
      await this.infoData();
      if (this.modelTag === 0) {
        this.$refs.infoCard.info(true);
      } else {
        this.$refs.infoList.info(true);
      }
    },
    searchHandle(searchData) {
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
    async getChartsData() {
      try {
        let params = {
          resultIndexId: this.$parent.row.id,
        };
        let res = await this.$http.post(governanceevaluation.getFaceResultStatistics, params);
        if (!res.data.data || !res.data.data.indexJson) return;
        let resultStatistics = JSON.parse(res.data.data.indexJson) || [];
        this.abnormalCount.forEach((v) => {
          if (v.key !== 'integrityRate') {
            let item = resultStatistics.find((n) => {
              return n.key === v.key;
            });
            v.count = item.desc;
          } else {
            let rate = parseFloat(
              parseFloat(parseFloat(this.abnormalCount[3].count) / parseFloat(this.abnormalCount[2].count)) * 100,
            ).toFixed(2);
            if (rate >= res.data.data.standardsValue) {
              v.qualified = true;
            } else {
              v.qualified = false;
            }
            v.count = rate + '%';
          }
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.hearder-title {
  color: #fff;
  margin-top: 16px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.carInfo {
  position: relative;
  .title_text {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .tagView {
    display: inline-block;
  }
  .card1 {
    width: calc(calc(100% - 70px) / 7);
    margin: 0 10px 10px 0;
  }
  /deep/ .keypersonlibrary-content {
    max-height: 500px;
    min-height: 500px;
    overflow-y: auto;
  }
  /deep/ .keypersonlibrary .page {
    padding-left: 0;
  }
}
</style>
