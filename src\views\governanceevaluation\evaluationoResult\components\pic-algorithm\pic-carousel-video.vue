<template>
  <el-carousel
    indicator-position="none"
    ref="carousel"
    class="img-list"
    :arrow="imgList.length > 1 ? 'always' : 'never'"
    :autoplay="false"
    :initial-index="imgIndex"
    @change="changeIndex"
  >
    <el-carousel-item v-for="(item, index) in imgList" :key="`${index}-${item.id}`">
      <div class="content" ref="contentRef">
        <!-- 左侧内容 -->
        <div class="left-box" v-if="showLeftBox">
          <!-- 左上 -->
          <div ref="clockBoxRef">
            <clock-card
              v-if="currentAlgorithmInfo.clockData.length && clockPosition === 'left'"
              class="clock-content"
              :style="{ '--pic-box-width': picBoxWidth, '--imgDistRight': lenFormRight }"
              :current-algorithm-info="currentAlgorithmInfo"
              :show-algorithm-name="false"
            >
              <template #pseudo-class>
                <div class="line-before-class" v-if="currentAlgorithmInfo.clockData.length"></div>
              </template>
            </clock-card>
          </div>

          <!-- 左下 -->
          <camera-card
            class="left-bottom-content"
            v-if="currentAlgorithmInfo.cameraData.length"
            :current-algorithm-info="currentAlgorithmInfo"
            :current-row="item"
            :show-algorithm-name="false"
          ></camera-card>
        </div>

        <!-- 中间 图片部分 -->
        <div
          class="pic-box"
          :class="{
            'set-line': showRightBox && currentAlgorithmInfo.middleData.length,
            'no-img-data': !item[filedNameMap.bigPicName],
          }"
          :style="{
            '--bottom-line-margin': currentAlgorithmInfo.minMargin + lenFormRight,
          }"
        >
          <uiImage
            v-if="!!item[filedNameMap.bigPicName]"
            :src="item[filedNameMap.bigPicName]"
            ref="bigImage"
            @loadImage="loadImage"
          />
          <div v-else class="none">
            <uiImage :src="noDataImgUrl" />
            <span class="secondary">暂无大图</span>
          </div>
          <slot name="small-pic" :currentItem="item"></slot>
        </div>

        <!-- 右侧内容 -->
        <div class="right-box" v-if="showRightBox" ref="rightBoxRef">
          <!-- 右上 -->
          <div
            class="right-top-div"
            :class="{
              'set-max-height-100': !currentAlgorithmInfo.addressData.allInfo.length,
              'set-max-height-0': !currentAlgorithmInfo.middleData.length,
            }"
            ref="rightTopBoxRef"
          >
            <clock-card
              v-if="currentAlgorithmInfo.clockData.length && clockPosition === 'right'"
              class="clock-content"
              :current-algorithm-info="currentAlgorithmInfo"
              :show-algorithm-name="false"
              :style="{ '--imgDistRight': lenFormRight }"
              ref="clockBoxRef"
            >
              <template #pseudo-class>
                <div class="line-before-class" v-if="currentAlgorithmInfo.clockData.length"></div>
              </template>
            </clock-card>

            <div class="relative height-full">
              <div
                v-if="currentAlgorithmInfo.middleData.length"
                class="card-content middle-content"
                :class="{ 'mt-sm': currentAlgorithmInfo.clockData.length && clockPosition === 'right' }"
                :style="{
                  '--bottom-line-margin': currentAlgorithmInfo.minMargin + lenFormRight,
                }"
              >
                <p
                  class="p-text"
                  v-for="(middleItem, middleIndex) in currentAlgorithmInfo.middleData"
                  :key="`${middleIndex}-${middleItem.key}`"
                >
                  <span class="mr-xs">{{ middleItem.value }}</span>
                </p>
              </div>
            </div>
          </div>
          <!-- 右下 -->
          <div
            class="other-content"
            :class="{
              'set-max-height-100': !currentAlgorithmInfo.middleData.length,
              'set-max-height-0': !currentAlgorithmInfo.addressData.allInfo.length,
            }"
            ref="rightBottomBoxRef"
          >
            <!-- 右下 -- 地址相关信息 -->
            <div
              class="all-address-content"
              v-if="
                currentAlgorithmInfo.addressData.resultInfo.length || currentAlgorithmInfo.addressData.otherInfo.length
              "
              :class="{
                'set-max-height-100': !currentAlgorithmInfo.addressData.addressBottomInfo.length,
              }"
              :style="{
                '--rightBoxHeight': maxHeight.rightBoxHeight,
              }"
              ref="addressContentBoxRef"
            >
              <div class="right-bottom-line"></div>
              <!-- 需要显示 【档案信息】 的地址异常原因 -->
              <address-card
                v-if="currentAlgorithmInfo.addressData.resultInfo.length"
                class="address-content"
                :style="{ '--bottom-other-content-height': maxHeight.addressOtherRef }"
                :current-algorithm-info="currentAlgorithmInfo"
                :current-row="item"
                mode-type="video"
                ref="addresRef"
              ></address-card>
              <!-- 其余的地址异常原因 -->
              <div
                class="card-content bottom-other-content"
                v-if="currentAlgorithmInfo.addressData.otherInfo.length"
                :class="{
                  'set-max-height-100': !currentAlgorithmInfo.addressData.resultInfo.length,
                  '--rightBoxHeight': maxHeight.rightBoxHeight,
                }"
                ref="addressOtherRef"
              >
                <div
                  class="p-text"
                  v-for="(bottomItem, bottomIndex) in currentAlgorithmInfo.addressData.otherInfo"
                  :key="`${bottomIndex}-${bottomItem.key}`"
                >
                  {{ bottomItem.value }}
                </div>
              </div>
            </div>
            <!-- 最右下 --  地址下方的异常信息，如：  地址信息下方非法标注内容 -->
            <div
              class="card-content bottom-content"
              v-if="currentAlgorithmInfo.addressData.addressBottomInfo.length"
              :class="{
                'set-max-height-100':
                  !currentAlgorithmInfo.addressData.resultInfo.length &&
                  !currentAlgorithmInfo.addressData.otherInfo.length,
              }"
              :style="{ '--rightBoxHeight': maxHeight.rightBoxHeight }"
              ref="addresBottomRef"
            >
              <div
                class="p-text"
                v-for="(bottomItem, bottomIndex) in currentAlgorithmInfo.addressData.addressBottomInfo"
                :key="`${bottomIndex}-${bottomItem.key}`"
              >
                {{ bottomItem.value }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <slot
        name="other"
        :currentItem="item"
        :algorithmInfo="currentAlgorithmInfo"
        :showLeftBox="showLeftBox"
        :showRightBox="showRightBox"
      ></slot>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
import { mapGetters } from 'vuex';
import nodataImg from '@/assets/img/common/nodata-img.png';
import nodataImgLight from '@/assets/img/common/nodata-img-light.png';

const leftClass = 'info-view-left';
const rightClass = 'info-view-right';

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    viewIndex: {
      default: 0,
    },
    imgList: {
      type: Array,
      required: true,
    },
    filedNameMap: {
      type: Object,
      default: () => {
        return {
          smallPicName: 'imageUrl', // 小图
          bigPicName: 'imageUrl', // 大图
          resultTip: 'resultTip', // 图片备注
          qualified: 'qualified', // 不合格
        };
      },
    },
  },
  components: {
    uiImage: require('@/components/ui-image.vue').default,
    ClockCard: require('./clock-card.vue').default,
    CameraCard: require('./camera-card.vue').default,
    AddressCard: require('./address-card.vue').default,
  },
  data() {
    return {
      noDataImgUrl: '',
      imgIndex: 0,
      lenFormRight: 0, // 实际渲染图片右边  到  右边框  的距离
      currentAlgorithmInfo: {},
      picBoxWidth: 0, // 渲染图片 .pic-box 实际的宽度
      maxHeight: {
        rightBoxHeight: 0,
        addressOtherRef: 0,
      },
      clockPosition: 'left', //  clock-card组件 位置    left: 左边    right: 右边
      showLeftBox: false,
      showRightBox: false,
      parentElClass: [], // 设置到 el-carousel组件的 class名
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  watch: {
    viewIndex(val) {
      this.imgIndex = val;
      this.$refs.carousel.setActiveItem(this.imgIndex);
      this.changeIndex(val);
    },
    themeType(val) {
      this.noDataImgUrl = val === 'light' ? nodataImgLight : nodataImg;
    },
    visible: {
      handler(val) {
        if (!val) return;
        this.changeIndex(this.viewIndex);
      },
      deep: true,
      immediate: true,
    },
    imgList(val) {
      if (!val || !val.length || !this.visible) return;
      this.$nextTick(() => {
        this.changeIndex(this.viewIndex);
      });
    },
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * 处理 算法信息
     */
    getAlgorithmInfo(item) {
      let obj = {
        cameraData: [], // 图片左下角 - 摄像机
        infoAlgorithmTextList: [], // 左下角- 摄像机- 算法识别到的内容
        clockData: [], // 图片右上角 - 时钟
        middleData: [],
        addressData: {
          allInfo: [],
          resultInfo: [],
          otherInfo: [],
          addressBottomInfo: [],
        }, // 图片右下角 - 地址
        addressAlgorithmTextList: [], // 左下角- 地址信息- 算法识别到的内容
        minMargin: 0, // 指标配置中设置的间距 --  取最小值
      };

      let { timePositionCheckMin, timePositionCheckMax, areaPositionCheckMin, areaPositionCheckMax } =
        item?.configBo || {};

      obj.minMargin = Math.min(
        timePositionCheckMin || 0,
        timePositionCheckMax || 0,
        areaPositionCheckMin || 0,
        areaPositionCheckMax || 0,
      );

      // 处理 算法识别内容
      let ocrTextVos = item?.ocrTextVos || [];
      let infoOcrList = []; // 左下角 - 算法识别内容
      let addressOcrList = []; // 右下角 - 算法识别内容
      ocrTextVos.forEach((orcItem) => {
        let infoOcrVo = orcItem?.infoOcrVo || [];
        if (infoOcrVo.length) {
          infoOcrList.push({
            textList: infoOcrVo.map((vo) => vo.text),
          });
        }
        let addressOcrVo = orcItem?.addressOcrVo || [];
        if (addressOcrVo.length) {
          addressOcrList.push({
            textList: addressOcrVo.map((vo) => vo.text),
          });
        }
      });
      obj.infoAlgorithmTextList = infoOcrList;
      obj.addressAlgorithmTextList = addressOcrList;

      // 图片左下角 - 摄像机信息
      if (item?.deviceInfo?.detectAdditionalErrorMessageMap) {
        let infoMap = item.deviceInfo?.detectAdditionalErrorMessageMap || {};
        Object.keys(infoMap).forEach((infoItem) => {
          obj.cameraData.push({
            key: infoItem,
            value: infoMap[infoItem],
          });
        });
      }

      // 图片右上角 -- 时钟信息
      if (item?.clockDetail?.detectDateErrorMessageMap) {
        let timeMap = item.clockDetail?.detectDateErrorMessageMap || {};
        Object.keys(timeMap).forEach((timeItem) => {
          // 4210: "时钟位置不规范（未右对齐）"
          if (['4210'].includes(timeItem)) {
            obj.middleData.push({
              key: timeItem,
              value: timeMap[timeItem],
            });
          } else {
            let tooltipTextList = [];
            // 4209: 时钟格式错误
            if (timeItem === '4209') {
              tooltipTextList.push({
                key: timeItem,
                textList: ['标准格式：YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样'],
              });
            } else if (timeItem === '4211') {
              // 4211: 与标准时间误差大于Ns
              // 标注时间与视图库抓拍时间不一致
              let clockDetail = item?.clockDetail || null;
              if (clockDetail) {
                tooltipTextList.push({
                  key: timeItem,
                  textList: [
                    `标准时间：${clockDetail.ocrDate || ''}`,
                    `识别时间：${clockDetail.ntpDate || ''}`,
                    `时间误差：${clockDetail.formatClockSkew || ''}`,
                  ],
                });
              }
            }

            obj.clockData.push({
              key: timeItem,
              value: timeMap[timeItem],
              tooltipTextList: tooltipTextList, // 直接显示的提示内容
            });
          }
        });
      }

      // 图片右下角
      if (item?.areaInfo?.detectAreaErrorMessageMap) {
        let addressMap = item.areaInfo?.detectAreaErrorMessageMap || {};
        Object.keys(addressMap).forEach((addressItem) => {
          // 4213: "区划与地址标注位置不规范（未右对齐）"
          if (['4213'].includes(addressItem)) {
            obj.middleData.push({
              key: addressItem,
              value: addressMap[addressItem],
            });
          } else {
            obj.addressData.allInfo.push({
              key: addressItem,
              value: addressMap[addressItem],
            });
            // 特殊处理：渲染到不同 card内容块
            let fieldsName = '';
            if (['4215', '4227', '4228', '4229', '4230', '4231'].includes(addressItem)) {
              // "4215":区划与地址标注内容与档案信息不匹配,   "4227": "【省级】未标注或与档案信息不匹配",   "4228": "【地市级】未标注或与档案信息不匹配",   "4229": "【区县级】未标注或与档案信息不匹配",  "4230": "【队所/派出所】未标注或与组织机构信息表不匹配；",   "4231": "【地址信息】未标注或与档案信息不匹配",
              fieldsName = 'resultInfo';
            } else if (['4225'].includes(addressItem)) {
              // "4225": 地址信息下方非法标注内容
              fieldsName = 'addressBottomInfo';
            } else {
              fieldsName = 'otherInfo';
            }
            obj.addressData[fieldsName].push({
              key: addressItem,
              value: addressMap[addressItem],
            });
          }
        });
      }

      return obj;
    },
    // 设置class类名
    setParentElClassName(val, classNameList, className) {
      if (val) {
        !classNameList.includes(className) ? classNameList.push(className) : '';
      } else {
        let classIndex = classNameList.findIndex((classItem) => classItem === className);
        classIndex !== -1 ? classNameList.splice(classIndex, 1) : '';
      }
    },
    changeIndex(index) {
      // 获取当前算法信息
      this.currentAlgorithmInfo = this.getAlgorithmInfo(this.imgList[index]);

      this.$nextTick(() => {
        this.showLeftBox = true;
        this.showRightBox = true;

        this.clockPosition = 'left';
        let rightHeight = this.$refs.contentRef?.[index]?.clientHeight || 0;
        this.maxHeight.rightBoxHeight = rightHeight;

        this.$nextTick(() => {
          // 1.1 判断 clockCard组件 是渲染到 左边 还是 右边
          let { cameraData, clockData, middleData, addressData } = this.currentAlgorithmInfo;

          let rightTopHeight = this.$refs.rightTopBoxRef?.[index]?.clientHeight || 0;
          let rightBottomHeight = this.$refs.rightBottomBoxRef?.[index]?.clientHeight || 0;
          let clockHeight = this.$refs.clockBoxRef?.[index]?.clientHeight || 0;

          this.maxHeight.addressOtherRef = this.$refs.addressOtherRef?.[index]?.clientHeight || 0;

          if (
            (rightHeight - rightTopHeight - rightBottomHeight - 15 >= clockHeight &&
              (middleData.length || addressData.allInfo.length)) ||
            (!cameraData.length && clockData.length && !middleData.length && !addressData.allInfo.length)
          ) {
            this.clockPosition = 'right';
          } else {
            this.clockPosition = 'left';
          }
          // console.log(rightHeight, rightTopHeight, rightBottomHeight, clockHeight, 56565656, this.clockPosition);

          // 1.2 判断 图片左右两边是否显示
          this.showLeftBox = cameraData.length || (clockData.length && this.clockPosition === 'left');
          this.showRightBox =
            middleData.length || addressData.allInfo.length || (clockData.length && this.clockPosition === 'right');

          // 2. 给el-carousel组件设置动态类名，调整 切换箭头 的位置
          this.setParentElClassName(this.showLeftBox, this.parentElClass, leftClass);
          this.setParentElClassName(this.showRightBox, this.parentElClass, rightClass);
          let el = this.$refs.carousel?.$el || '';
          if (el) {
            this.parentElClass.includes(leftClass) ? el.classList.add(leftClass) : el.classList.remove(leftClass);
            this.parentElClass.includes(rightClass) ? el.classList.add(rightClass) : el.classList.remove(rightClass);
          }

          this.$emit(
            'getAlgorithmInfo',
            { ...this.currentAlgorithmInfo, showLeftBox: this.showLeftBox, showRightBox: this.showRightBox },
            this.imgList[index],
          );
          this.getCurrentImageSize(index);
        });
      });
    },
    // 获取到 渲染图片 相关信息
    getCurrentImageSize(index) {
      if (!this.visible) return;
      this.lenFormRight = 0;
      this.$nextTick(() => {
        let { width, height } = this.$refs.bigImage?.[index]?.imageSize || {};
        if (!width || !height) return;
        // 外壳的宽高
        let parentClientWidth = this.$refs.bigImage[index].$el.clientWidth;
        // let parentClientHeight = this.$refs.bigImage[index].$el.clientHeight;
        let clientWidth = this.$refs.bigImage[index].$refs.imgRef.clientWidth;
        // let clientHeight = clientWidth ? (clientWidth / width) * height : 0;
        // 110 :  图片加载出错后， 自定义的 无大图 或 显示破裂 图标 大概的大小， 这种情况 为0 即可
        this.lenFormRight = clientWidth < 110 ? 0 : (parentClientWidth - clientWidth) / 2;

        this.picBoxWidth = parentClientWidth;

        // console.log(
        //   index,
        //   '实际图片大小：',
        //   width,
        //   height,
        //   '等比例渲染后 大小：',
        //   clientWidth,
        //   // clientHeight,
        //   '容器大小：',
        //   parentClientWidth,
        //   // parentClientHeight,
        //   '渲染后图片右边到边框的距离:',
        //   this.lenFormRight,
        //   666,
        // );
      });
    },
    loadImage() {
      if (!this.visible) return;
      this.getCurrentImageSize(this.viewIndex);
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .p-tip,
  .tooltip-text {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}
.img-list {
  height: 100%;
  padding: 20px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .left-box,
    .right-box {
      width: 290px;
      height: 100%;
      display: flex;
      flex-direction: column;
      position: relative;
      .card-content {
        padding: 8px 10px;
        background: var(--bg-card);
        font-size: 13px;
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        color: var(--color-card);
        line-height: 21px;
      }
      .p-text {
        position: relative;
        padding: 1px 0 1px 18px;
        color: var(--color-failed);
        &::before {
          content: ' ';
          width: 11px;
          height: 11px;
          display: inline-block;
          background: var(--bg-quadrate-outside);
          transform: rotate(45deg);
          position: absolute;
          top: 5px;
          left: 0;
        }
        &::after {
          content: ' ';
          width: 5px;
          height: 5px;
          display: inline-block;
          background: var(--bg-quadrate-interior);
          transform: rotate(45deg);
          position: absolute;
          top: 8px;
          left: 3px;
        }
      }
      .p-tip {
        position: relative;
        padding: 1px 0 1px 18px;
        color: rgba(0, 0, 0, 0.8);
        &::before {
          content: ' ';
          width: 11px;
          height: 11px;
          display: inline-block;
          border-radius: 50%;
          background: var(--bg-circle-outside);
          position: absolute;
          top: 6px;
          left: 0;
        }
        &::after {
          content: ' ';
          width: 5px;
          height: 5px;
          display: inline-block;
          border-radius: 50%;
          background: var(--bg-circle-interior);
          position: absolute;
          top: 9px;
          left: 3px;
        }
      }
    }
    .left-box {
      justify-content: space-between;
      margin-right: 23px;
      .clock-content {
        overflow: hidden;
        overflow-y: auto;
        &::before {
          content: ' ';
          position: absolute;
          left: 50%;
          top: -10px;
          width: 1px;
          height: 10px;
          background: var(--bg-markers);
        }
        &::after {
          content: ' ';
          position: absolute;
          left: 50%;
          top: -10px;
          // --pic-box-width : .pic-box容器的宽度
          width: calc(var(--pic-box-width) * 1px + 60px - var(--imgDistRight) * 1px);
          height: 1px;
          background: var(--bg-markers);
        }
        .line-before-class {
          position: absolute;
          left: calc(var(--pic-box-width) * 1px + 182px - var(--imgDistRight) * 1px);
          top: 0;
          height: 21px;
          &::before {
            content: ' ';
            position: absolute;
            right: -23px;
            top: -10px;
            width: 1px;
            height: 10px;
            background: var(--bg-markers);
          }
          &::after {
            content: ' ';
            position: absolute;
            right: -28px;
            bottom: 16px;
            width: 10px;
            height: 10px;
            background: var(--bg-markers);
            border-radius: 50%;
            z-index: 100;
          }
        }
      }
      .left-bottom-content {
        overflow: hidden;
        overflow-y: auto;
        margin-top: 10px;
        &::before {
          content: ' ';
          position: absolute;
          right: -23px;
          bottom: 20px;
          width: 23px;
          height: 1px;
          background: var(--bg-markers);
        }
        &::after {
          content: ' ';
          position: absolute;
          right: -29px;
          bottom: 16px;
          width: 10px;
          height: 10px;
          background: var(--bg-markers);
          border-radius: 50%;
          z-index: 100;
        }
      }
    }
    .right-box {
      justify-content: space-between;
      margin-left: 23px;
      & > div {
        position: relative;
        &.right-top-div {
          max-height: 15%;
          &.set-max-height-100 {
            max-height: 100%;
          }
          &.set-max-height-0 {
            max-height: 0;
          }
        }
        &.other-content {
          max-height: 84%;
          display: flex;
          flex-direction: column;
          &.set-max-height-100 {
            max-height: 100%;
          }
          &.set-max-height-0 {
            max-height: 0;
          }
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
        }
      }
      .clock-content {
        overflow: hidden;
        overflow-y: auto;
        &::before {
          content: ' ';
          position: absolute;
          left: 50%;
          top: -10px;
          width: 1px;
          height: 10px;
          background: var(--bg-markers);
        }
        &::after {
          content: ' ';
          position: absolute;
          right: 50%;
          top: -10px;
          width: calc(278px + var(--imgDistRight) * 1px);
          height: 1px;
          background: var(--bg-markers);
        }
        .line-before-class {
          position: absolute;
          left: -110px;
          top: 0;
          height: 21px;
          &::before {
            content: ' ';
            position: absolute;
            left: calc(-23px - var(--imgDistRight) * 1px);
            top: -10px;
            width: 1px;
            height: 10px;
            background: var(--bg-markers);
          }
          &::after {
            content: ' ';
            position: absolute;
            left: calc(-28px - var(--imgDistRight) * 1px);
            bottom: 16px;
            width: 10px;
            height: 10px;
            background: var(--bg-markers);
            border-radius: 50%;
            z-index: 100;
          }
        }
      }
      .middle-content {
        max-height: 100%;
        overflow: hidden;
        overflow-y: auto;
        &::before {
          content: ' ';
          position: absolute;
          left: calc(var(--bottom-line-margin) * (-1px) - 28px);
          top: 20%;
          width: calc(var(--bottom-line-margin) * 1px + 28px);
          height: 1px;
          background: var(--bg-markers);
          z-index: 10;
        }
        &::after {
          content: ' ';
          position: absolute;
          left: calc(var(--bottom-line-margin) * (-1px) - 30px);
          top: 16px;
          width: 10px;
          height: 10px;
          background: var(--bg-markers);
          border-radius: 50%;
          z-index: 10;
        }
      }
      .address-content {
        margin-top: 10px;
        // max-height: 79%;
        max-height: calc(100% - var(--bottom-other-content-height) * 1px);

        overflow: hidden;
        overflow-y: auto;
        &::before {
          content: ' ';
          position: absolute;
          left: -11px;
          // .bottom-other-content的高 +  margin-top  +  距离.address-content底部的高度
          bottom: calc(var(--bottom-other-content-height) * 1px + 10px + 40px);
          width: 11px;
          height: 1px;
          background: var(--bg-markers);
        }
        &::after {
          content: ' ';
          position: absolute;
          left: -11px;
          bottom: 20px;
          width: 1px;
          // .bottom-other-content的高 +  margin-top  +  距离.address-content底部的高度  -  自身bottom
          height: calc(var(--bottom-other-content-height) * 1px + 10px + 40px - 20px);
          background: var(--bg-markers);
          z-index: 100;
        }
      }
      .bottom-other-content {
        margin-top: 10px;
        // max-height: 21%;
        max-height: calc(var(--rightBoxHeight) * 0.84 * 0.21 * 1px);

        &.set-max-height-100 {
          max-height: 100%;
        }
        overflow: hidden;
        overflow-y: auto;
        &::before {
          content: ' ';
          position: absolute;
          left: -11px;
          bottom: 20px;
          width: 11px;
          height: 1px;
          background: var(--bg-markers);
        }
      }
      .bottom-content {
        margin-top: 10px;
        // max-height: 15%;
        max-height: calc(var(--rightBoxHeight) * 0.84 * 0.15 * 1px);
        &.set-max-height-100 {
          max-height: 100%;
        }
        overflow: hidden;
        overflow-y: auto;
        &::before {
          content: ' ';
          position: absolute;
          left: -23px;
          bottom: 15px;
          width: 23px;
          height: 1px;
          background: var(--bg-markers);
        }
        &::after {
          content: ' ';
          position: absolute;
          left: -28px;
          bottom: 10px;
          width: 10px;
          height: 10px;
          background: var(--bg-markers);
          border-radius: 50%;
          z-index: 100;
        }
      }
      .all-address-content {
        position: relative;
        // max-height: 84%;
        max-height: calc(var(--rightBoxHeight) * 0.84 * 0.84 * 1px);
        &.set-max-height-100 {
          max-height: 100%;
        }
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        .right-bottom-line {
          position: absolute;
          bottom: 10px;
          left: 0;
          &::before {
            content: ' ';
            position: absolute;
            left: -23px;
            bottom: 10px;
            width: 12px;
            height: 1px;
            background: var(--bg-markers);
          }
          &::after {
            content: ' ';
            position: absolute;
            left: -28px;
            bottom: 5px;
            width: 10px;
            height: 10px;
            background: var(--bg-markers);
            border-radius: 50%;
            z-index: 100;
          }
        }
      }
    }
    .pic-box {
      flex: 1;
      height: 100%;
      border: 1px solid var(--border-color);
      position: relative;
      &.set-line::after {
        content: '';
        height: 100%;
        width: 1px;
        background-color: var(--bg-markers);
        position: absolute;
        top: 0;
        right: calc(var(--bottom-line-margin) * 1px);
        z-index: 10;
      }
      &.no-img-data {
        .ui-image {
          width: fit-content;
          height: fit-content;
        }
        .none {
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        }
      }
    }
    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
    .none {
      height: 650px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  @{_deep} .el-carousel__container {
    height: 100%;
  }
  @{_deep} .el-carousel__item {
    overflow: visible !important;
  }
}

.info-view-left {
  @{_deep}.el-carousel__arrow--left {
    left: 320px !important;
  }
}
.info-view-right {
  @{_deep}.el-carousel__arrow--right {
    right: 320px !important;
  }
}
.tooltip-text {
  white-space: pre-line;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.8);
}
.card-content {
  color: var(--color-content);
  .p-label {
    .span-label {
      white-space: pre-wrap;
    }
  }
  .text-title {
    padding: 1px 0;
    color: var(--color-title);
  }
  .result {
    color: var(--color-failed);
    white-space: pre-wrap;
  }
}
.sub-title {
  color: var(--color-sub-title);
}
@{_deep} .icon-warning {
  background: var(--bg-wenhao) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}
</style>
