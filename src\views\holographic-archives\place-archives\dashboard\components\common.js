/*
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-07-10 14:25:37
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-22 16:47:06
 * @Description:
 */
//车身类型
export const vehicleClassTypeList = [
  {
    dataValue: "客车",
    value: "vehicleClassType_k",
    type: "vehicleClassTypeKList",
  },
  {
    dataValue: "货车",
    value: "vehicleClassType_h",
    type: "vehicleClassTypeHList",
  },
  {
    dataValue: "牵引车",
    value: "vehicleClassType_q",
    type: "vehicleClassTypeQList",
  },
  {
    dataValue: "专项作业车",
    value: "vehicleClassType_z",
    type: "vehicleClassTypeZList",
  },
  {
    dataValue: "电车",
    value: "vehicleClassType_d",
    type: "vehicleClassTypeDList",
  },
  {
    dataValue: "摩托车",
    value: "vehicleClassType_m",
    type: "vehicleClassTypeMList",
  },
  {
    dataValue: "三轮汽车",
    value: "vehicleClassType_n",
    type: "vehicleClassTypeNList",
  },
  {
    dataValue: "拖拉机",
    value: "vehicleClassType_t",
    type: "vehicleClassTypeTList",
  },
  {
    dataValue: "轮式机械",
    value: "vehicleClassType_j",
    type: "vehicleClassTypeJList",
  },
  {
    dataValue: "全挂车",
    value: "vehicleClassType_g",
    type: "vehicleClassTypeGList",
  },
  {
    dataValue: "半挂车",
    value: "vehicleClassType_b",
    type: "vehicleClassTypeBList",
  },
  {
    dataValue: "其他",
    value: "vehicleClassType_x",
    type: "vehicleClassTypeXList",
  },
  {
    dataValue: "未知",
    value: "vehicleClassType_unknow", // 后端无法识别的类型
    type: "",
  },
];

// 场所围栏 - 根据dictionary/placeFirstLevelList
export const placeFenceType = [
  {
    code: "67021",
    name: "购物服务",
    color: "#F37A7A",
    layerType: "placeShop",
  },
  {
    code: "67012",
    name: "教育培训",
    color: "#1FAF8A",
    layerType: "placeEdu",
  },
  {
    code: "67011",
    name: "政府机构",
    color: "#2C86F8",
    layerType: "placeGovernment",
  },
  {
    code: "67010",
    name: "房产小区",
    color: "#2AB2F4",
    layerType: "placeHouse",
  },
  {
    code: "67008",
    name: "医疗保健",
    color: "#EA4A36",
    layerType: "placeMedical",
  },
  {
    code: "67005",
    name: "公司企业",
    color: "#8C80FB",
    layerType: "placeCompany",
  },
  {
    code: "67004",
    name: "住宿服务",
    color: "#C832DC",
    layerType: "placeHotel",
  },
  {
    code: "67002",
    name: "交通设施",
    color: "#D82B84",
    layerType: "placeTraffic",
  },
];
