<template>
  <common-form v-bind="$props" ref="formData">
    <template #indexDefinition>
      <div class="base-text-color">
        <p>1、对于省/直辖市：设备数量达标率=（该单位实际建设数量/达标数量）*（达标地市数/实有地市数)</p>
        <p>2、对于市：设备数量达标率=（该单位实际建设数量/达标数量）*（达标区县数/实有区县数）</p>
        <p>
          全量目录完整率=下一级已上报组织机构数/实有下一级组织机构数 * 下二级已上报组织机构数/实有下二级组织机构数。
        </p>
        <p>
          3、对于省/直辖市：设备数量达标率=（该单位实际建设数量/达标数量）*（达标地市数/实有地市数) *
          (达标区县数/实有区县数）
        </p>
      </div>
    </template>
  </common-form>
</template>
<script>
export default {
  name: 'basic-full-dir',
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {};
  },
  methods: {
    async validate() {
      try {
        return await this.$refs.formData.validate();
      } catch (error) {
        throw new Error(error);
      }
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
  },
};
</script>
<style scoped lang="less">
@{_deep} .evaluation-criterion {
  width: 926px !important;
}
</style>
