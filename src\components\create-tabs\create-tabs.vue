<template>
  <div @click="create">
    <slot></slot>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    componentName: {
      type: String,
      required: true,
    },
    tabsText: {
      type: String,
      required: true,
    },
    // 自定义标签页名称,不传就是默认 -
    importantTabName: {
      type: String,
    },
    // 标签页参数，用来刷新页面时重新获取数据
    tabsQuery: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
    }),
    create() {
      // 点位信息采集APP 不需要跳转路由，特殊处理
      if (this.componentName == 'app') {
        this.$emit('selectModule', this.componentName);
        return;
      }

      this.createTabs(this.componentName);
    },
    createTabs(name) {
      // 创建组件标签命名为此组件名字
      const cacheRouterName = `${name}`;
      // 判断缓存路由中是否已经存在要创建的组件标签
      const index = this.cacheRouterList.findIndex((row) => row.name === cacheRouterName);
      /**
       * 如果是打开的标签页组件
       * 组件名称参数应为 componentName 已经存在的组件名称-要打开的组件名称
       */
      let existComponent = this.$route.query.componentName ? this.$route.query.componentName : null;
      let componentName = existComponent ? existComponent + '-' + name : name;
      /**
       * 如果没有要创建的组件标签
       * name：此模块的name名字应不与所有菜单重名，为组件名称
       * path：当前路由地址path
       * text：tabs中显示的中文名称
       * meta: {
       *  componentName：此组件名称
       *  routeName: 此组件所在路由
       *  queryParams: 要传入新标签的额外参数
       * }
       */
      let router = null;
      if (this.$route.query.componentName) {
        router = this.cacheRouterList.find(
          (row) => !!row.meta && row.meta.componentName === this.$route.query.componentName,
        );
      } else {
        router = this.cacheRouterList.find((row) => row.name === this.$route.name);
      }
      let tabName = `${router.text}-${this.tabsText}`;
      this.importantTabName ? (tabName = this.importantTabName) : null;
      if (index === -1) {
        this.cacheRouterList.push({
          name: cacheRouterName,
          path: this.$route.path,
          text: tabName,
          meta: {
            componentName: componentName,
            routeName: this.$route.name,
            queryParams: this.tabsQuery,
          },
        });
        this.setCacheRouterList(this.cacheRouterList);
      } else {
        let list = {
          name: cacheRouterName,
          path: this.$route.path,
          text: tabName,
          meta: {
            componentName: componentName,
            routeName: this.$route.name,
            queryParams: this.tabsQuery,
          },
        };
        this.cacheRouterList.splice(index, 1, list);
        this.setCacheRouterList(this.cacheRouterList);
      }
      // 创建组件标签后跳转路由，并且路由中应带入此组件名称参数
      this.$router.push({
        name: this.$route.name,
        query: Object.assign(this.tabsQuery, { componentName: componentName }),
      });
      this.$emit('selectModule', name);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
  },
  components: {},
};
</script>
<style lang="less" scoped></style>
