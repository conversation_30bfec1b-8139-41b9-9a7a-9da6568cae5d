<template>
  <!-- 轨迹准确率弹框 -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-if="!!trackList && trackList.detectionAmount != 0">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <!-- <line-title title-name="轨迹准确性存疑列表"></line-title> -->
        <line-title title-name="轨迹准确性存疑列表">
          <template slot="content">
            <slot name="content">
              <tagView class="tagView fr" ref="tagView" :list="['图像模式', '聚档模式']" @tagChange="tagChange" />
            </slot>
          </template>
        </line-title>
        <div class="list auto-fill">
          <!-- <table-data class="table-data" :tableData="tableData"></table-data> -->
          <!-- <ui-table
            v-if="modelTag === 0"
            class="ui-table auto-fill"
            :table-columns="tableColumns"
            :table-data="tableData"
            :loadin="loading"
          >
            <template #trackImage="{ row }">
              <img
                @click="viewBigPic(row)"
                :src="!row.trackImage ? noImg : row.trackImage"
                alt=""
              />
            </template>
            <template #algResult="{ row }">
              <span v-if="row.algResult">
                <div
                  v-for="(item, index) in JSON.parse(row.algResult)"
                  :key="index"
                >
                  <span>{{ item.algorithmType }}:</span>
                  <span>{{ item.score }}%</span>
                </div>
              </span>
              <span v-else>--</span>
            </template>
            <template #identityPhoto="{ row }">
              <img
                @click="viewBig(row)"
                :src="!row.identityPhoto ? noImg : row.identityPhoto"
                alt=""
              />
            </template>
            <template #unableDetectTip="{ row }">
              <span :class="row.synthesisResult === '1' ? '' : 'color-failed'">{{
                row.unableDetectTip
              }}</span>
            </template>
          </ui-table>
          <loading v-if="loading"></loading>
        </div>
        <ui-page
          class="page"
          :page-data="searchData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page> -->
          <TableList
            v-if="modelTag == 0 && tableColumns.length > 0"
            ref="infoList"
            :columns="tableColumns"
            :loadData="loadDataList"
          >
            <template #trackImage="{ row }">
              <img @click="viewBigPic(row)" :src="!row.trackImage ? noImg : row.trackImage" alt="" />
            </template>
            <template #algResult="{ row }">
              <span v-if="row.algResult">
                <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
                  <span>{{ item.algorithmType }}:</span>
                  <span>{{ item.score }}%</span>
                </div>
              </span>
              <span v-else>--</span>
            </template>
            <template #identityPhoto="{ row }">
              <img @click="viewBig(row)" :src="!row.identityPhoto ? noImg : row.identityPhoto" alt="" />
            </template>
            <template #unableDetectTip="{ row }">
              <span :class="row.synthesisResult === '1' ? '' : 'color-failed'">{{ row.unableDetectTip }}</span>
            </template>
          </TableList>
          <TableCard ref="infoCard" :loadData="loadPageList" :cardInfo="cardInfo" v-if="modelTag === 1">
            <!-- 卡片 -->
            <template #card="{ row }">
              <UiGatherCard
                class="card"
                :list="row"
                :personTypeList="personTypeList"
                :cardInfo="cardInfo"
                @detail="detailInfo(row)"
              ></UiGatherCard>
            </template>
          </TableCard>
        </div>
        <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
        <captureDetail ref="captureDetail" :taskObj="taskObj" />
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .card {
      width: calc(calc(100% - 40px) / 4);
      margin: 0 5px 10px;
    }
    .list {
      position: relative;
      margin-top: 10px;
      //   height: 450px;
      //   overflow-y: auto;

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      img {
        width: 56px !important;
        height: 56px !important;
      }
      @{_deep}.ivu-table-cell-slot {
        margin-top: 10px;
      }
    }
  }
}
</style>
<script>
import { mapActions, mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import inspectionrecord from '@/config/api/inspectionrecord';
import imgSrc from '@/assets/img/load-error-img.png';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      noImg: imgSrc,
      echartRing: {},
      ringStyle: {
        width: '650px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['轨迹图像准确图像', '轨迹准确存疑图像'],
        showData: [
          { name: '轨迹图像准确图像', value: 0 },
          { name: '轨迹准确存疑图像', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '轨迹准确率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '轨迹图像',
          key: 'trackImage',
          slot: 'trackImage',
          tooltip: true,
          minWidth: 150,
        },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '证件照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
        {
          title: '算法识别结果',
          key: 'algResult',
          slot: 'algResult',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '检测结果',
          key: 'unableDetectTip',
          slot: 'unableDetectTip',
          tooltip: true,
          width: 300,
        },
        {
          title: '综合判定',
          key: 'synthesisResultDesc',
          tooltip: true,
          minWidth: 150,
        },

        // { title: "操作", slot: "option" },
      ],
      tableData: [],
      minusTable: 600,
      searchData: {},
      styles: {
        width: '9rem',
      },
      visible: true,
      loading: false,
      bigPictureShow: false,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},
      taskObj: {
        rootId: this.$parent.row.resultId,
        taskId: this.$route.query.id,
      },
      modelTag: 0, // 聚档模式,图像模式
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'total' },
        { name: '异常轨迹：', value: 'abnormal', color: '#BC3C19' },
      ],
      loadDataList: (parameter) => {
        return this.$http
          .post(
            governanceevaluation.getPageTrackDetails,
            Object.assign(parameter, {
              resultId: this.$parent.row.resultId,
              indexId: this.$parent.row.indexId,
            }),
          )
          .then((res) => {
            return res.data;
          });
      },
      loadPageList: (parameter) => {
        return this.$http
          .post(
            inspectionrecord.pageListGroup,
            Object.assign(parameter, {
              lastResultId: this.$parent.row.resultId,
              taskId: this.$route.query.id,
            }),
          )
          .then((res) => {
            return res.data;
          });
      },
    };
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
    await this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    detailInfo(info) {
      this.$refs.captureDetail.show(1, info);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
    viewBigPic(item) {
      if (!item.trackLargeImage) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item.trackLargeImage];
      this.bigPictureShow = true;
    },
    viewBig(item) {
      this.imgList = [item.identityPhoto];
      this.bigPictureShow = true;
    },
    // 列表
    async init() {
      // let data = {
      //   resultId: this.$parent.row.resultId,
      //   indexId: this.$parent.row.indexId,
      //   pageSize: this.searchData.pageSize,
      //   pageNumber: this.searchData.pageNum,
      // }
      // try {
      //   this.loading = true
      //   this.tableData = []
      //   let res = await this.$http.post(governanceevaluation.getPageTrackDetails, data)
      //   this.tableData = res.data.data.entities
      //   this.searchData.totalCount = res.data.data.total
      //   this.loading = false
      // } catch (err) {
      //   console.log(err)
      //   this.loading = false
      // } finally {
      //   this.loading = false
      // }
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.get(governanceevaluation.getTrackByResultId, {
          params: {
            resultId: this.$parent.row.resultId,
            indexId: this.$parent.row.indexId,
          },
        });
        this.trackList = res.data.data;
        this.moduleData.rateValue = this.trackList.precisionRate || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '轨迹图像准确图像') {
          item.value = this.trackList.qualifiedAmount;
        } else {
          item.value = this.trackList.impeachAmount;
        }
      });
      this.zdryChartObj.count = this.trackList.detectionAmount;
      let formatData = {
        seriesName: '检测轨迹图像',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    tagChange(val) {
      if (this.modelTag == val) {
        return;
      }
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    // UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    tagView: require('./components/tags.vue').default,
    TableCard: require('./components/tableCard.vue').default,
    TableList: require('./components/tableList.vue').default,
    UiGatherCard: require('./components/ui-gather-card.vue').default,
    captureDetail:
      require('@/views/governanceevaluation/inspectionrecord/keypersonlibrary/component/capture-detail.vue').default,
  },
};
</script>
