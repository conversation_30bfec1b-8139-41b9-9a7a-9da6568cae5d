<template>
  <div class="equipmentlibrary auto-fill">
    <div class="config" @click="configFn()">
      <i class="icon-font icon-jichushuxing"></i>
    </div>
    <!--左侧抽屉 树-->
    <!--<slide-unit-tree @selectOrgCode="selectOrgCode" :select-key="selectKey"/>-->
    <!-- 顶部统计 -->
    <chartsContainer :abnormalCount="countList" class="charts" />
    <!--搜索参数-->
    <div class="search-module">
      <div>
        <ui-label class="inline mr-md" label="组织机构" :width="65">
          <api-organization-tree
            class="tree-style"
            :select-tree="selectOrgTree"
            :custorm-node="true"
            :custorm-node-data="custormNodeData"
            @selectedTree="selectedOrgTree1"
            placeholder="请选择组织机构"
          />
        </ui-label>
        <ui-label class="inline mr-md" :label="global.filedEnum.deviceId" :width="65">
          <Input v-model="searchData.deviceId" class="width-md" :placeholder="global.filedEnum.deviceId" />
        </ui-label>
        <ui-label class="inline mr-md" :label="global.filedEnum.deviceName" :width="65">
          <Input v-model="searchData.deviceName" class="width-md" :placeholder="global.filedEnum.deviceName" />
        </ui-label>
        <!-- <ui-label class="inline mr-md" label="数据来源" :width="65">
          <Input
            class="width-md"
            v-model="searchData.sourceId"
            placeholder="请输入数据来源"
          ></Input>
        </ui-label> -->
        <ui-label label="检测状态" :width="65" class="inline mr-md">
          <Select
            class="width-sm"
            v-model="searchData.checkStatus"
            placeholder="请选择检测状态"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in checkStatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>

        <ui-label label="建档状态" :width="65" class="inline mr-md">
          <Select
            class="width-md"
            v-model="searchData.buildStatus"
            placeholder="请选择建档状态"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in buildStatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label :label="global.filedEnum.sbdwlx" :width="90" class="inline mr-md">
          <Select
            class="width-md"
            v-model="searchData.sbdwlx"
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label :label="global.filedEnum.sbgnlx" :width="110" class="inline mr-md">
          <Select
            class="width-md"
            v-model="searchData.sbgnlx"
            :placeholder="`请选择${global.filedEnum.sbgnlx}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-md" label="行政区划" :width="65">
          <api-area-tree
            :select-tree="selectTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
        <ui-label :label="global.filedEnum.phyStatus" :width="65" class="inline mr-md" style="margin-top: 12px">
          <Select
            class="width-sm"
            v-model="searchData.deviceStatus"
            :placeholder="`请选择${this.global.filedEnum.phyStatus}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-md" label="接收时间" :width="65">
          <DatePicker
            class="width-md"
            v-model="searchData.startReceiveTime"
            type="datetime"
            placeholder="请选择开始时间"
            :options="startTimeOption"
            confirm
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="width-md"
            v-model="searchData.endReceiveTime"
            type="datetime"
            placeholder="请选择结束时间"
            :options="endTimeOption"
            confirm
          ></DatePicker>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" @click="searchHandle">查询</Button>
          <Button class="ml-sm" @click="resetHandle">重置</Button>
        </div>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="operation">
      <Checkbox v-model="allCheck">全选</Checkbox>
      <div class="right-btn fr">
        <Button type="primary" @click="batchOperateHandle(false, 'Check')"
          ><i class="btn icon-font icon-piliangjiance"></i>批量检测
        </Button>
        <Button type="primary" @click="batchOperateHandle(false, 'File')"
          ><i class="btn icon-font icon-piliangjiandang"></i>批量建档
        </Button>
        <Button type="primary" @click="batchOperateHandle(false, 'Issued')"
          ><i class="btn icon-font icon-piliangxiafa"></i>批量下发
        </Button>
        <Button type="primary" @click="issueRecordHandle()"
          ><i class="btn icon-font icon-xiafajilu"></i>下发记录</Button
        >
      </div>
    </div>
    <!-- 表格 -->
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selected"
      >
        <template #deviceId="{ row }">
          <span
            v-if="row.buildStatus == '1'"
            class="font-active-color pointer device-id"
            :class="row.rowClass"
            @click="deviceArchives(row)"
            >{{ row.deviceId }}</span
          >
          <span v-else :class="row.rowClass">{{ row.deviceId }}</span>
        </template>
        <template #deviceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
            {{ row.deviceName }}
          </div>
        </template>
        <template #address="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.address">
            {{ row.address }}
          </div>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #orgName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
            {{ row.orgName }}
          </div>
        </template>
        <template #deviceStatus="{ row }">
          <div>
            <span class="build" :class="[row.deviceStatus === '1' ? 'font-green' : 'font-red']">
              {{ row.deviceStatus | filterType(phystatusList) }}
            </span>
          </div>
        </template>
        <template #checkStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="[row.checkStatus === '0' ? 'bg-failed' : '', row.checkStatus === '1' ? 'bg-success' : '']"
            >
              {{ handleCheckStatus(row.checkStatus) }}
            </span>
          </div>
        </template>
        <template #buildStatus="{ row }">
          <div>
            <span class="build" :class="[row.buildStatus === '0' ? 'font-red' : 'font-green']">
              {{ row.buildStatus === '1' ? '已建档' : '未建档' }}
            </span>
          </div>
        </template>
        <template #azsj="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.azsj">
            {{ row.azsj }}
          </div>
        </template>
        <template #ipAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
            {{ row.ipAddr }}
          </div>
        </template>
        <template #sbgnlx="{ row }">
          <span>{{ row.sbgnlx | filterType(propertySearchLbgnlx) }}</span>
        </template>
        <template #macAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
            {{ row.macAddr }}
          </div>
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              :styles="{ color: '#269F26  ', 'font-size': '14px' }"
              class="mr-md"
              icon="icon-piliangxiafa"
              content="下发处理"
              @click.native="batchOperateHandle(row, 'Issued')"
            />
            <ui-btn-tip
              style="color: #269f26"
              class="mr-md"
              v-if="row.checkStatus === '1'"
              :icon="row.buildStatus == '1' ? 'icon-conggongxiangkugengxin' : 'icon-shebeidangan'"
              :content="row.buildStatus == '1' ? '建档更新' : '设备建档'"
              @click.native="batchOperateHandle(row, 'File')"
            />
            <ui-btn-tip
              :styles="{ color: '#438CFF', 'font-size': '14px' }"
              class="mr-md"
              icon="icon-chakanxiangqing"
              content="信息详情"
              @click.native="getDetailHandle(row)"
            />
            <ui-btn-tip
              :styles="{ color: '#DE990F ', 'font-size': '14px' }"
              class="mr-md"
              v-if="row.checkStatus === '0'"
              icon="icon-yichangyuanyin"
              content="异常原因"
              @handleClick="viewRecord(row)"
            />
            <ui-btn-tip
              :styles="{ color: '#CF3939 ', 'font-size': '14px' }"
              class="mr-md"
              icon="icon-piliangshanchu"
              content="删除"
              @handleClick="deleteHandle(row)"
            />
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize" />
    </div>
    <!--详情-->
    <device-detail
      v-model="deviceDetailShow"
      :choosed-org="choosedOrg"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :unqualified="deviceUnqualified"
      @update="searchHandle"
    />
    <!--异常原因-->
    <view-detection-field ref="unqualifiedModel" v-model="unqualifiedVisible" />
    <!-- 下发记录 -->
    <issue-record
      v-model="issueRecordShow"
      :view-data="issueRecordData"
      :need-option="true"
      @recordModalShow="getDetailHandle"
    />
    <!-- 信息详情 -->
    <info-detail ref="InfoDetail" v-model="InfoDetailShow" :view-data="InfoDetailData" />
    <!-- 配置 -->
    <config-page ref="config" v-model="configShow" />
  </div>
</template>
<script>
import taganalysis from '@/config/api/taganalysis';
import cascade from '@/config/api/cascade';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'cascade',
  components: {
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    apiOrganizationTree: require('@/api-components/api-organization-tree').default,
    UiTable: require('@/components/ui-table.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    chartsContainer: require('./chartsContainer').default,
    ViewDetectionField: require('./dialog/view-detection-field.vue').default,
    issueRecord: require('./dialog/issue-record.vue').default,
    configPage: require('./config/config-page.vue').default,
    InfoDetail: require('./dialog/info-detail.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
    isRepeatRecord() {
      return this.activeKey === 5;
    },
  },
  data() {
    return {
      allCheck: false,
      loading: false,
      exportDataLoading: false,
      unqualifiedVisible: false,
      selectKey: '0',
      deviceUnqualified: null,
      selectTree: {
        regionCode: '',
      },
      selectOrgTree: {
        orgCode: '',
      },
      searchData: {
        buildStatus: '',
        checkStatus: '',
        civilCode: '',
        orgCode: '',
        configOrgCode: '',
        deviceId: '',
        deviceName: '',
        deviceStatus: '',
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
        sbdwlx: '',
        sbgnlx: '',
        searchValue: '',
        startReceiveTime: '',
        endReceiveTime: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      checkStatusList: [
        { dataKey: '0', dataValue: '异常' },
        { dataKey: '1', dataValue: '合格' },
      ],
      buildStatusList: [
        { dataKey: '1', dataValue: '已建档' },
        { dataKey: '0', dataValue: '未建档' },
      ],
      deviceStatusList: [
        { dataKey: '1', dataValue: '可用' },
        { dataKey: '2', dataValue: '不可用' },
      ],
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      choosedOrg: {},
      tableData: [],
      selectTable: [],
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true /*slot: 'deviceName'*/,
        },
        {
          minWidth: 120,
          title: '组织机构',
          key: 'gldwName',
          align: 'left',
          tooltip: true /*// slot: 'orgName',*/,
        },
        {
          minWidth: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true /* // slot: 'ipAddr',*/,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true /* // slot: 'macAddr',*/,
        },
        // { minWidth: 120, title: '设备功能类型', slot: 'sbgnlx', tooltip: true },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          align: 'left',
        },
        {
          minWidth: 100,
          title: '采集区域',
          key: 'sbcjqyText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: this.global.filedEnum.phyStatus,
          slot: 'deviceStatus',
        },
        { width: 90, title: '检测状态', slot: 'checkStatus', align: 'left' },
        { width: 90, title: '建档状态', slot: 'buildStatus', align: 'left' },
        {
          width: 150,
          title: '档案更新时间',
          key: 'recentlyBuildTime',
          align: 'left',
        },
        {
          width: 150,
          title: '首次接收时间',
          key: 'firstReceiveTime',
          align: 'left',
        },
        { width: 150, title: '首次检测时间', key: 'fristCheckTime' },
        { width: 150, title: '创建时间', key: 'createTime' },
        {
          width: 160,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        }, // className 操作栏列-单元格padding设置
      ],
      deviceDetailShow: false,
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      viewDeviceId: 0,
      customFilter: false,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: `3.125rem`,
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
      countList: [
        { title: '已接收总量', count: '0', icon: 'icon-equipmentlibrary' },
        { title: '已建档数量', count: '0', icon: 'icon-yijiandangshuliang' },
        {
          title: '最新检测成功数量',
          count: '0',
          icon: 'icon-zuixinjiancechenggongshuliang',
        },
        {
          title: '最新检测失败数量',
          countKey: '0',
          icon: 'icon-zuixinjianceshibaishuliang',
        },
        {
          title: '最新下发数量',
          countKey: '0',
          icon: 'icon-zuixinxiafashuliang',
        },
      ],
      recordShow: false,
      recordData: {},
      deviceCode: '',
      issueRecordShow: false, // 下发记录
      issueRecordData: {},
      configShow: false, // 配置
      InfoDetailShow: false, // 信息详情
      InfoDetailData: {},
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endReceiveTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startReceiveTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    this.init();
    this.queryDeviceCount();
  },
  mounted() {
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
  },
  activated() {
    this.getPropertyList();
    // this.queryDeviceCount()
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {};
        this.copySearchDataMx(this.searchData);
        Object.assign(params, this.searchData);
        let res = await this.$http.post(cascade.deviceDetailList, params);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities || [];
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
    // 获取头部统计信息
    async queryDeviceCount() {
      try {
        let res = await this.$http.post(cascade.queryReceiveDeviceStatistics, this.searchData);
        let obj = res.data.data;
        this.countList[0].count = obj.deviceTotal;
        this.countList[1].count = obj.fileBuildTotal;
        this.countList[2].count = obj.lastCheckSuccessTotal;
        this.countList[3].count = obj.lastCheckFailedTotal;
        this.countList[4].count = obj.lastDistributeTotal;
      } catch (err) {
        console.error('err', err);
      } finally {
        // this.loading = false
      }
    },
    searchHandle() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
      this.queryDeviceCount();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.params.pageSize = val;
      this.searchHandle();
    },
    // 配置
    configFn() {
      this.$refs.config.show();
    },
    // table选中方法
    selected(selection) {
      this.selectTable = selection;
    },
    //信息详情
    getDetailHandle(item) {
      this.$nextTick(() => {
        this.$refs.InfoDetail.show(item);
        this.InfoDetailShow = true;
        this.InfoDetailData = item;
      });
    },

    // 批量操作方法  批量建档、批量检测、批量下发
    batchOperateHandle(row, key) {
      let operateName = '';
      switch (key) {
        case 'Check':
          operateName = '检测';
          break;
        case 'File':
          operateName = '建档';
          break;
        case 'Issued':
          operateName = '下发';
          break;
      }
      let resultFormData = {};
      let selectedDataLen = this.selectTable.length;
      let content = '';
      let title = '批量' + operateName;
      // 批量建档、
      if (selectedDataLen === 0) {
        if (!this.allCheck && !row) {
          this.$Message.warning('请勾选全选按钮');
          return false;
        }
        resultFormData = this.searchData;
        content = `确定按照搜索条件进行批量【${operateName}】操作吗？`;
      } else {
        content = `已选择${row ? 1 : selectedDataLen}条设备，确定进行【${operateName}】操作吗？`;
        resultFormData.deviceIds = this.selectTable.map((item) => item.deviceId);
      }
      // 单独建档
      if (row) {
        content = `确定将该设备进行【${operateName}】操作吗？`;
        title = `设备${operateName}`;
        resultFormData = {};
        resultFormData.deviceIds = [row.deviceId];
      }
      this.$UiConfirm({
        content,
        title,
      })
        .then(() => {
          this.$http.post(cascade['deviceDetailBatch' + key], resultFormData).then((res) => {
            this.$Message.success(res.data.msg);
            this.selectTable = [];
            this.allCheck = false;
            this.init();
          });
        })
        .catch((res) => {
          console.error(res);
        });
    },
    // 下发记录
    issueRecordHandle() {
      this.issueRecordShow = true;
      this.issueRecordData = {};
    },
    // 删除
    deleteHandle(row) {
      this.$UiConfirm({
        content: '您要确定要删除吗？',
        title: '提示',
      })
        .then(() => {
          this.$http.delete(cascade.deviceDetailRemove + row.id).then((res) => {
            this.$Message.success(res.data.msg);
            this.init();
          });
        })
        .catch((res) => {
          console.error(res);
        });
    },
    // 选择组织机构
    selectedOrgTree1(val) {
      this.searchData.orgCode = val.orgCode;
      this.searchData.configOrgCode = val.orgCode;
      this.queryDeviceCount();
    },
    // 选择行政区划
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // 选择左侧抽屉组织机构
    selectOrgCode(data) {
      this.searchData.orgCode = data.orgCode;
      this.choosedOrg = data;
      this.init();
      this.queryDeviceCount();
    },
    handleCheckStatus(row) {
      const flag = {
        0: '异常',
        1: '合格',
      };
      return flag[row];
    },
    deviceModalShow(row, unqualified) {
      this.deviceUnqualified = unqualified || null;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    async deviceArchives(item) {
      let res = await this.$http.get(cascade.getDeviceInfoByDeviceCode + '?deviceCode=' + item.deviceId);
      console.log(res);
      if (res.data && res.data.data.length > 0) {
        let routeData = this.$router.resolve({
          name: 'archives',
          query: { id: res.data.data[0].id },
          // query: {id: item.id},
        });
        window.open(routeData.href, '_blank');
      } else {
        this.$Message.warning('未查询到该设备信息');
      }
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        const property = this.allPropertyList.find((row) => row.propertyName === 'deviceId');
        this.defaultCheckedList = [property.propertyName];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId';
        };
        this.rightDisabled = (item) => {
          return item.propertyName !== 'deviceId';
        };
      } catch (err) {
        console.log(err);
      }
    },
    // 查看不合格原因
    viewRecord(row) {
      this.unqualifiedVisible = true;
      this.$refs.unqualifiedModel.init(row);
    },
    // 重置
    resetHandle() {
      this.selectOrgTree.orgCode = '';
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, this.searchHandle);
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'], [data-theme='deepBlue']{
  .config {
    .icon-font {
      color: #888888;
      &:hover {
        color: var(--color-switch-tab-active);
      }
    }
  }
}
.equipmentlibrary {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);

  .charts {
    margin: 20px 0 0 20px;
  }

  .search-module {
    padding: 10px 20px;

    .keyword-input {
      width: 300px;
    }
  }

  .ui-table {
    padding: 0 20px;
  }
}

.operation {
  margin: 2px 20px 12px 20px;
  line-height: 34px;

  .right-btn {
    button {
      margin-left: 12px;
    }
  }
}

.config {
  position: absolute;
  right: 20px;
  top: 20px;
  color: var(--color-switch-tab);
  cursor: pointer;

  .icon-font {
    font-size: 20px;
  }

  &:hover {
    color: var(--color-switch-tab-active);
  }
}

/deep/ .ivu-badge-dot {
  top: 1px;
  right: -15px;
  border: 0;
  z-index: 3;
}

/deep/ .icon-font {
  font-size: 15px;
}

.btn {
  color: #f5f5f5 !important;
  margin-right: 10px;
}
</style>
