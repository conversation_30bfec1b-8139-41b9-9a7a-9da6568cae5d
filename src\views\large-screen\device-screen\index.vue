<!--
    * @FileDescription: 设备专题大屏
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="screen">
        <div class="screen-left">
            <card title="设备数量统计" class="screen-box">
                <ul class="tab">
                    <li class="tab-btn" :class="{'tab-btn-active': devIndex == 0}" @click="handldevice(0)">视图数据</li>
                    <li class="tab-btn" :class="{'tab-btn-active': devIndex == 1}" @click="handldevice(1)">物联数据</li>
                    <li class="tab-btn" :class="{'tab-btn-active': devIndex == 2}" @click="handldevice(2)">类型占比</li>
                </ul>
                <div class="device-box" v-if="devIndex == 0">
                    <div class="device-box-top device-box-details">
                        <div class="device-box-msg">
                            <img :src="getImgUrl('camera.png')" alt="">
                            <div class="device-box-right">
                                <div class="device-box-type device-title">摄像机</div>
                                <count-to :start-val="0.000" :end-val="deviceViewData.cameraCount" :duration="1000" class="dinpro device-box-num"></count-to>
                            </div>
                        </div>
                        <div class="device-box-small">
                            <div class="device-samll-details">
                                <img :src="getImgUrl('Guncamera.png')" alt="">
                                <p class="device-samll-name">枪机</p>
                                <count-to :start-val="0.000" :end-val="deviceViewData.gunCount" :duration="1000" class="dinpromini devicesmall-num"></count-to>
                            </div>
                            <div class="device-samll-details">
                                <img :src="getImgUrl('sphericalcamera.png')" alt="">
                                <p class="device-samll-name">球机</p>
                                <count-to :start-val="0.000" :end-val="deviceViewData.ballCount" :duration="1000" class="dinpromini devicesmall-num"></count-to>
                            </div>
                        </div>
                    </div>
                    <div class="classify-point">
                        <div class="classify-point-one classify-point-com">
                            <div class="classify-circle"></div>
                            <p class="classify-name">一类点</p>
                            <count-to :start-val="0.000" :end-val="deviceViewData.oneClassPointCount" :duration="1000" class="dinpromini devicesmall-num"></count-to>
                        </div>
                        <div class="classify-point-two classify-point-com">
                            <div class="classify-circle"></div>
                            <p class="classify-name">二类点</p>
                            <count-to :start-val="0.000" :end-val="deviceViewData.twoClassPointCount" :duration="1000" class="dinpromini devicesmall-num"></count-to>
                        </div>
                        <div class="classify-point-three classify-point-com">
                            <div class="classify-circle"></div>
                            <p class="classify-name">三类点</p>
                            <count-to :start-val="0.000" :end-val="deviceViewData.threeClassPointCount" :duration="1000" class="dinpromini devicesmall-num"></count-to>
                        </div>
                    </div>
                    <div class="cross-line">
                        <div class="cross-line-real"></div>
                        <div class="cross-line-empty"></div>
                        <div class="cross-line-real"></div>
                    </div>
                    <div class="device-box-bottom device-box-details">
                        <div class="device-box-msg">
                            <img :src="getImgUrl('device-face.png')" alt="">
                            <div class="device-box-right">
                                <div class="device-box-type device-title">人脸卡口</div>
                                <count-to :start-val="0.000" :end-val="deviceViewData.faceCount" :duration="1000" class="dinpro device-box-num"></count-to>
                            </div>
                        </div>
                        <div class="device-box-msg">
                            <img :src="getImgUrl('device-car.png')" alt="">
                            <div class="device-box-right">
                                <div class="device-box-type device-title">车辆卡口</div>
                                <count-to :start-val="0.000" :end-val="deviceViewData.vehicleCount" :duration="1000" class="dinpro device-box-num"></count-to>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="device-list" v-if="devIndex == 1">
                    <div class="device-box-msg" v-for="(item, index) in interList" :key="index">
                        <img :src="getImgUrl(item.url)" alt="">
                        <div class="device-box-right">
                            <div class="device-box-type device-title">{{ item.name }}</div>
                            <count-to :start-val="0.000" :end-val="item.num" :duration="1000" class="dinpro device-box-num"></count-to>
                        </div>
                    </div>
                </div>
                <pie-chart v-if="devIndex == 2" :picData="typeList" :urlType="4"></pie-chart>
            </card>
            <card title="设备在线率及时长" class="screen-box">
                <div class="form-box">
                    <p class="warpper-title">数据类型</p>
                    <div class="warpper-content">
                        <Select v-model="formData.type" size="small" @on-change="handleChange">
                            <Option :value="1">摄像机</Option>
                            <Option :value="2">人脸</Option>
                            <Option :value="3">车辆抓拍</Option>
                        </Select>
                    </div>
                </div>
                <div class="box-lines">
                    <div class="box-line-img">
                        <div class="box-line-left">
                            <span>{{ onlineData.onlineRate || 0 }}%</span>
                        </div>
                        <p>整体在线率</p>
                    </div>
                    <div class="box-line-img">
                        <div class="box-line-right">
                            <span>{{ onlineData.averageOnlineTime || 0 }}h</span>
                        </div>
                        <p>平均在线率</p>
                    </div>
                </div>
            </card>
            <card title="感知数据统计" class="screen-box">
                <ul class="tab">
                    <li class="tab-btn" :class="{'tab-btn-active': perIndex == 0}" @click="handleper(0)">视图数据</li>
                    <li class="tab-btn" :class="{'tab-btn-active': perIndex == 1}" @click="handleper(1)">物联数据</li>
                </ul>
                <ul class="data-box-ul">
                    <li class="data-box" v-for="(item, index) in dataList" :key="index">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <div class="data-box-top">
                            <div class="box-top-left">
                                <p class="data-box-name">{{ item.name }}</p>
                                <count-to :start-val="0" :end-val="item.num" :duration="1000" class="data-box-num"></count-to>
                            </div>
                            <img class="icon-type" :src="item.iconUrl" alt="">
                        </div>
                        <p class="data-box-add">今日新增</p>
                        <div class="box-add">
                            <div class="box-add-left">{{ item.addNum }}</div>
                            <div class="box-add-right">
                                <p>+5</p>
                                <img :src="getImgUrl('up.png')" alt="">
                            </div>
                        </div>
                    </li>
                </ul>
            </card> 
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        价值数据转化图
                    </div>
                    <div class="view-box-contnet">
                        <div class="box-content-left">
                            <div class="content-left-top">
                                <p class="box-title">价值数据总量</p>
                                <count-to :start-val="0" :end-val="dataValue.dataCount" :duration="1000" class="bank-li-total dinpro"></count-to>
                            </div>
                            <div class="content-left-bottom">
                                <p class="box-title">抓拍数据总量</p>
                                <count-to :start-val="0" :end-val="dataValue.snapDataCount" :duration="1000" class="bank-li-total dinpro"></count-to>
                            </div>
                        </div>
                        <div class="box-content-right">
                            <div class="data-percent">{{ dataValue.resultRate }}%</div>
                            <p>战果转化率</p>
                        </div>
                    </div>
                </div>
                <div class="content-box-right view-box">
                    <div class="title-img bigtitle">
                        设备产生战果统计
                    </div>
                    <month-chart></month-chart>
                </div>
            </div>
            <map-chart ref="mapChart" class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="设备维护单位排行" class="screen-box">
                <div class="table">
                    <div class="table-header">
                        <div class="table-column-pm">排名</div>
                        <div class="table-column-fxj">分县局</div>
                        <div class="table-column-xysc">平均响应时长(小时)</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in timeList" :key="index">
                            <div class="table-column-pm">
                                <img v-if="index<3" :src="getImgUrl('ranking-'+index+'.png') " alt="">
                                <span v-else>{{ item.ranking }}</span>
                            </div>
                            <div class="table-column-fxj">{{ item.name }}</div>
                            <div class="table-column-xysc">
                                <p class="time time-red" v-if="item.type == 1">{{ item.time }}</p>
                                <p class="time time-greed" v-if="item.type == 2">{{ item.time }}</p>
                                <img :src="item.type == 1 ?getImgUrl('down.png') : getImgUrl('up.png')" alt="">
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
            <card title="设备故障处理情况" class="screen-box">
                <div class="maintain-box">
                    <div class="maintain-box-top">
                        <img :src="getImgUrl('gz.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ faultData.faultTotal || 0 }}</p>
                            <p class="detail-li-title">故障数量</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <img :src="getImgUrl('yjj.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ faultData.faultSolve || 0 }}</p>
                            <p class="detail-li-title">已解决</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <img :src="getImgUrl('djj.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ faultData.faultUnSolve || 0 }}</p>
                            <p class="detail-li-title">待解决</p>
                        </div>
                    </div>
                </div>
            </card>
            <card title="平均故障修复时间" class="screen-box">
                <div class="timeShow-box">
                    <div class="box-content">
                        <img :src="getImgUrl('pjxy.png')" alt="">
                        <div class="box-time box-left-time">{{ faultData.faultAvgResponseTime || 0 }}h</div>
                        <p class="box-title">平均响应时间</p>
                    </div>
                    <div class="box-content">
                        <img :src="getImgUrl('pjjj.png')" alt="">
                        <div class="box-time box-right-time">{{ faultData.faultAvgTime || 0 }}h</div>
                        <p class="box-title">平均解决时间</p>
                    </div>
                </div>
            </card>
        </div>
    </div>
</template>
<script> 
import { dataValueRate, 
    deviceCountStat, 
    deviceFaultDetails, 
    deviceOnlineRateStat, 
    deviceUnitRanking,
    sensoryDataStat,
    deviceRegionStat } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import mapChart from './map-chart.vue';
import monthChart from './month-tendency-chart.vue';
import pieChart from './pie-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        mapChart,
        monthChart,
        pieChart,
        CountTo,
    },
    data() {
        return {
            perIndex: 0,
            devIndex: 0,
            dataList: [],
            formData: {
                type: 1,
            },
            viewList: [
                {
                    name: '人脸抓拍总数',
                    num: 7625012924,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/face.png`),
                    type: 'face'
                },
                {
                    name: '车辆抓拍总数',
                    num: 5125033123,
                    addNum: 12916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/car.png`),
                    type: 'vehicle'
                },
                {
                    name: '非机动车抓拍总数',
                    num: 1224012924,
                    addNum: 12916,
                    speed: -6,
                    iconUrl: require(`@/assets/img/screen/fj.png`),
                    type: 'nonMotor'
                },
                {
                    name: '人体抓拍总数',
                    num: 825713924,
                    addNum: 3234,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/rt.png`),
                    type: 'humanBody'
                },
            ],
            objectList: [
                {
                    name: 'ETC采集总量',
                    num: 324321789,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/etc.png`),
                    type: 'etc'
                },
                {
                    name: '电磁采集总量',
                    num: 7625012924,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/dianci.png`),
                    type: 'electromagnetic'
                },
                {
                    name: 'GPS采集总量',
                    num: 7625012924,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/gps.png`),
                    type: 'gps'
                },
                {
                    name: 'RFID采集总量',
                    num: 7625012924,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/rfid.png`),
                    type: 'rfid'
                },
                {
                    name: 'MAC采集总量',
                    num: 7625012924,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/mac.png`),
                    type: 'mac'
                },
                {
                    name: 'IMSI采集总量',
                    num: 7625012924,
                    addNum: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/imsi.png`),
                    type: 'imsi'
                },
            ],
            timeList: [
                { ranking: 1, name: 'XXX分局', time: '0.4h', type: 1, rankingUrl: 'ranking-0.png'},
                { ranking: 2, name: 'XXX分局', time: '0.8h', type: 2, rankingUrl: 'ranking-1.png'},
                { ranking: 3, name: 'XXX分局', time: '1h', type: 1, rankingUrl: 'ranking-2.png'},
                { ranking: 4, name: 'XXX分局', time: '1.2h', type: 2},
                { ranking: 5, name: 'XXX分局', time: '1.5h', type: 2},
                { ranking: 6, name: 'XXX分局', time: '2h', type: 1},
                { ranking: 7, name: 'XXX分局', time: '1.9h', type: 2},
            ],
            interList: [
                { url: 'WiFi.png', name: 'Wi-Fi', num: 7308, type: 'wifiCount'},
                { url: 'Electricgirth.png', name: '点围', num: 4035, type: 'electricCount'},
                { url: 'etc-icon.png', name: 'ETC', num: 203, type: 'etcCount'},
                { url: 'rfid-icon.png', name: 'RFID', num: 3345, type: 'rfidCount'},
                { url: 'solenoid.png', name: '电磁', num: 5499, type: 'pointCount'},
                { url: 'gps-icon.png', name: 'GPS', num: 8494, type: 'gpsCount'},
            ],
            typeList: [
                { value: '25', name: '枪机', type: 'gunPercent' },
                { value: '12', name: '球机', type: 'ballPercent' },
                { value: '12', name: '人脸卡口', type: 'facePercent' },
                { value: '29', name: '车辆卡口', type: 'vehiclePercent' },
                { value: '29', name: 'Wi-Fi', type: 'wifiPercent' },
                { value: '34', name: 'RFID', type: 'rfidPercent' },
                { value: '34', name: 'ETC', type: 'etcPercent'},
                { value: '34', name: '电磁', type: 'electricPercent' },
            ],
            dataValue: {},
            deviceViewData: {},
            faultData: {},
            onlineData: {
                onlineRate: 0,
                averageOnlineTime: 0
            },
            perceptionlist: { }
        }
    },
    created() {
        this.dataList = this.viewList;
        this.init();
        this.queryonlineRate();
        this.queryMap();
    },
    methods: {
        init() {
            dataValueRate()
            .then(res => {
                this.dataValue = res.data;
            })
            deviceCountStat()
            .then(res => {
                this.deviceViewData = res.data.viewData;
                let thing = res.data.thingData;
                this.interList.forEach(item => {
                    if(thing[item.type]) {
                        item.num = thing[item.type];
                    }
                })
                let typeData = res.data.typePercent;
                this.typeList.forEach(item => {
                    if(typeData[item.type]) {
                        item.num = typeData[item.type];
                    }
                })
            })
            // 设备故障详情
            deviceFaultDetails()
            .then(res => {
                this.faultData = res.data;
            })
            // 设备维护单位排名
            deviceUnitRanking()
            .then(res => {
                console.log(res, 'deviceUnitRanking')
            })
            sensoryDataStat()
            .then(res => {
                this.perceptionlist = res.data;
                this.dataList.forEach(item => {
                    item.num = this.perceptionlist[item.type].total;
                    item.addNum = this.perceptionlist[item.type].todayAddCount;
                    item.speed = this.perceptionlist[item.type].changeCount;
                })
            })
        },
        queryMap() {
            deviceRegionStat()
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        },
        queryonlineRate() {
            deviceOnlineRateStat(this.formData.type)
            .then(res => {
                this.onlineData = res.data;
            })
        },
        handleper(index) {
            this.perIndex = index;
            if(index == 0) {
                this.dataList = this.viewList;
            } else {
                this.dataList = this.objectList;
            }
            this.dataList.forEach(item => {
                item.num = this.perceptionlist[item.type].total;
                item.addNum = this.perceptionlist[item.type].todayAddCount;
                item.speed = this.perceptionlist[item.type].changeCount;
            })
        },
        handldevice(index) {
            this.devIndex = index;
        },
        handleChange() {
            this.queryonlineRate();
        },
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    },
}
</script>
<style lang="less" scoped>
@import "../style/resetui.less";
.screen{
    height:calc( ~'100% - 110px');
    display: flex;
    flex: 1;
    padding: 0 40px 20px; 
    .form-box{
        top: 25px !important;
        right: 5px !important;
    }
    .device-box{
        height: 100%;
        padding-top: 30px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        .device-box-top{
            display: flex;
            justify-content: space-between;
            .device-box-small{
                
                .device-samll-details{
                    display: flex;
                    align-items: center;
                    img{
                        width: 16px;
                        height: 16px; 
                    }
                    .device-samll-name{
                        margin: 0 10px 0 6px;
                        color: #00CCFF; 
                    }
                    .devicesmall-num{
                        color: #ffffff;
                        font-size: 16px;
                    }
                }
            }
        }
        .device-box-details{
            
        }
        .classify-point{
            display: flex;
            justify-content: space-between;
            .classify-point-com{
                display: flex;
                align-items: center;
                .classify-circle{
                    width: 6px;
                    height: 6px;
                    border-radius: 3px;
                }
                .classify-name{
                    margin: 0 10px 0 6px;
                    font-size: 12px;
                }
                .devicesmall-num{
                    color: #ffffff;
                    font-size: 14px;
                }
            }
            .classify-point-one{
                .classify-circle{
                    background: #94DAFF;
                    box-shadow: 0px 0px 4px 0px #0073FF;
                }
                .classify-name{
                    color: #27B5FF;
                }
            }
            .classify-point-two{
                .classify-circle{
                    background: #94DAFF;
                    box-shadow: 0px 0px 4px 0px #B283FF;
                }
                .classify-name{
                    color: #B283FF;
                }
            }
            .classify-point-three{
                .classify-circle{
                    background: #94DAFF;
                    box-shadow: 0px 0px 4px 0px #FF8BED;
                }
                .classify-name{
                    color: #FF8BED;
                }
            }

        }
        .cross-line{
            display: flex;
            .cross-line-real{
                width: 6px;
                height: 2px;
                background: #C1E5FF;
            }
            .cross-line-empty{
                flex:1;
                border: 1px dashed #0796FF;
                opacity: 0.5;
            }
        }
        .device-box-bottom{
            display: flex;
        }
    }
    .device-list{
        height: 100%;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        padding: 25px 10px 0;
        .device-box-msg{
            margin-bottom: 5px;
        }
    }
    .device-box-msg{
        display: flex;
        img{
            width: 70px;
            height: 56px;
        }
        .device-box-right{
            margin-left: 10px;
            .device-box-num{
                margin-left: 8px;
                color: #F1FCFF;
                font-size: 24px;    
            }
            .device-title{
                font-size: 14px;
                font-weight: bold;
                color: #fff;
                height: 20px;
                width: 90px;
                padding-left: 10px;
                position: relative;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                top: 0;
                z-index: 1;
                background: linear-gradient(270deg, rgba(3, 145,255, 0) 0%, rgba(3, 145,255, 0.4) 100%);
                &:before {
                    content: '';
                    position: absolute;
                    width: 2px;
                    height: 20px;
                    top: 50%;
                    transform: translateY(-50%);
                    left: 0px;
                    background: rgba(0, 204, 255, 1);
                }
            }
        }
    }
    .box-lines{
        display: flex;
        align-items: center;
        justify-content: space-around;
        width: 100%;
        height: 100%;
        .box-line-img{
            text-align: center;
            color: #ffffff;
            font-size: 14px;
            font-weight: 400;
            .box-line-left{
                width: 152px;
                height: 148px;
                background: url('~@/assets/img/screen/bule.png') no-repeat;
                text-align: center;
                font-weight: 700;
                font-size: 20px;
                color: #ffffff;
                padding-top: 25px;
            }
            .box-line-right{
                width: 152px;
                height: 148px;
                background: url('~@/assets/img/screen/yellow.png') no-repeat;
                text-align: center;
                font-weight: 700;
                font-size: 20px;
                color: #ffffff;
                padding-top: 25px;
            }
        }
    }
    .view-box-contnet{
        padding: 0 !important;
    }
}
</style>