<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template slot="checkStatus" slot-scope="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #orgName="{ row }">
        {{ row?.orgName ?? row?.deviceOrgCodeName }}
      </template>
      <template #qualifiedRate="{ row }">
        <span>{{ row.qualifiedRate || 0 }}%</span>
      </template>
      <template #unqualifiedNum="{ row }">
        <span class="font-red">{{ row.unqualifiedNum }}</span>
      </template>
      <template #urlNotUseCount="{ row }">
        <span class="font-red">{{ row.urlNotUseCount }}</span>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-chakantupian"
          content="查看图片"
          @click.native="clickRow(row)"
          class="mr-sm"
        ></ui-btn-tip>
      </template>
    </Particular>
    <!-- <CheckPicture v-model="checkPicture"
                  :list="currentRow"
                  :resultId="CheckPictureParams"
                  :active-index-item="activeIndexItem"
                  :interface="interfaceFunc"
                  img-key="scenePath" :getParams="getParams"></CheckPicture> -->
    <CheckPicture
      v-model="checkPicture"
      class="picture"
      :list="currentRow"
      :resultId="CheckPictureParams"
      :tagList="tagList"
      @algorithmsReview="(row) => clickAlgorithmsReview(row)"
      :interface="interfaceFunc"
    >
    </CheckPicture>
    <carPropertyDialog ref="carPropertyDialog"></carPropertyDialog>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
// 外层公共配置
import {
  qualifiedColorConfig,
  iconStaticsFaceAndVehicle,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { tableColumns, normalFormData } from './util/enum/ReviewParticular.js';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch],
  props: {
    activeIndexItem: {},
  },
  data() {
    return {
      iconList: [
        ...iconStaticsFaceAndVehicle,
        {
          name: this.activeIndexItem.indexName,
          count: '0',
          countStyle: {
            color: 'var(--color-bluish-green-text)',
          },
          style: {
            color: 'var(--color-bluish-green-text)',
          },
          iconName: 'icon-shipinjiankongjianshezongliang',
          fileName: 'resultValueFormat',
          type: 'percent', // 百分比
        },
      ],
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: [],
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(tableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      currentRow: {},
      checkPicture: false, //查看图片
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      // 1 - 合格、2 - 不合格 3 - 无法检测
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      cardList: [],
      statisticShow: false,
      activeMode: 'device',
      artificialVisible: false,
      artificialRow: {},
      CheckPictureParams: {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
      },
      exportLoading: false,
      algorithmsTableColumns: {
        3003: (tableData) => {
          return [
            { title: '算法名称', key: 'algorithmTitle', align: 'left' },
            {
              title: '车牌号',
              key: 'plateNo',
              align: 'left',
              render: (h, { index, row }) => {
                if (index === 0) {
                  return <span>{row.plateNo}</span>;
                }
                if (index === tableData.length - 1) {
                  return (
                    <span class={{ green: row.votePlateNo, red: !row.votePlateNo }}>
                      {row.votePlateNo ? '准确' : '存疑'}
                    </span>
                  );
                }
                return <span>{row.algorithmPlateNo}</span>;
              },
            },
            {
              title: '车牌颜色',
              key: 'plateColor',
              align: 'left',
              render: (h, { index, row }) => {
                if (index === 0) {
                  return <span>{row.plateColor}</span>;
                }
                if (index === tableData.length - 1) {
                  return (
                    <span
                      class={{
                        green: row.votePlateColor,
                        red: !row.votePlateColor,
                      }}
                    >
                      {row.votePlateColor ? '准确' : '存疑'}
                    </span>
                  );
                }
                return <span>{row.algorithmPlateColor}</span>;
              },
            },
          ];
        },
        3004: (tableData) => {
          return [
            { title: '算法名称', key: 'algorithmTitle', align: 'left' },
            {
              title: '车牌号',
              key: 'plateNo',
              align: 'left',
              render: (h, { index, row }) => {
                if (index === 0) {
                  return <span>{row.plateNo}</span>;
                }
                if (index === tableData.length - 1) {
                  return (
                    <span class={{ green: row.votePlateNo, red: !row.votePlateNo }}>
                      {row.votePlateNo ? '准确' : '存疑'}
                    </span>
                  );
                }
                return <span>{row.algorithmPlateNo}</span>;
              },
            },
            {
              title: '车牌颜色',
              key: 'plateColor',
              align: 'left',
              render: (h, { index, row }) => {
                if (index === 0) {
                  return <span>{row.plateColor}</span>;
                }
                if (index === tableData.length - 1) {
                  return (
                    <span
                      class={{
                        green: row.votePlateColor,
                        red: !row.votePlateColor,
                      }}
                    >
                      {row.votePlateColor ? '准确' : '存疑'}
                    </span>
                  );
                }
                return <span>{row.algorithmPlateColor}</span>;
              },
            },
            {
              title: '车辆类型',
              key: 'vehicleClass',
              align: 'left',
              render: (h, { index, row }) => {
                if (index === 0) {
                  return <span>{row.vehicleClass}</span>;
                }
                if (index === tableData.length - 1) {
                  return (
                    <span
                      class={{
                        green: row.voteVehicleClass,
                        red: !row.voteVehicleClass,
                      }}
                    >
                      {row.voteVehicleClass ? '准确' : '存疑'}
                    </span>
                  );
                }
                return <span>{row.algorithmVehicleClass}</span>;
              },
            },
            {
              title: '车辆品牌',
              key: 'vehicleBrand',
              align: 'left',
              render: (h, { index, row }) => {
                if (index === 0) {
                  return <span>{row.vehicleBrand}</span>;
                }
                if (index === tableData.length - 1) {
                  return (
                    <span
                      class={{
                        green: row.voteVehicleBrand,
                        red: !row.voteVehicleBrand,
                      }}
                    >
                      {row.voteVehicleBrand ? '准确' : '存疑'}
                    </span>
                  );
                }
                return <span>{row.algorithmVehicleBrand}</span>;
              },
            },
          ];
        },
      },
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
    if (this.colorType.length === 0) {
      this.getcolorType();
    }
    if (this.vehicleBandType.length === 0) {
      this.getvehicleBandType();
    }
    if (this.vehicleClassType.length === 0) {
      this.getvehicleClassType();
    }
    if (this.plateClassType.length === 0) {
      this.getplateClassType();
    }
  },
  methods: {
    ...mapActions({
      getcolorType: 'algorithm/getcolorType',
      getvehicleBandType: 'algorithm/getvehicleBandType',
      getvehicleClassType: 'algorithm/getvehicleClassType',
      getplateClassType: 'algorithm/getplateClassType',
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    async interfaceFunc(params) {
      try {
        const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          ...params,
        };
        let res = await this.$http.post(evaluationoverview.getPolyData, data);
        let entities = res.data.data.entities || [];
        let cardList = entities.map((item) => {
          //重点车辆卡口设备过车数据准确率 显示算法详情
          item.hasAlgorithms = true;
          // 处理图片模式字段展示
          let importFileList = [
            {
              label: '车辆类型：',
              value: this.$options.filters.filterType(
                item.vehicleClass,
                this.vehicleClassType,
                'dictKey',
                'dictValue',
                '缺失',
              ),
            },
            {
              label: '车辆品牌：',
              value: this.$options.filters.filterType(
                item.vehicleBrand,
                this.vehicleBandType,
                'dictKey',
                'dictValue',
                '缺失',
              ),
            },
          ];
          item.fileList = [
            { label: '车牌号：', value: item.plateNo },
            {
              label: '车牌颜色：',
              value: this.$options.filters.filterType(item.plateColor, this.colorType, 'dictKey', 'dictValue', '缺失'),
            },
            ...(this.$route.query.indexType === 'VEHICLE_INFO_PASS_IMPORTANT' ? importFileList : ''),
            { label: '抓拍时间：', value: item.shotTime },
            { label: '抓拍地点：', value: item.address },
          ];
          return item;
        });
        return { cardList, total: res.data.data.total };
      } catch (err) {
        ('');
        console.log(err);
      }
    },
    initAll() {
      // 获取列表
      this.getTableData();
      this.getTableDataTotal();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] ?? 0;
        });
      });
    },
    // 获取不合格原因下拉列表
    // mode 1:设备模式，2:图片模式
    getQualificationList(mode = 1) {
      // 异常原因
      this.MixinDisQualificationList(mode).then((data) => {
        if (mode === 1) {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
          findErrorCodes.options = data.map((item) => {
            // 嘉鹏说: 设备模式查询需转换数字模式
            return { value: Number(item.key), label: item.value };
          });
        } else {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'causeErrors');
          findErrorCodes.options = data.map((item) => {
            return { value: item.key, label: item.value };
          });
        }
      });
    },
    clickAlgorithmsReview(row) {
      this.$refs.carPropertyDialog.init(row.id, row.indexId, this.algorithmsTableColumns[row.indexId]);
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      this.formData = {
        deviceId: params.deviceId,
        deviceName: params.deviceName,
        outcome: params.outcome,
        errorCodes: params.errorCodes,
      };
      this.getTableData();
      this.getTableDataTotal();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        // this.totalCount = data.total;
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        cardList: this.cardList,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    CheckPicture: require('@/views/governanceevaluation/evaluationoResult/common-pages/check-picture/index.vue')
      .default,
    exportData: require('../components/export-data').default,
    carPropertyDialog: require('@/views/governanceevaluation/evaluationoResult/components/car-property-dialog.vue')
      .default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
</style>
