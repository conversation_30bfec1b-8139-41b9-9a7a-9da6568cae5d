<template>
  <div>
    <Upload
      v-if="!isView"
      v-show="multiple || (!multiple && uploadData.length < 1)"
      v-bind="$attrs"
      ref="upload"
      class="inline"
      :action="uploadUrl"
      :show-upload-list="false"
      :headers="headers"
      :max-size="20480"
      :before-upload="beforeUpload"
      :on-success="importSuccess"
      :on-error="importError"
      :on-format-error="importFormatError"
      :on-exceeded-size="importExceededSize"
      :multiple="multiple"
    >
      <Button :loading="importLoading">
        <slot name="uploadText">
          <i class="icon-font icon-shangchuan f-12 vt-middle"></i>
          <span class="vt-middle ml-sm">{{ uploadPlaceholder }}</span>
        </slot>
      </Button>
    </Upload>
    <slot name="downloadtip"></slot>
    <div class="mb-sm">
      <div v-for="(upItem, upIndex) in uploadData" :key="'upIndex' + upIndex">
        <Button type="text" @click.stop="downLoad(upItem)" title="点击下载">
          <span class="upload-name">{{ upItem.name }}</span>
        </Button>
        <ui-btn-tip
          v-if="!isView"
          icon="icon-shangchuan"
          class="font-blue ml-sm"
          content="重新上传"
          @click.native.stop="reupload(upIndex)"
        ></ui-btn-tip>
        <ui-btn-tip
          v-if="!isView"
          icon="icon-piliangshanchu"
          class="font-red ml-sm"
          content="删除"
          @click.native.stop="delUpload(upIndex)"
        ></ui-btn-tip>
      </div>
    </div>
  </div>
</template>

<script>
//https://vo52iz.axshare.com/#g=1&p=%E6%96%B0%E5%BB%BA%E5%B7%A5%E5%8D%95%EF%BC%88%E8%87%AA%E5%AE%9A%E4%B9%89%E9%97%AE%E9%A2%98%EF%BC%89
export default {
  name: 'ui-upload',
  components: {},
  props: {
    // 如果不需要上传到服务器上可以为空，同过uploadFile可以拿到对应的文件
    uploadUrl: {
      type: String,
      default: '',
    },
    /**
     * 双向数据绑定的数据如果为数组 应为[{url: xxx, name: xxx}]
     * 如果为字符串则应该为 url,url
     *  */
    value: {
      type: [String , Array],
    },
    //on-success 的回调，处理上传后需要返回的字段等问题
    getUploadParams: {
      type: Function,
    },
    uploadPlaceholder: {
      type: String,
      default: '上传附件',
    },
    // 是否支持多选文件
    multiple: {
      type: Boolean,
      default: false,
    },
    // 是否是查看
    isView: {
      type: Boolean,
      default: false,
    },
    maxUpload: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importLoading: false,
      uploadData: [],
      isReload: false,
      reIndex: 0,
    };
  },
  computed: {},
  filter: {},
  mounted() {},
  methods: {
    downLoad(upItem) {
      if (!upItem.url) {
        throw Error('没有下载地址');
      }
      this.$util.common.transformBlob(upItem.url, upItem.name);
    },
    delUpload(index) {
      this.uploadData.splice(index, 1);
      this.$emit('input', '');
      this.$emit('delUpload');
    },
    reupload(index) {
      this.isReload = true;
      this.reIndex = index;
      this.$refs.upload.handleClick();
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$Message.error('上传文件大小不能超过10MB!');
        return false;
      }
      if (this.uploadData.length >= this.maxUpload) {
        this.$Message.error(`最多可上传${this.maxUpload}个文件！`);
        return false;
      }
      if (!this.uploadUrl) {
        this.uploadData = [file];
        this.$emit('uploadFile', file);
        return false;
      }
      this.importLoading = true;
    },
    importSuccess(res, file) {
      this.importLoading = false;
      if (this.isReload) {
        this.uploadData.splice(this.reIndex, 1, file);
        this.isReload = false;
      } else {
        if (this.multiple) {
          this.uploadData.push(file);
        } else {
          this.uploadData = [file];
          this.$refs.upload.clearFiles();
        }
      }
      this.$emit('input', this.getUploadParams(this.uploadData));
      this.$Message.success(res.msg);
    },
    importFormatError() {
      this.$Message.error('上传文件类型不符合要求');
      this.importLoading = false;
    },
    importExceededSize() {
      this.$Message.error('上传文件超出20M');
      this.importLoading = false;
    },
    importError(err) {
      this.importLoading = false;
      err.msg && this.$Message.error(err.msg);
    },
    getFileName(val) {
      let fileName = val && val.split('/').pop();
      return fileName || '';
    },
    handleClearFiles() {
      this.$refs.upload.clearFiles();
    },
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          this.$nextTick(() => {
            this.handleClearFiles();
          });
          this.uploadData = [];
          return false;
        }
        // 如果是数组则直接赋值，如果是字符串则处理后赋值
        if (val instanceof Array) {
          this.uploadData = this.$util.common.deepCopy(val);
        } else if (typeof val === 'string') {
          // 如果是多个地址，则切割赋值
          if (val.indexOf(',') !== -1) {
            let uploadList = [];
            val.split(',').forEach((row) => {
              uploadList.push({
                url: row,
                name: this.getFileName(row),
              });
            });
            this.uploadData = uploadList;
          } else {
            this.uploadData = [{ url: val, name: this.getFileName(val) }];
          }
        }
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.upload-name {
  display: inline-block;
  max-width: 265px;
  overflow: hidden;
  vertical-align: middle;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-decoration: underline;
}
</style>
