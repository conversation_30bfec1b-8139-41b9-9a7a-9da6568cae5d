<template>
  <div class="height-full report-generation auto-fill">
    <!--    <lucky-excel title="视频流数据质量治理"> </lucky-excel>-->
    <div class="ml-lg head">
      <div class="title">
        <i class="icon-font icon-qitagongju"></i>
        <span class="inline vt-middle ml-sm">报表生成</span>
      </div>
      <Button type="text" class="btn-back mr-sm" @click="backHandle">&lt;&lt; 返回</Button>
    </div>
    <div class="auto-fill content">
      <div class="inline mb-lg">
        <ui-label label="报表模板">
          <ui-upload
            class="inline"
            ref="UiUpload"
            v-model="formData.file"
            @uploadFile="uploadFile"
            upload-placeholder="上传EXCEL表格模版"
          >
          </ui-upload>
          <span class="ml-sm tip f-14" @click="templateTip">模板示例说明</span>
        </ui-label>
        <ui-label label="模板类型" class="mt-md">
          <Select v-model="formData.template" class="width-input">
            <Option v-for="(item, index) in typeList" :key="index" :label="item.label" :value="item.value"></Option>
          </Select>
        </ui-label>
        <Button type="primary" class="generate-report" :loading="generateLoading" @click="generateReport">
          生成报表
        </Button>
        <div class="report-file" v-if="reportFile">
          <i class="icon-font icon-chenggong"></i>
          <span class="ml-mini inline vt-middle pointer ellipsis width-lg file-name" :title="reportFileText">
            {{ reportFileText }}
          </span>
          <span class="ml-md inline vt-middle pointer a-link" @click="downLoadReport">下载报表</span>
          <span class="ml-sm mr-sm inline vt-middle">|</span>
          <span class="ml-sm mr-sm inline vt-middle pointer a-link" @click="goDownload">去【我的下载】中下载</span>
        </div>
      </div>
    </div>
    <ui-modal v-model="tipShow" title="模板示例说明" footer-hide>
      <img v-if="themeType === 'dark'" src="@/assets/img/governancetoolset/report-template.png" alt="" />
      <img v-else src="@/assets/img/governancetoolset/report-template-light.png" alt="" />
    </ui-modal>
  </div>
</template>
<script>
import user from '@/config/api/user';
import governancetoolset from '@/config/api/governancetoolset';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {},
  data() {
    return {
      generateLoading: false,
      typeList: [],
      formData: {
        template: '',
        file: '',
      },
      reportFile: null,
      reportFileText: '',
      tipShow: false,
    };
  },
  created() {
    this.getDictData();
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
    }),
    // 返回
    backHandle() {
      this.$router.push({ name: 'governancetoolset' });
    },
    async getDictData() {
      try {
        const params = {
          typekey: 'template_export_type',
        };
        let { data } = await this.$http.get(user.queryByTypeKey, { params });
        this.typeList = data.data.map((item) => {
          return {
            label: item.dataValue,
            value: item.dataKey,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    uploadFile(file) {
      this.formData.file = file;
      this.reportFile = null;
    },
    downLoadReport() {
      this.$util.common.transformBlob(this.reportFile);
    },
    async generateReport() {
      try {
        this.generateLoading = true;
        const form = new FormData();
        form.append('template', this.formData.template);
        form.append('file', this.formData.file);
        const res = await this.$http.post(governancetoolset.importTemplate, form);
        this.reportFile = res.data.data;
        const index = this.reportFile.lastIndexOf('\/');
        this.reportFileText = res.data.data.substring(index + 1, this.reportFile.length);
      } catch (err) {
        console.log(err);
      } finally {
        this.generateLoading = false;
      }
    },
    goDownload() {
      const index = this.cacheRouterList.findIndex((row) => row.name === 'downloadcenter');
      if (index === -1) {
        this.cacheRouterList.push({
          name: 'downloadcenter',
          path: '/downloadcenter',
          text: '我的下载',
        });
        this.setCacheRouterList(this.cacheRouterList);
      }
      this.$router.push({ name: 'downloadcenter' });
    },
    templateTip() {
      this.tipShow = true;
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
      themeType: 'common/getThemeType',
    }),
  },
  components: {
    // LuckyExcel: require('@/components/lucky-excel').default,
    UiUpload: require('@/components/ui-upload.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .report-generation {
    background: var(--bg-content) url('~@/assets/img/governancetoolset/report-generation-bg.png') no-repeat center/cover;
    .title {
      color: #19d5f6;
    }
    .content {
      .report-file {
        i {
          color: #0e8f0e;
        }
        .file-name {
          color: #a9bed9;
        }
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .report-generation {
    background: #f1f3f6 url('~@/assets/img/governancetoolset/report-generation-bg-light.png') no-repeat center/cover;
    .title {
      color: var(--color-primary);
    }
    .content {
      .report-file {
        i {
          color: var(--color-success);
        }
        .file-name {
          color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }
}

.report-generation {
  @{_deep}.ivu-modal {
    width: auto !important;
  }
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    height: 50px;
    .title {
      font-size: 16px;
      i {
        font-size: 16px;
      }
    }
  }
  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    @{_deep}.upload-name {
      max-width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .tip {
      text-decoration: underline;
      color: var(--color-active);
      cursor: pointer;
    }
    .width-input {
      width: 300px;
    }
    .generate-report {
      width: 300px;
      margin-top: 40px;
      margin-left: 65px;
      margin-bottom: 30px;
    }
    .report-file {
      font-size: 14px;
      color: var(--color-active);
      .a-link {
        text-decoration: underline;
      }
    }
  }
  .btn-back {
    color: var(--color-active) !important;
  }
}
</style>
