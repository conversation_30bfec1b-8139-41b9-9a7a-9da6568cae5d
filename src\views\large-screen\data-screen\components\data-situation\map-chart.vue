<!--
    * @FileDescription: 地图
    * @Author: H
    * @Date: 2024/04/29
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="mapChart" class="mapChart" ref="mapChart"></div>
        <div class="operatio-btn">
            <div class="data-hot">
                <p>数据热力图</p>
                <i-switch v-model="formData.dataHot" @on-change="handleData"/>
            </div>
            <div class="police-hot">
                <p>警力热力图</p> 
                <i-switch v-model="formData.policeHot" @on-change="handlePolice"/>
            </div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import mapConfig from './bengbu.json';
import { regionStat } from '@/api/largeScreen';
export default {
    props: {
        urlType: {
            type: Number,
            default: 0
        },
        picData: {
            type: Array,
            default: () => {
                let list = [
                    { value: '75', name: '有效' },
                    { value: '25', name: '无效' },
                ];
                return list
            }
        },
        title: {
            type: String,
            default: ''
        }
    },  
    data() {
        return {
            myEchart: null,
            formData: {
                dataHot: true,
                policeHot: false,
            },
            dataColor:{
                areaColor: 'rgba(8, 224, 255, 0.8)',
                offsetTop: 'rgba(38,58,207,0.8)',
                offsetEnd: 'rgba(74,133,225,0.8)',
            },
            option: {},
            mapDateList: []
        }
    },
    mounted() {
        // this.init()
        // this.mapchart()
        this.queryMap();
    },
    methods: {
        handleData() {
            if(this.formData.dataHot) {
                this.formData.policeHot = false;
            }
            this.dataColor = {
                areaColor: 'rgba(8, 224, 255, 0.8)',
                offsetTop: 'rgba(38,58,207,0.8)',
                offsetEnd: 'rgba(74,133,225,0.8)',
            };
            this.mapchart(this.mapDateList)
        },
        handlePolice() {
            if(this.formData.policeHot) {
                this.formData.dataHot = false;
            }
            this.dataColor = {
                areaColor: '#FFA41C',
                offsetTop: 'rgba(185,48,23,0.8)',
                offsetEnd: 'rgba(185,48,23,1)',
            };
            this.mapchart(this.mapDateList)
        },
        queryMap() {
            regionStat(1)
            .then(res => {
                this.init(res.data)
            })
        },
        init(list) {
            var mapDate = [
                { name: '蚌山区', value: 100, regionCode: '340303', exData: {} },
                { name: '龙子湖区', value: 33.33, regionCode: '340302', exData: {} },
                { name: '禹会区', value: 16.67, regionCode: '340304', exData: {} },
                { name: '淮上区', value: 40, regionCode: '340311', exData: {} },
                { name: '五河县', value: 6.67, regionCode: '340322', exData: {} },
                { name: '固镇县', value: 26.67, regionCode: '340323', exData: {} },
                { name: '怀远县', value: 33.33, regionCode: '340321', exData: {} },
            ];
            let mapList = new Map(list.map(item => [item.regionCode, item]))
            mapDate.forEach(item => {
                if(mapList.get(item.regionCode)) {
                    let obj = JSON.parse(mapList.get(item.regionCode).extensionData)
                    item.value = this.parseValue(obj);
                    item.exData = obj;
                }
            })
            this.mapDateList=mapDate;
            this.mapchart(mapDate)
        },
        parseValue(obj) {
            let num = 0;
            for(let key in obj) {
                num += obj[key]
            }
            return num
        },
        mapchart(mapDate) {
            // 数据 1-10
            // rgba(44,190,248,1);  rgba(36,168,234,1); rgba(22,130,209,1); rgba(13,103,191,1); rgba(0,78,194,1);
            // 警情 
            // rgba(248,122,107,1); rgba(222,91,72,1); rgba(210,77,56,1); rgba(248,122,107,1); rgba(185,48,23,1);     
            this.myEchart = echarts.init(this.$refs.mapChart);
            echarts.registerMap('蚌埠市', mapConfig);
            var mapName = "蚌埠市"
            this.option = {
                // visualMap: {
                //     show: true,
                //     bottom: 0,
                //     right: "5%",
                //     type: "piecewise",
                //     align: "left",
                //     orient: "vertical",
                //     inverse: false,
                //     textStyle: {
                //         color: "#95cfee",
                //         fontSize: 12,
                //     },
                //     itemGap: 10,
                //     pieces: [
                //         {
                //             gt: -1000,
                //             lte: 10,
                //             // color: new echarts.graphic.RadialGradient(0.5, 0.5, 1.4, [
                //             //     {
                //             //         offset: 0,
                //             //         color: 'rgba(35,227,202,0.7)'
                //             //     },
                //             //     {
                //             //         offset: 0.4,
                //             //         color: 'rgba(35,227,202,0.1)'
                //             //     },
                //             //     {
                //             //         offset: 0.5,
                //             //         color: 'rgba(35,227,202,0)'
                //             //     },
                //             //     {
                //             //         offset: 1,
                //             //         color: 'rgba(35,227,202,0)'
                //             //     },

                //             // ]),
                //             color: 'rgba(35,227,202,0.7)',
                //             label: "0<x<10",
                //             symbol: "circle",
                //         },
                //         {
                //             gt: 10,
                //             lt: 20,
                //             // color: "rgba(82,127,239,0.5)",
                //             color: new echarts.graphic.RadialGradient(0.5, 0.5, 1.4, [
                //                 {
                //                     offset: 0,
                //                     color: 'rgba(82,127,239,0.7)'
                //                 },
                //                 {
                //                     offset: 0.4,
                //                     color: 'rgba(82,127,239,0.1)'
                //                 },
                //                 {
                //                     offset: 0.5,
                //                     color: 'rgba(82,127,239,0)'
                //                 },
                //                 {
                //                     offset: 1,
                //                     color: 'rgba(82,127,239,0)'
                //                 },

                //             ]),
                //             label: "10≤x<20",
                //             symbol: "circle",
                //         },
                //         {
                //             gt: 20,
                //             lte: 30,
                //             // color: "rgba(249,198,44,0.5)",
                //             color: new echarts.graphic.RadialGradient(0.5, 0.5, 1.4, [
                //                 {
                //                     offset: 0,
                //                     color: 'rgba(249,198,44,0.7)'
                //                 },
                //                 {
                //                     offset: 0.4,
                //                     color: 'rgba(249,198,44,0.1)'
                //                 },
                //                 {
                //                     offset: 0.5,
                //                     color: 'rgba(249,198,44,0)'
                //                 },
                //                 {
                //                     offset: 1,
                //                     color: 'rgba(249,198,44,0)'
                //                 },

                //             ]),
                //             label: "20≤x<30",
                //             symbol: "circle",
                //         },
                //         {
                //             gt: 30,
                //             colorAlpha: 1,
                //             opacity: 1,
                //             // color: "rgba(240,77,77,0.5)",
                //             color: "rgba(8, 224, 255, 0.5)",
                //             // color: new echarts.graphic.RadialGradient(0.5, 0.5, 1.4, [
                //             //     {
                //             //         offset: 0,
                //             //         color: 'rgba(240,77,77,0.7)'
                //             //     },
                //             //     {
                //             //         offset: 0.4,
                //             //         color: 'rgba(240,77,77,0.1)'
                //             //     },
                //             //     {
                //             //         offset: 0.5,
                //             //         color: 'rgba(240,77,77,0)'
                //             //     },
                //             //     {
                //             //         offset: 1,
                //             //         color: 'rgba(240,77,77,0)'
                //             //     },

                //             // ]),
                //             label: "x≥30",
                //             symbol: "circle",
                //         },
                //     ],
                // },
                yAxis: {
                    show: false,
                    data: mapDate,
                },
                tooltip: {
                    trigger: "item",
                    borderWidth: '0',
                    borderColor: "none",
                    padding: 16,
                    textStyle: {
                        fontSize: 12,
                        color: 'rgba(209, 212, 220, 1)',
                    },
                    tooltip: {
                        show: true
                    },
                    extraCssText: 'background: rgba(61, 63, 71, 1)',
                    className: "custom-tooltip-box",
                    formatter: (params) => {
                        let taskObj = {};
                        mapDate.forEach(item => {
                            if(item.name == params.name) {
                                taskObj = item;
                            }
                        })
                        console.log(taskObj, 'taskObj')
                        return `<div class='custom-tooltip-style'>
                                <div class="tooltip-title">
                                    <div class="tooltip-dot"></div>
                                    <span>${params.name}</span> 
                                </div>
                                <div class="tooltip-withe-line"></div>
                                <div class="tooltip-content">
                                    <p class="tooltip-content-title">布控任务</p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">人像：</span>
                                        <span class="tooltip-warpper-content">${taskObj.exData.face}</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">机动车：</span>
                                        <span class="tooltip-warpper-content">${taskObj.exData.vehicle}</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">人体：</span>
                                        <span class="tooltip-warpper-content">${taskObj.exData.humanBody}</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">非机动车：</span>
                                        <span class="tooltip-warpper-content">${taskObj.exData.nonMotor}</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">MAC：</span>
                                        <span class="tooltip-warpper-content">${taskObj.exData.mac}</span>
                                    </p> 
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">IMSI：</span>
                                        <span class="tooltip-warpper-content">${taskObj.exData.imsi}</span>
                                    </p> 
                                </div>
                            </div>`
                    }
                },
                geo: [
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        z: 2,
                        layoutCenter: ["50%", "42%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                        emphasis: {
                            show: false,
                        },
                        },
                        itemStyle: {
                        normal: {
                            areaColor: {
                                type: "linear",
                                x: 60,
                                y: 10,
                                x2: 20,
                                y2: 110,
                                colorStops: [
                                    {
                                        offset: 0,
                                        color: this.dataColor.offsetTop, // 0% 处的颜色
                                    },
                                    {
                                        offset: 1,
                                        color: this.dataColor.offsetEnd, // 50% 处的颜色
                                    },
                                ],
                                global: true, // 缺省为 false
                            },
                            borderColor: "#fff",
                            borderWidth: 0.2,
                        },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "43%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 1,
                                shadowColor: "#8cd3ef",
                                shadowOffsetY: 10,
                                shadowBlur: 100,
                                areaColor: "transparent",
                            },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "44%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 1,
                                shadowColor: "#8cd3ef",
                                shadowOffsetY: 10,
                                shadowBlur: 100,
                                areaColor: "transparent",
                            },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "45%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 1,
                                shadowColor: "#8cd3ef",
                                shadowOffsetY: 10,
                                shadowBlur: 100,
                                areaColor: "transparent",
                            },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "46%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 10,
                                shadowColor: "rgba(121,149,250,1)",
                                shadowOffsetY: 10,
                                shadowBlur: 5,
                                opacity: 0.2,
                                areaColor: "rgba(121,149,250,0.1)",
                            },
                        }
                    },
                ],
                series: [
                    {
                        name: mapName,
                        type: "map",
                        mapType: mapName,
                        selectedMode: "false", //是否允许选中多个区域
                        showLegendSymbol: true,
                        roam: false,
                        top: "8%",
                        aspectScale: 0.8,
                        layoutSize: "100%",
                        layoutCenter: ["50%", "42%"],
                        zoom: 0, //当前视角的缩放比例
                        itemStyle: {
                            normal: {
                                areaColor: 'transparent',
                                borderColor: "#80AAE1",
                                borderWidth: 2
                            },
                            //选中样式
                            emphasis: {
                                disabled: true,
                                borderWidth: 2,
                                label:{
                                    textStyle: {
                                        fontSize: 12,
                                        fontWeight: "bolder",
                                        color: "#9CCDDC",
                                        shadowColor: "rgba(39,76,193,0.4)",
                                        shadowBlur: 2,
                                        shadowOffsetX: 1,
                                        shadowOffsetY: 1,
                                    },
                                },
                                borderColor: "#80AAE1",
                                // areaColor: 'transparent', //鼠标浮动样式
                                areaColor: this.dataColor.areaColor,
                            },
                        },
                        label: {
                            normal: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    fontWeight: "bolder",
                                    color: "#9CCDDC",
                                    shadowColor: "rgba(39,76,193,0.4)",
                                    shadowBlur: 2,
                                    shadowOffsetX: 1,
                                    shadowOffsetY: 1,
                                },
                            },
                        },
                        data: mapDate,
                    },
                ],
            };
            this.myEchart.setOption(this.option)
            window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    .mapChart{
        height: 539px;
        width: 757px;
        margin: auto;
    }
    
}
</style>
<style lang='less'>
@import "../common/style.less";
.chart{
    position: relative;
}
.operatio-btn{
    position: absolute;
    bottom: 85px;
    right: 65px;
    .data-hot{
        display: flex;
        margin-bottom: 10px;
        p{
            color: #FFFFFF;
            font-size: 14px;
            font-weight: 400;
            margin-right: 10px;
        }
    }
    .police-hot{
        display: flex;
        margin-bottom: 10px;
        p{
            color: #FFFFFF;
            font-size: 14px;
            font-weight: 400;
            margin-right: 10px;
        }
    }
}
/deep/ .ivu-switch{
    border-color: #098EFF;
    background-color: transparent;
}
</style>