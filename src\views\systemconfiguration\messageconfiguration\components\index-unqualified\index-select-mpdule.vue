<template>
  <div class="tree-module">
    <ui-modal v-model="visible" :title="title" :styles="style">
      <CollapseTree
        v-bind="$attrs"
        :show-checkbox="true"
        :expand-on-click-node="true"
        :default-checked-keys="defaultSelectKeys"
        :default-keys="defaultSelectKeys"
        :node-key="nodeKey"
        :default-props="defaultProps"
        @check="checkFn"
      ></CollapseTree>
      <template slot="footer">
        <Button type="primary" @click="query" class="plr-30">确 定</Button>
        <Button type="default" @click="cancel" class="plr-30">取 消</Button>
      </template>
    </ui-modal>
  </div>
</template>

<script>
export default {
  props: {
    value: {},
    title: {
      default: '选择指标',
    },
    // 默认勾选的key
    defaultSelectKeys: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      style: {
        width: '6rem',
        height: '4rem',
      },
      checkedKeys: [],
      nodeKey: 'indexId',
      defaultProps: {
        label: 'indexName',
        children: 'children',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    query() {
      this.visible = false;
      this.$emit('onSubmit', this.checkedKeys);
    },
    cancel() {
      this.visible = false;
    },
    checkFn(checkedKeys) {
      this.checkedKeys = checkedKeys.filter((item) => !String(item).includes('indexModule-'));
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {},
  components: {
    CollapseTree: require('@/components/collapse-tree/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.tree-module {
  @{_deep} .ivu-modal-content {
    height: 100%;
  }
  @{_deep} .ivu-modal-body {
    height: calc(100% - 95px);
  }
}
</style>
