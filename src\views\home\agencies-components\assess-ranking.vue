<template>
  <div class="assess-ranking" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-kaohepaihangbang">
      月考核排行榜
      <template #filter>
        <span class="score">满分：{{ score || 0 }}</span>
      </template>
    </HomeTitle>
    <div class="ranking-list" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
      <ul>
        <li v-for="(item, index) in rankData" :key="index" @click="$emit('toResultExamination', item)" class="pointer">
          <div class="content-firstly">
            <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
            <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
            <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
            <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
              item.rank
            }}</span>
          </div>
          <Tooltip class="content-second" transfer :content="item.regionName">
            <div>
              <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
              <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
              <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
              <!-- <span>{{ item.regionName }}</span> -->
              <span v-if="item.rank == 1">{{ item.regionName }}</span>
              <span v-if="item.rank == 2">{{ item.regionName }}</span>
              <span v-if="item.rank == 3">{{ item.regionName }}</span>
              <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                item.regionName
              }}</span>
            </div>
          </Tooltip>
          <div class="content-thirdly">
            <span class="thirdly">{{ item.standardsValue }}分</span>
          </div>

          <div class="content-fourthly">
            <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
            <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
            <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
            <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
            <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{ item.rankRise || 0 }}</span>
            <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<style lang="less" scoped>
.assess-ranking {
  height: 48%;
  width: 462px;
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 104, 183, 0.13);
  .score {
    color: #eb825a;
    margin-right: 10px;
  }
}
.assess-title {
  height: 32px;
  width: 100%;
  background: #40e1fe;
  opacity: 0.36;
}
.ranking-list {
  display: inline-block;
  width: 100%;
  height: calc(100% - 32px);
  overflow-y: scroll;
  ul {
    width: 100%;
    overflow: hidden;
    padding-bottom: 10px;
    li {
      display: flex;
      padding-top: 15px;
      align-items: center;
      .content-fourthly {
        display: inline-flex;
        font-size: 14px;
        position: relative;
      }
      div {
        display: flex;

        align-items: center;
        font-size: 14px;
        position: relative;
      }

      .content-firstly {
        // width: 60px;
        flex: 1;
        margin-left: 10px;
      }
      .content-thirdly {
        justify-content: center;
        flex: 1;
      }
      .content-fourthly {
        justify-content: center;
        // width: 90px;
        flex: 1;
      }
      .content-second {
        color: #fff;
        justify-content: center;
        // width: 150px;
        flex: 1;

        img {
          vertical-align: middle;
        }

        span {
          // width: calc(100% - 80px);
          width: 75px;
          padding-left: 10px;
          display: inline-block;
          // text-align: center;
          vertical-align: middle;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .rankText {
          margin-left: 20px;
        }
      }

      .bg_color {
        min-width: 21px;
        min-height: 21px;
        text-align: center;
        font-weight: bold;
        color: #fff;
        font-size: 14px;
      }
      .firstly1 {
        background-color: #f1b700;
      }
      .firstly2 {
        background-color: #eb981b;
      }
      .firstly3 {
        background-color: #ae5b0a;
      }
      .firstly4 {
        background-color: var(--color-primary);
      }

      .thirdly {
        overflow: hidden;
        color: var(--color-primary);
      }
      .color-sheng {
        color: #0e8f0e;
        font-size: 14px;
      }
      .color-jiang {
        color: #bc3c19;
        font-size: 14px;
      }
    }
  }
}
.full-screen-container {
  position: absolute;
  bottom: 10px;
  height: 45%;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'plant-assets',
  props: {
    taskType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      rankData: [],
      rankLoading: false,
      score: '',
    };
  },
  methods: {
    async getRankStatistics() {
      this.rankLoading = true;
      let data = {
        examTaskId: this.taskType,
      };
      try {
        let res = await this.$http.post(home.getRankStatistics, data);
        this.rankData = res.data.data;
        this.rankData.map((item) => {
          this.score = item.score;
        });
        this.rankLoading = false;
      } catch (err) {
        this.rankLoading = false;
        console.log(err);
      }
    },
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  watch: {
    taskType: {
      handler(val) {
        if (!val) return false;
        this.getRankStatistics();
      },
      immediate: true,
    },
  },
  components: {
    HomeTitle: require('./home-title').default,
  },
};
</script>
