<!--
    * @FileDescription: 人脸抓拍/ 车辆抓拍
    * @Author: H
    * @Date: 2023/10/07
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="grab-page">
    <div class="list-card box-1">
      <!-- <div class="collection paddingIcon">
                <div class="bg"></div>
                <ui-btn-tip class="collection-icon" v-if="itemList.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(itemList, 2)" />
                <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(itemList, 1)" />
            </div> -->
      <div class="img-content">
        <div class="similarity" v-if="itemList.idScore">
          <span class="gerling-num" v-if="itemList.idScore">{{ itemList.idScore }}%</span>
        </div>
        <template>
          <ui-image :src="itemList.traitImg" alt="动态库" @click.native="faceDetailFn(itemList)" />
          <b class="shade vehicle" v-if="itemList.plateNo">
            <plateNumber :plateNo="itemList.plateNo" :color="itemList.plateColor" size='mini'></plateNumber>
          </b>
        </template>
      </div>
      <!-- 动态库 -->
      <div class="bottom-info">
        <time>
          <Tooltip content="抓拍时间" placement="right" transfer theme="light">
            <i class="iconfont icon-time"></i>
          </Tooltip>
          {{ itemList.absTime }}
        </time>
        <p>
          <Tooltip content="抓拍地点" placement="right" transfer theme="light">
            <i class="iconfont icon-location"></i>
          </Tooltip>
          <!-- <ui-textOver-tips refName="detailAddress" :content="itemList.deviceName"></ui-textOver-tips> -->
          <span class="message ellipsis" v-html="itemList.deviceName" :title="itemList.deviceName?itemList.deviceName.replace(/(<\/?span.*?>)/gi,''):''"></span>
        </p>
      </div>
      <!-- <div class="operate-bar">
                <p class="operate-content">
                    <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(itemList)" />
                    <ui-btn-tip content="分析" icon="icon-fenxi" />
                    <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
                </p>
            </div> -->
    </div>
  </div>
</template>

<script>
import { getVehicleBaseInfoByplateNo } from '@/api/vehicleArchives';
import plateNumber from '@/components/ui-vehicle/index.vue'
export default {
  props: {
    itemList: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: '1'
    }
  },
  components: {
    plateNumber
  },
  data () {
    return {

    }
  },
  methods: {
    singleChecked () {

    },
    tableListFn () {

    },
    faceDetailFn () {
      this.$emit('pageDetail')
    },
    collection () {

    },
    archivesPage (item) {
      if (this.type == 1) {
        if (!item.vid) {
          this.$Message.warning('档案不存在！');
          return;
        }
        const { href } = this.$router.resolve({
          name: 'video-archive',
          query: {
            archiveNo: item.vid,
            source: 'video',
            initialArchiveNo: item.vid
          }
        })
        window.open(href, '_blank')
      } else if (this.type == 2) {
        getVehicleBaseInfoByplateNo(item.plateNo).then(res => {
          if (res.data.archiveNo) {
            const { href } = this.$router.resolve({
              name: 'vehicle-archive',
              query: {
                archiveNo: JSON.stringify(res.data.archiveNo),
                plateNo: JSON.stringify(item.plateNo),
                source: 'car'
              }
            })
            window.open(href, '_blank')
          } else {
            this.$Message.error('尚未查询到该辆车的档案信息')
          }
        })
      }

    }
  },
}
</script>

<style lang='less' scoped>
.grab-page {
  width: 10.7%;
  .list-card {
    position: relative;
    background-color: #f9f9f9;
    margin-bottom: 10px;
    height: min-content;

    box-sizing: border-box;
    .operate-bar {
      height: 30px;
      background: linear-gradient(
        90deg,
        rgba(87, 187, 252, 0.8) 0%,
        #2c86f8 100%
      );
      border-radius: 0px 0px 4px 0px;
      position: absolute;
      right: -100%;
      transition: all 0.3s;
      bottom: 0;
      transform: skewX(-20deg);
      .operate-content {
        padding: 0 5px;
        transform: skewX(20deg);
        height: 100%;
        display: flex;
        align-items: center;
        color: #fff;
        /deep/ .ivu-tooltip-rel {
          padding: 6px;
        }
      }
    }
  }
  .collection {
    width: 30px;
    height: 30px;
    position: absolute;
    z-index: 2;
    top: 3px;
    right: 3px;
    .collection-icon {
      position: absolute;
      top: -1px;
      right: 1px;
      /deep/ .iconfont {
        font-size: 14px;
        color: #fff;
      }
      /deep/ .icon-shoucang {
        color: #888888 !important;
        text-shadow: 0px 1px 0px #e1e1e1;
      }
      /deep/ .icon-yishoucang {
        color: #f29f4c !important;
      }
    }
  }
  .paddingIcon {
    top: 13px;
    right: 13px;
  }
  .empty-card-1 {
    width: 10.7%;
  }
  .empty-card-2 {
    width: 12.2%;
  }
  .box-1 {
    // width: 10.7%;
    overflow: hidden;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #d3d7de;
    box-shadow: 1px 1px 7px #cdcdcd;
    margin-left: 6px;
    &:hover {
      border: 1px solid #2c86f8;
      .operate-bar {
        right: -6px;
        bottom: -1px;
      }
      &:before {
        border-color: #2c86f8;
      }
      .check-box {
        display: inline-block;
      }
    }
    .check-box {
      position: absolute;
      top: 4px;
      left: 4px;
      z-index: 10;
      display: none;
    }
    .checked {
      .check-box {
        display: inline-block;
      }
    }
    .isChecked {
      &:before {
        border-color: #2c86f8;
      }
      .content {
        .check-box {
          display: inline-block;
        }
      }
    }
    .img-content {
      width: 100%;
      position: relative;
      border: 1px solid #cfd6e6;
      height: 167px;
      img {
        width: 100%;
        height: 100%;
        display: block;
      }
      .num,
      .shade {
        position: absolute;
      }
      .num {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 8;
        font-size: 12px;
        padding: 2px 5px;
        border-radius: 4px;
        color: #fff;
        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
      }
      .shade {
        background: rgba(0, 0, 0, 0.7);
        font-size: 12px !important;
        width: 100%;
        bottom: 0;
        left: 0;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        line-height: 18px;
        padding: 3px 0;
      }
    }
    .bottom-info {
      padding-top: 5px;
      time,
      p {
        display: flex;
        align-items: center;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgba(0, 0, 0, 0.8);
        white-space: nowrap;
        width: 100%;
        .iconfont {
          margin-right: 2px;
          color: #888;
        }
      }
    }
  }
  @media only screen and (max-width: 1367px) {
    .box-1 {
      width: 12%;
    }
  }
  .box-2 {
    width: 12.5%;
    height: 120px;
    padding: 3px;
    .content {
      overflow: hidden;
      position: relative;
      height: 100%;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      border: 1px solid #d3d7de;

      .check-box {
        position: absolute;
        top: 4px;
        left: 4px;
        display: none;
      }
      &:hover {
        &:before {
          border-color: #2c86f8;
        }
        .operate-bar {
          right: -6px;
          bottom: -1px;
        }
      }
    }
    &:hover {
      &:before {
        border-color: #2c86f8;
      }
      .content {
        .check-box {
          display: inline-block;
        }
      }
    }
    .identifier-content {
      display: flex;
      color: #7263e1;
      font-size: 16px;
      align-items: center;
      border-bottom: 1px solid #d3d7de;
      padding: 5px 10px;
      font-weight: bold;
      .img-icon {
        width: 20px;
        height: 20px;
        margin-right: 7px;
      }
    }
    .bottom-info {
      padding: 10px;
      time {
        margin-bottom: 4px;
      }
      time,
      p {
        display: flex;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
        line-height: 18px;
        color: rgba(0, 0, 0, 0.8);
        font-size: 12px;
        margin-bottom: 4px;
        span {
          flex: 1;
        }
        .iconfont {
          margin-right: 3px;
          font-size: 12px;
          color: #888;
        }
      }
    }
  }
  .checked {
    .check-box {
      display: inline-block;
    }
  }
  .isChecked {
    &:before {
      border-color: #2c86f8;
    }
    .content {
      .check-box {
        display: inline-block;
      }
    }
  }
}
</style>