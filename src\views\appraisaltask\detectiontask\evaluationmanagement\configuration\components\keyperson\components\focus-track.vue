<template>
  <div class="keyperson">
    <!-- 重点人 -->
    <common-form
      ref="commonForm"
      :label-width="160"
      :form-data="formData"
      :form-model="formModel"
      :module-action="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <template #extract>
        <FormItem
          prop="deviceQueryForm.dayByCapture"
          label="图片抽取范围"
          :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
          v-if="['2', '3'].includes(formData.detectMode)"
        >
          <span class="base-text-color mr-xs">近</span>
          <InputNumber
            v-model.number="formData.dayByCapture"
            :min="0"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span class="base-text-color">天，</span>
          <InputNumber
            v-model.number="formData.startByCapture"
            :min="0"
            :max="23"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span class="base-text-color mr-sm">点至</span>
          <InputNumber
            v-model.number="formData.endByCapture"
            :min="0"
            :max="23"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span class="base-text-color mr-sm">点</span>
        </FormItem>
      </template>
    </common-form>
    <Form ref="formData" class="form-content edit-form" :model="formData" :label-width="160">
      <div>
        <FormItem label="检测模型" class="right-item mb-lg">
          <Select
            v-model="formData.checkModel"
            placeholder="请选择检测模型"
            @on-change="formData.threshold = null"
            class="width-lg"
          >
            <Option value="SC">相似度比较模型</Option>
            <Option value="AD">准确度偏离模型</Option>
          </Select>
        </FormItem>
        <template v-if="formData.checkModel === 'SC'">
          <FormItem label="相似度阈值" class="right-item mb-lg">
            <InputNumber class="width-lg" :max="100" v-model="formData.threshold" placeholder="请输入相似度阈值">
            </InputNumber>
            <span class="base-text-color ml-xs">%</span>
          </FormItem>
          <p class="font-warning text">如果有多数算法判定轨迹照片和静态照片达到相似度阈值，则人员轨迹准确。</p>
        </template>
        <template v-else>
          <FormItem label="偏离程度" class="right-item mb-lg">
            <InputNumber class="width-lg" :max="100" v-model="formData.threshold" placeholder="请输入偏离程度">
            </InputNumber>
            <span class="base-text-color ml-xs">%</span>
          </FormItem>
          <p class="font-warning text">
            如果有多数算法判定轨迹照片和静态照片的相似度，与原始值偏离大于设置阈值，则人员轨迹不准确。
          </p>
        </template>
      </div>
    </Form>
  </div>
</template>
<script>
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      indexRuleList: [],
      formData: {
        timeDelay: null,
        format: 'h',
        threshold: null,
        dataType: '0',
        detectMode: '1',
        dayByCapture: 2,
        startByCapture: 0,
        endByCapture: 23,
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
    };
  },
  watch: {
    moduleAction: {
      handler(val) {
        if (val.config) {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
          };
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 表单提交校验
    async handleSubmit() {
      return await this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
  },
};
</script>
<style lang="less" scoped>
.keyperson {
  margin-bottom: 20px;
  .text {
    margin-left: 160px;
  }
  .dis-select {
    user-select: none;
  }
  .setting {
    color: var(--color-primary);
  }
  .params-lazy-time {
    width: 160px;
  }
  @{_deep} .select-width {
    width: 380px;
  }
}
</style>
