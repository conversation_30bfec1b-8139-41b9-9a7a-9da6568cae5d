import { renderHeaderStatistics } from '@/views/specialassessment/utils/menuConfig.js';

const getTableColumns = (params) => {
  return {
    FACE_QUALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'name',
        slot: 'civilName',
        align: 'center',
        tooltip: true,
        minWidth: 150,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '抓拍大图可用性',
        align: 'center',
        indexIds: [2001, 2002],
        colkey: 'usability',
        children: [
          {
            title: '检测数量',
            keyName: 'detection',
            key: '',
            slot: 'detection',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: 'URL不可用数',
            keyName: 'unqualified',
            key: '',
            slot: 'unqualified',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '图片URL可用率',
            keyName: 'rate',
            key: '',
            slot: 'rate',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
        ],
      },
      {
        title: '抓拍图片合格性',
        align: 'center',
        indexIds: [2003],
        colkey: 'eligibility',
        children: [
          {
            title: '检测数量',
            keyName: 'detection',
            key: '',
            slot: 'detection',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '抓拍不合格数',
            keyName: 'unqualified',
            key: '',
            slot: 'unqualified',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '合格率',
            keyName: 'rate',
            key: '',
            slot: 'rate',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
        ],
      },
      {
        title: '抓拍图片评分',
        align: 'center',
        indexIds: [2026],
        colkey: 'score',
        children: [
          {
            title: '检测数量',
            keyName: 'detection',
            key: '',
            slot: 'detection',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '低评分数',
            keyName: 'unqualified',
            key: '',
            slot: 'unqualified',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '评分率',
            keyName: 'rate',
            key: '',
            slot: 'rate',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
        ],
      },
    ],
    VEHICLE_QUALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'name',
        slot: 'civilName',
        align: 'center',
        tooltip: true,
        minWidth: 150,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '抓拍大图可用性',
        align: 'center',
        indexIds: [3010, 3011],
        colkey: 'usability',
        children: [
          {
            title: '检测数量',
            keyName: 'detection',
            key: '',
            slot: 'detection',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: 'URL不可用数',
            keyName: 'unqualified',
            key: '',
            slot: 'unqualified',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '图片URL可用率',
            keyName: 'rate',
            key: '',
            slot: 'rate',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
        ],
      },
      {
        title: '抓拍数据完整性',
        align: 'center',
        indexIds: [3001, 3002],
        colkey: 'integrity',
        children: [
          {
            title: '检测数量',
            keyName: 'detection',
            key: '',
            slot: 'detection',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '图片属性不完整数',
            keyName: 'unqualified',
            key: '',
            slot: 'unqualified',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '完整率',
            keyName: 'rate',
            key: '',
            slot: 'rate',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
        ],
      },
      {
        title: '抓拍数据准确性',
        align: 'center',
        indexIds: [3003, 3004],
        colkey: 'accuracy',
        children: [
          {
            title: '检测数量',
            keyName: 'detection',
            key: '',
            slot: 'detection',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '图片属性不准确数',
            keyName: 'unqualified',
            key: '',
            slot: 'unqualified',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
          {
            title: '准确率',
            keyName: 'rate',
            key: '',
            slot: 'rate',
            align: 'center',
            minWidth: 100,
            sortable: 'custom',
          },
        ],
      },
    ],
  };
};
export { getTableColumns };
