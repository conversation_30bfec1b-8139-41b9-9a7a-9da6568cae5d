<template>
  <div class="device-tags">
    <span class="close pointer" @click="$emit('close')">X</span>
    <div class="dom-title f-16">设备标签</div>
    <div class="img-box mt-sm"></div>
    <draw-echarts :echart-option="echartPersonnelBar" :echart-style="ringStyle" ref="barChart" class="charts">
    </draw-echarts>
  </div>
</template>
<script>
export default {
  props: {
    tagList: {
      default: () => [],
    },
  },
  data() {
    return {
      echartPersonnelBar: {},
      ringStyle: {
        width: '100%',
        height: '100%',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {
    tagList: {
      handler(val) {
        const data = val.map((item) => {
          let one = {
            value: 100,
            name: item.tagName,
          };
          return one;
        });
        this.$nextTick(() => {
          this.echartPersonnelBar = this.$util.doEcharts.echartPieOption(data);
        });
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.device-tags {
  padding: 10px;
  background-color: rgba(27, 59, 101, 0.8);
  border: 1px solid #1a82be;
  border-radius: 8px;
  z-index: 50;
  width: 395px;
  height: 300px;
  position: relative;
  .dom-title {
    color: @font-color-link;
    border-left: 4px solid @font-color-link;
    padding-left: 10px;
  }
  .close {
    position: absolute;
    top: 10px;
    right: 15px;
    color: @font-color-link;
    z-index: 1;
  }
  .charts {
    position: absolute;
    top: 0px;
  }
  .img-box {
    padding: 20px 80px;
    position: relative;
    width: 160px;
    height: 160px;
    background: url('../../../../assets/img/common/device.png');
    background-size: 100% 100%;
    margin: 40px auto;
  }
}
</style>
