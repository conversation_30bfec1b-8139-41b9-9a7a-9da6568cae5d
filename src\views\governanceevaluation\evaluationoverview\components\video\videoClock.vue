<template>
  <!-- 视频时钟准确 -->
  <div class="videoClock" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="true" :isIconBg="true"></statistics>
      <!-- <div
        class="information-echart"
        v-ui-loading="{ loading: echartsLoading, tableData: echartData }"
      >
        <draw-echarts
          v-if="echartData.length != 0"
          :echart-option="determinantEchart"
          :echart-style="ringStyle"
          ref="attributeChart"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div> -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                  item.rank
                }}</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                  <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                  <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="item.rank == 1">{{ item.regionName }}</span>
                  <span v-if="item.rank == 2">{{ item.regionName }}</span>
                  <span v-if="item.rank == 3">{{ item.regionName }}</span>
                  <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                    item.regionName
                  }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.standardsValue }}%</span>
              </div>

              <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                  item.rankRise || 0
                }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-yichangshujuliebiao f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div>
          <!-- v-permission="{ route: 'evaluationmanagement', permission: 'batchrecheck' }" -->
          <!-- <Button type="primary" class="btn_search mr-md" @click="batchInspection">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs">批量复检</span>
          </Button> -->
          <Button type="primary" class="btn_search" :loading="exportLoading" @click="getExport">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs">导出</span>
          </Button>
        </div>
      </div>
      <div class="search-wrapper">
        <ui-label class="mr-lg" label="设备编码" width="70">
          <Input class="input-width" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label label="设备名称" :width="70" class="mr-lg">
          <Input class="input-width" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
        </ui-label>
        <ui-label label="检测结果" :width="70" class="mr-lg">
          <Select v-model="searchData.qualified" clearable placeholder="请选择检测结果" class="width-input">
            <Option :value="1">合格</Option>
            <Option :value="2">不合格</Option>
            <Option :value="3">无法检测</Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="fl btns" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button type="default" class="mr-sm" @click="reast"> 重置 </Button>
        </ui-label>
      </div>
      <div class="list">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <template #detectionMode="{ row }">
            {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
          </template>
          <template #clockSkew="{ row }">
            <span>{{ row.clockSkew || 0 }} s</span>
          </template>

          <template #deviceId="{ row }">
            <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
              row.deviceId
            }}</span>
          </template>
          <template #qualified="{ row }">
            <span
              class="check-status"
              :class="[
                row.qualified === '1' ? 'bg-b77a2a' : '',
                row.qualified === '2' ? 'bg-17a8a8' : '',
                row.qualified === '3' ? 'bg-D66418' : '',
              ]"
            >
              {{ handleCheckStatus(row.qualified) }}
            </span>
            <span
              v-permission="{
                route: $route.name,
                permission: 'artificialreviewr',
              }"
              class="ml-sm"
              v-if="row.dataMode === '3'"
            >
              (人工)
            </span>
          </template>
          <template #videoStartTime="{ row }">
            <span>
              {{ !row.videoStartTime ? '--' : row.videoStartTime }}
            </span>
          </template>
          <template #startTime="{ row }">
            <span>
              {{ !row.startTime ? '--' : row.startTime }}
            </span>
          </template>
          <template #ntpTime="{ row }">
            <span>
              {{ row.ntpTime ? row.ntpTime : '--' }}
            </span>
          </template>
          <template #phyStatus="{ row }">
            <span>
              {{ !row.phyStatus ? '--' : row.phyStatusText }}
            </span>
          </template>
          <template #reason="{ row }">
            <Tooltip :content="row.reason" transfer max-width="150">
              {{ row.reason }}
            </Tooltip>
          </template>
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
          <template #option="{ row }">
            <div class="boxCenter">
              <ui-btn-tip
                icon="icon-bofangshipin"
                content="播放视频"
                @click.native="clickRow(row)"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip
                icon="icon-chakanjietu"
                content="查看截图"
                v-if="row.isScreenShot === 1 && row.screenShot"
                @click.native="viewResult(row)"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip class="mr-sm" v-else icon="icon-chakanjietu" content="查看截图" disabled></ui-btn-tip>
              <!--  v-permission="{ route: 'evaluationmanagement', permission: 'recheck' }" -->
              <!-- <ui-btn-tip
                icon="icon-fujian"
                content="复检"
                @click.native="viewRecheck(row)"
                class="mr-sm"
              ></ui-btn-tip> -->
              <!--   v-permission="{ route: 'evaluationmanagement', permission: 'artificialreviewr' }" -->
              <ui-btn-tip
                v-if="!isFormCascade()"
                icon="icon-rengongfujian"
                class="mr-sm"
                content="人工复核"
                v-permission="{
                  route: $route.name,
                  permission: 'artificialreviewr',
                }"
                @click.native="artificialReview(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                class="mr-sm f-16"
                icon="icon-tianjiabiaoqian"
                content="添加标签"
                @click.native="addTags(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
    <ArtificialReview ref="result" :artificialRow="artificialRow"></ArtificialReview>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="margin-top: -15px">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>

    <RecheckModal ref="recheckModal"></RecheckModal>
    <!--    <BatchInspection ref="batchInspection" :paramsData="paramsList"></BatchInspection>-->
    <ArtificialReview ref="artificialReview" title="人工复核" :artificialRow="artificialRow">
      <div slot="search" class="mt-md">
        <ui-label class="block" label="人工复核:" :width="80">
          <RadioGroup v-model="artificialData.qualified">
            <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">{{ item.key }}</Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="block" label=" " :width="80">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reason"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </ui-label>
      </div>
      <div slot="footer" class="footer">
        <Button v-if="currentNum > 0" type="primary" class="plr-30" @click="preReview">上一条</Button>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
        <Button v-if="currentNum < searchData.totalCount - 1" type="primary" class="plr-30" @click="nextReview"
          >下一条</Button
        >
      </div>
    </ArtificialReview>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less" scoped>
.videoClock {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      // width: 755px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 18px;
    }
    .information-echart {
      display: inline-block;
      width: 650px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .echarts-box {
        width: 100%;
        height: 100% !important;
        .charts {
          width: 100%;
          height: 100% !important;
        }
      }
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      padding-right: 2px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      //.no-data {
      //  position: absolute;
      //  top: 50%;
      //  left: 50%;
      //}
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
  .search-wrapper {
    margin-top: 10px;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .input-width {
      width: 176px;
    }
    .ui-label {
      margin-bottom: 10px;
    }
  }
  /deep/.conter-center {
    padding: 0;
    width: 100%;
  }
  /deep/.statistics-ul {
    li {
      height: 210px !important;
      width: 19.42% !important;
    }
  }
  /deep/.f-55 {
    font-size: 55px !important;
  }
  .boxCenter {
    display: flex;
    justify-content: center;
  }
  .color_qualified {
    color: #13b13d;
  }
  .color_unqualified {
    color: #e44f22;
  }
  .desc {
    width: 925px;
  }
}
</style>

<script>
import vedio from '@/config/api/vedio-threm';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import downLoadTips from '@/mixins/download-tips';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [downLoadTips],
  name: 'basic-information',
  data() {
    return {
      qualifiedList: [
        { key: '时钟准确', value: '1' },
        { key: '时钟不准确', value: '2' },
      ],
      artificialData: { qualified: '1', reason: '' },
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      determinantEchart: {},
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticsList: [
        {
          name: '视频监控设备总数',
          value: 0,
          icon: 'icon-shipinjiankongshebeizongshu-011',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'deviceCount',
        },
        {
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-shijiceshebeishuliang-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'evaluatingCount',
        },
        {
          name: '设备时钟不准确数量',
          value: 0,
          icon: 'icon-shebeishizhongbuzhunqueshuliang-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'evaluatingFailedCount',
        },
        {
          name: '无法检测设备数量',
          value: 0,
          icon: 'icon-wufajianceshebeishuliang-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          textColor: 'color5',
          type: 'number',
          key: 'unableEvaluatingCount',
        },
        {
          name: '普通时钟准确率',
          value: 0,
          icon: 'icon-zhongdianputongshizhongzhunqueshuai-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
          key: 'resultValueFormat',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        { title: '设备物理状态 ', slot: 'phyStatus', minWidth: 120 },
        // { title: '监控点位类型', key: 'orgName', minWidth: 150, tooltip: true },
        {
          title: '检测方式',
          slot: 'detectionMode',
          minWidth: 150,
          tooltip: true,
        },
        { title: '检测结果', slot: 'qualified', minWidth: 150, tooltip: true },
        { title: '原因', key: 'reason', minWidth: 150, tooltip: true },
        { title: '检测时间', slot: 'videoStartTime', minWidth: 150, tooltip: true },
        {
          title: '设备时间',
          slot: 'startTime',
          minWidth: 150,
          tooltip: true,
        },
        { title: '对应标准时间', slot: 'ntpTime', minWidth: 150, tooltip: true },
        { title: '时间误差', slot: 'clockSkew', minWidth: 150, tooltip: true },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          minWidth: 130,
          tooltip: true,
          fixed: 'right',
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        deviceName: '',
        deviceId: '',
        // customParameters: {
        //   qualified: '',
        // },
        qualified: '',
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      errorMessages: [],
      // exportList: [
      //   { name: '导出设备总表', type: false },
      //   { name: '按异常原因导出分表', type: true },
      // ],
      exportName: '',
      videoUrl: '',
      videoVisible: false,
      videoStyles: {
        width: '5rem',
      },
      contentClientHeight: 0,
      echartsLoading: false,
      artificialRow: {},
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      currentNum: 0, // 当前点击人工复核的索引值
    };
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
    this.getTagList();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    isFormCascade() {
      //级联清单 跳转 不需要 人工复核
      return this.$route.name === 'cascadelist';
    },
    handleCheckStatus(row) {
      const flag = {
        1: '合格',
        2: '不合格',
        3: '无法检测',
      };
      return flag[row];
    },
    // 复检
    viewRecheck(row) {
      this.$refs.recheckModal.init(row);
    },
    // 人工复核
    artificialReview(row) {
      this.currentNum = row._index;
      this.artificialData.qualified = row.qualified;
      this.artificialData.reason = row.reason;
      this.$refs.artificialReview.init();
      this.artificialRow = row;
    },

    async artificial() {
      let data = {
        data: {
          id: this.artificialRow.id,
          qualified: this.artificialData.qualified,
          reason: this.artificialData.reason,
        },
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.getTableData();
        // this.$refs.artificialReview.hide()
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    // 批量复检
    batchInspection() {
      this.$refs.batchInspection.init();
    },
    // onClickIndex(val) {
    //   this.exportList.map((item) => {
    //     if (val === item.name) {
    //       this.exportName = item.type
    //     }
    //   })

    //   this.getExport()
    // },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        multiSheet: this.exportName,
        errorMessages: this.errorMessages,
        orgRegionCode: this.paramsList.orgRegionCode,
        displayType: this.paramsList.displayType,
        customParameters: {
          ...this.searchData,
          totalCount: undefined,
          pageNum: undefined,
          pageSize: undefined,
        },
      };
      try {
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 柱状图统计
    async getGraphsInfo() {
      this.echartsLoading = true;
      let data = {
        regionCode: this.paramsList.regionCode,
        rootResultIds: this.paramsList.rootResultIds,
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data;
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        console.log(err);
        this.echartsLoading = false;
      }
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          orgRegionCode: this.paramsList.orgRegionCode,
          displayType: this.paramsList.displayType,
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          customParameters: {
            qualified: this.searchData.qualified,
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
          },
        };
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // let res = await this.$http.post(evaluationoverview.getDetailData, params)
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
      } finally {
        this.loading = false;
      }
    },
    //视频播放
    async clickRow(row) {
      try {
        // this.isLive = true
        // this.loadingVideo = true
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          vedio.getplay,
          data,
          'post',
          this.$route.query.cascadeId,
          {},
          this.global.SERVER.stream,
        );
        // let res = await this.$http.post(vedio.getplay, data)
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        // this.loadingVideo = false
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    async onCancel() {
      this.videoUrl = '';
      /**------级联清单特殊替换处理接口(后端转发)-------**/
      await superiorinjectfunc(
        this,
        vedio.stop + this.playDeviceCode,
        {},
        'post',
        this.$route.query.cascadeId,
        {},
        this.global.SERVER.stream,
      );
      // this.$http.post(vedio.stop + this.playDeviceCode)
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValueFormat' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValueFormat' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    //统计
    // async getChartsData() {
    //   try {
    //     let params = {
    //       indexId: this.paramsList.indexId,
    //       rootId: this.paramsList.resultId,
    //       orgCode: this.paramsList.regionCode,
    //     }
    //     let res = await this.$http.post(api.queryEvaluatingVideoCount, params)
    //     this.statisticalList = res.data.data
    //     this.statisticsList[0].value = this.statisticalList.deviceCount
    //     this.statisticsList[1].value = this.statisticalList.evaluatingCount
    //     this.statisticsList[2].value = this.statisticalList.evaluatingFailedCount
    //     this.statisticsList[3].value = this.statisticalList.evaluatingSuccessCount
    //     this.statisticsList[4].value = this.statisticalList.unableEvaluatingCount
    //     if (this.paramsList.indexId === 21) {
    //       this.statisticsList[8].name = '重点实时视频可调阅率'
    //     }
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    // 查看结果
    viewResult(row) {
      this.artificialRow = row;
      this.$refs.result.init();
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
        deviceInfoId: this.deviceInfoId,
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    // selectInfo(val) {
    //   this.errorMessages = val.map((item) => {
    //     return item.name
    //   })
    //   this.getTableData()
    // },
    selectInfo(infoList) {
      this.searchData.pageNumber = 1;
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.getTableData();
      //   this.searchData.tagIds = infoList.map((row) => {
      //     return row.id;
      //   });
      //   this.search();
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
    },
    search() {
      this.searchData = { ...this.searchData, pageNum: 1 };
      this.getTableData();
    },
    reast() {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        qualified: '',
        deviceName: '',
        deviceId: '',
      };
      this.getTableData();
    },
    preReview() {
      if (this.currentNum > 0 && this.currentNum < this.searchData.totalCount) {
        this.currentNum--;
      }
      this.artificialRow = this.tableData[this.currentNum];
      this.artificialData.qualified = this.tableData[this.currentNum].qualified;
      this.artificialData.reason = this.tableData[this.currentNum].reason;
    },
    nextReview() {
      if (this.currentNum < this.searchData.totalCount) {
        this.currentNum++;
      }
      this.artificialRow = this.tableData[this.currentNum];
      this.artificialData.qualified = this.tableData[this.currentNum].qualified;
      this.artificialData.reason = this.tableData[this.currentNum].reason;
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          if (this.paramsList.indexId === 4004) {
            this.statisticsList[4].name = '重点时钟准确率';
          } else {
            this.statisticsList[4].name = '普通时钟准确率';
          }
          this.getTableData(); //表格
          // this.getSelectTabs()
          // this.getChartsData()
          // this.getGraphsInfo() //柱状图
          // this.queryDeviceCheckColumnReports() //头部中间echarts
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    EasyPlayer: require('@/components/EasyPlayer').default,
    RecheckModal: require('./components/recheck-modal.vue').default,
    ArtificialReview: require('./components/artificial-review.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
