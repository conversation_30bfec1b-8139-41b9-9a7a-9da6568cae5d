<!--
    * @FileDescription: 人体
    * @Author: H
    * @Date: 2024/2/26
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-23 16:19:20
 -->
<template>
  <section class="main-container">
    <div class="search-bar">
      <search-human
        ref="searchBar"
        :queryParam.sync="queryParam"
        :visible.sync="visible"
        @search="searchHandle"
        @reset="resetHandle"
        @imgChange="imgChange"
      />
    </div>
    <div class="table-container">
      <div class="data-export">
        <Checkbox @on-change="checkAllHandler" v-model="checkAll"
          >全选</Checkbox
        >
        <div class="export-box">
          <Button class="mr" @click="handleExport($event)" size="small">
            <ui-icon type="daoru" color="#2C86F8"></ui-icon>
            导出
          </Button>
          <exportBox
            ref="exportbox"
            v-if="exportShow"
            @confirm="confirm"
            @cancel="exportShow = false"
          ></exportBox>
        </div>
        <Button
          class="mr"
          :type="queryParam.sortField == 'similarity' ? 'primary' : 'default'"
          @click="handleAdvancedSort('similarity')"
          size="small"
          v-if="this.queryParam.features && this.queryParam.features.length > 0"
        >
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
        <Button
          class="mr"
          :type="queryParam.sortField == 'absTime' ? 'primary' : 'default'"
          @click="handleAdvancedSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <!-- <Button @click="dataAboveMapHandler" size="small" style="float: right">
                    <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
                    数据上图
                </Button> -->
        <Button
          @click="handleFalls"
          size="small"
          style="float: right; margin-right: 10px"
        >
          {{ fallsPage ? "传统翻页版本" : "瀑布流版本" }}
        </Button>
      </div>
      <div class="table-container-box">
        <div class="table-content" @scroll="handleScroll">
          <div
            class="list-card box-1"
            v-for="(item, index) in dataList"
            :key="index"
            :class="{ checked: item.isChecked }"
          >
            <div class="collection paddingIcon">
              <div class="bg"></div>
              <ui-btn-tip
                class="collection-icon"
                v-if="item.myFavorite == '1'"
                content="取消收藏"
                icon="icon-yishoucang"
                transfer
                @click.native="collection(item, 2)"
              />
              <ui-btn-tip
                class="collection-icon"
                v-else
                content="收藏"
                icon="icon-shoucang"
                transfer
                @click.native="collection(item, 1)"
              />
            </div>
            <Checkbox
              class="check-box"
              v-model="item.isChecked"
              @on-change="(e) => checkHandler(e, index)"
            ></Checkbox>
            <p class="img-content">
              <span
                class="num"
                :class="{
                  'gerling-num': queryParam.algorithmVendorType == 'SW',
                }"
                v-if="item.similarity"
                >{{ item.similarity || "0" }}%</span
              >
              <ui-image
                :src="item.traitImg"
                alt="动态库"
                @click.native="handleDetail(item, index)"
              />
              <!-- <img :src="item.traitImg" alt="" v-viewer /> -->
            </p>
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.absTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i
                    v-if="item.taskType === '3'"
                    class="iconfont icon-a-filemode"
                    style="color: #08bba2"
                  ></i>
                  <i v-else class="iconfont icon-location"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{ item.deviceName }}</span>
              </p>
            </div>
            <div class="fast-operation-bar">
              <Poptip trigger="hover" placement="right-start">
                <i class="iconfont icon-gengduo"></i>
                <div class="mark-poptip" slot="content">
                  <p @click="archivesPage(item)">
                    <i class="iconfont icon-renti1"></i>以图搜图
                  </p>
                  <p @click="openDirectModel(item)">
                    <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                  </p>
                  <p @click="handleTargetAdd(item)">
                    <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
                  </p>
                </div>
              </Poptip>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 9 - (dataList.length % 9)"
            :key="index + 'demo'"
          ></div>
          <div class="gobacktop" @click="toTop">
            <div class="top-icon">
              <Icon type="md-download" />
            </div>
            <div class="top-text">
              <p>回到</p>
              <p>顶部</p>
            </div>
          </div>
        </div>
        <ui-empty v-if="dataList.length === 0 && !listLoading"></ui-empty>
        <ui-loading v-if="listLoading"></ui-loading>
        <!-- 分页 -->
        <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[27, 54, 81, 108]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <details-modal
      v-show="humanShow"
      ref="humanbody"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="humanShow = false"
    >
    </details-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="loadCancel"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="wranModalShow"
      title="提示"
      :r-width="500"
      @onCancel="onCancel"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
    <direction-model ref="directionModel"></direction-model>
  </section>
</template>
<script>
import { myMixins } from "../mixin/index.js";
import searchHuman from "../components/search-human";
import detailsModal from "@/components/detail/details-modal.vue";
import hlModal from "@/components/modal/index.vue";
import {
  queryDeviceList,
  humanDownload,
  taskView,
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { queryHumanRecordSearch } from "@/api/viewParsingLibrary";
import { addCollection, deleteMyFavorite } from "@/api/user";
import directionModel from "@/views/wisdom-cloud-search/search-center/advanced-search/components/direction-model";
import exportBox from "@/views/wisdom-cloud-search/search-center/components/export/export-box.vue";
export default {
  components: {
    searchHuman,
    detailsModal,
    directionModel,
    exportBox,
    hlModal,
  },
  mixins: [myMixins], //全局的mixin
  data() {
    return {
      queryParam: {
        urlList: [],
        selectDeviceList: [], // 选择设备
        selectTaskList: [], // 选择任务
        selectFileList: [], // 选择文件
        features: [],
        selectType: "",
        imageBases: [],
        similarity: 80,
        upperTexture: "", //上身纹理
        sleeveStyle: "", //上身袖子类型
        upperColor: "", //上身颜色
        lowerStyle: "", //下身类型
        lowerColor: "", //下身颜色
        shoesStyle: "", //鞋子类别
        shoesColor: "", //鞋子颜色
        gender: "", //性别
        hairStyle: "", //发型
        behavior: "", //行为
        appendix: "", //附属物
      },
      visible: false,
      dataList: [],
      listLoading: false,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      fallsPage: false,
      checkAll: false,
      exportShow: false,
      humanShow: false,
      modalShow: false,
      wranModalShow: false,
      timeUpDown: false,
      similUpDown: false,
      maybeTime: 0,
    };
  },
  watch: {
    exportShow(val) {
      //点击空白处隐藏
      if (val) {
        document.addEventListener("click", () => {
          this.exportShow = false;
        });
      } else {
        document.addEventListener("click", () => {});
      }
    },
  },
  created() {
    this.setLayoutNoPadding(true);
  },
  mounted() {},
  methods: {
    // 搜索
    searchHandle() {
      this.refreshDataList(true);
    },
    refreshDataList(isRefreshAll = false, noRest = false) {
      if (this.fallsPage) {
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 48,
        };
        this.dataList = [];
      } else {
        const { pageNumber, pageSize } = this.pageInfo;
        this.pageInfo = {
          pageNumber: isRefreshAll ? 1 : pageNumber,
          pageSize: isRefreshAll ? 27 : pageSize,
        };
        this.mayRequest = false;
      }
      this.queryList(false, noRest);
    },
    async getDataList(page = 0) {
      this.dataList = [];
      this.listLoading = true;
      let params = JSON.parse(JSON.stringify(this.queryParam));
      delete params.urlList;
      delete params.selectDeviceList;
      delete params.selectFileList;
      delete params.selectTaskList;
      params.similarity = params.similarity / 100;
      queryHumanRecordSearch({ ...params, ...this.pageInfo })
        .then((res) => {
          this.total = res.data.total;
          this.dataList = res.data.entities || [];
          if (page == 1) {
            this.$refs.humanbody.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.humanbody.nextPage(this.dataList);
          }
          this.logParams(
            params,
            {
              muen: "视图解析库",
              name: "查询视图解析库人体",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    /**
     *
     * @param {*} page 详情翻页
     * @param {*} noRest 排序
     */
    queryList(page = false, noRest = false) {
      let deviceIds = this.queryParam.selectDeviceList.map((item) => {
        return item.deviceGbId;
      });
      let files = this.queryParam.selectFileList.map((item) => {
        return item.id;
      });
      this.queryParam.deviceIds =
        this.queryParam.selectType == "3" ? files : deviceIds;
      let taskIds = this.queryParam.selectTaskList.map((item) => {
        return item.id;
      });
      this.queryParam.taskIds = taskIds;
      this.queryParam.taskType = this.queryParam.selectType;
      if (this.queryParam.features.length > 0 && !noRest) {
        this.queryParam.order = "desc";
        this.queryParam.sortField = "similarity";
      } else if (
        this.queryParam.features.length == 0 &&
        this.queryParam.sortField == "similarity" &&
        !noRest
      ) {
        this.queryParam.order = "desc";
        this.queryParam.sortField = "absTime";
      }
      if (this.fallsPage) {
        this.cardScroll(page);
      } else {
        this.getDataList(page);
      }
    },
    cardScroll(page = 0) {
      let params = JSON.parse(JSON.stringify(this.queryParam));
      delete params.urlList;
      delete params.selectDeviceList;
      delete params.selectFileList;
      delete params.selectTaskList;
      params.similarity = params.similarity / 100;
      this.listLoading = true;
      queryHumanRecordSearch({ ...params, ...this.pageInfo })
        .then((res) => {
          if (page == 2) {
            this.$refs.humanbody.nextPage(res.data.entities || []);
          }
          if (res.data.entities.length == 0) {
            this.mayRequest = false;
          } else {
            this.dataList.push(...res.data.entities);
            this.logParams(
              params,
              {
                muen: "视图解析库",
                name: "查询视图解析库人体",
                type: "4",
              },
              this.queryParam.selectDeviceList
            );
            if (this.pageInfo.pageNumber <= 1) {
              this.pageInfo.pageNumber += 1;
              this.queryList(false, true);
            }
            this.mayRequest = true;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 图片
    imgChange() {},
    // 重置
    resetHandle() {
      this.refreshDataList(true);
    },
    checkHandler(e, i) {
      this.dataList[i].isChecked = e;
      this.checkAll =
        this.dataList.filter((e) => e.isChecked).length === this.dataList.length
          ? true
          : false;
    },
    // 详情
    handleDetail(row, index) {
      this.humanShow = true;
      this.$refs.humanbody.init(
        row,
        this.dataList,
        index,
        1,
        this.pageInfo.pageNumber
      );
    },
    collection(item, flag) {
      var param = {
        favoriteObjectId: item.recordId,
        favoriteObjectType: 26,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$set(item, "myFavorite", "1");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$set(item, "myFavorite", "2");
        });
      }
    },
    // 导出
    handleExport($event) {
      $event.stopPropagation();
      this.exportShow = !this.exportShow;
    },
    loadCancel() {
      this.modalShow = false;
      this.wranModalShow = true;
    },
    onCancel() {
      this.modalShow = true;
      this.wranModalShow = false;
    },
    onOk() {
      this.modalShow = false;
      this.wranModalShow = false;
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },
    confirm(param) {
      let funparams = this.queryParam;
      let params = {};
      if (param.type == "1") {
        let list = this.dataList.filter((e) => e.isChecked);
        if (list.length > 0) {
          let ids = list.map((item) => item.recordId);
          params = {
            ids,
            downloadPics: param.downloadPics,
            downloadSize: null,
            ...funparams,
          };
          this.exportShow = false;
          this.modalShow = true;
        } else {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params = {
          ids: [],
          downloadPics: param.downloadPics,
          downloadSize: param.downloadSize,
          ...funparams,
        };
        this.exportShow = false;
        this.modalShow = true;
      }
      humanDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {});
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.refreshDataList(false, true);
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.refreshDataList(false, true);
    },
    checkAllHandler(val) {
      this.dataList = this.dataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    // 瀑布流展示
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.refreshDataList(true, true);
    },
    archivesPage(row) {
      const { href } = this.$router.resolve({
        path: "/viewanalysis/viewParsingLibrary?sectionName=humanBodyContent&noMenu=1",
        query: {
          imgUrl: row.traitImg,
        },
      });
      window.open(href, "_blank");
    },
    openDirectModel(row) {
      const { geoPoint, deviceName } = { ...row };
      if (!geoPoint) {
        this.$Message.error("暂无坐标");
        return;
      }
      this.$refs.directionModel.show({ geoPoint, deviceName });
    },
    // 目标添加
    handleTargetAdd(row) {
      //   let fileData = new FormData();
      //   fileData.append("algorithmType", 3);
      //   fileData.append("fileUrl", row.traitImg);
      //   picturePick(fileData).then((res) => {
      //     let params = res.data[0];
      //     let urlList = {
      //       fileUrl: "data:image/jpeg;base64," + params.imageBase,
      //       feature: params.feature,
      //       imageBase: params.imageBase,
      //     };
      //     this.$refs.searchBar.urlImgList([urlList, ""], 2);
      //     this.refreshDataList(true);
      //   });
      let fileData = new FormData();
      fileData.append("algorithmType", 3);
      fileData.append("fileUrl", row.traitImg);
      picturePick(fileData).then(async (res) => {
        if (res.data && res.data.length > 0) {
          const response = await this.getBase64ByImageCoordinate(res.data[0]);
          let urlList = {
            fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
            feature: response.data.feature,
            imageBase: response.data.imageBase,
          };
          this.$refs.searchBar.urlImgList([urlList, ""], 2);
          this.refreshDataList(true);
        } else {
          this.$Message.warning("未提取到特征，暂无查询结果");
        }
      });
    },
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },
    /**
     * 上一个
     */
    prePage(pageNum) {
      if (pageNum < 1 || this.fallsPage) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        if (!this.fallsPage) {
          this.queryList(1, true);
        }
      }
    },
    /**
     * 下一个
     */
    async nextPage(pageNum) {
      let size = this.pageInfo.pageSize;
      if (this.total <= pageNum * size) {
        this.$Message.warning("已经是最后一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        this.queryList(2, true);
      }
    },
    toTop() {
      let scrollTop = document.querySelector(".table-content");
      scrollTop.scrollTo(0, 0);
    },
    handleScroll() {
      // 距离顶部距离
      let scrollTop = document.querySelector(".table-content").scrollTop;
      // 可视区的高度
      let windowHelght = document.querySelector(".table-content").clientHeight;
      // 滚动条的总高度
      let scrollHeight = document.querySelector(".table-content").scrollHeight;
      // 计算底部距离
      let _dis = scrollTop + windowHelght;
      if (this.pageInfo.pageNumber >= 6) {
        this.mayRequest = false;
        return;
      }
      if (_dis + 1000 > scrollHeight && this.mayRequest && !this.listLoading) {
        this.pageInfo.pageNumber += 1;
        this.queryList(false, true);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
@import "../../../wisdom-cloud-search/search-center/advanced-search/pages/style/index";
</style>
