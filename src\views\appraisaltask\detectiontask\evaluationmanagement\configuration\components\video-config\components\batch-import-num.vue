<template>
  <div class="batch-import-num">
    <slot name="filtermodule"></slot>
    <div class="batch-import-num-container">
      <div class="tree-title base-text-color">
        <span class="org-name">行政区划名称</span>
        <span
          v-for="(astItem, astIndex) in assessmentItems"
          :key="'astIndex' + astIndex"
          :class="['mr-100', `${assessmentItemSize}`]"
          >{{ astItem.title }}</span
        >
        <span class="is-checked">是否考核</span>
        <span class="is-all">全部</span>
      </div>
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          :highlight-current="false"
          node-key="nodeKey"
          :tree-data="areaTreeData"
          :default-props="defaultProps"
          expandAll
          checkStrictly
        >
          <template #label="{ node, data }">
            <div class="flex">
              <div class="vt-middle left-item">{{ data.regionName }}</div>
              <div class="inline right-item">
                <div v-if="data.check && edit">
                  <Input
                    v-for="(asstItem, asstIndex) in assessmentItems"
                    :key="'asstIndex' + asstIndex"
                    :class="['vt-middle', 'mr-100', `${assessmentItemSize}`]"
                    v-model="data[asstItem.key]"
                    :placeholder="`请输入${asstItem.title}`"
                  ></Input>
                </div>
                <div class="vt-middle switch-width">
                  <i-switch v-model="data.check" size="small" @on-change="handleCheck($event, node, data)"></i-switch>
                </div>
                <Checkbox
                  class="vt-middle check-width"
                  v-model="data.checkAll"
                  @on-change="handleCheckAll($event, node, data)"
                  :style="{ visibility: !data.children ? 'hidden' : '' }"
                  >{{ `${data.checkAll ? '取消' : '全部'} ` }}
                </Checkbox>
              </div>
            </div>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'batch-import-num',
  props: {
    assessmentItems: {
      type: Array,
      default: () => [],
    },
    defaultProps: {
      default: () => {
        return {
          label: 'regionName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'regionCode',
    },
    assessmentItemSize: {
      type: String,
      default: 'width-sm',
    },
    edit: {
      type: Boolean,
      default: true,
    },
    // editAssessmentData: {
    //   type: Array,
    //   default: () => []
    // },
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      areaTreeData: [],
      form: {
        number: 1,
      },
      checkedTreeData: [],
    };
  },
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  methods: {
    handleCheck(val, node) {
      this.handleClearInputVal(node.data);
      if (!!node.parent && !!val) {
        this.handleRecursionCheck(node.parent, 'parent', val);
      }
      this.updateTreeData();
    },
    handleCheckAll(val, node) {
      this.$set(node.data, 'check', val);
      this.handleClearInputVal(node.data);
      if (node.parent) {
        this.handleRecursionCheck(node.parent, 'parent', val);
      }
      if (node.childNodes.length) {
        this.handleRecursionCheck(node.childNodes, 'childNodes', val);
      }
      this.updateTreeData();
    },
    handleRecursionCheck(node, key, val) {
      if (Array.isArray(node)) {
        node.forEach((item) => {
          this.$set(item.data, 'check', val);
          this.handleClearInputVal(item.data);
          if (item[key]) {
            this.handleRecursionCheck(item[key], key, val);
          }
        });
        return false;
      }
      this.$set(node.data, 'check', val);
      if (!node[key]) return false;
      this.handleRecursionCheck(node[key], key);
    },
    handleClearInputVal(item) {
      this.assessmentItems.forEach((assessItem) => {
        this.$set(item, assessItem.key, '');
      });
    },
    getCheckedNodes(data) {
      data.forEach((item) => {
        if (item.check) {
          let obj = {
            ...item,
          };
          this.assessmentItems.forEach((assessItem) => {
            obj[assessItem.key] = item[assessItem.key] || '';
          });
          this.checkedTreeData.push(obj);
        }
        if (item.children) this.getCheckedNodes(item.children);
      });
    },
    validateCheckedNodes() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.areaTreeData);
      if (!this.checkedTreeData.length) {
        this.$Message.error('请选择行政区划并设置达标数量');
        return false;
      }
      for (let i = 0; i < this.checkedTreeData.length; i++) {
        for (let j = 0; j < this.assessmentItems.length; j++) {
          let reg = /^[1-9]\d*$/;
          if (
            !this.checkedTreeData[i][this.assessmentItems[j].key] ||
            !reg.test(this.checkedTreeData[i][this.assessmentItems[j].key])
          ) {
            this.$Message.error(`${this.checkedTreeData[i].regionName}${this.assessmentItems[j].title}格式不正确`);
            return false;
          }
        }
      }
      return this.checkedTreeData;
    },
    updateTreeData() {
      this.$emit('updateTreeData', this.areaTreeData);
    },
    // 当树结构的子节点都选中时自动选中全部
    checkedAll(item) {
      // 取消全选时判断树是否有子节点
      if (!!item.children && item.children.length) {
        // 当前节点有字节点，则判断当前节点的子节点数和已选中的子节点数比较，不相等则将当前节点的【checkAll】值置成false
        // 将当前节点的所有子节点扁平化
        const delayeringData = this.$util.common.jsonToArray(JSON.parse(JSON.stringify(item.children)));
        // 在已扁平化的子节点里过滤出已选中的子节点
        const childCheckedLen = delayeringData.filter((childItem) => childItem.check);
        // 将所有子节点数和已选中的子节点数进行比较， 相等则将当前节点的【checkAll】值置成 true ，否则 false
        if (delayeringData.length === childCheckedLen.length) {
          this.$set(item, 'checkAll', true);
        } else {
          this.$set(item, 'checkAll', false);
        }
        // 对当前节点还有子节点的进行递归处理
        item.children.forEach((childItem) => {
          this.checkedAll(childItem);
        });
        // 返回处理后的数据
        return item;
      }
    },
  },
  watch: {
    treeData: {
      handler(val) {
        this.areaTreeData = JSON.parse(JSON.stringify(val));
        this.areaTreeData = this.areaTreeData.map((item) => {
          return this.checkedAll(item);
        });
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>

<style lang="less" scoped>
.batch-import-num {
  &-container {
    border: 1px solid var(--border-color);
    .tree-filter {
      line-height: 34px;
      margin-bottom: 30px;
    }
    .tree-title {
      display: flex;
      width: 100%;
      height: 48px;
      line-height: 48px;
      padding: 0 15px;
      background: var(--bg-table-header-th);
      span {
        display: inline-block;
      }
    }
    .tree-wrapper {
      height: 300px;
      overflow: auto;
      .ui-search-tree {
        height: 100%;
      }
      @{_deep} .el-tree {
        .el-tree-node {
          .el-tree-node__content {
            height: 40px;
            &.has-child-panel {
              margin-bottom: 5px;
            }

            .custom-tree-node {
              line-height: 34px;
            }
            .el-tree-node__expand-icon {
              margin-left: 20px;
              font-size: 16px;
              margin-top: -2px;
            }
          }
        }
      }
    }
  }
}
.flex {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.right-item {
  display: flex;
  align-items: center;
}
.org-name,
.left-item {
  flex: 1;
}
.is-all,
.is-checked,
.switch-width,
.check-width {
  width: 100px;
}
.ipt-item {
  display: flex;
  justify-content: left;
}
.mr-50 {
  margin-right: 50px;
}

.mr-100 {
  margin-right: 100px;
}
</style>
