<template>
  <div class="play-history">
    <ui-loading v-if="loading"/>
    <ul class="search_form_type">
      <li v-for="(item, index) in typeList" :key="index" class="typeTab" :class="{ typeActive: typeIndex == index }" @click="handleTypeClick(index)">
        {{ item.name }}
      </li>
    </ul>
    <div class="operter">
      <div>共<span class="num">{{ total }}</span>条记录</div>
      <div class="clear" v-if="typeIndex == 0" @click="clearAll">清空列表</div>
    </div>
    <div class="history-list">
      <Scroll :on-reach-bottom="handleReachBottom" height="100%" :loading-text="loadingText" :distance-to-edge="10">
        <div v-for="(item, index) in historyList" class="history-list-item" :class="{playing: playingHistoryIds.includes(item.id)}" :key="index" draggable @dblclick="handleNodeClick(item)" @dragstart="handleDragStart(item, $event)">
          <div :class="item.videoType == 'live' ? 'live' : 'vod'">{{ item.videoType == 'live' ? '实时视频' : '历史录像' }}</div>
          <div class="main">
            <div class="top">
              <i v-if="playingHistoryIds.includes(item.id)" class="icon playing-icon"></i>
              <i v-else class="icon iconfont color-bule mr-5" :class="item.ptzType == 1 || item.ptzType == 2 ? 'icon-qiuji' : 'icon-shebeizichan'"></i>
              <div class="name ellipsis" :title="item.deviceName">{{ item.deviceName }}</div>
              <span @click.stop="">
                <Dropdown @on-click="showMore($event, item)">
                  <i class="action iconfont icon-gengduo"></i>
                  <DropdownMenu slot="list">
                    <DropdownItem :name="1">查看视频</DropdownItem>
                    <DropdownItem :name="2">查看档案</DropdownItem>
                    <DropdownItem :name="3">删除记录</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </span>
            </div>
            <div class="time">{{ item.palayTime }}</div>
          </div>
        </div>
      </Scroll>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { getPlaybackPageList, removePlayback, queryCameraDeviceList, clearPlayback } from '@/api/player'
export default {
  data() {
    return {
      typeList: [{ name: '全部' }, { name: '实时' }, { name: '录像' }],
      typeIndex: 0,
      historyList: [],
      loadingText: '加载中',
      pageInfo: {
        pageNumber: 1,
        pageSize: 10
      },
      total: 0,
      loading: false,
    }
  },
  props: {
    playingHistoryIds: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    ...mapGetters({ 
      userInfo: "userInfo"
    })
  },
  mounted() {
    this.getList()
  },
  methods: {
    handleTypeClick(index) {
      this.typeIndex = index
      this.getList()
    },
    getList() {
      this.loading = true
      this.pageInfo.pageNumber = 1
      getPlaybackPageList({userId: this.userInfo.id, ...this.pageInfo, videoType: this.typeIndex == 1 ? 'live' : this.typeIndex == 2 ? 'vod' : ''}).then(res => {
        this.historyList = res.data.entities
        this.total = res.data.total
        this.loading = false
      })
    },
    handleReachBottom () {
      let totalPage = Math.ceil(this.total / this.pageInfo.pageSize)
      this.loadingText = '加载中'
      if (this.pageInfo.pageNumber >= totalPage) {
        this.loadingText = '已经是最后一页了'
        return
      }
      this.pageInfo.pageNumber = this.pageInfo.pageNumber + 1
      return getPlaybackPageList({userId: this.userInfo.id, ...this.pageInfo, videoType: this.typeIndex == 1 ? 'live' : this.typeIndex == 2 ? 'vod' : ''}).then(res => {
        let list = res.data.entities
        this.historyList = this.historyList.concat(list)
      })
    },
    showMore(name, item) {
      switch (name) {
        // 查看视频
        case 1:
          this.handleNodeClick(item)
          break
        // 查看档案
        case 2:
          const { href } = this.$router.resolve({
            name: 'device-archive',
            query: { archiveNo: item.deviceId }
          })
          window.open(href, '_blank')
          break
        // 删除记录
        case 3:
          this.$Modal.confirm({
            title: '提示',
            closable: true,
            content: `确定删除该记录吗？`,
            onOk: () => {
              removePlayback(item.id).then(res => {
                this.pageInfo.pageNumber = 1
                this.getList()
                this.$Message.success(res.msg)
              })
            }
          })
          break
      }
    },
    handleDragStart(item, ev) {
      let {deviceId, videoUrl, deviceName, videoType, histroyStartTime, histroyEndTime } = {...item}
      ev.dataTransfer.setData('text/plain', JSON.stringify({ deviceId, videoUrl, deviceName, histroyStartTime, histroyEndTime, devicetype: liveType, playType: videoType }));
      console.log('drag start', ev.dataTransfer.getData('text/plain'));
    },
    async handleNodeClick(item) {
      if (item.deviceId) {
        let result = await queryCameraDeviceList({deviceId: item.deviceId})
        let obj = {...result.data[0]}
        let {id: historyId, videoUrl, videoType, histroyStartTime, ptzType, histroyEndTime } = {...item}
        this.$emit('handleClick', { ...obj, historyId, videoUrl, ptzType, histroyStartTime, histroyEndTime, devicetype: liveType, playType: videoType })
        if (videoType == 'live') {
          this.queryLog({
            muen: '视频中心',
            name: '播放历史',
            type: '4',
            remark: `查看【${item.deviceName}】实时视频`
          })
        } else {
          this.queryLog({
            muen: '视频中心',
            name: '播放历史',
            type: '4',
            remark: `查看【${item.deviceName}】历史视频`
          })
        }
      }
    },
    clearAll() {
      this.$Modal.confirm({
        title: '提示',
        closable: true,
        content: `确认清空列表吗？`,
        onOk: () => {
          clearPlayback().then(res => {
            this.getList()
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.play-history {
  height: 100%;
  position: relative;
  .search_form {
    &_type {
      display: flex;
      margin: 15px;
      .typeTab {
        border: 1px solid #2c86f8;
        border-left: none;
        background: #ffffff;
        color: #2c86f8;
        width: 50%;
        height: 34px;
        text-align: center;
        line-height: 34px;
        font-size: 14px;
        cursor: pointer;
        &:first-child {
          border-left: 1px solid #2c86f8;
          border-top-left-radius: 4px;
          border-bottom-left-radius: 4px;
        }
        &:last-child {
          border-top-right-radius: 4px;
          border-bottom-right-radius: 4px;
        }
      }
      .typeActive {
        background: #2c86f8;
        color: #fff;
      }
    }
    .btn-group {
      .btnwidth {
        width: 258px;
      }
    }
    .wrapper {
      display: flex;
      margin-bottom: 15px;
      align-items: center;
      .wrapper-title {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        width: 95px;
      }
      .custom-time {
        height: 34px;
        line-height: 34px;
      }
      .wrapper-input {
        flex: 1;
      }
      &:first-child {
        .wrapper-title {
          width: 40px;
        }
      }
      .unit {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        margin-left: 5px;
      }
    }
  }
  .operter {
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
    .num {
      color: #2c86f8;
    }
    .clear {
      cursor: pointer;
    }
  }
  .history-list {
    height: calc(~'100% - 67px');
    overflow: hidden;
    &-item {
      margin: 0 15px 10px 15px;
      display: flex;
      background-color: #f9f9f9;
      cursor: pointer;
      &.playing {
        background-color: rgba(44, 134, 248, 0.1);
      }
      &:hover {
        background-color: rgba(44, 134, 248, 0.1);
      }
      .live,
      .vod {
        color: #fff;
        writing-mode: vertical-rl;
        padding: 3px;
        letter-spacing: 3px;
      }
      .live {
        background: #2c86f8;
      }
      .vod {
        background: #f29f4c;
      }
      .color-bule {
        color: #2c86f8;
      }
      .main {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: calc(~'100% - 24px');
        .top {
          display: flex;
          margin-bottom: 5px;
          font-size: 14px;
          font-weight: 400;
          i.playing-icon {
            width: 18px;
            height: 18px;
            background: url('~@/assets/img/player/playing.gif') 50% no-repeat;
          }
          .icon {
            margin: 0 10px;
          }
          .name {
            flex: 1;
            color: rgba(0, 0, 0, 0.8);
          }
          .action {
            margin: 0 10px 0 5px;
            cursor: pointer;
            color: rgba(136, 136, 136, 0.6);
          }
        }
        .time {
          font-weight: 400;
          color: rgba(0, 0, 0, 0.45);
          margin-left: 37.5px;
        }
      }
    }
  }
  /deep/ .ivu-scroll-wrapper {
    height: 100%;
    position: initial;
    .ivu-scroll-container {
      overflow: auto;
      height: 100%;
    }
  }
}
</style>
