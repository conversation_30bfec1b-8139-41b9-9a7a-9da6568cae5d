<template>
  <div class="second-step-box">
    <div class="top-tips">注：将需要关系挖掘的基础关系拖入左边</div>
    <div class="step-content">
      <div
        class="left-box"
        id="mountNodeSecond"
        @dragover="dragover"
        @drop="drop"
      ></div>
      <div class="right-box">
        <div class="title">基础关系选择</div>
        <div class="nodes-box">
          <div
            class="rlation-item"
            v-for="(item, index) in relationTypeList"
            :key="index"
          >
            <div class="relation-title">{{ item.title }}</div>
            <div class="relation-type-box">
              <div
                class="type-item"
                :class="subItem.select ? 'type-item-active' : ''"
                v-for="(subItem, subIndex) in item.RelationTypeItemList"
                :key="subIndex"
                draggable="true"
                @dragend="dragend(subItem, $event)"
                @dragstart="dragstart"
              >
                <img src="./img/relation.png" alt="" />
                <div>{{ subItem.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
import { color, style } from "d3";
import object from "element-resize-detector/src/detection-strategy/object";
import log from "@/libs/configuration/util.log";
import { getDependencyRelations } from "@/api/number-cube";
import data from "@/components/map/data";
export default {
  components: {},
  props: {
    firstStepData: {
      type: Object,
      default: () => {},
    },
    targetNodeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      graphSecond: null,
      graphData: {
        nodes: [],
        edges: [],
      },
      relationTypeList: [
        {
          title: "直接关系",
          RelationTypeItemList: [
            {
              uuid: "111",
              relationType: "zhijie",
              select: false,
              name: "人人-人脸同行",
            },
            {
              uuid: "222",
              relationType: "zhijie",
              select: false,
              name: "人人-同驾乘",
            },
            {
              uuid: "333",
              relationType: "zhijie",
              select: false,
              name: "人人-同驾乘qq",
            },
          ],
        },
        {
          title: "间接关系",
          RelationTypeItemList: [
            {
              uuid: "444",
              relationType: "jianjie",
              select: false,
              name: "人人-同行",
              label: "人员",
              img: require("./img/people_gray.png"),
            },
            {
              uuid: "555",
              relationType: "jianjie",
              select: false,
              name: "人车-副驾",
              label: "车辆",
              img: require("./img/vehicle_gray.png"),
            },
            {
              uuid: "666",
              relationType: "jianjie",
              select: false,
              name: "人场所-出现",
              label: "场所",
              img: require("./img/place_gray.png"),
            },
            {
              uuid: "777",
              relationType: "jianjie",
              select: false,
              name: "人设备-抓拍",
              label: "设备",
              img: require("./img/device_gray.png"),
            },
          ],
        },
      ],
      isDrop: false,
      startEdge: [], //页面初始化直接关系
      startNodes: [], //页面初始化实体
      zhijieSelectList: [], //直接关系右侧选中列表
      zhijieEdges: [], //直接关系数据
      jianjieSelectList: [], //间接关系右侧选中列表
      jianjieNodes: [], //间接实体数据
      jianjieEdges: [], //间接关系数据
    };
  },
  mounted() {
    this.graphSecond = new G6.Graph({
      container: "mountNodeSecond",
      width: 646,
      height: 465,
      fitView: true,
      fitViewPadding: [20, 100, 20, 100],
      modes: {
        default: ["drag-canvas", "zoom-canvas", "'drag-node'"],
      },
    });
    this.initGraphEvent();
  },
  methods: {
    initGraphEvent() {
      let that = this;
      this.graphSecond.on("edge:mouseenter", (evt) => {
        const { item } = evt;
        that.graphSecond.setItemState(item, "active", true);
        if (
          item._cfg &&
          item._cfg.model.id &&
          item._cfg.model.select &&
          item._cfg.model.edgesType == "zhijie"
        ) {
          let edgeDelObj = {
            id: "edge-del",
            label: "",
            x: 430,
            y:
              item._cfg.model.startPoint.y +
              item._cfg.model.curveOffset * item._cfg.model.curvePosition -
              6,
            size: [10, 10],
            style: {
              fill: "rgba(0,0,0,0)",
              stroke: "",
            },
            icon: {
              show: true,
              img: require("./img/del_blue.png"),
              width: 10,
              height: 10,
            },
          };
          that.graphSecond.addItem("node", edgeDelObj);
        }
      });
      this.graphSecond.on("edge:mouseleave", (evt) => {
        const { item } = evt;
        that.graphSecond.setItemState(item, "active", false);
        that.graphSecond.setItemState(item, "selected", false);

        if (
          item._cfg &&
          item._cfg.model.id &&
          item._cfg.model.select &&
          item._cfg.model.edgesType == "zhijie"
        ) {
          that.graphSecond.removeItem("edge-del");
        }
      });
      this.graphSecond.on("edge:click", (evt) => {
        const { item } = evt;
        that.graphSecond.setItemState(item, "selected", true);
        //删除边
        if (
          item._cfg &&
          item._cfg.model.id &&
          item._cfg.model.select &&
          item._cfg.model.edgesType == "zhijie"
        ) {
          that.relationTypeList[0].RelationTypeItemList.forEach((itm) => {
            if (itm.uuid == item._cfg.model.uuid) {
              itm.select = false;
            }
          });
          that.zhijieSelectList = that.zhijieSelectList.filter(
            (it) => it.uuid != item._cfg.model.uuid
          );
          let newZhijieEdges = that.zhijieEdges.filter(
            (it) => it.uuid != item._cfg.model.uuid
          );
          that.zhijieEdges = [...newZhijieEdges];
          if (that.zhijieEdges.length == 0) {
            that.startEdge = that.firstStepData.edges.map((item) => ({
              ...item,
            }));
          }
          that.graphData.edges = [
            ...that.startEdge,
            ...that.zhijieEdges,
            ...that.jianjieEdges,
          ];
          that.graphSecond.removeItem("edge-del");
          that.initGraph();
        }
      });
      this.graphSecond.on("node:mouseover", (evt) => {
        const { item } = evt;
        if (
          item._cfg &&
          item._cfg.model.id &&
          item._cfg.model.select &&
          item._cfg.model.edgesType == "jianjie"
        ) {
          let nodeDelObj = {
            uuid: item._cfg.model.id,
            id: "jianjie-node-del",
            x: item._cfg.model.x,
            y: item._cfg.model.y,
            size: [60, 60],
            style: {
              fill: "rgba(0,0,0,0.5)",
              stroke: "rgba(0,0,0,0 )",
              lineWidth: 40,
            },
            icon: {
              show: true,
              img: require("./img/del.png"),
            },
          };
          that.graphSecond.addItem("node", nodeDelObj);
        }
      });
      this.graphSecond.on("node:mouseleave", (evt) => {
        const { item } = evt;
        if (item._cfg && item._cfg.model.id == "jianjie-node-del") {
          that.graphSecond.removeItem(item);
        }
      });
      this.graphSecond.on("node:click", (evt) => {
        const { item } = evt;
        if (item._cfg && item._cfg.model.id == "jianjie-node-del") {
          that.relationTypeList[1].RelationTypeItemList.forEach((itm) => {
            if (itm.uuid == item._cfg.model.uuid) {
              itm.select = false;
            }
          });
          that.jianjieSelectList = that.jianjieSelectList.filter(
            (it) => it.uuid != item._cfg.model.uuid
          );
          let newJianjieEdges = that.jianjieNodes.filter(
            (it) => it.uuid != item._cfg.model.uuid
          );
          that.jianjieNodes = [...newJianjieEdges];
          that.graphData.nodes = [...that.startNodes, ...that.jianjieNodes];
          that.graphSecond.removeItem(item);
          that.initGraph();
        }
      });
    },
    async getRelationData() {
      //   console.log(this.firstStepData, "firstStepData");
      this.relationTypeList[0].RelationTypeItemList = [];
      this.relationTypeList[1].RelationTypeItemList = [];
      let params = {
        graphInstanceId:
          this.firstStepData.nodes[1].targetNodeInfo.graphInstanceId,
        maxDepth: 1,
        minedEntityName: this.firstStepData.nodes[0].ext.label,
        relationSources: [1],
        targetEntityName: this.firstStepData.nodes[1].nodeType,
      };

      const res = await getDependencyRelations(params);
      let entityTypes = [params.minedEntityName, params.targetEntityName];
      if (res.data && res.data.length > 0) {
        let entityType = "";
        let entityKey = "";
        res.data.forEach((item, index) => {
          if (
            entityTypes.indexOf(item.targetEntity) == -1 ||
            entityTypes.indexOf(item.sourceEntity) == -1
          ) {
            entityType =
              entityTypes[0] == item.targetEntity
                ? item.sourceEntity
                : item.targetEntity;
            entityKey = this.entityTypeFun(entityType);
            this.relationTypeList[1].RelationTypeItemList.push({
              uuid: index + 1000 + "",
              relationType: "jianjie",
              select: false,
              name: item.nameCn,
              label:
                entityKey == "people"
                  ? "人员"
                  : entityKey == "vehicle"
                  ? "车辆"
                  : entityKey == "place"
                  ? "场所"
                  : entityKey == "device"
                  ? "设备"
                  : "",
              img: require(`./img/${entityKey}_gray.png`),
              relationInfo: item,
            });
          } else {
            this.relationTypeList[0].RelationTypeItemList.push({
              uuid: index + 1000 + "",
              relationType: "zhijie",
              select: false,
              name: item.nameCn,
              relationInfo: item,
            });
          }
        });
      }
    },
    entityTypeFun(type) {
      let entityKey = "";
      let arr1 = [];
      let arr2 = [];
      //处理目标实体类型数据
      let tarNode = {};
      tarNode = JSON.parse(this.targetNodeData.diggableEntities);
      for (let key in tarNode) {
        arr1.push(key);
        arr2.push(tarNode[key]);
      }
      entityKey =
        arr2.indexOf(type) == -1 ? "normal" : arr1[arr2.indexOf(type)];
      return entityKey;
    },
    init() {
      this.startNodes = [];
      this.startEdge = [];
      this.zhijieSelectList = [];
      this.zhijieEdges = [];
      this.jianjieSelectList = [];
      this.jianjieNodes = [];
      this.jianjieEdges = [];
      this.startNodes = this.firstStepData.nodes.map((item) => item);
      this.startEdge = this.firstStepData.edges.map((item) => item);
      this.graphData.edges = [...this.startEdge];
      this.graphData.nodes = [...this.startNodes];
      this.initGraph();
    },
    initGraph() {
      if ([...this.zhijieSelectList, ...this.jianjieSelectList].length > 0) {
        this.graphData.edges[0].label =
          this.graphData.edges[0].label == "+ 关系"
            ? ""
            : this.graphData.edges[0].label;
      } else {
        this.graphData.edges[0].label = "+ 关系";
        this.graphData.edges[0].labelCfg = {
          refY: 20,
          style: {
            fill: "#D8D8D8",
            background: {
              fill: "#f9f9f9",
              stroke: "#D3D7DE",
              padding: [8, 30, 5, 30],
              radius: 12,
              lineDash: [5, 5],
            },
          },
        };
      }

      G6.Util.processParallelEdges(this.graphData.edges, 30);
      this.graphSecond.data(this.graphData);
      this.graphSecond.render();
      this.$emit(
        "getSecondData",
        this.graphData,
        this.zhijieSelectList,
        this.jianjieSelectList
      );
    },
    dragstart(e) {
      this.isDrop = false;
    },
    dragend(item, e) {
      if (this.isDrop) {
        if (item.relationType == "zhijie") {
          if (!item.select) {
            item.select = true;
            this.startEdge = [];
            this.zhijieEdges = [];
            this.zhijieSelectList.push(item);
            for (let i = 0; i < this.zhijieSelectList.length; i++) {
              this.zhijieEdges.push({
                edgesType: this.zhijieSelectList[i].relationType,
                uuid: this.zhijieSelectList[i].uuid,
                select: this.zhijieSelectList[i].select,
                label: this.zhijieSelectList[i].name,
                source: this.firstStepData.edges[0].source,
                target: this.firstStepData.edges[0].target,
                style: this.firstStepData.edges[0].style,
                type: "quadratic",
                labelCfg: {
                  refY: 5,
                  autoRotate: true,
                  style: {
                    fill: "#2C86F8",
                    fontSize: 12,
                    cursor: "pointer",
                  },
                },
              });
            }
            this.graphData.edges = [
              ...this.startEdge,
              ...this.zhijieEdges,
              ...this.jianjieEdges,
            ];
            this.initGraph();
          }
        } else {
          if (!item.select) {
            item.select = true;
            this.jianjieSelectList.push(item);
            this.drawNodeLineData(this.jianjieSelectList);
          }
        }
      }
    },
    drawNodeLineData(list) {
      this.jianjieNodes = [];
      this.jianjieEdges = [];
      for (let i = 0; i < list.length; i++) {
        this.jianjieNodes.push({
          x: 390,
          y: 220 + 100 * (i + 1),
          size: [60, 60],
          edgesType: list[i].relationType,
          uuid: list[i].uuid,
          select: list[i].select,
          label: list[i].label,
          id: list[i].uuid,
          type: "image",
          img: list[i].img,
          labelCfg: {
            style: {
              fill: "#333",
              lineWidth: 2,
              fontSize: 13,
            },
          },
        });
        this.jianjieEdges.push(
          {
            label: list[i].name,
            source: this.firstStepData.nodes[0].id,
            target: list[i].uuid,
            style: this.firstStepData.edges[0].style,
            type: "quadratic",
            labelCfg: {
              autoRotate: true,
              style: {
                fill: "#2C86F8",
                fontSize: 12,
              },
            },
          },
          {
            label: list[i].name,
            source: this.firstStepData.nodes[1].id,
            target: list[i].uuid,
            style: this.firstStepData.edges[0].style,
            type: "quadratic",
            labelCfg: {
              autoRotate: true,
              style: {
                fill: "#2C86F8",
                fontSize: 12,
              },
            },
          }
        );
      }
      this.graphData.nodes = [...this.startNodes, ...this.jianjieNodes];
      this.graphData.edges = [
        ...this.startEdge,
        ...this.zhijieEdges,
        ...this.jianjieEdges,
      ];
      this.initGraph();
    },
    dragover() {
      return false;
    },
    drop(e) {
      this.isDrop = true;
    },
  },
  watch: {
    firstStepData(val) {
      this.init();
      if (this.firstStepData.nodes[1].targetNodeInfo) {
        this.getRelationData();
      }
    },
    immediate: true,
  },
};
</script>
<style lang="less" scoped>
.second-step-box {
  width: 100%;
  height: 100%;
  .top-tips {
    width: 100%;
    text-align: right;
    font-size: 14px;
    margin-bottom: 5px;
    color: #f29f4c;
  }
  .step-content {
    width: 100%;
    height: calc(~"100% - 10px");
    display: flex;
    background: #ffffff;
    #mountNodeSecond {
      width: 60%;
      height: 100%;
      border: 1px solid #d3d7de;
      margin-right: 10px;
    }
    .right-box {
      flex: 1;
      height: 100%;
      border: 1px solid #d3d7de;
      .title {
        height: 40px;
        line-height: 40px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: 700;
        padding-left: 10px;
        background: #f9f9f9;
      }
      .nodes-box::-webkit-scrollbar {
        width: 0;
      }
      .nodes-box {
        width: 100%;
        height: calc(~"100% - 40px");
        overflow: auto;
        padding: 10px;
        .rlation-item {
          width: 100%;
          margin-bottom: 10px;
          .relation-title {
            color: #3d3d3d;
            font-weight: 700;
            padding-left: 12px;
            position: relative;
          }
          .relation-title::before {
            content: "";
            display: block;
            position: absolute;
            width: 6px;
            height: 6px;
            background: #2c86f8;
            left: 0;
            top: 8px;
          }
          .relation-type-box {
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            .type-item {
              height: 36px;
              line-height: 30px;
              text-align: center;
              padding: 0 15px;
              margin: 10px 10px 0 0;
              background: rgba(44, 134, 248, 0.1028);
              border-radius: 39px 39px 39px 39px;
              color: #3d3d3d;
              cursor: move;
              border: 2px solid #fff;
              display: flex;
              align-items: center;
              img {
                width: 16px;
                height: 15px;
                margin-right: 5px;
              }
            }
            .type-item:hover,
            .type-item-active {
              box-shadow: 0px 2px 10px 0px rgba(44, 134, 248, 0.5);
              border: 2px solid #2c86f8;
            }
          }
        }
      }
    }
  }
}
</style>
