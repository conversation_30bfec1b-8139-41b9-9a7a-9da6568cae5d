/**
 * 通用混合
 * */
import { getConfigDate } from "@/util/modules/common";
import { logRecord } from "@/api/user.js";
import { mapMutations, mapActions, mapGetters } from "vuex";
import { searchCenter } from "./data";
export const commonMixins = {
  data() {
    return {
      searchCenter,
      firmList: [
        {
          dataKey: "GLST",
          dataValue: "格灵深瞳",
        },
        {
          dataKey: "HK",
          dataValue: "海康",
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj", //系统配置数据
      ipbdFaceCaptureGender: "dictionary/getIpbdFaceCaptureGender", // 性别
      faceCaptureGender: "dictionary/getIpbdFaceCaptureGender", // 性别
      // 人脸
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      getCollectJudge: "map/getCollectJudge",
      skinColorList: "dictionary/getSkinColor", //肤色
      captureGlassesList: "dictionary/getIpbdFaceCaptureGlasses", // 眼镜
      captureCapList: "dictionary/getIpbdFaceCaptureCap", //帽子
      captureMaskList: "dictionary/getIpbdFaceCaptureMask", //口罩
      ipbdFaceCaptureAge: "dictionary/getIpbdFaceCaptureAge", // 年龄段
      // 车辆
      plateClassList: "dictionary/getPlateClassList", // 车牌类型(枚举精准检索)
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
      plateOcclusionList: "dictionary/getPlateOcclusionList", // 遮挡(枚举精准检索)
      vehicleClassTypeList: "dictionary/getVehicleClassTypeList", // 车辆类型、车身类型(枚举精准检索)
      plateColorIpbdList: "dictionary/getVehicleColorList", // 车身颜色
      pointViewList: "dictionary/getPointViewList", // 角度(枚举精准检索)
      specialVehicleList: "dictionary/getSpecialVehicleList", // 特殊车辆
      roofItemsList: "dictionary/getRoofItemsList", // 车顶物件
      annualInspectionNumList: "dictionary/getAnnualInspectionNumList", // 年检标
      markerTypeList: "dictionary/getMarkerTypeList", // 标志物
      copilotList: "dictionary/getCopilotList", // 副驾有人
      facialOcclusionList: "dictionary/getFacialOcclusionList", // 面部遮挡
      sunVisorStatusList: "dictionary/getSunVisorStatusList", // 遮阳板
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      // 主要人体
      upperBodyTextureList: "dictionary/getUpperBodyTexture", // 上身纹理
      upperBodyTypeList: "dictionary/getUpperBodyType", //上身类型
      upperSleeveTypeList: "dictionary/getUpperSleeveType", //上身袖子类型
      recognitionColorList: "dictionary/getRecognitionColor", //颜色
      lowerBodyType: "dictionary/getLowerBodyType", //下身类型
      shoeCategory: "dictionary/getShoeCategory", //鞋子类别
      hairStyleList: "dictionary/getHairStyleList", //发型
      behaviorList: "dictionary/getBehaviorList", //行为
      appendantList: "dictionary/getAppendantList", //附属物
      genderList: "dictionary/getGenderList", //性别
      packetTypeList: "dictionary/getPacketType", //包类型
      // 主要非机动车
      nonmotorVehicleType: "dictionary/getNonmotorVehicleType", //非机动车类型
      mannedSituation: "dictionary/getMannedSituation", //载人情况
      nonmotorVehicleAngle: "dictionary/getNonmotorVehicleAngle", //非机动车角度

      // 其他
      nationList: "dictionary/getNationList", //民族

      sbgnlxList: "dictionary/getSbgnlxList", // 设备功能类型
      userInfo: "userInfo", // 用户信息
      algorithmTypeList: "dictionary/getAlgorithmTypeList", // 算法场商

      // 模型集市
      trajectoryTypeList: "dictionary/getTrajectoryTypeList", //轨迹类型
      caseTimeTypeList: "dictionary/getCaseTimeTypeList", //前后时间

      // 步态
      yhsdUpperBodyTexture: "dictionary/getYhsdUpperBodyTexture", // 上身纹理
      yhsdLowerBodyType: "dictionary/getYhsdLowerBodyType", // 下身类型
      yhsdFaceCaptureAge: "dictionary/getYhsdFaceCaptureAge", //  年龄范围
      yhsdBehaviorList: "dictionary/getYhsdBehaviorList", // 特征行为类型
      yhsdCaptureMask: "dictionary/getYhsdCaptureMask", // 是否戴口罩
      yhsdCaptureCap: "dictionary/getYhsdCaptureCap", // 是否戴帽子
    }),
    judgeUser() {
      return this.userInfo.username == "admin" ? false : true;
    },
    algorithmTypeSelect() {
      let typeList = this.algorithmTypeList.map((item) => item.dataKey);
      return typeList;
    },
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * 日志操作内容
     * @param {String} params 查询参数
     * @param {String} operation 操作类型
     * @param {String} deviceList 设备资源
     */
    logParams(params, operation, deviceList) {
      let deviceText = "";
      if (deviceList && deviceList.length <= 5) {
        deviceList.forEach((item, index) => {
          let comma = deviceList.length == index + 1 ? "" : ",";
          deviceText += item.deviceName + comma;
        });
      } else if (deviceList && deviceList.length > 5) {
        for (let i = 0; i < 5; i++) {
          let comma = i == 4 ? `等${deviceList.length}个设备` : ",";
          deviceText += deviceList[i].deviceName + comma;
        }
      }
      let text = "";
      for (let key in this.searchCenter) {
        if (this.searchCenter[key].dic) {
          //对于需要翻译字典的数据
          let label = params[this.searchCenter[key].keyName];
          let value = this.filterInversion(
            label,
            this[this.searchCenter[key].dic]
          );
          let searchText = value
            ? `${this.searchCenter[key].label}:${value},`
            : "";
          text += searchText;
        } else if (this.searchCenter[key].type == "device") {
          //设备列表
          text += deviceText
            ? `${this.searchCenter[key].label}:${deviceText},`
            : "";
        } else {
          //直接拿取数据
          let value = params[this.searchCenter[key].keyName];
          let searchText = value
            ? `${this.searchCenter[key].label}:${value},`
            : "";
          text += searchText;
        }
      }
      // console.log(text, 'text')
      this.queryLog({
        ...operation,
        remark: text,
        imageBases: params.imageBases,
      });
    },
    /**
     * 过滤方法 转化为中文
     * @param {String} data 值
     * @param {Array} list 数据源列表
     * @param {String} keyName 键名
     * @param {String} valueName 值名
     * @param {String} nullValue 空值显示
     */
    filterInversion(data, list, keyName, valueName, nullValue) {
      const keyNames = keyName || "dataKey";
      const valueNames = valueName || "dataValue";
      const nullValues = nullValue || "";
      if (!data && data != 0) return nullValues;
      if (list.length === 0) return data;
      let arr = "";
      if (typeof data === "string") {
        arr = data.split("/");
      } else {
        arr = data.toString().split("/");
      }
      let str = "";
      if (arr.length > 1 && arr.length) {
        arr.forEach((item) => {
          for (const i of list) {
            if (item === i[keyNames]) {
              str += i[valueNames] + " / ";
              break;
            }
          }
        });
        return str.substring(0, str.length - 3);
      } else {
        for (const i of list) {
          if (data == i[keyNames]) {
            str = i[valueNames];
            break;
          } else {
            str = data;
          }
        }
        return str;
      }
    },
    // 查询日志
    queryLog(data) {
      // type 类型(1 新增、2 编辑、3 删除、 4 查询、5 检索  6 登录 7 登出)
      let params = {
        orgCode: this.userInfo.orgVoList[0].orgCode,
        applicationCode: data.muen,
        resourceCode: data.name,
        type: data.type,
        remark: data.remark, // "在地图上，单击图标查看【名称为L_JF3033龙新路行知实验学校大门口】的【资源】详情",
        imageBases:
          data.imageBases && data.imageBases.length
            ? data.imageBases
            : undefined,
      };
      logRecord(params)
        .then((res) => {})
        .finally(() => {});
    },
    // 当 $route 更新时触发
    appRouteChange(to, from) {},
    // 获取字典
    translate(value) {
      return this[value];
    },
    // 设备类型
    facilitySplit(value) {
      if (!value) {
        return "--";
      }
      let list = value.split("/");
      let val = "";
      if (list.length == 0) {
        val = "--";
      } else if (list.length == 1) {
        val = value;
      }
      if (list.length > 1) {
        return list;
      }
      return val;
    },
    /**
     * 穿戴翻译字典
     * 0-未戴； 1-戴； 2-太阳镜；-1-其他
     */
    handleWear(value) {
      let wear = {
        0: "未戴",
        1: "戴",
        2: "太阳镜",
        "-1": "其他",
      };
      let text = wear[value] ? wear[value] : "--";
      return text;
    },
    // 时间默认赋值
    timeAssig() {
      let queryParam = this.$refs.searchBar.queryParam;
      let { startDate, endDate } = this.timeTransition(
        queryParam.timeSlot,
        queryParam.timeSlotArr
      );
      this.queryParam.startDate = startDate;
      this.queryParam.endDate = endDate;
    },
    /**
     * 根据时段（中文）获取相对应的时间时（可拓展）
     * @param {String}
     * @param {Number}
     * @param {Number}
     * @returns {Number}
     */
    timeTransition(timeText, timeArr) {
      let startDate = "",
        endDate = "";
      switch (timeText) {
        case "近一天":
          var arr = getConfigDate(-1);
          startDate = arr[1] + " 00:00:00";
          // endDate = arr[1] + ' 23:59:59'
          endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
          break;
        case "近三天":
          var arr = getConfigDate(-2);
          startDate = arr[0] + " 00:00:00";
          // endDate = arr[1] + ' 23:59:59'
          endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
          break;
        case "近一周":
          var arr = getConfigDate(-6);
          startDate = arr[0] + " 00:00:00";
          // endDate = arr[1] + ' 23:59:59'
          endDate = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
          break;
        case "自定义":
          let start = "",
            end = "";
          if (timeArr && timeArr[0] != "") {
            start = timeArr[0];
            end = timeArr[1];
          } else {
            start = new Date();
            end = new Date();
          }
          startDate = this.$dayjs(start).format("YYYY-MM-DD HH:mm:ss");
          endDate = this.$dayjs(end).format("YYYY-MM-DD HH:mm:ss");
          break;
        default:
          break;
      }
      return { startDate, endDate };
    },
  },
};
