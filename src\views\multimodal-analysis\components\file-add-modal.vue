<template>
  <div class="add-modal" v-if="isShow">
    <div class="title-box" v-if="!isApproval">
      <div class="goback" @click="isShow = false">
        <i class="iconfont icon-return"></i>
      </div>
      文件解析<span class="now-state"> > {{ title }}</span>
    </div>
    <div class="content">
      <div class="video-left mr-10">
        <Form
          ref="form"
          :model="fileTaskValue"
          :disabled="isApproval"
          class="form"
          :label-width="80"
        >
          <FormItem label="任务名称:" prop="taskName" class="search-input">
            <Input
              placeholder="请输入"
              clearable
              v-model="fileTaskValue.taskName"
              maxlength="50"
            />
          </FormItem>
          <FormItem label="执行时间:" prop="planExecTime" class="search-input">
            <hl-daterange
              v-model="fileTaskValue.planExecTime"
              key="1"
            ></hl-daterange>
          </FormItem>
          <FormItem label="解析类型:" class="search-input">
            <Select v-model="fileTaskValue.structureAlgorithmId" clearable>
              <Option
                :value="item.id"
                :key="item.id"
                v-for="item in structureAlgorithmList"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="处理类型:" class="search-input">
            <RadioGroup v-model="fileTaskValue.structuredDataType" clearable>
              <Radio :label="0" :disabled="tableData.length > 0"
                ><span>视频</span></Radio
              >
              <Radio :label="1" :disabled="tableData.length > 0"
                ><span>图片</span></Radio
              >
            </RadioGroup>
          </FormItem>
          <div class="sac-config-item-split pad-16">
            ------------分割线---------------
          </div>
        </Form>
        <div class="tipWordDiv">
          任务文件列表：
          <span class="tipsWordSpan">(注：单次导入上限50个文件)</span>
        </div>
        <fileUpload
          class="file-upload"
          :fileUploadType="fileTaskValue.structuredDataType"
          @fileUploadSuccess="fileUploadSuccess"
        ></fileUpload>
      </div>
      <div class="right-content">
        <div class="fileFilterArea">
          <span>文件名称：</span>
          <Input v-model="filterStr" placeholder="请输入文件名称"></Input>
        </div>
        <div class="sac-config-item-split pad-16">
          ------------分割线---------------
        </div>
        <div class="fileToolArea">
          <Button size="small" @click="mulLacation">
            <ui-icon type="xunjianguiji" color="#2C86F8"></ui-icon>
            批量定位
          </Button>
          <Button size="small" @click="mulAdjustTime">
            <ui-icon type="location1" color="#2C86F8"></ui-icon>
            批量校准
          </Button>
        </div>
        <div class="fileTableArea">
          <ui-table
            :columns="columns"
            :data="tableShowData"
            @on-select="onSelect"
            @on-select-cancel="onSelectCancel"
            @on-select-all="onSelectAll"
            @on-select-all-cancel="onSelectAllCancel"
          >
            <template #absTime="{ row }">
              <div>{{ row.absTime || "--" }}</div>
            </template>
            <template #position="{ row }">
              <div>
                {{ row.position || "--" }}
              </div>
            </template>
            <template #opreate="{ row, index }">
              <div class="tools">
                <Poptip
                  trigger="hover"
                  placement="left-start"
                  transfer
                  popper-class="expand-row-poptip-box"
                >
                  <i class="iconfont icon-gengduo"></i>
                  <div class="mark-poptip" slot="content">
                    <p @click="adjustTime(row)">
                      <i class="iconfont icon-lishijilu"></i>时间校准
                    </p>
                    <p @click="position(row)">
                      <i class="iconfont icon-dongtai-shangtudaohang"></i
                      >位置校准
                    </p>
                    <p @click="deleteFile(row, index)">
                      <i class="iconfont icon-shanchu1"></i>删除
                    </p>
                  </div>
                </Poptip>
              </div>
            </template>
          </ui-table>
        </div>
      </div>
    </div>
    <div class="file-footer" v-if="!isApproval">
      <Button class="mr-20" @click="closePanel">取消</Button>
      <Button type="primary" @click="saveFunc">保存</Button>
    </div>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      @closeDragDialog="closeDragDialog"
    >
    </adjustPosition>
    <adjust-time
      v-model="isShowAdjustTime"
      :adjustProps="adjustProps"
      :adjustTimeKey="'absTime'"
      @adjustTimeClose="adjustTimeFunc"
    ></adjust-time>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import {
  resourceBatchAdd,
  resourceDelete,
  multiModalTaskAdd,
  multiModalTaskModify,
} from "@/api/multimodal-analysis.js";
import hlDaterange from "@/components/hl-daterange/index.vue";
import adjustTime from "@/views/viewanalysis/components/adjustTime.vue";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import fileUpload from "@/views/multimodal-analysis/components/file-upload.vue";
export default {
  name: "fileAddModal",
  components: {
    hlDaterange,
    adjustTime,
    adjustPosition,
    fileUpload,
  },
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    taskId: {
      type: String,
      require: "",
    },
    subDeviceId: {
      type: String,
      require: "",
    },
    // 是否是审核详情
    isApproval: {
      type: Boolean,
      require: false,
    },
  },
  watch: {
    isShow(val) {
      this.$emit("input", val);
    },
    value: {
      handler(val) {
        this.isShow = val;
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      structureAlgorithmList: "multimodal/getStructureAlgorithmList", // 结构化算法
    }),
    // 文件名称过滤
    tableShowData() {
      return this.tableData.filter(
        (v) => v.fileName && v.fileName.includes(this.filterStr)
      );
    },
  },
  data() {
    return {
      filterStr: "",
      isShow: false,
      title: "文件结构化",
      isSaveFlag: true,
      isShowDragDialog: false,
      pointData: [],
      isShowAdjustTime: false,
      adjustProps: [],
      modules: {},
      oldAnalyzeType: "",
      componentId: "",
      tableData: [],
      selectedData: [], //表格中选中的数据
      fileTaskValue: {
        taskName: `${new Date().format("yyyyMMddhhmmss")}`,
        planExecTime: "",
        structureAlgorithmId: "",
        structuredDataType: 1, // 处理类型
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "文件名称", key: "fileName", minWidth: 150 },
        { title: "校准时间", slot: "absTime" },
        { title: "定位", slot: "position" },
        { title: "分辨率", key: "resolution" },
        { title: "文件大小", key: "fileSizeTable" },
        { title: "操作", slot: "opreate", width: 80 },
      ],
      defaultList: [],
    };
  },
  mounted() {},
  methods: {
    init(taskInfo = {}) {
      this.filterStr = "";
      if (!taskInfo?.id) {
        this.title = "新增文件结构化任务";
        this.reset();
        return;
      }
      this.title = "编辑文件结构化任务";
      const {
        id,
        taskName,
        planExecTime,
        structureAlgorithmId,
        taskResourceList,
      } = taskInfo;
      const structuredDataType =
        taskInfo?.taskResourceList[0]?.resourceInfo?.resourceType - 1;
      this.fileTaskValue = {
        id,
        taskName,
        planExecTime,
        structureAlgorithmId,
        structuredDataType,
      };
      this.tableData = taskResourceList.map((item) => {
        const { id, resourceId, resourceInfo } = item;
        return {
          ...resourceInfo,
          fileName: resourceInfo?.resourceName,
          position: resourceInfo?.longitude + " " + resourceInfo?.latitude,
          fileSizeTable: this.round(resourceInfo?.fileSize / 1024, 2) + "KB",
          id,
          resourceId,
        };
      });
    },
    async getImageDataByUrl(url) {
      const res = await fetch(url);
      const data = await res.blob();
      const fileSize = data.size;
      const resUrl = window.URL.createObjectURL(data);
      const img = new Image();
      return new Promise((resolve, reject) => {
        img.onload = () => {
          resolve({
            resolution: `${img.naturalWidth}*${img.naturalHeight}`,
            fileSize: fileSize,
          });
        };
        img.onerror = () => {
          resolve({ resolution: "0*0", fileSize: 0 });
        };
        img.src = resUrl;
      });
    },
    async fileAddInit(imageList = []) {
      this.title = "新增文件结构化任务";
      this.reset();
      for (let i = 0; i < imageList.length; i++) {
        const url = imageList[i];
        const { resolution, fileSize } = await this.getImageDataByUrl(url);
        const size = this.round(fileSize / 1024, 2);
        const data = {
          fileUrl: url,
          fileName: url.split("/").pop(),
          absTime: new Date().format("yyyy-MM-dd hh:mm:ss"),
          position: "",
          fileSize: fileSize,
          fileSizeTable: size + "KB",
          resolution: resolution,
          videoDuration: "",
        };
        this.tableData.push(data);
      }
    },
    reset() {
      this.fileTaskValue = {
        taskName: `${new Date().format("yyyyMMddhhmmss")}`,
        planExecTime: "",
        structureAlgorithmId: "",
        structuredDataType: 1, // 处理类型
      };
      this.tableData = [];
      this.selectedData = [];
    },
    closePanel() {
      this.isShow = false;
    },
    /**
     * table 选中一项
     */
    onSelect(selection) {
      this.selectedData = selection;
    },
    onSelectCancel(selection) {
      this.selectedData = selection;
    },
    onSelectAll(selection) {
      this.selectedData = selection;
    },
    onSelectAllCancel() {
      this.selectedData = [];
    },
    async saveFunc() {
      //dialog保存提交接口
      let root = this;
      let taskName = root.fileTaskValue.taskName;
      let structureAlgorithmId = root.fileTaskValue.structureAlgorithmId;
      if (!taskName) {
        this.$Message.warning("请输入任务名称");
        return false;
      }
      if (!new RegExp(/^[\u4E00-\u9FA5a-zA-Z0-9]+$/).test(taskName)) {
        this.$Message.warning("任务名称不支持特殊字符，只支持数字、字母、汉字");
        return false;
      }
      if (!structureAlgorithmId) {
        this.$Message.warning("请选择解析类型");
        return false;
      }
      let batchParam = this.tableData
        ?.filter((item) => !item.id)
        ?.map((item) => {
          return {
            absTime: item.absTime,
            fileSize: item.fileSize,
            latitude: item.position.split(" ")[1],
            longitude: item.position.split(" ")[0],
            resolution: item.resolution,
            resourceFilePath: item.fileUrl,
            resourceName: item.fileName,
            resourceType: this.fileTaskValue.structuredDataType == 0 ? 2 : 1, // 文件类型 1:图片 2:视频
            videoDuration: item.videoDuration, // 视频时长 1:14:23
          };
        });
      let resourceIds = [];
      if (batchParam.length > 0) {
        const { data = [] } = await resourceBatchAdd(batchParam);
        resourceIds = data || [];
      }
      // 编辑时已经上传过的文件
      const editResourceId = this.tableData
        ?.filter((item) => item.id)
        ?.map((item) => item.resourceId);
      const param = {
        resourceIds: [...resourceIds, ...editResourceId],
        ...this.fileTaskValue,
        structuredParsingType: "5", // 文件解析
      };

      if (param.id) {
        // 编辑
        await multiModalTaskModify(param);
      } else {
        // 新增
        await multiModalTaskAdd(param);
      }

      this.$Message.success((param?.id ? "编辑" : "新增") + "任务成功");
      this.$emit("updated");
    },
    mulLacation() {
      if (this.selectedData.length > 0) this.position();
      else this.$Message.warning("当前未选中文件");
    },
    mulAdjustTime() {
      if (this.selectedData.length > 0) this.adjustTime();
      else this.$Message.warning("当前未选中文件");
    },
    deleteFile(record, index) {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确认删除${record.fileName}?`,
        onOk: () => {
          this.tableData.splice(index, 1);
        },
      });
    },
    position(items) {
      if (!items) items = this.selectedData;
      else items = [items];
      let root = this;
      root.pointData = [...items];
      root.isShowDragDialog = true;
    },
    closeDragDialog(items) {
      this.isShowDragDialog = false;
      if (!items) return false;
      items.forEach((v) => {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].fileUrl == v.fileUrl) {
            this.tableData[i].position = v.position;
            break;
          }
        }
      });
    },
    adjustTime(items) {
      if (!items) items = this.selectedData;
      else items = [items];
      let root = this;
      root.adjustProps = items;
      root.isShowAdjustTime = true;
    },
    adjustTimeFunc(values) {
      let root = this;
      values.forEach((v) => {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].fileUrl == v.fileUrl) {
            this.tableData[i].absTime = v.adjustTime;
            break;
          }
        }
      });
      root.isShowAdjustTime = false;
    },
    fileUploadSuccess({ fileList, structuredDataType }) {
      let newFile = fileList.map((item) => {
        let size = this.round(item.file.size / 1024, 2);
        return {
          ...item,
          fileName: item.originalFilename,
          absTime: new Date().format("yyyy-MM-dd hh:mm:ss"),
          position: "",
          fileSize: item.file.size,
          fileSizeTable: size + "KB",
          resolution: item.resolution,
          videoDuration: item.videoDuration,
        };
      });
      this.tableData.push(...newFile);
    },
    round(number, precision) {
      return Math.round(number + "e" + precision) / Math.pow(10, precision);
    },
    resourceDeleteBatch(ids) {
      if (ids?.length == 0) {
        return;
      }
      resourceDelete(ids.join(",")).then((res) => {
        if (res.msg == "成功") {
          this.$Message.success("删除成功");
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.add-modal {
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .title-box {
    height: 50px;
    border-bottom: 1px solid #d3d7de;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.35);

    .now-state {
      color: #3d3d3d;
    }

    .goback {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      margin-right: 10px;
    }
  }

  .content {
    flex: 1;
    display: flex;
    overflow: hidden;
    border-bottom: 1px solid #d8d8d8;

    .color-blue {
      color: #2c86f8;
    }

    .video-left {
      width: 416px;
      height: 100%;
      padding: 20px;
      display: flex;
      border-right: 1px solid #d8d8d8;
      flex-direction: column;

      /deep/ .ivu-form-item {
        margin-bottom: 15px;
      }

      .tipWordDiv {
        font-weight: bold;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.75);
        margin-bottom: 10px;

        .tipsWordSpan {
          font-weight: normal;
          font-size: 12px;
          color: #f29f4c;
          letter-spacing: 1px;
        }
      }
    }

    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;
      padding: 20px;

      .fileFilterArea {
        display: flex;
        align-items: center;

        span {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4513);
          margin-right: 10px;
        }

        .ivu-input-wrapper {
          width: 200px;
        }
      }

      .fileToolArea {
        margin-bottom: 16px;

        button {
          margin-right: 10px;
        }
      }

      .fileTableArea {
        flex: 1;
        overflow: hidden;
        .ui-table {
          height: 100%;
        }

        .tools {
          color: #2c86f8;
          width: 20px;
          height: 20px;
          text-align: center;
          line-height: 20px;
          margin-left: 5px;

          .icon-gengduo {
            transform: rotate(90deg);
            transition: 0.1s;
            display: inline-block;
          }

          p:hover {
            color: #2c86f8;
          }

          &:hover {
            background: #2c86f8;
            color: #fff;

            .icon-gengduo {
              transform: rotate(0deg);
              transition: 0.1s;
            }

            border-radius: 10px;
          }

          /deep/ .ivu-poptip-popper {
            min-width: 150px !important;
            width: 40px !important;
            height: auto;
          }

          /deep/.ivu-poptip-body {
            height: auto !important;
          }
        }
      }
    }
  }

  .file-footer {
    height: 50px;
    text-align: center;
    padding-top: 5px;
  }
}
.mark-poptip {
  width: 128px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
  padding: 10px 0px;
  border-radius: 4px;
  p {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.8);
    padding: 0 20px;
    text-align: left;
    cursor: pointer;
    i {
      color: #2c86f8;
      margin-right: 10px;
    }
    &:hover {
      background: rgba(44, 134, 248, 0.1028);
    }
  }
}
.sac-config-item-split {
  width: 100%;
  padding: 0;
  margin: 10px 0 20px 0;
  border-top: 1px dotted #ccc;
  height: 1px;
  font-size: 0;
}

.pad-16 {
  margin: 16px 0;
}
</style>
<style lang="less">
.expand-row-poptip-box {
  width: auto !important;
  min-width: unset;

  .ivu-poptip-body {
    padding: 0;
  }
}
</style>
