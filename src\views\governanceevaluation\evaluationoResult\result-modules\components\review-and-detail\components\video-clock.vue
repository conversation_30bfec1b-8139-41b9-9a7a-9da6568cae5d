<template>
  <base-review v-bind="getAttrs" v-on="$listeners">
    <template #detailcontent="{ detailInfo }">
      <div class="detail-content">
        <div class="detail-img mr-md">
          <ui-image :src="detailInfo.screenShot" class="image" />
        </div>
        <div class="detail-info">
          <ui-label align="right" class="block mt-md" label="设备时间:" :width="120">
            <Input readonly class="width-lg ml-sm" v-model="detailInfo.startTime" type="text"></Input>
            <ui-label align="right" class="block" label=" " :width="120">
              <i class="icon-font icon-wenhao ml-sm f-12 mark-color"></i>
              <span class="ml-xs advert-color">OCR识别图片时间</span>
            </ui-label>
          </ui-label>
          <ui-label align="right" class="block" label="对应标准时间:" :width="120">
            <Input readonly class="width-lg ml-sm" v-model="detailInfo.ntpTime" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block" label=" " :width="120">
            <i class="icon-font icon-wenhao ml-sm f-12 mark-color"></i>
            <span class="ml-xs advert-color">截取图片时的北京时间</span>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="时间误差:" :width="120">
            <Input readonly class="width-lg ml-sm" v-model="detailInfo.clockSkew" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="检测结果:" :width="120">
            <Input readonly class="width-lg ml-sm" v-model="detailInfo.description" type="text"></Input>
          </ui-label>
        </div>
      </div>
    </template>
  </base-review>
</template>

<script>
export default {
  name: 'clock',
  computed: {
    getAttrs() {
      return {
        ...this.$attrs,
      };
    },
  },
  components: {
    BaseReview: require('./base-review').default,
  },
};
</script>

<style lang="less" scoped>
.detail-content {
  display: flex;
  width: 100%;
  padding: 20px;
  border-radius: 4px;

  .detail-img {
    width: 590px;
    max-height: 430px;
    object-fit: contain;
  }
  .mark-color {
    color: var(--color-warning);
  }
  .advert-color {
    color: #ea4a36;
  }
}
[data-theme='dark'] {
  .detail-content {
    background: #041129;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .detail-img {
    border: 1px solid #d3d7de;
  }
}
</style>
