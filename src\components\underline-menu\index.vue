<template>
  <div class="underline-menu-wrapper dis-select">
    <div
      v-for="(item, index) in list"
      :key="index"
      :class="['tab', activeCode === item.code ? 'active' : '']"
      @click="onClick(item)"
    >
      {{ item.label }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'underline-menu',
  components: {},
  props: {
    value: {},
    data: {},
    // 切换菜单之前需要调用的函数
    beforeOnChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      list: [],
      activeCode: '',
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.activeCode = val;
      },
    },
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.list = val;
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    async onClick(val) {
      try {
        await this.beforeOnChange();
        this.activeCode = val.code;
        this.$emit('input', this.activeCode);
        this.$emit('on-change', this.activeCode);
      } catch (err) {
        console.log(err, 'err');
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .underline-menu-wrapper {
    background: #0a2754;
    .tab {
      color: #56789c;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .underline-menu-wrapper {
    background: #f9f9f9;
    .tab {
      color: rgba(0, 0, 0, 0.35);
    }
  }
}
.underline-menu-wrapper {
  position: relative;
  width: 100%;
  height: 54px;
  line-height: 54px;
  background: var(--bg-content);
  padding: 0 20px 0 20px;
  white-space: nowrap;
  .tab {
    display: inline-block;
    position: relative;
    font-size: 16px;
    margin-right: 38px;
    cursor: pointer;

    &:hover {
      .active();
    }
  }
  .active {
    //transition: all .5s ease-out;
    color: var(--color-active) !important;
    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 3px;
      background-color: var(--color-active);
    }
  }
}
</style>
