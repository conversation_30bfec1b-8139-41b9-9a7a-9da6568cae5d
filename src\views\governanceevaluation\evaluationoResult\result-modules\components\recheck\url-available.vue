<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel" @handleGetConfig="setOcrModel">
    <template>
      <FormItem label="最大等待时长">
        <InputNumber
          v-model.number="formData.visitTimeout"
          :min="0"
          :max="Number.MAX_SAFE_INTEGER"
          :precision="0"
          placeholder="请输入最大等待时长"
          class="mr-sm width-input"
        ></InputNumber>
        <span class="base-text-color">毫秒</span>
      </FormItem>
      <FormItem label="检测大图标注抓拍时间和地点">
        <RadioGroup v-model="formData.snap" @on-change="onChangeSnap">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="formData.snap !== 0 && !!formData.snap" label="优先算法" prop="ocrModel" key="'isShowOcrModel'">
        <Select
          v-model="customFormData.optimizationAlgorithm"
          placeholder="请选择算法"
          transfer
          class="input-width-number"
          @on-change="onChangeOcrModel($event, 'optimizationAlgorithm')"
        >
          <template v-for="(algorithmItem, index) in algorithmVendorData">
            <Option
              :value="`${algorithmItem.algorithmType},${algorithmItem.algorithmVendorType}`"
              :key="`algorithmItem${index}`"
              :data="algorithmItem.algorithmType"
            >
              {{ algorithmItem.algorithmVendorTypeName }}-{{ algorithmItem.algorithmTypeName }}
            </Option>
          </template>
        </Select>
      </FormItem>
      <!--     深网 才支持751       -->
      <FormItem class="mb-xs" v-if="customFormData.optimizationAlgorithm">
        <Checkbox
          v-model="customFormData.optimizationAlgorithm751"
          @on-change="onChange751($event, 'optimizationAlgorithm')"
          :true-value="1"
          :false-value="0"
          >按照《GAT-751-2008视频图像文字标注规范》要求检测</Checkbox
        >
      </FormItem>
      <FormItem
        class="mb-md"
        prop="optimizationAlgorithmDetectContent"
        v-if="customFormData.optimizationAlgorithm && customFormData.optimizationAlgorithm751"
      >
        <template #label>
          <Tooltip placement="top-start" class="tooltip-sample-graph">
            <i class="icon-font icon-wenhao f-16 mr-xs" :style="{ color: 'var(--color-warning)' }"></i>
            <span>检测内容</span>
            <div slot="content">
              <div class="check-content-img">
                <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
              </div>
            </div>
          </Tooltip>
        </template>
      </FormItem>
      <detect-content
        @on-change="
          (formData, detectContent) => {
            onChangeFormData(formData, detectContent, 'optimizationAlgorithm');
          }
        "
        form-model="edit"
        ref="optimizationAlgorithmDetectContent"
        v-bind="$props"
        :config-info="formData.optimizationAlgorithm"
        :detect-content-list="formData.optimizationAlgorithm.detectContent"
        v-if="customFormData.optimizationAlgorithm && customFormData.optimizationAlgorithm751"
      >
      </detect-content>
      <FormItem v-if="formData.snap !== 0 && !!formData.snap" label="备用算法">
        <Select
          v-model="customFormData.standbyAlgorithm"
          placeholder="请选择算法"
          transfer
          clearable
          class="input-width-number"
          @on-change="onChangeOcrModel($event, 'standbyAlgorithm')"
        >
          <template v-for="(algorithmItem, index) in algorithmVendorData">
            <Option
              :value="`${algorithmItem.algorithmType},${algorithmItem.algorithmVendorType}`"
              :key="`algorithmItem${index}2`"
              :data="algorithmItem.algorithmType"
            >
              {{ algorithmItem.algorithmVendorTypeName }}-{{ algorithmItem.algorithmTypeName }}
            </Option>
          </template>
        </Select>
      </FormItem>
      <FormItem class="mb-xs" v-if="customFormData.standbyAlgorithm">
        <Checkbox
          v-model="customFormData.standbyAlgorithm751"
          @on-change="onChange751($event, 'standbyAlgorithm')"
          :true-value="1"
          :false-value="0"
          >按照《GAT-751-2008视频图像文字标注规范》要求检测</Checkbox
        >
      </FormItem>
      <FormItem
        class="mb-md"
        prop="standbyAlgorithmDetectContent"
        v-if="customFormData.standbyAlgorithm && customFormData.standbyAlgorithm751"
      >
        <template #label>
          <Tooltip placement="top-start" class="tooltip-sample-graph">
            <i class="icon-font icon-wenhao f-16 mr-xs" :style="{ color: 'var(--color-warning)' }"></i>
            <span>检测内容</span>
            <div slot="content">
              <div class="check-content-img">
                <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
              </div>
            </div>
          </Tooltip>
        </template>
      </FormItem>
      <detect-content
        @on-change="
          (formData, detectContent) => {
            onChangeFormData(formData, detectContent, 'standbyAlgorithm');
          }
        "
        ref="standbyAlgorithmDetectContent"
        form-model="edit"
        v-bind="$props"
        :config-info="formData.standbyAlgorithm"
        :detect-content-list="formData.standbyAlgorithm.detectContent"
        v-if="customFormData.standbyAlgorithm && customFormData.standbyAlgorithm751"
      >
      </detect-content>
    </template>
  </basic-recheck>
</template>

<script>
import { mapGetters } from 'vuex';
import { defaultDetectContent } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';

export default {
  name: 'url-available',
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
    DetectContent:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content.vue')
        .default,
  },
  props: {},
  data() {
    const validateOcrModel = (rule, value, callback) => {
      if (this.formData.snap && !this.customFormData.optimizationAlgorithm) {
        callback(new Error('请选择优先算法'));
      }
      callback();
    };
    const validateOptimizationAlgorithmDetectContent = ({ triggerElement }, value, callback) => {
      if (this.customFormData[`${triggerElement}751`] && this.formData[triggerElement].detectContent.length === 0) {
        callback(new Error('请选择检测内容'));
      }
      callback();
    };
    return {
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: 1, // 复检计划
        scheduletime: '', // 自定义时间
        deviceIds: [],
        ocrModel: '',
        ocrModel2: '',
        visitTimeout: null,
        snap: null,
        isUpdatePhyStatus: '',
        optimizationAlgorithm: {
          //优先算法
          ocrModel: '', //算法
          algorithmType: '', //算法类型
          detectContent: [], //检测内容
          existsTime: false, //-是否在指定区域标注
          //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
          timeFormCheck: false,
          //（右上角）时间信息检测 --位置规范检测：
          timePositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。 右边距: min
          timePositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。 右边距: max
          timePositionCheckMax: null,
          //（右上角）时间信息检测 --准确性规则，
          timeDeviationCheck: false,
          //设备时间与标准时间允许偏差 XX 秒
          timeDeviationCheckValue: null,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsArea: false,
          //（右下角）区划与地址信息检测 --位置规范检测：
          areaPositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
          areaPositionCheckValueLine: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
          areaPositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
          areaPositionCheckMax: null,
          //地址信息只能占用一行
          areaPositionCheckLine: false,
          //（右下角）区划与地址信息检测 --准确性规则，
          areaDeviationCheck: false,
          // 省/市/区县、地点信息需与档案信息保持一致
          areaDeviationCheckValue: false,
          //队所（派出所）信息需标注准确（与组织机构表匹配）
          areaDeviationCheckLine: false,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsCamera: false,
          //准确性规则
          cameraDeviationCheck: false,
        },
        standbyAlgorithm: {
          //备用算法
          ocrModel: '',
          algorithmType: '',
          detectContent: [],
          existsTime: false, //-是否在指定区域标注
          //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
          timeFormCheck: false,
          //（右上角）时间信息检测 --位置规范检测：
          timePositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。 右边距: min
          timePositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。 右边距: max
          timePositionCheckMax: null,
          //（右上角）时间信息检测 --准确性规则，
          timeDeviationCheck: false,
          //设备时间与标准时间允许偏差 XX 秒
          timeDeviationCheckValue: null,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsArea: false,
          //（右下角）区划与地址信息检测 --位置规范检测：
          areaPositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
          areaPositionCheckValueLine: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
          areaPositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
          areaPositionCheckMax: null,
          //地址信息只能占用一行
          areaPositionCheckLine: false,
          //（右下角）区划与地址信息检测 --准确性规则，
          areaDeviationCheck: false,
          // 省/市/区县、地点信息需与档案信息保持一致
          areaDeviationCheckValue: false,
          //队所（派出所）信息需标注准确（与组织机构表匹配）
          areaDeviationCheckLine: false,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsCamera: false,
          //准确性规则
          cameraDeviationCheck: false,
        },
      },
      specificConfig: {},
      customFormData: {
        optimizationAlgorithm: '',
        standbyAlgorithm: '',
        optimizationAlgorithm751: '',
        standbyAlgorithm751: '',
      },
      detectionRules: [
        {
          label: '',
          text: '(右上角) 时间信息是否正确',
          value: 'timeLocation',
        },
        {
          label: '',
          text: '(右下角) 区划与地址信息是否正确',
          value: 'areaLocation',
        },
        {
          label: '',
          text: '(左下角) 摄像机信息是否正确',
          value: 'cameraInfo',
        },
      ],
      ruleCustom: {
        ocrModel: {
          validator: validateOcrModel,
          required: true,
          message: '请选择优先算法',
          trigger: 'change',
        },
        optimizationAlgorithmDetectContent: {
          validator: validateOptimizationAlgorithmDetectContent,
          required: true,
          trigger: 'change',
          triggerElement: 'optimizationAlgorithm',
        },
        standbyAlgorithmDetectContent: {
          validator: validateOptimizationAlgorithmDetectContent,
          required: true,
          trigger: 'change',
          triggerElement: 'standbyAlgorithm',
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      algorithmVendorData: 'algorithm/getAlgorithmList',
    }),
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          deviceIds: this.formData.deviceIds,
          visitTimeout: this.formData.visitTimeout,
          snap: this.formData.snap,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
          ...this.specificConfig,
        },
        ruleCustom: this.ruleCustom,
        ...this.$attrs,
      };
    },
  },
  watch: {},
  filter: {},
  created() {},
  methods: {
    onChangeFormData(formData, detectContent, ref) {
      this.specificConfig[ref] = {
        ocrModel: this.formData[ref].ocrModel, //算法
        algorithmType: this.formData[ref].algorithmType, //算法类型
        detectContent: detectContent, //检测内容
        ...formData,
      };
    },
    onChange751(val, key) {
      this.formData[key] = {
        ...this.formData[key],
        detectContent: val ? ['timeLocation', 'areaLocation'] : [],
        ...defaultDetectContent,
      };
      this.specificConfig[key] = {
        ...this.specificConfig[key],
        detectContent: val ? ['timeLocation', 'areaLocation'] : [],
        ...defaultDetectContent,
      };
    },
    setOcrModel() {
      // 删除无用字段 ocrModel ocrModel2
      delete this.formData.ocrModel;
      delete this.formData.ocrModel2;
      this.specificConfig = {
        optimizationAlgorithm: this.formData.optimizationAlgorithm,
        standbyAlgorithm: this.formData.standbyAlgorithm,
      };
      if (this.formData.optimizationAlgorithm) {
        let { ocrModel, algorithmType, detectContent } = this.formData.optimizationAlgorithm;
        let algorithmVendor = this.algorithmVendorData.find(
          (item) => item.algorithmVendorType === ocrModel && item.algorithmType === algorithmType,
        );
        this.customFormData.optimizationAlgorithm = algorithmVendor
          ? `${algorithmVendor.algorithmType},${algorithmVendor.algorithmVendorType}`
          : '';
        this.customFormData.optimizationAlgorithm751 = detectContent.length > 0 ? 1 : 0;
      }
      if (this.formData.standbyAlgorithm) {
        let { ocrModel, algorithmType, detectContent } = this.formData.standbyAlgorithm;
        let algorithmVendor = this.algorithmVendorData.find(
          (item) => item.algorithmVendorType === ocrModel && item.algorithmType === algorithmType,
        );
        this.customFormData.standbyAlgorithm = algorithmVendor
          ? `${algorithmVendor.algorithmType},${algorithmVendor.algorithmVendorType}`
          : '';
        this.customFormData.standbyAlgorithm751 = detectContent.length > 0 ? 1 : 0;
      }
    },
    /**
     *
     * @param val 算法值
     * @param key 'optimizationAlgorithm' || 'standbyAlgorithm'
     */
    onChangeOcrModel(val, key) {
      let valList = val ? val.split(',') : [];
      let type = valList[0];
      let vendorType = valList[1];
      this.customFormData[`${key}751`] = 0;
      let { algorithmVendorType, algorithmType } =
        this.algorithmVendorData.find(
          (item) => item.algorithmType === type && item.algorithmVendorType === vendorType,
        ) || {};
      this.formData[key] = {
        ocrModel: algorithmVendorType,
        algorithmType: algorithmType,
        detectContent: [],
        ...this.defaultDetectContent,
      };
      this.specificConfig[key] = {
        ocrModel: algorithmVendorType,
        algorithmType: algorithmType,
        detectContent: [],
        ...this.defaultDetectContent,
      };
    },
    onChangeSnap() {
      this.formData.optimizationAlgorithm = {
        //优先算法
        ocrModel: '', //算法
        algorithmType: '', //算法类型
        detectContent: [], //检测内容
      };
      this.formData.standbyAlgorithm = {
        //备用算法
        ocrModel: '',
        algorithmType: '',
        detectContent: [],
      };
      this.specificConfig.optimizationAlgorithm = {
        //优先算法
        ocrModel: '', //算法
        algorithmType: '', //算法类型
        detectContent: [], //检测内容
      };
      this.specificConfig.standbyAlgorithm = {
        //备用算法
        ocrModel: '',
        algorithmType: '',
        detectContent: [],
      };
      this.customFormData = {
        optimizationAlgorithm: '',
        standbyAlgorithm: '',
        optimizationAlgorithm751: '',
        standbyAlgorithm751: '',
      };
    },
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: 1, // 复检计划
        scheduletime: '', // 自定义时间
        deviceIds: [], //批量复检 选择设备
        isUpdatePhyStatus: '',
        visitTimeout: null,
        snap: null,
        optimizationAlgorithm: {
          //优先算法
          ocrModel: '', //算法
          algorithmType: '', //算法类型
          detectContent: [], //检测内容
        },
        standbyAlgorithm: {
          //备用算法
          ocrModel: '',
          algorithmType: '',
          detectContent: [],
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.input-width-number {
  width: 380px;
}
@{_deep} .ivu-modal-body {
  max-height: 650px;
  overflow-y: auto;
}
@{_deep} .detect-content {
  margin-left: 70px;
}

@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: #062f68;
      border: none;
      color: var(--color-primary);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: #ffffff;
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
    }
  }
}
</style>
