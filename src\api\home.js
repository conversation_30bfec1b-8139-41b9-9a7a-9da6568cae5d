import request from "@/libs/request";
import { auth, service, manager, mapService } from "./Microservice";
import Setting from "@/libs/configuration/setting";

// 当前在线列表
export function onLine(data) {
  return request({
    url: auth + "/token/page",
    method: "post",
    data,
  });
}
// 获取有效期
export function querySysOauthClient(data) {
  return request({
    url: service + "/system/SysOauthClientDetails/querySysOauthClient",
    method: "post",
    data,
  });
}
// 新增云搜
export function addMyCloudSearch(data) {
  return request({
    url: manager + "/workbench/cloudSearch/addMyCloudSearch",
    method: "post",
    data,
  });
}
// 我的云搜记录
export function queryCloudSearchpageList(data) {
  return request({
    url: manager + "/workbench/cloudSearch/queryCloudSearchpageList",
    method: "post",
    data,
  });
}
// 快捷应用
export function queryMyQuickApplicationList(data) {
  return request({
    url: manager + "/workbench/quickApplication/queryMyQuickApplicationList",
    method: "post",
    data,
  });
}
// 编辑快捷应用
export function motifyMyQuickApplication(data) {
  return request({
    url: manager + "/workbench/quickApplication/motifyMyQuickApplication",
    method: "post",
    data,
  });
}
// 我的收藏列表
export function queryMyFavoritePageList(data) {
  return request({
    url: manager + "/myFavorite/queryMyFavoritePageList",
    method: "post",
    data,
  });
}
// 我的收藏分页列表
export function queryUserFavoritePage(data) {
  return request({
    url: manager + "/myFavorite/queryUserFavoritePage",
    method: "post",
    data,
  });
}
// 应用列表
export function resourceList() {
  return request({
    url:
      service +
      `/system/resource/resourceList?applicationCode=${applicationCode}`,
    method: "GET",
  });
}

// 作战列表
export function pageList(data) {
  return request({
    url: mapService + "/combat/record/pageList",
    method: "post",
    data,
  });
}
// 数据来源
export function queryDataByKeys(data) {
  return request({
    url: manager + "/dw/queryDataByKeys",
    method: "post",
    data,
  });
}
// 数据来源-新
export function queryDataByKeysNew(data) {
  return request({
    url: manager + "/workbench/statisticsData/queryDeviceAlarmTotal",
    method: "post",
    data,
  });
}
// 查询我的云盘-视频图片记录分页列表
export function diskPageList(data) {
  return request({
    url: manager + "/cloud/disk/pageList",
    method: "post",
    data,
  });
}
// 我的云盘-视频图片记录删除
export function diskRemove(id) {
  return request({
    url: manager + "/cloud/disk/remove/" + id,
    method: "delete",
  });
}
//档案统计
export function queryArchiveStatisticsData() {
  return request({
    url: manager + "/workbench/statisticsData/queryArchiveStatisticsData",
    method: "post",
  });
}
//档案统计-图表
export function queryChartStatisticsData(data) {
  return request({
    url: manager + "/workbench/statisticsData/queryChartStatisticsData",
    method: "post",
    data,
  });
}

// 数据来源
export function answer(data) {
  return request(
    {
      url: "/v1" + "/api/public_chat/",
      method: "post",
      data,
    },
    {
      Authorization:
        "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxNDEwLCJjZWxscGhvbmUiOiIxMzk1MTY2NDM0MiIsImV4dHJhIjoiMjAyMy0xMS0wOCAxMDo0NToyOSIsInByb2plY3RfbmFtZSI6ImNoYXQtd2ViLXN2ci12MSJ9.I6a55LlpU6VuZrLA-iouGmBtr1sZvnEfByiv8IGt67s",
    }
  );
}
