<template>
  <div class="labels-list">
    <div
      class="label-item"
      v-for="item in labelList"
      :key="item.id"
      :style="{
        color: item.labelColor,
        borderColor: item.labelColor,
      }"
    >
      {{ item.labelName }}
    </div>
  </div>
</template>
<script>
export default {
  name: "LabelList",
  props: {
    labelList: {
      type: Array,
      default: () => [],
    },
  },
};
</script>
<style lang="less" scoped>
.labels-list {
  display: flex;
  flex-flow: wrap;
  gap: 5px;
  .label-item {
    background: #ecf4ff;
    border-radius: 2px;
    font-size: 12px;
    color: #2c86f8;
    padding: 0 6px;
    height: 28px;
    line-height: 28px;
    border: 1px solid #2c86f8;
  }
}
</style>
