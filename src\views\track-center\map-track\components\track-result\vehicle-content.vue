<template>
  <div class="vehicle-content-wrapper">
    <div class="btn-list">
      <Button @click="handleSort" size="small">
        <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        时间排序
      </Button>
      <Button
        v-show="updateLayerId"
        size="small"
        @click="handleExport"
        :disabled="!updateLayerId"
        v-if="dataList.length"
      >
        <ui-icon
          type="daoru"
          :color="updateLayerId ? '#2C86F8' : 'rgba(0, 0, 0, 0.35)'"
        ></ui-icon>
        导出
      </Button>
    </div>
    <div class="vehicle-content warpper-box">
      <div
        v-for="(e, i) in dataList"
        :key="i"
        class="vehicle-item box-item"
        :class="{ active: currentClickIndex == i }"
        @click="chooseMapItem(i)"
      >
        <div class="header">
          <div class="header-left">
            <span
              class="serialNumber"
              :class="{ activeNumber: currentClickIndex == i }"
            >
              <span>{{ i + 1 }}</span>
            </span>
            <!-- <span class="license-plate-small">{{ e.plateNo || '--' }}</span> -->
            <ui-plate-number
              :plateNo="e.plateNo"
              :color="e.plateColor"
            ></ui-plate-number>
          </div>
          <ui-icon
            v-if="canWrite"
            class="ml-5 mr-5"
            type="shanchu"
            @click.native.stop="deleteItem(e, i)"
          ></ui-icon>
        </div>
        <div class="content">
          <div class="content-left">
            <img v-lazy="e.traitImg" alt="" />
          </div>
          <div class="content-right">
            <span class="ellipsis">
              <ui-icon type="time" :size="14"></ui-icon>
              <span>{{ e.absTime || "--" }}</span>
            </span>
            <span class="ellipsis">
              <ui-icon type="location" :size="14"></ui-icon>
              <span>{{ e.deviceName || "--" }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="!dataList.length" />
  </div>
</template>

<script>
import { exportExcel } from "@/api/operationsOnTheMap";
export default {
  props: {
    // 当前点击顺序
    currentClickIndex: {
      type: Number,
      default: -1,
    },
    orderType: {
      type: String,
      default: "",
    },
    updateLayerId: {
      type: [String, Number],
      default: "",
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    // 写权限，被分享的只能读
    canWrite: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      timeUpDown: false,
    };
  },
  watch: {
    // currentClickIndex: {
    //     handler (newVal){
    //         if(newVal > -1){
    //             let list =  document.querySelectorAll('.vehicle-item');
    //             list[newVal].scrollIntoView(false)
    //         }
    //     },
    // },
    orderType: {
      handler(newVal) {
        this.timeUpDown = newVal == "desc" ? false : true;
      },
    },
  },
  methods: {
    chooseMapItem(index) {
      this.$emit("chooseMapItem", index);
    },
    // 排序
    handleSort(val) {
      this.timeUpDown = !this.timeUpDown;
      this.$emit("changeOrder", this.timeUpDown ? "asc" : "desc");
    },
    handleExport() {
      exportExcel({
        id: this.updateLayerId,
        excelType: "vehicle",
      }).then((res) => {
        if (res.data) {
          let aLink = document.createElement("a");
          aLink.href = res.data;
          aLink.click();
        }
      });
    },
    deleteItem(item, index) {
      this.$emit("deleteItem", item, index);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
.vehicle-content {
  .header-left {
    width: 100%;
  }
}
</style>
