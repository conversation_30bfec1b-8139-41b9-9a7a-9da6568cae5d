<template>
  <div class="title-container">
    <span class="square vt-middle inline lh-1" :style="{ background: squareColor, height: `${fontSize}px` }"></span>
    <span
      class="default-color vt-middle inline ml-xs mr-md lh-1"
      :style="{ color: textColor, fontSize: `${fontSize}px` }"
      >{{ label }}</span
    >
    <span class="fr">
      <slot name="filter"></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'textTitle',
  components: {},
  props: {
    icon: {
      type: String,
      default: '',
    },
    squareColor: {
      type: String,
      default: 'var(--color-display-title-before)',
    },
    textColor: {
      type: String,
      default: 'var(--color-display-title)',
    },
    label: {
      type: String,
      default: '',
    },
    fontSize: {
      type: [String, Number],
      default: '14',
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.square {
  width: 4px;
  height: 15px;
  background: var(--color-primary);
}

.lh-1 {
  line-height: 1;
}
.title-container {
  position: relative;
  width: 100%;
  .default-color {
    color: var(--color-content);
  }
}
</style>
