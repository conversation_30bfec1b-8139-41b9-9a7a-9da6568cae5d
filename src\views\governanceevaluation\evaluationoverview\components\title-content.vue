<template>
  <div class="title-center">
    <div class="conter-center">
      <span>{{ title }}</span>
    </div>
  </div>
</template>
<style lang="less" scoped>
[data-theme='light'] {
  .title-center {
    .conter-center {
      background: linear-gradient(180deg, #2c86f8 0%, rgba(216, 216, 216, 0) 189%);
      clip-path: polygon(0% 0%, 100% 0%, 75% 100%, 20% 100%);
    }
  }
}
[data-theme='deepBlue'] {
  .title-center {
    .conter-center {
      background: linear-gradient(180deg, #113961 0%, #D8D8D8 189%);
      clip-path: polygon(0% 0%, 100% 0%, 75% 100%, 20% 100%);
    }
  }
}
.title-center {
  height: 100%;
  .conter-center {
    height: 30px;
    font-size: 16px;
    font-weight: 400;
    display: inline-block;
    vertical-align: middle;
    background: linear-gradient(180deg, #2c86f8 0%, rgba(216, 216, 216, 0) 189%);
    clip-path: polygon(0% 0%, 100% 0%, 75% 100%, 20% 100%);
    background-size: cover;
    span {
      line-height: 30px;
      height: 100%;
      padding: 0 100px;
      color: #fff;
      display: inline-block;
    }
  }
}
</style>
<script>
export default {
  data() {
    return {};
  },
  props: {
    title: {},
  },
};
</script>
