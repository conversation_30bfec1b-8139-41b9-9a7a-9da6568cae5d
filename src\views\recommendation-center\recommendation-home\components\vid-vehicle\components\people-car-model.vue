<template>
    <hlModal
        v-model="modalShow"
        :r-width="1600"
        :title="driverFlag == 1 ? '主驾车辆' : '副驾车辆'"
        list-content
        @onCancel="handleCancel"
        @onOk="confirmHandle"
    >
        <div class="content-box">
            <div class="table-container">
                <div class="table-content">
                    <div class="list-card box-1"
                        v-for="(item, index) in dataList"
                        :key="index">
                        <div class="img-content">
                            <ui-image
                                :src="item.sceneImg"
                                alt="动态库"
                                @click.native="faceDetailFn(item, index)"
                            />
                            <b class="shade vehicle">
                                <ui-plate-number :plateNo="item.plateNo" :color="item.plateColor" size='mini'></ui-plate-number>
                            </b>
                        </div>
                        <div class="bottom-info">
                            <time>
                                <Tooltip
                                    content="抓拍时间"
                                    placement="right"
                                    transfer
                                    theme="light"
                                >
                                    <i class="iconfont icon-time"></i>
                                </Tooltip>
                                {{ item.absTime }}
                            </time>
                            <p>
                                <Tooltip
                                    content="抓拍地点"
                                    placement="right"
                                    transfer
                                    theme="light"
                                >
                                    <i class="iconfont icon-location"></i>
                                </Tooltip>
                                <ui-textOver-tips
                                    refName="captureAddress"
                                    :content="item.captureAddress"
                                ></ui-textOver-tips>
                            </p>
                        </div>
                    </div>
                    <div
                        class="empty-card-1"
                            v-for="(item, index) of 9 - (dataList.length % 9)"
                            :key="index + 'demo'"
                        ></div>
                        <ui-empty v-if="dataList.length === 0 && !loading" ></ui-empty>
                        <ui-loading v-if="loading"></ui-loading>
                </div>
            </div>

        </div>
        <div slot="footer">
            <div></div>
        </div>
        <!-- 车辆详情 -->
        <details-vehicle-modal
            v-if="videoShow"
            ref="videoDetail"
            @close="videoShow = false"
        ></details-vehicle-modal>
    </hlModal>
</template>
<script>
import hlModal from '@/components/modal/index.vue';
import { vidVehicleBindingTrail } from "@/api/recommend";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
export default{
    components: { hlModal, detailsVehicleModal },
    data() {
        return {
            modalShow: false,
            dataList: [],
            loading: false,
            params: {},
            driverFlag: 1,
            videoShow: false,
        }
    },
    created() {
    },
    methods: {
        show(params, driverFlag) {
            this.modalShow = true;
            this.params = params
            this.driverFlag = driverFlag
            this.queryList()
        },
        queryList() {
            this.loading = true;
            this.dataList = [];
            vidVehicleBindingTrail(this.params)
            .then(res => {
                this.dataList = res.data.filter(v => v.driverFlag == this.driverFlag);
            })
            .finally(() => {
                this.loading = false;
            })
        },
        faceDetailFn(row, index) {
            this.videoShow = true;
            this.$nextTick(() => {
                let collectionType = 5;
                this.$refs.videoDetail.init(
                    row,
                    this.dataList.map(v => {
                        v.traitImg = v.sceneImg
                        return v
                    }),
                    index,
                    collectionType,
                    1
                );
            });
        },
        confirmHandle() {},
        handleCancel() {},
    }
}
</script>
<style lang="less" scoped>
.content-box{
    height: 640px;
    display: flex;
    flex-direction: column;
    .search-bar{
        display: flex;
        justify-content: space-between;
    }
    .table-container{
        overflow: auto;
        display: flex;
        flex: 1;
        flex-direction: column;
        position: relative;
        .table-content {
            overflow-y: auto;
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            justify-content: start;
            align-content: flex-start;
            .list-card {
                position: relative;
                background-color: #f9f9f9;
                margin-bottom: 10px;
                height: min-content;
                box-sizing: border-box;
                .operate-bar {
                    width: 70px;
                    height: 30px;
                    background: linear-gradient(
                        90deg,
                        rgba(87, 187, 252, 0.8) 0%,
                        #2c86f8 100%
                    );
                    border-radius: 0px 0px 4px 0px;
                    position: absolute;
                    right: -100%;
                    transition: all 0.3s;
                    bottom: 0;
                    transform: skewX(-20deg);
                    .operate-content {
                        padding: 0 5px;
                        transform: skewX(20deg);
                        height: 100%;
                        display: flex;
                        align-items: center;
                        color: #fff;
                        /deep/ .ivu-tooltip-rel {
                            padding: 6px;
                        }
                    }
                }
            }
            .empty-card-1 {
                width: 12%;
            }
            .box-1 {
                width: 12%;
                padding: 10px;
                border-radius: 4px;
                border: 1px solid #d3d7de;
                box-shadow: 1px 1px 7px #cdcdcd;
                margin-left: 6px;
                &:hover {
                    border: 1px solid #2c86f8;
                    .operate-bar {
                        right: -6px;
                        bottom: -1px;
                    }
                    &:before {
                        border-color: #2c86f8;
                    }
                }
                .img-content {
                    width: 100%;
                    position: relative;
                    border: 1px solid #cfd6e6;
                    height: 167px;
                    img {
                        width: 100%;
                        height: 100%;
                        display: block;
                    }
                    .num,
                    .shade {
                        position: absolute;
                    }
                    .shade {
                        position: absolute;
                        background: rgba(0, 0, 0, 0.7);
                        font-size: 12px !important;
                        width: 100%;
                        bottom: 0;
                        left: 0;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                        line-height: 18px;
                        padding: 3px 0;
                    }
                    .num {
                        position: absolute;
                        top: 0;
                        left: 0;
                        z-index: 8;
                        font-size: 12px;
                        padding: 2px 5px;
                        border-radius: 4px;
                        color: #fff;
                        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
                    }
                    
                }
                .bottom-info {
                    padding-top: 5px;
                    time,
                    p {
                        display: flex;
                        align-items: center;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: rgba(0, 0, 0, 0.8);
                        white-space: nowrap;
                        width: 100%;
                        .iconfont {
                            margin-right: 2px;
                            color: #888;
                        }
                    }
                    .device-name{
                        cursor: pointer;
                    }
                }
            }
        }
    }
}
</style>