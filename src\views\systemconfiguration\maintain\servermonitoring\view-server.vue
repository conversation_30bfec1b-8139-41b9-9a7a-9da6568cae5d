<template>
  <div v-if="viewData.status">
    <ui-modal title="服务器详情" v-model="visible" width="80rem" :footer-hide="true">
      <div class="modal-content-wrap auto-fill">
        <div class="detail-content table-text-content detail-title">
          {{ viewData?.serverName }} | {{ viewData?.ip }}
        </div>
        <div class="detail-content secondary mb-sm">
          <div class="mb-sm">
            <span class="parameter"
              >操作系统:
              <span class="table-text-content">{{
                viewData.system.arch || '' + ' ' + viewData.system.name + ' ' + viewData.system.version
              }}</span></span
            >
            <span class="parameter"
              >CPU:
              <span class="table-text-content"
                >{{ viewData.cpu.vendor }} {{ viewData.cpu.model }} {{ viewData.cpu.totalCore }}核
                {{ viewData.cpu.threadCount }}线程</span
              ></span
            >
            <span class="parameter"
              >GPU:
              <span class="table-text-content">
                {{ computedGpu.gpusText }}
              </span></span
            >
          </div>
          <div>
            <span class="parameter"
              >内存: <span class="table-text-content">{{ viewData.memory.total }}G</span></span
            >
            <span class="parameter"
              >磁盘: <span class="table-text-content">{{ viewData.disc.total }}G</span></span
            >
            <span class="parameter"
              >网卡:
              <span class="table-text-content"
                >{{ viewData.net?.netCardCount || '-' }} {{ viewData.net?.maxSpeed || '-' }}Mbps</span
              ></span
            >
          </div>
        </div>
        <div class="statistics table-head-category mb-sm over-flow">
          <div class="detail-parameter border">
            <div class="mb-xs" v-for="(item, index) in viewData.disc.discDetailList" :key="index">
              <span class="detail-parameter-description ellipsis" :title="item.discName">{{ item.discName }}</span>
              <Progress class="width-xs ml-sm" :percent="item.usePercent" status="normal" hide-info />
              <span
                class="ml-sm detail-parameter-description ellipsis"
                :title="`${item.use}GB/${item.total}GB/${item.usePercent}%`"
                >{{ item.use }}GB/{{ item.total }}GB/{{ item.usePercent }}%</span
              >
            </div>
          </div>
          <div class="charts-box ml-lg" ref="chartsBox">
            <div class="chart-item">
              <draw-echarts :echart-option="echartCPURing" :echart-style="ringStyle" ref="CPUChart"></draw-echarts>
            </div>
            <div class="chart-item">
              <draw-echarts :echart-option="echartRAMRing" :echart-style="ringStyle" ref="RAMChart"></draw-echarts>
            </div>
            <template v-if="echartGPURings?.length">
              <div class="chart-item" v-for="(item, index) in echartGPURings" :key="index">
                <draw-echarts :echart-option="item" :echart-style="ringStyle" ref="GPUChart"></draw-echarts>
              </div>
            </template>
          </div>

          <div class="ml-lg speed">
            <div class="speed-title border">
              <i class="icon-font icon-wangluo"></i>
              <span class="ml-mini">网络</span>
            </div>
            <div class="ml-sm">
              <p>
                <span class="vt-middle speed-text">{{ viewData.net?.upLinkSpeed | filterSpeed }}</span>
                <i class="icon-font icon-xiangshang ml-sm" style="color: var(--color-progress-warning)"></i>
              </p>
              <p class="mt-sm">
                <span class="vt-middle speed-text">{{ viewData.net?.downLinkSpeed | filterSpeed }}</span>
                <i class="icon-font icon-xiangxia ml-sm" style="color: var(--color-progress-success)"></i>
              </p>
            </div>
          </div>
        </div>
        <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #ip="{}">
            <div>{{ viewData.ip }}</div>
          </template>
          <template #status="{ row }">
            <div
              class="status-tag"
              :style="`background: ${!!row.status ? 'var(--color-success)' : 'var(--color-failed)'}`"
            >
              {{ !!row.status ? '在线' : '离线' }}
            </div>
          </template>
          <template #action="{ row }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-zhongqi"
              content="重启"
              :styles="{ color: ' #269F26' }"
              @click.native="refresh(row)"
            ></ui-btn-tip>
          </template>
        </ui-table>
      </div>

      <!-- <ui-page
        class="page"
        :page-data="searchData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page> -->
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-content: center;
}
.detail-content {
  border-bottom: 1px solid var(--devider-line);
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.detail-title {
  font-size: 16px;
  font-weight: bold;
  color: var(--color-title);
}
.parameter {
  display: inline-block;
  width: 500px;
  font-size: 14px;
  color: var(--color-label);
}
.table-text-content {
  padding-left: 10px;
  color: var(--color-content);
}
.statistics {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .detail-parameter {
    height: 166px;
    width: 480px;
    overflow: auto;
    padding: 15px;
    background: var(--bg-info-card);
    .detail-parameter-description {
      width: 90px;
      display: inline-block;
      vertical-align: text-top;
    }
  }
}
.text-description {
  line-height: 166px;
}
.speed {
  width: 288px;
  display: flex;
  justify-content: center;
  align-items: center;
  .speed-title {
    padding: 10px;
    border: 1px soild;
  }
  .speed-img {
    width: 20px;
    height: 20px;
  }
}
.speed-text {
  color: var(--color-content);
}
.modal-content-wrap {
  width: 100%;
  height: 800px;
}
.status-tag {
  width: 60px;
  height: 22px;
  line-height: 22px;
  text-align: center;
  border-radius: 4px;
  color: #ffffff;
}

.charts-box {
  overflow: auto;
  display: flex;
  align-items: center;
}
</style>
<script>
let timer = null;
import maintain from '@/config/api/maintain';
export default {
  data() {
    return {
      visible: false,
      loading: false,
      minusTable: 600,
      ringStyle: {
        width: '147px',
        padding: '0 23px',
        height: '0.86rem',
      },
      echartCPURing: {},
      echartGPURing: {},
      echartGPURings: [],
      echartRAMRing: {},
      searchData: {
        serverId: '',
        totalCount: 0,
        pageNumber: 1,
        pageSize: 20,
      },
      tableColumns: [
        { type: 'index', title: '序号', width: 50, align: 'center' },
        { title: '服务名称', key: 'serviceName', minWidth: 400 },
        { title: '端口号', key: 'port', width: 250 },
        { title: 'IP', slot: 'ip', minWidth: 200 },
        { title: '状态', slot: 'status', width: 120 },
        {
          title: '操作',
          slot: 'action',
          width: 60,
          fixed: 'right',
          align: 'center',
          className: 'table-action-padding',
          // render: (h, column) => {
          //   const row = column.row;
          //   return h(
          //     "div",
          //     this.actionList.map(item => {
          //       return h("i", {
          //         class: item.icon,
          //         style: item.style,
          //         domProps: {
          //           title: item.title
          //         },
          //         on: {
          //           click: () => {
          //             switch (item.action) {
          //               case "refresh":
          //                 this.refresh(row);
          //                 break;
          //             }
          //           }
          //         }
          //       });
          //     })
          //   );
          // }
        },
      ],
      tableData: [],
      // actionList: [
      //   { icon: "icon-refresh", action: "refresh", title: "重启", style: {marginRight: "20px",cursor: "pointer"} }
      // ],
    };
  },
  created() {},
  mounted() {},
  filters: {
    filterSpeed(val) {
      let netSpeed = '';
      let unit = '';
      // 判断网速大小 更改单位
      if (val / 1024 > 1) {
        if (val / 1024 / 1024 > 1) {
          netSpeed = (val / 1024 / 1024).toFixed(2);
          unit = 'GB/s';
        } else {
          netSpeed = (val / 1024).toFixed(2);
          unit = 'MB/s';
        }
      } else {
        netSpeed = val;
        unit = 'KB/s';
      }
      return netSpeed + ' ' + unit;
    },
  },
  methods: {
    async initService() {
      try {
        this.loading = true;
        let res = await this.$http.post(maintain.getServiceList, this.searchData);
        this.tableData = res.data.data;
        this.searchData.totalCount = res.data.data.totalCount;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    /**
     * 设备
     * echarts图表必须初始化
     **/
    initRing() {
      //GPU配置方法
      const getGPUOpts = (gpuItem) => {
        const percent = (gpuItem.gpuMemUsed / gpuItem.gpuMemTotal) * 100;
        const formatPercent = Number.isInteger(percent) ? percent : percent.toFixed(2) * 1;
        let GPUOpts = {
          color: percent > 90 ? $var('--color-error') : $var('--color-warn'),
          percent: formatPercent,
          text: `${gpuItem.model}/GPU内存`,
          parameter: `${gpuItem.gpuMemUsed}GB/${gpuItem.gpuMemTotal}GB`,
        };
        return this.$util.doEcharts.servermonitoringRing(GPUOpts);
      };
      let CPUOpts = {
        color: this.viewData.cpu.usePercent > 90 ? $var('--color-error') : $var('--color-normal'),
        percent: this.viewData.cpu.usePercent,
        text: 'CPU',
        parameter: '',
      };
      let RAMOpts = {
        color: this.viewData.memory.usePercent > 90 ? $var('--color-error') : '#2B84E2',
        percent: this.viewData.memory.usePercent,
        text: '内存',
        parameter: `${Number(this.viewData.memory.use).toFixed(1)}GB/${this.viewData.memory.total}GB`,
      };
      this.echartCPURing = this.$util.doEcharts.servermonitoringRing(CPUOpts);
      this.echartGPURings = this.viewData?.gpu.length
        ? this.viewData.gpu.map((gpuItem) => {
            return getGPUOpts(gpuItem);
          })
        : [];
      this.echartRAMRing = this.$util.doEcharts.servermonitoringRing(RAMOpts);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    async refresh(row) {
      this.$Modal.confirm({
        title: '警告',
        content: `您将要重启服务 ${row.serviceName}，是否确认?`,
        onOk: async () => {
          try {
            this.loading = true;
            await this.$http.post(maintain.serviceRestart, {
              serviceId: row.serviceId,
            });
            this.$Message.success('重启成功, 请等待');
            this.initService();
          } catch (err) {
            console.log(err);
          } finally {
            this.loading = false;
          }
        },
      });
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.tableData = [];
        this.initService();
        if (this.viewData.status) {
          this.$nextTick(() => {
            this.initRing();
            this.searchData.serverId = this.viewData.serverId;
            timer = setInterval(() => {
              this.$emit('update');
              this.initService();
            }, 3000);
          });
        }
      } else {
        clearInterval(timer);
        timer = null;
      }
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {
    computedGpu() {
      let gpusNum = this.viewData.gpu?.length ? this.viewData.gpu.length : 0;
      let gpusText = gpusNum ? this.viewData.gpu.map((item) => item.model).join(',') : '无';
      return {
        gpusText,
        gpusNum,
      };
    },
  },
  props: {
    value: {},
    viewData: {},
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
