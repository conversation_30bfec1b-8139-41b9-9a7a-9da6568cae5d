<template>
  <ui-modal v-model="visible" title="摄像机功能类型检测" width="40%" @query="handleSave">
    <Form ref="formValidate" :label-width="100">
      <FormItem label="检测逻辑配置">
        <Checkbox v-model="formValidate.source1Check" :true-value="1" :false-value="0"
          >来源联网平台的设备，但没有【视频监控】功能类型</Checkbox
        >
        <Checkbox v-model="formValidate.source3Check" :true-value="1" :false-value="0"
          >来源视图库的设备，但没有【人脸识别】或【车辆识别】功能类型</Checkbox
        >
        <Checkbox
          v-model="formValidate.faceCheck"
          :true-value="1"
          :false-value="0"
          @on-change="formValidate.faceLatelyDay = null"
          >近<InputNumber
            class="width-sm mr-sm ml-sm dis-select mb-sm"
            :disabled="!formValidate.faceCheck"
            v-model="formValidate.faceLatelyDay"
            placeholder="请输入天数"
          ></InputNumber
          >天，有人脸抓拍记录，但没有【人脸识别】功能类型</Checkbox
        >
        <Checkbox
          v-model="formValidate.vehicleCheck"
          :true-value="1"
          :false-value="0"
          @on-change="formValidate.vehicleLatelyDay = null"
          >近<InputNumber
            class="width-sm mr-sm ml-sm dis-select"
            :disabled="!formValidate.vehicleCheck"
            v-model="formValidate.vehicleLatelyDay"
            placeholder="请输入天数"
          ></InputNumber
          >天，有车辆抓拍记录，但没有【车辆识别】功能类型</Checkbox
        >
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    /**
     * 指标详情
     */
    formData: {
      required: true,
      default: () => {},
    },
    /**
     * 规则详情
     */
    indexRule: {
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      indexConfig: {},
      formValidate: {
        source1Check: 1, //--来源联网平台的设备，但没有【视频监控】功能类型  0未选中，1选中
        source3Check: 1, //--来源视图库的设备，但没有【人脸识别】或【车辆识别】功能类型   0未选中，1选中
        faceCheck: 1, // --近N天，有人脸抓拍记录，但没有【人脸识别】功能类型； 0未选中，1选中
        vehicleCheck: 1, //--近N天，有车辆抓拍记录，但没有【车辆识别】功能类型；        0未选中，1选中
        faceLatelyDay: 1, //-- faceCheck = 1 时需要填写
        vehicleLatelyDay: 2, //--vehicleCheck = 1 时需要填写
      },
    };
  },
  created() {},
  methods: {
    validateForm() {
      if (
        !this.formValidate.source1Check &&
        !this.formValidate.source3Check &&
        !this.formValidate.faceCheck &&
        !this.formValidate.vehicleCheck
      ) {
        this.$Message.warning('请配置检测逻辑');
        return false;
      }
      if (
        (this.formValidate.faceCheck && !this.formValidate.faceLatelyDay) ||
        (this.formValidate.vehicleCheck && !this.formValidate.vehicleLatelyDay)
      ) {
        this.$Message.warning('近**天不能为空');
        return false;
      }
      return true;
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getCameraConfig();
    },
    async getCameraConfig() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        let extraParam = JSON.parse(
          data.extraParam ||
            '{"source1Check":1,"source3Check":1,"faceCheck":1,"vehicleCheck":1,"faceLatelyDay":1,"vehicleLatelyDay":2}',
        );
        this.formValidate = { ...this.formValidate, ...extraParam };
      } catch (error) {
        console.log(error);
      }
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        if (!this.validateForm()) return;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify(this.formValidate),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  margin-top: 0 !important;
  padding: 20px 50px !important;
}
</style>
