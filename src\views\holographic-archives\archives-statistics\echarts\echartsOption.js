import dayjs from "dayjs";
export function everyDayTrendEcharts(options) {
  return {
    title: {
      text: "单位：万",
      textStyle: {
        color: "rgba(0, 0, 0, 0.35)",
        fontSize: 12,
      },
      top: 20,
      left: 20,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let series = ``;
        let title = "";
        params.forEach((item) => {
          let { axisValue, marker, seriesName, value } = item;
          title = `<div><span>${axisValue}</span></div>`;
          series += `<div>${marker}<span>${seriesName}</span> <span>${value}</span>万</div>`;
        });
        return title + series;
      },
    },
    legend: {
      top: 20,
      right: 20,
      icon: "rect",
      itemWidth: 20,
      itemHeight: 4,
      data: options.legend,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: options.xAxisData,
      axisLabel: {
        formatter: function (value) {
          const today = dayjs(value);
          return today.date();
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: false,
    },
    series: [
      {
        name: "实名档案",
        type: "line",
        stack: "Total",
        symbol: "none",
        itemStyle: {
          color: "rgba(242, 159, 76, 1)",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(242, 159, 76, 0.1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(242, 159, 76, 0)", // 100% 处的颜色
              },
            ],
          },
        },
        data: options.series.monthRealNameCountList,
      },
      {
        name: "视频档案",
        type: "line",
        stack: "Total",
        data: options.series.monthVidCountList,
        symbol: "none",
        itemStyle: {
          color: "rgba(31, 175, 129, 1)",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(31, 175, 129, 0.1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(31, 175, 129, 0)", // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
}

export function zdrEveryDayTrendEcharts(options) {
  return {
    title: {
      text: "单位：万",
      textStyle: {
        color: "rgba(0, 0, 0, 0.35)",
        fontSize: 12,
      },
      top: 20,
      left: 20,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let series = ``;
        let title = "";
        params.forEach((item) => {
          let { axisValue, marker, seriesName, value } = item;
          title = `<div><span>${axisValue}</span></div>`;
          series += `<div>${marker}<span>${seriesName}</span> <span>${value}</span>万</div>`;
        });
        return title + series;
      },
    },
    legend: {
      top: 20,
      right: 20,
      icon: "rect",
      itemWidth: 20,
      itemHeight: 4,
      data: options.legend,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: options.xAxisData,
      axisLabel: {
        formatter: function (value) {
          const today = dayjs(value);
          return today.date();
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: false,
    },
    series: [
      {
        name: options.legend[0],
        type: "line",
        stack: "Total",
        data: options.series.monthImportantCountList,
        symbol: "none",
        itemStyle: {
          color: "rgba(234, 74, 54, 1)",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(234, 74, 54, 0.1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(242, 159, 76, 0)", // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
}
export function vehicleEveryDayTrendEcharts(options) {
  return {
    title: {
      text: "单位：万",
      textStyle: {
        color: "rgba(0, 0, 0, 0.35)",
        fontSize: 12,
      },
      top: 20,
      left: 20,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let series = ``;
        let title = "";
        params.forEach((item) => {
          let { axisValue, marker, seriesName, value } = item;
          title = `<div><span>${axisValue}</span></div>`;
          series += `<div>${marker}<span>${seriesName}</span> <span>${value}</span>万</div>`;
        });
        return title + series;
      },
    },
    legend: {
      top: 20,
      right: 20,
      icon: "rect",
      itemWidth: 20,
      itemHeight: 4,
      data: options.legend,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: options.xAxisData,
      axisLabel: {
        formatter: function (value) {
          const today = dayjs(value);
          return today.date();
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: false,
    },
    series: [
      {
        name: options.legend[0],
        type: "line",
        stack: "Total",
        data: options.series.monthVehicleCountList,
        symbol: "none",
        itemStyle: {
          color: "rgba(167, 134, 255, 1)",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(167, 134, 255, 0.1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(44, 134, 248, 0)", // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
}
export function nonMotorVehicleEveryDayTrendEcharts(options) {
  return {
    title: {
      text: "单位：万",
      textStyle: {
        color: "rgba(0, 0, 0, 0.35)",
        fontSize: 12,
      },
      top: 20,
      left: 20,
    },
    tooltip: {
      trigger: "axis",
      formatter: function (params) {
        let series = ``;
        let title = "";
        params.forEach((item) => {
          let { axisValue, marker, seriesName, value } = item;
          title = `<div><span>${axisValue}</span></div>`;
          series += `<div>${marker}<span>${seriesName}</span> <span>${value}</span>万</div>`;
        });
        return title + series;
      },
    },
    legend: {
      top: 20,
      right: 20,
      icon: "rect",
      itemWidth: 20,
      itemHeight: 4,
      data: options.legend,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: options.xAxisData,
      axisLabel: {
        formatter: function (value) {
          const today = dayjs(value);
          return today.date();
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: false,
    },
    series: [
      {
        name: options.legend[0],
        type: "line",
        stack: "Total",
        data: options.series.monthNonVehicleCountList,
        symbol: "none",
        itemStyle: {
          color: "rgba(44, 134, 248, 1)",
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(44, 134, 248, 0.1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(72, 186, 255, 0)", // 100% 处的颜色
              },
            ],
          },
        },
      },
    ],
  };
}
