<template>
  <!-- 卡片 -->
  <div class="card cursor-p">
    <div class="flex-b card-top">
      <p class="ellipsis">
        <span class="shen-icon">身</span>
        <span class="card-num">{{ list.cardNum }}</span>
      </p>
      <i class="change-icon" @click="changeCard(list, indexInfo)"></i>
    </div>
    <div class="card-center flex-s">
      <img class="person-img" :src="list.img" alt="" />
      <div class="person-msg">
        <p class="flex-s" v-for="(it, index) in cardInfo" :key="index">
          <span class="fs-12">{{ it.name }}</span>
          <span class="address">{{ list[it.value] }}</span>
        </p>
      </div>
    </div>
    <tags-more
      :tagList="list.labelList"
      :defaultTags="defaultTags"
      placement="bottom-start"
    ></tags-more>
  </div>
</template>
<script>
export default {
  components: {
    TagsMore: require("./tags-more").default,
  },
  props: {
    // 卡片右侧字段
    cardInfo: {
      type: Array,
      default: () => [],
    },
    // 卡片数据
    list: {
      type: Object,
      default: () => {},
    },
    indexInfo: {
      type: Number,
      default: null,
    },
    // 标签数量
    defaultTags: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {};
  },
  watch: {
    defaultTags() {
      return this.defaultTags;
    },
  },
  created() {},
  methods: {
    // 实名档案/路人档案切换
    changeCard(item, index) {
      this.$$emit("changeCard", item, index);
    },
    // 查看详情
    detail() {
      this.$emit("detail");
    },
  },
};
</script>
<style lang="less" scoped>
.card {
  width: 19.55%;
  height: 196px;
  margin-bottom: 10px;
  margin-right: 10px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  border-radius: 4px;
  border: 1px solid #d3d7de;
  .card-top {
    width: 100%;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #d3d7de;
    p {
      width: 180px;
      .shen-icon {
        color: #fff;
        font-size: 12px;
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        background: #2c86f8;
        border-radius: 4px;
        margin: 0 10px;
      }
      .card-num {
        color: #2c86f8;
        font-size: 16px;
        font-weight: bold;
      }
    }
    .change-icon {
      display: inline-block;
      width: 40px;
      height: 100%;
      background: url("~@/assets/img/card/card-change.png") no-repeat;
    }
  }
  .card-center {
    margin: 10px;
    .person-img {
      width: 115px;
      height: 115px;
      margin-right: 10px;
      border: 1px solid #cfd6e6;
    }
    .person-msg {
      p {
        line-height: 23px;
        font-size: 14px;
        color: #000;
      }
      .fs-12 {
        font-size: 12px;
        font-weight: bold;
      }
      .address {
        display: inline-block;
        width: 78%;
        overflow: hidden;
        -webkit-line-clamp: 2;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
      }
    }
  }
  .tags-more {
    margin: 0 10px;
    /deep/.ivu-tag {
      margin: 0;
      padding: 0 6px;
      margin-right: 5px;
    }
    /deep/.ivu-tag-border {
      height: 22px;
      line-height: 20px;
      .ivu-tag-text {
        font-size: 12px;
      }
    }
  }
}
.card:nth-child(5n + 0) {
  margin-right: 0;
}
</style>
