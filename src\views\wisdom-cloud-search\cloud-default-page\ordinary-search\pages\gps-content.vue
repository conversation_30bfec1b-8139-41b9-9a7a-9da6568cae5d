<template>
	<section class="main-container">
		<div class="search-bar">
			<searchGps ref="searchGps" @searchInfo="searchInfo"></searchGps>
		</div>
		<div class="table-container">
			<div class="data-export">
				<Button class="mr mb" @click="handleSort('absTime')" size="small">
					<Icon type="md-arrow-round-down" v-if="!timeUpDown" />
					<Icon type="md-arrow-round-up" v-else />
					时间排序
				</Button>
			</div>
			<div class="table-content">
				<ui-table :columns="columns" :data="tableList">
					<template #objectType="{ row }">
						<span>{{ tranDictType(row.objectType)}}</span>
					</template>
					<template #lon="{ row }">
						<div>{{ row.geoPoint.lon }}</div>
					</template>
					<template #lat="{ row }">
						<div>{{ row.geoPoint.lat }}</div>
					</template>
					<template #state="{ row }">
						<span>{{ row.state=='1'?'在线':'离线' }}</span>
					</template>
					<template #action="{ row }">
						<div class="btn-tips">
							<ui-btn-tip content="定位" icon="icon-location" class="mr-20 primary" @click.native="openDirectModel(row)"></ui-btn-tip>
						</div>
					</template>
				</ui-table>
			</div>
			<!-- <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty> -->
			<ui-loading v-if="loading"></ui-loading>
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[40, 80, 120]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
			<direction-model ref="directionModel"></direction-model>

		</div>
	</section>
</template>
<script>
	import searchGps from '../components/search-gps'
	import { gpsSearch } from '@/api/wisdom-cloud-search'
	import { myMixins } from '../../components/mixin/index.js';
	import directionModel from '../components/direction-model'
	import { queryDataByKeyTypes } from '@/api/user.js'

	export default {
		mixins: [myMixins], //全局的mixin
		name: 'gpsContent',
		components: {
			searchGps,
			directionModel
		},
		props: {
			// 首页参数
			indexSearchData: {
				type: Object,
				default: () => { }
			}
		},
		data() {
			return {
				queryParam: {
					sortField: 'absTime',
					order: 'desc'
				},
				tableList: [],
				pageInfo: {
					pageNumber: 1,
					pageSize: 40,
				},
				total: 0,
				loading: false,
				timeUpDown: false,
				columns: [
					{ title: '编号', align: 'center', width: 90, type: 'index', key: 'index' },
					{ title: '设备编号', key: 'deviceId', },
					{ title: '所属类型', slot: 'objectType', },
					{ title: '经度', slot: 'lon', },
					{ title: '纬度', slot: 'lat', },
					// { title: '方向', key: 'direction', },
					{ title: '状态', slot: 'state', },
					{ title: '采集时间', key: 'absTime', },
					{ title: '操作', slot: 'action', width: 100 }

				],
				gpsTypeList: []
			}
		},
		activated() {
			if (this.$route.query.deviceInfo) {
				this.$nextTick(() => {
					let deviceInfo = JSON.parse(this.$route.query.deviceInfo)
					this.$refs.searchGps.selectData([{ ...deviceInfo, select: true }])
					this.queryParam.devices = [deviceInfo.deviceId];
					this.queryList()
				})
			} else {
				this.queryList()
			}
		},
		mounted() {
			this.getDictDataPageList()

		},
		methods: {
			//所属类型字典数据
			getDictDataPageList() {
				queryDataByKeyTypes(["ivcp_gps_type"]).then(res => {
					this.gpsTypeList = res.data[0].ivcp_gps_type
				})
			},
			//所属类型字典数据转换
			tranDictType(type) {
				let typeName = ''
				this.gpsTypeList.forEach(item => {
					if (item.dataKey == type) {
						typeName = item.dataValue
					}
				})
				return typeName
			},
			// 排序
			handleSort(val) {
				this.queryParam.sortField = val;
				this.timeUpDown = !this.timeUpDown
				this.queryParam.order = this.timeUpDown ? 'asc' : 'desc';
				this.queryList()
			},
			queryList() {
				let formData = this.$refs.searchGps.formData
				this.queryParam = { ...this.queryParam, ...formData }
				if (this.queryParam.timeSlot != '自定义') {
					this.dispTime()
				} else {
					this.queryParam.startDate = this.$dayjs(formData.perceiveDate[0]).format('YYYY-MM-DD HH:mm:ss')
					this.queryParam.endDate = this.$dayjs(formData.perceiveDate[1]).format('YYYY-MM-DD HH:mm:ss')
				}
				this.loading = true;
				this.tableList = []
				let params = {
					startDate: this.queryParam.startDate,
					endDate: this.queryParam.endDate,
					devices: this.queryParam.devices,
					gpsCode: this.queryParam.gpsCode,
					keyWords: this.indexSearchData.keyWords,
					order: this.queryParam.order,
				}
				gpsSearch({ ...params, ...this.pageInfo })
					.then(res => {
						const { total, entities } = res.data
						this.total = total
						this.tableList = entities
					})
					.catch(err => {
						console.error(err)
					})
					.finally(() => {
						this.loading = false;
					})
			},
			searchInfo(obj) {
				this.pageInfo.pageNumber = 1
				if (obj.selectDeviceList) {
					// 处理已选择设备
					var ids = []
					obj.selectDeviceList.forEach(item => {
						ids.push(item.deviceId)
					})
					obj.devices = ids
				}
				this.queryParam = obj
				this.queryList()
			},
			// 页数改变
			pageChange(size) {
				this.pageInfo.pageNumber = size
				this.queryList()
			},
			// 页数量改变
			pageSizeChange(size) {
				this.pageInfo.pageNumber = 1
				this.pageInfo.pageSize = size
				this.queryList()
			},
			openDirectModel(row) {
				this.$refs.directionModel.show(row)
			},

		}
	}
</script>
<style lang="less" scoped>
	@import "style/index";
	.mb {
		margin-bottom: 10px;
	}
</style>
