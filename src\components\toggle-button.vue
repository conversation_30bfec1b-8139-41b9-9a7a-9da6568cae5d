<template>
  <div class="toggle-button" @mousedown="toggleDragDown">
    ||
  </div>
</template>
<script>
export default {
  props: {
    boxWidth: {
      type: Number,
      default () {
        return 0
      }
    },
    defaultWidth: {
      type: Number,
      default () {
        return 316
      }
    },
    maxWidth: {
      type: Number,
      default () {
        return 600
      }
    }
  },
  data () {
    return {
      toggleDraging: false,
      currentLeftWidth: 0
    }
  },
  mounted() {
    document.addEventListener("mouseup", this.toggleDragUp)
    document.addEventListener("mousemove", this.toggleDragMove)
  },
  beforeDestroy() {
    document.removeEventListener("mouseup", this.toggleDragUp)
    document.removeEventListener("mousemove", this.toggleDragMove)
  },
  methods: {
    toggleDragDown(e) {
      this.mouseX = e.x
      this.currentLeftWidth = this.boxWidth
      this.toggleDraging = true
    },
    toggleDragUp() {
      this.toggleDraging = false
    },
    toggleDragMove(e) {
      if (this.toggleDraging) {
        let offset = e.x - this.mouseX
        let width = this.currentLeftWidth + offset < this.defaultWidth ? this.defaultWidth : this.currentLeftWidth + offset > this.maxWidth ? this.maxWidth : this.currentLeftWidth + offset
        this.$emit('update:boxWidth', width)
      }
    }
  }
}
</script>
<style lang="less" scoped>
.toggle-button {
  padding: 8px 1px;
  border-top: 1px solid #999999;
  border-bottom: 1px solid #999999;
  cursor: w-resize;
  color: #999999;
  user-select: none;
}
</style>
