<!--
    * @FileDescription: 套牌分析
    * @Date: 2024/04/07
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-20 11:25:41
 -->
<template>
  <div class="deck-analysis">
    <div class="left-search">
      <div class="title">
        <p>套牌分析</p>
      </div>
      <div class="form-box">
        <Form :inline="true" :label-width="75">
          <FormItem label="车牌号码:">
            <Input
              v-model="queryParam.plateNo"
              placeholder="请输入"
              style="width: 259px"
            ></Input>
          </FormItem>
          <FormItem label="开始时间:">
            <DatePicker
              v-model="queryParam.startTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              :clearable="false"
              placeholder="开始时间"
              transfer
              style="width: 259px"
            ></DatePicker>
          </FormItem>
          <FormItem label="结束时间:">
            <DatePicker
              v-model="queryParam.endTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              :clearable="false"
              placeholder="结束时间"
              transfer
              style="width: 259px"
            ></DatePicker>
          </FormItem>
          <FormItem label="处理状态:">
            <Select
              clearable
              v-model="queryParam.status"
              placeholder="请选择"
              style="width: 259px"
            >
              <Option
                :value="item.dataKey"
                :label="item.dataValue"
                v-for="(item, index) in fakeStatusList"
                :key="index"
              ></Option>
            </Select>
          </FormItem>
          <FormItem label="选择设备:">
            <div class="select-tag-button" @click="selectDevice()">
              选择设备/已选（{{ queryParam.selectDeviceList.length }}）
            </div>
          </FormItem>
        </Form>
        <div class="btn-group">
          <Button type="primary" style="width: 248px" @click="searchHandle"
            >查询</Button
          >
          <Button @click="resetHandle">重置</Button>
        </div>
      </div>
    </div>
    <div class="right-list">
      <div class="title" v-if="dataList.length !== 0">
        <p>查询结果</p>
      </div>
      <div class="box-content">
        <listCard
          v-for="(item, ind) in dataList"
          :key="ind"
          :item="item"
          @toDetail="toDetail(item)"
        ></listCard>
      </div>
      <div
        class="empty-card-1"
        v-for="(item, index) of 9 - (dataList.length % 9)"
        :key="index + 'demo'"
      ></div>
      <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
      <p class="tips" v-if="dataList.length == 0 && !loading">
        请在左侧操作后查询结果
      </p>
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[9, 27, 54, 108]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>

    <select-device
      ref="selectDevice"
      :showOrganization="true"
      @selectData="selectData"
    />

    <detail-modal
      ref="detailModal"
      @handleComplete="getDataList"
    ></detail-modal>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { fakeVehicleList, fakeVehicleDetail } from "@/api/modelMarket";
import listCard from "./components/list-card.vue";
import detailModal from "./components/detail-modal.vue";
export default {
  name: "deck-analysis",
  components: {
    listCard,
    detailModal,
  },
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      queryParam: {
        plateNo: "",
        startTime: "",
        endTime: "",
        status: "",
        selectDeviceList: [],
      },
      loading: false,
      dataList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 9,
      },
      total: 0,
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      fakeStatusList: "dictionary/getFakeStatusList",
    }),
  },
  async created() {
    await this.getDictData();
    // 推荐中心查看
    console.log("taskParams: ", this.taskParams);
    if (!Toolkits.isEmptyObject(this.taskParams)) {
      this.queryParam.selectDeviceList =
        this.taskParams.params.selectDeviceList || [];
      if (this.taskParams.queryStartTime)
        this.queryParam.startTime = this.taskParams.queryStartTime;
      if (this.taskParams.queryEndTime)
        this.queryParam.endTime = this.taskParams.queryEndTime;
      this.$nextTick(() => {
        if (this.taskParams.taskResult) this.searchHandle();
      });
    }
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 查询
    searchHandle() {
      this.pageInfo.pageNumber = 1;
      this.getDataList();
    },
    getDataList() {
      this.loading = true;
      let params = {
        plateNo: this.queryParam.plateNo,
        startTime: this.queryParam.startTime
          ? this.$dayjs(this.queryParam.startTime).format("YYYY-MM-DD HH:mm:ss")
          : this.queryParam.startTime,
        endTime: this.queryParam.endTime
          ? this.$dayjs(this.queryParam.endTime).format("YYYY-MM-DD HH:mm:ss")
          : this.queryParam.endTime,
        status: this.queryParam.status,
        deviceIds: this.queryParam.selectDeviceList.map((v) => v.deviceGbId),
        ...this.pageInfo,
      };
      fakeVehicleList(params).then((res) => {
        this.loading = false;
        this.dataList = res.data.entities || [];
        this.total = res.data.total;
      });
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.getDataList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.getDataList();
    },
    // 重置
    resetHandle() {
      this.queryParam = {
        plateNo: "",
        startTime: "",
        endTime: "",
        status: "",
        selectDeviceList: [],
      };
      this.dataList = [];
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList, "");
    },
    selectData(arr) {
      this.queryParam.selectDeviceList = arr;
      this.$forceUpdate();
    },
    toDetail(item) {
      fakeVehicleDetail(item.id).then((res) => {
        this.$refs.detailModal.show(res.data);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.deck-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  .left-search {
    width: 370px;
    height: inherit;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
    border-radius: 4px 4px 4px 4px;
    margin-right: 10px;
    .form-box {
      padding: 10px 5px;
      .btn-group {
        padding: 0 10px;
      }
    }
  }
  .right-list {
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
    border-radius: 4px 4px 4px 4px;
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    .box-content {
      padding: 20px;
      flex: 1;
      overflow-y: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: start;
      align-content: flex-start;
    }
    .pages {
      padding: 0 20px;
    }
    .data-export {
      margin: 10px 0 0 20px;
    }
  }
  /deep/.ivu-form-item {
    margin-bottom: 10px;
  }
  .title {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    height: 40px;
    position: relative;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #d3d7de;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 0;
    z-index: 1;
    background: #fff;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:before {
      content: "";
      position: absolute;
      width: 3px;
      height: 20px;
      top: 50%;
      transform: translateY(-50%);
      left: 10px;
      background: #2c86f8;
    }
    span {
      color: #2c86f8;
    }
    /deep/.ivu-icon-ios-close {
      font-size: 30px;
      cursor: pointer;
    }
  }
}
.empty-card-1 {
  width: 10.7%;
}
.tips {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, 50%);
  font-weight: 400;
  color: rgba(0, 0, 0, 0.45);
}
</style>
