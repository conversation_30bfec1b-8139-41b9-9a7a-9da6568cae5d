<template>
  <!-- 人脸车辆 年度人卡历史最高在线情况  -->
  <div class="face-device-clock-accuracy auto-fill">
    <div class="evaluation-header">
      <div class="filtrate">
        <span class="f-16 color-filter ml-sm"
          >{{ paramsList.orgRegionName }}-本年度{{ navbarTitle }}历史最高在线情况</span
        >
      </div>
      <div>
        <span class="evaluation-time">
          <i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：{{ paramsList.examineTime || '未知' }}
        </span>
      </div>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div slot="search" class="fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <TableList
        ref="infoList"
        class="infoList auto-fill"
        :columns="tableColumns"
        :loadData="loadDataList"
        listKey="data"
        :paging="false"
      >
      </TableList>
    </div>
  </div>
</template>

<style lang="less" scoped>
.face-device-clock-accuracy {
  position: relative;
  overflow-y: auto;
  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }

    .evaluation-time {
      color: #a9bed9;
      font-size: 14px;
      margin-left: 10px;

      .mr-x {
        margin-right: 3px;
      }
    }

    .active {
      background: rgba(2, 57, 96, 1);
    }
  }
  .information-main {
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .infoList {
      @{_deep} .left-div {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        .ui-table {
          display: flex;
          flex-direction: column;
          flex: 1;
          overflow: hidden;
        }
      }
    }
    @{_deep}.ivu-table-header {
      width: auto;

      &:first-child {
        tr {
          border-top: 1px solid var(--border-color) !important;
          border-left: 1px solid var(--border-color) !important;

          th {
            &:last-child {
              border-bottom: 1px solid var(--border-color) !important;
              // border-left: none !important;
            }
          }
        }
      }

      tr {
        border-top: 1px solid var(--border-color) !important;
        border-left: 1px solid var(--border-color) !important;

        th {
          border-bottom: 1px solid var(--border-color) !important;
          border-left: 1px solid var(--border-color) !important;

          span {
            color: #a9bed9;
          }

          div {
            color: #a9bed9;
          }
        }
      }
    }
    @{_deep} .ivu-table-fixed-header thead tr th {
      border: 1px solid var(--border-color) !important;
    }

    @{_deep}.ivu-table-tbody {
      table {
        border-collapse: 0 !important;
        border-spacing: 0;
      }
      tr {
        border-right: 1px solid var(--border-color) !important;
        td {
          border-left: 1px solid var(--border-color) !important;
          border-bottom: 1px solid var(--border-color) !important;
        }
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'face-car-top-online',
  data() {
    return {
      loading: false,
      tableData: [],
      exportLoading: false,
      paramsList: {},
      loadDataList: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getThirdModelData,
            Object.assign(parameter, {
              indexId: this.paramsList.indexId,
              batchId: this.paramsList.batchId,
              access: this.paramsList.access,
              displayType: this.paramsList.displayType,
              orgRegionCode: this.paramsList.orgRegionCode,
            }),
          )
          .then((res) => {
            return res;
          });
      },
      navbarTitle: '',
    };
  },
  computed: {
    tableColumns() {
      let { indexId } = this.$route.query;
      let title = '';
      if (indexId === 2016) {
        this.navbarTitle = title = '人卡';
      } else if (indexId === 3020) {
        this.navbarTitle = title = '车卡';
      }
      return [
        {
          title: `2022年度${this.navbarTitle}历史最高在线情况`,
          align: 'center',
          children: [
            {
              type: 'index',
              width: 70,
              title: '序号',
              fixed: 'left',
              align: 'center',
            },
            {
              title: '地区',
              align: 'center',
              key: 'orgRegionName',
              fixed: 'left',
              tooltip: true,
              minWidth: 120,
            },
            {
              title: '1月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'january',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '2月',
              align: 'center',
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'february',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '3月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'march',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '4月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'april',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '5月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'may',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '6月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'june',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '7月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'july',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '8月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'august',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '9月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'september',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '10月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'october',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '11月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'november',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '12月',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'december',
                  tooltip: true,
                  minWidth: 100,
                },
              ],
            },
            {
              title: '历史最高在线数',
              align: 'center',
              tooltip: true,
              minWidth: 100,
              fixed: 'right',
              children: [
                {
                  title: '在线数',
                  align: 'center',
                  key: 'maxOnline',
                  tooltip: true,
                  minWidth: 100,
                  fixed: 'right',
                },
              ],
            },
          ],
        },
      ];
    },
  },
  methods: {
    init() {
      this.$refs.infoList.info(true);
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            exportType: 'third', //value：first、second、third
          },
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  activated() {
    this.paramsList = this.$route.query;
    this.init();
  },
  components: {
    TableList: require('./components/tableList.vue').default,
  },
};
</script>
