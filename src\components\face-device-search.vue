<template>
  <div class="base-search">
    <!-- <Button type="primary" class="exportBtn mr-sm">
			<i class="icon-font icon-daochu f-12"></i>导出
		</Button> -->
    <ui-label class="fl" label="抓拍时间" :width="66">
      <div class="date-picker-box">
        <DatePicker
          class="input-width mb-md"
          type="datetime"
          v-model="searchData.startTime"
          placeholder="请选择开始时间"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="input-width"
          type="datetime"
          v-model="searchData.endTime"
          placeholder="请选择结束时间"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
        ></DatePicker>
      </div>
    </ui-label>
    <ui-label class="fl ml-lg" label="抓拍设备" :width="66">
      <select-camera class="mt4" @pushCamera="pushCamera" :device-ids="searchData.deviceIds"></select-camera>
    </ui-label>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, startSearch)">重置</Button>
    </ui-label>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      searchData: {
        deviceIds: [],
        startTime: '',
        endTime: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
  },
  watch: {},
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  margin: 15px 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    line-height: 40px;
  }
}
.mt4 {
  margin-top: 4px;
}
</style>
