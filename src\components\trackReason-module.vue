<template>
  <ui-modal v-model="visible" :title="title" width="600" :styles="style" :footerHide="true">
    <div class="title-n">车牌原始值：{{ this.msg.plateNo }}</div>
    <ui-table class="ui-table mt-lg auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #algorithmType="{ row }">
        <span>{{ row.algorithmType }}</span>
      </template>
    </ui-table>
    <!-- <div class="decision-box" v-if="tableData.length">
      <span>综合判定</span>
      <p>
        <span
          class="icon-font icon-zhiliangfen-line standard_icon"
          :class="isUnQuatify ? 'font-warning' : 'font-green'"
        >
          <i class="icon_text_error" :class="isUnQuatify ? 'font-warning' : 'font-green'">
            {{ isUnQuatify ? '不一致' : '一致' }}
          </i>
        </span>
      </p>
    </div> -->
  </ui-modal>
</template>

<script>
import algorithm from '@/config/api/algorithm.js';
export default {
  data() {
    return {
      visible: false,
      title: '轨迹确定性存疑原因',
      style: {
        // top: '30px',
      },
      tableData: [],
      tableColumns: [
        { title: '检测算法', key: 'algorithmType', slot: 'algorithmType' },
        // { title: '识别结果', key: 'algorithmType', slot: 'algorithmType' },
        { title: '判定结果', key: 'score' },
      ],
      loading: false,
      msg: {},
    };
  },
  methods: {
    info(item) {
      this.visible = true;
      this.msg = item;
      if (item.extraResult != null) {
        this.tableData = JSON.parse(item.extraResult);
        let data = {};
        data.typeKey = 'algorithmVendorType';
        this.$http.post(algorithm.dictDataList, data).then((res) => {
          let Msg = res.data.data.entities;
          for (let i of this.tableData) {
            for (let k of Msg) {
              if (i.algorithmType == k.dataKey) {
                i.algorithmType = k.dataValue;
              }
            }
          }
        });
      } else {
        this.tableData = [];
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
ul {
  li {
    padding: 10px 0;
    span {
      display: inline-block;
      width: 120px;
      height: 25px;
      line-height: 25px;
      margin-right: 10px;
    }
    span:nth-child(1) {
      width: 260px;
    }
  }
}
</style>
