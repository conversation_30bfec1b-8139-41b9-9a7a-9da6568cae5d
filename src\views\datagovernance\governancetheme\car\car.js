var car = [
  {
    icon1: 'zu16191',
    icon2: 'chenggong',
    icon3: 'chenggong',
    title: '数据输入',
    desc: '选择待治理数据',
    switch: false,
  },
  {
    icon1: 'cheliangshitushuju',
    icon2: 'chenggong',
    icon3: 'chenggong',
    title: '车辆结构化',
    desc: '选择多家算法对车辆图像进行结构化',
    switch: true,
  },
  [
    {
      icon1: 'tuxiangzhuapaishijianzhunquexingjiance',
      icon2: 'chenggong',
      icon3: 'chenggong',
      title: '图像抓拍时间准确性检测',
      desc: '检测图像抓拍时间是否准确',
      switch: true,
    },
    {
      icon1: 'tuxiangshangchuanjishixingjiance',
      icon2: 'chenggong',
      icon3: 'chenggong',
      title: '图像上传及时性检测',
      desc: '检测图像上传是否及时',
      switch: true,
    },
    {
      icon1: 'datuURLjiance',
      icon2: 'chenggong',
      icon3: 'chenggong',
      // title: '大图URL检测',
      title: '图像URL检测配置',
      desc: '小图对应的大图可以正常访问',
      switch: true,
    },
  ],
  [
    // {
    //     icon1: 'shujushuru',
    //     icon2: 'chenggong',
    //     icon3: 'chenggong',
    //     title: '大小图关联正确检测',
    //     desc: '检测小图是否出现在大图中',
    // },
    {
      icon1: 'shebeibianmageshijiance',
      icon2: 'chenggong',
      icon3: 'chenggong',
      title: '车牌识别准确性检测优化',
      desc: '多算法验证车辆识别是否准确',
      switch: true,
    },
    {
      icon1: 'cheliangshitushuju',
      icon2: 'chenggong',
      icon3: 'chenggong',
      title: '车辆结构化属性完整性与准确检性测优化',
      // title: '车辆结构化属性准确检性测优化',
      desc: '多算法交叉验证车辆属性是否结构化完整',
      switch: true,
    },
    // {
    //     icon1: 'shujushuru',
    //     icon2: 'chenggong',
    //     icon3: 'chenggong',
    //     title: '车辆结构化属性正确检测',
    //     desc: '多算法交叉验证车辆属性是否结构化完整',
    // },
    // {
    //     icon1: 'shujushuru',
    //     icon2: 'chenggong',
    //     icon3: 'chenggong',
    //     title: '大小图关联正确检测',
    //     desc: '多算法验证图片是否能提取对象特征',
    // },
  ],
  {
    icon1: 'zu1665',
    icon2: 'chenggong',
    icon3: 'chenggong',
    title: '数据输出',
    desc: '追踪查阅数据量检测结果',
    switch: false,
  },
  {
    icon1: 'shujushuru',
    icon2: 'chenggong',
    icon3: 'chenggong',
    title: '模糊图像识别处理',
    desc: '多算法验证图片是否能提取对象特征',
    switch: false,
  },
];

var carSelectList = [
  {
    value: '1',
    label: '宇视车辆结构化算法',
  },
  {
    value: '2',
    label: '商汤车辆结构化算法',
  },
  {
    value: '3',
    label: '海康车辆结构化算法',
  },
  {
    value: '4',
    label: '大华车辆结构化算法',
  },
  {
    value: '5',
    label: '格林深瞳车辆结构化算法',
  },
  {
    value: '6',
    label: '依图车辆结构化算法',
  },
  {
    value: '7',
    label: '云天励飞车辆结构化算法',
  },
];

var configList = [
  { title: '车辆品牌' },
  { title: '车辆品牌' },
  { title: '车辆品牌' },
  { title: '车辆品牌' },
  { title: '车辆品牌' },
  { title: '车辆品牌' },
];

export { car, carSelectList, configList };
