<template>
  <div class="thematic-database">
    <div class="container detail-info">
      <div class="info">
        <img :src="imgUrl" class="info-img" alt />
        <div class="info-content">
          <div class="name primary">{{ currentRow.libName }}</div>
          <div class="info-item">
            <span class="label title-color">更新时间：</span>
            <span class="value text-color">{{ currentRow.modifyTime }}</span>
          </div>
          <div class="info-item">
            <span class="label title-color">备注：</span>
            <span class="value text-color">{{ currentRow.remark }}</span>
          </div>
        </div>
      </div>
      <div class="statistics">
        <div class="label title-color">人员数量：</div>
        <count-to
          :start-val="0"
          :end-val="pageInfo.total || 0"
          :duration="1000"
          class="num color-warning"
        ></count-to>
      </div>
    </div>
    <div class="container content">
      <div class="search card-border-color">
        <Form
          ref="searchData"
          :model="searchData"
          inline
          :class="visible ? 'advanced-search-show' : ''"
        >
          <FormItem prop="idCardNo" label="身份证号:">
            <Input v-model="searchData.idCardNo" placeholder="请输入身份证号" />
          </FormItem>
          <FormItem prop="name" label="姓名:">
            <Input v-model="searchData.name" placeholder="请输入姓名" />
          </FormItem>
          <FormItem class="btn-group">
            <Button type="primary" @click="init()">查询</Button>
            <Button @click="resetHandle">重置</Button>
            <span
              class="advanced-search-text"
              @click.stop="advancedSearchHandle"
            >
              <img :src="advancedImg" alt />
              <span class="primary">{{
                visible
                  ? "更多条件"
                  : searchText === "高级检索"
                  ? "高级检索"
                  : "更多条件"
              }}</span>
            </span>
          </FormItem>
          <div class="advanced-search" v-if="visible">
            <!-- <div class="upload-input-list">
              <uiUploadImg ref="uploadImg" v-model="images" size="small" :algorithmType="1" @imgUrlChange="imgUrlChange" />
            </div> -->
            <div class="other-search">
              <FormItem label="证件类型:" prop="idCardNoType">
                <Select
                  v-model="searchData.idCardNoType"
                  placeholder="请选择证件类型"
                  class="input-200"
                  transfer
                  filterable
                >
                  <Option
                    v-for="(item, $index) in identityTypeList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem label="民族:" prop="national">
                <Select
                  v-model="searchData.national"
                  placeholder="请选择民族"
                  class="input-200"
                  transfer
                  filterable
                >
                  <Option
                    v-for="(item, $index) in nationTypeList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem label="籍贯:" prop="nativePlace">
                <Input
                  placeholder="请输入"
                  v-model="searchData.nativePlace"
                  maxlength="50"
                />
              </FormItem>
            </div>
            <!-- 选择标签 -->
            <!-- <LabelModal @setCheckedLabel="setCheckedLabel" ref="labelModal" /> -->
          </div>
        </Form>

        <div class="btn-group">
          <Button
            @click="addPersonnelHandle"
            v-permission="['staticLibr-personal-add']"
            ><i class="iconfont icon-jia"></i>新增</Button
          >
          <Button
            @click="personnelDelHandle('')"
            v-permission="['staticLibr-personal-dele']"
            ><i class="iconfont icon-shanchu"></i>删除</Button
          >
          <Button
            @click="importFn"
            v-permission="['staticLibr-personal-import']"
            ><i class="iconfont icon-Import"></i>导入</Button
          >
          <Button
            @click="exportFn"
            v-permission="['staticLibr-personal-export']"
            ><i class="iconfont icon-export"></i>导出</Button
          >
          <UploadFile
            style="display: none"
            :deployType="true"
            :faceLibId="Number($route.query.id)"
            @successPut="fileUploadSuccess"
            ref="uploadFile"
          />
        </div>
      </div>
      <div class="personnel-table">
        <ui-table
          :columns="columns"
          :data="tableData"
          :loading="loading"
          @on-selection-change="selectionChangeHandle"
        >
          <template #photoUrlList="{ row }">
            <img
              v-viewer
              :src="row.photoUrlList[0].photoUrl"
              class="personnel-photo"
              alt
            />
          </template>
          <template #idCardNoType="{ row }">
            <div>
              {{ row.idCardNoType | commonFiltering(identityTypeList) }}
            </div>
          </template>
          <template #national="{ row }">
            <div>{{ row.national | commonFiltering(nationTypeList) }}</div>
          </template>
          <template #bizLabelVos="{ row }">
            <div>
              <LabelList :labelList="row.bizLabelVos"></LabelList>
            </div>
          </template>
          <template #action="{ row }">
            <div class="btn-tips">
              <!-- <ui-btn-tip content="档案" icon="icon-dangan2" class="primary" @click.native="archivesHandle(row)"/> -->
              <ui-btn-tip
                content="详情"
                icon="icon-xiangqing"
                class="primary"
                @click.native="personnelDetailHandle(row)"
              />
              <ui-btn-tip
                content="编辑"
                icon="icon-bianji"
                class="primary"
                @click.native="personnelEditHandle(row)"
                v-permission="['staticLibr-personal-update']"
              />
              <ui-btn-tip
                content="删除"
                icon="icon-shanchu"
                class="primary"
                @click.native="personnelDelHandle(row)"
                v-permission="['staticLibr-personal-dele']"
              />
            </div>
          </template>
        </ui-table>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="pageInfo.total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <!-- 新增/编辑人员 -->
    <AddModal
      ref="addModal"
      :identityTypeList="identityTypeList"
      :nationTypeList="nationTypeList"
      :currentRow="currentRow"
    />
    <!-- 人员详情 -->
    <DetailModal
      ref="detailModal"
      :nationTypeList="nationTypeList"
      :identityTypeList="identityTypeList"
      :currentRow="currentRow"
    />
  </div>
</template>
<script>
import {
  queryDeviceInfoPageList,
  addFaceLibPersonInfo,
  motifyFaceLibPersonInfo,
  deleteFaceLibPersonInfo,
  batchDeleteFaceLibPerson,
  doExportZipPersonFiles,
} from "@/api/dataGovernance";
import { mapActions, mapGetters } from "vuex";
import CountTo from "vue-count-to";
import AddModal from "./components/add-modal.vue";
import DetailModal from "./components/detail-modal.vue";
import UploadFile from "@/components/ui-upload-file";
import LabelList from "@/views/juvenile-data-storage/special-library/personnel-thematic-database/components/label-list.vue";

export default {
  components: {
    CountTo,
    AddModal,
    UploadFile,
    DetailModal,
    LabelList,
  },
  data() {
    return {
      visible: false,
      currentRow: {},
      searchData: {
        idNumber: "",
        idCardNoType: "",
        national: "",
        name: "",
      },
      advancedImg: require("@/assets/img/down-circle-icon.png"),
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      loading: false,
      searchText: "高级检索",
      imgUrl: require("@/assets/img/default-img/people_archives_default.png"),
      columns: [
        { type: "selection", width: 60, align: "center" },
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "人员照片", slot: "photoUrlList" },
        { title: "姓名", key: "name" },
        { title: "证件类型", slot: "idCardNoType" },
        { title: "证件号码", key: "idCardNo" },
        { title: "民族", slot: "national" },
        { title: "标签", slot: "bizLabelVos", width: 200 },
        { title: "籍贯", key: "nativePlace" },
        { title: "档案更新时间", key: "qsdiUpdateTime" },
        { title: "操作", slot: "action", width: 174 },
      ],
      tableData: [],
      total: 3,
      selectionList: [],
    };
  },
  computed: {
    ...mapGetters({
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      identityTypeList: "dictionary/getIdentityTypeList", //证件类型
    }),
  },
  async activated() {},
  async mounted() {
    await this.getDictData();
    this.currentRow = this.$route.query;
    this.currentRow.libCount = parseInt(this.$route.query.libCount);
    this.$nextTick(() => {
      this.init();
    });
    // await this.getDictData()
    // this.init()
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    init() {
      this.tableData = [];
      this.visible = false;
      this.loading = true;
      var param = {
        ...this.searchData,
        ...this.pageInfo,
        faceLibId: this.currentRow.id,
      };
      queryDeviceInfoPageList(param)
        .then((res) => {
          this.tableData = res.data.entities;
          this.pageInfo.total = res.data.total;
        })
        .catch()
        .finally(() => {
          this.loading = false;
        });
    },
    // 重置
    resetHandle() {
      this.$refs.searchData.resetFields();
      this.searchData.idCardNoType = "";
      this.searchData.national = "";
      this.searchData.nativePlace = "";
      this.pageInfo.pageNumber = 1;
      this.init();
    },
    // 新增人员
    addPersonnelHandle() {
      this.$refs.addModal.show(false);
    },
    // 档案
    archivesHandle(item) {
      const { href } = this.$router.resolve({
        name: "people-archive",
        query: {
          archiveNo: item.idCardNo,
          source: "people",
          initialArchiveNo: item.idCardNo,
        },
      });
      window.open(href, "_blank");
    },
    // 详情
    personnelDetailHandle(item) {
      this.$refs.detailModal.show(item);
    },
    // 编辑
    personnelEditHandle(item) {
      this.$refs.addModal.show(true, item);
    },
    // 表格选中框事件
    selectionChangeHandle(list) {
      this.selectionList = list;
    },
    // 删除
    personnelDelHandle(item) {
      if (item) {
        // 单个删除
        this.$Modal.confirm({
          title: "提示",
          width: 450,
          closable: true,
          content: `确定删除该人员信息？`,
          onOk: () => {
            deleteFaceLibPersonInfo(item.id).then((res) => {
              this.init();
            });
          },
        });
      } else {
        // 批量删除
        if (!this.selectionList.length) {
          this.$Message.warning("请选择要删除的人员信息");
        } else {
          this.$Modal.confirm({
            title: "提示",
            width: 450,
            closable: true,
            content: `确定批量删除人员信息？`,
            onOk: () => {
              let idlist = this.selectionList.map((item) => item.id);
              batchDeleteFaceLibPerson(idlist).then((res) => {
                this.$Message.success("删除成功");
                this.selectionList = [];
                this.pageInfo.pageNumber = 1;
                this.init();
              });
            },
          });
        }
      }
    },
    // 高级搜索切换
    advancedSearchHandle() {
      if (this.visible) {
        this.visible = false;
      } else {
        this.visible = true;
      }
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    importFn() {
      this.$refs.uploadFile.clickFn();
    },
    exportFn() {
      if (this.pageInfo.total == 0) return;
      if (!this.selectionList.length) {
        this.$Message.warning("请选择数据");
        return;
      }
      var ids = [];
      this.selectionList.forEach((item, index) => {
        ids.push(item.id);
      });
      var obj = {
        faceLibId: this.$route.query.id,
        ...this.searchData,
        ids,
      };
      var param = {
        taskName: 2,
        taskType: 2,
        condition: JSON.stringify(obj),
      };

      doExportZipPersonFiles(param).then((res) => {
        let aLink = document.createElement("a");
        aLink.href = res.data;
        aLink.click();
      });
    },
    fileUploadSuccess(res, data) {
      if (data && data != "") {
        this.$Message.success("存在上传失败文件，已下载！");
        let aLink = document.createElement("a");
        aLink.href = data;
        aLink.click();
      } else {
        this.$Message.success("文件上传成功");
      }
      this.init();
    },
  },
};
</script>
<style lang="less" scoped>
.thematic-database {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .detail-info {
    height: 120px;
    padding: 10px;
    flex: none;
    display: flex;
    margin-bottom: 10px;
    .info {
      display: flex;
      flex: 1;
      .info-img {
        width: 100px;
        height: 100px;
        object-fit: contain;
      }
      .info-content {
        padding-left: 20px;
        box-sizing: border-box;
        .name {
          font-size: 24px;
          font-family: "MicrosoftYaHei-Bold";
          font-weight: bold;
          line-height: 26px;
          margin-bottom: 4px;
          display: inline-block;
        }
        .info-item {
          margin-top: 10px;
          .label {
            font-size: 12px;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            line-height: 20px;
            white-space: nowrap;
            display: inline-block;
          }
          .value {
            font-size: 14px;
            line-height: 20px;
            display: inline-block;
          }
        }
      }
    }
    .statistics {
      padding: 0 10px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .label {
        font-size: 12px;
        font-family: "MicrosoftYaHei-Bold";
        font-weight: bold;
        line-height: 18px;
        white-space: nowrap;
      }
      .num {
        font-family: "MicrosoftYaHei-Bold";
        font-size: 30px;
        font-weight: bold;
        line-height: 30px;
        margin-top: 8px;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    .personnel-table {
      display: flex;
      flex: 1;
      .personnel-photo {
        width: 64px;
        height: 64px;
        object-fit: contain;
        cursor: pointer;
      }
    }
  }
}

.advanced-search-show {
  .advanced-search {
    max-height: 400px;
    transition: max-height 0.5s;
  }
  .advanced-search-text {
    /deep/img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}

.search {
  position: relative;
}
.advanced-search-text {
  display: inline-block;
  margin-left: 10px;
  cursor: pointer;
  img {
    width: 16px;
    margin-top: 7px;
    float: left;
    margin-right: 3px;
    transform: rotate(180deg);
  }
}

.advanced-search {
  display: flex;
  align-items: center;
  position: absolute;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  margin-top: 1px;
  z-index: 10;
  max-height: 0px;
  transition: max-height 0.3s;
  overflow: hidden;
  .upload-input-list {
    display: flex;
    /deep/.ivu-upload {
      margin-right: 10px;
    }
  }
  .other-search {
    display: flex;
    flex: 1;
    padding: 10px 0 0 10px;
    box-sizing: border-box;
    flex-direction: initial;
    .other-search-top {
      display: flex;
      border-bottom: 1px dashed #fff;
    }
    .ivu-form-item {
      margin-bottom: 10px;
    }
    .other-search-bottom {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding-top: 10px;
      box-sizing: border-box;
      .slider-content {
        height: 34px;
      }
    }
  }
}
</style>
