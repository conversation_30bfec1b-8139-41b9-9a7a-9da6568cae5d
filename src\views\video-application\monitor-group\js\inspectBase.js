/**
 * 轮巡类, 用于处理各种轮巡遍历逻辑
 * <AUTHOR> @param config
 * @constructor
 */
import * as PortraitTool from '@/util/modules/common.js'

function Inspect(config) {
	//内部声明=====
	this.set = {
		batchModel: true // 分组批处理模式: 如果外部操作有涉及到http请求的, 建议使用批处理模式, 轮巡的一批一起处理发请求, 而不是分开一个个发请求
		, slots: [] //插槽数组
		, operators: [] //插子
		, inspectGroupId:''
		, planName:''
		, operate: {
			exe: function() {},     //执行回调函数
			start: function() {},   //启动...
			stop: function() {},    //停止...
			pause: function() {},   //暂停...
			continue: function() {} //停止后继续...
		}
		, time: 10 //间隔时间
		, showTip: true // 轮巡操作提示
	};
	this.isLock = false;
	this.timer = null;
	this.osIndex = 0; //当前遍历到的执行者(起始)位置
	//======内部声明

	//初始换轮巡参数
	PortraitTool.extend(this.set, config);
	this.set.time *= 1000;
	/**
	 * 设置轮巡参数, 一般用于启动后的轮巡, 中途修改参数
	 * @param config
	 */
	this.config = function(config) {
		PortraitTool.extend(this.set, config);
		this.set.time *= 1000;
	};
	this.inspectingOperas = [];
	/**
	 * 启动轮巡
	 */
	this.start = function() {
		this._clearTimer();
		this._loopExe();
		this.set.operate.start && this.set.operate.start();
	};
	/**
	 * 暂停轮巡
	 */
	this.pause = function() {
		this._clearTimer();
		this.set.operate.pause && this.set.operate.pause();
	};
	/**
	 * 暂停后继续轮巡
	 */
	this.continue = function() {
		//this.pause();
		this._loopExe();
		this.set.operate.continue && this.set.operate.continue();
	};
	/**
	 * 停止轮巡
	 */
	this.stop = function() {
		this._clearTimer();
		this.osIndex = 0;
		this.destory();
	};
	/**
	 * 强制销毁轮巡
	 */
	this.destory = function() {
		this._clearTimer();
        this.set.operate && this.set.operate.stop && this.set.operate.stop(this.inspectingOperas, this.set.slots);
		this.set = {};
	};

	//内部函数: 轮巡执行定时器机制
	this._loopExe = function() {
		var exeCount = this.set.slots.length, //单次处理个数
			opers = this._getLoopOpers(this.osIndex, exeCount);
		this._exe(opers, exeCount, this.set.inspectGroupId, this.set.planName);
        
		//插槽不少于叉子 不用定时 保持即可 以减少请求  isOneLoop 单个设备是否匀速循环
		if(this.set.operators.length <= this.set.slots.length && !this.set.isOneLoop) { return; }
		this.osIndex = (this.osIndex + exeCount) % this.set.operators.length;
        if(IX.isFn(this.set.globalCallBack)){
            this.set.globalCallBack({current:opers,next:this._getLoopOpers(this.osIndex,exeCount)});
        }
		if(this.timer) this.timer = null;
		this.timer = window.setInterval(function() {
			var inspectCount =  this.set.slots.length;
			if(inspectCount >= this.set.operators.length && !this.set.isOneLoop) {
				this.osIndex = 0;
				opers = this._getLoopOpers(this.osIndex, inspectCount);
				this._exe(opers, inspectCount, this.set.inspectGroupId, this.set.planName);
				this._clearTimer();
				this.isLock = true;
				return;
			}
			opers = this._getLoopOpers(this.osIndex, inspectCount);
			this._exe(opers, inspectCount, this.set.inspectGroupId, this.set.planName);
			this.osIndex = (this.osIndex + inspectCount) % this.set.operators.length;
            if(IX.isFn(this.set.globalCallBack)){
                 this.set.globalCallBack({current:opers,next:this._getLoopOpers(this.osIndex,inspectCount)});
            }
		}.bind(this), this.set.time);
	};
	//内部函数: 单个轮巡动作(处理回调)
	this._exe = function(opers, exeCount, inspectId, planName) {
		console.log("---轮巡....输出项", opers);
		if(this.set.batchModel) { //批处理模式,参数为数组
			this.set.operate.exe(opers, this.set.slots, inspectId, planName);
		} else {
			for(var i  = 0; i < exeCount; i++) { //非批处理模式,参数为单项
				this.set.operate.exe(opers[i], this.set.slots[i], inspectId, planName);
			}
		}
	};
	//内部函数: 清除定时器
	this._clearTimer = function() {
		if(this.timer) window.clearInterval(this.timer);
	};
	//内部函数: 当前处理批数据获取
	this._getLoopOpers = function(index, length) {
		length = this.set.slots.length; //length 参数废弃，内部重新获取，兼容轮询过程更改配置数据slots的
		var lengthAll = this.set.operators.length,
			endIndex = index + length,
			firstCut = this.set.operators.slice(index, endIndex);
		if(lengthAll <= this.set.slots.length) { //如果插子很少, 插槽很多
			this.inspectingOperas = firstCut;
			return firstCut;
		}
		if(endIndex <= lengthAll) { //正常截取
			this.inspectingOperas = firstCut;
			return firstCut;
		} else { //截取到末尾 再从头开始补数据
			var fixLength = length - firstCut.length,
				secondCut =  this.set.operators.slice(0, 0 + fixLength);
			this.inspectingOperas = firstCut.concat(secondCut);
			return firstCut.concat(secondCut);
		}
	};
	this.screenLock = function(val, cameraId) {
		this.set.slots = val;
		let index = this.set.operators.indexOf(cameraId);
		if(index > -1) {
			this.set.operators.splice(index, 1);
			if(this.osIndex > 0) {
				this.osIndex--;
			} else {
				this.osIndex = this.set.operators.length;
			}
		} else {
			let initInspectCameras = this.set.initInspectCameras;
			let operators = this.set.operators;
			operators.push(cameraId);
			this.set.operators = operators.sort((operator1, operator2) => {
				let index1 = initInspectCameras.indexOf(operator1);
				let index2 = initInspectCameras.indexOf(operator2);
				return index1 -  index2 ;
			})
		}
		if(this.isLock && this.set.slots.length < this.set.operators.length) {
			this._loopExe();
			this.isLock = false
		}
	}
}

//simple demo
/*var inspect = new Inspect({
	slots: ['a','b','c','d'],
	operators: [1,2,3,4,5,6,7,8,9,10,11,12,13],
	operate: {
		exe: function(o, s) {
			console.warn("---o: ", o);
			console.warn("---s: ", s);
		}
	},
	time: 5
});
inspect.start();*/
export default {
	construct: Inspect
}
