import ViewRestExpandRow from '../components/ViewRestExpandRow.vue';
export const tableColumns = (isExpandRow = false) => {
  return [
    { type: 'index', width: 50, title: '序号', align: 'center', isShow: !isExpandRow },
    {
      type: 'expand',
      width: 40,
      isShow: !isExpandRow,
      render: (h, params) => {
        return h(ViewRestExpandRow, {
          props: {
            rowGpu: params.row.gpu,
          },
        });
      },
    },
    { title: '服务IP', key: 'ip', minWidth: 100, tooltip: true, isShow: true },
    { title: '服务器名称', key: 'serverName', minWidth: 100, tooltip: true, isShow: true },
    { title: '操作系统', slot: 'system', minWidth: 120, tooltip: true, isShow: true },
    { title: '检测时间', key: 'lastNormalTime', minWidth: 150, isShow: true },
    { title: 'CPU', slot: 'cpu', width: 160, isShow: true },
    { title: '内存', slot: 'memory', width: 160, isShow: true },
    { title: '硬盘', slot: 'disc', width: 160, isShow: true },
    {
      title: '网络',
      key: 'option',
      minWidth: 100,
      isShow: true,
      render: (h, column) => {
        const row = column.row;
        let netSpeed = '';
        let unit = '';
        if (row.net) {
          // 判断网速大小 更改单位
          if (row.net.netSpeed / 1024 > 1) {
            if (row.net.netSpeed / 1024 / 1024 > 1) {
              netSpeed = (row.net.netSpeed / 1024 / 1024).toFixed(2);
              unit = 'GB/s';
            } else {
              netSpeed = (row.net.netSpeed / 1024).toFixed(2);
              unit = 'MB/s';
            }
          } else {
            netSpeed = row.net.netSpeed;
            unit = 'KB/s';
          }
          return h('div', netSpeed + unit);
        } else {
          return h('div', '');
        }
      },
    },
    { title: 'GPU', slot: 'GPU', width: 230, isShow: true },
    { title: '状态', slot: 'status', minWidth: 70, isShow: true },
    {
      title: '操作',
      slot: 'action',
      width: 60,
      fixed: 'right',
      align: 'center',
      className: 'table-action-padding',
      isShow: !isExpandRow,
    },
  ];
};
