<template>
  <div class="general-config-box">
    <div class="config-item">
      <div class="config-item-title">
        <span>数据备份</span>
        <Button type="primary" @click="backupSave">保存</Button>
      </div>
      <Form class="form-box backup-form" :label-width="labelWidth" :model="dataBackup" ref="formValidate">
        <FormItem label="设备资产库自动备份">
          <div class="config-content">
            <div class="width-full t-right">
              <i-switch v-model="dataBackup.isAutoBackup" @on-change="changeAutoBackup" />
            </div>
          </div>
        </FormItem>
        <FormItem
          label="备份计划"
          prop="cronData"
          :rules="[
            {
              required: dataBackup.isAutoBackup,
              message: '请选择备份计划',
              trigger: 'change',
              type: 'array',
            },
          ]"
        >
          <div class="config-content">
            <div class="width-full">
              <TestPlan :form-data="dataBackup" :is-view="!dataBackup.isAutoBackup" @checkTime="checkTime"></TestPlan>
            </div>
          </div>
        </FormItem>
        <FormItem
          label="最大保留数据"
          prop="maxRetainNum"
          :rules="[
            {
              required: dataBackup.isAutoBackup,
              message: '请输入最大保留数据',
              trigger: 'change',
              type: 'number',
            },
          ]"
        >
          <div class="config-content">
            <div class="width-full">
              <InputNumber
                v-model.number="dataBackup.maxRetainNum"
                class="width-input ml-xs mr-xs"
                :min="0"
                :disabled="!dataBackup.isAutoBackup"
                placeholder="请输入"
              ></InputNumber>
              <span>份</span>
            </div>
          </div>
        </FormItem>
      </Form>
    </div>

    <div class="config-item">
      <div class="config-item-title">
        <span>专题考核</span>
        <Button type="primary" @click="specialSave">保存</Button>
      </div>
      <Form class="form-box" :label-width="labelWidth" :model="dataBackup">
        <FormItem label="显示全量数据评测任务">
          <div class="config-content">
            <div class="width-full t-right">
              <i-switch v-model="specialAssessment.isfullDose" />
            </div>
          </div>
        </FormItem>
        <FormItem label="显示上报数据评测任务">
          <div class="config-content">
            <div class="width-full t-right">
              <i-switch v-model="specialAssessment.isReport" />
            </div>
          </div>
        </FormItem>
        <FormItem label="显示其他评测任务">
          <div class="config-content">
            <div class="width-full t-right">
              <i-switch v-model="specialAssessment.isOther" />
            </div>
          </div>
        </FormItem>
      </Form>
    </div>

    <div class="config-item">
      <div class="config-item-title">
        <span>检测结果</span>
        <Button type="primary" @click="detectionResultSave">保存</Button>
      </div>
      <Form class="form-box" :label-width="labelWidth">
        <FormItem label="检测结果导出，需显示人工复核标识" class="form-item-label">
          <div class="config-content">
            <div class="width-full t-right">
              <i-switch v-model="detectionResult.isMode" true-value="1" false-value="0" />
            </div>
          </div>
        </FormItem>
        <FormItem label="检测结果发布" class="form-item-label">
          <div class="config-content">
            <div class="width-full">
              <RadioGroup v-model="detectionResult.showResult">
                <Radio :label="1" class="mr-lg">始终发布</Radio>
                <Radio :label="2" class="mr-lg">检测（复检）完成后发布</Radio>
                <Radio :label="0" class="mr-lg">手动发布</Radio>
              </RadioGroup>
            </div>
          </div>
        </FormItem>
      </Form>
    </div>

    <div class="config-item">
      <div class="config-item-title">
        <span>数据监测</span>
        <Button type="primary" @click="dataMonitorSave">保存</Button>
      </div>
      <Form class="form-box" :label-width="labelWidth" :model="dataMonitor">
        <FormItem label="视图库离线">
          <div class="config-content">
            <div class="width-full flex-aic">
              <span>超过</span>
              <InputNumber
                class="ml-sm mr-sm"
                :min="1"
                :step="1"
                :precision="0"
                @on-blur="onBlurOfflineTime"
                v-model="dataMonitor.offlineTime"
              ></InputNumber>
              <Select v-model="dataMonitor.offlineUnit" class="width-mini mr-sm">
                <Option value="hour" key="hour">时</Option>
                <Option value="minute" key="minute">分</Option>
              </Select>
              <span>未接收到抓拍数据！</span>
            </div>
          </div>
        </FormItem>
      </Form>
    </div>
    <div class="config-item">
      <div class="config-item-title">
        <span>治理工单</span>
        <div class='options-bar'>
          <i class="link-text-box f-14 mr-xs" @click="handleClickAutoAssignOrg">配置各单位工单接收人</i>
          <Tooltip transfer max-width="300" class='mr-sm'>
            <i class="icon-font icon-wenhao vt-middle icon-warning"></i>
            <template #content>
              备注：每个单位选择一个工单接收人，系统设备的所属单位自动将工单指派给工单接收人！
            </template>
          </Tooltip>
          <Button @click="handleAddWorkOrder" class="mr-sm">
            <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"></i>
            <span class="vt-middle">新增</span>
          </Button>
          <Button type="primary" @click="handleSaveWorkOrder" :loading='saveWorkOrderLoading'>保存</Button>
        </div>
      </div>
      <Form :label-width="190" :model="dataMonitor">
        <FormItem label="">
          <ui-table
            class="ui-table mt-lg"
            :table-columns="workOrderConfigTableColumns"
            :table-data="workOrderConfigTableData"
            :loading="workOrderLoading"
            :row-key='false'
            :max-height="600"
          >
            <template #option="{ index }">
              <ui-btn-tip icon="icon-shanchu3" content="删除" @click.native="deleteWorkOrder(index)"></ui-btn-tip>
            </template>
            <template #autoCreate="{ index }">
              <i-switch
                v-model="workOrderConfigTableData[index]['autoCreate']"
                :true-value="AUTO_CREATE"
                :false-value="UN_AUTO_CREATE"
                @on-change="onChangeAutoCreate($event, index)"
              />
            </template>
            <template #autoAssign="{ row, index }">
              <i-switch
                v-model="workOrderConfigTableData[index]['autoAssign']"
                :true-value="AUTO_ASSIGN"
                :false-value="UN_AUTO_ASSIGN"
                :disabled='!row.autoCreate'
                class="mr-sm"
                @on-change="onChangeAutoAssign($event, index)"
              />
              <Select
                class="width-sm"
                v-model="workOrderConfigTableData[index]['assignMode']"
                transfer
                placeholder="请选择指派规则"
                v-if="row.autoAssign === AUTO_ASSIGN"
              >
                <Option :label="item.label" :value="item.value" v-for="(item, index) in ASSIGN_MODE_LIST" :key="index">
                </Option>
              </Select>
            </template>
            <template #workLevel="{ row, index }">
              <Select v-model="workOrderConfigTableData[index]['workLevel']" transfer placeholder="请选择紧急程度" class="width-mini mr-sm">
                <Option
                  :label="item.label"
                  :value="item.value"
                  v-for="(item, index) in workLevelOptions"
                  :key="`${item.value}-${index}`"
                >
                  <div class="inline work-level-box mr-sm vt-middle" :class="item.style"></div>
                  <span>{{ item.label }}</span>
                </Option>
              </Select>
              <div class="inline work-level-box mr-sm vt-middle" :class="`${workLevelMap[row.workLevel]}`"></div>
            </template>
            <template #plannedDay="{ index }">
              <Input v-model.number="workOrderConfigTableData[index]['plannedDay']" placeholder="请输入默认处理时长" class='width-mini mr-sm'></Input>
              <span>天</span>
            </template>
          </ui-table>
        </FormItem>
      </Form>
    </div>
    <edit-work-order
      v-model="workOrderVisible"
      :data="workOrderConfigTableData"
      @on-submit="handleSubmit"
    ></edit-work-order>

    <select-receiver
      ref="selectpeopleConfig"
      v-model="selectPeopleConfigVisible"
    >
    </select-receiver>
  </div>
</template>
<script>
import home from '@/config/api/home';
import governancetask from '@/config/api/governancetask';
import {
  workLevelOptions,
  ASSIGN_MODE_LIST,
  AUTO_ASSIGN_UNIT,
  AUTO_ASSIGN_ORG,
  AUTO_ASSIGN,
  UN_AUTO_ASSIGN,
  UN_AUTO_CREATE,
  AUTO_CREATE,
} from '@/views/disposalfeedback/governanceorder/util/enum.js';

export default {
  components: {
    TestPlan: require('./test-plan.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    selectReceiver: require('@/views/disposalfeedback/governanceorder/components/select-receiver.vue').default,
    EditWorkOrder: require('@/views/systemconfiguration/dockingservice/generalconfig/edit-work-order.vue').default,
  },
  data() {
    return {
      labelWidth: 170,
      // 数据备份
      dataBackup: {
        isAutoBackup: false,
        cronType: '1',
        timePoints: [],
        cronData: [],
        maxRetainNum: null,
        tables: ['t_device_info'],
        xxlJobId: '',
      },
      // 状态考核
      specialAssessment: {
        isfullDose: false,
        isReport: false,
        isOther: true,
      },
      // 检测结果
      detectionResult: {
        isMode: '0',
        showResult: 1,
      },
      backupKey: 'GENERAL_BACKUP_DATA',
      specialKey: 'GENERAL_SPECIAL_EXAM',
      detectionKey: 'EXPORT_RESULT_MODE',
      dataMonitorKey: 'GENERAL_DATA_MONITORING', //数据监测
      dataMonitor: {
        offlineUnit: 'hour',
        offlineTime: 2,
      },
      workOrderConfigTableColumns: [
        {
          title: '任务名称',
          key: 'taskName',
          tooltip: true,
          align: 'left',
          minWidth: 150,
        },
        {
          title: '指标名称',
          key: 'indexName',
          tooltip: true,
          align: 'left',
          minWidth: 200,
        },
        {
          title: '自动生成',
          slot: 'autoCreate',
          tooltip: true,
          align: 'left',
          minWidth: 100,
          renderHeader: (h, { column }) => {
            return (
              <Tooltip transfer max-width="300">
                <span class='mr-xs'>{column.title}</span>
                <i class="icon-font icon-wenhao vt-middle icon-warning"></i>
                <template slot="content">
                  检测结果已发布且复检（如有）结束后，系统将检测不合格的设备自动生成工单下发处理。
                  如需手动复检或复核才能确定最终结果，请关闭自动下发功能！
                </template>
              </Tooltip>
            );
          },
        },
        {
          title: '自动指派',
          slot: 'autoAssign',
          tooltip: true,
          align: 'left',
          minWidth: 230,
        },
        {
          title: '默认紧急程度',
          slot: 'workLevel',
          tooltip: true,
          align: 'left',
          minWidth: 130,
        },
        {
          title: '默认处理时长',
          slot: 'plannedDay',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          tooltip: true,
          width: 60,
        },
      ],
      workOrderConfigTableData: [],
      loading: false,
      workOrderVisible: false,
      workOrderLoading: false,
      saveWorkOrderLoading: false,
      selectPeopleConfigVisible: false,
      workLevelOptions,
      //转换一下 style
      workLevelMap: {
        1: 'one-leve',
        2: 'two-leve',
        3: 'three-leve',
        4: 'four-leve',
      },
      ASSIGN_MODE_LIST,
      AUTO_ASSIGN_ORG,
      AUTO_ASSIGN,
      UN_AUTO_ASSIGN,
      AUTO_CREATE,
      UN_AUTO_CREATE,
    };
  },
  created() {
    this.getDataBackupConfig();
    this.getSpecialConfig();
    this.getDetectionConfig();
    this.getDataMonitorConfig();
    this.getWorkOrderConfig();
  },
  methods: {
    handleClickAutoAssignOrg() {
      this.selectPeopleConfigVisible = true;
    },
    onChangeAutoCreate(val, index) {
      this.$set(this.workOrderConfigTableData[index], 'autoAssign', val);
    },
    onChangeAutoAssign(val, index) {
      this.$set(this.workOrderConfigTableData[index], 'assignMode', AUTO_ASSIGN_UNIT);
    },
    handleSubmit(val) {
      this.workOrderConfigTableData = JSON.parse(JSON.stringify(val)).map((item) => {
        const {
          id,
          taskSchemeId,
          taskName,
          indexId,
          indexName,
          autoCreate,
          autoAssign,
          assignMode,
          workLevel,
          plannedDay,
        } = item;
        return {
          id: id || '',
          taskSchemeId,
          taskName,
          indexId,
          indexName,
          autoCreate: autoCreate || UN_AUTO_CREATE,
          autoAssign: autoAssign || UN_AUTO_ASSIGN,
          assignMode: assignMode || AUTO_ASSIGN_UNIT,
          workLevel: workLevel || '1', //一级
          plannedDay: plannedDay || 7, //7天
        };
      });
    },
    handleAddWorkOrder() {
      this.workOrderVisible = true;
    },
    async deleteWorkOrder(index) {
      try {
        await this.$UiConfirm();
        this.workOrderConfigTableData.splice(index, 1);
      } catch (e) {
        console.log(e);
      }
    },
    async getDataBackupConfig() {
      let obj = await this.getConfig(this.backupKey);
      if (obj) {
        this.dataBackup = obj;
      }
    },
    async getSpecialConfig() {
      let obj = await this.getConfig(this.specialKey);
      if (obj) {
        this.specialAssessment = obj;
      }
    },
    async getDetectionConfig() {
      let obj = await this.getConfig(this.detectionKey);
      if (obj) {
        this.detectionResult = obj;
        if (!('showResult' in obj)) {
          this.detectionResult.showResult = 1;
        }
      }
    },
    async getDataMonitorConfig() {
      let obj = await this.getConfig(this.dataMonitorKey);
      if (obj) {
        this.dataMonitor = obj;
      }
    },
    async getConfig(key) {
      try {
        let params = {
          key: key,
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = data?.paramValue ? JSON.parse(data.paramValue) : null;
        return paramValue;
      } catch (error) {
        return null;
      }
    },
    changeAutoBackup(val) {
      if (val) {
        this.dataBackup.maxRetainNum = 7;
      } else {
        this.dataBackup.maxRetainNum = null;
        this.$refs['formValidate'].resetFields();
      }
      this.dataBackup.cronType = '1';
      this.dataBackup.timePoints = [];
      this.dataBackup.cronData = [];
    },
    checkTime(obj) {
      let { cronType, timePoints, cronData } = obj;
      this.dataBackup.cronType = cronType;
      this.dataBackup.timePoints = timePoints || [];
      this.dataBackup.cronData = cronData || [];
    },
    // 数据备份
    async updateJob(key, paramValue) {
      try {
        let data = {
          paramKey: key,
          paramValue: JSON.stringify(paramValue),
          paramType: 'ivdg',
          status: 0,
          description: '数据备份（通用配置）',
        };
        let res = await this.$http.post(home.updateJob, data);
        paramValue.xxlJobId = res.data.data;
      } catch (error) {
        throw Error(error);
      }
    },
    // 数据备份 【保存】
    async backupSave() {
      let valid = this.$refs['formValidate'].validate();
      if (valid) {
        this.updateConfig(this.backupKey, this.dataBackup);
      }
    },
    // 专题考核 【保存】
    async specialSave() {
      this.updateConfig(this.specialKey, this.specialAssessment);
    },
    // 检测结果 【保存】
    detectionResultSave() {
      this.updateConfig(this.detectionKey, this.detectionResult);
    },
    // 数据监测 【保存】
    async dataMonitorSave() {
      this.updateConfig(this.dataMonitorKey, this.dataMonitor);
    },
    async updateConfig(key, paramValue) {
      if (key === this.backupKey) {
        await this.updateJob(key, paramValue);
      }
      try {
        let params = {
          paramKey: key,
          paramValue: JSON.stringify(paramValue),
          paramType: 'ivdg',
        };
        await this.$http.put(home.updateByParamKey, params);
        this.$Message.success('保存成功');
      } catch (error) {
        this.$Message.error('保存失败');
        throw Error(error, 'err');
      }
    },
    //治理工单配置
    async getWorkOrderConfig() {
      try {
        this.workOrderLoading = true;
        const { data } = await this.$http.get(governancetask.getAutoConfiguration);
        this.workOrderConfigTableData = data.data;
      } catch (e) {
        console.log(e);
      } finally {
        this.workOrderLoading = false;
      }
    },
    async handleSaveWorkOrder() {
      if (!this.workOrderConfigTableData.length) return this.$Message.warning('治理工单配置不能为空!');
      await this.updateWorkOrderConfig();
      await this.getWorkOrderConfig();
    },
    async updateWorkOrderConfig() {
      try {
        this.saveWorkOrderLoading = true;
        const params = this.workOrderConfigTableData.map((item) => {
          const {
            id,
            taskSchemeId,
            taskName,
            indexId,
            indexName,
            autoCreate,
            autoAssign,
            assignMode,
            workLevel,
            plannedDay,
          } = item;
          return {
            id: id || '',
            taskSchemeId,
            taskName,
            indexId,
            indexName,
            autoCreate,
            autoAssign,
            assignMode,
            workLevel,
            plannedDay,
          };
        });
        const { data } = await this.$http.post(governancetask.updateAutoConfiguration, params);
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.saveWorkOrderLoading = false;
      }
    },
    //视图库时间失焦
    onBlurOfflineTime() {
      if (!this.dataMonitor.offlineTime) {
        this.dataMonitor.offlineTime = 2;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import '~@/views/disposalfeedback/governanceorder/components/work-level-tag/index.less';

.work-level-box {
  height: 16px;
  width: 16px;
}
.general-config-box {
  padding: 20px;
  font-size: 14px;
  color: var(--color-input);
  background: var(--bg-content);
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  overflow-y: auto;

  .form-box {
    margin-top: 25px;
  }

  @{_deep} .ivu-form-item-error-tip {
    left: 30px !important;
  }

  .config-item {
    width: 1200px;
    border-top: 1px dashed var(--devider-line);
    padding-top: 15px;
    margin-bottom: 25px;

    &:first-child {
      border-top: none;
      padding-top: 0;
    }

    .config-item-title {
      color: var(--color-card-title);
      font-size: 16px;
      font-weight: 900;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .options-bar {
        font-weight: 400;
      }
    }

    @{_deep}.ivu-table {
      .ivu-table-tip {
        table {
          height: 300px;
        }
      }
    }

    .config-wrap {
      margin-top: 25px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .config-title {
      width: 300px;
      text-align: right;

      &.required-text::before {
        content: '*';
        display: inline-block;
        margin-right: 4px;
        line-height: 1;
        font-family: SimSun;
        font-size: 12px;
        color: #ed4014;
      }
    }

    .config-content {
      // width: calc(100% - 126px);
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      background-color: var(--bg-table-body-td);
      margin-left: 18px;
      padding: 10px 20px;

      .width-full {
        width: 100%;
      }

      .tips {
        color: #c76d28;
      }
    }
  }

  @{_deep} .backup-form {
    .ivu-form-item {
      display: flex;
    }

    .ivu-form-item-label {
      padding-top: 20px;
    }

    .ivu-form-item-content {
      margin-left: 0 !important;
      flex: 1;
    }
  }

  @{_deep} .form-item-label .ivu-form-item-label {
    padding-top: 5px;
    line-height: 20px;
  }
}
</style>
