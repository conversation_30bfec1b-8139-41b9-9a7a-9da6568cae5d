<template>
  <div class="collection-area-container">
    <div class="search-container">
      <index-config @on-change="onChangeConfig" :filter-index="filterIndex"></index-config>
    </div>
    <div class="config-container" v-ui-loading="{ loading }">
      <base-config-area-num :data="configCjqyList" ref="baseConfigAreaNum"></base-config-area-num>
    </div>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';

export default {
  props: {},
  data() {
    return {
      keyDeviceGatherArea: [], //重点采集区域
      areaData: [], //所有的采集区域
      indexConfigAreaData: [], //所有的采集区域
      configCjqyList: [],

      formData: [],
      filterIndex: ['BASIC_CJQY_QUANTITY_STANDARD'],
      loading: false,
    };
  },
  async created() {
    await this.getDeviceSbcjqyData();
    await this.getStanderConfig();
  },
  methods: {
    onChangeConfig(val) {
      // 载入数量达标率 中的配置
      let {
        indexConfig: { configList, importList, otherList },
      } = val;
      if (!importList || !otherList || !configList) {
        return this.$Message.error('所选指标未配置采集区域');
      }
      this.setConfigCjqyListFormIndexConfig(configList);
    },
    validate(importList, otherList) {
      [...importList, ...otherList].forEach((item) => {
        if (!item.faceConfigNum || !item.vehicleConfigNum || !item.videoConfigNum) {
          let info = `${item.cjqyType === 0 ? '重点' : '其他'}必报采集区域${item.key}${item.value}达标数量不能为空`;
          this.$Message.error(info);
          throw new Error(info);
        }
      });
    },
    save() {
      let { importList, otherList } = this.$refs.baseConfigAreaNum.query();
      this.validate(importList, otherList);
      this.formData = [...importList, ...otherList].map((item) => {
        return {
          code: item.key,
          name: item.value,
          type: 2,
          videoNum: item.videoConfigNum,
          faceNum: item.faceConfigNum,
          vehicleNum: item.vehicleConfigNum,
        };
      });
      const configInfo = {
        type: 2,
        data: this.formData,
      };
      this.$emit('save', configInfo);
    },
    async getStanderConfig() {
      try {
        this.loading = true;
        //type =1 行政区划  2 采集区域
        const {
          data: { data },
        } = await this.$http.get(`${equipmentassets.getStanderConfig}/?type=2`);
        this.indexConfigAreaData = data || [];
        this.setConfigCjqyList();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    onChangeTaskScheme() {
      this.getTaskIndexGeneralConfig();
    },
    onChangeTaskIndex() {
      this.getConfig();
    },

    async getDeviceSbcjqyData() {
      try {
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getDeviceSbcjqyData);
        this.areaData = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    // set 数值
    setConfigCjqyList() {
      // 1 重点 2 是必报  0 普通
      let importList = [];
      let otherList = [];
      this.areaData.forEach((item) => {
        let index = this.indexConfigAreaData.findIndex((value) => value.code === item.code);
        if (item.type === '1' || item.type === '2') {
          importList.push({
            key: item.code,
            value: item.name,
            cjqyType: item.type === '1' ? 0 : 1, //--采集区域类型，0重点，1其他
            faceConfigNum: index !== -1 ? this.indexConfigAreaData[index]['faceNum'] || null : null,
            videoConfigNum: index !== -1 ? this.indexConfigAreaData[index]['videoNum'] || null : null,
            vehicleConfigNum: index !== -1 ? this.indexConfigAreaData[index]['vehicleNum'] || null : null,
          });
        }
      });
      this.configCjqyList = [...importList, ...otherList];
    },
    /**
     * @param configList
     * "c":"A0101",    --采集区划编码
     * "fn":100,     --人脸卡口 需上报数量
     * "vhn":120,    --车辆卡口 需上报数量
     * "vdn":150,  --视频监控 需上报数量
     * "t":0     --采集区域类型，0重点，1其他
     */
    setConfigCjqyListFormIndexConfig(configList) {
      let configIndex = configList.findIndex((item) => item.civilCode === this.getHomeConfig.regionCode);
      if (configIndex === -1) {
        return this.$Message.error('采集区域上报数量未配置');
      }
      //从指标 获取配置
      let importList = [];
      let otherList = [];
      let { cqList } = configList[configIndex];
      this.areaData.forEach((item) => {
        let index = cqList.findIndex((value) => value.c === item.code);
        if (item.type === '1' || item.type === '2') {
          importList.push({
            key: item.code,
            value: item.name,
            faceConfigNum: index !== -1 ? cqList[index]['fn'] || null : null,
            videoConfigNum: index !== -1 ? cqList[index]['vdn'] || null : null,
            vehicleConfigNum: index !== -1 ? cqList[index]['vhn'] || null : null,
            cjqyType: item.type === '1' ? 0 : 1,
          });
        }
      });
      this.configCjqyList = [...importList, ...otherList];
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
    }),
  },
  components: {
    BaseConfigAreaNum:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/base-config-area-num.vue')
        .default,
    IndexConfig: require('@/views/viewassets/commonreport/pointanalysis/components/index-config.vue').default,
  },
};
</script>
<style lang="less" scoped>
.collection-area-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow-y: auto;
}
</style>
