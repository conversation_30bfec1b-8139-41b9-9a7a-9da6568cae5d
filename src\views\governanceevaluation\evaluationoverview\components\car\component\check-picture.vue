<template>
  <ui-modal v-model="visible" title="查看图片" :styles="styles" class="check-picture" footer-hide>
    <ui-label class="mr-lg inline" label="检测结果：" :width="70"> </ui-label>
    <tagView class="tagView" ref="tagView1" :list="tagList" @tagChange="tagview" />
    <ui-label
      class="inline fr"
      label="异常原因"
      :width="70"
      v-if="[3010, 3011].includes(paramsList.indexId) && searchData.outcome == '2'"
    >
      <Select v-model="searchData.causeError" @on-change="changeErrorReason" class="width-lg fr">
        <Option v-for="(item, index) in checkList" :key="index" :value="item.key">{{ item.value }}</Option>
      </Select>
    </ui-label>
    <div class="check-content-wrap block">
      <TableCard ref="detailCard" :loadData="detailDataCard" :cardInfo="cardInfo">
        <!-- 卡片 -->
        <template #card="{ row }">
          <UiGatherCard
            v-if="cardInfo.length > 0 && [3001, 3003, 3006, 3007].includes(paramsList.indexId)"
            class="card"
            :list="row"
            :cardInfo="cardInfo"
            @bigImageUrl="bigImageUrl"
          ></UiGatherCard>
          <UiGatherCard1
            v-else-if="cardInfo.length > 0 && [3002].includes(paramsList.indexId)"
            class="card2"
            :list="row"
            :cardInfo="cardInfo"
            @bigImageUrl="bigImageUrl"
          ></UiGatherCard1>
          <InfoCard
            v-else
            class="card1"
            :list="row"
            :cardInfo="cardInfo"
            @bigImageUrl="bigImageUrl"
            :check-data="checkList"
            :paramsData="paramsList"
          >
          </InfoCard>
        </template>
      </TableCard>
    </div>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  props: {
    checkData: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    tagList: {
      type: Array,
      default: () => [],
    },
    value: {
      required: true,
      type: Boolean,
    },
    paramsList: {
      type: Object,
      default: () => {},
    },
    cardInfo: {
      type: Array,
      default() {},
    },
  },
  components: {
    InfoCard: require('./infoCard.vue').default,
    tagView: require('./tags.vue').default,
    UiGatherCard: require('./ui-gather-card.vue').default,
    UiGatherCard1: require('./ui-gather-card1.vue').default,
    TableCard: require('@/views/appraisaltask/inspectionrecord/components/tableCard.vue').default,
    LookScene: require('@/components/look-scene').default,
  },
  data() {
    return {
      checkList: [],
      visible: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      styles: {
        width: '7.7rem',
      },
      searchData: {
        outcome: '1',
        column: '',
        errorColumn: '',
        causeError: '',
      },
      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      deviceId: null,
      params: {},
      detailDataCard: (parameter) => {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        //卡片详情接口
        return superiorinjectfunc(
          this,
          evaluationoverview.getPolyData,
          {
            pageNumber: parameter.params.pageNumber,
            pageSize: parameter.params.pageSize,
            indexId: this.paramsList.indexId,
            batchId: this.paramsList.batchId,
            displayType: this.paramsList.displayType,
            regionCode: this.paramsList.regionCode,
            customParameters: {
              deviceIds: this.deviceId.split(','),
              ...this.searchData,
            },
          },
          'post',
          this.$route.query.cascadeId,
          {},
        ).then((res) => {
          return res.data;
        });
      },
    };
  },
  watch: {
    checkData: {
      handler(val) {
        if (val) {
          this.checkList = val;
        }
      },
      deep: true,
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
      this.info();
    },
    value(val) {
      this.visible = val;
    },
    list(val) {
      this.deviceId = val.deviceId;
    },
  },
  mounted() {},
  methods: {
    changeErrorReason(val) {
      this.searchData.causeError = val;
      this.$refs.detailCard.info(true);
    },
    // 初始化
    info() {
      this.searchData.outcome = '1';
      this.searchData.column = '';
      this.$nextTick(() => {
        this.$refs.detailCard.info(true);
      });
      this.$refs.tagView1.init();
    },
    // 标签状态切换
    tagview(val) {
      let outcomeAll = val;
      if (outcomeAll) {
        this.searchData.outcome = outcomeAll.split('__')[0];
        if (this.searchData.outcome == 4) {
          this.searchData.column = outcomeAll.split('__')[1] || '';
          this.searchData.outcome = '2';
          this.searchData.errorColumn = '';
        } else if (this.searchData.outcome == 2) {
          this.searchData.column = '';
          this.searchData.errorColumn = outcomeAll.split('__')[1] || '';
          this.searchData.outcome = '2';
        } else {
          this.searchData.errorColumn = '';
          this.searchData.column = '';
        }
      }
      this.$refs.detailCard.info(true);
    },
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
  },
};
</script>
<style lang="less" scoped>
.check-picture {
  @{_deep} .ivu-modal-body {
    padding: 20px;
    // height: 700px;
  }
  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  .check-content-wrap {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    margin-top: 10px;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
    padding-bottom: 8px;
  }
  .card1 {
    width: calc(calc(100% - 72px) / 5);
    margin: 0 5px 10px;
  }
  .card2 {
    width: calc(calc(100% - 40px) / 3);
    margin: 0 5px 10px 0;
  }
  .tagView {
    @{_deep} li {
      margin-bottom: 15px;
    }
  }
}
</style>
