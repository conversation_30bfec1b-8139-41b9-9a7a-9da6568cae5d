<template>
  <div class="search card-border-color">
    <Form :inline="true" :class="visible ? 'advanced-search-show' : ''">
      <div class="general-search">
        <div class="input-content">
          <div class="upload-input-list">
            <uiUploadImg
              ref="uploadImgRef"
              :algorithmType="algorithmType"
              @change="imgUrlChange"
              size="small"
            />
          </div>
          <div class="other-search">
            <div class="other-search-top card-border-color">
              <FormItem label="图搜类型:" prop="algorithmType">
                <RadioGroup
                  v-model="algorithmType"
                  @on-change="changeRadioGroup"
                >
                  <Radio
                    :label="item.dataKey"
                    v-for="(item, index) in graphTypeList"
                    :key="index"
                  >
                    <span>{{ item.dataValue }}</span>
                  </Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="相似度:" class="slider-form-item">
                <div class="slider-content">
                  <i
                    class="iconfont icon-jian add-subtract"
                    @click="addAndSubtract(false)"
                  ></i>
                  <Slider v-model="queryParam.similarity"></Slider>
                  <i
                    class="iconfont icon-jia add-subtract"
                    @click="addAndSubtract(true)"
                  ></i>
                  <span>{{ queryParam.similarity }}%</span>
                </div>
              </FormItem>
            </div>
            <div class="other-search-bottom">
              <div class="flex">
                <FormItem label="设备资源:">
                  <div class="select-tag-button" @click="selectDevice()">
                    选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                  </div>
                </FormItem>
                <FormItem label="抓拍时段:">
                  <ui-quick-date
                    ref="quickDateRef"
                    v-model="dateType"
                    @change="changeTime"
                  ></ui-quick-date>
                </FormItem>
              </div>
              <div class="btn-group">
                <span
                  class="advanced-search-text primary"
                  @click.stop="visible = !visible"
                >
                  更多条件 <i class="iconfont icon-jiantou"></i>
                </span>
                <Button type="primary" @click="searchHandle">查询</Button>
                <Button @click="resetHandle">重置</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--更多搜索条件-->
      <div class="advanced-search">
        <section class="search-container">
          <div class="search-item-container" id="searchItemContainer">
            <div class="classify-content">
              <span class="classify-name">服饰</span>
              <div class="items">
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="上身纹理:">
                        <selectTag
                          ref="upperTexture"
                          :list="upperBodyTextureList"
                          vModel="upperTexture"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="上身袖子类型:" class="long-label">
                        <selectTag
                          ref="sleeveStyle"
                          :list="upperSleeveTypeList"
                          vModel="sleeveStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>
                <div class="advanced-search-list card-border-color">
                  <FormItem label="上身颜色:">
                    <ui-tag-select
                      ref="upperColor"
                      @input="
                        (e) => {
                          handleInput(e, 'upperColor');
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in recognitionColorList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey"
                      >
                        <div
                          v-if="staticBodyColorList[item.dataKey]"
                          :style="{
                            borderColor:
                              staticBodyColorList[item.dataKey].borderColor,
                          }"
                          class="plain-tag"
                        >
                          <div :style="staticBodyColorList[item.dataKey].style">
                            {{ item.dataValue }}
                          </div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="下身类型:">
                        <selectTag
                          ref="lowerStyle"
                          :list="lowerBodyType"
                          vModel="lowerStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="下身颜色:">
                        <ui-tag-select
                          ref="lowerColor"
                          @input="
                            (e) => {
                              handleInput(e, 'lowerColor');
                            }
                          "
                        >
                          <ui-tag-select-option
                            v-for="(item, $index) in recognitionColorList"
                            :key="$index"
                            effect="dark"
                            :name="item.dataKey"
                          >
                            <div
                              v-if="staticBodyColorList[item.dataKey]"
                              :style="{
                                borderColor:
                                  staticBodyColorList[item.dataKey].borderColor,
                              }"
                              class="plain-tag"
                            >
                              <div
                                :style="staticBodyColorList[item.dataKey].style"
                              >
                                {{ item.dataValue }}
                              </div>
                            </div>
                          </ui-tag-select-option>
                        </ui-tag-select>
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">鞋子</span>
              <div class="items">
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="鞋子类别:">
                        <selectTag
                          ref="shoesStyle"
                          :list="shoeCategory"
                          vModel="shoesStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="鞋子颜色:">
                        <ui-tag-select
                          ref="shoesColor"
                          @input="
                            (e) => {
                              handleInput(e, 'shoesColor');
                            }
                          "
                        >
                          <ui-tag-select-option
                            v-for="(item, $index) in recognitionColorList"
                            :key="$index"
                            effect="dark"
                            :name="item.dataKey"
                          >
                            <div
                              v-if="staticBodyColorList[item.dataKey]"
                              :style="{
                                borderColor:
                                  staticBodyColorList[item.dataKey].borderColor,
                              }"
                              class="plain-tag"
                            >
                              <div
                                :style="staticBodyColorList[item.dataKey].style"
                              >
                                {{ item.dataValue }}
                              </div>
                            </div>
                          </ui-tag-select-option>
                        </ui-tag-select>
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">其他</span>
              <div class="items">
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="性别:">
                        <selectTag
                          ref="gender"
                          :list="genderList"
                          vModel="gender"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="发型:">
                        <selectTag
                          ref="hairStyle"
                          :list="hairStyleList"
                          vModel="hairStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>
                <!-- <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="行为:">
                        <selectTag
                          ref="behavior"
                          :list="behaviorList"
                          vModel="behavior"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="附属物:">
                        <selectTag
                          ref="appendix"
                          :list="appendantList"
                          vModel="appendix"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div> -->
              </div>
            </div>
          </div>
        </section>
      </div>
    </Form>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      :checkedLabels="checkedLabels"
      :defaultProps="defaultProps"
      :formDataProp="formDataProp"
      :formItemList="formItemList"
      :queryDataApiFn="queryDataApiFn"
      :tableColumns="tableColumns"
      @selectData="selectData"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import uiUploadImg from "@/components/ui-upload-image/index";
import selectTag from "../../components/select-tag.vue";
import { staticBodyColorList } from "@/libs/system";
import { queryYhsdDeviceList, picturePick } from "@/api/wisdom-cloud-search";
import dayjs from "dayjs";

export default {
  name: "",
  components: {
    uiUploadImg,
    selectTag,
  },
  data() {
    return {
      visible: false,
      algorithmType: 5, // 人体
      dateType: 1, // 抓拍时段类型
      queryParam: {
        algorithmVendorType: 5,
        selectDeviceList: [],
        features: [],
        imageBases: [],
        similarity: 50,
        startDate: dayjs().format("YYYY-MM-DD 00:00:00"), // 抓拍时段 - 开始时间
        endDate: dayjs().format("YYYY-MM-DD 23:59:59"), // 抓拍时段 - 结束时间
        deviceIds: [],
        upperTexture: "", // 上身纹理
        sleeveStyle: "", // 上身袖子
        upperColor: "", // 上身颜色
        lowerStyle: "", // 下身类型
        lowerColor: "", // 下身颜色
        shoesStyle: "", // 鞋子类别
        shoesColor: "", // 鞋子颜色
        gender: "", // 性别
        hairStyle: "", // 发型
      },
      checkedLabels: [],
      staticBodyColorList,
      graphTypeList: [
        { dataKey: 5, dataValue: "人脸" },
        { dataKey: 6, dataValue: "人体" },
      ],
      defaultProps: {
        id: "cameraId",
        deviceName: "cameraName",
      },
      formDataProp: {
        deviceName: "",
      },
      formItemList: [
        {
          type: "input",
          key: "deviceName",
          label: "设备名称",
          placeholder: "请输入设备名称或ip",
        },
      ],
      tableColumns: [
        { title: "选择", width: 65, type: "selection", key: "index" },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "设备名称", key: "cameraName" },
        { title: "设备编码", key: "deviceId" },
      ],
    };
  },
  computed: {
    ...mapGetters({
      pageType: "common/getPageType", // 页面类型
      globalObj: "systemParam/globalObj",
      upperBodyTextureList: "dictionary/getYhsdUpperBodyTexture", // 上身纹理
      upperBodyTypeList: "dictionary/getUpperBodyType", //上身类型
      upperSleeveTypeList: "dictionary/getUpperSleeveType", //上身袖子类型
      recognitionColorList: "dictionary/getRecognitionColor", //颜色
      lowerBodyType: "dictionary/getYhsdLowerBodyType", //下身类型
      shoeCategory: "dictionary/getShoeCategory", //鞋子类别
      hairStyleList: "dictionary/getHairStyleList", //发型
      behaviorList: "dictionary/getBehaviorList", //行为
      appendantList: "dictionary/getAppendantList", //附属物
      genderList: "dictionary/getGenderList", //性别
      classifySearchData: "common/getClassifySearchData", //查询数据
    }),
  },
  async activated() {
    //#region 系统配置参数 - 页面刷新时
    if (!this.globalObj.searchForPicturesDefaultSimilarity) {
      await this.getDictData();
    }
    this.queryParam.similarity = Number(
      this.globalObj.searchForPicturesDefaultSimilarity
    );
    //#endregion

    //#region 路由参数处理
    let query = this.$route.query;
    /**
     * 走到了mounted生命周期中，并满足该条件，说明是点击左侧菜单触发的，此时不需要处理路由携带的参数
     * 否则，则表示直接从别的页面跳到该页面，并直接定位到当前组件
     */
    if (query.sectionName !== "gaitContent") {
      let { list, timeSlot, startDate, endDate, searchSelect } =
        this.classifySearchData;
      if (searchSelect == 1) {
        this.queryParam.selectDeviceList = list;
        this.dateType = timeSlot;
        this.$nextTick(() => {
          this.$refs.quickDateRef.handleInit(timeSlot, startDate, endDate);
        });
      }
      this.$nextTick(() => {
        // 需要等时间更新后发起查询
        this.$emit("search");
      });
      return;
    }

    this.algorithmType = query.algorithmType ? Number(query.algorithmType) : 5;
    if (query.imgUrl) {
      let fileData = new FormData();
      fileData.append("algorithmType", this.algorithmType);
      fileData.append("fileUrl", query.imgUrl);
      await picturePick(fileData).then((res) => {
        if (!res.data || (res.data && res.data.length == 0)) {
          this.$Message.warning("没有识别出目标！");
        } else {
          let { feature, imageUrl, imageBase } = res.data[0];
          let urlList = {
            fileUrl: imageUrl,
            feature: feature,
            imageBase: imageBase,
          };
          this.urlImgList([urlList, ""], 1);
        }
      });
    }
    //#endregion
    this.$nextTick(() => {
      // 需要等时间更新后发起查询
      this.$emit("search");
    });
  },
  async mounted() {
    window.addEventListener("click", this.hideMoreQuery);
  },
  beforeDestroy() {
    window.removeEventListener("click", this.hideMoreQuery);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 隐藏更多条件框
     */
    hideMoreQuery() {
      this.visible = false;
    },

    /**
     * @description: 修改抓拍时段起始时间
     * @param {object} val 起始时间
     */
    changeTime(val) {
      this.queryParam.startDate = val.startDate;
      this.queryParam.endDate = val.endDate;
      let params = {
        list: this.queryParam.selectDeviceList,
        timeSlot: val.timeSlot || 1,
        startDate: val.startDate,
        endDate: val.endDate,
        searchSelect: 1,
      };
      this.setClassifySearchData(params);
    },

    /**
     * 选中tag赋值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.dataKey;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },
    /**
     * 选择接口返回数据
     */
    handleInput(e, key) {
      this.queryParam[key] = e;
    },

    /**
     * @description: 相似度调整
     * @param {boolean} flag 调整方向: true-加，false-减
     */
    addAndSubtract(flag = false) {
      if (flag) {
        this.queryParam.similarity++;
      } else {
        this.queryParam.similarity--;
      }
    },

    // 查询
    searchHandle() {
      this.hideMoreQuery();
      this.$emit("search");
    },
    resetHandle() {
      this.$refs.uploadImgRef.setDefaultUploadImage([""]);
      let searchList = {
        upperTexture: "",
        sleeveStyle: "",
        upperColor: "",
        lowerStyle: "",
        lowerColor: "",
        shoesStyle: "",
        shoesColor: "",
        gender: "",
        hairStyle: "",
        // behavior: "",
        // appendix: "",
      };
      this.algorithmType = 5;
      for (let key in searchList) {
        this.$refs[key].clearChecked();
        this.queryParam[key] = "";
      }
      this.queryParam.algorithmVendorType = 5;
      this.queryParam.selectDeviceList = [];
      this.queryParam.features = [];
      this.queryParam.imageBases = [];
      this.queryParam.similarity = Number(
        this.globalObj.searchForPicturesDefaultSimilarity
      );
      this.$refs.timerange.clearChecked(false);
      //#region 重置时间
      // 如果是自定义被重置，则需要清空已选择的时间
      if (this.dateType == 4) {
        this.$refs.quickDateRef.setDefaultDate();
      }
      this.dateType = 1;
      this.$nextTick(() => {
        this.changeTime(this.$refs.quickDateRef.getDate());
        this.searchHandle();
      });
      //#endregion
    },
    // 选择设备
    selectDevice() {
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList);
    },

    selectData(list) {
      this.queryParam.selectDeviceList = list;
      this.queryParam.deviceIds = list.map((item) => item.cameraId);
    },

    /**
     * @description: 设置默认的上传的图片
     * @param {array} list 图片列表
     * @param {number} index 标识，1 - 直接跳到分类检索，从$router.query拿图片，2 - 在当前分类检索下进行搜索目标添加，需要与已上传的图片合并
     */
    urlImgList(list, index = 1) {
      let newList = [...list];
      if (index == 2) {
        let alreadyUploadImages = this.$refs.uploadImgRef.getUploadedImages();
        if (alreadyUploadImages.length < 5) {
          // 当已上传图片不足5张时
          newList = [...alreadyUploadImages, ...newList]
            .filter((v) => !!v)
            .concat([""]);
        } else if (
          alreadyUploadImages.length === 5 &&
          !alreadyUploadImages[alreadyUploadImages.length - 1]
        ) {
          // 当为5但是最后一个为上传的按钮操作时
          newList = [...alreadyUploadImages, ...newList].filter((v) => !!v);
        } else {
          this.$Message.warning("数量超出限制");
          return false; // 数量超出限制后不做处理
        }
      }
      this.$refs.uploadImgRef.setDefaultUploadImage(newList);
      this.imgUrlChange(newList);
    },
    /**
     * 图片上传结果返回
     */
    imgUrlChange(list) {
      // 以图搜图字段
      let features = [];
      let imageBases = [];
      list.forEach((item) => {
        if (item) {
          features.push(item.feature);
          imageBases.push(item.imageBase);
        }
      });
      this.queryParam.features = features;
      this.queryParam.imageBases = imageBases;
    },

    async queryDataApiFn(data) {
      try {
        let res = await queryYhsdDeviceList(data);
        return res;
      } catch (error) {
        throw new Error(error);
      }
    },
    changeRadioGroup(val) {
      this.queryParam.algorithmVendorType = val;
    },

    /**
     * @description: 获取查询参数，供父组件使用
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./search-vehicle.less";
@import "style/index.less";
.search {
  padding: 10px 20px 0;
}
.other-search {
  display: flex;
  flex: 1;
  box-sizing: border-box;
  flex-direction: column;
  padding-left: 10px;
  .other-search-top {
    display: flex;
    border-bottom: 1px dashed #fff;
  }
  /deep/ .ivu-form-item {
    display: flex;
    margin-bottom: 10px;
    /deep/ .ivu-form-item-label {
      padding-right: 10px;
    }
    // .ivu-input-wrapper,
    .add-subtract {
      cursor: pointer;
    }
    .ivu-select {
      width: 200px;
    }
  }
  .other-search-bottom {
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    box-sizing: border-box;
    .slider-form-item {
      /deep/ .ivu-form-item-content {
        display: flex;
        align-items: center;
      }
    }
  }
}
.advanced-search-list {
  width: 100%;
  padding: 10px 0;
  border-bottom: 1px dashed #fff;
  &:last-child {
    border-bottom: 0;
  }
}
.searchPading {
  padding: 10px 20px;
}
/deep/ .long-label.ivu-form-item .ivu-form-item-label {
  width: 100px !important;
}
</style>
