<template>
  <ui-card :title="title" class="peers">
    <div slot="extra" class="more-btn">
      <ui-time-select
        :value="searchData.dateType"
        @on-change-date="onChangeDate"
        :time-list="timeList"
        :options="options"
        :identKey="identKey"
        :format="format"
      >
      </ui-time-select>
      <span @click="peersMoreHandle" class="ml-10">更多</span>
      <i class="iconfont icon-more"></i>
    </div>
    <div class="portrait-capture-content">
      <template v-for="(item, index) in computedTableList">
        <div class="content-item" :key="index" @click="peerDetail(item)">
          <div class="img-content">
            <i v-if="item.idCardNo" class="badge bg-primary">身</i>
            <ui-image
              :type="type"
              :src="item.traitImg || item.sceneImg"
              alt="动态库"
              objectFit="fill"
            />
            <div
              v-show="type === 'vehicle' || type === 'nonMotorVehicle'"
              class="shade"
            >
              {{ item.plateNo ? item.plateNo : "未知" }}
            </div>
          </div>
          <div class="bottom-info">
            <p class="info">
              近一月同行抓拍<span class="color-primary">{{ item.peerNum }}</span
              >次
            </p>
            <time>{{ item.absTime }}</time>
          </div>
        </div>
      </template>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!tableList || !tableList.length) && !loading" />
    <ui-modal v-model="detailShow" footer-hide class="peers-modal">
      <template #header>
        <div class="detail-title">
          <p>
            同行 <span> {{ currentDetail.peerNum || 0 }} </span> 次
          </p>
        </div>
      </template>
      <ul
        class="peer-content-box"
        v-infinite-scroll="load"
        :infinite-scroll-immediate="false"
      >
        <li
          class="content-box-li"
          v-for="(item, index) in currentDetail.peerDetails"
          :key="`${index}-${item.peerCaptureId}`"
        >
          <div class="box-li-icon">{{ index + 1 }}</div>
          <div class="box-li-right">
            <p class="box-li-right-title">{{ item.deviceAddress || "--" }}</p>
            <ul class="box-li-right-content">
              <li
                @click="
                  peerMessage(item.source, {
                    deviceAddress: item.deviceAddress,
                  })
                "
              >
                <div class="right-img-list">
                  <ui-image
                    :type="type"
                    :src="item.source.traitImg || item.source.sceneImg"
                    alt=""
                  />
                  <p class="right-img-list-tag tag-bule">对象</p>
                </div>
                <p class="box-li-right-time">
                  {{ item.source.absTime || "--" }}
                </p>
              </li>
              <li
                @click="
                  peerMessage(item.peer, {
                    deviceAddress: item.deviceAddress,
                  })
                "
              >
                <div class="right-img-list">
                  <!-- <ui-image :src="item.ferriteDetailVo.traitImg" viewer /> -->
                  <ui-image
                    :type="type"
                    :src="item.peer.traitImg || item.peer.sceneImg"
                    alt=""
                  />
                  <p class="right-img-list-tag tag-yellow">同行</p>
                  <div class="plateNumber">{{ item.peer.plateNo }}</div>
                </div>
                <p class="box-li-right-time">{{ item.peer.absTime || "--" }}</p>
              </li>
            </ul>
          </div>
        </li>
        <p class="loading" v-if="loadingText">加载中...</p>
        <p class="loading" v-if="noMore && peerList.length != 0">没有更多了</p>
      </ul>
    </ui-modal>
    <ui-modal
      v-model="peersMoreVisible"
      footer-hide
      class="peersMore"
      :rWidth="1430"
    >
      <template #header>
        <div class="detail-title">
          <p>{{ title }}</p>
        </div>
      </template>
      <div class="portrait-capture-content">
        <template v-for="(item, index) in tableList">
          <div class="content-item" :key="index" @click="peerDetail(item)">
            <div class="img-content">
              <i v-if="item.idCardNo" class="badge bg-primary">身</i>
              <ui-image
                :type="type"
                :src="item.traitImg || item.sceneImg"
                alt="动态库"
              />
              <div
                v-show="type === 'vehicle' || type === 'nonMotorVehicle'"
                class="shade"
              >
                {{ item.plateNo ? item.plateNo : "未知" }}
              </div>
            </div>
            <div class="bottom-info">
              <p class="info">
                近一月同行抓拍<span class="color-primary">{{
                  item.peerNum
                }}</span
                >次
              </p>
              <time>{{ item.absTime }}</time>
            </div>
          </div>
        </template>
      </div>
    </ui-modal>
    <video-detail
      v-show="videoShow"
      class="faceDetail"
      ref="videoDetail"
      :cutShow="false"
      @close="handleClose($event, 'videoShow')"
    />
    <vehicle-detail
      v-show="vehicleShow"
      class="vehicleDetail"
      :cutShow="false"
      ref="vehicleDetail"
      @close="handleClose($event, 'vehicleShow')"
    />
    <non-motor-vehicle
      v-show="nonMotorVehicleShow"
      class="vehicleDetail"
      :cutShow="false"
      ref="nonMotorVehicleDetail"
      @close="handleClose($event, 'nonMotorVehicleShow')"
    />
  </ui-card>
</template>

<script>
import {
  archivesQueryFaceCapturePageList,
  archivesQueryVehicleCapturePageList,
  queryFaceCapturePageList,
  queryNonMotorPeerAnalysisPageList,
  queryVehicleCapturePageList,
} from "@/api/modelMarket";
import NonMotorVehicle from "@/components/detail/non-motor-vehicle.vue";
import videoDetail from "@/components/detail/video";
import vehicleDetail from "@/components/detail/vehicle.vue";
import UiTimeSelect from "@/components/ui-time-select.vue";

export default {
  components: {
    NonMotorVehicle,
    videoDetail,
    vehicleDetail,
    UiTimeSelect,
  },
  props: {
    baseInfo: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    archiveNo: {
      type: String | Number,
      default: "",
    },
    data: {
      type: Array,
      default() {
        return [];
      },
    },
    getDataList: {
      type: Function,
      require: true,
    },
  },
  data() {
    return {
      identKey: {
        customKey: 4, // 自定义
        type: "text", //radio //tag
        key: "key",
        value: "value",
      },
      options: {
        disabledDate(date) {
          return false;
        },
      },
      format: "yyyy-MM-dd",
      timeList: [
        { value: "当天", key: 1 },
        { value: "近7天", key: 2 },
        { value: "近30天", key: 3 },
        { value: "自定义", key: 4 },
      ],
      // 5 是三个月 6 是6个月
      timeMap: {
        5: 3,
        6: 6,
      },
      searchData: {
        dateType: 1,
        startDate: "",
        endDate: "",
      },
      tableList: [],
      total: 0,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      peerList: [],
      detailShow: false,
      loadingText: false,
      noMore: false,
      currentDetail: {},
      detailParams: {},
      videoShow: false,
      vehicleShow: false,
      nonMotorVehicleShow: false,
      peersMoreVisible: false,
      loading: false,
    };
  },
  computed: {
    computedTableList() {
      if (!this.tableList) return [];
      if (this.tableList.length > 7) {
        return this.tableList.slice(0, 7);
      }
      return this.tableList;
    },
    timeValue() {
      let { value, key } = this.timeList.find(
        (item) => item.key === this.searchData.dateType
      );
      return value;
    },
  },
  created() {
    this.getPeerList();
  },
  methods: {
    onChangeDate(dataRange, times) {
      const [startDate = "", endDate = ""] = times;
      this.searchData.dateType = dataRange;
      this.searchData.startDate = startDate;
      this.searchData.endDate = endDate;
      // 特殊处理 近三个月六个月 后端未定义 需要前端传具体时间
      if ([5, 6].includes(dataRange)) {
        let [sd = "", ed = ""] = this.recentlyMonth(this.timeMap[dataRange]);
        this.searchData.startDate = sd;
        this.searchData.endDate = ed;
      }
      this.getPeerList();
    },
    async getPeerList() {
      try {
        this.tableList = [];
        this.loading = true;
        let params = {
          ...this.page,
          ...this.searchData,
          dateType: this.timeMap[this.searchData.dateType]
            ? 4
            : this.searchData.dateType, // 5 6 传自定义
        };
        let res = await this.getDataList(params);
        this.tableList = res.data.entities || [];
        this.total = res.data.total || 0;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    peersMoreHandle() {
      if (!this.tableList || this.tableList.length < 8)
        return this.$Message.warning("没有更多了");
      this.peersMoreVisible = true;
    },
    peerDetail(item) {
      this.detailShow = true;
      //非机动车档案不用调接口 直接取列表
      this.currentDetail = item;
    },
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        setTimeout(() => {
          this.noMore = false;
        }, 1000);
        return;
      } else {
        this.noMore = false;
        this.loadingText = true;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.rightList();
      }
    },
    /**
     * 同行 车辆详情弹框
     * @param row 同行车辆详情
     */
    peerMessage(row, extraInfo = {}) {
      if (this.type === "people") {
        this.videoShow = true;
        this.$refs.videoDetail.init(row);
      } else if (this.type === "vehicle") {
        this.vehicleShow = true;
        this.$refs.vehicleDetail.init(row);
      } else {
        this.nonMotorVehicleShow = true;
        this.$refs.nonMotorVehicleDetail.init({ ...extraInfo, ...row });
      }
    },
    handleClose(event, key) {
      event.stopPropagation();
      this[key] = false;
    },
  },
};
</script>

<style lang="less" scoped>
.shade {
  background: rgba(0, 0, 0, 0.7);
  font-size: 12px !important;
  width: 100%;
  bottom: 0;
  left: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  line-height: 18px;
  padding: 3px 0;
}

.peers {
  .more-btn {
    margin-right: 20px;
  }
  .portrait-capture-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .content-item {
    width: 187px;
    background: #f9f9f9;
    box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    border: 1px solid #d3d7de;
    box-sizing: border-box;
    padding: 10px;
    overflow: hidden;
    margin-right: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    &:nth-child(1) {
      margin-left: 0;
    }
    .img-content {
      width: 100%;
      height: 140px;
      overflow: hidden;
      position: relative;
      /deep/ .ui-image {
        width: 100%;
        height: 100%;
      }
      div {
        width: 100%;
        height: 30px;
        position: absolute;
        left: 0;
        bottom: 0;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        font-weight: 700;
      }
      .plate-no {
        display: inline-block;
        width: 84px;
        height: 22px;
        background: #2379f9;
        border-radius: 2px 2px 2px 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          width: 80px;
          height: 18px;
          border-radius: 2px 2px 2px 2px;
          border: 1px solid #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .badge {
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 4px;
        left: 0;
        top: 0;
        color: #fff;
        font-size: 12px;
        line-height: 21px;
        text-align: center;
      }
    }
    .bottom-info {
      padding-top: 10px;
      color: #000;
      font-size: 12px;
      .info,
      time {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      time {
        display: inline-block;
        margin-top: 10px;
      }
    }
  }
  /deep/ .card-content {
    padding: 20px 15px !important;
    min-height: 240px;
    box-sizing: border-box;
  }
  /deep/ .dom-wrapper {
    position: fixed;
    top: 91px;
    left: 500px;
    height: auto;
    z-index: 1999;
  }
  .peers-modal {
    /deep/ .ivu-modal-header {
      padding-left: 0 !important;
      background-color: #fff !important;
    }

    /deep/ .ivu-modal {
      top: -48px;
      left: 10px;
      margin: 0;
    }

    /deep/ .ivu-modal-body {
      height: 880px;
      overflow-y: auto;
    }
  }
  .peersMore {
    /deep/ .ivu-modal-header {
      padding-left: 0 !important;
      background-color: #fff !important;
    }

    /deep/ .ivu-modal-body {
      height: 880px;
      overflow-y: auto;
    }
  }
}
.peer-content-box {
  height: 640px;
  overflow: auto;
  position: relative;
  .content-box-li {
    margin-top: 10px;
    margin-bottom: 13px;
    display: flex;
    .box-li-icon {
      width: 24px;
      background: url("../../../../../../assets/img/archives/mark-red.png")
        no-repeat;
      background-size: 24px 28px;
      display: flex;
      justify-content: center;
      height: 30px;
      color: #ea4a36;
    }
    .box-li-right {
      flex: 1;
      margin-left: 12px;
      &-title {
        font-size: 12px;
        font-weight: bold;
        color: #181818;
      }
      &-content {
        background: #f9f9f9;
        border-radius: 4px;
        padding: 10px 9px 12px;
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
        li {
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .right-img-list {
          position: relative;
          width: 100px;
          margin-bottom: 10px;
          height: 100px;
          img {
            width: 100px;
            height: 100px;
            cursor: pointer;
          }
          &-tag {
            // width: 40px;
            // height: 20px;
            border-radius: 0px 0px 4px 0px;
            position: absolute;
            top: 0;
            color: #fff;
            font-size: 12px;
            padding: 1px 3px;
            z-index: 20;
          }
          .tag-bule {
            background: #2c86f8;
          }
          .tag-yellow {
            background: #f29f4c;
          }
          .plateNumber {
            position: absolute;
            bottom: 0px;
            left: 1px;
            background: rgba(0, 0, 0, 0.6);
            width: 98px;
            text-align: center;
            // opacity: 0.6;
            color: #ffffff;
            font-size: 14px;
          }
        }
      }
      .box-li-right-time {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
}

.detail-title {
  font-size: 16px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.9);
  position: relative;
  padding-left: 20px;
  border-bottom: 1px solid #d3d7de;
  display: flex;
  justify-content: space-between;
  align-items: center;
  top: 0;
  // z-index: 999;
  background: #fff;
  &:before {
    content: "";
    position: absolute;
    width: 3px;
    height: 20px;
    top: 50%;
    transform: translateY(-50%);
    left: 10px;
    background: #2c86f8;
  }
  span {
    color: #2c86f8;
  }
}
</style>
