<template>
  <div class="alarm">
    <div v-if="list.length > 0" class="my-swiper-container" id="mySwiper2">
      <swiper
        ref="mySwiper"
        :options="swiperOption"
        class="my-swiper"
        id="together-swiper"
      >
        <template v-for="(item, index) in list">
          <swiper-slide v-bind:key="index">
            <div class="swiper-item">
              <TogetherAlarm
                :data="item"
                @click.native="goDetailInfo(item, index)"
              >
              </TogetherAlarm>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="togetherLeft"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="togetherRight"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <TogetherDetail
      v-if="detailShow"
      ref="togetherDetailRef"
      :tableList="list"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      :showLeftLabel="true"
      @close="close"
    />
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import juvenileAlarm from "./collect/juvenileAlarm.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import TogetherAlarm from "./collect/together-alarm.vue";
import TogetherDetail from "@/views/juvenile/components/detail/together-detail.vue";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
    juvenileAlarm,
    TogetherAlarm,
    TogetherDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 1.235,
        centeredSlides: true,
        speed: 1000,
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch:
            (((420 / 1920) * window.innerWidth) / 1.235) *
            0.806 *
            (window.innerWidth / 1920), // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 150, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: false, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: "#togetherRight",
          prevEl: "#togetherLeft",
        },
        observer: true,
        observeParents: true,
      },
      tableList: [],
      total: 0,
      detailShow: false,
      alarmInfo: {},
      tableIndex: 0,
    };
  },
  watch: {},
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    goDetailInfo(item, index) {
      // this.$refs.togetherDetailRef.showInfo(item, index);
      this.tableIndex = index;
      this.detailShow = true;
    },
    close() {
      this.detailShow = false;
    },
    collection(params, flag) {
      if (flag == 1) {
        addCollection(params).then((res) => {
          this.$Message.success("收藏成功");
          if (this.carType == 1) {
            this.personnelList();
          } else {
            this.vehicleList();
          }
        });
      } else {
        deleteMyFavorite(params).then((res) => {
          this.$Message.success("取消收藏成功");
          if (this.carType == 1) {
            this.personnelList();
          } else {
            this.vehicleList();
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;

  span {
    color: #2c86f8;
  }
}
.alarm {
  width: 100%;
  height: 100%;
}
.my-swiper-container {
  padding: 0 20px;
  position: relative;
  height: 100%;
  .my-swiper {
    margin: auto;
    height: 100%;
    .swiper-item {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;

  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 20px;
}

.swiper-button-next {
  right: 20px;
}
</style>
