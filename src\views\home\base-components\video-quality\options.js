import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import Vue from 'vue';
import * as echarts from 'echarts';
import { fontSize } from '@/util/module/common';
import videoQualityStyle from '@/views/home/<USER>/module/video-quality/index.js';

export const getPieEchartOptions = (pieEchartsList, baseWidth = 120, homeStyleType) => {
  return {
    // backgroundColor: '#fff',
    tooltip: {
      padding: [0, 0], //内边距
      trigger: 'item',
      //enterable: true,
      axisPointer: {
        type: 'shadow',
      },
      confine: true, // 限制tootip在容器内
      formatter: function (params) {
        // ToolTipCard为vue组件，直接import , 第二个参数为props
        let tooltip = Vue.extend(TooltipDom);
        let _this = new tooltip({
          el: document.createElement('div'),
          name: 'toolBarTipDom',
          data() {
            return {
              data: params.data?.data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    },
    series: handlePieSeries(pieEchartsList, baseWidth, homeStyleType),
  };
};
const handlePieSeries = (pieEchartsList, baseWidth, homeStyleType) => {
  let baseTypeData = {
    1: {
      // 左上
      pos: ['26%', '26%'],
    },
    2: {
      // 左上
      pos: ['26%', '75%'],
    },
    3: {
      // 右上
      pos: ['70%', '26%'],
    },
    4: {
      // 右下
      pos: ['70%', '75%'],
    },
  };
  let result = [];
  let colorEnumObject = {
    ...(videoQualityStyle[`style${homeStyleType}`]['colorEnumObject'] ||
      videoQualityStyle['style1']['colorEnumObject']),
  };
  let startAngle = 200;
  let endAngle = -20;
  // let baseWidth = 120
  pieEchartsList.forEach((pieOne, index) => {
    let item = {
      indexId: pieOne?.indexId,
      name: pieOne?.indexName,
      value: pieOne.resultValue, // 其实是半圆的百分比[1-100]
      // 左右，上下
      pos: baseTypeData[(index % 4) + 1]?.pos ?? ['70%', '80%'],
      // 1是合格
      type: pieOne?.qualified === '1' ? 'blue' : 'red',
      num: pieOne.standardsValue,
      percent: `${pieOne.resultValue}%`,
    };
    result.push(
      // 最外面的一层半圆线
      {
        name: '外面半圆线',
        type: 'gauge',
        center: item.pos,
        radius: 0.5 * baseWidth,
        startAngle: startAngle,
        endAngle: endAngle,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            color: [
              [item.value / 100, colorEnumObject[item.type].color1],
              [1, colorEnumObject[item.type].color1],
            ],
            width: fontSize(2),
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        detail: {
          show: false,
        },
        title: {
          show: false,
        },
        data: [
          {
            value: item.value,
          },
        ],
        pointer: {
          show: true,
          icon: 'circle',
          length: '70%',
          radius: 0.6 * baseWidth,
          width: fontSize(3), //指针粗细
          itemStyle: {
            color: colorEnumObject[item.type].color2,
          },
        },
        animationDuration: 1000,
      },
      {
        name: '外部阴影',
        type: 'gauge',
        center: item.pos,
        radius: 0.5 * baseWidth,
        startAngle: startAngle,
        endAngle: endAngle,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            color: [
              [item.value / 100, colorEnumObject[item.type].outerShadowColor],
              [1, colorEnumObject[item.type].outerShadowColor],
            ],
            width: fontSize(38),
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        detail: {
          show: false,
        },
        title: {
          show: false,
        },
        data: [
          {
            value: item.value,
          },
        ],
        pointer: {
          show: false,
        },
        animationDuration: 1000,
      },
      // 有值的时候的刻度阴影
      {
        name: '内部阴影',
        type: 'gauge',
        radius: 0.38 * baseWidth,
        center: item.pos,
        startAngle: startAngle,
        endAngle: endAngle,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            color: [
              [
                item.value / 100,
                new echarts.graphic.LinearGradient(0, 1, 0, 0, colorEnumObject[item.type].shadowColor),
              ],
              [1, 'rgba(28,128,245,0)'],
            ],
            width: fontSize(20),
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        itemStyle: {
          show: false,
        },
        pointer: {
          show: false,
        },
      },
      // 外向内  - 第二个边
      {
        name: '内部小圆',
        type: 'gauge',
        center: item.pos,
        radius: 0.4 * baseWidth,
        startAngle: startAngle,
        endAngle: endAngle,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            color: [
              [
                item.value / 100,
                {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: colorEnumObject[item.type].color1,
                    },
                    {
                      offset: 1,
                      color: colorEnumObject[item.type].color2,
                    },
                  ],
                  global: true,
                },
              ],
              [1, 'rgba(0,0,0,0)'],
            ],
            width: fontSize(3),
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        itemStyle: {
          show: false,
        },
        pointer: {
          show: false,
        },
      },
      {
        name: '内部圆的border',
        type: 'gauge',
        center: item.pos,
        radius: 0.2 * baseWidth,
        startAngle: startAngle,
        endAngle: endAngle,
        splitNumber: 10,
        axisLine: {
          lineStyle: {
            color: [
              [item.value / 100, colorEnumObject[item.type].innerCircleBorder],
              [1, colorEnumObject[item.type].innerCircleBorder],
            ],
            width: fontSize(1),
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        // 文字
        detail: {
          formatter: function (value) {
            let one = pieEchartsList.find((item) => item.indexId === value);
            let indexName = one?.indexName ?? '未知';
            let indexNameLength = indexName.length;
            indexNameLength > 10 ? (indexName = indexName.substring(0, 12) + '...') : null;
            if (item.type === 'red') {
              return `{blue| ${one?.deviceNum ?? 0} /}{red| ${one?.resultValue ?? 0}%}\n{normal|${indexName}}`;
            } else {
              return `{blue| ${one?.deviceNum ?? 0} /}{green| ${one?.resultValue ?? 0}%}\n{normal|${indexName}}`;
            }
          },
          rich: {
            ...(videoQualityStyle[`style${homeStyleType}`]['rich'] || videoQualityStyle['style1']['rich']),
          },
          offsetCenter: [0, fontSize(24)],
        },
        // detail只能是数字，所以要根据id来对应查找数据
        data: [item.indexId],
        title: {
          show: false,
        },
        pointer: {
          show: false,
        },
        animationDuration: 1000,
      },
      {
        // 为了toolTip创建的透明圆
        type: 'pie',
        emphasis: {
          scale: false,
        },
        legendHoverLink: false,
        radius: [0.5 * baseWidth, 0.04 * baseWidth],
        center: item.pos,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: 0,
            itemStyle: {
              color: 'transparent',
            },
            data: item,
          },
        ],
      },
      {
        // 指针上的圆
        type: 'pie',
        tooltip: {
          show: false,
        },
        emphasis: {
          scale: false,
        },
        legendHoverLink: false,
        radius: ['0%', 0.04 * baseWidth],
        center: item.pos,
        label: {
          show: false,
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: 0,
            itemStyle: {
              color: colorEnumObject[item.type].color2,
            },
          },
        ],
      },
    );
  });
  return result;
};
