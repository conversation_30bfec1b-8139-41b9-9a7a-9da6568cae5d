<template>
  <ui-modal v-model="visible" width="60" :title="`已选择${modalTitle}`" @onCancel="handleReset" @query="handleSubmit">
    <div class="content-wrap auto-fill">
      <div class="mb-md">
        <Button type="primary" @click="bandleBatchRemove()" class="fr">
          <i class="icon-font icon-yichu2 f-14 mr-xs"></i>
          <span class="vt-middle">批量移除</span>
        </Button>
      </div>
      <ui-table
        reserveSelection
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template slot="action" slot-scope="{ row }">
          <ui-btn-tip
            class="operatbtn"
            icon="icon-yichu1"
            content="移除"
            @click.native="handleRemove(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        :transfer="true"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    selectedData: {
      type: Array,
      default: () => [],
    },
    modalTitle: {
      default: '设备',
    },
    rowKey: {
      default: 'id',
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      tableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: this.global.filedEnum.deviceId,
          key: 'deviceId',
          align: 'left',
          minWidth: 170,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.deviceName,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
          width: 80,
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      allTableDatas: [],
      defaultStoreData: [],
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.defaultStoreData = [];
        this.visible = true;
        if (this.selectedData.length) {
          this.loading = true;
          const ids = this.selectedData.map((item) => item[this.rowKey]);
          const paramsKey = this.rowKey === 'id' ? 'ids' : 'deviceIds';
          const res = await this.$http.post(equipmentassets.queryDeviceInfoByIds, { [paramsKey]: ids });
          this.allTableDatas = res.data.data;
          this.pageHandle();
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleReset() {},
    pageHandle() {
      this.tableData = this.$util.common.pagination(this.allTableDatas, this.pageData.pageNum, this.pageData.pageSize);
      this.pageData.totalCount = this.allTableDatas.length;
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.pageHandle();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.pageHandle();
    },
    storeSelectList(selection) {
      this.defaultStoreData = selection;
    },
    handleRemove(row) {
      let rmIndex = this.allTableDatas.findIndex((item) => {
        return item.id === row.id;
      });
      this.allTableDatas.splice(rmIndex, 1);
      this.pageHandle();
    },
    bandleBatchRemove() {
      this.defaultStoreData.forEach((item) => {
        this.handleRemove(item);
      });
      this.defaultStoreData = [];
      this.pageData.pageNum = 1;
      this.pageHandle();
    },
    handleSubmit() {
      this.visible = false;
      this.$emit('getSelectedList', this.allTableDatas);
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.content-wrap {
  height: 665px;
  padding: 0 20px 20px;
  background-color: var(--bg-content);
  overflow: initial;
}
.ui-table {
  overflow: auto;
}
</style>
