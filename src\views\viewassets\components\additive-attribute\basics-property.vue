<template>
  <div class="basics-property">
    <Form
      class="form"
      ref="form"
      inline
      autocomplete="off"
      label-position="right"
      :rules="ruleCustom"
      :model="formCustom"
    >
      <FormItem :required="isRequired('deviceId')" class="left-item" :label="`${global.filedEnum.deviceId}`">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.deviceId"
          placeholder="请输入设备编号"
          :maxlength="20"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.deviceId }}</span>
        <div class="error-tip ellipsis" :title="errorData.deviceId">
          {{ errorData.deviceId }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('deviceName')" class="left-item" :label="`${global.filedEnum.deviceName}`">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.deviceName"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.deviceName || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.deviceName">
          {{ errorData.deviceName }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sourceId')" class="left-item" label="数据来源" prop="sourceId">
        <Select class="width-md" v-if="!isView" v-model="formCustom.sourceId" placeholder="请选择数据来源" clearable>
          <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sourceIdText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sourceId">
          {{ errorData.sourceId }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('roomType')" class="left-item" label="安装位置">
        <Select v-if="!isView" v-model="formCustom.roomType" class="width-md" clearable placeholder="请选择安装位置">
          <Option v-for="(item, index) in locationList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.roomTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.roomType">
          {{ errorData.roomType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('port')" class="left-item" label="端口">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.port"
          placeholder="请输入端口号"
          :min="0"
          :max="999999999"
          :precision="0"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.port || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.port">
          {{ errorData.port }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('macAddr')" class="left-item" :label="`${global.filedEnum.macAddr}`">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.macAddr"
          :placeholder="`请输入${global.filedEnum.macAddr}`"
          :maxlength="32"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.macAddr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.macAddr">
          {{ errorData.macAddr }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('ipAddr')" class="left-item" label="IPV4地址">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.ipAddr"
          placeholder="请输入IP地址"
          :maxlength="32"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.ipAddr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ipAddr">
          {{ errorData.ipAddr }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('ipv6Addr')" class="left-item" label="IPV6地址">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.ipv6Addr"
          placeholder="请输入IPV6地址"
          :maxlength="64"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.ipv6Addr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ipv6Addr">
          {{ errorData.ipv6Addr }}
        </div>
      </FormItem>

      <FormItem :required="isRequired('longitude')" class="left-item" label="经度">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.longitude"
          placeholder="请输入经度"
          :min="0"
          :max="999999999"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.longitude || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.longitude">
          {{ errorData.longitude }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('latitude')" class="left-item" label="纬度">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.latitude"
          placeholder="请输入纬度"
          :min="0"
          :max="999999999"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.latitude || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.latitude">
          {{ errorData.latitude }}
        </div>
      </FormItem>

      <FormItem :required="isRequired('userId')" class="left-item" label="用户账号">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.userId"
          placeholder="请输入用户账号"
          :maxlength="64"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.userId || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.userId">
          {{ errorData.userId }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('password')" class="left-item" label="设备口令">
        <Input
          class="width-md"
          type="password"
          placeholder="请输入设备口令"
          password
          v-if="!isView"
          v-model="formCustom.password"
          @on-focus="focusPassword"
          @on-blur="blurPassword"
          :readonly="passwordRead"
          :maxlength="16"
        >
        </Input>
        <span v-else class="base-text-color">{{ formCustom.password || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.password">
          {{ errorData.password }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('laneNum')" class="left-item" label="卡口车道数">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.laneNum"
          placeholder="请输入卡口车道数"
          :min="0"
          :max="999999999"
          :precision="0"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.laneNum || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.laneNum">
          {{ errorData.laneNum }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('polingCode')" class="left-item" label="所属立杆编号">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.polingCode"
          placeholder="请输入所属立杆编号"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.polingCode || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.polingCode">
          {{ errorData.polingCode }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('capDirection')" class="left-item" label="车辆抓拍方向">
        <Select
          v-if="!isView"
          v-model="formCustom.capDirection"
          class="width-md"
          clearable
          placeholder="请选择车辆抓拍方向"
        >
          <Option
            v-for="(item, index) in carDirectionList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.capDirectionText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.capDirection">
          {{ errorData.capDirection }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('directionType')" class="left-item" label="摄像机监控方位">
        <Select
          v-if="!isView"
          v-model="formCustom.directionType"
          class="width-md"
          filterable
          clearable
          placeholder="请选择摄像机监视方位"
        >
          <Option
            v-for="(item, index) in deviceDirectionList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.directionTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.directionType">
          {{ errorData.directionType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('horizontalHeight')" class="left-item" label="摄像机水平安装高度">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.horizontalHeight"
          placeholder="请输入摄像机水平安装高度"
          :min="0"
          :max="999999999"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.horizontalHeight || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.horizontalHeight">
          {{ errorData.horizontalHeight }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('visualDistance')" class="left-item" label="摄像机可视距离">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-md"
          v-model="formCustom.visualDistance"
          placeholder="请输入摄像机可视距离"
          :min="0"
          :max="999999999"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.visualDistance || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.visualDistance">
          {{ errorData.visualDistance }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('businessGroupId')" class="left-item" label="虚拟组织所属业务分组ID">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.businessGroupId"
          placeholder="请输入虚拟组织所属业务分组ID"
          :maxlength="20"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.businessGroupId || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.businessGroupId">
          {{ errorData.businessGroupId }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbdwlx')" class="left-item" :label="`${global.filedEnum.sbdwlx}`">
        <Select
          v-if="!isView"
          v-model="formCustom.sbdwlx"
          class="width-md"
          filterable
          clearable
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(item, index) in cameraPointTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sbdwlxText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbdwlx">
          {{ errorData.sbdwlx }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('ptzType')" class="left-item" label="摄像机类型扩展">
        <Select
          v-if="!isView"
          v-model="formCustom.ptzType"
          class="width-md"
          filterable
          clearable
          placeholder="请选择摄像机类型扩展"
        >
          <Option v-for="(item, index) in ptzTypeList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.ptzTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ptzType">
          {{ errorData.ptzType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('useType')" class="left-item" label="摄像机用途">
        <Select
          v-if="!isView"
          v-model="formCustom.useType"
          class="width-md"
          filterable
          clearable
          placeholder="请选择摄像机用途"
        >
          <Option v-for="(item, index) in useTypeList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.useTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.useType">
          {{ errorData.useType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('positionType')" class="left-item" label="摄像机位置类型扩展">
        <Select
          v-if="!isView"
          v-model="positionType"
          class="width-md"
          filterable
          multiple
          :max-tag-count="1"
          clearable
          placeholder="请选择摄像机位置类型扩展"
        >
          <Option
            v-for="(item, index) in positionTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.positionTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.positionType">
          {{ errorData.positionType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbcjqy')" class="left-item" :label="global.filedEnum.sbcjqy">
        <Button v-if="!isView" type="dashed" class="area-btn" @click="clickArea"
          >请选择采集区域 {{ `已选择 ${sbcjqy.length}个` }}</Button
        >
        <span v-else class="base-text-color">{{ formCustom.sbcjqy || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbcjqy">
          {{ errorData.sbcjqy }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbgnlx')" class="left-item" :label="global.filedEnum.sbgnlx">
        <Select
          v-if="!isView"
          v-model="sbgnlx"
          class="width-md"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
        >
          <Option
            v-for="(item, index) in sbgnlxTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sbgnlxText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbgnlx">
          {{ errorData.sbgnlx }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbgnlxExt')" class="left-item" :label="global.filedEnum.sbgnlxExt">
        <Select
          v-if="!isView"
          v-model="sbgnlxExt"
          class="width-md"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbgnlxExt}`"
        >
          <Option
            v-for="(item, index) in sbgnlxExtList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sbgnlxExtText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbgnlxExt">
          {{ errorData.sbgnlxExt }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('nlj')" class="left-item" label="摄像机能力集">
        <Select
          v-if="!isView"
          v-model="nlj"
          class="width-md"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          :placeholder="`请选择摄像机能力集`"
        >
          <Option v-for="(item, index) in nljList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.nljText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.nlj">
          {{ errorData.nlj }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('supplyLightType')" class="left-item" label="摄像机补光属性">
        <Select
          v-if="!isView"
          v-model="formCustom.supplyLightType"
          class="width-md"
          filterable
          clearable
          placeholder="请选择摄像机补光属性"
        >
          <Option
            v-for="(item, index) in supplyLightTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.supplyLightTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.supplyLightType">
          {{ errorData.supplyLightType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('videoCodingM')" class="left-item" label="视频主码流编码格式">
        <Select
          v-if="!isView"
          v-model="formCustom.videoCodingM"
          class="width-md"
          filterable
          clearable
          placeholder="请选择视频主码流编码格式"
        >
          <Option
            v-for="(item, index) in videocodingMListTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.videocodingMText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.videoCodingM">
          {{ errorData.videoCodingM }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('videoCodingS')" class="left-item" label="视频子码流编码格式">
        <Select
          v-if="!isView"
          v-model="formCustom.videoCodingS"
          class="width-md"
          filterable
          clearable
          placeholder="请选择视频子码流编码格式"
        >
          <Option
            v-for="(item, index) in videocodingMListTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.videocodingSText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.videoCodingS">
          {{ errorData.videoCodingS }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('resolution')" class="left-item" label="摄像机支持的分辨率">
        <Input
          v-if="!isView"
          v-model="formCustom.resolution"
          class="width-md"
          placeholder="请输入分辨率多个以/隔开"
          :maxlength="50"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.resolution || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.resolution">
          {{ errorData.resolution }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('manufacturer')" class="left-item" label="设备厂商名称">
        <Select
          v-if="!isView"
          v-model="formCustom.manufacturer"
          class="width-md"
          clearable
          filterable
          placeholder="请选择设备厂商"
        >
          <Option
            v-for="(item, index) in manufacturerList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.manufacturerText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.manufacturer">
          {{ errorData.manufacturer }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('model')" class="left-item" label="设备规格型号">
        <Input
          v-if="!isView"
          v-model="formCustom.model"
          class="width-md"
          placeholder="请输入设备规格型号"
          :maxlength="50"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.model || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.model">
          {{ errorData.model }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('softVersion')" class="left-item" label="设备软件版本">
        <Input
          v-if="!isView"
          v-model="formCustom.softVersion"
          class="width-md"
          placeholder="请输入设备软件版本"
          :maxlength="50"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.softVersion || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.softVersion">
          {{ errorData.softVersion }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('networkType')" class="left-item" label="所属网络">
        <Select
          v-if="!isView"
          v-model="formCustom.networkType"
          class="width-md"
          filterable
          clearable
          placeholder="请选择所属网络"
        >
          <Option
            v-for="(item, index) in networkTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.networkTypeText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.networkType">
          {{ errorData.networkType }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('owner')" class="left-item" label="设备归属">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.owner"
          placeholder="请输入设备归属"
          :maxlength="300"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.owner || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.owner">
          {{ errorData.owner }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbgzcc')" class="left-item" label="是否挂载存储">
        <Select v-if="!isView" v-model="formCustom.sbgzcc" class="width-md" clearable placeholder="请选择是否挂载存储">
          <Option v-for="(item, index) in isList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sbgzccText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbgzcc">
          {{ errorData.sbgzcc }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('gzccip')" class="left-item" label="对应存储设备IP">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.gzccip"
          placeholder="请输入对应存储设备IP"
          :maxlength="20"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.gzccip || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.gzccip">
          {{ errorData.gzccip }}
        </div>
      </FormItem>
      <FormItem class="left-item" label="高度类型" :required="isFisrtPoint || isRequired('gdlx')">
        <Select v-if="!isView" v-model="formCustom.gdlx" class="width-md" clearable placeholder="请选择高度类型">
          <Option v-for="(item, index) in gdlxList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.gdlxText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.gdlxText">
          {{ errorData.gdlxText }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('hdjgh')" class="left-item" label="后端结构化">
        <Select v-if="!isView" v-model="formCustom.hdjgh" class="width-md" clearable placeholder="请选择是否后端结构化">
          <Option v-for="(item, index) in isList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.hdjghText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.hdjgh">
          {{ errorData.hdjgh }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('ljxy')" class="left-item" label="连接协议">
        <Select
          v-if="!isView"
          v-model="ljxy"
          class="width-md"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          placeholder="请选择连接协议"
        >
          <Option v-for="(item, index) in ljxyList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.ljxyText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ljxy">
          {{ errorData.ljxy }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('lxyjcc')" class="left-item" label="录像是否永久保存">
        <Select
          v-if="!isView"
          v-model="formCustom.lxyjcc"
          class="width-md"
          clearable
          placeholder="请选择录像是否永久保存"
        >
          <Option v-for="(item, index) in isList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.lxyjccText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.lxyjcc">
          {{ errorData.lxyjcc }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbsyq')" class="left-item" label="拾音器">
        <Select v-if="!isView" v-model="formCustom.sbsyq" class="width-md" clearable placeholder="请选择拾音器">
          <Option v-for="(item, index) in hasList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sbsyqText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbsyq">
          {{ errorData.sbsyq }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('parentId')" class="left-item" label="父设备/区域/设备ID">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.parentId"
          placeholder="请输入父设备/区域/设备ID"
          :maxlength="20"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.parentId || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.parentId">
          {{ errorData.parentId }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('parental')" class="left-item" label="是否有子设备">
        <Select
          v-if="!isView"
          v-model="formCustom.parental"
          @on-change="changeParental"
          class="width-md"
          clearable
          placeholder="请选择是否有子设备"
        >
          <Option v-for="(item, index) in whetherList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.parentalText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.parental">
          {{ errorData.parental }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('isMultiView')" class="left-item" label="镜头类型" v-if="hasMultiView">
        <Select
          v-if="!isView"
          v-model="formCustom.isMultiView"
          class="width-md"
          clearable
          placeholder="请选择是否多目摄像机"
        >
          <Option v-for="(item, index) in jtlxList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.isMultiViewText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.isMultiView">
          {{ errorData.isMultiView }}
        </div>
      </FormItem>
      <template v-if="!(formCustom.parental !== '1' && formCustom.isMultiView !== '1')">
        <FormItem
          :required="isRequired('childId')"
          v-if="formCustom.parental === '1'"
          class="left-item"
          label="子设备编号"
        >
          <Input
            v-if="!isView"
            type="text"
            class="width-md"
            v-model="formCustom.childId"
            placeholder="请输入子设备编号"
            :maxlength="20"
          ></Input>
          <span v-else class="base-text-color">{{ formCustom.childId || '未知' }}</span>
          <div class="error-tip ellipsis" :title="errorData.childId">
            {{ errorData.childId }}
          </div>
        </FormItem>
      </template>
      <FormItem v-if="formCustom.isMultiView === '1'" class="left-item" label="多目摄像机关联设备" required>
        <div class="device-content inline">
          <div class="camera" @click="selectCamera">
            <span class="font-blue camera-text" v-if="selectedList.length > 0"
              >已选择{{ selectedList.length }}条设备</span
            >
            <span v-else>
              <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
              <span class="font-blue camera-text">请选择设备</span>
            </span>
          </div>
        </div>
      </FormItem>
      <FormItem :required="isRequired('qdfs')" class="left-item" label="取电方式">
        <Select v-if="!isView" v-model="formCustom.qdfs" class="width-md" clearable placeholder="请选择取电方式">
          <Option v-for="(item, index) in qdfsList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.qdfsText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.qdfs">
          {{ errorData.qdfs }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('tdh')" class="left-item" label="通道号">
        <Input
          v-if="!isView"
          type="text"
          class="width-md"
          v-model="formCustom.tdh"
          placeholder="请输入通道号"
          :maxlength="20"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.tdh || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.tdh">
          {{ errorData.tdh }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('monitorAreaDesc')" class="area-desc" label="监控区域说明">
        <Input
          v-if="!isView"
          type="textarea"
          class="desc"
          v-model="formCustom.monitorAreaDesc"
          placeholder="请输入监视区域说明"
          :rows="5"
          :maxlength="256"
        ></Input>
        <span v-else class="base-text-color monitor-area-desc"> {{ formCustom.monitorAreaDesc || '未知' }}</span>
      </FormItem>
    </Form>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :expand-all="false"
      :checked-tree-data-list="checkedTreeData"
    ></area-select>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    choosedOrg: {
      type: Object,
    },
    defaultForm: {
      type: Object,
    },
    modalAction: {
      type: String,
    },
    errorData: {
      type: Object,
    },
    allDicData: {
      type: Object,
    },
    selectedList: {
      type: Array,
    },
    // 是否包含多目
    hasMultiView: {
      type: Boolean,
      default: true,
    },
    // 校验必填字段
    isRequired: {
      type: Function,
    },
  },
  data() {
    return {
      areaSelectModalVisible: false,
      checkedTreeData: [],
      carDirectionList: [
        // { label: "拍车头", value: 0 },
        // { label: "拍车尾", value: 1 },
      ],
      deviceDirectionList: [
        // { label: "东", value: "1" },
        // { label: "南", value: "2" },
        // { label: "西", value: "3" },
        // { label: "北", value: "4" },
        // { label: "东南", value: "5" },
        // { label: "东北", value: "6" },
        // { label: "西南", value: "7" },
        // { label: "西北", value: "8" },
      ],
      whetherList: [
        // { label: "否", value: 0 },
        // { label: "是", value: 1 },
      ],
      locationList: [
        // { label: "室内", value: "0" },
        // { label: "室外", value: "1" },
      ],
      cameraPointTypeList: [
        // { label: "一类点", value: "1" },
        // { label: "二类点", value: "2" },
        // { label: "三类点", value: "3" },
        // { label: "公安内部监控点", value: "4" },
        // { label: "其他点位", value: "9" },
      ],
      ptzTypeList: [
        // { label: "球机", value: "1" },
        // { label: "半球", value: "2" },
        // { label: "固定枪机", value: "3" },
        // { label: "遥控枪机", value: "4" },
      ],
      useTypeList: [
        // { label: "治安", value: 1 },
        // { label: "交通", value: 2 },
        // { label: "重点", value: 3 },
      ],
      positionTypeList: [
        // { label: "省际检查站", value: 1 },
        // { label: "党政机关", value: 2 },
        // { label: "车站码头", value: 3 },
        // { label: "中心广场", value: 4 },
        // { label: "体育场馆", value: 5 },
        // { label: "商业中心", value: 6 },
        // { label: "宗教场所", value: 7 },
        // { label: "校园周边", value: 8 },
        // { label: "治安复杂区域", value: 9 },
        // { label: "交通干线", value: 10 },
      ],
      sbgnlxTypeList: [
        // { label: "车辆卡口", value: "1" },
        // { label: "人员卡口", value: "2" },
        // { label: "微卡口", value: "3" },
        // { label: "特征摄像机", value: "4" },
        // { label: "普通监控", value: "5" },
        // { label: "高空瞭望摄像机", value: "6" },
        // { label: "其他", value: "99" },
      ],
      sbgnlxExtList: [],
      supplyLightTypeList: [
        // { label: "无补光", value: "1" },
        // { label: "红外补光", value: "2" },
        // { label: "白光补光", value: "3" },
        // { label: "其他补光", value: "9" },
      ],
      videocodingMListTypeList: [
        // { label: "MPEG-4", value: "1" },
        // { label: "H.264", value: "2" },
        // { label: "SVAC", value: "3" },
        // { label: "H.265", value: "4" },
      ],
      networkTypeList: [
        // { label: "监控报警专网", value: 4 },
        // { label: "公安信息网", value: 5 },
        // { label: "政务网", value: 6 },
        // { label: "互联网", value: 7 },
        // { label: "社会资源接入网", value: 8 },
        // { label: "预留", value: 9 },
      ],
      sourceList: [],
      manufacturerList: [],
      jtlxList: [],
      gdlxList: [],
      ljxyList: [],
      qdfsList: [],
      isList: [],
      hasList: [],
      nljList: [],
      passwordRead: true,
      fromList: [],
      sbgnlx: [],
      sbgnlxExt: [],
      sbcjqy: [],
      ljxy: [],
      nlj: [],
      positionType: [], //摄像机位置扩展
      formCustom: {
        deviceId: '',
        deviceName: '',
        sourceId: '',
        sourceName: '',
        macAddr: '',
        ipAddr: '',
        ipv6Addr: '',
        longitude: null,
        latitude: null,
        capDirection: '',
        capDirectionText: '',
        userId: '',
        password: '',
        polingCode: '',
        directionType: '',
        directionTypeText: '',
        parental: '',
        parentalText: '',
        port: null,
        roomType: '',
        roomTypeText: '',
        businessGroupId: '',
        sbdwlx: '',
        sbdwlxText: '',
        ptzType: '',
        ptzTypeText: '',
        useType: '',
        useTypeText: '',
        positionType: '',
        positionTypeText: '',
        sbgnlx: '',
        sbgnlxText: '',
        sbgnlxExt: '',
        sbgnlxExtText: '',
        nlj: '',
        nljText: '',
        supplyLightType: '',
        supplyLightTypeText: '',
        videoCodingM: '',
        videocodingMText: '',
        videoCodingS: '',
        videocodingSText: '',
        resolution: '',
        manufacturer: '',
        manufacturerText: '',
        model: '',
        softVersion: '',
        networkType: '',
        networkTypeText: '',
        horizontalHeight: null,
        visualDistance: null,
        owner: '',
        ljxy: '',
        lxyjcc: '',
        lxyjccText: '',
        qdfs: '',
        qdfsText: '',
        gdlx: '',
        gdlxText: '',
        hdjgh: '',
        hdjghText: '',
        sbgzcc: '',
        sbgzccText: '',
        gzccip: '',
        sbsyq: '',
        sbsyqText: '',
        parentId: '',
        laneNum: null,
        childId: '',
        monitorAreaDesc: '',
        sbcjqy: '',
        isMultiView: '',
        isMultiViewText: '',
        tdh: '',
      },
      formError: {},
      ruleCustom: {
        // sourceId: [
        //     {
        //         required: true,
        //         trigger: "blur change",
        //         message: "请选择数据来源",
        //         type: "number",
        //     },
        // ],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        this.checkedTreeData = this.sbcjqy || [];
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data) {
      this.sbcjqy = data;
    },

    selectSource(val) {
      let sourceId = val ? val.sourceId : '';
      this.formCustom.sourceId = sourceId;
    },
    focusPassword() {
      this.passwordRead = false;
    },
    blurPassword() {
      this.passwordRead = true;
    },
    changeParental(val) {
      if (val === 0) {
        delete this.formCustom.childId;
      } else {
        this.$set(this.formCustom, 'childId', '');
      }
    },
    async initDataList() {
      try {
        let res = await this.$http.get(equipmentassets.getAllDataImportSource, {
          params: { orgCode: this.choosedOrg.orgCode },
        });
        this.fromList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    validate() {
      return this.$refs.form.validate((valid) => {
        this.formCustom.sbgnlx = this.sbgnlx.join('/');
        this.formCustom.sbgnlxExt = this.sbgnlxExt.join('/');
        this.formCustom.sbcjqy = this.sbcjqy.join('/');
        this.formCustom.ljxy = this.ljxy.join('/');
        this.formCustom.nlj = this.nlj.join('/');
        this.formCustom.positionType = this.positionType.join('/');
        // 表单验证是否通过都需要更新表单数据
        this.$emit('putData', this.formCustom);
        return valid;
      });
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.sbgnlx = [];
      this.sbgnlxExt = [];
      this.sbcjqy = [];
      this.ljxy = [];
      this.nlj = [];
      this.positionType = [];
      this.formCustom = {
        deviceId: '',
        deviceName: '',
        sourceId: '',
        sourceName: '',
        macAddr: '',
        ipAddr: '',
        ipv6Addr: '',
        longitude: null,
        latitude: null,
        capDirection: '',
        capDirectionText: '',
        userId: '',
        password: '',
        polingCode: '',
        directionType: '',
        directionTypeText: '',
        parental: '',
        parentalText: '',
        port: null,
        roomType: '',
        roomTypeText: '',
        businessGroupId: '',
        sbdwlx: '',
        sbdwlxText: '',
        ptzType: '',
        ptzTypeText: '',
        useType: '',
        useTypeText: '',
        positionType: '',
        positionTypeText: '',
        sbgnlx: '',
        sbgnlxText: '',
        sbgnlxExt: '',
        sbgnlxExtText: '',
        nlj: '',
        nljText: '',
        supplyLightType: '',
        supplyLightTypeText: '',
        videoCodingM: '',
        videocodingMText: '',
        videoCodingS: '',
        videocodingSText: '',
        resolution: '',
        manufacturer: '',
        manufacturerText: '',
        model: '',
        softVersion: '',
        networkType: '',
        networkTypeText: '',
        horizontalHeight: null,
        visualDistance: null,
        owner: '',
        ljxy: '',
        lxyjcc: '',
        lxyjccText: '',
        qdfs: '',
        qdfsText: '',
        gdlx: '',
        gdlxText: '',
        hdjgh: '',
        hdjghText: '',
        sbgzcc: '',
        sbgzccText: '',
        gzccip: '',
        sbsyq: '',
        sbsyqText: '',
        parentId: '',
        laneNum: null,
        childId: '',
        monitorAreaDesc: '',
        sbcjqy: '',
        isMultiView: '',
        isMultiViewText: '',
        tdh: '',
      };
    },
    selectCamera() {
      this.$emit('selectCamera');
    },
  },
  watch: {
    choosedOrg() {
      // this.initDataList();
    },
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          if (length > 0) {
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                switch (key) {
                  case 'sbgnlx':
                  case 'sbgnlxExt':
                  case 'sbcjqy':
                  case 'ljxy':
                  case 'nlj':
                  case 'positionType':
                    if (val[key]) {
                      this[key] = val[key].split('/');
                    } else {
                      this[key] = [];
                    }
                    break;
                  default:
                    this.formCustom[key] = val[key] || '';
                    break;
                }
              }
            });
          }
        });
      },
      immediate: true,
    },
    errorData: {
      handler(val) {
        let length = Object.keys(val).length;
        if (length > 0) {
          Object.keys(val).forEach((key) => {
            if (this.formCustom.hasOwnProperty(key)) {
              this.formError[key] = val[key];
            }
          });
        } else {
          this.formError = {};
        }
      },
      immediate: true,
      deep: true,
    },
    allDicData: {
      handler(val) {
        for (let i in val) {
          if (this.hasOwnProperty(i)) {
            this[i] = val[i];
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    isAdd() {
      return this.modalAction === 'add';
    },
    isView() {
      return this.modalAction === 'view';
    },
    isFisrtPoint() {
      return this.formCustom.sbdwlx === '1';
    },
  },
  components: {
    AreaSelect: require('@/components/area-select').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 200px;
@inputWidth: 200px;
.basics-property {
  width: 100%;
  height: 586px;
  margin-top: 20px;
  overflow-y: auto;
  // border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  .area-desc {
    @{_deep} .ivu-form-item-error-tip {
      margin-left: @leftMargin;
    }
    @{_deep}.ivu-form-item-label {
      width: 200px;
    }
  }
  .desc {
    width: 660px;
  }
  .monitor-area-desc {
    word-wrap: break-word;
  }
  @{_deep}.org-tree {
    .ivu-select {
      width: @inputWidth;
    }
    .ivu-dropdown {
      width: @inputWidth;
    }
  }
  @{_deep}.ivu-form-item {
    width: 100%;
    margin-right: 0;
  }
  .left-item {
    width: 50%;
    @{_deep} .ivu-form-item-error-tip {
      margin-left: @leftMargin;
    }
    @{_deep}.ivu-form-item-label {
      width: 200px;
    }
  }
  @{_deep}.select-width {
    width: @inputWidth;
  }
  @{_deep}.tree {
    width: @inputWidth;
  }
  .error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1;
    padding-top: 5px;
    color: #ed4014;
    margin-left: @leftMargin;
    width: @inputWidth;
  }
  .ivu-select-dropdown {
    width: 100% !important;
  }
  .device-content {
    display: flex;
    justify-content: center;
    width: @inputWidth;
    .camera {
      width: @inputWidth;
      margin: 0 auto;
      padding: 0;
      height: 34px;
      line-height: 32px;
      background: rgba(43, 132, 226, 0.1);
      border: 1px dashed var(--color-primary);
      &:hover {
        background: rgba(43, 132, 226, 0.2);
        border: 1px dashed #3c90e9;
      }
      .icon-xuanzeshexiangji {
        line-height: 30px;
      }
    }
  }
  .area-btn {
    width: 200px;
  }
  @{_deep}.ivu-btn-dashed {
    padding: 0 !important;
  }
}
</style>
