// 评测报表接口
export default {
  getOptionalTasks: '/ivdg-evaluation-app/evaluation/task/getOptionalTasks', //根据组织机构查任务
  getOptionalResultsByTaskType: '/ivdg-evaluation-app/evaluation/app/taskResult/getOptionalResultsByTaskType', //根据任务查询考核时间
  // getOptionalResults: "/ivdg-evaluation-app/evaluation/result/getOptionalResults", //根据任务查询考核时间
  getSyntheticStatistics: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getSyntheticStatistics', //综合达标值柱状图
  getAverageValueTrend: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getAverageValueTrend', //综合达标值柱状图
  getRankStatistics: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getRankStatistics', //平均排行
  getSubIndexStatistics: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getSubIndexStatistics', //下级指标评价统计
  getIndexStatistics: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getIndexStatistics', //环形指标统计
  getHistogramIndexDetail: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getHistogramIndexDetail', //下级指标评价统计
  getReportStatisticsOfSimple: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getReportStatisticsOfSimple', //下级指标评价统计
  exportReportStatisticsOfSimple: '/ivdg-evaluation-app/evaluation/graphicalStatistics/exportReportStatisticsOfSimple', //导出报表
  exportReportOfDetail: '/ivdg-evaluation-app/evaluation/graphicalStatistics/exportReportOfDetail', //导出报表
  getReportStatisticsOfDetail: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getReportStatisticsOfDetail', //详报
  getOptionalRegion: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getOptionalRegion', //一览表组织机构
  getIndexOverviewData: '/ivdg-evaluation-app/evaluation/graphicalStatistics/getIndexOverviewData', //一览表列表
  exportIndexOverviewData: '/ivdg-evaluation-app/evaluation/graphicalStatistics/exportIndexOverviewData', //一览表导出

  queryEvaluationAmountCount: '/ivdg-data-governance-service/topic/statistics/queryEvaluationAmountCount', //数据总量

  getAllData: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getJiangsuHistogram', //获取全部数据
  getInterfaceStability: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getJiangsuStability', //获取接口稳定
  getInterfaceStatistics: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getListCount', //获取接口稳定统计
  getProblemOverview: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getProblemOverview', //获取接口稳定统计
  getOptionalBatchIdsByTaskSchemeId: '/ivdg-evaluation-app/evaluation/app/taskResult/getOptionalBatchIdsByTaskSchemeId', //获取接口稳定统计
  getOptionalBatchIdsByTaskSchemeIdV2:
    '/ivdg-evaluation-app/evaluation/app/taskResult/getOptionalBatchIdsByTaskSchemeIdV2', // post 获取接口稳定统计-有后端过滤逻辑
  operationIndexResultByBatchId: '/ivdg-evaluation-app/evaluation/app/taskResult/operationIndexResultByBatchId', // post 更改发布结果状态
};
