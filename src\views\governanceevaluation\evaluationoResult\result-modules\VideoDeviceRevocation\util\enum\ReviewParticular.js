export const iconStaticsList = [
  {
    name: '撤销次数:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-chexiaocishu',
    fileName: 'revocationCount',
  },
  {
    name: '累计撤销数量:',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-leijichexiaoshuliang',
    fileName: 'addUpRevocationNumber',
  },
  {
    name: '最终撤销数量:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-zuizhongchexiaoshuliang',
    fileName: 'finalRevocationNumber',
  },
  {
    name: '视频监控设备撤销率:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    style: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongshebeichexiaoshuai',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const tableColumns = () => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '对比时间',
      key: 'comparisonDate',
      align: 'left',
      tooltip: true,
      // minWidth: 260,
    },
    {
      title: '设备总量',
      key: 'deviceTotal',
      align: 'left',
      tooltip: true,
      // minWidth: 260, differenceValueByDeviceTotal
      render: (h, { row }) => {
        return (
          <span>
            <span>{row.deviceTotal || 0}</span>
            <span class={['ml-sm', row.differenceValueByDeviceTotal >= 0 ? 'color-up' : 'color-down']}>
              (
              {row.differenceValueByDeviceTotal > 0
                ? `+${row.differenceValueByDeviceTotal}`
                : row.differenceValueByDeviceTotal}
              )
            </span>
          </span>
        );
      },
    },
    {
      title: '撤销',
      key: 'revocationQuantity',
      align: 'left',
      tooltip: true,
      // minWidth: 260,
    },
    {
      title: '新增',
      key: 'addQuantity',
      align: 'left',
      tooltip: true,
      render: (h, { row }) => {
        return <span class={['ml-sm', 'color-up']}>{row.addQuantity || 0}</span>;
      },
    },
    {
      title: '修改',
      key: 'updateQuantity',
      align: 'left',
      tooltip: true,
      // minWidth: 260,
    },
    {
      title: '操作',
      slot: 'option',
      align: 'center',
      tooltip: true,
      width: 60,
      fixed: 'right',
      className: 'table-action-padding', // 操作栏列-单元格padding设置
    },
  ];
};
