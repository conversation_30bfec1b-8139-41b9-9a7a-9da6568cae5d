import { getIcon } from "../util/getIcon";

const getLinearGradientColor = (colors) => {
  const linearGradientColor = "l(0) ";
  return (
    linearGradientColor +
    colors
      .map((color, index) => `${index / (colors.length - 1)}:${color}`)
      .join(" ")
  );
};
export const customIconfontShape = (group, cfg) => {
  const size = cfg.size;
  const { unicode, bgColor } = getIcon(cfg.img);
  const iconfontGroup = group.addGroup({
    id: "iconfont-text-group",
    name: "iconfont-text-group",
    draggable: true,
  });
  iconfontGroup.addShape("circle", {
    attrs: {
      r: size[0] / 2,
      fill:
        Object.prototype.toString.call(bgColor) === "[object Array]"
          ? getLinearGradientColor(bgColor)
          : bgColor,
    },
    draggable: true,
    name: "custom-circle-iconfontBg-keyShape",
  });

  iconfontGroup.addShape("text", {
    attrs: {
      fontFamily: "iconfontconfigure", // 对应css里面的font-family: "iconfont";
      textAlign: "center",
      textBaseline: "middle",
      fill: "#fff",
      text: unicode,
      fontSize: size[0] / 2,
    },
    draggable: true,
    name: "iconfont-text-shape",
  });
  return iconfontGroup;
};
