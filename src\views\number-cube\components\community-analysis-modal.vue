import { number } from 'echarts';
<template>
  <ui-modal
    v-model="visible"
    title="社群分析"
    :r-width="450"
    @onOk="comfirmHandle">
    <Form ref="formData" :model="formData">
      <FormItem label="社群连接数:">
        <FormItem prop="calculationType" class="calculation-select">
          <Select :popper-append-to-body="false" v-model="formData.calculationType" placeholder="请选择" class="w110">
            <Option v-for="(item, $index) in calculationList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
          </Select>
        </FormItem>
        <FormItem prop="calculationValue">
          <Input v-model="formData.calculationValue" placeholder="请输入"/>
        </FormItem>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
  export default {
    data() {
      return {
        visible: false,
        formData: {
          calculationType: '=',
          calculationValue: ''
        },
        calculationList: [
          { dataKey: '=' , dataValue: '=' },
          { dataKey: '>' , dataValue: '>' },
          { dataKey: '<' , dataValue: '<' },
          { dataKey: '>=', dataValue: '>=' },
          { dataKey: '<=', dataValue: '<=' }
        ]
      }
    },
    methods: {
      init() {
        this.visible = true
        this.$nextTick(() => {
          this.$refs.formData.resetFields()
        })
      },
      comfirmHandle() {
        let calculationValue = parseInt(this.formData.calculationValue)
        if(!this.formData.calculationValue || typeof calculationValue !== 'number'){
          this.$Message.warning("请正确填写连接数")
          return
        }
        this.$emit('socialAnalysis', this.formData.calculationType,calculationValue)
        // const stragety = {
        //   '=' : (a,b) => a == b,
        //   '>' : (a,b) => a > b,
        //   '<' : (a,b) => a < b,
        //   '>=': (a,b) => a >= b,
        //   '<=': (a,b) => a <= b,
        // }
        // this.$emit('socialAnalysis',(num)=>{
        //   // 计算是否为true
        //   return stragety[this.formData.calculationType](num, calculationValue)
        // })
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .w110 {
    width: 110px;
  }
  /deep/ .ivu-modal-body {
    padding: 50px 72px 10px 46px !important;
  }
  /deep/ .ivu-form-item {
    margin-bottom: 20px;
    .ivu-form-item-content {
      display: flex;
    }
  }
  .calculation-select {
    margin-right: 6px;
  }
</style>
