<template>
  <div class="dom-wrapper" v-if="isShow">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon type="md-close" size="14" @click.native="isShow = false" />
      </header>
      <section class="dom-content">
        <carousel
          class="carousel-box"
          ref="carousel"
          :same-position-point="samePositionPoint"
          :nameKey="'communityName'"
          :imageKey="'communityImage'"
          @changeVid="changeVid"
        ></carousel>
        <div class="info-box">
          <div class="info-box-left">
            <div class="thumbnail">
              <img v-lazy="datailsInfo.traitImg" alt="" />
              <!-- <span class="similarity" v-if="datailsInfo.score"
                >{{ datailsInfo.score }}%</span
              > -->
            </div>
            <div
              class="record-title"
              :style="{
                'justify-content': 'flex-start',
              }"
            >
              <span :class="{ active: checkIndex == 0 }" @click="tabsChange(0)">
                抓拍记录
              </span>
              <!-- 没有vid不展示档案 -->
              <span
                v-show="datailsInfo.vid"
                :class="{ active: checkIndex == 1 }"
                @click="tabsChange(1)"
                >人员档案</span
              >
            </div>
            <div class="through-record" v-if="checkStatus">
              <div class="wrapper-content">
                <span class="label">抓拍地点</span>：
                <span
                  class="message ellipsis"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  v-html="datailsInfo.deviceName"
                  :title="
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                      : ''
                  "
                ></span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in faceList"
                :key="index"
              >
                <span class="label">{{ item.title }}</span
                ><span>：</span>
                <span
                  class="message"
                  :class="
                    item.clickHandler && datailsInfo[item.key] ? 'herf' : ''
                  "
                  v-if="!item.dictionary"
                  @click="
                    item.clickHandler && datailsInfo[item.key]
                      ? item.clickHandler(datailsInfo[item.key])
                      : () => {}
                  "
                  >{{ datailsInfo[item.key] || "--" }}</span
                >
                <span class="message" v-else>{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else>
              <div class="wrapper-content">
                <span class="label">视频身份</span><span>：</span>
                <span class="address" :class="{ identity: datailsInfo.vid }">{{
                  datailsInfo.vid || "--"
                }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) of archiveLabels"
                :key="index"
              >
                <span class="label">{{ item.title }}</span
                ><span>：</span>
                <span class="message">{{
                  faceArchives[item.key] || "--"
                }}</span>
              </div>
            </div>
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleTab('left')"
            ></i>
            <details-largeimg
              boxSeleType="rect"
              :acrossAppJump="true"
              :info="datailsInfo"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                // 'sc',
                'hy',
              ]"
              @collection="collection"
              :collectionType="5"
              :algorithmType="1"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleTab('right')"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleTab('left')"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="item.recordId"
            >
              <div class="img-box">
                <img v-lazy="item.traitImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleTab('right')"
        ></i>
      </footer>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import detailsLargeimg from "@/components/detail/details-largeimg.vue";
import { cutMixins } from "@/views/juvenile/components/detail/mixins.js";
import { commonMixins } from "@/mixins/app.js";
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
import carousel from "@/views/operations-on-the-map/map-default-page/components/map-dom/carousel.vue";
import { getNightEnterDetail } from "@/api/monographic/community-management.js";
export default {
  name: "",
  mixins: [commonMixins, cutMixins], //全局的mixin
  components: {
    swiper,
    swiperSlide,
    detailsLargeimg,
    carousel,
  },
  props: {
    isNoSearch: {
      type: Boolean,
      default: false,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
    tableIndex: {
      type: Number,
      default: -1,
    },
  },
  data() {
    const that = this;
    return {
      swiperOption: {
        spaceBetween: 10,
        slidesPerView: 12,
        freeMode: true,
        watchSlidesProgress: true,
        navigation: {
          nextEl: ".snap-next",
          prevEl: ".snap-prev",
        },
      },
      cutList: [],
      activeIndex: 0,
      faceList: [
        { key: "absTime", title: "抓拍时间", dictionary: "" },
        { key: "name", title: "姓名", dictionary: "" },
        { key: "age", title: "年龄", dictionary: "" },
        { key: "gender", title: "性别", dictionary: "genderList" },
        {
          key: "idCardNo",
          title: "身份证号",
          dictionary: "",
          clickHandler: (idCardNo) => {
            const { href } = that.$router.resolve({
              path:
                this.$route.path.split("-")?.[0] + "-archive/people-dashboard",
              query: {
                archiveNo: idCardNo,
                source: "people",
                initialArchiveNo: idCardNo,
              },
            });
            window.open(href, "_blank");
          },
        },
        { key: "national", title: "民族", dictionary: "nationList" },
        { key: "phoneNo", title: "联系方式", dictionary: "" },
        { key: "address", title: "户籍地址", dictionary: "" },
      ],
      archiveLabels: [
        { key: "xm", title: "姓名" },
        { key: "lxdh", title: "联系方式" },
        { key: "gmsfhm", title: "身份证号" },
        { key: "xzz_mlxxdz", title: "家庭住址" },
      ],
      checkStatus: true, // 抓拍记录，人员档案tab切换
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("@/assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      recordList: {},
      overlay: false,
      collectionType: 0,
      checkIndex: 0,
      imgBoxWidth: "",
      linkVehicle: {},
      pageParam: {
        pageNumber: 1,
        pageSize: 20,
      },
      isShow: false,
      bizAlarmType: "",
      idCardNo: "",
      firstPage: false,
      lastPage: false,
      samePositionPoint: [],
      faceArchives: {},
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      nationList: "dictionary/getNationList", //民族
      genderList: "dictionary/getGenderList", //性别
    }),
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    changeVid(value) {
      const { faceCaptureVoList = [], ...otherParam } = value;
      this.cutList = faceCaptureVoList?.map((item) => ({
        ...otherParam,
        ...item,
      }));
      this.activeIndex = 0;
      this.datailsInfo = {
        ...this.cutList[0],
      };
      this.locationPlay(0);
    },
    async tabsChange(index) {
      if (this.checkIndex == index) {
        return;
      }
      this.checkIndex = index;
      this.checkStatus = this.checkIndex == 0 ? true : false;
      if (!this.checkStatus) {
        try {
          if (!this.datailsInfo.vid) {
            this.$Message.warning("档案不存在！");
            return;
          }
          let res = await getPersonInfoByPersonId({
            vid: this.datailsInfo.vid,
          });
          if (res.code === 200) {
            this.faceArchives = res.data || {};
          }
        } catch (error) {
          this.$Message.warning("档案暂无数据");
        }
      }
    },
    // 切换
    handleTab(item, index) {
      if (item == "left") {
        if (this.activeIndex == 0) {
          this.goLastpage();
          return;
        }
        index = this.activeIndex - 1;
      }
      if (item == "right") {
        if (this.activeIndex + 1 == this.cutList.length) {
          this.goNextPage();
          return;
        }
        index = this.activeIndex + 1;
      }
      this.datailsInfo = this.cutList[index];
      this.activeIndex = index;
      this.play(index);
    },
    collection(flag) {
      this.$set(this.cutList[this.activeIndex], "myFavorite", flag);
    },
    // 不查询使用提供的列表
    showList(item) {
      getNightEnterDetail({ alarmId: item.alarmId }).then((res) => {
        this.samePositionPoint =
          res?.data?.map((item) => ({
            ...item,
            Index: item?.faceCaptureVoList?.length || 0,
          })) || [];

        if (this.samePositionPoint.length == 0) {
          return this.$Message.warning("该人员没有抓拍！");
        }
        const { faceCaptureVoList = [], ...otherParam } = res?.data[0];
        this.cutList = faceCaptureVoList?.map((item) => ({
          ...otherParam,
          ...item,
        }));
        this.activeIndex = 0;
        this.datailsInfo = {
          ...this.cutList[0],
        };
        this.isShow = true;
        this.$nextTick(() => {
          const divElement = document.querySelector(".list-wrapper");
          const firstElement = divElement.firstChild;
          this.imgBoxWidth = firstElement.clientWidth;
          this.imageNumWidth = this.imgBoxWidth;
          this.locationPlay(0);
        });
      });
    },
    // 上一页
    goLastpage() {
      return this.$Message.warning("已经是第一页");
      // if (this.firstPage) {

      // }
      // this.pageParam.pageNumber--;
      // this.getDataList(this.bizAlarmType, this.idCardNo, 19);
      // this.resetRightPage(19);
    },
    // 下一页
    goNextPage() {
      return this.$Message.warning("已经是最后一页");
      // if (this.lastPage) {

      // }
      // this.pageParam.pageNumber++;
      // this.getDataList(this.bizAlarmType, this.idCardNo, 0);
      // this.resetLeftPage(0);
    },
    // 获取字典
    translate(value) {
      return this[value];
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.carousel-box {
  margin-bottom: 10px;
}
.info-box {
  height: calc(~"100% - 90px") !important;
  .info-box-right {
    height: 100% !important;
  }
}
.info-box-left {
  .record-title {
    display: flex;
    gap: 20px;
    > span {
      cursor: pointer;
    }

    .record-right {
      margin-left: 20px !important;
    }
  }
}

.device-click {
  cursor: pointer;
  text-decoration: underline;
}

.rcgl {
  .title {
    color: #2c86f8;
  }

  .imgBox {
    cursor: pointer;
    width: 80%;
    margin: 5px 0;
    display: block;
    position: relative;

    img {
      width: 100%;
    }

    .driverFlag {
      position: absolute;
      right: 2px;
      top: 2px;
      color: #fff;
      background: red;
      border-radius: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.line {
  margin: 10px 0;
  height: 1px;
  background: #d3d7de;
}

.sub-title {
  font-size: 12px;

  .label {
    color: #2c86f8 !important;
    font-weight: bold;
  }
}
.active-box {
  // width: 100px;
  // height: 100px;
  border: 2px rgba(44, 134, 248, 1) solid !important;
  // position: absolute;
  // z-index: 1;
  // left: 10px;
  // transition: all 0.2s;
}
.herf {
  color: #2c86f8 !important;
  cursor: pointer;
}
</style>
