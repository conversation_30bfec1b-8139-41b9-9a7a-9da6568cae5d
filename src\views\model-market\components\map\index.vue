<!--
    * @FileDescription: 地图展示
    * @Author: 
    * @Date: 
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-20 11:41:43
 -->
<template>
  <div class="map-boxs">
    <div :id="mapId" class="map"></div>
    <component
      :show-header="true"
      :ref="sectionMenuName"
      @changeListTab="changeListTab"
      :positionPoints="positionPoints"
      :clickIndexId="clickIndexId"
      :is="sectionMenuName"
      @close="closeMapDom"
    >
    </component>
    <!-- 框选底部操作兰 -->
    <bottom-tool
      v-show="mapLayerConfig.mapToolVisible"
      @cancelDraw="cancelDraw"
      @selectDraw="selectDraw"
      @clearDraw="clearDraw"
      @closeMapTool="closeBottomTool"
      :drawComplete="drawComplete"
    ></bottom-tool>
    <!-- 框选结果弹框 -->
    <frame-selection-result
      ref="frameSelectionResult"
      :selectDeviceList="selectDeviceList"
      @openMapDom="openMapDom"
      @closeSelectWindowDom="clearModalBox"
      @updateLayerCheckedNames="updateLayerCheckedNames"
      @goSearch="goSearch"
      :crashType="crashType"
    >
    </frame-selection-result>
    <frame-selection-movebox
      ref="frameSelectionMovebox"
      :selectDeviceList="selectDeviceList"
      @openMapDom="openMapDom"
      @closeSelectWindowDom="clearModalBox"
      @updateLayerCheckedNames="updateLayerCheckedNames"
      @goSearch="goSearch"
      :crashType="crashType"
      @boxSelect="handleBoxSelect"
      @saveSelect="handleSaveSelect"
    >
    </frame-selection-movebox>
    <dele-draw ref="deleDraw" @dele="handleDele"></dele-draw>
    <!-- 鼠标浮动 -->
    <mouse-title ref="mouseDom" :name="mouseName"></mouse-title>
  </div>
</template>

<script>
import Vue from "vue";
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions, mapMutations } from "vuex";
import face from "../detail-dom/face-modal.vue";
import vehicle from "../detail-dom/vehicle-modal.vue";
import capture from "../detail-dom/capture-modal.vue";
import faceMap from "@/views/operations-on-the-map/map-default-page/components/map-dom/face-map-dom.vue";
import vehicleMap from "@/views/operations-on-the-map/map-default-page/components/map-dom/vehicle-map-dom.vue";
import trackmodal from "../detail-dom/track-modal.vue";
import FrameSelectionResult from "./frame-selection-result.vue";
import frameSelectionMovebox from "./frame-selection-movebox.vue";
import deleDraw from "./dele-draw.vue";
import mouseTitle from "@/components/map/mouse-title.vue";
import { LayerType, siteType } from "@/map/core/enum/LayerType.js";
import { myMixins } from "./point.js";
import BottomTool from "@/components/map/map-tool.vue";
import { kmeanData } from "./kmean_data.js";
import { mapMixins } from "@/mixins/map.js";

let mapMain = {
  trajectory: null, //轨迹分析
  peer: null, //同行分析
  nightDazed: null, //昼伏夜出
  foothold: null, //落脚点分析
  Camera_Face: null,
  Camera_Vehicle: null,
  casetoperson: null, //由案到人
  frequency: null, //人员频次分析
  prequent: null,
  vehicleAnaly: null, //车辆频次分析
  personToCase: null, //由人到案
};
let infoWindowArr = [];
let selectWindowArr = [];
let selectAloneWindow = [];
let mouseInfoWindow = [];
let dotColl = null; //碰撞

let _mapGeometery = null;
let _polygonLayer = null;
let _polygonStyle = {
  color: "#2C86F8",
  fillColor: "#1976ED", //填充颜色
  opacity: 1,
  weight: 3,
  fillOpacity: 0.25, //填充的透明度，取值范围0 - 1
};

export default {
  name: "",
  mixins: [myMixins, mapMixins],
  components: {
    face,
    vehicle,
    capture,
    trackmodal,
    FrameSelectionResult,
    frameSelectionMovebox,
    faceMap,
    vehicleMap,
    deleDraw,
    mouseTitle,
    BottomTool,
  },
  props: {
    // 是否禁止地图的滚动条事件
    disableScroll: {
      type: Boolean,
      default: false,
    },
    // 对象的轨迹
    lintList: {
      type: Array,
      default: () => [],
    },
    // 同行人的轨迹
    peerList: {
      type: Array,
      default: () => [],
    },
    // 重合位置撒点
    coincideList: {
      type: Array,
      default: () => [],
    },
    // 切换类型 加载弹框
    sectionName: {
      type: String,
      default: "face",
    },
    // 点击同行人(重合位置)
    peerIndex: {
      type: Number,
      default: -1,
    },
    // 轨迹同行
    trackList: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 图层点位信息
    allCameraList: {
      type: Array,
      default: () => [],
    },
    // 资源图层-选中的图层名称
    layerCheckedNames: {
      type: Array,
      default: () => [],
    },
    // 落脚点撒点
    castPoints: {
      type: Array,
      default: () => [],
    },
    // 当前点击的轨迹节点
    currentClickIndex: {
      type: Number,
      default: 0,
    },
    // 时空碰撞所选择的碰撞类型
    crashType: {
      type: String,
      default: "",
    },
    // 感知设备碰撞 碰撞设备类型
    eleCrashType: {
      type: Array,
      default: () => [
        "Camera_Vehicle",
        "Camera_Wifi",
        "Camera_RFID",
        "Camera_Electric",
      ],
    },
    // 是否感知碰撞
    isEleCrash: {
      type: Boolean,
      default: false,
    },
    // 时空碰撞
    collisionPoints: {
      type: Array,
      default: () => [],
    },
    // 建多个地图
    mapType: {
      type: String,
      default: "",
    },
    cutIcon: {
      type: String,
      default: "",
    },
    // 标准撒点数据
    basicPoints: {
      type: Array,
      default: () => [],
    },
    // 框选数据弹出框
    selectionBox: {
      type: Boolean,
      default: false,
    },
    // 隐匿车轨迹
    trackPoints: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      Map: null,
      mapId: "mapId" + Math.random(),
      onceInfoWindow: null,
      mapDomData: {},
      secLinePoint: null,
      lineLocation: null,
      coinPoint: null,
      lineTrack: null,
      basicPoint: null,
      sectionMenuName: "", // 当前选中的menuName
      clicktype: "",
      positionPoints: [],
      drawStyle: {
        color: "#2C86F8", //颜色
        fillColor: "#2C86F8", //填充颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
        lineStyle: "#2C86F8",
        strokeColor: "#2C86F8",
        showRadius: true,
      },
      boxPoint: null,
      selectDeviceList: [], // 框选设备集合
      selectPoints: [], // 单个框选图形位置集合
      drawComplete: false, //框选是否结束
      mapLayerConfig: {
        selectionResult: true, // 是否显示框选结果弹框
        mapToolVisible: false,
      },
      dotimg: null, //列表选择撒点
      dotColl: null, //碰撞
      markers: [], // 运动轨迹图层
      points: [],
      clickIndexId: 0,
      conditionIndex: -1, // 当前操作添加索引
      mouseName: "",
      kmeanData,
    };
  },
  watch: {
    lintList: {
      handler(val) {
        if (val.length > 0) {
          this.positionPoints = val;
          this.pathLine(0, val);
        }
      },
      immediate: true,
    },
    peerList: {
      handler(val) {
        if (val.length > 0) {
          this.$nextTick(() => {
            this.positionPoints = val;
            this.pathLine(1, val);
          });
        }
      },
      immediate: true,
    },
    trackList: {
      handler(val) {
        if (JSON.stringify(val) !== "{}") {
          this.$nextTick(() => {
            for (let key in val) {
              let color = key == "vidTrajectory" ? "#2C86F8" : "#1FAF81";
              if (val[key]) {
                this.trackLine(val[key], color, key);
              }
            }
          });
        }
      },
      immediate: true,
    },
    // coincideList: {
    //     handler(val) {
    //         console.log(val, 122)
    //         if( val.length > 0) {
    //             this.$nextTick(() => {
    //                 this.peerPoint();
    //             })
    //         }
    //     },
    //     immediate:true
    // },
    getPeerData: {
      //切换同行列表数据(索引，类型)
      handler(newVal) {
        let peerIndex = newVal.peerIndex;
        if (peerIndex == -1) {
          return;
        }
        let pointItem = [];
        if (newVal.menuType == "track") {
          //轨迹分析
          pointItem =
            (this.getTrackList.length && this.getTrackList[peerIndex]) || {};
        } else {
          //同行分析
          pointItem =
            (this.getPeerList.length && this.getPeerList[peerIndex]) || {};
          this.clicktype = "coincideModal";
        }
        // 缺 经纬度 地图不展示点位
        if (
          !pointItem.geoPoint ||
          !pointItem.geoPoint.lon ||
          !pointItem.geoPoint.lat
        ) {
          return this.$Message.warning("经纬度信息不全");
        }
        let sectionName = "";
        if (newVal.menuType == "track") {
          sectionName = newVal.type;
        } else {
          sectionName = this.sectionName;
        }
        this.sectionMenuName = sectionName + "";
        this.$nextTick(() => {
          if (pointItem) {
            this.selectItem(pointItem, sectionName, "", newVal.menuType);
          }
          pointItem &&
            this.$refs[this.sectionMenuName]
              .init(pointItem, [], "peer", newVal.menuType)
              .then((res) => {
                if (res.data) {
                } else {
                  this.$Message.warning("暂无数据");
                }
              });
        });
      },
      immediate: true,
    },
    allCameraList: {
      handler(val) {
        if (val.length) {
          let obj = val.map((arr, index) => {
            if (!(arr instanceof Array)) {
              return arr;
            }
            let item = {};
            item.deviceGbId = arr[0];
            item.deviceName = arr[1];
            item.deviceType = arr[2] + "";
            item.deviceChildType = arr[3] + "";
            item.sbgnlx = arr[4];
            item.isOnline = arr[5] + "";
            item.longitude = arr[6] + "";
            item.latitude = arr[7] + "";
            item.deviceId = arr[8];
            item.isCommunity = arr[9];
            item.geoPoint = { lat: item.latitude, lon: item.longitude };
            item.mapType = item.deviceType;
            item.ptzType = item.deviceChildType
              ? item.deviceChildType
              : item.ptzType;
            return item;
          });
          this._initSystemPoints2Map(obj);
        }
      },
    },
    // 落脚点
    castPoints: {
      handler(newVal) {
        if (newVal.length) {
          this.points = [...newVal];
          this.positionPoints = [...newVal];
          this.closeAllInfoWindow(); //关闭弹窗
          this.handlerSprinkle(newVal);
        }
      },
      immediate: true,
    },
    // 时空碰撞
    collisionPoints: {
      handler(newVal) {
        if (newVal.length) {
          this.points = [...newVal];
          this.positionPoints = [...newVal];
          this.closeAllInfoWindow(); //关闭弹窗
          this.handlerCollision(newVal);
        }
      },
      immediate: true,
    },
    // 隐匿车轨迹
    trackPoints: {
      handler(newVal) {
        if (newVal.length) {
          this.points = [...newVal];
          this.positionPoints = [...newVal];
          this.closeAllInfoWindow(); //关闭弹窗
          this.handlerTrackPoints(newVal);
        }
      },
      immediate: true,
    },
    sectionName: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.sectionMenuName = newVal + "";
        }
      },
      immediate: true,
    },
    currentClickIndex: {
      handler(newVal) {
        if (newVal == -1) {
          return;
        }
        const { currentClickIndex, points = [], sectionMenuName } = this;
        const pointItem = (points.length && points[currentClickIndex]) || {};
        // 缺 经纬度 地图不展示点位
        if (
          !pointItem.geoPoint ||
          !pointItem.geoPoint.lon ||
          !pointItem.geoPoint.lat
        ) {
          return this.$Message.warning("经纬度信息不全");
        }
        this.sectionMenuName = this.sectionName + "";
        // 普通搜索弹出模态框
        this.$nextTick(() => {
          if (pointItem) {
            this.selectItem(pointItem, this.sectionName, null, true);
          }

          // 活动轨迹下方切换
          let cutIcon = this.cutIcon == "map" ? false : true;
          pointItem &&
            this.$refs[this.sectionMenuName]
              .init(points[currentClickIndex], pointItem, "", cutIcon, true)
              .then((res) => {
                if (res.data) {
                } else {
                  this.$Message.warning("暂无数据");
                }
              });
        });
        // this.closeAllInfoWindow()
        this.clickIndexId = this.points[this.currentClickIndex].id;
        // this.handlerSprinkle(points)
      },
    },
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
      getPeerData: "map/getPeerData",
      getPeerList: "map/getPeerList",
      getTrackList: "map/getTrackList",
      resourceCoverage: "map/getResourceCoverage",
    }),
    // 图层名称数组
    layerTypeList() {
      let list = [];
      for (let key in LayerType) {
        list.push(LayerType[key].value);
      }
      for (let key in siteType) {
        list.push(siteType[key].value);
      }
      return list;
    },
  },
  created() {},
  async mounted() {
    await this.getMapConfig();
  },
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    ...mapMutations({
      setPeerData: "map/setPeerData",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    // 初始化地图
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain[this.mapType] = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain[this.mapType].init(mapId, data, style);
        _mapGeometery = new MapPlatForm.Base.MapGeometry(mapMain.map);
        // 禁止滚动条
        if (this.disableScroll) {
          mapMain[this.mapType].map.disableScrollWheelZoom();
        }
        this.configDefaultMap();
        this.Map = mapMain[this.mapType].map;
        this._mapGeometry = new MapPlatForm.Base.MapGeometry(this.Map);
        this.$emit("createMapBack", mapMain[this.mapType].map);
        this.$emit("initialed", this);
      });
    },
    getMap() {
      return mapMain[this.mapType].map;
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain[this.mapType].map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain[this.mapType].map.centerAndZoom(point, mapLayerLevel);
    },
    // 配置最大层级Symbomap
    addlimitLayerNum() {
      if (!!Number(this.mapObj.maxNumberOfLayer) || 600) {
        let allLayers = mapMain[this.mapType].map.getAllLayers.length;
        if (allLayers >= this.limitLayerNum) {
          this.$Message.error("已超过配置最大图层数量");
          return false;
        }
      }
      return true;
    },
    // 加载点位到地图上(资源图层)
    _initSystemPoints2Map(points) {
      this.$nextTick(() => {
        setTimeout(() => {
          // 加载点位
          mapMain[this.mapType].renderMarkers(
            mapMain[this.mapType].convertSystemPointArr3MapPoint(points),
            this.getMapEvents()
          );
        }, 1000);
      });
    },
    // 点击 设备
    getMapEvents() {
      const opts = {
        click: (marker) => {
          return;
          // const {
          //     ext: { Lat: lat, Lon: lon }
          // } = marker
          // this.openMapDom({ ...marker.ext, lat, lon })
        },
        mouseover: (marker) => {
          this.showMouseDom(marker);
        },
        mouseout: (marker) => {
          this.closeMouseInfoWindow();
        },
      };
      return opts;
    },
    closeMouseInfoWindow() {
      mouseInfoWindow.forEach((row) => {
        row.close();
      });
    },
    // 鼠标浮动在资源图层图标上
    showMouseDom(marker) {
      let { Lat, Lon } = marker.ext;
      this.mouseName = marker.ext.placeName || marker.ext.deviceName;
      const point = new NPMapLib.Geometry.Point(Lon, Lat);
      this.$nextTick(() => {
        const dom = this.$refs["mouseDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (60 / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 15;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain[this.mapType].map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        mouseInfoWindow.push(infoWindow);
      });
    },
    // 框选弹框
    selectionResultItem(pointItem, centerPoint, type) {
      if (!this.mapLayerConfig.selectionResult) {
        return false;
      }
      this.closeSelectWindowDom();
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        pointItem.lat || pointItem.geoPoint.lat
      );
      // mapMain.map.centerAndZoom(point, 17)
      mapMain[this.mapType].map.setCenter(centerPoint);
      const opts = {
        width: 50, // 信息窗宽度
        height: 180, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(-0, -0), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      this.mapDomData = this.allCameraList[0];
      let dom = "";
      if (type) {
        dom = this.$refs.frameSelectionMovebox.$el;
      } else {
        dom = this.$refs.frameSelectionResult.$el;
      }
      infoWindow.setContentDom(dom);
      mapMain[this.mapType].map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      selectWindowArr.push(infoWindow);
    },
    // 框选打开弹框(资源图层)
    openMapDom({
      deviceId,
      deviceName,
      detailAddress,
      lat,
      lon,
      mapType,
      picUrl,
      imageUrls,
      sbgnlx,
      myFavorite,
    }) {
      const map = new Map()
        .set("1", "1")
        .set("2", "1")
        .set("3", "1")
        .set("4", "2")
        .set("4", "2")
        .set("5", "3")
        .set("6", "4");
      this.sectionMenuName = "device";
      if (this.resourceCoverage) {
        return;
      }
      // 设置显示弹框
      this.$nextTick(() => {
        this.$refs["device"]
          .init(
            { deviceId, modelType: "1" },
            {
              geoPoint: {
                lat,
                lon,
              },
              deviceName,
              deviceId,
              detailAddress,
              deviceType: map.get(mapType),
              picUrl,
              imageUrls,
              sbgnlx,
              myFavorite,
            }
          )
          .then((res) => {
            if (res.data) {
              // this.selectItem({ lat, lon }, 'device', true, null, true)
            } else {
              this.$Message.warning("暂无数据");
            }
          });
      });
    },
    //更新图层
    updateLayerCheckedNames(layerCheckedNames) {
      this.$emit("updateLayerCheckedNames", layerCheckedNames);
    },
    goSearch(list) {
      this.$emit("gettingData", list, this.conditionIndex);
      // this.$emit('goSearch', this.selectDeviceList, isMerge)
      // this.clearDraw()
    },
    // 模态框上方tab切换后重新渲染
    changeListTab(item, type) {
      if (this.sectionName == "faceMap" || this.sectionName == "vehicleMap") {
        this.handlerCollision([item], type);
        return;
      }
      if (this.clicktype == "lineModal") {
        this.pathLine(0, [
          {
            lon: item.lon,
            lat: item.lat,
            faceCaptureList: [{ ...item }],
          },
        ]);
      } else if (this.clicktype == "peerModal") {
        this.pathLine(1, [
          {
            lon: item.lon,
            lat: item.lat,
            faceCaptureList: [{ ...item }],
          },
        ]);
      } else if (this.clicktype == "coincideModal") {
        this.peerPoint([
          {
            geoPoint: item.geoPoint,
            num: item.num,
            peerDetailVo: item,
          },
        ]);
      } else if (this.clicktype == "frequency") {
        this.selectItem(item, type);
        this.$nextTick(() => {
          this.$refs[type]
            .init(item, this.positionPoints, "frequency")
            .then((res) => {
              if (res.data) {
              } else {
                this.$Message.warning("暂无数据");
              }
            });
        });
      } else {
        let color = type == "vehicle" ? "#2C86F8" : "#1FAF81";
        this.trackLine([item], color, type);
      }
    },
    // 频次分析撒点
    sprinkleNormalPoint(pointList, detailFlag) {
      this.positionPoints = pointList;
      this.basicPoint = new NPMapLib.Layers.OverlayLayer("basicPoint");
      mapMain[this.mapType].map.addLayer(this.basicPoint);
      let points = [];
      let mapList = pointList;
      mapList.forEach((item) => {
        item.lon = item.geoPoint.lon;
        item.lat = item.geoPoint.lat;
        points.push(new NPMapLib.Geometry.Point(item.lon, item.lat));
      });
      // 判断某坐标是否多个
      let bothcoord = this.repetition(mapList);
      let multipleIcon = {};
      // 图片
      mapList.forEach((item) => {
        let k = item.lon + "_" + item.lat;
        let icon = null;
        let url = null;
        url = item.traitImg;
        const markerSize = item.showIconBorder ? [36, 36] : [32, 41];
        let size = new NPMapLib.Geometry.Size(...markerSize);
        let marker = new NPMapLib.Symbols.Marker(
          new NPMapLib.Geometry.Point(item.lon, item.lat)
        );
        icon = new NPMapLib.Symbols.Icon(this.$imgProxyToHttps(url), size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        marker.k = k;
        this.basicPoint.addOverlay(marker);
        let eventMarker = marker;
        let windowOffsetSize = markerSize;
        // 开启覆盖物点击打开弹窗
        if (item.showIconBorder) {
          let boxMarker = null;
          // 用来判断当前点位上是否有多个图片
          if (multipleIcon[k]) {
            multipleIcon[k] = multipleIcon[k] + 1;
          } else {
            multipleIcon[k] = 1;
          }
          if (!bothcoord.both[k] && multipleIcon[k] == 1) {
            let boxUrl = require(`@/assets/img/map/record_unRed.png`);
            let boxSize = new NPMapLib.Geometry.Size(40, 48);
            windowOffsetSize = [boxSize.width, boxSize.height];
            boxMarker = new NPMapLib.Symbols.Marker(
              new NPMapLib.Geometry.Point(item.lon, item.lat)
            );
            let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
            boxIcon.setAnchor(
              new NPMapLib.Geometry.Size(
                -boxSize.width / 2,
                -boxSize.height + 10
              )
            );
            boxMarker.setIcon(boxIcon);
            boxMarker.k = k;
            this.basicPoint.addOverlay(boxMarker);
          } else if (multipleIcon[k] >= 2) {
            if (bothcoord.single[k] == multipleIcon[k]) {
              let boxUrl = require(`@/assets/img/map/moreRecord_unRed.png`);
              let boxSize = new NPMapLib.Geometry.Size(44, 52);
              windowOffsetSize = [boxSize.width, boxSize.height];
              boxMarker = new NPMapLib.Symbols.Marker(
                new NPMapLib.Geometry.Point(item.lon, item.lat)
              );
              let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
              boxIcon.setAnchor(
                new NPMapLib.Geometry.Size(
                  -boxSize.width / 2,
                  -boxSize.height + 10
                )
              );
              boxMarker.setIcon(boxIcon);
              boxMarker.k = k;
              if (multipleIcon[k] >= 2) {
                multipleIcon[k] = multipleIcon[k] + 1;
              }
              this.basicPoint.addOverlay(boxMarker);
            }
          }
          eventMarker = boxMarker;
        }
        this.basicPoint.setZIndex(600);
        if (eventMarker && detailFlag) {
          // 点击事件
          eventMarker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
            console.log(this.sectionName, "this.sectionName");
            this.clicktype = "frequency";
            this.selectItem(item, this.sectionName);
            this.$nextTick(() => {
              this.$refs[this.sectionName]
                .init(item, mapList, "frequency")
                .then((res) => {
                  if (res.data) {
                  } else {
                    this.$Message.warning("暂无数据");
                  }
                });
            });
          });
        }
      });
    },
    // 频次分析列表点击事件
    chooseNormalPoint(data, type, index) {
      this.selectItem(data[index], type);
      this.$nextTick(() => {
        this.$refs[type].init(data[index], data, "frequency").then((res) => {
          if (res.data) {
          } else {
            this.$Message.warning("暂无数据");
          }
        });
      });
    },
    // 路线，点位
    pathLine(index, pointList) {
      if (index == 0) {
        this.lineLocation = new NPMapLib.Layers.OverlayLayer("firstLine");
      } else {
        this.secLinePoint = new NPMapLib.Layers.OverlayLayer("secLine");
      }
      let lineLay = index == 0 ? this.lineLocation : this.secLinePoint;
      mapMain[this.mapType].map.addLayer(lineLay);
      let points = [];
      // let mapList = index == 0 ? this.lintList : this.peerList;
      let mapList = pointList;
      mapList.forEach((item) => {
        points.push(new NPMapLib.Geometry.Point(item.lon, item.lat));
      });

      //#region 画轨迹
      let polyLineOption = {
        color: index ? "#F29F4C" : "#2C86F8", //颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        lineStyle: NPMapLib.LINE_TYPE_SOLID, //样式
      };
      let polyline = null;
      this.$util.common.getRoadNet(mapList).then((res) => {
        if (res.length) {
          polyline = new NPMapLib.Geometry.Polyline(
            res.map((item) => new NPMapLib.Geometry.Point(item.lon, item.lat)),
            polyLineOption
          );
        } else {
          polyline = new NPMapLib.Geometry.Polyline(points, polyLineOption);
        }
        polyline.isEnableEdit = true;
        if (index == 0) {
          this.lineLocation.addOverlay(polyline);
        } else {
          this.secLinePoint.addOverlay(polyline);
        }
      });
      //#endregion
      mapList.forEach((item) => {
        let k = item.lon + "_" + item.lat;
        let icon = null;
        let url = null;
        let boxUrl = require(`@/assets/img/map/moreRecord_${
          index == 0 ? "blue" : "orange"
        }.png`);
        let boxSize = new NPMapLib.Geometry.Size(44, 52);
        let boxMarker = new NPMapLib.Symbols.Marker(
          new NPMapLib.Geometry.Point(item.lon, item.lat)
        );
        let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
        boxIcon.setAnchor(
          new NPMapLib.Geometry.Size(-boxSize.width / 2, -boxSize.height)
        );
        boxMarker.setIcon(boxIcon);
        boxMarker.k = k;
        if (index == 0) {
          this.lineLocation.addOverlay(boxMarker);
        } else {
          this.secLinePoint.addOverlay(boxMarker);
        }
        // let url =  require(`@/assets/img/map/${index ? "trajectory-yellow": 'trajectory-blue'}.png`);
        if (item.faceCaptureList.length > 0) {
          item.faceCaptureList.map((ite, ind) => {
            url = ite.traitImg;
            let size = new NPMapLib.Geometry.Size(33, 33);
            let marker = new NPMapLib.Symbols.Marker(
              new NPMapLib.Geometry.Point(item.lon, item.lat)
            );
            icon = new NPMapLib.Symbols.Icon(url, size);
            icon.setAnchor(
              new NPMapLib.Geometry.Size(-size.width / 2, -size.height - 11)
            );
            marker.setIcon(icon);
            marker.k = k;
            if (index == 0) {
              this.lineLocation.addOverlay(marker);
            } else {
              this.secLinePoint.addOverlay(marker);
            }

            marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
              ite.lon = item.lon;
              ite.lat = item.lat;
              ite.captureAddress = item.captureAddress;
              this.selectItem(ite, this.sectionName);
              this.clicktype = index == 0 ? "lineModal" : "peerModal";
              item.faceCaptureList.map((iteGeo) => {
                iteGeo.lon = item.lon;
                iteGeo.lat = item.lat;
              });
              this.$nextTick(() => {
                this.$refs[this.sectionMenuName]
                  .init(ite, item.faceCaptureList)
                  .then((res) => {
                    if (res.data) {
                    } else {
                      this.$Message.warning("暂无数据");
                    }
                  });
              });
            });
          });
        }
      });
    },

    // 轨迹分析撒点
    trackLine(pointList, color, type) {
      if (!pointList.length) {
        return;
      }
      this.lineTrack = new NPMapLib.Layers.OverlayLayer("trackLine");
      mapMain[this.mapType].map.addLayer(this.lineTrack);
      let points = [];
      let mapList = pointList;
      mapList.forEach((item) => {
        if (item.geoPoint.lon && item.geoPoint.lat) {
          item.lon = item.geoPoint.lon;
          item.lat = item.geoPoint.lat;
          points.push(new NPMapLib.Geometry.Point(item.lon, item.lat));
        }
      });

      //#region 画轨迹
      let polyLineOption = {
        color: color, //颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        lineStyle: NPMapLib.LINE_TYPE_SOLID, //样式
      };
      let polyline = null;
      this.$util.common.getRoadNet(mapList).then((res) => {
        if (res.length) {
          polyline = new NPMapLib.Geometry.Polyline(
            res.map((item) => new NPMapLib.Geometry.Point(item.lon, item.lat)),
            polyLineOption
          );
        } else {
          polyline = new NPMapLib.Geometry.Polyline(points, polyLineOption);
        }
        polyline.isEnableEdit = true;
        this.lineTrack.addOverlay(polyline);
      });
      //#endregion
      // 判断某坐标是否多个
      let bothcoord = this.repetition(mapList);
      let multipleIcon = {};
      // 图片
      mapList.forEach((item) => {
        let k = item.lon + "_" + item.lat;
        let icon = null;
        let url = null;
        url = item.traitImg;
        let size = new NPMapLib.Geometry.Size(36, 36);
        let marker = new NPMapLib.Symbols.Marker(
          new NPMapLib.Geometry.Point(item.lon, item.lat)
        );
        icon = new NPMapLib.Symbols.Icon(this.$imgProxyToHttps(url), size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height - 11)
        );
        marker.setIcon(icon);
        marker.k = k;
        this.lineTrack.addOverlay(marker);
        let boxMarker = null;
        // 用来判断当前点位上是否有多个图片
        if (multipleIcon[k]) {
          multipleIcon[k] = multipleIcon[k] + 1;
        } else {
          multipleIcon[k] = 1;
        }
        if (!bothcoord.both[k] && multipleIcon[k] == 1) {
          let boxUrl = require(`@/assets/img/map/record_unRed.png`);
          let boxSize = new NPMapLib.Geometry.Size(40, 48);
          boxMarker = new NPMapLib.Symbols.Marker(
            new NPMapLib.Geometry.Point(item.lon, item.lat)
          );
          let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
          boxIcon.setAnchor(
            new NPMapLib.Geometry.Size(-boxSize.width / 2, -boxSize.height)
          );
          boxMarker.setIcon(boxIcon);
          boxMarker.k = k;
          this.lineTrack.addOverlay(boxMarker);
        } else if (multipleIcon[k] >= 2) {
          if (bothcoord.single[k] == multipleIcon[k]) {
            let boxUrl = require(`@/assets/img/map/moreRecord_unRed.png`);
            let boxSize = new NPMapLib.Geometry.Size(44, 52);
            boxMarker = new NPMapLib.Symbols.Marker(
              new NPMapLib.Geometry.Point(item.lon, item.lat)
            );
            let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
            boxIcon.setAnchor(
              new NPMapLib.Geometry.Size(-boxSize.width / 2, -boxSize.height)
            );
            boxMarker.setIcon(boxIcon);
            boxMarker.k = k;
            if (multipleIcon[k] >= 2) {
              multipleIcon[k] = multipleIcon[k] + 1;
            }
            this.lineTrack.addOverlay(boxMarker);
          }
        }
        this.lineTrack.setZIndex(600);
        if (boxMarker) {
          boxMarker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
            let sectionName = "trackmodal";
            this.clicktype = "trackLine";
            // trackmodal
            this.selectItem(item, sectionName);
            this.$nextTick(() => {
              this.$refs[this.sectionMenuName]
                .init(item, mapList, "", "track")
                .then((res) => {
                  if (res.data) {
                  } else {
                    this.$Message.warning("暂无数据");
                  }
                });
            });
          });
        }
      });
    },
    // 重合同行
    peerPoint(coincideList) {
      this.coinPoint = new NPMapLib.Layers.OverlayLayer("peerPoint");
      mapMain[this.mapType].map.addLayer(this.coinPoint);
      let points = [];
      let label = null;
      coincideList.forEach((item) => {
        item.lat = item.geoPoint.lat;
        item.lon = item.geoPoint.lon;
        points.push(new NPMapLib.Geometry.Point(item.lon, item.lat));
      });
      // 判断某坐标是否多个
      let bothcoord = this.repetition(coincideList);
      let multipleIcon = {};
      let imgUrl = null,
        iconbg = null;
      coincideList.forEach((item) => {
        let k =
          (item.lon || item.geoPoint.lon) +
          "_" +
          (item.lat || item.geoPoint.lat);
        // 获取的图片
        let url = item.peerDetailVo.traitImg;
        let size = new NPMapLib.Geometry.Size(34, 37);
        let marker = new NPMapLib.Symbols.Marker(
          new NPMapLib.Geometry.Point(item.lon, item.lat)
        );
        let icon = new NPMapLib.Symbols.Icon(url, size);
        marker.setIcon(icon);
        // 文本标记
        label = new NPMapLib.Symbols.Label(`${item.num}`);
        // label.setOffset(new NPMapLib.Geometry.Size(-13, 30))
        label.setStyle({
          fontSize: 12, //文字大小
          fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
          color: "#fff", //文字前景色
          align: "cm", //对方方式
          isBold: true, //是否粗体
        });
        // 用来判断当前点位上是否有多个图片
        if (multipleIcon[k]) {
          multipleIcon[k] = multipleIcon[k] + 1;
        } else {
          multipleIcon[k] = 1;
        }
        // marker.setLabel(label)
        mapMain[this.mapType].map.addLayer(this.coinPoint);
        this.coinPoint.addOverlay(marker);
        // 背景图片
        let markerBg = new NPMapLib.Symbols.Marker(item);
        if (!bothcoord.both[k] && multipleIcon[k] == 1) {
          let imgSize = new NPMapLib.Geometry.Size(40, 48);
          imgUrl = require(`@/assets/img/map/point_unRed.png`);
          iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize);
          iconbg.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2 - 2, -size.height - 2)
          );
          label.setOffset(new NPMapLib.Geometry.Size(-14, 30));
          markerBg.setIcon(iconbg);
          markerBg.setLabel(label);
          mapMain[this.mapType].map.addLayer(this.coinPoint);
          this.coinPoint.addOverlay(markerBg);
        } else if (multipleIcon[k] >= 2) {
          if (bothcoord.single[k] == multipleIcon[k]) {
            imgUrl = require(`@/assets/img/map/morePoint_unRed.png`);
            let moreSize = new NPMapLib.Geometry.Size(44, 52);
            iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize);
            iconbg.setAnchor(
              new NPMapLib.Geometry.Size(-size.width / 2 - 5, -size.height - 6)
            );
            label.setOffset(new NPMapLib.Geometry.Size(-12, 30));
            markerBg.setIcon(iconbg);
            markerBg.setLabel(label);
            mapMain[this.mapType].map.addLayer(this.coinPoint);
            this.coinPoint.addOverlay(markerBg);
          }
        }
        markerBg.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
          this.selectItem(item.peerDetailVo, this.sectionName);
          this.clicktype = "coincideModal";
          this.$nextTick(() => {
            this.$refs[this.sectionMenuName]
              .init(item.peerDetailVo, [], "peerMap")
              .then((res) => {
                if (res.data) {
                } else {
                  this.$Message.warning("暂无数据");
                }
              });
          });
        });
      });
    },
    clearSelectDraw() {
      if (_polygonLayer) {
        _polygonLayer.removeAllOverlays();
        mapMain[this.mapType].map.removeOverlay(_polygonLayer);
        _polygonLayer = null;
      }
    },
    // 区域显示
    showPolygon({ positionParam }) {
      var _polygon = _mapGeometery.getGeometryByGeoJson(positionParam);
      _polygon.setStyle(_polygonStyle);
      if (_polygonLayer) {
        _polygonLayer.removeAllOverlays();
      } else {
        _polygonLayer = new NPMapLib.Layers.OverlayLayer("_polygonLayer");
        mapMain[this.mapType].map.addLayer(_polygonLayer);
      }
      _polygonLayer.addOverlay(_polygon);
      var extent = _polygon.getExtent();
      if (mapMain[this.mapType].map.getZoom() < 15)
        mapMain[this.mapType].map.zoomTo(
          mapMain[this.mapType].map.getMaxZoom()
        );
      mapMain[this.mapType].map.zoomToExtent(extent, false, true);
    },
    // 落脚点撒点
    handlerSprinkle(points, listTab = false) {
      if (!points || points.length === 0) {
        return false;
      }
      // 判断某坐标是否多个
      let bothcoord = this.repetition(points);
      let markers = [];
      let temp = {};
      let multipleIcon = {};
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        item.iconType = true; //暂时需要
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let k = item.lon + "_" + item.lat;
        temp[k] == null ? (temp[k] = 0) : temp[k]++;
        let imgUrl = "",
          label = null,
          icon = null,
          iconbg = null,
          size = null;

        if (item.iconType) {
          //人像or车辆
          size = new NPMapLib.Geometry.Size(40, 40);
        } else {
          size = new NPMapLib.Geometry.Size(35, 35);
        }
        let marker = new NPMapLib.Symbols.Marker(item);
        // 用来判断当前点位上是否有多个图片
        if (multipleIcon[k]) {
          multipleIcon[k] = multipleIcon[k] + 1;
        } else {
          multipleIcon[k] = 1;
        }
        // 文本标记
        label = new NPMapLib.Symbols.Label(
          `${item.Index ? item.Index : index + 1}`
        );
        // 多数同一点、 一点一数
        this.dotimg = new NPMapLib.Layers.OverlayLayer("mapPoints");
        if (item.iconType) {
          // 人像or车辆
          imgUrl = item.traitImg;
          label.setOffset(new NPMapLib.Geometry.Size(-13, 30));
          label.setStyle({
            fontSize: 12, //文字大小
            fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
            color: "#fff", //文字前景色
            align: "cm", //对方方式
            isBold: true, //是否粗体
          });
        }
        // 设置图片
        icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        marker.setLabel(label);
        marker.k = k;
        mapMain[this.mapType].map.addLayer(this.dotimg);
        this.dotimg.addOverlay(marker);
        let markerBg = new NPMapLib.Symbols.Marker(item);

        if (item.iconType) {
          if (!bothcoord.both[k] && multipleIcon[k] == 1) {
            //单个
            let imgSize = new NPMapLib.Geometry.Size(44, 52);
            let imgTitle = listTab ? "blue" : item.active ? "blue" : "unRed";
            imgUrl = require(`@/assets/img/map/point_${imgTitle}.png`);
            iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize);
            iconbg.setAnchor(
              new NPMapLib.Geometry.Size(-size.width / 2 - 2, -size.height - 2)
            );
            label.setOffset(new NPMapLib.Geometry.Size(-13, 30));
            markerBg.setIcon(iconbg);
            markerBg.setLabel(label);
            mapMain[this.mapType].map.addLayer(this.dotimg);
            this.dotimg.addOverlay(markerBg);
            // markerBg.flash()
          } else if (multipleIcon[k] >= 2) {
            //多个
            if (bothcoord.single[k] == multipleIcon[k]) {
              imgUrl = require(`@/assets/img/map/morePoint_${
                item.active ? "blue" : "unRed"
              }.png`);
              let moreSize = new NPMapLib.Geometry.Size(52, 56);
              iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize);
              iconbg.setAnchor(
                new NPMapLib.Geometry.Size(
                  -size.width / 2 - 7,
                  -size.height - 6
                )
              );
              label.setOffset(new NPMapLib.Geometry.Size(-14, 30));
              if (multipleIcon[k] >= 2) {
                multipleIcon[k] = multipleIcon[k] + 1;
              }
              markerBg.setIcon(iconbg);
              markerBg.setLabel(label);
              mapMain[this.mapType].map.addLayer(this.dotimg);
              this.dotimg.addOverlay(markerBg);
              // markerBg.flash()
            }
          }
        }
        this.dotimg.setZIndex(600);
        markerBg.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
          // this.modalShow = false;
          // setTimeout(()=> {
          //     this.modalShow = true
          // }, 200)
          // this.clicktype= 'search';
          this.selectItem(this.points[index], this.sectionName, true, true);
          this.$emit("chooseMapItem", index);
        });
        marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
          // this.clicktype= 'search';
          // this.selectItem(this.points[index], this.sectionName, true, true)
          // this.$emit('chooseMapItem', index)
        });
        markers.push(marker);
      }
      this.markers = markers;
    },
    // 撒点
    /**
     * pointList: 数据
     * name：图层名
     * 兼容一开始未传name调用
     */
    sprinklePoint(pointList, name = "basicPoint", { windowOpts } = {}) {
      this.basicPoint = new NPMapLib.Layers.OverlayLayer(name);
      mapMain[this.mapType].map.addLayer(this.basicPoint);
      let points = [];
      let mapList = pointList;
      mapList.forEach((item) => {
        item.lon = item.geoPoint.lon;
        item.lat = item.geoPoint.lat;
        points.push(new NPMapLib.Geometry.Point(item.lon, item.lat));
      });
      // 判断某坐标是否多个
      let bothcoord = this.repetition(mapList);
      let multipleIcon = {};
      // 图片
      mapList.forEach((item) => {
        let k = item.lon + "_" + item.lat;
        const markerSize = item.showIconBorder ? [36, 36] : [32, 41];
        let eventMarker = null;
        let windowOffsetSize = markerSize;
        // 开启覆盖物点击打开弹窗
        // const dataTypes = ["face", "vehicle"];
        if (item.showIconBorder) {
          let boxMarker = null;
          // 用来判断当前点位上是否有多个图片
          if (multipleIcon[k]) {
            multipleIcon[k] = multipleIcon[k] + 1;
          } else {
            multipleIcon[k] = 1;
          }
          if (!bothcoord.both[k] && multipleIcon[k] == 1) {
            eventMarker = this.creatSprinkleMarker(item, k, markerSize);
            let boxUrl = require(`@/assets/img/map/record_unRed.png`);
            let boxSize = new NPMapLib.Geometry.Size(40, 48);
            windowOffsetSize = [boxSize.width, boxSize.height];
            boxMarker = new NPMapLib.Symbols.Marker(
              new NPMapLib.Geometry.Point(item.lon, item.lat)
            );
            let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
            boxIcon.setAnchor(
              new NPMapLib.Geometry.Size(
                -boxSize.width / 2,
                -boxSize.height + 10
              )
            );
            boxMarker.setIcon(boxIcon);
            boxMarker.k = k;
            this.basicPoint.addOverlay(boxMarker);
          } else if (multipleIcon[k] >= 2 && bothcoord.both[k]) {
            if (bothcoord.single[k] == multipleIcon[k]) {
              eventMarker = this.creatSprinkleMarker(item, k, markerSize);
              let boxUrl = require(`@/assets/img/map/moreRecord_unRed.png`);
              let boxSize = new NPMapLib.Geometry.Size(44, 52);
              windowOffsetSize = [boxSize.width, boxSize.height];
              boxMarker = new NPMapLib.Symbols.Marker(
                new NPMapLib.Geometry.Point(item.lon, item.lat)
              );
              let boxIcon = new NPMapLib.Symbols.Icon(boxUrl, boxSize);
              boxIcon.setAnchor(
                new NPMapLib.Geometry.Size(
                  -boxSize.width / 2,
                  -boxSize.height + 10
                )
              );
              boxMarker.setIcon(boxIcon);
              boxMarker.k = k;
              if (multipleIcon[k] >= 2) {
                multipleIcon[k] = multipleIcon[k] + 1;
              }
              this.basicPoint.addOverlay(boxMarker);
            }
          }
          eventMarker = boxMarker;
        } else {
          eventMarker = this.creatSprinkleMarker(item, k, markerSize);
        }
        this.basicPoint.setZIndex(600);
        if (eventMarker && windowOpts) {
          // const data = multipleIcon[k].singleSerial.length > 1
          // 点击事件
          eventMarker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
            this.creatInfoWindow(item, {
              ...windowOpts,
              offsetSize: [0, -1 * windowOffsetSize[1]],
              ...item.windowOpts,
            });
          });
        }
      });
    },
    creatSprinkleMarker(item, k, markerSize) {
      let icon = null;
      let url = null;
      url = item.traitImg;
      let size = new NPMapLib.Geometry.Size(...markerSize);
      let marker = new NPMapLib.Symbols.Marker(
        new NPMapLib.Geometry.Point(item.lon, item.lat)
      );
      icon = new NPMapLib.Symbols.Icon(url, size);
      icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height));
      marker.setIcon(icon);
      marker.k = k;
      this.basicPoint.addOverlay(marker);
      return marker;
    },
    //创建地图弹窗
    creatInfoWindow(pointItem, windowOpts = {}) {
      const {
        setContentDom,
        offsetSize = [0, 0],
        contentpProps,
        eventHandler,
        nativeEventHandler,
        ...opt
      } = windowOpts;
      const point = this.getPoint(pointItem);
      // mapMain.map.centerAndZoom(point, 17)
      this.setCenter(pointItem);
      this.closeInfoWindow();
      const opts = {
        offset: new NPMapLib.Geometry.Size(...offsetSize), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
        autoSize: true, // 默认true, 窗口大小是否自适应
        isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
        ...opt,
      };
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      this.mapDomData = this.allCameraList[0];
      const dom = document.createElement("div");
      dom.setAttribute("id", "map-info-window");
      infoWindow.setContentDom(dom);
      mapMain[this.mapType].map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      // infoWindow.panMapIfOutOfView();
      // infoWindow.updatePosition();
      this.onceInfoWindow = infoWindow;
      const contentDom = setContentDom && setContentDom(pointItem);
      if (this.isVueComponent(contentDom)) {
        new Vue({
          store: this.$store,
          router: this.$router,
          render: (h) =>
            h(contentDom, {
              props: {
                data: { ...pointItem },
                close: this.closeInfoWindow,
                ...contentpProps,
              },
              style: { transform: "translate(-50%,-100%)" },
              // 仅用于组件，用于监听原生事件，而不是组件内部使用
              on: {
                ...eventHandler,
              },
              // `vm.$emit` 触发的事件。
              nativeOn: {
                ...nativeEventHandler,
              },
            }),
        }).$mount("#map-info-window");
      }
      return infoWindow;
    },
    isVueComponent(component) {
      return (
        typeof component === "object" &&
        (component.render || component.template)
      );
    },
    getPoint(pointItem, offsetSize = [0, 0]) {
      return new NPMapLib.Geometry.Point(
        (pointItem.lon || pointItem.geoPoint.lon) + offsetSize[0],
        (pointItem.lat || pointItem.geoPoint.lat) + offsetSize[1]
      );
    },
    openPositionTheWindow(pointItem, windowOpts) {
      this.creatInfoWindow(pointItem, windowOpts);
    },
    setCenter(pointItem) {
      const flag = ["face", "vehicle"].includes(pointItem.dataType);
      const offsetSize = flag ? [0, 0.01] : [0, 0];
      const centerPoint = this.getPoint(pointItem, offsetSize);
      mapMain[this.mapType].map.centerAndZoom(centerPoint, flag ? 15 : null);
    },
    closeInfoWindow() {
      this.onceInfoWindow && this.onceInfoWindow.close();
      this.onceInfoWindow = null;
    },
    // 模拟撒点
    simulatorPoint() {
      let simuPoint = new NPMapLib.Layers.OverlayLayer("sampleGraph");
      let iconIndex = 0;
      for (let key in this.kmeanData) {
        iconIndex++;
        this.kmeanData[key].forEach((item, index) => {
          let childPoint = new NPMapLib.Layers.OverlayLayer("sampleGraph");
          let imgUrls = "",
            sizes = null,
            icons = null;
          imgUrls = require(`@/assets/img/map/new/icon_loca${
            (iconIndex % 9) + 1
          }.png`);
          sizes = new NPMapLib.Geometry.Size(20, 20);
          let points = {
            lon: item[0],
            lat: item[1],
          };
          let ks = points.lon + "_" + points.lat;
          let markers = new NPMapLib.Symbols.Marker(
            new NPMapLib.Geometry.Point(points.lon, points.lat)
          );
          icons = new NPMapLib.Symbols.Icon(imgUrls, sizes);
          icons.setAnchor(
            new NPMapLib.Geometry.Size(-sizes.width / 2, -sizes.height)
          );
          markers.setIcon(icons);
          markers.k = ks;
          mapMain[this.mapType].map.addLayer(childPoint);
          childPoint.addOverlay(markers);
        });
        let imgUrl = "",
          size = null,
          icon = null;
        size = new NPMapLib.Geometry.Size(40, 50);
        let point = key.split(",");
        let location = {
          lon: Number(point[0]),
          lat: Number(point[1]),
        };
        let k = location.lon + "_" + location.lat;
        let marker = new NPMapLib.Symbols.Marker(
          new NPMapLib.Geometry.Point(location.lon, location.lat)
        );
        imgUrl = require(`@/assets/img/map/red-position.png`);
        icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        marker.k = k;
        mapMain[this.mapType].map.addLayer(simuPoint);
        simuPoint.addOverlay(marker);
      }
    },
    // 由案到人范围
    /**
     * list: 数据
     * radius： 直径
     * name： 图层名
     */
    pointScope(list, radius, name, centerPointName) {
      let basicPoint = new NPMapLib.Layers.OverlayLayer(name);
      let basicCenterPoint = new NPMapLib.Layers.OverlayLayer(centerPointName);
      mapMain[this.mapType].map.addLayer(basicPoint);
      mapMain[this.mapType].map.addLayer(basicCenterPoint);
      basicCenterPoint.setZIndex(610);
      list.forEach((item, index) => {
        let center = new NPMapLib.Geometry.Point(
          item.lon || item.geoPoint.lon,
          item.lat || item.geoPoint.lat
        );
        console.log(center, "center");
        let circle = new NPMapLib.Geometry.Circle(center, radius, {
          color: "#F29F4C", //颜色
          fillColor: "rgba(242, 159, 76, 0.1)", //填充颜色
          weight: 2, //宽度，以像素为单位
          opacity: 1, //透明度，取值范围0 - 1
          fillOpacity: 0.5, //填充的透明度，取值范围0 - 1
        });
        circle.isEnableEdit = true;
        circle.isEnableContextMenu = true;
        // mapMain[this.mapType].map.addOverlay(circle)
        basicPoint.addOverlay(circle);
        // basicPoint.setZIndex(601);
        this.casePoint(center, item.type, basicCenterPoint);
      });
    },
    // 由案到人撒点
    casePoint(point, type = 1, basicPoint) {
      let size = new NPMapLib.Geometry.Size(50, 57);
      let marker = new NPMapLib.Symbols.Marker(point);
      let url = require(`@/assets/img/model/icon/map_${
        type == 1 ? "alarm" : "case"
      }_active.png`);
      let icon = new NPMapLib.Symbols.Icon(url, size);
      icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height));
      marker.setIcon(icon);
      // marker.k = k;
      basicPoint.addOverlay(marker);
    },
    // 由案到人详情撒点
    caseDetailsPoint(list, name) {
      if (!list || list.length === 0) {
        return false;
      }
      let boxPoint = new NPMapLib.Layers.OverlayLayer(name);
      mapMain[this.mapType].map.addLayer(boxPoint);
      let points = [];
      let mapList = list;
      mapList.forEach((item) => {
        if (
          (item.lon || item.geoPoint.lon) &&
          (item.lat || item.geoPoint.lat)
        ) {
          points.push(
            new NPMapLib.Geometry.Point(
              item.lon || item.geoPoint.lon,
              item.lat || item.geoPoint.lat
            )
          );
        }
      });
      //多段线
      var polyline = new NPMapLib.Geometry.Polyline(points, {
        color: "red", //颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        lineStyle: NPMapLib.LINE_TYPE_SOLID, //样式
      });
      polyline.isEnableEdit = true;
      boxPoint.addOverlay(polyline);
      list.forEach((item, index) => {
        let point = new NPMapLib.Geometry.Point(
          item.lon || item.geoPoint.lon,
          item.lat || item.geoPoint.lat
        );
        let size = new NPMapLib.Geometry.Size(35, 35);

        let url = require(`@/assets/img/map/trajectory-red.png`);
        let icon = new NPMapLib.Symbols.Icon(url, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );

        // 文本标记
        let label = new NPMapLib.Symbols.Label(`${index + 1}`);
        label.setOffset(new NPMapLib.Geometry.Size(-1, 22));
        label.setStyle({
          fontSize: 12, //文字大小
          fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
          color: "#EA4A36", //文字前景色
          align: "cm", //对方方式
          isBold: true, //是否粗体
        });

        let marker = new NPMapLib.Symbols.Marker(point);
        marker.setIcon(icon);
        marker.setLabel(label);
        // marker.k = k;
        boxPoint.addOverlay(marker);
      });
      return boxPoint;
    },
    // 清空点位
    resetMarker() {
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].map.removeOverlay(this.lineLocation);
        if (this.lineLocation) {
          this.lineLocation.removeAllOverlays();
        }
        this.lineLocation = null;
        this.resetMarkerRight();
      }
    },
    resetMarkerRight() {
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].map.removeOverlay(this.secLinePoint);
        mapMain[this.mapType].map.removeOverlay(this.coinPoint);
        if (this.secLinePoint) {
          this.secLinePoint.removeAllOverlays();
          this.coinPoint.removeAllOverlays();
        }
        this.secLinePoint = null;
        this.coinPoint = null;
      }
    },
    resetPointMarker() {
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].map.removeOverlay(this.dotimg);
        if (this.dotimg) {
          this.dotimg.removeAllOverlays();
        }
        this.dotimg = null;
      }
    },
    resetCollMarker() {
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].map.removeOverlay(this.dotColl);
        if (this.dotColl) {
          this.dotColl.removeAllOverlays();
        }
        this.dotColl = null;
      }
    },
    resetMarkerTrack() {
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].map.removeOverlay(this.lineTrack);
        if (this.lineTrack) {
          this.lineTrack.removeAllOverlays();
        }
        this.lineTrack = null;
      }
    },
    // 显示隐藏
    coverageShowOrHide(name, type) {
      let chartName = mapMain[this.mapType].map.getLayerByName(name);
      if (chartName) {
        this.closeInfoWindow();
        if (type) {
          chartName.show();
        } else {
          chartName.hide();
        }
      }
    },
    /**
     * 与resetMarkerbasicPoint方法想通 兼顾写法，后期可去除下一个
     * 默认全删
     * list:Array, 需要删除的图层列表
     * getLayerByName(): 根据name获取图层
     * getAllLayers()：获取全部图层
     */
    resetMarkerPoint(data) {
      if (mapMain[this.mapType]) {
        if (data.length > 0) {
          //特定删除 getLayerByName
          data.forEach((item) => {
            let getCoverage = mapMain[this.mapType].map.getLayerByName(item);
            mapMain[this.mapType].map.removeOverlay(getCoverage);
            if (getCoverage) {
              getCoverage.removeAllOverlays();
            }
          });
        } else {
          // 全部删除
          let list = mapMain[this.mapType].map.getAllLayers();
          list.forEach((item) => {
            mapMain[this.mapType].map.removeOverlay(item);
            if (item) {
              item.removeAllOverlays();
            }
          });
        }
        this.closeInfoWindow();
      }
    },
    resetMarkerbasicPoint() {
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].map.removeOverlay(this.basicPoint);
        if (this.basicPoint) {
          this.basicPoint.removeAllOverlays();
        }
        this.basicPoint = null;
      }
    },
    // 框选
    /**
     * drawType: 框选形状
     * moreShow: 是否展示多个框选 false否 true 是
     * index: 条件索引
     * style: 框选框样式
     * modalShow: 是否显示框选结果弹框
     */
    //  frameSelectionMovebox
    selectDraw(
      drawType = "selectCircle",
      moreShow = false,
      index = 0,
      style,
      modalShow = true
    ) {
      let mapStyle = {};
      if (!moreShow) {
        this.clearDraw();
        mapStyle = this.drawStyle;
      } else {
        this.clearModalBox();
        mapStyle = style;
      }
      this.drawComplete = false;
      this.conditionIndex = index;
      // this.boxPoint = new NPMapLib.Layers.OverlayLayer("聚合图层");
      // mapMain[this.mapType].map.addLayer(this.boxPoint);
      mapMain[this.mapType][drawType](
        (points, position, extent) => {
          // 初次款框选点位(根据不同类型获取相对应设备)
          //  人脸 => 人脸卡口 => Camera_Face
          //  车辆 => 车辆卡口 => Camera_Vehicle
          //  wifi => wifi设备 => Camera_Wifi
          //  rfid => RFID设备 => Camera_RFID
          //  电围 => 点位设备 => Camera_Electric
          if (this.isEleCrash) {
            // 感知碰撞设备筛选
            this.selectDeviceList = points.filter((item) => {
              if (this.crashType.includes(item.LayerType)) {
                return item;
              }
            });
          } else {
            if (this.crashType) {
              this.selectDeviceList = points.filter((item) => {
                if (
                  item.LayerType === this.crashType ||
                  this.crashType.includes(item.LayerType)
                ) {
                  console.log(item, "item");
                  return item;
                }
              });
            } else {
              this.selectDeviceList = points;
            }
          }
          // if (this.crashType) {
          //   this.selectDeviceList = points.filter((item) => {
          //     if (
          //       item.LayerType === this.crashType ||
          //       this.crashType.includes(item.LayerType)
          //     ) {
          //       console.log(item, "item");
          //       return item;
          //     }
          //   });
          // } else {
          //   this.selectDeviceList = points;
          // }
          const { bottom, left, right, top } = extent;
          // 组装框选数组数据，方便后面点击选中
          let selectPointsItem = {
            bottom,
            left,
            right,
            top,
            points: position.points,
            center: position.center,
            devicePoints: points,
          };
          if (this.selectPoints.length > 0) {
            this.selectPoints.forEach((e) => {
              if (
                (e.bottom !== bottom && e.left !== left && e.right !== right,
                e.top !== top)
              ) {
                this.selectPoints.push(selectPointsItem);
              }
            });
          } else {
            this.selectPoints.push(selectPointsItem);
          }
          // 绘制图形完成
          this.drawComplete = true;
          if (!modalShow) {
            this.$emit("selectDrawPoints", this.selectDeviceList);
            return;
          }
          if (points.length == 0) {
            return;
          }
          let crashModule = this.selectionBox ? "crashModule" : "";
          if (
            drawType == "selectCircle" ||
            drawType == "selectDrawCircleByDiameter"
          ) {
            const {
              ne: { lat, lon },
            } = extent;
            // 打开弹框
            this.selectionResultItem(
              { lat, lon },
              position.center,
              crashModule
            );
          } else {
            const positionItem = this.calculationLocation(
              drawType,
              selectPointsItem.points
            );
            this.selectionResultItem(
              positionItem,
              position.center,
              crashModule
            );
          }
          if (moreShow) {
            // this.$emit('gettingData', this.selectDeviceList, index)
          }
        },
        (extent, position, type) => {
          this.conditionIndex = index; //获取当前操作条件的索引
          console.log(type, "点击右键");
          selectAloneWindow.forEach((row) => {
            row.close();
          });
          let selectPointsItem = {};
          const { bottom, left, right, top } = extent;

          let x = extent.screenX;
          let y = extent.screenY;
          let geoPoint = {
            lat: position.lat,
            lon: position.lon,
          };
          this.selectPoints.forEach((e) => {
            if (
              (e.bottom == bottom && e.left == left && e.right == right,
              e.top == top)
            ) {
              selectPointsItem = { ...e };
            }
          });
          if (!modalShow) {
            this.$emit("selectDrawPoints", this.selectDeviceList);
            return;
          }
          let crashModule = this.selectionBox ? "crashModule" : "";
          if (
            drawType !== "selectCircle" &&
            drawType !== "selectDrawCircleByDiameter"
          ) {
            const positionItem = this.calculationLocation(
              drawType,
              selectPointsItem.points
            );
            this.selectionResultItem(
              positionItem,
              selectPointsItem.center,
              crashModule
            );
          } else {
            // 圆形取回东西经纬度
            const {
              ne: { lat, lon },
            } = extent;
            // 打开弹框
            this.selectionResultItem(
              { lat, lon },
              selectPointsItem.center,
              crashModule
            );
          }
          if (type) {
            this.deleResultItem(geoPoint, selectPointsItem.center);
          }
          if (this.crashType) {
            this.selectDeviceList = selectPointsItem.devicePoints.filter(
              (item) => {
                if (item.LayerType == this.crashType) {
                  return item;
                }
              }
            );
          } else {
            this.selectDeviceList = [...selectPointsItem.devicePoints];
          }
        },
        false,
        { ...mapStyle },
        index
      );
    },
    // 删除按钮弹出框
    // 框选弹框
    deleResultItem(pointItem, centerPoint) {
      this.closeSelectWindowDom();
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        pointItem.lat || pointItem.geoPoint.lat
      );
      // mapMain.map.centerAndZoom(point, 17)
      mapMain[this.mapType].map.setCenter(centerPoint);
      const opts = {
        width: 74, // 信息窗宽度
        height: 32, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(0, 0), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      const dom = this.$refs.deleDraw.$el;
      infoWindow.setContentDom(dom);
      mapMain[this.mapType].map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      selectAloneWindow.push(infoWindow);
    },
    // 碰撞撒点
    handlerCollision(points) {
      if (!points || points.length === 0) {
        return false;
      }
      // 判断某坐标是否多个
      let bothcoord = this.repetition(points);
      let markers = [];
      let temp = {};
      let multipleIcon = {};
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        item.iconType = true; //暂时需要
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let k = item.lon + "_" + item.lat;
        temp[k] == null ? (temp[k] = 0) : temp[k]++;
        let imgUrl = "",
          icon = null,
          iconbg = null,
          size = null;

        if (item.iconType) {
          //人像or车辆
          size = new NPMapLib.Geometry.Size(40, 40);
        } else {
          size = new NPMapLib.Geometry.Size(35, 35);
        }
        let marker = new NPMapLib.Symbols.Marker(item);
        // 用来判断当前点位上是否有多个图片
        if (multipleIcon[k]) {
          multipleIcon[k] = multipleIcon[k] + 1;
        } else {
          multipleIcon[k] = 1;
        }
        // 多数同一点、 一点一数
        this.dotColl = new NPMapLib.Layers.OverlayLayer(this.crashType);
        if (item.iconType) {
          // 人像or车辆
          imgUrl = item.traitImg;
        }
        // 设置图片
        icon = new NPMapLib.Symbols.Icon(this.$imgProxyToHttps(imgUrl), size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        marker.k = k;
        mapMain[this.mapType].map.addLayer(this.dotColl);
        this.dotColl.addOverlay(marker);
        let markerBg = new NPMapLib.Symbols.Marker(item);

        if (item.iconType) {
          if (!bothcoord.both[k] && multipleIcon[k] == 1) {
            //单个
            let imgSize = new NPMapLib.Geometry.Size(44, 52);
            let imgTitle = item.active ? "blue" : "unRed";
            imgUrl = require(`@/assets/img/map/record_${imgTitle}.png`);
            iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize);
            iconbg.setAnchor(
              new NPMapLib.Geometry.Size(-size.width / 2 - 2, -size.height - 2)
            );
            markerBg.setIcon(iconbg);
            mapMain[this.mapType].map.addLayer(this.dotColl);
            this.dotColl.addOverlay(markerBg);
          } else if (multipleIcon[k] >= 2) {
            //多个
            if (bothcoord.single[k] == multipleIcon[k]) {
              imgUrl = require(`@/assets/img/map/moreRecord_${
                item.active ? "blue" : "unRed"
              }.png`);
              let moreSize = new NPMapLib.Geometry.Size(52, 56);
              iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize);
              iconbg.setAnchor(
                new NPMapLib.Geometry.Size(
                  -size.width / 2 - 7,
                  -size.height - 6
                )
              );
              if (multipleIcon[k] >= 2) {
                multipleIcon[k] = multipleIcon[k] + 1;
              }
              markerBg.setIcon(iconbg);
              mapMain[this.mapType].map.addLayer(this.dotColl);
              this.dotColl.addOverlay(markerBg);
            }
          }
        }
        this.dotColl.setZIndex(600);
        markerBg.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
          this.selectItem(this.points[index], this.sectionName, true, true);
          this.$emit("chooseMapItem", index);
        });
        marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {});
        markers.push(marker);
      }
      this.markers = markers;
    },
    // 删除单一框
    handleDele(index = -1) {
      if (index > -1) {
        mapMain[this.mapType].clearModelDraw(index);
      } else {
        mapMain[this.mapType].clearModelDraw(this.conditionIndex);
      }
      this.closeSelectWindowDom();
      selectAloneWindow.forEach((row) => {
        row.close();
      });
      let indexes = index > -1 ? index : this.conditionIndex;
      this.$emit("clearList", indexes);
    },
    clearAll(val) {
      val.map((item, index) => {
        mapMain[this.mapType].clearModelDraw(index);
      });
      this.closeAllInfoWindow();
      this.closeSelectWindowDom();
    },
    // 清除框选
    // clearDraw() {
    //     mapMain[this.mapType].clearDraw();
    //     this.closeAllInfoWindow();
    //     this.closeSelectWindowDom()
    // },
    // 清除弹出框
    clearModalBox() {
      this.closeAllInfoWindow();
      this.closeSelectWindowDom();
    },
    // 时空碰撞清楚框选
    clearDraw() {
      mapMain[this.mapType].clearModelDraw(this.conditionIndex);
      if (this.conditionIndex < 0) {
        mapMain[this.mapType].clearDraw();
      }
      this.closeAllInfoWindow();
      this.closeSelectWindowDom();
    },
    // 底部操作栏
    // 取消框选-地图可移动
    cancelDraw() {
      mapMain[this.mapType].cancelDraw();
    },
    // 关闭 底部操作栏
    closeBottomTool() {
      this.clearDraw();
      this.$emit("closeMapTool");
    },
    // 关闭框选弹框
    closeSelectWindowDom() {
      selectWindowArr.forEach((row) => {
        row.close();
      });
    },
    // 计算区域弹框位置
    calculationLocation(drawType, points) {
      if (drawType === "selectPolygon") {
        return points.reduce((prev, current) =>
          prev.lat < current.lat
            ? prev
            : current && prev.lon > current.lon
            ? prev
            : current
        );
      } else if (drawType === "selectRectangle") {
        return points.reduce((prev, current) =>
          prev.lat > current.lat
            ? prev
            : current && prev.lon > current.lon
            ? prev
            : current
        );
      }
    },
    // 用于对背景图渲染做判断
    repetition(item) {
      let single = {}; //计算每个出现的次数
      let both = {}; //有重复的数据
      let singleSerial = {};
      item.forEach((item, index) => {
        let k =
          (item.lon || item.geoPoint.lon) +
          "_" +
          (item.lat || item.geoPoint.lat);
        if (!single[k]) {
          single[k] = 1;
          singleSerial[k] = [index];
        } else {
          single[k] = single[k] += 1;
          singleSerial[k].push(index);
        }
        if (single[k] > 1) {
          both[k] = true;
        }
      });
      return { single, both, singleSerial };
    },
    // 点击图层展示弹框,isMapClick 地图上点击不展示左右切换详情
    selectItem(pointItem, sectionName, distance = false, isAprink = "") {
      if (isAprink == "track") {
        // 点击列表后 切换地图显示图片
        this.trackLine([pointItem], "", sectionName);
      } else if (isAprink == "peer") {
        // 同行的点击列表
        this.peerPoint([pointItem]);
      }
      // pointItem.currentClickIndex = this.currentClickIndex
      // this.isMapClick = isMapClick
      this.closeAllInfoWindow();
      this.sectionMenuName = !!sectionName ? sectionName : sectionMenuName;
      // 显示之前先清除其他提示框
      const { sectionMenuName } = this;
      // const { pointLon, pointLat, offsetWidth, offsetHeight } = this.offsetMaps[sectionMenuName]
      // const point = new NPMapLib.Geometry.Point(+pointItem.lon + pointLon, +pointItem.lat + pointLat)
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        pointItem.lat || pointItem.geoPoint.lat
      );
      // 获取不同弹框位置偏移量
      mapMain[this.mapType].map.centerAndZoom(
        new NPMapLib.Geometry.Point(
          pointItem.lon || pointItem.geoPoint.lon,
          (pointItem.lat || pointItem.geoPoint.lat) + 0.0025
        ),
        17
      );
      // this.mapDomData = this.allCameraList[0]
      this.$nextTick(() => {
        const dom = this.$refs[this.sectionMenuName].$el;
        const offsetObject = this.handleOffset();
        let left = distance
          ? -offsetObject.offsetLeft + 9
          : -offsetObject.offsetLeft;
        let top = distance
          ? -offsetObject.offsetTop + 25
          : -offsetObject.offsetTop - 5;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: false, // 默认true, 窗口大小是否自适应
          width: 900,
          // paddingForPopups: NPMapLib.Geometry.Extent, // 信息窗自动弹回后，距离四边的值。isAdaptation为true时，该设置有效
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          // useDomStyle: true,
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain[this.mapType].map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        infoWindow.show();
        infoWindowArr.push(infoWindow);
      });
    },
    handleOffset() {
      // 获取不到这个地图dom动态的offsetWidth（未知原因）
      // 暂处理为获取1920分辨率下的宽高
      // 列表点击 - 人脸和车辆宽高840,600
      // 列表点击 - 普通设备的详情mapdom宽高  - 490,335
      // 地图上点击的mapdom宽高 950,600
      const stragetyOffset = {
        face: { width: 840, height: 600 },
        vehicle: { width: 840, height: 600 },
        capture: { width: 840, height: 480 },
        faceMap: { width: 840, height: 600 },
        vehicleMap: { width: 840, height: 600 },
        trackmodal: { width: 840, height: 600 },
        device: { width: 950, height: 600 },
        default: { width: 490, height: 335 },
      };
      let width =
        this.sectionMenuName in stragetyOffset
          ? stragetyOffset[this.sectionMenuName].width
          : stragetyOffset.default.width;
      let height =
        this.sectionMenuName in stragetyOffset
          ? stragetyOffset[this.sectionMenuName].height
          : stragetyOffset.default.height;
      let htmlFontSize = window.getComputedStyle(
        window.document.documentElement
      )["font-size"];
      let offsetLeft = (((width + 20) / 192) * parseFloat(htmlFontSize)) / 2;
      let offsetTop = ((height + 60) / 192) * parseFloat(htmlFontSize);
      return {
        offsetLeft,
        offsetTop,
      };
    },
    // 关闭多个弹框
    closeAllInfoWindow() {
      infoWindowArr.forEach((row) => {
        row.close();
      });
      infoWindowArr = [];
    },
    //关闭普通搜索-弹框
    closeMapDom() {
      this.closeAllInfoWindow();
      this.$emit("chooseMapItem", -1);
      // 重置点击同行的索引
      this.setPeerData({
        peerIndex: -1,
        type: "",
      });
    },
    // 取消框选
    handleBoxSelect() {
      this.closeSelectWindowDom();
      mapMain[this.mapType].clearModelDraw(this.conditionIndex);
    },
    // 保存框选数据
    handleSaveSelect(data) {
      this.closeSelectWindowDom();
      this.$emit("selectData", data);
    },
    // 蚌埠碰撞撒点
    handlerCollision2(points) {
      if (!points || points.length === 0) {
        return false;
      }
      if (dotColl) {
        dotColl.removeAllOverlays();
      } else {
        dotColl = new NPMapLib.Layers.OverlayLayer(this.crashType);
        mapMain[this.mapType].map.addLayer(dotColl);
      }
      dotColl.setZIndex(800);
      let markers = [];
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let imgUrl = "",
          icon = null,
          size = null,
          label = null;
        size = new NPMapLib.Geometry.Size(35, 35);
        let marker = new NPMapLib.Symbols.Marker(item);
        imgUrl = require(`@/assets/img/map/trajectory-${
          item.active ? "blue" : "red"
        }.png`);
        // 文本标记
        label = new NPMapLib.Symbols.Label(
          `${item.recordNum ? item.recordNum : index + 1}`
        );
        label.setOffset(new NPMapLib.Geometry.Size(-0.5, 23));
        label.setStyle({
          fontSize: 14, //文字大小
          fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
          color: !item.active ? "#EA4A36" : "#2C86F8", //文字前景色
          align: "cm", //对方方式
          isBold: true, //是否粗体
        });
        marker.setLabel(label);
        // 设置图片
        icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        dotColl.addOverlay(marker);
        markers.push(marker);
      }
      this.markers = markers;
      if (!!markers.length) {
        let clickFn = (marker) => {
          this.$emit("chooseMapItemObj", marker._position);
        };
        for (let index = markers.length - 1; index >= 0; index--) {
          markers[index].addEventListener(NPMapLib.MARKER_EVENT_CLICK, clickFn);
        }
      }
    },
    handlerTrackPoints(points) {
      if (!points || points.length === 0) {
        return false;
      }
      if (dotColl) {
        dotColl.removeAllOverlays();
      } else {
        dotColl = new NPMapLib.Layers.OverlayLayer(this.crashType);
        mapMain[this.mapType].map.addLayer(dotColl);
      }
      dotColl.setZIndex(800);
      let markers = [];
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let imgUrl = "",
          icon = null,
          size = null,
          label = null;
        size = new NPMapLib.Geometry.Size(35, 35);
        let marker = new NPMapLib.Symbols.Marker(item);
        imgUrl = require(`@/assets/img/map/trajectory-${
          item.active ? "blue" : "red"
        }.png`);
        // 文本标记
        label = new NPMapLib.Symbols.Label(
          `${item.recordNum ? item.recordNum : index + 1}`
        );
        label.setOffset(new NPMapLib.Geometry.Size(-0.5, 23));
        label.setStyle({
          fontSize: 14, //文字大小
          fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
          color: !item.active ? "#EA4A36" : "#2C86F8", //文字前景色
          align: "cm", //对方方式
          isBold: true, //是否粗体
        });
        marker.setLabel(label);
        // 设置图片
        icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        marker.setIcon(icon);
        dotColl.addOverlay(marker);
        markers.push(marker);
      }
      this.markers = markers;
      if (!!markers.length) {
        let clickFn = (marker, index) => {
          this.$emit("chooseMapItem", index);
        };
        for (let index = markers.length - 1; index >= 0; index--) {
          markers[index].addEventListener(
            NPMapLib.MARKER_EVENT_CLICK,
            function (event) {
              clickFn(event, index);
            }
          );
        }
      }
      setTimeout(() => {
        this.rePosition(points);
      }, 200);
    },
    //轨迹回到可视区域
    rePosition(arrList) {
      let minLat, minLon, maxLon, maxLat, sortLat, sortLon;
      if (!arrList || arrList.length === 0) {
        return false;
      }
      sortLat = arrList.map((e) => e.lat);
      sortLon = arrList.map((e) => e.lon);
      minLat = Math.min(...sortLat);
      maxLat = Math.max(...sortLat);
      minLon = Math.min(...sortLon);
      maxLon = Math.max(...sortLon);
      let dLat = maxLat - minLat;
      let dLon = maxLon - minLon;
      let extent = new NPMapLib.Geometry.Extent(
        minLon - dLon * 0.1,
        minLat - dLat * 0.1,
        maxLon + dLon * 0.1,
        maxLat + dLat * 0.1
      );
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].zoomToExtend(extent);
      }
    },
    clearModelDraw(index) {
      mapMain[this.mapType].clearModelDraw(index);
    },
  },
};
</script>

<style lang="less" scoped>
.map-boxs {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }

  .dianji {
    position: absolute;
    top: 0;
    font-size: 20px;
  }

  .dianji2 {
    position: absolute;
    top: 20px;
    font-size: 20px;
  }
}

/deep/ #npgis_GroupDiv {
  overflow: inherit !important;
}
</style>
