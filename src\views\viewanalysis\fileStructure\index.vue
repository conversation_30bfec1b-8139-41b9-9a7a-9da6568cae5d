<template>
  <div class="container">
    <!-- 查询 -->
    <Search ref="search" :subTaskType="subTaskType" @searchForm="searchForm" />
    <div class="table-container">
      <div class="data-above">
        <Button size="small" @click="handleAdd">
          <ui-icon type="jia" color="#2C86F8"></ui-icon>
          新增任务
        </Button>
        <Button class="mr" @click="handleSort" size="small">
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          创建时间排序
        </Button>
        <Button size="small" @click="handleDelJobs">
          <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
          批量删除
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-expand="handleExpand"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #totalTask="{ row }">
            <div>总数：{{ row.totalTask }}</div>
          </template>
          <template #totalFileSize="{ row }">
            <div>
              {{ row.totalFileSize | fileSizeFormat }}/{{
                row.totalDuration | formatDuring
              }}
            </div>
          </template>
          <template #resolution="{}">
            <div>--</div>
          </template>
          <template #taskStatus="{}">
            <div>--</div>
          </template>
          <template #executeTime="{}">
            <div>--</div>
          </template>
          <template #createTime="{ row }">
            <div>{{ row.createTime | timeFormat }}</div>
          </template>
          <template #baseTime="{}">
            <div>--</div>
          </template>
          <template #applyStatus="{ row }">
            <div>
              {{
                row.applyStatus == 1
                  ? "通过"
                  : row.applyStatus == 2
                  ? "驳回"
                  : "未审核"
              }}
            </div>
          </template>
          <template #opreate="{ row }">
            <div class="opreate">
              <div class="tools">
                <Poptip trigger="hover" placement="left-start">
                  <i class="iconfont icon-gengduo"></i>
                  <div class="mark-poptip" slot="content">
                    <p
                      :class="row.allowStop ? '' : 'disabled'"
                      @click="handleStop(row)"
                    >
                      <i class="iconfont icon-pause"></i>停止
                    </p>
                    <p @click="handleEdit(row)">
                      <i class="iconfont icon-bianji"></i>编辑
                    </p>
                    <p @click="handleSearch(row)">
                      <i class="iconfont icon-gaojisousuo"></i>资源检索
                    </p>
                    <p @click="mapLoaction(row)">
                      <i class="iconfont icon-dongtai-shangtudaohang"></i
                      >地图定位
                    </p>
                    <p @click="handleDownloadFile(row)">
                      <i class="iconfont icon-download"></i>文件下载
                    </p>
                    <p @click="handleDel(row)">
                      <i class="iconfont icon-shanchu1"></i>删除
                    </p>
                  </div>
                </Poptip>
              </div>
            </div>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <fileAddModal
      v-model="isShowAdd"
      :taskId="subTaskId"
      :subDeviceId="subDeviceId"
      @updated="jobUpdated"
    ></fileAddModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import Search from "../components/search.vue";
import expandRow from "@/components/ui-table-expandRow/expandRow.vue";
import fileAddModal from "../components/file-add-modal.vue";
import adjustPosition from "../components/adjustPosition.vue";
import {
  getStructureJobList,
  getStructureRealTaskList,
  delTask,
  delJob,
  stopJob,
  stopTask,
  restartTask,
  getFileInfos,
} from "@/api/viewAnalysis";
export default {
  name: "fileStructure",
  components: {
    Search,
    expandRow,
    fileAddModal,
    adjustPosition,
  },
  props: {},
  data() {
    return {
      subTaskType: "file",
      list: [],
      childList: [],
      loading: false,
      pageForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        structureJobType: 3,
        sort: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                taskList: this.childList[params.index],
                columns: this.childColumns,
                currentJob: this.list[params.index],
                subTaskType: this.subTaskType,
              },
              on: {
                rehandle: (val) => {
                  this.rehandle(val);
                },
                handleStop: (val) => {
                  this._stopTask(val.id);
                },
                handleEdit: (val) => {
                  this.subTaskId = val.structureJobId;
                  this.subDeviceId = val.deviceGbId;
                  this.isShowAdd = true;
                },
                handleSearch: (val, currentJob) => {
                  this.toDetailByTask(val, currentJob);
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDownloadFile: (val) => {
                  this.downLoadFile(val);
                },
                handleDel: (val, currentJob) => {
                  this.deleteTasks([val], currentJob);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "name", width: 150 },
        { title: "解析类型", slot: "totalTask", minWidth: 220 },
        { title: "大小/时长", slot: "totalFileSize", width: 180 },
        { title: "分辨率", slot: "resolution" },
        { title: "任务状态", slot: "taskStatus" },
        { title: "处理时长", slot: "executeTime" },
        { title: "创建时间", slot: "createTime" },
        { title: "校准时间", slot: "baseTime" },
        { title: "创建人", key: "creatorName" },
        { title: "审核状态", slot: "applyStatus" },
        { title: "操作", slot: "opreate", width: 100 },
      ],
      childColumns: [
        { title: "任务名称", slot: "fileName", width: 315, align: "center" },
        { title: "状态", slot: "vehicle", minWidth: 240 },
        { title: "大小/时长", slot: "fileSize", width: 180 },
        { title: "分辨率", slot: "resolution" },
        { title: "任务状态", slot: "progress" },
        { title: "处理时长", slot: "analysisTime" },
        { title: "创建时间", slot: "createTime" },
        { title: "校准时间", slot: "baseTime" },
        { title: "创建人" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {
    this.getDictStructData();
    this.getList();
    this.timer = setInterval(() => {
      let expandList = this.list.filter((v) => v._expanded);
      expandList.forEach((v) => {
        this.handleExpand(v, true);
      });
    }, 15000);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    ...mapActions({
      getDictStructData: "dictionary/getDictStructData",
    }),
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sort = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    // 查询列表
    getList() {
      var param = Object.assign(this.pageForm, this.params, {
        pageNumber: this.params.pageNumber - 1,
      });
      getStructureJobList(param).then((res) => {
        const { list, total } = res.data;
        this.list = list || [];
        this.total = total;
        this.selectedData = [];
      });
    },
    jobUpdated() {
      this.getList();
    },
    // 查询
    searchForm(form) {
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
        getStructureRealTaskList({
          structureJobId: row.jobId,
          structureJobType: this.params.structureJobType,
          sort: this.params.sort,
          pageNumber: 0,
          pageSize: 999,
        }).then((res) => {
          const { list } = res.data;
          this.$set(this.childList, index, list || []);
          if (!list.length) this.getList();
        });
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
    },
    handleEdit(row) {
      this.subTaskId = row.jobId;
      this.subDeviceId = "";
      this.isShowAdd = true;
    },
    handleStop(row) {
      if (!row.allowStop) return;
      this._stopJob(row.jobId);
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    handleDownloadFile(row) {
      this.downLoadFiles(row);
    },
    //批量视频下载
    downLoadFiles(record) {
      getFileInfos(record.jobId).then((res) => {
        let hasFileDownload = res.data.some(function (item) {
          return item.originalFilePfsPath;
        });

        if (hasFileDownload) {
          res.data.forEach(function (item) {
            let filePath = item.originalFilePfsPath;
            let fileName = item.fileName;
            Toolkits.ocxUpDownHttp(
              "pfs",
              "video",
              filePath,
              fileName,
              "",
              "",
              "",
              "",
              "",
              item.pfsIp
            );
          });
        } else {
          this.$Message.warning("当前没有可下载文件!");
        }
      });
    },
    //视频下载
    downLoadFile(record) {
      let filePath = record.originalFilePfsPath;
      let fileName;
      if (record.fileName) {
        fileName = record.fileName;
      } else {
        fileName = record.name;
      }
      let fileType = record.originalFileLocalPath.match(/\.\w+\b$/g);
      fileName = fileName + fileType[0];
      Toolkits.ocxUpDownHttp(
        "pfs",
        "video",
        filePath,
        fileName,
        "",
        "",
        "",
        "",
        "",
        record.pfsIp
      );
    },
    /**
     * 停止文件结构化任务
     */
    _stopJob(id) {
      stopJob(id).then((res) => {
        if (res) {
          this.$Message.success("视频文件结构化任务停止成功");
          this.getList();
        } else {
          this.$Message.error("视频文件结构化任务停止失败");
        }
      });
    },
    /**
     * 停止结构化子任务
     */
    _stopTask(ids) {
      stopTask(ids).then((res) => {
        if (res) {
          this.$Message.success("视频文件结构化任务停止成功");
          this.getList();
        } else {
          this.$Message.error("视频文件结构化任务停止失败");
        }
      });
    },
    rehandle(item) {
      restartTask(item.id)
        .then((res) => {
          if (res) {
            this.$Message.info("操作成功");
            this.getList();
          } else {
            this.$Message.error("操作失败");
          }
        })
        .catch((e) => {
          this.$Message.error("操作失败");
        });
    },
    toDetailByJob(row) {
      let param = {
        structureJobId: row.jobId,
        pageNumber: 0,
        pageSize: 1000,
        structureJobType: 3,
      };
      getStructureRealTaskList(param).then((res) => {
        const list = res.data.list;
        let startTimeArray = list.map((item) => {
          return !item.baseTime
            ? row.taskStartTime - item.duration
            : item.baseTime;
        });
        let endTimeArray = list.map((item) => {
          return !item.baseTime
            ? row.taskStartTime
            : item.baseTime + item.duration;
        });

        const { href } = this.$router.resolve({
          name: "viewParsingLibrary",
          query: {
            noMenu: 1,
            type: 3,
            jobId: row.jobId,
            startDate: Math.min.apply(null, startTimeArray),
            endDate: Math.max.apply(null, endTimeArray),
          },
        });
        window.open(href, "_blank");
      });
    },
    toDetailByTask(row, currentJob) {
      const { href } = this.$router.resolve({
        name: "viewParsingLibrary",
        query: {
          noMenu: 1,
          type: 3,
          taskId: JSON.stringify(
            row.id.split(",").map((v) => {
              return { id: v, name: row.name };
            })
          ),
          // 数据列表跳转至目标排查携带的时间参数值规则:
          // 情景一： 设置了文件的校准时间(基准时间)  开始时间 = 校准时间, 结束时间 = 校准时间 + 视频时长
          // 情景二： 未设置文件的校准时间(基准时间)  开始时间 = 任务执行时间 - 视频时长, 结束时间 = 任务执行时间
          startDate: !row.baseTime
            ? currentJob.taskStartTime - row.duration
            : row.baseTime,
          endDate: !row.baseTime
            ? currentJob.taskStartTime
            : row.baseTime + row.duration,
        },
      });
      window.open(href, "_blank");
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      var arrayP = [];
      item.devices &&
        item.devices.length > 0 &&
        item.devices.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: !!item["name"] ? item.name : null,
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      } else {
        this.$Message.error("该视频文件无点位信息，无法定位");
      }
    },
    onSelect(selection) {
      this.selectedData = selection;
    },
    onSelectCancel(selection) {
      this.selectedData = selection;
    },
    onSelectAll(selection) {
      this.selectedData = selection;
    },
    onSelectAllCancel() {
      this.selectedData = [];
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },
    //删除---删除任务&批量删除任务
    deleteJobs(data) {
      if (!data || !data.length) {
        this.$Message.warning("当前未选中任务");
        return;
      }
      const ids = data.map((item) => item.jobId).join(",");
      const names = data.map((item) => item.name).join(",");
      if (ids === "") {
        this.$Message.warning("当前未选中任务");
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `您确认删除【${names}】视频文件结构化任务吗?`,
        onOk: () => {
          delJob(ids).then((res) => {
            this.queryLog({
              muen: "视图解析",
              name: "文件结构化",
              type: "3",
              remark: `删除【${names}】文件结构化任务。`,
            });
            res
              ? this.$Message.success("任务删除成功")
              : this.$Message.success("任务删除失败");
            this.getList();
          });
        },
      });
    },
    //批量删除子任务
    deleteTasks(files, currentJob) {
      if (!files || !files.length) {
        this.$Message.warning("当前未选中任务");
        return;
      }
      var _this = this;
      var arr_fileId = files.map(function (item) {
        return item.id;
      });
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `您确认删除【${files[0].name}】视频文件结构化任务吗?`,
        onOk: () => {
          delTask(arr_fileId.join(",")).then((res) => {
            this.queryLog({
              muen: "视图解析",
              name: "文件结构化",
              type: "3",
              remark: `删除【${currentJob.name}】文件结构化任务的【${files[0].name}】视频文件。`,
            });
            res
              ? _this.$Message.success("任务删除成功")
              : _this.$Message.success("任务删除失败");
            _this.handleExpand(currentJob, true);
          });
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .data-above {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      button {
        margin-right: 10px;
      }
    }
    .table-content {
      flex: 1;
      .ui-table {
        height: 100%;
      }
    }
    .opreate {
      display: flex;
    }
    .tools {
      color: #2c86f8;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      margin-left: 5px;
      .icon-gengduo {
        transform: rotate(90deg);
        transition: 0.1s;
        display: inline-block;
      }
      p:hover {
        color: #2c86f8;
      }
      &:hover {
        background: #2c86f8;
        color: #fff;

        .icon-gengduo {
          transform: rotate(0deg);
          transition: 0.1s;
        }
        border-radius: 10px;
      }
      /deep/ .ivu-poptip-popper {
        min-width: 150px !important;
        width: 40px !important;
        height: auto;
      }
      /deep/.ivu-poptip-body {
        height: auto !important;
      }
      .mark-poptip {
        color: #000;
        cursor: pointer;
        text-align: left;
        /deep/ .ivu-icon-ios-add-circle-outline {
          font-weight: 600;
        }
      }
      .disabled {
        cursor: not-allowed;
        color: grey !important;
      }
    }
    /deep/td.ivu-table-expanded-cell {
      padding: 0 !important;
    }
  }
}
</style>
