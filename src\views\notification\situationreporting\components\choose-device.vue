<template>
  <ui-modal
    v-model="visible"
    :title="`请选择${modalTitle}`"
    class-name="choose-device-container"
    width="96rem"
    @onCancel="handleReset"
  >
    <underline-menu
      v-if="needOtherSearch"
      v-model="chooseMenu.chooseType"
      :data="chooseMenu.chooseTypeList"
      @on-change="changeChooseType"
      class="mb-sm"
    >
    </underline-menu>
    <div v-show="chooseMenu.chooseType === 'default'" class="content-wrap">
      <section class="left-content" v-show="chooseMenu.chooseType === 'default'">
        <ui-search-tree
          class="padding20 auto-fill"
          ref="uiSearchTree"
          placeholder="请输入组织机构名称或组织机构编码"
          show-checkbox
          check-strictly
          relation
          :tree-loading="treeLoading"
          :node-key="treeNodeKey"
          :tree-data="treeData"
          :default-props="defaultProps"
          :default-checked-keys="orgCodeList"
          @check="check"
        >
        </ui-search-tree>
      </section>
      <section class="right-content auto-fill">
        <div class="search-box content-title">
          <div class="search-top">
            <div class="search-header">
              <template v-if="chooseMenu.chooseType === 'default'">
                <slot name="search-header"></slot>
              </template>
            </div>
          </div>
          <div class="search-bottom mb-lg">
            <div v-if="needHandle" class="align-flex">
              <!-- <Checkbox class="checks mr-lg align-flex" v-model="isAll" @on-change="handleAllCheck"> 全选 </Checkbox> -->
              <Checkbox
                v-if="needExclude && needHandle"
                class="checks mr-lg align-flex"
                v-model="isExclude"
                @on-change="handleExcludeCheck"
              >
                排除
              </Checkbox>
              <RadioGroup v-if="isExclude" v-model="excludeType">
                <Radio label="0">全量{{ modalTitle }}排除选中{{ modalTitle }}</Radio>
                <Radio label="1">筛选结果排除选中{{ modalTitle }}</Radio>
              </RadioGroup>
            </div>
            <p class="devicenumtext font-color">
              已选择{{ modalTitle }}
              <span class="font-red">&nbsp;{{ isAll ? devTotal : chooseTableData.length }}&nbsp;</span>条
              <span v-if="chooseTableData.length && !isAll"
                >，
                <span class="preview" @click="detail"> 点击查看 </span>
              </span>
            </p>
          </div>
        </div>
        <ui-table
          reserveSelection
          class="ui-table auto-fill"
          :row-key="nodeKey"
          :table-columns="tableColumnsList"
          :table-data="leftTableData"
          :loading="loading"
          :is-all="isAll"
          :default-store-data="defaultStoreData"
          @storeSelectList="storeSelectList"
        >
          <template v-for="(item, index) in tableColumnsList" #[item.slot]="{ row }">
            <slot :name="item.slot" :row="row" :index="index">
              <span :key="'longitude' + index" v-if="item.slot === 'longitude'">
                {{ row.longitude | filterLngLat }}
              </span>
              <span :key="'latitude' + index" v-if="item.slot === 'latitude'">
                {{ row.latitude | filterLngLat }}
              </span>
              <div :key="'status' + index" v-if="item.slot === 'status'">
                <span
                  v-if="row.extendColumns"
                  :style="{
                    color: row.extendColumns.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
                  }"
                  >{{ row.extendColumns.phyStatusText }}
                </span>
              </div>
              <tags-more :key="'tagList' + index" v-if="item.slot === 'tagList' && row.tagList" :tag-list="row.tagList">
              </tags-more>
              <div :key="'resultText' + index" v-if="item.slot === 'resultText'">
                <span
                  :style="{
                    color: row.result === '1' ? '#0E8F0E' : '#BC3C19',
                  }"
                >
                  {{ row.resultText }}
                </span>
              </div>
            </slot>
          </template>
        </ui-table>
        <ui-page
          class="page menu-content-background"
          transfer
          :page-data="pageData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </section>
    </div>
    <div v-show="chooseMenu.chooseType != 'default'" class="importFile">
      <div v-show="TableFileData.length == 0" class="importFile-upload">
        <div>
          <Upload
            action="/ivdg-examination-service/examination/reportReview/importFile"
            ref="upload"
            class="inline"
            :show-upload-list="false"
            :headers="headers"
            :before-upload="beforeUpload"
            :on-success="importSuccess"
            :on-error="importError"
          >
            <Button type="primary" class="cyan-btn ml-sm" :loading="importLoading">
              <i class="icon-font icon-piliangdaoru-01 f-12"></i>
              <span class="vt-middle ml-sm">导入Excel文件</span>
            </Button>
          </Upload>
          <span class="hint" @click="exportModule">下载模板</span>
          <!-- @click="exportModule" -->
          <p class="remark">备注：无法导入资产库不存在或不在权限范围内的设备！</p>
        </div>
      </div>
      <div v-if="TableFileData.length > 0" class="file-table">
        <div class="table-hint">
          <p class="remark">成功导入{{ TableFileData.length }}条设备!</p>
          <!-- <p class="export" @click="handleImportfail">导出失败设备</p> -->
          <p class="import" @click="handleAnewImport">重新导入</p>
        </div>
        <ui-table
          reserveSelection
          class="ui-table auto-fill"
          :table-columns="tableFileColumns"
          :table-data="TableFileData"
          :loading="loading"
        >
          <template slot="sblwzt" slot-scope="{ row }">
            <span
              :style="{
                color: row.sblwzt === '1' ? '#0E8F0E' : '#BC3C19',
              }"
            >
              {{ row.sblwzt == 1 ? '可用' : '不可用' }}
            </span>
          </template>
          <template slot="action" slot-scope="{ row, index }">
            <span class="delete" @click="handleDelete(row, index)">移除</span>
          </template>
        </ui-table>
      </div>
    </div>
    <template #footer>
      <Button @click="handleReset" class="plr-30"> 取 消 </Button>
      <Button type="primary" @click="handleSubmit" class="plr-30" :loading="saveLoading"> 确 定 </Button>
    </template>
    <selected-preview-modal
      ref="SelectedPreviewModal"
      :modalTitle="modalTitle"
      :row-key="nodeKey"
      :selected-data="chooseTableData"
      @getSelectedList="getSelectedList"
    >
    </selected-preview-modal>
    <slot></slot>
  </ui-modal>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import taganalysis from '@/config/api/taganalysis';
import detectionResult from '@/config/api/detectionResult';
import { fileColumns } from '../situationreporting.js';
export default {
  props: {
    isAllowClose: {
      type: Boolean,
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    treeNodeKey: {
      default: 'orgCode',
    },
    nodeKey: {
      type: String,
      default: 'id',
    },
    tableColumns: {
      type: Array,
      default: () => {},
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
    // 是否需要全选排除
    needHandle: {
      type: Boolean,
      default: true,
    },
    // 全选按钮状态
    checkDeviceFlag: {
      type: [Number, String],
      default: '0',
    },
    // 默认选中
    selectedList: {
      type: Array,
      default: () => [],
    },
    // 搜索条件
    searchConditions: {
      type: Object,
      default: () => {},
    },
    //ui-modal title
    modalTitle: {
      default: '设备',
    },
    // 根据行政区域过滤组织机构
    regionCode: {
      type: String,
      default: '',
    },
    needExclude: {
      type: Boolean,
      default: false,
    }, // 是否需要排除
    needOtherSearch: {
      type: Boolean,
      default: false,
    }, // 是否需要其他方式搜索
    saveLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      orgUnWatch: null,
      loading: false,
      treeLoading: false,
      treeData: [],
      keyword: '',
      leftTableData: [],
      chooseTableData: [],
      chooseMenu: {
        chooseType: 'default',
        chooseTypeList: [
          {
            code: 'default',
            label: '设备库中选择',
          },
          {
            code: 'result',
            label: '导入文件',
          },
        ],
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      connectingOptions: {
        // 箭头样式
        width: '1.2%',
        height: '0.04rem',
        top: '50%',
        left: '13.6%',
      },
      isAll: false, // 全选
      isExclude: false, // 排除
      excludeType: '0', // 0: 全量设备排除选中设备;  1: 筛选结果排除选中设备
      visible: false,
      defaultStoreData: [], // 存储勾选的设备
      orgCodeList: [],
      devTotal: 0,
      // 按检测结果搜索
      taskList: [],
      indexList: [],
      checkTimeList: [],
      taskSchemeId: '',
      indexId: '',
      indexLoading: false,
      checkTimeLoading: false,
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      selectTree: {
        regionCode: '',
      },
      checkResultList: [
        {
          label: '合格',
          value: 1,
        },
        {
          label: '不合格',
          value: 2,
        },
        {
          label: '合格（人工）',
          value: 3,
        },
        {
          label: '不合格（人工）',
          value: 4,
        },
      ],
      unqualifiedList: [],
      tagList: [],
      searchResultData: {
        orgCode: '',
        regionCode: '',
        deviceId: '',
        deviceName: '',
        batchId: '',
        results: [],
        errorMessages: [],
        tagIds: [],
        pageNumber: 1,
        pageSize: 20,
      },
      resultColumnsList: [
        {
          type: 'selection',
          width: 50,
          fixed: 'left',
          align: 'center',
        },
        {
          title: '序号',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          key: 'regionName',
          align: 'left',
          tooltip: true,
        },
        { title: '设备状态', slot: 'status', width: 150 },
        {
          title: '设备标签',
          slot: 'tagList',
          width: 150,
          align: 'center',
        },
        {
          title: '检测结果',
          slot: 'resultText',
          align: 'left',
        },
        {
          title: '原因',
          key: 'message',
          align: 'left',
        },
      ],
      tableColumnsList: this.tableColumns,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importLoading: false,
      tableFileColumns: [],
      TableFileData: [],
    };
  },
  created() {
    this.orgUnWatch = this.$watch(
      'initialOrgList',
      (val) => {
        if (val && val.length) {
          this.setTreeData();
        }
      },
      { immediate: true },
    );
    this.copySearchDataMx(this.searchResultData);
    this.tableFileColumns = fileColumns;
  },
  mounted() {
    this.getListTaskSchemes();
    this.getTagList();
  },
  watch: {
    isAllowClose(val) {
      if (val) {
        this.visible = false;
      }
    },
    regionCode() {
      this.setTreeData();
    },
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
      setSource: 'common/setSource',
    }),
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagList = res.data.data.map((row) => {
          return {
            name: row.tagName,
            id: row.tagId,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 切换搜索后，取消当前正在进行的请求，否则会接口过慢返回数据赋值
     */
    changeChooseType(val) {
      this.leftTableData = [];
      this.cancelSource.cancel && this.cancelSource.cancel('取消请求');
      this.setSource(this.$http.CancelToken.source());
      this.reset();
      this.isAll = false;
      this.isExclude = false;
      this.$nextTick(() => {
        if (val === 'default') {
          this.tableColumnsList = this.tableColumns;
          this.getList();
        } else {
          this.TableFileData = [];
          this.chechResultReset();
        }
      });
    },
    async getListTaskSchemes() {
      try {
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.taskList = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    async changeIndex() {
      try {
        if (!this.indexId) return;
        this.checkTimeLoading = true;
        const {
          data: { data },
        } = await this.$http.get(governanceevaluation.getIndexBatchByTaskSchemeIdAndIndex, {
          params: {
            indexId: this.indexId,
            taskSchemeId: this.taskSchemeId,
          },
        });
        this.checkTimeList = data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.checkTimeLoading = false;
      }
    },
    async getList() {
      try {
        this.loading = true;
        let result = null;
        if (this.chooseMenu.chooseType === 'default') {
          result = await this.loadData(this.searchData);
        } else {
          if (!this.searchResultData.batchId) {
            this.$Message.warning('请选择检测时间');
            return;
          }
          const { data } = await this.$http.post(governanceevaluation.recordDevicePagelist, this.searchResultData);
          result = data;
        }
        this.leftTableData = result.data[this.listKey];

        this.pageData.totalCount = result.data.total;
        this.devTotal = this.pageData.totalCount;
        this.handleDataChecked();
      } catch (e) {
        console.log(e, 'err');
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.reset();
      this.getList();
    },
    async init() {
      this.visible = true;
      this.loading = true;
      await this.setDefaultSearch();
      // this.getTotal()
      await this.getList();
      this.defaultStoreData = this.selectedList.map((row) => {
        let obj = {};
        obj[this.nodeKey] = row;
        return obj;
      });
      this.chooseTableData = this.defaultStoreData.map((row) => row);
    },
    async setDefaultSearch() {
      // 默认选中的orgCode
      const initialOrgList = this.$util.common.jsonToArray(this.$util.common.deepCopy(this.treeData));
      if (this.searchConditions.orgCodeList && this.searchConditions.orgCodeList.length) {
        const defaultChecked = initialOrgList.filter((row) => {
          return this.searchConditions.orgCodeList.findIndex((rw) => rw === row[this.treeNodeKey]) !== -1;
        });
        this.orgCodeList = defaultChecked.map((row) => row.orgCode);
      } else {
        this.orgCodeList = initialOrgList.filter((row) => !row.disabled).map((row) => row.orgCode);
      }
      this.$nextTick(() => {
        // fixed element 选中禁用节点的子节点bug 需要树手动关联上下级勾选获取选中的数据
        this.searchData.orgCodeList = this.$refs.uiSearchTree.getCheckedKeys();
      });

      // 如果存在自定义搜索则赋值搜索条件
      if (this.searchConditions.customMode) {
        this.chooseMenu.chooseType = 'result';
        this.taskSchemeId = this.searchConditions.taskSchemeId;
        this.indexId = this.searchConditions.indexId;
        await this.changeIndex();
        await this.changeCheckTime();
        this.selectOrgTree.orgCode = this.searchConditions.orgCode;
        this.selectTree.regionCode = this.searchConditions.regionCode;
        Object.keys(this.searchConditions).forEach((key) => {
          if (this.searchResultData.hasOwnProperty(key)) {
            this.searchResultData[key] = this.searchConditions[key];
          }
        });
        this.tableColumnsList = this.resultColumnsList;
        this.searchConditions.tagIds.forEach((row) => {
          const tag = this.tagList.find((tag) => tag.id === row);
          !!tag && this.$set(tag, 'select', true);
        });
      } else {
        this.chooseMenu.chooseType = 'default';
        this.tableColumnsList = this.tableColumns;
      }
      this.reset();
      if (this.checkDeviceFlag === '0') {
        this.empty();
      } else if (this.checkDeviceFlag === '1' || this.checkDeviceFlag === '3') {
        this.isAll = false;
        this.isExclude = true;
        this.excludeType = this.checkDeviceFlag === '1' ? '0' : '1';
      } else if (this.checkDeviceFlag === '2') {
        this.isAll = true;
        this.isExclude = false;
      }
    },
    async getTotal() {
      try {
        const result = await this.loadData(this.searchData);
        this.devTotal = result.data.total;
      } catch (err) {
        console.log(err);
      }
    },
    reset() {
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.searchData.pageNumber = 1;
      this.searchResultData.pageNumber = 1;
      this.searchData.pageSize = 20;
      this.searchResultData.pageSize = 20;
    },
    chechResultReset() {
      this.$refs.orgTree.reset();
      this.selectTree.regionCode = '';
      this.taskSchemeId = '';
      this.indexId = '';
      this.leftTableData = [];
      this.reset();
      this.resetSearchDataMx(this.searchResultData);
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    removeOneDevice(row) {
      let deleteIndex = this.defaultStoreData.findIndex((item) => {
        return item.id === row.id;
      });
      this.defaultStoreData.splice(deleteIndex, 1);
    },
    // 全部移除
    removeAllDevice() {
      this.defaultStoreData = [];
      this.chooseTableData = [];
      this.handleDataChecked();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.searchResultData.pageNumber = val;
      this.pageData.pageNum = val;
      this.getList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchResultData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.searchResultData.pageSize = val;
      this.pageData.pageSize = val;
      this.getList();
    },
    check(data) {
      this.searchData.orgCodeList = data;
      // this.getTotal()
      this.$emit('getOrgCode', data);
    },
    // 全选
    handleAllCheck(val) {
      this.chooseTableData = [];
      if (val) {
        this.isExclude = false;
      } else {
        this.defaultStoreData = [];
      }
      this.handleDataChecked();
    },
    // 排除
    handleExcludeCheck(val) {
      if (val) {
        this.isAll = false;
      } else {
        this.defaultStoreData = [];
        this.chooseTableData = [];
      }
      this.handleDataChecked();
    },
    // 点击全选或者排除时，对表格复选框进行操作
    handleDataChecked() {
      this.leftTableData = this.leftTableData.map((item) => {
        this.$set(item, '_checked', this.isAll);
        this.$set(item, '_disabled', this.isAll);
        return item;
      });
    },
    storeSelectList(selection) {
      this.chooseTableData = selection;
      // this.defaultStoreData = selection
    },
    handleReset() {
      this.$nextTick(() => {
        this.visible = false;
      });
    },
    handleSubmit() {
      let list = [];
      if (this.chooseMenu.chooseType == 'default') {
        list = this.chooseTableData;
        console.log(this.chooseTableData, 'this.chooseTableData');
      } else {
        list = this.TableFileData;
        console.log(this.TableFileData, 'this.TableFileData');
      }
      this.$emit('getDeviceIdList', list);
      if (this.isAllowClose) {
        this.visible = false;
      }
      return;
      const selects = {
        selectedDevNum: this.selectedDevNum,
        chooseIds: this.chooseTableData.map((item) => item.id),
        chooseDeviceIds: this.chooseTableData.map((item) => item.deviceId),
        searchCustomData:
          this.chooseMenu.chooseType === 'default'
            ? null
            : { ...this.searchResultData, indexId: this.indexId, taskSchemeId: this.taskSchemeId, customMode: 2 },
      };
      // checkDeviceFlag: 不选（默认） 0，  全量排除 1 ，筛选排除3，  全选 2
      if (this.isAll) {
        selects.checkDeviceFlag = '2';
      } else if (this.isExclude) {
        selects.checkDeviceFlag = this.excludeType === '0' ? '1' : '3';
      } else {
        selects.checkDeviceFlag = '0';
      }
    },
    empty() {
      this.isAll = false;
      this.isExclude = false;
      this.excludeType = '0';
      this.defaultStoreData = []; // 表格选中数据取消勾选
      this.chooseTableData = [];
    },
    detail() {
      this.$refs.SelectedPreviewModal.init();
    },
    getSelectedList(data) {
      this.defaultStoreData = data;
      this.chooseTableData = data;
      this.getList();
    },
    async getOrgList(code) {
      try {
        this.treeLoading = true;
        const res = await this.$http.get(governanceevaluation.getOrglistByRegioncodeWithDataScope, {
          params: {
            regioncode: code,
          },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.treeLoading = false;
      }
    },
    async setTreeData() {
      try {
        let orgList = [];
        if (this.regionCode) {
          orgList = await this.getOrgList(this.regionCode);
        } else {
          orgList = this.initialOrgList;
        }
        const copyData = this.$util.common.deepCopy(orgList);
        this.treeData = this.$util.common.arrayToJson(copyData, 'id', 'parentId');
      } catch (err) {
        console.log(err);
      }
    },
    selectedOrgTree(val) {
      this.searchResultData.orgCode = val.orgCode;
    },
    selectedArea(area) {
      this.searchResultData.regionCode = area.regionCode;
    },
    async getUnqualifiedList() {
      try {
        if (!this.searchResultData.batchId) return;
        // 后端要求传入的参数 by Youyaohua
        const res = await this.$http.post(detectionResult.getVideoUnqualifiedInfo, {
          batchId: this.searchResultData.batchId,
          customParameters: {
            indexType: this.indexList.find((row) => row.indexId === this.indexId).indexType,
            model: 1,
          },
        });
        this.unqualifiedList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    changeCheckTime() {
      this.getUnqualifiedList();
    },
    selectInfo(infoList) {
      this.searchResultData.tagIds = infoList.map((item) => item.id);
      this.getList();
    },
    // 移除
    handleDelete(row, index) {
      this.$UiConfirm({
        content: `您要移除数据：${row.deviceName}  这项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.TableFileData.splice(index, 1);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    // 导出失败设备
    handleImportfail() {},
    // 重新导入
    handleAnewImport() {
      document.querySelector('.inline .cyan-btn').click();
    },
    // 上传文件
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code == 200) {
        this.$Message.success(res.msg);
        this.TableFileData = res.data;
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    // 下载模板
    exportModule() {
      this.$emit('exportModule');
    },
  },
  computed: {
    ...mapGetters({
      initialOrgList: 'common/getInitialOrgList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      cancelSource: 'common/getSource',
    }),
    selectedDevNum() {
      let totalCount = 0;
      if (this.isAll) {
        totalCount = this.pageData.totalCount;
      } else if (this.isExclude) {
        if (this.excludeType === '0') {
          totalCount =
            this.devTotal - this.chooseTableData.length > 0 ? this.devTotal - this.chooseTableData.length : 0;
        } else {
          totalCount =
            this.pageData.totalCount - this.chooseTableData.length > 0
              ? this.pageData.totalCount - this.chooseTableData.length
              : 0;
        }
      } else {
        totalCount = this.chooseTableData.length;
      }
      return totalCount;
    },
  },
  components: {
    UnderlineMenu: require('@/components/underline-menu').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    SelectedPreviewModal: require('@/components/choose-device/selected-preview-modal.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
  beforeDestroy() {
    this.orgUnWatch();
  },
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.content-wrap {
  display: flex;
  height: 700px;
  background-color: var(--bg-content);
  border-top: 1px solid var(--border-modal-footer);
  .content-title {
    padding: 20px 20px 0;
    border-bottom: 1px solid var(--border-modal-footer);
  }
  .left-content {
    display: flex;
    flex-direction: column;
    width: 300px;
    height: 100%;
  }
  .right-content {
    width: 715px;
    height: 100%;
    border-left: 1px solid var(--border-modal-footer);
    border-right: 1px solid var(--border-modal-footer);
    .search-top {
      width: 100%;
      .align-flex;
    }
    .search-bottom {
      width: 100%;
      height: 15px;
      .align-flex;
      .devicenumtext {
        flex: 1;
        display: flex;
        justify-content: flex-end;
      }
    }
    .search-box {
      width: 100%;
      .search-header {
        flex: 1;
        .align-flex;
        flex-wrap: wrap;
      }
    }
    .search-input {
      position: relative;
      display: flex;
      .icon-sousuo {
        position: absolute;
        right: 10px;
      }
    }
    .ui-table {
      // width: 100%;
      position: relative;
      margin: 20px 20px 0;
    }
  }
  .padding20 {
    padding: 20px;
  }
  .font-green-color {
    color: var(--color-bluish-green-text);
  }
  .arrow-box {
    height: 600px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
}
.importFile {
  height: 700px;
  &-upload {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .hint {
      color: #65a7ee;
      vertical-align: bottom;
      text-decoration: underline;
      margin-left: 10px;
      cursor: pointer;
    }
    .remark {
      color: red;
      margin-top: 5px;
    }
  }
  .file-table {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .table-hint {
      display: flex;
      padding-bottom: 10px;
      .remark {
        color: red;
      }
      .import,
      .export {
        cursor: pointer;
        color: #65a7ee;
        text-decoration: underline;
        margin-left: 10px;
      }
    }
  }
}
.preview {
  font-size: 16px;
  color: var(--color-primary);
  text-decoration: underline;
  cursor: pointer;
  &:hover {
    color: #4e9ef2;
  }
}
@{_deep} .choose-device-container > .ivu-modal > .ivu-modal-content > .ivu-modal-body {
  padding: 0 !important;
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
.select-label {
  display: flex;
  align-items: center;
  color: var(--color-content);
  font-size: 14px;
  .ui-select-tabs {
    flex: 1;
  }
}
.delete {
  cursor: pointer;
  color: #438cff;
}
.font-color {
  color: var(--color-content);
}
</style>
