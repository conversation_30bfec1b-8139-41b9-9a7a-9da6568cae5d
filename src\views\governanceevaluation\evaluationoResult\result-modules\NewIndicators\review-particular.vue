<!-- 字幕标注合规性与时钟准确性 -->
<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      :default-page-data="pageData"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #otherButton v-if="activeMode === 'device'">
        <div class="other-button ml-lg inline">
          <span class="font-active-color mr-sm pointer update-btn" v-if="statisticShow" @click="updateStatistics"
            >更新统计结果</span
          >
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template #detectionMode="{ row }">
        {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
      </template>
      <template #phyStatus="{ row }">
        <span>
          {{ !row.phyStatusText ? '--' : row.phyStatusText }}
        </span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="getRowTag(row)"></tags-more>
      </template>
      <template slot="qualifiedName" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ row.qualifiedName }}
        </Tag>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          v-if="$route.name !== 'cascadelist'"
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
          icon="icon-rengongfujian"
          content="人工复核"
          class="vt-middle f-14 mr-md"
          @click.native="clickArtificialReview(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="mr-md"
          icon="icon-tianjiabiaoqian"
          content="添加标签"
          @click.native="addTags(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="f-14"
          icon="icon-chakanxiangqing"
          content="原因详情"
          @handleClick="reasonDetail(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
    <!-- 添加标签 -->
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <reason-detail
      v-model="detailVisible"
      :reason-detail-data="reasonDetailData"
      :title="activeIndexItem.indexName"
    ></reason-detail>
    <artificial
      v-model="artificialVisible"
      :detail-data="detailData"
      :page-data="pageData"
      :total-count="totalCount"
      :table-data="tableData"
      @handlePage="handlePageByAuto"
      @update="showRecountBtn"
    ></artificial>
    <!-- 导出   -->
    <export-data ref="exportModule" :export-loading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { mapActions, mapGetters } from 'vuex';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { tableColumns, iconStaticsList, normalFormData } from './util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import taganalysis from '@/config/api/taganalysis';

export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      customSearch: false,
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      chooseOne: {
        tagList: [],
      },
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(tableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      // 1 - 合格、2 - 不合格 3 - 无法检测
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      statisticShow: false,
      activeMode: 'device',
      detailVisible: false,
      artificialVisible: false,
      detailData: {},
      reasonDetailData: {},
      exportLoading: false,
    };
  },
  created() {
    this.getTagList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    initAll() {
      // 获取列表
      this.getTableData();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            item.name = this.activeIndexItem.indexName;
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      this.getTableData();
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    async getTableData() {
      try {
        const data = await this.MixinGetTableData();
        this.tableData = data.entities.map((item) => {
          item.errorMessages = item.errorMessages.join(',');
          return item;
        });
        this.totalCount = data.total;
      } catch (e) {
        console.log(e);
      }
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => {
          item.name = this.activeIndexItem.indexName;
          return (item.count = data[item.fileName] || 0);
        });
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
        this.$Message.success('更新成功');
        this.statisticShow = false;
      } catch (err) {
        console.log(err);
      }
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    // 人工复核
    clickArtificialReview(row) {
      this.artificialVisible = true;
      this.detailData = { ...row };
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    handlePageByAuto(val) {
      this.pageData.pageNum = val;
      this.getTableData();
    },
    reasonDetail(row) {
      this.detailVisible = true;
      this.reasonDetailData = {
        id: row.id,
        criteriaStandard: row.criteriaStandard,
        qualified: row.qualified,
        qualifiedName: row.qualifiedName,
      };
    },
    getRowTag(row) {
      if (!row.tagNames) return [];
      const tagArr = row.tagNames.split(',');
      return tagArr.map((item) => {
        return {
          tagName: item,
        };
      });
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    ReasonDetail: require('./components/reason-detail').default,
    artificial: require('./components/artificial').default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
.line-clamp {
  display: -webkit-box; /* 将对象作为弹性伸缩盒子模型显示 */
  -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
  -webkit-line-clamp: 2; /* 2行，只有 webkit内核支持 */
  word-break: break-all; /* 纯英文换行 */
  overflow: hidden;
}
//.update-btn{
//  display: flex;
//  align-items: center;
//  font-size: 14px;
//  text-decoration: underline;
//}
</style>
