<template>
  <div v-ui-loading="{ loading: loading }" class="height-full">
    <!-- <div class="config-item">
      <div class="config-title">
        <i :class="['icon-font', 'icon-canshupeizhi']"></i>
        <span class="inline vt-middle ml-xs">基础配置</span>
      </div>
      <div class="mt-sm">
        <Tooltip content="说明：指【设备状态（phystatus）】为可用状态！" placement="right">
          <Checkbox v-model="configConvert" :true-value="1" :false-value="0">设备可用状态</Checkbox>
          <i class="icon-font icon-wenhao"></i>
        </Tooltip>
      </div>
      <div class="mt-xs">
        <Tooltip
          content="说明：指最新一次检测设备填报合格，【检测状态（scheckstatus）】的基础信息状态合格！"
          placement="right"
        >
          <Checkbox v-model="configConvert" :true-value="1" :false-value="0">填报合格</Checkbox>
          <i class="icon-font icon-wenhao"></i>
        </Tooltip>
      </div>
      <div class="mt-xs">
        <Tooltip content="说明：指时钟最新一次检测准确，【检测状态（scheckstatus）】的时钟状态合格！" placement="right">
          <Checkbox v-model="configConvert" :true-value="1" :false-value="0">时钟合格</Checkbox>
          <i class="icon-font icon-wenhao"></i>
        </Tooltip>
      </div>
      <div class="mt-xs">
        <Tooltip
          content="说明：针对视频监控设备，视频流相关指标最新一次检测合格，【检测状态（scheckstatus）】的视频流数据状态合格！"
          placement="right"
        >
          <Checkbox v-model="configConvert" :true-value="1" :false-value="0">视频数据状态合格</Checkbox>
          <i class="icon-font icon-wenhao"></i>
        </Tooltip>
      </div>
      <div class="mt-xs">
        <Tooltip
          content="说明：针对人脸卡口设备，人脸视图数据相关指标最新一次检测合格，【检测状态（scheckstatus）】的人脸视图数据状态合格！"
          placement="right"
        >
          <Checkbox v-model="configConvert" :true-value="1" :false-value="0">人脸数据状态合格</Checkbox>
          <i class="icon-font icon-wenhao"></i>
        </Tooltip>
      </div>
      <div class="mt-xs">
        <Tooltip
          content="说明：针对车辆卡口设备，车辆视图数据相关指标最新一次检测合格，【检测状态（scheckstatus）】的车辆视图数据状态合格！"
          placement="right"
        >
          <Checkbox v-model="configConvert" :true-value="1" :false-value="0">车辆数据状态合格</Checkbox>
          <i class="icon-font icon-wenhao"></i>
        </Tooltip>
      </div>
    </div> -->
    <div class="config-item">
      <!-- <div class="config-title">
        <i :class="['icon-font', 'icon-canshupeizhi']"></i>
        <span class="inline vt-middle ml-xs">高级配置</span>
      </div> -->
      <div class="mt-sm">
        <Checkbox v-model="configConvert.fillingQuality.selected" class="type" :true-value="1" :false-value="0"
          >填报质量</Checkbox
        >
        <span>
          <Checkbox v-model="configConvert.fillingQuality.selected" :true-value="1" :false-value="0"></Checkbox>
          <span class="mr-xs">最新</span>
          <Input class="width-mini" v-model="configConvert.fillingQuality.continueCount"></Input>
          <span class="ml-xs">次合格</span>
        </span>
      </div>
      <div class="mt-lg">
        <Checkbox v-model="configConvert.clockStatus.selected" class="type" :true-value="1" :false-value="0"
          >时钟质量</Checkbox
        >
        <span>
          <Checkbox v-model="configConvert.clockStatus.selected" :true-value="1" :false-value="0"></Checkbox>
          <span class="mr-xs">最新</span>
          <Input class="width-mini" v-model="configConvert.clockStatus.continueCount"></Input>
          <span class="ml-xs">次合格</span>
        </span>
      </div>
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">视频流</span>
          <span class="tips">备注：仅针对视频监控设备，勾选的条件需同时满足！</span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.videoOnline.selected" class="type" :true-value="1" :false-value="0"
            >视频在线</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.videoOnline.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'videoOnline')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.videoOnline.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.videoOnline.conditionType"
              @on-change="(val) => changeConditionType(val, 'videoOnline')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.videoOnline.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'videoOnline')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.videoOnline.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.historyVideoAccessed.selected" class="type" :true-value="1" :false-value="0"
            >历史视频可调阅</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.historyVideoAccessed.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'historyVideoAccessed')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.historyVideoAccessed.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.historyVideoAccessed.conditionType"
              @on-change="(val) => changeConditionType(val, 'historyVideoAccessed')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.historyVideoAccessed.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'historyVideoAccessed')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.historyVideoAccessed.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.historyVideoRecord.selected" class="type" :true-value="1" :false-value="0"
            >历史录像完整</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.historyVideoRecord.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'historyVideoRecord')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.historyVideoRecord.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.historyVideoRecord.conditionType"
              @on-change="(val) => changeConditionType(val, 'historyVideoRecord')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.historyVideoRecord.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'historyVideoRecord')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.historyVideoRecord.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.videoQuality.selected" class="type" :true-value="1" :false-value="0"
            >视频质量</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.videoQuality.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'videoQuality')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.videoQuality.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.videoQuality.conditionType"
              @on-change="(val) => changeConditionType(val, 'videoQuality')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.videoQuality.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'videoQuality')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.videoQuality.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.videoSubtitles.selected" class="type" :true-value="1" :false-value="0"
            >视频字幕</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.videoSubtitles.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'videoSubtitles')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.videoSubtitles.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.videoSubtitles.conditionType"
              @on-change="(val) => changeConditionType(val, 'videoSubtitles')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.videoSubtitles.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'videoSubtitles')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.videoSubtitles.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
      </div>
      <!-- 人脸 -->
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">人脸视图数据</span>
          <span class="tips">备注：仅针对人脸卡口设备，勾选的条件需同时满足！</span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.humanCardOnline.selected" class="type" :true-value="1" :false-value="0"
            >人卡在线</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.humanCardOnline.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'humanCardOnline')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.humanCardOnline.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.humanCardOnline.conditionType"
              @on-change="(val) => changeConditionType(val, 'humanCardOnline')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.humanCardOnline.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'humanCardOnline')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.humanCardOnline.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.qualifiedFacialImage.selected" class="type" :true-value="1" :false-value="0"
            >人脸图片合格</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.qualifiedFacialImage.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'qualifiedFacialImage')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.qualifiedFacialImage.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.qualifiedFacialImage.conditionType"
              @on-change="(val) => changeConditionType(val, 'qualifiedFacialImage')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.qualifiedFacialImage.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'qualifiedFacialImage')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.qualifiedFacialImage.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.timelyFacialUpload.selected" class="type" :true-value="1" :false-value="0"
            >人脸上传及时</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.timelyFacialUpload.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'timelyFacialUpload')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.timelyFacialUpload.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.timelyFacialUpload.conditionType"
              @on-change="(val) => changeConditionType(val, 'timelyFacialUpload')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.timelyFacialUpload.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'timelyFacialUpload')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.timelyFacialUpload.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox
            v-model="configConvert.accessibleFacialImages.selected"
            class="type"
            :true-value="1"
            :false-value="0"
            >人脸图片可访问</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.accessibleFacialImages.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'accessibleFacialImages')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.accessibleFacialImages.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.accessibleFacialImages.conditionType"
              @on-change="(val) => changeConditionType(val, 'accessibleFacialImages')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.accessibleFacialImages.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'accessibleFacialImages')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.accessibleFacialImages.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
      </div>
      <!-- 车辆 -->
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">车辆视图数据</span>
          <span class="tips">备注：仅针对车辆卡口设备，勾选的条件需同时满足！</span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.vehicleOnline.selected" class="type" :true-value="1" :false-value="0"
            >车辆在线</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.vehicleOnline.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleOnline')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.vehicleOnline.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.vehicleOnline.conditionType"
              @on-change="(val) => changeConditionType(val, 'vehicleOnline')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.vehicleOnline.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleOnline')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.vehicleOnline.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.vehicleDataIntegrity.selected" class="type" :true-value="1" :false-value="0"
            >车辆数据完整性</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.vehicleDataIntegrity.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleDataIntegrity')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.vehicleDataIntegrity.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.vehicleDataIntegrity.conditionType"
              @on-change="(val) => changeConditionType(val, 'vehicleDataIntegrity')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.vehicleDataIntegrity.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleDataIntegrity')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.vehicleDataIntegrity.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.vehicleDataAccuracy.selected" class="type" :true-value="1" :false-value="0"
            >车辆数据准确性</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.vehicleDataAccuracy.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleDataAccuracy')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.vehicleDataAccuracy.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.vehicleDataAccuracy.conditionType"
              @on-change="(val) => changeConditionType(val, 'vehicleDataAccuracy')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.vehicleDataAccuracy.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleDataAccuracy')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.vehicleDataAccuracy.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.timelyVehicleUpload.selected" class="type" :true-value="1" :false-value="0"
            >车辆上传及时</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.timelyVehicleUpload.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'timelyVehicleUpload')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.timelyVehicleUpload.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.timelyVehicleUpload.conditionType"
              @on-change="(val) => changeConditionType(val, 'timelyVehicleUpload')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.timelyVehicleUpload.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'timelyVehicleUpload')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.timelyVehicleUpload.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
        <div class="mt-lg">
          <Checkbox v-model="configConvert.vehicleImageAccessed.selected" class="type" :true-value="1" :false-value="0"
            >车辆图片可访问</Checkbox
          >
          <span>
            <Checkbox
              v-model="configConvert.vehicleImageAccessed.continueSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleImageAccessed')"
            ></Checkbox>
            <span class="mr-xs">最新</span>
            <Input class="width-mini" v-model="configConvert.vehicleImageAccessed.continueCount"></Input>
            <span class="ml-xs">次合格</span>
            <Select
              class="width-mini ml-lg mr-lg"
              v-model="configConvert.vehicleImageAccessed.conditionType"
              @on-change="(val) => changeConditionType(val, 'vehicleImageAccessed')"
            >
              <Option v-for="(item, index) in orAnd" :key="index" :value="item.value">{{ item.label }}</Option>
            </Select>
            <Checkbox
              v-model="configConvert.vehicleImageAccessed.grandSelected"
              :true-value="1"
              :false-value="0"
              @on-change="(val) => selectedChange(val, 'vehicleImageAccessed')"
            ></Checkbox>
            <span class="mr-xs">近30天累加</span>
            <Input class="width-mini" v-model="configConvert.vehicleImageAccessed.grandTotal"></Input>
            <span class="ml-xs">次合格</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    dataType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      loading: false,
      configInfo: {}, // 后端返回的配置数据
      // 前端展示所用的转换后的数据
      configConvert: {
        fillingQuality: {
          code: 101,
          cnName: '填报质量',
          selected: '',
          continueSelected: 1,
          continueCount: '',
        },
        clockStatus: {
          code: 201,
          cnName: '时钟质量',
          selected: '',
          continueSelected: 1,
          continueCount: '',
        },
        videoOnline: {
          code: 301,
          cnName: '视频在线',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        historyVideoAccessed: {
          code: 302,
          cnName: '历史视频可调阅',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        historyVideoRecord: {
          code: 303,
          cnName: '历史录像完整',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        videoQuality: {
          code: 304,
          cnName: '视频质量',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        videoSubtitles: {
          code: 305,
          cnName: '视频字幕',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        humanCardOnline: {
          code: 401,
          cnName: '人卡在线',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        qualifiedFacialImage: {
          code: 402,
          cnName: '人脸图片合格',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        timelyFacialUpload: {
          code: 403,
          cnName: '人脸上传及时',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        accessibleFacialImages: {
          code: 404,
          cnName: '人脸图片可访问',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        vehicleOnline: {
          code: 501,
          cnName: '车辆在线',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        vehicleDataIntegrity: {
          code: 502,
          cnName: '车辆数据完整性',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        vehicleDataAccuracy: {
          code: 503,
          cnName: '车辆数据准确性',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        timelyVehicleUpload: {
          code: 504,
          cnName: '车辆上传及时',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
        vehicleImageAccessed: {
          code: 505,
          cnName: '车辆图片可访问',
          selected: '',
          continueSelected: 0,
          continueCount: '',
          conditionType: null,
          grandSelected: 0,
          grandTotal: '',
        },
      },
      orAnd: [
        {
          value: 1,
          label: '或',
        },
        {
          value: 2,
          label: '且',
        },
      ],
    };
  },
  created() {
    this.init();
  },
  methods: {
    selectedChange(val, key) {
      this.configConvert[key].conditionType = null;
    },
    changeConditionType(val, key) {
      if (val) {
        this.configConvert[key].continueSelected = 1;
        this.configConvert[key].grandSelected = 1;
      }
    },
    async init() {
      try {
        this.loading = true;
        let params = {
          dataType: this.dataType,
        };
        const res = await this.$http.post(equipmentassets.qualityConfig, params);
        this.configInfo = res.data.data;
        this.dealConfig();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 处理后端数据为前端可以展示的数据
    dealConfig() {
      this.configInfo.forEach((row) => {
        switch (row.dataSubModule) {
          case 101:
            Object.assign(this.configConvert.fillingQuality, row);
            break;
          case 201:
            Object.assign(this.configConvert.clockStatus, row);
            break;
          // 视频
          case 301:
            Object.assign(this.configConvert.videoOnline, row);
            break;
          case 302:
            Object.assign(this.configConvert.historyVideoAccessed, row);
            break;
          case 303:
            Object.assign(this.configConvert.historyVideoRecord, row);
            break;
          case 304:
            Object.assign(this.configConvert.videoQuality, row);
            break;
          case 305:
            Object.assign(this.configConvert.videoSubtitles, row);
            break;
          // 人脸
          case 401:
            Object.assign(this.configConvert.humanCardOnline, row);
            break;
          case 402:
            Object.assign(this.configConvert.qualifiedFacialImage, row);
            break;
          case 403:
            Object.assign(this.configConvert.timelyFacialUpload, row);
            break;
          case 404:
            Object.assign(this.configConvert.accessibleFacialImages, row);
            break;
          // 车辆
          case 501:
            Object.assign(this.configConvert.vehicleOnline, row);
            break;
          case 502:
            Object.assign(this.configConvert.vehicleDataIntegrity, row);
            break;
          case 503:
            Object.assign(this.configConvert.vehicleDataAccuracy, row);
            break;
          case 504:
            Object.assign(this.configConvert.timelyVehicleUpload, row);
            break;
          case 505:
            Object.assign(this.configConvert.vehicleImageAccessed, row);
            break;
        }
      });
    },
    // 处理前端数据为后端需要的
    convertInfo() {
      const configInfo = [];
      Object.keys(this.configConvert).forEach((key) => {
        if (this.configConvert[key].selected) {
          if (!this.configConvert[key].continueSelected && !this.configConvert[key].grandSelected) {
            this.$Message.error(`请填写${this.configConvert[key].cnName}的连续或累加合格次数！`);
            throw new Error(`请填写${this.configConvert[key].cnName}的连续或累加合格次数！`);
          }
          if (this.configConvert[key].continueSelected && !this.configConvert[key].continueCount) {
            this.$Message.error(`请填写${this.configConvert[key].cnName}的连续合格次数！`);
            throw new Error(`请填写${this.configConvert[key].cnName}的连续合格次数！`);
          }
          if (this.configConvert[key].grandSelected && !this.configConvert[key].grandTotal) {
            this.$Message.error(`请填写${this.configConvert[key].cnName}的累加合格次数！`);
            throw new Error(`请填写${this.configConvert[key].cnName}的累加合格次数！`);
          }
        }
        configInfo.push({
          dataSubModule: this.configConvert[key].code,
          selected: this.configConvert[key].selected,
          grandSelected: this.configConvert[key].grandSelected,
          grandTotal: this.configConvert[key].grandTotal,
          continueSelected: this.configConvert[key].continueSelected,
          continueCount: this.configConvert[key].continueCount,
          conditionType: this.configConvert[key].conditionType,
        });
      });
      return configInfo;
    },
    save() {
      const configInfo = this.convertInfo();
      this.$emit('save', configInfo);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.config-item {
  border: 1px solid var(--bg-collapse-item);
  padding: 10px 20px;
  color: var(--color-content);
  margin-bottom: 20px;
  .config-title {
    i {
      color: var(--color-title);
    }
    span {
      color: var(--color-title);
    }
    padding-bottom: 10px;
    border-bottom: 1px solid var(--bg-collapse-item);
  }
  @{_deep}.ivu-tooltip-inner {
    max-width: fit-content;
  }
  .subclass-config {
    .subclass-title {
      margin-top: 20px;
      .title {
        display: inline-block;
        width: 210px;
        color: var(--color-content);
        font-weight: 900;
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 14px;
          background-color: var(--color-primary);
          vertical-align: middle;
          margin-right: 5px;
        }
      }
    }
    .tips {
      color: var(--color-tips);
    }
  }
  .type {
    width: 200px;
  }
}
.icon-wenhao {
  color: var(--color-warning);
}
</style>
