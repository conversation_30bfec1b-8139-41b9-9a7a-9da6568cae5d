---
description: 
globs: 
alwaysApply: false
---
## 代码规范检查

```rule
name: 检查代码规范
description: 检查当前文件的代码规范
command: >
  if [[ "$FILENAME" == *.vue ]]; then
    npx eslint "$FILENAME" --fix
  elif [[ "$FILENAME" == *.js ]]; then
    npx eslint "$FILENAME" --fix
  elif [[ "$FILENAME" == *.ts ]]; then
    npx eslint "$FILENAME" --fix
  fi
```

## 添加Vue组件模板

```rule
name: 添加Vue组件模板
description: 在当前目录创建一个新的Vue组件
command: >
  read -p "请输入组件名称: " component_name
  
  cat > ${component_name}.vue << EOL
  <template>
    <div class="${component_name}">
      
    </div>
  </template>
  
  <script>
  export default {
    name: '${component_name}',
    props: {
      
    },
    data() {
      return {
        
      }
    },
    computed: {
      
    },
    methods: {
      
    },
    mounted() {
      
    }
  }
  </script>
  
  <style scoped>
  .${component_name} {
    
  }
  </style>
  EOL
  
  echo "已创建 ${component_name}.vue 组件"
```

## 添加API请求

```rule
name: 添加API请求
description: 在api文件夹中添加新的API请求
command: >
  read -p "请输入API文件名: " api_name
  
  mkdir -p src/api
  
  cat > src/api/${api_name}.js << EOL
  import request from '@/utils/request'
  
  export function fetchList(params) {
    return request({
      url: '/${api_name}/list',
      method: 'get',
      params
    })
  }
  
  export function fetchDetail(id) {
    return request({
      url: '/${api_name}/detail',
      method: 'get',
      params: { id }
    })
  }
  
  export function create(data) {
    return request({
      url: '/${api_name}/create',
      method: 'post',
      data
    })
  }
  
  export function update(data) {
    return request({
      url: '/${api_name}/update',
      method: 'put',
      data
    })
  }
  
  export function remove(id) {
    return request({
      url: '/${api_name}/delete',
      method: 'delete',
      params: { id }
    })
  }
  EOL
  
  echo "已创建 src/api/${api_name}.js 文件"
```

## 添加Vuex模块

```rule
name: 添加Vuex模块
description: 在store文件夹中添加新的Vuex模块
command: >
  read -p "请输入模块名称: " module_name
  
  mkdir -p src/store/modules
  
  cat > src/store/modules/${module_name}.js << EOL
  const state = {
    list: [],
    detail: {},
    loading: false
  }
  
  const mutations = {
    SET_LIST: (state, list) => {
      state.list = list
    },
    SET_DETAIL: (state, detail) => {
      state.detail = detail
    },
    SET_LOADING: (state, loading) => {
      state.loading = loading
    }
  }
  
  const actions = {
    getList({ commit }, params) {
      commit('SET_LOADING', true)
      // 这里添加API调用
      commit('SET_LOADING', false)
    },
    getDetail({ commit }, id) {
      commit('SET_LOADING', true)
      // 这里添加API调用
      commit('SET_LOADING', false)
    }
  }
  
  export default {
    namespaced: true,
    state,
    mutations,
    actions
  }
  EOL
  
  echo "已创建 src/store/modules/${module_name}.js 文件"
```

## Git相关操作

```rule
name: Git提交
description: 快速进行Git提交
command: >
  read -p "请输入提交信息: " commit_message
  
  git add .
  git commit -m "$commit_message"
  echo "已提交代码: $commit_message"
```

```rule
name: Git拉取最新代码
description: 拉取并合并最新代码
command: >
  git pull
  echo "已拉取最新代码"
```

## 项目构建与部署

```rule
name: 构建项目
description: 构建项目并输出生产环境代码
command: >
  npm run build
  echo "项目构建完成"
```

```rule
name: 启动开发服务器
description: 启动开发环境服务器
command: >
  npm run serve
```

## 依赖管理

```rule
name: 安装依赖
description: 安装新的NPM依赖
command: >
  read -p "请输入依赖包名称: " package_name
  read -p "是否为开发依赖? (y/n): " is_dev
  
  if [[ "$is_dev" == "y" ]]; then
    npm install $package_name --save-dev
  else
    npm install $package_name --save
  fi
  
  echo "已安装 $package_name"
```

```rule
name: 更新依赖
description: 更新项目依赖
command: >
  npm update
  echo "依赖已更新"
```

## 代码生成

```rule
name: 生成Vue页面
description: 创建一个完整的Vue页面（包含列表和详情）
command: >
  read -p "请输入页面名称: " page_name
  
  # 创建目录
  mkdir -p src/views/${page_name}
  
  # 创建列表页
  cat > src/views/${page_name}/index.vue << EOL
  <template>
    <div class="${page_name}-container">
      <div class="filter-container">
        <!-- 搜索表单 -->
      </div>
      
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
      >
        <!-- 表格列 -->
        <el-table-column label="操作" align="center" width="230">
          <template slot-scope="{row}">
            <el-button type="primary" size="mini" @click="handleUpdate(row)">
              编辑
            </el-button>
            <el-button type="danger" size="mini" @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="listQuery.page"
        :limit.sync="listQuery.limit"
        @pagination="getList"
      />
    </div>
  </template>
  
  <script>
  import Pagination from '@/components/Pagination'
  
  export default {
    name: '${page_name}List',
    components: { Pagination },
    data() {
      return {
        list: [],
        total: 0,
        listLoading: false,
        listQuery: {
          page: 1,
          limit: 20
        }
      }
    },
    created() {
      this.getList()
    },
    methods: {
      getList() {
        this.listLoading = true
        // 这里添加API调用
        this.listLoading = false
      },
      handleUpdate(row) {
        this.\$router.push(\`/\${page_name}/edit/\${row.id}\`)
      },
      handleDelete(row) {
        // 删除操作
      }
    }
  }
  </script>
  
  <style scoped>
  .${page_name}-container {
    padding: 20px;
  }
  </style>
  EOL
  
  # 创建详情页
  cat > src/views/${page_name}/detail.vue << EOL
  <template>
    <div class="${page_name}-detail-container">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <!-- 表单字段 -->
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </template>
  
  <script>
  export default {
    name: '${page_name}Detail',
    data() {
      return {
        form: {},
        rules: {},
        loading: false
      }
    },
    created() {
      if (this.\$route.params.id) {
        this.fetchData(this.\$route.params.id)
      }
    },
    methods: {
      fetchData(id) {
        this.loading = true
        // 这里添加API调用
        this.loading = false
      },
      submitForm() {
        this.\$refs.form.validate(valid => {
          if (valid) {
            // 提交表单
          }
        })
      },
      goBack() {
        this.\$router.push(\`/\${page_name}\`)
      }
    }
  }
  </script>
  
  <style scoped>
  .${page_name}-detail-container {
    padding: 20px;
  }
  </style>
  EOL
  
  echo "已创建 ${page_name} 页面"
```

## 文件操作

```rule
name: 批量重命名
description: 批量重命名文件
command: >
  read -p "请输入要查找的文件模式: " file_pattern
  read -p "请输入查找的文本: " search_text
  read -p "请输入替换的文本: " replace_text
  
  for file in $(find . -name "$file_pattern" -type f); do
    new_file=$(echo $file | sed "s/$search_text/$replace_text/g")
    if [ "$file" != "$new_file" ]; then
      mkdir -p $(dirname "$new_file")
      mv "$file" "$new_file"
      echo "重命名: $file -> $new_file"
    fi
  done
  
  echo "批量重命名完成"
```

```rule
name: 查找文本
description: 在项目中查找指定文本
command: >
  read -p "请输入要查找的文本: " search_text
  read -p "请输入文件类型 (例如: js,vue,ts): " file_types
  
  if [ -z "$file_types" ]; then
    grep -r "$search_text" --include="*.*" .
  else
    IFS=',' read -ra TYPES <<< "$file_types"
    for type in "${TYPES[@]}"; do
      grep -r "$search_text" --include="*.$type" .
    done
  fi
```

## 项目工具

```rule
name: 检查项目健康状况
description: 检查项目的依赖和构建状态
command: >
  echo "===== 检查NPM依赖 ====="
  npm outdated
  
  echo "===== 检查依赖安全问题 ====="
  npm audit
  
  echo "===== 检查代码规范 ====="
  if [ -f "package.json" ] && grep -q "\"lint\"" package.json; then
    npm run lint
  else
    echo "项目没有配置lint命令"
  fi
  
  echo "===== 检查能否成功构建 ====="
  if [ -f "package.json" ] && grep -q "\"build\"" package.json; then
    npm run build --dry-run
  else
    echo "项目没有配置build命令"
  fi
```

```rule
name: 项目统计
description: 统计项目代码行数
command: >
  echo "===== 代码行数统计 ====="
  
  find . -name "*.js" -o -name "*.vue" -o -name "*.ts" -o -name "*.css" -o -name "*.scss" | xargs wc -l
  
  echo "===== 文件类型统计 ====="
  
  find . -type f -name "*.*" | grep -v "node_modules" | grep -v "dist" | grep -v ".git" | sed 's/.*\.//' | sort | uniq -c | sort -nr
```


