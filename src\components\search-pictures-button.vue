<template>
  <section class="search-pictures-button">
    <Upload
      class="upload"
      :beforeUpload="(file) => beforeUpload(file)"
      action="*"
      :show-upload-list="false"
    >
      <i class="iconfont icon-xiangji"></i>
    </Upload>

    <ui-modal
      v-model="modalShow"
      :title="dialogData.title"
      :r-width="dialogData.rWidth"
      ref="iModal"
      :loading="btnLoading"
      footer-hide
    >
      <p v-if="selectListBox.length > 1" class="tips color-warning">
        检测出图片有多个目标，请选择要识别的目标！
      </p>
      <p v-else-if="selectListBox.length == 0" class="tips color-error">
        未检测出图片目标！
      </p>
      <div class="img-container">
        <img :src="tempUrl" alt="" id="nodeBox" ref="nodeBox" v-if="tempUrl" />
        <div
          class="select-preview click-preview"
          v-for="(item, index) in boxStyleList"
          :key="index"
          :style="{
            left: item.x + 'px',
            top: item.y + 'px',
            width: item.width + 'px',
            height: item.height + 'px',
            borderColor: item.color,
            backgroundColor: getOpacityColor(item.color, 0.1),
            zIndex: item.zindex,
          }"
          @dblclick="handleBoxSelect($event, index, item.type)"
        ></div>
      </div>
    </ui-modal>
  </section>
</template>
<script>
import { getAllPicturePick, cutImageBase64 } from "@/api/player";
import { getOpacityColor } from "@/util/modules/common.js";
import { mapActions, mapMutations } from "vuex";
export default {
  name: "SearchPicturesButton",
  data() {
    return {
      modalShow: false,
      dialogData: {
        title: "选择目标",
        rWidth: 800,
      },
      btnLoading: false,
      copperList: [],
      tempUrl: "",
      boxStyleList: [],
      selectListBox: [],
    };
  },
  async mounted() {},
  methods: {
    ...mapMutations({
      setWisdomCloudSearchData: "common/setWisdomCloudSearchData",
    }),
    ...mapActions({
      addCloud: "common/addCloud",
    }),
    getOpacityColor,
    //上传图片
    beforeUpload(file) {
      let isAskFile = false;
      isAskFile = /\.(PNG|JPG|JPEG|BMP|png|jpg|jpeg|bmp)$/.test(file.name);
      if (!isAskFile) {
        this.$Message.error("请上传png、jpg、jpeg或bmp格式图片！");
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 30MB!");
      }
      if (!isAskFile || !isLt30M) return false;
      this.fileUploading = this.$Message.loading({
        content: "文件上传中...",
        duration: 0,
      });

      this.boxStyleList = [];
      this.selectListBox = [];
      this.tempUrl = window.URL.createObjectURL(file);
      this.modalShow = true;

      let fileData = new FormData();
      fileData.append("file", file);
      getAllPicturePick(fileData)
        .then((res) => {
          let data = res.data;
          if (data.length == 0) {
            this.$Message.error("没有识别出目标，请选择其它图片");
            return;
          }
          const imgBox = this.$refs.nodeBox;
          const nw = imgBox.naturalWidth;
          const nh = imgBox.naturalHeight;
          const w = parseInt(window.getComputedStyle(imgBox).width);
          const h = parseInt(window.getComputedStyle(imgBox).height);
          const rateW = w / nw;
          const rateH = h / nh;
          data.forEach((v) => {
            if (v.positionVos && v.positionVos.length) {
              v.positionVos.forEach((item) => {
                let boxStyle = {};
                if (nh < 610) {
                  boxStyle = {
                    x: item.left,
                    y: item.top,
                    width: item.right - item.left,
                    height: item.bottom - item.top,
                    type: v.type,
                    color: this.getInfoByType(v.type).color,
                    zindex: this.getInfoByType(v.type).zindex,
                  };
                } else {
                  boxStyle = {
                    x: item.left * rateW,
                    y: item.top * rateH,
                    width: (item.right - item.left) * rateW,
                    height: (item.bottom - item.top) * rateH,
                    type: v.type,
                    color: this.getInfoByType(v.type).color,
                    zindex: this.getInfoByType(v.type).zindex,
                  };
                }
                this.boxStyleList.push(boxStyle);
                this.selectListBox.push(item);
              });
            }
          });
        })
        .finally(() => {
          this.fileUploading();
          this.fileUploading = null;
        });
      return false;
    },
    // 结构化类型
    getInfoByType(type) {
      switch (type) {
        case "face":
          return { color: "#ffea4b", path: "faceContent", zindex: 400 };
        case "vehicle":
          return { color: "#4b8bff", path: "vehicleContent", zindex: 100 };
        case "human":
          return { color: "#bf3e50", path: "humanBodyContent", zindex: 300 };
        case "nonMotor":
          return {
            color: "#67c23a",
            path: "nonmotorVehicleContent",
            zindex: 200,
          };
        default:
          return { color: "#ffea4b", path: "faceContent", zindex: 400 };
      }
    },
    // 框选跳转
    async handleBoxSelect(e, index, type) {
      this.routeParam = {};
      let imgData = this.selectListBox[index];
      const { imageUrl, bottom ,left,right,top,feature } = imgData
      let base64Data = await cutImageBase64(imgData);
      // let routeParam = {
      //   fileUrl: "data:image/jpeg;base64," + base64Data.data.imageBase,
      //   feature: base64Data.data.feature,
      //   imageBase: base64Data.data.imageBase,
      // };
      //#region 添加图片智搜搜索记录
      let urlList = [];
      urlList.push({
        feature: base64Data.data.feature,
        fileUrl: "data:image/jpeg;base64," + base64Data.data.imageBase,
      });
      // 人脸 - 1，车辆 - 2，人体 - 3，非机动车 -4
      let algorithmType = 1; // 默认为人脸
      if (type === "face") {
        algorithmType = 1;
      } else if (type === "vehicle") {
        algorithmType = 2;
      } else if (type === "human") {
        algorithmType = 3;
      } else {
        algorithmType = 4;
      }
      this.setWisdomCloudSearchData({
        keyWords: "",
        algorithmType,
        urlList,
      });
      this.addCloud();
      //#endregion
      let page = this.getInfoByType(type).path;
      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center?sectionName=${page}&noMenu=1`,
        query: {
          // sectionName:page, urlList: [routeParam]
          sectionName: page,
          imgUrl: imageUrl,
          selectSquare: JSON.stringify({
            bottom,left,right,top,type,feature,
          }),
          noMenu: 1,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.search-pictures-button {
  margin: 0 auto;
  .upload {
    cursor: pointer;
  }
  .img-container {
    position: relative;
    width: fit-content;
    margin: 0 auto;
    margin-top: 10px;
    img {
      max-width: 100%;
    }
  }
  .select-preview {
    position: absolute;
    top: 0;
    left: 0;
    // width: 90px;
    // height: 90px;
    background: rgba(255, 234, 75, 0.1);
    // background: #FFEA4B;
    border-radius: 4px;
    border: 2px solid rgba(255, 234, 75, 1);
    display: block;
  }
  .click-preview {
    display: block;
    cursor: pointer;
  }
  #nodeBox {
    width: auto;
    max-height: 600px;
  }
}
</style>
