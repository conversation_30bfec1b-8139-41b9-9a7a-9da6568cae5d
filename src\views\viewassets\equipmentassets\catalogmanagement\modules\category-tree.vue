<template>
  <div class="category-tree">
    <el-tree
      ref="slotTree"
      :data="treeDataArray"
      :node-key="NODE_KEY"
      :default-expand-all="false"
      :props="defaultProps"
      :empty-text="emptyText"
      :expand-on-click-node="false"
      :default-expanded-keys="activeExpand"
      highlight-current
      :current-node-key="currentLivingId"
      @node-click="handleNodeClick"
    >
      <div class="over-flow" slot-scope="{ node, data }" style="width: 100%">
        <template v-if="node.isEdit">
          <Input
            class="special-input"
            :ref="`slotTreeInput${data[NODE_KEY]}`"
            v-model="data.label"
            :placeholder="node.label"
            @on-enter.native="handleInput(node, data)"
            @on-blur.stop="handleInput(node, data)"
          >
          </Input>
        </template>
        <template v-else>
          <div class="custom-tree-node">
            <span :class="[data[NODE_KEY] < NODE_ID_START ? 'is-new' : 'comp-tr-node--name']" class="ellipsis">
              {{ node.label }}
            </span>
            <span class="">
              <i
                class="icon-font icon-tree-add f-14 pointer icon-operation-color mr-xs"
                title="添加"
                @click.stop="() => handleAdd(node, data)"
              >
              </i>
              <i
                class="icon-font icon-bianji f-14 pointer icon-operation-color mr-xs"
                title="编辑"
                @click.stop="() => handleEdit(node, data)"
              >
              </i>
              <i
                class="icon-font icon-shanchu f-14 pointer icon-operation-color mr-sm"
                title="删除"
                @click.stop="() => handleDelete(node, data)"
              >
              </i>
            </span>
          </div>
        </template>
      </div>
    </el-tree>
  </div>
</template>
<script>
import category from '@/config/api/catalogmanagement';

export default {
  props: {
    treeDataObject: {
      default: {},
    },
    chooseOrgCode: {
      default: '',
    },
    defaultExpandedKeys: {
      default: () => [],
    },
  },
  data() {
    const data = [
      {
        id: 1,
        areaTreeName: '一级 1',
        childList: [
          {
            id: 4,
            areaTreeName: '二级 1-1',
            childList: [
              {
                id: 9,
                areaTreeName: '三级 1-1-1',
              },
              {
                id: 10,
                areaTreeName: '三级 1-1-2',
              },
            ],
          },
        ],
      },
      {
        id: 2,
        areaTreeName: '一级 2',
        childList: [
          {
            id: 5,
            areaTreeName: '二级 2-1',
          },
          {
            id: 6,
            areaTreeName: '二级 2-2',
          },
        ],
      },
      {
        id: 3,
        areaTreeName: '一级 3',
        childList: [
          {
            id: 7,
            areaTreeName: '二级 3-1',
          },
          {
            id: 8,
            areaTreeName: '二级 3-2',
          },
        ],
      },
    ];
    return {
      //data: JSON.parse(JSON.stringify(data)),
      data: JSON.parse(JSON.stringify(data)),
      emptyText: '暂无数据',
      defaultProps: {
        label: 'directoryName',
        children: 'childList',
      },
      NODE_KEY: 'id',
      MAX_LEVEL: 3,
      NODE_ID_START: 0, // 新增节点id
      startId: null,
      treeDataArray: [],
      activeExpand: [],
    };
  },
  computed: {
    currentLivingId() {
      if (this.treeDataArray.length > 0) {
        return this.treeDataArray[0].id;
      } else {
        return 0;
      }
    },
  },
  watch: {
    treeDataObject: {
      handler(val) {
        this.treeDataArray = val;
        this.$nextTick(() => {});
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.startId = this.NODE_ID_START;
  },
  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    // 节点选中触发
    handleNodeClick(data) {
      this.$emit('changeSelfTreeNode', data);
    },
    handleAdd(_node, _data) {
      this.emptyText = '';
      if (_node.level >= this.MAX_LEVEL) {
        this.$Message.warning('已经是最大层级了');
      }
      let obj = {};
      obj.parentId = _data[this.NODE_KEY];
      (obj.customAreaName = ''), (obj[this.NODE_KEY] = -1); // 新增的节点都为 -1
      if (!_data.childList) {
        this.$set(_data, 'childList', []);
      }
      _data.childList.push(obj);
      if (!_node.expanded) {
        _node.expanded = true;
      }
      this.$nextTick(() => {
        this.handleEdit([..._node.childNodes].pop(), [..._data.childList].pop());
      });
      this.activeExpand = [_data.id];
    },
    handleEdit(_node, _data) {
      this.$set(_node, 'isEdit', true);
      this.emptyText = ' ';
      if (!_node.hasOwnProperty('isEdit')) {
        this.$set(_node, 'isEdit', true);
      }
      this.$nextTick(() => {
        if (this.$refs[`slotTreeInput${_data[this.NODE_KEY]}`]) {
          this.$refs[`slotTreeInput${_data[this.NODE_KEY]}`].$refs.input.focus();
        }
      });
    },
    async handleInput(_node, _data) {
      // 新增
      if (!_data.directoryName) {
        if (_data.label) {
          // -1为自己新增的节点编辑
          await this.addCustomAreaTree(_data, _node);
          if (_node.isEdit) {
            this.$set(_node, 'isEdit', false);
          }
        } else {
          this.$Message.warning('请填写节点名称');
        } // 编辑
      } else {
        this.updateCustomAreaTree(_node, _data);
      }
    },
    handleDelete(_node, _data) {
      this.$UiConfirm({
        content: `您将删除 ${_data.directoryName} 及所有子级?`,
        title: '警告',
      })
        .then(() => {
          this.deleteCustomAreaTree(_node, _data);
        })
        .catch((res) => {
          console.error(res);
        });
      // let deleteOprate = () => {
      //   if (this.$refs.slotTree) {
      //     this.$refs.slotTree.remove(_data);
      //     this.$Message.success('删除成功');
      //   }
      // };
    },
    handleAddTreeTop() {
      let obj = this.$util.common.deepCopy(this.initParam);
      obj[this.NODE_KEY] = --this.startId;
      this.setTree.push(obj);
    },
    /* ----------------------------------------- 自定义方法 ----------------------------------------- */
    // 新增自定义目录树节点
    async addCustomAreaTree(_data) {
      try {
        const params = {
          directoryName: _data.label,
          orgCode: this.chooseOrgCode,
          parentId: _data.parentId || '',
        };
        let { data } = await this.$http.post(category.deviceDirectoryAdd, params);
        this.$Message.success(data.msg);
        this.$emit('updateTree');
      } catch (err) {
        console.error(err);
      }
    },
    // 删除自定义树节点
    async deleteCustomAreaTree(_node, _data) {
      this.activeExpand = [_data.parentId];
      try {
        let { data } = await this.$http.delete(category.deviceDirectoryDelete + _data.id);
        this.$Message.success(data.msg);
        this.$emit('updateTree');
      } catch (err) {
        console.error(err);
      }
    },
    // 编辑自定义目录树节点
    async updateCustomAreaTree(_node, _data) {
      this.activeExpand = [_data.parentId];
      try {
        const params = {
          id: _data.id,
          directoryName: _data.label,
          orgCode: this.chooseOrgCode,
          parentId: _data.parentId,
        };
        _data.label ? (params.directoryName = _data.label) : (params.directoryName = _data.directoryName);
        let { data } = await this.$http.put(category.deviceDirectoryUpdate, params);
        this.$Message.success(data.msg);
        this.$emit('updateTree');
      } catch (err) {
        console.error(err);
      }
    },
    //设置高亮  slotTree 元素的ref   value 绑定的node-key
    setCurrentKeyHandle(value) {
      this.$nextTick(() => {
        this.$refs.slotTree.setCurrentKey(value);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.category-tree {
  .special-input {
    @{_deep}.ivu-input {
      height: 25px;
      line-height: 25px;
      width: 100%;
      &&::-webkit-input-placeholder {
        font-size: 12px !important;
      }
    }
  }
  @{_deep}.el-tree__empty-block {
    min-height: 15px;
    .el-tree__empty-text {
      font-size: 12px;
      color: #fff;
    }
  }
  @{_deep}.el-tree-node__content {
    border: 1px solid transparent;
    background: transparent;
    height: 34px;
  }
  @{_deep}.el-tree-node {
    &.is-current {
      > .el-tree-node__content {
        border-color: var(--color-primary) !important;
        background: var(--color-primary);
        color: #fff;
      }
    }
  }
}
.custom-tree-node {
  width: 100%;
  height: 30px;
  line-height: 30px;
  display: flex;
  justify-content: space-between;
}
</style>
