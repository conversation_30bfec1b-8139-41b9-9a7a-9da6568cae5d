<template>
  <ui-modal
    v-model="modalShow"
    :title="isEdit ? '编辑资源' : '新增资源'"
    :width="dialogData.width"
    @onOk="submitData"
    @onCancel="close"
  >
    <Form
      class="form-modal"
      ref="dataForm"
      :model="dataForm"
      :rules="ruleValidate"
      :label-width="110"
    >
      <div>
        <FormItem label="资源名称" prop="resourceName">
          <Input
            v-model="dataForm.resourceName"
            placeholder="请输入资源名称"
            :disabled="isEdit"
          ></Input>
        </FormItem>
        <FormItem label="所属目录" prop="catalogIds" v-if="modalShow">
          <ui-treeSelect
            style="width: 100%"
            class="select-node-tree filter-tree"
            multiple
            v-model="dataForm.catalogIds"
            filterable
            check-strictly
            placeholder="请选择所属目录"
            default-expand-all
            show-checkbox
            :expand-on-click-node="false"
            check-on-click-node
            node-key="id"
            :treeData="directoryList"
          >
          </ui-treeSelect>
        </FormItem>
        <FormItem label="资源中文名称" prop="resourceNameCn">
          <Input
            v-model="dataForm.resourceNameCn"
            placeholder="请输入资源中文名称"
            :disabled="isEdit"
          ></Input>
        </FormItem>
        <FormItem label="资源描述" :label-width="110" prop="describeCn">
          <Input
            v-model="dataForm.describeCn"
            :maxlength="500"
            :rows="4"
            type="textarea"
            placeholder="请输入描述信息"
          />
        </FormItem>
        <FormItem label="添加数据项" prop="itemDetails">
          <div class="operate_bar">
            <Button type="primary" @click="addFieldHandle">新增</Button>
            <!-- <Input
              style="width: 80px"
              v-model="addFieldNum"
              placeholder="输入"
              maxlength="2"
            >
              <span slot="append">条</span>
            </Input> -->
            <Button type="default" @click="deletMore">删除</Button>
            <Upload
              action="*"
              :before-upload="beforeUpload"
              class="upload-content"
              :limit="1"
            >
              <Button type="primary">导入Excel</Button>
            </Upload>
            <Button type="primary" v-if="isEdit" @click="generateData()"
              >生成数据</Button
            >
          </div>
          <ui-table
            class="auto-fill table"
            :columns="columns"
            border
            :loading="loading"
            :data="dataForm.itemDetails"
            :rules="ruleValiTabledate"
            max-height="300"
            ref="seletction"
            @on-select="handleSelect"
            @on-select-cancel="handleSelect"
            @on-select-all="handleSelectAll"
            @on-select-all-cancel="handleSelectAll"
          >
            <template #fieldName="{ index }">
              <Input
                placeholder="请输入字段名"
                v-model="dataForm.itemDetails[index].fieldName"
                :disabled="dataForm.itemDetails[index]._disabled"
              ></Input>
            </template>
            <template #fieldNameCn="{ index }">
              <Input
                placeholder="请输入中文名"
                v-model="dataForm.itemDetails[index].fieldNameCn"
                :disabled="dataForm.itemDetails[index]._disabled"
              ></Input>
            </template>
            <template #fieldType="{ index }">
              <Select
                :placeholder="
                  dataForm.itemDetails[index].fieldType
                    ? dataForm.itemDetails[index].fieldType
                    : '数据类型'
                "
                v-model="dataForm.itemDetails[index].fieldType"
                :disabled="dataForm.itemDetails[index]._disabled"
                transfer
                @on-change="changeFieldType(dataForm.itemDetails[index])"
              >
                <Option
                  v-for="(item, $index) in fileType"
                  :value="item"
                  :key="$index"
                  >{{ item }}</Option
                >
              </Select>
            </template>
            <template #fieldTypeLen="{ index }">
              <Input
                placeholder="数据长度"
                v-model="dataForm.itemDetails[index].fieldTypeLen"
                :disabled="
                  notSetLen.includes(dataForm.itemDetails[index].fieldType) ||
                  dataForm.itemDetails[index]._disabled
                "
              ></Input>
            </template>
            <template #isPk="{ index }">
              <Select
                v-model="dataForm.itemDetails[index].isPk"
                :disabled="dataForm.itemDetails[index]._disabled"
                transfer
              >
                <Option :value="0">否</Option>
                <Option :value="1">是</Option>
              </Select>
            </template>
            <template #isIndex="{ index }">
              <Select
                v-model="dataForm.itemDetails[index].isIndex"
                :disabled="dataForm.itemDetails[index]._disabled"
                transfer
              >
                <Option :value="0">否</Option>
                <Option :value="1">是</Option>
              </Select>
            </template>
            <template #sensitiveLv="{ index }">
              <Select
                v-model="dataForm.itemDetails[index].sensitiveLv"
                :disabled="dataForm.itemDetails[index]._disabled"
                transfer
              >
                <Option :value="0">一级</Option>
                <Option :value="1">二级</Option>
              </Select>
            </template>
            <template #dataElement="{ index }">
              <Input
                placeholder="请输入"
                v-model="dataForm.itemDetails[index].dataElement"
                :disabled="dataForm.itemDetails[index]._disabled"
              ></Input>
            </template>
            <template #determiner="{ index }">
              <Input
                placeholder="请输入"
                v-model="dataForm.itemDetails[index].determiner"
                :disabled="dataForm.itemDetails[index]._disabled"
              ></Input>
            </template>
            <template #action="{ row }">
              <div
                v-if="!row._disabled"
                style="display: flex; justify-content: center"
              >
                <ui-btn-tip
                  content="删除"
                  icon="icon-shanchu"
                  class="primary"
                  @click.native="del(row)"
                ></ui-btn-tip>
              </div>
            </template>
          </ui-table>
        </FormItem>
      </div>
    </Form>
  </ui-modal>
</template>
<script>
import {
  parseExcel,
  saveResource,
  queryResItems,
  addResItems,
  updateResource,
} from "@/api/dataGovernance";
import UiTreeSelect from "@/components/ui-tree-select";

export default {
  props: {
    directoryList: {
      type: Array,
      default: () => [],
    },
  },
  components: { UiTreeSelect },
  data() {
    return {
      type: "add", //新增/编辑
      dialogData: {
        width: 1050,
        title: "Excel一键创建资源",
      },
      ruleValidate: {
        catalogIds: [
          {
            required: true,
            type: "array",
            message: "请选择所属目录",
            trigger: "change",
          },
        ],
        resourceName: [
          { required: true, message: "资源名称", trigger: "blur" },
        ],
        resourceNameCn: [
          { required: true, message: "资源中文名称", trigger: "blur" },
        ],
      },
      catalogList: [],
      indeterminate: false,
      checkAll: false,
      modalShow: false,
      dataForm: {
        resourceName: "",
        resourceNameCn: "",
        describeCn: "",
        catalogIds: [],
        itemDetails: [],
      },
      checkAllGroup: [],
      addFieldNum: "1",
      detailObj: {},
      loading: false,
      fileType: ["GEOPOINT", "VARCHAR2", "DATETIME", "NUMBER", "BLOB"],
      // 不设置长度的字段类型
      notSetLen: ["GEOPOINT", "DATETIME", "TIMESTAMP"],
      ruleValiTabledate: {
        fieldName: [{ required: true, message: "字段名称", trigger: "blur" }],
        fieldNameCn: [
          { required: true, message: "字段中文名称", trigger: "blur" },
        ],
        fileType: [{ required: true, message: "字段类型", trigger: "blur" }],
      },
      columns: [
        { title: "", width: 40, type: "selection", key: "index" },
        {
          title: "序号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "字段名称", slot: "fieldName", width: 140 },
        { title: "字段中文名称", slot: "fieldNameCn", width: 160 },
        { title: "字段类型", slot: "fieldType", width: 140 },
        { title: "字段类型长度", slot: "fieldTypeLen", width: 140 },
        { title: "是否主键", slot: "isPk", width: 140 },
        { title: "是否索引", slot: "isIndex", width: 140 },
        { title: "敏感级别", slot: "sensitiveLv", width: 140 },
        { title: "数据元", slot: "dataElement", width: 140 },
        { title: "限定词", slot: "determiner", width: 180 },
        { title: "操作", slot: "action", width: 80, align: "center" },
      ],
      selectinIndexs: [],
    };
  },
  computed: {
    isEdit() {
      return this.type === "edit";
    },
  },
  methods: {
    show(item) {
      this.detailObj = item;
      this.modalShow = true;
      this.dataForm = {
        resourceName: "",
        resourceNameCn: "",
        describeCn: "",
        catalogIds: [],
        itemDetails: [],
      };
      this.addFieldNum = "1";
      this.type = item ? "edit" : "add";
      this.$nextTick(() => {
        this.$refs.dataForm.resetFields();
        if (this.isEdit) {
          // 编辑字段回显
          console.log(item, "编辑回显");
          const { catalogId, resourceName, resourceNameCn, describeCn, id } =
            item;
          this.dataForm.id = id;
          this.dataForm.catalogIds = [...catalogId.split(",")];
          this.dataForm.resourceName = resourceName;
          this.dataForm.resourceNameCn = resourceNameCn;
          this.dataForm.describeCn = describeCn;
          this.loading = true;
          queryResItems(this.dataForm.id)
            .then((res) => {
              this.dataForm.itemDetails = res.data;
              this.dataForm.itemDetails.forEach((item, index) => {
                item.index = index;
                item._disabled = true;
              });
              this.loading = false;
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    },
    // 新增字段
    addFieldHandle() {
      if (!this.addFieldNum) {
        this.$Message.error("请输入新增字段数量");
        return;
      }
      for (let i = 0; i < this.addFieldNum; i++) {
        let obj = {
          fieldName: "",
          fieldNameCn: "",
          fieldType: "VARCHAR2",
          fieldTypeLen: "255",
          isPk: 0,
          isIndex: 0,
          sensitiveLv: "",
          dataElement: "",
          determiner: "",
          _disabled: false,
        };
        this.dataForm.itemDetails.unshift(obj);
        this.dataForm.itemDetails.forEach((item, index) => {
          item.index = index;
        });
      }
      this.addFieldNum = "1";
    },
    //切换字段类型
    changeFieldType(val) {
      if (val.fieldType == "GEOPOINT") {
        val.fieldTypeLen = "0";
      } else {
        val.fieldTypeLen = "255";
      }
    },
    //保存 创建资源
    submitData() {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          let correctCount = 0;
          this.dataForm.itemDetails.forEach((item) => {
            if (!item.fieldName) {
              this.$Message.error(
                "第" +
                  (this.dataForm.itemDetails.indexOf(item) + 1) +
                  "条数据项字段名称为空"
              );
              return;
            }
            if (!item.fieldNameCn) {
              this.$Message.error(
                "第" +
                  (this.dataForm.itemDetails.indexOf(item) + 1) +
                  "条数据项字段中文名称为空"
              );
              return;
            }
            correctCount++;
          });
          if (correctCount === this.dataForm.itemDetails.length) {
            console.log(this.dataForm, "this.dataForm");
            if (this.isEdit) {
              //编辑
              let params = {
                id: this.dataForm.id,
                catalogId: this.dataForm.catalogIds,
                resourceName: this.dataForm.resourceName,
                resourceNameCn: this.dataForm.resourceNameCn,
                describeCn: this.dataForm.describeCn,
              };
              let paramsItemDetails = [];
              this.dataForm.itemDetails.forEach((item) => {
                if (!item._disabled) {
                  paramsItemDetails.push(item);
                }
              });
              updateResource(params).then((res) => {
                if (paramsItemDetails.length > 0) {
                  //新增字段
                  addResItems({
                    resourceId: this.dataForm.id,
                    itemDetails: paramsItemDetails,
                  }).then((req) => {
                    if (req.code !== 200) {
                      this.$Message.error(req.msg);
                      return;
                    }
                    this.$Message.success("修改成功");
                    this.$emit("refreshDataList");
                    this.modalShow = false;
                  });
                } else {
                  //不新增字段
                  if (res.code !== 200) {
                    this.$Message.error(res.msg);
                    return;
                  }
                  this.$Message.success("修改成功");
                  this.$emit("refreshDataList");
                  this.modalShow = false;
                }
              });
            } else {
              // 新增
              saveResource(this.dataForm).then((res) => {
                this.$Message.success("新增成功");
                this.$emit("refreshDataList");
                this.modalShow = false;
              });
            }
          }
        } else {
          this.$Message.error("信息不完整，请完善");
        }
      });
    },
    //解析excel表
    beforeUpload(file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error("仅支持Excel文件格式的导入");
        return false;
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 30MB!");
        return false;
      }
      let fileData = new FormData();
      fileData.append("file", file);
      this.loading = true;
      parseExcel(fileData)
        .then((res) => {
          this.dataForm.resourceName = res.data.defaultResName;
          this.dataForm.resourceNameCn = res.data.defaultResNameCn;
          let newItemDetails = [];
          if (this.dataForm.itemDetails.length > 0) {
            for (var i = 0; i < res.data.itemDetails.length; i++) {
              var obj = res.data.itemDetails[i];
              var fieldName = obj.fieldName;
              var isExist = false;
              for (var j = 0; j < this.dataForm.itemDetails.length; j++) {
                var aj = this.dataForm.itemDetails[j];
                var n = aj.fieldName;
                if (n == fieldName) {
                  isExist = true;
                  break;
                }
              }
              if (!isExist) {
                newItemDetails.push(obj);
              }
            }
          } else {
            newItemDetails = res.data.itemDetails;
          }
          newItemDetails.forEach((item) => {
            item.fieldType =
              item.fieldType == "CHAR" ? "VARCHAR2" : item.fieldType;
            item._disabled = false;
          });
          this.dataForm.itemDetails = [
            ...newItemDetails,
            ...this.dataForm.itemDetails,
          ];
          this.dataForm.itemDetails.forEach((item, index) => {
            item.index = index;
          });
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 生成数据
    generateData() {
      this.$emit("generateData", this.detailObj);
    },
    // 全选
    handleSelectAll(selection) {
      // 取消全选判断
      if (selection.length === 0) {
        this.selectinIndexs = [];
      } else {
        // 全部选中
        selection.forEach((e, index) => {
          this.selectinIndexs.push(index);
        });
      }
    },
    // 单选
    handleSelect(selection) {
      this.selectinIndexs = [];
      selection.forEach((item) => {
        this.selectinIndexs.push(item.index);
      });
    },
    //批量删除
    deletMore() {
      if (this.selectinIndexs.length !== 0) {
        for (let i = this.dataForm.itemDetails.length - 1; i >= 0; i--) {
          if (this.selectinIndexs.indexOf(i) !== -1) {
            this.dataForm.itemDetails.splice(i, 1);
          }
        }
        this.selectinIndexs = [];
        this.dataForm.itemDetails.forEach((item, index) => {
          item.index = index;
        });
      } else {
        this.$Message.warning("删除项不能为空！");
      }
    },
    //行内单个删除
    del(row) {
      this.selectinIndexs = [];
      this.selectinIndexs.push(row.index);
      this.deletMore();
    },
    close() {
      this.dataForm = {
        resourceName: "",
        resourceNameCn: "",
        describeCn: "",
        catalogIds: [],
        itemDetails: [],
      };
      this.$refs.dataForm.resetFields();
    },
  },
};
</script>
<style scoped lang="less">
.form-modal {
  .ivu-row {
    display: flex;
    justify-content: space-around;
  }
}
.operate_bar {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  .upload-content {
    display: flex;
    margin-right: 10px;
  }
  & > .ivu-btn {
    margin-right: 10px;
  }
  & > .ivu-input-group {
    display: flex;
    margin-right: 10px;
    height: 100%;

    /deep/ .ivu-input-group-append {
      background-color: #e3e3e3;
      width: 25px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.ivu-form-item {
  margin-bottom: 20px;
}
.auto-fill {
  height: 330px;
}
/deep/ .ivu-tag .ivu-icon {
  top: 4px !important;
  font-size: 16px !important;
}
</style>
