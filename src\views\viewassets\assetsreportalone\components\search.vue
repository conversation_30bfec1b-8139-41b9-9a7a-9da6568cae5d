<template>
  <div class="search-module">
    <div class="search">
      <ui-label class="inline" label="组织机构">
        <ApiOrganizationTree
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </ApiOrganizationTree>
      </ui-label>
      <ui-label class="inline ml-lg" label="行政区划">
        <ApiAreaTree :select-tree="selectTree" @selectedTree="selectedArea" placeholder="请选择行政区划"></ApiAreaTree>
      </ui-label>
      <ui-label class="inline ml-lg" :label="global.filedEnum.deviceId">
        <Input
          v-model="searchData.deviceId"
          class="width-sm"
          :placeholder="`请输入${global.filedEnum.deviceId}`"
        ></Input>
      </ui-label>
      <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName">
        <Input
          v-model="searchData.deviceName"
          class="width-sm"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
        ></Input>
      </ui-label>
      <ui-label label="上报状态" class="inline ml-lg">
        <Select class="width-sm" v-model="searchData.cascadeReportStatus" placeholder="请选择上报状态" clearable>
          <Option value="0">未上报</Option>
          <Option value="1">已上报</Option>
        </Select>
      </ui-label>
      <div class="fr">
        <UiSearch class="ui-search mr-lg" v-model="searchModel">
          <template #content>
            <div class="search-content">
              <ui-label label="最近上报状态" class="inline ml-lg">
                <Select
                  class="width-sm"
                  v-model="searchData.recentlyReportStatus"
                  placeholder="请选择最近上报状态"
                  clearable
                >
                  <Option value="0">上报失败</Option>
                  <Option value="1">上报成功</Option>
                </Select>
              </ui-label>
              <ui-label class="inline mb-10 ml-lg" label="检测状态">
                <Select
                  class="width-md"
                  v-model="searchData.checkStatuses"
                  multiple
                  clearable
                  placeholder="请选择检测状态"
                  :max-tag-count="1"
                  @on-change="choseStuses"
                >
                  <Option v-for="(item, index) in checkStatus" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="inline ml-lg" label="异常类型" v-if="searchData.checkStatuses.indexOf('3') !== -1">
                <Select
                  @on-change="choseType"
                  class="width-md"
                  v-model="searchData.errorCategory"
                  placeholder="请选择异常类型"
                  clearable
                >
                  <Option v-for="(item, index) in errorCategoryList" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label
                class="inline ml-lg"
                label="错误类别"
                v-if="searchData.errorCategory && searchData.checkStatuses.indexOf('3') !== -1"
              >
                <Select
                  class="width-md"
                  v-model="searchData.errorMessageList"
                  multiple
                  placeholder="请选择错误类别"
                  clearable
                  :max-tag-count="1"
                >
                  <Option :value="item" v-for="item in messageList" :key="item">{{ item }}</Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.sbdwlx" class="inline ml-lg">
                <Select
                  class="width-sm"
                  v-model="searchData.sbdwlx"
                  :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                  clearable
                >
                  <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.sbgnlx" class="inline ml-lg">
                <Select
                  class="width-sm"
                  v-model="searchData.sbgnlx"
                  :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                  :max-tag-count="1"
                  multiple
                  clearable
                >
                  <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>

              <ui-label :label="global.filedEnum.phyStatus" class="inline ml-lg mt-sm mb-sm">
                <Select
                  class="width-sm"
                  v-model="searchData.phyStatus"
                  :placeholder="`请选择${global.filedEnum.phyStatus}`"
                  clearable
                >
                  <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label
                class="inline"
                :class="{ 'ml-lg': searchData.checkStatuses.indexOf('3') === -1 }"
                label="上报时间"
              >
                <DatePicker
                  class="width-sm"
                  v-model="searchData.recentlyReportTimeStart"
                  type="datetime"
                  placeholder="请选择开始时间"
                  :options="startTimeOption"
                  confirm
                ></DatePicker>
                <span class="ml-sm mr-sm">--</span>
                <DatePicker
                  class="width-sm"
                  v-model="searchData.recentlyReportTimeEnd"
                  type="datetime"
                  placeholder="请选择结束时间"
                  :options="endTimeOption"
                  confirm
                ></DatePicker>
              </ui-label>
              <ui-label class="inline mb-sm ml-lg" label="在线状态">
                <Select class="width-md" v-model="searchData.isOnline" clearable placeholder="请选择设备在线状态">
                  <Option v-for="(item, index) in propertySearch_isonline" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-select-tabs
                class="ui-select-tabs"
                :list="MixinTagList"
                @selectInfo="MixinSelectInfo"
                ref="uiSelectTabs"
              >
              </ui-select-tabs>
            </div>
          </template>
        </UiSearch>
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';
import getTagMixin from '@/views/viewassets/mixins/getTagMixin';
export default {
  mixins: [getTagMixin],
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UiSearch: require('@/components/ui-search').default,
  },
  data() {
    return {
      searchModel: false,
      selectOrgTree: {
        orgCode: null,
      },
      choosedOrg: {},
      allCheck: '',
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      searchData: {
        deviceId: '',
        deviceName: '',
        orgCode: '',
        regionCode: '',
        cascadeReportStatus: '',
        recentlyReportStatus: '',
        checkStatuses: [],
        errorCategory: '',
        errorMessageList: [],
        sbdwlx: '',
        sbgnlx: [],
        phyStatus: '',
        recentlyReportTimeStart: '',
        recentlyReportTimeEnd: '',
        isOnline: '',
      },
      messageList: [],
      selectTree: {
        regionCode: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.recentlyReportTimeEnd);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.recentlyReportTimeStart);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      errorCategoryList: 'algorithm/error_category',
      phystatusList: 'algorithm/propertySearch_phystatus',
      propertySearch_isonline: 'algorithm/propertySearch_isonline', // 在线状态
    }),
  },
  watch: {},
  filter: {},
  created() {
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.choosedOrg = this.defaultSelectedOrg;
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
  },
  mounted() {
    this.search();
  },
  methods: {
    // 查询
    search() {
      this.copySearchDataMx(this.searchData);
      this.$emit('startSearch', this.searchData, this.choosedOrg);
    },
    choseStuses(val) {
      //清空
      if (val.indexOf('3') === -1) {
        this.searchData.errorCategory = '';
        this.searchData.errorMessageList = [];
      }
    },
    // 重置
    reset() {
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.selectTree.regionCode = '';
      this.$refs.uiSelectTabs && this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.search);
      this.$emit('reset');
    },
    // 行政区划
    selectedArea(data) {
      this.searchData.civilCode = data.regionCode;
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.choosedOrg = val;
      this.$emit('startSearch', this.searchData, this.choosedOrg);
    },
    async choseType(val) {
      try {
        this.searchData.errorMessageList = [];
        let res = await this.$http.get(equipmentassets.queryErrorReason, {
          params: { errorType: val },
        });
        if (val === 'BASICS') {
          this.messageList = res.data.data;
        } else if (val === 'IMAGE') {
          this.messageList = res.data.data;
        } else if (val === 'VIDEO') {
          this.messageList = res.data.data;
        }
      } catch (err) {
        console.log(err);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.search-module {
  padding: 10px 0;
  position: relative;
  @{_deep} .select-width {
    width: 160px;
  }
}
</style>
