<!--
    * @FileDescription: 布控任务统计
    * @Author: H
    * @Date: 2024/04/28
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="statis" class="statis" ref="statis"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default{
    data() {
        return {
            formData: {
                type: ''
            },
            myEchart: null
        }
    },
    mounted() {
        // this.init()
    },
    methods: {
        init(data) {
            const arr = [
                {  x: '0', xStr: '人像布控', waitAudit: 0, executing: 0, type:'face'},
                {  x: '1', xStr: '机动车布控', waitAudit: 0, executing: 0, type:'vehicle'},
                {  x: '2', xStr: '感知数据布控', waitAudit: 0, executing: 0, type:'sensory' },
                {  x: '3', xStr: '多维布控', waitAudit: 0, executing: 0, type:'multi' },
            ];
            arr.forEach(item => {
                if(data[item.type]){
                    item.waitAudit = data[item.type].waitAudit;
                    item.executing = data[item.type].executing;
                }else{
                    item.waitAudit = 0;
                    item.executing = 0;
                }
            })
            this.statisChart(arr)
        },
        statisChart(arr) {
            this.myEchart = echarts.init(this.$refs.statis)
            const LinearGradientColor = {
                0: {
                    left: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#009DFF', },
                        { offset: 1, color: 'rgba(0,137,234,0)',},
                    ]),
                    right: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#009DFF',},
                        { offset: 1, color: 'rgba(0,137,234,0)',},
                    ]),
                    top: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#009DFF',},
                        // { offset: 0.5, color: '#69ACFF',},
                        { offset: 1, color: 'rgba(0,137,234,0)',},
                    ]),
                },
                1: {
                    left: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#25E4CA',},
                        { offset: 1, color: 'rgba(25, 168,151, 0.6)',},
                    ]),
                    right: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#25E4CA',},
                        { offset: 1, color: 'rgba(25, 168,151, 0.6)',},
                    ]),
                    top: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0,color: '#7CFFEE',},
                        { offset: 1, color: '#30E8D2',},
                    ]),
                },
            };
            const offsetX = 12; // 10 面宽度
            const offsetY = 7; // 5 倾斜度
            const offsetYY = 0; // 柱形整体下移距离
            // 绘制左侧面
            const CubeLeft = echarts.graphic.extendShape({
                shape: {
                    x: 0,
                    y: 0,
                },
                buildPath: function (ctx, shape) {
                    const xAxisPoint = shape.xAxisPoint;
                    const yBase = shape.yBase;
                    const c0 = [shape.x, shape.y + yBase]; // t-r
                    const c1 = [shape.x - offsetX, shape.y - offsetY + yBase]; // t-l
                    const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY + yBase]; // b-l
                    const c3 = [xAxisPoint[0], xAxisPoint[1] + yBase]; // b-r
                    ctx.moveTo(c0[0], c0[1]).lineTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).closePath()
                },
            });
            // 绘制右侧面
            const CubeRight = echarts.graphic.extendShape({
                shape: {
                    x: 0,
                    y: 0,
                },
                buildPath: function (ctx, shape) {
                    const xAxisPoint = shape.xAxisPoint
                    const yBase = shape.yBase
                    const c1 = [shape.x, shape.y + yBase]
                    const c2 = [xAxisPoint[0], xAxisPoint[1] + yBase]
                    const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY + yBase]
                    const c4 = [shape.x + offsetX, shape.y - offsetY + yBase]
                    ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
                },
            });
            // 绘制顶面
            const CubeTop = echarts.graphic.extendShape({
                shape: {
                    x: 0,
                    y: 0,
                },
                buildPath: function (ctx, shape) {
                    if (shape.yValue == 0) return
                    const yBase = shape.yBase
                    const c1 = [shape.x, shape.y + yBase]
                    const c2 = [shape.x + offsetX, shape.y - offsetY + yBase] //右点
                    const c3 = [shape.x, shape.y - offsetX + yBase]
                    const c4 = [shape.x - offsetX, shape.y - offsetY + yBase]
                    ctx.moveTo(c1[0], c1[1]).lineTo(c2[0], c2[1]).lineTo(c3[0], c3[1]).lineTo(c4[0], c4[1]).closePath()
                },
            });
            // 注册三个面图形
            echarts.graphic.registerShape('CubeLeft', CubeLeft)
            echarts.graphic.registerShape('CubeRight', CubeRight)
            echarts.graphic.registerShape('CubeTop', CubeTop)
            // 计算叠加绘制高度值
            const calcSuperpositionVal = (params, api, yData) => {
                const currentSeriesIndices = api.currentSeriesIndices()
                const seriesIndex = params.seriesIndex
                if (seriesIndex == 0 || currentSeriesIndices.length == 1 || currentSeriesIndices[0] === seriesIndex) return offsetYY
                const dataIndex = params.dataIndex
                const seriesData = yData.map(x => x[dataIndex])
                const yBaseNum = currentSeriesIndices.reduce((pre, cur) => {
                    if (cur < seriesIndex) {
                        pre = pre + seriesData[cur]
                    }
                    return pre
                }, 0)
                const chazhi = api.coord([0, yBaseNum])[1] - api.coord([0, 0])[1] + offsetYY
                return params.seriesIndex > 0 ? chazhi : offsetYY
            }
            const axisColor = 'rgba(211,228,242,0.6)'
            const markTextColor = '#D3E4F2'
            const splitLineColor = 'rgba(211,228,242,0.16)'
            const marks = [
                { name: '代审核', color: '#4FAEFF' },
                { name: '执行中', color: '#62F7E4' },
            ]
            // const arr = [
            //     {  x: '0', xStr: '人像布控', yxqslY: 1, yhlslY: 5 },
            //     {  x: '1', xStr: '机动车布控', yxqslY: 0, yhlslY: 6 },
            //     {  x: '2', xStr: '感知数据布控', yxqslY: 5, yhlslY: 5 },
            //     {  x: '3', xStr: '多维布控', yxqslY: 1, yhlslY: 5 },
            // ]
            const x = [],
                y = [[], [], []]

            for (const i of arr) {
                x.push(i.xStr)
                y[0].push(i.waitAudit) // 代审核
                y[1].push(i.executing) // 执行中
            }
            let option = {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(2, 27, 71, 0.8)',
                    borderColor: '#098EFF',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                legend: {
                    bottom: '5px',
                    left: 'center',
                    icon: 'circle',
                    itemWidth: 10,
                    itemHeight: 10,
                    itemStyle: {
                        borderRadius: 5,
                    },
                    textStyle: {
                        color: '#567BBB'
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    axisLabel:{
                        interval: 0,
                        lineStyle: {
                            color: '#A8D0FF'
                        },
                        textStyle: {
                            color: '#B6CBD5'
                        }
                    },
                    splitLine: {
                        show: false,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(168, 208, 255, 0.2)'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    data: x,
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        color: axisColor,
                        fontSize: 14,
                        margin: 14,
                        textStyle: {
                            color: '#B6CBD5'
                        },
                    },
                    // axisLine: {
                    //     show: true,
                    //     lineStyle: {
                    //         color: splitLineColor,
                    //     },
                    // },
                    min: 0,
                    minInterval: 1,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(168, 208, 255, 0.2)'
                        }
                    },
                },
                grid: {
                    top: '20px',
                    left: '30px',
                    right: '5px',
                    bottom: '50px'
                },
                series:  marks.map((i, j) => {
                    return {
                        type: 'custom',
                        name: i.name,
                        stack: 'Ad',
                        color: i.color,
                        // emphasis: {
                        // 	focus: 'series'
                        // },
                        renderItem: (params, api) => {
                            const location = api.coord([api.value(0), api.value(1)])
                            const yBase = calcSuperpositionVal(params, api, y)
                            const xAxisPoint = api.coord([api.value(0), 0])
                            return {
                                type: 'group',
                                children: [
                                    {
                                        type: 'CubeLeft',
                                        shape: {
                                            api,
                                            params,
                                            x: location[0],
                                            y: location[1],
                                            xAxisPoint,
                                            yBase,
                                        },
                                        style: {
                                            fill: LinearGradientColor[params.seriesIndex].left,
                                        },
                                    },
                                    {
                                        type: 'CubeRight',
                                        shape: {
                                            api,
                                            x: location[0],
                                            y: location[1],
                                            xAxisPoint,
                                            yBase,
                                        },
                                        style: {
                                            fill: LinearGradientColor[params.seriesIndex].right,
                                        },
                                    },
                                    {
                                        type: 'CubeTop',
                                        shape: {
                                            api,
                                            yValue: api.value(1),
                                            x: location[0],
                                            y: location[1],
                                            xAxisPoint,
                                            yBase,
                                        },
                                        style: {
                                            fill: LinearGradientColor[params.seriesIndex].top,
                                        },
                                    },
                                ],
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: (params) => {
                                    let colorList = [
                                        '#00E4EF', '#FFC963', '#4FAEFF', '#3DE87A'
                                    ]
                                    return colorList[params.dataIndex % colorList.length]
                                }
                            }
                        },
                        data: y[j],
                    }
                })
            }
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        },
        handleChange() {

        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .statis{
        height: 100%;
        width: 100%;  
    }
}
</style>