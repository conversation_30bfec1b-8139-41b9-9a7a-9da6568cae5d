<template>
  <div :style="$props.style" class="ui-image">
    <template v-if="imgUrl">
      <Spin v-if="spinning" fix>
        <img src="@/assets/img/default-img/image_loading.gif" alt />
      </Spin>
      <!-- img用法 -->
      <div
        v-if="inline"
        :class="[roundClass, viewerClass]"
        :style="radiusStyle"
        class="ui-image-div"
        v-viewer="{ inline: true }"
        @click="viewImageHandle"
      >
        <img
          v-show="!spinning"
          :style="{ imgStyle: imgUrl === src }"
          :src="imgUrl | imgProxyToHttps"
          :class="{ tileImage: imgUrl === src }"
          @load="loadImage"
          @error="errorImage"
          alt
        />
      </div>
      <div
        v-else
        :class="[roundClass, viewerClass]"
        :style="radiusStyle"
        class="ui-image-div"
        @click="viewImageHandle"
      >
        <img
          v-show="!spinning"
          :style="{ imgStyle: imgUrl === src }"
          :src="imgUrl | imgProxyToHttps"
          :class="{ tileImage: imgUrl === src, pad10: defaultImg }"
          @load="loadImage"
          @error="errorImage"
          alt
        />
      </div>
    </template>
    <template v-else>
      <div :class="[roundClass]" :style="radiusStyle" class="ui-image-div">
        <img
          :style="{ imgStyle: imgUrl === src }"
          :src="defaultImages[type]"
          :class="{ tileImage: imgUrl === src }"
          alt
        />
      </div>
    </template>
  </div>
</template>

<script>
import errorUrl from "@/assets/img/default-img/image_error.png";
export default {
  name: "UiImage",
  components: {},
  props: {
    inline: {
      type: Boolean, // 行内样式
      default: false,
    },
    src: {
      type: String, // 图片路径
      default: "",
    },
    imgStyle: {
      type: Object, // 图片样式
      default() {},
    },
    radius: {
      type: Number, // 图片圆角
      default: 0,
    },
    round: {
      type: Boolean, // 圆形图片
      default: false,
    },
    overLoadTime: {
      type: Number, // 图片超时时间
      default: 10000,
    },
    // 显示的默认图片类型
    type: {
      type: String,
      default: "null",
    },
    // 自定义默认图片路径
    defaultIcon: {
      type: String,
      default: "null",
    },
    // 是否放大预览
    viewer: {
      type: Boolean,
      default: false,
    },
    // 放大预览图片地址 - 大小图片不一致的情况
    viewerImage: {
      require: false,
      type: String, // 图片路径
      default: "",
    },
  },
  data() {
    return {
      imgUrl: "",
      spinning: true,
      complete: false, // 图片加载状态
      defaultImg: false,
      overLoadTimer: null,
      defaultImages: {
        null: require("@/assets/img/default-img/null_default.png"), // 默认
        home: require("@/assets/img/default-img/home_default.png"), // 房屋
        vehicle: require("@/assets/img/default-img/vehicle_default.png"), //车辆
        car: require("@/assets/img/default-img/vehicle_default.png"), //车辆
        people: require("@/assets/img/default-img/real_name_default.png"), //实名
        video: require("@/assets/img/default-img/people_default.png"), //视频
        device: require("@/assets/img/default-img/device_default.png"), //设备
        cloudDevice: require("@/assets/img/default-img/device_default.png"), //云搜设备，也不知道为啥要用个cloudDevice类型
        place: require("@/assets/img/default-img/place_default.png"), //场所
        sensing: require("@/assets/img/default-img/sensing_device_default.png"), //感知设备
      },
    };
  },
  computed: {
    roundClass() {
      return this.round ? "round" : "";
    },
    viewerClass() {
      return this.viewer ? "viewer" : "";
    },
    radiusStyle() {
      return this.radius > 0 ? { "border-radius": this.radius + "px" } : null;
    },
  },
  watch: {
    src: {
      handler() {
        this.imgUrl = this.src;
      },
    },
  },
  mounted() {
    this.complete = false;
    this.imgUrl = this.src;
    if (this.imgUrl) {
      this.fetchImg();
    }
  },
  methods: {
    errorImage() {
      clearTimeout(this.overLoadTimer);
      this.spinning = false;
      this.complete = false;

      if (this.defaultIcon != "null") {
        this.imgUrl = this.defaultIcon;
        this.defaultImg = true;
      } else {
        this.imgUrl = errorUrl;
      }
    },
    loadImage() {
      this.complete = true;
      clearTimeout(this.overLoadTimer);
      this.spinning = false;
      this.$emit("load");
    },
    // 图片超时
    fetchImg() {
      this.overLoadTimer = setTimeout(() => {
        if (this.complete) {
          clearTimeout(this.overLoadTimer);
        } else {
          this.errorImage();
        }
      }, this.overLoadTime);
    },
    // 预览图片
    viewImageHandle() {
      if (this.viewer) {
        this.$viewerApi({
          images: [
            this.$imgProxyToHttps(
              !!this.viewerImage ? this.viewerImage : this.imgUrl
            ),
          ],
        });
        this.$nextTick(() => {
          document
            .querySelector(".viewer-container")
            .addEventListener("click", (e) => {
              e.stopPropagation();
            });
        });
      }
    },
  },
};
</script>

<style lang="less">
.ui-image {
  width: 100%;
  height: 100%;
  position: relative;
  // z-index: 10;
  .ivu-spin-fix {
    background: #fff;
    .ivu-spin-main {
      width: 100%;
      height: 100%;
      .ivu-spin-text {
        width: 100%;
        height: 100%;
        & > img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
    }
  }
  .round {
    border-radius: 50%;
    overflow: hidden;
  }
  .viewer {
    cursor: pointer;
  }
  .ui-image-div {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    // background: #fff;
    cursor: pointer;
    // border: 1px solid #ededed;
    border-radius: 0;
    img {
      margin: 0 auto;
      width: 100%;
      height: 100%;
      // object-fit: cover;
    }
    .tileImage {
      //   width: 100%;
      //   height: 100%;
    }
  }
}

.pad10 {
  padding: 10px;
}

.small {
  .tileImage {
    padding: 10px;
  }
}
</style>
