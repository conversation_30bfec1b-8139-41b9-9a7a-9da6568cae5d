<template>
  <div class="ui-image" :style="$props.style">
    <Spin v-if="spinning" fix>
      <img :src="loadingImage" alt />
    </Spin>
    <!-- img用法 -->
    <div class="ui-image-div image-box" :class="[roundClass]" :style="radiusStyle">
      <img
        ref="imgRef"
        v-show="!spinning"
        :class="[src ? '' : 'no-image']"
        :style="imgUrl === src ? imgStyle : ''"
        :src="imgUrl"
        @load="loadImage"
        @error="errorImage"
      />
    </div>
  </div>
</template>

<script>
import errorUrl from '@/assets/img/load-error-img.png';
import errorUrlLight from '@/assets/img/load-error-img-light.png';
import nodataImg from '@/assets/img/common/nodata-img.png';
import nodataImgLight from '@/assets/img/common/nodata-img-light.png';
import loadingImg from '@/assets/img/load-img.gif';
import loadingImgLight from '@/assets/img/load-img-light.gif';
import loadingImgDeepBlue from '@/assets/img/load-img-deepBlue.gif';
import { mapGetters } from 'vuex';
export default {
  name: 'ui-image',
  components: {},
  props: {
    src: {
      type: String, // 图片路径
      default: '',
    },
    imgStyle: {
      type: Object, // 图片样式
      default() {},
    },
    radius: {
      type: Number, // 图片圆角
      default: 0,
    },
    round: {
      type: Boolean, // 圆形图片
      default: false,
    },
    overLoadTime: {
      type: Number, // 图片超时时间
      default: 60000,
    },
  },
  data() {
    return {
      imgUrl: null,
      spinning: true,
      complete: false, //图片加载状态
      overLoadTimer: null,
      loadingImage: '',
      imageSize: {
        width: null,
        height: null,
      }, // 原始图片大小
    };
  },
  watch: {
    src: {
      handler() {
        this.imgUrl = this.src;
        // this.fetchImg()
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
    roundClass() {
      return this.round ? 'round' : '';
    },
    radiusStyle() {
      return this.radius > 0 ? { 'border-radius': this.radius + 'px' } : null;
    },
  },
  mounted() {
    this.complete = false;
    this.imgUrl = this.src;
    this.fetchImg();
  },
  methods: {
    errorImage() {
      clearTimeout(this.overLoadTimer);
      this.spinning = false;
      this.complete = false;
      if (this.themeType === 'dark') {
        this.imgUrl = this.imgUrl ? errorUrl : nodataImg;
        this.loadingImage = loadingImg;
      } else if (this.themeType === 'deepBlue') {
        this.imgUrl = this.imgUrl ? errorUrlLight : nodataImgLight;
        this.loadingImage = loadingImgDeepBlue;
      } else {
        this.imgUrl = this.imgUrl ? errorUrlLight : nodataImgLight;
        this.loadingImage = loadingImgLight;
      }
    },
    loadImage(e) {
      this.complete = true;
      clearTimeout(this.overLoadTimer);
      this.spinning = false;
      this.imageSize.width = e.target.naturalWidth || 0;
      this.imageSize.height = e.target.naturalHeight || 0;
      this.$emit('loadImage', this.imageSize);
    },
    // 图片超时
    fetchImg() {
      this.overLoadTimer = setTimeout(() => {
        if (this.complete) {
          clearTimeout(this.overLoadTimer);
        } else {
          this.errorImage();
        }
      }, this.overLoadTime);
    },
  },
};
</script>

<style lang="less">
.ui-image {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 10;
  .ivu-spin-fix {
    background: var(--bg-content);
    img {
      // width: 60px;
      // height: 60px;
    }
  }
  .round {
    border-radius: 50%;
    overflow: hidden;
  }

  .ui-image-div {
    display: flex;
    align-items: center;
    height: 100%;
    background: var(--bg-content);
    img {
      margin: 0 auto;
      max-width: 100%;
      max-height: 100%;
    }
    .no-image {
      max-width: 100px;
      max-height: 100px;
    }
  }
}
</style>
