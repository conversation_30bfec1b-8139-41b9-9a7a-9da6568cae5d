<template>
  <basic-select v-bind="getAttrs" v-on="$listeners" @reset="reset">
    <template #select>
      <Select
        v-model="tempErrorCodes.reason"
        placeholder="请选择不合格原因"
        class="width-md"
        multiple
        clearable
        @on-change="onChangeReason"
      >
        <Option v-for="item in errorCodesOptionsReason" :value="item" :key="item">{{ item }}</Option>
      </Select>
      <span class="base-text-color ml-xs mr-xs">-</span>
      <Select
        v-model="tempErrorCodes.desc"
        placeholder="请选择不合格原因"
        class="width-md"
        multiple
        clearable
        :disabled="tempErrorCodes.reason.length === 0"
      >
        <Option v-for="item in errorCodesOptionsDesc" :value="item.code" :key="item.code">{{ item.desc }}</Option>
      </Select>
    </template>
    <!-- 表格插槽 -->
    <template slot="outcome" slot-scope="{ row }">
      <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
      <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      <Tooltip
        transfer
        placement="bottom"
        v-if="
          row.detectionMode != null &&
          (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
        "
      >
        <i class="icon-font icon-wenhao ml-xs f-12" :style="{ color: 'var(--color-warning)' }"> </i>
        <div slot="content">
          <json-viewer
            :expand-depth="5"
            v-if="row.dateImageText != null"
            :value="JSON.parse(row.dateImageText)"
            theme="my-awesome-json-theme"
          ></json-viewer>
          <json-viewer
            :expand-depth="5"
            v-if="row.additionalImageText != null"
            :value="JSON.parse(row.additionalImageText)"
            theme="my-awesome-json-theme"
          ></json-viewer>
          <json-viewer
            :expand-depth="5"
            v-if="row.areaImageText != null"
            :value="JSON.parse(row.areaImageText)"
            theme="my-awesome-json-theme"
          ></json-viewer>
        </div>
      </Tooltip>
    </template>
    <template #detectionMode="{ row }">
      {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
    </template>
    <template #phyStatus="{ row }">
      <span>
        {{ !row.phyStatusText ? '--' : row.phyStatusText }}
      </span>
    </template>

    <template #deviceId="{ row }">
      <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
        row.deviceId
      }}</span>
    </template>
    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template #tagNames="{ row }">
      <tags-more :tag-list="row.tagList || []"></tags-more>
    </template>
  </basic-select>
</template>
<script>
import errorCodesMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/errorCodesMixin.js';
import dealWatch from '@/mixins/deal-watch';
export default {
  mixins: [errorCodesMixin, dealWatch],
  props: {
    qualifiedColorConfig: {},
  },
  data() {
    return {
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
      },
    };
  },
  created() {
    // errorCodesMixin.js
    this.getQualificationList(this.$attrs.moduleData);
  },
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
