<template>
  <div>
    <ui-modal title="代码表" v-model="visible" :width="1096" :footerHide="true">
      <div class="form">
        <label for="关键词" class="based-field-label">关键词</label>
        <Input v-model="searchData.roleName" placeholder="请输入字典项名称/代码" style="width: 200px" />
        <div class="inline ml-sm">
          <Button type="primary" @click="search">搜索</Button>
          <Button class="ml-sm" @click="resetSearchDataMx1(searchData, search)">重置</Button>
        </div>
        <Button
          class="fr ml-sm"
          type="primary"
          @click="add()"
          v-permission="{ route: 'dictionary', permission: 'codeTableAdd' }"
        >
          <i class="icon-font icon-tianjia f-12"></i>
          <span class="inline vt-middle ml-xs">新增数据</span>
        </Button>
      </div>
      <div class="left-div">
        <ui-table
          class="ui-table"
          :loading="loading"
          :table-columns="tableColumns"
          :table-data="tableData"
          ref="table"
          :minus-height="400"
        >
          <template slot-scope="{ row }" slot="action">
            <ui-btn-tip
              class="mr-md f-14"
              icon="icon-bianji2"
              content="编辑"
              @click.native="edit(row)"
              v-permission="{
                route: 'dictionary',
                permission: 'codeTableEdit',
              }"
            ></ui-btn-tip>
            <ui-btn-tip
              class="f-14"
              icon="icon-shanchu3"
              content="删除"
              @click.native="deleteItem(row)"
              v-permission="{ route: 'dictionary', permission: 'codeTableDel' }"
            ></ui-btn-tip>
          </template>
        </ui-table>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
        <!-- 新增or编辑数据 -->
        <codeTableAddOrEdit @search="search" ref="codeTableAddOrEdit" />
      </div>
    </ui-modal>
  </div>
</template>

<script>
import metadatamanagement from '@/config/api/metadatamanagement';
export default {
  data() {
    return {
      loading: false,
      visible: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        identCode: '',
        searchValue: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      tableColumns: [
        { title: '内部标识代码', key: 'identCode' },
        { title: '字典项名称', key: 'dictName' },
        { title: '字典项代码', key: 'dictCode' },
        {
          title: '操作',
          slot: 'action',
          fixed: 'right',
          width: 70,
          align: 'center',
        },
      ],
      tableData: [],
      identCode: '',
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 检索
    search() {
      this.infoList();
    },
    async infoList() {
      try {
        let res = await this.$http.post(metadatamanagement.detailPageList, this.searchData);
        this.tableData = res.data.data.entities || [];
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      }
    },
    resetSearchDataMx1() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        identCode: this.searchData.identCode,
        searchValue: '',
        params: { pageNumber: 1, pageSize: 20 },
      };
      this.infoList();
    },
    open(row) {
      this.searchData.identCode = row.identCode;
      this.identCode = row.identCode;
      this.visible = true;
      this.infoList();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
    add() {
      this.$refs.codeTableAddOrEdit.open({ identCode: this.identCode }, '新增数据');
    },
    edit(row) {
      this.$refs.codeTableAddOrEdit.open(Object.assign(row, { identCode: this.identCode }), '编辑数据');
    },
    // 删除数据
    deleteItem(item) {
      this.$UiConfirm({
        content: `您将要删除字典 ${item.dictName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.remove(item);
        })
        .catch((res) => {
          console.log(res);
        });
      // this.$Modal.confirm({
      // 	title: "警告",
      // 	content: `您将要删除字典 ${item.dictName}，是否确认?`,
      // 	onOk: () => {
      // 		this.remove(item)
      // 	}
      // });
    },
    // 删除
    async remove(item) {
      try {
        let res = await this.$http.delete(metadatamanagement.detailRemove + item.id);
        if (res.data.code === 200) {
          this.$Message.success('删除成功');
          this.infoList();
        } else {
          this.$Message.error('删除失败');
        }
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {},
  props: {},
  components: {
    codeTableAddOrEdit: require('./code-table-addoredit.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.form {
  padding: 20px;
  .based-field-label {
    color: var(--color-label);
    font-size: 14px;
    padding-right: 12px;
  }
  button {
    margin-left: 12px;
  }
}
.mr30 {
  margin-right: 30px;
}
</style>
