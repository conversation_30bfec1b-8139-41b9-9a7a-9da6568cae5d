<template>
  <div class="analysis-plan" v-ui-loading="{ loading: loading }">
    <plan-time-picker
      class="inline"
      ref="planTimePicker"
      :time-picker-data="timePickerData"
      @handleUpatePlanData="handleUpatePlanData"
    ></plan-time-picker>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    dataType: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      loading: false,
      formData: {},
      timePickerData: {
        cronType: '1',
        cronData: [],
        timePoints: [],
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      try {
        this.loading = true;
        let params = {
          dataType: this.dataType,
        };
        const res = await this.$http.post(equipmentassets.queryQualityJobCron, params);
        this.timePickerData = JSON.parse(res.data?.data?.executorParam);
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleUpatePlanData(val) {
      this.formData.cronType = val.cronType;
      this.formData.cronData = val.cronData;
      if (val.timePoints) {
        this.formData.timePoints = val.timePoints;
      }
    },
    save() {
      const configInfo = this.formData;
      this.$emit('save', configInfo);
    },
  },
  watch: {},
  components: {
    PlanTimePicker:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/plan-time-picker')
        .default,
  },
};
</script>
<style lang="less" scoped>
.analysis-plan {
  // position: absolute;
  // top: 50%;
  // left: 50%;
  // transform: translateX(-50%) translateY(-50%);
  height: 100%;
  @{_deep}.plan-time-picker {
    position: absolute;
    width: auto;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
}
</style>
