<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="年龄:">
      <div class="flex-box">
        <Input
          v-model="queryParam.minAge"
          placeholder="请输入"
          style="width: 80px"
        ></Input>
        <div class="separtor"></div>
        <Input
          v-model="queryParam.maxAge"
          placeholder="请输入"
          style="width: 80px"
        ></Input>
      </div>
    </FormItem>
    <FormItem label="民族:" prop="nation">
      <Select
        v-model="queryParam.nation"
        placeholder="请选择"
        :multiple="false"
        transfer
        :max-tag-count="1"
      >
        <Option
          :value="item.dataKey"
          v-for="item in nationTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
export default {
  data() {
    return {
      queryParam: {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        minAge: "",
        maxAge: "",
        nation: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      partiTypeList: "dictionary/getPartiTypeList", // 案件类型 这个字典值后边可能要单独拎出来一个给未成年人用
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    if (this.$route.query.idCardNo) {
      this.queryParam.idCardNo = this.$route.query.idCardNo;
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 重置
     */
    reset() {
      this.queryParam = {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        minAge: "",
        maxAge: "",
        nation: "",
      };
    },
    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.selectTag {
  margin-top: 4px;
}
.gerling {
  color: #f29f4c;
}
.hk {
  color: #2c86f8;
}
.separtor {
  margin: 0 10px;
  width: 16px;
  height: 1px;
  background: rgba(0, 0, 0, 0.45);
}
.flex-box {
  display: flex;
  align-items: center;
}
.select-record-kind {
  /deep/.ivu-select-item {
    width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
