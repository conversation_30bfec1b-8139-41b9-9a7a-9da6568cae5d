<template>
  <div class="demo">
    <h2>color</h2>
    <div class="color-list">
      <div class="color-item">
        <div class="rect primary-color">
          <div>主题</div>
          <div>#2C86F8</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect info-color">
          <div>信息</div>
          <div>#888888</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect success-color">
          <div>成功</div>
          <div>#1FAF81</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect warning-color">
          <div>警告</div>
          <div>#F29F4C</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect error-color">
          <div>错误</div>
          <div>#EA4A36</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect normal-color">
          <div>默认</div>
          <div>#888888</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect disabled-color">
          <div>禁用</div>
          <div>#E9E9E9</div>
        </div>
      </div>
    </div>
    <h2>link-color</h2>
    <div class="color-list">
      <div class="color-item">
        <div class="rect link-normal-color">
          <div>link-normal</div>
          <div>#2C86F8</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect link-hover-color">
          <div>link-hover</div>
          <div>#4597FF</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect link-active-color">
          <div>link-active</div>
          <div>#1A74E7</div>
        </div>
      </div>
    </div>
    <h2>font-color</h2>
    <div class="color-list">
      <div class="color-item">
        <div class="rect title-font-color">
          <div>标题/表头</div>
          <div>#000 90%</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect text-font-color">
          <div>正文</div>
          <div>#000 80%</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect input-font-color">
          <div>输入框标题</div>
          <div>#000 75%</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect secondary-font-color">
          <div>次要</div>
          <div>#000 60%</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect auxiliary-font-color">
          <div>辅助</div>
          <div>#000 35%</div>
        </div>
      </div>
    </div>
    <h2>linear-gradient-color</h2>
    <div class="color-list">
      <div class="color-item">
        <div class="rect linear-gradient-color">
          <div>渐变</div>
          <div>#5BA3FF #2C86F8</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect linear-gradient-hover-color">
          <div>渐变-hover</div>
          <div>#76B2FF #3A91FF</div>
        </div>
      </div>
      <div class="color-item">
        <div class="rect linear-gradient-active-color">
          <div>渐变-active</div>
          <div>#4794F6 #1A70DD</div>
        </div>
      </div>
    </div>
    <h2>border-color</h2>
    <div class="color-list">
      <div class="color-item">
        <div class="rect border-color">
          <div>边框</div>
          <div>#D3D7DE</div>
        </div>
      </div>
    </div>
    <h2>font-size</h2>
    <div class="font-list">
      <div class="mr font-size12">标题12</div>
      <div class="mr font-size14">标题14</div>
      <div class="mr font-size16">标题16</div>
      <div class="mr font-size18">标题18</div>
      <div class="mr font-size20">标题20</div>
    </div>
    <h1>iview</h1>
    <h2>Checkbox</h2>
    <div class="checkbox-list">
      <Checkbox>未选中</Checkbox>
      <Checkbox :value="true">选中</Checkbox>
      <Checkbox :indeterminate="true">半选</Checkbox>
      <Checkbox disabled>未选中禁用</Checkbox>
      <Checkbox disabled :value="true">选中禁用</Checkbox>
      <Checkbox disabled :indeterminate="true">半选禁用</Checkbox>
    </div>
    <h2>Radio</h2>
    <div class="checkbox-list">
      <Radio>未选中</Radio>
      <Radio :value="true">选中</Radio>
      <Radio disabled>未选中禁用</Radio>
      <Radio disabled :value="true">选中禁用</Radio>
    </div>
    <h2>Switch</h2>
    <div class="checkbox-list">
      <i-switch />未选中 <i-switch :value="true" />选中
    </div>
    <h2>Input</h2>
    <div class="checkbox-list">
      <Form>
        <FormItem>
          <Input placeholder="请输入" style="width: 200px" />
        </FormItem>
        <FormItem>
          <Input
            disabled
            placeholder="请输入"
            value="禁用"
            style="width: 200px"
          />
        </FormItem>
        <FormItem error="请输入">
          <Input placeholder="请输入" value="" style="width: 200px" />
        </FormItem>
      </Form>
    </div>
    <h2>Select</h2>
    <div class="checkbox-list">
      <Form>
        <FormItem>
          <Select style="width: 200px">
            <Option v-for="item in 2" :value="item" :key="item">选择1</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select disabled style="width: 200px">
            <Option v-for="item in 2" :value="item" :key="item">选择1</Option>
          </Select>
        </FormItem>
        <FormItem error="请输入">
          <Select style="width: 200px">
            <Option v-for="item in 2" :value="item" :key="item">选择1</Option>
          </Select>
        </FormItem>
      </Form>
    </div>
    <h2>Button</h2>
    <div class="checkbox-list">
      <Button type="primary">Primary</Button>
      <Button type="primary" disabled>Primary</Button>
      <Button type="primary" icon="ios-search">Primary</Button>
      <Button>Default</Button>
      <Button disabled>Default</Button>
      <Button icon="ios-search">Default</Button>
      <Button type="dashed">Dashed</Button>
      <Button type="dashed" disabled>Dashed</Button>
      <Button type="text">Text</Button>
      <Button type="text" class="link-btn">Link</Button>
      <Button type="primary" size="large">large</Button>
      <Button type="primary" size="small">small</Button>
    </div>
    <h2>Table</h2>
    <div>
      <Table :columns="columns1" :data="data1"></Table>
    </div>
    <h2>Page</h2>
    <Page :total="100" show-elevator show-sizer />
  </div>
</template>
<script>
export default {
  data() {
    return {
      columns1: [
        {
          type: "selection",
          width: 60,
          align: "center",
        },
        {
          title: "普通文本",
          key: "name",
        },
        {
          title: "Age",
          key: "age",
        },
        {
          title: "Address",
          key: "address",
        },
      ],
      data1: [
        {
          name: "John Brown",
          age: 18,
          address: "New York No. 1 Lake Park",
          date: "2016-10-03",
        },
        {
          name: "Jim Green",
          age: 24,
          address: "London No. 1 Lake Park",
          date: "2016-10-01",
        },
        {
          name: "Joe Black",
          age: 30,
          address: "Sydney No. 1 Lake Park",
          date: "2016-10-02",
        },
        {
          name: "Jon Snow",
          age: 26,
          address: "Ottawa No. 2 Lake Park",
          date: "2016-10-04",
        },
      ],
    };
  },
  methods: {},
};
</script>
<style lang="less" scoped>
// @import url('./demo.less');
</style>
