<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'

/**
 *  参考  https://www.makeapie.com/editor.html?c=xklYtZRN3Y
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    smooth: { // 是否平滑曲线
      type: Boolean,
      default: false
    },
    // 图表样式
    echartStyle: {
      type: Object,
      default: () => {
        return {
          lineColor: '#19a3df', // 线条颜色
          startColor: 'rgba(88,255,255,0.2)',
          endColor: 'rgba(88,255,255,0)',
          axisLineColor: '#243753',
          yAxisLineType: 'dashed', // Y轴背景区域线条样式dashed/solid
          yAxisLineColor: '#FFFFFF', // Y轴背景区域线条颜色
          xAxisAxisTick: false, // Y轴刻度
          showSymbol: false // 是否显示 symbol, 如果 false 则只有在 tooltip hover 的时候显示。
        }
      }
    },
    names: {
      type: Array,
      default: () => ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
    },
    values: {
      type: Array,
      default: () => [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    }
  },
  data () {
    return {
      myEchart: null

    }
  },
  computed: {},
  watch: {},
  filter: {},
  mounted () {
    this.init()
  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      this.myEchart = echarts.init(this.$refs.echart)
      var option = {
        grid: {
          top: 10,
          bottom: 10,
          right: 0,
          left: 0,
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          axisPointer: {
            lineStyle: {
              type: 'solid',
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 255, 255, 0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: 'rgba(255, 255, 255, 0.5)' // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 255, 255, 0)' // 100% 处的颜色
                  }
                ],
                global: false // 缺省为 false
              }
            }
          },
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: true, // 边界间隙，起始点跟轴之间的距离
            axisLabel: {
              formatter: '{value}',
              fontSize: 12,
              margin: 10,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.75)'
              }
            },
            axisLine: {
              lineStyle: {
                color: this.echartStyle.axisLineColor,
                width: 2
              }
            },
            axisTick: {
              show: this.echartStyle.xAxisAxisTick,
              lineStyle: {
                color: this.echartStyle.axisLineColor
              }
            },
            data: this.names
          }
        ],
        yAxis: [
          {
            boundaryGap: false,
            type: 'value',
            minInterval: 1,
            axisLabel: {
              textStyle: {
                color: 'rgba(255, 255, 255, 0.75)'
              }
            },
            nameTextStyle: {
              color: '#fff',
              fontSize: 12,
              lineHeight: 40
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: this.echartStyle.yAxisLineType,
                color: this.echartStyle.yAxisLineColor
              }
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#283352'
              }
            },
            axisTick: {
              show: false
            }
          }
        ],
        series: [
          {
            name: '',
            type: 'line',
            smooth: this.smooth,
            showSymbol: this.echartStyle.showSymbol,
            symbol: 'circle',
            symbolSize: 8, // 圈
            zlevel: 1,
            itemStyle: {
              normal: {
                color: this.echartStyle.lineColor,
                borderWidth: 2,
                borderColor: '#071B39'
              }
            },
            lineStyle: {
              normal: {
                width: 2, // 线宽
                color: this.echartStyle.lineColor
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: this.echartStyle.startColor
                    },
                    {
                      offset: 1,
                      color: this.echartStyle.endColor
                    }
                  ],
                  false
                )
              }
            },
            emphasis: {
              itemStyle: {
                color: '#fff',
                borderWidth: 2,
                borderColor: this.echartStyle.lineColor
              }
            },
            data: this.values
          }
        ]
      }

      this.myEchart.setOption(option)
      window.addEventListener('resize', () => this.myEchart.resize())
    },

    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
