<template>
  <div class="height-full">
    <div class="equipmentimport auto-fill">
      <div class="search-module mb-lg">
        <ui-label class="inline rigth-margin bottom-margin" label="公告名称">
          <Input v-model="searchData.title" class="width-md" placeholder="请输入公告名称"></Input>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
          <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, startSearch)">重置</Button>
        </div>
        <Button type="primary" class="fr" @click="addNotice">
          <i class="icon-font icon-tianjia f-12 mr-sm"></i>
          <span class="inline vt-middle ml-sm">新增公告</span>
        </Button>
      </div>
      <div class="table-module auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #deviceId="{ row }">
            <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
              row.deviceId
            }}</span>
          </template>
          <template #switch="{ row }">
            <i-switch
              class="iswitch"
              size="small"
              true-value="0"
              false-value="1"
              :value="row.status"
              @on-change="handleSwitch($event, row)"
            />
          </template>
          <template #switchTop="{ row }">
            <i-switch
              class="iswitch"
              size="small"
              true-value="0"
              false-value="1"
              :value="row.top"
              @on-change="handleTopSwitch($event, row)"
            />
          </template>
          <template #option="{ row }">
            <ui-btn-tip icon="icon-bianji2" class="mr-md" content="编辑" @click.native="editNotice(row)"></ui-btn-tip>
            <ui-btn-tip icon="icon-shanchu3" content="删除" @click.native="deleteRow(row)"></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      <loading v-if="importLoading"> 导入中，请勿刷新页面，请耐心等待 </loading>
    </div>
    <add-notice v-model="addNoticeShow" @saveNoticeAction="saveNoticeAction" :active-row="activeRow"></add-notice>
  </div>
</template>
<script>
import systemconfig from '@/config/api/systemconfig';
export default {
  name: 'equipmentimport',
  props: {},
  data() {
    return {
      addNoticeShow: false,
      activeRow: {
        id: -1,
        title: '',
        content: '',
        fileVos: [],
      },
      searchData: { title: '' },
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'center' },
        {
          title: '公告名称',
          key: 'title',
          align: 'left',
        },
        {
          title: '公告内容',
          width: 400,
          key: 'content',
          align: 'left',
          tooltip: true,
        },
        {
          title: '发布状态',
          align: 'left',
          slot: 'switch',
        },
        {
          title: '置顶状态',
          align: 'left',
          slot: 'switchTop',
        },
        { title: '操作人', key: 'modifier', align: 'left' },
        { title: '操作时间', key: 'modifyTime', align: 'left' },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          align: 'center',
          width: 100,
        },
      ],
      deleteData: [],
      importLoading: false,
      createLoading: false,
      dataStatuList: [],
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.init();
  },
  methods: {
    startSearch() {
      this.pageData.pageNum = 1;
      this.init();
    },
    addNotice() {
      this.activeRow = {
        id: -1,
        title: '',
        content: '',
        fileVos: [],
      };
      this.addNoticeShow = true;
    },
    editNotice(row) {
      this.activeRow = Object.assign({}, row);
      this.addNoticeShow = true;
    },
    handleSwitch($event, row) {
      this.activeRow = Object.assign({}, row);
      this.$UiConfirm({
        content: `确定${$event ? '' : '取消'}发布这条公告吗?`,
        title: '警告',
      }).then(() => {
        this.saveNoticeAction({
          id: row.id,
          // title: row.title,
          // content: row.content,
          // top: row.top,
          status: $event,
        });
      });
    },
    handleTopSwitch($event, row) {
      console.log($event, row, row.top);
      this.activeRow = Object.assign({}, row);
      this.$UiConfirm({
        content: `${$event === '0' ? '确定' : '取消'}置顶这条公告?`,
        title: '警告',
      }).then(() => {
        this.saveNoticeAction({
          id: row.id,
          // title: row.title,
          // content: row.content,
          // status: row.status,
          top: $event,
        });
      });
    },
    async init() {
      try {
        this.loading = true;
        let params = {
          ...this.searchData,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        let res = await this.$http.post(systemconfig.notifyPageList, params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNumber = 1;
      this.pageData.pageSize = val;
      this.init();
    },
    deleteRow(row) {
      this.$UiConfirm({
        content: `您将要删除  ${row.title}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteInit(row);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    deleteInit(row) {
      this.$http
        .delete(`${systemconfig.deleteNotify}/${row.id}`)
        .then(() => {
          this.$Message.success('删除成功');
          this.startSearch();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    async saveNoticeAction(searchData) {
      try {
        this.loading = true;
        let interfaceName = 'addNotify';
        let params = { ...searchData };
        this.activeRow.id !== -1 ? (interfaceName = 'updateNotify') : null;
        this.activeRow.id !== -1 ? (params.id = this.activeRow.id) : null;
        await this.$http.post(systemconfig[interfaceName], params);
        this.$Message.success(`${this.activeRow.id !== -1 ? '修改' : '新增'}成功`);
        this.addNoticeShow = false;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
        this.init();
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    addNotice: require('./add-notice.vue').default,
  },
};
</script>
<style lang="less" scoped>
.equipmentimport {
  overflow: hidden;
  position: relative;
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
  .table-module {
    position: relative;
    @{_deep}.ivu-table td.content-class {
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
