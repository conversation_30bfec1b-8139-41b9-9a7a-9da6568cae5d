<template>
  <div class="">
    <icon-statistics :statistics-list="abnormalList" :isflexfix="false"></icon-statistics>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      listyle: {
        height: `${353 / 192}rem`,
        width: `${102 / 192}rem`,
      },
      abnormalList: [
        {
          key: 'total',
          name: '检测地市数量',
          value: 0,
          icon: 'icon-gongdanzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
        {
          key: 'completeAmount',
          name: '不达标地市数量',
          value: 0,
          icon: 'icon-anshiwanchenggongdanzongshu',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
        },
        {
          key: 'delayAmount',
          name: '视频监控设备总量',
          value: 0,
          icon: 'icon-yanqiwanchenggongdanshuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color6',
        },
        {
          key: 'delaySignAmount',
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-yanqiqianshougongdanshuliang',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color3',
        },
        {
          key: 'processingAgainAmount',
          name: '检测合格设备数量',
          value: 0,
          icon: 'icon-duocichuliweiwancheng',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color4',
        },
        {
          key: 'completeAgainAmount',
          name: '时钟准确率',
          value: 0,
          icon: 'icon-duocichuliyiwancheng',
          iconColor: 'icon-bg11',
          liBg: 'li-bg11',
          type: 'percentage', // number数字 percentage 百分比
          textColor: 'color11',
        },
      ],
    };
  },
  created() {},
  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    /* ----------------------------------------- 自定义方法 ----------------------------------------- */
  },
  watch: {},
  components: {
    IconStatistics: require('@/components/icon-statistics').default,
  },
};
</script>
<style lang="less" scoped></style>
