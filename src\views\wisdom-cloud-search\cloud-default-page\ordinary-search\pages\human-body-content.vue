<!--
    * @FileDescription: 人体
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="main-container">
        <div class="search-bar">
            <search-human ref="searchBar"
                @search="searchHandle"
                @reset="resetHandle" />
        </div>
        <div class="table-container">
            <div class="btn-list">
                <Button class="mr" @click="handleSort('absTime')" size="small">
                    <Icon type="md-arrow-round-down" v-if="!timeUpDown"/> 
                    <Icon type="md-arrow-round-up" v-else/>
                    时间排序
                </Button>
            </div>
            <div class="table-content">
                <div class="list-card box-1" v-for="(item, index) in dataList" :key="index">
                    <div class="collection paddingIcon">
                        <div class="bg"></div>
                        <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
                        <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
                    </div>
                     <p class="img-content">
                        <span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
                        <ui-image :src="item.traitImg" alt="动态库" @click.native="handleDetail(item, index)" />
                        <!-- <img :src="item.traitImg" alt="" v-viewer /> -->
                    </p>
                    <div class="bottom-info">
                        <time>
                            <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                <i class="iconfont icon-time"></i>
                            </Tooltip>
                            {{ item.absTime }}
                        </time>
                        <p>
                            <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                <i class="iconfont icon-location"></i>
                            </Tooltip>
                            <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
                        </p>
                    </div>
                    <!-- <div class="operate-bar">
                        <p class="operate-content">
                            <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" />
                            <ui-btn-tip content="分析" icon="icon-fenxi" />
                            <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
                        </p>
                    </div> -->
                </div>
                <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
            </div>
            <ui-empty v-if="dataList.length === 0"></ui-empty>
            <ui-loading v-if="listLoading"></ui-loading>
            <!-- 分页 -->
            <ui-page :current="pageInfo.pageNumber" 
                :total="total" 
                countTotal
                :page-size="pageInfo.pageSize" 
                :page-size-opts="[27, 54, 81, 108]"
                @pageChange="pageChange" 
                @pageSizeChange="pageSizeChange">
            </ui-page>
        </div>
        <details-modal v-show="humanShow" ref='humanbody'
            @prePage="prePage"
            @nextPage="nextPage" 
            @close="humanShow = false"></details-modal>
    </div>
</template>

<script>
import detailsModal from '@/components/detail/details-modal.vue';
import searchHuman from '../components/search-human.vue';
import { queryHumanRecordSearch } from '@/api/wisdom-cloud-search';
import { addCollection, deleteMyFavorite } from '@/api/user';
import { myMixins } from '../../components/mixin/index.js';
export default {
    name: '',
    props:{
        // 首页参数
        indexSearchData: {
            type: Object,
            default: () => { }
        },
    },
    mixins: [myMixins], //全局的mixin
    components:{
        searchHuman,
        detailsModal  
    },
    data () {
        return {
            dataList: [],
            listLoading: false,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize : 27
            },
            humanShow: false,
            queryParam: {},
            timeUpDown: false
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    activated() {
        this.$nextTick(() =>{
            this.queryList()
        })
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        searchHandle() {
            this.pageInfo.pageNumber = 1;
            this.queryList();
        },
        queryList(page = 0) {
            let queryParam = this.$refs.searchBar.queryParam;
            queryParam = { ...queryParam, ...this.indexSearchData, ...this.pageInfo };
            let deviceIds = queryParam.selectDeviceList.map(item => {
                return item.deviceId
            })
            this.queryParam = queryParam;
            this.dispTime()
            // 抓拍时段时间处理
            let { startDate, endDate } = this.timeTransition(this.queryParam.timeSlot, this.queryParam.timeSlotArr);
            this.queryParam.startDate = startDate;
            this.queryParam.endDate = endDate;
            this.queryParam.deviceIds = deviceIds;
            this.getDataList(page);
        },
        resetHandle() {
            this.pageInfo = {
                pageNumber: 1,
                pageSize : 27
            };
            this.queryParam = { 
                ...this.$parent.indexSearchData, 
                ...this.pageInfo, 
                ...this.$refs.searchBar.queryParam
            }
            this.dispTime()
            this.getDataList()
        },
        async getDataList(page = 0) {
            this.listLoading = true;
            queryHumanRecordSearch(this.queryParam)
            .then(res => {
                this.total = res.data.total;
                this.dataList = res.data.entities;
                if(page == 1) {
                    this.$refs.humanbody.prePage(this.dataList)
                }else if(page == 2) {
                    this.$refs.humanbody.nextPage(this.dataList)
                }
            })
            .finally(() => {
                this.listLoading = false
            })
        },
        // 详情
        handleDetail(row, index) {
            this.humanShow = true;
            this.$refs.humanbody.init(row, this.dataList, index, 1, this.pageInfo.pageNumber)
        },
        collection(item, flag) {
            var param = {
                favoriteObjectId: item.recordId,
                favoriteObjectType: 16,
            }
            if(flag == 1){
                addCollection(param).then(res => {
                    this.$Message.success("收藏成功");
                    this.getDataList()
                })
            }else{
                deleteMyFavorite(param).then(res => {
                    this.$Message.success("取消收藏成功");
                    this.getDataList()
                })
            }
        },
        archivesPage() {

        },
        pageChange(size) {
            this.pageInfo.pageNumber = size;
            this.queryList()
        },
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1
            this.pageInfo.pageSize = size
            this.queryList()
        },
        /**
         * 首页调用方法
         */
        indexSearchHandle () {
            this.pageInfo.pageNumber = 1
            this.queryParam = {
                keyWords: this.indexSearchData.keyWords
            }
            this.timeAssig()
            this.indexSearchData.features = []
            this.$refs.searchBar.resetHandle()
        },
        /**
         * 上一个
         */
        prePage(pageNum) {
            if(pageNum < 1) {
                this.$Message.warning("已经是第一个了")
                return
            } else {
                this.pageInfo.pageNumber = pageNum;
                this.queryList(1)
            }
        },
        /**
         * 下一个
         */
        async nextPage(pageNum) {
            this.pageInfo.pageNumber = pageNum;
            let num = this.pageInfo.pageNumber;
            let size = this.pageInfo.pageSize;
            if(this.total <= num*size) {
                this.$Message.warning("已经是最后一个了")
                return
            }else{
                this.queryList(2)
            }
        },
    }
}
</script>

<style lang='less' scoped>
@import 'style/index';

</style>
