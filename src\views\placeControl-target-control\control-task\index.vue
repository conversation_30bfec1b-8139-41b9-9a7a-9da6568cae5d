<!--
 * @Author: du<PERSON>en
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-13 11:37:25
 * @Description: 
-->
<template>
  <div class="layout">
    <!-- <tabsPage v-model="selectLi" :list="componentList" /> -->
    <keep-alive>
      <component
        :is="currentComponent.componentName"
        :key="selectLi"
        :compareType="selectLi"
      />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import CriminalRecord from "./criminal-record/index.vue";

export default {
  name: "alarm-manager",
  components: {
    tabsPage,
    CriminalRecord,
  },
  data() {
    return {
      selectLi: 1,
      componentList: [
        {
          label: "前科人员发现报警",
          value: 1,
          componentName: "CriminalRecord",
        },
      ],
    };
  },
  computed: {
    currentComponent() {
      let component = this.componentList.find(
        (item) => item.value == this.selectLi
      ) || {
        label: "前科人员发现报警",
        value: 1,
        radioList: [],
        componentName: "CriminalRecord",
      };
      return component;
    },
  },
  created() {},
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
