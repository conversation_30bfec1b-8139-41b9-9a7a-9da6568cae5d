<template>
  <ui-modal v-model="modalShow" :title="dialogData.title" :width="dialogData.width" @onOk="submitData">
    <Form class="form-modal" ref="dataForm" :model="dataForm" :rules="ruleValidate" :label-width="110">
      <div>
        <Row>
          <Col span="10">
          <FormItem label="资源名称" prop="resourceName">
            <Input v-model="dataForm.resourceName" placeholder="请输入资源名称"></Input>
          </FormItem>
          <FormItem label="资源中文名称" prop="resourceNameCn">
            <Input v-model="dataForm.resourceNameCn" placeholder="请输入资源中文名称"></Input>
          </FormItem>
          </Col>
          <Col span="10">
          <FormItem label="所属目录" prop="catalogIds">
            <ui-treeSelect class="select-node-tree filter-tree" multiple v-model="dataForm.catalogIds" filterable
              check-strictly placeholder="请选择所属目录" default-expand-all show-checkbox :expand-on-click-node="false"
              check-on-click-node node-key="id" :treeData="directoryList"> </ui-treeSelect>
          </FormItem>
          <FormItem label="资源描述" :label-width="110" prop="describeCn">
            <Input v-model="dataForm.describeCn" :maxlength="500" :rows="4" type="textarea" placeholder="请输入描述信息" />
          </FormItem>
          </Col>
        </Row>
      </div>
      <div class="operate_bar">
        <Input style="width: 152px;" v-model="addFieldNum" placeholder="输入" maxlength="2">
        <span slot="prepend">条</span>
        <Button slot="append" type="primary" @click="addFieldHandle">新增</Button>
        </Input>
        <Upload action="*" :before-upload="beforeUpload" class="upload-content" :limit="1">
          <Button type="primary">解析Excel</Button>
        </Upload>
        <Button type="default" @click="removeHandle()">删除</Button>
      </div>
      <div class="checkbox_content">
        <Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="checkAllHandle">全选（已选<font>{{
    checkAllGroup.length }}</font>条）
        </Checkbox>
        <p>共<font>{{ dataForm.itemDetails.length }}</font>条</p>
      </div>
      <div class="type-form-container">
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange" class="checkbox_items" max-height="580">
          <FormItem class="table_item" v-for="(field, index) in dataForm.itemDetails" :key="index">
            <div slot="label">
              <Checkbox :label="index">
                <Input class="td w100" placeholder="字段名" v-model="field.fieldName" />
              </Checkbox>
            </div>
            <Input class="w150" placeholder="中文名称" v-model="field.fieldNameCn" />
            <Select class="w150" :placeholder="field.fieldType ? field.fieldType : '数据类型'" v-model="field.fieldType"
              transfer>
              <Option v-for="(item, index) in fileType" :key="index" :value="item"></Option>
            </Select>
            <Input class="w100" placeholder="数据长度" v-model="field.fieldTypeLen" />
            <Select class="w100" placeholder="是否主键" v-model="field.isPk" transfer>
              <Option :value="0">否</Option>
              <Option :value="1">是</Option>
            </Select>
            <Select class="w100" placeholder="是否索引" v-model="field.isIndex" transfer>
              <Option :value="0">否</Option>
              <Option :value="1">是</Option>
            </Select>
            <Select class="w100" placeholder="敏感级别" v-model="field.sensitiveLv" transfer>
              <Option :value="1">一级</Option>
              <Option :value="2">二级</Option>
            </Select>
            <Input class="w200" placeholder="数据元" v-model="field.dataElement" />
            <Input class="w200" placeholder="敏感词" v-model="field.determiner" />
            <Button type="default" @click="removeHandle(true, index)">删除</Button>
          </FormItem>
        </CheckboxGroup>
      </div>
    </Form>
  </ui-modal>
</template>
<script>
import { parseExcel, saveResource } from '@/api/dataGovernance'
import UiTreeSelect from '@/components/ui-tree-select'

export default {
  props: {
    directoryList: {
      type: Array,
      default: () => []
    }
  },
  components: { UiTreeSelect },
  data () {
    return {
      dialogData: {
        width: 1050,
        title: 'Excel一键创建资源'
      },
      ruleValidate: {
        catalogIds: [{ required: true, type: 'array', message: '请选择所属目录', trigger: 'blur' }],
        resourceName: [{ required: true, message: '资源名称', trigger: 'blur' }],
        resourceNameCn: [{ required: true, message: '资源中文名称', trigger: 'blur' }]
      },
      fileType: ["VARCHAR", "DATETIME", "BLOB"],
      catalogList: [
      ],
      indeterminate: false,
      checkAll: false,
      modalShow: false,
      dataForm: {
        sequenceNo: "",
        itemDetails: [],
        resourceName: "",
        resourceNameCn: "",
        describeCn: "",
        catalogIds: []
      },
      checkAllGroup: [],
      addFieldNum: '',
    };
  },
  methods: {
    show () {
      this.modalShow = true
      this.dataForm = {
        sequenceNo: "",
        itemDetails: [],
        resourceName: "",
        resourceNameCn: "",
        describeCn: "",
        catalogIds: []
      }
    },
    // 创建资源
    submitData () {
      this.$refs.dataForm.validate((valid) => {
        if (valid) {
          saveResource(this.dataForm).then(res => {
            this.$Message.success('保存成功')
            this.$emit('refreshDataList')
            this.modalShow = false
          })
        } else {
          this.$Message.error('信息不完整，请完善')
        }
      })
    },
    // 删除
    removeHandle (isDelMore, index) {
      if (this.checkAllGroup.length === 0 && !isDelMore) {
        this.$Message.warning('请先选择删除项')
        return false
      }
      if (isDelMore) {
        // 单个删除
        this.dataForm.itemDetails.splice(index, 1)
      } else {
        // 多个删除
        this.checkAllGroup.forEach((itemIndex) => {
          for (let index = 0; index < this.dataForm.itemDetails.length; index++) {
            if (index === itemIndex) {
              this.dataForm.itemDetails.splice(index, 1)
            }
          }
        })
        this.checkAllGroup = []
        this.checkAll = false
        this.indeterminate = false
      }
    },
    // 单个check改变
    checkAllGroupChange (data) {
      if (data.length === this.dataForm.itemDetails.length) {
        this.indeterminate = false
        this.checkAll = true
      } else if (data.length > 0) {
        this.indeterminate = true
        this.checkAll = false
      } else {
        this.indeterminate = false
        this.checkAll = false
      }
      this.checkAllGroup = data
    },
    //全选 、全不选
    checkAllHandle () {
      if (this.indeterminate) {
        this.checkAll = false
      } else {
        this.checkAll = !this.checkAll
      }
      this.indeterminate = false
      if (this.checkAll) {
        this.checkAllGroup = this.dataForm.itemDetails.map((item) => item.sequenceNo)
      } else {
        this.checkAllGroup = []
      }
    },
    // 新增字段
    addFieldHandle () {
      if (!this.addFieldNum) return this.$Message.warning('请输入新增条数,条数不能大于100条')
      for (let i = 0; i < this.addFieldNum; i++) {
        // 索引从1开始
        let sequenceNo = this.dataForm.itemDetails.length >= 1 ? this.dataForm.itemDetails[0].sequenceNo + 1 : 1
        this.dataForm.itemDetails.unshift({
          sequenceNo,
          fieldName: '',
          fieldNameCn: '',
          fieldType: '',
          fieldTypeLen: '',
          isPk: '',
          isIndex: '',
          sensitiveLv: '',
          dataElement: '',
          determiner: ''
        })
      }
      this.addFieldNum = ''
    },
    //解析excel表
    beforeUpload (file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error('仅支持Excel文件格式的导入')
        return false
      }
      const isLt30M = file.size / 1024 / 1024 < 30
      if (!isLt30M) {
        this.$Message.error('上传文件大小不能超过 30MB!')
        return false
      }
      let fileData = new FormData()
      fileData.append("file", file)
      parseExcel(fileData).then(res => {
        this.dataForm.resourceName = res.data.defaultResName
        this.dataForm.resourceNameCn = res.data.defaultResNameCn
        this.dataForm.itemDetails = res.data.itemDetails.reverse()
      })
    }
  }
};
</script>
<style scoped lang="less">
/deep/ .w200 {
  width: 380px !important;
}

/deep/ .w150 {
  width: 150px !important;
}

/deep/ .w100 {
  width: 100px !important;
}

.step_content {
  width: 328px;
  margin: 0 auto 30px;
}

.has-tip .ivu-poptip {
  position: absolute;
  width: 50px;
}

/deep/ .ivu-checkbox-wrapper {
  display: flex;
  align-items: center;
  white-space: nowrap;

  .ivu-checkbox {
    margin-right: 10px;
  }
}

/deep/ .tips_btn {
  float: right;

  .ivu-icon {
    color: #E99E53;
    font-size: 18px;
  }
}

/deep/ .ivu-input-group-prepend {
  position: absolute;
  right: 82px;
  color: #037CBE;
  z-index: 10;
  top: 10px;
  padding: 0;
  width: 14px;
}

.form-modal {
  /deep/ .ivu-tag .ivu-icon {
    top: 4px !important;
    font-size: 16px !important;
  }

  .ivu-row {
    display: flex;
    justify-content: space-around;
  }
}

.operate_bar {
  display: flex;
  align-items: center;
  width: 100%;

  .upload-content {
    display: flex;
    margin-right: 10px;
  }

  &.flex_justify_between {
    justify-content: space-between;
  }

  &>.ivu-input-group,
  &>.ivu-btn {
    margin-right: 10px;
  }

  /deep/ .ivu-input {
    border-radius: 4px 0 0 4px;
  }
}

.checkbox_content {
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  font {
    color: #23a8f9;
    margin: 0 4px;
  }
}

.checkbox_items {
  height: 420px;
  overflow: scroll;
}

.ivu-form-item {
  margin-bottom: 20px;

  &.table_item {
    margin-bottom: 5px;
    display: flex;

    /deep/ .ivu-form-item-label {
      padding-right: 0;
    }

    /deep/ .ivu-form-item-content {
      display: flex;
      align-items: center;
      margin: 0 !important;

      .td {
        margin-right: 10px;
        white-space: nowrap;
      }
    }
  }
}
</style>
