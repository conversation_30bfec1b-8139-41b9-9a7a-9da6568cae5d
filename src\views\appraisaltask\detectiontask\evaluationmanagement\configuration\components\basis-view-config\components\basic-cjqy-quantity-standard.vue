<template>
  <div class="vehicle-modal">
    <common-form
      :label-width="170"
      class="common-form"
      ref="commonForm"
      :moduleAction="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
    </common-form>
    <Form ref="formData" class="form-content edit-form" :model="formData" :rules="ruleCustom" :label-width="170">
      <FormItem required label="重点必报采集区域类型">
        <Button type="dashed" class="area-btn" @click="clickImportantArea"
          >请选择采集区域类型 <span>{{ `已选择 ${(importList || 0) && importList.length}个` }}</span></Button
        >
      </FormItem>
      <FormItem required label="其他必报采集区域类型">
        <Button type="dashed" class="area-btn" @click="clickOtherArea"
          >请选择采集区域类型 <span>{{ `已选择 ${(otherList || 0) && otherList.length}个` }}</span></Button
        >
      </FormItem>
      <FormItem required label="设置达标数量"> </FormItem>
    </Form>
    <area-select
      key="otherAreaSelect"
      dataSource
      :data="captureAreaData"
      v-model="otherAreaSelectVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>

    <common-capture-area-select
      ref="captureAreaSelect"
      :data="areaTreeData"
      :paste-fun="pasteFun"
      :copy-fun="copyFun"
      :config-fun="configFun"
      :isConfig="isConfigCaptureArea"
    >
      <template #configName>
        <span class="inline width-md">采集区域类型数量</span>
      </template>
    </common-capture-area-select>
    <config-area-num :data="configCjqyList" @commit="commitConfigAreaNum" v-model="configAreaNumVisible">
    </config-area-num>
  </div>
</template>

<script>
import {
  defaultEmphasisData,
  defaultGeneralData,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
import { mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  components: {
    ConfigAreaNum:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/config-area-num')
        .default,
    AreaSelect: require('@/components/area-select').default,
    CommonCaptureAreaSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-capture-area-select.vue')
        .default,
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      treeData: 'common/getInitialAreaList',
      ocrCheckModelList: 'algorithm/ivdg_image_ods_check_model',
    }),
    allAreaData() {
      let data = [];
      this.importList.forEach((item) => {
        data.push({
          ...item,
          cjqyType: 0, //--采集区域类型，0重点，1其他
        });
      });
      this.otherList.forEach((item) => {
        data.push({
          ...item,
          cjqyType: 1, //--采集区域类型，0重点，1其他
        });
      });
      return data;
    },
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
        } else {
          //  共78个必报采集区域  12个重点 66个其他
          let importList = defaultEmphasisData.map((item) => {
            return {
              code: item.key,
              name: item.value,
            };
          });
          // 过滤重点区域 78 - 12 = 66 个普通
          let filterOther = defaultGeneralData.filter(
            (item) => !defaultEmphasisData.find((value) => value.key === item.key),
          );
          let otherList = filterOther.map((item) => {
            return {
              code: item.key,
              name: item.value,
            };
          });
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            importList,
            otherList,
          };
        }
        this.getCjqyList();
      },
      immediate: true,
    },
  },
  data() {
    return {
      algorithm: [], //算法
      formData: {},
      ruleCustom: {},
      orgCodes: [],
      areaTreeData: [],
      selectAreaTree: {
        regionCode: '',
      },
      checkedTreeData: [],
      importList: [],
      otherList: [],
      otherAreaSelectVisible: false,
      areaData: [], // 原始采集区域
      currentAreaType: '', //'importantCjqy','otherCjqy'
      captureAreaData: [],
      areaConfigList: [], //传入配置
      configAreaNumVisible: false,
      captureAreaTreeData: [],
      configCjqyList: [],
    };
  },
  created() {
    this.initTree();
  },
  methods: {
    /*----------------*/
    commitConfigAreaNum(importList, otherList) {
      let index = this.areaConfigList.findIndex((item) => item.civilCode === this.regionCode);
      if (index !== -1) {
        this.areaConfigList[index] = {
          civilCode: this.regionCode,
          cqList: [...importList, ...otherList],
        };
      } else {
        this.areaConfigList.push({
          civilCode: this.regionCode,
          cqList: [...importList, ...otherList],
        });
      }
      this.configAreaNumVisible = false;
    },
    pasteFun({ regionCode }) {
      if (!this.copyRegionCode) {
        this.$Message.error('请先复制');
      }
      let copyIndex = this.areaConfigList.findIndex((item) => item.civilCode === this.copyRegionCode);
      let pasteIndex = this.areaConfigList.findIndex((item) => item.civilCode === regionCode);
      if (pasteIndex === -1) {
        this.areaConfigList.push({
          civilCode: regionCode,
          cqList: this.areaConfigList[copyIndex]['cqList'],
        });
      } else {
        this.areaConfigList[pasteIndex]['cqList'] = this.$util.common.deepCopy(
          this.areaConfigList[copyIndex]['cjqyQuantityList'],
        );
      }
      this.$Message.success('粘贴成功');
    },
    copyFun({ regionCode }) {
      let index = this.areaConfigList.findIndex((item) => item.civilCode === regionCode);
      this.copyRegionCode = index === -1 ? '' : regionCode;
      if (index === -1) {
        return this.$Message.error('复制失败，未配置采集区域数量');
      } else {
        return this.$Message.success('复制成功');
      }
    },
    configFun({ regionCode }) {
      if (!this.importList.length || !this.otherList.length) {
        return this.$Message.error('请配置采集区域类型');
      }
      this.regionCode = regionCode;
      let index = this.areaConfigList.findIndex((item) => item.civilCode === this.regionCode);
      if (index === -1) {
        this.configCjqyList = this.$util.common.deepCopy(this.allAreaData);
      } else {
        let cqList = this.areaConfigList[index]['cqList'];
        let allCqList = this.$util.common.deepCopy(this.allAreaData);
        allCqList.forEach((val, index) => {
          cqList.forEach((item) => {
            if (val.key === item.key) {
              allCqList[index] = { ...item, ...val };
            }
          });
        });
        this.configCjqyList = allCqList;
      }
      this.configAreaNumVisible = true;
    },
    isConfigCaptureArea({ regionCode }) {
      return this.areaConfigList.findIndex((item) => item.civilCode === regionCode) !== -1;
    },

    /*---------*/
    getCjqyName(code) {
      let data = [...this.importList, ...this.otherList];
      let result = data.find((item) => item.key === code);
      return result['value'];
    },
    getCjqyList() {
      let { importList = [], otherList = [], configList = [], regionCode } = this.formData;
      //1、处理 重点/其他必报采集区域类型
      this.importList = importList.map((item) => {
        return {
          key: item.code,
          value: item.name,
        };
      });
      this.otherList = otherList.map((item) => {
        return {
          key: item.code,
          value: item.name,
        };
      });
      let areaConfigList = [];
      configList.forEach((item) => {
        let cqList = item.cqList.map((value) => {
          return {
            cjqyType: value.t,
            key: value.c,
            value: this.getCjqyName(value.c),
            faceConfigNum: value.fn,
            videoConfigNum: value.vdn,
            vehicleConfigNum: value.vhn,
          };
        });
        areaConfigList.push({
          civilCode: item.civilCode,
          cqList: cqList,
          examine: item.examine,
        });
      });
      this.areaConfigList = areaConfigList;
      //设置达标数量 回显处理
      let filterTreedata = this.treeData.filter(
        (item) => item.regionCode === regionCode || item.parentCode === regionCode,
      );
      filterTreedata.forEach((item) => {
        configList.forEach((value) => {
          if (item.regionCode === value.civilCode) {
            this.$set(item, 'check', !!value.examine);
          }
        });
      });
      let copyFilterTreedata = this.$util.common.deepCopy(filterTreedata);
      this.areaTreeData = this.$util.common.arrayToJson(copyFilterTreedata, 'regionCode', 'parentCode');
    },
    /**
     * 视频图像采集区域数量达标率指标indexConfig参数：
     * {
     *         "importList":[                --重点必填采集区域类型
     *                 {"code":"A0101","name":"重大群众性集会场所"},
     *                 {"code":"A0102","name":"商业服务场所"}
     *         ],
     *         "otherList":[        --其他必填采集区域类型
     *                 {
     *                         "code":"B0701",                --采集区域编码
     *                         "name":"学校"                --采集区域编码
     *                 },
     *                 {"code":"B0702","name":"幼儿园"}
     *         ],
     *         "configList":[                --行政区划-采集区域 配置
     *                 {
     *                         "civilCode":"320000",                                --行政区划编码
     *                         "cqList":[
     *                                 {
     *                                         "c":"A0101",                        --采集区划编码
     *                                         "fn":100,                --人脸卡口 需上报数量
     *                                         "vhn":120,                --车辆卡口 需上报数量
     *                                         "vdn":150,                --视频监控 需上报数量
     *                                         "t":0                                --采集区域类型，0重点，1其他
     *                                 }
     *                         ]
     *                 },
     *                 {
     *                         "civilCode":"320100",
     *                         "cqList":[{如上}]
     *                 }
     *         ]
     *
     * }
     */
    setCjqyList() {
      let checkRegion = this.$refs.captureAreaSelect.getCheckRegion();
      if (!checkRegion.length) {
        this.$Message.error('请配置需要考核的行政区划');
        return false;
      }
      let importList = this.importList.map((item) => {
        return {
          code: item.key,
          name: item.value,
        };
      });
      let otherList = this.otherList.map((item) => {
        return {
          code: item.key,
          name: item.value,
        };
      });
      let configList = [];
      this.areaConfigList.forEach((item) => {
        let index = checkRegion.findIndex((v) => v === item.civilCode);
        let cqList = item.cqList.map((value) => {
          return {
            t: value.cjqyType,
            c: value.key,
            fn: value.faceConfigNum,
            vdn: value.videoConfigNum,
            vhn: value.vehicleConfigNum,
          };
        });
        if (index !== -1) {
          configList.push({
            civilCode: item.civilCode,
            cqList: cqList,
            examine: index === -1 ? 0 : 1,
          });
        }
      });
      this.formData.importList = importList;
      this.formData.otherList = otherList;
      this.formData.configList = configList;
      return true;
    },

    /**
     *
     * @param data
     * @param dataWithName {key: '', value: ''}
     */
    confirmArea(data, dataWithName) {
      if (this.currentAreaType === 'importantCjqy') {
        this.importList = dataWithName;
      } else if (this.currentAreaType === 'otherCjqy') {
        this.otherList = dataWithName;
      }
    },
    async initTree() {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getDeviceSbcjqyData);
        this.areaData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    clickImportantArea() {
      this.currentAreaType = 'importantCjqy';
      if (!this.areaData.length) return this.$Message.error('采集区域数据不存在');
      this.checkedTreeData = [];
      let data = this.importList || [];
      this.checkedTreeData = data.map((item) => item.key);
      this.captureAreaData = this.$util.common.deepCopy(this.areaData);
      this.captureAreaData.forEach((item) => {
        let index = this.otherList.findIndex((value) => value.key === item.code);
        this.$set(item, 'disabled', index !== -1);
      });
      this.otherAreaSelectVisible = true;
    },
    clickOtherArea() {
      if (!this.importList.length) {
        return this.$Message.error('请选择重点必报采集区域类型');
      }
      this.currentAreaType = 'otherCjqy';
      if (!this.areaData.length) return this.$Message.error('采集区域数据不存在');
      this.checkedTreeData = [];
      this.captureAreaData = this.$util.common.deepCopy(this.areaData);
      this.captureAreaData.forEach((item) => {
        let index = this.importList.findIndex((value) => value.key === item.code);
        this.$set(item, 'disabled', index !== -1);
      });
      this.otherAreaSelectVisible = true;
      let data = this.otherList || [];
      this.checkedTreeData = data.map((item) => item.key);
    },
    quantityQuery(data) {
      this.formData.quantityConfig = data;
    },
    validateForm() {},
    // 添加时间区间
    toAdd() {
      this.formData.dateRang.push({ hourStart: null, hourEnd: null });
    },
    // 删除时间区间
    toDel(index) {
      this.formData.dateRang.splice(index, 1);
    },
    handleChange(time, item) {
      item.planTime = time;
    },
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    handleSubmit() {
      return this.$refs['commonForm'].handleSubmit() && this.setCjqyList();
    },
  },
};
</script>

<style lang="less" scoped>
.vehicle-modal {
  /deep/.select-width,
  .input-width {
    width: 380px;
  }
  .w240 {
    width: 240px;
  }
  .w292 {
    width: 292px;
  }
  .w390 {
    width: 390px;
  }
  .params-pre {
    display: inline-block;
    float: left;
    width: 40px;
    height: 34px;
    line-height: 34px;
    font-size: 16px;
    color: var(--color-primary);
    text-align: center;
    border: 1px solid #10457e;
    opacity: 1;
    border-radius: 4px;
    margin-right: 10px;
  }
  .label-color {
    color: #e44f22;
  }
  .color-white {
    color: #fff;
  }
  .color-bule {
    color: #1b82d2 !important;
  }
  .width-picker {
    width: 174px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .assessTime {
    /deep/.ivu-form-item-label {
      &::before {
        content: '*';
        display: inline-block;
        margin-right: 0.020833rem;
        line-height: 1;
        font-family: SimSun;
        font-size: 0.072917rem;
        color: #ed4014;
      }
    }
  }
  .inspection {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
}
.notCheck {
  color: #56789c;
}
</style>
