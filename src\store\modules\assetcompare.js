import assetcomparison from '@/config/api/assetcomparison';
import axios from 'axios';
/**
 * 资产对比配置
 */
export default {
  namespaced: true,
  state: {
    configInfo: {},
  },
  mutations: {
    setConfigInfo(state, params) {
      state.configInfo = params;
    },
  },
  getters: {
    getConfigInfo(state) {
      return state.configInfo;
    },
  },
  actions: {
    async getConfig({ commit }) {
      try {
        let { data } = await axios.get(assetcomparison.getConfig);
        commit('setConfigInfo', data.data || {});
      } catch (err) {
        console.log(err);
      }
    },
  },
};
