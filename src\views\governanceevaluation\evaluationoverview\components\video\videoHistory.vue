<template>
  <!-- 历史视频可调阅率 -->
  <div class="videoHistory" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
      <!-- <div
        class="information-echart"
        v-ui-loading="{ loading: echartsLoading, tableData: echartData }"
      >
        <draw-echarts
          v-if="echartData.length != 0"
          :echart-option="determinantEchart"
          :echart-style="ringStyle"
          ref="attributeChart"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div> -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                  item.rank
                }}</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                  <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                  <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="item.rank == 1">{{ item.regionName }}</span>
                  <span v-if="item.rank == 2">{{ item.regionName }}</span>
                  <span v-if="item.rank == 3">{{ item.regionName }}</span>
                  <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                    item.regionName
                  }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.standardsValue }}%</span>
              </div>

              <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                  item.rankRise || 0
                }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-yichangshujuliebiao f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <Button type="primary" class="btn_search" :loading="exportLoading" @click="getExport">
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <div class="search-wrapper">
        <ui-label class="mr-lg" label="设备编码" width="70">
          <Input class="input-width" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label label="设备名称" :width="70" class="mr-lg">
          <Input class="input-width" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
        </ui-label>
        <ui-label label="在线状态" :width="70" class="mr-lg">
          <Select v-model="searchData.online" clearable placeholder="请选择在线状态" class="width-input">
            <Option value="1">文件存在</Option>
            <Option value="2">文件不存在</Option>
            <!-- <Option value="3">无法检测</Option> -->
          </Select>
        </ui-label>
        <ui-label label="完好状态" :width="70" class="mr-lg">
          <Select v-model="searchData.normal" clearable placeholder="请选择完好状态" class="width-input">
            <Option value="1">取流及时响应</Option>
            <Option value="2">取流超时响应</Option>
            <!-- <Option value="3">无法检测</Option> -->
          </Select>
        </ui-label>
        <ui-label label="可用状态" :width="70" class="mr-lg">
          <Select v-model="searchData.canPlay" clearable placeholder="请选择可用状态" class="width-input">
            <Option value="1">取流成功</Option>
            <Option value="2">取流失败</Option>
            <!-- <Option value="3">无法检测</Option> -->
          </Select>
        </ui-label>
        <ui-label label="检测结果" :width="70" class="mr-lg">
          <Select v-model="searchData.qualified" clearable placeholder="请选择检测结果" class="width-input">
            <Option value="1" label="合格"></Option>
            <Option value="2" label="不合格"></Option>
            <Option value="3" label="无法检测"></Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="fl" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button type="default" class="mr-sm" @click="reast"> 重置 </Button>
        </ui-label>
      </div>
      <div class="list">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <!-- 在线状态 -->
          <template #online="{ row }">
            <span
              :class="{
                color_qualified: row.online === '1',
                color_unqualified: row.online === '2',
              }"
            >
              {{ row.online === '1' ? '文件存在' : row.online === '2' ? '文件不存在' : '' }}
            </span>
          </template>
          <!-- 完好状态 -->
          <template #normal="{ row }">
            <span
              :class="{
                color_qualified: row.normal === '1',
                color_unqualified: row.normal === '2',
              }"
            >
              {{ row.normal === '1' ? '取流及时响应' : row.normal === '2' ? '取流超时响应' : '' }}
            </span>
          </template>
          <!-- 可用状态 -->
          <template #canPlay="{ row }">
            <span
              :class="{
                color_qualified: row.canPlay === '1',
                color_unqualified: row.canPlay === '2',
              }"
            >
              {{ row.canPlay === '1' ? '取流成功' : row.canPlay === '2' ? '取流失败' : '' }}
            </span>
          </template>
          <template #phyStatus="{ row }">
            <span>
              {{ !row.phyStatus ? '--' : row.phyStatusText }}
            </span>
          </template>
          <template #qualified="{ row }">
            <span
              class="check-status"
              :class="[
                row.qualified === '1' ? 'bg-b77a2a' : '',
                row.qualified === '2' ? 'bg-17a8a8' : '',
                row.qualified === '3' ? 'bg-D66418' : '',
              ]"
            >
              {{ handleCheckStatus(row.qualified) }}
            </span>
            <span
              v-permission="{
                route: $route.name,
                permission: 'artificialreviewr',
              }"
              class="ml-sm"
              v-if="row.dataMode === '3'"
            >
              (人工)
            </span>
          </template>
          <template #videoStartTime="{ row }">
            <span>{{ row.videoStartTime ? row.videoStartTime : '--' }}</span>
          </template>
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
          <template #option="{ row }">
            <div class="boxCenter">
              <ui-btn-tip
                icon="icon-bofangshipin"
                content="播放历史录像"
                @click.native="clickHis(row)"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip
                icon="icon-chakanjietu"
                content="查看截图"
                disabled
                v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip
                v-else
                icon="icon-chakanjietu"
                content="查看截图"
                @click.native="showResult(row)"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip
                v-if="!isFormCascade()"
                icon="icon-rengongfujian"
                class="mr-sm"
                content="人工复核"
                @click.native="artificialReview(row)"
                v-permission="{
                  route: $route.name,
                  permission: 'artificialreviewr',
                }"
              ></ui-btn-tip>
              <ui-btn-tip
                class="mr-sm f-16"
                icon="icon-tianjiabiaoqian"
                content="添加标签"
                @click.native="addTags(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
        <!-- <loading v-if="loading"></loading> -->
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
    <resultModel ref="result"></resultModel>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="margin-top: -15px">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
    <!-- 人工复核 -->
    <ui-modal v-model="artificialVisible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
      <ui-label class="block" label="人工复核:" :width="80">
        <RadioGroup v-model="artificialData.qualified">
          <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">{{ item.key }}</Radio>
        </RadioGroup>
      </ui-label>
      <ui-label class="block mt-sm" label=" " :width="80">
        <Input
          type="textarea"
          class="desc"
          v-model="artificialData.reason"
          placeholder="请输入备注信息"
          :rows="5"
          :maxlength="256"
        ></Input>
      </ui-label>
      <template #footer>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </template>
    </ui-modal>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less" scoped>
.videoHistory {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      // width: 755px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
    }
    .information-echart {
      display: inline-block;
      width: 650px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .echarts-box {
        width: 100%;
        height: 100% !important;
        .charts {
          width: 100%;
          height: 100% !important;
        }
      }
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      padding-right: 2px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
  .search-wrapper {
    margin-top: 10px;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .input-width {
      width: 160px;
    }
    .width-input {
      width: 160px;
    }
    .ui-label {
      margin-bottom: 10px;
    }
  }
  /deep/.conter-center {
    padding: 0;
    width: 100%;
  }
  /deep/.f-55 {
    font-size: 55px !important;
  }
  /deep/.statistics-ul {
    padding-right: 310px;
    position: relative;
    li {
      width: 24% !important;
    }
    li:last-child {
      height: 210px !important;
      width: 20% !important;
      position: absolute;
      right: 15px;
      z-index: 1;
    }
    li:nth-last-child(1) {
      margin-right: 0;
    }
  }
  .boxCenter {
    display: flex;
    justify-content: center;
  }
  .color_qualified {
    color: #13b13d;
  }
  .color_unqualified {
    color: #e44f22;
  }
  .desc {
    width: 600px;
  }
}
</style>

<script>
import vedio from '@/config/api/vedio-threm';
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [downLoadTips],
  name: 'basic-information',
  data() {
    return {
      //人工复核
      artificialStyles: {
        width: '4rem',
      },
      qualifiedList: [
        { key: '历史视频可调阅', value: '1' },
        { key: '历史视频不可调阅', value: '2' },
      ],
      artificialVisible: false,
      artificialData: { qualified: '1', reason: '' },
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      videoStyles: {
        width: '5rem',
      },
      videoVisible: false,
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticsList: [
        {
          name: '视频监控设备总数',
          value: 0,
          icon: 'icon-shipinjiankongshebeizongshu-011',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'deviceCount',
        },
        {
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-shijiceshebeishuliang-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'evaluatingCount',
        },
        {
          name: '历史视频合格设备',
          value: 0,
          icon: 'icon-shishishipinhegeshebei-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          textColor: 'color5',
          key: 'evaluatingSuccessCount',
        },
        {
          name: '历史视频异常设备',
          value: 0,
          icon: 'icon-shishishipinyichangshebei-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'evaluatingFailedCount',
        },
        {
          name: '无法检测设备数量',
          value: 0,
          icon: 'icon-wufajianceshebeishuliang-01',
          iconColor: 'icon-bg7',
          liBg: 'li-bg7',
          type: 'number',
          textColor: 'color7',
          key: 'unableEvaluatingCount',
        },
        {
          name: '在线率',
          value: 0,
          icon: 'icon-zaixianshuai-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage',
          key: 'evaluatingOnlineResult',
          textColor: 'color5',
        },
        {
          name: '完好率',
          value: 0,
          icon: 'icon-wanhaoshuai-01',
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          type: 'percentage',
          key: 'evaluatingNormalResult',
          textColor: 'color8',
        },
        {
          name: '可用率',
          value: 0,
          icon: 'icon-keyongshuai-01',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'percentage',
          key: 'evaluatingCanPlayResult',
          textColor: 'color1',
        },
        {
          name: '普通历史视频可调阅率',
          value: 0,
          icon: 'icon-zhongdianputonglishishipinketiaoyueshuai-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage',
          textColor: 'color5',
          key: 'resultValueFormat',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '设备编码',
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '设备名称',
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        { title: '设备物理状态 ', slot: 'phyStatus', minWidth: 120 },
        {
          title: `在线状态`,
          key: 'online',
          slot: 'online',
          width: 120,
          renderHeader: (h) => {
            return h('div', [
              h('span', '在线状态'),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'top',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                    },
                    '录像文件存在',
                  ),
                ],
              ),
            ]);
          },
        },
        {
          title: `完好状态`,
          key: 'normal',
          slot: 'normal',
          width: 120,
          renderHeader: (h) => {
            return h('div', [
              h('span', '完好状态'),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'top',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                    },
                    '录像文件存在，拉流请求有响应',
                  ),
                ],
              ),
            ]);
          },
        },
        {
          title: `可用状态`,
          key: 'canPlay',
          slot: 'canPlay',
          width: 150,
          renderHeader: (h) => {
            return h('div', [
              h('span', '可用状态'),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'top',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                    },
                    '成功接收到历史视频流',
                  ),
                ],
              ),
            ]);
          },
        },
        {
          title: `检测结果`,
          key: 'qualified',
          slot: 'qualified',
          width: 120,
          renderHeader: (h) => {
            return h('div', [
              h('span', '检测结果'),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'top',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                      style: {
                        whiteSpace: 'normal',
                        wordBreak: 'normal',
                        maxWidth: '300px',
                      },
                    },
                    `设备的【${this.indexDetectionModeMap[this.statisticalList.indexDetectionMode]}】作为指标计算结果`,
                  ),
                ],
              ),
            ]);
          },
        },
        { title: '原因', key: 'reason', width: 120, tooltip: 'true' },
        { title: '检测时间', slot: 'videoStartTime', width: 160 },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          minWidth: 130,
          tooltip: true,
          fixed: 'right',
          align: 'center',
        },
      ],
      indexDetectionModeMap: {
        1: '在线状态',
        2: '完好状态',
        3: '可用状态',
        null: '',
      },
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        online: '',
        normal: '',
        canPlay: '',
        qualified: '',
        deviceName: '',
        deviceId: '',
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      statisticalList: {}, //统计数量
      rankData: [],
      paramsList: {},
      errorMessages: [],
      // exportList: [
      //   { name: '导出设备总表', type: false },
      //   { name: '按异常原因导出分表', type: true },
      // ],
      exportName: '',
      videoUrl: '',
      contentClientHeight: 0,
      artificialRow: {},
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
    this.getTagList();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    isFormCascade() {
      //级联清单 跳转 不需要 人工复核
      return this.$route.name === 'cascadelist';
    },
    handleCheckStatus(row) {
      const flag = {
        1: '合格',
        2: '不合格',
        3: '无法检测',
      };
      return flag[row];
    },
    // 人工复核
    artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.reason = '';
      this.artificialRow = row;
      this.artificialVisible = true;
    },
    async artificial() {
      let data = {
        data: {
          id: this.artificialRow.id,
          qualified: this.artificialData.qualified,
          reason: this.artificialData.reason,
        },
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.getTableData();
        this.artificialVisible = false;
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    // onClickIndex(val) {
    //   this.exportList.map((item) => {
    //     if (val === item.name) {
    //       this.exportName = item.type
    //     }
    //   })

    //   this.getExport()
    // },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        multiSheet: this.exportName,
        errorMessages: this.errorMessages,
        orgRegionCode: this.paramsList.orgRegionCode,
        displayType: this.paramsList.displayType,
        customParameters: {
          ...this.searchData,
          totalCount: undefined,
          pageNum: undefined,
          pageSize: undefined,
        },
      };
      try {
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 柱状图统计
    async getGraphsInfo() {
      let data = {
        regionCode: this.paramsList.regionCode,
        rootResultIds: this.paramsList.rootResultIds,
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data;
        this.initRing();
      } catch (err) {
        console.log(err);
      }
    },
    // //统计
    // async getChartsData() {
    //   try {
    //     let params = {
    //       indexId: this.paramsList.indexId,
    //       rootId: this.paramsList.resultId,
    //       orgCode: this.paramsList.regionCode,
    //     }
    //     let res = await this.$http.post(api.queryEvaluatingVideoCount, params)
    //     this.statisticalList = res.data.data
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValueFormat' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValueFormat' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
      this.statisticsList[0].value = this.statisticalList.deviceCount;
      this.statisticsList[1].value = this.statisticalList.evaluatingCount;
      this.statisticsList[2].value = this.statisticalList.evaluatingSuccessCount;
      this.statisticsList[3].value = this.statisticalList.evaluatingFailedCount;
      this.statisticsList[4].value = this.statisticalList.unableEvaluatingCount;
      this.statisticsList[5].value = this.statisticalList.evaluatingOnlineResult;
      this.statisticsList[6].value = this.statisticalList.evaluatingNormalResult;
      this.statisticsList[7].value = this.statisticalList.evaluatingCanPlayResult;
      this.statisticsList[8].value = this.statisticalList.resultValueFormat;
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          orgRegionCode: this.paramsList.orgRegionCode,
          displayType: this.paramsList.displayType,
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          deviceId: this.searchData.deviceId,
          deviceName: this.searchData.deviceName,
          customParameters: {
            deviceName: this.searchData.deviceName,
            deviceId: this.searchData.deviceId,
            normal: this.searchData.normal,
            online: this.searchData.online,
            canPlay: this.searchData.canPlay,
            qualified: this.searchData.qualified,
          },
        };
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // let res = await this.$http.post(evaluationoverview.getDetailData, params)
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    // 播放视频
    //视频播放
    async clickHis(row) {
      try {
        // this.isLive = false
        // this.loadingVideo = true
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let obj = {};
        obj.startTime = row.playStartTime;
        obj.endTime = row.playEndTime;
        obj.deviceId = row.deviceId;
        obj.pullLevel = 3;
        obj.urlEncryption = true;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          vedio.playback,
          obj,
          'post',
          this.$route.query.cascadeId,
          {},
          this.global.SERVER.stream,
        );
        // let res = await this.$http.post(vedio.playback, obj)
        // this.loadingVideo = false
        // console.log(res.data.data.ts)
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
        // this.videoUrl =
        //   'http://192.168.1.118:9002/qsdi/2021/08/25/ca4360b434a74c8fb687f50dd4d78330.flv'
      } catch (err) {
        console.log(err);
      }
    },
    async onCancel() {
      this.videoUrl = '';
      /**------级联清单特殊替换处理接口(后端转发)-------**/
      await superiorinjectfunc(
        this,
        vedio.stop + this.playDeviceCode,
        {},
        'post',
        this.$route.query.cascadeId,
        {},
        this.global.SERVER.stream,
      );
      // this.$http.post(vedio.stop + this.playDeviceCode)
    },
    //查看截图
    showResult(row) {
      this.$refs.result.showModal(row);
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
        deviceInfoId: this.deviceInfoId,
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.queryEvaluationDeviceResult,
          params,
          'post',
          this.$route.query.cascadeId,
        );
        // let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params)
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },

    // selectInfo(val) {
    //   this.errorMessages = val.map((item) => {
    //     return item.name
    //   })
    //   this.getTableData()
    // },
    selectInfo(infoList) {
      this.searchData.pageNumber = 1;
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.getTableData();
      //   this.searchData.tagIds = infoList.map((row) => {
      //     return row.id;
      //   });
      //   this.search();
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
    },
    search() {
      this.searchData = { ...this.searchData, pageNum: 1 };
      this.getTableData();
    },
    reast() {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        online: '',
        normal: '',
        canPlay: '',
        qualified: '',
        deviceName: '',
        deviceId: '',
      };
      this.getTableData();
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          if (this.paramsList.indexId === 4008) {
            this.statisticsList[8].name = '普通历史视频可调阅率';
          } else {
            this.statisticsList[8].name = '重点历史视频可调阅率';
          }
          this.getTableData(); // 表格
          // this.getSelectTabs()
          // this.getGraphsInfo() //柱状图
          // this.getChartsData()
          // this.queryDeviceCheckColumnReports() //头部中间echarts
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    resultModel: require('@/components/result-model.vue').default,
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    EasyPlayer: require('@/components/EasyPlayer').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
