<template>
  <div>
    <ui-modal ref="modal" :title="title" :footerHide="true" v-model="modalvisible" :width="width">
      <div class="content" v-loading="loading">
        <ul v-if="Object.keys(conclusion).length > 0">
          <li>
            <span>算法名称</span>
            <template v-if="index == 3003 || index == 3006">
              <span>车牌号</span>
              <span>车牌颜色</span>
            </template>
            <template v-else>
              <span>车辆类型</span>
              <span>车辆品牌</span>
            </template>
          </li>
          <li>
            <span>原始值</span>
            <template v-if="index == 3003 || index == 3006">
              <span>{{ conclusion.plateNo }}</span>
              <span>{{ conclusion.plateColor }}</span>
            </template>
            <template v-else>
              <span>{{ conclusion.vehicleClass }}</span>
              <span>{{ conclusion.vehicleBrand }}</span>
            </template>
          </li>
          <li v-if="conclusion.details && conclusion.details.length > 0">
            <span>{{ conclusion.details[0].algorithmTitle }}</span>
            <template v-if="index == 3003 || index == 3006">
              <span>{{ conclusion.details[0].algorithmPlateNo }}</span>
              <span>{{ conclusion.details[0].algorithmPlateColor }}</span>
            </template>
            <template v-else>
              <span>{{ conclusion.details[0].algorithmVehicleClass }}</span>
              <span>{{ conclusion.details[0].algorithmVehicleBrand }}</span>
            </template>
          </li>
          <li>
            <span>结论</span>
            <template v-if="index == 3003 || index == 3006">
              <span :class="conclusion.votePlateNo === true ? 'green' : 'red'">{{
                conclusion.votePlateNo === true ? '准确' : '存疑'
              }}</span>
              <span :class="conclusion.votePlateColor === true ? 'green' : 'red'">{{
                conclusion.votePlateColor === true ? '准确' : '存疑'
              }}</span>
            </template>
            <template v-else>
              <span :class="conclusion.voteVehicleClass === true ? 'green' : 'red'">{{
                conclusion.voteVehicleClass === true ? '准确' : '存疑'
              }}</span>
              <span :class="conclusion.voteVehicleBrand === true ? 'green' : 'red'">{{
                conclusion.voteVehicleBrand === true ? '准确' : '存疑'
              }}</span>
            </template>
          </li>
        </ul>
        <div class="empt" v-else>暂无~</div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import api from '@/config/api/inspectionrecord';
export default {
  name: 'yichang',
  components: {},
  data() {
    return {
      width: 680,
      title: '车辆属性判定',
      modalvisible: false,
      loading: false,
      conclusion: {},
      algorithm: {},
      oldResuit: {},
      index: null,
    };
  },
  created() {},
  methods: {
    init(id, i) {
      this.index = i;
      this.modalvisible = true;
      this.loading = true;
      this.$http.get(api.vehicleDetail + id).then((res) => {
        this.conclusion = res.data.data ? res.data.data : {};
      });
      this.loading = false;
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-top: 20px;
  margin-bottom: 30px;
  min-height: 180px;
  ul {
    max-width: 650px;
    color: #fff;
    li {
      width: 100%;
      text-align: left;
      background: #062042;
      span {
        display: inline-block;
        padding: 10px;
        width: 200px;
      }
    }
    li:nth-child(2n + 0) {
      background: #092955;
    }
    li:hover {
      background: #073456;
    }
    li:first-child:hover {
      background: #062042;
    }
  }
  .red {
    color: #e44f22;
  }
  .green {
    color: green;
  }
  .empt {
    margin-top: 30px;
    font-size: 16px;
  }
}
</style>
