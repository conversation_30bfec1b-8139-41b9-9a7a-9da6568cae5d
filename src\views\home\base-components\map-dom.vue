<template>
  <div class="map-dom">
    <div class="map-dom-title">
      <p>
        <img src="../../../../src/assets/img/home/<USER>" alt="" class="map-dom-title-img" />
        <span class="f-16">{{ `${toolTipData?.regionName}` ?? '未知' }}</span>
      </p>
      <span ref="viewDetail" class="f-12 pointer link-text-box" onclick="window.jump()" v-if="hasDetail">详情</span>
    </div>
    <ul class="mt-lg mr-xs" v-ui-loading="{ tableData: toolTipData?.examineIndexModuleLis }">
      <template v-if="toolTipData?.examineIndexModuleList?.length">
        <li class="li" v-for="(item, index) in toolTipData?.examineIndexModuleList ?? []" :key="index">
          <span class="title">{{ item?.label ?? `${item?.indexModuleText}不达标指标` ?? '未知' }}</span>
          <span class="active-color num">{{ item?.value ?? item?.unqualified ?? 0 }}</span>
        </li>
      </template>
      <li class="no-data" v-else>
        <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt />
        <img v-else src="@/assets/img/common/nodata-light.png" alt />
      </li>
    </ul>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  name: 'map-dom',
  props: {
    hasDetail: {
      default: true,
    },
    // 外部传入优先
    outerData: {
      default: () => null,
    },
  },
  data() {
    return {
      toolTipData: {},
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  mounted() {},
  methods: {
    /*jump() {
      console.log('执行')
      // this.$router.push({
      //   name: 'evaluationoResult',
      //   query: {
      //     orgCode: this.homePageConfig.regionCode,
      //     regionCode: this.homePageConfig.regionCode,
      //     statisticType: 'REGION',
      //     taskSchemeId: this.homePageConfig.schemeId,
      //   },
      // })
    },*/
  },
  beforeDestroy() {},
  watch: {
    outerData: {
      handler() {
        if (this.outerData) {
          this.toolTipData = this.outerData;
        }
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.map-dom {
  background: url('~@/assets/img/home/<USER>');
  background-size: 100% 100%;
  width: 240px;
  padding-bottom: 10px;
  z-index: 999;

  .map-dom-title {
    color: #bfdbff;
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;
    padding: 10px 20px 0px 0px;

    > p {
      display: flex;
      font-weight: 700;
      .active-color;
    }

    .map-dom-title-img {
      width: 44px;
      height: 44px;
    }
  }

  .active-color {
    color: #06ddfd;
  }

  > ul {
    min-height: 130px;
    max-height: 200px;
    overflow-y: scroll;
    position: relative;

    .li {
      height: 25px;
      line-height: 25px;
      display: flex;
      justify-content: space-between;
      padding: 0px 20px;

      .title {
        color: #fff;
        font-size: 14px;
      }

      .num {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}
.no-data {
  > img {
    width: 100%;
  }
}
</style>
