<!--
    * @FileDescription: 建设态势专题大屏-全域覆盖态势
    * @Author: H
    * @Date: 2024/06/11
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="screen-content">
        <div class="screen-left">
            <card title="一期建设概况" class="screen-box">
                <p class="introductory">
                    <!-- 蚌埠市公安局积极响应城市安全和交通管理的挑战，
                    最近启动了摄像监控设备的一期建设计划。
                    该计划涉及市区主要交通要道、商业中心和人流密集区域的覆盖，
                    通过安装摄像头和建立智能监控系统，实现对城市的实时监测和管理。
                    这些设备不仅可以提升城市的安全水平，还能够优化交通流量，
                    改善市民的出行体验。
                    该计划的实施将为城市治理提供更多数据支持和技术手段，
                    为建设智慧城市打下坚实基础，为市民创造更安全、更便捷的生活环境。 -->
                    {{ phaseData.constructionDesc }}
                </p>
            </card>
            <card title="一期建设占比统计" class="screen-box">
                <div class="chart-list">
                    <div class="child-chart">
                        <pie-chart name="摄像机覆盖率" ref="camerPieChart" :colorData="['rgba(45,161,255,1)', 'rgba(14,223,255,1)']"></pie-chart>
                    </div>
                    <div class="child-chart">
                        <pie-chart name="高清摄像机占比" ref="hdPieChart" :colorData="['rgba(45,255,220,1)', 'rgba(14,255,154,1)']"></pie-chart>
                    </div>
                    <div class="child-chart">
                        <pie-chart name="智能抓拍机占比" ref="smartPieChart" :colorData="['rgba(255,161,45,1)', 'rgba(255,231,14,1)']"></pie-chart>
                    </div>
                </div>
            </card>
            <card title="各区县建设详情" class="screen-box">
                <div class="table">
                    <div class="table-header">
                        <div class="table-column-pm">排名</div>
                        <div class="table-column-fxj">分县局名称</div>
                        <div class="table-column-ghjxzs">规划建设总数</div>
                        <div class="table-column-yjsl">已建数量</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in timeList" :key="index">
                            <div class="table-column-pm">
                                <img v-if="index<3" :src="getImgUrl('ranking-'+index+'.png') " alt="">
                                <span v-else>{{ item.ranking }}</span>
                            </div>
                            <div class="table-column-fxj">{{ item.projectName }}</div>
                            <div class="table-column-ghjxzs">
                                <!-- {{ item.projectValue }} -->
                                <count-to :start-val="0" :end-val="item.projectValue" :duration="1000" class="dinpromini"></count-to>
                            </div>
                            <div class="table-column-yjsl">
                                <!-- {{ item.projectValue2 }} -->
                                <count-to :start-val="0" :end-val="item.projectValue2" :duration="1000" class="dinpromini"></count-to>
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        一期规划建设数量
                    </div>
                    <div class="view-box-top"> 
                        <p>摄像机</p>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>总数</p>
                            <count-to :start-val="0" :end-val="phaseData.planCameraCount" :duration="1000" class="dinpromini"></count-to>
                        </div>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>高清</p>
                            <count-to :start-val="0" :end-val="phaseData.planCameraHDCount" :duration="1000" class="dinpromini bomColor"></count-to>
                        </div>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-top"> 
                        <p>抓拍机</p>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>总数</p>
                            <count-to :start-val="0" :end-val="phaseData.planCameraCapCount" :duration="1000" class="dinpromini"></count-to>
                        </div>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>智能</p>
                            <count-to :start-val="0" :end-val="phaseData.planCameraSmartCount" :duration="1000" class="dinpromini bomColor"></count-to>
                        </div>
                    </div>
                </div>
                <div class="content-box-right view-box">
                    <div class="title-img bigtitle">
                        一期已建数量
                    </div>
                    <div class="view-box-top"> 
                        <p>摄像机</p>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>总数</p>
                            <count-to :start-val="0" :end-val="phaseData.buildCameraCount" :duration="1000" class="dinpromini"></count-to>
                        </div>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>高清</p>
                            <count-to :start-val="0" :end-val="phaseData.buildCameraHDCount" :duration="1000" class="dinpromini bomColor"></count-to>
                        </div>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-top"> 
                        <p>抓拍机</p>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>总数</p>
                            <count-to :start-val="0" :end-val="phaseData.buildCameraCapCount" :duration="1000" class="dinpromini"></count-to>
                        </div>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>智能</p>
                            <count-to :start-val="0" :end-val="phaseData.buildCameraSmartCount" :duration="1000" class="dinpromini bomColor"></count-to>
                        </div>
                    </div>
                </div>
            </div>
            <map-chart ref="mapChart" class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="各区县摄像机覆盖率" class="screen-box screen-box-title" :padding="0">
                <rangking-chart ref="camerChart"></rangking-chart>
            </card>
            <card title="各区县高清摄像机占比" class="screen-box screen-box-title" :padding="0">
                <rangking-chart ref="hdChart"></rangking-chart>
            </card>
            <card title="各区县智能抓拍机占比" class="screen-box screen-box-title" :padding="0">
                <rangking-chart ref="smartChart"></rangking-chart>
            </card>
        </div>
    </div>
</template>
<script>
import { countyConstructionRank,
    countyCameraRank,
    countyHDVideoRank,
    countySmartVideoRank,
    onePhaseConstruction,
    glRegionStat } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import rangkingChart from './rangking-chart.vue';
import pieChart from './pie-chart.vue';
import mapChart from './map-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        rangkingChart,
        pieChart,
        mapChart,
        CountTo
    },
    data() {
        return {
            timeList: [
                { ranking: 1, projectName: 'XXX分局', projectValue: 0, projectValue2: 0, type: 1, rankingUrl: 'ranking-0.png'},
                { ranking: 2, projectName: 'XXX分局', projectValue: 0, projectValue2: 0, type: 2, rankingUrl: 'ranking-1.png'},
                { ranking: 3, projectName: 'XXX分局', projectValue: 0, projectValue2: 0, type: 1, rankingUrl: 'ranking-2.png'},
                { ranking: 4, projectName: 'XXX分局', projectValue: 0, projectValue2: 0, type: 2 },
                { ranking: 5, projectName: '', projectValue: 0, projectValue2: 0, type: 2},
                { ranking: 6, projectName: '', projectValue: 0, projectValue2: 0, type: 1},
                { ranking: 7, projectName: '', projectValue: 0, projectValue2: 0, type: 2},
            ],
            phaseData: {}
        }
    },
    created() {
        this.init();
        this.queryMap();
    },
    methods: {
        init() {
            countyConstructionRank()
            .then(res => {
                this.timeList = []
                res.data.forEach((item,  index) => {
                    if(index < 2) {
                        this.timeList.push(
                            {
                                rankingUrl: `ranking-${index}.png`,
                                projectName: item.projectName,
                                projectValue: Number(item.projectValue),
                                projectValue2: Number(item.projectValue2),
                            }
                        )
                    }else{
                        this.timeList.push(
                            {
                                ranking: index+1,
                                projectName: item.projectName,
                                projectValue: Number(item.projectValue),
                                projectValue2: Number(item.projectValue2),
                            }
                        ) 
                    }
                    // this.timeList[index].projectName = item.projectName;
                    // this.timeList[index].projectValue = item.projectValue;
                    // this.timeList[index].projectValue2 = item.projectValue2;
                })
            })
            countyCameraRank()
            .then(res => {
                let list = res.data.length > 6? res.data.slice(0,6) : res.data;
                this.$refs.camerChart.init(list)
            })
            countyHDVideoRank()
            .then(res => {
                let list = res.data.length > 6? res.data.slice(0,6) : res.data;
                this.$refs.hdChart.init(list)
            })
            countySmartVideoRank() 
            .then(res => {
                let list = res.data.length > 6? res.data.slice(0,6) : res.data;
                this.$refs.smartChart.init(list)
            })
            onePhaseConstruction()
            .then(res => {
                this.phaseData = res.data;
                this.$refs.camerPieChart.init(res.data.cameraCoverage)
                this.$refs.hdPieChart.init(res.data.cameraCoverage)
                this.$refs.smartPieChart.init(res.data.cameraCoverage)
            })
        },
        queryMap() {
            glRegionStat()
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        },  
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    }
}
</script>
<style lang='less' scoped>
@import "../../../style/resetui.less";
.screen-content{
    display: flex;
    width: 100%;
    height: 100%;
    .introductory{
        text-indent: 2em;
        font-size: 14px;
        font-weight: 400;
        color: #D0DDE7;

    }
    .chart-list{
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: space-between;
        .child-chart{
            width: 33%;
            height: 100%;
        }
    }
    .screen-main{
        .view-box{
            padding: 0 40px;
        }
        .view-box-top{
            color: #fff;
            font-size: 14px;
            font-weight: 400;
            height: 50%;
            margin-top: 10px;
            .gross{
                font-size: 28px;
                color: #F1FCFF;
                margin-top: 23px;
                font-family: DINPro;
                // span{
                //     font-size: 14px;
                //     color: #03A9FF;
                // }
            }
            .optimize{
                display: flex;
                align-items: center;
                .dot{
                    width: 8px;
                    height: 8px;
                    background: #F1FCFF;
                    box-shadow: 0px 0px 5px 0px #0988FF;
                    border: 1px solid #08E0FF;
                    border-radius: 4px;
                }
                p{
                    margin: 0 10px;
                }
                span{
                    color: #03A9FF;
                    font-size: 20px;
                    font-weight: 700;
                }
                .bomColor{
                    color: #2DDF6C;
                }
            }
        }
        .view-box-bot-right{
            width: 110px;
            height: 110px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: url('~@/assets/img/screen/green-ball.png') no-repeat;
            p{
                font-weight: 400;
                font-size: 14px;
                color: #ffffff;
            }
        }
    }
    .screen-box-title{
        /deep/ .card-head{
            background-size: 520px 35px;
        }
    }
}
</style>