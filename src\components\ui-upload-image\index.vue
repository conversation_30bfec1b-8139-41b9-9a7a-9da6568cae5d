<template>
  <div class="upload-list" :class="size ? 'upload-' + size : ''">
    <template v-for="(url, index) in urlList">
      <!-- 已上传图片展示 -->
      <div class="img-content" v-if="url" :key="index">
        <img :src="url.fileUrl" alt />
        <p class="delete-mask">
          <i
            class="iconfont icon-shanchu"
            @click.stop="minImgDeleteHandle(index)"
          ></i>
        </p>
      </div>

      <!-- 上传图片按钮 -->
      <Upload
        v-else
        :key="index + 'a'"
        type="drag"
        :beforeUpload="(file) => beforeUpload(file, index)"
        action="*"
        :show-upload-list="false"
      >
        <template>
          <div class="img-box">
            <Icon type="ios-add" />
            <div class="img-box-tip" v-if="size != 'mini2'">
              <p>点击上传图片</p>
              <span>png,jpg,jpeg,bmp</span>
              <p>图片小于10M</p>
            </div>
          </div>
        </template>
      </Upload>
    </template>

    <!-- 图片上传成功后，选择图片 -->
    <image-recognition
      ref="imageRecognition"
      :dataCopper="dataCopper"
      :tempUrl="tempUrl"
      @getMinImgUrl="getMinImgUrl"
      @destroy="imageRecognitionVisible = false"
      v-if="imageRecognitionVisible"
    />
  </div>
</template>
<script>
import imageRecognition from "./image-recognition";
import {
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
export default {
  name: "SearchPictures",
  components: { imageRecognition },
  props: {
    size: {
      type: String,
      default: "", // large、 default、 small
    },
    // 1人脸2车辆
    algorithmType: {
      type: [String, Number],
      default: null,
    },
  },
  data() {
    this.limitLength = 5; // 限制上传图片数量
    return {
      urlList: [""],
      dataCopper: [],
      tempUrl: "",
      imageRecognitionVisible: false,
      selectedUploadIndex: 0, //选中的uploadIndex
      baseImg: null,
      bigImageBase64: null,
    };
  },
  computed: {
    sectionMenuName() {
      let sectionName = this.$route.query.sectionName || "faceContent";
      let type = "";
      switch (sectionName) {
        case "faceContent":
          type = "face";
          break;
        case "vehicleContent":
          type = "vehicle";
          break;
        case "humanBodyContent":
          type = "human";
          break;
        case "nonmotorVehicleContent":
          type = "nonMotor";
          break;
      }
      return type;
    },
  },
  methods: {
    /**
     * @description: 删除已上传图片
     * @param {number} index 图片序列
     */
    minImgDeleteHandle(index) {
      let list = this.urlList.filter((_, i) => i != index);
      if (
        this.urlList.length === this.limitLength &&
        this.urlList[this.urlList.length - 1]
      ) {
        // 当达到上传数量限制被删除时，需要补充一个上传按钮
        this.urlList = [...list, ""];
      } else {
        this.urlList = list;
      }
      this.modifyUploadImage();
    },

    /**
     * @description: 选取框选图片
     */
    getMinImgUrl(data) {
      // 当有多个目标时，选择确认后才在urlList中添加一项
      this.setImageUrl(data);
    },

    /**
     * @description: 图片列表进行了更改，包括新上传图片、删除已上传图片
     */
    modifyUploadImage() {
      this.$emit("change", this.urlList);
    },

    /**
     * @description: 上传图片
     * @param {file} file 图片文件
     * @param {number} index 当前的序列
     */
    beforeUpload(file, index) {
      this.selectedUploadIndex = index;
      let isAskFile = false;
      isAskFile = /\.(PNG|JPG|JPEG|BMP|png|jpg|jpeg|bmp)$/.test(file.name);
      if (!isAskFile) {
        this.$Message.error("请上传png、jpg、jpeg或bmp格式图片！");
      }
      const isLt30M = file.size / 1024 / 1024 < 10;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 10MB!");
      }
      if (!isAskFile || !isLt30M) return false;
      let fileUploading = this.$Message.loading({
        content: "文件上传中...",
        duration: 0,
      });
      let fileData = new FormData();
      fileData.append("file", file);
      fileData.append("algorithmType", this.algorithmType);
      picturePick(fileData)
        .then((res) => {
          if (!res.data.length) {
            this.$Message.error("没有识别出目标，请选择其它图片");
            return;
          }
          this.tempUrl = window.URL.createObjectURL(file);
          if (res.data.length == 1) {
            // 当只识别到单张图片，并且目前上传的图片少于5张，则在urlList中添加一项
            this.setImageUrl(res.data[0]);
            return;
          }
          // 多张图片时，进行框选处理
          this.imageRecognition();
          this.imageDataHandle(res.data);
        })
        .finally(() => {
          fileUploading();
          fileUploading = null;
        });
      return false; // 阻止默认上传
    },
    // 打开图片识别器
    imageRecognition() {
      this.imageRecognitionVisible = true;
      this.$nextTick(() => {
        this.$refs.imageRecognition.init();
      });
    },

    /**
     * @description: 设置图片url
     * @param {object} data 图片信息
     */
    async setImageUrl(data) {
      if (this.urlList.length < this.limitLength) {
        this.urlList.push("");
      }
      const response = await this.getBase64ByImageCoordinate(data);
      const _d = response.data;
      this.$set(this.urlList, this.selectedUploadIndex, {
        fileUrl: "data:image/jpeg;base64," + _d.imageBase,
        feature: _d.feature,
        imageBase: _d.imageBase,
      });
      this.modifyUploadImage();
    },

    /**
     * 图片上传返回数据处理
     * feature: 返回后台用
     *
     * 后台返回的数据结构：bottom, left, right, top
     * 返回值为图片的2个坐标点，分别为左上和右下
     * 左上：left top
     * 右下：right bottom
     */
    imageDataHandle(list) {
      var arr = [];
      list.forEach((item) => {
        var obj = {
          x: item.left,
          y: item.top,
          width: item.right - item.left,
          height: item.bottom - item.top,
          feature: item.feature,
          imageBase: item.imageBase,
          initData: item, // 用于后续请求
        };

        arr.push(obj);
      });
      this.dataCopper = arr;
    },

    // 根据图片坐标截取图片base64
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: this.sectionMenuName };
      return getBase64ByImageCoordinateAPI(params);
    },

    /**
     * @description: 获取已经上传的图片
     * @return {array} 已经上传的图片
     */
    getUploadedImages() {
      return this.urlList;
    },

    /**
     * @description: 设置默认的上传图片
     * @param {array} images 上传的图片
     */
    setDefaultUploadImage(images) {
      this.urlList = images;
    },
  },
};
</script>
<style lang="less" scoped>
.upload-list {
  &.upload-large {
    /deep/ .ivu-upload,
    .img-content {
      width: 140px;
      height: 140px;
    }
  }

  &.upload-small {
    /deep/ .ivu-upload,
    .img-content {
      width: 80px;
      height: 80px;
      margin-right: 10px;
    }

    /deep/ .ivu-upload {
      .ivu-upload-drag {
        font-size: 12px;
        border-radius: 0;
      }

      .ivu-icon {
        font-size: 38px;
      }
    }
  }

  &.upload-mini {
    /deep/ .ivu-upload,
    .img-content {
      width: 60px;
      height: 60px;
    }

    /deep/ .ivu-upload {
      .ivu-upload-drag {
        font-size: 10px;
        border-radius: 0;
      }

      .ivu-icon {
        font-size: 30px;
      }
    }
  }

  &.upload-mini2 {
    /deep/ .ivu-upload,
    .img-content {
      width: 34px;
      height: 34px;
    }

    /deep/ .ivu-upload {
      .ivu-upload-drag {
        font-size: 10px;
        border-radius: 0;
      }

      .ivu-icon {
        font-size: 30px;
      }
    }
  }

  width: 100%;
  display: flex;
  justify-content: space-between;

  /deep/ .ivu-upload {
    width: 116px;
    height: 116px;

    .ivu-upload-drag {
      height: 100%;
      width: 100%;
      background: #f9f9f9;
      border: 1px dashed #d3d7de;
      color: rgba(0, 0, 0, 0.45);
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      font-size: 14px;

      &:hover {
        border-color: #2c86f8;
      }
    }

    .ivu-icon {
      font-weight: bold;
      font-size: 46px;
      color: #888;
    }
  }

  .img-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 116px;
    height: 116px;

    &:hover {
      .delete-mask {
        display: flex;
      }
    }

    img {
      display: block;
      height: auto;
      max-width: 100%;
      max-height: 100%;
    }

    .delete-mask {
      position: absolute;
      z-index: 100;
      background: rgba(0, 0, 0, 0.5);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: none;
      align-items: center;
      justify-content: center;

      .iconfont {
        color: #fff;
        font-size: 28px;
      }
    }
  }
}
.img-box {
  transform: scale(0.75);
  .img-box-tip {
    color: #000;
    span {
      overflow: hidden;
      word-break: break-all;
    }
  }
}
</style>
