<template>
  <div>
    <!-- 编辑弹窗 -->
    <ui-modal v-model="visible" :title="moduleAction.title" :styles="styles">
      <Form ref="formData" class="form-content edit-form" :model="formData" :rules="ruleCustom">
        <FormItem
          label="请选择算法厂商"
          class="right-item mb-sm"
          prop="algVendors"
          :label-width="130"
          style="margin-bottom: 20px"
        >
          <CheckboxGroup v-model="formData.algVendors">
            <Checkbox class="mr-lg" :label="item.key" v-for="(item, index) in arithmeticList" :key="index">
              <span>{{ item.value }}</span>
            </Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem
          label="相似度阈值"
          class="right-item mb-lg"
          prop="threshold"
          :label-width="130"
          v-if="modalData.strategy == 'PORTRAIT_FILE_ACCURACY'"
        >
          <InputNumber class="width-lg" :max="100" v-model="formData.threshold" placeholder="请输入相似度阈值">
          </InputNumber>
          <span class="base-text-color ml-xs">%</span>
        </FormItem>
      </Form>
      <template slot="footer">
        <Button @click="visible = false" class="plr-30">取 消</Button>
        <Button type="primary" @click="query" class="plr-30">确 定</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  max-height: 220px !important;
  height: 220px !important;
  overflow-y: auto;
  padding: 30px !important;
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    moduleAction: {
      default: () => {
        return {
          title: '选择算法',
          action: 'arithmetic',
        };
      },
    },
    modalData: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      formData: {
        algVendors: [],
        algProcess: '',
        threshold: '',
      },
      arithmeticList: {},
      ruleCustom: {
        algVendors: [{ required: true, type: 'array', min: 1, message: '请选择算法厂商', trigger: 'change' }],
        algProcess: [{ required: true, message: '请选择判断方式', trigger: 'change' }],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    async query() {
      try {
        let validate = await this.$refs.formData.validate();
        if (!validate) return;
        this.visible = false;
        this.$emit('getFormData', this.formData);
      } catch (err) {
        console.log(err);
      }
    },
    async getOptionAlgVendors() {
      try {
        let res = await this.$http.get(governanceevaluation.getOptionAlgVendors, {
          params: { indexId: this.modalData.indexId },
        });
        this.arithmeticList = res.data.data;
        this.$emit('arithmeticList', this.arithmeticList);
      } catch (err) {
        console.log(err);
      }
    },
    resetForm() {
      this.$refs.formData.resetFields();
      this.formData = {
        algVendors: this.modalData.algVendors || [],
        algProcess: this.modalData.algProcess,
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      this.getOptionAlgVendors(this.modalData);
      this.resetForm();
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {
    //单选算法
    isArithmetic() {
      return this.moduleAction.action === 'arithmetic';
    },
    //
  },
  components: {},
};
</script>
