<template>
  <div class="main-container">
    <div class="search-bar">
      <search-gait
        ref="searchBar"
        @search="searchHandle"
        @reset="resetHandle"
      />
    </div>
    <div class="table-container">
      <div class="data-export" v-if="dataList.length">
        <Button
          class="mr"
          :type="queryParam.sortField == 'similarity' ? 'primary' : 'default'"
          @click="handleAdvancedSort('similarity')"
          size="small"
          v-if="this.queryParam.features && this.queryParam.features.length > 0"
        >
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
        <Button
          class="mr"
          :type="queryParam.sortField == 'startTime' ? 'primary' : 'default'"
          @click="handleAdvancedSort('startTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <slot name="mutilAction"></slot>
      </div>
      <div class="table-container-box">
        <div class="table-content" @scroll="handleScroll">
          <div
            class="list-card box-1"
            v-for="(item, index) in dataList"
            :key="index"
            :class="{ checked: item.isChecked }"
          >
            <p class="img-content">
              <span class="num" v-if="item.similarity"
                >{{ item.similarity || "0" }}%</span
              >
              <ui-image
                :src="item.previewImage"
                alt="动态库"
                @click.native="handleDetail(item, index)"
              />
              <!-- <img :src="item.previewImage" alt="" v-viewer /> -->
            </p>
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.startTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-location"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{ item.cameraName }}</span>
              </p>
            </div>
            <div class="fast-operation-bar">
              <Poptip trigger="hover" placement="right-start">
                <i class="iconfont icon-gengduo"></i>
                <div class="mark-poptip" slot="content">
                  <p @click="archivesPage(item)">
                    <i class="iconfont icon-a-lianhe322"></i>以图搜图
                  </p>
                  <p @click="openDirectModel(item)">
                    <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                  </p>
                  <p @click="handleTargetAdd(item)">
                    <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
                  </p>
                </div>
              </Poptip>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 9 - (dataList.length % 9)"
            :key="index + 'demo'"
          ></div>
          <div class="loading-box" v-if="fallsPage">
            <ui-loading v-if="scrollLoading"></ui-loading>
          </div>
          <div class="gobacktop" @click="toTop">
            <div class="top-icon">
              <Icon type="md-download" />
            </div>
            <div class="top-text">
              <p>回到</p>
              <p>顶部</p>
            </div>
          </div>
        </div>
        <ui-empty
          v-if="dataList.length === 0 && !listLoading && !fallsPage"
        ></ui-empty>
        <ui-loading v-if="listLoading"></ui-loading>
        <!-- 分页 -->
        <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[27, 54, 81, 108]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <details-gait-modal
      v-show="detailsShow"
      ref="detailsGaitRef"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="detailsShow = false"
    >
    </details-gait-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="loadCancel"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="wranModalShow"
      title="提示"
      :r-width="500"
      @onCancel="onCancel"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
    <direction-model ref="directionModel"></direction-model>
  </div>
</template>

<script>
import detailsGaitModal from "@/components/detail/details-gait-modal.vue";
import searchGait from "../components/search-gait.vue";
import {
  queryGaitRecordSearchEx,
  humanDownload,
  taskView,
  picturePick,
} from "@/api/wisdom-cloud-search";
import { myMixins } from "../../components/mixin/index.js";
import { mapMutations, mapGetters, mapActions } from "vuex";
import exportBox from "../../components/export/export-box.vue";
import hlModal from "@/components/modal/index.vue";
import directionModel from "../components/direction-model";
import { deepCopy } from "@/util/modules/common";
export default {
  name: "",
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [myMixins], //全局的mixin
  components: {
    searchGait,
    detailsGaitModal,
    exportBox,
    hlModal,
    directionModel,
  },
  data() {
    return {
      dataList: [],
      listLoading: false,
      scrollLoading: false,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      detailsShow: false,
      queryParam: {
        sortField: "startTime",
        order: "desc",
        features: [],
      },
      checkAll: false,
      timeUpDown: false,
      similUpDown: false,
      exportShow: false,
      deviceKeyWords: "",
      modalShow: false,
      wranModalShow: false,
      downTaskId: "",
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0,
      mayRequest: false,
      fallsPage: false,
    };
  },
  watch: {
    exportShow(val) {
      //点击空白处隐藏
      if (val) {
        document.addEventListener("click", () => {
          this.exportShow = false;
        });
      } else {
        document.addEventListener("click", () => {});
      }
    },
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      globalObj: "systemParam/globalObj",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.recordId);
    },
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    async getDataList(page = 0) {
      this.dataList = [];
      this.listLoading = true;
      let params = JSON.parse(JSON.stringify(this.queryParam));
      delete params.urlList;
      delete params.selectDeviceList;
      queryGaitRecordSearchEx({ ...params, ...this.pageInfo })
        .then((res) => {
          this.total = res.data.total;
          this.dataList = res.data.entities || [];
          // 勾选
          if (this.dataList.length && this.upImageData.length) {
            this.dataList.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.recordId)) {
                e.isChecked = true;
              }
            });
          }
          if (page == 1) {
            this.$refs.detailsGaitRef.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.detailsGaitRef.nextPage(this.dataList);
          }
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索步态",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    cardScroll() {
      let params = JSON.parse(JSON.stringify(this.queryParam));
      delete params.urlList;
      delete params.selectDeviceList;
      queryGaitRecordSearchEx(params).then((res) => {
        if (res.data.entities.length == 0) {
          this.mayRequest = false;
        } else {
          // 勾选
          if (res.data.entities.length && this.upImageData.length) {
            res.data.entities.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.recordId)) {
                e.isChecked = true;
              }
            });
          }
          this.dataList.push(...res.data.entities);
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索步态",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
          let { pageNumber, pageSize } = this.pageInfo;
          if (pageNumber <= 3 && res.data.total > pageNumber * pageSize) {
            this.pageInfo.pageNumber += 1;
            this.queryList(false, false, true);
          } else {
            this.scrollLoading = false;
          }
          this.mayRequest = true;
        }
      });
    },
    // 瀑布流展示
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.scrollLoading = true;
      this.dataList = [];
      if (this.fallsPage) {
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 48,
        };
        this.queryList(false, false, true);
      } else {
        this.mayRequest = false;
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 27,
        };
        this.queryList();
      }
    },
    toTop() {
      let scrollTop = document.querySelector(".table-content");
      scrollTop.scrollTo(0, 0);
    },
    handleScroll() {
      // 距离顶部距离
      let scrollTop = document.querySelector(".table-content").scrollTop;
      // 可视区的高度
      let windowHelght = document.querySelector(".table-content").clientHeight;
      // 滚动条的总高度
      let scrollHeight = document.querySelector(".table-content").scrollHeight;
      // 计算底部距离
      let _dis = scrollTop + windowHelght;
      if (_dis + 1000 > scrollHeight && this.mayRequest) {
        this.pageInfo.pageNumber += 1;
        this.queryList(0, false);
      }
    },

    /**
     * @description: 获取列表
     * @param {number} page 翻页，1 - 前一页，2 - 后一页
     * @param {boolean} noRest 排序
     */
    queryList(page = 0, noRest = false) {
      this.queryParam = {
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
        ...this.$refs.searchBar.getQueryParams(),
      };
      this.queryParam.similarity = this.queryParam.similarity / 100;
      if (this.fallsPage) {
        this.cardScroll();
      } else {
        this.getDataList(page);
      }
    },
    // 导出
    handleExport($event) {
      $event.stopPropagation();
      this.exportShow = !this.exportShow;
    },
    loadCancel() {
      this.modalShow = false;
      this.wranModalShow = true;
    },
    onCancel() {
      this.modalShow = true;
      this.wranModalShow = false;
    },
    onOk() {
      this.modalShow = false;
      this.wranModalShow = false;
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },
    confirm(param) {
      let funparams = this.queryParam;
      let params = {};
      if (param.type == "1") {
        let list = this.dataList.filter((e) => e.isChecked);
        if (list.length > 0) {
          let ids = list.map((item) => item.recordId);
          params = {
            ids,
            downloadPics: param.downloadPics,
            downloadSize: null,
            ...funparams,
          };
          this.exportShow = false;
          this.modalShow = true;
        } else {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params = {
          ids: [],
          downloadPics: param.downloadPics,
          downloadSize: param.downloadSize,
          ...funparams,
        };
        this.exportShow = false;
        this.modalShow = true;
      }
      humanDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {});
    },
    searchHandle() {
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    // 更新搜索数据
    imgChange() {
      let queryParam = this.$refs.searchBar.queryParam;
      this.queryParam.features = queryParam.features;
      if (!queryParam.features || queryParam.features.length === 0) {
        this.queryParam.order = "desc";
        this.queryParam.sortField = "startTime";
      }
    },
    resetHandle() {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 27,
      };
      let queryParam = this.$refs.searchBar.queryParam;
      queryParam = { ...queryParam, ...this.pageInfo };
      this.queryParam = queryParam;
      this.queryParam.similarity = this.queryParam.similarity / 100;
      this.checkAll = false;
      this.getDataList();
    },
    // 详情
    handleDetail(row, index) {
      this.detailsShow = true;
      this.$refs.detailsGaitRef.init(
        row,
        this.dataList,
        index,
        this.pageInfo.pageNumber,
        this.$refs.searchBar.algorithmType
      );
    },
    checkAllHandler(val) {
      this.dataList = this.dataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    checkHandler(e, i) {
      this.dataList[i].isChecked = e;
      this.checkAll =
        this.dataList.filter((e) => e.isChecked).length === this.dataList.length
          ? true
          : false;
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    archivesPage(row) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=gaitContent&noMenu=1",
        query: {
          imgUrl: row.previewImage,
        },
      });
      window.open(href, "_blank");
    },
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList(false, true);
    },
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList(false, true);
    },
    /**
     * 上一个
     */
    prePage(pageNum) {
      if (pageNum < 1) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        if (!this.fallsPage) {
          this.queryList(1);
        }
      }
    },
    /**
     * 下一个
     */
    async nextPage(pageNum) {
      this.pageInfo.pageNumber = pageNum;
      let num = this.pageInfo.pageNumber;
      let size = this.pageInfo.pageSize;
      if (this.total <= num * size) {
        this.$Message.warning("已经是最后一个了");
        return;
      } else {
        if (!this.fallsPage) {
          this.queryList(2);
        }
      }
    },
    openDirectModel(row) {
      const { latitude, longitude, cameraName } = { ...row };
      this.$refs.directionModel.show({
        geoPoint: { lat: latitude, lon: longitude },
        deviceName: cameraName,
      });
    },
    // 目标添加
    handleTargetAdd(row) {
      let fileData = new FormData();
      fileData.append("algorithmType", this.$refs.searchBar.algorithmType);
      fileData.append("fileUrl", row.previewImage);
      picturePick(fileData).then(async (res) => {
        if (!res.data || (res.data && res.data.length == 0)) {
          this.$Message.warning("没有识别出目标！");
        } else {
          let { feature, imageUrl, imageBase } = res.data[0];
          let urlList = {
            fileUrl: imageUrl,
            feature: feature,
            imageBase: imageBase,
          };

          this.$refs.searchBar.urlImgList([urlList, ""], 2);
          this.pageInfo = {
            pageNumber: 1,
            pageSize: 27,
          };
          this.queryList();
        }
      });
    },
    // dataCograph(list) {
    //   this.pointCograph(list);
    // },
    dataAboveMapHandler() {
      this.pointCograph(this.dataList);
    },
    pointCograph(dataList) {
      // 已上图的不需要再次上图
      let seleNum = dataList.filter(
        (e) => e.isChecked && !this.alreadyUpImageIds.includes(e.recordId)
      );
      if (!seleNum.length) {
        this.$Message.warning("请选择上图数据");
        return;
      }
      // 判断是否有坐标信息
      let listHasLocation = seleNum.filter((item) => {
        return item.geoPoint && item.geoPoint.lat && item.geoPoint.lon;
      });
      if (!listHasLocation.length) {
        this.$Message.warning("无经纬度信息，无法上图");
        return;
      }
      if (listHasLocation.length < seleNum.length) {
        this.$Message.warning(
          `已过滤${seleNum.length - listHasLocation.length}条无经纬度信息的数据`
        );
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        listHasLocation.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = JSON.stringify(this.getListNum);
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      listHasLocation.map((item) => {
        item.delePoints = true;
        item.deleType = "gait";
      });
      this.$emit("dataAboveMapHandler", {
        type: "gait",
        list: listHasLocation,
        deleIdent: "gait-" + this.getListNum,
      });
    },
    // 获取选择的数据
    getSelectDataList() {
      const selectList = this.dataList.filter((e) => e.isChecked);
      return deepCopy(selectList);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";

.main-container .table-container .data-export {
  z-index: 8;
}
/deep/ .ui-image .ui-image-div img {
  width: fit-content !important;
  max-width: 100% !important;
}
</style>
