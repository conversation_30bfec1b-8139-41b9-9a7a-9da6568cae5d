<template>
  <!-- 人脸卡口在线率 提升 -->
  <div class="county-quantity-reach auto-fill">
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="abnormal-title">
          <div class="fl">
            <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
            <span class="f-16 color-filter ml-sm">检测结果统计</span>
          </div>
          <div class="export fr">
            <create-tabs
              class="inline mr-md"
              component-name="FaceCarTopOnline"
              tabs-text="查看本年度人卡历史最高在线情况"
              :tabs-query="{
                ...paramsData,
                orgRegionName,
                examineTime,
              }"
            >
              <span class="link f-16">查看本年度{{ getIndexName() }}卡口历史最高在线情况</span>
            </create-tabs>
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs f-14">导出</span>
            </Button>
          </div>
        </div>
        <div class="auto-fill">
          <ui-table class="auto-fill" :tableColumns="tableColumns" :table-data="tableData" :loading="loading">
            <template #qualifiedNum="{ row }">
              <create-tabs
                :component-name="themData.componentName"
                :tabs-text="themData.text"
                :tabs-query="{
                  ...paramsData,
                  orgRegionName: paramsList.displayType === 'REGION' ? row.civilName : row.orgName,
                  orgRegionCode: paramsList.displayType === 'REGION' ? row.civilCode : row.orgCode,
                  examineTime,
                  outcome: 1, //在线
                }"
              >
                <span class="link">{{ row.qualifiedNum }}</span>
              </create-tabs>
            </template>
            <template #unqualifiedNum="{ row }">
              <create-tabs
                :component-name="themData.componentName"
                :tabs-text="themData.text"
                :tabs-query="{
                  ...paramsData,
                  orgRegionName: paramsList.displayType === 'REGION' ? row.civilName : row.orgName,
                  orgRegionCode: paramsList.displayType === 'REGION' ? row.civilCode : row.orgCode,
                  examineTime,
                  outcome: 2, //在线
                }"
              >
                <span class="link">{{ row.unqualifiedNum }}</span>
              </create-tabs>
            </template>
          </ui-table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.county-quantity-reach {
  .content {
    color: #fff;
    background: @bg-blue-block;
    border-radius: 4px;
    position: relative;
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
}
.link {
  color: var(--color-primary);
  cursor: pointer;
  text-decoration: underline;
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
export default {
  mixins: [downLoadTips, dealWatch],
  name: 'face-car-online-rate-advance',
  data() {
    return {
      tableData: [],
      loading: false,
      exportLoading: false,
      paramsList: {},
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentLevel: 2, //组件标签层级 如果是标签组件套用标签组件需要此参数
      themData: {
        componentName: 'FaceCarOnlineNum', // 需要跳转的组件名
        text: '在线/离线人脸卡口', // 跳转页面标题
        title: '在线/离线人脸卡口',
        type: 'view',
      },
    };
  },
  computed: {
    tableColumns() {
      let title = this.getIndexName();
      return [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '所在市县',
          key: 'orgRegionName',
          align: 'center',
        },
        {
          title: '区域类型',
          key: 'typeName',
          align: 'center',
        },
        {
          title: `${title}卡口建设总量`,
          key: 'totalCount',
          align: 'center',
          width: 200,
        },
        {
          title: '检测数量',
          key: 'checkNum',
          align: 'center',
          width: 200,
        },
        {
          title: '在线数量',
          slot: 'qualifiedNum',
          align: 'center',
          width: 200,
        },
        {
          title: '离线数量',
          slot: 'unqualifiedNum',
          align: 'center',
          width: 200,
        },
        {
          title: '本年度最高在线数',
          key: 'maxOnline',
          align: 'center',
          width: 200,
        },
        {
          title: '在线率提升',
          key: 'onlineRateAdvance',
          align: 'center',
          width: 200,
        },
      ];
    },
  },
  methods: {
    getIndexName() {
      if (this.paramsList.indexId === 2016) {
        return '人脸';
      } else if (this.paramsList.indexId === 3020) {
        return '车辆';
      }
    },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: {
          exportType: 'second', //value：first、second、third
        },
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    async getEvaluationRecord() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getPolyData, params);
        this.tableData = data.entities || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    orgRegionName: {},
    examineTime: {},
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getEvaluationRecord();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
