<template>
	<div class="search  card-border-color">
		<Form :inline="true" :class="visible ? 'advanced-search-show':''">
			<div class="general-search">
				<div class="input-content">
					<div class="input-content-row">
						<FormItem label="视频身份:">
							<Input v-model="formData.vid" placeholder="请输入"></Input>
						</FormItem>
					</div>

				</div>
				<div class="btn-group" v-if="!visible">
					<Button type="primary" @click="search">查询</Button>
					<Button type="default" @click="resetForm">重置</Button>
				</div>
			</div>
		</Form>
	</div>
</template>
<script>
	export default {
		data() {
			return {
				visible: false,
				formData: {
					vid: ''
				}
			}
		},
		created() {

		},
		methods: {
			search() {
				let searchForm = {
					'vid': this.formData.vid,
					'st': '',
					'et': ''
				};
				this.$emit('searchInfo', searchForm)
			},
			resetForm() {
				this.formData = {
					vid: '',
				};
				this.$emit('searchInfo', this.formData)
			}
		}
	}
</script>
<style lang="less" scoped>
	.btn-group {
		display: flex;
		align-items: flex-end;
		margin-bottom: 16px;
		justify-content: flex-end;
		flex: 1;
	}
	.search {
		border-bottom: 1px solid #ffff;
		display: flex;
		width: 100%;
		justify-content: space-between;
		position: relative;
		.ivu-form-inline {
			width: 100%;
		}
		.ivu-form-item {
			margin-bottom: 16px;
			margin-right: 30px;
			display: flex;
			align-items: center;
			/deep/ .ivu-form-item-label {
				white-space: nowrap;
				width: 85px;
			}
			/deep/ .ivu-form-item-content {
				display: flex;
			}
		}
		.general-search {
			display: flex;
			width: 100%;
			.input-content {
				width: 85%;
				display: flex;
				flex-wrap: wrap;
				.input-content-row {
					display: flex;
				}
			}
		}
	}
</style>
