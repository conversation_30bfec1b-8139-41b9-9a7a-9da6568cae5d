/*
 * @Date: 2025-01-22 16:16:44
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-22 16:33:55
 * @FilePath: \icbd-view\src\views\ipbd-system\basicsConfig\juenileForm.js
 */
export const JuenileformData = {
  // 报警配置
  alarmLevelConfig: [{}, {}, {}],
  // 前科活跃分数配置
  activistsScoreConfig: {
    alarmScore: 1,
    appearInNightScore: 1,
    appearInRecreationScore: 1,
    classTimeOutsideScore: 1,
    travelAlongScore: 1,
  },
  // 深夜出现配置
  appearInNight: {
    dayTimeRange: [],
    deviceIds: [],
  },
  // 上课时间配置
  classTime: {
    afternoonTimeRange: [],
    morningTimeRange: [],
    otherExcludeRanges: [[]],
    summerVacationRange: [],
    winterVacationRange: [],
  },
  // 频繁出入娱乐场所的最小频次
  minAppearInRecreation: 0,
  // 娱乐场所设置的场所
  recreationGrounds: [
    {
      placeIds: [],
      subPlaceCode: "",
    },
  ],
  // 同行配置
  travelAlong: {
    maxIntervalSeconds: 10,
    minCount: 1,
  },
};
