<template>
  <div class="auto-fill bayonet-detection">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCountMa" />
    <TableList ref="infoList" class="infolist" :columns="columns" :loadData="loadDataList">
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <SearchBayonet
          :currentTree="currentTree"
          ref="search"
          @startSearch="startSearch"
          :treeData="treeData"
          :taskObj="taskObj"
        />
      </div>
      <!-- 表格操作 -->
      <template #action="{ row }">
        <ui-btn-tip icon="icon-chakanjietu" content="查看抓拍图片" @click.native="checkReason(row)"></ui-btn-tip>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" @click="deviceArchives(row)">{{ row.deviceId }}</span>
      </template>
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <template #deviceStatusText="{ row }">
        <span :class="row.deviceStatus == 1 ? 'sucess' : 'error'">{{ row.deviceStatusText }}</span>
      </template>
    </TableList>
    <look-pictute ref="lookPictute" title="车辆抓拍图片"></look-pictute>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';

export default {
  components: {
    TableList: require('../components/tableList.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    SearchBayonet: require('./component/searchBayonet').default,
    lookPictute: require('./component/look-pictute.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      treeData: [],
      searchData: {},

      loadDataList: (parameter) => {
        if (!this.taskObj.batchId) {
          console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        } else {
          return this.$http
            .post(
              this.currentTree.id === 413 ? governanceevaluation.onlinePageList : governanceevaluation.netWorkPageList,
              Object.assign(
                parameter,
                {
                  batchId: this.taskObj.batchId,
                  indexId: this.taskObj.indexId,
                  orgCode: this.taskObj.regionCode,
                },
                this.searchData,
              ),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
      columns: [
        { title: '设备在线状态', slot: 'deviceStatusText' },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 60,
          className: 'table-action-padding',
          fixed: 'right',
        },
        { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx' }, // 点位类型.
        { title: `${this.global.filedEnum.deviceId}`, key: 'deviceId', slot: 'deviceId' },
      ],
      abnormalCountMa: [],
      infoObj: {},
    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
      },
    },
  },
  filter: {},
  async created() {
    if (this.propertySearchLbdwlx.length === 0) await this.getAlldicData();
  },
  mounted() {
    this.info();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    info() {
      // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      // this.selectKey = orgCode || ''
      // if (!orgCode) return this.$Message.error('您没有此行政区划权限')
      // this.treeData =
      //   this.taskObj.orgCodeList && this.taskObj.orgCodeList.length > 0
      //     ? this.$util.common.arrayToJson(
      //         JSON.parse(JSON.stringify(this.taskObj.orgCodeList)),
      //         'id',
      //         'parentId'
      //       )
      //     : []
      this.$http
        .get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.taskObj.regionCode },
        })
        .then((res) => {
          this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(res.data.data)), 'id', 'parentId');
        });
      switch (this.currentTree.id) {
        case 414:
          this.abnormalCountMa = [
            { title: '车辆卡口总数量', icon: 'icon-cheliangkakoulianwangjiance', count: 0 },
            { title: '实际测设备数量', icon: 'icon-cheliangkakoulianwangjiance', count: 0 },
            { title: '已联网设备', icon: 'icon-cheliangkakoulianwangjiance', count: 0 },
            { title: '未联网设备', icon: 'icon-cheliangkakoulianwangjiance', count: 0 },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName', width: 200 },
            { title: '组织机构', key: 'orgName' },
            { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
            { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
            { title: this.global.filedEnum.ipAddr, key: 'ipAddr' },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx' }, // 点位类型
            { title: '近X天抓拍数量', key: 'imageNum' },
            // { title: '回传时间', key: 'timeDifference', slot: 'timeDifference' },
            {
              title: '设备联网状态',
              key: 'deviceStatusText',
              slot: 'deviceStatusText',
              width: 140,
            },
            // {
            //   title: '操作',
            //   slot: 'action',
            //   align: 'center',
            //   width: 60,
            //   className: 'table-action-padding',
            //   fixed: 'right',
            // },
          ];
          break;
        case 413:
          this.abnormalCountMa = [
            { title: '车辆卡口总数量', icon: 'icon-cheliangkakouzaixianjiance', count: 0 },
            { title: '实际测设备数量', icon: 'icon-cheliangkakouzaixianjiance', count: 0 },
            { title: '在线设备', icon: 'icon-cheliangkakouzaixianjiance', count: 0 },
            { title: '离线设备', icon: 'icon-cheliangkakouzaixianjiance', count: 0 },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
            },
            { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName', width: 200 },
            { title: '组织机构', key: 'orgName', tooltip: true, width: 120 },
            { title: `${this.global.filedEnum.longitude}`, key: 'longitude', width: 140 },
            { title: `${this.global.filedEnum.latitude}`, key: 'latitude', width: 140 },
            { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 140 },
            { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 140 }, // 点位类型
            { title: '过车数量', key: 'imageNum', width: 140 },
            { title: '设备在线状态', slot: 'deviceStatusText', width: 140 },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 70,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
      }
      this.static();
      this.$refs.infoList.info(true);
    },
    // 统计
    static() {
      if (this.taskObj.rootId) {
        this.$http
          .post(governanceevaluation.queryEvaluationBasicRecord, {
            resultId: this.taskObj.rootId,
            orgCode: this.selectKey,
            indexId: this.currentTree.indexId,
          })
          .then((res) => {
            let data = res.data.data || {};
            switch (this.currentTree.id) {
              case 414:
                this.abnormalCountMa = [
                  {
                    title: '车辆卡口总数量',
                    icon: 'icon-cheliangkakoulianwangjiance',
                    count: data.accuracyFieldsSumCount || 0,
                  },
                  {
                    title: '实际测设备数量',
                    icon: 'icon-cheliangkakoulianwangjiance',
                    count: data.accuracyFieldsSumCount || 0,
                  },
                  {
                    title: '已联网设备',
                    icon: 'icon-cheliangkakoulianwangjiance',
                    count: data.accuracyFieldsQualifiedCount || 0,
                  },
                  {
                    title: '未联网设备',
                    icon: 'icon-cheliangkakoulianwangjiance',
                    count: data.accuracyFieldsSumCount
                      ? data.accuracyFieldsSumCount - data.accuracyFieldsQualifiedCount
                      : 0,
                  },
                ];
                break;
              case 413:
                this.abnormalCountMa = [
                  {
                    title: '车辆卡口总数量',
                    icon: 'icon-cheliangkakouzaixianjiance',
                    count: data.accuracyFieldsSumCount || 0,
                  },
                  {
                    title: '实际测设备数量',
                    icon: 'icon-cheliangkakouzaixianjiance',
                    count: data.accuracyFieldsSumCount || 0,
                  },
                  {
                    title: '在线设备',
                    icon: 'icon-cheliangkakouzaixianjiance',
                    count: data.accuracyFieldsQualifiedCount || 0,
                  },
                  {
                    title: '离线设备',
                    icon: 'icon-cheliangkakouzaixianjiance',
                    count: data.accuracyFieldsSumCount
                      ? data.accuracyFieldsSumCount - data.accuracyFieldsQualifiedCount
                      : 0,
                  },
                ];
                break;
            }
          });
      }
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.searchData = Object.assign(this.searchData, searchData);
      this.$refs.infoList.info(true);
    },
    checkReason(row) {
      this.$refs.lookPictute.info(row);
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId ? item.deviceInfoId : item.id },
      });
      window.open(routeData.href, '_blank');
    },
  },
};
</script>
<style lang="less" scoped>
.infolist {
  margin-top: 10px;
}
.sucess {
  color: @color-success;
}
.error {
  color: @color-failed;
}
</style>
