<!--
    * @FileDescription: 首次入城
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-09 11:09:00
-->
<template>
  <div class="analyse-wrap">
    <div class="search_box" v-if="!isHideLeft">
      <div class="title">
        <p>首次入城</p>
      </div>
      <div class="form-box" v-if="isForm">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="80"
        >
          <FormItem label="开始时间:">
            <DatePicker
              style="width: 100%"
              v-model="formData.st"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="开始时间"
              transfer
            ></DatePicker>
          </FormItem>
          <FormItem label="结束时间:">
            <DatePicker
              style="width: 100%"
              v-model="formData.et"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="结束时间"
              transfer
            ></DatePicker>
          </FormItem>
          <FormItem label="回溯时长:">
            <Select v-model="formData.backDays" placeholder="请选择">
              <Option :value="7">一周</Option>
              <Option :value="30">一个月</Option>
              <Option :value="90">三个月</Option>
            </Select>
          </FormItem>
          <FormItem label="通行设备:">
            <ul class="search_content">
              <li
                class="active-area-sele"
                @click="handleSelemodel(selectDeviceList, false)"
              >
                选择设备/已选({{ formData.deviceGbIdList.length }})
              </li>
            </ul>
          </FormItem>
          <FormItem label="车牌号码:">
            <Input v-model="formData.plateNo" placeholder="请输入"></Input>
          </FormItem>
          <FormItem label="车牌颜色:">
            <ui-tag-select
              ref="bodyColor"
              @input="
                (e) => {
                  input(e, 'plateColor');
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in licensePlateColorList"
                :key="$index"
                effect="dark"
                :name="item.dataKey"
              >
                <div
                  v-if="licensePlateColorArray[item.dataKey]"
                  :style="{
                    borderColor:
                      licensePlateColorArray[item.dataKey].borderColor,
                  }"
                  class="plain-tag-color"
                >
                  <div
                    :style="licensePlateColorArray[item.dataKey].style"
                  ></div>
                </div>
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
          <FormItem label="车辆品牌:">
            <Select
              v-model="formData.vehicleBrand"
              filterable
              placeholder="请选择"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleBrandList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆颜色:">
            <Select v-model="formData.vehicleColor" placeholder="请选择">
              <Option
                v-for="(item, $index) in bodyColorList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆类型:">
            <Select
              v-model="formData.vehicleType"
              filterable
              placeholder="请选择车辆类型"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleClassTypeList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车牌类型:">
            <Select v-model="formData.plateClass" placeholder="请选择">
              <Option
                v-for="(item, $index) in plateClassList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>

          <div class="btn-group">
            <Button type="primary" @click="handleAddTask">创建任务</Button>
            <Button type="primary" @click="handleTaskList">查看任务</Button>
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
      <div class="task-list" v-else>
        <p class="result-header">
          <span>
            <Button
              v-if="!taskParams.taskResult"
              class="ml-10"
              type="primary"
              size="small"
              @click="goback()"
              >返回</Button
            >
            <Button
              class="ml-10"
              type="primary"
              size="small"
              @click="refreshTaskList()"
              >刷新</Button
            >
          </span>
          <span
            >共
            <span class="t-blue-color">{{ taskList.length }}</span> 条任务</span
          >
        </p>
        <div class="result-list">
          <div v-scroll style="height: 100%">
            <div
              class="result-item"
              :class="currentTaskId == item.id ? 'actived' : ''"
              v-for="item in taskList"
              :key="item.id"
              @click="handleView(item)"
            >
              <ul class="result-item-info">
                <li class="ellipsis">
                  <span class="label">任务名称:</span>
                  <span class="value" :title="item.taskName">
                    {{ item.taskName }}
                  </span>
                </li>
                <li class="ellipsis">
                  <span class="label">任务状态:</span>
                  <span
                    class="value"
                    :title="
                      item.taskRunState | commonFiltering(taskRunStateList)
                    "
                  >
                    {{ item.taskRunState | commonFiltering(taskRunStateList) }}
                  </span>
                </li>
                <li class="ellipsis">
                  <span class="label">运行时间:</span>
                  <span class="value" :title="item.taskStartTime">
                    {{ item.taskStartTime }}
                  </span>
                </li>
                <transition name="fade">
                  <div v-if="!!item.isShowDetail">
                    <li class="ellipsis">
                      <span class="label">开始时间:</span>
                      <span class="value" :title="item.taskParam.st">
                        {{ item.taskParam.st }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">结束时间:</span>
                      <span class="value" :title="item.taskParam.et">
                        {{ item.taskParam.et }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">回溯时长:</span>
                      <span class="value" :title="item.taskParam.backDays">
                        {{ item.taskParam.backDays }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">通行设备:</span>
                      <span class="value">
                        <li
                          class="active-area-sele"
                          @click.stop="
                            handleSelemodel(
                              item.taskParam.deviceList,
                              true,
                              item.id
                            )
                          "
                        >
                          已选({{ item.taskParam.deviceGbIdList.length }})
                        </li>
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">车牌号码:</span>
                      <span class="value" :title="item.taskParam.plateNo">
                        {{ item.taskParam.plateNo }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">车牌颜色:</span>
                      <span class="value">
                        {{
                          item.taskParam.plateColor
                            | commonFiltering(licensePlateColorList)
                        }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">车辆品牌:</span>
                      <span class="value">
                        {{
                          item.taskParam.vehicleBrand
                            | commonFiltering(vehicleBrandList)
                        }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">车辆颜色:</span>
                      <span class="value">
                        {{
                          item.taskParam.vehicleColor
                            | commonFiltering(bodyColorList)
                        }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">车辆类型:</span>
                      <span class="value">
                        {{
                          item.taskParam.vehicleType
                            | commonFiltering(vehicleClassTypeList)
                        }}
                      </span>
                    </li>
                    <li class="ellipsis">
                      <span class="label">车牌类型:</span>
                      <span class="value">
                        {{
                          item.taskParam.plateClass
                            | commonFiltering(plateClassList)
                        }}
                      </span>
                    </li>
                  </div>
                </transition>
              </ul>
              <div class="operate">
                <i
                  class="iconfont icon-jiantou"
                  :class="{ up: item.isShowDetail }"
                  title="参数详情"
                  @click.stop="toggleItem(item)"
                ></i>
                <i
                  class="iconfont icon-shanchu1"
                  title="删除"
                  @click.stop="deleteTask(item)"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table_box">
      <div class="search-bar">
        <searchForm ref="searchForm" @searchInfo="searchInfo"></searchForm>
      </div>
      <div class="data-export">
        <Button
          class="mr"
          type="default"
          @click="handleSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button class="mr" @click="handleExport($event)" size="small">
          <ui-icon type="daoru" color="#2C86F8"></ui-icon>
          导出
        </Button>
      </div>
      <div class="table_content">
        <div class="list-card" v-for="(item, index) in dataList" :key="index">
          <div class="img-content">
            <ui-image
              :src="item.traitImg"
              alt="动态库"
              @click.native="handleDetail(item, index)"
            />
            <b class="shade vehicle">
              <ui-plate-number
                :plateNo="item.plateNo"
                :color="item.plateColor"
                size="mini"
              ></ui-plate-number>
            </b>
          </div>
          <div class="bottom-info">
            <time>
              <Tooltip
                content="抓拍时间"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-time"></i>
              </Tooltip>
              {{ item.absTime }}
            </time>
            <p>
              <Tooltip
                content="抓拍地点"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-location"></i>
              </Tooltip>
              <span class="ellipsis" v-show-tips>{{ item.deviceName }}</span>
            </p>
          </div>
          <div class="fast-operation-bar">
            <Poptip trigger="hover" placement="right-start">
              <i class="iconfont icon-gengduo"></i>
              <div class="mark-poptip" slot="content">
                <p @click="archivesPage(item)">
                  <i class="iconfont icon-qiche1"></i> 以图搜图
                </p>
                <p @click="openDirectModel(item)">
                  <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                </p>
                <p v-if="item.plateNo" @click="toArchive(item)">
                  <i class="iconfont icon-chepai"></i>车辆档案
                </p>
                <p v-if="item.plateNo" @click="handleTrack(item)">
                  <i class="iconfont icon-huodongguilv"></i>行车轨迹
                </p>
              </div>
            </Poptip>
          </div>
        </div>
        <ui-empty v-if="dataList.length === 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="pageInfo.total"
        :page-size="pageInfo.pageSize"
        :page-size-opts="[21, 42, 84, 168]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />
    <direction-model ref="directionModel"></direction-model>
    <details-vehicle-modal
      v-show="vehicleShow"
      ref="vehicleDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="vehicleShow = false"
    >
    </details-vehicle-modal>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import { getDateTime } from "@/util/modules/common";
import {
  addFirstEntryCityTask,
  getFirstEntryCityTaskList,
  delFirstEntryCityTask,
  getFirstEntryCityTaskDetail,
} from "@/api/modelMarket";
import plateNumber from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/components/plate-number.vue";
import directionModel from "@/views/wisdom-cloud-search/search-center/advanced-search/components/direction-model.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import searchForm from "./search-form";
export default {
  components: {
    plateNumber,
    directionModel,
    detailsVehicleModal,
    searchForm,
  },
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      currentIndex: 0,
      vehicleShow: false,
      formData: {
        backDays: 7,
        vehicleColor: "",
        vehicleType: "",
        deviceGbIdList: [],
        st: "",
        et: "",
        plateNo: "",
        plateColor: "",
        vehicleBrand: "",
        plateClass: "",
      },
      queryParam: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 21,
        total: 0,
      },
      dataList: [],
      timeUpDown: false,
      loading: false,
      selectDeviceList: [],
      ruleValidate: {},
      checkedLabels: [], // 已选择的标签
      licensePlateColorArray,
      afterKey: undefined,
      isForm: true,
      taskList: [],
      currentTaskId: null,
      isHideLeft: false,
      nowTaskId: -1, // 当前查看的任务
    };
  },
  computed: {
    ...mapGetters({
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      plateClassList: "dictionary/getPlateClassList", // 车牌类型(枚举精准检索)
      vehicleBrandList: "dictionary/getVehicleBrandList", //车辆品牌
      taskRunStateList: "dictionary/getTaskRunStateList", //任务状态
    }),
  },
  async created() {
    await this.getDictData();
    this.$nextTick(() => {
      this.formData.st = getDateTime(-7);
      this.formData.et = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
      // 推荐中心查看
      console.log("taskParams: ", this.taskParams);
      if (!Toolkits.isEmptyObject(this.taskParams)) {
        if (this.taskParams.taskResult) {
          this.isHideLeft = true;
          this.currentTaskId = this.taskParams.taskResult.tacticTaskId;
          this.$refs.searchForm.search();
        }
      }
    });
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    input(e, key) {
      this.formData[key] = e;
    },
    // 创建任务
    handleAddTask() {
      if (this.formData.st > this.formData.et) {
        this.$Message.wraning("结束时间不能大于开始时间！");
        return;
      }
      this.loading = true;
      let params = {
        ...this.formData,
        st: this.$dayjs(this.formData.st).format("YYYY-MM-DD HH:mm:ss"),
        et: this.$dayjs(this.formData.et).format("YYYY-MM-DD HH:mm:ss"),
        deviceList: this.selectDeviceList,
      };
      addFirstEntryCityTask(params)
        .then((res) => {})
        .finally(() => {
          this.loading = false;
          this.handleTaskList();
        });
    },
    // 查看任务列表
    handleTaskList() {
      this.isForm = false;
      this.currentTaskId = null;
      this.dataList = [];
      this.loading = true;
      getFirstEntryCityTaskList()
        .then((res) => {
          res.data = res.data.map((v) => {
            v.taskParam = JSON.parse(v.taskParam);
            return v;
          });
          this.taskList = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    goback() {
      this.isForm = true;
      this.dataList = [];
    },
    refreshTaskList() {
      this.handleTaskList();
    },
    deleteTask(item) {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确认删除${item.taskName}?`,
        onOk: () => {
          delFirstEntryCityTask(item.id).then((res) => {
            this.refreshTaskList();
          });
        },
      });
    },
    handleView(item) {
      this.currentTaskId = item.id;
      this.queryParam = {};
      this.dataList = [];
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 21,
        total: 0,
      };
      this.$refs.searchForm.resetForm();
    },
    getList(isDetailNext) {
      this.loading = true;
      getFirstEntryCityTaskDetail({
        tacticTaskId: this.currentTaskId,
        ...this.pageInfo,
        asc: this.timeUpDown,
        ...this.queryParam,
      })
        .then((res) => {
          this.dataList = res.data.entities;
          this.pageInfo.total = res.data.total;
          if (isDetailNext) this.$refs.vehicleDetail.nextPage(this.dataList);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchInfo(obj) {
      if (!this.currentTaskId) {
        this.$Message.error("请先选择需要查询的任务");
        return;
      }
      this.pageInfo.pageNumber = 1;
      if (obj.selectDeviceList) {
        // 处理已选择设备
        obj.deviceGbIdList = obj.selectDeviceList.map((v) => v.deviceGbId);
      }
      this.queryParam = obj;
      this.getList();
    },
    // 重置
    handleReset() {
      this.formData = {
        backDays: 7,
        vehicleColor: "",
        vehicleType: "",
        deviceGbIdList: [],
        st: "",
        et: "",
        plateNo: "",
        plateColor: "",
        vehicleBrand: "",
        plateClass: "",
      };
      this.selectDeviceList = [];
    },
    handleSort() {
      if (!this.currentTaskId) {
        this.$Message.error("请先选择需要查看的任务");
        return;
      }
      this.timeUpDown = !this.timeUpDown;
      this.pageInfo.pageNumber = 1;
      this.getList();
    },
    handleExport() {},
    handleDetail(row, index) {
      this.currentIndex = index;
      this.vehicleShow = true;
      this.$refs.vehicleDetail.init(
        row,
        this.dataList,
        index,
        this.pageInfo.pageNumber
      );
    },
    /**
     * 选择设备
     * @param taskId 在任务查看界面 当前选中的任务id
     */
    handleSelemodel(list, isView = false, taskId = -1) {
      list.forEach((v, index) => (list[index].select = true));
      if (taskId !== -1) {
        this.nowTaskId = taskId;
      }
      this.$refs.selectDevice.show(list, "", "", isView);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list.map(
        ({ deviceId, deviceGbId, deviceName }) => {
          return { deviceId, deviceGbId, deviceName, select: true };
        }
      );
      this.formData.deviceGbIdList = list.map((item) => item.deviceGbId);
      // 任务查看时 更改通行设备
      if (this.nowTaskId !== -1) {
        this.taskList.forEach((v, index) => {
          if (this.taskList[index].id === this.nowTaskId) {
            this.taskList[index].taskParam.deviceList = list.map(
              ({ deviceId, deviceGbId, deviceName }) => {
                return { deviceId, deviceGbId, deviceName, select: true };
              }
            );
            this.taskList[index].taskParam.deviceGbIdList = list.map(
              (item) => item.deviceGbId
            );
          }
        });
      }
    },
    pageChange(page) {
      console.log(page, "page");
      this.pageInfo.pageNumber = page;
      this.getList();
    },
    pageSizeChange(size) {
      console.log(size, "size");
      this.pageInfo.pageSize = size;
      this.getList();
    },
    prePage(pageNum) {
      this.pageInfo.pageNumber = pageNum;
      console.log(pageNum, "pageNumber");
      this.getList(true);
    },
    /**
     * 下一个
     */
    nextPage(pageNum) {
      this.pageInfo.pageNumber = pageNum;
      console.log(pageNum, "pageNumber");
      this.getList(true);
    },
    archivesPage(row) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1",
        query: {
          imgUrl: row.traitImg,
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    openDirectModel(row) {
      const { geoPoint, deviceName } = { ...row };
      this.$refs.directionModel.show({ geoPoint, deviceName });
    },
    // 车辆档案
    toArchive(row) {
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query: {
          archiveNo: JSON.stringify(row.plateNo + "_" + row.plateColor),
          plateNo: JSON.stringify(row.plateNo),
          source: "car",
          idcardNo: row.idcardNo,
        },
      });
      window.open(href, "_blank");
    },
    handleTrack(row) {
      const { href } = this.$router.resolve({
        path: "/model-market/vehicle-warfare/car-path?noMenu=1",
        query: {
          plateNo: row.plateNo,
          absTime: this.$dayjs(row.absTime).valueOf(),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    toggleItem(row) {
      this.$set(row, "isShowDetail", !row.isShowDetail);
    },
  },
};
</script>
<style lang="less" scoped>
@import "../../style/index";
@import "../../style/vehicle";
/deep/ .ivu-tag-select-option {
  margin-right: 5px !important;
}
.page-button {
  text-align: right;
  padding-bottom: 10px;
}
.btn-group {
  display: flex;
  justify-content: space-between;
  button {
    flex: 1;
  }
}
.task-list {
  height: calc(~"100% - 40px");
  .result-header {
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    text-align: right;
    // color: #989cad;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .result-list {
    height: calc(~"100% - 40px");
    .result-item {
      display: flex;
      .result-item-info {
        padding: 10px;
        border-bottom: 1px solid #d3d7de;
        cursor: pointer;
        overflow: hidden;
      }
      &:hover {
        background: rgba(44, 134, 248, 0.2);
        .iconfont {
          opacity: 1;
          color: #2c86f8;
        }
      }
      &.actived {
        background: rgba(44, 134, 248, 0.2);
      }
      .operate {
        position: relative;
      }
      .icon-shanchu1 {
        opacity: 0;
        padding: 5px;
        cursor: pointer;
      }
      .icon-jiantou {
        cursor: pointer;
        position: absolute;
        bottom: 5px;
        &.up {
          transform: rotate(180deg);
        }
      }
      .active-area-sele {
        width: 100px;
        height: 22px;
        border-radius: 4px;
        text-align: center;
        line-height: 22px;
        cursor: pointer;
        border: 1px dashed #2c86f8;
        background: rgba(44, 134, 248, 0.1);
        color: rgba(44, 134, 248, 1);
        display: inline-block;
        margin-left: 5px;
      }
    }
  }
}
</style>
