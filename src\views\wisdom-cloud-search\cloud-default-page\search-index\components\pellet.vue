<template>
  <div id="indexLizi" class="pellet"></div>
</template>
<script>
  export default {
    data() {
      return {
      }
    },
    created() {
      let _that = this;
      let commonColor = new THREE.Color();
      let screenWidth = document.documentElement.clientWidth;
      let screenHeight = document.documentElement.clientHeight;
      let screenRatio = screenWidth / screenHeight;
      let screenCenterX = screenWidth;
      let screenCenterY = screenHeight;
      const setupScene = () => {
        const scene = new THREE.Scene();
        // track mouse movement 
        let mouse = {
          x: screenCenterX,
          y: screenCenterY
        };
        // setup renderer 
        const renderer = new THREE.CanvasRenderer({
          alpha: true,
          antialias: true,
          precision: 'mediump'
        });
        renderer.setSize(screenWidth, screenHeight);
        // renderer.setPixelRatio(window.devicePixelRatio);
        renderer.setClearColor(0xffffff, 0);
        renderer.sortObjects = true;
        renderer.domElement.setAttribute('id', 'stageElement');
        document.getElementById('indexLizi').appendChild(renderer.domElement);
        // setup camera 
        const camera = new THREE.PerspectiveCamera(75, screenRatio, 1, 1500);
        camera.position.set(0, 0, 300);
        camera.rotation.set(-100, 0, -100);
        camera.lookAt(scene.position);
        // setup light source 
        const light = new THREE.PointLight(0xffffff, 1, 10000);
        light.position.set(0, 200, -500);
        light.castShadow = false;
        light.target = scene;
        light.color = commonColor;
        scene.add(light);
        // setup objects 
        mountains.create(scene);
        groundPlain.create(scene);
        // animation loop 
        const loop = () => {
          requestAnimationFrame(loop);
          // update objects 
          mountains.update(mouse);
          groundPlain.update(mouse);

          // render scene 
          renderer.render(scene, camera);
        };
        loop();
      };
      const LoaderHelper = {
        _data: {},
        _loaded: 0,
        _cb: null,
        // get loaded resource by name  
        get(name) {
          return this._data[name] || null;
        },
        // complete handler 
        onReady(cb) {
          this._cb = cb;
        },
        // common error handler 
        onError(err) {
          console.error(err.message || err);
        },
        // when a resource is loaded 
        onData(name, data) {
          this._loaded += 1;
          this._data[name] = data;
          let total = Object.keys(this._data).length;
          let loaded = (total && this._loaded === total);
          let hascb = (typeof this._cb === 'function');
          if (loaded && hascb) this._cb(total);
        },
        // load image file 
        loadTexture(name, file) {
          if (!name || !file) return;
          this._data[name] = new THREE.Texture();
          const path = file;
          const loader = new THREE.TextureLoader();
          loader.load(path, data => {
            this.onData(name, data)
          }, null, this.onError);
        },
      };
      const mountains = {
        group: null,
        simplex: null,
        geometry: null,
        move: {
          x: 0,
          y: 0,
          z: -3500
        },
        look: {
          x: 0,
          y: 0,
          z: 0
        },
        create(scene) {
          this.group = new THREE.Object3D();
          this.group.position.set(this.move.x, this.move.y, this.move.z);
          this.group.rotation.set(this.look.x, this.look.y, this.look.z);
          this.simplex = new SimplexNoise();
          this.geometry = new THREE.PlaneGeometry(1000, 1000, 128, 32);
        },
        // update 
        update(mouse) {
          this.move.x = -(mouse.x * 0.02);
          _that.addEase(this.group.position, this.move, this.ease);
          _that.addEase(this.group.rotation, this.look, this.ease);
        },
      };
      const groundPlain = {
        group: null,
        geometry: null,
        material: null,
        plane: null,
        simplex: null,
        factor: 300, // smoothness 
        scale: 30, // terrain size
        speed: 0.008, // move speed 
        cycle: 0,
        ease: 12,
        move: {
          x: 0,
          y: -300,
          z: -1000
        },
        look: {
          x: 29.8,
          y: 0,
          z: 29.7
        },
        // create
        create(scene) {
          this.group = new THREE.Object3D();
          this.group.position.set(this.move.x, this.move.y, this.move.z);
          this.group.rotation.set(this.look.x, this.look.y, this.look.z);
          this.geometry = new THREE.PlaneGeometry(6000, 2000, 128, 64);
          this.material = new THREE.MeshBasicMaterial({
            color: 0x5C87F1,
            opacity: 0.2,
            // blending: THREE.NoBlending,
            // side: THREE.FrontSide,
            // transparent: false,
            // depthTest: false,
            wireframe: true,
            wireframeLinewidth: 1,
            wireframeLinecap: 'round',
            wireframeLinejoin: 'round',
            shading: THREE.NoShading
          });
          this.plane = new THREE.Mesh(this.geometry, this.material);
          this.plane.position.set(0, 0, 0);
          this.simplex = new SimplexNoise();
          this.moveNoise();
          this.group.add(this.plane);
          scene.add(this.group);
        },

        // change noise values over time 
        moveNoise() {
          for (let vertex of this.geometry.vertices) {
            let xoff = (vertex.x / this.factor);
            let yoff = (vertex.y / this.factor) + this.cycle;
            let rand = this.simplex.noise2D(xoff, yoff) * this.scale;
            vertex.z = rand;
          }
          this.geometry.verticesNeedUpdate = true;
          this.cycle += this.speed;
        },

        // update
        update(mouse) {
          this.moveNoise();
          this.move.x = -(mouse.x * 0.04);
          _that.addEase(this.group.position, this.move, this.ease);
          _that.addEase(this.group.rotation, this.look, this.ease);
        },
      };
      LoaderHelper.onReady(setupScene);
      LoaderHelper.loadTexture('mountainTexture', 'https://wow.techbrood.com/uploads/1908/fight//terrain2.jpg');
      LoaderHelper.loadTexture('engineTexture', 'https://wow.techbrood.com/uploads/1908/fight//water.jpg');
    },
    methods: {
      setupScene() {
        
      },
      addEase(pos, to, ease) {
        pos.x += (to.x - pos.x) / ease;
        pos.y += (to.y - pos.y) / ease;
        pos.z += (to.z - pos.z) / ease; 
      }
    }
  }
</script>
<style lang="less" scoped>
  .pellet {
    width: 100%;
    height: 100%;
  }
</style>