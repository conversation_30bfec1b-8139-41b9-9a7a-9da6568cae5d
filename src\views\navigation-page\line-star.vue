<template>
  <div class="container">
    <div class="line-black" v-for="item in 9" :key="item"></div>
    <div class="animations"></div>
    <div class="animations2"></div>
  </div>
</template>

<script>
export default {
  name: 'line-star',
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.animations {
  z-index: 1;
  position: absolute;
  background: url('~@/assets/img/navigation-page/start.png') no-repeat;
  background-size: cover;
  width: 109px;
  height: 10px;
  animation: move 4s infinite linear;
}
.animations2 {
  z-index: 1;
  position: absolute;
  background: url('~@/assets/img/navigation-page/start.png') no-repeat;
  background-size: cover;
  width: 109px;
  height: 10px;
  opacity: 0;
  animation: move 4s 2s infinite linear;
}

@keyframes move {
  0% {
    opacity: 1;
    transform: translate(0, -50%);
  }
  100% {
    transform: translate(1480%, -50%);
    opacity: 1;
  }
}

.container {
  position: relative;
  width: 100%;
  height: 8px;
  background-color: #11dff9;
  border-top: 3px solid #19a5e5;
  border-bottom: 3px solid #19a5e5;
  display: flex;
  justify-content: space-between;

  .line-black {
    z-index: 2;
    width: 45px;
    height: 19px;
    background: #0d84d8;
    border-radius: 5px;
    position: relative;
    top: -10px;
    margin-left: 45px;
    margin-right: 45px;
  }
}
</style>
