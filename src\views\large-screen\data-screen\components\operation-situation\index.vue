<!--
    * @FileDescription: 数据专题大屏-运营态势
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="screen-content">
        <div class="screen-left">
            <card title="活跃用户排名" class="screen-box">
                <div class="table">
                    <div class="table-header">
                        <div class="table-column-pm">排名</div>
                        <div class="table-column-fxj">分县局</div>
                        <div class="table-column-xysc">平均响应时长(小时)</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in timeList" :key="index">
                            <div class="table-column-pm">
                                <img v-if="index<3" :src="getImgUrl('ranking-'+index+'.png') " alt="">
                                <span v-else>{{ item.ranking }}</span>
                            </div>
                            <div class="table-column-fxj">{{ item.projectName }}</div>
                            <div class="table-column-xysc">
                                <p class="time time-red" v-if="item.type == 1">{{ item.time }}</p>
                                <p class="time time-greed" v-if="item.type == 2">{{ item.time }}</p>
                                <img :src="item.type == 1 ?getImgUrl('down.png') : getImgUrl('up.png')" alt="">
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
            <card title="高频操作功能排名" class="screen-box">
                <div class="ranking">
                    <div class="ranking-no2">
                        <img :src="getImgUrl('icon-no2.png')" alt="">
                        <p class="ranking-name">{{ frequencyList[1].projectName }}</p>
                        <count-to :start-val="0" :end-val="frequencyList[1].projectValue" :duration="1000" class="ranking-num"></count-to>
                    </div>
                    <div class="ranking-no1">
                        <img :src="getImgUrl('icon-no1.png')" alt="">
                        <p class="ranking-name">{{ frequencyList[0].projectName }}</p>
                        <count-to :start-val="0" :end-val="frequencyList[0].projectValue" :duration="1000" class="ranking-num"></count-to>
                    </div>
                    <div class="ranking-no3">
                        <img :src="getImgUrl('icon-no3.png')" alt="">
                        <p class="ranking-name">{{ frequencyList[2].projectName }}</p>
                        <count-to :start-val="0" :end-val="frequencyList[2].projectValue" :duration="1000" class="ranking-num"></count-to>
                    </div>
                </div>
                <div class="table-list">
                    <div class="td-li" v-for="(item, index) in frequencyList.slice(3,8)" :key="index">
                        <div class="td-index">{{ item.rankIndex }}</div>
                        <div class="td-name">{{ item.projectName }}</div>
                        <div class="td-num">
                            <count-to :start-val="0" :end-val="item.projectValue" :duration="1000" class="data-box-num"></count-to>
                        </div>
                    </div>
                </div>
            </card>
            <card title="高频操作设备排名" class="screen-box" :padding="0">
                <rangking-chart ref="rangkChart"></rangking-chart>
            </card>
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        用户统计
                    </div>
                    <div class="view-box-top"> 
                        <p>平均用户数量</p>
                        <p class="gross">
                            <count-to :start-val="0" :end-val="1339" :duration="1000" class="dinpro"></count-to>
                        </p>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-bot"> 
                        <p class="new-today">活跃用户数量</p>
                        <div class="chain-system">
                            <count-to :start-val="0" :end-val="376" :duration="1000" class="dinpro"></count-to>
                        </div>
                    </div>
                </div>
                <div class="content-box-right view-box">
                    <div class="title-img bigtitle">
                        优化建议转化
                    </div>
                    <div class="view-box-top"> 
                        <p>平台优化建议</p>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>总数</p>
                            <span class="dinpromini">{{ platformData.suggestTotal || 0}}</span>
                        </div>
                        <div class="optimize">
                            <div class="dot"></div>
                            <p>采纳</p>
                            <span class="dinpromini">{{ platformData.suggestAdopt || 0 }}</span>
                        </div>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-bot-right"> 
                        <div class="data-percent">{{platformData.suggestAdoptRate || 0}}%</div>
                        <p>建议转化率</p>
                    </div>
                </div>
            </div>
            <map-chart ref="mapChart" class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="平台战果统计" class="screen-box">
                <month-chart></month-chart>
            </card>
            <card title="平台维护情况统计" class="screen-box">
                <div class="maintain-box">
                    <div class="maintain-box-top">
                        <img :src="getImgUrl('gz.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ platformData.faultTotal || 0 }}</p>
                            <p class="detail-li-title">故障数量</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <img :src="getImgUrl('yjj.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ platformData.faultSolve || 0 }}</p>
                            <p class="detail-li-title">已解决</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <img :src="getImgUrl('djj.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ platformData.faultUnSolve || 0 }}</p>
                            <p class="detail-li-title">待解决</p>
                        </div>
                    </div>
                </div>
            </card>
            <card title="平台故障解决时间" class="screen-box">
                <div class="timeShow-box">
                    <div class="box-content">
                        <img :src="getImgUrl('pjxy.png')" alt="">
                        <div class="box-time box-left-time">{{ platformData.faultAvgTime || 0}}h</div>
                        <p class="box-title">平均响应时间</p>
                    </div>
                    <div class="box-content">
                        <img :src="getImgUrl('pjjj.png')" alt="">
                        <div class="box-time box-right-time">{{ platformData.faultSolveTime || 0}}h</div>
                        <p class="box-title">平均解决时间</p>
                    </div>
                </div>
            </card>
        </div>
    </div>
</template>
<script>
import { activeUserStat, highFrequencyDevice, highFrequencyOperation, platformInfo, regionStat } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import monthChart from './month-tendency-chart.vue';
import mapChart from './map-chart.vue';
import timeFrameChart from './time-frame-chart.vue';
import rangkingChart from './rangking-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        monthChart,
        mapChart,
        timeFrameChart,
        rangkingChart,
        CountTo
    },
    data() {
        return {
            titleImg: require(`@/assets/img/screen/midtitle.png`),
            dataList: [],
            timeList: [
                { ranking: 1, name: 'XXX分局', time: '0.4h', type: 1, rankingUrl: 'ranking-0.png'},
                { ranking: 2, name: 'XXX分局', time: '0.8h', type: 2, rankingUrl: 'ranking-1.png'},
                { ranking: 3, name: 'XXX分局', time: '1h', type: 1, rankingUrl: 'ranking-2.png'},
                { ranking: 4, name: 'XXX分局', time: '1.2h', type: 2},
                { ranking: 5, name: 'XXX分局', time: '1.5h', type: 2},
                { ranking: 6, name: 'XXX分局', time: '2h', type: 1},
                { ranking: 7, name: 'XXX分局', time: '1.9h', type: 2},
            ],
            frequencyList: [
                { index: 4, name: 'XXX分局', num: 100234 },
                { index: 5, name: 'XXX分局', num: 93234 },
                { index: 6, name: 'XXX分局', num: 83234 },
                { index: 7, name: 'XXX分局', num: 73234 },
                { index: 8, name: 'XXX分局', num: 63234 },
            ],
            platformData:{}
        }
    },
    created() {
        this.init();
        this.queryMap();
    },
    methods: {
        init() {
            activeUserStat()
            .then(res => {
                res.data.forEach((item,  index) => {
                    this.timeList[index].projectName = item.projectName;
                    this.timeList[index].time = item.projectValue
                })
            })
            highFrequencyDevice()
            .then(res => {
                let list = res.data.length > 6? res.data.slice(0,6) : res.data;
                this.$refs.rangkChart.init(list)
            })
            highFrequencyOperation()
            .then(res => {
                this.frequencyList = [];
                res.data.forEach((item, index) => {
                    this.frequencyList.push(
                        { ...item, rankIndex: index+1, projectValue: Number(item.projectValue) }
                    )
                })
            })
            platformInfo()
            .then(res => {
                this.platformData = res.data;
            })
        },
        queryMap() {
            regionStat(3)
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        },
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    }
}
</script>
<style lang='less' scoped>
@import "../common/style.less";
.screen-content{
    display: flex;
    width: 100%;
    height: 100%;
    .screen-left, 
    .screen-right{
        width: 420px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .screen-box{
            height: calc( ~'33% - 5px');
            position: relative;
            .tab{
                display: flex;
                position: absolute;
                top: 10px;
                right: 6px;
                .tab-btn{
                    padding: 2px 4px;
                    background: rgba(0, 150, 255, .1);
                    color: #7195D0;
                    font-size: 12px;
                    font-weight: 400;
                    margin-left: 5px;
                    cursor: pointer;
                }
                .tab-btn-active{
                    color: #ffffff;
                    background: linear-gradient(180deg, rgba(8,224,255,0) 0%, #08E0FF 100%);
                }
            }
            .data-box-ul{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                height: 100%;
                overflow-y: auto;
            }
            .data-box-ul ::-webkit-scrollbar {
                width: 1px;
            }
            .data-box{
                background: rgba(24, 98, 187, 0.1);
                width: 195px;
                height: calc( ~'50% - 5px');
                padding: 10px 10px 5px 10px;
                margin-bottom: 10px;
                .data-box-top{
                    display: flex;
                    justify-content: space-between;
                    .box-top-left{
                        .data-box-name{
                            font-size: 14px;
                            font-weight: 400;
                            color: #ffffff;
                        }
                        .data-box-num{
                            font-family: 'DINPro';
                            font-size: 20px;
                            color: #03A9FF;
                        }
                    }
                    .icon-type{
                        width: 30px;
                        height: 30px;
                    }
                }
                .data-box-add{
                    font-size: 12px;
                    font-weight: 400;
                    color: #ffffff;
                    margin-top: 3%;
                }
                .box-add{
                    display: flex;
                    .box-add-left{
                        width: 100px;
                        font-size: 14px;
                        color: #2DDF5C;
                    }
                    .box-add-right{
                        font-size: 14px;
                        color: #2DDF5C;
                        display: flex;
                        align-items: center;
                        img{
                            width: 12px;
                            height: 14px;
                            margin-left: 10px;
                        }
                    }
                }
            }
            .data-box:last-child{
                margin-bottom: 0;
            }
            .data-box:nth-last-child(2){
                margin-bottom: 0;
            }
            .maintain-box{
                display:flex;
                flex-wrap: wrap;
                justify-content: space-between;
                height: 100%;
                .maintain-box-top{
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-evenly;
                }
                .maintain-box-bot{
                    width: 49%;
                    background: rgba(24,98, 187, 0.1);
                    display: flex;
                    justify-content: space-evenly;
                    align-items: center;
                    position: relative;
                }
                .detail-li{
                    text-align: center;
                    .detail-li-num{
                        font-size: 30px;
                        font-weight: 700;
                        color: #F1FCFF;
                    }
                    .detail-li-title{
                        font-size: 14px;
                        font-weight: 400;
                        color: #ffffff;
                    }
                }
            }
            
            .timeShow-box{
                display: flex;
                padding: 0 60px;
                justify-content: space-between;
                height: 100%;
                align-items: center;
                .box-title{
                    font-size: 14px;
                    font-weight: 400;
                    color: #ffffff;
                }
                .box-content{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .box-time{
                        width: 91px;
                        height: 39px;
                        color: #ffffff;
                        border: 2px solid;
                        border-top: none;
                        font-size: 26px;
                        font-weight: 700;
                        font-family: 'DINPro';
                        text-align: center;
                        margin: 15px 0 10px;
                    }
                    .box-left-time{
                        background: linear-gradient(180deg, rgba(20,142,255,0) 0%, rgba(20,142,255, 0.5) 100%);
                        border-image: linear-gradient(180deg, rgba(20, 142, 255, 0), rgba(20,142,255,1)) 2 2;
                    }
                    .box-right-time{
                        background: linear-gradient(180deg, rgba(255,227,46,0) 0%, rgba(225,227,46, 0.5) 100%);
                        border-image: linear-gradient(180deg, rgba(255, 227, 46, 0), rgba(225,227,46, 1)) 2 2;
                    }
                }
            }
        }
    }
    .screen-main{
        flex: 1;
        position: relative;
        padding: 10px 20px 0 20px;
        height: 100%;
        background: url('~@/assets/img/screen/screen-quan.png') no-repeat;
        background-position: center;
        background-size: auto;
        display: flex;
        flex-direction: column;
        .content-top{
            display: flex;
            justify-content: space-around;
            .view-box{
                width: 411px;
                height: 170px;
                background: url('~@/assets/img/screen/midboxbg.png') no-repeat;
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 44px;
                .title-img{
                    width: 150px;
                    height: 40px;
                    position: absolute;
                    background: url('~@/assets/img/screen/midtitle.png') no-repeat;
                    left: 50%;
                    transform: translateX(-50%);
                    top: -20px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 20px;
                    color: #ffffff;
                }
                .view-box-top{
                    color: #fff;
                    font-size: 14px;
                    font-weight: 400;
                    height: 50%;
                    margin-top: 10px;
                    .gross{
                        font-size: 28px;
                        color: #F1FCFF;
                        margin-top: 23px;
                        font-family: DINPro;
                        // span{
                        //     font-size: 14px;
                        //     color: #03A9FF;
                        // }
                    }
                    .optimize{
                        display: flex;
                        align-items: center;
                        .dot{
                            width: 8px;
                            height: 8px;
                            background: #F1FCFF;
                            box-shadow: 0px 0px 5px 0px #0988FF;
                            border: 1px solid #08E0FF;
                            border-radius: 4px;
                        }
                        p{
                            margin: 0 10px;
                        }
                        span{
                            color: #03A9FF;
                            font-size: 20px;
                            font-weight: 700;
                        }
                    }
                }
                .view-box-bot-right{
                    width: 110px;
                    height: 110px;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    background: url('~@/assets/img/screen/green-ball.png') no-repeat;
                    .data-percent{
                        font-weight: 700;
                        font-size: 24px;
                        color: #ffffff;
                    }
                    p{
                        font-weight: 400;
                        font-size: 14px;
                        color: #ffffff;
                    }
                }
                .view-box-bot{
                    margin-top: 10px;
                    .new-today{
                        font-size: 14px;
                        color: #ffffff;
                        span{
                            font-size: 14px;
                            color: #2DDF6C;
                            margin-left: 10px;
                        }
                    }
                    .chain-system{
                        display: flex;
                        margin-top: 23px;
                        font-size: 28px;
                        color: #9EF98C;
                        font-family: DINPro;
                        .chain-system-num{
                            display: flex; 
                            align-items: center;
                            margin-left: 10px;
                            p{
                                font-size: 14px;
                                color: #03A9FF;
                            }
                            img{
                                width: 12px;
                                height: 14px;
                            }
                        }
                    }
                }
            }
        }
        .map-chart{
            flex:1;
        }
    }
}
</style>