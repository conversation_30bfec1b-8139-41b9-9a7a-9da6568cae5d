.area-container {
  border: 1px solid var(--border-color);
  .tree-wrapper {
    height: 600px;
    opacity: 1;
    overflow: auto;
    // el-tree 样式重置
    /deep/ .el-tree {
      .el-tree-node {
        .el-tree-node__content {
          height: 40px;
          &.has-child-panel {
            margin-bottom: 5px;
          }
          .custom-tree-node {
            line-height: 34px;
          }
          .el-tree-node__expand-icon {
            margin-left: 20px;
            font-size: 16px;
            margin-top: -2px;
          }
        }
      }
    }
    .ui-search-tree {
      height: 100%;
      padding-top: 0;
    }

    .options {
      display: flex;
      align-items: center;

      span {
        padding-left: 5px;
      }
    }
  }

  .w150 {
    width: 150px;
  }

  .w200 {
    width: 200px;
  }

  .w250 {
    width: 250px;
  }

  .w300 {
    width: 300px;
  }
}

@{_deep} .ivu-modal-body {
  max-height: 100% !important;
}
