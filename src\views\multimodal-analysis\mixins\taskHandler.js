// 处理任务删除资源删除的公共逻辑
import {
  multiModalTaskPageList,
  multiModalTaskDelete,
  multiModalTaskStart,
  multiModalTaskStop,
  multiModalTaskResourceDelete,
} from "@/api/multimodal-analysis.js";
export default {
  data: function () {
    return {
      selectedData: [],
    };
  },
  methods: {
    onSelect(selection) {
      this.selectedData = selection;
    },
    onSelectCancel(selection) {
      this.selectedData = selection;
    },
    onSelectAll(selection) {
      this.selectedData = selection;
    },
    onSelectAllCancel() {
      this.selectedData = [];
    },
    //删除---删除任务&批量删除任务
    deleteJobs(data) {
      const ids = data.map((item) => item.id);
      multiModalTaskDelete(ids).then((res) => {
        if (res.msg == "成功") {
          this.$Message.success("删除成功");
          this.getList();
        }
      });
    },
    //批量删除子任务
    deleteTasks(data) {
      const ids = data.map((item) => item.id);
      multiModalTaskResourceDelete(ids).then((res) => {
        if (res.msg == "成功") {
          this.$Message.success("删除成功");
          this.getList();
        }
      });
    },
    // 任务开启
    async startJobs(data) {
      const ids = data.map((item) => item.id);
      try {
        const res = await multiModalTaskStart(ids);
        if (res.msg == "成功") {
          this.$Message.success("开启成功");
        }
        return true;
      } catch (e) {
        console.log(e);
        return false;
      }
    },
    // 任务结束
    async stopJobs(data) {
      const ids = data.map((item) => item.id);
      try {
        const res = await multiModalTaskStop(ids);
        if (res.msg == "成功") {
          this.$Message.success("结束成功");
        }
        return true;
      } catch (e) {
        console.log(e);
        return false;
      }
    },
    // 资源检索
    toDetailByTask(val) {
      const { resourceId, taskResourceList = [] } = val;
      let resourceIds = [];
      if (taskResourceList?.length > 0) {
        resourceIds = taskResourceList.map((item) => item.resourceId);
      } else {
        resourceIds = resourceId ? [resourceId] : [];
      }
      const param = {
        taskIds: val.taskId,
        taskType: val.structuredParsingType,
        resourceIds,
      };
      const { href } = this.$router.resolve({
        path: "/multimodal-analysis/multimodal-analysis-lib",
        query: {
          ...param,
          noMenu: 1,
        },
      });
      window.open(href, "_blank");
    },
  },
};
