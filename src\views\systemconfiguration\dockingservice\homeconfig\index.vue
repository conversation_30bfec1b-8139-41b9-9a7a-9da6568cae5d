<template>
  <div class="home-container">
    <div class="config-nav" :class="{ 'layout-config-nav': defaultActive === 1 }">
      <tag-view :list="tagList" :default-active="defaultActive" @tagChange="changeStatus" ref="tagView"> </tag-view>
      <div class="fr">
        <template v-for="btn in btnList">
          <Button
            v-if="btn.isShow"
            :disabled="btn.disabled"
            type="primary"
            class="ml-sm"
            :key="btn.name"
            @click="clickBtnItem(btn)"
          >
            <span class="icon-font f-14 mr-xs font-white" :class="[btn.icon]"></span>
            {{ btn.name }}
          </Button>
        </template>
      </div>
    </div>
    <component
      :is="componentName"
      :isEdit="isEdit"
      ref="componentRef"
      class="com-box"
      @changeBtnList="changeBtnList"
    ></component>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'homeconfig',
  components: {
    TagView: require('@/components/tag-view.vue').default,
    FunctionConfig: require('./components/function-config.vue').default,
    LayoutConfig: require('./components/layout-config.vue').default,
  },
  props: {},
  data() {
    return {
      componentName: 'FunctionConfig',
      defaultActive: 0,
      tagList: [
        {
          label: '功能配置',
          value: 'FunctionConfig',
          disabled: false,
        },
        {
          label: '布局配置',
          value: 'LayoutConfig',
          disabled: false,
        },
      ],
      btnList: [
        {
          key: 'reset',
          name: '重置',
          icon: 'icon-shoudongshuaxin',
          isShow: false,
          disabled: true,
        },
        {
          key: 'modify',
          name: '修改',
          icon: 'icon-bianji3',
          isShow: true,
          disabled: false,
        },
        {
          key: 'save',
          name: '保存',
          icon: 'icon-baocun',
          isShow: false,
          disabled: false,
        },
      ],
      isEdit: false, // 是否处于编辑状态
    };
  },
  computed: {
    // systemConfig.distinguishVersion 1：默认，2:江苏省厅,3:河北(石家庄),4:四川省厅,5:内江市局,6:四川高新区,7:济南市局,8:甘肃省厅,9:兰州,10:乌市
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
  watch: {
    defaultActive() {
      if (this.defaultActive === 1) {
        this.btnList[0].isShow = true;
      } else {
        this.btnList[0].isShow = false;
      }
    },
  },
  filter: {},
  created() {},
  methods: {
    changeStatus(index) {
      this.defaultActive = index;
      this.componentName = this.tagList[index].value;
    },
    async clickBtnItem(btn) {
      let disabledTagIndex = this.tagList.length === 1 ? 0 : this.defaultActive === 0 ? 1 : 0;
      switch (btn.key) {
        case 'reset':
          this.$refs.componentRef.resetFn();
          return;
        case 'modify':
          this.tagList[disabledTagIndex].disabled = true;
          this.btnList[0].disabled = false;
          this.btnList[1].isShow = false;
          this.btnList[2].isShow = true;
          this.isEdit = true;
          return;
        case 'save':
          this.$refs.componentRef.updateByParamKey();
          return;
      }
    },
    changeBtnList(key) {
      switch (key) {
        case 'reset':
          return;
        case 'save':
          let disabledTagIndex = this.tagList.length === 1 ? 0 : this.defaultActive === 0 ? 1 : 0;
          this.tagList[disabledTagIndex].disabled = false;
          this.btnList[0].disabled = true;
          this.btnList[1].isShow = true;
          this.btnList[2].isShow = false;
          this.isEdit = false;
          return;
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .home-container {
    background: url('~@/assets/img/home/<USER>/home-config.png') no-repeat;
  }
}
.home-container {
  position: relative;
  height: 100%;
  width: 100%;
  background: url('~@/assets/img/home/<USER>/home-config-light.png') no-repeat;
  background-size: cover;
  display: flex;
  flex-direction: column;
  .config-nav {
    height: 54px;
    width: 100%;
    border-bottom: 1px solid var(--devider-line);
    padding: 10px 20px;
    &.layout-config-nav {
      background: var(--bg-content);
    }
    /deep/ .ivu-btn-primary[disabled] .icon-font {
      color: var(--color-btn-primary-disabled);
    }
    /deep/ .ivu-btn-primary[disabled]:hover {
      background: var(--bg-btn-primary-disabled);
      border-color: var(--border-btn-primary-disabled);
    }
  }
  .com-box {
    flex: 1;
    overflow: hidden;
  }
}
</style>
