<template>
  <div class="list-item">
    <p class="list-item-title f-16 mb-sm mt-sm">
      {{ oneDetail.name }}
    </p>
    <section class="list-item-cotent" v-html="oneDetail.description"></section>
    <p class="time-and-more mt-sm mb-xs">
      <span>{{ oneDetail.createTime }}</span>
      <span class="font-active-color pointer" @click="$emit('viewDetail', oneDetail)">查看详情 >></span>
    </p>
  </div>
</template>
<script>
export default {
  props: {
    oneDetail: {},
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .list-item {
    .list-item-title {
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
.list-item {
  border-bottom: 1px solid var(--border-color);
  color: #838d9c;
  .list-item-title {
    color: #19d5f6;
  }
  .list-item-cotent {
    @{_deep} img {
      width: 70px !important;
      height: 70px !important;
    }
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }
  .time-and-more {
    display: flex;
    justify-content: space-between;
    padding: 0 10px 0 0;
  }
}
</style>
