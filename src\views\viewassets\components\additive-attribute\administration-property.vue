<template>
  <div class="administration-property">
    <Form class="form" ref="form" inline autocomplete="off" label-position="right">
      <FormItem :required="isRequired('azsj')" class="left-item" label="设备安装时间">
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.azsj"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'azsj')"
          placeholder="请选择设备安装时间"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.azsj || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.azsj">
          {{ errorData.azsj }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('address')" class="left-item" label="设备安装地址">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.address"
          placeholder="请输入设备安装地址"
          :maxlength="256"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.address || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.address">
          {{ errorData.address }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbyxnx')" class="left-item" label="设备有效年限">
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.sbyxnx"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'sbyxnx')"
          placeholder="请选择设备有效年限"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.sbyxnx || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbyxnx">
          {{ errorData.sbyxnx }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('belongProject')" class="left-item" label="所属项目">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.belongProject"
          placeholder="请输入所属项目"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.belongProject || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.belongProject">
          {{ errorData.belongProject }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('civilCode')" class="left-item" label="行政区域">
        <api-area-tree
          class="area-tree"
          v-if="!isView"
          :select-tree="selectAreaTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区域"
        ></api-area-tree>
        <span v-else class="base-text-color">{{ formCustom.civilName || '未知' }}</span>
      </FormItem>
      <FormItem :required="isRequired('jsdw')" class="left-item" label="建设单位">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.jsdw"
          placeholder="请输入建设单位"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.jsdw || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.jsdw">
          {{ errorData.jsdw }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('jsdwlxr')" class="left-item" label="建设单位联系人">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.jsdwlxr"
          placeholder="请输入建设单位联系人"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.jsdwlxr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.jsdwlxr">
          {{ errorData.jsdwlxr }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('jsdwlxfs')" class="left-item" label="建设单位联系方式">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.jsdwlxfs"
          placeholder="请输入建设单位联系方式"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.jsdwlxfs || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.jsdwlxfs">
          {{ errorData.jsdwlxfs }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('orgCode')" class="left-item" label="所属组织机构">
        <api-organization-tree
          v-if="!isView"
          class="org-tree"
          placeholder="请选择所属组织机构"
          :select-tree="selectOrgTree"
          @selectedTree="selectedOrgTree"
        ></api-organization-tree>
        <span v-else class="base-text-color">{{ formCustom.orgName || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.orgCode">
          {{ errorData.orgCode }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('gldwlxr')" class="left-item" label="管理单位联系人">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.gldwlxr"
          placeholder="请输入管理单位联系人"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.gldwlxr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.gldwlxr">
          {{ errorData.gldwlxr }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('gldwlxfs')" class="left-item" label="管理单位联系方式">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.gldwlxfs"
          placeholder="请输入管理单位联系方式"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.gldwlxfs || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.gldwlxfs">
          {{ errorData.gldwlxfs }}
        </div>
      </FormItem>
      <FormItem
        :required="isRequired('cjdw') || defaultForm.sbdwlx === '1' || defaultForm.sbdwlx === '4'"
        class="left-item"
        label="承建单位"
      >
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.cjdw"
          placeholder="请输入承建单位联系人"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.cjdw || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.cjdw">
          {{ errorData.cjdw }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('cjdwlxr')" class="left-item" label="承建单位联系人">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.cjdwlxr"
          placeholder="请输入承建单位联系人"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.cjdwlxr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.cjdwlxr">
          {{ errorData.cjdwlxr }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('cjdwlxfs')" class="left-item" label="承建单位联系人电话">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.cjdwlxfs"
          placeholder="请输入承建单位联系人电话"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.cjdwlxfs || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.cjdwlxfs">
          {{ errorData.cjdwlxfs }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('whdw')" class="left-item" label="维护单位">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.whdw"
          placeholder="请输入维护单位"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.whdw || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.whdw">
          {{ errorData.whdw }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('whdwlxr')" class="left-item" label="维护单位联系人">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.whdwlxr"
          placeholder="请输入维护单位联系人"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.whdwlxr || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.whdwlxr">
          {{ errorData.whdwlxr }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('whdwlxfs')" class="left-item" label="维护单位联系人电话">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.whdwlxfs"
          placeholder="请输入维护单位联系人电话"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.whdwlxfs || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.whdwlxfs">
          {{ errorData.whdwlxfs }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('glry')" class="left-item" label="管理员姓名">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.glry"
          placeholder="请输入管理员姓名"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.glry || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.glry">
          {{ errorData.glry }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('glrylxfs')" class="left-item" label="管理员联系电话">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.glrylxfs"
          placeholder="请输入管理员联系电话"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.glrylxfs || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.glrylxfs">
          {{ errorData.glrylxfs }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('platform')" class="left-item" label="所属平台">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.platform"
          placeholder="请输入所属平台"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.platform || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.platform">
          {{ errorData.platform }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('platformCode')" class="left-item" label="所属平台编码">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.platformCode"
          placeholder="请输入所属平台编码"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.platformCode || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.platformCode">
          {{ errorData.platformCode }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('usageStatus')" class="left-item" label="设备使用状态">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.usageStatus"
          clearable
          placeholder="请选择设备使用状态"
        >
          <Option
            v-for="(item, index) in usageStatusList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.usageStatus || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.usageStatus">
          {{ errorData.usageStatus }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbjsServer')" class="left-item" label="最近一次设备校时服务">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.sbjsServer"
          placeholder="请输入最近一次设备校时服务"
          :maxlength="30"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.sbjsServer || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbjsServer">
          {{ errorData.sbjsServer }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sbjsTime')" class="left-item" label="最近一次设备校时时间">
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.sbjsTime"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'sbjsTime')"
          placeholder="请选择最近一次设备校时时间"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.sbjsTime || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sbjsTime">
          {{ errorData.sbjsTime }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('lxbcts')" class="left-item" label="录像保存天数">
        <InputNumber
          v-if="!isView"
          type="number"
          class="width-lg"
          v-model="formCustom.lxbcts"
          placeholder="请输入录像保存天数"
          :min="0"
          :max="999999999"
          :precision="0"
        ></InputNumber>
        <span v-else class="base-text-color">{{ formCustom.lxbcts || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.lxbcts">
          {{ errorData.lxbcts }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('downloadSpeed')" class="left-item" label="下载倍速范围">
        <Select
          v-if="!isView"
          class="width-lg"
          v-model="formCustom.downloadSpeed"
          clearable
          placeholder="请选择下载倍速范围"
        >
          <Option
            v-for="(item, index) in downloadSpeedList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.downloadSpeed || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.downloadSpeed">
          {{ errorData.downloadSpeed }}
        </div>
      </FormItem>
      <FormItem
        :required="isRequired('qytime') || defaultForm.sbdwlx === '1' || defaultForm.sbdwlx === '4'"
        class="left-item"
        label="启用时间"
      >
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.qytime"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'qytime')"
          placeholder="请选择设备启用时间"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.qytime || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.qytime">
          {{ errorData.qytime }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('yytime')" class="left-item" label="停用时间">
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.yytime"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'yytime')"
          placeholder="请选择设备启用时间"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.yytime || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.yytime">
          {{ errorData.yytime }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('bftime')" class="left-item" label="报废时间">
        <DatePicker
          v-if="!isView"
          class="width-lg"
          type="datetime"
          v-model="formCustom.bftime"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formCustom, 'bftime')"
          placeholder="请选择设备启用时间"
        ></DatePicker>
        <span v-else class="base-text-color">{{ formCustom.bftime || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.bftime">
          {{ errorData.bftime }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('ssbm')" class="left-item" label="所属部门">
        <Select
          v-if="!isView"
          v-model="formCustom.ssbm"
          class="width-lg"
          filterable
          clearable
          placeholder="请选择所属部门"
        >
          <Option
            v-for="(item, index) in departmentList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.ssbmText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.ssbm">
          {{ errorData.ssbm }}
        </div>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    choosedOrg: {
      type: Object,
    },
    defaultForm: {
      type: Object,
    },
    modalAction: {
      type: String,
    },
    errorData: {
      type: Object,
    },
    allDicData: {
      type: Object,
    },
    // 校验必填字段
    isRequired: {
      type: Function,
    },
  },
  data() {
    return {
      departmentList: [],
      downloadSpeedList: [],
      usageStatusList: [],
      selectOrgTree: {
        orgCode: '',
      },
      selectAreaTree: {
        regionCode: '',
      },
      formCustom: {
        azsj: '',
        ssbm: '',
        ssbmText: '',
        address: '',
        sbyxnx: '',
        jsdw: '',
        jsdwlxfs: '',
        belongProject: '',
        civilCode: '',
        civilName: '',
        orgCode: '',
        orgName: '',
        gldwlxfs: '',
        gldwlxr: '',
        jsdwlxr: '',
        cjdw: '',
        cjdwlxr: '',
        cjdwlxfs: '',
        whdw: '',
        whdwlxr: '',
        whdwlxfs: '',
        glry: '',
        glrylxfs: '',
        platform: '',
        platformCode: '',
        usageStatus: '',
        sbjsServer: '',
        sbjsTime: '',
        lxbcts: null,
        downloadSpeed: '',
        qytime: '',
        yytime: '',
        bftime: '',
      },
      formError: {},
    };
  },
  created() {},
  methods: {
    selectedArea(area) {
      this.formCustom.civilCode = area.regionCode;
    },
    validate() {
      return this.$refs.form.validate((valid) => {
        // 表单验证是否通过都需要更新表单数据
        this.$emit('putData', this.formCustom);
        return valid;
      });
    },
    selectedOrgTree(val) {
      this.formCustom.orgCode = val.orgCode;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.formCustom = {
        azsj: '',
        ssbm: '',
        ssbmText: '',
        address: '',
        sbyxnx: '',
        jsdw: '',
        jsdwlxfs: '',
        belongProject: '',
        civilCode: '',
        civilName: '',
        orgCode: '',
        orgName: '',
        gldwlxfs: '',
        gldwlxr: '',
        jsdwlxr: '',
        cjdw: '',
        cjdwlxr: '',
        cjdwlxfs: '',
        whdw: '',
        whdwlxr: '',
        whdwlxfs: '',
        glry: '',
        glrylxfs: '',
        platform: '',
        platformCode: '',
        usageStatus: '',
        sbjsServer: '',
        sbjsTime: '',
        lxbcts: null,
        downloadSpeed: '',
        qytime: '',
        yytime: '',
        bftime: '',
      };
    },
  },
  watch: {
    choosedOrg() {
      this.selectOrgTree.orgCode = this.choosedOrg.orgCode;
      this.formCustom.orgCode = this.choosedOrg.orgCode;
    },
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          this.selectOrgTree.orgCode = '';
          if (length > 0) {
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                switch (key) {
                  default:
                    this.formCustom[key] = val[key];
                    break;
                }
              }
            });
            this.selectOrgTree.orgCode = val.orgCode;
            this.selectAreaTree.regionCode = val.civilCode;
          }
        });
      },
      immediate: true,
    },
    allDicData: {
      handler(val) {
        for (let i in val) {
          if (this.hasOwnProperty(i)) {
            this[i] = val[i];
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    isView() {
      return this.modalAction === 'view';
    },
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 190px;
@inputWidth: 230px;
.administration-property {
  height: 586px;
  overflow-y: auto;
  margin-top: 20px;
  @{_deep}.org-tree {
    .ivu-select {
      width: @inputWidth;
    }
    .ivu-dropdown {
      width: @inputWidth;
    }
  }
  @{_deep}.ivu-form-item {
    width: 100%;
    margin-right: 0;
  }
  @{_deep}.ivu-form-item-label {
    text-align: right;
  }
  .left-item {
    width: 50%;
    @{_deep} .ivu-form-item-error-tip {
      margin-left: @leftMargin;
    }
    @{_deep}.ivu-form-item-label {
      width: @leftMargin;
    }
  }
  .error-tip {
    position: absolute;
    top: 100%;
    left: 0;
    line-height: 1;
    padding-top: 5px;
    color: #ed4014;
    margin-left: @leftMargin;
    width: @inputWidth;
  }
  .area-tree {
    @{_deep}.ivu-select {
      width: @inputWidth;
    }
    @{_deep}.ivu-select-dropdown {
      width: @inputWidth;
    }
  }
}
</style>
