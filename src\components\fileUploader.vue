<!--
 * @Date: 2025-07-24 09:33:12
 * @LastEditors: z<PERSON><PERSON><PERSON><PERSON> zhu<PERSON><EMAIL>
 * @LastEditTime: 2025-07-28 09:27:35
 * @FilePath: \icbd-view\src\components\fileUploader.vue
-->
<template>
    <div class="fileUploader">
        <div class="uploadContainer">
            <div class="uploadSelectBtn">
                <Button class="w_80" size="small" type="success" @click="openFileDlg()">文件导入</Button>
            </div>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
    name: "fileUploader",
    props: {
        fileStructureFormat: {
            type: String,
            default: "Data Files (*.avi;*.wmv;*.mbf;*.mp4;*.dav;*.bmp;*.ts;)|*.avi;*.wmv;*.mbf;*.mp4;*.dav;*.bmp;*.ts;*.jpeg;||"
        },
    },
    data() {
        return {
            fileNameGet: ""
        };
    },
    computed:{
      ...mapGetters({
          userInfo: "userInfo"
      }),
    },
    methods: {
        openFileDlg() {
            var opnenFileDlg = {
                defext: "",
                filename: "",
                flags: 0x1206,
                filter: this.fileStructureFormat,
                title: "选择上传文件"
            };
            this.$H5PlayerAPI.openSaveFileDlg(opnenFileDlg).then(res => {
                console.log("---------openSaveFileDlg----------", res);
                console.log(
                    "--------------------------------h5上传 start-----------------------------------"
                );
                try {
                    if (res.error < 0) {
                        return;
                    }
                    //替换路径中的双斜杠
                    var selUpFiles = JSON.parse(res.files);
                    for (let i = 0; i < selUpFiles.length; i++) {
                        const file = selUpFiles[i];
                        var selUpFilePath = file.filename;
                        console.log(
                            "选择文件：" + JSON.stringify(selUpFilePath)
                        );
                        if (selUpFilePath.trim() != "") {
                            selUpFilePath = selUpFilePath.replace(/\\/g, "/");
                        } else {
                            return;
                        }
                        var fileNameArray = selUpFilePath.split("/");
                        var fileNameGet =
                            fileNameArray[fileNameArray.length - 1]; ////名称
                        this.fileNameGet = fileNameGet;
                        var fileNameSuffix = fileNameGet.substr(fileNameGet.lastIndexOf(".")+1)
                        console.log("替换反斜杠后文件路径：" + selUpFilePath); //文件绝对路径
                        var uploadDefaultInfo = {
                            src: {
                                filepath: Toolkits.replacehttpparam(selUpFilePath)
                            },
                            dest: {
                                type: "minio",
                                ip: window.minioInfo.ip,
                                port: window.minioInfo.port,
                                accessKey: window.minioInfo.accessKey,
                                secretKey: window.minioInfo.secretKey,
                                bucketName: this.userInfo.username,
                                pathName: `/${this.userInfo.username}`,
                            }
                        };
                        console.log("单文件上传参数:", uploadDefaultInfo);
                        this.startUpload(
                            fileNameSuffix,
                            uploadDefaultInfo,
                            fileNameGet
                        );
                    }
                } catch (e) {
                    return;
                }
            });
        },
        startUpload(fileNameSuffix, uploadDefaultInfo, fileNameGet) {
            //默认是上传视频
            console.log(
                "---------startUpload--------",
                fileNameSuffix,
                uploadDefaultInfo
            );
            var self = this;
            var filetypeValue
            if (
                fileNameSuffix == "gif" ||
                fileNameSuffix == "png" ||
                fileNameSuffix == "jpg" ||
                fileNameSuffix == "jpeg"
            ) {
                filetypeValue = "imgs";
            } else if (
                fileNameSuffix == "avi" ||
                fileNameSuffix == "wmv" ||
                fileNameSuffix == "mbf" ||
                fileNameSuffix == "mp4" ||
                fileNameSuffix == "dav" ||
                fileNameSuffix == "bmp" ||
                fileNameSuffix == "ts"
            ) {
                filetypeValue = "videos";
            } else {
                filetypeValue = "others";
            }
            uploadDefaultInfo.dest.pathName =
                uploadDefaultInfo.dest.pathName +
                "/" +
                filetypeValue +
                "/" +
                fileNameGet
            console.log(uploadDefaultInfo);
            Toolkits.jsonpAjaxChar(
                window.startuploadUrl.split("?")[0],
                JSON.stringify(uploadDefaultInfo),
                "GB2312"
            ).then(data => {
                console.log("----------startupload----------", data);
                var resultParam = JSON.parse(data.startupload.result).info;
                // 公开上传资源
                resultParam.fileName = fileNameGet;
                var emitParams = {
                    info: {
                        dest: [resultParam],
                        src: [uploadDefaultInfo.src]
                    }
                };
                console.log("------------resultParam------------", emitParams);

                self.handleSuceess({
                    url: resultParam.dest,
                    ip: resultParam.ip,
                    port: resultParam.port,
                    size: resultParam.totalsize,
                    fileName: this.fileNameGet
                });
            });
        },
        handleSuceess: function(fileParam) {
            if (fileParam != "") {
                this.$emit("addResource", fileParam);
            } else {
                this.$Message.warning("上传失败");
            }
        },
    }
};
</script>
<style>
.UIOCXFILEUP {
    position: absolute;
    left: -999px;
    width: 1px;
}
.uploadSelectBtn {
    display: inline-block;
}
.nopadding {
    padding: 0 !important;
}
.uploadContainer {
    height: 90px;
}
.w_80 {
  width: 80px;
}
</style>
