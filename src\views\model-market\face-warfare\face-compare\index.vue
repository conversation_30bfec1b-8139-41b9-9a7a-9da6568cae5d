<!--
    * @FileDescription: 人脸对比
    * @Author: H
    * @Date: 2023/09/06
    * @LastEditors: 
    * @LastEditTime: 
 -->

<template>
    <div class="face-compare container">
        <div class="face-compare-title">
            <p>人脸比对</p>
        </div>
        <div class="title-icon"></div>
        <div class="content">
            <div class="left-side"></div>
            <div class="left-face upload-img">
                <uploadImg key="1" :algorithmType="1" 
                    v-model="queryParam.leftImg" 
                    @imgUrlChange="imgUrlChange"
                    @deleteImgUrl="deleteImgUrl"></uploadImg>
            </div>
            <div class="calculate">
                <div class="calculate-loading" v-if="!detailsorLoading">
                    <div class="vsLoading">
                        <span>vs</span>
                    </div>
                    <div class="operate" 
                        v-if="JSON.stringify(queryParam.leftImg) !== '{}' && JSON.stringify(queryParam.rightImg) !== '{}'">
                        <p class="operate_title">算法选择:</p>
                        <CheckboxGroup class="checkbox" v-model="queryParam.algorithmSelect" @on-change="handleChangeAlgor">
                            <Checkbox 
                                :label="item.dataKey" 
                                v-for="(item, index) in algorithmTypeList" 
                                :key="index"
                            >
                                <span :class="[item.dataKey == 'GLST' ? 'gerling' : 'hk'] ">{{ item.dataValue }}</span>
                            </Checkbox>
                        </CheckboxGroup>
                        <Button type="primary" class="compare-btn" @click="handleCompare">比对</Button>
                    </div>
                </div>
                <div class="details" v-else>
                    <div class="scoreMsg" :class="[item.type == 'GLST'? 'glMsg' : 'hkMsg']" v-for="(item, index) in similatityList" :key="index">
                        <p>{{ item.name }}</p>
                        <div class="first-circle">
                            <div class="second-circle">
                                <div class="end-circle">
                                    <p>{{ item.score }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="glMsg" v-if="queryParam.algorithmSelect.includes(this.algorithmTypeSelect[0])">
                        <p>{{ algorithmTypeList[0].dataValue }}</p>
                        <div class="first-circle">
                            <div class="second-circle">
                                <div class="end-circle">
                                    <p>{{ glSimilatity }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="hkMsg" v-if="queryParam.algorithmSelect.includes(this.algorithmTypeSelect[1])">
                        <p>{{ algorithmTypeList[1].dataValue }}</p>
                        <div class="first-circle">
                            <div class="second-circle">
                                <div class="end-circle">
                                    <p>{{ hkSimilatity }}</p>
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
            <div class="right-face upload-img">
                <uploadImg key="2" :algorithmType="1" 
                v-model="queryParam.rightImg"
                @imgUrlChange="imgUrlChange" 
                @deleteImgUrl="deleteImgUrl"></uploadImg>
            </div>
            <div class="right-side"></div>
        </div>
    </div>
</template>

<script>
import uploadImg from '@/components/ui-upload-new-img/face-comparison';
import { faceCompare } from '@/api/modelMarket';
import { mapActions } from 'vuex';
export default {
    name: 'face-compare',
    components:{
        uploadImg  
    },
    data () {
        return {
            queryParam: {
                algorithmSelect:['HK'],
                leftImg: {},
                rightImg: {},
            },
            hkSimilatity: 0,
            glSimilatity: 0, 
            similatityList: [],
            detailsorLoading: false
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    async created() {
        await this.getDictData()
        this.queryParam.algorithmSelect = [this.algorithmTypeSelect[0]];
    },
    mounted(){
            
    },
    methods: {
        ...mapActions({
            getDictData: 'dictionary/getDictAllData'
        }),
        // 比对
        handleCompare() {
            if(JSON.stringify(this.queryParam.leftImg) == '{}' || JSON.stringify(this.queryParam.rightImg) == '{}'){
                this.$Message.warning('请先上传图片')
                return;
            }
            if(this.queryParam.algorithmSelect == 0){
                this.$Message.warning('请选择算法')
                return;
            }
            let params = {
                algorithmVendorTypes: this.queryParam.algorithmSelect,
                imgOne: {
                    imgBase64: this.queryParam.leftImg.imageBase,
                    feature: this.queryParam.leftImg.feature
                },
                imgTwo: {
                    imgBase64: this.queryParam.rightImg.imageBase,
                    feature: this.queryParam.rightImg.feature
                }
            }
            faceCompare(params)
            .then(res => {
                this.similatityList = [];
                this.detailsorLoading = true;
                let nameList = new Map(this.algorithmTypeList.map(item => [item.dataKey, item]));
                res.data.forEach(item => {
                    let numBox = nameList.get(item.algorithmVendorType);
                    this.similatityList.push({
                        "type": item.algorithmVendorType,
                        'score': item.score ? item.score + '%' : '--',
                        'name': numBox.dataValue,
                    })
                    if(item.algorithmVendorType == 'HK'){
                        this.hkSimilatity = item.score ? item.score + '%' : '--';
                    }
                    if(item.algorithmVendorType == 'GLST') {
                        this.glSimilatity = item.score ? item.score + '%' : '--';
                    }   
                })
                this.$forceUpdate()
            })
        },
        handleChangeAlgor(){

        },
        deleteImgUrl() {
            this.hkSimilatity = '--',
            this.glSimilatity = '--', 
            this.detailsorLoading = false;
        },
        imgUrlChange() {
            this.hkSimilatity = '--',
            this.glSimilatity = '--', 
            this.detailsorLoading = false;
        }
    }
}
</script>

<style lang='less' scoped>
.face-compare{
    padding: 0;
    position: relative;
    width: 100%;
    height: inherit;
    background: url('~@/assets/img/model/backImg.png') no-repeat, #fff;
    background-size: 100% 100%;
    &-title{
        font-size: 16px;
        font-weight: bold;
        color: rgba(0,0,0,0.9);
        height: 40px;
        position: relative;
        line-height: 40px;
        padding-left: 20px;
        border-bottom: 1px solid #D3D7DE;
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        z-index: 1;
        background: #fff;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 20px;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
            background: #2c86f8;
        }
        span{
            color: #2C86F8; 
        }
        /deep/.ivu-icon-ios-close{
            font-size: 30px;
            cursor: pointer;
        }
    }
    .title-icon{
        width: 539px;
        height: 65px;
        background: url('~@/assets/img/model/title.png') no-repeat;
        margin: 56px auto 0;
    }
    .content{
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 46px;
        .left-side{ 
            width: 258px;
            height: 438px;
            background: url('~@/assets/img/model/left_slices.png') no-repeat;
        }
        .upload-img{
            width: 320px;
            height: 400px;
            background: url('~@/assets/img/model/face_slices.png') no-repeat;
            display: flex;
            justify-content: center;
            align-items: end;
            font-size: 20px;
            font-weight: 700;
            color: #2C86F8;
            /deep/ .upload-btn{
                margin-bottom: 40px;
            }
        }
        .calculate{
            margin: 0 11px;
            .calculate-loading{
                margin-top: 40px;
                .vsLoading{
                    width: 248px;
                    height: 248px;
                    background: url('~@/assets/img/model/loading.gif') no-repeat;
                    background-size: 100% 100%;
                    font-size: 34px;
                    font-weight: 700;
                    color: #2C86F8;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .operate{
                    text-align: center;
                    .operate_title{
                        font-size: 14px;
                        font-weight: 400;
                        color: rgba(0,0,0,0.4535);
                        text-align: left;
                    }
                    .checkbox{
                        margin: 10px 0 27px;
                        display: flex;
                        justify-content: space-between;
                    }
                    .compare-btn{
                        width: 180px;
                    }
                }
            }
            .details{
                margin: 0 47px;
                .hkMsg{
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0,0,0,0.75);
                    text-align: center;  
                    .first-circle{
                        width: 156px;
                        height: 155px;
                        border-radius: 50%;
                        background: rgba(114, 196, 255, .1);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-top: 4px;
                        .second-circle{
                            width: 137px;
                            height: 137px;
                            border-radius: 50%;
                            background: rgba(114, 196, 255, .2);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            .end-circle{
                                width: 124px;
                                height: 124px;
                                border-radius: 50%;
                                background: linear-gradient(209deg, #72C4FF 0%, #2C86F8 100%);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                font-size: 26px;
                                font-weight: 700;
                                color: #FFFFFF;
                            }
                        }
                    }
                } 
                .scoreMsg{
                    font-size: 14px;
                    font-weight: 400;
                    color: rgba(0,0,0,0.75); 
                    text-align: center;
                    margin-bottom: 46px;
                    .first-circle{
                        width: 155px;
                        height: 155px;
                        border-radius: 50%;
                        // background: rgba(255, 187, 30, .1);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-top: 4px;
                        .second-circle{
                            width: 137px;
                            height: 137px;
                            border-radius: 50%;
                            // background: rgba(255, 187, 30, .2);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            .end-circle{
                                width: 124px;
                                height: 124px;
                                border-radius: 50%;
                                // background: linear-gradient(209deg, #FFBB1E 0%, #FF8F1E 100%);
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                font-size: 26px;
                                font-weight: 700;
                                color: #FFFFFF;
                            }
                        }
                    }
                }
                .glMsg{
                    .first-circle{
                        background: rgba(255, 187, 30, .1);
                        .second-circle{
                            background: rgba(255, 187, 30, .2);
                            .end-circle{
                                background: linear-gradient(209deg, #FFBB1E 0%, #FF8F1E 100%);
                            }
                        }
                    }
                }
                .hkMsg{
                    .first-circle{
                        background: rgba(114, 196, 255, .1);
                        .second-circle{
                            background: rgba(114, 196, 255, .2);
                            .end-circle{
                                background: linear-gradient(209deg, #72C4FF 0%, #2C86F8 100%);
                            }
                        }
                    }
                }
            }
        }
        .right-side{
            width: 258px;
            height: 438px;
            background: url('~@/assets/img/model/right_slices.png') no-repeat;
        }

    }
    .gerling {
		color: #f29f4c;
	}
}
</style>
