<template>
  <div class="monitor-group">
    <div class="video-left mr-10">
      <plan-list
        @addPlan="addPlan"
        @refreshList="getPlanList"
        @addOSD="addOSD"
        @loopPlan="loopPlan"
        @pausePlan="pausePlan"
        @containuePlan="containuePlan"
        @stopPlan="stopPlan"
        @cacluateStyle="cacluateStyle"
        @changeAutoStart="changeAutoStart"
        @planNext="planNext"
        :planList="planList"
        :lockedCameras="lockedCameras"
        ref="planList"
      >
      </plan-list>
    </div>
    <div class="right-content">
      <div class="top">
        <h5-player ref="monitorH5Player" @sucess="sucess"></h5-player>
      </div>

      <div class="toolbar" :class="{ 'toolbar-hide': !isShowToolBar }">
        <div
          class="toggle"
          :class="{ hide: !isShowToolBar }"
          @click="isShowToolBar = !isShowToolBar"
        >
          <Tooltip
            :always="!isShowToolBar"
            content="工具栏"
            placement="bottom"
            max-width="18"
          >
            <i class="iconfont icon-doubleright"></i>
          </Tooltip>
        </div>
        <div class="toolbar-title">播放控件</div>
        <div class="toolbar-buttons">
          <!-- <i class="iconfont icon-touping" title="发送到扩展屏" @click="extendScreenHandler"></i> -->
          <i
            class="iconfont icon-quanping_xian"
            v-show="!isFullScreen"
            title="全屏"
            @click="fullScreen"
          ></i>
          <i
            class="iconfont icon-quanping_xian"
            v-show="isFullScreen"
            title="退出全屏"
            @click="screenMethod('exitFullScreen')"
          ></i>
          <!-- <i class="iconfont icon-guanbi close" title="全部关闭" @click="closeAll"></i> -->
        </div>
      </div>
    </div>

    <add-plan
      @refreshList="refreshList"
      @changeAutoStart="changeAutoStart"
      ref="addPlan"
    ></add-plan>
  </div>
</template>

<script>
import planList from "./components/planList.vue";
import addPlan from "./components/addPlan.vue";
import { getPlan, deletePlan, enableAutoStart } from "@/api/inspectionTour.js";
import { queryVideoDeviceGroupList } from "@/api/player";
import Manage from "./js/manage.js";
import inspectBase from "./js/inspectBase.js";
import ExpandScreen from "@/util/modules/extend-screen.js";
import { getTimeDiff } from "@/util/modules/common.js";
export default {
  name: "monitor-group",
  components: {
    planList,
    addPlan,
  },
  data() {
    return {
      planCount: 0,
      planList: [],
      inspectValue: {},
      mainInspect: {},
      inspectStatus: 0,
      showInspectList: false,
      dialogTitle: "新增预案",
      isLoading: false,
      h5Options: {
        params: {
          el: "#monotorH5Player",
          layout: "2*2",
        },
        sceneFrom: "monitorVideo",
        wrapStyle: "height:100%;width:100%",
      },
      lockedCameras: {},
      beginPlay: false,
      isFullScreen: false,
      isShowToolBar: false,
      timer: null,
    };
  },
  created() {
    Manage.add("mainIndex", this);
    this.getPlanList();
  },
  mounted() {
    this.$nextTick(() => {
      this.H5Cmpt = this.$refs.monitorH5Player;
    });
  },
  methods: {
    //新增预案
    addPlan(item, type) {
      this.dialogTitle = item.id ? "编辑预案" : "新增预案";
      this.$refs.addPlan.open(item, type);
    },
    planNext() {
      this.$refs.addPlan.next();
    },
    refreshList() {
      const type = this.$refs.planList.planType === "own" ? 0 : 1;
      this.getPlanList(this.$refs.planList.searchPlan, type);
    },
    //获取预案列表
    getPlanList(keyWord = "", type = 0) {
      this.isLoading = true;
      getPlan({ keyWord, sharedState: type })
        .then((res) => {
          IX.isArray(res.data) &&
            res.data.map((item) => {
              item.isOpen = false;
              item.isAutoOpen = false;
              item.autoStart =
                item.autoStart && item.autoStart === 1 ? false : true;
              if (item.groupList) {
                item.groupList.map((groups, index1) => {
                  const groupIdx = index1 + 1;
                  if (groups.cameraData) {
                    groups.cameraData.map((cameras, index2) => {
                      const cameraIdx = index2 + 1;
                      cameras.markNumber =
                        groupIdx.toString() + " - " + cameraIdx;
                      // cameras.ignore = this.isDeviceIgnore(cameras); //资源屏蔽
                    });
                  }
                });
              }
            });
          this.planList = IX.isArray(res.data) ? res.data : [];
          console.log("预案列表---------------", this.planList);
          this.watchAutoStart();
        })
        .finally((res) => {
          this.isLoading = false;
        });
    },
    //删除预案
    deletePlan(id, name) {
      deletePlan(id).then((res) => {
        this.queryLog({
          muen: "视频中心",
          name: "视频预案",
          type: "3",
          remark: `删除预案【${name}】`,
        });
        this.$Message.success(`删除成功`);
        this.getPlanList();
      });
    },
    //编辑预案
    editPlan(item, flag) {
      this.dialogTitle = "编辑预案";
      //编辑数据注入
      this.$refs.addPlan.open(item, 1);
      this.$nextTick(() => {
        this.$refs.addPlan.setData(item, flag);
      });
    },
    //计算样式
    cacluateStyle(camera) {
      let lockedCameras = Object.values(this.lockedCameras);
      let lockedCamera = lockedCameras.find((i) => i.cameraId === camera.id);
      if (lockedCamera) {
        this.$set(camera, "isLocking", lockedCamera.isLock);
      } else {
        this.$set(camera, "isLocking", false);
      }
      if (camera.isPlaying || camera.isLocking) {
        return "playing-style";
      }
      return camera.cameraType == 0 ? "i-b_sxj" : "i-b_qj";
    },
    //轮巡逻辑处理
    async loopPlan(value) {
      if (value.groupList.length === 0) {
        this.$Message.warning("当前预案下无视频分组，请先添加分组再开启预案");
        this.$refs.planList.stopPlan(value);
        return;
      }
      //无实时查看权限
      if (!this.$_has(["video-realTime"])) {
        this.$Message.warning("无实时查看权限");
        return;
      }
      console.log("轮巡数据----", value);
      this.$Message.info("预案启动");
      this.$refs.planList.startLoopPlan(); // 改变轮巡状态
      this.addOSD(IX.clone(value), true);
      this.inspectStatus = 1;
      value.isOpen = true;
      this.inspectValue = value;
      this.showInspectList = true;
      value.groupList.forEach(async (item) => {
        if (!item.cameraData) {
          const res = await queryVideoDeviceGroupList({ groupId: item.id });
          const resourceData = res.data.map((v) => {
            v.id = v.deviceId;
            return v;
          });
          if (!resourceData.data) {
            item.cameraData = [];
            return;
          }
          this.$set(item, "cameraData", resourceData.data);
          !!item.screenIndex &&
            this._initInspect(
              item.screenIndex,
              item.cameraData,
              item.id,
              item.stopTime,
              item.name
            );
        } else {
          this._initInspect(
            item.screenIndex,
            item.cameraData,
            item.id,
            item.stopTime,
            item.name
          );
        }
        item.isOpen = true; //bug[93219] index === 0 ? true : false;
      });
    },
    //轮巡暂停
    pausePlan(id) {
      this.commonHandler(this.mainInspect, (item) => {
        item.pause();
      });
      this.$Message.info("预案暂停");
      this.inspectStatus = 2;
    },
    //轮巡停止
    stopPlan(id, type) {
      this.commonHandler(this.mainInspect, (item) => {
        if (item) {
          setTimeout((_) => {
            item.stop();
          }, 100);
        }
      });
      // VueExpress.$store.dispatch("TRIGGER_COMPUTED", null);
      if (!type) {
        this.$Message.info("预案停止");
      }
      this.inspectStatus = 0;
      this.inspectValue = {};
      this.H5Cmpt.closeAll();
    },
    //轮巡继续
    containuePlan(id) {
      this.commonHandler(this.mainInspect, (item) => {
        item.continue();
      });
      this.$Message.info("预案继续");
      this.inspectStatus = 1;
    },
    commonHandler(val, fn) {
      Object.values(val).forEach((item) => {
        fn(item);
      });
    },
    //返回预案列表
    returnPlanList() {
      this.showInspectList = false;
      this.inspectStatus !== 0 && this.stopPlan();
      this.inspectValue = {};
    },
    //计算开关的显示隐藏
    cacluateShowSwitch(item) {
      var flag = true;
      item.tourTime.forEach((_i) => {
        if (!_i.startTime || !_i.endTime || !_i.week) {
          flag = false;
        }
      });
      return flag;
    },
    setLayout(data) {
      const num = data.val ? data.val.split("*") : [0, 0];
      if (data.type === "A") {
        this.$refs.monitorH5Player &&
          this.$refs.monitorH5Player.changeLayout(num.join("A"));
      } else {
        this.$refs.monitorH5Player &&
          this.$refs.monitorH5Player.changeLayout(data.val);
      }
    },
    //全屏
    fullScreen() {
      this.H5Cmpt.setFullScreen();
    },
    // 全屏逻辑
    screenMethod(type) {
      if (document.documentElement.querySelector("#side-resize")) {
        if (type === "fullScreen") {
          document.documentElement.querySelector("#side-resize").style.display =
            "none";
        } else {
          document.documentElement.querySelector("#side-resize").style.display =
            "unset";
        }
      }
      this.playerFoo(type);
    },
    setFullScreenStatus() {
      this.isFullScreen = this.$refs.monitorH5Player.isFullScreen();
    },
    //发送到扩展屏
    extendScreenHandler() {
      if (Object.keys(this.inspectValue).length === 0) {
        this.$Message.warning("无正在播放的视频");
      } else {
        this.inspectValue.groupList.map((val) => {
          val.cameras = IX.clone(val.cameraData);
        });
        ExpandScreen.exMonitorGroupScreen(
          { ...this.inspectValue, isAutoOpen: false },
          () => {
            this.returnPlanList();
            this.$refs.planList.extendScreen();
          }
        );
      }
    },
    //自启动开启与停止
    changeAutoStart(item, isText = true) {
      console.log(item, 5616);
      var autoStartValue = item.autoStart ? "2" : "1";
      var enableParams = {
        id: item.id,
        autoStart: autoStartValue,
      };
      enableAutoStart(enableParams).then(() => {
        if (isText) {
          this.$Message.success(`${item.autoStart ? "开启" : "关闭"}成功`);
        }
        this.watchAutoStart();
        //如果是开启状态，需要清除localStorage中key为用户名值为推送信息的item项
        // if (item.autoStart) {
        //   Toolkits.removeLocalValue(Toolkits.getLocalValue("userInfo").id);
        // }
      });
    },
    // h5vp 加载成功后的逻辑
    sucess(val, item) {
      this.inspectValue.groupList.map((data) => {
        const { screenIndex = "" } = data;
        const tar = [];
        data.cameraData.map((val) => {
          if (val.isPlaying) {
            tar.push(val);
          }
        });
        let screenIndexArr = screenIndex ? screenIndex.split(",") : [];
        screenIndexArr = screenIndexArr.filter((sa) => sa != "");
        screenIndexArr.sort((a, b) => a - b);
        //this.H5Cmpt.videoObj.deleteAllWaterMarks();
        screenIndexArr.map((sa, index) => {
          this.H5Cmpt.videoObj.delWaterMarkById(Number(sa), 1);
          this.H5Cmpt.videoObj.addWaterMark(Number(sa), [
            {
              id: 1,
              text: tar[index].markNumber,
              fontSize: "16px",
              fontWeight: "bold",
              color: "#2d87f9",
              left: "10px",
              top: "30px",
            },
          ]);
        });
      });
      if (val === "beginPlay") {
        this.beginPlay = true;
      } else {
        this.beginPlay = false;
      }
    },
    _initInspect(slotsData, datas, groupId, time, planName) {
      if (!datas || datas.length === 0 || slotsData === "") return;
      let data = datas.map(
        ({
          id: deviceId,
          gbId: deviceGbId,
          name: deviceName,
          childType,
          ptzType,
          latitude,
          longitude,
        }) => {
          return {
            deviceId,
            deviceGbId,
            deviceName,
            ptzType: childType ? childType : ptzType,
            latitude,
            longitude,
            devicetype: liveType,
          };
        }
      );
      let slots = slotsData
        .slice(0, slotsData.length - 1)
        .split(",")
        .map((item) => parseInt(item));
      console.log("轮巡分屏：", slots, ", 轮巡数据：", data);
      //轮巡器参数
      if (this.mainInspect[groupId]) this.mainInspect[groupId] = null;
      this.mainInspect[groupId] = new inspectBase.construct({
        batchModel: true,
        slots: slots,
        operators: data,
        inspectGroupId: groupId,
        isOneLoop: true,
        planName: planName,
        operate: {
          exe: (objs, slots, groupId, planName) => {
            this.changeLiStyle(objs, datas);
            this.playInspectExe(objs, slots, groupId, planName);
          }, //执行回调函数
          start: function () {}, //启动...
          stop: () => {
            datas.forEach((item) => {
              this.$set(item, "isPlaying", false);
            });
          }, //停止...
          pause: function () {}, //暂停...
          continue: function () {}, //停止后继续...
        },
        time: time,
      });
      this.mainInspect[groupId].start();
      this.mainInspect[groupId].slots = slots;
    },
    changeLiStyle(objs, cameras) {
      cameras.forEach((item) => {
        if (objs.some((v) => v.deviceId == item.id)) {
          this.$set(item, "isPlaying", true);
        } else {
          this.$set(item, "isPlaying", false);
        }
      });
    },
    //轮巡批次播放
    playInspectExe: function (datas, slots, inspectId, planName) {
      var ids = [],
        _s = slots;
      this.inspectIds = "";
      _s.forEach((slot, i) => {
        if (!datas[i]) {
          return;
        } //屏比设备多的情况
        ids.push(datas[i]);
        this.playFree(datas[i], _s[i], inspectId, planName, true, false);
      });
      this.inspectIds = ids.join(",");
      console.log("this.inspectIds = ", this.inspectIds);
      this.$emit("inspectAndIds", this.inspectIds);
    },
    //播放
    playFree: function (obj, index, inspectId, planName) {
      if (index !== void 0) {
        this.H5Cmpt.playStream({ ...obj, inspectId, planName }, "live", index);
      } else {
        this.H5Cmpt.playStream({ ...obj, inspectId, planName }, "live");
      }
    },
    opened() {},
    close() {
      this.$refs.addPlan.close();
    },
    //modal关闭钩子
    closeHandler() {
      this.$refs.addPlan.setDefaultStatus();
    },
    addOSD(item, flag) {
      const obj = {
        val: item.specification,
        screen: item.layout,
        type: item.layoutType,
      };
      this.setLayout(obj);
      const num = obj.val ? obj.val.split("*") : [0, 0];
      var fontColorStyle = flag ? "#f1b95b" : "#2d87f9";
      // for (let i = 0, l = num[0] * num[1]; i < l; i++) {
      // 	this.H5Cmpt.videoObj.delWaterMarkById(i);
      // }
      this.H5Cmpt.deleteAllWaterMarks();
      if (!item.groupList) {
        item.groupList = [];
      }
      item.groupList.forEach((_li) => {
        var indexArr = _li.screenIndex.split(",");
        indexArr.length > 0 &&
          indexArr.forEach((li) => {
            li &&
              this.H5Cmpt.videoObj.addWaterMark(parseInt(li), [
                {
                  id: 1,
                  text: _li.name,
                  fontSize: "14px",
                  fontWeight: "bold",
                  color: fontColorStyle,
                  left: "20px",
                  top: "20px",
                },
              ]);
          });
      });
    },
    // getDeviceIgnorePlanList() {
    //   this.planList &&
    //     this.planList.map((item) => {
    //       if (item.groupList) {
    //         item.groupList.map((groups, index1) => {
    //           if (groups.cameraData) {
    //             groups.cameraData.map((cameras, index2) => {
    //               cameras.ignore = this.isDeviceIgnore(cameras); //资源屏蔽
    //             });
    //           }
    //         });
    //       }
    //     });
    // },
    // 自启动
    watchAutoStart() {
      this.timer && clearInterval(this.timer);
      let duration = 5000;
      this.timer = setInterval(() => {
        const days = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
        const today = new Date().getDay();
        const dayOfWeek = days[today];
        let list = this.planList
          .filter((v) => v.autoStart && !v.starting)
          .map((v) => v.tourTime)
          .flat();
        let filterList = list.filter((v) => v.week.indexOf(dayOfWeek) != -1);
        filterList.forEach((v) => {
          const diffMs = getTimeDiff(v.startTime);
          if (Math.abs(diffMs) < duration) {
            // 启动
            const item = this.planList.find((i) => i.id == v.planId);
            console.log("自动启动预案：", item);
            this.$set(item, "starting", true);
            this.$refs.planList.loopPlan(item);
          }
        });
      }, duration);
    },
  },
  beforeRouteLeave(to, from, next) {
    this.timer && clearInterval(this.timer);
    //如果轮巡不是停止状态 切换路由时停止轮巡 清除轮巡定时器
    this.inspectStatus !== 0 &&
      this.commonHandler(this.mainInspect, (item) => {
        item.stop();
      });
    next();
  },
};
</script>
<style lang="less" scoped>
.monitor-group {
  width: 100%;
  display: flex;
  position: relative;
  .video-left {
    width: 316px;
    height: 100%;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    position: relative;
  }
  .right-content {
    flex: 1;
    background-color: #2c3033;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    position: relative;
    .top {
      flex: 1;
      padding: 7px;
    }
  }
  .toolbar {
    position: fixed;
    top: 206px;
    transform: translate(0, -50%);
    right: 0;
    width: 70px;
    height: 200px;
    background: #fff;
    box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;
    transition: all 0.5s;
    &-hide {
      right: -70px;
    }
    .toolbar-title {
      width: 100%;
      text-align: center;
      line-height: 39px;
      font-size: 14px;
      font-weight: 700;
      color: #3d3d3d;
      border-bottom: 1px solid #d3d7de;
    }
    .toolbar-buttons {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      width: 100%;
      .color-blue {
        color: #2c86f8 !important;
      }
      .ivu-dropdown {
        width: 100%;
        text-align: center;
      }
      .iconfont {
        font-size: 22px;
        color: #484847;
        cursor: pointer;
        &:hover {
          color: #2c86f8;
        }
      }
    }
    .toggle {
      position: absolute;
      top: 4px;
      left: -18px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      cursor: pointer;
      width: 18px;
      height: 36px;
      background: #2c86f8;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      &.hide {
        i {
          transform: rotate(180deg);
        }
      }
    }
  }
}
/deep/ .ivu-tooltip-inner {
  padding: 3px;
  font-size: 12px;
  border-radius: 2px;
}
/deep/ .ivu-tooltip-popper {
  left: 0 !important;
}
</style>
<style lang="less">
.ivu-dropdown-transfer {
  max-height: 300px !important;
  .current {
    background: rgba(44, 134, 248, 0.1029);
    color: #2c86f8;
  }
}
</style>
