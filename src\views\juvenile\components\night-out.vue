<template>
  <div style="height: 100%; width: 100%">
    <div class="darktime-out" v-if="list.length > 0">
      <div
        class="dartktiem-item"
        v-for="(item, index) in list"
        @click="handleDetailFn(item, index)"
      >
        <div class="left-content">
          <div class="item-image">
            <ui-image :src="item?.faceCaptureVo?.traitImg || ''"></ui-image>
          </div>
          <div class="name-item">
            <span class="primary">{{ item.name }}</span
            ><span>（{{ item.age }}岁）</span>
          </div>
        </div>

        <div class="right-content">
          <div class="location">{{ item.deviceName }}</div>
          <div class="time">{{ item.absTime }}</div>
        </div>
      </div>
    </div>
    <ui-empty v-if="list.length == 0"></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="list"
    ></CaptureDetail>
  </div>
</template>

<script>
import CaptureDetail from "./detail/capture-detail.vue";
export default {
  name: "nightOut",
  components: {
    CaptureDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tableIndex: -1,
    };
  },
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.showList(index);
    },
  },
};
</script>

<style lang="less" scoped>
.darktime-out {
  height: 100%;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;

  .dartktiem-item {
    height: 52px;
    display: flex;
    font-size: 16px;
    font-weight: bold;
    justify-content: space-between;
    background: #f9f9f9;
    padding-right: 10px;
    cursor: pointer;
    .left-content {
      display: flex;
      align-items: center;
      gap: 12px;

      .item-image {
        width: 48px;
        height: 48px;
        padding: 3px;
      }
    }

    .right-content {
      text-align: right;
      font-weight: 400;
      font-size: 14px;
      display: flex;
      padding: 5px 0;
      flex-direction: column;
      justify-content: space-between;
      color: rgba(0, 0, 0, 0.75);

      .time {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.35);
      }
    }
  }
}
</style>
