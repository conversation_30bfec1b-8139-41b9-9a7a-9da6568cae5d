<!--
    * @FileDescription: 布控时段排名
    * @Author: H
    * @Date: 2024/05/08
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="rangking" class="rangking" ref="rangking"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default{
    data() {
        return {
            formData: {
                type: ''
            },
            
            myEchart: null
        }
    },
    mounted() {
        // this.init()
    },
    methods: {
        init(list) {
            let yLeft = [];
            let yRight = [];
            list.forEach(item => {
                yLeft.push('【'+item.range+ '】');
                yRight.push(item.size)
            })
            this.rangkingChart(yLeft, yRight)
        },
        rangkingChart(yLeft, yRight) {
            let rank1 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAASCAYAAAAzI3woAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADWSURBVEiJzZXBcQIxDEWfdlyAqABK2BLSQxpIh6mBCkIJUAF7ykAsWTl4faAB5H/x2Kc3+l/fAhA/X8pT7mRL2AoAD5RmgACRd4rsQPW59sdkCZcO5KZzAI0JmZ9m4CG4daDwIy2SaYAW1w7UqtKSYQCI3bI/X6ewrMgIdZ0j1OabxPeH4jFFKcrn+VB4oCyWjQOwARTid8WHXaM5ef994dKBzBRZSP0yEGixT8jbCZlg54MbQAE/4hOUIu0KUKimr74mKWRYZusUQJQ91G6ajQJAtQ3gHyWPecrpoZC+AAAAAElFTkSuQmCC';
            let rank2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAASCAYAAAAzI3woAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAC5SURBVEiJzdTBjQIxEETRZ9ZIHJ0BhIDIP4edECAC5jiLPeo9jInBrosln76qWj+Bd5Sfs7fxWTNcKLWNZsEXqO7uo0l6lgyqIg1GgdQbSs0tRsMg8coQzXU0DATPY7KmCKTjd9gbfTJ1jqM+p+9RN2UwC9h2a/YbZRKg1SOt+bIp9TSaBayQa7ibw9ILZH+KGRpKvaHU3GICS6fwghz7JFIMT8j2LsXROfXJfCaRIsuOPImDbPlo6B+Ks0zXpyIXGAAAAABJRU5ErkJggg==';
            let rank3 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAASCAYAAAAzI3woAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADgSURBVEiJzZXBcYNADEWf8DKT47oCuwQmFaSklJA2UpUpwa7AHLFB+jkALUSry87e3jx9SQbw9fyp/no/SS4zpgLAPFdFMg3AAfRaYrBsGABjLADmqkLZOFhnm6FY/aoGFNnKowBIcVG+IAzuBcBDlQaIXN3WMnkMLQD1p248MlSzYQDmhcmG23dFc/pSBE3j5++5wFzD87eiGRNA8fcygIEAI+0VNm5Aq6qZ0oE4DBFxzZ8vIHgAlHBdaOBsIN0BiryNkZf2lsljaMAPpz72UIdX9st6ZOyo//wv/ccE8AeMu5FLZqiL3wAAAABJRU5ErkJggg==';
            let rank4 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAASCAYAAAAzI3woAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADgSURBVEiJzdMxTsNAEAXQN/YiEhESCWp67n8NLhEpFCAhwE4CSWx2KZw7rKeZ9unPnwClbNqzN7Wn6BIsWV+KvrYnwmuC4ehZqg8qxUuCkixCfZDGewLFY5kDqNgmaMImZ70GmVo78ribEspCqwPtVVthR0rfU4fCgxl82eJ8PRnGmAFo3/lJSrl1MmCoiYlw8hS/yafbvMjV04mm6SEtlzaXXOqD2MHUoWi7qhqMefryVEIbUb/Q+IJ0otzkGYD+7CHdLZzHfgagtROk496gOdQHWU0g9w62H1GbY7XK8A8TgWdwd2MIWAAAAABJRU5ErkJggg==';
            this.myEchart = echarts.init(this.$refs.rangking);
            // const yAxisDataLeft = ['【永久】', '【2024.4.30-2024.5.6】', '【2024.1.1-2024.3.31】', '【2024.4.1-2024.6.30】', '【2024.7.1-2024.9.30】', '【2024.10.1-2024.12.31】'];
            // const yAxisDataRight = ['8', '9', '11', '12','23',  '34', ];
            const yAxisDataLeft = yLeft;
            const yAxisDataRight = yRight.reverse();
            let option = {
                grid: {
                    left: 10,
                    bottom: 0,
                    right: 20,
                    top: 5,
                },
                xAxis: {
                    max: 130,
                    show: false,
                },
                yAxis: [
                    {
                        inverse: true,
                        data: yAxisDataLeft,
                        axisLabel: {
                            show: true,
                            inside: true,
                            margin:2,
                            lineHeight:28,
                            verticalAlign: 'bottom',
                            textStyle: {
                                color: '#D0DDE7',
                                fontSize: 14,
                                align: 'left',
                            },
                            formatter: function (value, index) {
                                if (index < 3) {
                                    return `{b${index}|NO.${index+1}} {a|${value}}`
                                }
                                else {
                                    return `{b|NO.${index+1}} {a|${value}}`
                                }
                            },
                            rich: {
                                a: {
                                    color: '#D0DDE7',
                                    lineHeight: 12,
                                },
                                b0: {
                                    fontWeight: 700,
                                    color: '#FFFFFF',
                                    fontFamily: 'DINPro',
                                    marginRight: 10,
                                    width: 30,
                                    height: 16,
                                    padding: [1, 2, 0, 6],
                                    lineHeight: 16,
                                    backgroundColor: {
                                        image: rank1,
                                    },
                                },
                                b1: {
                                    fontWeight: 700,
                                    color: '#FFFFFF',
                                    fontFamily: 'DINPro',
                                    marginRight: 10,
                                    width: 30,
                                    height: 16,
                                    padding: [1, 2, 0, 6],
                                    lineHeight: 16,
                                    backgroundColor: {
                                        image: rank2,
                                    },
                                },
                                b2: {
                                    fontWeight: 700,
                                    color: '#FFFFFF',
                                    fontFamily: 'DINPro',
                                    marginRight: 10,
                                    width: 30,
                                    height: 16,
                                    padding: [1, 2, 0, 6],
                                    lineHeight: 16,
                                    backgroundColor: {
                                        image: rank3,
                                    },
                                },
                                b: {
                                    fontWeight: 700,
                                    color: '#FFFFFF',
                                    fontFamily: 'DINPro',
                                    marginRight: 10,
                                    width: 30,
                                    height: 16,
                                    padding: [1, 2, 0, 6],
                                    textAlign: 'center',
                                    lineHeight: 16,
                                    backgroundColor: {
                                        image: rank4,
                                    },
                                },
                            }
                        },
                        //offset: 30,
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        }
                    },
                    {
                        inverse: false,
                        data: yAxisDataRight,
                        axisLabel: {
                            inside: true,
                            textStyle: {
                                color: '#ffffff',
                                fontSize: 14,
                                fontWeight: 700,
                                align: 'right',
                            },
                            formatter: '{value}\n{a|占位}\n{a|占位}',
                            rich: {
                                a: {
                                    color: 'transparent',
                                    lineHeight: 12,
                                    fontFamily: 'digital'
                                }
                            }
                        },
                        offset: 0,
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        }
                    }
                ],
                series: [
                    {
                        // 辅助系列
                        type: 'bar',
                        barGap: '-100%',
                        silent: true,
                        itemStyle: {
                            color: 'rgba(255, 255, 255, 0.1)',
                        },
                        barWidth: 8,
                        data: [130, 130, 130, 130, 130, 130]
                    },
                    {
                        type: 'bar',
                        data: [120, 100, 90, 60, 30, 20],
                        barWidth: 8,
                        label: {
                            position: [10, 10],
                            normal: {
                                position: [800, -24],
                                show: true,
                                textStyle: {
                                    color: '#8db0ff',
                                    fontSize: 14,
                                },
                            },
                        },
                        itemStyle: {
                            normal: {
                            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                'rgba(0,143,236,0)', '#00B9EC',
                                ].map((color, offset) => ({
                                    color,
                                    offset
                                })))
                            }
                        }
                    }
                ]
            }
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        },
        handleChange() {

        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .rangking{
        height: 100%;
        width: 100%;  
    }
}
</style>