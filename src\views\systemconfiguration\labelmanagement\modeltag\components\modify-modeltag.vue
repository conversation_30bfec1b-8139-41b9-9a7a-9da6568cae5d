<template>
  <ui-modal v-model="visible" :title="modalTitle" width="648px">
    <div>
      <Form
        ref="formData"
        :model="formData"
        :rules="mode == 'update' ? updateFormRules : formRules"
        :label-width="90"
        @submit.native.prevent
      >
        <FormItem prop="name" label="模型名称">
          <Input type="text" v-model="formData.name" placeholder="请输入模型名称"> </Input>
        </FormItem>
        <FormItem prop="type" label="主体类型">
          <Select v-model="formData.type" :disabled="mode == 'update'">
            <Option v-for="item in pricipalTypes" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem prop="tagId" label="关联标签">
          <Select v-model="formData.tagId" :filterable="true">
            <Option v-for="item in allDeviceTagList" :value="item.tagId" :key="item.tagId">{{ item.tagName }}</Option>
          </Select>
        </FormItem>

        <FormItem prop="packageUrl" label="模 型 包" v-if="mode == 'update'">
          <span
            class="inline width-lg ellipsis font-blue device-id pointer"
            :title="formData.packageUrl"
            @click="downLoadFile(formData.packageUrl)"
            >{{ formData.packageUrl }}</span
          >
        </FormItem>
        <FormItem prop="packageUrl" label="模 型 包" v-else>
          <ui-upload
            v-model="formData.packageUrl"
            :accept="accept"
            :format="format"
            :upload-url="uploadUrl"
            :get-upload-params="getUploadParams"
            @input="fileInput"
            @delUpload="delUpload"
            uploadPlaceholder="上传文件"
          >
          </ui-upload>
        </FormItem>

        <FormItem prop="description" label="备 注">
          <Input
            type="textarea"
            :disabled="mode == 'update'"
            v-model="formData.description"
            :rows="5"
            placeholder="请输入备注信息"
          >
          </Input>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
      <Button class="plr-30" type="primary" :loading="submitLoading" @click="handleSubmit">保 存</Button>
    </template>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';
import { PRINCIPAL_TYPES } from '../util/enum.js';
export default {
  props: {
    value: {},
    mode: {
      type: String,
      default: 'create',
    },
    rowObj: {
      type: Object,
      default() {
        return {};
      },
    },
    allDeviceTagList: {
      type: Array,
    },
  },
  data() {
    return {
      visible: false,
      formData: {
        name: '',
        type: 1,
        tagId: '',
        packageUrl: '',
        description: '',
      },
      formRules: {
        name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
        type: [{ required: true, type: 'number', message: '请选择主体类型', trigger: 'change' }],
        tagId: [{ required: true, type: 'number', message: '请选择关联标签', trigger: 'change' }],
        packageUrl: [{ required: true, message: '请上传模型包', trigger: 'change' }],
      },
      updateFormRules: {
        name: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
      },
      pricipalTypes: PRINCIPAL_TYPES, //主体类型
      uploadUrl: governanceevaluation.uploadStatistics,
      format: ['doc', 'docx', 'xlsx', 'bmp', 'jpg', 'png', 'JPEG', 'zip', '7z'],
      accept: '.bmp,.jpeg,.jpg,.png,.zip,.7z,.doc,.docx,.xlsx',
      submitLoading: false,
    };
  },
  components: {
    UiUpload: require('@/components/ui-upload.vue').default,
  },
  watch: {
    value(val) {
      // 关闭该弹框时清空
      if (!val) {
        this.formData = { type: 1 };
      }
      this.formData = this.$util.common.deepCopy(this.rowObj);
      this.formData.type = 1;
      //给地理区域赋值
      this.visible = val;
    },
    visible(val) {
      this.$refs.formData.resetFields();
      this.$emit('modelTagVisibleChange', val);
    },
  },
  computed: {
    modalTitle() {
      if (this.mode == 'update') {
        return '编辑模型';
      }
      return '新增模型';
    },
  },
  methods: {
    cancel() {
      this.visible = false;
    },
    //点击提交
    async handleSubmit() {
      //表单校验
      let validResult;
      this.$refs.formData.validate((valid) => (validResult = valid));
      if (!validResult) {
        return;
      }
      //提交请求
      try {
        const params = {
          ...this.formData,
        };
        this.submitLoading = true;
        if (this.mode == 'update') {
          await this.$http.put(taganalysis.tagModelDataUpdate, params);
        }
        if (this.mode == 'create') {
          await this.$http.post(taganalysis.tagModelDataAdd, params);
        }
        this.$Message.success('成功');
        this.$emit('submitSuccess');
        this.visible = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.submitLoading = false;
      }
    },
    getUploadParams(uploadData) {
      let uploadParams = [];
      uploadData.forEach((row) => {
        uploadParams.push(row.url || row.response.data);
      });
      return uploadParams.join(',');
    },
    //文件上传后清除校验
    fileInput() {
      this.$refs.formData.validateField('packageUrl');
    },
    //清除文件的回调
    delUpload() {
      this.formData.packageUrl = '';
    },
    //下载
    downLoadFile(url) {
      if (!url) return;
      window.open(url);
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20px 50px 40px 50px;
}
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
@{_deep} .ivu-form-item:last-child {
  margin-bottom: 0;
}
</style>
