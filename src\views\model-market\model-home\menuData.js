/*
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-09-02 14:12:19
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-19 09:55:33
 * @Description:
 */

const modelList = [
  {
    name: "人脸战法",
    englishName: "FACE WARFARE",
    list: [
      {
        name: "身份鉴别",
        englishName: "IDENTITY AUTHENTIVATION",
        imgUrl: require("@/assets/img/model/model1.png"),
        url: "identity-authentivation",
      },
      {
        name: "人脸比对",
        englishName: "FACE COMPARISON",
        imgUrl: require("@/assets/img/model/model2.png"),
        url: "face-compare",
      },
      {
        name: "人脸轨迹",
        englishName: "FACE-ANALYSIS",
        imgUrl: require("@/assets/img/model/model11.png"),
        url: "face-analysis",
      },
      {
        name: "同行分析",
        englishName: "FACE-PEER-ANALYSIS",
        imgUrl: require("@/assets/img/model/model11.png"),
        url: "face-peer-analysis",
      },
      {
        name: "碰撞分析",
        englishName: "FACE-COLLISION",
        imgUrl: require("@/assets/img/model/model6.png"),
        url: "face-collision",
      },
      {
        name: "频次分析",
        englishName: "FREQUENCY-ANALYSIS",
        imgUrl: require("@/assets/img/model/model6.png"),
        url: "face-frequency-analysis",
      },
      {
        name: "频繁出没",
        englishName: "FREQUENT OCCURRENCE",
        imgUrl: require("@/assets/img/model/model12.png"),
        url: "face-prequent-occurrence",
      },
    ],
  },
  {
    name: "车辆战法",
    englishName: "VEHICLE WARFARE",
    list: [
      {
        name: "行车轨迹",
        englishName: "WHEEL PATH",
        imgUrl: require("@/assets/img/model/model3.png"),
        url: "car-path",
      },
      {
        name: "跟车分析",
        englishName: "FOLLOW ANALYSIS",
        imgUrl: require("@/assets/img/model/model5.png"),
        url: "follow-analysis",
      },
      {
        name: "碰撞分析",
        englishName: "COLLISION ANALYSIS",
        imgUrl: require("@/assets/img/model/model6.png"),
        url: "collision-analysis",
      },
      {
        name: "频次分析",
        englishName: "FREQUENCY ANALYSIS",
        imgUrl: require("@/assets/img/model/model7.png"),
        url: "frequency-analysis",
      },
      {
        name: "昼伏夜出",
        englishName: "HIDE BY DAY AND COME OUT BY NIGHT",
        imgUrl: require("@/assets/img/model/model9.png"),
        url: "day-night",
      },
      {
        name: "落脚点分析",
        englishName: "FOOTHOLD ANALYSIS",
        imgUrl: require("@/assets/img/model/model8.png"),
        url: "foothold-analysis",
      },
      {
        name: "套牌分析",
        englishName: "DECK ANALYSIS",
        imgUrl: require("@/assets/img/model/model10.png"),
        url: "deck-analysis",
      },
      {
        name: "频繁夜出",
        englishName: "FREQUENTLY NIGHT OUT",
        imgUrl: require("@/assets/img/model/model9.png"),
        url: "frequently-night-out",
      },
      {
        name: "隐匿车分析",
        englishName: "HIDDENVEHICLE ANALYSIS",
        imgUrl: require("@/assets/img/model/model6.png"),
        url: "hidden-vehicle-analysis",
      },
      {
        name: "多目标轨迹",
        englishName: "MULTIPLE TRAJECTORY",
        imgUrl: require("@/assets/img/model/model3.png"),
        url: "multiple-trajectory",
      },
      {
        name: "无牌车分析",
        englishName: "ANANYSIS OF UNLICENSED CARS",
        imgUrl: require("@/assets/img/model/model15.png"),
        url: "unlicensed-vehicle",
      },
      {
        name: "未系安全带分析",
        englishName: "NOT WEARING SEATBELT",
        imgUrl: require("@/assets/img/model/model14.png"),
        url: "nonseatbelt-search",
      },
      {
        name: "夜间遮阳板分析",
        englishName: "VISOR DETECTION",
        imgUrl: require("@/assets/img/model/model17.png"),
        url: "sunvisor-search",
      },
      {
        name: "开车打电话",
        englishName: "DRIVE AND TALK ON THE PHONE",
        imgUrl: require("@/assets/img/model/model16.png"),
        url: "drivephone-search",
      },
    ],
  },
  {
    name: "感知战法",
    englishName: "ELECTIC WARFARE",
    list: [
      {
        name: "碰撞分析",
        englishName: "PANORAMIC PURSUIT",
        imgUrl: require("@/assets/img/model/model7.png"),
        url: "ele-collision-analysis",
      },
      {
        name: "连环智搜",
        englishName: "CROSS TRACK",
        imgUrl: require("@/assets/img/model/pic5.png"),
        url: "serial-search",
      },
    ],
  },
  {
    name: "视频战法",
    englishName: "VIDEO WARFARE",
    list: [
      {
        name: "跨镜追踪",
        englishName: "CROSS TRACK",
        imgUrl: require("@/assets/img/model/pic5.png"),
        url: "cross-track",
        // page: "page-npmap.html",
      },
      {
        name: "全景追逃",
        englishName: "PANORAMIC PURSUIT",
        imgUrl: require("@/assets/img/model/model7.png"),
        url: "panoramic-pursuit",
      },
      {
        name: "警卫路线",
        englishName: "GUARD ROUTE",
        imgUrl: require("@/assets/img/model/pic1.png"),
        url: "guard-route",
      },
    ],
  },

  {
    name: "特色战法",
    englishName: "SPECIAL METHODS",
    list: [
      {
        name: "由案到人",
        englishName: "CASE TO PERSON",
        imgUrl: require("@/assets/img/model/model14.png"),
        url: "casetoperson",
      },
      {
        name: "由人到案",
        englishName: "PERSON TO CASE",
        imgUrl: require("@/assets/img/model/model15.png"),
        url: "person-to-case",
      },
    ],
  },
];

export { modelList };
