<template>
  <div class="capture-details-left">
    <div class="capture-details-left-sculpture">
      <ui-image :src="personinfo.identityPhoto" />
      <!-- <img :src="personinfo.identityPhoto ? personinfo.identityPhoto : 'errimg'" alt="" /> -->
    </div>
    <p class="capture-details-left-item">
      <span class="capture-details-left-item-label">姓名：</span>
      <span class="capture-details-left-item-value">{{ personinfo.name ? personinfo.name : '无' }}</span>
    </p>
    <p class="capture-details-left-item">
      <span class="capture-details-left-item-label">证件号：</span
      ><span class="capture-details-left-item-value">{{ personinfo.idCard ? personinfo.idCard : '无' }}</span>
    </p>
    <tags-more :tagList="tagList" :defaultTags="4" placement="left-start" bgColor="#2D435F"></tags-more>
  </div>
</template>
<script>
export default {
  props: {
    personinfo: {
      type: Object,
      default: () => {},
    },
    tagList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {
    TagsMore: require('@/components/tags-more_str').default,
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.capture-details-left {
  width: 362px;
  height: 100%;
  padding: 50px 20px 27px;
  border: 1px solid var(--border-color);
  &-sculpture {
    width: 100%;
    height: 312px;
    margin-bottom: 27px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-item {
    margin-bottom: 14px;
    font-size: 14px;
    &-label {
      color: #8797ac;
    }
    &-value {
      color: #ffffff;
    }
  }
}
</style>
