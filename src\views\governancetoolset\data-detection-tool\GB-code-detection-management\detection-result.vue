<template>
  <div class="detection-result">
    <!-- 统计 -->
    <charts-container :abnormalCount="statisticsList"></charts-container>
    <div class="detection-result-container">
      <div class="detection-result-head">
        <div class="head">
          <i class="icon-font icon-jiancejieguoxiangqing"></i><span class="title">检测结果详情</span>
        </div>
        <Button
          :loading="exportLoading"
          custom-icon="icon-font icon-daochu"
          type="primary"
          class="export-btn"
          @click="exportHandle"
        >
          导出
        </Button>
      </div>
      <div class="detection-result-body">
        <div class="search-module">
          <div class="search">
            <ui-label class="inline" label="设备编码">
              <Input v-model="searchData.deviceId" class="width-lg" placeholder="请输入设备编码"></Input>
            </ui-label>
            <ui-label class="inline ml-lg" label="设备名称">
              <Input v-model="searchData.deviceName" class="width-lg" placeholder="请输入设备名称"></Input>
            </ui-label>
            <ui-label class="inline ml-lg" label="检测结果">
              <Select v-model="searchData.resultStatus" class="width-lg" placeholder="请选择检测结果" clearable>
                <Option value="1">合格</Option>
                <Option value="2">不合格</Option>
              </Select>
            </ui-label>
            <div class="inline ml-lg">
              <Button type="primary" @click="searchHandle">查询</Button>
              <Button class="ml-sm" @click="resetHandle">重置</Button>
            </div>
          </div>
        </div>
        <div class="auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :class="tableData.length > 0 ? '' : 'ui-table-scroll-nodata'"
            :loading="loading"
            :table-columns="columns"
            :table-data="tableData"
          >
            <template slot="deviceId" slot-scope="{ row }">
              <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
            </template>
            <template #isSuccess="{ row }">
              <span :style="{ color: row.isSuccess ? '#269F26' : '#CF3939' }">{{
                row.isSuccess ? '合格' : '不合格'
              }}</span>
            </template>
            <template #errorMessage="{ row }">
              <span>{{ row.errorMessage || '--' }}</span>
            </template>
          </ui-table>
        </div>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
      </div>
    </div>
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';

export default {
  components: {
    'charts-container': require('../components/charts-container.vue').default,
    'ui-table': require('@/components/ui-table.vue').default,
  },
  props: {
    dataObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statisticsList: [
        { title: '检测设备总数', count: 0, icon: 'icon-jianceshuliang' },
        { title: '国标编码异常 ', count: 0, icon: 'icon-guobiaobianmajiance' },
        {
          title: '长度不符合设定位数',
          count: 0,
          icon: 'icon-shujuchangdujiance',
        },
        {
          title: '前6位与行政区划不匹配',
          count: 0,
          icon: 'icon-qian6weiyuhangzhengquhuabupipei',
        },
        {
          title: '11-13位不符合标准',
          count: 0,
          icon: 'icon-a-1-13weibufuhebiaozhun',
        },
      ],
      exportLoading: false,
      searchData: {
        deviceId: '',
        deviceName: '',
        resultStatus: '',
      },
      loading: false,
      columns: [
        { title: '序号', width: 50, type: 'index', align: 'center' },
        { title: '设备编码', slot: 'deviceId', align: 'left', minWidth: 180 },
        {
          title: '设备名称',
          minWidth: 150,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          title: '行政区划',
          minWidth: 150,
          key: 'civilCode',
          align: 'left',
          tooltip: true,
        },
        {
          title: '检测结果',
          minWidth: 100,
          key: 'isSuccess',
          slot: 'isSuccess',
          align: 'left',
        },
        {
          title: '异常原因',
          minWidth: 200,
          key: 'errorMessage',
          slot: 'errorMessage',
          align: 'left',
        },
      ],
      tableDataSource: [],
      searchDataSource: [],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
    };
  },
  methods: {
    init() {
      this.tableDataSource = [];
      this.searchDataSource = [];
      this.tableData = [];
      this.loading = true;
      this.statisticsList.forEach((v) => {
        v.count = 0;
      });
      this.pageData.totalCount = 0;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.searchData.deviceId = '';
      this.searchData.deviceName = '';
      this.searchData.resultStatus = '';
      this.$http
        .post(governancetoolset.getClockList, this.dataObj)
        .then((res) => {
          this.tableDataSource = res.data;
          this.pageData.totalCount = this.tableDataSource.length;
          this.statisticsList[0].count = this.pageData.totalCount;
          this.tableDataSource.forEach((v) => {
            if (!v.isSuccess) {
              this.statisticsList[1].count++;
            }
          });
          this.searchDataSource = JSON.parse(JSON.stringify(this.tableDataSource));
          this.listHandle();
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
      this.listHandle();
    },
    listHandle() {
      let count = (this.pageData.pageNum - 1) * this.pageData.pageSize;
      this.tableData = this.searchDataSource.slice(count, count + this.pageData.pageSize);
    },
    // 查询
    searchHandle() {
      const resultStatus = this.searchData.resultStatus;
      this.searchDataSource = this.tableDataSource.filter((v) => {
        return (
          v.deviceId.indexOf(this.searchData.deviceId) >= 0 &&
          v.deviceName.indexOf(this.searchData.deviceName) >= 0 &&
          (resultStatus ? v.isSuccess === (resultStatus === '1') : true)
        );
      });
      this.pageData.totalCount = this.searchDataSource.length;
      this.pageData.pageNum = 1;
      this.listHandle();
    },
    // 重置
    resetHandle() {
      this.pageData.pageSize = 20;
      this.searchData.deviceId = '';
      this.searchData.deviceName = '';
      this.searchData.resultStatus = '';
      this.searchHandle();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.listHandle();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.listHandle();
    },
    // 跳转详情
    deviceArchives(item) {
      this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
    },
    // 导出
    async exportHandle() {
      this.exportLoading = true;
      try {
        let res = await this.$http.post(governancetoolset.exportInternationTestRecord, this.tableDataSource, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.error(err);
        this.exportLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.detection-result {
  display: flex;
  flex: 1;
  flex-direction: column;
  .detection-result-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    .detection-result-head {
      width: 100%;
      height: 54px;
      border-bottom: 1px solid var(--devider-line);
      display: flex;
      align-items: center;
      color: var(--color-primary);
      justify-content: space-between;
      .head {
        display: flex;
        align-items: center;
        .icon-font {
          font-size: 16px;
          margin-right: 6px;
        }
        .title {
          font-size: 14px;
          line-height: 20px;
        }
      }
      .export-btn {
        display: flex;
        align-items: center;
        /deep/ .icon-font {
          font-size: 14px;
          line-height: 14px;
          margin-right: 8px;
        }
      }
    }
    .detection-result-body {
      display: flex;
      flex: 1;
      flex-direction: column;
      .search-module {
        padding: 10px 0;
      }
      /deep/ .ui-page {
        padding: 20px 2px;
      }
    }
  }
}
</style>
