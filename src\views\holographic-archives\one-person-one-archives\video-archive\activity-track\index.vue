<template>
  <div class="trajectory-wrap">
    <div class="trajectory">
      <ui-card title="抓拍轨迹" class="m-b20" :padding="20">
        <div class="left">
          <div>
            <label>时间:</label>
            <ui-quick-date
              class="input-width"
              ref="searchQuickDateRef"
              v-model="searchParams.dataRange"
              type="month"
              border
              @change="dataRangeHandler"
            />
          </div>
          <div>
            <Button type="primary" class="btn-search" @click="search"
              >查询</Button
            >
          </div>
          <TimeLine
            :loading="loading"
            :currentClickIndex="currentClickIndex"
            :timelineList="positionPoints"
            @chooseTimeline="chooseTimeline"
            @handleReachBottom="handleReachBottom"
            :height="680"
          />
        </div>
        <div class="right">
          <mapBase
            ref="map"
            :mapLayerConfig="{
              showStartPoint: false,
              tracing: true,
            }"
            :sectionName="sectionName"
            :positionPoints="positionPoints"
            :currentClickIndex="currentClickIndex"
            @chooseMapItem="chooseTimeline"
            cutIcon="track"
          />
        </div>
      </ui-card>
    </div>
  </div>
</template>
<script>
import { getPerceivedTrajectory } from "@/api/activityTrack";
import mapBase from "@/components/map/index.vue";
import uiTag from "@/views/holographic-archives/components/ui-tag.vue";
import TimeLine from "@/views/holographic-archives/components/time-line.vue";
export default {
  components: { mapBase, uiTag, TimeLine },
  props: {},
  data() {
    return {
      positionPoints: [],
      // 字段隐射处理
      trajectoryMapFiled: {
        hotel: {
          roomNumber: "房间号",
          hotelAddress: "酒店地址",
        },
      },
      currentClickIndex: -1,
      searchParams: {
        dataRange: 2,
        dataType: 2,
        archiveNo: this.$route.query.archiveNo,
        startDate: "",
        endDate: "",
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 200,
      },
      loading: false,
      sectionName: "",
    };
  },
  mounted() {
    const { startDate, endDate } = this.$refs.searchQuickDateRef.getDate();
    this.searchParams.startDate = startDate;
    this.searchParams.endDate = endDate;
    this.getPerceivedTrajectory();
  },
  methods: {
    chooseTimeline(i) {
      if (i > -1) {
        const { dataType, geoPoint } = this.positionPoints[i];
        if (!geoPoint || !geoPoint.lat || !geoPoint.lon) {
          this.$Message.warning("经纬度信息不全");
          return;
        }
        const typeMap = {
          0: "face",
          1: "vehicle",
          2: "imsi",
        };
        this.sectionName = typeMap[dataType];
      }
      this.$nextTick(() => {
        this.currentClickIndex = i;
      });
    },

    /**
     * @description: 活动轨迹 - 切换时间类型
     * @param {object} val 起止时间
     */
    dataRangeHandler(val) {
      this.searchParams.startDate = val.startDate;
      this.searchParams.endDate = val.endDate;
      this.positionPoints = [];
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = 200;
      this.getPerceivedTrajectory();
    },
    // 底部滚动加载更多
    handleReachBottom() {
      if (this.loading) return;
      if (this.total <= this.pageInfo.pageSize) return;
      if (
        this.pageInfo.pageNumber >=
        Math.ceil(this.total / this.pageInfo.pageSize)
      )
        return;
      this.pageInfo.pageNumber++;
      this.getPerceivedTrajectory();
    },
    search() {
      this.positionPoints = [];
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = 200;
      this.getPerceivedTrajectory();
    },
    getPerceivedTrajectory() {
      this.$refs.map.resetMarker();
      this.$refs.map.resetSpeed();
      const { searchParams, pageInfo } = this;
      this.loading = true;
      getPerceivedTrajectory({ ...searchParams, ...pageInfo })
        .then((res) => {
          if (res.code === 200) {
            res.data.entities.map((item, index) => {
              item.id = `${index}_${item.vid}`;
            });
            this.total = res.data.total;
            this.positionPoints = res.data.entities;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.trajectory-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  padding: 16px 10px 10px;
}
.trajectory {
  display: flex;
  flex: 1;
  height: 100%;
  /deep/.card-content {
    display: flex;
    flex: 1;
    flex-direction: row;
    // height: calc(~"(100% - 30px)");
  }
  /deep/ .ui-card {
    height: 100%;
  }
  .left {
    width: 330px;
    height: calc(~"(100% - 5px)");
    > div {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      > label {
        font-size: 14px;
        margin-right: 10px;
      }
    }
    .input-width {
      width: 270px;
    }
    .btn-search {
      width: 100%;
      margin-right: 18px;
    }
  }
  .right {
    margin-top: -26px;
    width: calc(~"(100% - 300px)");
  }
}
.m-b20 {
  margin-bottom: 20px;
}
.extra {
  margin-top: 12px;
}
.travel-info {
  width: 100%;
  position: relative;
  .list {
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: -30px;
    height: 34px;
    line-height: 34px;
    margin-top: -40px;
    .ivu-tabs-tab {
      float: left;
      border: 1px solid #2c86f8;
      border-right: none;
      padding: 0 15px;
      color: #2c86f8;
      &:hover {
        background: #2c86f8;
        color: #ffffff;
      }
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-right: none;
      }
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-right: 1px solid #2c86f8;
      }
    }
    .active {
      background: #2c86f8;
      color: #fff;
    }
  }
  .page-content {
    height: 50px;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
