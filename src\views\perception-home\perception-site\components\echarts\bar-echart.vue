<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=xMMVsaQVBg
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    barBg: { // 是否显示背景
      type: Boolean,
      default: () => true
    },
    names: {
      type: Array,
      required: true,
      default: () => ['标签1', '标签2', '标签3', '标签4', '标签5', '标签6', '标签7', '标签8', '标签9', '标签10']
    },
    values: {
      type: Array,
      required: true,
      default: () => ['150', '28', '34', '46', '88', '12', '36', '96', '125', '126', '115', '128']
    }
  },
  data () {
    return {
      myEchart: null
    }
  },
  computed: {},
  watch: {},
  filter: {},
  mounted () {
    this.init()
  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      const dowmWidth = document.body.clientHeight
      var salvProMax = [] // 背景按最大值
      for (let i = 0; i < this.values.length; i++) {
        salvProMax.push(this.values[0] <= 0 ? 100 : this.values[0])
      }
      var option = {
        grid: {
          left: '0',
          right: '2%',
          bottom: '-10%',
          top: '0%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          formatter: function (params) {
            return _that.names[params[0].dataIndex] + ' : ' + params[0].value
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          show: false,
          type: 'value'
        },
        yAxis: [{
          show: false,
          type: 'category',
          inverse: true,
          axisLabel: {
            show: true,
            textStyle: {
              color: 'rgba(255, 255, 255, 0.89)',
              fontSize: 12,
              lineHeight: 12
            }
          },
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLine: {
            show: false
          }
          // data: this.names
        }, {
          show: true,
          type: 'category',
          inverse: true,
          axisTick: 'none',
          axisLine: 'none',
          axisLabel: {
            textStyle: {
              color: '#E99E53',
              fontSize: dowmWidth / 1080 * 14,
              fontWeight: 'bold',
              lineHeight: dowmWidth / 1080 * 14
            },
            formatter: function (value) {
              return value
            }
          },
          data: this.values
        }],
        series: [{
          name: '值',
          type: 'bar',
          zlevel: 1,
          itemStyle: {
            normal: {
              barBorderRadius: 5,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                offset: 0,
                color: 'rgba(7, 71, 153, 0)'
              }, {
                offset: 0.9,
                color: '#12C1E1'
              }, {
                offset: 1,
                color: '#A7F1FF'
              }])
            }
          },
          barWidth: 7,
          data: this.values
        }
        ]
      }

      if (this.barBg) {
        option.series.push({
          name: '背景',
          type: 'bar',
          show: false,
          barWidth: 8,
          barGap: '-100%',
          data: salvProMax,
          itemStyle: {
            normal: {
              color: 'rgba(7, 53, 94, 0.2)',
              barBorderRadius: 5
            }
          }
        })
      }
      this.myEchart.setOption(option)
      window.addEventListener('resize', () => this.myEchart.resize())
    },

    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
