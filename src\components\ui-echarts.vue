<template>
  <div class='echarts-container'>
    <ui-loading v-if="loading" />
    <div class="echarts" ref="echarts"></div>
  </div>
</template>
<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      echarts: null,
    }
  },
  props: {
    loading: {
      require: true,
      default: false
    },
    options: {
      require: true,
      type: Object,
    }
  },
  async mounted() {
    await this.$nextTick()
    this.initEcharts()
  },
  beforeDestroy() {
    this.removeListener()
  },
  methods: {
    initEcharts() {
      this.echarts = echarts.init(this.$refs.echarts)
      this.echarts.setOption(this.options)
      window.addEventListener('resize', () => this.echarts.resize())
    },
    removeListener() {
      window.removeEventListener('resize', () => this.echarts.resize())
    }
  },
  watch: {
    options: {
      handler(val) {
        this.echarts.setOption(this.options)
      },
      deep: true,
    }
  }
}
</script>
<style scoped lang="less">
.echarts-container, .echarts{
  position: relative;
  height: 100%;
  width: 100%;
}
</style>