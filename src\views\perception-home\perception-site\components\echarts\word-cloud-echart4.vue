<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
import * as wordcloud from 'echarts-wordcloud'
/**
 *  参考  https://www.makeapie.com/editor.html?c=x9cbKbSZbZ
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    labelList: {
      type: Array,
      default: () => []
    }

  },
  data () {
    return {
      myEchart: null,
      wordcloud: wordcloud
    }
  },
  computed: {},
  watch: {},
  filter: {},
  mounted () {
    this.init()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      const documentWidth = document.body.clientWidth

      let sum = 0
      this.labelList.map((v) => {
        sum = sum + v.name.length
      })
      let gridSize = 30
      if (documentWidth <1700 && documentWidth > 1400) {
        gridSize = 28
      } else if (documentWidth < 1400) {
        gridSize = 28
      }
      var option = {
        tooltip: {
          show: true,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          },
          formatter: function (params) {
            return `${params.data.labelName}`
          }
        },
        series: [
          {
            type: 'wordCloud',
            // 网格大小，各项之间间距
            gridSize: gridSize,
            // 形状 circle 圆，cardioid  心， diamond 菱形，
            // triangle-forward 、triangle 三角，star五角星
            // 文字旋转角度范围
            rotationRange: [0, 0],
            left: 'center',
            top: 'center',
            right: null,
            bottom: null,
            // 是否渲染超出画布的文字
            drawOutOfBound: true,
            layoutAnimation: true,
            textStyle: {
              fontStyle: 'normal',
              fontFamily: 'Microsoft YaHei',
              textVerticalAlign: 'center',
              borderRadius: 2,
              borderWidth: 0.5
            },
            label: {
              formatter: '{c} %'
            },
            itemStyle: {
              label: {
                formatter: '{c} %'
              }
            },
            data: this.labelList
          }
        ]
      }
      this.myEchart.setOption(option)
      this.myEchart.on('click', function (params) {
        _that.$router.push({
          path: '/label-management/label-info',
          query: {
            id: params.data.labelId,
            curName: params.name
          }
        })
      })
      window.addEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
