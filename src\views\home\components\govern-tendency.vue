<template>
  <!-- 治理趋势 -->
  <div class="govern-tendency" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-zhiliqushi" v-model="activeValue" :data="tabData" @on-change="onChangeTitle">
      <template #filter>
        <DatePicker
          :open="open"
          :value="year"
          confirm
          :options="options"
          type="year"
          format="yyyy"
          placeholder="请选择年"
          @on-change="handleChange"
          @on-ok="handleOk"
        >
          <i @click="handleClick" class="year-color">{{ year }}年</i>
        </DatePicker>
        <IndexSelect
          v-if="activeValue === 'DrawEcharts'"
          v-model="selectData"
          :data="sourceData"
          @change="handleChangeIndex"
          class="ml-sm"
        ></IndexSelect>
        <IndexModuleSelect
          v-else
          v-model="indexModuleSelectData"
          :data="sourceData"
          @change="handleChangeIndexModule"
          class="ml-sm"
        ></IndexModuleSelect>
      </template>
    </HomeTitle>
    <div
      class="echarts-box"
      v-ui-loading="{
        loading: echartsLoading,
        tableData: activeValue === 'DrawEcharts' ? valueList : governEffectData,
      }"
    >
      <component
        :is="activeValue"
        :echart-option="governEchart"
        ref="governChart"
        class="echart"
        :echarts-loading="echartsLoading"
        :data="governEffectData"
        :tableColumns="tableColumns"
      ></component>
    </div>
  </div>
</template>
<style lang="less" scoped>
@{_deep} .ivu-picker-confirm {
  .ivu-btn-default {
    display: none;
  }
  display: flex;
  justify-content: center;
  align-items: center;
}
.year-color {
  color: #63ccfc;
}
.govern-tendency {
  z-index: 2;
  width: 25%;
  height: 33%;
  position: absolute;
  right: 10px;
  bottom: 10px;
  background: rgba(0, 104, 183, 0.13);
  .govern-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .echarts-box {
    width: 100% !important;
    height: calc(100% - 42px) !important;
    .echart {
      height: 100% !important;
    }
    @{_deep} .ivu-table-tip {
      display: none !important;
    }
  }
}
.full-screen-container {
  height: 30.6%;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
import Vue from 'vue';
import GovernTendencyTooltip from '@/views/home/<USER>/govern-tendency-tooltip';
export default {
  name: 'govern-tendency',

  data() {
    return {
      activeValue: 'DrawEcharts',
      tabData: [
        { label: '治理趋势', id: 'DrawEcharts' },
        { label: '治理成效', id: 'GovernEffect' },
      ],
      open: false,
      year: `${new Date().getFullYear()}`,
      options: {
        disabledDate: function (date) {
          let now = new Date().getFullYear();
          return date.getFullYear() > now;
        },
      },
      sourceData: {},
      selectData: [],
      governEffectData: [],
      indexModuleSelectData: '1',
      governStyle: {
        width: '100%',
        height: '230px',
      },

      governEchart: {},
      echartsLoading: false,
      echart1: [],

      xData: (function () {
        var data = [];
        for (var i = 1; i < 13; i++) {
          data.push(i + '月');
        }
        return data;
      })(),
      valueList: [],
      legendList: [],
      colorList: ['rgba(12, 208, 132, 1)', 'rgba(245, 152, 53, 1)', 'rgba(157, 105, 226, 1)', 'rgba(4, 205, 244, 1)'],
      tooltipFormatter: (data) => {
        let year = this.year;
        let GovernTendencyTooltip1 = Vue.extend(GovernTendencyTooltip);
        let _this = new GovernTendencyTooltip1({
          el: document.createElement('div'),
          data() {
            return {
              data,
              year,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      tableColumns: [
        {
          title: '治理内容',
          key: 'indexName',
          minWidth: 160,
          ellipsis: true,
          render: (h, { row }) => {
            return <span title={row.indexName}>{row.indexName}</span>;
          },
        },
        {
          title: '治理前（1月平均）',
          key: 'minResultValue',
          tooltip: true,
          minWidth: 100,
          render: (h, { row }) => {
            return <span class="color-red">{row.minResultValue}%</span>;
          },
          renderHeader: (h) => {
            return <span>治理前({this.minMonth}月平均)</span>;
          },
        },
        {
          title: '治理后（12月平均）',
          key: 'maxResultValue',
          tooltip: true,
          minWidth: 100,
          render: (h, { row }) => {
            return <span class="color-blue">{row.maxResultValue}%</span>;
          },
          renderHeader: (h) => {
            return <span>治理后({this.maxMonth}月平均)</span>;
          },
        },
        {
          title: '变化',
          key: 'changeValue',
          tooltip: true,
          minWidth: 70,
          align: 'right',
          render: (h, { row }) => {
            return (
              <span>
                <span class={Number.parseFloat(row.changeValue) >= 0 ? ['color-green'] : ['color-red']}>
                  {row.changeValue}%
                </span>
                <span
                  class={
                    Number.parseFloat(row.changeValue) >= 0
                      ? ['icon-font', 'icon-shangsheng', 'color-green']
                      : ['icon-font', 'icon-xiajiang', 'color-red']
                  }
                ></span>
              </span>
            );
          },
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
    }),
    minMonth() {
      if (this.governEffectData.length > 0) {
        return this.governEffectData[0]['minMonth'];
      }
      return 1;
    },
    maxMonth() {
      if (this.governEffectData.length > 0) {
        return this.governEffectData[0]['maxMonth'];
      }
      return 12;
    },
  },
  async mounted() {
    await this.getAllEvaluationIndex();
    this.randomIndex();
    await this.init();
    await this.governRin();
  },

  methods: {
    async initList() {
      try {
        let { taskSchemeId, regionCode } = this.getHomeConfig;
        this.echartsLoading = true;
        let params = {
          indexModule: this.indexModuleSelectData,
          taskSchemeId,
          year: this.year,
          civilCode: regionCode,
        };
        let {
          data: { data },
        } = await this.$http.post(home.getHomeGovernanceResultList, params);
        this.governEffectData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    onChangeTitle() {
      if (this.activeValue !== 'DrawEcharts') {
        this.initList();
      }
    },
    handleClick() {
      this.open = !this.open;
    },
    handleChange(date) {
      this.year = date;
    },
    async handleOk() {
      this.open = false;
      if (this.activeValue === 'DrawEcharts') {
        await this.init();
        await this.governRin();
      } else {
        await this.initList();
      }
    },
    /**
     * 通过indexType获取IndexName
     * @param indexType
     */
    getIndexNameByIndexType(indexType) {
      let keys = Object.keys(this.sourceData);
      for (let j = 0; j < keys.length; j++) {
        let key = keys[j];
        let data = this.sourceData[key];
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          if (item.indexType === indexType) {
            return item.indexName;
          }
          if (indexType === 'SCHEME_INDEX_RATE') {
            return '指标达标率';
          }
        }
      }
    },
    /**
     * 随机选3个指标
     * @param
     */
    randomIndex() {
      this.selectData = [];
      if (this.sourceData.hasOwnProperty(1)) {
        let indexTypeList = this.sourceData[1].map((item) => item.indexType);
        this.selectData = indexTypeList.splice(0, 3);
      }
    },
    async getAllEvaluationIndex() {
      let {
        data: { data },
      } = await this.$http.get(home.getAllEvaluationIndex);
      this.sourceData = data || {};
    },
    async init() {
      try {
        this.echartsLoading = true;
        let params = {
          years: this.year,
          schemes: this.selectData,
        };
        let {
          data: { data },
        } = await this.$http.post(home.queryGovernanceTrends, params);
        this.valueList = [];
        this.legendList = [];
        Object.keys(data).map((key) => {
          this.valueList.push(data[key]);
          this.legendList.push(this.getIndexNameByIndexType(key));
        });
      } catch (e) {
        // console.log(e)
      } finally {
        this.echartsLoading = false;
      }
    },
    async handleChangeIndex() {
      await this.init();
      await this.governRin();
    },
    async handleChangeIndexModule() {
      await this.initList();
    },
    governRin() {
      let series = this.valueList.map((item, index) => {
        return {
          name: this.legendList[index],
          type: 'line',
          data: this.valueList[index],
          showSymbol: false,
          lineStyle: {
            width: this.$util.common.fontSize(1),
            color: this.colorList[index % this.colorList.length], //线条颜色
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 1,
            shadowOffsetY: 1,
          },
          itemStyle: {
            color: this.colorList[index % this.colorList.length],
            borderColor: this.colorList[index % this.colorList.length],
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'top',
            color: this.colorList[index % this.colorList.length],
          },
        };
      });
      let opts = {
        xAxis: this.xData,
        data: series,
        dayType: this.dayType,
        year: this.year,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.governEchart = this.$util.doEcharts.governColumn(opts);
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
    IndexSelect: require('./index-select').default,
    IndexModuleSelect: require('@/views/home/<USER>/index-module-select.vue').default,
    GovernEffect: require('@/views/home/<USER>/govern-effect.vue').default,
  },
};
</script>
