/**
 * 节流: n秒内只执行一次
 * @param {event} 绑定事件(默认点击事件)
 * @param {wait} 默认300ms执行一次
 * @param {fn} 执行函数
 */

export default function (Vue) {
  Vue.directive('throttle', {
    inserted(el, bind) {
      let [fn, event = 'click', wait = 300] = bind.value;
      let previous = 0;
      el.addEventListener(event, () => {
        let now = Date.now();
        if (now - previous > wait) {
          fn();
          previous = Date.now();
        }
      });
    },
  });
}
