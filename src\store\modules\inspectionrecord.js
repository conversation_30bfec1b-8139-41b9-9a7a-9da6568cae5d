export default {
  namespaced: true,
  state: {
    deviceTestOverview: {},
  },
  mutations: {
    setDeviceTestOverview(state, data) {
      state.deviceTestOverview = data;
    },
  },
  getters: {
    getDeviceTestOverview(state) {
      return state.deviceTestOverview;
    },
  },
  actions: {
    deviceTestOverviewAction({ commit }, data) {
      commit('setDeviceTestOverview', data);
    },
  },
};
