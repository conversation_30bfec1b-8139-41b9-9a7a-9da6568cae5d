<template>
  <difference v-model="differenceShow" :field-list="fieldList" :sign="sign" modal-title="差异详情">
    <template #contentTitle>
      <div>
        <span>字段名称</span>
      </div>
      <div class="over-flow">
        <i class="icon-font icon-zichankushebei"></i>
        <span class="ml-xs">资产库设备</span>
      </div>
      <div class="over-flow">
        <i class="icon-font icon-tianbaoshebei"></i>
        <span class="ml-xs">同步设备</span>
      </div>
    </template>
    <template #filter>
      <RadioGroup class="fl" v-model="sign">
        <Radio label="differ">标记差异字段</Radio>
        <Radio class="ml-lg" label="unqualified">标记不合格字段</Radio>
      </RadioGroup>
      <Checkbox class="fr" v-model="onlyDiff" @on-change="changeOnly">
        {{ sign === 'differ' ? '只显示差异字段' : '只显示不合格字段' }}
      </Checkbox>
    </template>
  </difference>
</template>
<script>
export default {
  props: {
    value: {},
    activeDifferDetail: {},
  },
  data() {
    return {
      sign: 'differ',
      onlyDiff: false,
      fieldList: [],
      differenceShow: false,
    };
  },
  created() {},
  methods: {
    cancel() {
      this.differenceShow = false;
    },
    changeOnly(val) {
      const stragety = {
        differ: 'isDiffer',
        unqualified: 'isUnqualified',
      };
      if (val) {
        this.fieldList = this.activeDifferDetail.filter((row) => row[stragety[this.sign]] === '1');
      } else {
        this.fieldList = this.$util.common.deepCopy(this.activeDifferDetail);
      }
    },
  },
  watch: {
    value(val) {
      this.differenceShow = val;
      if (val) {
        this.fieldList = this.$util.common.deepCopy(this.activeDifferDetail);
      }
    },
    differenceShow(val) {
      this.$emit('input', val);
    },
  },
  components: {
    Difference: require('@/views/viewassets/components/difference.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
