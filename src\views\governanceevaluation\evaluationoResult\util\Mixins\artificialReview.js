import evaluationoverview from '@/config/api/evaluationoverview';

//  人工复核方法混入[组件中需引组件]
const mixin = {
  mixins: [],
  data() {
    return {
      artificialVisible: false,
      statisticShow: false,
      artificialRow: {},
    };
  },
  methods: {
    // 人工复核
    clickArtificialReview(row) {
      this.artificialRow = row;
      this.artificialVisible = true;
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {},
};
export default mixin;
