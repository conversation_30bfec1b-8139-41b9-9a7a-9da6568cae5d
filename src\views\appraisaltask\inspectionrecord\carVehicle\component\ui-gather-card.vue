<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card">
    <div class="ui-gather-card-left">
      <div class="img">
        <ui-image :src="list.imageUrl" class="ui-image-card" />
        <p class="shadow-box" style="z-index: 11" title="查看大图">
          <i class="icon-font icon-yichang search-icon mr-xs base-text-color" @click="viewBigPic(list.imageUrl)"></i>
        </p>
      </div>
    </div>
    <div class="ui-gather-card-right">
      <div class="ui-gather-card-right-items" v-for="(item, index) in cardInfo" :key="index">
        <p v-if="!item.type" class="ui-gather-card-right-items-p">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            v-if="!item.algorithm"
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-value"
            :style="{
              color: item.than ? (list[item.value] == list[item.than] ? '#ffffff' : '#C43D2C') : '#ffffff',
            }"
            >{{ list[item.value] || '缺失' }}</span
          >
          <span
            v-else
            :title="list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失')"
            class="ui-gather-card-right-item-value"
            :style="{
              color: item.than ? (list[item.value] == list[item.than] ? '#ffffff' : '#C43D2C') : '#ffffff',
            }"
          >
            {{ list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失') }}</span
          >
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  computed: {
    cardInfoImage() {
      let array = [];
      this.cardInfo.map((val) => {
        if (val.type === 'image') {
          array.push(val);
        }
      });
      return array;
    },
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
    }),
  },
  created() {},
  mounted() {},
  methods: {
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
    algorithm(item) {
      return this[item];
    },
  },
  watch: {},
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  margin-bottom: 10px;
  padding: 10px;
  padding-bottom: 6px;
  background: #0f2f59;
  &-left {
    margin-right: 20px;
    width: 31%;
    display: flex;
    align-items: center;
    .img {
      width: 104px;
      height: 104px;
      position: relative;
    }
  }
  &-right {
    flex: 1;
    width: 50%;
    &-items {
      // margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
    }
  }
}
.ui-gather-card-right-items-p {
  // margin-bottom: 6px;
}
.ui-image-card {
  cursor: pointer;
}
.ui-gather-card-right-item-value {
  color: #ffffff;
  color: rgb(255, 255, 255);
  width: 66%;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  position: relative;
  top: 5px;
}
.ui-gather-card-right-item-label {
  color: #8797ac;
}
.shadow-box {
  height: 28px;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  bottom: 0;
  display: none;
  padding-left: 10px;
  > i:hover {
    color: var(--color-primary);
  }
}
.img:hover {
  .shadow-box {
    display: block;
  }
}
</style>
