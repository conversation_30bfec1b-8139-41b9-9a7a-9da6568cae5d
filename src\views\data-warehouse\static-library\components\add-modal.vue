<template>
  <ui-modal
    v-model="visible"
    :title="type === 1 ? '新增': '编辑'"
    :r-width="720"
    @onOk="comfirmHandle">
    <Form ref="staticLibraryForm" :model="staticLibraryForm" :rules="ruleInline" :label-width="120">
      <FormItem prop="libName" label="名称">
        <Input v-model="staticLibraryForm.libName" :maxlength="20" placeholder="请输入名称"/>
      </FormItem>
      <FormItem label="是否用于置信" v-show="this.libraryType == '1'">
        <RadioGroup v-model="staticLibraryForm.archiveStatus">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
      </RadioGroup>
      </FormItem>
      <FormItem label="是否用于布控">
        <RadioGroup v-model="staticLibraryForm.controlStatus">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
      </RadioGroup>
      </FormItem>
      <FormItem label="算法厂商" v-show="this.libraryType == '1'">
            <RadioGroup v-model="staticLibraryForm.controlStatus">
            <Radio label="1">海康</Radio>
            <Radio label="0">格灵深瞳</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem prop="remark" label="备注" >
        <Input v-model="staticLibraryForm.remark" :maxlength="200" :rows="4" type="textarea" placeholder="请输入备注"/>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
  import { personnelAdd, personnelEdit, addVehicleLib, motifyVehicleLib } from '@/api/data-warehouse'
  export default {
    props: {
      libraryType: {
        type: String,
        default: '1'
      }
    },
    data() {
      return {
        type: 1,
        visible: false,
        staticLibraryForm: {
          archiveStatus: '1',
          controlStatus: '1',
          libName: '',
          remark: ''
        },
        ruleInline: {
          libName: [
            { required: true, message: '请输入名称', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      show(val, item) {
        this.type = val
        this.visible = true
        this.$nextTick(() => {
          this.$refs.staticLibraryForm.resetFields()
          if(val === 2) {
            this.staticLibraryForm = JSON.parse(JSON.stringify(item))
          }
        })
      },
      comfirmHandle() {
        this.$refs.staticLibraryForm.validate((valid) => {
          if (valid) {
            // 新增
            if(this.type === 1) {
              if (this.libraryType == '1') {
                personnelAdd(this.staticLibraryForm).then((res) => {
                  this.visible = false
                  this.$Message.success('新增成功')
                  // this.$emit('on-change', this.staticLibraryForm)
                  this.$parent.init()
                })
              } else {
                var param = JSON.parse(JSON.stringify(this.staticLibraryForm))
                delete param.archiveStatus
                addVehicleLib(param).then((res) => {
                  this.visible = false
                  this.$Message.success('新增成功')
                  // this.$emit('on-change', this.staticLibraryForm)
                  this.$parent.init()
                })
              }
            }else {
            //编辑
              if (this.libraryType == '1') {
                personnelEdit(this.staticLibraryForm).then(res => {
                  this.visible = false
                  this.$Message.success('编辑成功')
                  this.$parent.init()
                })
              }else {
                var param = JSON.parse(JSON.stringify(this.staticLibraryForm))
                delete param.archiveStatus
                motifyVehicleLib(param).then(res => {
                  this.visible = false
                  this.$Message.success('编辑成功')
                  this.$parent.init()
                })
              }
            }

          }
        })
      }
    }
  }
</script>