/*
 * @Author: zhengming<PERSON> <EMAIL>
 * @Date: 2025-06-05 16:53:27
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-06-11 10:11:34
 * @FilePath: \icbd-view\src\views\semantic-placement\alert-manager\components\enum.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const taskTypeList = [
    {
      value: 1,
      label: "实时解析",
    },
    {
      value: 3,
      label: "历史解析",
    },
    {
      value: 5,
      label: "文件解析",
    },
  ];

export const getTaskTypeName = (val) => taskTypeList?.find(el => el.value == val)?.label;
