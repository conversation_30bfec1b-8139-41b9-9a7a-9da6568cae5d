<template>
  <div class="auto-fill">
    <div class="search-module over-flow">
      <ui-label class="inline" label="姓名">
        <Input class="width-sm" v-model="searchData.name" placeholder="请输入姓名"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="证件号">
        <Input class="width-sm" v-model="searchData.idCard" placeholder="请输入证件号"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="所属地">
        <api-area-tree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </ui-label>
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="btn-box">
      <Button type="text" class="ml-sm" @click="exportModule">
        <span class="link">下载模板</span>
      </Button>
      <Upload
        action="/ivdg-asset-app/asset/data/import"
        ref="upload"
        class="inline"
        :show-upload-list="false"
        :headers="headers"
        :data="uploadData"
        :before-upload="beforeUpload"
        :on-success="importSuccess"
        :on-error="importError"
      >
        <Button type="primary" class="ml-sm" :loading="importLoading">
          <i class="icon-font icon-daoruwentishebei f-12"></i>
          <span class="vt-middle ml-sm">导入设备</span>
        </Button>
      </Upload>
      <Button class="ml-sm" type="primary" @click="add">
        <i class="icon-font icon-tianjia f-12"></i>
        <span class="ml-sm">新增人员信息</span>
      </Button>
      <Button class="ml-sm" type="primary" :loading="deleteLoading" @click="batchDelete">
        <i class="icon-font icon-piliangshanchu f-12"></i>
        <span class="ml-sm">批量删除</span>
      </Button>
    </div>
    <div class="table-module auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
      <div class="table-box">
        <picture-item
          class="picture-item"
          v-for="(item, index) in tableData"
          v-model="item.checked"
          :key="index"
          :img-src="item.url"
        >
          <template #button>
            <div class="option">
              <div class="button-div">
                <div class="inline" @click="edit(item)">
                  <i class="icon-font icon-bianji"></i>
                  <span class="ml-mini">编辑</span>
                </div>
                <span class="sign">|</span>
                <div class="inline" @click="deleteItem(item)">
                  <i class="icon-font icon-piliangshanchu"></i>
                  <span class="ml-mini">删除</span>
                </div>
              </div>
            </div>
          </template>
          <template #message>
            <p class="text">
              <i class="icon-font icon-ren"></i>
              <span class="inline vt-middle font-text ml-xs ellipsis" :title="item.name">{{
                item.name || '未知'
              }}</span>
            </p>
            <p class="text mt-xs">
              <i class="icon-font icon-shenfenzheng"></i>
              <span class="inline vt-middle font-text ml-xs ellipsis" :title="item.idCard">{{ item.idCard }}</span>
            </p>
            <p class="text mt-xs">
              <i class="icon-font icon-dizhi"></i>
              <span class="inline vt-middle font-text ml-xs" :title="item.civilName">{{ item.civilName }}</span>
            </p>
          </template>
        </picture-item>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <add-edit
      v-model="addEditShow"
      :modal-action="modalAction"
      :modal-data="modalData"
      :save="save"
      @update="init"
    ></add-edit>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    searchFun: {},
    deleteFun: {},
    save: {},
  },
  data() {
    return {
      loading: false,
      deleteLoading: false,
      selectTree: {
        regionCode: '',
      },
      searchData: {
        name: '',
        idCard: '',
        type: 1,
        civilCode: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      addEditShow: false,
      modalAction: {
        title: '新增人员信息',
        action: 'add',
      },
      modalData: {},
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      uploadData: {
        type: 1,
      },
      importLoading: false,
    };
  },
  created() {
    this.init();
  },
  methods: {
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.searchFun(this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    deleteItem(row) {
      this.$UiConfirm({
        content: `您确定要删除${row.name}，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          try {
            this.deleteLoading = true;
            const res = await this.deleteFun([row]);
            this.$Message.success(res.data.msg);
            this.search();
          } catch (err) {
            console.log(err);
          } finally {
            this.deleteLoading = false;
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    batchDelete() {
      const deleteArr = this.tableData.filter((row) => !!row.checked);
      if (!deleteArr.length) {
        this.$Message.warning('请选择要删除的数据');
        return;
      }
      this.$UiConfirm({
        content: `您确定要删除这${deleteArr.length}项，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          try {
            this.deleteLoading = true;
            const res = await this.deleteFun(deleteArr);
            this.$Message.success(res.data.msg);
            this.search();
          } catch (err) {
            console.log(err);
          } finally {
            this.deleteLoading = false;
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    add() {
      this.modalAction = {
        title: '新增人员信息',
        action: 'add',
      };
      this.modalData = {};
      this.addEditShow = true;
    },
    edit(row) {
      this.modalAction = {
        title: '编辑人员信息',
        action: 'edit',
      };
      this.modalData = row;
      this.addEditShow = true;
    },
    async exportModule() {
      try {
        const res = await this.$http.get(equipmentassets.dowloadManagementData, {
          responseType: 'blob',
          params: {
            type: 1,
          },
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        this.$Message.success(res.msg);
        this.search();
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
  },
  watch: {},
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    PictureItem: require('../components/picture-item.vue').default,
    AddEdit: require('./add-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-module {
  padding: 10px 20px;
  margin-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}
.table-module {
  padding: 10px 10px 20px;
  padding-left: 10px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow-y: auto;
}
.text {
  display: flex;
  align-items: center;
  span {
    width: calc(100% - 14px);
  }
  i {
    color: #8797ac;
    width: 14px;
    text-align: center;
    font-size: 12px;
  }
}
.picture-item {
  width: calc((100% - 80px) / 8);
  &:hover {
    .option {
      display: flex;
    }
  }
}
.option {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 28px;
  background-color: var(--bg-info-card-option);
  color: var(--color-info-card-option);
  z-index: 2;
  display: none;
  justify-content: center;
  align-items: center;
  i {
    font-size: 12px;
  }
  .button-div {
    width: 100%;
    position: relative;
    cursor: pointer;
    > div {
      width: 50%;
      text-align: center;
    }
    .sign {
      color: #8797ac;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
.font-text {
  color: var(--color-input);
}
.btn-box {
  display: flex;
  justify-content: flex-end;
  padding: 0 20px;
}
</style>
