<template>
  <div class="asset-comparison-wrap auto-fill">
    <div class="header">
      <div class="header-left">
        <div class="title">
          <i class="icon-font icon-shujuyuan1"></i>
          <span> 数据源: 资产库</span>
        </div>
        <div class="content">
          <div class="content-item" v-for="(e, i) in statisticalLeftList" :key="i">
            <span>
              <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
            </span>
            <span>
              <div class="name">{{ e.name }}</div>
              <div class="count" :style="{ color: e.color }">
                {{ e.count }}
              </div>
            </span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="title">
          <span>
            <i class="icon-font icon-shujuyuan1"></i>
            <span> 数据源: 上报库</span>
          </span>
        </div>
        <div class="content">
          <div class="content-item" v-for="(e, i) in statisticalRightList" :key="i">
            <span>
              <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
            </span>
            <span>
              <div class="name">{{ e.name }}</div>
              <div class="count" :style="{ color: e.color }">
                {{ e.count }}
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="tab-line">
      <div class="tab-line-left">
        <div
          v-for="(item, index) in statusList"
          :key="index"
          :class="{ active: item.status === activeStatus }"
          class="ivu-tabs-tab"
          @click="tabClick(item)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="tab-line-right">
        <Checkbox v-if="this.activeStatus !== '0'" @on-change="changeIsLeftAll" v-model="isAll" :disabled="false">
          <span class="ml-xs">全选</span>
        </Checkbox>
        <Button
          v-if="this.activeStatus !== '1'"
          type="primary"
          class="ml-sm button-blue"
          :loading="exportDataLoading"
          @click="updateSync"
        >
          <i class="icon-font icon-xinxizidongtongbu f-12 mr-sm vt-middle"></i>
          <span class="inline vt-middle ml-xs">同步</span>
        </Button>
        <Button type="primary" class="ml-sm button-blue" :loading="exportDataLoading" @click="exportExcel">
          <i class="icon-font icon-daochu delete-icon"></i>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
        <Button type="primary" class="ml-sm button-blue" :loading="exportDataLoading" @click="deleteBulk">
          <i class="icon-font icon-shanchu mr-sm"></i>
          <span class="inline vt-middle ml-xs">删除</span>
        </Button>
      </div>
    </div>
    <div class="content auto-fill">
      <div class="content-left auto-fill">
        <Checkbox v-if="activeStatus === '0'" v-model="isLeftAll" :disabled="false" @on-change="changeIsLeftAll">
          <span class="ml-xs">全选</span>
        </Checkbox>
        <ui-table
          class="ui-table auto-fill mt-sm"
          ref="leftTableRef"
          reserveSelection
          :key="activeStatus"
          :default-store-data="defaultStoreData"
          :is-all="isLeftAll"
          :loading="leftLoading"
          :table-columns="leftTableColumns"
          :table-data="tableDataLeftList"
          rowKey="deviceId"
          @storeSelectList="storeSelectList"
        >
        </ui-table>
      </div>
      <div class="content-right auto-fill">
        <Checkbox v-if="activeStatus === '0'" v-model="isRightAll" :disabled="false" @on-change="changeIsRightAll">
          <span class="ml-xs">全选</span>
        </Checkbox>
        <ui-table
          class="ui-table auto-fill mt-sm"
          reserveSelection
          ref="rightTableRef"
          :default-store-data="defaultRightStoreData"
          :is-all="isRightAll"
          :table-columns="rightTableColumns"
          :table-data="tableDataRightList"
          :loading="rightLoading"
          rowKey="deviceId"
          @storeSelectList="storeRightSelectList"
        >
        </ui-table>
      </div>
    </div>
    <div class="page-wrapper">
      <ui-page class="page" :page-data="pageBaseData" @changePage="changeBasePage" @changePageSize="changeBasePageSize">
      </ui-page>
      <ui-page
        class="page"
        v-if="activeStatus === '0'"
        :page-data="pageRightData"
        @changePage="changeRightPage"
        @changePageSize="changeRightPageSize"
      >
      </ui-page>
    </div>
    <AsyncModal
      v-model="asyncModalShow"
      :description="description"
      :isDisabled="isDisabled"
      :chooseListLength="chooseListLength"
      :params="asyncModalParams"
      @confirmAsync="updateList"
    ></AsyncModal>
    <!-- <equipment-Info-comparison
      ref="info-comparison"
      :title="'设备信息比对'"
      :comparison-fields="comparisonFields"
      :table-data-left="tableDataLeft"
      :table-data-right="tableDatableRight"
      :resultData="resultData"
      @handleResetTableData="getComparisonResultList"
    ></equipment-Info-comparison> -->
  </div>
</template>

<script>
import { leftTableColumns, rightTableColumns, statisticalQuantityList, statisticalRightList } from './enum';
import viewassets from '@/config/api/viewassets';

export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AsyncModal: require('./async-modal.vue').default,
    // EquipmentInfoComparison: require('./components/equipment-info-comparison.vue')
    //   .default,
  },
  data() {
    return {
      asyncModalShow: false,
      statusList: Object.freeze([
        { name: '相同', status: '1' },
        { name: '差异', status: '2' },
        { name: '独有', status: '0' },
      ]),
      statisticalLeftList: statisticalQuantityList,
      statisticalRightList: statisticalRightList,
      activeStatus: '1',
      tableDataLeftList: [],
      tableDataRightList: [],
      defaultStoreData: [], // 存储勾选的设备
      defaultRightStoreData: [],
      leftTableColumns: Object.freeze(leftTableColumns),
      rightTableColumns: Object.freeze(rightTableColumns),
      rightLoading: false,
      leftLoading: false,
      pageRightData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      pageBaseData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      isAll: false,
      isLeftAll: false,
      isRightAll: false,
      exportDataLoading: false,
      isDisabled: false,
      chooseListLength: 0,
      asyncModalParams: {},
    };
  },
  computed: {
    description() {
      // 独有设备
      let isAlone = this.activeStatus === '0';
      if (!isAlone) {
        this.isDisabled = false;
        this.isAll
          ? (this.chooseListLength = this.pageBaseData.totalCount)
          : (this.chooseListLength = this.defaultStoreData.length);
        this.asyncModalParams = {
          type: 0, // 差异
          ids: this.defaultStoreData.map((item) => item.id),
        };
        return '已选择';
      } else {
        // 是独有的设备
        let leftLength = this.defaultStoreData.length;
        let rightLength = this.defaultRightStoreData.length;
        // 只能资产到上报
        if ((!rightLength && !!leftLength) || this.isLeftAll) {
          this.isDisabled = true;
          this.isLeftAll
            ? (this.chooseListLength = this.pageBaseData.totalCount)
            : (this.chooseListLength = this.defaultStoreData.length);
          this.asyncModalParams = {
            type: 1, // 独有
            ids: this.defaultStoreData.map((item) => item.id),
          };
          return '设备资产库独有设备';
        }
      }
      return '';
    },
  },
  watch: {},
  async activated() {
    this.getStatic();
    this.initList();
    this.$nextTick(() => {
      this.addScrollListener();
    });
  },
  async mounted() {},
  methods: {
    tabClick({ status }) {
      this.activeStatus = status;
      this.rightTableColumns = Object.freeze(rightTableColumns);
      this.resetAllData();
      // 独有设备
      if (status === '0') {
        this.rightTableColumns = Object.freeze([
          {
            type: 'selection',
            width: 50,
            align: 'center',
          },
          ...rightTableColumns,
        ]);
        this.$refs.rightTableRef.$forceUpdate();
        this.getAloneAll();
        this.removeScrollListener();
      } else {
        this.$refs.rightTableRef.$forceUpdate();
        this.initList();
        this.$nextTick(() => {
          this.addScrollListener();
        });
      }
    },
    changeIsLeftAll(val) {
      this.tableDataLeftList.forEach((item) => {
        this.$nextTick(() => {
          this.$set(item, '_checked', val);
        });
        this.$set(item, '_disabled', val);
      });
      this.isRightAll = false;
      this.tableDataRightList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
    },
    changeIsRightAll(val) {
      this.tableDataRightList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
      this.isLeftAll = false;
      this.tableDataLeftList.forEach((item) => {
        this.$nextTick(() => {
          this.$set(item, '_checked', false);
        });
        this.$set(item, '_disabled', false);
      });
    },
    updateList() {
      this.getStatic();
      this.tabClick({ status: this.activeStatus });
    },
    async getStatic() {
      try {
        let { data } = await this.$http.get(viewassets.getStat);
        let statics = data.data;
        this.statisticalLeftList.forEach((item) => {
          item.key in statics ? (item.count = statics[item.key]) : (item.count = 0);
        });
        this.statisticalRightList.forEach((item) => {
          item.key in statics ? (item.count = statics[item.key]) : (item.count = 0);
        });
      } catch (err) {
        console.log(err);
      }
    },
    async initList() {
      this.leftLoading = true;
      this.rightLoading = true;
      try {
        let { data } = await this.$http.post(viewassets.getLastComparison, {
          type: this.activeStatus,
          pageSize: this.pageBaseData.pageSize,
          pageNumber: this.pageBaseData.pageNum,
        });
        this.tableDataLeftList = data.data.entities.map((item) => {
          if (!!item.columnList && !!item.columnList.length) {
            item.deviceInfoVo.cellClassName = {};
            item.columnList.forEach((one) => {
              item.deviceInfoVo.cellClassName[one] = 'active-warning-cell';
            });
          }
          return item.deviceInfoVo;
        });
        this.tableDataRightList = data.data.entities.map((item) => {
          if (!!item.columnList && !!item.columnList.length) {
            item.reportVo.cellClassName = {};
            item.columnList.forEach((one) => {
              item.reportVo.cellClassName[one] = 'active-warning-cell';
            });
          }
          return item.reportVo;
        });
        this.pageBaseData.totalCount = data.data.total;
        this.pageRightData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.leftLoading = false;
        this.rightLoading = false;
      }
    },
    handlerDifferCellName(differArray) {
      /**------标识差异字段，给差异字段添加类名标识----**/
      let differObject = {};
      differArray.forEach((item) => {
        item.asycId in differObject ? null : (differObject[item.asycId] = {});
        differObject[item.asycId][item.fieldName] = 'active-warning-cell';
      });
      this.baseTableData.forEach((item) => {
        item.cellClassName = differObject[item.id];
      });
    },
    async getAloneAll(defaultStragety) {
      // 1 资产库 2- 上报库
      let stragety = [
        {
          flag: 1,
          pageFiled: 'pageBaseData',
          loadingName: 'leftLoading',
          tableName: 'tableDataLeftList',
        },
        {
          flag: 2,
          pageFiled: 'pageRightData',
          loadingName: 'rightLoading',
          tableName: 'tableDataRightList',
        },
      ];
      if (defaultStragety) stragety = defaultStragety;
      stragety.forEach((item) => {
        this[item.loadingName] = true;
        this.initAloneList({
          flag: item.flag,
          pageSize: this[item.pageFiled].pageSize,
          pageNumber: this[item.pageFiled].pageNum,
        })
          .then((data) => {
            this[item.tableName] = data.entities;
            this[item.pageFiled].totalCount = data.total;
          })
          .finally(() => {
            this[item.loadingName] = false;
          });
      });
    },
    async initAloneList(params) {
      try {
        let { data } = await this.$http.post(viewassets.getLastComparisonAlone, {
          type: this.activeStatus,
          ...params,
        });
        return data.data;
      } catch (err) {
        console.log(err);
      }
    },
    resetAllData() {
      this.defaultStoreData = [];
      this.defaultRightStoreData = [];
      this.tableDataLeftList = [];
      this.tableDataRightList = [];
      this.isAll = false;
      this.isLeftAll = false;
      this.isRightAll = false;
      this.pageRightData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.pageBaseData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
    },
    storeSelectList(selectData) {
      this.defaultStoreData = selectData; // 存储勾选的设备
      if (this.status !== '0') return;
      this.tableDataRightList.forEach((item) => {
        this.$set(item, '_checked', false);
      });
    },
    storeRightSelectList(selectData) {
      this.defaultRightStoreData = selectData; // 存储勾选的设备
      this.tableDataLeftList.forEach((item) => {
        this.$set(item, '_checked', false);
      });
    },
    changeBasePage(val) {
      this.pageBaseData.pageNum = val;
      // 独有设备
      if (this.activeStatus === '0') {
        // 独有资产库
        this.getAloneAll([
          {
            flag: 1,
            pageFiled: 'pageBaseData',
            loadingName: 'leftLoading',
            tableName: 'tableDataLeftList',
          },
        ]);
      } else {
        this.initList();
      }
    },
    changeBasePageSize(val) {
      this.pageBaseData.pageNum = 1;
      this.pageBaseData.pageSize = val;
      // 独有设备
      if (this.activeStatus === '0') {
        // 独有资产库
        this.getAloneAll([
          {
            flag: 1,
            pageFiled: 'pageBaseData',
            loadingName: 'leftLoading',
            tableName: 'tableDataLeftList',
          },
        ]);
      } else {
        this.initList();
      }
    },
    changeRightPage(val) {
      this.pageRightData.pageNum = val;
      // 独有上报库
      this.getAloneAll([
        {
          flag: 2,
          pageFiled: 'pageRightData',
          loadingName: 'rightLoading',
          tableName: 'tableDataRightList',
        },
      ]);
    },
    changeRightPageSize(val) {
      this.pageRightData.pageNum = 1;
      this.pageRightData.pageSize = val;
      // 独有上报库
      this.getAloneAll([
        {
          flag: 2,
          pageFiled: 'pageRightData',
          loadingName: 'rightLoading',
          tableName: 'tableDataRightList',
        },
      ]);
    },
    updateSync() {
      // 独有的,并且没有全选任何，还没有单个选中
      let leftLength = this.defaultStoreData.length;
      let rightLength = this.defaultRightStoreData.length;
      let aloneNothing =
        this.activeStatus === '0' && !this.isLeftAll && !this.isRightAll && !leftLength && !rightLength;
      let nothing = this.activeStatus !== '0' && !this.isAll && !leftLength;
      if (aloneNothing || nothing) {
        this.$Message.warning('请选择同步设备');
        return;
      }
      if ((!!rightLength && !leftLength) || this.isRightAll) {
        this.$Message.warning('仅支持资产库同步到上报库');
        return;
      }
      this.asyncModalShow = true;
    },
    async exportExcel() {
      var params = {
        type: this.activeStatus,
      };
      // 独有
      if (this.activeStatus === '0') {
        if (
          !this.isLeftAll &&
          !this.isRightAll &&
          !this.defaultStoreData.length &&
          !this.defaultRightStoreData.length
        ) {
          this.$Message.warning('请选择设备进行导出');
        }
        // flag 1 资产库 2- 上报库
        this.isLeftAll ? (params.flag = 1) : null;
        this.isRightAll ? (params.flag = 2) : null;
        if (!!this.defaultStoreData.length && !this.isLeftAll) {
          params.ids = this.defaultStoreData.map((item) => item.id);
          params.flag = 1;
        }
        if (!!this.defaultRightStoreData.length && !this.isRightAll) {
          params.reportIds = this.defaultRightStoreData.map((item) => item.id);
          params.flag = 2;
        }
      } else {
        params.ids = this.defaultStoreData.map((item) => item.id);
      }
      try {
        let res = await this.$http.post(viewassets.syncDeviceExport, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    deleteItem(params) {
      this.$UiConfirm({
        content: '删除后设备将从上报库移除（不影响资产库），确定删除吗？',
        title: '删除设备',
      }).then(() => {
        this.$http.delete(viewassets.deleteReport, { data: params }).then(() => {
          this.$Message.success('删除成功');
          this.getStatic();
          this.initList();
        });
      });
    },
    deleteBulk() {
      // 独有设备 - 只能删除上报库
      if (this.activeStatus === '0') {
        if (!this.isRightAll && !this.defaultRightStoreData.length) {
          this.$Message.warning('请选择需要删除的上报库设备');
          return;
        }
        let params = {};
        params.isAll = this.isRightAll;
        !this.isRightAll ? (params.ids = this.defaultRightStoreData.map((item) => item.id)) : null;
        this.deleteItem(params);
      } else {
        if (!this.isAll && !this.defaultStoreData.length) {
          this.$Message.warning('请选择需要删除的设备');
          return;
        }
        let params = {};
        params.isAll = this.isAll;
        !this.isAll ? (params.ids = this.defaultStoreData.map((item) => item.id)) : null;
        this.deleteItem(params);
      }
    },
    handLeftScroll() {
      this.$refs.rightTableRef.$refs.table.$refs.body.scrollTop =
        this.$refs.leftTableRef.$refs.table.$refs.body.scrollTop;
      this.$refs.rightTableRef.$refs.table.$refs.body.scrollLeft =
        this.$refs.leftTableRef.$refs.table.$refs.body.scrollLeft;
    },
    handRightScroll() {
      this.$refs.leftTableRef.$refs.table.$refs.body.scrollTop =
        this.$refs.rightTableRef.$refs.table.$refs.body.scrollTop;
      this.$refs.leftTableRef.$refs.table.$refs.body.scrollLeft =
        this.$refs.rightTableRef.$refs.table.$refs.body.scrollLeft;
    },
    addScrollListener() {
      this.$refs.rightTableRef.$refs.table.$refs.body.addEventListener('scroll', this.handRightScroll);
      this.$refs.leftTableRef.$refs.table.$refs.body.addEventListener('scroll', this.handLeftScroll);
    },
    removeScrollListener() {
      this.$refs.rightTableRef.$refs.table.$refs.body.removeEventListener('scroll', this.handRightScroll);
      this.$refs.leftTableRef.$refs.table.$refs.body.removeEventListener('scroll', this.handLeftScroll);
    },
  },
};
</script>

<style lang="less" scoped>
.asset-comparison-wrap {
  height: 100%;
  padding: 0 20px 20px;
  @{_deep}.active-warning-cell {
    color: rgb(188, 60, 25);
  }
  .header {
    display: flex;
    > div {
      width: 50%;
      padding-bottom: 10px;
      .title {
        height: 53px;
        line-height: 53px;
        font-size: 16px;
        color: var(--color-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        & > span {
          margin-right: 62px;
          i {
            margin-right: 4px;
          }
        }
      }
      .content {
        height: 110px;
        display: flex;
        align-items: center;
        width: 100%;
        background: var(--bg-navigation);
        .content-item {
          width: 25%;
          display: flex;
          align-items: center;
          position: relative;
          .icon-font {
            font-size: 37px;
            margin-left: 30px;
            margin-right: 10px;
          }
          .name {
            font-size: 14px;
            color: var(--color-content);
          }
          .count {
            font-size: 18px;
          }
          &:first-child::after {
            content: '';
            width: 0px;
          }
        }
        .content-item::after {
          content: '';
          width: 1px;
          height: 40px;
          position: absolute;
          top: 10px;
          left: 0;
          background: #1568ad;
        }
      }
    }
    .header-left {
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }
    .header-right {
      padding-left: 10px;
    }
  }
  .tab-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    background: var(--bg-navigation);
    padding: 10px;
    margin-bottom: 10px;
    .ivu-tabs-tab {
      float: left;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border: 1px solid var(--border-btn-dashed);
      border-right: none;
      padding: 0 22px;
      color: var(--color-btn-dashed);
      &:hover {
        background: var(--color-primary);
        color: #fff;
        cursor: pointer;
      }
      &:last-child {
        border-right: 1px solid var(--border-btn-dashed);
      }
    }
    .active {
      background: var(--color-primary);
      color: #fff;
    }
    .tab-line-right {
      display: flex;
      align-items: center;
    }
  }
  /deep/ .content {
    display: flex;
    flex-direction: row;
    .content-left {
      width: 50%;
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }
    .content-right {
      width: 50%;
      padding-left: 10px;
    }
    .content-left,
    .content-right {
      .ui-table {
        .ivu-table-column-left {
          padding-left: 10px;
        }
      }
    }
    .ivu-table-cell-ellipsis {
      .ivu-table-cell-slot {
        > span {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .footer {
    display: flex;
    .page-right,
    .page-left {
      width: 50%;
      padding: 20px 8px;
    }
  }
  .page-wrapper {
    display: flex;
    .page {
      flex: 1;
    }
  }
}
</style>
