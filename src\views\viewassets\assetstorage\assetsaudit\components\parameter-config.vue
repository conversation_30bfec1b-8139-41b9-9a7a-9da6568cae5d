<template>
  <ui-modal v-model="visible" title="参数配置" width="62.5rem">
    <instorage-config ref="InstorageConfig" :property-list="propertyList">
      <template #custorm-content>
        <section class="section-box">
          <p class="mb-md">
            1、设备是否自动审核入库（资产库）:
            <RadioGroup v-model="searchParams.isStorage">
              <Radio label="1">是</Radio>
              <Radio label="0">否</Radio>
            </RadioGroup>
          </p>
          <!-- 自动入库才展示 -->
          <p class="mb-md d_flex ml-sp" v-if="searchParams.isStorage === '1'">
            自动入库（资产库）条件：
            <CheckboxGroup class="align-flex wrap" v-model="searchParams.storageCondition">
              <Checkbox
                v-for="(item, index) in propertyFields"
                :key="index"
                class="align-flex flex-4"
                :label="item.value"
              >
                {{ item.label }}
              </Checkbox>
            </CheckboxGroup>
          </p>
        </section>
        <section class="section-box">
          2、相同设备入库策略：
          <p class="mt-md mb-md ml-sp">
            待入库设备的字段为空时：
            <RadioGroup class="ml-md" v-model="searchParams.fieldNull">
              <Radio label="0">覆盖资产库值</Radio>
              <Radio label="1">保留资产库值</Radio>
            </RadioGroup>
          </p>
          <p class="mb-md ml-sp">
            待入库设备的字段不为空时：
            <RadioGroup v-model="searchParams.fieldNotNull">
              <Radio label="0">覆盖资产库值</Radio>
              <Radio label="1">保留资产库值</Radio>
            </RadioGroup>
          </p>
        </section>
      </template>
    </instorage-config>
    <template #footer>
      <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
      <Button class="plr-30" type="primary" @click="saveQuery">保存</Button>
    </template>
  </ui-modal>
</template>

<script>
import taganalysis from '@/config/api/taganalysis';
import assetsSync from '@/config/api/assetsSync';
export default {
  name: 'parameter-config',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      configType: '1',
      searchParams: {
        storageParam: {},
        isStorage: '1',
        fieldNull: '1',
        fieldNotNull: '0',
        storageCondition: ['0', '1', '2'],
      },
      propertyList: [],
      defaultId: '',
      defaultStorageParam: [],
      propertyFields: Object.freeze([
        { label: '未检测设备', value: '0' },
        { label: '检测通过设备', value: '1' },
        { label: '检测不通过设备', value: '2' },
      ]),
      defaultSelectedList: [],
      defaultSelectKey: {
        deviceId: {
          addType: 1, // 覆盖
          _checked: true, // 选中复选框
          disabled: true, // 入库策略-下拉框禁用
          _disabled: true, // // 复选框不可取消选中
        },
        sourceId: {
          addType: 2, // 追加
          _checked: true,
          disabled: false,
          _disabled: false,
        },
      },
    };
  },
  methods: {
    async queryByConfigType() {
      try {
        let {
          data: { data },
        } = await this.$http.get(assetsSync.queryByConfigType, { params: { configType: this.configType } });
        this.searchParams.isStorage = data.isStorage;
        this.searchParams.fieldNull = data.fieldNull;
        this.searchParams.fieldNotNull = data.fieldNotNull;
        this.searchParams.storageCondition = data.storageCondition ? data.storageCondition.split(',') : [];
        this.defaultId = data.id;
        this.defaultStorageParam = data.storageParam ? JSON.parse(data.storageParam) : [];
      } catch (err) {
        console.log(err);
      }
    },
    // 查询全部字段
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1', // 视图
        });
        this.defaultSelectedList = [];
        this.propertyList = data.data.filter((item) => {
          if (item.propertyName === 'id') {
            return false;
          }
          if (Object.keys(this.defaultSelectKey).includes(item.propertyName)) {
            item.addType = this.defaultSelectKey[item.propertyName].addType;
            item._checked = this.defaultSelectKey[item.propertyName]._checked;
            item.disabled = this.defaultSelectKey[item.propertyName].disabled;
            item._disabled = this.defaultSelectKey[item.propertyName]._disabled;
            return item;
          }
          this.defaultStorageParam.forEach((storageItem) => {
            if (item.propertyName === storageItem.fieldName) {
              item.addType = storageItem.addType ? storageItem.addType : 1;
              item._checked = true;
            }
          });
          return item;
        });
      } catch (err) {
        console.log(err);
      }
    },
    async saveQuery() {
      try {
        const targetList = this.$refs.InstorageConfig.targetList.concat(this.defaultSelectedList);
        const storageParams = targetList.map((item) => {
          const obj = {
            addType: item.addType,
            fieldName: item.checkColumnName,
            fieldRemark: item.checkColumnValue,
          };
          return obj;
        });
        this.searchParams.storageParam = JSON.stringify(storageParams);
        let params = {
          configType: this.configType,
          id: this.defaultId || null,
          ...this.searchParams,
        };
        params.storageCondition = this.searchParams.storageCondition.length
          ? this.searchParams.storageCondition.join(',')
          : '';
        await this.$http.post(assetsSync.updateConfig, params);
        this.$Message.success('配置成功');
        this.cancel();
      } catch (err) {
        console.log(err);
      }
    },
    cancel() {
      this.visible = false;
      this.searchParams = {};
      this.propertyList = [];
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      if (val) {
        await this.queryByConfigType();
        this.getPropertyList();
      }
      this.visible = val;
    },
  },
  components: {
    InstorageConfig: require('./instorage-config.vue').default,
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 16px 20px;
}
</style>
