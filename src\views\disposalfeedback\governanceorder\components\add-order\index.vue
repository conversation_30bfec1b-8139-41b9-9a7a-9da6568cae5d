<template>
  <ui-modal
    v-model="visible"
    title="新建工单"
    :loading="loading"
    @onCancel="reset"
    @query="addWorkOrder"
    :styles="styles"
  >
    <search-top
      class="search-top"
      ref="searchTopRef"
      :edit-view-action="editViewAction"
      @changeIndexId="changeIndexId"
      @on-change-task="handleChangeTask"
      @on-change-index="handleChangeIndex"
      @changeErrorReason="changeErrorReason"
      @radioChange="radioChange"
    ></search-top>
    <table-module
      v-if="this.tableShow"
      :search-table="searchTable"
      ref="tableRef"
      @getEvaForm="getEvaForm"
    ></table-module>
  </ui-modal>
</template>
<script>
import governancetask from '@/config/api/governancetask';

export default {
  data() {
    return {
      loading: false,
      tableShow: true,
      visible: false,
      styles: {
        minWidth: '9.2rem',
      },
      searchTable: {
        indexId: '',
        reasons: [],
      },
      evaForm: {},
      errorList: [],
    };
  },
  mounted() {},
  methods: {
    radioChange(boolean) {
      this.tableShow = boolean;
      boolean ? (this.styles.minWidth = '9.2rem') : (this.styles.minWidth = '7.2rem');
    },
    handleChangeTask() {
      this.$refs.tableRef.clearRightSelectTableData();
      this.$refs.tableRef.clearLeftTableData();
    },
    handleChangeIndex() {
      this.$refs.tableRef.clearRightSelectTableData();
      this.$refs.tableRef.clearLeftTableData();
    },
    async changeIndexId(item) {
      this.evaForm = {};
      this.searchTable = {
        indexId: item.indexId,
        batchId: item.batchId,
        orgRegionCode: item.orgRegionCode,
      };
      if (item) {
        await this.$nextTick();
        this.$refs.tableRef.init();
      }
    },
    changeErrorReason(reasons, errorList) {
      this.searchTable.reasons = reasons;
      this.errorList = errorList;
      //切换异常原因也筛选
      this.$refs.tableRef.init();
    },
    addWorkOrder() {
      let searchForm = this.$refs.searchTopRef.searchForm;
      // 关联检测任务
      if (searchForm.workOrderType === 1) {
        this.$refs.searchTopRef.handleSubmit().then((valid) => {
          let params = this.handleTaskParams();
          if (!params.workLevel) return this.$Message.error('紧急程度不能为空');
          let allRightData = this.$refs.tableRef.allRightData;
          if (!this.evaForm.isCheckAll && !allRightData.length) {
            this.$Message.warning('请选择需要关联的设备！');
            return;
          }
          if (valid) {
            this.addDeviceInfoMeta(params);
          }
        });
      } else {
        // 关联自定义问题
        if (!searchForm.workOrderName) {
          this.$Message.error('请填写工单名称');
          return;
        }
        let params = {
          workOrderName: searchForm.workOrderName,
          taskPlannedDate: searchForm.taskPlannedDate,
          workOrderType: searchForm.workOrderType,
          taskContent: searchForm.taskContent,
          receiverName: searchForm.receiverName,
          receiverId: searchForm.receiverId,
          fileUrl: searchForm.fileUrl,
          workLevel: searchForm.workLevel,
        };
        this.addDeviceInfoMeta(params);
      }
    },
    handleTaskParams() {
      let params = {};
      let searchForm = this.$refs.searchTopRef.searchForm;
      let allRightData = this.$refs.tableRef.allRightData;
      params = {
        ...searchForm,
        deviceIds: allRightData.map((item) => item.deviceId),
      };
      // 后端强加的操作，查询列表用中文查，保存工单异常用英文存
      let errorlistObject = {};
      this.errorList.forEach((ele) => {
        errorlistObject[ele.dicKey] = ele;
      });
      params.reasons = searchForm.reasons.map((item) => {
        if (!this.isChinese(item)) {
          return errorlistObject[item].dicValue;
        } else {
          return item;
        }
      });
      delete params.workOrderName;
      delete params.workOrderNum;
      // 全选特殊处理
      if (this.evaForm.isCheckAll) {
        params.evaForm = Object.assign({}, this.evaForm);
        params.isCheckAll = params.evaForm.isCheckAll ? '1' : '0';
        delete params.evaForm.pageNumber;
        delete params.evaForm.pageSize;
        params.evaForm.displayType = 'REGION';
      }
      params.indexName ? (params.indexName = JSON.parse(params.indexName).indexName) : null;
      console.log(params, 'params');
      return params;
    },
    isChinese(s) {
      if (escape(s).indexOf('%u') < 0) {
        return false;
      } else {
        return true;
      }
    },
    async addDeviceInfoMeta(params) {
      this.loading = true;
      let interfaceName = params.workOrderType === 1 ? 'batchAddOrder' : 'saveCustomTask';
      try {
        let { data } = await this.$http.post(governancetask[interfaceName], params);
        let { success = 0, failure = 0 } = data.data || {};
        this.$Message.success({
          content:
            interfaceName === 'batchAddOrder'
              ? `共创建${Number.parseInt(success) + Number.parseInt(failure)}条工单，
          成功指派${success}条工单！${
                  failure
                    ? `有${failure}条工单无法匹配 ${
                        params.assignMode === 1 ? '维护单位联系人' : '对应单位的工单接收人'
                      }，无法指派！请在工单列表页面手动指派！`
                    : ''
                } `
              : data.msg,
          duration: 5,
        });
        this.$emit('updateOrders');
        this.reset();
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    getEvaForm(params) {
      Object.assign(this.evaForm, params);
    },
    reset() {
      this.visible = false;
      this.$refs.searchTopRef.reset();
      this.tableShow = false;
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        // 初始化表格数据
        this.searchTable = {
          indexId: '',
          reasons: [],
        };
        val ? (this.tableShow = true) : (this.tableShow = false);
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {},
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    editViewAction: {
      default: () => {
        return {
          type: '',
          row: {},
        };
      },
    },
  },
  components: {
    SearchTop: require('./search-top.vue').default,
    TableModule: require('./table-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding-bottom: 0;
}
</style>
