<template>
  <div class="search card-border-color">
    <Form
      :inline="true"
      :class="visible ? 'advanced-search-show' : ''"
      @submit.native.prevent
    >
      <!-- 关键字搜索 -->
      <div class="general-search">
        <div class="input-content">
          <div class="upload-input-list">
            <uiUploadImg
              :algorithmType="1"
              ref="uploadImgRef"
              @change="imgUrlChange"
              size="small"
            />
          </div>
          <div class="other-search">
            <div class="other-search-top card-border-color">
              <FormItem label="相似度:" class="slider-form-item">
                <div class="slider-content">
                  <i
                    class="iconfont icon-jian add-subtract"
                    @click="addAndSubtract(false)"
                  ></i>
                  <Slider v-model="queryParam.similarity"></Slider>
                  <i
                    class="iconfont icon-jia add-subtract"
                    @click="addAndSubtract(true)"
                  ></i>
                  <span>{{ queryParam.similarity }}%</span>
                </div>
              </FormItem>
              <!-- 算法2个以上（含）才可选择 -->
              <FormItem
                label="算法选择:"
                prop="algorithm"
                v-if="algorithmTypeList.length > 1"
              >
                <CheckboxGroup
                  v-if="queryParam.features.length > 0"
                  v-model="queryParam.algorithmSelect"
                  @on-change="handleChangeAlgor"
                >
                  <Checkbox
                    :label="item.dataKey"
                    v-for="(item, index) in algorithmTypeList"
                    :key="index"
                  >
                    <span :class="[index === 0 ? 'gerling' : 'hk']">{{
                      item.dataValue
                    }}</span>
                  </Checkbox>
                </CheckboxGroup>
                <RadioGroup v-else v-model="queryParam.algorithmVendorType">
                  <Radio
                    :label="item.dataKey"
                    v-for="(item, index) in algorithmTypeList"
                    :key="index"
                  >
                    <span :class="[index === 0 ? 'gerling' : 'hk']">{{
                      item.dataValue
                    }}</span>
                  </Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="身份证:" prop="idCardNo">
                <Input
                  v-model="queryParam.idCardNo"
                  placeholder="请输入身份证号"
                ></Input>
              </FormItem>
              <FormItem label="姓 名:" prop="name">
                <Input
                  v-model="queryParam.name"
                  placeholder="请输入姓名"
                ></Input>
              </FormItem>
            </div>
            <div class="other-search-bottom">
              <div class="flex">
                <FormItem label="设备资源:">
                  <div class="select-tag-button" @click="selectDevice()">
                    选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                  </div>
                </FormItem>
                <FormItem label="抓拍时段:">
                  <ui-quick-date
                    ref="quickDateRef"
                    v-model="dateType"
                    @change="changeTime"
                  ></ui-quick-date>
                </FormItem>
                <FormItem label="视频身份:">
                  <Input
                    v-model="queryParam.videoIdentity"
                    placeholder="请输入"
                  ></Input>
                </FormItem>
              </div>
              <div class="btn-group">
                <!-- v-if="queryParam.features.length < 1" -->
                <span
                  class="advanced-search-text primary"
                  @click.stop="visible = !visible"
                >
                  更多条件 <i class="iconfont icon-jiantou"></i>
                </span>
                <Button type="primary" @click="searchHandle">查询</Button>
                <Button @click="resetHandle">重置</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--更多搜索条件-->
      <div class="advanced-search" @click.stop>
        <template>
          <div class="advanced-search-item card-border-color">
            <FormItem label="数据来源:">
              <dataSource sectionName="faceContent" />
            </FormItem>
          </div>
          <div class="advanced-search-item card-border-color">
            <Row>
              <Col span="6">
                <FormItem label="眼       镜:">
                  <ui-tag-select
                    ref="glasses"
                    @input="
                      (e) => {
                        input(e, 'glasses');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureGlasses"
                      :key="$index"
                      :name="item.dataKey"
                      :label="item.dataValue"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="性       别:">
                  <ui-tag-select
                    ref="gender"
                    @input="
                      (e) => {
                        input(e, 'gender');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureGender"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="advanced-search-item card-border-color">
            <Row>
              <Col span="6">
                <FormItem label="帽       子:">
                  <ui-tag-select
                    ref="cap"
                    @input="
                      (e) => {
                        input(e, 'cap');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureCap"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="口       罩:">
                  <ui-tag-select
                    ref="faceMask"
                    @input="
                      (e) => {
                        input(e, 'faceMask');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureMask"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="advanced-search-item">
            <Row>
              <Col span="6">
                <FormItem label="年  龄   段:">
                  <ui-tag-select
                    ref="age"
                    @input="
                      (e) => {
                        input(e, 'age');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureAge"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
            </Row>
          </div>
        </template>
      </div>
      <!-- 选择设备 -->
      <select-device
        ref="selectDevice"
        showOrganization
        @selectData="selectData"
        :isCheckShow="true"
      />
    </Form>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import uiUploadImg from "@/components/ui-upload-image/index";
import LabelModal from "@/views/holographic-archives/components/relationship-map/label-add";
import {
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import dayjs from "dayjs";
import dataSource from "../../components/data-source.vue";

export default {
  components: {
    LabelModal,
    uiUploadImg,
    dataSource,
  },
  props: {
    // 档案资料抓拍弹框
    searchFields: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      visible: false, // 更多条件显示隐藏
      dateType: 1, // 抓拍时段类型
      queryParam: {
        dataSource: 2, // 数据资源，必传，当前版本固定为2，1-静态库，2-抓拍库
        name: "", // 姓名
        idCardNo: "", // 身份证
        gender: "", // 性别
        age: "", // 年龄
        cap: "", // 帽子
        glasses: "", // 眼镜
        faceMask: "", // 口罩
        selectDeviceList: [], // 选中的设备全量 - 手动记录日志需要该信息
        startDate: dayjs().format("YYYY-MM-DD 00:00:00"), // 抓拍时段 - 开始时间
        endDate: dayjs().format("YYYY-MM-DD 23:59:59"), // 抓拍时段 - 结束时间
        similarity: 75, // 相似度
        algorithmVendorType: "", // 单选 - 选中的算法
        algorithmSelect: [], // 多选 - 选中的算法，当以图搜图时，算法为多选
        features: [], // 图片信息
        imageBases: [], // 图片信息
        videoIdentity: "", // 视频身份
        deviceIds: [], // 选中的设备id
      },
    };
  },
  async activated() {
    //#region 系统配置参数 - 页面刷新时
    if (!this.globalObj.searchForPicturesDefaultSimilarity) {
      await this.getDictData();
    }
    this.queryParam.similarity = Number(
      this.globalObj.searchForPicturesDefaultSimilarity
    );
    //#endregion

    //#region 路由参数处理
    let query = this.$route.query;
    /**
     * 走到了mounted生命周期中，并满足该条件，说明是点击左侧菜单触发的，此时不需要处理路由携带的参数
     * 否则，则表示直接从别的页面跳到该页面，并直接定位到当前组件
     */
    if (query.sectionName !== "faceContent") {
      let { list, timeSlot, startDate, endDate, searchSelect } =
        this.classifySearchData;
      if (searchSelect == 1) {
        this.queryParam.selectDeviceList = list;
        this.dateType = timeSlot;
        this.$refs.quickDateRef.handleInit(timeSlot, startDate, endDate);
      }
      this.$nextTick(() => {
        console.log(this.queryParam, "this.queryParam");
        // 需要等时间更新后发起查询
        this.$emit("search");
      });
      return;
    }
    // 选中的设备
    if (query.deviceList) {
      let deviceList = JSON.parse(query.deviceList);
      let list = deviceList.map((item) => {
        return { ...item, deviceId: item.deviceGbId, select: true };
      });
      this.selectData(list);
    }
    // 关键字 - from全景智搜
    if (query.keyWords) {
      this.queryParam.searchValue = query.keyWords;
    }
    if (query.dateType) {
      this.dateType = Number(query.dateType);
      if ([2, 3].includes(this.dateType)) {
        this.$nextTick(() => {
          this.changeTime(this.$refs.quickDateRef.getDate());
        });
      }
      // 自定义时间，当为自定义时间时，query必定携带dateRange起止时间
      if (this.dateType === 4) {
        let { startDate, endDate } = query;
        this.changeTime({ startDate, endDate });
        this.$nextTick(() => {
          this.$refs.quickDateRef.setDefaultDate([startDate, endDate]);
        });
      }
    }
    if (query.deviceInfo) {
      let deviceInfo = JSON.parse(query.deviceInfo);
      deviceInfo.deviceId = deviceInfo.deviceGbId || deviceInfo.deviceId;
      deviceInfo.deviceGbId = deviceInfo.deviceGbId || deviceInfo.deviceId;
      this.selectData([{ ...deviceInfo, select: true }]);
    }
    if (query.urlList) {
      // 图片结构化跳转
      let list = JSON.parse(query.urlList);
      this.urlImgList([list, ""]);
    }
    // TODO panorama 目前没看到哪里有传入，后续再验证，没有就去掉
    if (query.panorama) {
      let list = this.picData.urlList;
      this.urlImgList(list);
    }

    if (query.imgUrl) {
      let selectSquare = query.selectSquare
        ? { imageUrl:query.imgUrl, ...JSON.parse(query.selectSquare) }
        : null;
      if (!selectSquare) {
        let fileData = new FormData();
        fileData.append("algorithmType", 1);
        fileData.append("fileUrl", query.imgUrl);
        const res = await picturePick(fileData);
        selectSquare = res?.data?.[0];
      }


      if (selectSquare) {
        const response = await this.getBase64ByImageCoordinate(selectSquare);
        let urlList = {
          fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
          feature: response.data.feature,
          imageBase: response.data.imageBase,
        };
        this.urlImgList([urlList, ""]);
      } else {
        this.$Message.warning("未提取到特征，暂无查询结果");
      }
    }

    // 身份鉴别进入 搜索目标添加不发起查询
    if (query.work !== "add") {
      this.$nextTick(() => {
        // 需要等时间更新后发起查询
        this.$emit("search");
      });
    }
    //#endregion
  },
  async mounted() {
    window.addEventListener("click", this.hideMoreQuery);

    // 2024-07-29 新增一种情况，在详情打开弹框，不用route.query来判断了
    if (Object.keys(this.searchFields).length) {
      let fields = this.searchFields;
      // 视频身份，从档案-抓拍弹窗
      if (fields.videoIdentity) {
        this.queryParam.videoIdentity = fields.videoIdentity;
      }
      // 视图设备档案抓拍
      if (fields.deviceInfo) {
        let deviceInfo = JSON.parse(fields.deviceInfo);
        deviceInfo.deviceId = deviceInfo.deviceGbId || deviceInfo.deviceId;
        deviceInfo.deviceGbId = deviceInfo.deviceGbId || deviceInfo.deviceId;
        this.selectData([{ ...deviceInfo, select: true }]);
      }

      // 档案跳过来的都使用90天的时间
      this.dateType = 4;
      let date = [
        dayjs().subtract(89, "day").format("YYYY-MM-DD 00:00:00"),
        dayjs().format("YYYY-MM-DD 23:59:59"),
      ];
      this.changeTime({ startDate: date[0], endDate: date[1] });
      this.$nextTick(() => {
        this.$refs.quickDateRef.setDefaultDate(date);
      });
      this.$emit("search");
      return;
    }
  },
  beforeDestroy() {
    window.removeEventListener("click", this.hideMoreQuery);
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      ipbdFaceCaptureClustering: "dictionary/getIpbdFaceCaptureClustering", // 聚类
      ipbdFaceCaptureGender: "dictionary/getIpbdFaceCaptureGender", // 性别
      ipbdFaceCaptureGlasses: "dictionary/getIpbdFaceCaptureGlasses", // 眼镜
      ipbdFaceCaptureMask: "dictionary/getIpbdFaceCaptureMask", // 口罩
      ipbdFaceCaptureCap: "dictionary/getIpbdFaceCaptureCap", // 帽子
      picData: "common/getWisdomCloudSearchData",
      classifySearchData: "common/getClassifySearchData", //查询数据
    }),
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("common", ["setClassifySearchData"]),
    /**
     * @description: 隐藏更多条件框
     */
    hideMoreQuery() {
      this.visible = false;
    },

    /**
     * @description: 修改抓拍时段起始时间
     * @param {object} val 起始时间
     */
    changeTime(val) {
      this.queryParam.startDate = val.startDate;
      this.queryParam.endDate = val.endDate;
      let params = {
        list: this.queryParam.selectDeviceList,
        timeSlot: val.timeSlot || 1,
        startDate: val.startDate,
        endDate: val.endDate,
        searchSelect: 1,
      };
      this.setClassifySearchData(params);
    },

    /**
     * @description: 相似度调整
     * @param {boolean} flag 调整方向: true-加，false-减
     */
    addAndSubtract(flag = false) {
      if (flag) {
        this.queryParam.similarity++;
      } else {
        this.queryParam.similarity--;
      }
    },

    /**
     * @description: 算法选择，当上传了图片搜索后，算法变为多选框
     * @param {array} value 选中的算法值
     */
    handleChangeAlgor(value) {
      if (value.length == 0 && this.queryParam.features.length > 0) {
        this.queryParam.algorithmVendorType = this.algorithmTypeSelect[0];
        this.queryParam.algorithmSelect = [];
      } else {
        this.queryParam.algorithmVendorType = value[0];
      }
    },

    /**
     * @description: 搜索，手动触发，需要清空来自全景智搜的keyWords，如果有的话，重置视为主动触发查询
     */
    searchHandle() {
      this.hideMoreQuery();
      delete this.queryParam.searchValue;
      // 以图搜索下算法为多选，至少选择一个
      if (
        !this.queryParam.algorithmSelect.length &&
        this.queryParam.features.length
      ) {
        this.$Message.warning("至少选择一个算法！");
        return;
      }
      if (this.queryParam.algorithmSelect.length === 2) {
        // 调整算法选择的顺序，避免多算法展示错误
        this.queryParam.algorithmSelect = this.algorithmTypeSelect;
      }
      this.$emit("search");
    },

    /**
     * @description: 重置
     */
    resetHandle() {
      this.$refs.uploadImgRef.setDefaultUploadImage([""]);
      // 清空tag组件选中状态
      this.$refs.glasses.clearChecked();
      this.$refs.age.clearChecked();
      this.$refs.faceMask.clearChecked();
      this.$refs.cap.clearChecked();
      this.$refs.gender.clearChecked();

      this.queryParam.selectDeviceList = [];
      this.queryParam.features = [];
      this.queryParam.imageBases = [];
      this.queryParam.algorithmVendorType = this.algorithmTypeSelect[0];
      this.queryParam.algorithmSelect = [];
      this.queryParam.gender = "";
      this.queryParam.age = "";
      this.queryParam.cap = "";
      this.queryParam.glasses = "";
      this.queryParam.faceMask = "";
      this.queryParam.name = "";
      this.queryParam.idCardNo = "";
      this.queryParam.deviceIds = [];
      this.queryParam.similarity = Number(
        this.globalObj.searchForPicturesDefaultSimilarity
      );
      this.queryParam.videoIdentity = "";
      //#region 重置时间
      // 如果是自定义被重置，则需要清空已选择的时间
      if (this.dateType == 4) {
        this.$refs.quickDateRef.setDefaultDate();
      }
      this.dateType = 1;
      this.$nextTick(() => {
        this.changeTime(this.$refs.quickDateRef.getDate());
        this.searchHandle();
      });
      //#endregion
    },

    /**
     * @description: 切换tag
     * @param {string} e 当前选中的值
     * @param {string} key 当前类别
     */
    input(e, key) {
      this.queryParam[key] = e;
    },

    /**
     * @description: 打开设备弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList);
    },

    /**
     * @description: 选中设备
     * @param {array} list 选中的设备
     */
    selectData(list) {
      this.queryParam.selectDeviceList = list;
      this.queryParam.deviceIds = list.map((item) => item.deviceGbId);
    },

    /**
     * @description: 设置默认的上传的图片
     * @param {array} list 图片列表
     * @param {number} index 标识，1 - 直接跳到分类检索，从$router.query拿图片，2 - 在当前分类检索下进行搜索目标添加，需要与已上传的图片合并
     */
    urlImgList(list, index = 1) {
      let newList = [...list];
      if (index == 2) {
        let alreadyUploadImages = this.$refs.uploadImgRef.getUploadedImages();
        if (alreadyUploadImages.length < 5) {
          // 当已上传图片不足5张时
          newList = [...alreadyUploadImages, ...newList]
            .filter((v) => !!v)
            .concat([""]);
        } else if (
          alreadyUploadImages.length === 5 &&
          !alreadyUploadImages[alreadyUploadImages.length - 1]
        ) {
          // 当为5但是最后一个为上传的按钮操作时
          newList = [...alreadyUploadImages, ...newList].filter((v) => !!v);
        } else {
          this.$Message.warning("数量超出限制");
          return false; // 数量超出限制后不做处理
        }
      }
      this.$refs.uploadImgRef.setDefaultUploadImage(newList);
      this.imgUrlChange(newList);
    },

    /**
     * @description: 图片上传结果返回
     * @param {array} list 图片列表
     */
    imgUrlChange(list) {
      // 以图搜图字段
      let features = [];
      let imageBases = [];
      list.forEach((item) => {
        if (item) {
          item.feature && features.push(item.feature);
          item.imageBase && imageBases.push(item.imageBase);
        }
      });
      if (features.length) {
        this.queryParam.algorithmSelect = this.algorithmTypeSelect;
        this.queryParam.glasses = "";
        this.queryParam.gender = "";
        this.queryParam.cap = "";
        this.queryParam.faceMask = "";
        this.queryParam.age = "";
        // this.algorithmTypeSelect.length === 1 &&
        //   (this.queryParam.algorithmVendorType = this.algorithmTypeSelect?.[0]);
      } else {
        // this.queryParam.algorithmVendorType = this.algorithmTypeSelect[0];
        this.queryParam.algorithmSelect = [];
      }
      this.queryParam.algorithmVendorType = this.algorithmTypeSelect?.[0];
      this.queryParam.features = features;
      this.queryParam.imageBases = imageBases;
    },

    /**
     * @description: 获取查询参数，供父组件使用
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },

    /**
     * @description: 根据图片坐标截取图片base64
     * @param {object} data 图片信息
     * @return {promise} 获取图片base64
     */
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },

    /**
     * @description: 清空静态库搜索条件：身份证 | 姓名
     */
    clearStaticLibrarySearch() {
      this.queryParam.idCardNo = "";
      this.queryParam.name = "";
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index.less";
.btn-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .advanced-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 30px;
    font-size: 14px;

    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}

.search {
  padding: 10px 20px 0;
  border-bottom: 1px solid #ffff;
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;

  .ivu-form-inline {
    width: 100%;
  }

  .ivu-form-item {
    margin-bottom: 0;
    margin-right: 30px;
    display: flex;
    align-items: center;

    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
  }

  .general-search {
    display: flex;
    width: 100%;

    .input-content {
      flex: 1;
      display: flex;

      .upload-input-list {
        display: flex;
        max-width: 440px;
      }

      .other-search {
        display: flex;
        flex: 1;
        box-sizing: border-box;
        flex-direction: column;
        padding-left: 10px;

        .other-search-top {
          display: flex;
          border-bottom: 1px dashed #fff;
        }

        .ivu-form-item {
          display: flex;
          margin-bottom: 10px;

          /deep/ .ivu-form-item-label {
            padding-right: 10px;
          }
          .add-subtract {
            cursor: pointer;
          }
          .ivu-input-wrapper,
          .ivu-select {
            width: 200px;
          }
        }

        .other-search-bottom {
          display: flex;
          justify-content: space-between;
          padding-top: 10px;
          box-sizing: border-box;
          /deep/ .ivu-form-item-content {
            display: flex;
          }
          .slider-form-item {
            /deep/ .ivu-form-item-content {
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
    /deep/ .ivu-form-item-content {
      display: flex;
      align-items: center;
    }
  }

  .advanced-search {
    display: flex;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    z-index: 11;
    max-height: 0px;
    top: 100%;
    left: 0;
    transition: max-height 0.3s;
    overflow: auto;
    flex-direction: column;

    .advanced-search-item {
      &.justify-content-normal {
        justify-content: normal;
      }

      //   display: flex;
      //   justify-content: flex-start;
      // justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px dashed #fff;

      //   align-items: center;
      &:first-child {
        border-top: 1px solid;
      }

      .ivu-form-item {
        margin-right: 30px;

        &:last-child {
          margin-right: 0;
        }

        &.percent-70 {
          width: 70%;
        }

        display: flex;

        .text-radio-group {
          margin-left: -10px;
        }
      }

      .btn-group {
        flex: 1;
        justify-content: flex-end;
      }

      /deep/ .ivu-form-item-content {
        display: flex;
        align-items: center;
      }
    }
  }

  .advanced-search-show {
    .advanced-search {
      max-height: 400px;
      transition: max-height 0.7s;
    }

    .advanced-search-text {
      /deep/ .icon-jiantou {
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
}

.searchPading {
  padding: 10px 20px;
}
</style>
