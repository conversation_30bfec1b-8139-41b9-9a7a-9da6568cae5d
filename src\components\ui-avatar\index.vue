<template>
  <span class="avatar-container">
    <img v-if="src" :src="src" :style="styles" alt="" />
    <i v-else class="iconfontconfigure icon-gerenzhongxintouxiang"></i>
  </span>
</template>

<script>
export default {
  name: 'ui-avatar',
  components: {},
  props: {
    src: {
      type: String,
    },
  },
  data() {
    return {
      styles: {
        'object-fit': 'cover',
      },
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.avatar-container {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border: 1px solid var(--border-avatar);
  border-radius: 50%;
  background: var(--bg-avatar);
  overflow: hidden;
  img {
    height: 100%;
    width: 100%;
  }
  .icon-gerenzhongxintouxiang {
    color: var(--color-touxiang);
    font-size: 20px;
  }
}
</style>
