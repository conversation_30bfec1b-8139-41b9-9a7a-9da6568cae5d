<template>
  <div class="ui-time-line">
    <Timeline>
      <TimelineItem v-for="(item, $index) in data" :key="$index">
        <p class="time">{{ item[timeKey] }} </p>
        <p class="content">
          <slot :row="item"></slot>
        </p>
      </TimelineItem>
    </Timeline>
  </div>
</template>
<script>
  export default {
    props: {
      // 时间轴数据
      data: {
        type: Array,
        default: () => []
      },
      // 时间字段
      timeKey: {
        type: String,
        default: 'time'
      }
    },
    data () {
      return {

      }
    },
    created () {
    }
  }
</script>
<style lang="less" scoped>
  .ui-time-line {
    padding: 4px 0 0 7px;
    box-sizing: border-box;
    /deep/ .ivu-timeline {
      .ivu-timeline-item:first-child {
       .ivu-timeline-item-head::after {
         content: '';
         width: 20px;
         height: 20px;
         border-radius: 50%;
         border: 1px solid #2B84E2;
         display: inline-block;
         top: -7px;
         position: absolute;
         left: -7px;
         background: #02162B;
         z-index: -1;
       }
      }
      .ivu-timeline-item {
        z-index: 1;
        padding-bottom: 0;
        .ivu-timeline-item-tail {
          border-left: 2px solid #07355E;
          left: 3px;
          top: 4px;
          z-index: -1;
        }
        .ivu-timeline-item-head {
          width: 8px;
          height: 8px;
          background: #2B84E2;
          top: 3px;
        }
        .ivu-timeline-item-head-blue {
          border-color: #2B84E2;
          color: #2B84E2;
        }
        .ivu-timeline-item-content {
          padding: 0 0 20px 24px;
          .time {
            font-size: 14px;
            color: #2B84E2;
            font-weight: bold;
            line-height: 20px;
            .creator {
              margin-left: 5px;
            }
          }
          .content {
            background: rgba(3, 21, 48, 1);
            padding: 10px;
            box-sizing: border-box;
            border-radius: 4px;
            margin-top: 10px;
          }
        }
      }
    }

  }
</style>