<template>
  <div class="offline-table-box">
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="tableLoading">
    </ui-table>
  </div>
</template>
<script>
import { offlineTableColumns } from '../utils/enum';
export default {
  name: 'offlinetable',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableColumns: [],
    };
  },
  methods: {
    setTableColumn() {
      const emitTableFunc = ({ row }, handlerType = 'viewContinuousNoData') => {
        this.$emit('tableItemClick', { row }, handlerType);
      };
      this.tableColumns = offlineTableColumns(emitTableFunc);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  mounted() {
    this.setTableColumn();
  },
};
</script>
<style lang="less" scoped></style>
