<template>
  <ui-modal
    v-model="visible"
    :title="type === 'add' ? '新增分组' : '编辑'"
    :r-width="900"
    @onOk="comfirmHandle"
  >
    <div class="content">
      <div class="select_left">
        <Form ref="form" :model="form" :rules="ruleForm" class="form">
          <FormItem label="分组名称" :label-width="80" prop="groupName">
            <Input
              v-model="form.groupName"
              :maxlength="20"
              placeholder="请输入"
            />
          </FormItem>
        </Form>
        <div class="tree-list">
          <xn-tree
            class="deviceTree"
            :ref="'tree'"
            :option="option"
            :label="labelFn"
          ></xn-tree>
          <ui-loading v-if="loading"></ui-loading>
        </div>
      </div>
      <div class="show-right">
        <div class="right-header">
          <p>
            已选择: <span class="bule-color">{{ 0 }}</span
            >设备
          </p>
          <p class="deleAll bule-color" @click="handleDele">删除全部</p>
        </div>
        <!-- <div class="device-list">
                    <div>
                        <p></p>
                        <i class="iconfont color-white icon-shanchu ml-10" @click="remove(data)"></i>
                    </div>
                </div> -->
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { mapGetters } from "vuex";
import {
  queryDeviceOrgTree,
  addMyVideoGroup,
  updateMyVideoGroup,
} from "@/api/player";
import xnTree from "@/components/xn-tree/index.vue";
export default {
  data() {
    return {
      type: "add", //新增/编辑
      visible: false,
      form: {
        groupName: "",
      },
      ruleForm: {
        //表单分组名称校验
        groupName: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: "null", //radio,null,checkbox
        canMove: false,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            let roleParam = {
              roleId: this.userInfo.roleVoList[0].id,
              filter:
                this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
            };
            return new Promise((resolve) => {
              queryDeviceOrgTree({
                orgCode: node.orgCode,
                deviceType: 1,
                ...roleParam,
              }).then((res) => {
                resolve(this._formatDeviceOrgList(res));
              });
            });
          },
        },
      },
      treeData: [],
      loading: false,
      total: 0,
      playingDeviceIds: [],
    };
  },
  components: {
    xnTree,
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  methods: {
    // 初始化
    show(item) {
      this.type = item ? "edit" : "add";
      this.visible = true;
      this.$nextTick(() => {
        this.getParentData();
        this.$refs.form.resetFields();
        if (this.type === "edit") {
          // 编辑字段回显
          this.form = {
            groupName: item.groupName,
            id: item.id,
          };
        }
      });
    },
    getParentData() {
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
      };
      this.loading = true;
      let params = { searchKey: this.searchInput, deviceType: 1, ...roleParam };
      if (this.searchInput) params = { ...params, ...this.pageInfo };
      queryDeviceOrgTree(params).then((res) => {
        this.total = res.data.total || 0;
        this.loading = false;
        this.treeData = this._formatDeviceOrgList(res);
        this.$refs.tree.initTree(this.treeData);
      });
    },
    // 确认提交
    comfirmHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 新增
          if (this.type === "add") {
            addMyVideoGroup({
              userId: this.userInfo.id,
              groupName: this.form.groupName,
            }).then((res) => {
              this.visible = false;
              this.$Message.success(res.msg);
              this.$emit("refreshDataList");
            });
          } else {
            //编辑
            updateMyVideoGroup({
              groupName: this.form.groupName,
              id: this.form.id,
            }).then((res) => {
              this.visible = false;
              this.$Message.success(res.msg);
              this.$emit("refreshDataList");
            });
          }
        }
      });
    },
    handleDele() {},
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let playingClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing"
          : "";
      let iconClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing-icon"
          : !data.deviceId
          ? "icon-fenju"
          : this.getDeviceIcon(data);
      let iconColor = this.getDeviceIconColor(data);
      let statistics = !data.deviceId
        ? `(<span class="color-bule">${data.onlineTotal || 0}</span>/${
            data.allTotal || 0
          })`
        : "";
      let html = `<div class="node-title ${titleClass} ${playingClass}">
                    <i class="iconfont color-bule ${iconClass}" style="color: ${iconColor}"></i>
                    <span class="label">${data.label}</span>
                    <span class="statistics">${statistics}</span>
                </div>
                `;
      return html;
    },
    // 格式化设备、组织、统计数据
    _formatDeviceOrgList(deviceOrgResult) {
      let deviceList = deviceOrgResult.data.deviceList
        ? deviceOrgResult.data.deviceList.map((v) => {
            v.id = v.deviceId;
            v.label = v.deviceName;
            (v.isLeaf = true),
              (v.ptzType = v.deviceChildType ? v.deviceChildType : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
            return v;
          })
        : [];
      let deviceOrgList = deviceOrgResult.data.deviceOrgList
        ? deviceOrgResult.data.deviceOrgList.map((v) => {
            v.label = v.orgName;
            return v;
          })
        : [];
      deviceOrgList = deviceOrgList.filter((v) => v.allTotal > 0);
      return [...deviceOrgList, ...deviceList];
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-input {
  width: 330px;
}
// .form {
//   height: 200px;
// }
.content {
  display: flex;
//   height: 680px;
  .select_left {
    flex: 1;
    .tree-list {
      position: relative;
      overflow-y: scroll;
      height: 580px;
    }
  }
  .show-right {
    width: 300px;
    padding: 10px;
    margin-left: 10px;
    background: #f6f7f8;
    .right-header {
      display: flex;
      justify-content: space-between;
      .bule-color {
        color: #2c86f8;
      }
      .deleAll {
        cursor: pointer;
      }
    }
    .device-list {
      overflow-y: scroll;
    }
  }
}
</style>
