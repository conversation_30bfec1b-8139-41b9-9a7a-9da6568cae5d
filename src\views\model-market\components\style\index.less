.case_leftBox{
    .title{
        display: flex;
        font-weight: 700;
        align-items: center;
        border-bottom: 1px solid #D3D7DE;
        padding: 9px 15px;
        .title_num{
            width: 18px;
            height: 18px;
            border-radius: 10px;
            background: #2C86F8;
            font-size: 14px;
            color: #FFFFFF;
            text-align: center;
            line-height: 18px;
        }
        .title_text{
            margin-left: 10px;
            font-size: 16px;
            color: rgba(0,0,0,0.9);
        }
    }
}
.line-title{
    // 头部名称
    .title{
        font-size: 16px;
        font-weight: bold;
        color: rgba(0,0,0,0.9);
        height: 40px;
        position: relative;
        line-height: 40px;
        padding-left: 20px;
        border-bottom: 1px solid #D3D7DE;
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        z-index: 1;
        background: #fff;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 20px;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
            background: #2c86f8;
        }
        span{
            color: #2C86F8; 
        }
        /deep/.ivu-icon-ios-close{
            font-size: 30px;
            cursor: pointer;
        }
    }
}
// 搜索底部收起，展开
.footer{
    display: flex;
    justify-content: center;
    cursor: pointer;
    padding: 10px 0;
    color: #2C86F8;
    &:hover{
        color: #4597FF;
    }
    img{
        transform: rotate(0deg);
        transition: transform 0.2s;
        margin-right: 5px;
    }
}
.packArrow{
    img{
        transform: rotate(180deg);
        transition: transform 0.2s;
    }
}
.ivu-form-item {
	display: flex;
    align-items: center;
}