<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="民族:" prop="nation">
      <Select
        v-model="queryParam.nation"
        placeholder="请选择"
        :multiple="false"
        transfer
        :max-tag-count="1"
      >
        <Option
          :value="item.dataKey"
          v-for="item in nationTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
    <FormItem label="性别:" prop="sex">
      <Select
        v-model="queryParam.sex"
        placeholder="请选择"
        :multiple="false"
        transfer
        :max-tag-count="1"
      >
        <Option
          :value="item.dataKey"
          v-for="item in sexTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
    <FormItem label="小区:" prop="sex">
      <Select
        v-model="queryParam.placeId"
        filterable
        clearable
        placeholder="请选择小区"
        transfer
      >
        <Option
          :value="item.id"
          v-for="item in selectPlaceList"
          :key="item.id"
          >{{ item.name }}</Option
        >
      </Select>
    </FormItem>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { getConfigPlaces } from "@/api/monographic/community-management.js";
export default {
  data() {
    return {
      selectDeviceList: [], // 选中的设备
      queryParam: {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        minAge: "",
        maxAge: "",
        nation: "",
        sex: "",
      },
      selectPlaceList: [],
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      sexTypeList: "dictionary/getGenderList", // 性别
      partiTypeList: "dictionary/getPartiTypeList", // 案件类型 这个字典值后边可能要单独拎出来一个给未成年人用
    }),
    keyWords() {
      return this.$route.query.keyWords || "";
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.getPlaceList();
    if (this.$route.query.idCardNo) {
      this.queryParam.idCardNo = this.$route.query.idCardNo;
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 获取系统管理设置的小区信息
    getPlaceList() {
      getConfigPlaces().then(({ data }) => {
        this.selectPlaceList = data || [];
      });
    },
    /**
     * @description: 选择设备，打开弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList, this.keyWords);
    },

    /**
     * @description: 初始化已选择的设备
     * @param {array} arr 已选择的设备
     */
    selectData(arr) {
      this.selectDeviceList = arr;
      this.queryParam.deviceIds = arr.map((e) => e.deviceGbId);
    },

    /**
     * @description: 重置
     */
    reset() {
      this.queryParam.name = "";
      this.queryParam.idCardNo = "";
      this.queryParam.minAge = "";
      this.queryParam.maxAge = "";
      this.queryParam.nation = "";
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },
    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.selectTag {
  margin-top: 4px;
}
.gerling {
  color: #f29f4c;
}
.hk {
  color: #2c86f8;
}
.separtor {
  margin: 0 10px;
  width: 16px;
  height: 1px;
  background: rgba(0, 0, 0, 0.45);
}
.flex-box {
  display: flex;
  align-items: center;
}
.select-record-kind {
  /deep/.ivu-select-item {
    width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
