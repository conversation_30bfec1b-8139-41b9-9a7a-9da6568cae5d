<template>
  <map-window-container
    :title="title"
    :close="close"
    class="map-faceOrVehicle-window-container"
  >
    <component
      :ref="sectionMenuName"
      :is="sectionMenuName"
      :map-dom-data="mapDomData"
      :is-map-click="isMapClick"
      @preDetial="(Index) => $emit('update:currentClickIndex', Index)"
      @nextDetail="(Index) => $emit('update:currentClickIndex', Index)"
      @close="close"
      :clickIndexId="clickIndexId"
      @isModelDetail="changeModelDetail"
    ></component>
  </map-window-container>
</template>

<script>
import mapWindowContainer from "./map-window-container.vue";
import face from "@/views/operations-on-the-map/map-default-page/components/map-dom/face-map-dom.vue";
import vehicle from "@/views/operations-on-the-map/map-default-page/components/map-dom/vehicle-map-dom.vue";
import getPersonTrajectoryDetailHOC from "./getPersonTrajectoryDetailHOC";
export default {
  components: {
    face,
    vehicle,
    mapWindowContainer,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    close: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      title: "点位详情",
      clickIndexId: "",
      mapDomData: {},
      isMapClick: false,
      sectionMenuName: "face",
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      const { dataType, detail, dataId } = this.data;
      let detailData = detail; // 这字段存在代表数据本身有详情，不需要查询详情接口
      if (!detailData) {
        detailData = await getPersonTrajectoryDetailHOC({ dataId, dataType });
      }
      if (!["face", "vehicle"].includes(dataType)) return;
      this.sectionMenuName = dataType;
      this.$nextTick(() => {
        this.$refs[this.sectionMenuName].init({}, detailData, "", false, []);
      });
    },
    changeModelDetail() {},
  },
};
</script>

<style lang="less" scoped>
.map-faceOrVehicle-window-container {
  width: 1000px;
}
</style>
