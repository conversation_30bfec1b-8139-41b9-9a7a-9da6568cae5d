<template>
  <div class="container box">
    <div class="person">
      <!-- 查询 -->
      <Search @searchForm="searchForm" />
      <ui-table
        ref="seletction"
        :columns="columns"
        :data="tableData"
        :loading="loading"
      >
        <template #markColor="{ row }">
          <div
            class="mark-color"
            :style="{
              backgroundColor:
                row.markColor == '1'
                  ? '#EB4B4B'
                  : row.markColor == '2'
                  ? '#F29F4C'
                  : row.markColor == '3'
                  ? '#FDEE38'
                  : row.markColor == '4'
                  ? '#67D28D'
                  : '#2379F9',
            }"
          ></div>
        </template>
        <template #action="{ row }">
          <div class="btn-tips">
            <ui-btn-tip
              content="详情"
              icon="icon-xiangqing"
              class="mr-20 primary"
              @click.native="view(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              v-if="row.permission == 'write'"
              content="编辑"
              icon="icon-bianji"
              class="mr-20 primary"
              @click.native="edit(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              v-if="row.permission == 'write'"
              content="共享"
              icon="icon-caozuo"
              class="mr-20 primary"
              @click.native="share(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              v-if="row.permission == 'write'"
              content="删除"
              icon="icon-shanchu"
              class="mr-20 primary"
              @click.native="delet(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <!-- 分页 -->
      <ui-page
        :current="params.pageNumber"
        :total="total"
        :page-size="params.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
    <!-- 新增/编辑 -->
    <edit-modal ref="editModal" @refreshDataList="refreshDataList" />

    <!-- 选择用户 -->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
      :orgin="'powerInner'"
    ></select-user>
  </div>
</template>
<script>
import Search from "./components/search";
import editModal from "./components/edit-modal";
import { framePageList, frameDelete, frameShare } from "@/api/frame.js";
import { mapGetters } from "vuex";
import SelectUser from "@/components/select-modal/select-user.vue";

export default {
  name: "tag-manager",
  components: { Search, editModal, SelectUser },
  props: {},
  data() {
    return {
      loading: false,
      pageForm: {},
      params: {
        pageNumber: 1,
        pageSize: 10,
      },
      total: 0,
      tableData: [], //列表
      columns: [
        { title: "序号", width: 90, type: "index", key: "index" },
        { title: "标记名称", key: "markTitle" },
        { title: "标记颜色", slot: "markColor" },
        { title: "标记人", key: "creator" },
        { title: "设备名称", key: "deviceName" },
        { title: "所属组织", key: "orgName" },
        { title: "标记时间", key: "markTime" },
        { title: "操作", slot: "action", width: 170 },
      ],
      currentItem: "",
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  created() {
    this.getList();
  },
  watch: {
    $route: {
      handler(val) {
        if (val.name == "tag-manager") {
          this.getList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 列表
    getList() {
      this.loading = true;
      framePageList(Object.assign(this.pageForm, this.params))
        .then((res) => {
          const { entities, pageSize, pageNumber, total } = res.data;
          this.tableData = entities;
          this.params = {
            pageSize: pageSize,
            pageNumber: pageNumber,
          };
          this.total = total;
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 新增/编辑
    refreshDataList() {
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm(search) {
      console.log(search);
      this.pageForm = {
        ...search,
      };
      this.params.pageNumber = 1;
      this.getList();
    },
    view(item) {
      this.$refs.editModal.show(item, "view");
    },
    // 编辑资源
    edit(item) {
      this.$refs.editModal.show(item, "edit");
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 删除
    delet(item) {
      if (this.userInfo.id == item.userId) {
        this.$Modal.confirm({
          title: "提示",
          closable: true,
          content: `确定删除吗？`,
          onOk: () => {
            frameDelete(item.id).then((res) => {
              this.$Message.success(res.msg);
              this.getList();
            });
          },
        });
      } else {
        this.$Message.warning("只能删除自己创建的帧标记");
      }
    },
    // 共享
    share(data) {
      this.currentItem = data;
      let sharedUserList = data.sharedUserList || [];
      sharedUserList.forEach((item) => {
        item.select = true;
        item.id = item.id || item.userId;
      });
      this.$refs.selectUser.show(sharedUserList);
    },
    handleUserData(list) {
      if (!list.length) return;
      if (this.currentItem) {
        frameShare({
          id: this.currentItem.id,
          sharedUserIds: list.map((v) => v.id),
        }).then((res) => {
          if (res.code == 200) {
            this.currentItem.sharedUserList = list;
            this.$Message.success("分享成功");
          }
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.box {
  overflow: hidden;
}
.person {
  display: flex;
  flex: 1;
  height: 100%;
  width: 100%;
  // width: 1820px;
  flex-direction: column;
}
.mark-color {
  width: 30px;
  height: 5px;
}
</style>
