<!-- 人脸抓拍图片质量合格率（FACE_QUALITY_PASS_RATE） -->
<template>
  <div class="complete-rule-form">
    <common-form
      :label-width="180"
      class="common-form"
      ref="commonForm"
      :moduleAction="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="extractCar">
        <template>
          <FormItem label="每设备抽取图片" prop="captureNum">
            <InputNumber
              v-model="formData.captureNum"
              class="input-width"
              placeholder="请输入抽取图片"
              clearable
            ></InputNumber>
          </FormItem>
          <FormItem
            prop="deviceQueryForm.dayByCapture"
            label="图片抽取范围"
            :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
          >
            <span class="base-text-color mr-xs">近</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.dayByCapture"
              :min="0"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color">天，</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.startByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点至</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.endByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点</span>
          </FormItem>
        </template>
      </div>
      <div slot="waycondiction" class="mt-xs" v-if="['1', '3'].includes(formData.detectMode)">
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
              >设备可用</Checkbox
            >
            <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.examReportStatus" true-value="1" false-value="0"
              >设备未报备</Checkbox
            >
          </div>
          <div>
            <FormItem
              prop="deviceQueryForm.dayByFilterOnline"
              label=""
              :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
            >
              <Checkbox class="mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
              <span class="base-text-color">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
              <div class="capture-vehicle">
                <span class="base-text-color mr-sm">抓拍人脸不少于</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.countByFilterOnline"
                  :min="0"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color">张</span>
                <p class="color-failed">说明：系统只检测满足条件的设备。</p>
              </div>
            </FormItem>
          </div>
        </div>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="180">
      <FormItem label="检测方法" :prop="checkMethodsKey" key="'isShowOsdModel'">
        <Select
          v-model="formData[checkMethodsKey]"
          placeholder="请选择"
          transfer
          class="input-width"
          @on-change="handleCheckMethods"
          disabled
        >
          <template v-for="e in checkMethodsWay">
            <Option :value="e.dataKey" :key="e.dataKey">{{ e.dataValue }} </Option>
          </template>
        </Select>
      </FormItem>
      <FormItem label="检测内容">
        <CheckboxGroup v-model="wangliCheckContent">
          <Checkbox
            v-for="(wqItem, wqIndex) in wangliVideoQualityCheckContent"
            :key="'wq' + wqIndex"
            :label="wqIndex"
            :disabled="wqItem.disabled"
          >
            {{ wqItem.text }}
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <!--      综合质量评分    -->
      <FormItem required class="quality-score" label="综合质量评分低于">
        <FormItem prop="threshold" label="">
          <Input class="ipt-width-50" v-model="formData.threshold" />
          <span class="params-suffix"> 分，且单项异常比高于 </span>
        </FormItem>
        <FormItem prop="minThreshold">
          <Input class="ipt-width-50" v-model="formData.minThreshold" />
          <span class="params-suffix"> % 非必填，则图像质量不合格</span>
        </FormItem>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import videoConfigsData from '../../video-config/utils/video-config';
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {},
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        //this.schemeList = this.getHour()
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            quantityConfig: [],
            deviceDetection: {
              dataConfig: {
                key: 'now', //lately 最近
                value: 'month', // 30 10
                quantity: 0,
              },
            },
            deviceQueryForm: {
              ...this.deviceQueryForm,
            }, //默认值
            visitTimeout: null,
          };
        }
      },
      immediate: true,
    },
  },
  data() {
    const validateThresholdPass = (rule, value, callback) => {
      !value ? callback(new Error('请输入综合质量评分设置值')) : callback();
    };
    const validateMinThresholdPass = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        const reg = new RegExp('^([1-9]|[1-9]\\d|100)$');
        !reg.test(value) ? callback(new Error('请输入1-100的整数')) : callback();
      }
    };
    return {
      formData: {
        hourStart: null, //时间区间开始时间
        hourEnd: null, //时间区间结束时间
        captureNum: null, //抓拍数量
        videoQualityDetectMode: '3',
        threshold: '80',
        minThreshold: '80',
      },
      checkMethodsWay: [
        {
          dataKey: '3',
          dataValue: '视频流质量检测算法 (精简模型)',
        },
      ], // 检测方法数据
      checkMethodsKey: 'videoQualityDetectMode', // 检测方法配合后端用的字段 (视频质量合格率,视频流质量合格率（人工复核）: videoQualityDetectMode; 其他指标： osdModel)
      wangliCheckContent: [0, 1, 2, 3, 4, 5],
      deviceQueryForm: {
        detectPhyStatus: null,
        filterOnline: null,
        dayByFilterOnline: 2,
        countByFilterOnline: 1,
        startByFilterOnline: 0,
        endByFilterOnline: 23,

        dayByCapture: 2,
        startByCapture: 0,
        endByCapture: 23,
      },
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      ...videoConfigsData.data(),
      ruleCustom: {
        threshold: [{ validator: validateThresholdPass, trigger: 'blur' }],
        minThreshold: [{ validator: validateMinThresholdPass, trigger: 'blur' }],
      },
    };
  },
  async created() {},
  methods: {
    handleCheckMethods() {
      this.$set(this.formData, 'threshold', '80');
      this.$set(this.formData, 'minThreshold', '80');
      this.videoQualityCheckContent.forEach((item) => {
        if (this.formData[item.key]) {
          delete this.formData[item.key];
        }
      });
    },
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      await this.$refs.modalData.validate();
      return this.$refs['commonForm'].handleSubmit();
    },
  },
};
</script>

<style lang="less" scoped>
.complete-rule-form {
  .input-width {
    width: 380px;
  }
  .ipt-width-50 {
    width: 50px;
  }
  .params-suffix {
    color: var(--color-content);
    margin-left: 5px;
  }
  .quality-score {
    @{_deep} .ivu-form-item-content {
      margin-left: 0 !important;
      display: flex;
      align-items: center;
    }
  }
}
</style>
