<template>
  <div class="auto-fill">
    <TableList
      ref="infoList"
      class="table-list"
      :columns="columns"
      :minusHeight="minusHeight"
      :loadData="loadDataList"
      :paging="false"
      listKey="data"
    >
      <!-- 检索 -->
      <template slot="search">
        <div class="search-container">
          <div class="jump">
            <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
          </div>
          <!-- <div class="search">
            <ui-label label="设备类型" :width="70" class="ml-lg fl">
              <Select class="w200" v-model="searchData.deviceTagCategory" :clearable="true">
                <Option value="2">重点设备 </Option>
                <Option value="1">普通设备 </Option>
              </Select>
            </ui-label>
            <Button type="primary" class="ml-lg" @click="startSearch">查询</Button>
            <Button class="ml-sm" @click="resetSearchDataMx1">重置</Button>
          </div> -->
        </div>
      </template>
      <!-- 表格操作 -->
      <template #orgName="{ row }">
        <span v-if="orgCode == row.orgCode">{{ row.orgName }}</span>
        <span class="font-table-action pointer" v-else @click="change(row)">{{ row.orgName }}</span>
      </template>
    </TableList>
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import { mapGetters } from 'vuex';
export default {
  components: {
    TableList: require('../components/tableList.vue').default,
    UiBreadcrumb: require('../components/ui-breadcrumb').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      breadcrumbData: [],
      searchData: {},
      selectKey: '',
      minusHeight: 260, // 430
      columns: [
        {
          title: '行政区划',
          slot: 'orgName',
          align: 'center',
          width: 120,
          className: 'header-table',
        },
        {
          title: '关键属性（车牌号码、车牌颜色）完整性检测',
          align: 'center',
          className: 'header-table',
          key: '1',
          children: [
            {
              title: '抽检数量（普通）',
              key: 'keyOrdinaryPullSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'keyOrdinaryPullUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '属性缺失图片数量',
              key: 'keyOrdinaryPullMissingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '抽检数量（重点）',
              key: 'keyFocusPullSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'keyFocusPullUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '属性缺失图片数量',
              key: 'keyFocusPullMissingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
          ],
        },
        {
          title: '关键属性（车牌号码、车牌颜色）准确性检测',
          key: '1',
          align: 'center',
          className: 'header-table',
          children: [
            {
              title: '抽检数量（普通）',
              key: 'keyOrdinaryAccurateSamplingCount',
              className: 'header-table',
              width: 160,
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'keyOrdinaryAccurateUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '属性错误图片数量',
              key: 'keyOrdinaryAccurateErrorCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '抽检数量（重点）',
              key: 'keyFocusAccurateSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'keyFocusAccurateUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '属性错误图片数量',
              key: 'keyFocusAccurateErrorCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
          ],
        },
        {
          title: '类型属性（车辆类型、车辆品牌）准确性检测',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '抽检数量（重点）',
              key: 'typeFocusAccurateSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'typeFocusAccurateUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '属性错误图片数量',
              key: 'typeFocusAccurateErrorCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
          ],
        },
        {
          title: '时钟准确性检测',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '抽检数量（重点）',
              key: 'clockFocusSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'clockFocusUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '时钟不准确图片数量',
              key: 'clockFocusErrorCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
          ],
        },
        {
          title: '上传及时性检测',
          key: '1',
          align: 'center',
          className: 'header-table',
          children: [
            {
              title: '抽检数量（普通）',
              key: 'uploadOrdinarySamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'uploadOrdinaryUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '上传超时图片数量',
              key: 'uploadOrdinaryTimeOutCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '抽检数量（重点）',
              key: 'uploadFocusSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'uploadFocusUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '上传超时图片数量',
              key: 'uploadFocusTimeOutCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
          ],
        },
        {
          title: 'URL可用性检测',
          key: '1',
          align: 'center',
          className: 'header-table',
          children: [
            {
              title: '抽检数量（普通）',
              key: 'urlOrdinarySamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'urlOrdinaryUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: 'URL不可用图片数量',
              key: 'urlOrdinaryNotUseCount',
              width: 180,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '抽检数量（重点）',
              key: 'urlFocusSamplingCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: '不合格设备数量',
              key: 'urlFocusUnqualifiedCount',
              width: 160,
              className: 'header-table',
              align: 'center',
            },
            {
              title: 'URL不可用图片数量',
              key: 'urlFocusNotUseCount',
              width: 180,
              className: 'header-table',
              align: 'center',
            },
          ],
        },
      ],
      infoObj: {},
      params: {},
      orgCode: '',
      loadDataList: () => {
        return this.$http
          .post(
            inspectionrecord.vehicleForDeviceSurvey,
            Object.assign(
              {
                ...this.params,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res;
          });
      },
    };
  },
  computed: {
    ...mapGetters({
      orgTreeData: 'common/getOrganizationList',
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
      },
    },
  },
  filter: {},
  created() {},
  mounted() {
    this.info();
  },
  methods: {
    async info() {
      if (!this.taskObj.batchIds.length) {
        return;
      }
      const { batchIds, regionCode, indexId, taskIndexId, regionName } = this.taskObj;
      let orgName = regionName;
      var arr = this.breadcrumbData.filter((item) => {
        return item.add == regionName;
      });
      if (arr.length == 0) {
        this.breadcrumbData.push({ id: regionCode, add: orgName });
      }
      this.params = {
        batchIds,
        civilCode: regionCode,
        evaluationIndexId: indexId,
        taskIndexId,
      };
      // let { orgCode, orgName } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      // this.selectKey = orgCode || ''
      // let orgName1 = orgName || ''
      // if (!orgCode) return this.$Message.error('您没有此行政区划权限')
      // var arr = this.breadcrumbData.filter((item) => {
      //   return item.add == orgName1
      // })
      // if (arr.length == 0) {
      //   this.breadcrumbData.push({ id: this.selectKey, add: orgName1 })
      // }

      await this.$refs.infoList.info(true);
    },
    resetSearchDataMx1() {
      this.searchData = {};
      this.$refs.infoList.info(true);
    },
    startSearch() {
      this.$refs.infoList.info(true);
    },
    handleChange(val) {
      if (val.id === this.taskObj.regionCode) {
        this.params = {
          ...this.params,
          orgCode: undefined,
          civilCode: val.id,
        };
      } else {
        this.params = {
          ...this.params,
          orgCode: val.id,
        };
      }
      this.$refs.infoList.info(true);
    },
    change(row) {
      let id = row.orgCode;
      let add = row.orgName;
      var arr = this.breadcrumbData.filter((item) => {
        return item.add == add;
      });
      if (arr.length == 0) {
        this.breadcrumbData.push({ id: id, add: add });
      }
      this.selectKey = row.orgCode;
      this.params = {
        ...this.params,
        civilCode: undefined,
        orgCode: row.orgCode,
      };
      this.$refs.infoList.info(true);
    },
  },
};
</script>
<style lang="less" scoped>
.base-search {
  width: 100%;
  height: 50px;
  .w200 {
    width: 200px;
  }
}
/deep/ .table-list > .left-div > .ui-table .ivu-table-tbody td {
  background: var(--bg-content);
}
.table-list {
  /deep/ .ui-table {
    border-top: 1px solid var(--border-table);
    th .ivu-table-cell {
      color: #8797ac;
    }
  }
  /deep/ .ivu-table-body > table {
    border-right: 1px solid var(--border-table);
  }
  /deep/ .ivu-table-header {
    border-right: 1px solid var(--border-table);
  }
  /deep/ .header-table {
    box-shadow: none;
    // box-shadow: inset 1px -1px 0 0 #0d477d;
    border-left: 1px solid var(--border-table);
    border-bottom: 1px solid var(--border-table);
  }
  @{_deep} .ivu-table-tip {
    overflow-x: auto;
  }
}
.search-container {
  display: flex;
  justify-content: space-between;
  margin: 20px 0 20px 0;
  .jump {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.w200 {
  width: 200px;
}
</style>
