import { queryParamDataByKeys } from "@/api/config";
import { getAllCombatRecord } from "@/api/operationsOnTheMap";
export default {
  namespaced: true,
  state: {
    maxLayer: {}, // 规定最大值
    existLayer: {
      //已存在的层数，点位
      layerNum: 0, //图层数
      pointsNum: 0, //点数
    },
    layer: [], //新增图层数据
    newAddLayer: {
      //新添加图层，点数(未保存)
      layer: 0, //图层
      pointsInLayer: 0, //点数
    },
    listNum: 0, //新添加标识
    pageInfo: {
      //用于区分详情框分页
      startPage: 0,
      endPage: 0,
    },
  },
  mutations: {
    // 添加
    setLayer(state, layer) {
      state.layer.push(layer);
    },
    // 删除后重新赋值
    setDeleLayer(state, layer) {
      state.layer = layer;
    },
    // 新增但未保存
    setNewAddLayer(state, item) {
      state.newAddLayer = item;
    },
    setNum(state, item) {
      //已存在
      state.existLayer = item;
    },
    setMaxLayer(state, maxLayer) {
      state.maxLayer = maxLayer;
    },
    setList(state, listNum) {
      state.listNum = listNum;
    },
    setPageInfo(state, page) {
      state.pageInfo = page;
    },
  },
  getters: {
    getLayer(state) {
      return state.layer;
    },
    // 新增但未保存
    getNewAddLayer(state) {
      return state.newAddLayer;
    },
    getMaxLayer(state) {
      return state.maxLayer;
    },
    getListNum(state) {
      return state.listNum;
    },
    getNum(state) {
      //已存在
      return state.existLayer;
    },
    getPageInfo(state) {
      return state.pageInfo;
    },
  },
  actions: {
    setSum({ state, commit, dispatch }) {
      return new Promise((resolve, reject) => {
        queryParamDataByKeys(["ICBD_MAP_CONFIG"])
          .then((res) => {
            commit("setMaxLayer", JSON.parse(res.data[0].paramValue));
            // dispatch('countTotal')
            resolve();
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    deleLayer({ state, commit, dispatch }, layer) {
      let { mapObj, index } = layer;
      let deleMapLayer = mapObj[index];
      let ind = "";
      state.layer.map((item, index) => {
        if (item.deleIdent === deleMapLayer.deleIdent) {
          ind = index;
        }
      });
      if (ind == "") {
        let layerNum = state.existLayer.layerNum - 1;
        let pointsNum =
          state.existLayer.pointsNum - deleMapLayer.position.length;
        commit("setNum", { layerNum, pointsNum });
        // dispatch('countTotal')
        return;
      } else {
        let layerNum = state.existLayer.layerNum - 1;
        let pointsNum =
          state.existLayer.pointsNum - deleMapLayer.position.length;
        commit("setNum", { layerNum, pointsNum });
        state.layer.splice(ind, 1);
        commit("setDeleLayer", state.layer);
      }
    },
    // 计算已存在的图层和点数
    countTotal({ commit }) {
      getAllCombatRecord().then((res) => {
        if (res.code === 200) {
          const { data = [] } = res;
          let recordsList = [...data];
          let layerNum = 0;
          let pointsNum = 0;
          recordsList.forEach((item) => {
            let data = JSON.parse(item.dataText);
            for (let key in data) {
              if (data[key].length > 0) {
                layerNum += data[key].length; //图层总数
                data[key].forEach((ite) => {
                  if (ite.position.length > 0) {
                    pointsNum += ite.position.length; // 点数总数
                  }
                });
              }
            }
          });
          // commit('setNum', {layerNum, pointsNum});
        }
      });
    },
  },
};
