<!--
    * @FileDescription: 感知设备报警
    * @Author: H
    * @Date: 2023/10/07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-17 15:19:29
 -->
<template>
  <div class="personnel_alarm">
    <sensoryCard
      :isShowCheckBox="false"
      :collectIcon="false"
      class="alarnRow"
      @refresh="tableListFn()"
      :alarmInfo="itemList"
      :compareType="itemList.compareType"
      :isShowAction="false"
    />
  </div>
</template>
<script>
import sensoryCard from "@/views/target-control/alarm-manager/sensory/components/sensory-card.vue";
export default {
  props: {
    itemList: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    sensoryCard,
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.personnel_alarm {
  width: calc(~"20% - 10px");
  margin-left: 10px;
}
</style>
