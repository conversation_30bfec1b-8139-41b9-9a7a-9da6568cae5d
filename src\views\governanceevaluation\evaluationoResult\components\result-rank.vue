<template>
  <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
    <div class="ranking-title f-14">
      <span class="icon-font icon-xiajipaihang mr-sm"></span>
      <span>下级排行</span>
    </div>
    <div class="ranking-list">
      <div class="rank-item" v-for="(item, index) in rankData" :key="index">
        <div class="content-firstly">
          <span class="bg_color font-white t-center" :class="`firstly${item.rank > 3 ? 4 : item.rank}`">{{
            item.rank
          }}</span>
        </div>
        <Tooltip class="content-second" transfer :content="item.regionName">
          <div>
            <img class="vt-middle second-image" :src="getImageUrl(item.rank)" />
            <span class="ellipsis base-text-color">{{ item.regionName }}</span>
          </div>
        </Tooltip>
        <div class="content-thirdly">
          <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
        </div>
        <div class="content-fourthly">
          <span class="icon-font f-16" :class="getClassByRankType(item.rankType)"></span>
          <span class="plus ml-sm" :class="`color-${item.rankType}`">{{ item.rankRise || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'ranking-info',
  mixins: [dealWatch],
  data() {
    return {
      rankData: [],
      rankLoading: false,
    };
  },
  created() {
    this.startWatch(
      '$route.query',
      () => {
        this.getRankInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    getClassByRankType(val) {
      switch (val) {
        case 'RISE':
        case 'SAME':
          return ['icon-shangsheng', 'color-RISE'];
        case 'DOWN':
          return ['icon-xiajiang', 'color-DOWN'];
      }
    },
    getImageUrl(rank) {
      if ([1, 2, 3].includes(rank)) {
        return require(`@/assets/img/crown_${rank}.png`);
      }
    },
    async getRankInfo() {
      this.rankLoading = true;
      const { regionCode, orgCode, statisticType, indexId, access, batchId, canPlay } = this.$route.query;
      let data = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        sortField: 'RESULT_VALUE',
      };
      if (canPlay && canPlay !== '2') {
        Object.assign(data, {
          customParameters: {
            canPlay: canPlay,
          },
        });
      }
      try {
        let res = await this.$http.post(evaluationoverview.getRankInfo, data);
        this.rankData = res.data.data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.rankLoading = false;
      }
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.information-ranking {
  position: relative;
  width: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  height: 100%;
  border-right: 4px;
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .ranking-list {
    padding: 10px;
    width: 100%;
    height: 81%;
    overflow-y: auto;

    .rank-item {
      display: flex;
      margin-bottom: 15px;
      align-items: center;

      .content-fourthly {
        display: inline-flex;
        font-size: 14px;
        position: relative;
        justify-content: center;
        flex: 1;
      }

      div {
        display: flex;
        align-items: center;
        font-size: 14px;
        position: relative;
      }

      .content-firstly {
        flex: 1;
        margin-left: 10px;
      }

      .content-thirdly {
        justify-content: center;
        flex: 1;
      }

      .content-second {
        color: #fff;
        justify-content: center;
        flex: 1;
        .second-image {
          width: 16px;
        }
        span {
          width: 75px;
          padding-left: 10px;
          display: inline-block;
        }

        .rankText {
          margin-left: 20px;
        }
      }

      .bg_color {
        min-width: 21px;
        min-height: 21px;
        font-weight: bold;
      }

      .firstly1 {
        background-color: #f1b700;
      }

      .firstly2 {
        background-color: #eb981b;
      }

      .firstly3 {
        background-color: #ae5b0a;
      }

      .firstly4 {
        background-color: var(--color-primary);
      }

      .thirdly {
        overflow: hidden;
        color: var(--color-primary);
      }

      .color-RISE {
        color: var(--color-success);
      }

      .color-DOWN {
        color: var(--color-failed);
      }

      .color-SAME {
        color: var(--color-success);
      }
    }
  }
}
</style>
