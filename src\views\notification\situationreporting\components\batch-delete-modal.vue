<template>
  <ui-modal
    v-model="visible"
    :title="title"
    width="35rem"
    @onCancel="handleReset"
    @query="handleSubmit"
    :loading="loading"
  >
    <div class="content f-14">
      <p v-if="cantHandleIds.length">
        <span>
          有<span class="font-red ml-xs mr-xs">{{ cantHandleIds.length }}</span>
          条报备信息不能删除，
        </span>
        确定要删除其他
        <span class="font-red ml-xs mr-xs">{{ canHandleIds.length }}</span>
        条报备信息吗？
      </p>
      <p v-else>
        已选择<span class="font-red ml-xs mr-xs">{{ canHandleIds.length || 0 }}</span
        >条报备信息，确定要删除吗？
      </p>
    </div>
  </ui-modal>
</template>

<script>
import examination from '@/config/api/examination.js';
export default {
  name: 'batch-delete-modal',
  props: {
    title: {
      type: String,
      default: '批量删除',
    },
    canHandleIds: {
      type: Array,
      default: () => [],
    },
    cantHandleIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      number: 0,
    };
  },
  methods: {
    init() {
      this.visible = true;
    },
    handleReset() {},
    async handleSubmit() {
      try {
        let { data } = await this.$http.delete(examination.remove + `/${this.canHandleIds}`);
        console.log(data, 'data');
        this.$emit('updateInfo');
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  justify-content: center;
  padding: 50px 0;
  color: #ffffff;
}
</style>
