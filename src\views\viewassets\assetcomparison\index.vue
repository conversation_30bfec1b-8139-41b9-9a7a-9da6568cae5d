<template>
  <div class="auto-fill">
    <div v-show="!componentName" class="equipmentlibrary evaluat_div">
      <div class="title">
        <i class="icon-font icon-zichanjieruqingkuangpandian color-active"></i>
        <span class="font-weight ml-sm">资产接入情况盘点</span>
      </div>

      <!-- 顶部统计 -->
      <Row :gutter="66" style="padding: 20px 66px">
        <Col span="8" v-for="(item, index) in tableList" :key="index">
          <TableView :tableInfo="item" :class="{ 'table-top-light': !['dark'].includes(themeType) }" />
        </Col>
      </Row>

      <!-- VS -->
      <Row type="flex" justify="center" :gutter="16">
        <Col span="6">
          <FormView
            ref="leftRow"
            :config="true"
            :assetsDataSource="assetsDataSource"
            :propertySearchLbgnlx="propertySearchLbgnlx"
            :dataSourceCategory="dataSourceCategory"
            @configFn="configFn"
            @databaseChange="databaseChange"
          />
        </Col>
        <Col type="flex" justify="center" span="2" align="middle">
          <i class="icon-font icon-VS1"></i>
        </Col>
        <Col span="6">
          <FormView
            ref="rightRow"
            :assetsDataSource="assetsDataSource"
            :propertySearchLbgnlx="propertySearchLbgnlx"
            :dataSourceCategory="dataSourceCategory"
            @databaseChange="databaseChange"
          />
        </Col>
      </Row>

      <!-- 按钮 -->
      <div style="margin-top: 60px; text-align: center">
        <create-tabs
          v-if="currrentBtn"
          :componentName="themData.componentName"
          :tabs-text="themData.text"
          @selectModule="selectModule"
          :tabs-query="{ id: 1 }"
          class="inline"
        >
          <Button type="primary" class="start" @click="startFn">开 始 比 对</Button>
        </create-tabs>
        <Button v-if="!currrentBtn" type="primary" class="start" @click="startFn">开 始 比 对</Button>
      </div>
    </div>

    <!-- <keep-alive v-else> -->
    <component :is="componentName" :resultData="resultData"></component>
    <!-- </keep-alive> -->

    <!-- 配置比对字段 -->
    <Config ref="Config" @render="configBack"></Config>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import api from '@/config/api/assetcomparison';
export default {
  name: 'assetcomparison',
  data() {
    return {
      currrentBtn: false,
      componentName: null,
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      themData: {
        componentName: 'assetComparisonResult', // 需要跳转的组件名
        text: '结果', // 跳转页面标题
        title: '结果',
        type: 'view',
        // title: "视图基础数据治理主题",
      },
      resultData: null,
      tableList: [
        {
          title: '人脸卡口',
          title1: '系统设备资产库',
          title2: '功能类型=人脸卡口',
          title3: '来源标识=人脸视图库',
          title4: '人脸视图库原始设备表',
          value1: null,
          value2: null,
          value3: null,
        },
        {
          title: '车辆卡口',
          title1: '系统设备资产库',
          title2: '功能类型=车辆卡口',
          title3: '来源标识=车辆视图库',
          title4: '车辆视图库原始设备表',
          value1: null,
          value2: null,
          value3: null,
        },
        {
          title: '视频监控',
          title1: '系统设备资产库',
          title2: '功能类型=视频监控',
          title3: '来源标识=视频监控',
          title4: '联网平台原始设备表',
          value1: null,
          value2: null,
          value3: null,
        },
      ],
    };
  },
  async created() {
    if (this.propertySearchLbgnlx.length === 0) await this.getAlldicData();
    this.init();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 批量获取字典值
    }),
    init() {
      this.$http.get(api.statistics).then((res) => {
        const obj = res.data.data;
        this.tableList[0].value1 = obj.assertFace;
        this.tableList[0].value2 = obj.assertFaceSource;
        this.tableList[0].value3 = obj.faceViewLib;
        this.tableList[1].value1 = obj.assertVehicle;
        this.tableList[1].value2 = obj.assertVehicleSource;
        this.tableList[1].value3 = obj.vehicleViewLib;
        this.tableList[2].value1 = obj.assertMonitor;
        this.tableList[2].value2 = obj.assertMonitorSource;
        this.tableList[2].value3 = obj.onLinePlatform;
      });
    },

    // 配置比对字段页面显示
    configFn() {
      this.$refs.Config.init();
    },

    // 配置比对字段返回数据
    configBack() {},

    // 开始对比
    startFn() {
      if (this.checkInfo()) {
        let leftForm = this.$refs.leftRow.form;
        let rightForm = this.$refs.rightRow.form;

        this.resultData = {
          left: {
            ...leftForm,
            tableName: this.tableName(leftForm.tableType, this.assetsDataSource),
            funtionTypesNames: this.functionTypeList(leftForm.funtionTypes, this.propertySearchLbgnlx),
          },
          right: {
            ...rightForm,
            tableName: this.tableName(rightForm.tableType, this.assetsDataSource),
            funtionTypesNames: this.functionTypeList(rightForm.funtionTypes, this.propertySearchLbgnlx),
          },
        };
      }
    },

    // 获取数据源名称
    tableName(key, list) {
      return list.filter((item) => {
        return item.dataKey == key;
      })[0].dataValue;
    },

    // 获取功能类型中文名
    functionTypeList(arr, list) {
      var names = [];
      if (!list) {
        console.log('功能类型列表不存在...');
        return names;
      }
      if (arr && arr.length > 0) {
        arr.forEach((item) => {
          list.forEach((ite) => {
            if (ite.dataKey == item) {
              names.push(ite.dataValue);
            }
          });
        });
      }
      return names;
    },

    // 检查搜索条件
    checkInfo() {
      let left = this.$refs.leftRow.checkInfo();
      let right = this.$refs.rightRow.checkInfo();
      if (left && right) {
        return true;
      }
    },

    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },

    databaseChange() {
      var leftForm = this.$refs.leftRow.form;
      var rightForm = this.$refs.rightRow.form;
      if (leftForm.tableType && rightForm.tableType) {
        this.currrentBtn = true;
      } else {
        this.currrentBtn = false;
      }
    },
  },
  watch: {
    $route: 'getParams',
  },
  computed: {
    ...mapGetters({
      assetsDataSource: 'algorithm/assets_data_source', // 数据源
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      dataSourceCategory: 'algorithm/data_source_category', //来源标识
      themeType: 'common/getThemeType',
    }),
  },
  props: {},
  components: {
    TableView: require('./components/table.vue').default,
    FormView: require('./components/form').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    Config: require('./components/config').default,
    assetComparisonResult: require('./asset-comparison-result').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .title {
    color: var(--color-content);
  }
  .equipmentlibrary {
    background: url('~@/assets/img/assetcomparison-light.png');
    background-size: 100% 100%;
  }
}
@{_deep}.table-top-light .ivu-table {
  background-color: transparent !important;
  .ivu-table-tbody td,
  th {
    border: 1px solid #8897a4 !important;
  }
}

.title {
  margin: 20px 0 0 20px;
  font-size: 16px;
  color: var(--color-primary);
}
.equipmentlibrary {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  background: url('~@/assets/img/assetcomparison.png');
  background-size: 100% 100%;
}

.start {
  width: 330px;
  height: 34px;
  // border: 1px solid rgba(0, 0, 0, 0);
  background: var(--bg-btn-primary);
  opacity: 1;
  border-radius: 4px;
  color: #fff;
}

/deep/ .ivu-col-span-2 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: var(--color-display-text);

  .icon-VS1 {
    margin-top: 30px;
    font-size: 30px;
  }
}
</style>
