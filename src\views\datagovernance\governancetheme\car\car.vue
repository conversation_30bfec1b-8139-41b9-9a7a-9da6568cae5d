<template>
  <div class="view">
    <div class="btns">
      <Button type="primary" :loading="loading" @click="run">
        <span v-if="!loading">开始运行</span>
        <span v-else>Loading...</span>
      </Button>
    </div>

    <div class="flexUi">
      <div class="item item1">
        <span :class="'icon-font icon-' + car[0].icon1"></span>
        <i class="icon-font icon-chenggong success"></i>
        <p class="title">{{ car[0].title }}</p>
        <p class="desc">{{ car[0].desc }}</p>
      </div>
      <img src="@/assets/img/arrow.png" alt srcset />

      <div class="group1">
        <div class="groupItem" v-for="(item, index) in car[2]" :key="index">
          <span :class="'icon-font icon-' + item.icon1" style="font-size: 22px"></span>
          <p class="title">{{ item.title }}</p>
          <p class="desc">{{ item.desc }}</p>
          <i v-if="index != 0" class="config icon-font icon-systemmanagement" @click="picUploadFn(index)"></i>
          <m-switch :class="{ nullConfig: index == 0 }" v-if="pageLoading" :options="car[2][index].datas" />
        </div>
      </div>
      <img src="@/assets/img/arrow.png" alt srcset />

      <!-- 车辆结构化 -->
      <div class="item item2">
        <i class="icon-font icon-shujushuru leftIcon"></i>
        <span :class="'icon-font icon-' + car[1].icon1"></span>
        <i class="icon-font icon-chenggong success"></i>
        <p class="title">{{ car[1].title }}</p>
        <p class="desc">{{ car[1].desc }}</p>
        <i class="config icon-font icon-systemmanagement" @click="carConfigFn(2)"></i>
        <m-switch :class="{ nullConfig: index == 0 }" v-if="pageLoading" :options="car[1].datas" />
      </div>
      <img src="@/assets/img/arrow.png" alt srcset />

      <div class="group2">
        <div class="groupItem" v-for="(item, index) in car[3]" :key="index">
          <span :class="'icon-font icon-' + item.icon1"></span>
          <p class="title">{{ item.title }}</p>
          <p class="desc">{{ item.desc }}</p>
          <i v-if="index == 1" class="config icon-font icon-systemmanagement" @click="carOkConfigFn()"></i>
          <m-switch :class="{ nullConfig: index == 0 }" v-if="pageLoading" :options="car[3][index].datas" />
        </div>
        <div class="groupItem add">拖拽添加组件</div>
      </div>
      <img src="@/assets/img/arrow.png" alt srcset />

      <div class="item item5">
        <span :class="'icon-font icon-' + car[4].icon1"></span>
        <p class="title">{{ car[4].title }}</p>
        <p class="desc">{{ car[4].desc }}</p>
      </div>
    </div>

    <div class="faceprocess-right">
      <component-package></component-package>
    </div>

    <carDialog ref="modal" />
    <picUpload ref="picUpload" />
    <carConfig ref="carConfig" />
    <carOkConfig ref="carOkConfig" />
    <bigPicUrl ref="bigPicUrl" />
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
import { car } from './car';
import carDialog from './carDialog';
export default {
  name: 'carView',
  props: {},
  data() {
    return {
      loading: false,
      pageLoading: false,
      car: car,
      switch1: false,
      carChildrenList: [],
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.$http
        .get(api.queryCarList + '3')
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data.componentList;

            this.car.forEach((item) => {
              if (item instanceof Object) {
                item.datas = list.find((it) => it.componentName == item.title) || {};
              }

              if (item instanceof Array) {
                item.forEach((childItem) => {
                  if (childItem.switch) {
                    childItem.datas = list.find((it) => it.componentName == childItem.title) || {};
                  }
                });
              }
            });

            this.pageLoading = true;
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    picUploadFn(index) {
      if (index == 1) {
        this.$refs.picUpload.showModal();
      } else {
        this.$refs.bigPicUrl.showModal(this.car[2][index]);
      }
    },

    carConfigFn() {
      this.$refs.carConfig.showModal();
    },

    carOkConfigFn() {
      this.$refs.carOkConfig.showModal();
    },

    showModal(val) {
      this.$refs.modal.showModal(val);
    },

    run() {
      this.loading = true;
      var param = {
        id: 3,
      };
      this.$http
        .post(api.carRun, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.loading = false;
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
            console.log(res.data.msg);
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },

    switchChange(e, v) {
      var param = {
        id: v,
      };
      this.$http
        .put(api.updateStatus, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
            console.log(res.data.msg);
          }
        })
        .catch(() => {});
    },
  },
  watch: {},
  components: {
    carDialog,
    ComponentPackage: require('@/views/datagovernance/governancetheme/components/componentPackage.vue').default,
    picUpload: require('./pic-upload').default,
    carConfig: require('./car-config').default,
    bigPicUrl: require('./big-pic-url').default,
    carOkConfig: require('./car-ok-config').default,
    mSwitch: require('../components/m-switch').default,
  },
};
</script>
<style lang="less" scoped>
.view {
  position: relative;
  width: 100%;
  height: 100%;
  // padding: 24px;
  background-image: url('../../../../assets/img/thememanagement/process-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  padding: 0 20px;
  background-repeat: no-repeat;
  .flexUi {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: calc(100% - 243px);
    align-items: center;
    top: 50%;
    transform: translateY(-50%);
    .item {
      position: relative;
      width: calc(23% - 100px);
      min-height: 92px;
      background: #0f2f59;
      border: 1px solid var(--color-primary);
      border-radius: 4px;
      padding: 20px 10px 20px 50px;
      color: #fff;
    }
    .groupItem {
      position: relative;
      min-height: 92px;
      background: #0f2f59;
      border: 1px solid var(--color-primary);
      border-radius: 4px;
      padding: 10px 10px 20px 50px;
      color: #fff;
      margin-top: 10px;
    }
    .groupItem.add {
      border: 1px dashed var(--color-primary);
      line-height: 72px;
      color: #407dbd;
      font-size: 16px;
    }
    .leftIcon {
      position: absolute;
      color: var(--color-primary);
      font-size: 30px;
      left: 12px;
      top: 16px;
    }
    .success {
      position: absolute;
      color: #43bf00;
      font-size: 14px;
      right: 10px;
      top: 10px;
    }

    .config {
      font-size: 12px;
      color: #56789c;
      position: absolute;
      right: 10px;
      bottom: 10px;
    }

    .title {
      font-size: 14px;
    }

    .desc {
      color: #99a4af;
    }

    .group1 {
      left: 300px;
      top: 150px;
      width: calc(23% - 100px);
      padding: 0 10px 10px 10px;
      border: 1px solid var(--color-primary);
    }
    .group2 {
      left: 900px;
      top: 130px;
      width: calc(23% - 100px);
      padding: 0 10px 10px 10px;
      border: 1px solid var(--color-primary);
    }

    .iswitch {
      position: absolute;
      right: 30px;
      bottom: 12px;
    }
    .nullConfig {
      right: 10px;
    }
  }
}

span.icon-font {
  position: absolute;
  background-image: linear-gradient(#0f84e9, #12a4c9);
  background-clip: text;
  left: 16px;
  font-size: 20px;
  -webkit-text-fill-color: transparent;
}

.faceprocess-right {
  position: absolute;
  right: 0;
  top: 0;
  overflow: auto;
  width: 243px;
  height: 100%;
  background-color: var(--bg-content);
}

.btns {
  position: absolute;
  right: 260px;
  top: 20px;
  button {
    margin-left: 10px;
  }
}
</style>
