<template>
    <div class="search_condition">
        <div class="search_form">
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">视频身份</p>
                </div>
                <ul class="search_content">
                    <Input v-model="queryParams.vid" placeholder='请输入' maxlength="50"></Input>
                </ul>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">开始时间</p>
                </div>
                <div class="search_content daterange">
                    <DatePicker v-model="queryParams.startTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" :clearable="false" placeholder="开始时间" transfer @on-change="timeChange"></DatePicker>
                </div>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">结束时间</p>
                </div>
                <div class="search_content daterange">
                    <DatePicker v-model="queryParams.endTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" :clearable="false" placeholder="结束时间" transfer @on-change="timeChange"></DatePicker>
                </div>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">出现频次</p>
                </div>
                <div class="search_content">
                    <span class="f-ib">{{maxFre}}次</span>
                </div>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">同行频次</p>
                </div>
                <div class="search_content">
                    <Input v-model="queryParams.minCount"></Input>
                </div>
            </div>
            <div class="search_wrapper">
                <div class="search_title">
                    <p class="search_strut">间隔时间</p>
                </div>
                <div class="search_content">
                    <Input v-model="queryParams.rangTime" class="wrapper-input"></Input><span class="tip">&nbsp;秒</span>
                </div>
            </div>
            
            <div class="btn-group">
                <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
            </div>
        </div>
    </div>
</template>

<script>
import { vidBaseStationPointCount } from '@/api/modelMarket';
export default {
    name: '',
    data () {
        return {
            maxFre: 0,
            queryParams:{
                vid: '',
                startTime: '',
                endTime: '',
                minCount: 0,
                rangTime: 20
            },
            ifFromFeature: false
        }
    },
    mounted(){
        this.queryParams.startTime = this.$dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
        this.queryParams.endTime = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
    },
    watch: {
        'queryParams.vid' (val, oldVal) {
            if(val == oldVal || (val&&val.length < 19)) return;
            if (this.queryParams.vid) {
                this.getPointCount();
            } else {
                this.maxFre = 0;
                this.queryParams.minCount = 0;
            }
        }
    },
    methods: {
        // 查询
        handleSearch() {
            console.log(this.queryParams)
            this.$emit('search', this.queryParams)
        },
        getPointCount () {
            let self = this;
            if (!self.queryParams.vid) return;
            vidBaseStationPointCount({
                vid: self.queryParams.vid,
                startTime: self.$dayjs(self.queryParams.startTime).format('YYYY-MM-DD HH:mm:ss'),
                endTime: self.$dayjs(self.queryParams.endTime).format('YYYY-MM-DD HH:mm:ss'),
                relationshipType: 2, //类型：1-电围设备与人脸抓拍设备  2-RFID设备与人脸抓拍设备
                //deviceIds: self.queryParams.deviceIds
            }).then(res => {
                if (res.code == 200) {
                    self.maxFre = res.data;
                    if (self.maxFre === 0) {
                        self.$Message.warning('该时间段内没有出行记录，请重新选择时间段');
                    } else {
                        if (self.maxFre >= 5) {
                            self.queryParams.minCount = 5;
                        } else {
                            self.queryParams.minCount = self.maxFre;
                        }
                        self.$Message.success('最高出现频次已更新!');
                    }
                }
            }).catch(err => {}).finally(() => {});
        },
        timeChange() {
            if (this.queryParams.vid && this.queryParams.startTime && this.queryParams.endTime) {
                this.getPointCount()
            } else {
                this.queryParams.minCount = 0;
            }
        },
    }
}
</script>

<style lang='less' scoped>
.search_condition{
    max-height: 620px;
    transition: max-height 0.2s ease-out;
    .search_form{
        padding: 10px 15px 10px 20px;

        .search_wrapper{
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 15px;
            .search_title{
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                margin-right: 10px;
                display: flex;
            }
            .search_strut{
                // text-align: justify;
                width: 65px;
                // text-align-last: justify;
                span{
                    color: red;
                }
            }
            .search_content{
                display: flex;
                flex-wrap: nowrap;
                flex: 1;
                align-items: center;
                &.daterange {
                    .ivu-date-picker {
                        width: 100%;
                    }
                }
                &-li{
                    font-size: 14px;
                    color: rgba(0,0,0,0.9);
                    font-weight: 400;
                    cursor: pointer;
                    width: 65px;
                    text-align: center;
                    padding: 3px 0;
                    margin-right: 15px;
                }
                &-li-active{
                    color: #fff;
                    background: #2C86F8;
                    border-radius: 2px;
                }
            }
            img {
                width: 100px;
                margin: 0 auto;
            }
        }
        .btn-group{
            .btnwidth{
                width: 330px;
            }
        }
    }
}
</style>
