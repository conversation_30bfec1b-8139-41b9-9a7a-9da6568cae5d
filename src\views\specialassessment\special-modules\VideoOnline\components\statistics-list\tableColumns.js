import { renderHeaderStatistics } from '@/views/specialassessment/utils/menuConfig.js';

const defaultTableColumns = (params) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'civilName',
      slot: 'civilName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
      renderHeader: (h, { column, index }) => {
        return renderHeaderStatistics(h, { column, index, params });
      },
    },
    {
      title: '检测数量',
      key: 'detection',
      slot: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '合格数量',
      key: 'qualified',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '不合格数量',
      key: 'unqualified',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '视频在线率',
      key: 'rate',
      slot: 'rate',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ];
};

// 重点/实时视频可调阅率、人脸卡口在线率、车辆卡口在线率
let matchingRateArr = () => {
  return [
    {
      title: '资产质量',
      minWidth: 80,
      slot: 'accuracyRate',
    },
    {
      title: '视频在线率*资产质量',
      minWidth: 100,
      key: 'comRate',
      render: (h, { row }) => {
        return (
          <span>
            {row?.detail[0]?.onlineRateVO?.comRate || row?.detail[0]?.onlineRateVO?.comRate === 0
              ? `${row?.detail[0]?.onlineRateVO?.comRate}%`
              : '--'}
          </span>
        );
      },
    },
  ];
};

const tableColumns = (params) => {
  // 视频在线
  let matchingRate = [];
  if (params.showColRegion) {
    matchingRate = matchingRateArr(params);
  }
  return {
    BASIC_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '合格数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '不合格数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '视频在线率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    FACE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '合格数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '不合格数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '视频在线率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    VEHICLE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '合格数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '不合格数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '视频在线率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    VIDEO_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '合格数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '不合格数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '视频在线率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    VIDEO_PLAYING_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        slot: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '在线数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '离线数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '视频在线率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      ...matchingRate,
    ],
    VIDEO_HISTORY_ACCURACY: defaultTableColumns(params),
    VIDEO_OSD: defaultTableColumns(params),
    VIDEO_CLOCK: defaultTableColumns(params),
    VIDEO_PASS: defaultTableColumns(params),
    VIDEO_HISTORY_COMPLETE: defaultTableColumns(params),
    FACE_CLOCK_QUALITY: defaultTableColumns(params),
    VEHICLE_CLOCK_QUALITY: defaultTableColumns(params),
    FACE_DELAY: defaultTableColumns(params),
    VEHICLE_DELAY: defaultTableColumns(params),
    VIDEO_CODE_STANDARD_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        slot: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '编码规范数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '编码不规范数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '视频编码规范率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ], //编码规范
    FACE_DEVICE_CONNECT_INTERNET: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        slot: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '活跃数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '非活跃数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '卡口活跃率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    VEHICLE_DEVICE_CONNECT_INTERNET: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        slot: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '活跃数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '非活跃数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '卡口活跃率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    // 人体
    BODY_ACTIVE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        slot: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '活跃数量',
        key: 'qualified',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '非活跃数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '卡口活跃率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    BODY_UPLOAD: defaultTableColumns(params),
    BODY_CLOCK: defaultTableColumns(params),
  };
};
export { tableColumns };
