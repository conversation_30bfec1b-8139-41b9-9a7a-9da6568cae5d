<template>
  <ui-modal v-model="visible" :title="title" :styles="styles">
    <div class="mode-device-wrapper" v-ui-loading="{ loading: deviceListLoading }">
      <div class="message-wrapper">
        <div class="message-wrapper-left mb-sm">
          <div class="mr-lg">
            <span class="base-text-color inline mr-sm vt-middle">
              设备编码：
              <span class="copy-text">{{ activeDeviceItem.deviceId }}</span>
            </span>
            <i class="icon-font icon-fuzhi" title="复制" @click="copyText(activeDeviceItem.deviceId)"></i>
          </div>
          <div>
            <span class="base-text-color inline mr-sm vt-middle">
              设备名称：
              <span class="copy-text">{{ activeDeviceItem.deviceName }}</span>
            </span>
            <i class="icon-font icon-fuzhi" title="复制" @click="copyText(activeDeviceItem.deviceName)"></i>
          </div>
        </div>
        <div class="message-wrapper-left mb-sm new-status-box">
          <span class="base-text-color inline mr-lg vt-middle new-status-box-one">
            最新状态：
            <span :class="[getNewStatus(activeDeviceItem[filedNameMap.qualified]).className]">
              {{ getNewStatus(activeDeviceItem[filedNameMap.qualified]).text }}
              {{ activeDeviceItem.dataMode === '3' ? `(人工)` : '' }}
            </span>
          </span>
          <span
            class="base-text-color inline mr-lg vt-middle new-status-box-item-top"
            :title="activeDeviceItem[filedNameMap.description]"
          >
            原因：
            <span
              class="ellipsis error-description inline"
              :class="[getNewStatus(activeDeviceItem[filedNameMap.qualified]).className]"
            >
              {{ activeDeviceItem[filedNameMap.description] }}
            </span>
          </span>
        </div>
        <div class="message-wrapper-left mb-sm">
          <span class="base-text-color inline vt-middle">设备复核：</span>
          <RadioGroup v-model="deviceReviewStatus">
            <Radio class="mr-lg" :label="item.value" v-for="(item, index) in qualifiedList" :key="index">
              {{ item.key }}
            </Radio>
          </RadioGroup>
        </div>
        <div class="message-wrapper-right">
          <Input
            v-model="activeDeviceDescription"
            type="textarea"
            class="desc"
            placeholder="请输入备注信息"
            :rows="3"
            :maxlength="180"
          ></Input>
        </div>
      </div>
      <div
        class="image-wrapper"
        v-ui-loading="{
          loading: deviceImgloading || deviceBtnloading.nextBtn || deviceBtnloading.preBtn,
          noShowImg: true,
        }"
      >
        <div class="image-wrapper-left mr-md">
          <div class="image-carousel-wrapper" id="wrapper-box" v-ui-loading="{ tableData: carouselList }">
            <image-carousel
              class="image-carousel"
              :img-list="carouselList"
              :view-index="activePicItemIndex"
              @changeCarousel="changeCarousel"
              @clickImage="clickImage"
            >
              <template #totalNum="{ currentNum }">
                <div class="image-num-box">{{ currentNum }} / {{ carouselList.length }}</div>
              </template>
            </image-carousel>
            <tag
              :tag-bg-color="handleTagColor(activePicItem[filedNameMap.qualified])"
              :tag-text="handleTagText(activePicItem[filedNameMap.qualified])"
              v-if="!!activePicItem?.[filedNameMap.qualified]"
            ></tag>
            <div
              class="small-pic drag-small-pic"
              v-if="smallShow && carouselList.length"
              v-drag:allTime="'wrapper-box'"
            >
              <ui-image :src="activePicItem[filedNameMap.smallPicName] || ''" :key="imgKey" />
            </div>
          </div>
          <div class="wrapper-title wrapper-img-box">
            <div class="message-wrapper-left mb-sm" v-if="showVehicleInfo">
              <template v-if="showVehicleColor">
                <span class="base-text-color inline mr-lg vt-middle">车牌号：{{ activePicItem.plateNo }}</span>
                <span class="base-text-color inline mr-lg vt-middle">
                  车牌颜色：
                  {{
                    $options.filters.filterType(
                      activePicItem.plateColor,
                      this.colorType,
                      'dictKey',
                      'dictValue',
                      '缺失',
                    )
                  }}
                </span>
              </template>
              <template v-if="showVehicleType">
                <span class="base-text-color inline mr-lg vt-middle">
                  车辆类型：
                  {{
                    $options.filters.filterType(
                      activePicItem.vehicleClass,
                      this.vehicleClassType,
                      'dictKey',
                      'dictValue',
                      '缺失',
                    )
                  }}
                </span>
                <span class="base-text-color inline mr-lg vt-middle">
                  车辆品牌：
                  {{
                    $options.filters.filterType(
                      activePicItem.vehicleBrand,
                      this.vehicleBandType,
                      'dictKey',
                      'dictValue',
                      '缺失',
                    )
                  }}
                </span>
              </template>
              <span
                class="base-text-color inline mr-lg vt-middle pointer link-text-box"
                v-if="activePicItem.id"
                @click="$emit('algorithmsReview', activePicItem)"
                >查看算法结果</span
              >
            </div>
            <div class="message-wrapper-left mb-sm new-status-box">
              <span class="base-text-color inline mr-lg vt-middle new-status-box-one">
                最新状态：
                <span :class="[getNewStatus(activePicItem[filedNameMap.qualified]).className]">
                  {{ getNewStatus(activePicItem[filedNameMap.qualified]).text }}
                </span>
              </span>
              <span
                class="base-text-color inline mr-lg vt-middle new-status-box-item"
                :title="activePicItem[filedNameMap.resultTip]"
              >
                原因：
                <span
                  class="ellipsis inline error-description"
                  :class="[getNewStatus(activePicItem[filedNameMap.qualified]).className]"
                >
                  {{ activePicItem[filedNameMap.resultTip] }}
                </span>
                <span
                  class="base-text-color inline ml-sm vt-middle pointer link-text-box"
                  v-if="activePicItem[filedNameMap.resultTip] && activePicItem[filedNameMap.qualified] === '2'"
                  @click="clickImage"
                >
                  不合格详情
                </span>
              </span>
            </div>
            <div class="message-wrapper-left mb-sm wrapper-status">
              <span class="base-text-color inline vt-middle">复核当前选中图片：</span>
              <RadioGroup v-model="checkedImgStatusObj[activePicItem.id]" @on-change="handleChangeQualified">
                <Radio class="mr-lg" label="1"> 可用 </Radio>
                <!-- 展示不合格原因 -->
                <Tooltip>
                  <Radio class="mr-lg" label="2"> 不可用 </Radio>
                  <template #content>
                    <CheckboxGroup v-model="checkedImgErrorObj[activePicItem.id]" @on-change="handleUnQualified">
                      <Checkbox :label="item.value" v-for="(item, index) in errorCodeList" :key="index">
                        {{ item.label }}
                      </Checkbox>
                    </CheckboxGroup>
                  </template>
                </Tooltip>
              </RadioGroup>
            </div>
          </div>
        </div>
        <div class="image-wrapper-right">
          <ul class="pic-list-ul mt-sm" v-ui-loading="{ tableData: imgList }">
            <li class="" v-for="(item, index) in imgList" :key="`${index}-${item.id}`" @click="changeCarousel(index)">
              <ui-image
                :src="item[filedNameMap.bigPicName]"
                class="img-border"
                :class="{ 'active': item.id === activePicItem?.id }"
              />
              <div class="small-pic" :class="{ 'small-pic-active': item.id === activePicItem?.id }" v-if="smallShow">
                <ui-image :src="item[filedNameMap.smallPicName] ?? ''" />
              </div>
              <tag
                size="small"
                :tag-bg-color="handleTagColor(item[filedNameMap.qualified])"
                :tag-text="handleTagText(item[filedNameMap.qualified])"
              ></tag>
            </li>
          </ul>
          <div class="wrapper-title">
            <Checkbox class="fl" v-model="smallShow">
              <span class="table-text-content"> 显示小图</span>
            </Checkbox>
            <div class="d_flex">
              <p class="small-tab mr-lg">
                <span class="active mr-md" v-show="showPreImgButton" @click="preImgList">上一批</span>
                <span v-show="showNextImgButton" @click="nextImgList">下一批</span>
              </p>
              <Dropdown @on-click="changePicFilter">
                <a href="javascript:void(0)">
                  <Icon type="md-arrow-dropdown" />
                  {{ activeFilter === '2' ? '不合格图片' : '全部图片' }}
                </a>
                <DropdownMenu slot="list">
                  <DropdownItem
                    v-for="(item, index) in filterList"
                    :key="index"
                    :name="item.value"
                    :selected="item.value === activeFilter"
                    >{{ item.name }}</DropdownItem
                  >
                </DropdownMenu>
              </Dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template slot="footer">
      <Button
        :loading="deviceBtnloading.preBtn"
        type="primary"
        class="plr-30"
        @click="preDevice"
        v-show="showPreButton"
      >
        上一条设备
      </Button>
      <Button class="plr-30" type="primary" @click="artificialDevice('cur')" :loading="saveModalLoading">
        确定复核结果
      </Button>
      <Button
        class="plr-30"
        type="primary"
        @click="artificialDevice('next')"
        v-show="showNextButton"
        :loading="saveModalLoading"
      >
        确定复核结果并下一条
      </Button>
      <Button
        :loading="deviceBtnloading.nextBtn"
        type="primary"
        class="plr-30"
        @click="nextDevice"
        v-show="showNextButton"
      >
        下一条设备
      </Button>
    </template>
    <!-- 查看大图 -->
    <AlgorithmLookScene
      class="look-scene-modal"
      v-model="visibleScence"
      :img-list="imgList"
      :view-index="activePicItemIndex"
      :filed-name-map="filedNameMap"
    ></AlgorithmLookScene>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';
import copyText from '@/mixins/copyText.js';
import { mapGetters } from 'vuex';

export default {
  name: 'base-review',
  props: {
    custormParams: {}, // 人工复核需要参数
    tableData: {}, // 设备列表
    pageData: {},
    totalCount: {}, // 外部列表的总数
    activeIndexItem: {},
    qualifiedList: {}, // 设备的
    reviewRowData: {}, // 当前的设备
    title: {
      type: String,
      default: '人工复核',
    },
    value: {
      type: Boolean,
      default: false,
    },
    styles: {
      type: Object,
      default: () => {
        return {
          width: '9rem',
        };
      },
    },
    // 自定义字段名称 [兼容车辆和人脸字段不一致问题]
    filedNameMap: {
      default: () => {
        // 默认以人脸为主
        return {
          smallPicName: 'facePath', // 小图
          bigPicName: 'scenePath', // 大图
          qualified: 'qualified', // 不合格
          description: 'reason', // 设备备注
          resultTip: 'resultTip', // 图片备注
          deviceDetailId: 'faceDeviceDetailId', // 单个图片的id
        };
      },
    },
    // 设备列表搜索条件
    searchParames: {
      type: Object,
    },
    // 调用方处理， 主要是 为了保持与原有的新增字段逻辑一直，确保字段没有丢失 , 目前整理 只有两个字段： 显示的不合格原因、 是否合格字段
    customValueField: {
      type: Object,
      default: () => {
        return {
          reason: 'errorCodeName', //  自定义字段： 取值字段
          qualified: 'outcome',
        };
      },
    },
    // 获取列表的接口 --- 若调用接口不一样，则需要调用方自行处理
    getListApi: {
      type: Function,
    },
  },
  mixins: [copyText],
  computed: {
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
    }),
    carouselList() {
      return this.imgList.map((item) => item[this.filedNameMap.bigPicName]);
    },
    showPreButton() {
      // 是第一页 && 是第一条设备 才不展示
      return this.cacheDeviceList.length && !(this.currentPageNum === 1 && this.currentIndexByCache === 0);
    },
    showNextButton() {
      // 最后一页，并且是最后一条设备才不展示
      return this.cacheDeviceList.length && !this.activeDeviceItem.isLastPage;
    },
    showPreImgButton() {
      let pageNum = this.pageImgData.pageNum;
      return pageNum > 1;
    },
    showNextImgButton() {
      let { isEndPage } = this.pageImgData;
      return !isEndPage;
    },
    // 设备的最新状态
    getNewStatus() {
      return (str) => {
        return {
          text: str == '1' ? '合格' : '不合格',
          className: str == '1' ? 'c-green' : 'c-red',
        };
      };
    },
    // 车辆信息
    showVehicleInfo() {
      return this.showVehicleColor || this.showVehicleType;
    },
    showVehicleColor() {
      const { indexType } = this.$route.query;
      // 重点车辆卡口设备过车数据准确率、车辆卡口设备过车数据准确率、重点车辆卡口设备主要属性准确率
      return ['VEHICLE_INFO_PASS_IMPORTANT', 'VEHICLE_INFO_PASS', 'VEHICLE_MAIN_PROP'].includes(indexType);
    },
    showVehicleType() {
      const { indexType } = this.$route.query;
      // 重点车辆卡口设备类型属性识别准确率
      return ['VEHICLE_TYPE_PROP'].includes(indexType);
    },
  },
  data() {
    return {
      reviewImgLoading: false,
      saveModalLoading: false,
      smallShow: true,
      visible: false,
      errorCodeList: [],
      filterList: [
        { name: '不合格图片', value: '2' },
        { name: '全部图片', value: '0' },
      ],
      // imgQualifiedList: [
      //   { key: '合格', value: '1' },
      //   { key: '不合格', value: '2' }
      // ],
      activePicItem: {
        [this.filedNameMap.qualified]: '1',
      }, // 当前处理的图片
      activePicItemIndex: 0, // 当前处理图片的index
      activeDeviceItem: {}, // 当前处理的设备
      activeFilter: '1',
      pageImgData: {
        // 一批图片
        pageNum: 1,
        pageSize: 12,
        totalCount: 0,
        isEndPage: false,
      },
      deviceList: [], // 设备列表
      totalImgCount: 0, // 图片总共数量
      imgList: [],
      deviceImgloading: false,
      deviceBtnloading: {
        preBtn: false,
        nextBtn: false,
      },
      deviceReviewStatus: '', // 设备复核状态， 注：以 最新状态 反选
      activeDeviceDescription: '', // 备注信息
      reviewImgList: [], // 有改动过的图片且需要复核的列表
      checkedImgErrorObj: {}, // 勾选  不可用的原因 列表
      checkedImgStatusObj: {}, // 勾选 复核当前选中图片 状态
      imgKey: 0,
      // 查看大图
      visibleScence: false,

      // 一页一条数据的请求方案： 需要维护一个  isUnExistTable: Boolean， 代表该数据是否已经不符合当前筛选条件下的数据了  true: 与条件不符合，已不存在   false: 满足条件，存在
      // currentDeviceIndex: 0,
      cacheDeviceList: [], // 只有请求接口才会 缓存起来
      currentIndexByCache: 0,
      currentPageNum: 1, // 当前页码（基于总数算，对于一页一条数据去请求，该字段其实也代表了在总数据中第几条数据）
      isReview: false, // 是否复核过
      devicePageData: {
        pageSize: 50,
      },
      deviceListLoading: false,
    };
  },
  mounted() {
    // 图片
    this.getQualificationList(2).then((data) => {
      this.errorCodeList = data.map((item) => {
        return { value: item.key, label: item.value };
      });
    });
  },
  methods: {
    async initFn() {
      this.isReview = false;

      // 该条数据 在 外部表格中的索引
      let index = this.tableData.findIndex(
        (item) => item.deviceId === this.reviewRowData.deviceId && item.id === this.reviewRowData.id,
      );
      // 该条数据 在 总数中的索引
      this.currentPageNum = (this.pageData.pageNum - 1) * this.pageData.pageSize + index + 1;

      // 默认查询 该条数据所在页码对应的 devicePageData.pageSize条数据
      this.cacheDeviceList = [];
      let res = await this.getDeviceList();
      this.cacheDeviceList = res.entities;
      this.currentIndexByCache = this.cacheDeviceList.findIndex(
        (item) => item.deviceId === this.reviewRowData.deviceId && item.id === this.reviewRowData.id,
      );

      let deviceItem = this.cacheDeviceList.filter(
        (item) => item.deviceId === this.reviewRowData.deviceId && item.id === this.reviewRowData.id,
      );
      this.activeDeviceItem = { ...deviceItem[0] };

      this.getDeviceImgList();
    },
    getErrorCodeList() {
      let text = '';
      let causeError = this.activePicItem.causeError;
      let qualified = this.activePicItem[this.filedNameMap.qualified];
      if (causeError && qualified === '2') {
        causeError = causeError.split(',');
        this.errorCodeList.map((item) => {
          if (causeError.includes(item.value)) {
            text += text ? ` / ${item.label}` : item.label;
          }
        });
      }
      return text;
    },
    // 异常原因 - 1是设备，2是图片
    async getQualificationList(mode = 1) {
      let params = {
        indexType: this.$route.query.indexType,
        model: mode,
      };
      try {
        let res = await this.$http.get(detectionResult.getUnqualifiedInfo, {
          params: params,
        });
        return res.data.data || {};
      } catch (err) {
        console.log(err);
      }
    },
    handleTagColor(qualified) {
      let color = qualified === '1' ? 'var(--color-success)' : 'var(--color-failed)';
      return color;
    },
    handleTagText(qualified) {
      return qualified === '1' ? '合格' : '不合格';
    },
    handleChangeQualified(val) {
      let { id } = this.activePicItem;
      // 点击 可用
      if (val === '1') {
        this.checkedImgErrorObj[id] = [];
      }
      this.$set(this.checkedImgStatusObj, id, val);
      if (!this.reviewImgList.some((item) => item.id === id)) {
        this.reviewImgList.push(this.activePicItem);
      }
    },
    handleUnQualified(val) {
      let { id } = this.activePicItem;
      this.$set(this.checkedImgStatusObj, id, '2');
      if (!this.reviewImgList.some((item) => item.id === id)) {
        this.reviewImgList.push(this.activePicItem);
      }
      this.$set(this.checkedImgErrorObj, id, val);
    },
    changePicFilter(val) {
      this.activeFilter = val;
      this.getDeviceImgList();
    },
    preImgList() {
      this.artificialBulkImg();
      this.pageImgData.pageNum--;
      this.getDeviceImgList();
    },
    nextImgList() {
      this.artificialBulkImg();
      this.pageImgData.pageNum++;
      this.getDeviceImgList();
    },
    changeCarousel(index) {
      this.activePicItemIndex = index;
      this.activePicItem = this.imgList?.[index] || {};
      this.imgKey++;
    },
    // 获取当前设备的图片列表
    async getDeviceImgList() {
      try {
        let copyimgList = this.$util.common.deepCopy(this.imgList);
        let { pageSize, pageNum } = this.pageImgData;
        const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          customParameters: {
            deviceIds: this.activeDeviceItem.deviceId,
            faceDeviceDetailId: this.activeDeviceItem.id, // 设备的id
          },
          pageNumber: pageNum,
          pageSize: pageSize,
          excludeTotal: true, // 不计算total
        };

        this.deviceImgloading = true;
        this.activePicItem = {};
        this.imgList = [];
        // 1,是合格，2不,
        this.activeFilter === '2' ? (data.customParameters.outcome = this.activeFilter) : null;
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, data);
        // let res = await this.$http.post(evaluationoverview.getPolyData, data)

        // 备注：后端不返回total后，前端在请求完接口后，才能知道是否为 最后一页，这要特殊处理下
        let arr = res.data.data.entities ?? [];
        if (arr.length === 0 || arr.length < pageSize) {
          this.pageImgData.isEndPage = true;
        } else {
          this.pageImgData.isEndPage = false;
        }
        if (pageNum !== 1 && arr.length === 0) {
          this.imgList = copyimgList;
          this.pageImgData.pageNum--;
          this.$Message.warning('已经是最后一批！');
        } else {
          this.imgList = arr;
          this.changeCarousel(0);
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.deviceImgloading = false;
      }
    },
    async preDevice() {
      let { nextBtn } = this.deviceBtnloading;
      if (this.saveModalLoading || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }

      try {
        this.deviceBtnloading.preBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.currentIndexByCache - 1]?.isUnExistTable) {
          this.currentPageNum--;
        }

        if (this.currentIndexByCache === 0) {
          // 已经不存在上一条，则调接口
          let res = await this.getDeviceList();
          let endIndex = res.entities.length - 1;
          this.activeDeviceItem = res.entities[endIndex];
          // 注意： 要添加到数组前面
          this.cacheDeviceList = [...res.entities, ...this.cacheDeviceList];
          this.currentIndexByCache = endIndex;
        } else {
          this.currentIndexByCache--;
          this.activeDeviceItem = this.cacheDeviceList[this.currentIndexByCache];
        }
        this.getDeviceImgList('preBtn');
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.preBtn = false;
      }
    },
    async nextDevice() {
      let { preBtn } = this.deviceBtnloading;
      if (this.saveModalLoading || preBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      try {
        this.deviceBtnloading.nextBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.currentIndexByCache].isUnExistTable) {
          this.currentPageNum++;
        }
        this.currentIndexByCache++;

        if (this.cacheDeviceList[this.currentIndexByCache]) {
          this.activeDeviceItem = this.cacheDeviceList[this.currentIndexByCache];
        } else {
          // 缓存中不存在下一条数据，则调接口
          let res = await this.getDeviceList();
          this.activeDeviceItem = { ...res.entities[0] };
          // 注意： 要添加到数组后面
          this.cacheDeviceList = [...this.cacheDeviceList, ...res.entities];
        }
        this.getDeviceImgList('nextBtn');
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.nextBtn = false;
      }
    },
    // 批量图片复核
    async artificialBulkImg() {
      if (!this.reviewImgList.length) {
        return;
      }

      this.reviewImgLoading = true;
      let params = {
        dataList: this.reviewImgList.map((item) => {
          return {
            id: item.id,
            qualified: this.checkedImgStatusObj[item.id],
            // reason: this.resultTip,vehicleDetailId
            deviceDetailId: item[this.filedNameMap.deviceDetailId],
            deviceId: item.deviceId,
            errorCode: this.checkedImgErrorObj[item.id]?.join(',') || '',
            type: 'detail',
          };
        }),
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheckBatch, params);
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      } finally {
        this.reviewImgLoading = false;
      }
    },
    async artificialDevice(str = '') {
      let { preBtn, nextBtn } = this.deviceBtnloading;
      if (preBtn || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      let params = {
        data: {
          id: this.activeDeviceItem.id,
          qualified: this.deviceReviewStatus,
          reason: this.activeDeviceDescription,
          type: 'device',
          taskIndexId: this.activeDeviceItem.taskIndexId,
          deviceId: this.activeDeviceItem.deviceId,
          indexType: this.$route.query.indexType,
        },
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      let isSuccess = false;
      try {
        this.saveModalLoading = true;
        let res = await this.$http.post(evaluationoverview.manualRecheck, params);
        this.isReview = true;
        this.$Message.success(res.data.msg);
        isSuccess = true;

        if (this.reviewImgList.length) {
          await this.artificialBulkImg();
        }

        // 复核成功后，需要 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
        this.updateInfo(str);
      } catch (err) {
        console.log(err);
      } finally {
        this.saveModalLoading = false;
        // 确定复核并下一条  触发
        if (str && isSuccess) {
          this.clearData();
          if (str === 'next') {
            this.nextDevice();
          }
        }
      }
    },
    clearData() {
      this.activeDeviceDescription = '';
      this.checkedImgStatusObj = {};
      this.checkedImgErrorObj = {};
      this.reviewImgList = [];
    },
    // 查看大图
    clickImage() {
      this.visibleScence = true;
    },
    // 获取设备数据
    async getDeviceList(customParams, isUpdate = false) {
      try {
        if (!isUpdate) {
          this.deviceListLoading = true;
        }
        let res = {};
        let { pageSize } = this.devicePageData;
        // 使用调用方参入的函数
        if (this.getListApi) {
          let params = {
            pageSize: pageSize,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            isUpdate: isUpdate,
            currentRow: { ...this.activeDeviceItem },
          };
          res = await this.getListApi(params);
        } else {
          let { regionCode, orgCode, statisticType, indexId, batchId } = this.$route.query;
          let params = {
            indexId: indexId,
            batchId: batchId,
            access: 'TASK_RESULT',
            displayType: statisticType,
            orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
            customParameters: this.searchParames,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            pageSize: pageSize,
          };
          // 以传入参数为主
          if (customParams) {
            params = { ...params, ...customParams };
          }
          res = await this.$http.post(evaluationoverview.getDetailData, params);
        }

        let { entities, lastPage } = res.data.data;

        if (!isUpdate) {
          // 注意： 如果中间存在 isUnExistTable=true , 那此时查询到的列表数据，存在部分数据已缓存过了 --- 每页一条查询是不会的，但现在支持根据 devicePageData.pageSize可配置
          entities = entities.filter(
            (item) =>
              !this.cacheDeviceList.some(
                (cacheItem) => cacheItem.id === item.id && cacheItem.deviceId === item.deviceId,
              ),
          );
        }

        entities = entities.map((item, index) => {
          // 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
          if (this.customValueField) {
            Object.keys(this.customValueField).forEach((key) => {
              item[key] = item[this.customValueField[key]];
            });
          }
          return {
            ...item,
            isLastPage: lastPage && index === entities.length - 1, // 是否为最后一条数据
          };
        });
        res.data.data.entities = entities;

        return res.data.data;
      } catch (error) {
        console.log(error);
        throw new Error();
      } finally {
        this.deviceListLoading = false;
      }
    },
    // 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
    async updateInfo(str = '') {
      if (this.searchParames?.outcome && this.searchParames?.outcome !== this.deviceReviewStatus) {
        // 【检查结果】 刷选条件有值，并且与  当前设备复核结果不同
        this.cacheDeviceList[this.currentIndexByCache].isUnExistTable = true;
        this.activeDeviceItem.isUnExistTable = true;

        // 主要是 下面调接口更新需要时间， 接口完成前 先前端替换， 这样就不会影响到上一条下一条显示数据没有更新问题 ， 且也不会影响到 关闭弹框时所做的判断
        // 其实就是 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
        if (this.customValueField) {
          this.cacheDeviceList[this.currentIndexByCache][this.customValueField.qualified] = this.deviceReviewStatus;
          this.cacheDeviceList[this.currentIndexByCache][this.customValueField.reason] = this.activeDeviceDescription;
          this.cacheDeviceList[this.currentIndexByCache].dataMode = '3';
          this.activeDeviceItem[this.customValueField.qualified] = this.deviceReviewStatus;
          this.activeDeviceItem[this.customValueField.reason] = this.activeDeviceDescription;
          this.activeDeviceItem.dataMode = '3';
        }
      }
      let { deviceId, isLastPage } = this.activeDeviceItem;
      let params = {
        customParameters: {
          deviceId: deviceId,
        },
        pageNumber: 1,
      };
      let res = await this.getDeviceList(params, true);
      let obj = res.entities[0];
      // 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
      if (this.customValueField) {
        Object.keys(this.customValueField).forEach((key) => {
          obj[key] = obj[this.customValueField[key]];
        });
      }
      let currentIndexByCache = this.cacheDeviceList.findIndex(
        (item) => item.deviceId === obj.deviceId && item.id === obj.id,
      );
      let info = {
        ...this.cacheDeviceList[currentIndexByCache],
        ...obj,
        isLastPage: isLastPage, // 保留之前的状态值
      };
      this.cacheDeviceList[currentIndexByCache] = info;
      if (str === 'cur' && this.activeDeviceItem.id === obj.id && this.activeDeviceItem.deviceId === obj.deviceId) {
        this.activeDeviceItem = info;
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.initFn();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        let closeIndex = this.currentPageNum - 1;
        // 特殊处理，关闭弹框时，如果是 最后一条数据 且 复核 的情况
        let { isLastPage } = this.activeDeviceItem;
        if (
          isLastPage &&
          this.searchParames?.outcome &&
          this.searchParames?.outcome !== this.activeDeviceItem[this.customValueField.qualified]
        ) {
          closeIndex = closeIndex - 1;
        }
        this.$emit('closeFn', {
          isReview: this.isReview,
          closeIndex: closeIndex >= 0 ? closeIndex : 0,
        });
      }
    },
    activeDeviceItem: {
      handler() {
        let str = this.activeDeviceItem[this.filedNameMap.qualified];
        this.deviceReviewStatus = str == '1' ? '2' : '1';
        this.clearData();
      },
      deep: true,
    },
  },
  components: {
    ImageCarousel: require('@/components/image-carousel.vue').default,
    Tag: require('../face-vehicle-url/tag.vue').default,
    AlgorithmLookScene:
      require('@/views/governanceevaluation/evaluationoResult/components/pic-algorithm/look-scene.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .image-wrapper-right {
    border: 0 !important;
  }
}
.ui-image .ui-image-div img {
  width: 100%;
}
@{_deep}.ivu-modal-body {
  padding: 0 20px;
}
@{_deep}.look-scene-modal .ivu-modal-body {
  padding: 0 !important;
}
@{_deep}.image-carousel {
  height: 100%;
  .img-list {
    margin-top: 0 !important;
    height: 100%;
  }
  .content {
    cursor: pointer;
  }
}
.image-wrapper {
  width: 100%;
  height: 575px;
  display: flex;
  padding: 10px;
  .image-num-box {
    position: absolute;
    bottom: 13px;
    z-index: 10;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.4);
    padding: 1px 8px;
    color: #ffffff;
    font-size: 12px;
  }
  .wrapper-title {
    width: 100%;
    // height: 48px;
    display: flex;
    align-items: center; /*垂直交叉轴居中*/
    background: var(--bg-form-item);
    padding: 15px 20px;
    position: relative;
    bottom: 0;
    @{_deep}.ivu-tooltip-popper {
      // top: -120px !important;
      // left: 74px !important;
    }
    .ivu-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      width: 385px;
      .ivu-checkbox-wrapper {
        width: 47%;
        position: relative;
      }
    }
    &.wrapper-img-box {
      flex-direction: column;
      align-items: flex-start;
    }
    .wrapper-status {
      position: relative;
    }
  }

  .small-tab {
    color: #fff;
    cursor: pointer;
    > span:hover {
      color: var(--color-primary);
    }
  }
  .small-pic {
    position: absolute;
    left: 1px;
    top: 1px;
    width: 20%;
    &.small-pic-active {
      left: 2px;
      top: 2px;
    }
  }
  .drag-small-pic {
    z-index: 3;
    & > .ui-image {
      border: 1px solid var(--color-primary);
    }
  }
  .image-wrapper-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    .image-carousel-wrapper {
      position: relative;
      flex: 1;
      overflow: hidden;
      border: 1px solid var(--devider-line);
    }
  }
  .image-wrapper-right {
    width: 45%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border: 1px solid var(--devider-line);
    .wrapper-title {
      justify-content: space-between;
    }
    .pic-list-ul {
      display: flex;
      flex-wrap: wrap;
      max-height: calc(100% - 60px);
      overflow-y: scroll;
      > li {
        width: 31%;
        height: 145.75px;
        margin-bottom: 10px;
        margin-right: 10px;
        position: relative;
        overflow: hidden;
        .img-border {
          border: 1px solid #10457e;
        }
        .active {
          border: 2px solid var(--color-primary);
        }
      }
      @{_deep}.no-image {
        width: 100%;
        height: 100%;
      }
    }
  }
}
.mode-device-wrapper {
  border: 1px solid var(--devider-line);
  margin-bottom: 10px;
}
.message-wrapper {
  // display: flex;
  margin: 10px 10px 0;
  padding: 10px 20px;
  background: var(--bg-form-item);
  .message-wrapper-left {
    // width: 25%;
    position: relative;
    display: flex;
    align-items: center;
    //   .title {
    //   color: var(--color-primary);
    //   font-size: 14px;
    //   margin-left: 10px;
    //   margin-bottom: 20px;
    //   position: relative;
    //   &::after {
    //     position: absolute;
    //     content: '';
    //     width: 4px;
    //     height: 15px;
    //     left: -10px;
    //     top: 3px;
    //     background: var(--color-primary);
    //   }
    // }
    .icon-fuzhi {
      color: var(--color-primary);
      font-size: 15px;
    }
  }
  .message-wrapper-right {
    flex: 1;
  }
}
.new-status-box {
  display: flex;
  .error-description {
    max-width: calc(100% - 50px);
  }
  .new-status-box-one {
    max-width: 180px;
  }
  .new-status-box-item-top {
    width: calc(100% - 230px);
    display: flex;
  }
  .new-status-box-item {
    width: 650px;
    display: flex;
    .error-description {
      max-width: calc(100% - 130px);
    }
  }
}
@{_deep}.ivu-btn.ivu-btn-loading {
  .ivu-icon-ios-loading {
    position: absolute;
    left: 12px;
    top: 5px;
  }
  .ivu-icon + span {
    margin-left: 0 !important;
  }
}
</style>
