<template>
  <div class="alarm">
    <div v-if="list.length > 0" class="my-swiper-container" id="mySwiper">
      <swiper ref="mySwiper" :options="swiperOption" class="my-swiper">
        <template v-for="(item, index) in list">
          <swiper-slide
            :key="index"
            @click.native="handleDetailFn(item, index)"
          >
            <div class="swiper-item">
              <juvenile-alarm
                :isShowCheckBox="false"
                @collection="collection"
                :data="item"
              ></juvenile-alarm>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="alarmLeft"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="alarmRight"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <AlarmDetail
      v-if="detailShow"
      :tableList="list"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      :compareType="compareType"
      ref="alarmDetail"
      @close="close"
    ></AlarmDetail>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import juvenileAlarm from "@/views/juvenile/components/collect/juvenileAlarm.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import AlarmDetail from "@/views/juvenile/components/detail/alarm-detail.vue";
import { getFaceAlarmPageList } from "@/api/monographic/juvenile.js";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
    juvenileAlarm,
    AlarmDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 1.235,
        centeredSlides: true,
        speed: 1000,
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch:
            (((420 / 1920) * window.innerWidth) / 1.235) *
            0.806 *
            (window.innerWidth / 1920), // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 50, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: "#alarmRight",
          prevEl: "#alarmLeft",
        },
        observer: true,
        observeParents: true,
      },

      alarmConfigInfo: {},
      total: 0,
      detailShow: false,
      vehicleAlarmShow: false,
      alarmInfo: {},
      tableIndex: 0,
      list: [],
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      targetObj: "systemParam/targetObj",
    }),
  },
  async created() {},
  mounted() {
    window.addEventListener("resize", () => {
      if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
        // this.$refs.mySwiper.swiper.params.coverflowEffect.stretch = window.innerWidth*window.innerWidth*0.0000743
        this.$refs.mySwiper.swiper.params.coverflowEffect.stretch =
          (((420 / 1920) * window.innerWidth) / 1.235) *
          0.806 *
          (window.innerWidth / 1920);
        this.$refs.mySwiper.swiper.update();
      }
    });
    this.getAlarmList();
  },
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.tableIndex = index;
      this.detailShow = true;
      this.alarmInfo = item;
    },
    collection(params, flag) {
      if (flag == 1) {
        addCollection(params).then((res) => {
          this.$Message.success("收藏成功");
        });
      } else {
        deleteMyFavorite(params).then((res) => {
          this.$Message.success("取消收藏成功");
        });
      }
    },
    close() {
      this.detailShow = false;
    },
    getAlarmList() {
      this.loading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        idCardNo: this.idCardNo,
      };
      getFaceAlarmPageList(param)
        .then(({ data }) => {
          let arr = data.entities || [];
          arr.forEach((item) => {
            let info = this.targetObj.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
          this.list = arr;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;
  span {
    color: #2c86f8;
  }
}
.my-swiper-container {
  padding: 0 20px;
  position: relative;
  .my-swiper {
    margin: auto;
    .swiper-item {
      width: 100%;
      height: 200px;
    }
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;
  .iconfont {
    color: #fff;
    font-size: 18px;
  }
  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
  &:active {
    background: rgba(0, 0, 0, 1);
  }
}
.swiper-button-prev {
  transform: rotate(180deg);
  left: 20px;
}
.swiper-button-next {
  right: 20px;
}
</style>
