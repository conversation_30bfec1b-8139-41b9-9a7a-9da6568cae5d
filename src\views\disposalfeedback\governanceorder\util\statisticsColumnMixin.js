import { tableColumnsUnitstatistics, tableColumnsTypedetails } from './enum.js';
import governancetask from '@/config/api/governancetask';
import { mapGetters } from 'vuex';
const mixin = {
  data() {
    return {
      getColumnLoading: false, //获取Column的loading
      unitInitColumns: tableColumnsUnitstatistics, //单元初始列
      typeInitColumns: tableColumnsTypedetails, //类型初始列
      backstageData: [], //后台记录的列数据
      showColumns: [], //进行展示的列数据
    };
  },
  computed: {
    ...mapGetters({
      getUserInfo: 'user/getUserInfo',
    }),
    initColumns() {
      return this.searchData.statisticsType === 1 ? this.unitInitColumns([], this) : this.typeInitColumns([], this);
    },
    computedShowColumns() {
      return this.showColumns;
    },
  },
  methods: {
    //获取后台返回的展示数据
    async getColumnsMx() {
      try {
        this.getColumnLoading = true;
        this.backstageData = [];
        let params = {
          userName: this.getUserInfo?.username || '',
        };
        let { data } = await this.$http.post(governancetask.deviceWorkOrderUserColQueryList, params);
        this.backstageData = data.data;
        this.matchShowColumns();
      } catch (err) {
        this.showColumns = this.initColumns;
        console.log(err);
      } finally {
        this.spliceColumnsMx();
        this.getColumnLoading = false;
      }
    }, //end_getColumnsMx
    //根据单位创建和指派给单位区分个别的列
    spliceColumnsMx() {
      //单位创建时候显示['未指派'],指派给单位不显示
      const spliceList = [{ key: 'wzpAmount', lastKey: 'total' }];
      spliceList.forEach((spliceItem) => {
        let column_index = this.showColumns.findIndex((item) => item.key === spliceItem.key);
        let splice_obj = this.initColumns.find((item) => item.key === spliceItem.key);
        let lastKey_index = spliceItem?.lastKey
          ? this.showColumns.findIndex((item) => item.key == spliceItem.lastKey)
          : this.showColumns.length - 1;
        if (this.commonSearchData.queryType === '1') {
          //单位创建
          column_index === -1 && this.showColumns.splice(lastKey_index, 0, splice_obj);
        } else {
          //指派给单位
          column_index !== -1 && this.showColumns.splice(column_index, 1);
        }
      });
    },
    //后台与初始列数据进行匹配,存在则进行进行记录
    matchShowColumns() {
      this.showColumns = [];
      //若返回数据为空,则全部展示
      if (!this.backstageData.length) {
        this.showColumns = this.initColumns;
        return;
      }
      //showKeys: 要展示的列key值
      let showKeys = this.backstageData.map((item) => item.colKey);
      let defaultColumns = this.searchData.statisticsType === 1 ? this.unitInitColumns : this.typeInitColumns;
      defaultColumns(showKeys, this).forEach((item) => {
        if (item.isShow) {
          this.showColumns.push(item);
        }
        if (item.children?.length) {
          let itemChildren = item.children.filter((child) => child.isShow);
          itemChildren.length && this.showColumns.push({ ...item, children: itemChildren });
        }
      });
    },
    //选择后匹配展示
    getColumnsAfterSelectMx(data) {
      try {
        this.backstageData = data;
        this.matchShowColumns();
      } catch {
        this.showColumns = this.initColumns;
      }
    },
  },
};
export default mixin;
