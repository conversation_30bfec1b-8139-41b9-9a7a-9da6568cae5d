export const qualifiedColorConfig = {
  '1': {
    color: 'var(--color-success)',
    dataValue: '合格',
  },
  '2': {
    color: 'var(--color-failed)',
    dataValue: '不合格',
  },
  '3': {
    color: 'var(--color-failed)',
    dataValue: '无法检测',
  },
};
export const defaultIconStaticsList = [
  {
    name: '视频监控总量:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'deviceCount',
  },
  {
    name: '实际检测数量:',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'evaluatingCount',
  },
  {
    name: '检测合格数量:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'evaluatingSuccessCount',
  },
];
// 人脸和车辆
export const iconStaticsFaceAndVehicle = [
  {
    name: '设备总量',
    count: '0',
    countStyle: {
      color: '#EBB225',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'deviceNum',
  },
  {
    name: '实际检测设备数量',
    count: '0',
    countStyle: {
      color: '#B113B1',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'actualNum',
  },
  {
    name: '合格设备数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancebuhegeshebeishu',
    fileName: 'qualifiedNum',
  },
];
export const iconStaticsImgList = [
  {
    name: '检测图片数量',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-renliankakou',
    fileName: 'faceDataTotal',
  },
  {
    name: '合格图片数量',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-URLkeyongtupianshuliang',
    fileName: 'passFaceDataTotal',
  },
  {
    name: '不合格图片数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-URLbukeyongtupianshu',
    fileName: 'notPassFaceDataTotal',
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
    width: 175,
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
    width: 175,
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    width: 150,
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
  {
    type: 'start-end-num',
    label: '时钟误差（秒）',
    startKey: 'clockSkewMin',
    endKey: 'clockSkewMax',
    width: 70,
  },
];
export const imgFormData = [
  {
    type: 'start-end-time',
    label: '抓拍时间',
    startKey: 'startTime',
    endKey: 'endTime',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'camera',
    label: '抓拍设备',
    key: 'deviceIds',
  },
  {
    type: 'select',
    key: 'causeErrors',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
