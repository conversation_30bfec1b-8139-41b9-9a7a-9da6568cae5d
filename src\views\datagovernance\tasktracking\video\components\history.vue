<template>
  <div>
    <ui-modal ref="modal2" :title="title" :styles="styles" footer-hide>
      <!-- <div class="updata-time">
				<div class="search">
					<label for>组织机构</label>
					<api-organization-tree
						:select-tree="selectOrgTree"
						@selectedTree="selectedOrgTree"
						placeholder="请选择"
					></api-organization-tree>
					<Button type="primary" class="mr-sm" @click="search">查询</Button>
					<Button type="default" class="mr-sm" @click="clear">重置</Button>
				</div>
				最新更新时间：2021-04-06 02:12:00
			</div> -->

      <searchView @startSearch="search"></searchView>
      <!-- <div class="echarts-title"><i></i><div class="titles">检测结果统计</div></div> -->
      <p class="title-text" v-if="$parent.list && $parent.list.length > 0">
        <span>
          检测数量总量:
          <i>{{ $parent.list[2].accessDataCount }}</i>
        </span>
        <span>
          异常数据总量：
          <i>{{ $parent.list[2].existingExceptionCount }}</i>
        </span>
      </p>
      <!-- <div class="echarts">
        <div class="morePie">
          <morePie ref="morePic" />
        </div>
        <div class="singlePie">
          <singlePie ref="singlePic1" :dataNum="echartSingle[0]" :color="color3" refName="echart-3" title="合格数据" />
        </div>
        <div class="singlePie" style="width:150px; height:150px;">
          <singlePie ref="singlePic2" :dataNum="echartSingle[1]" :color="color2" refName="echart-4" title="异常数据"/>
			</div>-->
      <!-- <div class="singlePie" style="width:150px; height:150px;">
          <singlePie ref="singlePic2" :dataNum="echartSingle[2]" :color="color3" refName="echart-5" title="异常数据"/>
			</div>-->
      <!-- </div>
			<div class="echarts-title"><i></i><div class="titles">检测结果统计</div></div>-->
      <ui-table
        class="ui-table"
        :table-columns="columns"
        :table-data="tableData"
        :loading="loading"
        :minus-height="minusTable"
      >
        <template #no="{ row }">
          <span class="red" :title="row.errorMessage">{{ row.errorMessage }}</span>
        </template>
        <template #macAddr="{ row }">
          <span>{{ row.macAddr || '--' }}</span>
        </template>
        <template #ipAddr="{ row }">
          <span>{{ row.ipAddr || '--' }}</span>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>

      <div slot="footer"></div>
    </ui-modal>
  </div>
</template>
<script>
import api from '@/config/api/vedio-threm.js';
export default {
  name: 'historyView',
  props: {},
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      model1: '',
      loading: false,
      title: '历史视频流通畅性检测',
      showEchart: false,
      titleChild: '车辆属性判定',
      value4: '',
      time: '2021-05-05 12:30:30',
      styles: {
        width: '95%',
      },
      minusTable: 420,
      images: [require('@/assets/img/navigation-page/systemmanagement.png')],
      curItem: 1,
      timeList: [
        { value: 1, label: '时' },
        { value: 2, label: '分' },
        { value: 3, label: '秒' },
      ],
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
        orgCode: null,
        reasonTypes: [7],
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      columns: [
        { type: 'index', width: 70, title: '序号' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 200,
        },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
        { title: '组织机构', key: 'orgCode' },
        { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
        { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
        { title: `${this.global.filedEnum.macAddr}`, slot: 'macAddr' },
        { title: this.global.filedEnum.ipAddr, slot: 'ipAddr' },
        { title: '安装地址', key: 'address' },
        { title: '不合格原因', key: 'errorMessage', slot: 'no' },
      ],
      tableData: [],
      curTag: 1,
      echartSingle: [],
      color1: ['#807C0D', '#BFBA14'],
      color2: ['#4D1F08', '#C02E02'],
      color3: ['#32A19E', '#05CE98'],
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  mounted() {
    this.init();
  },
  methods: {
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.getVedioPage();
    },
    init() {
      this.echartSingle = [26598, 65986, 69853];
      // this.$refs.morePic.resizeFn();
      // this.$refs.singlePic1.init();
      // this.$refs.singlePic2.init();
      // this.$refs.singlePic3.init();
    },
    resetSearch() {},
    clear() {
      this.selectOrgTree.orgCode = null;
      this.pageData.pageNum = 1;
      this.resetSearchDataMx(this.searchData, this.getVedioPage);
      // this.searchData = {
      //   name: "",
      //   params:{
      //     pageNumber: 1,
      //     pageSize: 20,
      //   },
      //   totalCount: 0,
      //   orgCode: null,
      // }
      // this.pageData.pageNum = 1;
      // this.getVedioPage();
    },
    search() {
      this.getVedioPage();
    },
    getVedioPage() {
      this.loading = true;
      this.searchData.reasonTypes = [7];
      this.$http.post(api.queryVideoResultPageList, this.searchData).then((res) => {
        if (res.data.code == 200) {
          this.loading = false;
          this.tableData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        }
      });
    },
    showModal() {
      this.$refs.modal2.modalShow = true;
      this.getVedioPage();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.getVedioPage();
    },

    changePageSize(val) {
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.searchData.params.pageNumber = 1;
      this.getVedioPage();
    },
  },
  watch: {},
  components: {
    // morePie: require('./pie-more-echart').default,
    // singlePie: require('./pie-single-echart').default,
    UiTable: require('@/components/ui-table.vue').default,
    // ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    searchView: require('./search.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-title {
  width: 100%;
  display: flex;
  margin-top: -15px;
  height: 30px;
  line-height: 30px;
  margin-bottom: 5px;
  i {
    width: 8px;
    height: 100%;
    background: #239df9;
    margin-right: 6px;
  }
  .titles {
    width: 100%;
    padding-left: 5px;
    background-image: linear-gradient(to right, #0a4f8d, #09284d);
  }
}

.echarts {
  width: 100%;
  height: 150px;
  background: var(--bg-sub-content);
  margin-top: 6px;
  margin-bottom: 20px;
  .morePie {
    position: relative;
    float: left;
    width: 300px;
    height: 150px;
    // background: #0b3469;
  }
  .singlePie {
    position: relative;
    float: left;
    width: 150px;
    height: 150px;
    // background: #0b3469;
    margin-left: 30px;
  }
}

.ivu-checkbox-group-item {
  padding: 6px 0;
}

.quality {
  padding: 10px 0;
  color: #fff;
}

.explain {
  color: #c76d28;
}
.p {
  margin-top: 10px;
  color: #fff;
}
.ul {
  overflow: hidden;
  li {
    position: relative;
    float: left;
    font-size: 14px;
    padding: 6px 16px;
    background: var(--color-primary);
    color: #fff;
    border-radius: 4px;
    margin-right: 10px;
    margin-top: 10px;
  }

  .add {
    border: 1px solid #037cbe;
    background: transparent;
    padding: 6px 16px;
    cursor: pointer;
  }

  .close {
    position: absolute;
    right: -3px;
    top: -3px;
    opacity: 0.8;
    cursor: pointer;
  }
  .close:hover {
    opacity: 1;
  }
}

.search-wrapper {
  width: 100%;
  height: 50px;
}

/deep/ .ivu-modal-body {
  color: #fff;
}

/deep/ .ivu-modal-footer {
  height: 0;
  padding: 0;
}

.carItem {
  height: 200px;
  padding: 10px;
  .item {
    height: 100%;
    background: #22416f;
    .img {
      position: relative;
      width: 100%;
      height: 130px;
      .btn {
        position: absolute;
        width: 100%;
        height: 30px;
        background: rgba(0, 0, 0, 0.2);
        bottom: 0;
        display: none;
        img {
          margin-left: 10px;
          width: 26px;
          height: 26px;
          margin-top: 2px;
          cursor: pointer;
        }
      }
    }
    .img:hover {
      .btn {
        display: block;
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      height: 130px;
      max-height: 160px;
      background: #999;
    }
  }
}

.blue {
  color: var(--color-primary);
}
.red {
  color: #e44f22;
  display: inline-block;
  width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.cyan {
  color: var(--color-bluish-green-text);
}

.topImg {
  // position: fixed;
  // width: 100%;
  // height: 100%;
  // top: 0;
}

.clear {
  clear: both;
}

/deep/ .ivu-tabs {
  color: #fff;
}
.updata-time {
  label {
    margin-right: 10px;
  }
}
</style>
