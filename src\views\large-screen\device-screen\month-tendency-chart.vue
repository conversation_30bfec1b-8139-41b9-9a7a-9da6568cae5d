<!--
    * @FileDescription: 数据月度趋势
    * @Author: H
    * @Date: 2024/04/28
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="chart">
        <div id="month" class="month" ref="month"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default{
    data() {
        return {
            formData: {
                type: ''
            },
            option: {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(2, 27, 71, 0.8)',
                    borderColor: '#098EFF',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    axisLabel:{
                        interval: 0,
                        lineStyle: {
                            color: '#A8D0FF'
                        },
                        textStyle: {
                            color: '#B6CBD5'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(168, 208, 255, 0.2)'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
                },
                yAxis: {
                    type: 'value',
                    nameLocation: 'start',
                    nameTextStyle: {
                        color: '#B6CBD5',
                        padding: [0, 25, 0, 0]
                    },
                    nameGap: 15,
                    splitLine: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#B6CBD5'
                        },
                    }
                    
                },
                grid: {
                    top: '20px',
                    left: '30px',
                    right: '5px',
                    bottom: '30px'
                },
                series: [
                    {
                        data: [ '8','12','10','20','22','18','24','22','20','18','16','12' ],
                        type: 'line',
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0,0,0,1, [
                                { offset:0, color: 'rgba(0, 150, 255, 1)' },  
                                { offset:1, color: 'rgba(0, 150, 255, 0)' },  
                            ]) 
                            
                        },
                        symbol: 'circle',
                        symbolSize: 10,
                        showSymbol: false,
                        itemStyle: {
                            emphasis: {
                                color: '#0096FF',
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        },
                        smooth: true,
                        
                    }
                ]
            },
            myEchart: null
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            this.myEchart = echarts.init(this.$refs.month)
            let option = {
                // title: this.title,
                // legend: this.legend,
                // grid: this.grid,
                // xAxis: this.xAxis,
                // yAxis: this.yAxis,
                // tooltip: this.tooltip,
                // series: this.series
            }
            this.myEchart.setOption(this.option)
            window.addEventListener('resize', () => this.myEchart.resize())
        },
        handleChange() {

        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .month{
        height: 100%;
        width: 100%;  
    }
}
/deep/ .ivu-select{
    &:hover{
        color: #567BBB;
    }
    .ivu-select-selection{
        border: 1px solid #098EFF;
        background: rgba(9,142,255, 0.1);
        color: #567BBB;
    }
    .ivu-select-selected-value{
        color: #567BBB;
    }
    .ivu-select-arrow{
        color: #567BBB;
    }
    .ivu-select-dropdown{
        background: rgba(9,142,255, 0.3);
    }
    .ivu-select-item, .ivu-select-placeholder{
        color: #567BBB;
    }
    .ivu-select-item-selected:hover, 
    .ivu-select-item:hover, 
    .ivu-select-placeholder:hover, 
    .ivu-select-selection:hover{
        color: #fff;
    }
    .ivu-select-item-selected{
        color: #fff;
    }
}
</style>