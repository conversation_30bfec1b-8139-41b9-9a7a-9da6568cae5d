<template>
  <ui-modal ref="modal" :title="title" v-model="visible">
    <div class="bg">
      <div class="form">
        <Form ref="form" :model="form" :rules="rules" :label-width="80">
          <FormItem label="生产厂商" prop="manufacturer">
            <Select v-model="form.manufacturer" placeholder="请选择生产厂商">
              <Option :value="item.dataKey" v-for="(item, index) in manufacturerTypeList" :key="index">{{
                item.dataValue
              }}</Option>
            </Select>
          </FormItem>
          <FormItem label="国标ID" prop="deviceId">
            <Input v-model="form.deviceId" placeholder="请输入国标ID"></Input>
          </FormItem>
          <FormItem :label="`${global.filedEnum.ipAddr}`" prop="ip">
            <Input :placeholder="`请输入${global.filedEnum.ipAddr}`" v-model="form.ip"></Input>
          </FormItem>
          <FormItem label="端口号" prop="port">
            <Input placeholder="请输入端口号" v-model.number="form.port"></Input>
          </FormItem>
        </Form>
      </div>
    </div>
    <template slot="footer">
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" @click="query" class="plr-30">确 定</Button>
    </template>
  </ui-modal>
</template>
<script>
import algorithm from '@/config/api/algorithm';
export default {
  name: 'videoAdd',
  props: ['manufacturerTypeList'],
  data() {
    return {
      form: {
        manufacturer: '',
      },
      rules: {
        manufacturer: [{ required: true, message: '请选择生产厂商', trigger: 'change' }],
        deviceId: [{ required: true, message: '请输入国标ID', trigger: 'blur' }],
        ip: [
          { required: true, message: '请输入IP地址', trigger: 'blur' },
          {
            validator: function (rule, value, callback) {
              if (
                /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/gi.test(value) == false
              ) {
                callback(new Error('IP格式不正确'));
              } else {
                callback();
              }
            },
            trigger: 'blur',
          },
        ],
        port: [{ required: true, message: '请输入端口号且是数字', trigger: 'blur', type: 'number' }],
      },
      title: '',
      visible: false,
    };
  },
  created() {
    // this.$http.get(algorithm.dictData + 'manufacturer').then((res) => {
    //   if (res.data.code == 200) {
    //     this.csList = res.data.data
    //   }
    // })
  },
  methods: {
    // submit(name) {
    //   this.$refs[name].validate((valid) => {
    //     if (valid) {
    //       this.$Message.success('Success!')
    //     } else {
    //       this.$Message.error('Fail!')
    //     }
    //   })
    // },
    showModal(val) {
      switch (val) {
        case 1:
          this.form = {};
          this.title = '新增下级域';
          break;
        case 2:
          this.title = '编辑下级域';
          break;
      }
      this.visible = true;
    },
    add() {
      this.$http.post(algorithm.videoConfig, this.form).then((res) => {
        if (res.data.code == 200) {
          this.$Message.success('添加成功');
          this.visible = false;
        }
      });
    },
    query() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.add();
          this.$parent.init();
        } else {
          // this.$Message.error('请将信息填写完整！')
        }
      });
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 0;
}
.bg {
  width: 100%;
  height: 100%;
  padding: 24px 50px 10px 50px;
  // background: url('../../../../assets/img/vediobg.png');
  background-size: 100% 100%;
}
.form {
  // width: 600px;
  margin: 0 auto;
  // margin-top: 33px;
}
</style>
