<template>
  <div class="specialassessment-menu">
    <div class="input-div mb-md">
      <Input v-model="searchText" @keyup.enter.native="enter" :placeholder="placeholder" type="text">
        <Icon type="ios-search" slot="suffix" @click="enter" />
      </Input>
    </div>
    <div class="tree-menu auto-fill">
      <el-tree
        ref="tree"
        :data="menuList"
        node-key="id"
        accordion
        :expand-on-click-node="true"
        :default-expanded-keys="defaultExpandedKeys"
        @node-click="handleNodeClick"
        @node-expand="handleNodeExpand"
        @node-collapse="handleNodeCollapse"
      >
        <p class="custom-tree-node" slot-scope="{ data }">
          <i
            v-if="iconVisible(data)"
            :class="['icon-font', 'f-14', 'menu-icon', activeId === data.id ? data.activeIcon : data.icon]"
          ></i>
          <span class="ml-xs menu-text" :class="!data.children ? 'underline-text' : ''">
            <span class="ellipsis" :title="data.name">{{ data.name }}</span>
            <span v-if="!!data.children"
              >(&nbsp;<span class="nodeNum">{{ data.count }}</span
              >&nbsp;)</span
            >
          </span>
        </p>
      </el-tree>
    </div>
  </div>
</template>
<script>
import { menuConfig } from '@/views/specialassessment/utils/menuConfig.js';
export default {
  name: 'SpecialassessmentMenu',
  props: {
    placeholder: {
      type: String,
      default: '搜索',
    },
    menuList: {
      type: Array,
      default: () => [],
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchText: '',
      activeId: '',
    };
  },
  methods: {
    enter() {
      this.$emit('searchMenuList', this.searchText);
    },
    handleNodeClick(data) {
      if (data.children) return false;
      const menuItem = menuConfig.find((item) => data.key === item.type);
      if (!menuItem) return false;
      const menuData = {
        ...data,
        componentName: menuItem.component,
      };
      this.$emit('getIndexData', menuData);
    },
    handleNodeExpand(data) {
      if (!data.activeIcon) return false;
      this.activeId = data.id;
    },
    handleNodeCollapse(data) {
      if (!data.activeIcon) return false;
      this.activeId = '';
    },
    iconVisible(data) {
      return data.icon || (data.activeIcon && this.activeId === data.id);
    },
  },
  watch: {
    defaultExpandedKeys: {
      handler(val) {
        this.activeId = val[0];
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.specialassessment-menu {
  display: flex;
  flex-direction: column;
  padding: 16px;
  background: var(--bg-content);
  border-radius: 4px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
}
.tree-menu {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .pl-lg {
    padding-left: 30px;
  }
  //.menu-icon{
  //  font-size: 20px;
  //  color: #2C86F8;
  //}
  .ivu-icon-md-arrow-dropdown {
    font-size: 20px;
  }
  @{_deep} .ivu-menu {
    width: 100% !important;
    border-radius: 4px;
    &-submenu-title {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
      &:hover {
        text-decoration: underline;
      }
    }
    .ivu-icon-ios-arrow-down {
      display: none !important;
    }
    &-item {
      border-radius: 4px !important;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  overflow: hidden;
  .menu-icon {
    font-size: 20px;
    color: var(--color-btn-default);
  }
  .menu-text {
    flex: 1;
    display: flex;
    font-size: 14px;
    font-weight: normal;
    overflow: hidden;
  }
  .underline-text {
    &:hover {
      text-decoration: underline;
    }
  }
}
.nodeNum {
  color: var(--color-btn-default);
}
@{_deep} .el-tree-node__content {
  height: 40px !important;
}
</style>
