/*
 * @FileDescription: 框选图片
 * @Author: H
 * @Date: 2023/06/01
 * @LastEditors: du<PERSON><PERSON> du<PERSON>@qishudi.com
 * @LastEditTime: 2024-05-15 10:37:31
 */
export const myMixins = {
  data() {
    return {
      imgBoxList: {},
    };
  },
  mounted() {},
  created() {},
  methods: {
    // 框选处理
    loadImage(e, value) {
      if (!value) {
        document.querySelector(".select-preview").style.display = "none";
      }
      let box = document.querySelector(".fun-img");
      box.style.width = "100%";
      box.style.height = "100%";
      const imgBox = document.getElementById("imgBox");
      const nw = imgBox.naturalWidth;
      const nh = imgBox.naturalHeight;
      const w = parseInt(window.getComputedStyle(imgBox).width);
      const h = parseInt(window.getComputedStyle(imgBox).height);
      const rateW = w / nw;
      const rateH = h / nh;
      // 存在框选框
      if (value) {
        let data = value.split(",");
        // "rect":{ "left":0, "top":0,"right":0,"bottom":0}
        if (nh < 400) {
          this.imgBoxList = {
            x: data[0],
            y: data[1],
            width: data[2] - data[0],
            height: data[3] - data[1],
          };
        } else {
          this.imgBoxList = {
            x: data[0] * rateW,
            y: data[1] * rateH,
            width: (data[2] - data[0]) * rateW,
            height: (data[3] - data[1]) * rateH,
          };
        }
      }
      box.style.width = w + "px";
      box.style.height = h + "px";
      // 居中，涉及到框选位置计算和移动，不能直接用flex布局
      let largeBoxDom = document.querySelector(".common-info-right");
      box.style.marginLeft = (largeBoxDom.clientWidth - w) / 2 + "px";
      box.style.marginTop = (largeBoxDom.clientHeight - h) / 2 + "px";
      document.querySelector(".select-preview").style.display = "block";
    },
  },
};
