<template>
  <div class="history-video-modal" v-if="isShow">
    <div class="title-box">
      <div class="goback" @click="isShow = false">
        <i class="iconfont icon-return"></i>
      </div>
      返回
    </div>
    <div class="search-box">
      <div class="search-item">
        <DatePicker
          class="mr-10"
          v-model="time"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm:ss"
          @on-change="handleOnchange"
          @on-clear="handleClearchange"
          placeholder="请选择时间"
          style="width: 350px"
        ></DatePicker>
        <Button class="mr-10" type="primary" @click="searchAll">查询</Button>
      </div>
      <div class="search-item">
        <span class="search-item-r search-item-hover" @click="setFullScreen">
          <i class="iconfont icon-quanping_xian" title="全屏"></i>
          <span>全屏</span>
        </span>
        <span class="search-item-r">
          <span class="mr-10">分屏选择</span>
          <Select
            size="small"
            v-model="currentLayout"
            @on-change="changeLayout($event)"
            :style="{ width: '100px' }"
          >
            <Option
              :value="item.value"
              v-for="(item, index) in layoutOptions"
              :key="index"
              >{{ item.label }}</Option
            >
          </Select>
        </span>
        <span class="search-item-r search-item-hover" @click="closeAll">
          <i class="iconfont icon-guanbi close" title="全部关闭"></i>
          <span>全部关闭</span>
        </span>
      </div>
    </div>
    <div class="content">
      <div class="video-left mr-10">
        <div class="label">
          共<span class="color-blue">{{ historyDevices.length }}</span
          >个摄像机
        </div>
        <div class="historyDevices">
          <div
            v-for="(item, index) in historyDevices"
            :class="{ noVod: !item.hasVod }"
            :key="index"
            @dblclick="handleNodeClick(item)"
          >
            <span
              class="node-title"
              :class="{ playing: playingDeviceIds.includes(item.deviceGbId) }"
            >
              <!-- 在线状态0：在线，1：离线 -->
              <span :class="{ offline: item.Status == '1' }">
                <i
                  v-if="playingDeviceIds.includes(item.deviceGbId)"
                  class="playing-icon"
                ></i>
                <i
                  v-else
                  class="iconfont color-blue mr-5"
                  :class="
                    item.LayerType == 'Camera_QiuJi'
                      ? 'icon-qiuji'
                      : 'icon-shebeizichan'
                  "
                ></i>
                <span :title="item.deviceName" class="ellipsis name-width"
                  >{{ item.deviceName
                  }}<template v-if="!item.hasVod">(无录像)</template></span
                >
              </span>
            </span>
          </div>
        </div>
      </div>
      <div class="right-content">
        <h5-player ref="H5Player" @changeWinList="getWinList"></h5-player>
      </div>
    </div>
  </div>
</template>

<script>
import { fetchVod, startVod, getChannel, getCipherChannel } from "@/api/player";

export default {
  name: "",
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    historyDevices: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    isShow(val) {
      this.$emit("input", val);
    },
    value: {
      handler(val) {
        this.isShow = val;
        if (val) this.init();
      },
      immediate: true,
    },
  },
  data() {
    return {
      isShow: false,
      time: [],
      searchTime: [],
      playingHistoryIds: [],
      playingDeviceIds: [],
      currentLayout: "2*2",
      layoutOptions: [
        {
          label: "单分屏",
          value: "1*1",
          count: 1,
        },
        {
          label: "三分屏",
          value: "1A2",
          count: 3,
        },
        {
          label: "三分屏",
          value: "2A1",
          count: 3,
        },
        {
          label: "四分屏",
          value: "2*2",
          count: 4,
        },
        {
          label: "六分屏",
          value: "1A5",
          count: 6,
        },
        {
          label: "八分屏",
          value: "1A7",
          count: 8,
        },
        {
          label: "九分屏",
          value: "3*3",
          count: 9,
        },
        {
          label: "十六分屏",
          value: "4*4",
          count: 16,
        },
      ],
    };
  },
  methods: {
    init() {
      let date = new Date();
      let startTime = new Date(date.getTime() - 2 * 60 * 60 * 1000);
      let endTime = date;
      this.searchTime = [
        this.$dayjs(startTime).format("YYYY-MM-DDTHH:mm:ss"),
        this.$dayjs(endTime).format("YYYY-MM-DDTHH:mm:ss"),
      ];
      this.time = [startTime, endTime];
      this.searchAll();
    },
    getWinList(val) {
      this.playingHistoryIds = val.map((v) => v.historyId);
      this.playingDeviceIds = val.map((v) => v.deviceId);
    },
    handleOnchange(val) {
      this.searchTime = val;
    },
    handleClearchange() {
      this.searchTime = [];
    },
    async searchAll() {
      if (!this.searchTime[0] || !this.searchTime[1]) {
        this.$Message.error("开始时间和结束时间不能为空");
        return;
      }
      let batchSize = 5;
      this.historyDevices.forEach((v) => (v.hasVod = false));
      for (let i = 0; i < this.historyDevices.length; i += batchSize) {
        const batch = this.historyDevices.slice(i, i + batchSize);
        const promises = batch.map(({ deviceGbId }) =>
          this.searchList(deviceGbId)
        );
        const results = await Promise.all(promises);
        results.forEach((item, index) => {
          let obj = this.historyDevices[i + index];
          obj.hasVod = !!item.length;
          this.$set(this.historyDevices, i + index, obj);
        });
      }
      console.log(this.historyDevices);
    },
    searchList(deviceGbId) {
      return new Promise((resolve) => {
        let endTime = "",
          startTime = "";
        if (this.searchTime.length > 0) {
          startTime = this.searchTime[0];
          endTime = this.searchTime[1];
        }
        if (vodType == "pvg67" || vodType == "pvgplus") {
          let requestUrl = desencode == 1 ? getCipherChannel : getChannel;
          requestUrl(deviceGbId).then((res) => {
            if (res.code != 200 || !res.data) {
              resolve([]);
              return;
            }
            let { channelNo, ip, port, userName, password } = res.data;
            let record = {
              begintime: this.$dayjs(startTime).format(
                "YYYY-MM-DD HH:mm:ss:SSS"
              ),
              channel: channelNo,
              desencode: desencode,
              devicetype: vodType,
              endtime: this.$dayjs(endTime).format("YYYY-MM-DD HH:mm:ss:SSS"),
              ip: ip,
              password: password,
              port: parseInt(port),
              streamtype: "vod",
              user: userName,
              vod: vodStorage == "device" ? 1 : 0,
            };
            this.$refs.H5Player.videoObj.queryRecord(record, (res) => {
              try {
                if (res.error != 0) {
                  // TODO 暂时隐藏，测试要求
                  // this.$Message.error("查询录像失败！" + this.videoObj.errorCodeToString(res.error));
                  resolve([]);
                  return;
                }
                let obj = JSON.parse(res.info);
                if (!obj) {
                  resolve([]);
                  return;
                }
                resolve(obj);
              } catch (e) {}
            });
          });
        } else {
          let params = {
            endtime: endTime,
            starttime: startTime,
            channel: deviceGbId,
            download: true,
            storage: vodStorage,
          };
          fetchVod(params).then((res) => {
            if (res.code === 200) {
              resolve(res.RecordList || []);
            }
          });
        }
      });
    },
    // 切换布局
    changeLayout(layout) {
      this.currentLayout = layout;
      this.$refs.H5Player.changeLayout(layout);
    },
    closeAll() {
      this.$refs.H5Player.closeAll();
    },
    // 全屏
    setFullScreen() {
      this.$refs.H5Player.setFullScreen();
    },
    handleNodeClick(item) {
      if (!item.hasVod) return;
      let params = {
        deviceId: item.deviceGbId,
        deviceName: item.deviceName,
        geoPoint: {
          lon: item.Lon,
          lat: item.Lat,
        },
        devicetype: vodType,
        playType: "vod",
        begintime: this.searchTime[0],
        endtime: this.searchTime[1],
        type: item.deviceType,
      };
      this.$refs.H5Player.playStream(params, params.playType);
      this.queryLog({
        muen: "时空分析",
        name: "框选设备",
        type: "4",
        remark: `查看【${params.deviceName}】,【${this.$dayjs(
          params.begintime
        ).format("YYYY-MM-DD HH:mm:ss")}-${this.$dayjs(params.endtime).format(
          "YYYY-MM-DD HH:mm:ss"
        )}】的历史视频`,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.history-video-modal {
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  z-index: 9999;
  .title-box {
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    .goback {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
  }
  .search-box {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d3d7de;
    .search-item {
      display: flex;
      height: 100%;
      align-items: center;
      margin: 0 10px;
      .search-item-r {
        display: flex;
        align-items: center;
        margin-right: 20px;
        .iconfont {
          margin-right: 5px;
          color: #2c86f8;
        }
        &:last-child {
          margin-right: 0;
        }
      }
      .search-item-hover {
        cursor: pointer;
      }
    }
  }
  .content {
    height: calc(~"100% - 91px");
    padding: 10px;
    display: flex;
    .color-blue {
      color: #2c86f8;
    }
    .video-left {
      width: 416px;
      height: 100%;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      .label {
        height: 30px;
        line-height: 30px;
        margin: 0 12px;
        color: black;
        border-bottom: 1px solid #d3d7de;
      }
      .historyDevices {
        height: calc(~"100% - 30px");
        overflow-y: auto;
        .noVod {
          color: #a5b0b6;
          .node-title {
            cursor: not-allowed;
          }
        }
        .node-title {
          cursor: pointer;
          padding: 5px 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          &.playing {
            color: #ff8d33;
            .name-width {
              font-weight: bold;
            }
          }
          i.playing-icon {
            width: 18px;
            height: 18px;
            margin-right: 5px;
            background: url("~@/assets/img/player/playing.gif") 50% no-repeat;
          }
          .statistics {
            margin-left: 5px;
          }
          .name-width {
            display: block;
            flex: 1;
          }
          span {
            display: flex;
            align-items: center;
            overflow: hidden;
          }
          .offline {
            i {
              color: #888888;
            }
          }
          &:hover {
            color: #2c86f8;
            background-color: rgba(61, 183, 255, 0.1);
          }
        }
      }
    }
    .right-content {
      flex: 1;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      position: relative;
      padding: 5px;
    }
  }
}
</style>
