<template>
  <div class="records">
    <div class="records-item" v-for="(e, i) in recordList" :key="i" @click="chooseRecord(e)">
      <ui-icon type="time" :size="14"></ui-icon>
      <span class="searchValue ellipsis">{{ e.keyWord }}</span>
    </div>
    <footer>
      <span @click="deleteRecord">删除历史</span>
    </footer>
  </div>
</template>

<script>
import { deleteRecord } from '@/api/operationsOnTheMap'
export default {
  props: {
    // 历史记录
    recordList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {
    // 选中记录
    chooseRecord(e) {
      this.$emit('chooseRecord', e)
    },
    // 删除记录
    deleteRecord() {
      deleteRecord()
        .then(res => {
          if (res.code === 200) {
            this.$Message.success('删除成功')
            this.$emit('closeRecords')
          }
        })
        .catch(e => {
          console.log(e)
        })
    }
  }
}
</script>

<style lang="less" scoped>
.records {
  width: 350px;
  max-height: 412px;
  background: #ffffff;
  &-item {
    display: flex;
    align-items: center;
    height: 38px;
    line-height: 38px;
    padding-left: 16px;
    cursor: pointer;
    .searchValue {
      color: rgba(0, 0, 0, 0.8);
      margin-left: 16px;
      margin-right: 10px;
      font-size: 14px;
    }
    .address {
      color: rgba(0, 0, 0, 0.35);
    }
  }
  &-item:hover {
    background: rgba(44, 134, 248, 0.1);
  }
  &-item:last-of-type {
    border-bottom: 1px solid #d3d7de;
  }
  > footer {
    padding-right: 15px;
    color: rgba(0, 0, 0, 0.35);
    padding-top: 2px;
    line-height: 30px;
    float: right;
    font-size: 14px;
    > span {
      cursor: pointer;
    }
  }
}
</style>
