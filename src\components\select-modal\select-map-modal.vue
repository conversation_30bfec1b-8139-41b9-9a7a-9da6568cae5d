<!--
    * @FileDescription: 选择设备-框选
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <custom-modal 
        v-model="visible" 
        title="框选范围" 
        :r-width="1714" 
        :footer='true' 
        @onOk="confirmHandle" 
        @onCancel="handleCancel">
        <div class="map">
            <mapBase ref="mapBase" 
                :mapLayerConfig="mapLayerConfig" 
                :searchBtn="false" 
                :siteListFlat="siteListFlat"
                @selectlist="selectlist"
                :crashType="crashType" />
        </div>
        <div slot="footer">
            <Button @click="handleCancel">取消</Button>
            <Button type="primary" @click="confirmHandle">确定</Button>
        </div>
    </custom-modal>
</template>

<script>
import mapBase from '@/components/map/index.vue';
import customModal from '@/components/modal'
import { getSimpleDeviceList } from '@/api/operationsOnTheMap';
import { mapGetters } from 'vuex'
export default {
    name: '',
    components:{
        mapBase,
        customModal 
    },
    data () {
        return {
            visible: true,
            mapLayerConfig: {
                tracing: false, // 是否需要刻画轨迹
                showStartPoint: false, // 是否显示起点终点图标
                mapToolVisible: true, // 框选操作栏
                selectionResult: true, // 是否显示框选结果弹框
                resultOrderIndex: false, // 搜索结果排序,
                showLatestLocation: false // 显示地图最新位置
            },
            siteListFlat: [],
            deviceIdList: [],
            crashType:{ Camera: true, Camera_QiuJi: true, Camera_QiangJi: true, Camera_Face: true, Camera_Vehicle: true}
        }
    },
    watch:{
            
    },
    computed:{
        ...mapGetters({
            userInfo: "userInfo",
        }),
    },
    async created() {
        await this.getMapLayerByType();
    },
    mounted(){
            
    },
    methods: {
        show() {
            this.visible = true;
        },
        selectlist(value) {
            this.deviceIdList = value;
        },
        confirmHandle() {
            if(this.deviceIdList.length == 0) {
                this.$Message.warning('暂未框选数据!');
                return
            }
            this.$emit('configdata', this.deviceIdList)
        },
        handleCancel() {
            this.$emit('configdata', [])
        },
        // 进入页面查询所有点位信息
        async getMapLayerByType () {
            // 1摄像机,2车辆卡口,3WIFI设备,4电子围栏,5RIFD设备,6定位设备，7高空相机，11人脸抓拍机,13监视器，14微卡, 15卡口设备类型', 16ETC
            let deviceTypes = [1, 2, 11]
            this.siteListFlat = [];
            let roleParam = {roleId: this.userInfo.roleVoList[0].id, filter: this.userInfo.roleVoList[0].initFlag == '1' ? false : true, socialResources: 0, excludeDeviceTypes: [5,6,7]}
            let { data } = await getSimpleDeviceList(roleParam)
            data = data.filter(v => deviceTypes.includes(v[2]))
            this.siteListFlat = [...data]
        },
    }
}
</script>

<style lang='less' scoped>
.map{
    height: 640px;
    position: relative;
}
</style>
