<template>
    <div class="top" :class="visible ? 'more-search-show' : ''">
        <RadioGroup class="left" v-model="formData.applyStatus" type="button" @on-change="radioChange">
            <Radio v-for="(item, index) in radioList" :key="index" :label="item.key" >{{ item.value }}</Radio>
        </RadioGroup>
        <div class="right">
            <div class="item fixWidth">
                <div class="title wid100">任务名称：</div>
                <Input placeholder="请输入" clearable v-model="formData.jobName" maxlength="50" />
            </div>
            <div class="btn">
                <Button class="margin" type="primary" @click="query()">查询</Button>
                <Button @click="reset()">重置</Button>
            </div>
            <div class="btn-group"  @click="visible = !visible">
                <img src="@/assets/img/down-circle-icon.png" alt />
                <div class="more more-search-text"> {{visible ? '普通检索' : '高级检索'}} </div>
            </div>
        </div>
        <div class="more-search">
            <Form ref="form" :model="formData" class="form" inline>
              <FormItem v-if="structureJobType == 1 || structureJobType == 2" label="设备名称:" prop="deviceName" class="search-input">
                <Input placeholder="请输入" clearable v-model="formData.deviceName" maxlength="50" />
              </FormItem>
              <FormItem v-else label="文件名称:" prop="fileName" class="search-input">
                <Input placeholder="请输入" clearable v-model="formData.fileName" maxlength="50" />
              </FormItem>
              <FormItem label="解析类型:" prop="structureType" class="search-input">
                <Select v-model="formData.structureType" clearable transfer>
                  <Option v-for="item in structDataTypeList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">{{ item.dataValue }}</Option>
                </Select>
              </FormItem>
              <FormItem label="任务状态:" prop="taskStatus" class="search-input">
                <Select v-model="formData.taskStatus" clearable transfer>
                  <Option v-for="item in statusList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">{{ item.dataValue }}</Option>
                </Select>
              </FormItem>
              <FormItem label="创建时间:" prop="daterange" class="search-input">
                <div class="datepicker-wrap">
                  <hl-daterange v-model="daterange[0]" key="1"></hl-daterange>
                  <div class="line"></div>
                  <hl-daterange v-model="daterange[1]" key="2"></hl-daterange>
                </div>
              </FormItem>
              <FormItem label="创建人员:" prop="userName" class="search-input">
                <Input placeholder="请输入" clearable v-model="formData.userName" maxlength="50" />
              </FormItem>
            </Form>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import hlDaterange from '@/components/hl-daterange/index.vue';
  export default {
    components: {
      hlDaterange
    },
    props: {
      structureJobType: {
        type: [String, Number],
        default: 1
      }
    },
    data() {
      return {
        visible: false,
        radioList: [
          { key: 0, value: "待审核" },
          { key: 99, value: "全部" },
        ],
        formData: {
          applyStatus: 0,
          jobName: '',
          deviceName: '',
          fileName: '',
          structureType: '',
          taskStatus: '',
          userName: '',
          startTime: '',
          endTime: ''
        },
        daterange: []
      }
    },
    computed: {
      ...mapGetters({
        structDataTypeList: 'dictionary/getStructDataType', //解析类型
        structTaskStatusList: 'dictionary/getStructTaskStatus', //实时视频解析任务状态
        structHistroytaskStatusList: 'dictionary/getStructHistroytaskStatus', //历史视频解析任务状态
        filestrucureTaskStatusList: 'dictionary/getFilestrucureTaskStatus', //文件解析任务状态
      }),
      statusList() {
        return this.structureJobType == 1 ? this.structTaskStatusList : this.structureJobType == 2 ? this.structHistroytaskStatusList : this.filestrucureTaskStatusList
      }
    },
    activated() {},
    mounted() {
      this.getDictStructData()
      this.query()
    },
    methods: {
      ...mapActions({
          getDictStructData: 'dictionary/getDictStructData'
      }),
      radioChange(key) {
        this.query()
      },
      // 查询
      query() {
        let {applyStatus, jobName, deviceName, fileName, structureType, taskStatus, userName} = {...this.formData}
        this.$emit('searchForm', {
          applyStatus: applyStatus == 99 ? undefined : applyStatus,
          jobName: jobName ? jobName : undefined,
          deviceName: deviceName ? deviceName : undefined,
          fileName: fileName ? fileName : undefined,
          structureType: structureType ? structureType :undefined,
          taskStatus: taskStatus ? taskStatus : undefined,
          userName: userName ? userName : undefined,
          startTime:this.daterange[0] ? this.$dayjs(this.daterange[0]).valueOf() : this.daterange[0],
          endTime:this.daterange[1] ? this.$dayjs(this.daterange[1]).valueOf() : this.daterange[1]
        })
      },
      // 重置
      reset() {
        this.$refs.form.resetFields()
        this.daterange = []
        this.query()
      }
    }
  }
</script>
<style lang="less" scoped>
 .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #dfdfdf;
    padding-bottom: 16px;
    .right {
      display: flex;

      .item {
        display: flex;
        .title {
          line-height: 36px;
          font-size: 14px;
        }
        .wid100 {
          width: 100px;
        }
      }
      .fixWidth {
        width: 250px;
        margin-left: 26px;
      }
      .time-form{
        display: flex;
        align-items: center;
      }
      .right20 {
        margin-right: 20px;
      }
      .btn {
        padding: 0 30px;
        .margin {
          margin-right: 12px;
        }
      }
      .more {
        line-height: 36px;
        color: #1678f5;
        cursor: pointer;
      }
    }
 }

 .more-search {
    display: flex;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    background: #fff;
    z-index: 20;
    max-height: 0px;
    top: 100%;
    left: 0;
    transition: 0.3s;
    overflow: hidden;
    flex-direction: column;
    box-shadow: 0 2px 3px #b7cbe5;
 }

 .btn-group {
  display: flex;
  align-items: end;
  .more-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    // margin-right: 30px;
    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
 .more-search-show {
    .more-search {
      max-height: 300px;
      transition:  0.7s;
      padding-top: 20px;
      margin-top: 1px;
    }
    .more-search-text {
      /deep/ .icon-jiantou {
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
  .selectTag {
    display: flex;
    align-items: center;
  }

  .btn-group {
      height: 34px;
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
    .more-search-show {
      .advanced-search {
        max-height: 400px;
        transition: max-height 0.5s;
      }
      .btn-group {
        /deep/img {
          transform: rotate(0deg);
          transition: transform 0.2s;
        }
      }
    }
    .datepicker-wrap{
      display: flex;
      align-items: center;
      .line{
          height: 3px;
          width: 20px;
          background: #d2d8db;
          margin: 0 5px;
      }
      .hl-btn{
          color: #2C86F8;
          margin-left: 10px;
          cursor: pointer;
      }
      margin-right: 10px;
    }
/deep/ .ivu-radio {
  margin-right: 0 !important;
}
/deep/ .ivu-radio-wrapper-checked {
  background: rgba(44, 134, 248, 0.10) !important;
}
</style>