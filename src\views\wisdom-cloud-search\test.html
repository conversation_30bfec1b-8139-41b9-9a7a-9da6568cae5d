<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Document</title>
  <style>
    * {
      padding: 0;
      margin: 0;
    }
    .box {
      display: flex;
      flex-wrap: wrap;
      width: 300px;
      height: 300px;
      background: #ccc;
      margin: auto;
      border: 1px solid #f00;
      border-bottom: 0;
      border-right: 0;
    }
    .item {
      width: 100px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      border-right: 1px solid #333;
      border-bottom: 1px solid #888;
      box-sizing: border-box;
    }
    table {
      width: 300px;
      margin: auto;
      margin-top: 20px;
      border-collapse: collapse;
      border: 1px solid #f00;
    }
    tr td {
      border: 1px solid #f00;
      height: calc(300px / 3);
      text-align: center;
    }
    .box2 {
      width: 300px;
      margin: auto;
      margin-top: 20px;
      border-left: 1px solid #333;
      border-top: 1px solid #333;
      overflow: hidden;
    }
    .item2 {
      width: 100px;
      height: 100px;
      line-height: 100px;
      float: left;
      text-align: center;
      border-right: 1px solid #333;
      border-bottom: 1px solid #333;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
<div class="box">
  <p class="item">1</p>
  <p class="item">2</p>
  <p class="item">3</p>
  <p class="item">4</p>
  <p class="item">5</p>
  <p class="item">6</p>
  <p class="item">7</p>
  <p class="item">8</p>
  <p class="item">9</p>
</div>
<table >
  <tr>
    <td>1</td>
    <td>2</td>
    <td>3</td>
  </tr>
  <tr>
    <td>1</td>
    <td>2</td>
    <td>3</td>
  </tr>
  <tr>
    <td>1</td>
    <td>2</td>
    <td>3</td>
  </tr>
</table>
<div class="box2">
  <p class="item2">1</p>
  <p class="item2">2</p>
  <p class="item2">3</p>
  <p class="item2">4</p>
  <p class="item2">5</p>
  <p class="item2">6</p>
  <p class="item2">7</p>
  <p class="item2">8</p>
  <p class="item2">9</p>
</div>
<div class="checkbox-container">
  <div class="checkbox-list" id="checkboxContent">
    <input type="checkbox">
    <input type="checkbox">
    <input type="checkbox">
    <input type="checkbox">
    <input type="checkbox">
  </div>
  <label><input type="checkbox" id="allCheckbox" onclick="allHandle(this)">全选</label>
  <label><input type="checkbox" id="invertCheckbox" onclick="invertHandle(this)">反选</label>
</div>
<div class="box3">
  <input type="checkbox" id="changeInput">
</div>
</body>
<script>
var checkboxContent = document.getElementById('checkboxContent')
var inputs = document.querySelectorAll('.checkbox-list input')

function allHandle (that) {
  var isChecked = that.checked
  for (var i = 0; i < inputs.length; i++) {
    inputs[i].checked = isChecked
  }
}

function invertHandle (that) {
  for (var i = 0; i < inputs.length; i++) {
    inputs[i].checked = !inputs[i].checked
  }
}

checkboxContent.onclick = function (e) {
  var target = e.target
  if (typeof target.checked === 'boolean') {
    var isAll = true
    for (var i = 0; i < inputs.length; i++) {
      if (!inputs[i].checked) {
        isAll = false
        break
      }
    }
    console.log(isAll)
    document.getElementById('allCheckbox').checked = isAll
  }
}
var changeInput = document.getElementById('changeInput')
changeInput.addEventListener('change', function (e) {
  var isChecked = this.checked
  for (var i = 0; i < inputs.length; i++) {
    inputs[i].checked = isChecked
  }
})
for (var i = 0; i < inputs.length; i++) {
  if (!inputs[i].checked) {
    isAll = false
    break
  }
}
</script>
</html>
