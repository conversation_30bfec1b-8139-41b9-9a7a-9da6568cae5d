<template>
  <div class="national-point">
    <ui-search-tree
      class="ui-search-tree"
      placeholder="请输入组织机构名称或组织机构编码"
      node-key="channelId"
      :scroll="190"
      :tree-data="treeData"
      :tree-loading="treeLoading"
      :default-props="defaultProps"
      :current-node-key="currentNodeKey"
      @selectTree="selectTree"
    >
      <template #label="{ node, data }">
        <span :class="{ 'nav-not-selected': node.disabled, allowed: node.disabled }">
          {{ node.label }}
          {{ data.children ? `(${data.children.length})` : '' }}
        </span>
      </template>
    </ui-search-tree>
    <div class="right-module height-full auto-fill">
      <div class="search-module">
        <ui-label class="inline" label="关键词" :width="55">
          <Input
            v-model="searchData.keyWord"
            class="search-input"
            placeholder="请输入设备名称/设备编码/经纬度/安装地址"
          ></Input>
        </ui-label>
        <div class="ml-lg inline">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="resetSearchDataMx(searchData, search)">重置</Button>
        </div>
        <div class="button-div fr">
          <Button type="primary" class="mr-sm" @click="updateCatalog" :loading="updateCatalogLoading">
            <i class="icon-font icon-conggongxiangkugengxin f-14"></i>
            <span class="inline vt-middle ml-sm">从共享库更新</span>
          </Button>
          <Button type="primary" class="mr-sm" @click="exportExcel" v-if="showExport">
            <i class="icon-font icon-daochu f-14"></i>
            <span class="inline vt-middle ml-sm">导出全部</span>
          </Button>
          <Button type="primary" @click="updateDevice" :loading="updateLoading">
            <i class="icon-font icon-tongbudaoshebeiku f-14"></i>
            <span class="vt-middle ml-sm">同步到设备库</span>
          </Button>
        </div>
      </div>
      <div class="table-module auto-fill">
        <ui-table
          ref="selection"
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          @selectTable="selectionChange"
        >
        </ui-table>
        <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'nationalpoint',
  props: {},
  data() {
    return {
      loading: false,
      treeLoading: false,
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      treeData: [],
      channel: {},
      currentNodeKey: '',
      searchData: {
        channelId: '',
        keyWord: '',
        pageNumber: 1,
        pageSize: 50,
      },
      pageData: {
        pageNum: 1,
        pageSize: 50,
        totalCount: 0,
      },
      tableColumns: [
        {
          type: 'selection',
          width: 50,
          align: 'center',
        },
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'left',
        },
        {
          title: '设备编码',
          key: 'channelId',
          width: 230,
          align: 'left',
        },
        {
          title: '设备名称',
          key: 'name',
          align: 'left',
        },
        {
          title: '所属区域',
          key: 'civilCode',
          width: 100,
          align: 'left',
        },
        {
          title: '经度',
          key: 'longitude',
          width: 200,
          align: 'left',
        },
        {
          title: '纬度',
          key: 'latitude',
          width: 200,
          align: 'left',
        },
      ],
      tableData: [],
      selectedData: [],
      updateLoading: false,
      updateCatalogLoading: false,
    };
  },
  async created() {
    await this.initTreeData();
    if (this.treeData.length) {
      this.searchData.channelId = this.treeData[0].channelId;
      this.currentNodeKey = this.treeData[0].channelId;
    }
    this.init();
  },
  activated() {},
  methods: {
    selectTree(data) {
      this.searchData.channelId = data.channelId;
      this.channel = data;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.$http.get(equipmentassets.getListByChannelId, {
          params: this.searchData,
        });
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async initTreeData() {
      try {
        this.loading = true;
        this.treeLoading = true;
        const res = await this.$http.get(equipmentassets.videoDeviceChannelGetTree);
        this.treeData = this.$util.common.arrayToJson(
          this.$util.common.deepCopy(res.data.data),
          'channelId',
          'parentId',
        );
      } catch (err) {
        console.log(err);
      } finally {
        this.treeLoading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    async exportExcel() {
      try {
        let res = await this.$http.get(equipmentassets.downloadDeviceByChannelId, {
          responseType: 'blob',
          params: this.searchData,
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    selectionChange(val) {
      this.selectedData = val.map((row) => row.channelId);
    },
    async updateDevice() {
      try {
        this.updateLoading = true;
        let params = {
          channelIds: this.selectedData,
        };
        const res = await this.$http.post(
          equipmentassets.syncYSDeviceInfoList,
          Object.assign(params, this.searchData, {
            pageSize: this.pageData.totalCount,
          }),
        );
        this.selectedData = [];
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      } finally {
        this.updateLoading = false;
      }
    },
    async updateCatalog() {
      try {
        this.updateCatalogLoading = true;
        const res = await this.$http.get(equipmentassets.updateCatalog, {
          params: { orgId: this.searchData.channelId },
        });
        this.$Message.success(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.updateCatalogLoading = false;
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultExpandedKeys: 'common/getDefaultExpandedKeys',
    }),
    showExport() {
      return !this.channel.children || !this.channel.children.length;
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.national-point {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .ui-search-tree {
    float: left;
    width: 300px;
    padding: 20px 20px 0 20px;
  }
  .right-module {
    float: left;
    border-left: 1px solid var(--border-color);
    width: calc(100% - 300px);
    padding: 20px 20px 0 20px;
    .search-module {
      .search-input {
        width: 300px;
      }
      margin-bottom: 20px;
    }
  }
}
</style>
