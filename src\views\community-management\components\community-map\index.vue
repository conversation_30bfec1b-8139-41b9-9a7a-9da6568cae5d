<!--
 * @Date: 2025-01-16 11:07:33
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-03-28 14:41:48
 * @FilePath: \icbd-view\src\views\juvenile\components\juvenile-map\index.vue
-->
<template>
  <div class="juvenile-map">
    <div class="map-box">
      <MapSearchPlace
        class="search-box"
        @searchHandler="searchCommunity"
        @locationMap="locationMap"
      ></MapSearchPlace>
      <mapBase
        ref="mapBase"
        :mapLayerConfig="mapLayerConfig"
        :searchBtn="false"
        :juvenilePlaceList="allPlaceList"
        :layerCheckedNames="layerCheckedNames"
        :layerTypePlaceList="placeKindList"
        :isShowArchive="true"
        :goArchiveInfo="goArchiveInfo"
        @onload="mapLoad"
      />
      <PlaceList
        ref="placeKindListRef"
        class="place-dropdown"
        @changeLayerName="changeLayerName"
        :placeList="placeKindList"
        :title="'房产小区'"
      />
    </div>
  </div>
</template>

<script>
import mapBase from "@/views/juvenile/components/juvenile-map/mapBase.vue";
import PlaceList from "@/views/juvenile/components/juvenile-map/placeList.vue";
import MapSearchPlace from "./components/search.vue";
import {
  getConfigPlaceSecondLevels,
  getConfigPlaces,
} from "@/api/monographic/community-management.js";
import { placeType } from "@/map/core/enum/LayerType.js";
export default {
  name: "CommunityMap",
  components: {
    mapBase,
    PlaceList,
    MapSearchPlace,
  },
  props: {
    alarmList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      mapLayerConfig: {
        tracing: false, // 是否需要刻画轨迹
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: false, // 框选操作栏
        selectionResult: false, // 是否显示框选结果弹框
        resultOrderIndex: false, // 搜索结果排序,
        showLatestLocation: false, // 显示地图最新位置
      },
      allPlaceList: [],
      layerCheckedNames: [],
      placeKindList: [],
      keyWords: "",
    };
  },
  computed: {},
  methods: {
    async mapLoad() {
      await this.getPlaceKindList();
      await this.getPlaceList();
      this.$refs.placeKindListRef.initAllSelectPlace();
    },
    getPlaceList() {
      getConfigPlaces().then(({ data }) => {
        if (!Array.isArray(data)) {
          data = [];
        }
        this.allPlaceList = data.map((el) => {
          return {
            ...el,
            iconType: this.placeKindList.find(
              (item) => item.key == el.secondLevel
            )?.icon,
            LayerType: el.secondLevel,
          };
        });
      });
    },
    async getPlaceKindList() {
      const { data = [] } = await getConfigPlaceSecondLevels();
      this.placeKindList = data?.map((item) => {
        let icon = item.icon;
        if (icon) {
          icon = JSON.parse(icon);
          if (icon["font_class"]) {
            icon["font_class"] = placeType[icon["font_class"]]
              ? icon["font_class"]
              : "hotel";
          }
        } else {
          icon = {
            font_class: "hotel",
            color: "#EB8A5D",
          };
        }

        return {
          key: item.typeCode,
          title: item.typeName,
          icon: icon["font_class"] || "hotel",
          color: icon["color"] || "#EB8A5D",
        };
      });
    },
    changeLayerName(value) {
      this.layerCheckedNames = value;
    },
    searchCommunity() {
      let param = {
        name: this.keyWords, // 场所名称
        address: "", // 场所地址
      };
    },
    /**
     * @description: 查看设备档案
     * @param {string} deviceId 设备id
     */
    goArchiveInfo(item) {
      const { href } = this.$router.resolve({
        path: "/community-place-archive/place-dashboard",
        query: { archiveNo: item.id, source: "place" },
      });
      window.open(href, "_blank");
    },
    /**
     * 地图定位到选择的小区
     */
    locationMap(value) {
      try {
        const centerPoint = JSON.parse(value.centerPoint);
        // 组装参数
        let data = {
          properties: { ...value },
          center: centerPoint,
        };
        this.$refs.mapBase.aoiModelShow(data);
      } catch (e) {
        return this.$Message.warning("经纬度信息不全");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.juvenile-map {
  width: 100%;
  height: 100%;
  position: relative;

  .map-box {
    width: 100%;
    height: 100%;
    position: relative;

    .place-dropdown {
      position: absolute;
      top: 10px;
      right: 0;
    }
  }
  .search-box {
    position: absolute;
    top: 10px;
    left: 5px;
    width: 300px;
    z-index: 10;
  }
}
</style>
