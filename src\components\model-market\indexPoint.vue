<!--
    * @FileDescription: 列表索引
    * @Author: H
    * @Date: 2024/04/17
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="icon_num">
    <div class="icon_num_text">{{ index }}</div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    index: {
      type: [String, Number],
      default: 1,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {},
};
</script>

<style lang='less' scoped>
.icon_num {
  min-width: 30px;
  height: 30px;
  padding-top: 5px;
  background: url("~@/assets/img/map/red-position.png") no-repeat;
  background-size: 100% 100%;
  .icon_num_text {
    height: 16px;
    // border: 2px solid #EA4A36;
    // border-radius: 10px;
    // background: #fff;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
  }
}
</style>
