<template>
  <!-- 人像档案置信率弹框 -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <!--  v-if="!!trackList && trackList.detectionAmount != 0" -->
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="已聚档车辆"> </line-title>
        <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
        <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo">
          <div class="search-wrapper" slot="search">
            <ui-label class="fl" label="车牌号" width="70">
              <Input class="input-width" v-model="carNum" placeholder="请输入车牌号"></Input>
            </ui-label>
            <ui-label label="车牌颜色" :width="65" class="mr-lg" style="margin-left: 50px">
              <span class="all vt-middle" :class="isAll ? 'checked-all' : ''" @click="checkedAll">全部</span>
              <tag-color
                :mutiple-checked="true"
                :default-tag-list="defaultTagList"
                class="tag-color inline vt-middle"
                @tagCheckChange="tagCheckChange"
              ></tag-color>
            </ui-label>
            <ui-label :width="30" class="fl" label=" ">
              <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
              <Button type="default" class="mr-sm" @click="reast"> 重置 </Button>
            </ui-label>
          </div>
          <!-- 卡片 -->
          <template #card="{ row }">
            <UiGatherCard
              class="card"
              :list="row"
              :personTypeList="personTypeList"
              :cardInfo="cardInfo"
              @detail="detailInfo(row)"
            ></UiGatherCard>
          </template>
        </TableCard>
      </div>
    </div>
    <!-- 图像模式 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <car-detail ref="captureDetail" :resultId="this.$parent.row.resultId">
      <template #searchList>
        <trajectory-search class="mb-sm" @startSearch="startDetailSearch"></trajectory-search>
      </template>
    </car-detail>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }

    .card {
      width: calc(calc(100% - 40px) / 4);
      margin: 0 5px 10px;
    }
    .hearder-title {
      color: #fff;
      margin-top: 10px;
      font-size: 14px;
      .mr20 {
        margin-right: 20px;
      }
      .blue {
        color: #19c176;
      }
    }
    .ui-images {
      width: 56px;
      height: 56px;
      margin-bottom: 9px;
      .ui-image {
        min-height: 56px !important;
        /deep/ .ivu-spin-text {
          img {
            width: 56px;
            height: 56px;
            margin-top: 5px;
          }
        }
      }
    }
  }
  .choseName {
    margin: 0 10px 0 20px;
  }
  .all {
    display: inline-block;
    text-align: center;
    background-color: #02162b;
    border: 1px solid #174f98;
    color: var(--color-primary);
    width: 50px;
    height: 30px;
    line-height: 30px;
    margin-right: 10px;
    border-radius: 4px;
    cursor: pointer;
  }
  /deep/.tags-more {
    display: none;
  }
}
</style>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '700px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['注册登记车辆数量', '车辆聚档数量'],
        showData: [
          { name: '注册登记车辆数量', value: 0 },
          { name: '车辆聚档数量', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '车辆档案置信率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        plateColor: [],
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},

      bigPictureShow: false,
      carNum: '',
      cardInfo: [
        { name: '车牌号：', value: 'plateNo' },
        { name: '车牌颜色：', value: 'plateColor' },
        { name: '车辆类型：', value: 'vehicleClass' },
        { name: '抓拍总数：', value: 'urlNum', color: '#BC3C19' },
      ],
      defaultTagList: [
        { color: '#3153DC', checkable: false, name: '#3153DC', id: '1' },
        { color: '#DF9E1D', checkable: false, name: '#DF9E1D', id: '2' },
        { color: '#2AA543', checkable: false, name: '#2AA543', id: '3' },
        { color: '#000000', checkable: false, name: '#000000', id: '4' },
        { color: '#C6CDD5', checkable: false, name: '#C6CDD5', id: '5' },
        {
          color: 'linear-gradient(to top,#2AA543 0%,#2AA543 50%,#DF9E1D 51%,#DF9E1D 100%)',
          checkable: false,
          name: 'linear-gradient(to top,#2AA543 0%,#2AA543 50%,#DF9E1D 51%,#DF9E1D 100%)',
          id: '6',
        },
      ],
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            equipmentassets.confidenceList,
            Object.assign(
              parameter,
              {
                resultId: this.$parent.row.resultId,
                indexId: this.$parent.row.indexId,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
    };
  },
  async mounted() {
    // this.$refs.infoCard.info(true)
    // if (this.personTypeList.length == 0) await this.getPersonTypeList()
    this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    handleCheckStatus(row) {
      const flag = {
        1: '可用',
        2: '不可用',
      };
      return flag[row];
    },
    ...mapActions({
      getPersonTypeList: 'algorithm/getPersonTypeList',
    }),
    async init() {
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    search() {
      this.searchData.plateNo = this.carNum;
      this.$refs.infoCard.info(true);
    },
    reast() {
      this.carNum = '';
      this.searchData = {};
      this.searchData.plateColor = [];
      this.defaultTagList = [
        { color: '#3153DC', checkable: false, name: '#3153DC', id: '1' },
        { color: '#DF9E1D', checkable: false, name: '#DF9E1D', id: '2' },
        { color: '#2AA543', checkable: false, name: '#2AA543', id: '3' },
        { color: '#000000', checkable: false, name: '#000000', id: '4' },
        { color: '#C6CDD5', checkable: false, name: '#C6CDD5', id: '5' },
        {
          color: 'linear-gradient(to top,#2AA543 0%,#2AA543 50%,#DF9E1D 51%,#DF9E1D 100%)',
          checkable: false,
          name: 'linear-gradient(to top,#2AA543 0%,#2AA543 50%,#DF9E1D 51%,#DF9E1D 100%)',
          id: '6',
        },
      ];
      this.$refs.infoCard.info(true);
    },
    // 详情
    detailInfo() {
      // this.$refs.captureDetail.show(info)
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },

    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.post(equipmentassets.getDetailCount, {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          orgCode: 370000,
        });
        this.trackList = res.data.data;
        this.moduleData.rateValue = this.$parent.row.resultValue || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '注册登记车辆数量') {
          item.value = this.trackList.count;
        } else {
          item.value = this.trackList.confidenceRatCount;
        }
      });
      this.zdryChartObj.count = this.trackList.count + this.trackList.confidenceRatCount;
      let formatData = {
        seriesName: '检测轨迹图像',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    startSearch(searchData) {
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
    tagCheckChange(data) {
      this.searchData.plateColor = [];
      data.forEach((item) => {
        let tag = this.defaultTagList.find((row) => item.name === row.name);
        this.$set(tag, 'checkable', item.checkable);
        if (item.checkable) {
          this.searchData.plateColor.push(item.id);
        }
      });
    },
    checkedAll() {
      if (this.isAll) {
        this.searchData.plateColor = [];
        this.defaultTagList.forEach((item) => {
          this.$set(item, 'checkable', false);
          return item.id;
        });
      } else {
        this.searchData.plateColor = this.defaultTagList.map((item) => {
          this.$set(item, 'checkable', true);
          return item.id;
        });
      }
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/personTypeList',
    }),
    isAll() {
      return this.searchData.plateColor.length === this.defaultTagList.length;
    },
  },
  watch: {
    // '$parent.taskObj': {
    //   deep: true,
    //   handler: function(val) {
    //     this.info()
    //   },
    // },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    LookScene: require('@/components/look-scene').default,
    TableCard: require('./emphasis/components/tableCard.vue').default,
    UiGatherCard: require('./emphasis/components/ui-gather-card.vue').default,
    carDetail: require('@/components/car-detail.vue').default,
    TrajectorySearch: require('./emphasis/components/trajectory-search.vue').default,
    TagColor: require('@/views/standardtreatment/labelmanagement/components/tag-color.vue').default,
  },
};
</script>
