<template>
  <ul v-if="!!iconList.length" class="icon-ul">
    <li v-for="(item, index) of iconList" :key="index" class="icon-li f-14">
      <div class="icon-bg inline mr-sm">
        <i class="icon-font icon-delete f-16" :class="item.iconName"></i>
      </div>
      <span>{{ item.name }} </span>
      <span :style="item.countStyle"> {{ item.count }} </span>
    </li>
  </ul>
</template>
<script>
export default {
  props: {
    iconList: {
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .icon-font {
    color: #fff;
  }
  .icon-bg {
    background-image: url('~@/assets/img/async-bg-light.png');
    background-size: 100%;
  }
}
.icon-ul {
  display: flex;
  align-items: center;
}
.icon-li {
  margin-right: 40px;
  color: var(--color-content);
  height: 30px;
  line-height: 30px;
}
.icon-font {
  color: #73d2f6;
  margin: 0 auto;
  font-size: 12px !important;
}
.icon-bg {
  background: url('~@/assets/img/async-bg.png') no-repeat no-repeat;
  width: 30px;
  height: 32px;
  align-items: center;
  text-align: center;
}
</style>
