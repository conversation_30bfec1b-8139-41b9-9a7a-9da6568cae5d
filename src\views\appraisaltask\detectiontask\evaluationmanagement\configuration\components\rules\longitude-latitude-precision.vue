<template>
  <ui-modal v-model="visible" title="经纬度精度检测配置" width="39.06rem" @query="handleSave">
    <Form ref="formValidate" :label-width="0">
      <FormItem label="" prop="point">
        <div class="testing-item">
          <p>
            <span class="base-text-color">经纬度不能少于</span>
            <InputNumber v-model="longitudeLatitudeAccuracy" class="form-width ml-sm" />
            <span class="base-text-color ml-sm">位小数</span>
          </p>
          <p class="mt-sm">
            <span class="base-text-color mr-md inline vt-bottom">精度是否允许补零</span>
            <RadioGroup v-model="allowLastDigitsDot">
              <Radio :label="1" class="mr-md">允许</Radio>
              <Radio :label="0">不允许</Radio>
            </RadioGroup>
          </p>
          <p>
            <span v-show="allowLastDigitsDot === 0" class="color-failed mr-md"
              >说明：系统将清除小数末尾所有零后再判断精度。</span
            >
          </p>
        </div>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    /**
     * 指标详情
     */
    formData: {
      required: true,
      default: () => {},
    },
    /**
     * 规则详情
     */
    indexRule: {
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      longitudeLatitudeAccuracy: 6,
      indexConfig: {},
      allowLastDigitsDot: 1,
    };
  },
  created() {},
  methods: {
    resetForm() {
      this.longitudeLatitudeAccuracy = 6;
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getLngLat();
    },
    async getLngLat() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        if (data.rulePropertyList) {
          this.longitudeLatitudeAccuracy = data.rulePropertyList[0].repeatCount || 6;
          let extraParam = JSON.parse(data.extraParam);
          this.allowLastDigitsDot = extraParam.allowLastDigitsDot ? 1 : 0;
        }
      } catch (error) {
        console.log(error);
      }
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        if (!this.longitudeLatitudeAccuracy) {
          return this.$Message.error('请输入经纬度精度');
        }
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          repeatCount: this.longitudeLatitudeAccuracy,
          extraParam: JSON.stringify({ allowLastDigitsDot: !!this.allowLastDigitsDot }),
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.testing-item {
  width: 400px;
  text-align: left;
  margin: 0 auto;
}

.form-width {
  width: 118px;
}

@{_deep} .ivu-modal-body {
  padding: 20px 50px !important;
  text-align: center;
  height: 150px;
}
</style>
