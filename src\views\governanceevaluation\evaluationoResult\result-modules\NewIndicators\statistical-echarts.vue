<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <statistical-bar :staticsList="statisticsList" :qualified-val="qualifiedVal" :isEqual="false"></statistical-bar>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem">
          <template #rank-title>
            <span>{{ sortText }}</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart-tooltip-formatter
        class="line-chart"
        :active-index-item="activeIndexItem"
        :tooltip-formatter="tooltipFormatter"
      ></line-chart-tooltip-formatter>
    </div>
  </div>
</template>

<script>
import AbnormalListConfig from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AbnormalListConfig';
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'statistical-echarts',
  mixins: [dealWatch],
  props: {
    activeIndexItem: {},
    sortText: {
      type: String,
      default: '按合规率排序',
    },
  },
  data() {
    return {
      statisticsList: [],
      qualifiedVal: '1',
      paramsList: {},
      codeKey: '',
    };
  },
  created() {
    let informationStatistics =
      AbnormalListConfig.find((item) => item.indexType === this.activeIndexItem.indexType) || {};
    this.statisticsList = informationStatistics.abnormalList || [];
    this.statisticsList = this.statisticsList.map((item) => {
      if (item.isModifyName) {
        item.name = this.activeIndexItem.indexName;
      }
      return item;
    });
  },
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    async getStatInfo() {
      try {
        const params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          // access: "REPORT_MODE",
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
          // sortField: "ACTUAL_NUM"
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        this.statisticsList = this.statisticsList.map((item) => {
          item[item.key] = data[item.key];
          return item;
        });
        this.qualifiedVal = data.qualified;
      } catch (e) {
        console.log(e);
      }
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.getStatInfo();
    },
    tooltipFormatter(data) {
      let str = '';
      data.forEach((item) => {
        str = `<p>${item.data.startTime}</p>
               <p><span class="mr-md">${this.activeIndexItem.indexName}</span>${item.data.vertical || 0}%</p>
               <p><span class="mr-md">检测设备</span>${item.data.actualNum || 0}</p>
               <p><span class="mr-md">合格设备</span>${item.data.qualifiedNum || 0}</p>
               <p><span class="mr-md">不合格设备</span>${item.data.unqualifiedNum || 0}</p>`;
      });
      return str;
    },
  },
  computed: {},
  watch: {},
  components: {
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChartTooltipFormatter: require('../../components/line-chart-tooltip-formatter.vue').default,
    StatisticalBar: require('./components/statistical-bar').default,
  },
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      flex: 1;
      //width: calc(100% - 488px);
    }
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
  @{_deep} .info-statics-list ul {
    .custormWidth {
      //width: calc((100% - 20px) / 3) !important;
      height: 102px;
    }
    li:nth-child(1),
    li:nth-child(2),
    li:nth-child(3) {
      width: calc(calc(100% - 30px) / 3) !important;
    }
    li:nth-child(4),
    li:nth-child(5),
    li:nth-child(6),
    li:nth-child(7) {
      width: calc(calc(100% - 40px) / 4) !important;
    }
  }
}
</style>
