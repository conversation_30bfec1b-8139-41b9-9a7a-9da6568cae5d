<template>
  <div class="statistical-results auto-fill">
    <statistical-List :tableColumns="tableColumns" :result-data="resultData" @startSearch="startSearch">
      <Button slot="export" type="primary" class="button-export mb-sm" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu font-white mr-xs"></i>
        <span class="ml-xs">导出</span>
      </Button>
      <template #qualified="{ row }">
        <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
          {{ row.qualified === '1' ? '达标' : '不达标' }}
        </span>
      </template>
      <template #action="{ row }">
        <div class="action">
          <ui-btn-tip
            class="mr-sm"
            icon="icon-chakanxiangqing"
            content="详情"
            @handleClick="handleViewDetail(row)"
          ></ui-btn-tip>
        </div>
      </template>
    </statistical-List>
  </div>
</template>

<script>
import downLoadTips from '@/mixins/download-tips';
import { statisticalFields } from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoClockAccuracy/util/enum/tableColumns.js';
import detectionResult from '@/config/api/detectionResult';
export default {
  name: 'statistical-results',
  mixins: [downLoadTips],
  props: {
    activeIndexItem: {
      default: () => {},
    },
  },
  data() {
    return {
      exportLoading: false,
      tableColumns: [],
      resultData: {},
      searchData: {},
    };
  },
  created() {
    this.tableColumns = statisticalFields.defaultTableColumns;
  },
  methods: {
    // 获取统计列表数据
    async getStatisticalList() {
      let params = {
        // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
        batchId: 'A#3608#0000000035', // 任务执行批次id
        displayType: 'REGION', // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
        indexId: '1001', // 指标id
        orgRegionCode: '370100', // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
        qualified: this.searchData.searchData.qualified,
        pageNumber: this.searchData.pageNum,
        pageSize: this.searchData.pageSize,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.resultData = data;
      } catch (e) {
        console.log(e);
      }
    },
    startSearch(searchParams) {
      this.$set(this, 'searchData', searchParams);
      this.getStatisticalList();
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          batchId: 'A#3608#0000000035', // 任务执行批次id
          displayType: 'REGION', // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: '1001', // 指标id
          orgRegionCode: '370100', // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(detectionResult.exportStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    handleViewDetail() {},
  },
  components: {
    StatisticalList: require('../../ui-pages/statistical-list/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-results {
  padding: 10px 12px 0;
}
</style>
