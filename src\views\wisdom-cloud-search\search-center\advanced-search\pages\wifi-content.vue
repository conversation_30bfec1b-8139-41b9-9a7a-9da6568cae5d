<template>
  <section class="main-container">
    <div class="search-bar" v-show="!onlyResult">
      <searchWifi ref="searchWifi" @search="searchInfo"></searchWifi>
    </div>
    <div class="table-container">
      <div class="data-export">
        <div class="export-box" v-if="!mapOnData">
          <Button
            v-show="!onlyResult"
            class="mr"
            @click.stop="exportShow = true"
            size="small"
          >
            <ui-icon type="daoru" color="#2C86F8"></ui-icon>
            导出
          </Button>
          <exportBox
            ref="exportbox"
            v-if="exportShow"
            :needPic="false"
            @confirm="confirm"
            @cancel="hideExportModal"
          ></exportBox>
        </div>
        <Button class="mr" @click="handleSort('absTime')" size="small">
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button
          v-show="!onlyResult"
          @click="dataAboveMapHandler"
          size="small"
          style="float: right"
        >
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columnSum"
          :data="tableList"
          @on-selection-change="selectionChangeHandle"
        >
          <template #action="{ row }">
            <div class="btn-tips">
              <ui-btn-tip
                content="定位"
                icon="icon-location"
                class="mr-20 primary"
                @click.native="openDirectModel(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
      </div>
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[40, 80, 120]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>

      <direction-model ref="directionModel"></direction-model>

      <hl-modal
        v-model="modalShow"
        title="提示"
        :r-width="500"
        @onCancel="modalStatus(false, true)"
      >
        <div class="content">
          <p class="tipLoad">数据打包中，请等候......</p>
          <p>大约尚需{{ maybeTime }}秒</p>
        </div>
      </hl-modal>
      <ui-modal
        v-model="warnModalShow"
        title="提示"
        :r-width="500"
        @onCancel="modalStatus(true, false)"
        @onOk="onOk"
      >
        <div class="content">
          <p>当前存在打包任务，请确认是否离开！</p>
        </div>
      </ui-modal>
    </div>
  </section>
</template>
<script>
import searchWifi from "../components/search-wifi";
import {
  wifiRecordSearchEx,
  taskView,
  wifiDownload,
} from "@/api/wisdom-cloud-search";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { mapMutations, mapGetters } from "vuex";
import { myMixins } from "../../components/mixin/index.js";
import directionModel from "../components/direction-model";
import exportBox from "../../components/export/export-box.vue";
import hlModal from "@/components/modal/index.vue";
export default {
  mixins: [myMixins], //全局的mixin
  name: "wifiContent",
  components: {
    searchWifi,
    directionModel,
    exportBox,
    hlModal,
  },
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      queryParam: {
        sortField: "absTime",
        order: "desc",
      },
      tableList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 40,
      },
      total: 0,
      loading: false,
      timeUpDown: false,
      columns: [
        { type: "selection", width: 70, align: "center" },
        {
          title: "编号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "终端MAC", key: "mac" },
        { title: "采集时间", key: "absTime" },
        { title: "终端厂商", key: "brand" },
        { title: "采集设备地址", key: "detailAddress" },
        { title: "场所编号", key: "placeCode" },
        { title: "操作", slot: "action", width: 100 },
      ],
      selectionList: [],
      exportShow: false, // 导出弹框
      modalShow: false, // 导出loading弹框
      warnModalShow: false, // 中断导出提示框
      downTaskId: "", // 下载任务
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0, // 预计时间
    };
  },
  mounted() {
    window.addEventListener("click", this.hideExportModal);
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
    window.removeEventListener("click", this.hideExportModal);
  },
  activated() {
    if (this.mapOnData) {
      // 轨迹搜索上图返回加载数据
      this.selectionList = [];
      this.queryList();
    }
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.id);
    },
    // 只展示搜索结果，不能数据上图
    onlyResult() {
      return this.$route.query.noSearch;
    },
    columnSum() {
      if (this.onlyResult) {
        return this.columns.filter((v) => v.type !== "selection");
      }
      return this.columns;
    },
  },

  methods: {
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    /**
     * @description: 关闭导出框
     */
    hideExportModal() {
      this.exportShow = false;
    },

    /**
     * @description: 导出提示框
     * @param {boolean} modalShow 导出loading弹框
     * @param {boolean} warnModalShow 中断导出提示框
     */
    modalStatus(modalShow, warnModalShow) {
      this.modalShow = modalShow;
      this.warnModalShow = warnModalShow;
    },

    /**
     * @description: 中断下载
     */
    onOk() {
      this.modalStatus(false, false);
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },

    /**
     * @description: 轮询导出数据
     */
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },

    /**
     * @description: 确认导出
     * @param {object} param 导出配置
     */
    confirm(param) {
      let params = {
        ids: [],
        downloadPics: param.downloadPics,
        downloadSize: null,
        ...this.queryParam,
      };
      // type: 1导出选中数据
      if (param.type == "1") {
        params.ids = this.selectionList.map((e) => e.id);
        if (!params.ids.length) {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params.downloadSize = param.downloadSize;
      }
      this.hideExportModal();
      this.modalShow = true;
      wifiDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {
          // this.$refs.exportbox.handleEnd();
        });
    },
    // 排序
    handleSort(val) {
      this.queryParam.sortField = val;
      this.timeUpDown = !this.timeUpDown;
      this.queryParam.order = this.timeUpDown ? "asc" : "desc";
      this.queryList();
    },
    queryList() {
      this.queryParam = {
        ...this.queryParam,
        ...this.$refs.searchWifi.getQueryParams(),
      };
      this.loading = true;
      this.tableList = [];
      wifiRecordSearchEx({ ...this.queryParam, ...this.pageInfo })
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.tableList = entities;
          if (this.tableList.length) {
            this.tableList.forEach((e, idx) => {
              if (
                this.alreadyUpImageIds.length &&
                this.alreadyUpImageIds.includes(e.id)
              ) {
                e.isChecked = true;
                e._checked = true; // iview table的手动选中
                this.selectionList.push(e);
                this.$set(this.tableList, idx, e);
              }
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchInfo() {
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageSize = size;
      this.searchInfo();
    },

    selectionChangeHandle(val) {
      this.selectionList = val.map((item) => {
        return {
          ...item,
          isChecked: true,
        };
      });
    },
    dataAboveMapHandler() {
      let seleNum = this.selectionList.filter(
        (e) => !this.alreadyUpImageIds.includes(e.id)
      );
      if (!seleNum.length) {
        this.$Message.warning("请选择上图数据");
        return;
      }
      // 判断是否有坐标信息
      let listHasLocation = seleNum.filter((item) => {
        return item.geoPoint && item.geoPoint.lat && item.geoPoint.lon;
      });
      if (!listHasLocation.length) {
        this.$Message.warning("无经纬度信息，无法上图");
        return;
      }
      if (listHasLocation.length < seleNum.length) {
        this.$Message.warning(
          `已过滤${seleNum.length - listHasLocation.length}条无经纬度信息的数据`
        );
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        listHasLocation.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = JSON.stringify(this.getListNum);
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      listHasLocation.map((item) => {
        item.delePoints = true;
        item.deleType = "wifi";
      });
      this.$emit("dataAboveMapHandler", {
        type: "wifi",
        deleIdent: "wifi-" + this.getListNum,
        list: listHasLocation,
      });
    },
    /**
     * 收藏
     */
    collection(data, flag) {
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType: 10,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.queryList();
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.queryList();
        });
      }
    },
    openDirectModel(row) {
      if (!row.geoPoint || !row.geoPoint.lon || !row.geoPoint.lat) {
        this.$Message.warning("无坐标信息");
        return;
      }
      this.$refs.directionModel.show(row);
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
</style>
