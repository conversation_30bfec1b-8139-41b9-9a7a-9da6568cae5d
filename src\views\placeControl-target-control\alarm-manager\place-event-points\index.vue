<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: zhujin<PERSON><PERSON> zhu<PERSON>@qishudi.com
 * @LastEditTime: 2025-05-09 11:16:30
 * @FilePath: \icbd-view\src\views\placeControl-target-control\alarm-manager\place-event-points\index.vue
-->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="livingAlone"
      :radioList="[]"
      :noMore="true"
    >
      <query ref="slotQuery" />
    </searchForm>
    <!-- 列表 -->
    <div class="list">
      <ui-table
        ref="tableRef"
        :columns="columns"
        :data="tableList"
        :loading="tableLoading"
      >
        <template #image="{ row }">
          <div
            class="table-image"
            style="width: 80px; height: 80px; margin: auto"
          >
            <ui-image viewer type="place" :src="row.image" />
          </div>
        </template>
        <template #score="{ row, index }">
          <span
            class="click-point"
            style="color: #ea4a36"
            >{{ row.score }}</span
          >
        </template>
        <template #abnormalBehaviorAlarmCount="{ row, index }">
          <div>重点人发现：<span class="primary">{{ row.emphasisAlarmCount }}</span></div>
          <div>疑似重点人聚集：<span class="primary">{{ row.gatheredAlarmCount }}</span></div>
          <div>场所异常行为：<span class="primary">{{ row.abnormalBehaviorAlarmCount }}</span></div>
        </template>
      </ui-table>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="tableList"
    ></CaptureDetail>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import searchForm from "../../components/search-form.vue";
import query from "./components/query.vue";
import { queryEventScore } from "@/api/monographic/place.js";
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
export default {
  name: "",
  components: { searchForm, query, CaptureDetail },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 0, value: "未处理" },
        { key: 1, value: "有效" },
        { key: 2, value: "无效" },
      ],
    },
  },
  data() {
    return {
      tableList: [],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "场所照片", slot: "image", align: "center", width: 100 },
        {
          title: "场所名称",
          key: "placeName",
          align: "center",
          render: (h, { row }) => {
            return h("div", { class: "primary" }, row.placeName || "--");
          },
        },
        { title: "一级分类", key: "placeFirstType", align: "center", width: 120 },
        { title: "二级分类", key: "secondLevelName", align: "center", width: 120 },
        { title: "所属区县", key: "adname", align: "center" },
        { title: "所属街道", key: "townname", align: "center", width: 120 },
        { title: "场所地址", key: "address", align: "center" },
        { title: "总积分", slot: "score", align: "center" },
        { title: "最新报警时间", key: "alarmTime", align: "center" },
        { title: "事件统计", slot: "abnormalBehaviorAlarmCount", align: "center" },
      ],
      tableLoading: false,
      taskList: [],
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.query();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 获取报警列表
     */
    tableListFn() {
      this.tableLoading = true;
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        // ...this.$refs.slotQuery.getQueryParams(),
      };
      queryEventScore(data)
        .then((res) => {
          this.total = res.data?.total || 0;
          this.tableList =
            res?.data?.entities || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @description: 手动触发查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      // this.$refs.slotQuery.reset();
      this.query();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },

    /**
     * @description: 改变每页数量
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },
    // 跳转档案
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        path: "/community-archive/people-dashboard",
        query: {
          archiveNo: item.idCardNo,
          source: "people",
          initialArchiveNo: item.idCardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .list {
    flex: 1;
    padding-top: 16px;
    overflow: scroll;
    /deep/ .ui-table {
      height: 100%;
    }
  }
}
.primary {
  color: #2c86f8;
}
.click-point {
  cursor: pointer;
}
</style>
