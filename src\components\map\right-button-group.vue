<template>
  <div class="button-group">
    <div>
      <i class="iconfont icon-location1" @click="pointMap('homing')"></i>
    </div>
    <div>
      <i class="iconfont icon-jia" @click="pointMap('enlarge')"></i>
    </div>
    <div>
      <i class="iconfont icon-jian" @click="pointMap('narrow')"></i>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    pointMap(type) {
      this.$emit("pointMap", type);
    },
  },
};
</script>
<style lang="less" scoped>
.button-group {
    position: absolute;
    right: 10px;
    bottom: 10px;
    > div {
        height: 26px;
        width: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #ffffff;
        box-shadow: 0px 2px 3px 0px rgba(0, 0, 0, 0.3);
        
        .iconfont {
            color: #888888;
            font-size: 18px;
            cursor: pointer;
            &:hover{
                color: #2c86f8;
            }
        }
    }
    > div:first-child {
        margin-bottom: 10px;
    }
    > div:last-child {
        border-top: none;
    }
}
</style>
