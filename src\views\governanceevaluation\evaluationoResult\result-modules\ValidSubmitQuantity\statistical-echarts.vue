<template>
  <div class="statistical-echarts">
    <div class="statistical-echarts-bar">
      <statistical-bar
        v-if="['1', '2', '3'].includes(this.dataType)"
        :statics-list="staticsList"
        :qualified-val="qualifiedVal"
      ></statistical-bar>
    </div>
    <div class="reporting mt-sm" v-if="['1', '2', '3'].includes(this.dataType)">
      <city-report-chart
        :city-charts-data="cityChartsData"
        :city-chart-title="cityChartTitle"
        :echartsLoading="numRankInfoLoadig"
        @changeCitySortField="changeCitySortField"
      ></city-report-chart>
    </div>
    <div class="reporting mt-sm" v-if="['1'].includes(this.dataType)">
      <reporting-chart
        :activeIndexItem="activeIndexItem"
        :rank-info-list="rankInfoList"
        :echartsLoading="barGraphRankInfoLoading"
        @changeAreaSortField="changeAreaSortField"
      ></reporting-chart>
    </div>
    <div class="history mt-sm">
      <line-chart
        class="line-chart"
        :activeIndexItem="activeIndexItem"
        :date-type="dateType"
        :line-data="lineData"
        :echartsLoading="trendLoading"
        @statusChange="statusChange"
      >
        <template #custom-node>
          <tag-view slot="mid" :list="tagList" @tagChange="changeStatus" ref="tagView" class="tag-view"></tag-view>
          <DatePicker
            class="ml-md"
            ref="DatePicker"
            v-if="dateType === 'DAY'"
            type="month"
            placeholder="请选择月份"
            format="yyyy年MM月"
            :value="month"
            :editable="false"
            @on-change="handleChange"
          ></DatePicker>
          <DatePicker
            class="ml-md"
            ref="yearPicker"
            v-else-if="dateType === 'MONTH'"
            type="year"
            placeholder="请选择年"
            format="yyyy年"
            :value="year"
            :editable="false"
            @on-change="handleChangeYear"
          ></DatePicker>
        </template>
      </line-chart>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'statistical-echarts',
  mixins: [dealWatch],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      staticsArr: [
        {
          dataType: ['1'],
          icon: 'icon-sheng',
          iconColor: $cssVar('--icon-card-gradient-green'),
          liBg: $cssVar('--bg-card-green'),
          boxShadow: $cssVar('--bg-card-green-shadow'),
          counts: [
            { label: '设备总数', key: 'deviceNum', value: 0 },
            { label: '省满分数', key: 'fullDeviceNum', value: 0 },
            { label: '省达标数', key: 'standardNum', value: 0 },
          ],
          res: {
            title: '达标情况',
            value: '',
            key: 'qualifiedDesc',
            color: [$cssVar('--color-success'), $cssVar('--color-failed')],
            qualify: '',
            qualifyKey: 'qualified',
          },
        },
        {
          dataType: ['1'],
          icon: 'icon-shi',
          iconColor: $cssVar('--icon-card-gradient-cyan'),
          liBg: $cssVar('--bg-card-cyan'),
          boxShadow: $cssVar('--bg-card-cyan-shadow'),
          counts: [
            { label: '地市数量', key: 'cityCount', value: 0 },
            { label: '达标地市数', key: 'qualifiedCityCount', value: 0 },
            { label: '不达标地市数', key: 'unqualifiedCityCount', value: 0 },
          ],
          res: {
            title: '地市达标率',
            value: '',
            key: 'cityResultValue',
            color: [$cssVar('--color-success'), $cssVar('--color-failed')],
            qualify: '',
            qualifyKey: 'cityQualified',
          },
        },
        {
          dataType: ['2'],
          icon: 'icon-shi',
          iconColor: $cssVar('--icon-card-gradient-cyan'),
          liBg: $cssVar('--bg-card-cyan'),
          boxShadow: $cssVar('--bg-card-cyan-shadow'),
          counts: [
            { label: '设备总数', key: 'deviceNum', value: 0 },
            { label: '市满分数', key: 'fullDeviceNum', value: 0 },
            { label: '市达标数', key: 'standardNum', value: 0 },
          ],
          res: {
            title: '达标情况',
            value: '',
            key: 'qualifiedDesc',
            color: [$cssVar('--color-success'), $cssVar('--color-failed')],
            qualify: '',
            qualifyKey: 'qualified',
          },
        },
        {
          dataType: ['1', '2'],
          icon: 'icon-quxian',
          iconColor: $cssVar('--icon-card-gradient-orange'),
          liBg: $cssVar('--bg-card-orange'),
          boxShadow: $cssVar('--bg-card-orange-shadow'),
          counts: [
            { label: '区县数量', key: 'areaCount', value: 0 },
            { label: '达标区县数', key: 'qualifiedAreaCount', value: 0 },
            { label: '不达标区县数', key: 'unqualifiedAreaCount', value: 0 },
          ],
          res: {
            title: '区县达标率',
            value: '0',
            key: 'areaResultValue',
            color: [$cssVar('--color-success'), $cssVar('--color-failed')],
            qualify: '',
            qualifyKey: 'areaQualified',
          },
        },
        {
          dataType: ['3'],
          icon: 'icon-shi',
          iconColor: $cssVar('--icon-card-gradient-cyan'),
          liBg: $cssVar('--bg-card-cyan'),
          boxShadow: $cssVar('--bg-card-cyan-shadow'),
          counts: [
            { label: '设备总数', key: 'deviceNum', value: 0 },
            { label: '区县满分数', key: 'fullDeviceNum', value: 0 },
            { label: '区县达标数', key: 'standardNum', value: 0 },
          ],
          res: {
            title: '达标情况',
            value: '',
            key: 'qualifiedDesc',
            color: [$cssVar('--color-success'), $cssVar('--color-failed')],
            qualify: '',
            qualifyKey: 'qualified',
          },
        },
        {
          key: 'resultValueFormat',
          name: this.activeIndexItem.indexName,
          value: '0%',
          icon: 'icon-sheng',
          iconColor: $cssVar('--icon-card-gradient-blue'),
          liBg: $cssVar('--bg-card-blue'),
          boxShadow: $cssVar('--bg-card-blue-shadow'),
          type: 'percentage', // number数字 percentage 百分比
          textColor: $cssVar('--font-card-blue'),
        },
      ],
      staticsList: [],
      dataType: '',
      citySortField: null,
      areaSortField: null,
      cityChartsData: {
        xData: [],
        qualifiedData: [],
        unqualifiedData: [],
      },
      rankList: [],
      rankInfoList: [],
      qualifiedVal: '1',
      dateType: 'DAY',
      tagList: ['日', '月'],
      month: '',
      year: null,
      monthIndex: '', // 选中月份序号
      lineData: {},
      cityChartTitle: '地市上报数量',
      numRankInfoLoadig: false,
      barGraphRankInfoLoading: false,
      trendLoading: false,
    };
  },
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    async init() {
      // let {indexType} = this.$route.query
      // let quantityStandardList = ['FACE_QUANTITY_STANDARD', 'VIDEO_QUANTITY_STANDARD', 'VEHICLE_QUANTITY_STANDARD']
      this.getDate();
      await this.getStatInfo();
      await this.getNumRankInfo();
      if (this.dataType === '1') await this.getBarGraphRankInfo();
      await this.getDayCaptureStatistics();
    },
    async getStatInfo() {
      try {
        const params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          // access: "REPORT_MODE",
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
          // sortField: "ACTUAL_NUM"
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        if (!data || !data.dataType) {
          this.dataType = '';
          return false;
        }
        this.dataType = data.dataType;
        this.cityChartTitle = this.dataType === '1' ? '地市上报数量' : '区县上报数量';
        this.staticsList = this.staticsArr.filter((item) => !!item.name || item.dataType.includes(this.dataType));
        this.staticsList = this.staticsList.map((item) => {
          if (item.name) {
            item.value = data[item.key] || '0%';
          } else {
            item.counts.forEach((countItem) => {
              countItem.value = data[countItem.key] || 0;
            });
            item.res.value = data[item.res.key] || 0;
            item.res.qualified = data[item.res.qualifyKey];
          }
          return item;
        });
        this.qualifiedVal = data.qualified;
      } catch (e) {
        console.log(e);
      }
    },
    // 地市上报情况
    async getNumRankInfo() {
      this.numRankInfoLoadig = true;
      let params = {
        // access: "REPORT_MODE",
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.statisticType,
        orgRegionCode: this.paramsList[this.codeKey],
        sortField: this.citySortField ? 'ACTUAL_NUM' : null,
      };
      this.cityChartsData = {
        xData: [],
        qualifiedData: [],
        unqualifiedData: [],
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getNumRankInfo, params);
        if (!data || !data.length) return false;
        this.cityChartsData.xData = data.map((item) => {
          if (item.qualified === '1') {
            this.cityChartsData.qualifiedData.push(item.actualNum);
            this.cityChartsData.unqualifiedData.push(0);
          } else {
            this.cityChartsData.unqualifiedData.push(item.actualNum);
            this.cityChartsData.qualifiedData.push(0);
          }
          return item.regionName;
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.numRankInfoLoadig = false;
      }
    },
    // 区县上报情况
    async getBarGraphRankInfo() {
      this.barGraphRankInfoLoading = true;
      let params = {
        access: this.paramsList.access || 'REPORT_MODE',
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.statisticType,
        orgRegionCode: this.paramsList[this.codeKey],
        sortField: this.areaSortField ? 'AREA_RESULT_VALUE' : null,
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getBarGraphRankInfo, params);
        this.rankInfoList = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.barGraphRankInfoLoading = false;
      }
    },
    // 获取当前月份
    getDate() {
      this.year = new Date().getFullYear() + '';
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 10 ? month : `0${month}`;
      this.month = `${this.year}-${this.monthIndex}`;
    },
    // 变化趋势
    async getDayCaptureStatistics() {
      try {
        this.trendLoading = true;
        let params = {
          taskSchemeId: this.paramsList.taskSchemeId,
          indexId: this.paramsList.indexId,
          indexType: this.paramsList.indexType,
          dateType: this.dateType,
          year: this.year,
          month: this.monthIndex,
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getHistoryTrend, params);
        this.lineData = data || {};
      } catch (err) {
        console.log(err);
      } finally {
        this.trendLoading = false;
      }
    },
    changeCitySortField(val) {
      this.citySortField = val;
      this.getNumRankInfo();
    },
    changeAreaSortField(val) {
      this.areaSortField = val;
      this.getBarGraphRankInfo();
    },
    /**
     * DAY/MONTH
     * @param index
     * @param item
     */
    changeStatus(index, item) {
      if (item === '日') {
        this.dateType = 'DAY';
        this.getDate();
      } else if (item === '月') {
        this.month = null;
        this.monthIndex = 0;
        this.dateType = 'MONTH';
      }
      this.getDayCaptureStatistics();
    },
    statusChange(type) {
      if (type === 'DAY') {
        this.getDate();
      }
      this.getDayCaptureStatistics();
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    handleChangeYear(val) {
      const yearArr = val.split('年');
      this.year = yearArr[0];
      this.getDayCaptureStatistics();
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.init();
    },
  },
  components: {
    StatisticalBar: require('./components/statistical-bar').default,
    ReportingChart: require('./components/reporting-chart.vue').default,
    LineChart: require('./components/line-chart.vue').default,
    CityReportChart: require('./components/city-report-chart.vue').default,
    TagView: require('@/components/tag-view.vue').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-echarts {
  padding: 10px;
  overflow-y: auto;
  &-bar {
    width: 100%;
  }
  .reporting {
    width: 100%;
    height: 310px;
  }
  .history {
    width: 100%;
    height: 330px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
