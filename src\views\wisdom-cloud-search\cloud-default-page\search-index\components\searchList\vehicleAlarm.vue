<!--
    * @FileDescription: 车辆报警
    * @Author: H
    * @Date: 2023/10/07
 * @LastEditors: du<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2024-06-18 15:06:17
 -->
 <template>
  <div class="personnel_alarm">
    <vehicleCard
      :isShowCheckBox="false"
      :collectIcon="false"
      class="alarnRow"
      @click.native="detailFn(itemList)"
      @collection="collection"
      @refresh="tableListFn()"
      :alarmInfo="itemList"
      @singleChecked="singleChecked"
      :showOperate="false"
    />
  </div>
</template>

<script>
import vehicleCard from "@/views/target-control/alarm-manager/vehicle/components/vehicle-card.vue";
export default {
  props: {
    itemList: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    vehicleCard,
  },
  data() {
    return {};
  },
  methods: {
    singleChecked() {},
    tableListFn() {},
    detailFn() {
      this.$emit("detailFn");
    },
    collection() {},
  },
};
</script>

<style lang='less' scoped>
.personnel_alarm {
  width: calc(~"20% - 10px");
  margin-left: 10px;
}
</style>