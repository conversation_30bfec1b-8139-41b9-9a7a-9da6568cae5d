<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics></index-statistics>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem">
          <template #rank-title>
            <span>按提升率排名</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart" :tooltip-formatter="tooltipFormatter"></line-chart>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import lineChartTooltip from './line-chart-tooltip.vue';

export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('./subordinate-chart.vue').default,
    LineChart: require('@/views/governanceevaluation/evaluationoResult/components/line-chart.vue').default,
  },
  data() {
    return {
      tooltipFormatter: (data) => {
        let lineChartTooltipConstructor = Vue.extend(lineChartTooltip);
        let _this = new lineChartTooltipConstructor({
          el: document.createElement('div'),
          data() {
            return {
              data,
              dateType: this.dateType,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: 100%;
      @{_deep} .index-statistics .information-statistics .statistics-ul li {
        width: calc((100% / 3) - 7.8px) !important;
        &:nth-child(3) {
          margin-right: 0 !important;
        }
      }
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
