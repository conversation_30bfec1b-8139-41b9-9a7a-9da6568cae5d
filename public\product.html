<!DOCTYPE html>
<html lang="en" class="page-theme">
<head>
    <meta charset="UTF-8">
    <title>视频图像信息综合应用平台</title>
    <link type="image/x-icon" rel="shortcut icon" href="./images/badges.png"/>
    <script src="./config.js"></script>
    <style>
        html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            font: inherit;
            vertical-align: baseline;
        }

        html {
            font-size: 12px;
            font-family: "Microsoft Yahei", "微软雅黑", "宋体";
            color: #333333;
        }

        .ml-20 {
            margin-left: 20px;
        }

        /*清除浮动样式*/
        .clearFix:after {
            content: "";
            display: block;
            clear: both;
        }

        .clearFix {
            zoom: 1;
        }

        .wrapper {
            color: #333333;
            line-height: normal;
            height: 100%;
            width: 100%;
            position: absolute;
            min-width: 1366px;
            min-height: 630px;
        }

        #header {
            height: 55px;
            position: relative;
            width: 100%;
            z-index: 1;
            background: #2D87F9;
        }

        #badges {
            color: #FFFFFF;
            display: block;
            font-size: 24px;
            line-height: 55px;
            text-decoration: none;
            padding-left: 20px;
            /* background: transparent url(../images/helper/badges.png) 23px center no-repeat; */
            font-family: "Microsoft Yahei", "微软雅黑", "宋体";
            cursor: default;
        }

        #content {
            width: 100%;
            background: #F8F8F8;
            position: absolute;
            top: 50px;
            bottom: 0;
            overflow-y: auto;
        }

        #content .plugins {
            width: 980px;
            margin: 12px auto 0;

        }

        #content .plugin {
            background: #ffffff;
            border: 1px solid #eeeeee;
            padding: 40px;
            margin-bottom: 10px;
        }

        .plugin .source {
            float: left;
        }
        .source .pluginImg {
            height: 40px;
            font-size: 18px;
            line-height: 40px;
            padding-left: 50px;
        }

        .source .pluginImg{
            background: transparent url("../images/helper/product.png") 0 0 no-repeat;
        }
        .source .pluginImgPlayer {
            height: 40px;
            font-size: 18px;
            line-height: 40px;
            padding-left: 50px;
            background: transparent url("../images/helper/setup.ico") 0 0 no-repeat;
            background-size: 40px 40px;
        }
        .source .download {
            display: block;
            width: 188px;
            height: 62px;
            margin-top: 20px;
            background: url("../images/helper/product.png") 0 0 no-repeat;

        }

        .source .player {
            background-position: 3px -700px;
        }

        .source .chrome {
            background-position: -130px -500px;
        }

        .source .npExtension {
            background-position: -130px -395px;
        }

        .source .IVL {
            background-position: 4px -702px;
        }

        .source .flash {
            background-position: 4px -398px;
            width: 40%;
        }

        .source .VLC {
            background-position: -130px -600px;
        }

        .plugin .intro {
            margin-left: 355px;
            font-size: 14px;
            line-height: 24px;
        }

        .intro h3 {
            font-size: 16px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .intro li {
            list-style: disc inside none;
        }

        #footer {
            line-height: 50px;
            text-align: center;
            border-top: 1px solid #dddddd;

        }
        #badges{
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        #badges img{
            width: 40px;
            vertical-align: text-bottom;
        }
        #badges p{
            font-weight: bold;
            color: #fff;
            margin-left: 20px;
        }
    </style>
</head>
<body>

<div class="wrapper">

    <header id="header">
        <div>
            <a href="/index.html" id="badges">
                <img id="logo" src="../images/helper/badges.png" alt="">
                <p>视频图像信息综合应用平台</p>
            </a>
        </div>
    </header>

    <div id="content">

        <div class="plugins">
            <!-- <div class="plugin clearFix">
                <div class="source">
                    <h3 class="pluginImg player">H5VPPlayer播放器</h3>
                    <a href="./H5VPSetup_3.0.7.exe" class="download" id="pvaPlayer"></a>
                </div>
                <div class="intro">
                    <h3 class="">最新版本：<span id="playerVersion">3.0.7</span></h3>
                    <ul>
                        <li>支持启数视频云平台、RTSP/RTMP/HIS流媒体协议的播放。</li>
                        <li>可定制化多分屏、UI工具条。</li>
                        <li>兼容chrome60及以上版本浏览器。</li>
                    </ul>
                </div>
            </div> -->
            <div class="plugin clearFix">
                <div class="source">
                    <h3 class="pluginImgPlayer">QSPlayer播放器</h3>
                    <a href="./QSPlayerSetup.exe" class="download" id="pvaPlayer"></a>
                </div>
                <div class="intro">
                    <h3 class="">最新版本：<span id="playerVersion">1.0.1.2</span></h3>
                    <ul>
                        <li>支持启数视频云平台、RTSP/RTMP/HIS流媒体协议的播放。</li>
                        <li>可定制化多分屏、UI工具条。</li>
                        <li>兼容chrome60及以上版本浏览器。</li>
                    </ul>
                </div>
            </div>

            <div class="plugin clearFix">
                <div class="source">
                    <h3 class="pluginImg chrome">chrome120.0.6099.110</h3>
                    <a href="./120.0.6099.110_chrome_installer_x64.exe" class="download"></a>
                </div>
                <div class="intro">
                    <h3 class="">最新版本：120.0.6099.110<span id="chromeVersion"></span></h3>
                    <ul>
                        <li>win10以上系统可安装</li>
                        <li>目前提供的是64位安装文件</li>
                        <li>
                            如需使用下载录像功能，请先按以下设置浏览器：
                                <p class="ml-20">1、浏览器输入：chrome://flags/#block-insecure-private-network-requests</p>
                                <p class="ml-20">2、将block-insecure-private-network-requests设置为Disabled</p>
                                <p class="ml-20">3、点击右下角Relaunch重启浏览器</p>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="plugin clearFix">
                <div class="source">
                    <h3 class="pluginImg chrome">chrome109.0.5414.120</h3>
                    <a href="./109.0.5414.120_chrome_installer.exe" class="download"></a>
                </div>
                <div class="intro">
                    <h3 class="">最新版本：109.0.5414.120<span id="chromeVersion"></span></h3>
                    <ul>
                        <li>win7系统可安装</li>
                        <li>目前提供的是64位安装文件</li>
                        <li>
                            如需使用下载录像功能，请先按以下设置浏览器：
                                <p class="ml-20">1、浏览器输入：chrome://flags/#block-insecure-private-network-requests</p>
                                <p class="ml-20">2、将block-insecure-private-network-requests设置为Disabled</p>
                                <p class="ml-20">3、点击右下角Relaunch重启浏览器</p>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="plugin clearFix">
                <div class="source">
                    <h3 class="pluginImg chrome">极速浏览器</h3>
                    <a href="./360csex_setup.exe" class="download"></a>
                </div>
                <div class="intro">
                    <h3 class="">最新版本：22.1.1056.64<span id="chromeVersion"></span></h3>
                    <ul>
                        <li>视综平台极速浏览器</li>
                    </ul>
                </div>
            </div>

            <!-- <div class="plugin clearFix">
                <div class="source">
                    <h3 class="pluginImg VLC">视云闪播</h3>
                    <a href="./shiyunshanbo.exe" class="download"></a>
                </div>
                <div class="intro">
                    <h3 class="">最新版本：2.2.4<span id="chromeVersion"></span></h3>
                    <ul>
                        <li>目前提供的是64位安装文件</li>
                        <li>跨平台多媒体播放器</li>
                        <li>可播放大多数多媒体文件</li>
                    </ul>
                </div>
            </div> -->

        </div>
        <footer id="footer">
            <div>南京启数智能系统有限公司版权所有 &nbsp;&nbsp;©&nbsp;2023</div>
        </footer>
    </div>
</div>
<script>
        fetch('/qsdi-system-service/token/getApplicationInfo?applicationCode=' + applicationCode)
            .then(response => response.json())
            .then(data => {
                if(data.code==200){
                    var info =  data.data[0];
                    document.getElementById("logo").src= info.logoUrl;
                    document.querySelector("#badges p").innerHTML= info.applicationName;
                    document.title = info.applicationName;
                }
                
            })
            .catch(error => {
                console.error(error);
            });
</script>
</body>
</html>