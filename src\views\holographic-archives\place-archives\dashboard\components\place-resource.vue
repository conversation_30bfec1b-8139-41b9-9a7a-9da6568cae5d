<template>
  <ui-module class="card-container" title="场所资源">
    <div class="info-wrapper">
      <!-- 设备统计 -->
      <div class="device-parse-wrapper">
        <ui-icon type="doubleleft" @click.native="arrowTo(false)"></ui-icon>
        <div class="device-type-wrapper">
          <div
            v-for="item in deviceTypeList"
            :class="{ active: curDeviceType === item.type, common: true }"
            :key="item.deviceType"
            @click="changeResourceType(item.type)"
          >
            <span class="device-type">{{ item.label }}</span>
            <span class="device-count">{{ getCount(item.type) }}</span>
          </div>
        </div>
        <ui-icon type="doubleright" @click.native="arrowTo(true)"></ui-icon>
      </div>
      <!-- 设备列表 -->
      <div class="device-list-wrapper">
        <template v-if="deviceList.length">
          <div
            class="common device-item"
            v-for="item in deviceList"
            :key="item.id"
          >
            <div
              :class="['device-label', deviceIsOffline(item) ? 'offline' : '']"
            >
              <ui-icon :type="icons.icon" :color="icons.color"></ui-icon>
              <span>{{ item.deviceName }}</span>
            </div>
            <div class="more-operate">
              <el-popover
                ref="popover"
                placement="bottom-end"
                trigger="hover"
                popper-class="more-list"
              >
                <ui-icon
                  class="more-icon"
                  slot="reference"
                  type="gengduo"
                ></ui-icon>
                <ul>
                  <li @click="viewDetail(item)">查看详情</li>
                  <li @click="locationOnMap(item)">地图定位</li>
                  <!-- WiFi，RFID，电围没有加入分组功能，暂时没有档案详情，也不展示【查看档案】入口 -->
                  <template
                    v-if="
                      ![
                        'Camera_WIFI',
                        'Camera_Electric',
                        'Camera_RFID',
                      ].includes(curDeviceType)
                    "
                  >
                    <li @click="viewArchive(item)">查看档案</li>
                    <li @click="joinGroup(item.deviceId)">加入分组</li>
                  </template>
                  <li @click="viewCatalog(item.deviceId)">查看目录</li>
                  <li @click="copyName(item.deviceName)">复制名称</li>
                </ul>
              </el-popover>
            </div>
          </div>
        </template>
        <ui-empty v-else></ui-empty>
      </div>
    </div>
    <ui-loading v-show="loading"></ui-loading>
    <!-- 分组 -->
    <AddGroup ref="addGroupRef" />
    <!-- 设备详情 -->
    <Modal
      class="device-info-modal"
      v-model="showDeviceInfo"
      footer-hide
      draggable
      :width="isShowModelDetail ? 650 + 532 : 532"
      @on-cancel="closeDeviceInfoModal"
    >
      <template #header>
        <p class="modalHeader">
          <span>{{ curDeviceName }}</span>
        </p>
      </template>
      <Device
        ref="deviceRef"
        :isopen="isopen"
        @isModelDetail="isShowModelDetail = !isShowModelDetail"
      />
    </Modal>
  </ui-module>
</template>

<script>
import { copyText } from "@/util/modules/common";
import { getDevicesInPlaceAPI } from "@/api/placeArchive";
import UiModule from "../../components/ui-module.vue";
import AddGroup from "./add-group.vue";
import { getTreeAncestors } from "@/api/player";
import Device from "@/views/operations-on-the-map/map-default-page/components/map-dom/device-map-dom.vue";

export default {
  name: "PlaceResource",
  components: { UiModule, AddGroup, Device },
  data() {
    this.deviceTypeList = Object.freeze([
      {
        type: "Camera_QiuJi",
        label: "球机",
        icon: "qiuji",
        color: "#187AE4",
      },
      {
        type: "Camera_QiangJi",
        label: "枪机",
        icon: "shebeizichan",
        color: "#187AE4",
      },
      {
        type: "Camera_Face",
        label: "人脸卡口",
        icon: "renlian1",
        color: "#48BAFF ",
      },
      {
        type: "Camera_Vehicle",
        label: "车辆卡口",
        icon: "qiche",
        color: "#1faf8a",
      },
      { type: "Camera_WIFI", label: "WiFi", icon: "wifi", color: "#914FFF" },
      {
        type: "Camera_Electric",
        label: "电围",
        icon: "ZM-dianwei",
        color: "#614FFF",
      },
      { type: "Camera_RFID", label: "RFID", icon: "RFID", color: "#8173FF " },
    ]);
    return {
      curDeviceType: "Camera_QiuJi", // 当前选中的资源类型
      loading: false,
      list: [], // 数据列表
      showDeviceInfo: false,
      curDeviceName: "设备详情", // 当前选中的设备名称
      isShowModelDetail: false, // 是否展示设备详情弹框的右侧信息
      isopen: false, // Device组件使用，用来判断是不是加载视频组件
    };
  },
  computed: {
    // 当前设备的icon
    icons() {
      return this.deviceTypeList.find((v) => v.type === this.curDeviceType);
    },
    // 当前要展示的设备列表
    deviceList() {
      return (
        this.list.find((v) => v.type === this.curDeviceType)?.devicesList || []
      );
    },
  },
  created() {
    this.getDeviceList();
  },
  methods: {
    /**
     * @description: 获取场所资源列表
     */
    getDeviceList() {
      this.loading = true;
      getDevicesInPlaceAPI(this.$route.query.archiveNo)
        .then((res) => {
          if (res.code == 200 && res.data?.length) {
            this.list = res.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * @description: 获取设备数量
     * @param {string} type 设备类型
     */
    getCount(type) {
      return this.list.find((v) => v.type === type)?.count || 0;
    },

    /**
     * @description: 左右移动
     * @param {boolean} flag false-左，true-右
     */
    arrowTo(flag) {
      let deviceDOM = document.querySelector(".device-type-wrapper");
      deviceDOM.scrollTo({
        left: flag ? deviceDOM.scrollWidth - deviceDOM.offsetWidth : 0,
        behavior: "smooth",
      });
    },

    /**
     * @description: 切换资源类型
     * @param {string} type 资源类型
     */
    changeResourceType(type) {
      if (type === this.curDeviceType) return;
      this.curDeviceType = type;
    },

    /**
     * @description: 设备离线状态
     * @param {object} item 设备信息
     */
    deviceIsOffline(item) {
      // 0在线、1离线、9其他
      let status = "";
      if (
        ["Camera_WIFI", "Camera_RFID", "Camera_Electric"].includes(
          this.curDeviceType
        )
      ) {
        // 感知设备使用onlineStatus判断在线状态
        status = item.onlineStatus;
      } else {
        // 视图设备使用isOnline判断在线状态
        status = item.isOnline;
      }
      return status == "0" ? false : true;
    },

    /**
     * @description: 补充信息，用于对设备信息做转化
     * @param {object} info 原始设备信息
     */
    supplementInfo(info) {
      let item = JSON.parse(JSON.stringify(info));
      item.deviceType = this.getDeviceType(this.curDeviceType).deviceType;
      item.deviceChildType = this.getDeviceType(
        this.curDeviceType
      ).deviceChildType;
      item.source = "place_archive";
      item.deviceGbId = item.deviceId;
      return item;
    },

    /**
     * @description: 查看详情
     * @param {object} item 设备信息
     */
    viewDetail(item) {
      if (item.deviceId) {
        if (!item.latitude || !item.longitude) {
          this.$Message.info("该设备没有点位坐标信息！");
        } else {
          this.curDeviceName = item.deviceName;
          this.showDeviceInfo = true;
          this.isopen = true;
          this.$nextTick(() => {
            let info = {
              ...this.supplementInfo(item),
              lon: item.longitude,
              lat: item.latitude,
            };
            this.$refs.deviceRef.init(info, info, "", false, true);
          });
        }
      }
    },

    /**
     * @description: 关闭弹窗
     */
    closeDeviceInfoModal() {
      this.$refs.deviceRef.stopVideo();
    },

    /**
     * @description: 地图定位
     * @param {object} item 设备信息
     */
    locationOnMap(item) {
      const { href } = this.$router.resolve({
        name: "map-default-page",
        query: { ...this.supplementInfo(item) },
      });
      window.open(href, "_blank");
    },

    getDeviceType(val) {
      let deviceType = "";
      let deviceChildType = "";
      if (val == "Camera_QiangJi") {
        // 枪机
        deviceType = "1";
      } else if (val == "Camera_QiuJi") {
        // 球机
        deviceType = "1";
        deviceChildType = "1";
      } else if (val == "Camera_Vehicle") {
        // 车辆卡口
        deviceType = "2";
      } else if (val == "Camera_WIFI") {
        // wifi
        deviceType = "3";
      } else if (val == "Camera_Electric") {
        // 电子围栏
        deviceType = "4";
      } else if (val == "Camera_RFID") {
        // rfid
        deviceType = "5";
      } else if (val == "Camera_Face") {
        // 人脸抓拍机
        deviceType = "11";
      }
      return { deviceType, deviceChildType };
    },
    /**
     * @description: 查看设备档案
     * @param {object} item 设备信息
     */
    viewArchive(item) {
      let sbgnlxType = item.sbgnlx.split("/");
      const { href } = this.$router.resolve({
        name: "device-dashboard",
        query: {
          archiveNo: item.deviceId,
          type: sbgnlxType.find((v) => v != "1"),
        },
      });
      window.open(href, "_blank");
    },

    /**
     * @description: 加入分组
     * @param {string} deviceId 设备id
     */
    joinGroup(deviceId) {
      this.$refs.addGroupRef.show(deviceId);
    },

    /**
     * @description: 查看目录
     * @param {string} deviceId 设备id
     */
    viewCatalog(deviceId) {
      getTreeAncestors(deviceId).then((res) => {
        if (res.code == 200) {
          let list = res.data.data.deviceOrgList;
          let text = list.map((v) => v.orgName).join("/");
          this.$Message.info({ content: text, duration: 5 });
        } else {
          this.$Message.error(res.msg);
        }
      });
    },

    /**
     * @description: 复制名称
     * @param {string} name 设备名称
     */
    copyName(name) {
      copyText(name);
    },
  },
};
</script>

<style lang="less" scoped>
.card-container {
  --active-color: #2c86f8;
  --active-bg: rgba(44, 134, 248, 0.1);
  /deep/ .module-content {
    padding: 0 0 20px !important;
  }
  .info-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    .device-parse-wrapper {
      padding: 10px 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .device-type-wrapper {
        flex: 1;
        display: inline-flex;
        overflow: hidden;
        & > div {
          border: 1px solid #d3d7de;
          cursor: pointer;
          min-width: fit-content;
        }
        & > div + div {
          border-left: 0;
        }
      }

      .active {
        background: var(--active-bg);
        border: 1px solid var(--active-color) !important;
        span {
          color: var(--active-color) !important;
        }
      }

      .device-type {
        color: rgba(0, 0, 0, 0.45);
      }
      .device-count {
        margin-left: 4px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: bold;
      }
    }
    .device-list-wrapper {
      flex: 1;
      padding-left: 20px;
      overflow-y: auto;
      position: relative; // 供【无数据】组件定位使用

      .device-item {
        display: flex;
        justify-content: space-between;
        &:hover {
          background: var(--active-bg);
          // 鼠标划入，显示.more-operate
          .more-operate {
            display: block;
          }
        }
        .device-label {
          span {
            margin-left: 6px;
            color: rgba(0, 0, 0, 0.8);
          }
        }
        .offline {
          i {
            color: #888 !important;
          }
        }
        .more-operate {
          display: none;
          .more-icon {
            display: inline-block;
            color: var(--active-color) !important;
            transform: rotate(90deg);
          }
        }
      }
    }

    .common {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      padding: 5px 10px;
    }
  }
}
</style>

<style lang="less">
.device-info-modal {
  .ivu-modal-body {
    padding: 0 !important;
  }
  .modalHeader {
    color: #fff;
    font-weight: 700;
    font-size: 14px;
    /* display: flex;
    justify-content: space-between;
    padding-right: 30px; */
  }
  .ivu-modal-close {
    top: 5px;
    .ivu-icon-ios-close {
      color: #fff;
    }
  }
  .ivu-modal-header {
    background: rgb(44, 134, 248);
    border-bottom: none;
    padding: 10px;
    .ivu-modal-header-inner {
      font-size: 14px;
      font-weight: 700;
      color: #fff;
    }
  }
}
.more-list {
  z-index: 999 !important;
  min-width: fit-content !important;
  padding: 0 !important;
  /deep/ .el-popper .popper__arrow::after {
    display: none !important;
  }
  li {
    padding: 6px 10px;
    cursor: pointer;
    width: 100px;
    text-align: center;
    &:hover {
      background: rgba(44, 134, 248, 0.2);
    }
  }
}
</style>
