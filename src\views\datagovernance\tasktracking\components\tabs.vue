<template>
  <div class="tabs">
    <Tabs size="small" v-model="active" @on-click="activeClick">
      <TabPane v-for="(item, index) in list" :label="item" :key="index"></TabPane>
    </Tabs>
  </div>
</template>
<script>
export default {
  name: 'tabs',
  props: {
    list: {
      type: Array,
      default: [],
    },
    value: {
      type: [String, Number],
      default: '',
    },
  },
  data() {
    return {
      active: '',
    };
  },
  created() {},
  methods: {
    activeClick(val) {
      this.$emit('tagChange', val);
    },
  },
  watch: {
    active(val) {
      this.$emit('input', val);
    },
    value: {
      handler(val) {
        this.active = val;
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.tabs {
  /deep/ .ivu-tabs-bar {
    margin-bottom: 12px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 1px;
  }
  /deep/ .ivu-tabs-tab {
    font-size: 14px;
    // width: 86px;
    height: 30px;
    // line-height: 30px;
    text-align: center;
    padding: 6px 22px;
    color: #56789c;
    background: #022143;
    // font-weight: bold;
  }
  /deep/ .ivu-tabs-ink-bar {
    background: var(--color-primary);
  }
  /deep/ .ivu-tabs-tab-active {
    background: #0d3560;
    color: #2b84e2;
  }
}
</style>
