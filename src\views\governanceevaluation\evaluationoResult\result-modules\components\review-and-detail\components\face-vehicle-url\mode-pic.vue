<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" :footer-hide="viewOnly">
    <div class="image-wrapper" v-ui-loading="{ loading: deviceListLoading }">
      <div class="image-wrapper-left mr-md">
        <div class="image-carousel-wrapper">
          <ui-image :src="activePicItem?.[filedNameMap.bigPicName]" />
          <!-- <image-carousel class="image-carousel" :imgList="carouselList"
            :viewIndex="activePicItemIndex" @changeCarousel="changeCarousel"></image-carousel> -->
          <div class="small-pic" v-if="smallShow && !viewOnly">
            <ui-image :src="activePicItem?.[filedNameMap.smallPicName]" />
          </div>
          <div
            v-if="viewOnly && showPreButton"
            class="carousel-arrow left-arrow pointer flex-column-center"
            :disabled="deviceBtnloading.preBtn"
            @click="preBulk"
          >
            <Icon type="ios-arrow-back" class="f-16" />
          </div>
          <div
            v-if="viewOnly && showNextButton"
            class="carousel-arrow right-arrow pointer flex-column-center"
            :disabled="deviceBtnloading.nextBtn"
            @click="nextBulk"
          >
            <Icon type="ios-arrow-forward" class="f-16" />
          </div>
        </div>
      </div>
      <div class="image-wrapper-right mt-sm">
        <div class="algorithm-table">
          <ui-table
            v-ui-loading="{loading: algorithmTableLoading}"
            class="ui-table"
            :table-columns="algorithmTableColumns"
            :table-data="algorithmTableData"
            :loading="algorithmTableLoading"
            :max-height="300"
          >
          </ui-table>
        </div>
        <div v-if="!viewOnly">
          <p class="title mt-sm">人工复核</p>
          <RadioGroup vertical v-model="isQualify" @on-change="changePicRadio">
            <Radio class="mr-lg" label="1"> 图片可用</Radio>
            <Radio class="mr-lg" label="2"> 图片不可用</Radio>
          </RadioGroup>
        </div>
        <div v-if="!viewOnly" class="cause-error-box" v-show="isQualify === '2'">
          <CheckboxGroup v-model="causeErrorList" @on-change="handleUnQualified">
            <ul class="error-ul">
              <li class="error-ul-li error-ul-li_title">不合格原因</li>
              <li class="error-ul-li" v-for="(item, index) in errorCodeList" :key="index">
                <Checkbox :label="item.value">
                  {{ item.label }}
                </Checkbox>
              </li>
            </ul>
          </CheckboxGroup>
        </div>
        <!-- <div class="textarea-wrapper mt-lg">
          <Input v-model="resultTip" type="textarea" class="desc" placeholder="请输入备注信息" :rows="5"
            :maxlength="180"></Input>
        </div> -->
      </div>
    </div>
    <template slot="footer">
      <Button :loading="deviceBtnloading.preBtn" type="primary" class="plr-30" @click="preBulk" v-show="showPreButton">
        上一张
      </Button>
      <Button class="plr-30" type="primary" @click="artificial" :loading="saveModalLoading">确定复核结果</Button>
      <Button
        :loading="deviceBtnloading.nextBtn"
        type="primary"
        class="plr-30"
        @click="nextBulk"
        v-show="showNextButton"
      >
        下一张
      </Button>
    </template>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import inspectionrecord from '@/config/api/inspectionrecord';
import { getAlgorithmColumns } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/VehicleProperties.js';
export default {
  name: 'modePic',
  props: {
    // 本页所有数据
    tableData: [],
    totalCount: {},
    pageData: {
      default: () => {
        return {
          pageNum: 1,
          pageSize: 20,
        };
      },
    },
    reviewRowData: {}, // 当前的设备
    errorCodeList: {
      // 图片模式错误原因
      default: () => [],
    },
    title: {
      type: String,
      default: '图片人工复核',
    },
    value: {
      type: Boolean,
      default: false,
    },
    // 自定义字段名称 [兼容车辆和人脸字段不一致问题]
    filedNameMap: {
      default: () => {
        // 默认以人脸为主
        return {
          smallPicName: 'facePath', // 小图
          bigPicName: 'scenePath', // 大图
          qualified: 'qualified', // 不合格
          description: 'reason', // 设备备注
          // resultTip: 'resultTip', // 图片备注
          deviceDetailId: 'faceDeviceDetailId', // 单个图片的id
        };
      },
    },
    // 设备列表搜索条件
    searchParames: {
      type: Object,
    },
    // 调用方处理， 主要是 为了保持与原有的新增字段逻辑一直，确保字段没有丢失 , 目前整理 只有两个字段： 显示的不合格原因、 是否合格字段
    customValueField: {
      type: Object,
      default: () => {
        return {
          reason: 'resultTip', //  自定义字段： 取值字段
          qualified: 'qualified',
        };
      },
    },
    // 获取列表的接口 --- 若调用接口不一样，则需要调用方自行处理
    getListApi: {
      type: Function,
    },
    reviewModalType: {
      type: String,
      default: 'review',
    },
  },
  computed: {
    showPreButton() {
      // 是第一页 && 是第一条图片 才不展示
      return this.cacheDeviceList.length && !(this.currentPageNum === 1 && this.activePicItemIndex === 0);
    },
    showNextButton() {
      // 最后一页，并且是最后一条图片才不展示
      return this.cacheDeviceList.length && !this.activePicItem.isLastPage;
    },
    viewOnly() {
      return this.reviewModalType === 'viewOnly';
    },
  },
  data() {
    return {
      saveModalLoading: false,
      smallShow: true,
      visible: false,
      isQualify: '1',
      // resultTip: '',
      activePicItem: {
        [this.filedNameMap.qualified]: '1', // 默认合格
      }, // 当前处理的图片
      activePicItemIndex: 0, // 当前处理图片的index
      causeErrorList: [],
      styles: {
        width: '8rem',
      },
      deviceBtnloading: {
        preBtn: false,
        nextBtn: false,
      },

      // 一页一条数据的请求方案： 需要维护一个  isUnExistTable: Boolean， 代表该数据是否已经不符合当前筛选条件下的数据了  true: 与条件不符合，已不存在   false: 满足条件，存在
      // currentDeviceIndex: 0,
      cacheDeviceList: [], // 只有请求接口才会 缓存起来
      currentPageNum: 1, // 当前页码（基于总数算，对于一页一条数据去请求，该字段其实也代表了在总数据中第几条数据）
      isReview: false, // 是否复核过
      devicePageData: {
        pageSize: 50,
      },
      deviceListLoading: false,
      algorithmTableColumns: [],
      algorithmTableData: [],
      algorithmTableLoading: false,
    };
  },
  methods: {
    async initFn() {
      this.isReview = false;

      // 该条数据 在 外部表格中的索引
      let index = this.tableData.findIndex((item) => item.id === this.reviewRowData.id);
      // 该条数据 在 总数中的索引
      this.currentPageNum = (this.pageData.pageNum - 1) * this.pageData.pageSize + index + 1;

      // 默认查询 该条数据所在页码对应的 devicePageData.pageSize条数据
      this.cacheDeviceList = [];
      let res = await this.getDeviceList();
      this.cacheDeviceList = res.entities;
      this.activePicItemIndex = this.cacheDeviceList.findIndex((item) => item.id === this.reviewRowData.id);

      this.changeCarousel(this.activePicItemIndex);
    },
    handleUnQualified() {},
    changePicRadio() {
      // this.resultTip = ''
      this.causeErrorList = [];
    },
    async preBulk() {
      let { nextBtn } = this.deviceBtnloading;
      if (this.saveModalLoading || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }

      try {
        this.deviceBtnloading.preBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.activePicItemIndex - 1]?.isUnExistTable) {
          this.currentPageNum--;
        }

        if (this.activePicItemIndex === 0) {
          // 已经不存在上一条，则调接口
          let res = await this.getDeviceList();
          let endIndex = res.entities.length - 1;
          // 注意： 要添加到数组前面
          this.cacheDeviceList = [...res.entities, ...this.cacheDeviceList];
          this.activePicItemIndex = endIndex;
        } else {
          this.activePicItemIndex--;
        }
        this.changeCarousel(this.activePicItemIndex);
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.preBtn = false;
      }
    },
    async nextBulk() {
      let { preBtn } = this.deviceBtnloading;
      if (this.saveModalLoading || preBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      try {
        this.deviceBtnloading.nextBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.activePicItemIndex].isUnExistTable) {
          this.currentPageNum++;
        }
        this.activePicItemIndex++;

        if (!this.cacheDeviceList[this.activePicItemIndex]) {
          // 缓存中不存在下一条数据，则调接口
          let res = await this.getDeviceList();
          // 注意： 要添加到数组后面
          this.cacheDeviceList = [...this.cacheDeviceList, ...res.entities];
        }
        this.changeCarousel(this.activePicItemIndex);
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.nextBtn = false;
      }
    },
    changeCarousel(index) {
      try {
        // 如果是第一条数据，就得获取上一页的数据去
        this.activePicItemIndex = index;
        this.activePicItem = this.cacheDeviceList[index];
        // this.resultTip = this.activePicItem[this.filedNameMap.resultTip]
        this.isQualify = this.activePicItem[this.filedNameMap.qualified];
        this.causeErrorList = this.activePicItem.causeError ? this.activePicItem.causeError.split(',') : [];
        this.getAlgorithmTableData();
      } catch (error) {
        console.log(error);
      }
    },
    async artificial() {
      let { preBtn, nextBtn } = this.deviceBtnloading;
      if (preBtn || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      let params = {
        data: {
          id: this.activePicItem.id,
          qualified: this.isQualify,
          // reason: this.resultTip,
          deviceDetailId: this.activePicItem[this.filedNameMap.deviceDetailId],
          deviceId: this.activePicItem.deviceId,
          errorCode: (this.causeErrorList && this.causeErrorList.join(',')) || '',
          type: 'detail',
        },
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      try {
        this.saveModalLoading = true;

        let res = await this.$http.post(evaluationoverview.manualRecheck, params);
        this.$Message.success(res.data.msg);
        this.isReview = true;

        // 复核成功后，需要 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
        this.updateInfo();
      } catch (err) {
        console.log(err);
      } finally {
        this.saveModalLoading = false;
      }
    },
    // 获取设备数据
    async getDeviceList(customParams, isUpdate = false) {
      try {
        if (!isUpdate) {
          this.deviceListLoading = true;
        }
        let res = {};
        let { pageSize } = this.devicePageData;
        // 使用调用方参入的函数
        if (this.getListApi) {
          let params = {
            pageSize: pageSize,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            isUpdate: isUpdate,
            currentRow: { ...this.activePicItem },
          };
          res = await this.getListApi(params);
        } else {
          let { regionCode, orgCode, statisticType, indexId, batchId } = this.$route.query;
          let params = {
            indexId: indexId,
            batchId: batchId,
            displayType: statisticType,
            orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
            customParameters: this.searchParames,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            pageSize: pageSize,
          };
          // 以传入参数为主
          if (customParams) {
            params = { ...params, ...customParams };
          }
          res = await this.$http.post(evaluationoverview.getPolyData, params);
        }

        let { entities, lastPage } = res.data.data;

        if (!isUpdate) {
          // 注意： 如果中间存在 isUnExistTable=true , 那此时查询到的列表数据，存在部分数据已缓存过了 --- 每页一条查询是不会的，但现在支持根据 devicePageData.pageSize可配置
          entities = entities.filter((item) => !this.cacheDeviceList.some((cacheItem) => cacheItem.id === item.id));
        }

        entities = entities.map((item, index) => {
          // 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
          if (this.customValueField) {
            Object.keys(this.customValueField).forEach((key) => {
              item[key] = item[this.customValueField[key]];
            });
          }
          return {
            ...item,
            isLastPage: lastPage && index === entities.length - 1, // 是否为最后一条数据
          };
        });
        res.data.data.entities = entities;

        return res.data.data;
      } catch (error) {
        console.log(error);
        throw new Error();
      } finally {
        this.deviceListLoading = false;
      }
    },
    // 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
    async updateInfo() {
      if (this.searchParames?.outcome && this.searchParames?.outcome !== this.isQualify) {
        // 【检查结果】 刷选条件有值，并且与  当前设备复核结果不同
        this.cacheDeviceList[this.activePicItemIndex].isUnExistTable = true;
        this.activePicItem.isUnExistTable = true;

        // 主要是 下面调接口更新需要时间， 接口完成前 先前端替换， 这样就不会影响到上一条下一条显示数据没有更新问题 ， 且也不会影响到 关闭弹框时所做的判断
        // 其实就是 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
        if (this.customValueField) {
          this.cacheDeviceList[this.activePicItemIndex][this.customValueField.qualified] = this.isQualify;
          this.cacheDeviceList[this.activePicItemIndex].causeError =
            (this.causeErrorList && this.causeErrorList.join(',')) || '';
          this.activePicItem[this.customValueField.qualified] = this.isQualify;
          this.activePicItem.causeError = (this.causeErrorList && this.causeErrorList.join(',')) || '';
        }
      }
      let isLastPage = this.activePicItem.isLastPage;
      let params = {
        customParameters: {
          id: this.activePicItem.id,
        },
        pageNumber: 1,
      };
      let res = await this.getDeviceList(params, true);
      let obj = res.entities[0];
      // 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
      if (this.customValueField) {
        Object.keys(this.customValueField).forEach((key) => {
          obj[key] = obj[this.customValueField[key]];
        });
      }
      let activePicItemIndex = this.cacheDeviceList.findIndex((item) => item.id === obj.id);
      let info = {
        ...this.cacheDeviceList[activePicItemIndex],
        ...obj,
        isLastPage: isLastPage, // 保留之前的状态值
      };
      this.cacheDeviceList[activePicItemIndex] = info;
      if (this.activePicItem.id === obj.id) {
        this.activePicItem = info;
      }
    },
    async getAlgorithmTableData() {
      this.algorithmTableData = [];
      try {
        this.algorithmTableLoading = true;
        let res = await this.$http.get(inspectionrecord.vehicleDetail + this.activePicItem.id);
        let { details, ...rest } = res.data.data || {};
        this.algorithmTableData = [
          { ...rest, algorithmTitle: '原始值' },
          ...details,
          { ...rest, algorithmTitle: '结论' },
        ];
        this.algorithmTableColumns = getAlgorithmColumns({
          indexId: this.activePicItem.indexId,
          configText: this.activePicItem.configText,
        })(this.algorithmTableData);
        console.log(this.algorithmTableColumns, this.activePicItem.configText);
      } catch (e) {
        console.log(e);
      } finally {
        this.algorithmTableLoading = false;
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.initFn();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        let closeIndex = this.currentPageNum - 1;
        // 特殊处理，关闭弹框时，如果是 最后一条数据 且 复核 的情况
        let { isLastPage } = this.activePicItem;
        if (
          isLastPage &&
          this.searchParames?.outcome &&
          this.searchParames?.outcome !== this.activePicItem[this.customValueField.qualified]
        ) {
          closeIndex = closeIndex - 1;
        }
        this.$emit('closeFn', {
          isReview: this.isReview,
          closeIndex: closeIndex >= 0 ? closeIndex : 0,
        });
      }
    },
  },
  components: {
    // ImageCarousel: require('@/components/image-carousel.vue').default,
    // Tag: require('./tag.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.ui-image .ui-image-div img {
  // width: 100%;
}
@{_deep}.ivu-modal-body {
  padding: 0 20px;
}
@{_deep}.image-carousel {
  height: 100%;
  .img-list {
    margin-top: 0 !important;
    height: 100%;
  }
}
.image-wrapper {
  width: 100%;
  height: 680px;
  display: flex;
  padding: 10px;

  .small-pic {
    position: absolute;
    left: 1px;
    top: 1px;
    width: 20%;
  }
  .image-wrapper-left {
    flex: 1;
    .image-carousel-wrapper {
      position: relative;
      height: calc(100% - 58px) !important;
      overflow: hidden;
      margin-top: 10px;
      border: 1px solid var(--devider-line);
      .carousel-arrow {
        border-radius: 50%;
        height: 30px;
        width: 30px;
        background: rgba(7, 27, 57, 0.5);
        color: white;
        z-index: 15;
        &:hover {
          background: rgba(7, 27, 57, 0.8);
        }
      }
      .left-arrow {
        position: absolute;
        top: 50%;
        left: 10px;
      }
      .right-arrow {
        position: absolute;
        top: 50%;
        right: 10px;
      }
    }
  }
  .image-wrapper-right {
    width: 30%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .title {
      color: var(--color-primary);
      font-size: 14px;
      margin-left: 10px;
      margin-bottom: 10px;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        width: 4px;
        height: 15px;
        left: -10px;
        top: 3px;
        background: var(--color-primary);
      }
    }
    .algorithm-table {
      @{_deep} .red {
        color: var(--color-failed);
      }
      @{_deep} .green {
        color: var(--color-success);
      }
    }
    .cause-error-box {
      padding-top: 10px;
      overflow: auto;
      flex: 1;
      .error-ul {
        .error-ul-li {
          margin-bottom: 10px;
        }
        .error-ul-li_title {
          color: var(--color-failed);
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
