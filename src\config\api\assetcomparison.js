const basePre = '/ivdg-asset-app';
export default {
  getComparisonResultList: basePre + '/comparison/comparisonResultList', // 查询对比结果列表
  getComparisonTableHearder: basePre + '/comparison/getComparisonTableHearder', // 查询对比表头
  getComparisonStatistics: basePre + '/comparison/getComparisonStatistics', // 查询对比结果统计
  queryLeftUniqueDeviceList: basePre + '/AssertComparisonUnique/queryLeftUniqueDeviceList', // 查询对比结果统计-独有-左
  queryRightUniqueDeviceList: basePre + '/AssertComparisonUnique/queryRightUniqueDeviceList', // 查询对比结果统计-独有-右边
  updateDeviceInfo: basePre + '/comparison/updateDeviceInfo', //资产比对：保存数据
  statistics: basePre + '/comparison/config/statistics', // 资产比对统计
  listAll: basePre + '/comparison/config/listAll', // 查询资产比对：查询全部字段列表
  update: basePre + '/comparison/config/update/', // 资产比对：字段配置编辑
  exportData: basePre + '/comparison/exportData', // 导出

  getLatestStatistics: basePre + '/assert/compare/statistics/getLatestStatistics', // 获取最新统计
  getHistoryDropDown: basePre + '/assert/compare/statistics/getHistoryDropDown', // 获取对账历史
  getHistory: basePre + '/assert/compare/statistics/getHistory', // 获取对账历史详情
  getDetail: basePre + '/assert/compare/statistics/getDetail', // 获取对账详情
  getConfig: basePre + '/assert/compare/config/getConfig', // 获取配置
  updateConfig: basePre + '/assert/compare/config/updateConfig', // 更新配置
  detailExport: basePre + '/assert/compare/statistics/detailExport', // 导出对账明细
  getStatistics: basePre + '/assert/compare/statistics/getStatistics', // 导出统计详情
};
