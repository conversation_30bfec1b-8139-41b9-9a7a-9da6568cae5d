<template>
  <div class="child-tendency-container">
    <!-- 下级趋势header -->
    <div class="child-tendency-header flex-row">
      <div class="flex-aic f-16 breadcrumb-item">
        <div class="title-color-box"></div>
        <span class="pointer" @click="handleTaskCivil"
          >{{ receivedParams.year }}年{{ receivedParams.taskCivilName || '-' }}成绩趋势&nbsp;&gt;&nbsp;</span
        >
        <span class="active">{{ receivedParams.civilName || '-' }}考核成绩趋势</span>
      </div>
      <CheckboxGroup class="inline ml-lg" v-model="overBtn">
        <Checkbox :label="1" :disabled="modelDisable(1)">统计图</Checkbox>
        <Checkbox :label="2" :disabled="modelDisable(2)">报表</Checkbox>
      </CheckboxGroup>
    </div>
    <div class="devider-line"></div>
    <!-- 下级趋势图表 -->
    <div class="chart-box" v-show="overBtn.includes(1)">
      <child-tendency-chart
        :header-list="childTendencyHeader"
        :body-list="childTendencyBody"
        :query-params="receivedParams"
        :echarts-loading="getChildTendencyLoading"
        :get-child-tendency-params="getChildTendencyParams"
        @echartClick="echartClick"
      ></child-tendency-chart>
    </div>
    <!-- 下级趋势表格 -->
    <div class="table-box" v-show="overBtn.includes(2)">
      <child-tendency-table
        :header-list="childTendencyHeader"
        :body-list="childTendencyBody"
        :table-loading="getChildTendencyLoading"
        @onClickMonth="onClickMonth"
      >
        <!-- 表格中的按钮组 -->
        <template #tableButtons="{ isOpen }">
          <Button
            type="primary"
            v-if="childTendencyBody?.length"
            :loading="exportLoading"
            @click="handleExport(isOpen)"
          >
            <i class="icon-font icon-daochu f-12 mr-sm vt-middle"></i>
            <span class="vt-middle">导出</span>
          </Button>
        </template>
      </child-tendency-table>
    </div>
    <!-- 月考核明细弹窗 -->
    <index-detail ref="indexDetail" @showHkDetail="showHkDetail" @showYsDetail="showYsDetail"></index-detail>
    <hk-detail v-model="hkDetail.isShow" :title="hkDetail.title" :hk-params="hkDetail.params"></hk-detail>
    <ys-detail v-model="ysDetail.isShow" :title="ysDetail.title" :ys-params="ysDetail.params"></ys-detail>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'childTendency',
  mixins: [dealWatch],
  inject: {
    examSchemeType: {
      value: 'examSchemeType',
      default: 1,
    },
  },
  props: {},
  data() {
    return {
      getChildTendencyLoading: false,
      childTendencyHeader: [],
      childTendencyBody: [],
      receivedParams: {},
      overBtn: [1, 2],
      hkDetail: {
        isShow: false,
        title: '',
        params: {},
      },
      ysDetail: {
        isShow: false,
        title: '',
        params: {},
      },
      getChildTendencyParams: {},
      exportLoading: false, //导出loading
    };
  },
  methods: {
    //获取子级趋势图数据
    async getChildTendency() {
      try {
        this.getChildTendencyLoading = true;
        this.childTendencyHeader = [];
        this.childTendencyBody = [];
        this.getChildTendencyParams = {
          year: this.receivedParams.year,
          examTaskId: this.receivedParams.examTaskId,
          orgRegeionCode: this.receivedParams.orgRegeionCode,
          orgCode: this.receivedParams.orgCode,
          examSchemeId: this.receivedParams.examSchemeId,
          queryChild: 1,
        };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getExamChildScoreTrend, this.getChildTendencyParams);
        this.childTendencyHeader = data.headerList;
        this.childTendencyBody = data.bodyList;
      } catch (err) {
        console.log(err);
      } finally {
        this.getChildTendencyLoading = false;
      }
    },
    //接收参数
    receiveParams(params) {
      this.receivedParams = params;
      this.getChildTendency();
    },
    //控制统计图、报表视图
    modelDisable(val) {
      return this.overBtn[0] === val && this.overBtn.length === 1;
    },
    //跳转回上级
    handleTaskCivil() {
      let name = this.examSchemeType === 1 ? 'examinationTendency' : 'examinationTendencyGAB';
      this.$router.push({ name: name });
    },
    //打开月详情
    echartClick(chartsParams) {
      if (chartsParams.componentType !== 'xAxis' || chartsParams.targetType !== 'axisLabel') {
        return;
      }
      let cilckMonth = chartsParams.value?.replace('月', '');
      let tagetObj = this.childTendencyBody.find((item) => item.month == Number(cilckMonth));
      let params = {
        month: tagetObj.month, //月份
        score: tagetObj.score, //总分
      };
      this.viewMonthDetail(params);
    },
    onClickMonth(val) {
      let params = {
        month: val.month, //月份
        score: val.score, //总分
      };
      this.viewMonthDetail(params);
    },
    //下级趋势列表点击月份
    viewMonthDetail(val) {
      // 下列参数依照原考核成绩
      let params = {
        column: {
          code: 'TOTAL_SCORE', //手动设置总分的key
          key: 'ORG_REGEION_CODE', //组织机构
          name: '月',
        },
        row: {
          ORG_REGEION_CODE: this.receivedParams.civilName,
          orgCode: this.receivedParams.orgRegeionCode,
          TOTAL_SCORE: {
            score: val.score,
          },
        },
      };
      // 参数依照原考核成绩
      let searchData = {
        examSchemeId: this.receivedParams.examSchemeId,
        examTaskId: this.receivedParams.examTaskId,
        tableTitleTaskName: this.receivedParams.tableTitleTaskName,
        time: this.receivedParams.year + '-' + (val.month < 10 ? `0${val.month}` : `${val.month}`),
        month: val.month,
        year: this.receivedParams.year,
        orgRegeionCode: this.receivedParams.orgRegeionCode,
        evaluationTaskSchemeId: this.receivedParams.evaluationTaskSchemeId,
        scoreDesc: false,
      };
      this.$refs.indexDetail.init(params, searchData);
    },
    showHkDetail(row) {
      this.hkDetail.title = row.categoryName;
      this.hkDetail.params = row;
      this.hkDetail.isShow = true;
    },
    showYsDetail(row) {
      this.ysDetail.title = row.categoryName;
      this.ysDetail.params = Object.assign(
        {
          schemeId: this.searchData.examSchemeId,
        },
        row,
      );
      this.ysDetail.show = true;
    },
    //导出
    async handleExport(isOpen) {
      try {
        this.exportLoading = true;
        const params = {
          year: this.receivedParams.year,
          examTaskId: this.receivedParams.examTaskId,
          orgRegeionCode: this.receivedParams.orgRegeionCode,
          examSchemeId: this.receivedParams.examSchemeId,
          queryChild: 1,
        };
        if (isOpen) {
          params.extChild = 1; // 导出展开项
        }
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.exportExamChildScoreTrendExcel, params);
        this.$util.common.transformBlob(data, this.receivedParams.civilName + '成绩趋势');
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        if (this.$route.query.componentName === 'childtendency') {
          this.receiveParams(this.$route.query);
        }
      },
      { immediate: true },
    );
  },
  mounted() {},
  components: {
    ChildTendencyChart: require('./ChildTendencyChart.vue').default,
    ChildTendencyTable: require('./ChildTendencyTable.vue').default,
    indexDetail: require('@/views/governanceevaluation/examinationresult/components/index-detail.vue').default,
    HkDetail: require('@/views/governanceevaluation/examinationresult/components/hk-detail.vue').default,
    YsDetail: require('@/views/governanceevaluation/examinationresult/components/ys-detail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.child-tendency-container {
  overflow: auto;
  width: 100%;
  height: 100%;
  background: var(--bg-content);
  padding: 20px;
  .child-tendency-header {
    .title-color-box {
      width: 4px;
      height: 15px;
      background: var(--bg-breadcrumb-head);
      margin-right: 10px;
    }
    .breadcrumb-item {
      color: var(--color-breadcrumb-item);
      .active {
        font-weight: bold;
        color: var(--color-breadcrumb-item-active);
      }
    }
  }
  .chart-box {
    height: 50%;
  }
  .devider-line {
    background: var(--devider-line);
    height: 1px;
    margin: 15px 0 30px 0;
  }
}
</style>
