.area-container {
  .tree-filter {
    margin-bottom: 10px;
  }

  .tree-wrapper {
    height: 500px;
    border: 1px solid var(--border-modal-footer);
    opacity: 1;
    border-radius: 4px;
    overflow: auto;
    .ui-search-tree {
      height: 100%;
      @{_deep} .el-tree-node {
        .is-leaf + .el-checkbox .el-checkbox__inner {
          display: inline-block;
        }
        .el-checkbox .el-checkbox__inner {
          display: none;
        }
      }
    }
  }
  .h500 {
    height: 500px;
  }
  .h560 {
    height: 560px;
  }
}

@{_deep} .ivu-modal-body {
  max-height: 100% !important;
}

.btn-mini {
  height: 24px;
  @{_deep} .ivu-input {
    height: 24px;
    line-height: 24px;
  }
}
