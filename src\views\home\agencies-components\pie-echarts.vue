<template>
  <div class="pie-echarts">
    <div class="columnar-echarts">
      <draw-echarts
        ref="echart1"
        :echartOption="echartOption1"
        @echartClick="echartClick"
        :echart-style="echartStyle"
      ></draw-echarts>
    </div>
    <div class="columnar-echarts">
      <draw-echarts
        ref="echart2"
        :echart-style="echartStyle"
        :echartOption="echartOption2"
        @echartClick="echartClick"
      ></draw-echarts>
    </div>
  </div>
</template>
<script>
import { fontSize } from '@/util/module/common.js';

export default {
  name: 'tasktracking',
  props: {
    columnarPieData: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      echartOption1: {},
      echartOption2: {},
      echartStyle: {
        width: '100%',
        height: '100%',
      },
    };
  },
  mounted() {},
  methods: {
    echartClick(params) {
      let one = this.columnarPieData[params.dataIndex].allMessage;
      this.$emit('echartClick', one);
    },
    // 单个指标
    async init() {
      var color = { 0: ['#0CD084', 'RGBA(12, 123, 105, 0.6)'], 1: ['#CA783F', 'RGBA(107, 78, 70, 0.6)'] };
      this.columnarPieData.map((val, index) => {
        //let myChart = this.$echarts.init(this.$refs['echart' + (index + 1)])
        var option = {
          title: [
            {
              text: val.value + '%',
              x: '50%',
              y: '46%',
              textAlign: 'center',
              textStyle: {
                fontSize: fontSize(18),
                fontWeight: 'bold',
                color: '#fff',
                textAlign: 'center',
              },
            },
            {
              text: val.name,
              left: '50%',
              top: '80%',
              textAlign: 'center',
              textStyle: {
                fontSize: fontSize(16),
                fontWeight: '100',
                color: '#fff',
                textAlign: 'center',
              },
            },
          ],
          polar: {
            radius: [64, 72],
            center: ['50%', '50%'],
          },
          angleAxis: {
            max: 100,
            show: false,
          },
          radiusAxis: {
            type: 'category',
            show: true,
            axisLabel: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
          series: [
            {
              name: '',
              type: 'bar',
              roundCap: true,
              barWidth: this.$util.common.fontSize(80),
              showBackground: true,
              backgroundStyle: {
                color: color[index][1],
              },
              data: [val.value],
              coordinateSystem: 'polar',
              itemStyle: {
                color: color[index][0],
              },
            },
          ],
        };
        this[`echartOption${index + 1}`] = option;
        //myChart.setOption(option, true)
      });
    },
    // 全部指标
    // initAll() {
    //   myChart.setOption(option, true)
    // },
    // resizeFn() {
    //   this.init()
    // },
  },
  watch: {
    columnarPieData: {
      handler() {
        this.init();
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.pie-echarts {
  display: flex;
  flex-wrap: nowrap;
}
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
.columnar-echarts {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
