// 引入mockjs
import Mock from 'mockjs';

// 身份证查询
Mock.mock('/search/IDnumber', 'post', {
    'code': 200,
    'msg': '成功',
    'data':{
        'total': 10,
        'entities': [
            {
                'id|+1': 1,
                'archiveNo': "371002200108212050",
                'xm': "钱组上",
                'gmsfhm' : '320102198703071905',
                'photo': require('@/assets/img/mock/face.png'),
            },
        ]
    }
})
// 车辆 
Mock.mock('/search/vehicle', 'post', {
    'code': 200,
    'msg': '成功',
    'data':{
        'total': 10,
        'entities': [
            {
                'id|+1': 1,
                'idcardNo': "371002200108212050",
                'ownerName': "张伟",
                'plateNo' : '苏A 34567',
                'gmsfhm' : '******************',
                'photoUrl': require('@/assets/img/mock/vehicle.png'),
            },
        ]
    }
})
// 落脚点点位
// 人脸
Mock.mock('/search/faceList', 'post', {
    'code': 200,
    'msg': '成功',
    'data':{
        'total': 10,
        'entities': [
            {
                'id|+1': 1,
                'archiveNo': "371002200108212050",
                'xm': "钱组上",
                'gmsfhm' : '',
                'geoPoint': {lat: 31.970031425117856, lon: 118.74651236859219},
                'sceneImg': require('@/assets/img/mock/face.png'),
                'traitImg': require('@/assets/img/mock/face.png'),
                'ageName': '青年',
                'eyeglass': '',
                'isCap': '',
                'mask': '',
                'absTime': '',
                'deviceName': '',
                'deviceId': '12345',
                'id': 'q123'
            },
            {
                'id|+1': 2,
                'archiveNo': "150825200208191257",
                'xm': "陶昊名",
                'gmsfhm' : '',
                'geoPoint': {lat: 31.975021, lon: 118.758414},
                'sceneImg': require('@/assets/img/mock/face.png'),
                'traitImg': require('@/assets/img/mock/face.png'),
                'ageName': '青年',
                'eyeglass': '',
                'isCap': '',
                'mask': '',
                'absTime': '',
                'deviceName': '',
                'deviceId': '34567',
                'id': 'q234'
            },
        ]
    }
})
// 车辆
Mock.mock('/search/vehicleList', 'post', {
    'code': 200,
    'msg': '成功',
    'data':{
        'total': 10,
        'entities': [
            {
                'id|+1': 1,
                'archiveNo': "371002200108212050",
                'xm': "钱组上",
                'gmsfhm' : '',
                'geoPoint': {lat: 31.970031425117856, lon: 118.74651236859219},
                'sceneImg': require('@/assets/img/mock/vehicle.png'),
                'traitImg': require('@/assets/img/mock/vehicle.png'),
                'vehicleSpeed': '100km/h',
                'absTime': '',
                'moveDirection': '',
                'deviceName': '',
                'deviceId': '12345',
                'id': 'q123'
            },
            {
                'id|+1': 2,
                'archiveNo': "150825200208191257",
                'xm': "陶昊名",
                'gmsfhm' : '',
                'geoPoint': {lat: 31.975021, lon: 118.758414},
                'sceneImg': require('@/assets/img/mock/vehicle.png'),
                'traitImg': require('@/assets/img/mock/vehicle.png'),
                'vehicleSpeed': '100km/h',
                'absTime': '',
                'moveDirection': '',
                'deviceName': '',
                'deviceId': '34567',
                'id': 'q234'
            },
        ]
    }
})
