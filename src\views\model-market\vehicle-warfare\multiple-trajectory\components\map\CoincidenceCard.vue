<template>
    <div class="coincidence-card">
        <header class="header">
            <i class="iconfont icon-aim" style="color:#2d87f9"/>
            <span class="position-name">{{record.deviceName}}</span>
            <span class="times"><span class="number">{{record.count}}</span>次</span>
        </header>
        <article class="avatar-block">
            <img :src="item.sceneImg" class="avatar-icon" v-for="(item,index) in list" :key="index" @click="showDetail(item, index)"/>
        </article>
    </div>
</template>
<script>

export default {
    name: 'coincidenceCard',
    props:["record"],
    data() {
        return {
        //    list:this.record.list.slice(0,5),
        }
    },
    computed: {
        list() {
            return this.record.records.slice(0,5)
        }
    },
    methods:{
        showDetail(item, index){
            $pubsub.publish("show-lappoints-detail",index,item,this.list)
        }
    },
}
</script>
<style lang="less", scoped>
.coincidence-card {
    padding-bottom: 10px;
    border-bottom: 1px solid #999999;

    .header {
        display: flex;
        height: 30px;
        align-items: center;
        // border: 1px solid red;

        .device-icon {
        width: 20px;
        height: 20px;
        border: 1px solid lightgrey;
        }

        .position-name {
            margin-left: 5px;
            width: calc(~'100% - 55px');
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .times {
            position: absolute;
            right: 10px;
            color: #999999;
            .number {
                color: #2d87f9;
            }
        }
    }

    .avatar-block {
        padding: 10px 0 0 25px;

        .avatar-icon {
            width: 50px;
            height:50px;
            border: 1px solid lightgrey;
            margin-right: 5px;
            cursor: pointer;
        }
    }
}
</style>