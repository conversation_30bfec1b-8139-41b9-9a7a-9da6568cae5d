<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card">
    <div>
      <div @click="viewBigPic(list.scenePath)">
        <ui-image :src="list.facePath" class="ui-image-card" />
      </div>

      <div class="ui-gather-card-image-item ellipsis" v-for="(item, index) in cardInfo" :key="index">
        <p v-if="!item.type" class="ellipsis">
          <i
            v-if="item.icon"
            :class="item.icon"
            class="icon-font base-text-color vt-middle mr-xs ui-gather-card-right-item-label"
          ></i>
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            class="ui-gather-card-right-item-value"
            :style="{ color: item.color ? item.color : '#ffffff' }"
            :title="list[item.value] || '暂无'"
            >{{ list[item.value] || '暂无' }}</span
          >
        </p>
        <p v-else-if="item.type === 'lazy'" class="ellipsis">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span class="ui-gather-card-right-item-value" :style="{ color: item.color ? item.color : '#ffffff' }">{{
            $util.common.timeFn(list[item.value] || 0) || '暂无'
          }}</span>
        </p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      imgList: [],
      bigPictureShow: false,
    };
  },
  computed: {},
  created() {},
  methods: {
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
  },
  watch: {},
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  height: max-content;
  margin-bottom: 10px;
  padding: 10px;
  background: #0f2f59;
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
      .wrapper {
        width: 100%;
      }
    }
  }
}
.ui-gather-card-image-item {
  font-size: 14px;
  margin: 4px 0 4px 0;
}
.ui-image-card {
  height: 150px;
  cursor: pointer;
}
</style>
