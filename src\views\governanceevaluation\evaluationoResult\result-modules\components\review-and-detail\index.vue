<template>
  <component :is="componentName" v-bind="attrs" v-on="$listeners"></component>
</template>

<script>
export default {
  name: 'osdDetail',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      componentName: null,
      reviewDetailIndex: Object.freeze({
        // 人脸
        FACE_URL_AVAILABLE: 'FaceVehicleUrlAlgorithm',
        FACE_SMALL_URL_AVAILABLE: 'FaceSmallUrl',
        FACE_FOCUS_URL_AVAILABLE: 'FaceVehicleUrlAlgorithm',
        // 车辆
        VEHICLE_URL_AVAILABLE: 'FaceVehicleUrlAlgorithm',
        VEHICLE_URL_AVAILABLE_IMPORTANT: 'FaceVehicleUrlAlgorithm',
        VEHICLE_INFO_PASS_IMPORTANT: 'FaceVehicleUrl',
        VEHICLE_INFO_PASS: 'FaceVehicleUrl',
        VEHICLE_MAIN_PROP: 'FaceVehicleUrl',
        VEHICLE_TYPE_PROP: 'FaceVehicleUrl',
        // 视频流
        VIDEO_GENERAL_CLOCK_ACCURACY: 'VideoClock',
        VIDEO_CLOCK_ACCURACY: 'VideoClock',
        VIDEO_CLOCK_ACCURACY_PROMOTION_RATE: 'VideoClock',
        VIDEO_GENERAL_OSD_ACCURACY: 'VideoAlgorithm',
        VIDEO_OSD_ACCURACY: 'VideoAlgorithm',
        VIDEO_OSD_ACCURACY_PROMOTION_RATE: 'VideoOsd',
        VIDEO_GENERAL_OSD_CLOCK_ACCURACY: 'VideoAlgorithm',
        VIDEO_OSD_CLOCK_ACCURACY: 'VideoAlgorithm',
        VIDEO_QUALITY_PASS_RATE: 'VideoQualityPass',
        VIDEO_QUALITY_PASS_RATE_RECHECK: 'VideoQualityPass',
        VIDEO_GENERAL_PLAYING_ACCURACY: 'VideoPlayingAccuracy',
        VIDEO_PLAYING_ACCURACY: 'VideoPlayingAccuracy',
        VIDEO_READ_PROMOTION_RATE: 'BaseReview',
        VIDEO_GENERAL_HISTORY_ACCURACY: 'VideoPlayingAccuracy',
        VIDEO_HISTORY_ACCURACY: 'VideoPlayingAccuracy',
        VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN: 'VideoPlayingAccuracy',
      }),
      visible: false,
    };
  },
  methods: {
    getAttrs() {
      return {
        value: this.value,
        ...this.$attrs,
      };
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      this.componentName = this.reviewDetailIndex[this.activeIndexItem.indexType];
    },
  },
  computed: {
    attrs() {
      return this.getAttrs();
    },
  },
  components: {
    BaseReview: require('./components/base-review').default,
    VehicleUrl: require('./components/vehicle-url').default,
    VideoClock: require('./components/video-clock').default,
    FaceUrl: require('./components/face-url.vue').default,
    VideoQualityPass: require('./components/video-quality-pass').default,
    VideoOsd: require('./components/video-osd').default,
    VideoPlayingAccuracy: require('./components/video-playing-accuracy').default,
    VideoOsdClock: require('./components/video-osd-clock').default,
    FaceVehicleUrl: require('./components/face-vehicle-url/index.vue').default,
    FaceVehicleUrlAlgorithm: require('./components/face-vehicle-url-algorithm/index.vue').default,
    FaceSmallUrl: require('./components/face-small-url/index.vue').default,
    VideoAlgorithm: require('./components/video-algorithm/index.vue').default,
  },
};
</script>

<style lang="less" scoped></style>
