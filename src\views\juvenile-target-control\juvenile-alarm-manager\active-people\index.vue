<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 10:43:20
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-alarm-manager\active-people\index.vue
-->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="active"
      :compareType="compareType"
      :radioList="radioList"
      :taskList="taskList"
    >
      <query ref="slotQuery" />
    </searchForm>

    <!-- 列表 -->
    <div class="list">
      <ui-table
        class="table-box"
        ref="tableRef"
        :columns="columns"
        :data="tableList"
        :loading="tableLoading"
      >
        <template #photoUrl="{ row }">
          <div
            class="table-image"
            style="width: 65px; height: 80px; margin: auto"
          >
            <ui-image viewer type="people" :src="row.photoUrl" />
          </div>
        </template>
        <template #idCardNo="{ row }">
          <span class="primary click-point" @click="goArchivesInfo(row)">{{
            row.idCardNo
          }}</span>
        </template>
        <template #activeScore="{ row }">
          <span class="primary">{{ row.activeScore }}</span>
        </template>
        <template #cardType="{ row }">
          <div>{{ row.cardType | commonFiltering(identityTypeList) }}</div>
        </template>
        <template #nation="{ row }">
          <div>{{ row.nation | commonFiltering(nationTypeList) }}</div>
        </template>
        <template #record="{ row }">
          <div v-if="row.bizLabels">
            <span class="warning"> {{ row.bizLabels.join(",") || "--" }}</span>
          </div>
          <span v-else class="warning">--</span>
        </template>
        <template #detail="{ row }">
          <div class="detail-info">
            <p>
              前科人员发现报警：<span class="primary">{{
                row.compareAlarmCount
              }}</span>
            </p>
            <p>
              频繁出入娱乐场所：<span class="primary">{{
                row.appearInRecreationCount
              }}</span>
            </p>
            <p>
              上学时间校外出现：<span class="primary">{{
                row.classTimeOutsideCount
              }}</span>
            </p>
            <p>
              深夜时间出行：<span class="primary">{{
                row.appearInNightCount
              }}</span>
            </p>
            <p>
              与违法前科人同行：<span class="primary">{{
                row.alongCriminalCount
              }}</span>
            </p>
          </div>
        </template>
      </ui-table>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import searchForm from "../../components/search-form.vue";
import query from "./components/query.vue";
import { getOverviewPageList } from "@/api/monographic/juvenile.js";
export default {
  name: "",
  components: { searchForm, query },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 2, value: "前科人员" },
        { key: 1, value: "非前科人员" },
      ],
    },
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.query();
  },
  data() {
    return {
      tableList: [],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "人员照片", slot: "photoUrl", align: "center" },
        { title: "姓名", key: "name", align: "center", width: 80 },
        { title: "年龄", key: "age", align: "center", width: 80 },
        { title: "证件类型", slot: "cardType", align: "center" },
        { title: "身份证号", slot: "idCardNo", align: "center" },
        { title: "民族", slot: "nation", align: "center" },
        { title: "前科类型", slot: "record", align: "center" },
        { title: "活跃度", slot: "activeScore", align: "center", width: 100 },
        { title: "最后一次活跃时间", key: "lastActiveTime", align: "center" },
        { title: "活跃详情", slot: "detail", align: "left", width: 230 },
      ],
      tableLoading: false,
      taskList: [],
    };
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 获取查询参数，并进行调整
     */
    getQueryParams() {
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
      };
      data = {
        ...data,
        libBizType: data.operationType == "" ? 0 : data.operationType,
      };
      delete data.operationType;
      return data;
    },
    /**
     * @description: 获取报警列表
     */
    tableListFn(param = {}) {
      this.tableLoading = true;
      let data = this.getQueryParams();
      getOverviewPageList({ ...data, ...param, ...this.params })
        .then((res) => {
          this.total = res.data?.total;
          this.tableList = res.data?.entities || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @description: 手动触发查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.$refs.slotQuery.reset();
      this.query();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },
    // 跳转档案
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        name: "juvenile-archive",
        query: {
          archiveNo: item.idCardNo,
          source: "people",
          initialArchiveNo: item.idCardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.primary {
  color: #2c86f8;
}
.warning {
  color: #ea4a36;
}
.click-point {
  cursor: pointer;
}
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .list {
    flex: 1;
    padding-top: 16px;
    overflow: scroll;
    .table-box {
      height: 100%;
    }
  }
}
</style>
