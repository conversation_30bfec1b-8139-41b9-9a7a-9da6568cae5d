<!--
 * @Date: 2025-03-07 16:43:35
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-13 10:56:54
 * @FilePath: \icbd-view\src\views\multimodal-analysis\multimodal-analysis-lib\components\muti-search.vue
-->
<template>
  <div class="search-content" style="min-width: 1200px" v-show="!noSearch">
    <div class="search-top">
      <div class="top-left">
        <Input
          v-model="keyWords"
          :placeholder="placeholder"
          :maxlength="50"
          class="search-input"
        >
        </Input>
      </div>
      <div class="top-right">
        <ui-icon type="mai_line" :size="20" @click.native="openAudio"></ui-icon>
        <Button
          type="primary"
          class="search-btn"
          slot="append"
          @click="searchHandler"
        >
          搜索
        </Button>
      </div>
    </div>
    <div class="search-bottom">
      <div class="bottom-left">
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          @change="changeDateType"
        />
      </div>
      <div class="bottom-right">
        <Slider v-model="queryForm.similarity"></Slider>
        <div>{{ queryForm.similarity }}%</div>
        <!-- <div class="select-box">
          <Select v-model="queryForm.searchType" style="width: 150px">
            <Option
              v-for="item in searchTypeList"
              :value="item.value"
              :key="item.value"
              placeholder="请选择检索类型"
              >{{ item.label }}</Option
            >
          </Select>
        </div> -->
        <div>
          <div class="select-detail">
            <div class="select-tag-button" @click="selectDevice()">
              选择设备（{{ selectDeviceList.length }}）
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 选择设备 -->
    <ys-select-device
      ref="selectDevice"
      showOrganization
      @selectData="selectData"
    />
    <UiAudio ref="audioRef" @confirm="(val) => (keyWords = val)"></UiAudio>
  </div>
</template>

<script>
import UiAudio from "@/components/ui-audio/index.vue";
import ysSelectDevice from "./ys-select-device.vue";
export default {
  name: "mutiSearch",
  components: {
    UiAudio,
    ysSelectDevice
  },
  data() {
    return {
      keyWords: "",
      queryForm: {
        startTime: "",
        endTime: "",
        similarity: 65,
      },
      dateType:1,
      placeholder:"您可以输入任何您要搜索的内容，如：穿着蓝色衣服背黑包的人",
      noSearch: false,
      selectDeviceList: [],
    };
  },
  mounted() {
    this.$refs.quickDateRef.handleInit(this.dateType);
  },
  methods: {
    openAudio() {
      this.$refs.audioRef.show();
    },
    searchHandler() {
      if (!this.keyWords) {
        return this.$message.warning("请输入检索内容");
      }
      let param = {
        ...this.queryForm,
        keywords: this.keyWords,
        deviceIds:this.selectDeviceList.map((el) => el.deviceId),
      };
      this.$emit("searchHander", param);
    },
    /**
     * @description: 切换日期
     * @param {object} value 当前选中的时间区间
     */
    changeDateType(value) {
      this.queryForm.startTime = value.startDate;
      this.queryForm.endTime = value.endDate;
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    selectData(list) {
      this.selectDeviceList = list;
    },
  },
};
</script>

<style lang="less" scoped>
.search-content {
  width: 1300px;
  height: 110px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #cdd5e0;
  padding: 12px 20px;
  .search-top {
    display: flex;
    justify-content: space-between;
    height: 50px;
    width: 100%;
    border-bottom: 1px solid #d8d8d8;
    padding-bottom: 10px;
    .top-left {
      display: flex;
      flex: 1;
      .upload-img-box {
        width: 50px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        img {
          max-width: 100%;
          max-height: 100%;
        }
        .delete-box {
          position: absolute;
          z-index: 100;
          background: rgba(0, 0, 0, 0.5);
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: none;
        }
        &:hover .delete-box {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .search-input {
        /deep/ .ivu-input {
          border: none;
          padding: 0;
        }
        input {
          border: none;
        }
      }
    }
    .top-right {
      width: 168px;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      align-items: center;
      button {
        width: 100px;
        height: 36px;
        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
        border-radius: 4px 4px 4px 4px;
      }
    }
  }
  .search-bottom {
    padding-top: 6px;
    display: flex;
    justify-content: space-between;
    .bottom-right {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .select-detail {
        display: flex;
        justify-content: space-between;
        .select-tag-button {
          width: 150px;
          margin-left: 10px;
        }
        .select-tag-taskId {
          width: 150px;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
