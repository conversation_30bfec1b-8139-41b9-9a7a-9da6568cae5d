<template>
  <barEchart :title="{}" :legend="legend" :grid="grid" :xAxis="xAxis" :yAxis="yAxis" :series="series"></barEchart>
</template>
<script>
import * as echarts from 'echarts'
import barEchart from '@/components/echarts/bar-echart'

export default {
  components: { barEchart },
  props: {},
  data() {
    return {
      legend: {
        show: false
      },
      grid: {
        left: '0',
        top: '5%',
        right: '0.1%',
        bottom: '0',
        containLabel: true
      },
      xAxis: {
        data: ['在逃人员库', '吸毒人员库', '重点人员库', '涉黄人员库', '涉军人员库'],
        axisLine: {
          lineStyle: {
            color: '#D3D7DE'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.35)'
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: 'dashed',
            color: '#D3D7DE'
          }
        }
      },
      yAxis: {
        axisLabel: {
          color: 'rgba(0, 0, 0, 0.35)'
        },
        splitLine: {
          lineStyle: {
            type: 'solid',
            color: '#D3D7DE'
          }
        }
      },
      series: [
        {
          name: '白天',
          type: 'bar',
          stack: 'one',
          data: [100, 232, 600, 400, 460, 300, 200],
          barWidth: '20',
          itemStyle: {
            normal: {
              barBorderRadius: [15, 15, 15, 15],
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1, //渐变色在下面修改，这里是透明度
                [
                  {
                    offset: 0,
                    color: '#5BA3FF'
                  },
                  {
                    offset: 1,
                    color: '#2C86F8'
                  }
                ]
              )
            }
          }
        }
      ]
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="less" scoped></style>
