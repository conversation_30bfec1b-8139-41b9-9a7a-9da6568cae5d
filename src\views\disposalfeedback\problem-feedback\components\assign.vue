<template>
  <div :class="['assign', isBatch ? '' : '']">
    <RadioGroup v-model="assignMode" @on-change="onChangeAssignMode">
      <Radio :label="0">自定义指派</Radio>
      <Radio :label="1">自动指派</Radio>
    </RadioGroup>
    <ui-label v-if="assignMode === 0" label="" class="block">
      <div class="receive-camera mt-sm pointer ml-67" v-if="assignMode === 0" @click="selectPeople">
        <span class="font-blue" v-if="!searchForm.people">请选择任务接收人</span>
        <span v-else class="font-blue">{{ searchForm.people.name }}</span>
      </div>
    </ui-label>
    <ui-label v-if="assignMode === 1" label="" class="block">
      <Select class="width-md mt-sm ml-67" v-model="searchForm.assignMode" placeholder="请选择指派规则">
        <Option label="自动指派维护单位人" :value="1"> </Option>
        <Option label="自动指派组织机构人" :value="2"> </Option>
      </Select>
      <Button type="text" class="ml-sm" v-if="searchForm.assignMode === 2" @click="onClickAutoAssignOrg"
        >配置各单位工单接收人</Button
      >
    </ui-label>
    <select-people ref="selectpeople" v-model="peopleShow" @putPeople="putPeople"></select-people>
    <select-receiver
      ref="selectpeopleConfig"
      v-model="selectPeopleConfigVisible"
      @on-select-receiver="OnSelectReceiver"
    >
    </select-receiver>
  </div>
</template>
<script>
export default {
  props: {
    isBatch: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      peopleShow: false,
      selectPeopleConfigVisible: false,
      assignMode: 0,
      searchForm: {
        people: null,
        assignMode: 0,
        assignList: [],
      },
    };
  },
  updated() {
    this.$emit('updateDisabled', this.searchForm);
  },
  methods: {
    OnSelectReceiver(val) {
      this.searchForm.assignList = val.map((item) => {
        return {
          assignId: item.username,
          assignName: item.name,
          orgCode: item.orgCode,
          orgName: item.orgName,
          orgId: item.orgId,
        };
      });
    },
    onChangeAssignMode(val) {
      // 后端不存 自动指派
      if (val === 0) {
        this.searchForm.assignMode = 0;
      } else {
        this.searchForm.assignMode = 1;
      }
    },
    onClickAutoAssignOrg() {
      this.selectPeopleConfigVisible = true;
    },
    selectPeople() {
      this.peopleShow = true;
    },
    putPeople(item) {
      this.searchForm.people = item;
      this.peopleShow = false;
      this.$emit('updateDisabled', item);
    },
    reset() {
      this.searchForm.people = null;
      this.peopleShow = false;
      this.assignMode = 0;
      this.searchForm = {
        assignMode: 0,
        assignList: [],
      };
      this.$refs.selectpeople.reset();
    },
  },
  watch: {},
  components: {
    SelectPeople: require('@/api-components/select-people/select-people.vue').default,
    selectReceiver: require('@/views/disposalfeedback/governanceorder/components/select-receiver.vue').default,
  },
};
</script>
<style lang="less" scoped>
.assign {
  &-label {
    width: 65px;
    text-align: left;
    color: #ffffff;
  }
}

.single {
  display: flex;
  justify-content: center;
  padding: 50px 0;
}
.receive-camera {
  background: rgba(43, 132, 226, 0.1);
  width: 230px;
  height: 34px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dotted #037cbe;
}
.ml-67 {
  margin-left: 67px;
}
</style>
