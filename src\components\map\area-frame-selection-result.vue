<template>
    <div class="select-result-dom">
        <section class="dom-content"
            v-if="!!Object.keys(selectPointsList).length">
            <div class="dom-content-form">
              <div class="label">区域名称:</div>
              <Input v-model.trim="addForm.name"
                    :disabled="!!selectDrawInfo.isOnlyView"
                    :maxlength="20"
                    placeholder="请输入"
                    style="width: auto"></Input>
            </div>
            <div class="label">框选设备:</div>
            <div class="layerList"
                v-if="!!Object.keys(selectPointsList).length">
                <template v-for="(e, key) in selectPointsList">
                    <div class="dom-content-type"
                        :key="key"
                        v-if="selectPointsList[key].list.length && !selectPointsList[key].show && selectPointsList[key].isLayerShow">
                        <div class="header"
                            @click.stop="switchHandle(key)">
                            <ui-icon :class="selectPointsList[key].isShow ? 'arrowrun' : 'arrow'"
                                    type="caret-right"
                                    color="#187AE4"></ui-icon>
                            <div class="device-type">
                                <ui-icon :type="deviceTypeMap[key].icon"
                                        :color="deviceTypeMap[key].color"></ui-icon>
                                <span>{{ deviceTypeMap[key].name }}</span>
                                <span v-if="selectPointsList[key].list.length"
                                    class="type-total">({{ selectPointsList[key].list.filter(e => !e.show).length }})</span>
                            </div>
                        </div>
                        <div class="dom-content-list"
                            slot="content"
                            v-if="e.isShow">
                            <template v-for="(item, i) in e.list">
                                <div :key="i"
                                    :class="item.Status == '1' ? 'offline' : ''"
                                    v-if="!item.show">
                                    <ui-icon :type="deviceTypeMap[key].icon"
                                            :color="deviceTypeMap[key].color"></ui-icon>
                                    <p :title="item.deviceName" 
                                      @click.stop="deviceClick(item)"
                                      @dblclick.stop="deviceDblClick(item)">{{ item.deviceName }}</p>
                                    <ui-icon title="复制名称" type="ETL-guanlian" @click.native.stop="copyText(item.deviceName)"></ui-icon>
                                    <ui-icon v-if="!selectDrawInfo.isOnlyView" title="删除" type="shanchu" @click.native.stop="delItem(e.list, i)"></ui-icon>
                                </div>
                            </template>
                        </div>
                    </div>
                </template>
            </div>
            <div class="action-wrapper" v-if="!selectDrawInfo.isOnlyView">
                <Button class="grouping" type="primary" @click="handleSave">保存</Button>
            </div>
        </section>
    </div>
</template>
<script>
import { copyText } from '@/util/modules/common'
import { addRegionalScope, updateRegionalScope } from '@/api/config'

export default {
  props: {
    selectDrawInfo: {
        type: Object,
        default: () => ({})
    },
  },
  watch: {
    selectDrawInfo: {
      handler({name, selectDeviceList}) {
        this.addForm.name = name || ''
        const { layerTypeList } = this.$parent.$parent
        // 组装框选数据分组
        let selectPointsMap = {}
        layerTypeList.forEach(e => {
          selectPointsMap[e] = {
            isLayerShow: true,
            isShow: true,
            list: []
          }
        })
        selectDeviceList.forEach(e => {
          selectPointsMap[e.LayerType].list.push(e)
          selectPointsMap[e.LayerType].layerType = e.LayerType
        })
        this.selectPointsList = {
          ...selectPointsMap
        }
      }
    }
  },
  data () {
    return {
      selectPointsList: {}, //框选设备
      deviceTypeMap: {
        // Camera: { name: '高清视频', icon: 'shebeizichan', color: '#187AE4' },
        Camera_QiuJi: { name: '球机', icon: 'qiuji', color: '#187AE4' },
        Camera_QiangJi: { name: '枪机', icon: 'shebeizichan', color: '#187AE4' },
        Camera_Vehicle: { name: '车辆卡口', icon: 'qiche', color: '#1faf8a' },
        Camera_Face: { name: '人脸卡口', icon: 'yonghu', color: '#48BAFF' },
        Camera_RFID: { name: 'RFID设备', icon: 'RFID', color: '#8173FF' },
        Camera_Wifi: { name: 'Wi-Fi设备', icon: 'wifi', color: '#914FFF' },
        Camera_Electric: { name: '电围设备', icon: 'ZM-dianwei', color: '#614FFF' },
        Camera_ETC: { name: 'ETC设备', icon: 'a-ETC1x', color: '#48BAFF' },
        Place_Hotel:     { name: "酒店", icon: "hotel", color: '#EB8A5D' },
        Place_InterBar:  { name: "网吧", icon: "diannao", color: '#EB6C6C' },
        Place_Government:{ name: "政府机关", icon: "gejizhengfu", color: '#187AE4' },
        Place_School:    { name: "学校", icon: "xuexiao", color: '#1faf8a' },
        Place_Key:       { name: "重点场所", icon: "yishoucang", color: '#EA4A36' },
      },
      addForm: {}
    }
  },
  methods: {
    copyText(name) {
      copyText(name)
    },
    // 展开-收起
    switchHandle (key) {
      const { isShow } = this.selectPointsList[key]
      this.selectPointsList[key].isShow = !isShow
    },
    deviceClick(item) {
      if (item.deviceId) {
        if (!item.Lat || !item.Lon) {
          this.$Message.info("该设备没有点位坐标信息！")
        } else {
          this.$emit('handleMapSelectDevice', item)
        }
      }
    },
    // 打开地图弹框
    deviceDblClick (item) {
      if (item.deviceId) {
        if (!item.Lat || !item.Lon) {
          this.$Message.info("该设备没有点位坐标信息！")
        } else {
          this.$emit('handleMapCurrentDevice', item)
        }
      }
    },
    delItem(array, index) {
      array.splice(index, 1)
    },
    handleSave() {
      if (!this.addForm.name) {
        this.$Message.error('请输入区域名称')
        return
      }
      const { id, selectGeometry } = this.selectDrawInfo
      let deviceList = []
      for (let key in this.selectPointsList) {
        deviceList = [...deviceList, ...this.selectPointsList[key].list]
      }
      this.$parent.$parent.getMapImg().then(src => {
        if (id) {
          updateRegionalScope({
            id,
            base64: src,
            deviceParam: JSON.stringify(deviceList),
            name: this.addForm.name,
            positionParam: selectGeometry
          }).then(res => {
            this.$parent.$parent.closeBottomTool()
            this.$parent.$parent.$emit('clear')
            this.$parent.$parent.$emit('refreshAreaList')
          })
        } else {
          addRegionalScope({
            base64: src,
            deviceParam: JSON.stringify(deviceList),
            name: this.addForm.name,
            positionParam: selectGeometry
          }).then(res => {
            this.$parent.$parent.closeBottomTool()
            this.$parent.$parent.$emit('clear')
            this.$parent.$parent.$emit('refreshAreaList')
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.select-result-dom {
  margin-right: 10px;
  height: calc(~'100vh - 220px');
  .dom-content {
    padding-bottom: 5px;
    height: 100%;
    .layerList {
      height: calc(~'100% - 130px');
      overflow-y: auto;
    }
    .label {
      margin: 10px;
      font-weight: bold;
    }
    &-form {
      display: flex;
      align-items: center;
      margin-top: 10px;
      .ivu-input-wrapper {
        flex: 1;
      }
    }
    &-type {
      padding: 0 16px;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        .device-type {
          flex: 1;
          display: flex;
          align-items: center;
          margin-left: 10px;
          > span {
            color: rgba(0, 0, 0, 0.9);
            font-weight: bold;
            margin-left: 11px;
          }
          .type-total {
            color: #2c86f8;
          }
        }
        .arrowrun {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(90deg);
        }
        .arrow {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(0deg);
        }
      }
    }
    &-list {
      padding-left: 28px;
      cursor: pointer;
      .offline {
        i {
          color: #888888!important;
        }
      }
      > div {
        display: flex;
        align-items: center;
        height: 30px;
        width: 100%;
      }
      P {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
      }
      > div:hover {
        background: rgba(44, 134, 248, 0.1);
        > P {
          color: rgba(0, 0, 0, 0.8);
        }
      }
      i {
        margin-left: 10px;
      }
      .label {
        display: inline-block;
        color: #ffffff;
        white-space: nowrap;
        width: 54px;
      }
      .message {
        display: inline-block;
        color: #b4ceef;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 18px;
      }
      .font-green {
        color: #19a33e;
      }
      .font-yellow {
        color: #bfa631;
      }
      .font-orange {
        color: #f5852a;
      }
    }
  }
}
/deep/.ivu-input-with-suffix {
  padding-right: 0.16667rem;
}
.action-wrapper {
  padding: 10px 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.grouping{
    margin-right: 10px;
}
</style>
