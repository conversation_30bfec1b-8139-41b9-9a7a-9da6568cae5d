import screenfull from 'screenfull';
export default {
  namespaced: true,
  state: {
    state: false, //状态 default fullScreen back
  },
  mutations: {
    setState(state, params) {
      state.state = params;
    },
  },
  getters: {
    getState(state) {
      return state.state;
    },
    getDesc(state) {
      return state.state ? '返回' : '全屏';
    },
    getIcon(state) {
      return state.state ? 'icon-fanhui' : 'icon-ivdg-quanping';
    },
  },
  actions: {
    setState({ commit }, self) {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit();
        commit('setState', false);
      } else {
        commit('setState', true);
        screenfull.toggle(self.$parent.$el);
      }
    },
  },
};
