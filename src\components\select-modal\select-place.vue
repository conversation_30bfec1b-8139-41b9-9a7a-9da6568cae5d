<template>
  <ui-modal
    v-model="modalShow"
    :r-width="1600"
    :title="'场所选择'"
    list-content
    @onCancel="handleCancel"
    @onOk="confirmHandle"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="select-label-left">
        <div class="organization mr-10">
          <div class="search-box">
            <Input
              placeholder="请输入场所类型名称"
              v-model="searchInput"
              width="100%"
              @keydown.enter.native="searchPlace"
            >
              <Icon
                type="ios-search"
                class="font-16 cursor-p"
                slot="suffix"
                maxlength="50"
                @click.prevent="searchPlace"
              />
            </Input>
          </div>
          <div class="place-tree-box">
            <el-tree
              ref="treeRef"
              :data="isSearch ? placeSearchList : placeTreeList"
              node-key="id"
              :default-expand-all="false"
              :expand-on-click-node="false"
              :highlight-current="true"
              :current-node-key="currentNodeKey"
            >
              <template slot-scope="{ node, data }">
                <div class="tree-item" @click.stop="treeNodeClick(data)">
                  <div class="tree-title">
                    <div class="tree-label">
                      {{ data.label }}(
                      <div class="tree-number" style="">
                        {{ data.allTotal || 0 }}
                      </div>
                      )
                    </div>
                  </div>
                </div>
              </template>
            </el-tree>
            <ui-empty v-if="placeTreeList.length === 0"></ui-empty>
            <ui-loading v-if="loading"></ui-loading>
          </div>
        </div>
        <div class="select-label-content">
          <DynamicSearch
            ref="dynamicSearch"
            :form-item-data="formItemData"
            :form-data="formData"
            @handleQuery="searchTablePlace"
            @resetForm="resetForm"
          ></DynamicSearch>
          <div class="manual">
            <!-- <Checkbox
              class="checks mr-lg align-flex"
              :value="
                selectFirstLevelList.includes(placeParams.firstLevel) ||
                selectSecondLevelList.includes(placeParams.secondLevelList[0])
              "
              @on-change="handleAllCheck"
            >
              全选
            </Checkbox> -->
          </div>
          <div class="auto-fill table">
            <ui-table
              ref="table"
              :columns="columns"
              :data="placeList"
              :loading="tableLoading"
              @on-select="onSelect"
              @on-select-cancel="onSelectCancel"
              @on-select-all="onSelectAll"
              @on-select-all-cancel="onSelectAllCancel"
              @scroll.stop
            >
              <template #image="{ row }">
                <div class="table-image" style="width: 110px; height: 60px">
                  <ui-image viewer type="place" :src="row.image" />
                </div>
              </template>
              <template #action="{ row }">
                <div class="table-action">
                  <i
                    class="iconfont icon-xiangqing"
                    @click="openDetail(row)"
                    title="详情"
                  ></i>
                  <i
                    class="iconfont icon-bianji"
                    @click="openDetailEdit(row)"
                    title="编辑"
                  ></i>
                  <i
                    class="iconfont icon-shanchu1"
                    @click="deleteHandler([row])"
                    title="删除"
                  ></i>
                </div>
              </template>
            </ui-table>
          </div>
          <!-- 分页 -->
          <div>
            <ui-page
              :current="pageNumber"
              :total="total"
              :page-size="pageSize"
              @pageChange="pageChange"
              @pageSizeChange="pageSizeChange"
            >
            </ui-page>
          </div>
        </div>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{ selectTableData.length }}</span> 个</span
          >
          <span class="del-btn" @click="removeAllHandle">
            <i class="iconfont icon-shanchu"></i>清空
          </span>
        </div>
        <div class="label-container">
          <ul>
            <li v-for="(item, index) in selectTableData" :key="item.id">
              <Checkbox :value="true" @on-change="selectChange(item, index)"
                >{{ item.name }}
              </Checkbox>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { countBySecondLevel, getaoi } from "@/api/operationsOnTheMap";
import { mapActions, mapGetters } from "vuex";
import { commonMixins } from "@/mixins/app.js";
import { defaultFormItemData, defaultFormData } from "./placeForm";
import { deepCopy } from "@/util/modules/common";
import { getPlaceTypeList, deletePlaceType } from "@/api/placeArchive.js";
export default {
  components: {
    DynamicSearch: require("./dynamic-search.vue").default,
  },
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
    // 是否展示选择所有
    isCheckShow: {
      type: Boolean,
      default: false,
    },
    // 兼容 取值字段不一致的情况
    defaultProps: {
      type: Object,
      default: () => {
        return {
          id: "deviceId", // 唯一键标识
          deviceName: "deviceName",
          // ...
        };
      },
    },
    // 对应 formData的数据
    formDataProp: {
      type: Object,
    },
    // 对应 formItemData的数据
    formItemList: {
      type: Array,
      default: () => [],
    },
    queryDataApiFn: {
      type: Function,
    },
    tableColumns: {
      type: Array,
    },
  },
  mixins: [commonMixins], //全局的mixin
  data() {
    return {
      modalShow: false,
      formData: {
        name: "",
        address: "",
      },
      pageSize: 20,
      pageNumber: 1,
      total: 0,
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "场所照片", slot: "image", align: "center" },
        { title: "场所名称", key: "name", align: "center" },
        { title: "场所面积(m²)", key: "area", align: "center" },
        { title: "所属区县", key: "adname", align: "center", width: 100 },
        { title: "所属街道", key: "townname", align: "center" },
        { title: "场所地址", key: "address", align: "center" },
      ],
      selectTableData: [],
      treeData: [],
      checkShow: this.isCheckShow,
      tableLoading: false,
      placeTreeList: [],
      placeSearchList: [],
      formItemData: defaultFormItemData,
      placeParams: {
        secondLevelList: [],
        firstLevel: "",
      },
      placeList: [],
      searchInput: "",
      currentNodeKey: "",
      loading: false,
      isSearch: "",
      selectSecondLevelList: [], // 全选的二级分类编码
      selectFirstLevelList: [], // 全选的一级分类编码
    };
  },
  created() {
    this.getDictData();
  },
  mounted() {},
  computed: {
    ...mapGetters({
      placeFirstLevelList: "dictionary/getPlaceFirstLevelList", // 场所一级分类
      placeSecondLevelList: "dictionary/getPlaceSecondLevelList", // 场所二级分类
    }),
  },
  watch: {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    async initPlaceTypeList() {
      let { data } = await getPlaceTypeList({});
      return data;
    },
    searchTablePlace() {
      this.pageNumber = 1;
      this.queryInfo();
    },
    searchPlace() {
      if (this.searchInput !== "") {
        this.isSearch = true;
        this.loading = true;
        this.placeSearchList = [];
      } else {
        this.isSearch = false;
        return;
      }
      this.placeTreeList.forEach((item) => {
        if (item.label.includes(this.searchInput)) {
          this.placeSearchList.push(item);
        } else {
          let searchChildList = deepCopy(item);
          searchChildList.children = [];
          let total = 0;
          item.children.forEach((v) => {
            if (v.label.includes(this.searchInput)) {
              total += v.allTotal;
              searchChildList.children.push(v);
            }
          });
          if (searchChildList.children.length > 0) {
            searchChildList.allTotal = total;
            this.placeSearchList.push(searchChildList);
          }
        }
      });
      this.loading = false;
    },
    // 初始化树数据
    async loadNode() {
      this.loading = true;
      try {
        let { data } = await countBySecondLevel();
        const placeListAll = await this.initPlaceTypeList();
        // 一级二级组合
        let group = [];
        placeListAll.forEach((v) => {
          // 二级场所类型
          if (v.parentId != -1) {
            return;
          }
          // 一级场所类型 添加二级内容
          let children = placeListAll.filter((item) => item.parentId == v.id);
          let total = 0;
          children = children.map((item) => {
            for (let i = 0; i < data.length; i++) {
              if (item.typeCode === data[i].key) {
                item.allTotal = data[i].statisticsQuantity || 0;
                total += data[i].statisticsQuantity || 0;
                data.splice(i, 1);
                break;
              }
            }
            return {
              ...item,
              label: item.typeName,
              id: item.id,
              leaf: true,
              leave: 2,
            };
          });
          group.push({
            ...v,
            children,
            label: v.typeName,
            id: v.id,
            leave: 1,
            allTotal: total,
          });
        });
        this.placeTreeList = group;
        // 默认选中第一项
        this.placeParams.firstLevel = this.placeTreeList[0]?.typeCode || "";
        this.queryInfo();
        this.$nextTick(() => {
          this.$refs.treeRef.setCurrentKey(this.placeTreeList[0]?.id || "");
          this.currentNodeKey = this.placeTreeList[0]?.id;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    /**
     * @description 点击子树
     * @param data
     */
    treeNodeClick(data) {
      // 更新elTree 的选中树
      this.$nextTick(() => {
        this.$refs.treeRef.setCurrentKey(data.id);
      });
      this.currentNodeKey = data.id;
      // 右侧table查询参数
      if (data.leave > 1) {
        // 二级参数
        this.placeParams.secondLevelList = [data.typeCode];
        this.placeParams.firstLevel = "";
      } else {
        // 一级参数
        this.placeParams.secondLevelList = [];
        this.placeParams.firstLevel = data.typeCode;
      }
      // 初始化分页
      this.pageNumber = 1;
      this.queryInfo();
    },
    /**
     * @description 列表二级查询
     * @param param 二级信息
     */
    queryInfo(param = {}) {
      this.tableLoading = true;
      getaoi({
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...this.placeParams,
        ...this.formData,
        ...param,
      })
        .then(({ data }) => {
          this.total = data.total;
          this.placeList = data.entities;
          this.tableIsSelect();
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 显示model
     */
    show(list = [], keyWords = "") {
      this.modalShow = true;
      this.selectTableData = deepCopy(list);
      this.loadNode();
    },
    pageChange(value) {
      this.pageNumber = value;
      this.queryInfo();
    },
    pageSizeChange(value) {
      this.pageSize = value;
      this.queryInfo();
    },
    /**
     * 右侧清空
     */
    removeAllHandle() {
      this.selectTableData = [];
      this.$refs.table.getTabelRef().selectAll(false);
    },

    /**
     * 确定按钮
     */
    confirmHandle() {
      this.modalShow = false;
      let list = deepCopy(this.selectTableData);
      this.$emit("selectPlace", list);
    },
    // 取消
    handleCancel() {
      if (this.showOrganization) {
        this.$refs.resourceTree.setCheckedNodes([]);
      }
      this.isAll = false;
      this.isExclude = false;
    },
    /**
     * table 选中一项
     */
    onSelect(selection, row) {
      var obj = this.selectTableData.find((item) => {
        return item.id == row.id;
      });
      row.select = true;
      if (!obj) {
        this.selectTableData.push(row);
      } else {
        obj.select = true;
      }
    },
    /**
     * table 取消选中一项
     */
    onSelectCancel(selection, row) {
      var num = this.selectTableData.findIndex((item) => {
        return item.id == row.id;
      });
      this.selectTableData.splice(num, 1);
    },
    /**
     * table 全选
     */
    onSelectAll(selection) {
      selection.forEach((item) => {
        item.select = true;
        var obj = this.selectTableData.find((itm) => {
          return itm.id == item.id;
        });
        if (!obj) {
          this.selectTableData.push(item);
        }
      });
    },

    /**
     * table 取消全选
     */
    onSelectAllCancel(selection) {
      this.placeList.forEach((item) => {
        var num = this.selectTableData.findIndex((itm) => {
          return itm.id == row.id;
        });
        if (num != -1) {
          this.selectTableData.splice(num, 1);
        }
      });
    },
    /**
     * 重置表单
     */
    resetForm() {
      this.pageSize = 20;
      this.pageNumber = 1;
      this.queryInfo();
    },
    // 全选
    async handleAllCheck(val) {
      try {
        if (val) {
          // 将该分类下的所有场所装进去
          let { data } = await getaoi({
            pageNumber: 1,
            pageSize: this.total,
            ...this.placeParams,
            ...this.formData,
          });
          this.selectTableData.push(...data.entities);
        } else {
          this.selectTableData = [];
        }
        this.tableIsSelect();
      } catch (error) {
        console.error(error);
      } finally {
        this.tableLoading = false;
      }
    },
    /**
     * table回显
     */
    tableIsSelect() {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.$refs.table.getTabelRef().objData;
        // 清空table选中状态
        Object.keys(obj).forEach((key) => {
          obj[key]._isChecked = false;
        });
        if (this.selectTableData.length == 0) {
          return;
        }
        // 回显
        Object.keys(obj).forEach((key) => {
          var row = this.selectTableData.find((i) => {
            return obj[key].id == i.id;
          });
          if (row) {
            this.$refs.table.getTabelRef().objData[key]._isChecked = true;
          }
        });
      }, 20);
    },
    selectChange(item, index) {
      this.selectTableData.splice(index, 1);
      this.tableIsSelect();
    },
  },
};
</script>
<style lang="less" scoped>
.sele-type {
  display: flex;
  margin-bottom: 10px;
}

.find {
  margin-right: 10px;
}

/deep/ .ivu-input-wrapper {
  width: 100% !important;
}

/deep/ .ivu-modal-body {
  height: 650px;
}

.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 0px 0px;
  display: flex;
  height: 100%;

  .select-label-left {
    display: flex;
    flex: 1;
  }

  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;

    .place-table-box {
      height: 100%;
      width: 100%;
    }

    .manual {
      display: flex;
    }
  }

  .map-content {
    flex: 1;
    height: 520px;
  }

  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;

    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  .label-container {
    overflow: auto;
    height: 400px;
    position: relative;
    flex: 1;
  }

  .organization {
    width: 240px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: auto;
    border-right: 1px solid #d3d7de;

    .search-box {
      padding: 5px;

      /deep/ .ivu-input {
        width: 100%;
      }
    }

    .place-tree-box {
      position: relative;
      overflow-y: scroll;
      // el-tree组件右侧不对其
      flex: 1;

      .tree-item {
        width: 100%;
        display: flex;
        justify-content: space-between;
        height: 35px;
        line-height: 35px;

        .tree-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.8);

          .tree-label {
            font-weight: 600;
            display: flex;

            .tree-number {
              color: #2c86f8;
              font-weight: normal;
            }
          }
        }

        .tree-action {
          display: none;

          i {
            padding-left: 10px;
            font-size: 16px;
            color: #2c86f8;
          }
        }

        &:hover {
          .tree-action {
            display: block;
          }
        }
      }

      /deep/.el-tree-node.is-current > .el-tree-node__content {
        background: #2c86f8 !important;
        color: #fff;

        .tree-title {
          color: #fff;

          .tree-label {
            .tree-number {
              color: #fff;
            }
          }
        }

        .tree-action {
          display: block;

          i {
            color: #fff;
          }
        }
      }
    }
  }

  .preview-select-label-content {
    width: 250px;
    padding: 10px 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;

    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;

      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }

    .label-container {
      padding-top: 10px;
    }
  }
}

.del-btn {
  cursor: pointer;
}

.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 10px;
}

.form {
  width: 100%;

  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }

  /deep/.ivu-select {
    width: 100%;
  }

  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");

    .ivu-form-item-content {
      flex: 1;

      .ivu-select {
        width: 100%;
      }
    }
  }

  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }

  /deep/ .ivu-form-item-content {
    display: flex;
  }
}

.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;

  span {
    font-size: 14px;
    color: #000;

    i {
      color: #23a8f9;
      margin-right: 10px;
    }
  }
}
</style>
