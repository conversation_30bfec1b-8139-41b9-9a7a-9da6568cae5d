<template>
  <div class="layout">
    <Anchor class="anchor" show-ink container=".content" :scroll-offset="30">
      <AnchorLink href="#module1" title="重点人管控专题" />
    </Anchor>
    <div class="content">
      <div class="module" id="module1">
        <div class="title">
          <h2>重点人管控专题</h2>
          <Button type="primary" @click="saveImportantConfig()">保存</Button>
        </div>
        <div class="row">
          <div class="left-text">重点场所人员活动</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">重点场所选择</span>
                <div class="dashed_line w-220" @click="selectPlaceHandler">
                  选择场所/已选({{ selectPlaceList.length }})
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">与其他重点人同行</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">同行次数≥</span>
                <div class="right-input w-220">
                  <InputNumber
                    placeholder="请输入"
                    v-model="importantConfig.travelAlongConfig.minCount"
                    style="width: 100%"
                    :min="1"
                    :precision="0"
                  >
                  </InputNumber>
                  <div class="position">次</div>
                </div>
              </div>
              <div class="childRight">
                <span class="span">时间间隔≤</span>
                <div class="right-input w-220">
                  <InputNumber
                    placeholder="请输入"
                    v-model="
                      importantConfig.travelAlongConfig.maxIntervalSeconds
                    "
                    style="width: 100%"
                    :min="1"
                    :precision="0"
                  >
                  </InputNumber>
                  <div class="position">秒</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">人员轨迹活动异常</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">布控任务选择</span>
                <div class="right-input w-220">
                  <!-- <div>{{ libList }}</div> -->
                  <Select
                    v-model="importantConfig.abnormalActivityConfig.taskIds"
                    placeholder="请选择布控任务"
                    transfer
                    multiple
                  >
                    <Option
                      v-for="item in taskList"
                      :key="item.taskId"
                      :value="item.taskId"
                    >
                      {{ item.taskName }}
                    </Option>
                  </Select>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">重点人员发现报警</div>
          <div class="table">
            <alarmTable
              type="people"
              :tableData="importantConfig.alarmLevelConfig"
              @colorRefrsh="colorRefrsh"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 选择场所 -->
    <SelectPlace ref="selectPlace" @selectPlace="selectPlaceConfirm">
    </SelectPlace>
  </div>
</template>
<script>
import SelectPlace from "@/components/select-modal/select-place.vue";
import alarmTable from "@/views/ipbd-system/basicsConfig/components/alarm-table.vue";
import { importantFormData } from "./importantForm.js";
import {
  getImportantConfig,
  updateImportantConfig,
  getConfigPlaces,
  taskPageList,
} from "@/api/monographic/importantPerson-management.js";
export default {
  name: "ipbd-system",
  components: {
    SelectPlace,
    alarmTable,
  },
  props: {},
  data() {
    return {
      taskList: [],
      importantConfig: { ...importantFormData }, // 社区管控专题
      selectPlaceList: [],
    };
  },
  computed: {},
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.getTaskList();
    // 社区管控配置
    this.queryImportantConfig();
  },
  methods: {
    // 获取布控任务列表
    getTaskList() {
      taskPageList({
        compareType: 1,
        pageNumber: 1,
        pageSize: 999,
      }).then((res) => {
        this.taskList = res?.data?.entities || [];
      });
    },
    queryImportantConfig() {
      getImportantConfig().then((res) => {
        let importantData = res?.data?.paramValue || "{}";
        let json = JSON.parse(importantData);
        Object.keys(json).forEach((item) => {
          this.importantConfig[item] = json[item];
        });
        if (this.importantConfig.alarmLevelConfig.length == 0) {
          // 给默认值
          this.importantConfig.alarmLevelConfig = [{}, {}, {}];
        }
        // 回填场所信息
        getConfigPlaces().then(({ data = [] }) => {
          this.selectPlaceList =
            data?.map((item) => {
              return {
                ...item,
              };
            }) || [];
        });
      });
    },
    saveImportantConfig() {
      let param = { ...this.importantConfig };
      updateImportantConfig(param).then((res) => {
        this.$Message.success("修改" + res.msg);
      });
    },
    // 场所选择
    selectPlaceHandler() {
      this.$refs.selectPlace.show(this.selectPlaceList);
    },
    selectPlaceConfirm(value) {
      this.selectPlaceList = value;
      this.importantConfig.placeIds = value.map((item) => item.id);
    },
    colorRefrsh(index, name, type) {
      this.importantConfig.alarmLevelConfig[index].alarmColour = name;
      this.$forceUpdate();
    },
  },
};
</script>
<style lang="less" scoped>
.w-220 {
  width: 220px;
}
.padding-10 {
  padding: 10px !important;
}

.layout {
  position: relative;
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  // overflow-y: auto;
  padding-bottom: 20px;

  .button {
    position: fixed;
    right: 30px;
    top: 110px;
    cursor: pointer;
  }

  .content {
    width: calc(~"50% + 64px");
    overflow: auto;
    scrollbar-width: none;

    .module {
      padding: 0 32px 20px;
      margin-top: 35px;
      border-bottom: 1px dashed #d3d7de;

      .title {
        display: flex;
        justify-content: space-between;
      }

      .row {
        display: flex;
        justify-content: start;
        font-size: 14px;
        min-height: 50px;
        // line-height: 50px;
        margin-top: 20px;

        .left {
          min-width: 80px;
        }

        .left-text {
          width: 70px;
          text-align: right;
          margin-right: 15px;
        }

        .right-more {
          display: flex;
          flex-direction: column;
          gap: 10px;
          flex: 1;

          /deep/ .ivu-input {
            width: 100%;
          }
        }

        .right-input {
          display: flex;
          align-items: center;
          position: relative;

          .position {
            position: absolute;
            right: 30px;
          }
        }

        .right {
          flex: 1;
          background: #f9f9f9;
          padding: 0 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .span {
            float: left;
            margin-right: 20px;
            display: inline-block;
          }

          .ivu-checkbox-group {
            float: left;
          }

          .ivu-select {
            flex: 1;
          }

          .btn {
            position: relative;
            display: flex;
            align-items: center;

            .position {
              position: absolute;
              right: 10px;
            }
          }

          .more-datepicke {
            display: flex;
            flex-direction: column;
          }
        }

        .justContent {
          justify-content: space-around;
        }

        .right2 {
          flex: 1;
          display: flex;
          min-height: 50px;
          line-height: 50px;
          justify-content: space-between;

          .childLeft {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;

            .position {
              position: absolute;
              right: 30px;
            }
          }

          .childRight {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;

            .position {
              position: absolute;
              right: 30px;
            }
          }
        }

        .dashed_line {
          border: 1px dashed #3c8bef;
          height: 36px;
          line-height: 36px;
          padding: 0 10px;
          border-radius: 6px;
          color: #2c86f8;
          cursor: pointer;
          background: #ebf4ff;
          text-align: center;
        }
      }

      .aotoHeight {
        height: auto;

        .right {
          padding: 0;
        }
      }
    }

    .module:last-of-type {
      border: none;
      padding-bottom: 40px;
    }
  }
}

.content::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

.preview {
  position: relative;
  width: 100%;

  .seleList {
    position: absolute;
    z-index: 9;
    display: inline-block;
    font-size: 12px;
    line-height: 22px;
    // pointer-events: none;
    margin-left: 5px;
    top: 50%;
    transform: translate(0, -50%);
    margin-right: 30px;

    .seletabs {
      border: 1px solid #e8eaec;
      background: #f7f7f7;
      padding: 0 6px;
      display: inline-block;
      font-size: 14px;
      border-radius: 5px;
      margin: 3px 4px 3px 0;
      position: relative;
      cursor: move;

      span {
        margin-right: 14px;
      }

      .icon-close {
        cursor: pointer;
        color: #66666675;
        font-size: 14px;
        position: absolute;
        right: 6px;
        top: 2px;
      }
    }
  }
}

/deep/ .ivu-input-group-append {
  width: 30px;
}

/deep/ .ivu-input {
  width: 150px;
}

.people-case {
  display: flex;
  margin-top: 10px;

  .left {
    align-self: center;
  }

  .right {
    margin-left: 20px;
    flex: 1;
  }

  .row {
    width: 100%;
    margin-top: 0px !important;
    justify-content: space-between !important;
    background: #f9f9f9;
    padding: 0 10px;
  }
}

.table {
  flex: 1;

  /deep/ .ivu-input {
    width: 120px;
  }
}

.link-active-color {
  color: #1a74e7;
  cursor: pointer;
}

/deep/ .ivu-select-item-disabled {
  color: #c5c8ce;
}

/deep/ .icon-tishi {
  color: #f29f4c;
}

.anchor-point-infomation {
  width: 100px;
  position: fixed;
  top: 78px;
  right: 18px;
  z-index: 9;

  .export-btn {
    margin-bottom: 10px;
  }
}

.anchor {
  width: 120px;
  position: absolute;
  right: 17%;
  margin-top: 30px;

  /deep/ .ivu-anchor-wrapper {
    box-shadow: 0 0 !important;
  }
}

h2 {
  color: #000;
  font-size: 24px;
}
</style>
