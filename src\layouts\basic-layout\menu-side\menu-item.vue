<template>
  <div>
    <MenuItem
      v-if="!menu.hidden"
      :to="menu.path"
      :replace="menu.replace"
      :name="menu.name"
      @click.native="handleClick(menu)"
    >
      <i-menu-side-title :menu="menu" :hide-title="hideTitle" />
      <Badge
        v-if="badgeData"
        v-bind="badgeData"
        class="i-layout-menu-side-badge"
      />
    </MenuItem>
  </div>
</template>
<script>
import iMenuSideTitle from "./menu-title";
import clickItem from "../mixins/click-item";
import menuBadge from "../mixins/menu-badge";

export default {
  name: `iMenuSideItem`,
  components: { iMenuSideTitle },
  mixins: [clickItem, menuBadge],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    handleClick(menu) {
    //   console.log(menu);
    },
  },
};
</script>
