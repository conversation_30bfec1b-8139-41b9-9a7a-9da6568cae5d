<template>
  <div class="statistical-main" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <div class="evaluate-tendency">
      <!-- 地市柱状图统计 -->
      <div class="mt-sm cl grap-content">
        <Checkbox v-model="sort" @on-change="onChangeSort">按得分排序</Checkbox>
        <div class="echarts-box">
          <draw-echarts
            v-if="echartList.length"
            :echart-option="taskEchart"
            :echart-style="ringStyle"
            ref="taskChart"
            class="charts"
            :echarts-loading="echartsLoading"
            @echartClick="echartClick"
            @echartLegendselectchanged="onSelectedChange"
          ></draw-echarts>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
import Vue from 'vue';
import taskTooltip from '@/views/governanceevaluation/resultExamination/components/task-tooltip';
export default {
  name: 'statisticalGraph',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    echartList: {
      type: Array,
      default: () => [],
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
    search: {},
    data: {},
  },
  data() {
    return {
      sort: false, //排序
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      num: 0,
      lengName: [],
      echartsSelected: [],
      name: '',
      taskEchart: {},
      colorList: [
        'icon-bg1',
        'icon-bg2',
        'icon-bg3',
        'icon-bg4',
        'icon-bg5',
        'icon-bg6',
        'icon-bg7',
        'icon-bg8',
        'icon-bg9',
        'icon-bg10',
      ],
      searchData: {},
      taskList: [],
      tooltipFormatter: (data) => {
        data.map((item) => {
          this.taskList.forEach((i) => {
            i.children &&
              i.children.forEach((ite) => {
                if (item.axisValue == ite.regionName && item.seriesName === ite.name) {
                  item.taskList = ite.children;
                }
              });
          });
        });
        let taskTooltipShow = Vue.extend(taskTooltip);
        let _this = new taskTooltipShow({
          el: document.createElement('div'),
          data() {
            return {
              data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    echartList: {
      handler(val) {
        this.initRing(val);
      },
      deep: true,
      immediate: true,
    },
    search: {
      handler(val) {
        this.searchData = val;
      },
      deep: true,
      immediate: true,
    },
    data: {
      handler(val) {
        this.taskList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /**
     *
     * @param data {array} 考核任务统计
     * @param name {string} "车辆卡口考核得分"
     */
    sortEcharts(data, name) {
      //取出children
      let children = data.map((item) => item.children);
      //排序
      try {
        let sortChildren = children.sort((a, b) => {
          let aName = a.find((aItem) => aItem.name === name);
          let bName = b.find((bItem) => bItem.name === name);
          return bName.score - aName.score;
        });

        //还原
        return sortChildren.map((item) => {
          let { regionCode, regionName } = item[0];
          return {
            children: item,
            name: null,
            regionCode,
            regionName,
            score: null,
          };
        });
      } catch (e) {
        console.log('排序失败', e);
        return data;
      }
    },
    onChangeSort(val) {
      this.$emit('on-sort', val);
    },
    echartClick(val) {
      this.$emit('echartClick', val);
    },
    onSelectedChange(val) {
      this.echartsSelected = val.selected;
      let name = Object.keys(this.echartsSelected).find((key) => this.echartsSelected[key]);
      if (!this.sort || !name) return;
      this.initRing(this.sortEcharts(this.echartList, name));
    },
    // 柱状统计图
    initRing(list) {
      let { regionCode } = this.taskList.find((value) => value.id === this.searchData.examTaskId) || {};
      let yAxis = {};
      this.lengName = [];
      this.taskList = [];
      if (list.length > 0) {
        list.map((item) => {
          if (item.regionCode !== regionCode) {
            this.lengName.push(item.regionName);
            this.taskList.push({ children: item.children });
          }
          if (item.children.length > 0) {
            item.children.map((i) => {
              if (i.regionCode !== regionCode) {
                if (!yAxis[i.name]) {
                  yAxis[i.name] = [i.score];
                } else {
                  yAxis[i.name].push(i.score);
                }
              }
            });
          }
        });
      }
      let series = [];
      for (let key in yAxis) {
        series.push({
          name: key,
          type: 'bar',
          data: yAxis[key],
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barWidth: '30px',
          showSymbol: false,
          label: {
            show: false,
            position: 'top',
          },
        });
        this.num++;
      }
      let opts = {
        selected: this.echartsSelected,
        xAxis: this.lengName,
        data: series,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.taskEchart = this.$util.doEcharts.taskResultMadeSC(opts);
    },
  },
};
</script>
<style lang="less" scoped>
.statistical-main {
  width: 100%;
  height: 100%;
  .evaluate-tendency {
    height: 100%;
    color: #fff;
    /deep/.ivu-tooltip-inner {
      overflow: auto;
    }
    .fl-st {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .fs-16 {
      font-size: 16px;
      font-weight: bold;
    }
    .score-icon {
      font-size: 18px;
      background: var(--color-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .grap-content {
      height: 100%;
      width: 100%;
      .echarts-box {
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;
        .charts {
          height: 100%;
          width: 100%;
          display: inline-block;
          flex: 1;
        }
      }
      .next-echart {
        width: 22px;
        height: 22px;
        background: #02162b;
        border: 1px solid #10457e;
        opacity: 1;
        border-radius: 4px;
        top: 50%;
        right: 8px;
        position: absolute;
        text-align: center;
        line-height: 22px;
        transform: translate(0, -50%);
        .icon-youjiantou1 {
          color: var(--color-primary);
          font-size: 12px;
          vertical-align: top !important;
        }
        &:active {
          width: 22px;
          height: 22px;
          background: #02162b;
          border: 1px solid var(--color-primary);
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: #4e9ef2;
            font-size: 12px;
            vertical-align: top !important;
          }
        }
        &:hover {
          width: 22px;
          height: 22px;
          background: #02162b;
          border: 1px solid #146ac7;
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: var(--color-primary);
            font-size: 12px;
            vertical-align: top !important;
          }
        }
      }
    }
  }
  .icon-bg1 {
    background: linear-gradient(92deg, #007bd9 0%, #0ca7dc 100%);
    opacity: 1;
  }
  .icon-bg2 {
    background: linear-gradient(92deg, #5f63cc 0%, #373dd1 100%);
    opacity: 1;
  }
  .icon-bg3 {
    background: linear-gradient(92deg, #185cea 0%, #1548b4 100%);
    opacity: 1;
  }
  .icon-bg4 {
    background: linear-gradient(92deg, #10b599 0%, #108faf 100%);
    opacity: 1;
  }
  .icon-bg5 {
    background: linear-gradient(180deg, #af8748 0%, #8f5d0e 100%);
    opacity: 1;
  }
  .icon-bg6 {
    background: linear-gradient(92deg, #e8396f 0%, #de4c52 100%);
    opacity: 1;
  }
  .icon-bg7 {
    background: linear-gradient(92deg, #a1bb12 0%, #569505 100%);
    opacity: 1;
  }
  .icon-bg8 {
    background: linear-gradient(92deg, #c911d6 0%, #7a15b4 100%);
    opacity: 1;
  }
  .icon-bg9 {
    background: linear-gradient(92deg, #07a22c 0%, #0d7d33 100%);
    opacity: 1;
  }
  .icon-bg10 {
    background: linear-gradient(92deg, #c45b8c 0%, #9d38a1 100%);
    opacity: 1;
  }
}
</style>
