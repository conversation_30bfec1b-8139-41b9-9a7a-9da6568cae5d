<template>
  <!-- 数量达标率 （市） -->
  <ui-modal class="city-quantity-reach" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-if="Object.keys(indexList).length">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button>
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="list auto-fill">
          <ui-table
            class="auto-fill"
            :tableColumns="tableColumns"
            :table-data="tableData"
            :minus-height="minusTable"
            :loading="loading"
          >
            <template #regionName="{ row }">
              <span>{{ row.regionName === null ? '--' : row.regionName }}</span>
            </template>
            <template #sxjCount="{ row }">
              <span>{{ row.sxjCount }}</span>
            </template>
            <template #sxjReportCount="{ row }">
              <span>{{ row.sxjReportCount === null ? '--' : row.sxjReportCount }}</span>
            </template>

            <template #sxjStandardsValue="{ row }">
              <span>{{ row.sxjStandardsValue === null ? '--' : row.sxjStandardsValue }}</span>
            </template>

            <template #rlkkCount="{ row }">
              <span>{{ row.rlkkCount === null ? '--' : row.rlkkCount }}</span>
            </template>
            <template #rlkkReportCount="{ row }">
              <span>{{ row.rlkkReportCount === null ? '--' : row.rlkkReportCount }}</span>
            </template>

            <template #rlkkStandardsValue="{ row }">
              <span>{{ row.rlkkStandardsValue === null ? '--' : row.rlkkStandardsValue }}</span>
            </template>
            <template #clkkCount="{ row }">
              <span>{{ row.clkkCount === null ? '--' : row.clkkCount }}</span>
            </template>

            <template #clkkReportCount="{ row }">
              <span>{{ row.clkkReportCount === null ? '--' : row.clkkReportCount }}</span>
            </template>

            <template #clkkStandardsValue="{ row }">
              <span>{{ row.clkkStandardsValue === null ? '--' : row.clkkStandardsValue }}</span>
            </template>
          </ui-table>
        </div>
        <table class="table_content">
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>达标区县数量</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardSxjReportCount === null ? '--' : indexList.quantityStandardSxjReportCount
                }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardRlkkReportCount === null ? '--' : indexList.quantityStandardRlkkReportCount
                }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardClkkReportCount === null ? '--' : indexList.quantityStandardClkkReportCount
                }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>不达标区县数量</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{ sxjCount }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{ rlkkCount }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{ clkkCount }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>分项达标率</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardSxjFormula === null ? '--' : indexList.quantityStandardSxjFormula
                }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardRlkkFormula === null ? '--' : indexList.quantityStandardRlkkFormula
                }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardClkkFormula === null ? '--' : indexList.quantityStandardClkkFormula
                }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>数量达标率</span>
              </div>
            </td>
            <td class="td_text">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardFormula === null ? '--' : indexList.quantityStandardFormula
                }}</span>
              </div>
            </td>
          </tr>
        </table>

        <div class="content_footer">
          <div class="sta_item_right">
            <div class="equip_num">
              <div class="precision">
                <i class="i_line"></i>
                <span
                  >{{ moduleData.price }}：<i
                    class="font-green"
                    :class="moduleData.priceValue < 100 ? 'color_red' : 'color_green'"
                    >{{ moduleData.priceValue }}%</i
                  ></span
                >
              </div>
            </div>
            <div class="result">
              <div class="precision">
                <i class="i_line"> </i><span>{{ moduleData.result }}：</span>
                <span
                  v-if="moduleData.resultValue != '合格'"
                  class="icon-font icon-zhiliangfen-line standard_icon font-warning"
                >
                  <i class="icon_text_error font-warning">不达标</i>
                </span>
                <span
                  v-if="moduleData.resultValue === '合格'"
                  class="icon-font icon-zhiliangfen-line standard_icon font-green"
                >
                  <i class="icon_text_succeed inline font-green">达标</i>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt />
        <img v-else src="@/assets/img/common/nodata-light.png" alt />
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.city-quantity-reach {
  //       .no-data_str {
  //   transform: translate(-24%, -50%);
  // }
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 40px;
        display: inline-block;
        line-height: 40px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 170px;
        line-height: 170px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 170px;
        line-height: 170px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    @media screen and (max-width: 1366px) {
      .list {
        margin-top: 10px;
        height: 100%;
        overflow-y: scroll;
        .ui-table {
          @{_deep}.ivu-table-body {
            min-height: 246px !important;
            max-height: 246px !important;
            overflow-y: scroll;
          }
        }
      }
    }
    .list {
      margin-top: 10px;
      height: 388px;
      overflow-y: scroll;
      @{_deep}.ivu-table-wrapper {
        height: 350px !important;
      }
      @{_deep}.ivu-table-body {
        min-height: 250px !important;
        max-height: 250px !important;
        overflow-y: scroll;
      }
      @{_deep}.ivu-table-header {
        tr {
          border-top: 1px solid var(--border-color) !important;
          border-right: 1px solid var(--border-color) !important;
          th {
            border-left: 1px solid var(--border-color) !important;
          }
        }
      }

      @{_deep}.ivu-table-tbody {
        tr {
          border-top: 1px solid var(--border-color) !important;
          border-right: 1px solid var(--border-color) !important;
          td {
            border-left: 1px solid var(--border-color) !important;
            border-bottom: 1px solid var(--border-color) !important;
          }
        }
      }
      .ui-table {
        position: relative;
      }
    }
    @{_deep}.no-data {
      position: absolute;
      left: 50%;
      top: 62%;
      transform: translateX(-50%) translateY(-50%);
      .no-data-img {
        width: 50%;
        height: 100%;
      }
      .null-data-text {
        color: var(--color-primary);
        font-size: 16px;
        line-height: 1.5;
      }
    }
    .table_content {
      height: 200px;
      width: 100%;
      margin-bottom: 10px;
      border-bottom: 1px solid var(--border-color);
      .con_one {
        height: 50px;
        width: 100%;
        display: flex;
        border-top: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);
        border-bottom: 1px solid var(--border-color) !important;

        td {
          text-align: center;
          line-height: 50px;
          border-left: 1px solid var(--border-color);
          div {
            height: 100%;
            display: inline-block;
            width: 100%;
          }
        }
        .con_color {
          color: #0d477d;
        }
        .quantity_color {
          color: #bc3c19;
        }
        .td_one {
          width: 260px;
          background-color: #092955;
          // flex: 1;
        }
        .td_text {
          flex: 1;
        }
        .td_two {
          flex: 1;
        }
        .td_three {
          flex: 1;
        }
        .td_four {
          flex: 1;
        }
      }
    }
  }
  .content_footer {
    height: 160px;
    width: 100%;

    background-color: var(--bg-sub-content);
    .sta_item_right {
      display: flex;
      height: 100%;
      line-height: 160px;
      .i_line {
        width: 6px;
        height: 20px;
        margin-right: 10px;
        margin-left: 45px;
        background: var(--color-primary);
      }
      .equip_num {
        .precision {
          img {
            vertical-align: middle;
          }
        }
      }
      .result {
        .precision {
        }
        .standard_icon {
          vertical-align: middle;
          font-size: 120px;
          position: relative;
          margin-left: 30px;
          .icon_text_error {
            font-size: 16px;
            position: absolute;
            right: 37px;
            top: -20px;
            font-weight: bold;
            transform: rotate(-32deg);
          }
          .icon_text_succeed {
            font-size: 16px;
            position: absolute;
            right: 44px;
            top: -20px;
            font-weight: bold;
            transform: rotate(-32deg);
          }
        }
      }
    }
    .color_blue {
      color: var(--color-bluish-green-text);
    }
    .color_green {
      color: #19c176;
    }
    .color_red {
      color: #bc3c19;
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      loading: false,
      moduleData: {
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: 0,
      },
      tableColumns: [
        {
          title: '序号',
          width: 60,
          align: 'center',
          render: (h, params) => {
            return h('span', params.row._index + 1);
          },
        },

        {
          title: '行政区域',
          key: 'regionName',
          slot: 'regionName',
          align: 'center',
          width: 100,
        },
        {
          title: '区域类型',
          key: 'regionTypeName',
          // slot: "regionTypeName",
          align: 'center',
          width: 100,
        },

        {
          title: '视频监控',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'sxjCount',
              slot: 'sxjCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'sxjReportCount',
              slot: 'sxjReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'sxjStandardsValue',
              slot: 'sxjStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },
        {
          title: '人脸卡口',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'rlkkCount',
              slot: 'rlkkCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'rlkkReportCount',
              slot: 'rlkkReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'rlkkStandardsValue',
              slot: 'rlkkStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },

        {
          title: '车辆卡口',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'clkkCount',
              slot: 'clkkCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'clkkReportCount',
              slot: 'clkkReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'clkkStandardsValue',
              slot: 'clkkStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },
      ],
      tableData: [],

      minusTable: 615,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '7.7rem',
      },
      visible: true,
      detailInfo: {},
      detailList: [],
      modalId: { title: '测评结果' },
      indexList: {},
      sxjCount: 0,
      rlkkCount: 0,
      clkkCount: 0,
      exportLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  async mounted() {
    await this.getEvaluationRecord();
  },

  methods: {
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.get(governanceevaluation.exportQuantityStandardForCity, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    operationTask() {},
    async getEvaluationRecord() {
      try {
        this.loading = true;
        this.tableData = [];
        let data = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(governanceevaluation.queryEvaluationBasicRecord, data);
        if (!res.data.data) return;
        this.indexList = res.data.data;
        this.moduleData.rateValue = this.indexList.resultValue; //建档率
        this.moduleData.priceValue = this.indexList.standardsValue; //达标值
        this.moduleData.resultValue = this.indexList.qualifiedDesc; //考核结果
        this.tableData = this.indexList.quantityStandardRecordDetailVos;
        this.sxjCount = this.indexList.quantityStandardSxjCount - this.indexList.quantityStandardSxjReportCount || '--';
        this.rlkkCount =
          this.indexList.quantityStandardRlkkCount - this.indexList.quantityStandardRlkkReportCount || '--';
        this.clkkCount =
          this.indexList.quantityStandardClkkCount - this.indexList.quantityStandardClkkReportCount || '--';
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    lineTitle: require('@/components/line-title').default,
  },
};
</script>
