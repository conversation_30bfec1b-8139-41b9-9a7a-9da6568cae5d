@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
.area-container {
  border: 1px solid var(--border-modal-footer);
  .tree-filter {
    line-height: 34px;
    margin-bottom: 30px;
  }
  .tree-title {
    position: relative;
    line-height: 48px;
    height: 48px;
    width: 100%;
    background: var(--bg-table-header-th);
    .org-name {
      position: absolute;
      left: 30px;
    }
    .is-all {
      position: absolute;
      right: 70px;
    }
    .is-checked {
      position: absolute;
      right: 174px;
    }
    .car {
      position: absolute;
      right: 448px;
    }
    .face {
      position: absolute;
      right: 667px;
    }
    .video {
      position: absolute;
      right: 928px;
    }
  }

  .tree-wrapper {
    height: 500px;
    overflow: auto;
    .ui-search-tree {
      height: 100%;
    }
    .options {
      .value {
        margin-right: 114px;
      }
    }
    @{_deep} .el-tree {
      .el-tree-node {
        .el-tree-node__content {
          height: 40px;
          &.has-child-panel {
            margin-bottom: 5px;
          }
          .custom-tree-node {
            line-height: 34px;
          }
          .el-tree-node__expand-icon {
            margin-left: 20px;
            font-size: 16px;
            margin-top: -2px;
          }
        }
      }
    }
  }
}

@{_deep} .ivu-modal-body {
  max-height: 100% !important;
}

.mr-50 {
  margin-right: 50px;
}

.mr-100 {
  margin-right: 100px;
}
