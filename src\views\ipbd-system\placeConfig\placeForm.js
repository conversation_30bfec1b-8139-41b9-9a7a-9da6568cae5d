/*
 * @Date: 2025-01-22 16:16:44
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-22 16:33:55
 * @FilePath: \icbd-view\src\views\ipbd-system\basicsConfig\juenileForm.js
 */
export const placeFormData = {
  // 报警配置
  alarmLevelConfig: [
    {
      alarmColour: "",
      alarmLevel: 0,
      alarmLevelName: "",
      defaultUrl: "",
      isNotification: "",
      isSound: "",
      soundName: "",
      soundType: "",
      soundUrl: "",
    },
  ],
  // 重点部位实时监测设备
  importPartMonitorConfigVo: {
    deviceIds: [],
    pollingInterval: 0
  },
  // 疑似重点人聚集场所
  emphasisPersonGatheredPlaceConfigVo: {
    gatheredEmphasisPersonNumber: 0,
    timeInterval: 0
  },
  // 场所事件积分规则
  placeEventScoreConfigVo: {
    findEmphasisPersonScore: 0,
    emphasisPersonGatheredScore: 0,
    placeAbnormalBehaviorScore: 0,
    highAttentionScore: 0,
    midAttentionScore: 0,
    analysisDayNumber: 0
  },
  // 疑似工作人员
  placeWorkPersonConfigVo: {
    placeId: [],
    analysisDayNumber: 0,
    appearDayNumber: 0,
    appearTotalNumber: 0,
    appearDayNumberAndAppearTotalNumberRelation: true
  },
};
