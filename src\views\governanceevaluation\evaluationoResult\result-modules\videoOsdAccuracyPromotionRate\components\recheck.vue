<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" @query="handleSubmit" @onCancel="handleCancel">
    <Form ref="formValidate" class="form-content" :model="formValidate" :rules="ruleValidate" :label-width="180">
      <FormItem label="复检设备" v-if="isBatchRecheck">
        <RadioGroup v-model="formValidate.model">
          <Radio v-for="(modelItem, modelIndex) in modelList" :key="modelIndex" :label="modelItem.dataKey">
            <span>{{ modelItem.dataValue }}</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="" v-if="formValidate.model === 'CUSTOM'">
        <choose-recheck-device
          :tableColumns="tableColumns"
          :formModel="formModel"
          :regionCode="paramsList[this.codeKey]"
          @getDeviceQueryForm="getDeviceQueryForm"
        >
          <template slot="qualified" slot-scope="{ row }">
            <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
              {{ qualifiedColorConfig[row.qualified].dataValue }}
            </Tag>
            <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
          </template>
          <template #deviceId="{ row }">
            <span :class="row.rowClass">{{ row.deviceId }}</span>
          </template>
          <template #delaySipMillSecond="{ row }">
            <span>
              {{ !row.delaySipMillSecond ? '--' : row.delaySipMillSecond }}
            </span>
          </template>
          <template #phyStatus="{ row }">
            <span>
              {{ !row.phyStatus ? '--' : row.phyStatusText }}
            </span>
          </template>
          <template #delayStreamMillSecond="{ row }">
            <span>
              {{ !row.delayStreamMillSecond ? '--' : row.delayStreamMillSecond }}
            </span>
          </template>
          <template #delayIdrMillSecond="{ row }">
            <span>
              {{ !row.delayIdrMillSecond ? '--' : row.delayIdrMillSecond }}
            </span>
          </template>
          <template #videoStartTime="{ row }">
            <span>
              {{ !row.videoStartTime ? '--' : row.videoStartTime }}
            </span>
          </template>
          <!-- 在线状态 -->
          <template #online="{ row }">
            <span
              :class="{
                color_qualified: row.online === '1',
                color_unqualified: row.online === '2',
              }"
            >
              {{ row.online === '1' ? '在线' : row.online === '2' ? '离线' : '' }}
            </span>
          </template>
          <!-- 完好状态 -->
          <template #normal="{ row }">
            <span
              :class="{
                color_qualified: row.normal === '1',
                color_unqualified: row.normal === '2',
              }"
            >
              {{ row.normal === '1' ? '取流及时响应' : row.normal === '2' ? '取流超时响应' : '' }}
            </span>
          </template>
          <!-- 可用状态 -->
          <template #canPlay="{ row }">
            <span
              :class="{
                color_qualified: row.canPlay === '1',
                color_unqualified: row.canPlay === '2',
              }"
            >
              {{ row.canPlay === '1' ? '取流成功' : row.canPlay === '2' ? '取流失败' : '' }}
            </span>
          </template>

          <template #reason="{ row }">
            <Tooltip :content="row.reason" transfer max-width="150">
              {{ row.reason }}
            </Tooltip>
          </template>
          <template #networkingQuality="{ row }">
            <span
              class="check-status"
              :class="[
                row.networkingQuality === '1' ? 'bg-b77a2a' : '',
                row.networkingQuality === '2' ? 'bg-17a8a8' : '',
                row.networkingQuality === '3' ? 'bg-D66418' : '',
              ]"
            >
              {{ getNetworkingQualityDesc(row.networkingQuality) }}
            </span>
          </template>
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
        </choose-recheck-device>
      </FormItem>
      <FormItem label="复检计划" v-if="isBatchRecheck">
        <RadioGroup v-model="formValidate.plan">
          <Radio v-for="(planItem, planIndex) in planList" :key="planIndex" :label="planItem.dataKey">
            <span>{{ planItem.dataValue }}</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="formValidate.plan === '2'"
        label=""
        prop="scheduletime"
        :rules="[
          {
            required: formValidate.plan === '2' ? true : false,
            message: '请选择复检时间',
            trigger: 'change',
            type: 'date',
          },
        ]"
      >
        <DatePicker
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          v-model="formValidate.scheduletime"
          placeholder="请选择复检时间"
          style="width: 180px"
          @on-change="handleChangeTime"
        ></DatePicker>
      </FormItem>
      <FormItem label="检测方法" :prop="checkMethodsKey" key="'isShowOsdModel'">
        <Select
          v-model="formValidate[checkMethodsKey]"
          placeholder="请选择"
          transfer
          class="width-input"
          @on-change="handleCheckMethods"
        >
          <template v-for="e in checkMethodsWay">
            <Option :value="e.dataKey" :key="e.dataKey">{{ e.dataValue }} </Option>
          </template>
        </Select>
      </FormItem>
      <FormItem class="check-content180" prop="detectionMode">
        <label
          name="label"
          class="check-content-label"
          :style="{
            width: '180px',
          }"
        >
          <Tooltip placement="top-start" class="tooltip-sample-graph">
            <i class="icon-font icon-wenhao f-16" :style="{ color: 'var(--color-warning)' }"></i>
            <div slot="content">
              <div class="check-content-img">
                <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
              </div>
            </div>
          </Tooltip>
          检测内容
        </label>
        <CheckboxGroup class="check-inline" v-model="formValidate.detectionMode" @on-change="checkGroupChange">
          <Checkbox
            v-for="(checkItem, checkIndex) in detectionRules"
            :key="'check' + checkIndex"
            :label="checkItem.value"
            :disabled="!!checkItem.disabled ? true : false"
          >
            <span v-if="checkItem.label">{{ checkItem.label }}:</span>{{ checkItem.text }}
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="设备与标准时间允许的偏差" prop="timeDelay">
        <Input class="width-input" placeholder="" v-model="formValidate.timeDelay" />
        <span class="params-suffix">秒</span>
      </FormItem>
    </Form>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'recheck',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    moduleData: {
      type: Object,
      default: () => {},
    },
    isBatchRecheck: {
      type: Boolean,
      default: false,
    },
    // taskIndexId: {
    //   type: String,
    //   default: ''
    // },
    tableColumns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      title: '复检',
      styles: {
        width: '4.5rem',
      },
      paramsList: {},
      codeKey: '',
      formValidate: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '', // 复检计划
        detectionMode: [], // 检测内容
      },
      ruleValidate: {},
      detectionRules: [
        { label: '在线率', text: '设备在国标平台为在线状态', value: '1' },
        { label: '完好率', text: '设备在线，拉流请求有响应', value: '2' }, // , text2: '秒内有响应', isTime: true,
        { label: '可用率', text: '成功接收到实时视频流', value: '3' },
        { label: '联网质量', text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时', value: '4' },
      ],
      modelList: [
        { dataKey: 'ALL', dataValue: '全部已检测设备' },
        { dataKey: 'UNQUALIFIED', dataValue: '检测不合格设备' },
        { dataKey: 'CUSTOM', dataValue: '自定义' },
      ],
      schedule: '',
      planList: [
        { dataKey: '1', dataValue: '立即复检  ' },
        { dataKey: '2', dataValue: '自定义时间' },
      ],
      formModel: '',
      checkMethodsKey: 'osdModel',
      checkMethodsWay: [],
      detectionRules: [
        { label: '', text: '(右上角) 时间信息位置是否正确', value: 'timeLocation' },
        { label: '', text: '(右下角) 辖区和地址信息是否正确', value: 'areaLocation' },
        { label: '', text: '(左下角) 摄像机信息是否正确', value: 'cameraInfo' },
      ],
    };
  },
  created() {
    this.checkMethodsWay = this.odsCheckModelList;
  },
  methods: {
    getDeviceQueryForm({ ids, totalCount, params, ...deviceQueryForm }) {
      this.deviceQueryForm = deviceQueryForm;
      this.ids = ids;
    },
    handleChangeTime(date) {
      this.schedule = date;
    },
    async handleSubmit() {
      try {
        const params = {
          batchId: this.paramsList.batchId,
          extensionData: {
            customConfig: this.deviceQueryForm,
            indexConfig: {
              regionCode: this.paramsList[this.codeKey],
              detectionMode: this.formValidate.detectionMode,
              // isScreenshots: this.formValidate.isScreenshots,
              // indexDetectionMode: this.formValidate.indexDetectionMode,
              // useRecentCache: "true",
            },
            reinspect: {
              model: this.formValidate.model || 'CUSTOM',
              plan: this.formValidate.plan || '1',
              type: this.isBatchRecheck ? 'PROGRAM_BATCH' : 'PROGRAM_SINGLE', // MANUAL,PROGRAM_SINGLE,PROGRAM_BATCH
            },
          },
        };
        // if(formValidate.detectionMode.includes('4')){
        //   params.extensionData.indexConfig = Object.assign(params.extensionData.indexConfig, {
        //     delayIdrTimeOut: this.formValidate.delayIdrTimeOut,
        //     delaySipTimeOut: this.formValidate.delaySipTimeOut,
        //     delayStreamTimeOut: this.formValidate.delayStreamTimeOut,
        //   })
        // }
        if (this.isBatchRecheck) {
          params.extensionData.reinspect.plan = this.formValidate.plan;
          params.extensionData.reinspect.schedule = this.schedule;
          params.extensionData.reinspect.ids = this.ids;
        }
        await this.$http.post(evaluationoverview.programRecheck, params);
        this.$Message.success('复检操作成功！');
        this.$emit('handleUpdate');
        this.handleCancel();
      } catch (e) {
        console.log(e);
      }
    },
    handleCancel() {
      this.formValidate = {
        model: '', // 复检设备
        plan: '', // 复检计划
        detectionMode: [], // 检测内容
        // indexDetectionMode: '', // 指标取值
        // scheduletime: '', // 自定义时间
        // isScreenshots: '2', // 需要视频截图
      };
      this.schedule = '';
      this.visible = false;
    },
    checkGroupChange(val) {
      if (!val.length || !!this.osdType.includes(this.moduleData.indexType)) {
        this.formValidate.indexDetectionMode = '';
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b); // 从小到大排序
      // 找出最大值进行遍历（第一项单独操作不联动）
      const sortMax = Number(sortDetectionMode[sortDetectionMode.length - 1]);
      if (sortMax > 1) {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 1 : 2;
        for (let mx = startIndex; mx < sortMax; mx++) {
          if (!sortDetectionMode.includes(mx.toString())) {
            let mode = this.$util.common.deepCopy(this.formValidate.detectionMode);
            mode.push(mx.toString());
            this.$set(this.formValidate, 'detectionMode', mode);
          }
        }
      }
      if (!val.includes('3')) {
        this.$set(this.formValidate, 'isScreenshots', '2');
      } else {
        this.$set(this.formValidate, 'isScreenshots', '1');
      }
      this.formValidate.indexDetectionMode = val.includes(this.oldIndexDetectionMode) ? this.oldIndexDetectionMode : '';
    },
  },
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
    }),
  },
  watch: {
    value(val) {
      this.visible = val;
      // if (!!val) {
      //   this.paramsList = this.$route.query
      //   if (!this.paramsList || !this.paramsList.batchId) return false
      //   this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode'
      //   if (this.taskIndexId !== '') this.getConfig()
      //   this.getConfig()
      // }
    },
    visible(val) {
      this.$emit('input', val);
    },
    // 'formValidate.detectionMode'(val) {
    //   // 不需要禁掉的指标: 通字幕标注合规率、重点字幕标注合规率
    //   if (!val || !!this.osdType.includes(this.indexType)) return false
    //   this.handleCheckDiabled(val)
    // },
  },
  components: {
    ChooseRecheckDevice: require('../../../components/choose-recheck-device/choose-recheck-device').default,
    // TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>

<style lang="less" scoped>
.form-content {
  //padding-left: 60px;
  /deep/.width-input {
    width: 380px;
    .ivu-input-group-append {
      background-color: #02162b;
      border: 1px solid #10457e;
      border-left: none;
    }
  }
  .params-suffix {
    color: #fff;
    margin-left: 5px;
  }
  /deep/.ivu-form-item {
    .ivu-form-item-content {
      .ivu-checkbox-group {
        display: flex;
        flex-direction: column;
        .ivu-checkbox-wrapper {
          display: flex;
          align-items: center;
          > span {
            margin-right: 10px;
          }
        }
      }
      .ivu-radio-group {
        .ivu-radio-group-item {
          margin-right: 30px;
          > span {
            margin-right: 10px;
          }
        }
      }
    }
  }
}
.check-content180 {
  @{_deep} .ivu-form-item-content {
    margin-left: 0 !important;
  }
  @{_deep} .ivu-form-item-error-tip {
    margin-left: 180px !important;
  }
}
.check-content-label {
  //margin-left: 0;
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #ffffff;
  line-height: 1;
  padding: 10px 12px 10px 0;
  box-sizing: border-box;
  @{_deep} .ivu-tooltip {
    position: relative;
    &-inner {
      width: 400px;
    }
    &-popper {
      left: -13px !important;
    }
  }
}
.check-inline {
  display: inline-block !important;
}
</style>
