<template>
  <section class="main-container">
    <div class="search-bar">
      <search-face
        ref="searchBar"
        @search="searchHandle"
        @reset="resetHandle"
        @imgChange="imgChange"
      />
    </div>
    <div class="table-container">
      <!-- 没数据时不展示工具栏 -->
      <div
        class="data-export"
        v-if="this.queryParam.algorithmSelect.length < 2 && dataList.length"
      >
        <Checkbox @on-change="checkAllHandler" v-model="checkAll"
          >全选</Checkbox
        >
        <div class="export-box" v-if="!mapOnData">
          <Button class="mr" @click="handleExport($event)" size="small">
            <ui-icon type="daoru" color="#2C86F8"></ui-icon>
            导出
          </Button>
          <exportBox
            ref="exportbox"
            v-if="exportShow"
            @confirm="confirm"
            @cancel="exportShow = false"
          ></exportBox>
        </div>
        <Button
          class="mr"
          :type="queryParam.sortField == 'similarity' ? 'primary' : 'default'"
          @click="handleSort('similarity')"
          size="small"
          v-if="this.queryParam.features && this.queryParam.features.length > 0"
        >
          <!-- <ui-icon type="daoru" color="#2C86F8"></ui-icon> -->
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
        <Button
          class="mr"
          :type="queryParam.sortField == 'absTime' ? 'primary' : 'default'"
          @click="handleSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button @click="dataAboveMapHandler" size="small" style="float: right">
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
        <Button
          @click="handleFalls"
          size="small"
          style="float: right; margin-right: 10px"
        >
          {{ fallsPage ? "传统翻页版本" : "瀑布流版本" }}
        </Button>
      </div>
      <div
        class="table-box"
        v-if="
          this.queryParam.algorithmSelect &&
          this.queryParam.algorithmSelect.length == 2
        "
      >
        <geling
          class="table-box-geling"
          ref="geling"
          @collection="collection"
          @checkHandler="checkHandler"
          @archivesPage="archivesPage"
          @targetAdd="handleTargetAdd"
          @dataCograph="dataCograph"
          :mapOnData="mapOnData"
          :queryParam="queryParam"
        ></geling>
        <hk
          class="table-box-hk"
          ref="hk"
          @collection="collection"
          @checkHandler="checkHandler"
          @archivesPage="archivesPage"
          @targetAdd="handleTargetAdd"
          @dataCograph="dataCograph"
          :mapOnData="mapOnData"
          :queryParam="queryParam"
        ></hk>
      </div>
      <div class="table-container-box" v-else>
        <div class="table-content" @scroll="handleScroll">
          <div
            class="list-card box-1"
            :class="{ checked: item.isChecked }"
            v-for="(item, index) in dataList"
            :key="index"
          >
            <div class="collection paddingIcon">
              <div class="bg"></div>
              <ui-btn-tip
                class="collection-icon"
                v-if="item.myFavorite == '1'"
                content="取消收藏"
                icon="icon-yishoucang"
                transfer
                @click.native="collection(item, 2)"
              />
              <ui-btn-tip
                class="collection-icon"
                v-else
                content="收藏"
                icon="icon-shoucang"
                transfer
                @click.native="collection(item, 1)"
              />
            </div>
            <Checkbox
              class="check-box"
              v-model="item.isChecked"
              @on-change="(e) => checkHandler(e, index)"
            ></Checkbox>
            <div class="img-content">
              <div class="similarity" v-if="item.idScore">
                <span
                  class="num"
                  :class="{
                    'gerling-num': queryParam.algorithmVendorType == 'GLST',
                  }"
                  v-if="item.idScore"
                  >{{ item.idScore }}%</span
                >
                <!-- <span class="gerling-num" v-if="item.idScore">{{ item.idScore }}%</span> -->
              </div>
              <!-- <span class="num" v-if="item.score">{{ item.score }}%</span> -->
              <template v-if="childDataSourceVal == 2">
                <ui-image
                  :src="item.traitImg"
                  alt="动态库"
                  @click.native="faceDetailFn(item, index)"
                />
              </template>
            </div>
            <!-- 动态库 -->
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.absTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-location"></i>
                </Tooltip>
                <!-- <span class="ellipsis" v-show-tips>{{item.detailAddress}}</span> -->
                <ui-textOver-tips
                  refName="detailAddress"
                  :content="item.deviceName"
                ></ui-textOver-tips>
              </p>
            </div>
            <div class="fast-operation-bar">
              <Poptip trigger="hover" placement="right-start">
                <i class="iconfont icon-gengduo"></i>
                <div class="mark-poptip" slot="content">
                  <p @click="archivesPage(item, 1)">
                    <i class="iconfont icon-renlian1"></i>以图搜图
                  </p>
                  <p @click="archivesPage(item, 2)">
                    <i class="iconfont icon-renlian1"></i>身份核验
                  </p>
                  <p @click="openDirectModel(item)">
                    <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                  </p>
                  <p @click="handleTargetAdd(item)">
                    <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
                  </p>
                  <p @click="handleGaitPage(item)">
                    <i class="iconfont icon-a-lianhe322"></i>人脸搜步态
                  </p>
                </div>
              </Poptip>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 9 - (dataList.length % 9)"
            :key="index + 'demo'"
          ></div>
          <div class="loading-box" v-if="fallsPage">
            <ui-loading v-if="scrollLoading"></ui-loading>
          </div>
          <div class="gobacktop" @click="toTop">
            <div class="top-icon">
              <Icon type="md-download" />
            </div>
            <div class="top-text">
              <p>回到</p>
              <p>顶部</p>
            </div>
          </div>
        </div>
        <ui-empty
          v-if="dataList.length === 0 && !listLoading && !fallsPage"
        ></ui-empty>
        <ui-loading v-if="listLoading"></ui-loading>
        <!-- 分页 -->
        <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[27, 54, 81, 108]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <id-card
      ref="idCard"
      v-if="idCardShow"
      @close="handleClose"
      @targetAdd="handleTargetAdd"
    >
    </id-card>
    <!-- 动态库人脸详情 -->
    <details-face-modal
      v-if="videoShow"
      ref="videoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="videoShow = false"
    ></details-face-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="loadCancel"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="wranModalShow"
      title="提示"
      :r-width="500"
      @onCancel="onCancel"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
    <direction-model ref="directionModel"></direction-model>
  </section>
</template>
<script>
import searchFace from "../components/search-face";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import hlModal from "@/components/modal/index.vue";
import {
  queryFaceRecordSearch,
  faceDownload,
  queryDeviceList,
  taskView,
  getBase,
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { mapMutations, mapGetters, mapActions } from "vuex";
import { myMixins } from "../../components/mixin/index.js";
import geling from "../../components/face/geling";
import hk from "../../components/face/hk";
import exportBox from "../../components/export/export-box.vue";
import idCard from "./idCard-content.vue";
import directionModel from "../components/direction-model";
import axios from "axios";
import util from "@/libs/configuration/util";
import { dateTime } from "@/util/modules/common";
export default {
  name: "faceContent",
  props: {
    // 轨迹中心
    mapOnData: {
      type: Boolean,
      default: false,
    },
    // 首页参数
    indexSearchData: {
      type: Object,
      default: () => {},
    },
  },
  mixins: [myMixins], //全局的mixin
  components: {
    searchFace,
    detailsFaceModal,
    geling,
    hk,
    exportBox,
    hlModal,
    idCard,
    directionModel,
  },
  data() {
    return {
      videoShow: false,
      listLoading: false,
      HKLoading: false,
      idCardShow: false,
      dataList: [],
      HKDataList: [],
      queryParam: {
        sortField: "absTime",
        order: "desc",
        algorithmSelect: [],
      },
      currentIndex: 0,
      pageInfo: {
        page: 1,
        pageNumber: 1,
        pageSize: 27,
      },
      total: 0,
      Hktotal: 0,
      childDataSourceVal: 2, // 子组件数据资源值
      checkAll: false,
      timeUpDown: false,
      similUpDown: false,
      exportShow: false,
      deviceKeyWords: "",
      modalShow: false,
      wranModalShow: false,
      downTaskId: "",
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0,
      scrollLoading: false,
      mayRequest: false,
      fallsPage: false,
    };
  },
  activated() {
    if (this.mapOnData) {
      // 轨迹搜索上图返回加载数据
      this.selectionList = [];
      this.queryList();
    }
  },
  watch: {
    exportShow(val) {
      //点击空白处隐藏
      if (val) {
        document.addEventListener("click", () => {
          this.exportShow = false;
        });
      } else {
        document.addEventListener("click", () => {});
      }
    },
  },
  async mounted() {
    // let { videoIdentity, deviceId } = this.$route.query
    // 一人一档，实名档案跳转
    // if (videoIdentity) {
    // 	this.$refs.searchBar.queryParam.videoIdentity = videoIdentity
    // }
    // 一机一档跳转
    // if (deviceId) {
    // 	this.$refs.searchBar.queryParam.devices = [deviceId]
    // }
    await this.getDictData();
    //#region
    /**
     * 走到了mounted生命周期中，并满足该条件，说明是点击左侧菜单触发的，此时不需要处理路由携带的参数
     * 否则，则表示直接从别的页面跳到该页面，并直接定位到当前组件
     */
    if (this.$route.query.sectionName !== "faceContent") {
      this.queryList();
      return;
    }
    //#endregion
    if (this.$route.query.deviceList) {
      let deviceList = JSON.parse(this.$route.query.deviceList);
      let list = [];
      deviceList.forEach((item) => {
        list.push({ ...item, deviceId: item.deviceGbId, select: true });
      });
      this.$refs.searchBar.selectData(list);
      this.queryList();
    } else if (
      this.$route.query.keyWords &&
      ["rlzp", "clzp", "rtzp", "fjdczp"].includes(this.$route.query.pageName)
    ) {
      this.deviceKeyWords = JSON.parse(this.$route.query.keyWords);
      let timeDate = JSON.parse(this.$route.query.time);
      this.$refs.searchBar.reflectTime(timeDate);
      let params = {
        deviceName: this.deviceKeyWords,
        //   deviceType: 1,
        pageNumber: 1,
        pageSize: 100,
        filter: this.judgeUser,
        orgCodes: [],
      };
      let { data } = await queryDeviceList(params);
      data.entities.map((item) => {
        item.select = true;
      });
      this.$refs.searchBar.selectData(data.entities);
      this.queryList();
    } else if (this.$route.query.deviceInfo) {
      let deviceInfo = JSON.parse(this.$route.query.deviceInfo);
      deviceInfo.deviceId = deviceInfo.deviceGbId;
      this.$refs.searchBar.selectData([{ ...deviceInfo, select: true }]);
      this.queryList();
    } else if (this.$route.query.urlList) {
      // 图片结构化跳转
      let list = JSON.parse(this.$route.query.urlList);
      // let list = this.$route.query.urlList;
      this.$refs.searchBar.urlImgList([list, ""], 1);
      this.queryList();
    } else if (this.$route.query.panorama) {
      let list = this.picData.urlList;
      this.$refs.searchBar.urlImgList(list, 1);
      this.queryList();
    } else if (this.$route.query.imgUrl) {
      const imgUrl = this.$route.query.imgUrl;
      let selectSquare = this.$route.query.selectSquare
        ? { imageUrl: imgUrl, ...JSON.parse(this.$route.query.selectSquare) }
        : null;

      if (!selectSquare) {
        let fileData = new FormData();
        fileData.append("algorithmType", 1);
        fileData.append("fileUrl", imgUrl);
        const res = await picturePick(fileData);
        selectSquare = res?.data?.[0];
      }
      if (selectSquare) {
        const response = await this.getBase64ByImageCoordinate(selectSquare);
        let urlList = {
          fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
          feature: response.data.feature,
          imageBase: response.data.imageBase,
        };
        this.$refs.searchBar.urlImgList([urlList, ""], 1);
        this.queryList();
      } else {
        this.$Message.warning("未提取到特征，暂无查询结果");
      }
    } else if (this.$route.query.videoIdentity) {
      this.$refs.searchBar.queryParam.videoIdentity =
        this.$route.query.videoIdentity;
      this.queryList();
    } else {
      let { list, timeSlot, startDate, endDate, searchSelect } =
        this.classifySearchData;
      if (searchSelect == 1) {
        this.serachDeviceTime(list, timeSlot, startDate, endDate, searchSelect);
      } else {
        this.queryList();
      }
    }
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      picData: "common/getWisdomCloudSearchData",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.id);
    },
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // demos搜索
    async demoList() {
      this.listLoading = true;
      this.dataList = [];
      let demoParams = this.demoParams();
      let params = {
        ...demoParams,
        page: {
          ...this.pageInfo,
        },
      };
      const demoToken = util.common.getdemoToken();
      axios
        .post(
          "http://50.146.187.33:10219/whale-openapi/composite-search/capture/search",
          params,
          {
            headers: { accessToken: demoToken },
          }
        )
        .then((res) => {
          let list = res.data.data.captureGroups[0].captures;
          this.total = list.length;
          list.forEach((item, index) => {
            let glasses =
              item.face.attributeMap && item.face.attributeMap.glasses
                ? item.face.attributeMap.glasses.value
                : "--";
            let mask =
              item.face.attributeMap && item.face.attributeMap.mask
                ? item.face.attributeMap.mask.value
                : "--";
            let obj = {
              idScore: (item.score * 100).toFixed(2),
              traitImg: item.face.image.url || "",
              absTime: dateTime(item.captureTime),
              sceneImg: item.bigImage.url || "",
              deviceName: item.camera.name || "",
              eyeglass: glasses,
              ethnic: "--",
              isCap: "--",
              mask: mask,
              skinColour: "--",
              geoPoint: {
                lat: item.camera.latitude || "",
                lon: item.camera.longitude || "",
              },
            };
            this.dataList.push(obj);
          });
          this.$forceUpdate();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.listLoading = false;
          // this.isActivated = true
        });
    },
    // 厂商一个
    async getDataList(page = 0) {
      this.listLoading = true;
      this.dataList = [];
      let funparams = this.funcParams();
      let params = {
        ...funparams,
        ...this.pageInfo,
      };
      await queryFaceRecordSearch(params)
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.dataList = entities || [];
          // 勾选
          if (this.dataList.length && this.upImageData.length) {
            this.dataList.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.id)) {
                e.isChecked = true;
              }
            });
          }
          if (page == 1) {
            this.$refs.videoDetail.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.videoDetail.nextPage(this.dataList);
          }
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索人脸",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
          this.$forceUpdate();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.listLoading = false;
          // this.isActivated = true
        });
    },
    cardScroll() {
      let funparams = this.funcParams();
      let params = {
        ...funparams,
        ...this.pageInfo,
      };
      queryFaceRecordSearch(params).then((res) => {
        if (res.data.entities.length == 0) {
          this.mayRequest = false;
        } else {
          // 勾选
          if (res.data.entities.length && this.upImageData.length) {
            res.data.entities.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.id)) {
                e.isChecked = true;
              }
            });
          }
          this.dataList.push(...res.data.entities);
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索人脸",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
          if (this.pageInfo.pageNumber <= 3) {
            this.pageInfo.pageNumber += 1;
            this.queryList(false, false, true);
          }
          this.mayRequest = true;
        }
      });
    },
    // 瀑布流展示
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.scrollLoading = true;
      this.dataList = [];
      if (this.fallsPage) {
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 48,
        };
        this.queryList(false, false, true);
      } else {
        this.mayRequest = false;
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 27,
        };
        this.queryList();
      }
    },
    // 查询
    searchHandle() {
      this.pageInfo.pageNumber = 1;
      let queryParam = this.$refs.searchBar.queryParam;
      if (queryParam.idCardNo || queryParam.name) {
        this.idCardShow = true;
        this.$nextTick(() => {
          this.$refs.idCard.searchList({
            idCardNo: queryParam.idCardNo,
            name: queryParam.name,
          });
        });
      } else {
        this.queryList();
      }
    },
    handleClose() {
      this.idCardShow = false;
    },
    // 更新搜索数据
    imgChange(params) {
      this.queryParam = { ...this.queryParam, searchImage: params };
    },
    toTop() {
      let scrollTop = document.querySelector(".table-content");
      scrollTop.scrollTo(0, 0);
    },
    handleScroll() {
      // 距离顶部距离
      let scrollTop = document.querySelector(".table-content").scrollTop;
      // 可视区的高度
      let windowHelght = document.querySelector(".table-content").clientHeight;
      // 滚动条的总高度
      let scrollHeight = document.querySelector(".table-content").scrollHeight;
      // 计算底部距离
      let _dis = scrollTop + windowHelght;
      if (this.pageInfo.pageNumber >= 6) {
        this.scrollLoading = false;
        return;
      }
      if (_dis + 1000 > scrollHeight && this.mayRequest) {
        this.pageInfo.pageNumber += 1;
        this.queryList(false, false, true);
      }
    },
    /**
     *
     * @param {*} page 详情翻页
     * @param {*} noRest 排序
     */
    queryList(page = false, noRest = false, fallsPage = false) {
      this.checkAll = false;
      let queryParam = this.$refs.searchBar.queryParam;
      console.log(queryParam, "queryParam");
      this.childDataSourceVal = queryParam.dataSource;
      this.queryParam = { ...this.queryParam, ...queryParam };
      // 精准搜索处理
      this.queryParam.similarity = this.queryParam.similarity / 100;
      // 处理已选择设备
      var ids = [];
      this.queryParam.selectDeviceList.forEach((item) => {
        ids.push(item.deviceGbId);
      });
      this.queryParam.deviceIds = ids;
      delete this.queryParam.urlList;
      this.childDataSourceVal = this.queryParam.dataSource;
      if (this.compareTime()) {
        return;
      }
      if (this.queryParam.algorithmSelect.length == 2) {
        this.$nextTick(() => {
          if (!noRest) {
            this.queryParam.order = "desc";
            this.queryParam.sortField = "similarity";
          }
          this.$refs.geling.init();
          this.$refs.hk.init();
        });
      } else {
        if (this.queryParam.features.length > 0 && !noRest) {
          this.queryParam.order = "desc";
          this.queryParam.sortField = "similarity";
        }
        if (fallsPage) {
          this.cardScroll();
        } else {
          if (this.queryParam.features.length > 0) {
            this.demoList(page);
          } else {
            this.getDataList(page);
          }
        }
      }
    },
    funcParams() {
      return {
        similarity: this.queryParam.similarity,
        algorithmVendorType: this.queryParam.algorithmVendorType,
        deviceIds: this.queryParam.deviceIds,
        startDate: this.queryParam.startDate,
        endDate: this.queryParam.endDate,
        glasses: this.queryParam.glasses,
        gender: this.queryParam.gender,
        cap: this.queryParam.cap,
        faceMask: this.queryParam.faceMask,
        age: this.queryParam.age,
        dataSource: this.queryParam.dataSource,
        features: this.queryParam.features,
        imageBases: this.queryParam.imageBases,
        labelIds: this.queryParam.labelIds,
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
        videoIdentity: this.queryParam.videoIdentity,
        isDeduplication: this.queryParam.isDeduplication ? 1 : 0,
      };
    },
    demoParams() {
      return {
        objectSerial: "1706019454803",
        serial: "1706019454718",
        search: true,
        searchFilter: this.queryParam.searchFilter,
        searchImage: this.queryParam.searchImage,
        period: {
          endTime: new Date(this.queryParam.endDate).getTime(),
          startTime: new Date(this.queryParam.startDate).getTime(),
          type: 0,
        },
        threshold: this.queryParam.similarity,
        imageSource: 0,
        cameraGroups: [
          {
            selectType: 99,
          },
        ],
        integrated: false,
        searchType: "FACE",
        sort: "SCORE",
      };
    },
    // 排序
    handleSort(val) {
      if (val != this.queryParam.sortField) {
        this.queryParam.sortField = val;
        if (val == "similarity") {
          this.queryParam.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.queryParam.order = this.timeUpDown ? "asc" : "desc";
        }
      } else {
        this.queryParam.sortField = val;
        if (val == "similarity") {
          this.similUpDown = !this.similUpDown;
          this.queryParam.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.timeUpDown = !this.timeUpDown;
          this.queryParam.order = this.timeUpDown ? "asc" : "desc";
        }
      }
      this.getDataList();
    },
    // 导出
    handleExport($event) {
      $event.stopPropagation();
      this.exportShow = !this.exportShow;
    },
    loadCancel() {
      this.modalShow = false;
      this.wranModalShow = true;
    },
    onCancel() {
      this.modalShow = true;
      this.wranModalShow = false;
    },
    onOk() {
      this.modalShow = false;
      this.wranModalShow = false;
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },
    confirm(param) {
      let funparams = this.funcParams();
      let params = {};
      if (param.type == "1") {
        let list = this.dataList.filter((e) => e.isChecked);
        if (list.length > 0) {
          let ids = list.map((item) => item.id);
          params = {
            ids,
            downloadPics: param.downloadPics,
            downloadSize: null,
            ...funparams,
          };
          this.exportShow = false;
          this.modalShow = true;
        } else {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params = {
          ids: [],
          downloadPics: param.downloadPics,
          downloadSize: param.downloadSize,
          ...funparams,
        };
        this.exportShow = false;
        this.modalShow = true;
      }
      faceDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {
          // this.$refs.exportbox.handleEnd();
        });
    },
    /**
     * 重置
     */
    resetHandle() {
      this.$nextTick(() => {
        this.queryParam = {};
        this.pageInfo.pageNumber = 1;
        this.queryParam = {
          ...this.indexSearchData,
          ...this.$refs.searchBar.queryParam,
        };
        this.queryParam.dataSource = 2; // 资源类型
        this.queryParam.similarity = this.queryParam.similarity / 100;
        this.childDataSourceVal = 2;
        this.checkAll = false;
        this.getDataList();
      });
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList(false, true);
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList(false, true);
    },
    /**
     * 首页调用方法
     */
    indexSearchHandle() {
      this.pageInfo.pageNumber = 1;
      this.queryParam = {
        dataSource: 2, // 资源类型
        keyWords: this.indexSearchData.keyWords,
      };
      this.indexSearchData.features = [];
      this.$nextTick(() => {
        this.queryParam = { ...this.queryParam, ...this.indexSearchData };
        this.getDataList();
      });
    },

    faceDetailFn(row, index) {
      this.currentIndex = index;
      this.videoShow = true;
      this.$nextTick(() => {
        var val = this.$refs.searchBar.queryParam.dataSource;
        let collectionType = val == 2 ? 5 : 13;
        this.$refs.videoDetail.init(
          row,
          this.dataList,
          index,
          collectionType,
          this.pageInfo.pageNumber
        );
      });
    },
    /**
     * 跳转到一人一档页面
     */
    archivesPage(item, index) {
      let pageUrl = "";
      if (index == 1) {
        pageUrl =
          "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1";
      } else {
        pageUrl = "/model-market/face-warfare/identity-authentivation?noMenu=1";
      }
      const { href } = this.$router.resolve({
        path: pageUrl,
        query: {
          imgUrl: item.traitImg,
        },
      });
      window.open(href, "_blank");
    },
    checkAllHandler(val) {
      this.dataList = this.dataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    checkHandler(e, i) {
      this.dataList[i].isChecked = e;
      this.checkAll =
        this.dataList.filter((e) => e.isChecked).length === this.dataList.length
          ? true
          : false;
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    dataCograph(list) {
      this.pointCograph(list);
    },
    dataAboveMapHandler() {
      this.pointCograph(this.dataList);
    },
    pointCograph(dataList) {
      // 已上图的不需要再次上图
      let seleNum = dataList.filter(
        (e) => e.isChecked && !this.alreadyUpImageIds.includes(e.id)
      );
      if (!seleNum.length) {
        this.$Message.warning("请选择上图数据");
        return;
      }
      // 判断是否有坐标信息
      let listHasLocation = seleNum.filter((item) => {
        return item.geoPoint && item.geoPoint.lat && item.geoPoint.lon;
      });
      if (!listHasLocation.length) {
        this.$Message.warning("无经纬度信息，无法上图");
        return;
      }
      if (listHasLocation.length < seleNum.length) {
        this.$Message.warning(
          `已过滤${seleNum.length - listHasLocation.length}条无经纬度信息的数据`
        );
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        listHasLocation.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = JSON.stringify(this.getListNum);
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      listHasLocation.map((item) => {
        item.delePoints = true;
        item.deleType = "face";
      });
      let list = listHasLocation.map((item, index) => {
        return {
          age: item.age,
          ageName: item.ageName,
          eyeglass: item.eyeglass,
          isCap: item.isCap,
          mask: item.mask,
          absTime: item.absTime,
          deleType: item.deleType,
          detailAddress: item.detailAddress,
          deviceId: item.deviceId,
          deviceName: item.deviceName,
          driverFlag: item.driverFlag,
          featureId: item.featureId,
          geoPoint: item.geoPoint,
          id: item.id,
          lat: item.lat,
          location: item.location,
          lon: item.lon,
          recordId: item.recordId,
          sceneImg: item.sceneImg,
          sourceId: item.sourceId,
          traitImg: item.traitImg,
          vid: item.vid,
          isChecked: item.isChecked,
          structureTime: item.structureTime,
          infoKind: item.infoKind,
          sbgnlx: item.sbgnlx,
          gender: item.gender,
          ethnic: item.ethnic,
          skinColour: item.skinColour,
        };
      });
      this.$emit("dataAboveMapHandler", {
        type: "face",
        deleIdent: "face-" + this.getListNum,
        list,
      });
    },
    /**
     * 收藏
     */
    collection(data, flag) {
      var val = this.$refs.searchBar.queryParam.dataSource;
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType: val == 2 ? 5 : 13,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$set(data, "myFavorite", "1");
          this.$Message.success("收藏成功");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$set(data, "myFavorite", "2");
          this.$Message.success("取消收藏成功");
        });
      }
    },
    /**
     * 上一个
     */
    prePage(pageNum) {
      if (pageNum < 1) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        if (!this.fallsPage) {
          this.queryList(1, true);
        }
      }
    },
    /**
     * 下一个
     */
    nextPage(pageNum) {
      let num = pageNum - 1; // 这个时候的pageNum已经为下一页的页码了
      let size = this.pageInfo.pageSize;
      if (this.total <= num * size) {
        this.$Message.warning("已经是最后一个了");
      } else {
        if (!this.fallsPage) {
          this.pageInfo.pageNumber = pageNum;
          this.queryList(2, true);
        }
      }
    },
    openDirectModel(row) {
      const { geoPoint, deviceName } = { ...row };
      this.$refs.directionModel.show({ geoPoint, deviceName });
    },
    // 目标添加
    handleTargetAdd(row) {
      // 如果是从身份证 | 姓名搜索页过来的，那么需要关闭该组件
      if (this.idCardShow) {
        this.handleClose();
      }
      let fileData = new FormData();
      fileData.append("algorithmType", 1);
      fileData.append("fileUrl", row.traitImg);
      picturePick(fileData).then(async (res) => {
        if (res.data && res.data.length > 0) {
          const response = await this.getBase64ByImageCoordinate(res.data[0]);
          let urlList = {
            fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
            feature: response.data.feature,
            imageBase: response.data.imageBase,
          };
          this.$refs.searchBar.urlImgList([urlList, ""], 2);
          this.pageInfo = {
            pageNumber: 1,
            pageSize: 27,
          };
          this.queryList();
        } else {
          this.$Message.warning("未识别出人脸！");
        }
      });
    },
    // 根据图片坐标截取图片base64
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },

    // 人脸搜步态
    handleGaitPage(row) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=gaitContent&noMenu=1",
        query: {
          algorithmType: 5,
          imgUrl: row.traitImg,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
.main-container {
  position: relative;
}
.faceDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.data-above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.idCardNo {
  background: rgba(0, 0, 0, 0.7);
  height: 26px;
  line-height: 26px;
  text-align: center;
  margin-top: -26px;
  z-index: 999;
  position: absolute;
  width: 100%;
  color: #2c86f8;
  // font-size: 13px;
  font-weight: 600;
}
</style>
