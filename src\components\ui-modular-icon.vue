<template>
  <div class="icons-sel">
    <Input
      v-model="searchInfo.name"
      placeholder="请输入图标名称"
      class="ui-input"
    />
    <div class="searchIcon" @click="searchIconList">
      <Icons :type="`icon-icon-test3`" :size="21" />
    </div>
    <div class="icons-list-wrap">
      <div v-if="iconList.length > 0">
        <div class="icons-list">
          <div
            class="icon"
            v-for="(e, i) in iconList"
            :key="e.icon_id"
            @click="selectIcon(e, i)"
          >
            <!-- <i :class="`iconfontconfigure icon-${e.font_class}`"></i> -->
            <ui-icon :type="e.icon" :color="e.color"></ui-icon>
            <div class="name">{{ e.text }}</div>
            <Icons
              class="active"
              :type="'icon-check'"
              :size="8"
              color="#FFF"
              v-show="selectIconInfo.font_class === e.font_class"
            />
          </div>
        </div>
        <div class="ui-page">
          <Page
            :total="total"
            :current="pageInfo.pageNum"
            :page-size="pageInfo.pageSize"
            @on-change="changePage"
            size="small"
          />
        </div>
      </div>
      <div class="noData" v-else>
        <img src="@/assets/img/empty-page/null_main_icon.png" alt="" />
        <p>抱歉~没有搜索到相关内容</p>
      </div>
    </div>
  </div>
</template>
<script>
import Icons from "@/components/icons/index.js";
import { placeType } from "@/map/core/enum/LayerType.js";

export default {
  name: "modular-icon",
  data() {
    return {
      searchInfo: {
        name: "",
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 18,
      },
      total: 0,
      iconList: [],
      selectIconInfo: {
        font_class: "",
        id: "",
      },
      iconsDataList: [],
    };
  },
  components: {
    Icons,
  },
  mounted() {
    const arr = [];
    Object.keys(placeType).forEach((item) => {
      arr.push({
        ...placeType[item],
        icon: item,
      });
    });
    this.iconsDataList = arr;
    this.init("");
  },
  methods: {
    init(val) {
      this.searchInfo.name = "";
      this.selectIconInfo = {
        font_class: "",
        color: "",
        id: "",
      };
      this.pageInfo = {
        pageNum: 1,
        pageSize: 21,
      };
      if (val) {
        let selectIconItem = this.iconsDataList.find((v) => {
          return v.icon_id === val;
        });
      }
      this.getIconList();
    },
    searchIconList() {
      this.pageInfo.pageNum = 1;
      this.getIconList();
    },
    getIconList() {
      this.total = this.iconsDataList.length; // 符合条件的总条数
      this.iconList = this.pageBySize(
        [...this.iconsDataList],
        this.pageInfo.pageNum,
        this.pageInfo.pageSize
      );
    },
    pageBySize(arr, page, size) {
      return arr.splice((page - 1) * size, size);
    },
    changePage(val) {
      this.pageInfo.pageNum = val;
      this.getIconList();
    },
    selectIcon(e) {
      if (e) {
        this.selectIconInfo = {
          font_class: e.icon,
          color: e.color || "",
        };
      }
      this.$emit("on-select-icon", this.selectIconInfo);
    },
  },
};
</script>
<style lang="less">
.icons-sel {
  position: relative;
  padding: 0px 72px 0px 91px;

  .ui-input {
    width: 420px;
    position: relative;
  }

  .searchIcon {
    position: absolute;
    right: 100px;
    top: 32px;
    cursor: pointer;
    i {
      color: var(--pageSelectedColor) !important;
    }
  }

  .icons-list-wrap {
    display: flex;
    position: relative;
  }

  .icons-list {
    margin-top: 20px;
    margin-bottom: 30px;
    width: 100%;
    overflow: hidden;
    box-sizing: border-box;
    height: 188px;
    .icon {
      display: inline-block;
      width: 60px;
      height: 57px;
      padding-top: 3px;
      color: var(--fontColor);
      border-radius: 4px;
      margin-right: 10px;
      margin-bottom: 10px;
      text-align: center;
      box-sizing: border-box;
      flex: none;
      cursor: pointer;
      position: relative;

      .name {
        width: 60px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .iconfontconfigure {
        font-size: 18px;
      }
    }

    .icon:hover {
      background: rgba(3, 124, 190, 0.2);
    }

    .active {
      position: absolute;
      left: 5px;
      top: 7px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: var(--hoverColor);
    }
  }

  .noData {
    height: 240px;
    text-align: center;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--pageSelectedColor);

    img {
      width: 220px;
      height: 220px;
      content: var(--searchEmptyBg);
    }

    p {
      margin-top: -20px;
    }
  }

  .icons-list::after {
    display: block;
    content: "";
    width: 30%;
    height: 0px;
  }

  .ui-page {
    position: absolute;
    bottom: -10px;
    right: 15px;
  }
}

.icons-sel::before {
  content: "";
  width: 520px;
  height: 1px;
  left: 20px;
  top: 0px;
  background-color: var(--bordeColor);
  position: absolute;
}
</style>
