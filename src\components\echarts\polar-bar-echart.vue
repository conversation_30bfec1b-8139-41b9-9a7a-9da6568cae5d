<template>
  <div class="echart-wrap">
    <div v-show="title.show" class="echart-text title-color">
      {{ title.text }}
    </div>
    <div v-show="title.show" class="echart-subtext auxiliary-color">
      {{ title.subtext }}
    </div>
    <div ref="echart" class="echart-content"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  props: {
    title: {
      type: Object,
      default: () => {
        return {
          show: true,
          text: "活动时段分布图",
          subtext: "单位：次/小时",
        };
      },
    },
    grid: {
      type: Object,
      default: () => {
        return {
          left: "0",
          top: "22%",
          right: "0",
          bottom: "0",
          containLabel: true,
        };
      },
    },
    angleAxis: {
      type: Object,
      default: () => {
        return {
          type: "category",
          data: [
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
            19, 20, 21, 22, 23,
          ],
          startAngle: 90,
          boundaryGap: true,
          axisLine: {
            lineStyle: {
              color: "rgba(211, 215, 222, 1)",
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "rgba(0, 0, 0, 0.35)",
            fontSize: 12,
            align: "left",
            // verticalAlign: 'bottom'
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: "rgba(211, 215, 222, 1)",
            },
          },
        };
      },
    },
    radiusAxis: {
      type: Object,
      default: () => {
        return {
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: "rgba(211, 215, 222, 1)",
            },
          },
        };
      },
    },
    polar: {
      type: Object,
      default: () => {
        return {
          radius: ["0%", "78%"],
        };
      },
    },
    tooltip: {
      typeof: Object,
      default: () => {
        return {
          show: true,
          borderColor: "rgba(0, 0, 0, 0)",
        };
      },
    },
    series: {
      type: Array,
      default: () => {
        return [
          {
            type: "bar",
            data: [
              {
                value: 1,
                itemStyle: {
                  color: "#F29F4C",
                },
              },
              {
                value: 0,
              },
              {
                value: 2,
                itemStyle: {
                  color: "#2C86F8",
                },
              },
            ],
            coordinateSystem: "polar",
            stack: "a",
            barCategoryGap: "0%",
          },
        ];
      },
    },
  },
  data() {
    return {
      color: ["red"],
      // color: [ '#EC9240'],
      // color: [ '#EC9240', '#2C86F8'],
      // color: [  '#2C86F8','#EC9240'],
      myEchart: null,
    };
  },
  mounted() {
    this.init();
  },
  deactivated() {
    this.removeResizeFun();
  },
  beforeDestroy() {
    this.removeResizeFun();
  },
  watch: {
    series: {
      deep: true,
      handler(val) {
        this.init();
      },
    },
  },
  methods: {
    init() {
      this.myEchart = echarts.init(this.$refs.echart);
      let option = {
        // title: this.title,
        grid: this.grid,
        angleAxis: this.angleAxis,
        radiusAxis: this.radiusAxis,
        polar: this.polar,
        tooltip: this.tooltip,
        series: this.series,
      };
      this.myEchart.setOption(option);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
    removeResizeFun() {
      window.removeEventListener("resize", () => this.myEchart.resize());
    },
  },
};
</script>
<style lang="less" scoped>
.echart-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .echart-text {
    font-size: 14px;
    font-family: "MicrosoftYaHei-Bold";
    font-weight: bold;
    line-height: 20px;
    text-align: center;
  }
  .echart-subtext {
    margin-top: 8px;
    font-size: 12px;
    line-height: 18px;
  }
  .echart-content {
    flex: 1;
  }
}
</style>