/**
 * 两个版本的nav-left
 */
import { fontSize } from '@/util/module/common.js';
let fontSize80 = fontSize(80);
let fontSize50 = fontSize(50);
let fontSize200 = fontSize(200);
let fontSize800 = fontSize(800);

export const LayoutComponentsEnum = {
  horizon: {
    componentName: 'NavLeftHorizon',
    style: {
      width: `${fontSize200}px !important`,
      minWidth: `${fontSize200}px !important`,
      maxWidth: `${fontSize200}px !important`,
      flex: `0 0 ${fontSize200}px !important`,
    },
    iconStatuStyle: {
      width: `${fontSize50}px !important`,
      minWidth: `${fontSize50}px !important`,
      maxWidth: `${fontSize50}px !important`,
      flex: `0 0 ${fontSize50}px !important`,
    },
    // 点击收起 - 展示图标不隐藏
    isExpandShow: true,
  },
  normal: {
    componentName: 'NavLeft',
    style: {
      width: `${fontSize80}px !important`,
      minWidth: `${fontSize80}px !important`,
      maxWidth: `${fontSize800}px !important`,
      flex: `0 0 ${fontSize80}px !important`,
    },
    // 点击收起 - 直接隐藏
    isExpandShow: false,
  },
};
