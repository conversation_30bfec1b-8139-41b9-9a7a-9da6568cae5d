<template>
  <div class="camera-module">
    <ui-modal v-model="visible" title="配置各单位工单接收人" width="62.5rem" @query="query" @onCancel="cancel">
      <p class="ml-lg mb-sm color-warning">
        备注：每个单位选择一个工单接收人，系统自动根据设备所属单位将工单指派给对应接收人！未指派接收人的单位的工单，则自动指派给上级单位接收人
      </p>
      <Row class="camera-content">
        <Col :span="6" class="tree height-full">
          <ui-search-tree
            ref="uiSearchTree"
            placeholder="请输入组织机构名称"
            class="ui-search-tree"
            :check-strictly="true"
            :show-checkbox="true"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-checked-keys="defaultExpandedKeys"
            :current-node-key="getDefaultSelectedOrg.orgCode"
            :default-keys="defaultExpandedKeys"
            :node-key="nodeKey"
            :showCheckbox="false"
            @selectTree="selectTree"
          >
            <template #label="{ data }">
              <span>{{ data.orgName }}</span>
              <span class="icon-font icon-chenggong1 f-14 ml-sm color-success" v-show="data.check"></span>
            </template>
          </ui-search-tree>
        </Col>
        <Col :span="9" class="camera-list height-full">
          <div class="select-top">
            <div class="fl">
              <span>
                <Input search placeholder="请输入姓名" class="width-md" v-model="searchText" />
              </span>
            </div>
          </div>
          <div
            class="height-full pt-sm select-content"
            v-scroll="335"
            v-ui-loading="{ loading: loading, tableData: computedPeopleList }"
          >
            <div
              v-for="(item, index) in computedPeopleList"
              :key="index"
              class="mr-sm mb-sm secondary fl people-item"
              @click="selectPeople(item)"
              :class="{ active: !!item.active }"
            >
              {{ item.name }}
            </div>
          </div>
        </Col>
        <Col :span="9" class="camera-list height-full">
          <div class="selected-title over-flow">
            <span class="font-color fl">
              已选（<span class="font-red">{{ selectedPeople.length }}</span
              >人）
            </span>
            <span class="link-text-box fr pointer" @click="unSelectAll">清空</span>
          </div>
          <div class="selected-list" v-scroll="350">
            <div v-for="(item, index) in selectedPeople" :key="index">
              <div class="over-flow selected-item font-color">
                <span class="inline selected-orgname ellipsis vt-middle mr-sm" :title="item.orgName">{{
                  item.orgName
                }}</span>
                <span>
                  <span class="inline name ellipsis vt-middle" :title="item.name">{{ item.name }}</span>
                  <span v-if="!!item.phoneNumber" class="inline vt-middle">（{{ item.phoneNumber }}）</span>
                </span>

                <span class="close fr font-red inline vt-middle remove" @click="unSelect(item, index)">移除</span>
              </div>
            </div>
          </div>
        </Col>
      </Row>
      <div class="config-box">
        <RadioGroup v-model="configData.dataType">
          <Radio class="ml-lg" :label="item.value" v-for="item in orderReceiveList" :key="item.value">
            <span>{{ item.desc }}</span>
          </Radio>
        </RadioGroup>
      </div>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.camera-module {
  @{_deep} .ivu-modal-body {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
  .camera-content {
    border-top: 1px solid var(--border-modal-footer);
    height: 650px;
    .tree {
      padding: 10px 20px 10px;
      @{_deep}.el-tree {
        //padding-top: 20px;
      }
    }
    .camera-list {
      border-left: 1px solid var(--border-modal-footer);
      .select-top {
        overflow: hidden;
        padding: 0 15px;
        line-height: 50px;
        border-bottom: 1px solid var(--border-modal-footer);
      }
      .people-item {
        padding: 5px 10px;
        color: var(--color-btn-text);
        border: 1px solid var(--border-btn-default);
        border-radius: 4px;
        cursor: pointer;
        &.active {
          color: #fff;
          background-color: var(--color-btn-dashed);
        }
        &:hover {
          color: #fff;
          background-color: var(--color-btn-dashed-hover);
        }
      }

      .selected-title {
        padding: 0 20px;
        height: 50px;
        line-height: 50px;
        border-bottom: 1px solid var(--border-modal-footer);
      }
      .selected-list {
        padding: 5px 0;
        .close {
          cursor: pointer;
        }

        .selected-item {
          padding: 5px 20px;
          &:hover {
            background-color: var(--border-modal-footer);
          }
          .selected-orgname {
            width: 100px;
          }
          .name {
            max-width: 150px;
          }
          .remove {
            width: 50px;
          }
        }
      }
    }
  }
  .select-content {
    padding: 20px;
  }
  .font-color {
    color: var(--color-content);
  }
  .config-box {
    border-top: 1px solid var(--border-modal-footer);
    padding: 15px;
  }
}
</style>
<script>
import { mapGetters } from 'vuex';
import user from '@/config/api/user';
import governancetask from '@/config/api/governancetask';
import home from '@/config/api/home';
export default {
  data() {
    return {
      visible: false,
      loading: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      defaultCheckedKeys: '',
      nodeKey: 'orgCode',
      peopleList: [],
      viewPeopleList: [], //当摄像头过多的时候不要显示所有的摄像头只显示该数组的摄像头
      initPeopleList: [], //搜索时摄像头临时存储列表 当搜空的时候 返回所有的摄像头
      searchText: '',
      peopleSliceNum: 200,
      selectedPeople: [],
      currentOrgObj: {},
      orderReceiveList: Object.freeze([
        {
          value: 'PROVINCE',
          desc: '省厅下级工单统一指派给省厅用户',
        },
        {
          value: 'CITY',
          desc: '市局下级工单统一指派给市局用户',
        },
        {
          value: 'COUNTY',
          desc: '分局下级工单统一指派给分局用户',
        },
      ]),
      configKey: 'WORK_ORDER_USER_CONFIG',
      configData: {
        dataType: null,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    async updateAssignList() {
      try {
        const params = this.selectedPeople.map((item) => {
          return {
            assignId: item.username,
            assignName: item.name,
            orgCode: item.orgCode,
            orgName: item.orgName,
            orgId: item.orgId,
          };
        });
        const { data } = await this.$http.post(governancetask.updateAssignList, params);
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      }
    },
    unSelectAll() {
      this.peopleList.forEach((row) => {
        row.active = false;
      });
      this.selectedPeople = [];
      this.setTreeData(this.$refs.uiSearchTree.treeData);
    },
    unSelect(item, index) {
      let people = this.peopleList.find(
        (value) => value.username === item.username && value.orgList.includes(item.orgId),
      );
      if (people) {
        this.$set(people, 'active', false);
      }
      this.selectedPeople.splice(index, 1);
      this.setTreeData(this.$refs.uiSearchTree.treeData);
    },
    //监听滑动距离加载隐藏的摄像机列表
    listenScroll() {
      let box = this.$refs.cameraBox;
      box.addEventListener(
        'scroll',
        () => {
          // console.log(box.scrollTop + box.clientHeight === box.scrollHeight);
          //  判断是否滚动到底部，如果滚动到底部则则再加载200个摄像头
          if (box.scrollTop + box.clientHeight === box.scrollHeight) {
            this.peopleSliceNum += 200;
          }
        },
        false,
      );
    },
    async getOrgPoliceman(checkedData) {
      try {
        this.loading = true;
        let res = await this.$http.post(user.queryUserList, {
          orgIdList: checkedData,
        });
        this.peopleList = res.data.data;
        this.setCheckedReceiver();
        if (this.peopleList.length > 200) {
          this.listenScroll();
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    setCheckedReceiver() {
      let { id } = this.currentOrgObj;
      this.peopleList.forEach((item) => {
        let index = this.selectedPeople.findIndex((value) => value.username === item.username && value.orgId === id);
        if (index !== -1) {
          this.$set(item, 'active', true);
        }
      });
    },
    selectTree(val) {
      this.currentOrgObj = val;
      let { id } = val;
      this.getOrgPoliceman([id]);
    },
    query() {
      if (!this.selectedPeople.length) return this.$Message.error('请选择工单接收人');
      this.visible = false;
      this.$emit('on-select-receiver', this.selectedPeople);
      //后期优化为不用$emit 直接保存
      this.updateAssignList();
      // 保存配置
      this.saveConfig();
    },
    cancel() {
      this.visible = false;
    },
    selectPeople(item) {
      let { orgCode, orgName, id } = this.currentOrgObj;
      let index = this.selectedPeople.findIndex((value) => value.username === item.username && value.orgId === id);
      if (index === -1) {
        let selectPeopleList = this.peopleList.filter((value) => value.active);
        if (selectPeopleList.length > 0) {
          return this.$Message.error('每单位只能配置一个工单接收人');
        }
        this.$set(item, 'active', !item.active);
        this.$set(this.currentOrgObj, 'check', item.active);
        this.selectedPeople.push({
          ...item,
          orgCode,
          orgName,
          orgId: id,
        });
      } else {
        this.$set(item, 'active', !item.active);
        this.$set(this.currentOrgObj, 'check', item.active);
        this.selectedPeople.splice(index, 1);
      }
    },
    reset() {
      this.selectedPeople = [];
    },
    // 获取已配置的单位工单接收人
    async getDeviceWorkOrderOrgCode() {
      try {
        let {
          data: { data },
        } = await this.$http.post(governancetask.getDeviceWorkOrderOrgCode, {});
        this.selectedPeople = data.map((item) => {
          return {
            username: item.assignId,
            name: item.assignName,
            orgCode: item.orgCode,
            orgName: item.orgName,
            orgId: item.orgId,
          };
        });
      } catch (e) {
        console.log(e);
      }
    },
    setTreeData(data) {
      data.forEach((item) => {
        let select = this.selectedPeople.find((value) => value.orgId === item.id);
        this.$set(item, 'check', !!select);
        if (item.children) {
          this.setTreeData(item.children);
        }
      });
    },
    // 配置单位
    async getConfig() {
      try {
        let params = {
          key: this.configKey,
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = data?.paramValue ? JSON.parse(data.paramValue) : null;
        this.configData = paramValue;
      } catch (err) {
        console.log(err);
      }
    },
    async saveConfig() {
      try {
        let params = {
          paramKey: this.configKey,
          paramValue: JSON.stringify(this.configData),
          paramType: 'ivdg',
        };
        await this.$http.put(home.updateByParamKey, params);
      } catch (err) {
        this.$$Message.error('保存配置失败！');
        console.log(err);
      }
    },
  },
  watch: {
    async value(val) {
      // 关闭该弹框时清空已选中的树列表
      if (!val) {
        this.$refs.uiSearchTree.setCheckedKeys([]);
        this.peopleList = [];
        this.selectedPeople = [];
      }
      if (val) {
        await this.getDeviceWorkOrderOrgCode();
        this.selectTree(this.getDefaultSelectedOrg);
        this.setTreeData(this.$refs.uiSearchTree.treeData);
        this.getConfig();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
    computedPeopleList() {
      return this.peopleList.slice(0, this.peopleSliceNum).filter((item) => item.name.indexOf(this.searchText) !== -1);
    },
  },
  props: {
    value: {},
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
