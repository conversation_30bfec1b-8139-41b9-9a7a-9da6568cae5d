<template>
  <div class="container" :class="getFullscreen ? 'full-screen-container' : ''" @click="setScreenFull">
    <i class="icon-font f-24 icon" :class="getIcon"></i>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'full-screen',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
    getIcon() {
      if (this.getFullscreen) {
        return 'icon-tuichuquanping';
      } else {
        return 'icon-ivdg-quanping';
      }
    },
  },
  watch: {},
  filter: {},
  mounted() {
    this.DetectFullscreenChange();
  },
  methods: {
    ...mapActions({
      setFullscreen: 'home/setFullscreen',
    }),
    DetectFullscreenChange() {
      if (screenfull.isEnabled) {
        screenfull.on('change', () => {
          if (screenfull.isFullscreen) {
            this.setFullscreen(true);
          } else {
            this.setFullscreen(false);
          }
        });
      }
    },
    setScreenFull() {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit();
        this.setFullscreen(false);
      } else {
        screenfull.toggle(this.$parent.$el);
        this.setFullscreen(true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .icon {
    color: #888888 !important;
  }
}
.container {
  position: absolute;
  right: 20px;
  top: 5px;
  .icon {
    line-height: 12px;
    color: #ffffff;
    padding: 5px;
  }
}
.full-screen-container {
  right: 35px;
  top: 25px;
}
</style>
