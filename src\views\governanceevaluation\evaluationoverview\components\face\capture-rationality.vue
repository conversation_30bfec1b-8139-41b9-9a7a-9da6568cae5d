<template>
  <div class="auto-fill">
    <div class="statistics-container">
      <div class="statistics">
        <icon-statistics :statistics-list="abnormalCount" :isflexfix="false">
          <template #normalPercent="{ row }">
            <span
              class="icon-font position f-14"
              :class="row.qualified === '2' ? 'icon-budabiao warning' : 'icon-dabiao success'"
            ></span>
          </template>
        </icon-statistics>
      </div>
      <div class="information-echart">
        <draw-echarts
          :echart-option="determinantEchart"
          :echart-style="ringStyle"
          ref="attributeChart"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
      <div class="rank">
        <rank :rank-data="rankData"></rank>
      </div>
    </div>
    <div class="abnormal-title">
      <div class="fl">
        <i class="icon-font icon-xiugaijilu f-16 color-filter"></i>
        <span class="f-16 color-filter ml-sm">异常数据列表</span>
      </div>
      <div class="export fr">
        <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs f-14">导出</span>
        </Button>
      </div>
    </div>
    <div class="hearder-title">
      <search-rationality
        :currentTree="currentTree"
        :treeData="treeData"
        :taskObj="taskObj"
        :width="width"
        @startSearch="startSearch"
      />
    </div>
    <ui-table class="auto-fill" :table-columns="columns" :table-data="tableData" :loading="loading">
      <template #deviceId="{ row, index }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #checkStatus="{ row }">
        <span
          class="tag"
          :style="{
            background: row.checkStatus === '1' ? '#0E8F0E' : '#BC3C19',
          }"
          >{{ checkStatusList(row.checkStatus) }}</span
        >
      </template>
      <!-- 表格操作 -->
      <template #action="{ row }">
        <ui-btn-tip icon="icon-chakanjietu" content="查看抓拍图片" @click.native="viewCapturePicture(row)"></ui-btn-tip>
      </template>
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <capture-picture v-model="capturePictureVisible" :params="currentRow"></capture-picture>
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import evaluationoverview from '@/config/api/evaluationoverview';
import { captureRationality } from '../../../../../util/module/doEcharts';
export default {
  name: 'capture-rationality',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchRationality: require('../components/search-rationality').default,
    IconStatistics: require('@/components/icon-statistics').default,
    Rank: require('../components/rank').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    CapturePicture: require('@/views/appraisaltask/inspectionrecord/faceviewdata/component/capture-picture').default,
  },
  props: {
    /**
     * 右上角检测任务筛选
     */
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    /**
     * 左侧树结构
     */
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    rankList: {
      type: Array,
      default: () => [],
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      treeData: [],
      getDefaultSelectedOrg: '',
      currentOrgObj: {}, //机构树
      loading: false,
      width: 155, // 设备模式数量检索展示对应label宽度
      bigPictureShow: false, // 打图展示
      imgList: [], // 大图图片
      abnormalCount: [
        {
          key: 'totalDeviceCount',
          name: '应检测设备数量',
          value: 0,
          icon: 'icon-yingjianceshebeishuliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
        {
          key: 'checkDeviceCount',
          name: '实际测设备数量',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
        },
        {
          key: 'normalDeviceCount',
          name: '抓拍数量合格设备',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color3',
        },
        {
          key: 'abnormalDeviceCount',
          name: '抓拍数量异常设备',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color4',
        },
        {
          key: 'normalPercent',
          name: '抓拍数量合格率',
          value: 0,
          icon: 'icon-zhuapaishulianghegeshuai',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color5',
        },
      ],
      echartsData: [
        {
          key: 'errorNoData',
          name: '历史无抓拍',
          value: 0,
        },
        {
          key: 'errorTodayNoData',
          name: '昨日无抓拍',
          value: 0,
        },
        {
          key: 'errorTooLessData',
          name: '抓拍数据过少',
          value: 0,
        },
        {
          key: 'errorDataSwoop',
          name: '抓拍数量突降',
          value: 0,
        },
      ],
      infoObj: [], // 统计接口返回
      searchData: {}, // 查询参数
      columns: [
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          width: 120,
          ellipsis: true,
          tooltip: true,
        },
        { title: '抓拍总数量', key: 'captureCount', width: 100 },
        { title: '近天抓拍数量', key: 'captureRecentlyCount', width: 120 },
        { title: '昨日抓拍数量', key: 'captureTodayCount', width: 120 },
        { title: '持续无抓拍天数', key: 'noCaptureDays', width: 120 },
        {
          title: '历史同天平均抓拍量',
          key: 'historyAverageCount',
          width: 180,
          renderHeader: (h) => {
            return (
              <Tooltip max-width="400" transfer>
                <span class="vt-middle">历史同天平均抓拍量</span>
                <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
                <template slot="content">
                  <p class="mb-md f-14">抓拍数量突降计算逻辑：</p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    昨日抓拍量C1： 假设今日2021/10/20日发起检测，则C1=2021/10/19日抓拍量；
                  </p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    历史同天抓拍量C2： 平台上线至2021年10月 19 日号前所有星期二（10月19日是星期二）抓拍量的平均抓拍量；
                  </p>
                  <p class="f-12">
                    <div class="white-circle mr-xs"></div>
                    若（C2-C1）/C2 >= 50%（ 配置值），则判定抓拍数据量突降。
                  </p>
                </template>
              </Tooltip>
            );
          },
        },
        { title: '昨日变化', key: 'changeRatio', width: 80 },
        {
          title: '检测结果',
          key: 'checkStatus',
          width: 90,
          slot: 'checkStatus',
        },
        {
          title: '异常原因',
          key: 'message',
          width: 150,
          ellipsis: true,
          tooltip: true,
        },
        { title: '检测时间', key: 'examineTime', width: 150 },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 60,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      tableData: [],
      rankData: [],
      echartsLoading: false,
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      determinantEchart: {},
      barData: [],
      echartData: [],
      currentRow: {},
      capturePictureVisible: false,
      exportLoading: false,
    };
  },
  computed: {
    computedDeviceType() {
      /**
       * ("设备类型 1-车辆卡扣 2-视图抓拍类")
       * private String deviceType;
       */
      return this.$route.query.indexId == 82 ? 2 : 1;
    },
  },
  watch: {
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    await this.statisticsCount();
    await this.abnormalCountMap();
    await this.initList();
    this.initRing();
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        pageNum: 1,
        pageSize: this.pageData.totalCount,
        deviceType: this.computedDeviceType,
        resultId: this.$route.query.resultId,
      };
      try {
        let res = await this.$http.post(inspectionrecord.getEvaluationCaptureRationalityExport, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    viewCapturePicture(row) {
      this.currentRow = row;
      this.capturePictureVisible = true;
    },
    initRing() {
      let opts = {
        xAxis: this.echartsData.map((item) => item.name),
        data: this.echartsData.map((item) => item.value),
      };
      this.determinantEchart = this.$util.doEcharts.captureRationality(opts);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(
          inspectionrecord.getRationalityPageList,
          Object.assign(
            this.pageData,
            {
              resultId: this.$route.query.resultId,
              deviceType: this.computedDeviceType,
            },
            this.searchData,
          ),
        );
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    async statisticsCount() {
      let { indexId, resultId } = this.$route.query;
      // 统计接口
      let params = {
        resultId: resultId,
        indexId: indexId,
        isRecord: false, //false 为结果详情
      };
      let {
        data: { data },
      } = await this.$http.get(inspectionrecord.getRationalityRecordStatistics, { params });
      this.infoObj = data;
    },
    async getBigImageUrl(item) {
      try {
        let params = {
          deviceCode: item.deviceId,
        };
        let {
          data: { data },
        } = await this.$http.get(inspectionrecord.getCaptureByDevice, {
          params,
        });
        let list = data.map((item) => item.scenePath);
        this.imgList = list;
        if (!list.length) {
          this.$Message.error('抓拍图片不存在');
          return;
        }
        this.bigPictureShow = true;
      } catch (e) {
        console.log(e);
      }
    },
    // 检索
    async startSearch(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.$nextTick(async () => {
        await this.statisticsCount();
        await this.abnormalCountMap();
        this.initList();
        this.initRing();
      });
    },
    // 组织机构切换
    currentChange(data) {
      this.currentOrgObj = data;
    },
    // 统计参数填充
    abnormalCountMap() {
      /*      let map = {
        totalDeviceCount: '应检测设备数量',
        checkDeviceCount: '实际测设备数量',
        normalDeviceCount: '抓拍数量合格设备',
        abnormalDeviceCount: '抓拍数量异常设备',
        normalPercent: '抓拍数量合格率',

        errorNoData: '历史无抓拍',
        errorTodayNoData: '昨日无抓拍',
        errorTooLessData: '抓拍数据过少',
        errorDataSwoop: '抓拍数量突降',
      }*/
      let qualified = this.infoObj.find((item) => item.key === 'qualified');
      this.infoObj &&
        this.infoObj.map((value) => {
          this.abnormalCount.map((item) => {
            if (item.key === value.key) {
              item.value = Number.parseInt(value.desc);
            }
            if (item.key === 'normalPercent') {
              item.qualified = qualified.desc;
            }
          });
          this.echartsData.map((item) => {
            if (item.key === value.key) {
              item.value = value.desc;
            }
          });
        });
    },
    checkStatusList(checkStatus) {
      return checkStatus === '1' ? '正常' : '异常';
    },
  },
};
</script>
<style lang="less" scoped>
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.white-circle {
  position: relative;
  display: inline-block;
  line-height: 10px;
  vertical-align: middle;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #f5f5f5;
}
.statistics-container {
  display: flex;

  .statistics {
    width: 766px;
    height: 252px;
    margin-top: 10px;
    margin-right: 10px;
    @{_deep} .information-statistics {
      background: var(--bg-sub-content);
      .statistics-ul {
        padding: 20px 5px 10px 20px;
      }
    }
  }
  .information-echart {
    width: 640px;
    height: 252px;
    background: var(--bg-sub-content);
    margin-top: 10px;

    .echarts-box {
      width: 100%;
      height: 100% !important;

      .charts {
        width: 100%;
        height: 100% !important;
      }
    }
  }
  .rank {
    width: 340px;
    height: 252px;
    margin-left: 10px;
    margin-top: 10px;
    @{_deep} .information-ranking {
      padding: 0;
    }
  }
}
.abnormal-title {
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(7, 66, 119, 1);
  .color-filter {
    color: rgba(43, 132, 226, 1);
    vertical-align: middle;
  }
}
.success {
  color: #0e8f0e;
}
.warning {
  color: #bc3c19;
}
.position {
  position: absolute;
  right: 10px;
  top: 10px;
}
</style>
