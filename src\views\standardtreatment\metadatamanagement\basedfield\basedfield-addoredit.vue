<template>
  <div>
    <ui-modal :title="title" v-model="visible" :width="620" @query="onSubmit">
      <div class="basedField-addOrEdit">
        <Form ref="modalData" :model="modalData" :rules="modalDataValidate" :label-width="80">
          <FormItem label="字段名称" prop="fieldName" class="left-item">
            <Input
              type="text"
              v-model="modalData.fieldName"
              class="width-input"
              placeholder="请输入字段名称"
              :maxlength="20"
            ></Input>
          </FormItem>
          <FormItem label="注释" prop="fieldComment" class="left-item">
            <Input
              type="text"
              v-model="modalData.fieldComment"
              class="width-input"
              placeholder="请输入注释"
              :maxlength="100"
            ></Input>
          </FormItem>
          <FormItem label="标识代码" prop="identCode" class="left-item">
            <Select
              v-model="modalData.identCode"
              @on-change="changeCode"
              filterable
              clearable
              class="width-input"
              placeholder="请选择标识代码"
            >
              <Option v-for="(item, index) in identCodeList" :key="index" :value="item.identCode">{{
                item.identName
              }}</Option>
            </Select>
          </FormItem>
          <FormItem label="字符类型" prop="fieldType" class="left-item">
            <Input
              :disabled="true"
              type="text"
              v-model="modalData.fieldNames"
              class="width-input"
              placeholder="根据标识代码定义"
              :maxlength="20"
            ></Input>
          </FormItem>
          <FormItem label="字符长度" prop="fieldLength" class="left-item">
            <Input
              :disabled="true"
              type="text"
              v-model="modalData.fieldLength"
              class="width-input"
              placeholder="请输入字符长度"
              :maxlength="20"
            ></Input>
          </FormItem>
          <FormItem label="备注" prop="remark" class="left-item">
            <Input
              type="textarea"
              :rows="5"
              :maxlength="500"
              class="width-input"
              v-model="modalData.remark"
              placeholder="请输入备注"
            ></Input>
          </FormItem>
          <FormItem label="是否可空" prop="isNull" class="left-item" required>
            <RadioGroup v-model="modalData.isNull">
              <Radio label="1">是</Radio>
              <Radio label="2">否</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </div>
    </ui-modal>
  </div>
</template>

<script>
import metadatamanagement from '@/config/api/metadatamanagement';
export default {
  data() {
    const validateFieldLength = (rule, value, callback) => {
      if (Number(value)) {
        this.modalData.fieldLength = String(Number(value));
      }
      if (!value || value === '') {
        callback(new Error('请输入字符长度'));
      }
      if (!Number(value)) {
        callback(new Error('请输入数字格式字符长度'));
      } else {
        callback();
      }
    };
    const validateFieldName = (rule, value, callback) => {
      let regUrl = /^[A-Za-z0-9-!_"“”；;]{1,20}$/;
      if (!value || value === '') {
        callback(new Error('请输入字段名称'));
      } else if (!regUrl.test(value)) {
        callback(new Error('请输入1-20位字母、数字和_”的字段名称'));
      } else {
        callback();
      }
    };
    return {
      modalData: { isNull: 1 },
      title: '新增字段',
      visible: false,
      accessTypeList: [{ dataKey: '1', dataValue: '字符型' }],
      identCodeList: [],
      modalDataValidate: {
        fieldName: [{ required: true, validator: validateFieldName, trigger: 'blur' }],
        fieldComment: [{ required: true, message: '请输入注释', trigger: 'blur' }],
        identCode: [{ required: true, message: '请选择标识代码', trigger: 'change' }],
        fieldType: [{ required: true, message: '请选择字符类型', trigger: 'blur' }],
        fieldLength: [{ required: true, validator: validateFieldLength, trigger: 'blur' }],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    open(row, title) {
      this.title = title;
      this.visible = true;
      this.$refs.modalData.resetFields();
      if (row) {
        this.modalData = JSON.parse(JSON.stringify(row));
        this.modalData.isNull = String(this.modalData.isNull) || '1';
        this.modalData.fieldNames = this.metaFieldType[this.modalData.fieldType];
      }
      this.getDictList();
    },
    async getDictList() {
      try {
        let res = await this.$http.get(metadatamanagement.dictGetAllData);
        this.identCodeList = res.data.data || [];
      } catch (err) {
        console.log(err);
      }
    },
    // 标识代码选中
    changeCode(value) {
      this.identCodeList.map((val) => {
        if (val.identCode === value) {
          this.modalData.fieldType = val.fieldType;
          this.modalData.fieldNames = this.metaFieldType[val.fieldType];
          this.modalData.fieldLength = val.fieldLength;
        }
      });
    },
    // 保存
    onSubmit() {
      if (this.validate('modalData') === 'error') {
        return false;
      }
      // 编辑
      if (this.modalData.id) {
        this.upadte();
        // 新增
      } else {
        this.add();
      }
    },
    async upadte() {
      try {
        let res = await this.$http.put(metadatamanagement.managementUpdate, this.modalData);
        if (res.data.code === 200) {
          this.$emit('search');
          this.$Message.success('编辑成功');
        }
        this.visible = false;
      } catch (err) {
        console.log(err);
      }
    },
    async add() {
      try {
        let res = await this.$http.post(metadatamanagement.managementAdd, this.modalData);
        if (res.data.code === 200) {
          this.$emit('search');
          this.$Message.success('新增成功');
        }
        this.visible = false;
      } catch (err) {
        console.log(err);
      }
    },
    validate(name) {
      let message = 'success';
      this.$refs[name].validate((valid) => {
        if (valid) {
          message = 'success';
        } else {
          message = 'error';
        }
      });
      return message;
    },
  },
  computed: {},
  props: {
    metaFieldType: {
      type: Object,
      default() {},
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.basedField-addOrEdit {
  padding-left: 70px;
  .width-input {
    width: 380px;
  }
  /deep/.ivu-select-input {
    color: #fff;
  }
}
</style>
