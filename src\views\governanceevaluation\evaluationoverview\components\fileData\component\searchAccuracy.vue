<template>
  <div class="base-search">
    <ui-label class="inline" label="姓名" :width="50">
      <Input v-model="searchData.name" class="width-lg" placeholder="请输入姓名"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" label="证件号" :width="50">
      <Input v-model="searchData.idCard" class="width-lg" placeholder="请输入证件号"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" label="检测结果" :width="70">
      <Select class="width-sm" v-model="searchData.result" clearable placeholder="请选择检测结果">
        <Option :value="1" label="聚档合格"></Option>
        <Option :value="2" label="聚档不合格"></Option>
      </Select>
    </ui-label>
    <ui-label :width="30" class="inline search-button" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      searchData: {
        idCard: '',
        name: '',
        result: '',
      },
    };
  },
  created() {},
  methods: {
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        idCard: '',
        name: '',
        result: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  computed: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.base-search {
  margin: 10px 0;
  .input-width {
    width: 200px;
  }
}
</style>
