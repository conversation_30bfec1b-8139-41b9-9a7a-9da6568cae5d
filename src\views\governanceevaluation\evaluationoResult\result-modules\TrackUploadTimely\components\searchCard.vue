<template>
  <div class="base-search">
    <div class="image-model" v-if="isImageModel">
      <ui-label class="fl" label="抓拍时间" :width="70">
        <div class="date-picker-box">
          <DatePicker
            class="input-width"
            v-model="imageSearchData.customParameters.beginTime"
            type="datetime"
            placeholder="请选择开始时间"
            :options="startTimeOption"
            confirm
            @on-change="
              (formatTime, timeType) =>
                changeTimeMx(formatTime, timeType, imageSearchData.customParameters, 'beginTime')
            "
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="input-width"
            v-model="imageSearchData.customParameters.endTime"
            type="datetime"
            placeholder="请选择结束时间"
            :options="endTimeOption"
            confirm
            @on-change="
              (formatTime, timeType) => changeTimeMx(formatTime, timeType, imageSearchData.customParameters, 'endTime')
            "
          ></DatePicker>
        </div>
      </ui-label>
      <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
        <select-camera
          class
          @pushCamera="pushCamera"
          :device-ids="imageSearchData.customParameters.deviceIds"
        ></select-camera>
      </ui-label>
      <ui-label class="fl ml-lg" label="判定结果" :width="70">
        <Select v-model="checkResult" style="width: 200px" placeholder="请选择判定结果">
          <Option v-for="(item, index) in checkStatus" :key="index" :value="item.checkKey">{{ item.name }}</Option>
        </Select>
      </ui-label>
    </div>
    <div class="file-model" v-else>
      <ui-label class="inline mr-lg fl" label="姓 名" :width="40">
        <Input class="input-width" placeholder="请输入姓名" v-model="fileSearchData.customParameters.name"></Input>
      </ui-label>
      <ui-label class="inline fl" label="证 件 号" :width="60">
        <Input class="input-width" placeholder="请输入证件号" v-model="fileSearchData.customParameters.idCard"></Input>
      </ui-label>
    </div>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button
        type="default"
        class="mr-lg"
        @click="resetSearchDataMx(isImageModel ? imageSearchData : fileSearchData, startSearch)"
        >重置</Button
      >
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    isImageModel: {
      default: () => true,
    },
    modular: {
      default: 1,
    },
    searchNum: {
      default: 1,
    },
    taskObj: {
      type: Object,
      default() {},
    },
    treeData: {
      type: Array,
      default() {},
    },
    checkStatus: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      imageSearchData: {
        customParameters: {
          deviceIds: [],
          beginTime: '',
          endTime: '',
          // synthesisResult: '',
          // delayStatus: '',
        },
      },
      fileSearchData: {
        customParameters: { name: '', idCard: '' },
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.imageSearchData.customParameters.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.imageSearchData.customParameters.beginTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      checkResult: '',
    };
  },
  created() {},
  mounted() {},
  methods: {
    pushCamera(list) {
      this.imageSearchData.customParameters.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      if (this.searchNum == 1) {
        this.imageSearchData.customParameters.synthesisResult = this.checkResult;
      } else if (this.searchNum == 2) {
        this.imageSearchData.customParameters.delayStatus = this.checkResult;
      } else if (this.searchNum == 3) {
        this.imageSearchData.customParameters.urlAvailableStatus = this.checkResult;
      }
      this.$emit('startSearch', this.isImageModel ? this.imageSearchData : this.fileSearchData);
    },
    resetSearchDataMx() {
      this.fileSearchData = {
        customParameters: { name: '', idCard: '' },
      };
      this.imageSearchData = {
        customParameters: {
          deviceIds: [],
          beginTime: '',
          endTime: '',
          // synthesisResult: '',
          // delayStatus: '',
          // urlAvailableStatus: '',
        },
      };
      this.checkResult = '';
      this.$emit('startSearch', this.isImageModel ? this.imageSearchData : this.fileSearchData);
    },
  },
  watch: {},
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
    // ApiOrganizationTree: require('./api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.small-width {
  width: 70px;
}
.base-search {
  overflow: hidden;
  padding-bottom: 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
}
.track-search {
  padding: 20px 0 0 20px;
}
</style>
