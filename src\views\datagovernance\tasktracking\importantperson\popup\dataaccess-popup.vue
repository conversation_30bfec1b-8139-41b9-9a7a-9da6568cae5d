<template>
  <ui-modal title="数据输入-原始数据表-人员基础信息数据" v-model="visible" :styles="styles" :footer-hide="true">
    <base-search @startSearch="startSearch" :tagTypeList="tagTypeList"></base-search>
    <!-- <p class="statics mt-sm mb-sm">
            数据总量：<span class="active-color">{{ pageData.totalCount }}</span>
        </p> -->
    <ui-table
      class="ui-table mt-lg"
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="minusTable"
      :loading="loading"
    >
      <template #faceImg="{ row }">
        <ui-image :src="row.identityPhoto" class="image" />
        <!-- <img src="@/assets/img/common/nodata.png" class="image"> -->
      </template>
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<style lang="less" scoped>
.statics {
  color: #fff;
  .active-color {
    color: var(--color-bluish-green-text);
  }
}
.image {
  width: 56px;
  height: 56px;
  margin: 3px 0;
}
</style>
<script>
import tasktracking from '@/config/api/tasktracking';
export default {
  data() {
    return {
      visible: false,
      styles: {
        width: '95%',
      },
      activeRouterName: null,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '人员照片', slot: 'faceImg' },
        { title: '姓名', key: 'name' },
        { title: '性别', key: 'gender' },
        { title: '民族', key: 'nation' },
        { title: '证件类型', key: 'cardTypeText' },
        { title: '证件号', key: 'idCard' },
        { title: '重点人员类型', key: 'personTypeText' },
        { title: '居住地址', key: 'homeAddress' },
      ],
      tableData: [],
      minusTable: 350,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchData: {
        personType: '',
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    init() {
      this.visible = true;
      this.queryPersonInitAccessList();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.queryPersonInitAccessList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.queryPersonInitAccessList();
    },
    startSearch(val) {
      Object.assign(this.searchData, val);
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.queryPersonInitAccessList();
    },
    async queryPersonInitAccessList() {
      try {
        this.loading = true;
        let { data } = await this.$http.post(tasktracking.queryPersonInitAccessList, this.searchData);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  props: {
    value: {},
    tagTypeList: {
      default: () => [],
    },
  },
  components: {
    BaseSearch: require('../components/base-search.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
