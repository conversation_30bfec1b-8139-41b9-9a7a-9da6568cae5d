import ArchivesLayout from "@/layouts/archives-layout";
import BasicLayout from "@/layouts/basic-layout";

export default [
  {
    path: "/placeControl-data-storage2",
    name: "placeControl-data-storage2",
    component: BasicLayout,
    children: [
      {
        path: "/placeControl-data-storage/special-library/personnel-thematic-database",
        name: "placeControl-personnel-thematic-database",
        tabShow: true,
        parentName: "placeControl-data-storage/special-library",
        component: (resolve) =>
          require([
            "@/views/juvenile-data-storage/special-library/personnel-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "专题库-人像库",
        },
      },
    ],
  },
  {
    path: "/placeControl-archive",
    name: "placeControl-archive",
    component: ArchivesLayout,
    meta: {
      title: "人员档案",
      icon: "icon-shouye",
      show: true,
    },
    children: [
      {
        path: "/placeControl-archive/people-dashboard",
        name: "placeControl-people-dashboards",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/dashboard"
          ),
        meta: {
          title: "人员概览",
          tagShow: true,
        },
      },
      {
        path: "/placeControl-archive/people-profile",
        name: "placeControl-people-profile",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/profile"
          ),
        meta: {
          title: "人员资料",
          tagShow: true,
        },
      },
      {
        path: "/placeControl-archive/people-activity-track",
        name: "placeControl-people-activity-track",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/activity-track"
          ),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/placeControl-archive/people-relationship-map",
        name: "placeControl-people-relationship-map",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/relationship-map"
          ),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/placeControl-place-archive",
    name: "placeControl-place-archive",
    component: ArchivesLayout,
    redirect: "/placeControl-place-archive/place-dashboard",
    meta: {
      title: "场所档案 ",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/placeControl-place-archive/place-dashboard",
        name: "placeControl-place-dashboard",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/placeControl-place-archives/dashboard"
          ),

        meta: {
          title: "场所概览",
          tagShow: true,
        },
      },
      {
        path: "/placeControl-place-archive/place-relationship-map",
        name: "placeControl-place-relationship-map",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/place-archives/relationship-map"
          ),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
];
