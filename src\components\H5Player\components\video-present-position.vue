<template>
    <Modal ref="printScreenModal" v-model="options.open" :z-index="10000" sticky :mask="false" :title="options.title" width="300" class-name="vertical-center-modal" @on-ok="handleOk" @on-cancle="handleCancel">
        <Form ref="form" :model="form">
			<FormItem label="预置位名称:" prop="name" class="form-item">
				<Input v-model="form.name" :maxlength="50" placeholder="请输入预置位名称" class="width-md" />
			</FormItem>
			<!-- <FormItem label="是否设为归位点:" prop="isRestoration" class="form-item">
				<RadioGroup v-model="form.isRestoration">
                    <Radio :label="0">否</Radio>
                    <Radio :label="1">是</Radio>
                </RadioGroup>
			</FormItem> -->
		</Form>
        <ui-loading v-if="loading" />
    </Modal>
</template>

<script>
    import { createPreset } from '@/api/player.js'
    import EventBus from '@/util/eventBus.js'
    export default {
        props: {
            options: {
                type: Object,
                default: () => {}
            }
        },
        data(){
            return {
                form: {
                    name: '',
                    isRestoration: 0
                },
                loading: false
            }
        },
        watch: {
            'options.open': {
                handler (val) {
                    if (val) {
                        this.form = {
                            name: '',
                            isRestoration: 0
                        }
                    }
                },
            }
        },
        methods: {
            storeImage(){
                var imgBase64 = this.options.H5Player.capturePicture(this.options.index,"jpeg",1286, 723);
                return imgBase64
            },
            async handleOk(){
                if (!this.form.name) {
                    this.$Message.error('预置位名称不能为空');
                    return;
                }
                let reg = /^[0-9a-zA-Z_\u4e00-\u9fa5]{1,20}$/;
                if (!reg.test(this.form.name)) {
                    this.$Message.error('预置位名称由1到20位数字、字母、中文、下划线组成');
                    return;
                }
                // 对名称做验证
                try{
                    let imgUrl = await this.storeImage();
                    var param = {
                        "gbId": this.options.cameraInfo.deviceGbId,
                        "imageStream": imgUrl,
                        "isRestoration": this.form.isRestoration,
                        "name": this.form.name,
                        "stopTime": 5
                    };
                    this.loading = true
                    createPreset(param).then((res)=>{
                        this.$Message.success("添加预置位成功");
                        this.options.open = false
                        this.options.H5Player = null
                        EventBus.$emit('createdPreset')
                    }).finally(() => {
                        this.loading = false
                    });
                }catch(e){}
            },
            handleCancel(){
                this.options.open = false
                this.options.H5Player = null
            }
        }
    }
</script>

<style lang="less">
.vertical-center-modal {
    display: flex;
    align-items: center;
    justify-content: center;
}
.form-item {
    display: flex;
    .ivu-form-item-label {
        white-space: nowrap;
    }
}
</style>
