<template>
  <div class="equipmentlibraryList auto-fill">
    <!--    <slide-unit-tree @selectOrgCode="selectOrgCode" :select-key="selectKey"></slide-unit-tree>-->
    <!-- 顶部统计 -->
    <chartsContainer :abnormalCount="countList" class="charts" />

    <div class="search-module">
      <div class="fr">
        <UiSearch class="ui-search mr-lg" v-model="searchModel">
          <template #content>
            <div class="search-content">
              <ui-label class="inline mb-10" label="是否检测">
                <Select class="width-md" v-model="searchData.isCheck" placeholder="请选择" clearable :max-tag-count="1">
                  <Option value="">请选择</Option>
                  <Option value="1">是</Option>
                  <Option value="0">否</Option>
                </Select>
              </ui-label>

              <!-- 基础信息状态 -->
              <ui-label class="inline mb-10" label="基础信息状态">
                <Select
                  :disabled="searchData.isCheck != '1'"
                  class="width-md leftSelect"
                  v-model="searchData.baseCheckStatus"
                  placeholder="请选择是否合格"
                  clearable
                  :max-tag-count="1"
                >
                  <Option value="">请选择</Option>
                  <Option value="0">合格</Option>
                  <Option value="1">不合格</Option>
                </Select>
                <span class="split">-</span>
                <Select
                  class="width-md rightSelect"
                  :disabled="searchData.baseCheckStatus != '1'"
                  v-model="searchData.baseErrorMessageList"
                  multiple
                  placeholder="请选择错误类别"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(val, key, index) in baseErrorMessageList" :value="key" :key="index"
                    >{{ val }}
                  </Option>
                </Select>
              </ui-label>

              <!-- 视图数据状态 -->
              <ui-label class="inline mb-10" label="视图数据状态">
                <Select
                  :disabled="searchData.isCheck != '1'"
                  class="width-md leftSelect"
                  v-model="searchData.viewCheckStatus"
                  placeholder="请选择是否合格"
                  clearable
                  :max-tag-count="1"
                >
                  <Option value="">请选择</Option>
                  <Option value="0">合格</Option>
                  <Option value="1">不合格</Option>
                </Select>
                <span class="split">-</span>
                <Select
                  :disabled="searchData.viewCheckStatus != '1'"
                  class="width-md rightSelect"
                  v-model="searchData.viewErrorMessageList"
                  multiple
                  placeholder="请选择错误类别"
                  clearable
                  :max-tag-count="1"
                >
                  <Option :value="key" v-for="(val, key, index) in viewErrorMessageList" :key="index"
                    >{{ val }}
                  </Option>
                </Select>
              </ui-label>

              <!-- 视频流数据状态 -->
              <ui-label class="inline mb-10" label="视频流数据状态">
                <Select
                  :disabled="searchData.isCheck != '1'"
                  class="width-md leftSelect"
                  v-model="searchData.videoCheckStatus"
                  placeholder="请选择是否合格"
                  clearable
                  :max-tag-count="1"
                >
                  <Option value="">请选择</Option>
                  <Option value="0">合格</Option>
                  <Option value="1">不合格</Option>
                </Select>
                <span class="split">-</span>
                <Select
                  :disabled="searchData.videoCheckStatus != '1'"
                  class="width-md rightSelect"
                  v-model="searchData.videoErrorMessageList"
                  multiple
                  placeholder="请选择错误类别"
                  clearable
                  :max-tag-count="1"
                >
                  <Option :value="key" v-for="(val, key, index) in videoErrorMessageList" :key="index"
                    >{{ val }}
                  </Option>
                </Select>
              </ui-label>

              <ui-label class="inline mb-10" label="数据来源">
                <Select class="width-md" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
                  <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>

              <!-- 监控点位类型 -->
              <ui-label class="inline mb-10" :label="global.filedEnum.sbdwlx">
                <Select
                  class="width-md"
                  v-model="searchData.sbdwlx"
                  :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>

              <!-- 摄像机功能类型 -->
              <ui-label class="inline mb-10" :label="global.filedEnum.sbgnlx">
                <Select
                  class="width-md"
                  v-model="searchData.sbgnlx"
                  :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                  clearable
                  :max-tag-count="1"
                >
                  <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
                    >{{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="inline" label="设备重点类型">
                <Select class="width-md" v-model="searchData.isImportant" clearable placeholder="请选择设备重点类型">
                  <Option label="普通设备" :value="0"></Option>
                  <Option label="重点设备" :value="1"></Option>
                </Select>
              </ui-label>

              <!-- 摄像机采集区域 -->
              <ui-label class="inline mb-10" :label="`${global.filedEnum.sbcjqy}列表`">
                <Button type="dashed" class="area-btn" @click="clickArea"
                  >请选择采集区域 {{ `已选择 ${searchData.sbcjqyList.length}个` }}
                </Button>
              </ui-label>
              <ui-label class="inline mb-10" label="维护单位">
                <Input v-model="searchData.whdw" class="width-md" placeholder="维护单位"></Input>
              </ui-label>
              <ui-label class="inline mb-10" label="更新时间">
                <DatePicker
                  class="width-md"
                  v-model="searchData.startModifyTime"
                  type="datetime"
                  placeholder="请选择开始时间"
                  @on-change="
                    (formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startModifyTime')
                  "
                  :options="startTimeOption"
                  confirm
                />
                <span class="ml-sm mr-sm">--</span>
                <DatePicker
                  class="width-md"
                  v-model="searchData.endModifyTime"
                  type="datetime"
                  placeholder="请选择结束时间"
                  @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endModifyTime')"
                  :options="endTimeOption"
                  confirm
                />
              </ui-label>
            </div>
          </template>
        </UiSearch>
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
      <ui-label class="inline mb-10" label="组织机构">
        <api-organization-tree
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label class="inline mb-10" label="行政区划">
        <api-area-tree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </ui-label>
      <ui-label class="inline mb-10" :label="`${global.filedEnum.deviceId}`">
        <Input v-model="searchData.deviceId" class="width-md" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline mb-10" :label="`${global.filedEnum.deviceName}`">
        <Input v-model="searchData.deviceName" class="width-md" placeholder="请输入设备名称"></Input>
      </ui-label>

      <!-- 设备状态 -->
      <ui-label class="inline mb-10" :label="global.filedEnum.phyStatus">
        <Select
          class="width-md"
          v-model="searchData.phyStatus"
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
    </div>

    <div class="btns" style="">
      <Button
        type="primary"
        class="fr ml-sm"
        @click="addDevice"
        v-permission="{
          route: 'equipmentlibrary',
          permission: 'addDevice',
        }"
      >
        <i class="icon-font icon-tianjia f-14"></i>
        <span class="vt-middle ml-sm">新增入库设备</span>
      </Button>
      <Button type="primary" class="fr ml-sm" @click="exportModule">
        <i class="icon-font icon-daochu f-14"></i>
        <span class="vt-middle ml-sm">导出</span>
      </Button>
      <Upload
        action="/ivdg-asset-app/device/importDevice"
        ref="upload"
        class="inline fr"
        :show-upload-list="false"
        :headers="headers"
        :data="uploadParams"
        :before-upload="beforeUpload"
        :on-success="importSuccess"
        :on-error="importError"
      >
        <Button type="primary" class="ml-sm" :loading="importLoading">
          <i class="icon-font icon-daoruwentishebei f-12"></i>
          <span class="vt-middle ml-sm">导入设备</span>
        </Button>
      </Upload>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selectTable"
      >
        <template #Index="{ row, index }">
          <div :class="row.rowClass">
            <span>{{ index + 1 }}</span>
          </div>
        </template>
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <template #deviceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
            {{ row.deviceName }}
          </div>
        </template>
        <template #address="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.address">
            {{ row.address }}
          </div>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #orgName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
            {{ row.orgName }}
          </div>
        </template>
        <template #checkStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="
                row.checkStatus === '0000'
                  ? 'bg-success'
                  : row.checkStatus === '1000'
                    ? 'bg-other'
                    : row.checkStatus
                      ? 'bg-failed'
                      : ''
              "
            >
              {{ handleCheckStatus(row.checkStatus) }}
            </span>
          </div>
        </template>
        <template #baseCheckStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
            >
              {{ row.baseCheckStatus == 1 ? '不合格' : row.baseCheckStatus == 0 ? '合格' : '--' }}
            </span>
          </div>
        </template>
        <template #viewCheckStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="row.viewCheckStatus === '0' ? 'bg-success' : row.viewCheckStatus === '1' ? 'bg-failed' : ''"
            >
              {{ row.viewCheckStatus == 1 ? '不合格' : row.viewCheckStatus == 0 ? '合格' : '--' }}
            </span>
          </div>
        </template>
        <template #videoCheckStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="row.videoCheckStatus === '0' ? 'bg-success' : row.videoCheckStatus === '1' ? 'bg-failed' : ''"
            >
              {{ row.videoCheckStatus == 1 ? '不合格' : row.videoCheckStatus == 0 ? '合格' : '--' }}
            </span>
          </div>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltipType">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <template #sourceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.sourceId">
            {{ row.sourceId }}
          </div>
        </template>
        <template #azsj="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.azsj">
            {{ row.azsj }}
          </div>
        </template>
        <template #ipAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
            {{ row.ipAddr }}
          </div>
        </template>
        <template #macAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
            {{ row.macAddr }}
          </div>
        </template>
        <template #errorType="{ row }">
          <span>{{ handleCheckStatus1(row.checkStatus) }}</span>
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              class="mr-md"
              icon="icon-bianji2"
              content="编辑"
              @click.native="deviceModalShow(row)"
              v-permission="{
                route: 'equipmentlibrary',
                permission: 'updateDeviceById',
              }"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
              icon="icon-shebeidangan"
              content="设备档案"
              @click.native="deviceArchives(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: 'var(--color-table-btn-default)', 'font-size': '14px' }"
              icon="icon-a-ditu2"
              content="地图"
              @click.native="deviceMap(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              :disabled="row.checkStatus === '1000' || row.checkStatus === '0000'"
              :styles="{ color: 'var(--color-failed)', 'font-size': '14px' }"
              icon="icon-yichangyuanyin"
              content="不合格原因"
              @handleClick="viewRecord(row)"
            ></ui-btn-tip>
            <!-- <Button type="text" class="mr-lg" ></Button> -->
            <!-- <Button type="text" @click="deviceArchives(row)">设备档案</Button> -->
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <device-detail
      v-model="deviceDetailShow"
      :choosed-org="choosedOrg"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :unqualified="deviceUnqualified"
      @update="search"
    >
    </device-detail>
    <customize-filter
      v-model="customFilter"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <Tooltip content="只会导出选中字段的模板，不会导出设备数据" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'module')" :loading="exportModuleLoading"
            >{{ exportModuleLoading ? '下载中' : '模板导出' }}
          </Button>
        </Tooltip>
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading"
            >{{ exportDataLoading ? '下载中' : '数据导出' }}
          </Button>
        </Tooltip>
      </template>
    </customize-filter>
    <view-detection-field
      v-model="recordShow"
      :view-data="recordData"
      :need-option="true"
      @recordModalShow="deviceModalShow"
    ></view-detection-field>
    <upload-error v-model="uploadErrorVisible" :error-data="errorData" :error-columns="errorColumns"></upload-error>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'equipmentlibraryList',
  mixins: [downLoadTips],
  props: {},
  data() {
    return {
      uploadErrorVisible: false,
      loading: false,
      exportDataLoading: false,
      exportModuleLoading: false,
      // selectKey: '0',
      deviceUnqualified: null,
      selectTree: {
        regionCode: '',
      },
      searchData: {
        cascadeReportStatus: 1,
        orgCode: '',
        civilCode: '',
        deviceId: '',
        deviceName: '',
        sbdwlx: '',
        sbgnlx: '',
        sourceId: '',
        errorType: '',
        checkStatuses: [],
        errorMessageList: [],
        isImportant: '',
        whdw: '',
        sbcjqyList: [],
        pageNumber: 1,
        pageSize: 20,
        isCheck: '',
        baseCheckStatus: '',
        viewCheckStatus: '',
        videoCheckStatus: '',
        startModifyTime: '',
        endModifyTime: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      choosedOrg: {},
      checkedData: [],
      tableData: [],
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        { minWidth: 120, title: '维护单位', key: 'whdw', tooltip: true },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        { width: 120, title: '数据来源', key: 'sourceIdText' },
        {
          width: 100,
          title: '基础信息状态',
          slot: 'baseCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视图数据状态',
          slot: 'viewCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视频数据状态',
          slot: 'videoCheckStatus',
          align: 'left',
        },
        // { width: 90, title: '检测状态', slot: 'checkStatus', align: 'left' },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 110,
          title: `${this.global.filedEnum.sbcjqy}`,
          key: 'sbcjqyText',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        // { minWidth: 150, title: '异常类型', slot: 'errorType', align: 'left', tooltip: true },
        {
          width: 250,
          title: '设备安装地址',
          key: 'address',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: '设备安装时间',
          key: 'azsj',
          align: 'left',
          tooltip: true,
        },
        { width: 160, title: '入库时间', key: 'createTime' },
        { width: 160, title: '更新时间', key: 'modifyTime' },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      // allDicData: {},
      // dicDataEnum: Object.freeze({
      //   check_status_sq: 'checkStatusList',
      //   propertySearch_sbdwlx: 'sbdwlxTypeList',
      //   sxjgnlx_receive: 'sbgnlxTypeList',
      // }),
      deviceDetailShow: false,
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      viewDeviceId: 0,
      customFilter: false,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: `3.125rem`,
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
      countList: [
        { title: '设备总量', count: '0', icon: 'icon-equipmentlibrary' },
        { title: '视频监控', count: '0', icon: 'icon-ivdg-shipinjiankong' },
        { title: '人脸卡口', count: '0', icon: 'icon-renliankakou' },
        { title: '车辆卡口', countKey: '0', icon: 'icon-cheliangkakou' },
      ],
      recordShow: false,
      recordData: {},
      deviceCode: '',
      selectOrgTree: {
        orgCode: null,
      },
      messageList: [],
      importLoading: false,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      uploadParams: {
        execInsert: '1', //区别治理中心信息填报导入
      },
      errorData: [],
      errorColumns: [],
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      areaSelectModalVisible: false,
      checkedTreeData: [],
      baseErrorMessageList: [],
      viewErrorMessageList: [],
      videoErrorMessageList: [],
      searchModel: false,
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endModifyTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startModifyTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  async created() {
    // this.dictTypeListGroupByType()
    if (this.propertySearchLbdwlx.length === 0) await this.getAlldicData();
  },
  mounted() {
    this.getPropertyList();
    // this.selectKey = this.defaultSelectedOrg.orgCode
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.init();
    this.queryDeviceCount();
    this.queryErrorList();
  },
  methods: {
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        this.checkedTreeData = this.searchData.sbcjqyList || [];
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data) {
      this.searchData.sbcjqyList = data;
    },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        if (res.data.failCount === 0) {
          this.$Message.success(res.data.tip);
        } else {
          this.$Message.warning({
            closable: true,
            content: res.data.tip,
          });
        }
        this.search();
      } else if (res.code === 81598) {
        this.dealError(res.msg);
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    dealError(error) {
      this.errorData = JSON.parse(error);
      this.uploadErrorVisible = true;
      this.errorColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'left',
        },
        {
          title: '设备编码',
          key: 'deviceId',
          align: 'left',
        },
        {
          title: '错误原因',
          key: 'errorReason',
          align: 'left',
        },
      ];
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.search();
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // selectOrgCode(data) {
    //   this.searchData.orgCode = data.orgCode
    //   this.choosedOrg = data
    //   this.init()
    //   this.queryDeviceCount()
    // },
    selectSource(val) {
      let sourceId = val ? val.sourceId : '';
      this.searchData.sourceId = sourceId;
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.getPageDeviceList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.loading = false;
      }
    },
    async queryDeviceCount() {
      try {
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(taganalysis.deviceStatistics, this.searchData);
        let obj = res.data.data;
        this.countList[0].count = obj.deviceTotal;
        this.countList[1].count = obj.videoMonitor;
        this.countList[2].count = obj.faceCount;
        this.countList[3].count = obj.vehicleCount;
      } catch (err) {
        console.log('err', err);
      } finally {
        // this.loading = false
      }
    },
    // 地图跳转
    deviceMap(row) {
      this.$emit('mapInfo', row);
    },
    choseStuses(val) {
      //清空
      if (val.indexOf('3') === -1) {
        this.searchData.errorCategory = '';
        this.searchData.errorMessageList = [];
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
      this.queryDeviceCount();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    handleCheckStatus(row) {
      const flag = {
        1000: '待检测',
        '0000': '合格',
      };
      let msg = '';
      if (row) {
        msg = flag[row] ? flag[row] : '不合格';
      }
      return msg;
    },
    handleCheckStatus1(row) {
      const flag = {
        1000: '待检测',
        '0000': '合格',
        '0001': '视频流不合格',
        '0010': '视图不合格',
        '0011': '视图、视频流不合格',
        '0100': '基础信息不合格',
        '0101': '基础信息、视频流不合格',
        '0110': '基础信息、视图不合格',
        '0111': '基础信息、视图、视频流不合格',
      };
      return flag[row] ? flag[row] : '--';
    },
    addDevice() {
      this.deviceDetailTitle = '新增入库设备';
      this.deviceDetailAction = 'add';
      this.deviceDetailShow = true;
    },
    deviceModalShow(row, unqualified) {
      this.deviceDetailTitle = '修改入库设备';
      this.deviceDetailAction = 'edit';
      this.deviceUnqualified = unqualified || null;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    exportModule() {
      this.customFilter = true;
    },
    async exportExcel(propertyList, type) {
      try {
        if (type === 'module') {
          this.exportModuleLoading = true;
        } else {
          this.$_openDownloadTip();
          this.exportDataLoading = true;
        }
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          isTemplate: type === 'module',
          ids: this.checkedData.map((item) => item.id),
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(equipmentassets.exportDevice, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportModuleLoading = false;
        this.exportDataLoading = false;
      }
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        this.defaultCheckedList = [
          'deviceId',
          'id',
          'deviceName',
          'sbgnlx',
          'sbdwlx',
          'ipAddr',
          'macAddr',
          'longitude',
          'latitude',
          'sbcjqy',
          'phyStatus',
        ];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId' || item.propertyName === 'id';
        };
        this.rightDisabled = (item) => {
          return !(item.propertyName === 'deviceId' || item.propertyName === 'id');
        };
      } catch (err) {
        console.log(err);
      }
    },

    viewRecord(row) {
      this.recordShow = true;
      this.recordData = row;
    },
    reset() {
      this.checkStatuses = [];
      this.searchData = {
        isCheck: '',
        checkStatuses: [],
      };
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, this.search);
      this.queryDeviceCount();
    },
    async choseType(val) {
      try {
        this.searchData.errorMessageList = [];
        let res = await this.$http.get(equipmentassets.queryErrorReason, {
          params: { errorType: val },
        });
        if (val === 'BASICS') {
          this.messageList = res.data.data;
        } else if (val === 'IMAGE') {
          this.messageList = res.data.data;
        } else if (val === 'VIDEO') {
          this.messageList = res.data.data;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.baseErrorMessageList = res.data.data;

      let res2 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'IMAGE' },
      });
      this.viewErrorMessageList = res2.data.data;

      let res3 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'VIDEO' },
      });
      this.videoErrorMessageList = res3.data.data;
    },
    // async dictTypeListGroupByType() {
    //   try {
    //     let { data } = await this.$http.post(
    //       user.queryDataByKeyTypes,
    //       Object.keys(this.dicDataEnum)
    //     )
    //     const dataObject = data.data
    //     Object.keys(this.dicDataEnum).forEach((key) => {
    //       let obj = dataObject.find((row) => {
    //         return !!row[key]
    //       })
    //       this.$set(this.allDicData, this.dicDataEnum[key], obj[key])
    //     })
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
  },
  watch: {
    'searchData.isCheck'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.baseCheckStatus = '';
        this.searchData.viewCheckStatus = '';
        this.searchData.videoCheckStatus = '';
        this.searchData.baseErrorMessageList = [];
        this.searchData.viewErrorMessageList = [];
        this.searchData.videoErrorMessageList = [];
      }
    },
    'searchData.baseCheckStatus'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.baseErrorMessageList = [];
      }
    },
    'searchData.viewCheckStatus'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.viewErrorMessageList = [];
      }
    },
    'searchData.videoCheckStatus'(newValue) {
      if (newValue == '0' || newValue == '') {
        this.searchData.videoErrorMessageList = [];
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
    // hasUnqualified() {
    //   return this.searchData.checkStatuses.findIndex((row) => row === '3') !== -1
    // },
  },
  components: {
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    chartsContainer: require('./chartsContainer').default,
    ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UploadError: require('@/views/datagovernance/onlinegovernance/information/components/upload-error.vue').default,
    AreaSelect: require('@/components/area-select').default,
    UiSearch: require('@/components/ui-search').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .search-content {
    background: #0b2348;
    border: 1px solid #2169b9;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .search-content {
    background: #fff;
    box-shadow: 0px 2px 5px 0px rgba(147, 171, 206, 0.7016);
  }
}

.equipmentlibraryList {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .search-model {
    position: absolute;
    padding: 0 20px;
    top: 163px;
    z-index: 9;

    .search-content {
      transition: all 1s ease-in-out;
      padding: 0 20px;
      padding-top: 20px;
    }
  }
  .search-model:before {
    box-sizing: content-box;
    width: 0px;
    height: 0px;
    position: absolute;
    top: -16px;
    right: 220px;
    padding: 0;
    border-bottom: 10px solid var(--bg-nav);
    border-top: 8px solid transparent;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    display: block;
    content: '';
    z-index: 12;
  }
  .search-model:after {
    box-sizing: content-box;
    width: 0px;
    height: 0px;
    position: absolute;
    top: -18px;
    right: 219px;
    padding: 0;
    border-bottom: 9px solid #2169b9;
    border-top: 9px solid transparent;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    display: block;
    content: '';
    z-index: 10;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .charts {
    margin: 20px 0 0 20px;
  }
  .ui-label {
    margin-right: 30px;
  }
  .search-module {
    position: relative;
    margin: 10px 20px 0px;
    .keyword-input {
      width: 300px;
    }
    .ui-label {
      margin-right: 30px;
    }
  }
  .ui-table {
    padding: 0 20px;
  }
  ///deep/ .select-width {
  //  width: 160px;
  //}
}
.tooltipType {
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.tree-select {
  width: 300px !important;
}

.btns {
  padding-right: 20px;
  margin-bottom: 12px;
}

.leftSelect {
  width: 150px !important;
}
.rightSelect {
  width: 240px !important;
}

.rotate {
  transition: all 0.2s ease-in-out;
  display: inline-block;
  transform: rotate(180deg);
}
.norotate {
  transition: all 0.2s ease-in-out;
  display: inline-block;
  transform: rotate(0deg);
}

.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}

@{_deep} .ivu-poptip-inner {
  white-space: initial;
}
</style>
