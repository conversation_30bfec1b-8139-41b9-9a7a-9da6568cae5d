<template>
  <div class="keypersonlibrary auto-fill" :style="{ 'max-height': contentClientHeight + 'px' }">
    <slot name="search"></slot>
    <div class="keypersonlibrary-content auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <div class="keypersonlibrary-content-wrap">
        <template v-for="(item, index) in cardList">
          <slot name="card" :row="item" :index="index"></slot>
        </template>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default() {},
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
    contentClientHeight: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      loading: false,
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      cardList: [],
    };
  },
  computed: {},
  async mounted() {},
  methods: {
    // 此方法在父组件调用表格方法
    async info(boolen) {
      this.loading = true;
      if (boolen) {
        this.reset();
      }
      const result = this.loadData(this.searchData);
      if (typeof result == 'object' && typeof result.then == 'function') {
        result
          .then((res) => {
            if (res) {
              this.cardList = res.data[this.listKey];
              for (let i of this.cardList) {
                if (i.personTypes) {
                  i.personTypes = i.personTypes;
                }
              }
              this.pageData.totalCount = res.data.total;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.cardList = [];
        this.pageData.totalCount = 0;
        this.loading = false;
      }
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = { pageNumber: 1, pageSize: 20 };
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.info();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.info();
    },
    // 详情
    detail(info) {
      this.$emit('detailInfo', info);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.keypersonlibrary {
  // padding-top: 15px;
  background-color: var(--bg-content);
  //max-height: calc(100vh - 380px);
  &-content {
    position: relative;
    // margin: 20px 0 0;
    height: 100%;
    overflow-y: auto;
    &-wrap {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }
  .page {
    padding-right: 0;
  }
}
</style>
