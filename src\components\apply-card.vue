<template>
	<div class="alarm">
		<div class="top">
			<div class="left">
				<i class="iconfont icon-download"></i>
				<span>视频下载申请</span>
			</div>
			<div class="allDel" @click.stop="$emit('allDel')">一键关闭</div>
			<i class="ivu-icon ivu-icon-ios-close ivu-tabs-close" @click.stop="$emit('delAlarm')"></i>
		</div>
		<div class="content">
			<div class="conten-item">申请时间：<span class="value fw">{{ $dayjs(applyInfo.applyTime).format('YYYY-MM-DD HH:mm:ss')}}</span></div>
			<div class="box-flex">
				<div class="conten-item">申请部门：<span class="value fw">{{applyInfo.orgName}}</span></div>
				<div class="conten-item">申请人：<span class="value fw">{{applyInfo.userName}}</span></div>
			</div>
			<div class="down-box">
				<div class="conten-item">下载设备名：
					<div class="value ellips" :title="applyInfo.deviceName">{{applyInfo.deviceName}}</div>
				</div>
				<div class="conten-item">时间段：
					<div class="value">{{$dayjs(applyInfo.startTime).format('YYYY-MM-DD HH:mm:ss')}} - {{ $dayjs(applyInfo.endTime).format('YYYY-MM-DD HH:mm:ss')}}</div>
				</div>
				<div class="conten-item">申请理由：
					<div class="value ellips" :title="applyInfo.applyReason">{{applyInfo.applyReason}}</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>

	export default {
		props: {
			applyInfo: {
				type: Object,
				default: () => { }
			}
		},
		data() {
			return {

			}
		},
		computed: {},
		activated() { },
		mounted() { },
		methods: {

		}
	}
</script>
<style lang="less" scoped>
	.alarm {
		width: 380px;
		height: 260px;
		position: fixed;
		z-index: 9;
		background: linear-gradient(180deg, #ffffff 60%, #cde3ff 100%);
		box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.3);
		overflow: hidden;
		border-radius: 3px;
		right: 14px;
		bottom: 34px;
		.top {
			position: relative;
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 0 0 0 20px;
			height: 40px;
			line-height: 40px;
			color: #fff;
			background: linear-gradient(210deg, #5bdbff 0%, #2c86f8 100%);
			.left {
				display: flex;
				align-items: center;
				i {
					margin-right: 5px;
				}
			}
		}
		.content {
			padding: 10px;
			.box-flex {
				display: flex;
				justify-content: space-between;
				margin: 5px 0;
			}
			.conten-item {
				color: rgba(0, 0, 0, 0.6);
				font-size: 14px;
				.value {
					color: rgba(0, 0, 0, 0.9);
				}
				.fw {
					font-weight: 700;
					color: #2c86f8;
				}
			}
			.down-box {
				background: #fff;
				border-radius: 4px;
				border: 1px solid #d3d7de;
				padding: 10px;
			}
			.ellips {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				cursor: pointer;
			}
		}
	}
</style> 