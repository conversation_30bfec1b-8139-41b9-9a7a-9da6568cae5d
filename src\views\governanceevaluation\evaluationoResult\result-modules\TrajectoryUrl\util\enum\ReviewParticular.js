import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';

export const iconStaticsList = [
  {
    name: '检测轨迹数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'actualNum',
  },
  {
    name: '合格轨迹数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'qualifiedNum',
  },
  {
    name: '不合格轨迹数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'unqualifiedNum',
  },
  {
    name: '轨迹图片可访问率',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
    type: 'percent', // 百分比
  },
];
export const listFormData = [
  {
    type: 'start-end-time',
    label: '抓拍时间',
    startKey: 'beginTime',
    endKey: 'endTime',
  },
  {
    type: 'camera',
    label: '抓拍设备',
    key: 'deviceIds',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    // causeErrors
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const cardFormData = [
  {
    type: 'input',
    key: 'name',
    label: '姓名',
  },
  {
    type: 'input',
    key: 'idCard',
    label: '证件号',
  },
];

export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  { title: '人脸抓拍', slot: 'trackImage', tooltip: true, minWidth: 150 },
  { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
  { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
  {
    title: '档案照',
    slot: 'identityPhoto',
    tooltip: true,
    minWidth: 150,
  },
  { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
  { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
  {
    title: '检测结果',
    slot: 'outcome',
    minWidth: 100,
    tooltip: true,
  },
  {
    title: '不合格原因',
    key: 'firstLevelResultTip',
    minWidth: 160,
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'option',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];
