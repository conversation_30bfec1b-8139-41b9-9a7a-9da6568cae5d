<template>
  <div class="tag-add pointer" @click="click">
    <span class="icon-font icon-tianjia f-12 tag-add-title"></span>
    <span class="tag-add-title ml-xs"><slot></slot></span>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {
    click() {
      this.$emit('click');
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tag-add {
  width: 100px;
  height: 34px;
  line-height: 34px;
  margin-right: 10px;
  margin-bottom: 10px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  opacity: 1;
  border-radius: 4px;
  border: 1px solid var(--color-primary);
  .tag-add-title {
    color: var(--color-primary);
  }
}
</style>
