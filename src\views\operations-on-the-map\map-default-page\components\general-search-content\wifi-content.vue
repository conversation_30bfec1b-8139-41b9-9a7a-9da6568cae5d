<template>
  <div class="wifi-content-wrapper">
    <div class="wifi-content">
      <div v-for="(e, i) in wifiList" :key="i">
        <div class="wifi-item" :class="{ active: currentClickIndex == i }" @click="chooseMapItem(i, e)">
          <div class="header">
            <div class="header-left">
              <span class="serialNumber ellipsis" :class="{ activeNumber: currentClickIndex == i }">
                <span>{{ i + 1 }}</span>
              </span>
            </div>
            <div class="header-name">
              {{ e.mac || '--' }}
            </div>
            <div>
              <operate-bar :list='e' :tabType="{'type': 10}" @collection="collection($event, e, i)"></operate-bar>
            </div>
          </div>
          <div class="content">
            <div class="content-right">
              <!-- <span>
                <ui-icon type="MAC" :size="14"></ui-icon>
                <span>{{ e.name }}</span>
              </span> -->
              <span class="ellipsis">
                <ui-icon type="time" :size="14"></ui-icon>
                <span>{{ e.absTime || '--' }}</span>
              </span>
              <span class="ellipsis">
                <ui-icon type="location" :size="14"></ui-icon>
                <span>{{ e.placeName || '--' }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="!loading && !wifiList.length" />
    <div class="general-search-footer">
      <ui-page :simple="true" countTotal :show-elevator="false" :show-sizer="false" :total="total" :current="pageInfo.pageNumber" :page-size="pageInfo.pageSize" @pageChange="pageChange" size="small" show-total> </ui-page>
    </div>
  </div>
</template>

<script>
import { queryWifiRecordSearch } from '@/api/operationsOnTheMap';
import operateBar from '@/components/mapdom/operate-bar.vue';
import { mapMutations, mapGetters } from 'vuex';
export default {
    components: {
        operateBar
    },
    props: {
        //搜索条件
        searchPrams: {
            type: Object,
            default: () => {}
        },
        currentClickIndex: {
            type: Number,
            default: -1
        }
    },
    data() {
        return {
            wifiList: [],
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20
            },
            loading: false,
        }
    },
    computed:{
        ...mapGetters({
            getCollectJudge: 'map/getCollectJudge',
            getClickObj: 'map/getClickObj'
        })
    },
    watch:{
        getCollectJudge: {
            handler(val) {
                if(this.currentClickIndex > -1) {
                    let jude = JSON.stringify(this.getClickObj) == '{}' ? false : this.getClickObj.id == this.wifiList[this.currentClickIndex].id;
                    if(jude){
                        let collect = (val % 2) == 0 ? 2: 1;
                        this.$set(this.wifiList[this.currentClickIndex], 'myFavorite', collect)
                    }
                }
            },  
            immediate: true
        }
    },
    created() {
        this.init()
    },
    methods: {
        ...mapMutations({
            setCollectJudge: 'map/setCollectJudge', 
            setClickObj:'map/setClickObj',
        }),
        init() {
            this.queryWifiRecordSearch()
        },
        chooseMapItem(index, item) {
            this.setClickObj(item)
            this.$emit('chooseMapItem', index)
        },
        // 收藏/取消收藏
        collection($event, item, index){
            // item.myFavorite = $event;
            this.$set(this.wifiList[index], 'myFavorite', $event); //更改本页面数据
            if(this.currentClickIndex == index){
                if($event == this.getCollectJudge) {
                    let changedata = $event + 2;
                    this.setCollectJudge(changedata)
                }else {
                    this.setCollectJudge($event)
                }
                this.setClickObj(this.wifiList[index])
            }
        },
        queryWifiRecordSearch() {
            const { pageInfo, searchPrams } = this
            const params = { ...pageInfo, ...searchPrams }
            this.loading = true
            // 后端字段命名未统一
            params.devices = params.deviceIds
            delete params.deviceIds
            queryWifiRecordSearch(params)
            .then(res => {
            if (res.code === 200) {
                const {
                    data: { entities = [], total = 0 }
                } = res
                this.wifiList = entities
                this.total = total
                this.$emit('mapResultHandler', entities)
            }
            })
            .catch(() => {
                this.wifiList = []
                this.$emit('mapResultHandler', [])
            })
            .finally(() => {
                this.loading = false
            })
        },
        pageChange(pageNumber) {
            this.pageInfo.pageNumber = pageNumber
            this.queryWifiRecordSearch()
        }
    }
}
</script>

<style lang="less" scope>
.wifi-content {
  padding: 10px;
  width: 100%;
  height: calc(~'100% - 56px');
  overflow: hidden;
  overflow-y: auto;
  cursor: pointer;
  .wifi-item {
    height: 92px;
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    .header {
        display: flex;
        justify-content: space-between;
        height: 42px;
        align-items: center;
        padding-left: 10px;
      &-left {
        display: flex;
        align-items: center;
        line-height: 42px;
        .serialNumber {
          position: relative;
          display: inline-block;
          width: 32px;
          height: 32px;
          margin-right: 14px;
          color: black;
          background: url('~@/assets/img/map/trajectory-red.png') no-repeat;
          > span {
            position: absolute;
            top: -10px;
            width: 32px;
            color: #ea4a36;
            text-align: center;
          }
        }
        .activeNumber {
          background: url('~@/assets/img/map/trajectory-blue.png') no-repeat !important;
          > span {
            color: #2c86f8 !important;
          }
        }
      }
      &-name {
        width: 300px;
        font-size: 14px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        margin-left: -14px;
      }
    }
    .content {
      display: flex;
      padding: 0 10px;
      &-left {
        > img {
          width: 60px;
          height: 60px;
          border: 1px solid #d3d7de;
        }
      }
      &-right {
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        width: 330px;
        .iconfont {
          margin-right: 10px;
        }
      }
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.1);
  }
}
</style>
