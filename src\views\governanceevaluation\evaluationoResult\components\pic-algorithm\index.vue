<template>
  <div class="pic-mode auto-fill">
    <div class="pic-mode-list">
      <pic-mode-card
        v-for="(item, index) in cardList"
        :key="`${item.id}-${index}`"
        :small-img-key="smallImgKey"
        :class="['pic-mode-item', 'mb-sm', (index + 1) % picModeCount === 0 ? '' : 'mr-sm']"
        :img-index="index"
        :card-data="item"
        @handleLookScence="handleLookScence"
        @artificialReview="(cardData) => $emit('artificialReview', cardData)"
        @algorithmsReview="(cardData) => $emit('algorithmsReview', cardData)"
      >
        <div class="cardInfo" slot="cardInfo">
          <slot name="cardInfo" :item="item">
            <p
              :title="one.value"
              class="ellipsis"
              v-for="(one, index) in item.fileList"
              :key="index"
              :style="'style' in one ? one.style : {}"
            >
              <i class="icon-font mr-xs" :class="one.iconName" v-if="'iconName' in one"></i>
              <span v-if="'label' in one" class="card-info-label">{{ one.label }}</span>
              <!-- 缺失时显示显示title         -->
              <span class="lable-text" :style="'style' in one ? one.style : {}" :title="one.title || one.value">{{
                one.value || (one.render && one.render()) || '缺失'
              }}</span>
            </p>
          </slot>
        </div>
      </pic-mode-card>
    </div>
    <look-scene
      v-model="visibleScence"
      :img-list="cardList"
      :view-index="viewIndex"
      :filed-name-map="filedNameMap"
    ></look-scene>
  </div>
</template>

<script>
export default {
  name: 'pic-mode',
  props: {
    // 图片的字段
    imgKey: {
      type: String,
      default: 'imageUrl',
    },
    //小图
    smallImgKey: {
      type: String,
      default: 'imageUrl',
    },
    // 图片数据
    cardList: {
      type: Array,
      default: () => [],
    },
    picModeCount: {
      type: Number,
      default: 7,
    },
    // 自定义字段名称 [兼容取值字段不一致问题]
    filedNameMap: {
      default: () => {
        return {
          smallPicName: 'imageUrl', // 小图
          bigPicName: 'imageUrl', // 大图
          resultTip: 'resultTip', // 图片备注
          qualified: 'qualified', // 不合格
          // ...
        };
      },
    },
  },
  data() {
    return {
      visibleScence: false,
      viewIndex: 0,
    };
  },
  methods: {
    handleLookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {},
  components: {
    PicModeCard: require('./pic-mode-card.vue').default,
    LookScene: require('./look-scene.vue').default,
  },
};
</script>

<style lang="less" scoped>
.pic-mode {
  &-list {
    flex: 1;
    display: flex;
    align-content: flex-start;
    flex-wrap: wrap;
    overflow-y: auto;
  }
  &-item {
    width: calc(calc(100% - 70px) / 7);
    height: fit-content;
  }
  .cardInfo {
    width: 100%;
  }
}
[data-theme='dark'] {
  .card-info-label {
    color: #8797ac;
  }
  .lable-text {
    color: #ffffff;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .card-info-label {
    color: rgba(0, 0, 0, 0.6);
  }
  .label-text {
    color: rgba(0, 0, 0, 0.8);
  }
}
</style>
