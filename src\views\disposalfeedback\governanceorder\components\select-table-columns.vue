<template>
  <div>
    <el-popover
      ref="selectColumnPopover"
      v-model="selectColumnPopoverVisible"
      placement="bottom"
      width="200"
      trigger="click"
      @show="showPop"
    >
      <div class="select-column-box">
        <el-tree
          ref="tree"
          :props="props"
          :data="treeData"
          node-key="key"
          show-checkbox
          :default-checked-keys="defaultCheckedKeys"
        >
        </el-tree>
        <div class="mt-md t-center">
          <Button size="small" class="width-mini" @click="cancelChangeColumn">取消</Button>
          <Button size="small" class="ml-sm width-mini" type="primary" @click="certainChangeColumn" :loading="submitLoading"
            >确定</Button
          >
        </div>
      </div>
    </el-popover>
    <i class="icon-font icon-shaixuan" v-popover:selectColumnPopover title="自定义表头"></i>
  </div>
</template>
<script>
import { tableColumnsUnitstatistics, tableColumnsTypedetails } from '../util/enum.js';
import governancetask from '@/config/api/governancetask';
export default {
  props: {
    queryType: {
      type: [Number, String],
      default: 1,
    },
    statisticsType: {
      type: [Number, String],
      default: 1,
    },
    showColumns: [],
    backstageData: [],
  },
  data() {
    return {
      selectColumnPopoverVisible: false,
      unitInitColumns: tableColumnsUnitstatistics, //单元初始列
      typeInitColumns: tableColumnsTypedetails, //类型初始列
      treeData: [], //展示的数据
      // backstageData: [],//后台传来的数据
      defaultCheckedKeys: [], //默认选中的key
      props: {
        children: 'children',
        label: 'title',
      },
      submitLoading: false,
    };
  },
  methods: {
    showPop() {
      // 选中默认数据
      this.checkDefaultData();
      // 初始化数据
      this.getTreeData();
    },
    //获取数据,根据不同的queryType展示不同的树
    getTreeData() {
      this.treeData = this.statisticsType == 1 ? this.unitInitColumns() : this.typeInitColumns();
      this.removeDefaultColumns();
      // 转换数组
    },
    //匹配到用户显示的并进行选中
    checkDefaultData() {
      this.defaultCheckedKeys = this.backstageData.map((item) => {
        return item.colKey;
      });
    },
    //点击确定，发送请求并更改页面表头
    async certainChangeColumn() {
      let selectkeys = this.$refs.tree.getCheckedKeys();
      let paramTreeData = [];
      let unUseFirstCols = ['gdqs', 'gdcl', 'gdgb']; //仅作为父级展示,column-key:工单签收、工单处理、工单关闭
      if (selectkeys.length) {
        this.trans2DTo1D(this.treeData).forEach((item) => {
          if (selectkeys.includes(item.colKey) && !unUseFirstCols.includes(item.colKey)) {
            paramTreeData.push({ firstCol: item.firstCol, secondCol: item.secondCol, colKey: item.key });
          }
        });
      }

      let params = {
        // statisticsType: this.statisticsType,
        // queryType: this.queryType,
        userColFormList: paramTreeData,
      };
      try {
        this.submitLoading = true;
        await this.$http.post(governancetask.deviceWorkOrderUserColUpdate, params);
      } catch (err) {
        console.log(err);
      } finally {
        this.submitLoading = false;
      }
      this.selectColumnPopoverVisible = false;
      this.$emit('certainChangeColumn', paramTreeData);
    },
    //点击取消
    cancelChangeColumn() {
      this.selectColumnPopoverVisible = false;
    },
    /**
     * 将二维数组转换为一维数组(两层数据方法)
     * params: arr2D 2维数组，列表源数据
     * return {item,firstCol父级title，secondCol子级title，colKey：key}
     */
    trans2DTo1D(arr2D) {
      let newArr = [];
      for (let i = 0; i < arr2D.length; i++) {
        //第一级元素
        newArr.push({ ...arr2D[i], firstCol: arr2D[i].title, secondCol: arr2D[i].title, colKey: arr2D[i].key });
        //若有第二级元素
        if (arr2D[i].children && arr2D[i].children.length) {
          arr2D[i].children.forEach((item) => {
            newArr.push({ ...item, firstCol: arr2D[i].title, secondCol: item.title, colKey: item.key });
          });
        }
      }
      return newArr;
    },
    //去除默认展示的列
    removeDefaultColumns() {
      let indexIndex = this.treeData.findIndex((item) => item.type == 'index');
      this.treeData.splice(indexIndex, 1); //移除序号那一列
      //添加默认展示选项
      let unitDefaultKeys = ['orgName', 'total', 'wzpAmount', 'dcxfAmount'];
      let typeDefaultKeys = ['indexModuleName', 'indexName', 'total', 'wzpAmount'];
      let unshowKeys = this.statisticsType == 1 ? unitDefaultKeys : typeDefaultKeys;
      unshowKeys.forEach((keyItem) => {
        //展示列里清除掉默认列
        let index = this.treeData.findIndex((indexItem) => indexItem.key == keyItem);
        if (index != -1) {
          this.treeData.splice(index, 1);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.icon-shaixuan {
  color: var(--color-el-tree-node__expand-icon);
}
</style>
