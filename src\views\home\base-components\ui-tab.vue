<template>
  <div class="ui-tab-container">
    <span
      @click="clickItem(item)"
      class="ui-tab-item inline mr-sm"
      :class="item.id === activeValue ? 'link-text-active' : ''"
      v-for="item in tabData"
      :key="item.id"
      >{{ item.label }}</span
    >
  </div>
</template>

<script>
export default {
  name: 'tab-title',
  components: {},
  props: {
    data: {},
    value: {},
  },
  data() {
    return {
      tabData: [],
      activeValue: '',
    };
  },
  computed: {},
  watch: {
    data: {
      handler(val) {
        this.tabData = val || [];
      },
      deep: true,
      immediate: true,
    },
    value: {
      handler(val) {
        this.activeValue = val;
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    clickItem(val) {
      this.activeValue = val.id;
      this.$emit('input', val.id);
      this.$emit('on-change', val.id, val);
    },
  },
};
</script>

<style lang="less" scoped>
.back-color {
  color: rgba(27, 134, 255, 1);
}
.link-text {
  color: #1b86ff;
  cursor: pointer;
}
.link-text-active {
  color: #ffffff;
}
.ui-tab-container {
  position: relative;
  color: #0185f6;
  display: flex;
  .ui-tab-item {
    width: 26px;
    height: 17px;
    line-height: 17px;
    cursor: pointer;
    border: 1px solid #0185f6;
    background: #043881;
    text-align: center;
  }
}
</style>
