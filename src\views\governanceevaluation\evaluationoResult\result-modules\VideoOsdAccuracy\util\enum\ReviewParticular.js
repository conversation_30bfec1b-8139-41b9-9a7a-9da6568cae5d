import global from '@/util/global';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { iconStaticsFaceAndVehicle } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  ...iconStaticsFaceAndVehicle,
  {
    name: '字幕标注合规率',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    // causeErrors
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
    slot: 'select',
  },
];

// 字幕标注合规率
export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    minWidth: 110,
    tooltip: true,
  },
  {
    title: '检测结果',
    slot: 'outcome',
    minWidth: 85,
    tooltip: true,
  },
  {
    title: '原因',
    key: 'errorCodeName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '检测时间',
    key: 'videoStartTime',
    minWidth: 150,
    tooltip: true,
  },
  {
    minWidth: 150,
    title: '设备标签',
    slot: 'tagNames',
  },
  {
    title: '监控点位类型',
    key: 'sbdwlxText',
    align: 'left',
    tooltip: true,
    minWidth: 130,
  },
  {
    title: '设备状态',
    slot: 'phyStatus',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测方式',
    slot: 'detectionMode',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '备注',
    key: 'reason',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'option',
    align: 'center',
    tooltip: true,
    minWidth: 160,
    fixed: 'right',
    className: 'table-action-padding', // 操作栏列-单元格padding设置
  },
];

// 结果比对
export const tableColumn_comparison = [
  {
    type: 'index',
    title: '序号',
    align: 'center',
    width: 70,
  },
  {
    title: '检测批次',
    key: 'detectDate',
    slot: 'detectDate',
    align: 'left',
  },
  {
    title: '检测总量',
    key: 'detectTotal',
    align: 'left',
  },
  {
    title: '不合格数量',
    key: 'offlineQuantity',
    slot: 'offlineQuantity',
    align: 'left',
  },
  {
    title: '新增不合格数量',
    key: 'newOfflineQuantity',
    align: 'left',
  },
  {
    title: '恢复合格数量',
    key: 'restoreOnlineQuantity',
    align: 'left',
  },
  {
    title: '操作',
    slot: 'action',
    align: 'center',
    tooltip: true,
    width: 80,
    fixed: 'right',
    className: 'table-action-padding', // 操作栏列-单元格padding设置
  },
];
