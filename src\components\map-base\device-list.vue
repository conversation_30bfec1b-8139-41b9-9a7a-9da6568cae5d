<!----------------------------------------------------------------------------------
author:
date:
----------------------------------------------------------------------------------->
<template>
  <div class="device-div">
    <div class="over-flow">
      <ui-label class="mb-xs fl" label="组织机构:" width="70">
        <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree"></api-organization-tree>
      </ui-label>
      <div class="fr">
        <Icon
          type="ios-arrow-down"
          class="expand-icon pointer"
          :class="{ 'expand-icon-trun': showCamera }"
          @click="expandCamera"
        />
      </div>
    </div>
    <div v-show="showCamera">
      <div class="search-div menu-content-background">
        <Input
          search
          placeholder="检索-请输入设备名称"
          class="width-md"
          v-model="searchText"
          @on-search="searchCamaraAdd"
          @on-enter="searchCamaraAdd"
        />
      </div>
      <div class="over-flow camera-detail border">
        <div class="fl select-camera" ref="cameraBox" v-scroll="270">
          <div>
            <Checkbox class="mb-sm" v-model="mainChecked" @on-change="chooseChecked" :disabled="isView">
              <span class="base-text-color">全选</span>
            </Checkbox>
          </div>
          <div v-for="(item, index) in viewCameraList" :key="index" class="mr-sm mb-sm base-text-color fl">
            <Checkbox v-model="item.checked" @on-change="selectCamera(item)" :disabled="isView"></Checkbox>
            {{ item.deviceName }}
          </div>
          <Spin size="large" fix v-if="loading"></Spin>
        </div>
        <div class="fr border selected-div">
          <div class="selected-title over-flow">
            <span class="ml-sm base-text-color fl">已选{{ selectedCameraList.length }}个</span>
            <span v-if="!isView" class="table-text-content fr pointer base-text-color" @click="unSelectAll">清空</span>
          </div>
          <div class="selected-list" ref="selectedBox" v-scroll="290">
            <div v-for="(item, index) in viewSelectedCameraList" :key="index" :title="item.deviceName">
              <div class="over-flow selected-item" v-if="item.checked">
                <span class="base-text-color fl ellipsis width-md">{{ item.deviceName }}</span>
                <Icon
                  v-if="!isView"
                  type="ios-close"
                  class="protruding close fr base-text-color"
                  @click="unSelect(item, index)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    allDeviceList: {
      type: Array,
      default: () => [],
    },
    defaultCameraList: {
      type: Array,
      default: () => [],
    },
    loading: {
      default: false,
    },
    // 查看
    isView: {
      type: Boolean,
    },
  },
  data() {
    return {
      // 左侧摄像头选择
      selectOrgTree: {},
      cameraList: [],
      viewCameraList: [], //当摄像头过多的时候不要显示所有的摄像头只显示该数组的摄像头
      initCameraList: [], //搜索时摄像头临时存储列表 当搜空的时候 返回所有的摄像头
      selectedCameraList: [],
      viewSelectedCameraList: [],
      showCamera: false,
      searchText: '',
      mainChecked: false,
      viewNum: 200,
      cameraSliceNum: 200,
      selectedCameraSliceNum: 200,
    };
  },

  mounted() {
    this.selectOrgTree = this.defaultSelectedOrg;
    this.listenScroll(this.$refs.cameraBox, () => {
      this.cameraSliceNum += this.viewNum;
      this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
    });
    this.listenScroll(this.$refs.selectedBox, () => {
      this.selectedCameraSliceNum += this.viewNum;
      this.viewSelectedCameraList = this.selectedCameraList.slice(0, this.selectedCameraSliceNum);
    });
  },
  methods: {
    // 左侧选择
    expandCamera() {
      this.showCamera = !this.showCamera;
    },
    //-----------------根据名字检索摄像机函数---------------------------
    searchCamaraAdd() {
      let searchArr = [];
      if (this.searchText !== '') {
        for (let i = 0; i < this.initCameraList.length; i++) {
          let str = this.initCameraList[i].deviceName || '';
          if (str.indexOf(this.searchText) !== -1) {
            searchArr.push(this.initCameraList[i]);
          }
        }
        this.cameraList = searchArr;
      } else {
        this.cameraList = this.initCameraList;
      }
      this.viewCameraList = this.muchCamera(this.cameraList, this.cameraSliceNum);
    },
    //------------------全选所有摄像机函数---------------------------------
    chooseChecked(val) {
      this.cameraList.forEach((row) => {
        row.checked = val;
      });
      if (val) {
        this.selectedCameraList = this.$util.common.deepCopy(this.cameraList);
        this.viewSelectedCameraList = this.muchCamera(this.selectedCameraList, this.selectedCameraSliceNum);
      } else {
        this.selectedCameraList = [];
        this.selectedCameraSliceNum = this.viewNum;
        this.viewSelectedCameraList = [];
      }
    },
    muchCamera(totalList, sliceNum) {
      sliceNum = this.viewNum;
      return totalList.slice(0, sliceNum);
    },
    //监听滑动距离加载隐藏的摄像机列表
    listenScroll(dom, callback) {
      dom.addEventListener('scroll', this.scrollMethod(dom, callback), false);
    },
    scrollMethod(dom, callback) {
      return () => {
        //  判断是否滚动到底部，如果滚动到底部则则再加载200个摄像头
        if (!!dom.scrollTop && dom.scrollTop + dom.clientHeight === dom.scrollHeight) {
          //到底部要做的操作
          callback();
        }
      };
    },
    searchChild(orgCodeList, parentId) {
      this.initialOrgList.forEach((row) => {
        if (parentId === row.parentId) {
          orgCodeList.push(row);
          this.searchChild(orgCodeList, row.id);
        }
      });
    },
    selectedOrgTree(val) {
      this.$emit('selectedOrgTree', val);
      this.changeOrg(val);
    },
    changeOrg(val) {
      this.cameraList = [];
      // 获取机构列表包括已选择的该组织机构下的子列表
      let orgCodeList = [val];
      this.searchChild(orgCodeList, val.id);
      let tempObj = {}; //一个临时对象
      // 将一个数组转换为一个对象减少for循环
      orgCodeList.forEach((row) => {
        tempObj[row.orgCode] = row;
      });
      //从所有的摄像机列表中和选中的树结构的区域编码做对比，如果是选中的区域编码则加入中间的摄像机列表
      this.allDeviceList.forEach((row) => {
        let device = this.$util.common.deepCopy(row);
        if (tempObj[device.orgCode]) {
          this.cameraList.push(device);
        }
      });
      //提取一个对象减少for循环
      let tempObj2 = {};
      this.selectedCameraList.forEach((row) => {
        tempObj2[row.deviceId] = row.deviceId;
      });
      //初始化中间摄像机列表时，要跟右边已选中摄像机列表做对比，选中摄像机
      this.cameraList = this.cameraList.map((row) => {
        if (tempObj2[row.deviceId]) {
          this.$set(row, 'checked', true);
        } else {
          this.$set(row, 'checked', false);
        }
        return row;
      });
      //当摄像头过多时进行处理不显示所有摄像头只先显示200个
      this.viewCameraList = this.muchCamera(this.cameraList, this.cameraSliceNum);
      this.initCameraList = this.cameraList;
      this.showCamera = true;
    },
    check(checkedKeys) {
      this.cameraList = [];
      let tempObj = {}; //一个临时对象
      // 将一个数组转换为一个对象减少for循环
      checkedKeys.forEach((row) => {
        tempObj[row] = row;
      });
      this.isCheckAll();
    },
    //中间摄像头列表选中
    selectCamera(item) {
      //这里因为不想要中间的摄像头列表影响到右边选中的摄像头列表所以需要深度克隆
      let temp = this.$util.common.deepCopy(item);
      let index = this.selectedCameraList.findIndex((row) => {
        return temp.deviceId === row.deviceId;
      });
      if (temp.checked) {
        if (index === -1) {
          this.selectedCameraList.push(temp);
        }
      } else {
        this.selectedCameraList.splice(index, 1);
      }
      this.viewSelectedCameraList = this.muchCamera(this.selectedCameraList, this.selectedCameraSliceNum);
      this.isCheckAll();
    },
    // 检查是否全选所有中间摄像机列表
    isCheckAll() {
      if (this.cameraList.length === 0) {
        this.mainChecked = false;
        return false;
      }
      this.mainChecked = true;
      this.cameraList.forEach((row) => {
        if (!row.checked) this.mainChecked = false;
      });
    },
    unSelectAll() {
      this.cameraList.forEach((row) => {
        row.checked = false;
      });
      this.initCameraList.forEach((row) => {
        row.checked = false;
      });
      this.selectedCameraSliceNum = this.viewNum;
      this.selectedCameraList = [];
      this.viewSelectedCameraList = [];
      this.mainChecked = false;
    },
    unSelect(item, index) {
      item.checked = false;
      let temp = this.$util.common.deepCopy(item);
      let obj = this.cameraList.find((row) => {
        return temp.deviceId === row.deviceId;
      });
      if (obj) {
        obj.checked = item.checked;
      }
      this.selectedCameraList.splice(index, 1);
      this.isCheckAll();
    },
  },
  watch: {
    defaultCameraList: {
      handler(val) {
        if (!val) return;
        this.selectedCameraList = val.map((row) => {
          this.$set(row, 'checked', true);
          return row;
        });
        this.viewSelectedCameraList = this.muchCamera(this.selectedCameraList, this.selectedCameraSliceNum);
      },
      immediate: true,
    },
    selectedCameraList(val) {
      this.$emit('putCameraList', val);
    },
    allDeviceList: {
      handler(val) {
        if (val && val.length) {
          this.changeOrg(this.selectOrgTree);
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .device-div {
    background-color: rgba(0, 0, 0, 0.7);
    .expand-icon {
      color: #ffffff;
    }
    .selected-item {
      &:hover {
        background-color: #101e2b !important;
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .device-div {
    background-color: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    margin: 6px;
    max-height: calc(100% - 12px);
    .expand-icon {
      color: #808695;
    }
    .selected-item {
      &:hover {
        background-color: #d5e7fe !important;
      }
    }
  }
}

.device-div {
  position: absolute;
  right: 0;
  top: 0;
  width: 500px;
  z-index: 999;
  padding: 5px;
  .expand-icon {
    font-size: 20px;
    line-height: 32px;
    transition: all 0.2s;
  }
  .expand-icon-trun {
    transform: rotate(180deg);
  }
  .area-name {
    width: 200px;
    position: absolute;
    top: 60px;
    left: 20px;
    z-index: 400;
  }
  .search-div {
    padding: 10px;
  }
  .camera-detail {
    border-top: 1px solid var(--devider-line);
    .select-camera {
      padding: 5px;
      width: 200px;
    }
    .selected-div {
      padding: 5px;
      border-left: 1px solid var(--devider-line);
      width: 280px;
      .selected-list {
        padding: 5px 0;
        .close {
          font-size: 20px;
          cursor: pointer;
        }
        .selected-item {
          padding: 5px 10px;
        }
      }
    }
  }
}
</style>
