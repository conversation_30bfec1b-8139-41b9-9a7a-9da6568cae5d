<template>
  <div class="group-tree">
    <ui-loading v-if="loading" />
    <div class="search-top">
      <Input
        placeholder="请输入"
        v-model="searchInput"
        @keydown.enter.native="searchName"
      >
        <Icon
          type="ios-search"
          class="font-16 cursor-p"
          slot="suffix"
          maxlength="50"
          @click.prevent="searchName"
        />
      </Input>
    </div>

    <xn-tree
      class="deviceTree"
      :ref="'tree'"
      :option="option"
      :label="labelFn"
      :fileOpe="fileOpe"
      @dblclickNode="dblclickNode"
      @startInspect="startInspect"
    ></xn-tree>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  queryMyVideoGroupList,
  queryVideoDeviceGroupList,
  removeVideoFromGroup,
} from "@/api/player";
import { copyText } from "@/util/modules/common";
import xnTree from "@/components/xn-tree/index.vue";
import inspectMixin from "../mixins/inspect-mixin";

export default {
  components: {
    xnTree,
  },
  data() {
    return {
      searchInput: "",
      loading: false,
      firstInit: true,
      treeData: [],
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: null, //radio,null,checkbox
        canMove: false,
        draggable: true,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            return new Promise((resolve) => {
              this.loading = true;
              queryVideoDeviceGroupList({ groupId: node.id }).then((res) => {
                let deviceList = res.data
                  ? res.data.map((v) => {
                      v.id = v.deviceId;
                      v.label = v.deviceName;
                      (v.isLeaf = true),
                        (v.ptzType = v.deviceChildType
                          ? v.deviceChildType
                          : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
                      return v;
                    })
                  : [];
                this.loading = false;
                resolve(deviceList);
              });
            });
          },
        },
      },
      fileOpe: [
        {
          label: "查看视频",
          show: this.$_has(["video-realTime"]),
          clickFn: (item) => {
            this.dblclickNode(item);
          },
        },
        // {
        //   label: '历史录像',
        //   clickFn: (item) => {
        //     this.dblclickNode(item, 'vod')
        //   }
        // },
        {
          label: "查看档案",
          show: true,
          clickFn: (item) => {
            const { href } = this.$router.resolve({
              name: "device-archive",
              query: { archiveNo: item.deviceId },
            });
            window.open(href, "_blank");
          },
        },
        {
          label: "移出分组",
          show: true,
          clickFn: (item) => {
            this.$Modal.confirm({
              title: "提示",
              closable: true,
              content: `确定移出分组吗？`,
              onOk: () => {
                let parentNode = item.$pId
                  ? this.$refs.tree.xnTree.getNodeById(item.$pId)
                  : {};
                removeVideoFromGroup({
                  deviceId: item.deviceId,
                  groupId: parentNode.id,
                }).then((res) => {
                  if (res.code == 200) {
                    this.$refs.tree.xnTree.deleteNode(item.deviceId);
                    this.$Message.success("移出成功");
                  } else {
                    this.$Message.error(res.msg);
                  }
                });
              },
            });
          },
        },
        {
          label: "复制名称",
          show: true,
          clickFn: (item) => {
            copyText(item.deviceName);
          },
        },
        // {
        //   label: "复制国标编号",
        //   show: true,
        //   clickFn: (item) => {
        //     copyText(item.deviceGbId);
        //   },
        // },
        // {
        //   label: "发送工单",
        //   show: true,
        //   clickFn: (item) => {
        //     Toolkits.toServiceCatalog(this.userInfo.username, item.deviceGbId);
        //   },
        // },
        // {
        //   label: "查询工单",
        //   show: true,
        //   clickFn: (item) => {
        //     Toolkits.toProcessOrder(this.userInfo.username, item.deviceGbId);
        //   },
        // },
      ],
    };
  },
  mixins: [inspectMixin],
  props: {
    playingDeviceIds: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      ignoreDevices: "player/getIgnoreDevices",
    }),
  },
  watch: {
    playingDeviceIds: {
      handler(val) {
        if (this.$refs.tree.xnTree) this.$refs.tree.xnTree.refreshDom();
      },
    },
  },
  mounted() {
    this.getParentData();
  },
  methods: {
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let playingClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing"
          : "";
      let iconClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing-icon"
          : !data.deviceId
          ? data.$expanded
            ? "icon-folder-open-fill"
            : "icon-folder-fill"
          : this.getDeviceIcon(data);
      let iconColor = this.getDeviceIconColor(data);
      let ignoreClass = this.ignoreDevices.includes(data.deviceId)
        ? "ignore"
        : "";
      let operate = "";
      if (
        this.$_has(["video-realTime"]) &&
        data.children &&
        data.children.find((v) => v.isLeaf)
      ) {
        // operate += `<i class="iconfont operate startInspect icon-lunxunkaishi" title="轮巡播放"></i>`
      }
      let total = data.total ? "(" + data.total + ")" : "";
      let html = `<div class="node-title ${titleClass} ${playingClass} ${ignoreClass}">
            <i class="iconfont ${iconClass}" style="color: ${iconColor}"></i>
            <span class="label 111">${data.label}${total}</span>
          </div>
          <div class="operateBtns">${operate}</div>
          `;
      return html;
    },
    getParentData() {
      this.loading = true;
      queryMyVideoGroupList({
        userId: this.userInfo.id,
        searchKey: this.searchInput,
      }).then((res) => {
        let groupList = res.data.grouplist
          ? res.data.grouplist.map((v) => {
              v.label = v.groupName;
              return v;
            })
          : [];
        let deviceList = res.data.deviceList
          ? res.data.deviceList.map((v) => {
              v.id = v.deviceId;
              v.label = v.deviceName;
              v.isLeaf = true;
              return v;
            })
          : [];
        this.loading = false;
        this.firstInit = false;
        this.treeData = [...groupList, ...deviceList];
        this.$refs.tree.initTree(this.treeData);
      });
    },
    dblclickNode(nodeData, playType = "live") {
      if (nodeData.deviceId) {
        let obj = { ...nodeData };
        this.$emit("handleClick", { ...obj, devicetype: liveType, playType });
        if (playType == "live") {
          this.queryLog({
            muen: "视频中心",
            name: "电视墙",
            type: "4",
            remark: `【${nodeData.deviceName}】视频上墙`,
          });
        }
      }
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
    searchName() {
      this.getParentData();
    },
  },
};
</script>
<style lang="less" scoped>
.group-tree {
  @import "./style/index";
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
</style>
