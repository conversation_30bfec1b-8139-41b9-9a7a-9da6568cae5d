<template>
  <div class="content auto-fill">
    <div class="container auto-fill" v-if="!componentName">
      <div class="indexparticulars-wrapper">
        <div class="fl label-text">
          <div class="fl">
            <i class="icon-font f-16 icon-renwushuliang-01 inline"></i>
            <span class="base-text-color">
              任务名称:
              <span class="label-text">
                {{ detailStatistic.taskName || '未知' }}
              </span>
            </span>
          </div>
          <div class="ml_lg fl">
            <i class="icon-font f-16 icon-pingcefangan-01 inline"></i>
            <span class="base-text-color">
              评测方案:
              <span class="label-text">
                {{ detailStatistic.schemeName || '未知' }}
              </span>
            </span>
          </div>
          <div class="ml_lg fl">
            <i class="icon-font f-18 icon-jianceduixiang inline"></i>
            <span class="base-text-color">
              检测对象:
              <span class="label-text">
                {{ detailStatistic.regionName || '未知' }}
              </span>
            </span>
          </div>
        </div>
      </div>
      <div class="ploe-statistics">
        <statistic-card :statisticsList="statisticsList"></statistic-card>
      </div>
      <div class="search-wrapper">
        <ui-label label="指标名称" :width="70" class="inline">
          <Input
            class="width-md"
            @keyup.enter.native="search"
            v-model="searchData.indexName"
            placeholder="请输入指标名称"
          ></Input>
        </ui-label>

        <ui-label class="ml-lg inline" label="指标类型" :width="70">
          <Select class="width-md" placeholder="请选择指标类型" clearable v-model="searchData.indexModule">
            <Option v-for="(item, index) in global.indexTypeList" :key="index" :value="item.id">{{
              item.title
            }}</Option>
          </Select>
        </ui-label>
        <ui-label label="检测状态" :width="65" class="ml-lg inline">
          <Select
            class="width-md"
            v-model="searchData.taskStatus"
            placeholder="请选择检测状态"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in taskStatusList" :key="index" :value="item.taskKey"
              >{{ item.taskValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label :width="0" label=" " class="inline ml-lg">
          <Button type="primary" class="mr-sm inline" @click="search">查询 </Button>
          <Button class="mr-lg" @click="resetSearchDataMx(searchData, search)"> 重置 </Button>
        </ui-label>
        <Button
          type="primary"
          @click="batchTriggerIndexJob('batch')"
          class="fr"
          v-permission="{
            route: $route.name,
            permission: 'batchStart',
          }"
        >
          <i class="icon-font icon-qidong mr-xs font-white"></i>
          <span class="inline vt-middle">批量启动</span>
        </Button>
        <Button
          type="primary"
          @click="batchTerminateRunningJob('batch')"
          class="fr mr-md"
          v-permission="{
            route: $route.name,
            permission: 'batchPause',
          }"
        >
          <i class="icon-font icon-zanting-ivdg mr-xs font-white"></i>
          <span class="inline vt-middle">批量暂停</span>
        </Button>
      </div>
      <div class="table-box navBarWrap auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          @selectTable="onSelectionChange"
        >
          <!-- 指标名称增加复检中显示 -->
          <template #indexName="{ row }">
            <div class="d_flex name-box">
              <Tooltip max-width="300" class="tooltip-cell-box">
                <span class="ellipsis inline width-indexName vt-middle">{{ row.indexName }}</span>
                <template #content>
                  {{ row.indexName }}
                </template>
              </Tooltip>
              <i
                class="icon-font icon-wenhao vt-middle icon-warning ml-sm icon-details"
                @click="openDetailsDialog(row)"
              ></i>
              <span class="font-orange inline vt-middle" v-if="row?.reinspectStatus === '1'">（复检中）</span>
            </div>
          </template>
          <template #detectPlan="{ row }">
            <span>{{ row.detectPlan || '' }}</span>
          </template>
          <template #taskStatus="{ row }">
            <span
              class="check-status"
              :class="[
                row.taskStatus == 0 ? 'bg-787878' : '',
                row.taskStatus == 1 ? 'bg-D9B916' : '',
                row.taskStatus == 2 ? 'bg-success' : '',
                row.taskStatus == 3 ? 'bg-failed' : '',
                row.taskStatus == 4 ? 'bg-failed' : '',
                row.taskStatus == 5 ? 'bg-failed' : '',
              ]"
            >
              {{ row.taskStatusText }}
            </span>
          </template>
          <template #indexType="{ row }">
            <span>{{ handleIndexType(row.indexModule) }}</span>
          </template>
          <template #schemeType="{ row }">
            {{ getSchemeType(row.schemeType) }}
          </template>
          <template #spendTime="{ row }">
            <span class="ml-lg"> {{ row.spendTime }}</span>
          </template>
          <template #taskProgress="{ row }">
            <div>
              <div class="progress-box">
                <div class="progress">
                  <span class="task-progress f-12">{{ row.taskProgress }}</span>
                  <Progress
                    class="inline progress1"
                    style="border: 1px solid #787878"
                    v-if="row.taskStatus == 0"
                    :stroke-width="15"
                    :percent="row.taskProgressPercent"
                    :stroke-color="row.taskProgressPercent < 105 ? '#787878' : ''"
                    :hide-info="true"
                  ></Progress>
                  <Progress
                    class="inline progress2"
                    style="border: 1px solid #d9b916"
                    v-if="row.taskStatus == 1"
                    :stroke-width="15"
                    :percent="row.taskProgressPercent"
                    :stroke-color="row.taskProgressPercent < 105 ? '#D9B916' : ''"
                    :hide-info="true"
                  ></Progress>
                  <Progress
                    class="inline progress3"
                    v-if="row.taskStatus == 2"
                    style="border: 1px solid #0e8f0e"
                    :stroke-width="15"
                    :percent="row.taskProgressPercent"
                    :stroke-color="row.taskProgressPercent < 105 ? '#0E8F0E' : ''"
                    :hide-info="true"
                  ></Progress>
                  <Progress
                    class="inline progress4"
                    v-if="row.taskStatus == 3"
                    style="border: 1px solid #bc3c19"
                    :stroke-width="15"
                    :percent="row.taskProgressPercent"
                    :stroke-color="row.taskProgressPercent < 105 ? '#BC3C19' : ''"
                    :hide-info="true"
                  ></Progress>
                  <Progress
                    class="inline progress5"
                    v-if="row.taskStatus == 4"
                    style="border: 1px solid #bc3c19"
                    :stroke-width="15"
                    :percent="row.taskProgressPercent"
                    :stroke-color="row.taskProgressPercent < 105 ? '#BC3C19' : ''"
                    :hide-info="true"
                  ></Progress>
                  <Progress
                    class="inline progress6"
                    v-if="row.taskStatus == 5"
                    style="border: 1px solid #bc3c19"
                    :stroke-width="15"
                    :percent="row.taskProgressPercent"
                    :stroke-color="row.taskProgressPercent < 105 ? '#BC3C19' : ''"
                    :hide-info="true"
                  ></Progress>
                </div>
                <span
                  :class="[
                    row.taskStatus == 0 ? 'font-grey' : '',
                    row.taskStatus == 1 ? 'font-D9B916' : '',
                    row.taskStatus == 2 ? 'font-green' : '',
                    row.taskStatus == 3 ? 'color-failed' : '',
                    row.taskStatus == 4 ? 'color-failed' : '',
                    row.taskStatus == 5 ? 'color-failed' : '',
                  ]"
                  class="text"
                  >{{ row.taskProgressPercent }}%</span
                >
              </div>
            </div>
          </template>
          <template #taskProgressPercent="{ row }">
            <div class="progress-task-box">
              <div class="progress">
                <Progress
                  :percent="row.taskProgressPercent || 0"
                  stroke-color="var(--color-progress-success)"
                  :hide-info="true"
                ></Progress>
                <span class="text font-green">{{ row.taskProgressPercent || 0 }}%</span>
              </div>
            </div>
          </template>
          <template #option="{ row, index }">
            <ui-btn-tip
              v-permission="{
                route: $route.name,
                permission: 'config',
              }"
              icon="icon-bianji2"
              content="编辑"
              class="mr-md ml_md"
              :class="row.taskStatus != 1 ? 'btn-text-default' : 'disable-text-color'"
              @click.native="getConfiguration('configuration', row, index)"
            ></ui-btn-tip>
            <ui-btn-tip
              v-permission="{
                route: $route.name,
                permission: 'triggerTask',
              }"
              class="mr-md"
              icon="icon-zhongxinzhihang"
              content="立即检测"
              @click.native="execute(row)"
              :class="
                row.config == true && row.taskStatus != 1 && !hasDependentParent(row)
                  ? 'btn-text-default'
                  : 'disable-text-color'
              "
            ></ui-btn-tip>
            <ui-btn-tip
              v-permission="{
                route: $route.name,
                permission: 'evaluationResult',
              }"
              v-if="row.batchId && isExistIndex(row.indexType)"
              class="mr-md"
              icon="icon-chakanxiangqing"
              content="检测结果"
              @handleClick="handleClickJump(row)"
            ></ui-btn-tip>
            <create-tabs
              v-permission="{
                route: $route.name,
                permission: 'evaluationResult',
              }"
              v-if="row.batchId && !isExistIndex(row.indexType)"
              class="mr-md inline"
              :componentName="themData.componentName"
              :tabs-text="themData.text"
              @selectModule="selectModule"
              :tabs-query="{
                indexId: row.indexId,
                code: row.regionCode,
                access: 'TASK_RESULT',
                batchId: row.batchId,
                startTime: row.lastTaskStartTime,
                indexType: row.indexType,
              }"
            >
              <ui-btn-tip content="检测结果" icon="icon-chakanxiangqing"></ui-btn-tip>
            </create-tabs>
            <ui-btn-tip
              v-permission="{
                route: $route.name,
                permission: 'evaluationResult',
              }"
              class="mr-md"
              v-if="row.batchId == null"
              content="检测结果"
              icon="icon-chakanxiangqing"
              :class="row.batchId == null && row.batchId !== '' ? 'disable-text-color' : 'btn-text-default'"
            ></ui-btn-tip>
            <ui-btn-tip
              v-permission="{
                route: $route.name,
                permission: 'taskTerminate',
              }"
              @click.native="environ(row)"
              class="mr-md"
              content="任务终止"
              icon="icon-renwuzhongzhi"
              :class="row.taskStatus != 1 ? 'disable-text-color' : 'btn-text-default'"
            ></ui-btn-tip>
            <Tooltip
              v-permission="{
                route: $route.name,
                permission: 'pauseStart',
              }"
              :content="row.status == 1 ? '任务暂停' : '任务启动'"
            >
              <i-switch
                :disabled="row.taskStatus == 1 || row.config == false"
                :value="row.status === '1' ? true : false"
                :before-change="() => handleBeforeChange(row)"
                size="small"
              >
              </i-switch>
            </Tooltip>

            <!-- <ui-btn-tip
            class="mr-lg  f-16"
            icon="icon-yunhanghuanjingzijian-01"
            content="运行环境自检"
          ></ui-btn-tip> -->
          </template>
        </ui-table>
      </div>
    </div>
    <keep-alive>
      <component :is="componentName"></component>
    </keep-alive>
    <!-- <ui-page
      class="page menu-content-background"
      :page-data="searchData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page> -->
    <BasicInformation
      ref="BasicInformation"
      :module-action="moduleAction"
      :modalData="modalData"
      @updateInfo="search"
    />
    <!-- 指标配置详情 -->
    <common-model ref="model" v-model="indexVisible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>
<style lang="less">
.ivu-tooltip-arrow {
  right: 32px !important;
}
// @{_deep}.ivu-tooltip-popper {
//   .ivu-tooltip-content {
//     &[x-placement='bottom-end'] {
//       .ivu-tooltip-arrow {
//         right: 32px !important;
//       }
//     }
//   }
// }
</style>
<style lang="less" scoped>
.navBarWrap {
  //position: sticky !important;
  //width: 100%;
  @media screen and (max-width: 1366px) {
    // @{_deep} .ivu-table .ivu-table-fixed-right {
    //   width: 170px !important; /*no*/
    // }
    .icon-wenhao {
      margin-right: 60px !important; /*no*/
    }
    @{_deep}.ivu-progress-bg {
      height: 10px !important; /*no*/
    }
  }
}
.content {
  background: var(--bg-content);
  background-size: 100% 100%;
  height: 100%;
  padding: 0 15px; /*no*/
  //position: relative;
  //overflow-y: auto;
  //&::-webkit-scrollbar {
  //  display: none; /* Chrome Safari */
  //}
  .ploe-statistics {
    margin: 3px 0 10px 0; /*no*/
  }

  .indexparticulars-wrapper {
    overflow: hidden;
    //padding-left: 20px !important;
    margin-top: 20px;
    .label-text {
      color: var(--color-primary);
      font-size: 14px;
    }
    .label-content {
      margin-left: 50px;
    }
  }
  @{_deep}.search-wrapper {
    //padding: 0 20px;
    .ivu-input {
      height: 32px;
    }
    .ui-label {
      line-height: 30px;
    }
    .ivu-select {
      height: 30px;
    }
  }
  .table-box {
    padding: 10px 0;
    position: relative;
    .sucess {
      color: @color-success;
    }
    .error {
      color: @color-failed;
    }
    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    @{_deep} .ivu-table {
      //&-tip,
      //&-overflowX {
      //  min-height: 450px !important;
      //}
      &-tbody,
      &-body {
        td {
          padding: 10px 0 10px 0;
        }
      }
    }
  }
}
// @{_deep}.ivu-table-header {
//   .ivu-table-fixed-right {
//     .ivu-table-fixed-header {
//       @{_deep}.ivu-table-cell {
//         width: 100% !important;
//       }
//     }
//   }
// }
@{_deep}.ivu-table-cell {
  width: 100% !important;

  .icon-chakanxiangqing {
    color: rgba(67, 140, 255, 1) !important;
  }
  .icon-lijijiance-01 {
    font-size: 20px !important;
  }
  .icon-yunhanghuanjingzijian-01 {
    font-size: 20px !important;
  }
}
.ml_lg {
  margin-left: 50px;
}
.ml_lgg {
  margin-left: 70px;
}

.operat {
  width: 100%;
  li {
    padding: 6px 18px;
    color: #ffffff;
    cursor: pointer;
    &:hover {
      background: #07346e;
    }
  }
  .disable-text-color {
    cursor: not-allowed !important;
  }
}
.progress-box {
  display: flex;
  .task-progress {
    position: absolute;
    z-index: 6;
    margin-top: 1px; /*no*/
  }
  @{_deep}.ivu-progress-bg {
    height: 94% !important;
  }
  @{_deep}.ivu-progress {
    border-radius: 20px;
    height: 21px; /*no*/
    line-height: 18px; /*no*/
    padding: 0px 1px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .progress1 {
    @{_deep}.ivu-progress-outer {
      background-color: #000000;
    }
  }
  .progress2 {
    @{_deep}.ivu-progress-outer {
      background-color: #4b4008;
    }
  }
  .progress3 {
    @{_deep}.ivu-progress-outer {
      background-color: #084b08;
    }
  }
  .progress4 {
    @{_deep}.ivu-progress-outer {
      background-color: #531a0a;
    }
  }
  .progress5 {
    @{_deep}.ivu-progress-outer {
      background-color: #531a0a;
    }
  }
  .progress6 {
    @{_deep}.ivu-progress-outer {
      background-color: #531a0a;
    }
  }
  @{_deep}.ivu-progress-outer {
    border-radius: 20px;
    height: 15px;
    width: 98.9%;
    position: relative;
  }

  @{_deep}.ivu-progress-inner {
    background-color: transparent;
    height: 16px;
    position: absolute;
    top: 0;
  }
  @{_deep}.ivu-progress-text-inner {
    color: var(--color-primary) !important;
  }
}
// 更改进度条
@{_deep} .progress-task-box {
  .ivu-progress {
    .ivu-progress-outer {
      .ivu-progress-inner {
        border: 1px solid var(--color-progress-success);
        background: transparent;
        padding: 1px;
        .ivu-progress-bg {
          height: 8px !important;
          max-width: 100% !important;
        }
      }
    }
  }
  .text {
    color: var(--color-progress-success);
  }
}
.progress,
.progress-task-box {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.text {
  width: 60px; /*no*/
  margin-left: 2px; /*no*/
}

@{_deep}.btn-text-default {
  cursor: pointer !important;
  font-size: 14px !important;
  .icon-zhongxinzhihang {
    cursor: pointer !important;
  }
  .icon-shanchurenwu {
    cursor: pointer !important;
  }
  .icon-bianjirenwu {
    cursor: pointer !important;
  }
  .icon-chakanxiangqing {
    cursor: pointer !important;
  }
  .icon-renwuzhongzhi {
    color: #cf3939 !important;
    cursor: pointer !important;
  }
}
@{_deep}.disable-text-color {
  font-size: 14px !important;
  cursor: not-allowed !important;

  .icon-chakanxiangqing {
    color: var(--color-btn-primary-disabled) !important;
    cursor: not-allowed !important;
  }
  .icon-zhongxinzhihang {
    color: var(--color-btn-primary-disabled) !important;
    cursor: not-allowed !important;
  }
  .icon-bianji2 {
    color: var(--color-btn-primary-disabled) !important;
    cursor: not-allowed !important;
  }
  .icon-renwuzhongzhi {
    color: var(--color-btn-primary-disabled) !important;
    cursor: not-allowed !important;
  }
}
.ml_md {
  margin-left: 20px;
}
.mt_sm {
  margin-top: 8px;
}
.option-content {
  width: 100% !important;
}
.option {
  color: #297ed9;
}
.name-box {
  width: 100%;
  /deep/ .ivu-tooltip-popper {
    left: 80px !important;
  }
  /deep/ .ivu-tooltip-rel {
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  .tooltip-cell-box {
    max-width: calc(100% - 100px);
  }
  .icon-details {
    width: 18px;
    display: none;
  }
}
/deep/ .ivu-table-row:hover {
  .icon-details {
    display: block;
  }
}
</style>

<script>
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
import { checkConfigmodule, detectionRules } from '../configuration/static-field';
export default {
  name: 'evaluationDetail',
  mixins: [dealWatch, evaluationoResultMixin],
  data() {
    return {
      currentRow: {},
      indexVisible: false,
      componentLevel: 1, //组件标签层级 如果是标签组件套用标签组件需要此参数
      themData: {
        componentName: 'taskToOverview', // 需要跳转的组件名
        text: '评测结果', // 跳转页面标题
        title: '评测结果',
        type: 'view',
      },
      componentName: null,
      btnList: [],
      taskStatusList: [
        { taskKey: '0', taskValue: '未开始' },
        { taskKey: '1', taskValue: '运行中' },
        { taskKey: '2', taskValue: '已完成' },
        { taskKey: '3', taskValue: '失败' },
        { taskKey: '4', taskValue: '任务异常' },
        { taskKey: '5', taskValue: '任务终止' },
      ],
      id: 1,
      orgCode: '',
      loading: false,
      addorEditModalShow: false,
      moduleList: [],
      selection: [],
      tableColumns: [
        {
          width: 50,
          type: 'selection',
          align: 'center',
        },
        {
          type: 'index',
          tooltip: true,
          width: 50,
          title: '序号',
          align: 'center',
        },
        {
          title: '指标名称',
          slot: 'indexName',
          tooltip: true,
          align: 'left',
          minWidth: 350,
        },
        {
          title: '指标类型',
          key: 'indexType',
          slot: 'indexType',
          tooltip: true,
          align: 'left',
          minWidth: 150,
          sortable: true,
        },
        {
          title: '检测计划',
          slot: 'detectPlan',
          tooltip: true,
          align: 'left',
          minWidth: 160,
        },
        {
          title: '检测方式',
          key: 'detectModeText',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        // {
        //   title: '运行环境状态',
        //   key: 'operating0Status',
        //   tooltip: true,
        //   align: 'left',
        //   width: 120,
        // },
        {
          title: '当前状态',
          key: 'taskStatus',
          width: 100,
          slot: 'taskStatus',
          tooltip: true,
          align: 'left',
          sortable: true,
        },
        // {
        //   title: '任务进度',
        //   key: 'taskProgress',
        //   slot: 'taskProgress',
        //   align: 'left',
        //   width: 265,
        //   renderHeader: (h) => {
        //     return <span class="ml_lgg">任务进度</span>
        //   },
        // },
        {
          title: '检测进度',
          key: 'taskProgressPercent',
          slot: 'taskProgressPercent',
          align: 'left',
          width: 180,
        },
        {
          title: '已耗时',
          slot: 'spendTime',
          tooltip: true,
          align: 'left',
          width: 180,
          renderHeader: (h) => {
            return <span class="ml-lg">已耗时</span>;
          },
        },
        {
          title: '最新检测时间',
          key: 'latestDetectTime',
          tooltip: true,
          align: 'left',
          minWidth: 180,
        },
        {
          title: '最近一次耗时',
          key: 'latestSpendTime',
          tooltip: true,
          align: 'left',
          width: 150,
        },
        {
          title: '总执行次数',
          key: 'detectCount',
          tooltip: true,
          align: 'left',
          width: 100,
        }, //没有
        {
          title: '平均耗时',
          key: 'averageSpendTime',
          tooltip: true,
          align: 'left',
          width: 120,
        },
        {
          title: '操作',
          slot: 'option',
          tooltip: true,
          width: 200,
          align: 'left',
          fixed: 'right',
          renderHeader: (h) => {
            return (
              <Tooltip max-width="200" transfer class="option-content" placement="bottom-end">
                <span class="vt-middle fl ml_md mt_sm">操作</span>
                <i class="icon-font icon-wenhao vt-middle icon-warning fr mr-lg  mt_sm"></i>
                <template slot="content">
                  <p class="mb-md">
                    <span class="f-12 option">任务终止</span>
                    <span class="f-12">：终止当前正在执行的检测任务，不影响下一次触发任务。</span>
                  </p>
                  <p class="mb-md">
                    <span class=" f-12 option">任务暂停</span>
                    <span class=" f-12">：暂停此任务的执行计划，以后不会再被触发，直至任务重新启动。</span>
                  </p>
                </template>
              </Tooltip>
            );
          },
        },
      ],
      resultList: [
        { name: '合格', value: '1', index: 0 },
        { name: '不合格', value: '2', index: 1 },
        { name: '无法考核', value: '3', index: 2 },
      ],
      tableData: [],
      minusTable: 288,
      addorEditModalAction: {
        title: '新增',
        action: 'add',
        visible: false,
      },
      modalData: {},
      editList: {},
      org: '',
      examineData: [],
      configurationShow: false,
      configurationAction: {},
      statisticsList: [
        {
          name: '指标数量',
          value: 0,
          icon: 'icon-renwushuliang-01',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          textColor: 'color1',
        },
        {
          name: '进行中指标',
          value: 0,
          icon: 'icon-jinhangzhong-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
        },

        {
          name: '今日待执行指标',
          value: 0,
          icon: 'icon-jinridaizhihang-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          textColor: 'color5',
        },
        {
          name: '今日已完成指标',
          value: 0,
          icon: 'icon-jinriyiwancheng-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
        {
          name: '任务异常指标',
          value: 0,
          icon: 'icon-renwuyichang-01',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          textColor: 'color6',
        },
      ],
      detailStatistic: {},
      searchData: {
        pageNum: 1,
        pageSize: 999,
        totalCount: 0,
        taskStatus: '',
        indexType: null,
        indexName: '',
        indexModule: '',
      },
      moduleAction: {},
      navBarFixed: false,
      paramsData: {},
      activeTaskSchemeId: null,
      statisticalModelBo: {
        statisticalModel: [this.global.STATISTICAL_MODAL.SMODE_REGION, this.global.STATISTICAL_MODAL.SMODE_ORG],
        tagIds: [],
      },
    };
  },
  created() {
    this.getParams();
  },
  activated() {
    if (window.sessionStorage.getItem('type')) {
      this.searchData = {
        pageNum: 1,
        pageSize: 999,
        totalCount: 0,
        taskStatus: '',
        indexType: null,
        indexName: '',
        indexModule: '',
      };
      this.copySearchDataMx(this.searchData);
      window.sessionStorage.removeItem('type');
    }
    this.startWatch(
      '$route',
      () => {
        this.getParams();
        if (this.$route.query.componentName == 'evaluationDetail') {
          // 同一任务tab切换不请求
          if (this.activeTaskSchemeId === this.$route.query.taskSchemeId) {
            return;
          }
          this.activeTaskSchemeId = this.$route.query.taskSchemeId;
          this.getStatistics();
          this.getIndexPageList();
          this.getTaskById();
        }
      },
      { immediate: true },
    );
  },

  methods: {
    onSelectionChange(selection) {
      this.selection = selection;
    },
    async batchTriggerIndexJob() {
      if (!this.selection.length) {
        return this.$Message.error('请选待启动指标');
      }
      let ids = this.selection.map((item) => item.id);
      this.triggerIndexJob(ids);
    },
    async batchTerminateRunningJob() {
      if (!this.selection.length) {
        return this.$Message.error('请选待暂停指标');
      }
      let ids = this.selection.map((item) => item.id);
      this.terminateRunningJob(ids);
    },
    async triggerIndexJob(ids) {
      let params = {
        ids: ids,
      };
      try {
        let { data } = await this.$http.post(governanceevaluation.batchTriggerIndexJob, params);
        this.$Message.success(data.msg);
        this.selection = [];
      } catch (err) {
        console.log('err', err);
      }
    },
    async terminateRunningJob(ids) {
      let params = {
        ids: ids,
      };
      try {
        let { data } = await this.$http.post(governanceevaluation.batchTerminateRunningJob, params);
        this.$Message.success(data.msg);
        this.selection = [];
      } catch (err) {
        console.log('err', err);
      }
    },
    handleOperations(obj) {
      this[obj.methods](obj.data);
    },
    environ(row) {
      if (row.taskStatus != 1) {
        return;
      }
      this.$UiConfirm({
        content: '您确认要立即终止此任务?',
        title: '警告',
      })
        .then(() => {
          this.environmentInit(row);
        })
        .catch(() => {});
    },
    async environmentInit(row) {
      let params = {
        id: row.id,
        batchId: row.batchId,
      };
      try {
        let res = await this.$http.post(governanceevaluation.terminateRunningJob, params);
        this.$Message.success(res.data.msg);
        this.getStatistics();
        this.getIndexPageList();
      } catch (err) {
        console.log(err);
      }
    },
    getConfiguration(action, row) {
      if (row.taskStatus == 1) {
        return;
      }
      this.modalData = JSON.parse(JSON.stringify(row));
      const metaType = {
        configuration: () => {
          this.moduleAction = {
            title: row.indexName + '配置',
            statisticalModelBo: this.statisticalModelBo,
            ...row,
          };
          if (checkConfigmodule.includes(this.moduleAction.indexType)) {
            this.moduleAction.checkConfig = detectionRules[this.moduleAction.indexType]
              ? detectionRules[this.moduleAction.indexType]
              : detectionRules.routine;
          }
          this.$refs.BasicInformation.init({ ...this.moduleAction });
        },
      };
      metaType[action]();
    },
    // 指标详情列表
    async getIndexPageList() {
      this.loading = true;
      let params = {
        taskSchemeId: this.$route.query.taskSchemeId,
        pageNumber: this.searchData.pageNum,
        pageSize: this.searchData.pageSize,
        taskStatus: this.searchData.taskStatus,
        indexModule: this.searchData.indexModule,
        indexName: this.searchData.indexName,
      };
      try {
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(governanceevaluation.taskIndexPageList, params);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 指标详情统计
    async getStatistics() {
      try {
        let res = await this.$http.get(
          governanceevaluation.indexDetailStatistics + '/' + this.$route.query.taskSchemeId,
        );
        this.detailStatistic = res.data.data;
        this.statisticsList[0].value = this.detailStatistic.totalCount;
        this.statisticsList[1].value = this.detailStatistic.processingCount;
        this.statisticsList[2].value = this.detailStatistic.todayPendingCount;
        this.statisticsList[3].value = this.detailStatistic.todayCompleteCount;
        this.statisticsList[4].value = this.detailStatistic.errorCount;
      } catch (err) {
        console.log(err);
      }
    },
    // 存在依赖关系的指标  parentIds > 0， 需要禁用
    hasDependentParent(row) {
      return row.parentIds && Number(row.parentIds) > 0;
    },
    // 立即检测
    execute(row) {
      if (row.config == false || row.taskStatus == 1 || this.hasDependentParent(row)) {
        return;
      }
      this.$UiConfirm({
        content: '您确认要立即执行此任务?',
        title: '警告',
      })
        .then(() => {
          this.executeNowInit(row);
        })
        .catch(() => {});
    },
    async executeNowInit({ id }) {
      try {
        let res = await this.$http.get(governanceevaluation.triggerIndexJob + '/' + id);
        this.$Message.success(res.data.msg);
        this.getStatistics();
        this.getIndexPageList();
      } catch (err) {
        console.log(err);
      }
    },
    // 启动/暂停
    async handleBeforeChange(row) {
      return new Promise(async (resolve, reject) => {
        let res = await this.$http.get(governanceevaluation.pauseStartIndexJob + '/' + row.id);

        if (res.data.code == 200) {
          resolve();
          this.$Message.success(res.data.msg);
          this.getStatistics();
          // this.getIndexPageList()
        } else {
          this.$Message.warning(res.data.msg);
          reject();
        }
      });
    },
    configuration() {
      this.configurationShow = true;
      this.configurationAction = {
        title: '配置任务参数',
      };
    },
    getSchemeType(schemeType) {
      switch (schemeType) {
        case '1':
          return '全量数据检测';
        case '2':
          return '上报考核检测';
        case '3':
          return '临时检测';
        default: {
          return '';
        }
      }
    },
    handleCheckStatus(row) {
      const flag = {
        0: '未开始',
        1: '运行中',
        2: '已完成',
        3: '失败',
        4: '异常',
      };
      return flag[row];
    },
    handleIndexType(type) {
      this.global.indexTypeList.map((row) => {
        if (row.id == type) {
          type = row.title;
        }
      });
      return type;
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getIndexPageList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getIndexPageList();
    },
    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.getStatistics();
      this.getIndexPageList();
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr[nameArr.length - 1] === 'taskToOverview' ? 1 : 2;
      }
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    handleClickJump({ batchId, indexId, indexType, taskSchemeId, regionCode, orgCode }) {
      if (!batchId) return;
      //evaluationoResultMixin.js
      this.jump({
        orgCode: orgCode,
        regionCode: regionCode,
        statisticType: 'REGION',
        taskSchemeId: taskSchemeId,
        indexId: `${indexId}`,
        indexType: indexType,
        batchId: batchId,
      });
    },
    // 指标详情
    openDetailsDialog(row) {
      let { indexType, indexName, indexId } = row;
      this.indexVisible = true;
      this.currentRow = {
        indexType: indexType,
        indexName: indexName,
        id: indexId,
      };
    },
    async getTaskById() {
      //任务列表也有返回statisticalModelBo字段，但通过createTab-query传参略显冗余
      try {
        let res = await this.$http.get(governanceevaluation.getTaskPermissionList, {
          params: { id: this.$route.query.taskSchemeId },
        });
        if (res.data.data.statisticalModelBo) {
          this.statisticalModelBo = res.data.data.statisticalModelBo;
        }
      } catch (e) {
        console.log(e);
      }
    },
  },
  computed: {},
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddorEditModal: require('../addor-edit-modal.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    StatisticCard: require('../../components/statistic-card.vue').default,
    OperationBtnMore: require('@/components/operation-btn-more.vue').default,
    BasicInformation:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/basics/basic-information.vue')
        .default,
    taskToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue').default,
    OnOffLineDetail: require('@/views/governanceevaluation/evaluationoverview/components/video/on-off-line-detail.vue')
      .default,
    VideoHistoryOnline:
      require('@/views/governanceevaluation/evaluationoverview/components/video/video-history-online.vue').default,
    ReportingRate: require('@/views/governanceevaluation/evaluationoverview/components/video/reporting-rate.vue')
      .default,
    FaceCarOnlineNum: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-online-num.vue')
      .default,
    FaceCarTopOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-top-online.vue')
      .default,
    FaceCarReportRate:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-report-rate.vue').default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
  },
};
</script>
