<template>
  <ui-modal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onCancel="handleCancel"
    @onOk="confirmHandle"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="select-label-left">
        <div class="organization" v-if="showOrganization">
          <div class="title">资源列表</div>
          <el-tree
            highlight-current
            ref="resourceTree"
            :props="treeProps"
            lazy
            :load="loadNode"
            node-key="id"
            draggable
            :expand-on-click-node="false"
          >
            <span
              @click="handleNodeClick(data)"
              class="custom-tree-node"
              slot-scope="{ node, data }"
              :key="data.id"
            >
              <template>
                <span class="label">
                  <i class="iconfont icon-fenju"></i>
                  {{ node.data.orgName }}
                </span>
              </template>
            </span>
          </el-tree>
        </div>
        <div class="select-label-content">
          <div class="manual" v-if="checkShow">
            <Checkbox
              class="checks mr-lg align-flex"
              v-model="isAll"
              @on-change="handleAllCheck"
            >
              全选
            </Checkbox>
          </div>
          <Table
            class="auto-fill table"
            ref="table"
            :height="335"
            :columns="columns"
            :data="tableData"
            :loading="tableLoading"
            @on-select="onSelect"
            @on-select-cancel="onSelectCancel"
            @on-select-all="onSelectAll"
            @on-select-all-cancel="onSelectAllCancel"
          >
            <template #loading>
              <ui-loading></ui-loading>
            </template>
            <template slot="deviceGbId" slot-scope="{ row }">
              <span class="link-btn cursor-p" @click="deviceArchives(row)">{{
                row.deviceGbId
              }}</span>
            </template>
            <template slot="sbgnlx" slot-scope="{ row }">
              <span>{{ getGnlx(row.sbgnlx) }}</span>
            </template>
            <template #labels="{ row }">
              <ui-tag-poptip
                v-if="row.labels && row.labels.length"
                :data="row.labels"
              />
            </template>
          </Table>
          <ui-page
            :current="pageInfo.pageNumber"
            :total="total"
            :page-size="pageInfo.pageSize"
            @pageChange="pageChange"
            @pageSizeChange="pageSizeChange"
          >
          </ui-page>
        </div>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<span>{{ selectTableData.length }}</span> 个</span
          >
          <span class="del-btn" @click="removeAllHandle">
            <i class="iconfont icon-shanchu"></i>清空
          </span>
        </div>
        <div class="label-container">
          <ul>
            <li v-for="(item, index) in selectTableData" :key="index">
              <Checkbox
                v-model="item.select"
                @on-change="selectChange(item, index)"
                >{{ item[defaultProps.deviceName] }}
              </Checkbox>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { ysDeviceList,ysOrgCodeList } from "@/api/multimodal-analysis";
import { commonMixins } from "@/mixins/app.js";
import {
  defaultFormItemData,
} from "@/views/wisdom-cloud-search/search-center/advanced-search/components/utils.js";
import { deepCopy } from "@/util/modules/common";
export default {
  components: {},
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
    // 设备类型， 1：摄像机， 2：wifi, 3: RFID, 4: 电围
    deviceType: {
      type: Number,
      default: 1,
    },
    // 组织机构显示标识
    showOrganization: {
      type: Boolean,
      default: false,
    },
    // 是否展示选择所有
    isCheckShow: {
      type: Boolean,
      default: false,
    },
    // 兼容 取值字段不一致的情况
    defaultProps: {
      type: Object,
      default: () => {
        return {
          id: "deviceId", // 唯一键标识
          deviceName: "deviceName",
          // ...
        };
      },
    },
    // 对应 formData的数据
    formDataProp: {
      type: Object,
    },
    // 对应 formItemData的数据
    formItemList: {
      type: Array,
      default: () => [],
    },
    queryDataApiFn: {
      type: Function,
    },
    tableColumns: {
      type: Array,
    },
    // 场所围栏
    placeFence: {
      type: Object,
      default: () => {},
    },
  },
  mixins: [commonMixins], //全局的mixin
  data() {
    return {
      seleType: 1,
      selectMapType: 2,
      modalShow: false,
      formData: {
        orgCode: "",
      },
      dialogData: {
        title: "选择设备",
        rWidth: 1600,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
      columns: [
        { title: "选择", width: 65, type: "selection", key: "index" },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "设备名称", key: "deviceName" },
        { title: "设备编码", key: "deviceId" },
      ],
      tableData: [],
      selectTableData: [],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: {
        orgName: "根节点",
        orgCode: "iccsid",
      },
      isAll: false,
      isExclude: false,
      excludeType: "1",
      checkShow: this.isCheckShow,
      treeProps: {
        label: "orgName",
        isLeaf: "isLeaf",
        children: "children",
      },
      tableLoading: false,
    };
  },
  created() {
  },
  methods: {
    handleRadioChange() {
      this.selectTableData = [];
      this.$refs.table.selectAll(false);
    },
    // 更改交集或并集
    handleMapChange() {
      this.removeAllHandle();
      this.$refs.mapBox.cleanBox();
      this.$refs.mapBox.selectType(this.selectMapType);
    },
    deviceList(value) {
      value.forEach((item) => {
        item.select = true;
      });
      if (this.selectMapType == 2) {
        let list = [...value, ...this.selectTableData];
        this.selectTableData = Array.from(
          new Set(list.map((item) => JSON.stringify(item)))
        ).map((item) => JSON.parse(item));
        return;
      }
      this.selectTableData = value;
    },
    // 兼容 默认数据
    handlerFormItemData() {
      if (this.formItemList.length) return;
      // 功能类型
      let index = defaultFormItemData.findIndex(
        (item) => item.key === "sbgnlxs"
      );
      if (index !== -1 && this.sbgnlxList.length) {
        defaultFormItemData[index].options = this.sbgnlxList.map((item) => {
          return { value: item.dataKey, label: item.dataValue };
        });
      }
      this.formItemData = deepCopy(defaultFormItemData);
    },
    handleQuery() {
      this.pageInfo.pageNumber = 1;
      this.init();
    },
    async init() {
      try {
        let res =  await ysDeviceList({
             cascade:1,
            ...this.pageInfo,
            ...this.formData,
          });
        const { total, entities } = res.data;
        this.total = total;
        this.tableData = entities;
        this.tableIsSelect();
      } catch (error) {
        console.error(error);
      } finally {
        this.tableLoading = false;
      }
    },
    /**
     * table回显
     */
    tableIsSelect() {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.$refs.table.objData;
        debugger
        // 清空table选中状态
        if (this.selectTableData.length == 0) {
          Object.keys(obj).forEach((key) => {
            obj[key]._isChecked = false;
          });
          return;
        }
        // 回显
        Object.keys(obj).forEach((key) => {
          var row = this.selectTableData.find((i) => {
            return obj[key][this.defaultProps.id] == i[this.defaultProps.id];
          });
          if (row) {
            this.$refs.table.objData[key]._isChecked = true;
          }
        });
      }, 20);
    },
    /**
     * 显示model
     */
    show(list = [], keyWords = "") {
      this.modalShow = true;
      this.selectTableData = JSON.parse(JSON.stringify(list))?.map((el) => ({
        ...el,
        select: true,
      })); //防止数据浅拷贝，改变父组件
      this.seleType = 1;
      this.formData.orgCode = this.custormNodeData.orgCode;
      this.init();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    /**
     * 右侧清空
     */
    removeAllHandle() {
      this.isAll = false;
      this.selectTableData = [];
      this.$refs.table.selectAll(false);
      // this.$emit("selectData", []);
    },

    /**
     * 确定按钮
     */
    confirmHandle() {
      this.modalShow = false;
      let params = {
        ...this.formData,
      };
      // 选择的数据
      let list = this.selectTableData.map((item) => item);
      this.$emit("selectData", list, params);
    },
    // 取消
    handleCancel() {
      if (this.showOrganization) {
        this.$refs.resourceTree.setCheckedNodes([]);
      }
      this.isAll = false;
      this.isExclude = false;
    },
    /**
     * table 选中一项
     */
    onSelect(selection, row) {
      var obj = this.selectTableData.find((item) => {
        return item[this.defaultProps.id] == row[this.defaultProps.id];
      });
      row.select = true;
      if (!obj) {
        this.selectTableData.push(row);
      } else {
        obj.select = true;
      }
    },
    /**
     * table 取消选中一项
     */
    onSelectCancel(selection, row) {
      var num = this.selectTableData.findIndex((item) => {
        return item[this.defaultProps.id] == row[this.defaultProps.id];
      });
      this.selectTableData.splice(num, 1);
    },
    /**
     * table 全选
     */
    onSelectAll(selection) {
      selection.forEach((item) => {
        item.select = true;
        var obj = this.selectTableData.find((itm) => {
          return itm[this.defaultProps.id] == item[this.defaultProps.id];
        });
        if (!obj) {
          this.selectTableData.push(item);
        }
      });
    },

    /**
     * table 取消全选
     */
    onSelectAllCancel(selection) {
      this.tableData.forEach((item) => {
        var num = this.selectTableData.findIndex((itm) => {
          return itm[this.defaultProps.id] == item[this.defaultProps.id];
        });
        if (num != -1) {
          this.selectTableData.splice(num, 1);
        }
      });
    },

    /**
     * 表格右侧 已选中内容操作
     */
    selectChange(row, index) {
      var obj = this.$refs.table.objData;
      if (row.select) {
        // 选中
        Object.keys(obj).forEach((key) => {
          if (obj[key][this.defaultProps.id] == row[this.defaultProps.id]) {
            obj[key]._isChecked = true;
          }
        });
      } else {
        // 取消选中
        Object.keys(obj).forEach((key) => {
          if (obj[key][this.defaultProps.id] == row[this.defaultProps.id]) {
            obj[key]._isChecked = false;
          }
        });
      }
      this.$nextTick(() => {
        this.selectTableData.splice(index, 1);
      });
    },
    loadNode(node, resolve) {
      // 加载子树数据的方法
      const parent = node.parent
      if(!parent) resolve([{...this.custormNodeData}])
      else ysOrgCodeList( parent ? node?.data?.orgCode : this.custormNodeData.orgCode).then((res) => {
        const data= res?.data || [];
          resolve(data);
      });
    },
    handleNodeClick(data) {
      this.checkShow = true;
      this.formData.orgCode = data.orgCode;
      this.handleQuery();
    },
    /**
     * 选择组织机构树
     */
    selectedOrgTree() {},
    /**
     * 重置表单
     */
    resetForm() {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 20,
      };
      if (this.showOrganization) {
        this.$refs.resourceTree.setCheckedNodes([]);
      }
      this.init();
    },
    // 全选
    async handleAllCheck(val) {

      try {
        if (!val) {
          // 取消全选的逻辑
          var obj = this.$refs.table.objData;
          Object.keys(obj).forEach((key) => {
            obj[key]._isChecked = false;
          });
          this.$nextTick(() => {
            this.selectTableData = [];
          });
          return;
        }
        const maxSize = 200; // 最大数量限制为5000
        if (this.selectTableData.length == maxSize) {
          this.isAll = false;
          this.$Message.warning(`单次选择最大数量不能大于${maxSize}，请重新选择`);
          return;
        }
        this.tableLoading = true;
        const res = await ysDeviceList({
            cascade:1,
            ...this.formData,
            pageNumber: 1,
            pageSize: maxSize,
          });
        const {  entities } = res.data;
        if (this.selectTableData.length + entities.length > maxSize) {
          this.isAll = false;
          this.$Message.warning(`单次选择最大数量不能大于${maxSize}，请重新选择`);
        } else {
          entities.forEach((item) => {
            item.select = true;
            var obj = this.selectTableData.find((itm) => {
              return itm[this.defaultProps.id] == item[this.defaultProps.id];
            });
            if (!obj) {
              this.selectTableData.push(item);
            }
          });
          this.comparisonList();
        }
      } catch (error) {
        console.error(error);
      } finally {
        this.tableLoading = false;
      }
    },
    // 对比当前已选数据
    comparisonList() {
      this.$nextTick(() => {
        var obj = this.$refs.table.objData;
        let selectList = new Map(
          this.selectTableData.map((item) => [item[this.defaultProps.id], item])
        );
        Object.keys(obj).forEach((key) => {
          if (selectList.get(obj[key][this.defaultProps.id])) {
            obj[key]._isChecked = true;
          }
        });
      });
    },
    // 排除
    handleExcludeCheck(val) {
      if (val) {
        this.isAll = false;
      }
      this.handleDataChecked();
    },
    // 点击全选或者排除时，对表格复选框进行操作
    handleDataChecked() {
      this.tableData = this.tableData.map((item) => {
        this.$set(item, "_checked", this.isAll);
        this.$set(item, "_disabled", this.isAll);
        return item;
      });
    },
    getGnlx(str) {
      if (!str) {
        return;
      }
      var arr = str.split("/");
      var gnlx = "";
      arr.forEach((item) => {
        var row = this.sbgnlxList.find((i) => i.dataKey == item);
        gnlx += row.dataValue + " / ";
      });
      return gnlx.substring(0, gnlx.length - 2);
    },
  },
};
</script>
<style lang="less" scoped>
.sele-type {
  display: flex;
  margin-bottom: 10px;
}
.find {
  margin-right: 10px;
}
/deep/ .ivu-input-wrapper {
  width: 100% !important;
}
.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 0px 0px;
  display: flex;
  .select-label-left {
    display: flex;
    flex: 1;
  }
  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
    .manual {
      display: flex;
    }
  }
  .map-content {
    flex: 1;
    height: 520px;
  }
  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    height: 400px;
    position: relative;
  }
  .organization {
    width: 240px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 472px;
    overflow: auto;
    border-right: 1px solid #d3d7de;
    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      background: #f9f9f9;
    }
  }
  .preview-select-label-content {
    width: 250px;
    padding: 10px 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.del-btn {
  cursor: pointer;
}
.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 10px;
}

/deep/ .ivu-icon {
  // color: #fff;
}

.form {
  width: 100%;
  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }
  // /deep/ .ivu-input {
  //   width: 125px;
  // }
  /deep/.ivu-select {
    width: 100%;
  }

  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");
    .ivu-form-item-content {
      flex: 1;
      .ivu-select {
        width: 100%;
      }
    }
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
  // /deep/ .ivu-form-item-content{
  //   float: left;
  // }
  // .btn-group {
  //   margin-right: 0;
  // }
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-size: 14px;
    color: #000;
    i {
      color: #23a8f9;
      margin-right: 10px;
    }
  }
}
</style>
