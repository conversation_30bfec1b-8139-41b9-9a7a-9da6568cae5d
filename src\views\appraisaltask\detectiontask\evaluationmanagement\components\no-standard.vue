<template>
  <div class="sta_item_right">
    <div class="equip_num">
      <div class="precision deft">
        <i class="i_line"></i>
        <span>
          {{ moduleData.rate }}
          ：
          <i class="font-warning" :class="moduleData.rateValue < 100 ? 'color_red' : 'color_green'">
            {{ moduleData.rateValue }}%
          </i>
        </span>
        <Tooltip :content="moduleData.remarkValue" placement="top" v-if="moduleData.remarkValue != ''">
          <img v-if="moduleData.remarkValue != ''" class="ml-xs" src="@/assets/img/car-modal/icon1.png" alt="" />
        </Tooltip>
      </div>
      <div class="precision">
        <i class="i_line"></i>
        <span
          >{{ moduleData.price }}：<i
            class="font-green"
            :class="moduleData.priceValue < 100 ? 'color_red' : 'color_green'"
            >{{ moduleData.priceValue || 0 }}%</i
          ></span
        >
      </div>
    </div>
    <div class="result">
      <div class="precision">
        <i class="i_line"> </i><span>{{ moduleData.result }}：</span>
        <span
          class="icon-font icon-zhiliangfen-line standard_icon font-warning"
          v-if="moduleData.resultValue != '合格'"
        >
          <i class="icon_text_error font-warning">不达标</i>
        </span>
        <span class="icon-font icon-zhiliangfen-line standard_icon font-green" v-if="moduleData.resultValue == '合格'">
          <i class="icon_text_succeed inline font-green">达标</i>
        </span>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.sta_item_right {
  //   width: 51%;
  display: flex;
  .i_line {
    width: 6px;
    height: 20px;
    margin-right: 10px;
    margin-left: 45px;
    background: var(--color-primary);
  }
  .equip_num {
    height: 100%;
    min-width: 200px;
    max-width: 400px;
    padding-top: 40px;
    .precision {
      height: 30px;
      line-height: 30px;
      margin-bottom: 20px;
      img {
        vertical-align: middle;
      }
      @{_deep}.ivu-tooltip-rel {
        overflow: inherit;
      }
    }
  }
  .result {
    height: 100%;
    padding-top: 40px;
    .precision {
      height: 30px;
      line-height: 30px;
      margin-bottom: 20px;
    }
    .standard_icon {
      vertical-align: middle;
      font-size: 120px;
      position: relative;
      .icon_text_error {
        font-size: 16px;
        position: absolute;
        right: 37px;
        top: 44px;
        font-weight: bold;
        transform: rotate(-32deg);
      }
      .icon_text_succeed {
        font-size: 16px;
        position: absolute;
        right: 44px;
        top: 44px;
        font-weight: bold;
        transform: rotate(-32deg);
      }
    }
  }
  .color_blue {
    color: var(--color-bluish-green-text);
  }
  .color_green {
    color: #19c176;
  }
  .color_red {
    color: #bc3c19;
  }
}
</style>
<script>
export default {
  props: {
    moduleData: {},
  },
  data() {
    return {};
  },
  mounted() {},
  watch: {},
};
</script>
