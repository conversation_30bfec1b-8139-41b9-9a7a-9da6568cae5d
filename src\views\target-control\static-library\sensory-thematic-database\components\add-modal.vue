<template>
  <ui-modal
    v-model="visible"
    :title="!isEdit ? '新增': '编辑'"
    :r-width="700"
    @onOk="comfirmHandle">
    <div>
      <Form ref="formRef" :model="form" class="personnel-form">
        <FormItem v-if="currentRow.libCategory  == '3'" prop="mac" label="mac地址:">
          <Input v-model="form.mac" placeholder="请输入mac地址" class="input-200"/>
        </FormItem>
        <FormItem v-if="currentRow.libCategory  == '5'" prop="rfidCode" label="rfid地址:">
          <Input v-model="form.rfidCode" placeholder="请输入rfid地址" class="input-200"/>
        </FormItem>
        <FormItem v-if="currentRow.libCategory  == '4'" prop="imsi" label="IMSI编码:">
          <Input v-model="form.imsi" placeholder="请输入IMSI编码" class="input-200"/>
        </FormItem>
        <FormItem v-if="currentRow.libCategory  == '4'" prop="imei" label="国际移动台设备识别码:">
          <Input v-model="form.imei" placeholder="请输入国际移动台设备识别码" class="input-200"/>
        </FormItem>
        <FormItem v-if="currentRow.libCategory  == '6'" prop="obuId" label="Etc编号:">
          <Input v-model="form.obuId" placeholder="请输入Etc编号" class="input-200"/>
        </FormItem>

        <template v-if="currentRow.libCategory  == '3' || currentRow.libCategory  == '4'">
          <FormItem prop="name" label="姓名:">
            <Input v-model="form.name" placeholder="请输入姓名" class="input-200"/>
          </FormItem>
          <FormItem prop="idCardNo" label="身份证号:">
            <Input v-model="form.idCardNo" placeholder="请输入身份证号" class="input-200"/>
          </FormItem>
        </template>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
  import { sensoryDataAdd, sensoryDataUpdate } from '@/api/data-warehouse'
  export default {
    props: {
      // 当前库对象
      currentRow: {
        type: Object,
        default: {}
      },
     
    },
    data() {
      return {
        visible: false,
        isEdit: false,    //false-新增, true-编辑
        form: {},
      }
    },
    methods: {
      show(bool, item) {
        this.isEdit = bool
        this.visible = true
        
        this.$nextTick(() => {
          if(this.isEdit) {
            // 编辑
            this.form = JSON.parse(JSON.stringify(item))
            this.$forceUpdate()
          }else{
            this.form = {
              libCategory: this.currentRow.libCategory,
              libId: this.currentRow.id
            }
          }
        })
      },
      comfirmHandle() {
        if (this.currentRow.libCategory  == '3' && !this.form.mac) {
          this.$Message.info('请输入mac地址')
          return
        }
        if (this.currentRow.libCategory  == '5' && !this.form.rfidCode) {
          this.$Message.info('请输入rfid地址')
          return
        }
        if (this.currentRow.libCategory  == '4' && !this.form.imsi) {
          this.$Message.info('请输入IMSI编码')
          return
        }
        if (this.currentRow.libCategory  == '6' && !this.form.obuId) {
          this.$Message.info('请输入Etc编号')
          return
        }
        // 新增
        if(!this.isEdit) {
          sensoryDataAdd(this.form).then(res => {
            this.visible = false
            this.$Message.success('新增成功')
            this.$parent.init()
          })
        }else {
        //编辑
          sensoryDataUpdate(this.form).then(res => {
            this.visible = false
            this.$Message.success('编辑成功')
            this.$parent.init()
          })
        }
      },
    }
  }
</script>
<style lang="less" scoped>
.personnel-form {
  /deep/ .ivu-form-item {
    display: flex;
    .ivu-form-item-label {
      width: 120px;
    }
    .ivu-form-item-content {
      flex: 1;
    }
  }
}
</style>