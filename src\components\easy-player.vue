<template>
  <div class="easy-player">
    <EasyPlayer 
        :autoplay="true" 
        :video-url="url" 
        :live="isLive" 
        :reconnection="true" 
        :stretch="stretch" 
        :show-custom-button="false" 
        :class="{ 'no-stretch': !stretch }" 
        fluent 
        ref="easyPlayer"></EasyPlayer>
  </div>
</template>

<script>
import EasyPlayer from '@easydarwin/easyplayer'
export default {
  name: 'VideoPlayer',
  props: {
    videoUrl: {
      type: String
    },
    isLive: {
      type: Boolean,
      dafault: true
    },
    needDecrypt: {
      type: <PERSON>olean,
      default: true
    },
    decryptKey: {
      type: String,
      default: 'QSDI123456'
    },
    stretch: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      url: ''
    }
  },
  components: {
    EasyPlayer
  },
  mounted() {},
  methods: {},
  watch: {
    videoUrl: {
      handler(val) {
        if (!val) {
          this.url = ''
          return
        }
        if (this.needDecrypt) {
          this.url = this.$util.common.decryptDes(val, this.decryptKey)
        } else {
          this.url = val
        }
        console.log(this.url)
      },
      immediate: true
    }
  }
}
</script>
<style lang="less" scoped>
.easy-player {
  margin-top: -20px;
  height: 100%;
  width: 100%;
  /deep/ .easy-player-right-menu {
    display: none;
  }
  /deep/ .vjs-stretch-control {
    display: none;
  }
  /deep/ .easy-player-loading-text{
    opacity: 0 !important;
  }
}
// fix: easyplayer 的strech字段设置无效
.no-stretch {
  /deep/&.easy-player-fill-container .video-js .vjs-tech {
    object-fit: contain !important;
  }
}
</style>
