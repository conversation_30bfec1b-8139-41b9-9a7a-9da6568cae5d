/**
 * 请按照指标类型以及indexId排序
 **/
const baseFaceFunc = (slotTableColumn) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'civilName',
      align: 'left',
      tooltip: true,
      minWidth: 150,
      sortable: 'custom',
    },
    {
      title: '行政区划代码',
      key: 'civilCode',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '设备总量',
      key: 'deviceNum',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '实际检测设备数量',
      key: 'actualNum',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
    {
      title: '合格设备数量',
      key: 'qualifiedNum',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '不合格设备数量',
      key: 'unqualifiedNum',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
    ...slotTableColumn,
    {
      title: '达标情况',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '操作',
      slot: 'action',
      align: 'left',
      tooltip: true,
      width: 60,
      fixed: 'right',
      className: 'table-action-padding', // 操作栏列-单元格padding设置
      btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
    },
  ];
};

// 离线统计
const offlineStatFn = (params) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'civilName',
      align: 'left',
      tooltip: true,
      minWidth: 150,
      sortable: 'custom',
      renderHeader: (h, { column, index }) => {
        return renderHeaderRegion(h, { column, index, params });
      },
    },
    {
      title: '行政区划代码',
      key: 'civilCode',
      align: 'left',
      tooltip: true,
      minWidth: 100,
      renderHeader: (h, { column, index }) => {
        return renderHeaderRegionCode(h, { column, index, params });
      },
    },
    {
      title: '检测数量',
      key: 'actualNum',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '连续离线或累计离线设备数量',
      key: 'offlineTotal',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '本月连续离线>=X天数量',
      key: 'continuousOffline',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '本月累计离线>=Y天数量',
      key: 'addUpOffline',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '操作',
      slot: 'action',
      align: 'center',
      tooltip: true,
      width: 60,
      fixed: 'right',
      className: 'table-action-padding', // 操作栏列-单元格padding设置
      btnArr: [
        {
          text: '详情',
          icon: 'icon-chakanxiangqing',
          emitFun: 'viewDetail',
        },
      ],
    },
  ];
};
let renderHeaderRegion = (h, { column, params }) => {
  let { statisticType } = params;
  if (statisticType === 'REGION') {
    column.key = 'civilName';
    return <span>行政区划</span>;
  } else {
    column.key = 'orgName';
    return <span>组织机构</span>;
  }
};
let renderHeaderRegionCode = (h, { column, params }) => {
  let { statisticType } = params;
  if (statisticType === 'REGION') {
    column.key = 'civilCode';
    return <span>行政区划代码</span>;
  } else {
    column.key = 'orgCode';
    return <span>组织机构代码</span>;
  }
};

// 重点/实时视频可调阅率、人脸卡口在线率、车辆卡口在线率
let matchingRateArr = (params) => {
  return [
    {
      title: '建档匹配率',
      minWidth: 80,
      slot: 'accuracyRate',
    },
    {
      title: '指标名称*建档匹配率',
      minWidth: 100,
      key: 'comRate',
      renderHeader: (h) => {
        return h('span', `${params.indexName ? params.indexName : '指标名称'}*建档匹配率`);
      },
      render: (h, { row }) => {
        return (
          <span>
            {row.detail?.onlineRateVO?.comRate || row.detail?.onlineRateVO?.comRate === 0
              ? `${row.detail?.onlineRateVO?.comRate}%`
              : '--'}
          </span>
        );
      },
    },
  ];
};

let tableColumns = (params) => {
  let matchingRate = [];
  if (
    params.showColRegion &&
    [
      'VIDEO_PLAYING_ACCURACY',
      'VIDEO_GENERAL_PLAYING_ACCURACY',
      'VEHICLE_ONLINE_RATE',
      'FACE_ONLINE_RATE',
      'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
    ].includes(params.indexType)
  ) {
    matchingRate = matchingRateArr(params);
  }
  return {
    // 视图基础数据
    BASIC_INPUT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        align: 'center',
        title: '视频监控',
        children: [
          {
            title: '本级建档',
            key: 'biVideoCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biVideoCount || 0}</span>;
            },
          },
          {
            title: '上级建档',
            key: 'biParentVideoCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biParentVideoCount || 0}</span>;
            },
          },
          {
            title: '未建档',
            key: 'videoUnDocCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.videoUnDocCount || 0}</span>;
            },
          },
          {
            title: '建档率',
            key: 'biVideoResultValueFormat',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biVideoResultValueFormat || 0}</span>;
            },
          },
        ],
      },
      {
        align: 'center',
        title: '人脸卡口',
        children: [
          {
            title: '本级建档',
            key: 'biFaceCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biFaceCount || 0}</span>;
            },
          },
          {
            title: '上级建档',
            key: 'biParentFaceCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biParentFaceCount || 0}</span>;
            },
          },
          {
            title: '未建档',
            key: 'faceUnDocCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.faceUnDocCount || 0}</span>;
            },
          },
          {
            title: '建档率',
            key: 'biFaceResultValueFormat',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biFaceResultValueFormat || 0}</span>;
            },
          },
        ],
      },
      {
        align: 'center',
        title: '车辆卡口',
        children: [
          {
            title: '本级建档',
            key: 'biVehicleCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biVehicleCount || 0}</span>;
            },
          },
          {
            title: '上级建档',
            key: 'biParentVehicleCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biParentVehicleCount || 0}</span>;
            },
          },
          {
            title: '未建档',
            key: 'vehicleUnDocCount',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.vehicleUnDocCount || 0}</span>;
            },
          },
          {
            title: '建档率',
            key: 'biVehicleResultValueFormat',
            align: 'center',
            tooltip: true,
            minWidth: 100,
            render: (h, { row }) => {
              return <span>{row.detail.biVehicleResultValueFormat || 0}</span>;
            },
          },
        ],
      },
      {
        title: '建档率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 视图基础数据建档率
    BASIC_DEVICE_HUNDRED: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '人口数量',
        key: 'personNum',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.personNum) || 0}</span>;
        },
      },
      {
        title: '百人达标占比率',
        key: 'p1',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.p1) || 0}%</span>;
        },
      },
      {
        title: '达标设备数量',
        key: 'standDeviceNum',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.standDeviceNum) || 0}</span>;
        },
      },
      {
        title: '实际设备数量',
        key: 'actDeviceNum',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.actDeviceNum) || 0}</span>;
        },
      },
      {
        title: '差值',
        key: 'diffNum',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.diffNum) || 0}</span>;
        },
      },
      {
        title: '实际占比率',
        key: 'actP1',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.actP1) || 0}%</span>;
        },
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
    ], //设备百人占比率
    BASIC_CJQY_QUANTITY_STANDARD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '应上报采集区域类型数量',
        key: 'standardCount',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        render: (h, { row }) => {
          return <span>{row.detail.standardCount || 0}</span>;
        },
      },
      {
        title: '采集区域类型达标数量',
        key: 'qualifiedStandardCount',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        render: (h, { row }) => {
          return <span>{row.detail.qualifiedStandardCount || 0}</span>;
        },
      },
      {
        title: '视频监控采集区域类型达标数量',
        key: 'videoStandardCount',
        align: 'left',
        tooltip: true,
        minWidth: 220,
        render: (h, { row }) => {
          return <span>{row.detail.videoStandardCount || 0}</span>;
        },
      },
      {
        title: '人脸卡口采集区域类型达标数量',
        key: 'faceStandardCount',
        align: 'left',
        tooltip: true,
        minWidth: 220,
        render: (h, { row }) => {
          return <span>{row.detail.faceStandardCount || 0}</span>;
        },
      },
      {
        title: '车辆卡口采集区域类型达标数量',
        key: 'vehicleStandardCount',
        align: 'left',
        tooltip: true,
        minWidth: 220,
        render: (h, { row }) => {
          return <span>{row.detail.vehicleStandardCount || 0}</span>;
        },
      },
      {
        title: '视频图像采集区域数量达标率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
        render: (h, { row }) => {
          return <span>{row.detail.resultValueFormat || 0}</span>;
        },
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 重点位置类型视频图像设备数量达标率

    // 视频流
    VIDEO_GENERAL_PLAYING_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '普通实时视频可调阅率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 110,
      },
      ...matchingRate,
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 普通实时视频可调阅率
    VIDEO_PLAYING_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '重点实时视频可调阅率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 110,
      },
      ...matchingRate,
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点实时视频可调阅率
    VIDEO_GENERAL_HISTORY_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '普通历史视频可调阅率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 普通历史视频可调阅率
    VIDEO_READ_PROMOTION_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '治理前可调阅率',
        key: 'beforeRate',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.beforeRate) || 0}%</span>;
        },
      },
      {
        title: '治理后可调阅率',
        key: 'afterRate',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.afterRate) || 0}%</span>;
        },
      },
      {
        title: '可调阅提升率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    VIDEO_OSD_ACCURACY_PROMOTION_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '治理前字幕标注合规率',
        key: 'beforeRate',
        align: 'left',
        tooltip: true,
        minWidth: 140,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.beforeRate) || 0}%</span>;
        },
      },
      {
        title: '治理后字幕标注合规率',
        key: 'afterRate',
        align: 'left',
        tooltip: true,
        minWidth: 140,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.afterRate) || 0}%</span>;
        },
      },

      {
        title: '字幕标注合规提升率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 140,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    VIDEO_CLOCK_ACCURACY_PROMOTION_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '治理前时钟准确率',
        key: 'beforeRate',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.beforeRate) || 0}%</span>;
        },
      },
      {
        title: '治理后时钟准确率',
        key: 'afterRate',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.afterRate) || 0}%</span>;
        },
      },
      {
        title: '时钟准确提升率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    VIDEO_HISTORY_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点历史视频可调阅率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点历史视频可调阅率
    VIDEO_GENERAL_OSD_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '普通字幕标注合规率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 普通字幕标注合规率
    VIDEO_OSD_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点字幕标注合规率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点字幕标注合规率
    VIDEO_GENERAL_CLOCK_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '普通时钟准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 普通时钟准确率
    VIDEO_CLOCK_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点时钟准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点时钟准确率
    VIDEO_VALID_SUBMIT_QUANTITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '达标数量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 120,
      },
      {
        title: '满分数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '上报数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实有区县数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标区县',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '视频监控数量达标率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 视频监控有效报送数量达标率
    VIDEO_QUANTITY_STANDARD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '达标数量',
        key: 'standardNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '满分数量',
        key: 'fullDeviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '上报数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实有区县数量',
        key: 'areaCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标区县',
        key: 'qualifiedAreaCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '视频监控数量达标率',
        key: 'areaResultValue',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '区县详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 视频监控数量达标率
    VIDEO_GENERAL_OSD_CLOCK_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '字幕标注合规性与时钟准确性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 字幕标注合规性与时钟准确性
    VIDEO_OSD_CLOCK_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '(重点)字幕标注合规性与时钟准确性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // (重点)字幕标注合规性与时钟准确性
    VIDEO_QUALITY_PASS_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '视频流质量合格率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 视频流质量合格率
    VIDEO_DEVICE_REVOCATION: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '撤销次数',
        key: 'revocationCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.revocationCount) || 0}</span>;
        },
      },
      {
        title: '累计撤销数量',
        key: 'addUpRevocationNumber',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.addUpRevocationNumber) || 0}</span>;
        },
      },
      {
        title: '最终撤销数量',
        key: 'finalRevocationNumber',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{(row.detail && row.detail.finalRevocationNumber) || 0}</span>;
        },
      },
      {
        title: '视频监控设备撤销率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 视频流质量合格率
    VIDEO_QUALITY_PASS_RATE_RECHECK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '视频流质量合格率（人工复核）',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 视频流质量合格率（人工复核）
    VIDEO_QUANTITY_INCREASE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '当前设备数量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '去年年末设备数量',
        key: 'lastYearDeviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '视频监控数量增长率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'center',
        tooltip: true,
        width: 100,
      },
    ], // 视频监控数量增长率
    VIDEO_HISTORY_COMPLETE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '历史视频完整率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点历史录像完整率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],

    // 人脸
    FACE_CAPTURE_RATIONALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸抓拍数据合理性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史无抓拍',
        key: 'errorNoData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日无抓拍',
        key: 'errorTodayNoData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '抓拍数量过少',
        key: 'errorTooLessData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '抓拍数量突降',
        key: 'errorDataSwoop',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日活跃率',
        key: 'todayActivePercent',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸抓拍数据合理性
    FACE_UPLOAD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸卡口设备及时上传率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸卡口设备及时上传率
    FACE_FOCUS_UPLOAD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点人脸卡口设备及时上传率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点人脸卡口设备及时上传率
    FACE_URL_AVAILABLE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸卡口设备图片地址可用率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸卡口设备图片地址可用率
    FACE_SMALL_URL_AVAILABLE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸卡口设备小图图片地址可用率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸卡口设备小图图片地址可用率
    FACE_FOCUS_URL_AVAILABLE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点人脸卡口设备图片地址可用率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点人脸卡口设备图片地址可用率
    FACE_CLOCK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'cutom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸卡口设备时钟准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸设备抓拍图片合格率
    // 人脸
    FACE_CAPTURE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸抓拍数据合理性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史无抓拍',
        key: 'errorNoData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日无抓拍',
        key: 'errorTodayNoData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '抓拍数量过少',
        key: 'errorTooLessData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '抓拍数量突降',
        key: 'errorDataSwoop',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日活跃率',
        key: 'todayActivePercent',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸抓拍数据合理性
    FACE_CAPTURE_PASS: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸设备抓拍图片合格率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 人脸卡口设备时钟准确率
    FACE_QUANTITY_STANDARD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '达标数量',
        key: 'standardNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '满分数量',
        key: 'fullDeviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '上报数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实有区县数量',
        key: 'areaCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标区县',
        key: 'qualifiedAreaCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '人脸卡口数量达标率',
        key: 'areaResultValue',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '区县详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 人脸卡口数量达标率
    FACE_ASSET_REGISTER: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '资产库已注册人脸卡口数量',
        key: 'registerDeviceCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '未注册人脸卡口数量',
        slot: 'unregisterDeviceCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '人脸卡口资产注册率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '查看未注册设备',
            icon: 'icon-chakanweizhuceshebei',
            style: { color: '#438CFF' },
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 人脸卡口资产注册率
    FACE_ONLINE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '人脸卡口在线率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 110,
      },
      ...matchingRate,
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 人脸卡口联网率
    FACE_DEVICE_CONNECT_INTERNET: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸卡口联网率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
        isModifyName: true,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 人脸卡口设备图片存储时长达标率
    FACE_IMAGE_STORE_PASS: baseFaceFunc([
      {
        title: '人脸卡口设备图片存储时长达标率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
    ]),
    // 人脸抓拍数据上传合规率
    FACE_DATA_COMPLY_RULE_RATE: baseFaceFunc([
      {
        title: '人脸抓拍数据上传合规率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
    ]),
    // 人脸抓拍数据上传完整率
    FACE_CAPTURE_COMPLETENESS_RATE: baseFaceFunc([
      {
        title: '人脸抓拍数据上传完整率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
    ]),
    //人脸抓拍图片质量合格率
    FACE_QUALITY_PASS_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸设备抓拍图片合格率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    // 车辆
    VEHICLE_CAPTURE_RATIONALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆抓拍数据合理性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史无抓拍',
        key: 'errorNoData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日无抓拍',
        key: 'errorTodayNoData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '抓拍数量过少',
        key: 'errorTooLessData',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '抓拍数量突降',
        key: 'errorDataSwoop',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日活跃率',
        key: 'todayActivePercent',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 车辆卡口设备图片存储时长达标率
    VEHICLE_IMAGE_STORE_PASS: baseFaceFunc([
      {
        title: '车辆卡口设备图片存储时长达标率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
    ]),
    // 车辆抓拍数据合理性
    VEHICLE_CLOCK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆卡口设备时钟准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 车辆卡口设备时钟准确率
    VEHICLE_UPLOAD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆卡口设备及时上传率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 车辆卡口设备时钟准确率
    VEHICLE_FULL_INFO: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '车辆卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆卡口设备抓拍数据完整率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 车辆卡口设备抓拍数据完整率
    VEHICLE_FULL_INFO_IMPORTANT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '车辆卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点车辆卡口设备抓拍数据完整率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], //重点车辆卡口设备抓拍数据完整率
    VEHICLE_INFO_PASS_IMPORTANT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '车辆卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点车辆卡口设备过车数据准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '大图无法访问数量',
        key: 'urlNotAvailable',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '大图无法访问占比',
        key: 'urlNotAvailablePercent',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.urlNotAvailablePercent}%</span>;
        },
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], //重点车辆卡口设备过车数据准确率
    VEHICLE_INFO_PASS: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '车辆卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆卡口设备过车数据准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '大图无法访问数量',
        key: 'urlNotAvailable',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.urlNotAvailable || 0}</span>;
        },
      },
      {
        title: '大图无法访问占比',
        key: 'urlNotAvailablePercent',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.urlNotAvailablePercent || 0}%</span>;
        },
      },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], //车辆卡口设备过车数据准确率
    VEHICLE_UPLOAD_IMPORTANT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点车辆卡口设备及时上传率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点车辆卡口设备及时上传率
    VEHICLE_URL_AVAILABLE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆卡口设备过车图片地址可用率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 车辆卡口设备过车图片地址可用率
    VEHICLE_URL_AVAILABLE_IMPORTANT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点车辆卡口设备过车图片地址可用率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点车辆卡口设备过车图片地址可用率
    VEHICLE_QUANTITY_STANDARD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '达标数量',
        key: 'standardNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '满分数量',
        key: 'fullDeviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '上报数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实有区县数量',
        key: 'areaCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标区县',
        key: 'qualifiedAreaCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '车辆卡口数量达标率',
        key: 'areaResultValue',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '区县详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 车辆卡口数量达标率
    VEHICLE_ASSET_REGISTER: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '资产库已注册车辆卡口数量',
        key: 'registerDeviceCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '未注册车辆卡口数量',
        slot: 'unregisterDeviceCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '车辆卡口资产注册率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '查看未注册设备',
            icon: 'icon-chakanweizhuceshebei',
            style: { color: '#438CFF' },
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 车辆卡口资产注册率
    VEHICLE_ONLINE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '车辆卡口在线率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 110,
      },
      ...matchingRate,
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 车辆卡口设备在线率
    // 车辆卡口联网率
    VEHICLE_DEVICE_CONNECT_INTERNET: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆卡口联网率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
        isModifyName: true,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],

    // 填报准确率
    BASIC_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '填报准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 视图基础数据填报准确率
    FACE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '填报准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 人脸卡口填报准确率
    VEHICLE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '填报准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 车辆卡口填报准确率
    //车辆抓拍图片质量合格率
    VEHICLE_QUALITY_PASS_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '车辆设备抓拍图片合格率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    VIDEO_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '填报准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 视频监控填报准确率

    // 重点人员
    FOCUS_TRACK_URL_AVAILABLE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '轨迹总量',
        key: 'trackAmount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测轨迹数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格轨迹数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格轨迹数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '轨迹图片可访问率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 轨迹图片可访问率
    FOCUS_TRACK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '轨迹总量',
        key: 'trackAmount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测轨迹数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格轨迹数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格轨迹数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '轨迹准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 轨迹准确率
    FOCUS_TRACK_REAL: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '轨迹总量',
        key: 'trackAmount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测轨迹数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格轨迹数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格轨迹数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实时轨迹上传及时性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 实时轨迹上传及时性

    // 平台可用性指标
    DISTRIBUTED_IDENTITY_API_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '接口名称',
        key: 'apiName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        render: (h, { row }) => {
          return <span>{row.detail.apiName}</span>;
        },
      },
      {
        title: '今日检测次数',
        key: 'todayCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.todayCount || 0}</span>;
        },
      },
      {
        title: '检测合格次数',
        key: 'todayQualifiedCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.todayQualifiedCount || 0}</span>;
        },
      },
      {
        title: '本月检测次数',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.actualNum || 0}</span>;
        },
      },
      {
        title: '检测合格次数',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.qualifiedNum || 0}</span>;
        },
      },
      {
        title: '检测不合格次数',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.unqualifiedNum || 0}</span>;
        },
      },
      {
        title: '分布式身份确认接口稳定性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
    ], // 分布式身份确认接口稳定性
    PORTRAIT_TRACK_API_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '接口名称',
        key: 'apiName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        render: (h, { row }) => {
          return <span>{row.detail.apiName}</span>;
        },
      },
      {
        title: '今日检测次数',
        key: 'todayCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.todayCount || 0}</span>;
        },
      },
      {
        title: '检测合格次数',
        key: 'todayQualifiedCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.todayQualifiedCount || 0}</span>;
        },
      },
      {
        title: '本月检测次数',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.actualNum || 0}</span>;
        },
      },
      {
        title: '检测合格次数',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.qualifiedNum || 0}</span>;
        },
      },
      {
        title: '检测不合格次数',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '人像轨迹查询接口稳定性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
    ], // 人像轨迹查询接口稳定性
    FACE_MONITOR_API_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '布控照片',
        slot: 'monitorUrls',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月返回布控报警数量',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.receiveCount || 0}</span>;
        },
      },
      {
        title: '检测结果',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 人脸布控接口稳定性
    VEHICLE_TRACK_API_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '接口名称',
        key: 'apiName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        render: (h, { row }) => {
          return <span>{row.detail.apiName}</span>;
        },
      },
      {
        title: '今日检测次数',
        key: 'todayCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.todayCount || 0}</span>;
        },
      },
      {
        title: '检测合格次数',
        key: 'todayQualifiedCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.todayQualifiedCount || 0}</span>;
        },
      },
      {
        title: '本月检测次数',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.actualNum || 0}</span>;
        },
      },
      {
        title: '检测合格次数',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.qualifiedNum || 0}</span>;
        },
      },
      {
        title: '检测不合格次数',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '车辆轨迹查询接口稳定性',
        key: '',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
    ], // 车辆轨迹查询接口稳定性
    VEHICLE_MONITOR_API_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '布控照片',
        slot: 'monitorUrls',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月返回布控报警数量',
        key: 'receiveCount',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '检测结果',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 车辆布控接口稳定性
    FACE_CAPTURE_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '本月抓拍数量',
        slot: 'currentCaptureNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.currentCaptureNum || 0}</span>;
        },
      },
      {
        title: '去年统计抓拍数量',
        slot: 'lastCaptureNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.lastCaptureNum || 0}</span>;
        },
      },
      {
        title: '人脸抓拍数量上传稳定性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h) => {
          return (
            <Tooltip max-width="400" transfer>
              <span class="vt-middle">人脸抓拍数量上传稳定性</span>
              <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
              <template slot="content">
                <p class="mb-md f-14">人脸抓拍数据上传稳定性=本月去重抓拍数量/（去年统计抓拍数量*α）</p>
              </template>
            </Tooltip>
          );
        },
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
    ], // 人脸抓拍数量上传稳定性
    VEHICLE_CAPTURE_STABILITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '本月抓拍数量',
        slot: 'currentCaptureNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.currentCaptureNum || 0}</span>;
        },
      },
      {
        title: '去年统计抓拍数量',
        slot: 'lastCaptureNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.lastCaptureNum || 0}</span>;
        },
      },
      {
        title: '车辆抓拍数据上传稳定性',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h) => {
          return (
            <Tooltip max-width="400" transfer>
              <span class="vt-middle">车辆抓拍数据上传稳定性</span>
              <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
              <template slot="content">
                <p class="mb-md f-14">车辆抓拍数据上传稳定性=本月去重抓拍数量/（去年统计抓拍数量*α）</p>
              </template>
            </Tooltip>
          );
        },
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
    ], // 车辆抓拍数量上传稳定性
    FACE_PLATFORM_ONLINE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '本月在线率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月在线天数',
        key: 'onlineNumOfM',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月离线天数',
        key: 'unOnlineNumOfM',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月报备天数',
        key: 'reportDayNumOfM',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日在线状态',
        slot: 'customcol',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史在线天数',
        key: 'onlineNumOfH',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史离线天数',
        key: 'unOnlineNumOfH',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史报备天数',
        key: 'reportDayNumOfH',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 人脸视图库在线率
    VEHICLE_PLATFORM_ONLINE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '本月在线率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月在线天数',
        key: 'onlineNumOfM',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月离线天数',
        key: 'unOnlineNumOfM',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '本月报备天数',
        key: 'reportDayNumOfM',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '昨日在线状态',
        slot: 'customcol',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史在线天数',
        key: 'onlineNumOfH',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史离线天数',
        key: 'unOnlineNumOfH',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '历史报备天数',
        key: 'reportDayNumOfH',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ], // 车辆视图库在线率
    VIDEO_PLATFORM_ONLINE_RATE: [
      { type: 'index', width: 70, title: '序号', align: 'center' },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column }) => {
          let { statisticType } = params;
          column.key = statisticType === 'REGION' ? 'civilName' : 'orgName';
          return <span>所属平台</span>;
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '当前状态',
        key: 'isOnline',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          let val = (row.detail && row.detail.isOnline) || '';
          return (
            <span class={[val == '1' ? 'font-green' : val == '2' ? 'font-warning' : '']}>
              {val == '1' ? '在线' : val == '2' ? '离线' : ''}
            </span>
          );
        },
      },
      { title: '本月在线率', key: 'resultValueFormat', tooltip: true, minWidth: 150 },
      { title: '本月在线时长', key: 'onlineTimeM', tooltip: true, minWidth: 150 },
      { title: '本月离线时长', key: 'unOnlineTimeM', tooltip: true, minWidth: 150 },
      { title: '本月报备天数', key: 'reportDayNumOfM', tooltip: true, minWidth: 150 },
      { title: '昨日离线时长', key: 'unOnlineTime', tooltip: true, minWidth: 150 },
      { title: '历史在线时长', key: 'onlineTimeH', tooltip: true, minWidth: 150 },
      { title: '历史离线时长', key: 'unOnlineTimeH', tooltip: true, minWidth: 150 },
      { title: '历史报备天数', key: 'reportDayNumOfH', tooltip: true, minWidth: 150 },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        width: 80,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '离线详情',
            icon: 'icon-lixianxiangqing',
            emitFun: 'viewDetail',
          },
          {
            type: 'detection-detail',
            text: '检测详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetectionDetail',
          },
        ],
      },
    ], // 共享联网平台在线率

    // 新增指标
    COMPOSITE_INDEX: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '新增指标名称',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        isModifyName: true,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            type: 'detail',
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    FACE_CATALOGUE_SAME: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '视图库人脸卡口总量',
        key: 'beanReportedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '有图片未上报视图库人脸卡口数量',
        key: 'unReportedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '联网人脸卡口目录一致率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    VEHICLE_CATALOGUE_SAME: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 130,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '视图库车辆卡口总量',
        key: 'beanReportedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '有图片未上报视图库车辆卡口数量',
        key: 'unReportedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '联网车辆卡口目录一致率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    // 重点车辆卡口设备主要属性准确率
    VEHICLE_MAIN_PROP: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '车辆卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点车辆卡口设备主要属性准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '大图无法访问数量',
        key: 'urlNotAvailable',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.urlNotAvailable || 0}</span>;
        },
      },
      {
        title: '大图无法访问占比',
        key: 'urlNotAvailablePercent',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.urlNotAvailablePercent || 0}%</span>;
        },
      },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    // 重点车辆卡口设备类型属性识别准确率
    VEHICLE_TYPE_PROP: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '车辆卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '重点车辆卡口设备类型属性识别准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 170,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'center',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    // 实时视频离线统计
    VIDEO_OFFLINE_STAT: offlineStatFn(params),
    // 人脸卡口离线统计
    FACE_OFFLINE_STAT: offlineStatFn(params),
    // 车辆卡口离线统计
    VEHICLE_OFFLINE_STAT: offlineStatFn(params),
    // 视频流编码规范率
    VIDEO_CODE_STANDARD_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '视频监控设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '视频流编码规范率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 人脸设备抓拍图片评分率
    FACE_CAPTURE_SCORE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '人脸卡口设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人脸设备抓拍图片评分率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [
          {
            text: '详情',
            icon: 'icon-chakanxiangqing',
            emitFun: 'viewDetail',
          },
        ],
      },
    ],
    // 联网数量提升率
    VIDEO_NETWORKING_PROMOTION_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '当前设备数量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.deviceNum || 0}</span>;
        },
      },
      {
        title: '去年年末设备数量',
        key: 'lastYearDeviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        render: (h, { row }) => {
          return <span>{row.detail.lastYearDeviceNum || 0}</span>;
        },
      },
      {
        title: '联网数量提升率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'center',
        tooltip: true,
        width: 100,
      },
    ],
    // 人体卡口设备在线率
    BODY_ONLINE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '人体卡口设备在线率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 110,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 人体卡口设备活跃率
    BODY_ACTIVE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人体卡口设备活跃率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
        isModifyName: true,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 人体卡口设备时钟准确率
    BODY_CLOCK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'cutom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人体卡口设备时钟准确率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 220,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    // 人体卡口设备及时上传率
    BODY_UPLOAD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测设备数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '人体卡口设备及时上传率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 180,
      },
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ],
    VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
        sortable: 'custom',
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegion(h, { column, index, params });
        },
      },
      {
        title: '行政区划代码',
        key: 'civilCode',
        align: 'left',
        tooltip: true,
        minWidth: 100,
        renderHeader: (h, { column, index }) => {
          return renderHeaderRegionCode(h, { column, index, params });
        },
      },
      {
        title: '设备总量',
        key: 'deviceNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '实际检测数量',
        key: 'actualNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '合格设备数量',
        key: 'qualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '不合格设备数量',
        key: 'unqualifiedNum',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '重点指挥图像在线率',
        key: 'resultValueFormat',
        align: 'left',
        tooltip: true,
        minWidth: 110,
      },
      ...matchingRate,
      {
        title: '达标情况',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        minWidth: 100,
      },
      {
        title: '操作',
        slot: 'action',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
        btnArr: [{ text: '详情', icon: 'icon-chakanxiangqing', emitFun: 'viewDetail' }],
      },
    ], // 重点指挥图像在线率
  };
};
export default tableColumns;
