<template>
  <ui-modal v-model="visible" title="配置抽检对象和抽取设备数量" :styles="styles" class="ui-modal" @query="query">
    <div class="config-define-container">
      <ui-label label="抽取设备数量">
        <InputNumber class="input-width" v-model="carNum" placeholder="抽取设备数量"></InputNumber>
        <Button type="text" class="ml-lg" @click="bulkDeviceNum">批量填入</Button>
      </ui-label>
      <div class="tree-wrapper">
        <section class="tree-wrapper-title mt-sm">
          <p class="tree-wrapper-title-item">抽取对象</p>
          <p class="tree-wrapper-title-item t-center">抽取设备数量</p>
        </section>
        <Form ref="formValidateRef" :model="orgCountMap" :label-width="0">
          <ui-search-tree
            ref="uiTree"
            class="ui-search-tree"
            no-search
            :highlight-current="false"
            node-key="nodeKey"
            :tree-data="newAreaTreeData"
            :default-props="defaultProps"
            expandAll
            checkStrictly
          >
            <template #label="{ node, data }">
              <div class="ui-search-tree-options" :class="data.children && 'is_parent'">
                <p>
                  <Checkbox v-model="data.check" class="mr-sm" @on-change="check($event, node, data)"></Checkbox>
                  <span>{{ data.orgName }}</span>
                  <Button
                    type="text"
                    @click.stop="checkAll(node, data)"
                    :style="{ visibility: !data.children ? 'hidden' : '' }"
                    >{{ `${data.checkAll ? '取消全选' : '全选'} ` }}</Button
                  >
                </p>
                <FormItem
                  v-if="data.check"
                  label=""
                  :prop="data.orgCode"
                  :rules="{ required: true, message: ' ', trigger: 'blur', type: 'number' }"
                >
                  <InputNumber
                    v-model="data.deviceNum"
                    class="input-wrapper input-width"
                    placeholder="请输入抽取设备数量"
                    clearable
                    :min="1"
                  >
                  </InputNumber>
                </FormItem>
              </div>
            </template>
          </ui-search-tree>
        </Form>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>
<script>
export default {
  data() {
    return {
      carNum: null,
      orgCountMap: {},
      newAreaTreeData: [],
      loading: false,
    };
  },
  methods: {
    check(val, node) {
      this.checkParent(node, val);
    },
    bulkDeviceNum() {
      this.asyncDeviceNum(this.newAreaTreeData);
    },
    asyncDeviceNum(data) {
      data.forEach((item) => {
        item.check ? this.$set(item, 'deviceNum', this.carNum) : null;
        if ('children' in item && !!item.children.length) {
          this.asyncDeviceNum(item.children);
        }
      });
    },
    checkAll(node, data) {
      if (node.childNodes) {
        this.$set(data, 'checkAll', !data.checkAll);
        this.checkParent(node, data.checkAll);
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
          this.$set(item.data, 'deviceNum', null);
        });
      }
    },
    checkParent(node, check) {
      this.$set(node.data, 'check', check);
      this.$set(node.data, 'deviceNum', null);
    },
    query() {
      this.orgCountMap = {};
      this.findAllDeviceNum(this.newAreaTreeData);
      this.$nextTick(() => {
        this.$refs.formValidateRef.validate((valid) => {
          if (valid) {
            this.$emit('selfSelectedDevice', this.orgCountMap);
          } else {
            this.$Message.error('选中设备抽取数量必填');
          }
        });
      });
    },
    findAllDeviceNum(data) {
      data.forEach((item) => {
        !!item.check && !!item.deviceNum ? (this.orgCountMap[item.orgCode] = item.deviceNum) : null;
        if ('children' in item && !!item.children.length) {
          this.findAllDeviceNum(item.children);
        }
      });
    },
    trampoline(f) {
      while (f && f instanceof Function) {
        f = f();
      }
      return f;
    },
    handleDefaultDeviceNum(data) {
      data.forEach((item) => {
        if (item.orgCode in this.originForm.orgCountMap) {
          this.$set(item, 'check', true);
          this.$set(item, 'deviceNum', this.originForm.orgCountMap[item.orgCode]);
        }
        if ('children' in item && !!item.children.length) {
          this.handleDefaultDeviceNum(item.children);
        }
      });
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        this.newAreaTreeData = this.$util.common.deepCopy(this.areaTreeData);
        if (val && !this.originForm.whole) {
          this.handleDefaultDeviceNum(this.newAreaTreeData);
        }
      },
      immediate: true,
    },
  },
  computed: {},
};
</script>
<script setup>
import UiSearchTree from '@/components/ui-search-tree.vue';
import { computed, reactive } from 'vue';
const props = defineProps({
  modelValue: Boolean,
  areaTreeData: {
    require: true,
    type: Array,
    default: () => {
      return [];
    },
  },
  originForm: {
    require: true,
    type: Object,
    default: () => {},
  },
  defaultProps: {
    default: () => {
      return {
        label: 'orgName',
        children: 'children',
      };
    },
  },
});
const emit = defineEmits(['update:modelValue']);
const styles = reactive({
  width: '4rem',
});
const visible = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit('update:modelValue', value);
  },
});
</script>
<style lang="less" scoped>
.config-define-container {
  padding: 10px 20px;
  .input-width {
    width: 200px;
  }
}
@{_deep}.ivu-form-item {
  margin-bottom: 0;
}
.tree-wrapper {
  border: 1px solid var(--border-color);
  border-top: 0;
  .tree-wrapper-title {
    display: flex;
    color: var(--color-table-header-th);
    background: var(--bg-table-header-th);
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    .tree-wrapper-title-item {
      flex: 1;
    }
  }
  .ui-search-tree {
    height: 500px;
    @{_deep}.el-tree {
      padding: 0px 20px 20px 0;
      .el-tree-node__content {
        height: 40px;
        line-height: 40px;
      }
    }
    @{_deep}.ui-search-tree-options {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .ui-search-tree-options-p {
        margin: 20px 0;
      }
    }
  }
}
</style>
