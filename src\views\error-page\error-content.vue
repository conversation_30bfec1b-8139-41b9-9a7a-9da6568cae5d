<template>
  <div class="error-page">
    <div class="content-con">
      <img :src="src" alt='' />
      <div class="text-con">
        {{ desc }}
      </div>
      <back-btn-group class="back-btn-group"/>
    </div>
  </div>
</template>

<script>
import './error.less'
import backBtnGroup from './back-btn-group.vue'

export default {
  name: 'ErrorContent',
  components: {
    backBtnGroup
  },
  props: {
    desc: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: ''
    }
  }
}
</script>
