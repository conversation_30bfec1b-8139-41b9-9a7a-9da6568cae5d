<template>
  <!--  flex-column-center -->
  <div class="height-full width-full auto-fill">
    <div class="home-container" :class="getFullscreen ? 'full-screen-container' : ''" v-if="!componentName">
      <create-tabs
        ref="createTabsRef"
        style="display: inline"
        :componentName="activeComponent"
        :tabs-text="activeTabsName"
        @selectModule="selectModule"
        :tabs-query="activeTabsQuery"
      >
      </create-tabs>
      <top-title v-if="getFullscreen"></top-title>
      <!-- 基础数据监测 -->
      <plant-assets
        ref="plantAssets"
        :batchIds="batchIds"
        :indexModules="indexModules"
        @makeCreateTabs="makeCreateTabs"
      />
      <!-- 档案数据监测 -->
      <detection-file ref="detectionFile" :batchIds="batchIds" @makeCreateTabs="makeCreateTabs" />
      <map-echarts
        v-if="!isActivated"
        :home-page-config="homePageConfig"
        :query-access-data="queryAccessData"
        @on-jump="onJumpMap"
      ></map-echarts>

      <!--  顶部统计  -->
      <statistics-top ref="statistics-top" :query-access-data="queryAccessData"></statistics-top>
      <full-screen ref="full-screen"></full-screen>
      <!-- zdr数据检测 -->
      <ZdrDataDetection
        ref="zdrDataDetection"
        @makeCreateTabs="makeCreateTabs"
        :batchIds="batchIds"
        :indexModules="indexModules"
      />
      <!-- 考核排行榜 -->
      <AssessRanking :task-type="evaluationTask" @toResultExamination="toResultExamination" />
      <!-- 图像数据监测 -->
      <ImageDataMonitor :batchIds="batchIds" :indexModules="indexModules" @makeCreateTabs="makeCreateTabs" />
      <div class="location" :class="getFullscreen ? 'full-screen-location' : ''">
        <!-- <span class="icon-font icon-dingwei f-14"></span>
        <span class="f-14">
          {{ regionName }}
        </span> -->
        <notice ref="noticeRef">
          <span @click="noticeMore" class="pointer view-detail ml-sm">更多公告 >></span>
        </notice>
      </div>
    </div>
    <keep-alive :include="cacheRouterName" v-else>
      <component
        :is="componentName"
        :style="componentName === 'overviewEvaluation' ? { padding: `${20 / 192}rem` } : {}"
      >
      </component>
    </keep-alive>
  </div>
</template>

<script>
import home from '@/config/api/home';
import evaluationreport from '@/config/api/evaluationreport';
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'home',
  props: {},
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      isActivated: false,
      queryAccessData: [
        {
          label: '设备总量',
          key: 'deviceTotalAmount',
          deviceTotalAmount: 0,
          color: '#25f3f1',
          icon: 'icon-shitujichushuju',
        },
        {
          label: '人脸抓拍抽样总量',
          key: 'faceViewAmount',
          faceViewAmount: 0,
          color: '#bf89f7',
          icon: 'icon-renliankakou',
        },
        {
          label: '车辆抓拍抽样总量',
          key: 'vehicleViewAmount',
          vehicleViewAmount: 0,
          color: '#f5af55',
          icon: 'icon-cheliangkakou',
        },
        {
          label: 'ZDR人像轨迹抽样总量',
          key: 'zdrTrackAmount',
          zdrTrackAmount: 0,
          color: '#ea5252',
          icon: 'icon-keypersonlibrary',
        },
      ],
      taskType: 1, // 前端写死, 获取batchIds使用
      batchIds: [],
      indexModules: {
        basic: { module: '视图基础数据', value: 1 },
        face: { module: '人脸视图数据', value: 2 },
        car: { module: '车辆视图数据', value: 3 },
        video: { module: '视频流数据', value: 4 },
        zdr: { module: '重点人员数据', value: 5 },
        monitor: { module: '视频监控', value: 6 },
      },
      evaluationTask: '',
      regionName: '',
      activeTabsQuery: {
        displayType: 'REGION',
        indexId: null,
        code: null,
        batchId: null,
        access: 'EXAM_RESULT',
        uuid: null,
      },
      activeComponent: 'overviewEvaluation',
      activeTabsName: '评测详情',
      homePageConfig: {},
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
    cacheRouterName() {
      let cacheList = [];
      this.cacheRouterList.forEach((row) => {
        let arr = row.path.split('/');
        // 去除首个空路由
        arr.splice(0, 1);
        arr.push(row.name);
        // 缓存路由为标签路由列表与当前所有路由
        cacheList = [...cacheList, ...arr];
      });
      cacheList = Array.from(new Set(cacheList));
      return cacheList;
    },
  },
  watch: {
    $route: 'getParams',
  },
  filter: {},
  deactivated() {
    this.isActivated = true;
  },
  activated() {
    this.isActivated = false;
  },
  async mounted() {
    this.getParams();
    if (!this.componentName) {
      await this.viewByParamKey();
      await this.getOptionalResultsByTaskType();
      await this.initQueryDeviceOfAccessDataCount();
    }
  },
  methods: {
    ...mapActions({
      setHomeConfig: 'home/setHomeConfig',
    }),
    /**
     *  地图跳转评测总览
     */
    onJumpMap(originData, params) {
      let { civilCode } = params;
      let { taskSchemeId } = this.homePageConfig;
      let query = {
        regionCode: `${civilCode}`,
        statisticType: 'REGION',
        taskSchemeId: taskSchemeId,
      };
      this.$router.push({
        name: 'evaluationoResult',
        query: query,
      });
    },
    handleClickMap({ data: { regionCode, name } }) {
      this.activeComponent = 'evaluationoResult';
      this.activeTabsName = '评测结果';
      this.activeTabsQuery = {
        regionCode,
        regionName: name,
      };
      this.$nextTick(() => {
        this.$refs.createTabsRef.create();
      });
    },
    toResultExamination(item) {
      this.activeComponent = 'resultExamination';
      this.activeTabsName = '考核成绩';
      this.activeTabsQuery = {
        examTaskId: this.evaluationTask,
        orgCode: item.regionCode,
      };
      this.$nextTick(() => {
        this.$refs.createTabsRef.create();
      });
    },
    makeCreateTabs(params) {
      this.activeComponent = 'overviewEvaluation';
      this.activeTabsName = '评测详情';
      this.activeTabsQuery = {
        displayType: 'REGION',
        indexId: params.indexId,
        code: params.regionCode,
        batchId: params.batchId,
        access: 'EXAM_RESULT',
        uuid: params.batchId,
      };
      this.$nextTick(() => {
        this.$refs.createTabsRef.create();
      });
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
      this.$nextTick(() => {
        if (this.$refs.noticeRef) {
          this.$refs.noticeRef.initFunc();
        }
      });
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr.length - 1;
      }
      if (this.$route.query.componentName) {
        let components = name.split('-');
        this.componentName = components[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    async initQueryDeviceOfAccessDataCount() {
      try {
        let {
          data: { data },
        } = await this.$http.get(home.queryDeviceOfAccessDataCount);
        if (!Object.keys(data).length) return false;
        this.queryAccessData = this.queryAccessData.map((item) => {
          item[item.key] = data[item.key];
          return item;
        });
      } catch (e) {
        console.log(e);
      }
    },
    async getOptionalResultsByTaskType() {
      try {
        let {
          data: { data },
        } = await this.$http.get(evaluationreport.getOptionalResultsByTaskType, {
          params: {
            taskType: this.taskType,
          },
        });
        this.$set(this, 'batchIds', data.batchIds ? data.batchIds : []);
      } catch (err) {
        console.log(err);
      }
    },
    async viewByParamKey() {
      try {
        let params = { key: 'HOME_PAGE_CONFIG' };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = JSON.parse(data.paramValue || '{}');
        this.homePageConfig = paramValue;
        this.regionName = paramValue.regionName;
        this.evaluationTask = paramValue.assessTask;
        this.setHomeConfig(paramValue);
      } catch (e) {
        console.log(e);
      }
    },
    noticeMore() {
      this.$router.push({
        name: 'noticeannouncements',
      });
    },
  },
  components: {
    ImageDataMonitor: require('./agencies-components/image-data-monitor.vue').default,
    ZdrDataDetection: require('./agencies-components/zdr-data-detection.vue').default,
    AssessRanking: require('./agencies-components/assess-ranking.vue').default, //右上排名列表
    PlantAssets: require('./agencies-components/plant-assets.vue').default,
    DetectionFile: require('./agencies-components/detection-file.vue').default,
    MapEcharts: require('@/views/home/<USER>/map-echarts.vue').default,
    StatisticsTop: require('./components/statistics-top.vue').default,
    FullScreen: require('./agencies-components/full-screen').default,
    TopTitle: require('./components/top-title').default,
    Notice: require('./components/notice').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    NoticeAnnouncements: require('@/views/notification/noticeannouncements/index.vue').default,
    overviewEvaluation: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
    resultExamination: require('@/views/governanceevaluation/resultExamination/index.vue').default,
    evaluationoResult: require('@/views/governanceevaluation/evaluationoResult/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.home-container {
  background: url('~@/assets/img/home/<USER>') no-repeat;
  background-position: center;
  background-size: cover;
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px;
}
.location {
  position: absolute;
  left: 27%;
  top: 10%;
  color: #2389d0;
  display: flex;
  align-items: center;
  @{_deep} .notice-box-div {
    width: 764px;
  }
}
.ml-llg {
  margin-left: 30px;
}
.view-detail {
  color: var(--color-primary);
}
.full-screen-container {
  padding: 0;
}
.full-screen-location {
  top: 16%;
}
@{_deep}.map-echarts {
  top: 11%;
}
</style>
