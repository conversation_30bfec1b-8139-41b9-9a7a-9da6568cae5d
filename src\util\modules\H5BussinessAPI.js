var H5BussinessAPI = {
  //用于单独判断是否具有H5对象
  EL() {
    var $el = document.getElementById("assistantH5"),
      $cacheEl,
      Instance;
    Instance = H5Player;
    $cacheEl = $(
      '<div id="assistantH5" style="position: fixed; top: -10px; left: -10px;overflow:hidden"></div>'
    );
    !$el && $("body").append($cacheEl);
    return new Instance({
      ele: "#assistantH5",
      layout: "1*1",
    });
  },
  ELCLASS() {
    return H5Player;
  },
  /**
   * 判断是否安装了播放器
   */
  checkPlayer() {
    return new Promise((resolve) => {
      let clazz = Toolkits.isHighVersion() ? this : this.ELCLASS();
      clazz.setupversionV2().then((result) => {
        if (result && result.isexist && !result.hasOwnProperty("isUpdate")) {
          let exevesion = result.setupver,
            sdkVesion = this.getVersion();
          result.isUpdate = false;
          if (exevesion && sdkVesion) {
            exevesion = exevesion.split(".");
            sdkVesion = sdkVesion.split(".");
            let maxLength = Math.max(exevesion.length, sdkVesion.length);
            for (let i = 0, diff = 0; i < maxLength; i++) {
              diff = sdkVesion[i] - exevesion[i];
              if (diff > 0) result.isUpdate = true;
              if (diff !== 0) break;
            }
          }
        }
        resolve(result);
      });
    });
  },
  /**
   * [getVersion 获取控件版本号]
   * <AUTHOR>
   * @date 2018-03-17
   * @param
   * @return {[字符串]}     [版本号，如”PVA_OCX_V0.0.1”]
   */
  getVersion() {
    return this.ELCLASS().version;
  },
  /**
     * [runProgress 执行指定的进程]
     * info
     *  {
	        "path": val, [进程全路径，如果含中文、空格等需要加引号]
	        "showtype": 1, [显示方式 0：隐藏；1：正常；2：最小化；3：最大化]
	        "param": "npwonder@" + cacheImgUrl + "@" + requestId + "@" + remateUrl [启动参数 如果含中文、空格等需要加引号]
	    }
     * @return {[res.error]}      [0表示正确，负数表示错误码]
     */
  runProgress(info) {
    return new Promise((resolve) => {
      this.ELCLASS().runProcess(info, (res) => {
        resolve(res.error);
      });
    });
  },
  /**
	 * [moveProcessWindow 发送屏幕到制定位置
	 * @param  {[字符串]} info    [指定进程的进程名称]
	 * {
            "process": "chrome.exe",
            "title": "视频图像解析系统-扩展屏",
            "showtype": 1, [0代表仅移动位置，宽度和高度无效， 1代表移动到指定点所在屏幕并最大化显示， 2代表移动到指定位置并设置为指定大小]
            "left": rect.left,
            "top": rect.top,
            "width": rect.width,
            "height": rect.height
		}
	 * @callback {[res.error]}      [0表示正确，负数表示错误码]
	 */
  moveProcessWindow(info) {
    return new Promise((resolve) => {
      this.ELCLASS().moveProcessWindow(info, (res) => {
        resolve(res.error);
      });
    });
  },
  /**
   * 剪切版功能，输入base64字符串 ctrl+V 输出解码后的字符串
   * context, callback
   * @param {*} info
   */
  copyToClipboard(context, callback) {
    this.ELCLASS().copyToClipboard(info, callback);
  },
  /**
   * 文件是否存在,filepath <String>
   * @param {*} filepath,callback
   */
  isFileExist(filepath, callback) {
    this.ELCLASS().isFileExist(filepath, callback);
  },
  /**
   * 删除本地文件,filepath <String>
   * @param {*} filepath,callback
   */
  removeFile(filepath, callback) {
    this.ELCLASS().removeFile(filepath, callback);
  },
  /**
   * 保存本地图片，输入base64 （内容太长有问题）
   * @param {*} info
   * @param {*} callback
   */
  savePicture(info, callback) {
    this.ELCLASS().savePicture(info, callback);
  },
  /**
   * openSaveFileDlg 打开对话框
   * info 窗口参数
   */
  openSaveFileDlg(info) {
    return new Promise((resolve) => {
      this.ELCLASS().openSaveFileDlg(info, (res) => {
        resolve(res);
      });
    });
  },
  /**
   *defpath,callback
   * defpath:默认路径'D:/Recods'
   */
  openLocalSelectFolderDlg(defpath, callback) {
    this.ELCLASS().openLocalSelectFolderDlg(defpath, callback);
  },
  /**
   * [getDownloadRecordDirectory 获取录像下载的存储目录]
   * <AUTHOR>
   * @date 2018-03-17
   * @return {[字符串]}     [录像下载的存储目录]
   */
  getDownloadRecordDirectory() {
    return this.EL().getDownloadDir();
  },
  /**
   * [stopUploadPFSFile 停止下载]
   * @param  {[整数]} strid   [StartUploadPFSFile()成功返回的strid]
   * @param  {[布尔]} bdel    [true表示停止上传的任务并删除服务器上已上传的文件；false表示停止上传的任务]
   * @return {[整数]}      [正数表示正确，负数表示错误码]
   */
  stopDownload(strid, bdel, callback) {
    return this.EL().stopDownload(id, param);
  },
  /**
   * [startUploadPFSFile 上传文件到PFS上]
   * <AUTHOR>
   * @date 2018-03-17
   * @param  {[Json字符串]} uploadInfo   [上传的文件信息]
   * @param  {[函数]} CallBack   [上传的结果及上传的状态回调]
   * @param  {[整数]} userParam   [用户参数，CallBack()回传给用户]
   * @return {[Json字符串]}      [格式为：{"id": 123,"error":0}。id表示任务的标识；error=0表示正确，error=负数表示错误码]
   * 说明：1.返回值中，若”error”为负值，不会触发回调
   */
  startUploadPFSFile(uploadInfo, CallBack) {
    return this.EL().startDownload(uploadInfo, CallBack);
  },
  /**
     * [deletePFSFile 删除PFS文件（普通存储方式）]
     * info 
        ip <String> 
        pfs服务器ip
        port <Number> 
        pfs服务器端口
        username <String> 
        pfs服务器用户名
        password <String> 
        pfs服务器密码
        path <String>
     */
  deletePFSFile(info, callback) {
    return this.EL().deletePfsFile(info, callback);
  },
  /**
   * [getMacString 获取本地MAC地址]
   * @param
   * @return {[Json字符串]}      []
   */
  getMacString: function () {
    return new Promise((resolve) => {
      this.ELCLASS().getMacString((data) => {
        resolve(data.value);
      });
    });
  },
  /**
   * [getExpandScreenInfo 获取显示器信息]
   * <AUTHOR>
   * @date 2018-03-17
   * @param
   * @return {[Json字符串]}      [扩展屏数量，当前鼠标是否在主屏幕上，显示器分辨率及所有显示器的属性]
   */
  getExpandScreenInfo() {
    return new Promise((resolve) => {
      this.ELCLASS().getExpandScreenInfo((data) => {
        resolve(data.info);
      });
    });
  },
  /**
   * [getRegistryInfo 查询注册表指定key值(类型为字符串)对应value值]
   * @date 2018-03-17
   * @param  {[info]} info   [指定要获取key值]
   * @return {callback}      [返回option指定的信息]
   */
  getRegistryInfo(info) {
    return new Promise((resolve) => {
      this.ELCLASS().getRegistryInfo(info, (data) => {
        resolve(data.value);
      });
    });
  },
  /**
     * [下载Http文件]
        error number 0-成功，负数-错误码
        id string返回下载唯一标识
        status number 0初始状态 1为开始 2为下载中 3为暂停 4为完成
        process number下载进度 0-10000
        process number 下载进度 0-10000
        bitsrate number 下载速度
        totalsize string文件总大小 3.70GB
        currentsize string 已下载大小 3.21MB
        filename string
        pfsdown、http 已下载的文件名 D:/RecordDownloads/t01f58f.jpg
        filenameList  string
        pvg67、pvgplus {filename:'D:/RecordDownloads/t0.mbf'
        filepath  string
        pfsup "{"ip":"*************","port":9000,"username":"admin","password":"admin","path":"/video/A3CE0C9F-BF1.jpg"}"
     */
  startDownloadHttpFile(info, callback) {
    this.EL().startDownload(info, callback);
  },
  /**
   * [getMemory 获取当前进程内存使用情况]
   * <AUTHOR>
   * @date 2018-03-17
   * @param
   * @return {[Json字符串]}      []
   */
  getMemory() {
    return this.ELCLASS().getCpuMemory();
  },
  /**
   * 打开文件所在目录
   * @param {*} info
   * @param {*} callback
   */
  openFileDirectory(filepath, callback) {
    this.EL().openFileDirectory(filepath, callback);
  },
  setupversionV2() {
    return new Promise((reslove) => {
      $http({
        url: "webExe/h5vp_update.json",
        type: "GET",
      }).then((result) => {
        this.ELCLASS().isUpdateSetup(JSON.stringify(result), (res) => {
          if (res.error == 0 && res.isUpdate) {
            reslove({
              isexist: true,
              isUpdate: true,
              ocxConfig: {
                ...result,
                specialversion: "",
              },
            });
          } else if (res.error == -1) {
            reslove({
              isexist: false,
              isUpdate: true,
              ocxConfig: {
                ...result,
                specialversion: "",
              },
            });
          } else {
            reslove({
              isexist: true,
              isUpdate: false,
            });
          }
        });
      });
    });
  },
};
export default H5BussinessAPI;
