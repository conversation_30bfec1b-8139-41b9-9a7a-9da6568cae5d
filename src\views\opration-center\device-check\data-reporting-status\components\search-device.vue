<template>
	<div class="search  card-border-color">
		<Form :inline="true">
			<div class="general-search">
				<div class="input-content">
					<div class="input-content-row">
						<FormItem label="分组路径:">
							<Input v-model="formData.dirPath" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="设备名称:">
							<Input v-model="formData.deviceName" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="选择设备:">
							<div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{ formData.selectDeviceList.length }}）</div>
          	</FormItem>
                        <FormItem label="数据类型:">
							<Select v-model="formData.dataTypeList" placeholder="请选择" multiple :max-tag-count="1">
								<Option :value="item.dataKey" v-for="(item, index) in dataTypeArrayList" :key="index">{{ item.dataValue }}</Option>
							</Select>
						</FormItem>
						<FormItem label="数据有无状态:">
							<Select v-model="formData.haveDataStatusList" placeholder="请选择" multiple :max-tag-count="1">
								<Option :value="item.dataKey" v-for="(item, index) in haveDataStatusList" :key="index">{{ item.dataValue }}</Option>
								
							</Select>
						</FormItem>
                        <FormItem label="数据延迟状态:">
							<Select v-model="formData.delayStatusList" placeholder="请选择" multiple :max-tag-count="1">
								<Option :value="item.dataKey" v-for="(item, index) in delayStatusList" :key="index">{{ item.dataValue }}</Option>
								
							</Select>
						</FormItem>
					</div>
				</div>
				<div class="btn-group">
					<Button type="primary" @click="search">查询</Button>
					<Button type="default" @click="resetForm">重置</Button>
				</div>
			</div>
		</Form>

		<select-device 
			ref="selectDevice" 
			:showOrganization="true"
			@selectData="selectData" />
	</div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import { getConfigDate } from '@/util/modules/common';
export default {
    props:{
        formData: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
	data() {
		return {
			
			dateTimeList: [ ]
		}
	},
	computed: {
		...mapGetters({
            haveDataStatusList: 'dictionary/getHaveDataStatusList', // 数据有无状态
            delayStatusList: 'dictionary/getDelayStatusList', //数据延迟状态
            dataTypeArrayList: 'dictionary/getDataTypeList', // 数据类型
		})
	},
	async created() {
		await this.getDictData();
	},
	methods: {
		...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
		search() {
			this.$emit('searchInfo')
		},
		resetForm() {
			this.$emit('resetForm')
		},
		/**
		 * 选择设备
		 */
		selectDevice() {
			this.$refs.selectDevice.show(this.formData.selectDeviceList, '')
		},
		selectData(arr) {
			this.formData.selectDeviceList = arr
			this.$forceUpdate()
		},
	}
}
</script>
<style lang="less" scoped>
.btn-group {
    display: flex;
    align-items: flex-end;
    margin-bottom: 16px;
    justify-content: flex-end;
}
.search {
    padding: 16px 20px 0;
    border-bottom: 1px solid #ffff;
    display: flex;
    width: 100%;
    justify-content: space-between;
    position: relative;
    .ivu-form-inline {
        width: 100%;
    }
    .ivu-form-item {
        margin-bottom: 16px;
        margin-right: 20px;
        display: flex;
        align-items: center;
        /deep/ .ivu-form-item-label {
            white-space: nowrap;
            max-width: 90px;
        }
        /deep/ .ivu-form-item-content {
            display: flex;
						width: 170px;
        }
    }
    .general-search {
        display: flex;
        width: 100%;
        .input-content {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            .input-content-row {
                display: flex;
								flex-wrap: wrap;
            }
        }
    }
}
</style>
