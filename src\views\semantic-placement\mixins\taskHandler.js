// 处理任务删除资源删除的公共逻辑
import {
  getLLMCompareAlgorithmPageList,
  removeLLMCompareTask,
  removeTaskResourceLLMCompareTask,
  startLLMCompareTask,
  stopLLMCompareTask,
} from "@/api/semantic-placement.js";
import { taskTypeList, taskStatusList } from "../enums/index.js";
export default {
  data() {
    return {
      taskTypeList,
      taskStatusList,
      selectedData: [],
      algorithmList: [],
    };
  },
  methods: {
    /**
     * 获取算法列表
     */
    async getLLMCompareAlgorithm() {
      const { data } = await getLLMCompareAlgorithmPageList({
        pageNumber: 1,
        pageSize: 100,
      });
      this.algorithmList = data?.entities || [];
    },
    selectChangeHandler(selection) {
      this.selectedData = [...selection];
    },
    //删除---删除任务&批量删除任务
    deleteJobs(data) {
      const ids = data.map((item) => item.id);
      removeLLMCompareTask(ids).then((res) => {
        if (res.msg == "成功") {
          this.$Message.success("删除成功");
          this.getList();
        }
      });
    },
    //批量删除子任务
    deleteTasks(data) {
      const ids = data.map((item) => item.id);
      removeTaskResourceLLMCompareTask(ids).then((res) => {
        if (res.msg == "成功") {
          this.$Message.success("删除成功");
          this.getList();
        }
      });
    },
    // 任务开启
    async startJobs(data) {
      const ids = data.map((item) => item.id);
      try {
        const res = await startLLMCompareTask(ids);
        if (res.msg == "成功") {
          this.$Message.success("开启成功");
        }
        return true;
      } catch (e) {
        console.log(e);
        return false;
      }
    },
    // 任务结束
    async stopJobs(data) {
      const ids = data.map((item) => item.id);
      try {
        const res = await stopLLMCompareTask(ids);
        if (res.msg == "成功") {
          this.$Message.success("关闭成功");
        }
        return true;
      } catch (e) {
        console.log(e);
        return false;
      }
    },
    // 资源检索
    toDetailByTask(val) {
      const { taskId, taskType, resourceId, taskResourceList = [] } = val;
      let resourceIds = [];
      if (taskResourceList?.length > 0) {
        resourceIds = taskResourceList.map((item) => item.resourceId);
      } else {
        resourceIds = resourceId ? [resourceId] : [];
      }
      const param = {
        taskId: taskId,
        taskType: taskType,
        // resourceIds,
      };
      const { href } = this.$router.resolve({
        path: "/semantic-placement/alert-manager",
        query: {
          ...param,
          noMenu: 1,
        },
      });
      window.open(href, "_blank");
    },
  },
};
