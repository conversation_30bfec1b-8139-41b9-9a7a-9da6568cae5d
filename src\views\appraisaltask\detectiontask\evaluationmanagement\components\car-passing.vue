<template>
  <!-- 过车数据上传合规率弹框 -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-if="!!trackList">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
        </div>
        <line-title title-name="检测结果统计">
          <template slot="content">
            <slot name="content">
              <tagView class="tagView fr" ref="tagView" :list="['图片模式']" @tagChange="tagChange" />
            </slot>
          </template>
        </line-title>
        <ChartsContainer :abnormalCount="abnormalCount" />
        <line-title title-name="检测结果详情">
          <!-- <template slot="content">
            <slot name="content">
              <Button type="primary" class="btn_search" size="small">
                <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
                <span class="inline ml-xs">导出</span>
              </Button>
            </slot>
          </template> -->
        </line-title>
        <!-- 图片模式------------------>
        <TableCard v-if="modelTag == 0" ref="infoList" :loadData="loadDataList" :listKey="'entities'">
          <!-- 检索 -->
          <div slot="search" class="hearder-title">
            <ui-label class="fl" label="抓拍时间" :width="70">
              <div class="date-picker-box">
                <DatePicker
                  class="w180 mb-md"
                  v-model="searchData.startTime"
                  type="datetime"
                  placeholder="请选择开始时间"
                  :options="startTimeOption"
                  confirm
                ></DatePicker>
                <span class="ml-sm mr-sm">--</span>
                <DatePicker
                  class="w180"
                  v-model="searchData.endTime"
                  type="datetime"
                  placeholder="请选择结束时间"
                  :options="endTimeOption"
                  confirm
                ></DatePicker>
              </div>
            </ui-label>
            <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
              <select-camera @pushCamera="pushCamera" :device-ids="searchData.deviceIds"></select-camera>
            </ui-label>
            <ui-label class="fl ml-lg" label="检测结果" :width="70">
              <Select class="w180" v-model="searchData.outcome" :clearable="true" placeholder="请选择检测结果">
                <Option v-for="(item, index) in cardSearchList" :value="item.dataKey" :key="index">{{
                  item.dataValue
                }}</Option>
              </Select>
            </ui-label>
            <ui-label :width="30" class="fl" label=" ">
              <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
              <Button type="default" class="mr-lg" @click="resetSearch(searchData, startSearch)">重置</Button>
            </ui-label>
          </div>
          <template #card="{ row }">
            <InfoCard
              class="card"
              :list="{
                ...row,
                scenePath: row.imageUrl,
                facePath: row.imageUrl,
              }"
              :cardInfo="cardInfo"
              @bigImageUrl="bigImageUrl"
            >
            </InfoCard>
          </template>
        </TableCard>
      </div>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  @{_deep} .el-tree {
    max-height: 594px;
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        .text_left {
          margin-top: 10px;
        }
      }
      .btn_search {
        float: right;
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }

    .card {
      width: calc(calc(100% - 70px) / 7);
      margin: 0 10px 10px 0;
    }
    .hearder-title {
      color: #fff;
      margin-top: 10px;
      font-size: 14px;
      .date-picker-box {
        display: flex;
      }
      .w180 {
        width: 180px;
      }
    }
    .ui-images {
      width: 56px;
      height: 56px;
      margin-bottom: 9px;
      .ui-image {
        min-height: 56px !important;
        /deep/ .ivu-spin-text {
          img {
            width: 56px;
            height: 56px;
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      searchData: {
        deviceIds: [],
      },
      styles: {
        width: '8.2rem',
      },
      visible: true,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},
      modelTag: 0, // 图片模式
      bigPictureShow: false,
      loadDataList: (parameter) => {
        return this.$http
          .post(
            governanceevaluation.getVehiclePageList,
            Object.assign(
              parameter,
              {
                resultId: this.$parent.row.resultId,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      cardInfo: [
        { name: '抓拍：', value: 'shotTime' },
        { name: '接收：', value: 'firstIntoViewTime' },
        {
          type: 'fieldQualified',
          value: 'outcome',
          color: 'red',
          num_field: 'reason',
        },
      ],
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      cardSearchList: [
        { dataKey: '1', dataValue: '字段合规' },
        { dataKey: '2', dataValue: '字段不合规' },
      ],
      abnormalCount: [
        {
          title: '检测设备数量',
          icon: 'icon-jianceshebeishuliang',
          key: 'evaluationDeviceNum',
        },
        {
          title: '单设备检测图像数量',
          icon: 'icon-danshebeijiancetuxiangshuliang',
          key: 'perPumpQuantity',
        },
        {
          title: '检测图像总数量',
          icon: 'icon-ziduanwanzhengtupianshuliang',
          key: 'dataTotalNum',
        },
        {
          title: '字段合规图片数量',
          icon: 'icon-shijijiancetupianzongliang',
          key: 'passDataNum',
        },
        {
          title: '字段不合规图片数量',
          icon: 'icon-ziduanqueshitupianshuliang',
          key: 'notPassDataNum',
        },
        {
          title: '过车数据上传合规率',
          icon: 'icon-guocheshujushangchuanheguishuai',
          key: 'compliancerate',
          qualified: false,
        },
      ],
    };
  },
  async mounted() {
    await this.init();
  },

  methods: {
    tagChange(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    async init() {
      this.getResultStatistics();
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },

    startSearch() {
      let params = JSON.parse(JSON.stringify(this.searchData));
      if (params.startTime) params.startTime = this.$util.common.formatDate(this.searchData.startTime);
      if (params.endTime) params.endTime = this.$util.common.formatDate(this.searchData.endTime);
      this.searchData = { ...params };
      this.$refs.infoList.info(true);
    },
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    resetSearch() {
      this.$nextTick(() => {
        this.searchData = {
          deviceIds: [],
          startTime: '',
          endTime: '',
          outcome: '',
        };
        this.$refs.infoList.info(true);
      });
    },
    getResultStatistics() {
      this.$http
        .post(governanceevaluation.getFaceResultStatistics, {
          resultIndexId: this.$parent.row.id,
        })
        .then((res) => {
          const { data } = res.data;
          this.trackList = res.data.data;
          const { standardsValue } = this.$parent.row;
          let statisticsMap = {};
          JSON.parse(data.indexJson).map((e) => {
            statisticsMap[e.key] = e.desc;
          });
          const { passDataNum, dataTotalNum } = statisticsMap;
          statisticsMap.compliancerate = `${((passDataNum / dataTotalNum) * 100).toFixed(2)}%`;
          this.abnormalCount = this.abnormalCount.map((e) => {
            e.count = statisticsMap[e.key];
            if (e.key == 'compliancerate' && standardsValue / 100 < passDataNum / dataTotalNum) {
              e.qualified = true;
            }
            return { ...e };
          });
        });
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },

  components: {
    lineTitle: require('@/components/line-title').default,
    InfoCard: require('./face/uploadIntegrityComponent/infoCard.vue').default,
    tagView: require('./emphasis/components/tags.vue').default,
    LookScene: require('@/components/look-scene').default,
    TableCard: require('@/views/appraisaltask/inspectionrecord/components/tableCard.vue').default,
    SelectCamera: require('@/components/select-camera.vue').default,
    ChartsContainer: require('@/views/governanceevaluation/inspectionrecord/components/chartsContainer.vue').default,
  },
};
</script>
