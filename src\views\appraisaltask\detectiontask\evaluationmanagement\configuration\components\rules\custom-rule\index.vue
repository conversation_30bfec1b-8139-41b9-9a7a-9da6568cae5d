<template>
  <div class="modal-wrapper">
    <ui-modal v-model="visible" title="自定义规则" width="30%" @query="handleSave">
      <div class="btn-box">
        <Button type="primary" @click="addRule" class="mb-sm">
          <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"> </i
          ><span class="vt-middle">新增规则</span></Button
        >
      </div>
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="formValidate.ruleList"
        :loading="loading"
      >
        <template #action="{ row, index }">
          <div>
            <ui-btn-tip icon="icon-bianji2 mr-md" content="编辑" @click.native="handleEdit(row, index)"></ui-btn-tip>
            <ui-btn-tip icon="icon-shanchu3" content="删除" @click.native="handleDelete(row, index)"></ui-btn-tip>
          </div>
        </template>
      </ui-table>
    </ui-modal>
    <edit-custom-rule ref="editCustomRule" @save="handleSaveRule"></edit-custom-rule>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {},
  data() {
    return {
      tableData: [],
      visible: false,
      loading: false,
      longitudeLatitudeOffset: 2000,
      indexConfig: {},
      formValidate: {
        ruleBatchId: '',
        ruleList: [],
      },
      ruleValidate: {
        criticalValue: [{ required: true, message: '允许的误差不能为空', type: 'number', trigger: 'blur' }],
        type: [{ required: true, message: '不能为空', type: 'number', trigger: 'change' }],
      },
      tableColumns: [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        { title: '规则名称', key: 'ruleName', ellipsis: true, tooltip: true },
        {
          title: '操作',
          width: 80,
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
    };
  },
  created() {},
  methods: {
    addRule() {
      this.$refs.editCustomRule.init(
        {
          'ruleName': '',
          'javaCode': '',
        },
        null,
      );
    },
    handleEdit(row, index) {
      this.$refs.editCustomRule.init(row, index);
    },
    handleDelete(row, index) {
      this.$UiConfirm({
        content: `您确认要删除此规则吗?`,
        title: '警告',
      })
        .then(() => {
          this.formValidate.ruleList.splice(index, 1);
        })
        .catch(() => {});
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getCustomRule();
    },
    async getCustomRule() {
      try {
        this.loading = true;
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        let extraParam = JSON.parse(data.extraParam || '{}');
        this.formValidate = { ...this.formValidate, ...extraParam };
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify({ ...this.formValidate, ruleBatchId: Date.now() }),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
    handleSaveRule(formValidate, index) {
      if (index === null) {
        this.formValidate.ruleList.push(formValidate);
      } else {
        this.formValidate.ruleList.splice(index, 1, formValidate);
      }
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    EditCustomRule: require('./edit-custom-rule').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  margin-top: 0 !important;
  padding: 20px 50px !important;
}
.ui-table {
  height: 400px;
}
.btn-box{
  display: flex;
  justify-content: flex-end;
}
</style>
