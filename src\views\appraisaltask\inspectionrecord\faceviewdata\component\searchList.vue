<template>
  <div class="base-search">
    <div class="inline search-wrapper">
      <ui-label class="inline mr-lg mb-sm" label="组织机构" :width="70">
        <tree-select
          :tree-data="treeData"
          nodeKey="orgCode"
          class="width-md"
          v-model="searchData.orgCode"
          @current-change="currentChange"
          placeholder="请选择组织机构"
        >
        </tree-select>
      </ui-label>
      <ui-label class="inline mr-lg" label="关键词" :width="55">
        <Input v-model="searchData.keyWord" class="width-lg" placeholder="请输入设备名称/设备编码"></Input>
      </ui-label>
      <ui-label class="inline mr-lg" :label="global.filedEnum.sbdwlx" :width="70">
        <Select
          class="width-sm"
          v-model="searchData.sbdwlx"
          :clearable="true"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in propertySearchLbdwlx"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg" label="" :width="55" v-if="[402, 403, 404, 405, 406].includes(currentTree.id)">
        <Select v-model="searchData.column" style="width: 100px; margin-right: 15px" :clearable="true">
          <Option v-for="(sbdwlxItem, bdindex) in searchList" :key="'sbdwlx' + bdindex" :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
        <InputNumber
          class="input-number-list"
          v-model="searchData.miniCount"
          controls-outside
          :min="1"
          @on-blur="blurLimit(searchData.miniCount)"
          placeholder="请填写数量"
        ></InputNumber>
        <span class="ml-sm mr-sm">--</span>
        <InputNumber
          class="input-number-list"
          v-model="searchData.maxCount"
          :min="1"
          @on-blur="blurLimit(searchData.maxCount)"
          controls-outside
          placeholder="请填写数量"
        ></InputNumber>
      </ui-label>
      <ui-label class="inline mr-lg" label="设备类型" :width="70" v-if="[302, 303].includes(currentTree.id)">
        <Select class="width-sm" v-model="searchData.isImportantDevice" :clearable="true" placeholder="请选择设备类型">
          <Option value="2">重点设备 </Option>
          <Option value="1">普通设备 </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg" :label="lable" :width="width">
        <InputNumber
          class="input-number-list"
          v-model="searchData.minErrorCount"
          controls-outside
          :min="1"
          @on-blur="blurLimit(searchData.minErrorCount)"
          placeholder="请填写数量"
        ></InputNumber>
        <span class="ml-sm mr-sm">--</span>
        <InputNumber
          class="input-number-list"
          v-model="searchData.maxErrorCount"
          :min="1"
          @on-blur="blurLimit(searchData.maxErrorCount)"
          controls-outside
          placeholder="请填写数量"
        ></InputNumber>
      </ui-label>
    </div>
    <ui-label :width="0" label="" class="inline mb-sm search-button">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
import user from '@/config/api/user';
import { mapActions, mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    lable: {
      type: String,
      default: '时钟错误图片数量',
    },
    width: {
      type: Number,
      default: 155,
    },
    searchList: {
      type: Array,
      default() {
        return [];
      },
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        keyWord: '',
        sbdwlx: '',
        isImportantDevice: '',
        minErrorCount: null,
        maxErrorCount: null,
        orgCode: '',
        // resultId: '',
      },
      cardSearchList: [],
      dictData: {},
      treeData: [],
    };
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData();
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    currentChange(data) {
      this.searchData.orgCode = data.orgCode;
      // this.searchData.resultId = resultId
      this.$emit('params-change', this.searchData);
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx1(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        keyWord: '',
        sbdwlx: '',
        isImportantDevice: '',
        minErrorCount: null,
        maxErrorCount: null,
        orgCode: '',
        // resultId: '',
      };
      this.$emit('startSearch', this.searchData);
    },
    blurLimit() {
      if (this.searchData.miniCount) {
        this.searchData.miniCount = Math.round(Number(this.searchData.miniCount));
      }
      if (this.searchData.maxCount) {
        this.searchData.maxCount = Math.round(Number(this.searchData.maxCount));
      }
      this.$forceUpdate();
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getOrgTreeList(val) {
      if (!!val && val.regionCode) {
        await this.getRegioncode(val.regionCode);
      }
      // if (val.orgCodeList && val.orgCodeList.length) {
      //   let {orgCode, resultId} = val.orgCodeList.find(item=> !item.disabled) || {}
      //   this.searchData.orgCode = orgCode || ''
      //   this.searchData.resultId = resultId || ''
      //   this.treeData = this.$util.common.arrayToJson(
      //     JSON.parse(JSON.stringify(val.orgCodeList)),
      //     'id',
      //     'parentId'
      //   )
      // }
    },
    // 获取组织机构数据
    async getRegioncode(code) {
      try {
        const params = { regioncode: code };
        const res = await this.$http.get(governanceevaluation.getOrgDataByRegioncode, { params });
        const orgCodeList = res.data.data;
        let { orgCode } = this.$util.common
          .jsonToArray(JSON.parse(JSON.stringify(orgCodeList)))
          .find((item) => !item.disabled);
        this.searchData.orgCode = orgCode || '';
        this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(orgCodeList)), 'id', 'parentId');
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: async function (val) {
        this.copySearchDataMx(this.searchData);
        await this.getOrgTreeList(val);
        this.startSearch();
      },
    },
  },
  components: {
    TreeSelect: require('./tree-select').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .search-button {
    white-space: nowrap;
  }
}
.input-number-list {
  padding: 0px;
  width: 88px;
  /deep/ .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
</style>
