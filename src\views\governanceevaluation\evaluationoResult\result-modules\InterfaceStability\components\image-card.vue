<template>
  <div class="image-card-container mr-sm mb-sm" :class="active ? 'active' : ''">
    <ui-image :src="src" class="ui-image-card" @click.native="clickImage" />
    <slot></slot>
    <div class="percentage" v-show="percentage">
      <slot name="percentage">100%</slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'image-card',
  components: {},
  props: {
    data: {
      default: () => [],
    },
    percentage: {
      default: false,
    },
    src: {},
    isClick: {
      type: Boolean,
      default: false,
    },
    active: {
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    clickImage() {
      if (this.isClick) {
        this.$emit('click');
      }
    },
  },
};
</script>

<style lang="less" scoped>
.image-card-container {
  position: relative;
  background: var(--bg-info-card);
  border: 1px solid var(--border-info-card);
  padding: 15px;
  z-index: 10;
  width: 195px;
  height: fit-content;
  .ui-image-card {
    width: 167px;
    height: 167px;
    cursor: pointer;
  }
  .percentage {
    position: absolute;
    left: 15px;
    top: 15px;
    width: 32px;
    height: 18px;
    background: #ea800f;
    z-index: 10;
    font-size: 12px;
  }
}
.active {
  position: relative;
  box-shadow: 0 3px 6px 1px rgba(43, 132, 226, 1);
  border: 2px solid var(--color-primary);
}
</style>
