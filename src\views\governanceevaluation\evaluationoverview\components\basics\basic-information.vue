<template>
  <!-- 填报准确率 -->
  <div class="basic-information auto-fill">
    <div class="basic-content">
      <div class="informat-content" :style="tableData.length != 0 ? styles1 : ''">
        <div class="information-header">
          <statistics
            class="statistics"
            :statistics-list="statisticsList"
            :isflexfix="false"
          ></statistics>
          <div class="information-echart">
            <div
              class="echart-box"
              v-ui-loading="{ loading: echartLoading, tableData: echartData }"
            >
              <draw-echarts
                v-if="echartData.length != 0"
                :echart-option="determinantEchart"
                :echart-style="ringStyle"
                ref="attributeChart"
                class="charts"
              ></draw-echarts>
              <span class="next-echart" v-if="echartData.length > 10">
                <i
                  class="icon-font icon-youjiantou1 f-12"
                  @click="scrollRight('attributeChart', echartData, [], 10)"
                ></i>
              </span>
            </div>
          </div>
          <div class="information-ranking">
            <div class="rank" v-if="rankData.length != 0">
              <div class="ranking-title">
                <title-content title="下级排行"></title-content>
              </div>
              <div class="ranking-list">
                <ul>
                  <li v-for="(item, index) in rankData" :key="index">
                    <div class="content-firstly">
                      <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                      <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                      <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                      <span
                        class="bg_color firstly4"
                        v-if="item.rank != 1 && item.rank != 2 && item.rank != 3"
                        >{{ item.rank }}</span
                      >
                    </div>
                    <Tooltip class="content-second" transfer :content="item.regionName">
                      <div>
                        <img
                          class=" "
                          v-if="item.rank == 1"
                          src="@/assets/img/crown_1.png"
                          alt=""
                        />
                        <img
                          class=" "
                          v-if="item.rank == 2"
                          src="@/assets/img/crown_2.png"
                          alt=""
                        />
                        <img
                          class=" "
                          v-if="item.rank == 3"
                          src="@/assets/img/crown_3.png"
                          alt=""
                        />
                        <!-- <span>{{ item.regionName }}</span> -->
                        <span v-if="item.rank == 1">{{ item.regionName }}</span>
                        <span v-if="item.rank == 2">{{ item.regionName }}</span>
                        <span v-if="item.rank == 3">{{ item.regionName }}</span>
                        <span
                          class="rankText"
                          v-if="item.rank != 3 && item.rank != 2 && item.rank != 1"
                          >{{ item.regionName }}</span
                        >
                      </div>
                    </Tooltip>
                    <div class="content-thirdly">
                      <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                    </div>

                    <div class="content-fourthly">
                      <i
                        class="icon-font icon-shangsheng color-sheng f-16"
                        v-if="item.rankType === 'RISE'"
                      ></i>
                      <i
                        class="icon-font icon-shangsheng color-sheng f-16"
                        v-if="item.rankType === 'SAME'"
                      ></i>
                      <i
                        class="icon-font icon-xiajiang f-16 color-jiang"
                        v-if="item.rankType === 'DOWN'"
                      ></i>
                      <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{
                        item.rankRise || 0
                      }}</span>
                      <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                        item.rankRise || 0
                      }}</span>
                      <span class="plus color-sheng ml-md" v-else>{{
                        item.rankRise == null ? 0 : item.rankRise
                      }}</span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div class="no-data" v-if="rankData.length == 0">
              <img class="no-data-img" src="@/assets/img/common/nodata.png" />
              <div class="null-data-text">暂无数据</div>
            </div>
          </div>
        </div>
        <div class="information-main">
          <div class="abnormal-title">
            <div class="fl">
              <i class="icon-font icon-yichangshujuliebiao f-16 color-filter"></i>
              <span class="f-16 color-filter ml-sm">异常数据列表</span>
            </div>
            <div class="export fr">
              <Button type="primary" class="btn_search" @click="onClickIndex">
                <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
                <span class="inline ml-xs">导出</span>
              </Button>
            </div>
          </div>

          <ui-select-tabs class="tabs-ui" @selectInfo="selectInfo" :list="selectTabs">
          </ui-select-tabs>
          <div class="list">
            <ui-table
              class="ui-table"
              :table-columns="tableColumns"
              :table-data="tableData"
              :loading="loading"
              :maxHeight="tableData.length > 6 ? handleHeight : handleHeight2"
            >
              <template #tagNames="{ row }">
                <tags-more :tag-list="row.tagList || []"></tags-more>
              </template>
              <template #deviceId="{ row }">
                <span
                  class="font-active-color pointer device-id"
                  :class="row.rowClass"
                  @click="deviceArchives(row)"
                  >{{ row.deviceId }}</span
                >
              </template>
              <template #longitude="{ row }">
                <span>{{ row.longitude | filterLngLat }}</span>
              </template>
              <template #latitude="{ row }">
                <span>{{ row.latitude | filterLngLat }}</span>
              </template>
              <template #option="{ row }">
                <ui-btn-tip
                  icon="icon-chakanyichangxiangqing"
                  class="mr-sm"
                  content="不合格原因"
                  @click.native="checkReason(row)"
                ></ui-btn-tip>
                <ui-btn-tip
                  class="mr-sm"
                  icon="icon-tianjiabiaoqian"
                  content="添加标签"
                  @click.native="addTags(row)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
        </div>
        <nonconformance
          ref="nonconformance"
          title="检测不合格原因"
          :tableColumns="reasonTableColumns"
          :tableData="reasonTableData"
          :reasonLoading="reasonLoading"
        ></nonconformance>
        <export-data
          ref="exportModule"
          @exportAdd="exportAdd"
          :exportLoading="exportLoading"
        ></export-data>
      </div>
      <ui-page
        :pageShow="false"
        class="page"
        :page-data="searchData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
    </div>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less" scoped>
.basic-information {
  @media screen and (max-width: 1366px) {
    .basic-content {
      height: 640px !important; /*no*/
      position: relative;
    }

    .informat-content {
      height: 92% !important;
    }
    @{_deep}.ui-page {
      position: absolute;
    }
    @{_deep}.ivu-table-body {
      height: 100% !important; /*no*/
    }
    .list {
      margin-top: 10px;
      // height: 100%;
      @{_deep}.ivu-table-tip {
        height: 290px !important; /*no*/
      }
      .ui-table {
        height: 500px !important; /*no*/
      }
    }
  }
  .basic-content {
    height: 100%;
    position: relative;
    .informat-content {
      &::-webkit-scrollbar {
        display: none; /* Chrome Safari */
      }
      .information-header {
        margin-top: 10px;
        height: 252px;
        display: flex;
        .information-statistics {
          display: flex;
          width: 755px;
          height: 100%;
          background: #08264d;
          margin-right: 10px;
          padding: 20px 5px 0 15px;
          @{_deep}.statistics-ul {
            li {
              &:nth-child(3) {
                margin-right: 0px !important;
              }
            }
          }
        }
        .information-echart {
          display: inline-block;
          width: 650px;
          height: 100%;
          background: #08264d;
          margin-right: 10px;
          .echart-box {
            width: 100%;
            height: 100%;
            position: relative;
          }
          .echarts-box {
            width: 100%;
            height: 100% !important;
            .charts {
              width: 100%;
              height: 100% !important;
            }
          }
          .next-echart {
            width: 22px;

            height: 22px;
            background: #02162b;
            border: 1px solid #10457e;
            opacity: 1;
            border-radius: 4px;
            top: 50%;
            right: 8px;
            position: absolute;
            text-align: center;
            line-height: 22px;
            transform: translate(0, -50%);
            .icon-youjiantou1 {
              color: #2b84e2;
              font-size: 12px;
              vertical-align: top !important;
            }
            &:active {
              width: 22px;
              height: 22px;
              background: #02162b;
              border: 1px solid #2b84e2;
              opacity: 1;
              border-radius: 4px;
              .icon-youjiantou1 {
                color: #4e9ef2;
                font-size: 12px;
                vertical-align: top !important;
              }
            }
            &:hover {
              width: 22px;
              height: 22px;
              background: #02162b;
              border: 1px solid #146ac7;
              opacity: 1;
              border-radius: 4px;
              .icon-youjiantou1 {
                color: #2b84e2;
                font-size: 12px;
                vertical-align: top !important;
              }
            }
          }
        }
        .information-ranking {
          width: 364px;
          background: #08264d;
          height: 100%;
          padding: 10px;
          position: relative;
          .rank {
            width: 100%;
            height: 100%;
          }
          .ranking-title {
            height: 30px;
            text-align: center;
          }
          .ranking-list {
            width: 100%;
            height: calc(100% - 30px);
            overflow-y: auto;
            display: block !important;
            ul {
              width: 100%;
              height: 100%;
              li {
                display: flex;
                padding-top: 15px;
                align-items: center;
                .content-fourthly {
                  display: inline-flex;
                  font-size: 14px;
                  position: relative;
                }
                div {
                  display: flex;

                  align-items: center;
                  font-size: 14px;
                  position: relative;
                }

                .content-firstly {
                  // width: 60px;
                  flex: 1;
                  margin-left: 10px;
                }
                .content-thirdly {
                  justify-content: center;
                  flex: 1;
                }
                .content-fourthly {
                  justify-content: center;
                  // width: 90px;
                  flex: 1;
                }
                .content-second {
                  color: #fff;
                  justify-content: center;
                  // width: 150px;
                  flex: 1;

                  img {
                    vertical-align: middle;
                  }

                  span {
                    // width: calc(100% - 80px);
                    width: 75px;
                    padding-left: 10px;
                    display: inline-block;
                    // text-align: center;
                    vertical-align: middle;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                  .rankText {
                    margin-left: 20px;
                  }
                }

                .bg_color {
                  min-width: 21px;
                  min-height: 21px;
                  text-align: center;
                  font-weight: bold;
                  color: #fff;
                  font-size: 14px;
                }
                .firstly1 {
                  background-color: #f1b700;
                }
                .firstly2 {
                  background-color: #eb981b;
                }
                .firstly3 {
                  background-color: #ae5b0a;
                }
                .firstly4 {
                  background-color: #2b84e2;
                }

                .thirdly {
                  overflow: hidden;
                  color: #2b84e2;
                }
                .color-sheng {
                  color: #0e8f0e;
                  font-size: 14px;
                }
                .color-jiang {
                  color: #bc3c19;
                  font-size: 14px;
                }
              }
            }
          }
        }
      }
      .information-main {
        height: 100%;
        .abnormal-title {
          height: 48px;
          line-height: 48px;
          .color-filter {
            color: rgba(43, 132, 226, 1);
            vertical-align: middle;
          }
        }
        .list {
          margin-top: 10px;
          // height: 100%;
          @{_deep}.ivu-table-tip {
            height: 200px;
          }
          .ui-table {
            height: 620px;
          }
        }
      }

      @{_deep} .ivu-dropdown-item {
        background: rgba(5, 30, 67, 1);
        color: rgba(255, 255, 255, 1);
        &:hover {
          background: rgba(2, 57, 96, 1);
        }
      }
      @{_deep} .ivu-dropdown-item-selected {
        background: #073167;
      }
      .export {
        @{_deep}.ivu-select-dropdown {
          position: absolute !important;
          left: 1647px !important;
          top: 364px !important;
        }
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview'
import { mapActions, mapGetters } from 'vuex'
import dataZoom from '@/mixins/data-zoom'
import downLoadTips from '@/mixins/download-tips'
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js'
import taganalysis from '@/config/api/taganalysis'
export default {
  name: 'basic-information',
  mixins: [dataZoom, downLoadTips],
  data() {
    return {
      handleHeight: 620,
      handleHeight2: 800,
      styles1: { height: '92%', overflow: 'auto', paddingRight: '3px' },
      styles2: { height: '92%', overflow: 'hidden', paddingRight: '3px' },
      ringStyle: {
        width: '96%',
        height: '250px',
      },
      determinantEchart: {},
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticalList: {},
      statisticsList: [
        {
          name: '应检测设备数量',
          value: 0,
          icon: 'icon-yingjianceshebeishuliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'baDetectionShouldCount',
        },
        {
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'baDetectionActualCount',
        },
        {
          name: '检测合格设备数',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
          key: 'baDetectionQualifiedCount',
        },
        {
          name: '检测不合格设备数',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'baDetectionUnQualifiedCount',
        },
        {
          name: '填报准确率',
          value: 0,
          icon: 'icon-tianbaozhunqueshuai',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage',
          textColor: 'color5',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          width: 150,
        },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 120 },
        {
          title: '数据来源',
          key: 'sourceIdText',
          minWidth: 150,
          tooltip: true,
        },
        { title: '安装地址', key: 'address', minWidth: 150, tooltip: true },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        pageAction: 1,
        firstId: null,
        lastId: null,
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      // reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      selectTabs: [],
      rankData: [],
      paramsList: {},
      errorMessages: [],
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],
      exportName: '',
      exportShow: false,
      copyPage: 0,
      echartLoading: false,
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    }
  },

  mounted() {
    this.getTagList()
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row
      this.customSearch = true
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      }
      let tagList = row.tagList || []
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId
      })
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId
      })
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      }
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params)
        this.$Message.success(data.msg)
        this.customSearch = false
        this.getTableData()
      } catch (err) {
        console.log(err)
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      })
      window.open(routeData.href, '_blank')
    },
    onClickIndex() {
      this.$refs.exportModule.init(this.paramsList.batchId)
    },
    exportAdd(val) {
      this.getExport(val)
    },

    async getExport(val) {
      // let data = []
      // val.list.map((item) => {
      //   data.push(item.orgCode)
      // })

      this.exportLoading = true
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          multiSheet: JSON.parse(val.multiSheet),
          orgCodes: val.orgCodes,
          exportZip: true,
          customParameters: {
            errorMessages: this.errorMessages,
          },
        }
        this.$_openDownloadTip()
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(this, evaluationoverview.exportDeviceDetailData,params, 'post',this.$route.query.cascadeId, {responseType: '',})
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data)
        this.$refs.exportModule.hide()
      } catch (err) {
        console.log(err)
      } finally {
        this.exportLoading = false
      }
    },

    // 柱状图统计
    async getGraphsInfo() {
      this.echartLoading = true
      let data = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
      }
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(this, evaluationoverview.getGraphsInfo,data, 'post',this.$route.query.cascadeId, {})
        // let res = await this.$http.post(evaluationoverview.getGraphsInfo, data)
        this.echartData = res.data.data || []
        this.initRing()
        this.echartLoading = false
      } catch (err) {
        console.log(err)
        this.echartLoading = false
      }
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true
        this.tableData = []
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            errorMessages: this.errorMessages,
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          pageAction: this.searchData.pageAction,
          firstId: this.searchData.firstId,
          lastId: this.searchData.lastId,
        }
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(this, evaluationoverview.getDetailData,params, 'post',this.$route.query.cascadeId, {})
        // let res = await this.$http.post(evaluationoverview.getDetailData, params)

        const datas = res.data.data
        this.tableData = datas.entities || []
        if (this.tableData.length != 0) {
          this.searchData.firstId = this.tableData[0].id
          this.searchData.lastId = this.tableData[this.tableData.length - 1].id
        }
        this.searchData.totalCount = datas.total
        this.loading = false
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        }
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(this, evaluationoverview.getAbnormalLabel,params, 'post',this.$route.query.cascadeId, {})
        // let res = await this.$http.post(evaluationoverview.getAbnormalLabel, params)
        this.selectTabs =
          res.data.data.map((item) => {
            let obj = {}
            obj.name = item
            return obj
          }) || []
      } catch (error) {
        console.log(error)
      }
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false
        }
      })
    },
    changePage(val) {
      if (this.copyPage > val) {
        this.searchData.pageAction = 0
      } else {
        this.searchData.pageAction = 1
      }
      this.searchData.pageNum = val
      this.copyPage = this.searchData.pageNum
      this.getTableData()
    },
    changePageSize(val) {
      this.searchData.pageSize = val
      this.getTableData()
    },
    // 不合格原因
    checkReason(row) {
      this.deviceInfoId = row.deviceInfoId
      this.getReason()
      this.$refs.nonconformance.init()
    },
    async getReason() {
      this.reasonLoading = true
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: { deviceInfoId: this.deviceInfoId },
      }
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(this, evaluationoverview.getSecondaryPopUpData,params, 'post',this.$route.query.cascadeId, {})
        // let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params)
        const datas = res.data.data
        this.reasonTableData = datas || []
        // this.reasonPage.totalCount = datas.total
        this.reasonLoading = false
      } catch (error) {
        console.log(error)
      }
    },

    selectInfo(infoList) {
      this.searchData.pageNumber = 1
      this.errorMessages = infoList.map((item) => {
        return item.name
      })
      this.searchData.pageNum = 1
      this.searchData.pageSize = 20
      this.searchData.firstId = null
      this.searchData.lastId = null
      this.searchData.pageAction = 1
      this.getTableData()
    },
    initRing() {
      this.barData = this.echartData.map((row, index) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion || '0%',
        }
      })
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      }
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts)
      if (this.echartData.length != 0) {
        setTimeout(() => {
          this.setDataZoom('attributeChart', [], 10)
        })
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val
          this.statistical()
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val
        } else {
          this.rankData = []
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val
          // 2017, 3021, 4019
          if (this.paramsList.indexId === 2017) {
            this.statisticsList[4].name = '人脸卡口填报准确率'
          } else if (this.paramsList.indexId === 3021) {
            this.statisticsList[4].name = '车辆卡口填报准确率'
          } else if (this.paramsList.indexId === 4019) {
            this.statisticsList[4].name = '视频监控填报准确率'
          } else {
            this.statisticsList[4].name = '填报准确率'
          }
          this.searchData.pageNum = 1
          this.searchData.pageSize = 20
          this.searchData.firstId = null
          this.searchData.lastId = null
          this.searchData.pageAction = 1
          this.getTableData() //表格
          this.getSelectTabs()
          this.getGraphsInfo() //柱状图
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
      systemConfig: 'common/getSystemConfig',
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  components: {
    exportData: require('./export-data.vue').default,
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue')
      .default,
    lineTitle: require('@/views/governanceevaluation/evaluationoverview/components/basics/line-title.vue')
      .default,
    // selectTabs: require('@/views/governanceevaluation/evaluationoverview/components/basics/select-tabs.vue')
    //   .default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    nonconformance: require('./nonconformance.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
}
</script>
