<template>
  <ui-modal v-model="visible" :styles="styles" title="评测结果" footerHide>
    <div class="result-container auto-fill">
      <dynamic-condition
        :form-item-data="formItemData"
        :form-data="formData"
        @search="handleStartSearch"
        @reset="handleStartSearch"
      >
        <template #otherButton>
          <div class="other-button ml-lg inline">
            <Button type="primary" :loading="exportLoading" @click="onExport">
              <i class="icon-font icon-daochu"></i> 导出
            </Button>
          </div>
        </template>
      </dynamic-condition>

      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
      >
      </ui-table>
      <ui-page
        class="page menu-content-background"
        transfer
        :page-data="pageData"
        @changePage="handlePage"
        @changePageSize="handlePageSize"
      >
      </ui-page>
    </div>
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </ui-modal>
</template>
<script>
import { IS_IMPORTANT } from '@/views/disposalfeedback/cascadelist/util/enum.js';
import cascadeListMixin from '@/views/disposalfeedback/cascadelist/mixins/cascadeListMixin.js';
import {
  tableColumns,
  normalFormData,
} from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoPlayingAccuracy/util/enum/ReviewParticular.js';
export default {
  name: 'device-result-compare',
  components: {
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    UiTable: require('@/components/ui-table.vue').default,
    exportData: require('@/views/governanceevaluation/evaluationoResult/result-modules/components/export-data.vue')
      .default,
  },
  mixins: [cascadeListMixin],
  props: {
    value: {},
    activeItem: {},
  },
  data() {
    return {
      visible: false,
      detail: {},
      styles: {
        width: '8.5rem',
      },
      tableLoading: false,
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      tableColumns: [],
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
        online: '',
        normal: '',
        canPlay: '',
      },
      formItemData: normalFormData,
      exportLoading: false,
    };
  },
  computed: {},
  watch: {
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        try {
          this.initList();
          this.getQualificationList();
          this.getStatInfo();
        } catch (e) {
          console.log(e);
        }
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  created() {
    this.visible = true;
  },
  methods: {
    handleExport(val) {
      let { id, batchId, indexId } = this.activeItem;
      let params = {
        statisticType: 'ORG',
        cascadeId: id,
        customParameters: this.formData,
        indexId,
        batchId,
        ...val,
      };
      this.mixinGetExport(params);
    },
    onExport() {
      this.$refs.exportModule.init(this.activeItem.batchId);
    },
    async getStatInfo() {
      let isImportant = this.activeItem.indexType === 'VIDEO_PLAYING_ACCURACY' ? IS_IMPORTANT : '';
      this.statisticalList = await this.mixinGetStatInfo({
        statisticType: 'ORG',
        isImportant,
        cascadeId: this.activeItem.id,
        ...this.activeItem,
      });
      this.tableColumns = tableColumns({
        statisticalList: this.statisticalList,
      }).filter((item) => item.slot !== 'option'); //不需要操作列 这里过滤掉
    },
    async getQualificationList() {
      let res = await this.mixinGetQualificationList({
        statisticType: 'ORG',
        cascadeId: this.activeItem.id,
        ...this.activeItem,
      });
      let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
      findErrorCodes.options = res;
    },
    handleStartSearch() {
      this.pageData.pageNum = 1;
      this.initList();
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    async initList() {
      try {
        let params = {
          ...this.activeItem,
          ...this.pageData,
          statisticType: 'ORG',
          cascadeId: this.activeItem.id,
          customParameters: this.formData,
        };
        let { data } = await this.mixinGetFirstModeData(params);
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style scoped lang="less">
.result-container {
  position: relative;
  height: 700px;
}
</style>
