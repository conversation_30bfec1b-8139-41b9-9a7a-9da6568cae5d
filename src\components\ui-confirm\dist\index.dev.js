"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _index = _interopRequireDefault(require("./index.vue"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

/**
 *
 * @description 代替 this.$Modal.confirm(config)
 * @example
 * this.$UiConfirm({content: "", title: ""}).then(res=>{
        console.log(res);
    }).catch(res=>{
        console.log(res);
    })
 * */
var confirm = {};
var $vm = null;

confirm.install = function (Vue) {
  var ConfirmConstructor = Vue.extend(_index["default"]);
  $vm = new ConfirmConstructor({
    el: document.createElement('div')
  });

  Vue.prototype.$UiConfirm = function () {
    var _ref = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},
        _ref$content = _ref.content,
        content = _ref$content === void 0 ? '您确定要删除吗?' : _ref$content,
        _ref$title = _ref.title,
        title = _ref$title === void 0 ? '提示' : _ref$title,
        _ref$hasLoading = _ref.hasLoading,
        hasLoading = _ref$hasLoading === void 0 ? false : _ref$hasLoading;

    return new Promise(function (resolve, reject) {
      $vm.visible = true;
      $vm.content = content;
      $vm.title = title;
      document.body.appendChild($vm.$el);

      $vm.callback = function (visible) {
        if (!hasLoading) {
          $vm.visible = false;
          $vm.$el.parentNode.removeChild($vm.$el);
        }

        if (visible) {
          resolve($vm);
        } else {
          reject(visible);
          $vm.loading = false;
        }
      };
    });
  };
};

var _default = confirm;
exports["default"] = _default;