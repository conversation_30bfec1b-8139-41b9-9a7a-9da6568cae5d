<template>
  <div class="echart-wrap">
    <div v-show="title.show" class="echart-text title-color">
      {{ title.text }}
    </div>
    <div v-show="title.show" class="echart-subtext auxiliary-color">
      {{ title.subtext }}
    </div>
    <div ref="echart" class="echart-content"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  props: {
    title: {
      type: Object,
      default: () => {
        return {
          show: true,
          text: "活动次数统计",
          subtext: "单位：次",
        };
      },
    },
    legend: {
      type: Array | Object,
      default: () => {
        return {
          type: "scroll",
          show: true,
          bottom: "2%",
          itemGap: 15,
          itemWidth: 8,
          itemHeight: 8,
          icon: "circle",
          textStyle: {
            color: "rgba(0, 0, 0, 0.35)",
            // lineHeight: 18,
            padding: [0, 0, 0, 3],
          },
        };
      },
    },
    color: {
      type: Array,
      default: () => {
        return [
          "#2C86F8",
          "#F0824C",
          "#F1BA4D",
          "#6760D9",
          "#4DC7D4",
          "#879DC6",
        ];
      },
    },
    grid: {
      type: Object,
      default: () => {
        return {
          left: "0",
          top: "10%",
          right: "0.1%",
          bottom: "0",
          containLabel: true,
        };
      },
    },
    tooltip: {
      type: Object,
      default: () => {
        return {
          show: true,
          borderColor: "rgba(0, 0, 0, 0)",
        };
      },
    },
    graphic: {
      type: Object,
      default: () => {
        return {
          elements: [],
        };
      },
    },
    series: {
      type: Array,
      default: () => {
        return [
          {
            type: "pie",
            radius: ["58%", "84%"],
            label: {
              show: false,
              position: "center",
            },
            labelLine: {
              show: false,
            },
            data: [{ value: 1048, name: "人" }],
          },
        ];
      },
    },
  },
  data() {
    return {
      myEchart: null,
    };
  },
  mounted() {
    this.init();
  },
  deactivated() {
    this.removeResizeFun();
  },
  beforeDestroy() {
    this.removeResizeFun();
  },
  watch: {
    series: {
      deep: true,
      handler(val) {
        console.log("------pie-echart-------", val);
        this.init();
      },
    },
  },
  methods: {
    init() {
      this.myEchart = echarts.init(this.$refs.echart);
      let option = {
        legend: this.legend,
        color: this.color,
        grid: this.grid,
        tooltip: this.tooltip,
        graphic: this.graphic,
        series: this.series,
      };
      this.myEchart.setOption(option, true);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
    removeResizeFun() {
      window.removeEventListener("resize", () => this.myEchart.resize());
    },
  },
};
</script>
<style lang="less" scoped>
.echart-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .echart-text {
    font-size: 14px;
    font-family: "MicrosoftYaHei-Bold";
    font-weight: bold;
    line-height: 20px;
    text-align: center;
  }
  .echart-subtext {
    margin-top: 8px;
    font-size: 12px;
    line-height: 18px;
  }
  .echart-content {
    flex: 1;
  }
}
</style>
