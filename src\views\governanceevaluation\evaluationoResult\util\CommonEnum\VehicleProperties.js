/**
 *
 * @param1 indexId:指标id
 * 3003 车辆卡口设备过车数据准确率、3004 重点车辆卡口设备过车数据准确率
 * 3006 重点车辆卡口设备主要属性准确率
 * 3007 重点车辆卡口设备类型属性识别准确率
 * @param2 configText:检测任务配置的检测属性字段
 * @returns
 */
export const getAlgorithmColumns = ({ indexId = '', configText = '' }) => {
  return (tableData) => {
    let allConfigColumns = [
      {
        title: '车牌号',
        key: 'plateNo',
        align: 'left',
        isShowByIndexId: indexId ? [3003, 3004, 3006].includes(indexId) : true,
        isShowByConfigText: configText ? configText.includes('plateNo') : true,
        render: (h, { index, row }) => {
          if (index === 0) {
            return <span>{row.plateNo}</span>;
          }
          if (index === tableData.length - 1) {
            return (
              <span class={{ green: row.votePlateNo, red: !row.votePlateNo }}>{row.votePlateNo ? '准确' : '存疑'}</span>
            );
          }
          return <span>{row.algorithmPlateNo}</span>;
        },
      },
      {
        title: '车牌颜色',
        key: 'plateColor',
        align: 'left',
        isShowByIndexId: indexId ? [3003, 3004, 3006].includes(indexId) : true,
        isShowByConfigText: configText ? configText.includes('plateColor') : true,
        render: (h, { index, row }) => {
          if (index === 0) {
            return <span>{row.plateColor}</span>;
          }
          if (index === tableData.length - 1) {
            return (
              <span
                class={{
                  green: row.votePlateColor,
                  red: !row.votePlateColor,
                }}
              >
                {row.votePlateColor ? '准确' : '存疑'}
              </span>
            );
          }
          return <span>{row.algorithmPlateColor}</span>;
        },
      },
      {
        title: '车辆类型',
        key: 'vehicleClass',
        align: 'left',
        isShowByIndexId: indexId ? [3003, 3004, 3007].includes(indexId) : true,
        isShowByConfigText: configText ? configText.includes('vehicleClass') : true,
        render: (h, { index, row }) => {
          if (index === 0) {
            return <span>{row.vehicleClass}</span>;
          }
          if (index === tableData.length - 1) {
            return (
              <span
                class={{
                  green: row.voteVehicleClass,
                  red: !row.voteVehicleClass,
                }}
              >
                {row.voteVehicleClass ? '准确' : '存疑'}
              </span>
            );
          }
          return <span>{row.algorithmVehicleClass}</span>;
        },
      },
      {
        title: '车辆品牌',
        key: 'vehicleBrand',
        align: 'left',
        isShowByIndexId: indexId ? [3003, 3004, 3007].includes(indexId) : true,
        isShowByConfigText: configText ? configText.includes('vehicleBrand') : true,
        render: (h, { index, row }) => {
          if (index === 0) {
            return <span>{row.vehicleBrand}</span>;
          }
          if (index === tableData.length - 1) {
            return (
              <span
                class={{
                  green: row.voteVehicleBrand,
                  red: !row.voteVehicleBrand,
                }}
              >
                {row.voteVehicleBrand ? '准确' : '存疑'}
              </span>
            );
          }
          return <span>{row.algorithmVehicleBrand}</span>;
        },
      },
      {
        title: '车辆颜色',
        key: 'vehicleColor',
        align: 'left',
        isShowByIndexId: indexId ? [3003, 3004].includes(indexId) : true,
        isShowByConfigText: configText ? configText.includes('vehicleColor') : true,
        render: (h, { index, row }) => {
          if (index === 0) {
            return <span>{row.vehicleColor}</span>;
          }
          if (index === tableData.length - 1) {
            return (
              <span
                class={{
                  green: row.voteVehicleColor,
                  red: !row.voteVehicleColor,
                }}
              >
                {row.voteVehicleColor ? '准确' : '存疑'}
              </span>
            );
          }
          return <span>{row.algorithmVehicleColor}</span>;
        },
      },
    ];
    let filterColumns = allConfigColumns.filter((col) => col.isShowByIndexId && col.isShowByConfigText);
    return [{ title: '算法名称', key: 'algorithmTitle', align: 'left' }, ...filterColumns];
  };
};
