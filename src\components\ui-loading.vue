<template>
  <div :style="{ background: bgColor }" class="cover">
    <!-- <svg class="loading" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="40px" height="40px" viewBox="0 0 50 50" style="enable-background: new 0 0 50 50" xml:space="preserve">
      <path :fill="color" d="M43.935,25.145c0-10.318-8.364-18.683-18.683-18.683c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615c8.072,0,14.615,6.543,14.615,14.615H43.935z" />
    </svg> -->
    <svg
      t="1693535205621"
      class="loading icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="9065"
      width="64"
      height="64"
    >
      <path
        d="M597.466453 76.356267C597.466453 118.510933 563.264853 152.712533 521.110187 152.712533 478.95552 152.712533 444.75392 118.510933 444.75392 76.356267 444.75392 34.2016 478.95552 0 521.110187 0 563.264853 0 597.466453 34.2016 597.466453 76.356267"
        fill="#2C86F8"
        p-id="9066"
      ></path>
      <path
        d="M403.060053 118.227627C403.060053 157.924693 370.87232 190.112427 331.175253 190.112427 291.478187 190.112427 259.324587 157.924693 259.324587 118.227627 259.324587 78.53056 291.478187 46.342827 331.175253 46.342827 370.87232 46.342827 403.060053 78.53056 403.060053 118.227627"
        fill="#2C86F8"
        p-id="9067"
      ></path>
      <path
        d="M248.49408 231.297707C248.49408 268.50304 218.320213 298.676907 181.11488 298.676907 143.875413 298.676907 113.701547 268.50304 113.701547 231.297707 113.701547 194.092373 143.875413 163.918507 181.11488 163.918507 218.320213 163.918507 248.49408 194.092373 248.49408 231.297707"
        fill="#2C86F8"
        p-id="9068"
      ></path>
      <path
        d="M152.579413 396.752213C152.579413 431.499947 124.419413 459.659947 89.67168 459.659947 54.923947 459.659947 26.763947 431.499947 26.763947 396.752213 26.763947 362.00448 54.923947 333.84448 89.67168 333.84448 124.419413 333.84448 152.579413 362.00448 152.579413 396.752213"
        fill="#2C86F8"
        p-id="9069"
      ></path>
      <path
        d="M133.90848 593.435307C133.90848 625.691307 107.762347 651.871573 75.472213 651.871573 43.216213 651.871573 17.07008 625.691307 17.07008 593.435307 17.07008 561.179307 43.216213 534.99904 75.472213 534.99904 107.762347 534.99904 133.90848 561.179307 133.90848 593.435307"
        fill="#2C86F8"
        p-id="9070"
      ></path>
      <path
        d="M197.116587 772.16768C197.116587 801.96608 172.98432 826.098347 143.18592 826.098347 113.38752 826.098347 89.255253 801.96608 89.255253 772.16768 89.255253 742.36928 113.38752 718.237013 143.18592 718.237013 172.98432 718.237013 197.116587 742.36928 197.116587 772.16768"
        fill="#2C86F8"
        p-id="9071"
      ></path>
      <path
        d="M325.256533 904.789333C325.256533 932.096 303.104 954.248533 275.797333 954.248533 248.490667 954.248533 226.338133 932.096 226.338133 904.789333 226.338133 877.482667 248.490667 855.330133 275.797333 855.330133 303.104 855.330133 325.256533 877.482667 325.256533 904.789333"
        fill="#2C86F8"
        p-id="9072"
      ></path>
      <path
        d="M499.503787 972.485973C499.503787 997.33504 479.36512 1017.439573 454.550187 1017.439573 429.70112 1017.439573 409.562453 997.33504 409.562453 972.485973 409.562453 947.636907 429.70112 927.532373 454.550187 927.532373 479.36512 927.532373 499.503787 947.636907 499.503787 972.485973"
        fill="#2C86F8"
        p-id="9073"
      ></path>
      <path
        d="M691.705173 958.296747C691.705173 980.65408 673.580373 998.77888 651.22304 998.77888 628.865707 998.77888 610.740907 980.65408 610.740907 958.296747 610.740907 935.939413 628.865707 917.814613 651.22304 917.814613 673.580373 917.814613 691.705173 935.939413 691.705173 958.296747"
        fill="#2C86F8"
        p-id="9074"
      ></path>
      <path
        d="M852.67456 866.8672C852.67456 886.7328 836.563627 902.877867 816.663893 902.877867 796.798293 902.877867 780.68736 886.7328 780.68736 866.8672 780.68736 847.0016 796.798293 830.856533 816.663893 830.856533 836.563627 830.856533 852.67456 847.0016 852.67456 866.8672"
        fill="#2C86F8"
        p-id="9075"
      ></path>
      <path
        d="M961.25952 716.786347C961.25952 734.194347 947.162453 748.291413 929.754453 748.291413 912.346453 748.291413 898.215253 734.194347 898.215253 716.786347 898.215253 699.378347 912.346453 685.28128 929.754453 685.28128 947.162453 685.28128 961.25952 699.378347 961.25952 716.786347"
        fill="#2C86F8"
        p-id="9076"
      ></path>
      <path
        d="M998.64576 526.861653C998.64576 541.77792 986.528427 553.895253 971.61216 553.895253 956.695893 553.895253 944.57856 541.77792 944.57856 526.861653 944.57856 511.945387 956.695893 499.828053 971.61216 499.828053 986.528427 499.828053 998.64576 511.945387 998.64576 526.861653"
        fill="#2C86F8"
        p-id="9077"
      ></path>
    </svg>
    <div class="text">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.cover {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  height: initial;
  width: 100%;
  z-index: 5;
}

.text {
  height: calc(~"100% - 80px");
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 80px;
}

/*
    Set the color of the icon
*/
// svg path,
// svg rect {
//     fill: #ff6700;
// }
.loading {
  z-index: 5;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  animation: turn1 0.8s linear infinite;
}

@keyframes turn1 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
<script>
export default {
  components: {},
  props: {
    color: {
      type: String,
      default: "#2C86F8",
    },
    bgColor: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
