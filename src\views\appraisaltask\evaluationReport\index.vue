<template>
  <div class="page-evaluationReport auto-fill">
    <div class="head-title">
      <span class="title-headline">{{ titleName || '暂无' }}-治理评价综合统计</span>
      <el-popover
        popper-class="reference-popover"
        visible-arrow="false"
        placement="bottom"
        v-model="visible"
        trigger="click"
      >
        <div class="form-content">
          <ui-label class="mb-lg" label="考核任务" :width="65">
            <Select v-model="taskType" placeholder="请选择" class="width-input">
              <Option value="1" key="1">视图数据全量检测任务</Option>
              <Option value="2" key="2">视图数据上报检测任务</Option>
            </Select>
          </ui-label>

          <div class="mb-md t-center">
            <Button class="ml-sm" type="primary" @click="submit">确 定</Button>
          </div>
        </div>
        <i class="icon-font icon-shaixuan f-16 screen" slot="reference"></i>
      </el-popover>
    </div>
    <div class="evaluationReport-content">
      <div class="content-box" v-if="tabList.length != 0">
        <div class="synthesize-reach">
          <synthesize-left
            ref="synthesize-left"
            :tabList="tabList"
            :statisticList="statisticList"
            :queryAccessDataCount="queryAccessDataCount"
            @getData="getData"
            @echartClick="echartClick"
          ></synthesize-left>
          <comprehensive-ranking
            ref="comprehensiveRanking"
            :rank-data="averageList"
            :rankLoading="rankLoading"
          ></comprehensive-ranking>
        </div>
        <div class="synthesize-tendency" v-ui-loading="{ loading: echartsLoading, tableData: echart1 }">
          <div class="tendency-box" v-if="echart1.length > 0">
            <div class="reach-title">
              <title-content v-if="dayType === '日'" :title="+year + '年' + month + '月份评测趋势图'"></title-content>
              <title-content v-if="dayType === '月'" :title="+year + '年评测趋势图'"></title-content>
              <div class="btns">
                <span class="btn_jk" :class="{ active: curBtn === 1 }" @click="changeBtn(1)"> 日 </span>
                <span class="btn_kk" :class="{ active: curBtn === 2 }" @click="changeBtn(2)"> 月 </span>
              </div>
            </div>
            <div class="echarts-box">
              <draw-echarts
                :echart-option="echartRin"
                :echart-style="rinStyle"
                ref="zdryChart"
                class="charts"
                :echarts-loading="echartsLoading"
              ></draw-echarts>
            </div>
          </div>
        </div>
        <div class="capture-title"><span>评价指标检测结果一览表</span></div>
        <div class="detection-box">
          <div class="detection-title">
            <ui-label class="inline" label="行政区划" :width="66">
              <Select placeholder="请选择行政区划" clearable v-model="administrative" @on-change="administrativeChange">
                <Option v-for="(item, index) in administrativeList" :key="index" :value="item.key">{{
                  item.value
                }}</Option>
              </Select></ui-label
            >
            <!-- <ui-label label="" :width="65" class="">
              <span class="ml-lg base-text-color f-12"> 最近一次评测时间：2021-06-16 21:21:45</span>
            </ui-label> -->
            <ui-label label="" :width="10" class="title-export">
              <ui-btn-tip icon="icon-wenhao" class="mr-sm vt-middle" @click.native="hintModalShow()">
                <template #content>
                  <p>评价指标说明</p>
                  <p @click="hintModalShow" class="click-hint">立即点开查看</p>
                </template>
              </ui-btn-tip>
              <Button type="primary" class="btn_search fr" :loading="isDisabled" @click="getExport">
                <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
                <span class="inline ml-xs">导出</span>
              </Button>
            </ui-label>
          </div>
          <div class="detection-table">
            <ui-table
              class="ui-table"
              :table-columns="columns14"
              :table-data="tableData"
              :span-method="handleSpan"
              :stripe="false"
              :disabledHover="true"
              :loading="tableLoading"
            ></ui-table>
          </div>
        </div>
        <div class="capture-title"><span>评价结果分类展示</span></div>
        <capture-box ref="captureEvaluation" :tabList="tabList"></capture-box>

        <hint-modal-show ref="hintModalShow" v-model="hintShow" v-if="hintShow" title="评价指标说明"></hint-modal-show>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.reference-popover {
  margin-top: 0 !important;
  position: absolute !important;
  top: 166px !important;
  left: 1540px !important;
  z-index: 99999 !important;
  width: 350px !important;
  height: 150px !important;
  border: none !important;
  padding: 28px 20px 20px !important;
  background: #051e43 !important;
  .popper__arrow {
    display: none !important;
  }
}
.hint-icon {
  background: var(--bg-wenhao);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.click-hint {
  color: #2d8cf0;
  cursor: pointer;
}
</style>
<style lang="less" scoped>
.width-input {
  width: 210px;
}

.page-evaluationReport {
  padding: 20px;
  background-color: var(--bg-content);
  position: relative;
  .head-title {
    height: 56px;
    line-height: 56px;
    background: linear-gradient(180deg, rgba(13, 93, 197, 0.6) 0%, rgba(13, 49, 97, 0.6) 100%);
    position: relative;
    // opacity: 0.63;
    text-align: center;

    .title-time {
      font-size: 14px;
      color: #ffffff;
      opacity: 1;
      position: absolute;
      left: 20px;
    }
    .title-headline {
      font-size: 24px;
      font-weight: bold;
      color: #0df6e7;
      opacity: 1;
    }
    .screen {
      position: absolute;
      right: 20px;
      color: #56789c;
      &:hover {
        color: var(--color-primary);
      }
    }
  }
  .evaluationReport-content {
    overflow-y: auto;
    width: 100%;
    height: 100%;
    .content-box {
      overflow-y: auto;
      margin-top: 10px;
    }
  }

  .synthesize-reach {
    height: 610px;
    width: 100%;
    display: flex;
    .synthesize-left {
      width: 75%;
    }
  }

  .synthesize-tendency {
    height: 300px;
    width: 100%;
    background-color: var(--bg-sub-content);
    margin-top: 10px;
    margin-bottom: 10px;
    .tendency-box {
      .reach-title {
        text-align: center;
        position: relative;
        padding-top: 20px;
        width: 100%;
        .days {
          position: absolute;
          left: 43px;
          top: 40px;
          font-size: 14px;
          color: #fff;
        }
        .btns {
          position: absolute;
          right: 20px;
          top: 22px;

          span {
            display: inline-block;
            padding: 2px 20px;
            color: #fff;

            font-family: 'MicrosoftYaHei-Bold';
            background-color: #12294e;
            border-radius: 0;
            border: 1px solid #1b82d2;
          }
          .active {
            color: #fff;
            background-color: #2d8cf0;
          }
          span:hover {
            display: inline-block;
            color: #fff;
            background-color: #2d8cf0;
          }
        }
        .active {
          color: #fff;
          background-color: #2d8cf0;
        }
      }
      // .echarts-box {
      //   height: 250px;
      //   width: 100%;
      //   position: relative;
      //   .charts {
      //     display: inline-block;
      //   }
      // }
    }
  }
  .detection-box {
    width: 100%;
    height: 680px;
    margin: 10px 0;
    overflow: visible;
    background-color: var(--bg-sub-content);
    .detection-title {
      height: 50px;
      line-height: 50px;
      padding: 0 20px;
      position: relative;
      .ui-label {
        display: inline-block;
        .ivu-select {
          width: 180px;
          .ivu-select-dropdown {
            position: absolute;
            top: 0;
          }
        }
      }
      .title-export {
        // margin-top: 10px;
        position: absolute;
        right: 20px;
        top: 10px;
        @{_deep}.ivu-btn-primary {
          padding: 0 16px;
          height: 30px;
          line-height: 30px;
        }
      }
    }
    @{_deep}.ivu-tooltip-rel {
      .icon-wenhao {
        background: var(--bg-wenhao) !important;
        -webkit-background-clip: text !important;
        color: #0000 !important;
      }
    }

    .detection-table {
      height: calc(100% - 54px);
      padding: 0 20px 10px;
      .ui-table {
        height: 100%;
        border: solid 1px #085aa7 !important;
        // overflow-y: auto;
      }

      @{_deep}.ivu-table table {
        width: 1730px !important;
        border-collapse: unset !important;
      }
      @{_deep}.ivu-table-fixed-header,
      @{_deep}.ivu-table-header,
      @{_deep}.ivu-table-fixed-right {
        th {
          background: #002f71 !important;
          font-size: 13px;
          font-family: MicrosoftYaHei;
          line-height: 19px;
          color: #8ab9f8;
          opacity: 0.9;
        }
      }
      @{_deep}.ivu-table-header {
        .ivu-table-column-left {
          .ivu-table-cell {
            padding: 0 15px !important;
          }
        }
        tr {
          border-top: solid 1px #085aa7 !important;
          border-left: solid 1px #085aa7 !important;

          th {
            border-bottom: 1px solid #085aa7 !important;
            border-left: solid 1px #085aa7 !important;
            &:last-child {
              border-left: none !important;
            }
            &:first-child {
              border-left: none !important;
            }
          }
        }
      }

      @{_deep}.ivu-table-tbody {
        table {
          border-collapse: 0 !important;
          border-spacing: 0;
        }
        .ivu-table-column-left {
          .ivu-table-cell {
            padding: 0 15px !important;
          }
        }
        overflow-y: auto;
        background-color: var(--bg-sub-content) !important;
        tr {
          border-top: solid 1px #085aa7 !important;
          border-right: solid 1px #085aa7 !important;
          td {
            border-left: solid 1px #085aa7 !important;
            border-bottom: 1px solid #085aa7 !important;
            &:first-child {
              border-left: none !important;
              @{_deep}.ivu-table-cell {
                padding: 0 5px !important;
              }
            }
          }
        }
      }
      @{_deep}.ivu-table-header {
        .none {
          display: none;
        }
      }
    }
  }
}

.capture-title {
  height: 56px;
  line-height: 56px;
  background: linear-gradient(180deg, rgba(13, 93, 197, 0.6) 0%, rgba(13, 49, 97, 0.6) 100%);
  position: relative;
  text-align: center;
  font-size: 20px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #0df6e7;
  opacity: 1;
}
</style>

<script>
import { mapGetters } from 'vuex';
import evaluationreport from '@/config/api/evaluationreport';
import home from '@/config/api/home';
export default {
  name: 'evaluationReport',
  data() {
    return {
      hintShow: false,
      disabledHover: false,
      isDisabled: false,
      selectOrgTree: {
        orgCode: '',
      },
      tableLoading: false,
      countObj: {},
      countChildObj: {},
      columns14: [
        {
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center',
        },
        {
          title: '数据类别',
          // key: 'moduleName',
          align: 'center',
          children: [
            {
              title: '指标模块名称',
              className: 'none',
              key: 'moduleName',
              align: 'center',
            },
            {
              title: '数据类别名称',
              className: 'none',
              key: 'category',
              align: 'center',
            },
          ],
        },

        {
          title: '指标名称',
          key: 'name',
          align: 'left',
        },
        {
          title: '目标值',
          key: 'standardValue',
          align: 'left',
        },
        {
          title: '考核分值',
          key: 'checkScore',
          align: 'left',
        },
        {
          title: '本次评测值',
          key: 'resultValue',
          align: 'left',
        },
        {
          title: '本次评测得分',
          key: 'currentScore',
          align: 'left',
        },
        {
          title: '本次综合得分',
          key: 'synthesisScore',
          align: 'center',
        },
      ],
      tableData: [],
      visible: false,
      rankLoading: false,
      curBtn: 1,
      taskType: '1',
      code: '00000',
      taskList: [],
      timeList: [],
      tabList: [],
      tabListCopy: [],
      averageList: [],
      task: 0,
      titleName: '',
      echartList: [],
      echart1: [],
      trendType: 'DAY',
      dayType: '日',
      rinStyle: {
        width: '100%',
        height: '230px',
      },
      echartRin: {},
      indexTitle: '',
      indexList: {},
      indexVal: '',
      regionCode: '',
      echartsLoading: false,
      statisticList: {},
      queryAccessDataCount: {},
      orgVal: {},
      year: '',
      month: '',
      searchData: {
        orgCode: null,
      },
      administrativeList: [],
      administrative: '',
      selfRegionCode: '',
    };
  },
  activated() {
    // window.addEventListener('scroll', this.handleScroll, true)
  },
  created() {},
  mounted() {},
  methods: {
    handleSpan({ row, rowIndex, columnIndex }) {
      //针对第一列
      //第一行数据直接向下合并
      if (rowIndex === 0 && columnIndex === 1) {
        return [Object.values(this.countObj)[0], 1];
      }
      /*
        之后的数据发现标识符相同的则置空, 标识符与上一列不符则开始向下合并
      */
      if (rowIndex !== 0 && columnIndex === 1) {
        if (row.moduleType === this.tableData[rowIndex - 1]['moduleType']) {
          return [0, 0];
        } else if (row.moduleType !== this.tableData[rowIndex - 1]['moduleType']) {
          return [this.countObj[row.moduleType], 1];
        }
      }
      //针对第二列
      //第一行数据直接向下合并
      if (rowIndex === 0 && columnIndex === 2) {
        return [Object.values(this.countChildObj)[0], 1];
      }
      /*
        之后的数据发现标识符相同的则置空, 标识符与上一列不符则开始向下合并
      */
      if (rowIndex !== 0 && columnIndex === 2) {
        if (row.categoryType === this.tableData[rowIndex - 1]['categoryType']) {
          return [0, 0];
        } else if (row.categoryType !== this.tableData[rowIndex - 1]['categoryType']) {
          return [this.countChildObj[row.categoryType], 1];
        }
      }
      if (rowIndex === 0 && columnIndex === 8) {
        return [this.tableData.length, 1];
      } else if (rowIndex !== 0 && columnIndex === 8) {
        return [0, 0];
      }
    },
    // handleScroll() {
    //   let dom = document.getElementsByClassName('head-title')[0]
    //   if (document.getElementsByClassName('evaluationReport-content')) {
    //     let scrollTop = document.getElementsByClassName('evaluationReport-content')[0].scrollTop
    //     if (scrollTop > 56) {
    //       dom.style.borderBottom = '1px solid #0F3971'
    //     } else {
    //       dom.style.borderBottom = 'none'
    //     }
    //   }
    // },
    hintModalShow() {
      this.hintShow = true;
    },
    async submit() {
      await this.getOptionalResultsByTaskType();
      if (!this.tabList.targetDataType) {
        this.$Message.warning('检测任务未执行，暂无统计数据！');
        return;
      } else {
        this.tabList = this.tabList;
      }
      this.$nextTick(() => {
        this.$refs['synthesize-left'].changeTab(
          this.tabList.indexModules[0].key,
          this.tabList.indexModules[0].value,
          this.tabList,
        );
        if (this.tabList.indexModules[0].key === '-1') {
          this.$refs.captureEvaluation.changeTab(
            this.tabList.indexModules[1].key,
            this.tabList.indexModules[1].value,
            this.tabList,
          );
        } else {
          this.$refs.captureEvaluation.changeTab(
            this.tabList.indexModules[0].key,
            this.tabList.indexModules[0].value,
            this.tabList,
          );
        }

        this.visible = false;
        this.queryIndexDeviceOverview();
        this.queryDeviceOfAccessDataCount();
        this.getOptionalRegion();
        this.getIndexOverviewData();
        this.viewByParamKey();
        this.getRankStatistics();
      });
    },

    selectedOrgTree(val) {
      this.selectOrgTree.orgCode = val.orgCode;
    },

    async getOptionalResultsByTaskType() {
      try {
        let res = await this.$http.get(evaluationreport.getOptionalResultsByTaskType, {
          params: {
            taskType: this.taskType,
          },
        });
        this.tabList = res.data.data;
        this.titleName = res.data.data.selfOrgName;
        this.selfRegionCode = this.tabList.selfRegionCode;
        this.administrative = this.selfRegionCode;
        this.year = this.tabList.year;
        this.month = this.tabList.month;
      } catch (err) {
        console.log(err);
      }
    },
    async queryIndexDeviceOverview() {
      try {
        let res = await this.$http.get(home.queryIndexDeviceOverview);
        this.statisticList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async queryDeviceOfAccessDataCount() {
      try {
        let {
          data: { data },
        } = await this.$http.get(home.queryDeviceOfAccessDataCount);
        this.queryAccessDataCount = data || {};
      } catch (e) {
        console.log(e);
      }
    },

    // async getqueryEvaluationAmountCount() {
    //   try {
    //     let res = await this.$http.get(evaluationreport.queryEvaluationAmountCount)
    //     this.statisticList = res.data.data
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    async getOptionalRegion() {
      let data = {
        selfRegionCode: this.administrative,
      };
      try {
        let res = await this.$http.post(evaluationreport.getOptionalRegion, data);
        this.administrativeList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    administrativeChange(val) {
      this.selfRegionCode = val;
      this.getIndexOverviewData();
    },
    async getIndexOverviewData() {
      this.tableData = [];
      this.countObj = {};
      this.countChildObj = {};
      this.tableLoading = true;
      let data = {
        selfRegionCode: this.selfRegionCode,
        rootResultIds: this.tabList.rootResultIds,
      };

      try {
        let res = await this.$http.post(evaluationreport.getIndexOverviewData, data);
        this.tableData = res.data.data;
        this.tableData.forEach((row) => {
          // 根据标识符统计相同类型数据
          if (row.moduleType) {
            if (this.countObj.hasOwnProperty(row.moduleType)) {
              this.countObj[row.moduleType]++;
            } else {
              this.countObj[row.moduleType] = 0;
              this.countObj[row.moduleType]++;
            }
          }

          if (row.categoryType) {
            if (this.countChildObj.hasOwnProperty(row.categoryType)) {
              this.countChildObj[row.categoryType]++;
            } else {
              this.countChildObj[row.categoryType] = 0;
              this.countChildObj[row.categoryType]++;
            }
          }
        });
        this.tableLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    //一览表 导出
    async getExport() {
      this.isDisabled = true;
      let params = {
        selfRegionCode: this.selfRegionCode,
        rootResultIds: this.tabList.rootResultIds,
      };
      try {
        let res = await this.$http.post(evaluationreport.exportIndexOverviewData, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.isDisabled = false;
      } catch (err) {
        console.log(err);
      }
    },

    initRin() {
      this.barData = this.echart1.map((row) => {
        return {
          id: row.id,
          value: row.standardsValue,
          day: row.dateTime,
        };
      });
      let opts = {
        xAxis: this.echart1.map((row) => row.dateTime),
        data: this.barData,
        dayType: this.dayType,
      };
      this.echartRin = this.$util.doEcharts.evaluationTendencyColumn(opts);
    },

    async getAverageValueTrend(val, list) {
      this.echartsLoading = true;
      let data = {
        indexModule: val,
        resultIds: list.resultIds,
        trendType: this.trendType,
        dataType: list.targetDataType,
        regionCode: this.regionCode,
        selfRegionCode: list.selfRegionCode,
        taskType: this.taskType,
      };
      try {
        let res = await this.$http.post(evaluationreport.getAverageValueTrend, data);
        this.echart1 = res.data.data;
        this.initRin();
        this.echartsLoading = false;
      } catch (err) {
        console.log(err);
        this.echartsLoading = false;
      }
    },
    async viewByParamKey() {
      try {
        let params = {
          key: 'HOME_PAGE_CONFIG',
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = JSON.parse(data.paramValue);
        this.taskType = paramValue.evaluationTask;
      } catch (e) {
        console.log(e);
      }
    },
    async getRankStatistics() {
      this.rankLoading = true;
      let data = {
        examTaskId: this.taskType,
      };
      try {
        // this.averageList = rankData
        let res = await this.$http.post(home.getRankStatistics, data);
        this.averageList = res.data.data;
        this.rankLoading = false;
      } catch (err) {
        this.rankLoading = false;
        console.log(err);
      }
    },

    // async getAverageTanking(val, value, list) {
    //   this.rankLoading = true
    //   let data = {
    //     indexModule: val,
    //     dataType: list.targetDataType,
    //     resultIds: list.resultIds,
    //     selfRegionCode: list.selfRegionCode,
    //   }
    //   try {
    //     let res = await this.$http.post(evaluationreport.getRankStatistics, data)
    //     this.averageList = res.data.data
    //     this.rankLoading = false
    //   } catch (err) {
    //     this.rankLoading = false
    //     console.log(err)
    //   }
    // },
    async getData(val, value, list) {
      this.indexTitle = value;
      this.indexList = list;
      this.indexVal = val;
      // await this.getAverageTanking(val, value, list)
      await this.getAverageValueTrend(val, list);
    },
    async echartClick(params) {
      this.regionCode = params.data.regionCode;
      await this.getAverageValueTrend(this.indexVal, this.indexList);
    },
    changeBtn(val) {
      this.curBtn = val;
      if (val === 1) {
        this.trendType = 'DAY';
      } else {
        this.trendType = 'MONTH';
      }
      if (this.trendType === 'DAY') {
        this.dayType = '日';
      } else {
        this.dayType = '月';
      }
      this.getAverageValueTrend(this.indexVal, this.indexList);
    },
  },

  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {
    comprehensiveConfig: {
      handler(val) {
        if (!!val && !!Object.keys(val).length) {
          this.submit();
        }
      },
      immediate: true,
    },
  },
  // deactivated() {
  //   window.removeEventListener('scroll', this.handleScroll, true)
  // },
  components: {
    synthesizeLeft: require('@/views/appraisaltask/evaluationReport/components/synthesize-left.vue').default,
    comprehensiveRanking: require('@/views/appraisaltask/evaluationReport/components/comprehensive-ranking.vue')
      .default,
    captureBox: require('@/views/appraisaltask/evaluationReport/components/capture-box.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,

    hintModalShow: require('@/views/appraisaltask/evaluationReport/components/hint-modal-show.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
