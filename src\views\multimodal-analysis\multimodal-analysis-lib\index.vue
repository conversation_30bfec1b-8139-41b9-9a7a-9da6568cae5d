<!--
 * @Date: 2025-03-04 16:25:41
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-30 16:42:37
 * @FilePath: \icbd-view\src\views\multimodal-analysis\multimodal-analysis-lib\index.vue
-->
<template>
  <div class="muti-content">
    <div class="search-box">
      <MutiSearch @searchHander="searchHander"></MutiSearch>
    </div>
    <div class="padding-box">
      <div class="container">
        <!-- 操作栏 -->
        <div class="operate">
          <!-- <div class="left-box">
            <Checkbox
              v-model="allCheckStatus"
              :indeterminate="indeterminate"
              @on-change="(e) => allChecked(e)"
            >
              全选
            </Checkbox>
            <Button class="margin"
              ><i class="iconfont icon-daochu"></i>导出</Button
            >
          </div> -->
          <div class="right-box">
            <div
              class="flex-center"
              v-for="item in sortTypelist"
              :key="item.value"
              @click="sortTypeHandle(item.value)"
              v-show="item.value === 'absTime' || showScore"
            >
              <i
                class="iconfont icon-moveup"
                :style="{
                  color: pageInfo.sortField === item.value ? '#2c86f8' : '',
                }"
                :class="{
                  rotate: sortType[item.value] === 'asc',
                }"
              ></i>
              &nbsp;&nbsp;{{ item.label }}
            </div>
            <!-- <div class="line"></div> -->
            <!-- <div class="point" @click="handleFalls()">
              {{ fallsPage ? "传统翻页版本" : "瀑布流版本" }}
            </div> -->
          </div>
        </div>
        <div class="result-list">
          <div
            class="result-item"
            v-for="(item, index) in dataList"
            :key="item.id"
          >
            <ResultCard
              showMoreTip
              :data="item"
              @onAction="onAction"
              @click.native="openDetailMutiAnalysis(item, index)"
              @checkHandler="(val) => checkHandler(val, item)"
            ></ResultCard>
          </div>
          <!-- <div
            class="empty-card-1"
            v-for="(item, index) of 5 - (dataList.length % 5)"
            :key="index + 'demo'"
          ></div> -->
          <ui-empty v-if="total === 0"></ui-empty>
          <div class="loading-box" :class="{ 'loading-box-position': isObserver}" ref="observerBox">
            <ui-loading v-if="scrollLoading"></ui-loading>
            <div
              class="last-page"
              v-if="!isObserver  && pageInfo.pageNumber > 1"
            >
              数据到底了！！！
            </div>
          </div>
        </div>
        <!-- <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[15, 30, 45]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page> -->
      </div>
    </div>
    <DetailMutiAnalysis
      v-if="showDetail"
      ref="detailMutiAnalysisRef"
      :tableList="dataList"
      @close="showDetail = false"
    ></DetailMutiAnalysis>
    <direction-model ref="directionModel"></direction-model>
  </div>
</template>

<script>
import { mapMutations } from "vuex";
import MutiSearch from "./components/muti-search.vue";
import ResultCard from "./components/result-card.vue";
import DetailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
import { llmStructureSearchPageList } from "@/api/multimodal-analysis";
import directionModel from "@/views/wisdom-cloud-search/search-center/advanced-search/components/direction-model";
export default {
  name: "muit-lib-analysis",
  components: {
    MutiSearch,
    ResultCard,
    DetailMutiAnalysis,
    directionModel,
  },
  data() {
    return {
      allCheckStatus: false,
      fallsPage: true,
      scrollLoading:false,
      pageInfo: {
        pageNumber: 1,
        pageSize: 45,
        sortField: "absTime",
      },
      total: 0,
      showDetail: false,
      dataList: [],
      isObserver: false,
      sortTypelist: [
        {
          label: "相似度排序",
          value: "score",
        },
        {
          label: "时间排序",
          value: "absTime",
        },
      ],
      sortType: {
        score: "desc",
        absTime: "desc",
      },
      showScore:false
    };
  },
  computed: {
    indeterminate() {
      return (
        this.dataList.filter((item) => item.isChecked).length !==
          this.dataList.length && this.allCheckStatus
      );
    },
  },
  mounted() {
    this.setLayoutNoPadding(true);
    this.initObserver();
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    openDetailMutiAnalysis(item, index) {
      this.showDetail = true;
      this.$nextTick(() => {
        this.$refs.detailMutiAnalysisRef.showList(index);
      });
    },
    allChecked(val) {
      this.dataList = this.dataList.map((item) => ({
        ...item,
        isChecked: val,
      }));
    },
    checkHandler() {
      // item.isChecked = checked;
      this.allCheckStatus =
        this.dataList.filter((item) => item.isChecked).length > 0;
    },
    initObserver() {
      const that = this;
      this.observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && that.fallsPage && that.isObserver) {
            this.getDataList(that.pageInfo.pageNumber + 1);
          }
        },
        {
          rootMargin: "0px",
          threshold: 0.1,
        }
      );

      // 在 nextTick 中确保 DOM 已渲染
      this.$nextTick(() => {
        const loadingRef = this.$refs.observerBox;
        if (loadingRef) {
          this.observer.observe(loadingRef);
        }
      });
    },
    sortTypeHandle(key) {
      this.pageInfo.sortField = key;
      this.sortType[key] = this.sortType?.[key] === "desc" ? "asc" : "desc";
      this.searchHander();
    },
    /**
     * @description: 瀑布 - 翻页切换
     */
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.queryList();
    },
    getDataList(page = 1) {
      this.scrollLoading = true;
      this.pageInfo.pageNumber = page;
      let params = {
        ...this.pageInfo,
      };
      if (!this.fallsPage) {
        this.checkAll = false;
      }
      llmStructureSearchPageList(params).then((res) => {
        this.scrollLoading = false;
        const { entities = [] } = res.data;
          this.dataList.push(...entities);
          this.isObserver =  !(entities?.length < this.pageInfo.pageSize);
         this.total = this.dataList.length;
      });
    },
    searchHander(params) {
      if(params){
        this.showScore = params.keywords || params.searchImageUrl || false;
        this.pageInfo.sortField = this.showScore ? "score" : "absTime";
      }
      this.pageInfo = {
        ...this.pageInfo,
        sortType: this.sortType[this.pageInfo.sortField],
        ...params,
      };
      this.queryList();
    },
    queryList() {
      this.pageInfo.pageSize = this.fallsPage ? 45 : 15;
      this.dataList = [];
      this.allCheckStatus = false;
      if (this.fallsPage) {
        this.isObserver = false;
      }
      this.getDataList();
    },
    pageChange(val) {
      this.getDataList(val);
    },
    pageSizeChange(val) {
      this.pageInfo.pageSize = val;
      this.getDataList();
    },
    onAction(item, type) {
      if (type === 3) this.openDirectModel(item);
    },
    openDirectModel(row) {
      const { latitude, longitude, name } =
        row?.deviceInfo || row?.resourceInfo;
      if (!latitude || !longitude) {
        this.$Message.error("暂无坐标");
        return;
      }
      this.$refs.directionModel.show({
        geoPoint: {
          lat: latitude,
          lon: longitude,
        },
        deviceName: name,
      });
    },
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.observer?.disconnect();
    this.setLayoutNoPadding(false);
  },
};
</script>

<style lang="less" scoped>
.muti-content {
  width: 100%;
  height: 100%;
  background: #fff;
  background-image: url("~@/assets/img/multimodal-analysis/multi-analysis-bg.png");
  background-repeat: no-repeat;
  background-size: contain;
  display: flex;
  flex-direction: column;

  .search-box {
    margin-top: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .padding-box {
    flex: 1;
    padding: 20px 10px 10px 10px;
    background: transparent;
    display: flex;
    overflow: hidden;
  }

  .container {
    display: flex;
    flex-direction: column;
    .operate {
      display: flex;
      justify-content: space-between;

      .right-box {
        display: flex;
        gap: 10px;
        height: 20px;
        align-items: center;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);

        .flex-center {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .rotate {
          transform: rotate(180deg);
        }
        .line {
          height: 16px;
          border: 1px solid #d8d8d8;
        }

        .point {
          cursor: pointer;
        }
      }
    }

    .result-list {
      flex: 1;
      position: relative;
      padding-top: 16px;
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      height: calc(~"100% - 40px");
      overflow: scroll;

      .result-item {
        width: 335px;
      }
      .empty-card-1 {
        width: 335px;
        height: 254px;
      }
      .loading-box {
        width: 100%;
        height: 80px;
        &.loading-box-position{
          position: relative;
        }
        .last-page {
          margin: o auto;
          text-align: center;
          font-size: 20px;
        }
      }
    }
  }
}
</style>