<template>
  <div>
    <!-- 人脸视图数据 指标配置 -->
    <!--
FACE_CAPTURE_PASS ===人脸卡口设备抓拍合格率
FACE_UPLOAD ===人脸卡口设备及时上传率
FACE_FOCUS_UPLOAD ===重点人脸卡口设备及时上传率
FACE_CLOCK  ===人脸卡口设备时钟准确率
FACE_DEVICE_CONNECT_INTERNET ===人脸卡口联网率
FACE_ONLINE_RATE ===人脸卡口在线率
FACE_CAPTURE_RATIONALITY === 人脸卡口抓拍数量合理性
FACE_CAPTURE_RATE === 人脸卡口抓拍率
FACE_VALID_SUBMIT_QUANTITY 人脸卡口有效报送数量达标率
FACE_PLATFORM_ONLINE_RATE 人脸视图可在线率
 -->
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="getWidth">
      <common-form
        :label-width="getWidth"
        class="common-form"
        ref="commonForm"
        :moduleAction="moduleAction"
        :form-data="formData"
        :form-model="formModel"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
        @handleDetect="getDetect"
      >
        <div slot="extract">
          <template
            v-if="
              ['FACE_CAPTURE_PASS', 'FACE_UPLOAD', 'FACE_FOCUS_UPLOAD', 'FACE_CLOCK'].includes(indexType) &&
              formData.detectMode !== '4'
            "
          >
            <FormItem label="每设备抽取图片" prop="captureNum">
              <InputNumber
                v-model="formData.captureNum"
                class="input-width"
                placeholder="请输入抽取图片"
                clearable
              ></InputNumber>
            </FormItem>
            <FormItem
              v-if="['FACE_CAPTURE_PASS'].includes(indexType)"
              label=""
              :class="{ 'mt-minus-sm': formData.captureNum }"
              prop="isMissPic"
            >
              <Checkbox v-model="formData.isMissPic" label="" :true-value="1" :false-value="0">
                <span>图片数量不足，则设备不合格</span>
              </Checkbox>
            </FormItem>
            <FormItem
              prop="deviceQueryForm.dayByCapture"
              label="图片抽取范围"
              :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
            >
              <span class="base-text-color mr-xs">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByCapture"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByCapture"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByCapture"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
            </FormItem>
          </template>
        </div>
        <div
          slot="waycondiction"
          class="mt-xs"
          v-if="['1', '3'].includes(formData.detectMode) && filterCondition.includes(indexType)"
        >
          <div>
            <span class="base-text-color">检测条件：</span>
            <div>
              <Checkbox v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
                >设备可用</Checkbox
              >
              <Checkbox
                class="ml-sm"
                v-if="notReportionDeviceList.includes(indexType)"
                v-model="formData.deviceQueryForm.examReportStatus"
                true-value="1"
                false-value="0"
                >设备未报备</Checkbox
              >
            </div>
            <div v-if="!['FACE_ONLINE_RATE'].includes(indexType)">
              <FormItem
                prop="deviceQueryForm.dayByFilterOnline"
                label=""
                :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
              >
                <Checkbox class="mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
                <span class="base-text-color">近</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                  :min="0"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color">天，</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.startByFilterOnline"
                  :min="0"
                  :max="23"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color mr-sm">点至</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.endByFilterOnline"
                  :min="0"
                  :max="23"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color mr-sm">点</span>
                <div class="capture-vehicle">
                  <span class="base-text-color mr-sm">抓拍人脸不少于</span>
                  <InputNumber
                    v-model.number="formData.deviceQueryForm.countByFilterOnline"
                    :min="0"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color">张</span>
                  <p class="color-failed">说明：系统只检测满足条件的设备。</p>
                </div>
              </FormItem>
            </div>
          </div>
        </div>
      </common-form>

      <!-- 人脸卡口设备及时上传率  重点人脸卡口设备及时上传率-->
      <template v-if="['FACE_UPLOAD', 'FACE_FOCUS_UPLOAD'].includes(indexType)">
        <FormItem label="图片上传时延" class="right-item mt-md">
          <FormItem
            class="inline one-time"
            prop="beforeTimeDelay"
            :rules="[{ validator: validateTimeDelayBefore, trigger: 'change', required: false }]"
          >
            <InputNumber
              v-model.number="formData.beforeTimeDelay"
              :formatter="(value) => `${parseInt(value)}`"
              class="width-mini"
            ></InputNumber>
          </FormItem>
          <FormItem class="inline" prop="beforeTimeFormat">
            <Select v-model="formData.beforeTimeFormat" transfer class="width-mini ml-sm">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
          <span class="base-text-color"> <= 接收时间 - 抓拍时间 <= </span>
          <FormItem
            class="inline one-time"
            prop="timeDelay"
            :rules="[{ validator: validateTimeDelay, trigger: 'change', required: true }]"
          >
            <InputNumber
              v-model.number="formData.timeDelay"
              :formatter="(value) => `${parseInt(value)}`"
              class="width-mini mr-sm"
            ></InputNumber>
          </FormItem>
          <FormItem class="inline" prop="timeFormat">
            <Select v-model="formData.timeFormat" transfer class="width-mini">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
        </FormItem>
        <p class="color-failed" :style="{ marginLeft: `${getWidth}px` }">说明：不配置表示允许时间倒挂</p>
      </template>
      <!-- 人脸卡口设备时钟准确率 -->
      <template v-if="['FACE_CLOCK'].includes(indexType)">
        <FormItem label="抓拍时间和接收时间允许时间误差" class="right-item mb-sm mt-md" prop="timeDelay">
          <InputNumber v-model.number="formData.timeDelay" placeholder="请输入误差时间" class="w240"></InputNumber>
          <FormItem class="inline" prop="timeFormat">
            <Select v-model="formData.timeFormat" transfer class="width-mini ml-sm">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
          <p class="label-color">说明:时钟准确要求人脸数据的抓拍时间不得晚于数据接收时间，误差在30秒（可配置）以内。</p>
        </FormItem>
      </template>

      <!-- 人脸卡口在线率  人脸卡口联网率 -->
      <template>
        <FormItem
          label="时间范围"
          class="right-item mb-sm"
          prop="name"
          v-if="['FACE_ONLINE_RATE', 'FACE_DEVICE_CONNECT_INTERNET', 'FACE_ONLINE_RATE_ADVANCE'].includes(indexType)"
        >
          <Select v-model="formData.timeDelay" class="select-width" placeholder="请选择时间范围" transfer>
            <Option value="5">昨天</Option>
            <Option value="1">当天</Option>
            <Option value="2">最近两天</Option>
            <Option value="3">最近一周</Option>
            <Option value="4">最近30天</Option>
            <Option value="6" v-if="['FACE_ONLINE_RATE', 'FACE_DEVICE_CONNECT_INTERNET'].includes(indexType)"
              >自定义</Option
            >
          </Select>
        </FormItem>
        <FormItem
          label=" "
          :class="[formData.lastHours && formData.lastHours < 24 ? 'mb-md' : 'mb-lg']"
          v-if="formData.timeDelay == '6'"
          prop="lastHours"
          :rules="[{ validator: validateLastHours, trigger: 'change', required: false }]"
        >
          <div class="lastHours-input base-text-color">
            最近<InputNumber
              :min="0"
              :step="1"
              :active-change="false"
              :precision="1"
              v-model="formData.lastHours"
              class="mr-sm ml-sm"
              placeholder="请输入"
            ></InputNumber
            >小时
          </div>
        </FormItem>
        <FormItem
          label=" "
          :class="['mb-sm', !formData.lastHours || formData.lastHours > 24 ? '' : 'mt-minus-sm']"
          v-if="formData.timeDelay == '6' && ['FACE_ONLINE_RATE'].includes(indexType)"
        >
          <p class="label-color">说明：该时间区间内如果没有抓拍数据，则设备离线。</p>
        </FormItem>

        <FormItem
          label="时间区间"
          class="mb-sm"
          v-if="formData.timeDelay != '6' && ['FACE_ONLINE_RATE', 'FACE_ONLINE_RATE_ADVANCE'].includes(indexType)"
        >
          <div class="inspection">
            <div class="row-inspection" v-for="(item, index) in formData.dateRang" :key="index">
              <FormItem>
                <div class="form-row">
                  <span class="width-picker">
                    <Select class="time-picker" transfer v-model="item.hourStart" clearable>
                      <Option
                        v-for="it in schemeList"
                        :value="it.value"
                        :key="it.value"
                        :disabled="item.hourEnd ? item.hourEnd <= it.value : false"
                        :class="item.hourEnd ? (item.hourEnd <= it.value ? 'notCheck' : '') : ''"
                        >{{ it.label }}</Option
                      >
                    </Select>
                  </span>
                  <span class="color-bule mr-sm ml-sm">—</span>
                  <span class="width-picker">
                    <Select class="time-picker" transfer v-model="item.hourEnd" clearable>
                      <Option
                        v-for="it in schemeList"
                        :value="it.value"
                        :key="it.value"
                        :disabled="item.hourStart ? item.hourStart >= it.value : false"
                        :class="item.hourStart ? (item.hourStart >= it.value ? 'notCheck' : '') : ''"
                        >{{ it.label }}</Option
                      >
                    </Select>
                  </span>
                  <span>
                    <span class="addition ml-sm" v-if="formData.dateRang.length - 1 === index" @click="toAdd"
                      ><i class="icon-font icon-tree-add f-16 font-other" title="添加"></i
                    ></span>
                    <span class="cancel ml-sm" v-if="index != 0" @click="toDel(index)"
                      ><i class="icon-font icon-shanchu1 f-14 font-other" title="删除"></i></span
                  ></span>
                </div>
              </FormItem>
            </div>
          </div>
          <p class="label-color">说明：该时间区间内如果没有抓拍数据，则设备离线。</p>
        </FormItem>
        <!-- 人脸卡口联网率 -->
        <FormItem label="上传人脸数量" v-if="['FACE_DEVICE_CONNECT_INTERNET'].includes(indexType)">
          <span class="color-white mr-sm">不少于</span>
          <InputNumber v-model="formData.imageNum" :disabled="isView"></InputNumber>
          <span class="base-text-color ml-sm">张</span>
          <p class="label-color">说明：该时间范围内如果有足够数量的抓拍数据，则设备联网。</p>
        </FormItem>
        <template v-if="['FACE_DEVICE_CONNECT_INTERNET'].includes(indexType)">
          <FormItem label="多目设备检测" class="right-item mb-sm">
            <Checkbox v-model="formData.isMulti" :true-value="1" :false-value="0"
              >多目设备合并成一个设备进行检测统计</Checkbox
            >
          </FormItem>
          <FormItem
            label="多目设备定义"
            class="right-item mb-sm"
            prop="multiType"
            :rules="[
              { validator: validateDayByMultiType, trigger: 'change', required: formData.isMulti ? true : false },
            ]"
          >
            <RadioGroup class="mb-sm" v-model="formData.multiType">
              <Radio :label="1" class="mr-lg" :disabled="!formData.isMulti">IP地址相同</Radio>
              <Radio :label="2" class="mr-lg" :disabled="!formData.isMulti">MAC地址相同</Radio>
              <Radio :label="3" :disabled="!formData.isMulti">IP地址相同和MAC地址相同</Radio>
            </RadioGroup>
          </FormItem>
        </template>
      </template>

      <!-- 人脸卡口抓拍数量合理性、人脸卡口抓拍率 -->
      <template v-if="['FACE_CAPTURE_RATIONALITY', 'FACE_CAPTURE_RATE'].includes(indexType)">
        <FormItem label="抓拍数据过少" class="right-item mb-sm" prop="timeDelay">
          <span class="base-text-color mr-xs">近</span>
          <InputNumber class="mr-xs" v-model="formData.timeDelay"></InputNumber>
          <span class="base-text-color mr-xs">天，抓拍数量不大于</span>
          <FormItem :label-width="0" prop="imageNum" class="inline">
            <InputNumber v-model="formData.imageNum"></InputNumber>
          </FormItem>
          <span class="base-text-color ml-xs">张；</span>
        </FormItem>
        <FormItem label="抓拍数量突降" class="right-item mb-sm" prop="captureDrop">
          <span class="base-text-color mr-xs">检测当天抓拍数量较历史同一天的平均抓拍量相比，降低</span>
          <InputNumber class="mr-xs" v-model="formData.captureDrop" :precision="2"></InputNumber>
          <span class="base-text-color">%</span>
          <Tooltip transfer>
            <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
            <div slot="content" transfer>
              <p>抓拍数量突降计算逻辑：</p>
              <p>昨日抓拍量C1： 假设今日2021/10/20日发起检测，</p>
              <p>则C1=2021/10/19日抓拍量；</p>
              <p>历史同天抓拍量C2： 平台上线至2021年10月 19</p>
              <p>日号前所有星期二（10月19日是星期二）抓拍量的平均抓拍量；</p>
              <p>若（C2-C1）/C2>=50%（ 配置值），则判定抓拍数据量突降。</p>
            </div>
          </Tooltip>
        </FormItem>
      </template>
      <!--   FACE_VALID_SUBMIT_QUANTITY 人脸卡口有效报送数量达标率  -->
      <div v-if="indexType === 'FACE_VALID_SUBMIT_QUANTITY'">
        <FormItem label="达标数量设置">
          <Button type="dashed" class="area-btn" @click="regionalizationSelectVisible = true"
            >达标数量设置
            <span>{{ `已选择 ${formData.quantityConfig && formData.quantityConfig.length}个` }}</span>
          </Button>
        </FormItem>
        <FormItem label="有效设备检测">
          <div class="base-text-color">1、需产生人脸数据</div>
          <FormItem class="inline base-text-color">
            <Select v-model="formData.deviceDetection.dataConfig.key" transfer disabled class="width-mini ml-sm">
              <Option value="now">当</Option>
            </Select>
            <Select v-model="formData.deviceDetection.dataConfig.value" transfer disabled class="width-mini ml-sm">
              <Option value="month">月</Option>
            </Select>
            <span class="mr-xs">，抓拍人脸不少于</span>
            <InputNumber
              v-model.number="formData.deviceDetection.dataConfig.quantity"
              placeholder="请输入图片上传时延"
              class="width-mini mr-xs"
            ></InputNumber>
            <span>张</span>
          </FormItem>
          <div class="base-text-color">2、基础信息填报完整准确</div>
          <rule-list
            :formModel="formModel"
            :indexRuleList="formData.deviceDetection.ruleList"
            topicType="1"
            :moduleAction="moduleAction"
          ></rule-list>
        </FormItem>
      </div>
      <!--人脸卡口设备位置完整率  -->
      <FormItem label="摄像机位置类型" v-if="['FACE_EMPHASIS_LOCATION'].includes(moduleAction.indexType)">
        <RadioGroup class="mb-sm" v-model="formData.typeSource" @on-change="setDefaultEmphasisData">
          <Radio label="deviceTag" class="mr-lg">设备标签</Radio>
          <Radio label="deviceGather">采集区域字典</Radio>
        </RadioGroup>
        <div class="params-content mb-sm modal-video-image" v-if="formData.typeSource === 'deviceTag'">
          <ui-tag
            @close="handleClose(formData.emphasisData, item, index)"
            :closeable="item.source != 1"
            v-for="(item, index) in formData.emphasisData"
            :key="index + '-a' + item.key"
          >
            {{ item.value }}
          </ui-tag>
          <Button type="primary" @click="clickAdd('设备标签')">
            <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="新增"> </i
            ><span class="vt-middle">新增</span></Button
          >
        </div>
        <div class="area" v-else-if="formData.typeSource === 'deviceGather'">
          <Button type="dashed" class="area-btn" @click="clickArea"
            >请选择采集区域类型
            <span>{{ `已选择 ${(formData.emphasisData || 0) && formData.emphasisData.length}个` }}</span></Button
          >
        </div>
      </FormItem>
      <template v-if="['FACE_ACCURACY'].includes(indexType)">
        <FormItem label="检测规则设置" prop="rule">
          <rule-list
            :formModel="formModel"
            :formData="formData"
            :moduleAction="moduleAction"
            :indexRuleList="formData.ruleList"
          ></rule-list>
        </FormItem>
      </template>
      <template v-if="['FACE_ASSET_REGISTER'].includes(indexType)">
        <FormItem label="资产库已注册人脸卡口来源" required>
          <RadioGroup v-model="formData.source">
            <Radio label="ALL" class="mr-lg">资产库中所有人脸卡口</Radio>
            <Radio label="REGISTERED">视图库中所有已注册人脸卡口</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="资产库已注册人脸卡口要求">
          <Checkbox v-model="formData.registeredRequireData.phyStatus" :true-value="1" false-value=""
            >设备可用</Checkbox
          >
        </FormItem>
        <FormItem label="未注册人脸卡口要求">
          <Checkbox v-model="formData.unregisteredRequireData.isOnline" :true-value="1" false-value="" class="mr-lg"
            >设备可用</Checkbox
          >
          <Checkbox v-model="formData.unregisteredRequireData.phyStatus" :true-value="1" false-value=""
            >设备在线</Checkbox
          >
        </FormItem>
      </template>
      <FormItem :label="isUpdatePhyStatus(indexType)[0]['label']" v-if="isUpdatePhyStatus(indexType).length > 0">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="对历史检测结果比对分析" v-if="historyComparisonConfigModule.includes(indexType)">
        <RadioGroup v-model="formData.isDetectContrast">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <quantity-config
      v-model="regionalizationSelectVisible"
      v-if="regionalizationSelectVisible"
      :data="formData.quantityConfig"
      :regionData="treeData"
      @query="quantityQuery"
    ></quantity-config>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :checkbox-list="allDeviceFileList"
      :default-checked-list="defaultCheckedList"
      :field-name="fieldName"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>
  </div>
</template>

<script>
import { defaultEmphasisData } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';
import { mapGetters } from 'vuex';
import {
  isUpdatePhyStatus,
  historyComparisonConfigModule,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
export default {
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    indexType: {
      type: String,
      default: '',
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      filterCondition: [
        'FACE_QUANTITY_STANDARD',
        'FACE_CLOCK',
        'FACE_UPLOAD',
        'FACE_FOCUS_UPLOAD',
        'FACE_CAPTURE_PASS',
        'FACE_IMAGE_STORE_PASS',
        'FACE_ONLINE_RATE',
      ],
      regionalizationSelectVisible: false,
      algorithm: [], //算法
      formData: {
        dateRang: null,
        captureNum: null,
        isMissPic: 0, //是否以抓拍图片数量判定设备不合格
        lastHours: null, //选中自定义，最近x小时
        ruleList: [],
        snap: null,
        ocrModel: '',
        deviceDetection: {
          dataConfig: {
            key: 'now', //lately 最近
            value: 'month', // 30 10
            quantity: 0,
          },
          ruleList: [],
        },
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: false,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
        registeredRequireData: {
          phyStatus: '1',
        },
        unregisteredRequireData: {
          phyStatus: '',
        },
        isUpdatePhyStatus: 0,
      },
      ruleCustom: {
        captureDrop: [
          {
            required: true,
            type: 'number',
            message: '请输入抓拍数量突降比例',
            trigger: 'blur',
          },
        ],
        timeDelay: [
          {
            required: true,
            type: 'number',
            message: '请输入时间',
            trigger: 'blur',
          },
        ],
        imageNum: [
          {
            required: true,
            type: 'number',
            message: '请输入张数',
            trigger: 'blur',
          },
        ],
        timeFormat: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'blur',
          },
        ],
        ocrModel: {
          required: true,
          message: '请选择优先算法',
          trigger: 'change',
        },
      },
      schemeList: [],
      orgCodes: [],
      areaTreeData: [],
      selectAreaTree: {
        regionCode: '',
      },
      customSearch: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      defaultCheckedList: [],
      customizeAction: {
        title: '新增分析数据字段',
        leftContent: '所有分析数据字段',
        rightContent: '已选择分析数据字段',
        moduleStyle: {
          width: '80%',
        },
      },
      allDeviceFileList: [],
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      contentStyle: {
        height: '3.125rem',
      },
      needRuleConfig: ['FACE_VALID_SUBMIT_QUANTITY', 'FACE_ACCURACY'], // 需要配置规则的指标
      notReportionDeviceList: [
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_HISTORY_ACCURACY',
        'FACE_ONLINE_RATE',
        'VEHICLE_ONLINE_RATE',
      ], // 未报备设备显示属性
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      validateTimeDelay: (rule, value, callback) => {
        let { timeDelay } = this.formData;
        if (!timeDelay && timeDelay !== 0) {
          callback(new Error('请输入时间'));
        } else if (timeDelay <= 0) {
          callback(new Error('只允许填写正数'));
        }
        callback();
      },
      validateTimeDelayBefore: (rule, value, callback) => {
        let { beforeTimeDelay } = this.formData;
        if (beforeTimeDelay > 0) {
          callback(new Error('不允许填写正数'));
        }
        callback();
      },
      validateLastHours: (rule, value, callback) => {
        let { timeDelay, lastHours } = this.formData;
        if (timeDelay == '6' && !lastHours) {
          callback(new Error('请输入自定义时间'));
        } else if (timeDelay == '6' && lastHours > 24) {
          callback(new Error('最近时间不能超过24小时'));
        } else {
          callback();
        }
      },
      validateDayByMultiType: (rule, value, callback) => {
        let { isMulti, multiType } = this.formData;
        if (isMulti === 1 && !multiType) {
          callback(new Error('请选择多目设备定义'));
        } else {
          callback();
        }
      },
      historyComparisonConfigModule: historyComparisonConfigModule,
    };
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        this.schemeList = this.getHour();
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            timeDelay: null,
            imageNum: null,
            captureDrop: null,
            quantityConfig: [],
            visitTimeout: null,
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
          if (this.needRuleConfig.includes(this.indexType)) {
            this.getCheckRule();
          }
          if (['FACE_ASSET_REGISTER'].includes(this.indexType)) {
            this.formData.source = 'ALL';
            this.formData.registeredRequireData.phyStatus = '';
            this.formData.unregisteredRequireData.isOnline = '';
            this.formData.unregisteredRequireData.phyStatus = '';
          }
        }

        this.setDefaultTime();
      },
      immediate: true,
    },
    // 多目设备检测
    'formData.isMulti': {
      handler(val) {
        if (val === 0) {
          this.formData.multiType = null;
        }
      },
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
      algorithmVendorData: 'algorithm/getAlgorithmList',
      // ocrCheckModelList: 'algorithm/ivdg_image_ods_check_model',
    }),
    isView() {
      return this.formModel === 'view';
    },
    getWidth() {
      const customLabelWidthIndex = ['FACE_ASSET_REGISTER', ...this.historyComparisonConfigModule];
      return customLabelWidthIndex.includes(this.indexType) ? 200 : 155;
    },
  },
  // async created() {
  //   if (this.ocrCheckModelList.length == 0) await this.getAlldicData()
  // },
  methods: {
    // ...mapActions({
    //   getAlldicData: 'algorithm/getAlldicData',
    // }),
    isUpdatePhyStatus(indexType) {
      return isUpdatePhyStatus.filter((item) => item.indexType === indexType);
    },
    quantityQuery(data) {
      this.formData.quantityConfig = data;
    },
    async getCheckRule() {
      try {
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: 'BASIC_ACCURACY' },
        });
        let rule = data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.formData.deviceDetection.ruleList = rule;
        if (this.indexType == 'FACE_ACCURACY') {
          this.formData.ruleList = rule;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    toAdd() {
      this.formData.dateRang.push({ hourStart: null, hourEnd: null });
    },
    // 删除时间区间
    toDel(index) {
      this.formData.dateRang.splice(index, 1);
    },
    getDetect(val) {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
      this.detect = val;
    },
    validateForm() {
      let flag = false;
      if (!this.formData.ruleList || !this.formData.ruleList.length) flag = true;
      this.formData.ruleList &&
        this.formData.ruleList.map((item) => {
          if (item.isConfigure == 1) flag = true;
        });
      return flag;
    },
    // 表单提交校验
    async handleSubmit() {
      if (!this.validateForm()) {
        this.$Message.error('请选择检测规则');
        return;
      }

      this.handleParams();
      const valid = await this.$refs['modalData'].validate();
      return valid && this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    setDefaultTime() {
      /**
       * http://192.168.1.111:8088/zentao/task-view-2279.html
       人脸卡口设备及时上传率  默认改为  不填、60分
       重点人脸卡口设备及时上传率  默认改为  不填、30分
       人脸卡口设备时钟准确率  30s
       *
       */
      if (this.moduleAction.indexType === 'FACE_FOCUS_UPLOAD') {
        if (!this.formData.timeDelay && !this.formData.timeFormat) {
          this.formData.timeDelay = 30;
          this.formData.timeFormat = 'm';
        }
        if (!this.formData.beforeTimeDelay && !this.formData.beforeTimeFormat) {
          this.formData.beforeTimeDelay = null;
          this.formData.beforeTimeFormat = 's';
        }
      }

      if (this.moduleAction.indexType === 'FACE_UPLOAD') {
        if (!this.formData.timeDelay && !this.formData.timeFormat) {
          this.formData.timeDelay = 60;
          this.formData.timeFormat = 'm';
        }
        if (!this.formData.beforeTimeDelay && !this.formData.beforeTimeFormat) {
          this.formData.beforeTimeDelay = null;
          this.formData.beforeTimeFormat = 's';
        }
      }
      if (this.moduleAction.indexType === 'FACE_CLOCK' && !this.formData.timeDelay && !this.formData.timeFormat) {
        this.formData.timeDelay = 0;
        this.formData.timeFormat = 's';
      }
      if (this.indexType === 'FACE_PLATFORM_ONLINE_RATE' && !this.formData.timeDelay) {
        this.formData.timeDelay = '1';
        this.formData.dateRang = [{ hourStart: 0, hourEnd: 24 }];
      }
      this.orgList = [];
      if (this.filterCondition.includes(this.indexType)) {
        this.formData.deviceQueryForm.dayByFilterOnline = this.formData.deviceQueryForm.dayByFilterOnline || null;
        this.formData.deviceQueryForm.countByFilterOnline = this.formData.deviceQueryForm.countByFilterOnline || null;
      }
    },
    //设置默认值
    setDefaultEmphasisData(val) {
      if (val === 'deviceGather') {
        if (
          this.moduleAction.indexType === 'FACE_EMPHASIS_LOCATION' &&
          (!this.formData.emphasisData || this.formData.emphasisData.length === 0)
        ) {
          this.formData.emphasisData = defaultEmphasisData;
        }
      } else {
        this.formData.emphasisData = [];
      }
    },
    handleClose(data, item, index) {
      this.$Modal.confirm({
        title: '警告',
        content: `您要删除${item.value}，是否确认?`,
        onOk: () => {
          data.splice(index, 1);
        },
      });
    },
    async clickAdd(type) {
      this.defaultCheckedList = [];
      try {
        this.defaultCheckedList = (this.formData.emphasisData || []).map((item) => item.key);
        this.customizeAction = {
          title: `新增${type}`,
          leftContent: `所有${type}`,
          rightContent: `已选择${type}`,
          moduleStyle: {
            width: '80%',
          },
        };
        this.customSearch = true;
        let params = {
          tagType: '2',
          isPage: false,
        };
        let {
          data: { data },
        } = await this.$http.post(taganalysis.getDeviceTag, params);
        this.allDeviceFileList = data;
      } catch (error) {
        console.log(error);
      }
    },
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        let data = this.formData.emphasisData || [];
        this.checkedTreeData = data.map((item) => item.key);
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data, dataWithName) {
      this.formData.emphasisData = dataWithName;
    },
    confirmFilter(data) {
      this.customSearch = false;
      let list = [];
      data.map((item) => {
        list.push({
          key: item.tagId,
          value: item.tagName,
        });
      });
      this.formData.emphasisData = list;
    },
    //处理参数
    handleParams() {
      if (this.formData.snap == 0) {
        this.formData.ocrModel = '';
        this.formData.ocrModel2 = '';
      }
      //时间范围自定义则清空dateRang，其他清空lastHours。后端逻辑优先取dateRang字段
      if (this.formData.timeDelay != 6) {
        this.formData.lastHours = null;
      } else {
        this.formData.dateRang = [{ hourStart: null, hourEnd: null }];
      }
    },
  },
  components: {
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
    quantityConfig: require('@/components/quantity-config/index').default,
    CommonForm: require('./common-form/index').default,
    AreaSelect: require('@/components/area-select').default,
    CustomizeFilter: require('@/components/customize-filter').default,
    UiTag: require('@/components/ui-tag').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .lastHours-input .ivu-input-number {
    @{_deep}.ivu-input-number-handler-wrap {
      border-left: 1px solid #10457e;
      border-bottom: 1px solid #10457e;
      a {
        background: #02162b;
        color: var(--color-primary);
        .ivu-input-number-handler-down-inner,
        .ivu-input-number-handler-up-inner {
          color: var(--color-primary);
        }
      }
      .ivu-input-number-handler-down,
      .ivu-input-number-handler-up {
        border-color: #10457e;
      }
    }
  }
}
.capture-vehicle {
  margin-left: 40px;
}
.form-content {
  /deep/.width-input {
    width: 468px;
    .ivu-input-group-append {
      background-color: #02162b;
      border: 1px solid #10457e;
      border-left: none;
    }
  }
  /deep/.ivu-form-item {
    .ivu-form-item-content {
      .ivu-checkbox-group {
        display: flex;
        // align-items: center;
        flex-direction: column;
        .ivu-checkbox-wrapper {
          display: flex;
          align-items: center;
          > span {
            margin-right: 10px;
          }
        }
      }
    }
  }
  .input-width {
    width: 380px;
  }
  .params-pre {
    display: inline-block;
    float: left;
    width: 40px;
    height: 34px;
    line-height: 34px;
    font-size: 16px;
    color: var(--color-primary);
    text-align: center;
    border: 1px solid var(--border-input);
    opacity: 1;
    border-radius: 4px;
    margin-right: 10px;
  }

  .width-addition {
    width: 60px;
  }
  .width-time {
    width: 140px;
  }
}
/deep/.select-width,
.input-width {
  width: 380px;
}
.w240 {
  width: 240px;
}
.w292 {
  width: 292px;
}
.w390 {
  width: 390px;
}
.params-pre {
  display: inline-block;
  float: left;
  width: 40px;
  height: 34px;
  line-height: 34px;
  font-size: 16px;
  color: var(--color-primary);
  text-align: center;
  border: 1px solid var(--border-input);
  opacity: 1;
  border-radius: 4px;
  margin-right: 10px;
}
.label-color {
  color: #e44f22;
}
.color-white {
  color: #fff;
}
.color-bule {
  color: #1b82d2 !important;
}
.width-picker {
  width: 174px;
}
.form-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}
.assessTime {
  /deep/.ivu-form-item-label {
    &::before {
      content: '*';
      display: inline-block;
      margin-right: 0.020833rem;
      line-height: 1;
      font-family: SimSun;
      font-size: 0.072917rem;
      color: #ed4014;
    }
  }
}
.inspection {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}

.notCheck {
  color: #56789c;
}
.one-time /deep/ .ivu-form-item-error-tip {
  width: 155px;
}
.lastHours-input .ivu-input-number {
  width: 100px;
  @{_deep}.ivu-input-number-handler-wrap {
    display: inline-block;
  }
}
</style>
