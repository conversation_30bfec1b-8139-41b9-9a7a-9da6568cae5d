<template>
  <ui-modal v-model="visible" title="解析详情" :r-width="1440" footer-hide>
    <div class="video-file-detail-box">
      <div class="modal-head-box">
        <div class="left-box">
          <RadioGroup
            v-model="params.isAlarm"
            type="button"
            @on-change="radioChange"
          >
            <Radio
              v-for="(item, index) in radioList"
              :key="index"
              :label="item.key"
              >{{ item.value }}</Radio
            >
          </RadioGroup>
        </div>
        <div class="right-box">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="detail-photo-list-box">
        <div
          class="list-item"
          v-for="(item, index) in photoList"
          :key="item.frameId"
          @click="clickPhotoDetail(item)"
        >
          <ResultCard
            :class="{ 'alarm-card': item.isAlarm == 1 }"
            :data="{ ...item, resourceInfo: { resourceName } }"
            imageView
          ></ResultCard>
        </div>
        <ui-empty v-if="total === 0"></ui-empty>
        <div
          class="loading-box"
          :class="{ 'loading-box-position': isObserver }"
          ref="observerBox"
        >
          <ui-loading v-if="scrollLoading"></ui-loading>
          <div
            class="last-page"
            v-if="!isObserver && params.pageNumber > 1 && total != 0"
          >
            没有更多内容
          </div>
        </div>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { getResourceFrameList } from "@/api/semantic-placement.js";
import ResultCard from "@/views/multimodal-analysis/multimodal-analysis-lib/components/result-card.vue";
export default {
  name: "VideoFileDetail",
  components: { ResultCard },
  props: {},
  data() {
    return {
      visible: false,
      photoList: [],
      resourceName: "",
      radioList: [
        { key: -1, value: "全部" },
        { key: 1, value: "触发报警" },
      ],
      params: {
        pageNumber: 1,
        pageSize: 20,
        sortType: "asc",
        isAlarm: -1,
        resourceId: "",
        taskId: "",
      },
      timeUpDown: false,
      observer: null,
      isObserver: false,
      scrollLoading: false,
      total: 0,
    };
  },
  mounted() {
    this.initObserver();
  },
  methods: {
    show({ taskId, resourceId, resourceName }) {
      this.photoList = [];
      this.resertParam();
      this.params.taskId = taskId;
      this.params.resourceId = resourceId;
      this.resourceName = resourceName;
      this.visible = true;
      this.getDataList();
    },
    initObserver() {
      const that = this;
      this.observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && that.isObserver) {
            this.getDataList(that.params.pageNumber + 1);
          }
        },
        {
          rootMargin: "0px",
          threshold: 0.1,
        }
      );

      // 在 nextTick 中确保 DOM 已渲染
      this.$nextTick(() => {
        const loadingRef = this.$refs.observerBox;
        if (loadingRef) {
          this.observer.observe(loadingRef);
        }
      });
    },
    getDataList(page = 1) {
      this.scrollLoading = true;
      this.params.pageNumber = page;
      const finalParam = {
        ...this.params,
        isAlarm: this.params.isAlarm == -1 ? null : this.params.isAlarm,
      };
      getResourceFrameList(finalParam)
        .then((res) => {
          const { entities = [] } = res.data;
          this.photoList.push(...entities);
          this.isObserver = !(entities?.length < this.params.pageSize);
          this.total = this.photoList.length;
        })
        .finally(() => {
          this.scrollLoading = false;
        });
    },
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.photoList = [];
      this.getDataList(1);
    },
    radioChange() {
      this.photoList = [];
      this.getDataList(1);
    },
    resertParam() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
        sortType: "asc",
        isAlarm: -1,
        resourceId: "",
        taskId: "",
      };
    },
  },
  beforeDestroy() {
    this.observer?.disconnect();
  },
};
</script>

<style lang="less" scoped>
.video-file-detail-box {
  width: 100%;
  height: 600px;

  .modal-head-box {
    display: flex;
    justify-content: space-between;
    padding: 5px 20px 5px 0;
    .right-box {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);

      .flex-center {
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      .rotate {
        transform: rotate(180deg);
      }
    }
  }
  .detail-photo-list-box {
    width: 100%;
    overflow-y: scroll;
    height: calc(~"100% - 45px");
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-content: start;
    position: relative;
    .list-item {
      width: 339px;

      .alarm-card {
        border: 1px solid #ed4014;
      }
    }
    .loading-box {
      width: 100%;
      height: 80px;
      &.loading-box-position {
        position: relative;
      }
      .last-page {
        margin: o auto;
        text-align: center;
        font-size: 20px;
      }
    }
  }
}
/deep/ .ivu-radio {
  margin-right: 0 !important;
}
/deep/ .ivu-radio-wrapper-checked {
  background: rgba(44, 134, 248, 0.1) !important;
}
</style>
