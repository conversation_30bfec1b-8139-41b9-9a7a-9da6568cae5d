<template>
  <section class="search-pictures">
    <div class="header">
      <h3 class="title">以图搜图</h3>
      <Icon type="ios-close" @click="cancelHandle" />
    </div>
    <div class="body">
      <ui-upload-img
        ref="uploadImg"
        :algorithmType="picData.algorithmType"
        :value="picData.urlList"
        :size="size"
        @imgUrlChange="imgUrlChange"
      ></ui-upload-img>
    </div>
    <div class="footer">
      <div class="slider-content">
        <span>相似度</span>
        <Slider v-model="picData.similarity"></Slider>
        <span>{{ picData.similarity }}%</span>
      </div>
      <div class="btn-group">
        <Button type="default" @click="resetHandle">清空</Button>
        <Button type="primary" @click="confirmHandle">搜索</Button>
      </div>
    </div>
    <image-recognition
      ref="imageRecognition"
      :dataCopper="dataCopper"
      :tempUrl="tempUrl"
      @getMinImgUrl="getMinImgUrl"
      v-if="imageRecognitionVisible"
    />
  </section>
</template>
<script>
import dataCopper from "./test";
import imageRecognition from "./image-recognition";
import uiUploadImg from "@/components/ui-upload-img/index";
import { picturePick } from "@/api/wisdom-cloud-search";

export default {
  name: "SearchPictures",
  components: { imageRecognition, uiUploadImg },
  props: {
    // 关闭
    close: {
      type: Function,
      default: () => {},
    },
    size: {
      type: String,
      default: "",
    },
    picData: {
      type: Object,
      default: () => {
        return {
          similarity: 80,
          algorithmType: "1",
          urlList: ["", "", "", "", ""],
        };
      },
    },
  },
  data() {
    return {
      fileData: null,
      similarity: 0,
      algorithmType: "",
      urlList: [],
      dataCopper: [],
      tempUrl: "",
      imageRecognitionVisible: false,
      selectedUploadIndex: null, //选中的uploadIndex
    };
  },
  mounted() {
    this.similarity = this.picData.similarity || 80;
    this.algorithmType = this.picData.algorithmType || "1";
    this.urlList = this.picData.urlList || ["", "", "", "", ""];
  },
  methods: {
    confirmHandle() {
      const { similarity, algorithmType, urlList } = this;
      const params = {
        similarity: this.picData.similarity,
        algorithmType: this.algorithmType,
        urlList: this.urlList,
      };
      // console.log('---------', params)
      this.$emit("search", params);
    },
    cancelHandle() {
      this.$emit("cancel");
    },
    // 清空图片
    resetHandle() {
      this.urlList = this.urlList.map((d) => "");
      this.$forceUpdate();
      this.imgUrlChange(this.urlList);
    },
    //删除单个图片
    minImgDeleteHandle(index) {
      this.$set(this.urlList, index, "");
      this.$forceUpdate();
      this.imgUrlChange();
    },
    // 获取
    getMinImgUrl(url) {
      this.$set(this.urlList, this.selectedUploadIndex, url);
      this.$forceUpdate();
      this.imgUrlChange();
    },
    imgUrlChange(urlList) {
      this.$emit("imgUrlChange", this.urlList, urlList);
    },
    //上传图片
    beforeUpload(file, index) {
      this.selectedUploadIndex = index;
      let isAskFile = false;
      isAskFile = /\.(PNG|JPG|JPEG|BMP|png|jpg|jpeg|bmp)$/.test(file.name);
      if (!isAskFile) {
        this.$Message.error("请上传png、jpg、jpeg或bmp格式图片！");
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 30MB!");
      }
      if (!isAskFile || !isLt30M) return false;
      // this.fileUploading = this.$Message.loading({ content: '文件上传中...', duration: 0 })
      if (!this.fileData) {
        this.fileData = new FormData();
      }
      this.fileData.append("files", file);
      // fileData.append('algorithmType', 1)
      // picturePick(fileData)
      //   .then(res => {
      //     this.imageRecognition()
      //     this.dataCopper = dataCopper
      //     this.tempUrl = window.URL.createObjectURL(file)
      //   })
      //   .finally(() => {
      //     this.fileUploading()
      //     this.fileUploading = null
      //   })
      return false;
    },
    // 打开图片识别器
    imageRecognition(data) {
      this.imageRecognitionVisible = true;
      this.$nextTick(() => {
        this.$refs.imageRecognition.init(data);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.search-pictures {
  width: 572px;
  .upload-list {
    height: 140px;
  }
  &.small-pictures {
    width: 700px;
    .upload-list {
      height: 140px;
    }
  }
  .header {
    color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    padding-bottom: 11px;
    padding-left: 20px;
    padding-right: 5px;
    border-bottom: 1px solid #d3d7de;
    padding-top: 5px;
    position: relative;
    .title {
      font: 600 18px MicrosoftYaHei-Bold, MicrosoftYaHei;
      margin-right: 80px;
      color: rgba(0, 0, 0, 0.9);
    }
    .radio-group {
      flex: 1;
      .ivu-radio-group-item {
        margin-right: 40px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .ivu-icon-ios-close {
      font-size: 32px;
      color: #888;
      position: absolute;
      top: 50%;
      margin-top: -18px;
      right: 5px;
      cursor: pointer;
      &:hover {
        color: #666;
      }
    }
  }
  .body {
    padding: 20px;
    /deep/ .upload-list {
      height: 90px;
      .ivu-upload {
        width: 90px;
        height: 90px;
      }
      .img-content {
        width: 90px;
        height: 90px;
      }
    }
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 15px 20px;
    /deep/ .ivu-slider {
      width: 250px;
    }
  }
}
</style>
