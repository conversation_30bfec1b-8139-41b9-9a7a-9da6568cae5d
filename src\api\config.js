import request from "@/libs/request";
import { service, manager, icbdService, irasService } from "./Microservice";

// 根据系统参数值获取系统参数数据 - 多参数
export function queryParamDataByKeys(data) {
  return request({
    url: service + "/system/param/queryParamDataByKeys",
    method: "post",
    data,
  });
}
// 查询出工作台数据
export function pageList(data) {
  return request({
    url: service + "/dictData/pageList",
    method: "post",
    data,
  });
}
// 新增字典值
export function dictDataBatchAdd(data) {
  return request({
    url: service + "/dictData/batchAdd",
    method: "post",
    data,
  });
}
// 编辑字典值
export function dictDataUpdate(data) {
  return request({
    url: service + "/dictData/update",
    method: "post",
    data,
  });
}
// 删除字典值
/**
 * @param 字典ID ids 可以批量id逗号分隔
 * @returns
 */
export function dictDataDelete(ids) {
  return request({
    url: service + "/dictData/remove/" + ids,
    method: "post",
  });
}
// 根据系统参数值获取系统参数数据
export function getParamDataByKeys(key) {
  return request({
    url: service + "/system/param/getParamDataByKeys?key=" + key,
    method: "get",
  });
}
// 系统参数编辑
export function paramUpdate(data) {
  return request({
    url: service + "/system/param/update",
    method: "post",
    data,
  });
}

// 数据表查询
export function queryTableList(data) {
  return request({
    url: manager + "/dw/queryTables",
    method: "post",
    data,
  });
}

// DS数据表查询
export function queryDSTableList(data) {
  return request({
    url: manager + "/dw/queryDSList",
    method: "post",
    data,
  });
}

// 电视墙新增
export function videoWallAdd(data) {
  return request({
    url: icbdService + "/videoWall/add",
    method: "post",
    data,
  });
}
// 查询电视墙分页列表
export function videoWallPageList(data) {
  return request({
    url: icbdService + "/videoWall/pageList",
    method: "post",
    data,
  });
}
// 电视墙授权
export function permission(data) {
  return request({
    url: icbdService + "/videoWall/permission",
    method: "post",
    data,
  });
}
// 电视墙授权用户
export function permissionList(id) {
  return request({
    url: icbdService + `/videoWall/permission/${id}`,
    method: "get",
  });
}
// 电视墙删除
export function removeVideoWell(ids) {
  return request({
    url: icbdService + `/videoWall/remove/${ids}`,
    method: "delete",
  });
}
// 电视墙编辑
export function videoWallUpdate(data) {
  return request({
    url: icbdService + "/videoWall/update",
    method: "put",
    data,
  });
}
// 解码器新增
export function demodifierAdd(data) {
  return request({
    url: icbdService + "/demodifier/add",
    method: "post",
    data,
  });
}
// 解码器编辑
export function demodifierUpdate(data) {
  return request({
    url: icbdService + "/demodifier/update",
    method: "put",
    data,
  });
}
// 查询解码器分页列表
export function demodifierPageList(data) {
  return request({
    url: icbdService + "/demodifier/pageList",
    method: "post",
    data,
  });
}
// 获取监视器信息
export function demodifierScreen(data) {
  return request({
    url: icbdService + "/demodifier/screen",
    method: "put",
    data,
  });
}
//解码器删除
export function demodifierRemove(ids) {
  return request({
    url: icbdService + `/demodifier/remove/${ids}`,
    method: "delete",
  });
}
//获取解码器详细信息
export function demodifierView(id) {
  return request({
    url: icbdService + `/demodifier/view/${id}`,
    method: "get",
  });
}
// 获取监视器布局详情
export function getDemodifier(data) {
  return request({
    url: icbdService + "/demodifier/getDemodifier",
    method: "post",
    data,
  });
}
// 更新显示器位置
export function updateLocation(data) {
  return request({
    url: icbdService + "/videoWall/updateLocation",
    method: "post",
    data,
  });
}
// 系统配置所属图谱查询
export function queryGraph(data) {
  return request({
    url: irasService + "/manage/schema/graph/list",
    method: "post",
    data,
  });
}

// 资源屏蔽管理
// 获取资源屏蔽分组集合
export function getShieldingGroup(data) {
  return request({
    url: service + "/shielding/getShieldingGroup",
    method: "post",
    data,
  });
}
// 删除资源屏蔽任务
export function deleteShieldingTask(groupId) {
  return request({
    url: service + `/shielding/deleteShieldingTask/${groupId}`,
    method: "get",
  });
}
// 删除屏蔽的资源
export function deleteShieldingResource(data) {
  return request({
    url: service + "/shielding/deleteShieldingResource",
    method: "post",
    data,
  });
}
// 根据分组ID获取屏蔽设备的详细信息
export function getViewResource(data) {
  return request({
    url: service + "/shielding/getShieldingData",
    method: "post",
    data,
  });
}
// 保存/修改资源屏蔽任务/白名单/屏蔽的资源信息
export function createSheildTask(data) {
  return request({
    url: service + "/shielding/saveOrUpdateShieldingTask",
    method: "post",
    data,
  });
}
// 获取当前用户的屏蔽资源池
export function getShieldResourcePool() {
  return request({
    url: service + `/shielding/getShieldResourcePool`,
    method: "get",
  });
}
// 区域管理
// 区域管理控制器-新增
export function addRegionalScope(data) {
  return request({
    url: manager + "/regionalScopeManagement/add",
    method: "post",
    data,
  });
}
// 区域管理控制器-分页查询
export function regionalScopeList(data) {
  return request({
    url: manager + "/regionalScopeManagement/pageList",
    method: "post",
    data,
  });
}
// 区域管理控制器-删除
export function delRegionalScope(ids) {
  return request({
    url: manager + `/regionalScopeManagement/remove/${ids}`,
    method: "delete",
  });
}
// 区域管理控制器-更新
export function updateRegionalScope(data) {
  return request({
    url: manager + "/regionalScopeManagement/update",
    method: "put",
    data,
  });
}
// 区域管理控制器-详情
export function viewRegionalScope(ids) {
  return request({
    url: manager + `/regionalScopeManagement/view/${ids}`,
    method: "get",
  });
}

// 文件上传
export function fileUpload(data) {
  return request({
    url: service + "/file/upload",
    method: "post",
    data,
  });
}
