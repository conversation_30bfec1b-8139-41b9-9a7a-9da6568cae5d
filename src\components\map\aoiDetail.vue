<template>
  <div class="dom-wrapper">
    <div class="dom">
      <div class="content">
        <div class="header">
          <div class="header-left">场所详情</div>
          <div class="header-right">
            <ui-icon
              title="周边设备"
              type="video-camera"
              color="#2C86F8"
              @click.native="aroundDevice"
            ></ui-icon>
            <ui-icon
              title="场所档案"
              type="dangan2"
              color="#2C86F8"
              @click.native="viewArchive"
            ></ui-icon>
            <ui-icon type="close" @click.native="close"></ui-icon>
          </div>
        </div>
        <div class="info-wrapper">
          <div class="info-left">
            <ui-image :src="placeDetail.image" alt="静态库" viewer />
          </div>
          <div class="info-right">
            <div
              v-for="(label, key) in placeInfoItems"
              :key="key"
              class="ellipsis"
              :title="placeDetail[key]"
            >
              <span class="label">{{ label }}：</span>
              <span class="value">{{ placeDetail[key] }}</span>
            </div>
            <ui-tag-poptip
              v-if="placeDetail.labels && placeDetail.labels.length"
              :data="placeDetail.labels"
            />
          </div>
        </div>
      </div>
      <div class="hover-bottom"></div>
    </div>
  </div>
</template>
<script>
import object from "element-resize-detector/src/detection-strategy/object";
import { placeDetail, getPlaceDeviceInfo } from "@/api/operationsOnTheMap.js";
export default {
  components: {},
  props: {
    placeInfo: {
      type: object,
      default: {},
    },
  },
  computed: {},
  data() {
    return {
      placeDetail: {},
      placeInfoItems: {
        name: "场所名称",
        area: "场所面积",
        firstLevelName: "一级分类",
        secondLevelName: "二级分类",
        adname: "所属区县",
        townname: "所属街道",
        address: "场所地址",
      },
    };
  },
  watch: {
    placeInfo(val) {
      this.getPlaceDetail();
    },
    immediate: true,
  },
  methods: {
    getPlaceDetail() {
      this.placeDetail = {};
      placeDetail(this.placeInfo.id).then((res) => {
        this.placeDetail = res.data;
      });
    },
    close() {
      this.$emit("closeplaceInfoWindow");
    },

    /**
     * @description: 周边设备
     */
    aroundDevice() {
      getPlaceDeviceInfo(this.placeInfo.id).then((res) => {
        if (res.code == 200) {
          let deviceList = res.data.map((val) => {
            let item = {};
            item.deviceGbId = val.id;
            item.deviceName = val.deviceName;
            item.deviceType = val.deviceType;
            item.deviceChildType = val.deviceChildType;
            item.sbgnlx = val.monitorPointType;
            item.isOnline = val.isOnline;
            item.longitude = val.longitude;
            item.latitude = val.latitude;
            item.deviceId = val.deviceId;
            item.isCommunity = val.socialResources;
            item.geoPoint = val.geoPoint;
            item.mapType = item.deviceType;
            item.ptzType = item.deviceChildType
              ? item.deviceChildType
              : item.ptzType;
            return item;
          });
          this.$emit("openFrameModal", deviceList);
        }
      });
    },

    /**
     * @description: 查看设备档案
     * @param {string} deviceId 设备id
     */
    viewArchive() {
      const { href } = this.$router.resolve({
        name: "place-dashboard",
        query: { archiveNo: this.placeInfo.id, source: "place" },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
  //   padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}
.dom {
  width: 532px;
  height: 412px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: end;
  .hover-content {
    background: #fff;
    padding: 5px;
    border-radius: 5px;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    display: inline-block;
  }
  .hover-bottom {
    background: #fff;
  }
  .hover-bottom:before,
  .hover-bottom:after {
    content: "";
    display: block;
    border-width: 8px;
    position: absolute;
    bottom: -16px;
    left: 266px;
    border-style: solid dashed dashed;
    border-color: #ffffff transparent transparent;
    font-size: 0;
    line-height: 0;
  }
}
.header {
  background: rgba(211, 215, 222, 0.3);
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  padding: 10px;
  .header-left {
    color: rgba(0, 0, 0, 0.9);
    font-weight: 700;
  }
  .header-right {
    i {
      margin-left: 18px;
    }
  }
}

// #region
.info-wrapper {
  width: 530px;
  padding: 20px;
  display: flex;
  gap: 20px;
  .info-right {
    width: calc(~"100% - 210px");
    display: flex;
    flex-wrap: wrap;
    div {
      width: 100%;
    }
    span {
      font-size: 12px;
      line-height: 20px;
    }
    .label {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.6);
    }
    .value {
      font-weight: 700;
      color: rgba(0, 0, 0, 0.8);
    }
  }
}

// #endregion

.content {
  width: 100%;
  flex-wrap: wrap;
  //   padding: 20px 20px;
  background: #fff;
  border-radius: 5px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  .ui-image {
    width: 200px;
    height: 200px;
    margin: 0 auto;
  }
}
</style>
