<template>
  <div class="tooltip-container">
    <p>{{ year }}年{{ data[0]['axisValue'] }}</p>
    <p v-for="(item, index) in data" :key="index">
      <span class="block" :style="{ 'background-color': item.color }"> </span>
      <span> {{ item.seriesName }}： </span>
      <span> {{ item.data || 0 }}% </span>
    </p>
  </div>
</template>

<script>
export default {
  name: 'gobern-tendency-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.tooltip-container {
  font-size: 14px;
  padding: 4px 10px;
  line-height: 18px;
  .block {
    display: inline-block;
    height: 6px;
    width: 6px;
    line-height: 14px;
  }
}
</style>
