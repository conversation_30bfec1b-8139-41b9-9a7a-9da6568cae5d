.ivu-layout {
  height: 100%;
  background-color: @ivu-layout-color;
  .i-layout-header {
    background: @primary-color url('~@/assets/img/nav.png') no-repeat right/contain;
    height: 55px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    border-bottom: 1px solid @ivu-head-border-color;
    box-sizing: border-box;
    z-index: 0;
    .i-layout-header-logo {
      display: flex;
      align-items: center;
      .img-logo {
        width: 40px;
        height: 40px;
        object-fit: contain;
      }
      .title {
        margin-left: 20px;
        font-size: 24px;
        font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
        font-weight: bold;
        color: @white;
        line-height: 1.5;
      }
    }
    .i-layout-menu-head {
      flex: 1;
      padding-left: 20px;
      height: 54px;
      .ivu-menu  {
        height: 100%;
        .ivu-menu-item {
          padding: 0 18px;
          color: @nav-text-color;
          display: inline-flex;
          align-items: center;
          border-bottom: none;
          .i-layout-menu-head-title {
            line-height: 54px;
            display: flex;
            // align-items: center;
          }
          .i-layout-menu-head-title-icon {
            line-height: 20px;
            &>i {
              font-size: 20px;
            }
            margin-right: 10px;
          }
          .i-layout-menu-head-title-text {
            line-height: 20px;
          }
        }
        .ivu-menu-item-active, .ivu-menu-item:hover {
          color: @nav-text-active-color;
          background-color: @nav-text-active-bg-color;
          .i-layout-menu-head-title-text {
            // font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
            font-weight: bold;
          }
        }
        .ivu-menu-item-active {
          box-shadow: inset 0px -3px 0px 0px @nav-text-active-color;
        }
      }
      .ivu-menu-light {
        background-color: transparent;
      }
      .ivu-menu-light::after {
        height: 0;
      }
    }
    .i-layout-header-trigger {
      align-items: center;
      .i-layout-header-user {
        height: 100%;

        cursor: pointer;
        .ivu-dropdown-rel {
          height: 100%;
          display: flex;
          align-items: center;
        }
        .i-layout-header-user-name {
          color: @white;
          margin-left: 10px;
          line-height: 1.5;
        }
      }
    }
    .ivu-divider {
      height: 24px;
      margin: 0 24px;
      background-color: @head-user-line-color;
    }
  }
  .icon-pifu{
    cursor: pointer;
    margin-right: 20px;
  }
  .i-layout-header-fix {
    position: fixed;
    top: 0;
    left: 0;
  }
  .ivu-layout-sider {
    background: @second-menu-bg-color;
    transition: none;
    .i-layout-sider-logo  {
      height: 54px;
    }
    .ivu-menu {
      background: @second-menu-bg-color;
      .ivu-dropdown {
        width: 100%;
        .ivu-dropdown-menu {
          .ivu-dropdown-item:hover {
            background: fade(@primary-color, 20%);
          }
        }
      }
      .ivu-menu-item, .ivu-dropdown-item {
        border-bottom: 1px solid @second-menu-item-border-color;
        height: 70px;
        padding: 0 12px;
        .i-layout-menu-side-title {
          height: auto;
          width: 100%;
          color: @second-menu-text-color;
          .i-layout-menu-side-title-icon {
            font-size: 16px;
            line-height: 16px;
          }
        }
      }
      .ivu-menu-item {
        display: flex;
        justify-content: center;
        align-items: center;
        .i-layout-menu-side-title {
          display: flex;
          flex-direction: column;
          align-items: center;
          .i-layout-menu-side-title-text {
            line-height: 20px;
            margin-top: 6px;
          }
        }
      }
      .ivu-dropdown-item ,.ivu-menu-item{
        display: flex;
        justify-content: center;
        align-items: center;
        .i-layout-menu-side-title {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100%;
        }
        .i-layout-menu-side-title-text {
          line-height: 20px;
          margin-top: 6px;
          white-space: nowrap;
          width: 100%;
          text-overflow: ellipsis;
          overflow: hidden;
          text-align: center;
        }
      }
      .ivu-select-dropdown {
        width: 160px;
        .i-layout-menu-side-title-text {
          margin-top: 0 !important;
        }
      }
      .ivu-dropdown-item::after {
        content: '';
        position: absolute;
        right: 0;
        bottom: 0;
        width: 0;
        height: 0;
        border-bottom: 8px solid @second-menu-text-color;;
        border-left: 8px solid transparent;
      }
      .ivu-menu-item:hover {
        background: @second-menu-item-hover-color;
        .i-layout-menu-side-title {
          color: @second-menu-text-hover-color;
        }
      }
      .ivu-dropdown-item:hover::after {
        border-bottom: 8px solid @second-menu-text-hover-color;
      }
      .ivu-menu-item-active, .ivu-dropdown-item-selected, .ivu-menu-item-active:hover, .ivu-dropdown-item-selected:hover {
        background: @second-menu-item-active-color !important;
        .i-layout-menu-side-title {
          color: @second-menu-text-active-color;
        }
      }
      .ivu-dropdown-item:hover {
        background: fade(@white, 10);
      }
      .ivu-dropdown-item-selected::after, .ivu-dropdown-item-selected:hover::after {
        border-bottom: 8px solid @second-menu-text-active-color;
      }
    }
    .slider_collapse {
      position: absolute;
      bottom: 10px;
      left: 0;
      width: 50px;
      height: 25px;
      background: #2D86F6;
      border-top-right-radius: 12.5px;
      border-bottom-right-radius: 12.5px;
      color: @white;
      cursor: pointer;
      z-index: 999;
      .i-layout-header-trigger {
        width: 50px;
        display: inline-block;
        padding-left: 12px;
        box-sizing: border-box;
      }
      i {
        font-size: 16px;
      }
    }
  }
  .ivu-layout-content {
    padding-top: 54px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
    .i-layout-tabs {
      width: 100% !important;
      height: 36px;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      background: @nav-tabs-bg-color;
      .i-layout-tabs-main {
        height: 100%;
        .ivu-tabs  {
          height: 100%;
          .ivu-tabs-bar, .ivu-tabs-nav-container, .ivu-tabs-nav-wrap, .ivu-tabs-nav-scroll, .ivu-tabs-nav, .ivu-tabs-tab {
            height: 36px;
            margin: 0;
          }
          .ivu-tabs-tab {
            transition: none;
            border: none;
            border-radius: 0;
            padding: 0 16px;
            background: transparent;
            display: inline-flex;
            align-items: center;
            // border-bottom: 3px solid transparent;
          }
          .i-layout-tabs-title {
            line-height: 20px;
            color: @nav-tabs-item-text-color;
          }
          .ivu-tabs-close {
            width: 20px;
            height: 20px;
            font-size: 20px;
            line-height: 20px;
            margin-right: 0 !important;
            margin-left: 6px;
            color: @nav-tabs-item-close-color;
          }
          .ivu-tabs-tab-active,.ivu-tabs-tab:hover {
            background: @nav-tabs-item-active-bg-color;
            .i-layout-tabs-title, .ivu-tabs-close {
              color: @primary-color;
            }
          }
          .ivu-tabs-tab-active {
            box-shadow: inset 0px -3px 0px 0px @primary-color;
          }
        }
      }
    }
    .i-layout-content-main {
      display: flex;
      flex: 1;
      padding: 10px;
      box-sizing: border-box;
      overflow-y: hidden;
      &.no-padding{
        padding: 0;
      }
    }
    &.i-layout-content-nomenu {
      padding-top: 0;
    }
  }
}
