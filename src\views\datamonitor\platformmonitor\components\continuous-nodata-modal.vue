<template>
  <ui-modal v-model="visible" :styles="styles" footer-hide title="持续无数据时间">
    <div>
      <div class="end-time-box">统计截至 {{ modalRowData?.firstIntoViewTimeText || '-' }}</div>
      <icon-statics :icon-list="iconStaticsList" class="mb-lg"></icon-statics>
      <draw-echarts
        :echart-option="echartOption"
        :echart-style="echartStyle"
        :echarts-loading="echartsLoading"
        @echartClick="echartClick"
      ></draw-echarts>
      <day-capture-modal v-model="showDayCapVisible" v-bind="dayCaptureAttr"> </day-capture-modal>
    </div>
  </ui-modal>
</template>
<script>
import datamonitorApi from '@/config/api/datamonitor';

export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    modalRowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    echartsFactory: {
      type: Object,
      default: () => {
        return {};
      },
    },
    commonSearchData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      styles: {
        width: '5.8rem',
      },
      echartStyle: {
        width: '100%',
        height: '350px',
      },
      visible: false,
      echartsLoading: false,
      echartOption: {},
      echartsData: [],
      iconStaticsList: [
        {
          name: '持续无数据时间',
          count: '0',
          countStyle: {
            color: 'var(--color-failed)',
          },
          iconName: 'icon-leijilixian',
          fileName: 'keepOffline',
        },
      ], //展示的统计信息
      showDayCapVisible: false, //展示日抓拍
      dayCaptureAttr: {
        modalRowData: {},
        commonSearchData: {},
      },
    };
  },
  computed: {},
  created() {},
  methods: {
    cancel() {
      this.visible = false;
    },
    async getEchartsData() {
      try {
        this.echartsLoading = true;
        // 获取详情
        const params = {
          type: this.commonSearchData.activeMoinitorType,
          regionCode: this.modalRowData.code,
          differenceTime: this.modalRowData.differenceTime,
        };
        let {
          data: { data },
        } = await this.$http.get(datamonitorApi.getOfflineDataDetail, { params });
        this.echartsData = data || [];
        this.echartOption = this.echartsFactory.getContinuousNoDataOption(this.echartsData);
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    getStatisticTime() {
      const index = this.iconStaticsList.findIndex((item) => item.fileName === 'keepOffline');
      if (index !== -1) {
        this.iconStaticsList[index].count = this.modalRowData.durationWithoutTime;
      }
    },
    echartClick(params) {
      if (params.componentType !== 'yAxis' || params.targetType !== 'axisLabel') {
        return;
      }
      let reg = /^(\d{4})年(\d{2})月(\d{2})日$/;
      let paramDate = params.value.includes('年') ? params.value.replace(reg, '$1-$2-$3') : params.value;
      this.dayCaptureAttr.modalRowData = {
        regionCode: this.modalRowData.code,
        showDayTime: paramDate,
      };
      this.dayCaptureAttr.commonSearchData = {
        activeMoinitorType: this.commonSearchData.activeMoinitorType,
        name: this.modalRowData.name,
      };
      this.showDayCapVisible = true;
    },
  },
  watch: {
    value: {
      async handler(val) {
        this.visible = val;
        if (val) {
          this.getStatisticTime();
          await this.getEchartsData();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    IconStatics: require('@/components/icon-statics.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    dayCaptureModal: require('./day-capture-modal.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .icon-ul {
    background-color: #0a2754;
  }
  .end-time-box {
    color: var(--color-title);
    &::before {
      background-color: var(--color-title);
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .end-time-box {
    color: var(--color-content);
    &::before {
      background-color: var(--color-title);
    }
  }
}
@{_deep} .ivu-modal-body {
  padding: 0;
  .detail-title {
    display: flex;
    justify-content: right;
    margin: 0 50px 10px 50px;
  }
  .icon-ul {
    height: 50px;
    padding: 0 50px;
  }
  .echarts {
    padding: 0 50px;
  }
}
.end-time-box {
  margin: 30px 0 26px 62px;
  position: relative;
  font-size: 16px;
  line-height: 16px;
  &::before {
    content: '';
    position: absolute;
    left: -9px;
    height: 15px;
    width: 4px;
  }
}
</style>
