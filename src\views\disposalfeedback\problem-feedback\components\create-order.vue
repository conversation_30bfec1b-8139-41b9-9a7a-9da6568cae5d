<template>
  <ui-modal v-model="visible" title="生成工单" width="750px">
    <Form ref="targetOrderObj" :model="targetOrderObj" :rules="formRules">
      <FormItem prop="orderId" label="工单编号" class="block">
        <Input type="text" class="width-lg" v-model="targetOrderObj.orderId" placeholder="系统自动生成" disabled>
        </Input>
      </FormItem>
      <FormItem prop="workOrderName" label="工单名称">
        <Input
          type="text"
          class="long-input"
          v-model="targetOrderObj.workOrderName"
          placeholder="系统自动生成"
          disabled
        >
        </Input>
      </FormItem>
      <FormItem prop="taskPlannedDate" label="截至时间">
        <DatePicker
          class="width-lg"
          type="datetime"
          v-model="targetOrderObj.taskPlannedDate"
          placeholder="请选择截止日期"
          :options="timeOptions"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, targetOrderObj, 'taskPlannedDate')"
          clearable
        >
        </DatePicker>
      </FormItem>
      <FormItem label="工单指派">
        <assign ref="assign" @updateDisabled="updateDisabled"></assign>
      </FormItem>
      <FormItem label="处理异常" class="mb-sm">
        <p class="">
          <span class="font-warning">{{ tableRowObj.causeCodesText?.join(',') || '-' }}</span>
        </p>
      </FormItem>
      <FormItem prop="taskContent" label="任务说明">
        <Input
          class="long-input"
          v-model="targetOrderObj.taskContent"
          type="textarea"
          :rows="4"
          placeholder="请输入任务说明"
        />
      </FormItem>
      <text-title label="关联信息" v-if="formContent?.length" class="mb-sm" font-size="14"></text-title>
      <p class="font-color mb-sm" v-for="item in formContent" :key="item.key">
        <label>{{ item.title }}：</label> <span>{{ item.value || '-' }}</span>
      </p>
      <detail-row label="问题详情：" :content="tableRowObj.issueDetail || '-'"></detail-row>
      <detail-row
        label="问题图片："
        v-if="tableRowObj.issueImages?.length"
        :imgList="tableRowObj.issueImages"
      ></detail-row>
    </Form>
    <template #footer>
      <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
      <Button class="plr-30" type="primary" :loading="certainLoading" @click="handleCreateOrder">生成工单</Button>
    </template>
  </ui-modal>
</template>
<script>
import feedbackApi from '@/config/api/feedback';
export default {
  components: {
    textTitle: require('./text-title.vue').default,
    assign: require('./assign.vue').default,
    DetailRow: require('./detail-row.vue').default,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    tableRowObj: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      visible: false,
      certainLoading: false, //生成工单loading
      targetOrderObj: {
        orderId: '',
        workOrderName: '', //工单名称
        taskPlannedDate: '', //截至日期
        taskContent: '', //任务说明
        receiverName: '', //接收人名称
        assignList: [], //
        assignMode: 0, //0自定义指派,1自动指派维护单位人，2组织机构人
        receiverId: '', //接收人id
      },
      timeOptions: {
        disabledDate: (date) => {
          return date.getTime() < Date.now();
        },
      },
      formRules: {
        orderId: [{ required: true, message: '请输入工单编号', trigger: 'blur' }],
        workOrderName: [{ required: true, message: '请输入工单名称', trigger: 'blur' }],
      },
    };
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          //清空工单表单对象
          this.targetOrderObj = {};
          this.$refs.assign.reset();
        }
        this.visible = val;
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('changeModalVisible', val);
    },
  },
  computed: {
    formContent() {
      if (typeof this.tableRowObj.formContent === 'string') {
        return JSON.parse(this.tableRowObj.formContent);
      } else {
        return this.tableRowObj.formContent;
      }
    },
  },
  methods: {
    //选择指派人
    updateDisabled(val) {
      this.targetOrderObj.assignList = val.assignList; //指派列表
      this.targetOrderObj.assignMode = val.assignMode; //0自定义指派,1自动指派维护单位人，2组织机构人
      this.targetOrderObj.receiverId = val.people?.username || ''; // receiverId:'',//接收人id
      this.targetOrderObj.receiverName = val.people?.name || ''; //接收人名称
    },
    // DatePicker选择时间后格式化
    changeTimeMx(formatTime, timeType, searchData, timeField) {
      this.$set(searchData, timeField, formatTime);
    },
    //点击取消
    cancel() {
      this.visible = false;
    },
    //点击生成工单按钮
    async handleCreateOrder() {
      const params = {
        feedbackId: this.tableRowObj.id,
        ...this.targetOrderObj,
      };
      this.certainLoading = true;
      try {
        let { data } = await this.$http.post(feedbackApi.saveFeedbackTask, params);
        if (data.code == 200) {
          //成功关闭弹窗，刷新列表
          this.$Message.success(data.msg);
          this.visible = false;
          this.$emit('update');
        } else {
          this.$Message.error(data.msg);
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.certainLoading = false;
      }
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20px 50px 20px 50px;
}
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
@{_deep} .ivu-form-item-label:before {
  margin-right: 0;
}
@{_deep} .ivu-form-item-error-tip {
  margin-left: 77px;
}
.mb-sm {
  margin-bottom: 10px !important;
}
.long-input {
  width: 565px;
}
.font-color {
  color: var(--color-content);
}
</style>
