<template>
  <div class="select-device">
    <ui-modal v-model="visible" :title="title" width="97.3rem">
      <div class="select-device-wrap">
        <div class="select-device-wrap-header flex">
          <div class="left">所有设备列表 <span class="red">154562</span> 条</div>
          <div class="right">
            <ui-label class="inline right-margin" label="关键词" :width="55">
              <Input v-model="searchData.keyWord" class="input-width" placeholder="请输入设备名称或编码"></Input>
            </ui-label>
            <ui-label class="inline right-margin" :label="global.filedEnum.sbdwlx" :width="100">
              <Select class="input-width" v-model="searchData.orgcode" placeholder="请选择">
                <Option
                  v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
                  :key="'sbdwlx' + bdindex"
                  :value="sbdwlxItem.dataKey"
                  >{{ sbdwlxItem.dataValue }}</Option
                >
              </Select>
            </ui-label>
            <ui-label class="inline right-margin" :label="global.filedEnum.sbgnlx" :width="70">
              <Select class="input-width" v-model="searchData.orgcode" placeholder="请选择">
                <Option
                  v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
                  :key="'sbgnlx' + bdindex"
                  :value="sbgnlxItem.dataKey"
                  >{{ sbgnlxItem.dataValue }}</Option
                >
              </Select>
            </ui-label>
            <ui-label class="inline right-margin" label="检测状态" :width="70">
              <Select class="input-width" v-model="searchData.orgcode" placeholder="请选择">
                <Option value="beijing">New York</Option>
                <Option value="shanghai">London</Option>
                <Option value="shenzhen">Sydney</Option>
              </Select>
            </ui-label>
            <div class="inline">
              <Button type="primary" class="mr-sm" @click="search(searchData)"> 查询 </Button>
              <Button type="default" @click="resetSearchDataMx(searchData, () => search(searchData))"> 重置 </Button>
            </div>
          </div>
        </div>
        <div class="select-device-wrap-content">
          <div class="select-device-wrap-content-left">
            <ui-search-tree
              placeholder="请输入组织机构名称或组织机构编码"
              node-key="id"
              :scroll="400"
              :maxHeight="600"
              :tree-data="treeData"
              :default-props="defaultProps"
              @selectTree="selectTree"
            >
            </ui-search-tree>
          </div>
          <div class="select-device-wrap-content-right justify-flex">
            <div class="left-table auto-fill">
              <ui-table
                class="ui-table auto-fill"
                :table-columns="leftTableColumns"
                :table-data="leftTableData"
                :loading="tableLoading"
                @oneSelected="leftSingleSelect"
                @cancelSelectTable="leftSingleSelectCancel"
                @onSelectAllTable="leftAllSelect"
                @cacelAllSelectTable="leftAllSelectCancel"
              ></ui-table>
              <ui-page
                class="page menu-content-background"
                :page-data="leftPageData"
                @changePage="handleLeftPage"
                @changePageSize="handleLeftPageSize"
              >
              </ui-page>
            </div>
            <div class="right-table auto-fill">
              <ui-table
                class="ui-table auto-fill"
                :table-columns="rightTableColumns"
                :table-data="rightTableData"
                :loading="tableLoading"
                @oneSelected="rightSingleSelect"
                @cancelSelectTable="rightSingleSelectCancel"
                @onSelectAllTable="rightAllSelect"
                @cacelAllSelectTable="rightAllSelectCancel"
              ></ui-table>
              <ui-page
                class="page menu-content-background"
                :page-data="rightPageData"
                @changePage="handleRightPage"
                @changePageSize="handleRightPageSize"
              >
              </ui-page>
            </div>
          </div>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import governancetask from '@/config/api/governancetask';
export default {
  props: {},
  data() {
    return {
      visible: true,
      title: '选择设备',
      selectOrgTree: {
        orgCode: null,
      },
      searchData: {
        orgCode: '000000',
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
      },
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      leftTableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
        },
        { title: '异常原因', key: 'errorReason', align: 'left' },
      ],
      rightTableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
        },
        { title: '异常原因', key: 'errorReason', align: 'left' },
      ],
      leftTableData: [],
      rightTableData: [],
      tableLoading: false,
      leftPageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      rightPageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      dictData: {},
      searchForm: {
        keyWord: '',
        // orgCode: "",
        errorTypeItem: [],
        pageNumber: 1,
        pageSize: 20,
      },
      leftCheckIds: [],
      rightCheckIds: [],
    };
  },
  created() {},
  mounted() {
    this.search();
  },
  methods: {
    init(data) {
      this.dictData = data;
      this.visible = true;
      this.search();
    },
    selectedOrgTree(val) {
      this.orgCode = val.orgCode;
    },
    search() {
      this.searchData.pageNumber = 1;
      this.initTableList();
    },
    async initTableList() {
      try {
        this.loading = true;
        const params = {
          keyWord: '',
          orgCode: this.searchData.orgCode,
          errorTypeItem: [],
          pageNumber: this.leftPageData.pageNum,
          pageSize: this.leftPageData.pageSize,
        };
        let res = await this.$http.post(governancetask.getDevicePageList, params);
        this.leftTableData = res.data.data.entities.filter((item) => {
          item._checked = this.leftCheckIds.includes(item.id);
          return !item.workOrderId;
        });
        this.leftPageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      }
    },
    selectTree(data) {
      this.searchData.orgCode = data.orgCode;
      this.initTableList();
    },
    // 左边选择
    leftSingleSelect(selection) {
      selection.map((item) => {
        if (!this.leftCheckIds.includes(item.id)) {
          this.leftCheckIds.push(item.id);
          this.rightTableData.push(item);
        }
      });
    },
    leftSingleSelectCancel(selection, row) {
      const index = this.leftCheckIds.indexOf(row.id);
      this.rightTableData.splice(index, 1);
      this.leftCheckIds.splice(index, 1);
    },
    leftAllSelect() {},
    leftAllSelectCancel() {},
    // 右边选择
    rightSingleSelect() {},
    rightSingleSelectCancel() {},
    rightAllSelect() {},
    rightAllSelectCancel() {},

    handleLeftPage(val) {
      this.leftPageData.pageNum = val;
      this.initTableList();
    },
    handleLeftPageSize(val) {
      this.leftPageData.pageNum = 1;
      this.leftPageData.pageSize = val;
      this.initTableList();
    },
    handleRightPage() {},
    handleRightPageSize() {},
  },
  watch: {},
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      // defaultSelectedOrg: "common/getDefaultSelectedOrg",
    }),
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.justify-flex {
  display: flex;
  justify-content: center;
}
.right-margin {
  margin-right: 30px;
}
.select-device {
  width: 100%;
  &-wrap {
    width: 100%;
    // border-top: 1px solid var(--border-color);
    &-header {
      padding: 20px;
      border-top: 1px solid var(--border-color);
      border-bottom: 1px solid var(--border-color);
      .left {
        width: 303px;
        font-size: 14px;
        color: #ffffff;
      }
      .right {
        flex: 1;
      }
    }
    &-content {
      width: 100%;
      height: 700px;
      display: flex;
      &-left {
        width: 300px;
        height: 100%;
        padding: 0 10px;
        border-right: 1px solid var(--border-color);
      }
      &-right {
        flex: 1;
        height: 100%;
      }
    }
  }
  .input-width {
    width: 230px;
  }
  .red {
    color: #bc3c19;
  }
  .left-table,
  .right-table {
    flex: 1;
    height: 100%;
  }
  .right-table {
    border-left: 1px solid var(--border-color);
  }
}
@{_deep} .ivu-modal-body {
  padding: 30px 0 0 !important;
}
</style>
