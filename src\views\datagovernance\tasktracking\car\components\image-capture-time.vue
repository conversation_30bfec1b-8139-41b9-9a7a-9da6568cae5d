<template>
  <carModal
    :curIndexShow="false"
    :curIndex="curIndex"
    :infoModalShow="true"
    :bigImgShow="true"
    modalTitle="图像抓拍时间准确性检测-异常数据"
    titleChild="抓拍时间异常"
    ref="carModal"
    :listObj="listObj"
  >
    <div slot="Introduction">
      车辆图像总量：
      <span class="cyan mr20">{{ dataObj.accessDataCount }}</span>
      抓拍时间异常图像：
      <span class="red">{{ dataObj.existingExceptionCount }}</span>
    </div>
  </carModal>
</template>
<script>
import carThrem from '@/config/api/car-threm';
export default {
  name: 'image-capture-time',
  props: {
    curIndex: {
      type: Number,
      default: 1, // 当前展示步骤
    },
  },
  data() {
    return {
      dataObj: {},
      listObj: {
        columns: [
          { type: 'index', width: 70, title: '序号', fixed: 'left' },
          {
            title: `${this.global.filedEnum.deviceId}`,
            key: 'deviceId',
            width: 200,
            fixed: 'left',
          },
          {
            title: `${this.global.filedEnum.deviceName}`,
            key: 'deviceName',
            width: 200,
          },
          { title: '组织机构', key: 'orgCode', width: 200 },
          {
            title: `${this.global.filedEnum.longitude}`,
            key: 'longitude',
            width: 200,
          },
          {
            title: `${this.global.filedEnum.latitude}`,
            key: 'latitude',
            width: 200,
          },
          {
            title: `${this.global.filedEnum.macAddr}`,
            slot: 'macAddr',
            width: 200,
          },
          { title: this.global.filedEnum.ipAddr, slot: 'ipAddr', width: 200 },
          {
            title: this.global.filedEnum.sbgnlx,
            slot: 'sbgnlxText',
            width: 200,
          },
          {
            title: this.global.filedEnum.sbdwlx,
            slot: 'sbdwlxText',
            width: 200,
          },
          { title: '位置类型', slot: 'positionTypeText', width: 200 },
          { title: '安装地址', key: 'address', width: 200 },
          { title: '抓拍时间异常图片数量', key: 'errorPhotoNum', width: 200 },
          {
            title: '操作',
            slot: 'action',
            width: 140,
            fixed: 'right',
          },
        ],
        key: 'entities',
        loadData: (parameter) => {
          return this.$http
            .post(
              carThrem.queryVehiclePageList,
              Object.assign(parameter, {
                reasonTypes: this.curIndex != 6 ? [this.curIndex] : [],
              }),
            )
            .then((res) => {
              // 把后台数组转为list返回
              return res.data;
            });
        },
      },
    };
  },
  mounted() {
    // this.modalShow = true
  },
  methods: {
    init(item) {
      this.$store.commit('carAndVideo/setExportType', 1);
      if (item) {
        item.list.map((val) => {
          this.$set(this.dataObj, val.fileName, val.num);
        });
      }
      this.$refs.carModal.showModal();
    },
  },
  watch: {},
  components: {
    carModal: require('./carmodal/car-modal').default,
  },
};
</script>
<style lang="less" scoped>
.blue {
  color: var(--color-primary);
}
.red {
  color: #e44f22;
}
.cyan {
  color: var(--color-bluish-green-text);
}
.mr20 {
  margin-right: 20px;
}
</style>
