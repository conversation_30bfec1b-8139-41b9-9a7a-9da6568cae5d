<!--
    * @FileDescription: 人体搜索
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
	<div class="search card-border-color" :class="{'searchPading': pageType == 0}">
		<Form :inline="true" :class="visible ? 'advanced-search-show':''">
			<div class="general-search">
				<div class="input-content" v-if="pageType == 0">
					<FormItem label="设备资源:">
						<div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{queryParam.selectDeviceList.length}}）</div>
					</FormItem>
					<FormItem label="抓拍时段:">
						<ui-tag-select ref="tagSelect1" :hideCheckAll='true' :value="captureTimePeriod[0].name" @input=" e => { handleInput(e, 'timeSlot')}">
							<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
								{{ item.name }}
							</ui-tag-select-option>
						</ui-tag-select>
						<DatePicker v-if="queryParam.timeSlot == '自定义'" v-model="queryParam.timeSlotArr" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" @on-change="dateChange" placeholder="请选择抓拍时间段" style="width: 330px"></DatePicker>
					</FormItem>
				</div>
				<div class="input-content" v-else>
					<div class="upload-input-list">
						<uiUploadImg :algorithmType="3" v-model="queryParam.urlList" @imgUrlChange="imgUrlChange" size="small" />
					</div>
					<div class="other-search">
						<div class="other-search-top card-border-color">
							<FormItem label="相似度:" class="slider-form-item">
								<div class="slider-content">
									<Slider v-model="queryParam.similarity"></Slider>
									<span>{{ queryParam.similarity }}%</span>
								</div>
							</FormItem>
						</div>
						<div class="other-search-bottom">
							<div class="flex">
								<FormItem label="设备资源:">
									<div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{queryParam.selectDeviceList.length}}）</div>
								</FormItem>
								<FormItem label="抓拍时段:">
									<ui-tag-select ref="tagSelect1" :hideCheckAll='true' :value="captureTimePeriod[0].name" @input=" e => { handleInput(e, 'timeSlot')}">
										<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
											{{ item.name }}
										</ui-tag-select-option>
									</ui-tag-select>
									<DatePicker v-if="queryParam.timeSlot == '自定义'" v-model="queryParam.timeSlotArr" @on-change="dateChange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择抓拍时间段" style="width: 330px"></DatePicker>
								</FormItem>
							</div>
							<div class="btn-group">
								<span class="advanced-search-text primary" @click="advancedSearchHandle($event)">
									更多条件 <i class="iconfont icon-jiantou"></i>
								</span>
								<Button type="primary" @click="searchHandle">查询</Button>
								<Button @click="resetHandle">重置</Button>
							</div>
						</div>
					</div>
				</div>
				<div class="btn-group" v-if="pageType == 0">
					<span class="advanced-search-text primary" @click="advancedSearchHandle($event)">
						更多条件 <i class="iconfont icon-jiantou"></i>
					</span>
					<Button type="primary" @click="searchHandle">查询</Button>
					<Button @click="resetHandle">重置</Button>
				</div>
			</div>
			<!--更多搜索条件-->
			<div class="advanced-search">
				<section class="search-container">
					<div class="search-item-container" id="searchItemContainer">
						<div class="classify-content">
							<span class="classify-name">服饰</span>
							<div class="items">
								<div class="advanced-search-list card-border-color">
									<Row>
										<Col span="11">
										<FormItem label="上身纹理:">
											<selectTag ref="upperTexture" :list="upperBodyTextureList" vModel="upperTexture" @selectItem="selectItem" />
										</FormItem>
										</Col>
										<Col span="13">
										<FormItem label="上身袖子类型:">
											<selectTag ref="sleeveStyle" :list="upperSleeveTypeList" vModel="sleeveStyle" @selectItem="selectItem" />
										</FormItem>
										</Col>
									</Row>
								</div>
								<div class="advanced-search-list card-border-color">
									<FormItem label="上身颜色:">
										<ui-tag-select ref="upperColor" @input=" e => {handleInput(e, 'upperColor')}">
											<ui-tag-select-option v-for="(item, $index) in recognitionColorList" :key="$index" effect="dark" :name="item.dataKey">
												<div v-if="bodyColorList[item.dataKey]" :style="{borderColor: bodyColorList[item.dataKey].borderColor}" class="plain-tag-color">
													<div :style="bodyColorList[item.dataKey].style"></div>
												</div>
											</ui-tag-select-option>
										</ui-tag-select>
									</FormItem>
								</div>
								<div class="advanced-search-list card-border-color">
									<Row>
										<Col span="11">
										<FormItem label="下身类型:">
											<selectTag ref="lowerStyle" :list="lowerBodyType" vModel="lowerStyle" @selectItem="selectItem" />
										</FormItem>
										</Col>
										<Col span="13">
										<FormItem label="下身颜色:">
											<ui-tag-select ref="lowerColor" @input=" e => {handleInput(e, 'lowerColor')}">
												<ui-tag-select-option v-for="(item, $index) in recognitionColorList" :key="$index" effect="dark" :name="item.dataKey">
													<div v-if="bodyColorList[item.dataKey]" :style="{borderColor: bodyColorList[item.dataKey].borderColor}" class="plain-tag-color">
														<div :style="bodyColorList[item.dataKey].style"></div>
													</div>
												</ui-tag-select-option>
											</ui-tag-select>
										</FormItem>
										</Col>
									</Row>
								</div>
							</div>
						</div>
						<div class="classify-content">
							<span class="classify-name">鞋子</span>
							<div class="items">
								<div class="advanced-search-list card-border-color">
									<Row>
										<Col span="11">
										<FormItem label="鞋子类别:">
											<selectTag ref="shoesStyle" :list="shoeCategory" vModel="shoesStyle" @selectItem="selectItem" />
										</FormItem>
										</Col>
										<Col span="13">
										<FormItem label="鞋子颜色:">
											<ui-tag-select ref="shoesColor" @input=" e => {handleInput(e, 'shoesColor')}">
												<ui-tag-select-option v-for="(item, $index) in recognitionColorList" :key="$index" effect="dark" :name="item.dataKey">
													<div v-if="bodyColorList[item.dataKey]" :style="{borderColor: bodyColorList[item.dataKey].borderColor}" class="plain-tag-color">
														<div :style="bodyColorList[item.dataKey].style"></div>
													</div>
												</ui-tag-select-option>
											</ui-tag-select>
										</FormItem>
										</Col>
									</Row>
								</div>
							</div>
						</div>
						<div class="classify-content">
							<span class="classify-name">其他</span>
							<div class="items">
								<div class="advanced-search-list card-border-color">
									<Row>
										<Col span="11">
										<FormItem label="性别:">
											<selectTag ref="gender" :list="genderList" vModel="gender" @selectItem="selectItem" />
										</FormItem>
										</Col>
										<Col span="13">
										<FormItem label="发型:">
											<selectTag ref="hairStyle" :list="hairStyleList" vModel="hairStyle" @selectItem="selectItem" />
										</FormItem>
										</Col>
									</Row>
								</div>
								<div class="advanced-search-list card-border-color">
									<Row>
										<Col span="11">
										<FormItem label="行为:">
											<selectTag ref="behavior" :list="behaviorList" vModel="behavior" @selectItem="selectItem" />
										</FormItem>
										</Col>
										<Col span="13">
										<FormItem label="附属物:">
											<selectTag ref="appendix" :list="appendantList" vModel="appendix" @selectItem="selectItem" />
										</FormItem>
										</Col>
									</Row>
								</div>
							</div>
						</div>
					</div>
				</section>
			</div>
		</Form>
		<!-- 选择设备 -->
		<select-device ref="selectDevice" :checkedLabels="checkedLabels" @selectData="selectData" />
	</div>
</template>

<script>
	import { captureTimePeriod } from './search-vehicle.js';
	import { mapActions, mapGetters } from 'vuex';
	import uiUploadImg from '@/components/ui-upload-new-img/index';
	import selectTag from '../../components/select-tag.vue';
	import { bodyColorList } from '@/libs/system'
	export default {
		name: '',
		components: {
			uiUploadImg,
			selectTag
		},
		data() {
			return {
				captureTimePeriod,
				visible: false,
				queryParam: {
					timeSlot: '近一天',
					timeSlotArr: '',
					selectDeviceList: [],
					features: [],
					urlList: [],
					similarity: 60
				},
				checkedLabels: [],
				bodyColorList,
			}
		},
		watch: {

		},
		computed: {
			...mapGetters({
				pageType: 'common/getPageType',                // 页面类型
				globalObj: 'systemParam/globalObj',
				upperBodyTextureList: 'dictionary/getUpperBodyTexture',  // 上身纹理
				upperBodyTypeList: 'dictionary/getUpperBodyType',        //上身类型
				upperSleeveTypeList: 'dictionary/getUpperSleeveType',         //上身袖子类型
				recognitionColorList: 'dictionary/getRecognitionColor',  //颜色
				lowerBodyType: 'dictionary/getLowerBodyType',            //下身类型
				shoeCategory: 'dictionary/getShoeCategory',              //鞋子类别
				hairStyleList: 'dictionary/getHairStyleList',            //发型
				behaviorList: 'dictionary/getBehaviorList',              //行为
				appendantList: 'dictionary/getAppendantList',            //附属物
				genderList: 'dictionary/getGenderList',                     //性别
			})
		},
		async created() {
			await this.getDictData()
			if (this.pageType !== 0) {
				this.queryParam.similarity = Number(this.globalObj.searchForPicturesDefaultSimilarity);
			}
			window.addEventListener("click", (e) => {
				this.visible = false;
			})
		},
		mounted() {
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			/**
			 * 选中tag赋值
			 */
			selectItem(key, item) {
				// 具体业务处理逻辑
				if (item) {
					this.queryParam[key] = item.dataKey
				} else {
					// 全部选项，不返回数据到后端
					this.queryParam[key] = null
				}
			},
			/**
			 * 选择接口返回数据
			 */
			handleInput(e, key) {
				this.queryParam[key] = e;
				this.$forceUpdate();
			},
			advancedSearchHandle($event) {
				$event.stopPropagation()
				if (this.visible) {
					this.visible = false
				} else {
					this.visible = true
				}
			},
			// 查询
			searchHandle() {
				this.visible = false
				this.$emit('search', '')
			},
			// 重置
			resetHandle() {
				let searchList = {
					upperTexture: '',
					sleeveStyle: '',
					upperColor: '',
					lowerStyle: '',
					lowerColor: '',
					shoesStyle: '',
					shoesColor: '',
					gender: '',
					hairStyle: '',
					behavior: '',
					appendix: '',
				};
				this.queryParam = {
					timeSlot: '近一天',
					timeSlotArr: '',
					selectDeviceList: [],
					features: [],
					urlList: [],
					...searchList,
					similarity: this.globalObj.searchForPicturesDefaultSimilarity - 0,
				};
				this.$refs.tagSelect1.clearChecked(false);
				for (let key in searchList) {
					this.$refs[key].clearChecked();
				}
				this.visible = false;
				this.$emit("reset")
			},
			// 选择设备
			selectDevice() {
				this.$refs.selectDevice.show(this.queryParam.selectDeviceList)
			},
			selectData(list) {
				this.queryParam.selectDeviceList = list;
			},
			dateChange(start, end) {
				if (start[1].slice(-8) === '00:00:00') {
					start[1] = start[1].slice(0, -8) + '23:59:59'
				}
				this.queryParam.timeSlotArr = [start[0], start[1]];
			},
			/**
			 * 图片上传结果返回
			 */
			imgUrlChange(list) {
				// 以图搜图字段
				let features = []
				list.forEach(item => {
					if (item) {
						features.push(item.feature)
					}
				})
				this.queryParam.features = features
			},
		}
	}
</script>

<style lang='less' scoped>
	@import "./search-vehicle.less";
	.search {
		padding: 10px 20px 0;
	}
	.other-search {
		display: flex;
		flex: 1;
		box-sizing: border-box;
		flex-direction: column;
		padding-left: 10px;
		.other-search-top {
			display: flex;
			border-bottom: 1px dashed #fff;
		}
		/deep/ .ivu-form-item {
			display: flex;
			margin-bottom: 10px;
			/deep/ .ivu-form-item-label {
				padding-right: 10px;
			}
			// .ivu-input-wrapper,
			.ivu-select {
				width: 200px;
			}
		}
		.other-search-bottom {
			display: flex;
			justify-content: space-between;
			padding-top: 10px;
			box-sizing: border-box;
			.slider-form-item {
				/deep/ .ivu-form-item-content {
					display: flex;
					align-items: center;
				}
			}
		}
	}
	.advanced-search-list {
		width: 100%;
		padding: 10px 0;
		border-bottom: 1px dashed #fff;
		&:last-child {
			border-bottom: 0;
		}
	}
	.searchPading {
		padding: 10px 20px;
	}
</style>
