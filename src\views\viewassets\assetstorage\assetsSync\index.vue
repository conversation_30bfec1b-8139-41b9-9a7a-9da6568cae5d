<template>
  <div class="assets-sync auto-fill">
    <tabs :tabs-list="tabsList" @tabsChange="tabsChange">
      <div class="pointer">
        <i class="icon-font font-active-color icon-canshupeizhi f-16 mr-xs"></i>
        <span class="inline vt-middle font-active-color f-14" @click="showParamsConfig">参数配置</span>
      </div>
    </tabs>
    <div class="layout-content auto-fill">
      <!-- 2是共享联网平台 -->
      <div class="left-tree" v-if="activeTabs === '2'">
        <span class="base-text-color f-14">原始目录</span>
        <ui-search-tree
          ref="uiSearchTree"
          placeholder="请输入目录名称"
          class="ui-search-tree search-tree-box"
          :tree-data="treeData"
          :tree-loading="treeLoading"
          :default-props="defaultProps"
          :node-key="nodeKey"
          :current-node-key="currentNodeKey"
          :cancel-current-node-checked="true"
          @selectTree="selectTree"
        >
        </ui-search-tree>
      </div>
      <section class="right-content auto-fill">
        <icon-box
          :active-tabs="activeTabs"
          :org-id="orgId"
          :search-params="searchData"
          @bulkAssetsInstore="bulkAssetsInstore"
          @updateList="initList"
        ></icon-box>
        <search-wrapper
          ref="searchRef"
          :checked-device-list="checkedDeviceList"
          :is-disabled="searchData.deviceType === '3'"
          @compareModeChange="compareModeChange"
          @chooseAll="chooseAll"
          @startSearch="startSearch"
          @changeTab="changeTab"
        >
          <template #totalSlot>
            <span class="font-warning">{{
              !!searchData.isAllSelect ? pageData.totalCount : checkedDeviceList.length
            }}</span>
          </template>
          <!-- 删除设备没有资产比对按钮 -->
          <Button
            class="ml-sm"
            type="primary"
            v-show="searchData.deviceType !== '3'"
            :loading="buttonLoading"
            @click="assetsTestCompare"
          >
            <i class="icon-font icon-shujujiancegongju-01-01 f-16 mr-sm vt-middle"></i>
            <span>{{ !buttonLoading ? '资产检测与比对' : '检测与比对中...' }}</span>
          </Button>
          <Button
            class="ml-sm"
            type="primary"
            :disabled="!searchData.isAllSelect && !checkedDeviceList.length"
            @click="moveDevice"
          >
            <i class="icon-font icon-yidong f-16 mr-sm vt-middle"></i>
            <span>移动</span>
          </Button>
          <Button type="primary" class="btn_search ml-sm" @click="exportDevice" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs">导出</span>
          </Button>
          <template v-if="this.searchData.deviceType === stateOptionsObject['删除设备'].value">
            <Button type="primary" class="btn_search ml-sm" @click="bulkDeleteTarget" v-if="activeTabs === '2'">
              <i class="icon-font icon-shanchu mr-sm font-white"></i>
              <span class="inline ml-xs">删除联网平台来源标识</span>
            </Button>
            <Button type="primary" class="btn_search ml-sm" @click="makeUnVailable">
              <i class="icon-font icon-zhiweibukeyong font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">批量置为不可用</span>
            </Button>
          </template>
          <Button class="ml-sm" type="primary" @click="bulkDeleteDevice">
            <i class="icon-font icon-shanchu mr-sm"></i>
            <span>批量删除</span>
          </Button>
        </search-wrapper>
        <div class="relative auto-fill ml-md">
          <table-module
            :base-table-data="baseTableData"
            :compare-table-data="compareTableData"
            :compare-mode="compareMode"
            :active-tabs="activeTabs"
            :loading="tableLoading"
            :ret-default-data-flag="retDefaultDataFlag"
            :is-all-select="!!searchData.isAllSelect"
            @checkTableData="checkTableData"
          >
            <template #pageSlot>
              <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
              </ui-page>
            </template>
          </table-module>
          <slide-org-tree
            ref="slideTreeRef"
            class="transition-slide"
            :style="slideStyle"
            @moveSave="moveSaveFirst"
            @cancel="moveDevice"
          ></slide-org-tree>
        </div>
      </section>
    </div>
    <config-modal
      ref="configModelRef"
      v-model="paramsConfigShow"
      :active-store="activeTabs"
      :property-list="propertyList"
      @getConfigParams="(params) => (configParams = params)"
    ></config-modal>
    <!-- 资产入库和资产移动 -->
    <asset-instore-modal-mutiple
      v-model="assetInstoreModalShowMutiple"
      :module-action="assetInstoreAction"
      :property-list="propertyList"
      :default-params="configParams"
      @saveQuery="saveAssetInstore"
    >
      <p v-if="!!moveSlotMessage" class="mb-sm base-text-color move-slot-message" v-html="moveSlotMessage"></p>
    </asset-instore-modal-mutiple>
    <asset-instore-modal-single
      v-model="assetInstoreModalShowSingle"
      :active-differ-detail="activeDifferDetail"
      @oneAssetsConfirmInstore="oneAssetsConfirmInstore"
    >
    </asset-instore-modal-single>
    <difference-detail v-model="differenceDetailShow" :active-differ-detail="activeDifferDetail"></difference-detail>
    <upload-error v-model="errorReasonShow" :error-data="errorData" :error-columns="errorColumns" footer-hide>
    </upload-error>
    <device-detail
      v-model="deviceDetailShow"
      modal-title="查看详情"
      modal-action="view"
      :view-url="viewUrl"
      :view-device-id="activeTableItem.id"
      :device-code="''"
      :unqualified="null"
      :has-multiView="false"
    >
    </device-detail>
  </div>
</template>
<script>
import assetsSync from '@/config/api/assetsSync';
import taganalysis from '@/config/api/taganalysis';
import equipmentassets from '@/config/api/equipmentassets';
import { tabsList, errorColumns } from './util/enum.js';
import { stateOptionsObject } from './util/enum.js';
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'assetsSync',
  props: {},
  data() {
    return {
      stateOptionsObject: Object.freeze(stateOptionsObject),
      viewUrl: '',
      activeTableItem: {
        id: 0,
        deviceId: '',
      },
      activeDifferDetail: [],
      configParams: {},
      paramsConfigShow: false,
      assetInstoreModalShowMutiple: false,
      assetInstoreModalShowSingle: false,
      differenceDetailShow: false,
      errorReasonShow: false,
      deviceDetailShow: false,
      slideStyle: { transform: 'translateX(500px)' },
      slideTreeShow: false,
      searchData: {
        isAllSelect: 0,
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      baseTableData: [],
      compareTableData: [],
      checkedDeviceList: [],
      tableLoading: false,
      exportLoading: false,
      unvailableLoading: false,
      nodeKey: 'channelId',
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      treeData: [],
      tabsList: Object.freeze(tabsList),
      activeTabs: '2',
      activeInterfaceName: 'assertDeviceAsycNet',
      compareMode: false,
      propertyList: [],
      buttonLoading: false,
      currentNodeKey: '',
      treeLoading: false,
      retDefaultDataFlag: 0,
      optionListObject: Object.freeze({
        // 自己命名（不要改动）
        detail: {
          name: '查看详情',
          func: (row) => this.handlerAction(row, 'deviceDetailShow'),
          iconName: 'icon-chakanxiangqing',
        },
        instore: {
          name: '资产入库',
          func: (row) => this.openDifferTool(row, 'assetInstoreModalShowSingle'),
          iconName: 'icon-zichanruku2',
        },
        except: {
          name: '差异详情',
          func: (row) => this.openDifferTool(row, 'differenceDetailShow'),
          iconName: 'icon-chayixiangqing',
        },
        errorReason: {
          name: '异常原因',
          func: (row) => this.viewErrorReson(row),
          iconName: 'icon-yichangyuanyin',
        },
        delete: {
          name: '从资产库中删除',
          func: (row) =>
            this.uniConfirmTips('确认当前设备从资产库中删除吗', () => this.deleteDevice({ ids: [row.id] })),
          iconName: 'icon-shanchu3',
        },
        useless: {
          name: '置为不可用',
          func: (row) => this.uniConfirmTips('确认当前设备置为不可用吗', () => this.diabledDevice({ ids: [row.id] })),
          iconName: 'icon-zhiweibukeyong',
        },
      }),
      // 移入选择的目录
      activeOrg: {
        orgCode: null,
        orgName: '',
      },
      moveSlotMessage: '',
      assetInstoreAction: {
        title: '资产入库',
        type: 'instore',
      },
      errorData: [],
      errorColumns: Object.freeze(errorColumns),
      orgId: null,
    };
  },
  async created() {
    this.getAlldicData();
    await this.initTreeData();
    if (this.treeData.length) {
      // this.searchData.channelId = this.treeData[0].channelId
      //this.currentNodeKey = this.treeData[0].channelId
      // this.orgId = this.treeData[0].channelId;
    }
    this.initList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'assets/getAlldicData',
    }),
    async viewErrorReson(row) {
      try {
        let { data } = await this.$http.get(assetsSync.queryUnqualifiedDetail(this.activeInterfaceName), {
          params: { id: row.id },
        });
        this.errorData = data.data;
        this.errorReasonShow = true;
      } catch (err) {
        console.log(err);
      }
    },
    // 共享联网平台、视图库、一机一档切换
    tabsChange(val) {
      this.activeTabs = val;
      this.getPropertyList();
      delete this.searchData.channelId;
      this.compareMode = false;
      let Item = this.tabsList.find((item) => item.value === val);
      // 动态拼接接口
      this.activeInterfaceName = Item.interfaceMiddle;
      this.$refs.searchRef.reset();
      this.retDefaultDataFlag = Math.random();
      this.$nextTick(() => {
        this.$refs.configModelRef.queryByConfigType();
      });
    },
    // 全部设备、新增设备、相同设备、差异设备、删除设备切换
    changeTab(val) {
      this.compareMode = false;
      this.$refs.searchRef.reset({ deviceType: val.deviceType }, false);
    },
    // 比对模式切换
    compareModeChange(val) {
      this.compareMode = val;
      this.searchData.isCompare = val ? 1 : 0;
      this.initList();
    },
    startSearch(params) {
      Object.assign(this.searchData, params);
      this.initList();
    },
    // 列表全选
    chooseAll(val) {
      // 是否全选 0否  1是
      this.searchData.isAllSelect = val ? 1 : 0;
      this.checkedDeviceList = [];
      this.retDefaultDataFlag = Math.random();
    },
    // 查询全部字段
    async getPropertyList() {
      // 共享联网平台 - 不查询全部字段，做筛选
      let propertyType = '1'; // lmw
      if (this.activeTabs === '2') {
        propertyType = '8';
      }
      // 视图库 - 不查询全部字段，做筛选
      if (this.activeTabs === '3') {
        propertyType = '9';
      }
      // 一机一档 - 不查询全部字段，做筛选
      if (this.activeTabs === '4') {
        propertyType = '10';
      }
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: propertyType, // 视图
        });
        this.propertyList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    initList() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.retDefaultDataFlag = Math.random();
      this.getPageList();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.getPageList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.getPageList();
    },
    /**----------------------------- 处理表格数据start----------------------------------- */
    async getPageList() {
      this.tableLoading = true;
      this.baseTableData = [];
      this.compareTableData = [];
      try {
        let { data } = await this.$http.post(assetsSync.getCommonPageList(this.activeInterfaceName), this.searchData);
        this.baseTableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.handleListAction();
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    handleListAction() {
      let differArray = [];
      this.baseTableData.forEach((item) => {
        // 动态处理表格的操作按钮
        this.handleButtonAction(item);
        // 处理差异设备 - 比对模式的表格数据
        item.deviceInfo ? this.compareTableData.unshift(item.deviceInfo) : null;
        // 处理标识差异字段
        if (item.differList) {
          differArray.push(...item.differList);
        }
      });
      this.handlerDifferCellName(differArray);
    },
    handlerDifferCellName(differArray) {
      /**------标识差异字段，给差异字段添加类名标识----**/
      let differObject = {};
      differArray.forEach((item) => {
        item.asycId in differObject ? null : (differObject[item.asycId] = {});
        differObject[item.asycId][item.fieldName] = 'active-warning-cell';
      });
      this.baseTableData.forEach((item) => {
        item.cellClassName = differObject[item.id];
      });
    },
    handleButtonAction(item) {
      // 1.删除按钮
      item.operationList = [this.optionListObject.delete];
      // 2.置为不可用按钮 - 删除设备的tab ，按钮只有有从[资产库中删除、置为不可用]
      if (item.deviceType === stateOptionsObject['删除设备']) {
        item.operationList.unshift(this.optionListObject.useless);
        return;
      }
      // 3.查看详情
      item.operationList.unshift(this.optionListObject.detail);
      // 4.差异详情按钮 - 已比对状态的设备
      if (item.compareStatus === '1') {
        item.operationList.unshift(this.optionListObject.except);
      }
      // 5.资产入库按钮 - 未入库状态的设备
      if (item.storageStatus === '0') {
        item.operationList.unshift(this.optionListObject.instore);
      }
      // 6.异常原因按钮 - 基础信息状态不合格的设备
      if (item.checkStatus === '2') {
        item.operationList.unshift(this.optionListObject.errorReason);
      }
    },
    /**----------------------------- 处理表格数据end----------------------------------- */
    // 资产检测与比对
    assetsTestCompare() {
      let message = '';
      !!this.checkedDeviceList.length && !this.searchData.isAllSelect
        ? (message = `已选择${this.checkedDeviceList.length}条设备，确定进行检测与比对吗`)
        : (message = '确定对全部设备进行检测与比对吗?');
      this.$UiConfirm({ content: message }).then(async () => {
        this.buttonLoading = true;
        try {
          let params = {};
          // 全选需要传所有参数条件
          if (!!this.searchData.isAllSelect || (!this.searchData.isAllSelect && !this.checkedDeviceList.length)) {
            params = { ...this.searchData };
            params.isAllSelect = 1;
          }
          // 如果没有全选
          !this.searchData.isAllSelect ? (params.ids = this.checkedDeviceList.map((item) => item.id)) : null;
          let { data } = await this.$http.post(assetsSync.checkAndCompareDevice(this.activeInterfaceName), params);
          this.$Message.success(data.msg);
          this.initList();
        } catch (error) {
          console.log(error);
        } finally {
          this.buttonLoading = false;
        }
      });
    },
    handleCommonParams() {
      let params = {};
      // 全选需要传所有参数条件
      if (!!this.searchData.isAllSelect || (!this.searchData.isAllSelect && !this.checkedDeviceList.length)) {
        params = { ...this.searchData };
        params.isAllSelect = 1;
      }
      // 如果没有全选
      !this.searchData.isAllSelect ? (params.ids = this.checkedDeviceList.map((item) => item.id)) : null;
      return params;
    },
    async bulkDeleteTarget() {
      if (!this.checkedDeviceList.length && !this.searchData.isAllSelect) {
        this.$Message.warning('请先选择设备');
        return;
      }
      let params = this.handleCommonParams();
      try {
        await this.$http.post(assetsSync.updateDeviceOfSourceId(this.activeInterfaceName), params);
        this.$Message.warning('成功');
        this.initList();
      } catch (err) {
        console.log(err);
      }
    },
    // 批量置为不可用
    makeUnVailable() {
      if (!this.checkedDeviceList.length && !this.searchData.isAllSelect) {
        this.$Message.warning('请先选择设备');
        return;
      }
      let message = this.searchData.isAllSelect
        ? '确定全部置为不可用吗？'
        : `已选择${this.checkedDeviceList.length}条设备，确定全部置为不可用吗？`;
      this.$UiConfirm({ content: message }).then(async () => {
        try {
          let params = {};
          this.searchData.isAllSelect ? (params = { ...this.searchData }) : null;
          !this.searchData.isAllSelect ? (params.ids = this.checkedDeviceList.map((item) => item.id)) : null;
          this.diabledDevice(params);
        } catch (error) {
          console.log(error);
        }
      });
    },
    // 置为不可用
    async diabledDevice(params) {
      try {
        await this.$http.post(assetsSync.updateDeviceOfPhyStatus(this.activeInterfaceName), params);
        this.$Message.warning('成功');
        this.initList();
      } catch (err) {
        console.log(err);
      }
    },
    // 批量删除
    bulkDeleteDevice() {
      if (!this.checkedDeviceList.length && !this.searchData.isAllSelect) {
        this.$Message.warning('请先选择设备');
        return;
      }
      let length = this.checkedDeviceList.length;
      let message = this.searchData.isAllSelect ? '确定删除全部设备吗？' : `已选择${length}条设备，确定全部删除吗？`;
      // 视图库、一机一档
      if (this.activeTabs !== '2') {
        // 新增、相同、差异
        if (!!this.searchData.deviceType && this.searchData.deviceType !== '3') {
          message = this.searchData.isAllSelect
            ? '确定删除全部原始设备吗（不会删除资产库设备）？'
            : `已选择${length}条原始设备，确定删除吗（不会删除资产库设备）`;
        }
        // 删除
        if (!!this.searchData.deviceType && this.searchData.deviceType === '3') {
          message = this.searchData.isAllSelect
            ? '确定删除全部设备吗（从资产库中删除）？'
            : `已选择${length}条设备，确定从资产库中删除吗`;
        }
      }
      this.$UiConfirm({ content: message }).then(() => {
        let params = {};
        this.searchData.isAllSelect ? (params = { ...this.searchData }) : null;
        !this.searchData.isAllSelect ? (params.ids = this.checkedDeviceList.map((item) => item.id)) : null;
        this.deleteDevice(params);
      });
    },
    // 删除设备
    async deleteDevice(params, isGetPlantdevice = false) {
      let interfaceName = 'deleteListData';
      this.searchData.deviceType === '3' ? (interfaceName = 'updateDeviceOfIsDel') : null;
      try {
        await this.$http.post(assetsSync[interfaceName](this.activeInterfaceName), params);
        // 获取联网平台不需要提示删除
        if (!isGetPlantdevice) {
          this.$Message.warning('删除成功');
          this.initList();
        }
      } catch (err) {
        console.log(err);
      }
    },
    showParamsConfig() {
      this.paramsConfigShow = true;
    },

    moveDevice() {
      this.slideTreeShow = !this.slideTreeShow;
      !this.slideTreeShow ? this.$refs.slideTreeRef.clearTree() : null;
    },
    selectTree(data) {
      this.searchData.channelId = data.channelId;
      this.orgId = data.channelId;
      this.initList();
    },
    handlerAction(row, type) {
      this.viewUrl = assetsSync.commonView(this.activeInterfaceName);
      this.activeTableItem = row;
      this[type] = true;
    },
    /**-----------------------------资产入库start----------------------------------- */
    uniConfirmTips(message, callBack) {
      this.$UiConfirm({ content: message }).then(async () => {
        console.log(callBack);
        callBack();
      });
    },
    // 打开差异详情, 单个资产入库弹框
    openDifferTool(row, modalShowName) {
      this.activeTableItem = row;
      this.activeDifferDetail = [];
      this.differenceDetails().then((data) => {
        this.activeDifferDetail = data;
        this[modalShowName] = true;
      });
    },
    // 单个确认资产入库
    oneAssetsConfirmInstore(storageParam) {
      let newParams = {
        configType: this.activeTabs,
        config: Object.assign({ storageParam: storageParam }, this.configParams),
        isAllSelect: 0,
      };
      newParams.ids = [this.activeTableItem.id];
      this.confirmAssetsInstore(newParams).then(() => {
        this.assetInstoreModalShowSingle = false;
        this.initList();
      });
    },
    async differenceDetails() {
      try {
        const { data } = await this.$http.get(assetsSync.queryDifferDetail(this.activeInterfaceName), {
          params: {
            id: this.activeTableItem.id,
          },
        });
        return data.data;
      } catch (err) {
        console.log(err);
      }
      this.assetInstoreModalShowSingle = true;
    },
    // 批量资产入库
    bulkAssetsInstore() {
      if (!this.checkedDeviceList.length && !this.searchData.isAllSelect) {
        this.$Message.warning('请选择待入库的设备');
        return;
      }
      // 没有全选，验证选中设备是否有基础数据检测异常的设备
      if (!this.searchData.isAllSelect) {
        // 检测状态：0 未检测  ,1 合格，2 不合格，3 检测中
        let checkStatusExceptList = this.checkedDeviceList.filter((item) => item.checkStatus === '2');
        checkStatusExceptList.length ? this.InstoreNeedTips(checkStatusExceptList.length) : this.openInstoreModule();
        return;
      }
      // 全选设备，需要调用接口查看是否有基础数据检测异常设备
      this.validateNoInstoreList().then((data) => {
        data.checkFailCount ? this.InstoreNeedTips(this.pageData.totalCount) : this.openInstoreModule();
      });
    },
    InstoreNeedTips(total) {
      let message = `共选择${total}条设备，是否继续入库？`;
      this.$UiConfirm({ content: message }).then(async () => {
        this.openInstoreModule();
      });
    },
    openInstoreModule() {
      this.assetInstoreAction = {
        title: '资产入库',
        type: 'instore',
      };
      this.moveSlotMessage = '';
      this.assetInstoreModalShowMutiple = true;
    },
    // 批量资产入库
    async confirmBulkAssetsInstore(params) {
      let newParams = {
        isAllSelect: this.searchData.isAllSelect,
        // configType: this.activeTabs,
        config: Object.assign({}, this.configParams, params),
      };
      this.searchData.isAllSelect ? (newParams = { ...newParams, ...this.searchData }) : null;
      // 如果不是全选，需要传递设备ids
      !this.searchData.isAllSelect ? (newParams.ids = this.checkedDeviceList.map((item) => item.id)) : null;
      await this.confirmAssetsInstore(newParams);
      this.assetInstoreModalShowMutiple = false;
      this.initList();
    },
    // 确认资产入库
    async confirmAssetsInstore(params) {
      try {
        let { data } = await this.$http.post(assetsSync.storageDevice(this.activeInterfaceName), params);
        return data;
      } catch (error) {
        console.log(error);
      }
    },
    // 资产入库成功
    async saveAssetInstore(type, params) {
      // 资产移动需要再调移动接口
      if (type === 'move') {
        await this.moveSaveConfirm();
      } else {
        await this.confirmBulkAssetsInstore(params);
      }
      this.$nextTick(() => {
        this.$refs.configModelRef.queryByConfigType();
      });
    },
    /**-----------------------------资产入库end----------------------------------- */
    /**-----------------------------资产移动start----------------------------------- */
    moveSaveFirst(activeOrg) {
      this.activeOrg = activeOrg;
      if (activeOrg.orgCode === null) {
        this.$Message.warning('请选择待移入的目录');
        return;
      }
      if (!this.checkedDeviceList.length && !this.searchData.isAllSelect) {
        this.$Message.warning('请选择待移动的设备');
        return;
      }
      // 如果有未入库的设备需要先入库
      // 没有全选，验证选中设备是否有未入库设备
      if (!this.searchData.isAllSelect) {
        let noInstoreListIds = this.checkedDeviceList.filter((item) => item.storageStatus === '0').map((one) => one.id);
        // 没有未入库的，正式移入，有未入库的先入库
        noInstoreListIds.length ? this.needInstore(noInstoreListIds.length) : this.moveSaveConfirm();
      } else {
        // 全选设备，需要调用接口查看是否有未入库设备
        this.validateNoInstoreList().then((data) => {
          data.noneStorageCount ? this.needInstore(data.noneStorageCount) : this.moveSaveConfirm();
        });
      }
    },
    async validateNoInstoreList() {
      try {
        let { data } = await this.$http.post(
          assetsSync.moveBeforeStatistics(this.activeInterfaceName),
          this.searchData,
        );
        return data.data;
      } catch (error) {
        console.log(error);
      }
    },
    needInstore(num) {
      this.assetInstoreAction = {
        title: '资产移动',
        type: 'move',
      };
      this.moveSlotMessage = `共选择 <span class='font-active-color'>${this.checkedDeviceList.length} </span>条设备，
      其中有<span class='font-warning'>${num}</span>条为未入库设备，如果强行移入<span class='font-active-color'>【${this.activeOrg.orgName}】</span>目录，则默认将以上设备审核入库`;
      this.assetInstoreModalShowMutiple = true;
    },
    // 确定资产移入
    moveSaveConfirm() {
      let total = this.searchData.isAllSelect ? this.pageData.totalCount : this.checkedDeviceList.length;
      let message = `共选择${total}条设备，将移入【${this.activeOrg.orgName}】，确定移入吗？`;
      this.$UiConfirm({ content: message }).then(async () => {
        let newParams = {
          isAllSelect: this.searchData.isAllSelect,
          orgcode: this.activeOrg.orgName,
        };
        // 如果不是全选，需要传递设备ids
        !this.searchData.isAllSelect
          ? (newParams.deviceIds = this.checkedDeviceList.map((item) => item.deviceId))
          : null;
        // 组织机构和行政区划 - 哪个有就传哪个
        !this.activeOrg.orgCode
          ? (newParams.orgCode = this.activeOrg.orgCode)
          : (newParams.civilCode = this.activeOrg.regionCode);
        try {
          await this.$http.post(assetsSync.moveOrgCode(this.activeInterfaceName), newParams);
          this.initList();
        } catch (error) {
          console.log(error);
        } finally {
          //this.tableLoading = false
        }
      });
    },
    /**-----------------------------资产移动end----------------------------------- */
    checkTableData(selectedData) {
      this.checkedDeviceList = selectedData;
    },
    async initTreeData() {
      this.treeLoading = true;
      try {
        const res = await this.$http.get(equipmentassets.videoDeviceChannelGetTree);
        this.treeData = this.$util.common.arrayToJson(
          this.$util.common.deepCopy(res.data.data),
          'channelId',
          'parentId',
        );
      } catch (err) {
        console.log(err);
      } finally {
        this.treeLoading = false;
      }
    },
    async exportDevice() {
      this.exportLoading = true;
      try {
        let res = await this.$http.post(assetsSync.pageListExport(this.activeInterfaceName), this.searchData, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  watch: {
    slideTreeShow: {
      handler() {
        this.$nextTick(() => {
          let width = this.$refs.slideTreeRef.$el.offsetWidth;
          !this.slideTreeShow
            ? (this.slideStyle = { transform: `translateX(${width + 5}px)` })
            : (this.slideStyle = { transform: 'translateX(0px)' });
        });
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
      aysc_storage_status: 'assets/aysc_storage_status',
      aysc_compare_status: 'assets/aysc_compare_status',
      aysc_storage_type: 'assets/aysc_storage_type',
    }),
    fetchUrl() {
      return assetsSync.queryUnqualifiedDetail(this.activeInterfaceName);
    },
  },
  components: {
    Tabs: require('./components/tabs.vue').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    searchWrapper: require('./modules/search-wrapper.vue').default,
    TableModule: require('./modules/table-module.vue').default,
    SlideOrgTree: require('./modules/slide-org-tree.vue').default,
    ConfigModal: require('./modules/config-modal/index.vue').default,
    AssetInstoreModalMutiple: require('./modules/asset-instore-modal/mutiple.vue').default,
    AssetInstoreModalSingle: require('./modules/asset-instore-modal/single.vue').default,
    DifferenceDetail: require('./modules/difference-detail.vue').default,
    UploadError: require('@/views/datagovernance/onlinegovernance/information/components/upload-error.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    IconBox: require('./modules/icon-box.vue').default,
  },
};
</script>
<style lang="less" scoped>
.assets-sync {
  background: var(--bg-content);
  .slide-enter-active,
  .slide-leave-active {
    transition: translateX 0.5s;
  }
  .slide-enter,
  .slide-leave-to {
    transform: translateX(-300px);
  }
  .layout-content {
    position: relative;
    display: flex;
    flex-direction: row;
    .left-tree {
      width: 300px;
      padding: 20px 10px;
      height: 100%;
      border-right: 1px solid var(--devider-line);
      .ui-search-tree {
        height: calc(~'100% - 30px');
      }
    }
    .right-content {
      flex: 1;
    }
  }
  .move-slot-message {
    margin-left: 20px;
  }
  .transition-slide {
    transition: 1s;
    transform: translateX(258px);
  }
  @{_deep}.active-warning-cell {
    color: rgb(188, 60, 25);
  }
}
</style>
