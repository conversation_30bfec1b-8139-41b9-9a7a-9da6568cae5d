import request from "@/libs/request";
import { monographic } from "../Microservice";
import { emphasis as domainName } from "./base";

// 今日事件统计分析
export function queryTodayStatistic() {
  return request({
    url: monographic + domainName + "/statistics/queryTodayStatistic",
    method: "get",
  });
}

// 人员业务类型统计
export function personBizLabelStatistics(data = {}) {
  return request({
    url: monographic + domainName + "/statistics/personBizLabelStatistics",
    method: "post",
    data,
  });
}

// 获取系统中配置的场所信息
export function getConfigPlaces() {
  return request({
    url: monographic + domainName + "/sysConfig/getConfigPlaces",
    method: "post",
  });
}

// 人脸告警记录分页列表
export function faceAlarmPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/faceAlarmPageList",
    method: "post",
    data,
  });
}
// 重点场所人员活动
export function abnormalActivityPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/abnormalActivityPageList",
    method: "post",
    data,
  });
}

// 高危风险列表分页查询
export function riskBehaviorPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/riskBehaviorPageList",
    method: "post",
    data,
  });
}

// 人员轨迹异常报警
export function trackAbnormalAlarmPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/trackAbnormalAlarmPageList",
    method: "post",
    data,
  });
}

// 人员跨域流动入侵
export function intrusionPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/intrusionPageList",
    method: "post",
    data,
  });
}

// 与其他重点人同行
export function travelAlongDetailPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/travelAlongDetailPageList",
    method: "post",
    data,
  });
}

// 获取系统中配置的二级场所类型
export function getConfigPlaceSecondLevels() {
  return request({
    url: monographic + domainName + "/sysConfig/getConfigPlaceSecondLevels",
    method: "post",
  });
}

// 热力图
export function getHeatmapData(data) {
  return request({
    url: monographic + domainName + "/statistics/heatmap",
    method: "get",
    data,
  });
}

// 查询重点人专题配置信息
export function getImportantConfig() {
  return request({
    url: monographic + domainName + "/sysConfig/getEmphasisConfig",
    method: "post",
  });
}

// 修改重点人专题配置信息
export function updateImportantConfig(data) {
  return request({
    url: monographic + domainName + "/sysConfig/updateEmphasisConfig",
    method: "post",
    data,
  });
}

// 布控任务分页查询
export function taskPageList(data) {
  return request({
    url: monographic + domainName + "/compare/task/pageList",
    method: "post",
    data,
  });
}
