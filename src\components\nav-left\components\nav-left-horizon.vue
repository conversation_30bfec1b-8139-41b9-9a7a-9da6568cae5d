<template>
  <div class="menu-list">
    <el-menu :open-names="openNames" :default-active="activeRouter" :collapse="!isCollapsed" @select="selectMenu">
      <template v-for="item in menuList">
        <el-submenu
          class="sub-menu-first"
          v-if="item[menuProp.menuChildren] && item[menuProp.menuChildren].length > 0"
          :index="item[menuProp.menuName]"
          :key="item[menuProp.menuName]"
          :title="item[menuProp.menuText]"
        >
          <template #title>
            <i class="iconfontconfigure" :class="`icon-${item[menuProp.menuIcon]}`"></i>
            <span class="inline vt-middle ml-md">{{ item[menuProp.menuText] }}</span>
          </template>
          <template v-for="itm in item[menuProp.menuChildren]">
            <el-submenu
              v-if="itm[menuProp.menuChildren] && itm[menuProp.menuChildren].length > 0"
              :index="itm[menuProp.menuName]"
              :key="itm[menuProp.menuName]"
              :title="itm[menuProp.menuText]"
            >
              <template #title>
                <span class="inline vt-middle">{{ itm[menuProp.menuText] }}</span>
              </template>
              <el-menu-item
                v-for="row in itm[menuProp.menuChildren]"
                :key="row[menuProp.menuName]"
                :index="row[menuProp.menuName]"
                @selectMenu="selectMenu"
              >
                <span class="inline vt-middle">{{ row[menuProp.menuText] }}</span>
              </el-menu-item>
            </el-submenu>
            <el-menu-item v-else :index="itm[menuProp.menuName]" @selectMenu="selectMenu" :key="itm[menuProp.menuName]">
              <span class="inline vt-middle">{{ itm[menuProp.menuText] }}</span>
            </el-menu-item>
          </template>
        </el-submenu>
        <el-menu-item
          v-else
          class="menu-first"
          :index="item[menuProp.menuName]"
          :key="item[menuProp.menuName]"
          :title="item[menuProp.menuText]"
          @selectMenu="selectMenu"
        >
          <i class="iconfontconfigure" :class="`icon-${item[menuProp.menuIcon]}`"></i>
          <span class="inline vt-middle ml-md">{{ item[menuProp.menuText] }}</span>
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>
<script>
import navExtends from '../utils/extends';
export default {
  extends: navExtends,
};
</script>
<style lang="less" scoped>
.menu-item {
  color: #fff !important;

  &.is-active {
    background: #184f8d !important;
  }

  &:hover,
  &:focus {
    background: #023960 !important;
  }
}

.menu-list {
  height: calc(~'100% - 60px');
  overflow-y: auto;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    display: none;
  }

  @{_deep}.el-menu {
    background: transparent;
    color: #fff;
    font-size: 14px;
    border: 0;
    &.el-menu--collapse {
      width: 50px;
    }
    .el-menu-item {
      .menu-item;
      &:not(.menu-first) {
        padding-left: 50px !important;
      }
    }
    .el-submenu {
      .el-menu-item {
        &.is-active {
          background: linear-gradient(270deg, rgba(19, 122, 185, 0) 0%, #2b84e2 100%) !important;
        }
      }
    }

    .el-submenu__title {
      .menu-item;
    }
  }
}
</style>
