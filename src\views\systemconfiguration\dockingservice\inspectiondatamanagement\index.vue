<template>
  <div class="inspection-data-management auto-fill">
    <underline-menu v-model="activeCode" :data="libraryList"></underline-menu>
    <component :is="activeCode" :search-fun="getTableList" :delete-fun="deleteFun" :save="save"></component>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'inspectiondatamanagement',
  props: {},
  data() {
    return {
      activeCode: 'people',
      libraryList: [
        {
          label: '实有人口库',
          code: 'people',
        },
        {
          label: '人员照片库',
          code: 'peoplepicture',
        },
        {
          label: '车辆轨迹库',
          code: 'car',
        },
      ],
    };
  },
  created() {},
  methods: {
    getTableList(searchData) {
      return this.$http.post(equipmentassets.getManagementData, searchData);
    },
    deleteFun(deleteArr) {
      return this.$http.post(equipmentassets.removeManagementData, {
        ids: deleteArr.map((item) => item.id),
      });
    },
    save(formData) {
      return this.$http.post(equipmentassets.editManagementData, formData);
    },
  },
  watch: {},
  components: {
    People: require('./people').default,
    Peoplepicture: require('./peoplepicture').default,
    Car: require('./car').default,
    UnderlineMenu: require('@/components/underline-menu').default,
  },
};
</script>
<style lang="less" scoped>
.inspection-data-management {
  background: var(--bg-content);
}
</style>
