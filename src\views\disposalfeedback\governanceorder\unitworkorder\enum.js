import TaskExpandRow from './components/TaskExpandRow.vue';
//任务模式的列
export const getTaskModeTableColumns = (_that, isExpandRow = false) => {
  let _this = _that;
  return [
    {
      type: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left',
      isShow: !isExpandRow,
    },
    {
      title: '序号',
      type: 'index',
      width: 50,
      isShow: !isExpandRow,
    },
    {
      title: ' ',
      key: '',
      width: 56,
      isShow: isExpandRow,
    },
    {
      title: ' ',
      key: '',
      width: 50,
      isShow: isExpandRow,
    },
    {
      type: 'expand',
      width: 50,
      isShow: !isExpandRow,
      render: (h, params) => {
        return h(TaskExpandRow, {
          props: {
            taskList: params.row.orderTaskList,
          },
          on: {
            onStatisticsClick(_row, _column, _index) {
              _this.$emit('onStatisticsClick', _row, _column, _index);
            },
          },
        });
      },
    },
    {
      title: '任务名称',
      key: 'taskName',
      tooltip: true,
      // tree: true,
      width: 268,
      isShow: true,
    },
    {
      title: '工单总量',
      key: 'total',
      queryConditionStatus: 'qb',
      tooltip: true,
      minWidth: 120,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '待签收',
      key: 'dqsAmount',
      queryConditionStatus: 'dqs',
      tooltip: true,
      minWidth: 120,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link-warning'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '待处理',
      key: 'dclAmount',
      queryConditionStatus: 'dcl',
      tooltip: true,
      minWidth: 110,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '检测不通过',
      key: 'jcbtgAmount',
      queryConditionStatus: 'jcbtg',
      tooltip: true,
      minWidth: 110,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '待关闭',
      key: 'dgbAmount',
      queryConditionStatus: 'dgb',
      tooltip: true,
      minWidth: 110,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '已报备',
      key: 'ybbAmount',
      queryConditionStatus: 'ybb',
      tooltip: true,
      minWidth: 100,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '已关闭',
      key: 'ygbAmount',
      queryConditionStatus: 'ygb',
      tooltip: true,
      minWidth: 120,
      sortable: true,
      isShow: true,
      render: (h, { row, index, column }) => {
        return (
          <span
            class={'link'}
            onClick={() => {
              _this.$emit('onStatisticsClick', row, column, index);
            }}
          >
            {row[column.key] || 0}
          </span>
        );
      },
    },
    {
      title: '完成率',
      key: 'rate',
      slot: 'rate',
      width: 230,
      isShow: true,
    },
    {
      width: 200,
      title: '截止时间',
      key: 'taskPlannedDate',
      tooltip: true,
      isShow: true,
    },
    {
      width: 200,
      title: '创建人',
      key: 'createName',
      tooltip: true,
      isShow: true,
    },
    {
      title: ' ',
      key: '',
      width: 20,
      fixed: 'right',
      isShow: true,
    },
    {
      width: 135,
      title: '操作',
      slot: 'action',
      fixed: 'right',
      className: 'table-action-padding',
      isShow: true,
    },
  ];
};
