<template>
  <div class="equipmentlibraryList auto-fill">
    <div class="auto-fill">
      <div class="head-title">
        <span class="title-headline">{{ titleName || '暂无' }}-视图数据检测情况总览</span>
        <div class="org-region inline">
          <ui-label class="inline" label="行政区划" v-if="statisticType === 'REGION'">
            <api-area-tree
              ref="apiAreaTree"
              :filter-tree-code="taskConfig.selfRegionCode"
              :select-tree="searchData"
              @selectedTree="selectedArea"
              placeholder="请选择行政区划"
            >
            </api-area-tree>
          </ui-label>
          <ui-label class="inline" label="组织机构" v-else-if="statisticType === 'ORG'">
            <api-organization-tree
              ref="orgCode"
              :filter-tree-code="taskConfig.selfOrgCode"
              :select-tree="searchData"
              @selectedTree="selectedOrgTree"
              placeholder="请选择组织机构"
            ></api-organization-tree>
          </ui-label>
        </div>
        <el-popover
          popper-class="popover"
          visible-arrow="false"
          placement="bottom"
          v-model="visible"
          trigger="click"
          @show="showPopover"
          @hide="hidePopover"
        >
          <div class="form-content">
            <ui-label class="mb-lg" label="检测任务" :width="65">
              <Select v-model="tempTaskType" placeholder="请选择检测任务" class="width-input" filterable>
                <Option v-for="(item, index) in taskList" :key="index" :label="item.taskName" :value="item.id">
                  {{ item.taskName }}
                </Option>
              </Select>
            </ui-label>
            <ui-label class="mb-lg" label="统计方式" :width="65">
              <Select v-model="tempStatisticType" placeholder="请选择" class="width-input">
                <Option v-for="(item, index) in statisticsList" :key="index" :label="item.name" :value="item.type">
                  {{ item.name }}
                </Option>
              </Select>
            </ui-label>
            <div class="mt-md t-center">
              <Button class="ml-sm" :loading="taskLoading" type="primary" @click="onClickSubmit">确 定</Button>
            </div>
          </div>
          <i class="icon-font icon-shaixuan f-16 screen" slot="reference"></i>
        </el-popover>
      </div>
      <div class="statistic-list">
        <ul>
          <li v-for="(item, index) in statisticList" :key="index" :class="item.listBg" class="mr-sm">
            <div class="monitoring-data">
              <em class="icon-font f-50 pl20" :class="[item.icon, item.iconBg]"></em>
              <span>
                <p>{{ item.name }}</p>
                <p class="statistic-num">
                  {{ item.value || 0 }}
                </p>
              </span>
            </div>
          </li>
        </ul>
      </div>
      <div class="search-module">
        <ui-label class="inline mr-lg" label="指标名称">
          <Input v-model="searchData.indexName" class="width-md" placeholder="请输入指标名称"></Input>
        </ui-label>
        <ui-label class="inline mr-lg" label="指标类型">
          <Select
            class="width-md"
            v-model="searchData.indexModule"
            :placeholder="`请选择指标类型`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in global.indexTypeList" :key="index" :value="item.id"
              >{{ item.title }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg" label="达标情况">
          <Select
            class="width-md"
            v-model="searchData.qualified"
            :placeholder="`请选择达标情况`"
            clearable
            :max-tag-count="1"
          >
            <Option value="1" label="达标"></Option>
            <Option value="2" label="不达标"></Option>
            <!-- <Option value="3"
                    label="无法检测"></Option> -->
          </Select>
        </ui-label>
        <div class="right-btn inline">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button class="ml-sm" @click="reset">重置</Button>
        </div>
      </div>
      <ui-table
        reserveSelection
        class="ui-table auto-fill"
        :loading="tableLoading"
        :table-columns="tableColumns"
        :table-data="tableData"
      >
        <template #indexName="{ row }">
          <div class="text-icon-box">
            <span>{{ row.indexName }}</span>
            <i
              class="icon-font icon-wenhao vt-middle icon-warning ml-sm icon-details"
              @click="openDetailsDialog(row)"
            ></i>
          </div>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            slot="action"
            class="operatbtn"
            icon="icon-chakanxiangqing"
            content="检测详情"
            @click.native="handleRemove(row)"
          ></ui-btn-tip>
        </template>
        <template #resultValue="{ row }">
          <span class="font-warning" v-if="row.qualified === '3'"> - - </span>
          <span v-else :class="row.qualified === '1' ? 'color-success' : 'font-warning'">
            {{ row.resultValue !== null ? `${row.resultValue}%` : '- -' }}
          </span>
        </template>
        <template #situation="{ row }">
          <span class="font-warning" v-if="row.qualified === '3' || !row.qualified"> - - </span>
          <span
            v-else
            class="icon-font"
            :class="row.qualified === '1' ? ['icon-dabiao', 'color-success'] : ['icon-budabiao', 'color-failed']"
          ></span>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
    </div>
    <!-- 指标配置详情 -->
    <common-model ref="model" v-model="indexVisible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import evaluationreport from '@/config/api/evaluationreport';
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'equipmentlibraryList',
  props: {},
  mixins: [evaluationoResultMixin],
  data() {
    return {
      currentRow: {},
      indexVisible: false,
      visible: false,
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      titleName: '',
      searchData: {
        indexName: '',
        indexModule: '',
        qualified: '',
        pageNumber: 1,
        pageSize: 20,
        orgCode: '',
        regionCode: '',
      },
      tableLoading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      phystatusList: [],
      countList: [
        { title: '设备总量', count: '0', icon: 'icon-equipmentlibrary' },
        { title: '视频监控', count: '0', icon: 'icon-ivdg-shipinjiankong' },
        { title: '人脸卡口', count: '0', icon: 'icon-renliankakou' },
        { title: '车辆卡口', countKey: '0', icon: 'icon-cheliangkakou' },
      ],
      activeType: {
        sort: '',
        sortField: '',
      },
      tableColumns: [
        { title: '序号', type: 'index', align: 'center', width: 50 },
        { title: '指标名称', key: 'indexName', slot: 'indexName' },
        { title: '达标值', key: 'standardsValue' },
        {
          title: '指标类型',
          key: 'indexModuleName',
          sortable: true,
          sortMethod: (a, b, type) => {
            this.initSort(type.toUpperCase(), 'INDEX_MODULE');
          },
        },
        {
          title: '最新检测值',
          slot: 'resultValue',
          sortable: true,
          sortMethod: (a, b, type) => {
            this.initSort(type.toUpperCase(), 'RESULT_VALUE');
          },
        },
        {
          title: '最新检测时间',
          key: 'createTime',
          sortable: true,
          sortMethod: (a, b, type) => {
            this.initSort(type.toUpperCase(), 'CREATE_TIME');
          },
        },
        { title: '达标情况', slot: 'situation', width: 80, align: 'center' },
        {
          title: '操作',
          width: 80,
          align: 'center',
          slot: 'action',
          fixed: 'right',
        },
      ],
      tableData: [],
      statisticList: [
        {
          name: '检测指标总数',
          value: 0,
          icon: 'icon-pingcezhibiaozonglan',
          iconBg: 'icon-bg1',
          listBg: 'list-bg1',
          key: 'checkIndexNum',
        },
        {
          name: '达标指标数量',
          value: 0,
          icon: 'icon-dabiao',
          iconBg: 'icon-bg7',
          listBg: 'list-bg7',
          key: 'qualifiedIndexTotalNum',
        },
        {
          name: '不达标指标数量',
          value: 0,
          icon: 'icon-budabiao',
          iconBg: 'icon-bg6',
          listBg: 'list-bg6',
          key: 'unqualifiedIndexTotalNum',
        },
        {
          name: '未出结果指标数量',
          value: 0,
          icon: 'icon-weichujieguozhibiaoshuliang',
          iconBg: 'icon-bg4',
          listBg: 'list-bg4',
          key: 'unableIndexTotalNum',
        },
        {
          name: '视频监控数量',
          value: 0,
          icon: 'icon-ivdg-shipinjiankong',
          iconBg: 'icon-bg5',
          listBg: 'list-bg5',
          key: 'videoSurveillanceAmount',
        },
        {
          name: '车辆卡口数量',
          value: 0,
          icon: 'icon-cheliangkakou',
          iconBg: 'icon-bg3',
          listBg: 'list-bg3',
          key: 'vehicleBayonetAmount',
        },
        {
          name: '人脸卡口数量',
          value: 0,
          icon: 'icon-renliankakou',
          iconBg: 'icon-bg2',
          listBg: 'list-bg2',
          key: 'faceSwanAmount',
        },
      ],
      taskConfig: {},
      taskList: [],
      taskType: '',
      statisticType: '',
      statisticsList: [
        { name: '按组织机构统计', type: 'ORG' },
        { name: '按行政区划统计', type: 'REGION' },
      ],
      taskLoading: false,
      //临时参数 点确定时生效
      tempTaskType: '',
      tempStatisticType: '',
    };
  },
  async created() {
    await this.setAreaList();
    await this.setOrganizationList();
    await this.setDefaultStatisticType();
    await this.getListTaskSchemes();
    await this.setDefaultTask();
    await this.getOptionalBatchIdsByTaskSchemeId();
    await this.setDefaultOrgRegionCode();
    // this.queryDeviceStatic()
    // this.queryIndexDeviceOverview()
    // 不copy orgCode, regionCode 字段
    let { orgCode, regionCode, ...rest } = this.searchData;
    this.copySearchDataMx(rest);
    this.searchCode();
  },
  methods: {
    ...mapActions({
      setAreaList: 'common/setAreaList',
      setOrganizationList: 'common/setOrganizationList',
    }),
    showPopover() {
      this.tempTaskType = this.taskType;
      this.tempStatisticType = this.statisticType;
    },
    hidePopover() {
      this.taskType = this.tempTaskType;
      this.statisticType = this.tempStatisticType;
    },
    setDefaultStatisticType() {
      let { statisticType } = this.$route.query;
      this.statisticType = statisticType || 'REGION';
    },
    setDefaultTask() {
      // 设置默认任务和默认的行政区划/组织机构
      if (this.taskList.length > 0) {
        let { taskSchemeId } = this.$route.query;
        this.taskType = taskSchemeId || this.taskList[0]['id'];
      }
    },

    /**
     * 1、首次进入时 获取taskConfig 中的 selfRegionCode || selfOrgCode 作为默认值 <br>
     * 2、刷新时 获取 $route 作为默认值 <br>
     * 3、判断用户是否有对应默认值的权限 如果有权限就正常设置默认值，如果没有权限就默认有权限的第0个（regionType 最高的那个）<br>
     * @param source 默认 刷新时 从$route获取 source === taskConfig 切换检测任务从 taskConfig获取<br>
     */
    setDefaultOrgRegionCode(source) {
      let { regionCode, orgCode } = this.$route.query;
      let newRegionCode = '';
      let newOrgCode = '';
      if (source) {
        newRegionCode = this.statisticType === 'REGION' ? this.taskConfig.selfRegionCode : '';
        newOrgCode = this.statisticType === 'ORG' ? this.taskConfig.selfOrgCode : '';
      } else {
        newRegionCode = this.statisticType === 'REGION' ? regionCode || this.taskConfig.selfRegionCode : '';
        newOrgCode = this.statisticType === 'ORG' ? orgCode || this.taskConfig.selfOrgCode : '';
      }
      let { initialAreaList, initialOrgList } = this.getInitialAreaOrgList(); //evaluationoResultMixin.js
      if (this.statisticType === 'REGION') {
        let areaIndex = initialAreaList.findIndex((item) => item.regionCode === newRegionCode);
        if (areaIndex === -1) {
          newRegionCode = initialAreaList[0]['regionCode'] || '';
        } else {
          this.titleName = initialAreaList[areaIndex]['regionName'];
        }
        this.searchData.regionCode = newRegionCode;
        this.searchData.orgCode = '';
      } else {
        let orgIndex = initialOrgList.findIndex((item) => item.orgCode === newOrgCode);
        if (orgIndex === -1) {
          newOrgCode = initialOrgList[0]['orgCode'] || '';
        } else {
          this.titleName = initialOrgList[orgIndex]['orgName'];
        }
        this.searchData.orgCode = newOrgCode;
        this.searchData.regionCode = '';
      }
    },
    setParamsQuery() {
      let defaultQuery = {
        showResult: this.$route.query.showResult,
        orgCode: this.statisticType === 'ORG' ? this.searchData.orgCode : '',
        regionCode: this.statisticType === 'REGION' ? this.searchData.regionCode : '',
        statisticType: this.statisticType,
        taskSchemeId: this.taskType,
      };
      this.$router.push({
        name: 'evaluationoResult',
        query: defaultQuery,
      });
    },
    async getListTaskSchemes() {
      try {
        this.taskList = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.taskList = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.titleName = val.orgName;
      this.searchCode();
    },
    initSort(type, sortField) {
      if (this.activeType.sort === type && this.activeType.sortField === sortField) {
        return;
      }
      this.initList({
        sort: type,
        sortField: sortField,
      });
      this.searchData.sort = type;
      this.searchData.sortField = sortField;
      this.activeType.sort = type;
      this.activeType.sortField = sortField;
    },
    activeTabsQuery(row) {
      return {
        displayType: 'REGION',
        indexId: row.indexId,
        code: row.regionCode,
        batchId: row.batchId,
        access: 'EXAM_RESULT',
        uuid: row.batchId,
      };
    },
    selectedArea(area) {
      this.searchData.regionCode = area.regionCode;
      this.titleName = area.regionName;
      this.searchCode();
    },
    async onClickSubmit() {
      this.taskType = this.tempTaskType;
      this.statisticType = this.tempStatisticType;
      await this.getOptionalBatchIdsByTaskSchemeId();
      this.visible = false;
      this.setDefaultOrgRegionCode('taskConfig');
      this.searchCode();
    },
    async searchCode() {
      this.setParamsQuery();
      // await this.getOptionalBatchIdsByTaskSchemeId()
      this.initList();
      this.queryDeviceStatic();
      this.queryIndexDeviceOverview();
    },
    // 头部统计(前面三个)
    async queryIndexDeviceOverview() {
      let param = {
        orgRegionFlag: this.statisticType,
        orgRegeionCode: this.statisticType === 'REGION' ? this.searchData.regionCode : this.searchData.orgCode,
      };
      try {
        let {
          data: {
            data: { data },
          },
        } = await this.$http.get(evaluationoverview.getDeviceOverview, {
          params: param,
        });
        this.statisticList.forEach((item) => {
          item.key in data ? (item.value = data[item.key]) : null;
        });
      } catch (err) {
        console.log(err);
      }
    },
    async queryDeviceStatic() {
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getIndexDataCount, {
          displayType: this.statisticType,
          regionCode: this.statisticType === 'REGION' ? this.searchData.regionCode : this.searchData.orgCode,
          batchIds: this.taskConfig.batchIds,
        });
        this.statisticList.forEach((item) => {
          item.key in data ? (item.value = data[item.key]) : null;
        });
      } catch (err) {
        console.log('err', err);
      }
    },
    async getOptionalBatchIdsByTaskSchemeId() {
      try {
        this.taskLoading = true;
        let res = null;
        let { showResult } = this.$route.query;
        if (showResult) {
          res = await this.$http.get(evaluationreport.getOptionalBatchIdsByTaskSchemeId, {
            params: {
              taskSchemeId: this.taskType,
            },
          });
        } else {
          res = await this.$http.post(evaluationreport.getOptionalBatchIdsByTaskSchemeIdV2, {
            taskSchemeId: this.taskType,
            showResult: 1,
          });
        }
        let data = res.data.data || {};
        this.taskConfig = data || {};
      } catch (err) {
        console.log(err);
      } finally {
        this.taskLoading = false;
      }
    },
    handleRemove(row) {
      if (!this.isExistIndex(row.indexType) || this.isSpecialassessment(row.indexType)) {
        return this.$Message.error('当前选中指标暂无数据');
      }
      this.$emit('openIndexId', row);
    },
    async initList(data = {}) {
      this.tableLoading = true;
      let params = {
        ...this.searchData,
        orgCode: this.statisticType === 'ORG' ? this.searchData.orgCode : '',
        regionCode: this.statisticType === 'REGION' ? this.searchData.regionCode : '',
        batchIds: this.taskConfig.batchIds,
        displayType: this.statisticType,
      };
      Object.assign(params, data);
      try {
        let { data } = await this.$http.post(governanceevaluation.getPageJiangsuEvaluationIndexResult, params);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.tableLoading = false;
      }
    },
    startSearch() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initList();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.startSearch);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.initList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.searchData.pageNumber = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    // 指标详情
    openDetailsDialog(row) {
      let { indexType, indexName, indexId } = row;
      this.currentRow = {
        indexType: indexType,
        indexName: indexName,
        id: indexId,
      };
      this.indexVisible = true;
    },
  },
  watch: {
    $route() {
      this.setDefaultTask();
      this.setDefaultStatisticType();
      this.setDefaultOrgRegionCode();
      this.initList();
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      treeData: 'common/getAreaList',
      getDefaultSelectedArea: 'common/getDefaultSelectedArea',
      getInitialOrgList: 'common/getInitialOrgList',
      getInitialAreaList: 'common/getInitialAreaList',
    }),
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .equipmentlibraryList {
    background-color: #ffffff;

    .head-title {
      .title-time {
        color: #ffffff;
        opacity: 1;
        position: absolute;
        left: 20px;
      }

      .title-headline {
        color: rgba(0, 0, 0, 0.9);
      }

      .screen {
        position: absolute;
        right: 20px;
        color: var(--color-el-tree-node__expand-icon);

        &:hover {
          color: var(--color-el-tree-node-hover);
        }
      }
    }

    .statistic-list {
      ul {
        .list-bg1 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-1.png') no-repeat;
          background-size: cover;
        }
        .list-bg2 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-2.png') no-repeat;
          background-size: cover;
        }
        .list-bg3 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-3.png') no-repeat;
          background-size: cover;
        }
        .list-bg4 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-4.png') no-repeat;
          background-size: cover;
        }
        .list-bg5 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-5.png') no-repeat;
          background-size: cover;
        }
        .list-bg6 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-6.png') no-repeat;
          background-size: cover;
        }
        .list-bg7 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-7.png') no-repeat;
          background-size: cover;
        }
      }
    }

    //.icon-dabiao {
    //  color: #ffffff !important;
    //}
    //.icon-budabiao {
    //  color: #ffffff !important;
    //}
    .text-icon-box {
      display: flex;
      align-items: center;
      flex-direction: row;
      .icon-details {
        width: 18px;
        display: none;
      }
    }
    /deep/ .ivu-table-row:hover {
      .icon-details {
        display: block;
      }
    }
  }
}
.equipmentlibraryList {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  padding: 0 10px;
  height: 100%;

  .head-title {
    margin-top: 10px;
    height: 56px;
    line-height: 56px;
    background: var(--bg-title-card);
    position: relative;
    // opacity: 0.63;
    text-align: center;

    .title-time {
      font-size: 14px;
      color: #ffffff;
      opacity: 1;
      position: absolute;
      left: 20px;
    }

    .title-headline {
      font-size: 24px;
      font-weight: bold;
      color: #0df6e7;
      opacity: 1;
    }

    .screen {
      position: absolute;
      right: 20px;
      color: #56789c;

      &:hover {
        color: var(--color-primary);
      }
    }

    .org-region {
      float: right;
      margin-right: 50px;
    }
  }

  .search-module {
    margin: 10px 0;
  }

  .statistic-list {
    width: 100%;
    margin: 10px 0 0;
    height: 108px;

    ul {
      width: 100%;
      height: 108px;
      display: flex;
      justify-content: center;
      align-items: center;

      li {
        &:last-child {
          margin-right: 0;
        }
        flex: 1;
        height: 108px;
        display: flex;
        align-items: center;
        border-radius: 10px;

        .track-data {
          border-right: none;
        }

        div {
          height: 56px;
          width: 100%;
          // border-right: 1px solid #094a8a;
          line-height: 56px;
          display: flex;
          justify-content: center;
          align-items: center;

          em {
            display: inline-block;
            height: 56px;
            width: 56px;
            line-height: 56px;
            text-align: center;
            border-radius: 50%;
            background: rgba(218, 245, 248, 0.2);
          }

          .f-50 {
            font-size: 30px;
            color: #fff !important;
          }

          .pl20 {
            margin-left: 20px;
          }

          span {
            display: inline-block;
            height: 50px;
            flex: 1;
            margin-left: 20px;
            text-align: left;

            p {
              white-space: nowrap;
              font-style: normal;
              height: 25px;
              line-height: 25px;
              color: #f5f5f5;
              font-size: 12px;
            }

            .statistic-num {
              font-size: 24px;
              color: #f5f5f5;
              -webkit-text-stroke: 1 rgba(0, 0, 0, 0);
              opacity: 1;
              font-family: 'Microsoft YaHei';
            }
          }
        }
      }

      .list-bg1 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg1.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }

      .list-bg2 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg2.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }

      .list-bg3 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg3.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }

      .list-bg4 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg4.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }

      .list-bg5 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg5.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }

      .list-bg6 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg6.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }

      .list-bg7 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg7.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
    }
  }

  //.icon-dabiao {
  //  color: #ffffff !important;
  //}
  //.icon-budabiao {
  //  color: #ffffff !important;
  //}
  .text-icon-box {
    display: flex;
    align-items: center;
    flex-direction: row;
    .icon-details {
      width: 18px;
      display: none;
    }
  }
  /deep/ .ivu-table-row:hover {
    .icon-details {
      display: block;
    }
  }
}
</style>
