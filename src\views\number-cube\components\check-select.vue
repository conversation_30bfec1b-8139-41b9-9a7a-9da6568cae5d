<template>
  <!-- 实体类型筛选 -->
  <Collapse simple class="check-select" v-model="value1">
    <Panel name="1">
      <Checkbox @click.prevent.stop.native="handleCheckAll" :value="checkAll">{{type == 'entity' ? '全部实体' : '全部关系'}}</Checkbox>
      <div slot="content">
        <CheckboxGroup v-model="checkAllGroup" @on-change="changeCheckAll">
          <div v-for="(item,index) in list" :key="index" class="check-select-item">
            <Checkbox :label="item.id">{{ item.label }}
              <!-- ( {{ item.num }} ) -->
            </Checkbox>
          </div>
        </CheckboxGroup>
      </div>
    </Panel>
  </Collapse>
</template>
<script>
export default {
  components: {},
  props: {
    type: {
      type: String,
      default: "entity"
    },
    atlasListChilren: {
      type: [],
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      checkAll: true,
      checkAllGroup: [],
      list: [],
      value1: '1',
      currentType: '',   // 当前type
    }
  },
  computed: {},
  watch: {
    atlasListChilren: {
      deep: true,
      handler(val) {
        this.checkAllGroup = []
        let group = []
        if (val) {
          console.log('atlasListChilren---------------------', val, this.type)
          val.map(item => {
            if (this.type == "entity") {  // 实体
              let finds = group.find(items => {
                return items.id === item.label
              })
              if (group.length === 0) {
                group.push({ label: item.labelCn, num: 1, id: item.label })
              } else if (finds && finds.id === item.label) {
                group.find(items => {
                  return item.label === item.label
                }).num += 1
              } else {
                group.push({ label: item.labelCn, num: 1, id: item.label })
              }


              
            } else {   // 关系
              let finds = group.find(items => {
                return items.id === item.data.details[0].label
              })
              if (group.length === 0) {
                group.push({ label: item.data.details[0].labelCn, num: 1, id: item.data.details[0].label })
              } else if (finds && finds.id === item.data.details[0].label) {
                group.find(items => {
                  return item.label === item.data.details[0].labelCn
                }).num += 1
              } else {
                group.push({ label: item.data.details[0].labelCn, num: 1, id: item.data.details[0].label })
              }

              
            }
          })
        }
        this.list = group
        setTimeout(() => {
          this.checkAllGroup = group.map(val => {
            return val.id
          })
          this.checkAll = false
          this.handleCheckAll()
        }, 20);
        // if (this.type == "entity") {
        //   // this.checkAllGroup = group.map(val => {
        //   //   return val.id
        //   // })
        //   this.checkAllGroup = this.atlasListChilren.map(val => {
        //     return val.data.label
        //   })
        //   console.log('-----------1', this.checkAllGroup, group)
        // } else {
        //   this.checkAllGroup = this.atlasListChilren.map(val => {
        //     return val.data.details[0].label
        //   })
        //   console.log('-----------2', this.checkAllGroup, group)
        // }

        // this.$forceUpdate()

      },
      immediate: true
    },
    checkAllGroup: {
      // deep: true,
      handler(val) {
        // this.$emit('hidenShiti', this.checkAllGroup)
      }
      // immediate: true
    },
    /**
     * 筛选类型
     */
    type (val, old) {
      console.log('筛选类型type-----------', val, old)
      if(this.currentType == ''){ // 初次进入
        this.currentType = val
        this.checkAll = true
      }else {

      }
    }
  },
  filter: {}, 
  created() {},
  mounted() {
    this.init()
  },
  methods: {
    // 获取
    init() {
      console.log(this, '=======')
    },
    // 全选
    handleCheckAll() {
      this.checkAll = !this.checkAll
      if (!this.checkAll) {
        this.checkAllGroup = []
      } else {
        if (this.type == "entity") {  // 实体
          this.checkAllGroup = this.atlasListChilren.map(val => {
            return val.data.label
          })
        }else{
          this.checkAllGroup = this.atlasListChilren.map(val => {
            return val.data.details[0].label
          })
        }
        this.checkAllGroup = Array.from(new Set(this.checkAllGroup))
        // console.log('全部选中---------', this.checkAllGroup, this.atlasListChilren)
      }
      this.$emit('checkSelect', this.checkAllGroup)
    },
    // 单选
    changeCheckAll(checkArray) {
      console.log('单选筛选-------------', checkArray)
      if (checkArray.length === this.list.length) {
        this.checkAll = true
      } else {
        this.checkAll = false
      }
      this.$emit('checkSelect', this.checkAllGroup)
    }
  }
}
</script>
<style lang="less" scoped>
.check-select {
  position: absolute;
  top: 300px;
  width: 230px;
  left: 20px;
  z-index: 11;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  .check-select-item {
    margin-top: 20px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
  }
  /deep/ .ivu-collapse-header { 
    border-bottom: 1px solid #d3d7de !important;
    .ivu-icon {
      float: right;
      position: relative;
      top: 9px;
      font-size: 20px;
      &::before {
        content: '\F33D';
      }
    }
  }
  /deep/ .ivu-collapse-item-active > .ivu-collapse-header > i {
    transform: rotate(180deg);
  }
}
</style>
