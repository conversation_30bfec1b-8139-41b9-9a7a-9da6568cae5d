<template>
  <div class="asset-statistics height-full">
    <head-title @changeTab="changeTab" :home-config="homePageConfig"></head-title>
    <div class="asset-statistics-echarts">
      <statistics-box class="mt-sm mb-sm" :statistic-list="statisticList"></statistics-box>
      <!-- 功能类型区域分布、点位类型区域分布 -->
      <div class="d_flex mb-sm">
        <function-distribute class="mr-sm" :home-config="homePageConfig" :active-tab="activeTab"></function-distribute>
        <point-distribute :home-config="homePageConfig" :active-tab="activeTab"></point-distribute>
      </div>
      <div class="pie-container mb-sm">
        <pie-chart
          :home-config="homePageConfig"
          :active-tab="activeTab"
          :statistic-list="statisticList"
          v-for="(item, index) in pieData"
          :config="item"
          :key="index"
        >
          <template #deviceFilter="{ initList, searchData }">
            <drop-checkbox @on-change="(val) => handleChangeSelect(val, initList, searchData)"></drop-checkbox>
          </template>

          <template #amount="{ data, item }">
            <span class="mr-xs" :style="`color: ${item.color[1]}; `">{{ data.amountRate }}%</span>
          </template>
          <template #unAmount="{ data, item }">
            <span class="mr-xs" :style="`color: ${item.color[1]}; `">{{ data.unAmountRate }}%</span>
          </template>
        </pie-chart>
      </div>
      <div class="d_flex mb-sm">
        <!-- [设备状态区域分布]、[视频在线状态区域分布] -->
        <device-status-distribute
          class="mr-sm"
          :home-config="homePageConfig"
          :active-tab="activeTab"
        ></device-status-distribute>
        <online-status-distribute :home-config="homePageConfig" :active-tab="activeTab"></online-status-distribute>
      </div>
      <div class="quantitative-trend-container mb-sm">
        <!--   数量变化趋势    -->
        <quantitative-trend :home-config="homePageConfig" :active-tab="activeTab"></quantitative-trend>
      </div>
      <!-- 采集类型区域分布 -->
      <area-distribute :home-config="homePageConfig" :active-tab="activeTab"></area-distribute>
    </div>
  </div>
</template>
<script>
import { statisticList } from './utils/enum';
import home from '@/config/api/home';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  name: 'assetstatistics',
  props: {},
  data() {
    return {
      breadcrumbData: [],
      homePageConfig: {},
      activeTab: '1',
      pieData: [
        {
          filterSlot: 'deviceFilter',
          statisticKey: 'deviceTotalAmount',
          tabData: [{ label: '设备状态统计', id: 'phystatus_overview' }],
          legend: [
            {
              key: 'amount',
              label: '可用设备',
              color: [$var('--linear-gradient-green-1-start'), $var('--linear-gradient-green-1-end')],
            },
            {
              key: 'unAmount',
              label: '不可用设备',
              color: [$var('--linear-gradient-orange-1-start'), $var('--linear-gradient-orange-1-end')],
            },
            {
              key: 'undetectedAmount',
              label: '未知',
              color: [$var('--linear-gradient-yellow-1-start'), $var('--linear-gradient-yellow-1-end')],
              hidden: '1',
            },
          ],
        },
        {
          filterSlot: 'deviceFilter',
          tabData: [{ label: '重点类型统计', id: 'important_overview' }],
          legend: [
            {
              key: 'amount',
              label: '普通设备',
              color: [$var('--linear-gradient-blue-1-start'), $var('--linear-gradient-blue-1-end')],
            },
            {
              key: 'unAmount',
              label: '重点设备',
              color: [$var('--linear-gradient-yellow-1-start'), $var('--linear-gradient-yellow-1-end')],
            },
          ],
        },
        {
          filterSlot: 'deviceFilter',
          statisticKey: 'deviceTotalAmount',
          tabData: [{ label: '资产质量统计', id: 'checkstatus_overview' }],
          legend: [
            {
              key: 'amount',
              label: '合格数量',
              color: [$var('--linear-gradient-green-2-start'), $var('--linear-gradient-green-2-end')],
            },
            {
              key: 'unAmount',
              label: '不合格数量',
              color: [$var('--linear-gradient-orange-1-start'), $var('--linear-gradient-orange-1-end')],
            },
            {
              key: 'undetectedAmount',
              label: '未检测数量',
              color: [$var('--linear-gradient-yellow-1-start'), $var('--linear-gradient-yellow-1-end')],
              hidden: '1',
            },
          ],
        },
        {
          statisticKey: 'videoSurveillanceAmount',
          tabData: [{ label: '视频监控在线统计', id: 'videostatus_overview' }],
          legend: [
            {
              key: 'amount',
              label: '在线设备',
              color: [$var('--linear-gradient-cyan-1-start'), $var('--linear-gradient-cyan-1-end')],
            },
            {
              key: 'unAmount',
              label: '离线设备',
              color: [$var('--linear-gradient-orange-1-start'), $var('--linear-gradient-orange-1-end')],
            },
            {
              key: 'undetectedAmount',
              label: '未检测数量',
              color: [$var('--linear-gradient-yellow-1-start'), $var('--linear-gradient-yellow-1-end')],
              hidden: '1',
            },
          ],
        },
        {
          statisticKey: 'faceSwanAmount',
          tabData: [{ label: '人脸卡口在线统计', id: 'facestatus_overview' }],
          legend: [
            {
              key: 'amount',
              label: '在线设备',
              color: [$var('--linear-gradient-purple-1-start'), $var('--linear-gradient-purple-1-end')],
            },
            {
              key: 'unAmount',
              label: '离线设备 ',
              color: [$var('--linear-gradient-orange-1-start'), $var('--linear-gradient-orange-1-end')],
            },
            {
              key: 'undetectedAmount',
              label: '未检测数量',
              color: [$var('--linear-gradient-yellow-1-start'), $var('--linear-gradient-yellow-1-end')],
              hidden: '1',
            },
          ],
        },
        {
          statisticKey: 'vehicleBayonetAmount',
          tabData: [{ label: '车辆卡口在线统计', id: 'vehiclestatus_overview' }],
          legend: [
            {
              key: 'amount',
              label: '在线设备',
              color: [$var('--linear-gradient-blue-2-start'), $var('--linear-gradient-blue-2-end')],
            },
            {
              key: 'unAmount',
              label: '离线设备 ',
              color: [$var('--linear-gradient-orange-1-start'), $var('--linear-gradient-orange-1-end')],
            },
            {
              key: 'undetectedAmount',
              label: '未检测数量',
              color: [$var('--linear-gradient-yellow-1-start'), $var('--linear-gradient-yellow-1-end')],
              hidden: '1',
            },
          ],
        },
      ],
      statisticList: statisticList,
    };
  },
  async created() {
    await this.viewByParamKey();
    this.queryStatisticsList();
  },
  methods: {
    changeTab(val) {
      this.activeTab = val;
      this.queryStatisticsList();
    },
    async viewByParamKey() {
      try {
        let params = { key: 'HOME_PAGE_CONFIG' };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        this.homePageConfig = JSON.parse(data.paramValue || '{}');
      } catch (e) {
        console.log(e);
      }
    },
    handleChangeSelect(val, initList, searchData) {
      Object.keys(val).forEach((key) => {
        searchData[key] = val[key];
      });
      initList();
    },
    /**
     * 头部统计
     */
    // STATISTICS_OVERVIEW("statistics_overview","设备头部汇总统计"),
    async queryStatisticsList() {
      let { regionCode } = this.homePageConfig;
      let params = {
        civilCode: regionCode,
        type: this.activeTab,
        dataKey: 'statistics_overview',
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.postQueryOneAssertDeviceStatistics, params);
        this.statisticList = statisticList.map((item) => {
          let one = {
            ...item,
            value: data?.[item?.filedName] ?? 0,
          };
          if (one?.juniorList) {
            one.juniorList = one.juniorList.map((juniorItem) => {
              return {
                ...juniorItem,
                num: data[item.superiorName][juniorItem.filedName] ?? 0,
              };
            });
          }
          return one;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
  },
  watch: {},
  components: {
    HeadTitle: require('./components/head-title').default,
    StatisticsBox: require('./components/statistics-box').default,
    PieChart: require('./echarts-modules/pie-chart.vue').default,
    QuantitativeTrend: require('./echarts-modules/quantitative-trend.vue').default,
    FunctionDistribute: require('./echarts-modules/function-distribute.vue').default,
    PointDistribute: require('./echarts-modules/point-distribute.vue').default,
    AreaDistribute: require('./echarts-modules/area-distribute.vue').default,
    DeviceStatusDistribute: require('./echarts-modules/device-status-distribute.vue').default,
    OnlineStatusDistribute: require('./echarts-modules/online-status-distribute.vue').default,
    DropCheckbox: require('./components/drop-checkbox.vue').default,
  },
};
</script>
<style lang="less" scoped>
.asset-statistics {
  .pie-container {
    display: flex;
    @{_deep} .pie-chart-container {
      margin-right: 10px;
      width: calc(100% / 6);
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .quantitative-trend-container {
    height: 352px;
  }
  background: var(--bg-content);
  padding: 15px;
  .asset-statistics-echarts {
    height: calc(100% - 56px);
    overflow-y: scroll;
    overflow-x: hidden;
  }
}
</style>
