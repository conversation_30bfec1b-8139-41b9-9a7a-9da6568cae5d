<template>
  <ui-modal
    v-model="visible"
    :title="`请选择${modalTitle}`"
    class-name="choose-device-container"
    width="96rem"
    @onCancel="onCancel"
  >
    <div class="content-wrap">
      <section class="left-content pt-50">
        <ui-search-tree
          class="padding20 auto-fill ui-search-tree search-tree-box"
          ref="uiSearchTree"
          placeholder="请输入平台名称"
          :expandAll="true"
          :tree-loading="treeLoading"
          :node-key="treeNodeKey"
          :tree-data="treeData"
          :default-props="defaultProps"
          :click-disabled-item-show="false"
          @selectTree="selectTree"
        >
          <template #label="{ node, data }">
            <div
              class="tree-node-box-item"
              :class="{
                'nav-not-selected': node.disabled,
                allowed: node.disabled,
              }"
            >
              {{ node.label }}
              <img class="ml-sm img-gouxuan" src="~@/assets/img/device-map/gouxuan.png" v-if="showIcon(data)" />
              <!-- 遮罩 -->
              <div :class="{ 'nav-not-selected-mask': node.disabled }" @click.stop></div>
            </div>
          </template>
        </ui-search-tree>
      </section>
      <section class="right-content auto-fill pt-50">
        <underline-menu
          v-model="chooseMenu.chooseType"
          :data="chooseMenu.chooseTypeList"
          @on-change="changeChooseType"
          class="mb-sm right-header-tab"
        >
        </underline-menu>

        <!-- 资产库中选择 显示 -->
        <template v-if="chooseMenu.chooseType === 'default'">
          <div class="search-box content-title">
            <div class="search-top">
              <div class="search-header">
                <!-- 搜索条件 -->
                <dynamic-condition
                  :formItemData="filterList"
                  :formData="searchData"
                  @search="searchFn"
                  @reset="searchReset"
                >
                </dynamic-condition>
                <div class="data-list mb-sm">
                  <span class="mr-sm">设备标签</span>
                  <ui-select-tabs
                    class="ui-select-tabs"
                    :list="tagList"
                    @selectInfo="selectInfo"
                    ref="uiSelectTabs"
                  ></ui-select-tabs>
                </div>
              </div>
            </div>
            <div class="search-bottom mb-lg">
              <p class="devicenumtext base-text-color">
                已选择{{ modalTitle }}（<span class="color-failed">{{ getNodeSelectedData.length }}</span
                >条）
                <span v-if="getNodeSelectedData.length > 0"
                  >，
                  <span class="preview" @click="openSelectDeviceDetail"> 查看已选检测设备 </span>
                </span>
              </p>
            </div>
          </div>
          <ui-table
            reserveSelection
            class="ui-table auto-fill"
            :row-key="nodeKey"
            :table-columns="tableColumnsList"
            :table-data="tableData"
            :loading="tableLoading"
            :default-store-data="defaultStoreData"
            @oneSelected="oneSelected"
            @cancelSelectTable="cancelSelectTable"
            @onSelectAllTable="onSelectAllTable"
            @cacelAllSelectTable="cacelAllSelectTable($event, 'tableData')"
          >
            <template #isImportant="{ row }">
              <span>{{ row.isImportant === 0 ? '普通设备' : '重点设备' }}</span>
            </template>
            <template #phyStatus="{ row }">
              <span
                :style="{
                  color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
                }"
                >{{ row.phyStatus | filterType(phystatusList) }}</span
              >
            </template>
            <template #tagNames="{ row }">
              <tags-more :tag-list="row.tagList || []"></tags-more>
            </template>
          </ui-table>
          <ui-page
            class="page menu-content-background"
            transfer
            :page-data="pageData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          >
          </ui-page>
        </template>
        <!-- 导入文件 -->
        <div v-if="chooseMenu.chooseType === 'import'" class="import-tab-box">
          <div class="importFile" v-if="fileTableData.length === 0">
            <div class="imp-btn">
              <span>上传文件</span>
              <Upload
                :action="actionUrl"
                :data="uploadData"
                ref="upload"
                class="inline"
                :disabled="importLoading"
                :show-upload-list="false"
                :headers="headers"
                :before-upload="beforeUpload"
                :on-success="importSuccess"
                :on-error="importError"
              >
                <Button type="primary" class="daoru-btn ml-sm" :loading="importLoading">
                  <!-- <Icon type="md-cloud-upload" /> -->
                  <img src="~@/assets/img/device-map/shangchuang.png" />
                  <span class="vt-middle ml-sm btn-span">上传EXCEL表格模板</span>
                </Button>
              </Upload>
              <i class="el-icon-loading" v-if="exportLoading"></i>
              <span class="link-text-box pointer" v-else @click="exportModule">下载模板</span>
            </div>
            <p class="remark">备注：无法导入资产库不存在或不在权限范围内的视频监控设备！</p>
          </div>
          <div v-else class="file-table-box">
            <div class="file-status-text">
              <span class="mr-sm">
                成功导入
                <span class="font-green"> {{ fileTableData.length }} </span>
                条设备。
              </span>
              <span class="link-text-box pointer mr-sm btn-border-line" @click="deleteFileTableData">移除</span>
              <Upload
                :action="actionUrl"
                :data="uploadData"
                :disabled="importLoading"
                class="inline"
                :show-upload-list="false"
                :headers="headers"
                :before-upload="beforeUpload"
                :on-success="importSuccess"
                :on-error="importError"
              >
                <Button type="text" :loading="importLoading">
                  <span class="link-text-box pointer ml-sm mr-sm">继续导入</span>
                </Button>
              </Upload>
            </div>
            <ui-table
              reserveSelection
              class="ui-table auto-fill"
              :row-key="nodeKey"
              :table-columns="fileTableColumnsList"
              :table-data="fileTableData"
              :default-store-data="fileDefaultStoreData"
              @oneSelected="oneSelected"
              @cancelSelectTable="cancelSelectTable"
              @onSelectAllTable="onSelectAllTable"
              @cacelAllSelectTable="cacelAllSelectTable($event, 'fileTableData')"
              ref="fileTable"
            >
              <template #isImportant="{ row }">
                <span>{{ row.isImportant === 0 ? '普通设备' : '重点设备' }}</span>
              </template>
              <template #phyStatus="{ row }">
                <span
                  :style="{
                    color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
                  }"
                  >{{ row.phyStatus | filterType(phystatusList) }}</span
                >
              </template>
            </ui-table>
            <ui-page
              class="page menu-content-background"
              transfer
              :page-data="filePageData"
              @changePage="fileChangePage"
              @changePageSize="fileChangePageSize"
            >
            </ui-page>
          </div>
        </div>
      </section>
    </div>
    <template #footer>
      <Button @click="onCancel" class="plr-30"> 取 消 </Button>
      <Button type="primary" @click="querySubmit" class="plr-30"> 确 定 </Button>
    </template>

    <selected-preview-modal
      ref="SelectedPreviewModal"
      modalTitle="已选择设备"
      row-key="id"
      :selected-data="getNodeSelectedData"
      @getSelectedList="getSelectedList"
    >
    </selected-preview-modal>
  </ui-modal>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';

import equipmentassets from '@/config/api/equipmentassets';

import { filterList, tableColumns } from '../util/tableConfigindex';

export default {
  props: {
    value: {},
    //ui-modal title
    modalTitle: {
      default: '设备',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    treeNodeKey: {
      default: 'orgCode',
    },
    nodeKey: {
      type: String,
      default: 'id',
    },
    orgCodeList: {
      type: Array,
      default: () => [],
    },
    // 树
    treeData: {
      type: Array,
      default: () => [],
    },
    // 已选设备
    selectDeviceIds: {
      type: Array,
      default: () => [],
    },
    // 默认勾选树节点
    defaultCheckedNodeList: {
      type: Array,
      default: () => [],
    },
    // 默认勾选的设备信息对象
    defaultSelectDeviceData: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    UnderlineMenu: require('@/components/underline-menu').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    SelectedPreviewModal: require('@/components/choose-device/selected-preview-modal.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
  data() {
    return {
      visible: false,
      actionUrl: '/ivdg-evaluation-app/evaluation/app/taskScheme/importDeviceFile',
      uploadData: {},
      searchData: {
        deviceId: '', // 设备编码
        deviceName: '', // 设备名称
        sbdwlxList: [], // 监控点位类型
        sbgnlxList: ['1'], // 摄像机功能类型   m默认“视频监控”
        phyStatus: '', // 设备状态
        cascadeReportStatus: '', // 上报状态
        isImportant: '', // 重点类型
        sourceId: '1', // 数据来源
      },
      searchOtherData: {
        orgCodeList: [], // 左侧树
        tagIds: [], // 标签类型
      },
      // 左侧树
      treeLoading: false,
      selectTreeNode: '',
      selectTreeNodeItem: {},
      // 设备标签
      tagList: [],
      // 表格
      tableColumnsList: [],
      tableData: [],
      tableLoading: false,
      defaultStoreData: [], // 存储勾选的设备
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      chooseMenu: {
        chooseType: 'default',
        chooseTypeList: [
          {
            code: 'default',
            label: '资产库中选择',
          },
          {
            code: 'import',
            label: '导入文件',
          },
        ],
      },
      filterList: [],
      importLoading: false,
      exportLoading: false,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      fileTableColumnsList: [],
      allFileTableData: [], // 存储所有的 导入文件
      fileTableData: [], // 导入文件 当前页
      filePageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      allSelectedData: [], // 所有勾选项数据
      fileDefaultStoreData: [],
    };
  },
  computed: {
    ...mapGetters({
      cancelSource: 'common/getSource',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus', // 设备状态
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
    }),
    // 父节点 需包含 子节点
    getNodeSelectedData() {
      let arr = this.allSelectedData.filter(
        (item) => item.orgCode === this.selectTreeNode || this.selectTreeNodeItem.childrenOrgs?.includes(item.orgCode),
      );
      return arr || [];
    },
    showIcon() {
      return (data) => {
        let flag = this.allSelectedData.some((item) => {
          return data.childrenOrgs?.includes(item.orgCode) || item.orgCode === data.orgCode;
        });
        return flag;
      };
    },
  },
  created() {},
  mounted() {
    this.getTagList();
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      if (!val) return false;
      this.visible = val;
      this.tableColumnsList = tableColumns({ type: 'default' });
      this.fileTableColumnsList = tableColumns({ type: 'import' });
      this.uploadData = { orgCodes: this.orgCodeList.join(',') }; // 上传文件时的参数
      // 默认勾选 第一个可选择节点
      this.$nextTick(() => {
        this.$refs.uiSearchTree?.$refs.uiTree?.setCurrentKey(this.defaultCheckedNodeList[0].orgCode, true);
      });
      this.searchOtherData.orgCodeList = this.defaultCheckedNodeList.map((item) => item.orgCode);

      // 处理 已勾选设备
      this.getSelectDeviceList();

      // 筛查条件
      if (this.propertySearchLbdwlx.length === 0) {
        await this.getAlldicData();
      }
      let params = {
        moduleAction: this.moduleAction,
        propertySearchLbdwlx: this.turnOptions(this.propertySearchLbdwlx),
        propertySearchLbgnlx: this.turnOptions(this.propertySearchLbgnlx),
        phystatusList: this.turnOptions(this.phystatusList),
        sourceList: this.turnOptions(this.sourceList),
      };
      this.filterList = filterList(params);

      // 获取数据
      this.getTableList();
    },
  },
  methods: {
    ...mapActions({
      setSource: 'common/setSource',
      getAlldicData: 'algorithm/getAlldicData',
    }),
    turnOptions(arr) {
      let res = [];
      arr.map((item) => {
        res.push({ value: item.dataKey, label: item.dataValue });
      });
      return res;
    },
    onCancel() {
      this.visible = false;
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagList = res.data.data.map((row) => {
          return {
            name: row.tagName,
            id: row.tagId,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 重置
    searchReset() {
      this.getTableList();
    },
    searchFn(data) {
      this.resetPage();
      this.searchData = data;
      this.getTableList();
    },
    // 获取表格数据
    async getTableList() {
      try {
        this.tableLoading = true;
        let { pageNum, pageSize } = this.pageData;
        let data = {
          ...this.searchData,
          ...this.searchOtherData,
          pageNumber: pageNum,
          pageSize: pageSize,
        };
        let res = await this.$http.post(equipmentassets.queryDeviceInfoPageList, data);
        this.tableData = res.data.data?.entities || [];
        this.pageData.totalCount = res.data.data?.total || 0;
        // 根据 selectDeviceIds  对已勾选设备  重新勾选显示
        this.defaultStoreData = this.$util.common.deepCopy(this.getNodeSelectedData);
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    // 已选择的 设备信息
    async getSelectDeviceList() {
      try {
        this.selectTreeNode = this.defaultCheckedNodeList[0].orgCode;
        this.selectTreeNodeItem = this.defaultCheckedNodeList[0];
        // 传入的有，则不需要 调接口重新查
        if (this.defaultSelectDeviceData.length > 0) {
          this.allSelectedData = this.$util.common.deepCopy(this.defaultSelectDeviceData);
          this.defaultStoreData = this.$util.common.deepCopy(this.getNodeSelectedData);
          return;
        }

        if (this.selectDeviceIds.length === 0) return;
        let res = await this.$http.post(equipmentassets.queryDeviceInfoByIds, { ids: this.selectDeviceIds });
        this.allSelectedData = res.data.data || [];
        this.defaultStoreData = this.$util.common.deepCopy(this.getNodeSelectedData);
      } catch (err) {
        console.log(err);
      }
    },
    selectTree(data) {
      this.searchOtherData.orgCodeList = data[this.treeNodeKey] ? [data[this.treeNodeKey]] : [];
      this.selectTreeNode = data[this.treeNodeKey] || '';
      this.selectTreeNodeItem = data;
      this.resetPage();
      this.getTableList();
    },
    resetPage() {
      this.pageData.pageNum = 1;
      this.pageData.totalCount = 0;
    },
    selectInfo(infoList) {
      this.searchOtherData.tagIds = infoList.map((item) => item.id);
      this.getTableList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    /**
     * 切换搜索后，取消当前正在进行的请求，否则会接口过慢返回数据赋值
     */
    changeChooseType(val) {
      this.tableData = [];
      this.cancelSource.cancel && this.cancelSource.cancel('取消请求');
      this.setSource(this.$http.CancelToken.source());
      this.$nextTick(async () => {
        if (val === 'default') {
          this.getTableList();
        } else {
          this.fileDefaultStoreData = this.$util.common.deepCopy(this.allSelectedData);
        }
      });
    },
    openSelectDeviceDetail() {
      this.$refs.SelectedPreviewModal.init();
    },
    getSelectedList(data) {
      this.defaultStoreData = data;
      let deleteData = this.getNodeSelectedData.filter((item) => {
        return data.every((d) => d.deviceId != item.deviceId);
      });
      deleteData.map((item) => {
        this.cancelSelectTable(data, item);
      });
    },
    // 导入模板
    // 上传文件
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code == 200) {
        this.$Message.success(res.msg);

        // 注意去重
        let arr = [...res.data, ...this.allFileTableData];
        this.allFileTableData = this.doWeightArray(arr, 'deviceId');
        this.filePageHandle();
        this.fileDefaultStoreData = this.$util.common.deepCopy(this.allSelectedData);
      }
    },
    // 数组对象 根据某个字段 去重
    doWeightArray(arr, key) {
      let map = new Map();
      arr.map((item) => {
        map.set(item[key], item);
      });
      return [...map.values()];
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    // 下载模板
    async exportModule() {
      try {
        this.exportLoading = true;
        let res = await this.$http.get(governanceevaluation.downloadTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
    // 导入 分页
    filePageHandle() {
      this.fileTableData = this.$util.common.pagination(
        this.allFileTableData,
        this.filePageData.pageNum,
        this.filePageData.pageSize,
      );
      this.filePageData.totalCount = this.allFileTableData.length;
    },
    fileChangePage(val) {
      this.filePageData.pageNum = val;
      this.filePageHandle();
    },
    fileChangePageSize(val) {
      this.filePageData.pageNum = 1;
      this.filePageData.pageSize = val;
      this.filePageHandle();
    },
    deleteFileTableData() {
      let arr = this.allFileTableData.filter((item) => {
        return this.allSelectedData.some((s) => s.deviceId === item.deviceId);
      });
      if (arr.length === 0) {
        this.$Message.warning('请先选择需要移除的数据项！');
        return;
      }
      arr.map((item) => {
        let i = this.allFileTableData.findIndex((a) => a.deviceId === item.deviceId);
        this.allFileTableData.splice(i, 1);
        this.cancelSelectTable([], item);
      });
      this.filePageHandle();
    },
    // 确定
    querySubmit() {
      let arr = this.allSelectedData.map((item) => item.id);
      // 需判断 每个可选节点 是否都已有选择项，没有则需要提示
      let copyTreeData = this.$util.common.deepCopy(this.treeData);
      let nodeArr = this.$util.common.jsonToArray(copyTreeData);
      let isSelectNodeArr = nodeArr.filter((item) => !item.disabled);

      let flagArr = [];
      isSelectNodeArr.map((data) => {
        let flag = this.allSelectedData.some((item) => {
          return data.childrenOrgs?.includes(item.orgCode) || item.orgCode === data.orgCode;
        });
        flagArr.push(flag);
      });
      if (flagArr.some((item) => item === false)) {
        this.$Message.warning('存在平台没有选择设备，请检查！');
        return;
      }

      let obj = { ids: arr, allSelectedData: this.allSelectedData };
      this.$emit('selectedDevice', obj);
      this.visible = false;
    },

    // 处理表格勾选
    // 某项的选择
    oneSelected(selection, row) {
      // 已存在，则不重复添加
      let hasIndex = this.allSelectedData.findIndex((item) => item.deviceId === row.deviceId);
      if (hasIndex !== -1) return;
      this.allSelectedData.push(row);
    },
    // 取消某项的选择
    cancelSelectTable(selection, row) {
      let hasIndex = this.allSelectedData.findIndex((item) => item.deviceId === row.deviceId);
      if (hasIndex === -1) return;
      this.allSelectedData.splice(hasIndex, 1);
    },
    // 全选
    onSelectAllTable(selection) {
      let arr = this.$util.common.deepCopy(selection);
      // 接着 去重
      this.allSelectedData = this.doWeightArray([...arr, ...this.allSelectedData], 'deviceId');
    },
    // 取消全选
    cacelAllSelectTable(selection, arrName) {
      let tableRowKeys = [];
      this[arrName].forEach((item) => {
        tableRowKeys.push(item.deviceId);
      });
      this.allSelectedData = this.allSelectedData.filter((item) => {
        return !tableRowKeys.includes(item.deviceId);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.content-wrap {
  display: flex;
  height: 700px;
  background-color: var(--bg-content);
  .content-title {
    padding: 20px 20px 0;
    border-bottom: 1px solid var(--border-color);
  }
  .left-content {
    display: flex;
    flex-direction: column;
    width: 280px;
    height: 100%;
  }
  .right-content {
    width: 715px;
    height: 100%;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    .search-top {
      width: 100%;
      .align-flex;
    }
    .search-bottom {
      width: 100%;
      height: 15px;
      .align-flex;
      .devicenumtext {
        flex: 1;
        display: flex;
        justify-content: flex-end;
      }
    }
    .search-box {
      width: 100%;
      .search-header {
        flex: 1;
        .align-flex;
        flex-wrap: wrap;
      }
    }
    .search-input {
      position: relative;
      display: flex;
      .icon-sousuo {
        position: absolute;
        right: 10px;
      }
    }
    .ui-table {
      // width: 100%;
      position: relative;
      margin: 20px 20px 0;
    }
  }
  .padding20 {
    padding: 0 20px 20px;
  }
  .font-green-color {
    color: var(--color-bluish-green-text);
  }
  .arrow-box {
    height: 600px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
}
.preview {
  font-size: 16px;
  color: var(--color-primary);
  text-decoration: underline;
  cursor: pointer;
  &:hover {
    color: #4e9ef2;
  }
}
@{_deep} .choose-device-container > .ivu-modal > .ivu-modal-content {
  & > .ivu-modal-body {
    padding: 0 !important;
    margin: 0;
  }
  .ivu-modal-header {
    padding: 0 !important;
  }
}

@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
.data-list {
  display: flex;
  align-items: center;
  width: 100%;
  color: var(--color-content);
  font-size: 14px;
}
.select-tabs {
  flex: 1;
  display: flex;
  align-items: center;
}
.ui-select-tabs {
  flex: 1;
}
.pt-50 {
  padding-top: 50px !important;
}
.right-header-tab {
  background: inherit;
  border-bottom: 1px solid var(--devider-line);
}
.img-gouxuan {
  width: 14px;
}
.import-tab-box {
  width: 100%;
  height: 100%;
  .importFile {
    color: var(--color-content);
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: url('~@/assets/img/device-map/import-bg.png');
    background-size: 100% 100%;
    .imp-btn {
      display: flex;
      align-items: center;
      .daoru-btn {
        background: var(--bg-btn-default);
        border-radius: 4px;
        border: 1px dashed var(--border-btn-default);
        margin-right: 10px;
      }
      .btn-span {
        color: var(--color-primary);
      }
    }
    .remark {
      color: #c76d28;
      margin-left: 205px;
      margin-top: 10px;
    }
  }
  .file-status-text {
    color: var(--color-content);
    margin-left: 30px;
  }
  .file-table-box {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .btn-border-line {
    position: relative;
    &::before {
      content: '|';
      position: absolute;
      right: -10px;
      top: -3px;
      color: var(--devider-line);
    }
  }
}

@{_deep} .search-tree-box .el-tree .el-tree-node[aria-disabled='true'] > .el-tree-node__content {
  background: inherit !important;
  cursor: not-allowed;
}
.tree-node-box-item {
  display: flex;
  align-items: center;
  position: relative;
}
.nav-not-selected {
  color: rgba(69, 84, 107, 0.9) !important;
  cursor: not-allowed;
}
.nav-not-selected-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  cursor: not-allowed;
}
</style>
