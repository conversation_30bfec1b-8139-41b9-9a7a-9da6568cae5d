<template>
  <div class="messageconfiguration">
    <ul class="message-type-list">
      <li
        :class="['message-type', componentName === item.type ? 'active' : null]"
        v-for="(item, index) in messageTypeList"
        :key="index"
        @click="selectMessageType(item)"
      >
        <i :class="['icon-font', item.icon]"></i>
        <span class="inline vt-middle ml-sm f-14">{{ item.text }}</span>
      </li>
    </ul>
    <div class="config auto-fill">
      <div class="option">
        <div v-if="action === 'view'" class="fr">
          <Button type="primary" v-if="componentName == 'general-parameters'" @click="openAll(true)" class="mr-sm">
            <span class="inline vt-middle f-14 ml-xs">全部开启</span>
          </Button>
          <Button type="primary" v-if="componentName == 'general-parameters'" @click="openAll(false)" class="mr-sm">
            <span class="inline vt-middle f-14 ml-xs">全部关闭</span>
          </Button>
          <Button type="primary" @click="edit">
            <i class="icon-font icon-bianji3"></i>
            <span class="inline vt-middle f-14 ml-xs">编辑</span>
          </Button>
        </div>
        <div v-else class="fr">
          <Button type="primary" @click="save">
            <i class="icon-font icon-baocun"></i>
            <span class="inline vt-middle f-14 ml-xs">保存</span>
          </Button>
          <Button class="ml-sm" type="default" @click="cancel">
            <span class="inline vt-middle f-14 ml-xs">取消</span>
          </Button>
        </div>
      </div>
      <component ref="componentRef" :is="componentName" :action="action"></component>
    </div>
  </div>
</template>
<script>
import notification from '@/config/api/notification';
export default {
  name: 'messageconfiguration',
  props: {},
  data() {
    return {
      action: 'view',
      messageTypeList: [
        { icon: 'icon-tongyongcanshu1', type: 'general-parameters', text: '通用参数' },
        { icon: 'icon-waibuduijieyichang', type: 'external-docking', text: '外部对接异常' },
        { icon: 'icon-xitongyunweiyichang', type: 'system-operation', text: '系统运维异常' },
        { icon: 'icon-jiekouyichang', type: 'interface-exception', text: '接口异常' },
        { icon: 'icon-pingtailixian', type: 'platform-offline', text: '平台离线' },
        { icon: 'icon-shebeilixian', type: 'device-offline', text: '设备离线' },
        { icon: 'icon-zichanruku', type: 'assets-audit', text: '资产审核' },
        { icon: 'icon-gongdanchuli', type: 'order-dealing', text: '工单处理' },
        { icon: 'icon-zhibiaobudabiaosvg', type: 'index-unqualified', text: '指标不达标' },
      ],
      componentName: 'general-parameters',
    };
  },
  created() {},
  methods: {
    selectMessageType(item) {
      this.componentName = item.type;
      this.action = 'view';
    },
    edit() {
      this.action = 'edit';
    },
    cancel() {
      this.$refs.componentRef.reset();
      this.action = 'view';
    },
    async save() {
      try {
        // 必填校验
        if (this.$refs.componentRef.validateFn && this.$refs.componentRef.validateFn()) {
          return;
        }

        await this.$refs.componentRef.save();
        //联系人未输入手机号码，禁止提交
        if (!this.$refs.componentRef.checkSmsReceiveHasPhoneMx()) {
          return;
        }
        this.action = 'view';
      } catch (err) {
        console.log(err);
      }
    },
    //全部开启或关闭消息通知
    async openAll(bool) {
      this.$UiConfirm({
        content: `确定${bool ? '开启' : '关闭'}所有消息通知吗?`,
        title: '提示',
      })
        .then(async () => {
          try {
            await this.$http.get(notification.isOpenAll, {
              params: { isOpen: bool },
            });
            this.$Message.success(`${bool ? '开启' : '关闭'}成功`);
          } catch (error) {
            console.log(error);
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
  },
  watch: {},
  components: {
    GeneralParameters: require('./components/general-parameters/index.vue').default,
    ExternalDocking: require('./components/external-docking/index.vue').default,
    SystemOperation: require('./components/system-operation/index.vue').default,
    InterfaceException: require('./components/interface-exception/index.vue').default,
    PlatformOffline: require('./components/platform-offline/index.vue').default,
    DeviceOffline: require('./components/device-offline/index.vue').default,
    AssetsAudit: require('./components/assets-audit/index.vue').default,
    OrderDealing: require('./components/order-dealing/index.vue').default,
    IndexUnqualified: require('./components/index-unqualified/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.messageconfiguration {
  background-color: var(--bg-content);
  .message-type-list {
    width: 220px;
    height: 100%;
    padding-top: 8px;
    float: left;

    .message-type {
      height: 42px;
      padding: 0 14px;
      display: flex;
      align-items: center;
      cursor: pointer;
      color: var(--color-vertical-tab);
      .icon-font {
        color: var(--color-vertical-tab-icon);
      }
      &:hover {
        color: var(--color-vertical-tab-hover);
        background: var(--bg-vertical-tab-hover);
      }
      &.active {
        color: var(--color-vertical-tab-active);
        background-color: var(--bg-vertical-tab-active);
        .icon-font {
          color: var(--color-vertical-tab-active);
        }
      }
    }
  }
  .config {
    margin-left: 220px;
    height: 100%;
    // background-image: url('~@/assets/img/systemconfiguration/messageconfiguration-bg.png');
    background: var(--bg-content);
    background-size: 100% 100%;
    border-left: 1px solid var(--devider-line);
    .option {
      padding: 10px 20px;
      margin-bottom: 20px;
      overflow: hidden;
      border-bottom: 1px solid var(--devider-line);
    }
  }
}
</style>
