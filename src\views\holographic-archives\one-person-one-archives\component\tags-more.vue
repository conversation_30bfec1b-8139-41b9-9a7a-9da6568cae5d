<template>
  <div class="tags-more">
    <Tag type="border" v-for="(item, index) of defaultList" :color="item.color" :key="index">{{ item.label }}</Tag>
    <!-- <span v-if="!defaultList.length && tagData">无</span> -->
    <Poptip trigger="hover" :placement="placement" transfer popper-class="tags-transfer-poptip">
      <div slot="content" class="tag-wrapper">
        <Tag class="mr-5 mb-5" type="border" v-for="(item, index) of tagList" :key="index" :color="item.color">{{ item.label }}</Tag>
      </div>
      <Tag type="border" class="cursor-p" v-if="tagList.length > defaultTags && !expandAll">...</Tag>
    </Poptip>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    // 全部的标签
    tagList: {
      default: () => {
        return []
      }
    },
    tagData: {
      default: true,
      type: <PERSON>olean
    },
    // 默认显示两个
    defaultTags: {
      default: 2
    },
    // 不需要隐藏,展示所有标签
    expandAll: {
      default: false,
      type: Boolean
    },
    placement: {
      type: String,
      default: 'right-start'
    },
    bgColor: {
      type: String,
      default: '#2D435F'
    }
  },
  data() {
    return {
      transfer: true,
      originalLibData: '查询中..' // 来源
    }
  },
  computed: {
    defaultList() {
      if (this.expandAll) {
        return this.tagList
      } else {
        return this.tagList.slice(0, this.defaultTags)
      }
    }
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {},
  components: {}
}
</script>

<style scoped lang="less">
.tag-wrapper {
  display: flex;
  flex-wrap: wrap;
  max-width: 220px;
}
/deep/.ivu-poptip-body {
  padding: 0px;
}
/deep/.ivu {
  &-poptip {
    position: relative;
    &-arrow {
      border-top-color: transparent !important;
    }
    &-arrow:after {
      border-top-color: transparent !important;
    }
    &-inner {
      border: 1px solid #cfd6e6 !important;
    }
    &-body-content-inner {
      color: #ffffff !important;
      background-color: #cfd6e6 !important;
    }
    &-popper {
      &[x-placement^='right'] {
        .ivu-poptip-arrow {
          border-right-color: #cfd6e6 !important;
          &:after {
            border-right-color: #cfd6e6 !important;
            left: 1px !important;
          }
        }
      }
      &[x-placement^='left'] {
        top: -7px !important;
        left: -210px !important;
        .ivu-poptip-arrow {
          border-left-color: #cfd6e6 !important;
          &:after {
            border-left-color: #cfd6e6 !important;
          }
        }
      }
      &[x-placement^='top'] {
        .ivu-poptip-arrow {
          border-top-color: #cfd6e6 !important;
          &:after {
            border-top-color: #cfd6e6 !important;
            bottom: 1px;
          }
        }
      }
    }
  }
}
.pointTips:hover {
  background: #2b84e2 !important;
}
</style>
