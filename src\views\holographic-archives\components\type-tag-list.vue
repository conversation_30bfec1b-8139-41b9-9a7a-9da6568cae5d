<template>
  <div class="chose-type">
    <template v-for="(e, i) in typeTagList">
      <div :key="i" :class="{ checked: e.checked }" @click="choseTypeHandler(i)">
        <span> {{ e.name }} </span>
        <img v-show="e.checked" class="ui-tag-check-icon" src="@/assets/img/tag_check_icon.png" alt />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    typeTagList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    choseTypeHandler(i) {
      this.$emit('choseTypeHandler', i)
    }
  }
}
</script>

<style lang="less" scoped>
.chose-type {
  > div {
    display: inline-block;
    width: 48px;
    height: 24px;
    font-size: 14px;
    line-height: 22px;
    margin-right: 10px;
    text-align: center;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #e8eaec;
    cursor: pointer;
  }
  .checked {
    border: 1px solid #4e9ef2;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    .ui-tag-check-icon {
      width: 14px;
      height: 14px;
      position: absolute;
      right: -0px;
      bottom: -0px;
      z-index: 1;
    }
  }
}
</style>
