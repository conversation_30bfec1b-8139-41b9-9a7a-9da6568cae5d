<template>
  <div class="work-level-container f-14" :class="levelClass">
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: 'work-level-tag',
  props: {
    value: {
      required: true,
    }, //1,2,3,4
    classMap: {
      default: () => ({
        1: 'one-leve',
        2: 'two-leve',
        3: 'three-leve',
        4: 'four-leve',
      }),
    },
  },
  components: {},
  mixins: [],
  data() {
    return {};
  },
  computed: {
    levelClass() {
      if (!this.value) return;
      return this.classMap[this.value];
    },
  },
  created() {},
  mounted() {},
  watch: {},
  methods: {},
};
</script>

<style scoped lang="less">
@import './index.less';
.work-level-container {
  min-width: 40px;
  width: 40px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
}
</style>
