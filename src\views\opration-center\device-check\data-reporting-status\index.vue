<!--
    * @FileDescription: 24小时数据上报状态
    * @Author: H
    * @Date: 2023/12/04
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="container">
        <div class="search-bar">
			<searchDevice 
                ref="searchDevice"
                :formData.sync="formData"
                @searchInfo="searchInfo"
                @resetForm="resetForm"></searchDevice>
		</div>
        <div class="card-container">
            <div class="card-num-box">
                <div class="total-box">
                    <span>设备总数: <count-to :start-val="0" :end-val="headerNum.deviceTotal" :duration="1000"></count-to>/</span>
                    <span>在线数量: <count-to :start-val="0" :end-val="headerNum.liveCount" :duration="1000"></count-to>/</span>
                    <span>24小时在线率: {{ headerNum.liveRate }}%</span>
                </div>
                <div class="small-toatl-box">
                    <div class="small-item" :style="{'background': item.colour }" v-for="(item, index) in cardList" :key="index">
                        <div class="title">{{item.title}}</div>
                        <div class="num">
                            <count-to :start-val="0" :end-val="item.count" :duration="1000"></count-to>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="table-container">
            <Button type="primary" class="derive-btn" @click="handleImport($event)">导出</Button>
			<div class="table-content">
				<ui-table :columns="columns" :data="tableList" :loading="loading">
					<template #delayStatus="{ row }">
						<div>{{ row.delayStatus | commonFiltering(delayStatusList)}}</div>
					</template>
					<template #haveDataStatus="{ row }">
						<div>{{ row.haveDataStatus | commonFiltering(haveDataStatusList)}}</div>
					</template>
                    <template #dataType="{ row }">
						<div>{{ row.dataType | commonFiltering(dataTypeArrayList)}}</div>
					</template>
					<template #networkStatus="{ row }">
						<Button class="statusBtn" :type=" row.networkStatus=='1'?'success':row.networkStatus=='0'?'error':'warning'" size="small"> 
							{{ row.networkStatus | commonFiltering(deviceNetworkStatusList)}} 
						</Button>
					</template>
				</ui-table>
			</div>
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[10, 20, 40, 80]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
		</div>
        <hl-modal v-model="modalShow" title="提示" :r-width="500" @onCancel="loadCancel">
			<div class="content">
				<p class="tipLoad">数据打包中，请等候......</p>
				<!-- <p>大约尚需{{ maybeTime }}秒</p> -->
                <ui-loading v-if="modalLoading"></ui-loading>
			</div>
		</hl-modal>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import CountTo from 'vue-count-to';
import searchDevice from './components/search-device';
import hlModal from '@/components/modal/index.vue'
import { deviceCountGroupByHaveDataStatus, liveDeviceNum, pageList, timeOverDeviceCount,deviceDataLastTimeDown } from '@/api/data-warehouse';
export default {
    components: {
        searchDevice,
        CountTo,
        hlModal
    },
    props: {
        taskParams: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            cardList: [
                { title: '无数据设备总数', count: 0, dataStatus: 1, colour: 'rgb(44, 34, 168)' },
                { title: '1小时内', count: 0, dataStatus: 2, colour: 'rgb(55, 135, 45)' },
                { title: '1-2小时内', count: 0, dataStatus: 3, colour: 'rgb(168, 135, 3)' },
                { title: '2-4小时内', count: 0, dataStatus: 4, colour: 'rgb(255, 120, 10)' },
                { title: '4-24小时内', count: 0, dataStatus: 5, colour: 'rgb(212, 74, 58)' },
                { title: '24-48小时内', count: 0, dataStatus: 6, colour: 'rgb(196, 22, 42)' },
                { title: '大于48小时', count: 0, dataStatus: 7, colour: 'rgb(130, 16, 3)' },
                { title: '时间超前设备总数', count: 0, dataStatus: 0, colour: 'rgb(143, 59, 184)' },
            ],
            columns: [
                { title: '编号', align: 'center', width: 90, type: 'index', key: 'index' },
                { title: '分组路径', key: 'dirPath', },
                { title: '设备名称', key: 'deviceName', },
                { title: '最新数据接入时间', key: 'accessTime', },
                { title: '最新原始数据抓拍时间', key: 'dataTime', },
                { title: '数据有无状态', slot: 'haveDataStatus', },
                { title: '数据延迟状态', width: 120, slot: 'delayStatus', },
                { title: '数据类型', width: 120, slot: 'dataType', },
                { title: '更新时间', key: 'modifyTime', },
                { title: '端口连接状态', width: 120, slot: 'networkStatus' }
            ],
            tableList: [],
            loading: false,
            pageInfo: {
                pageNumber: 1,
                pageSize: 10,
            },
            total: 0,
            formData: {
                dirPath: '',
				deviceName: '',
                dataTypeList: [],
				haveDataStatusList: [],
                delayStatusList: [],
                selectDeviceList: []
            },
            headerNum: {
                deviceTotal: 0,
                liveCount: 0,
                liveRate: 0 
            },
            modalShow: false,
            modalLoading: false
        }
    },
    computed: {
        ...mapGetters({
            dataStatusArrayList: 'dictionary/getDataStatusList', // 数据状态
            dataTypeArrayList: 'dictionary/getDataTypeList', // 数据类型
            deviceNetworkStatusList: 'dictionary/getDeviceNetworkStatusList',
            haveDataStatusList: 'dictionary/getHaveDataStatusList', // 数据有无状态
            delayStatusList: 'dictionary/getDelayStatusList', //数据延迟状态
        })
    },
    async mounted() {
        await this.getDictData();
        // 推荐中心查看
        console.log('taskParams: ', this.taskParams)
        if (!Toolkits.isEmptyObject(this.taskParams)) {
            this.formData.selectDeviceList = this.taskParams.params.selectDeviceList || []
			this.formData.haveDataStatusList = this.taskParams.params.haveDataStatusList || []
            this.formData.delayStatusList = this.taskParams.params.delayStatusList || []
            this.init()
        }
    },  
    methods: {
        ...mapActions({
            getDictData: 'dictionary/getDictAllData'
        }),
        init() {
            this.queryList();
            deviceCountGroupByHaveDataStatus({})
            .then(res => {
                let list = res.data;
                let count = new Map(list.map(item => [item.haveDataStatus, item]))
                this.cardList.forEach(item => {
                    let numBox = count.get(Number(item.dataStatus));
                    if(numBox){
                        item.count = numBox.count
                    }
                })
            })
            liveDeviceNum({})
            .then(res => {
                let { deviceTotal, liveCount, liveRate } = res.data;
                this.headerNum = {
                    deviceTotal: deviceTotal,
                    liveCount: liveCount,
                    liveRate: liveRate.toFixed(4)*100
                };
            })
            timeOverDeviceCount({})
            .then(res => {
                this.cardList[7].count = res.data || 0;
            })
        },
        searchInfo() {
            this.pageInfo = {
                pageNumber: 1,
                pageSize: 10,
            };
            this.queryList()
        },
        resetForm() {
            this.pageInfo = {
                pageNumber: 1,
                pageSize: 10,
            };
            this.formData = {
                dirPath: '',
				deviceName: '',
                dataTypeList: [],
				haveDataStatusList: [],
                delayStatusList: [],
                selectDeviceList: []
            }
            this.queryList()
        },
        queryList() {
            this.loading = true;
            let params = {
                ...this.pageInfo,
                ...this.formData,
                deviceGbIdList: this.formData.selectDeviceList.map(v => v.deviceGbId)
            };
            pageList(params)
            .then(res => {
                this.tableList = res.data.entities;
                this.total = res.data.total;
            })
            .finally(() => {
                this.loading = false;
            })
        },
        // 导出
        handleImport($event){
            $event.stopPropagation()
            this.modalShow = true;
            this.modalLoading = true;
            let formData = this.$refs.searchDevice.formData;
            let params = { ...formData }
            params.deviceGbIdList = this.formData.selectDeviceList.map(v => v.deviceGbId)
            deviceDataLastTimeDown(params)
            .then(res =>{
                let filePath = res.data.path;
                let urllength = filePath.split('/')
                let filename = urllength[urllength.length -1];
                let flieType = filename.indexOf('zip') > 0 ? 'zip' : 'xlsx';
                let url = 'http://'+ document.location.host;
                Toolkits.ocxUpDownHttp(
                    "lis",
                    `${flieType}`,
                    `${url}${filePath}`,
                    `${filename}`
                );
                this.modalLoading = false;
            })
            .finally(() => {
                this.modalShow = false;
                this.modalLoading = false;
            })
        },
        loadCancel(){
            this.modalShow = false;
        },
        // 页数改变
        pageChange(size) {
            this.pageInfo.pageNumber = size
            this.queryList()
        },
        // 页数量改变
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1
            this.pageInfo.pageSize = size
            this.queryList()
        },
    },
}
</script>
<style lang="less" scoped>
.container{
    padding: 0;
	width: 100%;
    height: 100%;
    .card-container {
        height: 230px;
        padding: 0 20px 20px 20px;
        display: flex;
        .card-num-box{
            width: 100%;
            .total-box{
                height: 106px;
                width: 100%;
                background: #3274d9;
                color: #c7d0d9;
                font-size: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
            }
            .small-toatl-box{
                width: 100%;
                display: flex;
                justify-content: space-between;
                margin-top: 10px;
                .small-item{
                    width: 12%;
                    height: 106px;
                    color: #c7d0d9;
                    border-radius: 4px;
                    .title{
                        text-align: center; 
                    }
                    .num{
                        font-size: 38px;
                        text-align: center;
                        padding: 15px 0;
                    }
                }
            }
        }
    }
    .table-container{
        padding: 0 20px 0;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		position: relative;
		height: calc(~"100% - 320px");
        .derive-btn{
            width: 80px;
            margin-bottom: 10px;
        }
        .table-content {
			overflow: auto;
			flex: 1;
			display: flex;
			flex-wrap: wrap;
			justify-content: start;
			align-content: flex-start;
			.ui-table {
				height: 100%;
			}
			.statusBtn{
				cursor: default;
			}
		}
    }
}
</style>