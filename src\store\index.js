import Vue from "vue";
import Vuex from "vuex";
Vue.use(Vuex);
const modules = require.context("./modules", false, /([\w]+)\.js/);
const storeModules = {};
import admin from "./modules/admin";
modules.keys().forEach((key) => {
  const name = /([\w-]+)\.js$/i.exec(key);
  storeModules[name[1]] = modules(key).default;
});
const store = new Vuex.Store({
  modules: Object.assign(storeModules, { admin }),
  getters: {
    abc: (state) => state.token,
  },
  namespaced: true,
});

export default store;
