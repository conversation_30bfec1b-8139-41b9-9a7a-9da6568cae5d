<template>
  <ui-modal
    class="order-option"
    v-model="visible"
    :title="modalTitle"
    :styles="{ width: isBatch ? '7rem' : '3.5rem' }"
    @onCancel="$emit('cancel')"
  >
    <div class="order-option-content auto-fill" :style="{ height: isBatch ? '3.2rem' : '' }">
      <component
        :is="componentName"
        :ref="componentName"
        :isBatch="isBatch"
        @updateDisabled="$emit('updateDisabled')"
      ></component>
      <div v-if="componentName === 'Assign'">
        <div class="mb-md mt-md">
          <span class="base-text-color">短信通知：</span>
          <RadioGroup v-model="newMessageNotice" @on-change="$emit('changeNoticeType')">
            <Radio label="1" class="mr-lg">是</Radio>
            <Radio label="0">否</Radio>
          </RadioGroup>
        </div>
        <div class="mb-md" v-if="messageNotice === '1'">
          <span class="base-text-color">联系电话：</span>
          <Input placeholder="请输入联系电话" class="width-md" v-model="newTellphoneNumber" />
        </div>
      </div>
      <div class="select-table auto-fill" v-if="isBatch">
        <div class="select-title">待{{ titles[componentName] }}工单：</div>
        <p v-if="componentName === 'Assign'" class="tips">
          有{{ failData.length ? failData.length : 0 }}条已完成/已关闭工单，无需指派
        </p>
        <p v-if="componentName === 'Deal'" class="tips">
          有{{ failData.length ? failData.length : 0 }}条已关闭工单，无法处理
        </p>
        <p v-if="componentName === 'Close'" class="tips">
          有{{ notOwnersData.length ? notOwnersData.length : 0 }}条工单您无权关闭； 有{{
            notHandleData.length ? notHandleData.length : 0
          }}条工单未处理，无法关闭；有{{ closeData.length ? closeData.length : 0 }}条已关闭工单，无需关闭
        </p>
        <ui-table class="auto-fill" :tableColumns="modelColumns" :tableData="sucessData">
          <!-- 经纬度保留8位小数 -->
          <template #longitude="{ row }">
            <span>{{ row.longitude | filterLngLat }}</span>
          </template>
          <template #latitude="{ row }">
            <span>{{ row.latitude | filterLngLat }}</span>
          </template>

          <template slot="action" slot-scope="{ row }">
            <ui-btn-tip class="operatbtn" icon="icon-yichu1" content="移除" @click.native="remove(row)"></ui-btn-tip>
            <!-- <Button type="text" class="operatbtn" @click.stop="remove(row)">移除</Button> -->
          </template>
        </ui-table>
      </div>
    </div>
    <template #footer>
      <div v-if="!isBatch">
        <Button type="primary" :disabled="isDisabled" @click="$emit('submit')" class="plr-30">确 认</Button>
        <Button class="ml-sm plr-30" @click="$emit('cancle')">取 消</Button>
      </div>
      <Button v-else type="primary" :disabled="!sucessData.length || isDisabled" @click="$emit('submit')" class="plr-30"
        >确 认</Button
      >
    </template>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    modalTitle: {},
    isBatch: {},
    componentName: {},
    messageNotice: {},
    tellphoneNumber: {},
    failData: {},
    notOwnersData: {},
    closeData: {},
    sucessData: {},
    isDisabled: {},
  },
  data() {
    return {
      modelColumns: [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 180,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
          minWidth: 200,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          width: 110,
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          width: 110,
          align: 'left',
        },
        {
          title: '异常原因',
          key: 'errorReason',
          align: 'left',
          tooltip: true,
          minWidth: 200,
        },
        {
          title: '操作',
          slot: 'action',
          width: 90,
          align: 'center',
          className: 'table-action-padding',
        }, // 操作栏列-单元格padding设置
      ],
      visible: false,
      newMessageNotice: '0',
      newTellphoneNumber: '',
      titles: {
        Assign: '指派',
        Deal: '处理',
        Close: '关闭',
      },
    };
  },
  created() {},
  methods: {},
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    messageNotice(val) {
      this.newMessageNotice = val;
    },
    tellphoneNumber(val) {
      this.newTellphoneNumber = val;
    },
  },
  components: {
    Close: require('./components/close.vue').default,
    Deal: require('./components/deal.vue').default,
    Assign: require('./components/assign.vue').default,
    AddOrder: require('./components/add-order/index.vue').default,
    EditViewOrder: require('./components/edit-view-order.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.order-option {
  @{_deep} > .modal {
    > .ivu-modal-wrap {
      > .ivu-modal {
        > .ivu-modal-content {
          > .ivu-modal-body {
            display: flex;
            justify-content: center;
          }
        }
      }
    }
  }
  &-content {
    width: 100%;
    padding: 0 20px;
    .select-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      .tips {
        color: var(--color-failed);
        text-align: right;
      }
      .select-title {
        width: 110px;
        color: var(--color-content);
        text-align: left;
      }
    }
  }
}
</style>
