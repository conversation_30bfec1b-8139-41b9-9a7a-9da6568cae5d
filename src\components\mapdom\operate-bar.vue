<template>
    <div class="operate-bar">
        <div class="operate-item">
            <ui-btn-tip class="collection-icon" v-if="list.myFavorite == 1" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection($event, list, 2)" />
            <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection($event, list, 1)" />
        </div>
        <!-- <div class="operate-item">
            <i class="iconfont icon-fenxi color-primary"></i>
        </div>
        <div class="operate-item">
            <i class="iconfont icon-dunpai color-error"></i>
        </div> -->
    </div>
</template>

<script>
import { addCollection, deleteMyFavorite } from '@/api/user';
export default {
    props:{
        list: {
            type: Object,
            default: () => {
                return {}
            }
        },
        tabType: { //收藏的类型
            type: Object,
            default: () =>{
                return {}
            }
        }
    },
    methods:{
        collection($event, data, flag) {
            $event.stopPropagation();
            let type;
            if([16, 17].includes(this.tabType.type)){
                type = 'recordId'
            }else{
               type = this.tabType.deviceId ? 'deviceId' : 'id';
            }
            let params = {
                // favoriteObjectId: this.tabType.deviceId ? data.deviceId : data.id,
                favoriteObjectId: data[type],
                favoriteObjectType: this.tabType.type,
            }
            if(flag == 1){ //进行收藏
                addCollection(params).then(res => {
                    this.$Message.success("收藏成功");
                    this.$emit('collection', flag)
                })
            } else { //取消收藏
                deleteMyFavorite(params).then(res => {
                    this.$Message.success("取消收藏成功");
                    this.$emit('collection', flag)
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
.operate-bar {
    display: flex;
    align-items: center;
    .operate-item {
        margin-right: 10px;
    }
}
.collection-icon {
    /deep/ .iconfont {
        font-size: 14px;
        color: #fff;
    }
    .ivu-tooltip-rel {
        // margin-top: 3px;
    }
    /deep/ .icon-shoucang {
        color: #f29f4c !important;
        text-shadow: 0px 1px 0px #e1e1e1;
    }
    /deep/ .icon-yishoucang {
        color: #f29f4c !important;
    }
}

</style>
