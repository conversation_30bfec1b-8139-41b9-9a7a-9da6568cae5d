<template>
  <div class="ui-radio-group">
    <span v-if="!!label" class="f-12 label mr-xs">{{ label }}</span>
    <RadioGroup v-model="radioLabel" type="button" @on-change="radioChange">
      <Radio v-for="(item, index) in radioList" class="radio-item" :key="index" :label="item.label"></Radio>
    </RadioGroup>
  </div>
</template>
<style lang="less" scoped>
.ui-radio-group {
  .label {
    color: var(--color-label);
  }
  @{_deep} .radio-item {
    padding: 0 4px;
    color: var(--color-content);
    border-radius: 2px;
  }
}
@{_deep} .ivu-radio-wrapper {
  background: transparent;
  border: none;
  line-height: 30px;
  height: 30px;
  margin-right: 8px;
  &.ivu-radio-wrapper-checked {
    color: var(--color-btn-default);
    border: none;
  }
  &:first-child,
  &:last-child {
    border-left: none;
  }
  &:last-child {
    margin-right: 0;
  }
  &::before {
    background: transparent;
  }
  //   .ivu-radio-group-button .ivu-radio-wrapper:first-child
}
</style>
<script>
export default {
  data() {
    return {
      radioLabel: '',
    };
  },
  created() {},
  mounted() {
    let obj = this.radioList.find((row) => {
      return row.value === this.value;
    });
    obj ? (this.radioLabel = obj.label) : '';
  },
  methods: {
    radioChange(selected) {
      let obj = this.radioList.find((row) => {
        return row.label === selected;
      });
      this.$emit('input', obj.value);
      this.$emit('change', obj.value);
    },
  },
  watch: {
    value() {
      let obj = this.radioList.find((row) => {
        return row.value === this.value;
      });
      this.radioLabel = obj ? obj.label : '';
    },
  },
  computed: {},
  props: {
    // 绑定的值
    value: {},
    // 标题
    label: {},
    /**
     * 列表
     * label:名字
     * value:内容
     */
    radioList: {},
  },
  components: {},
};
</script>
