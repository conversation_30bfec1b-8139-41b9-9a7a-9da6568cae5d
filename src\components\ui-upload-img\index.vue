<template>
  <div class="upload-list" :class="size ? 'upload-' + size : ''">
    <template v-for="(url, index) in urlList">
      <!-- 已上传图片展示 -->
      <div class="img-content" v-if="url" :key="index">
        <img :src="url.imageUrl || url.fileUrl" alt />
        <p class="delete-mask">
          <i
            class="iconfont icon-shanchu"
            @click.stop="minImgDeleteHandle(index)"
          ></i>
        </p>
      </div>

      <!-- 上传图片按钮 -->
      <Upload
        v-else
        :key="index + 'a'"
        type="drag"
        :beforeUpload="(file) => beforeUpload(file, index)"
        action="*"
        :show-upload-list="false"
      >
        <template>
          <Icon type="ios-add" />
          <p v-if="size != 'mini2'">上传图片</p>
        </template>
      </Upload>
    </template>

    <!-- 图片上传成功后，选择图片 -->
    <image-recognition
      ref="imageRecognition"
      :dataCopper="dataCopper"
      :tempUrl="tempUrl"
      @getMinImgUrl="getMinImgUrl"
      v-if="imageRecognitionVisible"
    />
    <canvas v-show="false" id="mycanvas"></canvas>
    <img v-show="false" :src="imageSrc" alt />
  </div>
</template>
<script>
import dataCopper from "./test";
import imageRecognition from "./image-recognition";
import { picturePick } from "@/api/wisdom-cloud-search";
import { imgUpload } from "@/api/number-cube";
export default {
  name: "SearchPictures",
  components: { imageRecognition },
  props: {
    size: {
      type: String,
      default: "", // large、 default、 small
    },
    value: {
      type: Array,
      default: () => [],
    },
    // 1人脸2车辆
    algorithmType: {
      type: [String, Number],
      default: null,
    },
    uploadUlr: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    urlList(val) {
      if (val.length > 0) {
        this.$emit("input", val);
      }
    },
    value: {
      handler(val) {
        if (val.length > 0) {
          this.urlList = val;
        } else {
          this.urlList = ["", "", "", "", ""];
        }
        // this.urlList = val||['','','','','']
      },
      immediate: true,
    },
  },
  data() {
    return {
      urlList: ["", "", "", "", ""],
      dataCopper: [],
      tempUrl: "",
      imageRecognitionVisible: false,
      selectedUploadIndex: null, //选中的uploadIndex
      baseImg: null,
      bigImageBase64: null,
      imageSrc: "",
    };
  },
  methods: {
    //删除单个图片
    minImgDeleteHandle(index) {
      this.$set(this.urlList, index, "");
      this.$forceUpdate();
      this.imgUrlChange();
      this.$emit("deleteImgUrl", this.urlList);
    },
    // 获取
    getMinImgUrl(urlObj) {
      this.$set(this.urlList, this.selectedUploadIndex, {
        ...urlObj,
        imageUrl: urlObj.fileUrl,
      });
      this.$forceUpdate();
      this.imgUrlChange();
    },
    imgUrlChange() {
      this.$emit("imgUrlChange", this.urlList);
    },
    //上传图片
    beforeUpload(file, index) {
      this.getBase64(file).then((res) => {
        // console.log(res)
        this.bigImageBase64 = res;
      });
      this.selectedUploadIndex = index;
      let isAskFile = false;
      isAskFile = /\.(PNG|JPG|JPEG|BMP|png|jpg|jpeg|bmp)$/.test(file.name);
      if (!isAskFile) {
        this.$Message.error("请上传png、jpg、jpeg或bmp格式图片！");
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 30MB!");
      }
      if (!isAskFile || !isLt30M) return false;
      this.fileUploading = this.$Message.loading({
        content: "文件上传中...",
        duration: 0,
      });
      let fileData = new FormData();
      fileData.append("file", file);
      fileData.append("algorithmType", this.algorithmType);
      picturePick(fileData)
        .then((res) => {
          // console.log('文件上传返回值', res)
          if (res.data.length == 0) {
            this.$Message.error("没有识别出目标，请选择其它图片");
            return;
          }
          if (this.uploadUlr) {
            this.controlImageInfo(file);
          } else {
            if (res.data.length == 1) {
              this.tempUrl = window.URL.createObjectURL(file);
              this.oneImageInfo(res.data, index);
              return;
            }
            this.imageRecognition();
            this.imageDataHandle(res.data);
            // this.dataCopper = dataCopper
            this.tempUrl = window.URL.createObjectURL(file);
          }
        })
        .finally(() => {
          this.fileUploading();
          this.fileUploading = null;
        });
      return false;
    },
    // 打开图片识别器
    imageRecognition(data) {
      this.imageRecognitionVisible = true;
      this.$nextTick(() => {
        this.$refs.imageRecognition.init(data);
      });
    },
    // 布控中识别有头像 图片全传
    controlImageInfo(file) {
      const fd = new FormData();
      fd.append("file", file);
      imgUpload(fd).then((res) => {
        let obj = {
          fileUrl: res.data.fileUrl,
        };
        this.$set(this.urlList, this.selectedUploadIndex, obj);
        this.imgUrlChange();
      });
    },
    /**
     * 一张图片操作
     */
    async oneImageInfo(list) {
      let _that = this;
      await this.imageDataHandle(list);
      this.$nextTick(() => {
        var canvas = document.getElementById("mycanvas");
        const { left, top, right, bottom } = list[0];
        canvas.width = right - left;
        canvas.height = bottom - top;
        let ctx = canvas.getContext("2d");
        let image = new Image();
        image.crossOrigin = "anonymous"; // 网络图片跨域
        // image.src = 'https://img0.baidu.com/it/u=2484538263,336635826&fm=26&fmt=auto&gp=0.jpg'
        image.src = this.tempUrl;
        image.onload = function () {
          ctx.drawImage(image, left, top, right, bottom, 0, 0, right, bottom);
          // ctx.drawImage(image, 295, 40, 100, 100, 0 ,0, 100, 100)
          // var img = canvas.toDataURL("image/jpeg", 1); // toDataUrl可以接收2个参数，参数一：图片类型，参数二： 图片质量0-1（不传默认为0.92）
          // 将图片转成base64格式
          _that.imageSrc = canvas.toDataURL("image/png");
          var obj = {
            feature: list[0].feature,
            fileUrl: _that.imageSrc,
            imageBase: list[0].imageBase,
            imageUrl: list[0].imageUrl,
          };
          _that.$set(_that.urlList, _that.selectedUploadIndex, obj);
          _that.imgUrlChange();
        };
      });
    },

    /**
     * 图片转base64
     */
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader();
        let imgResult = "";
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },

    /**
     * 图片上传返回数据处理
     * feature: 返回后台用
     *
     * 后台返回的数据结构：bottom, left, right, top
     * 返回值为图片的2个坐标点，分别为左上和右下
     * 左上：left top
     * 右下：right bottom
     */
    imageDataHandle(list) {
      var arr = [];
      list.forEach((item) => {
        var obj = {
          x: item.left,
          y: item.top,
          width: item.right - item.left,
          height: item.bottom - item.top,
          feature: item.feature,
          imageBase: item.imageBase,
        };

        arr.push(obj);
      });
      this.dataCopper = arr;
    },
    //base64转Blob
    convertBase64UrlToBlob(urlData) {
      //去掉url的头，并转换为byte
      var split = urlData.split(",");
      var bytes = window.atob(split[1]);
      //处理异常,将ascii码小于0的转换为大于0
      var ab = new ArrayBuffer(bytes.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ab], { type: split[0] });
    },
  },
};
</script>
<style lang="less" scoped>
.upload-list {
  &.upload-large {
    /deep/ .ivu-upload,
    .img-content {
      width: 140px;
      height: 140px;
    }
  }

  &.upload-small {
    /deep/ .ivu-upload,
    .img-content {
      width: 80px;
      height: 80px;
      margin-right: 10px;
    }

    /deep/ .ivu-upload {
      .ivu-upload-drag {
        font-size: 12px;
        border-radius: 0;
      }

      .ivu-icon {
        font-size: 38px;
      }
    }
  }

  &.upload-mini {
    /deep/ .ivu-upload,
    .img-content {
      width: 60px;
      height: 60px;
    }

    /deep/ .ivu-upload {
      .ivu-upload-drag {
        font-size: 10px;
        border-radius: 0;
      }

      .ivu-icon {
        font-size: 30px;
      }
    }
  }

  &.upload-mini2 {
    /deep/ .ivu-upload,
    .img-content {
      width: 34px;
      height: 34px;
    }

    /deep/ .ivu-upload {
      .ivu-upload-drag {
        font-size: 10px;
        border-radius: 0;
      }

      .ivu-icon {
        font-size: 30px;
      }
    }
  }

  width: 100%;
  display: flex;
  justify-content: space-between;

  /deep/ .ivu-upload {
    width: 116px;
    height: 116px;

    .ivu-upload-drag {
      height: 100%;
      width: 100%;
      background: #f9f9f9;
      border: 1px dashed #d3d7de;
      color: rgba(0, 0, 0, 0.45);
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
      font-size: 14px;

      &:hover {
        border-color: #2c86f8;
      }
    }

    .ivu-icon {
      font-weight: bold;
      font-size: 46px;
      color: #888;
    }
  }

  .img-content {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    width: 116px;
    height: 116px;

    &:hover {
      .delete-mask {
        display: flex;
      }
    }

    img {
      display: block;
      height: auto;
      max-width: 100%;
      max-height: 100%;
    }

    .delete-mask {
      position: absolute;
      z-index: 100;
      background: rgba(0, 0, 0, 0.5);
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      display: none;
      align-items: center;
      justify-content: center;

      .iconfont {
        color: #fff;
        font-size: 28px;
      }
    }
  }
}
</style>