<!--
    * @FileDescription: 由人到案
    * @Author: zmm
    * @Date: 2024/04/15
 -->
<template>
  <div class="case-to-person">
    <mapCustom ref="mapBase" mapType="personToCase"></mapCustom>
    <leftBox
      ref="leftBox"
      @analysis="handleAnalysis"
      @openRightBox="showRightBox = true"
      @onResultData="onResultData"
      @dealPoliceAndCaseTrackData="dealPoliceAndCaseTrackData"
    ></leftBox>
    <template v-if="showRightBox">
      <rightBox
        v-if="rightShowList"
        @cancel="handleCancel"
        @openTrackLine="openTrackLine"
        @openPositionTheWindow="openPositionTheWindow"
        @openPositionAreaTheWindow="openPositionAreaTheWindow"
      >
      </rightBox>
      <track-details
        v-else
        @goback="rightShowList = true"
        :dataId="dataId"
        @openPositionTheWindow="openPositionTheWindow"
      ></track-details>
    </template>
  </div>
</template>

<script>
import { mapMutations, createNamespacedHelpers } from "vuex";
import * as turf from "@turf/turf";
import mapCustom from "../../components/map/index.vue";
import leftBox from "./components/leftBox.vue";
import rightBox from "./components/rightBox.vue";
import trackDetails from "./components/track-details.vue";
import CreatGeometryLayer from "../../components/map/CreatGeometryLayer.js";
import mapDetailWindow from "@/views/model-market/components/map/map-windows/map-detail-window.vue";
import mapAlarmWindow from "@/views/model-market/components/map/map-windows/map-alarm-window.vue";
import faceOrVehicle from "../../components/map/map-windows/faceOrVehicle.vue";

const {
  mapState: mapPersonToCaseState,
  mapMutations: mapPersonToCaseMutations,
} = createNamespacedHelpers("personToCase");
const peopleTrackLayerName = "peopleTrackPoint";
const policeAndCaseTrackLayerName = "policeAndCaseTrack";

export default {
  name: "person-to-case",
  components: {
    mapCustom,
    leftBox,
    rightBox,
    trackDetails,
  },
  data() {
    return {
      dataId: {},
      showRightBox: false,
      rightShowList: true,
      trackDetail: false,
      geometryLayer: null,
      collisionAreaMap: {},
    };
  },
  watch: {
    peopleTrackList(val) {
      this.closeInfoWindow();
      this.dealPeopleTrackData(val);
    },
    showArea() {
      this.showTrackPoint();
      this.showGeometryLayer();
    },
    analysisAreaList() {
      this.closeInfoWindow();
      this.getaAnalysisAreaMapStyle();
    },
    // 案件和警情
    showPolice() {
      this.showPolicePoint();
    },
    // policeAndCaseTrackList(val) {
    //   this.dealPoliceAndCaseTrackData(val);
    // },
  },
  computed: {
    ...mapPersonToCaseState({
      peopleTrackList: (state) => state.peopleTrackList,
      collisionAreas: (state) => state.collisionAreas,
      showArea: (state) => state.showArea,
      analysisAreaList: (state) => state.analysisAreaList,
      showPolice: (state) => state.showPolice,
      policeAndCaseTrackList: (state) => state.policeAndCaseTrackList,
    }),
  },
  async created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    // this.destroyData();
    this.setLayoutNoPadding(false);
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    ...mapPersonToCaseMutations(["setState"]),
    destroyData(
      stateNames = [
        "peopleTrackList",
        "collisionAreas",
        "analysisAreaList",
        "policeAndCaseTrackList",
        "analysisResultList",
      ]
    ) {
      const obj = {};
      stateNames.forEach((item) => {
        obj[item] = [];
      });
      this.setState(obj);
    },
    getMarketIcon(type) {
      return require(`@/assets/img/map/map-${type}.png`);
    },
    // 人员轨迹和区域数据处理
    dealPeopleTrackData(list) {
      const traceSprinkles = [];
      const collisionAreas = [];
      for (let i = 0; i < list.length; i++) {
        const { geoPoint, personBaseTrajectories = [] } = list[i];
        const geoPoints = [];
        const newPersonBaseTrajectories = personBaseTrajectories.map((item) => {
          geoPoints.push([item.geoPoint.lon, item.geoPoint.lat]);
          return {
            ...item,
            traitImg: item.traitImg || this.getMarketIcon(item.dataType),
            showIconBorder: !!item.traitImg,
          };
        });
        traceSprinkles.push(...newPersonBaseTrajectories);
        const polygon = this.getPolygon(geoPoints);
        collisionAreas.push({
          geoPoint,
          polygon,
          ellipse: this.transformtPolygonTotEllipse(polygon),
        });
      }
      // 定位中心点
      if (list && list.length > 0) {
        // 定位中心点
        this.$refs.mapBase.setCenter(list[0].geoPoint);
      }
      // 撒点
      this.creatTraceSprinkles(traceSprinkles, peopleTrackLayerName);
      this.showTrackPoint();
      // 区域
      this.creatCollisionArea(collisionAreas);
    },
    // 警情案件撒点
    dealPoliceAndCaseTrackData(list) {
      const traceSprinkles = list.map((item) => {
        return {
          ...item,
          traitImg: this.getMarketIcon(
            item.policeDataType == 1 ? "alarm" : "case"
          ),
          showIconBorder: false,
        };
      });
      this.creatTraceSprinkles(traceSprinkles, policeAndCaseTrackLayerName);
      this.showPolicePoint();
    },
    getPolygon(list) {
      if (list.length < 2) return [];
      try {
        const line = turf.lineString(list);
        const bbox = turf.bbox(line);
        return turf.bboxPolygon(bbox).geometry.coordinates[0];
      } catch (e) {
        return [];
      }
    },
    transformtPolygonTotEllipse(points) {
      if (points.length < 5) {
        return null;
      }
      const centerPoint = [];
      for (let i = 1; i < points.length; i++) {
        const point1 = turf.point(points[i]);
        const point2 = turf.point(points[i - 1]);
        const midpoint = turf.midpoint(point1, point2);
        centerPoint.push(midpoint);
      }
      const distancePoin1 = [centerPoint[0], centerPoint[2]];
      const distance1 = turf.distance(...distancePoin1);
      const distancePoin2 = [centerPoint[1], centerPoint[3]];
      const distance2 = turf.distance(...distancePoin2);
      // let distance = 0;
      let distancePoint = [];
      if (distance1 - distance2 > 0) {
        // distance = distance1;
        distancePoint = distancePoin1;
      } else {
        // distance = distance2;
        distancePoint = distancePoin2;
      }
      const [firstPoint, secondPoint] = distancePoint;
      return distance1 > 0
        ? {
            firstPoint: firstPoint.geometry.coordinates,
            secondPoint: secondPoint.geometry.coordinates,
            distance: (distance1 + distance2) * 1000,
          }
        : null;
    },
    getWindowComponent(info) {
      const { dataType, policeDataType } = info;
      if (["face", "vehicle"].includes(dataType)) {
        return faceOrVehicle;
      } else if (policeDataType) {
        return mapAlarmWindow;
      } else {
        return mapDetailWindow;
      }
    },
    getWindowOpts(opts = {}) {
      const that = this;
      return {
        setContentDom(info) {
          return that.getWindowComponent(info);
        },
        ...opts,
      };
    },
    // 显示选中案件区域高亮
    openPositionAreaTheWindow(item) {
      // this.$refs.mapBase.setCenter(item);
      this.openPositionTheWindow(item, {
        offsetSize: [0, -40],
      });
      this.changeCollisionAreaMapStyle(
        item.centers && item.centers.map((el) => this.getAreaMapKey(el))
      );
    },
    // 打开弹窗
    openPositionTheWindow(item, opts) {
      this.$refs.mapBase.openPositionTheWindow(
        item,
        this.getWindowOpts({
          offsetSize: [0, -40],
          ...opts,
        })
      );
    },
    resetMarkerPoint(layerNames) {
      this.$refs.mapBase.resetMarkerPoint(layerNames);
    },
    // 创建撒点
    creatTraceSprinkles(list, layerName) {
      this.resetMarkerPoint([layerName]);
      list.length > 0 &&
        this.$refs.mapBase.sprinklePoint(list, layerName, {
          windowOpts: this.getWindowOpts(),
        });
    },
    // 创建碰撞区域
    creatCollisionArea(collisionAreas) {
      this.collisionAreaMap = {};
      const map = this.$refs.mapBase.getMap();
      this.geometryLayer = new CreatGeometryLayer(
        map,
        "creatCollisionArea",
        "Sing"
      );
      this.geometryLayer.removeAllOverlays();
      for (const item of collisionAreas) {
        const { geoPoint, polygon, ellipse } = item;
        // this.collisionAreaMap[this.getAreaMapKey(geoPoint)] =
        //   this.geometryLayer.CreatGeometry(polygon, "Polygon", {});
        this.collisionAreaMap[this.getAreaMapKey(geoPoint)] = ellipse
          ? this.geometryLayer.CreatGeometry(ellipse, "Ellipse", {})
          : this.geometryLayer.CreatGeometry(
              { center: [geoPoint.lon, geoPoint.lat], radius: 1000 },
              "Circle",
              {}
            );
      }
      this.showGeometryLayer();
    },
    getAreaMapKey({ lon, lat }) {
      return `${lon}-${lat}`;
    },
    // 获取膨胀分析后的区域样式
    getaAnalysisAreaMapStyle() {
      let areaMapKey = [];
      if (this.analysisAreaList && this.analysisAreaList.length > 0) {
        // 定位中心点
        this.$refs.mapBase.setCenter(this.analysisAreaList[0]);
        areaMapKey = this.analysisAreaList.map((item) =>
          this.getAreaMapKey(item)
        );
      }
      this.changeCollisionAreaMapStyle(areaMapKey);
    },
    changeCollisionAreaMapStyle(areaMapKey = []) {
      for (const key in this.collisionAreaMap) {
        let color = "#2C86F8";
        if (areaMapKey.includes(key)) color = "#F29F4C";
        this.collisionAreaMap[key].setStyle({
          color,
          fillColor: color,
        });
      }
    },
    // 案件撒点显示控制
    showPolicePoint() {
      this.$refs.mapBase.coverageShowOrHide(
        policeAndCaseTrackLayerName,
        this.showPolice
      );
    },
    // 人员撒点显示控制
    showTrackPoint() {
      this.$refs.mapBase.coverageShowOrHide(
        peopleTrackLayerName,
        this.showArea
      );
    },
    showGeometryLayer() {
      if (this.showArea) this.geometryLayer.show();
      else this.geometryLayer.hide();
    },
    // 碰撞分析
    handleAnalysis() {
      this.rightShowList = true;
    },
    //  打开轨迹详情
    openTrackLine(params) {
      this.dataId = {
        reqSeq: this.$refs.leftBox.getReqSeq(),
        ...params,
      };
      this.rightShowList = false;
    },
    initRightBoxStatus() {
      this.showRightBox = false;
      this.rightShowList = true;
    },
    onResultData() {
      this.initRightBoxStatus();
      this.destroyData(["analysisAreaList"]);
      // this.destroyData(
      //   step === 3
      //     ? ["policeAndCaseTrackList", "analysisResultList"]
      //     : ["peopleTrackList", "collisionAreas"]
      // );
    },
    // 右侧结果取消
    handleCancel() {
      this.showRightBox = false;
      this.$refs.leftBox.resetResult();
    },
    closeInfoWindow() {
      this.$refs.mapBase.closeInfoWindow();
    },
  },
};
</script>

<style lang="less" scoped>
.case-to-person {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
