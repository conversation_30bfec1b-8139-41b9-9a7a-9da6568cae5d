<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="thumbnail">
              <img v-lazy="datailsInfo.previewImage" alt="" />
              <span class="similarity" v-if="datailsInfo.similarity">
                {{ datailsInfo.similarity }}%
              </span>
            </div>
            <div class="record-title">
              <span class="active">抓拍记录</span>
            </div>
            <div class="through-record">
              <div class="wrapper-content">
                <span class="label">抓拍地点</span>：
                <span
                  class="message ellipsis"
                  v-html="datailsInfo.cameraName"
                  :title="
                    datailsInfo.cameraName
                      ? datailsInfo.cameraName.replace(/(<\/?span.*?>)/gi, '')
                      : ''
                  "
                ></span>
              </div>
              <div class="wrapper-content">
                <span class="label">抓拍时间</span>：
                <span class="message">{{ datailsInfo.startTime || "--" }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in showList"
                :key="index"
              >
                <span
                  class="label"
                  :class="{ maxLabel: item.title.length > 4 }"
                >
                  {{ item.title }}
                </span>
                <span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">
                  {{
                    datailsInfo[item.key]
                      | commonFiltering(translate(item.dictionary))
                  }}
                </span>
                <span class="message" v-else-if="item.type == 'wear'">
                  {{ handleWear(datailsInfo[item.key]) }}
                </span>
                <span class="message" v-else>
                  {{ datailsInfo[item.key] || "--" }}
                </span>
              </div>
            </div>
            <!-- <div class="structuring">
                            <p class="structuring-title">结构化信息:</p>
                            <ul class="struct-box-ul">
                                <li v-for="( item, index) in showList" :key="index" class="struct-box-list">
                                    <p class="struct-title">{{ item.title }}</p><span>:</span>
                                    <p class="struct-content" v-if="item.type == 'filter'">{{ datailsInfo[item.key] | commonFiltering(translate(item.dictionary)) }}</p>
                                    <p class="struct-content" v-else-if="item.type == 'wear'">{{ handleWear(datailsInfo[item.key]) }}</p>
                                    <p class="struct-content" v-else>{{ datailsInfo[item.key] || '--' }}</p>
                                </li>
                            </ul>
                        </div> -->
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleLeft"
            ></i>
            <details-largeimg
              boxSeleType="faceRect"
              :btnJur="btnJur"
              :info="datailsInfo"
              :algorithmType="algorithmType"
              sceneImgKey="originalImage"
              smallImgKey="previewImage"
              @onAction="multilAnalysisHandler"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleRight"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleLeft"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="index"
            >
              <div class="img-box">
                <img v-lazy="item.previewImage" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleRight"
        ></i>
      </footer>
    </div>
    <detailMutiAnalysis
      v-if="isShowMutilAnalysis"
      ref="detailMutiAnalysisRef"
      :tableList="multiDataList"
      :isNoPage="true"
      @close="isShowMutilAnalysis = false"
    ></detailMutiAnalysis>
  </div>
</template>

<script>
import detailsLargeimg from "./details-largeimg.vue";
import { gaitList, nonmotorList } from "./structuring.js";
import { commonMixins } from "@/mixins/app.js";
import { cutMixins } from "./mixins.js";
import { mutilMixins } from "./mutil-mixins.js";
import detailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
export default {
  name: "",
  mixins: [commonMixins, cutMixins, mutilMixins], //全局的mixin
  components: {
    detailsLargeimg,
    detailMutiAnalysis,
  },
  data() {
    return {
      cutList: [],
      activeIndex: 0,
      showList: [],
      gaitList,
      nonmotorList,
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("../../assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      btnJur: ["btxl", "tp", "rl", "ytst", "fd", "sx", "xz", "dmt"],
      algorithmType: null,
    };
  },
  watch: {},
  computed: {
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    // 开始数据
    init(row, list, index, page, algorithmType) {
      this.cutList = [];
      this.datailsInfo = {};
      this.activeIndex = 0;
      this.showList = this.gaitList;
      this.algorithmType = algorithmType;
      this.cutList = list;
      this.activeIndex = index;
      this.datailsInfo = row;
      let small_pic = document.getElementById("scroll-ul");
      small_pic.style.left = 0;
      this.setPageInfo({
        startPage: page,
        endPage: page,
      });
      this.num = index;
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(index);
      });
    },
    // 切换
    handleTab(item, index) {
      this.resetData();
      this.datailsInfo = item;
      this.activeIndex = index;
      this.play(index);
    },
    scrollIntoViews() {
      this.$nextTick(() => {
        let ul = document.querySelector(".list-wrapper");
        let li = [...ul.childNodes][this.activeIndex];
        li.scrollIntoView({
          behavior: "smooth",
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
</style>
