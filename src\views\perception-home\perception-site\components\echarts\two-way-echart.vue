<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=xBiVxCuuRy
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    list: { // 数据列表
      type: Array,
      default: () => {
        return [
          {
            name: '规则任务',
            type: '1',
            data: []
          },
          {
            name: '模型任务',
            type: '2',
            data: []
          }
        ]
      }
    },
    names: {
      type: Array,
      required: true,
      default: () => ['未开始', '运行中', '已终止', '运行成功 ', '运行出错']
    }
  },
  data () {
    return {
      myEchart: null
    }
  },
  mounted () {
    this.init()
  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      const textColor = 'rgba(255, 255, 255, 0.75)'
      var ruleMax = 0 // 规则背景按最大值
      var modelMax = 0 // 模型背景按最大值
      this.list[0].data.forEach(v => {
        if (v.value > ruleMax) {
          ruleMax = v.value
        }
      })
      this.list[1].data.forEach(v => {
        if (v.value > modelMax) {
          modelMax = v.value
        }
      })
      const ruleMaxData = []
      const modelMaxData = []
      this.list[0].data.forEach(v => {
        ruleMaxData.push({
          value: ruleMax || (modelMax || 1),
          type: v.type,
          status: v.status
        })
      })
      this.list[1].data.forEach(v => {
        modelMaxData.push({
          value: modelMax || (ruleMax || 1),
          type: v.type,
          status: v.status
        })
      })
      const option = {
          timeline: {
            show: false,
            top: 0,
            data: []
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'none'
            },
            formatter: function (params) {
              return `
                <div>${params[1].seriesName}</div>
                <div style="display: flex; align-items: center;margin-top: 4px">
                  <span style="display: inline-block;width: 10px;height: 10px;border-radius: 50%;background: ${params[1].data.color};margin-right: 8px"></span><span>${_that.names[params[1].dataIndex]}：${params[1].value}</span>
                </div>
              `
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: {
              color: '#fff'
            }
          },
          grid: [{
              show: false,
              left: '2%',
              top: '2%',
              bottom: '0%',
              containLabel: true,
              width: '38%'
          }, {
              show: false,
              left: '52%',
              top: '2%',
              bottom: '9.5%',
              width: '0%'
          }, {
              show: false,
              right: '2%',
              top: '2%',
              bottom: '0%',
              containLabel: true,
              width: '38%'
          }],
          xAxis: [{
              type: 'value',
              inverse: true,
              axisLine: {
                  show: true,
                  lineStyle: {
                    color: '#07355E'
                  }
              },
              axisTick: {
                show: false
              },
              position: 'bottom',
              interval: 1,
              axisLabel: {
                show: true,
                color: textColor
              },
              splitLine: {
                show: false
              }
          }, {
              gridIndex: 1,
              show: false
          }, {
              gridIndex: 2,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#07355E'
                }
              },
              axisTick: {
                show: false
              },
              position: 'bottom',
              interval: 1,
              axisLabel: {
                show: true,
                color: textColor
              },
              splitLine: {
                show: false
              }
          }],
          yAxis: [{
              type: 'category',
              inverse: true,
              position: 'right',
              axisLine: {
                show: false
              },

              axisTick: {
                show: false
              },
              axisLabel: {
                show: false
              },
              data: this.names
          }, {
              gridIndex: 1,
              type: 'category',
              inverse: true,
              position: 'left',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: textColor,
                  fontSize: 12
                },
                align: 'center'
              },
              data: this.names
          }, {
              gridIndex: 2,
              type: 'category',
              inverse: true,
              position: 'left',
              axisLine: {
                show: false
              },
              axisTick: {
                show: false
              },
              axisLabel: {
                show: false
              },
              data: this.names
          }],
          series: [
            {
              name: '规则背景',
              type: 'bar',
              show: false,
              z: -10,
              barWidth: 12,
              barGap: '-100%',
              itemStyle: {
                normal: {
                  color: 'rgba(7, 53, 94, 0.2)',
                  barBorderRadius: 6
                }
              },
              data: ruleMaxData
            },
            {
            name: '规则任务',
            type: 'bar',
            barWidth: 12,
            stack: '1',
            itemStyle: {
              normal: {
                color: function (params) {
                  return _that.ruleSwitchColor(params)
                },
                barBorderRadius: 6
              }
            },
            data: this.list[0].data
            // animationEasing: 'elasticOut'
          },
          {
            name: '模型背景',
            type: 'bar',
            show: false,
            z: -10,
            barWidth: 12,
            xAxisIndex: 2,
            yAxisIndex: 2,
            barGap: '-100%',
            data: modelMaxData,
            itemStyle: {
              normal: {
                color: 'rgba(7, 53, 94, 0.2)',
                barBorderRadius: 6
              }
            }
          },
          {
            name: '模型任务',
            type: 'bar',
            stack: '2',
            barWidth: 12,
            xAxisIndex: 2,
            yAxisIndex: 2,
            itemStyle: {
              normal: {
                color: function (params) {
                  return _that.modelSwitchColor(params)
                },
                barBorderRadius: 6
              }
            },
            data: this.list[1].data
            // animationEasing: 'elasticOut'
          }
        ]
      }
      this.myEchart.setOption(option)
      this.myEchart.on('click', function (params) {
        const query = {
          taskType: params.data.type,
          status: params.data.status
        }
        _that.$router.push({
          path: '/label-analysis/label-task',
          query: {
            taskInfo: JSON.stringify(query)
          }
        })
      })
      window.addEventListener('resize', () => this.myEchart.resize())
    },

    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    },
    ruleSwitchColor (params) {
      if (params.data.status === '1') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#797F82' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#2E3537' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '2') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#BF5E1D' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#F2AF1C' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '3') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#D45321' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#740D0D' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '4') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#5D9FF4' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#1F3FE5' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '5') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#60C80B' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#0D710D' // 100% 处的颜色
        }], false)
      }
    },
    modelSwitchColor (params) {
      if (params.data.status === '1') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#2E3537' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#797F82' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '2') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#F2AF1C' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#BF5E1D' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '3') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#740D0D' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#D45321' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '4') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#1F3FE5' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#5D9FF4' // 100% 处的颜色
        }], false)
      } else if (params.data.status === '5') {
        return new echarts.graphic.LinearGradient(0, 0, 1, 0, [{
          offset: 0,
          color: '#0D710D' // 0% 处的颜色
        }, {
          offset: 1,
          color: '#60C80B' // 100% 处的颜色
        }], false)
      }
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
