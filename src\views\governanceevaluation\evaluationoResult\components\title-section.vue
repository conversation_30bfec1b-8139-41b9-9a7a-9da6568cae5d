<template functional>
  <div class="title-section">
    <p>
      <i class="icon-font icon-lishiqushi vt-middle mr-xs"></i>
      <span class="vt-middle f-14">{{ props.titleName }}</span>
    </p>
    <slot name="content"> </slot>
  </div>
</template>
<script></script>
<style lang="less" scoped>
.title-section {
  height: 44px;
  line-height: 44px;
  color: var(--color-title-echarts);
  background: var(--bg-sub-echarts-title);
  display: flex;
  justify-content: space-between;
  padding: 0 10px;

  .icon-font {
    color: var(--color-icon-echarts);
  }
}
</style>
