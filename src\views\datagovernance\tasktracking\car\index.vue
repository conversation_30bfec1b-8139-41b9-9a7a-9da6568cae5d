<template>
  <div class="facedata">
    <div v-for="(item, index) in aggregateOptions" :key="index">
      <aggre-connect :propData="item" dWidth="13.8%" bWidth="14.8%"></aggre-connect>
    </div>
    <!-- 图像抓拍时间准确性检测  1 -->
    <image-capture-time ref="carModal1" :curIndex="1" />
    <image-upload ref="carModal2" :curIndex="2" />
    <big-imag-url ref="carModal3" :curIndex="3" />
    <image-license-plate ref="carModal4" :curIndex="4" />
    <vehicleStructure ref="carModal5" :curIndex="5" />
    <data-output ref="carModal6" :curIndex="6" />
    <div class="viewLoading">
      <loading v-if="viewLoading"></loading>
    </div>
  </div>
</template>
<script>
import faceEnum from './util/enum';
import api from '@/config/api/car-threm.js';
export default {
  props: {
    id: {
      type: Number,
    },
    importTotalCount: {},
  },
  name: 'facedata',
  data() {
    return {
      aggregateOptions: faceEnum.aggregateOptions,
      viewLoading: false,
    };
  },
  async created() {
    await this.getViewList();
    if (`${this.$route.query.isExport}` === 'true' && this.$route.query.id == 3) {
      let [option] = [...faceEnum.aggregateOptions].reverse();
      this.$refs.carModal6.init(option.datas[0]);
    }
  },
  methods: {
    async getViewList() {
      this.$http
        .get(api.queryComponentStatisticsByTopicId + '3')
        .then((res) => {
          if (res.data.code == 200) {
            this.handleData(res.data.data);
          } else {
            this.$Message.error(res.data.msg);
          }
          this.viewLoading = false;
        })
        .catch(() => {
          this.viewLoading = false;
        });
    },
    async handleData(aggregateList) {
      // 处理接口数据变成对象
      let aggregateObject = {};
      await aggregateList.forEach((element) => {
        aggregateObject[element.topicComponentId] = element;
      });
      // 面板数据拍平
      let allAggregateDatas = [];
      await this.aggregateOptions.forEach((item) => {
        allAggregateDatas.push(...item.datas);
      });
      // 面板datas数据添加filedData字段（后端返回的所有数据）
      await allAggregateDatas.forEach((item) => {
        if (item.topicComponentId in aggregateObject) {
          item.filedData = aggregateObject[item.topicComponentId];
          let listData = item.filedData;
          if (!listData) return;
          if (listData.accessDataCount) {
            listData.successData = listData.accessDataCount - listData.existingExceptionCount;
            listData.successDataRate = (listData.successData / listData.accessDataCount) * 100;
            if (!listData.governanceOptimizationCount) {
              listData.governanceOptimizationCount = 0;
            }
            if (!listData.algorithmCount) {
              listData.algorithmCount = 0;
            }
          } else {
            listData.successData = 0;
            listData.successDataRate = 0 + '%';
          }
          item.list.forEach((one) => {
            if (one.fileName in listData) {
              one.num = listData[one.fileName];
              if (
                ['existingExceptionRate', 'qualifiedRate', 'governanceOptimizationRate', 'successDataRate'].includes(
                  one.fileName,
                )
              ) {
                one.num = listData[one.fileName] ? listData[one.fileName] * 100 : 0;
                one.num = one.num.toFixed(2) + '%';
              }
            }
          });
        }
      });
    },
  },
  watch: {
    $route: {
      handler() {
        if (`${this.$route.query.isExport}` === 'true' && this.$route.query.id == 3) {
          let [option] = [...faceEnum.aggregateOptions].reverse();
          this.$refs.carModal6.init(option.datas[0]);
        }
      },
      deep: true,
    },
  },
  components: {
    AggreConnect: require('../../governancetheme/components/aggre-connect.vue').default,
    dataOutput: require('./components/data-output').default,
    imageCaptureTime: require('./components/image-capture-time').default,
    imageUpload: require('./components/image-upload').default,
    bigImagUrl: require('./components/big-imag-url').default,
    imageLicensePlate: require('./components/image-license-plate').default,
    vehicleStructure: require('./components/vehicle-structure').default,
  },
};
</script>
<style lang="less" scoped></style>
