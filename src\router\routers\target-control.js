/** When your routing table is too long, you can split it into small modules**/
import BasicLayout from "@/layouts/basic-layout";
export default [
  {
    path: "/target-control3",
    name: "target-control3",
    component: BasicLayout,
    children: [
      {
        path: "/target-control/control-task/add",
        name: "control-task-add",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/people/add.vue",
          ], resolve),
        meta: {
          title: "新增人员布控",
        },
      },
      {
        path: "/target-control/control-task/edit",
        name: "control-task-edit",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/people/add.vue",
          ], resolve),
        meta: {
          title: "编辑人员布控",
        },
      },
      {
        path: "/target-control/control-task/detail",
        name: "control-task-detail",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/people/detail.vue",
          ], resolve),
        meta: {
          title: "人员布控详情",
        },
      },
      {
        path: "/target-control/control-task/addVehicle",
        name: "control-task-addVehicle",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/vehicle/add.vue",
          ], resolve),
        meta: {
          title: "新增车辆布控",
        },
      },
      {
        path: "/target-control/control-task/editVehicle",
        name: "control-task-editVehicle",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/vehicle/add.vue",
          ], resolve),
        meta: {
          title: "编辑车辆布控",
        },
      },
      {
        path: "/target-control/control-task/detailVehicle",
        name: "control-task-detailVehicle",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/vehicle/detail.vue",
          ], resolve),
        meta: {
          title: "车辆布控详情",
        },
      },
      {
        path: "/target-control/control-task/addSensory",
        name: "control-task-addSensory",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/sensory/add.vue",
          ], resolve),
        meta: {
          title: "新增感知布控",
        },
      },
      {
        path: "/target-control/control-task/editSensory",
        name: "control-task-editSensory",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/sensory/add.vue",
          ], resolve),
        meta: {
          title: "编辑感知布控",
        },
      },
      {
        path: "/target-control/control-task/detailSensory",
        name: "control-task-detailSensory",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/sensory/detail.vue",
          ], resolve),
        meta: {
          title: "感知布控详情",
        },
      },
      {
        path: "/target-control/control-task/addMulti",
        name: "control-task-addMulti",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/multi/add.vue",
          ], resolve),
        meta: {
          title: "新增多维布控",
        },
      },
      {
        path: "/target-control/control-task/editMulti",
        name: "control-task-editMulti",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/multi/add.vue",
          ], resolve),
        meta: {
          title: "编辑多维布控",
        },
      },
      {
        path: "/target-control/control-task/detailMulti",
        name: "control-task-detailMulti",
        tabShow: true,
        parentName: "target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/target-control/control-task/multi/detail.vue",
          ], resolve),
        meta: {
          title: "多维布控详情",
        },
      },
    ],
  },
];
