<template>
  <div class="statistics-container">
    <div class="detail-wrapper" v-for="(item, index) in columns" :key="index">
      <span :class="['iconfont', 'f-28', item.iconStyle, item.icon]"></span>
      <div class="count-wrapper ml-10">
        <span class="f-12 desc">{{ item.desc }}</span>
        <count-to :class="[item.descStyle, 'f-18']" :start-val="0" :end-val="data[item.key] || 0"></count-to>
      </div>
    </div>
  </div>
</template>
<script>
import CountTo from 'vue-count-to'

export default {
  name: 'statistics.vue',
  components: { CountTo },
  props: {
    data: {},
    columns: {}
  },
  data() {
    return {}
  }
}
</script>
<style scoped lang="less">
.icon-bg-blue {
  background: linear-gradient(228deg, #5bcaff 1%, #2c86f8 98%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-bg-green {
  background: linear-gradient(222deg, #27d676 5%, #36be7f 94%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-bg-yellow {
  background: linear-gradient(223deg, #ffcc65 1%, #e77811 96%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.color-blue {
  color: #2c86f8;
}
.color-green {
  color: #1faf81;
}
.color-yellow {
  color: #f29f4c;
}

.f-28 {
  font-size: 28px;
}
.statistics-container {
  position: relative;
  height: 100%;
  display: flex;
  .detail-wrapper {
    position: relative;
    display: flex;
    margin-right: 20px;
    &:after {
      content: '';
      right: 0;
      position: absolute;
      height: 22px;
      width: 1px;
      background: #d3d7de;
      transform: translate(10px, 50%);
    }
    &:last-child {
      margin-right: 0;
      &:after {
        display: none;
      }
    }
    .count-wrapper {
      display: flex;
      flex-direction: column;
      .desc {
        color: rgba(0, 0, 0, 0.35);
      }
    }
  }
}
</style>