<template>
  <customize-filter
    v-model="customSearch"
    :customize-action="customizeAction"
    :content-style="contentStyle"
    :checkbox-list="getQuestionList"
    :default-checked-list="defaultCheckedList"
    :field-name="fieldName"
    @confirmFilter="confirmFilter"
  >
    <template #leftSlot="{ originCheckedList }">
      <ui-search-tree
        ref="uiTree"
        class="customize-tree mr-sm"
        :no-search="false"
        :show-checkbox="true"
        :highlight-current="false"
        :simple-checked-nodes="true"
        node-key="id"
        :tree-data="questionTreeList"
        :default-props="defaultProps"
        :default-checked-keys="originCheckedList"
        expandAll
        @simpleCheckedNodes="checkChange"
      >
        <!-- <template #label="{ node, data }">
          <div class="custom-tree-node">
            <span>{{ node.label }}</span>
            <Poptip title=""
                    transfer
                    content="content"
                    placement="left-start"
                    v-model="node.poptipshow">
              <p @click="openAction(node, data)">Right Top</p>
              <template #content>
                <p><Button>Info</Button></p>
                <p><Button>Info</Button></p>
                <p><Button>Info</Button></p>
              </template>
            </Poptip>
          </div>
        </template> -->
      </ui-search-tree>
    </template>
  </customize-filter>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    value: {},
  },
  data() {
    return {
      customSearch: false,
      customizeAction: {
        title: '添加关联问题',
        leftContent: '请选择关联问题',
        rightContent: '已选关联问题',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: '3.125rem',
      },
      allDeviceFileList: [],
      defaultCheckedList: [],
      fieldName: {
        id: 'id',
        value: 'name',
      },
      defaultProps: {
        id: 'id',
        label: 'name',
        children: 'children',
      },
    };
  },
  created() {
    this.setQuestionList();
    this.setIndexModuleList();
  },
  methods: {
    ...mapActions({
      setQuestionList: 'knowledgebase/setQuestionList',
      setIndexModuleList: 'knowledgebase/setIndexModuleList',
    }),
    confirmFilter(data) {
      this.customSearch = false;
      this.$emit('getRelateQuetion', data);
    },
    checkChange(checkedKeys) {
      this.defaultCheckedList = [];
      checkedKeys.forEach((item) => {
        if ('children' in item) {
          let ids = item.children.map((one) => one.id);
          this.defaultCheckedList = [...this.defaultCheckedList, ...ids];
        } else {
          this.defaultCheckedList.push(item.id);
        }
      });
    },
  },
  watch: {
    value(val) {
      this.customSearch = val;
    },
    customSearch(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    questionTreeList() {
      let getQuestionObject = {};
      this.getQuestionList.forEach((item) => {
        this.$set(item, 'name', item.errorMessage);
        if (!(item.indexModule in getQuestionObject)) {
          getQuestionObject[item.indexModule] = {};
        }
        if ('children' in getQuestionObject[item.indexModule]) {
          getQuestionObject[item.indexModule].children.push(item);
        } else {
          getQuestionObject[item.indexModule].children = [item];
        }
      });
      return Object.keys(getQuestionObject).map((key) => {
        return {
          id: this.getIndexModuleObject[key],
          name: this.getIndexModuleObject[key],
          children: getQuestionObject[key].children,
        };
      });
    },
    ...mapGetters({
      getQuestionList: 'knowledgebase/getQuestionList',
      getIndexModuleObject: 'knowledgebase/getIndexModuleObject',
    }),
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    CustomizeFilter: require('@/components/customize-filter').default,
  },
};
</script>
<style lang="less" scoped></style>
