<template>
  <div class="detail-content auto-fill">
    <Form class="form" ref="form" autocomplete="off" label-position="right" :model="formData" :rules="formRules">
      <FormItem class="form-item" label="检测对象" required>
        <api-area-tree
          class="api-area-tree width-380"
          :disabled="true"
          :select-tree="selectAreaTree"
          placeholder="请选择检测对象"
        ></api-area-tree>
      </FormItem>
      <FormItem class="form-item" label="检测平台" prop="orgCodes" required>
        <Button @click="chooseCode" class="width-lg" :class="{ 'error-btn': !formData.orgCodeList.length }">
          <span v-if="formData.orgCodeList && formData.orgCodeList.length === 0">请选择待检测的互联平台</span>
          <span v-else>{{ `已选择 ${(formData.orgCodeList && formData.orgCodeList.length) || 0}个` }}</span>
        </Button>
      </FormItem>
      <FormItem class="form-item" label="检测方案" prop="plan" required>
        <RadioGroup v-model="formData.plan">
          <Radio :label="1">检测设备离线情况判定平台是否离线</Radio>
          <Radio :label="2">对接平台保活信息判定平台是否离线</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        class="form-item"
        v-if="showFormItems.includes('checkRate')"
        label="检测设备范围"
        prop="checkRate"
        required
      >
        <div>
          <Select
            class="width-380"
            v-model="formData.deviceQueryForm.checkRate"
            placeholder="请选择检测设备范围"
            @on-change="changeCheckRate"
          >
            <Option v-for="item in checkRateList" :key="item.value" :value="item.value">{{ item.label }}</Option>
          </Select>
        </div>
        <div
          v-if="formData.deviceQueryForm.checkRate === 1"
          class="camera mt-sm"
          :class="{ 'error-btn': !formData.deviceQueryForm.ids.length }"
          @click="chooseCheckRate"
        >
          <span class="font-blue camera-text" v-if="formData.deviceQueryForm.ids.length"
            >已选择{{ formData.deviceQueryForm.ids.length }}条设备</span
          >
          <span v-else>
            <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
            <span class="font-blue camera-text">请选择检测设备范围</span>
          </span>
        </div>
      </FormItem>
      <!-- 检测设备范围 选择 【全部设备】后，检测方式 默认为 【抽检】，且禁止选择 -->
      <FormItem
        class="form-item"
        v-if="showFormItems.includes('detectMode')"
        label="检测方式"
        prop="detectMode"
        required
      >
        <Select
          class="width-380"
          v-model="formData.detectMode"
          placeholder="请选择检测方式"
          :disabled="formData.deviceQueryForm.checkRate === 0"
        >
          <Option v-for="item in detectModeList" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
        <p class="check-plan mt-md" v-if="formData.detectMode === '2'">
          <span>每平台检测数量：</span>
          <Input class="mr-xs width-60" v-model="formData.deviceNum" maxlength="3" />
          <span>路</span>
        </p>
        <p class="font-p">说明：检测的某平台的所有设备均离线则认为该平台离线。</p>
      </FormItem>
      <FormItem class="form-item" v-if="showFormItems.includes('checkType')" label="设备离线" prop="checkType" required>
        <RadioGroup v-model="formData.deviceQueryForm.checkType">
          <Radio :label="0" class="mr-lg">联网平台返回【离线】状态</Radio>
          <Radio :label="1">
            <Input
              class="mr-xs width-60"
              v-model="formData.deviceQueryForm.visitTimeout"
              :disabled="formData.deviceQueryForm.checkType === 0"
            />秒内无法拉流成功!
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        class="form-item form-item-end"
        v-if="showFormItems.includes('cronNum')"
        label="检测计划"
        prop="cronNum"
        required
      >
        <p class="check-plan">
          每
          <Input v-model="formData.cronNum" class="ml-sm mr-sm width-60" @on-change="handleChangeTime" />
          <Select v-model="formData.cronType" transfer class="width-80">
            <Option value="4">分</Option>
            <Option value="5">时</Option>
          </Select>
          <span class="ml-xs">检测一次</span>
        </p>
      </FormItem>
    </Form>

    <OrgModal
      v-model="orgModalVisible"
      @query="choosQuery"
      :org-list="formData.orgCodeList"
      :form-model="formModel"
      :area-tree-data="areaTreeData"
      v-if="areaTreeData.length != 0"
    ></OrgModal>

    <select-device
      v-model="selectDeviceVisible"
      :tree-data="selectAreaTreeNodeList"
      :org-code-list="formData.orgCodeList"
      :select-device-ids="formData.deviceQueryForm.ids"
      :default-checked-node-list="defaultCheckedNodeList"
      :default-select-device-data="allSelectDeviceData"
      v-bind="[$attrs, $props]"
      @selectedDevice="selectedDevice"
    ></select-device>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateTimePass = (rule, value, callback) => {
      const regx = /^\+?[1-9][0-9]*$/;
      !regx.test(value) && value !== '' ? callback(new Error('请输入大于0整数！')) : '';
      if (this.formData.cronType === '4' && (value < 30 || value > 60)) {
        callback(new Error('选择分钟为单位，仅支持输入30-60分钟；如需更大时间间隔，请选择小时为单位！'));
      }
      if (this.formData.cronType === '5') {
        const regx = /^\+?[1-9][0-9]*$/;
        !regx.test(value) ? callback(new Error('请输入正整数！')) : '';
      }
      callback();
    };
    const validateNumPass = (rule, value, callback) => {
      const regx = /^\+?[1-9][0-9]*$/;
      !regx.test(value) && value !== '' ? callback(new Error('请输入大于0的整数！')) : '';
      if (value !== '' && value > 100) {
        callback(new Error('最大支持抽检100路设备！'));
      }
      callback();
    };
    const validateCheckType = (rule, value, callback) => {
      let { checkType, visitTimeout } = this.formData.deviceQueryForm;
      if (checkType === 1 && visitTimeout < 0) {
        callback(new Error('请输入正整数！'));
      } else if (checkType === 1 && !visitTimeout) {
        callback(new Error('请输入拉流等待时间'));
      } else if (checkType !== 0 && !checkType) {
        callback(new Error('请选择设备离线'));
      }
      callback();
    };
    const validateDetectMode = (rule, value, callback) => {
      if (value === '2' && this.formData.deviceNum < 0) {
        callback(new Error('请输入正整数！'));
      } else if (value === '2' && !this.formData.deviceNum) {
        callback(new Error('请输入每平台检测数量'));
      } else if (!value) {
        callback(new Error('请选择检测方式'));
      }
      callback();
    };
    const validateCheckRate = (rule, value, callback) => {
      let {
        deviceQueryForm: { checkRate, ids },
      } = this.formData;
      if ((checkRate === 1 && !ids.length) || (!checkRate && checkRate !== 0)) {
        callback(new Error('请选择检测设备范围'));
      }
      callback();
    };
    return {
      formData: {
        regionCode: '', //检测对象
        orgCodes: '',
        orgCodeList: [], // 检测平台
        detectMode: '2', // 检测方式  string  1 全检 ，  2 抽检
        cronType: '4', // 检测计划 -- 分/时
        cronNum: null, // 检测计划  - xxx
        cronData: [],
        deviceNum: 20, // 抽检数量
        deviceQueryForm: {
          checkType: 0, // 设备离线 --- number  0 离线状态，1 拉流状态
          checkRate: 0, // 检测设备范围 number 0 全部设备，1 自定义
          visitTimeout: null, // 拉流超时时间
          ids: [], //检测设备范围  为 自定义时，存放已选择 设备id
        },
        plan: 1, //检测方案
      },
      formRules: {
        orgCodes: [
          {
            required: true,
            message: '请选择检测平台',
          },
        ],
        checkRate: [
          {
            required: true,
            validator: validateCheckRate,
          },
        ],
        detectMode: [
          {
            required: true,
            validator: validateDetectMode,
          },
        ],
        cronNum: [
          {
            required: true,
            validator: validateTimePass,
          },
        ],
        deviceNum: [
          {
            required: true,
            validator: validateNumPass,
          },
        ],
        checkType: [
          {
            required: false,
            validator: validateCheckType,
          },
        ],
      },
      // 检测平台 -- 即 选择组织机构
      selectAreaTreeNodeList: [],
      selectAreaTree: {
        regionCode: '',
      },
      areaTreeData: [],
      orgModalVisible: false,
      detectModeList: [
        {
          label: '全检',
          value: '1',
        },
        {
          label: '抽检',
          value: '2',
        },
      ],
      checkRateList: [
        {
          label: '全部设备',
          value: 0,
        },
        {
          label: '自定义',
          value: 1,
        },
      ],
      selectDeviceVisible: false,
      defaultCheckedNodeList: [],
      allSelectDeviceData: [], // 已选择设备 的设备信息对象
    };
  },
  computed: {
    showFormItems() {
      //根据检测方案动态变化展示item和表单rules
      if (this.formData.plan === 2) {
        return ['cronNum'];
      } else {
        return ['checkRate', 'detectMode', 'checkType', 'cronNum'];
      }
    },
  },
  created() {},
  mounted() {
    this.getOrgTreeList();
    this.getSelectDeviceList();
  },
  watch: {
    moduleAction: {
      handler(val) {
        if (!val) return;
        this.selectAreaTree.regionCode = val.regionCode;
      },
      deep: true,
      immediate: true,
    },
    formModel: {
      async handler(val) {
        if (val === 'edit') {
          let deviceQueryFormCopy = this.$util.common.deepCopy(this.formData.deviceQueryForm);
          Object.keys(this.configInfo).forEach((key) => {
            this.$set(this.formData, key, this.configInfo[key]);
          });
          // 这一步是为了 兼容历史数据， 因为 上一步  deviceQueryForm对象会被替换掉，会导致新增的字段，失去响应式
          Object.keys(deviceQueryFormCopy).map((key) => {
            this.$set(this.formData.deviceQueryForm, key, this.formData.deviceQueryForm[key] || '');
          });
          let orgCodeList = (this.formData.orgCodes && this.formData.orgCodes.split(',')) || [];
          this.formData.orgCodeList = orgCodeList.filter(Boolean);

          // 兼容下 历史数据 （主要是后端存的是 json，无法通过刷脚本）
          let {
            deviceQueryForm: { checkType, checkRate },
            detectMode,
          } = this.formData;
          checkType !== 0 && !checkType ? (this.formData.deviceQueryForm.checkType = 0) : ''; // 设备离线 --- number  0 离线状态，1 拉流状态
          checkRate !== 0 && !checkRate ? (this.formData.deviceQueryForm.checkRate = 0) : ''; // 检测设备范围 number 0 全部设备，1 自定义
          detectMode === '3' ? (this.formData.detectMode = '2') : ''; // 检测方式  只有  '2':抽检  '1':全检     兼容历史数据 '3':自定义
        }
      },
      immediate: true,
    },
    // 设备离线
    'formData.deviceQueryForm.checkType': {
      handler(val) {
        if (val === 0) {
          this.formData.deviceQueryForm.visitTimeout = null;
        }
      },
    },
    // 检测设备范围
    'formData.deviceQueryForm.checkRate': {
      handler(val) {
        if (val === 1) {
          this.formData.deviceQueryForm.ids = [];
        }
      },
    },
    // 检测方式
    'formData.detectMode': {
      handler(val) {
        if (val === '1') {
          this.formData.deviceNum = null;
        }
      },
    },
  },

  methods: {
    // 检测设备范围  需要特殊校验： 当【自定义】时，选择的 检测平台 必须都选择了设备
    async isAllOrgSelectedDevice() {
      let {
        deviceQueryForm: { checkRate, ids },
      } = this.formData;
      if (checkRate === 1 && ids.length > 0) {
        // 需判断 每个可选节点 是否都已有选择项，没有则需要提示
        let copyTreeData = this.$util.common.deepCopy(this.selectAreaTreeNodeList);
        let nodeArr = this.$util.common.jsonToArray(copyTreeData);
        let isSelectNodeArr = nodeArr.filter((item) => !item.disabled);
        let flagArr = [];
        isSelectNodeArr.map((data) => {
          let flag = this.allSelectDeviceData.some((item) => {
            return data.childrenOrgs?.includes(item.orgCode) || item.orgCode === data.orgCode;
          });
          flagArr.push(flag);
        });
        if (flagArr.some((item) => item === false)) {
          this.$Message.warning('存在检测平台没有选择设备，请检查！');
          return true;
        }
        return false;
      } else if (checkRate !== 1) {
        return false;
      }
    },
    // 必填校验
    async handleSubmit() {
      let flag = await this.isAllOrgSelectedDevice();
      if (flag) return false;

      return this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.handleParams();
          return true;
        } else {
          this.$Message.error('请填写必须要填写的信息');
          return false;
        }
      });
    },
    // 选择组织机构
    async getOrgTreeList() {
      try {
        let res = await this.$http.get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.selectAreaTree.regionCode },
        });
        this.areaTreeData = this.$util.common.arrayToJson(res.data.data || [], 'id', 'parentId');
      } catch (err) {
        console.log(err);
      }
    },
    async chooseCode() {
      if (!this.areaTreeData.length) {
        await this.getOrgTreeList();
      }
      this.$nextTick(() => {
        this.orgModalVisible = true;
      });
    },
    // 检测计划
    handleChangeTime() {
      let { cronNum } = this.formData;
      this.formData.cronData = cronNum ? [parseInt(cronNum)] : [];
    },
    changeCheckRate() {
      if (this.formData.deviceQueryForm.checkRate === 0) {
        this.formData.detectMode = '2';
      }
    },
    // 选择检测设备范围
    async chooseCheckRate() {
      // 先提示 选择检测平台
      let flag = true;
      this.$refs.form.validateField('orgCodes', (valid) => {
        // valid为空，则表明表单验证通过
        flag = !valid ? true : false;
      });
      if (!flag) return;

      if (!this.areaTreeData.length) {
        await this.getOrgTreeList();
      }
      this.defaultCheckedNodeList = [];
      await this.getSelectAreaTreeNodeList();
      this.selectDeviceVisible = true;
    },
    choosQuery(data) {
      this.formData.orgCodes = data.join(',');
      this.formData.orgCodeList = data;
      // 处理数据，重新组装会 树形结构
      this.getSelectAreaTreeNodeList();
    },
    getSelectAreaTreeNodeList() {
      // 处理数据，重新组装会 树形结构
      let copyData = this.$util.common.deepCopy(this.areaTreeData);
      let arr = this.handerSelectNodeData(copyData);
      this.selectAreaTreeNodeList = this.$util.common.arrayToJson(arr, 'id', 'parentId');
    },
    // 提取已选择的组织节点，包括 已选节点的父节点
    handerSelectNodeData(arr, resArr = [], idsArr = [], parentOrgsArr = []) {
      arr.map((item) => {
        item.childrenOrgs = item.children ? this.getChildrenOrgs(item.children) : []; // 存储该节点所有 子节点的组织编码
        if (item?.children?.length) {
          this.handerSelectNodeData(item.children, resArr, idsArr, [...parentOrgsArr, item.orgCode]);
        }
        if (this.formData.orgCodeList.includes(item.orgCode) || idsArr.includes(item.id)) {
          item.parentOrgs = parentOrgsArr; // 存储该节点 的 父父...组织节点

          delete item.children;
          // 没有勾选的，禁止选择
          item.disabled = idsArr.includes(item.id) && !this.formData.orgCodeList.includes(item.orgCode) ? true : false;
          // 设置默认 勾选第一个可选择的子节点
          !this.defaultCheckedNodeList.length ? (this.defaultCheckedNodeList = [item]) : '';
          resArr.push(item);
          idsArr.push(item.parentId);
        }
      });
      return resArr;
    },
    getChildrenOrgs(arr, list = []) {
      arr.map((item) => {
        list.push(item.orgCode);
        if (item.children) {
          return this.getChildrenOrgs(item.children, list);
        }
      });
      return list;
    },
    selectedDevice({ ids, allSelectedData }) {
      this.formData.deviceQueryForm.ids = ids;
      this.allSelectDeviceData = this.$util.common.deepCopy(allSelectedData);
    },
    // 已选择的 设备信息
    async getSelectDeviceList() {
      try {
        let {
          deviceQueryForm: { checkRate, ids },
        } = this.formData;
        if (ids.length === 0 || checkRate !== 1) return;
        let res = await this.$http.post(equipmentassets.queryDeviceInfoByIds, { ids: ids });
        this.allSelectDeviceData = res.data.data || [];
      } catch (err) {
        console.log(err);
      }
    },
    handleParams() {
      //当plan为2时清空不必要的配置参数
      if (this.formData.plan != 1) {
        this.formData.deviceQueryForm.checkType = 0;
        this.formData.deviceQueryForm.checkRate = 0;
        this.formData.deviceQueryForm.visitTimeout = null;
        this.formData.deviceQueryForm.ids = [];
        this.formData.detectMode = '1';
        this.formData.deviceNum = null;
      }
    },
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    OrgModal: require('../../org-modal/index.vue').default,
    SelectDevice: require('../components/select-device.vue').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 200px;
.form-item {
  @{_deep}.ivu-form-item-label {
    width: @leftMargin;
  }
  @{_deep}.ivu-form-item-content {
    margin-left: @leftMargin;
  }
}
@{_deep} .ivu-form-item-error .error-btn {
  border-color: #bc3c19 !important;
}
.check-plan {
  display: flex;
  align-items: center;
  color: var(--color-content);
}
.width-60 {
  width: 60px !important;
}
.width-80 {
  width: 80px !important;
}
.width-380 {
  width: 380px !important;
}
@{_deep} .select-width {
  .width-380;
}
.mr-xs {
  margin-right: 5px !important;
}
.form-item-end {
  margin-bottom: 40px !important;
}
.font-p {
  color: #c76d28;
}
.camera {
  width: 230px;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
</style>
