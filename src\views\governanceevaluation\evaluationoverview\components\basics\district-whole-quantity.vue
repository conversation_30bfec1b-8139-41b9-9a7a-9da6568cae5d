<template>
  <!-- 全量目录完整率  区-->
  <div class="whole-quantity auto-fill">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="true"></statistics>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-xiugaijilu f-16 color-filter"></i>
          <span class="f-16 color-filter ml-xs">未上报目录</span>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>

      <div class="list auto-fill" ref="basicFullDirNot" v-ui-loading="{ loading: loading, tableData: treeData }">
        <ui-search-tree
          class="auto-fill"
          v-if="treeData.length !== 0"
          node-key="id"
          :no-search="false"
          :max-height="488"
          :tree-data="treeData"
          :default-props="defaultProps"
        >
        </ui-search-tree>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.whole-quantity {
  .information-header {
    margin-top: 10px;
    height: 140px;
    display: flex;
    background: var(--bg-sub-content);
    padding: 20px;
    @{_deep}.information-statistics {
      margin-right: 0 !important;
    }
    @{_deep}.monitoring-icon {
      margin-left: 40px !important;
    }
  }
  .information-main {
    height: calc(100% - 140px);
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .btns {
        .btn_jk {
          border-right: none !important;
        }

        button {
          color: #56789c;
          background-color: #12294e;
          border-radius: 0;
          &:hover {
            color: #fff !important;
            background-color: rgba(43, 132, 226, 1);
          }
        }
        .active {
          color: #fff;
          background-color: rgba(43, 132, 226, 1);
        }
      }
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      background: var(--bg-sub-content);
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'whole-quantity',
  data() {
    return {
      chartDatas: [],
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      rankData: [],
      rankLoading: false,
      taskType: '',
      treeData: [],
      loading: false,
      exportLoading: false,
      statisticsList: [
        {
          name: '应上报派出所数量',
          value: 0,
          icon: 'icon-yingshangbaopaichusuoshuliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
        },
        {
          name: '实际上报派出所数量',
          value: 0,
          icon: 'icon-shijishangbaopaichusuoshuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
        },
        {
          name: '未上报派出所数量',
          value: 0,
          icon: 'icon-weishangbaopaichusuoshuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
        {
          name: '全量目录完整率',
          value: 0,
          icon: 'icon-quanliangmuluwanzhengshuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
        },
      ],
      curBtn: 1,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      municipalchart: {},
      countEchart: {},
      modelTag: 'BASIC_FULL_DIR_NOT',
      region: '',
    };
  },

  activated() {},
  async mounted() {},
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        tab: this.modelTag,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          pageNumber: 0,
          pageSize: 0,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        if (this.modelTag == 'BASIC_FULL_DIR_REPORT') {
          this.tableData = datas.fullDirReport;
        } else {
          if (this.paramsList.displayType == 'ORG') {
            this.defaultProps = { label: 'orgName', children: 'children' };
            this.treeData = this.$util.common.arrayToJson(
              this.$util.common.deepCopy(datas.fullDirNotReport),
              'id',
              'parentId',
            );
          } else {
            this.defaultProps = { label: 'regionName', children: 'children' };
            this.treeData = this.$util.common.arrayToJson(datas.fullDirNotReport, 'regionCode', 'parentCode');
          }
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  props: {
    statisticalData: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val.length !== 0) {
          this.statisticsList[0].value = val.bfdShouldFirstCount;
          this.statisticsList[1].value = val.bfdYetFirstCount;
          this.statisticsList[2].value = val.bfdNotFirstCount;
          this.statisticsList[3].value = val.bfdResultValue;
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getTableData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    statistics: require('@/components/icon-statistics').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
