import { renderHeaderStatistics } from '@/views/specialassessment/utils/menuConfig.js';

const commonTableColumns = (params) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'civilName',
      slot: 'civilName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
      renderHeader: (h, { column, index }) => {
        return renderHeaderStatistics(h, { column, index, params });
      },
    },
    {
      title: '检测数量',
      key: 'detection',
      slot: 'detection',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },

    {
      title: '连续离线或累计离线设备数量',
      slot: 'total',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '连续离线>=X天数量',
      slot: 'unqualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '累计离线>=Y天数量',
      slot: 'qualified',
      align: 'left',
      tooltip: true,
      sortable: 'custom',
      minWidth: 100,
    },
  ];
};

const tableColumns = (params) => {
  return {
    VIDEO_OFFLINE_STAT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '检测数量',
        key: 'detection',
        slot: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '连续离线或累计离线设备数量',
        slot: 'total',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '本月连续离线>=X天数量',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '本月累计离线>=Y天数量',
        slot: 'qualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    FACE_OFFLINE_STAT: commonTableColumns(params),
    VEHICLE_OFFLINE_STAT: commonTableColumns(params),
  };
};
export { tableColumns };
