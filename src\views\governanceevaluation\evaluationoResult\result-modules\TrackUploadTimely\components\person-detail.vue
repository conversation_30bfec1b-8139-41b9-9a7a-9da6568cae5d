<template>
  <!-- 实时轨迹上传及时性 -->
  <ui-modal v-model="captureVisible" title="抓拍详情" :footerHide="true" width="90rem">
    <div class="detail-content">
      <div class="detail-content-left">
        <div v-if="!!personObj">
          <div class="capture-details-left-sculpture">
            <ui-image :src="personObj.identityPhoto" />
          </div>
          <div>
            <span class="sculpture-item-label">姓名：</span>
            <span>{{ personObj.name || '未知' }}</span>
          </div>
          <div class="mt-sm">
            <span class="sculpture-item-label">身份证号：</span>
            <span>{{ personObj.idCard || '未知' }}</span>
          </div>
          <tags-more
            v-if="personObj.personTypes"
            :tagList="personObj.personTypes"
            :defaultTags="4"
            placement="left-start"
            bgColor="#2D435F"
          ></tags-more>
        </div>
      </div>
      <div class="detail-content-right auto-fill">
        <div class="card-wrap auto-fill">
          <!--          <div class="card-wrap-count" v-if="staticsList.length">-->
          <!--            <label v-for="(item, index) in staticsList" :key="index">-->
          <!--              {{ item.label }}：-->
          <!--              <span>{{ item.count }}</span>-->
          <!--            </label>-->
          <!--          </div>-->
          <slot name="searchList"></slot>
          <div class="list auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
            <div class="card-list">
              <div class="card-item" v-for="(item, index) in tableData" :key="item.id">
                <div class="item">
                  <div class="img" @click="viewBigPic(index, item.trackLargeImage)">
                    <ui-image :src="item.trackImage" />
                    <p class="shadow-box" style="z-index: 11" title="查看检测结果" v-if="item.qualified === '2'">
                      <i
                        class="icon-font icon-yichang search-icon mr-xs base-text-color"
                        @click.stop="accuracyDetail(item)"
                      ></i>
                    </p>
                    <div class="num" v-if="item.similarity">
                      {{ similarityVal(item.similarity) }}
                    </div>
                  </div>
                  <div class="group-message">
                    <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据'}`">
                      <i class="icon-font icon-shijian"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{ item.shotTime }}</span>
                    </p>
                    <p :title="item.catchPlace">
                      <i class="icon-font icon-dizhi"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                        item.catchPlace ? item.catchPlace : '暂无数据'
                      }}</span>
                    </p>
                  </div>
                  <i class="icon-font icon-chaoshi" v-if="item.qualified === '2'"></i>
                </div>
              </div>
            </div>
          </div>
          <!-- 分页 -->
          <ui-page
            class="page menu-content-background"
            :page-data="pageData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          ></ui-page>
        </div>
      </div>
    </div>
    <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    <UploadModal ref="accuracyDetail" />
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import api from '@/config/api/inspectionrecord';

export default {
  name: 'personDetail',
  props: {
    resultId: {},
    interFaceName: {
      default: api.pageTrackCatchDetails,
    }, // 接口名称
    staticsList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      captureVisible: false,
      tableData: [],
      searchData: {
        pageNumber: 1,
        pageSize: 20,
        customParameters: {},
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styleScroll6: {
        //   position: 'relative',
        //   width: '100%',
        //   height: '660px',
        //   'overflow-y': 'scroll',
      },
      bigPictureShow: false,
      imgList: [],
      personObj: {}, // 人员信息对象
      loading: false,
    };
  },
  async created() {},
  mounted() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  methods: {
    show(item) {
      this.personObj = item;
      this.infoList();
      // this.staticsList.forEach((one) => {
      //   if (this.personObj[one.filedName]) {
      //     one.count = this.personObj[one.filedName]
      //   }
      //   if (one.label === 'urlNotAvailableAmount') {
      //     one.count = this.personObj.smallUrlNotAvailableAmount + this.personObj.largeUrlNotAvailableAmount
      //   }
      // })
      this.captureVisible = true;
    },
    async infoList() {
      this.loading = true;
      let params = Object.assign(this.searchData);
      params.customParameters.idCard = this.personObj.idCard;
      params.indexId = this.resultId.indexId;
      params.batchId = this.resultId.batchId;
      params.access = this.resultId.access;
      params.displayType = this.resultId.displayType;
      params.orgRegionCode = this.resultId.orgRegionCode;
      let res = await this.$http.post(this.interFaceName, Object.assign(params));
      this.tableData = res.data.data.entities;
      this.pageData.totalCount = res.data.data.total;
      this.loading = false;
    },
    viewBigPic(index, item) {
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 分页
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
    accuracyDetail(item) {
      this.$refs.accuracyDetail.show(item);
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
    },
    // 检索，父组件调用
    startSearch(searchData) {
      this.searchData = Object.assign(this.searchData, searchData);
      this.infoList();
    },
    similarityVal(val) {
      return (val * 100).toFixed(2) + '%';
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      !val ? this.reset() : null;
      this.captureVisible = val;
    },
  },
  components: {
    UploadModal: require('./upload-modal.vue').default,
    uiImage: require('@/components/ui-image').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>

<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 0 !important;
}

@{_deep}.ivu-modal-header {
  padding: 0 !important;
}

.detail-content {
  display: flex;
  height: 800px;

  &-left {
    width: 300px;
    color: var(--color-content);
    padding: 0 20px;
    border-right: 1px solid var(--border-color);
    .capture-details-left-sculpture {
      width: 243px;
      height: 243px;
      margin: 40px auto 20px;
      border: 1px solid var(--border-color);
    }

    img {
      width: 100%;
    }

    ul {
      li {
        float: left;
        padding: 6px 10px;
        background: #1a447b;
        border-radius: 4px;
        margin-right: 10px;
        margin-top: 10px;
      }
    }

    .sculpture-item-label {
      color: var(--color-label);
    }
  }

  &-right {
    flex: 1;
  }
}
.card-wrap {
  padding-top: 30px;
  color: #ffffff;
  &-count {
    margin-top: 30px;
    padding: 20px;
    font-size: 14px;
    color: #ffffff;
    border-bottom: 1px solid var(--border-color);

    label {
      margin-right: 58px;

      span {
        color: var(--color-bluish-green-text);
      }
    }
  }
  .list {
    padding-left: 20px;
  }
  .card-list {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-content: flex-start;
    flex-wrap: wrap;
    overflow-y: auto;
  }
}
.card-item {
  height: 236px;
  margin: 10px 10px 0px 0;
  width: 188px;
  .item {
    position: relative;
    height: 100%;
    background: var(--bg-info-card);
    border: 1px solid var(--border-info-card);

    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      z-index: 1;

      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;

        > i:hover {
          color: var(--color-primary);
        }
      }

      &:hover {
        .shadow-box {
          display: block;
        }
      }

      .num {
        position: absolute;
        left: 0;
        top: 15px;
        z-index: 10;
        padding: 0px 6px;
        // border-radius: 5px;
        background: #ea800f;
        // background: rgba(42, 95, 175, 0.6);
      }
    }

    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 12px;
      color: #8797ac;
    }

    .icon-URLbukefangwen1 {
      color: var(--color-failed);
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }

    .icon-cunyi {
      color: var(--color-failed);
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }

    .icon-chaoshi {
      color: var(--color-failed);
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }
  }
}

.onlys {
  width: 80%;
}
</style>
