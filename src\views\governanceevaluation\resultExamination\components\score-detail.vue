<template>
  <div class="index-detail">
    <ui-modal v-model="visible" title="编辑考核成绩" :width="30" @query="handleOK">
      <div class="detail-content">
        <div class="table-box">
          <!-- 统计详情 -->
          <ui-table
            class="ui-table table-content"
            ref="tableContent"
            :table-columns="tableColumns"
            :table-data="tableData"
            noFill
          >
          </ui-table>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '',
      row: {},
      taskDetail: {},
      tableColumns: [],
      tableData: [],
      table_row: {}, //点击获取的数据
      examTitle: '',
      totalTableColumns: [],
      countColumnObj: {}, //合并总得分第二行所需
      exportLoading: false,
      listSearchData: {},
      currentRow: {},
      currentTaskObject: {},
    };
  },
  methods: {
    handleOK() {
      let { column } = this.currentRow;
      for (let i = 0; i < this.tableData.length; i++) {
        let row = this.tableData[i];
        // if (row.score > column.score || row.score < 0) {
        //   return this.$Message.error(`分项分值不小于0且不大于${column.score}分`)
        // }
        if (row.score > column.score) {
          return this.$Message.error(`分项分值不大于${column.score}分`);
        }
      }
      let tableData = this.tableData.filter((item) => {
        if (item.score || item.score === 0) {
          return item;
        }
      });
      this.visible = false;
      this.$emit('change', tableData);
    },
    onChangeScore(val, row, column, index) {
      this.tableData[index]['score'] = val;
    },
    init(currentRow, currentTaskObject) {
      this.currentTaskObject = currentTaskObject;
      this.currentRow = JSON.parse(JSON.stringify(currentRow));
      this.visible = true;
      this.initTableRow();
      this.initTableHeader();
    },
    initTableHeader() {
      let { row, column } = this.currentRow;
      this.tableColumns = [
        {
          title: `${row['regionName']}`,
          align: 'center',
          children: [
            {
              title: '考核分类',
              key: 'age',
              align: 'center',
              children: [
                {
                  title: '考核项',
                  align: 'center',
                  key: 'scoreDate',
                  renderHeader: this.randerSlash,
                  className: 'triangle',
                },
              ],
            },
            {
              title: `${column.parentName}`,
              align: 'center',
              children: [
                {
                  title: `${column.name}(${column.score})分`,
                  key: 'score',
                  align: 'center',
                  render: (h, { row, column, index }) => {
                    return (
                      <InputNumber
                        value={row.score}
                        vOn:input={($event) => this.onChangeScore($event, row, column, index)}
                      ></InputNumber>
                    );
                  },
                },
              ],
            },
          ],
        },
      ];
    },
    formatDate(date) {
      let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2) month = '0' + month;
      if (day.length < 2) day = '0' + day;
      return [year, month, day].join('-');
    },
    initTableRow() {
      let { row, column } = this.currentRow;
      let examModelScoreChildrenList =
        row['examModelChildrenList'][column['_index'] - 2]['examModelScoreChildrenList'] || [];
      let { year, month } = this.currentTaskObject;
      let date = new Date(year, month, 0).getDate();
      this.tableData = [];
      for (let i = 0; i < date; i++) {
        this.tableData.push({
          scoreDate: this.formatDate(`${year}-${month}-${i + 1}`),
          score: null,
        });
      }
      this.tableData.forEach((value) => {
        examModelScoreChildrenList.forEach((item) => {
          if (value.scoreDate === item.scoreDate) {
            this.$set(value, 'score', item.score);
          }
        });
      });
    },
    randerSlash(h) {
      return h(
        'div',
        {
          attrs: {
            class: 'type',
          },
        },
        [
          h(
            'strong',
            {
              attrs: {
                class: 'detection-time',
              },
            },
            '考核日期',
          ),
          h(
            'strong',
            {
              attrs: {
                class: 'detection-indicator',
              },
            },
            '考核项',
          ),
        ],
      );
    },
  },
};
</script>
<style lang="less" scoped>
.index-detail {
  // min-height: 260px;
  .ui-table {
    height: 600px;
    padding: 0 10px;
    @{_deep}.total {
      font-size: 22px;
    }
    @{_deep}.total-score {
      color: var(--color-bluish-green-text);
    }
    @{_deep}.ivu-table {
      &:before {
        width: 0;
      }
      .ivu-table-header {
        tr {
          th {
            border-right: none !important;
          }
        }
      }
    }
    @{_deep} .ivu-table {
      th,
      td {
        border: 1px solid var(--border-color) !important;
      }
      &:before {
        content: '';
        position: absolute;
        background-color: #0d477d !important;
      }
      .ivu-table-summary {
        td {
          background: #062042;
        }
      }
    }
  }
  @{_deep} .ivu-table-header {
    .triangle {
      padding: 0;
      .ivu-table-cell {
        padding: 0;
        width: 100%;
        height: 100%;
      }
      .type {
        position: relative;
        background-color: #0d477d;
        width: 100%;
        height: 100%;
        z-index: 0;
        &:after {
          content: '';
          display: block;
          width: 100%;
          height: 100%;
          clip-path: polygon(100% calc(100% - 0.5px), 100% 0px, 0px -0.5px);
          position: absolute;
          top: 0;
          background-color: #092955;
        }
        &:before {
          content: '';
          display: block;
          width: 100%;
          height: 100%;
          clip-path: polygon(0px 0.5px, 0px 100%, calc(100% - 0.5px) calc(100% + 0.5px));
          position: absolute;
          top: 0;
          background-color: #092955;
        }
        .detection-time {
          position: absolute;
          left: 6px;
          bottom: 5px;
          z-index: 1;
        }
        .detection-indicator {
          position: absolute;
          right: 6px;
          top: 5px;
          z-index: 1;
        }
      }
    }
  }
}
</style>
