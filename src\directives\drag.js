export default function (Vue) {
    Vue.directive('drag', {
        bind (el, bind, vnode) {
        //   const dom = el

        //   dom.style.userSelect = 'none'
        //   dom.onmousedown = (e) => {
        //     const [disX, disY] = [e.clientX - dom.offsetLeft, e.clientY - dom.offsetTop]
        //     document.onmousemove = (e) => {
        //         console.log('移动')
        //       const l = e.clientX - disX
        //       const t = e.clientY - disY
        //       dom.style.left = l + 'px'
        //       dom.style.top = t + 'px'
        //     }
        //     document.onmouseup = () => {
        //         console.log('暂停')
        //       document.onmousemove = null
        //       document.onmouseup = null
        //     }
        //   }
        },
        inserted (el, bind, vnode) {
            const target = el;
            el.onmousedown = (e) => {
                e.stopPropagation()
                e.preventDefault();
                const disX = e.pageX - target.offsetLeft;
                const disY = e.pageY - target.offsetTop;
                document.onmousemove = (de) => {
                    // target.style.left = de.pageX - disX + 'px';
                    // target.style.top = de.pageY - disY + 'px';
                    target.style.marginLeft = de.pageX - disX + 'px';
                    target.style.marginTop = de.pageY - disY + 'px';
                }
                document.onmouseup = (de) => {
                    document.onmousemove = document.onmouseup = null
                }
            }
        },
        update (el, bind, vnode) {
            // el.style.left = 0;
            // el.style.top = 0;
            let imgBox = el.firstChild;
            const w = parseInt(window.getComputedStyle(imgBox).width);
            const h = parseInt(window.getComputedStyle(imgBox).height);
            if(w && h){
                el.style.marginLeft = (el.parentNode.offsetWidth - w) /2;
                el.style.marginTop = (el.parentNode.offsetHeight - h) /2;
            }
        },
        unbind (el, bind, vnode) {

        },
        runs (el, bind) {

        }
    })
}
