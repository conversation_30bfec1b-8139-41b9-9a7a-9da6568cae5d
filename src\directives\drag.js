let left = null,
  top = null;

export default function (Vue) {
  Vue.directive('drag', {
    bind(el, bind) {
      let dom = el;

      dom.style.userSelect = 'none';
      el.style.cursor = 'move';

      // 是否 一直保持该定位
      if (bind.arg === 'allTime' && (left || top)) {
        dom.style.left = left + 'px';
        dom.style.top = top + 'px';
      }

      dom.onmousedown = (e) => {
        let [disX, disY] = [e.clientX - dom.offsetLeft, e.clientY - dom.offsetTop];
        document.onmousemove = (e) => {
          // 是否存在 指定位置 才允许拖拽
          let dragContainer = null;
          if (bind.value) {
            dragContainer = document.getElementById(bind.value);
          } else {
            dragContainer = document.documentElement;
          }
          let l = e.clientX - disX;
          let t = e.clientY - disY;

          if (l <= 0) {
            l = 5; // 设置成5,离边缘不要太近
          } else if (l > dragContainer.clientWidth - el.clientWidth) {
            // document.documentElement.clientWidth屏幕可视区宽度
            l = dragContainer.clientWidth - el.clientWidth - 5;
          }

          if (t <= 0) {
            t = 5;
          } else if (t > dragContainer.clientHeight - el.clientHeight) {
            t = dragContainer.clientHeight - el.clientHeight - 5;
          }
          dom.style.left = l + 'px';
          dom.style.top = t + 'px';

          if (bind.arg === 'allTime') {
            left = l;
            top = t;
          }
        };
        document.onmouseup = () => {
          document.onmousemove = null;
          document.onmouseup = null;
        };
      };
    },
    inserted() {},
    update() {},
    unbind() {},
    runs() {},
  });
}
