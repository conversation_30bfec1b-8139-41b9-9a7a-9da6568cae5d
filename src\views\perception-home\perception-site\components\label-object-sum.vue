<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt=''/><span class="title-text">标签对象总量TOP10</span>
    </div>
    <div class="echart-wrap">
      <div :style="{width: 60+'px'}" class="label-object-total-ul">
        <div
          v-for="(item, $index) in statisticsList"
          :key="$index"
          :title="item.name"
          :style="{fontSize: 12+'px',height:parseFloat(statisticsList.length / 1).toFixed(2) * 100 + '%'}"
          class="label-object-total-item ellipsis"
          @click="labelHandle(item)">
          {{ item.name }}
        </div>
      </div>
      <bar-echart v-if="labelObjectSumObj.names.length" :names="labelObjectSumObj.names" :values="labelObjectSumObj.values" class="bar-echart"/>
    </div>
  </ui-card>
</template>
<script>
  import BarEchart from './echarts/bar-echart.vue'
  export default {
    components: {
      BarEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        labelObjectSumObj: {
          names: [],
          values: []
        }
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.labelObjectSumObj = this.updataLabelObject(this.statisticsList)
        },
        immediate: true
      }
    },
    methods: {
      /**
       * 标签对象总量TOP10
      */
      updataLabelObject (list) {
        return {
          names: list.map(item => {
            return item.name
          }),
          values: list.map(item => {
            return item.objectCount
          })
        }
      },
      labelHandle (item) {
        this.$router.push({
          path: '/label-management/label-info',
          query: {
            id: item.id,
            curName: item.name
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    display: flex;
    flex: 1;
    .label-object-total-ul {
      color: rgba(255, 255, 255, 0.89);
      display: flex;
      flex-direction: column;
      .label-object-total-item {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        cursor: pointer;
      }
    }
    .bar-echart {
      display: flex;
      flex: 1;
    }
  }
</style>