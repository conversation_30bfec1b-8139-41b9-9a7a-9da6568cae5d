<template>
  <div>
    <div v-if="list.length !== 0">
      <div class="my-swiper-container" id="mySwiper">
        <swiper
          ref="mySwiper"
          v-if="!swiperResize"
          :options="swiperOption"
          class="my-swiper"
        >
          <template v-for="(item, index) in list">
            <swiper-slide
              :key="index"
              @click.native.stop="faceDetailFn(item, index)"
            >
              <div class="swiper-item">
                <p class="img-content" v-if="type == 'vehicle'">
                  <img v-lazy="item.sceneImg" alt="" />
                </p>
                <p class="img-content" v-else>
                  <img v-lazy="item.traitImg" alt="" />
                </p>
                <div class="bottom-info">
                  <p :title="item.deviceName" class="info">
                    {{ item.deviceName }}
                  </p>
                  <time>{{ item.absTime }}</time>
                </div>
              </div>
            </swiper-slide>
          </template>
        </swiper>
        <div class="swiper-button-prev snap-prev-record" slot="button-prev">
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div class="swiper-button-next snap-next-record" slot="button-next">
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="list.length === 0"></ui-empty>
    <!-- 人 -->
    <details-face-modal
      v-if="videoShow"
      ref="videoDetail"
      @close="videoShow = false"
    ></details-face-modal>
    <!-- 车 -->
    <details-vehicle-modal
      v-if="vehicleShow"
      ref="vehicleDetail"
      @close="vehicleShow = false"
    >
    </details-vehicle-modal>
  </div>
</template>
<script>
import vehicleDetail from "@/components/detail/vehicle";
import faceDetail from "@/components/detail/face";
import videoDetail from "@/components/detail/video";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import { swiper, swiperSlide } from "vue-awesome-swiper";
export default {
  components: {
    swiper,
    swiperSlide,
    faceDetail,
    vehicleDetail,
    videoDetail,
    detailsFaceModal,
    detailsVehicleModal,
  },
  props: {
    // 加载
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
    // 显示类型
    type: {
      type: String,
      default: "people",
    },
  },
  data() {
    return {
      vehicleShow: false,
      videoShow: false,
      swiperResize: false,
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 2.85,
        centeredSlides: true,
        initialSlide: 2,
        // loop: false,
        // loopAdditionalSlides: 5,
        speed: 1000,
        // autoplay: {
        //   delay: 100000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false
        // },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 50, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 90, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".snap-next-record",
          prevEl: ".snap-prev-record",
        },
        observer: true,
        observeParents: true,
      },
    };
  },
  activated() {
    // 点击遮罩层，不点击window
    let dom = document.querySelectorAll(".dom-wrapper");
    dom.forEach((item) => {
      item.addEventListener("click", (e) => {
        this.videoShow = false;
        this.vehicleShow = false;
      });
    });
  },
  mounted() {
    let _this = this;
    // this.$erd.listenTo(document.getElementById("mySwiper"), (element) => {
    //   _this.updateSliderHandle();
    // });
  },
  methods: {
    updateSliderHandle() {
      const w = document.documentElement.clientWidth;
      this.swiperOption.coverflowEffect.stretch = (w / 192) * 5.85;
      this.swiperOption.coverflowEffect.depth = (w / 192) * 10;
      this.swiperResize = true;
      this.$nextTick(() => {
        this.swiperResize = false;
      });
    },
    faceDetailFn(row, index) {
      if (this.type === "video" || this.type === "people") {
        this.videoShow = true;
        this.$nextTick(() => {
          this.$refs.videoDetail.init(row, this.list, index, 5, 1);
        });
      } else {
        this.vehicleShow = true;
        this.$nextTick(() => {
          this.$refs.vehicleDetail.init(row, this.list, index, 1);
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.vehicleDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 666;
}
.faceDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 666;
}
.my-swiper-container {
  padding: 0 30px;
  position: relative;
  .my-swiper {
    margin: auto;
    padding: 15px 0;
    .swiper-item {
      width: 100%;
      height: 190px;
      background: #f9f9f9;
      box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      box-sizing: border-box;
      padding: 10px;
      overflow: hidden;
      .img-content {
        width: 100%;
        height: 120px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .bottom-info {
        padding-top: 10px;
        color: #000;
        font-size: 12px;
        .info,
        time {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  /deep/ .swiper-container-3d {
    .swiper-slide-shadow-left {
      background-image: linear-gradient(
        to left,
        rgba(255, 255, 255, 1),
        rgba(255, 255, 255, 0)
      );
    }
    .swiper-slide-shadow-right {
      background-image: linear-gradient(
        to right,
        rgba(255, 255, 255, 1),
        rgba(255, 255, 255, 0)
      );
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    margin-top: -15px;
    .iconfont {
      color: #fff;
      font-size: 18px;
    }
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    &:active {
      background: rgba(0, 0, 0, 1);
    }
  }
  .swiper-button-prev {
    transform: rotate(180deg);
    left: 12px;
  }
  .swiper-button-next {
    right: 12px;
  }
}
</style>
