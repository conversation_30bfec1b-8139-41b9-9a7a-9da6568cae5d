<!--
 * @Author: zhengmingming <EMAIL>
 * @Date: 2025-04-28 18:03:05
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-06 11:46:32
 * @FilePath: \icbd-view\src\views\place-control\components\community-map\components\attentionList.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <DropdownMenu slot="list">
    <DropdownItem>
      <Checkbox
        @on-change="selectAll"
        :value="selectAttention.length == attentionList.length"
        >关注度
      </Checkbox>
    </DropdownItem>
    <div class="line"></div>
    <CheckboxGroup v-model="selectAttention" @on-change="changeSelectAttention">
      <DropdownItem v-for="item in attentionList" :key="item.key">
        <div class="item-list">
          <Checkbox :label="item.key">
            <span style="margin-left: 10px">{{ item.title }}</span>
          </Checkbox>
        </div>
      </DropdownItem>
    </CheckboxGroup>
    <div class="line"></div>
  </DropdownMenu>
</template>

<script>
export default {
  name: "attentionList",
  props: {
    title: {
      type: String,
      default: "全部图层",
    },
  },
  components: {},
  data() {
    return {
      visible: false,
      attentionList: [
        { key: 0, title: "高" },
        { key: 1, title: "中" },
        { key: 2, title: "低" },
      ],
      selectAttention: [],
    };
  },
  mounted() {
    this.initAllSelectAttention();
  },
  methods: {
    changeSelectAttention() {
      this.$emit("changeLayerName", this.selectAttention);
    },
    selectAll(value) {
      let arr = this.attentionList.map((item) => item.key);
      if (!value) {
        this.selectAttention = [];
      } else {
        this.selectAttention.push(...arr);
      }
      this.$emit("changeLayerName", this.selectAttention);
    },
    initAllSelectAttention() {
      this.selectAttention = this.attentionList.map((item) => item.key);
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  width: 180px;
  height: 1px;
  background: #d3d7de;
  padding: 0 10px;
}
.dropdown-box {
  .dropdown-btn {
    width: 180px;
    display: flex;
    justify-content: space-between;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.75);
    line-height: 20px;
    margin-right: 5px;
  }

  /deep/ .ivu-dropdown-item {
    padding: 0 0;
  }
  .item-list {
    width: 140px;
  }
}
</style>
