<template>
  <div class="face-content-wrapper">
    <div class="btn-list">
      <Button @click="handleSort" size="small">
        <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        时间排序
      </Button>
      <Button
        v-show="updateLayerId"
        size="small"
        @click="handleExport"
        :disabled="!updateLayerId"
        v-if="dataList.length"
      >
        <ui-icon
          type="daoru"
          :color="updateLayerId ? '#2C86F8' : 'rgba(0, 0, 0, 0.35)'"
        ></ui-icon>
        导出
      </Button>
    </div>
    <div class="face-content warpper-box">
      <div
        v-for="(e, i) in dataList"
        :key="i"
        class="face-item box-item"
        :class="{ active: currentClickIndex == i }"
        @click="chooseMapItem($event, i)"
      >
        <div class="header">
          <div class="header-left">
            <span
              class="serialNumber"
              :class="{ activeNumber: currentClickIndex == i }"
            >
              <span>{{ i + 1 }}</span>
            </span>
            <!-- <span class="header-deviceid">{{ e.vid }}</span> -->
            <ui-plate-number
              v-if="e.type == 'vehicle'"
              :plateNo="e.plateNo"
              :color="e.plateColor"
              :style="{ height: '35px' }"
            ></ui-plate-number>
          </div>
          <div v-if="e.type !== 'vehicle'" class="header-name" v-show-tips>
            {{ e[formatType[e.type].idKey] || "--" }}
          </div>
          <ui-icon
            v-if="canWrite"
            class="ml-5 mr-5"
            type="shanchu"
            @click.native.stop="deleteItem(e, i)"
          ></ui-icon>
        </div>
        <div class="content">
          <div class="content-left">
            <img
              v-if="!formatType[e.type].iconName"
              v-lazy="e.traitImg"
              alt=""
            />
            <i v-else class="iconfont" :class="formatType[e.type].iconName"></i>
          </div>
          <div class="content-right">
            <span class="ellipsis">
              <ui-icon type="location1" :size="14"></ui-icon>
              <span>{{ formatType[e.type].label || "--" }}</span>
            </span>
            <span class="ellipsis">
              <ui-icon type="time" :size="14"></ui-icon>
              <span>{{ e.absTime || "--" }}</span>
            </span>
            <span class="ellipsis">
              <ui-icon type="location" :size="14"></ui-icon>
              <span>{{ e.detailAddress || "--" }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="!dataList.length" />
  </div>
</template>

<script>
import { exportExcel } from "@/api/operationsOnTheMap";
export default {
  props: {
    // 当前点击顺序
    currentClickIndex: {
      type: Number,
      default: -1,
    },
    orderType: {
      type: String,
      default: "",
    },
    updateLayerId: {
      type: [String, Number],
      default: "",
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    // 写权限，被分享的只能读
    canWrite: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      timeUpDown: false,
      formatType: {
        face: { label: "人像", idKey: "id" },
        vehicle: { label: "车辆", idKey: "id" },
        humanbody: { label: "人体", idKey: "recordId" },
        nonmotorVehicle: { label: "非机动车", idKey: "recordId" },
        wifi: { label: "Wi-Fi", idKey: "mac", iconName: "icon-wifi" },
        rfid: { label: "RFID", idKey: "rfidCode", iconName: "icon-RFID" },
        electric: { label: "电围", idKey: "imsi", iconName: "icon-ZM-dianwei" },
        gps: { label: "GPS", idKey: "gps", iconName: "icon-gps" },
        etc: { label: "ETC", idKey: "etc", iconName: "icon-a-ETC1x" },
      },
    };
  },
  watch: {
    // currentClickIndex: {
    //     handler (newVal){
    //         if(newVal > -1) {
    //             let list =  document.querySelectorAll('.face-item');
    //             list[newVal].scrollIntoView(false)
    //         }
    //     },
    // },
    orderType: {
      handler(newVal) {
        this.timeUpDown = newVal == "desc" ? false : true;
      },
    },
  },
  methods: {
    chooseMapItem($event, index) {
      $event.stopPropagation();
      this.$emit("chooseMapItem", index);
    },
    // 排序
    handleSort(val) {
      this.timeUpDown = !this.timeUpDown;
      this.$emit("changeOrder", this.timeUpDown ? "asc" : "desc");
    },
    handleExport() {
      exportExcel({
        id: this.updateLayerId,
        excelType: "",
      }).then((res) => {
        if (res.data) {
          let aLink = document.createElement("a");
          aLink.href = res.data;
          aLink.click();
        }
      });
    },
    deleteItem(item, index) {
      this.$emit("deleteItem", item, index);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
.face-content {
  .score {
    color: #2c86f8 !important;
    font-weight: bold;
  }
}
</style>
