<!--
 * @Date: 2025-01-15 14:05:53
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-06 10:55:21
 * @FilePath: \icbd-view\src\views\juvenile\components\collect\school-out-alarm.vue
-->
<template>
  <div class="school-card-box">
    <div class="image-box">
      <ui-image type="people" :src="data.traitImg" alt="" />
    </div>
    <div class="list-box">
      <div class="item" v-for="item in itemList" :key="item.param">
        <div class="info-name">
          <Tooltip
            :content="item.title"
            placement="right"
            transfer
            theme="light"
          >
            <i class="iconfont" :class="item.icon"></i>
          </Tooltip>
        </div>
        <div class="info-content info-color-sub" :style="item.style">
          {{ data[item.param] || "--" }}
        </div>
      </div>
      <div class="label-box">
        <div
          class="label-item"
          v-for="(ite, index) in data?.bizLabels"
          :key="index"
        >
          {{ ite }}
        </div>
      </div>
    </div>
    <div class="out-num-box">
      出入小区数:
      <span>{{ data.enterNightNumber }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: "NightOutCommunityAlarm",
  props: {
    // 数据
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      imageTest: require("@/assets/img/face.png") || "",
      itemList: [
        {
          icon: "icon-xingming",
          param: "name",
          title: "姓名",
          style: { color: "#2c86f8" },
        },
        {
          icon: "icon-shenfenzheng",
          param: "idCardNo",
          title: "身份证",
          style: {},
        },
        {
          icon: "icon-time",
          param: "lastCaptureTime",
          title: "最后抓拍时间",
          style: {},
        },
        {
          icon: "icon-location",
          param: "lastCaptureAddress",
          title: "最后抓拍地点",
          style: {},
        },
        {
          icon: "icon-060delay",
          param: "alarmTime",
          title: "报警时间",
          style: {},
        },
      ],
      labelList: ["烧杀抢掠", "吸毒贩毒"],
    };
  },
};
</script>

<style lang="less" scoped>
.school-card-box {
  padding: 10px;
  width: 320px;
  // height: 140px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  display: flex;
  gap: 10px;
  position: relative;
  border-radius: 4px;
  .image-box {
    width: 139px;
    height: 139px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .list-box {
    margin-top: 12px;
    .item {
      display: flex;
      gap: 8px;
      font-size: 14px;

      .info-content {
        max-width: 130px;
        text-overflow: ellipsis;
        text-wrap: nowrap;
        overflow: hidden;
      }
    }
    .label-box {
      display: flex;
      gap: 4px;
      margin-top: 6px;
      width: 167px;
      overflow: auto;
      .label-item {
        padding: 2px 4px;
        background: rgba(234, 74, 54, 0.1);
        border-radius: 3px 3px 3px 3px;
        font-size: 12px;
        color: #ea4a36;
        white-space: nowrap;
      }
    }
  }
  .out-num-box {
    position: absolute;
    right: 0;
    top: 0;
    background: #2c86f8;
    border-radius: 0 4px 0 4px;
    font-size: 12px;
    color: #ffffff;
    padding: 2px 6px;
  }
}
</style>
