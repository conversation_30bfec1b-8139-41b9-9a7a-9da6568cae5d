<!--
    * @FileDescription: 报警管理
    * @Author: H
    * @Date: 2023/4/17
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="alarm-box" @click="detailFn()">
        <div class="top">
            <div class="level-title">
                <img v-if="data.bgIndex == 1" class="level" src="@/assets/img/target/title1.png" alt />
                <img v-if="data.bgIndex == 2" class="level" src="@/assets/img/target/title2.png" alt />
                <img v-if="data.bgIndex == 3" class="level" src="@/assets/img/target/title3.png" alt />
                <img v-if="data.bgIndex == 4" class="level" src="@/assets/img/target/title4.png" alt />
                <img v-if="data.bgIndex == 5" class="level" src="@/assets/img/target/title5.png" alt />
                <div class="num">
                    {{ 
                        data.taskLevel == 1 ? '一级' :
                        data.taskLevel == 2 ? '二级' : '三级'
                    }}
                </div>
            </div>
            <div class="collection-btn">
                <ui-btn-tip v-if="data.myFavorite == 1" class="collection-icon" content="取消收藏" icon="icon-yishoucang" transfer @click.stop.native="collection(2)" />
                <ui-btn-tip v-else class="collection-icon"  content="收藏" icon="icon-shoucang" transfer @click.stop.native="collection(1)" />
            </div>
        </div>
        <div class="contrast">
            <div class="block border">
                <ui-image :src="data.traitImg"></ui-image>
            </div>
            <div class="block"> 
                <ui-image v-if="data.bgIndex == 1" class="animation" :src="c1"></ui-image>
                <ui-image v-else-if="data.bgIndex == 2" class="animation" :src="c2"></ui-image>
                <ui-image v-else-if="data.bgIndex == 3" class="animation" :src="c3"></ui-image>
                <ui-image v-else-if="data.bgIndex == 4" class="animation" :src="c4"></ui-image>
                <ui-image v-else class="animation" :src="c5"></ui-image>
                <div class="num" 
                :class="{
                    'c1': data.bgIndex == 1,
                    'c2': data.bgIndex == 2,
                    'c3': data.bgIndex == 3,
                    'c4': data.bgIndex == 4,
                    'c5': data.bgIndex == 5,
                }"
                >{{data.simScore? (data.simScore.toFixed(4) * 100).toString().substring(0,5):0}}%</div>
            </div>
            <div class="block border">
                <ui-image :src="data.photoUrl"></ui-image>
            </div>
        </div>
        <div class="info">
            <div class="left">
                <!-- <div class="p" v-for="item in 6">
                <div class="title">布控目标：</div>
                <div class="val">布控人员</div>
                </div> -->
                <div class="p">
                    <div class="title">布控目标:</div>
                    <div class="val">
                        {{ data.name || '未知' }}
                        {{ data.taskType == '1' ? '' : '（'+data.libName+'）' }}
                    </div>
                </div>
                <div class="p">
                    <div class="title">布控来源:</div>
                    <div class="val">{{ data.taskType == '1' ? '单体布控' : '库布控' }}</div>
                </div>
                <div class="p">
                    <div class="title">报警时间:</div>
                    <div class="val">{{ data.alarmTime }}</div>
                </div>
                <div class="p">
                    <div class="title">报警设备:</div>
                    <div class="val">{{ data.deviceName }}</div>
                </div>
                <div class="p">
                    <div class="title">所属任务:</div>
                    <div class="val">{{ data.taskName }}</div>
                </div>
            </div>
            <div class="right">
                <ui-image v-if="data.operationType == 1" class="img" :src="valid"></ui-image>
                <ui-image v-if="data.operationType == 2" class="img" :src="invalid"></ui-image>
                <ui-image v-if="data.operationType == 0" class="img" :src="unproces"></ui-image>
            </div>
        </div>
        <alarmDetail 
            v-if="detailShow"
            :tableList="[data]"
            :showNext="false"
            :tableIndex="0"
            ref="alarmDetail" @close.stop="close"/>
    </div>
</template>

<script>
import round from '@/assets/img/target/round.png'
import valid from '@/assets/img/target/valid.png'
import c1 from '@/assets/img/target/c-one.png'
import c2 from '@/assets/img/target/c-two.png'
import c3 from '@/assets/img/target/c-three.png'
import c4 from '@/assets/img/target/c-four.png'
import c5 from '@/assets/img/target/c-five.png'
import invalid from '@/assets/img/target/invalid.png'
import unproces from '@/assets/img/target/unproces.png'
import alarmDetail from '@/views/target-control/alarm-manager/people/components/alarm-detail.vue'
export default {
    name: '',
    components:{
        alarmDetail  
    },
    props:{
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
    },
    data () {
        return {
            c1, c2, c3, round, valid, invalid, unproces, c4, c5,
            historyList: [],
            detailShow: false,
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        collection(flag){
            var param = {
                favoriteObjectId: this.data.alarmTopId,
                favoriteObjectType: 14,
            }
            this.$emit('collection', param, flag)
        },
        detailFn(){
            this.detailShow = true;
        },
        close() {
            this.detailShow = false
        },
    }
}
</script>

<style lang='less' scoped>
@import 'style/index';
</style>
