<template>
  <base-review ref="baseReviewRef" v-bind="getAttrs" v-on="$listeners" @changeDevice="changeDevice">
    <template #detailcontent="{ detailInfo }" v-if="$attrs.value">
      <div class="detail-content">
        <ui-image :src="detailInfo.screenShot" class="image" v-if="isScreenShot || type === 'screenShot'" />
        <EasyPlayer v-if="!isScreenShot && type === 'video'" class="easy-player" :videoUrl="videoUrl" ref="easyPlay">
        </EasyPlayer>
        <span
          v-if="!isScreenShot"
          class="detail-content_button pointer"
          :title="type === 'screenShot' ? '播放视频' : '查看截图'"
          @click="changeMode"
        >
          {{ type === 'screenShot' ? '播放视频' : '查看截图' }}
        </span>
      </div>
    </template>
  </base-review>
</template>

<script>
import vedio from '@/config/api/vedio-threm';
export default {
  name: 'video-quality-pass',
  props: {
    // true 仅截图模式 / false截图和视频切换
    isScreenShot: {
      default: false,
    },
    // 获取视频的接口url
    getVideoUrl: {
      default: vedio.getplay,
    },
    // 获取视频的接口的参数函数回调处理
    getVideoParamsFunc: {
      type: Function,
      default: (row) => {
        return { deviceId: row.deviceId };
      },
    },
  },
  data() {
    return {
      videoUrl: '',
      playDeviceCode: '',
      type: 'screenShot',
    };
  },
  computed: {
    getAttrs() {
      return {
        ...this.$attrs,
      };
    },
  },
  watch: {
    '$attrs.value': {
      handler() {
        this.type = 'screenShot';
      },
      immediate: true,
    },
  },
  methods: {
    changeMode() {
      if (this.type === 'screenShot') {
        this.type = 'video';
        this.handleStart();
      } else {
        this.type = 'screenShot';
      }
    },
    // 上一条设备下一条设备
    changeDevice() {
      this.type = 'screenShot';
    },
    // 视频播放
    handleStart() {
      // 先清除上一个视频流
      this.playDeviceCode ? this.$http.post(vedio.stop + this.playDeviceCode) : null;
      this.playDeviceCode = '';
      this.$nextTick(() => {
        this.playVideo(this.$refs.baseReviewRef.detailInfo);
      });
    },
    //视频播放
    async playVideo(row) {
      try {
        this.videoUrl = '';
        this.playDeviceCode = row.deviceId;
        let params = this.getVideoParamsFunc(row);
        // let data = {
        //   deviceId: row.deviceId,
        //   // startTime: row.playStartTime,
        //   // endTime: row.playEndTime,
        //   // pullLevel: 3,
        //   // urlEncryption: true
        // }
        let res = await this.$http.post(this.getVideoUrl, params);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      } finally {
        // this.videoLoading = false
      }
    },
  },
  components: {
    BaseReview: require('./base-review').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>

<style lang="less" scoped>
.detail-content {
  display: flex;
  width: 100%;
  padding: 20px;
  background: #041129;
  border-radius: 4px;
  position: relative;
  .image {
    @{_deep}.ui-image-div {
      min-height: 200px;
    }
  }

  .detail-content_button {
    position: absolute;
    top: 30px;
    left: 30px;
    color: var(--color-primary);
    z-index: 10;
  }
  .easy-player {
    width: 100%;
    margin-top: 0px;
  }
}
</style>
