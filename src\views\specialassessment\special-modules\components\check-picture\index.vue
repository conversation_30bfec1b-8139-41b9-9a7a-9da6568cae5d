<template>
  <ui-modal v-model="visible" title="查看图片" :styles="styles" class="check-picture" footer-hide>
    <component v-if="visible" :is="componentName" v-bind="getAttrs" v-on="$listeners"></component>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.49rem',
      },
      componentName: '',
      checkPictureComponentConfig: [
        // 人脸
        { indexType: 'FACE_CLOCK', componentName: 'FaceClock' }, // 人脸卡口设备时钟准确率
        { indexType: 'FACE_UPLOAD', componentName: 'FaceDelay' }, // 人脸卡口设备及时上传率
        { indexType: 'FACE_FOCUS_UPLOAD', componentName: 'FaceDelay' }, // 重点人脸卡口设备及时上传率
        { indexType: 'FACE_URL_AVAILABLE', componentName: 'FaceUrlAvailable' }, // 人脸卡口设备图片地址可用率
        { indexType: 'FACE_FOCUS_URL_AVAILABLE', componentName: 'FaceUrlAvailable' }, // 重点人脸卡口设备图片地址可用率
        { indexType: 'FACE_CAPTURE_PASS', componentName: 'FacePass' }, // 人脸设备抓拍图片合格率
        { indexType: 'FACE_CAPTURE_SCORE', componentName: 'FacePass' }, // 人脸设备抓拍图片合格率
        // 车辆
        { indexType: 'VEHICLE_CLOCK', componentName: 'CarClock' }, // 车辆卡口设备时钟准确率
        { indexType: 'VEHICLE_UPLOAD', componentName: 'CarDelay' }, // 车辆卡口设备及时上传率
        { indexType: 'VEHICLE_UPLOAD_IMPORTANT', componentName: 'CarDelay' }, // 车辆卡口设备及时上传率
        { indexType: 'VEHICLE_URL_AVAILABLE', componentName: 'CarUrlAvailable' }, // 车辆卡口设备过车图片地址可用率
        { indexType: 'VEHICLE_URL_AVAILABLE_IMPORTANT', componentName: 'CarUrlAvailable' }, // 重点车辆卡口设备过车图片地址可用率
        { indexType: 'VEHICLE_FULL_INFO', componentName: 'CarComplete' }, // 车辆卡口设备抓拍数据完整率
        { indexType: 'VEHICLE_FULL_INFO_IMPORTANT', componentName: 'CarComplete' }, // 重点车辆卡口设备抓拍数据完整率
        { indexType: 'VEHICLE_INFO_PASS', componentName: 'CarAccuracy' }, // 车辆卡口设备过车数据准确率
        { indexType: 'VEHICLE_INFO_PASS_IMPORTANT', componentName: 'CarAccuracy' }, // 重点车辆卡口设备过车数据准确率
        // 人体
        { indexType: 'BODY_CLOCK', componentName: 'BodyClock' }, // 人体卡口设备时钟准确率
        { indexType: 'BODY_UPLOAD', componentName: 'BodyDelay' }, // 人体卡口设备及时上传率
      ],
    };
  },
  computed: {
    getAttrs() {
      return {
        ...this.$attrs,
        activeIndexItem: this.activeIndexItem,
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (!val) return false;
      const checkPictureObj = this.checkPictureComponentConfig.find(
        (item) => item.indexType === this.activeIndexItem.jobType,
      );
      this.componentName = checkPictureObj.componentName;
    },
  },
  components: {
    FaceClock: require('./components/face-clock.vue').default,
    FaceDelay: require('./components/face-delay.vue').default,
    FaceUrlAvailable: require('./components/face-url-available.vue').default,
    FacePass: require('./components/face-pass.vue').default,
    CarClock: require('./components/car-clock.vue').default,
    CarDelay: require('./components/car-delay.vue').default,
    CarUrlAvailable: require('./components/car-url-available.vue').default,
    CarComplete: require('./components/car-complete.vue').default,
    CarAccuracy: require('./components/car-accuracy.vue').default,
    BodyClock: require('./components/body-clock.vue').default,
    BodyDelay: require('./components/body-delay.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
