<template>
  <div>
    <div class="alarmlog auto-fill">
      <div class="title-div over-flow">
        <div class="title-item total-alarm-bg over-flow fl">
          <div class="title-icon-div fl">
            <i class="icon-font icon-gaojingdeng" title="总告警"></i>
          </div>
          <div class="fl mr-sm message">
            <div>
              <p>运维总计告警数</p>
              <p class="f-16 title-item-count">
                {{ alarmStatistics.totalAlarmCount }}
              </p>
            </div>
            <div class="mt-md">
              <p>运维总计现离线数</p>
              <p class="f-16">{{ alarmStatistics.totalOfflineCount }}</p>
            </div>
          </div>
        </div>
        <div class="title-item total-device-bg over-flow fl">
          <div class="title-icon-div fl">
            <i class="icon-font icon-wufajianceshebeishuliang" title="前端设备总告警"></i>
          </div>
          <div class="fl mr-sm message">
            <div @click="searchType('device')" class="pointer">
              <p class="statistic-title">前端设备总告警数</p>
              <p class="f-16">{{ alarmStatistics.deviceAlarmCount }}</p>
            </div>
            <div class="mt-md pointer" @click="viewOffline('equipmentmonitoring')">
              <p class="statistic-title">现离线数</p>
              <p class="f-16">{{ alarmStatistics.deviceOfflineCount }}</p>
            </div>
          </div>
        </div>
        <div class="title-item total-server-bg over-flow fl">
          <div class="title-icon-div fl">
            <i class="icon-font icon-fuwuqizongliang" title="服务器总告警"></i>
          </div>
          <div class="fl mr-sm message">
            <div @click="searchType('server')" class="pointer">
              <p class="statistic-title">服务器总告警数</p>
              <p class="f-16">{{ alarmStatistics.serverAlarmCount }}</p>
            </div>
            <div class="mt-md pointer" @click="viewOffline('servermonitoring')">
              <p class="statistic-title">现离线数</p>
              <p class="f-16">{{ alarmStatistics.serverOfflineCount }}</p>
            </div>
          </div>
        </div>
        <div class="title-item total-service-bg over-flow fl">
          <div class="title-icon-div fl">
            <i class="icon-font icon-fuwujiance" title="服务总告警"></i>
          </div>
          <div class="fl mr-sm message">
            <div @click="searchType('service')" class="pointer">
              <p class="statistic-title">服务总告警数</p>
              <p class="f-16">{{ alarmStatistics.serviceAlarmCount }}</p>
            </div>
            <div class="mt-md pointer" @click="viewOffline('servicemonitoring')">
              <p class="statistic-title">现离线数</p>
              <p class="f-16">{{ alarmStatistics.serviceOfflineCount }}</p>
            </div>
          </div>
        </div>
      </div>
      <div class="search-module">
        <ui-label class="inline" label="关键词">
          <Input
            v-model="searchData.keyword"
            class="input-width"
            placeholder="请输入报警设备、服务、系统名称、设备IP"
          ></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="告警时间">
          <DatePicker
            class="width-md"
            v-model="searchData.startTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择开始时间"
            :options="startTimeOption"
            confirm
            @on-ok="timeOk"
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="width-md"
            v-model="searchData.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择结束时间"
            :options="endTimeOption"
            confirm
            @on-ok="timeOk"
          ></DatePicker>
        </ui-label>
        <ui-label label="告警类型" class="inline ml-lg">
          <Select
            class="width-md"
            v-model="searchData.alarmType"
            placeholder="请选择告警类型"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in typeList" :key="index" :value="item.type">{{ item.typeName }} </Option>
          </Select>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </div>
        <Button type="primary" class="ml-sm fr" @click="download">
          <i class="icon-font icon-daochu f-12"></i>
          <span class="vt-middle ml-sm">导出</span>
        </Button>
      </div>
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </div>
</template>
<style lang="less" scoped>
.alarmlog {
  //float: left;
  //width: calc(~"100% - 373px");
  overflow: hidden;
  position: relative;
  padding: 20px 20px 0;
  height: 100%;
  background: var(--bg-content);
  .title-div {
    //padding: 10px 10px 0 10px;
    .title-item {
      width: 300px;
      height: 120px;
      margin-right: 10px;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-radius: 10px;
      color: #fff;
      .title-icon-div {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        text-align: center;
        line-height: 90px;
        i {
          font-size: 43px;
          color: #fff;
        }
      }
      .message {
        width: 120px;
        text-align: left;
      }
      .statistic-title {
        border-radius: 4px;
        //&:hover {
        //  box-shadow: 0 0 1px 1px rgba(204, 204, 204, 0.5);
        //}
      }
      &-count {
        font-weight: bold;
        color: #f5f5f5;
        opacity: 0.9;
      }
    }
    .total-alarm-bg {
      background: var(--bg-card-gradient-light-blue);
      .title-icon-div {
        background: var(---bg-card-icon);
        //background: #BED6F3;
        //opacity: 0.2;
      }
      i {
        font-size: 43px;
        color: #e7eef2;
      }
    }
    .total-device-bg {
      background: var(--bg-card-gradient-light-green);
      .title-icon-div {
        background: var(---bg-card-icon);
      }
      i {
        font-size: 43px;
        color: #e7eef2;
      }
      .statistic-title {
        //background: rgba(152, 165, 241, 0.26);
      }
    }
    .total-server-bg {
      background: var(--bg-card-gradient-blue-purple);
      .title-icon-div {
        background: var(---bg-card-icon);
      }
      i {
        font-size: 43px;
        color: #e7eef2;
      }
      .statistic-title {
        //background: rgba(32, 184, 252, 0.26);
      }
    }
    .total-service-bg {
      background: var(--bg-card-gradient-dark-blue);
      .title-icon-div {
        background: var(---bg-card-icon);
      }
      i {
        font-size: 43px;
        color: #e7eef2;
      }
      .statistic-title {
        //background: rgba(150, 225, 239, 0.26);
      }
    }
  }
  .ui-table {
    //padding: 10px;
  }
}
//@{_deep}.ivu-date-picker {
//  display: block;
//}
.right-div {
  float: right;
  width: 363px;
  height: 100%;
  padding: 10px;
  background: var(--bg-content);
  .key-word {
    width: 280px;
  }
  .button-div {
    margin-top: 30px;
  }
}
.search-module {
  margin: 10px 0;
}
.input-width {
  width: 280px;
}
</style>
<script>
import maintain from '@/config/api/maintain';
export default {
  name: 'alarmlog',
  data() {
    return {
      minusTable: 285,
      loading: false,
      alarmStatistics: {},
      copySerach: {},
      searchData: {
        keyword: '',
        alarmType: '',
        startTime: '',
        endTime: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      typeList: [],
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      tableColumns: [
        { title: '序号', type: 'index', align: 'center', width: 50 },
        { title: '告警类型', key: 'alarmTypeName', tooltip: true },
        { title: '告警设备/服务/系统名称', key: 'alarmName', tooltip: true },
        { title: '设备IP', key: 'ip', tooltip: true },
        { title: '告警时间', key: 'alarmTime', tooltip: true },
      ],
      tableData: [],
    };
  },
  activated() {
    this.copySerach = this.$util.common.deepCopy(this.searchData);
    this.init();
  },
  mounted() {},
  methods: {
    clear() {
      this.searchData = this.$util.common.deepCopy(this.copySerach);
      this.search();
    },
    init() {
      this.initHeader();
      this.initList();
      this.initTypeList();
    },
    async initTypeList() {
      try {
        let res = await this.$http.get(maintain.alarmTypeList);
        this.typeList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async initHeader() {
      try {
        let res = await this.$http.get(maintain.alarmStatistics);
        this.alarmStatistics = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async initList() {
      try {
        this.loading = true;
        this.searchData.startTime = this.$util.common.formatDate(this.searchData.startTime);
        this.searchData.endTime = this.$util.common.formatDate(this.searchData.endTime);
        const params = {
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          notSearchTotal: true,
          ...this.searchData,
        };
        let res = await this.$http.post(maintain.alarmList, params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    searchType(type) {
      this.searchData.alarmType = type;
      this.search();
    },
    timeOk() {
      this.searchData.startTime = this.$util.common.formatDate(this.searchData.startTime);
      this.searchData.endTime = this.$util.common.formatDate(this.searchData.endTime);
    },
    search() {
      this.searchData.pageNum = 1;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.search();
    },
    download() {
      this.$http
        .get(maintain.alarmDownload, { responseType: 'blob' })
        .then((res) => {
          let blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel;charset=UTF-8',
          });
          let url = window.URL.createObjectURL(blob);
          let aLink = document.createElement('a');
          //获取heads中的filename文件名
          let temp = res.headers['content-disposition'].split(';')[1].split('filename=')[1];
          let fileName = decodeURIComponent(temp);
          aLink.setAttribute('download', fileName);
          aLink.style.display = 'none';
          aLink.href = url;
          document.body.appendChild(aLink);
          aLink.click();
          document.body.removeChild(aLink);
          window.URL.revokeObjectURL(url);
        })
        .catch((error) => {
          console.log(error);
        });
    },
    viewOffline(type) {
      this.$router.push({
        name: type,
        query: {
          status: 0,
        },
      });
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
