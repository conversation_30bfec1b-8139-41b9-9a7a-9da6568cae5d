export default {
  namespaced: true,
  state: {
    mapItem: {},
    homeConfig: {},
    isFullscreen: false
  },
  mutations: {
    setMapItem (state, mapItem) {
      state.mapItem = mapItem
    },
    setHomeConfig (state, item) {
      state.homeConfig = item
    },
    setFullscreen (state, item) {
      state.isFullscreen = item
    }
  },
  getters: {
    getMapItem (state) {
      return state.mapItem
    },
    getHomeConfig (state) {
      return state.homeConfig
    },
    getFullscreen (state) {
      return state.isFullscreen
    }
  },
  actions: {
    setMapItem ({
      commit
    }, mapItem) {
      commit('setMapItem', mapItem)
    },
    setHomeConfig ({
      commit
    }, item) {
      commit('setHomeConfig', item)
    },
    setFullscreen ({
      commit
    }, item) {
      commit('setFullscreen', item)
    }
  }

}