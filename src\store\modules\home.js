import home from '@/config/api/home';
import axios from 'axios';

export default {
  namespaced: true,
  state: {
    mapItem: {},
    homeConfig: {},
    isFullscreen: false,
    longEchartBoxShowNum: 5, // 首页长图表区域 最多显示 n个 组件
    normalEchartBoxShowNum: 2, // 首页正常图表区域最多显示 n个 组件
  },
  mutations: {
    setMapItem(state, mapItem) {
      state.mapItem = mapItem;
    },
    setHomeConfig(state, item) {
      state.homeConfig = item;
    },
    setFullscreen(state, item) {
      state.isFullscreen = item;
    },
  },
  getters: {
    getMapItem(state) {
      return state.mapItem;
    },
    getHomeConfig(state) {
      return state.homeConfig;
    },
    getFullscreen(state) {
      return state.isFullscreen;
    },
    getLongEchartBoxShowNum(state) {
      return state.longEchartBoxShowNum;
    },
    getNormalEchartBoxShowNum(state) {
      return state.normalEchartBoxShowNum;
    },
  },
  actions: {
    setMapItem({ commit }, mapItem) {
      commit('setMapItem', mapItem);
    },
    async setHomeConfig({ commit, state }) {
      if (Object.keys(state.homeConfig).length > 0) return;
      try {
        let params = { key: 'HOME_PAGE_CONFIG' };
        let {
          data: { data },
        } = await axios.get(home.viewByParamKey, { params });
        commit('setHomeConfig', JSON.parse(data.paramValue || '{}'));
      } catch (e) {
        console.log(e);
      }
    },
    setFullscreen({ commit }, item) {
      commit('setFullscreen', item);
    },
  },
};
