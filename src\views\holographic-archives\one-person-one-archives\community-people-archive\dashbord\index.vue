<template>
  <ArchiveDashbord :base-info="baseInfo">
    <template #lastestAlaram>
      <LastestAlaram
        :alarmKindList="alarmKindList"
        :archiveNo="baseInfo.archiveNo"
        @handleAlarmMore="handleAlarmMore"
      />
    </template>
  </ArchiveDashbord>
</template>

<script>
import ArchiveDashbord from "@/views/holographic-archives/one-person-one-archives/importantPerson-archive/dashboard/index.vue";
import AlarmTab from "@/views/juvenile/components/alarm-tab.vue";
import LivingAlonePeopleTab from "@/views/community-management/components/living-alone-people-tab.vue";
import NightOutCommunityTab from "@/views/community-management/components/night-out-community-tab.vue";
import TogetherTab from "@/views/juvenile/components/together-tab.vue";
import LastestAlaram from "@/views/holographic-archives/one-person-one-archives/importantPerson-archive/dashboard/components/lastest-alarm.vue";
import { getFaceAlarmPageList } from "@/api/monographic/juvenile.js";
import {
  getElderlyPageList,
  travelAlongDetailPageList,
  getNightEnterPageList,
} from "@/api/monographic/community-management.js";
const getTravelAlongDetailPageList = async (param) => {
  const res = await travelAlongDetailPageList(param);
  const arr =
    res?.data?.entities.map(
      ({
        emphasisFaceCaptureVo,
        emphasisPersonInfo,
        srcFaceCaptureVo,
        srcPersonInfo,
        ...item
      }) => ({
        srcFaceCaptureVo,
        srcPersonInfo,
        criminalFaceCaptureVo: emphasisFaceCaptureVo,
        criminalPersonInfo: emphasisPersonInfo,
        ...item,
      })
    ) || [];
  return { data: { entities: arr } };
};
const setResultElderlyPageList = async (param) => {
  const res = await getElderlyPageList(param);
  const arr =
    res?.data?.entities.map((item) => ({
      ...item.faceCaptureVo,
      ...item,
    })) || [];
  return { data: { entities: arr } };
};
export default {
  name: "community-people-archive-dashbord",
  components: {
    ArchiveDashbord,
    LastestAlaram,
  },
  props: {
    baseInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      alarmKindList: [
        {
          title: "重点人发现报警",
          key: 1,
          list: [],
          component: AlarmTab,
          request: getFaceAlarmPageList,
        },
        {
          title: "夜间出入多小区",
          key: 5,
          list: [],
          component: NightOutCommunityTab,
          request: getNightEnterPageList,
        },
        {
          title: "孤寡老人多日未出现",
          key: 3,
          list: [],
          component: LivingAlonePeopleTab,
          request: setResultElderlyPageList,
          style: { padding: "10px" },
        },
        {
          title: "社区人员与重点人同行",
          key: 6,
          list: [],
          component: TogetherTab,
          request: getTravelAlongDetailPageList,
        },
      ],
    };
  },
  methods: {
    // 报警更多
    handleAlarmMore(selectAlarmKind) {
      const { href } = this.$router.resolve({
        path: "/community-target-control/alarm-manager",
        query: {
          compareType: selectAlarmKind.key,
          idCardNo: this.archiveNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.capture-menu {
  overflow: hidden;
  height: 30px;
  width: 300px;
  /deep/ .ivu-menu-horizontal {
    display: flex;
    height: 30px;
    line-height: 30px;
    .ivu-menu-item {
      padding: 0;
      width: 100px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &::after {
      background: transparent;
    }
  }
}
.extra-right {
  flex: 1;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.35);
  text-align: right;
  padding-right: 20px;
  cursor: pointer;
  line-height: 20px;
}
</style>
