/**
 * 处理保存接口和相关表单验证的业务逻辑
 */
import { saveJob, updateJob } from '@/api/viewAnalysis'
export default {
    data: function () {
        return {

        }
    },
    methods: {
        //保存接口参数格式化及表单验证
        _taskDataFormat: function () {
            var errorMsg = "";
            var task = this.taskInfo,
                structureOldType = (this.subTaskType === "real" ? 1 : 2);
            for (var i = 0; i < this.taskInfo.taskList.length; i++) {
                var FACE = 0,
                    HUMAN = 0,
                    VEHICLE = 0,
                    NONMOTOR = 0;
                if (this.taskInfo.taskList[i].structureOldType.face === true) {
                    FACE = 1
                }
                if (this.taskInfo.taskList[i].structureOldType.human === true) {
                    HUMAN = 2
                }
                if (this.taskInfo.taskList[i].structureOldType.vehicle === true) {
                    VEHICLE = 4
                }
                if (this.taskInfo.taskList[i].structureOldType.nonmotor === true) {
                    NONMOTOR = 8
                }
                this.taskInfo.taskList[i].structureType = FACE + HUMAN + VEHICLE + NONMOTOR
                this.taskInfo.taskList[i].structureJobType = (this.subTaskType === "real" ? 1 : 2)
                if (structureOldType == 2) {
                    this.taskInfo.taskList[i].speed = this.taskInfo.speed;
                }
                delete this.taskInfo.taskList[i].cameraType;
            }
            var param = {
                structureJob: {
                    name: task.name,
                    namePy: "",
                    type: structureOldType,
                    createTime: new Date().getTime(),
                    requestReason: task.requestReason,
                    requestAttachmentUrl: task.requestAttachmentUrl
                },
                tasks: task.taskList,
                // name: task.name,
                // subParams: task.taskList,
                baseTask: {
                    taskStartTime: new Date(task.times[0].startTime).getTime(),
                    taskEndTime: task.times[0].endTime ? new Date(task.times[0].endTime).getTime() : null
                },
                // structureOldType: structureOldType, /*数据类型 1-实时  、  2-历史结构化  、 3-虚拟卡口"(必填)*/
                // options: task.options
            };

            if (this.subTaskId) {
                param.structureJob.id = this.subTaskId;
            }
            if (typeof param.structureJob.name === "undefined" || param.structureJob.name === "") {
                errorMsg = "任务名称不能为空";
                return errorMsg;
            }
            if (!(new RegExp(/^[\u4E00-\u9FA5a-zA-Z0-9]+$/).test(param.structureJob.name))) { //注意: 空格也不支持
                errorMsg = "任务名称不支持特殊字符，只支持数字、字母、汉字";
                return errorMsg;
            }
            //处理时段校验
            if (this.taskInfo.isTaskSection) {
                if (!this.taskInfo.sectionStart || !this.taskInfo.sectionEnd) {
                    errorMsg = "处理时段未填写完整!";
                    return errorMsg;
                }
                var _sSTime = parseInt(this.taskInfo.sectionStart),
                    _sETime = parseInt(this.taskInfo.sectionEnd);
                if (_sSTime >= _sETime) {
                    errorMsg = "处理时段开始时间必须小于结束时间!";
                    return errorMsg;
                }
                param.baseTask.workStartTime = _sSTime;
                param.baseTask.workEndTime = _sETime;
            } else {
                param.baseTask.workStartTime = null;
                param.baseTask.workEndTime = null;
            }
            // if (typeof param.options === "undefined" || param.options === "") {
            // 	errorMsg = "任务类型没有勾选";
            // 	return errorMsg;
            // }
            if (!param.baseTask.taskStartTime) {
                errorMsg = "请选择开始时间";
                return errorMsg;
            }
            if (!!param.baseTask.taskEndTime && (param.baseTask.taskEndTime <= param.baseTask.taskStartTime)) {
                errorMsg = "任务有效(执行)时间:结束时间必须大于开始时间";
                return errorMsg;
            }
            if (param.tasks.length < 1) {
                errorMsg = "没有勾选设备";
                return errorMsg;
            }
            if (this._isRealStr()) { //实时结构化
                if (param.baseTask.taskEndTime && param.baseTask.taskEndTime < new Date().getTime()) {
                    errorMsg = "有效时间区间不能在当前时间之前!";
                    return errorMsg;
                }
            } else { //历史结构化判断
                param.baseTask.videoStartTime = new Date(this.histime[0]).getTime();
                param.baseTask.videoEndTime = new Date(this.histime[1]).getTime();
                if (!param.baseTask.videoStartTime || !param.baseTask.videoEndTime) {
                    errorMsg = "录像时段不能为空";
                    return errorMsg;
                }
                if (!(param.baseTask.videoStartTime < param.baseTask.videoEndTime)) {
                    errorMsg = "录像时段结束时间不能小于起始时间";
                    return errorMsg;
                }
                if (param.baseTask.taskStartTime < param.baseTask.videoEndTime || param.baseTask.taskStartTime < param.baseTask.videoStartTime) {
                    errorMsg = "执行时间不能小于录像开始时间或结束时间";
                    return errorMsg;
                }
                var _subParams = IX.clone(param.tasks).filter(function (f) {
                    return f.hasHis === true;
                });
                if (_subParams.length < 1) {
                    errorMsg = "没有选中有录像的设备,请重新选择!";
                    return errorMsg;
                } else {
                    param.tasks = _subParams;
                }
                param.baseTask.taskEndTime = ""; // 只需要执行起始时间 手动删除结束时间
                param.tasks = task.taskList.filter(function (f) {
                    return f.hasHis; //过滤只有录像的设备
                });
            }
            return param;
        },
        //保存按钮提交任务
        saveTask: function () {
            if (this.isSaveMuiltForbit) {
                console.log("SaveMuiltForbit");
                return;
            }
            var isDeviceChecked = this._testDeviceTypes();
            if (!isDeviceChecked) {
                this.$Message.error("存在设备未勾选任务类型或无设备");
                return;
            }
            var param = this._taskDataFormat();
            console.log(param);
            if (typeof param === "string") {
                this.$Message.error(param);
                return;
            }
            if (!param.structureJob.id) {
                this._saveTask("strTasksSaveEx", param);
                return;
            }
            this._saveTask("strTasksUpdateEx", param);
        },
        //接口级别:新建/编辑任务
        _saveTask: function (url, param) {
            this.isSaveMuiltForbit = true; //禁用save按钮
            if (!param.structureJob.id) {
                saveJob(param).then(result => {
                    if (result) {
                        this.$Message.success("任务保存成功");
                        this.$emit("structFinish", result); // 任务创建完成 任务信息回传
                    }
                    this.isSaveMuiltForbit = false;
                    this.closePanel();
                    this.$emit('updated')
                }).catch(() => {
                    this.isSaveMuiltForbit = false;
                });

            } else {
                updateJob(param).then(result => {
                    if (result) {
                        this.$Message.success("任务保存成功");       
                    }
                    this.isSaveMuiltForbit = false;
                    this.closePanel();
                    this.$emit('updated')
                }).catch(() => {
                    this.isSaveMuiltForbit = false;
                });
            }

        }
    }
}
