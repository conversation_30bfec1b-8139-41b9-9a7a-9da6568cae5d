.dom-content-p {
    margin-top: 6px;
    width: 100%;
    display: flex;
    height: 16px;
    line-height: 16px;

    .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 55px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
    }

    .message {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        // margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        white-space: nowrap;
    }

    .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        // margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
    }

    .plateNo {
        cursor: pointer;
        color: #2C86F8;
    }

    .identity {
        cursor: pointer;
        color: #F29F4C;
    }
}

.title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    height: 20px;
    width: 100%;
    display: flex;
    align-items: center;

    // cursor: pointer;
    >span {
        color: rgba(0, 0, 0, 0.6);
        position: relative;
        margin-right: 34px;
    }

    .active {
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
    }

    .active:before {
        content: '';
        position: absolute;
        width: 56px;
        height: 3px;
        bottom: -3px;
        background: #2c86f8;
    }

    .num {
        font-weight: normal;

        >span {
            font-weight: normal;
            color: #2c86f8;
        }
    }

    .more {
        color: rgba(0, 0, 0, 0.35);
    }
}

.fun-img {
    position: relative;

    >img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }
}

.select-preview {
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(255, 234, 75, 0.1);
    border-radius: 4px;
    border: 2px solid rgba(255, 234, 75, 1);
    display: none;
}

.similarity {
    position: absolute;
    left: 4px;
    top: 4px;
    padding: 0 10px;
    height: 30px;
    line-height: 30px;
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    border-radius: 4px;
    font-size: 18px;
    color: #fff;
    text-align: center;
}