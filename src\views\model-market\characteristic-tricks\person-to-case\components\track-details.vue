<template>
  <div class="details-box" :class="{ 'details-box-pack': footerUpDown }">
    <div class="box-hint">
      <i class="iconfont icon-jiantou" @click="handleback"></i>
      <span @click="handleback">返回碰撞结果</span>
    </div>
    <div class="box-content line-title">
      <div class="title">
        <p>共{{ dataList.length }}条轨迹详情</p>
      </div>
      <trackOffline
        v-show="!trackLine"
        ref="trackoffline"
        @openPositionTheWindow="(info) => $emit('openPositionTheWindow', info)"
      ></trackOffline>
      <ui-empty v-if="dataList.length === 0 && loading == false"></ui-empty>
      <ui-loading v-if="loading"></ui-loading>
    </div>
    <div
      class="footer"
      :class="{ packArrow: footerUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ footerUpDown ? "展开" : "收起" }}</p>
    </div>
  </div>
</template>

<script>
import trackOffline from "../../casetoperson/components/track-offline";
import { getAnalysisResultDetailByPerson } from "@/api/modelMarket";

export default {
  name: "",
  props: {
    dataId: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    trackOffline,
  },
  data() {
    return {
      dataList: [],
      loading: false,
      total: 0,
      footerUpDown: false,
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      trackLine: false,
    };
  },
  computed: {},
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    async init() {
      this.loading = true;
      const res = await getAnalysisResultDetailByPerson(this.dataId);
      this.loading = false;
      this.dataList = res.data || [];
      // this.$emit('tracklineAll', this.dataList)
      this.$refs.trackoffline.init(this.dataList);
    },

    handleback() {
      this.$emit("goback");
    },
    handleChangeSwitch(value) {
      console.log(value, "value");
    },
    // 收缩、展开
    handlePackup() {
      this.footerUpDown = !this.footerUpDown;
    },
  },
};
</script>

<style lang='less' scoped>
@import "../../../components/style/index";

.details-box {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 10px;
  height: calc(~"100% - 30px");
  transition: height 0.2s ease-out;

  .box-hint {
    width: 370px;
    height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    filter: blur(0px);
    color: #2c86f8;
    font-size: 14px;
    line-height: 40px;
    padding-left: 14px;

    .icon-jiantou {
      transform: rotate(90deg);
      display: inline-block;
      cursor: pointer;
    }

    span {
      font-size: 14px;
      cursor: pointer;
      margin-left: 10px;
    }
  }

  .box-content {
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    margin-top: 10px;
    height: calc(~"100% - 50px");
    .title {
      .switch {
        font-weight: 400;
        font-size: 14px;
        color: #3d3d3d;
        display: flex;
        align-items: center;

        p {
          margin-left: 10px;
        }
      }

      .title-left {
        display: flex;
        align-items: center;

        .title-icon {
          position: relative;
          background: url("~@/assets/img/map/trajectory-red.png") no-repeat;
          background-size: 100% 100%;
          width: 20px;
          height: 22px;
          color: #ea4a36;

          > span {
            position: absolute;
            top: -12px;
            width: 20px;
            font-size: 10px;
            color: #ea4a36;
            text-align: center;
          }
        }
      }
    }
  }

  .footer {
    color: #000000;
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 100%;
    z-index: 30;
  }
}

.details-box-pack {
  height: 120px;
  transition: height 0.2s ease-out;
  overflow: hidden;
}
</style>
