<template>
  <div class="" v-clickoutside="dropHide">
    <Dropdown class="drop-tree" trigger="custom" :visible="visible" ref="dropDown">
      <div class="ivu-select ivu-select-single t-left" ref="dropSelect">
        <div class="ivu-select-selection select-width" @click="dropShow">
          <Input type="hidden" v-model="treeProps.treeValue" v-if="false"></Input>
          <div>
            <span class="ivu-select-placeholder" v-if="!treeProps.treeValue">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="treeProps.treeValue">{{ treeProps.treeValue }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!treeProps.treeValue"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="treeProps.treeValue" />
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" ref="dropDownMenu" :style="dropDownMenuStyle">
        <div class="dropdown-content flex">
          <el-tree
            class="tree"
            :class="horizontalLayout ? 'fl' : 'fr'"
            :tree-style="treeStyle"
            :data="list"
            :props="defaultProps"
            :node-key="treeProps.nodeKey"
            :expand-on-click-node="true"
            @node-click="handleNodeClick"
            @current-change="currentChange"
          >
            <!-- :expand-on-click-node="false" 节点全部展开 -->
            <template #default="{ node, data }">
              <div class="node">
                <span class="fl" :title="node.label">{{ node.label }} </span>
                <i class="icon-font icon-arrow fr arrow" v-if="!!data.cascader && data.cascader.length >= 0"></i>
              </div>
            </template>
          </el-tree>
          <div class="cascader fl" v-if="cascaderShow">
            <slot name="right-table">
              <div
                class="cascader-item"
                v-for="(item, index) in cascaderData"
                :key="index"
                :title="item[cascaderProps.label]"
                @click="selectCascader(item)"
              >
                {{ item[cascaderProps.label] }}
              </div>
            </slot>
          </div>
        </div>
        <slot name="drop-footer">
          <div class="drop-footer" v-if="needQuery">
            <Button type="primary" @click="submitCategory">确定</Button>
            <Button @click="resetChecked">重置</Button>
          </div>
        </slot>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
.align-justify-flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.select-width {
  width: 200px;
}
.dropdown-content {
  width: 100%;
}
.tree {
  //   min-width: 160px;
  flex: 1;
  overflow-y: auto;
  font-size: 12px;
  max-height: 300px;
  margin-right: 10px;
  border-right: 1px solid var(--devider-line);
  .node {
    width: 100%;
    span {
      // color: #56789c;
    }
  }
}
.cascader {
  //   min-width: 160px;
  flex: 1;
  overflow-x: auto;
  max-height: 300px;
  .cascader-item {
    font-size: 12px;
    color: #fff;
    cursor: pointer;
    padding: 0 10px;
    width: 200px;
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
    &:hover {
      background: @bg-darkblue-block;
    }
    &:focus {
      background: #5daaf0;
    }
  }
}
.arrow {
  font-size: 12px;
  transform: rotate(-90deg);
}
.drop-tree {
  width: 100%;
}
.flex {
  display: flex;
}
.drop-footer {
  width: 100%;
  padding: 10px 0 5px;
  border-top: 1px solid var(--devider-line);
  .align-justify-flex;
  @{_deep} .ivu-btn-default {
    .align-justify-flex;
    width: 48px;
    height: 28px;
    border-radius: 4px;
  }
  @{_deep} .ivu-btn-primary {
    .align-justify-flex;
    width: 48px;
    height: 28px;
    margin-right: 10px;
    border-radius: 4px;
  }
}
@{_deep} .el-tree-node {
  min-width: 100%;
  width: max-content;
}
</style>
<script>
export default {
  props: {
    /**
     * treeProps.treeValue: 选中的文本
     * treeProps.id: 选中的id
     * treeProps.nodeKey: 对应树组件的 nodeKey
     */
    treeProps: {
      required: true,
    },
    /**
     * defaultProps.label: 展示的字段
     * defaultProps.children: 子节点的字段
     */
    defaultProps: {
      required: true,
    },
    list: {
      required: true,
      default() {
        return [];
      },
    },
    // 树结构style
    treeStyle: {},
    isDefault: {
      type: Boolean,
      default: true,
    },
    // 根节点有是否可以选中
    rootNodeClick: {
      type: Boolean,
      default: true,
    },
    hasCascader: {
      type: Boolean,
      default: false,
    },
    // 级联选择
    cascaderProps: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value',
          nodeKey: 'id',
        };
      },
    },
    // 更改下拉框级联选择样式
    cascaderChange: {
      type: Function,
    },
    placeholder: {
      type: String,
      default: () => {
        return '请选择';
      },
    },
    dropWidth: {
      type: Number,
    },
    // 是否自定义右侧内容
    isCustom: {
      type: Boolean,
      default: false,
    },
    // 已选中的id
    // checkIds: {
    //   type: Array,
    //   default: () => [],
    // },
    // 已选中的节点数据
    customAreaNames: {
      type: Array,
      default: () => [],
    },
    needQuery: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      defaultKeys: [],
      cascaderShow: false,
      cascaderData: [],
      selectItem: null,
      dropDownMenuStyle: {},
      dropMenuWidth: null, //下拉列表宽度
      horizontalLayout: true, //是否左右水平布局
      cascaderWidth: null, //级联选择宽度
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 节点选中触发
    handleNodeClick(data) {
      if (this.isCustom) {
        this.$emit('selected', data);
        this.cascaderShow = true;
        return;
      }
      if (!this.rootNodeClick && !!data.children && !!data.children.length) return;
      this.selectItem = null;
      if (this.hasCascader) {
        this.cascaderData = data.cascader;
        if (!!data.cascader && data.cascader.length > 0) {
          //   this.changeLeft(true, this.changeDropStyle);
          setTimeout(() => {
            this.cascaderShow = true;
          });
        } else {
          //   this.changeLeft(false, this.changeDropStyle);
          this.cascaderShow = false;
        }
      } else {
        this.$set(this.treeProps, 'id', data[this.treeProps.nodeKey]);
        this.$emit('selectedTree', data);
      }
    },
    // 用id找到当前的树对应的item
    searchId(arr, val) {
      // 如果有级联选择则查询级联选择中的数据
      if (this.hasCascader) {
        for (let i = 0, length = arr.length; i < length; i++) {
          if (!!arr[i][this.cascaderProps.nodeKey] && arr[i][this.cascaderProps.nodeKey].length !== 0) {
            for (let j = 0, len = arr[i][this.cascaderProps.nodeKey].length; j < len; j++) {
              if (arr[i][this.cascaderProps.nodeKey][j][this.cascaderProps.value] === val) {
                this.selectItem = arr[i][this.cascaderProps.nodeKey][j];
                return false;
              }
            }
          }
          // 如果没有找到级联选择中对应的值，则查询子列表级联
          if (
            !this.selectItem &&
            !!arr[i][this.defaultProps.children] &&
            arr[i][this.defaultProps.children].length !== 0
          ) {
            this.searchId(arr[i][this.defaultProps.children], val);
          }
        }
      } else {
        for (let i = 0, length = arr.length; i < length; i++) {
          if (arr[i][this.treeProps.nodeKey] !== val) {
            if (!!arr[i][this.defaultProps.children] && arr[i][this.defaultProps.children].length !== 0) {
              this.searchId(arr[i][this.defaultProps.children], val);
            }
          } else {
            this.selectItem = arr[i];
            return false;
          }
        }
      }
    },
    dropHide() {
      if (this.hasCascader && this.visible) {
        this.cascaderShow = false;
        this.visible = false;
      }
    },
    dropShow() {
      if (this.hasCascader) {
        this.cascaderShow = false;
        this.getBoundingClientRect();
        // this.changeLeft(false, this.changeDropStyle);
      }
      this.visible = !this.visible;
      this.$emit('handleTreeShow');
    },
    currentChange(data) {
      if (!(this.hasCascader || (!this.rootNodeClick && !!data.children && !!data.children.length))) {
        this.visible = false;
      }
    },
    clear() {
      this.$set(this.treeProps, 'treeValue', '');
      this.$emit('clearTree');
    },
    selectCascader(item) {
      this.$set(this.treeProps, 'id', item[this.cascaderProps.value]);
      this.$emit('selectCascader', item);
      this.visible = false;
      this.cascaderShow = false;
      //   this.changeLeft(false, this.changeDropStyle);
    },
    // 更改下拉列表宽度，适应级联选择样式
    changeDropStyle() {
      this.dropDownMenuStyle.width = this.dropWidth ? this.dropWidth : this.$refs.dropSelect.clientWidth / 2 + 'px';
    },
    // 判断是否已经到窗口边界
    getBoundingClientRect() {
      this.dropMenuWidth = (this.dropWidth * parseFloat(document.documentElement.style.fontSize)) / 192;
      this.cascaderWidth = this.dropMenuWidth - this.$refs.dropSelect.clientWidth;
      if (
        document.documentElement.clientWidth - this.$refs.dropDown.$el.getBoundingClientRect().right <
        this.cascaderWidth
      ) {
        this.horizontalLayout = false;
      } else {
        this.horizontalLayout = true;
      }
    },
    submitCategory() {
      this.$emit('handleSaveCategory');
      this.visible = false;
    },
    // 重置
    resetChecked() {
      this.visible = false;
      this.treeProps.treeValue = '';
      this.$emit('resetCategory');
    },
    /**
     * bool：判断是否更改下拉列表左侧定位
     * callback：定位更改完成后，更改下拉列表宽度用来适应级联选择样式
     */
    // changeLeft(bool, callback) {
    //   let left = "initial";
    //   if (!this.horizontalLayout && bool) {
    //     left = -this.cascaderWidth + "px";
    //   }
    //   let dom = this.$refs.dropDown.$children.find((item) => {
    //     return item.$el.className.indexOf("ivu-select-dropdown") !== -1;
    //   });
    //   /**
    //    * iview 本身drop会有一个动画 当动画执行完成后left才会发生改变
    //    * 所以这里需要延时之后修改left的值
    //    */
    //   setTimeout(() => {
    //     dom.$el.style.left = left;
    //     callback(bool);
    //   });
    // },
  },
  watch: {
    list(val) {
      if (val.length !== 0 && this.isDefault) {
        this.defaultKeys = val.map((row) => {
          return row[this.treeProps.nodeKey];
        });
      }
    },
    // 当选择的树id改变时查找树列表中对应的item并把label显示出来
    'treeProps.id'(val) {
      if (val) {
        this.searchId(this.list, val);
        let valueStr = this.hasCascader ? this.cascaderProps.label : this.defaultProps.label;

        let treeValue = this.selectItem[valueStr] ? this.selectItem[valueStr] : '';
        this.$set(this.treeProps, 'treeValue', treeValue);
      } else {
        this.$set(this.treeProps, 'treeValue', '');
      }
    },
    customAreaNames() {
      this.treeProps.treeValue = this.customAreaNames.join(' ');
    },
    // checkNodes(){
    //   const checkeds = []
    //   this.checkNodes.forEach((item) => {
    //     checkeds.push(item.areaTreeName);
    //   });
    //   this.treeProps.treeValue = checkeds.join(" ");
    // }
  },
  computed: {},
  components: {},
};
</script>
