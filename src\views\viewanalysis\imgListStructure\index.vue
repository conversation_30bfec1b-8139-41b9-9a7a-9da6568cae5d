<!--
    * @FileDescription: 图片序列解析
    * @Author: H
    * @Date: 2024/05/15
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-23 16:25:44
 -->
<template>
  <div class="container">
    <!-- 查询 -->
    <Search ref="search" :subTaskType="subTaskType" @searchForm="searchForm" />
    <div class="table-container">
      <div class="data-above">
        <Button size="small" @click="handleViewDetails">
          <i class="iconfont icon-gaojisousuo"></i>
          批量实时查看
        </Button>
        <Button class="mr" @click="handleLocation" size="small">
          <i class="iconfont icon-dongtai-shangtudaohang"></i>
          批量查看定位
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #dataTypeIcon="{ row }">
            <div class="status">
              <div class="exist">
                <i
                  class="iconfont icon-qiche1 cl"
                  v-if="row.dataType == 2"
                  title="车辆"
                ></i>
                <i class="iconfont icon-qiche1" v-else title="车辆"></i>
              </div>
              <div class="exist">
                <i
                  class="iconfont icon-diandongche fjdc"
                  v-if="row.dataType == 3"
                  title="非机动车"
                ></i>
                <i
                  class="iconfont icon-diandongche"
                  v-else
                  title="非机动车"
                ></i>
              </div>
              <div class="exist">
                <i
                  class="iconfont icon-renlian1 rl"
                  v-if="row.dataType == 1"
                  title="人脸"
                ></i>
                <i class="iconfont icon-renlian1" v-else title="人脸"></i>
              </div>
              <div class="exist">
                <i
                  class="iconfont icon-renti1 rt"
                  v-if="row.dataType == 4"
                  title="人体"
                ></i>
                <i class="iconfont icon-renti1" v-else title="人体"></i>
              </div>
            </div>
          </template>
          <template #dataType="{ row }">
            <span>{{ row.deviceType | commonFiltering(ivcpDeviceType) }}</span>
          </template>
          <template #opreate="{ row }">
            <div class="opreate">
              <div class="tools">
                <Poptip trigger="hover" placement="left-start">
                  <i class="iconfont icon-gengduo"></i>
                  <div class="mark-poptip" slot="content">
                    <p @click="handleViewList(row)">
                      <i class="iconfont icon-gaojisousuo"></i>实时查看
                    </p>
                    <p @click="mapLoaction(row)">
                      <i class="iconfont icon-dongtai-shangtudaohang"></i>定位
                    </p>
                    <!-- <p @click="handleDownloadFile(row)"> <i class="iconfont icon-download"></i>卡扣结构化资源</p> -->
                  </div>
                </Poptip>
              </div>
            </div>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>

    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
    <direction-model ref="directionModel"></direction-model>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import Search from "./components/search.vue";
import adjustPosition from "../components/adjustPosition.vue";
import {
  getStructureRealTaskList,
  pageList,
  restartTask,
  getFileInfos,
} from "@/api/viewAnalysis";
import directionModel from "@/views/wisdom-cloud-search/search-center/advanced-search/components/direction-model";
import { commonFiltering } from "@/util/filters";
export default {
  name: "fileStructure",
  components: {
    Search,
    adjustPosition,
    directionModel,
  },
  props: {},
  data() {
    return {
      subTaskType: "file",
      list: [],
      loading: false,
      pageForm: {
        dataTime: "",
      },
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "设备名称", key: "deviceName" },
        { title: "解析类型", slot: "dataTypeIcon", align: "center" },
        { title: "设备类型", slot: "dataType" },
        { title: "前一日处理数量", key: "accessNum" },
        { title: "操作", slot: "opreate", width: 100 },
      ],
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
    };
  },
  computed: {
    ...mapGetters({
      ivcpDeviceType: "dictionary/getIvcpDeviceType", // 车辆品牌
    }),
  },
  created() {
    this.getDictStructData();
    this.$nextTick(() => {
      this.pageForm.dataTime = this.$refs.search.formData.dataTime;
      this.getList();
    });
  },
  methods: {
    ...mapActions({
      getDictStructData: "dictionary/getDictStructData",
    }),
    // 查询列表
    getList() {
      this.loading = true;
      this.list = [];
      let params = {
        ...this.pageForm,
        ...this.params,
      };
      pageList(params)
        .then((res) => {
          const { entities, total } = res.data;
          this.list = entities || [];
          this.total = total;
          this.selectedData = [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 查询
    searchForm(form) {
      if (form.dataTime == "") {
        this.$Message.warning("数据时间为必填项！");
        return;
      }
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleViewDetails() {
      if (!this.selectedData || !this.selectedData.length) {
        this.$Message.warning("当前未选中数据");
        return;
      }
      let deviceList = this.selectedData.map((item) => {
        return {
          deviceGbId: item.deviceId,
          deviceName: item.deviceName,
        };
      });
      let page = {
        1: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        2: "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1",
        4: "/wisdom-cloud-search/search-center?sectionName=humanBodyContent&noMenu=1",
        3: "/wisdom-cloud-search/search-center?sectionName=nonmotorVehicleContent&noMenu=1",
      };
      const { href } = this.$router.resolve({
        path: `${page[this.selectedData[0].dataType]}`,
        query: {
          deviceList: JSON.stringify(deviceList),
        },
      });
      window.open(href, "_blank");
    },
    handleViewList(row) {
      let page = {
        1: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        2: "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1",
        4: "/wisdom-cloud-search/search-center?sectionName=humanBodyContent&noMenu=1",
        3: "/wisdom-cloud-search/search-center?sectionName=nonmotorVehicleContent&noMenu=1",
      };
      const { href } = this.$router.resolve({
        path: `${page[row.dataType]}`,
        query: {
          deviceList: JSON.stringify([
            { deviceGbId: row.deviceId, deviceName: row.deviceName },
          ]),
        },
      });
      window.open(href, "_blank");
    },
    handleLocation() {
      if (!this.selectedData || !this.selectedData.length) {
        this.$Message.warning("当前未选中数据");
        return;
      }
      let pointList = [];
      this.selectedData.forEach((item) => {
        if (item.geoPoint && item.geoPoint.lat && item.geoPoint.lon) {
          pointList.push({
            geoPoint: item.geoPoint,
            deviceName: item.deviceName,
          });
        }
      });
      if (pointList.length > 0) {
        this.$refs.directionModel.showList(pointList);
      } else {
        this.$Message.error("当前选择设备无点位信息，无法定位");
      }
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      if (item.geoPoint && item.geoPoint.lat && item.geoPoint.lon) {
        const { geoPoint, deviceName } = { ...item };
        this.$refs.directionModel.show({ geoPoint, deviceName });
      } else {
        this.$Message.error("该设备无点位信息，无法定位");
        return;
      }
    },

    onSelect(selection) {
      this.selectedData = selection;
    },
    onSelectCancel(selection) {
      this.selectedData = selection;
    },
    onSelectAll(selection) {
      this.selectedData = selection;
    },
    onSelectAllCancel() {
      this.selectedData = [];
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;

  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    .data-above {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      button {
        margin-right: 10px;
      }
    }

    .table-content {
      flex: 1;

      .ui-table {
        height: 100%;
      }

      .status {
        display: flex;
        justify-content: center;

        .without,
        .exist {
          display: flex;
          margin-right: 10px;

          i {
            margin-right: 5px;
          }
        }

        .cl {
          color: #15997d;
        }

        .fjdc {
          color: #49bf00;
        }

        .rl {
          color: #d36c29;
        }

        .rt {
          color: #08c6d4;
        }
      }
    }

    .opreate {
      display: flex;
    }

    .tools {
      color: #2c86f8;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      margin-left: 5px;

      .icon-gengduo {
        transform: rotate(90deg);
        transition: 0.1s;
        display: inline-block;
      }

      p:hover {
        color: #2c86f8;
      }

      &:hover {
        background: #2c86f8;
        color: #fff;

        .icon-gengduo {
          transform: rotate(0deg);
          transition: 0.1s;
        }

        border-radius: 10px;
      }

      /deep/ .ivu-poptip-popper {
        min-width: 150px !important;
        width: 40px !important;
        height: auto;
      }

      /deep/.ivu-poptip-body {
        height: auto !important;
      }

      .mark-poptip {
        color: #000;
        cursor: pointer;
        text-align: left;

        /deep/ .ivu-icon-ios-add-circle-outline {
          font-weight: 600;
        }
      }

      .disabled {
        cursor: not-allowed;
        color: grey !important;
      }
    }

    /deep/td.ivu-table-expanded-cell {
      padding: 0 !important;
    }
  }
}
</style>
