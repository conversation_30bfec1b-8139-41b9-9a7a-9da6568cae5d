<template>
  <div class="ui-switch-tab">
    <ul class="list">
      <li
        v-for="item in indexList"
        :key="item.key"
        :class="{ active: item.key == activeKey, 'no-drop': isDrop(item.key) }"
        class="ivu-tabs-tab"
        @click="tabClick(item)"
      >
        <!-- <i v-if="tabNum !== 2" class="icon-font f-12 icon-all" :class="[item.key == '-1' ? 'icon-viewassets' : '']" :title="item.value"></i> -->
        <i class="icon-font f-12" :class="item.icon + ' icon-bg' + item.key" :title="item.value"></i>
        <!-- <i class="icon-font f-12" :class="[item.key == '5' ? 'icon-keypersonlibrary icon-bg6' : '']" :title="item.value"></i>
        <i class="icon-font f-12 icon-bg1" :class="[item.key == '1' ? 'icon-shitujichushuju' : '']" :title="item.value"></i>
        <i class="icon-font f-12 icon-bg2" :class="[item.key == '2' ? 'icon-renliankakou' : '']" :title="item.value"></i>
        <i class="icon-font f-12 icon-bg3" :class="[item.key == '3' ? 'icon-cheliangshitushuju' : '']" :title="item.value"></i>
        <i class="icon-font f-12 icon-bg1" :class="[item.key == '6' ? 'icon-shishishipinliutongchangjiance1' : '']" :title="item.value"></i> -->
      </li>
    </ul>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  name: 'reach-tabs',
  props: {
    tabList: {
      required: true,
      default() {
        return {};
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    tabNum: {
      type: Number,
      required: true,
    },
    value: {
      type: [Array, String, Number],
    },
  },
  data() {
    return {
      list: [],
      icon: '',
      activeKey: 0,
    };
  },
  created() {},
  mounted() {},
  methods: {
    tabClick(item) {
      if (this.isDrop(item.key)) return;
      this.activeKey = item.key;
      this.$emit('changeTab', item.key, item.value, this.tabList);
    },
    isDrop(key) {
      return !(this.tabList.indexModules || [])
        .map((it) => {
          return it.key;
        })
        .includes(key);
    },
  },
  watch: {
    tabList: {
      handler(val) {
        if (!val) return;
        // 区分省厅 - 默认
        if (
          this.systemConfig.distinguishVersion === '1' ||
          (this.systemConfig.distinguishVersion === '2' && val.taskType === 2)
        ) {
          if (val.indexModules) {
            this.activeKey = val.indexModules[1].key;
          }
        } else {
          if (val.indexModules) {
            this.activeKey = val.indexModules[0].key;
          }
        }

        // if (this.tabNum == 2 && this.tabList.indexModules && this.tabList.indexModules[0] && this.tabList.indexModules[0].key === '-1') {
        //   this.list = this.$util.common.deepCopy(this.indexList)
        //   this.activeKey = this.tabList.indexModules[1].key
        // } else {
        //   this.list = this.$util.common.deepCopy(this.tabList.indexModules)
        //   this.activeKey = this.list && this.list[0] && this.list[0].key
        // }
      },
      immediate: true,
    },
    value() {},
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
    indexList() {
      let list = [];
      if (
        this.systemConfig.distinguishVersion === '1' ||
        (this.systemConfig.distinguishVersion === '2' && this.tabList.taskType === 2)
      ) {
        list = [
          { key: '1', value: '视图基础数据指标', icon: 'icon-shitujichushuju' },
          { key: '2', value: '人脸视图数据指标', icon: 'icon-renliankakou' },
          {
            key: '3',
            value: '车辆视图数据指标',
            icon: 'icon-cheliangshitushuju',
          },
          { key: '4', value: '视频流数据指标', icon: 'icon-shipinliushuju' },
          {
            key: '5',
            value: '重点人员数据指标',
            icon: 'icon-keypersonlibrary',
          },
        ];
      } else {
        list = [
          { key: '-1', value: '全部数据指标', icon: 'icon-wentizongshu' },
          { key: '1', value: '视图基础数据指标', icon: 'icon-shitujichushuju' },
          { key: '2', value: '人脸视图数据指标', icon: 'icon-renliankakou' },
          {
            key: '3',
            value: '车辆视图数据指标',
            icon: 'icon-cheliangshitushuju',
          },
          { key: '4', value: '视频流数据指标', icon: 'icon-shipinliushuju' },
          {
            key: '5',
            value: '重点人员数据指标',
            icon: 'icon-keypersonlibrary',
          },
          { key: '7', value: '接口稳定性指标', icon: 'icon-danganshuju' },
        ];
      }
      return list;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .ui-switch-tab {
    .list {
      border: 1px solid var(--border-switch-tag-tab);
      .no-drop, .no-drop:hover{
        background-color: #ffffff;
        .icon-all {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg-1 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg1 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg2 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg3 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg4 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg5 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg6 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg7 {
          background: var(--color-switch-tab-active);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      li {
        background: #ffffff;
        color: var(--color-switch-tab-active);
        border-left: 1px solid var(--border-switch-tag-tab);
        &:hover {
          background-color: var(--color-primary);
          .icon-all {
            background: linear-gradient(180deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg-1 {
            background: linear-gradient(360deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg1 {
            background: linear-gradient(180deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg2 {
            background: linear-gradient(360deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg3 {
            background: linear-gradient(360deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg4 {
            background: linear-gradient(180deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg5 {
            background: linear-gradient(180deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg6 {
            background: linear-gradient(360deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg7 {
            background: linear-gradient(360deg, #fff 0%, #fff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
      .icon-all {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg-1 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg1 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg2 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg3 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg4 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg5 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg6 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg7 {
        background: var(--color-switch-tab-active);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .active {
        background: var(--color-primary);
        .icon-all {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg-1 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg1 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg2 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg3 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg4 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg5 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg6 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg7 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        &:hover {
          color: #fff;
        }
      }
    }
  }
}
.ui-switch-tab {
  font-size: 14px;
  .list {
    display: inline-block;
    border: 1px solid #174f98;
    border-radius: 4px;
    .no-drop {
      cursor: no-drop !important;
      background-color: #022143;
      i {
        // color: #215287 !important;
        cursor: no-drop !important;
      }
      .icon-all {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg-1 {
        background: linear-gradient(360deg, #ff7474 0%, #ff7474 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg1 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg2 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg3 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg4 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg5 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg6 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg7 {
        background: linear-gradient(360deg, #215287 0%, #215287 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &:hover {
        background: #022143;
        .icon-all {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg-1 {
          background: linear-gradient(360deg, #ff7474 0%, #ff7474 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg1 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg2 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg3 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg4 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg5 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg6 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg7 {
          background: linear-gradient(360deg, #215287 0%, #215287 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    li {
      cursor: pointer;
      display: inline-block;
      background: #022143;
      padding: 5px 9px;
      color: #215287;
      position: relative;
      border-left: 1px solid #174f98;
      &:hover {
        background-color: var(--color-primary);
        .icon-all {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg-1 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg1 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg2 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg3 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg4 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg5 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg6 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .icon-bg7 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-left: none;
      }
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .icon-all {
      background: linear-gradient(180deg, #b58e0d 0%, #7e6f0a 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg-1 {
      background: linear-gradient(360deg, #ff7474 0%, #ff7474 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg1 {
      background: linear-gradient(180deg, #1bafd5 0%, #0a9f90 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg2 {
      background: linear-gradient(360deg, #52049f 0%, #9f5ce4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg3 {
      background: linear-gradient(360deg, #1641ee 0%, #67acfb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg4 {
      background: linear-gradient(180deg, #b58e0f 0%, #bb6603 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg5 {
      background: linear-gradient(180deg, #d811cc 0%, #7710aa 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg6 {
      background: linear-gradient(360deg, #780f0f 0%, #ec5353 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg7 {
      background: linear-gradient(360deg, #17c89e 0%, #17c89e 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .active {
      background: var(--color-primary);
      .icon-all {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg-1 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg1 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg2 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg3 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg4 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg5 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg6 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .icon-bg7 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      &:hover {
        color: #fff;
      }
    }
  }
}
</style>
