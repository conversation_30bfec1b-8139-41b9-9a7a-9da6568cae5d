<template>
  <div class="statistics-list auto-fill">
    <title-bar
      v-bind="$attrs"
      :index-data="indexData"
      :tab-list="tabList"
      :sort-data="sortData"
      :date-type-list="dateTypeList"
      @handleChangeTab="handleChangeTab"
      @onDateTypeChange="onDateTypeChange"
    >
    </title-bar>
    <div class="statistics-list-content auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :row-class-name="rowClassName"
        @onSortChange="onSortChange"
      >
        <template #civilName="{ row, column }">
          <span
            :class="['font-highlight', paramsList.dateType === '1' ? 'font-active-color underline-text pointer' : '']"
            @click="viewMonthDetail(row)"
          >
            {{ row[column.key] }}
          </span>
        </template>
        <template #unqualified="{ row }">
          <span
            :class="['unqualified-color', paramsList.dateType === '2' ? 'underline-text pointer' : '']"
            @click="viewDayDetail(row, '2')"
          >
            {{ row.unqualified }}
          </span>
        </template>
        <template #rate="{ row }">
          <span :class="row.qual === '1' ? '' : 'unqualified-color'">{{ row.rateFormat }}</span>
        </template>
      </ui-table>
    </div>
    <day-review-details
      v-model="dayReviewDetailVisible"
      v-bind="$attrs"
      :data-dimension-enum="dataDimensionEnum"
      :index-data="indexData"
      :row-data="rowData"
    ></day-review-details>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import { tableColumns } from './tableColumns.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import { DAY_STATISTICS } from '@/views/specialassessment/utils/common';

export default {
  name: 'VideoOnline',
  mixins: [dealWatch],
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      tableColumns: [],
      tableData: [],
      dayReviewDetailVisible: false,
      dataDimensionEnum: 'DEVICE_ALL',
      rowData: {},
      tabList: [],
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      examineTime: '',
      examineDate: '',
      dateTypeList: [{ label: '日统计', value: '2' }],
    };
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        let { dateType, batchIds } = this.$route.query;
        if (dateType === DAY_STATISTICS && !batchIds) return;
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        const queryParams = this.$route.query;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: queryParams.examineTime,
          type: queryParams.dateType || '2',
          dataDimensionEnum: this.dataDimensionEnum,
          nodeEnum: queryParams.indexType,
          paramForm: {},
          statisticalModel: queryParams.statisticsCode ? Number(queryParams.statisticsCode) : null,
          displayType:
            queryParams.statisticsCode === '1'
              ? 'ORG'
              : queryParams.statisticsCode === '2'
                ? 'REGION'
                : queryParams.statisticsCode === '3'
                  ? 'TAG'
                  : '',
        };
        if (queryParams.dateType === '2') {
          params.batchIds = queryParams.batchIds ? queryParams.batchIds.split(',') : [];
        }
        if (this.sortData.sortField) {
          params.paramForm = {
            indexId: this.tableData[0].detail[0].indexId,
            sortField: this.sortData.sortField,
            sort: this.sortData.sort,
          };
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfoList, params);
        if (!data || !data.length) {
          this.tableData = [];
          return false;
        }
        this.tabList = data[0].dataDimensionsDisplay || [];
        this.tableData = data.map((item) => {
          if (item.detail) {
            return {
              ...item,
              ...item.detail[0],
            };
          }
          return item;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    rowClassName(row, index) {
      if (index === this.tableData.length - 1) {
        return 'active-blue';
      }
      return '';
    },
    onSortChange(column) {
      if (column.order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          indexId: this.tableData[0].detail[0].indexId,
          sortField: column.key,
          sort: column.order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    handleChangeTab(data) {
      this.dataDimensionEnum = data.value;
      this.getTableList();
    },
    viewMonthDetail(row) {
      if (this.paramsList.dateType === '2') return false;
      this.$emit('changeComponentName', [
        'MonthReviewDetail',
        {
          dataDimensionEnum: this.dataDimensionEnum,
          ...row,
        },
      ]);
    },
    // qualVal 控制检测详情筛选条件-检测结果默认值
    viewDayDetail(row, qualVal) {
      if (this.paramsList.dateType === '1') return false;
      this.dayReviewDetailVisible = true;
      this.rowData = {
        qualVal,
        ...row,
        ...row.detail[0],
      };
    },
    onDateTypeChange() {
      this.sortData = {};
    },
    getParams() {
      this.paramsList = this.$route.query;
      const { examineTime, indexType, statisticsCode } = this.$route.query;
      this.tableColumns = this.$util.common.deepCopy(tableColumns({ statisticsCode: statisticsCode })[indexType]);
      this.tableData = [];
      this.examineTime = examineTime;
      this.getTableList();
    },
    changeMonthPicker(date) {
      this.examineTime = date;
      this.$router.push({
        query: {
          ...this.paramsList,
          examineTime: this.examineTime || '',
        },
      });
    },
  },
  components: {
    TitleBar: require('@/views/specialassessment/components/title-bar.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DayReviewDetails: require('@/views/specialassessment/special-modules/components/day-review-details/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.statistics-list {
  &-content {
    padding: 0 20px;
  }
  .underline-text {
    text-decoration: underline;
  }
  .unqualified-color {
    color: var(--color-failed);
  }
  .qualified-color {
    color: var(--color-success);
  }
  @{_deep} .active-blue {
    td:nth-child(2) {
      .font-highlight {
        color: var(--color-warning) !important;
      }
    }
  }
}
</style>
