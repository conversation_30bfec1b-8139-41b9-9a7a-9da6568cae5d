import BasicLayout from "@/layouts/basic-layout";
// import archivesRouter from './archives'
export default [
  {
    path: "/login",
    name: "<PERSON><PERSON>",
    hidden: true,
    component: (resolve) => require(["@/views/login/login.vue"], resolve),
    meta: {
      title: "登录",
    },
  },
  // 从外部进来 / 跳转到动态的首页 配合不同专题库使用
  // {
  //   path: "/",
  //   redirect: "/login",
  //   hidden: true,
  // },
  {
    path: "/404",
    name: "error_404",
    hidden: true,
    component: (resolve) => require(["@/views/error-page/404.vue"], resolve),
  },
  {
    path: "/demo",
    component: (resolve) => require(["@/views/demo/demo.vue"], resolve),
  },
  {
    path: "/mapDemo",
    component: (resolve) => require(["@/views/mapDemo/index.vue"], resolve),
  },
  {
    path: "/exscreen",
    hidden: true,
    component: (resolve) =>
      require(["@/views/video-application/exscreen.vue"], resolve),
  },
  // {
  //   path: '/deviceInfo',
  //   name: 'deviceInfo',
  //   component: (resolve) => require(['@/views/deviceInfo/index'], resolve),
  //   hidden: true
  // }
];
