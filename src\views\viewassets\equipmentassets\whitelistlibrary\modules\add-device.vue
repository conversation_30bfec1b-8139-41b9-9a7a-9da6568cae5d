<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles">
    <div class="model-wrapper">
      <section class="left-content">
        <p class="content-title base-text-color mb-sm">
          所有设备列表（
          <span class="font-red">{{ pageData.totalCount }}</span> )条
        </p>
        <ui-search-tree
          class="padding20"
          placeholder="请输入组织机构名称或组织机构编码"
          node-key="orgCode"
          :max-height="650"
          :tree-data="treeData"
          :default-props="defaultProps"
          :default-keys="defaultExpandedKeys"
          :current-node-key="currentNodeKey"
          @selectTree="selectTree"
        >
        </ui-search-tree>
      </section>
      <section class="middle-content">
        <div class="search-box content-title">
          <ui-label label="关键词" :width="60" class="inline search-input">
            <Input
              class="width-lg"
              suffix="ios-search"
              v-model="searchData.keyWord"
              placeholder="请输入设备名称或设备编码"
              @on-enter.native="searchkeyword"
              >></Input
            >
            <div class="ml-lg">
              <Button type="primary" class="mr-sm" @click="searchkeyword"> 查询 </Button>
              <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, searchkeyword)"> 重置 </Button>
            </div>
          </ui-label>

          <Checkbox v-model="allDeviceChecked" class="mr-sm" @on-change="checkAll"> &nbsp;全部 </Checkbox>
        </div>
        <ui-table
          ref="selection"
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :minus-height="minusTable"
          :loading="loading"
          @selectTable="selectLeftTable"
          @oneSelected="oneSelected"
          @cancelSelectTable="cancelSelectTable"
          @onSelectAllTable="onSelectAllTable"
          @cacelAllSelectTable="cacelAllSelectTable"
        >
          <template slot="deviceId" slot-scope="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
        </ui-table>
        <ui-page
          class="page menu-content-background"
          :simple-page="true"
          :page-data="pageData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </section>
      <div class="arrow-box">
        <double-arrow></double-arrow>
      </div>
      <section class="right-content">
        <div class="content-title">
          <p class="base-text-color">
            <span>截止时间:</span>
            <DatePicker
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择截止时间"
              style="width: 200px; margin-left: 10px"
              :value="dateTime"
              @on-change="dateTime = $event"
              :options="options"
            ></DatePicker>
          </p>

          <div class="base-text-color">
            已关联设备（<span class="font-red">{{ chooseTableData.length }}</span
            >）
            <Button type="primary" @click="removeAllDevice">全部移除</Button>
          </div>
        </div>
        <ui-table
          class="ui-table"
          :table-columns="rightTableColumns"
          :table-data="chooseTableData"
          :minus-height="minusTable"
          :special-jsx="specialJSX"
          @selectTable="selectTable"
        >
          <template slot="deviceId" slot-scope="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
          <template #actionSlot="{ row, index }">
            <i class="icon-font f-14 icon-yichu-01 icon-operation-color mr-sm" @click="removeOneDevice(row, index)"></i>
          </template>
        </ui-table>
      </section>
    </div>
    <template #footer>
      <Button class="plr-30" @click="visible = false">取 消</Button>
      <Button :loading="isSubmitLoading" type="primary" class="plr-30" @click="submit()">确 定</Button>
    </template>
  </ui-modal>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';

import { mapGetters, mapActions } from 'vuex';
import global from '@/util/global';
const tableColumns = [
  { type: 'selection', width: 50, align: 'center' },
  { title: '序号', width: 70, type: 'index', align: 'center' },
  {
    title: `${global.filedEnum.deviceId}`,
    width: 180,
    slot: 'deviceId',
    align: 'left',
  },
  { title: `${global.filedEnum.deviceName}`, key: 'deviceName', align: 'left' },
  { title: '组织机构', key: 'orgName', align: 'left' },
  { title: '安装地址', key: 'address', align: 'left', tooltip: true },
];
const rightTableColumns = [
  { title: '序号', width: 70, type: 'index', align: 'center' },
  {
    title: `${global.filedEnum.deviceId}`,
    width: 180,
    slot: 'deviceId',
    align: 'left',
  },
  { title: `${global.filedEnum.deviceName}`, key: 'deviceName', align: 'left' },
  { title: '组织机构', key: 'orgName', align: 'left' },
  { title: '安装地址', key: 'address', align: 'left', tooltip: true },
  { title: '操作', key: 'action', slot: 'actionSlot' },
];
let specialJSX = `<span class="font-active-color f-14">已选择全部数据</span>`;
export default {
  data() {
    return {
      keyword: '',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      tableColumns: Object.freeze(tableColumns),
      rightTableColumns: Object.freeze(rightTableColumns),
      tableData: [],
      chooseTableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      visible: false,
      styles: {
        width: '1800px',
      },
      loading: false,
      minusTable: 400,
      searchData: {
        keyWord: '',
        orgCode: null,
        pageNumber: 1,
        pageSize: 20,
      },
      connectingOptions: {
        width: '1.2%',
        height: '0.04rem',
        top: '50%',
        left: '13.6%',
      },
      allDeviceChecked: false,
      specialJSX: null,
      dateTime: '',
      isSubmitLoading: false,
      options: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 8.64e7;
        },
      },
      currentNodeKey: '',
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
    }),
    submit() {
      if (this.dateTime) {
        this.$emit(
          'addDeviceToCategory',
          this.chooseTableData,
          this.allDeviceChecked,
          this.searchData.orgCode,
          this.dateTime,
        );
        this.isSubmitLoading = true;
      } else {
        this.$Message.error('请选择截止时间');
      }
    },
    checkAll(val) {
      if (val) {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', true);
          this.$set(item, '_disabled', true);
        });
        this.chooseTableData = [];
        this.specialJSX = specialJSX;
      } else {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', false);
          this.$set(item, '_disabled', false);
        });
        this.specialJSX = null;
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    removeOneDevice(row, index) {
      this.chooseTableData.splice(index, 1);
      let deleteIndex = this.tableData.findIndex((item) => {
        return item.id === row.id;
      });
      this.tableData[deleteIndex]._checked = false;
      console.log(this.$refs.table);
    },
    removeAllDevice() {
      this.chooseTableData = [];
      this.tableData.forEach((item) => {
        item._checked = false;
        this.$set(item, '_disabled', false);
      });
      this.allDeviceChecked = false;
    },
    selectLeftTable(selection) {
      this.chooseTableData = selection;
      selection.forEach((row) => {
        let roww = this.tableData.find((item) => {
          return item.id === row.id;
        });
        roww._checked = true;
      });
    },
    handleData() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      if (this.allDeviceChecked) {
        this.tableData.forEach((item) => {
          this.$set(item, '_checked', true);
          this.$set(item, '_disabled', true);
        });
        this.chooseTableData = [];
        this.specialJSX = specialJSX;
      } else {
        this.tableData.forEach((row) => {
          // if (selectDataObject.hasOwnProperty(row.id)) {
          if (Object.prototype.hasOwnProperty.call(selectDataObject, row.id)) {
            this.$set(row, '_checked', true);
          } else {
            this.$set(row, '_checked', false);
          }
        });
      }
    },
    oneSelected(selection, row) {
      this.chooseTableData.push(row);
      row._checked = true;
    },
    cancelSelectTable(selection, row) {
      let rowId = this.tableData.find((item) => {
        return item.id === row.id;
      });
      this.chooseTableData = this.chooseTableData.filter((item) => item.id !== rowId.id);
    },
    onSelectAllTable() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        // 没有就push进去
        if (!selectDataObject.hasOwnProperty(row.id)) {
          this.chooseTableData.push(row);
        }
      });
    },
    cacelAllSelectTable() {
      let tableDataIds = this.tableData.map((item) => {
        return item.id;
      });
      this.chooseTableData = this.chooseTableData.filter((row) => {
        return !tableDataIds.includes(row.id);
      });
    },
    selectTable(selection) {
      this.checkedData = selection.map((row) => {
        return row.id;
      });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.pageNumber = val;
      this.initAllData();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.pageSize = val;
      this.initAllData();
    },
    selectTree(data) {
      this.searchData.orgCode = data.orgCode;
      this.initAllData();
    },
    searchkeyword() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initAllData();
    },
    // 查询设备数据列表
    async initAllData() {
      this.loading = true;
      this.tableData = [];
      try {
        let res = await this.$http.post(equipmentassets.getNotInwhitePageList, this.searchData);
        this.tableData = res.data.data.entities;
        this.handleData();
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        await this.setOrganizationList();
        this.selectTree(this.getDefaultSelectedOrg);
        this.currentNodeKey = this.getDefaultSelectedOrg.orgCode;
      } else {
        this.currentNodeKey = '';
        this.allDeviceChecked = false;
        this.isSubmitLoading = false;
        this.dateTime = '';
        this.searchData = {
          keyWord: '',
          orgCode: null,
          pageNumber: 1,
          pageSize: 20,
          checkStatuses: [],
        };
        this.pageData = {
          pageNum: 1,
          pageSize: 20,
          totalCount: 0,
        };
        this.chooseTableData = [];
      }
    },
    choosedCategory() {
      //console.log('我选中的目录需要关联设备', val)
    },
  },
  computed: {
    isView() {
      return this.modalAction.action === 'view';
    },
    isAdd() {
      return this.modalAction.action === 'add';
    },
    isEdit() {
      return this.modalAction.action === 'edit';
    },
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    modalAction: {
      default: () => {
        return { title: '关联设备', action: 'add' };
      },
    },
    choosedCategory: {
      default: () => {},
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DoubleArrow: require('@/components/double-arrow.vue').default,
  },
};
</script>
<style lang="less" scoped>
.model-wrapper {
  display: flex;
  background-color: #0e2246;
  .content-title {
    height: 65px;
    line-height: 65px;
    padding-left: 20px;
  }
  .left-content {
    width: 350px;
    border: 1px solid #0068b7;
    border-right-color: transparent;
  }
  .middle-content,
  .right-content {
    width: 715px;
    position: relative;
    border: 1px solid #0068b7;
  }
  .middle-content {
    .search-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .search-input {
      position: relative;
      display: flex;
      .icon-sousuo {
        position: absolute;
        right: 10px;
      }
    }
    .ui-table {
      width: 100%;
    }
  }
  .right-content {
    .content-title {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
    }
  }
  .padding20 {
    padding: 0 20px;
  }
  .font-green-color {
    color: var(--color-bluish-green-text);
  }
  .arrow-box {
    height: 600px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
}
</style>
