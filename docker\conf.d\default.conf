server {
    listen    80;
    #server_name  www.server.com;

    client_max_body_size       50m;

    location / {
       root   /web/html;
       index  index.html index.htm;
       try_files $uri $uri/ /index.html;
    }

    error_page  500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # location ~ ^/api/ {
    #     proxy_pass http://qsdi-gateway.service:8888;
    #     proxy_set_header X-Real-IP $remote_addr;
    #     proxy_read_timeout 600;
    #     proxy_set_header Host $host;
    #     proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    # }

    location  /iras {
        proxy_pass http://qsdi-gateway.service:8888;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_read_timeout 600;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location  /qsdi {
        proxy_pass http://qsdi-gateway.service:8888;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_read_timeout 600;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location  /datasources-management-service/ {
        proxy_pass http://qsdi-gateway.service:8888;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_read_timeout 600;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location  /ipbd {
        proxy_pass http://qsdi-gateway.service:8888;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_read_timeout 600;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # location ^~/node/ {
    #     rewrite ^/node/(.*)/static-resource/proxy /api/v1/static-resource/proxy break;
    #     proxy_pass http://qsdi-gateway.service:8888/;
    # }
    # location ^~/api/v1/ {
    #     rewrite ^/api/v1/   / break;
    #     proxy_pass http://qsdi-gateway.service:8888/;
    # }
    # location /fileupload/data {
    #     root   /web/html;
    #     try_files $uri $uri/ /fileupload/;
    # }
}
