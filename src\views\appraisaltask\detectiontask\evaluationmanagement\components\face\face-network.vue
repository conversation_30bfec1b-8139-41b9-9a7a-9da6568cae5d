<template>
  <ui-modal class="face-network" v-model="visible" v-if="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <!--  -->
    <div class="content auto-fill" v-if="Object.keys(indexList).length">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button>
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="检测数据列表"></line-title>
        <div class="search-wrapper">
          <ui-label class="fl" label="组织机构" :width="70">
            <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择">
            </api-organization-tree>
          </ui-label>
          <ui-label class="fl ml-lg" label="关 键 词" :width="60">
            <Input class="width-lg" placeholder="请输入设备编码/名称/经纬度/地址" v-model="searchData.keyword"></Input>
          </ui-label>
          <ui-label class="fl ml-lg" :label="global.filedEnum.sbdwlx" :width="100">
            <Select
              v-model="searchData.sbdwlx"
              clearable
              :placeholder="`请选择${global.filedEnum.sbdwlx}`"
              class="input-width"
            >
              <Option v-for="(item, index) in propertySearch_sbdwlx" :key="index" :value="item.dataKey">{{
                item.dataValue
              }}</Option>
            </Select>
          </ui-label>
          <ui-label class="fl ml-lg" :label="global.filedEnum.phyStatus" :width="70">
            <Select v-model="searchData.deviceStatus" clearable placeholder="请选择" class="input-width">
              <Option :value="1">已联网</Option>
              <Option :value="2">未联网</Option>
            </Select>
          </ui-label>
          <ui-label :width="70" class="fl" label=" ">
            <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
            <Button class="mr-sm" @click="resetInitial"> 重置 </Button>
          </ui-label>
        </div>
        <div></div>
        <div class="list auto-fill">
          <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
            <template #timeDifference="{ row }">
              <span :class="row.deviceStatus === '1' ? 'sucess' : 'error'">{{ row.timeDifference }}</span>
            </template>
            <template #deviceStatusText="{ row }">
              <span :class="row.deviceStatus === '1' ? 'sucess' : 'error'">{{ row.deviceStatusText }}</span>
            </template>
            <template #option="{ row }">
              <span class="btn-text-default" @click="checkReason(row)">不合格原因</span>
            </template>
          </ui-table>
          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
      <nonconformance
        ref="nonconformance"
        title="查看不合格原因"
        :tableColumns="reasonTableColumns"
        :tableData="reasonTableData"
        :reasonPage="reasonPage"
        :reasonLoading="reasonLoading"
        @handlePageChange="handlePageChange"
        @handlePageSizeChange="handlePageSizeChange"
      ></nonconformance>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.face-network {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 0 20px;
    }
  }
  .no-box {
    width: 1726px;
    min-height: 860px;
    max-height: 860px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 860px;
    max-height: 860px;
    border-radius: 4px;
    position: relative;
    .container {
      @{_deep}.tabs {
        .up {
          top: 2px;
        }
      }
      .list_item {
        width: calc(100% - 70px);
        height: 50px !important;
        line-height: 50px !important;
      }
      .title_text {
        width: 100%;
        display: inline-block;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .sucess {
      color: @color-success;
    }
    .error {
      color: @color-failed;
    }
    .list {
      margin-top: 10px;
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }

    .btn-text-default {
      cursor: pointer;
      font-size: 14px;
      color: var(--color-primary);
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }
    .label-title {
      @{_deep}.label {
        height: 50px !important;
        line-height: 50px !important;
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import user from '@/config/api/user';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      selectOrgTree: {
        orgCode: '',
      },
      exportLoading: false,
      echartRing: {},
      ringStyle: { width: '750px', height: '180px' },
      zdryChartObj: {
        xAxisData: ['已联网设备', '未联网设备'],
        showData: [
          { name: '已联网设备', value: 0 },
          { name: '未联网设备', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formatData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '人脸卡口联网率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: `${this.global.filedEnum.deviceId}`, key: 'deviceId' },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
        { title: '组织机构', key: 'orgName' },
        { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
        { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr' },
        { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
        { title: '近X天抓拍数量', key: 'imageNum' },
        // { title: '回传时间', key: 'timeDifference', slot: 'timeDifference' },
        {
          title: '设备联网状态',
          key: 'deviceStatusText',
          slot: 'deviceStatusText',
        },
        // { title: "操作", slot: "option", fixed: "right" },
      ],
      tableData: [],
      minusTable: 580,
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 70, title: '序号' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      searchData: {
        keyword: '',
        sbdwlx: '',
        orgCode: '',
        deviceStatus: '',
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: { width: '9rem' },
      visible: true,
      loading: false,
      deviceInfoId: '',

      indexList: {},
      reasonLoading: false,
      dicDataEnum: Object.freeze({
        propertySearch_sbdwlx: 'propertySearch_sbdwlx',
      }),
      propertySearch_sbdwlx: [],
    };
  },
  async mounted() {
    await this.getTableData();
    await this.getChartsData(); // 获取图表数据
    await this.initRing();
    await this.initDicContent();
  },

  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        orgCode: this.searchData.orgCode,
        keyword: this.searchData.keyword,
        sbdwlx: this.searchData.sbdwlx,
        indexName: this.$parent.row.indexName,
        total: this.searchData.totalCount,
        deviceStatus: this.searchData.deviceStatus,
      };
      try {
        let res = await this.$http.post(governanceevaluation.netWorkExport, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    search() {
      this.searchData.pageNum = 1;
      this.getTableData();
    },
    async initDicContent() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));

        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = data.data.find((row) => {
            return !!row[key];
          });
          this[this.dicDataEnum[key]] = obj[key];
        });
      } catch (err) {
        console.log(err);
      }
    },
    resetInitial() {
      this.selectOrgTree.orgCode = '';
      this.searchData.keyword = '';
      this.searchData.sbdwlx = '';
      this.searchData.deviceStatus = '';
      this.searchData.orgCode = '';
      this.searchData.pageNum = 1;
      this.getTableData();
    },

    async getTableData() {
      try {
        this.loading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          orgCode: this.searchData.orgCode,
          keyword: this.searchData.keyword,
          sbdwlx: this.searchData.sbdwlx,
          deviceStatus: this.searchData.deviceStatus,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        let res = await this.$http.post(governanceevaluation.netWorkPageList, params);
        const datas = res.data.data;
        this.searchData.totalCount = datas.total;
        this.tableData = datas.entities;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },

    async getChartsData() {
      try {
        let data = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(governanceevaluation.queryEvaluationBasicRecord, data);
        console.log(res, 'res');
        // if (!res.data.data) return;
        this.indexList = res.data.data;
        this.moduleData.rateValue = this.indexList.resultValue; //率
        this.moduleData.priceValue = this.indexList.standardsValue; //达标值
        this.moduleData.resultValue = this.indexList.qualifiedDesc; //考核结果
      } catch (err) {
        console.log(err);
      }
    },

    initRing() {
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '已联网设备') {
          item.value = this.indexList.accuracyFieldsQualifiedCount;
        } else {
          item.value = this.indexList.accuracyFieldsSumCount - this.indexList.accuracyFieldsQualifiedCount;
        }
      });

      this.zdryChartObj.count = this.indexList.accuracyFieldsSumCount;
      let formatData = {
        seriesName: '检测设备总量',
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    async getReason() {
      try {
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          deviceInfoId: this.deviceInfoId,
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        this.reasonLoading = true;
        let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    checkReason(row) {
      this.deviceInfoId = row.id;
      this.getReason(row);
      this.$refs.nonconformance.init();
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
