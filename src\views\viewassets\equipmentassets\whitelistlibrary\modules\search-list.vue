<template>
  <div class="search-list">
    <div>
      <ui-label class="inline mr-lg mb-lg" label="关键词" :width="55">
        <Input v-model="searchData.keyWord" class="input-width" placeholder="请输入设备名称/编码/IP/MAC"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="数据来源" :width="65">
        <Select class="width-md" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
          <Option v-for="(item, index) in dictData['sourceList']" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.sbdwlx" :width="100">
        <Select
          class="input-width"
          v-model="searchData.sbdwlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}</Option
          >
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.sbgnlx" :width="115">
        <Select
          class="input-width"
          v-model="searchData.sbgnlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
        >
          <Option v-for="(item, index) in dictData['sxjgnlx_receive']" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="检测状态" :width="70">
        <Select
          class="input-width"
          multiple
          :max-tag-count="1"
          v-model="searchData.checkStatuses"
          placeholder="请选择检测状态"
          clearable
        >
          <Option v-for="(item, index) in dictData['checkStatus']" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.phyStatus" :width="70">
        <Select
          class="width-md"
          v-model="searchData.phyStatus"
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in dictData['phystatusList']" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="截止时间" :width="70">
        <DatePicker
          type="datetime"
          class="width-md"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择截止时间"
          :value="dateTime"
          :options="options"
          @on-change="changeTime"
          @on-clear="clear"
        ></DatePicker>
      </ui-label>
      <div class="inline mb-lg">
        <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
        <Button type="default" @click="reset"> 重置 </Button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'search-list',
  props: {
    dictData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        keyWord: '',
        sourceId: '',
        sbdwlx: '',
        sbgnlx: '',
        checkStatuses: [],
        phyStatus: '',
        pageNumber: 1,
        pageSize: 20,
      },
      dateTime: '',
      options: {
        disabledDate(date) {
          return date.getTime() < Date.now() - 8.64e7;
        },
      },
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    search() {
      this.$emit('startSearch', [this.searchData]);
    },
    reset() {
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('startSearch', [this.searchData]);
      });
    },
    changeTime(val) {
      this.$emit('changeTime', val);
    },
    clear() {
      this.$emit('changeTime', '');
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.input-width {
  width: 200px;
}
.align-flex {
  display: flex;
  align-items: center;
}
.data-list {
  .align-flex;
  color: var(--color-content);
  font-size: 14px;
  .ui-select-tabs {
    flex: 1;
  }
}
</style>
