<!-- 数量达标率 -->
<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      @on-change-code="onChangeOrgRegion"
    >
      <tag-view
        slot="mid"
        :list="reallyTagList"
        :default-active="defaultActive"
        @tagChange="changeStatus"
        ref="tagView"
        class="tag-view"
      ></tag-view>
    </result-title>
    <component :is="componentName" v-bind="handleProps()" @viewDetail="viewDetail">
      <template v-if="componentName === 'StatisticalResult'" #customBtn="{ row, btnInfo }">
        <template v-for="(btnItem, btnIndex) in btnInfo.btnArr">
          <ui-btn-tip
            :key="btnIndex"
            v-if="btnItem.type === 'detail'"
            class="mr-sm"
            :icon="btnItem.icon"
            :content="btnItem.text"
            :disabled="row.dataType === '3'"
            @handleClick="viewDetail(row)"
          ></ui-btn-tip>
        </template>
      </template>
    </component>
    <district-detail v-model="districtDetailVisible" :detail-obj="detailObj"></district-detail>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'VideoGenralOsdAccuracy',
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalEcharts',
      iconList: [],
      tagList: Object.freeze([
        {
          label: '统计图表',
          value: 'StatisticalEcharts',
        },
        {
          label: '统计列表',
          value: 'StatisticalResult',
        },
      ]),
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
      districtDetailVisible: false,
      detailObj: {},
    };
  },
  created() {
    this.reallyTagList = this.tagList.map((item) => item.label);
    this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    changeStatus(index) {
      this.defaultActive = index;
      this.componentName = this.tagList[index].value;
    },
    // 动态组件 - 动态传参
    handleProps() {
      let props = {
        activeIndexItem: this.activeIndexItem,
      };
      return props;
    },
    viewDetail(row) {
      this.detailObj = row;
      this.districtDetailVisible = true;
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
    }),
  },
  watch: {},
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ResultTitle: require('../../components/result-title.vue').default,
    StatisticalEcharts: require('./statistical-echarts.vue').default,
    StatisticalResult: require('./statistical-results.vue').default,
    DistrictDetail: require('./components/district-detail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
}
</style>
