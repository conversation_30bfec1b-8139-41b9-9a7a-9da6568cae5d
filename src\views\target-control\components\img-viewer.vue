<template>
  <div class="imgViewer">
    <img class="default" :src="src" v-viewer="{inline: true}" alt="" />
  </div>
</template>
<script>


export default {
  components: {
   
  },
  props: {
    src: {
      type: String,
      default: ''
    },
  },
  
  data() {
    return {
      multiples: 1,
    }
  },
  
  methods: {
   
  }
}
</script>
<style lang="less" scoped>
.imgViewer {
  height: 620px;
  .default {
    opacity: 0;
    max-width: 100%;
    max-height: 620px;
  }
  /deep/ .viewer-backdrop {
    background-color: #fff;
  }

  /deep/ .viewer-title {
    opacity: 0;
  }
}
</style>
