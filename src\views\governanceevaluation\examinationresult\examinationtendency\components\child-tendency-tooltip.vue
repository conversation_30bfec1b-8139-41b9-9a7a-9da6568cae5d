<template>
  <div class="child-tendency-tooltip">
    <div class="tooltip-header flex-row">
      <div class="f-14">{{ month }}</div>
      <div>全省排名：{{ dataItem?.rank }}</div>
    </div>
    <div class="devider-line"></div>
    <p class="mb-sm">总分：{{ dataItem?.score }}</p>
    <div class="flex-row mb-sm" v-for="(item, index) in showKeys" :key="index">
      <div class="flex-aic">
        <span class="color-box mr-xs" :style="{ background: colors[index] }"></span>
        <span>{{ item?.name }}</span>
      </div>
      <span
        v-if="
          dataItem && typeof dataItem[item?.code] === 'number' && (dataItem[item?.code] || dataItem[item?.code] === 0)
        "
        >{{ dataItem[item?.code] }}</span
      >
      <span v-else> - </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'child-tendency-tooltip',
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.child-tendency-tooltip {
  // background: var(--bg-tooltip);
  color: var(--color-content);
  min-width: 300px;
  padding: 10px;
  font-size: 12px;
  max-height: 360px;
  overflow: auto;
  .devider-line {
    background: var(--devider-line);
    height: 1px;
    margin: 10px 0;
  }
  .color-box {
    height: 12px;
    width: 12px;
  }
}
</style>
