<template>
  <div class="container" :class="getFullscreen ? 'full-screen-container' : ''" @click="setScreenFull">
    <i class="icon-font f-12 icon" :class="getIcon"></i>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'full-screen',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
    getIcon() {
      if (this.getFullscreen) {
        return 'icon-tuichuquanping';
      } else {
        return 'icon-ivdg-quanping';
      }
    },
  },
  watch: {},
  filter: {},
  mounted() {
    this.DetectFullscreenChange();
  },
  methods: {
    ...mapActions({
      setFullscreen: 'home/setFullscreen',
    }),
    DetectFullscreenChange() {
      if (screenfull.isEnabled) {
        screenfull.on('change', () => {
          if (screenfull.isFullscreen) {
            this.setFullscreen(true);
          } else {
            this.setFullscreen(false);
          }
        });
      }
    },
    setScreenFull() {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit();
        this.setFullscreen(false);
      } else {
        screenfull.toggle(this.$parent.$el);
        this.setFullscreen(true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  position: absolute;
  left: 74%;
  top: 10px;
  .icon {
    line-height: 12px;
    color: #1f88a2;
    border: 1px solid #1f88a2;
    padding: 5px;
  }
}
.full-screen-container {
  left: 98%;
}
</style>
