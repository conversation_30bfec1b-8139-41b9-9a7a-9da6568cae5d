<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics></index-statistics>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem" :lengName="lengName">
          <template #rank-title> 按稳定性排名 </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart" :activeIndexItem="activeIndexItem" :columns="columns"></line-chart>
    </div>
  </div>
</template>

<script>
export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChart: require('./components/line-chart').default,
  },
  data() {
    return {
      listyle: {
        height: '0.53rem',
        width: '2.006rem',
      },
      lengName: [
        {
          name: '去年同期抓拍数量',
          key: 'unqualifiedNum',
          color: '#4BC3D1',
        },
        {
          name: '本月抓拍数量',
          key: 'qualifiedNum',
          color: '#17A7DF',
        },
      ],
      columns: [
        {
          key: '',
          title: '',
          renderTitle: (item) => {
            return `${this.formatDateTime(item.data.startTime)}`;
          },
        },
        {
          key: 'value',
          title: this.activeIndexItem.indexName,
          render: (item) => {
            return `${item.data.value}%`;
          },
        },
        {
          key: 'actualNum',
          title: '去年同期抓拍数量',
        },
        {
          key: 'qualifiedNum',
          title: '本月抓拍数量',
        },
      ],
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) {
        return '';
      }
      let dateObject = new Date(dateTime);
      let year = dateObject.getFullYear();
      let month = dateObject.getMonth() + 1 < 10 ? '0' + (dateObject.getMonth() + 1) : dateObject.getMonth() + 1;
      let date = dateObject.getDate() < 10 ? `0${dateObject.getDate()}` : dateObject.getDate();
      let hours = dateObject.getHours() < 10 ? `0${dateObject.getHours()}` : dateObject.getHours();
      let minutes = dateObject.getMinutes() < 10 ? `0${dateObject.getMinutes()}` : dateObject.getMinutes();
      let seconds = dateObject.getSeconds() < 10 ? `0${dateObject.getSeconds()}` : dateObject.getSeconds();
      return `${year}年${month}月${date}日 ${hours}:${minutes}:${seconds}`;
    },
    tooltipFormatter(params) {
      let { name } = params[0];
      let formatter = `<span class="icon-font icon-dingwei1 f-14 vt-middle mr-xs f-14" style="background: var(--color-primary);-webkit-background-clip: text;-webkit-text-fill-color: transparent;"></span>${name}<br>`;
      params.forEach((item) => {
        let icon = `<span style="display: inline-block;width: 8px;height: 8px; margin-left: 20px; background-color: ${item.color};color: ${item.color}" class="vt-middle mr-xs"></span>`;
        formatter += `${icon}<span class="f-12">${item.seriesName}  ${item.value}${
          item.seriesName.includes('率') ? '%' : ''
        }<br></span>`;
      });
      return formatter;
    },
  },
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: 100%;
      @{_deep} .index-statistics .information-statistics .statistics-ul li {
        width: calc((100% / 3) - 7.8px) !important;
        &:nth-child(3n + 0) {
          margin-right: 0 !important;
        }
      }
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
