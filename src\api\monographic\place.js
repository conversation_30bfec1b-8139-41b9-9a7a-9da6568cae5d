import request from "@/libs/request";
import { monographic } from "../Microservice";
import { place as domainName, getRouteDomain } from "./base";

// 场所详情附带关注度
export function queryPlacesAndAttention(data) {
  return request({
    url: monographic + domainName + "/statistics/queryPlacesAndAttention",
    method: "post",
    data
  });
}

// 场所分类统计
export function queryPlaceCategory() {
  return request({
    url: monographic + domainName + "/statistics/queryPlaceCategory",
    method: "post",
  });
}

// 场所事件积分排名
export function queryEventScore(data) {
  return request({
    url: monographic + domainName + "/statistics/queryEventScore",
    method: "post",
    params: data
  });
}

// 场所事件统计分析
export function queryTodayStatistic(data) {
  return request({
    url: monographic + domainName + "/statistics/queryTodayStatistic",
    method: "post",
    data
  });
}

// 查询场所专题配置信息
export function getPlaceConfig(data) {
  return request({
    url: monographic + domainName + "/sysConfig/getPlaceConfig",
    method: "post",
    data
  });
}

// 修改场所专题配置信息
export function updatePlaceConfig(data) {
  return request({
    url: monographic + domainName + "/sysConfig/updatePlaceConfig",
    method: "post",
    data
  });
}

// 场所异常行为预警列表分页查询
export function abnormalBehaviorPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/abnormalBehaviorPageList",
    method: "post",
    data,
  });
}

// 疑似重点人聚集场所分页查询
export function queryEmphasisPersonGatheredPlace(data) {
  return request({
    url: monographic + domainName + "/alarm/queryEmphasisPersonGatheredPlace",
    method: "post",
    data,
  });
}

// 疑似从业人员
export function queryPeronWorkPlace(data) {
  return request({
    url: monographic + domainName + "/alarm/queryPeronWorkPlace",
    method: "post",
    data,
  });
}

// 疑似从业人员详情
export function queryPeronWorkPlaceDetail(data) {
  return request({
    url: monographic + domainName + "/alarm/queryPeronWorkPlaceDetail",
    method: "post",
    data,
  });
}

// 场所人员统计
export function queryPersonStatistics(data) {
  return request({
    url: monographic + domainName + "/statistics/queryPersonStatistics",
    method: "post",
    data,
  });
}

// 疑似重点人聚集场所报详情
export function queryEmphasisPersonGatheredPlaceAlarmDetail(data) {
  return request({
    url: monographic + domainName + "/alarm/queryEmphasisPersonGatheredPlaceAlarmDetail",
    method: "post",
    data,
  });
}

// 疑似重点人聚集场所报人员抓拍详情
export function queryEmphasisPersonGatheredPlaceCaptureDetail(data) {
  return request({
    url: monographic + domainName + "/alarm/queryEmphasisPersonGatheredPlaceCaptureDetail",
    method: "post",
    data,
  });
}