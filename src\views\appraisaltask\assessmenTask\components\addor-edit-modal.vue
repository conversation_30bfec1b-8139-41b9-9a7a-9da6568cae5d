<template>
  <div class="addor-modal">
    <ui-modal
      v-model="visible"
      :styles="styles"
      :title="title"
      :loading="saveModalLoading"
      @onCancel="visible = false"
      @query="handleSubmit('formData')"
    >
      <Form ref="formData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="135">
        <FormItem label="任务名称" prop="taskName">
          <Input
            type="text"
            v-model="formData.taskName"
            placeholder="请输入任务名称"
            class="width-input"
            :maxlength="50"
          ></Input>
        </FormItem>
        <FormItem label="考核类型" prop="examCycleType">
          <RadioGroup v-model="formData.examCycleType">
            <Radio label="1">月考核</Radio>
            <Radio label="2">周考核</Radio>
          </RadioGroup>
          <span class="vt-middle tips">备注：月初（06:00）自动考核上一个月的数量质量情况</span>
        </FormItem>
        <FormItem label="是否自动输出每日考核成绩">
          <RadioGroup v-model="formData.examMarkType">
            <Radio label="1">是</Radio>
            <Radio label="0">否</Radio>
          </RadioGroup>
          <span class="vt-middle tips">备注：当日（06:00）自动考核昨天的数量质量情况</span>
        </FormItem>
        <FormItem label="考核方案" prop="schemeId">
          <Select
            class="width-input"
            placeholder="请选择考核方案"
            filterable
            transfer
            v-model="formData.schemeId"
            @on-change="setSchemeId"
          >
            <Option v-for="(item, index) in moduleList" :key="index" :value="item.id">{{ item.schemeName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="关联检测任务" prop="evaluationTaskSchemeId">
          <Select
            class="width-input"
            placeholder="请选择关联检测任务"
            filterable
            transfer
            v-model="formData.evaluationTaskSchemeId"
          >
            <Option
              v-for="(item, index) in evaluationTaskList"
              :key="index"
              :value="item.taskSchemeVo.taskSchemeId"
              @click.native="changeTask(item.indexVos)"
              >{{ item.taskSchemeVo.taskName }}</Option
            >
          </Select>
        </FormItem>
      </Form>
      <!-- 检测内容 -->
      <div class="check-contents">
        <ui-table
          v-if="tableData.length"
          :maxHeight="handleHeight"
          :stripe="false"
          :disabledHover="true"
          :loading="taskLoading"
          :table-columns="tableColumns"
          :table-data="tableData"
          :span-method="handleSpan"
        >
          <template #state="{ row }">
            <span :class="['icon-font', taskList.includes(row.indexType) ? 'icon-dabiao' : 'icon-budabiao']"></span>
          </template>
        </ui-table>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    moduleList: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      handleHeight: 400,
      taskLoading: false,
      visible: true,
      errorShow: false,
      saveModalLoading: false,
      taskList: [],
      taskContentList: [],
      evaluationTaskList: [], //检测任务
      formData: {
        id: '',
        examCycleType: '1',
        taskName: '',
        // orgCode: '',
        schemeId: '',
        evaluationTaskSchemeId: '',
        examMarkType: '0',
      },
      tableColumns: [
        {
          title: '考核内容',
          key: 'schemeContentName',
          tooltip: true,
          align: 'left',
          minWidth: 220,
        },
        {
          title: '检测对象',
          key: 'indexName',
          tooltip: true,
          align: 'left',
          minWidth: 220,
        },
        {
          title: '检测关联任务',
          key: 'state',
          slot: 'state',
          align: 'left',
          width: 120,
        },
      ],
      tableData: [],
      ruleCustom: {
        taskName: [
          {
            required: true,
            message: '请输入任务名称',
            trigger: 'blur',
          },
        ],
        examCycleType: [
          {
            required: true,
            message: '请选择考核类型',
            trigger: 'change',
          },
        ],
        evaluationTaskSchemeId: [
          {
            required: true,
            message: '请选择关联检测任务',
            trigger: 'change',
          },
        ],
        schemeId: [
          {
            required: true,
            message: '请选择考核方案',
            type: 'number',
            trigger: 'change',
          },
        ],
      },
      styles: {
        width: '4.2rem',
      },
    };
  },
  watch: {
    visible(val) {
      if (!val) {
        this.$emit('onClose');
      }
    },
  },
  methods: {
    async init(val) {
      this.$refs.formData.resetFields();
      this.tableData = [];
      this.evaluationTaskList = await this.getEvaluation();
      this.visible = true;
      if (val.id) {
        this.formData = {
          ...val,
        };
        this.getIndexDatas(val.list);
        let editTask = this.evaluationTaskList.find((item) => {
          return item.taskSchemeVo.taskSchemeId === val.evaluationTaskSchemeId;
        });
        this.taskList = editTask.indexVos.map((e) => {
          return e.indexType;
        });
        this.assembleData();
      }
    },
    // 检测任务列表
    async getEvaluation() {
      try {
        let res = await this.$http.get(governanceevaluation.queryEvaluationTaskData);
        let arr = res.data.data ? res.data.data : [];
        return arr;
      } catch (err) {
        console.log(err);
      }
    },
    // 选中任务
    changeTask(e) {
      this.taskList = [];
      this.taskList = e.map((e) => {
        return e.indexType;
      });
      this.assembleData();
    },
    // 计算合并的列数行数
    handleSpan({ row, columnIndex }) {
      if (columnIndex === 0) {
        let x = row.num == 0 ? 0 : row.num;
        let y = row.num == 0 ? 0 : 1;
        return [x, y];
      }
    },
    // 考核内容表格数据处理
    assembleData() {
      if (!this.formData.schemeId || !this.formData.evaluationTaskSchemeId) {
        return false;
      }
      let data = [];
      data = this.taskContentList;
      this.taskLoading = true;
      for (var i = 0; i < data.length; i++) {
        if (data[i].already !== 1) {
          if (data[i]) {
            data[i].num = 1;
            for (var a = i + 1; a < data.length; a++) {
              if (data[i].schemeContentName === data[a].schemeContentName) {
                data[i].num++;
                data[a].num = 0;
                data[a].already = 1;
              } else {
                break;
              }
            }
          }
        }
      }
      //将整理后的数据交给表格渲染
      this.tableData = data;
      this.taskLoading = false;
    },
    // 考核方案选中
    async setSchemeId(e) {
      this.formData.schemeId = e;
      this.getTaskContent(this.formData.schemeId);
      this.assembleData();
    },
    // 新增获取考核内容
    async getTaskContent(val) {
      if (!val) {
        return false;
      }
      try {
        let res = await this.$http.get(governanceevaluation.queryTaskContent, {
          params: { id: val },
        });
        let arr = res.data.data ? res.data.data : [];
        this.getIndexDatas(arr);
      } catch (err) {
        console.log(err);
      }
    },
    // 考核内容处理
    getIndexDatas(val) {
      let newArr = [];
      val.map((item) => {
        item.indexDatas.forEach((val) => {
          val.schemeContentName = item.schemeContentName;
          val.state = false;
          newArr.push(val);
        });
      });
      this.taskContentList = newArr;
    },
    // 确定提交
    handleSubmit(name) {
      this.$refs[name].validate(async (valid) => {
        if (valid) {
          try {
            this.saveModalLoading = true;
            let data = {
              jobId: this.formData.jobId,
              id: this.formData.id ? this.formData.id : '0',
              evaluationTaskSchemeId: this.formData.evaluationTaskSchemeId,
              taskName: this.formData.taskName,
              schemeId: this.formData.schemeId,
              examCycleType: this.formData.examCycleType,
              examMarkType: this.formData.examMarkType,
            };
            let res = await this.$http.post(
              this.title === '新增考核任务'
                ? governanceevaluation.addExamSchemeTask
                : governanceevaluation.updateEvaluationTask,
              data,
            );
            this.$Message.success(res.data.msg);
            this.visible = false;
            this.$emit('update');
          } catch (err) {
            console.log(err);
          } finally {
            this.saveModalLoading = false;
          }
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.tips {
  color: var(--color-tips);
}
.addor-modal {
  .width-input {
    width: 400px;
  }
  .width-time {
    flex: 1;
  }
  .width-task {
    width: 220px;
  }
  .form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
}
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
}
@{_deep}.ivu-modal-body {
  padding: 10px 50px !important;
}
.check-contents {
  margin-bottom: 20px;
  @{_deep}.ivu-table table {
    border-collapse: unset !important;
  }
  @{_deep}.ivu-table-header {
    width: auto;
    .ivu-table-column-left {
      .ivu-table-cell {
        padding: 0 15px !important;
      }
    }
    th {
      // border-bottom: 1px solid var(--border-color);
      // border-right: 1px solid var(--border-color);
      // &:first-child {
      //   border-left: none !important;
      // }
      // &:last-child {
      //   border-right: none !important;
      // }
      span {
        color: var(--color-table-header-th);
      }
      div {
        color: var(--color-table-header-th);
      }
    }
  }

  @{_deep}.ivu-table-tbody {
    table {
      border-collapse: 0 !important;
      border-spacing: 0;
    }
    .ivu-table-column-left {
      .ivu-table-cell {
        margin: 0 15px !important;
      }
    }
    background-color: var(--bg-sub-content) !important;
    tr {
      td {
        border-right: 1px solid var(--border-modal-footer);
        border-bottom: 1px solid var(--border-modal-footer);
        &:last-child {
          border-right: none !important;
        }
        span {
          color: var(--color-content);
        }
        div {
          color: var(--color-content);
        }
      }
    }
  }
  .icon-budabiao,
  .icon-dabiao {
    color: var(--color-failed) !important;
    font-size: 16px;
    line-height: 16px;
  }
  .icon-dabiao {
    color: var(--color-success) !important;
  }
}
</style>
