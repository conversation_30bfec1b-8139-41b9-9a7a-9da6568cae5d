<template>
  <!-- <div class="platform-stability-container">
    <tab-title class="tab-title" v-model="activeValue" :data="tabData">
      <i class="icon-font icon-fanhui" v-if="showBack" @click="showBack = false"></i>
    </tab-title>
  </div> -->
  <div class="body-container" v-ui-loading="{ loading, tableData: filterEvaluationIndexResultData }">
    <template v-if="!showBack">
      <div class="progress-wrapper">
        <ui-progress
          v-for="(item, index) in filterEvaluationIndexResultData"
          :key="index"
          :percent="item.resultValue || 0"
          :type="item.qualified === '1' ? 'default' : 'primary'"
          class="pointer"
          @click="goDetail(item)"
        >
          <span class="inline progress-name ellipsis f-12 mr-sm" @click.stop="$emit('on-index-detail', item)">{{
            `${item.indexName}`
          }}</span>
        </ui-progress>
      </div>
    </template>
    <template v-else>
      <self-drop-down
        :dropdown-list="dropdownList"
        :default-active-id="activedropId"
        @chooseOne="chooseOneIndex"
      ></self-drop-down>
      <draw-echarts
        class="charts"
        :echart-option="barEchartOptions"
        :echart-style="ringStyle"
        ref="barEchartRef"
        :echarts-loading="echartsLoading"
        @echartClick="echartClickJump"
      ></draw-echarts>
      <span class="next-echart">
        <i
          class="icon-font icon-zuojiantou1 f-12"
          @click="scrollRight('barEchartRef', barChartList, [], comprehensiveConfig.homeNum)"
        ></i>
      </span>
    </template>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import { platFormEnum } from '@/views/home/<USER>/platformEnum';
import dataZoom from '@/mixins/data-zoom';

export default {
  name: 'platform-stability',
  mixins: [dataZoom],
  components: {
    // TabTitle: require('@/views/home/<USER>/tab-title.vue').default,
    UiProgress: require('@/views/home/<USER>/ui-progress.vue').default,
    selfDropDown: require('@/views/home/<USER>/echarts-dom/self-drop-down.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    tableData: {},
    loading: {},
  },
  data() {
    return {
      tabData: [{ label: '平台稳定性', id: 'stability' }],
      activeValue: 'stability',
      evaluationIndexResultData: [],
      filterEvaluationIndexResultData: [],
      showBack: false,
      dropdownList: [],
      activedropId: null,
      barEchartOptions: {},
      barChartList: [],
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      echartsLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    tableData: {
      handler(val) {
        this.evaluationIndexResultData = val || [];
        if (val) {
          this.initList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    echartClickJump(params) {
      this.$emit('on-jump', params.data.data.originData);
    },
    initList() {
      this.filterEvaluationIndexResultData = this.evaluationIndexResultData.filter((item) => item.indexModule === '7');
      this.dropdownList = this.filterEvaluationIndexResultData.map((item) => {
        return {
          id: item.indexId,
          label: item.indexName,
        };
      });
    },
    chooseOneIndex(indexId) {
      this.activedropId = indexId;
      this.getBarInfo();
    },
    goDetail(item) {
      this.activedropId = item.indexId;
      this.getBarInfo();
      this.showBack = true;
    },
    // 柱状图获取
    async getBarInfo() {
      if (!this.activedropId) return;
      let isIndexIdItem = this.filterEvaluationIndexResultData.find((item) => item.indexId === this.activedropId) ?? {};
      this.echartsLoading = true;
      let params = {
        indexId: this.activedropId,
        batchId: isIndexIdItem?.batchId,
        access: 'REPORT_MODE',
        displayType: 'REGION',
        orgRegionCode: isIndexIdItem?.civilCode,
        sortField: 'REGION_CODE',
      };
      try {
        let { data } = await this.$http.post(governanceevaluation.getNumRankInfo, params);
        this.barChartList = data.data.map((item) => {
          return Object.assign({}, item?.detail ?? {}, item);
        });
        this.handleBarCharts(isIndexIdItem);
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleBarCharts(oneIndexItem) {
      let { batchId, indexType } =
        this.filterEvaluationIndexResultData.find((item) => item.indexId === this.activedropId) ?? {};
      let platFormObject = {};
      let originSeriesData = [];
      let platFormData = this.activedropId in platFormEnum ? platFormEnum[this.activedropId] : platFormEnum['default'];
      platFormData.forEach((item) => {
        platFormObject[item.key] = [];
        originSeriesData.push({
          type: 'bar',
          name: item.name,
          color: [item.color, item.color],
          data: platFormObject[item.key],
          yAxisIndex: 1,
        });
      });
      let xAxisData = [];
      let lineData = [];
      this.barChartList.forEach((item) => {
        let detailList = [];
        let qualityRate = item?.resultValue ?? 0;
        let detailData = {
          name: item.regionName,
          title: oneIndexItem.indexName,
          titleNum: `${qualityRate.toFixed(2)}%`,
          list: detailList,
          originData: {
            ...item,
            indexId: this.activedropId,
            batchId: batchId,
            indexType: indexType,
          },
        };
        platFormData.forEach((one) => {
          platFormObject[one.key].push({
            data: detailData,
            value: item[one.key] ?? 0,
          });
          detailList.push({
            label: one.name,
            color: one.color,
            num: item[one.key] ?? 0,
          });
        });
        xAxisData.push(item.regionName);
        lineData.push(qualityRate);
      });
      originSeriesData.push({
        type: 'line',
        data: lineData,
        yAxisIndex: 0,
      });
      this.barEchartOptions = this.$util.doEcharts.getHomeAreaBar({
        toolTipDom: TooltipDom,
        yAxisData: [
          {
            min: 0,
            max: 100,
            axisLabel: {
              //y轴文字的配置
              color: '#8ABAFB',
              formatter: '{value} %',
              fontSize: this.$util.common.fontSize(12),
            },
          },
          {
            min: 0,
            axisLabel: {
              //y轴文字的配置
              color: '#8ABAFB',
              formatter: '{value}',
              fontSize: this.$util.common.fontSize(12),
            },
          },
        ],
        series: originSeriesData,
        xAxisData: xAxisData,
      });
      setTimeout(() => {
        this.setDataZoom('barEchartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  position: relative;
  height: calc(100% - 40px);
  overflow-y: auto;
  overflow-x: hidden;
  .progress-name {
    color: #c0daff;
    width: 190px;
  }
  .progress-wrapper {
    padding: 10px;
    height: 100%;

    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
</style>
