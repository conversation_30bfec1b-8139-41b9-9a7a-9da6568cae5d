import request from "@/libs/request";
import { cloudSearch, manager, service, modelSearch } from "./Microservice";
// 上传图片至minio并返回url
export function uploadPicToMinio(data) {
  return request({
    url: cloudSearch + "/savePicToMinio",
    method: "post",
    data,
  });
}

// 图片结构化选取区域
export function picturePick(data) {
  return request({
    url: cloudSearch + "/getPicturePick",
    method: "post",
    data,
  });
}
// 图片路径转化为base64
export function getBase(data) {
  return request({
    url: cloudSearch + `/getBase64?url=${data}`,
    method: "post",
  });
}
// 首页统计总数(旧)
export function countDataTotal(data) {
  return request({
    url: cloudSearch + "/getDataTotal",
    method: "post",
    data,
  });
}
// 新
export function totalCount() {
  return request({
    url: modelSearch + "/fullTextSearch/totalCount",
    method: "get",
  });
}

// 我的云搜记录新增
export function addMyCloudSearch(data) {
  return request({
    url: manager + "/workbench/cloudSearch/addMyCloudSearch",
    method: "post",
    data,
  });
}
// 人像档案搜索
export function queryFaceRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryFaceRecordSearch",
    method: "post",
    data,
  });
}
// 人像高级搜索
export function faceRecordSearchPlus(data) {
  return request({
    url: cloudSearch + "/faceRecordSearchPlus",
    method: "post",
    data,
  });
}
// 车辆档案搜索
export function vehicleRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryVehicleRecordSearch",
    method: "post",
    data,
  });
}

// 车辆高级搜索
export function vehicleRecordSearchPlus(data) {
  return request({
    url: cloudSearch + "/vehicleRecordSearchPlus",
    method: "post",
    data,
  });
}
// 人体搜索
export function queryHumanRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryHumanRecordSearch",
    method: "post",
    data,
  });
}
// 非机动车搜索
export function queryNonmotorRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryNonmotorRecordSearch",
    method: "post",
    data,
  });
}

// FRID搜索
export function RFIDRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryRfidRecordSearch",
    method: "post",
    data,
  });
}

// 电围搜索
export function electricCircumferenceRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryElectricCircumferenceRecordSearch",
    method: "post",
    data,
  });
}

// 设备档案搜索
export function queryDeviceRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryDeviceRecordSearch",
    method: "post",
    data,
  });
}

// 设备分别统计总数
export function countDeviceDataTotal(data) {
  return request({
    url: cloudSearch + "/getDeviceDataTotal",
    method: "post",
    data,
  });
}

// 警务搜索_左边树加载
export function realRecordSearchTree(data) {
  return request({
    url: cloudSearch + "/queryRealRecordSearchTree",
    method: "post",
    data,
  });
}

// 警务搜索
export function realRecordSearch(data) {
  return request({
    url: cloudSearch + "/queryRealRecordSearch",
    method: "post",
    data,
  });
}

// 设备选择弹出框数据查询
// export function queryDeviceList(data) {
//   return request({
//     url: cloudSearch + '/queryDeviceList',
//     method: 'post',
//     data
//   })
// }
export function queryDeviceList(data) {
  return request({
    url: service + "/device/selectDeviceList",
    method: "post",
    data,
  });
}
/**
 * 以下高级搜索接口
 */
// 设备档案搜索
export function queryDeviceRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryDeviceRecordSearchEx",
    method: "post",
    data,
  });
}

// 电围搜索
export function electricCircumferenceRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryElectricCircumferenceRecordSearchEx",
    method: "post",
    data,
  });
}

// 人像档案搜索
export function queryFaceRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryFaceRecordSearchEx",
    method: "post",
    data,
  });
}
// 人体高级搜索
export function queryHumanRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryHumanRecordSearchEx",
    method: "post",
    data,
  });
}
// 非机动车高级搜索

// 警务搜索
export function realRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryRealRecordSearchEx",
    method: "post",
    data,
  });
}

// FRID搜索
export function RFIDRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryRfidRecordSearchEx",
    method: "post",
    data,
  });
}

// 车辆档案搜索
export function vehicleRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryVehicleRecordSearchEx",
    method: "post",
    data,
  });
}

// wifi搜索
export function wifiRecordSearchEx(data) {
  return request({
    url: cloudSearch + "Ex/queryWifiRecordSearchEx",
    method: "post",
    data,
  });
}

// gps搜索
export function gpsSearch(data) {
  return request({
    url: cloudSearch + "/gpsSearch",
    method: "post",
    data,
  });
}
// 查询静态库的表名 - 来源
export function getFaceLibNameByLibType(data) {
  return request({
    url: cloudSearch + "/getFaceLibNameByLibType",
    method: "post",
    data,
  });
}
//警务搜索_左边树懒加载
export function selectCatalog(data) {
  return request({
    url: cloudSearch + "/selectCatalog",
    method: "post",
    data,
  });
}

// 档案类型总数查询
export function getEveryTypeArchiveTotalApi() {
  return request({
    url: `${cloudSearch}/queryArchivesStatisticsList`,
    method: "post",
  });
}

// ES索引总数查询
export function getEsIndexStatisticsListApi() {
  return request({
    url: `${cloudSearch}/queryEsIndexStatisticsList`,
    method: "post",
  });
}

// //警务搜索列表加载
export function querySearch(id) {
  return request({
    url: cloudSearch + "/querySearch?resourceId=" + id,
    method: "post",
  });
}

// 警务详情
export function queryPoliceData(data) {
  return request({
    url: cloudSearch + "/queryPoliceData",
    method: "post",
    data,
  });
}

// 获取一个关联的摄像机,根据gbId
export function getRelationCameraByGbId(gbId) {
  return request({
    url: service + `/device/relation/getRelationCameraByGbId/${gbId}`,
    method: "get",
  });
}
// 人脸下载
export function faceDownload(data) {
  return request({
    url: modelSearch + "/search/face/download",
    method: "post",
    data,
  });
}
// 车辆下载
export function vehicleDownload(data) {
  return request({
    url: modelSearch + "/search/vehicle/download",
    method: "post",
    data,
  });
}
// 人体下载
export function humanDownload(data) {
  return request({
    url: modelSearch + "/search/human/download",
    method: "post",
    data,
  });
}
// 非机动车下载
export function nonMotorDownload(data) {
  return request({
    url: modelSearch + "/search/nonMotor/download",
    method: "post",
    data,
  });
}

// gps下载
export function gpsDownload(data) {
  return request({
    url: modelSearch + "/search/gps/download",
    method: "post",
    data,
  });
}
// wifi下载
export function wifiDownload(data) {
  return request({
    url: modelSearch + "/search/wifi/download",
    method: "post",
    data,
  });
}
// rfid下载
export function rfidDownload(data) {
  return request({
    url: modelSearch + "/search/rfid/download",
    method: "post",
    data,
  });
}
// electricContent下载
export function electricContentDownload(data) {
  return request({
    url: modelSearch + "/search/electricContent/download",
    method: "post",
    data,
  });
}

//查询下载状态
export function taskView(taskId) {
  return request({
    url: modelSearch + `/down/task/view/${taskId}`,
    method: "get",
  });
}

// 电围数据搜索
export function baseStationDataSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/baseStationDataSearch",
    method: "post",
    data,
  });
}
// 电围设备搜索
export function baseStationDeviceSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/baseStationDeviceSearch",
    method: "post",
    data,
  });
}
// etc数据搜索
export function etcDataSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/etcDataSearch",
    method: "post",
    data,
  });
}
// 视图解析库--融合下载
export function wholeDownload(data) {
  return request({
    url: modelSearch + "/search/fusion/download",
    method: "post",
    data,
  });
}
// 视图设备搜索
export function cameraDeviceSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/cameraDeviceSearch",
    method: "post",
    data,
  });
}
// 人脸抓拍搜索
export function faceCaptureSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/faceCaptureSearch",
    method: "post",
    data,
  });
}
// Gps数据搜索
export function gpsDataSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/gpsDataSearch",
    method: "post",
    data,
  });
}
// 人体抓拍搜索
export function humanCaptureSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/humanCaptureSearch",
    method: "post",
    data,
  });
}
// mac搜索
export function macSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/macSearch",
    method: "post",
    data,
  });
}
// 非机动车抓拍搜索
export function nonmotorCaptureSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/nonmotorCaptureSearch",
    method: "post",
    data,
  });
}
// rfid数据搜索
export function rfidDataSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/rfidDataSearch",
    method: "post",
    data,
  });
}
// gps设备搜索
export function gpsDeviceSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/gpsDeviceSearch",
    method: "post",
    data,
  });
}
// etc设备搜索
export function etcDeviceSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/etcDeviceSearch",
    method: "post",
    data,
  });
}
// rfid设备搜索
export function rfidDeviceSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/rfidDeviceSearch",
    method: "post",
    data,
  });
}
// 车辆抓拍搜索
export function vehicleCaptureSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/vehicleCaptureSearch",
    method: "post",
    data,
  });
}
// wifi设备搜索
export function wifiDeviceSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/wifiDeviceSearch",
    method: "post",
    data,
  });
}
// 人员报警
export function faceAlarmSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/faceAlarmSearch",
    method: "post",
    data,
  });
}
// 车辆报警
export function vehicleAlarmSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/vehicleAlarmSearch",
    method: "post",
    data,
  });
}
// wifi报警
export function wifiAlarmSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/wifiAlarmSearch",
    method: "post",
    data,
  });
}
// rfid报警
export function rfidAlarmSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/rfidAlarmSearch",
    method: "post",
    data,
  });
}
// 电围报警
export function baseStationAlarmSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/baseStationAlarmSearch",
    method: "post",
    data,
  });
}
// etc报警
export function etcAlarmSearch(data) {
  return request({
    url: modelSearch + "/fullTextSearch/etcAlarmSearch",
    method: "post",
    data,
  });
}

// 根据图片坐标截取图片base64
export function getBase64ByImageCoordinateAPI(data) {
  return request({
    url: cloudSearch + "/cutImageBase64",
    method: "post",
    data,
  });
}

// 视频档案搜索
export function videoArchiveSearch(data) {
  return request({
    url: `${modelSearch}/fullTextSearch/videoArchiveSearch`,
    method: "post",
    data,
  });
}

// 实名档案搜索
export function realNameArchiveSearch(data) {
  return request({
    url: `${modelSearch}/fullTextSearch/realNameArchiveSearch`,
    method: "post",
    data,
  });
}

// 一机一档搜索
export function vehicleArchiveSearch(data) {
  return request({
    url: `${modelSearch}/fullTextSearch/vehicleArchiveSearch`,
    method: "post",
    data,
  });
}

// 资源数据类
export function resourceSearch(data) {
  return request({
    url: `${modelSearch}/fullTextSearch/resourceSearch`,
    method: "post",
    data,
  });
}

// 场所档案搜索
export function placeSearch(data) {
  return request({
    url: `${modelSearch}/fullTextSearch/placeArchiveSearch`,
    method: "post",
    data,
  });
}

// ETC-高速出或入轨迹搜索
export function etcHighwayExitOrEntryVehicleTrack(data) {
  return request({
    url: modelSearch + "/CloudSearch/etc/etcHighwayExitOrEntryVehicleTrack",
    method: "post",
    data,
  });
}
// ETC-高速出行轨迹记录搜索
export function etcHighwayVehicleTrack(data) {
  return request({
    url: modelSearch + "/CloudSearch/etc/etcHighwayVehicleTrack",
    method: "post",
    data,
  });
}
// ETC-通行卡过车信息搜索
export function etcVehicleTrack(data) {
  return request({
    url: modelSearch + "/CloudSearch/etc/etcVehicleTrack",
    method: "post",
    data,
  });
}
// ETC-卡主身份信息搜索
export function etcVehicleTrackCardOwner(data) {
  return request({
    url: modelSearch + "/CloudSearch/etc/etcVehicleTrackCardOwner",
    method: "post",
    data,
  });
}
// ETC-高速出或入轨迹下载
export function etcHighwayExitOrEntryVehicleTrackDownload(data) {
  return request({
    url: modelSearch + "/search/etc/etcHighwayExitOrEntryVehicleTrackDownload",
    method: "post",
    data,
  });
}
// ETC-高速出行轨迹记录下载
export function etcHighwayVehicleTrackDownload(data) {
  return request({
    url: modelSearch + "/search/etc/etcHighwayVehicleTrackDownload",
    method: "post",
    data,
  });
}
// ETC-卡主身份信息下载
export function etcVehicleTrackCardOwnerDownload(data) {
  return request({
    url: modelSearch + "/search/etc/etcVehicleTrackCardOwnerDownload",
    method: "post",
    data,
  });
}
// ETC-通行卡过车信息下载
export function etcVehicleTrackDownload(data) {
  return request({
    url: modelSearch + "/search/etc/etcVehicleTrackDownload",
    method: "post",
    data,
  });
}

// 获取非机动车抓拍关联的信息
export function getNonmotorCaptureLink(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/getNonmotorCaptureLink",
    method: "post",
    data,
  });
}
// 获取车辆抓拍关联的信息
export function getVehicleCaptureLink(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/getVehicleCaptureLink",
    method: "post",
    data,
  });
}
// 获取关联的车辆抓拍信息
export function getLinkVehicleCapture(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/getLinkVehicleCapture",
    method: "post",
    data,
  });
}

// 步态高级搜索
export function queryGaitRecordSearchEx(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/queryGaitRecordSearchEx",
    method: "post",
    data,
  });
}

// 步态-设备选择弹出框数据查询
export function queryYhsdDeviceList(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/queryYhsdDeviceList",
    method: "post",
    data,
  });
}
