<template>
  <div class="detection-result">
    <!-- 统计 -->
    <charts-container :abnormalCount="statisticsList"></charts-container>
    <div class="detection-result-container">
      <div class="detection-result-head">
        <div class="head">
          <i class="icon-font icon-jiancejieguoxiangqing"></i><span class="title">检测结果详情</span>
        </div>
        <Button
          :loading="exportLoading"
          custom-icon="icon-font icon-daochu"
          type="primary"
          class="export-btn"
          @click="exportHandle"
          >导出</Button
        >
      </div>
      <div class="detection-result-body">
        <div class="search-module">
          <div class="search">
            <ui-label class="inline" label="设备编码">
              <Input v-model="searchData.deviceId" class="width-lg" placeholder="请输入设备编码"></Input>
            </ui-label>
            <ui-label class="inline ml-lg" label="设备名称">
              <Input v-model="searchData.deviceName" class="width-lg" placeholder="请输入设备名称"></Input>
            </ui-label>
            <ui-label class="inline ml-lg" label="检测结果">
              <Select v-model="searchData.resultStatus" class="width-lg" placeholder="请选择检测结果" clearable>
                <Option value="1">时钟准确 </Option>
                <Option value="2">时钟异常 </Option>
                <Option value="3">无法检测 </Option>
              </Select>
            </ui-label>
            <div class="inline ml-lg">
              <Button type="primary" @click="searchHandle">查询</Button>
              <Button class="ml-sm" @click="resetHandle">重置</Button>
            </div>
          </div>
        </div>
        <div class="auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :class="tableData.length > 0 ? '' : 'ui-table-scroll-nodata'"
            :loading="loading"
            :table-columns="columns"
            :table-data="tableData"
          >
            <template #pwd="{ row }">
              <Tooltip v-if="row.pwd" :content="row.pwd">********</Tooltip>
              <span v-else>--</span>
            </template>
            <template #success="{ row }">
              <span
                :style="{
                  color: !row.success ? '' : row.errReason ? '#CF3939' : '#269F26',
                }"
                >{{ !row.success ? '无法检测' : row.errReason ? '时钟异常' : '时钟准确' }}</span
              >
            </template>
          </ui-table>
        </div>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
      </div>
    </div>
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';
export default {
  components: {
    'charts-container': require('../components/charts-container.vue').default,
    'ui-table': require('@/components/ui-table.vue').default,
  },
  props: {
    dataObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statisticsList: [
        { title: '检测设备总数', count: 0, icon: 'icon-jianceshuliang' },
        {
          title: '时钟准确设备数量 ',
          count: 0,
          icon: 'icon-shizhongzhunqueshebeishuliang',
        },
        {
          title: '时钟异常设备数量',
          count: 0,
          icon: 'icon-shizhongyichangshebeishuliang',
        },
        {
          title: '无法检测设备数量',
          countKey: 0,
          icon: 'icon-wufajianceshebeishuliang1',
        },
      ],
      searchData: {
        deviceId: '',
        deviceName: '',
        resultStatus: '',
      },
      loading: false,
      columns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '设备编码', minWidth: 180, key: 'deviceId' },
        { title: '设备名称', minWidth: 180, key: 'deviceName', tooltip: true },
        { title: 'IP地址', minWidth: 180, key: 'ip' },
        { title: '端口号', minWidth: 100, key: 'port' },
        { title: '通道号', minWidth: 120, key: 'channelNum' },
        { title: '账号', minWidth: 100, key: 'userName' },
        { title: '密码', minWidth: 120, slot: 'pwd' },
        { title: '检测结果', minWidth: 120, slot: 'success' },
      ],
      tableDataSource: [],
      searchDataSource: [],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      exportLoading: false,
    };
  },
  methods: {
    init() {
      this.loading = true;
      this.tableDataSource = [];
      this.searchDataSource = [];
      this.tableData = [];
      this.statisticsList.forEach((v) => {
        v.count = 0;
      });
      this.pageData.totalCount = 0;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.searchData.deviceId = '';
      this.searchData.deviceName = '';
      this.searchData.resultStatus = '';
      this.dataObj.time = this.dataObj.time + '';
      this.$http
        .post(governancetoolset.getClockList, this.dataObj)
        .then((res) => {
          this.tableDataSource = res.data;
          this.pageData.totalCount = this.tableDataSource.length;
          this.statisticsList[0].count = this.pageData.totalCount;
          this.tableDataSource.forEach((v) => {
            if (!v.success) {
              this.statisticsList[3].count++;
            } else {
              if (v.errReason) {
                this.statisticsList[2].count++;
              } else {
                this.statisticsList[1].count++;
              }
            }
          });
          this.searchDataSource = JSON.parse(JSON.stringify(this.tableDataSource));
          this.listHandle();
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
      this.listHandle();
    },
    listHandle() {
      let count = (this.pageData.pageNum - 1) * this.pageData.pageSize;
      this.tableData = this.searchDataSource.slice(count, count + this.pageData.pageSize);
    },
    // 查询
    searchHandle() {
      this.searchDataSource = this.tableDataSource.filter((v) => {
        return (
          v.deviceId &&
          v.deviceId.indexOf(this.searchData.deviceId) >= 0 &&
          v.deviceName &&
          v.deviceName.indexOf(this.searchData.deviceName) >= 0 &&
          (this.searchData.resultStatus === '1'
            ? v.success && !v.errReason
            : this.searchData.resultStatus === '2'
              ? v.success && v.errReason
              : this.searchData.resultStatus === '3'
                ? !v.success
                : true)
        );
      });
      this.pageData.totalCount = this.searchDataSource.length;
      this.pageData.pageNum = 1;
      this.listHandle();
    },
    // 重置
    resetHandle() {
      this.pageData.pageSize = 20;
      this.searchData.deviceId = '';
      this.searchData.deviceName = '';
      this.searchData.resultStatus = '';
      this.searchHandle();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.listHandle();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.listHandle();
    },
    // 导出
    async exportHandle() {
      this.exportLoading = true;
      try {
        let res = await this.$http.post(governancetoolset.exportClockTestRecord, this.tableDataSource, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.detection-result {
  display: flex;
  flex: 1;
  flex-direction: column;
  .detection-result-container {
    display: flex;
    flex: 1;
    flex-direction: column;
    .detection-result-head {
      width: 100%;
      height: 54px;
      border-bottom: 1px solid var(--devider-line);
      display: flex;
      align-items: center;
      color: var(--color-primary);
      justify-content: space-between;
      .head {
        display: flex;
        align-items: center;
        .icon-font {
          font-size: 16px;
          margin-right: 6px;
        }
        .title {
          font-size: 14px;
          line-height: 20px;
        }
      }
      .export-btn {
        display: flex;
        align-items: center;
        /deep/.icon-font {
          font-size: 14px;
          line-height: 14px;
          margin-right: 8px;
        }
      }
    }
    .detection-result-body {
      display: flex;
      flex: 1;
      flex-direction: column;
      .search-module {
        padding: 10px 0;
      }
      /deep/ .ui-page {
        padding: 20px 2px;
      }
    }
  }
}
</style>
