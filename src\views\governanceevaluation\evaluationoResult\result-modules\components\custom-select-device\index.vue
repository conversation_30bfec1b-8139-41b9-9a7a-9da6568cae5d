<template>
  <component :is="componentName" v-bind="attrs" v-on="$listeners"></component>
</template>
<script>
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      componentName: null,
      recheckIndex: Object.freeze({
        VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN: 'playing-select',
        VIDEO_PLAYING_ACCURACY: 'playing-select',
        VIDEO_READ_PROMOTION_RATE: 'playing-select',
        VIDEO_GENERAL_PLAYING_ACCURACY: 'playing-select',
        VIDEO_HISTORY_ACCURACY: 'history-select',
        VIDEO_GENERAL_HISTORY_ACCURACY: 'history-select',
        VIDEO_CLOCK_ACCURACY: 'clock-select',
        VIDEO_GENERAL_CLOCK_ACCURACY: 'clock-select',
        VIDEO_CLOCK_ACCURACY_PROMOTION_RATE: 'clock-select',
        VIDEO_OSD_ACCURACY: 'subtitle-select',
        VIDEO_GENERAL_OSD_ACCURACY: 'subtitle-select',
        VIDEO_OSD_ACCURACY_PROMOTION_RATE: 'subtitle-promotion-select',
        VIDEO_OSD_CLOCK_ACCURACY: 'subtitle-clock-select',
        VIDEO_GENERAL_OSD_CLOCK_ACCURACY: 'subtitle-clock-select',
        VIDEO_QUALITY_PASS_RATE_RECHECK: 'quality-select',
        VIDEO_QUALITY_PASS_RATE: 'quality-select',
        FACE_URL_AVAILABLE: 'url-available-select',
        FACE_SMALL_URL_AVAILABLE: 'url-available-select',
        FACE_FOCUS_URL_AVAILABLE: 'url-available-select',
        VEHICLE_URL_AVAILABLE: 'url-available-select',
        VEHICLE_URL_AVAILABLE_IMPORTANT: 'url-available-select',
        VIDEO_HISTORY_COMPLETE_ACCURACY: 'history-select',
        VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN: 'history-select',
      }),
      attrs: {},
    };
  },
  created() {
    if (this.recheckIndex[this.moduleData.indexType]) {
      this.componentName = this.recheckIndex[this.moduleData.indexType];
      this.attrs = this.getAttrs();
    } else {
      throw '请配置该指标自定义设备对应的列表';
    }
  },
  methods: {
    getAttrs() {
      return {
        qualifiedColorConfig: qualifiedColorConfig,
        ...this.$attrs,
        moduleData: this.moduleData,
      };
    },
  },
  watch: {},
  components: {
    PlayingSelect: require('./playing-select.vue').default,
    HistorySelect: require('./history-select.vue').default,
    ClockSelect: require('./clock-select.vue').default,
    SubtitleSelect: require('./subtitle-select.vue').default,
    SubtitlePromotionSelect: require('./subtitle-promotion-select.vue').default,
    SubtitleClockSelect: require('./subtitle-clock-select.vue').default,
    QualitySelect: require('./quality-select.vue').default,
    UrlAvailableSelect: require('./url-available-select.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
