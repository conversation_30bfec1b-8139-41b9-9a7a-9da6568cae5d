<!--
    * @FileDescription: 昼伏夜出
    * @Author: H
    * @Date: 2023/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="nightDazed">
        <!-- 地图 -->
        <mapCustom ref="mapBase" mapType="nightDazed" :allCameraList="allCameraList" @goSearch="goSearch"/>
        <!-- 左面信息展示框 -->
        <left-box ref="leftBox" @search="handleSearch" @selectDraw="selectDraw"></left-box>
        <bottomBox v-if="chartShow" :echartDate="searchDate"></bottomBox>
        <rightBox v-if="rightShow" 
            @cancel="handleCancel"
            @search="handleRightSearch"
            @retrieveDetails="handleDetails"></rightBox>
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import bottomBox from './components/bottomBox.vue';
import rightBox from './components/rightBox.vue';
import mapCustom from '../../components/map/index.vue';
import { getMapLayerByType, queryPlaceInfoPageList } from '@/api/operationsOnTheMap';
import { mapMutations } from 'vuex';
import { myMixins } from '../../mixins/index.js'
export default {
    name: 'nightDazed',
    mixins: [myMixins],
    components:{
        leftBox,
        bottomBox,
        rightBox,
        mapCustom
    },
    data () {
        return {
            rightShow: false,
            // allCameraList: [],
            chartShow: false,
            searchDate:[]
        }
    },
    watch:{
            
    },
    computed:{
    },
    async created() {
        // await this.getMapLayerByType();   
    },
    mounted(){
            
    },
    activated() {
        this.setLayoutNoPadding(true)
    },
    deactivated(){
        this.setLayoutNoPadding(false)
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        handleCancel() {
            this.rightShow = false;
            this.chartShow = false;
        },
        // 搜索
        handleSearch(val) {
            if(val.dateType == 2) {
                this.searchDate = this.dayDispose(7);
            }else if(val.dateType == 3){
                this.searchDate = this.dayDispose(30);
            }else{
                let dateList = []
                this.getDiffDate(val.startDate, val.endDate).forEach(item => {
                    let day = item.split('/');
                    dateList.push(day[1] + '.' + day[2])
                })
                this.searchDate = dateList;
            }
            this.rightShow = true;
            this.chartShow = false;
        },
        dayDispose(val) {
            var thrityMonth = [];
            for (var i = 0; i < val; i++) {
                thrityMonth.unshift(new Date(new Date().setDate(new Date().getDate() - i)).toLocaleDateString())
            }
            let dateList = []
            thrityMonth.forEach(item => {
                let day = item.split('/');
                dateList.push(day[1] + '.' + day[2])
            })
            return dateList
        },
        getDiffDate (start, end) {
            var startTime = this.getDate(start);
            var endTime = this.getDate(end);
            var dateArr = [];
            while ((endTime.getTime() - startTime.getTime()) > 0) {
                var year = startTime.getFullYear();
                var month = (startTime.getMonth()+1).toString().length === 1 ? "0" + (parseInt(startTime.getMonth().toString(),10) + 1) : (startTime.getMonth() + 1);
                var day = startTime.getDate().toString().length === 1 ? "0" + startTime.getDate() : startTime.getDate();
                dateArr.push(year + "/" + month + "/" + day);
                startTime.setDate(startTime.getDate() + 1);
            }
            return dateArr;
        },
        getDate (datestr) {
            var temp = datestr.split("-");
            if (temp[1] === '01') {
                temp[0] = parseInt(temp[0],10) - 1;
                temp[1] = '12';
            } else {
                temp[1] = parseInt(temp[1],10) - 1;
            }
            //new Date()的月份入参实际都是当前值-1
            var date = new Date(temp[0], temp[1], temp[2]);
            return date;
        },
        selectDraw(type) {
            this.$refs.mapBase.selectDraw(type);
        },
        // 查询
        handleRightSearch() {
            this.chartShow = false;
        },
        // 检索详情
        handleDetails() {
            this.chartShow = true;
        },
        goSearch(value, type){
            this.$refs.leftBox.goSearch(value, type);
        },
        // 进入页面查询所有点位信息
        async getMapLayerByType () {
            this.allCameraList = [];
            const params = {
                typeKeys: {
                map_device: 'all',
                map_place: 'all',
                map_police: 'all'
                }
            }
            let  res = await queryPlaceInfoPageList({});
            let { data } = await getMapLayerByType(params);
            res.data.entities.map((item, index) => {
                this.$set(this.allCameraList, index, item)
            })
            let len = this.allCameraList.length - 1;
            data.map((item, index) => {
                this.$set(this.allCameraList, len + index, item)
            })
        },
    }
}
</script>

<style lang='less' scoped>
.nightDazed{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%; 
}
</style>
