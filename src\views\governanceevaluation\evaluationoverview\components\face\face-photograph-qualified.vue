<template>
  <!-- 人脸设备抓拍图片合格率(人脸卡口设备抓拍合格率) -->
  <div class="face-photograph-qualified" ref="contentScroll">
    <div class="information-header">
      <carStatistics :statistics-list="statisticsList" :isflexfix="true"></carStatistics>
      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <tagView class="tagView fr" ref="tagView" :list="['设备模式', '图片模式']" @tagChange="tagChange1" />
      </div>
      <!-- 设备模式 -->
      <TableList
        class="navBarWrap"
        :contentClientHeight="contentClientHeight"
        v-if="modelTag == 0 && tableColumns.length > 0"
        ref="infoList"
        :columns="tableColumns"
        :loadData="loadDataList"
      >
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <SearchCard :is-image-model="false" @startSearch="startSearch"></SearchCard>
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
        <!-- 表格操作 -->
        <template #canNotDetectFaceDataTotal="{ row }">
          <span class="font-red">{{ row.canNotDetectFaceDataTotal }}</span>
        </template>
        <template #unqualifiedNum="{ row }">
          <span class="font-red">{{ row.unqualifiedNum }}</span>
        </template>
        <template #outcome="{ row }">
          <span
            class="check-status"
            :class="[
              row.outcome === '1' ? 'bg-success' : '',
              row.outcome === '2' ? 'bg-failed' : '',
              row.outcome === '3' ? 'bg-D66418' : '',
            ]"
            :title="row.description"
          >
            {{ handleCheckStatus(row.outcome) }}
          </span>
        </template>
        <template #qualifiedRate="{ row }">
          <span>{{ row.qualifiedRate || 0 }}%</span>
        </template>
        <template #tagNames="{ row }">
          <tags-more :tag-list="row.tagList || []"></tags-more>
        </template>
        <template #option="{ row }">
          <ui-btn-tip
            icon="icon-chakantupian"
            content="查看图片"
            class="mr-sm"
            @click.native="clickRow(row)"
          ></ui-btn-tip>
          <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
        </template>
      </TableList>
      <!-- 图像模式 -->
      <TableCard
        ref="infoCard"
        class="card-list auto-fill"
        :loadData="loadDataCard"
        :cardInfo="cardInfo"
        v-if="modelTag == 1"
      >
        <div slot="search" class="hearder-title">
          <SearchCard :checkStatus="checkStatus" @startSearch="startSearch"></SearchCard>
          <Button type="primary" class="btn_search" @click="getSecondExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
        <!-- 卡片 -->
        <template #card="{ row }">
          <InfoCard class="card1" :checkNum="3" :list="row" :cardInfo="cardInfo" @bigImageUrl="bigImageUrl"> </InfoCard>
        </template>
      </TableCard>
      <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
      <CheckPicture
        v-model="checkPicture"
        :list="currentRow"
        :resultId="{
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        }"
        :tagList="tagList"
        :interFaceName="getSecondaryPopUpData"
        :checkNum="3"
      ></CheckPicture>
    </div>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less" scoped>
.face-photograph-qualified {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .navBarWrap {
    position: sticky !important;
    top: 100px;
    width: 100%;
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .card-list {
    width: 100%;
    min-height: 460px !important;
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: calc(100% - 370px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
    }

    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .hearder-title {
      position: relative;
      padding: 10px 0;

      .btn_search {
        position: absolute;
        right: 0px;
        top: 10px;
      }
    }
  }
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import taganalysis from '@/config/api/taganalysis';
import { mapActions, mapGetters } from 'vuex';
export default {
  mixins: [downLoadTips],
  name: 'face-photograph-qualified',
  data() {
    return {
      rankLoading: false,
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,

      statisticsList: [
        {
          name: '人脸卡口总量',
          value: 0,
          icon: 'icon-renliankakou',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          textColor: 'color1',
          type: 'number',
          key: 'total',
        },
        {
          name: '检测设备数量',
          value: 0,
          icon: 'icon-jianceshebeishuliang1',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'deviceDataTotal',
        },
        {
          name: '检测合格设备数',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          type: 'number',
          textColor: 'color9',
          key: 'passDeviceDataTotal',
        },
        {
          name: '检测不合格设备数',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'notPassDeviceDataTotal',
        },
        {
          name: '检测图片数量',
          value: 0,
          icon: 'icon-jiancetupianshuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          textColor: 'color6',
          type: 'number',
          key: 'faceDataTotal',
        },
        {
          name: '小图唯一人脸图片数量',
          value: 0,
          icon: 'icon-xiaotuweiyirenliantupianshuliang',
          iconColor: 'icon-bg7',
          liBg: 'li-bg7',
          textColor: 'color7',
          type: 'number',
          key: 'passFaceDataTotal',
        },
        {
          name: '小图非唯一人脸图片数量',
          value: 0,
          icon: 'icon-xiaotufeiweiyirenliantupianshuliang',
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          textColor: 'color8',
          type: 'number',
          key: 'notPassFaceDataTotal',
        },
        {
          name: '无法检测图片数量',
          value: 0,
          icon: 'icon-wufajiancetupianshuliang',
          iconColor: 'icon-bg10',
          liBg: 'li-bg10',
          textColor: 'color10',
          type: 'number',
          key: 'canNotDetectFaceDataTotal',
        },
        {
          name: '人脸设备抓拍图片合格率',
          value: 0,
          icon: 'icon-quedingzhilishuju',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          textColor: 'color3',
          type: 'percentage',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 120,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', tooltip: true, minWidth: 120 },
        { title: '点位类型', key: 'sbdwlxText', tooltip: true, minWidth: 120 },
        { title: '检测图片数量', key: 'total', tooltip: true, minWidth: 150 },
        { title: '小图唯一人脸图片数量', key: 'qualifiedNum', width: 200 },
        {
          title: '小图非唯一人脸图片数量',
          key: 'unqualifiedNum',
          slot: 'unqualifiedNum',
          width: 200,
        },
        {
          title: '无法检测图片数量',
          key: 'canNotDetectFaceDataTotal',
          slot: 'canNotDetectFaceDataTotal',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '合格率',
          key: 'qualifiedRate',
          slot: 'qualifiedRate',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '检测状态',
          key: 'outcome',
          slot: 'outcome',
          tooltip: true,
          minWidth: 120,
        },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        customParameters: { deviceId: '', deviceName: '', outcome: '' },
      },
      exportLoading: false,
      rankData: [],
      paramsList: {},
      statisticalList: {},
      loadDataList: async (parameter) => {
        let params = Object.assign(
          parameter,
          {
            indexId: this.paramsList.indexId,
            batchId: this.paramsList.batchId,
            access: this.paramsList.access,
            displayType: this.paramsList.displayType,
            orgRegionCode: this.paramsList.orgRegionCode,
          },
          this.searchData,
        );
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        return res.data;
      },
      loadDataCard: async (parameter) => {
        let params = Object.assign(
          parameter,
          {
            indexId: this.paramsList.indexId,
            batchId: this.paramsList.batchId,
            access: this.paramsList.access,
            displayType: this.paramsList.displayType,
            orgRegionCode: this.paramsList.orgRegionCode,
          },
          this.searchData,
        );
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getPolyData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        return res.data;
      },
      modelTag: 0, // 设备模式,图像模式
      cardInfo: [
        { icon: 'icon-shijian', value: 'shotTime', text: 'tip' },
        { icon: 'icon-dizhi', value: 'address', text: 'tip' },
        { icon: 'icon-shujujiancegongju-01-01', value: 'resultTip', text: 'tip' },
      ],
      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      checkPicture: false, //查看图片
      currentRow: {},
      checkStatus: [
        { name: '小图唯一人脸', checkKey: 1 },
        { name: '小图非唯一人脸', checkKey: 2 },
        { name: '无法检测', checkKey: 3 },
      ],
      tagList: [
        { label: '小图唯一人脸', outcome: 1, value: 1 },
        {
          label: '小图非唯一人脸',
          outcome: 2,
          value: 2,
        },
        {
          label: '无法检测',
          outcome: 3,
          value: 3,
        },
      ],
      contentClientHeight: 0,
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  mounted() {
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 155 : 0;
    this.getTagList();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.init();
      } catch (err) {
        console.log(err);
      }
    },
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    handleCheckStatus(row) {
      const flag = {
        1: '合格',
        2: '不合格',
        3: '无法考核',
      };
      return flag[row];
    },
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图图片地址缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    startSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    async init() {
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.customParameters.deviceId,
            deviceName: this.searchData.customParameters.deviceName,
            outcome: this.searchData.customParameters.outcome,
          },
        };
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    async getSecondExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: { ...this.searchData.customParameters },
        };
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportSecondModelData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportSecondModelData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  components: {
    TableList: require('./components/tableList.vue').default,
    TableCard: require('./components/tableCard.vue').default,
    InfoCard: require('./components/infoCard.vue').default,
    tagView: require('./components/tags.vue').default,
    SearchCard: require('./components/searchCard.vue').default,
    CheckPicture: require('./components/check-picture.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    LookScene: require('@/components/look-scene').default,
    carStatistics:
      require('@/views/governanceevaluation/evaluationoverview/components/car/component/car-statistics.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
