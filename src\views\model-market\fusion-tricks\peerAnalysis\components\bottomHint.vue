/**
* 底部提示信息
 */
<template>
    <div class="hint">
        <ul class="hint_ul">
            <li class="hint_ul_list" v-for='(item, index) in list' :key='index'>
                <div v-if="index == 0 || index == 1" class="lint" :class="'lint_'+ item.color"></div>
                <div v-else class="back" :class="'back_'+ item.url"></div>
                <p class="title">{{ item.name }}</p>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            list:[
                { 'name': '目标轨迹', 'color': 'bule' },
                { 'name': '同行轨迹', 'color': 'yellow' },
                { 'name': '对象位置', 'url': 'bule' },
                { 'name': '同行位置', 'url': 'yellow' },
                { 'name': '重合位置', 'url': 'red' },
            ]
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {}
}
</script>

<style lang='less' scoped>
.hint{
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    &_ul{
        width: 642px;
        height: 40px;
        background: #000000;
        border-radius: 4px;
        opacity: 0.7;
        display: flex;
        font-size: 14px;
        color: #fff;
        justify-content: space-evenly;
        // gap: 30px;
        &_list{
            display: flex;
            align-items: center;
            .lint{
                width: 30px;
                height: 3px;
            }
            .title{
                margin-left: 10px;
            }
            .lint_bule{
                background: #2C86F8;
            }
            .lint_yellow{
                background: #F29F4C;
            }
            .back{
                width: 20px;
                height: 22px;
            }
            .back_bule{
                background: url('../../../../../assets/img/map/trajectory-blue.png') no-repeat;
                background-size: cover;
            }
            .back_yellow{
                background: url('../../../../../assets/img/map/trajectory-yellow.png') no-repeat;
                background-size: cover;
            }
            .back_red{
                background: url('../../../../../assets/img/map/trajectory-red.png') no-repeat ;
                background-size: cover;
            }
        }
    }
}

</style>

 