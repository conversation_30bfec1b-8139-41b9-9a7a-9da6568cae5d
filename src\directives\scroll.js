export default function (Vue) {
  // 传进来的value为要减去的下面的值
  Vue.directive('scroll', {
    bind(el, bind) {
      let func = () => {
        bind.def.runs(el, bind);
      };
      el.style.overflowY = 'auto';
      el.style.overflowX = 'auto';
      window.addEventListener('resize', func);
      // parseFloat(document.documentElement.style.fontSize) 是初始化的时候的高度
      // console.log(parseFloat(document.documentElement.style.fontSize))
      el.style.height =
        window.innerHeight - (bind.value * parseFloat(document.documentElement.style.fontSize)) / 192 + 'px';
    },
    inserted() {},
    update(el, bind) {
      el.style.height =
        window.innerHeight - (bind.value * parseFloat(document.documentElement.style.fontSize)) / 192 + 'px';
    },
    unbind(el, bind) {
      window.removeEventListener('resize', bind.def.runs);
    },
    runs(el, bind) {
      el.style.height =
        window.innerHeight - (bind.value * parseFloat(document.documentElement.style.fontSize)) / 192 + 'px';
    },
  });
}
