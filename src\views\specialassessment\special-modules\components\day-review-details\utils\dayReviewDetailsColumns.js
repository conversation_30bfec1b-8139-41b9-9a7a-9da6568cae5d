import { normalFormData as deviceConnectInternetFormData } from '@/views/governanceevaluation/evaluationoResult/result-modules/BayonetConnectionRate/util/enum/ReviewParticular.js';
import { formItemData as networkConsistencyRateFormData } from '@/views/governanceevaluation/evaluationoResult/result-modules/VehicleNetworkConsistencyRate/util/enum/ReviewParticular.js';

const offlineStatFormData = {
  deviceId: '',
  deviceName: '',
  isImportant: '',
  serialOffMin: null,
  serialOffMax: null,
  offerTotalMin: null,
  offerTotalMax: null,
};
const qualifiedColorConfig = {
  '1': {
    color: '#1FAF81',
    dataValue: '合格',
  },
  '2': {
    color: '#EA4A36',
    dataValue: '不合格',
  },
};
const isImportantDict = [
  { value: '0', label: '普通设备' },
  { value: '1', label: '重点设备' },
];
const defaultFormItemData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'isImportant',
    label: '设备重点类型',
    placeholder: '请选择设备重点类型',
    options: isImportantDict,
  },
  {
    type: 'select',
    key: 'qualified',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择设不合格原因',
    options: [],
  },
];
const defaultFormData = {
  deviceId: '',
  deviceName: '',
  isImportant: '',
  qualified: '',
  errorCodes: [],
};
const formItemData = {
  BASIC_ACCURACY: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'sbgnlx',
      label: '摄像机功能类型',
      options: [],
    },
  ],
  FACE_ACCURACY: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'sbgnlx',
      label: '摄像机功能类型',
      options: [],
    },
  ],
  VEHICLE_ACCURACY: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'sbgnlx',
      label: '摄像机功能类型',
      options: [],
    },
  ],
  VIDEO_ACCURACY: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'sbgnlx',
      label: '摄像机功能类型',
      options: [],
    },
  ],
  VIDEO_PLAYING_ACCURACY: defaultFormItemData,
  VIDEO_HISTORY_ACCURACY: defaultFormItemData,
  VIDEO_OSD: defaultFormItemData,
  VIDEO_CLOCK: defaultFormItemData,
  VIDEO_PASS: defaultFormItemData,
  VIDEO_HISTORY_COMPLETE: defaultFormItemData,
  FACE_ONLINE: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'qualified',
      label: '检测结果',
      placeholder: '请选择检测结果',
      options: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          value: key,
          label: qualifiedColorConfig[key].dataValue,
        };
      }),
    },
  ],
  FACE_CLOCK_QUALITY: defaultFormItemData,
  FACE_DELAY: defaultFormItemData,
  FACE_QUALITY: defaultFormItemData,
  VEHICLE_ONLINE: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'qualified',
      label: '检测结果',
      placeholder: '请选择检测结果',
      options: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          value: key,
          label: qualifiedColorConfig[key].dataValue,
        };
      }),
    },
  ],
  VEHICLE_CLOCK_QUALITY: defaultFormItemData,
  VEHICLE_DELAY: defaultFormItemData,
  VEHICLE_QUALITY: defaultFormItemData,
  FACE_OFFLINE_STAT: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: isImportantDict,
    },
    {
      type: 'start-end-num',
      label: '累计离线天数',
      startKey: 'offerTotalMin',
      endKey: 'offerTotalMax',
    },
    {
      type: 'start-end-num',
      label: '最大连续离线天数',
      startKey: 'serialOffMin',
      endKey: 'serialOffMax',
    },
  ],
  VEHICLE_OFFLINE_STAT: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: isImportantDict,
    },
    {
      type: 'start-end-num',
      label: '累计离线天数',
      startKey: 'offerTotalMin',
      endKey: 'offerTotalMax',
    },
    {
      type: 'start-end-num',
      label: '最大连续离线天数',
      startKey: 'serialOffMin',
      endKey: 'serialOffMax',
    },
  ],
  VIDEO_OFFLINE_STAT: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: isImportantDict,
    },
    {
      type: 'start-end-num',
      label: '累计离线天数',
      startKey: 'offerTotalMin',
      endKey: 'offerTotalMax',
    },
    {
      type: 'start-end-num',
      label: '最大连续离线天数',
      startKey: 'serialOffMin',
      endKey: 'serialOffMax',
    },
  ],
  VIDEO_CODE_STANDARD_RATE: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: isImportantDict,
    },
    {
      type: 'select',
      key: 'qualified',
      label: '检测结果',
      placeholder: '请选择检测结果',
      options: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          value: key,
          label: qualifiedColorConfig[key].dataValue,
        };
      }),
    },
  ],
  VEHICLE_DEVICE_CONNECT_INTERNET: deviceConnectInternetFormData,
  FACE_DEVICE_CONNECT_INTERNET: deviceConnectInternetFormData,
  VEHICLE_CATALOGUE_SAME: networkConsistencyRateFormData,
  FACE_CATALOGUE_SAME: networkConsistencyRateFormData,
  BODY_UPLOAD: defaultFormItemData,
  BODY_CLOCK: defaultFormItemData,
  BODY_ONLINE_RATE: [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: '0', label: '普通设备' },
        { value: '1', label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'qualified',
      label: '检测结果',
      placeholder: '请选择检测结果',
      options: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          value: key,
          label: qualifiedColorConfig[key].dataValue,
        };
      }),
    },
  ],
  BODY_ACTIVE: deviceConnectInternetFormData,
};
const initFormData = {
  BASIC_ACCURACY: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    errorMessages: [],
    sbgnlx: '',
  },
  FACE_ACCURACY: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    errorMessages: [],
    sbgnlx: '',
  },
  VEHICLE_ACCURACY: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    errorMessages: [],
    sbgnlx: '',
  },
  VIDEO_ACCURACY: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    errorMessages: [],
    sbgnlx: '',
  },
  VIDEO_PLAYING_ACCURACY: defaultFormData,
  VIDEO_HISTORY_ACCURACY: defaultFormData,
  VIDEO_OSD: defaultFormData,
  VIDEO_CLOCK: defaultFormData,
  VIDEO_PASS: defaultFormData,
  VIDEO_HISTORY_COMPLETE: defaultFormData,
  FACE_ONLINE: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    qualified: '',
  },
  FACE_CLOCK_QUALITY: defaultFormData,
  FACE_DELAY: defaultFormData,
  FACE_QUALITY: defaultFormData,
  VEHICLE_ONLINE: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    qualified: '',
  },
  VEHICLE_CLOCK_QUALITY: defaultFormData,
  VEHICLE_DELAY: defaultFormData,
  VEHICLE_QUALITY: defaultFormData,
  FACE_OFFLINE_STAT: offlineStatFormData,
  VEHICLE_OFFLINE_STAT: offlineStatFormData,
  VIDEO_OFFLINE_STAT: offlineStatFormData,
  VIDEO_CODE_STANDARD_RATE: defaultFormData,
  VEHICLE_DEVICE_CONNECT_INTERNET: {
    deviceId: '',
    deviceName: '',
    outcome: '',
  },
  FACE_DEVICE_CONNECT_INTERNET: {
    deviceId: '',
    deviceName: '',
    outcome: '',
  },
  VEHICLE_CATALOGUE_SAME: defaultFormData,
  FACE_CATALOGUE_SAME: defaultFormData,
  BODY_UPLOAD: defaultFormData,
  BODY_CLOCK: defaultFormData,
  BODY_ONLINE_RATE: {
    deviceId: '',
    deviceName: '',
    isImportant: '',
    qualified: '',
  },
  BODY_ACTIVE: {
    deviceId: '',
    deviceName: '',
    outcome: '',
  },
};
const getTableColumns = () => {
  return {
    // 资产考核
    BASIC_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '行政区划',
        key: 'civilName',
        minWidth: 150,
        tooltip: true,
      },
      {
        title: '经度',
        key: 'longitude',
        slot: 'longitude',
        minWidth: 120,
      },
      {
        title: '纬度',
        key: 'latitude',
        slot: 'latitude',
        minWidth: 120,
      },
      {
        title: 'MAC地址',
        key: 'macAddr',
        minWidth: 150,
      },
      { title: 'IPv4地址', key: 'ipAddr', minWidth: 120 },
      { title: '摄像机功能类型', key: 'sbgnlxText', align: 'left', minWidth: 130, tooltip: true },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '摄像机采集区域', key: 'sbcjqyText', align: 'left', tooltip: true, minWidth: 130 },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    FACE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '行政区划',
        key: 'civilName',
        minWidth: 150,
        tooltip: true,
      },
      {
        title: '经度',
        key: 'longitude',
        slot: 'longitude',
        minWidth: 120,
      },
      {
        title: '纬度',
        key: 'latitude',
        slot: 'latitude',
        minWidth: 120,
      },
      {
        title: 'MAC地址',
        key: 'macAddr',
        minWidth: 150,
      },
      { title: 'IPv4地址', key: 'ipAddr', minWidth: 120 },
      { title: '摄像机功能类型', key: 'sbgnlxText', align: 'left', minWidth: 130, tooltip: true },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '摄像机采集区域', key: 'sbcjqyText', align: 'left', tooltip: true, minWidth: 130 },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VEHICLE_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '行政区划',
        key: 'civilName',
        minWidth: 150,
        tooltip: true,
      },
      {
        title: '经度',
        key: 'longitude',
        slot: 'longitude',
        minWidth: 120,
      },
      {
        title: '纬度',
        key: 'latitude',
        slot: 'latitude',
        minWidth: 120,
      },
      {
        title: 'MAC地址',
        key: 'macAddr',
        minWidth: 150,
      },
      { title: 'IPv4地址', key: 'ipAddr', minWidth: 120 },
      { title: '摄像机功能类型', key: 'sbgnlxText', align: 'left', minWidth: 130, tooltip: true },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '摄像机采集区域', key: 'sbcjqyText', align: 'left', tooltip: true, minWidth: 130 },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VIDEO_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 150,
      },
      {
        title: '行政区划',
        key: 'civilName',
        minWidth: 150,
        tooltip: true,
      },
      {
        title: '经度',
        key: 'longitude',
        slot: 'longitude',
        minWidth: 120,
      },
      {
        title: '纬度',
        key: 'latitude',
        slot: 'latitude',
        minWidth: 120,
      },
      {
        title: 'MAC地址',
        key: 'macAddr',
        minWidth: 150,
      },
      { title: 'IPv4地址', key: 'ipAddr', minWidth: 120 },
      { title: '摄像机功能类型', key: 'sbgnlxText', align: 'left', minWidth: 130, tooltip: true },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '摄像机采集区域', key: 'sbcjqyText', align: 'left', tooltip: true, minWidth: 130 },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        fixed: 'right',
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    // 视频考核
    VIDEO_PLAYING_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
        // renderHeader: (h, params) => {
        //     return h('div', [
        //         h('span', '检测结果'),
        //         h(
        //             'Tooltip', {
        //                 props: {
        //                     transfer: true,
        //                     placement: 'bottom',
        //                 },
        //                 style: {
        //                     verticalAlign: 'middle'
        //                 },
        //             },
        //             [
        //                 h('i', {
        //                     class: 'icon-font icon-wenhao ml-xs f-12',
        //                     style: {
        //                         width: '13px',
        //                         verticalAlign: 'top',
        //                         color: 'var(--color-warning)',
        //                     },
        //                 }),
        //                 h(
        //                     'span', {
        //                         slot: 'content',
        //                         style: {
        //                             whiteSpace: 'normal',
        //                             wordBreak: 'normal',
        //                             maxWidth: '300px',
        //                         },
        //                     },
        //                     `设备的【${
        //                         indexDetectionModeMap[ columnParams.statisticalList.indexDetectionMode ]
        //                     }】作为指标计算结果`
        //                 ),
        //             ]
        //         ),
        //     ])
        // },
      },
      {
        title: '信令时延(毫秒)',
        key: 'delaySipMillSecond',
        slot: 'delaySipMillSecond',
        width: 130,
        sortable: 'custom',
      },
      {
        title: '视频流时延(毫秒)',
        key: 'delayStreamMillSecond',
        slot: 'delayStreamMillSecond',
        width: 140,
        sortable: 'custom',
      },
      {
        title: '关键帧时延(毫秒)',
        key: 'delayIdrMillSecond',
        slot: 'delayIdrMillSecond',
        width: 140,
        sortable: 'custom',
      },
      {
        title: '原因',
        key: 'errorCodeName',
        minWidth: 120,
        tooltip: true,
      },
      {
        minWidth: 150,
        title: '设备标签',
        slot: 'tagNames',
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VIDEO_HISTORY_ACCURACY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
        // renderHeader: (h, params) => {
        //     return h('div', [
        //         h('span', '检测结果'),
        //         h(
        //             'Tooltip', {
        //                 props: {
        //                     transfer: true,
        //                     placement: 'bottom',
        //                 },
        //                 style: {
        //                     verticalAlign: 'middle'
        //                 },
        //             },
        //             [
        //                 h('i', {
        //                     class: 'icon-font icon-wenhao ml-xs f-12',
        //                     style: {
        //                         width: '13px',
        //                         verticalAlign: 'top',
        //                         color: 'var(--color-warning)',
        //                     },
        //                 }),
        //                 h(
        //                     'span', {
        //                         slot: 'content',
        //                         style: {
        //                             whiteSpace: 'normal',
        //                             wordBreak: 'normal',
        //                             maxWidth: '300px',
        //                         },
        //                     },
        //                     `设备的【${
        //                         indexDetectionModeMap[
        //                             columnParams.statisticalList.indexDetectionMode
        //                             ]
        //                     }】作为指标计算结果`
        //                 ),
        //             ]
        //         ),
        //     ])
        // },
      },
      {
        title: '原因',
        key: 'errorCodeName',
        minWidth: 120,
        tooltip: true,
      },
      {
        minWidth: 150,
        title: '设备标签',
        slot: 'tagNames',
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VIDEO_OSD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '监控点位类型',
        key: 'sbdwlxText',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
        noShowTooltip: true,
        // renderHeader: (h, params) => {
        //     return h('div', [
        //         h('span', '检测结果'),
        //         h(
        //             'Tooltip', {
        //                 props: {
        //                     transfer: true,
        //                     placement: 'bottom',
        //                 },
        //                 style: {
        //                     verticalAlign: 'middle'
        //                 },
        //             },
        //             [
        //                 h('i', {
        //                     class: 'icon-font icon-wenhao ml-xs f-12',
        //                     style: {
        //                         width: '13px',
        //                         verticalAlign: 'top',
        //                         color: 'var(--color-warning)',
        //                     },
        //                 }),
        //                 h(
        //                     'span', {
        //                         slot: 'content',
        //                         style: {
        //                             whiteSpace: 'normal',
        //                             wordBreak: 'normal',
        //                             maxWidth: '300px',
        //                         },
        //                     },
        //                     `设备的【${
        //                         indexDetectionModeMap[
        //                             columnParams.statisticalList.indexDetectionMode
        //                             ]
        //                     }】作为指标计算结果`
        //                 ),
        //             ]
        //         ),
        //     ])
        // },
      },
      {
        title: '原因',
        key: 'errorCodeName',
        minWidth: 120,
        tooltip: true,
      },
      {
        minWidth: 150,
        title: '设备标签',
        slot: 'tagNames',
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VIDEO_CLOCK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '监控点位类型',
        key: 'sbdwlxText',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
        // renderHeader: (h, params) => {
        //     return h('div', [
        //         h('span', '检测结果'),
        //         h(
        //             'Tooltip', {
        //                 props: {
        //                     transfer: true,
        //                     placement: 'bottom',
        //                 },
        //                 style: {
        //                     verticalAlign: 'middle'
        //                 },
        //             },
        //             [
        //                 h('i', {
        //                     class: 'icon-font icon-wenhao ml-xs f-12',
        //                     style: {
        //                         width: '13px',
        //                         verticalAlign: 'top',
        //                         color: 'var(--color-warning)',
        //                     },
        //                 }),
        //                 h(
        //                     'span', {
        //                         slot: 'content',
        //                         style: {
        //                             whiteSpace: 'normal',
        //                             wordBreak: 'normal',
        //                             maxWidth: '300px',
        //                         },
        //                     },
        //                     `设备的【${
        //                         indexDetectionModeMap[
        //                             columnParams.statisticalList.indexDetectionMode
        //                             ]
        //                     }】作为指标计算结果`
        //                 ),
        //             ]
        //         ),
        //     ])
        // },
      },
      {
        title: '原因',
        key: 'errorCodeName',
        minWidth: 120,
        tooltip: true,
      },
      {
        title: '设备时间',
        key: 'startTime',
        minWidth: 150,
        tooltip: true,
      },
      {
        title: '对应标准时间',
        key: 'ntpTime',
        minWidth: 150,
        tooltip: true,
      },
      {
        minWidth: 150,
        title: '设备标签',
        slot: 'tagNames',
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VIDEO_PASS: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
        // renderHeader: (h, params) => {
        //     return h('div', [
        //         h('span', '检测结果'),
        //         h(
        //             'Tooltip', {
        //                 props: {
        //                     transfer: true,
        //                     placement: 'bottom',
        //                 },
        //                 style: {
        //                     verticalAlign: 'middle'
        //                 },
        //             },
        //             [
        //                 h('i', {
        //                     class: 'icon-font icon-wenhao ml-xs f-12',
        //                     style: {
        //                         width: '13px',
        //                         verticalAlign: 'top',
        //                         color: 'var(--color-warning)',
        //                     },
        //                 }),
        //                 h(
        //                     'span', {
        //                         slot: 'content',
        //                         style: {
        //                             whiteSpace: 'normal',
        //                             wordBreak: 'normal',
        //                             maxWidth: '300px',
        //                         },
        //                     },
        //                     `设备的【${
        //                         indexDetectionModeMap[
        //                             columnParams.statisticalList.indexDetectionMode
        //                             ]
        //                     }】作为指标计算结果`
        //                 ),
        //             ]
        //         ),
        //     ])
        // },
      },
      {
        title: '原因',
        key: 'reason',
        minWidth: 120,
        tooltip: true,
      },
      {
        minWidth: 150,
        title: '设备标签',
        slot: 'tagNames',
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VIDEO_HISTORY_COMPLETE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '设备状态 ',
        slot: 'phyStatus',
        minWidth: 120,
      },
      {
        title: '存储类型',
        key: 'storageTypeName',
        align: 'left',
        tooltip: true,
        minWidth: 120,
      },
      {
        title: '录像完整天数',
        key: 'completeDay',
        slot: 'completeDay',
        align: 'left',
        tooltip: true,
        minWidth: 120,
        sortable: 'custom',
      },
      {
        title: '录像缺失天数',
        key: 'hiatusDay',
        slot: 'hiatusDay',
        align: 'left',
        tooltip: true,
        minWidth: 120,
        sortable: 'custom',
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        minWidth: 120,
        // renderHeader: (h, params) => {
        //     return h('div', [
        //         h('span', '检测结果'),
        //         h(
        //             'Tooltip', {
        //                 props: {
        //                     transfer: true,
        //                     placement: 'bottom',
        //                 },
        //                 style: {
        //                     verticalAlign: 'middle'
        //                 },
        //             },
        //             [
        //                 h('i', {
        //                     class: 'icon-font icon-wenhao ml-xs f-12',
        //                     style: {
        //                         width: '13px',
        //                         verticalAlign: 'top',
        //                         color: 'var(--color-warning)',
        //                     },
        //                 }),
        //                 h(
        //                     'span', {
        //                         slot: 'content',
        //                         style: {
        //                             whiteSpace: 'normal',
        //                             wordBreak: 'normal',
        //                             maxWidth: '300px',
        //                         },
        //                     },
        //                     `设备的【${
        //                         indexDetectionModeMap[
        //                             columnParams.statisticalList.indexDetectionMode
        //                             ]
        //                     }】作为指标计算结果`
        //                 ),
        //             ]
        //         ),
        //     ])
        // },
      },
      {
        title: '设备标签',
        slot: 'tagNames',
        minWidth: 150,
      },
    ],
    // 人像图库考核
    FACE_ONLINE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '监控点位类型',
        key: 'sbdwlxText',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      { title: '当日抓拍数量', key: 'total', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    FACE_CLOCK_QUALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'total',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'unqualifiedNum',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    FACE_DELAY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'total',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'unqualifiedNum',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    FACE_QUALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'total',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'unqualifiedNum',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    // 车辆图库考核
    VEHICLE_ONLINE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '监控点位类型',
        key: 'sbdwlxText',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      { title: '当日抓拍数量', key: 'total', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VEHICLE_CLOCK_QUALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'deviceOrgCodeName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'testPictureCount',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'clockErrCount',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VEHICLE_DELAY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'deviceOrgCodeName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'testPictureCount',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'uploadTimeOutCount',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VEHICLE_QUALITY: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'deviceOrgCodeName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'testPictureCount',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'urlNotUseCount',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],

    FACE_OFFLINE_STAT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', slot: 'phyStatus', tooltip: true, minWidth: 120 },
      { title: '本月累计离线天数', slot: 'offLineTotalOfM', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '本月最大连续离线天数', slot: 'serialOffLineOfM', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
    ],
    VEHICLE_OFFLINE_STAT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', slot: 'phyStatus', tooltip: true, minWidth: 120 },
      { title: '本月累计离线天数', slot: 'offLineTotalOfM', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '本月最大连续离线天数', slot: 'serialOffLineOfM', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
    ],
    VIDEO_OFFLINE_STAT: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', slot: 'phyStatus', tooltip: true, minWidth: 120 },
      { title: '本月累计离线天数', slot: 'offLineTotalOfM', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '本月最大连续离线天数', slot: 'serialOffLineOfM', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
    ],
    VIDEO_CODE_STANDARD_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '视频流编码格式',
        key: 'videoCodeType',
        slot: 'videoCodeType',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
      },
      {
        title: '原因',
        key: 'errorReason',
        minWidth: 120,
        tooltip: true,
      },
      {
        minWidth: 150,
        title: '设备标签',
        slot: 'tagNames',
      },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VEHICLE_DEVICE_CONNECT_INTERNET: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        slot: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '设备状态 ', slot: 'phyStatus', minWidth: 120 },
      { title: '当日抓拍数量', key: 'total', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      { minWidth: 150, title: '设备标签', slot: 'tagNames' },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    FACE_DEVICE_CONNECT_INTERNET: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        slot: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '设备状态 ', slot: 'phyStatus', minWidth: 120 },
      { title: '当日抓拍数量', key: 'total', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      { minWidth: 150, title: '设备标签', slot: 'tagNames' },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    VEHICLE_CATALOGUE_SAME: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
      },
      {
        title: '检测时间',
        key: 'createTime',
        align: 'left',
        tooltip: true,
        minWidth: 120,
      },
    ],
    FACE_CATALOGUE_SAME: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '行政区划',
        key: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: `检测结果`,
        key: 'qualified',
        slot: 'qualified',
        width: 120,
      },
      {
        title: '检测时间',
        key: 'createTime',
        align: 'left',
        tooltip: true,
        minWidth: 120,
      },
    ],
    // 人体视图考核
    BODY_ONLINE_RATE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '监控点位类型',
        key: 'sbdwlxText',
        align: 'left',
        tooltip: true,
        minWidth: 130,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      { title: '当日抓拍数量', key: 'imageNum', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    BODY_UPLOAD: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'total',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'unqualifiedNum',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    BODY_CLOCK: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        key: 'deviceId',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
      {
        title: '检测图片数量',
        key: 'total',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      {
        title: '不合格图片数量',
        key: 'unqualifiedNum',
        slot: 'unqualifiedNum',
        tooltip: true,
        sortable: 'custom',
        minWidth: 200,
      },
      {
        title: '合格率',
        key: 'qualifiedRate',
        slot: 'qualifiedRate',
        tooltip: true,
        sortable: 'custom',
        minWidth: 120,
      },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 60,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
    BODY_ACTIVE: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '设备编码',
        slot: 'deviceId',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        sortable: 'custom',
      },
      {
        title: '设备名称',
        key: 'deviceName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      {
        title: '组织机构',
        key: 'orgName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
      },
      { title: '监控点位类型', key: 'sbdwlxText', minWidth: 120 },
      { title: '设备状态 ', slot: 'phyStatus', minWidth: 120 },
      { title: '当日抓拍数量', key: 'imageNum', tooltip: true, sortable: 'custom', minWidth: 120 },
      { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
      { minWidth: 150, title: '设备标签', slot: 'tagNames' },
      {
        title: '操作',
        slot: 'option',
        align: 'left',
        tooltip: true,
        width: 80,
        className: 'table-action-padding', // 操作栏列-单元格padding设置
      },
    ],
  };
};
export { formItemData, initFormData, qualifiedColorConfig, getTableColumns };
