<!--
 * @Author: zhengmingming <EMAIL>
 * @Date: 2025-05-08 14:18:24
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-26 16:15:07
 * @FilePath: \icbd-view\src\views\outside-app\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <!-- <iframe
    ref="iframe"
    :src="iframeSrc"
    width="100%"
    height="100%"
    border="0"
    frameborder="0"
  ></iframe> -->
  <micro-app
    style="width: 100%; height: 100%"
    :name="appName"
    :url="iframeSrc"
    iframe
    router-mode="pure"
    keep-alive
    :data="data"
    @datachange="handleDataChange"
  ></micro-app>
</template>

<script>
import { mapState } from "vuex";
import { getToken } from "@/libs/configuration/util.common";
import { clearLoginToken } from "@/libs/request";
export default {
  data() {
    return {
      iframeSrc: "",
      appName: "",
      data: {},
    };
  },
  computed: {
    ...mapState("common", ["theme"]),
  },
  watch: {
    theme(val) {
      this.setIframeAppTheme(val);
    },
  },
  created() {
    this.data = {
      type: "init",
      token: getToken(),
      applicationCode,
    };
    this.appName = this.$route.name;
    this.iframeSrc = `${this.$route.meta.resourceUrl}`;
  },
  mounted() {
    // this.addEventListenerIframeMessage();
  },
  methods: {
    handleDataChange(e) {
      this.dealIframeAppAction(e.detail.data);
    },
    setIframeAppTheme(theme) {
      this.data = {
        type: "theme",
        theme,
      };
    },
    dealIframeAppAction({ type }) {
      switch (type) {
        case "logOut":
          clearLoginToken();
          break;
      }
    },
    addEventListenerIframeMessage() {
      const iframe = this.$refs.iframe;
      window.addEventListener("message", function (event) {
        if (event.source === iframe.contentWindow) {
          dealIframeAppAction(event.data);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped></style>
