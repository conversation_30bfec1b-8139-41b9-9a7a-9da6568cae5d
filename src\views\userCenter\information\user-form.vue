<template>
  <ui-modal
    v-model="visible"
    :styles="styles"
    title="修改信息"
    @onCancel="visible = false"
    @onOk="commit"
  >
    <Form
      ref="userForm"
      :label-width="100"
      :rules="ruleValidate"
      :model="userForm"
    >
      <Row>
        <Col span="12">
          <FormItem label="头像" prop="avatar">
            <upload-img
              ref="upload"
              :multiple-num="1"
              :default-list="defaultList"
              class="upload-img"
              @successPut="uploadLogoSuccess"
            >
            </upload-img>
          </FormItem>
          <FormItem label="姓名" prop="name">
            <Input
              v-model="userForm.name"
              class="dialog-input"
              type="text"
              placeholder="请输入姓名"
            ></Input>
          </FormItem>
          <FormItem label="账号" prop="username">
            <Input
              :disabled="true"
              v-model="userForm.username"
              class="dialog-input"
              type="text"
              placeholder="请输入账号"
            ></Input>
          </FormItem>
          <FormItem class="form-item gender-radio" label="性别" prop="sex">
            <div class="custom-password">
              <RadioGroup v-model="userForm.sex">
                <Radio label="0">男</Radio>
                <Radio label="1">女</Radio>
              </RadioGroup>
            </div>
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="手机号" prop="phoneNumber">
            <Input
              v-model="userForm.phoneNumber"
              class="dialog-input"
              type="tel"
              maxlength="11"
              placeholder="请输入手机号"
            ></Input>
          </FormItem>
          <FormItem label="用户级别" prop="shortNumber">
            <Input
              v-model="userForm.shortNumber"
              :disabled="true"
              class="dialog-input"
              type="text"
              placeholder="请输入用户级别"
            ></Input>
          </FormItem>
          <FormItem label="邮箱" prop="email">
            <Input
              v-model="userForm.email"
              class="dialog-input"
              type="email"
              placeholder="请输入邮箱"
            ></Input>
          </FormItem>
        </Col>
      </Row>
    </Form>
  </ui-modal>
</template>
<script>
import md5 from "md5";
import {
  checkPwd,
  checkCardId,
  checkPhone,
  checkEmail,
  checkShortNumber,
} from "@/libs/configuration/util.common";
import { getPersonalInfo, updatePersonalInfo } from "@/api/user";
import { mapActions } from "vuex";

export default {
  name: "UserForm",
  components: {
    UploadImg: require("@/components/upload-img").default,
  },
  props: {
    value: {
      default: false,
      type: Boolean,
    },
    info: {
      default: () => {},
      type: Object,
    },
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (
        this.userForm.passwordType === 1 &&
        !checkPwd(value) &&
        !this.userForm.isEdit
      ) {
        callback(new Error("请输入大写或小写字母开头包含数字的8-20位密码"));
      } else {
        callback();
      }
    };
    const validateCardId = (rule, value, callback) => {
      if (checkCardId(value)) {
        callback();
      } else {
        callback(new Error("请输入正确的身份证号"));
      }
    };
    const validatePhoneNumber = (rule, value, callback) => {
      if (value === "" || checkPhone(value)) {
        callback();
      } else {
        callback(new Error("请输入正确的手机号"));
      }
    };
    const validateEmail = (rule, value, callback) => {
      if (value === "" || checkEmail(value)) {
        callback();
      } else {
        callback(new Error("请输入正确的邮箱"));
      }
    };
    const validateShortNumber = (rule, value, callback) => {
      if (value === "" || checkShortNumber(value)) {
        callback();
      } else {
        callback(new Error("请输入3-6位数字短号"));
      }
    };
    return {
      styles: {
        width: "5rem",
      },
      defaultPassword: "",
      userForm: {
        avatar: "",
        name: "",
        username: "",
        sex: "0",
        passwordType: undefined,
        password: "",
        workCode: "",
        cardId: "",
        phoneNumber: "",
        shortNumber: "",
        email: "",
      },
      ruleValidate: {
        name: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        username: [{ required: true, message: "请输入账号", trigger: "blur" }],
        sex: [{ required: true, message: "请选择性别", trigger: "blur" }],
        passwordType: [
          { required: true, message: "请选择密码", trigger: "blur" },
        ],
        password: [{ validator: validatePass, trigger: "blur" }],
        workCode: [{ required: true, message: "请输入警号", trigger: "blur" }],
        cardId: [
          { required: true, message: "请输入身份证号", trigger: "blur" },
          { validator: validateCardId, trigger: "blur" },
        ],
        phoneNumber: [{ validator: validatePhoneNumber, trigger: "blur" }],
        // shortNumber: [{ validator: validateShortNumber, trigger: 'blur' }],
        email: [{ validator: validateEmail, trigger: "blur" }],
      },
      roleList: [],
      organizationList: [],
      dataPermissionsTree: [],
      showPassword: false,
      visible: false,
    };
  },
  computed: {
    defaultList() {
      return this.userForm.avatar ? [this.userForm.avatar] : [];
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.getPersonalInfo();
        this.resetFormFields();
      }
    },
    visible(val) {
      this.$emit("input", val);
    },
  },
  methods: {
    ...mapActions({
      getUserInfo: "getUserInfo",
    }),
    async commit() {
      const valid = await this.$refs["userForm"].validate((valid) => {});
      if (!valid) return;
      const form = JSON.parse(JSON.stringify(this.userForm));
      if (form.password) form.password = md5(form.password);
      updatePersonalInfo({ ...form })
        .then((res) => {
          this.$Message.success(res.msg);
          this.getUserInfo();
        })
        .finally(() => {
          this.visible = false;
        });
    },
    resetFormFields() {
      this.$nextTick(() => {
        this.$refs["userForm"].resetFields();
      });
    },
    uploadLogoSuccess(val) {
      if (val && val.length) {
        this.userForm.avatar = val[0];
      } else {
        this.userForm.avatar = "";
      }
    },
    async getPersonalInfo() {
      getPersonalInfo({ id: this.info.id }).then((res) => {
        const { data } = res;
        this.userForm = {
          ...data,
          dataScopeOrgList: undefined,
          roleList: undefined,
          orgList: undefined,
        };
      });
    },
  },
};
</script>
<style lang="less" scoped>
.upload-img {
  width: 120px;
  /deep/ .ivu-spin-fix {
    .ivu-spin-main {
      left: 33%;
    }
  }
}
.password-tips {
  //color: @warningColor;
  cursor: pointer;
}
.warning-tips {
  color: #e99e53;
  line-height: 18px;
  margin-top: 5px;
  font-size: 12px;
}
.default-password {
  display: inline-flex;
  align-items: center;
}
.password-item {
  display: flex;
}
.password-form-item {
  margin: 0 !important;
}
.custom-password {
  display: flex;
  height: 34px;
  align-items: center;
  .custom-password-input {
    width: 174px;
  }
}
.custom-password .ivu-radio-wrapper {
  height: 34px;
  line-height: 34px;
}
.gender-radio .ivu-radio-wrapper,
.default-password {
  margin-right: 30px;
}
</style>
