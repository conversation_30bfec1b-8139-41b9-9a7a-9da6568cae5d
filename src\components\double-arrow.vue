<template>
  <div class="double-arrow"></div>
</template>
<script>
export default {
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.double-arrow {
  width: 15px;
  height: 10px;
  background: var(--color-active);
  position: relative;
  &::before,
  &::after {
    position: absolute;
    content: '';
    border: 10px solid transparent;
  }
  &::before {
    border-right-color: var(--color-active);
    left: -18px;
    top: -4px;
  }
  &::after {
    border-left-color: var(--color-active);
    right: -18px;
    top: -4px;
  }
}
</style>
