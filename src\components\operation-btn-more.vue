<template>
  <Poptip
    v-if="!moreBtnHide"
    v-model="visible"
    placement="bottom-end"
    transfer
    popper-class="custom-transfer-poptip"
    :offset="18"
  >
    <!-- <Button v-if="!moreBtnHide" type="text" class="operatbtn font-weight color-table-btn-more">···</Button> -->
    <span v-if="!moreBtnHide" class="operatbtn font-weight color-table-btn-more pointer">···</span>
    <div slot="content">
      <slot name="content">
        <ul class="operation">
          <li
            v-for="(item, index) in btnList"
            :key="'btn' + index"
            :class="[item.disabled ? 'btn-disabled' : '']"
            @click="handle(item)"
          >
            {{ item.name }}
          </li>
        </ul>
      </slot>
      <slot name="more"> </slot>
    </div>
  </Poptip>
</template>
<script>
export default {
  props: {
    moreBtnHide: {
      type: Boolean,
      default: false,
    }, // 是否隐藏更多按钮
    btnList: {
      type: Array,
      default: () => [],
    },
    rowData: {
      type: Object,
      default: () => {},
    }, // 行数据
  },
  data() {
    return {
      visible: false,
    };
  },
  created() {},
  methods: {
    handle(item) {
      if (item.disabled) return;
      this.visible = false;
      this.$emit('handleOperations', {
        data: this.rowData,
        methods: item.funct,
      });
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.operation {
  li {
    padding: 6px 18px;
    color: var(--color-table-header-th);
    cursor: pointer;
    &:hover {
      color: var(--color-select-item-active);
      background: var(--bg-select-item-active);
    }
  }
  .btn-disabled {
    color: var(--color-btn-primary-disabled);
    cursor: not-allowed;
    &:hover {
      color: var(--color-btn-primary-disabled);
      background: none;
    }
  }
}
</style>
<style lang="less">
.operation {
  @{_deep} .custom-transfer-poptip {
    right: 35px !important;
  }
}
</style>
