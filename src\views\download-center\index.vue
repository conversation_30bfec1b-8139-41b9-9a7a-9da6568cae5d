<template>
  <div class="auto-fill download-center">
    <div class="over-flow search-module">
      <ui-label class="inline" label="文件名称">
        <Input class="width-lg" v-model="searchData.fileName" placeholder="请输入文件名称"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </ui-label>
      <p class="fr color-failed">已生成的文件系统保留7天，超出后自动删除。</p>
    </div>

    <div class="table-module auto-fill mt-sm">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #status="{ row }">
          <span
            :class="{
              'color-success': row.status === '2',
              'color-failed': row.status === '3',
            }"
            >{{ row.status | filterStatus }}</span
          >
        </template>
        <template #persistTime="{ row }">
          <span>{{ formatTime(row.persistTime) }}</span>
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              class="mr-md"
              v-if="row.status === '2'"
              :styles="{ color: '#DE990F', 'font-size': '14px' }"
              icon="icon-xiazai"
              content="下载"
              @click.native="downloadDoc(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: '#438CFF', 'font-size': '14px' }"
              icon="icon-shanchu"
              content="删除"
              @click.native="handleDelete(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </div>
</template>
<script>
import common from '@/config/api/common';
export default {
  name: 'downloadcenter',
  props: {},
  data() {
    return {
      loading: false,
      searchData: {
        fileName: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        {
          title: '文件名称',
          key: 'fileName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 300,
          title: '下载来源',
          key: 'source',
          align: 'left',
        },
        {
          width: 110,
          title: '文件生成状态',
          key: 'status',
          slot: 'status',
          align: 'left',
        },
        {
          width: 120,
          title: '剩余保留时长',
          key: 'persistTime',
          slot: 'persistTime',
          align: 'left',
        },
        {
          width: 160,
          title: '文件请求时间',
          key: 'createTime',
          align: 'left',
        },
        {
          width: 160,
          title: '文件生成时间',
          key: 'fileTime',
          align: 'left',
        },
        {
          width: 100,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      tableData: [],
    };
  },
  created() {
    this.init();
  },
  filters: {
    filterStatus(val) {
      switch (val) {
        case '0':
          return '未开始';
        case '1':
          return '进行中';
        case '2':
          return '已完成';
        case '3':
          return '生成失败';
      }
    },
  },
  activated() {
    this.init();
  },
  methods: {
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.$http.post(common.getDownloadCenterList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    formatTime(time) {
      if (!time) return;
      return this.$util.common.timeFn(new Date(time), 'dd天hh时mm分');
    },
    downloadDoc(row) {
      this.$util.common.transformBlob(row.url);
    },
    handleDelete(row) {
      this.$UiConfirm({
        content: `您要删除数据：${row.fileName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteDoc(row.id);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteDoc(id) {
      try {
        await this.$http.delete(common.delDownloadCenterList, {
          params: {
            id: id,
          },
        });
        this.$Message.success('删除成功');
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.download-center {
  background-color: var(--bg-content);
  .search-module {
    padding: 20px;
  }
  .table-module {
    padding: 0 20px;
  }
}
</style>
