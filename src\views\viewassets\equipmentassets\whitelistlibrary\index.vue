<template>
  <div class="outside-wrapper">
    <div class="category-management height-full">
      <device-table
        class="device-table"
        :table-data="tableData"
        :loading="deviceLoading"
        :total-count="totalCount"
        @addRelativeDeviceShow="selectCamera"
        @removeInit="removeDeviceForCategory"
        @search="search"
      ></device-table>
    </div>
    <choose-device
      ref="ChooseDevice"
      node-key="deviceId"
      :table-columns="tableColumns"
      :load-data="leftData"
      :search-conditions="searchConditions"
      :need-handle="false"
      @getDeviceIdList="getDeviceIdList"
      @getOrgCode="getOrgCode"
    >
      <template #search-header>
        <search-list
          ref="SearchList"
          :dict-data="dictData"
          @startSearch="startSearch"
          @changeTime="changeTime"
        ></search-list>
      </template>
      <template #checkStatusText="{ row }">
        <span class="statustag" :style="{ 'background-color': checktSatusBgcolor[row.checkStatus] }">
          {{ row.checkStatusText }}
        </span>
      </template>
    </choose-device>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  name: 'whitelistlibrary',
  props: {},
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        sourceId: '',
        sbdwlx: '',
        sbgnlx: '',
        checkStatuses: [],
        phyStatus: '',
        pageNumber: 1,
        pageSize: 20,
      },
      tableData: [],
      deviceLoading: false,
      totalCount: 0,
      dictData: {},
      searchConditions: {},
      dateTime: '',
      tableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 100,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        /*        { title: `${this.global.filedEnum.sbdwlx}`, key: 'sbdwlxText', align: 'left', width: 130, tooltip: true },
        { title: `${this.global.filedEnum.sbcjqy}`, key: 'sbgnlxText', align: 'left', width: 130, tooltip: true },*/
        {
          title: '检测状态',
          slot: 'checkStatusText',
          align: 'left',
          width: 90,
          minWidth: 200,
          tooltip: true,
        },
      ],
      leftData: (parameter) => {
        return this.$http
          .post(equipmentassets.getNotInwhitePageList, Object.assign(this.searchConditions, parameter))
          .then((res) => {
            if (res.data.data.entities.length) {
              res.data.data.entities.forEach((item) => {
                switch (item.checkStatus) {
                  case '1000':
                    item.checkStatus = '0';
                    item.checkStatusText = '待检测';
                    break;
                  case '0000':
                    item.checkStatus = '1';
                    item.checkStatusText = '合格';
                    break;
                  default:
                    item.checkStatus = '2';
                    item.checkStatusText = '不合格';
                    break;
                }
              });
            }
            return res.data;
          });
      },
      checktSatusBgcolor: ['var(--color-warning)', 'var(--color-success)', 'var(--color-warning)'],
      selectedList: [],
    };
  },
  created() {
    this.getWhiteList();
  },
  methods: {
    // 关联设备到组织机构目录下
    async addWhiteList() {
      try {
        const params = {
          ids: this.selectedList,
          dateTime: this.dateTime,
        };
        this.loading = true;
        let { data } = await this.$http.post(equipmentassets.addWhiteList, params);
        this.addDeviceShow = false;
        this.$Message.success(data.msg);
        this.initDevice();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search(searchData) {
      Object.assign(this.searchData, searchData);
      this.initDevice();
    },
    initDevice() {
      this.getWhiteList();
    },
    getTextByJs(arr) {
      let str = '';
      for (let i = 0; i < arr.length; i++) {
        str += arr[i] + ',';
      }
      //去掉最后一个逗号(如果不需要去掉，就不用写)
      if (str.length > 0) {
        str = str.substr(0, str.length - 1);
      }
      return str;
    },

    // 移除相关目录设备
    async removeDeviceForCategory(devices) {
      const str = this.getTextByJs(devices);
      try {
        let res = await this.$http.delete(`${equipmentassets.removewhiteList + str}`);
        this.$Message.success(res.data.msg);
        this.initDevice();
      } catch (err) {
        console.log(err);
      }
    },
    // 获取白名单列表
    async getWhiteList() {
      try {
        this.deviceLoading = true;
        let params = Object.assign({}, this.searchData);
        let { data } = await this.$http.post(equipmentassets.getWhiteList, params);
        this.tableData = data.data.entities;
        this.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.deviceLoading = false;
      }
    },
    selectCamera() {
      this.dictData = {
        sxjgnlx_receive: this.propertySearchSxjgnlx,
        propertySearch_sbdwlx: this.propertySearchLbdwlx,
        checkStatus: this.checkStatus,
        sourceList: this.sourceList,
        phystatusList: this.phystatusList,
      };
      this.$refs.ChooseDevice.init();
    },
    async startSearch(data) {
      this.searchConditions = Object.assign(this.searchConditions, data[0]);
      if (data[1]) {
        this.searchConditions.checkStatuses = [];
      }
      await this.$refs.ChooseDevice.getList();
    },
    getOrgCode(code) {
      this.searchConditions.orgCodeList = code;
      this.$refs.SearchList.reset();
    },
    getDeviceIdList(data) {
      this.selectedList = data.chooseIds;
      this.addWhiteList();
    },
    changeTime(time) {
      this.dateTime = time;
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  components: {
    DeviceTable: require('./modules/device-table.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device.vue').default,
    SearchList: require('./modules/search-list.vue').default,
  },
};
</script>
<style lang="less" scoped>
.outside-wrapper {
  overflow: hidden;
  position: relative;
}
.category-management {
  background-color: var(--bg-content);
  .self-tree-box {
    position: relative;
    .icon-box {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .content-ul {
    padding-top: 10px;
    .content-li {
      height: 25px;
      line-height: 25px;
      padding: 0 10px;
      border: 1px solid transparent;
      &:hover {
        background: @bg-darkblue-block;
      }
      &.active {
        border-color: #1a82be;
        background: transparent;
      }
      .icon-p {
        position: relative;
        .icon-shanchu {
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }
  }
  .addCategoryButton {
    width: 100%;
    margin-bottom: 10px;
  }
  .category-content {
    background-color: var(--bg-content);
    width: 320px;
    border-right: 0.005208rem solid var(--border-color);
    height: 100%;
    padding: 20px 10px;
    .category-box {
      height: 100%;
      width: 100%;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 0 10px;
      @{_deep}.ivu-collapse {
        border: 0;
      }
      @{_deep}.ivu-collapse-header {
        background-color: var(--bg-content);
        color: #fff;
        border: 0;
        padding-left: 0px;
      }
      @{_deep}.ivu-collapse-content {
        border: 0;
        background-color: var(--bg-content);
        padding: 0;
        width: 100%;
        color: #fff;
        overflow: initial !important;
        .ivu-collapse-content-box {
          padding-top: 0;
          padding-bottom: 10px;
        }
      }
      @{_deep}.ivu-collapse-item {
        border: 0;
      }
      @{_deep}.el-tree-node__content {
        border: 1px solid transparent;
        //background: #1b3b65;
      }
      @{_deep}.active-tree {
        .el-tree-node {
          &.is-current {
            > .el-tree-node__content {
              border-color: #1a82be;
              background: #1b3b65;
            }
          }
        }
      }
      .category-tree {
        padding: 0 10px;
      }
    }
  }

  .device-table {
    // float: left;
    // width: calc(100% - 320px);
    // margin-left: 20px;
    height: 100%;
    padding: 0 20px;
  }
  .special-input {
    margin-left: 20px;
    @{_deep}.ivu-input {
      height: 25px;
      line-height: 25px;
      width: 253px;
      &&::-webkit-input-placeholder {
        font-size: 12px !important;
      }
    }
  }
}
.statustag {
  .flex;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
