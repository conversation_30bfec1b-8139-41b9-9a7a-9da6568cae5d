<!--
    * @FileDescription: 地图-操作选项
    * @Author: H
    * @Date: 2023/09/26
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-07 15:54:39
 -->
<template>
  <div ref="menu" class="operationWin">
    <!-- 一级菜单 -->
    <div class="menu">
      <div
        title="人脸"
        class="nav-item face"
        :class="{ 'nav-item-active': isShowSecMenu.face.isHover }"
        @mouseenter="showSecondMenu('face')"
        @mouseleave="showSecondMenu('face')"
        @click="openMenu('rl')"
      >
        <img
          class="face-img default_img"
          src="@/assets/img/map/operation/icon_face.png"
        />
        <img
          class="face-img hover_img"
          src="@/assets/img/map/operation/icon_face_hover.png"
        />
        <!-- 二级菜单 -->
        <div
          class="second-item face-second-rljs"
          title="人脸检索"
          :class="[
            { 'second-item-active': isShowSecMenu.face.secondMenu.rljs },
            { control: !coronaState.face },
          ]"
          v-show="isShowSecMenu.face.isHover"
          @click="openMenu('rljs')"
          @mouseenter="activeSecondMenu('rljs', 'face')"
          @mouseleave="activeSecondMenu('rljs', 'face')"
        >
          <!-- <img class="icon-img" src="@/assets/images/map/operation/ico-face-rljs.png" /> -->
        </div>
      </div>
      <div
        title="车辆"
        class="nav-item car"
        :class="{ 'nav-item-active': isShowSecMenu.car.isHover }"
        @mouseenter="showSecondMenu('car')"
        @mouseleave="showSecondMenu('car')"
        @click="openMenu('car')"
      >
        <img
          class="car-img default_img"
          src="@/assets/img/map/operation/icon_car.png"
        />
        <img
          class="car-img hover_img"
          src="@/assets/img/map/operation/icon_car_hover.png"
        />
      </div>
      <div
        title="人体"
        class="nav-item body"
        :class="{ 'nav-item-active': isShowSecMenu.body.isHover }"
        @mouseenter="showSecondMenu('body')"
        @mouseleave="showSecondMenu('body')"
        @click="openMenu('body')"
      >
        <img
          class="body-img default_img"
          src="@/assets/img/map/operation/icon_body.png"
        />
        <img
          class="body-img hover_img"
          src="@/assets/img/map/operation/icon_body_hover.png"
        />
      </div>
      <div
        title="非机动车"
        class="nav-item nonMmotor"
        :class="{ 'nav-item-active': isShowSecMenu.nonMmotor.isHover }"
        @mouseenter="showSecondMenu('nonMmotor')"
        @mouseleave="showSecondMenu('nonMmotor')"
        @click="openMenu('nonMmotor')"
      >
        <img
          class="nonMmotor-img default_img"
          src="@/assets/img/map/operation/icon_nonMmotor.png"
        />
        <img
          class="nonMmotor-img hover_img"
          src="@/assets/img/map/operation/icon_nonMmotor_hover.png"
        />
      </div>
      <div
        title="关闭"
        class="nav-item close"
        :class="{ 'nav-item-active': isShowSecMenu.close.isHover }"
        @click.stop="cancalSelect"
        @mouseenter="showSecondMenu('close')"
        @mouseleave="showSecondMenu('close')"
      >
        <img
          class="close-img default_img"
          src="@/assets/img/map/operation/icon_close.png"
        />
        <img
          class="close-img hover_img"
          src="@/assets/img/map/operation/icon_close_hover.png"
        />
      </div>
      <div
        title="RFID"
        class="nav-item rfid"
        :class="{ 'nav-item-active': isShowSecMenu.rfid.isHover }"
        @mouseenter="showSecondMenu('rfid')"
        @mouseleave="showSecondMenu('rfid')"
        @click="openMenu('rfid')"
      >
        <img
          class="rfid-img default_img"
          src="@/assets/img/map/operation/icon_rfid.png"
        />
        <img
          class="rfid-img hover_img"
          src="@/assets/img/map/operation/icon_rfid_hover.png"
        />
      </div>
      <div
        title="WIFI"
        class="nav-item wifi"
        :class="{ 'nav-item-active': isShowSecMenu.wifi.isHover }"
        @mouseenter="showSecondMenu('wifi')"
        @mouseleave="showSecondMenu('wifi')"
        @click="openMenu('wifi')"
      >
        <img
          class="wifi-img default_img"
          src="@/assets/img/map/operation/icon_wifi.png"
        />
        <img
          class="wifi-img hover_img"
          src="@/assets/img/map/operation/icon_wifi_hover.png"
        />
      </div>
      <div
        title="ETC"
        class="nav-item etc"
        :class="{ 'nav-item-active': isShowSecMenu.ETC.isHover }"
        @mouseenter="showSecondMenu('ETC')"
        @mouseleave="showSecondMenu('ETC')"
        @click="openMenu('etc')"
      >
        <img
          class="etc-img default_img"
          src="@/assets/img/map/operation/icon_etc.png"
        />
        <img
          class="etc-img hover_img"
          src="@/assets/img/map/operation/icon_etc_hover.png"
        />
      </div>
      <div
        title="视频"
        class="nav-item video"
        :class="{ 'nav-item-active': isShowSecMenu.video.isHover }"
        @mouseenter="showSecondMenu('video')"
        @mouseleave="showSecondMenu('video')"
      >
        <img
          class="video-img default_img"
          src="@/assets/img/map/operation/icon_video.png"
        />
        <img
          class="video-img hover_img"
          src="@/assets/img/map/operation/icon_video_hover.png"
        />
        <!-- 二级菜单 -->
        <div
          class="second-item video-second-round"
          title="轮巡"
          :class="[
            { 'second-item-active': isShowSecMenu.video.secondMenu.round },
            { control: !coronaState.camera },
          ]"
          v-show="isShowSecMenu.video.isHover"
          @click="openMenu('round')"
          @mouseenter="activeSecondMenu('round', 'video')"
          @mouseleave="activeSecondMenu('round', 'video')"
        >
          <img
            class="icon-img default_sec_img"
            src="@/assets/img/map/operation/icon_video_round.png"
          />
          <img
            class="icon-img hover_sec_img"
            src="@/assets/img/map/operation/icon_video_round_hover.png"
          />
        </div>
        <div
          class="second-item video-second-his"
          title="录像回放"
          :class="[
            { 'second-item-active': isShowSecMenu.video.secondMenu.his },
            { control: !coronaState.camera },
          ]"
          v-show="isShowSecMenu.video.isHover"
          @click="openMenu('his')"
          @mouseenter="activeSecondMenu('his', 'video')"
          @mouseleave="activeSecondMenu('his', 'video')"
        >
          <img
            class="icon-img default_sec_img"
            src="@/assets/img/map/operation/icon_video_his.png"
          />
          <img
            class="icon-img hover_sec_img"
            src="@/assets/img/map/operation/icon_video_his_hover.png"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ExpandScreen from "@/util/modules/extend-screen.js";
export default {
  name: "",
  components: {},
  props: {
    selectDeviceList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      coronaState: {
        camera: false,
        traffic: false,
        face: false,
        body: false,
        wifi: false,
        rfid: false,
        nonmotor: false,
        deviceIgnore: true,
        deviceGroup: true,
      },
      isShowSecMenu: {
        body: {
          isHover: false,
          secondMenu: {
            rtjs: false,
          },
          auth: {},
        },
        car: {
          isHover: false,
          secondMenu: {
            dwsc: false,
            pzfx: false,
            pcfx: false,
          },
          auth: {},
        },
        nonMmotor: {
          isHover: false,
          secondMenu: {
            dwsc: false,
            pzfx: false,
            pcfx: false,
          },
          auth: {},
        },
        rfid: {
          isHover: false,
          secondMenu: {
            search: false,
          },
          auth: {},
        },
        ETC: {
          isHover: false,
          secondMenu: {
            search: false,
          },
          auth: {},
        },
        taxi: {
          isHover: false,
          secondMenu: {
            gjpz: false,
            gpssearch: false,
          },
          auth: {},
        },
        video: {
          isHover: false,
          secondMenu: {
            camera: false, //实时视频
            round: false, //轮巡
            his: false, //历史视频
            realStr: false, // 实时结构化 !!权限待注入
            hisStr: false, // 历史结构化 !!权限待注入
          },
          auth: {},
        },
        wifi: {
          isHover: false,
          secondMenu: {
            imsi: false, //mac碰撞（名字有待商榷）
            mac: false, // mac数据仓库mac检索
          },
          auth: {},
        },
        face: {
          isHover: false,
          secondMenu: {
            pfcm: false,
            rypz: false,
            rljs: false,
            rlbj: false,
          },
          auth: {},
        },
        all: {
          isHover: false,
          secondMenu: {
            resscearch: false,
            deviceIgnore: false,
            deviceGroup: false,
          },
          auth: {},
          isDisabled: false,
        },
        close: {
          isHover: false,
          secondMenu: {},
          auth: {},
        },
      },
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    /**
     * 根据能力类型获取数据
     *
     */
    getDataByAbilityType(type) {
      var arr = [];
      for (let i = 0; i < this.selectDeviceList.length; i++) {
        let el = this.selectDeviceList[i];
        if (el.LayerType.indexOf(type) !== -1) {
          arr.push(el);
        }
      }
      return arr;
    },
    openMenu(menu) {
      const { layerCheckedNames } = this.$parent.$parent; // 图层展示
      let page = "";
      let queryParams = "";
      let qiangjiData = layerCheckedNames.includes("Camera_QiangJi")
        ? this.getDataByAbilityType("Camera_QiangJi")
        : [];
      let qiujiData = layerCheckedNames.includes("Camera_QiuJi")
        ? this.getDataByAbilityType("Camera_QiuJi")
        : [];
      let renlianData = layerCheckedNames.includes("Camera_Face")
        ? this.getDataByAbilityType("Camera_Face")
        : [];
      let vehicleData = layerCheckedNames.includes("Camera_Vehicle")
        ? this.getDataByAbilityType("Camera_Vehicle")
        : [];
      switch (menu) {
        case "rl":
          let faceData = [...qiangjiData, ...qiujiData, ...renlianData];
          page = "faceContent";
          queryParams = faceData;
          break;
        case "car":
          let carData = [...qiangjiData, ...qiujiData, ...vehicleData];
          page = "vehicleContent";
          queryParams = carData;
          break;
        case "body":
          let baceData = [...qiangjiData, ...qiujiData, ...renlianData];
          page = "humanBodyContent";
          queryParams = baceData;
          break;
        case "nonMmotor":
          let nonMmotorData = [...qiangjiData, ...qiujiData, ...vehicleData];
          page = "nonmotorVehicleContent";
          queryParams = nonMmotorData;
          break;
        case "wifi":
          let wifiData = layerCheckedNames.includes("Camera_Wifi")
            ? this.getDataByAbilityType("Camera_Wifi")
            : [];
          page = "wifiContent";
          queryParams = wifiData;
          break;
        case "rfid":
          let rfidData = layerCheckedNames.includes("Camera_RFID")
            ? this.getDataByAbilityType("Camera_RFID")
            : [];
          page = "RFIDContent";
          queryParams = rfidData;
          break;
        case "etc":
          let etcData = layerCheckedNames.includes("Camera_ETC")
            ? this.getDataByAbilityType("Camera_ETC")
            : [];
          page = "etcContent";
          queryParams = etcData;
          break;
        case "round":
          let cameraData = [...qiangjiData, ...qiujiData];
          if (!cameraData || !cameraData.length) {
            this.$Message.warning("当前选择无摄像机设备！");
            return;
          }
          ExpandScreen.exMonitorScreen(cameraData);
          return;
          break;
        case "his":
          let hisData = [...qiangjiData, ...qiujiData];
          if (!hisData || !hisData.length) {
            this.$Message.warning(
              "当前未选中任何摄像机！不可进行视频相关操作！"
            );
            return;
          }
          this.$emit("showHistory", hisData);
          return;
          break;
      }
      console.log(queryParams, "queryParams");

      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center?sectionName=${page}&noMenu=1`,
        query: {
          deviceList: queryParams.length
            ? JSON.stringify(queryParams)
            : undefined,
        },
      });
      window.open(href, "_blank");
    },
    // 取消
    cancalSelect() {
      this.isShowSecMenu["close"].isHover = false;
      this.$emit("cancalSelect");
    },
    showSecondMenu(type) {
      if (type && this.isShowSecMenu.hasOwnProperty(type)) {
        this.isShowSecMenu[type].isHover = !this.isShowSecMenu[type].isHover;
      }
    },
    activeSecondMenu(type, parentType) {
      if (parentType && this.isShowSecMenu.hasOwnProperty(parentType)) {
        this.isShowSecMenu[parentType].secondMenu[type] =
          !this.isShowSecMenu[parentType].secondMenu[type];
      }
    },
  },
};
</script>

<style lang="less" scoped>
.operationWin {
  position: relative;
  padding: 10px 28px 0px 0;
  height: 100%;
}
.menu {
  width: 212px;
  height: 212px;

  background: url("~@/assets/img/map/operation/operation-disk.png") no-repeat;
  background-size: 100% 100%;
  background-size: contain;
  .nav-item {
    position: absolute;
    // opacity: 0.8;
    width: 40px;
    height: 50px;
    background-color: transparent;
    cursor: pointer;
    // overflow: hidden;
  }
  .hover_img {
    display: none;
  }
  .nav-item-active {
    .default_img {
      display: none;
    }
    .hover_img {
      display: block;
    }
  }
  .face {
    top: 100px;
    left: 164px;
    .face-img {
      margin-left: -13px;
      margin-top: -5px;
    }
  }
  .car {
    top: 59px;
    left: 158px;
    .car-img {
      margin-top: -11px;
      margin-left: -12px;
    }
  }
  .body {
    top: 144px;
    left: 150px;
    .body-img {
      margin-left: -15px;
      margin-top: -8px;
    }
  }
  .nonMmotor {
    top: 145px;
    left: 22px;
    .nonMmotor-img {
      margin-left: -14px;
      margin-top: -11px;
    }
  }
  .close {
    top: 183px;
    left: 84px;
    .close-img {
    }
  }
  .rfid {
    top: 97px;
    left: 6px;
    .rfid-img {
      margin-top: -3px;
      margin-left: -14px;
    }
  }
  .wifi {
    top: 56px;
    left: 15px;
    .wifi-img {
      margin-top: -7px;
      margin-left: -15px;
    }
  }
  .etc {
    top: 20px;
    left: 131px;
    .etc-img {
      margin-top: -7px;
      margin-left: -15px;
    }
  }
  .video {
    top: 23px;
    left: 41px;
    .video-img {
      margin-top: -10px;
      margin-left: -12px;
    }
  }
  // 二级菜单
  .second-item {
    position: absolute;
    width: 70px;
    height: 70px;
    opacity: 1;

    .icon-img {
      position: absolute;
      // left: 18px;
      // height: 18px;
    }
    img {
      width: 70px;
      height: 70px;
    }
  }
  .hover_sec_img {
    display: none;
  }
  .second-item-active {
    .default_sec_img {
      display: none;
    }
    .hover_sec_img {
      display: block;
    }
  }
  // 车辆二级
  .video-second-round {
    top: -65px;
    left: -50px;
  }
  .video-second-his {
    left: 20px;
    top: -65px;
  }
}
</style>
