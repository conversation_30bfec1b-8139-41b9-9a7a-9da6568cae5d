<template>
  <ui-modal
    ref="modalChild"
    class="unqualified-modal"
    title="不合格原因"
    v-model="visible"
    width="600px"
    :footerHide="true"
  >
    <!-- 表格 -->
    <TableList
      class="tables"
      ref="infoList"
      :columns="tableColumns"
      :minusHeight="429"
      :loadData="loadDataList"
      :paging="false"
    >
    </TableList>
  </ui-modal>
</template>
<script>
import viewassets from '@/config/api/viewassets';
export default {
  components: {
    TableList: require('./tableList').default,
  },
  props: {},
  data() {
    return {
      visible: false,
      allCheck: false,
      searchData: { deviceId: '', fallbackDetailId: '' },
      selectRows: [],
      loadDataList: () => {
        return this.$http.post(viewassets.unqualifiedReason, this.searchData).then((res) => {
          let data = {
            data: {
              count: 1,
              entities: res.data.data,
              firstPage: true,
              lastPage: true,
              pageNumber: 1,
              pageSize: 20,
            },
          };
          return data;
        });
      },
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { width: 180, title: '不合格字段', key: 'checkColumnName' },
        { title: '不合格原因', key: 'errorMessage', tooltip: true },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    // 初始化
    open(row) {
      this.$nextTick(() => {
        this.visible = true;
        this.searchData = {
          deviceId: row.deviceId,
          fallbackDetailId: row.id,
        };
        this.$refs.infoList.info(true);
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .operation {
  margin: 2px 0px 12px 0px;
  height: 34px;
  line-height: 34px;
  .right-btn {
    button {
      margin-left: 12px;
    }
  }
}
.tables {
  min-height: 360px;
  max-height: 660px;
}
</style>
