import { clearStates, getDisabletStates } from "../util/graph-deal";
export function useRegisterBehavior(G6) {
  // 点击创建关联
  G6.registerBehavior("click-add-edge", {
    getDefaultCfg() {
      return {
        edge: null,
        addingEdge: false,
        source: {},
      };
    },
    // Set the events and the corresponding responsing function for this behavior
    getEvents() {
      return {
        "node:click": "onClick", // The event is canvas:click, the responsing function is onClick
        "node:contextmenu": "contextmenu",
        mousemove: "onMousemove", // The event is mousemove, the responsing function is onMousemove
        "edge:click": "onEdgeClick", // The event is edge:click, the responsing function is onEdgeClick
      };
    },
    // The responsing function for node:click defined in getEvents
    onClick(ev) {
      const node = ev.item;
      const model = node.getModel();

      if (this.source.id === model.id) {
        this.$Message.warning(`${this.source.label}【请勿关联自己】`);
        return;
      }

      this.graph.updateItem(this.edge, {
        target: model.id,
      });

      // 获取当前节点的所有edge
      const edges = node.getEdges().map((edge) => edge);
      const newEdges = [];
      edges.forEach((edge) => {
        const edgeModel = edge.getModel();
        // 移除相同来源的edge，并且存入新的edges中
        if (edgeModel.source === this.source.id) {
          newEdges.push(edgeModel);
          this.graph.removeItem(edge);
        }
      });
      // 重新计算弧度
      G6.Util.processParallelEdges(newEdges);
      // 根据新的弧度，重新创建之前移除的edge
      newEdges.forEach((newEdge) => {
        this.graph.addItem("edge", newEdge);
      });

      this.targetText = model.label;
      this.$Message.success(`${this.source.label}【连线至】${model.label}`);
      this.edge = null;
      this.addingEdge = false;
      this.graph.setMode("default");
      this.graph.removeBehaviors(["click-add-edge", "click-select"], "addEdge");
    },
    // The responsing function for mousemove defined in getEvents
    onMousemove(ev) {
      // The current position the mouse clicks
      const point = { x: ev.x, y: ev.y };
      if (this.addingEdge && this.edge) {
        // Update the end node to the current node the mouse clicks
        this.graph.updateItem(this.edge, {
          target: point,
        });
      }
    },
    // The responsing function for edge:click defined in getEvents
    onEdgeClick(ev) {
      const currentEdge = ev.item;
      if (this.addingEdge && this.edge === currentEdge) {
        this.graph.removeItem(this.edge);
        this.edge = null;
        this.addingEdge = false;
      }
      this.graph.removeBehaviors(["click-add-edge", "click-select"], "addEdge");
      this.graph.setMode("default");
    },
    contextmenu() {
      this.graph.removeItem(this.edge);
      this.edge = null;
      this.addingEdge = false;
      this.graph.removeBehaviors(["click-add-edge", "click-select"], "addEdge");
      this.graph.setMode("default");
    },
  });

  // 拖动节点同时移动关联节点
  G6.registerBehavior("drag-node-move-source-neighbors", {
    getDefaultCfg() {
      return {
        nodeStartX: 0,
        nodeStartY: 0,
      };
    },
    getEvents() {
      return {
        "node:dragstart": "onNodeDragstart",
        "node:dragend": "onNodeDragend",
      };
    },
    onNodeDragstart(e) {
      const model = e.item.get("model");
      this.nodeStartX = model.x;
      this.nodeStartY = model.y;
    },
    onNodeDragend(e) {
      const node = e.item;
      // 如果已经选中的节点大于2个，则其他关联节点不再跟随当前节点移动
      const nodes = this.graph.getNodes();
      const selectedNodes = nodes.filter((node) => node.hasState("selected"));
      if (selectedNodes.length > 1) return;
      // 获取当前节点的关联节点
      const sourceNeighborsNodes = node.getNeighbors();
      const model = node.get("model");
      const nodeEndX = model.x;
      const nodeEndY = model.y;

      const moveX = nodeEndX - this.nodeStartX;
      const moveY = nodeEndY - this.nodeStartY;
      this.graph.setAutoPaint(false);
      const nodeIds = [];
      sourceNeighborsNodes.forEach((neighborNode) => {
        const neighborNodeMode = neighborNode.get("model");
        if (!nodeIds.includes(neighborNodeMode.id)) {
          nodeIds.push(neighborNodeMode.id);
          // 获取关联节点的关联节点如果有则不跟随当前节点移动
          const neighborsOfNeighbors = neighborNode
            .getNeighbors()
            .map((neighbor) => neighbor.get("id"))
            .filter((item) => item !== model.id);

          if (neighborsOfNeighbors.length === 0) {
            this.graph.updateItem(neighborNode, {
              ...neighborNodeMode,
              x: neighborNodeMode.x + moveX,
              y: neighborNodeMode.y + moveY,
            });
          }
        }
      });
      this.graph.paint();
      this.graph.setAutoPaint(true);
    },
  });

  // 由于G6 activate-relations 数据量过大有性能问题 所以这里自定义一个鼠标移入高亮相邻节点
  G6.registerBehavior("activate-relations-custom", {
    getEvents() {
      return {
        "node:mouseenter": "onNodeMouseenter",
        "node:mouseleave": "onNodeMouseleave",
      };
    },
    onNodeMouseenter(e) {
      const item = e.item;
      const disabletStates = getDisabletStates();
      const states = item.getStates();
      const isDisabletState = states.some((el) => disabletStates.includes(el));
      if (isDisabletState) return false;
      this.graph.setAutoPaint(false);
      clearStates(this.graph, ["hover", "connectionHover"]);
      item.setState("hover", true);
      const edges = item.getEdges();
      edges.forEach((edge) => {
        this.graph.setItemState(edge.getTarget(), "hover", true);
        this.graph.setItemState(edge.getSource(), "hover", true);
        this.graph.setItemState(edge, "connectionHover", true);
        edge.toFront();
      });
      this.graph.paint();
      this.graph.setAutoPaint(true);
    },
    onNodeMouseleave() {
      clearStates(this.graph, ["hover", "connectionHover"]);
    },
  });

  return {};
}
