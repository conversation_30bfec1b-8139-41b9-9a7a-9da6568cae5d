/**
 * 一： 设备详情 弹框中的 表格配置内容
 */

// 活跃分析
const activeInitFormData = {
  deviceId: '',
  deviceName: '',
  isImportant: '',
  analyseResult: null,
  shotNum1: null,
  shotNum2: null,
  avgNum1: null,
  avgNum2: null,
  nearbyAvgNum1: null,
  nearbyAvgNum2: null,
  avgPercent1: null,
  avgPercent2: null,
  shotDowncount1: null,
  shotDowncount2: null,
};
const activeFormItemData = (params) => {
  return [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      width: 190,
      placeholder: '请选择设备重点类型',
      options: [
        { value: 0, label: '普通设备' },
        { value: 1, label: '重点设备' },
      ],
    },
    {
      type: 'select',
      key: 'analyseResult',
      label: '分析结果',
      placeholder: '请选择分析结果',
      // selectMutiple: true,
      options: params?.resultList || [],
    },
    {
      type: 'start-end-num',
      label: '抓拍总量',
      startKey: 'shotNum1',
      endKey: 'shotNum2',
    },
    {
      type: 'start-end-num',
      label: '日均抓拍',
      startKey: 'avgNum1',
      endKey: 'avgNum2',
    },
    {
      type: 'start-end-num',
      label: '附近设备日均抓拍',
      startKey: 'nearbyAvgNum1',
      endKey: 'nearbyAvgNum2',
    },
    {
      type: 'start-end-num',
      label: '日均占比',
      startKey: 'avgPercent1',
      endKey: 'avgPercent2',
    },
    {
      type: 'start-end-num',
      label: '抓拍突降次数',
      startKey: 'shotDowncount1',
      endKey: 'shotDowncount2',
    },
  ];
};
const activeTableColumns = (params) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '设备编码',
      key: 'deviceId',
      align: 'left',
      tooltip: true,
      minWidth: 120,
      sortable: 'custom',
    },
    {
      title: '设备名称',
      key: 'deviceName',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '组织机构',
      key: 'orgName',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '设备状态 ',
      key: 'phyStatus',
      slot: 'phyStatus',
      minWidth: 90,
    },
    {
      title: '近X天抓拍总量',
      key: 'shotTotalNum',
      slot: 'shotTotalNum',
      sortable: 'custom',
      renderHeader: (h) => {
        return <span>{params?.dayNum ? `近${params?.dayNum}天抓拍总量` : '近X天抓拍总量'}</span>;
      },
      minWidth: 100,
    },
    {
      title: '日均抓拍',
      key: 'avgNum',
      slot: 'avgNum',
      sortable: 'custom',
      minWidth: 90,
    },
    {
      title: '附近设备日均抓拍',
      key: 'nearbyAvgNum',
      slot: 'nearbyAvgNum',
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '日均占比',
      key: 'avgPercent',
      slot: 'avgPercent',
      sortable: 'custom',
      minWidth: 90,
    },
    {
      title: '抓拍突降次数',
      key: 'shotDownCount',
      slot: 'shotDownCount',
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '分析结果',
      key: 'analyseResult',
      tooltip: true,
      minWidth: 120,
      className: 'result-span',
    },
    {
      title: '设备标签',
      key: 'tagNames',
      slot: 'tagNames',
      minWidth: 120,
    },
  ];
};

// 离线统计
const offlineInitFormData = {
  deviceId: '',
  deviceName: '',
  isImportant: '',
  accOffline1: null,
  accOffline2: null,
  serialOffline1: null,
  serialOffline2: null,
};
const offlineFormItemData = () => {
  return [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      placeholder: '请选择设备重点类型',
      options: [
        { value: 0, label: '普通设备' },
        { value: 1, label: '重点设备' },
      ],
    },
    {
      type: 'start-end-num',
      label: '累计离线天数',
      startKey: 'accOffline1',
      endKey: 'accOffline2',
    },
    {
      type: 'start-end-num',
      label: '最大连续离线天数',
      startKey: 'serialOffline1',
      endKey: 'serialOffline2',
    },
  ];
};
const offlineTableColumns = () => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '设备编码',
      key: 'deviceId',
      align: 'left',
      tooltip: true,
      minWidth: 120,
      sortable: 'custom',
    },
    {
      title: '设备名称',
      key: 'deviceName',
      align: 'left',
      tooltip: true,
      minWidth: 120,
    },
    {
      title: '组织机构',
      key: 'orgName',
      align: 'left',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '设备状态 ',
      key: 'phyStatus',
      slot: 'phyStatus',
      minWidth: 90,
    },
    {
      title: '本月累计离线天数',
      key: 'accOfflineNum',
      slot: 'accOfflineNum',
      sortable: 'custom',
      minWidth: 90,
    },
    {
      title: '本月最大连续离线天数',
      key: 'serialOfflineNum',
      slot: 'serialOfflineNum',
      sortable: 'custom',
      minWidth: 100,
    },
    {
      title: '设备标签',
      key: 'tagNames',
      slot: 'tagNames',
      minWidth: 120,
    },
  ];
};

const initFormData = {
  'FACE_ACTIVE_ANALYSE': { ...activeInitFormData },
  'VEHICLE_ACTIVE_ANALYSE': { ...activeInitFormData },
  'FACE_OFFLINE_ANALYSE': { ...offlineInitFormData },
  'VEHICLE_OFFLINE_ANALYSE': { ...offlineInitFormData },
};

const formItemData = (params) => {
  return {
    'FACE_ACTIVE_ANALYSE': activeFormItemData(params),
    'VEHICLE_ACTIVE_ANALYSE': activeFormItemData(params),
    'FACE_OFFLINE_ANALYSE': offlineFormItemData(params),
    'VEHICLE_OFFLINE_ANALYSE': offlineFormItemData(params),
  };
};

const getDetailsTableColumns = (params) => {
  return {
    'FACE_ACTIVE_ANALYSE': activeTableColumns(params),
    'VEHICLE_ACTIVE_ANALYSE': activeTableColumns(params),
    'FACE_OFFLINE_ANALYSE': offlineTableColumns(params),
    'VEHICLE_OFFLINE_ANALYSE': offlineTableColumns(params),
  };
};

/**
 * 二：分析页面 表格配置内容
 */

// 异常分析
const activeAbnormalTableColumns = () => {
  return [
    { type: 'index', width: 70, title: '序号', align: 'center' },
    { title: '行政区划', key: 'civilName', tooltip: true, minWidth: 120 },
    { title: '设备总量', key: 'deviceNum', slot: 'deviceNum', sortable: true, minWidth: 120 },
    { title: '异常设备总量', key: 'unqualifiedNum', slot: 'unqualifiedNum', sortable: true, minWidth: 120 },
    { title: '抓拍过少', key: 'thanMeNum', slot: 'thanMeNum', sortable: true, minWidth: 120 },
    { title: '抓拍量显著低于附近设备', key: 'lessNbNum', slot: 'lessNbNum', sortable: true, minWidth: 120 },
    { title: '抓拍突降', key: 'suddenNum', slot: 'suddenNum', sortable: true, minWidth: 120 },
  ];
};
const offlineAbnormalTableColumns = (params) => {
  return [
    { type: 'index', width: 70, title: '序号', align: 'center' },
    { title: '行政区划', key: 'civilName', tooltip: true, minWidth: 120 },
    { title: '设备总量', key: 'deviceNum', slot: 'deviceNum', sortable: true, minWidth: 120 },
    { title: '异常设备总量', key: 'unqualifiedNum', slot: 'unqualifiedNum', sortable: true, minWidth: 120 },
    { title: '有离线记录', key: 'offlineNum', slot: 'offlineNum', minWidth: 120 },
    {
      title: '连续离线>=X天数量',
      key: 'serialOffNum',
      slot: 'serialOffNum',
      sortable: true,
      minWidth: 120,
      renderHeader: (h) => {
        return <span>连续离线&gt;={params?.serialOffNum || 'X'}天数量</span>;
      },
    },
    {
      title: '累计离线>=Y天数量',
      key: 'accOffNum',
      slot: 'accOffNum',
      sortable: true,
      minWidth: 120,
      renderHeader: (h) => {
        return <span>累计离线&gt;={params?.accOffNum || 'Y'}天数量</span>;
      },
    },
  ];
};
const getAbnormalTableColumns = (params) => {
  return {
    'FACE_ACTIVE_ANALYSE': activeAbnormalTableColumns(),
    'VEHICLE_ACTIVE_ANALYSE': activeAbnormalTableColumns(),
    'FACE_OFFLINE_ANALYSE': offlineAbnormalTableColumns(params),
    'VEHICLE_OFFLINE_ANALYSE': offlineAbnormalTableColumns(params),
  };
};

// 影响分析
const someArr = [
  { title: '人卡总量', key: 'deviceNum', sortable: true, minWidth: 120 },
  { title: '正常设备', key: 'qualifiedNum', sortable: true, minWidth: 120 },
  { title: '异常设备', key: 'unqualifiedNum', sortable: true, minWidth: 120 },
  {
    title: '异常率',
    key: 'resultValue',
    sortable: true,
    minWidth: 120,
    render: (h, { row }) => {
      return h(
        'span',
        {
          attrs: {
            class: `${row.resultValue >= 50 ? 'unqualified-color' : ''}`,
          },
        },
        row.resultValue >= 0 ? `${row.resultValue}%` : '',
      );
    },
  },
];
const activeInfluenceTableColumns = (params) => {
  // 平台或网络  params.dataKey === 'org_code'
  let arr =
    params.dataKey === 'org_code'
      ? [
          { type: 'index', width: 70, title: '序号', align: 'center' },
          {
            title: '平台名称',
            key: 'platformName',
            tooltip: true,
            minWidth: 120,
          },
          { title: '突降时间', key: 'dropTime', minWidth: 120 },
          ...someArr,
        ]
      : [
          { type: 'index', width: 70, title: '序号', align: 'center' },
          {
            title: '具体的影响因素名称',
            key: params.key,
            tooltip: true,
            minWidth: 120,
            renderHeader: (h) => {
              return <span>{params.title}</span>;
            },
          },
          ...someArr,
        ];

  return arr;
};
const offlineInfluenceTableColumns = (params) => {
  // 平台或网络  params.dataKey === 'org_code'
  let arr =
    params.dataKey === 'org_code'
      ? [
          { type: 'index', width: 70, title: '序号', align: 'center' },
          {
            title: '组织机构',
            key: 'platformName',
            tooltip: true,
            minWidth: 120,
          },
          { title: '离线时间', key: 'dropTime', minWidth: 120 },
          ...someArr,
        ]
      : [
          { type: 'index', width: 70, title: '序号', align: 'center' },
          {
            title: '具体的影响因素名称',
            key: params.key,
            tooltip: true,
            minWidth: 120,
            renderHeader: (h) => {
              return <span>{params.title}</span>;
            },
          },
          ...someArr,
        ];

  return arr;
};
const getInfluenceTableColumns = (params) => {
  return {
    'FACE_ACTIVE_ANALYSE': activeInfluenceTableColumns(params),
    'VEHICLE_ACTIVE_ANALYSE': activeInfluenceTableColumns(params),
    'FACE_OFFLINE_ANALYSE': offlineInfluenceTableColumns(params),
    'VEHICLE_OFFLINE_ANALYSE': offlineInfluenceTableColumns(params),
  };
};

export { initFormData, formItemData, getDetailsTableColumns, getAbnormalTableColumns, getInfluenceTableColumns };
