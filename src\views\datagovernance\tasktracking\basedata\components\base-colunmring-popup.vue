<template>
  <ui-modal :title="title" v-model="visible" :styles="styles" :footer-hide="true">
    <div class="top-wrapper">
      <span>最新更新时间：{{ modifyTime }}</span>
      <p class="search-box">
        <slot name="searchSlot"></slot>
        <Button type="primary" class="ml-lg button-blue" @click="$emit('exportExcel')">
          <em class="icon-font icon-daochu delete-icon"></em>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
      </p>
    </div>
    <line-title title-name="检测结果统计"></line-title>
    <div class="echarts-wrapper">
      <div class="ring-box">
        <draw-echarts
          :echart-option="ringEchartsOption"
          :echart-style="ringStyle"
          ref="zdryChart1"
          class=""
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
      <div class="column-box">
        <slot name="columnTabSlot"></slot>
        <draw-echarts
          :echart-option="columnEchartsOption"
          :echart-style="columnStyle"
          ref="zdryChart2"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
    </div>
    <line-title title-name="问题数据列表" class="mb-sm"></line-title>
    <ui-select-tabs
      class="tabs-ui"
      @selectInfo="(val) => $emit('selectInfo', val)"
      :list="tabsList"
      v-if="!!tabsList.length"
    ></ui-select-tabs>
    <div class="table-wrapper mt-sm">
      <ui-table
        class="ui-table"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #action="{ row }">
          <span class="font-table-action pointer" @click="$emit('openUnquatifiyReason', row)">查看不合格原因</span>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {},
    ringEchartsOption: {
      type: Object,
    },
    columnEchartsOption: {
      type: Object,
    },
    tableData: {
      type: Array,
    },
    tabsList: {
      type: Array,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    echartsLoading: {
      type: Boolean,
    },
    totalCount: {
      default: 0,
    },
    modifyTime: {
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        // top: "0.4rem",
        width: '95%',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', fixed: 'left' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 200,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
          fixed: 'left',
        },
        { title: '组织机构', key: 'orgCode', width: 180 },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          width: 150,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          width: 150,
        },
        { title: 'MAC地址', key: 'macAddr', width: 180 },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 180 },
        { title: '数据来源', key: 'sourceId', width: 180 },
        { title: '安装地址', key: 'address', width: 200 },
        { title: `${this.global.filedEnum.sbdwlx}`, key: 'sbdwlx', width: 130 },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlx',
          width: 150,
        },
        { title: '摄像机位置类型', key: 'postionType', width: 150 },
        {
          title: `${this.global.filedEnum.phyStatus}`,
          key: 'sblwzt',
          width: 180,
        },
        { title: '操作', slot: 'action', fixed: 'right', width: 150 },
      ],
      minusTable: 620,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      ringStyle: {
        height: '180px',
        width: '400px',
      },
      columnStyle: {
        height: '180px',
        width: '1210px',
      },
      permission: '',
      // tabsList: [
      //     { name: '字段1为空', select: true },
      //     { name: '字段2为空', select: false },
      //     { name: '字段3为空', select: false },
      // ],
      title: '',
    };
  },
  methods: {
    init(option) {
      this.title = option.title;
      this.visible = true;
      this.$emit('popUpGetData', option);
    },
    changePage(val) {
      this.$emit('changePage', val);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.$emit('changePageSize', val);
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        this.pageData = {
          pageNum: 1,
          pageSize: 20,
          totalCount: 0,
        };
      }
    },
    value(val) {
      this.visible = val;
    },
    totalCount(val) {
      this.pageData.totalCount = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  margin: 10px 0 0;
  .ring-box {
    width: 570px;
    margin-right: 10px;
    .echarts {
      width: 540px !important;
    }
  }
  .column-box {
    flex: 1;
    position: relative;
  }
  .ring-box,
  .column-box {
    background: var(--bg-sub-content);
  }
  .charts {
    flex: 1;
    width: 300px;
  }
}
.table-wrapper {
  position: relative;
  .no-data {
    top: 60%;
  }
}
.top-wrapper {
  display: flex;
  justify-content: space-between;
  height: 30px;
  line-height: 30px;
  color: #fff;
  .search-box {
    display: flex;
    .input-width {
      width: 230px;
    }
  }
}
.tabs-ui {
  color: #fff;
  margin: 10px 0;
}
</style>
