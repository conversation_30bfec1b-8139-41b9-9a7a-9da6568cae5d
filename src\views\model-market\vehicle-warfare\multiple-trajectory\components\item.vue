  
  <template>
    <div class="multiperson-trait-item">
        <img class="origin-pic" :src="options.originPic" :onerror="`this.src='${errorDefault}'`" />
        <div>
            <p v-if="options.plateNo">车牌号:{{options.plateNo}}</p>
            <p v-if="options.plateColor">车牌颜色:{{options.plateColor|commonFiltering(plateColorList)}}</p>
        </div>
        <img class="target-pic" :src="options.targetPic" :onerror="`this.src='${errorDefault}'`" :class="{'poinerClass':options.originPic}" />
        <div class="delete-container">
            <i class="iconfont icon-shanchu" @click="deleteRecord"></i>
        </div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
    props: {
        options: {
            default: () => { },
            type: Object
        },
        index: {
            type: Number
        }
    },
    computed: {
        ...mapGetters({
            plateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
        })
    },
    data() {
        return {
            errorDefault: require('@/assets/img/default-img/vehicle_default.png')
        }
    },
    methods: {
        deleteRecord() {
            this.$emit("delete", this.index)
        }
    }
}
</script>
<style lang="less">
.multiperson-trait-item {
    display: grid;
    grid-template-columns: 120px 1fr 120px 40px;
    margin-bottom: 15px;
    height: 120px;

    background: #e9f3ff;
    align-items: center;
    text-align: center;
    div > p {
        line-height: 20px;
    }
    .origin-pic,
    .target-pic {
        width: 100px;
        border: 1px solid rgb(45, 135, 249);
        position: relative;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: 100px;
        object-fit: contain;
    }
    .target-pic {
        margin-right: 10px;
    }
    .origin-pic {
        margin-left: 10px;
    }
    .delete-container {
        background: #fff;
        height: 100%;
        align-items: center;
        text-align: center;
        display: grid;
        cursor: pointer;
        &:hover {
            color: #2d87f9;
        }
    }
    .poinerClass {
        cursor: pointer;
    }
}
</style>

