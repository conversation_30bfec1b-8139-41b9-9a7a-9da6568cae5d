<template>
  <div :class="indexVal == '-1' ? 'capture-box-1' : 'capture-box'">
    <reach-tabs class="reach-tabs" :tab-list="tabList" @changeTab="changeTab" :tab-num="2"></reach-tabs>
    <div class="capture-evaluation" v-show="indexVal != '-1'">
      <div class="capture-outer">
        <div class="reach-title">
          <title-content :title="indexTitle + '评价统计'"></title-content>

          <Select
            v-if="statisticType === 'ORG'"
            v-model="orgType"
            placeholder="请选择组织机构"
            class="width-md inline org-type"
            @on-change="orgChange"
          >
            <Option v-for="(item, index) in orgVos" :key="index" :value="item.orgCode">{{ item.orgName }}</Option>
          </Select>
        </div>
        <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: chartDatas }">
          <el-carousel
            v-if="chartDatas.length != 0"
            indicator-position="none"
            ref="carousel"
            class="img-list"
            :arrow="chartRingList.length > 8 ? 'always' : 'never'"
            :autoplay="false"
            :initial-index="echartsIndex"
          >
            <el-carousel-item v-for="index in Math.ceil(chartRingList.length / 8)" :key="index">
              <div class="capture-echarts">
                <div
                  class="echarts-content"
                  v-for="(item, idx) in chartRingList.slice(8 * (index - 1), 8 * index)"
                  :key="idx"
                >
                  <draw-echarts
                    :echart-option="item"
                    :echart-style="ringStyle"
                    ref="zdryChart"
                    class="charts"
                    :echarts-loading="echartsLoading"
                    @echartClick="echartClick"
                  ></draw-echarts>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
    <div class="subordinate-statistics" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <div class="subordinate-box" v-if="echartList.length != 0">
        <div class="reach-title">
          <title-content :title="indexTitle || '暂无' + '评价统计'"></title-content>
          <el-popover
            :append-to-body="false"
            popper-class="subordinate-popover"
            placement="top"
            trigger="click"
            visible-arrow="false"
            v-model="echartPopver"
          >
            <Button slot="reference" class="screen">
              <span class="vt-middle screen-span">
                <i class="icon-font icon-tulishaixuan f-12 mr-sm vt-middle"></i>图例筛选</span
              >
            </Button>
            <div class="form-content">
              <Checkbox
                class="mb-sm block check-all"
                :indeterminate="indeterminate"
                :value="checkAll"
                @click.prevent.native="handleCheckAll"
              >
                <span class="ml-xs vt-middle">全选</span>
              </Checkbox>
              <div class="popover-list">
                <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                  <Checkbox
                    class="mb-sm block"
                    v-for="(item, index) in this.indexStatisticsVosList"
                    :key="index"
                    :label="item"
                  >
                    <!-- <span
                      class="bg_color ml-xs  vt-middle"
                      :style="'backgroundImage: linear-gradient(' + colorList.itemStyle.normal.colorf.colorStops[0] + ');'"
                    ></span> -->
                    <span
                      class="bg_color ml-xs vt-middle"
                      :style="'backgroundImage: linear-gradient(' + colorList[index % colorList.length] + ');'"
                    ></span>
                    <span class="ml-sm vt-middle">{{ item }}</span>
                  </Checkbox>
                </CheckboxGroup>
              </div>
              <div class="t-center btn-popover">
                <Button @click="echartPopver = false"> 取消 </Button>
                <Button class="ml-sm" type="primary" @click="submit()">确 定</Button>
              </div>
            </div>
          </el-popover>
        </div>
        <div class="echarts-box">
          <draw-echarts
            :echart-option="echartRing"
            :echart-style="rinStyle"
            ref="carChart"
            class="charts"
            :echarts-loading="echartsLoading"
            @echartClick="echartClick"
          ></draw-echarts>
          <span
            v-if="echartList.regionNames.length > comprehensiveConfig.subordinateNum"
            class="next-echart"
            @click="
              scrollRight(
                'carChart',
                echartList.regionNames,
                [],
                systemConfig.distinguishVersion === '2' ? 13 : comprehensiveConfig.subordinateNum,
              )
            "
          >
            <i class="icon-font icon-youjiantou1 f-12"></i>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
[data-theme='light'],
[data-theme='deepBlue'] {
  .subordinate-popover {
    margin-top: 0 !important;
    top: 65px !important;
    left: 1433px !important;
    width: 324px !important;
    height: 230px !important;
    border: none;
    padding: 10px !important;
    background: var(--bg-select-dropdown) !important;
    position: relative;
    .form-content {
      position: relative;
      width: 100%;
      height: 100%;
      .popover-list {
        width: 100%;
        height: calc(100% - 80px);
        position: relative;
        overflow-y: auto;
        .ivu-checkbox-wrapper {
          margin-right: 0;
        }
        .bg_color {
          min-width: 15px;
          min-height: 15px;
          text-align: center;
          font-weight: bold;
          color: #fff;
          font-size: 14px;
          display: inline-block;
        }
        .firstly1 {
          background-color: #f1b700;
        }
        .check-all {
          width: 100%;
          height: 20px;
          top: 0;
          position: relative;
        }
      }
      .btn-popover {
        height: 60px;
        width: 100%;
        line-height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top: 1px solid var(--devider-line);
        button {
          font-size: 12px;
          height: 28px;
          line-height: 28px;
          width: 48px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .popper__arrow {
      display: none !important;
    }
  }
}
.subordinate-popover {
  margin-top: 0 !important;
  top: 65px !important;
  left: 1433px !important;
  width: 324px !important;
  height: 230px !important;
  border: none;
  padding: 10px !important;
  background: #051e43 !important;
  position: relative;
  .form-content {
    position: relative;
    width: 100%;
    height: 100%;
    .popover-list {
      width: 100%;
      height: calc(100% - 80px);
      position: relative;
      overflow-y: auto;
      .ivu-checkbox-wrapper {
        margin-right: 0;
      }
      .bg_color {
        min-width: 15px;
        min-height: 15px;
        text-align: center;
        font-weight: bold;
        color: #fff;
        font-size: 14px;
        display: inline-block;
      }
      .firstly1 {
        background-color: #f1b700;
      }
      .check-all {
        width: 100%;
        height: 20px;
        top: 0;
        position: relative;
      }
    }
    .btn-popover {
      height: 60px;
      width: 100%;
      line-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-top: 1px solid var(--border-color);
      button {
        font-size: 12px;
        height: 28px;
        line-height: 28px;
        width: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .popper__arrow {
    display: none !important;
  }
}
</style>
<style lang="less" scoped>
.capture-box,
.capture-box-1 {
  // height: 1500px;
  height: 89%;
  width: 100%;
  position: relative;
  margin: 10px 0;
  @{_deep}.conter-center {
    width: 420px;
  }
  .reach-tabs {
    position: absolute;
    left: 20px;
    top: 10px;
    z-index: 100;
  }
  .capture-evaluation {
    height: 48%;
    background-color: var(--bg-sub-content);
    position: relative;
    border-bottom: 1px solid var(--devider-line);
    .capture-outer {
      height: 100%;
      background-color: var(--bg-info-card);
      position: relative;
      .reach-title {
        text-align: center;
        position: relative;
        width: 100%;
        @{_deep}.ivu-select-dropdown {
          max-height: 300px;
          width: 200px;
          overflow-y: auto;
          position: absolute;
          top: 30px !important;
        }
        .org-type {
          position: absolute;
          right: 20px;
          top: 10px;
        }
      }
      .echarts-box {
        height: 85%;
        position: relative;
        margin-top: 20px;
        .capture-echarts {
          display: flex;
          width: 95%;
          margin: 0 auto;
          justify-content: flex-start;
          align-items: center;
          height: 100%;
        }
        .img-list {
          height: 100%;
        }
        .charts {
          display: inline-block;
          margin: 0 auto;
          flex: 1;
        }
      }
      @{_deep}.el-carousel__arrow {
        width: 24px;
        height: 24px;
        background: var(--bg-carousel-arrow);
        border: 1px solid var(--border-carousel-arrow);
        opacity: 1;
        border-radius: 4px;
        top: 100px;
        right: 16px;
        position: absolute;
        text-align: center;
        line-height: 24px;
        &:active {
          width: 24px;
          height: 24px;
          background: var(--bg-carousel-arrow-active);
          border: 1px solid var(--border-carousel-arrow-active);
          opacity: 1;
          border-radius: 4px;
        }
        &:hover {
          width: 24px;
          height: 24px;
          background: var(--bg-carousel-arrow-hover);
          border: 1px solid var(--border-carousel-arrow-hover);
          opacity: 1;
          border-radius: 4px;
        }
        .el-icon-arrow-left {
          color: var(--color-carousel-arrow);
          font-size: 12px;
          &:active {
            color: #4e9ef2;
            font-size: 12px;
          }
          &:hover {
            color: var(--icon-carousel-arrow-hover);
            font-size: 12px;
          }
        }
        .el-icon-arrow-right {
          color: var(--color-carousel-arrow);
          font-size: 12px;
          &:active {
            color: #4e9ef2;
            font-size: 12px;
          }
          &:hover {
            color: var(--icon-carousel-arrow-hover);
            font-size: 12px;
          }
        }
      }
    }
    @{_deep}.el-carousel__container {
      height: 100% !important;
    }
  }
  .subordinate-statistics {
    height: 50%;
    background-color: var(--bg-sub-content);
    position: relative;
    border-bottom: 1px solid var(--devider-line);
    .subordinate-box {
      height: 100%;
      background-color: var(--bg-info-card);
      position: relative;
      display: flex;
      flex-direction: column;
      .reach-title {
        text-align: center;
        position: relative;
        // padding-top: 20px;
        width: 100%;
        .reach-tabs {
          position: absolute;
          left: 20px;
          top: 10px;
        }
      }
      .echarts {
        height: 100% !important;
      }
      // @{_deep}.ivu-btn{
      //   padding: 0;
      // }
      .screen {
        position: absolute;
        right: 20px;
        top: 20px;
        color: #fff;
        background-color: var(--color-primary);
        // &:hover{
        //   color: ;
        // }
        .screen-span {
          display: inline-block;
          width: 100%;
          height: 100%;
        }
      }
      .echarts-box {
        display: flex;
        flex: 1;
        width: 100%;
        // margin-top: 24px;
        position: relative;
        margin: 20px;
        .echarts-content {
          height: 100% !important;
          line-height: 100% !important;
          display: inline-block;
        }
        .charts {
          display: inline-block;
        }
        .next-echart {
          width: 24px;
          height: 24px;
          background: var(--bg-next-echarts);
          border: 1px solid var(--border-next-echarts);
          opacity: 1;
          border-radius: 4px;
          top: 50%;
          right: 35px;
          position: absolute;
          text-align: center;
          line-height: 24px;
          transform: translate(0, -50%);

          .icon-youjiantou1 {
            color: var(--color-icon-next-echarts);
            font-size: 12px;
            vertical-align: top !important;
          }
          &:active {
            width: 24px;
            height: 24px;
            background: var(--bg-next-echarts-active);
            border: 1px solid var(--border-next-echarts-active);
            opacity: 1;
            border-radius: 4px;
            .icon-youjiantou1 {
              color: var(--color-icon-next-echarts-active);
              font-size: 12px;
              vertical-align: top !important;
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background: var(--bg-next-echarts-hover);
            border: 1px solid var(--border-next-echarts-hover);
            opacity: 1;
            border-radius: 4px;
            .icon-youjiantou1 {
              color: var(--color-icon-next-echarts-hover);
              font-size: 12px;
              vertical-align: top !important;
            }
          }
        }
      }
    }
  }
  // .evaluation-statement {
  //   background-color: var(--bg-sub-content);
  //   border-top: 1px solid var(--devider-line);
  //   width: 100%;
  //   padding: 0 20px;
  //   height: 730px;
  //   .reach-title {
  //     text-align: center;
  //     position: relative;
  //     width: 100%;
  //     display: inline-block;
  //     margin: 30px 0;
  //   }
  //   .btns {
  //     position: absolute;
  //     right: 130px;
  //     top: 8px;
  //     .btn_jk {
  //       border-top-left-radius: 4px;
  //       border-bottom-left-radius: 4px;
  //     }
  //     .btn_kk {
  //       border-top-right-radius: 4px;
  //       border-bottom-right-radius: 4px;
  //     }
  //     span {
  //       display: inline-block;
  //       height: 34px;
  //       width: 54px;
  //       display: inline-block;
  //       color: #fff;
  //       background-color: #12294e;
  //       border: 1px solid #1b82d2;
  //       font-size: 14px;
  //       font-weight: 400;
  //       text-align: center;
  //       line-height: 34px;
  //     }
  //     .active {
  //       color: #fff;
  //       background-color: #2d8cf0;
  //     }
  //     span:hover {
  //       display: inline-block;
  //       color: #fff;
  //       background-color: #2d8cf0;
  //     }
  //   }
  //   .btn_search {
  //     position: absolute;
  //     right: 2px;
  //     top: 8px;
  //     width: 94px;
  //     text-align: center;
  //     height: 34px;
  //     line-height: 34px;
  //     padding: 0;
  //   }
  //   .active {
  //     color: #fff;
  //     background-color: #2d8cf0;
  //   }

  //   .ui-table {
  //     height: 100%;
  //     border: solid 1px #085aa7 !important;
  //     // overflow-y: auto;
  //   }
  //   .table-box {
  //     height: calc(100% - 91px);
  //     background-color: hsl(214, 81%, 17%);

  //     @{_deep}.ivu-table-body {
  //       overflow-y: auto;
  //       background-color: #08264d !important;
  //     }
  //     @{_deep}.ivu-table-cell {
  //       padding: 0 15px !important;
  //     }
  //     @{_deep}.ivu-table table {
  //       width: 1730px !important;
  //       border-collapse: unset !important;
  //     }
  //     @{_deep}.ivu-table-fixed-header,
  //     @{_deep}.ivu-table-header,
  //     @{_deep}.ivu-table-fixed-right {
  //       th {
  //         background: #002f71 !important;
  //         font-size: 13px;
  //         font-family: MicrosoftYaHei;
  //         line-height: 19px;
  //         color: #8ab9f8;
  //         opacity: 0.9;
  //       }
  //     }
  //     @{_deep}.ivu-table-header {
  //       tr {
  //         border-top: solid 1px #085aa7 !important;
  //         border-left: solid 1px #085aa7 !important;

  //         &:first-child {
  //           th {
  //             &:last-child {
  //               border-left: none !important;
  //             }
  //             &:first-child {
  //               border-left: none !important;
  //               border-bottom: 1px solid #085aa7 !important;
  //             }
  //           }
  //         }
  //         th {
  //           border-bottom: 1px solid #085aa7 !important;
  //           border-left: solid 1px #085aa7 !important;
  //           // &:last-child {
  //           //   border-left: none !important;
  //           // }
  //         }
  //       }
  //     }

  //     @{_deep}.ivu-table-tbody {
  //       tr {
  //         border-top: solid 1px #085aa7 !important;
  //         border-right: solid 1px #085aa7 !important;
  //         td {
  //           border-left: solid 1px #085aa7 !important;
  //           border-bottom: 1px solid #085aa7 !important;
  //           &:first-child {
  //             border-left: none !important;
  //           }
  //         }
  //       }
  //     }
  //     // @{_deep}.ivu-table-wrapper-with-border {
  //     //   border: none !important;
  //     // }
  //     // @{_deep}.ivu-table-border:after {
  //     //   content: '';
  //     //   width: 0.005208rem;
  //     //   height: 100%;
  //     //   position: absolute;
  //     //   top: 0;
  //     //   right: 0;
  //     //   background-color: transparent !important;
  //     //   z-index: 3;
  //     // }
  //   }
  // }
}
.capture-box-1 {
  height: 48%;
  .subordinate-statistics {
    height: 100%;
  }
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import evaluationreport from '@/config/api/evaluationreport';
import dataZoom from '@/mixins/data-zoom';
import { mapGetters } from 'vuex';
import img1 from '@/assets/img/evaluationoverview/standard.png';
import img2 from '@/assets/img/evaluationoverview/no-standard.png';
export default {
  name: 'captureBox',
  mixins: [dataZoom],
  data() {
    return {
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      echartPopver: false,
      loading: false,
      echartRing: {},
      optionsArr: {},
      ringStyle: {
        width: '210px',
        height: '220px',
      },
      rinStyle: {
        width: '98%',
        height: '230px',
      },
      finishStatus: '',
      tableColumns: [],
      tableData: [],
      curBtn: 1,
      chartDatas: [],
      echartList: [],
      copyEchartList: [],
      xAxisData: [],
      chartRingList: [],
      echartsIndex: 0,
      zoomStart: 0,
      zoomEnd: 100,
      indexTitle: '',
      echartsLoading: false,
      indexList: {},
      indexVal: '',
      targetDataTypeDesc: '',
      indeterminate: false,
      checkAll: true,
      checkAllGroup: [],
      indexStatisticsVosList: [],
      // colorList: [],
      colorList: [
        `${$var('--linear-gradient-yellow-2-start')},${$var('--linear-gradient-yellow-2-end')}`,
        `${$var('--linear-gradient-purple-2-start')},${$var('--linear-gradient-purple-2-end')}`,
        `${$var('--linear-gradient-cyan-2-start')},${$var('--linear-gradient-cyan-2-end')}`,
        `${$var('--linear-gradient-green-4-start')},${$var('--linear-gradient-green-4-end')}`,
        `${$var('--linear-gradient-purple-3-start')},${$var('--linear-gradient-purple-3-end')}`,
        `${$var('--linear-gradient-yellow-3-start')},${$var('--linear-gradient-yellow-3-end')}`,
        `${$var('--linear-gradient-blue-6-start')},${$var('--linear-gradient-blue-6-end')}`,
        `${$var('--linear-gradient-pink-1-start')},${$var('--linear-gradient-pink-1-end')}`,
        `${$var('--linear-gradient-pink-2-start')},${$var('--linear-gradient-pink-2-end')}`,
        `${$var('--linear-gradient-green-5-start')},${$var('--linear-gradient-green-5-end')}`,
        `${$var('--linear-gradient-purple-4-start')},${$var('--linear-gradient-purple-4-end')}`,
      ],
      colorEnum: [],
      legendsName: '',
      dataType: null,
      orgVos: [],
      orgType: null,
    };
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll, true);
  },
  methods: {
    echartClick() {},
    handleScroll() {
      if (this.curBtn === 1) return;
      let parents = document.getElementsByClassName('capture-table')[0];
      let textName = parents.getElementsByClassName('textName')[0];
      let dom = textName.getElementsByClassName('ivu-table-cell')[0];

      let scrollLeft = parents.getElementsByClassName('ivu-table-body')[0].scrollLeft;
      dom.setAttribute(
        'style',
        `
        position : absolute;
        left : calc(45vw - 190px + ` +
          scrollLeft +
          `px );
        top: 16px;
      `,
      );
    },

    async getReportDetail() {
      let params = {
        indexModule: this.indexVal,
        batchIds: this.indexList.batchIds,
        dataType: this.indexList.targetDataType,
        selfRegionCode: this.indexList.selfRegionCode,
        statisticType: this.statisticType,
      };
      try {
        let res = await this.$http.post(evaluationreport.exportReportOfDetail, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },

    // 环形请求接口
    async getIndexStatistics(val, value, list) {
      this.echartsLoading = true;
      let data = {
        indexModule: val,
        batchIds: list.batchIds,
        dataType: list.selfDataType,
        selfRegionCode: list.selfRegionCode,
        displayType: this.statisticType,
        orgCode: this.orgType,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getIndexStatistics, data);
        this.chartDatas = res.data.data.statisticsVos || [];
        this.orgVos = res.data.data.orgVos;
        this.initChart();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },

    orgChange(data) {
      this.orgType = data;
      this.getIndexStatistics(this.indexVal, this.indexTitle, this.indexList);
    },
    // 获取全部数据
    async getAllData(val, value, list) {
      this.echartsLoading = true;
      this.echartList = [];
      this.copyEchartList = [];
      this.indexStatisticsVosList = [];
      let data = {
        indexModule: val,
        batchIds: list.batchIds,
        rootResultIds: list.rootResultIds,
        dataType: list.targetDataType,
        selfRegionCode: this.statisticType === 'ORG' ? list.selfOrgCode : list.selfRegionCode,
        displayType: this.statisticType,
        orgCode: this.orgType,
      };
      try {
        let res = await this.$http.post(evaluationreport.getAllData, data);
        this.echartList = this.$util.common.deepCopy(res.data.data);
        Object.keys(this.echartList.indexValues).forEach((key) => {
          this.echartList.indexValues[key].forEach((v, i) => {
            let arr = v.split('/');
            this.echartList.indexValues[key][i] = {
              value: ((Number(arr[0]) / Number(arr[1])) * 100).toFixed(2),
              sum: Number(arr[1]),
              percentage: Number(arr[0]),
            };
          });
        });
        this.copyEchartList = this.$util.common.deepCopy(this.echartList);
        this.indexStatisticsVosList = this.$util.common.deepCopy(this.echartList.legends);
        this.checkAllGroup = this.$util.common.deepCopy(this.echartList.legends);
        this.initRing('', true);
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },

    // 获取接口稳定性柱状图
    async getInterfaceStability(val, value, list) {
      this.echartsLoading = true;
      this.echartList = [];
      this.copyEchartList = [];
      this.indexStatisticsVosList = [];
      let data = {
        indexModule: val,
        rootResultIds: list.rootResultIds,
        dataType: list.targetDataType,
        selfRegionCode: list.selfRegionCode,
        batchIds: list.batchIds,
      };
      try {
        let res = await this.$http.post(evaluationreport.getInterfaceStability, data);
        this.echartList = this.$util.common.deepCopy(res.data.data);
        this.copyEchartList = this.$util.common.deepCopy(res.data.data);
        this.indexStatisticsVosList = this.$util.common.deepCopy(this.echartList.legends);
        this.checkAllGroup = this.$util.common.deepCopy(this.echartList.legends);
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },

    //接口稳定性评价统计
    async getInterfaceStatistics(val, value, list) {
      this.echartsLoading = true;
      let data = {
        indexModule: val,
        rootResultIds: list.rootResultIds,
        dataType: list.selfDataType,
        selfRegionCode: list.selfRegionCode,
        batchIds: list.batchIds,
      };
      try {
        let res = await this.$http.post(evaluationreport.getInterfaceStatistics, data);
        this.chartDatas = res.data.data;
        this.initChart();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },

    // 多个柱形指标评价统计请求数据
    async getSubIndexStatistics(val, value, list) {
      this.echartsLoading = true;
      this.echartList = [];
      this.copyEchartList = [];
      this.indexStatisticsVosList = [];
      let data = {
        indexModule: val,
        batchIds: list.batchIds,
        dataType: list.targetDataType,
        selfRegionCode: list.selfRegionCode,
        displayType: this.statisticType,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getHistogramIndexDetail, data);
        // res.data.data.regionNames = res.data.data.regionNames.map((item) => {
        //   if (item.endsWith('市') || item.endsWith('区'))
        //   item = item.substring(0, item.length - 1)
        //   return item
        // })
        this.echartList = this.$util.common.deepCopy(res.data.data) || {};
        this.copyEchartList = this.$util.common.deepCopy(res.data.data);
        this.indexStatisticsVosList = this.$util.common.deepCopy(this.echartList.legends);
        this.checkAllGroup = this.$util.common.deepCopy(this.echartList.legends);
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    initChart() {
      let colorEnum = [
        [$var('--linear-gradient-yellow-5-start'), $var('--linear-gradient-yellow-5-end')],
        [$var('--linear-gradient-purple-5-start'), $var('--linear-gradient-purple-5-end')],
        [$var('--linear-gradient-cyan-3-start'), $var('--linear-gradient-cyan-3-end')],
        [$var('--linear-gradient-green-7-start'), $var('--linear-gradient-green-7-end')],
        [$var('--linear-gradient-purple-6-start'), $var('--linear-gradient-purple-6-end')],
        [$var('--linear-gradient-yellow-6-start'), $var('--linear-gradient-yellow-6-end')],
        [$var('--linear-gradient-blue-8-start'), $var('--linear-gradient-blue-8-end')],
        [$var('--linear-gradient-pink-3-start'), $var('--linear-gradient-pink-3-end')],
      ];
      if (this.systemConfig.distinguishVersion === '2') {
        colorEnum = [
          ['#FACD89', '#978b72'],
          ['#F59835', '#946B40'],
          ['#0CD084', '#078c6f'],
          ['#4193FC', '#2868b8'],
          ['#CABDDD', '#7a82a5'],
          ['#FF6E82', '#9a526e'],
          ['#A09858', '#616b55'],
          ['#11DDE0', '#0b95a7'],
          ['#FFF14F', '#9aa14f'],
          ['#6cc783', '#42886f'],
          ['#10B4cf', '#0a7c9d'],
          ['#03adef', '#0278b0'],
        ];
      }
      this.chartRingList = [...this.chartDatas].map((row, index) => {
        let one = {
          title: row.indexName,
          subTitle: row.resultValue,
          standard: row.standard,
          img1: img1,
          img2: img2,
          data: [{ value: row.resultValue }],
          color: colorEnum[index % colorEnum.length],
        };
        return this.$util.doEcharts.evaluationIndexStatistics(one);
      });
    },
    initRing(type, dataObj = false) {
      let seriesArr = [];
      const colorList = [
        `${$var('--linear-gradient-yellow-2-start')}`,
        `${$var('--linear-gradient-purple-2-start')}`,
        `${$var('--linear-gradient-cyan-2-start')}`,
        `${$var('--linear-gradient-green-4-start')}`,
        `${$var('--linear-gradient-purple-3-start')}`,
        `${$var('--linear-gradient-yellow-3-start')}`,
        `${$var('--linear-gradient-blue-6-start')}`,
        `${$var('--linear-gradient-pink-1-start')}`,
        `${$var('--linear-gradient-pink-2-start')}`,
        `${$var('--linear-gradient-green-5-start')}`,
        `${$var('--linear-gradient-purple-4-start')}`,
      ];
      Object.keys(this.echartList.indexValues).forEach((key, index) => {
        let obj = {
          name: this.echartList.legends[index],
          type: 'bar',
          barWidth: '8px',

          data: this.echartList.indexValues[key],
        };

        // 区分省厅，默认
        if (this.systemConfig.distinguishVersion === '2') {
          obj.stack = '数量';
          obj.barWidth = '30px';
          obj.itemStyle = {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: colorList[index],
              },
              {
                offset: 1,
                color: colorList[index],
              },
            ]),
          };
        } else {
          let cLen = this.colorList.length;
          let cIndex = index >= cLen ? index % cLen : index;
          obj.itemStyle = {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: this.colorList[cIndex].split(',')[0],
              },
              {
                offset: 1,
                color: this.colorList[cIndex].split(',')[1],
              },
            ]),
          };
        }
        seriesArr.push(obj);
      });
      let opts = {
        xAxis: this.echartList.regionNames,
        data: seriesArr,
        yAxis: [{ show: false }],
        targetDataTypeDesc: this.targetDataTypeDesc,
        dataObj: dataObj,
      };

      this.echartRing = this.$util.doEcharts.evaluationSubordinateColumn(
        opts,
        this.systemConfig.distinguishVersion === '2' ? true : false,
      );
      // 区分省厅，默认
      if (this.systemConfig.distinguishVersion === '2') {
        this.echartRing.yAxis = [{ show: false }];
      }
      setTimeout(() => {
        this.setDataZoom(
          'carChart',
          [],
          this.systemConfig.distinguishVersion === '2' ? 13 : this.comprehensiveConfig.subordinateNum,
        );
      });
    },

    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.indexStatisticsVosList;
      } else {
        this.checkAllGroup = [];
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.indexStatisticsVosList.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    submit() {
      this.colorEnum = [];
      this.echartsLoading = true;
      // this.echartList = this.copyEchartList.map((item) => {
      //   let one = this.$util.common.deepCopy(item)
      //   one.indexStatisticsVos = item.indexStatisticsVos.filter((it, index) => {
      //     if (this.checkAllGroup.includes(it.strategy)) {
      //       this.colorEnum.push(index)
      //       return it
      //     }
      //   })
      //   return one
      // })
      this.echartList.indexValues = {};
      this.echartList.legends = this.copyEchartList.legends.filter((item, index) => {
        if (this.checkAllGroup.includes(item)) {
          this.colorEnum.push(index);
          return item;
        }
      });
      Object.keys(this.copyEchartList.indexValues).forEach((key, index) => {
        if (this.colorEnum.includes(index)) {
          this.echartList.indexValues[key] = this.copyEchartList.indexValues[key];
        }
      });
      this.initRing(1, this.indexVal === '-1' && this.systemConfig.distinguishVersion === '2' ? true : false);
      this.$nextTick(() => {
        this.$refs.carChart.setOption(this.echartRing, true);
      });
      this.echartsLoading = false;
    },
    // 切换指标按钮
    async changeTab(val, value, list) {
      this.indexTitle = value;
      this.indexVal = val;
      this.indexList = list;
      this.targetDataTypeDesc = list.targetDataTypeDesc;
      // await this.getReportStatisticsOfSimple()
      if (val == -1) {
        await this.getAllData(val, value, list);
      } else if (val == 7) {
        await this.getInterfaceStatistics(val, value, list);
        await this.getInterfaceStability(val, value, list);
      } else {
        await this.getIndexStatistics(val, value, list);
        await this.getSubIndexStatistics(val, value, list);
      }
      // this.changeBtn(1)
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
      systemConfig: 'common/getSystemConfig',
    }),
    chartList() {
      return Math.ceil(this.chartRingList.length / 8);
    },
    isArrow() {
      return this.chartRingList.length > 8 ? 'always' : 'never';
    },
  },
  props: {
    tabList: {
      required: true,
      default() {
        return [];
      },
    },

    statisticType: {
      type: String,
    },
  },
  deactivated() {
    window.removeEventListener('scroll', this.handleScroll, true);
  },
  components: {
    titleContent: require('@/views/governanceevaluation/evaluationoverview/components/title-content.vue').default,
    reachTabs: require('@/views/governanceevaluation/evaluationoverview/components/reach-tabs.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
