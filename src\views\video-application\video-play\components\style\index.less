.search-top {
    margin: 15px;
    margin-top: 0;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .add-group {
        cursor: pointer;
        font-size: 20px;
        border-radius: 4px;
        padding: 1px 5.5px;
        border: 1px solid #d3d7de;
    }

    /deep/ .ivu-btn {
        padding-left: 0;
        padding-right: 0;
        background: white;
        border: 1px solid rgb(211, 215, 222);
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left: none;
        color: #808695;

        .ivu-icon {
            color: #808695;
        }
        &:hover {
          border: 1px solid #248AFC;
          border-left: none;
        }
    }

    /deep/ .ivu-input {
        border-right: none;
        &:hover {
          border: 1px solid #248AFC;
        }
    }


    &:focus-within {
        /deep/ .ivu-btn {
            border: 1px solid #248AFC;
            border-left: none;
        }
    }
}

.tree-box {
    height: calc(~'100% - 49px');
    overflow-y: auto;
    position: initial;
}