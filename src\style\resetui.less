[data-theme='dark'] {
  .el-loading-mask {
    .el-loading-spinner {
      background: url('../assets/img/common/loading.gif') no-repeat;
    }
  }
  .ivu-message {
    .ivu-message-notice {
      .ivu-message-notice-content {
        &.ivu-message-notice-content-success {
          .ivu-icon:before {
            background-image: url('~@/assets/img/message/success.png');
          }
        }
        &.ivu-message-notice-content-warning {
          .ivu-message-warning {
            .ivu-icon:before {
              background-image: url('~@/assets/img/message/warning.png');
            }
          }
        }
        &.ivu-message-notice-content-error {
          .ivu-icon:before {
            background-image: url('~@/assets/img/message/error.png');
          }
        }
      }
    }
  }
  .reset-el-table.el-table {
    .el-table__body-wrapper,
    .el-table__fixed-body-wrapper {
      .el-table__empty-block {
        background: url('../assets/img/common/nodata.png') no-repeat;
        background-size: 6%;
        background-position: center;
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .el-loading-mask {
    .el-loading-spinner {
      background: url('../assets/img/common/loading-light.gif') no-repeat;
    }
  }
  .ivu-tooltip-popper {
    .ivu-tooltip-content {
      .ivu-tooltip-inner {
        border: 0;
        box-shadow: 0px 1px 5px 0px rgba(147, 171, 206, 0.7016);
      }
    }
    &[x-placement^='top'] {
      .ivu-tooltip-arrow {
        border: 0;
        box-shadow: 0px 1px 5px 0px rgba(147, 171, 206, 0.7016);
        filter: drop-shadow(0px 0px 6px rgba(147, 171, 206, 0.7016));
      }
    }
  
    &[x-placement^='bottom'] {
      .ivu-tooltip-arrow {
        border: 0;
        box-shadow: 0px 1px 5px 0px rgba(147, 171, 206, 0.7016);
        filter: drop-shadow(0px 0px 6px rgba(147, 171, 206, 0.7016));
      }
    }
  
    &[x-placement^='right'] {
      .ivu-tooltip-arrow {
        border: 0;
        box-shadow: 0px 1px 5px 0px rgba(147, 171, 206, 0.7016);
        filter: drop-shadow(0px 0px 6px rgba(147, 171, 206, 0.7016));
      }
    }
  
    &[x-placement^='left'] {
      .ivu-tooltip-arrow {
        border: 0;
        box-shadow: 0px 1px 5px 0px rgba(147, 171, 206, 0.7016);
        filter: drop-shadow(0px 0px 6px rgba(147, 171, 206, 0.7016));
      }
    }
  }
}
[data-theme='deepBlue'] {
  .el-loading-mask {
    .el-loading-spinner {                   
      background: url('../assets/img/common/loading-deepBlue.gif') no-repeat;
    }
  }
}

/*解决首页google浏览器自带的输入框缓存的颜色*/
input:-webkit-autofill,
select:-webkit-autofill,
textarea:-webkit-autofill {
  color: #fff !important;
  background-color: none !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  -webkit-transition:
    color 99999s ease-out,
    background-color 99999s ease-out;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  height: 10px;
  background-color: var(--bg-scrollbar);
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px transparent;
  border-radius: 10px;
  background-color: transparent;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  background-color: var(--scrollbar-thumb);
}

/*两个滚动条的交汇处*/
::-webkit-scrollbar-corner {
  background-color: transparent;
}

/*------------------------------iview框架ui修改------------------------------*/
// 更改左侧的导航栏样式
.ivu-menu-vertical .ivu-menu-submenu-title {
  padding: 12px 14px;
}

.ivu-menu-submenu .ivu-menu-item {
  padding-left: 20px !important;
}

// 更改日期图标
// .ivu-icon-ios-calendar-outline:before {
//     content: "\e681" !important;
//     font-family: "font_family" !important;
//     font-size: 16px;
//     font-style: normal;
//     -webkit-font-smoothing: antialiased;
//     -moz-osx-font-smoothing: grayscale;
// }

.ivu-btn-primary {
  padding: 0 22px;
  height: 34px;
  line-height: 34px;
  background: var(--bg-btn-primary);
  opacity: 1;
  border-radius: 4px;
  border: none;

  &:focus {
    box-shadow: none;
  }

  &:hover {
    color: #fff !important;
    background: var(--bg-btn-primary-hover);
    border: none;
  }

  &:active {
    background: var(--bg-btn-primary-active);
  }

  &[disabled] {
    color: var(--color-btn-primary-disabled) !important;
    background: var(--bg-btn-primary-disabled);
    border: 1px solid var(--border-btn-primary-disabled);
  }
}

.ivu-btn-dashed {
  padding: 0 22px;
  height: 34px;
  line-height: 34px;
  background: var(--bg-btn-dashed);
  color: var(--color-btn-dashed);
  border-radius: 4px;
  border: 1px dashed var(--border-btn-dashed);
  opacity: 1;

  &:focus {
    box-shadow: none;
  }

  &:hover {
    color: var(--color-btn-dashed-hover) !important;
    background: var(--bg-btn-dashed);
    border: 1px dashed var(--border-btn-dashed-hover);
  }

  &:active {
    color: var(--color-btn-dashed-hover) !important;
    background: var(--bg-btn-dashed);
    border: 1px dashed var(--border-btn-dashed-active);
  }

  &[disabled] {
    color: var(--color-btn-primary-disabled) !important;
    background: var(--bg-btn-primary-disabled);
    border: 1px dashed var(--border-btn-primary-disabled);
  }
}

.ivu-btn-default {
  padding: 0 22px;
  height: 34px;
  line-height: 34px;
  color: var(--color-btn-default);
  background: var(--bg-btn-default);
  border: 1px solid var(--border-btn-default);
  opacity: 1;
  border-radius: 4px;

  &:focus {
    box-shadow: none;
  }

  &:hover {
    color: var(--color-btn-default-hover) !important;
    background: var(--bg-btn-default);
    border: 1px solid var(--border-btn-default-hover);
  }

  &:active {
    color: var(--color-btn-default) !important;
    background: var(--bg-btn-default);
    border: 1px solid var(--border-btn-default-active);
  }

  &[disabled] {
    color: var(--color-btn-primary-disabled) !important;
    background: var(--bg-btn-default);
    border: 1px solid var(--border-btn-primary-disabled);
  }
}

// Button text
@ivu-btn-text-hover: {
  color: var(--color-btn-text-hover);
  background-color: transparent;
  border: none;
};
.ivu-btn-text {
  padding: 0;
  color: var(--color-btn-text);
  border: none;

  &:hover {
    @ivu-btn-text-hover();
  }

  &:focus {
    box-shadow: none;
  }

  &:active {
    color: var(--color-btn-text);
  }

  &[disabled] {
    color: var(--color-btn-primary-disabled) !important;
    background-color: transparent;
    border: none;
    &:hover {
      @ivu-btn-text-hover();
    }
  }

  &.ivu-btn-small.ivu-picker-confirm-time {
    color: var(--color-btn-text);
  }

  &.ivu-btn-loading {
    &:before {
      background-color: transparent;
    }
  }
}

//button small
.ivu-btn.ivu-btn-primary.ivu-btn-small,
.ivu-btn.ivu-btn-default.ivu-btn-small {
  height: 28px;
  line-height: 28px;
  padding: 0 12px;
}

.ivu-poptip-inner {
  background: var(--bg-tooltip) !important;
}

.ivu-poptip-arrow:after {
  border-top-color: transparent !important;
}

// table组件ui修改
.ivu-table-tip table td {
  background-color: var(--bg-table) !important;
}

.ivu-table-stripe .ivu-table-body tr:nth-child(2n) td,
.ivu-table-stripe .ivu-table-fixed-body tr:nth-child(2n) td {
  background: var(--bg-table-body-td-2n);
}

.ivu-table {
  background-color: var(--bg-table) !important;
  font-size: 14px;

  th,
  td {
    border: none;
    background: var(--bg-table-body-td);
  }

  &::before {
    background-color: transparent !important;
  }

  .ivu-table-fixed {
    &:before {
      background-color: transparent;
    }
  }

  .ivu-table-fixed-right {
    box-shadow: -2px 0px 5px 0px var(--color-ivu-table-fixed-right);

    &:before {
      background-color: transparent;
    }
  }

  .ivu-table-fixed-shadow {
    overflow: initial;
  }

  .ivu-table-fixed-header,
  .ivu-table-header,
  .ivu-table-fixed-right {
    th {
      height: 50px;
      background: var(--bg-table-header-th) !important;
      color: var(--color-table-header-th);
      font-size: 14px;
      font-weight: normal;
      box-shadow: var(--shadow-table-header);
    }
  }

  .ivu-table-tbody {
    td {
      // background: transparent !important;
      color: var(--color-table-header-th);
      border-color: transparent;

      // .ivu-table-cell {
      //     margin: 10px 0 10px 0;
      //     overflow: hidden;
      //     text-overflow: ellipsis;
      //     display: -webkit-box;
      //     -webkit-line-clamp: 2;
      //     /*超出3行部分显示省略号，去掉该属性 显示全部*/
      //     -webkit-box-orient: vertical;
      // }
    }

    // .ivu-table-row:nth-of-type(odd) {
    //     background: @bg-darkblue-block;
    // }

    // .ivu-table-row:nth-of-type(even) {
    //     background: @bg-table-block;
    // }
  }

  .ivu-table-fixed-right-header {
    border-top: none;
    border-bottom: none;
    background-color: var(--bg-table-header-th);
  }

  .ivu-table-row-hover td {
    background: var(--bg-table-row-hover) !important;
  }

  //解决错位问题
  .ivu-table-hidden {
    visibility: unset;
  }

  &-cell {
    padding: 0 5px;
  }

  // 表格操作栏设置padding值
  .table-action-padding {
    .ivu-table-cell {
      padding: 0 15px !important;
    }
  }
  .ivu-table-cell-with-selection .ivu-checkbox {
    margin-right: 0;
  }
}

.ivu-table-wrapper {
  .ivu-spin-fix {
    background: var(--bg-content);
  }
}

// 上传图片
.ivu-upload-drag {
  border: none;
  background: transparent;
  text-align: left;

  &:hover {
    border: none;
  }
}

.ivu-select-visible {
  .ivu-select-selection {
    box-shadow: none;
  }
}

// 提高提示优先级
.ivu-form-item-error .ivu-select-selection {
  border: 1px solid #ed4014 !important;
}

// 更改select
.ivu-select {
  color: var(--color-input);

  .ivu-select-selection {
    border: 1px solid var(--border-input);
    background-color: var(--bg-input);
    height: 34px;
    line-height: 34px;

    .ivu-select-placeholder {
      color: var(--color-input-placeholder);
      height: 34px;
      line-height: 34px;
    }

    .ivu-icon:before {
      color: var(--color-select-arrow);
    }

    &:focus {
      box-shadow: none;
    }

    &:hover {
      color: var(--color-input);
      border-color: var(--border-input-hover);
      .ivu-select-placeholder {
        color: var(--color-input);
      }

      .ivu-select-input {
        &::-webkit-input-placeholder {
          color: var(--color-input-placeholder);
        }
      }
    }

    .ivu-select-input {
      color: var(--color-input);

      &::-webkit-input-placeholder {
        color: var(--color-input-placeholder);
      }
    }
  }

  .ivu-select-dropdown {
    background-color: var(--bg-select-dropdown);

    .ivu-select-item {
      background-color: var(--bg-select-dropdown);
      color: var(--color-select-item);

      &:hover {
        background-color: var(--bg-select-item-hover);
      }

      &.ivu-select-item-selected {
        background: var(--bg-select-item-active);
        color: var(--color-select-item-active) !important;
      }
    }
  }

  &.ivu-select-disabled {
    .ivu-select-selection {
      background-color: var(--bg-select-disabled);
      border-color: var(--border-btn-primary-disabled);
      color: var(--color-input-disabled);
      &:hover {
        border-color: var(--border-input);
        .ivu-select-placeholder {
          color: var(--color-input-placeholder);
        }
      }
    }
  }
}

/* Select 设置transfer属性时*/
.ivu-select-dropdown {
  background-color: var(--bg-select-dropdown);

  .ivu-select-item {
    background-color: var(--bg-select-dropdown);
    color: var(--color-select-item);

    &:hover {
      background-color: var(--bg-select-item-hover);
    }

    &.ivu-select-item-selected {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active) !important;
    }
  }

  .ivu-time-picker-cells-cell-selected {
    background: var(--bg-select-item-active);
    color: var(--color-select-item-active);
  }

  .ivu-time-picker-cells-cell {
    color: var(--color-date-picker-header-label);

    &:hover {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active);
    }
  }

  .ivu-time-picker-cells-list {
    border-color: var(--border-input);
  }
}

//更改日历下拉框
.ivu-date-picker,
.home-date-transfer-box-body {
  .ivu-input-suffix {
    .ivu-icon:before {
      font-size: 14px;
      color: var(--color-select-arrow);
      font-family: 'icon-font';
      content: '\e742';
    }
  }

  &.ivu-date-picker-focused {
    input {
      box-shadow: none;
    }
  }

  .ivu-select-dropdown {
    background-color: var(--bg-select-dropdown);

    .ivu-time-picker-cells-cell.ivu-time-picker-cells-cell-selected {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active);
    }
  }

  .ivu-date-picker-header {
    border-bottom-color: var(--border-input);

    .ivu-date-picker-header-label {
      color: var(--color-date-picker-header-label);
    }
  }

  .ivu-date-picker-cells-cell {
    color: var(--color-date-picker-header-label);

    &.ivu-date-picker-cells-cell-selected.ivu-date-picker-cells-focused {
      em {
        background: var(--color-primary);
      }
    }
    &:hover {
      em {
        background-color: var(--color-primary);
        color: #fff;
      }
    }
  }
  .ivu-date-picker-cells-cell-selected em,
  .ivu-date-picker-cells-cell-selected:hover em {
    background: var(--color-primary);
  }
  .ivu-picker-panel-icon-btn:hover {
    color: var(--color-primary);
  }

  .ivu-date-picker-cells-focused em {
    box-shadow: 0 0 0 1px var(--color-primary) inset;
  }
  .ivu-date-picker-cells-cell-today em::after {
    background: var(--color-primary);
  }

  .ivu-date-picker-cells-cell-prev-month em,
  .ivu-date-picker-cells-cell-next-month em {
    color: var(--color-date-picker-header-label);
  }

  .ivu-date-picker-cells-cell-disabled {
    background-color: var(--color-date-picker-cells-cell-disabled);

    &:hover {
      background: var(--color-date-picker-cells-cell-disabled);
    }
  }

  .ivu-time-picker-cells-cell {
    color: var(--color-date-picker-header-label);

    &:hover {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active);
    }
  }

  .ivu-time-picker-cells-list {
    border-color: var(--border-input);
  }

  .ivu-picker-confirm {
    border-color: var(--border-input);
  }

  &[disabled] {
    color: red;
  }
}

// 更改弹出框
.ivu-modal-content {
  background: var(--bg-table) !important;
  border: 1px solid var(--border-modal-content);

  // box-shadow: 0 0 10px 1px #0D4B6F;
  .ivu-modal-header,
  .ivu-modal-footer {
    border-color: transparent;
  }

  .ivu-modal-footer {
    padding-bottom: 30px;
  }
}

.ivu-modal {
  top: 0;
}

.ivu-modal-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
}

//更改form表单
.ivu-form {
  .ivu-form-item-label {
    color: var(--color-form-label) !important;
    &:before {
      color: var(--color-form-icon-validate);
    }
  }

  //验证
  .ivu-form-item-error {
    .ivu-input:focus,
    .ivu-input:hover {
      box-shadow: none;
      border: 1px solid var(--color-form-icon-validate) !important;
    }
    .ivu-form-item-error-tip {
      color: var(--color-form-icon-validate);
    }
  }
}

.ivu-input {
  border: 1px solid var(--border-input);
  background-color: var(--bg-input);
  height: 34px;
  color: var(--color-input);

  &:focus {
    color: var(--color-input);
    border: 1px solid var(--border-input-hover) !important;
    box-shadow: none;
  }

  &:hover {
    border: 1px solid var(--border-input-hover);

    &&::-webkit-input-placeholder {
      color: var(--color-input) !important;
    }
  }

  &&::-webkit-input-placeholder {
    color: var(--color-input-placeholder) !important;
  }

  &[disabled] {
    background-color: var(--bg-select-disabled);
    border-color: var(--border-btn-primary-disabled);

    &:hover {
      border-color: var(--border-input);
    }

    &&::-webkit-input-placeholder {
      color: var(--color-input-placeholder) !important;
    }
  }
}
.ivu-input-group {
  .ivu-input-group-append,
  .ivu-input-group-prepend {
    border: 1px solid var(--color-primary) !important;
  }
}

.ivu-input-number {
  border: 1px solid var(--border-input);
  height: 34px;
  line-height: 34px;
  background-color: transparent;

  .ivu-input-number-handler-wrap {
    display: none;
  }

  &:focus {
    box-shadow: none;
  }

  &:hover {
    border: 1px solid var(--border-input-hover);
  }

  &.ivu-input-number-disabled {
    &:hover {
      border: 1px solid var(--border-input);
    }
  }

  &[disabled] {
    color: var(--color-input-disabled) !important;

    &:hover {
      border: 1px solid var(--border-input);
      background-color: var(--bg-input);
    }
  }
}

.ivu-input-number-input {
  background-color: var(--bg-input);
  height: 34px;
  line-height: 34px;
  color: var(--color-input);

  &:focus {
    color: var(--color-input);
  }

  &::placeholder {
    color: var(--color-input-placeholder);
  }

  &:hover {
    &::placeholder {
      color: var(--color-input);
    }
  }
  &[disabled] {
    color: var(--color-input-disabled);
  }
}

.ivu-input-number-focused {
  box-shadow: none;
  color: @font-color-white;
  border: 1px solid var(--color-primary);
  box-shadow: none;
}

.ivu-input-number-disabled .ivu-input-number-input {
  background-color: var(--bg-select-disabled);
}
.ivu-input-search {
  background: var(--bg-btn-primary) !important;
  border-color: var(--color-primary) !important;
  &:hover {
    background: var(--bg-btn-primary-hover) !important;
    border-color: var(--color-primary) !important;
  }
}

.ivu-radio-wrapper {
  color: var(--color-input);

  .ivu-radio {
    margin-right: 10px;
    .ivu-radio-inner {
      border: 1px solid var(--border-checkbox-inner);
      background-color: var(--bg-checkbox-inner);

      &.ivu-radio-focus {
        box-shadow: none;
      }

      &:after {
        background-color: var(--border-input-hover);
      }
    }

    &:hover {
      .ivu-radio-inner {
        border: 1px solid var(--border-input-hover);
      }
    }

    &.ivu-radio-disabled {
      .ivu-radio-inner {
        border-color: var(--border-btn-primary-disabled);

        &.ivu-radio-focus {
          box-shadow: none;
        }

        &:after {
          background-color: var(--bg-btn-primary-disabled);
        }
      }

      &:hover {
        .ivu-radio-inner {
          border-color: var(--border-btn-primary-disabled);
        }
      }
    }

    &.ivu-radio-checked {
      .ivu-radio-inner {
        border-color: var(--border-input-hover);
      }

      &.ivu-radio-disabled {
        .ivu-radio-inner {
          border-color: var(--border-btn-primary-disabled);
        }
      }
    }
  }
}

//ivu-checkbox
.ivu-checkbox-group-item {
  color: var(--color-input);
}

.ivu-checkbox-wrapper {
  color: var(--color-input);

  .ivu-checkbox {
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin-right: 10px;

    .ivu-checkbox-inner {
      border: 1px solid var(--border-checkbox-inner);
      background-color: var(--bg-checkbox-inner);

      &.ivu-checkbox-focus {
        box-shadow: none;
      }
    }

    &:hover {
      .ivu-checkbox-inner {
        border: 1px solid var(--color-primary);
      }
    }

    &.ivu-checkbox-checked {
      .ivu-checkbox-inner {
        border-color: var(--color-primary);
        background-color: var(--color-primary);
      }

      &.ivu-checkbox-disabled {
        .ivu-checkbox-inner {
          background-color: var(--bg-checkbox-disabled);
          border-color: var(--border-checkbox-disabled);
        }
      }
    }

    &.ivu-checkbox-disabled {
      .ivu-checkbox-inner {
        background-color: var(--bg-checkbox-disabled);
        border-color: var(--border-checkbox-disabled);
      }
    }
  }
}

//ivu-switch
.ivu-switch {
  border: 1px solid var(--border-switch-default);
  background-color: transparent;
  &::after {
    background-color: var(--bg-switch-after-default);
  }

  &.ivu-switch-checked {
    background-color: var(--color-primary);
    &::after {
      background-color: var(--bg-switch-after-active) !important;
    }
    &.ivu-switch-disabled {
      border: 1px solid var(--bg-switch-active-disabled);
      background-color: var(--bg-switch-active-disabled);

      .ivu-switch-inner::after {
        background-color: var(--bg-switch-active-disabled);
      }
    }
  }

  &.ivu-switch-disabled {
    border: 1px solid var(--border-switch-default-disabled);
    background-color: var(--bg-switch-default-disabled);

    .ivu-switch-inner::after {
      background-color: var(--bg-switch-default-disabled);
    }
  }

  &:focus {
    box-shadow: none;
  }
}

.ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-header {
    background-color: var(--bg-collapse-item);
    color: var(--color-content);
    border-bottom: none;
  }

  .ivu-collapse-content {
    background-color: var(--bg-content);
    color: var(--color-content);
  }
}

.ivu-modal-confirm-head-title,
.ivu-modal-confirm-body {
  color: var(--color-content);
}

.ivu-modal-mask {
  background: rgba(0, 0, 0, 0.5);
}

.ivu-upload-list-file {
  color: white;

  &:hover {
    background: transparent;
  }
}

/*------------------------------element框架ui修改------------------------------*/
//tree
.el-tree {
  background: transparent !important;
  color: var(--color-vertical-tab) !important;

  .el-tree-node {
    // 选中节点颜色
    &.is-current {
      > .el-tree-node__content {
        background-color: var(--bg-vertical-tab-active); //背景色
        color: var(--color-vertical-tab-active);
        .el-tree-node__expand-icon {
          color: var(--color-vertical-tab-active);
          &.is-leaf {
            color: transparent;
            cursor: default;
          }
        }
        .custom-tree-node {
          .icon-font,
          .nodeNum,
          .ivu-btn-text,
          .ivu-checkbox-wrapper {
            color: var(--color-vertical-tab-active);
          }
        }
        .ivu-checkbox-checked .ivu-checkbox-inner,
        .el-checkbox.is-checked .el-checkbox__inner {
          border-color: var(--border-tree-checkbox-inner) !important;
          background-color: var(--bg-tree-checkbox-inner) !important;
        }
        .ivu-switch {
          background-color: var(--bg-tree-current-node-switch);
        }
        .ivu-switch-checked {
          border-color: var(--border-switch-default) !important;
          background-color: var(--bg-tree-checkbox-inner) !important;
        }
        .custom-tree-slot-text {
          color: var(--color-vertical-tab-active);
        }
      }
    }
    &__expand-icon {
      color: var(--color-el-tree-node__expand-icon);
      &.is-leaf {
        color: transparent;
      }
    }
    &.is-expanded.is-focusable {
      background-color: var(--bg-el-tree-node-is-expanded) !important;
    }
  }

  // 点击节点颜色
  .el-tree-node:focus > .el-tree-node__content {
    background-color: var(--bg-vertical-tab-active); //背景色
    color: var(--color-vertical-tab-active);
    .el-tree-node__expand-icon {
      color: var(--color-vertical-tab-active);
      &.is-leaf {
        color: transparent;
        cursor: default;
      }
    }
    .custom-tree-node {
      .icon-font,
      .nodeNum,
      .ivu-btn-text,
      .ivu-checkbox-wrapper {
        color: var(--color-vertical-tab-active);
      }
    }
    .ivu-checkbox-checked .ivu-checkbox-inner,
    .el-checkbox.is-checked .el-checkbox__inner {
      border-color: var(--border-tree-checkbox-inner) !important;
      background-color: var(--bg-tree-checkbox-inner) !important;
    }
    .ivu-switch {
      background-color: var(--bg-tree-current-node-switch);
    }
    .ivu-switch-checked {
      border-color: var(--border-switch-default) !important;
      background-color: var(--bg-tree-checkbox-inner) !important;
    }
    .custom-tree-slot-text {
      color: var(--color-vertical-tab-active);
    }
  }

  .el-tree-node__label {
    font-size: 12px !important;
  }

  .el-tree-node__content:hover {
    color: var(--color-vertical-tab-hover);
    background: var(--bg-vertical-tab-hover);
  }

  .el-tree__empty-text {
    color: var(--color-no-data-img) !important;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    &:before {
      background: var(--bg-tree-checkbox-inner) !important;
    }
  }
}
// 覆盖
.search-tree-box {
  .el-tree {
    .el-tree-node:not(.is-current):focus > .el-tree-node__content {
      background-color: transparent !important; //背景色
      color: var(--color-vertical-tab) !important;
      .el-tree-node__expand-icon:not(.is-leaf) {
        color: var(--color-primary) !important;
      }
    }
  }
}

.el-input {
  .el-input__inner {
    background: @bg-darkblue-block;
    border: 1px solid @border-input-normal;
  }

  .el-input-group__append {
    background-color: @color-other;
    border-color: @color-other;
    color: #fff;
  }
}

// date-picker
.el-picker-panel {
  background: #051e43 !important;
  border: none !important;
  width: 240px !important;

  .popper__arrow {
    display: none !important;
  }

  .el-picker-panel__content {
    width: 220px !important;
    margin: 10px;
  }

  .el-date-picker__header-label.active,
  .el-date-picker__header-label:hover {
    color: #2d8cf0;
  }

  .el-date-picker__header {
    border-bottom: 1px solid #10457e;
    margin: 0;
  }

  .el-date-picker__header-label {
    color: #8d9cb1;
    font-size: 14px;
  }

  .el-date-picker__header--bordered {
    padding-bottom: 0;
  }

  .el-date-table th {
    color: #c5c8ce;
    border-bottom: none;
  }

  .el-date-table td.end-date span,
  .el-date-table td.start-date span {
    background-color: #2d8cf0;
  }

  .el-date-table td.today span {
    color: #2d8cf0;
  }

  .el-date-table td div span,
  .el-year-table td .cell,
  .el-month-table td .cell {
    color: #8d9cb1;
  }

  .el-date-table td {
    width: 24px;
    padding: 2px 0;
  }

  .el-date-table.is-week-mode .el-date-table__row.current div,
  .el-date-table.is-week-mode .el-date-table__row:hover div {
    background-color: #08284f !important;
  }

  .el-picker-panel__icon-btn {
    color: #c5c8ce;
  }

  .el-picker-panel__icon-btn:hover {
    color: #2d8cf0;
  }

  .el-date-picker__prev-btn {
    margin-left: 10px;
  }

  .el-date-picker__next-btn {
    margin-right: 10px;
  }

  .in-range span {
    color: #fff !important;
  }
}

.reset-el-table.el-table {
  &:before {
    background-color: transparent;
  }

  // color: #e7e9ec;
  color: var(--color-table-header-th);
  &,
  .el-table__expanded-cell {
    background-color: transparent;
  }

  th {
    &.el-table__cell {
      &.is-leaf {
        border: none;
      }
    }
  }

  .el-table-column--selection {
    .cell {
      padding: 0 10px;
    }
  }

  thead {
    color: var(--color-table-header-th);
    font-weight: normal;

    tr,
    th {
      background-color: var(--bg-table-header-th);
      border-bottom: none;
      font-weight: normal;
      box-shadow: var(--shadow-table-header);
    }
  }

  .el-table__fixed,
  .el-table__fixed-right {
    background-color: transparent;

    &:before {
      background-color: transparent;
    }
  }

  .el-table__fixed-right-patch {
    background-color: var(--bg-table-header-th);
    border-bottom-color: var(--bg-table-header-th);
  }

  .el-table__body-wrapper,
  .el-table__fixed-body-wrapper {
    .el-table__row {
      &.hover-row {
        > td {
          &.el-table__cell {
            background-color: #10407e;
          }
        }
      }

      td,
      tr {
        background-color: var(--bg-table-body-td);
        border-bottom: none;
      }

      &.el-table__row--striped {
        td,
        tr {
          background-color: var(--bg-table-body-td-2n);
        }
      }
    }

    .el-table__empty-block {
      background: url('../assets/img/common/nodata-light.png') no-repeat;
      background-size: 6%;
      background-position: center;

      .el-table__empty-text {
        color: var(--color-no-data-img);
        font-size: 16px;
        margin-top: 100px;
        margin-right: 10px;
      }
    }
  }
}

.el-table {
  &--enable-row-hover &__body {
    tr:hover {
      > td.el-table__cell {
        background-color: var(--bg-table-row-hover) !important;
      }
    }
  }
}

.el-checkbox {
  width: 16px;
  height: 16px;
  line-height: 16px;

  .el-checkbox__inner {
    background-color: var(--bg-checkbox-inner) !important;
    border-color: var(--border-checkbox__inner) !important;

    &:hover {
      border-color: var(--color-active);
    }
  }

  &.is-checked {
    .el-checkbox__inner {
      border-color: var(--color-active) !important;
      background-color: var(--color-active) !important;
    }
  }
}

.el-notification(100);
.el-notification(@n, @i:1) when (@i =< @n) {
  &.tips-notification-@{i} {
    right: (@i * 5px + 16px) !important;
    bottom: (@i * 5px + 16px) !important;
  }
  .el-notification(@n, (@i + 1));
}

.el-notification {
  background-color: var(--bg-content) !important;
  border-color: var(--bg-el-notification__title) !important;
  box-shadow: 0 2px 12px 0 var(--color-el-notification-shadow) !important;
  padding: 0 !important;
  border-radius: 0 !important;
  z-index: 99999 !important;
  &:nth-of-type(1) {
    bottom: 16px !important;
    right: 16px !important;
  }
  &:nth-of-type(2) {
    bottom: 26px !important;
    right: 26px !important;
  }
  .icon-jinggao {
    color: var(--color-warning);
  }
  .icon-chenggong1 {
    color: #0e8f0e;
  }
  .icon-cuowu {
    color: #bc3c19;
  }

  .el-notification__title {
    color: var(--color-el-notification__title);
    padding: 0 10px;
    height: 44px;
    line-height: 44px;
    border-bottom: 1px solid var(--bg-el-notification__title);
    background-color: var(--bg-el-notification__title);
  }

  .el-notification__group {
    margin: 0;
    width: 100%;

    .el-notification__content {
      height: 200px;
      padding: 15px;
    }
  }

  .el-notification__closeBtn {
    top: 15px;
    right: 10px;
    color: var(--color-el-notification__closeBtn);
  }
}

// .el-checkbox-checked>.el-checkbox__inner {
//     background-color: #2d8cf0;
// }

/*------------------------------地图ui修改----------------------------------*/
/* 地图点位mouseover信息框 */
.u-map-hover {
  position: absolute;
  display: block;
  /*background-color: #fff;*/
  padding: 5px;
  z-index: 19900215;
}

.u-map-hover .hover-container {
  background: hsla(0, 0%, 100%, 0.95);
  color: #000;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.4);
  border-radius: 2px;
  position: relative;
  word-break: break-word;
  text-align: left;
  -webkit-font-smoothing: antialiased;
}

.u-map-hover .hover-container .hover-content {
  position: relative;
  word-break: break-word;
  width: auto;
  height: auto;
  padding: 5px 10px;
  letter-spacing: 1px;
  -webkit-transition: opacity 0.15s;
  transition: opacity 0.15s;
}

.u-map-hover .hover-bottom {
  position: absolute;
  overflow: hidden;
  left: 50%;
  margin-left: 0;
  transform: translate(-10px, 0px);
  -webkit-transform: translate(-10px, 0px);
  height: 12px;
  width: 20px;
}

.u-map-hover .hover-bottom:after {
  content: '';
  width: 10px;
  height: 10px;
  position: absolute;
  background: #fff;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  left: 5px;
  bottom: 7px;
  box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.2);
}

.olMap {
  background-color: transparent !important;
}

.olMapViewport {
  background-color: var(--bg-content) !important;
}

.olPopup {
  height: 100% !important;
  background-color: transparent !important;
}

.v-transfer-dom .ivu-modal-confirm-footer .ivu-btn-text {
  padding: 0 30px;
  height: 34px;
  line-height: 34px;
  color: var(--color-btn-text);
  background: var(--bg-btn-default);
  border: 1px solid var(--border-btn-default);
  opacity: 1;
  border-radius: 4px;

  &:focus {
    box-shadow: none;
  }

  &:hover {
    color: var(--color-btn-default-hover) !important;
    background: var(--bg-btn-default);
    border: 1px solid var(--border-btn-default-hover);
  }

  &:active {
    color: var(--border-btn-default-hover) !important;
    background: var(--bg-btn-default);
    border: 1px solid var(--border-input);
  }
}

.statistics {
  .ivu-poptip-content {
    .ivu-poptip-arrow {
      border-top-color: transparent !important;
      border-right-color: var(--bg-tooltip) !important;
      border-left-color: var(--bg-tooltip) !important;
    }

    .ivu-poptip-arrow:after {
      border-top-color: transparent !important;
      border-right-color: var(--bg-tooltip) !important;
      border-left-color: var(--bg-tooltip) !important;
      filter: drop-shadow(0px 0px 0px rgba(147, 171, 206, 0.7016));
    }
  }

  .ivu-poptip-content {
    .ivu-poptip-inner {
      .ivu-poptip-body-content {
        .standard-model {
          .ui-table {
            min-height: 280px;
          }
        }
      }
    }
  }
}

.ivu-poptip-arrow {
  border-top-color: transparent !important;
}

.el-loading-mask {
  background-color: var(--bg-content) !important;

  .el-loading-spinner {
    height: 107px;
    top: 40%;
    left: 40%;
    background: url('../assets/img/common/loading.gif') no-repeat;

    .circular {
      display: none;
    }
  }
}

.ivu-message {
  .ivu-message-notice {
    .ivu-message-notice-content {
      background: var(--bg-msg);
      color: var(--color-input);
      border: 1px solid var(--border-input);

      &.ivu-message-notice-content-success {
        .ivu-icon:before {
          background-image: url('~@/assets/img/message/success-light.png');
          color: #fff;
          content: '';
          font-size: 12px;
          width: 16px;
          height: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      &.ivu-message-notice-content-warning {
        .ivu-message-warning {
          .ivu-icon:before {
            background-image: url('~@/assets/img/message/warning-light.png');
            background-repeat: no-repeat;
            background-size: contain;
            color: #fff;
            content: '';
            font-size: 12px;
            width: 16px;
            height: 16px;
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }

      &.ivu-message-notice-content-error {
        .ivu-icon:before {
          background: url('~@/assets/img/message/error-light.png') no-repeat;
          background-size: contain;
          color: #fff;
          content: '';
          font-size: 12px;
          width: 16px;
          height: 16px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      &.ivu-message-notice-content-loading {
      }
    }
  }
}

.ivu-dropdown {
  .ivu-dropdown-item {
    color: var(--color-dropdown-item);
  }
  .ivu-select-dropdown {
    background-color: var(--bg-select-dropdown);
  }
  .ivu-dropdown-item:hover {
    background: var(--bg-select-item-hover);
    color: var(--color-dropdown-item-hover);
  }
  .ivu-dropdown-item-selected,
  .ivu-dropdown-item.ivu-dropdown-item-selected:hover {
    background: var(--bg-select-item-hover);
    color: var(--color-dropdown-item-hover) !important;
  }
}

// Poptip 样式，开启弹层放置于body内, 且设定popper-class为custom-transfer-poptip即可
.custom-transfer-poptip {
  .ivu-poptip-body {
    padding: 0 !important;
  }

  .ivu-poptip-inner {
    background: var(--bg-tooltip) !important;
    border: 1px solid var(--border-input-hover) !important;
  }

  &[x-placement='bottom-end'],
  &[x-placement='bottom'] {
    .ivu-poptip-arrow {
      border-bottom-color: var(--border-input-hover) !important;

      &:after {
        border-bottom-color: var(--bg-select-dropdown) !important;
        top: 1px;
      }
    }
  }

  &[x-placement='top-end'],
  &[x-placement='top'] {
    .ivu-poptip-arrow {
      border-top-color: var(--border-input-hover) !important;

      &:after {
        border-top-color: var(--bg-select-dropdown) !important;
        bottom: 1px;
      }
    }
  }
}

.tags-transfer-poptip {
  .ivu-poptip-arrow::after {
    border-right-color: var(--bg-tooltip) !important;
  }
}

.ivu-tag {
  background: var(--bg-tag);
  border-radius: 4px;
  border-color: var(--bg-tag);

  .ivu-tag-text {
    color: #ffffff;
  }

  .ivu-icon.ivu-icon-ios-close {
    opacity: 1;

    &:before {
      color: #ffffff;
    }
  }

  &:hover {
    opacity: 1;
  }
}

//ivu-tooltip
.tooltip {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-color: #0000;
  border-style: solid;
}

.ivu-tooltip-popper {
  .ivu-tooltip-content {
    .ivu-tooltip-inner {
      color: var(--color-tooltip-inner);
      background-color: var(--bg-tooltip);
      border: 1px solid var(--border-tooltip-arrow);
      max-width: 400px;
    }
  }

  .ivu-tooltip-inner-with-width {
    white-space: normal !important;
  }

  &[x-placement^='top'] {
    .ivu-tooltip-arrow {
      border-top-color: var(--border-tooltip-arrow);

      &:before {
        .tooltip();
        bottom: 1px;
        left: -5px;
        border-width: 5px 5px 0;
        border-top-color: var(--border-tooltip);
      }
    }
  }

  &[x-placement^='bottom'] {
    .ivu-tooltip-arrow {
      border-bottom-color: var(--border-tooltip-arrow);

      &:before {
        .tooltip();

        left: -5px;
        top: 1px;
        border-width: 0 5px 5px;
        border-bottom-color: var(--border-tooltip);
      }
    }
  }

  &[x-placement^='right'] {
    .ivu-tooltip-arrow {
      border-right-color: var(--border-tooltip-arrow);

      &:before {
        .tooltip();

        top: -5px;
        right: -6px;
        border-width: 5px 5px 5px 0;
        border-right-color: var(--border-tooltip);
      }
    }
  }

  &[x-placement^='left'] {
    .ivu-tooltip-arrow {
      border-left-color: var(--border-tooltip-arrow);

      &:before {
        .tooltip();

        top: -5px;
        right: 1px;
        border-width: 5px 0 5px 5px;
        border-left-color: var(--border-tooltip);
      }
    }
  }
}

.ivu-message-notice-content-text {
  padding-right: 10px !important;
}
.el-popover {
  background: var(--bg-select-dropdown) !important;
  border: 0 !important;
  .popper__arrow {
    background: var(--bg-select-dropdown) !important;
    &::after {
      border-bottom-color: var(--bg-select-dropdown) !important;
    }
  }
}
.el-menu--vertical {
  .el-menu {
    background-color: #073250;
    .el-menu-item {
      color: #fff;
      &:hover {
        background: linear-gradient(270deg, rgba(19, 122, 185, 0) 0%, #2b84e2 100%);
      }
    }
  }
}

.home-date-transfer-box-body {
  .ivu-date-picker-cells-month,
  .ivu-date-picker-cells-year {
    margin: 3px 0 !important;
    span {
      margin: 4px 12px !important;
    }
    .ivu-date-picker-cells-cell-focused {
      background-color: transparent !important;
    }
  }
  .ivu-picker-confirm {
    .ivu-btn-default {
      display: none !important;
    }
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
  }
}
.tooltip-tip-box.ivu-tooltip-popper .ivu-tooltip-content .ivu-tooltip-inner {
  max-width: 600px;
  max-height: 600px;
}
.tooltip-sample-graph .ivu-tooltip-popper .ivu-tooltip-content .ivu-tooltip-inner {
  max-width: 900px;
  max-height: 900px;
}

.add-dropdown-box {
  .ivu-poptip-arrow {
    border-top-color: transparent !important;
    border-right-color: transparent !important;
    &:after {
      left: 0 !important;
      bottom: -6px !important;
      border-right-width: 8px !important;
      border-right-color: var(--bg-tooltip) !important;
    }
  }
}

.text-tooltip-box.ivu-tooltip-popper {
  .ivu-tooltip-content .ivu-tooltip-inner {
    min-width: 320px !important;
    max-height: 100% !important;
  }
}

.ivu-input[disabled] {
  color: var(--color-input-disabled);
}
