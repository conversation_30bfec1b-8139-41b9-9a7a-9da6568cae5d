<template>
  <div class="height-full">
    <listPage :listObj="listObj" :taskObj="taskObj" :currentTree="currentTree" @refeshCount="refeshCount">
      <template #description="{ row }">
        <span
          :class="[
            row.description === '合格' ? 'font-green' : '',
            row.description === '不合格' ? 'color-failed' : '',
            row.description === '无法检测' ? 'font-D66418' : '',
          ]"
          >{{ row.description }}</span
        >
      </template>
      <template #videoSignal="{ row }">
        <span
          :class="[
            row.videoSignal === '1' ? 'font-green' : '',
            row.videoSignal === '2' ? 'color-failed' : '',
            row.videoSignal === '0' ? 'font-D66418' : '',
          ]"
          >{{
            row.videoSignal === '1'
              ? '正常'
              : row.videoSignal === '2'
                ? '异常'
                : row.videoSignal === '0'
                  ? '无法检测'
                  : '--'
          }}</span
        >
      </template>
      <template #videoBrightness="{ row }">
        <span
          :class="[
            row.videoBrightness === '1' ? 'font-green' : '',
            row.videoBrightness === '2' ? 'color-failed' : '',
            row.videoBrightness === '0' ? 'font-D66418' : '',
          ]"
          >{{
            row.videoBrightness === '1'
              ? '正常'
              : row.videoBrightness === '2'
                ? '异常'
                : row.videoBrightness === '0'
                  ? '无法检测'
                  : '--'
          }}</span
        >
      </template>
      <template #videoColorCast="{ row }">
        <span
          :class="[
            row.videoColorCast === '1' ? 'font-green' : '',
            row.videoColorCast === '2' ? 'color-failed' : '',
            row.videoColorCast === '0' ? 'font-D66418' : '',
          ]"
          >{{
            row.videoColorCast === '1'
              ? '正常'
              : row.videoColorCast === '2'
                ? '异常'
                : row.videoColorCast === '0'
                  ? '无法检测'
                  : '--'
          }}</span
        >
      </template>
      <template #videoClear="{ row }">
        <span
          :class="[
            row.videoClear === '1' ? 'font-green' : '',
            row.videoClear === '2' ? 'color-failed' : '',
            row.videoClear === '0' ? 'font-D66418' : '',
          ]"
          >{{
            row.videoClear === '1'
              ? '正常'
              : row.videoClear === '2'
                ? '异常'
                : row.videoClear === '0'
                  ? '无法检测'
                  : '--'
          }}</span
        >
      </template>
      <template #videoOcclusion="{ row }">
        <span
          :class="[
            row.videoOcclusion === '1' ? 'font-green' : '',
            row.videoOcclusion === '2' ? 'color-failed' : '',
            row.videoOcclusion === '0' ? 'font-D66418' : '',
          ]"
          >{{
            row.videoOcclusion === '1'
              ? '正常'
              : row.videoOcclusion === '2'
                ? '异常'
                : row.videoOcclusion === '0'
                  ? '无法检测'
                  : '--'
          }}</span
        >
      </template>
      <template slot-scope="{ row }" slot="option">
        <ui-btn-tip
          icon="icon-chakanjietu"
          content="查看截图"
          disabled
          v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
        ></ui-btn-tip>
        <ui-btn-tip v-else icon="icon-chakanjietu" content="查看截图" @click.native="showResult(row)"></ui-btn-tip>
      </template>
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
    </listPage>
    <resultModel ref="result"></resultModel>
  </div>
</template>

<script>
import api from '@/config/api/inspectionrecord';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'commonPage',
  components: {
    listPage: require('./list').default,
    resultModel: require('./result-model.vue').default,
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {},
    },
    countInfo: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      bigPictureShow: false,
      imgList: [],
      searchData: {},
      listObj: {
        abnormalCount: [
          {
            title: '视频监控设备总数',
            count: '0',
            key: 'total',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '实测设备数量',
            count: '0',
            key: 'actualAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频质量合格设备',
            countKey: '0',
            key: 'passAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频质量异常设备',
            countKey: '0',
            key: 'notPassAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频遮挡',
            countKey: '0',
            key: 'videoOcclusionAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频信号异常',
            countKey: '0',
            key: 'videoSignalAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频亮度异常',
            countKey: '0',
            key: 'videoSignalAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频偏色',
            countKey: '0',
            key: 'videoCcolorCastAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
          {
            title: '视频清晰度异常',
            countKey: '0',
            key: 'videoCleanAmout',
            icon: 'icon-shipinliuzhiliangjiance',
          },
        ],
        columns: [
          { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText', width: 120 },
          { title: 'IP地址', key: 'ipAddr', tooltip: true, align: 'left', minWidth: 120 },
          { title: '检测结果', slot: 'description', width: 120 },
          { title: '视频信号', slot: 'videoSignal', width: 120 },
          { title: '视频亮度', slot: 'videoBrightness', width: 120 },
          { title: '视频偏色', slot: 'videoColorCast', width: 120 },
          { title: '视频清晰', slot: 'videoClear', width: 120 },
          { title: '视频遮挡', slot: 'videoOcclusion', width: 120 },
          { title: '检测时间', key: 'startTime', align: 'left', width: 160 },
          { title: '操作', slot: 'option', fixed: 'right', width: 100, align: 'left' },
        ],
        loadData: (parameter) => {
          return this.$http.post(api.videoQualityPageList, Object.assign(parameter)).then((res) => {
            return res.data;
          });
        },
      },
      videoStyles: {
        width: '5rem',
      },
      videoOptions: {},
      videoVisible: false,
      videoUrl: '',
      playDeviceCode: '',
    };
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    refeshCount(orgCode) {
      this.$emit('refeshCount', orgCode);
    },
    showResult(row) {
      this.$refs.result.showModal(row);
    },
  },
  async mounted() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    countInfo: {
      deep: true,
      handler: function () {
        let indexObj = JSON.parse(this.countInfo.indexJson);
        this.listObj.abnormalCount.map((val) => {
          indexObj.map((item) => {
            if (val.key === item.key) {
              val.count = item.desc;
            }
          });
          if (val.key === 'passRate') {
            let rate = val.value.toFixed(2) + '%';
            val.value = rate;
          }
        });
      },
    },
  },
};
</script>

<style lang="less" scoped>
.btn {
  margin: 0 6px;
}
.auto-fill {
  height: 100%;
}
.video-player {
  @{_deep} .ivu-modal-body {
    height: 530px;
    display: flex;
    flex-direction: column;
  }
}
</style>
