<template>
  <Button ghost class="logout-btn" @click="logOut"><i class="iconfont icon-tuichudenglu"></i></Button>
</template>
<script>
export default {
  name: `iHeaderLogout`,
  methods: {
    logOut () {
      this.$Modal.confirm({
        title: '友情提示',
        width: 450,
        closable: true,
        content: `确定退出登录吗？`,
        onOk: () => {
          this.confirmLoading = this.$Message.loading({ content: '退出中...', duration: 0 })
          this.$store.dispatch('handleLogOut').then(res => {
            this.$router.push('/login')
          }).finally(() => {
            this.confirmLoading()
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .logout-btn, .logout-btn:hover, .logout-btn:active {
    border-radius: 50%;
    border: none;
    width: 24px;
    height: 24px;
    padding: 0;
    background: #F8775C;
    color: #fff !important;
    .iconfont {
      font-size: 14px;
      margin: 0 auto !important;
      line-height: 24px;
    }
  }
</style>