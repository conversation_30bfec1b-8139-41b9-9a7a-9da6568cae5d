<template>
  <div class="map-box">
    <div :id="mapId" class="map"></div>
    <Modal
      ref="deviceModal"
      class-name="domModal"
      v-model="deviceModalOption.open"
      reset-drag-position
      footer-hide
      draggable
      sticky
      :title="deviceModalOption.title"
      :width="modelWidth"
      :styles="{ top: 0, width: 'auto' }"
      center
      :mask="false"
      @on-cancel="closeDeviceModal"
    >
      <component
        :ref="sectionMenuName"
        :is="sectionMenuName"
        :map-dom-data="mapDomData"
        :is-map-click="isMapClick"
        @preDetial="(Index) => $emit('update:currentClickIndex', Index)"
        @nextDetail="(Index) => $emit('update:currentClickIndex', Index)"
        @close="closeDeviceModal"
        :clickIndexId="clickIndexId"
        @isModelDetail="changeModelDetail"
      ></component>
    </Modal>
    <mouse-title ref="mouseDom" :name="mouseName"></mouse-title>
  </div>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
import axios from "axios";
import face from "@/views/operations-on-the-map/map-default-page/components/map-dom/face-map-dom.vue";
import vehicle from "@/views/operations-on-the-map/map-default-page/components/map-dom/vehicle-map-dom.vue";
import humanbody from "@/views/operations-on-the-map/map-default-page/components/map-dom/humanbody-map-dom.vue";
import nonmotorVehicle from "@/views/operations-on-the-map/map-default-page/components/map-dom/nonmotorVehicle-map-dom.vue";
import wifi from "@/views/operations-on-the-map/map-default-page/components/map-dom/wifi-map-dom.vue";
import point from "@/views/operations-on-the-map/map-default-page/components/map-dom/point-map-dom.vue";
import electric from "@/views/operations-on-the-map/map-default-page/components/map-dom/electric-map-dom.vue";
import rfid from "@/views/operations-on-the-map/map-default-page/components/map-dom/rfid-map-dom.vue";
import gps from "@/views/operations-on-the-map/map-default-page/components/map-dom/gps-map-dom.vue";
import etc from "@/views/operations-on-the-map/map-default-page/components/map-dom/etc-map-dom.vue";
import device from "@/views/operations-on-the-map/map-default-page/components/map-dom/device-map-dom.vue";
import mouseTitle from "./mouse-title.vue";
let mapMain2 = null;
let trackLayer = null; // 轨迹中心轨迹
let pointLayer = null;
let _wonderLayer = null; // 轨迹连线
let animationLine = null;
const muenInfoWindow = [];
export default {
  components: {
    face,
    vehicle,
    humanbody,
    nonmotorVehicle,
    wifi,
    point,
    electric,
    rfid,
    gps,
    etc,
    device,
    mouseTitle,
  },
  props: {
    // 地图图层配置信息
    mapLayerConfig: {
      type: Object,
      default: () => {
        return {
          tracing: false, // 是否需要刻画轨迹
          showStartPoint: false, // 是否显示起点终点图标
          selectionResult: true, // 是否显示框选结果弹框
          resultOrderIndex: false, // 搜索结果排序,
          showLatestLocation: false, // 显示地图最新位置
        };
      },
    },
    // 当前点击的轨迹节点
    currentClickIndex: {
      type: Number,
      default: 0,
    },
    // 是否禁止地图的滚动条事件
    disableScroll: {
      type: Boolean,
      default: false,
    },
    // 切换类型 加载弹框
    sectionName: {
      type: String,
      default: "",
    },
    // 轨迹中心点位
    trackPoints: {
      type: Array,
      default: () => [],
    },
    // 轨迹中心点位连线
    trackConnected: {
      type: Boolean,
      default: false,
    },
    cutIcon: {
      type: String,
      default: "",
    },
    idlerWheel: {
      type: Boolean,
      default: false,
    },
    // 地图定位
    positionPoints: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    // 普通搜索-列表点击的index
    currentClickIndex: {
      handler(newVal) {
        if (newVal == -1) {
          return;
        }
        const { currentClickIndex, points = [], sectionMenuName } = this;
        const pointItem = (points.length && points[currentClickIndex]) || {};
        // 缺 经纬度 地图不展示点位
        if (
          !pointItem.geoPoint ||
          !pointItem.geoPoint.lon ||
          !pointItem.geoPoint.lat
        ) {
          return this.$Message.warning("经纬度信息不全");
        }
        this.sectionMenuName = pointItem["type"]
          ? pointItem.type
          : this.sectionName + "";
        // 普通搜索弹出模态框
        this.$nextTick(() => {
          if (pointItem) {
            if (pointItem["type"]) {
              this.selectItem(
                pointItem,
                this.sectionMenuName,
                null,
                false,
                false,
                true
              );
            } else {
              this.selectItem(pointItem, this.sectionMenuName, null, true);
            }
          }
          // 活动轨迹下方切换
          let cutIcon = this.cutIcon == "track" ? false : true;
          pointItem &&
            this.$refs[this.sectionMenuName]
              .init(points[currentClickIndex], pointItem, "", cutIcon, true)
              .then((res) => {
                if (res.data) {
                } else {
                  this.$Message.warning("暂无数据");
                }
              });
        });
        this.closeAllInfoWindow();
        if (this.mapLayerConfig.resultOrderIndex) {
          this.resetMarker();
        }
        // 人体和非机动车 recordId 人脸和车辆 id
        if (
          this.sectionMenuName == "face" ||
          this.sectionMenuName == "vehicle"
        ) {
          this.clickIndexId = this.points[this.currentClickIndex].id;
        } else {
          this.clickIndexId = this.points[this.currentClickIndex].recordId;
        }
        this.sprinkleHandler(points);
      },
    },
    trackPoints: {
      handler(newVal) {
        if (newVal.length) {
          newVal.map((item, index) => {
            item.Index = index + 1;
          });
          this.points = [...newVal];
          this.closeAllInfoWindow(); //关闭弹窗
          this.resetMarker(); //重置
          this.sprinkleHandler(this.points); //撒点
          setTimeout(() => {
            this.rePosition(this.points);
          }, 200);
        } else {
          this.points = [];
          this.closeAllInfoWindow(); //关闭弹窗
          this.resetMarker(); //重置
        }
      },
      immediate: true,
    },
    trackConnected: {
      handler(newVal) {
        if (animationLine) {
          animationLine.stop();
          animationLine.remove();
          animationLine = null;
        }
        if (newVal) {
          // if (_wonderLayer) {
          //   _wonderLayer.removeAllOverlays();
          // } else {
          //   _wonderLayer = new NPMap.Layers.OverlayLayer(
          //       "_wonderLayer",
          //       false
          //   );
          //   mapMain2.map.addLayer(_wonderLayer);
          // }
          // let trackPolyline = new NPMapLib.Geometry.Polyline(this.points, {
          //     color: "#2D87F9",
          //     weight: 3, //线的宽度，以像素为单位
          //     opacity: 1, //线的透明度，取值范围0-1
          //     lineStyle: NPMapLib.LINE_TYPE_SOLID //线的样式
          // });
          // _wonderLayer.addOverlay(trackPolyline);
          // mapMain2.map.zoomToExtent(
          //     trackPolyline.getExtent(),
          //     false,
          //     true
          // );

          if (this.points.length > 1) {
            this.getRoadNet(this.points);
            // this.getRoadNet(this.points.map(v => v.lon + ',' + v.lat))
          }
        } else {
          if (_wonderLayer) {
            mapMain2.map.removeOverlay(_wonderLayer);
            _wonderLayer.removeAllOverlays();
            _wonderLayer = null;
          }
        }
      },
    },
    positionPoints: {
      handler(newVal) {
        if (newVal.length) {
          newVal.map((item, index) => {
            item.Index = index + 1;
          });
          this.points = [...newVal];
          setTimeout(() => {
            this.closeAllInfoWindow(); //关闭弹窗
            this.resetMarker(); //重置
            this.pointToMap(this.points); //撒点
            this.rePosition(this.points);
          }, 200);
        } else {
          this.points = [];
          this.closeAllInfoWindow(); //关闭弹窗
          this.resetMarker(); //重置
        }
      },
      immediate: true,
    },
  },
  computed: {
    modelWidth() {
      let offset = this.isShowModelDetail ? 220 : 0;
      return this.deviceModalOption.width + offset;
    },
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
      resourceCoverage: "map/getResourceCoverage",
    }),
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      mapDomData: {},
      sectionMenuName: "", // 当前选中的menuName
      markers: [], // 运动轨迹图层
      isMapClick: false,
      clickIndexId: 0,
      deviceModalOption: {
        open: false,
        title: "设备详情",
        width: "950px",
      },
      isShowModelDetail: false,
      mouseName: "",
    };
  },
  deactivated() {
    this.closeAllInfoWindow();
  },
  async mounted() {
    await this.getMapConfig();
    this.loading = false;
    if (!this.idlerWheel) {
      this.mapidlerWheel(); //防止地图与滚动条 滚轮冲突
    }
  },
  methods: {
    mapidlerWheel() {
      this.$nextTick(() => {
        let box = document.querySelector(".map");
        box.onmousewheel = (event) => {
          event = event || window.event;
          box.style.height = box.clientHeight;
          //取消火狐浏览器默认行为（因为是用addEventListener,所以必须用此方法来取消）
          event.preventDefault && event.preventDefault();
          //取消浏览器默认行为
          return false;
        };
        //为火狐浏览器绑定鼠标
        this.bind(box, "DOMMouseScroll", box.onmousewheel);
      });
    },
    bind(obj, eventStr, callback) {
      if (obj.addEventListener) {
        obj.addEventListener(eventStr, callback, false);
      } else {
        obj.attachEvent("on" + eventStr, function () {
          callback.call(obj);
        });
      }
    },
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain2 = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain2.init(mapId, data, style);
        // 禁止滚动条
        if (this.disableScroll) {
          mapMain2.map.disableScrollWheelZoom();
        }
        this.configDefaultMap();
        this.$emit("loaded");
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain2.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain2.map.centerAndZoom(point, mapLayerLevel);
    },
    // 配置最大层级
    addlimitLayerNum() {
      if (!!Number(this.mapObj.maxNumberOfLayer) || 600) {
        let allLayers = mapMain2.map.getAllLayers.length;
        if (allLayers >= this.limitLayerNum) {
          this.$Message.error("已超过配置最大图层数量");
          return false;
        }
      }
      return true;
    },
    // 关闭多个弹框
    closeAllInfoWindow() {
      this.deviceModalOption.open = false;
    },
    //关闭普通搜索-弹框
    closeDeviceModal() {
      if (this.sectionMenuName == "device") {
        this.$refs[this.sectionMenuName].stopVideo();
        this.resetMarker(); //重置
      }
      this.closeAllInfoWindow();
      this.$emit("chooseMapItem", -1);
    },
    preDetial(Index) {
      this.$emit("update:currentClickIndex", Index);
    },
    nextDetail(Index) {
      this.$emit("update:currentClickIndex", Index);
    },
    moveModal() {
      let zoomLat = 0.0005;
      let zoom = mapMain2.map.getMaxZoom();
      // 获取不同弹框位置偏移量
      if (zoom == 19) {
        zoomLat = 0.0001;
      } else if (zoom == 18) {
        zoomLat = 0.0002;
      } else if (zoom == 17) {
        zoomLat = 0.0005;
      }
      return zoomLat;
    },
    // 点击图层展示弹框,isMapClick 地图上点击不展示左右切换详情
    selectItem(
      pointItem,
      sectionName,
      isMapClick = false,
      isAprink = false,
      distance = false,
      noZoom = false
    ) {
      if (isAprink) {
        // 点击列表后 切换地图显示图片
        this.sprinkleHandler([pointItem]);
      }
      pointItem.currentClickIndex = this.currentClickIndex;
      this.isMapClick = isMapClick;
      this.closeAllInfoWindow();
      this.sectionMenuName = !!sectionName ? sectionName : sectionMenuName;
      // 显示之前先清除其他提示框
      const { sectionMenuName } = this;
      // const point = new NPMapLib.Geometry.Point(+pointItem.lon + pointLon, +pointItem.lat + pointLat)
      if (!noZoom) {
        const point = new NPMapLib.Geometry.Point(
          pointItem.lon || pointItem.geoPoint.lon,
          pointItem.lat || pointItem.geoPoint.lat
        );
        // 获取图层，计算弹框向下偏移量
        let zoomLat = this.moveModal();
        //mapMain2.map.setCenter(new NPMapLib.Geometry.Point(pointItem.lon || pointItem.geoPoint.lon, (pointItem.lat || pointItem.geoPoint.lat) + 0.0025))
        mapMain2.map.centerAndZoom(
          new NPMapLib.Geometry.Point(
            pointItem.lon || pointItem.geoPoint.lon,
            (pointItem.lat || pointItem.geoPoint.lat) + zoomLat
          ),
          mapMain2.map.getMaxZoom()
        );
      }

      let obj = this.handleOffset();
      let title =
        this.sectionMenuName == "device" && pointItem.deviceName
          ? pointItem.deviceName
          : obj.title;
      this.deviceModalOption.title = title;
      this.deviceModalOption.width = obj.width;
      this.deviceModalOption.open = true;
      // 修改弹框位置
      this.$nextTick(() => {
        let dragDom = this.$refs.deviceModal.$el.querySelector(
          ".ivu-modal-content-drag"
        );
        if (dragDom) {
          let left = window.innerWidth / 2 - dragDom.offsetWidth / 2;
          let top = window.innerHeight / 2 - dragDom.offsetHeight;
          dragDom.style.left = left + "px";
          dragDom.style.top = (top < 90 ? 90 : top) + "px";
        }
      });
    },
    zoomInto() {
      this.$nextTick(() => {
        if (this.currentClickIndex == -1) return;
        const pointItem = this.points[this.currentClickIndex];
        // 获取图层，计算弹框向下偏移量
        let zoomLat = this.moveModal();
        mapMain2.map.centerAndZoom(
          new NPMapLib.Geometry.Point(
            pointItem.lon || pointItem.geoPoint.lon,
            (pointItem.lat || pointItem.geoPoint.lat) + zoomLat
          ),
          mapMain2.map.getMaxZoom()
        );
      });
    },
    handleOffset() {
      // 地图弹框
      const stragetyOffset = {
        face: { title: "抓拍详情", width: 842, height: 560 },
        humanbody: { title: "抓拍详情", width: 842, height: 560 },
        nonmotorVehicle: { title: "抓拍详情", width: 842, height: 560 },
        vehicle: { title: "抓拍详情", width: 842, height: 560 },
        device: { title: "设备详情", width: 532, height: 372 },
        default: { title: "详情", width: 490, height: 335 },
      };
      let obj =
        this.sectionMenuName in stragetyOffset
          ? stragetyOffset[this.sectionMenuName]
          : stragetyOffset.default;
      return obj;
    },
    // 重置marker
    resetMarker() {
      // this.$nextTick(( )=> {
      if (mapMain2) {
        mapMain2.map.removeOverlays(this.markers);
        // 分页换切换tab 去除旧的点位
        mapMain2.map.removeOverlay(trackLayer);
        if (trackLayer) {
          trackLayer.removeAllOverlays();
        }
        trackLayer = null;
        mapMain2.map.removeOverlay(pointLayer);
        if (pointLayer) {
          pointLayer.removeAllOverlays();
        }
        pointLayer = null;
        if (animationLine) {
          animationLine.stop();
          animationLine.remove();
          animationLine = null;
        }
      }
      // })
      this.closeAllInfoWindow(); //关闭弹窗
    },
    changeModelDetail(val) {
      this.isShowModelDetail = val;
    },
    // 普通搜索和轨迹撒点
    sprinkleHandler(points) {
      if (!points || points.length === 0) {
        return false;
      }
      let trackMarkers = [];
      let temp = {};
      let multipleIcon = {};
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        let k =
          (item.lon || item.geoPoint.lon) +
          "_" +
          (item.lat || item.geoPoint.lat);
        temp[k] == null ? (temp[k] = 0) : temp[k]++;
        let imgUrl = "",
          label = null,
          icon = null,
          size = null;
        if (item.iconType) {
          //人像or车辆
          size = new NPMapLib.Geometry.Size(40, 40);
        } else {
          size = new NPMapLib.Geometry.Size(35, 35);
        }
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let marker = new NPMapLib.Symbols.Marker(item);
        marker.index = index;
        // 用来判断当前点位上是否有多个图片
        if (multipleIcon[k]) {
          multipleIcon[k] = multipleIcon[k] + 1;
        } else {
          multipleIcon[k] = 1;
        }
        if (this.mapLayerConfig.trackResult) {
          // 轨迹中心撒点
          // 文本标记
          label = new NPMapLib.Symbols.Label(
            `${item.Index ? item.Index : index + 1}`
          );
          // 多数同一点、 一点一数
          imgUrl = require(`@/assets/img/map/trajectory-${
            item.active ? "blue" : "red"
          }.png`);
          label.setOffset(new NPMapLib.Geometry.Size(-0.5, 23));
          label.setStyle({
            fontSize: 14, //文字大小
            fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
            color: !item.active ? "#EA4A36" : "#2C86F8", //文字前景色
            align: "cm", //对方方式
            isBold: true, //是否粗体
          });
          marker.setLabel(label);
          // 设置图片
          icon = new NPMapLib.Symbols.Icon(imgUrl, size);
          icon.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
          );
          marker.setIcon(icon);
          marker.k = k;
          trackMarkers.push(marker);
        }
      }
      this.markers = [...trackMarkers];
      if (!!trackMarkers.length) {
        // 轨迹中心撒点
        if (!trackLayer) {
          trackLayer = new NPMapLib.Layers.OverlayLayer("track");
          mapMain2.map.addLayer(trackLayer);
        }
        trackLayer.addOverlays(trackMarkers);
        let clickFn = (marker) => {
          this.$emit("chooseMapItem", marker.index);
        };
        for (let index = trackMarkers.length - 1; index >= 0; index--) {
          trackMarkers[index].addEventListener(
            NPMapLib.MARKER_EVENT_CLICK,
            clickFn
          );
        }
      }
    },
    pointToMap(points) {
      if (!points || points.length === 0) {
        return false;
      }
      let pointMarkers = [];
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        let imgUrl = "",
          icon = null,
          size = null;
        size = new NPMapLib.Geometry.Size(24, 27);
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let marker = new NPMapLib.Symbols.Marker(item);
        marker.index = index;
        // 多数同一点、 一点一数
        imgUrl =
          item.deviceChildType == "1" ||
          item.deviceChildType == "2" ||
          item.ptzType == "1" ||
          item.ptzType == "2"
            ? require(`@/assets/img/map/mapPoint/map-qiuji.png`)
            : require(`@/assets/img/map/mapPoint/map-qiangji.png`);
        // 设置图片
        icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height / 2)
        );
        marker.setIcon(icon);
        marker.ext = item;
        pointMarkers.push(marker);
      }
      this.markers = [...pointMarkers];
      if (!!pointMarkers.length) {
        if (!pointLayer) {
          pointLayer = new NPMapLib.Layers.OverlayLayer("mapPoint");
          mapMain2.map.addLayer(pointLayer);
        }
        pointLayer.addOverlays(pointMarkers);
        let overFn = (marker) => {
          this.showMouseDom(marker);
        };
        let outFn = (marker) => {
          this.closeMuenWindowDom();
        };
        for (let index = pointMarkers.length - 1; index >= 0; index--) {
          pointMarkers[index].addEventListener("mouseover", overFn);
          pointMarkers[index].addEventListener("mouseout", outFn);
        }
      }
    },
    showMouseDom(marker) {
      let { lat, lon } = marker.ext;
      this.mouseName = marker.ext.deviceName;
      const point = new NPMapLib.Geometry.Point(lon, lat);
      this.$nextTick(() => {
        const dom = this.$refs["mouseDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (60 / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 15;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain2.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        muenInfoWindow.push(infoWindow);
      });
    },
    // 关闭框选目录
    closeMuenWindowDom() {
      muenInfoWindow.forEach((row) => {
        row.close();
      });
    },
    //轨迹回到可视区域
    rePosition(arrList) {
      let minLat, minLon, maxLon, maxLat, sortLat, sortLon;
      if (!arrList || arrList.length === 0) {
        return false;
      }
      sortLat = arrList.map((e) => e.lat);
      sortLon = arrList.map((e) => e.lon);
      minLat = Math.min(...sortLat);
      maxLat = Math.max(...sortLat);
      minLon = Math.min(...sortLon);
      maxLon = Math.max(...sortLon);
      let dLat = maxLat - minLat;
      let dLon = maxLon - minLon;
      let extent = new NPMapLib.Geometry.Extent(
        minLon - dLon * 0.1,
        minLat - dLat * 0.1,
        maxLon + dLon * 0.1,
        maxLat + dLat * 0.1
      );
      if (mapMain2) {
        mapMain2.zoomToExtend(extent);
      }
    },
    // 框选打开弹框(资源图层)
    openMapDom({
      deviceId,
      deviceGbId,
      deviceName,
      detailAddress,
      lat,
      lon,
      deviceType,
      picUrl,
      imageUrls,
      myFavorite,
      placeName,
      placeTypeName,
    }) {
      this.sectionMenuName = "device";
      this.deviceModalOption.open = false;
      if (!deviceId) {
        return;
      }
      // 设置显示弹框
      this.$nextTick(() => {
        this.$refs["device"]
          .init(
            { deviceId, deviceGbId, modelType: "1" },
            {
              geoPoint: {
                lat,
                lon,
              },
              deviceName,
              deviceId,
              deviceGbId,
              detailAddress,
              deviceType,
              picUrl,
              imageUrls,
              myFavorite,
            }
          )
          .then((res) => {
            if (res.data) {
              this.selectItem(
                { deviceName, lat, lon },
                "device",
                true,
                null,
                true
              );
            } else {
              this.$Message.warning("暂无数据");
            }
          });
      });
    },
    getRoadNet(stops) {
      let pointList = stops.map(
        (v) => (v.lon || v.geoPoint.lon) + "," + (v.lat || v.geoPoint.lat)
      );
      let linePoints = pointList.join(";");
      try {
        let data = new FormData();
        data.append("stops", linePoints);
        axios
          .post("/npgisdataservice/gis/routing", data)
          .then((res) => {
            // 路网画线
            this.renderLinesByRoadNet(res.data);
          })
          .finally(() => {
            // this.$Message.warning("路网不存在，显示直线轨迹");
          });
      } catch (error) {
        let pointData = [];
        stops.forEach((item) => {
          pointData.push([
            item.lon || item.geoPoint.lon,
            item.lat || item.geoPoint.lat,
          ]);
        });
        this.createPolyline(pointData);
      }
    },
    // 根据路网创建地图线路
    renderLinesByRoadNet(roatNet) {
      if (!roatNet || !roatNet.length) {
        return;
      }
      let points = [];
      roatNet.forEach((item) => {
        if (!item.expend) return;
        // if (item.expend.indexOf('POINT') === -1) {
        // 过滤到非路网点
        points = points.concat(item.expend.split(";").map((p) => p.split(",")));
        // }
      });
      return this.createPolyline(points);
    },
    // 根据点信息画线
    createPolyline(data) {
      let points = [];
      for (let i = 0; i < data.length; i++) {
        let p = new NPMap.Geometry.Point(data[i][0], data[i][1]);
        points.push(p);
      }
      // data.forEach(item => {
      //     points.push(
      //         // new NPMapLib.Geometry.Point(item[0], item[1])
      //         // new NPMapLib.Geometry.Point(item.geoPoint.lon, item.geoPoint.lat)
      //         new NPMapLib.Geometry.Point((item.lon || item.geoPoint.lon), (item.lat || item.geoPoint.lat))
      //     )
      // })
      if (points.length === 0) {
        return;
      }
      if (_wonderLayer) {
        _wonderLayer.removeAllOverlays();
      } else {
        _wonderLayer = new NPMap.Layers.OverlayLayer("_wonderLayer", false);
        mapMain2.map.addLayer(_wonderLayer);
      }
      let trackPolyline = new NPMapLib.Geometry.Polyline(points, {
        color: "#2D87F9",
        weight: 5, //线的宽度，以像素为单位
        opacity: 1, //线的透明度，取值范围0-1
        lineStyle: NPMapLib.LINE_TYPE_SOLID, //线的样式
      });
      _wonderLayer.addOverlay(trackPolyline);
      mapMain2.map.zoomToExtent(trackPolyline.getExtent());

      let offset = new NPMap.Geometry.Size(-20, -20);
      let headerMarker = new NPMap.Symbols.Marker(points[0], {
        offset: offset,
      });
      let size = new NPMapLib.Geometry.Size(40, 40);
      let imgUrl =
        this.sectionName == "vehicle"
          ? require(`@/assets/img/map/temptracker.png`)
          : require(`@/assets/img/map/map-person-icon.png`);
      let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
      headerMarker.setIcon(icon);
      _wonderLayer.addOverlay(headerMarker);

      let options = {
        color: "red",
        renderFirst: true,
        opacity: 0.01,
        layer: _wonderLayer,
        weight: 1,
        headerMarker: headerMarker,
      };
      animationLine = new NPMap.Symbols.AnimationLine(
        mapMain2.map.id,
        points,
        options
      );
      animationLine.setSpeed(10);
      animationLine.start();
    },
    polylinePause() {
      animationLine.pause();
    },
    polylineContinus() {
      animationLine.start();
    },
  },
  beforeDestroy() {
    if (mapMain2) {
      this.resetMarker();
      mapMain2.destroy();
       _wonderLayer = null;
      mapMain2 = null;
    }
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }

  .draw-track {
    position: absolute;
    z-index: 500;
    left: 20px;
    right: 54px;
    bottom: 20px;
    width: 1456px;
    height: 48px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .play-operate {
      .iconfont {
        margin-right: 16px;
      }
    }

    .draw-progress {
      //   width: 1300px;
      width: calc(~"100% - 120px");
      /deep/.ivu-progress {
        overflow: hidden;

        &-text {
          color: #fff;

          .ivu-icon {
            font-size: 16px;
          }
        }

        &-bg {
          height: 8px !important;
          border-radius: 4px;
          color: #fff;
          position: relative;
        }

        &-bg:before {
          content: "";
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #ffffff;
          position: absolute;
          top: -3px;
          right: 0;
        }
      }
    }

    .speed-list {
      position: absolute;
      right: 0;
      bottom: 48px;
      padding: 10px 0;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      border-radius: 5px;

      .speed-item {
        padding: 10px 20px;
        text-align: center;
        cursor: pointer;

        &.selected {
          color: #2c86f8;
        }
      }
    }

    .times-btn {
      // width:43px;
      border-radius: 10px;
      background-color: #fff;
      padding: 2px 10px;
      // font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      cursor: pointer;
      &.active {
        background-color: #2c86f8;
        color: #fff;
      }
    }
  }
}
.content {
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
  .box {
    display: flex;
    width: 50%;
    .box-title {
      margin-left: 100px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 16px;
    }
    .box-content {
      font-size: 16px;
      margin-left: 10px;
    }
  }
  .box-img {
    width: 560px;
    height: 400px;
  }
}
/deep/ #npgis_GroupDiv {
  overflow: inherit !important;
}
/deep/.olPopupContent {
  overflow: inherit !important;
}
/deep/.domModal {
  .ivu-modal-body {
    padding: 0 !important;
  }
}
/deep/ .ivu-modal-close {
  top: 5px;
  .ivu-icon-ios-close {
    color: #fff;
  }
}
/deep/ .ivu-modal-header {
  background: rgb(44, 134, 248);
  border-bottom: none;
  padding: 10px;
  .ivu-modal-header-inner {
    font-size: 14px;
    font-weight: 700;
    color: #fff;
  }
}
</style>
