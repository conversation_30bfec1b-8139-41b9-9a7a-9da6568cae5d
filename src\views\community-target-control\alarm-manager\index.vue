<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-25 10:35:54
 * @Description: 
-->
<template>
  <div class="layout">
    <tabsPage v-model="selectLi" :list="componentList" />
    <keep-alive>
      <component :is="currentComponent.componentName" :key="selectLi" />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import AlarmManager from "./alarm-manager/index.vue";
import RiskPersonnel from "./risk-personnel/index.vue";
import LivingAlonePeople from "./living-alone-people/index.vue";
import Frequent from "./frequent/index.vue";
import NightOutCommunity from "./night-out-community/index.vue";
import PeerAnalysis from "./peer-analysis/index.vue";

export default {
  name: "alarm-manager",
  components: {
    tabsPage,
    AlarmManager,
    RiskPersonnel,
    LivingAlonePeople,
    Frequent,
    NightOutCommunity,
    PeerAnalysis,
  },
  data() {
    return {
      selectLi: 1,
      componentList: [
        {
          label: "社区重点人员发现报警",
          value: 1,
          componentName: "AlarmManager",
        },
        {
          label: "社区内异常行为预警",
          value: 2,
          componentName: "RiskPersonnel",
        },
        {
          label: "孤寡老人多日未出现",
          value: 3,
          componentName: "LivingAlonePeople",
        },
        {
          label: "陌生人频繁出入小区",
          value: 4,
          componentName: "Frequent",
        },
        {
          label: "夜间出入多小区",
          value: 5,
          componentName: "NightOutCommunity",
        },
        {
          label: "社区人员与重点人同行",
          value: 6,
          componentName: "PeerAnalysis",
        },
      ],
    };
  },
  computed: {
    currentComponent() {
      let component = this.componentList.find(
        (item) => item.value == this.selectLi
      ) || {
        label: "前科人员发现报警",
        value: 1,
        radioList: [],
        componentName: "CriminalRecord",
      };
      return component;
    },
  },
  created() {
    if (this.$route.query.compareType) {
      this.selectLi = Number(this.$route.query.compareType);
    }
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
