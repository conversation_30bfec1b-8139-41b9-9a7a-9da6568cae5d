import governancetask from '@/config/api/governancetask';
const mixin = {
  methods: {
    //获取批量操作的数据统计
    async getBatchBeforeStatistics() {
      const name = this.componentName;
      const params = this.returnHandleBatchParams();
      try {
        if (name) {
          this.batchLoadingObj[name] = true;
        }
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, params);
        this.batchStatisticsObj = data.data;
      } catch (err) {
        console.log(err);
      } finally {
        if (name) {
          this.batchLoadingObj[name] = false;
        }
      }
    },
    /**
     * 工单关闭、指派、处理
     */
    //点击批量
    async handleBatch(name, isBatch, row) {
      this.isDisabled = true;
      this.componentName = name;
      this.isBatch = isBatch;
      this.sucessData = [];
      this.modalTitle = '任务' + this.titles[name];
      if (this.isBatch) {
        if (!this.selectData.length && !this.isCheckAll) {
          this.$Message.error('请勾选治理工单！');
          return;
        }
        //获取批量操作数据
        await this.getBatchBeforeStatistics();
        if (this.batchStatisticsObj.useCount === 0) {
          const text = `有${this.batchStatisticsObj.total - this.batchStatisticsObj.useCount}条工单无需（或无权）${
            this.titles[name]
          }，请重新选择！`;
          this.$Message.error(text);
          return;
        }
        this.getBatchPageList();
      } else {
        this.sucessData.push(row);
      }
      this.orderOptionShow = true;
    },
    /**
     * 工单报备
     */
    async batchToReport() {
      if (!this.selectData.length && !this.isCheckAll) {
        this.$Message.error('请勾选治理工单！');
        return;
      }
      try {
        //1.1 获取可操作数量
        this.batchLoadingObj['Report'] = true;
        const statisticParams = this.returnHandleBatchParams({ batchType: 'report' });
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, statisticParams);
        const statisticData = data.data;
        this.batchLoadingObj['Report'] = false;
        //1.2 可操作数量大于1000或为0不向下进行
        if (statisticData.useCount > 1000) {
          this.$Message.warning({
            content: '每次最多选择1000条设备进行报备！',
            duration: 3,
          });
          return;
        }
        if (statisticData.useCount === 0) {
          this.$Message.error(`共${statisticData.total}条工单无需（或无权）报备,请重新选择`);
          return;
        }
        //2.1 获取报备提示
        const tiptext =
          statisticData.useCount === statisticData.total
            ? `确定对此${statisticData.useCount}条工单设备进行报备吗？`
            : `共${statisticData.total - statisticData.useCount}条工单无需（或无权）报备,确定对剩余${
                statisticData.useCount
              }条工单设备进行报备吗？`;
        //2.2 获取报备参数
        const params = { ...statisticParams, pageSize: 1000 };
        //2.3 执行报备操作
        this.toReportHandler(params, tiptext);
      } catch (err) {
        console.log(err);
      }
    },
    //执行报备操作,跳转到情况报备
    toReportHandler(params, tipText) {
      this.$UiConfirm({
        content: tipText,
        title: '提示',
        hasLoading: true,
      }).then(async ($vm) => {
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(governancetask.batchPageList, params);
          if (!data.data.entities?.length) {
            this.$Message.error('无可报备的设备，请重新选择');
            return; // 未获取到数据不跳转
          }
          this.$router.push({
            name: 'situationreporting',
            params: {
              purpose: 'autoChooseAndCreateByDatas', //目的
              datas: data.data.entities,
            },
          });
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
    //查看报备详情（跳转到对应页面）
    handleToViewReportDetail(row) {
      if (!row.examReportReviewVo?.id) {
        return;
      }
      let hasPermission = this.routerList.findIndex((item) => item.name === 'situationreporting');
      if (hasPermission === -1) {
        this.$Message.warning({
          content: '您暂无情况报备的权限，请联系管理员添加!',
          duration: 3,
        });
        return;
      }
      //页面跳转到情况报备
      this.$router.push({
        name: 'situationreporting',
        params: {
          purpose: 'openReportDetailModalById', //目的
          detailId: row.examReportReviewVo.id,
        },
      });
    },
    /**
     * 工单撤回
     */
    //单条(签收,指派)工单撤回
    handleSignAndAssignCancel(name, row) {
      let url = name === 'signCancel' ? governancetask.workOrderSignCancel : governancetask.workOrderAssignCancel;
      let tiptext = `确定撤回${name === 'signCancel' ? '签收' : '指派'}此条工单吗?`;
      let params = { ids: [row.id] };
      this.signAndAssignCancelHandler(url, params, tiptext);
    },
    //执行(签收,指派)工单撤回请求
    signAndAssignCancelHandler(url, params, tipText) {
      this.$UiConfirm({
        content: tipText,
        title: '提示',
        hasLoading: true,
      }).then(async ($vm) => {
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(url, params);
          this.$util.common.responseMessage({ ...data.data, successText: '撤回成功', failureText: '撤回失败' }, false);
          this.getTableData();
          this.selectDataReset();
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
    /**
     * 工单签收
     */
    //批量签收
    async batchSignTips() {
      try {
        if (!this.selectData.length && !this.isCheckAll) {
          this.$Message.error('请勾选治理工单！');
          return;
        }
        // 1.1 获取批量操作的统计
        this.batchLoadingObj['Sign'] = true;
        const statisticParams = this.returnHandleBatchParams({ batchType: 'sign' });
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, statisticParams);
        const statisticData = data.data;
        this.batchLoadingObj['Sign'] = false;
        // 1.2 可操作数量为0
        if (statisticData.useCount === 0) {
          this.$Message.error(`共${statisticData.total}条工单无需（或无权）签收,请重新选择`);
          return;
        }
        // 2.1 提示
        const tiptext =
          statisticData.total === statisticData.useCount
            ? '确定签收选中的工单任务吗？签收后请及时处理'
            : `${
                statisticData.total - statisticData.useCount
              }个工单无需签收，确定签收剩余的工单任务吗？签收后请及时处理`;
        // 2.2 参数
        const params = this.isCheckAll
          ? { ...statisticParams }
          : { ids: this.selectData.map((item) => item.id), batchType: 'sign' };
        // 2.3 执行
        this.signTipHandler(params, tiptext);
      } catch (err) {
        console.log(err);
      }
    },
    //单条签收
    async singleSignTips(row) {
      const param = { ids: [row.id] };
      this.signTipHandler(param);
    },
    // 执行签收操作
    signTipHandler(params, tipText = '确定签收该工单任务吗？签收后请及时处理！') {
      this.$UiConfirm({
        content: tipText,
        title: '警告',
        hasLoading: true,
      }).then(async ($vm) => {
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(governancetask.batchSign, params);
          this.$util.common.responseMessage({ ...data.data, successText: '成功签收' });
          this.getTableData();
          this.selectDataReset(); // 清空数据
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
    /**
     * 工单删除
     */
    //批量删除
    async batchDelete() {
      try {
        if (!this.selectData.length && !this.isCheckAll) {
          this.$Message.error('请勾选治理工单！');
          return;
        }
        // 1.1 获取批量操作的统计
        this.batchLoadingObj['Delete'] = true;
        const statisticParams = this.returnHandleBatchParams({ batchType: 'delete' });
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, statisticParams);
        const statisticData = data.data;
        this.batchLoadingObj['Delete'] = false;
        // 1.2 可操作数量为0
        if (statisticData.useCount === 0) {
          this.$Message.error(`有${statisticData.total}条工单您无权删除, 请重新选择！`);
          return;
        }
        // 2.1 获取操作提示
        const tiptext =
          statisticData.total === statisticData.useCount
            ? `确定要删除此${statisticData.useCount}条工单吗？`
            : `有${statisticData.total - statisticData.useCount}条工单您无权删除。确定要删除剩余${
                statisticData.useCount
              }条工单吗？`;
        // 2.2 删除参数
        const params = this.isCheckAll
          ? { ...statisticParams }
          : { ids: this.selectData.map((item) => item.id), batchType: 'delete' };
        this.deleteHandler(params, tiptext);
      } catch (err) {
        console.log(err);
      }
    },
    //单条删除
    singleDelete(row) {
      let param = { ids: [row.id] };
      this.deleteHandler(param);
    },
    //执行删除操作
    deleteHandler(params, tipText = '确定要删除此工单吗？') {
      this.$UiConfirm({
        content: tipText,
        title: '警告',
        hasLoading: true,
      }).then(async ($vm) => {
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(governancetask.remove, params);
          this.getTableData();
          this.$Modal.remove();
          this.$util.common.responseMessage({ ...data.data, successText: '成功删除' });
          this.selectDataReset(); // 清空数据
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
  },
};

export default mixin;
