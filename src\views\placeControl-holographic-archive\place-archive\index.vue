<template>
  <place-archives
    :isNewGoInfo="true"
    :goInfo="goArchivesInfo"></place-archives>
</template>
<script>
import PlaceArchives from "@/views/holographic-archives/one-place-one-archives/place-archives/index.vue";
export default {
  name: "placeControlPlaceArchive",
  components: {
    PlaceArchives,
  },
  methods: {
    /**
     * @description: 跳转到档案详情
     * @param {string} 场所档案id
     */
    goArchivesInfo(archiveNo) {
      const { href } = this.$router.resolve({
        path: "/placeControl-place-archive/place-dashboard",
        query: { archiveNo, source: "place" },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
