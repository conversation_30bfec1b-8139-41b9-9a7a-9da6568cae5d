<template>
    <Modal ref="modal" title="定位" v-model="isShow" width="1000" @on-cancel="cancleOperation">
        <template>
            <div class="drag-dialog-container-file" v-if="isShow">
                <mapPosition ref="map" @inited="onInited"></mapPosition>
            </div>
        </template>
    </Modal>
</template>
<script>
import mapPosition from '@/components/map/map-position.vue'
export default {
    name: "mapPositionModal",
    data: function() {
        return {
            isShow: false,
            markers: null,
            pointsData: []
        };
    },
    components: {
        mapPosition
    },
    methods: {
        showPoints(points) {
            this.isShow = true
            this.pointsData = points
        },
        cancleOperation() {
            this.pointsData = []
            this.isShow = false
        },
        onInited() {
            if (this.pointsData) {
                this.showPoint(this.pointsData);
            }
        },
        showPoint(points) {
            if (this.markers) {
                this.$refs.map.removeAllMarkers("POSITION", this.markers);
            }
            var markersParam = [];
            if (points.constructor != Array) {
                points.longitude = points.lon ? points.lon : 0;
                points.latitude = points.lat ? points.lat : 0;
                if (
                    points.longitude === undefined ||
                    points.longitude === 0 ||
                    points.latitude === undefined ||
                    points.latitude === 0
                ) {
                    return;
                }
                markersParam.push({
                    longitude: points.longitude,
                    latitude: points.latitude,
                    markerType: "DEFAULT",
                    iconNormal: null,
                    iconHover: null,
                    label: {},
                    zIndex: 10,
                    data: points.data
                });
            } else {
                for (let i = 0; i < points.length; i++) {
                    const p = points[i];
                    if (
                        p.longitude === undefined ||
                        p.longitude === 0 ||
                        p.latitude === undefined ||
                        p.latitude === 0
                    ) {
                        continue;
                    }
                    markersParam.push({
                        longitude: p.longitude,
                        latitude: p.latitude,
                        markerType: "DEFAULT",
                        iconNormal: null,
                        iconHover: null,
                        label: {},
                        zIndex: 10,
                        data: p.data
                    });
                }
            }

            this.markers = this.$refs.map.addMarkers("POSITION", markersParam, {});
        }
    }
};
</script>
<style lang="less">
.drag-dialog-fileStructure-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(102, 102, 102, 0.7);
    z-index: 999;
    display: table-cell;
    text-align: center;
}
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-active {
    opacity: 0;
}
.drag-dialog-fileStructure-head {
    height: 40px;
    line-height: 40px;
    .drag-dialog-fileStructure-Title {
        display: inline-block;
        position: relative;
        left: 0;
    }
    .closeBtn {
        position: relative;
        right: 0;
        display: inline-block;
        cursor: pointer;
        z-index: 616;
    }
}

.drag-dialog-container-file {
    width: 100%;
    height: 600px;
    background-color: #fff;
    z-index: 999;
    cursor: move;
    position: relative;
    .drag-dialog-content-file {
        width: 100%;
        height: 100%;
    }
    .tabMap {
        position: relative;
    }
}
</style>
