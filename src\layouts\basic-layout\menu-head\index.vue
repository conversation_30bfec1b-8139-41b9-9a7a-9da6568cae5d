<template>
  <div
    :class="{ 'i-layout-menu-head-mobile': isMobile }"
    class="i-layout-menu-head"
  >
    <Menu
      ref="menu"
      :active-name="headerName"
      mode="horizontal"
      :class="{ 'menu-list': shrinkShow }"
    >
      <template v-for="(item, index) in filterHeader.slice(0, 9)">
        <MenuItem
          v-if="!item.hidden"
          :key="index"
          :to="item.isExternalLink == 1 ?item.resourceUrl: item.path"
          :replace="item.replace"
         :target="item.isExternalLink == 1 ? '_blank' : item.target"
          :name="item.meta.title"
          @click.native="handleClick(item.path, 'header')"
        >
          <i-menu-head-title :item="item" :key="index" />
        </MenuItem>
      </template>
      <Dropdown v-if="filterHeader.length > 9 && !shrinkShow" transfer>
        <div class="menu-more" @click="moreIcon = !moreIcon">
          更多
          <Icon v-if="moreIcon" type="ios-arrow-up" />
          <Icon v-else type="ios-arrow-down" />
        </div>
        <DropdownMenu
          :key="index"
          v-if="moreIcon"
          v-for="(item, index) in filterHeader.slice(9, filterHeader.length)"
          slot="list"
        >
          <MenuItem
            v-if="!item.hidden"
            :key="index"
             :to="item.isExternalLink == 1 ?item.resourceUrl: item.path"
            :replace="item.replace"
            :target="item.isExternalLink == 1 ? '_blank' : item.target"
            :name="item.meta.title"
            @click.native="handleClick(item.path, 'header')"
          >
            <!-- <i-menu-head-title :item="item" :key="index" /> -->
            <div class="more-down">
              <i :class="item.meta.icon" class="iconfontconfigure" />
              <span>{{ item.meta.title }}</span>
            </div>
          </MenuItem>
        </DropdownMenu>
      </Dropdown>
      <template>
        <i
          class="iconfont icon-doubleleft arrowsIcon"
          v-if="!shrinkShow"
          @click="handleShrink(0)"
        ></i>
        <i
          class="iconfont icon-doubleright arrowsIcon"
          v-else
          @click="handleShrink(1)"
        ></i>
      </template>
    </Menu>
  </div>
</template>
<script>
import iMenuHeadTitle from "./title";
import { mapState, mapGetters } from "vuex";
import { getStyle } from "view-design/src/utils/assist";
import clickItem from "../mixins/click-item";
// eslint-disable-next-line no-unused-vars
import { on, off } from "view-design/src/utils/dom";
// eslint-disable-next-line no-unused-vars
import { throttle } from "lodash";

export default {
  name: `iMenuHead`,
  components: { iMenuHeadTitle },
  mixins: [clickItem],
  computed: {
    ...mapState("admin/layout", ["isMobile"]),
    ...mapState("admin/menu", ["headerName"]),
    ...mapGetters("admin/menu", ["filterHeader"]),
    ...mapGetters({
      individuation: "individuation",
    }),
  },
  data() {
    return {
      handleResize: () => {},
      isMenuLimit: false,
      menuMaxWidth: 0, // 达到这个值后，menu 就显示不下了
      moreIcon: false,
      shrinkShow: true,
    };
  },
  watch: {
    filterHeader(val) {
      this.handleGetMenuHeight();
    },
    isMobile() {
      this.handleGetMenuHeight();
    },
    "individuation.menuConfig": {
      handler(newVal) {
        this.shrinkShow = newVal;
      },
      immediate: true,
    },
  },

  mounted() {
    // this.handleResize = throttle(this.handleGetMenuHeight, 100, { leading: false });
    // on(window, 'resize', this.handleResize);
    this.handleGetMenuHeight();
    // this.shrinkShow = this.individuation.menuConfig;
  },
  beforeDestroy() {
    // off(window, 'resize', this.handleResize);
  },
  methods: {
    handleGetMenuHeight() {
      const menuWidth = parseInt(getStyle(this.$el, "width"));
      const $menu = this.$refs.menu;
      if ($menu) {
        const menuHeight = parseInt(getStyle(this.$refs.menu.$el, "height"));
        if (menuHeight > 64) {
          if (!this.isMenuLimit) {
            this.menuMaxWidth = menuWidth;
          }
          this.isMenuLimit = true;
        }
      } else if (menuWidth >= this.menuMaxWidth) {
        this.isMenuLimit = false;
      }
    },
    // 目录收缩
    handleShrink(index) {
      this.shrinkShow = index == 0 ? true : false;
    },
  },
};
</script>
<style lang="less" scoped>
.i-layout-menu-head {
  .ivu-menu {
    width: 100%;
    transition: width 0.5s;
  }
  .menu-list {
    overflow: hidden;
    width: 150px;
    transition: width 0.5s;
  }
  .arrowsIcon {
    font-size: 22px;
    line-height: 52px;
    color: #fff;
    cursor: pointer;
    position: relative;
    top: 2px;
    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  /deep/.menu-more {
    color: #fff !important;
    height: 54px;
    cursor: pointer;
    position: relative;
    top: -2px;
  }
  /deep/.ivu-layout-header {
    line-height: 54px !important;
  }
  /deep/.ivu-menu-horizontal {
    line-height: 54px !important;
  }
}
.i-layout-dropdown-menu {
  font-size: 12px;
  /deep/ .ivu-dropdown-menu {
    padding: 5px 10px !important;
    &:hover {
      background: rgba(44, 134, 248, 0.1);
    }
  }
}
.more-down {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 2px 10px;
  i {
    color: #2c86f8;
  }
  span {
    margin-left: 5px;
  }
}
.more-down:hover {
  color: #2c86f8;
  background: rgba(44, 134, 238, 0.2);
}
</style>
