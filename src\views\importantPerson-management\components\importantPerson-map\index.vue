<!--
 * @Date: 2025-01-16 11:07:33
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-29 19:21:55
 * @FilePath: \icbd-view\src\views\juvenile\components\juvenile-map\index.vue
-->
<template>
  <div class="juvenile-map">
    <div class="map-box">
      <mapBase
        ref="mapBase"
        :mapLayerConfig="mapLayerConfig"
        :searchBtn="false"
        :juvenilePlaceList="allPlaceList"
        :layerCheckedNames="layerCheckedNames"
        :layerTypePlaceList="placeKindList"
        @onload="onload"
      >
        <div class="hotel-heatmap">
          <div>重点人活动热力图</div>
          <i-switch v-model="heatmapSwitch" @on-change="onHeatmapChange" />
        </div>
      </mapBase>
      <PlaceList
        ref="placeKindListRef"
        class="place-dropdown"
        @changeLayerName="changeLayerName"
        :placeList="placeKindList"
        title="重点场所"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import mapBase from "@/views/juvenile/components/juvenile-map/mapBase.vue";
import PlaceList from "@/views/juvenile/components/juvenile-map/placeList.vue";
import {
  getConfigPlaceSecondLevels,
  getConfigPlaces,
  getHeatmapData,
} from "@/api/monographic/importantPerson-management";

export default {
  name: "",
  components: {
    mapBase,
    PlaceList,
  },
  props: {
    alarmList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      mapLayerConfig: {
        tracing: false, // 是否需要刻画轨迹
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: false, // 框选操作栏
        selectionResult: false, // 是否显示框选结果弹框
        resultOrderIndex: false, // 搜索结果排序,
        showLatestLocation: false, // 显示地图最新位置
      },
      allPlaceList: [],
      layerCheckedNames: [],
      placeKindList: [],
      heatData: [], //热力图数据
      heatmapSwitch: true, //热力图开关
    };
  },
  computed: {
    ...mapGetters({
      placeSecondLevelList: "dictionary/getPlaceSecondLevelList", // 场所二级分类
    }),
  },
  async created() {
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    onload() {
      this.initMapBasePlacelayer();
      this.queryHeatData();
    },
    async initMapBasePlacelayer() {
      await this.getPlaceKindList();
      await this.getPlaceList();
      // this.$refs.mapBase.juvenileAlarmList(this.alarmList);
      this.$refs.placeKindListRef.initAllSelectPlace();
    },
    getPlaceList() {
      getConfigPlaces().then(({ data }) => {
        this.allPlaceList = data?.map((el) => ({
          ...el,
          iconType: this.placeKindList.find(
            (item) => item.key === el.secondLevel
          )?.icon,
          LayerType: el.secondLevel,
        }));
      });
    },
    async getPlaceKindList() {
      const { data = [] } = await getConfigPlaceSecondLevels();
      const arr = [];
      data?.forEach((item) => {
        let icon = item.icon;
        if (icon) {
          icon = JSON.parse(icon);
        }
        let val = {
          key: item.typeCode,
          title: item.typeName,
          icon: icon?.["font_class"] || "hotel",
          color: icon?.["color"] || "#EB8A5D",
        };
        arr.push(val);
      });
      this.placeKindList = arr;
    },
    async queryHeatData() {
      const { data } = await getHeatmapData({});
      const heatData = data?.map((v) => {
        return {
          ...v.geoPoint,
          numCount: v.times,
        };
      });
      this.$refs.mapBase.renderHeatMap(heatData, 20);
    },
    onHeatmapChange(status) {
      this.$refs.mapBase.setHeatmapVisible(status);
    },
    changeLayerName(value) {
      this.layerCheckedNames = value;
    },
  },
};
</script>

<style lang="less" scoped>
.juvenile-map {
  width: 100%;
  height: 100%;
  .map-box {
    width: 100%;
    height: 100%;
    position: relative;
    .place-dropdown {
      position: absolute;
      top: 20px;
      right: 0;
    }
    .map-alarm {
      position: absolute;
      top: 150px;
      right: 80px;
    }
  }
  .hotel-heatmap {
    position: absolute;
    bottom: 15px;
    right: 10px;
    display: flex;
    gap: 5px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.75);
  }
}
</style>
