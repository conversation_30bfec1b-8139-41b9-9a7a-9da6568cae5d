<template>
  <div class="basisAdd" ref="mains">
    <!-- 第一步 -->
    <div class="first" v-show="componentName === 'first'">
      <Row class="basis-header">
        <Col :span="24" class="title">
          <div class="border-header"></div>
          <div class="title-header">第一步</div>
        </Col>
      </Row>
      <div class="first-content">
        <div>
          <Form ref="modalData" :model="modalData" :rules="modalDataValidate" :label-width="80">
            <FormItem label="表名称" prop="tableName" class="left-item">
              <Input
                type="text"
                v-model="modalData.tableName"
                class="width-input"
                placeholder="请输入表名称"
                :maxlength="50"
              ></Input>
            </FormItem>
            <FormItem label="表注释" prop="tableComment" class="left-item">
              <Input
                type="text"
                v-model="modalData.tableComment"
                class="width-input"
                placeholder="请输入表注释"
                :maxlength="50"
              ></Input>
            </FormItem>
            <FormItem label="类型" prop="tableType" class="left-item">
              <Select v-model="modalData.tableType" class="width-input" placeholder="请选择">
                <Option v-for="item in metaFieldTypeData" :key="item.id" :value="item.dataKey">{{
                  item.dataValue
                }}</Option>
              </Select>
            </FormItem>
            <FormItem label="维护人" prop="maintenancer" class="left-item">
              <Input
                type="text"
                v-model="modalData.maintenancer"
                class="width-input"
                placeholder="请输入维护人"
                :maxlength="50"
              ></Input>
            </FormItem>
            <FormItem label="联系方式" prop="maintenancerPhone" class="left-item">
              <Input
                type="text"
                v-model="modalData.maintenancerPhone"
                class="width-input"
                placeholder="请输入联系方式"
                :maxlength="50"
              ></Input>
            </FormItem>
            <FormItem class="left-item">
              <Button class type="primary" @click="next('second')">下一步</Button>
            </FormItem>
          </Form>
        </div>
      </div>
    </div>
    <!-- 第二步 -->
    <div class="second" v-show="componentName === 'second'">
      <Row class="basis-header">
        <Col :span="12" class="title">
          <div class="border-header"></div>
          <div class="title-header">第二步</div>
        </Col>
        <Col :span="12" align="right" style="position: relative">
          <div style="position: absolute; right: 0">
            <Button type="primary" @click="next('first')" class="mr10">
              <span class="vt-middle">上一步</span>
            </Button>
            <Button type="primary" @click="save">
              <span class="vt-middle">保存</span>
            </Button>
          </div>
        </Col>
      </Row>
      <Divider />
      <div class="basis-content">
        <div class="basis-content-left">
          <Scroll class="scroll-basis" :on-reach-bottom="handleReachBottom" :height="tableHeight">
            <div class="search-input">
              <Input
                prefix="ios-search"
                v-model="searchData.searchValue"
                placeholder="请输入字段名称"
                @on-enter="search"
              />
            </div>
            <div class="basis-content-checkbox">
              <CheckboxGroup v-model="checkboxArray" @on-change="changeVal">
                <div class="checkbox-list" v-for="item in checkboxList" :key="item.id">
                  <Checkbox :label="item.id">
                    <Tooltip placement="top">
                      <div class="checkbox-item">{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldComment }})</div>
                      <div slot="content">
                        <div>{{ item.fieldName }}&nbsp;&nbsp;({{ item.fieldComment }})</div>
                      </div>
                    </Tooltip>
                  </Checkbox>
                </div>
              </CheckboxGroup>
            </div>
          </Scroll>
        </div>
        <div class="basis-content-right">
          <div class="left-div">
            <div class="pr20 pl20 pt16">
              <ui-table
                class="ui-table"
                :loading="loading"
                :table-columns="tableColumns"
                :table-data="tableData"
                ref="table"
                :minus-height="230"
              >
                <template slot-scope="{ row }" slot="fieldType">{{ metaFieldType[row.fieldType] }}</template>
                <template slot-scope="{ row }" slot="action">
                  <a @click="deleteItem(row)">删除</a>
                </template>
              </ui-table>
            </div>
            <loading v-if="loading"></loading>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import metadatamanagement from '@/config/api/metadatamanagement';
import { mapActions } from 'vuex';
import algorithm from '@/config/api/algorithm';
export default {
  name: 'basisAdd',
  props: {},
  data() {
    const validateTableName = (rule, value, callback) => {
      let regUrl = /^[A-Za-z0-9-!_]{1,20}$/;
      if (!value || value === '') {
        callback(new Error('请输入表名称'));
      } else if (!regUrl.test(value)) {
        callback(new Error('请输入1-50位字母、数字和_的表名称'));
      } else {
        callback();
      }
    };
    const validateTableComment = (rule, value, callback) => {
      let regUrl = /[^\u4E00-\u9FA5]{1,20}$/;
      if (!value || value === '') {
        callback(new Error('请输入表注释'));
      } else if (regUrl.test(value)) {
        callback(new Error('请输入1-50位表注释，仅限汉字'));
      } else {
        callback();
      }
    };
    return {
      checkboxArray: [],
      loading: false,
      searchData: {
        searchValue: '',
        totalCount: 0,
        pageNum: 1,
        params: {
          pageNumber: 0,
          pageSize: 100,
        },
      },
      componentName: 'first',
      modalData: {},
      tableHeight: '',
      modalDataValidate: {
        tableName: [{ required: true, validator: validateTableName, trigger: 'blur' }],
        tableComment: [{ required: true, validator: validateTableComment, trigger: 'blur' }],
        tableType: [{ required: true, message: '请选择类型', trigger: 'blur' }],
        maintenancer: [{ required: true, message: '请输入维护人', trigger: 'blur' }],
        maintenancerPhone: [{ required: true, message: '请输入联系方式', trigger: 'blur' }],
      },
      tableColumns: [
        { title: '字段名', key: 'fieldName' },
        { title: '注释', key: 'fieldComment' },
        { title: '类型', slot: 'fieldType' },
        { title: '长度', key: 'fieldLength' },
        { title: '操作', slot: 'action', fixed: 'right' },
      ],
      tableData: [],
      metaFieldTypeData: [],
      metaFieldType: {},
      checkboxList: [],
    };
  },
  created() {},
  activated() {
    this.$nextTick(() => {
      document.getElementsByClassName('scroll-basis')[0].addEventListener('scroll', function () {});
      // 获取table距顶部高度 - 公共占用高度
      var tableOffsetTop = this.$refs.table.$el.offsetTop;
      // 获取当前页总高度
      var pageHeight = this.$refs.mains.scrollHeight;
      //
      this.tableHeight = pageHeight - tableOffsetTop - 160;
      if (this.$route.query.id) {
        this.id = this.$route.query.id;
        this.info();
      } else {
        this.getAlgorithmList();
      }
      this.handleReachBottom();
    });
  },
  methods: {
    ...mapActions({
      closeCacheRouter: 'tabs/closeCacheRouter',
    }),
    async info() {
      await this.getAlgorithmList();
      await this.getInfo();
      this.$forceUpdate();
    },
    // 路由更新刷新时调用
    getParams() {
      if (this.$route.query.componentName && this.$route.query.componentName === 'basisEdit') {
        this.id = this.$route.query.id;
      }
    },
    // 获取详情
    async getInfo() {
      try {
        let res = await this.$http.get(metadatamanagement.mangeView + this.id);
        if (res.data.code === 200) {
          this.tableData = res.data.data.fieldList || [];
          this.modalData = res.data.data;
          this.checkboxArray = this.tableData.map((val) => {
            return val.id;
          });
        } else {
          this.tableData = [];
          this.modalData = {};
        }
      } catch (err) {
        this.tableData = [];
      }
    },
    getAlgorithmList() {
      this.$http.get(algorithm.dictData + 'metaTableType').then((res) => {
        if (res.data.code === 200) {
          this.metaFieldTypeData = res.data.data || [];
          res.data.data.map((val) => {
            this.metaFieldType[val.dataKey] = val.dataValue;
          });
        }
      });
    },
    //
    changeVal() {},
    // 检索
    search() {
      this.searchData.params.pageNumber = 0;
      this.handleReachBottom('clear');
    },
    next(item) {
      if (item === 'second') {
        if (this.validate('modalData') === 'error') {
          return false;
        }
      }
      this.componentName = item;
    },
    // 加载
    handleReachBottom(type) {
      return new Promise((resolve) => {
        this.searchData.params.pageNumber += 1;
        if (type && type === 'clear') {
          this.checkboxList = [];
        }
        this.$http
          .post(metadatamanagement.managementPageList, this.searchData)
          .then((res) => {
            if (!res.data.data.entities || res.data.data.entities.length === 0) {
              this.$Message.warning('暂无更多数据');
              this.searchData.params.pageNumber -= 1;
            }
            res.data.data.entities.map((val) => {
              this.checkboxList.push(val);
            });
          })
          .catch((error) => {
            console.log(error);
          });
        resolve();
      });
    },
    // 删除数据
    deleteItem(item) {
      this.checkboxArray.map((val, index) => {
        if (val === item.id) {
          this.checkboxArray.splice(index, 1);
        }
      });
    },
    // 保存
    async save() {
      this.modalData.fieldList = this.tableData;
      if (!this.$route.query.id) {
        try {
          let res = await this.$http.post(metadatamanagement.mangeAdd, this.modalData);
          if (res.data.code === 200) {
            this.closeCacheRouter('basisAdd');
          } else {
            this.$Message.error('保存失败');
          }
          this.visible = false;
        } catch (err) {
          this.$Message.error('保存失败');
          console.log(err);
        }
      } else {
        try {
          let res = await this.$http.put(metadatamanagement.mangeUpdate, this.modalData);
          if (res.data.code === 200) {
            this.closeCacheRouter('basisEdit');
          } else {
            this.$Message.error('保存失败');
          }
          this.visible = false;
        } catch (err) {
          this.$Message.error('保存失败');
          console.log(err);
        }
      }
    },
    validate(name) {
      let message = 'success';
      this.$refs[name].validate((valid) => {
        if (valid) {
          message = 'success';
        } else {
          message = 'error';
        }
      });
      return message;
    },
  },
  watch: {
    checkboxArray: {
      deep: true,
      handler() {
        let tableData = JSON.parse(JSON.stringify(this.tableData));
        this.tableData = [];

        this.checkboxArray.map((val1) => {
          if (
            this.checkboxList
              .map((val) => {
                return val.id;
              })
              .includes(val1)
          ) {
            this.checkboxList.map((val) => {
              if (val.id === val1) {
                this.tableData.push(val);
              }
            });
          } else {
            tableData.map((val) => {
              if (val.id === val1) {
                this.tableData.push(val);
              }
            });
          }
        });
      },
    },
    $route: 'getParams',
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.basisAdd {
  height: 100%;

  .basis-header {
    padding: 20px;
    .title {
      display: flex;
      .border-header {
        width: 8px;
        height: 30px;
        background: #239df9;
        margin-right: 6px;
      }
      .title-header {
        color: #fff;
        width: 394px;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        font-size: 16px;
      }
    }
  }
  .first {
    background-image: url('../../../../assets/img/metadatamanagement/basisAddOrEdit.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 100%;
    .first-content {
      width: 100%;
      height: 100%;
      padding-right: 100px;
      & > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 500px;
        height: 80%;
        margin: 0 auto;
      }
    }
    .width-input {
      width: 380px;
    }
  }
  .second {
    height: 100%;
    .left-div {
      position: relative;
    }
    /deep/ .ivu-divider-horizontal {
      margin: 0;
      background: #07355e;
    }
    .mr30 {
      margin-right: 30px !important;
    }
    .basis-content {
      display: flex;
      white-space: nowrap;
      height: calc(100% - 70px);
      overflow: hidden;
      .basis-content-left {
        display: inline-block;
        width: 420px;
        height: calc(100vh - 200px);
        overflow-y: hidden;
        overflow-x: hidden;
        border-right: 1px solid var(--border-color);
        // & > div {
        // 	height: 100%;
        // 	overflow-y: auto;
        // 	overflow-x: hidden;
        // }
        .search-input {
          padding: 6px 20px 10px 20px;
          display: flex;
          .ivu-input-wrapper {
            // width: calc(100% - 54px);
          }
          .search-input {
            display: inline-block;
            width: 20px;
          }
          .ivu-icon-md-add {
            margin-left: 20px;
            font-size: 16px;
            color: #2b84e2;
            cursor: pointer;
            position: relative;
            top: 8px;
          }
        }
        .scroll-basis {
          width: 100%;
          /deep/ .ivu-scroll-container {
            width: calc(100% + 3px);
          }
        }
        .basis-content-checkbox {
          padding-left: 20px;
          // padding-right: 12px;
          // /deep/ .ivu-col-span-8 {
          // 	padding: 4px 0;
          // }
          /deep/ .ivu-checkbox {
            position: relative;
            top: 1px;
          }
          /deep/ .ivu-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            // justify-content: space-between;
          }
          .checkbox-list {
            padding: 4px 14px 4px 0;
            &:hover {
              .checkbox-item {
                color: #2b84e2;
              }
            }
          }
          /deep/ .ivu-checkbox-group-item {
            display: flex;
            .checkbox-item {
              font-size: 14px;
              color: #b8c5d5;
              padding-left: 10px;
              width: 92px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
          /deep/ .ivu-tooltip-inner {
            background: #0d3560;
          }
          /deep/ .ivu-tooltip-arrow {
            border-top-color: #0d3560;
          }
        }
      }
      .basis-content-right {
        flex: 1 1;
        text-align: right;
        white-space: nowrap;
      }
    }
  }
  .pr20 {
    padding-right: 20px;
  }
  .pl20 {
    padding-left: 20px;
  }
  .pt16 {
    padding-top: 16px;
  }
  .mr10 {
    margin-right: 10px;
  }
}
</style>
