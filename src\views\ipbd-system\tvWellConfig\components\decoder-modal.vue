<template>
    <ui-modal v-model="visible" title="添加解码器" :r-width="800" @onOk="comfirmHandle" @onCancel="onCancel">
        <div class="content">
            <div class="decoder-msg">
                <p class="title">解码器设备信息</p>
                <Form ref="form" :model="form" :rules="ruleForm" class="form">
                    <Row>
                        <Col span="12">
                            <FormItem label="解码器名称" :label-width="100" prop="name">
                                <Input v-model="form.name" :maxlength="20" @on-change="changeInput" placeholder="请输入解码器名称" />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="解码器IP" :label-width="100" prop="ip">
                                <Input v-model="form.ip" :maxlength="20" @on-change="changeInput" placeholder="非标协议必填，设备IP" />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="端口号" :label-width="100" prop="port">
                                <Input v-model="form.port" :maxlength="20" @on-change="changeInput" placeholder="请输入服务端口号" />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="用户名" :label-width="100" prop="userName">
                                <Input v-model="form.userName" :maxlength="20" @on-change="changeInput" placeholder="请输入用户名" />
                            </FormItem>
                        </Col>
                        <Col span="12">
                            <FormItem label="密码" :label-width="100" prop="password">
                                <Input v-model="form.password" :maxlength="20" @on-change="changeInput" placeholder="请输入密码" />
                            </FormItem>
                        </Col>
                    </Row>
                </Form>
            </div>
            <div class="monitor-btn">
                <Button type="primary" size="small" @click="handleMonitor">
                    获取监视器
                </Button>
            </div>
            <div>
                <p class="title">监视器信息</p>
                <Table
                    class="auto-fill table" 
                    ref="table"
                    :height="300"
                    :columns="columns" 
                    :data="tableData"
                >
                </Table>
            </div>
            <ui-loading v-if="loading"></ui-loading>
        </div>
    </ui-modal>
</template>
<script>
import { demodifierAdd, demodifierScreen, demodifierView, demodifierUpdate } from '@/api/config';
export default {
    props: {
        wellId: {
            type: String,
            default: ''
        }
    },
    data() {
        const validateIp = (rule, value, callback) => {
            if(value == ''){
                callback(new Error('请输入解码器IP'))
            }else if(value) {
                // ip规则
                let ipregExp = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
                if(!ipregExp.test(value)){
                    value = '';
                    callback(new Error('解码器IP格式不正确'))
                }else{
                    callback()
                }
            }
        }
        return {
            visible: false,
            tableData: [],
            columns: [
                { title: '监视器编号', key: 'gbdecodeChanID' },
                { title: '窗口编号', key: 'window' },
                { title: '窗口通道号', key: 'screen' },
            ],
            form: {
                name: '',
                ip: '',
                port: '',
                userName: '',
                password: ''
            },
            ruleForm: {
                name: [{ required: true, message: '请输入解码器名称', trigger: 'blur' }],
                ip: [{ required: true, validator: validateIp, message: '请输入解码器IP', trigger: 'blur' }],
                port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
                userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
                password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
            },
            loading: false,
            codeId: ''
        }
    },
    methods: {
        init(id = '') {
            this.visible = true;
            this.codeId = id;
            if(id){
                demodifierView(id)
                .then(res => {
                    let data = res.data;
                    this.form =  {
                        name: data.name,
                        ip: data.ip,
                        port: data.port,
                        userName: data.userName,
                        password: data.password
                    };
                    this.queryMonitor();
                })
            }
        },
        changeInput() {
            this.tableData = [];
            // let rule = false;
            // for(let key in this.form) {
            //     if(this.form[key] == '') {
            //         rule = true;
            //     }
            // }
            // if(!rule) {
            //     this.queryMonitor() 
            // }
        },
        // 监视器信息
        queryMonitor() {
            this.loading = true;
            let params = {
                videoWallId: this.wellId,
                ...this.form
            }
            demodifierScreen(params)
            .then(res => {
                this.tableData = res.data;
            })
            .finally(() => {
                this.loading = false;
            })
        },
        handleMonitor() {
            this.$refs.form.validate(valid => {
                if(valid) {
                    this.queryMonitor()
                }
            })
        },
        comfirmHandle() {
            this.$refs.form.validate(valid => {
                if(valid) {
                    if(this.tableData.length == 0) {
                        this.$Message.warning('请获取监视器信息');
                        return
                    }
                    let screenIds = ''
                    if(this.tableData.length > 0) {
                        screenIds = this.tableData.map(item => item.gbdecodeChanID).join(',')
                    }
                    let params = {
                        videoWallId: this.wellId,
                        screenCount: this.tableData.length,
                        screenIds: screenIds,
                        ...this.form
                    };
                    if(this.codeId){ //编辑
                        demodifierUpdate({...params, id: this.codeId})
                        .then(res => {
                            this.$Message.success('解码器编辑成功');
                            this.visible = false;
                            this.onCancel();
                            this.$emit('code')
                        })
                    }else{ //新增
                        demodifierAdd(params)
                        .then(res => {
                            this.$Message.success('添加解码器成功')
                            this.visible = false;
                            this.onCancel();
                            this.$emit('code')
                        })
                    }
                }
            })
        },
        onCancel() {
            this.$refs.form.resetFields();
            this.tableData = [];
        }
    }
}
</script>
<style lang="less" scoped>
.title{
    font-size: 14px;
    color: rgba(0,0,0,0.9);
    height: 40px;
    position: relative;
    line-height: 40px;
    padding-left: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 0;
    z-index: 1;
    background: #fff;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:before {
        content: '';
        position: absolute;
        width: 3px;
        height: 20px;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
        background: #2c86f8;
    }
}
.monitor-btn{
    text-align: center;
}
</style>
