export const nodeTemplates = [
  {
    name: 'group-node', //新图形的名称
    mainShape: {
      //主形状
      shape: 'circle',
      width: 60,
      height: 60,
      fillColor: '44,134,248',
      borderColor: '44,134,248',
      borderWidth: 1,
      borderRadius: 30,
      alpha: 1,
      selectedBorderWidth: 3,
      selectedBorderColor: '220,180,30'
    },
    elements: [
      //附加元素
      {
        // 单行文字
        type: 'text',
        attrs: {
          x: 0,
          y: 0,
          textAlign: 'center',
          textBaseline: 'middle',
          color: '#fff',
          font: '14px Arial',
          content: function (node) {
            if (node.properties.line == 1) {
              return node.label
            } else {
              return ''
            }
          }
        }
      },
      {
        // 多行文字1
        type: 'text',
        attrs: {
          x: 0,
          y: -10,
          textAlign: 'center',
          textBaseline: 'middle',
          color: '#fff',
          font: '12px Arial',
          content: function (node) {
            if (node.properties.line == 2) {
              return node.properties.name1
            } else {
              return ''
            }
          }
        }
      },
      {
        // 多行文字2
        type: 'text',
        attrs: {
          x: 0,
          y: 10,
          textAlign: 'center',
          textBaseline: 'middle',
          color: '#fff',
          font: '12px Arial',
          content: function (node) {
            if (node.properties.line == 2) {
              return node.properties.name2
            } else {
              return ''
            }
          }
        }
      },
      {
        type: 'defined',
        attrs: {
          drawFunction: function (ctx, node) {
            ctx.beginPath()
            ctx.arc(20, -22, 10, 0, Math.PI * 2, false)
            ctx.fillStyle = '#fff'
            ctx.shadowBlur = '30'
            ctx.shadowColor = '#93abceb3'
            ctx.fill()
            ctx.font = '12px Arial'
            ctx.fillStyle = '#2C86F8'
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            ctx.fillText(node.properties.count, 20, -21)
          }
        }
      },
      {
        type: 'defined',
        attrs: {
          drawFunction: function (ctx, node) {
            ctx.beginPath()
            ctx.arc(40, -12, 8, 0, Math.PI * 2, false)
            ctx.fillStyle = 'orange'
            ctx.fill()

            ctx.font = '20px Arial'
            ctx.fillStyle = '#fff'
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            ctx.fillText(node.properties.val, 40, -11)
          }
        },
        isInBound: function (node, x, y) {
          // 是否选中方法
          var pos = {
            x: node.cx + 40,
            y: node.cy - 12,
            radius: 8
          }
          if ((x - pos.x) * (x - pos.x) + (y - pos.y) * (y - pos.y) < pos.radius * pos.radius) {
            node.barNodeClick = true
            return true
          } else {
            node.barNodeClick = false
            return false
          }
        }
      }
    ]
  },
  // 默认图形样式
  {
    name: 'default-node', //新图形的名称
    mainShape: {
      //主形状
      shape: 'circle',
      // width: 60,
      // height: 60,
      size: 56,
      fillColor: '44,134,248',
      borderColor: '44,134,248',
      borderWidth: 1,
      borderRadius: 30,
      alpha: 1,
      selectedBorderWidth: 3,
      selectedBorderColor: '220,180,30',
      
    },
    elements: [
      //附加元素
      {
        type: 'rect',
        attrs: {
          width: 150,
          height: 26,
          x: -75,
          y: 36,
          fillColor: '#f9f9f9',
          borderWidth: 1,
          borderColor: '#D3D7DE'
        }
      },
      {
        type: 'text',
        attrs: {
          x: 0,
          y: 50,
          class: 'fontEllips',
          textAlign: 'center',
          textBaseline: 'middle',
          color: '#333',
          font: '14px Arial',
          content: function (node) {
            if (node.label.length > 12) {
              return node.label.substring(0, 12) + '...'
            }
            return node.label
          }
        }
      }
    ]
  }
]

export const nodeTemplates2 = [
  {
    name: 'group-node', //新图形的名称
    mainShape: {
      //主形状
      shape: 'rect',
      
      width: 120,
      height: 30,
      fillColor: '44,134,248',
      borderColor: '44,134,248',
      // fillColor:'255,255,255',
      // borderColor:'220,160,30',
      borderWidth: 1,
      borderRadius: 3,
      alpha: 1,
      selectedBorderWidth: 1,
      selectedBorderColor: '220,180,30',
      
    },
    elements: [
      //附加元素
      {
        type: 'text',
        attrs: {
          x: 15,
          y: 0,
          textAlign: 'center',
          textBaseline: 'middle',
          color: '#fff',
          // color:'orange',
          font: '12px Arial',
          content: function (node) {
            return node.label
          }
        }
      },
      {
        type: 'defined',
        attrs: {
          drawFunction: function (ctx, node) {
            ctx.beginPath()
            ctx.arc(-45, 0, 10, 0, Math.PI * 2, false)
            ctx.fillStyle = 'orange'
            ctx.fill()

            ctx.font = '12px Arial'
            ctx.fillStyle = '#fff'
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            ctx.fillText(node.properties.count, -45, 1)
          }
        }
      },
      {
        type: 'defined',
        attrs: {
          drawFunction: function (ctx, node) {
            ctx.beginPath()
            ctx.arc(70, -2, 8, 0, Math.PI * 2, false)
            ctx.fillStyle = 'orange'
            ctx.fill()

            ctx.font = '20px Arial'
            ctx.fillStyle = '#fff'
            ctx.textAlign = 'center'
            ctx.textBaseline = 'middle'
            ctx.fillText(node.properties.val, 70, -1)
          }
        },
        isInBound: function (node, x, y) {
          // 是否选中方法
          var pos = {
            x: node.cx + 70,
            y: node.cy - 2,
            radius: 8
          }
          if ((x - pos.x) * (x - pos.x) + (y - pos.y) * (y - pos.y) < pos.radius * pos.radius) {
            node.barNodeClick = true
            return true
          } else {
            node.barNodeClick = false
            return false
          }
        }
      }
    ]
  }
]
