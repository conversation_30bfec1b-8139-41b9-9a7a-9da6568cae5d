<!--
 * @Author: du<PERSON>en
 * @Date: 2024-08-30 09:19:48
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-22 10:54:42
 * @Description: 
-->
<template>
  <div class="search card-border-color">
    <Form :inline="true" :class="visible ? 'advanced-search-show' : ''">
      <div class="general-search">
        <div class="input-content">
          <div class="upload-input-list">
            <uiUploadImg
              :algorithmType="2"
              ref="uploadImgRef"
              @change="imgUrlChange"
              size="small"
            />
          </div>
          <div class="other-search">
            <div class="other-search-top card-border-color">
              <FormItem label="相似度:" class="slider-form-item">
                <div class="slider-content">
                  <i
                    class="iconfont icon-jian add-subtract"
                    @click="addAndSubtract"
                  ></i>
                  <Slider v-model="queryParam.similarity"></Slider>
                  <i
                    class="iconfont icon-jia add-subtract"
                    @click="addAndSubtract(true)"
                  ></i>
                  <span>{{ queryParam.similarity }}%</span>
                </div>
              </FormItem>
              <!-- 算法2个以上（含）才可选择 -->
              <FormItem
                label="算法选择:"
                prop="algorithm"
                v-if="algorithmTypeList.length > 1"
              >
                <CheckboxGroup
                  v-if="queryParam.features.length > 0"
                  v-model="queryParam.algorithmSelect"
                  @on-change="handleChangeAlgor"
                >
                  <Checkbox
                    :label="item.dataKey"
                    v-for="(item, index) in algorithmTypeList"
                    :key="index"
                  >
                    <span :class="[index === 0 ? 'gerling' : 'hk']">{{
                      item.dataValue
                    }}</span>
                  </Checkbox>
                </CheckboxGroup>
                <RadioGroup v-else v-model="queryParam.algorithmVendorType">
                  <Radio
                    :label="item.dataKey"
                    v-for="(item, index) in algorithmTypeList"
                    :key="index"
                  >
                    <span :class="[index === 0 ? 'gerling' : 'hk']">{{
                      item.dataValue
                    }}</span>
                  </Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="车牌号码:">
                <Input
                  v-model="queryParam.plateNo"
                  placeholder="请输入"
                ></Input>
              </FormItem>
            </div>
            <div class="other-search-bottom">
              <div class="flex">
                <FormItem label="设备资源:">
                  <div class="select-tag-button" @click="selectDevice()">
                    选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                  </div>
                </FormItem>
                <FormItem label="抓拍时段:">
                  <ui-quick-date
                    ref="quickDateRef"
                    v-model="dateType"
                    @change="changeTime"
                  ></ui-quick-date>
                </FormItem>
              </div>
              <div class="btn-group">
                <span
                  class="advanced-search-text primary"
                  @click.stop="visible = !visible"
                >
                  更多条件 <i class="iconfont icon-jiantou"></i>
                </span>
                <Button type="primary" @click="searchHandle">查询</Button>
                <Button @click="resetHandle">重置</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--更多搜索条件-->
      <div class="advanced-search" @click.stop>
        <section class="search-container" v-show="visible">
          <div class="search-item-container" id="searchItemContainer">
            <div class="classify-content">
              <span class="classify-name">常用</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="数据来源:">
                    <dataSource sectionName="vehicleContent" />
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="角度:" class="percent-60">
                    <selectTag
                      ref="pointOfView"
                      :list="pointViewList"
                      vModel="pointOfView"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">车牌</span>
              <div class="items">
                <div
                  class="advanced-search-item card-border-color"
                  id="vehicleCP"
                >
                  <FormItem label="车牌类型:">
                    <selectTag
                      ref="plateClass"
                      :moreBtn="true"
                      :list="plateClassList"
                      vModel="plateClass"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="车牌颜色:">
                    <ui-tag-select
                      ref="plateColors"
                      type="checkbox"
                      @input="
                        (e) => {
                          input(e, 'plateColors');
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in licensePlateColorList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey"
                      >
                        <div
                          :style="{
                            borderColor:
                              licensePlateColorArray[item.dataKey].borderColor,
                          }"
                          class="plain-tag"
                        >
                          <div
                            :style="licensePlateColorArray[item.dataKey].style"
                            :class="licensePlateColorArray[item.dataKey].class"
                          >
                            {{ item.dataValue }}
                          </div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="遮挡:">
                    <selectTag
                      ref="cover"
                      :list="plateOcclusionList"
                      vModel="cover"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">车身</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="车身类型:">
                    <selectTag
                      :isChild="true"
                      :carColor="true"
                      :list="vehicleClassTypeLists"
                      ref="bodyType"
                      vModel="bodyType"
                      @selectItem="selectItem"
                    />
                    <div class="carVal"></div>
                  </FormItem>
                </div>
                <div
                  class="advanced-search-item card-border-color"
                  id="vehicleCSYS"
                >
                  <FormItem label="车身颜色:">
                    <ui-tag-select
                      ref="bodyColors"
                      type="checkbox"
                      @input="
                        (e) => {
                          input(e, 'bodyColors');
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in plateColorIpbdList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey"
                      >
                        <div
                          v-if="vehicleBodyColorArray[item.dataKey]"
                          :style="{
                            borderColor:
                              vehicleBodyColorArray[item.dataKey].borderColor,
                          }"
                          class="plain-tag"
                        >
                          <div
                            :style="vehicleBodyColorArray[item.dataKey].style"
                          >
                            {{ item.dataValue }}
                          </div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div
                  class="advanced-search-item card-border-color"
                  id="vehiclePP"
                >
                  <FormItem label="车辆品牌:">
                    <div class="select-tag-button" @click="selectBrandHandle">
                      选择车辆品牌/已选（{{ queryParam.plateBrands.length }}）
                    </div>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="特殊车辆:">
                    <selectTag
                      :list="specialVehicleList"
                      ref="specialPlate"
                      vModel="specialPlate"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
                <div
                  class="advanced-search-item card-border-color"
                  id="markerTop"
                >
                  <FormItem label="车顶物件:">
                    <selectTag
                      :list="roofItemsList"
                      ref="carRoof"
                      vModel="carRoof"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                  <FormItem label="年检标:" class="percent-60">
                    <selectTag
                      :list="annualInspectionNumList"
                      ref="inspectAnnually"
                      vModel="inspectAnnually"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color" id="marker">
                  <FormItem label="标志物:">
                    <selectTag
                      :list="markerTypeList"
                      ref="markerType"
                      vModel="markerType"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">行为</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="副驾有人:">
                    <selectTag
                      :list="copilotList"
                      ref="ifCoDriverPeople"
                      vModel="ifCoDriverPeople"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                  <FormItem label="面部遮挡:" class="percent-60">
                    <selectTag
                      :list="facialOcclusionList"
                      ref="facialOcclusion"
                      vModel="facialOcclusion"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
          </div>
          <div class="img-search" @click="clickTagVehicleHandle">
            <div class="vehicle-front">
              <span
                v-for="(item, index) in anchorFrontList"
                :data-id="item.dataId"
                :class="item.class"
                :key="index"
                >{{ item.name }}</span
              >
              <img
                src="@/assets/img/wisdom-cloud-search/vehicle-back.png"
                alt=""
              />
            </div>
            <div class="vehicle-back">
              <span
                v-for="(item, index) in anchorAfterList"
                :data-id="item.dataId"
                :class="item.class"
                :key="index"
                >{{ item.name }}</span
              >
              <img
                src="@/assets/img/wisdom-cloud-search/vehicle-front.png"
                alt=""
              />
            </div>
          </div>
        </section>
      </div>
      <!-- 选择品牌 -->
      <BrandModal ref="brandModal" @on-change="selectBrand" />
      <!-- 选择设备 -->
      <select-device
        ref="selectDevice"
        showOrganization
        :checkedLabels="checkedLabels"
        @selectData="selectData"
      />
    </Form>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import uiUploadImg from "@/components/ui-upload-image/index";
import BrandModal from "@/components/ui-brand-modal.vue";
import selectTag from "../../components/select-tag.vue";
import selectMultipleTag from "../../components/select-multiple-tag";
import { licensePlateColorArray, vehicleBodyColorArray } from "@/libs/system";
import {
  queryDeviceList,
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import dayjs from "dayjs";
import {
  captureTimePeriod,
  angleList,
  shelterList,
  vehiclePlateTypes,
  vehiclePlateColors,
  vehicleBodyTypes,
  vehicleBodyColors,
  colors,
  bodyColors,
  carTopList,
  coDriverList,
  faceList,
  sunVisorList,
  yearCheckList,
  markerList,
  anchorFrontList,
  anchorAfterList,
  specialVehicles,
  vehicleClassTypeList,
} from "./search-vehicle.js";
import dataSource from "../../components/data-source.vue";

export default {
  components: {
    BrandModal,
    uiUploadImg,
    selectTag,
    dataSource,
    selectMultipleTag,
  },
  props: {
    // 档案资料抓拍弹框
    searchFields: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      visible: false, // 更多条件显示隐藏
      dateType: 1, // 抓拍时段类型
      curSearchItemId: "",
      algorithmType: 2,
      queryParam: {
        plateNo: "", // 车牌号
        selectDeviceList: [], // 选中的设备全量 - 手动记录日志需要该信息
        algorithmVendorType: "", // 单选 - 选中的算法
        algorithmSelect: [], // 多选 - 选中的算法，当以图搜图时，算法为多选
        plateBrands: [], // 选中的车辆品牌
        startDate: dayjs().format("YYYY-MM-DD 00:00:00"), // 抓拍时段 - 开始时间
        endDate: dayjs().format("YYYY-MM-DD 23:59:59"), // 抓拍时段 - 结束时间
        similarity: 75, // 相似度
        features: [], // 图片信息
        imageBases: [], // 图片信息
        pointOfView: "", // 角度
        plateClass: "", // 车牌类型
        plateColors: [], // 车牌颜色
        cover: "", // 是否遮挡
        bodyType: "", // 车身类型
        bodyColors: [], // 车身颜色
        specialPlate: "", // 特殊车辆
        carRoof: "", // 车顶物件
        inspectAnnually: "", // 年检标
        markerType: "", // 标志物
        ifCoDriverPeople: "", // 副驾是否有人
        facialOcclusion: "", // 面部遮挡
        devices: [], // 选中的设备id
      },
      captureTimePeriod,
      angleList,
      shelterList,
      vehiclePlateTypes,
      vehiclePlateColors,
      vehicleBodyTypes,
      vehicleBodyColors,
      carTopList,
      yearCheckList,
      markerList,
      colors,
      coDriverList,
      faceList,
      sunVisorList,
      bodyColors,
      specialVehicles,
      licensePlateColorArray, // 车牌颜色
      vehicleBodyColorArray, // 车身颜色
      anchorFrontList,
      anchorAfterList,
      checkedLabels: [], // 已选择的标签
      vehicleClassTypeLists: vehicleClassTypeList, // 车身类型
    };
  },
  async activated() {
    //#region 系统配置参数 - 页面刷新时
    if (!this.globalObj.searchForPicturesDefaultSimilarity) {
      await this.getDictData();
    }
    this.queryParam.similarity = Number(
      this.globalObj.searchForPicturesDefaultSimilarity
    );
    //#endregion

    //#region 路由参数处理
    let query = this.$route.query;
    /**
     * 走到了mounted生命周期中，并满足该条件，说明是点击左侧菜单触发的，此时不需要处理路由携带的参数
     * 否则，则表示直接从别的页面跳到该页面，并直接定位到当前组件
     */
    if (query.sectionName !== "vehicleContent") {
      let { list, timeSlot, startDate, endDate, searchSelect } =
        this.classifySearchData;
      if (searchSelect == 1) {
        this.queryParam.selectDeviceList = list;
        this.dateType = timeSlot;
        this.$nextTick(() => {
          this.$refs.quickDateRef.handleInit(timeSlot, startDate, endDate);
        });
      }
      this.$nextTick(() => {
        // 需要等时间更新后发起查询
        this.$emit("search");
      });
    }

    if (query.deviceId) {
      this.queryParam.devices = [query.deviceId];
    }
    // query是从别的页面跳过来
    if (query.archiveNo) {
      this.queryParam.plateNo = JSON.parse(query.archiveNo);
    }
    // 车牌颜色 query是从别的页面跳过来
    if (query.plateColor) {
      this.$refs.plateColors.handleChangeTag(query.plateColor);
    }
    // 选中的设备
    if (query.deviceList) {
      let deviceList = JSON.parse(query.deviceList);
      let list = deviceList.map((item) => {
        return { ...item, deviceId: item.deviceGbId, select: true };
      });
      this.selectData(list);
    }
    // 全景智搜进入
    if (query.keyWords) {
      this.queryParam.searchValue = query.keyWords;
    }
    if (query.dateType) {
      this.dateType = Number(query.dateType);
      if ([2, 3].includes(this.dateType)) {
        this.$nextTick(() => {
          this.changeTime(this.$refs.quickDateRef.getDate());
        });
      }
      // 自定义时间，当为自定义时间时，query必定携带dateRange起止时间
      if (this.dateType === 4) {
        let { startDate, endDate } = query;
        this.changeTime({ startDate, endDate });
        this.$nextTick(() => {
          this.$refs.quickDateRef.setDefaultDate([startDate, endDate]);
        });
      }
    }
    if (query.deviceInfo) {
      let deviceInfo = JSON.parse(query.deviceInfo);
      deviceInfo.deviceId = deviceInfo.deviceGbId || deviceInfo.deviceId;
      deviceInfo.deviceGbId = deviceInfo.deviceGbId || deviceInfo.deviceId;
      this.selectData([{ ...deviceInfo, select: true }]);
    }
    if (query.urlList) {
      // 图片结构化跳转
      let list = JSON.parse(query.urlList);
      this.urlImgList([list, ""]);
    }
    // TODO panorama 目前没看到哪里有传入，后续再验证，没有就去掉
    if (query.panorama) {
      let list = this.picData.urlList;
      this.urlImgList(list);
    }
    if (query.imgUrl) {
      let selectSquare = query.selectSquare
        ? { imageUrl: query.imgUrl, ...JSON.parse(query.selectSquare) }
        : null;
      if (!selectSquare) {
        let fileData = new FormData();
        fileData.append("algorithmType", 2);
        fileData.append("fileUrl", query.imgUrl);
        const res = await picturePick(fileData);
        selectSquare = res?.data?.[0];
      }
      if (selectSquare) {
        const response = await this.getBase64ByImageCoordinate(selectSquare);
        let urlList = {
          fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
          feature: response.data.feature,
          imageBase: response.data.imageBase,
        };
        this.urlImgList([urlList, ""]);
      } else {
        this.$Message.warning("未提取到特征，暂无查询结果");
      }
    }
    this.$nextTick(() => {
      // 需要等时间更新后发起查询
      this.$emit("search");
    });
  },
  async mounted() {
    window.addEventListener("click", this.hideMoreQuery);
    // 2024-07-29 新增一种情况，在详情打开弹框，不用route.query来判断了
    if (Object.keys(this.searchFields).length) {
      let fields = this.searchFields;
      if (fields.archiveNo) {
        this.queryParam.plateNo = JSON.parse(fields.archiveNo);
      }
      if (fields.plateColor) {
        this.$refs.plateColors.handleChangeTag(fields.plateColor);
      }
      if (fields.deviceInfo) {
        let deviceInfo = JSON.parse(fields.deviceInfo);
        deviceInfo.deviceId = deviceInfo.deviceGbId || deviceInfo.deviceId;
        deviceInfo.deviceGbId = deviceInfo.deviceGbId || deviceInfo.deviceId;
        this.selectData([{ ...deviceInfo, select: true }]);
      }
      // 档案跳过来的都使用90天的时间
      this.dateType = 4;
      let date = [
        dayjs().subtract(89, "day").format("YYYY-MM-DD 00:00:00"),
        dayjs().format("YYYY-MM-DD 23:59:59"),
      ];
      this.changeTime({ startDate: date[0], endDate: date[1] });
      this.$nextTick(() => {
        this.$refs.quickDateRef.setDefaultDate(date);
      });
      this.$emit("search");
      return;
    }
  },
  beforeDestroy() {
    window.removeEventListener("click", this.hideMoreQuery);
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      vehicleColorList: "dictionary/getVehicleColorList", // 车牌颜色
      sunVisorStatusList: "dictionary/getSunVisorStatusList", // 遮阳板
      driverFlagList: "dictionary/getDriverFlagList", // 副驾有人
      facialOcclusionList: "dictionary/getFacialOcclusionList", // 面部遮挡
      classifySearchData: "common/getClassifySearchData", //查询数据
    }),
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("common", ["setClassifySearchData"]),
    /**
     * @description: 隐藏更多条件框
     */
    hideMoreQuery() {
      this.visible = false;
    },

    /**
     * @description: 修改抓拍时段起始时间
     * @param {object} val 起始时间
     */
    changeTime(val) {
      this.queryParam.startDate = val.startDate;
      this.queryParam.endDate = val.endDate;
      let params = {
        list: this.queryParam.selectDeviceList,
        timeSlot: val.timeSlot || 1,
        startDate: val.startDate,
        endDate: val.endDate,
        searchSelect: 1,
      };
      this.setClassifySearchData(params);
    },

    /**
     * @description: 相似度调整
     * @param {boolean} flag 调整方向: true-加，false-减
     */
    addAndSubtract(flag = false) {
      if (flag) {
        this.queryParam.similarity++;
      } else {
        this.queryParam.similarity--;
      }
    },

    // 选择品牌
    selectBrandHandle() {
      this.$refs.brandModal.show();
    },

    /**
     * @description: 打开设备弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList);
    },

    /**
     * @description: 选中设备
     * @param {array} list 选中的设备
     */
    selectData(list) {
      this.queryParam.selectDeviceList = list;
      this.queryParam.devices = list.map((item) => item.deviceGbId);
    },

    // 点击右边车指示高亮左侧搜索项  因这里数据无特别规律，so这里为原生js操作dom
    clickTagVehicleHandle(e) {
      const target = e.target;
      if (target.nodeName === "SPAN") {
        let PChildren = target.parentNode.parentNode.childNodes;
        PChildren.forEach((pChild) => {
          pChild.childNodes.forEach((child) => {
            child.classList.remove("cur");
          });
        });
        target.classList.add("cur");
        const dataId = target.getAttribute("data-id");
        const leftItem = document.getElementById(dataId);
        if (!leftItem) return false;
        const leftPChildren =
          leftItem.parentNode.parentNode.parentNode.childNodes;
        leftPChildren.forEach((p) => {
          p.childNodes.forEach((pp) => {
            if (pp.nodeName === "DIV") {
              pp.childNodes.forEach((ppp) => {
                ppp.classList.remove("cur");
              });
            }
          });
        });
        leftItem.classList.add("cur");
        document.getElementById("searchItemContainer").scrollTop =
          leftItem.offsetTop;
        this.curSearchItemId = dataId;
      }
    },

    /**
     * @description: 算法选择，当上传了图片搜索后，算法变为多选框
     * @param {array} value 选中的算法值
     */
    handleChangeAlgor(value) {
      if (value.length == 0 && this.queryParam.features.length > 0) {
        this.queryParam.algorithmVendorType = this.algorithmTypeSelect[0];
        this.queryParam.algorithmSelect = [];
      } else {
        this.queryParam.algorithmVendorType = value[0];
      }
    },

    /**
     * @description: tag选中
     * @param {string} key 属性
     * @param {object} item 选中的tag
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.dataKey;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 车辆品牌已选择，返回数据
     * @param: 选中的品牌
     */
    selectBrand(list) {
      this.queryParam.plateBrands = list;
    },

    /**
     * @description: 切换tag
     * @param {string} e 当前选中的值
     * @param {string} key 当前类别
     */
    input(e, key) {
      this.queryParam[key] = e;
    },

    /**
     * @description: 搜索，，手动触发，需要清空来自全景智搜的keyWords，如果有的话，重置视为主动触发查询
     */
    searchHandle() {
      this.hideMoreQuery();
      delete this.queryParam.searchValue;
      // 以图搜索下算法为多选，至少选择一个
      if (
        !this.queryParam.algorithmSelect.length &&
        this.queryParam.features.length
      ) {
        this.$Message.warning("至少选择一个算法！");
        return;
      }
      if (this.queryParam.algorithmSelect.length === 2) {
        // 调整算法选择的顺序，避免多算法展示错误
        this.queryParam.algorithmSelect = this.algorithmTypeSelect;
      }
      this.$emit("search");
    },

    /**
     * @description: 重置
     */
    resetHandle() {
      this.$refs.uploadImgRef.setDefaultUploadImage([""]);
      // 清空tag组件选中状态
      this.$refs.pointOfView.clearChecked();
      this.$refs.plateClass.clearChecked();
      this.$refs.plateColors.clearChecked();
      this.$refs.cover.clearChecked();
      this.$refs.bodyType.clearChecked();
      this.$refs.bodyColors.clearChecked();
      this.$refs.specialPlate.clearChecked();
      this.$refs.carRoof.clearChecked();
      this.$refs.inspectAnnually.clearChecked();
      this.$refs.ifCoDriverPeople.clearChecked();
      this.$refs.facialOcclusion.clearChecked();
      this.$refs.brandModal.clearCheckAll();
      this.$refs.markerType.clearChecked();

      this.queryParam.plateNo = "";
      this.queryParam.selectDeviceList = [];
      this.queryParam.features = [];
      this.queryParam.imageBases = [];
      this.queryParam.algorithmVendorType = "";
      this.queryParam.algorithmSelect = [];
      this.queryParam.plateBrands = [];
      this.queryParam.pointOfView = "";
      this.queryParam.plateClass = "";
      this.queryParam.plateColors = [];
      this.queryParam.cover = "";
      this.queryParam.bodyType = "";
      this.queryParam.bodyColors = [];
      this.queryParam.specialPlate = "";
      this.queryParam.carRoof = "";
      this.queryParam.inspectAnnually = "";
      this.queryParam.markerType = "";
      this.queryParam.ifCoDriverPeople = "";
      this.queryParam.facialOcclusion = "";
      this.queryParam.devices = [];
      this.queryParam.similarity = Number(
        this.globalObj.searchForPicturesDefaultSimilarity
      );
      //#region 重置时间
      // 如果是自定义被重置，则需要清空已选择的时间
      if (this.dateType == 4) {
        this.$refs.quickDateRef.setDefaultDate();
      }
      this.dateType = 1;
      this.$nextTick(() => {
        this.changeTime(this.$refs.quickDateRef.getDate());
        this.searchHandle();
      });
      //#endregion
    },

    /**
     * @description: 设置默认的上传的图片
     * @param {array} list 图片列表
     * @param {number} index 标识，1 - 直接跳到分类检索，从$router.query拿图片，2 - 在当前分类检索下进行搜索目标添加，需要与已上传的图片合并
     */
    urlImgList(list, index = 1) {
      let newList = [...list];
      if (index == 2) {
        let alreadyUploadImages = this.$refs.uploadImgRef.getUploadedImages();
        if (alreadyUploadImages.length < 5) {
          // 当已上传图片不足5张时
          newList = [...alreadyUploadImages, ...newList]
            .filter((v) => !!v)
            .concat([""]);
        } else if (
          alreadyUploadImages.length === 5 &&
          !alreadyUploadImages[alreadyUploadImages.length - 1]
        ) {
          // 当为5但是最后一个为上传的按钮操作时
          newList = [...alreadyUploadImages, ...newList].filter((v) => !!v);
        } else {
          this.$Message.warning("数量超出限制");
          return false; // 数量超出限制后不做处理
        }
      }
      this.$refs.uploadImgRef.setDefaultUploadImage(newList);
      this.imgUrlChange(newList);
    },

    /**
     * @description: 图片上传结果返回
     * @param {array} list 图片列表
     */
    imgUrlChange(list) {
      // 以图搜图字段
      let features = [];
      let imageBases = [];
      list.forEach((item) => {
        if (item) {
          features.push(item.feature);
          imageBases.push(item.imageBase);
        }
      });
      if (features.length) {
        this.queryParam.algorithmSelect = this.algorithmTypeSelect;
      } else {
        // this.queryParam.algorithmVendorType = this.algorithmTypeSelect?.[0];
        this.queryParam.algorithmSelect = [];
      }
      this.queryParam.algorithmVendorType = this.algorithmTypeSelect?.[0];
      this.queryParam.features = features;
      this.queryParam.imageBases = imageBases;
    },

    /**
     * @description: 获取查询参数，供父组件使用
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },

    /**
     * @description: 根据图片坐标截取图片base64
     * @param {object} data 图片信息
     * @return {promise} 获取图片base64
     */
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "vehicle" };
      return getBase64ByImageCoordinateAPI(params);
    },
  },
};
</script>
<style lang="less" scoped>
@import "./search-vehicle";
@import "style/index.less";
</style>
