<template>
  <div class="auto-fill">
    <div class="header-box">
      <Button type="default" class="button-export" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu mr-xs f-14"></i>
        <span class="ml-xs">导出</span>
      </Button>
    </div>
    <!-- @onSortChange="onSortChange" -->
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template v-for="(item, index) in tableSlotList" #[item.slot]="{ row }">
        <span
          v-if="row.detail[item.key] || row.detail[item.key] === 0"
          @click="viewDeviceDetail(row, item)"
          :key="index"
          :class="{
            'unqualified-color': !['deviceNum'].includes(item.key),
            'underline-text': row.detail[item.key] !== 0,
          }"
        >
          {{ row.detail[item.key] }}
        </span>
        <span v-else :key="index">--</span>
      </template>
    </ui-table>
    <!-- 设备详情 -->
    <DetailsModal v-model="showDetailsModal" :row-data="currentRowData"></DetailsModal>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import dataAnalysis from '@/config/api/dataAnalysis.js';
import { getAbnormalTableColumns } from '../utils/tableConfig.js';
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DetailsModal: require('../components/details-modal.vue').default,
  },
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      exportLoading: false,
      tableData: [],
      tableColumns: [],
      tableSlotList: [],
      loading: false,
      sortData: {
        sortField: null, // 排序字段
        sort: null, // 排序方式: ASC("升序") | DESC("降序")
      },
      // 设备详情
      showDetailsModal: false,
      currentRowData: {},
    };
  },
  created() {
    let { code } = this.$route.query;
    this.tableColumns = getAbnormalTableColumns()[code];
    this.tableSlotList = this.tableColumns.filter((item) => item.slot);
    this.startWatch(
      '$route',
      () => {
        this.getTableList();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    getApiParams() {
      let { batchId } = this.$route.query;
      let data = {
        batchId,
        // pageNumber: 1,
        // pageSize: 1000,
        // ...this.sortData,
      };
      return data;
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let data = this.getApiParams();
        const res = await this.$http.post(dataAnalysis.exportStatInfoList, data);
        await this.$util.common.transformBlob(res.data.data);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
    onSortChange(column) {
      if (column.order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: column.key,
          sort: column.order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    async getTableList() {
      try {
        this.loading = true;
        let data = this.getApiParams();
        let res = await this.$http.post(dataAnalysis.getStatInfoList, data);
        this.tableData = res.data.data || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    viewDeviceDetail(row, columnItem) {
      if (row.detail[columnItem.key] === 0) return;
      this.showDetailsModal = true;
      // analyseResult： 对应详情弹框 中 【分析结果】过滤条件的值   100-抓拍过少  101-抓拍突降    102-抓拍量显著低于附近设备
      this.currentRowData = {
        ...row,
        analyseResult:
          columnItem.key === 'thanMeNum'
            ? 100
            : columnItem.key === 'lessNbNum'
              ? 102
              : columnItem.key === 'suddenNum'
                ? 101
                : '',
      };
    },
  },
};
</script>

<style lang="less" scoped>
.header-box {
  display: flex;
  justify-content: flex-end;
  .button-export {
    margin-bottom: 16px;
  }
}
.underline-text {
  text-decoration: underline;
  cursor: pointer;
}
.unqualified-color {
  color: var(--color-failed);
}
</style>
