<!--
    * @FileDescription: 隐匿车
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-13 17:36:25
 -->
<template>
  <div class="hidden-vehicle-analysis container">
    <!-- 地图 -->
    <mapCustom
      ref="mapBase"
      mapType="hidden-vehicle-analysis"
      :allCameraList="allCameraList"
      @selectDrawPoints="selectDrawPoints"
      @chooseMapItem="chooseMapItem"
      :trackPoints="countList"
      :currentClickIndex="countIndex"
      sectionName="vehicle"
    />
    <bottom-tool
      v-show="mapToolVisible"
      @cancelDraw="cancelDraw"
      @selectDraw="selectDraw"
      @clearDraw="clearDraw"
      @closeMapTool="closeBottomTool"
      :drawComplete="drawComplete"
    ></bottom-tool>
    <!-- 左面信息展示框 -->
    <leftBox
      ref="searchBar"
      @search="handleSearch"
      @selectDraw="selectDraw"
      @handleSeleArea="handleSeleArea"
      v-show="searchBox"
      :deviceList="deviceList"
      :taskParams="taskParams"
      @selectDevice="selectDevice"
    ></leftBox>
    <detailsBox
      ref="detailBox"
      @backSearch="handleBackSearch"
      @setChartList="setChartList"
      @showCountDetail="showCountDetail"
      @setPlateNo="setPlateNo"
      :dayNum="searchParams.dayNum"
      v-show="!searchBox"
    ></detailsBox>
    <charts v-if="isShowCharts" :statisticsList="statisticsList"></charts>
    <!-- 某条记录的详细频次 -->
    <div
      class="count_detail"
      id="count_detail"
      v-on:mouseover="drag('count_detail', 'hiddenVehicle', true)"
      v-show="count_detail"
    >
      <div class="count_detail_title">
        <div class="count_detail_title_header">
          <p class="count_detail_title_name">{{ plateNo }}</p>
          <i
            class="iconfont icon-close count_detail_close"
            @click="closeRecordPanel"
          />
        </div>
        <div class="count_detail_title_header">
          <span
            :class="{
              leftSelectedTabBtn: isShownBeforeList,
              leftNonTabBtn: !isShownBeforeList,
            }"
            @click="showBeforeList"
            style="margin-top: 6px"
            >案发前<span>{{ searchParams.dayNum }}</span
            >天过车</span
          >
          <span
            :class="{
              rightNonTabBtn: isShownBeforeList,
              rightSelectedTabBtn: !isShownBeforeList,
            }"
            @click="showAfterList"
            style="margin-top: 6px"
            >案发后<span>{{ searchParams.dayNum }}</span
            >天过车</span
          >
        </div>
        <div v-scroll ref="countDetail" class="count_detail_content">
          <div
            class="count_detail_item"
            v-for="(item, index) in countList"
            :key="index"
            @click="detailList(item, index)"
            :class="{ active_hover_class: index === countIndex }"
          >
            <div class="vertical_line"></div>
            <div style="">
              <span
                class="markIcon"
                :class="{ active_markIcon: index === countIndex }"
              >
                <span
                  class="oneDigitsStyle"
                  :class="{
                    twoDigitsStyle: parseInt(index + 1) >= 10,
                    threeDigitsStyle: parseInt(index + 1) >= 100,
                    fourDigitsStyle: parseInt(index + 1) >= 1000,
                    moreDigitsStyle: parseInt(index + 1) >= 10000,
                  }"
                  >{{ index + 1 >= 10000 ? "9999+" : index + 1 }}</span
                >
              </span>
            </div>
            <div class="count_detail_content_box">
              <div class="face_time face_time_title" :title="item.deviceName">
                {{ item.deviceName }}
              </div>
              <div class="face_time">
                <i class="iconfont icon-time"></i>{{ item.absTime }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <vehicle-info-window v-show="showVehicleWin" :data="vehicleDetail" ref="infowin" @detail="showVehicleDetail"
            @infowindow-close="closeWin" @infowindow-prev="changeMarker('prev')"
            @infowindow-next="changeMarker('next')" /> -->
  </div>
</template>

<script>
import leftBox from "./components/leftBox.vue";
import detailsBox from "./components/detailsBox.vue";
import charts from "./components/charts.vue";
import BottomTool from "@/components/map/map-tool.vue";
import mapCustom from "@/views/model-market/components/map/index.vue";
import { mapMutations } from "vuex";
import { deviceRegion } from "@/api/modelMarket";

export default {
  name: "hidden-vehicle-analysis",
  components: {
    mapCustom,
    leftBox,
    detailsBox,
    charts,
    BottomTool,
  },
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      searchBox: true,
      allCameraList: [],
      drawStyle: {
        color: "#2C86F8", //颜色
        fillColor: "#2C86F8", //填充颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
        lineStyle: "#2C86F8",
        strokeColor: "#2C86F8",
        showRadius: true,
      },
      deviceList: [],
      statisticsList: [],
      isShowCharts: false,
      count_detail: false,
      searchParams: {},
      countList: [],
      countIndex: -1,
      isShownBeforeList: true,
      showVehicleWin: false,
      plateNo: "",
      trackPoints: [],
      mapToolVisible: false,
      drawComplete: false,
    };
  },
  watch: {},
  computed: {},
  activated() {},
  deactivated() {},
  async created() {
    // 推荐中心查看
    console.log("taskParams: ", this.taskParams);
    if (!Toolkits.isEmptyObject(this.taskParams)) {
      this.deviceList = this.taskParams.params.selectDeviceList || [];
    } else {
      this.setLayoutNoPadding(true);
    }
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  mounted() {
    this.getMapLayerByTypeSite();
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    handleSearch(params) {
      console.log(params, "params");
      this.searchParams = params;
      this.searchBox = false;
      this.$nextTick(() => {
        this.$refs.detailBox.handleList(params);
        this.$refs.mapBase.clearDraw();
      });
    },
    handleBackSearch() {
      this.searchBox = true;
      this.statisticsList = [];
      this.isShowCharts = false;
      this.countList = [];
      this.count_detail = false;
      this.plateNo = "";
      this.closeWin();
      this.closeRecordPanel();
    },
    // 撒点数据
    async getMapLayerByTypeSite() {
      let roleParam = {
        dataTypeList: [2],
      };
      let { data } = await deviceRegion(roleParam);
      data.entities.forEach((item) => {
        item.mapType = item.deviceType;
      });
      this.allCameraList = data.entities;
    },
    selectDraw(type) {
      this.$refs.mapBase.selectDraw(type, false, -1, this.drawStyle, false);
    },
    clearDraw() {},
    closeBottomTool() {
      this.mapToolVisible = false;
    },
    cancelDraw() {},
    handleSeleArea() {
      this.mapToolVisible = true;
    },
    selectDrawPoints(points) {
      this.deviceList = points.map(({ deviceId, deviceGbId, deviceName }) => ({
        deviceId,
        deviceGbId,
        deviceName,
      }));
    },
    selectDevice(devices) {
      this.deviceList = devices;
    },
    setChartList(list) {
      this.statisticsList = list;
      this.isShowCharts = true;
    },
    showCountDetail(list) {
      this.countList = list;
      this.count_detail = true;
    },
    setPlateNo(plateNo) {
      this.plateNo = plateNo;
    },
    closeWin() {
      this.showVehicleWin = false;
      this.$refs.mapBase.resetCollMarker();
      this.$refs.mapBase.closeMapDom();
    },
    showBeforeList() {
      this.closeWin();
      if (this.isShownBeforeList === false) {
        this.$refs.detailBox.getDetailsListRequest("before");
      }
      this.countIndex = -1;
      this.isShownBeforeList = true;
    },
    showAfterList() {
      this.closeWin();
      if (this.isShownBeforeList === true) {
        this.$refs.detailBox.getDetailsListRequest("after");
      }
      this.countIndex = -1;
      this.isShownBeforeList = false;
    },
    detailList(item, index) {
      this.showVehicleWin = true;
      this.countIndex = index;
    },
    drag(imgId, parentId, isLimitedDrag) {
      Toolkits.drag(imgId, parentId, isLimitedDrag);
    },
    closeRecordPanel() {
      this.$refs.mapBase.resetCollMarker();
      this.count_detail = false;
      this.isShowCharts = false;
    },
    chooseMapItem(index) {
      this.countIndex = index;
    },
  },
};
</script>

<style lang="less" scoped>
.hidden-vehicle-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  .count_detail {
    position: absolute;
    top: 5px;
    right: 20px;
    width: 300px;
    height: 570px;
    line-height: 60px;
    font-size: 12px;
    color: #666666;
    z-index: 970;
    background: #ffffff;
    text-indent: 22px;
    border: solid 1px rgba(167, 172, 184, 0.3);
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);

    .count_detail_title {
      width: 100%;
      height: 100%;
      /*background: #2c86f8;*/
      border-top: 4px solid #2c86f8;

      .count_detail_title_header {
        height: 42px;
        background: #ffffff;
        padding: 0 20px;
        cursor: move;

        p {
          text-indent: 0;
          cursor: pointer;
        }

        .leftSelectedTabBtn,
        .leftNonTabBtn {
          position: absolute;
          left: 20px;
          width: 130px;
          height: 30px;
          line-height: 15px;
          text-align: center;
          border: 1px solid #2c86f8;
          padding: 7px 7px 7px 0px;
          cursor: pointer;
          box-sizing: border-box;
          border-radius: 5px 0px 0px 5px;
        }

        .rightNonTabBtn,
        .rightSelectedTabBtn {
          position: absolute;
          right: 20px;
          height: 30px;
          line-height: 15px;
          text-align: center;
          border: 1px solid #2c86f8;
          width: 130px;
          padding: 7px 7px 7px 0px;
          box-sizing: border-box;
          border-radius: 0px 5px 5px 0px;
          cursor: pointer;
        }

        .leftSelectedTabBtn,
        .rightSelectedTabBtn {
          background: #2c86f8;
          color: #fff;
        }

        .leftNonTabBtn,
        .rightNonTabBtn {
          background: #ffffff;
          color: #2c86f8;
        }

        .count_detail_title_name {
          float: left;
          width: auto;
          height: 50px;
          line-height: 50px;
          color: #666666;
          font-size: 14px;
          font-weight: bold;
        }

        .count_detail_close {
          position: absolute;
          right: 10px;
          top: 0;
          cursor: pointer;
          &:hover {
            color: red;
          }
        }

        .count_detail_export {
          float: right;
          height: 50px;
          line-height: 50px;
          color: #2c86f8;
          margin-right: 10px;
          cursor: pointer;
        }
      }
    }

    .count_detail_content {
      height: 480px;
      .count_detail_item {
        position: relative;
        width: 100%;
        height: 72px;
        margin-top: -1px;
        cursor: pointer;

        .markIcon {
          float: left;
          width: 55px;
          height: 100%;
          line-height: 71px;
          /*text-align: center;*/
          font-size: 12px;
          color: #fff;
          background: url("~@/assets/img/map/aggregation/track-car-marker-big.png")
            no-repeat center;

          span {
            position: relative;
            top: -2px;
          }
        }
        .oneDigitsStyle {
          margin-left: 3px;
        }
        .twoDigitsStyle {
          margin-left: -1px;
        }

        .threeDigitsStyle {
          margin-left: -5px;
        }

        .fourDigitsStyle {
          margin-left: -8px;
        }
        .moreDigitsStyle {
          margin-left: -12px;
        }
        .active_markIcon {
          float: left;
          width: 55px;
          height: 100%;
          line-height: 71px;
          /*text-align: center;*/
          font-size: 12px;
          color: #fff;
          background: url("~@/assets/img/map/aggregation/track-car-marker-hover-big.png")
            no-repeat center;
        }

        .vertical_line {
          display: none;
          position: absolute;
          width: 3px;
          height: 100%;
          background: #2c86f8;
        }

        .count_detail_content_box {
          /*width: 94%;*/
          height: 100%;
          margin: 0 auto;
          border-bottom: dashed 1px rgba(167, 172, 184, 0.3);

          p {
            text-indent: 0;
          }
          .face_time {
            float: left;
            margin-left: -20px;
            width: 248px;
            height: 28px;
            line-height: 40px;
            font-size: 12px;

            .icon-time {
              width: 16px;
              height: 16px;
              margin-right: 2px;
              vertical-align: middle;
            }
          }

          .face_time_title {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        &:hover {
          background: rgba(177, 181, 191, 0.2);

          .count_detail_content_box {
            border-bottom: dashed 1px rgba(167, 172, 184, 0.3);
          }

          .vertical_line {
            display: block;
          }

          .markIcon {
            float: left;
            width: 55px;
            height: 100%;
            line-height: 71px;
            /*text-align: center;*/
            font-size: 12px;
            color: #fff;
            background: url("~@/assets/img/map/aggregation/track-car-marker-hover-big.png")
              no-repeat center;
          }
        }
      }

      li.active_hover_class {
        background: #fff;

        .count_detail_content_box {
          border-bottom: dashed 1px rgba(167, 172, 184, 0.3);
        }

        .vertical_line {
          display: block;
        }
      }
      .xui-waterfall-item {
        display: block !important;
      }
    }
  }
}
</style>
