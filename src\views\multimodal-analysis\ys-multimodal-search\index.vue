<!--
 * @Date: 2025-03-04 16:25:41
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-12 16:17:58
 * @FilePath: \icbd-view\src\views\multimodal-analysis\multimodal-analysis-lib\index.vue
-->
<template>
  <div class="muti-content">
    <div class="search-box">
      <MutiSearch
        @searchHander="searchHander"
        :deviceList="deviceList"
      ></MutiSearch>
    </div>
    <div class="padding-box">
      <div class="container">
        <!-- 操作栏 -->
        <div class="result-list">
          <div
            class="result-item"
            v-for="(item, index) in dataList"
            :key="item.id"
          >
            <ResultCard
              showMoreTip
              :data="item"
              @onAction="onAction"
              @click.native="openDetailMutiAnalysis(item, index)"
              @checkHandler="(val) => checkHandler(val, item)"
            ></ResultCard>
          </div>
          <!-- <div
            class="empty-card-1"
            v-for="(item, index) of 5 - (dataList.length % 5)"
            :key="index + 'demo'"
          ></div> -->
          <ui-empty v-if="total === 0"></ui-empty>
          <div class="loading-box" ref="observerBox">
            <ui-loading v-if="scrollLoading && fallsPage"></ui-loading>
            <div
              class="last-page"
              v-if="!isObserver && fallsPage && pageInfo.start > 0"
            >
              数据到底了！！！
            </div>
          </div>
        </div>
      </div>
    </div>
    <EverySearchDetailMutiAnalysis
      v-if="showDetail"
      ref="detailMutiAnalysisRef"
      :tableList="dataList"
      @close="showDetail = false"
    ></EverySearchDetailMutiAnalysis>
    <direction-model ref="directionModel"></direction-model>
  </div>
</template>

<script>
import { mapMutations } from "vuex";
import MutiSearch from "./components/muti-search.vue";
import ResultCard from "../multimodal-everything-search/components/result-card.vue";
import EverySearchDetailMutiAnalysis from "@/components/detail/everySearch-detail-muti-analysis.vue";
import { ysLLmSearchList } from "@/api/multimodal-analysis";
import directionModel from "@/views/wisdom-cloud-search/search-center/advanced-search/components/direction-model";
export default {
  name: "ys-muit-lib-analysis",
  components: {
    MutiSearch,
    ResultCard,
    EverySearchDetailMutiAnalysis,
    directionModel,
  },
  data() {
    return {
      allCheckStatus: false,
      fallsPage: true,
      scrollLoading: false,
      pageInfo: {
        start: 0,
        limit: 45,
      },
      total: 1,
      showDetail: false,
      dataList: [],
      isObserver: false,
      deviceList: [],
    };
  },
  computed: {
    indeterminate() {
      return (
        this.dataList.filter((item) => item.isChecked).length !==
          this.dataList.length && this.allCheckStatus
      );
    },
  },
  mounted() {
    this.setLayoutNoPadding(true);
    this.initObserver();
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    openDetailMutiAnalysis(item, index) {
      this.showDetail = true;
      this.$nextTick(() => {
        this.$refs.detailMutiAnalysisRef.showList(index);
      });
    },
    allChecked(val) {
      this.dataList = this.dataList.map((item) => ({
        ...item,
        isChecked: val,
      }));
    },
    checkHandler() {
      // item.isChecked = checked;
      this.allCheckStatus =
        this.dataList.filter((item) => item.isChecked).length > 0;
    },
    initObserver() {
      const that = this;
      this.observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && that.fallsPage && that.isObserver) {
            this.getDataList(that.pageInfo.start + that.pageInfo.limit);
          }
        },
        {
          rootMargin: "0px",
          threshold: 0.1,
        }
      );

      // 在 nextTick 中确保 DOM 已渲染
      this.$nextTick(() => {
        const loadingRef = this.$refs.observerBox;
        if (loadingRef) {
          this.observer.observe(loadingRef);
        }
      });
    },
    /**
     * @description: 瀑布 - 翻页切换
     */
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.queryList();
    },
    getRect(rect) {
      if (!rect) return "";
      return rect?.replaceAll(";", ",");
    },
    getYTRect(rect) {
      if (!rect) return "";
      const data = this.getRect(rect)?.split(",");
      return {
          x: data[0],
          y: data[1],
          w: data[2] - data[0],
          h: data[3] - data[1],
        }
    },
    getDataList(page = 0) {
      this.scrollLoading = true;
      this.pageInfo.start = page;
      let params = {
        ...this.pageInfo,
      };
      if (!this.fallsPage) {
        this.checkAll = false;
      }
      ysLLmSearchList(params)
        .then((res) => {
          const data = res.data;
          this.dataList.push(
            ...data?.map((item) => ({
              ...item,
              score: item.similarity,
              ytRect: this.getYTRect(item.rect),
              rect: this.getRect(item.rect),
            }))
          );
          this.isObserver = !(data?.length < this.pageInfo.limit);
        })
        .finally(() => {
          this.total = this.dataList?.length;
          this.scrollLoading = false;
        });
    },
    searchHander(params) {
      this.pageInfo = {
        ...this.pageInfo,
        ...params,
      };
      this.queryList();
    },
    queryList() {
      this.dataList = [];
      this.allCheckStatus = false;
      if (this.fallsPage) {
        this.isObserver = false;
      }
      this.getDataList();
    },
    onAction(item, type) {
      if (type === 3) this.openDirectModel(item);
    },
    openDirectModel(row) {
      const { latitude, longitude, deviceName } = row;
      if (!latitude || !longitude) {
        this.$Message.error("暂无坐标");
        return;
      }
      this.$refs.directionModel.show({
        geoPoint: {
          lat: latitude,
          lon: longitude,
        },
        deviceName: deviceName,
      });
    },
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.observer?.disconnect();
    this.setLayoutNoPadding(false);
  },
};
</script>

<style lang="less" scoped>
.muti-content {
  width: 100%;
  height: 100%;
  background: #fff;
  background-image: url("~@/assets/img/multimodal-analysis/multi-analysis-bg.png");
  background-repeat: no-repeat;
  background-size: contain;
  display: flex;
  flex-direction: column;

  .search-box {
    margin-top: 20px;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .padding-box {
    flex: 1;
    padding: 20px 10px 10px 10px;
    background: transparent;
    display: flex;
    overflow: hidden;
  }

  .container {
    display: flex;
    flex-direction: column;
    .operate {
      display: flex;
      justify-content: space-between;

      .right-box {
        display: flex;
        gap: 10px;
        height: 20px;
        align-items: center;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);

        .flex-center {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .rotate {
          transform: rotate(180deg);
        }
        .line {
          height: 16px;
          border: 1px solid #d8d8d8;
        }

        .point {
          cursor: pointer;
        }
      }
    }

    .result-list {
      flex: 1;
      position: relative;
      padding-top: 16px;
      display: flex;
      gap: 20px;
      flex-wrap: wrap;
      height: calc(~"100% - 40px");
      overflow: scroll;

      .result-item {
        width: 335px;
      }
      .empty-card-1 {
        width: 335px;
        height: 254px;
      }
      .loading-box {
        position: relative;
        width: 100%;
        height: 80px;
        .last-page {
          margin: o auto;
          text-align: center;
          font-size: 20px;
        }
      }
    }
  }
}
</style>
