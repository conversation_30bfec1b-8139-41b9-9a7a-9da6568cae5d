<template>
  <div>
    <Form v-bind="$attrs" :model="formData" :rules="formRule" ref="formData" :label-width="100">
      <slot name="before" :row="formData"></slot>
      <FormItem label="指标名称" class="right-item mb-sm" prop="name">
        <Input
          type="text"
          v-model="formData.indexActualName"
          :maxlength="20"
          disabled
          class="form-item-width"
          placeholder="请输入指标名称"
        >
        </Input>
      </FormItem>
      <FormItem label="显示名称" class="right-item mb-sm" prop="indexName">
        <Input
          type="text"
          v-model="formData.indexName"
          :maxlength="20"
          :disabled="isView"
          class="form-item-width"
          placeholder="请输入显示名称"
        >
        </Input>
      </FormItem>
      <slot name="data-target-value" :row="formData">
        <FormItem label="达标值" class="right-item mb-md" prop="dataTargetValue">
          <InputNumber
            v-model.number="formData.dataTargetValue"
            :max="100"
            :min="1"
            placeholder="请输入达标值"
            :disabled="isView"
            class="form-item-width"
          ></InputNumber>
          <span class="params-suffix ml-sm">%</span>
        </FormItem>
      </slot>
      <FormItem label="计算公式" class="right-item mb-0" prop="name">
        <span style="color: #2b84e2" class="index-definition">{{ formData.indexDefinition }}</span>
        <slot name="indexDefinition"></slot>
      </FormItem>
      <FormItem label="评价标准" class="right-item mb-sm" prop="name">
        <Input
          v-model="formData.evaluationCriterion"
          :disabled="isEditEvaluationCriterion || isView"
          class="evaluation-criterion"
          type="textarea"
          :rows="7"
          placeholder="要求设备编码、设备名称、功能类型、设备厂家、行政区域、监控点类型、设备经纬度、设备IP地址均填报且正确。"
        />
      </FormItem>
      <slot name="after" :row="formData"></slot>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'common-form',
  components: {},
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      type: Object,
    },
    /**
     *  是否可以编辑 评价标准
     *  default false
     */
    isEditEvaluationCriterion: {
      default: false,
    },
    ruleValidate: {
      default: () => ({}),
    },
  },
  data() {
    return {};
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
    formRule() {
      let rule = {
        dataTargetValue: [
          {
            required: true,
            type: 'number',
            message: '达标值不能为空',
            trigger: 'blur',
          },
        ],
        indexName: [
          {
            required: true,
            type: 'string',
            message: '显示名称不能为空',
            trigger: 'blur',
          },
        ],
      };
      return { ...rule, ...this.ruleValidate };
    },
  },
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    async validate() {
      let validate = await this.$refs.formData.validate();
      if (validate) {
        return {
          id: this.formData.id,
          dataTargetValue: this.formData.dataTargetValue,
          evaluationCriterion: this.formData.evaluationCriterion,
          indexName: this.formData.indexName,
        };
      }
      throw new Error();
    },
  },
};
</script>

<style lang="less" scoped>
@import url('../../components/common.less');
.form-item-width {
  width: 380px;
}
.mb-0 {
  margin-bottom: 0;
}
.evaluation-criterion {
  width: 380px;
}
</style>
