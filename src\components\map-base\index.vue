<template>
  <div class="base-map">
    <div :class="['base-map', locationCursor ? 'location-cursor' : '']" :id="mapId"></div>
    <right-tool
      class="right-tool"
      v-if="rightTool"
      @initSatelliteMap="initSatelliteMap"
      @showMapTool="showMapTool"
      @cameraShow="cameraShow"
      @heatShow="heatShow"
    ></right-tool>
    <right-button-group v-show="rightButtonGroup" @pointMap="pointMap"> </right-button-group>
    <slot name="deviceSlot"></slot>
    <bottom-tool
      ref="bottomTool"
      v-if="bottomTool"
      v-show="mapToolVisible"
      :map-tool="mapTool"
      @cancelDraw="cancelDraw"
      @selectRect="selectRect"
      @selectCircle="selectCircle"
      @selectPolygon="selectPolygon"
      @clearDraw="clearDraw"
    >
      <template #addTool>
        <slot name="addTool"></slot>
      </template>
    </bottom-tool>
    <map-distributed
      v-if="mapDistributed"
      v-show="mapDistributedVisible"
      :class="mapDistributedVisible ? 'move-distributed' : ''"
    ></map-distributed>
    <!-- 地图上的正常dom弹框 -->
    <map-dom ref="mapDom" :map-dom-data="mapDomData" @close="closeMapDom">
      <template #mapbubble>
        <slot name="mapBubbleDom"></slot>
      </template>
    </map-dom>
  </div>
</template>
<style lang="less" scoped>
.base-map {
  position: relative;
  height: 100%;
  overflow: hidden;
  .base-map {
    width: 100%;
    height: 100%;
  }
  .move-distributed {
    right: 20px;
  }
  .right-tool {
    position: absolute;
    top: 20px;
    bottom: 0px;
    left: 20px;
    z-index: 725;
    display: flex;
  }
}
.location-cursor {
  cursor:
    url('../.././assets/img/device-map/position.png') 1 0,
    default !important;
}
</style>
<script>
import { NPGisMapMain } from '@/map/map.main';
import { mapGetters, mapActions } from 'vuex';
import common from '@/config/api/common';
let infoWindow = null;
export default {
  name: 'map-base',
  props: {
    chooseMapItem: {
      type: Object,
    },
    cameraList: {
      type: Array,
    },
    rightTool: {
      type: Boolean,
      default: false,
    },
    bottomTool: {
      type: Boolean,
      default: false,
    },
    mapDistributed: {
      type: Boolean,
      default: false,
    },
    mapPosition: {
      type: Object,
    },
    mapTool: {
      type: Array,
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    needQueryDeviceInfo: {
      type: Boolean,
      default: false,
    },
    rightButtonGroup: {
      type: Boolean,
      default: false,
    },
    // 加载地图区域轮廓
    geoRegions: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 更改地图缩放层级回调方法
    zoomChange: {
      type: Function,
    },
    // 拖动地图结束回调
    dragEnd: {
      type: Function,
    },
    // 地图中心点
    center: {
      type: Object,
    },
    mapDomOffsetHeight: {
      type: String,
      default: '-205',
    },
    locationCursor: {
      type: Boolean,
      default: false,
    },
    customMarker: {
      type: Object,
      default: () => {},
    }, // 自定义定位图标
    enableCloseOnClick: {
      type: Boolean,
      default: true,
    },
    needQueryAll: {
      type: Boolean,
      default: false,
    },
    needCloseCallBack: {
      type: Boolean,
      default: false,
    },
    // 地图移动偏移像素
    pixel: {
      type: Object,
      default: () => {
        return {
          x: 0,
          y: 0,
        };
      },
    },
  },
  data() {
    this.mapId = 'baseId' + Math.random();
    this.mapMain = null;
    this.infoWindowArr = [];
    this.pointData = [];
    this.heatData = [];
    return {
      mapToolVisible: true,
      mapDistributedVisible: true, //热力图示是否显示
      showCamera: true, // 摄像头默认显示
      mapDomData: {},
      position: {},
      selectedCameraList: [],
      locationMarker: '',
    };
  },
  created() {
    this.getMapConfig();
  },
  mounted() {},
  methods: {
    ...mapActions({
      setMapConfig: 'common/setMapConfig',
      setMapStyle: 'common/setMapStyle',
    }),
    /*-----------------------------------自定义事件-------------------------------- */
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig, this.mapStyle);
      } catch (err) {
        console.log(err);
      }
    },
    async _initMap(data, style) {
      this.mapMain = new NPGisMapMain();
      this.mapMain.init(this.mapId, data, style);

      //this.initHeatData();
      // 初始化自定义区域框选
      if (this.mapPosition) {
        this.drawPosition(this.mapPosition);
        this.position = this.mapPosition;
      }
      if (this.cameraList) this._initSystemPoints2Map(this.cameraList);
      if (this.geoRegions.length) this.updateGeoRegion(this.geoRegions);
      this.zoomChange && this.listenerZoomChange();
      this.dragEnd && this.listenerDragEnd();
    },
    listenerZoomChange() {
      this.mapMain.listenerZoomChange((zoom) => {
        this.$emit('getExtent', this.mapMain.getExtent());
        this.zoomChange(zoom);
        // this.mapMain.removeListenerZoomChange()
      });
    },
    listenerDragEnd() {
      this.mapMain.listenerDragEnd(() => {
        this.$emit('getExtent', this.mapMain.getExtent());
        this.dragEnd();
        // this.mapMain.removeListenerDragEnd()
      });
    },
    // 显示热力图
    initHeatData() {
      this.$http.get(common.getDeviceInfoList).then((res) => {
        this.heatData = res.data.data.map((row) => {
          return {
            lat: row.latitude,
            lon: row.longitude,
            count: row.captrueNum,
          };
        });
        this.mapMain.renderHeatMap(this.heatData, 15);
        if (this.mapDistributedVisible) {
          this.mapMain.showHeatMap();
        } else {
          this.mapMain.hideHeatMap();
        }
      });
    },
    // 加载点位到地图上
    _initSystemPoints2Map(points) {
      if (this.mapMain) {
        // 加载点位
        this.mapMain.renderMarkers(
          this.mapMain.convertSystemPointArr2MapPoint(points),
          this.getMapEvents(),
          false,
          this.customMarker,
        );
      }
    },
    renderAddMarkers(points) {
      this.mapMain.renderAddMarkers(this.mapMain.convertSystemPointArr2MapPoint(points), this.getMapEvents());
    },
    getMapEvents() {
      let opts = {
        click: (marker) => {
          if (this.needQueryDeviceInfo) {
            return this.$emit('queryDeviceInfo', this.needQueryAll ? marker.ext : marker.ext.ObjectID);
          }
          let point = {};
          point.deviceName = marker.ext.Name;
          point.lat = marker.ext.Lat;
          point.lon = marker.ext.Lon;
          point.address = marker.ext.Address;
          point.type = 'device';
          point.deviceId = marker.ext.ObjectID;
          point.orgName = marker.ext.OrgName;
          point.checkStatus = marker.ext.Status;
          point.tagList = marker.ext.TagList;
          point.imageUrls = marker.ext.ImageUrls;
          point.errorMessage = marker.ext.ErrorMessage;
          this.selectItem(point);
        },
        // mouseover要展示摄像头名称和详细信息
        mouseover: () => {},
      };
      return opts;
    },
    removeMarkers(points) {
      points.forEach((row) => {
        this.mapMain.removeMarker1(row.deviceID || row.deviceId);
      });
      // 不显示摄像头时，需要弹框提示也清除
      this.closeAllInfoWindow();
    },
    // 关闭所有的弹框提示
    closeAllInfoWindow() {
      this.infoWindowArr.forEach((row) => {
        row.close();
      });
    },
    selectItem(pointItem) {
      // this.closeMapDom()
      this.mapDomData = {};
      // 显示之前先清除其他提示框
      this.closeAllInfoWindow();
      let point = new NPMapLib.Geometry.Point(pointItem.lon, pointItem.lat);
      // 弹窗显示得时候地图是否需要移动到弹窗位置
      // setCenter ? this.mapMain.map.setCenter(point) : null;

      this.mapMain.map.centerAndZoom(point, 17);
      // 当弹框比较大的时候需要偏移地图展示完整的弹框
      this.mapMain.map.panByPixel(this.pixel.x, this.pixel.y);
      //this.mapMain.closeInfoWindow(this.selWinId);
      const { offsetWidth, offsetHeight } = this.calculateOffset(pointItem);
      let opts = {
        // width: 50, // 信息窗宽度
        // height: 100, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(offsetWidth, offsetHeight), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: this.enableCloseOnClick, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
        autoSize: true, // 默认true, 窗口大小是否自适应
        // paddingForPopups: NPMapLib.Geometry.Extent, // 信息窗自动弹回后，距离四边的值。isAdaptation为true时，该设置有效
        isAdaptation: true, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
        // useDomStyle: true,
        positionBlock: {
          // 箭头样式
          imageSrc: require('@/assets/img/map/triangle.png'),
          imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
          offset: new NPMapLib.Geometry.Size(-0, 80),
        },
      };
      infoWindow = new NPMapLib.Symbols.InfoWindow(point, null, null, opts);
      this.mapDomData = pointItem;
      let dom = this.$refs.mapDom.$el;
      infoWindow.setContentDom(dom);
      this.mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      this.infoWindowArr.push(infoWindow);
      this.$emit('getExtent', this.mapMain.getExtent());
      this.dragEnd && this.dragEnd();
    },
    //  关闭弹框
    closeMapDom() {
      infoWindow && infoWindow.close();
      infoWindow = null;
      if (!this.needCloseCallBack) return false;
      this.$emit('handleClose');
    },
    /*-----------------------------------绑定事件-------------------------------- */
    // 全屏展示
    initSatelliteMap(val) {
      this.$emit('fullScreen', val);
      // if (this.mapMain) {
      //   this.mapMain.destroy();
      //   this.mapMain = null;
      //   if (val) {
      //     this.getMapConfig(map.getSatelliteMap, val);
      //   } else {
      //     this.getMapConfig();
      //   }
      // }
    },
    //回到起始位置
    homingMap() {
      this.mapMain.map.reset();
    },
    // 固定放大地图
    enlargeMap() {
      this.mapMain.map.zoomInFixed();
    },
    // 固定缩小地图
    narrowMap() {
      this.mapMain.map.zoomOutFixed();
    },
    // 显示底部地图控件
    showMapTool(bool) {
      this.mapToolVisible = bool;
    },
    cameraShow(val) {
      this.showCamera = val;
      val ? this.renderAddMarkers(this.cameraList) : this.removeMarkers(this.cameraList);
    },
    heatShow(val) {
      val ? this.mapMain.showHeatMap() : this.mapMain.hideHeatMap();
      this.mapDistributedVisible = val;
    },
    // bottom工具操作
    cancelDraw() {
      this.mapMain.cancelDraw();
    },
    selectRect() {
      this.mapMain.selectRectangle((points, position) => {
        this.selectPoint(points);
        this.position = position;
      }, true);
    },
    selectCircle() {
      this.mapMain.selectCircle((points, position) => {
        this.selectPoint(points);
        this.position = position;
      }, true);
    },
    selectPolygon() {
      this.mapMain.selectPolygon((points, position) => {
        this.selectPoint(points);
        this.position = position;
      }, true);
    },
    clearDraw() {
      this.mapMain.clearDraw();
      this.mapMain.showHeatMap();
      this.mapMain.removeRectangle();
      // 如果摄像头不显示，清除框选之后要清除
      this.showCamera ? null : this.removeMarkers(this.selectedCameraList);
      this.closeAllInfoWindow();
      this.position = {};
      this.$emit('clear');
    },
    // 过滤点位加载到地图上
    selectPoint(points) {
      this.removeMarkers(this.selectedCameraList);
      this.selectedCameraList = [];
      // 将选中的点位数组转换为对象减少循环次数
      let pointObj = new Object();
      for (let i = 0, len = points.length; i < len; i++) {
        pointObj[points[i].ObjectID] = points[i];
      }
      this.cameraList.forEach((row) => {
        let index = this.selectedCameraList.findIndex((rw) => {
          return rw.deviceId === row.deviceId;
        });
        if (!!pointObj[row.deviceId] && index === -1) {
          this.selectedCameraList.push(row);
        }
      });
      this.showCamera ? null : this.removeMarkers(this.pointData);
      this.showCamera ? null : this.renderAddMarkers(this.pointData);
      this.$emit('selectCamera', this.selectedCameraList);
    },
    drawPosition(position) {
      this.mapMain.removeRectangle();
      switch (position.type) {
        case 'rectangle':
          this.mapMain.drawRectangle(position, this.isEdit, (points, positions) => {
            this.selectPoint(points);
            this.position = positions;
          });
          this.mapMain.map.setCenter(position.center);
          break;
        case 'polygon':
          this.mapMain.drawPolygon(position, this.isEdit, (points, positions) => {
            this.selectPoint(points);
            this.position = positions;
          });
          this.mapMain.map.setCenter(position.center);
          break;
        case 'circle':
          this.mapMain.drawCircle(position, this.isEdit, (points, positions) => {
            this.selectPoint(points);
            this.position = positions;
          });
          this.mapMain.map.setCenter(position.center);
          break;
        case 'multiPolygon':
          this.mapMain.drawMultiPolygon(position.pps);
          break;
        default:
          console.log('还不支持此图形');
          break;
      }
    },
    // 计算弹框偏移量
    calculateOffset(pointItem) {
      const { imageUrls, tagList, errorMessage } = pointItem;
      let offsetHeight = this.mapDomOffsetHeight;
      let offsetWidth = -40;
      let step = 20;
      if (imageUrls && imageUrls.length > 0) {
        offsetHeight = offsetHeight - 100;
      }
      if (tagList && tagList.length > 3) {
        offsetHeight = offsetHeight - step * 3;
      } else if (tagList && tagList.length >= 1 && tagList.length <= 3) {
        offsetHeight = offsetHeight;
      }
      if (errorMessage && errorMessage.length >= 3) {
        offsetHeight = offsetHeight - step * 2;
      } else if (errorMessage && errorMessage.length >= 1 && errorMessage.length < 3) {
        offsetHeight = offsetHeight - step * 1;
      }
      return {
        offsetWidth,
        offsetHeight,
      };
    },
    pointMap(type) {
      this[`${type}Map`]();
    },
    updateGeoRegion(geoRegions) {
      this.mapMain.destoryGeoRegion();
      geoRegions && geoRegions.length ? this.mapMain.addGeoRegions(geoRegions) : null;
    },

    // 添加地图监听事件
    handleAddMapEventListener(type, callBackMethod) {
      this.mapMain.addMapEventListener(type, callBackMethod);
    },
    // 移除监听事件
    removeEvent(type) {
      this.mapMain.removeEvent(type);
    },
    // 添加点位
    handleAddMarker(pt, markerParam) {
      const point = new NPMapLib.Geometry.Point(pt.lon, pt.lat);
      const size = new NPMapLib.Geometry.Size(markerParam.size.width, markerParam.size.height);
      const icon = new NPMapLib.Symbols.Icon(markerParam.url, size);
      icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height / 2));
      this.locationMarker = new NPMapLib.Symbols.Marker(point);
      this.locationMarker.setIcon(icon);
      this.mapMain.map.addOverlay(this.locationMarker);
    },
    // 移除点位
    handleRemoveMarker() {
      this.mapMain.map.removeOverlay(this.locationMarker);
    },
    markerDragEnd() {
      this.mapMain.addMarkerEventListener(this.locationMarker, 'MARKER_EVENT_DRAG_END', () => {
        const position = this.locationMarker.getPosition();
        this.$emit('handleMarkerDragEnd', position);
      });
    },
    removeListenerDragEnd() {
      if (!this.locationMarker) {
        return;
      }
      this.mapMain.removeMarkerEventListener(this.locationMarker, 'MARKER_EVENT_DRAG_END');
    },
    /**
     * 设置定位marker可编辑
     * type：
     *  NPMap.ModifyFeature_DRAG 拖放
     *  NPMap.ModifyFeature_ROTATE 旋转
     *  NPMap.ModifyFeature_RESIZE 大小变化
     * NPMap.ModifyFeature_RESHAPE 形状变化
     */
    handleMarkerEnableEditing(type) {
      this.locationMarker.enableEditing(type);
    },
    // 设置定位marker禁用编辑
    handleMarkerDisableEditing(type) {
      this.locationMarker.disableEditing(type);
    },
    handleClearLayers() {
      this.mapMain.clearLayers();
    },
  },
  watch: {
    cameraList: {
      handler() {
        this._initSystemPoints2Map(this.cameraList);
      },
      deep: true,
    },
    chooseMapItem: {
      handler(val) {
        if (!val || !Object.keys(val).length) return false;
        let point = Object.assign({}, val);
        point.lat = val.latitude;
        point.lon = val.longitude;
        point.type = 'device';
        point.id = val.deviceId;
        this.$nextTick(() => {
          this.selectItem(point);
        });
      },
      deep: true,
    },
    position: {
      handler() {
        this.$emit('putPosition', this.position);
      },
      deep: true,
    },
    mapPosition(val) {
      this.drawPosition(val);
      this.position = val;
    },
    geoRegions: {
      handler(val) {
        this.mapMain && this.updateGeoRegion(val);
      },
      deep: true,
    },
    center: {
      handler(val) {
        if (!this.mapMain) return;
        const point = new NPMapLib.Geometry.Point(val.lon, val.lat);
        this.mapMain.setCenter(point);
      },
    },
  },
  computed: {
    ...mapGetters({
      mapConfig: 'common/getMapConfig',
      mapStyle: 'common/getMapStyle',
      //cameraList: "common/getCameraList",
      getDefaultSelectedArea: 'common/getDefaultSelectedArea',
    }),
  },
  components: {
    BottomTool: require('@/components/map-tool.vue').default,
    RightTool: require('./right-tool.vue').default,
    RightButtonGroup: require('./right-button-group.vue').default,
    MapDistributed: require('./map-distributed.vue').default,
    MapDom: require('./map-dom').default,
  },
  beforeDestroy() {
    if (this.mapMain) {
      this.mapMain.destroy();
      this.mapMain = null;
    }
  },
};
</script>
