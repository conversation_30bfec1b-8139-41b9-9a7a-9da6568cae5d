<template>
  <div style="height: 100%">
    <listPage :listObj="listObj" :taskObj="taskObj" :currentTree="currentTree" @refeshCount="refeshCount">
      <template #action="{ row }">
        <ui-btn-tip
          class="mr-md"
          v-if="$parent.currentTree.id == '202'"
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          v-if="row.isScreenShot != 1 && row.detectionMode != 1"
          class="mr-md"
          disabled
          icon="icon-chakanjietu"
          content="查看截图"
        ></ui-btn-tip>
        <ui-btn-tip
          v-else
          class="mr-md"
          icon="icon-chakanjietu"
          content="查看截图"
          @click.native="findPic(row)"
        ></ui-btn-tip>
        <!-- <Button class="btn" type="text" @click.stop="clickRow(row)">复核结果</Button> -->
      </template>
      <!-- <template #status="{ row }">
        {{ lineStatus[parseInt(row.outcome)] }}
      </template> -->
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <!-- 在线状态 -->
      <template #online="{ row }">
        <span
          v-if="$parent.currentTree.id === 202 || $parent.currentTree.id === 203"
          :class="{
            color_qualified: row.online === '1',
            color_unqualified: row.online === '2',
          }"
        >
          {{ row.online === '1' ? '在线' : row.online === '2' ? '离线' : '' }}
        </span>
        <span
          v-if="$parent.currentTree.id === 204 || $parent.currentTree.id === 205"
          :class="{
            color_qualified: row.online === '1',
            color_unqualified: row.online === '2',
          }"
        >
          {{ row.online === '1' ? '文件存在' : row.online === '2' ? '文件不存在' : '' }}
        </span>
      </template>
      <!-- 完好状态 -->
      <template #normal="{ row }">
        <span
          :class="{
            color_qualified: row.normal === '1',
            color_unqualified: row.normal === '2',
          }"
        >
          {{ row.normal === '1' ? '取流及时响应' : row.normal === '2' ? '取流超时响应' : '' }}
        </span>
      </template>
      <!-- 可用状态 -->
      <template #canPlay="{ row }">
        <span
          :class="{
            color_qualified: row.canPlay === '1',
            color_unqualified: row.canPlay === '2',
          }"
        >
          {{ row.canPlay === '1' ? '取流成功' : row.canPlay === '2' ? '取流失败' : '' }}
        </span>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #delaySipMillSecond="{ row }">
        <span>
          {{ row.delaySipMillSecond == 0 ? '--' : row.delaySipMillSecond }}
        </span>
      </template>
      <template #delayStreamMillSecond="{ row }">
        <span>
          {{ row.delayStreamMillSecond == 0 ? '--' : row.delayStreamMillSecond }}
        </span>
      </template>
      <template #delayIdrMillSecond="{ row }">
        <span>
          {{ row.delayIdrMillSecond == 0 ? '--' : row.delayIdrMillSecond }}
        </span>
      </template>
      <template #reason="{ row }">
        <Tooltip :content="row.reason" transfer max-width="150">
          {{ row.reason }}
        </Tooltip>
      </template>
    </listPage>
    <!-- 大图组件 -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div>
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </div>
</template>

<script>
import api from '@/config/api/inspectionrecord';
import vedio from '@/config/api/vedio-threm';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'commonPage',
  components: {
    listPage: require('./list').default,
    LookScene: require('@/components/look-scene').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {},
    },
    countInfo: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      bigPictureShow: false,
      imgList: [],
      lineStatus: ['', '在线', '离线', '无法考核'],
      listObj: {
        abnormalCount: [
          {
            title: '采集监控设备总数',
            count: 0,
            icon: 'icon-putongshishishipinketiaoyueshuaijiance',
          },
          {
            title: '检测数量',
            count: 0,
            icon: 'icon-putongshishishipinketiaoyueshuaijiance',
          },
          {
            title: '实时视频流无法调阅数量',
            count: 0,
            icon: 'icon-putongshishishipinketiaoyueshuaijiance',
          },
          {
            title: '无法检测数量',
            countKey: 0,
            icon: 'icon-putongshishishipinketiaoyueshuaijiance',
          },
        ],
        columns: [
          {
            title: '在线状态',
            slot: 'online',
            width: 120,
            renderHeader: (h) => {
              return h('div', [
                h('span', '在线状态'),
                h(
                  'Tooltip',
                  {
                    props: {
                      transfer: true,
                      placement: 'bottom',
                    },
                    style: { verticalAlign: 'middle' },
                  },
                  [
                    h('i', {
                      class: 'icon-font icon-wenhao ml-xs f-12',
                      style: {
                        width: '13px',
                        verticalAlign: 'top',
                      },
                    }),
                    h(
                      'span',
                      {
                        slot: 'content',
                      },
                      '设备在国标平台为在线状态',
                    ),
                  ],
                ),
              ]);
            },
          },
          {
            title: '完好状态',
            slot: 'normal',
            width: 140,
            renderHeader: (h) => {
              return h('div', [
                h('span', '完好状态'),
                h(
                  'Tooltip',
                  {
                    props: {
                      transfer: true,
                      placement: 'bottom',
                    },
                    style: { verticalAlign: 'middle' },
                  },
                  [
                    h('i', {
                      class: 'icon-font icon-wenhao ml-xs f-12',
                      style: {
                        width: '13px',
                        verticalAlign: 'top',
                      },
                    }),
                    h(
                      'span',
                      {
                        slot: 'content',
                      },
                      '设备在线，拉流请求有响应',
                    ),
                  ],
                ),
              ]);
            },
          },
          {
            title: '可用状态',
            slot: 'canPlay',
            width: 120,
            renderHeader: (h) => {
              return h('div', [
                h('span', '可用状态'),
                h(
                  'Tooltip',
                  {
                    props: {
                      transfer: true,
                      placement: 'bottom',
                    },
                    style: { verticalAlign: 'middle' },
                  },
                  [
                    h('i', {
                      class: 'icon-font icon-wenhao ml-xs f-12',
                      style: {
                        width: '13px',
                        verticalAlign: 'top',
                      },
                    }),
                    h(
                      'span',
                      {
                        slot: 'content',
                      },
                      '成功接收到实时视频流',
                    ),
                  ],
                ),
              ]);
            },
          },
          { title: '信令时延(毫秒)', slot: 'delaySipMillSecond', width: 130 },
          {
            title: '视频流时延(毫秒)',
            slot: 'delayStreamMillSecond',
            width: 130,
          },
          { title: '关键帧时延(毫秒)', slot: 'delayIdrMillSecond', width: 130 },
          { title: '连续离线次数', key: 'continuousOffline', width: 130 },
          { title: '连续无响应次数', key: 'continuousNoResponse', width: 130 },
          {
            title: '连续无法拉流次数',
            key: 'continuousCanNotPlay',
            width: 130,
          },
          { title: '检测时间', key: 'startTime', width: 160 },
          { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 100 },
          { title: '异常原因', key: 'reason', tooltip: 'true', width: 120 },
          {
            width: 100,
            title: '操作',
            fixed: 'right',
            slot: 'action',
            align: 'left',
          },
        ],
        loadData: (parameter) => {
          const { indexId, batchId, regionCode: orgCode } = this.taskObj;
          const params = {
            indexId,
            batchId,
            ...parameter,
            orgCode: parameter.orgCode ? parameter.orgCode : orgCode,
          };
          return this.$http.post(api.queryEvaluationVideoPageList, params).then((res) => {
            return res.data;
          });
        },
      },
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
      videoVisible: false,
      playDeviceCode: '',
      loadingVideo: false,
      isLive: true,
    };
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    findPic(item) {
      this.imgList = [item.screenShot];
      this.bigPictureShow = true;
    },
    refeshCount(orgCode) {
      this.$emit('refeshCount', orgCode);
    },
    async clickRow(row) {
      try {
        // this.isLive = true
        // this.loadingVideo = true
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        // this.loadingVideo = false
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
  },
  async mounted() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    setTimeout(() => {
      this.listObj.abnormalCount[0].count = this.countInfo.deviceCount;
      this.listObj.abnormalCount[1].count = this.countInfo.evaluatingCount;
      this.listObj.abnormalCount[2].count = this.countInfo.evaluatingFailedCount;
      this.listObj.abnormalCount[3].count = this.countInfo.unableEvaluatingCount;
    }, 500);
    // 可调阅率列表区分
    if (this.currentTree.id === 202 || this.currentTree.id === 203) {
      this.listObj.columns.push();
    }
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    countInfo() {
      this.listObj.abnormalCount[0].count = this.countInfo.deviceCount;
      this.listObj.abnormalCount[1].count = this.countInfo.evaluatingCount;
      this.listObj.abnormalCount[2].count = this.countInfo.evaluatingFailedCount;
      this.listObj.abnormalCount[3].count = this.countInfo.unableEvaluatingCount;
    },
  },
};
</script>

<style lang="less" scoped>
.btn {
  margin: 0 6px;
}
.auto-fill {
  height: 100%;
}
.video-player {
  @{_deep} .ivu-modal-body {
    height: 530px;
    display: flex;
    flex-direction: column;
  }
}
.color_qualified {
  color: var(--color-success);
}
.color_unqualified {
  color: var(--color-failed);
}
.mr-md {
  .icon-chakanjietu {
    font-size: 16px !important;
  }
}
/deep/ .el-loading-mask {
  background-color: #000 !important;
}
/deep/.el-loading-spinner {
  height: 0;
  left: 0;
  top: 50%;
  background: #000;
  i {
    font-size: 30px;
  }
}
</style>
