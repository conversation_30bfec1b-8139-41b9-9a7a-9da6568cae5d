<template>
	<ul class="color-box" :class="{ disabled: isDisabled }">
		<li class="all" v-if="isShowAll" :class="{ actived: !currentColor || currentColor == 'all' }" @click="handleSelected({ value: '' })">全部</li>
		<li class="color-item" v-for="(item, index) in colorList" :key="index" :style="{ backgroundColor: item.color }" :class="{ actived: currentColor == item.value }" @click="handleSelected(item)"></li>
	</ul>
</template>
<script>
	export default {
		props: {
			// 颜色选项
			colorList: {
				type: Array,
				default: () => [
					{
						color: '#EB4B4B',
						value: '#EB4B4B'
					},
					{
						color: '#F29F4C',
						value: '#F29F4C'
					},
					{
						color: '#FDEE38',
						value: '#FDEE38'
					},
					{
						color: '#67D28D',
						value: '#67D28D'
					},
					{
						color: '#2379F9',
						value: '#2379F9'
					}
				]
			},
			// 当前选中的颜色
			currentColor: {
				type: String,
				default: () => '#EB4B4B'
			},
			// 是否显示全部选项
			isShowAll: {
				type: Boolean,
				default: () => false
			},
			// 禁止点击
			isDisabled: {
				type: Boolean,
				default: () => false
			}
		},
		methods: {
			handleSelected(item) {
				if (this.isDisabled) return
				this.$emit('update:currentColor', item.value)
			}
		}
	}
</script>
<style lang="less">
	.color-box {
		display: flex;
		height: 100%;
		align-items: center;
		.color-item {
			display: block;
			width: 22px;
			height: 22px;
			margin-right: 10px;
			cursor: pointer;
			border-radius: 2px;
			position: relative;
			box-shadow: 0px 0px 0px 1px #fff inset;
			&.actived {
				border: 1px solid rgba(44, 134, 248, 1);
				&::before {
					content: "";
					display: block;
					position: absolute;
					right: -1px;
					bottom: -1px;
					width: 0;
					height: 0;
					border: 7px solid rgba(44, 134, 248, 1);
					border-left: 7px solid transparent;
					border-top: 7px solid transparent;
				}
				&::after {
					content: "";
					position: absolute;
					right: -1px;
					bottom: 1px;
					width: 40%;
					height: 22%;
					border: 2px solid #fff;
					border-radius: 1px;
					border-top: none;
					border-right: none;
					background: transparent;
					transform: rotate(-45deg);
				}
			}
			&:last-child {
				margin: 0;
			}
		}
		.all {
			border: 1px solid rgba(44, 134, 248, 1);
			color: rgba(44, 134, 248, 1);
			padding: 0 5px;
			line-height: 22px;
			border-radius: 2px;
			cursor: pointer;
			margin-right: 10px;
			&.actived {
				color: #fff;
				background-color: rgba(44, 134, 248, 1);
			}
		}
		&.disabled {
			.color-item,
			.all {
				cursor: default;
			}
		}
	}
</style>
