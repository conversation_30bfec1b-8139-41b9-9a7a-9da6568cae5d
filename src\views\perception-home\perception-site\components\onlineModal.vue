<template>
    <Modal v-model="visible" title="" :width="970" @close="handleCancel">
        <div class="header-title" slot="header">
            <p>当前在线 <span>( {{ total }} )</span></p>
        </div>
        <table class="table">
            <colgroup>
                <col width="240">
                <col width="200">
                <col width="162">
                <col width="163">
                <col width="163">
                <col width="8">
            </colgroup>
            <tr class="table-header">
                <th class="table-th" id="table-th" v-for="(item, index) in columns" :key='index'>{{ item.title }}</th>
            </tr>
        </table>
        <div class="table-wrapper" v-load-more.expand="{func:handlepullDown, target:'.table-body', delay:500}"
                :load-more-disabled="judge >= total">
            <div class="table-body table-overflowY">
                <table class="table-t">
                    <colgroup>
                        <col width="240">
                        <col width="200">
                        <col width="162">
                        <col width="163">
                        <col width="163">
                    </colgroup>
                    <tbody>
                        <tr class="table-content" v-for="(item, index) in tableData" :key='index'>
                            <td>
                                <div class="td-padding">
                                    <div class="member-tips">
                                        <div class="header-img">
                                            <ui-image :src="item.avatar ? item.avatar : url" viewer  />
                                        </div>
                                        <div class="member">
                                            <p class="member-name">{{ item.name }}</p>
                                            <p class="member-num">{{ item.cardId }}</p>
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="td-padding">{{ item.loginTime }}</div>
                            </td>
                            <td>
                                <div class="td-padding">{{ item.onlineTime }}</div> 
                            </td>
                            <td>
                                <div class="td-padding">{{ item.orgName }}</div>
                            </td>
                            <td>
                                <div class="td-padding">{{ item.username }}</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <ui-empty v-if="tableData.length === 0 && loading== false"></ui-empty>
        </div>
        <!-- <div class="loadingIcon">
            <ui-empty v-if="tableData.length === 0 && loading== false"></ui-empty>
        </div> -->
        <ui-loading v-if="loading"></ui-loading>
        <div slot="footer">
            <div></div>
        </div>
    </Modal>
</template>

<script>
import { onLine, querySysOauthClient } from '@/api/home';
import { parseDate, dateTime, timeFn } from '@/util/modules/common'
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            visible: false,
            loading: false,
            url: require("@/assets/img/user-center/default_user_avatar.png"),
            columns:[
                { title: '成员', width:"240", slot: 'member' },
                { title: '登录时间', width:"200", key: 'loginTime' },
                { title: '在线时长', key: 'onlineTime' },
                { title: '组织', key: 'orgName' },
                { title: '登录名称', key: 'username', }
            ],
            tableData:[],
            total:0,
            page: {
                size: 10,
                current: 1
            },
            judge:10
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        show(){
            this.tableData = [];
            this.visible = true;
            this.loading = true;
            this.page.current = 1;
            this.judge = 10;
            this.queryList();
        },
        async queryList() {
            let timeRes = await querySysOauthClient();
            let params = {
                ...this.page
            };
            onLine(params)
            .then(res =>{
                this.total = res.total;
                res.records.map(item =>{
                    let time = (timeRes.data.accessTokenValidity*60 - item.expires_in) * 1000
                    item.onlineTime = timeFn(time)
                    item.loginTime = dateTime(new Date().getTime() - time)
                    item.orgName = item.orgList[0].orgName;
                })
                if(res.records.length > 0) {
                    res.records.map(item => {
                        this.tableData.push(item)
                    })
                }
            })
            .catch()
            .finally(()=>{
                this.loading = false;
            })
        },
        // 取消
        onCancel() {
            this.visible = false;
        },
        handleCancel(){
            this.visible = false;
        },
        handlepullDown(){
            if(this.judge >= this.total) {
                this.$Message.warning('暂无更多数据');
                return
            }
            this.page.current ++;
            this.judge += 10;
            this.queryList()
        }
    }
}
</script>

<style lang='less' scoped>
/deep/.ivu-modal-header{
    background: rgba(211, 215, 222, 0.3);
}
.header-title{
    font-size: 16px;
    color: rgba(0,0,0,0.9);
    p{
        font-weight: bold !important;
    }
    span{
        color: rgba(44, 134, 248, 1);
    }
}
.member-tips{
    display: flex;
    .header-img{
        width: 40px;
        height: 40px;
        /deep/ .viewer{
            border-radius: 50%;
            background: none;
            img{
                border-radius: 50%;
                width: 40px;
                height: 40px;
            }
        }
    }
    .member{
        margin-left: 20px;
        .member-name{
            font-size: 14px;
            color: rgba(0,0,0,0.9);
            font-weight: bold;
            margin-bottom: 3px;
        }
        .member-num{
            font-size: 12px;
            color: rgba(0,0,0,0.35);
        }
    }
}
.table{
    width: 100%;
    overflow-y: auto;
    .table-header{
        width: 100%;
        .table-th{
            color: rgba(0,0,0,0.6);
            font-size: 14px;
            text-align: left;
            padding-left: 10px;
        }
    }
}
.table-wrapper{
    margin-top: 10px;
    position: relative;
}
.table-body{
    .table-content{
        padding-top: 10px;
        height: 70px;
        td{
            vertical-align:middle;
        }
        .td-padding{
            height: 60px;
            background: #F9F9F9;
            display: flex;
            align-items: center;
            padding-left: 10px;
            font-size: 14px;
            color: rgba(0,0,0,0.9);
        }
    }
}
.table-overflowY{
    overflow-y: auto;
    height: 650px;
}
.loadingIcon{
    // position: relative;
    margin-top: 40px;
}
</style>
