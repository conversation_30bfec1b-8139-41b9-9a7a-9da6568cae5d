<template>
  <div>
    <ui-modal ref="errorModel" :title="title" :styles="styles" footer-hide>
      <ui-table
        class="ui-table"
        :table-columns="columns"
        :table-data="tableData"
        :loading="loading"
        :minus-height="minusTable"
      >
        <template #deviceOsd="{ row }">
          <span v-if="row.deviceOsd">{{ row.deviceOsd }}</span>
          <span v-else>--</span>
        </template>
        <template #address="{ row }">
          <span v-if="row.deviceOsd">{{ row.address }}</span>
          <span v-else>--</span>
        </template>
      </ui-table>

      <div slot="footer"></div>
    </ui-modal>
  </div>
</template>
<script>
import api from '@/config/api/vedio-threm.js';
export default {
  name: 'errorView',
  props: [''],
  data() {
    return {
      loading: false,
      title: '不合格原因',
      styles: {
        width: '80%',
      },
      minusTable: 420,
      columns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '异常类型', key: 'reasonType', tooltip: true },
        { title: '不合格原因', key: 'errorMessage', tooltip: true },
        { title: '设备OSD信息', slot: 'deviceOsd', tooltip: true },
        { title: '设备库OSD信息', slot: 'address', tooltip: true },
      ],
      tableData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    showModal(param) {
      this.$refs.errorModel.modalShow = true;
      this.loading = true;
      this.$http.post(api.queryVideoResultBydeviceIds, param).then((res) => {
        if (res.data.code == 200) {
          this.loading = false;
          this.tableData = res.data.data.entities;
        }
      });
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
