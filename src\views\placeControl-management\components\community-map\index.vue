<!--
 * @Date: 2025-01-16 11:07:33
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-16 16:11:17
 * @FilePath: \icbd-view\src\views\juvenile\components\juvenile-map\index.vue
-->
<template>
  <div class="juvenile-map">
    <div class="map-box">
      <MapSearchPlace
        class="search-box"
        @searchHandler="searchCommunity"
        @locationMap="locationMap"
      ></MapSearchPlace>
      <mapBase
        ref="mapBase"
        :layerOverlayStatusMap="layerOverlayStatusMap"
        :mapLayerConfig="mapLayerConfig"
        :searchBtn="false"
        :juvenilePlaceList="allPlaceList"
        :layerCheckedNames="layerCheckedNames"
        :layerTypePlaceList="placeKindList"
        :isShowArchive="true"
        :goArchiveInfo="goArchiveInfo"
        @onload="mapLoad"
      />
      <PlaceList
        ref="placeKindListRef"
        class="place-dropdown"
        @changeLayerName="changeLayerName"
        :placeList="placeKindList"
        :title="'图层列表'"
      >
        <AttentionList slot="top" @changeLayerName="changeAttentionLayerName" />
      </PlaceList>
    </div>
  </div>
</template>

<script>
import mapBase from "@/views/juvenile/components/juvenile-map/mapBase.vue";
import PlaceList from "@/views/juvenile/components/juvenile-map/placeList.vue";
import MapSearchPlace from "./components/search.vue";
import AttentionList from "./components/attentionList.vue";
import { queryPlacesAndAttention } from "@/api/monographic/place.js";
import { getPlaceTypeList } from "@/api/placeArchive.js";
import { placeType } from "@/map/core/enum/LayerType.js";
export default {
  name: "CommunityMap",
  components: {
    mapBase,
    PlaceList,
    MapSearchPlace,
    AttentionList,
  },
  props: {
    alarmList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      mapLayerConfig: {
        tracing: false, // 是否需要刻画轨迹
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: false, // 框选操作栏
        selectionResult: false, // 是否显示框选结果弹框
        resultOrderIndex: false, // 搜索结果排序,
        showLatestLocation: false, // 显示地图最新位置
      },
      allPlaceList: [],
      layerCheckedNames: [],
      placeKindList: [],
      keyWords: "",
      layerOverlayStatusMap: {},
      iconMap: {},
    };
  },
  computed: {},
  methods: {
    async mapLoad() {
      await this.getPlaceKindList();
      this.renderPlaceList();
      this.$refs.placeKindListRef.initAllSelectPlace(true);
    },
    async queryPlaceList(type,pageSize = 2000) {
      let flag = false;
      let pageNumber = 0;
      const allPlaceList = [];
      while (!flag) {
        pageNumber++;
        const { data } = await queryPlacesAndAttention({
          pageNumber: pageNumber,
          pageSize: pageSize,
          type:type
        });
        const { entities = [], lastPage } = data;
        flag = lastPage;
        allPlaceList.push(...entities);
      }
      return allPlaceList;
    },
    async renderPlaceList(){
      await this.getPlaceList(1,20000);
      await this.getPlaceList(0);
      this.$refs.mapBase.addContainFeaturesListener();
    },
    async getPlaceList(type,pageSize) {
      const entities = await this.queryPlaceList(type,pageSize);
      const allPlaceList = entities.map((el) => {
        return {
          ...el,
          iconType: this.iconMap[el.firstLevel]?.icon,
          LayerType: el.firstLevel,
        };
      });
      this.$refs.mapBase.renderClusterOverlayMarkers(allPlaceList);
    },
    async getPlaceKindList() {
      const { data = [] } = await getPlaceTypeList({});
      const iconMap = {};
      data
        ?.filter((el) => el.parentId !== -1)
        ?.forEach((element) => {
          if (!iconMap[element.parentId])
            iconMap[element.parentId] = element.icon
              ? JSON.parse(element.icon)
              : "";
        });
      this.placeKindList = data
        ?.filter((el) => el.parentId === -1)
        .map((item) => {
          let icon = iconMap[item.id];
          if (icon) {
            if (icon["font_class"]) {
              icon["font_class"] = placeType[icon["font_class"]]
                ? icon["font_class"]
                : "hotel";
            }
          } else {
            icon = {
              font_class: "hotel",
              color: "#EB8A5D",
            };
          }
          this.iconMap[item.typeCode] = {
            icon: icon["font_class"] || "hotel",
          };
          return {
            key: item.typeCode,
            title: item.typeName,
            icon: icon["font_class"] || "hotel",
            color: icon["color"] || "#EB8A5D",
          };
        });
    },
    changeLayerName(value) {
      this.layerCheckedNames = value;
    },
    changeAttentionLayerName(value) {
      this.layerOverlayStatusMap = {
        attention: value,
      };
    },
    searchCommunity() {
      let param = {
        name: this.keyWords, // 场所名称
        address: "", // 场所地址
      };
    },
    /**
     * @description: 查看设备档案
     * @param {string} deviceId 设备id
     */
    goArchiveInfo(item) {
      const { href } = this.$router.resolve({
        path: "/placeControl-place-archive/place-dashboard",
        query: { archiveNo: item.id, source: "place" },
      });
      window.open(href, "_blank");
    },
    /**
     * 地图定位到选择的小区
     */
    locationMap(value) {
      try {
        const centerPoint = JSON.parse(value.centerPoint);
        // 组装参数
        let data = {
          properties: { ...value },
          center: centerPoint,
        };
        this.$refs.mapBase.aoiModelShow(data);
      } catch (e) {
        return this.$Message.warning("经纬度信息不全");
      }
    },
  },
};
</script>

<style lang="less" scoped>
.juvenile-map {
  width: 100%;
  height: 100%;
  position: relative;

  .map-box {
    width: 100%;
    height: 100%;
    position: relative;

    .place-dropdown {
      position: absolute;
      top: 10px;
      right: 0;
    }
  }
  .search-box {
    position: absolute;
    top: 10px;
    left: 5px;
    width: 300px;
    z-index: 10;
  }
}
</style>
