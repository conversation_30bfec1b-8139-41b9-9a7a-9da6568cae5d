<template>
  <ui-modal
    v-model="visible"
    title="关系挖掘配置详情"
    :r-width="500"
    class="relation-excavate-config"
    footer-hide
    @onOk="comfirmHandle"
  >
    <div class="content">
      <div class="right-box">
        <div class="title">
          <div class="title-l">关系详情</div>
        </div>
        <div class="label-box">
          <div class="label">
            关&nbsp;&nbsp;系&nbsp;&nbsp;&nbsp;名&nbsp;&nbsp;称 ：
          </div>
          {{ baseInfo.name }}
          <div class="label" style="margin-left: 30px">关系颜色 ：</div>
          <ColorPicker v-model="baseInfo.color" disabled />
        </div>
        <div class="label-box">
          <div class="label">目标实体类型 ：</div>
          {{ baseInfo.type }}
        </div>
        <div class="title">
          <div class="title-l">挖掘规则</div>
          <div class="query-box">
            <div>【 {{ modelData.startTime }} - {{ modelData.endTime }} 】</div>
            <div>
              【 {{ modelData.isIntersection == 1 ? "交集" : "并集" }} 】
            </div>
          </div>
        </div>
        <div class="rules-box">
          <div
            class="rule-type"
            v-for="(value, index) in modelData.dataForms"
            :key="index"
          >
            <div class="rule-name">
              <img src="./img/relation.png" alt="" />
              <div>{{ value.ruleName }}</div>
            </div>
            <div class="label-box label-item">
              <div class="label">时间周期：</div>
              {{
                transTimePeriodData(
                  value.ruleList[0].labelValue,
                  value.ruleList[3].labelValue
                )
              }}
              <div class="label label2">次数：</div>
              {{ value.ruleList[1].labelValue | commonFiltering(countData)
              }}{{ value.ruleList[2].labelValue }}
            </div>
            <div
              v-for="(item, index) in value.ruleList.slice(
                4,
                value.ruleList.length
              )"
              :key="index"
            >
              <div
                v-if="item.labelValue && item.labelValue.length > 0"
                class="label-box label-item"
              >
                <div class="label">{{ item.labelName }}：</div>
                <span class="label-value" v-if="item.type == 'selectCommon'"
                  >{{
                    item.labelValue
                      | commonFiltering(transDicData(item.labelKey))
                  }}
                  <span v-if="item.multiple && item.isIntersection">{{
                    item.isIntersection == "11"
                      ? "并集"
                      : item.isIntersection == "12"
                      ? "交集"
                      : ""
                  }}</span>
                </span>
                <span v-else>{{ item.labelValue }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { getRelationByNames, getEntityByNames } from "@/api/number-cube";
import request from "@/libs/request";
import { queryDataByKeyTypes } from "@/api/user";

export default {
  props: {
    excavateNodeConfigData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      baseInfo: {
        name: "",
        color: "",
        type: "",
      },
      modelData: {
        dataForms: [],
        startTime: "",
        endTime: "",
        isIntersection: "",
      },
      timePeriodData: [
        { name: "每天", value: "00" },
        { name: "每个周一至周五", value: "01" },
        { name: "每周末", value: "02" },
        { name: "每周", value: "03" },
        { name: "每月", value: "04" },
        { name: "每年", value: "05" },
        { name: "某天", value: "10" },
        { name: "某个周一至周五", value: "11" },
        { name: "某周末", value: "12" },
        { name: "某周", value: "13" },
        { name: "某月", value: "14" },
        { name: "某年", value: "15" },
      ],
      countData: [
        { dataValue: ">", dataKey: "1" },
        { dataValue: ">=", dataKey: "2" },
        { dataValue: "<", dataKey: "3" },
        { dataValue: "<=", dataKey: "4" },
        { dataValue: "=", dataKey: "5" },
        { dataValue: "!=", dataKey: "6" },
      ],
      excavateNodeConfigDataCopy: null,
      selectOptionData: {},
    };
  },
  methods: {
    //下拉数据转为字典格式数据
    transData(data) {
      let newData = data.map((item) => {
        return {
          dataKey: item.value,
          dataValue: item.name,
        };
      });
      return newData;
    },
    transDicData(labelKey) {
      return this.selectOptionData?.[labelKey] || [];
    },
    transTimePeriodData(val1, val2) {
      let name = "";
      // 时间规则拼接， 先拼 0/1 （每/某） 再拼 1-5 （时间频率）
      this.timePeriodData.forEach((item) => {
        if (item.value == val2 + "" + val1) {
          name = item.name;
        }
      });
      return name;
    },
    async init(item) {
      //   console.log(item, "item");
      this.selectOptionData = {};
      this.excavateNodeConfigDataCopy = { ...this.excavateNodeConfigData };
      this.visible = true;
      this.baseInfo.name = item.relationMiningModel.relationNameCn;
      this.baseInfo.color = item.relationMiningModel.relationColor;
      this.baseInfo.type = this.entityTypeFun(
        item.relationMiningModel.targetEntityName
      );
      this.modelData.startTime =
        item.relationMiningModel.rules[0].startEventTime;
      this.modelData.endTime = item.relationMiningModel.rules[0].endEventTime;
      this.modelData.isIntersection =
        item.relationMiningModel.rules[0].andRelationRules.length > 0
          ? "1"
          : "2";
      let rulesList = [];
      if (item.relationMiningModel.rules[0].andRelationRules.length > 0) {
        rulesList = item.relationMiningModel.rules[0].andRelationRules;
      } else {
        rulesList = item.relationMiningModel.rules[0].orRelationRules;
      }

      const relatonData = await this.getRelatonData(
        rulesList.map((item) => item.relationName)
      );
      const targetEntityData = await this.getEntityData(
        rulesList.map((item) => item.targetEntityName)
      );
      const dataForms = [];
      const selectOptionData = [];
      for (const item of rulesList) {
        const { relationName, targetEntityName, targetEntityProperties } = item;
        const { nameCn } = relatonData.find((el) => el.name === relationName);
        const targetEntity = targetEntityData.find(
          (el) => el.name === targetEntityName
        );
        const properties = targetEntity.properties.filter(
          (itm) => itm.minedable == 1
        );
        const valueProperties = targetEntityProperties.map((el) => {
          const { propertyName, values } = el;
          const ite = properties.find((o) => o.name === propertyName);
          const isSelect = ite.propertyDicType || ite.propertyDataInterface;
          if (isSelect) selectOptionData.push({ ...ite });
          return {
            labelKey: propertyName,
            labelValue: values,
            labelName: ite.nameCn,
            type: isSelect ? "selectCommon" : "input",
          };
        });
        const ruleList = [
          {
            labelKey: "timePeriod",
            labelValue: item.timePeriod,
            labelName: "时间周期",
            type: "timePeriod",
          },
          {
            labelKey: "countAlgorithm",
            labelValue: item.statisticsRuleOps,
            labelName: "次数",
            type: "selectCommon",
            selectOptionList: [
              { name: ">", value: "1" },
              { name: ">=", value: "2" },
              { name: "<", value: "3" },
              { name: "<=", value: "4" },
              { name: "=", value: "5" },
              { name: "!=", value: "6" },
            ],
          },
          {
            labelKey: "countNum",
            labelValue: item.times,
            labelName: "",
            type: "inputNumber",
            required: false,
          },
          {
            labelKey: "timePeriodComplianceFrequency",
            labelValue: item.timePeriodComplianceFrequency,
            labelName: "时间周期",
            type: "timePeriod",
          },
        ];
        dataForms.push({
          ruleName: nameCn,
          ruleList: [...ruleList, ...valueProperties],
        });
      }
      this.modelData.dataForms = dataForms;
      selectOptionData?.length > 0 &&
        this.getSelectOptionData(selectOptionData);
    },
    async getSelectOptionData(selectOptionData) {
      const propertyDicTypeSet = [];
      selectOptionData.forEach((ite) => {
        if (ite.propertyDataInterface) {
          const option = { ...ite };
          option.propertyDataInterface = JSON.parse(
            ite.propertyDataInterface || "{}"
          );
          this.setPropertyDicType(option);
        } else propertyDicTypeSet.push(ite);
      });
      propertyDicTypeSet?.length &&
        this.queryDataByKeyTypesData(propertyDicTypeSet);
    },
    // 查询通用字典
    async queryDataByKeyTypesData(propertyDicTypeSet) {
      const propertyDicTypes = propertyDicTypeSet.map(
        (ite) => ite.propertyDicType
      );
      const resp = await queryDataByKeyTypes([...new Set(propertyDicTypes)]);
      const selectTypeData = {};
      resp.data?.forEach((item) => {
        const propertyDicType = Object.keys(item)[0];
        const propertyObj = propertyDicTypeSet.find(
          (el) => el.propertyDicType === propertyDicType
        );
        const { name } = propertyObj;
        selectTypeData[name] = item[propertyDicType];
      });
      this.selectOptionData = {
        ...this.selectOptionData,
        ...selectTypeData,
      };
    },
    // 查询特殊接口下拉数据
    async setPropertyDicType(option) {
      const { parentPropertyName, propertyDataInterface, name } = option;
      let labelValue = undefined;
      if (parentPropertyName) {
        this.modelData.dataForms.forEach((el) => {
          if (!labelValue)
            labelValue = el.ruleList.find(
              (el) => el.labelKey === parentPropertyName
            )?.labelValue;
        });
      }
      const data = await this.querySelectData(
        propertyDataInterface,
        labelValue
      );
      this.selectOptionData = {
        ...this.selectOptionData,
        [name]: data,
      };
    },
    // 获取非字典，从其它接口获取的数据
    async querySelectData(propertyDataInterface, val = "") {
      const { fieldNames, url, method, params } = propertyDataInterface;
      const newParams = {};
      for (const key in params) {
        const obj = params[key];
        newParams[key] = obj === "$" ? val : obj;
      }
      const resp = await request({
        url,
        method,
        [method === "post" ? "data" : "params"]: newParams,
      });
      const data = resp?.data?.entities || resp?.data || [];
      return data.map((item) => {
        const obj = {};
        for (const key in fieldNames) {
          obj[key] = item[fieldNames[key]];
        }
        return obj;
      });
    },
    async getEntityData(names) {
      let params = {
        graphInstanceId: this.excavateNodeConfigDataCopy.graphInstanceId,
        names,
      };
      const res = await getEntityByNames(params);
      return res.data || [];
    },
    async getRelatonData(names) {
      const params = {
        graphInstanceId: this.excavateNodeConfigDataCopy.graphInstanceId,
        names,
      };
      const res = await getRelationByNames(params);
      return res.data || [];
    },
    comfirmHandle() {
      this.visible = false;
    },
    entityTypeFun(type) {
      let entityKey = "";
      let arr1 = [];
      let arr2 = [];
      //处理目标实体类型数据
      let tarNode = {};
      tarNode = JSON.parse(this.excavateNodeConfigDataCopy.diggableEntities);
      for (let key in tarNode) {
        arr1.push(key);
        arr2.push(tarNode[key]);
      }
      entityKey =
        arr2.indexOf(type) == -1 ? "normal" : arr1[arr2.indexOf(type)];
      return entityKey == "people"
        ? "人员"
        : entityKey == "vehicle"
        ? "车辆"
        : entityKey == "place"
        ? "场所"
        : entityKey == "device"
        ? "设备"
        : "";
    },
  },
};
</script>
<style lang="less" scoped>
.relation-excavate-config {
  /deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
    height: 400px;
    padding: 0;
  }
  .content {
    height: 100%;
    padding: 20px;
  }
  .right-box::-webkit-scrollbar {
    width: 0;
  }
  .right-box {
    overflow: auto;
    width: 100%;
    height: 100%;
    border: 1px solid #d3d7de;
    .title {
      padding-left: 10px;
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      .title-l {
        color: rgba(0, 0, 0, 0.9);
        font-weight: 700;
      }
      .query-box {
        display: flex;
        margin: 10px 5px 0 0;
      }
    }
    .label-box {
      margin: 10px;
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.9);
      .label {
        color: rgba(0, 0, 0, 0.4513);
      }
      /deep/ .ivu-input-wrapper {
        width: 49%;
      }
      /deep/ .ivu-color-picker {
        .ivu-input-icon,
        .ivu-icon-ios-close {
          display: none;
        }
        .ivu-color-picker-confirm {
          .ivu-color-picker-confirm-color-editable {
            right: 142px;
          }
          .ivu-input {
            height: 29px;
            width: 85px;
            border-color: #4597ff;
          }
        }
        .ivu-color-picker-color {
          left: 2px;
          top: 8px;
        }
      }
    }
    .rules-box::-webkit-scrollbar {
      width: 0;
    }
    .rules-box {
      width: 100%;
      height: calc(~"100% - 165px");
      overflow: auto;
      padding: 10px;
      .rule-type {
        width: 100%;
        background: #f9f9f9;
        padding: 10px;
        margin-bottom: 10px;
        .rule-name {
          display: flex;
          align-items: center;
          height: 30px;
          color: #3d3d3d;
          font-weight: 700;
          border-bottom: 1px solid #d3d7de;
          img {
            width: 16px;
            height: 15px;
            margin-right: 5px;
          }
        }
        .label-item {
          margin: 10px 0 0 0;
          .label2 {
            margin-left: 20px;
          }
        }
      }
    }
  }
}
</style>
