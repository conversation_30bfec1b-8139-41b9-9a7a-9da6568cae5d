<template>
  <div>
    <ui-modal
      v-model="visible"
      :title="modalTitle"
      :styles="styles"
      :footer-hide="isView"
      @query="addWorkOrder"
      @onCancel="handleReset"
    >
      <div class="model-wrapper" v-if="visible" v-scroll="modelHeight">
        <common-search
          ref="commonSearchRef"
          :default-form="defaultForm"
          :is-view="isView"
          :edit-view-action="editViewAction"
          :is-self="allMessage.workOrderType === 2"
        >
          <!-- 有关联指标的需要展示 -->
          <Form :label-width="89" inline>
            <FormItem v-if="allMessage.workOrderType === 1" label="关联指标">
              <span class="font-active-color width-slg ellipsis">{{ allMessage.indexName }}</span>
            </FormItem>
            <FormItem v-if="allMessage.workOrderType === 1" label="处理异常">
              <span class="font-active-color width-slg ellipsis" :title="allMessage.errorReason">{{
                allMessage.errorReason
              }}</span>
            </FormItem>
            <FormItem label="任务状态">
              <Select
                v-model="saveForm.finish_status"
                class="width-slg"
                filterable
                clearable
                disabled
                :placeholder="allMessage.finishStatusText"
              >
                <Option
                  v-for="(item, index) in dataStatuList"
                  :key="index"
                  :label="item.dataValue"
                  :value="item.dataKey"
                >
                </Option>
              </Select>
            </FormItem>
            <FormItem label="附件" v-if="allMessage.workOrderType === 2">
              <ui-upload
                v-model="allMessage.fileUrl"
                :disabled="isView"
                :accept="accept"
                :format="format"
                :upload-url="uploadUrl"
                :get-upload-params="getUploadParams"
              ></ui-upload>
            </FormItem>
          </Form>
        </common-search>

        <div class="wait-device-message" v-if="[1, 3].includes(allMessage.workOrderType)">
          <p class="title-p mt-lg mb-sm">关联设备信息：</p>
          <section class="message-section">
            <p>
              <span class="msg-label mr-sm">{{ global.filedEnum.deviceId }}:</span>
              <span class="msg-text font-active-color">
                {{ allMessage.deviceId }}
              </span>
            </p>
            <p>
              <span class="msg-label mr-sm">{{ global.filedEnum.deviceName }}:</span>
              <span class="msg-text">
                {{ allMessage.deviceName }}
              </span>
            </p>
            <!-- <p>
            <span class="msg-label mr-sm">IP地址:</span>
            <span class="msg-text">
              {{ allMessage.orgName }}
            </span>
          </p>
          <p>
            <span class="msg-label mr-sm">所属项目:</span>
            <span class="msg-text">
              {{ allMessage.cjdw }}
            </span>
          </p> -->
            <p>
              <span class="msg-label mr-sm">所属单位:</span>
              <span class="msg-text">
                {{ allMessage.orgName }}
              </span>
            </p>
            <p>
              <span class="msg-label mr-sm">承建单位:</span>
              <span class="msg-text">
                {{ allMessage.cjdw }}
              </span>
            </p>
            <p>
              <span class="msg-label mr-sm">维护单位:</span>
              <span class="msg-text">
                {{ allMessage.whdw }}
              </span>
            </p>
            <p class="flex-aic">
              <span class="msg-label mr-sm">异常原因:</span>
              <span class="msg-text w400-ellipsis" :title="allMessage?.errorMessage">
                {{ allMessage.errorMessage }}
              </span>
              <Button
                type="text"
                class="ml-sm"
                @click="handleAbnormalDetail({ entryType: 'deviceInfo' })"
                v-if="
                  abnormalDetailIndexId.includes(allMessage.indexId) && allMessage?.errorMessage && allMessage.batchId
                "
                >异常详情</Button
              >
            </p>
          </section>
        </div>
        <template v-if="isView">
          <!-- 已完成的结果才展示处理结果 -->
          <div v-if="!!handleResult.name">
            <p class="title-p mt-lg mb-sm">最新处理结果：</p>
            <section class="message-section">
              <p>
                <span class="msg-label mr-sm">处理人:</span>
                <span class="msg-text font-active-color">
                  {{ handleResult.name }}
                </span>
              </p>
              <p>
                <span class="msg-label mr-sm">处理结果:</span>
                <span class="msg-text">
                  {{ handleResult.result }}
                </span>
              </p>
              <p>
                <span class="msg-label mr-sm">处理说明:</span>
                <span class="msg-text">
                  {{ handleResult.content }}
                </span>
              </p>
            </section>
          </div>
          <p class="title-p mt-lg mb-sm">操作记录</p>
          <ui-table
            class="ui-table auto-fill"
            :table-columns="logTableColumns"
            :table-data="handleLogList"
            :minus-height="minusTable"
            :loading="loading"
          >
            <template #operationContent="{ row }">
              <span v-if="row.operationType === '7' && row.checkStatus === '1'" class="color-success">{{
                row.operationContent
              }}</span>
              <span v-else-if="row.operationType === '7' && row.checkStatus === '2'" class="flex-aic">
                <span class="color-warning ellipsis maxwidth-250 inline" :title="row.operationContent">{{
                  row.operationContent
                }}</span>
                <Button
                  v-if="abnormalDetailIndexId.includes(editViewAction.row.indexId)"
                  type="text"
                  class="ml-sm"
                  @click="handleAbnormalDetail({ entryType: 'table', ...row }, row)"
                  >异常详情</Button
                >
              </span>
              <span v-else>{{ row.operationContent }}</span>
            </template>
          </ui-table>
        </template>
        <loading v-if="allLoading"></loading>
      </div>
    </ui-modal>
    <abnomal-details
      v-if="showAbnomalDetails"
      :showAbnomalDetails="showAbnomalDetails"
      ref="abnormalRef"
      :edit-view-action="editViewAction"
      :tableData="tableData"
      :abnormalDetailInfo="abnormalDetailInfo"
      :invokeType="entryType"
      @changeVisible="changeVisible"
    ></abnomal-details>
  </div>
</template>
<script>
import governancetask from '@/config/api/governancetask';
import user from '@/config/api/user';
import unified from '@/config/api/unified.js';
import { supportVideoIndexIds, supportFaceIndexIds, supportCarIndexIds } from './abnomal-details/enumFeild.js';
let reasonTableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '异常类型', key: 'errorTypeText', align: 'left' },
  { title: '异常原因', key: 'errorReason', align: 'left', tooltip: true },
  { title: '异常描述', slot: 'errorDescription', align: 'left' },
];
let logTableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '操作时间', key: 'createTime', align: 'left', width: 180 },
  { title: '操作人', key: 'senderName', align: 'left', width: 120 },
  { title: '所属组织', key: 'senderUnit', align: 'left', minWidth: 100, tooltip: true }, // yyh
  { title: '操作类型', key: 'operationTypeText', align: 'left', width: 120 },
  { title: '操作内容', slot: 'operationContent', key: 'operationContent', align: 'left', minWidth: 220, tooltip: true },
];

export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    editViewAction: {
      default: () => {
        return {
          type: 'edit',
          row: {},
        };
      },
    },
  },
  data() {
    return {
      handleResult: {
        name: '',
        result: '',
        content: '',
      },
      defaultForm: {
        workOrderNum: null,
        workLevel: null,
        workOrderName: null,
        taskPlannedDate: '',
        receiverName: '',
        receiverId: '',
        taskContent: '',
      },
      saveForm: {
        finish_status: '',
      },
      reasonTableColumns: Object.freeze(reasonTableColumns),
      logTableColumns: Object.freeze(logTableColumns),
      tableData: [{}, {}, {}],
      chooseTableData: [],
      visible: false,
      styles: {
        width: '1150px',
        // top: "0.3rem",
      },
      loading: false,
      allLoading: false,
      minusTable: 680,
      allMessage: {
        workOrderType: 1,
        taskPlannedDate: '',
        taskContent: '',
        finishStatus: '',
        deviceId: '',
        deviceName: '',
        area: '',
        longitude: '',
        latitude: '',
        receiverIdSolve: '',
        finishResult: '',
        errorReason: '',
      },
      reasonTableData: [],
      handleLogList: [],
      operationType: {},
      dataStatuList: [],
      ruleValidate: {
        taskPlannedDate: [
          {
            type: 'date',
            required: true,
            message: '请选择任务截止时间',
            trigger: 'change',
          },
        ],
        taskContent: [
          {
            required: true,
            message: '请填写任务说明',
            trigger: 'blur',
          },
        ],
      },
      indexLoading: false,
      uploadUrl: unified.fileUpload,
      format: ['doc', 'docx', 'xlsx', 'bmp', 'jpg', 'png', 'JPEG', 'zip', '7z'],
      accept: '.bmp, .jpeg,.jpg,.png,.zip,.7z,.doc,.docx,.xlsx',
      // 异常详情
      abnormalDetailIndexId: [...supportVideoIndexIds, ...supportFaceIndexIds, ...supportCarIndexIds], //可查看异常详情的指标id
      showAbnomalDetails: false,
      abnormalDetailInfo: {},
      entryType: '',
    };
  },
  async activated() {
    this.saveForm = {
      finish_status: '',
    };
    this.dataStatuList = await this.initSource('DETECTION_WORK_ORDER_STATUS');
    this.copySearchDataMx(this.dataStatuList);
  },
  methods: {
    getUploadParams(uploadData) {
      let uploadParams = [];
      uploadData.forEach((row) => {
        uploadParams.push(row.url || row.response.data.fileUrl);
      });
      return uploadParams.join(',');
    },
    async addWorkOrder() {
      // let valid = await this.$refs['formValidate'].validate()
      // if (!valid) {
      //   this.$Message.error('请将信息填写完整!')
      //   return false
      // }
      let commonSearchParams = this.$refs.commonSearchRef.searchForm;
      const params = {
        finish_status: this.saveForm.finish_status,
        taskPlannedDate: commonSearchParams.taskPlannedDate,
        taskContent: commonSearchParams.taskContent,
        orgCode: this.allMessage.orgCode,
        id: this.editViewAction.row.id,
        receiverId: commonSearchParams.receiverId,
        workLevel: commonSearchParams.workLevel,
        fileUrl: this.allMessage.fileUrl,
      };
      try {
        let { data } = await this.$http.put(governancetask.updateWorkOrder, params);
        this.$Message.success(data.msg);
        this.$emit('updateOrders');
      } catch (err) {
        console.log(err);
      }
    },
    handleNewResult() {
      if (this.handleLogList.length) {
        let Index = this.handleLogList.findIndex((item) => {
          return item.operationType === '6';
        });
        if (Index !== -1) {
          this.handleResult = {
            name: this.handleLogList[Index].senderName,
            result: this.handleLogList[Index].finishResult,
            content: this.handleLogList[Index].finishSituation,
          };
        }
      }
    },
    async getRecordMessage() {
      this.allLoading = true;
      try {
        let {
          data: { data },
        } = await this.$http.get(`${governancetask.recordMessage}/${this.editViewAction.row.id}`);
        this.allMessage = data;
        this.handleLogList = data.tvideoDeviceFlowVos || [];
        this.handleNewResult();
        this.reasonTableData = [this.allMessage];
        this.saveForm = {
          finish_status: `${data.finishStatus}`,
        };
        this.defaultForm = {
          workOrderNum: data.workOrderNum,
          workOrderName: data.workOrderName,
          taskPlannedDate: data.taskPlannedDate,
          receiverName: data.receiverName,
          receiverId: data.receiverId,
          taskContent: data.taskContent,
          workLevel: data.workLevel,
        };
      } catch (error) {
        console.log(error);
      } finally {
        this.allLoading = false;
      }
    },
    async initSource(typekey) {
      try {
        let res = await this.$http.get(user.queryByTypeKey, {
          params: { typekey: typekey },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    handleReset() {
      this.visible = false;
      this.showAbnomalDetails = false;
    },
    //查看异常详情
    handleAbnormalDetail(obj, row) {
      this.showAbnomalDetails = true;
      this.abnormalDetailInfo = row || {};
      this.entryType = obj.entryType;
    },
    changeVisible(val) {
      this.showAbnomalDetails = val;
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.getRecordMessage();
      } else {
        this.handleResult = {
          name: '',
          result: '',
          content: '',
        };
      }
    },
  },
  computed: {
    modelHeight() {
      if (this.isView) {
        return 180;
      }
      if (this.editViewAction.row.workOrderType == 2) {
        return 600;
      }
      return 400;
    },
    isView() {
      return this.editViewAction.type === 'view';
    },
    modalTitle() {
      if (this.isView) {
        return '查看工单';
      } else {
        return '编辑工单';
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CommonSearch: require('./common-search.vue').default,
    UiUpload: require('@/components/ui-upload.vue').default,
    AbnomalDetails: require('./abnomal-details').default,
  },
};
</script>
<style lang="less" scoped>
.title-p {
  color: var(--color-display-title);
}
@{_deep} .ivu-modal-body {
  padding: 20px 50px 0 50px;
}
.model-wrapper {
  @{_deep}.width-slg {
    width: 400px;
  }
  .special-label {
    width: 480px;
  }
  .flex {
    display: flex;
  }
  .content-title {
    height: 40px;
    line-height: 40px;
  }
  .content-box {
    display: flex;
    border: 1px solid var(--border-color);
  }
  .top-content {
    display: flex;
    .picker-end {
      width: 350px;
    }
    .desc {
      width: 500px;
      @{_deep}textarea.ivu-input {
        height: 60px;
      }
    }
  }

  .message-section {
    color: var(--color-content);
    position: relative;
    > p {
      height: 30px;
      line-height: 30px;
    }
    .msg-label {
      color: var(--color-label);
      width: 80px;
      text-align: right;
      display: inline-block;
    }
    .tagging {
      position: absolute;
      right: 200px;
      top: 0px;
    }
  }
  .wait-device-message {
    position: relative;
  }
  .left-content {
    width: 350px;
    padding: 10px;
  }
  .middle-content,
  .right-content {
    width: 715px;
    .choose-box {
      display: flex;
      justify-content: space-between;
      height: 50px;
      line-height: 50px;
      padding: 0 25px 0 10px;
      color: #fff;
    }
  }
  .middle-content {
    padding: 10px;
    border-left: 1px solid var(--border-color);
    border-right: 1px solid var(--border-color);
    .search-box {
      display: flex;
      justify-content: space-between;
    }
    .select-div {
      display: flex;
      .select-span {
        display: inline-block;
        height: 45px;
        line-height: 45px;
        color: #fff;
        font-size: 14px;
      }
      .tabs {
        flex: 1;
      }
    }
  }
  .w400-ellipsis {
    display: inline-block;
    max-width: 400px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .maxwidth-250 {
    max-width: 250px;
  }
}
.tipvisible {
  span {
    font-size: 18px;
    color: var(--color-primary);
  }
  cursor: pointer;
}
.text_hid {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
