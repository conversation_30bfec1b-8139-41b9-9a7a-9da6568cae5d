<template>
  <ui-modal class="details" v-model="visible" :title="`${areaData.parentName}-区县详情`" :styles="styles" footer-hide>
    <div class="mb-lg">
      <ui-label class="inline mr-lg" label="达标情况">
        <Select class="width-md" v-model="searchData.status" placeholder="请选择达标情况" clearable>
          <Option v-for="(item, index) in reachStandardList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #status="{ row }">
        <span class="check-status" :class="row.status === '1' ? 'bg-success' : row.status === '2' ? 'bg-failed' : ''">
          {{ row.status === '2' ? '不合格' : row.status === '1' ? '合格' : '--' }}
        </span>
      </template>
      <template #reportAndUnQualified="{ row }">
        <span class="font-report" @click="deviceDetails(row, '1', '2')">
          {{ row.reportAndUnQualified }}
        </span>
      </template>
      <template #reportAndQualified="{ row }">
        <span
          :class="['font-report', row.status === '2' ? 'font-report-error' : '']"
          @click="deviceDetails(row, '1', '1')"
        >
          {{ row.reportAndQualified }}
        </span>
      </template>
      <template #unReportAndQualified="{ row }">
        <span
          :class="['font-report', row.unReportAndQualified > 0 ? 'font-report-success' : '']"
          @click="deviceDetails(row, '0', '1')"
        >
          {{ row.unReportAndQualified }}
        </span>
      </template>
      <template #action="{ row }">
        <div>
          <ui-btn-tip
            class="mr-md"
            :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
            icon="icon-shebeimingxi"
            content="设备明细"
            @handleClick="deviceDetails(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="mr-md"
            :styles="{ color: 'var(--color-primary)', 'font-size': '14px' }"
            icon="icon-piliangshangbao"
            content="一键上报"
            @handleClick="report(row)"
          ></ui-btn-tip>
        </div>
      </template>
    </ui-table>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {
      type: Boolean,
    },
    areaData: {
      type: Object,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.5rem',
      },
      loading: false,
      reachStandardList: [
        {
          dataKey: '2',
          dataValue: '不达标',
        },
        {
          dataKey: '1',
          dataValue: '达标',
        },
      ],
      searchData: {
        status: '',
      },
      tableData: [],
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
        {
          width: 100,
          key: 'name',
          title: '行政区划',
        },
        {
          minWidth: 100,
          title: `达标数量`,
          key: 'configQualifiedNum',
        },
        {
          minWidth: 100,
          title: `已上报质量合格`,
          slot: 'reportAndQualified',
        },
        {
          minWidth: 100,
          title: `已上报质量不合格`,
          slot: 'reportAndUnQualified',
        },
        {
          minWidth: 100,
          title: `未上报质量合格`,
          slot: 'unReportAndQualified',
        },
        {
          minWidth: 100,
          title: `达标情况`,
          slot: 'status',
        },
        {
          width: 140,
          title: '操作',
          slot: 'action',
        },
      ],
    };
  },
  created() {},
  methods: {
    search() {
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(equipmentassets.qualityCivilCodeList, {
          parentCode: this.areaData.parentCode,
          sbgnlx: this.areaData.sbgnlx,
          sbdwlx: this.areaData.sbdwlx,
          status: this.searchData.status,
          isDetail: '1',
        });
        this.tableData = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    reset() {
      this.searchData.status = '';
      this.init();
    },
    deviceDetails(row, cascadeReportStatus = '', qualityStatus = '') {
      this.$emit('showDetails', row, cascadeReportStatus, qualityStatus);
    },
    report(row) {
      this.$emit('report', row);
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.searchData.status = this.areaData.status;
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.details {
  @{_deep} .ivu-modal-body {
    height: 830px;
    display: flex;
    flex-direction: column;
  }
}
.font-report {
  color: var(--color-content);
  text-decoration: underline;
  cursor: pointer;
  &.font-report-success {
    color: var(--color-success);
  }
  &.font-report-error {
    color: var(--color-warning);
  }
}
</style>
