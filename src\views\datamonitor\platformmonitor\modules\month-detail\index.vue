<template>
  <div class="month-detail-container auto-fill">
    <div class="detail-title flex-row mb-sm">
      <span class="f-16 color-title">{{ routerData?.name }}月{{ computedMonitorType }}抓拍情况</span>
      <DatePicker
        type="month"
        format="yyyy-MM"
        placeholder="请选择统计月份"
        v-model="searchData.time"
        :options="DISABLED_AFTER_NOW"
        @on-change="changeSearchTime"
      >
      </DatePicker>
    </div>
    <div class="content-container auto-fill simple-collapse-box">
      <!-- 日抓拍趋势 -->
      <Collapse v-model="collapseValue" simple @on-change="onChangeCollapse">
        <Panel name="1" class="mb-md">
          <div class="collapse-title inline">日抓拍趋势</div>
          <div slot="content" class="day-collapse-content">
            <div class="month-cap-box base-text-color f-14">
              月抓拍总量：<span class="font-blue">{{ requestObj?.captureNumCount }}</span>
            </div>
            <line-chart
              ref="lineChart"
              :cupture-month-or-day="chartsAttrs.cuptureMonthOrDay"
              :year="chartsAttrs.year"
              :echarts-loading="tableAttrs.tableLoading"
              :tendency-echart-data="requestObj.dataBoList"
            ></line-chart>
          </div>
        </Panel>
      </Collapse>
      <!-- 日抓拍明细 -->
      <div class="day-capture-detail auto-fill">
        <div class="detail-head flex-row mb-sm">
          <div class="collapse-title inline">日抓拍明细</div>
          <Button :loading="tableAttrs.tableExportLoading" type="primary" @click="handleTableExport">
            <i class="icon-font icon-daochu f-12"></i>
            <span class="vt-middle ml-sm">导出</span>
          </Button>
        </div>
        <ui-table
          class="ui-table auto-fill"
          :loading="tableAttrs.tableLoading"
          :table-columns="tableAttrs.tableColumns"
          :table-data="requestObj.dataBoList"
        >
          <template #sum="{ row }">
            <span class="link" @click="tableItemClick({ row }, 'viewdayCatchNum')">
              {{ row.sum }}
            </span>
          </template>
          <template #durationWithoutTime="{ row }">
            <span class="link text-error" @click="tableItemClick({ row }, 'viewdayCatchNum')">
              {{ row.durationWithoutTime }}
            </span>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 日抓拍弹窗 -->
    <day-capture-modal
      v-model="dayCaptureVisible"
      :modal-row-data="showModalRow"
      :common-search-data="routerData"
    ></day-capture-modal>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import datamonitorApi from '@/config/api/datamonitor';
import { MONITORTYPE_FACE, DISABLED_AFTER_NOW } from '../../utils/enum';
import { monthTableColumns } from './enum';
export default {
  name: 'monitormonthdetail',
  mixins: [dealWatch],
  props: {},
  data() {
    return {
      DISABLED_AFTER_NOW,
      collapseValue: '1',
      searchData: {
        time: '',
      },
      tableAttrs: {
        //表格相关属性
        tableLoading: false,
        tableColumns: monthTableColumns,
        tableExportLoading: false,
      },
      chartsAttrs: {
        cuptureMonthOrDay: 'month',
        year: '2023-12-01',
        echartsLoading: false,
        tendencyEchartData: [],
      },
      routerData: {}, //页面进来的参数
      requestObj: {
        dataBoList: [],
        captureNumCount: 0,
      }, //请求获取的对象，包含统计以及列表等信息
      showModalRow: {},
      dayCaptureVisible: false,
    };
  },
  computed: {
    computedMonitorType() {
      return this.routerData.activeMoinitorType === MONITORTYPE_FACE ? '人脸' : '车辆';
    },
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        if (this.$route.query.componentName == 'monthDetail') {
          this.receiveParams(this.$route.query);
        }
      },
      { immediate: true },
    );
  },
  methods: {
    async handleTableExport() {
      try {
        this.tableAttrs.tableExportLoading = true;
        const params = {
          regionCode: this.routerData.regionCode,
          time: this.$util.common.formatDate(this.searchData.time, 'yyyyMM'),
          type: this.routerData.activeMoinitorType,
        };
        const res = await this.$http.post(datamonitorApi.exportCaptureNumByMonthDetail, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.tableAttrs.tableExportLoading = false;
      }
    },
    //接收参数
    receiveParams(params) {
      let reg = /^(\d{4})(\d{2})$/;
      this.searchData.time = params.time.replace(reg, '$1-$2');
      this.routerData = { ...params };
      this.getMonthDetailData();
    },
    //切换月份
    changeSearchTime() {
      this.getMonthDetailData();
    },
    //获取数据
    async getMonthDetailData() {
      try {
        this.tableAttrs.tableLoading = true;
        const params = {
          regionCode: this.routerData.regionCode,
          time: this.$util.common.formatDate(this.searchData.time, 'yyyyMM'),
          type: this.routerData.activeMoinitorType,
        };
        const {
          data: { data },
        } = await this.$http.get(datamonitorApi.getCaptureNumByMonthDetail, { params });
        this.requestObj = data;
        if (data.dataBoList?.length) {
          this.chartsAttrs.year = data.dataBoList[0].day?.replaceAll('/', '-');
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.tableAttrs.tableLoading = false;
      }
    },
    //表格点击事件
    tableItemClick({ row }, handlerType = null) {
      if (!handlerType) {
        console.error('未添加操作类型');
        return;
      }
      this.showModalRow = { ...row, showDayTime: row.day };
      switch (handlerType) {
        case 'viewdayCatchNum':
          this.dayCaptureVisible = true;
          break;
      }
    },
    onChangeCollapse(keys) {
      if (keys.includes('1')) {
        this.$refs.lineChart.resizeEchart();
      }
    },
    // 设置列的点击事件
    setTableColumn() {
      const clickFunc = {
        sum: ({ row }) => {
          this.tableItemClick({ row }, 'viewdayCatchNum');
        },
        durationWithoutTime: ({ row }) => {
          this.tableItemClick({ row }, 'viewdayCatchNum');
        },
      };
      this.tableAttrs.tableColumns = monthTableColumns(clickFunc);
    },
  },
  mounted() {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    LineChart: require('../../components/LineChart.vue').default,
    dayCaptureModal: require('../../components/day-capture-modal.vue').default,
  },
};
</script>
<style lang="less" scoped>
.month-detail-container {
  padding-bottom: 20px;
  .detail-title {
    height: 58px;
    border-bottom: 1px solid var(--devider-line);
    padding: 0 20px;
    .color-title {
      color: var(--color-title);
    }
  }
  .content-container {
    .collapse-title {
      margin-left: 15px;
      position: relative;
      color: var(--color-display-title);
      font-size: 16px;
      line-height: 16px;
      &::before {
        content: '';
        position: absolute;
        left: -9px;
        height: 15px;
        width: 4px;
        background-color: var(--color-display-title-before);
      }
    }
    .day-collapse-content {
      position: relative;
      .month-cap-box {
        position: absolute;
        top: 10px;
        right: 20px;
      }
    }
    @{_deep} .ivu-collapse {
      background: transparent;
      border: none;
      .ivu-collapse-item {
        border: none;
        .ivu-collapse-header {
          border: none;
          color: var(--color-title);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          background-color: var(--bg-collapse-item);
          height: 50px;
          line-height: 50px;
          margin: 0 20px;
          .title {
            font-size: 16px;
            font-weight: 900;
          }
          .switch {
            top: 50%;
            transform: translateY(-50%);
            margin-right: 20px;
          }
          i {
            float: right;
            color: #fff;
            line-height: 50px;
            font-size: 20px;
          }
        }
        .ivu-collapse-content {
          padding: 0;
          background: var(--bg-content);
          .ivu-collapse-content-box {
            padding: 10px 20px;
          }
        }
        &.ivu-collapse-item-active {
          background: var(--bg-content);
        }
      }
    }
    .day-capture-detail {
      padding: 0 20px;
      .detail-head {
        padding: 0 15px;
        background: var(--bg-collapse-item);
        height: 50px;
      }
    }
  }
}
</style>
