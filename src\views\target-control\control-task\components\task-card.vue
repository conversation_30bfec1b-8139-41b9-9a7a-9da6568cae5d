<template>
  <div class="task">
    <div class="taskname">
      <div class="name" :title="taskInfo.taskName">{{ taskInfo.taskName }}</div>
      <div class="num" @click.stop="goAlarm()">
        <!-- <ui-btn-tip class="collection-icon" icon="icon-jingqing" transfer content="告警" /> -->
        <i class="iconfont icon-jingqing range"></i>
        <!-- {{ taskInfo.alarmCount }} -->
      </div>
    </div>
    <div class="info">
      <div class="left">
        <!-- <div class="p" v-for="item in 6">
                <div class="title">布控目标：</div>
                <div class="val">布控人员</div>
                </div> -->
        <div class="p">
          <div class="title">布控类型</div>
          <span class="colon">:</span>
          <div
            class="val"
            :title="taskInfo.taskType == 2 ? taskInfo.libs : null"
          >
            {{ taskInfo.taskType == 1 ? "单体布控" : taskInfo.libs }}
          </div>
          <!-- <div class="val">{{ taskInfo.scopeType == 1 ? '所有' : '自定义' }}</div> -->
        </div>
        <div class="p" v-if="taskInfo.taskTimeType == 0">
          <div class="title">时间类型</div>
          <span class="colon">:</span>
          <div class="val">
            {{ taskInfo.taskTimeType == 1 ? "自定义" : "永久" }}
          </div>
        </div>
        <div class="p" v-if="taskInfo.taskTimeType == 1">
          <div class="title">开始时间</div>
          <span class="colon">:</span>
          <div class="val">{{ taskInfo.taskTimeB }}</div>
        </div>
        <div class="p" v-if="taskInfo.taskTimeType == 1">
          <div class="title">结束时间</div>
          <span class="colon">:</span>
          <div class="val">{{ taskInfo.taskTimeE }}</div>
        </div>
        <div class="p">
          <div class="title">布控级别</div>
          <span class="colon">:</span>
          <div class="val" :class="'level' + taskInfo.taskLevel">
            {{
              taskInfo.taskLevel == 1
                ? "一级"
                : taskInfo.taskLevel == 2
                ? "二级"
                : "三级"
            }}
          </div>
        </div>
        <div class="p">
          <div class="title">创建人</div>
          <span class="colon">:</span>
          <div class="val">{{ taskInfo.creator }}</div>
        </div>
        <div class="p">
          <div class="title">创建时间</div>
          <span class="colon">:</span>
          <div class="val">{{ taskInfo.createTime }}</div>
        </div>
      </div>
      <div class="right">
        <ui-image
          v-if="taskInfo.taskStatus == 2"
          class="img"
          :src="alarm"
        ></ui-image>
        <img v-else src="@/assets/img/target/not.png" alt="" srcset="" />
        <!-- <ui-image v-else class="img" :src="not"></ui-image> -->
        <!-- <div :class="{'status': taskInfo.taskStatus == 1}">{{ taskInfo.taskStatus == 1 ? '执行中' : 
                        taskInfo.taskStatus == 2 ? '停用' : 
                        taskInfo.taskStatus == 3 ? '布控到期' : 
                        taskInfo.taskStatus == 4 ? '已删除' : '未开始'
                    }}</div> -->
        <div :class="{ status: taskInfo.taskStatus == 1 }">
          {{
            taskInfo.taskStatus == 0
              ? "未提交"
              : taskInfo.taskStatus == 1
              ? "待审核"
              : taskInfo.taskStatus == 2
              ? "执行中"
              : taskInfo.taskStatus == 3
              ? "驳回"
              : taskInfo.taskStatus == 4
              ? "已删除"
              : "布控到期"
          }}
        </div>
      </div>
    </div>
    <div class="btn">
      <div
        :class="{ grey: [1, 2, 4, 5].includes(taskInfo.taskStatus) }"
        @click.stop="handleEdit"
      >
        编辑<span></span>
      </div>
      <!-- <div v-if="taskInfo.taskStatus == 1" @click.stop="handleStatus(2)">
        停用<span></span>
      </div>
      <div
        :class="{ grey: taskInfo.taskStatus == 3 || taskInfo.taskStatus == 5 }"
        v-else
        @click.stop="handleStatus(1)"
      >
        启用<span></span>
      </div> -->
      <div v-if="taskInfo.taskStatus == 2" @click.stop="handleRemove">
        撤控待办<span></span>
      </div>
      <div
        :class="{ grey: [1, 2, 4, 5].includes(taskInfo.taskStatus) }"
        @click.stop="handleDele"
      >
        删除
      </div>
    </div>
  </div>
</template>
<script>
import alarm from "@/assets/img/target/alarm.gif";
import round from "@/assets/img/target/round.png";
import not from "@/assets/img/target/not.png";
export default {
  props: {
    taskInfo: {
      type: Object,
      default: () => {},
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      single: false,
      round,
      alarm,
      not,
    };
  },
  computed: {},
  activated() {},
  mounted() {},
  methods: {
    collection() {},
    //   编辑
    handleEdit() {
      if ([1, 2, 4, 5].includes(this.taskInfo.taskStatus)) return;
      this.$emit("edit");
    },
    // 删除
    handleDele() {
      if ([1, 2, 4, 5].includes(this.taskInfo.taskStatus)) return;
      this.$emit("dele", this.taskInfo);
    },
    // 删除
    handleStatus(num) {
      if (this.taskInfo.taskStatus == 3 || this.taskInfo.taskStatus == 5)
        return;
      this.$emit("status", num, this.taskInfo);
    },
    // 撤控待办
    handleRemove() {
      this.$emit("remove", this.taskInfo);
    },
    goAlarm() {
      this.$router.push({
        name: "alarm-manager",
        query: {
          taskId: this.taskInfo.taskId,
          // 多维布控，在报警中没有对应的tab，直接跳到人员报警
          compareType: this.compareType === 7 ? 1 : this.compareType,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.task {
  position: relative;
  width: 340px;
  height: 190px;
  box-shadow: 0 1px 3px #d9d9d9;
  overflow: hidden;
  border-radius: 3px;
  border: 1px solid #ededed;
  background: #f9f9f9;
  .taskname {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .name {
      color: #2c86f8;
      font-weight: 600;
      font-size: 16px;
      margin-top: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;
    }
    .num {
      background: linear-gradient(-90deg, #ea4a36 0%, #ff7d56 100%);
      width: 36px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      font-size: 12px;
      color: #fff;
      margin-right: -10px;
      border-bottom-left-radius: 50px;
      border-top-left-radius: 50px;
      margin-top: 16px;
      cursor: pointer;
    }
  }
  .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
    .level {
      position: absolute;
      left: 50%;
      margin-left: -46px;
    }
  }

  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .block {
      position: relative;
      width: 100px;
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
    }
    .border {
      border: 1px solid #ebebeb;
    }
  }

  .info {
    display: flex;
    padding: 12px;
    .left {
      flex: 1;
      width: 0;
      .p {
        display: flex;
        line-height: 26px;
        .title {
          color: rgba(0, 0, 0, 0.6);
          width: 60px;
          font-size: 14px;
          text-align-last: justify;
          text-align: justify;
        }
        .colon {
          color: #999;
        }
        .val {
          flex: 1;
          color: rgba(0, 0, 0, 0.9);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 10px;
        }
        .level1 {
          color: #ea4a36;
        }
        .level2 {
          color: #f29f4c;
        }
        .level3 {
          color: #48baff;
        }
      }
    }
    .right {
      width: 92px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .img {
        width: 92px;
        height: 88px;
        margin-bottom: 6px;
        /deep/ .ui-image-div {
          border: 0;
          background: transparent;
        }
      }
      .status {
        color: #ea4a36;
      }
    }
  }

  .btn {
    position: absolute;
    width: 100%;
    background: #2c86f8;
    color: #fff;
    display: flex;
    text-align: center;
    justify-content: center;
    height: 30px;
    line-height: 30px;
    bottom: -30px;
    transition: 0.3s;
    cursor: pointer;
    div {
      position: relative;
      flex: 1;
      span {
        display: inline-block;
        width: 2px;
        height: 20px;
        border-right: 1px solid #d1cbcb;
        position: absolute;
        right: 1px;
        top: 5px;
      }
    }
  }

  &:hover {
    border: 1px solid #2c86f8;
    .btn {
      bottom: 0;
      transition: 0.3s;
    }
  }
}

.grey {
  color: #c7c7c7;
}
</style>
