<template>
  <div class="group-tree">
    <ui-loading v-if="loading" />
    <div class="search-top">
      <Input
        placeholder="请输入"
        v-model="searchInput"
        @keydown.enter.native="searchName"
      >
        <Icon
          type="ios-search"
          class="font-16 cursor-p"
          slot="suffix"
          maxlength="50"
          @click.prevent="searchName"
        />
      </Input>
      <i
        class="iconfont icon-jia auxiliary-color ml-15 add-group"
        @click="addGroup"
      ></i>
    </div>

    <xn-tree
      class="deviceTree"
      :ref="'tree'"
      :option="option"
      :label="labelFn"
      :fileOpe="fileOpe"
      @dblclickNode="dblclickNode"
      @startInspect="startInspect"
      @parseInspect="parseInspect"
      @stopInspect="stopInspect"
      @handleEdit="handleEdit"
      @handleDel="handleDel"
      @handleShare="handleShare"
    ></xn-tree>

    <!-- 新增/编辑 -->
    <addModal ref="addModal" @refreshDataList="searchName" />

    <!-- 轮询 -->
    <inspectModal
      ref="inspectModal"
      :checkedIndexs="checkedIndexs"
      @inspectStart="inspectStart"
    />
    <!-- 选择用户  只能查询所属组织下的用户-->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
      :orgin="'powerInner'"
    ></select-user>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import addModal from "./add-modals.vue";
import inspectModal from "./inspect-modal.vue";
import {
  queryMyVideoGroupList,
  delMyVideoGroup,
  queryVideoDeviceGroupList,
  removeVideoFromGroup,
  shareGroup,
} from "@/api/player";
import { copyText } from "@/util/modules/common";
import inspectMixin from "../mixins/inspect-mixin";
import xnTree from "@/components/xn-tree/index.vue";
import SelectUser from "@/components/select-modal/select-user.vue";

export default {
  components: {
    addModal,
    inspectModal,
    xnTree,
    SelectUser,
  },
  data() {
    return {
      searchInput: "",
      loading: false,
      firstInit: true,
      treeData: [],
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: null, //radio,null,checkbox
        canMove: false,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            return new Promise((resolve) => {
              this.loading = true;
              queryVideoDeviceGroupList({ groupId: node.id }).then((res) => {
                let deviceList = res.data
                  ? res.data.map((v) => {
                      v.id = v.deviceId;
                      v.label = v.deviceName;
                      (v.isLeaf = true),
                        (v.ptzType = v.deviceChildType
                          ? v.deviceChildType
                          : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
                      return v;
                    })
                  : [];
                this.loading = false;
                resolve(deviceList);
              });
            });
          },
        },
      },
      fileOpe: [
        {
          label: "查看视频",
          show: this.$_has(["video-realTime"]),
          clickFn: (item) => {
            this.dblclickNode(item);
          },
        },
        {
          label: "历史录像",
          show: this.$_has(["video-history"]),
          clickFn: (item) => {
            this.dblclickNode(item, "vod");
          },
        },
        {
          label: "查看档案",
          show: true,
          clickFn: (item) => {
            const { href } = this.$router.resolve({
              name: "device-archive",
              query: { archiveNo: item.deviceId },
            });
            window.open(href, "_blank");
          },
        },
        {
          label: "移出分组",
          show: true,
          clickFn: (item) => {
            this.$Modal.confirm({
              title: "提示",
              closable: true,
              content: `确定移出分组吗？`,
              onOk: () => {
                let parentNode = item.$pId
                  ? this.$refs.tree.xnTree.getNodeById(item.$pId)
                  : {};
                removeVideoFromGroup({
                  deviceId: item.deviceId,
                  groupId: parentNode.id,
                }).then((res) => {
                  if (res.code == 200) {
                    this.$refs.tree.xnTree.deleteNode(item.deviceId);
                    this.$Message.success("移出成功");
                  } else {
                    this.$Message.error(res.msg);
                  }
                });
              },
            });
          },
        },
        {
          label: "复制名称",
          show: true,
          clickFn: (item) => {
            copyText(item.deviceName);
          },
        },
        // ,{
        //   label: '发送工单',
        //   show: true,
        //   clickFn: (item) => {

        //   }
        // },{
        //   label: '查询工单',
        //   show: true,
        //   clickFn: (item) => {

        //   }
        // }
      ],
      currentNode: null,
      video: "video",
    };
  },
  mixins: [inspectMixin],
  props: {
    playingDeviceIds: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      ignoreDevices: "player/getIgnoreDevices",
    }),
  },
  watch: {
    playingDeviceIds: {
      handler(val) {
        if (this.$refs.tree.xnTree) this.$refs.tree.xnTree.refreshDom();
      },
    },
  },
  mounted() {
    this.getParentData();
  },
  methods: {
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let playingClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing"
          : "";
      let iconColor = this.getDeviceIconColor(data);
      let ignoreClass = this.ignoreDevices.includes(data.deviceId)
        ? "ignore"
        : "";
      let iconClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing-icon"
          : !data.deviceId
          ? data.$expanded
            ? data.source == "owner"
              ? "icon-folder-open-fill"
              : "icon-shared_open"
            : data.source == "owner"
            ? "icon-folder-fill"
            : "icon-shared_close"
          : this.getDeviceIcon(data);
      let operate = "";
      if (
        this.$_has(["video-realTime"]) &&
        data.children &&
        data.children.find((v) => v.isLeaf)
      ) {
        if (!this.insoectNodes.find((v) => v.node.id == data.id))
          operate += `<i class="iconfont operate startInspect icon-lunxunkaishi" title="轮巡播放"></i>`;
        if (
          this.insoectNodes.find((v) => v.node.id == data.id) &&
          this.insoectNodes.find((v) => v.node.id == data.id)["parse"]
        )
          operate += `<i class="iconfont operate parseInspect icon-lunxunkaishi" title="轮巡继续"></i>`;
        if (
          this.insoectNodes.find((v) => v.node.id == data.id) &&
          !this.insoectNodes.find((v) => v.node.id == data.id)["parse"]
        )
          operate += `<i class="iconfont operate parseInspect icon-lunxunzanting" title="轮巡暂停"></i>`;
        if (this.insoectNodes.find((v) => v.node.id == data.id))
          operate += `<i class="iconfont operate stopInspect icon-lunxuntingzhi" title="轮巡停止"></i>`;
      }
      if (!data.isLeaf) {
        // operate += `<i class="iconfont operate handleEdit icon-bianji" title="编辑"></i>`;
        // operate += `<i class="iconfont operate handleDel icon-shanchu" title="删除"></i>`;
        if (data.permission == "write")
          operate += `<i class="iconfont operate handleEdit icon-bianji" title="编辑"></i>`;
        // operate += `<i class="iconfont operate handleExport icon-download" title="导出"></i>`
        if (data.permission == "write")
          operate += `<i class="iconfont operate handleShare icon-caozuo" title="共享"></i>`;
        if (data.permission == "write")
          operate += `<i class="iconfont operate handleDel icon-shanchu" title="删除"></i>`;
      }
      let total = data.total ? "(" + data.total + ")" : "";
      let html = `<div class="node-title ${titleClass} ${playingClass} ${ignoreClass}">
          <i class="iconfont color-bule ${iconClass}" style="color: ${iconColor}"></i>
          <span class="label 111">${data.label}${total}</span>
        </div>
        <div class="operateBtns">${operate}</div>
        `;
      return html;
    },
    getParentData() {
      this.loading = true;
      queryMyVideoGroupList({
        userId: this.userInfo.id,
        searchKey: this.searchInput,
      }).then((res) => {
        let groupList = res.data.grouplist
          ? res.data.grouplist.map((v) => {
              v.label = v.groupName;
              return v;
            })
          : [];
        let deviceList = res.data.deviceList
          ? res.data.deviceList.map((v) => {
              v.id = v.deviceId;
              v.label = v.deviceName;
              v.isLeaf = true;
              return v;
            })
          : [];
        this.loading = false;
        this.firstInit = false;
        this.treeData = [...groupList, ...deviceList];
        this.$refs.tree.initTree(this.treeData);
      });
    },
    dblclickNode(nodeData, playType = "live") {
      if (nodeData.deviceId) {
        let obj = { ...nodeData };
        this.$emit("handleClick", { ...obj, devicetype: liveType, playType });
        if (playType == "live") {
          this.queryLog({
            muen: "视频中心",
            name: "我的分组",
            type: "4",
            remark: `查看【${nodeData.deviceName}】实时视频`,
          });
        }
      }
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
    searchName() {
      this.getParentData();
    },
    // 新增
    addGroup() {
      this.$refs.addModal.resertForm();
      this.$refs.addModal.show();
    },
    // 编辑
    handleEdit(data) {
      this.$refs.addModal.show(data);
    },
    // 共享
    handleShare(data) {
      this.currentNode = data;
      let sharedUserList = data.sharedUserList || [];
      sharedUserList.forEach((item) => {
        item.select = true;
        item.id = item.id || item.userId;
      });
      this.$refs.selectUser.show(sharedUserList);
    },
    handleUserData(list) {
      if (this.currentNode) {
        shareGroup({
          id: this.currentNode.id,
          sharedUserIds: list.map((v) => v.id),
        }).then((res) => {
          if (res.code == 200) {
            this.currentNode.sharedUserList = list;
            this.$Message.success("分享成功");
          }
        });
      }
    },
    // 删除
    handleDel(data) {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定删除吗？`,
        onOk: () => {
          delMyVideoGroup(data.id).then((res) => {
            this.searchName();
            this.$Message.success(res.msg);
          });
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.group-tree {
  @import "./style/index";
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .search-top {
    /deep/ .ivu-input {
      border-right: 1px solid #d3d7de;
    }
  }
  .deviceTree {
    height: calc(~"100% - 120px");
    margin: 0 12px;
  }
}
</style>
