<template>
    <div class="container">
		<div class="device">
			<!-- 查询 -->
			<Search ref="search" @searchForm="searchForm" />
			<div class="table-content">
				<ui-table :columns="columns" :data="list" :loading="loading">
					<template #dataType="{ row }">
						<div>{{ row.dataType }}</div>
					</template>
					<template #dataStatus="{ row }">
						<div>{{ row.dataStatus }}</div>
					</template>
				</ui-table>
			</div>
			<!-- 分页 -->
			<ui-page :current="params.pageNumber" :total="total" :page-size="params.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
		</div>
	</div>
</template>
<script>
import Search from '../components/search.vue'
export default {
    name: 'imgStructure',
    components: {
        Search
    },
    props: {},
    data() {
        return {
            list: [],
			loading: false,
            pageForm: {},
            total: 0,
            params: {
                pageNumber: 1,
                pageSize: 20
            },
            columns: [
                { title: '编号', align: 'center', width: 90, type: 'index', key: 'index' },
                { title: '任务名称', key: 'name', width: 150 },
                { title: '解析类型', slot: 'totalTask', minWidth: 220 },
                { title: '任务状态', slot: 'taskStatus' },
                { title: '处理时长', slot: 'executeTime' },
                { title: '创建时间', slot: 'createTime' },
                { title: '创建人', key: 'creatorName', },
                { title: '操作', slot: 'opreate', width: 100},
            ],
        }
    },
    created() {
        this.getList()
    },
    methods: {
        // 查询列表
        getList() {
            var param = Object.assign(this.pageForm, this.params)
            // queryDeviceInfoPageList(param).then(res => {
            //     const { entities, pageNumber, pageSize, total } = res.data
            //     this.list = entities || []
            //     this.params.pageNumber = pageNumber
            //     this.params.pageSize = pageSize
            //     this.total = total
            // })
        },
        // 查询
        searchForm(form) {
            this.pageForm = JSON.parse(JSON.stringify(form))
            this.params.pageNumber = 1
            this.getList()
        },
        // 改变页码
        pageChange(pageNumber) {
            this.params.pageNumber = pageNumber
            this.getList()
        },
        // 改变分页个数
        pageSizeChange(size) {
            this.params.pageSize = size
            this.params.pageNumber = 1
            this.getList()
        }
    }
}
</script>
<style lang="less" scoped>
	.device {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;
        .table-content{
            flex: 1;
        }
	}
</style>