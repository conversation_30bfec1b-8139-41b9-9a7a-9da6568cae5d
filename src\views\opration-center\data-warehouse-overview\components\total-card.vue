<template>
	<div class="total-card">
		<div>{{ title }}</div>
		<div class="total">
            <count-to :start-val="0" :end-val="total" :duration="1000" class="num"></count-to>
        </div>
		<div>今日新增</div>
		<div>
			<span class="add-num">
                <count-to :start-val="0" :end-val="addNum" :duration="1000" class="num"></count-to>
            </span>
			<!-- <span :class="isAdd?'add-percent':'reduce-percent'">{{ addPercent }}
				<Icon type="ios-share-alt" />
			</span> -->
		</div>
		<div class="img-box">
			<img class="img" :src="require('@/assets/img/data-warehouse/images/'+imageUrl)" alt="">
		</div>
	</div>
</template>
<script>
    import CountTo from 'vue-count-to'
	export default {
		name: 'totalCard',
        components: {
            CountTo
		},
		props: {
			title: {
				type: String,
				default: '人脸抓拍总数'
			},
			total: {
				type: [String, Number],
				default: 0
			},
			addNum: {
				type: [String, Number],
				default: 0
			},
			addPercent: {
				type: String,
				default: '+5%'
			},
			isAdd: {
				type: Boolean,
				default: true
			},
			imageUrl: {
				type: String,
				default: 'total1.png'
			}
		},
		data() {
			return {

			}
		}
	}
</script>
<style lang="less" scoped>
	.total-card {
		width: 100%;
		height: 100%;
		background-color: #f9f9f9;
		border-radius: 4px;
		padding: 40px;
		font-size: 14px;
		display: flex;
		justify-content: center;
		flex-direction: column;
		position: relative;
		.total {
			font-size: 32px;
			font-weight: bold;
			color: #2c86f8;
			margin-bottom: 20px;
		}
		.add-num {
			font-size: 24px;
			color: #1faf81;
			font-weight: bold;
			margin-right: 30px;
		}
		.add-percent {
			font-size: 20px;
			color: #1faf81;
			font-weight: bold;
			i {
				transform: rotate(270deg);
			}
		}
		.reduce-percent {
			font-size: 20px;
			color: #f29f4c;
			font-weight: bold;
			i {
				transform: rotate(90deg);
			}
		}
		.img-box {
			width: 50px;
			height: 50px;
			position: absolute;
			top: 20px;
			right: 20px;
			border-radius: 25px;
			border: 1px solid #2c86f8;
			background-color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			img {
				width: 25px;
				height: 25px;
			}
		}
	}
</style>
