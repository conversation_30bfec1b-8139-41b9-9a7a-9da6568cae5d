import request from "@/libs/request";
import { numberCube, irasService, service } from "./Microservice";

// 查询关系图谱分类
export function getGraphlLabel(data) {
  return request({
    url: numberCube + "/relational/graph/label",
    method: "post",
    data,
  });
}
// 根据分类查询所有实体
export function getGraphlVertexByLabel(data) {
  return request({
    url: numberCube + "/relational/graph/vertexByLabel",
    method: "post",
    data,
  });
}
// 搜索关系图谱---搜索指定实体的关系图谱
export function getGraphlRelational(data) {
  return request({
    url: numberCube + "/relational/graph/relational",
    method: "post",
    data,
  });
}

/************ new **************/
// 首页查询 关键字、人脸搜图
export function entitySearch(data) {
  return request({
    url: numberCube + "/relation/query/entitySearch",
    method: "post",
    data,
  });
}

// 首页查询实体列表
export function entityList(data) {
  return request({
    url: irasService + "/manage/schema/entity/list",
    method: "post",
    data,
  });
}
// 实体统计
export async function entityCount(data) {
  return await request({
    url: numberCube + "/relation/query/entitySearchCount",
    method: "post",
    data,
  });
}
// 查询结果 - 确定按钮
export function relationDetail(data) {
  return request({
    url: numberCube + "/relation/query/nDegreeRelation/objectView",
    method: "post",
    data,
    // url: numberCube + '/relation/query/nDegreeRelation',
    // method: 'get',
    // params
  });
}

export function relationDetail_v2(data) {
  return request({
    url: numberCube + "/relation/query/v2/nDegreeRelation/objectView",
    method: "post",
    data,
    timeout: 0,
    // url: numberCube + '/relation/query/nDegreeRelation',
    // method: 'get',
    // params
  });
}
// // 查询结果 - 确定按钮
// export function relationDetail(ids, maxDepth) {
//   return request({
//     url: numberCube + '/relation/query/nDegreeRelation?entityIds='+ids+"&maxDepth="+maxDepth,
//     method: 'get',
//     params
//   })
// }
// 首页画布列表
export function anvasList(data) {
  return request({
    url: numberCube + "/relation/canvas/my",
    method: "post",
    data,
  });
}
// 画布新增
export function canvasAdd(data) {
  return request({
    url: numberCube + "/relation/canvas/add",
    method: "post",
    data,
  });
}
// 画布编辑
export function canvasUpdate(data) {
  return request({
    url: numberCube + "/relation/canvas/update",
    method: "put",
    data,
  });
}
// 画布删除
export function canvasRemove(ids) {
  return request({
    url: numberCube + "/relation/canvas/remove/" + ids,
    method: "delete",
  });
}
// 画布详情
export function canvasDetail(id) {
  return request({
    url: numberCube + "/relation/canvas/view/" + id,
    method: "get",
  });
}

// 获取全部关系类型
export function getRelateTypeList(data) {
  return request({
    url: irasService + "/manage/schema/relation/list",
    method: "post",
    data,
  });
}

// 查询实体类型分页列表
export function getEntityTypeList(data) {
  return request({
    url: irasService + "/manage/schema/entity/pageList",
    method: "post",
    data,
  });
}

// 图片上传
export function imgUpload(data) {
  return request({
    url: service + "/file/upload",
    method: "post",
    data,
  });
}

// 路径推演
export function pathLookup(data) {
  return request({
    url: numberCube + "/relation/query/pathLookup",
    method: "post",
    data,
  });
}

// 档案 - 关系统计
export function relationStat(data) {
  return request({
    url: irasService + "/manage/entity/relationStat",
    method: "post",
    data,
  });
}

// 关系详情
export function relationDetail2(data) {
  return request({
    url: numberCube + "/relation/query/v2/relationDetail",
    method: "post",
    data,
    timeout: 0,
  });
}

// 关系视图
export function relationView(data) {
  return request({
    url: numberCube + "/relation/query/nDegreeRelation/relationView",
    method: "post",
    data,
    timeout: 0,
  });
}

// N度关系
export function nDegreeRelation(params) {
  return request({
    url: numberCube + "/relation/query/nDegreeRelation",
    method: "get",
    params,
  });
}

// 查询两个节点间的路径
export function getAllReachablePaths(data) {
  return request({
    url: numberCube + "/relation/query/getAllReachablePaths",
    method: "post",
    data,
    timeout: 0,
  });
}

// 关系图谱
export function getRelationMap(data) {
  return request({
    url: numberCube + "/relation/query/relationMap",
    method: "post",
    data,
  });
}

// 关系图谱
export function relationIntro(data) {
  return request({
    url: numberCube + "/relation/query/archive/relationIntro",
    method: "post",
    data,
  });
}

// 路径推演-对象视图
export function objectViewPath(data) {
  return request({
    url: numberCube + "/relation/query/pathLookup/objectView",
    method: "post",
    data,
  });
}

// 连接分析-对象视图
export function connectAnalysis(data) {
  return request({
    url: numberCube + "/relation/query/connectionAnalysis/objectView",
    method: "post",
    data,
  });
}

// 首页字段属性列表
export function propertyList(data) {
  return request({
    url: irasService + "/manage/schema/property/pageList",
    method: "post",
    data,
  });
}

export function consanguinitySearch(data) {
  return request({
    url: numberCube + "/relation/query/consanguinitySearch",
    method: "post",
    data,
  });
}

// 关系挖掘新增
// 查询图谱挖掘配置信息
export function getMiningConfig(data) {
  return request({
    url: numberCube + "/relation/mining/getMiningConfig",
    method: "post",
    data,
  });
}

// 分页查询可用的挖掘关系
export function enableMiningRelationPageList(data) {
  return request({
    url: numberCube + "/relation/mining/enableMiningRelationPageList",
    method: "post",
    data,
  });
}

// 查询挖掘可依赖关系
export function getDependencyRelations(data) {
  return request({
    url: numberCube + "/relation/mining/getDependencyRelations",
    method: "post",
    data,
  });
}

// 获取实体类型获取实体
export function getEntityByNames(data) {
  return request({
    url: numberCube + "/relation/schema/query/getEntityByNames",
    method: "post",
    data,
  });
}
// 获取关系类型获取关系
export function getRelationByNames(data) {
  return request({
    url: numberCube + "/relation/schema/query/getRelationByNames",
    method: "post",
    data,
  });
}
// 关系挖掘的名称是否存在
export function relationNameExists(data) {
  return request({
    url: numberCube + "/relation/mining/relationNameExists",
    method: "post",
    data,
  });
}
// 关系挖掘执行
export function exec(data) {
  return request({
    url: numberCube + "/relation/mining/exec",
    method: "post",
    timeout: 60 * 60 * 1000,
    data,
  });
}
// 保存关系挖掘模型
export function saveMiningModel(data) {
  return request({
    url: numberCube + "/relation/mining/saveMiningModel",
    method: "post",
    timeout: 60 * 60 * 1000,
    data,
  });
}
// 保存关系挖掘结果
export function saveMiningRelation(data) {
  return request({
    url: numberCube + "/relation/mining/saveMiningRelation",
    method: "post",
    timeout: 60 * 60 * 1000,
    data,
  });
}

// 删除关系挖掘模型
export function deleteMiningModel(id) {
  return request({
    url: numberCube + "/relation/mining/deleteMiningModel/" + id,
    method: "delete",
  });
}

// 预览挖掘关系明细
export function previewMiningRelationDetail(data) {
  return request({
    url: numberCube + "/relation/mining/previewMiningRelationDetail",
    method: "post",
    timeout: 60 * 60 * 1000,
    data,
  });
}

export function getKgSummary(data) {
  return request({
    url: irasService + "/manage/image/kgSummary",
    method: "post",
    timeout: 0, // 不设置超时时间
    data,
  });
}

export function getKgDetective(data) {
  return request({
    url: irasService + "/manage/image/kgDetective",
    method: "post",
    timeout: 0, // 不设置超时时间
    data,
  });
}
