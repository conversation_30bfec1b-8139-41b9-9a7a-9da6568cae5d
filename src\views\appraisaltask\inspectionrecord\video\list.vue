<template>
  <div class="auto-fill">
    <!--  :treeData="taskObj.orgCodeList" -->
    <!-- <slide-unit-tree
      :treeData="treeData"
      @selectOrgCode="selectOrgCode"
      :current-node-key="getDefaultSelectedOrg.orgCode"
      :select-key="selectKey"
    >
    </slide-unit-tree> -->
    <chartsContainer :abnormalCount="listObj.abnormalCount" />
    <searchPage
      :taskObj="taskObj"
      :treeData="treeData"
      :currentTree="currentTree"
      @searchFn="searchFn"
      @selectedTree="selectOrgCode"
    />
    <!-- <slot name="dialog"> </slot> -->
    <ui-table
      v-if="showTable"
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
    >
      <!-- <div v-for="item in tableColumns" :key="item">
          <slot :slot='item.slot' :row="row"> </slot>
        </div> -->
      <template v-for="item in tableColumns" #[item.slot]="{ row, index }">
        <slot v-if="item.slot" :name="item.slot" :row="row" :index="index">
          <span v-if="item.slot === 'reason'" :key="index">{{ row.reason }}</span>
          <Tooltip placement="top" v-if="row.detectionMode === 1 && row.dateImageText != null">
            <i class="icon-font icon-wenhao ml-xs f-12"> </i>
            <div slot="content">
              <p v-if="row.dateImageText != null">
                {{ JSON.parse(row.dateImageText)[0].text }}
              </p>
              <p v-if="row.dateImageText != null">
                {{ JSON.parse(row.dateImageText)[0].verticeList }}
              </p>
            </div>
          </Tooltip>
        </slot>
      </template>
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>

<script>
import user from '@/config/api/user';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
export default {
  name: 'International-encoding-detection',
  props: {
    listObj: {
      type: Object,
      default() {},
    },
    currentTree: {
      type: Object,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {},
    },
  },
  components: {
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    searchPage: require('./search').default,
    chartsContainer: require('../components/chartsContainer').default,
  },
  data() {
    return {
      treeData: [],
      showTable: false,
      tableColumns: [
        { title: '序号', type: 'index', width: 70, align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 190,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: 'true',
          width: 120,
        },
        { title: '组织机构', key: 'orgName', width: 120 },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          tooltip: 'true',
          width: 140,
        },
      ],
      tableData: [],
      //分页
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      selectKey: '0',
      formValidate: {
        checkStatus: '',
        isImportantDevice: '',
        keyWord: '',
        orgCode: '',
        sbgnlx: '',
        totalCount: 0,
        pageNumber: 1,
        pageSize: 20,
      },
      dictData: {},
      loading: false,
      hasLast: false,
      statisticsData: {},
    };
  },
  mounted() {
    this.tableColumns = [...this.tableColumns, ...this.listObj.columns];
    for (let i of this.tableColumns) {
      if (i.key === 'reason') {
        i.slot = 'reason';
      }
    }
    this.showTable = true;
    Object.assign(this.formValidate, this.currentTree);
  },
  methods: {
    init(parameters) {
      if (!this.taskObj.batchId) {
        return;
      }
      this.loading = true;
      // this.formValidate.batchId = this.taskObj.batchId
      // this.formValidate.indexId = this.taskObj.indexId
      // this.formValidate.orgCode = this.taskObj.regionCode
      Object.assign({ ...parameters }, this.currentTree);
      this.listObj
        .loadData(Object.assign({ ...parameters }, this.pageData))
        .then((res) => {
          if (res.code === 200) {
            if (res.data.total == 0) {
              this.pageData.pageNum = 1;
            }
            this.pageData.totalCount = res.data.total;
            this.tableData = res.data[this.listObj.key || 'entities'];
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchFn(row) {
      this.pageData.pageNumber = 1;
      this.formValidate = {
        ...this.formValidate,
        ...row,
      };
      this.init(this.formValidate);
    },
    // getOrgDataByRegioncode
    // initData() {
    //   this.tableData.push({
    //     deviceId: '123',
    //     deviceName: '设备名称1',
    //     civilName: '223',
    //   })
    // },
    // async getEvaluationStatisticsCount() {
    //   try {
    //     let params = {
    //       deviceTagCategory: [202, 204, 206, 208].includes(this.currentTree.id) ? 2 : 1,
    //       evaluationStatisticsTypeId: this.$parent.currentTree.id,
    //       orgCode: this.formValidate.orgCode,
    //       resultId: this.$parent.taskObj.resultId,
    //     }
    //     let {
    //       data: { data },
    //     } = await this.$http.post(api.queryEvaluatingVideoCount, params)
    //     this.statisticsData = data
    //   } catch (e) {}
    // },
    checkStatusList(checkStatus) {
      return checkStatus === '1' ? '合格' : '不合格';
    },
    clickRow(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    search() {
      this.pageData.pageNumber = 1;
      this.pageData.pageSize = 10;
      this.init(this.formValidate);
    },
    changePageSize(val) {
      this.pageData.pageNumber = 1;
      this.pageData.pageSize = val;
      this.init(this.formValidate);
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.pageData.pageNumber = val;
      this.init(this.formValidate);
    },
    // async init2() {
    //   try {
    //     this.loading = true
    //     let params = {
    //       evaluationStatisticsTypeId: this.$parent.currentTree.id,
    //       orgCode: this.formValidate.orgCode,
    //       sbdwlx: this.formValidate.sbdwlx,
    //       sbgnlx: this.formValidate.sbgnlx,
    //       isImportantDevice: this.formValidate.isImportantDevice,
    //       checkStatus: this.formValidate.checkStatus,
    //       resultId: this.$parent.taskObj.resultId,
    //       keyWord: this.formValidate.keyWord,
    //       pageNumber: this.pageData.pageNumber,
    //       pageSize: this.pageData.pageSize,
    //     }
    //     let { data } = await this.$http.post(governanceevaluation.getEvaluationStatistics, params)
    //     this.tableData = data.data.entities
    //     this.pageData.totalCount = data.data.total
    //   } catch (e) {
    //     console.log(e)
    //   } finally {
    //     this.loading = false
    //   }
    // },
    selectOrgCode(data) {
      this.$emit('refeshCount', data.orgCode);
    },
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    resetForm() {
      this.formValidate = {
        checkStatus: '',
        isImportantDevice: '',
        keyWord: '',
        orgCode: '',
        sbdwlx: '',
        sbgnlx: '',
      };
      this.search();
    },
    getRegioncode() {
      if (!this.taskObj.regionCode) {
        return;
      }
      this.$http
        .get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.taskObj.regionCode },
        })
        .then((res) => {
          this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(res.data.data)), 'id', 'parentId');
        });
    },
  },

  computed: {
    ...mapGetters({
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {
    taskObj: {
      handler(val) {
        const { batchId, indexId, regionCode } = val;
        this.init({
          ...this.formValidate,
          batchId,
          indexId,
          regionCode,
        });
        this.getRegioncode();
        // if (val.orgCodeList && val.orgCodeList.length) {
        //   let defaultOrgCode = val.orgCodeList.find((item) => !item.disabled && !!item.orgCode)
        //   this.treeData = this.$util.common.arrayToJson(
        //     JSON.parse(JSON.stringify(val.orgCodeList)),
        //     'id',
        //     'parentId'
        //   )
        //   console.log(defaultOrgCode)
        //   if (!defaultOrgCode) {
        //     this.$Message.error('您没有此行政区划权限')
        //   } else {
        //     this.formValidate.orgCode = defaultOrgCode.orgCode
        //     this.selectKey = defaultOrgCode.orgCode
        //     this.selectOrgCode(defaultOrgCode)
        //     this.init()
        //   }
        // }
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.text_hid {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.f-32 {
  font-size: 32px;
}
.color-white {
  color: #fff;
}
.color-num {
  color: #19d5f6;
}
.icon-1 {
  background: linear-gradient(360deg, #176350 0%, #54f2b3 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-2 {
  background: linear-gradient(360deg, #193bc5 0%, #5b73ec 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-3 {
  background: linear-gradient(360deg, #78460b 0%, #d67506 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-4 {
  background: linear-gradient(359deg, #88750e 0%, #bba00b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-5 {
  background: linear-gradient(359deg, #8822cc 0%, #cf20e2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}
.search-container {
  margin-top: 10px;
  .charts-container {
    display: flex;
    .charts-item {
      margin-right: 10px;
      width: 304px;
      height: 88px;
      background: #0f2f59;
      display: flex;
      justify-content: center;
      align-items: center;
      .number-wrapper {
        display: inline-block;
        margin-left: 16px;
      }
    }
  }
  .search {
    margin-top: 20px;
    margin-bottom: 20px;
  }
}
.ui-table {
  @{_deep} .ivu-table-body {
    td {
      padding: 10px 0 10px 0;
    }
  }
  @{_deep} .ivu-table-fixed-right .ivu-table-fixed-body {
    td {
      padding: 10px 0 10px 0;
    }
  }
  @{_deep} .ivu-table-fixed .ivu-table-fixed-body {
    td {
      padding: 10px 0 10px 0;
    }
  }
  @{_deep} .ivu-table-cell {
    .text_hid();
    .ivu-tooltip-rel {
      .text_hid();
    }
  }
  @{_deep} .ivu-table-hidden {
    visibility: unset;
  }
}
.page {
  padding-right: 0;
}

.icon-font.icon-chakanjietu {
  font-size: 18px !important;
}

/deep/ td.ivu-table-column-left,
/deep/ th.ivu-table-column-left {
  text-align: center;
}
</style>
