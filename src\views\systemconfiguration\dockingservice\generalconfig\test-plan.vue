<template>
  <div class="time-modal" :class="{ 'align-row': align === 'row' }">
    <RadioGroup
      :class="{ 'mb-sm': align !== 'row', 'mr-lg': align === 'row' }"
      type="button"
      button-style="solid"
      v-model="form.cronType"
      @on-change="setPlan"
    >
      <Radio :label="item.value" v-for="(item, index) in getPlanList" :key="index" :disabled="isView">
        {{ item.label }}
      </Radio>
    </RadioGroup>
    <div class="form-row">
      <span v-if="form.cronType === '2'" class="width-time mr-sm">
        <Select
          key="week"
          placeholder="请选择"
          v-model="form.cronData"
          transfer
          multiple
          clearable
          :max-tag-count="1"
          :disabled="isView"
          @on-change="changeData"
        >
          <Option v-for="item in planWeek" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </span>
      <span v-if="form.cronType === '3'" class="width-time mr-sm">
        <Select
          key="month"
          placeholder="请选择"
          v-model="form.cronData"
          transfer
          multiple
          clearable
          :max-tag-count="1"
          :disabled="isView"
          @on-change="changeData"
        >
          <Option v-for="item in planMonth" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </span>
      <span class="width-picker">
        <Select
          class="time-picker"
          v-model="form.timePoints"
          transfer
          multiple
          clearable
          :max-tag-count="form.cronType === '1' ? 3 : 1"
          :class="form.cronType === '1' ? 'width18' : 'width12'"
          :disabled="isView"
          @on-change="changeCronTime"
        >
          <Option v-for="item in schemeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    formData: {
      type: Object,
      default: () => {},
    },
    isView: {
      type: Boolean,
      default: false,
    },
    customPlanList: {
      type: Array,
      default: () => [],
    },
    align: {
      type: String,
      default: 'column',
    },
    cronStrictly: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      form: {
        cronType: '1',
        timePoints: [],
        cronData: [],
      },
      planList: [
        { label: '每天', value: '1' },
        { label: '每周', value: '2' },
        { label: '每月', value: '3' },
      ],
      planWeek: [
        { label: '周一', value: 1 },
        { label: '周二', value: 2 },
        { label: '周三', value: 3 },
        { label: '周四', value: 4 },
        { label: '周五', value: 5 },
        { label: '周六', value: 6 },
        { label: '周日', value: 7 },
      ],
      planMonth: [],
      schemeList: [],
    };
  },
  computed: {
    getPlanList() {
      return this.customPlanList.length > 0 ? this.customPlanList : this.planList;
    },
  },
  watch: {
    formData: {
      handler(obj) {
        this.form = {
          cronType: obj.cronType,
          timePoints: obj.cronType === '1' ? obj.cronData : obj.timePoints,
          cronData: obj.cronType === '1' ? [] : obj.cronData,
        };
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.getHour();
    this.mGetDate();
  },
  mounted() {},
  methods: {
    // 获取时间列表
    getHour() {
      let arr = [];
      for (let i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      this.schemeList = arr;
    },
    // 获取当前月天数
    mGetDate() {
      let date = new Date();
      let curretMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      let curre = curretMonth.getDate();
      let arr = [];
      for (let i = 1; i <= curre; i++) {
        arr.push({ label: i + '号', value: i });
      }
      this.planMonth = arr;
    },
    // 选择天/周/月
    setPlan(e) {
      this.form.timePoints = [];
      this.form.cronData = [];
      this.form.cronType = e;
      this.$emit('checkTime', this.form);
    },
    // 选择周/月
    changeData() {
      if (this.form.cronType !== '1' && this.form.timePoints.length > 0) {
        this.$emit('checkTime', this.form);
      } else if (!this.cronStrictly && this.form.cronData.length > 0) {
        this.$emit('checkTime', this.form);
      }
    },
    changeCronTime() {
      const { cronType, cronData, timePoints } = this.form;
      const time = {
        cronData: cronType === '1' ? timePoints : cronData,
        cronType,
        timePoints: cronType === '1' ? [] : timePoints,
      };
      this.$emit('checkTime', time);
    },
  },
};
</script>
<style lang="less" scoped>
.time-modal {
  &.align-row {
    display: flex;
    align-items: center;
  }
  text-align: left;
  .width-time {
    width: 154px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .width18 {
    width: 380px;
  }
  .width12 {
    width: 217px;
  }
}
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
}
@{_deep}.ivu-modal-body {
  padding: 10px 50px !important;
}

@{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
  border: 1px solid var(--border-checkbox-inner);
  background: var(--bg-checkbox-inner);
  color: var(--color-switch-tab);
  > span {
    margin: 0;
  }
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:after {
  background: var(--border-checkbox-inner);
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
  background: var(--border-checkbox-inner);
}
@{_deep} .ivu-radio-group-button-solid .ivu-radio-wrapper-checked:not(.ivu-radio-wrapper-disabled) {
  background: var(--color-primary);
  color: #fff;
  border: 1px solid var(--color-primary);
  box-shadow: -1px 0 0 0 var(--color-primary);
}
@{_deep} .time-picker .ivu-icon-ios-arrow-down:before {
  content: '\F2DA';
}
@{_deep} .ivu-select-multiple .ivu-tag{
  margin: 4px 3px 7px 0 !important;
}
</style>
