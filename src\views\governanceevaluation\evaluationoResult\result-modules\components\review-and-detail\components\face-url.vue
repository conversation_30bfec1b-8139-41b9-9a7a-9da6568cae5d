<template>
  <base-review v-bind="getAttrs" v-on="$listeners">
    <template #customitem="{ reviewFormData }">
      <FormItem label="">
        <CheckboxGroup
          v-if="reviewFormData.qualified === '2' && $attrs['custorm-params']['type'] === 'detail'"
          v-model="$attrs['custorm-params']['errorCode']"
        >
          <Checkbox v-for="(item, index) in errorCodeList" :key="index" :label="item.value">
            <span class="check-text dis-select">{{ item.label }}</span>
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
    </template>
  </base-review>
</template>

<script>
export default {
  name: 'vehicle-url',
  props: {
    errorCodeList: {
      type: Array,
      default: () => [],
    },
    // 区分 设备模式 device 图片模式detail
    mode: {
      type: String,
      default: 'device',
    },
    reviewRowData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  computed: {
    getAttrs() {
      if (this.$attrs['custorm-params']['type'] === 'detail') {
        return {
          reviewRowData: this.reviewRowData,
          ...this.$attrs,
          'custorm-params': {
            ...this.$attrs['custorm-params'],
            errorCode: this.$attrs['custorm-params']['errorCode'].join(',') || '',
          },
        };
      } else {
        return {
          reviewRowData: this.reviewRowData,
          ...this.$attrs,
        };
      }
    },
  },
  watch: {},
  components: {
    BaseReview: require('./base-review').default,
  },
};
</script>

<style lang="less" scoped></style>
