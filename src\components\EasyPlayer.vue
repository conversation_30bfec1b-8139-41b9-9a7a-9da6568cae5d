<template>
  <div class="easy-player">
    <EasyPlayer
      alt="请求中..."
      :autoplay="true"
      :video-url="url"
      :live="isLive"
      :reconnection="true"
      :stretch="stretch"
      :show-custom-button="false"
      :class="{ 'no-stretch': !stretch }"
      fluent
      ref="easyPlayer"
    ></EasyPlayer>
  </div>
</template>

<script>
// import EasyPlayer from '@easydarwin/easyplayer';
export default {
  name: 'VideoPlayer',
  props: {
    videoUrl: {
      type: String,
    },
    isLive: {
      type: Boolean,
      dafault: true,
    },
    needDecrypt: {
      type: Boolean,
      default: true,
    },
    decryptKey: {
      type: String,
      default: 'QSDI123456',
    },
    stretch: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      url: '',
    };
  },
  components: {
    // EasyPlayer,
    EasyPlayer: () => import('@easydarwin/easyplayer'),
  },
  mounted() {},
  methods: {},
  watch: {
    videoUrl: {
      handler(val) {
        if (!val) {
          this.url = '';
          return;
        }
        if (this.needDecrypt) {
          this.url = this.$util.common.decryptDes(val, this.decryptKey);
        } else {
          this.url = val;
        }
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.easy-player {
  margin-top: -20px;
  height: 500px;
  @{_deep} .easy-player-right-menu {
    display: none;
  }
  @{_deep} .vjs-stretch-control {
    display: none;
  }
}
// fix: easyplayer 的strech字段设置无效
.no-stretch {
  @{_deep}&.easy-player-fill-container .video-js .vjs-tech {
    object-fit: contain !important;
  }
}
</style>
