<template>
  <div class="ui-gather-card">
    <div class="ui-gather-card-left">
      <div class="img">
        <ui-image :src="list.imageUrl" class="ui-image-card" @click.native="viewBigPic(list.imageUrl)" />
        <p v-if="!!list.objectSnapshotId" class="shadow-copy" style="z-index: 11" :title="list.objectSnapshotId">
          <i class="base-text-color f-14">ID:</i>
          <span class="base-text-color id-num ellipsis f-14" :title="list.objectSnapshotId">{{
            list.objectSnapshotId
          }}</span>
          <span class="copy-text f-14" v-clipboard="list.objectSnapshotId" v-clipboard:callback="copy">复制</span>
        </p>
      </div>
    </div>
    <div class="ui-gather-card-right">
      <div class="ui-gather-card-right-items" v-for="(item, index) in cardInfo" :key="index">
        <p v-if="!item.type" class="ui-gather-card-right-items-p">
          <span class="ui-gather-card-right-item-label">{{ item.name }}：</span>
          <template v-if="item.name == '抓拍地点' || item.name == '抓拍时间' || list.indexId === 3001">
            <span
              v-if="!item.algorithm"
              :title="list[item.value] || '缺失'"
              class="ui-gather-card-right-item-value"
              :style="{
                color: list[item.value] ? '#fff' : '#C43D2C',
              }"
              >{{ list[item.value] || '缺失' }}</span
            >
            <span
              v-else
              :title="list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失')"
              class="ui-gather-card-right-item-value"
              :style="{
                color: list[item.value] ? '#fff' : '#C43D2C',
              }"
            >
              {{ list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失') }}</span
            >
          </template>

          <template v-else>
            <span
              v-if="!item.algorithm"
              :title="list[item.value] || '缺失'"
              class="ui-gather-card-right-item-value"
              :style="{
                color: list[item.value] ? judgeUnusual(item.name) : '#C43D2C',
              }"
              >{{ list[item.value] || '缺失' }}</span
            >
            <span
              v-else
              :title="list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失')"
              class="ui-gather-card-right-item-value"
              :style="{
                color: list[item.value] ? judgeUnusual(item.name) : '#C43D2C',
              }"
            >
              {{ list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失') }}</span
            >
          </template>
        </p>
      </div>
      <p>
        <i
          title="查看大图"
          class="icon-font icon-yichang search-icon mr-sm font-blue f-16"
          @click="viewBigPic(list.imageUrl)"
        ></i>
        <i
          v-if="list.indexId === 3003 || list.indexId === 3006 || list.indexId === 3007"
          title="查看算法详情"
          class="icon-font icon-xiangqing search-icon mr-sm font-blue f-16"
          @click="detail(list, list.indexId)"
        ></i>
      </p>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  computed: {
    cardInfoImage() {
      let array = [];
      this.cardInfo.map((val) => {
        if (val.type === 'image') {
          array.push(val);
        }
      });
      return array;
    },
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
    }),
  },
  methods: {
    copy() {
      this.$Message.success('复制成功');
    },
    // 车辆属性判断
    judgeUnusual(val) {
      let result = '';
      if (this.list.reason) {
        if (this.list.reason.indexOf(val) == -1) {
          result = '#fff';
        } else {
          result = '#C43D2C';
        }
        return result;
      }
    },
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
    algorithm(item) {
      return this[item];
    },
    detail(list, index) {
      this.$refs.carPropertyDialog.init(list.id, index);
    },
  },
  watch: {},
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  margin-bottom: 10px;
  padding: 10px;
  padding-bottom: 6px;
  background: var(--bg-info-card);
  &-left {
    margin-right: 10px;
    width: 28%;
    display: flex;
    align-items: center;
    .img {
      width: 104px;
      height: 104px;
      position: relative;
    }
  }
  &-right {
    flex: 1;
    width: 50%;
    &-items {
      // margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
    }
  }
}
.ui-gather-card-right-items-p {
  // margin-bottom: 6px;
}
.ui-image-card {
  cursor: pointer;
}
.ui-gather-card-right-item-value {
  color: #ffffff;
  color: rgb(255, 255, 255);
  width: 66%;
  white-space: nowrap;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
  position: relative;
  top: 5px;
}
.ui-gather-card-right-item-label {
  color: #8797ac;
}
.shadow-box {
  height: 28px;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  bottom: 0;
  display: none;
  padding-left: 10px;
  > i:hover {
    color: var(--color-primary);
  }
}
.shadow-copy {
  height: 28px;
  line-height: 28px;
  width: 100%;
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 30px;
  display: none;
  padding-left: 10px;
  .id-num {
    width: 30px;
    vertical-align: top;
    display: inline-block;
  }

  i {
    vertical-align: top;
  }
  .copy-text {
    color: var(--color-primary);
    cursor: pointer;
    vertical-align: sub;
  }
  @media screen and (max-width: 1366px) {
    .id-num {
      width: 24px; /*no*/
    }
    .copy-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
.img:hover {
  .shadow-box {
    display: block;
  }
  .shadow-copy {
    display: block;
  }
}
</style>
