<template>
  <div class="container">
    <div class="person">
      <!-- 查询 -->
      <Search
        v-show="!$route.query.noSearch"
        type="video"
        @searchForm="searchForm"
        :dataType="pageForm.dataType"
        :page="'holographic'"
        :searchText="'高级检索'"
      />
      <div class="card-content">
        <div
          v-for="(item, index) in list"
          :key="index"
          :class="item.type === 'people' ? 'people-card' : 'video-card'"
          class="card-item"
        >
          <UiListCard
            :type="item.type"
            :showBar="false"
            :data="item"
            :isChange="item.realNameArchiveNo ? true : false"
            @archivesDetailHandle="archivesDetailHandle(item)"
            @on-change="changeCardHandle(item, index)"
            @collection="getList"
            @on-search-image="onSearchImage"
            @on-archive="archivesDetailHandle"
            @on-control="onControl"
          />
        </div>
        <ui-empty v-if="list.length === 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="params.pageNumber"
        countTotal
        :showElevator="true"
        :total="total"
        :page-size="params.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
    <search-image ref="searchImage" @on-submit="onSubmit"></search-image>
  </div>
</template>
<script>
import { mapActions, mapMutations } from "vuex";
import Search from "../real-name-file/components/search.vue";
import UiListCard from "@/components/ui-list-card";
import { getPersonPageList, personBaseInfo } from "@/api/realNameFile";
import SearchImage from "@/components/search-image/index.vue";
export default {
  components: { SearchImage, Search, UiListCard },
  props: {},
  data() {
    return {
      info: {},
      list: [],
      pageForm: {
        dataType: 2,
      },
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      keyWords: "", // 关键字，从全景智搜跳转过来
    };
  },
  created() {
    this.keyWords = this.$route.query.keyWords || "";
    this.getDictData();
    this.getList();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("common", ["setWisdomCloudSearchData"]),
    ...mapMutations("control", ["setPersonControl"]),
    async onControl(row) {
      this.setPersonControl({
        ...row,
        photoUrl: row.photo,
      });
      this.$router.push("/target-control/control-task/add");
    },
    async onSearchImage(src, row, currentIndex) {
      if (!src) {
        return this.$Message.error("图片不存在");
      }
      let picData = {
        algorithmType: "1", //1人脸 2 车辆 3 人体 4 非机动车
        similarity: 75, // 相似度
      };
      this.$refs.searchImage.searchImage(picData, src);
    },
    onSubmit({ algorithmType, similarity, urlList }) {
      let params = {
        keyWords: "",
        algorithmType: algorithmType,
        urlList: [urlList],
      };
      this.$router.push({
        path: "/wisdom-cloud-search/cloud-default-page",
        query: {
          params: params,
          type: 2,
        },
      });
    },

    /**
     * @description: 查询列表
     */
    getList() {
      this.loading = true;
      var param = {
        ...this.pageForm,
        ...this.params,
        searchValue: this.keyWords || undefined,
      };
      var labels = [];
      if (param.labelIds && param.labelIds.length > 0) {
        param.labelIds.forEach((item) => {
          labels.push(item.id);
        });
        param.labelIds = labels;
      }
      getPersonPageList(param).then((res) => {
        const { entities, pageNumber, pageSize, total } = res.data;
        entities.map((item) => {
          item.type = "video";
        });
        this.list = entities ? entities : [];
        this.params.pageNumber = pageNumber;
        this.params.pageSize = pageSize;
        this.total = total;
        this.loading = false;
        // 重新获取页码后回到顶部
        document.querySelector(".card-content").scrollTop = 0;
      });
    },
    // 基本信息
    async personBaseInfo(val) {
      let info = {};
      let data = {
        archiveNo: val,
        dataType: 1,
      };
      await personBaseInfo(data).then((res) => {
        info = res.data;
        info.type = "people";
      });
      return info;
    },
    // 档案详情
    archivesDetailHandle(item) {
      let name = item.type === "video" ? "video-archive" : "people-archive";
      let type = item.type === "people" ? "people" : "video";
      let archiveNo =
        item.type === "people" ? item.realNameArchiveNo : item.archiveNo;
      let query = {
        archiveNo: archiveNo,
        source: type,
        initialArchiveNo: archiveNo,
      };
      const { href } = this.$router.resolve({
        name: name,
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      // sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
    // 卡片切换
    async changeCardHandle(item, index) {
      if (this.list[index].type === "video") {
        let obj = await this.personBaseInfo(item.realNameArchiveNo);
        this.list[index].type = "people";
        this.list[index].xm = obj.xm;
        this.list[index].xbdm = obj.xbdm;
        this.list[index].mzdm = obj.mzdm;
        this.list[index].jgDzmc = obj.jgDzmc;
        this.list[index].gmsfhm = obj.gmsfhm;
        this.list[index].photos = obj.photos;
        // this.list[index].lableIds = obj.lableIds
      } else {
        this.list[index].type = "video";
      }
    },
    // 查询
    searchForm(form) {
      this.keyWords = ""; // 只要用户点击了查询，则之后的查询不再携带keyWords参数
      const { idcardNo, similarity, features, isRealName } = form;
      this.pageForm = {
        ...form,
        dataType: 2,
        archiveNo: idcardNo,
        features: features,
        similarity: similarity ? (similarity / 100).toFixed(2) : "",
        isRealName,
      };
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
// @import "~@/views/holographic-archives/style/page-hide.less";

.person {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
    // .video-card {
    //   transform: rotateY(180deg);
    // }
    // .video-card {
    //   .list-card {
    //     transform: rotateY(-180deg);
    //   }
    // }
  }
}
</style>
