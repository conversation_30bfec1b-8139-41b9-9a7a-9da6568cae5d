import global from '@/util/global';
export const macTableColumn = [
  {
    title: '序号',
    width: 50,
    type: 'selection',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '安装地址',
    key: 'address',
    align: 'left',
    tooltip: true,
  },
  {
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    align: 'left',
    tooltip: true,
  },
  {
    title: '治理结果',
    slot: 'taskStatusSlot',
    align: 'left',
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'toViewDevice',
    align: 'center',
    fixed: 'right',
    width: 60,
  },
];
export const pointTableColumn = [
  {
    title: '序号',
    width: 50,
    type: 'selection',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '安装地址',
    key: 'address',
    align: 'left',
    tooltip: true,
  },
  {
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlxText',
    align: 'left',
    tooltip: true,
  },
  {
    title: '治理结果',
    slot: 'taskStatusSlot',
    align: 'left',
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'toSetRecord',
    align: 'center',
    fixed: 'right',
  },
];
export const funcTableColumn = [
  {
    width: 50,
    title: '序号',
    type: 'selection',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    align: 'left',
    tooltip: true,
  },
  {
    title: '治理结果',
    slot: 'taskStatusSlot',
    align: 'left',
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'toSetRecord',
    align: 'center',
    fixed: 'right',
    width: 100,
  },
];
export const deviceTableColumn = [
  {
    width: 50,
    title: '序号',
    type: 'selection',
    align: 'center',
  },
  {
    width: 180,
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    minWidth: 180,
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 180,
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 130,
    title: global.filedEnum.ipAddr,
    key: 'ipAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: '设备厂商名称',
    key: 'manufacturer',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '设备规格型号',
    key: 'model',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '设备在线状态',
    key: 'isOnlineText',
    slot: 'isOnlineText',
    align: 'left',
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'toViewDevice',
    align: 'center',
    fixed: 'right',
    width: 60,
  },
];
export const clockTableColumn = [
  {
    width: 50,
    title: '序号',
    type: 'selection',
    align: 'center',
  },
  {
    minWidth: 180,
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    minWidth: 180,
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
  },
  {
    minWidth: 180,
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 130,
    title: `${global.filedEnum.ipAddr}`,
    key: 'ipAddr',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 80,
    title: '端口号',
    key: 'port',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 130,
    title: '账号',
    key: 'userId',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 130,
    title: '密码',
    key: 'password',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 100,
    title: '治理结果',
    slot: 'taskStatusSlot',
    align: 'left',
  },
  {
    minWidth: 60,
    title: '操作',
    slot: 'refreshClockSlot',
    align: 'center',
    fixed: 'right',
  },
];
export const osdTableColumn = [
  {
    width: 50,
    title: '序号',
    type: 'selection',
    align: 'center',
  },
  {
    width: 180,
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    minWidth: 180,
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 180,
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 130,
    title: global.filedEnum.ipAddr,
    key: 'ipAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: `${global.filedEnum.phyStatus}`,
    key: 'ptzStatus',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '设备类型',
    key: 'ptzType',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '安装位置',
    key: 'roomType',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '摄像机用途',
    key: 'useType',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '监视方位',
    key: 'directionType',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '安装地址',
    key: 'address',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: '设备厂商名称',
    key: 'manufacturer',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: '设备规格型号',
    key: 'model',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '设备在线状态',
    key: 'isOnlineText',
    slot: 'isOnlineText',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '治理结果',
    slot: 'taskStatusSlot',
    align: 'left',
  },
  {
    title: '操作',
    slot: 'refreshClockSlot',
    align: 'center',
    fixed: 'right',
    width: 60,
  },
];
export const ringColorEnum = {
  greenColor: 'greenColor',
  redColor: 'redColor',
  yellowColor: 'yellowColor',
  blueColor: 'blueColor',
};

export const deviceStatusColumn = [
  {
    width: 50,
    title: '序号',
    type: 'selection',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    tooltip: true,
    align: 'left',
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '所属单位',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '设备状态（最新）',
    key: 'phyStatusText',
    align: 'left',
    tooltip: true,
  },
  {
    title: '治理结果',
    slot: 'taskStatusSlot',
    align: 'left',
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'toSetRecord',
    align: 'center',
    fixed: 'right',
    width: 100,
  },
];
