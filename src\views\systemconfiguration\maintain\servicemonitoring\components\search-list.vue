<template>
  <div class="search-list">
    <ui-label class="inline mr-lg" label="关键词" :width="60">
      <Input v-model="searchData.keyword" class="width-lg" placeholder="请输入IP地址、服务器名称"></Input>
    </ui-label>
    <ui-label label="设备器节点" :width="85" class="inline mr-lg">
      <Select
        class="width-md"
        v-model="searchData.serverId"
        placeholder="请选择设备器节点"
        clearable
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in serverList" :key="index" :value="item.serverId">{{ item.serverName }} </Option>
      </Select>
    </ui-label>
    <ui-label label="服务状态" :width="75" class="inline mr-lg">
      <Select class="width-md" v-model="searchData.status" placeholder="请选择服务状态" clearable :max-tag-count="1">
        <Option v-for="(item, index) in statusList" :key="index" :value="item.value">{{ item.label }} </Option>
      </Select>
    </ui-label>
    <div class="inline">
      <Button type="primary" @click="search">查询</Button>
      <Button class="ml-sm" @click="reset">重置</Button>
    </div>
  </div>
</template>

<script>
import maintain from '@/config/api/maintain';
export default {
  name: 'search-list',
  props: {},
  data() {
    return {
      searchData: {
        keyword: '',
        status: '',
        serverId: '',
      },
      statusList: [
        { label: '在线', value: 1 },
        { label: '离线', value: 0 },
      ],
      serverList: [],
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
    this.initServerList();
  },
  methods: {
    search() {
      this.$emit('startSearch', this.searchData);
    },
    reset() {
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('startSearch', this.searchData);
      });
    },
    async initServerList() {
      try {
        let res = await this.$http.post(maintain.getServerList, {});
        this.serverList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.search-list {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
</style>
