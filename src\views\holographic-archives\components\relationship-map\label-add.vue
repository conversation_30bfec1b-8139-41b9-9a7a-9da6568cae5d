<template>
  <custom-modal
    v-model="modalShow"
    :r-width="dialogData.rWidth"
    :title="dialogData.title"
    list-content
    @onOk="confirmHandle"
    @onCancel="handleCancel"
    :footer="true"
  >
    <div class="select-label-container modal-list-has-footer-content">
      <div class="select-label-content">
        <div>
          <Form
            ref="queryParam"
            :model="queryParam"
            inline
            class="search-lable"
          >
            <FormItem label="标签名称" prop="name" class="search-input mr20">
              <Input
                v-model="queryParam.name"
                maxlength="50"
                type="text"
                placeholder="请输入"
              ></Input>
            </FormItem>
            <!-- <FormItem label="查询方式" prop="type" class="search-input">
              <Select v-model="queryParam.type" placeholder="请选择" style="width: 80px;">
                <Option value="1">并集</Option>
                <Option value="2">交集</Option>
              </Select>
            </FormItem> -->
            <!-- <FormItem label="标签类型" prop="type" class="search-input">
              <Input
                v-model="queryParam.name"
                maxlength="50"
                type="text"
                placeholder="请输入"
              ></Input>
              <Select v-model="queryParam.type" placeholder="请选择">
                <Option
                  v-for="item in labelTypeList"
                  :value="item.dataKey"
                  :key="item.dataKey"
                >
                  {{ item.dataValue }}
                </Option>
              </Select>
            </FormItem> -->
            <FormItem
              label="字母筛选"
              prop="pinyin"
              class="search-input not-mb"
            >
              <div class="letter-ul">
                <span
                  v-for="(item, $index) in letterArray"
                  :key="$index"
                  :class="[
                    'letter-li',
                    queryParam.pinyin === item ? 'letter-li-active' : '',
                  ]"
                  @click="changeLabelLetter(item)"
                  >{{ item }}</span
                >
              </div>
            </FormItem>
            <FormItem class="btn-group">
              <Button type="primary" @click="labelSearchHandle">查询</Button>
              <Button type="default" @click="labelResetHandle">重置</Button>
            </FormItem>
          </Form>
        </div>
        <div class="label-head">{{ queryParam.pinyin || "全部" }}</div>
        <div class="label-container">
          <div ref="labelUl" class="label-ul">
            <UiTag
              v-for="item in labelList"
              :key="item.id"
              :color="item.labelColor"
              :id="item.id"
              :checked="item.checked"
              :data="item"
              checkable
              multiple
              @on-change="tagChangeHandle"
              >{{ item.labelName }}
            </UiTag>
          </div>
          <ui-empty v-if="!labelList.length && !labelLoading"></ui-empty>
          <ui-loading v-if="labelLoading"></ui-loading>
        </div>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span
            >已经选：<font>{{ checkedLabelList.length }}</font> 个</span
          >
          <span class="del-btn" @click="removeAllHandle"
            ><font class="iconfont icon-shanchu"></font>清空</span
          >
        </div>
        <div class="label-container">
          <UiTag
            v-for="(item, $index) in checkedLabelList"
            :key="$index"
            :color="item.labelColor"
            :id="item.id"
            closable
            @on-close="closePreviewLabelHandle(item)"
            >{{ item.labelName }}
          </UiTag>
        </div>
      </div>
    </div>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="confirmHandle" :loading="saving"
        >确定</Button
      >
    </div>
  </custom-modal>
</template>
<script>
import { queryPageList, getLabelGroupAllList } from "@/api/labelPool";
import { mapActions, mapGetters } from "vuex";
import customModal from "@/components/modal";
const defaultQueryParam = {
  level: "",
  name: "",
  pinyin: "",
  property: "",
  type: "",
};
export default {
  components: {
    UiTag: require("./ui-tag.vue").default,
    customModal,
  },
  props: {
    // 档案号
    archiveNo: {
      type: String,
      default: "",
    },
    // 标签类型：1-设备，2-人员，3-物品，4-事件，5-场所，6-车辆
    labelType: {
      type: Number,
      default: 0,
    },
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      modalShow: false,
      saving: false,
      dialogData: {
        title: "选择标签",
        rWidth: 1060,
      },
      tabs: ["标签", "标签组"],
      tabIndex: 0,
      queryParam: { ...defaultQueryParam },
      labelGroupAllList: [],
      letterArray: [
        "全部",
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
        "U",
        "V",
        "W",
        "X",
        "Y",
        "Z",
      ],
      labelList: [],
      labelLoading: false,
      checkedLabelList: [],
      labelObj: "",
    };
  },
  computed: {
    ...mapGetters({
      // labelTypeList: "dictionary/getLabelTypeList",
      // labelLevelList: "dictionary/getLabelLevelList",
      // labelPropertyList: "dictionary/getLabelPropertyList",
    }),
  },
  watch: {
    // checkedLabels(val) {
    //     this.checkedLabelList = val;
    // },
    "queryParam.pinyin"(val) {
      this.requestLabelList();
    },
    checkedLabelList: {
      handler(n) {
        this.labelList = this.labelList.map((item) => {
          item.checked = this.checkedLabelList.some((d) => d.id === item.id);
          return item;
        });
      },
      immediate: true,
    },
  },
  created() {},
  methods: {
    ...mapActions({
      // getDictData: "dictionary/getDictAllData",
    }),
    async init(checkList = [], clearCondition) {
      // this.removeAllHandle()  // 清空上次已选择数据
      clearCondition && this.$refs.queryParam.resetFields();
      // this.ObjCode = code || "";
      this.modalShow = true;
      // 已选择的标签 - 回显
      this.checkedLabelList = JSON.parse(JSON.stringify(checkList));
      // 获取可选择的标签
      await this.requestLabelList();
      // this.getLabelGroupList();
      if (this.checkedLabels.length === 0) {
        this.queryParam.pinyin = "全部";
      }
    },
    // 获取标签列表
    requestLabelList() {
      this.$nextTick(() => {
        var code = "";
        if ([1, 2, 5, 6].includes(this.labelType)) {
          code = this.archiveNo;
        } else {
          code = this.archiveNo ? JSON.parse(this.archiveNo) : "";
        }
        this.labelLoading = true;
        this.$refs.labelUl.scrollTop = 0;
        // const resultQueryParam = { ...this.queryParam };
        // resultQueryParam.codeNotContain = [this.ObjCode];
        // resultQueryParam.pinyin = this.queryParam.pinyin === "全部" ? "" : resultQueryParam.pinyin;
        var param = {
          labelPinyin:
            this.queryParam.pinyin == "全部" ? "" : this.queryParam.pinyin,
          // notContainCode: code, // 当前档案没有的标签，回显不传
          dataType: this.labelType,
          pageNumber: 1,
          pageSize: 2000,
          labelName: this.queryParam.name,
        };
        queryPageList(param)
          .then((res) => {
            // TODO:此处可能出现性能问题，需优化
            this.labelList = res.data.entities.map((item) => {
              if (this.checkedLabelList.length > 0) {
                item.checked = this.checkedLabelList.some(
                  (d) => d.id === item.id
                );
              }

              return item;
            });
          })
          .catch((res) => {
            console.error(res);
          })
          .finally(() => {
            this.labelLoading = false;
          });
      });
    },
    // 获取标签组列表
    getLabelGroupList() {
      return new Promise((resolve) => {
        getLabelGroupAllList().then((res) => {
          this.labelGroupAllList = res.data;
          resolve(res.data);
        });
      });
    },
    changeLabelLetter(name) {
      this.queryParam.pinyin = name;
    },
    changeLabelGroupLetter(name) {
      this.labelGroupSearchData.pinyin = name;
    },
    // 控制标签组标签列表显示

    // 标签查询
    labelSearchHandle() {
      this.requestLabelList();
    },
    // 标签重置
    labelResetHandle() {
      this.$refs.queryParam.resetFields();
      this.queryParam.pinyin = "全部";
      this.requestLabelList();
    },
    // 标签的选择、取消
    tagChangeHandle(isChecked, row) {
      if (isChecked) {
        const isSelected = this.checkedLabelList.some((d) => d.id === row.id);
        if (!isSelected) {
          this.checkedLabelList.push(row);
        }
      } else {
        const itemIndex = this.checkedLabelList.findIndex(
          (d) => d.id === row.id
        );
        this.checkedLabelList.splice(itemIndex, 1);
      }
    },
    // 关闭标签
    closePreviewLabelHandle(item) {
      const itemIndex = this.checkedLabelList.findIndex(
        (d) => d.id === item.id
      );
      this.checkedLabelList.splice(itemIndex, 1);
    },
    removeAllHandle() {
      this.checkedLabelList = [];
    },
    // 确定
    confirmHandle($event) {
      $event.stopPropagation();
      let list = this.checkedLabelList.map((item) => item);
      if (this.archiveNo) {
        // 当有archiveNo时，需要父组件调用接口，所以等父组件请求接口结束后关闭弹框
        this.saving = true;
      } else {
        // 将标签作为查询条件
        this.modalShow = false;
      }
      this.$emit("setCheckedLabel", list);
    },
    // 取消
    handleCancel($event) {
      $event.stopPropagation();
      this.checkedLabelList = [];
      this.modalShow = false;
    },

    /**
     * @description: 关闭弹框，父组件调用
     */
    closeModal() {
      this.saving = false;
      this.modalShow = false;
    },
  },
};
</script>
<style lang="less" scoped>
.select-label-container {
  --right-label-width: 250px;
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 20px 20px;
  display: flex;
  position: relative;
  .select-label-content {
    width: calc(~"100% - var(--right-label-width)");
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
    .label-container {
      height: 400px;
    }
  }
  .search-lable {
    .search-input {
      display: inline-flex;
      margin-right: 0;
      &.not-mb {
        margin-bottom: 10px;
      }
      /deep/ .ivu-form-item-label {
        width: 70px;
        white-space: nowrap;
      }
    }
    .letter-ul {
      font-size: 16px;
      height: 34px;
      display: flex;
      align-items: center;
      .letter-li {
        cursor: pointer;
        padding: 5px;
        white-space: nowrap;
        line-height: 12px;
      }
      .letter-li-active {
        color: #2b84e2;
      }
    }
  }
  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    position: relative;
  }
  .preview-select-label-content {
    width: var(--right-label-width);
    position: absolute;
    right: 0;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
      flex: 1;
    }
  }
}
.del-btn {
  cursor: pointer;
}
.mr20 {
  margin-right: 20px !important;
}
</style>
