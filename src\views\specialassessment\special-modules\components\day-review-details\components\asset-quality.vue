<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition :form-item-data="formItemData" :form-data="formData" @search="search()" @reset="reset()">
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <div class="select-label">
        <span class="mr-sm">异常原因</span>
        <ui-select-tabs ref="uiSelectTabs" class="tabs-ui" @selectInfo="selectInfo" :list="selectTabs"></ui-select-tabs>
      </div>
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #phyStatus="{ row }">
        <span v-if="row.phyStatusText" :class="row.phyStatus === '1' ? 'qualified-color' : 'unqualified-color'">
          {{ row.phyStatusText }}
        </span>
        <span v-else>--</span>
      </template>
      <template slot="qualified" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-chakanyichangxiangqing"
          class="mr-sm"
          content="查看不合格原因"
          :styles="{ color: 'var(--color-primary)' }"
          @click.native="checkReason(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <slot name="page"></slot>
    <nonconformance
      ref="nonconformance"
      title="检测不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonLoading="reasonLoading"
    ></nonconformance>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'asset-quality',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      styles: {
        width: '9.45rem',
      },
      formData: {},
      reasonTableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      reasonLoading: false,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      selectTabs: [],
      errorMessages: [],
    };
  },
  created() {
    this.getSelectTabs();
  },
  methods: {
    // 不合格原因
    checkReason(row) {
      this.getReason(row.deviceInfoId);
      this.$refs.nonconformance.init();
    },
    async getReason(deviceInfoId) {
      this.reasonLoading = true;
      let params = {
        indexId: this.activeIndexItem.indexId,
        batchId: this.activeIndexItem.batchId,
        displayType: 'REGION',
        orgRegionCode: this.activeIndexItem.civilCode,
        customParameters: { deviceInfoId },
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getSecondaryPopUpData,params, 'post',this.$route.query.cascadeId, {})
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas || [];
        // this.reasonPage.totalCount = datas.total
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
    handlePopperShow(e, row) {
      this.$set(row, 'visible', true);
    },
    handlePopperHide(e, row) {
      this.$set(row, 'visible', false);
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let { statisticsCode } = this.$route.query;
        let { batchId, indexId, code } = this.activeIndexItem;
        let params = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        params.orgRegionCode = statisticsCode === '3' ? null : code;
        params.tagCodes = statisticsCode === '3' ? [code] : null;

        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getAbnormalLabel,params, 'post',this.$route.query.cascadeId, {})
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = data.map((item) => {
          return {
            name: item,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectInfo(infoList) {
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.formData.errorMessages = this.errorMessages;
      this.search();
    },
    search() {
      this.$emit('startSearch', this.formData);
    },
    reset() {
      this.$refs.uiSelectTabs.reset();
      this.$emit('startSearch', this.formData);
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    nonconformance:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/BasicAccuracy/components/nonconformance.vue')
        .default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tabs-ui {
  flex: 1;
}
.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }
}
.unqualified-color {
  color: #ea4a36;
}
.qualified-color {
  color: #1faf81;
}
.select-label {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 82px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  .ui-select-tabs {
    flex: 1;
    margin: 0 10px;
  }
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
</style>
<style lang="less">
.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }

  .ivu-tooltip-arrow {
    border-bottom-color: rgba(0, 21, 41, 0.15) !important;
  }
}
</style>
