<template>
  <div style="height: 100%; width: 100%">
    <div class="darktime-out" v-if="list.length > 0">
      <div
        class="darktime-item"
        v-for="(item, index) in list"
        @click="handleDetailFn(item, index)"
      >
        <div class="left-image-box">
          <ui-image :src="item.traitImg"></ui-image>
        </div>
        <div class="right-text-box">
          <div class="top-content">
            <div class="name-item">
              <span class="mian-title" :title="item.name"
                >{{ item.name || "--" }} &nbsp;</span
              >
              <span class="sub-title">{{ item.age || 0 }}岁</span
              ><span class="tag">（{{ item.noAppearDayNumber || 0 }}天）</span>
            </div>
            <div class="location">
              居住地：<span class="primary" :title="item.communityName">{{
                item.communityName || "--"
              }}</span>
            </div>
          </div>

          <div class="bottom-content">
            <div class="location">
              <i class="iconfont icon-location"></i>
              {{ item.deviceName || "--" }}
            </div>
            <div class="time">
              <i class="iconfont icon-time"></i>
              {{ item.absTime || "--" }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="list.length == 0"></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="list"
    ></CaptureDetail>
  </div>
</template>

<script>
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
export default {
  name: "nightOut",
  components: {
    CaptureDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      imageTest: require("@/assets/img/face.png") || "",
      selectIdCard: "",
      tableIndex: -1,
    };
  },
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.selectIdCard = item.idCard;
      this.$refs.videoDetail.showList(index);
    },
  },
};
</script>

<style lang="less" scoped>
.darktime-out {
  height: 100%;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;

  .darktime-item {
    display: flex;
    font-size: 16px;
    font-weight: bold;
    background: #f9f9f9;
    padding: 2px;
    cursor: pointer;
    .left-image-box {
      width: 48px;
      height: 48px;
      margin-right: 12px;
    }
    .right-text-box {
      flex: 1;
      .top-content {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px #d3d7de dashed;
        padding: 2px;
        .name-item {
          font-size: 16px;
          align-items: center;
          display: flex;
          .mian-title {
            color: rgba(0, 0, 0, 0.9);
            max-width: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
          }
          .sub-title {
            color: rgba(0, 0, 0, 0.6);
          }
          .tag {
            font-size: 14px;
            color: #ea4a36;
            font-weight: bold;
          }
          .location {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.75);
          }
        }
        .location {
          max-width: 180px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .bottom-content {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        padding: 1px;
        i {
          font-size: 12px;
        }
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
}
</style>
