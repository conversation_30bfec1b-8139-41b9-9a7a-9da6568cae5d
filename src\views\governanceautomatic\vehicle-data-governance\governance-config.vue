<template>
  <ui-modal v-model="visible" title="治理配置" :styles="styles">
    <div class="config-item">
      <p>图像不可访问治理</p>
      <div class="config-wrap mb-md">
        <span class="config-title">小图不可访问</span>
        <div class="config-content">
          <div class="width-full t-right">
            <i-switch v-model="configInfo.methodData.urlAvailable.checkFacePath" />
          </div>
        </div>
      </div>
      <div class="config-wrap">
        <span class="config-title">大图不可访问</span>
        <div class="config-content">
          <div class="width-full t-right">
            <i-switch v-model="configInfo.methodData.urlAvailable.checkScenePath" />
          </div>
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>低质量图像治理</p>
      <div class="config-wrap mb-md">
        <span class="config-title">低质量检测</span>
        <div class="config-content">
          <ui-label label="阈值 : ≤ ">
            <Input class="width-xs" v-model="configInfo.methodData.vqdDetect.threshold" disabled></Input>
            <span class="ml-sm">分</span>
          </ui-label>
          <ui-label label="算法配置 :">
            <RadioGroup v-model="configInfo.methodData.vqdDetect.videoQualityDetectMode">
              <Radio v-for="(item, index) in vqdDetectAlgorithmList" :key="index" :label="item.dataKey" disabled>
                {{ item.dataValue }}
              </Radio>
            </RadioGroup>
          </ui-label>
          <i-switch v-model="configInfo.methodData.vqdDetect.available" disabled />
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>重复图像治理</p>
      <div class="config-wrap">
        <span class="config-title flex-start">数据范围</span>
        <div class="config-content">
          <div>
            <Input class="width-xs" v-model="configInfo.methodData.imageRepeat.effectiveTime"></Input>
            <span class="ml-sm">时</span>
          </div>
          <i-switch v-model="configInfo.methodData.imageRepeat.available" />
          <p class="width-full mt-md tips">
            说明：在配置的数据范围内进行重复图像检测治理，最大支持2小时的数据重复治理。
          </p>
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>抓拍时间异常图像治理</p>
      <div class="config-wrap">
        <div class="config-title">
          <div class="t-left">抓拍时间和接收时间允许时间误差</div>
        </div>
        <div class="config-content">
          <div>
            <Input class="width-xs" v-model="configInfo.methodData.shotTime.timeInterval"></Input>
            <span class="ml-sm">秒</span>
          </div>
          <i-switch v-model="configInfo.methodData.shotTime.available" />
        </div>
      </div>
    </div>
    <div class="config-item">
      <p>结构化属性治理</p>
      <div
        class="config-wrap mb-md"
        v-for="(item, index) in configInfo.methodData.vehicleStructure.properties"
        :key="index"
      >
        <span class="config-title">{{ item.cnName }}</span>
        <div class="config-content">
          <div class="inline">
            <Checkbox
              class="mr-lg"
              v-for="(itm, index) in governanceList"
              :key="index"
              :label="item.property"
              v-model="item[itm.dataKey]"
            >
              {{ itm.dataValue }}
            </Checkbox>
          </div>
          <i-switch v-model="item.available" />
        </div>
      </div>
      <div class="config-wrap">
        <span class="config-title">算法配置</span>
        <div class="config-content">
          <CheckboxGroup
            class="inline algorithm-list"
            v-model="configInfo.methodData.vehicleStructure.algorithmTypeList"
          >
            <Checkbox class="mb-sm" v-for="(item, index) in algorithmList" :key="index" :label="item.dataKey">
              {{ item.dataValue }}
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </div>
    <template #footer>
      <Button @click="visible = false"> 取 消</Button>
      <Button class="ml-sm" type="primary" :loading="submitLoading" @click="query"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import imageDataGovernance from '@/config/api/image-data-governance';
export default {
  props: {
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      submitLoading: false,
      styles: {
        width: '5rem',
      },
      vqdDetectAlgorithmList: [],
      governanceList: [
        {
          dataKey: 'nullCheck',
          dataValue: '缺失检测',
        },
        {
          dataKey: 'nullCompletion',
          dataValue: '缺失补全',
        },
        {
          dataKey: 'errorCheck',
          dataValue: '错误检测',
        },
        {
          dataKey: 'errorCompletion',
          dataValue: '自动纠错',
        },
      ],
      configInfo: {
        type: 1,
        methodData: {
          urlAvailable: {
            checkFacePath: false,
            checkScenePath: false,
          },
          vqdDetect: {
            threshold: '',
            videoQualityDetectMode: '',
            available: false,
          },
          imageRepeat: {
            effectiveTime: '',
            available: false,
          },
          shotTime: {
            timeInterval: '',
            available: false,
          },
          vehicleStructure: {
            algorithmTypeList: [],
            properties: [
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'plateNo',
                cnName: '车牌号治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'plateColor',
                cnName: '车牌颜色治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleClass',
                cnName: '车辆类型治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleBrand',
                cnName: '车辆品牌治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleColor',
                cnName: '车身颜色治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleModel',
                cnName: '车辆型号治理',
                available: false,
              },
            ],
          },
        },
      },
    };
  },
  created() {
    this.getAlldicData();
    this.getVideoQualityDetectMode();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async getVideoQualityDetectMode() {
      try {
        const res = await this.$http.get(imageDataGovernance.getVideoQualityDetectMode);
        this.vqdDetectAlgorithmList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      await this.getDataTask();
    },
    async getDataTask() {
      try {
        const res = await this.$http.get(imageDataGovernance.getDataTask, {
          params: {
            id: 2,
          },
        });
        const extensionData = res.data.data.extensionData;
        if (extensionData) {
          const parseData = JSON.parse(extensionData).methodData;
          // 后端没有保存开启状态，需要前端开启，所以要处理后端返回的数据赋值到configInfo上
          Object.keys(parseData).forEach((keys) => {
            if (keys === 'vehicleStructure') {
              this.configInfo.methodData[keys].algorithmTypeList = parseData[keys].algorithmTypeList;
              this.configInfo.methodData[keys].properties.forEach((row) => {
                const property = parseData[keys].properties.find((temp) => row.property === temp.property);
                Object.assign(row, property);
              });
            } else {
              this.configInfo.methodData[keys] = parseData[keys];
            }
          });
        }
      } catch (err) {
        console.log(err);
      }
    },
    async query() {
      try {
        this.submitLoading = true;
        const params = {
          id: 2,
          extensionData: this.dealData(),
        };
        const res = await this.$http.post(imageDataGovernance.updateDataTask, params);
        this.$Message.success(res.data.msg);
        this.visible = false;
      } catch (err) {
        console.log(err, 'err');
      } finally {
        this.submitLoading = false;
      }
    },
    dealData() {
      const params = {
        type: 1,
        methodData: {},
      };
      // 后端没有保存开启状态，如果没有开启则不需要把参数传给后端
      Object.keys(this.configInfo.methodData).forEach((key) => {
        if (this.configInfo.methodData[key]?.available) {
          params.methodData[key] = Object.assign({}, this.configInfo.methodData[key]);
        } else if (this.configInfo.methodData[key]?.available === undefined) {
          if (this.configInfo.methodData[key]?.properties) {
            this.configInfo.methodData[key].properties.forEach((row) => {
              if (row.available) {
                if (params.methodData[key]?.properties) {
                  params.methodData[key].properties = [...params.methodData[key].properties, row];
                } else {
                  params.methodData[key] = {
                    algorithmTypeList: this.configInfo.methodData[key].algorithmTypeList,
                    properties: [row],
                  };
                }
              }
            });
          } else {
            params.methodData[key] = Object.assign({}, this.configInfo.methodData[key]);
          }
        }
      });
      console.log(params, 'params');
      return JSON.stringify(params);
    },
    reset() {
      this.configInfo = {
        type: 1,
        methodData: {
          urlAvailable: {
            checkFacePath: false,
            checkScenePath: false,
          },
          vqdDetect: {
            threshold: '',
            videoQualityDetectMode: '',
            available: false,
          },
          fullImageExtract: {
            algorithmTypeList: [],
            available: false,
          },
          imageRepeat: {
            effectiveTime: '',
            available: false,
          },
          shotTime: {
            timeInterval: '',
            available: false,
          },
          vehicleStructure: {
            algorithmTypeList: [],
            properties: [
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'plateNo',
                cnName: '车牌号治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'plateColor',
                cnName: '车牌颜色治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleClass',
                cnName: '车辆类型治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleBrand',
                cnName: '车辆品牌治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleColor',
                cnName: '车身颜色治理',
                available: false,
              },
              {
                errorCheck: false,
                errorCompletion: false,
                nullCheck: false,
                nullCompletion: false,
                property: 'vehicleModel',
                cnName: '车辆型号治理',
                available: false,
              },
            ],
          },
        },
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        this.reset();
        this.init();
      }
      this.visible = val;
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
  },
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  height: 750px;
  width: 900px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  margin-left: 50%;
  transform: translateX(-50%);
  padding-right: 30px;
}
.config-item {
  border-top: 1px dashed var(--devider-line);
  padding-top: 15px;
  margin-bottom: 25px;
  color: var(--color-content);
  &:first-child {
    border-top: none;
    padding-top: 0;
  }
  > p {
    color: var(--color-active);
    font-size: 16px;
    font-weight: 900;
  }
  .algorithm-list {
    width: 490px;
  }
  .flex-start {
    margin-top: -40px;
  }
  .config-wrap {
    margin-top: 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .config-title {
    width: 130px;
    text-align: right;
  }
  .config-content {
    width: calc(100% - 126px);
    background-color: var(--bg-form-item);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-left: 10px;
    padding: 10px 20px;
    > .width-full {
      width: 100%;
    }
    .tips {
      color: var(--color-warning);
    }
  }
}
</style>
