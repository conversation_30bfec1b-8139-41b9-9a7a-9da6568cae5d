const basePre = '/ivdg-asset-app';
export default {
  // 设备详情
  deviceDetailBatchCheck: basePre + '/receiveDeviceDetail/batchCheck', //批量检测
  deviceDetailBatchFile: basePre + '/receiveDeviceDetail/batchFile', //批量建档
  deviceDetailBatchIssued: basePre + '/receiveDeviceDetail/batchIssued', //批量下发
  deviceDetailList: basePre + '/receiveDeviceDetail/pageList', //查询资产管理：接收记录详情分页列表
  deviceDetailRemove: basePre + '/receiveDeviceDetail/remove/', //+{ids} 资产管理：接收记录详情删除
  deviceDetailInfo: basePre + '/receiveDeviceDetail/view/', //+{id}获取资产管理：接收记录详情详细信息
  deviceDetailInfoByDeviceId: basePre + '/receiveDeviceDetail/queryDeviceDetailByCode/', //+{deviceId}根据deviceId查询详情
  // 不合格原因
  deviceUnqualifiedList: basePre + '/receiveDeviceCheckResult/pageList', //查询资产管理：接收后检测完发布记录详情分页列表
  // 配置
  configQueryReceive: basePre + '/deviceCheckRule/queryReceive', //接收配置查询
  configQueryReceiveDetection: basePre + '/deviceCheckRule/queryRuleDetection', //接收配置查询
  configQueryAllData: basePre + '/deviceCheckRule/queryAllData', //字段规则展示左侧右侧数据
  configUpdateAllData: basePre + '/deviceCheckRule/updateAllData', //字段规则选择好后点击确定后保存数据库
  configSaveReceive: basePre + '/deviceCheckRule/saveReceive', //保存

  queryReceiveDeviceStatistics: basePre + '/receiveDeviceDetail/queryReceiveDeviceStatistics', //统计
  queryDistributeDetailPageList: basePre + '/receiveDeviceDistributeDetail/queryDistributeDetailPageList', //下发记录

  getDeviceInfoByDeviceCode: basePre + '/ivdgAsertCenter/info/getDeviceInfoByDeviceCode', //根据设备编码获取设备表信息
};
