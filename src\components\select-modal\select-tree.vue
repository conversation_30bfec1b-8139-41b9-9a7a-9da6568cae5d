<!--
    * @FileDescription: 树组件
    * @Author: H
    * @Date: 2023/4/18
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <el-tree ref="tree" class="tree" 
        node-key="id" 
        multiple 
        show-checkbox
        :style="treeStyle" 
        :data="treeData" 
        :props="defaultProps" 
        :expand-on-click-node="true" 
        :default-expanded-keys="defaultKeys" 
        :default-checked-keys="defaultChecked"
        :current-node-key="1"
        @node-click="handleNodeClick"
        @check-change="handleCheckChange"
        @check="handleCheck"
        @current-change="currentChange">
        <span class="custom-tree-node" slot-scope="{ node }">
            <span :class="{ 'nav-not-selected': node.disabled, allowed: node.disabled }">{{ node.label }}</span>
        </span>
    </el-tree>
</template>

<script>
export default {
    name: 'tree',
    props: {
        treeData: {
            type: Array,
            default: () => []
        },
        /**
         * selectTree.orgCode: 选中的值
         */
        selectTree: {
            required: true
        },
        // 树结构style
        treeStyle: {},
        placeholder: {
            type: String,
            default: () => {
                return '请选择'
            }
        },
        // 是否自定节点
        custormNode: {
            type: Boolean,
            default: false
        },
        // 自定义节点数据
        custormNodeData: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            defaultProps: {
                label: 'orgName',
                children: 'children'
            },
            defaultKeys: [],
            isClickTreeNode: true,
            defaultedLabel: '',
            defaultChecked: [],
        }
    },
    watch: {
        treeData(val) {
            if (val.length !== 0) {
                
            }
        }
    },
    computed: {
        styles() {
            return {
                fontSize: `${this.size / 192}rem`,
                color: this.color
            }
        }
    },
    methods: {
        init() {
            this.$refs.tree.setCheckedNodes(this.treeData);
            this.$emit('check', this.$refs.tree.getCheckedKeys())
        },
        resetCheck () {
            this.$refs.tree.setCheckedNodes([])
            this.$emit('check', this.$refs.tree.getCheckedKeys())
        },
        checkedKeys(data) {
            data.map(row => {
                this.defaultChecked.push(row.id)
                if(row.children && row.children.length > 0){
                    this.checkedKeys(row.children)
                }
            })
        },
        currentChange(item) {
        },

        handleNodeClick(item) {
        },
        handleCheckChange(item) {
        },
        handleCheck(item, checkData) {
            this.$emit('check', this.$refs.tree.getCheckedKeys())
            // console.log(this.getSimpleCheckedNodes(this.$refs.tree.store))
        },
        // 选中父级只传父级节点，子级全选传父级节点
        getSimpleCheckedNodes(store) {
            // 定义数组
            const checkedNodes = [];
            // 判断是否为全选，若为全选状态返回被全选的节点，不为全选状态正常返回被选中的节点
            const traverse = function (node) {
                const childNodes = node.root ? node.root.childNodes : node.childNodes;
                childNodes.forEach((child) => {
                if (child.checked) {
                    checkedNodes.push(child.data);
                }
                if (child.indeterminate) {
                    traverse(child);
                }
                });
            };
            traverse(store);
            return checkedNodes;
        },
    }
}
</script>

<style lang="less" scoped>
.select-width {
    width: 200px;
}
.tree {
    // min-width: 200px;
    overflow-x: auto;
    font-size: 12px;
    max-height: 500px;
    margin-right: 10px;
}
.custorm-tree-node {
    width: 100%;
    padding: 0 0.03125rem;
    color: #ffffff;
    cursor: pointer;
    &:hover {
        // background: #041129;
    }
}
</style>

