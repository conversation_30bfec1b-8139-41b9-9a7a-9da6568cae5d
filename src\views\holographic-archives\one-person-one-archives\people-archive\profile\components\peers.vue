<template>
  <ui-card :title="title" class="peers">
    <div class="portrait-capture-content">
      <template v-for="(item, index) in tableList">
        <div class="content-item" :key="index" @click="peerDetail(item)">
          <div class="img-content">
            <i v-if="item.idCardNo" class="badge bg-primary">身</i>
            <ui-image type='car' :src="item.traitImg || item.img" alt="动态库" />
            <div v-if="type === 'car'">
              <span class="plate-no">
                <span>{{ item.name ? item.name : '未知' }}</span>
              </span>
            </div>
            <div v-else>{{ item.name ? item.name : '未知' }}</div>
          </div>
          <div class="bottom-info">
            <p class="info">
              近一月同行抓拍<span class="color-primary">{{ item.num }}</span
              >次
            </p>
            <time>{{ item.time }}</time>
          </div>
        </div>
      </template>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!tableList || !tableList.length) && !loading" />
    <ui-modal v-model="detailShow" footer-hide>
      <template #header>
        <div class="detail-title">
            <p>同行 <span> {{ total }} </span> 次</p>
        </div>
      </template>
      <ul class="peer-content-box" v-infinite-scroll="load" :infinite-scroll-immediate='false'>
        <li class="content-box-li" v-for="(item, index) in peerList" :key='index'>
            <div class="box-li-icon">{{ index + 1 }}</div>
            <div class="box-li-right">
                <p class="box-li-right-title">{{ item.deviceName || '--' }}</p>
                <ul class="box-li-right-content">
                    <li>
                        <div class="right-img-list">
                            <ui-image type='car' :src="item.ferriteDetailVo.traitImg || item.ferriteDetailVo.sceneImg" alt="" />
                            <p class="right-img-list-tag tag-bule">对象</p>
                        </div>
                        <p class="box-li-right-time">{{ item.ferriteDetailVo.absTime || '--'}}</p>
                    </li>
                    <li @click="peerMessage(item)">
                        <div class="right-img-list">
                            <!-- <ui-image :src="item.ferriteDetailVo.traitImg" viewer /> -->
                            <ui-image type='car' :src="item[peerDetailVo[type]].traitImg || item[peerDetailVo[type]].sceneImg" alt="" />
                            <p class="right-img-list-tag tag-yellow">同行</p>
                            <div class="plateNumber">{{ showtext(item) }}</div>
                        </div>
                        <p class="box-li-right-time">{{ item[peerDetailVo[type]].absTime || '--'}}</p>
                    </li>
                </ul>
            </div>
        </li>
        <ui-empty v-if="peerList.length === 0 && loading== false"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
        <p class="loading" v-if="loadingText">加载中...</p>
        <p class="loading" v-if="noMore && peerList.length != 0">没有更多了</p>
    </ul>
    </ui-modal>
    <video-detail v-show="videoShow" class="faceDetail" ref="videoDetail" :cutShow='false' @close="handleClose($event,'video')" />
    <vehicle-detail v-show="vehicleShow" class="vehicleDetail" :cutShow='false' ref="vehicleDetail" @close="handleClose($event,'vehicle')" />
  </ui-card>
</template>

<script>
import { archivesQueryFaceCapturePageList, archivesQueryVehicleCapturePageList, queryFaceCapturePageList, queryVehicleCapturePageList } from '@/api/modelMarket';
import vehicleDetail from '@/components/detail/vehicle'
import videoDetail from '@/components/detail/video'
import getImageCropper from '@/mixins/mixinVehicleCrop'

export default {
  mixins:[getImageCropper],
  components: {
    vehicleDetail,
    videoDetail
  },
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'people'
    },
    archiveNo: {
      type: String | Number,
      default: ''
    },
  },
  data () {
      return {
          loading: false,
          tableList: [],
          total: 0,
          page: {
              "pageNumber": 1,
              "pageSize": 10,
          },
          peerList: [],
          detailShow: false,
          loadingText: false,
          noMore: false,
          detailRequest: {
              'people': queryFaceCapturePageList, // 人脸
              'car': queryVehicleCapturePageList, // 车辆
          },
          peerDetailVo : {
              'people': 'faceCapturePeerDetailVo',
              'car': 'vehiclePeerDetailVo',
          },
          detailParams: {},
          videoShow: false,
          vehicleShow: false,
      }
  },
  created() {
    this.init()
  },
  methods: {
    async init() {
      let res = null
      if(this.type === 'people') {
        res = await archivesQueryFaceCapturePageList({vid: this.$route.query.archiveNo})
        this.tableList = res.data.map(row=> {
          return {
            idCardNo: row.idCardNo,
            num: row.peerNum,
            img: row.sceneImg,
            time: row.absTime,
            name: row.name || row.vid,
            dateType: row.dateType,
            peerMinNumber: row.peerMinNumber,
            peerSecond: row.peerSecond,
            vid: row.vid,
            recordIdList: row.faceCaptureIdList
          }
        })
      } else {
        res= await archivesQueryVehicleCapturePageList({plateNo: JSON.parse(this.$route.query.plateNo)})
        this.tableList = res.data.map(row=> {
          return {
            ...row,
            num: row.peerNum,
            img: row.sceneImg,
            time: row.absTime,
            name: row.queryVid,
            dateType: row.dateType,
            peerMinNumber: row.peerMinNumber,
            peerSecond: row.peerSecond,
            vid: row.queryVid,
            recordIdList: row.vehicleCaptureIdList
          }
        })
      }
    },
    peerDetail(item) {
      const {
        dateType,
        peerMinNumber,
        peerSecond,
        vid,
        recordIdList
      } = item
      this.detailShow = true
      this.detailParams = {
        dateType,
        peerMinNumber,
        peerSecond,
        [this.type === 'people' ? 'vid':'plateNo']: vid,
        recordIdList
      }
      this.rightList()
    },
    rightList() {
        let params = {
            ...this.page,
            ...this.detailParams
        };
        this.detailRequest[this.type](params)
        .then(res =>{
            /**
             * 统一 同行字段
             * 人脸： faceCapturePeerDetailVo
             * 车辆： vehiclePeerDetailVo
             */
            res.data && res.data.entities.map(item => {
                item.peerDetailVo = this.type === 'people' ? item.faceCapturePeerDetailVo : item.vehiclePeerDetailVo;
            })
            this.peerList = this.peerList.concat(...res.data.entities);
            this.total = res.data.total;
        })
        .finally(()=>{
            this.loading = false;
            this.loadingText = false;
        })
    },
    load() {
        let totalPage = Math.ceil(this.total / this.page.pageSize);
        if(this.total <= 10) {
            return
        };
        if (this.page.pageNumber >= totalPage) {
            this.noMore = true;
            setTimeout(() => {
                this.noMore = false;
            }, 1000)
            return
        } else {
            this.noMore = false;
            this.loadingText = true;
            this.page.pageNumber = this.page.pageNumber + 1;
            this.rightList()
        }
    },
    showtext(item) {
        let val = '--'
        if(this.type === 'people') {
            val = item[this.peerDetailVo[this.type]].name || '--';
        }else if(this.type === 'car') {
            val = item[this.peerDetailVo[this.type]].plateNo;
        }
        return val
    },
    peerMessage(row) {
      console.log(row, 'row')
      if(this.type === 'people') {
        this.videoShow = true
        this.$refs.videoDetail.init(row.faceCapturePeerDetailVo)
      } else {
        this.vehicleShow = true
        this.$refs.vehicleDetail.init(row.vehiclePeerDetailVo)
      }
    },
    handleClose(event, val) {
        event.stopPropagation();
        switch(val){
            case 'video':
                this.videoShow = false;
                break;
            case 'vehicle':
                this.vehicleShow = false;
                break;
        }
    },
  }

}
</script>

<style lang="less" scoped>
.peers {
  .portrait-capture-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .content-item {
      width: 187px;
      background: #f9f9f9;
      box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      box-sizing: border-box;
      padding: 10px;
      overflow: hidden;
      margin-left: 10px;
      cursor: pointer;
      &:nth-child(1) {
        margin-left: 0;
      }
      .img-content {
        width: 100%;
        height: 140px;
        overflow: hidden;
        position: relative;
        /deep/ .ui-image {
          width: 100%;
          height: 100%;
        }
        div {
          width: 100%;
          height: 30px;
          position: absolute;
          left: 0;
          bottom: 0;
          color: #fff;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 700;
        }
        .plate-no {
          display: inline-block;
          width: 84px;
          height: 22px;
          background: #2379F9;
          border-radius: 2px 2px 2px 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          span {
            width: 80px;
            height: 18px;
            border-radius: 2px 2px 2px 2px;
            border: 1px solid #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .badge {
          position: absolute;
          width: 20px;
          height: 20px;
          border-radius: 4px;
          left: 0;
          top: 0;
          color: #fff;
          font-size: 12px;
          line-height: 21px;
          text-align: center;
        }
      }
      .bottom-info {
        padding-top: 10px;
        color: #000;
        font-size: 12px;
        .info,
        time {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        time {
          display: inline-block;
          margin-top: 10px;
        }
      }
    }
  /deep/ .card-content {
    padding: 20px 15px !important;
    min-height: 240px;
    box-sizing: border-box;
  }

  /deep/ .ivu-modal-header {
    padding-left: 0 !important;
    background-color: #fff !important;
  }

  /deep/ .ivu-modal {
    top: -48px;
    left: 10px;
    margin: 0;
  }

  /deep/ .ivu-modal-body {
    height: 880px;
    overflow-y: auto;
  }
  /deep/ .dom-wrapper {
    position: fixed;
    top: 91px;
    left: 500px;
    height: auto;
    z-index: 1999;
  }
}
.peer-content-box{
  height: 640px;
  overflow: auto;
  position: relative;
  .content-box-li{
      margin-top: 10px;
      margin-bottom: 13px;
      display: flex;
      .box-li-icon{
          width: 24px;
          background: url('../../../../../../assets/img/archives/mark-red.png') no-repeat;
          background-size: 24px 28px;
          display: flex;
          justify-content: center;
          height: 30px;
          color: #EA4A36;
      }
      .box-li-right{
          flex: 1;
          margin-left: 12px;
          &-title{
              font-size: 12px;
              font-weight: bold;
              color: #181818;
          }
          &-content{
              background: #F9F9F9;
              border-radius: 4px;
              padding: 10px 9px 12px;
              display: flex;
              justify-content: space-between;
              margin-top: 5px;
              li{
                  display: flex;
                  flex-direction: column;
                  align-items: center;
              }
              .right-img-list{
                  position: relative;
                  width: 100px;
                  margin-bottom: 10px;
                  height: 100px;
                  img{
                      width: 100px;
                      height: 100px;
                      cursor: pointer;
                  }
                  &-tag{
                      // width: 40px;
                      // height: 20px;
                      border-radius: 0px 0px 4px 0px;
                      position: absolute;
                      top: 0;
                      color: #fff;
                      font-size: 12px;
                      padding: 1px 3px;
                      z-index: 20;
                  }
                  .tag-bule{
                      background: #2C86F8;
                  }
                  .tag-yellow{
                      background:#F29F4C;
                  }
                  .plateNumber{
                      position: absolute;
                      bottom: 0px;
                      left: 1px;
                      background: rgba(0, 0, 0, 0.6);
                      width: 98px;
                      text-align: center;
                      // opacity: 0.6;
                      color: #FFFFFF;
                      font-size: 14px;
                  }
              }
          }
          .box-li-right-time{
              font-size: 12px;
              color: rgba(0,0,0,0.6);
          }
      }
  }
}

.detail-title {
  font-size: 16px;
  font-weight: bold;
  color: rgba(0,0,0,0.9);
  position: relative;
  padding-left: 20px;
  border-bottom: 1px solid #D3D7DE;
  display: flex;
  justify-content: space-between;
  align-items: center;
  top: 0;
  // z-index: 999;
  background: #fff;
  &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 20px;
      top: 50%;
      transform: translateY(-50%);
      left: 10px;
      background: #2c86f8;
  }
  span{
      color: #2C86F8;
  }
}


</style>
