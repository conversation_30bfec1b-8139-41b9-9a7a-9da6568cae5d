<template>
  <div style="height: 100%">
    <listPage :listObj="listObj" :taskObj="taskObj" :currentTree="currentTree" @refeshCount="refeshCount">
      <template #action="{ row }">
        <ui-btn-tip
          class="mr-md"
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="mr-md"
          v-if="row.isScreenShot != 1 && row.detectionMode != 1"
          disabled
          icon="icon-chakanjietu"
          content="查看截图"
        ></ui-btn-tip>
        <ui-btn-tip
          v-else
          class="mr-md"
          icon="icon-chakanjietu"
          content="查看截图"
          @click.native="findPic(row)"
        ></ui-btn-tip>
      </template>
      <template #detectionMode="{ row }">
        {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
      </template>
      <template #outcome="{ row }">
        <span>{{ outcomeVal(row) }}</span>
        <Tooltip
          placement="bottom"
          v-if="
            row.detectionMode != null &&
            (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
          "
        >
          <i class="icon-font icon-wenhao ml-xs f-12"> </i>
          <div slot="content">
            <json-viewer
              :expand-depth="5"
              v-if="row.dateImageText != null"
              :value="JSON.parse(row.dateImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
            <json-viewer
              :expand-depth="5"
              v-if="row.additionalImageText != null"
              :value="JSON.parse(row.additionalImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
            <json-viewer
              :expand-depth="5"
              v-if="row.areaImageText != null"
              :value="JSON.parse(row.areaImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
          </div>
        </Tooltip>
      </template>
      <template #clockSkew="{ row }"> {{ row.clockSkew }} s </template>
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #reason="{ row }">
        <Tooltip :content="row.reason" transfer max-width="150">
          {{ row.reason }}
        </Tooltip>
      </template>
    </listPage>
    <!-- 大图组件 -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="margin-top: -15px">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </div>
</template>

<script>
import api from '@/config/api/inspectionrecord';
import vedio from '@/config/api/vedio-threm';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'commonPage',
  components: {
    listPage: require('./list').default,
    LookScene: require('@/components/look-scene').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {},
    },
    countInfo: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      bigPictureShow: false,
      imgList: [],
      listObj: {
        abnormalCount: [
          {
            title: '采集监控设备总数',
            count: '0',
            icon: 'icon-zhongdianshizhongzhunqueshuaijiance',
          },
          {
            title: '检测数量',
            count: '0',
            icon: 'icon-zhongdianshizhongzhunqueshuaijiance',
          },
          {
            title: '设备时钟不准确数量',
            count: '0',
            icon: 'icon-zhongdianshizhongzhunqueshuaijiance',
          },
          {
            title: '无法检测设备数量',
            countKey: '0',
            icon: 'icon-zhongdianshizhongzhunqueshuaijiance',
          },
        ],
        columns: [
          // { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName', tooltip: 'true' },
          // { title: '组织机构', key: 'orgName' },
          // { title: `${this.global.filedEnum.ipAddr}`, key: 'ipAddr' },
          { title: '检测方式', slot: 'detectionMode', width: 100 },
          { title: '检测结果', slot: 'outcome', width: 100 },
          { title: '检测时间', key: 'bjTime', width: '170' },
          { title: '对应设备时间', key: 'startTime', width: '170' },
          { title: '时间误差', slot: 'clockSkew', width: 100 },
          { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 100 },
          { title: '异常原因', key: 'reason', tooltip: 'true' },
          {
            width: 100,
            title: '操作',
            fixed: 'right',
            slot: 'action',
            align: 'left',
          },
        ],
        loadData: (parameter) => {
          const { indexId, batchId, regionCode: orgCode } = this.taskObj;
          const params = {
            indexId,
            batchId,
            ...parameter,
            orgCode: parameter.orgCode ? parameter.orgCode : orgCode,
          };
          return this.$http.post(api.queryEvaluationVideoPageList, params).then((res) => {
            return res.data;
          });
        },
      },
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
      videoVisible: false,
      loadingVideo: false,
      isLive: true,
    };
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    findPic(item) {
      this.imgList = [item.screenShot];
      this.bigPictureShow = true;
    },
    outcomeVal(row) {
      switch (row.outcome) {
        case '1':
          return '正常';
        case '2':
          return '异常';
        default:
          return '无法检测';
      }
    },
    refeshCount(orgCode) {
      this.$emit('refeshCount', orgCode);
    },
    async clickRow(row) {
      try {
        // this.isLive = true
        // this.loadingVideo = true
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        // this.loadingVideo = false
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
  },
  async mounted() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    setTimeout(() => {
      this.listObj.abnormalCount[0].count = this.countInfo.deviceCount;
      this.listObj.abnormalCount[1].count = this.countInfo.evaluatingCount;
      this.listObj.abnormalCount[2].count = this.countInfo.evaluatingFailedCount;
      this.listObj.abnormalCount[3].count = this.countInfo.unableEvaluatingCount;
    }, 500);
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    countInfo() {
      this.listObj.abnormalCount[0].count = this.countInfo.deviceCount;
      this.listObj.abnormalCount[1].count = this.countInfo.evaluatingCount;
      this.listObj.abnormalCount[2].count = this.countInfo.evaluatingFailedCount;
      this.listObj.abnormalCount[3].count = this.countInfo.unableEvaluatingCount;
    },
  },
};
</script>

<style lang="less" scoped>
.btn {
  margin: 0 6px;
}

.auto-fill {
  height: 100%;
}
.video-player {
  @{_deep} .ivu-modal-body {
    height: 530px;
    display: flex;
    flex-direction: column;
  }
}
/deep/ .el-loading-mask {
  background-color: #000 !important;
}
/deep/.el-loading-spinner {
  height: 0;
  left: 0;
  top: 50%;
  background: #000;
  i {
    font-size: 30px;
  }
}
</style>
