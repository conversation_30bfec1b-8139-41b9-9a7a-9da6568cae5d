<template>
  <div class="result-list-page">
    <p class="result-header">
      <Button class="ml-10" type="primary" size="small" @click="goback(0)"
        >返回</Button
      >
      <span
        >共
        <span class="t-blue-color">{{ pageParams.totalCount }}</span>
        条记录</span
      >
    </p>

    <div class="result-item">
      <div v-scroll style="height: 100%" v-if="resultList.length">
        <ul>
          <li
            class="result-item-li"
            v-for="(item, index) in resultList"
            :key="index"
            :class="{ active: currentIndex == index }"
            @click="selectItem(item, index)"
          >
            <template v-if="pageName == 'together'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.faceImg" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="同行人员视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.archiveNo">
                      {{ item.archiveNo }}
                    </span>
                  </li>
                  <li class="ellipsis" v-if="item.idCard">
                    <span
                      class="iconfont icon-idcard t-blue-color mr-5"
                      title="身份证号"
                    ></span>
                    <span :title="item.idCard">{{ item.idCard }}</span>
                  </li>
                  <li class="ellipsis" v-if="item.name">
                    <span
                      class="iconfont icon-info-circle t-blue-color mr-5"
                      title="姓名"
                    ></span>
                    <span :title="item.name">{{ item.name }}</span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                  <!-- <li class="ellipsis m-top10" v-show="mainVid">
                                        <span class="add-person">添加至关联人员</span>
                                    </li> -->
                </ul>
                <span
                  @click.stop="togetherAnalyse(item)"
                  title="同行分析"
                  class="iconfont icon-bansuitonghang"
                ></span>
              </div>
            </template>
            <template v-else-if="pageName == 'person-area'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.traitImg" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.vid">
                      {{ item.vid }}
                    </span>
                  </li>
                  <li class="ellipsis" v-if="item.idCard">
                    <span
                      class="iconfont icon-idcard t-blue-color mr-5"
                      title="身份证号"
                    ></span>
                    <span :title="item.idCard">{{ item.idCard }}</span>
                  </li>
                  <li class="ellipsis" v-if="item.name">
                    <span
                      class="iconfont icon-info-circle t-blue-color mr-5"
                      title="姓名"
                    ></span>
                    <span :title="item.name">{{ item.name }}</span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >共出现在<b style="color: #ec3f3f">{{
                        item.appearCount || 0
                      }}</b
                      >个区域</span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'vehicle-together'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.photoUrl" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chepai t-blue-color mr-5"
                      title="同行车辆车牌号码"
                    ></span>
                    <span class="t-blue-color" :title="item.plateNo">
                      {{ item.plateNo }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                </ul>
                <span
                  @click.stop="togetherAnalyse(item)"
                  title="跟车分析"
                  class="iconfont icon-cheliang"
                ></span>
              </div>
            </template>
            <template v-else-if="pageName == 'collide'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.traitImg" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.vid">
                      {{ item.vid }}
                    </span>
                  </li>

                  <li class="ellipsis">
                    <span
                      class="iconfont icon-time t-blue-color mr-5"
                      title="最后一次出现时间"
                    ></span>
                    <span class="t-blue-color" :title="item.lastLogTime">
                      {{ item.lastLogTime }}
                    </span>
                  </li>

                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="最后一次出现地点"
                    ></span>
                    <span class="t-blue-color" :title="item.lastDeviceName">
                      {{ item.lastDeviceName }}
                    </span>
                  </li>

                  <li class="ellipsis" v-if="item.idCard">
                    <span
                      class="iconfont icon-idcard t-blue-color mr-5"
                      title="身份证号"
                    ></span>
                    <span :title="item.idCard">{{ item.idCard }}</span>
                  </li>
                  <li class="ellipsis" v-if="item.name">
                    <span
                      class="iconfont icon-info-circle t-blue-color mr-5"
                      title="姓名"
                    ></span>
                    <span :title="item.name">{{ item.name }}</span>
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'vehicle-collide'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.traitImg" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.plateNo">
                      {{ item.plateNo }}
                    </span>
                  </li>

                  <li class="ellipsis">
                    <span
                      class="iconfont icon-time t-blue-color mr-5"
                      title="最后一次出现时间"
                    ></span>
                    <span class="t-blue-color" :title="item.lastLogTime">
                      {{ item.lastLogTime }}
                    </span>
                  </li>

                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="最后一次出现地点"
                    ></span>
                    <span class="t-blue-color" :title="item.lastDeviceName">
                      {{ item.lastDeviceName }}
                    </span>
                  </li>

                  <!-- <li class="ellipsis" v-if="item.idCard">
                                        <span class="iconfont icon-idcard t-blue-color mr-5" title="身份证号"></span>
                                        <span :title="item.idCard">{{item.idCard}}</span>
                                    </li>
                                    <li class="ellipsis" v-if="item.name">
                                        <span class="iconfont icon-info-circle t-blue-color mr-5" title="姓名"></span>
                                        <span :title="item.name">{{item.name}}</span>
                                    </li> -->
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'frequence'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.traitImg" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.vid">
                      {{ item.vid }}
                    </span>
                  </li>
                  <li class="ellipsis" v-if="item.idCard">
                    <span
                      class="iconfont icon-idcard t-blue-color mr-5"
                      title="身份证号"
                    ></span>
                    <span :title="item.idCard">{{ item.idCard }}</span>
                  </li>
                  <li class="ellipsis" v-if="item.name">
                    <span
                      class="iconfont icon-info-circle t-blue-color mr-5"
                      title="姓名"
                    ></span>
                    <span :title="item.name">{{ item.name }}</span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >共出现 <b style="color: #ec3f3f">{{ item.freq || 0 }}</b
                      >次</span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'imei-frequence'">
              <div class="result-item-li-box">
                <!-- <div class="result-item-img">
                                    <img :src="item.traitImg" alt="" >
                                </div> -->
                <ul class="result-item-info" style="margin-left: 0">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.imsi">
                      {{ item.imsi }}
                    </span>
                  </li>
                  <!-- <li class="ellipsis" v-if="item.idCard">
                                        <span class="iconfont icon-idcard t-blue-color mr-5" title="身份证号"></span>
                                        <span :title="item.idCard">{{item.idCard}}</span>
                                    </li>
                                    <li class="ellipsis" v-if="item.name">
                                        <span class="iconfont icon-info-circle t-blue-color mr-5" title="姓名"></span>
                                        <span :title="item.name">{{item.name}}</span>
                                    </li> -->
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >共出现 <b style="color: #ec3f3f">{{ item.freq || 0 }}</b
                      >次</span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'vehicle-frequence'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.traitImg" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span class="iconfont icon-chepai t-blue-color mr-5"></span>
                    <span class="t-blue-color" :title="item.plateNo">
                      {{ item.plateNo }}
                    </span>
                  </li>
                  <!-- <li class="ellipsis" v-if="item.idCard">
                                        <span class="iconfont icon-idcard t-blue-color mr-5" title="身份证号"></span>
                                        <span :title="item.idCard">{{item.idCard}}</span>
                                    </li>
                                    <li class="ellipsis" v-if="item.name">
                                        <span class="iconfont icon-info-circle t-blue-color mr-5" title="姓名"></span>
                                        <span :title="item.name">{{item.name}}</span>
                                    </li> -->
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >共出现 <b style="color: #ec3f3f">{{ item.freq || 0 }}</b
                      >次</span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'vehicle-vespertine'">
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.imgPath" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chepai t-blue-color mr-5"
                      title="车牌号码"
                    ></span>
                    <span class="t-blue-color" title="item.remark">
                      {{ item.remark }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >昼伏夜出天数:
                      <b style="color: #ec3f3f"
                        >{{ item.totalDay || 0 }} 天</b
                      ></span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'imei-vespertine'">
              <div class="result-item-li-box">
                <!-- <div class="result-item-img">
                                    <img :src="item.imgPath" alt="" >
                                </div> -->
                <ul class="result-item-info" style="margin-left: 0">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.remark">
                      {{ item.remark }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >昼伏夜出天数:
                      <b style="color: #ec3f3f"
                        >{{ item.totalDay || 0 }} 天</b
                      ></span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'imei-collide'">
              <div class="result-item-li-box">
                <!-- <div class="result-item-img">
                                    <img :src="item.traitImg" alt="" >
                                </div> -->
                <ul class="result-item-info" style="margin-left: 0">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.imsi">
                      {{ item.imsi }}
                    </span>
                  </li>

                  <li class="ellipsis">
                    <span
                      class="iconfont icon-time t-blue-color mr-5"
                      title="最后一次出现时间"
                    ></span>
                    <span class="t-blue-color" :title="item.lastLogTime">
                      {{ item.lastLogTime }}
                    </span>
                  </li>

                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="最后一次出现地点"
                    ></span>
                    <span class="t-blue-color" :title="item.lastDeviceName">
                      {{ item.lastDeviceName }}
                    </span>
                  </li>

                  <!-- <li class="ellipsis" v-if="item.idCard">
                                        <span class="iconfont icon-idcard t-blue-color mr-5" title="身份证号"></span>
                                        <span :title="item.idCard">{{item.idCard}}</span>
                                    </li>
                                    <li class="ellipsis" v-if="item.name">
                                        <span class="iconfont icon-info-circle t-blue-color mr-5" title="姓名"></span>
                                        <span :title="item.name">{{item.name}}</span>
                                    </li> -->
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'imei-together'">
              <div class="result-item-li-box">
                <ul class="result-item-info" style="margin-left: 0">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.imsi">
                      {{ item.imsi }}
                      <span v-if="item.name">({{ item.name }})</span>
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span v-if="item.type == 1"
                      >权重值：<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b></span
                    >
                    <span v-else
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                  <!-- <li class="ellipsis m-top10" v-show="mainVid">
                                        <span class="add-person">添加至关联人员</span>
                                    </li> -->
                </ul>
                <span
                  v-if="!hideTogetherBtn"
                  @click.stop="togetherAnalyse(item)"
                  title="同行分析"
                  class="iconfont icon-bansuitonghang"
                ></span>
              </div>
            </template>
            <template v-else-if="pageName == 'vid-basestation'">
              <div class="result-item-li-box">
                <ul
                  class="result-item-info"
                  style="margin-left: 0; position: relative"
                >
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.imsi">
                      {{ item.imsi }}
                      <span v-if="item.name">({{ item.name }})</span>
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span v-if="item.type == 1"
                      >权重值：<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b></span
                    >
                    <span v-else
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                  <el-button
                    type="primary"
                    size="mini"
                    v-if="item.showLink"
                    style="position: absolute; top: 8px; right: 0"
                    @click.stop="linkInfo(item)"
                    >关联</el-button
                  >
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'basestation-vid'">
              <div class="result-item-li-box">
                <ul
                  class="result-item-info"
                  style="margin-left: 0; position: relative"
                >
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.vid">
                      {{ item.vid }}
                      <span v-if="item.name">({{ item.name }})</span>
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span v-if="item.type == 1"
                      >权重值：<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b></span
                    >
                    <span v-else
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                  <el-button
                    type="primary"
                    size="mini"
                    v-if="item.showLink"
                    style="position: absolute; top: 8px; right: -8px"
                    @click.stop="linkInfo(item)"
                    >关联</el-button
                  >
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'basestation-vehicle'">
              <div class="result-item-li-box">
                <ul
                  class="result-item-info"
                  style="margin-left: 0; position: relative"
                >
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chepai t-blue-color mr-5"
                      title="车牌号码"
                    ></span>
                    <span class="t-blue-color" :title="item.plateNo">
                      {{ item.plateNo }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span v-if="item.type == 1"
                      >权重值：<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b></span
                    >
                    <span v-else
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'vehicle-basestation'">
              <div class="result-item-li-box">
                <ul
                  class="result-item-info"
                  style="margin-left: 0; position: relative"
                >
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.imsi">
                      {{ item.imsi }}
                      <span v-if="item.name">({{ item.name }})</span>
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="摄像机点位"
                    ></span>
                    <span v-if="item.type == 1"
                      >权重值：<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b></span
                    >
                    <span v-else
                      >共经过<b style="color: #ec3f3f">{{
                        item.samePoint || 0
                      }}</b
                      >个相同点位</span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'imei-analyse'">
              <div class="result-item-li-box">
                <ul class="result-item-info" style="margin-left: 0">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-dianwei1 t-blue-color mr-5"
                      title="电围编号"
                    ></span>
                    <span class="t-blue-color" :title="item.imsi">
                      {{ item.imsi }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                      title="采集地点"
                    ></span>
                    <span>{{ item.deviceName }}</span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-time t-blue-color mr-5"
                      title="采集时间"
                    ></span>
                    <span>{{ item.absTime }}</span>
                  </li>
                </ul>
              </div>
            </template>
            <template v-else-if="pageName == 'fixedTerminal'">
              <div class="result-item-li-box">
                <!--<div class="result-item-img">
                                    <img :src="item.imgPath" alt="" >
                                </div>-->
                <ul class="result-item-info" style="margin-left: 0">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" title="item.id">
                      {{ item.id }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >满足频次天数:
                      <b style="color: #ec3f3f"
                        >{{ item.totalDay || 0 }} 天</b
                      ></span
                    >
                  </li>
                </ul>
              </div>
            </template>
            <template v-else>
              <div class="result-item-li-box">
                <div class="result-item-img">
                  <img :src="item.imgPath" alt="" v-viewer @click.stop="" />
                </div>
                <ul class="result-item-info">
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-chakanshipin t-blue-color mr-5"
                      title="视频身份"
                    ></span>
                    <span class="t-blue-color" :title="item.id">
                      {{ item.id }}
                    </span>
                  </li>
                  <li class="ellipsis">
                    <span
                      class="iconfont icon-location t-blue-color mr-5"
                    ></span>
                    <span
                      >昼伏夜出天数:
                      <b style="color: #ec3f3f"
                        >{{ item.totalDay || 0 }} 天</b
                      ></span
                    >
                  </li>
                </ul>
              </div>
            </template>
          </li>
        </ul>
      </div>
      <ui-empty v-else></ui-empty>
    </div>

    <div class="result-pagination">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="pageParams.totalCount"
        countTotal
        :current="pageParams.pageNumber"
        :page-size="pageParams.pageSize"
        @pageChange="handleCurrentChange"
        size="small"
        :showTotal="false"
      >
      </ui-page>
    </div>
  </div>
</template>

<script>
export default {
  name: "ResultList",
  props: {
    resultList: {
      type: Array,
      default: [],
    },
    pageParams: {
      type: Object,
      default: {
        pageSize: 10,
        pageNumber: 1,
        totalCount: 0,
      },
    },
    pageName: {
      type: String,
      default: "",
    },
    hideTogetherBtn: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentIndex: "",
    };
  },
  methods: {
    goback(step) {
      this.$emit("goback", step);
    },
    //关联信息
    linkInfo(item) {
      this.$emit("link-Item", item);
    },
    handleCurrentChange(val) {
      if (this.pageParams.pageNumber == val) return;
      this.$emit("change-page", val);
    },
    selectItem(item, index) {
      let self = this;
      self.currentIndex = index;
      this.$emit("change-Item", item);
    },
    togetherAnalyse(item) {
      this.$emit("together-analyse", item);
    },
  },
};
</script>

<style lang="less" scoped>
.result-list-page {
  width: 100%;
  height: 100%;
  position: relative;
  .t-blue-color {
    color: #47a3ff;
  }
  .result-header {
    height: 40px;
    line-height: 40px;
    margin-right: 10px;
    text-align: right;
    // color: #989cad;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .result-item {
    position: absolute;
    top: 30px;
    left: 0;
    right: 0;
    bottom: 40px;
    min-height: 62px;
    .result-item-li {
      padding: 10px;
      border-bottom: 1px solid #d3d7de;
      cursor: pointer;
      overflow: hidden;
      .result-item-li-box {
        padding: 10px;
        overflow: hidden;
        display: flex;
        .result-item-img {
          position: relative;
          img {
            width: 80px;
            height: 100px;
          }
        }
        .result-item-info {
          margin-left: 10px;
          line-height: 20px;
          flex: 1;
          overflow: hidden;
          li {
            cursor: pointer;
            span {
              vertical-align: middle;
            }
          }
        }
      }
      &:hover {
        .result-item-li-box {
          background-color: #f3f3f3 !important;
        }
      }
      &.active {
        .result-item-li-box {
          background-color: #fff;
        }
      }
    }
  }
  .result-pagination {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 10px;
    .pages {
      height: auto;
    }
  }
}
</style>
