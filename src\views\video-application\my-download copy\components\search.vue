<template>
  <div class="search">
    <Form :inline="true" ref="form" :model="form">
      <div class="left">
        <ButtonGroup>
          <Button :type="!form.type ? 'primary' : 'default'" @click="changeType('')">全部</Button>
          <Button :type="form.type == 'success' ? 'primary' : 'default'" @click="changeType('success')">下载完成</Button>
          <Button :type="form.type == 'download' ? 'primary' : 'default'" @click="changeType('download')">下载中</Button>
          <Button :type="form.type == 'error' ? 'primary' : 'default'" @click="changeType('error')">下载失败</Button>
          <Button :type="form.type == 'stop' ? 'primary' : 'default'" @click="changeType('stop')">暂停</Button>
        </ButtonGroup>
      </div>
      <div>
        <FormItem label="文件名:" class="mr-20" prop="fileName">
          <Input placeholder="请输入" v-model="form.fileName" maxlength="50"></Input>
        </FormItem>
        <FormItem label="下载时间:" class="mr-20" prop="time">
          <DatePicker v-model="form.time" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择时间"></DatePicker>
        </FormItem>
        <FormItem class="btn-group mr-0">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </div>
    </Form>
  </div>
</template>
<script>

export default {
  data() {
    return {
      form: {
        type: '',
        fileName: '',
        time: []
      }
    }
  },
  created() {},
  methods: {
    changeType(type) {
      this.form.type = type
      this.startSearch()
    },
    // 查询
    startSearch() {
      this.$emit('searchForm', this.form)
    },
    // 重置
    resetHandle() {
      this.$refs.form.resetFields()
      this.startSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  .mr-0 {
    margin-right: 0;
  }
  .mr-20 {
    margin-right: 20px;
  }
  .dialog-input {
    width: 200px;
  }
  .ivu-date-picker {
    width: 330px !important;
  }
  .ivu-form {
    display: flex;
    width: 100%;
    justify-content: space-between;
  }
  .left {
    .ivu-btn-default {
      border-color: #D3D7DE;
      color: rgba(0,0,0,0.45);
    }
    .ivu-btn-primary {
      color: #2C86F8;
      background-color: rgba(44,134,248,0.1028);
      border-color: #2C86F8;
      z-index: 11;
    }
  }
}
</style>
