<!-- 轨迹准确性 -->
<template>
  <div class="trajectory-accuracy" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <tagView class="tagView fr" ref="tagView" :list="['图像模式', '聚档模式']" @tagChange="tagChange1" />
      </div>
      <!-- 设备模式 -->
      <TableList
        class="list"
        v-if="modelTag == 0 && tableColumns.length > 0"
        ref="infoList"
        :contentClientHeight="contentClientHeight"
        :columns="tableColumns"
        :loadData="loadDataList"
      >
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <SearchCard :searchNum="1" :checkStatus="checkStatus" @startSearch="startSearch"></SearchCard>
          <!-- <Button type="primary" class="btn_search">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button> -->
        </div>
        <!-- 表格操作 -->
        <!-- <template #trackImage="{ row }">
          <img @click="viewBigPic(row)" :src="!row.trackImage ? noImg : row.trackImage" alt="" />
        </template> -->
        <template #trackImage="{ row }">
          <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
            <ui-image :src="row.trackImage" />
          </div>
        </template>
        <template #algResult="{ row }">
          <span v-if="row.algResult">
            <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
              <span>{{ item.algorithmType }}:</span>
              <span>{{ item.score }}%</span>
            </div>
          </span>
          <span v-else>--</span>
        </template>
        <template #identityPhoto="{ row }">
          <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
            <ui-image :src="row.identityPhoto" />
          </div>
        </template>
        <template #unableDetectTip="{ row }">
          <span :class="row.synthesisResult == '1' ? '' : 'font-red'">{{ row.unableDetectTip }}</span>
        </template>
      </TableList>
      <!-- 图像模式 -->
      <TableCard
        ref="infoCard"
        class="card-list"
        :loadData="loadDataCard"
        :cardInfo="cardInfo"
        v-if="modelTag == 1"
        :contentClientHeight="contentClientHeight"
      >
        <div slot="search" class="hearder-title">
          <SearchCard :is-image-model="false" @startSearch="startSearch"></SearchCard>
        </div>
        <!-- 卡片 -->
        <template #card="{ row }">
          <InfoCard
            class="card1"
            :list="row"
            :personTypeList="personTypeList"
            :cardInfo="cardInfo"
            @detail="detailInfo(row)"
          >
          </InfoCard>
        </template>
      </TableCard>
      <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
      <person-detail
        ref="captureDetail"
        :resultId="{
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        }"
        :interFaceName="getSecondaryPopUpData"
        :staticsList="personList"
      >
        <template #searchList>
          <SearchCard
            class="mb-sm track-search"
            :searchNum="1"
            :checkStatus="checkStatus"
            @startSearch="startDetailSearch"
          ></SearchCard>
        </template>
      </person-detail>
    </div>
  </div>
</template>
<style lang="less" scoped>
.trajectory-accuracy {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      //min-height: 380px !important;
      min-height: calc(100vh - 520px) !important;
    }
  }
  .card-list {
    width: 100%;
    min-height: 530px !important;
    max-height: calc(100vh - 380px);
  }
  .information-header {
    margin-top: 10px;
    display: flex;
    .information-statistics {
      display: flex;
      width: 100%;
      height: 100%;
      background: var(--bg-sub-content);
      padding: 15px;
      margin-right: 0px !important;
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 19.54% !important;
      margin-bottom: 0px !important;
      .monitoring-data {
        margin-left: 30px;
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      padding: 0 10px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .ui-images {
        width: 56px !important;
        height: 56px !important;
      }
    }
    .hearder-title {
      position: relative;
      padding: 10px 0 10px 10px;

      .btn_search {
        position: absolute;
        right: 0px;
        top: 10px;
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'trajectory-accuracy',
  data() {
    return {
      rankLoading: false,
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      statisticsList: [
        // {
        //   name: 'ZDR人员总量',
        //   value: 0,
        //   icon: 'icon-ZRDzongliang',
        //   iconColor: 'icon-bg1',
        //   liBg: 'li-bg1',
        //   type: 'number',
        //   textColor: 'color1',
        // },
        {
          name: 'ZRD人像轨迹总量',
          value: 0,
          icon: 'icon-guijizhunqueshuai',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'trackAmount',
        },
        {
          name: '检测轨迹数量',
          value: 0,
          icon: 'icon-jianceguijishuliang',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          textColor: 'color5',
          key: 'detectionAmount',
        },

        {
          name: '准确性存疑轨迹数量',
          value: 0,
          icon: 'icon-zhunquexingcunyiguijishuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          textColor: 'color6',
          key: 'impeachAmount',
        },

        {
          name: '无法检测轨迹数量',
          value: 0,
          icon: 'icon-wufajianceguijishuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'unableDetectCount',
        },

        {
          name: '轨迹准确率',
          value: '0.0%',
          icon: 'icon-guijizhunqueshuai1',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '轨迹图像',
          key: 'trackImage',
          slot: 'trackImage',
          tooltip: true,
          minWidth: 150,
        },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '证件照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
        { title: '原始算法', key: 'similarity', tooltip: true, minWidth: 150 },
        {
          title: '算法识别结果',
          key: 'algResult',
          slot: 'algResult',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '判定结果',
          key: 'unableDetectTip',
          slot: 'unableDetectTip',
          tooltip: true,
          minWidth: 150,
        },
      ],
      checkStatus: [
        { name: '存疑', checkKey: 0 },
        { name: '合格', checkKey: 1 },
        { name: '无法检测', checkKey: 2 },
      ],
      tableData: [],
      searchData: {},
      exportLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      loadDataList: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getDetailData,
            Object.assign(
              parameter,
              {
                indexId: this.paramsList.indexId,
                batchId: this.paramsList.batchId,
                access: this.paramsList.access,
                displayType: this.paramsList.displayType,
                orgRegionCode: this.paramsList.orgRegionCode,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getPolyData,
            Object.assign(
              parameter,
              {
                indexId: this.paramsList.indexId,
                batchId: this.paramsList.batchId,
                access: this.paramsList.access,
                displayType: this.paramsList.displayType,
                orgRegionCode: this.paramsList.orgRegionCode,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      modelTag: 0, // 设备模式,图像模式
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'total' },
        { name: '异常轨迹：', value: 'abnormal', color: '#BC3C19' },
      ],
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      bigPictureShow: false, //大图展示
      checkPicture: false, //查看图片
      currentRow: {},
      personList: [
        {
          label: '抓拍总量',
          count: 0,
          filedName: 'catchAmount',
        },
        {
          label: '准确性存疑轨迹数量',
          count: 0,
          filedName: 'impeachAmount',
        },
      ],
      contentClientHeight: 0,
    };
  },
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },

    startSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    async init() {
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },

    // 详情
    detailInfo(info) {
      this.$refs.captureDetail.show(info);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    TableList: require('./components/tableList.vue').default,
    TableCard: require('./components/tableCard.vue').default,
    SearchCard: require('./components/searchCard.vue').default,
    InfoCard: require('./components/ui-gather-card.vue').default,
    tagView: require('./components/tags.vue').default,
    personDetail: require('./components/person-detail.vue').default,
    LookScene: require('@/components/look-scene').default,
    statistics: require('@/components/icon-statistics').default,
  },
};
</script>
