<template>
  <div class="view">
    <div class="btns">
      <!-- <Button type="primary">开始运行</Button>
      <Button type="primary">保存配置</Button> -->
      <Button type="primary" :loading="loading" @click="run">
        <span v-if="loading">Loading...</span>
        <span v-else>开始运行</span>
      </Button>
    </div>
    <div class="flexUi">
      <div class="item item1">
        <span :class="'icon-font icon-' + video[0].icon1"></span>
        <i class="icon-font icon-chenggong success"></i>
        <p class="title">{{ video[0].title }}</p>
        <p class="desc">{{ video[0].desc }}</p>
      </div>
      <connecting-arrow :connectingOptions="arrowsInfo"></connecting-arrow>

      <div class="group1">
        <div class="groupItem" v-for="(item, index) in video[1]" :key="index">
          <span v-if="index == 0" :class="'icon-font icon-' + item.icon1" style="font-size: 25px"></span>
          <span v-else :class="'icon-font icon-' + item.icon1" style="font-size: 18px"></span>
          <p class="title">{{ item.title }}</p>
          <p class="desc">{{ item.desc }}</p>
          <i v-if="index == 1" class="config icon-font icon-systemmanagement" @click="historyVideoFn()"></i>
          <m-switch :class="{ nullConfig: index == 0 }" v-if="pageLoading" :options="video[1][index].datas" />
        </div>
      </div>
      <connecting-arrow :connectingOptions="arrowsInfo"></connecting-arrow>

      <div class="item item3">
        <span :class="'icon-font icon-' + video[2].icon1"></span>
        <p class="title">{{ video[2].title }}</p>
        <p class="desc">{{ video[2].desc }}</p>
        <m-switch class="nullConfig" v-if="pageLoading" :options="video[2].datas" />
      </div>
      <connecting-arrow :connectingOptions="arrowsInfo"></connecting-arrow>

      <div class="item item4">
        <span :class="'icon-font icon-' + video[3].icon1"></span>
        <p class="title">{{ video[3].title }}</p>
        <p class="desc">{{ video[3].desc }}</p>
      </div>
    </div>
    <div class="faceprocess-right">
      <component-package></component-package>
    </div>

    <videoDialog ref="modal" />
    <historyVideo ref="historyVideo" />
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
import { video } from './video';
import videoDialog from './videoDialog';
export default {
  name: 'videoView',
  props: {},
  data() {
    return {
      loading: false,
      pageLoading: false,
      video: video,
      arrowsInfo: {
        width: '170px',
        height: '8px',
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.$http
        .get(api.queryCarList + '4')
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data.componentList;
            // list.forEach(item => {
            //   var key = 'switch'+item.id;
            //   if (key in this.switchList) {
            //     this.switchList[key] = item.status == '1' ? true : false;
            //   }
            // })
            this.video.forEach((item) => {
              if (item instanceof Object) {
                item.datas = list.find((it) => it.componentName == item.title) || {};
              }

              if (item instanceof Array) {
                item.forEach((childItem) => {
                  if (childItem.switch) {
                    childItem.datas = list.find((it) => it.componentName == childItem.title) || {};
                  }
                });
              }
            });

            this.pageLoading = true;
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },
    showModal(val) {
      this.$refs.modal.showModal(val);
    },

    historyVideoFn() {
      this.$refs.historyVideo.showModal();
    },

    run() {
      this.loading = true;
      var param = {
        id: 4,
      };
      this.$http
        .post(api.videoRun, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.loading = false;
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
            this.loading = false;
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    switchChange(e, v) {
      var param = {
        id: v,
      };
      this.$http
        .put(api.updateStatus, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
        })
        .catch(() => {});
    },
  },
  watch: {},
  components: {
    videoDialog,
    ComponentPackage: require('@/views/datagovernance/governancetheme/components/componentPackage.vue').default,
    historyVideo: require('./history-video').default,
    ConnectingArrow: require('./connecting-arrow').default,
    mSwitch: require('../components/m-switch').default,
  },
};
</script>
<style lang="less" scoped>
.view {
  position: relative;
  width: 100%;
  height: 100%;
  // padding: 24px;
  background-image: url('../../../../assets/img/thememanagement/process-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
  padding: 0 60px;
  .flexUi {
    position: relative;
    width: calc(100% - 243px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 50%;
    transform: translateY(-50%);
    .item {
      position: relative;
      width: calc(21% - 100px);
      // width: 196px;
      min-height: 92px;
      background: #0f2f59;
      border: 1px solid var(--color-primary);
      border-radius: 4px;
      padding: 20px 10px 20px 50px;
      color: #fff;
    }
    .groupItem {
      position: relative;
      // width: 196px;
      min-height: 92px;
      background: #0f2f59;
      border: 1px solid var(--color-primary);
      border-radius: 4px;
      padding: 10px 10px 20px 50px;
      color: #fff;
      margin-top: 10px;
    }
    .leftIcon {
      position: absolute;
      color: var(--color-primary);
      font-size: 30px;
      left: 12px;
      top: 16px;
    }
    .success {
      position: absolute;
      color: #43bf00;
      font-size: 14px;
      right: 10px;
      top: 10px;
    }

    .config {
      font-size: 12px;
      color: #56789c;
      position: absolute;
      right: 10px;
      bottom: 10px;
    }

    .title {
      font-size: 14px;
    }

    .desc {
      color: #99a4af;
    }

    .group1 {
      left: 400px;
      top: 150px;
      width: calc(23% - 100px);
      // width: 216px;
      // height: 300px;
      padding: 0 10px 10px 10px;
      border: 1px solid var(--color-primary);
    }

    .iswitch {
      position: absolute;
      right: 30px;
      bottom: 12px;
    }
    .nullConfig {
      right: 10px;
    }
  }
}

.arrows {
  position: relative;
  width: 150px;
  height: 7.6px; /*no*/
  background: var(--color-primary);
  .arrow {
    position: absolute;
    right: -10px;
    top: -5px;
  }
}

span.icon-font {
  position: absolute;
  background-image: linear-gradient(#0f84e9, #12a4c9);
  background-clip: text;
  left: 16px;
  font-size: 20px;
  -webkit-text-fill-color: transparent;
}

.faceprocess-right {
  position: absolute;
  top: 0;
  right: 0;
  overflow: auto;
  width: 243px;
  height: 100%;
  background-color: var(--bg-content);
}

.btns {
  position: absolute;
  right: 260px;
  top: 20px;
  button {
    margin-left: 10px;
  }
}
</style>
