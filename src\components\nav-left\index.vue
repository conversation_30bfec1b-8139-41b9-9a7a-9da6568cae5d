<template>
  <component
    :is="LayoutComponents[getNavConfigType].componentName"
    v-bind="$attrs"
    @selectMenu="(name) => $emit('selectMenu', name)"
  />
</template>

<script>
import { LayoutComponentsEnum } from './utils/enum';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    NavLeftHorizon: require('./components/nav-left-horizon.vue').default,
    NavLeft: require('./components/nav-left.vue').default,
  },
  data() {
    return {
      LayoutComponents: Object.freeze(LayoutComponentsEnum),
    };
  },
  methods: {
    ...mapActions({
      // setCacheRouterList: 'tabs/setCacheRouterList',
      // closeCacheRouter: 'tabs/closeCacheRouter',
      // setActiveRouterName: 'tabs/setActiveRouterName',
      // setIsCollapsed: 'common/setIsCollapsed',
      // setLeftMenuList: 'permission/setLeftMenuList',
    }),
  },
  computed: {
    ...mapGetters({
      getNavConfigType: 'common/getNavConfigType',
    }),
  },
};
</script>
