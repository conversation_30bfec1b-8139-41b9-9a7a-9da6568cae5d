export default {
  findByCondition: '/ivdg-evaluation-app/evaluation/task/pageTasks', //测评分页
  addOrUpdate: '/ivdg-evaluation-app/evaluation/task/add', //新增或跟新评测
  updateOrUpdate: '/ivdg-evaluation-app/evaluation/task/update', //新增或跟新评测
  tEvaluatingContext: '/ivdg-evaluation-app/evaluation/task/removeById', //通过id删除测评
  getTaskById: '/ivdg-evaluation-app/evaluation/task/getById', //通过id查询任务详情
  getTaskChangeTaskState: '/ivdg-evaluation-app/evaluation/task/changeTaskState', //暂停/恢复评测
  getOrgStatResult: '/ivdg-evaluation-app/evaluation/result/getOrgStatResult', //测评统计
  getPageResults: '/ivdg-evaluation-app/evaluation/result/pageResults', //评测结果分页列表
  getExportResultByIds: '/ivdg-evaluation-app/evaluation/result/exportResultById', //导出考核结果

  // 方案管理
  getProjram: '/ivdg-evaluation-app/evaluation/scheme/pageSchemes', // 获取所有方案
  tEvaluatingProgram: '/ivdg-evaluation-app/evaluation/scheme/add', // 新增方案
  updateScheme: '/ivdg-evaluation-app/evaluation/scheme/update', // 修改方案
  deleteScheme: '/ivdg-evaluation-app/evaluation/scheme/removeById', // 删除方案
  getEvaluatingProgramIndexByPage: '/ivdg-evaluation-app/evaluation/schemeIndex/pageRelatedIndexes', //获取方案列表数据
  getPageOptionalIndexes: '/ivdg-evaluation-app/evaluation/schemeIndex/pageOptionalIndexes', //查询可关联指标列表
  getContingencyIndexes: '/ivdg-evaluation-app/evaluation/schemeIndex/contingencyIndexes', //保存关联指标
  getSchemeIndexRemoveById: '/ivdg-evaluation-app/evaluation/schemeIndex/removeById', //删除方案单个指标
  getConfigIndex: '/ivdg-evaluation-app/evaluation/schemeIndex/configIndex', //配置方案单个指标
  getSchemeIndexById: '/ivdg-evaluation-app/evaluation/schemeIndex/getById', //关联指标详细信息

  // 评测详情
  deviceList: '/ivdg-evaluation-app/evalute/device/deviceList', //设备列表接口-左侧列表,
  devicePageList: '/ivdg-evaluation-app/evalute/device/devicePageList', //根节点列表
  pageList: '/ivdg-evaluation-app/evalute/vehicle/pageList', //具体某个设备列表
  getTableHeader: 'ivdg-evaluation-management-service/detail/header/getTableHeader', //表头
  getListByVehicleDetailId: '/ivdg-evaluation-app/evalute/vehicle/getListByVehicleDetailId', // 算法规则列数据
  // 视频流
  videoPageList: '/ivdg-evaluation-app/evalute/video/pageList', //查询测评数据分页列表
  checkDeviceStatus: '/ivdg-evaluation-app/evalute/video/checkDeviceStatus', //检测设备状态
};
