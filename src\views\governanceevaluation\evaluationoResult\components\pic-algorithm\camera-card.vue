<template>
  <!-- 大图左下角--摄像机相关信息 -->
  <div class="card-content">
    <p class="text-title">
      <i class="icon-font icon-danganxinxi f-14 mr-xs"></i>
      <span>档案信息:</span>
    </p>
    <p class="p-tip p-label">
      <span>设备PTZ控制状态：</span>
      <span class="span-label">
        {{ currentRow?.deviceInfo?.ptzStatusText || '' }}
      </span>
    </p>
    <p class="p-tip p-label">
      <span>摄像机类型扩展：</span>
      <span class="span-label">{{ currentRow?.deviceInfo?.ptzTypeText || '' }}</span>
    </p>
    <p class="p-tip p-label">
      <span>安装位置室内外：</span>
      <span class="span-label">{{ currentRow?.deviceInfo?.roomTypeText || '' }}</span>
    </p>
    <p class="p-tip p-label">
      <span>摄像机用途：</span>
      <span class="span-label">{{ currentRow?.deviceInfo?.useTypeText || '' }}</span>
    </p>
    <p class="p-tip p-label">
      <span>监视方位：</span>
      <span class="span-label">{{ currentRow?.deviceInfo?.directionTypeText || '' }}</span>
    </p>
    <div class="text-title">
      <i class="icon-font icon-shibieneirong f-14 mr-xs"></i>
      <span>识别内容：</span>
      <div v-for="(algorithmItem, algorithmIndex) in currentAlgorithmInfo.infoAlgorithmTextList" :key="algorithmIndex">
        <span class="sub-title" v-if="currentAlgorithmInfo.infoAlgorithmTextList.length > 1 && showAlgorithmName">
          {{ algorithmItem.algorithmName }}：
        </span>
        <p
          class="tooltip-text p-tip"
          v-for="(text, textIndex) in algorithmItem.textList"
          :key="`${algorithmIndex}-${textIndex}`"
        >
          {{ text }}
        </p>
      </div>
    </div>
    <p class="text-title">
      <i class="icon-font icon-jiancejieguo1 f-14 mr-xs"></i>
      <span>检测结果：</span>
    </p>
    <div class="result">
      <p class="p-text" v-for="resultInfo in currentAlgorithmInfo.cameraData" :key="resultInfo.key">
        {{ resultInfo.value }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentRow: {
      type: Object,
    },
    currentAlgorithmInfo: {
      type: Object,
    },
    showAlgorithmName: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .p-tip,
  .tooltip-text {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}
.card-content {
  padding: 8px 10px;
  background: var(--bg-card);
  font-size: 13px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  color: var(--color-content);
  line-height: 21px;
  .p-label {
    .span-label {
      white-space: pre-wrap;
    }
  }
  .text-title {
    padding: 1px 0;
    color: var(--color-title);
  }
  .result {
    color: var(--color-failed);
    white-space: pre-wrap;
  }
  .p-text {
    position: relative;
    padding: 1px 0 1px 18px;
    color: var(--color-failed);
    &::before {
      content: ' ';
      width: 11px;
      height: 11px;
      display: inline-block;
      background: var(--bg-quadrate-outside);
      transform: rotate(45deg);
      position: absolute;
      top: 5px;
      left: 0;
    }
    &::after {
      content: ' ';
      width: 5px;
      height: 5px;
      display: inline-block;
      background: var(--bg-quadrate-interior);
      transform: rotate(45deg);
      position: absolute;
      top: 8px;
      left: 3px;
    }
  }
  .p-tip {
    position: relative;
    padding: 1px 0 1px 18px;
    color: rgba(0, 0, 0, 0.8);
    &::before {
      content: ' ';
      width: 11px;
      height: 11px;
      display: inline-block;
      border-radius: 50%;
      background: var(--bg-circle-outside);
      position: absolute;
      top: 6px;
      left: 0;
    }
    &::after {
      content: ' ';
      width: 5px;
      height: 5px;
      display: inline-block;
      border-radius: 50%;
      background: var(--bg-circle-interior);
      position: absolute;
      top: 9px;
      left: 3px;
    }
  }
  .sub-title {
    color: var(--color-sub-title);
  }
  .tooltip-text {
    white-space: pre-line;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.8);
  }
}
</style>
