<template>
  <div class="alarm">
    <div
      v-if="detailList.length > 0"
      class="my-swiper-container"
      id="mySwiper2"
    >
      <swiper
        ref="mySwiper"
        :options="swiperOption"
        class="my-swiper"
        id="frequent-swiper"
      >
        <template v-for="(item, index) in detailList">
          <swiper-slide :key="index">
            <FrequentAlarm
              :data="item"
              @click.native="() => handleDetailFn(item, index)"
            ></FrequentAlarm>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="frequentLeft"
          @click.stop
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="frequentRight"
          @click.stop
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <NightOutCommunityDetail
      ref="videoDetail"
      :currentRow="currentRow"
      @comfirmHandler="comfirmHandler"
    ></NightOutCommunityDetail>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import FrequentAlarm from "./collect/frequent-alarm.vue";
import NightOutCommunityDetail from "@/views/community-management/components/detail/night-out-community-confirm-detail.vue";
import { getCommunityConfig } from "@/api/monographic/community-management.js";
import { getFaceLib } from "@/api/monographic/juvenile.js";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
    FrequentAlarm,
    NightOutCommunityDetail,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      swiperOption: {
        slidesPerView: "3",
        speed: 1000,
        // spaceBetween: 10,
        // centeredSlides: true,
        // centeredSlidesBounds: true,
        navigation: {
          nextEl: "#frequentRight",
          prevEl: "#frequentLeft",
        },
        observer: true,
        observeParents: true,
      },
      detailShow: false,
      detailList: [],
      currentRow: {},
    };
  },
  watch: {
    list: {
      handler(val) {
        if (val?.length > 0) {
          this.detailList = [...val];
        }
      },
      immediate: true,
    },
  },
  computed: {},
  async created() {},
  mounted() {
    this.getConfigLibInfo();
  },
  methods: {
    // 获取配置添加的人员库的信息
    async getConfigLibInfo() {
      const res = await getCommunityConfig();
      const communityData = res?.data?.paramValue || "{}";
      const json = JSON.parse(communityData);
      // 库ID
      const libId = json?.strangerFreqConfig?.enterFaceLib || "";
      if (!libId) {
        return this.$Message.warning("未配置置信人员需要添加的库");
      }
      // 获取库信息
      const libRes = await getFaceLib({ ids: [libId] });
      const data = libRes?.data?.entities?.[0] || {};
      this.currentRow = { ...data };
    },
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.detailInit({ ...item }, index);
    },
    // 处置结果处理
    comfirmHandler(value) {
      this.$set(
        this.frequentList[value.index],
        "handlerStatus",
        value.handlerStatus
      );
      this.$set(
        this.frequentList[value.index],
        "handlerDetail",
        value.handlerDetail
      );
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;

  span {
    color: #2c86f8;
  }
}
.alarm {
  width: 100%;
  height: 100%;
}
.my-swiper-container {
  padding: 0 15px;
  position: relative;
  height: 100%;
  .my-swiper {
    margin: auto;
    height: 100%;
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;

  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 10px;
}

.swiper-button-next {
  right: 10px;
}
</style>
