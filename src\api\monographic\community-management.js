import request from "@/libs/request";
import { monographic } from "../Microservice";
import { community as domainName } from "./base";

// 获取系统中配置的场所信息
export function getConfigPlaces() {
  return request({
    url: monographic + domainName + "/sysConfig/getConfigPlaces",
    method: "post",
  });
}

// 获取系统中配置的二级场所类型
export function getConfigPlaceSecondLevels() {
  return request({
    url: monographic + domainName + "/sysConfig/getConfigPlaceSecondLevels",
    method: "post",
  });
}

// 修改社区专题配置信息
export function updateCommunityConfig(data) {
  return request({
    url: monographic + domainName + "/sysConfig/updateCommunityConfig",
    method: "post",
    data,
  });
}

// 查询社区专题配置信息
export function getCommunityConfig() {
  return request({
    url: monographic + domainName + "/sysConfig/getCommunityConfig",
    method: "post",
  });
}

// 分页查询场所列表
export function getPlacePageList(data) {
  return request({
    url: monographic + domainName + "/place/pageList",
    method: "post",
    data,
  });
}

// 社区人口统计
export function getStatisticsPageList(data) {
  return request({
    url: monographic + domainName + "/statistics/pageList",
    method: "post",
    data,
  });
}

// 夜间频繁出入小区详情查询
export function getNightEnterDetail(data) {
  return request({
    url: monographic + domainName + "/night/enter/detail",
    method: "post",
    data,
  });
}

// 夜间频繁出入小区分页查询
export function getNightEnterPageList(data) {
  return request({
    url: monographic + domainName + "/night/enter/pageList",
    method: "post",
    data,
  });
}

// 频繁出入小区分页查询
export function getStrangerPageList(data) {
  return request({
    url: monographic + domainName + "/stranger/pageList",
    method: "post",
    data,
  });
}

// 陌生人频繁出入小区处置
export function setStrangerHandler(data) {
  return request({
    url: monographic + domainName + "/stranger/handler",
    method: "post",
    data,
  });
}

// 频繁出入小区详情查询
export function getStrangerDetail(data) {
  return request({
    url: monographic + domainName + "/stranger/detail",
    method: "post",
    data,
  });
}

// 分页查询场所列表
export function faceAlarmPlacePageList(placeId, topN) {
  return request({
    url:
      monographic + domainName + `/place/faceAlarmPageList/${placeId}/${topN}`,
    method: "get",
  });
}

// 场所人员业务类型统计
export function personLabelStatistics(placeId) {
  return request({
    url: monographic + domainName + `/place/personLabelStatistics/${placeId}`,
    method: "get",
  });
}

// 孤寡老人多日未出现列表查询
export function getElderlyPageList(data) {
  return request({
    url: monographic + domainName + "/elderly/pageList",
    method: "post",
    data,
  });
}

// 社区重点人同行
export function travelAlongDetailPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/travelAlongDetailPageList",
    method: "post",
    data,
  });
}

// 社区内异常行为预警列表分页查询
export function abnormalBehaviorPageList(data) {
  return request({
    url: monographic + domainName + "/alarm/abnormalBehaviorPageList",
    method: "post",
    data,
  });
}
