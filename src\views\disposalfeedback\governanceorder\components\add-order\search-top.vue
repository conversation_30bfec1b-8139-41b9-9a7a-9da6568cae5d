<template>
  <section class="top-content">
    <common-search
      ref="commonSearch"
      :default-form="defaultCommonForm"
      :is-view="false"
      :edit-view-action="editViewAction"
      :is-self="searchForm.workOrderType === 2"
      @getSearchForm="getSearchForm"
    >
      <ui-label class="mb-sm" label="关联问题">
        <RadioGroup v-model="searchForm.workOrderType" @on-change="radioChange">
          <!-- <Radio :label="1"
                 class="radio-item">关联问题清单</Radio> -->
          <Radio :label="1" class="radio-item mr-md">关联检测任务 </Radio>
          <Radio :label="2">自定义问题</Radio>
        </RadioGroup>
      </ui-label>
      <div class="section-p mb-sm" v-if="searchForm.workOrderType === 1">
        <Form class="mb-sm specialTransfer" ref="formValidate" :model="searchForm" :rules="ruleValidate">
          <FormItem label="任务名称" class="inline" required prop="timedtaskId">
            <Select
              v-model="searchForm.timedtaskId"
              placeholder="请选择任务"
              class="width-slg"
              filterable
              @on-change="changeTaskName"
            >
              <Option v-for="(item, index) in taskList" :key="index" :value="item.taskSchemeId"
                >{{ item.taskName }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="指标名称" class="inline ml-lg" required prop="indexName">
            <Select
              v-model="searchForm.indexName"
              placeholder="请选择指标"
              class="width-slg"
              filterable
              :disabled="!searchForm.timedtaskId"
              :loading="indexLoading"
              @on-change="changeIndexId"
            >
              <Option v-for="(item, index) in IndexList" :key="index" :value="JSON.stringify(item)"
                >{{ item.indexName }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="检测时间" class="inline ml-lg" required>
            <DatePicker
              type="date"
              class="select-width"
              v-model="searchForm.startTime"
              placeholder="请选择"
              :options="timeOption"
              :disabled="!searchForm.indexName"
              format="yyyy-MM-dd"
              @on-change="changeDatePicker"
            >
            </DatePicker>
            <span class="ml-xs mr-xs form-line">—</span>
            <Select v-model="searchForm.batchId" class="select-width" @on-change="changeExamineDate" :disabled="!searchForm.indexName">
              <Option v-for="item in examineDateRounds" :value="item.batchId" :key="item.batchId"
                >{{ item.examineTime }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="处理异常" :label-width="78" class="inline ml-lg">
            <Select
              v-model="searchForm.reasons"
              placeholder="请选择处理异常"
              class="width-slg"
              multiple
              :max-tag-count="1"
              :disabled="!searchForm.indexName"
              @on-change="changeErrorReason"
            >
              <Option v-for="item in errorList" :key="item.dicKey" :value="item.dicKey">{{ item.dicValue }} </Option>
            </Select>
          </FormItem>
        </Form>
      </div>
      <ui-label class="flex-1 mt-sm" label="附件" :width="65" v-if="searchForm.workOrderType === 2">
        <ui-upload
          v-model="searchForm.fileUrl"
          :accept="accept"
          :format="format"
          :upload-url="uploadUrl"
          :get-upload-params="getUploadParams"
        ></ui-upload>
      </ui-label>
    </common-search>
  </section>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import governancetask from '@/config/api/governancetask';
import unified from '@/config/api/unified.js';

export default {
  props: {
    editViewAction: {
      default: () => {
        return {
          type: '',
          row: {},
        };
      },
    },
  },
  data() {
    const validatePass = (rule, value, callback) => {
      value ? callback() : callback('请选择任务名称');
    };
    return {
      searchForm: {
        timedtaskId: '',
        indexName: '',
        indexModule: '',
        taskContent: '',
        workOrderType: 1,
        reasons: [],
        taskIndexId: '',
        fileUrl: '',
        assignMode: 0,
        startTime: new Date(), //任务时间
        batchId: '', //任务检测时间
        orgRegionCode: '',
      },
      ruleValidate: {
        indexName: [{ required: true, message: '请选择指标', trigger: 'change' }],
        timedtaskId: [{ validator: validatePass, trigger: 'change' }],
      },
      defaultCommonForm: {
        workOrderName: '',
        workOrderNum: null,
        taskPlannedDate: '',
        receiverName: '',
        receiverId: '',
        taskContent: '',
        assignMode: 0,
        assignList: [],
      },
      taskList: [],
      IndexList: [],
      errorList: [],
      indexLoading: false,
      uploadUrl: unified.fileUpload,
      format: ['doc', 'docx', 'xlsx', 'bmp', 'jpg', 'png', 'JPEG', 'zip', '7z'],
      accept: '.bmp, .jpeg,.jpg,.png,.zip,.7z,.doc,.docx,.xlsx',
      //任务时间
      timeOption: {
        disabledDate: (date) => {
          const dateArr = this.clickableDates.map((item) => {
            const dateFormat = new Date(`${item} 00:00:00`);
            return dateFormat.getTime();
          });
          const dateTime = date.valueOf();
          return !dateArr.includes(dateTime);
        },
      },
      clickableDates: [],
      examineDateRounds: [], //检测的具体时间
    };
  },
  created() {
    this.findByCondition();
  },
  methods: {
    getUploadParams(uploadData) {
      let uploadParams = '';
      uploadData.forEach((row) => {
        uploadParams += row.response.data.fileUrl;
      });
      return uploadParams;
    },
    getSearchForm(params) {
      Object.assign(this.searchForm, params);
    },
    radioChange(value) {
      this.$emit('radioChange', value === 1);
    },
    changeIndexId(one) {
      this.$emit('on-change-index');
      this.searchForm.reasons = [];
      this.errorList = [];
      this.clickableDates = [];
      if (!one) return;
      let item = JSON.parse(one);
      this.searchForm.indexId = item.indexId;
      this.searchForm.taskIndexId = item.id;
      this.searchForm.indexModule = item.indexModule;
      // this.searchForm.batchId = item.batchId
      this.searchForm.orgRegionCode = item.regionCode;
      this.searchForm.batchId = null;
      this.searchForm.startTime = null;
      this.getHistoryTimeList();
    },
    changeErrorReason(item) {
      this.$emit('changeErrorReason', item, this.errorList);
    },
    async findByCondition() {
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, {
          pageNumber: 1,
          pageSize: 1000,
        });
        this.taskList = data.entities;
      } catch (err) {
        console.log(err);
      }
    },
    async changeTaskName() {
      this.$emit('on-change-task');
      if (!this.searchForm.timedtaskId) return;
      this.indexLoading = true;
      this.searchForm.reasons = [];
      this.IndexList = [];
      this.errorList = [];
      this.clickableDates = [];
      this.searchForm.batchId = null;
      this.searchForm.startTime = null;
      this.searchForm.indexName = '';

      let params = {
        taskSchemeId: this.searchForm.timedtaskId,
        pageNumber: 1,
        pageSize: 1000,
      };
      try {
        // let { data:{data} } = await this.$http.post(governanceevaluation.taskIndexPageList, params)
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.taskIndexPageListByWork, params);
        this.IndexList = data.entities;
      } catch (err) {
        console.log(err);
      } finally {
        this.indexLoading = false;
      }
    },
    async queryReasonList(params) {
      try {
        let {
          data: { data = [] },
        } = await this.$http.get(governancetask.queryReasonList, { params: params });
        this.errorList = data.map((item) => {
          // 人脸的数据需要单独处理
          if (params.indexModule === '2') {
            return {
              dicKey: item.causeError,
              dicValue: item.causeErrorText,
            };
          }
          // 视频流
          if (params.indexModule === '4') {
            return {
              dicKey: item.reasonCodes,
              dicValue: item.reason,
            };
          }
          return {
            dicKey: item,
            dicValue: item,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 共父组件调用
    handleSubmit() {
      return this.$refs['formValidate'].validate((valid) => {
        return valid;
      });
    },
    reset() {
      this.searchForm = {
        timedtaskId: '',
        indexName: '',
        taskContent: '',
        workOrderType: 1,
        reasons: [],
        fileUrl: '',
        assignMode: 0,
        startTime: null,
        batchId: '',
      };
      this.defaultCommonForm = {
        workOrderName: '',
        workOrderNum: null,
        taskPlannedDate: '',
        receiverName: '',
        receiverId: '',
        taskContent: '',
        assignMode: 0,
        assignList: [],
      };
      this.$refs.commonSearch.assignMode = 0;
    },
    //获取最近一个月的检测结果日期
    async getHistoryTimeList() {
      let params = {
        taskSchemeId: this.searchForm.timedtaskId,
        indexId: this.searchForm.indexId,
      };
      try {
        let { data } = await this.$http.post(governanceevaluation.getHistoryTimeList, params);
        this.clickableDates = data.data.reverse();
        this.clickableDates?.length && this.changeDatePicker(this.clickableDates[0]);
      } catch (err) {
        console.log(err);
        this.clickableDates = [];
      }
    },
    //选择时间-天数
    async changeDatePicker(date) {
      this.$set(this.searchForm, 'startTime', date);
      this.getHistoryBatchListByDate();
    },
    async getHistoryBatchListByDate() {
      let params = {
        taskSchemeId: this.searchForm.timedtaskId,
        indexId: this.searchForm.indexId,
        startTime: this.searchForm.startTime,
      };
      try {
        let { data } = await this.$http.post(governanceevaluation.getHistoryBatchListByDate, params);
        // this.examineDateRounds = data.data
        this.examineDateRounds = data.data.reverse();
        let firstBatchObj = this.examineDateRounds[0];
        if (firstBatchObj && firstBatchObj.batchId) {
          this.changeExamineDate(this.examineDateRounds[0].batchId);
        }
      } catch (err) {
        console.log(err);
        this.examineDateRounds = [];
      }
    },
    //切换
    changeExamineDate(val) {
      this.searchForm.batchId = val;
      if (!val) {
        return;
      }
      this.queryReasonList({
        batchId: this.searchForm.batchId,
        indexId: this.searchForm.indexId,
        indexModule: this.searchForm.indexModule,
      });
      let params = {
        indexId: this.searchForm.indexId,
        batchId: this.searchForm.batchId,
        orgRegionCode: this.searchForm.orgRegionCode,
      };
      this.$emit('changeIndexId', params);
    },
  },
  watch: {},
  components: {
    // CommonSearch: require('../common-search.vue').default,
    CommonSearch: require('./common-search.vue').default,
    UiUpload: require('@/components/ui-upload.vue').default,
  },
};
</script>
<style lang="less" scoped>
.top-content {
  // margin: 0 100px 0;
  margin: 0;

  .width-slg {
    width: 230px;
  }

  .specialTransfer {
    margin-left: -10px;
  }

  .pl-16 {
    padding-left: 16px;
  }

  .ui-label {
    display: flex;
  }

  .section-p {
    display: flex;
  }

  .desc {
    width: 940px;
  }

  .flex-1 {
    flex: 1;
  }

  .select-width {
    width: 160px;
  }
}
</style>
