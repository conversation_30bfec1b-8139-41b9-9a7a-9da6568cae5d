module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    commonjs: true,
    es2021: true,
  },
  extends: ['plugin:vue/essential', 'eslint:recommended'],
  parserOptions: {
    'ecmaVersion': 2020,
    'ecmaFeatures': {
      'jsx': true,
    },
    'sourceType': 'module',
  },
  // eslint-plugin-*插件，只写最后个名称就行
  plugins: ['vue'],
  // 声明全局变量
  globals: {
    '$': true,
    'NPMapLib': true,
    'require': true,
    '$var': true,
    '$cssVar': true,
  },
  // 0：关闭，1：警告，2：禁止   或者：off/warn/error
  rules: {
    'no-cond-assign': 2, // 禁止：在条件表达式中使用赋值语句
    'no-dupe-args': 2, // 禁止：函数参数重名
    'no-unused-vars': 2, // 禁止：未使用过的变量
    'semi': ['error', 'always'], // 禁止：必须有分号
    'no-console': 1, // 警告：代码中存在console.log
    'no-unreachable': 0, // 警告：有无法执行的代码
    'quotes': ['warn', 'single'], // 警告：字符串单引号
    'indent': ['off', 4], // 关闭：缩进4个空格
    'linebreak-style': ['off', 'unix'], // 关闭：换行符
    'vue/multi-word-component-names': 'off', // 关闭组件命名检查
    'no-self-assign': 'warn', // 警告：对属性的自我赋值
    'no-prototype-builtins': 'warn', // 警告：直接访问对象的内置属性或方法
    'no-case-declarations': 'warn', // 警告：在 switch 语句中的 case 子句内声明变量或常量
    'no-useless-escape': 'warn', // 警告： 转义字符
    'no-async-promise-executor': 'warn', // 警告：new Promise(async (resolve, reject) => {})
  },
};
