<!--
    * @FileDescription: 搜索结果
    * @Author: H
    * @Date: 2024/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div
    class="rightBox"
    :style="{ top: top + 'px' }"
    :class="{ 'rightBox-pack': packUpDown }"
  >
    <div class="rightBox-page">
      <div class="title">
        <p>{{ title }}</p>
        <Icon type="ios-close" @click="handleCancel" />
      </div>
      <div class="hint_title">
        共<span> {{ total }} </span> 条检索结果
      </div>
      <ul class="box-content" v-infinite-scroll="load">
        <li
          class="box-list"
          v-for="(item, index) in facePeerCount"
          :key="index"
        >
          <div class="box-number">
            <span>{{ index + 1 }}</span>
          </div>
          <div class="box-right">
            <div class="box-right-img">
              <img v-lazy="item.traitImg" alt="" />
            </div>
            <div class="box-right-details">
              <div class="details-head">
                <p>{{ item.Nonumber }}</p>
                <i
                  class="iconfont icon-dangan2"
                  @click="handleDetails($event, item, index)"
                ></i>
              </div>
              <p class="details-type">{{ item.deviceName || "--" }}</p>
              <p class="details-number">
                落脚 <span>{{ item.count }}</span> 次
              </p>
            </div>
          </div>
        </li>
        <ui-empty
          v-if="facePeerCount.length === 0 && loading == false"
        ></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </ul>
      <div
        class="footer"
        :class="{ packArrow: packUpDown }"
        @click="handlePackup"
      >
        <img :src="packUrl" alt="" />
        <p>{{ packUpDown ? "展开" : "收起" }}</p>
      </div>
      <p class="loading" v-if="loadingText">加载中...</p>
      <p class="loading" v-if="noMore && facePeerCount.length != 0">
        没有更多了
      </p>
    </div>
  </div>
</template>

<script>
import operaFloor from "../../../components/operat-floor/index.vue";
import { queryFaceFrequenciesList } from "@/api/modelMarket";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "检索结果",
    },
    marginTop: {
      type: Number,
      default: 0,
    },
  },
  components: {
    operaFloor,
  },
  data() {
    return {
      // title: '对象信息',
      facePeerCount: [],
      removableTop: 0,
      objModal: false,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      listIcon: {
        0: ["xingming", "shenfenzheng", "camera"],
        1: ["chepai", "xingming", "shenfenzheng"],
      },
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      typeIndex: {
        tab: 0,
        secTab: 0,
      },
      loading: false,
      // loadingText: '加载中',
      total: 0,
      loadingText: false,
      noMore: false,
      packUpDown: false,
      searchInfo: {},
    };
  },
  watch: {
    marginTop: {
      handler(val) {
        // console.log(val, 'marginTop')
      },
      immediate: true,
    },
  },
  computed: {
    top() {
      return this.marginTop + 20;
    },
    scrollHeight() {
      let htmlFontSize = parseFloat(
        window.document.documentElement.style.fontSize
      );
      if (!!htmlFontSize) {
        return htmlFontSize * (450 / 192);
      }
      return 450;
    },
  },
  created() {},
  mounted() {},
  methods: {
    init(val) {
      this.packUpDown = false;
      this.loading = true;
      this.page = {
        pageNumber: 1,
        pageSize: 10,
      };
      this.facePeerCount = [];
      this.searchInfo = val;
      this.total = 0;
      this.rightList(val);
    },
    rightList(val) {
      let params = {
        ...val,
        ...this.page,
        deivceList: val.deviceIds,
      };
      queryFaceFrequenciesList(params)
        .then((res) => {
          if (typeof res.data == "string") {
            this.$Message.warning(res.data);
          } else {
            let list = (res.data && res.data.entities) || [];
            this.facePeerCount = this.facePeerCount.concat(...list);
            this.total = res.data.total || 0;
            this.$emit("facelist", this.facePeerCount);
          }
        })
        .finally(() => {
          this.loading = false;
          this.noMore = false;
          this.loadingText = false;
        });
    },
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        setTimeout(() => {
          this.noMore = false;
        }, 1000);
        return;
      } else {
        this.loadingText = true;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.rightList(this.searchInfo);
      }
    },
    handleCancel() {
      this.$emit("cancel");
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    // 详情
    handleDetails($event, item, index) {
      $event.stopPropagation();
      this.$emit("details", item, index);
    },
  },
};
</script>

<style lang='less' scoped>
@import "./style/index";
.rightBox {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 0;
  background: #fff;
  height: calc(~"100% - 40px");
  transition: height 0.2s ease-out;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  &-page {
    height: 100%;
    width: 100%;
  }
  .hint_title {
    margin: 10px 0 0 15px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    span {
      color: rgba(44, 134, 248, 1);
    }
  }
  .box-content {
    padding: 0 15px;
    height: calc(~"100% - 110px");
    overflow-y: auto;
    position: relative;
    .box-list {
      display: flex;
      margin-bottom: 10px;
      align-items: center;
      .box-number {
        position: relative;
        background: url("~@/assets/img/map/trajectory-red.png") no-repeat;
        width: 32px;
        height: 32px;
        color: #ea4a36;
        > span {
          position: absolute;
          top: 0px;
          width: 32px;
          font-size: 12px;
          color: #ea4a36;
          text-align: center;
        }
      }
      .box-right {
        display: flex;
        justify-content: space-between;
        background: #f9f9f9;
        padding: 5px 10px 5px 5px;
        width: 100%;
        margin-left: 6px;
        &-img {
          width: 80px;
          height: 80px;
          border: 1px solid #cfd6e6;
          img {
            width: 100%;
            height: 100%;
          }
        }
        &-details {
          margin-left: 10px;
          flex: 1;
          .details-head {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            color: #2c86f8;
            font-size: 14px;
            i {
              cursor: pointer;
            }
            .icon-dangan2 {
              color: #2c86f8 !important;
            }
          }
          .details-type {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
            margin-top: 6px;
          }
          .details-number {
            color: rgba(0, 0, 0, 0.6);
            font-size: 14px;
            margin-top: 6px;
            span {
              color: #2c86f8;
            }
          }
        }
      }
    }
  }
  .footer {
    // color: #000000;
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 96%;
  }
}
.rightBox-pack {
  height: 80px;
  transition: height 0.2s ease-out;
  overflow: hidden;
}
</style>
