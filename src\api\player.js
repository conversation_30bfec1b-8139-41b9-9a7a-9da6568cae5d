import request from "@/libs/request";
import {
  icbdService,
  service,
  manager,
  modelSearch,
  mapService,
  compareNotice,
} from "./Microservice";

// 云台控制
export function fetchPTZControl(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/ptz/control?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// 查询流地址
export function fetchLive(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/live/start?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// 停止直播流
export function stopLive(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/live/stop?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// 停止录像流
export function stopVod(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/vod/stop?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// 查询录像
export function fetchVod(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/vod/query?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// 播放录像
export function startVod(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/vod/start?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// 录像播放控制
export function controlVod(data) {
  return request({
    url: `http://${lwptIp}:${lwptProt}` + `/v1/vod/control?token=${lwptToken}`,
    method: "post",
    data,
  });
}

// pvg-根据国标ID获取rtsp实时流
export function getRtspPvg(gbId) {
  return request({
    url: service + `/deviceVideo/getRtsp/${gbId}`,
    method: "get",
  });
}

// 根据国标id获取通道信息-明文
export function getChannel(gbId) {
  return request({
    url: service + `/deviceVideo/viewChannel/${gbId}`,
    method: "get",
  });
}

// 根据国标id获取通道信息-密文
export function getCipherChannel(gbId) {
  return request({
    url: service + `/deviceVideo/viewCipherChannel/${gbId}`,
    method: "get",
  });
}

// 根据国标id获取通道信息-密文-带表后缀
export function getCipherChannelNew(gbId, suffix) {
  return request({
    url:
      service +
      `/deviceVideo/viewCipherChannelNew?gbId=${gbId}&suffix=${
        suffix ? suffix : ""
      }`,
    method: "get",
  });
}

// 发送短信
export function sendSms(data) {
  return request({
    url: compareNotice + "/smsController/sendSms",
    method: "post",
    data,
  });
}

// 查询PVG通道信息列表
export function queryPvgChannelList() {
  return request({
    url: service + `/deviceVideo/queryPvgChannelList`,
    method: "get",
  });
}

// 更新PVG通道主平台
export function updateMasterPvg(id) {
  return request({
    url: service + `/deviceVideo/updateMaster/${id}`,
    method: "get",
  });
}

// 更新PVG通道信息状态(通道状态 0：正常 1：异常)
export function updatePvgChannelStatus(id, status) {
  return request({
    url:
      service + `/deviceVideo/updatePvgChannelStatus?id=${id}&status=${status}`,
    method: "get",
  });
}

// 云台锁定相关
// 云台锁定
export function setPtzLock(data) {
  return request({
    url: icbdService + "/ptz/lock",
    method: "post",
    data,
  });
}
// 云台解锁
export function setPtzUnlock(data) {
  return request({
    url: icbdService + "/ptz/unlock",
    method: "post",
    data,
  });
}
// 获取云台锁定信息
export function getLockInfo(deviceId) {
  return request({
    url: icbdService + `/ptz/lockInfo/${deviceId}`,
    method: "get",
  });
}

// 设备资源列表
export function queryDeviceOrgList(data) {
  return request({
    url: service + "/deviceVideo/queryDeviceOrgList",
    method: "post",
    data,
  });
}

// 设备资源列表--权限 图上作战有关键字搜索
export function queryDeviceAndKeys(data) {
  return request({
    url: mapService + "/business/map/queryDeviceAndKeys",
    method: "post",
    data,
  });
}
// 设备资源列表--权限
export function queryDeviceOrgTree(data) {
  return request({
    url: service + "/deviceVideo/queryDeviceOrgTree",
    method: "post",
    data,
  });
}

// 设备资源统计
export function queryDeviceStatisticsList(data) {
  return request({
    url: service + "/deviceVideo/queryDeviceStatisticsList",
    method: "post",
    data,
  });
}

// 设备资源统计-role
export function queryDeviceOrgTreeStatistics(data) {
  return request({
    url: service + "/deviceVideo/queryDeviceOrgTreeStatistics",
    method: "post",
    data,
  });
}

// 设备详情列表查询
export function queryCameraDeviceList(data) {
  return request({
    url: service + "/deviceVideo/queryCameraDeviceList",
    method: "post",
    data,
  });
}

// 我的分组关联设备新增
export function addVideoToGroup(data) {
  return request({
    url: icbdService + "/group/videoDeviceGroup/addVideoGroupInfo",
    method: "post",
    data,
  });
}

// 我的分组关联设备删除
export function removeVideoFromGroup(data) {
  return request({
    url: icbdService + `/group/videoDeviceGroup/deleteVideoGroupInfo`,
    method: "post",
    data,
  });
}

// 我的分组新增
export function addMyVideoGroup(data) {
  return request({
    url: icbdService + "/group/myVideoGroup/add",
    method: "post",
    data,
  });
}

// 我的分组编辑
export function updateMyVideoGroup(data) {
  return request({
    url: icbdService + "/group/myVideoGroup/update",
    method: "post",
    data,
  });
}

// 我的分组删除
export function delMyVideoGroup(ids) {
  return request({
    url: icbdService + `/group/myVideoGroup/remove/${ids}`,
    method: "delete",
  });
}

// 查询我的分组列表
export function queryMyVideoGroupList(data) {
  return request({
    url: icbdService + `/group/myVideoGroup/queryMyVideoGroupList`,
    method: "post",
    data,
  });
}

// 查询我的分组关联设备列表
export function queryVideoDeviceGroupList(data) {
  return request({
    url: icbdService + `/group/videoDeviceGroup/queryVideoDeviceGroupList`,
    method: "post",
    data,
  });
}

// 更新分组设备别名
export function updateDeviceAlias(data) {
  return request({
    url: icbdService + `/group/videoDeviceGroup/updateDeviceAlias`,
    method: "post",
    data,
  });
}

// 分组分享
export function shareGroup(data) {
  return request({
    url: icbdService + `/group/myVideoGroup/shareGroup`,
    method: "post",
    data,
  });
}

// 播放历史新增
export function addPlayback(data) {
  return request({
    url: icbdService + `/playback/playback/add`,
    method: "post",
    data,
  });
}

// 查询播放历史分页列表
export function getPlaybackPageList(data) {
  return request({
    url: icbdService + `/playback/playback/pageList`,
    method: "post",
    data,
  });
}

// 播放历史删除
export function removePlayback(ids) {
  return request({
    url: icbdService + `/playback/playback/remove/${ids}`,
    method: "delete",
  });
}
// 组织
export function deviceDirectoryTree(data) {
  return request({
    url: service + `/device/directory/deviceDirectoryTree `,
    method: "post",
    data,
  });
}

// 播放历史清空
export function clearPlayback() {
  return request({
    url: icbdService + `/playback/playback/delete`,
    method: "get",
  });
}

// 查询目录
export function getTreeAncestors(id) {
  return request({
    url: service + `/device/permission/treeAncestors`,
    method: "post",
    data: { ids: [id] },
  });
}

// 我的云盘-截图上传
export function imgUploadCloud(data) {
  return request({
    url: manager + "/cloud/disk/add",
    method: "post",
    data,
  });
}

// 图片全结构化
export function getAllPicturePick(data) {
  return request({
    url: modelSearch + "/CloudSearch/getAllPicturePick",
    method: "post",
    data,
  });
}

// 根据图片坐标截取图片base64
export function cutImageBase64(data) {
  return request({
    url: modelSearch + "/CloudSearch/cutImageBase64",
    method: "post",
    data,
  });
}

export function queryDeviceList(data) {
  return request({
    url: service + "/device/selectDeviceList",
    method: "post",
    data,
  });
}

// 设备治理-更新设备
export function updateDevice(data) {
  return request({
    url: service + "/device/update",
    method: "post",
    data,
  });
}

// 查询电视墙分页列表
export function queryVideoWallList(data) {
  return request({
    url: icbdService + "/videoWall/pageList",
    method: "post",
    data,
  });
}

// 获取监视器布局详情
export function queryDemodifier(data) {
  return request({
    url: icbdService + "/demodifier/getDemodifier",
    method: "post",
    data,
  });
}

// 电视墙视频播放
export function planPlay(data) {
  return request({
    url: icbdService + "/videoWall/plan/play",
    method: "post",
    data,
  });
}

// 电视墙视频停止播放
export function planStop(data) {
  return request({
    url: icbdService + "/videoWall/plan/stop",
    method: "post",
    data,
  });
}

// 电视墙预案新增/更新
export function addPlan(data) {
  return request({
    url: icbdService + "/videoWall/plan/saveOrUpdate",
    method: "post",
    data,
  });
}

// 查询电视墙预案分页列表
export function queryPlanList(data) {
  return request({
    url: icbdService + "/videoWall/plan/list",
    method: "post",
    data,
  });
}

// 删除电视墙预案
export function deletePlan(id) {
  return request({
    url: icbdService + `/videoWall/plan/delete/${id}`,
    method: "delete",
  });
}

// 分享
export function videoWallShare(data) {
  return request({
    url: icbdService + `/videoWall/plan/share`,
    method: "post",
    data,
  });
}

// 电视墙锁定
export function lockWall(data) {
  return request({
    url: icbdService + "/wall/lock",
    method: "post",
    data,
  });
}

// 获取电视墙锁定信息
export function getWallLockInfo(data) {
  return request({
    url: icbdService + "/wall/lockInfo",
    method: "post",
    data,
  });
}

// 电视墙解锁
export function unlockWall(data) {
  return request({
    url: icbdService + "/wall/unlock",
    method: "post",
    data,
  });
}

//预置位
// 创建预置位
export function createPreset(data) {
  return request({
    url: icbdService + "/preset/create",
    method: "post",
    data,
  });
}
// 根据通道号和排序规则查询预置位
export function getPresets(data) {
  return request({
    url: icbdService + "/preset/getByChannelNo",
    method: "post",
    data,
  });
}
// 根据预置位ID调用指定预置位
export function callPreset(id) {
  return request({
    url: icbdService + `/preset/call/${id}`,
    method: "get",
  });
}
// 根据预置位ID删除预置位
export function deletePreset(id) {
  return request({
    url: icbdService + `/preset/delete/${id}`,
    method: "delete",
  });
}

// 巡航
// 判断预置位是否在巡航中
export function hasCruiseByPresetId(presetId) {
  return request({
    url: icbdService + `/cruise/hasCruiseByPresetId/${presetId}`,
    method: "get",
  });
}
// 根据摄像机ID查询巡航信息
export function getCruiseByCameraId(cameraId) {
  return request({
    url: icbdService + `/cruise/getByCameraId/${cameraId}`,
    method: "get",
  });
}
// 更新巡航
export function updateCruise(data) {
  return request({
    url: icbdService + "/cruise/update",
    method: "post",
    data,
  });
}
// 新增巡航
export function addCruise(data) {
  return request({
    url: icbdService + "/cruise/add",
    method: "post",
    data,
  });
}
// 根据巡航ID删除巡航信息
export function deleteCruise(cruiseId) {
  return request({
    url: icbdService + `/cruise/delete/${cruiseId}`,
    method: "get",
  });
}
// 根据巡航ID更新巡航状态
export function switchOnOffCruise(data) {
  return request({
    url: icbdService + "/cruise/updateStatus",
    method: "post",
    data,
  });
}
