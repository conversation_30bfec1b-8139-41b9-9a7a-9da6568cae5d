.carfollow-control {
    position: fixed;
    z-index: 699;
    bottom: 5px;
    left: 50%;
    width: 70%;
    transform: translateX(-50%);
    min-width: 800px;
    overflow: hidden;
    border-radius: 3px;
}
.controlpanel-checked-icon:hover {
    color: #09a1e4;
    width: 16px;
    height: 16px;
    background: url('~@/assets/img/model/controlPanel.png') 0 0 no-repeat;
}

.controlpanel-checked-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    color: #fff;
    background: url('~@/assets/img/model/controlPanel.png') 0 -18px no-repeat;
}

.controlpanel-helper-icon:hover {
    background: url('~@/assets/img/model/controlPanel.png') 0 -34px no-repeat;
    
}

.controlpanel-helper-icon {
    display: inline-block;
    width: 17px;
    height: 17px;
    background: url('~@/assets/img/model/controlPanel.png') 0 -53px no-repeat;
}

.controlpanel-next-icon.hover {
    background: url('~@/assets/img/model/controlPanel.png') 0 -72px no-repeat;
}

.controlpanel-next-icon {
    display: inline-block;
    width: 16px;
    height: 21px;
    background: url('~@/assets/img/model/controlPanel.png') 0 -95px no-repeat;
}

.controlpanel-nochecked-icon:hover {
    color: #09a1e4;
    background: url('~@/assets/img/model/controlPanel.png') 0 -118px no-repeat;
}

.controlpanel-nochecked-icon {
    display: inline-block;
    width: 14px;
    height: 14px;
    color: #666;
    background: url('~@/assets/img/model/controlPanel.png') 0 -134px no-repeat;
}

.controlpanel-pause-icon.hover {
    background: url('~@/assets/img/model/controlPanel.png') 0 -150px no-repeat;
}

.controlpanel-pause-icon {
    display: inline-block;
    width: 17px;
    height: 24px;
    background: url('~@/assets/img/model/controlPanel.png') 0 -176px no-repeat;
}

.controlpanel-play-icon.hover {
    background: url('~@/assets/img/model/controlPanel.png') 0 -202px no-repeat;
}

.controlpanel-play-icon {
    display: inline-block;
    width: 12px;
    height: 24px;
    background: url('~@/assets/img/model/controlPanel.png') 0 -228px no-repeat;
}

.controlpanel-prev-icon.hover {
    background: url('~@/assets/img/model/controlPanel.png') 0 -254px no-repeat;
}

.controlpanel-prev-icon {
    display: inline-block;
    width: 16px;
    height: 21px;
    background: url('~@/assets/img/model/controlPanel.png') 0 -277px no-repeat;
}

.map-panel {
    position: relative;
    /*width: 961px;*/
    height: 60px;
    margin: 0 auto;
    background: rgba(0, 0, 0, .8);
    border-radius: 3px;
    -moz-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    -khtml-user-select: none;
    user-select: none;
}

.map-slider {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 220px;
    left: 115px;
}

.map-control-left {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 75px;
}

.map-control-right {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 220px;
}

.map-control-left >span {
    cursor: pointer;
}

.map-control-left .play-pause {
    position: absolute;
    top: 15px;
    left: 31px;
}

.map-control-left .play-prev {
    position: absolute;
    top: 17px;
    left: 0;
}

.map-control-left .play-next {
    position: absolute;
    top: 17px;
    right: 0;
}

.map-control-right .map-control-showall {
    position: absolute;
    top: 20px;
    left: 10px;
    width: 70px;
    padding-left: 20px;
    cursor: pointer;
}

.map-control-right .map-control-info {
    position: absolute;
    top: 20px;
    left: 90px;
    width: 70px;
    padding-left: 20px;
    cursor: pointer;
}

.map-control-right .play-helper {
    position: absolute;
    top: 20px;
    top: 18px;
    right: 15px;
}

.map-control-right .play-helper:hover .play-helper-info {
    right: -14px;
}

.play-helper .play-helper-info {
    position: absolute;
    right: -9999px;
    bottom: 40px;
    /*display: none;*/
    width: 90px;
    padding: 5px;
    color: #fff;
    background: rgba(0, 0, 0, .8);
    border-radius: 3px;
}

.slider-container {
    position: absolute;
    top: 11px;
    width: 100%;
    height: 33px;
}
.slider-container .slider-gauge {
    position: absolute;
    top: 16px;
    width: 100%;
    height: 1px;
    background: #b2b2b2;
}
.slider-area {
    position: absolute;
    z-index: 10;
    top: 0;
    left: 0;
    width: 20%;
    height: 33px;
    /* cursor: move; */
    background: rgba(11, 176, 255, .4);
    cursor: pointer;

}
.slider-container .drag-button {
    position: absolute;
    top: 0;
    display: block;
    width: 2px;
    height: 33px;
    background: #19b7ef;
    cursor: text;
}
.slider-container .drag-button .slider-tip {
    position: absolute;
    bottom: -13px;
    left: -9999px;
    width: 120px;
    text-align: center;
}
.slider-container .drag-button .slider-tip p {
    font-size: 10px;
    color: #cbcbcb;
    -webkit-transform: scale(.83);
    -o-transform: scale(1);
}
.slider-container .drag-button-left {
    left: -2px;
}
.slider-container .drag-button-right {
    right: -2px;
}
.slide-move {
    position: absolute;
    left: 0;
    width: 2px;
}
.slide-view {
    position: absolute;
    top: 16px;
    left: -9999px;
    width: 2px;
}
.slide-view .view-handler {
    position: absolute;
    top: -27px;
    width: 2px;
    height: 44px;
    background: #19b7ef;
}
.slide-view .view-message {
    line-height: 27px;
    position: absolute;
    top: -61px;
    left: -62px;
    width: 120px;
    height: 27px;
    text-align: center;
    background: rgba(0, 0, 0, .8);
}
.slide-move .move-handler {
    position: absolute;
    top: -11px;
    width: 2px;
    height: 44px;
    background: #19b7ef;
}
.slide-move .move-message {
    line-height: 27px;
    position: absolute;
    top: -45px;
    left: -62px;
    width: 120px;
    height: 27px;
    text-align: center;
    background: rgba(0, 0, 0, .8);
}
.slide-move .move-message p, .slide-view .view-message p {
    font-size: 10px;
    -webkit-transform: scale(.83);
    -o-transform: scale(1);
    color: #fff;
}
.slide-move .move-message:after, .slide-view .view-message:after {
    position: absolute;
    left: 58px;
    width: 0;
    height: 0;
    content: '';
    border-top: 4px solid rgba(0, 0, 0, .8);
    border-right: 4px solid rgba(0, 0, 0, 0);
    border-left: 4px solid rgba(0, 0, 0, 0);
}
