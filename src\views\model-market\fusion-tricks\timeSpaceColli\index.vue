<template>
    <div class="time-space-colli">
        <!-- 地图 -->
        <mapCustom ref="mapBase"
            :sectionName='mapType.sectionName'
            :allCameraList="allCameraList"
            @gettingData="gettingData"
            @clearList="clearList"
            @chooseMapItem="chooseMapItem"
            :collisionPoints='collisionPoints'
            :mapType="mapType.seleType"
            :crashType="mapType.seleType"
            :currentClickIndex.sync="currentClickIndex"
            cutIcon="map"
        />
        <!-- 左侧搜索 -->
        <leftBox ref="leftBox" 
            @search="handleSearch" 
            @reset='handleReset' 
            @selectDraw="selectDraw" 
            @deleDraw="deleDraw"
            :typeName="mapType.mapName"
            :seleArr="seleArr"></leftBox>
        <!-- 右侧列表 -->
        <rightBox 
            ref="rightBox" 
            v-show="rightBoxShow" 
            :tablist="tablists"
            @spaceTime="handleSpaceTime" 
            @cancel="handleCancel"></rightBox>
        <!-- 对象详情 -->
        <detailsBox ref="detailsBox" 
            @list="handleList" 
            @chooseMapItem="chooseMapItem"
            :drawColor="drawColor" 
            v-if="detailsShow" @goback="goback"></detailsBox>
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import rightBox from './components/rightBox.vue';
import detailsBox from './components/detailsBox.vue';
import mapCustom from '../../components/map/index.vue';
import { mapMutations } from 'vuex';
import { myMixins } from '../../mixins/index.js';
export default {
    name: 'timeSpaceColli',
    mixins: [myMixins],
    components:{
        mapCustom,
        leftBox,
        rightBox,
        detailsBox
    },
    props: {
        mapType: {
            type: Object,
            default: () => {
                return {
                    "mapName": 'face',
                    'sectionName': 'faceMap', // 模态框类型
                    'seleType': 'Camera_Face', //框选类型
                }
            }
        }
    },
    data () {
        return {
            seleArr: [0, 0, 0, 0, 0],
            deviceList: [],
            drawStyle: {
                color: '#2C86F8', //颜色
                fillColor: '#2C86F8', //填充颜色
                weight: 3, //宽度，以像素为单位
                opacity: 1, //透明度，取值范围0 - 1
                fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
                lineStyle: '#2C86F8',
                strokeColor: '#2C86F8'
            },
            drawColor: ['#2C86F8','#1FAF81','#F29F4C','#A786FF','#48BAFF'],
            tablists:[],
            detailsList: [],
            rightBoxShow: false,
            detailsShow: false,
            collisionPoints:[],
            currentClickIndex: -1,
        }
    },
    watch:{
    },
    computed:{
    },
    activated() {
        this.setLayoutNoPadding(true)
    },
    deactivated(){
        this.setLayoutNoPadding(false)
    },
    created() {
    },
    mounted(){
            
    },
    methods: {
        handleList(list) {
            this.collisionPoints = list;
        },
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        // 搜索
        handleSearch(tabs, maplist, formList) {
            this.detailsShow = false;
            this.rightBoxShow = true;
            this.clearMap()
            this.tablists = tabs.map(item => {
                return maplist.get(item)
            });
            let list = formList.map((item, index) => {
                let obj = {
                    'conditionNo': item.conditionNo,
                    'endDate': item.endDate,
                    'startDate': item.startDate,
                    'deviceIds': this.deviceList[index].map(item => item.deviceId)
                }
                this.$set(this.drawColor, index, item.color)
                return obj
            })
            let params = {
                'queryTypeList': tabs,
                'collisionFormList' : list
            };
            this.$refs.rightBox.init(params);
        },
        handleReset() {
            this.detailsShow = false;
            this.rightBoxShow = false; 
            this.seleArr = [0, 0, 0, 0, 0];
            this.$refs.mapBase.clearAll([1,2]);
            this.clearMap()
        },
        selectDraw(type, index, list) {
            list.map((item, index) => {
                this.$set(this.drawColor, index, item.color)
            })
            let style = {
                color: this.drawColor[index], //颜色
                fillColor: this.drawColor[index], //填充颜色
                weight: 3, //宽度，以像素为单位
                opacity: 1, //透明度，取值范围0 - 1
                fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
                lineStyle: this.drawColor[index],
                strokeColor: this.drawColor[index]
            }
            this.$refs.mapBase.selectDraw(type, true, index, style);
        },
        gettingData(value, index) {
            this.$set(this.deviceList, index, value)
            this.$set(this.seleArr, index, value.length)
            // this.$refs.leftBox.goSearch(value, type);
        },
        clearList(index) {
            this.$set(this.seleArr, index, 0);
            this.$refs.leftBox.cancelSelect(index);
        },
        handleCancel() {
           this.rightBoxShow = false; 
        },
        handleSpaceTime(params, type) {
            this.detailsShow = true;
            this.rightBoxShow = false;
            this.$nextTick(() => {
                this.$refs.detailsBox.init(params,type);
            })
        },
        goback() {
            this.detailsShow = false;
            this.rightBoxShow = true;
            this.clearMap()
        },
        // 清除地图上控件
        clearMap() {
            this.$refs.mapBase.resetCollMarker();
            this.$refs.mapBase.closeMapDom();
        },
        // 删除条件
        deleDraw(index) {
            this.$refs.mapBase.handleDele(index)
        },
        // 左侧选中项 点击普通搜索列表
        chooseMapItem (index, tab = '') {
            this.currentClickIndex = index;
        },
    }
}
</script>

<style lang='less' scoped>
.time-space-colli{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
