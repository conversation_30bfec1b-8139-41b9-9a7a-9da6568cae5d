<template>
  <div class="composite-config">
    <div class="composite-config-content">
      <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="230">
        <FormItem label="选择关联的检测任务" prop="taskSchemeId">
          <Select
            class="select-width"
            v-model="formValidate.taskSchemeId"
            clearable
            filterable
            placeholder="请选择关联的检测任务"
            @on-change="handleChangeTask"
          >
            <Option
              v-for="schemeItem in taskSchemeList"
              :key="schemeItem.taskSchemeId"
              :value="schemeItem.taskSchemeId"
            >
              {{ schemeItem.schemeName }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="选择指标" prop="indexIds">
          <Select
            class="select-width"
            v-model="formValidate.indexIds"
            multiple
            clearable
            filterable
            :max-tag-count="2"
            placeholder="请选择指标"
            @on-change="handleChangeIndex"
          >
            <Option
              v-for="indexItem in indexList"
              :key="indexItem.indexType"
              :value="`${indexItem.indexId},${indexItem.indexName},${indexItem.id}`"
            >
              {{ indexItem.indexName }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="统计时间" required>
          <div class="select-width">
            <plan-time-picker
              ref="planTimePicker"
              :required="true"
              :time-picker-data="timePickerData"
              @handleUpatePlanData="handleUpatePlanData"
            ></plan-time-picker>
          </div>
        </FormItem>
        <FormItem label="配置统计逻辑" prop="statType">
          <Select class="select-width" v-model="formValidate.statType" placeholder="请选择">
            <Option v-for="logicCItem in logicConfigList" :key="logicCItem.dataKey" :value="logicCItem.dataKey">
              {{ logicCItem.dataValue }}
            </Option>
          </Select>
        </FormItem>
        <FormItem label="" prop="condition">
          <RadioGroup v-model="formValidate.criteria">
            <Radio :label="1" class="mr-lg">所有条件满足</Radio>
            <Radio :label="2">任一条件满足</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
    </div>
    <div class="detection-list-container" v-if="!!formValidate.indexIds.length">
      <detection-form ref="detectionForm" :detection-list="detectionList" :required="true"></detection-form>
    </div>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  name: 'composite-config',
  props: {
    formModel: {
      type: String,
      default: 'add',
    },
    configInfo: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      formValidate: {
        indexIds: [],
        statType: 0,
        criteria: 2,
      },
      ruleValidate: {
        taskSchemeId: [{ required: true, message: '请选择关联的检测任务', trigger: 'change', type: 'string' }],
        indexIds: [{ required: true, message: '请选择指标', trigger: 'change', type: 'array' }],
        statType: [{ required: true, message: '请选择配置统计逻辑', trigger: 'change', type: 'number' }],
      },
      taskSchemeList: [],
      indexList: [],
      logicConfigList: [
        { dataKey: 0, dataValue: '配置不合格数据满足条件' },
        { dataKey: 1, dataValue: '配置合格数据满足条件' },
      ],
      detectionList: [],
      timePickerData: {},
      formData: {},
    };
  },
  created() {
    let modalWidth = '6rem';
    this.$emit('changeModalWidth', modalWidth);
  },
  methods: {
    async getTaskList() {
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, params);
        if (!data.entities.length) return false;
        this.taskSchemeList = data.entities.map((item) => {
          return {
            taskSchemeId: item.taskSchemeId,
            schemeName: item.schemeName,
            regionCode: item.regionCode,
          };
        });
      } catch (e) {
        console.log(e);
      }
    },
    async getIndexList() {
      try {
        const params = {
          taskSchemeId: this.formValidate.taskSchemeId,
          pageNumber: 1,
          pageSize: 9999,
        };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.taskIndexPageList, params);
        if (!data.entities.length) return false;
        this.indexList = data.entities.map((item) => {
          return {
            indexId: item.indexId,
            indexName: item.indexName,
            id: item.id,
          };
        });
      } catch (e) {
        console.log(e);
      }
    },
    async handleChangeTask() {
      this.indexList = [];
      this.formValidate.indexIds = [];
      this.detectionList = [];
      await this.getIndexList();
    },
    handleChangeIndex(val) {
      if (!val.length) return false;
      this.detectionList = val.map((item) => {
        const indexInfoArr = item.split(',');
        let detectObj = {
          indexName: indexInfoArr[1],
          id: indexInfoArr[2],
        };
        if (this.formModel === 'edit' && this.configInfo.details.length) {
          this.configInfo.details.forEach((detailItem) => {
            if (detailItem.id === indexInfoArr[2]) {
              detectObj = { ...detailItem, ...detectObj };
            }
          });
        }
        return detectObj;
      });
    },
    handleUpatePlanData(val) {
      this.formValidate.cronType = val.cronType;
      this.formValidate.cronData = val.cronData;
      if (val.timePoints) {
        this.formValidate.timePoints = val.timePoints;
      }
    },
    async handleSubmit() {
      const valid = await this.$refs['formValidate'].validate();
      const validPlanTime = await this.$refs.planTimePicker.handleValid();
      if (!valid || !validPlanTime) {
        this.$Message.error('请将信息填写完整!');
        return false;
      }
      const details = await this.$refs.detectionForm.handleSave();
      if (!details) return false;
      this.formData = {
        detectMode: '1',
        ...this.formValidate,
      };
      const taskSchemeObj = this.taskSchemeList.find((item) => item.taskSchemeId === this.formValidate.taskSchemeId);
      this.formData.regionCode = taskSchemeObj.regionCode;
      this.formData.indexIds = this.formData.indexIds.map((item) => {
        const arr = item.split(',');
        return arr[0];
      });
      this.formData.details = details.map((item) => {
        delete item.indexName;
        return item;
      });
      return true;
    },
  },
  watch: {
    formModel: {
      async handler(val) {
        await this.getTaskList();
        this.$set(this.formValidate, 'taskSchemeId', val === 'edit' ? this.configInfo.taskSchemeId : '');
        this.formValidate.statType = val === 'edit' ? this.configInfo.statType : 0;
        this.formValidate.criteria = val === 'edit' ? this.configInfo.criteria : 2;
        if (val === 'edit') {
          this.timePickerData = {
            cronType: this.configInfo.cronType || '1',
            cronData: this.configInfo.cronData || [],
            timePoints: this.configInfo.timePoints || [],
          };
          await this.getIndexList();
          const filterData = this.indexList.filter((item) =>
            this.configInfo.indexIds.includes(item.indexId.toString()),
          );
          this.formValidate.indexIds = filterData.map((item) => {
            return `${item.indexId},${item.indexName},${item.id}`;
          });
        }
      },
      immediate: true,
    },
  },
  components: {
    planTimePicker: require('../plan-time-picker').default,
    detectionForm: require('./components/detection-form').default,
  },
};
</script>

<style lang="less" scoped>
.composite-config-content {
  .select-width {
    width: 450px;
  }
}
.detection-list-container {
  max-height: 300px;
  overflow-y: auto;
}
@{_deep} {
  .ivu-form-item {
    width: 100%;
  }
}
</style>
