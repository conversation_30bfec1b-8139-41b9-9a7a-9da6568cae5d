<template>
  <div class="report-info" :class="{ 'report-flex': detailData.reportMethod == 2 && detailData.deviceList.length > 0 }">
    <div class="flex-box">
      <Form :model="detailData" label-position="right" :label-width="90" :label-colon="true">
        <FormItem label="报备名称">
          <span class="font-color">{{ !!detailData ? detailData.reportName : '' }}</span>
        </FormItem>
        <FormItem label="报备区划">
          <span class="font-color">{{ !!detailData ? detailData.civilName : '' }}</span>
        </FormItem>

        <FormItem label="报备类型">
          <span class="font-color" v-if="detailData.reportMethod == 2">设备报备</span>
          <div v-else class="type-content">
            <div class="type-item mr-xs" v-for="(typeItem, typeIndex) in reportTypeList" :key="'typeIndex' + typeIndex">
              <span class="font-color">
                {{ typeItem.text }}(
                <span class="font-red ml-xs mr-xs">{{ checkIndexLen(typeItem) }} </span>
                个检测指标）
              </span>
              <Tooltip placement="right-start">
                <i class="icon-font icon-wenhao f-16 font-D66418"></i>
                <div slot="content">
                  <p>
                    <span class="font-blue">【{{ typeItem.text }}】</span>有<span class="font-red ml-xs mr-xs">{{
                      checkIndexLen(typeItem)
                    }}</span
                    >个检测指标有：
                  </p>
                  <p
                    class="ml-xs"
                    v-for="(correlateItem, corIndex) in correlationIndexList[typeItem.value]"
                    :key="'corIndex' + corIndex"
                  >
                    {{ corIndex + 1 }}、{{ correlateItem.indexName }}
                  </p>
                </div>
              </Tooltip>
              <span class="font-color ml-xs">；</span>
            </div>
          </div>
        </FormItem>
        <FormItem v-if="detailData.reportMethod == 2" label="报备对象">
          <span class="font-color"> 共{{ detailData.deviceList.length }}条设备对象</span>
        </FormItem>
        <FormItem label="报备时间段" v-if="detailData?.beginTime && detailData.endTime">
          <span class="font-color"
            >{{ !!detailData ? detailData.beginTime : '' }} 至 {{ !!detailData ? detailData.endTime : '' }}</span
          >
        </FormItem>
        <FormItem label="报备备注">
          <span class="font-color">{{ !!detailData ? detailData.reportRemark : '' }}</span>
        </FormItem>
        <FormItem label="上传文件">
          <ul>
            <li class="file-list" v-for="(item, index) in detailData.fileUrl" :key="index">
              <span class="file-name font-color ellipsis" :title="item">{{ item }}</span>
              <span class="download-text ml-xs" @click.stop="downLoad(item)">下载</span>
            </li>
          </ul>
        </FormItem>
      </Form>
    </div>

    <div v-if="detailData.reportMethod == 2 && detailData.deviceList.length > 0" class="box-line"></div>
    <div class="auto-fill" v-if="detailData.reportMethod == 2 && detailData.deviceList.length > 0">
      <div class="mb-sm">
        <Button type="primary" class="fr" @click="getExport" :loading="exportLoading">
          <i class="icon-font icon-daochu f-14 mr-xs"></i>
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <ui-table
        class="ui-table unsetModal"
        reserveSelection
        :table-columns="tableColumns"
        :table-data="detailData.deviceList"
        :loading="loading"
        :defaultStoreData="defaultStoreData"
      >
        <template slot="reportReason" slot-scope="{ row, index }">
          <Select
            class="input-width"
            v-model="row.reportReason"
            placeholder="请选择报备原因"
            clearable
            disabled
            @on-change="handleChangeSele($event, row.reportReason, index)"
          >
            <Option v-for="item in reportReason" :key="item.dataKey" :value="item.dataKey">{{ item.dataValue }}</Option>
          </Select>
        </template>
        <template slot="beginTime" slot-scope="{ row, index }">
          <DatePicker
            class="width-mds"
            v-model="row.beginTime"
            type="date"
            placeholder="报备开始时间"
            confirm
            disabled
            @on-change="handleChangeBegin($event, row.beginTime, index)"
          ></DatePicker>
        </template>
        <template slot="endTime" slot-scope="{ row, index }">
          <DatePicker
            class="width-mds"
            v-model="row.endTime"
            type="date"
            placeholder="报备结束时间"
            confirm
            disabled
            @on-change="handleChangeEnd($event, row.endTime, index)"
          ></DatePicker>
        </template>
      </ui-table>
    </div>
  </div>
</template>

<script>
import examination from '@/config/api/examination.js';
import { reportModalTableColumns } from '.././situationreporting.js';
import { mapGetters } from 'vuex';
export default {
  name: 'report-info',
  props: {
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formData: {
        deviceList: [],
        reportMethod: '',
      },
      number: 0,
      reportTypeList: [],
      correlationIndexList: {},
      tableColumns: [],
      loading: false,
      defaultStoreData: [],
      exportLoading: false,
    };
  },
  mounted() {
    this.tableColumns = reportModalTableColumns;
  },
  methods: {
    async getCorrelationIndexListInfo() {
      try {
        let {
          data: { data },
        } = await this.$http.get(examination.getCorrelationIndexList);
        this.correlationIndexList = data;
      } catch (error) {
        console.log(error);
      }
    },
    getFileName(val) {
      let fileName = val && val.split('/').pop();
      return fileName || '';
    },
    downLoad(val) {
      this.$util.common.transformBlob(val, this.getFileName(val));
    },
    checkIndexLen(typeItem) {
      if (!Object.keys(this.correlationIndexList).length) return 0;
      return this.correlationIndexList[typeItem.value].length || 0;
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let { id } = this.detailData;
        let config = {
          responseType: 'blob',
        };
        let form = new FormData();
        form.append('id', id);
        let res = await this.$http.post(examination.deviceListExport, form, config);
        await this.$util.common.exportfile(res);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  computed: {
    ...mapGetters({
      reportReason: 'algorithm/evaluation_report_reason',
    }),
  },
  watch: {
    'detailData.reportType': {
      handler(val) {
        if (!val) return false;
        this.getCorrelationIndexListInfo();
      },
      immediate: true,
    },
    'detailData.reportTypeText': {
      handler(val) {
        if (!val) return false;
        const reportTypeTextArr = val.indexOf(',') !== -1 ? val.split(',') : [val];
        const reportTypeArr =
          this.detailData.reportType.indexOf(',') !== -1
            ? this.detailData.reportType.split(',')
            : [this.detailData.reportType];
        this.reportTypeList = reportTypeArr.map((item, index) => {
          let obj = {};
          obj.value = item;
          obj.text = reportTypeTextArr[index];
          return obj;
        });
      },
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.font-color {
  color: var(--color-content);
}

.report-info {
  font-size: 14px;

  ul {
    width: 100%;
  }

  .file-list {
    width: 100%;
    display: flex;
    align-items: center;
  }

  .file-name {
    display: inline-block;
    flex: 1;
  }

  .download-text {
    width: 50px;
    color: #438cff;
    cursor: pointer;
  }

  .type-content {
    display: flex;
    flex-wrap: wrap;
  }
  .type-item {
    //width: 50%;
  }

  @{_deep} .ivu-form-item {
    margin-bottom: 5px !important;
  }
}
.box-line {
  width: 1px;
  background: var(--border-modal-footer);
  margin: 0 10px;
}
.report-flex {
  display: flex;
  .flex-box {
    width: 550px;
  }
}
.width-mds {
  width: 150px;
}
</style>
