const basePre = '/ivdg-evaluation-app';
export default {
  getTokenPageList: basePre + '/cascade/token/pageList', // 查询级联token分页列表
  addToken: basePre + '/cascade/token/add', // 查询级联token分页列表
  removeToken: basePre + '/cascade/token/remove ', // 查询级联token分页列表
  refreshToken: basePre + '/cascade/token/refreshToken  ', // 查询级联token分页列表
  updateToken: basePre + '/cascade/token/update  ', // 更新token

  getSelfIssuePageList: basePre + '/cascade/issue/pageList', // 本级查询级联问题清单分页列表
  pageListBySuperior: basePre + '/cascade/issue/api/pageList', // 查询上级下发分页列表
  cascadeIssueStatistics: basePre + '/cascade/issue/getStatistics', // 本级级联清单统计
  getSuperiorResult: basePre + '/issue/getSuperiorResult', // 获取父类结果数据, 会检验签名【用于下级请求上级调用】
  getCascadeConfig: basePre + '/cascade/token/getConfig', // 获取配置信息
  postCascadeIssue: basePre + '/cascade/issue/api/handleIssue', // 问题处理
  publishIssue: basePre + '/cascade/issue/publish', // 下发问题清单

  getIndexResult: basePre + '/cascade/issue/api/getIndexResult', // 获取父类结果详情数据’
  getIndexResultByInputStream: basePre + '/cascade/issue/api/getIndexResultByInputStream', // 获取父类导出流数据
  viewResult: basePre + '/cascade/issue/view', // 查看结果详情
  // getSelfIssuePageList: basePre+'/cascade/issue/pageList', // 本级查询级联问题清单分页列表
};
