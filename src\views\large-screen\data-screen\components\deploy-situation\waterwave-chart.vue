<!--
    * @FileDescription: 布控战果转化-水波图
    * @Author: H
    * @Date: 2024/05/06
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="waterWave" class="waterWave" ref="waterWave"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import 'echarts-liquidfill';
export default{
    data() {
        return {
            option: {
                // backgroundColor: '#0F224C',
                series: [
                    {
                        type: 'liquidFill',
                        radius: '90%',
                        center: ['50%', '50%'],
                        backgroundStyle: {
                            color: 'transparent',
                        },
                        data: [ 0.4, 0.6 ],
                        amplitude: 10,
                        label: {
                            position: [ '50%', '45%'],
                            formatter: '380%',
                            textStyle: {
                                fontSize: '24px',
                                color: '#fff',
                                fontWeight: '400',
                            },
                        },
                        outline: {
                            borderDistance: 2,
                            itemStyle: {
                                borderWidth: 1,
                                borderColor: {
                                    type: 'linear',
                                    x:1,
                                    y:0,
                                    x2:0,
                                    y2:0,
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: '#007DFF',
                                        },
                                        {
                                            offset: 0.6,
                                            color: 'rgba(45, 67, 114, 1)',
                                        },
                                        {
                                            offset: 1,
                                            color: 'rgba(45, 67,114, 1)'
                                        }
                                    ],
                                    globalCoord: false
                                }
                            },
                        },
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0,1,0,0, [
                                { offset:0, color: 'rgba(0, 150, 255, 1)' },  
                                { offset:1, color: 'rgba(0, 150, 255, 0)' },  
                            ]) 
                            
                        },
                        
                    }
                ]
            },
            myEchart: null
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            this.myEchart = echarts.init(this.$refs.waterWave)
            let option = {
                // title: this.title,
                // legend: this.legend,
                // grid: this.grid,
                // xAxis: this.xAxis,
                // yAxis: this.yAxis,
                // tooltip: this.tooltip,
                // series: this.series
            }
            this.myEchart.setOption(this.option)
            window.addEventListener('resize', () => this.myEchart.resize())
        },
        handleChange() {

        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 33%;
    position: relative;
    .waterWave{
        height: 100%;
        width: 100%;  
    }
}
/deep/ .ivu-select{
    &:hover{
        color: #567BBB;
    }
    .ivu-select-selection{
        border: 1px solid #098EFF;
        background: rgba(9,142,255, 0.1);
        color: #567BBB;
    }
    .ivu-select-selected-value{
        color: #567BBB;
    }
    .ivu-select-arrow{
        color: #567BBB;
    }
    .ivu-select-dropdown{
        background: rgba(9,142,255, 0.3);
    }
    .ivu-select-item, .ivu-select-placeholder{
        color: #567BBB;
    }
    .ivu-select-item-selected:hover, 
    .ivu-select-item:hover, 
    .ivu-select-placeholder:hover, 
    .ivu-select-selection:hover{
        color: #fff;
    }
    .ivu-select-item-selected{
        color: #fff;
    }
}
</style>