<template>
  <div class="auto-fill">
    <component
      :is="currentPage"
      @refeshCount="refeshCount"
      :currentTree="currentTree"
      :taskObj="taskObj"
      :countInfo="countInfo"
    />
  </div>
</template>

<script>
import api from '@/config/api/inspectionrecord';
import { mapGetters } from 'vuex';
export default {
  name: 'International-encoding-detection',
  components: {
    surveyPage: require('./survey').default, // 201 - 检测概况
    realTime: require('./real-time').default, // 202 -重点实时视频通畅率,  203 - 普通实时视频可调阅率
    historyPage: require('./history').default, // 204 -重点历史录像通畅率  , 205 - 普通历史录像可调阅率
    devicePage: require('./device').default, // 206 - 重点时钟准确率,  207 - 普通时钟准确率
    osdPage: require('./osd').default, // 208 - 重点字幕标注合规率,  209 - 普通字幕标注合规率
    VideoGeneralHistoryAccuracy: require('./video-general-history-accuracy').default, // 210 - 历史视频录像完整率
    VideoStream: require('./video-stream').default, // 212 - 视频流质量检测
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      current: 5,
      currentPage: '',
      countInfo: {},
      orgCode: null,
    };
  },
  mounted() {
    this.current = this.currentTree.id;
    // this.orgCode = this.orgTreeData[0].orgCode
    if (this.current != 201) this.init();
    if (this.currentTree.id == 201) this.currentPage = 'surveyPage';
    if (this.currentTree.id == 202 || this.currentTree.id == 203) this.currentPage = 'realTime';
    if (this.currentTree.id == 204 || this.currentTree.id == 205) this.currentPage = 'historyPage';
    if (this.currentTree.id == 206 || this.currentTree.id == 207) this.currentPage = 'devicePage';
    if (this.currentTree.id == 208 || this.currentTree.id == 209) this.currentPage = 'osdPage';
    if (this.currentTree.id == 210) this.currentPage = 'VideoGeneralHistoryAccuracy';
    if (this.currentTree.id == 212) this.currentPage = 'VideoStream';
  },
  methods: {
    // 查询上面统计信息
    init() {
      if (this.current == 201) {
        return;
      }
      if (!this.orgCode || !this.taskObj.indexId) {
        return;
      }
      let val = [202, 204, 206, 208].includes(this.currentTree.id) ? 0 : 1; // 0 普通 1 重点
      let apiKey = api.queryEvaluatingVideoCount;
      let param = {
        deviceTagCategory: val,
        orgCode: this.orgCode,
        indexId: this.taskObj.indexId,
        type: this.currentTree.type,
        batchId: this.taskObj.batchId,
      };
      this.$http.post(apiKey, param).then((res) => {
        this.countInfo = res.data.data;
      });
    },
    refeshCount(orgCode) {
      this.orgCode = orgCode;
      this.init();
    },
  },
  computed: {
    ...mapGetters({
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      // orgTreeData: 'common/getOrganizationList',
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.orgCode = this.taskObj.regionCode;

        this.init();
      },
    },
    // orgTreeData: {
    //   deep: true,
    //   handler: function(val) {
    //   },
    // },
  },
};
</script>

<style lang="less" scoped>
.btn {
  margin: 0 6px;
}
</style>
