<template>
  <div :class=" !disHover ? 'ui-card':'ui-card-hover'">
    <div class="card-head">
      <p v-if="title" class="title linear-gradient-color"><span>{{ title }}</span></p>
      <slot v-else name="title"></slot>
      <slot name="content"></slot>
      <slot name="extra"></slot>
    </div>
    <!-- <div v-if="show">
    </div> -->
    <div :style="{padding: paddingValue}" class="card-content">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    // 卡片标题
    title: {
      type: String,
      default: ''
    },
    // 卡片内部间距，单位 px
    padding: {
      type: [Number, String],
      default: 20
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false
    },
    // 展示更多内容
    show: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    //   paddingValue: this.padding / 192 + 'rem'
    }
  },
    computed:{
        paddingValue(){
            if(this.padding.constructor === Number){
                return this.padding / 192 + 'rem';
            }else{
                let padding = this.padding.split(',');
                return padding[0] / 192 + 'rem' + ' ' + padding[1] / 192 + 'rem'
            }
        }
    }
}
</script>