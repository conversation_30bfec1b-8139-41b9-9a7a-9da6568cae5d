// 验证菜单列表权限加载不同的菜单
export function menuPermission(list) {
  let menu = JSON.parse(sessionStorage.getItem('menuPermission'));
  let permissionList = [];
  list.forEach((row) => {
    if (!!menu && !!menu[row.name]) permissionList.push(row);
  });
  return permissionList;
}

// 判断时候有该菜单的权限
export function includePermission(permissions = []) {
  if (!permissions.length) return true;
  let menu = JSON.parse(sessionStorage.getItem('menuPermission'));
  return !!permissions.find((permission) => !!menu[permission]);
}

/**
 * 创建重定向函数
 * @param {Object} redirect - 重定向对象
 * @param {string} redirect.name - 重定向的组件名称
 * @param {Array<any>} children - 子列表
 */
function createRedirectFn(redirect = {}, children = []) {
  // 避免缓存太大，只保留 children 的 name 和 permissions
  const permissionChildren = children.map(({ name = '', meta: { permissions = [] } = {} }) => ({ name, permissions }));
  return function () {
    // 这里一定不能在 return 的函数外面筛选，因为权限是异步获取的
    let children = [];
    const ModulePermission = JSON.parse(window.sessionStorage.getItem('ModulePermission'));
    const hasPermissionChildren = permissionChildren.filter((item) => includePermission(item.permissions));
    // 过滤是否有授权
    hasPermissionChildren.forEach((row) => {
      if (!(!!ModulePermission && ModulePermission.licenseAbilities[row.name] === false)) {
        children.push(row);
      }
    });
    // 默认填写的重定向的 name
    const defaultName = redirect.name || '';
    // 如果默认重定向没有权限，则从 children 中选择第一个有权限的路由做重定向
    const firstPermissionName = (children[0] || { name: '' }).name;
    // 判断是否需要修改默认的重定向
    const saveDefaultName = !!children.find((item) => item.name === defaultName && defaultName);
    if (saveDefaultName) return { name: defaultName };
    else return firstPermissionName ? { name: firstPermissionName } : redirect;
  };
}

/**
 * 创建有权限的路由配置（多级）
 * @param {Object} config - 路由配置对象
 * @param {Object} config.redirect - 必须是 children 中的一个，并且使用 name
 */
export function createPermissionRouter({ redirect, path, meta, children = [], ...others }) {
  const needRecursion = !!children.length;
  if (needRecursion) {
    return {
      ...others,
      path,
      meta,
      redirect: createRedirectFn(redirect, children),
      children: children.map((item) => createPermissionRouter(item)),
    };
  } else {
    if (path === '/') {
      return {
        ...others,
        path,
        meta,
        children,
        redirect: createMainPermission(meta),
      };
    } else {
      return {
        ...others,
        path,
        meta,
        redirect,
      };
    }
  }
}
/**
 * 总路由redirect根据permission生成
 */
export function createMainPermission(meta) {
  if (!meta) return false;
  return function () {
    let menu = JSON.parse(sessionStorage.getItem('menuPermission'));
    let ModulePermission = JSON.parse(sessionStorage.getItem('ModulePermission'));
    let permissionList = [];
    meta.permissions.forEach((row) => {
      if (ModulePermission) {
        if (!!menu && !!menu[row] && ModulePermission.licenseAbilities[row]) {
          permissionList.push(row);
        }
      } else {
        if (!!menu && !!menu[row]) {
          permissionList.push(row);
        }
      }
    });
    return { name: permissionList[0] };
  };
}
