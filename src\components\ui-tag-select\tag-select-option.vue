<template>
	<div :class="classes" @click="handleChange">
		<img v-if="checked && effect" src="@/assets/img/tag_check_icon.png" class="ivu-tag-select-checked-icon" alt="" />
		<slot></slot>
	</div>
</template>
<script>
export default {
	name: 'TagSelectOption',
	inject: ['TagSelectInstance'],
	props: {
		name: {
			type: [String, Number],
			required: true
		},
		label: {
			type: [String, Number],
		},
		// 主题
		effect: {
			type: String,
			default: ''
		}
	},
	data() {
		return {
			checked: false,
		}
	},
	computed: {
		classes() {
			return {
				'ivu-tag-select-option': true,
				'ivu-tag-select-option-border': this.effect === 'dark',
				'ivu-tag-select-option-dark': this.effect === 'dark' && this.checked,
				'select': this.effect !== 'dark' && this.checked
				// primary: !this.effect && this.checked
			}
		}
	},
	methods: {
		handleChange() {
			if (this.checked) {
				this.checked = false
			} else {
				this.checked = true
			}
			this.TagSelectInstance.handleChangeTag(this.name)
		}
	}
}
</script>
<style lang="less" scoped>
.ivu-tag-select-option-border {
	border: 1px solid #fff;
	box-sizing: border-box;
}
.ivu-tag-select-option-dark {
	border-radius: 2px;
	border: 1px solid #2c86f8;
	position: relative;
	box-sizing: border-box;
	.ivu-tag-select-checked-icon {
		width: 12px;
		height: 12px;
		position: absolute;
		bottom: 0;
		right: 0;
		z-index: 10;
	}
}

.select {
	color: #fff;
	background: #2C86F8;
	padding: 0 6px;
	border-radius: 2px;
}
</style>
