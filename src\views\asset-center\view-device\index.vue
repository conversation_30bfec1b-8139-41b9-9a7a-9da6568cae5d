<template>
	<div class="container">
		<div class="device">
			<!-- 查询 -->
			<Search ref="search" @searchForm="searchForm" />
			<div class="card-content">
				<div v-for="(item, index) in list" :key="index" class="card-item">
					<UiListCard type="device" :data="item" :index="index" @archivesDetailHandle="archivesDetailHandle(item)" @collection="getList" />
				</div>
				<ui-empty v-if="list.length === 0"></ui-empty>
				<ui-loading v-if="loading"></ui-loading>
			</div>
			<!-- 分页 -->
			<ui-page :current="params.pageNumber" :total="total" :page-size="params.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
		</div>
	</div>
</template>
<script>
	import { mapActions } from 'vuex'
	import Search from './components/search.vue'
	import UiListCard from '@/components/ui-list-card'
	import { queryDeviceInfoPageList } from '@/api/device'
	export default {
		name: 'one-plane-one-archives',
		components: {
			Search,
			UiListCard
		},
		props: {},
		data() {
			return {
				list: [],
				loading: false,
				pageForm: {},
				total: 0,
				params: {
					pageNumber: 1,
					pageSize: 20
				}
			}
		},

		created() {
			if(this.$route.query.keyWords){
				this.pageForm.deviceName = JSON.parse(this.$route.query.keyWords)
				this.$nextTick(() => {
					this.$refs.search.formData.deviceName = this.pageForm.deviceName
				})
			}
			this.getDictData()
			this.getList()
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			// 查询列表
			getList() {
				var param = Object.assign(this.pageForm, this.params)
				var labelIds = [];
				if (param.labelIds && param.labelIds.length > 0) {
					param.labelIds.forEach(item => {
						if (item && item != undefined) {
							labelIds.push(item.id)
						}
					})
					param.labelIds = labelIds
				}
				queryDeviceInfoPageList(param).then(res => {
					const { entities, pageNumber, pageSize, total } = res.data;
					this.list = entities || [];
					this.params.pageNumber = pageNumber;
					this.params.pageSize = pageSize;
					this.total = total;
				})
			},
			// 档案详情
			archivesDetailHandle(val) {
				const { href } = this.$router.resolve({
					name: 'device-archive',
					query: { archiveNo: val.deviceId }
				})
				window.open(href, '_blank')
			},
			// 查询
			searchForm(form) {
				this.pageForm = JSON.parse(JSON.stringify(form))
				this.params.pageNumber = 1
				this.getList()
			},
			// 改变页码
			pageChange(pageNumber) {
				this.params.pageNumber = pageNumber
				this.getList()
			},
			// 改变分页个数
			pageSizeChange(size) {
				this.params.pageSize = size
				this.params.pageNumber = 1
				this.getList()
			}
		}
	}
</script>
<style lang="less" scoped>
	.device {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;
		.card-content {
			display: flex;
			flex-wrap: wrap;
			overflow: auto;
			flex: 1;
			margin: 0 -5px;
			align-content: flex-start;
			position: relative;
			.card-item {
				width: 20%;
				padding: 0 5px;
				box-sizing: border-box;
				margin-bottom: 10px;
				transform-style: preserve-3d;
				transition: transform 0.6s;
				.list-card {
					width: 100%;
					backface-visibility: hidden;
					height: 198px;
					/deep/.list-card-content-body {
						height: 115px;
					}
					/deep/.content-img {
						width: 115px;
						height: 115px;
					}
					/deep/.tag-wrap {
						margin-top: 7px;
						.ui-tag {
							margin: 0 5px 0 0 !important;
						}
					}
				}
			}
		}
	}
</style>
