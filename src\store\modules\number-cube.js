import { relationStat } from "@/api/number-cube";

export default {
  namespaced: true,
  state: {
    peopleRelationList: [], // 实名档案关系统计
    videoRelationList: [], // 视频档案关系统计
    vehicleRelationList: [], // 车辆档案关系统计
    tabsList: [], // tabs列表
    tabFlag: "", // tab标识
    routeFlag: 1, // tab路由标识
    source: 1, // 来源：1-数智立方查询、我的画布点击进入，2-全局tabs点击进入
  },
  mutations: {
    addRouteFlag(state) {
      state.routeFlag = state.routeFlag + 1;
      console.log("动态路由标识---", state.routeFlag);
    },
    setTabsList(state, list) {
      state.tabsList = list;
    },
    setTabsFlag(state, flag) {
      state.tabFlag = flag;
    },
    setSource(state, type) {
      state.source = type;
    },
  },
  getters: {
    getPeopleRelationList(state) {
      return state.peopleRelationList;
    },
    getVideoRelationList(state) {
      return state.videoRelationList;
    },
    getVehicleRelationList(state) {
      return state.vehicleRelationList;
    },
    getRouteFlag(state) {
      return state.routeFlag;
    },
    getTabsList(state) {
      return state.tabsList;
    },
    getTabsFlag(state) {
      return state.tabFlag;
    },
    getSource(state) {
      return state.source;
    },
  },
  actions: {
    /**
     * 档案 - 关系统计
     */
    relationStat({ state, commit }, { type, searchKey, graphId }) {
      return new Promise((resolve, reject) => {
        var flag = false;
        if (type == "people" && state.peopleRelationList.length > 0)
          flag = true;
        if (type == "video" && state.videoRelationList.length > 0) flag = true;
        if (type == "vehicle" && state.vehicleRelationList.length > 0)
          flag = true;

        if (flag) {
          resolve();
          return false;
        } // 判断如果 请求过 不在进行接口请求

        // if (type == 'people') {
        //   searchKey = "350781196403073567"
        // }
        // if (type == 'vehicle') {
        //   searchKey = "川A14JM8"
        // }

        var name = "real_name_archive";
        if (type == "video") name = "vid_archive";
        if (type == "vehicle") name = "vehicle_archive";

        var param = {
          name: name,
          searchKey: searchKey,
          graphId: graphId,
        };
        relationStat(param)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    addRouteFlag({ commit }) {
      commit("addRouteFlag");
    },
    setTabsList({ commit }, list) {
      commit("setTabsList", list);
    },
    setTabsFlag({ commit }, flag) {
      commit("setTabsFlag", flag);
    },
    setSource({ commit }, type) {
      commit("setSource", type);
    },
  },
};
