import axios from "axios";
import map from "@/api/map";
import { getLicenseInfo, getAuthorUrl } from "@/api/user";
import { addMyCloudSearch } from "@/api/home";
// eslint-disable-next-line no-unused-vars
const CancelToken = axios.CancelToken;
export default {
  namespaced: true,
  state: {
    theme:'light',
    // 用于终止接口访问
    source: {
      token: null,
      cancel: null,
    },
    innerHeight: "", // 屏幕可用高度用来自适应屏幕
    routeFromData: {},
    mapConfig: null, // 地图配置
    mapStyle: null, // 地图风格化样式
    mapCancel: null, // 取消重复请求地图配置
    mapStyleCancel: null, // 取消重复请求地图样式
    authorInfo: "", // 授权信息
    wisdomCloudSearchData: {
      // 存储首页传参
      keyWords: "",
      algorithmType: "1",
      similarity: 80,
      urlList: [],
    },
    classifySearchData: {
      // 分类搜索
      list: [],
      timeSlot: "",
      startDate: "",
      endDate: "",
      searchSelect: 0,
    },
    parsingLibrarySearchData: [
      {},{},{},{},{}
    ], // 视图解析库搜索
    // {
    //   // 视图解析库搜索
    //   selectType: "",
    //   selectDeviceList: [], // 选择设备
    //   selectTaskList: [], // 选择任务
    //   selectFileList: [], // 选择文件
    //   timeSlot: "",
    //   startDate: "",
    //   endDate: "",
    //   searchSelect: 0,
    // },
    pageType: 0, // 页面类型  0-关键字搜索， 1-精准搜索
    noMenu: false, // 控制是否有菜单
  },
  getters: {
    getInnerHeight(state) {
      return state.innerHeight;
    },
    getNoMenu(state) {
      return state.noMenu;
    },
    getTagList(state) {
      return state.tagList;
    },
    getMapConfig(state) {
      return state.mapConfig;
    },
    getMapStyle(state) {
      return state.mapStyle;
    },
    getAuthorInfo(state) {
      return state.authorInfo;
    },
    getWisdomCloudSearchData(state) {
      return state.wisdomCloudSearchData;
    },
    getClassifySearchData(state) {
      return state.classifySearchData;
    },
    getParsingLibrarySearchData(state) {
      return state.parsingLibrarySearchData;
    },
    getPageType(state) {
      return state.pageType;
    },
  },
  mutations: {
    setGlobalTheme(state, theme) {
      state.theme = theme;
    },
    setSource(state, source) {
      state.source = source;
    },
    setNoMenu(state, noMenu) {
      state.noMenu = noMenu;
    },
    setInnerHeight(state, innerHeight) {
      state.innerHeight = innerHeight;
    },
    routerChange(state, routeFromData) {
      state.routeFromData = routeFromData;
    },
    setMapConfig(state, mapConfig) {
      state.mapConfig = mapConfig;
    },
    setMapStyle(state, mapStyle) {
      state.mapStyle = mapStyle;
    },
    setMapCancel(state, mapCancel) {
      state.mapCancel = mapCancel;
    },
    setMapStyleCancel(state, mapStyleCancel) {
      state.mapStyleCancel = mapStyleCancel;
    },
    setAuthorInfo(state, authorInfo) {
      state.authorInfo = authorInfo;
    },
    setWisdomCloudSearchData(state, wisdomCloudSearchData) {
      state.wisdomCloudSearchData = wisdomCloudSearchData;
    },
    setClassifySearchData(state, classifySearchData) {
      state.classifySearchData = classifySearchData;
    },
    setParsingLibrarySearchData(state, parsingLibrarySearchData) {
      let {
        selectType,
        selectDeviceList,
        selectTaskList,
        selectFileList,
        timeSlot,
        startDate,
        endDate,
        menuIndex,
      } = { ...parsingLibrarySearchData };
      let data = {
        selectType,
        selectDeviceList,
        selectTaskList,
        selectFileList,
        timeSlot,
        startDate,
        endDate,
        searchSelect: 1,
      };
      state.parsingLibrarySearchData[menuIndex] = data
    },
    setPageType(state, val) {
      state.pageType = val;
    },
  },
  actions: {
    // 云搜新增
    addCloud({ commit, state }, CloudSearchData) {
      let obj = state.wisdomCloudSearchData;
      let features = [],
        imageBases = [];
      obj.urlList.map((item) => {
        if (item) {
          features.push(item.feature);
          imageBases.push(item.fileUrl);
        }
      });
      let params = {};
      if (obj.keyWords !== "") {
        params = {
          keyWords: obj.keyWords,
        };
      } else {
        params = {
          searchType: obj.algorithmType,
          features: features,
          imageBases: imageBases,
        };
      }
      params.dateType = obj.dateType;
      params.startDate = obj.startDate;
      params.endDate = obj.endDate;
      addMyCloudSearch(params).then((res) => {});
    },
    routerChange({ commit, state }, routeFromData) {
      commit("routerChange", routeFromData);
    },
    setSource({ commit }, source) {
      commit("setSource", source);
    },
    setInnerHeight({ commit }, innerHeight) {
      commit("setInnerHeight", innerHeight);
    },
    async setMapStyle({ commit, state }) {
      if (!state.mapStyle) {
        if (!state.mapStyleCancel) {
          try {
            const res = await axios.get(map.getMapStyle, {
              cancelToken: new CancelToken((c) => {
                commit("setMapStyleCancel", c);
              }),
            });
            commit("setMapStyle", res.data);
            commit("setMapStyleCancel", null);
          } catch (err) {
            commit("setMapStyleCancel", null);
            console.log(err);
          }
        } else {
          state.mapStyleCancel("重复请求地图接口");
        }
      }
    },
    async setMapConfig({ commit, state, dispatch, rootState }) {
      if (!state.mapConfig) {
        if (!state.mapCancel) {
          try {
            const res = await axios.get(map.getMapConfig, {
              cancelToken: new CancelToken((c) => {
                commit("setMapCancel", c);
              }),
            });
            // 地图本地json配置 + 系统配置默认层级和中心坐标
            await dispatch("systemParam/getSystemAllData", {}, { root: true });
            let data = res.data;
            let mapOpts = data.mapOpts;
            let mapCenterPoint = rootState.systemParam.globalObj.mapCenterPoint;
            if (!!rootState.systemParam.globalObj.mapLayerLevel) {
              mapOpts.defaultZoom = parseInt(
                rootState.systemParam.globalObj.mapLayerLevel
              );
            }
            // !!rootState.systemParam.globalObj.mapLayerLevel ? mapOpts.defaultZoom = parseInt(rootState.systemParam.globalObj.mapLayerLevel) : null
            if (!!mapCenterPoint) {
              let mapCenterPointArray = mapCenterPoint.split("_");
              if (!!mapCenterPointArray.length == 2) {
                mapOpts.centerPoint = [
                  parseInt(mapCenterPointArray[0]),
                  parseInt(mapCenterPointArray[1]),
                ];
              }
              //   !!mapCenterPointArray.length == 2 ? mapOpts.centerPoint = [parseInt(mapCenterPointArray[0]),parseInt(mapCenterPointArray[1])] : null
            }
            commit("setMapConfig", data);
            commit("setMapCancel", null);
          } catch (err) {
            commit("setMapCancel", null);
            console.log(err);
          }
        } else {
          state.mapCancel("重复请求地图接口");
        }
      }
    },
    // 设置授权信息
    setAuthor({ commit }) {
      return new Promise((resolve, reject) => {
        getAuthorUrl()
          .then((resUrl) => {
            if (resUrl.data) {
              window.localStorage.setItem("authorUrl", resUrl.data.paramValue);
            } else {
              window.localStorage.setItem(
                "authorUrl",
                window.location.host + "/404"
              );
            }
            getLicenseInfo()
              .then((res) => {
                window.localStorage.setItem(
                  "authorInfo",
                  JSON.stringify(res.data)
                );
                commit("setAuthorInfo", res.data);
                resolve(true);
              })
              .catch(() => {
                reject(window.localStorage.getItem("authorUrl"));
              });
          })
          .catch(() => {});
      });
    },
  },
};
