import common from '@/config/api/common';
import user from '@/config/api/user';
import map from '@/config/api/map';

import { arrayToJson, deepCopy } from '@/util/module/common';
import axios from 'axios';
const cancelToken = axios.CancelToken;
export default {
  namespaced: true,
  state: {
    // 用于终止接口访问
    source: {
      token: null,
      cancel: null,
    },
    innerHeight: '', //屏幕可用高度用来自适应屏幕
    initialAreaList: [], //最初的一维数组辖区别表
    areaList: [], //树状辖区列表
    areaCancel: null, //取消重复请求区域接口
    defaultSelectedArea: '', //默认选中有权限的区域
    initialOrgList: [], //最初的一维数组组织机构列表
    organizationList: [], //组织结构列表
    organizationCancel: null, //取消重复请求组织机构接口
    defaultSelectedOrg: '', //默认选中有权限的组织机构
    orgDefaultkeys: [],
    cameraModuleDefaultKeys: [], //默认展开的列表
    cameraList: [], //摄像头列表
    cameraCancel: null, //取消重复请求摄像机接口
    systemConfig: {}, //系统名称
    comprehensiveConfig: {}, //治理评价综合统计配置
    cityAll: [], //所有城市列表 用于省市县3级联动
    mapConfig: null, //地图配置
    mapCancel: null, //取消重复请求地图配置
    mapStyle: null, //地图风格化样式
    mapStyleCancel: null, //取消重复请求地图样式
    modulePermission: null, //模块权限列表
    isCollapsed: true, //菜单展开缩放
    tipsShow: true, // table的tips显示
    mapSelectTreeList: [],
    deviceMapCameraList: [],
    authorInfo: '', //授权信息
    navConfigType: 'normal', // horizon 横版 normal 前一版本

    themeType: window.localStorage.getItem('theme') || 'dark',
    isShowXiaoZhi: false, // 是否显示小智机器人
    isPortalSkip: false, // 是否从门户页面调转
    portalConfig: '/', // 门户地址 可选值 "/" "portal"
    iframeConfig: '', // iframe地址 如： http://************:9999
  },

  getters: {
    getNavConfigType(state) {
      return state.navConfigType;
    },
    getSource(state) {
      return state.source;
    },
    getInnerHeight(state) {
      return state.innerHeight;
    },
    getInitialAreaList(state) {
      return state.initialAreaList;
    },
    getAreaList(state) {
      return state.areaList;
    },
    getDefaultSelectedArea(state) {
      return state.defaultSelectedArea;
    },
    getDefaultSelectedOrg(state) {
      return state.defaultSelectedOrg;
    },
    getInitialOrgList(state) {
      return state.initialOrgList;
    },
    getOrganizationList(state) {
      return state.organizationList;
    },
    getMapSelectTreeList(state) {
      return state.mapSelectTreeList;
    },
    getCameraList(state) {
      return state.cameraList;
    },
    getSystemConfig(state) {
      return state.systemConfig;
    },
    getComprehensiveConfig(state) {
      return state.comprehensiveConfig;
    },
    getDefaultExpandedKeys(state) {
      return state.cameraModuleDefaultKeys;
    },
    getDefaultOrgExpandedKeys(state) {
      return state.orgDefaultkeys;
    },
    getCityAll(state) {
      return state.cityAll;
    },
    getMapConfig(state) {
      return state.mapConfig;
    },
    getMapStyle(state) {
      return state.mapStyle;
    },
    //模块权限
    getModulePermission(state) {
      return state.modulePermission;
    },
    getIsCollapsed(state) {
      return state.isCollapsed;
    },
    getTipsShow(state) {
      return state.tipsShow;
    },
    getDeviceMapCameraList(state) {
      return state.deviceMapCameraList;
    },
    getThemeType(state) {
      return state.themeType;
    },
    getIsShowXiaoZhi(state) {
      return state.isShowXiaoZhi;
    },
    getIsPortalSkip(state) {
      return state.isPortalSkip;
    },
    getPortalConfig(state) {
      return state.portalConfig;
    },
    getIframeConfig(state) {
      return state.iframeConfig;
    },
  },
  mutations: {
    setSource(state, source) {
      state.source = source;
    },
    setInnerHeight(state, innerHeight) {
      state.innerHeight = innerHeight;
    },
    setInitialAreaList(state, initialAreaList) {
      state.initialAreaList = initialAreaList;
    },
    setAreaList(state, areaList) {
      state.areaList = areaList;
    },
    setDefaultSelectedArea(state, defaultSelectedArea) {
      state.defaultSelectedArea = defaultSelectedArea;
    },
    setAreaCancel(state, areaCancel) {
      state.areaCancel = areaCancel;
    },
    removeAreaList(state) {
      state.areaList.length = 0;
    },
    setInitialOrgList(state, initialOrgList) {
      state.initialOrgList = initialOrgList;
    },
    setOrganizationList(state, organizationList) {
      state.organizationList = organizationList;
    },
    setMapSelectTreeList(state, mapSelectTreeList) {
      state.mapSelectTreeList = mapSelectTreeList;
    },
    setDefaultSelectedOrg(state, defaultSelectedOrg) {
      state.defaultSelectedOrg = defaultSelectedOrg;
    },
    setOrganizationCancel(state, organizationCancel) {
      state.organizationCancel = organizationCancel;
    },
    removeOrganizationList(state) {
      state.organizationList.length = 0;
    },
    setOrgDefaultKeys(state, orgDefaultkeys) {
      state.orgDefaultkeys = orgDefaultkeys;
    },
    setCameraModuleDefaultKeys(state, cameraModuleDefaultKeys) {
      state.cameraModuleDefaultKeys = cameraModuleDefaultKeys;
    },
    setCameraList(state, cameraList) {
      state.cameraList = cameraList;
    },
    setCameraCancel(state, cameraCancel) {
      state.cameraCancel = cameraCancel;
    },
    removeCameraList(state) {
      state.cameraList.length = 0;
    },
    systemConfig(state, systemConfig) {
      state.systemConfig = systemConfig;
    },
    setComprehensiveConfig(state, comprehensiveConfig) {
      state.comprehensiveConfig = comprehensiveConfig;
    },

    setAuthorInfo(state, authorInfo) {
      state.authorInfo = authorInfo;
    },

    setCityAll(state, cityAll) {
      state.cityAll = cityAll;
    },
    setMapConfig(state, mapConfig) {
      state.mapConfig = mapConfig;
    },
    setMapCancel(state, mapCancel) {
      state.mapCancel = mapCancel;
    },
    setMapStyle(state, mapStyle) {
      state.mapStyle = mapStyle;
    },
    setMapStyleCancel(state, mapStyleCancel) {
      state.mapStyleCancel = mapStyleCancel;
    },
    setModulePermission(state, Permission) {
      state.modulePermission = Permission;
    },
    setIsCollapsed(state, isCollapsed) {
      state.isCollapsed = isCollapsed;
    },
    setTipsShow(state, tipsShow) {
      state.tipsShow = tipsShow;
    },
    deviceMapCameraList(state, deviceMapCameraList) {
      state.deviceMapCameraList = deviceMapCameraList;
    },
    setThemeType(state, themeType) {
      localStorage.setItem('theme', themeType);
      state.themeType = themeType;
    },
    setIsShowXiaoZhi(state, data) {
      state.isShowXiaoZhi = data;
    },
    setIsPortalSkip(state, data) {
      state.isPortalSkip = data;
    },
    setPortalConfig(state, data) {
      state.portalConfig = data;
    },
    setIframeConfig(state, data) {
      state.iframeConfig = data;
    },
  },
  actions: {
    setSource({ commit }, source) {
      commit('setSource', source);
    },
    setInnerHeight({ commit }, innerHeight) {
      commit('setInnerHeight', innerHeight);
    },
    async setAreaList({ commit, state }) {
      if (state.areaList.length === 0) {
        if (!state.areaCancel) {
          try {
            let res = await axios.get(user.queryUserRegionData, {
              cancelToken: new cancelToken((c) => {
                commit('setAreaCancel', c);
              }),
            });
            // 如果没有权限则disabled为true
            let arr = res.data.data.map((row) => {
              if (row.selectedStatus !== 1) {
                row.disabled = true;
              } else {
                row.disabled = false;
              }
              return row;
            });
            // 存储原始一维数组
            commit('setInitialAreaList', arr);
            // 把一维数组处理为树状结构数组
            let areaList = arrayToJson(JSON.parse(JSON.stringify(arr)), 'regionCode', 'parentCode');
            commit('setAreaList', areaList);
            commit('setAreaCancel', null);
            if (areaList.length > 0) {
              commit('setCameraModuleDefaultKeys', [areaList[0].regionCode]);
              // 寻找有权限的最高级设为默认行政区划
              let node,
                list = [...areaList];
              while ((node = list.shift())) {
                if (!node.disabled) {
                  commit('setDefaultSelectedArea', node);
                  return;
                }
                node.children && list.unshift(...node.children);
              }
            }
          } catch (err) {
            console.error(err);
            commit('setAreaCancel', null);
          }
        } else {
          state.areaCancel('重复请求区域列表');
        }
      }
    },
    removeAreaList({ commit }) {
      commit('removeAreaList');
    },
    async setOrganizationList({ commit, state }, datascopeVoList) {
      if (state.organizationList.length === 0) {
        if (!state.organizationCancel) {
          try {
            // let res = await axios.post(common.getOrgPermissionTree, {cancelToken: new cancelToken(c => {
            //     commit('setOrganizationCancel', c);
            // })});
            datascopeVoList = datascopeVoList.sort((a, b) => a.id - b.id);
            // 如果没有权限则disabled为true
            let arr = datascopeVoList.map((row) => {
              row.disabled = row.selectedStatus !== 1;
              // row.disabled = false;
              if (!state.defaultSelectedOrg && row.selectedStatus === 1) {
                commit('setDefaultSelectedOrg', row);
                commit('setOrgDefaultKeys', [row.orgCode]);
              }
              return row;
            });
            // 存储原始一维数组
            commit('setInitialOrgList', deepCopy(arr));
            // 把一维数组处理为树状结构数组
            let orgList = arrayToJson(deepCopy(arr), 'id', 'parentId');
            commit('setOrganizationList', orgList);
            commit('setOrganizationCancel', null);
            // if(orgList.length > 0){
            //     commit('setDefaultSelectedOrg', orgList[0]);
            //     commit('setOrgDefaultKeys', [orgList[0].orgCode]);
            // }
          } catch (err) {
            commit('setOrganizationCancel', null);
            console.log(err);
          }
        } else {
          state.organizationCancel('重复请求组织机构列表');
        }
      }
    },
    removeOrganizationList({ commit }) {
      commit('removeOrganizationList');
    },
    async setCameraList({ commit, state }, sbgnlxs = []) {
      if (state.cameraList.length === 0) {
        if (!state.cameraCancel) {
          try {
            let res = await axios.post(common.getDeviceList, {
              cancelToken: new cancelToken((c) => {
                commit('setCameraCancel', c);
              }),
              sbgnlxs: sbgnlxs,
            });
            commit('setCameraList', res.data.data);
            commit('setCameraCancel', null);
          } catch (err) {
            commit('setCameraCancel', null);
            console.log(err);
          }
        } else {
          state.cameraCancel('重复请求摄像头列表');
        }
      }
    },
    async getDeviceMapCameraList({ commit, state }) {
      if (state.deviceMapCameraList.length === 0) {
        try {
          let res = await axios.post(common.getMapSelectAllDevice, {});
          commit('deviceMapCameraList', res.data);
        } catch (err) {
          commit('deviceMapCameraList', null);
          console.log(err);
        }
      }
    },
    removeCameraList({ commit }) {
      commit('removeCameraList');
    },
    async setSystemConfig({ commit, dispatch }) {
      try {
        let config = await axios.get(common.getSystemConfig);
        dispatch('setPortalConfig');
        dispatch('setIframeConfig');
        const res = await axios.get(user.getApplicationInfo);
        let systemConfig = res.data.data[0];
        const { data } = await axios.get(user.getParamDataByKeys, {
          params: {
            key: 'ISSHOW_COMPANY_INFORMATION',
          },
        });
        if (!!data.data && !!data.data.paramValue) {
          let showLogo = data.data.paramValue === '1' ? true : false;
          systemConfig.showLogo = showLogo;
        }
        document.title = systemConfig.applicationName;
        const STATISTICS_STYLE = await axios.get(user.getParamDataByKeysS);
        if (!!STATISTICS_STYLE.data.data && !!STATISTICS_STYLE.data.data.paramValue) {
          // 基线：1，省厅：2
          systemConfig.distinguishVersion = STATISTICS_STYLE.data.data.paramValue || '1';
          // systemConfig.distinguishVersion = '2'
        } else {
          systemConfig.distinguishVersion = '1';
        }
        commit('systemConfig', systemConfig);
      } catch (err) {
        console.error(err);
      }
    },

    // 设置授权信息
    setAuthor({ commit }) {
      return new Promise((resolve, reject) => {
        axios.get(common.getAuthorUrl).then((resUrl) => {
          if (resUrl.data.data) {
            window.sessionStorage.setItem('authorUrl', resUrl.data.data.paramValue);
          } else {
            window.sessionStorage.setItem('authorUrl', window.location.host + '/404');
          }
          axios
            .get(common.getLicenseInfo)
            .then((res) => {
              window.sessionStorage.setItem('authorInfo', JSON.stringify(res.data.data));
              commit('setAuthorInfo', res.data.data);
              resolve(true);
            })
            .catch(() => {
              reject(window.sessionStorage.getItem('authorUrl'));
            });
        });
      });
    },

    async setCityAll({ commit, state }) {
      if (Object.keys(state.cityAll).length === 0) {
        try {
          let res = await axios.get(common.getCityAll);
          commit('setCityAll', res.data.cityAll);
        } catch (err) {
          console.error(err);
        }
      }
    },
    async setMapConfig({ commit, state }) {
      if (!state.mapConfig) {
        if (!state.mapCancel) {
          try {
            let res = await axios.get(map.getMapConfig, {
              cancelToken: new cancelToken((c) => {
                commit('setMapCancel', c);
              }),
            });
            commit('setMapConfig', res.data);
            commit('setMapCancel', null);
          } catch (err) {
            commit('setMapCancel', null);
            console.error(err);
          }
        } else {
          state.mapCancel('重复请求地图接口');
        }
      }
    },
    async setMapStyle({ commit, state }) {
      if (!state.mapStyle) {
        if (!state.mapStyleCancel) {
          try {
            const res = await axios.get(map.getMapStyle, {
              cancelToken: new cancelToken((c) => {
                commit('setMapStyleCancel', c);
              }),
            });
            const localStorageTheme = localStorage.getItem('theme');
            // 如果皮肤颜色为空或者皮肤颜色是深色则使用style否则就不使用
            if (localStorageTheme === 'dark' || !localStorageTheme) {
              commit('setMapStyle', res.data);
            } else {
              commit('setMapStyle', null);
            }
            commit('setMapStyleCancel', null);
          } catch (err) {
            commit('setMapStyleCancel', null);
            console.error(err);
          }
        } else {
          state.mapStyleCancel('重复请求地图接口');
        }
      }
    },
    //模块权限
    setModulePermission({ commit }, Permission) {
      commit('setModulePermission', Permission);
    },

    setIsCollapsed({ commit }, isCollapsed) {
      commit('setIsCollapsed', isCollapsed);
    },
    setTipsShow({ commit }, tipsShow) {
      commit('setTipsShow', tipsShow);
    },
    setThemeType({ commit }, themeType) {
      commit('setThemeType', themeType);
    },
    // 小智机器人
    async setShowXizoZhi({ commit }) {
      try {
        const { data } = await axios.get(user.xiaozhiByParamKey);
        if (!!data.data && !!data.data.paramValue) {
          let showXizoZhi = data.data.paramValue === '1' ? true : false;
          commit('setIsShowXiaoZhi', showXizoZhi);
        }
      } catch (error) {
        console.error(error);
      }
    },
    async setPortalConfig({ commit }) {
      try {
        const { data } = await axios.get(user.getPortalConfigByParamKey);
        if (!!data.data && !!data.data.paramValue) {
          commit('setPortalConfig', data.data.paramValue || '/');
        }
      } catch (error) {
        console.error(error);
      }
    },
    async setIframeConfig({ commit }) {
      try {
        const { data } = await axios.get(user.getIframeConfigByParamKey);
        if (!!data.data && !!data.data.paramValue) {
          commit('setIframeConfig', data.data.paramValue || '/');
        }
      } catch (error) {
        console.error(error);
      }
    },
    setIsPortalSkipPage({ commit }, data) {
      commit('setIsPortalSkip', data);
    },
  },
};
