<template>
  <div id="personnelprocess">
    <div class="personnelprocess-left">
      <div class="personnelprocess-left-optbtn">
        <Button type="primary" @click="run">开始运行</Button>
        <!-- <Button type="primary">保存配置</Button> -->
      </div>
      <div class="base-text">人员基础数据</div>
      <div class="trajectory-text">人员轨迹数据</div>
      <div v-for="(item, index) in procedureDatas" :key="index">
        <aggre-connect :propData="item" :dWidth="dWidth" :bWidth="bWidth"></aggre-connect>
      </div>
    </div>
    <div class="personnelprocess-right">
      <component-package></component-package>
    </div>
    <fieldmap ref="Fieldmap" topicType="5" :topicId="topicId" @render="getView"></fieldmap>
    <dictionarymapping ref="Dictionarymapping" topicType="5" :topicId="topicId" @render="getView"></dictionarymapping>
    <formawarehouse ref="Formawarehouse" :warehousData="warehousData" @render="getView"></formawarehouse>
    <empty ref="Empty" topicType="5" :topicId="topicId" @render="getView"></empty>
    <repeat ref="Repeat" topicType="5" :topicId="topicId" @render="getView"></repeat>
    <imgtest ref="Imgtest" :propData="imgData" :topicId="topicId" @render="getView"></imgtest>
    <face-structure ref="FaceStructure" title="人脸结构化" type="UniqueDetection" @render="getView"></face-structure>
    <face-structure
      ref="Trajectory"
      title="人员轨迹准确性检测优化"
      type="UniqueDetection"
      quality="抓拍照片与人员静态照片相似度阈值"
      tips="说明：如果选择的算法有多数算法判定轨迹照片和静态照片达到相似度阈值，则人员轨迹准确。"
      @render="getView"
      >%</face-structure
    >
    <data-input ref="DataInput"></data-input>
    <config-run ref="ConfigRun"></config-run>
    <loading v-if="loading"></loading>
  </div>
</template>
<script>
import { personData } from './personOptions';
import governancetheme from '@/config/api/governancetheme';
export default {
  name: 'personnelprocess',
  props: {},
  data() {
    return {
      aggregateOptions: [],
      imgData: {
        items: [
          {
            key1: 'important',
            key2: 'importantTime',
            value1: '',
            value2: '1',
            label: '重点人员轨迹数据时延：',
          },
        ],
      },
      loading: false,
      dWidth: '13.82%',
      bWidth: '15.23%',
      warehousData: {},
      procedure: [],
      procedureDatas: [],
      queryId: '',
      topicType: '',
      topicId: 0,
    };
  },
  mounted() {
    this.loading = true;
    this.getView();
    // this.aggregateOptions = personData;
  },
  methods: {
    // 获取流程图数据
    async getView() {
      try {
        const id = this.$route.query.tab;
        let res = await this.$http.get(governancetheme.view, {
          params: { id: id }, // 主题ID
        });
        this.topicType = res.data.data.topicType;
        this.topicId = res.data.data.id;
        this.procedure = res.data.data.componentList;
        this.procedureDatas = this.handleData(this.procedure);
        this.warehousData = JSON.parse(JSON.stringify(res.data.data));
        delete this.warehousData.componentList;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 将后端流程图数据与前端流程图数据整合
    handleData(procedure) {
      let group = [];
      procedure.forEach((item) => {
        group.push(item.sort);
      });
      group = Array.from(new Set(group)); // 去重
      let newDatas = group.map((item) => {
        let arr = [];
        procedure.forEach((dItem) => {
          if (dItem.sort === item) {
            arr.push(dItem);
          }
        });
        return arr;
      });
      return personData.map((item, index) => {
        item.datas = item.datas.map((childItem, childIndex) => {
          childItem = { ...newDatas[index][childIndex], ...childItem };
          childItem['isConfigure'] = newDatas[index][childIndex]['isConfigure'];
          return childItem;
        });
        return item;
      });
    },
    run() {
      this.$refs.ConfigRun.init();
    },
  },
  watch: {
    $route() {
      if (!this.$route.query.tab) {
        return false;
      }
      this.getView();
    },
  },
  components: {
    ComponentPackage: require('@/views/datagovernance/governancetheme/components/componentPackage.vue').default,
    AggreConnect: require('../components/aggre-connect.vue').default,
    Fieldmap: require('../viewprocess/components/fieldmap.vue').default,
    Dictionarymapping: require('../viewprocess/components/dictionarymapping.vue').default,
    Formawarehouse: require('../viewprocess/components/formawarehouse.vue').default,
    Empty: require('../viewprocess/components/empty.vue').default,
    Repeat: require('../viewprocess/components/repeat.vue').default,
    Imgtest: require('../faceprocess/components/imgtest.vue').default,
    FaceStructure: require('../faceprocess/components/face-structure.vue').default,
    DataInput: require('./components/dataInput.vue').default,
    ConfigRun: require('./components/config-run.vue').default,
  },
};
</script>
<style lang="less" scoped>
#personnelprocess {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  background-image: url('../../../../assets/img/thememanagement/process-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
  .personnelprocess-left {
    position: relative;
    flex: 1;
    height: 100%;
    // overflow: auto;
    // border-right: 1px solid var(--border-color);
    &-optbtn {
      position: absolute;
      top: 20px;
      right: 28px;
      // button:first-child {
      //   margin-right: 20px;
      // }
    }
  }
  .personnelprocess-right {
    width: 243px;
    height: 100%;
    background-color: var(--bg-content);
  }
  .base-text {
    position: absolute;
    top: 273px;
    left: 16.5%;
    font-size: 12px;
    color: #ffffff;
  }
  .trajectory-text {
    position: absolute;
    top: 440px;
    left: 135px;
    font-size: 12px;
    color: #ffffff;
  }
  @media screen and (max-width: 1366px) {
    .base-text,
    .trajectory-text {
      transform: scale(0.8) translateX(-15px);
    }
  }
}
</style>
