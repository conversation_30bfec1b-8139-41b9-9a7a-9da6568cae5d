<template>
  <div class="exam-score-gab-examination-score height-full">
    <examinationscore></examinationscore>
  </div>
</template>
<script>
export default {
  name: 'examinationScoreGAB',
  props: {},
  data() {
    return {};
  },
  provide() {
    return {
      examSchemeType: 2, //上级考核成绩
    };
  },
  methods: {},
  mounted() {},
  components: {
    examinationscore: require('@/views/governanceevaluation/examinationresult/examinationscore/index.vue').default,
  },
};
</script>
