<template>
  <base-review
    ref="baseReviewRef"
    v-bind="getAttrs"
    v-on="$listeners"
    :styles="{ width: '7rem' }"
    @changeDevice="changeDevice"
  >
    <template #detailcontent v-if="$attrs.value">
      <EasyPlayer class="easy-player" :videoUrl="videoUrl" ref="easyPlay"> </EasyPlayer>
    </template>
  </base-review>
</template>

<script>
import vedio from '@/config/api/vedio-threm';
export default {
  name: 'video-accuracy',
  props: {
    // 获取视频的接口url
    getVideoUrl: {
      default: vedio.getplay,
    },
    // 获取视频的接口的参数函数回调处理
    getVideoParamsFunc: {
      type: Function,
      default: (row) => {
        return { deviceId: row.deviceId };
      },
    },
  },
  data() {
    return {
      videoUrl: '',
      playDeviceCode: '',
    };
  },
  methods: {
    changeDevice(detailInfo) {
      this.playVideo(detailInfo);
    },
    handleStart(visible) {
      if (!visible) {
        // 先清除上一个视频流
        this.playDeviceCode ? this.$http.post(vedio.stop + this.playDeviceCode) : null;
        this.playDeviceCode = '';
      }
    },
    //视频播放
    async playVideo(row) {
      try {
        this.videoUrl = '';
        this.playDeviceCode = row.deviceId;
        let params = this.getVideoParamsFunc(row);
        // let data = {
        //   deviceId: row.deviceId,
        //   // startTime: row.playStartTime,
        //   // endTime: row.playEndTime,
        //   // pullLevel: 3,
        //   // urlEncryption: true
        // }
        // 重点| 普通 历史视频可调阅率
        let indexTypeArr = ['VIDEO_HISTORY_ACCURACY', 'VIDEO_GENERAL_HISTORY_ACCURACY'];
        if (indexTypeArr.includes(this.$route.query.indexType)) {
          await this.$http.post(vedio.record, {
            deviceId: row.deviceId,
            startTime: row.playStartTime,
            endTime: row.playEndTime,
          });
        }
        let res = await this.$http.post(this.getVideoUrl, params);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      } finally {
        this.videoLoading = false;
      }
    },
  },
  watch: {
    '$attrs.value': {
      handler(val) {
        this.handleStart(val);
      },
      immediate: true,
    },
  },
  computed: {
    getAttrs() {
      return {
        ...this.$attrs,
      };
    },
  },
  components: {
    BaseReview: require('./base-review').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>

<style lang="less" scoped>
.player-list {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  height: 560px;
  .easy-player {
    width: 49%;
    height: 280px;
    margin-right: 10px;
    margin-top: 10px;
  }
}
</style>
