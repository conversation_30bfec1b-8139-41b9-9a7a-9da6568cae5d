/**
 * 系统内置方法集，正常情况下您不应该修改或移除此文件
 * */

import { cloneDeep } from "lodash";

/**
 * @description 根据当前路由，找打顶部菜单名称
 * @param {String} currentPath 当前路径
 * @param {Array} menuList 所有路径
 * */
function getHeaderName(currentPath, menuList) {
  const allMenus = [];
  menuList.forEach((menu) => {
    const headerName = menu.meta ? menu.meta.title : "";
    const menus = transferMenu(menu, headerName);
    allMenus.push({
      path: menu.path,
      header: headerName,
      parentName: menu.parentName || "",
    });

    menus.forEach((item) => allMenus.push(item));
  });
  const currentMenu = allMenus.find((item) => item.path === currentPath);
  return {
    path: currentMenu.path,
    headerName: currentMenu.header,
    parentName: currentMenu.parentName || "",
  };
  // currentMenu && currentMenu.header ? currentMenu.header : currentMenu.meta && currentMenu.meta.title ? currentMenu.meta.title : null
}

function transferMenu(menu, headerName) {
  const parentPath = menu.parentPath || menu.path;
  if (menu.children && menu.children.length) {
    return menu.children.reduce((all, item) => {
      const path = item.path;
      item.parentPath = parentPath;
      all.push({
        path: path,
        header: headerName,
        parentName: item.parentName || "",
      });
      const foundChildren = transferMenu(item, headerName);
      return all.concat(foundChildren);
    }, []);
  } else {
    return [menu];
  }
}

export { getHeaderName };

/**
 * @description 根据当前顶栏菜单 name，找到对应的二级菜单
 * @param {Array} menuList 所有的二级菜单
 * @param {String} header 当前菜单信息 （区分静态与动态：parentName）
 * */
function getMenuSider(menuList, header = {}, menushow = false) {
  if (header.parentName) {
    let headerName =
      header.parentName.indexOf("/") !== -1
        ? header.parentName.split("/")[0]
        : header.parentName + "view";
    return menuList.filter((item) => {
      if (item.meta && item.name === headerName) {
        return item.name === headerName;
      } else {
        return false;
      }
    });
  } else if (header.headerName) {
    if (menushow) {
      return menuList.filter((item) => {
        if (item.meta && item.meta.show) {
          //针对设备档案不需要目录
          return true;
        }
      });
    } else {
      return menuList.filter((item) => {
        if (item.meta) {
          return item.meta.title === header.headerName;
        } else {
          return false;
        }
      });
    }
  } else {
    if (
      header.path == "/user/information" ||
      header.path == "/user/mydownload" ||
      header.path == "/user/myApply" ||
      header.path == "/user/myTargetControl" ||
      header.path == "/user/mySubStructuration"
    )
      return [];
    return menuList;
  }
}

export { getMenuSider };

/**
 * @description 根据当前路由，找到其所有父菜单 path，作为展开侧边栏 open-names 依据
 * @param {String} currentPath 当前路径
 * @param {Array} menuList 所有路径
 * */
function getSiderSubmenu(currentPath, menuList) {
  const allMenus = [];
  menuList.forEach((menu) => {
    const menus = transferSubMenu(menu, []);
    allMenus.push({
      path: menu.path,
      name: menu.name,
      openNames: [],
    });
    menus.forEach((item) => allMenus.push(item));
  });

  const currentMenu = allMenus.find((item) => item.path === currentPath);
  return currentMenu ? currentMenu.openNames : [];
}

function transferSubMenu(menu, openNames) {
  const parentPath = menu.path;
  if (menu.children && menu.children.length) {
    const itemOpenNames = openNames.concat([menu.path]);
    return menu.children.reduce((all, item) => {
      all.push({
        path: item.path,
        name: item.name,
        openNames: itemOpenNames,
      });
      const foundChildren = transferSubMenu(item, itemOpenNames);
      return all.concat(foundChildren);
    }, []);
  } else {
    return [menu].map((item) => {
      return {
        path: item.path,
        name: item.name,
        openNames: openNames,
      };
    });
  }
}

export { getSiderSubmenu };

/**
 * @description 根据当前路由，找到其所有父菜单 name，作为展开侧边栏 open-names 依据
 * @param {String} currentName 当前name
 * @param {Array} menuList 所有路径
 * */
function getSiderSubmenuName(currentName, menuList) {
  let currentMenu = {};
  function getChildrenSlder(list) {
    list.map((val) => {
      if (val.name === currentName) {
        currentMenu = val;
      }
      if (val.children && val.children.length > 0) {
        getChildrenSlder(val.children);
      }
    });
  }
  getChildrenSlder(menuList);
  let openNames = currentMenu.name
    ? [currentMenu.name, currentMenu.parentNameA]
    : [];
  return openNames;
}

export { getSiderSubmenuName };

/**
 * @description 递归获取所有子菜单
 * */
function getAllSiderMenu(menuList) {
  const allMenus = [];

  menuList.forEach((menu) => {
    if (menu.children && menu.children.length) {
      const menus = getMenuChildren(menu);
      menus.forEach((item) => allMenus.push(item));
    } else {
      allMenus.push(menu);
    }
  });

  return allMenus;
}

function getMenuChildren(menu) {
  if (menu.children && menu.children.length) {
    return menu.children.reduce((all, item) => {
      const foundChildren = getMenuChildren(item);
      return all.concat(foundChildren);
    }, []);
  } else {
    return [menu];
  }
}

export { getAllSiderMenu };

/**
 * @description 将菜单转为平级
 * */
function flattenSiderMenu(menuList, newList) {
  menuList.forEach((menu) => {
    const newMenu = {};
    for (const i in menu) {
      if (i !== "children") newMenu[i] = cloneDeep(menu[i]);
    }
    newList.push(newMenu);
    menu.children && flattenSiderMenu(menu.children, newList);
  });
  return newList;
}

export { flattenSiderMenu };

/**
 * @description 判断列表1中是否包含了列表2中的某一项
 * 因为用户权限 access 为数组，includes 方法无法直接得出结论
 * */
function includeArray(list1, list2) {
  let status = false;
  list2.forEach((item) => {
    if (list1.includes(item)) status = true;
  });
  return status;
}

// Find components downward
export const findComponentsDownward = (context, componentName) => {
  return context.$children.reduce((components, child) => {
    if (child.$options.name === componentName) components.push(child);
    const foundChilds = findComponentsDownward(child, componentName);
    return components.concat(foundChilds);
  }, []);
};

export { includeArray };

// 车牌颜色 对应字典项
import licensePlateColorArray from "../dictionaries/license-plate-color";
export { licensePlateColorArray };
// 车身颜色 对应字典项
import vehicleBodyColorArray from "../dictionaries/vehicle-body-color";
export { vehicleBodyColorArray };
// 人体，非机动车对应字典项
import staticBodyColorList from "../dictionaries/body-color-list";
export { staticBodyColorList };
// 人体，非机动车对应字典项
import bodyColorList from "../dictionaries/body-color-list";
export { bodyColorList };
// 车辆品牌 对应字典项
import vehicleBrandArray from "../dictionaries/vehicle-brand";
export { vehicleBrandArray };

// 数仓概况中间gift图
import dataWarehouseJson from "../data-warehouse-view";
export { dataWarehouseJson };
