import request from '@/libs/request'
import { monitorService } from './Microservice'
//主机接口

//查询主机在线状况
export function statusStatistics(data) {
    return request({
        url: monitorService + '/server/statusStatistics',
        method: 'GET',
        data: data
    })
}
//查询服务器列表
export function pageList(data) {
    return request({
        url: monitorService + '/server/pageList',
        method: 'POST',
        data: data
    })
}
//新增主机
export function add(data) {
    return request({
        url: monitorService + '/server/add',
        method: 'POST',
        data: data
    })
}
//修改主机
export function update(data) {
    return request({
        url: monitorService + '/server/update',
        method: 'POST',
        data: data
    })
}
// 删除主机
export function deleteMf(id) {
    return request({
        url: monitorService + '/server/delete/' + id,
        method: 'DELETE',
    })
}
//测试主机连接
export function testLink(data) {
    return request({
        url: monitorService + '/server/testLink',
        method: 'POST',
        data: data
    })
}


// 服务下拉列表
export function selectList(data) {
    return request({
        url: monitorService + '/server/selectList',
        method: 'POST',
        data: data
    })
}

// 应用服务列表
export function queryPageByConditions(data) {
    return request({
        url: monitorService + '/application/queryPageByConditions',
        method: 'POST',
        data: data
    })
}