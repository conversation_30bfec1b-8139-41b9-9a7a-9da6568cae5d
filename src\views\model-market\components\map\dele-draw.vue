<!--
    * @FileDescription: 地图-删除
    * @Author: H
    * @Date: 2023/03/21
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="dele-draw-dom">
        <section class="dom-content">
            <div class="deleDraw" @click="handleDele">
                <!-- <ui-icon type="shanchu1" :size="14"></ui-icon> -->
                <i class="el-icon-delete"></i>
                <p>删除</p>
            </div>
        </section>
    </div>
</template>
<script>
export default {
    components: {  },
    props: {
    },
    watch: {
    },
    computed: {
    },

    data () {
        return {
        }
    },
    methods: {
        handleDele() {
            this.$emit('dele')
        }
    }
}
</script>
<style lang="less" scoped>
.dele-draw-dom {
    width: 74px;
    height: 32px;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
    border-radius: 4px;
    .dom-content {
        // padding-bottom: 5px;
        .deleDraw{
            // position: absolute;
            background: #fff;
            box-shadow: 0px 2px 6px 0px rgba(0,21,41,0.15);
            border-radius: 4px 4px 4px 4px;
            width: 74px;
            height: 32px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
            z-index: 99;
            .el-icon-delete{
                color: #EA4A36 !important;
            }
            /deep/.icon-shanchu{
                color: #EA4A36 !important;
            }
            p{
                margin-left: 10px;
            }
        }
    }
}
</style>
