<template>
  <div class="function-distribute">
    <tab-title v-model="searchData.dataKey" :data="tabData" @on-change="onChangeTitle">
      <template #filter>
        <drop-select
          v-model="searchData.sbdwlxList"
          multiple
          :data="dropData"
          @on-change="handleChangeSelect"
        ></drop-select>
      </template>
    </tab-title>
    <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <draw-echarts
        class="charts"
        :echart-option="propertyEchart"
        :echart-style="echartStyle"
        ref="qualityChartRef"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <next-chart-icon
        v-if="echartList.length > comprehensiveConfig.homeNum"
        @scrollRight="scrollRight('qualityChartRef', echartList, [], comprehensiveConfig.homeNum)"
      ></next-chart-icon>
    </div>
  </div>
</template>

<script>
import { oneType } from '../utils/enum';
import { mapGetters } from 'vuex';
import assetstatisticsMultipleBar from '@/views/viewassets/assetstatistics/utils/echarts-config-bar.js';
import equipmentassets from '@/config/api/equipmentassets';
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'pie-chart',
  mixins: [dataZoom],
  components: {
    NextChartIcon: require('../components/next-chart-icon.vue').default,
    DropSelect: require('../components/drop-select.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    TabTitle: require('../components/tab-title.vue').default,
  },
  props: {
    homeConfig: {
      default: () => ({}),
    },
    activeTab: {
      default: '1',
    },
  },
  data() {
    let dropData = oneType.map((item) => {
      return {
        label: item.name,
        id: item.id,
      };
    });
    return {
      searchData: {
        dataKey: 'videostatus_overview_list',
        sbdwlxList: [],
      },
      dropData: dropData,
      tabData: [
        { label: '视频在线状态区域分布', id: 'videostatus_overview_list' },
        { label: '人卡在线状态区域分布', id: 'facestatus_overview_list' },
        { label: '车卡在线状态区域分布', id: 'vehiclestatus_overview_list' },
      ],
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      echartsLoading: false,
      echartList: [],
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    activeTab: {
      async handler() {
        this.postQueryStatisticsByAmount();
      },
    },
    homeConfig: {
      async handler(val) {
        if (Object.keys(val).length > 0) {
          this.postQueryStatisticsByAmount();
        }
      },
      deep: true,
    },
  },
  methods: {
    handleChangeSelect() {
      this.postQueryStatisticsByAmount();
    },
    onChangeTitle() {
      this.postQueryStatisticsByAmount();
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('qualityChartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    /**
     * VIDEOSTATUS_OVERVIEW_LIST("videostatus_overview_list","视频监控在线统计区域分布")
     * FACESTATUS_OVERVIEW_LIST("facestatus_overview_list","人脸卡口在线统计区域分布")
     * VEHICLESTATUS_OVERVIEW_LIST("vehiclestatus_overview_list","车辆卡口在线统计区域分布")
     */
    async postQueryStatisticsByAmount() {
      try {
        this.echartsLoading = true;
        let { regionCode } = this.homeConfig;
        let params = {
          civilCode: regionCode,
          type: this.activeTab,
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.postQueryStatisticsByCommonCondition, params);
        this.echartList = data || [];
        this.handleQualityCharts();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleQualityCharts() {
      let data = {
        color: [$var('--color-blue-11'), $var('--color-blue-12'), $var('--color-blue-13')],
        titleText: '设备数量',
        legendData: ['在线', '离线'],
        xAxisData: this.echartList.map((item) => item.civilCodeName),
        seriesData: [
          {
            name: '离线',
            stack: '使用情况',
            barWidth: 18,
            data: this.echartList.map((item) => item.unAmount),
            itemStyle: {
              normal: { color: $var('--color-pink-3') },
            },
          },
          {
            name: '在线',
            stack: '使用情况',
            barWidth: 18,
            itemStyle: {
              normal: { color: $var('--color-green-12') },
            },
            data: this.echartList.map((item) => item.amount),
          },
        ],
      };
      this.propertyEchart = assetstatisticsMultipleBar(data);
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.function-distribute {
  width: 100%;
  height: 320px;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .body-container {
    width: 100%;
    position: relative;
    height: calc(100% - 30px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
