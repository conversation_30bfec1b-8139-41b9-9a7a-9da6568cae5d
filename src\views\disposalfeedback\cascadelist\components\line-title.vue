<template>
  <div class="line-title f-16">
    <i class="icon-font mr-sm f-18" :class="iconName"></i>
    <span>{{ title }}</span>
  </div>
</template>
<script>
export default {
  props: {
    title: {},
    iconName: {
      default: 'icon-tianjia',
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.line-title {
  border-bottom: 1px solid var(--devider-line);
  padding-bottom: 20px;
  color: var(--color-display-title);
  i {
    color: var(--color-display-title-before);
  }
}
</style>
