<template>
  <div class="tabs">
    <span
      v-for="(item, index) in tabList"
      :key="index"
      @click="tabClick((cur = index))"
      :class="cur == index ? 'cur' : ''"
    >
      {{ item.title }}</span
    >
  </div>
</template>
<script>
export default {
  data() {
    return {
      cur: 0,
      tabList: [{ title: '图像模式' }, { title: '聚档模式' }],
    };
  },
  methods: {
    tabClick(index) {
      this.$emit('changeTab', index);
    },
  },
};
</script>
<style lang="less" scoped>
.tabs {
  margin: 20px 0;
  span {
    display: inline-block;
    width: 100px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    color: #4e6c92;
    border: 1px solid #1d64a6;
    cursor: pointer;
  }
  span:nth-child(1) {
    border-right: 0;
  }
  .cur {
    background-color: #2d83e3;
    color: #fff;
  }
}
</style>
