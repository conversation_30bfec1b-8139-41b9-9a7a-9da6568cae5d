<template>
  <div class="base-search">
    <ui-label class="inline" :label="global.filedEnum.deviceId" :width="70">
      <Input v-model="searchData.deviceId" class="width-lg" :placeholder="`请输入${global.filedEnum.deviceId}`"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName" :width="70">
      <Input
        v-model="searchData.deviceName"
        class="width-lg"
        :placeholder="`请输入${global.filedEnum.deviceName}`"
      ></Input>
    </ui-label>
    <ui-label class="inline ml-lg" label="设备状态" :width="70">
      <Select class="width-sm" v-model="searchData.deviceStatus" clearable placeholder="请选择检测结果">
        <Option :value="1" label="合格"></Option>
        <Option :value="2" label="不合格"></Option>
      </Select>
    </ui-label>
    <ui-label class="inline ml-lg" label="异常原因" :width="70">
      <Select class="width-lg" v-model="searchData.messages" placeholder="请选择异常原因" multiple :max-tag-count="1">
        <Option value="ERROR_TODAY_NO_DATA" label="昨日无抓拍"></Option>
        <Option value="ERROR_NO_DATA" label="无抓拍数据"></Option>
        <Option value="ERROR_TOO_LESS_DATA" label="抓拍数据过少"></Option>
        <Option value="ERROR_DATA_SWOOP" label="抓拍数量突降"></Option>
      </Select>
    </ui-label>
    <ui-label :width="30" class="inline search-button" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx()">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        deviceStatus: '', //checkStatus
        messages: '',
      },
    };
  },
  created() {
    this.reashfalf();
  },
  methods: {
    startSearch() {
      this.$emit('selectInfo', this.searchData);
    },
    resetSearchDataMx() {
      this.reashfalf();
      this.$emit('selectInfo', this.searchData);
    },
    reashfalf() {
      this.searchData = {
        deviceId: '',
        deviceName: '',
        deviceStatus: '',
        messages: '',
      };
    },
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  padding-top: 10px;
  .input-width {
    width: 200px;
  }
}
/deep/ .w150 {
  width: 150px;
}
</style>
