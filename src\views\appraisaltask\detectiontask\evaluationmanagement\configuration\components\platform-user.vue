<template>
  <div class="platform-user">
    <Form ref="modalData" class="form-content" :label-width="160" :model="formData" :rules="ruleCustom">
      <!-- 平台可用性 -->
      <common-form
        ref="commonForm"
        :form-data="formData"
        :form-model="formModel"
        :module-action="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
        @handleDetectMode="handleDetectMode"
      >
        <div slot="extract">
          <template
            v-if="
              ['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE', 'VIDEO_PLATFORM_ONLINE_RATE'].includes(
                moduleAction.indexType,
              )
            "
          >
            <FormItem label="检测平台" prop="orgList">
              <Button @click="chooseCode">
                <span v-if="formData.orgCodeList.length === 0 && moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE'"
                  >请选择待检测的人脸视图库</span
                >
                <span
                  v-if="formData.orgCodeList.length === 0 && moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'"
                  >请选择待检测的车辆视图库</span
                >
                <span v-if="formData.orgCodeList.length === 0 && moduleAction.indexType == 'VIDEO_PLATFORM_ONLINE_RATE'"
                  >请选择待检测的互联平台</span
                >
                <span v-else>{{ `已选择 ${formData.orgCodeList.length}个` }}</span>
              </Button>
            </FormItem>
          </template>
        </div>
      </common-form>
      <div class="platform" v-if="interfaceInfoConfig()">
        <p class="text">接口信息配置</p>
        <div class="left-city">
          <ul class="ul">
            <li
              :class="{ active: current == index }"
              v-for="(item, index) in formData.apiListModel"
              :key="index"
              @click="cityClick(item, index)"
            >
              <div class="til">
                <Tooltip :content="item.name" :disabled="item.name.length < 8">
                  {{ item.name }}
                </Tooltip>
              </div>
              <i-switch class="switch" v-model="item.type" />
            </li>
          </ul>
        </div>
        <Form ref="platformvalid" :rules="platformrule" class="form-content edit-form" :model="formData">
          <div class="orgCode" v-if="formData.apiListModel[current]">
            在待检测平台中的组织机构编码：
            <i-input
              v-model="formData.apiListModel[current].orgCode"
              placeholder="Enter something..."
              style="width: 300px"
            />
          </div>
          <div class="topUl">
            <ul>
              <li>
                <div class="item">接口名称</div>
                <div class="item">每轮检测调用次数(次)</div>
                <div class="item">调用超时阈值(秒)</div>
                <div class="item">相似度%</div>
              </li>
            </ul>
          </div>
          <div style="margin-top: 30px" v-if="formData.apiListModel[current]">
            <ul>
              <li v-for="(item, index) in formData.apiListModel[current].apiModel" :key="index">
                <div class="item">{{ item.name }}</div>
                <div class="item">
                  <FormItem label="" prop="apiTimes" :label-width="0">
                    <Input placeholder="次数" v-model.number="item.apiTimes"></Input>
                  </FormItem>
                </div>
                <div class="item">
                  <FormItem label="" prop="apiDura" :label-width="0">
                    <Input placeholder="阈值" v-model.number="item.apiDura"></Input>
                  </FormItem>
                </div>
                <div class="item">
                  <FormItem label="" prop="apiRate" :label-width="0">
                    <Input placeholder="相似度" v-model.number="item.apiRate"></Input>
                  </FormItem>
                </div>
              </li>
              <div class="clear"></div>
            </ul>
          </div>
        </Form>
      </div>
      <FormItem
        label="时间范围"
        class="right-item mb-sm"
        prop="name"
        v-if="['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(moduleAction.indexType)"
      >
        <Select v-model="formData.timeDelay" class="select-width" placeholder="请选择时间范围" transfer disabled>
          <Option value="1">前一天</Option>
        </Select>
      </FormItem>
      <FormItem
        label="时间区间"
        v-if="['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(moduleAction.indexType)"
      >
        <div class="inspection">
          <div class="row-inspection" v-for="(item, index) in formData.dateRang" :key="index">
            <FormItem>
              <div class="form-row">
                <span class="width-picker">
                  <Select
                    :disabled="
                      moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE' ||
                      moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'
                    "
                    class="time-picker"
                    transfer
                    v-model="item.hourStart"
                    clearable
                  >
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourEnd ? item.hourEnd <= it.value : false"
                      :class="item.hourEnd ? (item.hourEnd <= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
                <span class="color-bule mr-sm ml-sm">—</span>
                <span class="width-picker">
                  <Select
                    :disabled="
                      moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE' ||
                      moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'
                    "
                    class="time-picker"
                    transfer
                    v-model="item.hourEnd"
                    clearable
                  >
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourStart ? item.hourStart >= it.value : false"
                      :class="item.hourStart ? (item.hourStart >= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
              </div>
            </FormItem>
          </div>
        </div>
        <p v-if="moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE'" class="label-color w390">
          说明：检测时间范围内，配置的时间区间内如果没有抓拍人脸数据上传，则人脸视图库离线
        </p>
        <p v-if="moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'" class="label-color w390">
          说明：检测时间范围内，配置的时间区间内如果没有抓拍人脸数据上传，则人脸视图库离线
        </p>
      </FormItem>
    </Form>
    <Form ref="formCustom" :rules="formCustomrule" :model="formCustomData" class="form-content" :label-width="160">
      <FormItem label="检测计划" v-if="moduleAction.indexType === 'VIDEO_PLATFORM_ONLINE_RATE'" prop="checkTime">
        <p class="check-plan">
          每
          <Input v-model="formCustomData.checkTime" class="ml-sm mr-sm" style="width: 60px" @on-blur="handleBlurTime" />
          <Select v-model="formCustomData.cronType" transfer style="width: 80px" @on-change="handleTypeChange">
            <Option value="4">分</Option>
            <Option value="5">时</Option>
          </Select>
          <span class="ml-xs">检测一次</span>
        </p>
        <!--        <span class="base-text-color">实时监测</span>-->
      </FormItem>
      <FormItem label="检测逻辑" v-if="moduleAction.indexType === 'VIDEO_PLATFORM_ONLINE_RATE'" prop="deviceNum">
        <p class="base-text-color">检测的某平台的所有设备均离线则认为该平台离线。</p>
        <p class="check-plan mt-md">
          <span>检测数量：</span>
          <Select v-model="formCustomData.checkCount" transfer class="ml-sm mr-sm" style="width: 80px">
            <Option value="2">抽检</Option>
            <!--            <Option value="1">全部</Option>-->
            <!--            <Option value="2">抽检</Option>-->
          </Select>
          <Input
            class="mr-xs"
            v-model="formCustomData.deviceNum"
            maxlength="3"
            style="width: 60px"
            @on-blur="handleBlurNum"
          />
          <span>路</span>
        </p>
      </FormItem>
    </Form>

    <OrgModal
      v-model="orgModalVisible"
      @query="choosQuery"
      :orgList="formData.orgCodeList"
      :formModel="formModel"
      :areaTreeData="areaTreeData"
      v-if="areaTreeData.length != 0"
    ></OrgModal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  components: {
    OrgModal: require('./org-modal/index.vue').default,
    CommonForm: require('./common-form/index').default,
  },
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },

    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateTimePass = (rule, value, callback) => {
      const regx = /^\+?[1-9][0-9]*$/;
      !regx.test(value) && value !== '' ? callback(new Error('请输入大于0整数！')) : '';
      if (this.formCustomData.cronType === '4' && (value < 30 || value > 60)) {
        callback(new Error('选择分钟为单位，仅支持输入30-60分钟；如需更大时间间隔，请选择小时为单位！'));
      }
      if (this.formCustomData.cronType === '5') {
        const regx = /^\+?[1-9][0-9]*$/;
        !regx.test(value) ? callback(new Error('请输入正整数！')) : '';
      }
      callback();
    };
    const validateNumPass = (rule, value, callback) => {
      const regx = /^\+?[1-9][0-9]*$/;
      !regx.test(value) && value !== '' ? callback(new Error('请输入大于0的整数！')) : '';
      if (value !== '' && value > 100) {
        callback(new Error('最大支持抽检100路设备！'));
      }
      callback();
    };
    return {
      ruleCustom: {
        timeDelay: [
          {
            required: true,
            type: 'number',
            message: '请输入时间',
            trigger: 'blur',
          },
        ],
      },
      platformrule: {
        orgList: [{ required: true, type: 'array', message: '请选择检测平台', trigger: 'change' }],
      },
      formData: {
        detectMode: '1',
        apiListModel: [],
        orgCodeList: [],
        dateRang: null,
        cronData: ['30'],
        cronType: '4',
        deviceNum: '20',
      },
      current: 0,
      orgCodes: [],
      areaTreeData: [],
      selectAreaTree: {
        regionCode: '',
      },
      schemeList: [],
      orgModalVisible: false,
      orgList: [],
      formCustomData: {
        checkCount: '2',
        cronType: '4',
        checkTime: '30',
        deviceNum: '20',
      },
      formCustomrule: {
        checkTime: [{ required: true, validator: validateTimePass, trigger: 'blur' }],
        deviceNum: [{ required: true, validator: validateNumPass, trigger: 'blur' }],
      },
    };
  },
  watch: {
    formModel: {
      handler(val) {
        this.schemeList = this.getHour();
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
          this.formData.orgCodeList = this.formData.orgCodes.split(',');
          this.formCustomData.checkTime = this.configInfo.cronNum;
          this.formCustomData.cronType = this.configInfo.cronType;
          this.formCustomData.deviceNum = this.configInfo.deviceNum;
        } else {
          const { regionCode } = this.moduleAction;

          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            dateRang: [{ hourStart: null, hourEnd: null }],
            timeDelay: null,
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
          };
          this.getEngineIndex(regionCode);
        }
        this.setDefaultTime();
      },
      immediate: true,
    },
    // moduleAction: {
    //   handler(val) {
    //     if (val.config) {
    //       this.formData = {
    //         ...this.configInfo,
    //       }
    //     } else {
    //       const { regionCode } = val
    //       this.formData = {
    //         ...this.formData,
    //         regionCode: regionCode, // 检测对象
    //         detectMode: '1',
    //         dateRang: [{ hourStart: null, hourEnd: null }],
    //         timeDelay: null,
    //       }
    //       this.getEngineIndex(val.regionCode)
    //     }
    //     this.setDefaultTime()
    //   },
    //   immediate: true,
    // },
  },
  methods: {
    interfaceInfoConfig() {
      let configList = ['VIDEO_PLATFORM_ONLINE_RATE', 'FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'];
      return !configList.includes(this.moduleAction.indexType) && this.formData.apiListModel.length > 0;
    },
    setDefaultTime() {
      if (
        this.moduleAction.indexType === 'FACE_PLATFORM_ONLINE_RATE' ||
        ('VEHICLE_PLATFORM_ONLINE_RATE' && !this.formData.timeDelay)
      ) {
        this.formData.timeDelay = '1';
        this.formData.dateRang = [{ hourStart: 0, hourEnd: 24 }];
      }
      this.orgList = [];
    },
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    // 选择组织机构
    chooseCode() {
      this.getCode();
    },
    choosQuery(data) {
      this.orgCodes = data;
      this.formData.orgCodeList = this.orgCodes;
    },
    handleDetectMode(val) {
      this.selectAreaTree.regionCode = val;
    },
    async getCode() {
      try {
        let res = await this.$http.get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.selectAreaTree.regionCode },
        });
        this.areaTreeData = this.$util.common.arrayToJson(res.data.data, 'id', 'parentId');
        this.$nextTick(() => {
          this.orgModalVisible = true;
        });
      } catch (err) {
        console.log(err);
      }
    },

    // 获取接口配置参数
    getEngineIndex(val) {
      this.$http.get(governanceevaluation.getEngineIndexById, { params: { regionCode: val } }).then((res) => {
        var obj = res.data.apiListModel;
        this.formData.apiListModel = obj;
      });
    },
    cityClick(item, index) {
      this.current = index;
    },
    // 表单提交校验
    async handleSubmit() {
      const formcustomres = await this.$refs['formCustom'].validate();
      const commonres = await this.$refs['commonForm'].handleSubmit();
      return formcustomres && commonres;
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    handleBlurTime() {
      this.formData.cronData = [parseInt(this.formCustomData.checkTime)];
    },
    handleTypeChange(val) {
      this.formData.cronType = val;
    },
    handleBlurNum() {
      this.formData.deviceNum = this.formCustomData.deviceNum;
    },
  },
};
</script>

<style lang="less" scoped>
@import url('../../../../components/common.less');
.platform-user {
  margin-bottom: 20px;
  .platform {
    height: 450px;
    overflow-y: auto;
    margin-left: 40px;
    .text {
      color: var(--color-label);
      margin-left: 25px;
      margin-bottom: 15px;
    }
  }
  .left-city {
    float: left;
    width: 200px;
    min-height: 300px;
    color: #fff;
    border: 1px solid #037cbe;
    padding: 10px;
    border-radius: 5px;
  }
  .edit-form {
    margin-left: 220px;
    width: 650px;
  }

  .topUl {
    height: 30px;
    border-bottom: 1px solid #037cbe;
  }
  ul {
    color: #fff;
    margin-top: 30px;
    li {
      .item {
        float: left;
        width: 25%;
      }

      .item::after {
        clear: both;
        content: '';
      }

      /deep/ .ivu-input {
        width: 80%;
      }
    }
  }

  .orgCode {
    color: #fff;
    margin-top: 20px;
  }

  .clear {
    clear: both;
  }
  .check-plan {
    display: flex;
    align-items: center;
    color: #ffffff;
  }

  .ul {
    margin-top: 0;

    li {
      position: relative;
      width: 120px;
      padding: 0 10px;
      height: 40px;
      line-height: 40px;

      .til {
        width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 10px;
      }
    }
    li:hover {
      cursor: pointer;
    }
    li.active {
      cursor: pointer;
      background: #184f8d;
    }

    .switch {
      position: absolute;
      top: 8px;
      right: -58px;
    }
  }

  @{_deep} .ivu-modal-body {
    padding: 0 40px 0 40px !important;
  }

  @{_deep} .ivu-tooltip-rel {
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 10px;
  }
}
.notCheck {
  color: #56789c;
}
.inspection {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}
.label-color {
  color: #e44f22;
}
.color-white {
  color: #fff;
}
.color-bule {
  color: #1b82d2 !important;
}
.width-picker {
  width: 174px;
}
.form-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}
.w390 {
  width: 390px;
}
@{_deep}.select-width,
.input-width {
  width: 380px;
}
</style>
