export default {
    data() {
        return {
            originDataTank: {}
        }
    },
    methods: {
        getTreeData: function(arr, sarr, darr, fileTaskValue) {
            let rootPath = "";
            let re = new RegExp("\\\\", "g");
            let rex = new RegExp("\/\/", "g");
            let root = this;
            [arr, rootPath] = this.deleteRootFolder(arr);
            let pobj = {};
            arr.forEach((item, index) => {
                //  let srcItem = item.replace(re,"/").replace(rex,"/");
                let destItem = darr[0];
                let starr = item.split("/");
                for (let i = 0; i <= starr.length - 1; i++) {
                    let cobj = {};
                    if (i === starr.length - 1 &&  starr[i].split(".").length > 1) {
                        cobj.parentPath = starr[i - 1] ? starr[i - 1] : "根文件夹";
                        cobj.parentId = cobj.parentPath;
                        cobj.path = starr[i];
                        cobj.id = cobj.path;
                        cobj.name = starr[i].substr(0,starr[i].lastIndexOf("."));
                        cobj.fileSizeTable = root.getFileSizeTable(sarr[index]);
                        cobj.fileSize = parseInt(sarr[index], 10);
                        let middleSplit = destItem.dest[index].replace(re, "/").replace(rex, "/").split("/");
                        let fileConvertId = middleSplit[middleSplit.length - 2];
                        cobj.fileConvertId = fileConvertId;
                        cobj.originalFileLocalPath = `${rootPath}/${item}`;
                        cobj.type = 2;
                        cobj.isLeaf = true;
                        cobj.analyzeType = fileTaskValue.analyzeType;
                        cobj.videoTasks = [{
                            "detectTypeId": fileTaskValue.analyzeType
                        }]
                        if(darr[0].destsvr && darr[0].destsvr.length >0 ){
                            for (let k = 0; k < darr[0].destsvr.length; k++) {
                                let name =darr[0].destsvr[k].name;
                                if(name === starr[i]){
                                    cobj.ip = darr[0].destsvr[k].ip;
                                    console.log(`${name}------${darr[0].destsvr[k].ip}`);
                                    break;
                                }
                            }
                        }else{
                            cobj.ip = darr[0].ip;
                        }
                        console.log('cobj.ip :', cobj.ip);
                        if (pobj[cobj.parentPath]) {
                            pobj[cobj.parentPath].children.push(cobj.path);
                        }else {
                            pobj[cobj.parentPath] = {};
                            pobj[cobj.parentPath].children = [];
                            pobj[cobj.parentPath].path = cobj.parentPath;
                            pobj[cobj.parentPath].id = cobj.path;
                            pobj[cobj.parentPath].name = cobj.parentPath;
                            pobj[cobj.parentPath].type = 1;
                            pobj[cobj.parentPath].isOpen = pobj[cobj.parentPath].isOpen ? true : false;
                            pobj[cobj.parentPath].childLoaded = pobj[cobj.parentPath].childLoaded ? true : false;
                            pobj[cobj.parentPath].children.push(cobj.path);
                        }
                        pobj[cobj.parentPath].children = [...new Set(pobj[cobj.parentPath].children)];
                        if (!pobj[cobj.path]) pobj[cobj.path] = cobj;
                    } else {
                        cobj.path = starr[i] ? starr[i] : '根文件夹';
                        cobj.id = cobj.path ? cobj.path : '根文件夹';
                        cobj.parentPath = starr[i - 1] ? starr[i - 1] : "";
                        cobj.parentId = cobj.parentPath;
                        if (cobj.parentPath !== "") {
                            if (pobj[cobj.parentPath]) pobj[cobj.parentPath].children.push(cobj.path);
                            else {
                                pobj[cobj.parentPath] = {};
                                pobj[cobj.parentPath].children = [];
                                pobj[cobj.parentPath].path = cobj.parentPath;
                                pobj[cobj.parentPath].name = cobj.parentPath;
                                pobj[cobj.parentPath].type = 1;
                                pobj[cobj.parentPath].isOpen = pobj[cobj.parentPath].isOpen ? true : false;
                                pobj[cobj.parentPath].childLoaded = pobj[cobj.parentPath].childLoaded ? true : false;
                                pobj[cobj.parentPath].children.push(cobj.path);
                            }
                            pobj[cobj.parentPath].children = [...new Set(pobj[cobj.parentPath].children)];
                        }
                        if (!pobj[cobj.path]) {
                            cobj.type = 1;
                            cobj.isOpen = cobj.isOpen ? true : false;
                            cobj.childLoaded = cobj.childLoaded ? true : false;
                            cobj.name = cobj.path;
                            cobj.children = [];
                            pobj[cobj.path] = cobj;
                        }
                    }
                }
            });
            let midObj = pobj;
            let pobjArr = [];
            for (let key in midObj) {
                pobjArr.push(midObj[key]);
            }
            let originData = {
                "structureTaskId": "",
                "name": fileTaskValue.taskName && fileTaskValue.taskName !== "" ? fileTaskValue.taskName : '新建任务',
                "executeTime": 0,
                "totalFileSize": 0,
                "totalDuration": 0,
                "fileCount": 0,
                "finishedFileCount": 0,
                "createTime": 0,
                "createOrgId": "",
                "creatorId": "",
                "createOrgName": null,
                "creatorName": null,
                "taskTreeNodeExtendList": pobjArr
            };
            return originData;
        },
        transMiddleData: function(data) {
            let pobj = data.taskTreeNodeExtendList;
            let arrTree = [];
            for (let key in pobj) {
                pobj[key].children = [];
                arrTree.push(pobj[key]);
            }
            let treeData = this.transData(arrTree, "path", "parentPath", "children");
            let obj = {
                id: '00000000-0000-0000-0000-000000000000',
                structureTaskId: data.structureTaskId,
                name: data.name,
                executeTime: data.executeTime,
                totalFileSize: data.totalFileSize,
                totalDuration: data.totalDuration,
                fileCount: data.fileCount,
                finishedFileCount: data.finishedFileCount,
                createTime: data.createTime,
                createOrgId: data.createOrgId,
                creatorId: data.creatorId,
                createOrgName: data.createOrgName,
                creatorName: data.creatorName,
                type: 1,
                isOpen: data.isOpen ? true : false,
                childLoaded: data.childLoaded ? true : false,
                children: treeData
            };
            return [obj];
        },
        transData: function(a, idStr, pidStr, chindrenStr) {
            let r = [],
                hash = {},
                id = idStr,
                pid = pidStr,
                children = chindrenStr,
                i = 0,
                j = 0,
                len = a.length;
            for (; i < len; i++) {
                hash[a[i][id]] = a[i];
            }
            for (; j < len; j++) {
                var aVal = a[j],
                    hashVP = hash[aVal[pid]];
                if (hashVP) {
                    !hashVP[children] && (hashVP[children] = []);
                    if (typeof aVal === "object") hashVP[children].push(aVal);
                } else {
                    r.push(aVal);
                }
            }
            return r;
        },
        transDataInter: function(data) {
            let resData = data.taskTreeNodeExtendList;
            let root = this;
            let tree = []
            let obj = {
                id: '00000000-0000-0000-0000-000000000000',
                structureTaskId: data.structureTaskId,
                name: data.name,
                executeTime: data.executeTime,
                totalFileSize: data.totalFileSize,
                totalDuration: data.totalDuration,
                fileCount: data.fileCount,
                finishedFileCount: data.finishedFileCount,
                createTime: data.createTime,
                createOrgId: data.createOrgId,
                creatorId: data.creatorId,
                createOrgName: data.createOrgName,
                creatorName: data.creatorName,
                type: 1,
                isOpen: data.isOpen ? true : false,
                childLoaded: data.childLoaded ? true : false,
                children: [],
                ip:""
            };
            tree.push(obj);
            run(tree);

            function run(chiArr) {
                if (resData.length !== 0) {
                    for (let i = 0; i < chiArr.length; i++) {
                        for (let j = 0; j < resData.length; j++) {
                            resData[j].parentId = resData[j].parentId === "" ? resData[j].parentId="00000000-0000-0000-0000-000000000000":resData[j].parentId ;
                            if (chiArr[i].id === resData[j].parentId) {
                                let obj = {
                                    id: resData[j].id,
                                    parentId: resData[j].parentId,
                                    name: resData[j].name,
                                    fileConvertId: resData[j].fileConvertId,
                                    originalFileLocalPath: resData[j].originalFileLocalPath,
                                    baseTime: resData[j].baseTime,
                                    fileSizeTable: root.getFileSizeTable(resData[j].fileSize),
                                    fileSize: parseInt(resData[j].fileSize, 10),
                                    longitude: resData[j].longitude ? resData[j].longitude : 0,
                                    latitude: resData[j].latitude ? resData[j].latitude : 0,
                                    position: `${resData[j].longitude ? resData[j].longitude : 0} ${resData[j].latitude ? resData[j].latitude : 0}`,
                                    detectTypeId: resData[j].detectTypeId,
                                    ocxParam: resData[j].ocxParam,
                                    width: resData[j].width ? resData[j].width : 0,
                                    height: resData[j].height ? resData[j].height : 0,
                                    ratio: `${resData[j].width ? resData[j].width : 0}*${resData[j].height ? resData[j].height : 0}`,
                                    children: [],
                                    ip:resData[j].ip
                                };
                                if (resData[j].fileConvertId && resData[j].fileConvertId !== '') {
                                    obj.type = 2;
                                    obj.isLeaf = true;
                                    let analyzeTypeSet = new Set();
                                    if(resData[j].type === 2 && resData[j].structureOldType){
                                        resData[j].structureOldType.face ? analyzeTypeSet.add(1):null;
                                        resData[j].structureOldType.human ? analyzeTypeSet.add(2):null;
                                        resData[j].structureOldType.vehicle ? analyzeTypeSet.add(4):null;
                                        resData[j].structureOldType.nonmotor ? analyzeTypeSet.add(8):null;
                                        resData[j].analyzeType = Array.from(analyzeTypeSet).toString();
                                        obj.analyzeType = resData[j].analyzeType;
                                    }else{
                                        obj.analyzeType = resData[j].detectTypeId ? resData[j].detectTypeId : resData[j].analyzeType;
                                    }
                                    obj.videoTasks = [{
                                        "detectTypeId": resData[j].analyzeType ? resData[j].analyzeType : resData[j].detectTypeId,
                                        "ocxParam":resData[j].videoTasks ? resData[j].videoTasks[0].ocxParam : resData[j].ocxParam
                                    }]
                                } else {
                                    obj.type = 1;
                                    obj.isOpen = resData[j].isOpen ? true : false;
                                    obj.childLoaded = resData[j].childLoaded ? true : false;
                                }
                                chiArr[i].children.push(obj);
                                resData.splice(j, 1);
                                j--;
                            }
                        }
                        run(chiArr[i].children);
                    }
                }
            }
            return tree;
        },
        deleteRootFolder: function(arr) {
            let minLen = 0;
            let rarr = [];
            let rootPath = "";
            // let re = new RegExp("\\\\","g");
            // let rex = new RegExp("\/\/","g");
            if(arr.length > 1) {
                arr.forEach((item, index) => {
                    let len = item.split("\\").length - 2;
                    if (index === 0) minLen = len;
                    else minLen = minLen > len ? len : minLen;
                });
                arr.forEach((item, index) => {
                    let is = item.split("\\");
                    let strs = is.slice(minLen).join("/");
                    if (index === 0) rootPath = is.splice(0, minLen).join("/");
                    rarr.push(strs);
                });
            } else {
                arr.forEach((item, index) => {
                    let len = item.filepath ? item.filepath.split("\\").length - 2 : item.split("\\").length - 2;
                    if (index === 0) minLen = len;
                    else minLen = minLen > len ? len : minLen;
                });
                arr.forEach((item, index) => {
                    let is = item.filepath ? item.filepath.split("\\"):item.split("\\");
                    let strs = is.slice(minLen).join("/");
                    if (index === 0) rootPath = is.splice(0, minLen).join("/");
                    rarr.push(strs);
                });
            }
            return [rarr, rootPath];
        },
        treeDataToArray: function(arr) {
            let rootFolder = IX.clone(arr);
            let innerp = IX.clone(rootFolder.children);
            rootFolder.children = [];
            rootFolder.taskTreeNodeExtendList = [];
            run(innerp);

            function run(innerp) {
                innerp.forEach((item, index) => {
                    let obj = IX.clone(item);
                    // obj.children = [];
                    rootFolder.taskTreeNodeExtendList.push(obj);
                    if (item.children && item.children.length > 0)
                        run(item.children);
                })
            }
            return rootFolder;
        },
        getTreeDataFunc() {
            let root = this;
            let treeData = [];
            let innerP = IX.clone(root.originDataTank);
            if (root.taskId && root.taskId !== '') {
                treeData = root.transDataInter(innerP);
            } else {
                treeData = root.transMiddleData(innerP);
            }
            var treeDataLength = root.flat(treeData)
            treeDataLength.map(item => {
                if(item._nodeLevel > 9) {
                    treeData = []
                    root.$refs.dataTable.clear();
                    root.componentId = "ocxFolderUploader"
                    root.$Message.warning("当前文件目录超过7层，请重新上传");
                    return
                }
            })
            // if(isEmpty) {
            //     root.componentId = "ocxFolderUploader"
            // }
            return treeData;
        },
        flat(arr){
            return [].concat(...arr.map(item =>
                [].concat(item, ...this.flat(item.children))                  
            ));
        },
        getBaseTime(item){
            if (!item.baseTime) {
                // 暂定两种正则,从文件名中匹配视频的开始时间，以此作为校准时间,否则采用当前时间作为校准（基准）时间
                let fileNameReg1 = /\[(20)\d{2}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}/g; //如:朝阳路与科苑路[2001]北快车道向南（枪）[2018-09-04-08-10-00-2018-09-04-08-40-00].mbf
                let fileNameReg2 = /_(20)\d{12}/g; //如:(av#21#7)_20180903083800.000_20180903084018.000.mbf | ch12_20130303235707.mp4
                let fileNameReg3 = /\((20)\d{12}至(20)\d{12}\)/g; //如:B_NH4383南湖路与曹凌路交口东100米路南(20240226085844至20240226090244).mbf
                if (fileNameReg1.test(item.name)) {
                    let splitLoc = item.name.indexOf('[20'); //寻找'[20'位置,开始截取时间
                    let timeFromName = item.name.substr(splitLoc + 1, 19); //截取文件名中的时间
                    let formateTime = timeFromName.substr(0, 10) + timeFromName.substr(10).replaceAll('-', ':'); //时间格式转换 yyyy-MM-dd hh:mm:ss
                    item.baseTime = new Date(formateTime).getTime();
                } else if (fileNameReg2.test(item.name)) {
                    let splitLoc = item.name.indexOf('_20'); //寻找'_20'位置,开始截取时间
                    let timeFromName = item.name.substr(splitLoc + 1, 14); //截取文件名中的时间
                    let year = timeFromName.substr(0, 4);
                    let month = timeFromName.substr(4, 2);
                    let day = timeFromName.substr(6, 2);
                    let hour = timeFromName.substr(8, 2);
                    let minute = timeFromName.substr(10, 2);
                    let seconds = timeFromName.substr(12);
                    item.baseTime = new Date(`${year}-${month}-${day} ${hour}:${minute}:${seconds}`).getTime();
                } else if (fileNameReg3.test(item.name)) {
                    let splitLoc = item.name.indexOf('(20'); //寻找'_20'位置,开始截取时间
                    let timeFromName = item.name.substr(splitLoc + 1, 14); //截取文件名中的时间
                    let year = timeFromName.substr(0, 4);
                    let month = timeFromName.substr(4, 2);
                    let day = timeFromName.substr(6, 2);
                    let hour = timeFromName.substr(8, 2);
                    let minute = timeFromName.substr(10, 2);
                    let seconds = timeFromName.substr(12);
                    item.baseTime = new Date(`${year}-${month}-${day} ${hour}:${minute}:${seconds}`).getTime();
                } else {
                    item.baseTime = Date.now();
                }
            }
            if (isNaN(item.baseTime)) item.baseTime = Date.now();
            return item.baseTime;
        },
        getTableDataFunc() {
            let root = this;
            let tableData = [];
            // let innerP = IX.clone(root.originDataTank);
            let innerP = root.originDataTank;
            innerP.taskTreeNodeExtendList.forEach(item => {
                let miObj = {};
                if (item.fileConvertId && item.fileConvertId !== '') {
                    if (!item.uploadSuccessTime) item.uploadSuccessTime = '--';
                    // if(!item.adjustTime) item.adjustTime = new Date().format('yyyy-MM-dd hh:mm:ss');
                    item.baseTime = root.getBaseTime(item);
                    if (!item.position) {
                        // `${resData[j].longitude} ${resData[j].latitude}`
                        if (!!item.longitude && !!item.latitude) {
                            item.position = `${item.longitude} ${item.latitude}`;
                        } else {
                            item.position = '0 0';
                        }
                    }else{
                        if(item.position !== "0 0"){
                            let temp = [0, 0];
                            if(item.position && item.position.indexOf(",") > 0) temp = item.position.split(",");
                            if(item.position && item.position.indexOf(" ") > 0) temp = item.position.split(" ");
                            item.longitude = temp[0] === "0.0" ? 0: temp[0] ;
                            item.latitude = temp[1] === "0.0" ? 0: temp[1];
                        }
                    }
                    if (!item.ratio) {
                        if (!!item.width && !!item.height) {
                            item.ratio = `${item.width}*${item.height}`;
                        } else {
                            item.ratio = '0*0';
                        }
                    }
                    if (!item.fileSizeTable) {
                        if (!!item.fileSize) item.fileSizeTable = root.getFileSizeTable(item.fileSize)
                        else item.fileSizeTable = '--';
                    }
                    miObj = item;
                    miObj.fileName = Toolkits.replaceparamtohttp(item.name);
                    let detectTypeIds = [];
                    if (item.videoTasks && item.videoTasks.length > 0) {
                        item.videoTasks.forEach((videoTask, i) => {
                            detectTypeIds.push(videoTask.detectTypeId);
                        });
                        miObj.analyzeType = detectTypeIds.join(',');
                    } else {
                        miObj.analyzeType = item.detectTypeId? item.detectTypeId : item.analyzeType;
                    }
                    tableData.push(miObj);
                }
            });
            return tableData;
        },
        getInterSect: function(a, b) {
            return Array.minus(a.filter(item => item), b.filter(item => item));
        },
        getFileSizeTable(limit) {
            if (!limit) return '0'
            let size = "";
            if (limit < 0.1 * 1024) { //如果小于0.1KB转化成B
                size = limit.toFixed(2) + "B";
            } else if (limit < 0.1 * 1024 * 1024) { //如果小于0.1MB转化成KB
                size = (limit / 1024).toFixed(2) + "KB";
            } else if (limit < 0.1 * 1024 * 1024 * 1024) { //如果小于0.1GB转化成MB
                size = (limit / (1024 * 1024)).toFixed(2) + "MB";
            } else { //其他转化成GB
                size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
            }

            let sizestr = size + "";
            let len = sizestr.indexOf("\.");
            let dec = sizestr.substr(len + 1, 2);
            if (dec == "00") { //当小数点后为00时 去掉小数部分
                return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
            }
            return sizestr;
        }
    }
};
