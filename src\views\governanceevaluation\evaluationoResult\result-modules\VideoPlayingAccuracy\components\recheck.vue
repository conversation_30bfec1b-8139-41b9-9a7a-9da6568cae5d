<template>
  <ui-modal
    v-model="visible"
    :title="!!isBatchRecheck ? '批量复检' : '复检'"
    :styles="styles"
    @query="handleSubmit"
    @onCancel="handleCancel"
  >
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="100">
      <FormItem label="复检设备" v-if="isBatchRecheck">
        <RadioGroup v-model="formValidate.model">
          <Radio v-for="(modelItem, modelIndex) in modelList" :key="modelIndex" :label="modelItem.dataKey">
            <span>{{ modelItem.dataValue }}</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="" class="custom-form-item" v-if="formValidate.model === 'CUSTOM'">
        <choose-recheck-device
          :tableColumns="tableColumns"
          :formModel="formModel"
          :regionCode="paramsList[this.codeKey]"
          @getDeviceQueryForm="getDeviceQueryForm"
        >
          <template slot="qualified" slot-scope="{ row }">
            <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
              {{ qualifiedColorConfig[row.qualified].dataValue }}
            </Tag>
            <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
          </template>
          <template #deviceId="{ row }">
            <span :class="row.rowClass">{{ row.deviceId }}</span>
          </template>
          <template #delaySipMillSecond="{ row }">
            <span>
              {{ !row.delaySipMillSecond ? '--' : row.delaySipMillSecond }}
            </span>
          </template>
          <template #phyStatus="{ row }">
            <span>
              {{ !row.phyStatus ? '--' : row.phyStatusText }}
            </span>
          </template>
          <template #delayStreamMillSecond="{ row }">
            <span>
              {{ !row.delayStreamMillSecond ? '--' : row.delayStreamMillSecond }}
            </span>
          </template>
          <template #delayIdrMillSecond="{ row }">
            <span>
              {{ !row.delayIdrMillSecond ? '--' : row.delayIdrMillSecond }}
            </span>
          </template>
          <template #videoStartTime="{ row }">
            <span>
              {{ !row.videoStartTime ? '--' : row.videoStartTime }}
            </span>
          </template>
          <!-- 在线状态 -->
          <template #online="{ row }">
            <span
              :class="{
                color_qualified: row.online === '1',
                color_unqualified: row.online === '2',
              }"
            >
              {{ row.online === '1' ? '在线' : row.online === '2' ? '离线' : '' }}
            </span>
          </template>
          <!-- 完好状态 -->
          <template #normal="{ row }">
            <span
              :class="{
                color_qualified: row.normal === '1',
                color_unqualified: row.normal === '2',
              }"
            >
              {{ row.normal === '1' ? '取流及时响应' : row.normal === '2' ? '取流超时响应' : '' }}
            </span>
          </template>
          <!-- 可用状态 -->
          <template #canPlay="{ row }">
            <span
              :class="{
                color_qualified: row.canPlay === '1',
                color_unqualified: row.canPlay === '2',
              }"
            >
              {{ row.canPlay === '1' ? '取流成功' : row.canPlay === '2' ? '取流失败' : '' }}
            </span>
          </template>

          <template #reason="{ row }">
            <Tooltip :content="row.reason" transfer max-width="150">
              {{ row.reason }}
            </Tooltip>
          </template>
          <template #networkingQuality="{ row }">
            <span
              class="check-status"
              :class="[
                row.networkingQuality === '1' ? 'bg-b77a2a' : '',
                row.networkingQuality === '2' ? 'bg-17a8a8' : '',
                row.networkingQuality === '3' ? 'bg-D66418' : '',
              ]"
            >
              {{ getNetworkingQualityDesc(row.networkingQuality) }}
            </span>
          </template>
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
        </choose-recheck-device>
      </FormItem>
      <FormItem
        v-show="formValidate.model === 'CUSTOM'"
        label=""
        prop="deviceIds"
        :class="formValidate.model === 'CUSTOM' ? '' : 'custom-form-item'"
        :rules="[
          {
            required: formValidate.model === 'CUSTOM' ? true : false,
            message: '请选择设备',
            trigger: 'change',
            type: 'array',
          },
        ]"
      >
      </FormItem>
      <FormItem label="复检计划" v-if="isBatchRecheck">
        <RadioGroup v-model="formValidate.plan">
          <Radio v-for="(planItem, planIndex) in planList" :key="planIndex" :label="planItem.dataKey">
            <span>{{ planItem.dataValue }}</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="formValidate.plan === '2'"
        label=""
        prop="scheduletime"
        :rules="[
          {
            required: formValidate.plan === '2' ? true : false,
            message: '请选择复检时间',
            trigger: 'change',
            type: 'date',
          },
        ]"
      >
        <DatePicker
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          v-model="formValidate.scheduletime"
          placeholder="请选择复检时间"
          style="width: 200px"
          :options="startTimeOption"
          @on-change="handleChangeTime"
        ></DatePicker>
      </FormItem>
      <FormItem label="检测内容" prop="detectionMode">
        <CheckboxGroup class="check-inline" v-model="formValidate.detectionMode" @on-change="checkGroupChange">
          <template v-for="(checkItem, checkIndex) in detectionRules">
            <Checkbox
              v-if="!!checkItem.text"
              :key="'check' + checkIndex"
              :label="checkItem.value"
              :disabled="!!checkItem.disabled ? true : false"
            >
              <span v-if="checkItem.label">{{ checkItem.label }}:</span>{{ checkItem.text }}
            </Checkbox>
            <Checkbox
              v-else
              :key="'check' + checkIndex"
              :label="checkItem.value"
              :disabled="!!checkItem.disabled ? true : false"
            >
              <span v-if="checkItem.label">{{ checkItem.label }}:</span>{{ checkItem.text1 }}
              <Input v-model="formValidate.detectionTimeOut" placeholder="秒" class="ml-xs mr-xs" style="width: 60px" />
              {{ checkItem.text2 }}
            </Checkbox>
            <div
              v-if="
                checkItem.label === '联网质量' && formValidate.detectionMode && formValidate.detectionMode.includes('4')
              "
              :key="'checknet' + checkIndex"
              class="base-text-color online"
            >
              <div class="mb-sm">
                信令时延超时：
                <InputNumber class="mr-sm" v-model.number="formValidate.delaySipTimeOut"></InputNumber>
                毫秒
              </div>
              <div class="mb-sm">
                码流时延超时：
                <InputNumber class="mr-sm" v-model.number="formValidate.delayStreamTimeOut"></InputNumber>
                毫秒
              </div>
              <div>
                关键帧时延超时：
                <InputNumber class="mr-sm" v-model.number="formValidate.delayIdrTimeOut"></InputNumber>
                毫秒
              </div>
            </div>
          </template>
        </CheckboxGroup>
      </FormItem>
      <FormItem
        label="指标取值"
        v-if="!['VIDEO_GENERAL_OSD_ACCURACY', 'VIDEO_OSD_ACCURACY'].includes(moduleData.indexType)"
      >
        <RadioGroup v-model="formValidate.indexDetectionMode" @on-change="handleIndexDetectionModeChange">
          <Radio
            v-for="(checkItem, checkIndex) in detectionRules"
            :key="'check' + checkIndex"
            :label="(checkIndex + 1).toString()"
            :disabled="
              !!formValidate.detectionMode && !formValidate.detectionMode.includes((checkIndex + 1).toString())
            "
            >{{ checkItem.label }}
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="需要视频截图">
        <RadioGroup v-model="formValidate.isScreenshots">
          <Radio label="1" :disabled="!!formValidate.detectionMode && !formValidate.detectionMode.includes('3')"
            >是
          </Radio>
          <Radio label="2" :disabled="!!formValidate.osdMode && formValidate.osdModel === 'ocr'">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';

export default {
  name: 'recheck',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    moduleData: {
      type: Object,
      default: () => {},
    },
    isBatchRecheck: {
      type: Boolean,
      default: false,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      title: '复检',
      styles: {
        width: '4rem',
      },
      paramsList: {},
      codeKey: '',
      formValidate: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        detectionMode: [], // 检测内容
        indexDetectionMode: '', // 指标取值
        scheduletime: '', // 自定义时间
        isScreenshots: '1', // 需要视频截图
        delaySipTimeOut: '',
        delayStreamTimeOut: '',
        delayIdrTimeOut: '',
        deviceIds: [],
      },
      ruleValidate: {},
      detectionRules: [
        { label: '在线率', text: '设备在国标平台为在线状态', value: '1' },
        { label: '完好率', text: '设备在线，拉流请求有响应', value: '2' }, // , text2: '秒内有响应', isTime: true,
        { label: '可用率', text: '成功接收到实时视频流', value: '3' },
        {
          label: '联网质量',
          text: '检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时',
          value: '4',
        },
      ],
      modelList: [
        { dataKey: 'ALL', dataValue: '全部已检测设备' },
        { dataKey: 'UNQUALIFIED', dataValue: '检测不合格设备' },
        { dataKey: 'CUSTOM', dataValue: '自定义' },
      ],
      schedule: '',
      planList: [
        { dataKey: '1', dataValue: '立即复检  ' },
        { dataKey: '2', dataValue: '自定义时间' },
      ],
      parameters: {}, // 设备选择组件回显参数,
      formModel: '',
      osdType: ['VIDEO_GENERAL_OSD_ACCURACY', 'VIDEO_OSD_ACCURACY'], // 通字幕标注合规率、重点字幕标注合规率
      historyVideoIndexs: [
        'VIDEO_HISTORY_ACCURACY', // 重点历史视频可调阅率
        'VIDEO_GENERAL_HISTORY_ACCURACY', // 普通历史视频可调阅率
      ],
      oldIndexDetectionMode: '',
      deviceQueryForm: {},
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      ids: [],
      taskIndexId: '',
      startTimeOption: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
    };
  },
  methods: {
    async getConfig() {
      try {
        const {
          data: { data },
        } = await this.$http.get(governanceevaluation.getTaskIndexConfig + `/${this.taskIndexId}`);
        const { extensionData } = data;
        const { indexConfig } = JSON.parse(extensionData);
        this.formValidate.detectionMode = indexConfig.detectionMode;
        this.formValidate.indexDetectionMode = indexConfig.indexDetectionMode;
        this.formValidate.isScreenshots = indexConfig.isScreenshots;
        this.oldIndexDetectionMode = indexConfig.indexDetectionMode;
        if (indexConfig.detectionMode.includes('4')) {
          this.formValidate.delaySipTimeOut = indexConfig.delaySipTimeOut || '';
          this.formValidate.delayStreamTimeOut = indexConfig.delayStreamTimeOut || '';
          this.formValidate.delayIdrTimeOut = indexConfig.delayIdrTimeOut || '';
        }
      } catch (e) {
        console.log(e);
      }
    },
    checkGroupChange(val) {
      if (!val.length || !!this.osdType.includes(this.moduleData.indexType)) {
        this.formValidate.indexDetectionMode = '';
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b); // 从小到大排序
      // 找出最大值进行遍历（第一项单独操作不联动）
      const sortMax = Number(sortDetectionMode[sortDetectionMode.length - 1]);
      if (sortMax > 1) {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 1 : 2;
        for (let mx = startIndex; mx < sortMax; mx++) {
          if (!sortDetectionMode.includes(mx.toString())) {
            let mode = this.$util.common.deepCopy(this.formValidate.detectionMode);
            mode.push(mx.toString());
            this.$set(this.formValidate, 'detectionMode', mode);
          }
        }
      }
      if (!val.includes('3')) {
        this.$set(this.formValidate, 'isScreenshots', '2');
      } else {
        this.$set(this.formValidate, 'isScreenshots', '1');
      }
      if (val.includes(this.oldIndexDetectionMode)) {
        this.formValidate.indexDetectionMode = this.oldIndexDetectionMode;
      } else {
        this.oldIndexDetectionMode = '';
        this.formValidate.indexDetectionMode = '';
      }
    },
    handleCheckDiabled(val) {
      if (!val.length) {
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b);
      sortDetectionMode.forEach((item) => {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 0 : 1;
        if (item > startIndex && item !== sortDetectionMode[sortDetectionMode.length - 1]) {
          this.$set(this.detectionRules[item - 1], 'disabled', true);
        } else {
          this.$set(this.detectionRules[item - 1], 'disabled', false);
        }
      });
    },
    getDeviceQueryForm({ ids, totalCount, params, ...deviceQueryForm }) {
      this.deviceQueryForm = deviceQueryForm;
      this.ids = ids;
      if (deviceQueryForm.checkDeviceFlag === '2') {
        this.formValidate.deviceIds.push(this.ids);
      } else {
        this.$set(this.formValidate, 'deviceIds', ids);
      }
      this.$nextTick(() => {
        this.$refs.formValidate.validateField('deviceIds');
      });
    },
    handleChangeTime(date) {
      this.schedule = date;
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs['formValidate'].validate((valid) => valid);
        if (!valid) {
          this.$Message.error('请将信息填写完整！');
          return false;
        }
        const params = {
          batchId: this.paramsList.batchId,
          extensionData: {
            indexConfig: {
              regionCode: this.paramsList[this.codeKey],
              detectionMode: this.formValidate.detectionMode,
              isScreenshots: this.formValidate.isScreenshots,
              indexDetectionMode: this.formValidate.indexDetectionMode,
              // useRecentCache: "true",
            },
            reinspect: {
              reinspectCustomConfig: this.deviceQueryForm,
              model: this.isBatchRecheck ? this.formValidate.model || 'CUSTOM' : 'CUSTOM',
              plan: this.formValidate.plan || '1',
              type: !this.isBatchRecheck ? 'PROGRAM_SINGLE' : 'PROGRAM_BATCH', // MANUAL,PROGRAM_SINGLE,PROGRAM_BATCH
              ids: this.isBatchRecheck ? this.ids : [this.moduleData.deviceId],
            },
          },
        };
        if (this.formValidate.detectionMode.includes('4')) {
          params.extensionData.indexConfig = Object.assign(params.extensionData.indexConfig, {
            delayIdrTimeOut: this.formValidate.delayIdrTimeOut,
            delaySipTimeOut: this.formValidate.delaySipTimeOut,
            delayStreamTimeOut: this.formValidate.delayStreamTimeOut,
          });
        }
        if (this.isBatchRecheck) {
          params.extensionData.reinspect.plan = this.formValidate.plan;
          params.extensionData.reinspect.scheduleKey = 6;
          params.extensionData.reinspect.scheduleValue = this.schedule;
          params.extensionData.reinspect.ids = this.ids;
        }
        await this.$http.post(evaluationoverview.programRecheck, params);
        this.$Message.success('复检操作成功！');
        this.$emit('handleUpdate');
        this.handleCancel();
      } catch (e) {
        console.log(e);
      }
    },
    handleCancel() {
      this.formValidate = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        detectionMode: [], // 检测内容
        indexDetectionMode: '', // 指标取值
        scheduletime: '', // 自定义时间
        isScreenshots: '1', // 需要视频截图
      };
      this.schedule = '';
      this.formValidate.deviceIds = [];
      this.visible = false;
    },
    handleIndexDetectionModeChange(val) {
      this.oldIndexDetectionMode = val;
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.paramsList = this.$route.query;
        if (!this.paramsList || !this.paramsList.batchId) return false;
        this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
    'formValidate.detectionMode'(val) {
      // 不需要禁掉的指标: 通字幕标注合规率、重点字幕标注合规率
      if (!val || !!this.osdType.includes(this.indexType)) return false;
      this.handleCheckDiabled(val);
    },
    moduleData: {
      handler(val) {
        this.taskIndexId = val.taskIndexId;
        if (val !== '') this.getConfig();
      },
    },
  },
  components: {
    ChooseRecheckDevice: require('../../../components/choose-recheck-device/choose-recheck-device').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>

<style lang="less" scoped>
.online {
  margin-left: 25px;
}

.check-inline {
  display: inline-block !important;
}
.custom-form-item {
  margin-bottom: 0 !important;
}
@{_deep} .ivu-form-item {
  .ivu-form-item-content {
    .ivu-checkbox-group {
      display: flex;
      flex-direction: column;

      .ivu-checkbox-wrapper {
        display: flex;
        align-items: center;

        > span {
          margin-right: 10px;
        }
      }
    }

    .ivu-radio-group {
      .ivu-radio-group-item {
        margin-right: 30px;

        > span {
          margin-right: 10px;
        }
      }
    }
  }

  .camera {
    margin: 0 !important;
  }
}
</style>
