const qualifiedColorConfig = {
  '1': {
    color: 'var(--color-success)',
    dataValue: '合格',
  },
  '2': {
    color: 'var(--color-failed)',
    dataValue: '不合格',
  },
};
const searchConfig = {
  'VIDEO_GENERAL_PLAYING_ACCURACY': [
    {
      type: 'region',
      label: '行政区划',
    },
    {
      type: 'org',
      label: '组织机构',
    },
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'outcome',
      label: '检测结果',
      placeholder: '请选择检测结果',
      options: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          value: key,
          label: qualifiedColorConfig[key].dataValue,
        };
      }),
    },
    // {
    //     type: 'select',
    //     key: 'errorCodes',
    //     label: '不合格原因',
    //     selectMutiple: true,
    //     placeholder: '请选择异常原因',
    //     options: [],
    // },
    {
      type: 'select',
      key: 'online',
      label: '在线状态',
      placeholder: '请选择在线状态',
      options: [
        { value: '1', label: '在线' },
        { value: '2', label: '离线' },
      ],
    },
    {
      type: 'select',
      key: 'normal',
      label: '完好状态',
      placeholder: '请选择完好状态',
      options: [
        { value: '1', label: '取流及时响应' },
        { value: '2', label: '取流超时响应' },
      ],
    },
    {
      type: 'select',
      key: 'canPlay',
      label: '可用状态',
      placeholder: '请选择可用状态',
      options: [
        { value: '1', label: '取流成功' },
        { value: '2', label: '取流失败' },
      ],
    },
    {
      type: 'selecttabs',
      label: '标签类型',
    },
  ], // 普通实时视频可调阅率
};
export default searchConfig;
