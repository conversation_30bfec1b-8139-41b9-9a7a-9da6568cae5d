<template>
  <div class="asset-select-container">
    <span class="mr-sm f-14 color-blue">{{ name }}</span>
    <Dropdown @on-click="onClickMenu">
      <i class="icon-font icon-shaixuan f-16 color-filter"></i>
      <DropdownMenu slot="list">
        <DropdownItem v-for="(item, index) in dataList" :key="index" :name="item.name">{{ item.name }}</DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'type-select',
  components: {},
  props: {
    dataList: {
      type: Array,
      default() {},
    },
    defaultName: {
      type: String,
      default: '全部指标',
    },
    select: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      name: '',
    };
  },
  computed: {},
  watch: {
    defaultName: {
      deep: true,
      immediate: true,
      handler: function () {
        this.name = this.defaultName;
      },
    },
    dataList: {
      handler: function () {
        if (this.select) {
          this.name = this.defaultName;
        }
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    onClickMenu(val) {
      this.name = val;
      this.$emit('getName', this.name);
    },
  },
};
</script>

<style lang="less" scoped>
.color-blue {
  color: #63ccfc;
}
.color-filter {
  color: #1d84a0;
  &:hover {
    color: #539aea;
  }
}
@{_deep} .ivu-dropdown .ivu-select-dropdown {
  max-height: 600px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
