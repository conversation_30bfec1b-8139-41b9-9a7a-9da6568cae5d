<template>
  <div class="config-container">
    <ui-label class="inline mb-sm mr-lg" label="任务名称">
      <Select
        class="width-md"
        v-model="searchData.taskSchemeId"
        placeholder="请选择任务名称"
        clearable
        filterable
        @on-change="onChangeTaskScheme"
      >
        <Option v-for="(item, index) in listTaskSchemes" :key="`${index}-${item.id}`" :value="item.id"
          >{{ item.taskName }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mb-sm mr-sm" label="指标名称">
      <Select
        class="width-md"
        :loading="loading"
        v-model="searchData.taskIndexId"
        placeholder="请选择指标名称"
        clearable
        filterable
        :disabled="!searchData.taskSchemeId"
        @on-change="onChangeTaskIndex"
      >
        <Option v-for="(item, index) in indexConfigData" :key="`${index}-${item.id}`" :value="item.id"
          >{{ item.indexName }}
        </Option>
      </Select>
    </ui-label>
    <Button type="text" @click="config" :loading="indexConfigLoading">
      <span class="inline vt-middle link">填入</span>
    </Button>
  </div>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  name: 'index-config',
  components: {},
  props: {
    filterIndex: {
      default: () => [],
    },
  },
  data() {
    return {
      listTaskSchemes: [],
      indexConfigData: [], // 所有指标配置
      indexConfig: {}, // 单个指标配置
      searchData: {
        taskSchemeId: '',
        taskIndexId: '',
      },
      loading: false,
      indexConfigLoading: false,
    };
  },
  computed: {},
  watch: {},
  filter: {},
  async created() {
    await this.getListTaskSchemes();
  },
  methods: {
    onChangeTaskScheme(val) {
      this.searchData.taskIndexId = '';
      if (!val) {
        this.indexConfigData = [];
        return;
      }
      this.getTaskIndexGeneralConfig();
    },
    onChangeTaskIndex(val) {
      if (!val) {
        this.indexConfig = [];
        return;
      }
      this.getConfig();
    },
    async getListTaskSchemes() {
      try {
        this.listTaskSchemes = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.listTaskSchemes = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    async getTaskIndexGeneralConfig() {
      try {
        this.loading = true;
        let params = {
          taskSchemeId: this.searchData.taskSchemeId,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getTaskIndexGeneralConfig, params);
        if (!this.filterIndex.length) {
          return (this.indexConfigData = data.indexConfigData || []);
        }
        this.indexConfigData = (data.indexConfigData || []).filter((item) => this.filterIndex.includes(item.indexType));
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async getConfig() {
      try {
        this.indexConfigLoading = true;
        let {
          data: { data },
        } = await this.$http.get(`${governanceevaluation.getTaskIndexConfig}/${this.searchData.taskIndexId}`);
        this.indexConfig = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.indexConfigLoading = false;
      }
    },
    config() {
      if (!this.searchData.taskSchemeId || !this.searchData.taskIndexId) {
        return this.$Message.error('请选择任务和指标');
      }
      const { extensionData } = this.indexConfig;
      this.$emit('on-change', JSON.parse(extensionData || '{}'));
    },
  },
};
</script>

<style lang="less" scoped></style>
