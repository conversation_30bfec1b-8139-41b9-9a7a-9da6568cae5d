<template>
  <!--轨迹重复率 -->
  <div class="basic-information auto-fill">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
      </div>
      <TableCard ref="infoCard" :loadData="loadDataCard">
        <div slot="search" class="hearder-title">
          <SearchCard
            ref="search"
            style="margin: 0"
            @startSearch="startSearch"
            :selectList="[
              { id: 0, label: '轨迹未重复' },
              { id: 1, label: '轨迹重复' },
            ]"
          />
        </div>
        <!-- 卡片 -->
        <template #card="{ row }">
          <div class="carItem">
            <div class="item">
              <div class="img" @click="viewBigPic(row.trackLargeImage)">
                <ui-image :src="row.trackImage" />
                <div class="percentage" v-if="row.sameAmount">
                  {{ similarityVal(row.sameAmount) }}
                </div>
                <p class="shadow-box" @click.stop="captureDetail(row)" style="z-index: 11" title="查看轨迹">
                  <i class="icon-font icon-jianceguijishuliang search-icon mr-xs base-text-color"></i>
                </p>
              </div>
              <div class="group-message">
                <p class="marginP" :title="`抓拍时间：${row.shotTime || '暂无数据'}`">
                  <i class="icon-font icon-shijian"></i>
                  <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{ row.shotTime }}</span>
                </p>
                <p :title="row.catchPlace">
                  <i class="icon-font icon-dizhi"></i>
                  <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                    row.catchPlace ? row.catchPlace : '暂无数据'
                  }}</span>
                </p>
              </div>
            </div>
          </div>
        </template>
      </TableCard>
    </div>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <!-- 查看轨迹 -->
    <RepeatLocusMoadl ref="repeatLocusMoadl" @viewBigPicShow="viewBigPic" />
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'repeat-locus',
  components: {
    LookScene: require('@/components/look-scene').default,
    TableCard: require('./component/tableCard.vue').default,
    SearchCard: require('@/components/track-detail-search.vue').default,
    statistics: require('@/components/icon-statistics').default,
    RepeatLocusMoadl: require('./component/repeat-locus-moadl.vue').default,
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statisticalList: {},
      paramsList: {},
      bigPictureShow: false,
      imgList: [],
      searchData: {},
      statisticsList: [
        {
          name: 'ZDR人员总量',
          value: 0,
          icon: 'icon-ZRDzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          key: 'importPersonNumbr',
          textColor: 'color1',
        },
        {
          name: 'ZDR人像轨迹总量',
          value: 0,
          icon: 'icon-shishiguijishangchuanjishixingjiance',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'importPersonTrajectoryNumbr',
          textColor: 'color2',
        },
        {
          name: '检测轨迹数',
          value: 0,
          icon: 'icon-jianceguijishuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'detectionAmount',
          textColor: 'color4',
        },
        {
          name: '重复轨迹数量',
          value: 0,
          icon: 'icon-guanlianshebeiguijishuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          key: 'repeatAmount',
          textColor: 'color6',
        },
        {
          name: '未重复轨迹数量',
          value: 0,
          icon: 'icon-weiguanlianshebeiguijishuliang',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          key: 'noRepeatAmount',
          textColor: 'color5',
        },
        {
          name: '轨迹重复率',
          value: 0,
          icon: 'icon-guijishebeiguanlianxingjiance',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          qualified: true,
          key: 'repeatRate',
          textColor: 'color3',
        },
      ],
      loadDataCard: (parameter) => {
        let data = {
          indexId: this.paramsList.indexId,
          regionCode: this.paramsList.regionCode,
          resultId: this.paramsList.resultId,
        };
        return this.$http
          .post(evaluationoverview.getDetailData, Object.assign(parameter, data, this.searchData))
          .then((res) => {
            return res.data;
          });
      },
    };
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.regionCode) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 初始化
    init() {
      this.searchData = {};
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
    //统计
    statistical() {
      this.statisticsList[0].value = this.statisticalList.importPersonNumbr;
      this.statisticsList[1].value = this.statisticalList.importPersonTrajectoryNumbr;
      this.statisticsList[2].value = this.statisticalList.detectionAmount;
      this.statisticsList[3].value = this.statisticalList.repeatAmount;
      this.statisticsList[4].value = this.statisticalList.detectionAmount - this.statisticalList.repeatAmount;
      let rate = this.statisticalList.repeatRate ? parseFloat(this.statisticalList.repeatRate) : '0.0';
      if (rate >= this.statisticalList.qualifiedRate) {
        this.statisticsList[5].qualified = true;
      } else {
        this.statisticsList[5].qualified = false;
      }
      this.statisticsList[5].value = rate + '%';
    },
    similarityVal(val) {
      return Number(val) > 100 ? '99+' : val;
    },
    // 查看全部轨迹
    captureDetail(item) {
      this.$refs.repeatLocusMoadl.info(item);
    },
  },
};
</script>
<style lang="less" scoped>
.basic-information {
  .information-header {
    margin-top: 10px;
    height: 140px;
    display: flex;
    @{_deep}.information-statistics {
      width: 100%;
      height: 140px;
      padding: 20px;
      background: var(--bg-sub-content);
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 16.18% !important;
    }
  }
  .information-main {
    height: calc(100% - 140px);
    .abnormal-title {
      padding: 10px 0;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
  .ui-images {
    width: 56px;
    height: 56px;
    margin: 5px 0;
    .ui-image {
      min-height: 56px !important;
      /deep/ .ivu-spin-text {
        img {
          width: 56px;
          height: 56px;
          margin-top: 5px;
        }
      }
    }
  }
  .carItem {
    height: 240px;
    margin: 10px 10px 0px 0;
    width: calc(calc(100% - 90px) / 9);
    display: inline-block;
    .percentage {
      height: 18px;
      padding: 0 4px;
      line-height: 18px;
      background: #2cabc6;
      color: #fff;
      text-align: center;
      position: absolute;
      top: 14px;
      right: 0px;
      z-index: 11;
    }
    .item {
      position: relative;
      height: 100%;
      background: #0f2f59;
      .num {
        position: absolute;
        right: 0;
        z-index: 100;
        padding: 10px;
        border-radius: 5px;
        background: rgba(42, 95, 175, 0.6);
      }
      .img {
        cursor: pointer;
        position: relative;
        width: 160px;
        height: 174px;
        padding-top: 14px;
        margin-left: 14px;
        display: flex;
        align-items: center;
        .shadow-box {
          height: 28px;
          width: 100%;
          background: rgba(0, 0, 0, 0.3);
          position: absolute;
          bottom: 0;
          display: none;
          padding-left: 10px;
          > i:hover {
            color: var(--color-primary);
          }
        }
        &:hover {
          .shadow-box {
            display: block;
          }
        }
      }
      img {
        width: 100%;
        max-width: 100%;
        max-height: 156px;
        background: #999;
      }

      .group-message {
        padding-left: 12px;
        margin-top: 8px;
        color: #8797ac;
      }
    }
  }
  /deep/.information-statistics .statistics-ul .icon-budabiao,
  .icon-dabiao {
    top: 70px !important;
  }
  /deep/ .base-search .date-picker-box .ivu-date-picker {
    margin-bottom: 0 !important;
  }
}
</style>
