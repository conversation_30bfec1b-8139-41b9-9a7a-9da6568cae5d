const cache = {};
const themeAction = {
  dark () {
    if (!cache.dark) {
      cache.dark = import('@/style/themes/theme-dark/index-useable.less');
    }
    return cache.dark;
  },
  light () {
    if (!cache.light) {
      cache.light = import('@/style/themes/theme-light/index-useable.less');
    }
    return cache.light;
  }
};

let current = null;

async function setTheme (theme) {
  if (themeAction[theme]) {
    const style = await themeAction[theme]();
    console.log(style)
    if (current) {
      current.unuse();
    }
    style.use();
    current = style;
  }
}
export default setTheme;
