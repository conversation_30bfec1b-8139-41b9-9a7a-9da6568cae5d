// import OverlayLayerOpts = NPMapLib.Layers.OverlayLayerOpts;
import * as PortraitTool from "@/util/modules/common.js";
import { LayerType, siteType, juvenileAlarmType } from "../enum/LayerType";
// 场所图标分配
const palceMap = new Map();
// 已经分配了的图标索引
let iconIndex = 0;
/**
 * 图层工厂
 * 配置聚合点位的展示效果逻辑
 */
export class OverlayFactory {
  static getClusterOverlayOpt(opts) {
    if (opts) {
      const result = PortraitTool.extend(
        true,
        {},
        OverlayFactory.getDefaultClusterOverlayOpt(),
        opts
      );
      return result;
    }
    return OverlayFactory.getDefaultClusterOverlayOpt();
  }
  //  资源图层 场所 数据解析
  static getClusterOverlayOptSite(opts) {
    if (opts) {
      const result = PortraitTool.extend(
        true,
        {},
        OverlayFactory.getDefaultClusterOverlayOptSite(),
        opts
      );
      return result;
    }
    return OverlayFactory.getDefaultClusterOverlayOptSite();
  }
  static getDefaultClusterOverlayOpt() {
    return {
      // 点击摄像机点位
      // click: function (f) {
      //     console.log('2333')
      //     // console.debug("click.clusterMarker");
      // },
      mouseover: function (marker) {
        // marker.changeStyle({
        //     externalGraphic: require('@/assets/img/map/mapPoint/map-face_active.png'),
        // })
        // console.debug("mouseover", marker);
      },
      mouseout: function (marker) {
        // marker.changeStyle({
        //     externalGraphic: require('@/assets/img/map/mapPoint/map-face.png'),
        // })
        // console.debug("clusteronmouseout",marker);
      },
      getUrl: function (count, marker) {
        // 获取用户的
        let url;
        if (count) {
          // 有count, 说明是聚合类型
          switch (marker.markType) {
            case LayerType.Camera.value:
              url = require("@/assets/img/map/camera_cluster_2.png");
              break;
            // 枪机，球机
            case LayerType.Camera_QiuJi.value:
              url = require("@/assets/img/map/camera_cluster_2.png");
              break;
            case LayerType.Camera_QiangJi.value:
              url = require("@/assets/img/map/camera_cluster_2.png");
              break;
            case "":
              url = require("@/assets/img/map/aggregation/camera_cluster.png");
              break;
            // 人脸卡口
            case LayerType.Camera_Face.value:
              url = require("@/assets/img/map/aggregation/face_cluster.png");
              break;
            // 车辆卡口
            case LayerType.Camera_Vehicle.value:
              url = require("@/assets/img/map/aggregation/vehicle_cluster.png");
              break;
            // WIFI
            case LayerType.Camera_Wifi.value:
              url = require("@/assets/img/map/aggregation/WIFI_cluster.png");
              break;
            // RFID
            case LayerType.Camera_RFID.value:
              url = require("@/assets/img/map/aggregation/RFID_cluster.png");
              break;
            default:
              url = require("@/assets/img/map/map-cluster.png");
              break;
          }
        } else {
          // 没有count, 说明是单个点位, 在这里根据每个点位的数据来获取图标
          // markType 实际上就是取的 layerType 故这里使用LayerTypeEnum;
          switch (marker.markType) {
            // 普通视频
            case LayerType.Camera.value:
            case LayerType.Camera_QiangJi.value:
              // 判断是否为在线状态
              if (marker.ext.Status == "0") {
                if (marker.ext.sbgnlx == 1) {
                  url = LayerType.Camera_QiangJi.url;
                } else if (marker.ext.sbgnlx == 2) {
                  url = LayerType.Camera_QiangJi.url2;
                } else {
                  url = LayerType.Camera_QiangJi.url3;
                }
              } else {
                url = LayerType.Camera_QiangJi.offLineUrl;
              }
              break;
            case LayerType.Camera_QiuJi.value:
              // 判断是否为在线状态
              if (marker.ext.Status == "0") {
                if (marker.ext.sbgnlx == 1) {
                  url = LayerType.Camera_QiuJi.url;
                } else if (marker.ext.sbgnlx == 2) {
                  url = LayerType.Camera_QiuJi.url2;
                } else {
                  url = LayerType.Camera_QiuJi.url3;
                }
              } else {
                url = LayerType.Camera_QiuJi.offLineUrl;
              }
              break;
            // 人脸卡口
            case LayerType.Camera_Face.value:
              url = LayerType.Camera_Face.url;
              break;
            // 车辆卡口
            case LayerType.Camera_Vehicle.value:
              url = LayerType.Camera_Vehicle.url;
              break;
            // WIFI
            case LayerType.Camera_Wifi.value:
              url = LayerType.Camera_Wifi.url;
              break;
            // RFID
            case LayerType.Camera_RFID.value:
              url = LayerType.Camera_RFID.url;
              break;
            // 电围
            case LayerType.Camera_Electric.value:
              url = LayerType.Camera_Electric.url;
              break;
            // etc
            case LayerType.Camera_ETC.value:
              url = LayerType.Camera_ETC.url;
              break;
            // 多功能
            // case LayerType.Camera_Multi.value:
            //   url = OverlayFactory.fillterCamera(marker, 'Camera_Multi')
            //   break
            // 酒店
            case siteType.Place_Hotel.value:
              url = siteType.Place_Hotel.url;
              break;
            // 网吧
            case siteType.Place_InterBar.value:
              url = siteType.Place_InterBar.url;
              break;
            // 政府机关
            case siteType.Place_Government.value:
              url = siteType.Place_Government.url;
              break;
            // 学校
            case siteType.Place_School.value:
              url = siteType.Place_School.url;
              break;
            // 重点场所
            case siteType.Place_Key.value:
              url = siteType.Place_Key.url;
              break;
            // 一级报警
            case juvenileAlarmType.LevelOne.value:
              url = juvenileAlarmType.LevelOne.url;
              break;
            // 一级报警
            case juvenileAlarmType.LevelTwo.value:
              url = juvenileAlarmType.LevelTwo.url;
              break;
            // 一级报警
            case juvenileAlarmType.LevelThree.value:
              url = juvenileAlarmType.LevelThree.url;
              break;
            default:
              // 默认为普通摄像机
              if (marker.ext.Active) {
                url = require("@/assets/img/map/map-camera-active.png?v=1");
              } else {
                url = require("@/assets/img/map/map-camera-normal.png?v=1");
              }
              break;
          }
          if (marker.ext.isPoleGroupPoint) url = LayerType.PoleGroupPoint.url;
          marker.normalUrl = url;
        }
        return url;
      },
      getImageSize: function (count, marker) {
        let size;
        if (count) {
          size = {
            width: count < 10000 ? 36 : count.toString().length * 8,
            height: 30,
          };
        } else {
          switch (marker.markType) {
            case LayerType.Camera.value:
            // case LayerType.Camera_Body.value:
            // case LayerType.Camera_Face.value:
            //     size = {
            //         width: 34,
            //         height: 42
            //     }
            //     break
            // case LayerType.Camera_Vehicle.value:
            //     size = {
            //         width: 34,
            //         height: 42
            //     }
            //     break
            default:
              size = {
                width: 24,
                height: 26,
              };
              break;
          }
        }
        return size;
      },
      clusterClickModel: "zoom", // 示例为slice  ,zoom是放大

      getBackGroundColor: function (marker) {
        return "red";
      },
      getCustomLabelOffset: function (marker) {
        return {
          // width: 10,
          // height: 20
          width: 24,
          height: 27,
        };
      },

      clusteronmouseover: function (m) {
        // 大点 的鼠标移入事件
        // console.debug("clusteronmouseover", m);
      },
      clusterclick: function (m) {
        // console.debug("clusterclick", m);
      },
      // getRotation: function(count: number, marker: NPMapLib.Symbols.ClusterMarker){
      //     return 0;
      // },
      fontColor: "white",
      // distance和maxZoom的根据实际情况来
      // 示例为200
      distance: 200,
      maxZoom: 18,
      // 聚合信息偏移
      labelYOffset: 2,
      threshold: 2,
    };
  }
  static getDefaultClusterOverlayOptSite() {
    return {
      getUrl: function (count, marker) {
        // 获取用户的
        let url;
        if (count) {
          // 有count, 说明是聚合类型
          url = require("@/assets/img/map/map-cluster.png");
        } else {
          // 没有count, 说明是单个点位, 在这里根据每个点位的数据来获取图标
          // markType 实际上就是取的 siteType 故这里使用LayerTypeEnum;
          switch (marker.markType) {
            // 酒店
            case siteType.Place_Hotel.value:
              url = require("@/assets/img/map/map-hotel.png");
              break;
            // 网吧
            case siteType.Place_InterBar.value:
              url = require("@/assets/img/map/map-internetBar.png");
              break;
            // 政府机关
            case siteType.Place_Government.value:
              url = require("@/assets/img/map/map-governmentAgency.png");
              break;
            // 学校
            case siteType.Place_School.value:
              url = require("@/assets/img/map/map-school.png");
              break;
            // 重点场所
            case siteType.Place_Key.value:
              url = require("@/assets/img/map/map-keyPoint.png");
              break;
            default:
              url = require("@/assets/img/map/map-hotel.png");
              break;
          }
        }
        return url;
      },
      getImageSize: function (count, marker) {
        let size;
        size = {
          width: 24,
          height: 24,
        };
        return size;
      },
      clusterClickModel: "zoom", // 示例为slice  ,zoom是放大
      // 点击摄像机点位
      click: function (f) {
        // console.debug("click.clusterMarker");
      },

      getBackGroundColor: function (marker) {
        return "red";
      },
      getCustomLabelOffset: function (marker) {
        return {
          width: 10,
          height: 20,
        };
      },
      mouseover: function (marker) {
        // console.debug("mouseover", f);
      },
      mouseout: function (marker) {
        // console.debug("clusteronmouseout",f);
      },
      clusteronmouseover: function (m) {
        // 大点 的鼠标移入事件
        // console.debug("clusteronmouseover", m);
      },
      clusterclick: function (m) {
        // console.debug("clusterclick", m);
      },
      // getRotation: function(count: number, marker: NPMapLib.Symbols.ClusterMarker){
      //     return 0;
      // },
      fontColor: "white",
      // distance和maxZoom的根据实际情况来
      // 示例为200
      distance: 100,
      maxZoom: 18,
      // 聚合信息偏移
      labelYOffset: 2,
      threshold: 2,
    };
  }
  static fillterCamera(marker, camera) {
    const cameraMap = {
      Camera_Face: {
        normal: require("@/assets/img/map/map-face-normal.png?v=1"),
        onLineabnormal: require("@/assets/img/map/map-face-on-line-abnormal.png?v=1"),
        offLineNormal: require("@/assets/img/map/map-face-off-line-normal.png?v=1"),
        offLineAbnormal: require("@/assets/img/map/map-face-off-line-abnormal.png?v=1"),
      },
      Camera_Vehicle: {
        normal: require("@/assets/img/map/map-vehicle-normal.png?v=1"),
        onLineabnormal: require("@/assets/img/map/map-vehicle-on-line-abnormal.png?v=1"),
        offLineNormal: require("@/assets/img/map/map-vehicle-off-line-normal.png?v=1"),
        offLineAbnormal: require("@/assets/img/map/map-vehicle-off-line-abnormal.png?v=1"),
      },
      Camera: {
        normal: require("@/assets/img/map/map-camera-normal.png?v=1"),
        onLineabnormal: require("@/assets/img/map/map-camera-on-line-abnormal.png?v=1"),
        offLineNormal: require("@/assets/img/map/map-camera-off-line-normal.png?v=1"),
        offLineAbnormal: require("@/assets/img/map/map-camera-off-line-abnormal.png?v=1"),
      },
      Camera_Multi: {
        normal: require("@/assets/img/map/map-multi-normal.png?v=1"),
        onLineabnormal: require("@/assets/img/map/map-multi-on-line-abnormal.png?v=1"),
        offLineNormal: require("@/assets/img/map/map-multi-off-line-normal.png?v=1"),
        offLineAbnormal: require("@/assets/img/map/map-multi-off-line-abnormal.png?v=1"),
      },
    };
    let url;
    if (marker.ext.Status !== "1") {
      // 设备离线
      if (["0000", "1000"].includes(marker.ext.isNormal)) {
        url = cameraMap[camera].offLineNormal;
      } else {
        url = cameraMap[camera].offLineAbnormal;
      }
    } else if (marker.ext.Status === "1") {
      // 设备正常
      if (["0000", "1000"].includes(marker.ext.isNormal)) {
        url = cameraMap[camera].normal;
      } else {
        url = cameraMap[camera].onLineabnormal;
      }
    } else {
      url = cameraMap[camera].normal;
    }
    return url;
  }
}
