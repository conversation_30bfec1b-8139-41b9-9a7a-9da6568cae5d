// import OverlayLayerOpts = NPMapLib.Layers.OverlayLayerOpts;
import * as PortraitTool from '../../../util/module/common';
import { LayerType } from '../enum/LayerType';
let themeType = document.documentElement.getAttribute('data-theme') || 'dark';
/**
 * 图层工厂
 * 配置聚合点位的展示效果逻辑
 */
export class OverlayFactory {
  static getClusterOverlayOpt(opts, customMarker) {
    if (opts) {
      let result = PortraitTool.extend(true, {}, OverlayFactory.getDefaultClusterOverlayOpt(customMarker), opts);
      return result;
    }
    return OverlayFactory.getDefaultClusterOverlayOpt(customMarker);
  }

  static getDefaultClusterOverlayOpt(customMarker) {
    return {
      getUrl: function (count, marker) {
        // 获取用户的
        let url;
        if (count) {
          // 有count, 说明是聚合类型
          url =
            themeType === 'dark'
              ? require('@/assets/img/map/map-cluster.png')
              : require('@/assets/img/map/map-cluster-light.png');
        } else if (Object.keys(customMarker).length) {
          return customMarker.func(marker);
        } else {
          // 没有count, 说明是单个点位, 在这里根据每个点位的数据来获取图标
          // markType 实际上就是取的 layerType 故这里使用LayerTypeEnum;
          switch (marker.markType) {
            // 普通视频
            case LayerType.Camera.value:
              // 判断是否为选中状态
              if (marker.ext.Active) {
                url =
                  themeType === 'dark'
                    ? require('@/assets/img/map/map-camera-active.png?v=1')
                    : require('@/assets/img/map/map-camera-active-light.png?v=1');
              } else {
                url = OverlayFactory.fillterCamera(marker, 'Camera');
              }
              break;
            // case LayerType.Camera_Body.value:
            //     url = "/images/map/map-camera-body.png";
            //     break;
            // 人脸卡口
            case LayerType.Camera_Face.value:
              url = OverlayFactory.fillterCamera(marker, 'Camera_Face');
              break;
            case LayerType.Camera_Portrait.value:
              url = require('@/assets/img/map/map-camera-portrait.png?v=1');
              break;
            // 车辆卡口
            case LayerType.Camera_Vehicle.value:
              url = OverlayFactory.fillterCamera(marker, 'Camera_Vehicle');
              break;
            // 多功能
            case LayerType.Camera_Multi.value:
              url = OverlayFactory.fillterCamera(marker, 'Camera_Multi');
              break;
            default:
              // 默认为普通摄像机
              if (marker.ext.Active) {
                url =
                  themeType === 'dark'
                    ? require('@/assets/img/map/map-camera-active.png?v=1')
                    : require('@/assets/img/map/map-camera-active-light.png?v=1');
              } else {
                url =
                  themeType === 'dark'
                    ? require('@/assets/img/map/map-camera-normal.png?v=1')
                    : require('@/assets/img/map/map-camera-normal-light.png?v=1');
              }
              break;
          }
        }
        return url;
      },
      getImageSize: function (count, marker) {
        let size;
        if (count) {
          size = {
            width: 32,
            height: 25,
          };
        } else if (Object.keys(customMarker).length) {
          size = {
            width: customMarker.width,
            height: customMarker.height,
          };
        } else {
          switch (marker.markType) {
            // case LayerType.Camera_Body.value:
            case LayerType.Camera.value:
            case LayerType.Camera_Face.value:
              size = {
                width: 35,
                height: 41,
              };
              break;
            case LayerType.Camera_Portrait.value:
              size = {
                width: 35,
                height: 41,
              };
              break;
            case LayerType.Camera_Vehicle.value:
              size = {
                width: 35,
                height: 41,
              };
              break;
            default:
              size = {
                width: 35,
                height: 41,
              };
              break;
          }
        }
        return size;
      },
      clusterClickModel: 'zoom', //示例为slice  ,zoom是放大
      //点击摄像机点位
      click: function () {
        // console.debug("click.clusterMarker");
      },

      getBackGroundColor: function () {
        return 'red';
      },
      getCustomLabelOffset: function () {
        return {
          width: 10,
          height: 20,
        };
      },
      mouseover: function () {
        //console.debug("mouseover", f);
      },
      mouseout: function () {
        //console.debug("clusteronmouseout",f);
      },
      clusteronmouseover: function () {
        //大点 的鼠标移入事件
      },
      clusterclick: function () {},
      // getRotation: function(count: number, marker: NPMapLib.Symbols.ClusterMarker){
      //     return 0;
      // },
      fontColor: 'white',
      // distance和maxZoom的根据实际情况来
      //示例为200
      distance: 100,
      maxZoom: 18,
      // 聚合信息偏移
      labelYOffset: 2,
    };
  }
  static fillterCamera(marker, camera) {
    const cameraMapDark = {
      Camera_Face: {
        normal: require('@/assets/img/map/map-face-normal.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-face-on-line-abnormal.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-face-off-line-normal.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-face-off-line-abnormal.png?v=1'),
      },
      Camera_Vehicle: {
        normal: require('@/assets/img/map/map-vehicle-normal.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-vehicle-on-line-abnormal.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-vehicle-off-line-normal.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-vehicle-off-line-abnormal.png?v=1'),
      },
      Camera: {
        normal: require('@/assets/img/map/map-camera-normal.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-camera-on-line-abnormal.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-camera-off-line-normal.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-camera-off-line-abnormal.png?v=1'),
      },
      Camera_Multi: {
        normal: require('@/assets/img/map/map-multi-normal.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-multi-on-line-abnormal.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-multi-off-line-normal.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-multi-off-line-abnormal.png?v=1'),
      },
    };
    const cameraMapLight = {
      Camera_Face: {
        normal: require('@/assets/img/map/map-face-normal-light.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-face-on-line-abnormal-light.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-face-off-line-normal-light.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-face-off-line-abnormal-light.png?v=1'),
      },
      Camera_Vehicle: {
        normal: require('@/assets/img/map/map-vehicle-normal-light.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-vehicle-on-line-abnormal-light.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-vehicle-off-line-normal-light.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-vehicle-off-line-abnormal-light.png?v=1'),
      },
      Camera: {
        normal: require('@/assets/img/map/map-camera-normal-light.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-camera-on-line-abnormal-light.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-camera-off-line-normal-light.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-camera-off-line-abnormal-light.png?v=1'),
      },
      Camera_Multi: {
        normal: require('@/assets/img/map/map-multi-normal-light.png?v=1'),
        onLineabnormal: require('@/assets/img/map/map-multi-on-line-abnormal-light.png?v=1'),
        offLineNormal: require('@/assets/img/map/map-multi-off-line-normal-light.png?v=1'),
        offLineAbnormal: require('@/assets/img/map/map-multi-off-line-abnormal-light.png?v=1'),
      },
    };
    let cameraMap = themeType === 'dark' ? cameraMapDark : cameraMapLight;
    let url;
    if (marker.ext.Status !== '1') {
      // 设备离线
      if (['0000', '1000'].includes(marker.ext.isNormal)) {
        url = cameraMap[camera].offLineNormal;
      } else {
        url = cameraMap[camera].offLineAbnormal;
      }
    } else if (marker.ext.Status === '1') {
      // 设备正常
      if (['0000', '1000'].includes(marker.ext.isNormal)) {
        url = cameraMap[camera].normal;
      } else {
        url = cameraMap[camera].onLineabnormal;
      }
    } else {
      url = cameraMap[camera].normal;
    }
    return url;
  }
}
