<!--
    * @FileDescription: 人体
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="main-container">
        <div class="search-bar">
            <search-human ref="searchBar"
                @search="searchHandle"
                @reset="resetHandle" />
        </div>
        <div class="table-container">
            <div class="data-above" v-if="mapOnData">
                <Checkbox @on-change="checkAllHandler" v-model="checkAll">全选</Checkbox>
                <Button @click="dataAboveMapHandler">
                    <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
                    数据上图
                </Button>
            </div>
            <div class="table-content">
                <div class="list-card box-1" v-for="(item, index) in dataList" :key="index" :class="{ checked: item.isChecked }">
                    <div class="collection paddingIcon">
                        <div class="bg"></div>
                        <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
                        <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
                    </div>
                    <Checkbox class="check-box" v-if="mapOnData" v-model="item.isChecked" @on-change="e => checkHandler(e, index)"></Checkbox>
                     <p class="img-content">
                        <span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
                        <ui-image :src="item.traitImg" alt="动态库" @click.native="handleDetail(item, index)" />
                        <!-- <img :src="item.traitImg" alt="" v-viewer /> -->
                    </p>
                    <div class="bottom-info">
                        <time>
                            <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                <i class="iconfont icon-time"></i>
                            </Tooltip>
                            {{ item.absTime }}
                        </time>
                        <p>
                            <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                <i class="iconfont icon-location"></i>
                            </Tooltip>
                            <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
                        </p>
                    </div>
                    <!-- <div class="operate-bar">
                        <p class="operate-content">
                            <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" />
                            <ui-btn-tip content="分析" icon="icon-fenxi" />
                            <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
                        </p>
                    </div> -->
                </div>
                <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
            </div>
            <ui-empty v-if="dataList.length === 0"></ui-empty>
            <ui-loading v-if="listLoading"></ui-loading>
            <!-- 分页 -->
            <ui-page :current="pageInfo.pageNumber" 
                :total="total" 
                countTotal
                :page-size="pageInfo.pageSize"
                :page-size-opts="[27, 54, 81, 108]"
                @pageChange="pageChange" 
                @pageSizeChange="pageSizeChange">
            </ui-page>
        </div>
        <details-modal 
            v-show="humanShow" 
            ref='humanbody'
            @prePage="prePage"
            @nextPage="nextPage" 
            @close="humanShow = false"></details-modal>
    </div>
</template>

<script>
import detailsModal from '@/components/detail/details-modal.vue';
import searchHuman from '../../ordinary-search/components/search-human.vue';
import { queryHumanRecordSearchEx } from '@/api/wisdom-cloud-search';
import { myMixins } from '../../components/mixin/index.js';
import { mapMutations, mapGetters } from 'vuex';
import { addCollection, deleteMyFavorite } from '@/api/user'
export default {
    name: '',
    props:{
        mapOnData: {
            type: Boolean,
            default: false
        }
    },
    mixins: [myMixins], //全局的mixin
    components:{
        searchHuman,
        detailsModal  
    },
    data () {
        return {
            dataList: [],
            listLoading: false,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize : 27
            },
            humanShow: false,
            queryParam: { },
            checkAll: false
        }
    },
    watch:{
            
    },
    computed:{
        ...mapGetters({
            getMaxLayer: 'countCoverage/getMaxLayer',
            getNum: 'countCoverage/getNum',
            getNewAddLayer: 'countCoverage/getNewAddLayer',
            getListNum: 'countCoverage/getListNum',
            globalObj: 'systemParam/globalObj',
        }),    
    },
    activated() {
        if(this.$route.query.deviceInfo){
            this.$nextTick(() =>{
                let deviceInfo = JSON.parse(this.$route.query.deviceInfo)
                this.$refs.searchBar.selectData([{...deviceInfo, select: true}])
                this.queryList()
            })
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.queryList()
        })
    },
    methods: {
        searchHandle() {
            this.pageInfo.pageNumber = 1;
            this.queryList();
        },
        queryList(page= 0) {
            let queryParam = this.$refs.searchBar.queryParam;
             queryParam = { ...queryParam, ...this.pageInfo };
            if(queryParam.similarity){
                queryParam.similarity = queryParam.similarity / 100
            }
            let deviceIds = queryParam.selectDeviceList.map(item => {
                return item.deviceId
            })
            this.queryParam = queryParam;
            this.dispTime()
            // 抓拍时段时间处理
            let { startDate, endDate } = this.timeTransition(this.queryParam.timeSlot, this.queryParam.timeSlotArr);
            this.queryParam.startDate = startDate;
            this.queryParam.endDate = endDate;
            this.queryParam.deviceIds = deviceIds;
            delete this.queryParam.selectDeviceList;
            delete this.queryParam.urlList;
            this.getDataList(page);
        },
        resetHandle() {
            this.pageInfo = {
                pageNumber: 1,
                pageSize : 27
            };
            let queryParam = this.$refs.searchBar.queryParam
            queryParam = { ...queryParam, ...this.pageInfo }
            this.queryParam = queryParam;
            this.dispTime()
            this.queryParam.similarity = this.queryParam.similarity / 100;
            this.checkAll = false;
            this.getDataList()
        },
        async getDataList(page= 0) {
            this.listLoading = true;
            queryHumanRecordSearchEx(this.queryParam)
            .then(res => {
                this.total = res.data.total;
                this.dataList = res.data.entities;
                if(page == 1) {
                    this.$refs.humanbody.prePage(this.dataList)
                }else if(page == 2) {
                    this.$refs.humanbody.nextPage(this.dataList)
                }
            })
            .finally(() => {
                this.listLoading = false
            })
        },
        // 详情
        handleDetail(row, index) {
            this.humanShow = true;
            this.$refs.humanbody.init(row, this.dataList, index, 1, this.pageInfo.pageNumber)
        },
        collection(item, flag) {
            var param = {
                favoriteObjectId: item.recordId,
                favoriteObjectType: 16,
            }
            if(flag == 1){
                addCollection(param).then(res => {
                    this.$Message.success("收藏成功");
                    this.getDataList()
                })
            }else{
                deleteMyFavorite(param).then(res => {
                    this.$Message.success("取消收藏成功");
                    this.getDataList()
                })
            }
        },
        checkAllHandler(val) {
            this.dataList = this.dataList.map(e => {
                return {
                    ...e,
                    isChecked: val
                }
            })
        },
        checkHandler(e, i) {
            this.dataList[i].isChecked = e
            this.checkAll = this.dataList.filter(e => e.isChecked).length === this.dataList.length ? true : false
        },
        ...mapMutations({
            setNum:'countCoverage/setNum',
            setList: 'countCoverage/setList',
        }),
        dataAboveMapHandler() {
            const list = this.dataList.filter(e => e.isChecked);
            if (!list.length) {
                this.$Message.warning('请选择上图数据');
                return
            }
            let seleNum = this.dataList.filter(e => e.isChecked);
            let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
            let newNumPoints = this.getNum.pointsNum + this.getNewAddLayer.pointsInLayer + seleNum.length; //点位
            if(Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
                this.$Message.warning('已达到图层最大创建数量')
                return
            }
            if(Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
                this.$Message.warning('已达到上图最大点位总量')
                return
            }
            let num = JSON.stringify(this.getListNum);
            this.setList(num++)
            this.setNum({'layerNum': newNumLayer, 'pointsNum': newNumPoints})
            seleNum.map(item => {
                item.delePoints = true;
                item.deleType = 'humanbody'
            })
            this.$emit('dataAboveMapHandler', { type: 'humanbody', list,  deleIdent: 'humanbody-' + this.getListNum})
        },
        archivesPage() {

        },
        pageChange(size) {
            this.pageInfo.pageNumber = size;
            this.queryList()
        },
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1
            this.pageInfo.pageSize = size
            this.queryList()
        },
        /**
         * 上一个
         */
        prePage(pageNum) {
            if(pageNum < 1) {
                this.$Message.warning("已经是第一个了")
                return
            } else {
                this.pageInfo.pageNumber = pageNum;
                this.queryList(1)
            }
        },
        /**
         * 下一个
         */
        async nextPage(pageNum) {
            this.pageInfo.pageNumber = pageNum;
            let num = this.pageInfo.pageNumber;
            let size = this.pageInfo.pageSize;
            if(this.total <= num*size) {
                this.$Message.warning("已经是最后一个了")
                return
            }else{
                this.queryList(2)
            }
        },
    }
}
</script>

<style lang='less' scoped>
@import 'style/index';
</style>
