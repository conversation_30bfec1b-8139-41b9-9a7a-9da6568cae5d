<!--
 * @Date: 2024-11-25 16:36:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-12-16 16:15:41
 * @FilePath: \icbd-view\src\components\H5Player\components\print-screen-modal.vue
-->
<template>
  <Modal
    ref="printScreenModal"
    :transfer="false"
    v-model="options.open"
    draggable
    :z-index="10000"
    sticky
    :mask="false"
    :title="options.title"
    footer-hide
    width="880"
  >
    <div
      class="img-box"
      :style="{ height: options.imgUrls.length > 1 ? '564px' : '514px' }"
    >
      <img-preview :imgUrls="options.imgUrls" :structured="true"></img-preview>
    </div>
  </Modal>
</template>
<script>
import imgPreview from "@/components/img-preview.vue";

export default {
  components: {
    imgPreview,
  },
  props: {
    options: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    "options.open": {
      handler(value) {
        // 全屏状态下将模态框内容加入到全屏dom体中
        if (value) {
          const element = document.querySelector("[is-full-video]");
          if (element) {
            element.appendChild(this.$refs.printScreenModal.$el);
          }
        }
      },
    },
  },
  data() {
    return {};
  },
  beforeDestroy() {
    this.options.open = false;
  },
  deactivated() {
    this.options.open = false;
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.img-box {
  width: 100%;
}
/deep/ .ivu-modal-header {
  padding: 6px 10px;
  background: rgba(211, 215, 222, 0.3);
  .ivu-modal-header-inner {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.9);
    font-size: 16px;
  }
}
/deep/ .ivu-modal-body {
  padding: 10px;
}
/deep/ .ivu-modal-close {
  top: 0;
  right: 2px;
}
</style>
