<template>
  <div class="video-container">
    <common-form
      :label-width="200"
      :form-data="formData"
      :form-model="formModel"
      ref="commonForm"
      :moduleAction="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <div slot="waycondiction" class="mt-xs" v-if="formData.detectMode !== '3'">
        <p>
          <span class="base-text-color">检测条件：</span>
          <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
            >设备可用</Checkbox
          >
          <Checkbox v-model="formData.deviceQueryForm.detectIsOnline" true-value="1" false-value="0">设备在线</Checkbox>
          <span class="color-failed">（实时视频可调阅率指标检测合格则设备在线）</span>
        </p>
        <p class="color-failed">说明：系统只检测满足过滤条件的设备，未选择过滤条件，检测全部设备。</p>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="200">
      <FormItem label="与标准时间允许的偏差" prop="timeDelay">
        <Input class="width-input" placeholder="请输入时间偏差" v-model="formData.timeDelay" />
        <span class="base-text-color ml-sm">秒</span>
      </FormItem>
      <FormItem label="检测方法" prop="osdModel">
        <Select v-model="formData.osdModel" placeholder="请选择检测方法" transfer class="width-input">
          <Option v-for="item in odsCheckModelList" :value="item.dataKey" :key="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem v-if="formData.osdModel !== 'sdk'">
        <template #label>
          <Tooltip placement="top-start" max-width="200">
            <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
            <div slot="content">
              如果设备当天有实时视频拉取成功记录，则直接取该次视频流截图进行OCR识别，不再重新拉流检测。
            </div>
            取最近缓存结果
          </Tooltip>
        </template>
        <RadioGroup v-model="formData.useRecentCache">
          <Radio label="true">是</Radio>
          <Radio label="false">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="是否需要复检">
        <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
          <Radio label="1">是</Radio>
          <Radio label="2">否</Radio>
        </RadioGroup>
        <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
      </FormItem>
      <FormItem label="复检设备" v-if="isRecheck === '1'" prop="scheduleType">
        <RadioGroup v-model="formData.reinspect.model">
          <Radio label="UNQUALIFIED">检测不合格设备</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="复检时间" v-if="isRecheck === '1'">
        <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
          <Radio label="INTERVAL">时间间隔</Radio>
          <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
        </RadioGroup>
        <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
          <span class="base-text-color">检测结束</span>
          <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
          <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
            <Option :value="1">时</Option>
            <Option :value="2">分</Option>
          </Select>
        </div>
        <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
          <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
            <Option :value="3">当天</Option>
            <Option :value="4">第二天</Option>
            <Option :value="5">第三天</Option>
          </Select>
          <TimePicker
            :value="formData.reinspect.scheduleValue"
            transfer
            format="HH:mm"
            placeholder="请选择"
            class="width-xs"
            @on-change="handleChangeTime"
          ></TimePicker>
        </div>
      </FormItem>
      <FormItem label="治理前可调阅率">
        <RadioGroup value="1">
          <Radio label="1">手动填写</Radio>
          <!--          <Radio label="2">规则指定</Radio>-->
        </RadioGroup>
      </FormItem>
      <div class="fr mb-md ml-sm">
        <ui-label required class="inline ml-lg" label="可调阅率" :width="70">
          <InputNumber v-model="callRate" class="width-md" :min="0" :max="100" placeholder="请输入可调阅率">
          </InputNumber>
        </ui-label>
        <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
      </div>
      <common-capture-area-select class="clear-b" ref="captureAreaSelect" :data="areaTreeData">
        <template #configName>
          <span class="inline width-md">治理前可调阅率</span>
        </template>
        <template #label="{ data }">
          <div class="width-md">
            <InputNumber
              v-show="data.check"
              class="width-xs"
              :min="0"
              :max="100"
              v-model.number="data.value"
              placeholder="请输入治理前可调阅率"
            ></InputNumber>
          </div>
        </template>
      </common-capture-area-select>
    </Form>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'video-osd-accuracy-promotion-rate',
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    CommonCaptureAreaSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-capture-area-select.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateDetectionModePass = (rule, value, callback) => {
      !value.length ? callback(new Error('请选择检测内容')) : callback();
    };
    // const validateTimeDelayPass = (rule, value, callback) => {
    //   !value.length ? callback(new Error('请输入与标准时间允许的偏差')) : callback();
    // };
    const validateThresholdPass = (rule, value, callback) => {
      !value ? callback(new Error('请输入综合质量评分设置值')) : callback();
    };
    const validateIndexDetectionMode = (rule, value, callback) => {
      !value ? callback(new Error('请选择指标取值')) : callback();
    };
    const validateMinThresholdPass = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        const reg = new RegExp('^([1-9]|[1-9]\\d|100)$');
        !reg.test(value) ? callback(new Error('请输入1-100的整数')) : callback();
      }
    };
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    return {
      defaultProps: {
        default: () => {
          return {
            label: 'regionName',
            children: 'children',
          };
        },
      },
      ruleCustom: {
        timeDelay: [{ message: '请输入与标准时间允许的偏差', trigger: 'blur', required: true }],
        detectionMode: [{ validator: validateDetectionModePass, trigger: 'change', required: true }],
        threshold: [{ validator: validateThresholdPass, trigger: 'blur' }],
        minThreshold: [{ validator: validateMinThresholdPass, trigger: 'blur' }],
        indexDetectionMode: [{ validator: validateIndexDetectionMode, trigger: 'change', required: true }],
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
      },
      formData: {
        detectMode: '1',
        selectType: 0,
        quantityConfig: [],
        deviceDetection: {},
        deviceQueryForm: {
          detectPhyStatus: '0',
        },
        osdModel: 'sdk',
        delaySipTimeOut: null,
        delayStreamTimeOut: null,
        delayIdrTimeOut: null,
        detectionTimeOut: null,
        isScreenshots: '1',
        checkRegion: {}, //治理前可调阅率
      },
      intactRateSelect: false, // 指标取值完好率
      useRateSelect: false, // 指标取值可用率
      regionalizationSelectVisible: false,
      customSearch: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      defaultCheckedList: [],
      allDeviceFileList: [],
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      contentStyle: {
        height: '3.125rem',
      },
      screenWidth: '',
      wangliCheckContent: [0, 1, 2, 3, 4, 5],
      oldIndexDetectionMode: '',
      isRecheck: '2',

      areaTreeData: [],
      callRate: null,
    };
  },
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
      treeData: 'common/getInitialAreaList',
    }),
  },
  filter: {},
  async created() {
    if (this.odsCheckModelList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    setTreeData() {
      let treeData = this.$util.common.deepCopy(
        this.$util.common.getTreeChildren(this.treeData, this.formData.regionCode),
      );
      treeData.forEach((item) => {
        item.value = this.formData.checkRegion[item.regionCode] || null;
        item.check = !!this.formData.checkRegion[item.regionCode];
      });
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
    },
    quantityQuery(data) {
      this.formData.quantityConfig = data;
    },
    async handleSubmit() {
      this.formData.checkRegion = {};
      this.getCheckedKeys(this.areaTreeData);
      if (Object.keys(this.formData.checkRegion).length === 0) {
        this.$Message.error('治理前可调阅率不能为空');
        return false;
      }
      const valid = await this.$refs['modalData'].validate();
      if (!valid) {
        this.$Message.error('请将配置信息填写完整！');
        return;
      }
      if (!this.validateForm()) {
        this.$Message.error('请选择检测规则');
        return;
      }
      return this.$refs['commonForm'].handleSubmit();
    },
    validateForm() {
      let flag = false;
      if (!this.formData.ruleList || !this.formData.ruleList.length) flag = true;
      this.formData.ruleList &&
        this.formData.ruleList.map((item) => {
          if (item.isConfigure == 1) flag = true;
        });
      return flag;
    },
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    checkGroupChange(val) {
      // 检测内容和指标取值对应联动 - 最严格标准
      if (this.indexDetectionAnddetection.includes(this.indexType)) {
        let findMaxArray = val.sort();
        this.formData.indexDetectionMode = findMaxArray[findMaxArray.length - 1];
      }
      if (!val.length || !!this.osdType.includes(this.indexType)) {
        this.formData.indexDetectionMode = '';
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b); // 从小到大排序
      // 找出最大值进行遍历（第一项单独操作不联动）
      const sortMax = Number(sortDetectionMode[sortDetectionMode.length - 1]);
      if (sortMax > 1) {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 1 : 2;
        for (let mx = startIndex; mx < sortMax; mx++) {
          if (!sortDetectionMode.includes(mx.toString())) {
            let mode = this.$util.common.deepCopy(this.formData.detectionMode);
            mode.push(mx.toString());
            this.$set(this.formData, 'detectionMode', mode);
          }
        }
      }
      if (!val.includes('3')) {
        this.$set(this.formData, 'isScreenshots', '2');
      } else {
        this.$set(this.formData, 'isScreenshots', '1');
      }
      if (!this.unwantedIndexValue.includes(this.indexType)) return false;
      if (val.includes(this.oldIndexDetectionMode)) {
        this.formData.indexDetectionMode = this.oldIndexDetectionMode;
      } else {
        this.oldIndexDetectionMode = '';
        this.formData.indexDetectionMode = '';
      }
    },
    handleCheckDiabled(val) {
      if (!val.length) {
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b);
      sortDetectionMode.forEach((item) => {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 0 : 1;
        if (item > startIndex && item !== sortDetectionMode[sortDetectionMode.length - 1]) {
          this.$set(this.moduleAction.checkConfig[item - 1], 'disabled', true);
        } else {
          this.$set(this.moduleAction.checkConfig[item - 1], 'disabled', false);
        }
      });
    },
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        let data = this.formData.emphasisData || [];
        this.checkedTreeData = data.map((item) => item.key);
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data, dataWithName) {
      this.formData.emphasisData = dataWithName;
    },
    confirmFilter(data) {
      this.customSearch = false;
      let list = [];
      data.map((item) => {
        list.push({
          key: item.tagId,
          value: item.tagName,
        });
      });
      this.formData.emphasisData = list;
    },
    getCheckResultIndexs() {
      let indexArr = [];
      this.checkResultTips.forEach((item) => {
        if (
          (!!item.hasOcr && !!this.formData.osdModel && this.formData.osdModel !== 'sdk') ||
          (!this.formData.osdModel && !item.hasOcr) ||
          ['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'].includes(this.indexType)
        ) {
          indexArr = indexArr.concat(item.indexs);
        }
      });
      return indexArr;
    },
    handleIndexDetectionModeChange(val) {
      this.oldIndexDetectionMode = val;
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },

    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        this.formData.reinspect.model = 'UNQUALIFIED';
        this.formData.reinspect.scheduleValue = null;
      }
    },
    handlekeyChange() {
      const detectionTimeOut = this.formData.detectionTimeOut.replace(/[^\d]/g, '');
      this.$set(this.formData, 'detectionTimeOut', detectionTimeOut);
    },
    checkAll(val, node, data) {
      this.$set(node.data, 'check', data.checkAll);
      if (node.childNodes) {
        if (data.checkAll) {
          this.checkParent(node, data.checkAll);
        } else {
          this.checkChildren([node], data.checkAll);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node) {
      this.checkParent(node, val);
      this.checkChildren([node], val);
    },
    checkParent(node, check = true) {
      this.$set(node.data, 'check', check);
      if (node.parent) {
        if (check) {
          this.checkParent(node.parent, check);
        }
      }
    },
    checkChildren(node, check) {
      node &&
        node.map((item) => {
          this.$set(item.data, 'checkAll', false);
          this.$set(item.data, 'check', check);
          if (item.childNodes) {
            this.checkChildren(item.childNodes);
          }
        });
    },

    clickBatchInput() {
      if (!this.callRate) {
        return this.$Message.error('请输入可调阅率');
      }
      this.batchInput(this.areaTreeData);
    },
    batchInput(data) {
      data.map((item) => {
        if (item.check) {
          this.$set(item, 'value', this.callRate);
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },
    getCheckedKeys(data) {
      data.map((item) => {
        if (item.check) {
          this.formData.checkRegion[item.regionCode] = item.value;
        }
        if (item.children) {
          this.getCheckedKeys(item.children);
        }
      });
    },
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
          };
        }
        this.setTreeData();
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped></style>
