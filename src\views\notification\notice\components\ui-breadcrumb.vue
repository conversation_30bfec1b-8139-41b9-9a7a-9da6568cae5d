<template>
  <div class="breadcrumb-container">
    <span class="box vt-middle"></span>
    <span class="ml-sm">
      <i
        v-for="(item, index) in dataList"
        :key="index"
        class="address color-white f-14"
        @click="handleClick(item, index)"
        >{{ `${item.add} ${index !== dataList.length - 1 ? '>' : ''}` }}</i
      >
    </span>
  </div>
</template>

<script>
export default {
  name: 'ui-breadcrumb',
  props: {
    data: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      dataList: [],
    };
  },
  watch: {
    data: {
      handler: function (val) {
        this.dataList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleClick(item, index) {
      this.dataList.splice(index + 1, this.dataList.length);
      this.$emit('change', item);
    },
  },
};
</script>

<style lang="less" scoped>
.address:last-child {
  color: var(--color-primary);
}
.color-white {
  color: #fff;
}
.active {
  color: var(--color-primary);
}
.box {
  display: inline-block;
  width: 4px;
  height: 14px;
  background: var(--color-primary);
}
.breadcrumb-container {
  position: relative;
  display: inline-block;
}
</style>
