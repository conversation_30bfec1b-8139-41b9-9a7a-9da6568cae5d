<template>
  <ui-card :title="title" class="owner-information">
    <div slot="extra" class="more-btn primary" @click="archivesInfoHandle">查看档案信息</div>
    <div class="owner-information-content card-border-color">
      <div class="owner-item">
        <div class="owner-label card-bg title-color card-border-color">车主姓名</div>
        <div class="owner-value text-color card-border-color">{{vehicleInfo.ownName}}</div>
      </div>
      <div class="owner-item">
        <div class="owner-label card-bg title-color card-border-color">车主身份证号码</div>
        <div class="owner-value text-color card-border-color">{{vehicleInfo.idcardNo}} </div>
      </div>
      <div class="owner-item">
        <div class="owner-label card-bg title-color card-border-color">车主联系方式</div>
        <div class="owner-value text-color card-border-color">{{vehicleInfo.ownPhone}} </div>
      </div>
      <!-- <div class="owner-item owner-border-bottom">
        <div class="owner-label card-bg title-color card-border-color">车主视频身份</div>
        <div class="owner-value text-color card-border-color">V2367812641289749120</div>
      </div> -->
      <div class="owner-item owner-border-bottom">
        <div class="owner-label card-bg title-color card-border-color">车主居住地址</div>
        <div class="owner-value address text-color card-border-color">{{vehicleInfo.ownAddress}}</div>
      </div>
    </div>
  </ui-card>
</template>
<script>
  export default {
    props: {
      title: {
        type: String,
        default: ''
      },
      // 车主信息
      vehicleInfo: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {

      }
    },
    watch: {
      vehicleInfo: {
        handler (val) {
        //   console.log('---', this.vehicleInfo)
        },
        immediate: true,
        deep: true
      },
    },
    methods: {
      // 查看档案详情
      archivesInfoHandle() {
        this.$router.push({
          name: 'people-archive',
          query: {
            archiveNo: this.vehicleInfo.idcardNo,
            source: 'people',
            initialArchiveNo: this.vehicleInfo.idcardNo
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .owner-information {
    /deep/.card-head {
      padding-right: 20px;
    }
    .owner-information-content {
      display: flex;
      flex-wrap: wrap;
      border-left: 1px solid #fff;
      .owner-item {
        display: flex;
        .owner-label {
          width: 170px;
          padding: 9px 10px;
          box-sizing: border-box;
          font-size: 14px;
          font-weight: bold;
          line-height: 20px;
          font-family: 'MicrosoftYaHei-Bold';
          border-right: 1px solid #fff;
          border-top: 1px solid #fff;
        }
        .owner-value {
          width: 290px;
           padding: 9px 10px;
          box-sizing: border-box;
          font-size: 14px;
          line-height: 20px;
          border-right: 1px solid #fff;
          border-top: 1px solid #fff;
        }
        .address {
          flex: 1;
        }
      }
      .owner-border-bottom {
        width: 100%;
        .owner-label, .owner-value {
          border-bottom: 1px solid #fff;
        }
      }
    }
  }
</style>
