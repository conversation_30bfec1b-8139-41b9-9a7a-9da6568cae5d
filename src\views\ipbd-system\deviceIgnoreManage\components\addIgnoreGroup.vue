<template>
  <div class="add-dialog" v-if="isShow">
    <header class="add-header">
      <div style="display: flex">
        <div class="add-header-back" @click="close">
          <i class="iconfont icon-return"></i>
        </div>
        <div class="add-header-info">
          <div class="add-header-text">{{ title }}</div>
        </div>
      </div>
    </header>
    <div class="content">
      <keep-alive>
        <component
          :is="step"
          @stepListener="stepListener"
          :setData="setDatas"
          @close="close"
        ></component>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import firstStep from "./firstStep.vue";
import secondStep from "./secondStep.vue";
import { createSheildTask, getViewResource } from "@/api/config.js";
export default {
  name: "addIgnoreGroup",
  components: {
    firstStep,
    secondStep,
  },
  data() {
    return {
      step: "firstStep",
      title: "",
      id: "",
      setDatas: {},
      isShow: false,
    };
  },
  methods: {
    close() {
      this.isShow = false;
      this.step = "firstStep";
    },
    stepListener(step, { name, timeRange, selectedUsers, selectedDevs } = {}) {
      this.step = step || this.step;
      this.setDatas.name = name || this.setDatas.name;
      this.setDatas.timeRange = timeRange || this.setDatas.timeRange;
      this.setDatas.users = selectedUsers || this.setDatas.users;
      this.setDatas.resources = selectedDevs || this.setDatas.resources;
      if (!step) this.saveIgnoreGroup();
    },
    saveIgnoreGroup() {
      let params = {
        id: this.id || "",
        name: this.setDatas.name,
        userIds: this.setDatas.users.map((item) => item.id),
        resourceIds: this.setDatas.resources.map((item) => item.deviceId),
        createTime: this.$dayjs(this.setDatas.timeRange[0]).valueOf(),
        expiryTime: this.$dayjs(this.setDatas.timeRange[1]).valueOf(),
      };
      createSheildTask(params).then((res) => {
        if (!res) return;
        this.$Message.success("保存成功");
        this.close();
        this.$emit("refresh");
        // 刷新屏蔽设备
        this.$store.dispatch("player/fetchShieldResourcePool");
      });
    },
    async open({ title, id, name, createTime, expiryTime }) {
      this.title = title;
      this.isShow = true;
      this.id = id || "";
      let res = {};
      if (this.id) {
        res = await getViewResource({ groupId: id });
      }
      this.setDatas = {
        name: name || "",
        timeRange: [createTime, expiryTime],
        users: res.data?.users || [],
        resources: res.data?.resources || [],
      };
    },
  },
};
</script>
<style lang="less">
.add-dialog {
  font-size: 14px;
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    .first-step-container {
      height: 100%;
      .add-ignore-wrap {
        height: 100%;
      }
    }
  }
  .add-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    background: #ffffff;
    box-sizing: border-box;
    user-select: none;
    position: relative;
    .add-header-back {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .add-header-info {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
  }
}
</style>
