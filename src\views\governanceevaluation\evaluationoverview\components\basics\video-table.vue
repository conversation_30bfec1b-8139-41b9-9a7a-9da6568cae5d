<template>
  <ui-modal
    class="video-table"
    v-model="visible"
    v-if="visible"
    title="济南市未上报位置类型"
    :styles="styles"
    :footer-hide="true"
  >
    <ui-table class="auto-fill" :table-columns="columnsList" :table-data="tableData"> </ui-table>
  </ui-modal>
</template>

<style lang="less" scoped>
.video-table {
  @{_deep} .ivu-modal {
    width: 520px;
    height: 800px;
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 0 20px 20px;
    }
  }
}
.ui-table {
  padding: 0 10px;
  @{_deep} .ivu-table {
    th,
    td {
      border: 1px solid var(--border-color) !important;
    }
    tr:last-child td {
      border-bottom: none;
    }
    &:before {
      content: '';
      position: absolute;
      background-color: #0d477d !important;
    }
  }
  @{_deep}.ivu-table-overflowX {
    & ~ .ivu-table-fixed {
      height: calc(~'100% - 11px');
      > .ivu-table-fixed-body {
        // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
        height: calc(~'100% - 110px') !important;
      }
    }
  }

  @{_deep}.ivu-table-fixed {
    height: 100%;
    > .ivu-table-fixed-body {
      // 浮动高度应该为表格高度 - 浮动表头高度
      height: calc(~'100% - 110px') !important;
    }
  }
}
@{_deep} .ivu-table-fixed-header {
  .triangle {
    padding: 0;
    .ivu-table-cell {
      padding: 0;
      width: 100%;
      height: 100%;
    }
    .type {
      position: relative;
      background-color: #000;
      width: 100%;
      height: 100%;
      z-index: 0;
      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        clip-path: polygon(100% calc(100% - 0.5px), 100% 0px, 0px -0.5px);
        position: absolute;
        top: 0;
        background-color: #092955;
      }
      &:before {
        content: '';
        display: block;
        width: 100%;
        height: 100%;
        clip-path: polygon(0px 0.5px, 0px 100%, calc(100% - 0.5px) calc(100% + 0.5px));
        position: absolute;
        top: 0;
        background-color: #092955;
      }
      .place {
        position: absolute;
        left: 6px;
        bottom: 15px;
        z-index: 1;
      }
      .orgCode {
        position: absolute;
        right: 6px;
        top: 15px;
        z-index: 1;
      }
    }
  }
}
</style>
<script>
const renderTableHeader = (h) => {
  return h(
    'div',
    {
      attrs: {
        class: 'type',
      },
    },
    [
      h(
        'div',
        {
          attrs: {
            class: 'place',
          },
        },
        '重点场所',
      ),
      h(
        'div',
        {
          attrs: {
            class: 'orgCode',
          },
        },
        '行政区划',
      ),
    ],
  );
};
export default {
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    tag: {},
  },
  data() {
    return {
      tableData: [
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
        {
          orgCode: null,
          orgName: null,
          sxjcjqy: 'A0101',
          sxjcjqyName: '重大群众性集会场所',
          deviceTotal: null,
          videoMonitorCount: null,
          vehicleCount: null,
          faceCount: null,
          cslx: null,
          cslxName: null,
        },
      ],
      columnsList: [
        { type: 'index', width: 55, title: '序号', align: 'center', fixed: 'left' },
        { title: '区域编码', width: 100, key: 'sxjcjqy', align: 'center', fixed: 'left' },
        {
          key: 'sxjcjqyName',
          width: 150,
          className: 'triangle',
          renderHeader: renderTableHeader,
          align: 'center',
          fixed: 'left',
        },
        {
          title: '评测值',
          align: 'center',
          children: [
            {
              key: 'standardValue',
              title: '目标值',
              width: 100,
              align: 'center',
              className: 'standard-value-color',
              slot: 'standardValue',
            },
          ],
        },
      ],
      visible: true,
      styles: { width: '8rem' },
    };
  },
  mounted() {},

  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: { UiTable: require('@/components/ui-table.vue').default },
};
</script>
