<template>
  <div class="map-box">
    <div :id="mapId" class="map"></div>
    <right-button-group @pointMap="pointMap"> </right-button-group>
    <!-- 框选底部操作兰 -->
    <bottom-tool
      v-show="mapLayerConfig.mapToolVisible"
      @cancelDraw="cancelDraw"
      @selectDraw="selectDraw"
      @clearDraw="clearDraw"
      @closeMapTool="closeBottomTool"
      :drawComplete="drawComplete"
    ></bottom-tool>
    <MapDom
      ref="mapDom"
      :mapDomAreaData="chooseMapPosition"
      @close="closeAllInfoWindow"
    ></MapDom>

    <Modal
      ref="deviceDom"
      class-name="domModal"
      v-model="domModalOption.open"
      reset-drag-position
      footer-hide
      draggable
      sticky
      :title="domModalOption.title"
      :width="modelWidth"
      :styles="{ top: 0, width: 'auto' }"
      center
      :mask="false"
      @on-cancel="closeMapDom"
    >
      <component
        :ref="sectionMenuName"
        :is="sectionMenuName"
        :map-dom-data="mapDomData"
        :is-map-click="isMapClick"
        :position-points="positionPoints"
        @preDetial="(Index) => $emit('update:currentClickIndex', Index)"
        @nextDetail="(Index) => $emit('update:currentClickIndex', Index)"
        @close="closeMapDom"
        @changeListTab="changeListTab"
        :clickIndexId="clickIndexId"
        @isModelDetail="changeModelDetail"
      ></component>
    </Modal>

    <!-- 框选结果弹框 -->
    <frame-selection-result
      ref="frameSelectionResult"
      :selectDeviceList="selectDeviceList"
      @openMapDom="openMapDom"
      @closeSelectWindowDom="clearDraw"
      :listShow="searchBtn"
      @updateLayerCheckedNames="updateLayerCheckedNames"
      @goSearch="goSearch"
    ></frame-selection-result>
    <!-- 刻画轨迹 -->
    <div class="draw-track" v-show="mapLayerConfig.tracing">
      <div class="play-operate">
        <ui-icon
          :type="!playbackStatus ? 'yunhang' : 'pause1'"
          :color="'#fff'"
          :size="20"
          title="暂停/播放"
          @click.native="controlDraw"
        ></ui-icon>
        <!-- <ui-icon :type="'step-forward'" :color="'#fff'" :size="20" title="快进" @click.native="fastDraw"></ui-icon> -->
      </div>
      <div class="draw-progress">
        <Progress
          :percent="percent"
          :stroke-color="['#5BA3FF', '#2C86F8']"
          hide-info
        />
        <span>{{ curProgressVal }}</span>
      </div>
      <ul class="speed-list" v-show="showSpeedList">
        <li
          class="speed-item"
          v-for="(item, index) of timesSpeed"
          :key="index"
          :class="{ selected: item.selected }"
          @click="setSpeedFunc(item)"
        >
          {{ item.label }}
        </li>
      </ul>
      <p
        class="times-btn"
        @click="showSpeedList = !showSpeedList"
        :class="{ active: showSpeedList }"
      >
        倍速
      </p>
    </div>
    <!-- 鼠标浮动 -->
    <mouse-title ref="mouseDom" :name="mouseName"></mouse-title>
    <Modal v-model="visible" title="点位详情" center :width="770">
      <div class="content">
        <div class="box">
          <p class="box-title">点位类型:</p>
          <p class="box-content">{{ placeObj.placeTypeName }}</p>
        </div>
        <div class="box">
          <p class="box-title">点位名称:</p>
          <p class="box-content">{{ placeObj.placeName }}</p>
        </div>
        <div class="box-img" v-if="placeObj.placeImage">
          <ui-image :src="placeObj.placeImage" alt="静态库" viewer />
          <!-- <img v-lazy="placeObj.placeImage" alt="" style="width: 560px; height:400px;"/> -->
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
import RightButtonGroup from "./right-button-group.vue";
import MapDom from "./map-dom-area.vue";
import face from "@/views/operations-on-the-map/map-default-page/components/map-dom/face-map-dom.vue";
import vehicle from "@/views/operations-on-the-map/map-default-page/components/map-dom/vehicle-map-dom.vue";
import humanbody from "@/views/operations-on-the-map/map-default-page/components/map-dom/humanbody-map-dom.vue";
import nonmotorVehicle from "@/views/operations-on-the-map/map-default-page/components/map-dom/nonmotorVehicle-map-dom.vue";
import wifi from "@/views/operations-on-the-map/map-default-page/components/map-dom/wifi-map-dom.vue";
import point from "@/views/operations-on-the-map/map-default-page/components/map-dom/point-map-dom.vue";
import electric from "@/views/operations-on-the-map/map-default-page/components/map-dom/electric-map-dom.vue";
import rfid from "@/views/operations-on-the-map/map-default-page/components/map-dom/rfid-map-dom.vue";
import device from "@/views/operations-on-the-map/map-default-page/components/map-dom/device-map-dom.vue";
import BottomTool from "./map-tool.vue";
import FrameSelectionResult from "./frame-selection-result.vue";
import mouseTitle from "./search-center-position-title.vue";
import { LayerType, siteType } from "@/map/core/enum/LayerType.js";
import { speedList } from "./data.js";
let mapMain = null;
let _arealayer = null;
const mouseInfoWindow = [];
let selectWindowArr = [];
export default {
  components: {
    RightButtonGroup,
    face,
    vehicle,
    humanbody,
    nonmotorVehicle,
    wifi,
    point,
    electric,
    rfid,
    device,
    BottomTool,
    FrameSelectionResult,
    MapDom,
    mouseTitle,
  },
  props: {
    // 图层点位信息
    allCameraList: {
      type: Array,
      default: () => [],
    },
    // 图层点位信息(场所)
    siteList: {
      type: Array,
      default: () => [],
    },
    // 地图图层配置信息
    mapLayerConfig: {
      type: Object,
      default: () => {
        return {
          tracing: false, // 是否需要刻画轨迹
          showStartPoint: false, // 是否显示起点终点图标
          mapToolVisible: false, // 框选操作栏
          selectionResult: true, // 是否显示框选结果弹框
          resultOrderIndex: false, // 搜索结果排序,
          showLatestLocation: false, // 显示地图最新位置
        };
      },
    },
    // 当前点击的轨迹节点
    currentClickIndex: {
      type: Number,
      default: 0,
    },
    // 是否禁止地图的滚动条事件
    disableScroll: {
      type: Boolean,
      default: false,
    },
    // 切换类型 加载弹框
    sectionName: {
      type: String,
      default: "",
    },
    // 档案-轨迹
    positionPoints: {
      type: Array,
      default: () => [],
    },
    // 所有点位上图作战记录
    layerManageMap: {
      type: Object,
      default: () => {},
    },
    // 点位上图-选中的图层
    chooseLayerNameList: {
      type: [],
      default: () => [],
    },
    // 热力图数据
    heatData: {
      type: [],
      default: () => [],
    },
    // 档案位置信息
    archivesPositionPoints: {
      type: Array,
      default: () => [],
    },
    // 最新位置
    chooseMapPosition: {
      type: Object,
      default: () => {},
    },
    // 资源图层-选中的图层名称
    layerCheckedNames: {
      type: Array,
      default: () => [],
    },
    cutIcon: {
      type: String,
      default: "",
    },
    idlerWheel: {
      type: Boolean,
      default: false,
    },
    searchBtn: {
      type: Boolean,
      default: true,
    },
    // 框选自定义展示的列表类型
    crashType: {
      type: Object,
      default: () => {
        return {
          Camera: true,
          Camera_Vehicle: true,
          Camera_Face: true,
          Camera_RFID: true,
          Camera_Wifi: true,
          Camera_Electric: true,
          Place_Hotel: true,
          Place_InterBar: true,
          Place_Government: true,
          Place_School: true,
          Place_Key: true,
        };
      },
    },
    // 图片序列化解析设备 地图定位(此设备地图定位不受点位重叠影响)
    jiexiPosition:{
      type: Boolean,
      default: false,
    },
  },
  watch: {
    // 设备
    allCameraList: {
      handler(val) {
        if (val.length) {
          this._initSystemPoints2Map(val);
        }
      },
    },
    // 场所
    siteList: {
      handler(val) {
        if (val.length) {
          this._initSystemPoints3Map(val);
          // 从视频应用跳转，打开播放弹框
          this.openDomFromVideo();
        }
      },
    },
    chooseMapPosition: {
      handler(val) {
        if (val) {
          setTimeout(() => {
            // this.latestLocationHandler(val)
          }, 1000);
        }
      },
      immediate: true,
    },
    // 普通搜索-列表点击的index
    currentClickIndex: {
      handler(newVal) {
        if (newVal == -1) {
          return;
        }
        if (this.points && this.points.length) {
          this.points = this.points.map((e, i) => {
            return {
              ...e,
              active: i === newVal ? true : false,
            };
          });
        }
        const { currentClickIndex, points = [], sectionMenuName } = this;
        const pointItem = (points.length && points[currentClickIndex]) || {};
        // 缺 经纬度 地图不展示点位
        if (
          !pointItem.geoPoint ||
          !pointItem.geoPoint.lon ||
          !pointItem.geoPoint.lat
        ) {
          return this.$Message.warning("经纬度信息不全");
        }
        this.sectionMenuName = this.sectionName + "";
        if (pointItem.placeTypeCode) {
          this.visible = true;
          this.placeObj = pointItem;
          return;
        }
        // 普通搜索弹出模态框
        this.$nextTick(() => {
          if (pointItem) {
            this.selectItem(pointItem, this.sectionName, null, true);
          }
          // 活动轨迹下方切换
          let cutIcon = this.cutIcon == "track" ? false : true;
          pointItem &&
            this.$refs[this.sectionMenuName]
              .init(points[currentClickIndex], pointItem, "", cutIcon, true)
              .then((res) => {
                if (res.data) {
                } else {
                  this.$Message.warning("暂无数据");
                }
              });
        });
        this.closeAllInfoWindow();
        if (this.mapLayerConfig.resultOrderIndex) {
          this.resetMarker();
        }
        // 人体和非机动车 recordId 人脸和车辆 id
        if (this.sectionName == "face" || this.sectionName == "vehicle") {
          this.clickIndexId = this.points[this.currentClickIndex].id;
        } else {
          this.clickIndexId = this.points[this.currentClickIndex].recordId;
        }
        this.sprinkleHandler(points);
      },
    },
    sectionName: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.sectionMenuName = newVal + "";
        }
      },
      immediate: true,
    },
    positionPoints: {
      handler(newVal) {
        if (newVal.length && !this.fightModel) {
          newVal.map((item, index) => {
            item.Index = index + 1;
          });
          this.points = [...newVal];
          this.closeAllInfoWindow(); //关闭弹窗
          this.resetMarker(); //重置
          this.sprinkleHandler(this.points); //撒点

          setTimeout(() => {
            this.rePosition(this.points);
          }, 200);
        } else {
          this.points = [];
          this.closeAllInfoWindow(); //关闭弹窗
          this.resetMarker(); //重置
        }
      },
      immediate: true,
    },
    // 点位上图
    layerManageMap: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length) {
          this.layerManageHandel(newVal);
        }
      },
      immediate: true,
      deep: true,
    },
    chooseLayerNameList: {
      handler(newVal) {
        this.allOverlayLayerNames.forEach((e) => {
          newVal.includes(e)
            ? mapMain.map.getLayerByName(e).show()
            : mapMain.map.getLayerByName(e).hide();
        });
        this.closeAllInfoWindow(); //关闭弹窗
      },
    },
    // 选中的图层
    layerCheckedNames: {
      handler(list) {
        if (mapMain) {
          this.$nextTick(() => {
            this.layerTypeList.forEach((e) => {
              mapMain.map
                .getLayerByName("聚合图层")
                .setMakrerTypeVisiable(
                  e,
                  list.filter((item) => item).includes(e)
                );
            });
            this.closeAllInfoWindow(); //关闭弹窗
          });
        }
      },
      immediate: true,
    },
    // 档案-位置信息
    archivesPositionPoints: {
      handler(list) {
        this.positionInformationLayer(list);
      },
      immediate: true,
    },
    // 档案-热力图
    heatData: {
      handler(list) {
        this.renderHeatMap(list, 20);
      },
      immediate: true,
    },
    "domModalOption.open": {
      handler(val) {
        if (val) {
          if (this.clickedMarket) {
            this.clickedMarket.ext.clicked = true;
            this.clickedMarket.changeStyle(
              {
                externalGraphic:
                  LayerType[this.clickedMarket.markType].hoverUrl,
              },
              true
            );
          }
        } else {
          if (this.clickedMarket) {
            this.clickedMarket.ext.clicked = false;
            this.clickedMarket.changeStyle(
              {
                externalGraphic: LayerType[this.clickedMarket.markType].url,
              },
              true
            );
          }
        }
      },
      immediate: true,
    },
  },
  computed: {
    // 图上点位真实距离
    calcDistance() {
      let totalDistance = 0;
      const { points } = this;
      if (!points || points.length === 0) {
        return false;
      }
      for (let i = 0, len = points.length; i < len - 1; i++) {
        if (!mapMain) return [];
        // 返回两点之间的距离，单位是米 （start Object 起点 end Object 终点）
        totalDistance += mapMain.map.getDistance(
          { lat: points[i].lat, lon: points[i].lon },
          { lat: points[i + 1].lat, lon: points[i + 1].lon }
        );
      }
      return totalDistance;
    },
    // 图层名称数组
    layerTypeList() {
      let list = [];
      for (let key in LayerType) {
        list.push(LayerType[key].value);
      }
      for (let key in siteType) {
        list.push(siteType[key].value);
      }
      return list;
    },
    modelWidth() {
      let offset = this.isShowModelDetail ? 220 : 0;
      return this.domModalOption.width + offset;
    },
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
      resourceCoverage: "map/getResourceCoverage",
    }),
    // 进度
    curProgressVal() {
      return Math.floor(this.percent) + "%";
    },
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      mapDomData: {},
      positionsList: [],
      position: {},
      traceLayer: null, // 运动轨迹
      dotimg: null, //列表选择撒点
      fightDotimg: {}, //作战记录撒点
      fightOvericon: {},
      animationLine: null, // 动画
      animationArrow: null, //箭头动画
      trailStyle: {
        color: "#EA4A36",
        opacity: "",
        weight: 4,
      },
      drawStyle: {
        color: "#2C86F8", //颜色
        fillColor: "#2C86F8", //填充颜色
        weight: 3, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
        lineStyle: "#2C86F8",
        strokeColor: "#2C86F8",
      },
      selectDeviceList: [], // 框选设备集合
      selectPoints: [], // 单个框选图形位置集合
      selectPositionList: [], //框选位置
      drawComplete: false, //框选是否结束
      sectionMenuName: "", // 当前选中的menuName
      fightModel: false, // 用来判断作战记录模态框显示不重新撒点
      clicktype: "", //点击的类型 搜索，作战，资源
      fightType: "face", //作战记录下某种类型 ，用于弹窗上重新撒点
      // 不同类型弹框 偏移量设置
      offsetMaps: {
        face: {
          offsetWidth: -430,
          offsetHeight: -390,
          pointLon: -0.000665,
          pointLat: 0.0755,
        },
        vehicle: {
          offsetWidth: -430,
          offsetHeight: -390,
          pointLon: -0.00065,
          pointLat: 0.0755,
        },
        wifi: {
          offsetWidth: -254,
          offsetHeight: -133,
          pointLon: 0,
          pointLat: 0.07,
        },
        point: {
          offsetWidth: -254,
          offsetHeight: -200,
          pointLon: 0,
          pointLat: 0,
        },
        electric: {
          offsetWidth: -254,
          offsetHeight: -133,
          pointLon: 0,
          pointLat: 0.07,
        },
        rfid: {
          offsetWidth: -254,
          offsetHeight: -133,
          pointLon: 0,
          pointLat: 0.07,
        },
        device: {
          offsetWidth: -286,
          offsetHeight: -390,
          pointLon: 0,
          pointLat: 0.058,
        },
      },
      // 播放轨迹倍速
      timesSpeed: [
        { label: "0.5X", value: 0.5, selected: false },
        { label: "1.0X", value: 1, selected: true },
        { label: "1.25X", value: 1.25, selected: false },
        { label: "1.5X", value: 1.5, selected: false },
        { label: "2.0X", value: 2, selected: false },
      ],
      playbackStatus: false, // 播放状态
      showSpeedList: false, //
      ispause: false, //用于判断第几次播放
      percent: 0, //进度条的百分比
      timerProgress: null,
      currentTimes: 1, //当前播放倍速
      markers: [], // 运动轨迹图层
      allOverlayLayerNames: [],
      isMapClick: false,
      trackMarker: null, //轨迹的marker
      lineIndex: 1, //在哪条线上运动
      trackList: [],
      iconIndex: 0,
      headerMarker: [],
      arrowsCoverage: null,
      clickIndexId: 0,
      speedNum: 5,
      visible: false,
      placeObj: {},
      mouseName: "",
      domModalOption: {
        open: false,
        title: "设备详情",
        width: "950px",
      },
      isShowModelDetail: false,
    };
  },
  deactivated() {
    this.closeAllInfoWindow();
  },
  async mounted() {
    await this.getMapConfig();
    this.loading = false;
    if (!this.idlerWheel) {
      this.mapidlerWheel(); //防止地图与滚动条 滚轮冲突
    }
  },
  methods: {
    openDomFromVideo() {
      // 从视频应用跳转，打开播放弹框
      let _this = this;
      if (this.$route.query.deviceId) {
        setTimeout(() => {
          let item = _this.siteList.find(
            (i) => i.deviceGbId && i.deviceGbId == _this.$route.query.deviceId
          );
          if (item) {
            let markerItem = mapMain.convertSystemPoint2MapPoint(item);
            const { Lat: lat, Lon: lon } = markerItem;
            // 打开弹窗并切换到视频播放窗口
            _this.openMapDom({ ...markerItem, lat, lon });
          }
        }, 1500);
      }
    },
    // 删除作战记录图层
    delelayer(data, type = false) {
      if (type) {
        let list = [...new Set(this.allOverlayLayerNames)];
        list.forEach((item) => {
          this.fightDotimg[item].removeAllOverlays();
          this.fightOvericon[item].removeAllOverlays();
        });
      } else {
        this.fightDotimg[data.name].removeAllOverlays();
        this.fightOvericon[item].removeAllOverlays();
      }
    },
    mapidlerWheel() {
      this.$nextTick(() => {
        let box = document.querySelector(".map");
        box.onmousewheel = (event) => {
          event = event || window.event;
          // if (event.wheelDelta > 0 || event.detail < 0) {
          // 	box.style.height = box.clientHeight
          // } else {
          // 	box.style.height = box.clientHeight
          // }
          box.style.height = box.clientHeight;
          //取消火狐浏览器默认行为（因为是用addEventListener,所以必须用此方法来取消）
          event.preventDefault && event.preventDefault();
          //取消浏览器默认行为
          return false;
        };
        //为火狐浏览器绑定鼠标
        this.bind(box, "DOMMouseScroll", box.onmousewheel);
      });
    },
    bind(obj, eventStr, callback) {
      if (obj.addEventListener) {
        obj.addEventListener(eventStr, callback, false);
      } else {
        obj.attachEvent("on" + eventStr, function () {
          callback.call(obj);
        });
      }
    },
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain.init(mapId, data, style);
        // 禁止滚动条
        if (this.disableScroll) {
          mapMain.map.disableScrollWheelZoom();
        }
        // 档案位置信息
        this.positionInformationLayer(this.archivesPositionPoints);
        // 档案详情轨迹撒点
        this.sprinkleHandler(this.points);
        // 热力图
        this.renderHeatMap(this.heatData, 20);
        // 测试搜周边
        // setTimeout(() => {
        //   this.addCircleSearch()
        // }, 0)
        //比例尺控件
        var ctrl = new NPMapLib.Controls.ScaleControl();
        mapMain.map.addControl(ctrl);
        this.configDefaultMap();
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    // 配置最大层级
    addlimitLayerNum() {
      if (!!Number(this.mapObj.maxNumberOfLayer) || 600) {
        let allLayers = mapMain.map.getAllLayers.length;
        if (allLayers >= this.limitLayerNum) {
          this.$Message.error("已超过配置最大图层数量");
          return false;
        }
      }
      return true;
    },
    // 加载点位到地图上(资源图层/设备数据)
    _initSystemPoints2Map(points) {
      this.$nextTick(() => {
        setTimeout(() => {
          // 加载点位
          // mapMain.renderMarkers(mapMain.convertSystemPointArr2MapPoint(points,'1213'), this.getMapEvents())
        }, 1000);
      });
    },
    // 加载点位到地图上(资源图层/场所数据)
    _initSystemPoints3Map(points) {
      this.$nextTick(() => {
        setTimeout(() => {
          // 加载点位
          mapMain.renderMarkers(
            mapMain.convertSystemPointArr3MapPoint(points),
            this.getMapEvents()
          );
        }, 1000);
      });
    },
    // 点击 设备
    getMapEvents() {
      const opts = {
        click: (marker) => {
          const {
            ext: { Lat: lat, Lon: lon },
          } = marker;
          if (this.clickedMarket) {
            this.clickedMarket.ext.clicked = false;
            this.clickedMarket.changeStyle(
              {
                externalGraphic: LayerType[this.clickedMarket.markType].url,
              },
              true
            );
          }
          marker.ext.clicked = true;
          marker.changeStyle(
            {
              externalGraphic: LayerType[marker.markType].hoverUrl,
            },
            true
          );
          this.clickedMarket = marker;
          this.openMapDom({ ...marker.ext, lat, lon });
        },
        mouseover: (marker) => {
          marker.changeStyle(
            {
              externalGraphic: LayerType[marker.markType].hoverUrl,
            },
            true
          );
          this.showMouseDom(marker);
        },
        mouseout: (marker) => {
          if (!marker.ext.clicked) {
            // 判断是否为在线状态
            if (marker.ext.Status == "0") {
              marker.changeStyle(
                {
                  externalGraphic: LayerType[marker.markType].url,
                },
                true
              );
            } else {
              marker.changeStyle(
                {
                  externalGraphic:
                    LayerType[marker.markType].offLineUrl ||
                    LayerType[marker.markType].url,
                },
                true
              );
            }
          }
          this.closeMouseInfoWindow();
        },
      };
      return opts;
    },
    closeMouseInfoWindow() {
      mouseInfoWindow.forEach((row) => {
        row.close();
      });
    },
    // 关闭框选弹框
    closeSelectWindowDom() {
      selectWindowArr.forEach((row) => {
        row.close();
      });
    },
    // 关闭多个弹框
    closeAllInfoWindow() {
      this.domModalOption.open = false;
    },
    //关闭普通搜索-弹框
    closeMapDom() {
      if (this.sectionMenuName == "device") {
        this.$refs[this.sectionMenuName].stopVideo();
      }
      this.closeAllInfoWindow();
      this.$emit("chooseMapItem", -1);
    },
    preDetial(Index) {
      this.$emit("update:currentClickIndex", Index);
    },
    nextDetail(Index) {
      this.$emit("update:currentClickIndex", Index);
    },
    // 鼠标浮动在资源图层图标上
    showMouseDom(marker) {
      var Lat = marker._position.geoPoint.lat;
      var Lon = marker._position.geoPoint.lon;
      this.mouseName =
        marker._position.placeName || marker._position.deviceName;
      const point = new NPMapLib.Geometry.Point(Lon, Lat);
      this.$nextTick(() => {
        const dom = this.$refs["mouseDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (60 / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 20;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/face.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        mouseInfoWindow.push(infoWindow);
      });
    },
    moveModal() {
      let zoomLat = 0.0005;
      let zoom = mapMain.map.getMaxZoom();
      // 获取不同弹框位置偏移量
      if (zoom == 19) {
        zoomLat = 0.0001;
      } else if (zoom == 18) {
        zoomLat = 0.0002;
      } else if (zoom == 17) {
        zoomLat = 0.0005;
      }
      return zoomLat;
    },
    // 点击图层展示弹框,isMapClick 地图上点击不展示左右切换详情
    selectItem(
      pointItem,
      sectionName,
      isMapClick = false,
      isAprink = false,
      distance = false
    ) {
      if (isAprink) {
        // 点击列表后 切换地图显示图片
        this.sprinkleHandler([pointItem]);
      }
      pointItem.currentClickIndex = this.currentClickIndex;
      this.isMapClick = isMapClick;
      this.closeAllInfoWindow();
      this.sectionMenuName = !!sectionName ? sectionName : sectionMenuName;
      // 显示之前先清除其他提示框
      const { sectionMenuName } = this;
      // const { pointLon, pointLat, offsetWidth, offsetHeight } = this.offsetMaps[sectionMenuName]
      // const point = new NPMapLib.Geometry.Point(+pointItem.lon + pointLon, +pointItem.lat + pointLat)
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        pointItem.lat || pointItem.geoPoint.lat
      );
      // 获取图层，计算弹框向下偏移量
      let zoomLat = this.moveModal();
      //mapMain.map.setCenter(new NPMapLib.Geometry.Point(pointItem.lon || pointItem.geoPoint.lon, (pointItem.lat || pointItem.geoPoint.lat) + 0.0025))
      mapMain.map.centerAndZoom(
        new NPMapLib.Geometry.Point(
          pointItem.lon || pointItem.geoPoint.lon,
          (pointItem.lat || pointItem.geoPoint.lat) + zoomLat
        ),
        mapMain.map.getMaxZoom()
      );
      this.mapDomData = this.allCameraList[0];

      let obj = this.handleOffset();
      let title =
        this.sectionMenuName == "device" && pointItem.deviceName
          ? pointItem.deviceName
          : obj.title;
      this.domModalOption.title = title;
      this.domModalOption.width = obj.width;
      this.domModalOption.open = true;
      // 修改弹框位置
      this.$nextTick(() => {
        let dragDom = this.$refs.deviceDom.$el.querySelector(
          ".ivu-modal-content-drag"
        );
        if (dragDom) {
          let left = window.innerWidth / 2 - dragDom.offsetWidth / 2;
          let top = window.innerHeight / 2 - dragDom.offsetHeight;
          dragDom.style.left = left + "px";
          dragDom.style.top = (top < 90 ? 90 : top) + "px";
        }
      });
    },
    handleOffset() {
      // 地图弹框
      const stragetyOffset = {
        face: { title: "抓拍详情", width: 842, height: 560 },
        humanbody: { title: "抓拍详情", width: 842, height: 560 },
        nonmotorVehicle: { title: "抓拍详情", width: 842, height: 560 },
        vehicle: { title: "抓拍详情", width: 842, height: 560 },
        device: { title: "设备详情", width: 532, height: 372 },
        default: { title: "详情", width: 490, height: 335 },
      };
      let obj =
        this.sectionMenuName in stragetyOffset
          ? stragetyOffset[this.sectionMenuName]
          : stragetyOffset.default;
      return obj;
    },
    // 最新位置 视频档案、设备
    latestLocationHandler(pointItem) {
      if (!this.mapLayerConfig.showLatestLocation) return;
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        (pointItem.lat || pointItem.geoPoint.lat) + 0.00085
      );
      mapMain.map.centerAndZoom(point, mapMain.map.getMaxZoom());
      const opts = {
        width: 50, // 信息窗宽度
        height: 180, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(-128, -80), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      pointItem.lon = pointItem.lon ? pointItem.lon : pointItem.geoPoint.lon;
      pointItem.lat = pointItem.lat ? pointItem.lat : pointItem.geoPoint.lat;
      let marker = new NPMapLib.Symbols.Marker(pointItem);
      let size = new NPMapLib.Geometry.Size(40, 40);
      let imgUrl = require(`@/assets/img/map/position-map.png`);
      let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
      icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height));
      marker.setIcon(icon);
      mapMain.map.addOverlay(marker);
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      const dom = this.$refs.mapDom.$el;
      infoWindow.setContentDom(dom);
      mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
    },
    // 当前设备弹框信息内容
    selectPoint() {
      this.configDefaultMap();
    },
    // 地图左右导航
    pointMap(type) {
      this[`${type}Map`]();
    },
    homingMap() {
      this.selectPoint();
    },
    // 固定放大地图
    enlargeMap() {
      mapMain.map.zoomInFixed();
    },
    // 固定缩小地图
    narrowMap() {
      mapMain.map.zoomOutFixed();
    },
    // 计算区域弹框位置
    calculationLocation(drawType, points) {
      if (drawType === "selectPolygon") {
        return points.reduce((prev, current) =>
          prev.lat < current.lat
            ? prev
            : current && prev.lon > current.lon
            ? prev
            : current
        );
      } else if (drawType === "selectRectangle") {
        return points.reduce((prev, current) =>
          prev.lat > current.lat
            ? prev
            : current && prev.lon > current.lon
            ? prev
            : current
        );
      }
    },
    // 重置marker
    resetMarker() {
      // this.$nextTick(( )=> {
      if (mapMain) {
        mapMain.map.removeOverlays(this.markers);
        // 分页换切换tab 去除旧的点位
        mapMain.map.removeOverlay(this.dotimg);
        mapMain.map.removeOverlay(this.traceLayer);
        if (this.dotimg) {
          this.dotimg.removeAllOverlays();
        }
        if (this.traceLayer) {
          this.traceLayer.removeAllOverlays();
        }
        this.dotimg = null;
        this.traceLayer = null;
      }
      // })
      this.closeAllInfoWindow(); //关闭弹窗
    },
    resetSpeed() {
      this.playbackStatus = false; //变为未播放状态
      if (this.animationLine) {
        this.animationLine.remove();
      }
      if (this.headerMarker[this.iconIndex]) {
        this.headerMarker[this.iconIndex].hide();
        this.headerMarker = new Array(this.trackList.length).fill(null);
      }
      if (this.arrowsCoverage) {
        this.arrowsCoverage.removeAllOverlays();
      }
      this.percent = 0;
      this.iconIndex = 0;
    },
    //全息档案-位置信息
    positionInformationLayer(points) {
      if (!points || points.length === 0) {
        return false;
      }
      points.forEach((item, index) => {
        let temp = {};
        let k = item.lon + "_" + item.lat;
        temp[k] == null ? (temp[k] = 0) : temp[k]++;
        let imgUrl = "",
          label = null,
          icon = null;
        let size = new NPMapLib.Geometry.Size(28, 28);
        let marker = new NPMapLib.Symbols.Marker(item);
        if (item.type) {
          size = new NPMapLib.Geometry.Size(30, 34);
          imgUrl = item.markerIconUrl;
          icon = new NPMapLib.Symbols.Icon(imgUrl, size);
          icon.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
          );
          marker.setIcon(icon);
          marker.k = k;
          mapMain.map.addOverlay(marker);
        } else {
          imgUrl = require(`@/assets/img/map/trajectory-red.png`);
          icon = new NPMapLib.Symbols.Icon(imgUrl, size);
          icon.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
          );
          label = new NPMapLib.Symbols.Label(`${index + 1}`);
          label.setStyle({
            fontSize: 14, //文字大小
            fontFamily: "宋体", //字体
            color: !item.active ? "#EA4A36" : "#2C86F8", //文字前景色
            align: "center", //对方方式
            isBold: true, //是否粗体
          });
          label.setOffset(new NPMapLib.Geometry.Size(-0.5, 19.5));
          marker.setLabel(label);
          marker.setIcon(icon);
          marker.k = k;
          mapMain.map.addOverlay(marker);
        }
      });
    },
    // 作战记录初始化渲染图层
    layerManageHandel(val) {
      for (const [key, value] of Object.entries(val)) {
        let temp = {};
        let imgUrl = null,
          imgUrlMore = null,
          iconMore = null,
          icon = null;
        // imgUrl = require(`@/assets/img/map/layer-${key}.png`);
        imgUrl = require(`@/assets/img/map/cographPT_${key}.png`); //
        imgUrlMore = require(`@/assets/img/map/cographPTmore_${key}.png`);
        let size = new NPMapLib.Geometry.Size(40, 48);
        icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        iconMore = new NPMapLib.Symbols.Icon(imgUrlMore, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        iconMore.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        value.forEach((e, index) => {
          const { position, isChecked } = e;
          if (position && position.length) {
            if (!this.addlimitLayerNum) return;
            let markers = [],
              overlayLayer = null;
            this.fightDotimg[e.name] = new NPMapLib.Layers.OverlayLayer(
              e.name,
              false
            );
            this.fightOvericon[e.name] = new NPMapLib.Layers.OverlayLayer(
              e.name,
              false
            );
            // let dotimg = new NPMapLib.Layers.OverlayLayer(e.name, false);
            // let overicon = new NPMapLib.Layers.OverlayLayer(e.name, false);
            // 判断某坐标是否多个
            let bothcoord = this.repetition(position);
            let multipleIcon = {};
            position.forEach((item, ind) => {
              let k = item.lon + "_" + item.lat;
              temp[k] == null ? (temp[k] = 0) : temp[k]++;
              let marker = new NPMapLib.Symbols.Marker(item);
              marker.k = k;
              // 用来判断当前点位上是否有多个图片
              if (multipleIcon[k]) {
                multipleIcon[k] = multipleIcon[k] + 1;
              } else {
                multipleIcon[k] = 1;
              }
              if (
                key == "vehicle" ||
                key == "face" ||
                key == "humanbody" ||
                key == "nonmotorVehicle"
              ) {
                // !multipleIcon.includes(k)
                if (multipleIcon[k] == 1) {
                  let markerBg = new NPMapLib.Symbols.Marker(item);
                  let photoUrl = position[ind].traitImg;
                  let imgSize = new NPMapLib.Geometry.Size(24, 25);
                  let iconbg = new NPMapLib.Symbols.Icon(photoUrl, imgSize);
                  iconbg.setAnchor(
                    new NPMapLib.Geometry.Size(
                      -size.width / 2 + 8,
                      -size.height + 6
                    )
                  );
                  markerBg.setIcon(iconbg);
                  mapMain.map.addLayer(this.fightDotimg[e.name]);
                  this.fightDotimg[e.name].addOverlay(markerBg);
                  // mapMain.map.addLayer(dotimg)
                  // dotimg.addOverlay(markerBg);
                }
              }
              if (!bothcoord.both[k] && multipleIcon[k] == 1) {
                marker.setIcon(icon);
                // markers.push(marker);
                mapMain.map.addLayer(this.fightOvericon[e.name]);
                this.fightOvericon[e.name].addOverlay(marker);
                // mapMain.map.addLayer(overicon);
                //向图层中添加覆盖物
                // overicon.addOverlay(marker);
              } else if (multipleIcon[k] >= 2) {
                if (bothcoord.single[k] == multipleIcon[k]) {
                  marker.setIcon(iconMore);
                  // markers.push(marker);
                  mapMain.map.addLayer(this.fightOvericon[e.name]);
                  this.fightOvericon[e.name].addOverlay(marker);
                  // mapMain.map.addLayer(overicon);
                  //向图层中添加覆盖物
                  // overicon.addOverlay(marker);
                }
              }
              this.fightDotimg[e.name].setZIndex(600);
              this.fightOvericon[e.name].setZIndex(600);
              // dotimg.setZIndex(600)
              // overicon.setZIndex(600)
              marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
                this.clicktype = "fight"; //
                this.fightType = key; //作战记录下某种类型
                this.selectItem(item, key);
                this.sectionMenuName = key + "";
                this.$nextTick(() => {
                  this.fightModel = true;
                  this.$emit("update:positionPoints", [...position]);
                  this.$refs[this.sectionMenuName]
                    .init(item, item, e.name, false, [...position])
                    .then((res) => {
                      if (res.data) {
                        this.selectItem(item, key, null, null);
                        this.fightModel = false;
                      } else {
                        this.$Message.warning("暂无数据");
                        this.fightModel = false;
                      }
                    });
                });
              });
            });
            // 每一个checkbox 就是一个图层
            overlayLayer = new NPMapLib.Layers.OverlayLayer(e.name, false);
            this.allOverlayLayerNames.push(e.name);
            mapMain.map.addLayer(overlayLayer);
            //向图层中添加覆盖物
            overlayLayer.addOverlays(markers);
            console.log(e.name, "e.name");
            // console.log(dotimg.getOverlay(), 'overlayLayer')
            // console.log(dotimg.getOverlaysByProperty('人像点位1'))
            // console.log(dotimg.getOverlaysArry())
            if (isChecked) {
              overlayLayer.show();
              this.fightDotimg[e.name].show();
              this.fightOvericon[e.name].show();
              // dotimg.show();
              // overicon.show();
            } else {
              overlayLayer.hide();
              this.fightDotimg[e.name].hide();
              this.fightOvericon[e.name].hide();
              // dotimg.hide();
              // overicon.hide();
            }
          }
        });
      }
    },
    changeModelDetail(val) {
      this.isShowModelDetail = val;
    },
    // 模态框上方tab切换后重新渲染
    changeListTab(item) {
      this.$emit("update:currentClickIndex", item.Index - 1);
      if (this.clicktype == "fight") {
        let objs = {};
        this.$set(objs, this.fightType, [
          {
            isChecked: true,
            name: item.name,
            position: [item],
          },
        ]);
        this.layerManageHandel(objs);
      } else {
        this.sprinkleHandler([item], true);
      }
    },
    // 普通搜索和轨迹撒点
    sprinkleHandler(points, listTab = false) {
      if (!points || points.length === 0) {
        return false;
      }
      // 判断某坐标是否多个
      let bothcoord = this.repetition(points);
      let markers = [];
      let temp = {};
      let multipleIcon = {};
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        let k =
          (item.lon || item.geoPoint.lon) +
          "_" +
          (item.lat || item.geoPoint.lat);
        temp[k] == null ? (temp[k] = 0) : temp[k]++;
        let imgUrl = "",
          label = null,
          icon = null,
          iconbg = null,
          size = null;
        if (item.iconType) {
          //人像or车辆
          size = new NPMapLib.Geometry.Size(40, 40);
        } else {
          size = new NPMapLib.Geometry.Size(35, 35);
        }
        item.lon = item.lon ? item.lon : item.geoPoint.lon;
        item.lat = item.lat ? item.lat : item.geoPoint.lat;
        let marker = new NPMapLib.Symbols.Marker(item);
        // 用来判断当前点位上是否有多个图片
        if (multipleIcon[k]) {
          multipleIcon[k] = multipleIcon[k] + 1;
        } else {
          multipleIcon[k] = 1;
        }
        if (this.mapLayerConfig.resultOrderIndex) {
          // 文本标记
          label = new NPMapLib.Symbols.Label(
            `${item.Index ? item.Index : index + 1}`
          );
          // 多数同一点、 一点一数
          this.dotimg = new NPMapLib.Layers.OverlayLayer("dynamicDotImg");
          if (item.iconType) {
            // 人像or车辆
            imgUrl = item.traitImg;
            label.setOffset(new NPMapLib.Geometry.Size(-13, 30));
            label.setStyle({
              fontSize: 12, //文字大小
              fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
              color: "#fff", //文字前景色
              align: "cm", //对方方式
              isBold: true, //是否粗体
            });
          } else {
            imgUrl = require(`@/assets/img/map/trajectory-${
              item.active ? "blue" : "red"
            }.png`);
            label.setOffset(new NPMapLib.Geometry.Size(-0.5, 23));
            label.setStyle({
              fontSize: 14, //文字大小
              fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
              color: !item.active ? "#EA4A36" : "#2C86F8", //文字前景色
              align: "cm", //对方方式
              isBold: true, //是否粗体
            });
            marker.setLabel(label);
          }
          // 设置图片
          icon = new NPMapLib.Symbols.Icon(imgUrl, size);
          icon.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
          );
          marker.setIcon(icon);
          marker.setLabel(label);
          marker.k = k;
          mapMain.map.addLayer(this.dotimg);
          this.dotimg.addOverlay(marker);
          // marker.flash()
          // marker._layer.setZIndex(501)
          let markerBg = new NPMapLib.Symbols.Marker(item);
          if (item.iconType) {
            if (!bothcoord.both[k] && multipleIcon[k] == 1) {
              //单个
              let imgSize = new NPMapLib.Geometry.Size(44, 52);
              let imgTitle = listTab ? "blue" : item.active ? "blue" : "unRed";
              imgUrl = require(`@/assets/img/map/point_${imgTitle}.png`);
              iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize);
              iconbg.setAnchor(
                new NPMapLib.Geometry.Size(
                  -size.width / 2 - 2,
                  -size.height - 2
                )
              );
              label.setOffset(new NPMapLib.Geometry.Size(-13, 30));
              markerBg.setIcon(iconbg);
              markerBg.setLabel(label);
              mapMain.map.addLayer(this.dotimg);
              this.dotimg.addOverlay(markerBg);
              // markerBg.flash()
            } else if (multipleIcon[k] >= 2) {
              //多个
              if (bothcoord.single[k] == multipleIcon[k]) {
                imgUrl = require(`@/assets/img/map/morePoint_${
                  item.active ? "blue" : "unRed"
                }.png`);
                let moreSize = new NPMapLib.Geometry.Size(52, 56);
                iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize);
                iconbg.setAnchor(
                  new NPMapLib.Geometry.Size(
                    -size.width / 2 - 7,
                    -size.height - 6
                  )
                );
                label.setOffset(new NPMapLib.Geometry.Size(-14, 30));
                if (multipleIcon[k] >= 2) {
                  multipleIcon[k] = multipleIcon[k] + 1;
                }
                markerBg.setIcon(iconbg);
                markerBg.setLabel(label);
                mapMain.map.addLayer(this.dotimg);
                this.dotimg.addOverlay(markerBg);
                // markerBg.flash()
              }
            }
          }
          this.dotimg.setZIndex(600);
          markerBg.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
            this.clicktype = "search";
            if (this.points[index].placeTypeCode) {
              //场所
              this.visible = true;
              this.placeObj = this.points[index];
              return;
            } else {
              this.selectItem(this.points[index], this.sectionName, true, true);
            }
            this.$emit("chooseMapItem", index);
          });
          marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, () => {
            this.clicktype = "search";
            if (this.points[index].placeTypeCode) {
              //场所
              this.visible = true;
              this.placeObj = this.points[index];
              return;
            } else {
              this.selectItem(this.points[index], this.sectionName, true, true);
            }
            this.$emit("chooseMapItem", index);
          });
          markers.push(marker);
        } else {
          //人像档案，车辆档案
          imgUrl = item.traitImg;
          let recordSize = new NPMapLib.Geometry.Size(40, 40);
          // imgUrl = require(`@/assets/img/map/${item.dataType == 0 ? 'face' : item.dataType == 1 ? 'vehicle' : 'imsi' }${item.active ? '-active' : ''}.png`)
          icon = new NPMapLib.Symbols.Icon(imgUrl, recordSize);
          icon.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
          );
          marker.setIcon(icon);
          this.traceLayer = new NPMapLib.Layers.OverlayLayer("trail");
          if (!this.addlimitLayerNum) return;
          if (mapMain) {
            mapMain.map.addLayer(this.traceLayer);
          }
          this.traceLayer && this.traceLayer.addOverlay(marker);
          let markerBg = new NPMapLib.Symbols.Marker(item);
          console.log(this.jiexiPosition)
          if ( (this.jiexiPosition || !bothcoord.both[k]) && (this.jiexiPosition || multipleIcon[k] == 1)) {
            //单个
            if (item.active == "red-position") {
              let imgSize = new NPMapLib.Geometry.Size(30, 30);
              imgUrl = require(`@/assets/img/map/${item.active}.png`);
              iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize);
              iconbg.setAnchor(
                new NPMapLib.Geometry.Size(
                  -size.width / 2 - 2,
                  -size.height - 2
                )
              );
              markerBg.setIcon(iconbg);
            } else {
              let imgSize = new NPMapLib.Geometry.Size(44, 52);
              let imgTitle = listTab ? "blue" : item.active ? "blue" : "unRed";
              imgUrl = require(`@/assets/img/map/record_${imgTitle}.png`);
              iconbg = new NPMapLib.Symbols.Icon(imgUrl, imgSize);
              iconbg.setAnchor(
                new NPMapLib.Geometry.Size(
                  -size.width / 2 - 2,
                  -size.height - 2
                )
              );
              markerBg.setIcon(iconbg);
            }
            if (mapMain) {
              mapMain.map.addLayer(this.traceLayer);
              this.showMouseDom(marker);
            }
            this.traceLayer.addOverlay(markerBg);
            // markerBg.flash()
          } else if (multipleIcon[k] >= 2) {
            //多个
            if (bothcoord.single[k] == multipleIcon[k]) {
              imgUrl = require(`@/assets/img/map/moreRecord_${
                item.active ? "blue" : "unRed"
              }.png`);
              let moreSize = new NPMapLib.Geometry.Size(52, 56);
              iconbg = new NPMapLib.Symbols.Icon(imgUrl, moreSize);
              iconbg.setAnchor(
                new NPMapLib.Geometry.Size(
                  -size.width / 2 - 7,
                  -size.height - 6
                )
              );
              if (multipleIcon[k] >= 2) {
                multipleIcon[k] = multipleIcon[k] + 1;
              }
              markerBg.setIcon(iconbg);
              if (mapMain) {
                mapMain.map.addLayer(this.traceLayer);
              }
              this.traceLayer.addOverlay(markerBg);
              // markerBg.flash()
            }
          }

          markers.push(marker);
        }
      }

      this.markers = markers;
    },
    // 用于对背景图渲染做判断
    repetition(item) {
      let single = {}; //计算每个出现的次数
      let both = {}; //有重复的数据
      item.forEach((item, index) => {
        let k =
          (item.lon || item.geoPoint.lon) +
          "_" +
          (item.lat || item.geoPoint.lat);
        if (!single[k]) {
          single[k] = 1;
        } else {
          single[k] = single[k] += 1;
        }
        if (single[k] > 1) {
          both[k] = true;
        }
      });
      return { single, both };
    },
    // 根据经纬度信息展示轨迹
    _drawTrail(points) {
      if (this.animationLine) {
        //重新播放，删除已存在数据
        if (this.headerMarker[this.iconIndex]) {
          this.headerMarker[this.iconIndex].setIcon("");
        }
        this.animationLine.remove();
        if (this.arrowsCoverage) {
          this.arrowsCoverage.removeAllOverlays();
        }
      }
      let rating = mapMain.map.getZoom();
      this.speedNum = speedList[rating];
      this.moveTrack(points);
    },
    // 定点图片运动
    moveTrack(points) {
      let objKey = "";
      this.trackList = []; // 获取线路
      points.map((item, index) => {
        let k = item.lon + "_" + item.lat;
        if (objKey) {
          if (objKey === k) {
          } else {
            this.trackList.push(item);
            objKey = k;
          }
        } else {
          this.trackList.push(item);
          objKey = k;
        }
      });
      this.headerMarker = new Array(this.trackList.length).fill(null);
      this.moveAnimation();
    },
    moveAnimation() {
      if (this.trackList.length < 2) {
        this.resetSpeed();
        this.$Message.warning("暂无路线");
        return;
      }
      var layer = new NPMapLib.Layers.OverlayLayer("xasas");
      mapMain.map.addLayer(layer);
      //点序列
      var trackDot = [
        new NPMapLib.Geometry.Point(
          this.trackList[this.iconIndex].lon,
          this.trackList[this.iconIndex].lat
        ),
        new NPMapLib.Geometry.Point(
          this.trackList[this.iconIndex + 1].lon,
          this.trackList[this.iconIndex + 1].lat
        ),
      ];

      var offset = new NPMapLib.Geometry.Size(0, -12);
      this.headerMarker[this.iconIndex] = new NPMapLib.Symbols.Marker(
        trackDot[0],
        { offset: offset }
      );

      var icon = new NPMapLib.Symbols.Icon(
        this.trackList[this.iconIndex].traitImg,
        new NPMapLib.Geometry.Size(29, 29)
      );
      this.headerMarker[this.iconIndex].setIcon(icon);
      layer.addOverlay(this.headerMarker[this.iconIndex]);

      if (this.headerMarker[this.iconIndex]) {
        let color = "#EA4A36";
        let dataType = this.trackList[this.iconIndex].dataType;
        if (dataType == 0) {
          color = "#EA4A36";
        } else if (dataType == 1) {
          color = "#1FAF81";
        } else {
          color = "#F29F4C";
        }
        // if(this.iconIndex%2 == 0){
        //     color = '#EA4A36'
        // }else{
        //     color = '#1FAF81'
        // }
        this.animationLine = new NPMapLib.Symbols.AnimationLine(
          mapMain.map.id,
          trackDot,
          {
            headerMarker: this.headerMarker[this.iconIndex],
            isHeaderRotate: false, //控制是否headerMarker随线旋转方向
            color: color,
            opacity: 0.8,
            weight: 4,
          }
        );
      } else {
        this.animationLine = new NPMapLib.Symbols.AnimationLine(
          mapMain.map.id,
          trackDot,
          {
            color: color,
            opacity: 0.8,
            weight: 4,
          }
        );
      }
      console.log(this.speedNum * this.currentTimes, "速度");
      this.animationLine.setSpeed(this.speedNum * this.currentTimes);
      this.animationLine.start();
      // 两点之间进度条前进步伐
      let processStep = 100 / (this.trackList.length - 1);
      let fn = () => {
        let c_nums = this.animationLine.segArray.length; // 两点间行进的坐标点个数
        if (c_nums) {
          this.percent = processStep / c_nums + this.percent;
        }
      };
      this.animationLine.events.register("afterStep", fn);
      this.animationLine.events.register("preDraw", (evt) => {
        if (evt.index == 1) {
          this.animationLine.events.unregister("afterStep", fn);
          if (this.iconIndex + 2 < this.trackList.length) {
            this.faddArrow(trackDot, 15, Math.PI / 7);
            layer.removeAllOverlays();
            // console.log(this.headerMarker[this.iconIndex].getZIndex())
            // this.headerMarker[this.iconIndex].setZIndex(100)
            this.headerMarker[this.iconIndex] = null;
            this.iconIndex += 1;
            this.moveAnimation();
          } else {
            this.percent = 100;
            layer.removeAllOverlays();
            this.headerMarker[this.iconIndex] = null;
            this.playbackStatus = false;
            this.ispause = false;
            this.iconIndex = 0;
          }
        }
      });
    },
    /*
     * 在线的中点处画箭头
     * @param {Object} polyline 地图上的折线
     * @param {Object} length   代表箭头长度
     * @param {Object} angleValue 箭头和主线的夹角
     */
    faddArrow(polyline, length, angleValue) {
      var linePoint = polyline; //线的坐标串
      var arrowCount = linePoint.length;
      for (var i = 1; i < arrowCount; i++) {
        var pixelStart = mapMain.map.pointToPixel(linePoint[i - 1]);
        var pixelEnd = mapMain.map.pointToPixel(linePoint[i]);
        var angle = angleValue; //箭头和主线的夹角
        var r = length; // r/Math.sin(angle)代表箭头长度
        var delta = 0; //主线斜率，垂直时无斜率
        var param = 0; //代码简洁考虑
        var pixelTemX, pixelTemY, poMiddleX, poMiddleY; //临时点坐标
        var pixelX, pixelY, pixelX1, pixelY1; //箭头两个点
        poMiddleX = (pixelEnd.x + pixelStart.x) / 2;
        poMiddleY = (pixelEnd.y + pixelStart.y) / 2;
        if (poMiddleX - pixelStart.x == 0) {
          //斜率不存在时
          pixelTemX = poMiddleX;
          if (poMiddleY > pixelStart.y) {
            pixelTemY = poMiddleY - r;
          } else {
            pixelTemY = poMiddleY + r;
          }
          //已知直角三角形两个点坐标及其中一个角，求另外一个点坐标算法
          pixelX = pixelTemX - r * Math.tan(angle);
          pixelX1 = pixelTemX + r * Math.tan(angle);
          pixelY = pixelY1 = pixelTemY;
        } else {
          //斜率存在时
          delta = (poMiddleY - pixelStart.y) / (poMiddleX - pixelStart.x);
          param = Math.sqrt(delta * delta + 1);
          if (poMiddleX - pixelStart.x < 0) {
            //第二、三象限
            pixelTemX = poMiddleX + r / param;
            pixelTemY = poMiddleY + (delta * r) / param;
          } //第一、四象限
          else {
            pixelTemX = poMiddleX - r / param;
            pixelTemY = poMiddleY - (delta * r) / param;
          }
          //已知直角三角形两个点坐标及其中一个角，求另外一个点坐标算法
          pixelX = pixelTemX + (Math.tan(angle) * r * delta) / param;
          pixelY = pixelTemY - (Math.tan(angle) * r) / param;
          pixelX1 = pixelTemX - (Math.tan(angle) * r * delta) / param;
          pixelY1 = pixelTemY + (Math.tan(angle) * r) / param;
        }
        var pointArrow = mapMain.map.pixelToPoint(
          new NPMapLib.Geometry.Pixel(pixelX, pixelY)
        );
        var pointArrow1 = mapMain.map.pixelToPoint(
          new NPMapLib.Geometry.Pixel(pixelX1, pixelY1)
        );
        var pointMiddle = mapMain.map.pixelToPoint(
          new NPMapLib.Geometry.Pixel(poMiddleX, poMiddleY)
        );

        let color = "#EA4A36";
        let dataType = this.trackList[this.iconIndex].dataType;
        if (dataType == 0) {
          color = "#EA4A36";
        } else if (dataType == 1) {
          color = "#1FAF81";
        } else {
          color = "#F29F4C";
        }
        // if(this.iconIndex%2 == 0){
        //     color = '#EA4A36'
        // }else{
        //     color = '#1FAF81'
        // }
        //多段线
        var Arrow = new NPMapLib.Geometry.Polyline(
          [pointArrow1, pointMiddle, pointArrow],
          {
            color: color, //颜色
            weight: 3, //宽度，以像素为单位
            opacity: 0.5, //透明度，取值范围0 - 1
            // lineStyle: NPMapLib.LINE_TYPE_SOLID //样式
          }
        );

        this.arrowsCoverage = new NPMapLib.Layers.OverlayLayer(
          "arrowsCoverage"
        );
        mapMain.map.addLayer(this.arrowsCoverage);
        this.arrowsCoverage.addOverlay(Arrow);
      }
    },

    //清除轨迹
    clearTraceAnalyze() {
      if (this.traceLayer) {
        this.traceLayer.removeAllOverlays();
        mapMain.map.removeOverlay(this.traceLayer);
        this.traceLayer = null;
      }
      /*清除当前轨迹的信息窗 */
      if (this.infoWindow) {
        this.infoWindow.close();
        this.infoWindow = null;
      }
    },
    //轨迹回到可视区域
    rePosition(arrList) {
      let minLat, minLon, maxLon, maxLat, sortLat, sortLon;
      if (!arrList || arrList.length === 0) {
        return false;
      }
      sortLat = arrList.map((e) => e.lat);
      sortLon = arrList.map((e) => e.lon);
      minLat = Math.min(...sortLat);
      maxLat = Math.max(...sortLat);
      minLon = Math.min(...sortLon);
      maxLon = Math.max(...sortLon);
      let extent = new NPMapLib.Geometry.Extent(minLon, minLat, maxLon, maxLat);
      if (mapMain) {
        mapMain.zoomToExtend(extent);
      }
    },
    // 渲染热力图
    renderHeatMap(dataList, radius) {
      if (!dataList || dataList.length === 0) {
        return false;
      }
      let opt = {
        isBaseLayer: false,
        opacity: 1.0,
        projection: "EPSG:900913",
        visible: true,
        radius: radius,
        name: "heatLayer",
      };
      // 只存在一个 热力图
      let heatLayer = null;
      if (this.heatMapLayer) {
        heatLayer = this.heatMapLayer;
      } else {
        heatLayer = new NPMapLib.Layers.HeatMapLayer("heatLayer", opt);
      }
      // max 数据的 获取
      let countMax = 0;
      let countMin = 0;
      let currentCount = 0;
      let _dataList = dataList.map((val) => {
        currentCount = val.numCount || 1;
        countMax = Math.max(countMax, currentCount);
        countMin = Math.min(countMin, currentCount);
        return {
          lat: val.lat,
          lon: val.lon,
          count: currentCount,
        };
      });
      let dataset = {
        max: countMax,
        min: countMin,
        data: _dataList,
      };
      if (!this.heatMapLayer) {
        if (!this.addlimitLayerNum) return;
        mapMain.map.addLayers([heatLayer]);
      }
      heatLayer.setDataset(dataset);
      this.heatMapLayer = heatLayer;
    },
    // 底部操作栏
    // 取消框选-地图可移动
    cancelDraw() {
      mapMain.cancelDraw();
    },
    // 框选绘制图形
    selectDraw(drawType) {
      this.clearDraw();
      // drawType:drawPolygon,drawRectangle,drawCircle
      this.drawComplete = false;
      mapMain[drawType](
        (points, position, extent) => {
          // 初次款框选点位
          // 初次款框选点位(根据不同类型获取相对应设备)
          //  视频 => 高清视频 => Camera
          //  人脸 => 人脸卡口 => Camera_Face
          //  车辆 => 车辆卡口 => Camera_Vehicle
          //  wifi => wifi设备 => Camera_Wifi
          //  rfid => RFID设备 => Camera_RFID
          //  电围 => 点位设备 => Camera_Electric
          this.selectDeviceList = points.filter((item) => {
            if (this.crashType[item.LayerType]) {
              return item;
            }
          });
          //   this.selectDeviceList = [...points]
          const { bottom, left, right, top } = extent;
          // 组装框选数组数据，方便后面点击选中
          let selectPointsItem = {
            bottom,
            left,
            right,
            top,
            points: position.points,
            center: position.center,
            devicePoints: points,
          };
          if (this.selectPoints.length > 0) {
            this.selectPoints.forEach((e) => {
              if (
                (e.bottom !== bottom && e.left !== left && e.right !== right,
                e.top !== top)
              ) {
                this.selectPoints.push(selectPointsItem);
              }
            });
          } else {
            this.selectPoints.push(selectPointsItem);
          }
          // 绘制图形完成
          this.drawComplete = true;
          if (drawType == "selectCircle") {
            const {
              ne: { lat, lon },
            } = extent;
            // 打开弹框
            this.selectionResultItem(
              {
                lat,
                lon,
              },
              position.center
            );
          } else {
            const positionItem = this.calculationLocation(
              drawType,
              selectPointsItem.points
            );
            this.selectionResultItem(positionItem, position.center);
          }
          // // 搜索工具-框选后-清除
          // if (!this.mapLayerConfig.selectionResult) {
          //   setTimeout(() => {
          //     this.$emit('selectionResult', points)
          //     this.closeBottomTool()
          //   }, 1000)
          // }
          if (!this.searchBtn) {
            this.$emit("selectlist", this.selectDeviceList);
          }
        },
        (extent, position) => {
          let selectPointsItem = {};
          const { bottom, left, right, top } = extent;
          this.selectPoints.forEach((e) => {
            if (
              (e.bottom == bottom && e.left == left && e.right == right,
              e.top == top)
            ) {
              selectPointsItem = {
                ...e,
              };
            }
          });
          if (drawType !== "selectCircle") {
            const positionItem = this.calculationLocation(
              drawType,
              selectPointsItem.points
            );
            this.selectionResultItem(positionItem, selectPointsItem.center);
          } else {
            // 圆形取回东西经纬度
            const {
              ne: { lat, lon },
            } = extent;
            // 打开弹框
            this.selectionResultItem(
              {
                lat,
                lon,
              },
              selectPointsItem.center
            );
          }
          this.selectDeviceList = selectPointsItem.devicePoints.filter(
            (item) => {
              if (this.crashType[item.LayerType]) {
                return item;
              }
            }
          );
          //   this.selectDeviceList = [...selectPointsItem.devicePoints]
        },
        false,
        { ...this.drawStyle }
      );
    },
    cancelMeasure() {
      mapMain.cancelMeasure();
    },
    clearDraw() {
      mapMain.clearDraw();
      mapMain.showHeatMap();
      mapMain.removeRectangle();
      // 如果摄像头不显示，清除框选之后要清除
      // this.showCamera ? null : this.removeMarkers(this.selectedCameraList);
      this.closeAllInfoWindow();
      this.closeSelectWindowDom();
      this.position = {};
      this.$emit("clear");
    },
    // 关闭 底部操作栏
    closeBottomTool() {
      this.clearDraw();
      this.$emit("closeMapTool");
    },
    // 测量距离
    measuringDistance() {
      mapMain.measuringDistance();
    },
    // 框选弹框
    selectionResultItem(pointItem, centerPoint) {
      if (!this.mapLayerConfig.selectionResult) {
        return false;
      }
      this.closeSelectWindowDom();
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        pointItem.lat || pointItem.geoPoint.lat
      );
      // mapMain.map.centerAndZoom(point, 17)
      mapMain.map.setCenter(centerPoint);
      const opts = {
        width: 50, // 信息窗宽度
        height: 180, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(-0, -0), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      this.mapDomData = this.allCameraList[0];
      const dom = this.$refs.frameSelectionResult.$el;
      infoWindow.setContentDom(dom);
      mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      selectWindowArr.push(infoWindow);
    },
    // 播放完重新播放
    restart() {
      // 进度条随着轨迹运动逐渐向前进，当从100 - 0时需要去除动画
      if (this.percent == 100) {
        // 表示播完了重新播放
        // 去掉从100 - 0的动画
        document.querySelector(".ivu-progress-bg").style.transition = "none";
        setTimeout(() => {
          // 恢复从0 - 100的动画
          document.querySelector(".ivu-progress-bg").style.transition =
            "all 0.2s";
        }, 0);
      }
      this.percent = 0;
    },
    // 播放、暂停
    controlDraw() {
      this.playbackStatus = !this.playbackStatus;
      if (this.playbackStatus) {
        if (!this.ispause) {
          console.log("第一次播放 || 播完后的重新开始播放");
          //开始播放并且不是中途暂停的开始播放（即第一次播放）
          this.restart();
          this._drawTrail(this.points);
          //   this.trailProgress()
        } else {
          //中途续播
          console.log("中途续播");
          this.animationLine.start();
          //   this.trailProgress(100, this.percent)
        }
      } else {
        //中途暂停
        console.log("中途暂停");
        this.ispause = true;
        this.animationLine.stop();
        // clearInterval(this.timerProgress)
      }
    },
    //播放控件速度选择
    setSpeedFunc(item) {
      this.timesSpeed.forEach((row) => {
        row.selected = false;
        if (item.value === row.value) {
          row.selected = true;
          this.currentTimes = row.value;
          this.playbackStatus = true;
          //   console.log(this.percent > 0 && this.percent < 100, this.percent);
          if (this.percent > 0 && this.percent < 100) {
            //中途换速
            console.log("中途换速*" + this.currentTimes + "倍");
            this.animationLine.stop();
            this.animationLine.setSpeed(100 * this.currentTimes);
            this.animationLine.start();
            // this.trailProgress(100 * this.currentTimes, this.percent)
          } else {
            //一开始就换速或该次播完后换速重新播
            console.log("一开始就换速或该次播完后换速重新播");
            this.restart();
            this._drawTrail(this.points);
            // this.trailProgress(100 * this.currentTimes)
          }
        }
      });
    },
    fastDraw() {
      if (this.percent >= 0 && this.percent < 100) {
        this.animationLine.pause();
        this.animationLine.setSpeed(1000);
        this.animationLine.start();
        this.trailProgress(5000, this.percent);
      }
    },
    //监测轨迹运动的进度条
    trailProgress(step = 100, breakpoint = 0) {
      let minInterval = this.calcDistance / step,
        currentWidth = 0; //动画轨迹实时走的距离
      if (!!this.timerProgress) {
        clearInterval(this.timerProgress);
        this.timerProgress = null;
        // this.percent = 0;
      }
      this.timerProgress = setInterval(() => {
        if (minInterval <= 2 || this.percent >= 100) {
          //播放轨迹完成（要让播放开关自动关闭，并且是否是手动暂停开关置为否）
          console.log("该次轨迹播放已完成", this.timerProgress);
          this.percent = 100;
          clearInterval(this.timerProgress);
          this.timerProgress = null;
          this.playbackStatus = false;
          this.ispause = false;
          currentWidth = this.calcDistance;
          this.animationLine.setSpeed(10000);
        } else {
          this.percent = breakpoint;
          currentWidth += step;
          this.percent += Math.round((currentWidth / this.calcDistance) * 100);
        }
      }, 10);
    },
    // 搜周边
    addCircleSearch() {
      let center = mapMain.map.getCenter();
      let layer = new NPMapLib.Layers.OverlayLayer("circleSearch");
      if (!this.addlimitLayerNum) return;
      mapMain.map.addLayer(layer);
      let radius = 5000;
      //圆形
      let circle = new NPMapLib.Geometry.Circle(center, radius, {
        color: "yellow", //颜色
        fillColor: "blue", //填充颜色
        weight: 1, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        fillOpacity: 0.3, //填充的透明度，取值范围0 - 1
      });
      mapMain.map.addOverlay(circle);
      //图片大小
      let size = new NPMapLib.Geometry.Size(76, 24);
      //图片
      let imgUrl = require(`@/assets/img/map/editCircle.png`);
      let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
      let marker = null;
      //图像标记
      marker = new NPMapLib.Symbols.Marker(center);
      marker.setIcon(icon);
      marker.setOffset(new NPMapLib.Geometry.Size(20, 0));
      let label = new NPMapLib.Symbols.Label("5000米", {
        offset: new NPMapLib.Geometry.Size(-5, 13),
      });
      //设置样式
      label.setStyle({
        fontSize: 12, //文字大小
        fontFamily: "宋体", //字体
        align: "left", //对方方式
      });
      marker.setLabel(label);
      //设置偏移量
      let p = circle.getCenter();
      p.lon =
        p.lon +
        new NPMapLib.GisToolKit().getDistanceByProjection(2000, mapMain.map);
      marker.setPosition(p);
      marker.isEnableEdit = true;
      mapMain.map.addOverlay(marker);
      let isDown = false,
        isOver = false,
        isEdit = false;
      marker.addEventListener("featuremousedown", () => {
        isDown = true;
        isOver = true;
      });
      marker.addEventListener("mouseover", () => {
        if (!isEdit) {
          mapMain.map.enableEditing();
          isEdit = true;
        }
        isOver = true;
      });
      marker.addEventListener("mouseout", () => {
        if (!isDown) {
          if (isEdit) {
            mapMain.map.disableEditing();
            isEdit = false;
          }
        }
        isOver = false;
      });
      marker.addEventListener("draging", (marker) => {
        let p = circle.getCenter();
        let r = marker.getPosition().lon - circle.getCenter().lon;
        r = new NPMapLib.GisToolKit().getPlatDistanceByProjection(
          r,
          mapMain.map
        );
        if (r < 500) {
          p.lon =
            p.lon +
            new NPMapLib.GisToolKit().getDistanceByProjection(500, mapMain.map);
          r = 500;
        } else if (r > 5000) {
          p.lon =
            p.lon +
            new NPMapLib.GisToolKit().getDistanceByProjection(
              5000,
              mapMain.map
            );
          r = 5000;
        } else {
          p.lon = marker.getPosition().lon;
        }
        let label = marker.getLabel();
        label.setContent(Math.round(r) + "米");
        marker.setLabel(label);
        circle.setRadius(r);
        circle.refresh();
        marker.setPosition(p);
        marker.refresh();
      });
      marker.addEventListener("dragend", (marker) => {
        if (!isOver) {
          if (isEdit) {
            mapMain.map.disableEditing();
            isEdit = false;
          }
        }
        isDown = false;
      });

      mapMain.map.disableEditing();
    },
    //更新图层
    updateLayerCheckedNames(layerCheckedNames) {
      this.$emit("updateLayerCheckedNames", layerCheckedNames);
    },
    // 框选打开弹框(资源图层)
    openMapDom({
      deviceId,
      deviceGbId,
      deviceName,
      detailAddress,
      lat,
      lon,
      deviceType,
      picUrl,
      imageUrls,
      myFavorite,
      placeName,
      placeTypeName,
    }) {
      this.sectionMenuName = "device";
      this.domModalOption.open = false;
      // if(this.resourceCoverage) { //用于普通搜索后不可点击
      //     return
      // }
      if (this.mapLayerConfig.mapToolVisible) {
        //框选时不可点击
        return;
      }
      if (!deviceId) {
        this.visible = true;
        this.placeObj = {
          placeName,
          placeTypeName,
        };
        return;
      }
      // 设置显示弹框
      this.$nextTick(() => {
        this.$refs["device"]
          .init(
            { deviceId, deviceGbId, modelType: "1" },
            {
              geoPoint: {
                lat,
                lon,
              },
              deviceName,
              deviceId,
              deviceGbId,
              detailAddress,
              deviceType,
              picUrl,
              imageUrls,
              myFavorite,
            }
          )
          .then((res) => {
            if (res.data) {
              this.selectItem(
                { deviceName, lat, lon },
                "device",
                true,
                null,
                true
              );
            } else {
              this.$Message.warning("暂无数据");
            }
          });
      });
    },
    goSearch(isMerge) {
      this.$emit("goSearch", this.selectDeviceList, isMerge);
      this.clearDraw();
    },
    // 行政区划
    showAreaOnMap(result) {
      if (!_arealayer) {
        _arealayer = new NPMapLib.Layers.OverlayLayer("area-layer");
        mapMain.map.addLayer(_arealayer);
        _arealayer.setZIndex && _arealayer.setZIndex(300);
      }
      if (result) {
        mapMain.map.clearOverlays();
        _arealayer.removeAllOverlays();
        _arealayer.show();
        result.setStyle({
          lineStyle: NPMapLib.LINE_TYPE_DASH,
          color: "#00B7EE", //颜色
          fillColor: "#00B7EE",
          weight: 2, //宽度，以像素为单位
          opacity: 1, //透明度，取值范围0 - 1
          fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
        });
        _arealayer.addOverlay(result);
        mapMain.map.zoomToExtent(result.getExtent());
      }
    },
    hideAreaOnMap() {
      if (_arealayer) {
        _arealayer.hide();
      }
      mapMain.map.reset();
    },
  },
  beforeDestroy() {
    if (!!this.timerProgress) {
      this.animationLine.pause();
      clearInterval(this.timerProgress);
      this.timerProgress = null;
    }
    if (mapMain) {
      mapMain.destroy();
      mapMain = null;
    }
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }

  .draw-track {
    position: absolute;
    z-index: 500;
    left: 20px;
    right: 54px;
    bottom: 20px;
    width: 1456px;
    height: 48px;
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .play-operate {
      .iconfont {
        margin-right: 16px;
      }
    }

    .draw-progress {
      //   width: 1300px;
      width: calc(~"100% - 120px");
      display: flex;
      span {
        color: #fff;
        width: 40px;
        padding-left: 10px;
      }
      /deep/.ivu-progress {
        overflow: hidden;
        margin-top: -2px;
        &-text {
          .ivu-icon {
            font-size: 16px;
          }
        }

        &-bg {
          height: 8px !important;
          border-radius: 4px;
          color: #fff;
          position: relative;
        }

        &-bg:before {
          content: "";
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background: #ffffff;
          position: absolute;
          top: -3px;
          right: 0;
        }
      }
    }

    .speed-list {
      position: absolute;
      right: 0;
      bottom: 48px;
      padding: 10px 0;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      border-radius: 5px;

      .speed-item {
        padding: 10px 20px;
        text-align: center;
        cursor: pointer;

        &.selected {
          color: #2c86f8;
        }
      }
    }

    .times-btn {
      // width:43px;
      border-radius: 10px;
      background-color: #fff;
      padding: 2px 10px;
      // font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      cursor: pointer;
      &.active {
        background-color: #2c86f8;
        color: #fff;
      }
    }
  }
}
.content {
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
  .box {
    display: flex;
    width: 50%;
    .box-title {
      margin-left: 100px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 16px;
    }
    .box-content {
      font-size: 16px;
      margin-left: 10px;
    }
  }
  .box-img {
    width: 560px;
    height: 400px;
  }
}
/deep/ #npgis_GroupDiv {
  overflow: inherit !important;
}
/deep/.olPopupContent {
  overflow: inherit !important;
}
/deep/.domModal {
  .ivu-modal-body {
    padding: 0 !important;
  }
}
/deep/ .ivu-modal-close {
  top: 5px;
  .ivu-icon-ios-close {
    color: #fff;
  }
}
/deep/ .ivu-modal-header {
  background: rgb(44, 134, 248);
  border-bottom: none;
  padding: 10px;
  .ivu-modal-header-inner {
    font-size: 14px;
    font-weight: 700;
    color: #fff;
  }
}
</style>
