<template>
  <div class="statistical-results auto-fill">
    <statistical-List
      ref="StatisticalList"
      :tableColumns="tableColumns"
      :result-data="resultData"
      @startSearch="startSearch"
      @onSortChange="onSortChange"
    >
      >
      <Button slot="export" type="primary" class="button-export mb-sm" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu font-white mr-xs"></i>
        <span class="ml-xs">导出</span>
      </Button>
      <template #qualified="{ row }">
        <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
          {{ row.qualified === '1' ? '达标' : '不达标' }}
        </span>
      </template>
      <template #action="{ row, item }">
        <div class="action">
          <slot name="customBtn" :row="row" :btn-info="item">
            <ui-btn-tip
              v-for="(btnItem, btnIndex) in item.btnArr"
              :key="btnIndex"
              class="mr-sm"
              :icon="btnItem.icon"
              :content="btnItem.text"
              @handleClick="$emit(btnItem.emitFun, row)"
            ></ui-btn-tip>
          </slot>
        </div>
      </template>
    </statistical-List>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import downLoadTips from '@/mixins/download-tips';
import tableColumns from '../../common-pages/statistical-results/util/tableColumns.js';
import detectionResult from '@/config/api/detectionResult';
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  name: 'statistical-results',
  mixins: [dealWatch, downLoadTips],
  data() {
    return {
      exportLoading: false,
      tableColumns: [],
      resultData: [],
      searchData: {},
      paramsList: {},
      codeKey: '',
      dataType: '',
      sortValue: {},
    };
  },
  created() {
    this.paramsList = this.$route.query;
    this.tableColumns = tableColumns(this.paramsList)[this.paramsList.indexType];
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    // 获取统计列表数据
    async getStatisticalList() {
      try {
        this.$refs.StatisticalList.loading = true;
        let params = {
          // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          ...this.sortValue,
        };
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.resultData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.$refs.StatisticalList.loading = false;
      }
    },
    startSearch(searchParams) {
      this.$set(this, 'searchData', searchParams);
      this.getStatisticalList();
    },
    onSortChange(obj) {
      let { order, key } = obj;
      let val = order.toUpperCase();
      if (val !== 'NORMAL') {
        this.sortValue = {
          sortField: key === 'civilName' ? 'civil_code' : key === 'orgName' ? 'org_code' : key,
          sort: val,
        };
      } else {
        this.sortValue = {};
      }
      this.getStatisticalList(true);
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(detectionResult.exportStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    handleViewDetail(row) {
      this.$emit('viewDetail', row);
    },
    async getStatInfo() {
      try {
        const params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          // access: "REPORT_MODE",
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
          // sortField: "ACTUAL_NUM"
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        this.dataType = data.dataType;
        if (this.dataType !== '1') {
          this.tableColumns = this.tableColumns.filter((item) => {
            return item.key !== 'areaCount' && item.key !== 'qualifiedAreaCount' && item.slot !== 'action';
          });
        } else {
          this.tableColumns = tableColumns(this.paramsList)[this.paramsList.indexType];
        }
      } catch (e) {
        console.log(e);
      }
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.getStatInfo();
      this.$refs.StatisticalList.init();
    },
  },
  components: {
    StatisticalList: require('../../ui-pages/statistical-list/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-results {
  padding: 10px 12px 0;
}
</style>
