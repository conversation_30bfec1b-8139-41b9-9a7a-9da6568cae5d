import ScreenLayout from '@/layouts/screen-layout';
// const dataScreen = 'views/large-screen/data-screen';
// const deviceScreen = 'views/large-screen/device-screen';
// const analysisScreen = 'views/large-screen/analysis-screen';
// const constructionScreen = 'views/large-screen/construction-screen';

export default [
    {
        path: '/large-screen',
        name: 'large-screen',
        component: ScreenLayout,
        redirect: '/large-screen/data-screen',
        children: [
            {
                path: '/large-screen/data-screen',
                name: 'data-screen',
                    permission: 'addceshi',
                component: (resolve) => import('@/views/large-screen/data-screen/index.vue'),
                meta: {
                    title: '数据专题大屏'
                }
            },
            {
                path: '/large-screen/device-screen',
                name: 'device-screen',
                    permission: 'addceshi',
                component: (resolve) => import('@/views/large-screen/device-screen/index.vue'),
                meta: {
                    title: '设备专题大屏'
                }
            },
            {
                path: '/large-screen/analysis-screen',
                name: 'analysis-screen',
                    permission: 'addceshi',
                component: (resolve) => import('@/views/large-screen/analysis-screen/index.vue'),
                meta: {
                    title: '解析专题大屏'
                }
            },
            {
                path: '/large-screen/construction-screen',
                name: 'construction-screen',
                    permission: 'addceshi', 
                component: (resolve) => import('@/views/large-screen/construction-screen/index.vue'),
                meta: {
                    title: '建设态势大屏'
                }
            },
        ]
    }
]
// import BasicLayout from '@/layouts/basic-layout'
// export default [
//     {
//         path: 'test',
//         name: 'test',
//         component: BasicLayout,
//         children: [
//             {
//                 path: '/test_created',
//                 name: 'test_created',
//                 tabShow: true,
//                 parentName: 'holographic-archives',
//                 permission: 'addceshi',
//                 component: (resolve) => require(['@/views/test/index.vue'], resolve),
//                 meta: {
//                     title: '测试新tabs页'
//                 }
//             }, 
//         ]
//     }
// ]