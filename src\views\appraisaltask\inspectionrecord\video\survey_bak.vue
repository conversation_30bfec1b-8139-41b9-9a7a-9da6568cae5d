<template>
  <div class="auto-fill">
    <div class="search-container">
      <div class="jump">
        <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
      </div>
      <div class="search">
        <!-- <ui-label :label="global.filedEnum.sbgnlx" :width="70" class="ml-lg fl">
          <Select
              clearable
              class="width-md"
              v-model="formValidate.sbgnlx"
              placeholder="请选择摄像机功能类型"
              multiple
              :max-tag-count="1">
            <Option
                v-for="(sbgnlxItem, bdindex) in dictData[ 'sxjgnlx_receive' ]"
                :key="'sbgnlx' + bdindex"
                :value="sbgnlxItem.dataKey" >{{ sbgnlxItem.dataValue }}
            </Option>
          </Select>
        </ui-label> -->
        <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="ml-lg fl">
          <Select
            clearable
            class="width-md"
            v-model="formValidate.sbdwlx"
            multiple
            :max-tag-count="1"
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          >
            <Option
              v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
              :key="'sbdwlx' + bdindex"
              :value="sbdwlxItem.dataKey"
              >{{ sbdwlxItem.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <Button type="primary" class="ml-lg" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetForm">重置</Button>
      </div>
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #orgCode="{ row }">
        <span v-if="currentOrgCode == row.orgCode.split('-')[0]">{{ row.orgCode.split('-')[1] }}</span>
        <Button v-else type="text" @click="onClickOrg(row.orgCode)">{{ row.orgCode.split('-')[1] }}</Button>
      </template>
    </ui-table>
  </div>
</template>

<script>
import UiBreadcrumb from '../components/ui-breadcrumb';
import governanceevaluation from '@/config/api/governanceevaluation';
import user from '@/config/api/user';
import { mapGetters } from 'vuex';

export default {
  name: 'testing-overview',
  components: {
    UiBreadcrumb,
    UiTable: require('@/components/ui-table.vue').default,
  },
  data() {
    return {
      tableColumns: [
        {
          title: '行政区划',
          slot: 'orgCode',
          width: 120,
        },
        {
          title: '视频监控数量总数',
          key: '1',
          width: 100,
        },
        {
          title: '重点设备检测数量',
          key: '1',
          width: 100,
        },
        {
          title: '重点实时视频异常数',
          key: '1',
          width: 120,
        },
        {
          title: '重点历史视频异常数',
          key: '1',
          width: 120,
        },
        {
          title: '重点时钟异常数',
          key: '1',
          width: 100,
        },
        {
          title: '重点OSD字幕异常数',
          key: '1',
          width: 120,
        },
        {
          title: '普通实时视频异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
          ],
        },
        {
          title: '普通历史视频异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
          ],
        },
        {
          title: '普通时钟异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
          ],
        },
        {
          title: '普通OSD字幕异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'age',
              width: 160,
              className: 'header-table',
            },
          ],
        },
      ],
      tableData: [],
      hasLast: false,
      formValidate: {
        sbdwlx: [],
        sbgnlx: [],
      },
      dictData: {},
      loading: false,
      breadcrumbData: [],
      currentOrgCode: '',
    };
  },
  methods: {
    onClickOrg(val) {
      let id = val.split('-')[0];
      let add = val.split('-')[1];
      this.currentOrgCode = id;
      this.breadcrumbData.push({ id: id, add: add });
      this.init();
    },
    search() {
      this.init();
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    handleChange(val) {
      this.currentOrgCode = val.id;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        let params = {
          evaluationStatisticsTypeId: this.$parent.currentTree.id,
          orgCode: this.currentOrgCode,
          sbdwlx: this.formValidate.sbdwlx,
          sbgnlx: this.formValidate.sbgnlx,
          resultId: this.$parent.taskObj.resultId,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getEvaluationOverview, params);
        data.headers.map((item) => {
          if (item.key === 'orgCode') {
            this.$set(item, 'slot', 'orgCode');
          }
          item['width'] = 200;
        });
        data.headers.unshift({ title: '序号', type: 'index', width: 70 });
        this.tableColumns = data.headers;
        this.tableData = data.body;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    resetForm() {
      this.formValidate = {
        sbdwlx: [],
        sbgnlx: [],
      };
      this.search();
    },
  },
  mounted() {
    this.currentOrgCode = this.orgTreeData[0].orgCode;
    let orgName = this.orgTreeData[0].orgName;
    this.breadcrumbData.push({ id: this.currentOrgCode, add: orgName });
    this.init();
    this.getDictData();
  },
  computed: {
    ...mapGetters({
      orgTreeData: 'common/getOrganizationList',
    }),
  },
};
</script>

<style lang="less" scoped>
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.search-container {
  display: flex;
  justify-content: space-between;
  margin: 20px 0 20px 0;
  .jump {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.ui-table {
  /deep/ .header-table {
    box-shadow: inset 1px -1px 0 0 #000d1b;
  }
}
.jump {
  height: 34px;
  line-height: 34px;
  color: #fff;
  font-size: 14px;
}
</style>
