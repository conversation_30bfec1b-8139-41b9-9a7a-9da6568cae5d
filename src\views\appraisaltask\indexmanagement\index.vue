<template>
  <div class="container">
    <ui-layout>
      <template #layout-left>
        <ui-card
          :data="indexTypeList"
          v-model="activeIndex"
          @on-change="handleClickCard"
          :countShow="true"
          v-ui-loading="{ loading: statisticsLoading }"
        ></ui-card>
      </template>
      <template #layout-filter>
        <dynamic-condition
          :formItemData="formItemData"
          :formData="formData"
          @search="search"
          @reset="search"
        ></dynamic-condition>
        <div class="right-container">
          <div class="right-bar">
            <Button type="primary" @click="handleNewIndex()" class="mr-lg">
              <i class="icon-font icon-tianjia f-12 mr-sm vt-middle"></i>
              <span class="vt-middle">新增指标</span>
            </Button>
            <div class="index-num">
              <p class="rule-text">{{ activeModel.title }}数量</p>
              <p class="rule-num">{{ pageData.totalCount }}</p>
            </div>
          </div>
        </div>
      </template>
      <template #layout-content>
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #action="{ row }">
            <div>
              <ui-btn-tip icon="icon-bianji2 mr-md" content="编辑" @click.native="clickRow('edit', row)"></ui-btn-tip>
              <ui-btn-tip icon="icon-chakanxiangqing" content="查看" @click.native="clickRow('view', row)"></ui-btn-tip>
            </div>
          </template>
          <template #dataTargetValue="{ row }">
            <span v-if="row.dataTargetValue">{{ row.dataTargetValue }}%</span>
            <span v-else>--</span>
          </template>
        </ui-table>
      </template>
      <template #layout-footer>
        <ui-page
          class="page"
          :pageData="pageData"
          :hasLast="hasLast"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </template>
    </ui-layout>
    <!--公共基础弹框-->
    <common-model ref="model" v-model="visible" :formModel="model" @refresh="initList" :formData="currentRow">
    </common-model>
    <!--  新增指标  -->
    <new-index v-model="newIndexVisible" @refresh="initList"></new-index>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'indexmanagement',
  props: {},
  data() {
    return {
      tableColumns: [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        { title: '指标名称', key: 'indexActualName', ellipsis: true, tooltip: true },
        { title: '显示名称', key: 'indexName', ellipsis: true, tooltip: true },
        { title: '达标值', key: 'dataTargetValue', slot: 'dataTargetValue', width: 100 },
        { title: '评价标准', key: 'evaluationCriterion', ellipsis: true, tooltip: true },
        { title: '计算方法', key: 'indexDefinition', ellipsis: true, tooltip: true },
        {
          title: '操作',
          width: 80,
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      tableData: [],
      minusTable: 267,
      loading: false,
      //ui-model
      currentRow: {},
      model: 'edit',
      visible: false,
      formData: {
        searchValue: '',
      },
      //分页
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      hasLast: false,
      //搜索
      formItemData: [
        {
          type: 'input',
          key: 'searchValue',
          label: '关键词',
        },
      ],
      modelSyles: {
        top: '169px',
      },
      activeModel: '',
      activeIndex: '',
      //规则总数
      ruleTotal: '',
      cardData: [],
      indexTypeList: [],
      newIndexVisible: false,
      statisticsLoading: false,
    };
  },
  created() {
    this.search();
  },
  methods: {
    async getIndexStatistics() {
      try {
        this.statisticsLoading = true;
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getIndexStatistics);
        let originData = data.data || [];
        let tempIndexTypeList = [{ id: '', title: '全部指标', icon: 'icon-quanbu', count: data.totalCount }];
        this.global.indexTypeList.forEach((value) => {
          originData.forEach((item) => {
            if (item.indexModule === value.id + '') {
              //this.global.indexTypeList 存的number类型这里转成string类型
              tempIndexTypeList.push({
                id: value.id + '',
                title: item.indexModuleText,
                count: item.count,
                icon: value.icon,
              });
            }
          });
        });
        this.indexTypeList = tempIndexTypeList;
      } catch (error) {
        console.log(error);
      } finally {
        this.statisticsLoading = false;
      }
    },
    search() {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.initList();
    },
    handleClickCard(item) {
      const id = item.id;
      if (id === this.activeModel.id) return false; // 重复点击不做改变
      // 模拟数据
      this.activeModel = item;
      this.search();
    },

    clickRow(model, row) {
      this.currentRow = {};
      this.currentRow = row;
      if (this.currentRow.generalParamSettingJson) {
        this.$refs.model.time_num = JSON.parse(this.currentRow.generalParamSettingJson).timeDelay;
      }
      this.model = model; // 判断编辑or 查看指标
      this.visible = true;
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    async initList() {
      try {
        this.tableData = [];
        this.loading = true;
        let params = {
          indexModule: this.activeIndex,
          searchValue: this.formData.searchValue.trim(),
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getEvaluationIndexByPage, params);
        this.pageData.totalCount = data.total;
        this.tableData = data.entities;
        let index = this.indexTypeList.findIndex((item) => item.title === '全部指标');
        if (index === -1) {
          await this.getIndexStatistics();
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    handleNewIndex() {
      this.newIndexVisible = true;
    },
  },
  computed: {},
  watch: {},

  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiCard: require('../components/ui-card').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    UiLayout: require('../components/ui-layout').default,
    CommonModel: require('./common-model').default,
    NewIndex: require('./components/new-index').default,
  },
};
</script>
<style lang="less" scoped>
@import url('../components/common.less');

.container {
  position: relative;
  height: 100%;

  .right-container {
    position: absolute;
    right: 19px;
    .right-bar {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .rule-text {
      color: var(--color-content);
      font-size: 12px;
    }

    .rule-num {
      text-align: center;
      font-size: 20px;
      font-weight: 400;
      line-height: 26px;
      color: var(--color-display-text);
    }
  }

  @{_deep} .base-search-wrap {
    .mb-sm {
      margin-bottom: 0 !important;
    }
  }

  .ui-table {
    @{_deep} .ivu-table-body {
      td {
        padding: 10px 0 10px 0;
      }
    }

    @{_deep} .ivu-table-fixed-right .ivu-table-fixed-body {
      td {
        padding: 10px 0 10px 0;
      }
    }
  }
}
</style>
