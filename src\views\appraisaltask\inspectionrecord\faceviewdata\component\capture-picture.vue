<template>
  <div>
    <ui-modal ref="uiModal" v-model="visible" title="查看抓拍图片" :footer-hide="true">
      <div class="modal-container">
        <div class="filter"></div>
        <div class="list" v-ui-loading="{ loading: loading, tableData: imageList }">
          <div v-for="(item, index) in imageList" :key="index" class="list-wrapper">
            <div class="list-item-image">
              <ui-image :src="item.facePath" />
            </div>
            <div class="list-item-desc">
              <div class="time ellipsis">
                <span class="icon-font vt-middle icon-shijian mr-xs"></span>
                <span class="vt-middle">{{ item.shotTime }}</span>
              </div>
              <!--              <div class="time ellipsis">
                <span class="icon-font vt-middle icon-dizhi mr-xs"></span>
                <span class="vt-middle">{{item.desc}}</span>
              </div>-->
            </div>
          </div>
        </div>
        <ui-page
          class="page"
          :page-data="pageData"
          :hasLast="false"
          @changePage="changePage"
          @changePageSize="changePageSize"
        ></ui-page>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
export default {
  name: 'capture-picture',
  components: {
    uiImage: require('@/components/ui-image').default,
  },
  props: {
    params: {
      required: true,
      default: () => {},
    },
    value: {},
  },
  data() {
    return {
      loading: false,
      visible: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      imageList: [],
    };
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.resetPage();
        this.initList();
      }
    },
  },
  created() {},
  methods: {
    async initList() {
      try {
        this.loading = true;
        this.imageList = [];
        let params = {
          ...this.pageData,
          deviceId: this.params.deviceId,
          deviceType: this.params.indexId === 83 ? '1' : '2', //deviceType 设备类型 1-车辆卡扣 2-视图抓拍类
        };
        let {
          data: { data },
        } = await this.$http.post(inspectionrecord.getCapturePageListByDevice, params);
        this.imageList = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    resetPage() {
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal {
  height: 788px !important;
  width: 1482px !important;
}
.modal-container {
  .list {
    display: flex;
    flex-wrap: wrap;
    height: 655px;
    overflow-y: auto;
    .list-wrapper {
      width: 195px;
      height: 210px;
      padding: 15px;
      background: #0f2f59;
      margin-left: 10px;
      margin-bottom: 10px;

      .list-item-image {
        height: 167px;
        width: 167px;
      }

      .list-item-desc {
        color: #8797ac;
      }
    }
  }
}
</style>
