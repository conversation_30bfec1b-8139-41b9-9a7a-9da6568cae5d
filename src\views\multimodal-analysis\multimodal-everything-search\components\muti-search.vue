<!--
 * @Date: 2025-03-07 16:43:35
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-19 11:49:40
 * @FilePath: \icbd-view\src\views\multimodal-analysis\multimodal-analysis-lib\components\muti-search.vue
-->
<template>
  <div class="search-content" v-show="!noSearch">
    <div class="search-top">
      <div class="top-left">
        <Input
          v-model="keyWords"
          placeholder="请输入关键词检索，多个关键词请用空格隔开"
          :maxlength="50"
          class="search-input"
        >
        </Input>
      </div>
      <div class="top-right">
        <!-- <ui-icon type="mai_line" :size="20" @click.native="openAudio"></ui-icon> -->
        <Button
          type="primary"
          class="search-btn"
          slot="append"
          @click="searchHandler"
        >
          搜索
        </Button>
      </div>
    </div>
    <div class="search-bottom">
      <div class="bottom-left">
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          @change="changeDateType"
        />
      </div>
      <div class="bottom-right">
        <div>
          <div class="select-detail">
            <RadioGroup
              v-model="queryForm.thresholdLevel"
              type="button"
              button-style="solid"
            >
              <Radio
                v-for="item in thresholdLevelList"
                :key="item.dataKey"
                :label="item.dataKey"
                >{{ item.dataValue }}</Radio
              >
            </RadioGroup>
            <Select
              class="select-tag-taskId"
              v-model="queryForm.yiTuRepoIds"
              placeholder="请选择库"
              multiple
              filterable
              :max-tag-count="0"
            >
              <Option v-for="item in libList" :key="item.id" :value="item.id">{{
                item.name
              }}</Option>
            </Select>
            <Select
              class="select-tag-taskId"
              v-model="queryForm.deviceIds"
              placeholder="请选择设备"
              multiple
              filterable
              :max-tag-count="0"
            >
              <Option
                v-for="item in deviceList"
                :key="item.deviceId"
                :value="item.deviceId"
                >{{ item.deviceName }}</Option
              >
            </Select>
            <!-- <div class="select-tag-button" @click="selectDevice()">
              选择设备（{{ selectDeviceList.length }}）
            </div> -->
          </div>
        </div>
      </div>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      @selectData="selectData"
    />
  </div>
</template>

<script>
import { ytLLmLibList } from "@/api/multimodal-analysis";

export default {
  name: "mutiSearch",
  // components: {
  //   UiAudio,
  //   selectFile,
  // },
  props: {
    deviceList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      keyWords: "",
      dateType: 1,
      queryForm: {
        startTime: "",
        endTime: "",
        thresholdLevel: "",
        deviceIds: [],
        yiTuRepoIds: [],
      },
      thresholdLevelList: [
        { dataKey: "", dataValue: "全部" },
        { dataKey: "LOW", dataValue: "低" },
        { dataKey: "MID", dataValue: "中" },
        { dataKey: "HIGH", dataValue: "高" },
      ],
      noSearch: false,
      selectTaskList: [],
      selectDeviceList: [],
      fristQuery: true,
      libList: [],
    };
  },
  mounted() {
    let query = this.$route.query;
    const { endDate, startDate, keyWords, noSearch, dateType } = query;
    this.noSearch = noSearch === "1";
    if (keyWords) this.keyWords = keyWords;
    if (dateType) this.dateType = Number(dateType);
    if (endDate || startDate) {
      this.queryForm.endTime = endDate || "";
      this.queryForm.startTime = startDate || "";
    }
    this.getLibList();
    this.$nextTick(() => {
      this.$refs.quickDateRef.handleInit(this.dateType, startDate, startDate);
      // this.searchHandler();
    });
  },
  methods: {
    async getLibList() {
      const res = await ytLLmLibList();
      this.libList = res?.data || [];
    },
    searchHandler() {
      if (!this.keyWords) {
        return this.$message.warning("请输入检索内容");
      }
      let param = {
        ...this.queryForm,
        keywords: this.keyWords,
      };
      this.$emit("searchHander", param);
    },
    /**
     * @description: 切换日期
     * @param {object} value 当前选中的时间区间
     */
    changeDateType(value) {
      this.queryForm.startTime = value.startDate;
      this.queryForm.endTime = value.endDate;
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    selectData(list) {
      this.selectDeviceList = list;
    },
  },
};
</script>

<style lang="less" scoped>
.search-content {
  width: 1300px;
  height: 110px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #cdd5e0;
  padding: 12px 20px;
  .search-top {
    display: flex;
    justify-content: space-between;
    height: 50px;
    width: 100%;
    border-bottom: 1px solid #d8d8d8;
    padding-bottom: 10px;
    .top-left {
      flex: 1;
      .search-input {
        /deep/ .ivu-input {
          border: none;
          padding: 0;
        }
        input {
          border: none;
        }
      }
    }
    .top-right {
      width: 168px;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      align-items: center;
      button {
        width: 100px;
        height: 36px;
        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
        border-radius: 4px 4px 4px 4px;
      }
    }
  }
  .search-bottom {
    padding-top: 6px;
    display: flex;
    justify-content: space-between;
    .bottom-right {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .select-detail {
        display: flex;
        justify-content: space-between;
        .select-tag-button {
          width: 150px;
          margin-left: 10px;
        }
        .select-tag-taskId {
          width: 150px;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
