<template>
  <div class="area-map">
    <ui-modal title="选择区域" v-model="visible" :styles="styles" :footer-hide="true">
      <div class="device-div">
        <div class="over-flow">
          <ui-label class="mb-xs fl" label="组织机构:" width="70">
            <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree"></api-organization-tree>
          </ui-label>
          <div class="fr">
            <Icon
              type="ios-arrow-down"
              class="expand-icon pointer"
              :class="{ 'expand-icon-trun': showCamera }"
              @click="expandCamera"
            />
          </div>
        </div>
        <div v-show="showCamera">
          <div class="search-div menu-content-background">
            <Input
              search
              placeholder="检索-请输入名称"
              class="width-md"
              v-model="searchText"
              @on-search="searchCamaraAdd"
              @on-enter="searchCamaraAdd"
            />
          </div>
          <div class="over-flow camera-detail border">
            <div class="fl select-camera" ref="cameraBox" v-scroll="340">
              <div>
                <Checkbox class="mb-sm" v-model="mainChecked" @on-change="chooseChecked" :disabled="isView">
                  <span class="base-text-color">全选</span>
                </Checkbox>
              </div>
              <div v-for="(item, index) in viewCameraList" :key="index" class="mr-sm mb-sm base-text-color fl">
                <Checkbox v-model="item.checked" @on-change="selectCamera(item)" :disabled="isView"></Checkbox>
                {{ item.deviceName }}
              </div>
              <Spin size="large" fix v-if="loading"></Spin>
            </div>
            <div class="fr border selected-div">
              <div class="selected-title over-flow">
                <span class="ml-sm base-text-color fl">已选{{ selectedCameraList.length }}个</span>
                <span v-if="!isView" class="table-text-content fr pointer" @click="unSelectAll">清空</span>
              </div>
              <div class="selected-list height-full" v-scroll="360">
                <div v-for="(item, index) in selectedCameraList" :key="index" :title="item.deviceName">
                  <div class="over-flow selected-item" v-if="item.checked">
                    <span class="base-text-color fl ellipsis width-md">{{ item.deviceName }}</span>
                    <Icon v-if="!isView" type="ios-close" class="protruding close fr" @click="unSelect(item, index)" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="map" :id="mapId"></div>
      <map-tool
        v-if="!isView"
        @cancelDraw="cancelDraw"
        @selectRect="selectRect"
        @selectCircle="selectCircle"
        @selectPolygon="selectPolygon"
        @clearDraw="clearDraw"
      >
        <template slot="addTool">
          <li v-for="item in addTool" :key="item.title" :title="item.title" @click="item.fun" class="add-tool-list">
            <Icon :type="item.icon" color="#dddddd" class="add-tool"></Icon>
          </li>
        </template>
      </map-tool>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.device-div {
  position: absolute;
  right: 0;
  top: 0;
  width: 500px;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 999;
  padding: 5px;
  .expand-icon {
    font-size: 20px;
    line-height: 32px;
    transition: all 0.2s;
  }
  .expand-icon-trun {
    transform: rotate(180deg);
  }
  .area-name {
    width: 200px;
    position: absolute;
    top: 60px;
    left: 20px;
    z-index: 400;
  }
  .search-div {
    padding: 10px;
  }
  .camera-detail {
    border-top: 1px solid;
    .select-camera {
      padding: 5px;
      width: 200px;
    }
    .selected-div {
      padding: 5px;
      border-left: 1px solid;
      width: 280px;
      .selected-list {
        padding: 5px 0;
        .close {
          font-size: 20px;
          cursor: pointer;
        }
        .selected-item {
          padding: 5px 10px;
          &:hover {
            background-color: #101e2b;
          }
        }
      }
    }
  }
}
.map {
  width: 100%;
  height: 710px;
}
.add-tool-list {
  line-height: 40px;
  .add-tool {
    font-size: 25px;
  }
}

/*新增设备样式*/
.olFramedCloudPopupContent {
  width: 258px !important;
  height: 58px !important;
}
@{_deep} .ivu-modal-body {
  position: relative;
  padding: 0;
}
@{_deep} .device {
  width: 260px;
  height: 60px;
  padding: 10px;
  background: #0f1821;
  color: #a0e8f3;
  border: 1px solid#49badb;
  .icon-camera {
    float: left;
    width: 30px;
    height: 30px;
  }
  ul {
    margin-left: 40px;
    li {
      line-height: 30px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
<script>
import { NPGisMapMain } from '@/map/map.main';
import { mapGetters, mapActions } from 'vuex';
let mapMain = null;
let infoWindowArr = [];
export default {
  data() {
    return {
      visible: false,
      mapId: 'mapId' + Math.random(),
      styles: {
        top: '100px',
        height: '800px',
        width: '1500px',
        position: 'relative',
      },
      addTool: [
        { title: '确 定', icon: 'md-checkmark', fun: this.query },
        { title: '取 消', icon: 'md-close', fun: this.cancel },
      ],
      position: {},
      selectedCameraList: [],
      // 左侧摄像头选择
      selectOrgTree: {
        orgCode: '',
      },
      searchText: '',
      loading: false,
      mainChecked: false,
      showCamera: false,
      cameraList: [],
      viewCameraList: [], //当摄像头过多的时候不要显示所有的摄像头只显示该数组的摄像头
      initCameraList: [], //搜索时摄像头临时存储列表 当搜空的时候 返回所有的摄像头
    };
  },
  async created() {
    // 获取地图, 摄像头列表, 加载自定义区域
    await Promise.all([this.setMapConfig(), this.setMapStyle(), this.setCameraList()]);
  },
  mounted() {},
  methods: {
    ...mapActions({
      setCameraList: 'common/setCameraList',
      setMapConfig: 'common/setMapConfig',
      setMapStyle: 'common/setMapStyle',
    }),
    _initMap(data, style) {
      // 配置初始化层级
      mapMain = new NPGisMapMain();
      let mapId = this.mapId;
      mapMain.init(mapId, data, style);
      // 初始化自定义区域框选
      if (this.mapPosition) {
        this.drawPosition(this.mapPosition);
        this.position = this.mapPosition;
      }
      this.selectedCameraList = this.deviceList.map((row) => {
        this.$set(row, 'checked', true);
        return row;
      });
      // 把摄像机加载到地图上
      this.renderAddMarkers(this.deviceList);
      if (this.allCameraList.length !== 0) {
        this._initSystemPoints2Map(this.allCameraList);
      }
    },
    // 加载点位到地图上
    _initSystemPoints2Map(points) {
      if (mapMain) {
        // 加载点位
        mapMain.renderMarkers(mapMain.convertSystemPointArr2MapPoint(points), this.getMapEvents());
      }
    },
    renderAddMarkers(points) {
      mapMain.renderAddMarkers(mapMain.convertSystemPointArr2MapPoint(points), this.getMapEvents());
    },
    addMarkers(points) {
      mapMain.addMarkers(mapMain.convertSystemPointArr2MapPoint(points), this.getMapEvents());
    },
    getMapEvents() {
      let opts = {
        click: () => {
          // TODO edit wyr 2017.8.18 这里将ClusterMarker换为ClusterMarkerEx, 就可以取到objectId了, 请再测试下
          // TODO resolve zb
          // if (marker.markType == ObjectType.Camera.value) {
          //   $scope.$emit(trailBroadcastEnum.clickMapMark, marker.objectId);
          // }
        },
        // 宁添加
        // mouseover要展示摄像头名称和详细信息
        mouseover: (marker) => {
          let point = {};
          point.place = marker.titleName;
          point.lat = marker.location.lat;
          point.lon = marker.location.lon;
          point.type = 'device';

          this.selectItem(point, false);
        },
      };
      return opts;
    },
    closeAllInfoWindow() {
      infoWindowArr.forEach((row) => {
        row.close();
      });
    },
    selectItem(pointItem, setCenter) {
      // 宁 ，显示之前先清除其他提示框
      this.closeAllInfoWindow();
      let point = new NPMapLib.Geometry.Point(pointItem.lon, pointItem.lat);
      // 宁，弹窗显示得时候地图是否需要移动到弹窗位置
      setCenter ? mapMain.map.setCenter(point) : null;
      //mapMain.closeInfoWindow(this.selWinId);
      let opts = {
        width: 50, // 信息窗宽度
        height: 100, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(0, 0), // 信息窗位置偏移值
        iscommon: false, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: true, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
        autoSize: true, // 默认true, 窗口大小是否自适应
        // paddingForPopups: NPMapLib.Geometry.Extent, // 信息窗自动弹回后，距离四边的值。isAdaptation为true时，该设置有效
        isAdaptation: true, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
        // useDomStyle: true,
        positionBlock: {
          // 箭头样式
          // imageSrc: require('@/assets/img/datasituation/triangle.png'),
          imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
          offset: new NPMapLib.Geometry.Size(-130, -15),
        },
      };
      let div = document.createElement('div');
      switch (pointItem.type) {
        case 'device':
          div.className = 'device';
          div.innerHTML =
            '<i class="icon-camera"></i>' +
            '<ul><li title="' +
            pointItem.place +
            '"><i class="icon-info icon-location"></i>' +
            pointItem.place +
            '</li></ul>';
          break;
      }
      let infoWindow = new NPMapLib.Symbols.InfoWindow(point, null, null, opts);
      infoWindow.setContentDom(div);
      mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      infoWindowArr.push(infoWindow);
    },
    removeMarkers(points) {
      points.forEach((row) => {
        mapMain.removeMarker1(row.deviceId);
      });
    },
    cancelDraw() {
      mapMain.cancelDraw();
    },
    selectRect() {
      this.clearDraw();
      mapMain.selectRectangle((points, position) => {
        this.selectPoint(points);
        this.position = position;
      }, true);
    },
    selectCircle() {
      this.clearDraw();
      mapMain.selectCircle((points, position) => {
        this.selectPoint(points);
        this.position = position;
      }, true);
    },
    selectPolygon() {
      this.clearDraw();
      mapMain.selectPolygon((points, position) => {
        this.selectPoint(points);
        this.position = position;
      }, true);
    },
    clearDraw() {
      mapMain.clearDraw();
      mapMain.removeRectangle();
      this.removeMarkers(this.selectedCameraList);
      this.selectedCameraList = [];
      this.position = {};
    },
    // 过滤点位加载到地图上
    selectPoint(points) {
      this.removeMarkers(this.selectedCameraList);
      this.selectedCameraList = [];
      // 将选中的点位数组转换为对象减少循环次数
      let pointObj = new Object();
      for (let i = 0, len = points.length; i < len; i++) {
        pointObj[points[i].ObjectID] = points[i];
      }
      this.allCameraList.forEach((row) => {
        let index = this.selectedCameraList.findIndex((rw) => {
          return rw.deviceId === row.deviceId;
        });
        if (!!pointObj[row.deviceId] && index === -1) {
          this.$set(row, 'checked', true);
          this.selectedCameraList.push(row);
          this.selectCamera(row);
        }
      });
      this.showCamera = true;
      this.renderAddMarkers(this.selectedCameraList);
    },
    query() {
      // 这里深度克隆是因为clearDraw会清空列表影响提交的数据
      // 这里转换为字符串后端来接收字符串
      let mapPosition = this.$util.common.deepCopy(this.position);
      let deviceList = this.$util.common.deepCopy(this.selectedCameraList);
      this.$emit('putCameraList', deviceList, mapPosition);
      this.visible = false;
      this.clearDraw();
    },
    cancel() {
      this.visible = false;
      this.clearDraw();
    },
    drawPosition(position) {
      switch (position.type) {
        case 'rectangle':
          mapMain.drawRectangle(position, true, (points, positions) => {
            this.selectPoint(points);
            this.position = positions;
          });
          mapMain.map.setCenter(position.center);
          break;
        case 'polygon':
          mapMain.drawPolygon(position, true, (points, positions) => {
            this.selectPoint(points);
            this.position = positions;
          });
          mapMain.map.setCenter(position.center);
          break;
        case 'circle':
          mapMain.drawCircle(position, true, (points, positions) => {
            this.selectPoint(points);
            this.position = positions;
          });
          mapMain.map.setCenter(position.center);
          break;
        default:
          console.log('还不支持此图形');
          break;
      }
    },

    // 左侧选择
    expandCamera() {
      this.showCamera = !this.showCamera;
    },
    //-----------------根据名字检索摄像机函数---------------------------
    searchCamaraAdd() {
      let searchArr = [];
      if (this.searchText !== '') {
        for (let i = 0; i < this.initCameraList.length; i++) {
          let str = this.initCameraList[i].deviceName || '';
          if (str.indexOf(this.searchText) !== -1) {
            searchArr.push(this.initCameraList[i]);
          }
        }
        this.cameraList = searchArr;
      } else {
        this.cameraList = this.initCameraList;
      }
      this.muchCamera();
    },
    //------------------全选所有摄像机函数---------------------------------
    chooseChecked(val) {
      this.cameraList.forEach((row) => {
        row.checked = val;
        this.selectCamera(row);
      });
    },
    muchCamera() {
      //这里处理如果摄像机列表过多，则显示前200个，否则dom过多显示会卡死
      if (this.cameraList.length > 200) {
        this.listenScroll();
      }
      this.cameraSliceNum = 200;
      this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
    },
    //监听滑动距离加载隐藏的摄像机列表
    listenScroll() {
      let box = this.$refs.cameraBox;
      box.addEventListener(
        'scroll',
        () => {
          // console.log(box.scrollTop + box.clientHeight === box.scrollHeight);
          //  判断是否滚动到底部，如果滚动到底部则则再加载200个摄像头
          if (box.scrollTop + box.clientHeight === box.scrollHeight) {
            this.cameraSliceNum += 200;
            //到底部要做的操作
            this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
          }
        },
        false,
      );
    },
    searchChild(orgCodeList, list) {
      if (list) {
        list.forEach((row) => {
          this.searchChild(orgCodeList, row.children);
          orgCodeList.push(row);
        });
      }
    },
    selectedOrgTree(val) {
      this.cameraList = [];
      // 获取机构列表包括已选择的该组织机构下的子列表
      let orgCodeList = [val.orgCode];
      this.searchChild(orgCodeList, val.children);
      let tempObj = {}; //一个临时对象
      // 将一个数组转换为一个对象减少for循环
      orgCodeList.forEach((row) => {
        tempObj[row.orgCode] = row;
      });
      //从所有的摄像机列表中和选中的树结构的区域编码做对比，如果是选中的区域编码则加入中间的摄像机列表
      this.allCameraList.forEach((row) => {
        if (tempObj[row.orgCode]) {
          this.cameraList.push(row);
        }
      });
      //提取一个对象减少for循环
      let tempObj2 = {};
      this.selectedCameraList.forEach((row) => {
        tempObj2[row.deviceId] = row.deviceId;
      });
      //初始化中间摄像机列表时，要跟右边已选中摄像机列表做对比，选中摄像机
      this.cameraList = this.cameraList.map((row) => {
        if (tempObj2[row.deviceId]) {
          this.$set(row, 'checked', true);
        } else {
          this.$set(row, 'checked', false);
        }
        return row;
      });
      //当摄像头过多时进行处理不显示所有摄像头只先显示200个
      this.muchCamera();
      this.initCameraList = this.cameraList;
      this.showCamera = true;
    },
    check(checkedKeys) {
      this.cameraList = [];
      let tempObj = {}; //一个临时对象
      // 将一个数组转换为一个对象减少for循环
      checkedKeys.forEach((row) => {
        tempObj[row] = row;
      });
      this.isCheckAll();
    },
    //中间摄像头列表选中
    selectCamera(item) {
      //这里因为不想要中间的摄像头列表影响到右边选中的摄像头列表所以需要深度克隆
      let temp = this.$util.common.deepCopy(item);
      let index = this.selectedCameraList.findIndex((row) => {
        return temp.deviceId === row.deviceId;
      });
      if (temp.checked) {
        if (index === -1) {
          this.selectedCameraList.push(temp);
        }
      } else {
        this.selectedCameraList.splice(index, 1);
      }
      this.isCheckAll();
    },
    // 检查是否全选所有中间摄像机列表
    isCheckAll() {
      if (this.cameraList.length === 0) {
        this.mainChecked = false;
        return false;
      }
      this.mainChecked = true;
      this.cameraList.forEach((row) => {
        if (!row.checked) this.mainChecked = false;
      });
    },
    unSelectAll() {
      this.cameraList.forEach((row) => {
        row.checked = false;
      });
      this.initCameraList.forEach((row) => {
        row.checked = false;
      });
      this.selectedCameraList = [];
      this.mainChecked = false;
    },
    unSelect(item, index) {
      item.checked = false;
      let temp = this.$util.common.deepCopy(item);
      let obj = this.cameraList.find((row) => {
        return temp.deviceId === row.deviceId;
      });
      if (obj) {
        obj.checked = item.checked;
      }
      this.selectedCameraList.splice(index, 1);
      this.isCheckAll();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.$nextTick(() => {
          // 初始化地图
          this._initMap(this.mapConfig, this.mapStyle);
          // 如果有默认组织机构则选中
          if (this.defaultSelectedOrg) {
            this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
            this.selectedOrgTree(this.defaultSelectedOrg);
          }
        });
      } else {
        if (this.selectedCameraList) {
          this.removeMarkers(this.selectedCameraList);
        }
        if (mapMain) {
          mapMain.destroy();
          mapMain = null;
        }
      }
    },
    allCameraList() {
      // this._initSystemPoints2Map(this.allCameraList);
    },
    defaultSelectedOrg(val) {
      this.selectOrgTree.orgCode = val.orgCode;
      this.selectedOrgTree(val);
    },
  },
  computed: {
    ...mapGetters({
      allCameraList: 'common/getCameraList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      mapConfig: 'common/getMapConfig',
      mapStyle: 'common/getMapStyle',
    }),
  },
  props: {
    value: {},
    // 选中区域定位信息
    mapPosition: {},
    // 选中摄像头列表
    deviceList: {
      type: Array,
    },
    // 查看
    isView: {
      type: Boolean,
    },
  },
  components: {
    MapTool: require('@/components/map-tool.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
  beforeDestroy() {
    if (mapMain) {
      mapMain.destroy();
      mapMain = null;
    }
  },
};
</script>
