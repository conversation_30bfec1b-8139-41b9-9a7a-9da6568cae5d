<template>
  <div class="travel-trajectory">
    <template v-if="timeLineList.length">
      <Icon
        type="md-arrow-dropleft-circle"
        class="arrow-left"
        :size="30"
        @click="prev"
      />
      <div class="travel-box">
        <div
          class="travel-list"
          :style="{
            'margin-left': currentIndex * singleWidth + 'px',
            transition: 'all .3s ease-out .1s',
          }"
        >
          <template v-for="(e, i) in timeLineList">
            <div :key="i" class="travel-item">
              <!-- 娱乐场所出行 -->
              <template v-if="e.dataType == 1">
                <div class="travel-icon">
                  <i class="iconfont icon-gewuting"></i>
                </div>
                <div class="travel-content flex-box">
                  <div class="image-box">
                    <ui-image
                      :src="e?.faceCaptureVo?.traitImg || ''"
                      viewer
                    ></ui-image>
                  </div>
                  <div class="text-box">
                    <p class="travel-label">
                      <span>{{ e.absTime }}</span>
                    </p>
                    <p class="travel-label">
                      <span>{{ e.deviceName }}</span>
                    </p>
                  </div>
                </div>
                <div class="travel-date">
                  <div class="circle"></div>
                  <div class="verticalLine"></div>
                  <div class="time">
                    {{ dateSplit(e.absTime) }}
                  </div>
                </div>
              </template>
              <!-- 网吧 -->
              <template v-if="e.dataType == 5">
                <div class="travel-icon">
                  <i class="iconfont icon-diannao"></i>
                </div>
                <div class="travel-content">
                  <p class="title">{{ e.yycsDwmc }}</p>
                  <p class="travel-label">
                    <label>登记时间</label>
                    <span class="trainNumber"> {{ e.kssj }} </span>
                  </p>
                  <p class="travel-label">
                    <label>网吧地址</label>
                    <span> {{ e.yycsDzmc }} </span>
                  </p>
                  <!-- <p class="travel-label">
                  <label>出发时间</label>
                  <span> {{ e.timestamp }} </span>
                </p> -->
                </div>
                <div class="travel-date">
                  <div class="circle"></div>
                  <div class="verticalLine"></div>
                  <div class="time">
                    {{ dateSplit(e.kssj) }}
                  </div>
                </div>
              </template>
            </div>
          </template>
        </div>
      </div>
      <Icon
        type="md-arrow-dropright-circle"
        class="arrow-right"
        :size="30"
        @click="next"
      />
    </template>
    <ui-empty v-else />
  </div>
</template>

<script>
export default {
  name: "TimelineLevel",
  props: {
    timeLineList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      singleWidth: 298, //单个宽度
      currentIndex: 0,
    };
  },
  methods: {
    prev() {
      if (this.currentIndex < 0) {
        this.currentIndex += 1;
      }
    },
    next() {
      if (this.timeLineList.length + this.currentIndex > 6) {
        this.currentIndex -= 1;
      } else if (
        this.currentIndex < -(this.timeLineList.length + this.currentIndex) &&
        -(this.timeLineList.length + this.currentIndex) != -0
      ) {
        this.currentIndex -= 0;
      }
    },
    reset() {
      this.currentIndex = 0;
    },
    // 时间提取成年月日
    dateSplit(time) {
      if (!time) {
        return;
      }
      let date = time.split(" ");
      return date[0];
    },
  },
};
</script>

<style lang="less" scoped>
.travel-trajectory {
  height: 275px;
  display: flex;
  flex-direction: row;
  position: relative;
  padding: 0 30px;
  .arrow-left,
  .arrow-right {
    position: absolute;
    top: 15%;
  }
  .arrow-left {
    left: 0;
  }
  .arrow-right {
    right: 0;
  }
  .arrow-left:hover,
  .arrow-right:hover {
    color: #2c86f8;
  }
  .ivu-icon {
    height: 150px;
    display: flex;
    align-items: center;
    // cursor: pointer;
  }
  .travel-box {
    width: calc(~"(100% - 5px)");
    overflow: hidden;
    .travel-list {
      display: flex;
      flex-direction: row;
      overflow: hidden;
      height: 385px;
    }
    .travel-item {
      position: relative;
      margin-top: 40px;
      .travel-icon {
        position: absolute;
        top: -40px;
        left: 50%;
        margin-left: -25px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        > i {
          font-size: 28px;
          color: #2c86f8;
        }
      }
    }
    .travel-content {
      width: 258.5px;
      height: 150px;
      background: #f9f9f9;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      margin-left: 20px;
      margin-right: 20px;
      padding: 15px 10px 13px;
      box-sizing: border-box;
      .title {
        font-size: 20px;
        font-weight: bold;
        color: #2c86f8;
        text-align: center;
        margin-bottom: 10px;
      }
      .text-box {
        width: 143px;
      }
      .travel-label {
        display: flex;
        > label {
          font-size: 14px;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.9);
          // margin-left: 10px;
          // width: 80px;
          text-align: left;
          padding-right: 10px;
        }
        > span {
          font-size: 14px;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: rgba(0, 0, 0, 0.8);
        }
        .trainNumber {
          color: #f29f4c;
        }
      }
      .image-box {
        width: 80px;
        height: 80px;
        margin-right: 10px;
      }
    }
    .flex-box {
      display: flex;
      align-items: center;
    }
    .travel-date {
      position: absolute;
      top: 176.2px;
      transform: translateX(-50%);
      left: 50%;
      width: 100%;
      text-align: center;
      .circle {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #2c86f8;
        border: 1px solid #ffffff;
        margin: 0 auto;
        position: relative;
      }
      .circle:before {
        content: "";
        width: 177px;
        height: 1px;
        background: #d3d7de;
        position: absolute;
        top: 2px;
        right: 5px;
      }
      .circle:after {
        content: "";
        width: 177px;
        height: 1px;
        background: #d3d7de;
        position: absolute;
        top: 2px;
        left: 5px;
      }
      .verticalLine {
        position: absolute;
        top: -26px;
        left: 50%;
        transform: translateX(-50%);
        width: 1px;
        height: 26px;
        background: #d3d7de;
      }
      .time {
        font-size: 16px;
        font-weight: bold;
        color: #2c86f8;
      }
    }
  }
}
</style>
