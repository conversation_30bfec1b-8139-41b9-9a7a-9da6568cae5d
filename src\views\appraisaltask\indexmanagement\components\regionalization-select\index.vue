<template>
  <ui-modal v-model="visible" title="设置达标数量" :styles="styles" class="ui-modal" @query="query">
    <div class="area-filter mb-md ml-sm">
      <ui-label required class="inline" label="行政区划" :width="70">
        <Select class="width-md" v-model="form.regionType" placeholder="请选择行政区划">
          <Option value="province">省</Option>
          <Option value="city">市</Option>
          <Option value="region">区县</Option>
          <Option value="policeStation">派出所</Option>
        </Select>
      </ui-label>
      <ui-label required class="inline ml-lg" label="数据类型" :width="70">
        <Select class="width-md" v-model="form.numberType" placeholder="请选择数据类型">
          <Option v-for="(opItem, opIndex) in assessmentItems" :key="'opIndex' + opIndex" :value="opItem.key">{{
            opItem.optionLabel
          }}</Option>
        </Select>
      </ui-label>
      <ui-label required class="inline ml-lg" label="达标数量" :width="70">
        <InputNumber v-model="form.number" class="width-md" placeholder="请输入达标数量"> </InputNumber>
      </ui-label>
      <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
    </div>
    <div class="area-container">
      <div class="tree-title base-text-color">
        <span class="org-name">行政区划名称</span>
        <span v-for="(astItem, astIndex) in assessmentItems" :key="'astIndex' + astIndex" class="width-sm mr-100">{{
          astItem.title
        }}</span>
        <span class="is-checked">是否考核</span>
        <span class="is-all">全部</span>
      </div>
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          :highlight-current="false"
          node-key="nodeKey"
          :tree-data="areaTreeData"
          :default-props="defaultProps"
          expandAll
          checkStrictly
        >
          <template #label="{ node, data }">
            <div class="flex">
              <div class="vt-middle left-item">{{ data.regionName }}</div>
              <div class="inline right-item">
                <div v-show="data.check && edit">
                  <Input
                    v-for="(asstItem, asstIndex) in assessmentItems"
                    :key="'asstIndex' + asstIndex"
                    v-model="data[asstItem.key]"
                    class="width-sm vt-middle mr-100"
                    :placeholder="`请输入${asstItem.title}`"
                  ></Input>
                </div>
                <div class="vt-middle switch-width">
                  <i-switch v-model="data.check" size="small" @on-change="check($event, node, data)"></i-switch>
                </div>
                <Checkbox
                  class="vt-middle check-width"
                  v-model="data.checkAll"
                  @on-change="checkAll($event, node, data)"
                  :style="{ visibility: !data.children ? 'hidden' : '' }"
                  >{{ `${data.checkAll ? '取消' : '全部'} ` }}</Checkbox
                >
              </div>
            </div>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'regionalization-select',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'regionName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'regionCode',
    },
    data: {
      default: () => {
        return [];
      },
    },
    styles: {
      type: Object,
      default: () => {
        return {
          width: '7.6rem',
        };
      },
    },
    assessmentItems: {
      type: Array,
      default: () => [
        { title: '视频监控数量', key: 'sxjValue', optionLabel: '视频监控' },
        { title: '人脸卡口数量', key: 'rlkkValue', optionLabel: '人脸卡口' },
        { title: '车辆卡口数量', key: 'clkkValue', optionLabel: '车辆卡口' },
      ],
    }, // 考核项
  },
  data() {
    return {
      visible: false,
      loading: false,
      checkedTreeData: [],
      areaTreeData: [],
      form: {
        regionType: 'province',
        numberType: '',
        number: null,
      },
    };
  },
  created() {
    this.visible = true;
  },
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
    }),
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.form.numberType = this.assessmentItems[0].key;
        // this.init()
      } else {
        this.areaTreeData = [];
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler: function () {
        this.init();
      },
    },
  },
  methods: {
    clickBatchInput() {
      if (!this.form.number) {
        return this.$Message.error('请输入达标数量');
      }
      this.batchInput(this.areaTreeData);
    },
    /*
    1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所
    * */
    batchInput(data) {
      data.map((item) => {
        if (
          (item.regionType === '1' && this.form.regionType === 'province') ||
          (['2', '3', '4', '5'].includes(item.regionType) && this.form.regionType === 'city') ||
          (['6', '7', '8'].includes(item.regionType) && this.form.regionType === 'region') ||
          (item.regionType === '9' && this.form.regionType === 'policeStation')
        ) {
          this.$set(item, this.form.numberType, this.form.number);
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },
    setSourceDataToTreeData(source, target) {
      target.map((item) => {
        source.map((value) => {
          if (item.regionCode === value.key) {
            this.$set(item, 'check', true);
            this.$set(item, 'regionCode', value.key);
            this.$set(item, 'regionName', value.name);
            this.$set(item, 'sxjValue', value.sxjValue);
            this.$set(item, 'rlkkValue', value.rlkkValue);
            this.$set(item, 'clkkValue', value.clkkValue);
          }
        });
      });
    },
    init() {
      let treeData = JSON.parse(JSON.stringify(this.treeData));
      this.setSourceDataToTreeData(this.data, treeData);
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
    },
    query() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.areaTreeData);
      let result = this.validateCheckedNodes(this.checkedTreeData);
      if (result) {
        this.$emit('query', this.checkedTreeData);
        this.visible = false;
      }
    },
    getCheckedNodes(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push({
            key: item.regionCode,
            name: item.regionName,
            sxjValue: item.sxjValue || '',
            rlkkValue: item.rlkkValue || '',
            clkkValue: item.clkkValue || '',
          });
        }
        if (item.children) {
          this.getCheckedNodes(item.children);
        }
      });
    },
    validateCheckedNodes(data) {
      if (!data.length) {
        this.$Message.error('请选择行政区划并设置达标数量');
        return false;
      }
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        let sxjValue = item.sxjValue;
        let rlkkValue = item.rlkkValue;
        let clkkValue = item.clkkValue;
        let reg = /^[1-9]\d*$/;
        if (sxjValue && !reg.test(sxjValue)) {
          this.$Message.error(`${item.name}视频监控达标数量格式不正确`);
          return false;
        }
        if (rlkkValue && !reg.test(rlkkValue)) {
          this.$Message.error(`${item.name}人脸卡口达标数量格式不正确`);
          return false;
        }
        if (clkkValue && !reg.test(clkkValue)) {
          this.$Message.error(`${item.name}车辆达标数量格式不正确`);
          return false;
        }
        if (!sxjValue && !rlkkValue && !clkkValue) {
          this.$Message.error(`${item.name}达标数量不能为空`);
          return false;
        }
      }
      return true;
    },
    checkAll(val, node, data) {
      this.$set(node.data, 'check', data.checkAll);
      if (node.childNodes) {
        if (data.checkAll) {
          this.checkParent(node, data.checkAll);
        } else {
          this.checkChildren([node], data.checkAll);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node) {
      this.checkParent(node, val);
      this.checkChildren([node], val);
    },
    checkParent(node, check = true) {
      this.$set(node.data, 'check', check);
      if (node.parent) {
        if (check) {
          this.checkParent(node.parent, check);
        }
      }
    },
    checkChildren(node, check) {
      node &&
        node.map((item) => {
          this.$set(item.data, 'checkAll', false);
          this.$set(item.data, 'check', check);
          if (item.childNodes) {
            this.checkChildren(item.childNodes);
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
</style>
