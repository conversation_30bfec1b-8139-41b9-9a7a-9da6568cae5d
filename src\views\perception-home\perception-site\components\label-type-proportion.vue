<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt=''/><span class="title-text">标签类型统计</span>
    </div>
    <div class="echart-wrap">
      <pie-echart v-if="labelTypeList.length" :data="labelTypeList" class="pie-echart" />
      <div class="legend-ul">
        <div v-for="(item, $index) in labelTypeList" :key="$index" class="legend-item" @click="lgendHandle(item)">
          <span :style="{borderColor:item.color}" class="legend-item-icon"></span>
          <span class="legend-item-name">{{ item.name }}：</span>
          <span :style="{color:item.color}" class="legend-item-value">{{ item.value }}</span>
        </div>
      </div>
    </div>
  </ui-card>
</template>
<script>
  import PieEchart from './echarts/pie-echart.vue'
  import { mapGetters } from 'vuex'
  export default {
    components: {
      PieEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        labelTypeList: [],
        colors: ['#198FFF', '#20D7FF', '#08CF84', '#FECB33', '#F8775C']
      }
    },
    computed: {
      ...mapGetters({
        getLabelTypeList: 'dictionary/getLabelTypeList' // 标签类型
      })
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.labelTypeList = this.updataLabelType(this.statisticsList)
        },
        immediate: true
      }
    },
    created () {
      this.labelTypeList = []
    },
    methods: {
      /**
       * 处理标签类型占比
       * 1: 设备， 2：人员， 3：物品， 4： 事件， 5： 地址
      */
      updataLabelType (list) {
        const labelTypeList = []
        this.getLabelTypeList.forEach((v, i) => {
          const item = list.find(n => {
            return n.type === v.dataKey
          })
          labelTypeList.push({
            name: v.dataValue,
            value: item ? item.count : 0,
            type: v.dataKey,
            color: this.colors[i]
          })
        })
        return labelTypeList
      },
      lgendHandle (item) {
        const query = {
          params: 'labelList',
          labelType: item.type
        }
        this.$router.push({
          path: '/label-management/label-pool',
          query: {
            tabActive: 'labelList',
            labelInfo: JSON.stringify(query)
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    display: flex;
    flex: 1;
    position: relative;
    .pie-echart {
      width: 50%;
    }
    .legend-ul {
      position: absolute;
      right: 40px;
      top: 50%;
      transform: translate(0, -50%);
      .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        cursor: pointer;
        .legend-item-icon {
          width: 12px;
          height: 12px;
          border: 2px solid #fff;
          border-radius: 50%;
          margin-right: 10px;
        }
        .legend-item-name {
          font-size: 14px;
          line-height: 20px;
          color: #fff;
        }
        .legend-item-value {
          font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
          font-weight: bold;
          font-size: 16px;
          line-height: 22px;
        }
      }
      .legend-item:last-child {
        margin: 0;
      }
    }
  }
</style>