<!--
    * @FileDescription: 人员频次分析
    * @Author: H
    * @Date: 2024/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="frequency-analysis container">
    <mapCustom
      ref="mapBase"
      :allCameraList="allCameraList"
      :basicPoints="basicPoints"
      crashType="Camera_Face"
      @closeMapTool="closeMapTool"
      @gettingData="gettingData"
      mapType="frequency"
      sectionName="face"
    />
    <!-- 左面信息展示框 -->
    <left-box
      ref="leftbox"
      @search="querySearch"
      @seleArea="handleSeleArea"
      @reset="handleReset"
      @searchAnalyse="handleSearchAnalyse"
      @cutAnalyse="handleCutAnalyse"
    ></left-box>
    <right-box
      ref="rightBox"
      v-show="rightShowList"
      @details="handleDetails"
      @cancel="handleCancel"
    >
    </right-box>
    <detailsBox
      ref="detailsBox"
      v-show="detailsShow"
      @oftenHaunt="oftenHaunt"
      @goback="handleGoback"
      @chooseNormalPoint="chooseNormalPoint"
    ></detailsBox>
  </div>
</template>

<script>
import mapCustom from "../../components/map/index.vue";
import leftBox from "./components/leftBox.vue";
import rightBox from "./components/rightBox.vue";
import detailsBox from "./components/details-box.vue";
import { mapMutations } from "vuex";
import { myMixins } from "../../mixins/index.js";
export default {
  name: "",
  mixins: [myMixins],
  components: {
    mapCustom,
    leftBox,
    rightBox,
    detailsBox,
  },
  data() {
    return {
      detailsShow: false,
      rightShowList: false,
      searchData: {},
      basicPoints: [],
    };
  },
  watch: {},
  computed: {},
  created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  mounted() {},
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    querySearch(data) {
      if (this.detailsShow) {
        // 查询时,如果右侧打开了抓拍详情则关闭
        this.detailsShow = false;
      }
      this.searchData = data;
      this.rightShowList = true;
      this.$refs.rightBox.init(data);
    },
    handleReset() {
      // this.resetPoint(0);
      this.rightShowList = false;
      this.rightPeerList = false;
    },
    //
    handleSearchAnalyse(val, item, tabIndex) {},
    handleCutAnalyse() {},
    oftenHaunt(list) {
      //   this.basicPoints = list;
      //   this.$refs.mapBase.sprinklePoint(list);
      this.basicPoints = [];
      this.$refs.mapBase.resetMarkerbasicPoint();
      this.basicPoints = list.map((item) => {
        item.showIconBorder = true;
        item.dataType = "face";
        item.lat = item.geoPoint.lat;
        item.lon = item.geoPoint.lon;
        item.captureAddress = item.deviceName;
        return item;
      });
      this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, true);
      this.$refs.mapBase.setCenter(list[0]);
    },
    //详情列表数据点击展示弹框
    chooseNormalPoint(data) {
      this.basicPoints.forEach((item, index) => {
        if (item.recordId == data.recordId) {
          this.$refs.mapBase.chooseNormalPoint(this.basicPoints, "face", index);
        }
      });
    },
    handleSeleArea() {
      this.$refs.mapBase.mapLayerConfig.mapToolVisible = true;
    },
    handleDetails(item) {
      this.detailsShow = true;
      this.rightShowList = false;
      this.$nextTick(() => {
        this.$refs.detailsBox.init(item, this.searchData);
      });
    },
    closeMapTool() {
      this.$refs.mapBase.mapLayerConfig.mapToolVisible = false;
    },
    // 框选设备列表
    gettingData(value) {
      this.$refs.leftbox.showDevice(value);
      this.$refs.mapBase.clearDraw();
    },
    // 返回
    handleGoback(val) {
      if (val) {
        this.$refs.mapBase.resetMarkerbasicPoint();
        this.basicPoints = [];
        //   关闭频次分析结果详情弹框
        this.$refs.mapBase.closeMapDom();
      } else {
        this.detailsShow = false;
        this.rightShowList = true;
      }
    },
    handleCancel() {
      this.rightShowList = false;
    },
  },
};
</script>

<style lang="less" scoped>
.frequency-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
