<template>
  <div class="capture-box auto-fill">
    <div class="capture-evaluation">
      <div class="capture-outer">
        <div class="reach-title">
          <title-content :title="indexTitle + '评价统计'"></title-content>
          <reach-tabs class="reach-tabs" :tab-list="tabList" @changeTab="changeTab" :tab-num="2"></reach-tabs>
        </div>
        <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: chartDatas }">
          <el-carousel
            v-if="chartDatas.length != 0"
            indicator-position="none"
            ref="carousel"
            class="img-list"
            :arrow="chartRingList.length > 8 ? 'always' : 'never'"
            :autoplay="false"
            :initial-index="echartsIndex"
          >
            <el-carousel-item v-for="index in Math.ceil(chartRingList.length / 8)" :key="index">
              <div class="capture-echarts">
                <div
                  class="echarts-content"
                  v-for="(item, idx) in chartRingList.slice(8 * (index - 1), 8 * index)"
                  :key="idx"
                >
                  <draw-echarts
                    :echart-option="item"
                    :echart-style="ringStyle"
                    ref="zdryChart"
                    class="charts"
                    :echarts-loading="echartsLoading"
                  ></draw-echarts>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>
    <div class="subordinate-statistics" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <div class="subordinate-box" v-if="echartList.length != 0">
        <div class="reach-title">
          <title-content :title="indexTitle + '评价统计'"></title-content>
          <el-popover
            :append-to-body="false"
            popper-class="subordinate-popover"
            placement="bottom"
            trigger="click"
            visible-arrow="false"
            v-model="echartPopver"
          >
            <div class="form-content">
              <Checkbox
                class="mb-sm block check-all"
                :indeterminate="indeterminate"
                :value="checkAll"
                @click.prevent.native="handleCheckAll"
              >
                <span class="base-text-color ml-xs vt-middle">全选</span>
              </Checkbox>
              <div class="popover-list">
                <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                  <Checkbox
                    class="mb-sm block"
                    v-for="(item, index) in this.indexStatisticsVosList"
                    :key="index"
                    :label="item"
                  >
                    <!-- <span
                      class="bg_color ml-xs  vt-middle"
                      :style="'backgroundImage: linear-gradient(' + colorList.itemStyle.normal.colorf.colorStops[0] + ');'"
                    ></span> -->
                    <span
                      class="bg_color ml-xs vt-middle"
                      :style="'backgroundImage: linear-gradient(' + colorList[index] + ');'"
                    ></span>
                    <span class="base-text-color ml-sm vt-middle">{{ item }}</span>
                  </Checkbox>
                </CheckboxGroup>
              </div>
              <div class="t-center btn-popover">
                <Button @click="echartPopver = false"> 取消 </Button>
                <Button class="ml-sm" type="primary" @click="submit()">确 定</Button>
              </div>
            </div>
            <Button slot="reference" class="screen">
              <span class="vt-middle screen-span">
                <i class="icon-font icon-tulishaixuan f-12 mr-sm vt-middle"></i>图例筛选</span
              >
            </Button>
          </el-popover>
        </div>
        <div class="echarts-box">
          <draw-echarts
            :echart-option="echartRing"
            :echart-style="rinStyle"
            ref="carChart"
            class="charts"
            :echarts-loading="echartsLoading"
          ></draw-echarts>
          <span
            v-if="echartList.regionNames.length > comprehensiveConfig.subordinateNum"
            class="next-echart"
            @click="scrollRight('carChart', echartList.regionNames, [], comprehensiveConfig.subordinateNum)"
          >
            <i class="icon-font icon-youjiantou1 f-12"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="evaluation-statement">
      <div class="reach-title">
        <title-content :title="indexTitle + '报表'"></title-content>
        <div class="btns">
          <span class="btn_jk" :class="{ active: curBtn === 1 }" @click="changeBtn(1)"> 简报 </span>
          <span class="btn_kk" :class="{ active: curBtn === 2 }" @click="changeBtn(2)"> 详报 </span>
        </div>
        <Button type="primary" class="btn_search" @click="exportData">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <div class="table-box">
        <ui-table
          class="ui-table capture-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          :disabledHover="true"
          :stripe="false"
        >
        </ui-table>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.subordinate-popover {
  margin-top: 0 !important;
  top: 65px !important;
  left: 1433px !important;
  width: 324px !important;
  height: 270px !important;
  border: none;
  padding: 10px !important;
  background: #051e43 !important;
  position: relative;
  .form-content {
    position: relative;
    width: 100%;
    height: 100%;
    .popover-list {
      width: 100%;
      height: calc(100% - 80px);
      position: relative;
      overflow-y: auto;
      .ivu-checkbox-wrapper {
        margin-right: 0;
      }
      .bg_color {
        min-width: 15px;
        min-height: 15px;
        text-align: center;
        font-weight: bold;
        color: #fff;
        font-size: 14px;
        display: inline-block;
      }
      .firstly1 {
        background-color: #f1b700;
      }
      .check-all {
        width: 100%;
        height: 20px;
        top: 0;
        position: relative;
      }
    }
    .btn-popover {
      height: 60px;
      width: 100%;
      line-height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-top: 1px solid var(--border-color);
      button {
        font-size: 12px;
        height: 28px;
        line-height: 28px;
        width: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .popper__arrow {
    display: none !important;
  }
}
</style>
<style lang="less" scoped>
.capture-box {
  height: 1500px;
  width: 100%;
  .capture-evaluation {
    height: 320px;
    margin-top: 10px;
    background-color: var(--bg-sub-content);
    border-bottom: 1px solid var(--devider-line);
    position: relative;
    .capture-outer {
      height: 320px;
      background-color: var(--bg-sub-content);
      border-bottom: 1px solid var(--devider-line);
      position: relative;
      .reach-title {
        text-align: center;
        position: relative;
        padding-top: 20px;
        width: 100%;
        .reach-tabs {
          position: absolute;
          left: 30px;
          top: 20px;
        }
      }
      .echarts-box {
        height: 260px;
        position: relative;
        .capture-echarts {
          display: flex;
          width: 95%;
          margin: 0 auto;
          justify-content: flex-start;
          align-items: center;
          .echarts-content {
            height: 260px;
            line-height: 260px;
            display: inline-block;
          }
        }

        .charts {
          display: inline-block;
          margin: 0 auto;
          flex: 1;
        }
      }
      @{_deep}.el-carousel__arrow {
        width: 24px;
        height: 24px;
        background: #02162b;
        border: 1px solid #10457e;
        opacity: 1;
        border-radius: 4px;
        top: 100px;
        right: 16px;
        position: absolute;
        text-align: center;
        line-height: 24px;
        &:active {
          width: 24px;
          height: 24px;
          background: #02162b;
          border: 1px solid var(--color-primary);
          opacity: 1;
          border-radius: 4px;
        }
        &:hover {
          width: 24px;
          height: 24px;
          background: #02162b;
          border: 1px solid #146ac7;
          opacity: 1;
          border-radius: 4px;
        }
        .el-icon-arrow-left {
          color: var(--color-primary);
          font-size: 12px;
          &:active {
            color: #4e9ef2;
            font-size: 12px;
          }
          &:hover {
            color: var(--color-primary);
            font-size: 12px;
          }
        }
        .el-icon-arrow-right {
          color: var(--color-primary);
          font-size: 12px;
          &:active {
            color: #4e9ef2;
            font-size: 12px;
          }
          &:hover {
            color: var(--color-primary);
            font-size: 12px;
          }
        }
      }
    }
  }
  .subordinate-statistics {
    height: 440px;
    background-color: var(--bg-sub-content);
    position: relative;
    border-top: 1px solid var(--devider-line);
    .subordinate-box {
      height: 440px;
      background-color: var(--bg-sub-content);
      position: relative;
      border-bottom: 1px solid var(--devider-line);
      .reach-title {
        text-align: center;
        position: relative;
        padding-top: 20px;
        width: 100%;
        .reach-tabs {
          position: absolute;
          left: 30px;
          top: 20px;
        }
      }
      // @{_deep}.ivu-btn{
      //   padding: 0;
      // }
      .screen {
        position: absolute;
        right: 20px;
        top: 20px;
        color: #fff;
        background-color: var(--color-primary);
        // &:hover{
        //   color: ;
        // }
        .screen-span {
          display: inline-block;
          width: 100%;
          height: 100%;
        }
      }
      .echarts-box {
        height: 400px;
        width: 100%;
        position: relative;
        .echarts-content {
          height: 400px;
          line-height: 400px;
          display: inline-block;
        }
        .charts {
          display: inline-block;
        }
        .next-echart {
          width: 24px;
          height: 24px;
          background: #02162b;
          border: 1px solid #10457e;
          opacity: 1;
          border-radius: 4px;
          top: 160px;
          right: 35px;
          position: absolute;
          text-align: center;
          line-height: 24px;

          .icon-youjiantou1 {
            color: var(--color-primary);
            font-size: 12px;
            vertical-align: top !important;
          }
          &:active {
            width: 24px;
            height: 24px;
            background: #02162b;
            border: 1px solid var(--color-primary);
            opacity: 1;
            border-radius: 4px;
            .icon-youjiantou1 {
              color: #4e9ef2;
              font-size: 12px;
              vertical-align: top !important;
            }
          }
          &:hover {
            width: 24px;
            height: 24px;
            background: #02162b;
            border: 1px solid #146ac7;
            opacity: 1;
            border-radius: 4px;
            .icon-youjiantou1 {
              color: var(--color-primary);
              font-size: 12px;
              vertical-align: top !important;
            }
          }
        }
      }
    }
  }
  .evaluation-statement {
    background-color: var(--bg-sub-content);
    border-top: 1px solid var(--devider-line);
    width: 100%;
    padding: 0 20px;
    height: 730px;
    .reach-title {
      text-align: center;
      position: relative;
      width: 100%;
      display: inline-block;
      margin: 30px 0;
    }
    .btns {
      position: absolute;
      right: 130px;
      top: 8px;
      .btn_jk {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
      }
      .btn_kk {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
      span {
        display: inline-block;
        height: 34px;
        width: 54px;
        display: inline-block;
        color: #fff;
        background-color: #12294e;
        border: 1px solid #1b82d2;
        font-size: 14px;
        font-weight: 400;
        text-align: center;
        line-height: 34px;
      }
      .active {
        color: #fff;
        background-color: #2d8cf0;
      }
      span:hover {
        display: inline-block;
        color: #fff;
        background-color: #2d8cf0;
      }
    }
    .btn_search {
      position: absolute;
      right: 2px;
      top: 8px;
      width: 94px;
      text-align: center;
      height: 34px;
      line-height: 34px;
      padding: 0;
    }
    .active {
      color: #fff;
      background-color: #2d8cf0;
    }

    .ui-table {
      height: 100%;
      border: solid 1px #085aa7 !important;
      // overflow-y: auto;
    }
    .table-box {
      height: calc(100% - 91px);
      background-color: hsl(214, 81%, 17%);

      @{_deep}.ivu-table-body {
        overflow-y: auto;
        background-color: #08264d !important;
      }
      @{_deep}.ivu-table-cell {
        padding: 0 15px !important;
      }
      @{_deep}.ivu-table table {
        width: 1730px !important;
        border-collapse: unset !important;
      }
      @{_deep}.ivu-table-fixed-header,
      @{_deep}.ivu-table-header,
      @{_deep}.ivu-table-fixed-right {
        th {
          background: #002f71 !important;
          font-size: 13px;
          font-family: MicrosoftYaHei;
          line-height: 19px;
          color: #8ab9f8;
          opacity: 0.9;
        }
      }
      @{_deep}.ivu-table-header {
        tr {
          border-top: solid 1px #085aa7 !important;
          border-left: solid 1px #085aa7 !important;

          &:first-child {
            th {
              &:last-child {
                border-left: none !important;
              }
              &:first-child {
                border-left: none !important;
                border-bottom: 1px solid #085aa7 !important;
              }
            }
          }
          th {
            border-bottom: 1px solid #085aa7 !important;
            border-left: solid 1px #085aa7 !important;
            // &:last-child {
            //   border-left: none !important;
            // }
          }
        }
      }

      @{_deep}.ivu-table-tbody {
        tr {
          border-top: solid 1px #085aa7 !important;
          border-right: solid 1px #085aa7 !important;
          td {
            border-left: solid 1px #085aa7 !important;
            border-bottom: 1px solid #085aa7 !important;
            &:first-child {
              border-left: none !important;
            }
          }
        }
      }
      // @{_deep}.ivu-table-wrapper-with-border {
      //   border: none !important;
      // }
      // @{_deep}.ivu-table-border:after {
      //   content: '';
      //   width: 0.005208rem;
      //   height: 100%;
      //   position: absolute;
      //   top: 0;
      //   right: 0;
      //   background-color: transparent !important;
      //   z-index: 3;
      // }
    }
  }
}
</style>
<script>
import evaluationreport from '@/config/api/evaluationreport';
import dataZoom from '@/mixins/data-zoom';
import { mapGetters } from 'vuex';

export default {
  name: 'captureBox',
  mixins: [dataZoom],
  data() {
    return {
      echartPopver: false,
      loading: false,
      echartRing: {},
      optionsArr: {},
      ringStyle: {
        width: '210px',
        height: '240px',
      },
      rinStyle: {
        width: '98%',
        height: '350px',
      },
      finishStatus: '',
      tableColumns: [],
      tableData: [],
      curBtn: 1,
      chartDatas: [],
      echartList: [],
      copyEchartList: [],
      xAxisData: [],
      chartRingList: [],
      echartsIndex: 0,
      zoomStart: 0,
      zoomEnd: 100,
      indexTitle: '',
      echartsLoading: false,
      indexList: {},
      indexVal: '',
      targetDataTypeDesc: '',
      indeterminate: false,
      checkAll: true,
      checkAllGroup: [],
      indexStatisticsVosList: [],
      // colorList: [],
      colorList: [
        '#C8AD17,#64570C',
        '#786ADE,#3C356F',
        '#6AB0DE,#35586F',
        '#40B498,#205A4C',
        '#A46ADE,#671DB1',
        '#C6873E,#63441F',
        '#1684E4,#0B4272',
        '#E97575,#753B3B',
        '#F05C2E,#782E17',
        '#0B9B19,#064E0D',
        '#594EE9,#2D2775',
        '#C8AD17,#64570C',
        '#786ADE,#3C356F',
        '#6AB0DE,#35586F',
        '#40B498,#205A4C',
        '#A46ADE,#671DB1',
        '#C6873E,#63441F',
        '#1684E4,#0B4272',
        '#E97575,#753B3B',
        '#F05C2E,#782E17',
        '#0B9B19,#064E0D',
        '#594EE9,#2D2775',
      ],
      colorEnum: [],
      legendsName: '',
    };
  },

  mounted() {
    window.addEventListener('scroll', this.handleScroll, true);
  },

  methods: {
    handleScroll() {
      if (this.curBtn === 1) return;
      let parents = document.getElementsByClassName('capture-table')[0];
      // let children = parents.getElementsByClassName('ivu-table-column-center')[2]
      let textName = parents.getElementsByClassName('textName')[0];
      let dom = textName.getElementsByClassName('ivu-table-cell')[0];

      // let tableHeader = parents.getElementsByClassName('ivu-table-header')[0]
      // let tableTr = tableHeader.getElementsByTagName('tr')[1]
      // let div_len = tableTr.getElementsByTagName('div')
      // let span_len = tableTr.getElementsByTagName('div').length

      let scrollLeft = parents.getElementsByClassName('ivu-table-body')[0].scrollLeft;
      dom.setAttribute(
        'style',
        `
        position : absolute;
        left : calc(45vw - 190px + ` +
          scrollLeft +
          `px );
        top: 16px;
      `,
      );

      // let children5 = parents.getElementsByClassName('ivu-table-column-center')[4]

      // let dom5 = children5.getElementsByClassName('ivu-table-cell')[0]
      // console.log(dom5, scrollLeft)
      // if (scrollLeft < 2790) {
      //   dom5.setAttribute(
      //     'style',
      //     `
      //   position : absolute;
      //   left : calc(60vw - 790px + ` +
      //       scrollLeft +
      //       `px );
      // `
      //   )
      // }
      // for (let i = 0; i < span_len; i++) {
      //   div_len[i].setAttribute(
      //     'style',
      //     `
      //   position : absolute;
      //   left : calc(40vw - 190px + ` +
      //       scrollLeft +
      //       `px );
      // `
      //   )
      // }
    },
    // 导出
    async getExport() {
      let params = {
        indexModule: this.indexVal,
        rootResultIds: this.indexList.rootResultIds,
        dataType: this.indexList.targetDataType,
        selfRegionCode: this.indexList.selfRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationreport.exportReportStatisticsOfSimple, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    async getReportDetail() {
      let params = {
        indexModule: this.indexVal,
        rootResultIds: this.indexList.rootResultIds,
        dataType: this.indexList.targetDataType,
        selfRegionCode: this.indexList.selfRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationreport.exportReportOfDetail, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    // 表格简报
    async getReportStatisticsOfSimple() {
      this.loading = true;
      let data = {
        indexModule: this.indexVal,
        rootResultIds: this.indexList.rootResultIds,
        dataType: this.indexList.targetDataType,
        selfRegionCode: this.indexList.selfRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationreport.getReportStatisticsOfSimple, data);
        res.data.data.headers.shift();
        res.data.data.headers.unshift({
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center',
        });
        (this.tableColumns = res.data.data.headers), (this.tableData = res.data.data.body);

        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    // 表格详报
    async getReportStatisticsOfDetail() {
      this.loading = true;
      let data = {
        indexModule: this.indexVal,
        rootResultIds: this.indexList.rootResultIds,
        dataType: this.indexList.targetDataType,
        selfRegionCode: this.indexList.selfRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationreport.getReportStatisticsOfDetail, data);
        (this.tableColumns = res.data.data.headers), (this.tableData = res.data.data.details);
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    // 环形请求接口
    async getIndexStatistics(val, value, list) {
      this.echartsLoading = true;
      let data = {
        indexModule: val,
        rootResultIds: list.rootResultIds,
        dataType: list.selfDataType,
        selfRegionCode: list.selfRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationreport.getIndexStatistics, data);
        this.chartDatas = res.data.data;
        this.initChart();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    // 多个柱形指标评价统计请求数据
    async getSubIndexStatistics(val, value, list) {
      this.echartsLoading = true;
      this.echartList = [];
      this.copyEchartList = [];
      this.indexStatisticsVosList = [];
      let data = {
        indexModule: val,
        rootResultIds: list.rootResultIds,
        dataType: list.targetDataType,
        selfRegionCode: list.selfRegionCode,
      };
      try {
        let res = await this.$http.post(evaluationreport.getHistogramIndexDetail, data);
        // res.data.data.regionNames = res.data.data.regionNames.map((item) => {
        //   if (item.endsWith('市') || item.endsWith('区'))
        //   item = item.substring(0, item.length - 1)
        //   return item
        // })
        this.echartList = this.$util.common.deepCopy(res.data.data);
        this.copyEchartList = this.$util.common.deepCopy(res.data.data);
        this.indexStatisticsVosList = this.$util.common.deepCopy(this.echartList.legends);
        this.checkAllGroup = this.$util.common.deepCopy(this.echartList.legends);
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    initChart() {
      const colorEnum = [
        ['#C8AD17', '#38483F'],
        ['#786ADE', '#243772'],
        ['#6AB0DE', '#204972'],
        ['#40B498', '#164A60'],
        ['#A46ADE', '#2F3772'],
        ['#C6873E', '#383F49'],
        ['#1684E4', '#0B3E73'],
        ['#E97575', '#413A57'],
      ];
      this.chartRingList = [...this.chartDatas].map((row, index) => {
        let one = {
          title: row.indexName,
          subTitle: row.resultValue,
          data: [{ value: row.resultValue }],
          color: colorEnum[index % colorEnum.length],
        };
        return this.$util.doEcharts.evaluationIndexStatistics(one);
      });
    },
    initRing(type) {
      let seriesArr = [];
      Object.keys(this.echartList.indexValues).forEach((key, index) => {
        const obj = {
          name: this.echartList.legends[index],
          type: 'bar',
          barWidth: '8px',
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: !type
                  ? this.colorList[index].split(',')[0]
                  : this.colorList[this.colorEnum[index]].split(',')[0],
              },
              {
                offset: 1,
                color: !type
                  ? this.colorList[index].split(',')[1]
                  : this.colorList[this.colorEnum[index]].split(',')[1],
              },
            ]),
          },
          data: this.echartList.indexValues[key],
        };
        seriesArr.push(obj);
      });
      let opts = {
        xAxis: this.echartList.regionNames,
        data: seriesArr,
        targetDataTypeDesc: this.targetDataTypeDesc,
      };
      this.echartRing = this.$util.doEcharts.evaluationSubordinateColumn(opts);

      setTimeout(() => {
        this.setDataZoom('carChart', [], this.comprehensiveConfig.subordinateNum);
      });
    },

    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;

      if (this.checkAll) {
        this.checkAllGroup = this.indexStatisticsVosList;
      } else {
        this.checkAllGroup = [];
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.indexStatisticsVosList.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    submit() {
      this.colorEnum = [];
      this.echartsLoading = true;
      // this.echartList = this.copyEchartList.map((item) => {
      //   let one = this.$util.common.deepCopy(item)
      //   one.indexStatisticsVos = item.indexStatisticsVos.filter((it, index) => {
      //     if (this.checkAllGroup.includes(it.strategy)) {
      //       this.colorEnum.push(index)
      //       return it
      //     }
      //   })
      //   return one
      // })
      this.echartList.indexValues = {};
      this.echartList.legends = this.copyEchartList.legends.filter((item, index) => {
        if (this.checkAllGroup.includes(item)) {
          this.colorEnum.push(index);
          return item;
        }
      });
      Object.keys(this.copyEchartList.indexValues).forEach((key, index) => {
        if (this.colorEnum.includes(index)) {
          this.echartList.indexValues[key] = this.copyEchartList.indexValues[key];
        }
      });
      this.initRing(1);
      this.$nextTick(() => {
        this.$refs.carChart.setOption(this.echartRing, true);
      });
      this.echartsLoading = false;
    },
    // 切换指标按钮
    async changeTab(val, value, list) {
      this.indexTitle = value;
      this.indexVal = val;
      this.indexList = list;
      this.targetDataTypeDesc = list.targetDataTypeDesc;
      await this.getReportStatisticsOfSimple();
      await this.getIndexStatistics(val, value, list);
      await this.getSubIndexStatistics(val, value, list);
      this.changeBtn(1);
    },
    changeBtn(val) {
      this.curBtn = val;
      if (val === 1) {
        this.getReportStatisticsOfSimple();
      } else {
        this.getReportStatisticsOfDetail();
      }
    },
    exportData() {
      if (this.curBtn === 1) {
        this.getExport();
      } else {
        this.getReportDetail();
      }
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
    chartList() {
      return Math.ceil(this.chartRingList.length / 8);
    },
    isArrow() {
      return this.chartRingList.length > 8 ? 'always' : 'never';
    },
  },
  props: {
    tabList: {
      required: true,
      default() {
        return [];
      },
    },
  },
  deactivated() {
    window.removeEventListener('scroll', this.handleScroll, true);
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    reachTabs: require('@/views/appraisaltask/evaluationReport/components/reach-tabs.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
