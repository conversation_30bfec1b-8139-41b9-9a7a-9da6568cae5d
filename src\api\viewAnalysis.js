import request from "@/libs/request";
import { icbdStructureService, datacenterService } from "./Microservice";

// 结构化任务列表
export function getStructureJobList(data) {
  return request({
    url: icbdStructureService + "/structure/job/list",
    method: "post",
    data,
  });
}

// 任务详情
export function getTaskDetailById(id) {
  return request({
    url: icbdStructureService + `/structure/job/${id}`,
    method: "get",
  });
}

// 开始任务
export function startJob(id) {
  return request({
    url: icbdStructureService + `/structure/job/${id}/start`,
    method: "get",
  });
}

// 停止任务
export function stopJob(id) {
  return request({
    url: icbdStructureService + `/structure/job/${id}/stop`,
    method: "get",
  });
}

// 下载
export function getFileInfos(id) {
  return request({
    url: icbdStructureService + `/structure/file/original-path/${id}`,
    method: "get",
  });
}

// 删除
export function delJob(id) {
  return request({
    url: icbdStructureService + `/structure/job/${id}`,
    method: "delete",
  });
}

// 子列表
export function getStructureRealTaskList(data) {
  return request({
    url: icbdStructureService + "/structure/task/list",
    method: "post",
    data,
  });
}

// 删除
export function delTask(id) {
  return request({
    url: icbdStructureService + `/structure/task/${id}`,
    method: "delete",
  });
}

// 开始任务
export function startTask(id) {
  return request({
    url: icbdStructureService + `/structure/task/start/${id}`,
    method: "get",
  });
}

// 停止任务
export function stopTask(id) {
  return request({
    url: icbdStructureService + `/structure/task/stop/${id}`,
    method: "get",
  });
}

// restart
export function restartTask(id) {
  return request({
    url: icbdStructureService + `/structure/task/restart/${id}`,
    method: "get",
  });
}

// 保存
export function saveJob(data) {
  return request({
    url: icbdStructureService + `/structure/save`,
    method: "post",
    data,
  });
}

// 更新
export function updateJob(data) {
  return request({
    url: icbdStructureService + `/structure/update`,
    method: "post",
    data,
  });
}

// 查询文件结构化上传文件资源树
export function queryTask(data) {
  return request({
    url: icbdStructureService + `/structure/file/query-task`,
    method: "post",
    data,
  });
}

// 查询文件结构化上传文件资源树
export function queryFile(params) {
  return request({
    url: icbdStructureService + `/structure/file/query-file`,
    method: "get",
    params,
  });
}

// 查询文件结构化上传文件资源树
export function queryRoot(params) {
  return request({
    url: icbdStructureService + `/structure/file/query-root`,
    method: "get",
    params,
  });
}

// 查询结构化任务资源树
export function queryTaskSelect(data) {
  return request({
    url: icbdStructureService + `/structure/task/select`,
    method: "post",
    data,
  });
}

// 查询结构化任务资源树
export function queryTaskRoot(params) {
  return request({
    url: icbdStructureService + `/structure/task/query-root`,
    method: "get",
    params,
  });
}

// 审核
export function applyJob(data) {
  return request({
    url: icbdStructureService + `/structure/job/apply`,
    method: "post",
    data,
  });
}
// 查询设备接入数据量日统计分页列表
export function pageList(data) {
  return request({
    url: datacenterService + `/datacenter/deviceDataStat/pageList`,
    method: "post",
    data,
  });
}

// 解析统计
// 解析任务数统计
export function structureJobCount(data) {
  return request({
    url: icbdStructureService + "/structure/statistics/structureJobCount",
    method: "POST",
    data,
  });
}
// 解析任务状态数统计
export function structureJobStatusCount(data) {
  return request({
    url: icbdStructureService + "/structure/statistics/structureJobStatusCount",
    method: "POST",
    data,
  });
}
// 任务发起人TOP3统计
export function taskInitiationStatistics(data) {
  return request({
    url:
      icbdStructureService + "/structure/statistics/taskInitiationStatistics",
    method: "POST",
    data,
  });
}
// 任务审核状态统计
export function taskReviewStatusStatistics(data) {
  return request({
    url:
      icbdStructureService + "/structure/statistics/taskReviewStatusStatistics",
    method: "POST",
    data,
  });
}
