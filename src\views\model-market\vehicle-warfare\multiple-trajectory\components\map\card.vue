<template>
    <div class="card-outside">
        <div class="within">
            <div class="base-header">
                <!-- <Checkbox v-model="data.checked" @change="changeHandle(data.checked)"></Checkbox> -->
                <img class="avatar" :src="data.originPic || imgPic" />
                <span class="content">
                    <div class="single-content name">{{data.plateNo}}</div>
                    <div class="single-content id-card">{{data.plateColor|commonFiltering(plateColorList)}}
                    </div>
                    <div class="single-content action" @click="handleMore(data)">通行记录
                        <i :class="['iconfont icon-common' ,!data.moreTrajectory?'i-f_a_ddown':'i-f_a_dup']" ></i>
                    </div>
                </span>
                <img class="avatar" :src="data.targetPic || imgPic" />
            </div>
            <div v-show="data.moreTrajectory" class="more-content">
                <div class="total-block">共<span class="total">{{result.length || 0}}</span>条结果</div>
                <div @click="handleSelected(item,index)" v-for="(item,index) in result" :key="index">
                    <position-card :record="item" :index="index+1" :checked="index===currentIndex"></position-card>
                </div>

            </div>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex'
import imgPic from '@/assets/img/default-img/vehicle_default.png'
import PositionCard from './PositionCard'

export default {
    name: 'card',
    components: {
        PositionCard
    },
    props: {
        data: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        ...mapGetters({
            plateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
        })
    },
    data() {
        return {
            imgPic,
            moreTrajectory: false,
            currentIndex: -1,
            result: [],
            total: 17
        }
    },
    methods: {
        changeHandle(val) {
            this.$emit('change-check', val)
        },
        handleMore(obj) {
            this.$emit('get-obj', obj)
            this.result = obj.captureList
            if (obj.moreTrajectory) $pubsub.publish("record-list", this.result);
        },
        handleSelected(item,index) {
            this.currentIndex = index;
            $pubsub.publish("show-lappoints-detail", index, item, this.result)
        },
    },
    mounted() {
        $pubsub.subscribe("reshow-list", index => this.currentIndex = index);
    }
}
</script>
<style lang="less" scoped>
.card-outside {
    box-sizing: border-box;
    padding: 1px 1px 1px 5px;
    background-color: #2d87f9;
    // height: 80px;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    margin-bottom: 5px;

    .within {
        height: 100%;
        background-color: #fff;
        padding: 5px 5px 5px 6px;

        .base-header {
            display: flex;
            align-items: center;

            .avatar {
                display: inline-block;
                width: 70px;
                height: 70px;
                border: 1px solid lightgrey;
                margin-left: 10px;
            }

            .content {
                width: 146px;

                .single-content {
                    text-align: center;
                    margin-top: 10px;
                }

                .name {
                    color: #333333;
                    font-size: 14px;
                    font-weight: bold;
                }

                .id-card {
                }

                .action {
                    font-size: 12px;
                    color: #999999;
                    cursor: pointer;
                }

                .icon-common {
                    margin-left: 1px; 
                    font-size: 12px; 
                    color: #2d87f9
                }
            }
        }

        .more-content {
            padding: 10px;
            max-height: 190px;
            overflow-y: auto;
            .total-block {
                color: #999999;
                margin-bottom: 10px;

                .total {
                    color: #2d87f9;
                }
            }
        }
    }
}
</style>