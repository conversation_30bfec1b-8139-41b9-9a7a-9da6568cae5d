<template>
  <basic-select v-bind="getAttrs" v-on="$listeners">
    <!-- 表格插槽 -->
    <template #qualified="{ row }">
      <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
      <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
    </template>
    <template #deviceId="{ row }">
      <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
        row.deviceId
      }}</span>
    </template>
    <template #delaySipMillSecond="{ row }">
      <span>
        {{ !row.delaySipMillSecond ? '--' : row.delaySipMillSecond }}
      </span>
    </template>
    <template #phyStatus="{ row }">
      <span>
        {{ !row.phyStatus ? '--' : row.phyStatusText }}
      </span>
    </template>
    <template #delayStreamMillSecond="{ row }">
      <span>
        {{ !row.delayStreamMillSecond ? '--' : row.delayStreamMillSecond }}
      </span>
    </template>
    <template #delayIdrMillSecond="{ row }">
      <span>
        {{ !row.delayIdrMillSecond ? '--' : row.delayIdrMillSecond }}
      </span>
    </template>
    <template #videoStartTime="{ row }">
      <span>
        {{ !row.videoStartTime ? '--' : row.videoStartTime }}
      </span>
    </template>
    <!-- 在线状态 -->
    <template #online="{ row }">
      <span
        :class="{
          color_qualified: row.online === '1',
          color_unqualified: row.online === '2',
        }"
      >
        {{ row.online === '1' ? '在线' : row.online === '2' ? '离线' : '' }}
      </span>
    </template>
    <!-- 完好状态 -->
    <template #normal="{ row }">
      <span
        :class="{
          color_qualified: row.normal === '1',
          color_unqualified: row.normal === '2',
        }"
      >
        {{ row.normal === '1' ? '取流及时响应' : row.normal === '2' ? '取流超时响应' : '' }}
      </span>
    </template>
    <!-- 可用状态 -->
    <template #canPlay="{ row }">
      <span
        :class="{
          color_qualified: row.canPlay === '1',
          color_unqualified: row.canPlay === '2',
        }"
      >
        {{ row.canPlay === '1' ? '取流成功' : row.canPlay === '2' ? '取流失败' : '' }}
      </span>
    </template>

    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template #networkingQuality="{ row }">
      <span
        class="check-status"
        :class="[
          row.networkingQuality === '1' ? 'bg-b77a2a' : '',
          row.networkingQuality === '2' ? 'bg-17a8a8' : '',
          row.networkingQuality === '3' ? 'bg-D66418' : '',
        ]"
      >
        {{ getNetworkingQualityDesc(row.networkingQuality) }}
      </span>
    </template>
    <template #tagNames="{ row }">
      <tags-more :tag-list="row.tagList || []"></tags-more>
    </template>
    <!--  兼容历史录像完整率  -->
    <template #description="{ row }">
      <span :class="row.qualified === '1' ? 'sucess' : 'error'">{{ row.description }}</span>
    </template>
    <template slot="outcome" slot-scope="{ row }">
      <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
    </template>
  </basic-select>
</template>
<script>
import errorCodesMixins from './errorCodesMixins';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular';
export default {
  props: {
    qualifiedColorConfig: {},
  },
  mixins: [errorCodesMixins],
  data() {
    return {
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
        online: '',
        normal: '',
        canPlay: '',
      },
    };
  },
  created() {},
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
        formItemData: this.formItemData, // errorCodesMixins.js
        moduleData: this.moduleData, // errorCodesMixins.js
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped>
.sucess {
  color: @color-success;
}

.error {
  color: @color-failed;
}
</style>
