<template>
  <div class="nav-header">
    <div class="nav-body">
      <div class="nav-left">
        <div class="nav-logo fl">
          <img class="logo" v-if="systemConfig.logoUrl" :src="systemConfig.logoUrl" @error="handleError" />
          <i v-else class="iconfontconfigure icon-iVDG inline vt-middle"></i>
        </div>
        <div class="system-name inline vt-middle" @click="goHome" :title='portalTitle'>
          {{ systemConfig.applicationName }}
        </div>
      </div>

      <ui-menu class="nav-menu" :active="activeRouter" mode="horizontal">
        <!-- <menu-item name="home" @selectMenu="selectHome">
          <i class="iconfontconfigure icon-shouye-xuanzhong1 f-16 mr-sm"></i>
          <span class="inline vt-middle">首页</span>
        </menu-item> -->
        <template v-for="(item, index) in getMenuList">
          <menu-item :name="item[menuProp.menuName]" @selectMenu="selectNav" :key="index">
            <i class="iconfontconfigure mr-sm" :class="`icon-${item[menuProp.menuIcon]}`"></i>
            <span class="inline vt-middle">{{ item[menuProp.menuText] }}</span>
          </menu-item>
        </template>
        <template v-if="getOtherMenuList.length">
          <Dropdown class="mr-sm menu-item-more dropdown" @on-click="selectNav">
            <div class="menu-item-more-text">
              更多
              <Icon type="ios-arrow-down"></Icon>
            </div>
            <template #list>
              <DropdownMenu>
                <DropdownItem
                  v-for="(item, index) in getOtherMenuList"
                  :key="index"
                  :name="item[menuProp.menuName]"
                  class="dropdown-item"
                >
                  <i class="iconfontconfigure mr-sm f-14" :class="`icon-${item[menuProp.menuIcon]}`"></i>
                  <span class="inline vt-middle">{{ item[menuProp.menuText] }}</span>
                </DropdownItem>
              </DropdownMenu>
            </template>
          </Dropdown>
        </template>
      </ui-menu>

      <nav-header-right></nav-header-right>
    </div>
  </div>
</template>
<style lang="less" scoped>
[data-theme='dark'] {
  .nav-header {
    background-image: url('../assets/img/navigation-page/nav-header.png');
    @{_deep}.ivu-badge-count {
      background-color: #bc3c19;
    }
  }
}
[data-theme='light'] {
  .nav-header {
    background-image: url('../assets/img/navigation-page/nav-header-light.png');
    @{_deep}.ivu-badge-count {
      background-color: #df6969;
    }
  }
}
[data-theme='deepBlue'] {
  .nav-header {
    background-image: url('../assets/img/navigation-page/nav-header-deep-blue.png');
    @{_deep}.ivu-badge-count {
      background-color: #df6969;
    }
  }
}
.nav-header {
  height: 53px;
  // padding: 0 20px;
  padding-right: 20px;
  background: var(--bg-header);
  background-image: url('../assets/img/navigation-page/nav-header.png');
  background-size: cover;
  // background-size: 100% 100%;
  .icon-iVDG {
    font-size: 36px;
    background-image: var(--color-logo);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .nav-body {
    display: flex;
    width: 100%;
    height: 100%;
  }
  .nav-left {
    height: 100%;
    display: flex;
    align-items: center;
    letter-spacing: 2px;
    img {
      height: 100%;
    }
  }
  .nav-menu {
    margin-left: 100px;
  }

  .nav-title {
    line-height: 53px;
  }
  .nav-logo {
    width: 70px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    // margin-right: 10px;
    .logo {
      height: 100%;
      // width: 100%;
    }
  }
  .arrow {
    transition: all 0.2s linear;
    transform: scale(0.83);
  }
  .system-name {
    line-height: 53px;
    min-width: 272px;
    font-size: 22px;
    font-weight: bold;
    cursor: pointer;
    position: relative;
    background: var(--color-header-name);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    &:hover {
      .arrow {
        transform: rotateZ(180deg);
      }
    }
  }
  .drop-item-active {
    color: #06bde6;
    border-color: #10c0e6;
    background-color: #041e43;
  }
  @{_deep} .ivu-menu {
    z-index: 700;
  }
  .person-pic {
    margin-right: 10px;
  }
  .xiao-xi {
    margin-right: 20px;
  }
  .user-center {
    margin-right: 21px;
  }
  .notice-icon {
    color: #94b3d0;
    margin-right: 10px;
    font-size: 26px;
  }
  .notice {
    position: relative;
    margin-right: 15px;
    &:hover {
      .icon-xiaoxi {
        color: #bec7d2;
      }
    }
    .icon-xiaoxi {
      color: #6e86aa;
    }
    .notice-num {
      padding: 6px;
      text-align: center;
      line-height: 12px;
      display: inline-block;
      font-size: 16px;
      background-color: #f4442d;
      color: #fff;
      border-radius: 17px;
      position: absolute;
      top: 4px;
      left: 15px;
      min-width: 24px;
      transform: scale(0.6);
    }
  }
  .dropdown {
    @{_deep} .ivu-dropdown-item {
      color: var(--color-dropdown-item);
      text-align: left;
    }
    @{_deep} .ivu-select-dropdown {
      background-color: var(--bg-layout-header-tab);
    }
    @{_deep} .ivu-dropdown-item:hover {
      background: var(--bg-select-item-hover);
      color: var(--color-select-item);
    }
    @{_deep} .ivu-dropdown-item-divided {
      border-top: 1px solid var(--devider-line);
      &::before {
        background-color: initial;
      }
    }
  }
  @{_deep} .menu-item-more {
    .ivu-select-dropdown {
      z-index: 1000;
    }
    .ivu-dropdown-rel {
      height: 100%;
    }
    i {
      color: var(--color-primary);
    }
    .menu-item-more-text {
      height: 100%;
      padding: 0 20px;
      display: flex;
      align-items: center;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.7);
      i {
        color: inherit;
      }
      &:hover {
        background: var(--bg-menu-item-hover-horizontal);
        color: #fff;
      }
    }
    .dropdown-item {
      span {
        color: var(--color-dropdown-item);
      }
      &:hover {
        color: var(--color-primary);
        background: rgba(44, 134, 238, 0.2);
        span {
          color: var(--color-dropdown-item-hover);
        }
      }
    }
  }
}
</style>
<script>
import { mapActions, mapGetters } from 'vuex';
import logOutMixin from '@/mixins/logout-mixin';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  mixins: [logOutMixin],
  props: {
    menuList: {
      type: Array,
      required: true,
    }, //菜单列表
    menuProp: {
      type: Object,
      default: () => {
        return {
          menuChildren: 'children',
          menuName: 'name',
          menuText: 'text',
          menuIcon: 'iconName',
        };
      },
    },
  },
  data() {
    return {
      navList: [], //整体路由列表
      firstRoute: '', //一级路由
      activeRouter: '', //路由激活名字,
      mdfPasswordShow: false, //显示修改密码弹框
      mdfUserMessage: {}, //更改弹框的用户信息
      mdfUserMessageShow: false, //显示用户信息弹框
      settingDrop: [],
      titleMaxNun: 9,
    };
  },
  async mounted() {
    this.getPath();
  },
  methods: {
    ...mapActions({
      removeAreaList: 'common/removeAreaList',
    }),
    handleError() {
      this.systemConfig.logoUrl = null;
    },
    selectHome(name) {
      this.$router.push({ name: name });
    },
    // 获取路由来激活某个菜单的active属性
    getPath() {
      this.getActiveRoute(this.$route.name);
    },
    getActiveRoute(name) {
      const router = this.routerList.find((row) => row.name === name);
      if (!router) {
        this.activeRouter = name;
        return false;
      }
      if (router.parentId) {
        const parentRouter = this.routerList.find((row) => row.id === router.parentId);
        this.getActiveRoute(parentRouter.name);
      } else {
        this.activeRouter = router.name;
      }
    },
    async selectNav(name) {
      const menu = this.menuList.find((row) => row.name === name);
      let youYunLoginData;
      if (menu.isExternalLink) {
        switch (menu.name) {
          // 合众 (安全检测)
          case 'http://************:5000/auth/login':
            window.open('http://************:5000/auth/sso?lg_token=95e7a91037fe76b323884ada24aa6b31');
            break;
          // 优云 （运维管理）
          case 'http://*************/#/login':
            youYunLoginData = await this.$http.get(equipmentassets.getExternalLogin, {
              params: {
                externalType: 2,
              },
            });
            window.open(`http://*************/tenant/api/v1/sso/login${youYunLoginData.data.data}`);
            break;
          default:
            window.open(menu.name, '_blank');
            break;
        }
      } else {
        this.$router.push({ name: name });
      }
    },
    goHome() {
      this.$router.push({ name: this.getPortalConfig });
    },
    jumpRoute(item) {
      this.$router.push({ name: item.name });
    },
  },
  watch: {
    // 监听路由变化更改菜单的激活状态
    $route: 'getPath',
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
      routerTreeList: 'permission/getRouterTreeList',
      getRouterChildrenByName: 'permission/getRouterChildrenByName',
      routerList: 'permission/getRouterList',
      cacheRouterList: 'tabs/getCacheRouterList',
      userInfo: 'user/getUserInfo',
      noticeNum: 'websocket/getNoticeNum',
      themeType: 'common/getThemeType',
      getPortalConfig: 'common/getPortalConfig'
    }),
    getMenuList() {
      let arr = this.menuList.slice(0, this.titleMaxNun);
      return arr;
    },
    getOtherMenuList() {
      let arr = this.menuList.length > this.titleMaxNun ? this.menuList.slice(this.titleMaxNun) : [];
      return arr;
    },
    portalTitle() {
      return this.getPortalConfig === 'portal' ? '点击进入门户' : '';
    },
  },
  components: {
    MenuItem: require('@/components/ui-menu/menu-item.vue').default,
    UiMenu: require('@/components/ui-menu/ui-menu.vue').default,
    NavHeaderRight: require('./nav-header-right.vue').default,
  },
};
</script>
