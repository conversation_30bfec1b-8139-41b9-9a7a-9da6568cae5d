.font-size-16 {
  font-size: 16px;
}
.font-size-25 {
  font-size: 25px;
}

.custom-shadow {
  box-shadow: 0 0 20px rgba(8, 138, 213, 0.6) inset;
}

.title-color {
  color: #00f8ff;
}

.text-shadow-blue {
  text-shadow: 0 0 10px #267df3;
}

.icon-bg {
  background: linear-gradient(180deg, #00f8ff 0%, #267df3 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.flex-col {
  display: flex;
  justify-content: center;
  align-items: center;
}

//中间虚线
.vertical-dashed {
  position: absolute;
  height: 9.5%;
  width: 1px;
  border-left: 1px dashed #048598;
  transform: rotate(180deg);
}

.dashed {
  &:before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #7a8c9c;
    border-radius: 50%;
    content: '';
    right: -4px;
    bottom: 0;
  }
}

.homepage {
  position: relative;
  background: url('~@/assets/img/navigation-page/background.png') no-repeat;
  background-size: cover;
  padding: 10px;
  height: calc(100% - 50px);
  .layout-left {
    width: 3%;
    height: 100%;
    position: relative;
    .box1 {
      position: relative;
      width: 100%;
      height: 25%;
      margin-bottom: 8.5px;
      padding: 50% 30% 50% 30%;
      background-color: transparent;

      .box-desc {
        font-size: 20px;
        font-weight: bold;
        color: #127acb;
        opacity: 0.63;
        line-height: 1;
      }
    }

    .box2 {
      position: relative;
      width: 100%;
      height: 73.9%;
      background-color: transparent;
      padding: 400% 30% 400% 30%;

      .box-desc {
        font-size: 20px;
        font-weight: bold;
        color: #127acb;
        opacity: 0.63;
        line-height: 1;
      }
    }
  }

  .layout-right {
    height: 100%;
    margin-left: 3.5%;

    .third-party {
      display: flex;
      justify-content: space-between;
      height: 25%;
      .third-party-view-wrapper {
        width: 32%;
        height: 100%;

        .view-title {
          height: 18%;
          width: 100%;
          margin-bottom: 10px;
        }

        .third-party-view-inner {
          width: 100%;
          height: 77%;
          border: 1px dashed rgba(0, 248, 255, 0.4);
          padding: 9px 9px 0 9px;
          display: flex;
          justify-content: space-between;

          .view-item {
            width: 24%;
            height: 100%;
            background: linear-gradient(0deg, #071b3a 0%, #0e3d85 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .view-item-title {
              font-size: 14px;
              color: #00f8ff;
              text-align: center;
              margin-bottom: 8px;
            }

            .view-item-img {
              height: 73%;
              img {
                height: 100%;
              }
            }
          }
        }
      }

      .third-party-view-keyframe {
        width: 100%;
        position: absolute;
        left: 34.5%;
        top: 3.3%;
      }

      .third-party-converge-wrapper {
        width: 24%;
        height: 100%;

        .title {
          height: 18%;
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;

          .converge-title {
            height: 100%;
            width: 100%;
          }

          .converge-title-left {
            width: 38%;
          }

          .converge-title-right {
            width: 38%;
          }

          .converge-keyframe {
            width: 100%;
            position: absolute;
            left: 47.7%;
            top: 3.3%;
          }

          .converge-keyframe2 {
            width: 100%;
            position: absolute;
            left: 61.9%;
            top: 3.3%;
          }
        }

        .third-party-converge-inner {
          width: 100%;
          height: 77%;
          border: 1px dashed rgba(0, 248, 255, 0.4);
          padding: 9px 9px 0 9px;
          display: flex;
          justify-content: space-between;

          .converge-item {
            width: 32%;
            height: 100%;
            background: linear-gradient(0deg, #071b3a 0%, #0e3d85 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;

            .converge-item-title {
              font-size: 14px;
              text-align: center;
              margin-bottom: 8px;
            }

            .converge-item-img {
              height: 73%;
              img {
                height: 100%;
              }
            }
          }
        }
      }

      .third-party-save-wrapper {
        width: 20%;
        height: 100%;

        .title {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          height: 18%;

          .save-title {
            height: 100%;
            width: 100%;
          }

          .save-keyframe {
            width: 100%;
            position: absolute;
            left: 73.7%;
            top: 3.3%;
          }

          .save-keyframe2 {
            width: 100%;
            position: absolute;
            left: 85.4%;
            top: 3.3%;
          }

          .save-title-left {
            width: 38%;
          }

          .save-title-right {
            width: 38%;
          }
        }

        .third-party-save-inner {
          border: 1px dashed rgba(0, 248, 255, 0.4);
          padding: 9px 9px 0 9px;
          height: 77%;

          .save-inner-title {
            font-size: 14px;
            background: #0d3b81;
            width: 100%;
            height: 20%;
            line-height: 2;
            margin-bottom: 1.9%;
            text-align: center;
          }

          .save-inner-wrapper {
            width: 100%;
            height: 75%;
            display: flex;
            justify-content: space-between;

            .save-item {
              width: 32%;
              height: 100%;
              background: linear-gradient(0deg, #071b3a 0%, #0e3d85 100%);
              //display: flex;
              //flex-direction: column;
              //justify-content: center;
              //align-items: center;

              .save-item-title {
                font-size: 14px;
                color: #00f8ff;
                text-align: center;
                margin-top: 5px;
              }

              .save-item-img {
                height: 78%;
                margin: auto;
                text-align: center;
                img {
                  height: 100%;
                }
              }
            }
          }
        }
      }

      //实战应用
      .third-party-practice-wrapper {
        width: 10%;
        height: 100%;

        .practice-title {
          height: 18%;
          width: 100%;
          margin-bottom: 10px;
        }

        .third-party-practice-inner {
          border: 1px dashed rgba(0, 248, 255, 0.4);
          padding: 9px 9px 0 9px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          height: 77%;

          .practice-item {
            width: 100%;
            height: 50%;
            background: linear-gradient(0deg, #071b3a 0%, #0e3d85 100%);
            display: inline-block;
            text-align: center;

            .practice-item-title {
              font-size: 14px;
              color: #00f8ff;
            }

            .practice-item-img {
              margin-left: 16%;
              height: 70%;
              width: 70%;
              img {
                height: 100%;
              }
            }
          }
        }
      }
    }

    //line
    .line {
      position: relative;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      //height: 2.3%;
      //margin-top: 2%;
      //margin-bottom: 2%;
      height: 9.9%;
      //margin-top: 3.2vh;
      //margin-bottom: 3.2vh;

      .line-title {
        font-size: 14px;
        color: #00f8ff;
      }

      .line-line {
        z-index: 5;
        width: 100%;
        //margin-left: 5%;
        //transform: translate(-15px,-15px);
        position: relative;
        display: flex;
        flex-direction: row;
        align-items: center;
        p {
          white-space: nowrap;
        }
        .line-star {
          margin-left: 10px;
        }
      }
      .line-keyframe1 {
        z-index: 1;
        width: 100%;
        position: absolute;
        left: 15%;
        top: 48%;
      }
      .line-keyframe2 {
        width: 100%;
        position: absolute;
        left: 48%;
        top: 100%;
        z-index: 1;
      }
      .line-keyframe3 {
        width: 100%;
        position: absolute;
        left: 82%;
        top: -1%;
      }
      .line-keyframe4 {
        width: 100%;
        position: absolute;
        left: 10%;
        top: 0%;
      }
      .line-keyframe5 {
        width: 100%;
        position: absolute;
        left: 22%;
        top: 44%;
      }
      .line-keyframe6 {
        width: 100%;
        position: absolute;
        left: 43%;
        top: 0;
      }
      .line-keyframe7 {
        width: 100%;
        position: absolute;
        left: 56%;
        top: 44%;
      }
      .line-keyframe8 {
        width: 100%;
        position: absolute;
        left: 69%;
        top: 0;
      }
      .line-keyframe9 {
        width: 100%;
        position: absolute;
        top: 44%;
        left: 77%;
      }
    }

    //数据质量检测--
    .data {
      height: 23%;
      display: flex;
      justify-content: space-between;

      .data-item {
        height: 100%;
        position: relative;
        width: 30%;
        border: 1px dashed rgba(0, 248, 255, 0.4);
        padding: 12px;
        display: flex;
        justify-content: space-between;

        .item-desc {
          width: 58%;
          height: 100%;
          font-size: 14px;
          border-radius: 8px;
          padding: 15px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .item-title2 {
            color: var(--color-primary);
          }

          .item-label {
            color: #7c89a2;
          }

          .item-value-green {
            color: var(--color-bluish-green-text);
          }

          .item-value-red {
            color: #bc3c19;
          }
        }

        .item-img {
          height: 100%;
          margin-left: 6.5%;
          img {
            height: 100%;
          }
        }
        .item-img-align {
          height: 100%;
          //margin-left: 10%;
          transform: translate(0, -11%);
          img {
            height: 100%;
          }
        }

        .data-keyframe {
          width: 100%;
          position: absolute;
          left: 83.1%;
          top: 74%;
        }

        .item-package {
          position: absolute;
          width: 90px;
          height: 90px;
          right: 8%;
          bottom: -47%;
          img {
            height: 100%;
            width: 100%;
          }
          .package-title {
            font-size: 14px;
            position: absolute;
            color: #00f8ff;
            left: -48px;
            top: 32px;
          }
        }

        //直角虚线
        .angle-dashed {
          position: absolute;
          height: 31px;
          width: 31px;
          border-top: 1px dashed #7a8c9c;
          border-right: 1px dashed #7a8c9c;
          right: 36%;
          bottom: 59%;
        }
        .shujuzhiliang-keyframe {
          width: 100%;
          position: absolute;
          left: 100%;
          top: 50%;
        }
      }
    }

    .vertical-dashed-container {
      .left-dashed {
        left: 17%;
      }

      .medium-dashed {
        left: 50%;
      }

      .right-dashed {
        left: 82%;
      }
    }

    .table {
      height: 32%;
      //margin-top: 4.5%;
      margin-top: 8.6vh;
      display: flex;
      justify-content: space-between;

      .data-table {
        width: 33%;
        height: 100%;
        background: rgba(43, 114, 204, 0);
        border: 1px solid rgba(8, 138, 213, 0.3);
        border-radius: 10px;
        padding: 15px;

        .data-container {
          height: 100%;
          display: flex;
          justify-content: space-between;

          .table-row {
            width: 15%;
            height: 100%;
            &.table-row_flex {
              display: flex;
              width: 100%;
              flex-wrap: wrap;
              > .row-title {
                width: 25%;
              }
            }
            .row-title {
              width: 94px;
              height: 28%;
              text-align: center;

              .row-name {
                font-size: 12px;
                color: rgba(0, 248, 255, 1);
              }
              .row-num {
                font-size: 12px;
                color: rgba(0, 248, 255, 1);
              }
            }

            .row-data {
              font-size: 12px;
              width: 94px;
              height: 72%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              .row-label {
                height: 19%;
                .data-label {
                  margin-left: 5px;
                  color: rgba(124, 137, 162, 1);
                }
              }

              .cell {
                position: relative;
                width: 94px;
                height: 19%;
                text-align: center;
                font-weight: bold;

                .bottom-star {
                  position: absolute;
                  top: 18px;
                  right: 14px;
                }
              }

              .cell-1 {
                color: #00f8ff;
              }

              .cell-2 {
                color: #0e8f0e;
              }

              .cell-3 {
                color: #bc3c19;
              }

              .cell-4 {
                color: var(--color-warning);
              }

              .cell-5 {
                color: #bc3c19;
              }
            }
          }
        }
      }
    }
  }
}
