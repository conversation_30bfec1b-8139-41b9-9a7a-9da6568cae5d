<template>
  <div class="pic-mode-card">
    <div class="pic-mode-card-wrapper">
      <div class="pic-mode-card-img" @click="lookScence">
        <slot name="picMessage"></slot>
        <ui-image :src="cardData[smallImgKey]" />
      </div>
      <div class="pic-mode-card-info">
        <slot name="cardInfo">
          <div class="card-info">
            <p class="f-14" :title="one.value" v-for="(one, index) in cardData.fileList" :key="index">
              <span class="info-label" v-if="'label' in one">{{ one.label }}：</span>
              <span class="info-value" :title="one.value" :class="!one?.value ? 'font-warning' : 'base-text-color'">{{
                one.value ?? (one.render && one.render()) ?? '缺失'
              }}</span>
            </p>
          </div>
        </slot>
      </div>
    </div>
    <slot>
      <div class="pic-mode-card-state f-14">
        <p class="mr-md pic-mode-card-state-p">
          <i class="icon-font icon-shijian mr-xs"></i>
          <span class="inline ellipsis" :title="cardData.time">{{ cardData?.time ?? '未知' }}</span>
        </p>
        <p class="pic-mode-card-state-p f-14">
          <i class="icon-font icon-dizhi mr-xs"></i>
          <span class="ellipsis inline" :title="cardData.address">{{ cardData?.address ?? '未知' }}</span>
        </p>
      </div>
    </slot>
  </div>
</template>

<script>
export default {
  name: 'pic-mode-card',
  props: {
    cardData: {
      type: Object,
      default: () => {},
    },
    imgIndex: {
      type: Number,
    },
    smallImgKey: {
      type: String,
      default: 'imageUrl',
    },
  },
  data() {
    return {};
  },
  methods: {
    // 查看大图
    lookScence() {
      this.$emit('handleLookScence', this.imgIndex);
    },
  },
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>

<style lang="less" scoped>
.pic-mode-card {
  position: relative;
  background: var(--bg-info-card);
  border: 1px solid var(--border-info-card);
  &-wrapper {
    display: flex;
    padding: 10px;
  }
  &-img {
    width: 138px;
    height: 138px;
    cursor: pointer;
    position: relative;
    &:hover {
      .active-wrapper {
        display: block;
      }
    }
  }
  &-state {
    display: flex;
    height: 35px;
    line-height: 35px;
    color: #a6bacd;
    border-top: 1px solid rgba(82, 105, 135, 0.4);
    padding: 0 10px;
    &-p {
      display: flex;
      > span {
        width: calc((100% - 25px));
      }
      &:nth-of-type(1) {
        width: 47%;
      }
      &:nth-of-type(2) {
        width: 52%;
      }
    }
  }
  &-info {
    font-size: 12px;
    color: #8797ac;
    flex: 1;
    margin-left: 10px;
    p {
      margin-top: 6px;
      width: 200px;
    }
  }
}
</style>
