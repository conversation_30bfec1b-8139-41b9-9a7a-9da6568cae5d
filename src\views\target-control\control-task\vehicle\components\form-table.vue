<template>
	<div>
		<Table class="auto-fill table" :columns="columns" border :loading="loading" :data="tableData" max-height="580">
			<template #name="{ index }">
				<Input placeholder="请输入" v-model="tableData[index].name" maxlength="50" class="input-wid"></Input>
			</template>
			<template #plateColor="{ index }">
				<Select placeholder="请选择" v-model="tableData[index].plateColor" transfer>
					<Option v-for="item in licensePlateColorList" :value="item.dataKey" :key="item.dataKey">{{ item.dataValue }}</Option>
				</Select>
			</template>
			<template #plateNo="{ index }">
				<Input placeholder="请输入" v-model="tableData[index].plateNo" maxlength="50" class="input-wid"></Input>
			</template>

			<template #vehicleType="{ index }">
				<Select placeholder="请选择" :class="{filterable: tableData[index].selectVehicle}" v-model="tableData[index].vehicleType" filterable transfer @on-change="selectChange(tableData[index])">
					<Option v-for="item in vehicleClassTypeList" :value="item.dataKey" :key="item.dataKey">{{ item.dataValue }}</Option>
				</Select>
			</template>
			<template #vehicleColor="{ index }">
				<Select placeholder="请选择" v-model="tableData[index].vehicleColor" transfer>
					<Option v-for="item in bodyColorList" :value="item.dataKey" :key="item.dataKey">{{ item.dataValue }}</Option>
				</Select>
			</template>
			<template #vehicleBrand="{ index }">
				<div class="select-tag-button" @click="selectBrandHandle(index)">{{tableData[index].vehicleBrand ? vehicleBrandInfo(tableData[index].vehicleBrand) : '车辆品牌'}}</div>
			</template>
			<template #photoList="{ index }">
				<uiUploadImg ref="uploadImg" uploadUlr v-model="tableData[index].images" size="mini2" :algorithmType="2" @imgUrlChange="imgUrlChange($event, index)" />
			</template>
			<template #dictionaryCode="{ row, index }">
				<div class="btn-tips">
					<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(row, index)" />
				</div>
			</template>
		</Table>
		<div class="add-from" @click="addForm()" v-if="tableData.length < 10">
			<Icon type="md-add" />
		</div>
		<!-- 选择品牌 -->
		<BrandModal ref="brandModal" @on-change="selectBrand" />
	</div>
</template>
<script>
	import { resourceConfigur, queryResourceConfigur } from '@/api/dataGovernance'
	import { imgUpload } from '@/api/number-cube'
	import BrandModal from '@/views/target-control/components/brand-modal.vue'
	import uiUploadImg from '@/components/ui-upload-img/index'
	import { mapGetters, mapActions } from 'vuex'
	export default {
		components: {
			uiUploadImg, BrandModal,
		},
		props: {
			//对应字典
			dictTypedata: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				visible: false,
				loading: false,
				resourceId: '',
				vehicleBrand: '',
				selectVehicle: false,
				columns: [
					{ title: '车牌号码（必填）', slot: 'plateNo', width: 150 },
					{ title: '车牌颜色（必填）', slot: 'plateColor', width: 150 },
					{ title: '车主姓名', slot: 'name' },
					{ title: '车辆类型', slot: 'vehicleType' },
					{ title: '车辆颜色', slot: 'vehicleColor' },
					{ title: '车辆品牌', slot: 'vehicleBrand' },
					{ title: '上传照片', slot: 'photoList', width: 220 },
					{ title: '操作', slot: 'dictionaryCode', width: 70 },
				],
				tableData: [{ selectVehicle: false }],
				images: []
			}
		},
		computed: {
			...mapGetters({
				licensePlateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
				bodyColorList: 'dictionary/getBodyColorList', //车辆颜色
				vehicleBrandList: 'dictionary/getVehicleBrandList', //车辆品牌
				vehicleClassTypeList: 'dictionary/getVehicleTypeList', //车辆类型
				vehicleUseStatus: 'dictionary/getVehicleUseStatus', //车辆使用状态
				vehicleUseNature: 'dictionary/getVehicleUseNature', //车辆使用性质
				searchTypeList: 'dictionary/getSearchTypeList', //检索类型
				genderList: 'dictionary/getGenderList', //性别
				nationList: 'dictionary/getNationList' //民族
			})
		},
		watch: {},
		async created() {
			await this.getDictData();
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			// 删除
			handleDele(row, index) {
				if (this.tableData.length == 1) {
					this.$Message.warning("最少保留一条数据")
					return
				}
				this.tableData.splice(index, 1)
			},
			addForm() {
				this.tableData.push({ selectVehicle: false })
			},
			handleDis(index, item) {
				if (this.tableData[index].sex === 1) {
					if (this.tableData[index].name === 'ccrq') {
						if (item.dataKey == '6') {
							return false
						} else {
							return true
						}
					}
					return false
				} else {
					return true
				}
			},
			vehicleBrandInfo(key) {
				var obj = this.vehicleBrandList.find(item => item.dataKey == key)
				return obj.dataValue
			},
			// 选择品牌
			selectBrandHandle(index) {
				this.$refs.brandModal.show(index)
			},
			/**
			 * 车辆品牌已选择，返回数据
			 */
			selectBrand(arr, index, name) {
				// this.queryParam.plateBrands = list
				// this.tableData[index].vehicleColor = arr[0]
				this.$nextTick(() => {
					// this.tableData[index].vehicleBrand = name
					this.$set(this.tableData[index], 'vehicleBrand', name)
				})
				// this.$forceUpdate()
				// this.vehicleBrand = name
			},
			// 初始化
			show(val) {
				this.visible = true
				this.resourceId = val.id
				this.$nextTick(() => {
					this.loading = true
					let data = {
						resourceId: val.id,
						resourceName: val.resourceName
					}
					queryResourceConfigur(data).then(res => {
						let data = res.data
						// 默认值为否---0否/1是
						this.tableData = data.map(item => {
							let obj = {
								...item,
								resourceId: val.id,
								national: item.national || 0,
								sex: item.sex || 0,
								photoList: item.photoList || 0,
								dictionaryCode: item.dictionaryCode || '',
								cloudSearch: item.cloudSearch || 0
							}
							return obj
						})
						this.loading = false;
					})
				})
			},
			// 确认提交
			confirmHandle() {
				let data = {
					resourceId: this.resourceId,
					configEntityList: this.tableData
				}
				resourceConfigur(data).then(res => {
					this.visible = false
					this.$Message.success(res.msg)
					this.$emit('refreshDataList')
				})
			},
			imgUrlChange(event, index) {
			},
			selectChange(row) {
				console.log('---row:', row)
				row.selectVehicle = true;
			}
		}
	}
</script>
<style lang="less" scoped>
	.config {
		/deep/ .ivu-table-body {
			min-height: 220px;
		}
		/deep/.ivu-table-tbody tr td {
			background-color: #f9f9f9 !important;
		}
		/deep/ .ivu-table-border th {
			border-right: 1px solid #d3d7de !important;
		}
		/deep/.ivu-table td,
		.ivu-table th {
			border-bottom: 1px solid #d3d7de;
		}
		/deep/.ivu-input,
		.ivu-select {
			width: 110px;
		}
	}
	.add-from {
		background: #f9f9f9;
		margin-top: 3px;
		border: 1px solid #e8eaec;
		text-align: center;
		cursor: pointer;
		/deep/ .ivu-icon-md-add {
			color: #909399;
		}
	}
	.btn-tips {
		text-align: center;
	}
	.select-tag-button {
		width: auto;
		max-width: 80px;
		padding: 0 2px;
	}
</style>
