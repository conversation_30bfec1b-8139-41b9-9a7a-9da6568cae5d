import home from '@/config/api/home';
import { mapActions, mapGetters } from 'vuex';

const mixin = {
  props: {
    homePageConfig: {},
  },
  data() {
    return {
      mapCharts: null,
      geoCoordMapData: [],
    };
  },
  async created() {
    await this.viewByParamKey().then((regionCode) => {
      if (!regionCode) {
        this.toHomeConfig();
        return;
      }
      this.initMap(regionCode);
    });
  },
  computed: {
    ...mapGetters({
      getMapItem: 'home/getMapItem',
      getFullscreen: 'home/getFullscreen',
    }),
  },
  methods: {
    ...mapActions({
      setMapItem: 'home/setMapItem',
      setHomeConfig: 'home/setHomeConfig',
    }),
    toHomeConfig() {
      this.$UiConfirm({
        content: '首页未配置行政区划。是否配置行政区划？',
      }).then(() => {
        this.$router.push('/homeconfig');
      });
    },
    async viewByParamKey() {
      try {
        let params = {
          key: 'HOME_PAGE_CONFIG',
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = JSON.parse(data.paramValue);
        this.setHomeConfig(paramValue);
        let regionCode = paramValue.regionCode;
        return regionCode;
      } catch (e) {
        throw new Error('error'); //解决 总弹出 “首页未配置行政区划。是否配置行政区划？”问题
      }
    },
    async initMap(areaCode) {
      // 地图civilCode里面一定是6位的，后端可能返回6位 or 8位
      let reallyAreaCode = areaCode;
      if (!!areaCode && areaCode?.length > 6) {
        reallyAreaCode = areaCode.slice(0, 6);
      }
      let { data } = await this.$http.get(`/json/map-china-config/${reallyAreaCode}.json`);
      this.caculateMapCenter(data);
      this.mapCharts = this.$echarts.init(document.getElementById('map-chart'));
      this.$echarts.registerMap('map', data);
      let mapFeatures = this.$echarts.getMap('map').geoJson.features;
      mapFeatures.forEach((v) => {
        // 地区经纬度
        this.geoCoordMapData.push({
          name: v.properties.name,
          value: v.properties.cp || v.properties.centroid,
          img: `image://${require('@/assets/img/device.png')}`,
          civilCode: v.properties.adcode || v.properties.id,
        });
      });
      this.handleExpectHainan(areaCode);
      //this.clickMapEvent()
      this.scaleMapEvent();
      await this.getIndexMapStatistics().then((data) => {
        let dataObject = {};
        if (!data?.length) return;
        // mapStaticKey 是接口字段取值，每种地图单独配置
        // 地图civilCode里面一定是6位的，后端给的数据data可能是8位的，所以要截取
        data.forEach((item) => (dataObject[item[this.mapStaticKey].slice(0, 6)] = item));
        this.geoCoordMapData.forEach((item) => {
          item.data = dataObject[item.civilCode];
        });
        this.handleExpectHainan(areaCode);
      });
      window.addEventListener('resize', () => this.mapCharts.resize());
    },
    // 单独处理海南地图
    async handleExpectHainan(areaCode) {
      const finalOption = await this.handleMapFunction();
      if (areaCode === '460000') {
        finalOption.series[0].center = [109.844902, 19.0392];
        finalOption.series[0].layoutCenter = ['50%', '50%'];
        finalOption.series[0].layoutSize = '600%';
        finalOption.geo.forEach((item, index) => {
          item.center = [109.844902 + 0.04 * index, 19.0392 - 0.08 * index];
          item.layoutCenter = ['51%', '53%'];
          item.layoutSize = '600%';
        });
      }
      this.mapCharts.setOption(finalOption);
    },
    // 点击地图
    clickMapEvent() {
      this.mapCharts.on('click', async (params) => {
        this.geoCoordMapData.forEach((item, index) => {
          index === params.dataIndex ? (item.selected = true) : (item.selected = false);
        });
        this.setMapItem(this.geoCoordMapData[params.dataIndex]);
        this.mapCharts.setOption(this.handleClickMapFunction);
      });
    },
    // 放大缩小地图(使多个底层样式地图同步缩放)
    scaleMapEvent() {
      this.mapCharts.on('georoam', (params) => {
        let options = this.mapCharts.getOption();
        options.geo.forEach((map) => {
          params?.zoom ? (map.zoom = options.series[0].zoom + 0.01) : null;
          map.center = options.series[0].center;
        });
        this.mapCharts.setOption(options);
      });
    },
    caculateMapCenter(data) {
      // 根据获取的json处理中心点位 （
      data.features.forEach((rw) => {
        /**
         * 有些地区没有中心点位，需要把json中所有地图边界点位得到平均数来获取中心点
         */
        if (!rw.properties.cp && !rw.properties.centroid) {
          let sumLon = 0;
          let sumLat = 0;
          let aveLon = 0;
          let aveLat = 0;
          let length = 0;
          // coordinates不同的区域有可能不止一个
          rw.geometry.coordinates.forEach((i) => {
            i[0].forEach((r) => {
              sumLon += r[0];
              sumLat += r[1];
              length++;
            });
          });
          aveLon = sumLon / length;
          aveLat = sumLat / length;
          rw.properties.cp = [aveLon, aveLat];
        }
      });
    },
  },
  beforeDestroy() {
    if (this.mapCharts) {
      this.$echarts.dispose(this.$refs.chartsRef);
      this.mapCharts = null;
      window.removeEventListener('resize', () => this.mapCharts.resize());
    }
  },
};
export default mixin;
