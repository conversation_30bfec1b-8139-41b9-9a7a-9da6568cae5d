/**
 * 本级对账_联网平台对账
 * @type {string}
 */
export const THIS_LEVEL_BY_GB28181 = 'THIS_LEVEL_BY_GB28181';
/**
 * 本级对账_人脸视图库对账
 * @type {string}
 */
export const THIS_LEVEL_BY_GAT1400_FACE = 'THIS_LEVEL_BY_GAT1400_FACE';
/**
 * 本级对账_车辆视图库对账
 * @type {string}
 */
export const THIS_LEVEL_BY_GAT1400_VEHICLE = 'THIS_LEVEL_BY_GAT1400_VEHICLE';
/**
 * 上下级对账_联网平台对账
 * @type {string}
 */
export const SUPSUB_BY_GB28181 = 'SUPSUB_BY_GB28181';
/**
 * 跨网_资产库对账
 * @type {string}
 */
export const CROSS_NET_BY_DEVICE_INFO = 'CROSS_NET_BY_DEVICE_INFO';
/**
 * 跨网_联网平台对账
 * @type {string}
 */
export const CROSS_NET_BY_GB28181 = 'CROSS_NET_BY_GB28181';
/**
 * 跨网_人脸视图库对账
 * @type {string}
 */
export const CROSS_NET_BY_GAT1400_FACE = 'CROSS_NET_BY_GAT1400_FACE';
/**
 * 跨网_车辆视图库对账
 * @type {string}
 */
export const CROSS_NET_BY_GAT1400_VEHICLE = 'CROSS_NET_BY_GAT1400_VEHICLE';

export const TAB_LIST = [
  { label: '视频监控对账', desc: "联网平台VS资产库", source: '资产库', target: '联网平台', value: THIS_LEVEL_BY_GB28181, icon: 'icon-shujujiance' },
  { label: '人脸卡口对账', desc: "人脸视图库VS资产库",source: '资产库', target: '人脸视图库', value: THIS_LEVEL_BY_GAT1400_FACE, icon: 'icon-renliankakou' },
  { label: '车辆卡口对账', desc: "车辆视图库VS资产库",source: '资产库', target: '车辆视图库', value: THIS_LEVEL_BY_GAT1400_VEHICLE, icon: 'icon-cheliangkakou' },
  { label: '上下级对账', desc: "本级联网平台VS下级联网平台",value: SUPSUB_BY_GB28181, icon: 'icon-cheliangkakou' },
  { label: '资产库对账', desc: "本域资产库VS对比域资产库",source: '资产库', target: '资产库', value: CROSS_NET_BY_DEVICE_INFO, icon: 'icon-shujujiance', data: {} },
  { label: '联网平台对账',desc: "本域联网平台VS对比域联网平台",source: '联网平台', target: '联网平台', value: CROSS_NET_BY_GB28181, icon: 'icon-lianwangpingtaizaixianshuai1', data: {} },
  { label: '人脸视图库对账', desc: "本域人脸视图库VS对比域人脸视图库资产库",source: '人脸视图库', target: '人脸视图库', value: CROSS_NET_BY_GAT1400_FACE, icon: 'icon-renliankakou', data: {} },
  { label: '车辆视图库对账',desc: "本域车辆视图库VS对比域车辆视图库资产库", source: '车辆视图库', target: '车辆视图库', value: CROSS_NET_BY_GAT1400_VEHICLE, icon: 'icon-cheliangkakou', data: {} },
];

/**
 * 顶部tab切换和配置
 * @type {[{tagList: [{component: string, propertyType: number, label: string, id: string},{component: string, propertyType: number, label: string, id: string},{component: string, propertyType: number, label: string, id: string}], code: string, label: string},{tagList: [{component: string, propertyType: number, label: string, id: string}], code: string, label: string},{tagList: [{component: string, propertyType: number, label: string, id: string},{component: string, propertyType: number, label: string, id: string},{component: string, propertyType: number, label: string, id: string},{component: string, propertyType: number, label: string, id: string}], code: string, label: string}]}
 */
export const MENU_LIST = [
  {
    code: 'CurrentLevelCompare',
    label: '本级对账',
    tagList: [
      { label: '联网平台VS资产库', component: 'PlatformCompare', propertyType: 8 ,id: THIS_LEVEL_BY_GB28181, },
      { label: '人脸视图库VS资产库', component: 'FaceCompare', propertyType: 9, id: THIS_LEVEL_BY_GAT1400_FACE, },
      { label: '车辆视图库VS资产库', component: 'VehicleCompare', propertyType: 9,  id: THIS_LEVEL_BY_GAT1400_VEHICLE },
    ]
  },
  {
    code: 'SupSubCompare',
    label: '上下级对账',
    tagList: [
      { label: '本级联网平台VS下级联网平台', component: 'SupSubCompare', propertyType: 9, id: SUPSUB_BY_GB28181 },
    ]
  },
  {
    code: 'CrossNetCompare',
    label: '跨网对账',
    tagList: [
      { label: '本域资产库VS对比域资产库', component: 'CurrentDomainAsset', propertyType: 1, id: CROSS_NET_BY_DEVICE_INFO },
      { label: '本域联网平台VS对比域联网平台', component: 'CurrentDomainCrossNet', propertyType: 8, id: CROSS_NET_BY_GB28181 },
      { label: '本域人脸视图库VS对比域人脸视图库资产库', component: 'CurrentDomainFaceLib', propertyType: 9, id: CROSS_NET_BY_GAT1400_FACE },
      { label: '本域车辆视图库VS对比域车辆视图库资产库', component: 'CurrentDomainVehicleLib', propertyType: 9, id: CROSS_NET_BY_GAT1400_VEHICLE },
    ]
  },
]


/**
 * 同 视频监控设备撤销率 指标配置
 * @type {[{_disabled: boolean, propertyName: string, propertyColumn: string},{propertyName: string, propertyColumn: string},{propertyName: string, propertyColumn: string},{propertyName: string, propertyColumn: string},{propertyName: string, propertyColumn: string},null,null,null,null,null,null,null,null,null,null,null,null]}
 */
export const PROPERTY_LIST = [
  { propertyName: 'deviceId', propertyColumn: '设备编号',},
  { propertyName: 'deviceName', propertyColumn: '设备名称' },
  { propertyName: 'civilCode', propertyColumn: '行政区域代码' },
  { propertyName: 'orgCode', propertyColumn: '组织机构代码' },
  { propertyName: 'address', propertyColumn: '设备安装地址' },
  { propertyName: 'ipAddr', propertyColumn: 'IPV4地址' },
  { propertyName: 'ipv6Addr', propertyColumn: 'IPV6地址' },
  { propertyName: 'macAddr', propertyColumn: 'MAC地址' },
  { propertyName: 'sbdwlx', propertyColumn: '摄像机点位类型' },
  { propertyName: 'sbgnlx', propertyColumn: '摄像机功能类型' },
  { propertyName: 'phyStatus', propertyColumn: '设备物理状态' },
  { propertyName: 'sblwzt', propertyColumn: '设备联网状态' },
  { propertyName: 'isOnline', propertyColumn: '设备是否在线状态' },
  { propertyName: 'port', propertyColumn: '端口' },
  { propertyName: 'longitude', propertyColumn: '经度' },
  { propertyName: 'latitude', propertyColumn: '维度' },
  { propertyName: 'sbcjqy', propertyColumn: '摄像机采集区域' },
]

/**
 * 上下级对账指标
 * @type {{indexType: string, indexModule: string, indexName: string, indexId: string, componentName: string, indexModuleName: string}}
 */
export const VIDEO_DEVICE_REVOCATION = {
  'indexId': '4029',
  'indexModule': '4',
  'indexModuleName': '视频流数据',
  'indexName': '视频监控设备撤销率',
  'indexType': 'VIDEO_DEVICE_REVOCATION',
  'componentName': 'VideoDeviceRevocation',
};

/**
 * 韦恩图 颜色
 * @type {{A: string, B: string, 'A,B': string}}
 */
export const COLOR_MAP = {
  'A': '#384052',
  'B': '#3e4f4a',
  'A,B': '#36723e',
};