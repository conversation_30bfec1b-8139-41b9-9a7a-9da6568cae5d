<template>
  <!-- 离线分析配置 -->
  <div class="config-context">
    <div class="config-title">异常分析配置</div>
    <Form ref="anomalyFrom" label-colon :model="anomalyFormData" :rules="anomalyRuleCustom" :label-width="120">
      <FormItem label="分析内容" prop="content">
        <FormItem class="form-item-box flex-between">
          <Checkbox
            v-model="anomalyFormData.devOffBox"
            class="inline"
            @on-change="checkboxChange($event, '', 'offlineThs')"
          >
            设备离线
          </Checkbox>
        </FormItem>
        <FormItem prop="accOffBox" class="form-item-box flex-between">
          <Checkbox
            v-model="anomalyFormData.accOffBox"
            class="inline"
            @on-change="checkboxChange($event, 'accOffDay', 'freqOffThs', 'accOffBox,serialOffBox')"
          >
            累计离线
          </Checkbox>
          <div>
            <span>累计离线：>=</span>
            <InputNumber v-model="anomalyFormData.accOffDay" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
            <span>天</span>
          </div>
        </FormItem>
        <FormItem prop="serialOffBox" class="flex-label-item no-bottom form-item-box flex-between">
          <Checkbox
            v-model="anomalyFormData.serialOffBox"
            class="inline"
            @on-change="checkboxChange($event, 'serialOffDay', 'freqOffThs', 'accOffBox,serialOffBox')"
          >
            最大连续离线
          </Checkbox>
          <div>
            <span>最大连续离线：>=</span>
            <InputNumber v-model="anomalyFormData.serialOffDay" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
            <span>天</span>
          </div>
        </FormItem>
      </FormItem>
      <FormItem label="设备离线" prop="deviceOffline">
        <div class="form-item-box">
          <InputNumber
            v-model.number="anomalyFormData.offlineCons[0]"
            :min="0"
            :max="24"
            :precision="0"
            class="mr-sm width-mini"
          ></InputNumber>
          <span>点至</span>
          <InputNumber
            v-model.number="anomalyFormData.offlineCons[1]"
            :min="0"
            :max="24"
            :precision="0"
            class="ml-sm mr-sm width-mini"
          ></InputNumber>
          <span>点，连续</span>
          <InputNumber
            v-model.number="anomalyFormData.offlineCons[2]"
            :min="0"
            :max="24"
            :precision="0"
            class="ml-sm mr-sm width-mini"
          ></InputNumber>
          <span>小时无抓拍数据</span>
        </div>
      </FormItem>
      <FormItem label="抓拍数据范围" prop="staDayNum">
        <div class="form-item-box no-bottom">
          <span>近</span>
          <InputNumber v-model="anomalyFormData.staDayNum" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
          <span>天抓拍数量</span>
        </div>
      </FormItem>
    </Form>
    <div class="line mb-lg"></div>
    <div class="config-title">影响分析配置</div>
    <Form ref="influenceFrom" label-colon :model="influenceFormData" :rules="influenceRuleCustom" :label-width="120">
      <FormItem label="影响因素" prop="influencingFactor">
        <div class="flex" v-if="anomalyFormData.devOffBox">
          <AddElementItem
            :class="{ 'mb-lg': anomalyFormData.accOffBox || anomalyFormData.serialOffBox }"
            title="离线分析"
            :list="influenceFormData.offlineThs"
            type-key="DEVICE_OFFLINE_ANA"
            @changeSelectItem="changeSelectItem($event, 'offlineThs')"
          ></AddElementItem>
        </div>
        <div class="flex" v-if="anomalyFormData.accOffBox || anomalyFormData.serialOffBox">
          <AddElementItem
            title="频繁离线"
            :list="influenceFormData.freqOffThs"
            type-key="OFFLINE_ANA"
            @changeSelectItem="changeSelectItem($event, 'freqOffThs')"
          ></AddElementItem>
        </div>
      </FormItem>
      <FormItem label="异常占比阈值" prop="abThVal">
        <div class="form-item-box no-bottom">
          <span>>=</span>
          <InputNumber v-model="influenceFormData.abThVal" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
          <span>%</span>
        </div>
      </FormItem>
      <FormItem label="异常率阈值" prop="abPreval">
        <div class="form-item-box no-bottom">
          <span>>=</span>
          <InputNumber v-model="influenceFormData.abPreval" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
          <span>%</span>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    AddElementItem: require('./add-element-item').default,
  },
  props: {
    factorConfig: {
      type: Object,
    },
    // add: 新增   edit: 编辑
    entryType: {
      type: String,
      default: 'add',
    },
  },
  data() {
    const validateIsAccOffBox = (rule, value, callback) => {
      let { accOffBox, accOffDay } = this.anomalyFormData;
      if (accOffBox && !accOffDay) {
        callback(new Error('请输入'));
      } else {
        callback();
      }
    };
    const validateIsSerialOffBox = (rule, value, callback) => {
      let { serialOffBox, serialOffDay } = this.anomalyFormData;
      if (serialOffBox && !serialOffDay) {
        callback(new Error('请输入'));
      } else {
        callback();
      }
    };
    const validateContent = (rule, value, callback) => {
      let { devOffBox, accOffBox, serialOffBox } = this.anomalyFormData;
      if (!devOffBox && !accOffBox && !serialOffBox) {
        callback(new Error('请选择分析内容'));
      } else {
        callback();
      }
    };
    const validateDeviceOffline = (rule, value, callback) => {
      let offlineCons = this.anomalyFormData.offlineCons;
      if (offlineCons.some((item) => !item && item !== 0)) {
        callback(new Error('请输入'));
      } else if (offlineCons[1] < offlineCons[0]) {
        callback(new Error('开始时间不能小于结束时间'));
      } else if (offlineCons[1] - offlineCons[0] < offlineCons[2]) {
        callback(new Error('连续离线小时数不能大于配置的时间段'));
      } else {
        callback();
      }
    };
    const validateInfluencingFactor = (rule, value, callback) => {
      let { devOffBox, accOffBox, serialOffBox } = this.anomalyFormData;
      let { offlineThs, freqOffThs } = this.influenceFormData;
      if ((devOffBox && offlineThs.length === 0) || ((accOffBox || serialOffBox) && freqOffThs.length === 0)) {
        callback(new Error('请添加影响因素'));
      } else {
        callback();
      }
    };

    return {
      // 异常分析配置
      anomalyFormData: {
        devOffBox: false,
        accOffBox: false,
        accOffDay: null,
        serialOffBox: false,
        serialOffDay: null,
        // 设备离线     8:00 - 24:00  连续 8 小时无抓拍数据
        offlineCons: [null, null, null],
        // 抓拍数据范围
        staDayNum: null,
      },
      anomalyRuleCustom: {
        accOffBox: [
          {
            validator: validateIsAccOffBox,
            trigger: 'change',
          },
        ],
        serialOffBox: [
          {
            validator: validateIsSerialOffBox,
            trigger: 'change',
          },
        ],
        content: [
          {
            required: true,
            validator: validateContent,
            trigger: 'change',
          },
        ],
        deviceOffline: [
          {
            required: true,
            validator: validateDeviceOffline,
            trigger: 'change',
          },
        ],
        staDayNum: [
          {
            required: true,
            message: '请输入抓拍数据范围',
            type: 'number',
            trigger: 'blur',
          },
        ],
      },

      // 影响分析配置
      influenceFormData: {
        offlineThs: [],
        freqOffThs: [],
        abThVal: null,
        abPreval: null,
      },
      influenceRuleCustom: {
        influencingFactor: [
          {
            required: true,
            validator: validateInfluencingFactor,
            trigger: 'change',
          },
        ],
        abThVal: [
          {
            required: true,
            message: '请输入异常占比阈值',
            type: 'number',
            trigger: 'blur',
          },
        ],
        abPreval: [
          {
            required: true,
            message: '请输入异常占比阈值',
            type: 'number',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    formData() {
      return {
        anomalyFormData: this.anomalyFormData,
        influenceFormData: this.influenceFormData,
      };
    },
  },
  watch: {
    formData(val) {
      this.$emit('changeFormData', val);
    },
    factorConfig: {
      handler(val) {
        if (this.entryType === 'edit' && val) {
          this.anomalyFormData = this.$util.common.deepCopy(val.abnormalConfig);
          this.influenceFormData = this.$util.common.deepCopy(val.influenceConfig);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 异常分析配置
    checkboxChange(val, str = '', influencingVal = '', valName = '') {
      if (!val) {
        let keyArr = str.split(',');
        keyArr.forEach((key) => {
          this.anomalyFormData[key] = null;
        });

        // 对应的 影响因素 也清空
        let nameArr = valName.split(',');
        if (nameArr.length === 0 || !nameArr.some((key) => this.anomalyFormData[key] === true)) {
          this.influenceFormData[influencingVal] = [];
        }
      }
      this.$refs.anomalyFrom.validateField('content');
    },
    // 影响配置
    changeSelectItem(data, str = '') {
      this.influenceFormData[str] = data;
      this.$nextTick(() => {
        this.$refs.influenceFrom.validateField('influencingFactor');
      });
    },
    getFormData() {
      return this.formData;
    },
    async handleSubmit() {
      let anomalyValid = await this.$refs.anomalyFrom.validate();
      let influenceValid = await this.$refs.influenceFrom.validate();
      return {
        valid: anomalyValid && influenceValid,
        formData: this.formData,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.width-150 {
  width: 150px;
}
@{_deep} .ivu-checkbox-wrapper {
  color: var(--color-content);
  .ivu-checkbox {
    margin-right: 0;
  }
}
@{_deep} .flex-label-item .ivu-form-item-content {
  .flex;
}
.config-context {
  padding: 32px 71px 0 71px;
  .config-title {
    color: var(--color-sub-title-inpage);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  .line {
    width: 100%;
    height: 0;
    border: 1px dashed var(--border-color);
  }
  @{_deep} .flex-between .ivu-form-item-content {
    display: flex;
    justify-content: space-between;
  }
  .flex-between-item {
    text-align: right;
    .item-1 {
      margin-bottom: 16px;
    }
  }
  @{_deep} .ivu-form-item-label {
    padding-top: 18px;
  }
  @{_deep}.form-item-box {
    margin-bottom: 20px !important;
    background: var(--bg-form-item);
    padding: 8px 20px;
    color: var(--color-content);
    &.no-bottom {
      margin-bottom: 0 !important;
    }
  }
  .ivu-form-item {
    margin-bottom: 20px;
  }
}
</style>
