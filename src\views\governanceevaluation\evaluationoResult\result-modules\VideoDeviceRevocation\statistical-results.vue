<template>
  <div class="statistical-results auto-fill">
    <statistical-List
      ref="StatisticalList"
      :formItemData="formItemData"
      :formData="formData"
      :tableColumns="tableColumns"
      :result-data="resultData"
      :row-class-name="rowClassName"
      @startSearch="startSearch"
      @onSortChange="onSortChange"
    >
      <template #export>
        <Button type="primary" class="button-export mb-sm" @click="openExportModal">
          <i class="icon-font icon-daochu font-white mr-xs"></i>
          <span class="ml-xs">导出</span>
        </Button>
      </template>

      <template #qualified="{ row }">
        <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
          {{ row.qualifiedDesc }}
        </span>
      </template>
      <template #action="{ row, item }">
        <div class="action">
          <slot name="customBtn" :row="row" :btn-info="item">
            <ui-btn-tip
              v-for="(btnItem, btnIndex) in item.btnArr"
              :key="btnIndex"
              class="mr-sm"
              :icon="btnItem.icon"
              :content="btnItem.text"
              @handleClick="$emit(btnItem.emitFun, row)"
            ></ui-btn-tip>
          </slot>
        </div>
      </template>
    </statistical-List>
    <!-- 导出 -->
    <export-data
      ref="exportModule"
      :export-loading="exportLoading"
      :org-region-code="codeKey"
      @handleExport="handleExport"
    >
    </export-data>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import downLoadTips from '@/mixins/download-tips';
import tableColumns from '@/views/governanceevaluation/evaluationoResult/common-pages/statistical-results/util/tableColumns.js';
import detectionResult from '@/config/api/detectionResult';

export default {
  name: 'statistical-results',
  mixins: [dealWatch, downLoadTips],
  props: {
    formItemData: {},
    formData: {},
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    filterColumsMethod: {
      type: Function,
    },
  },
  data() {
    return {
      exportLoading: false,
      tableColumns: [],
      resultData: [],
      searchData: {},
      paramsList: {},
      codeKey: '',
      sortValue: {},
    };
  },
  computed: {},
  created() {
    this.paramsList = this.$route.query;
    this.getTableColumns();
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    // 获取统计列表数据
    async getStatisticalList(isSort = false) {
      try {
        if (isSort) {
          this.$refs.StatisticalList.loading = true;
        }
        let params = {
          // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          ...this.sortValue,
        };
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.resultData = this.$util.common.deepCopy(data);
      } catch (e) {
        console.log(e);
      }
    },
    startSearch(searchParams) {
      this.$set(this, 'searchData', searchParams);
      this.getStatisticalList();
    },
    onSortChange(obj) {
      let { order, key } = obj;
      let val = order.toUpperCase();
      if (val !== 'NORMAL') {
        this.sortValue = {
          sortField: key === 'civilName' ? 'civil_code' : key === 'orgName' ? 'org_code' : '',
          sort: val,
        };
      } else {
        this.sortValue = {};
      }
      this.getStatisticalList(true);
    },
    // 导出
    openExportModal() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    async handleExport(val) {
      try {
        let api = detectionResult.exportStatInfoList;
        let params = {
          batchId: this.paramsList.batchId, // 任务执行批次id
          displayType: this.paramsList.statisticType, // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
          indexId: this.paramsList.indexId, // 指标id
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          qualified: this.searchData.searchData.qualified,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
        };
        // 导出综合数据
        if (val.exportType === 'orgOrRegion') {
          params = {
            ...params,
            ...val.apiParams,
            taskIndexId: this.activeIndexItem.taskIndexId,
          };
          api = detectionResult.exportSynthesisData;
        }
        this.exportLoading = true;
        this.$_openDownloadTip();
        const res = await this.$http.post(api, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.$refs.StatisticalList.init();
    },
    rowClassName(row) {
      const rowCodeKey = this.paramsList.statisticType === 'REGION' ? 'civilCode' : 'orgCode';
      if (row[rowCodeKey] === this.paramsList[this.codeKey] && row.dataType !== '3') {
        return 'active-blue';
      }
      return '';
    },
    getTableColumns() {
      //根据指标获取columns
      let obj = { ...this.paramsList, indexName: this.activeIndexItem.indexName };
      this.tableColumns = tableColumns(obj)[this.paramsList.indexType];
      this.tableColumns = this.tableColumns.map((item) => {
        if (item.isModifyName) {
          item.title = this.activeIndexItem.indexName;
        }
        return item;
      });
      //使用props传入的过滤列的方法
      if (this.filterColumsMethod) {
        this.tableColumns = this.filterColumsMethod(this.tableColumns, this.resultData);
      }
    },
  },
  components: {
    StatisticalList: require('../../ui-pages/statistical-list/index.vue').default,
    ExportData: require('./components/export-data.vue').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-results {
  padding: 10px 12px 0;
}
.monitor-img {
  position: relative;
  width: 56px;
  height: 56px;
  margin: 5px 0;
  cursor: pointer;
}
.span-link {
  color: rgb(43, 132, 226);
  cursor: pointer;
  text-decoration: underline;
}
//@{_deep}.active-blue td {
//  color: #19d5f6 !important;
//}
</style>
