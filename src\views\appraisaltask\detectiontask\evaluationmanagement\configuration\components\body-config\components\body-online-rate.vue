<!-- 人体卡口设备在线率 -->
<template>
  <div class="face-body-online-rate">
    <common-form
      :label-width="labelWidth"
      class="common-form"
      ref="commonForm"
      :module-action="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="waycondiction" class="mt-xs" v-if="['1', '3'].includes(formData.detectMode)">
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0">
              设备可用
            </Checkbox>
            <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.examReportStatus" true-value="1" false-value="0">
              设备未报备
            </Checkbox>
          </div>
        </div>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="labelWidth">
      <FormItem label="时间范围" class="right-item mb-sm" prop="name">
        <Select v-model="formData.timeDelay" class="select-width" placeholder="请选择时间范围" transfer>
          <Option value="5">昨天</Option>
          <Option value="1">当天</Option>
          <Option value="2">最近两天</Option>
          <Option value="3">最近一周</Option>
          <Option value="4">最近30天</Option>
          <Option value="6"> 自定义 </Option>
        </Select>
      </FormItem>
      <FormItem
        label=" "
        :class="[formData.lastHours && formData.lastHours < 24 ? 'mb-md' : 'mb-lg']"
        v-show="formData.timeDelay === '6'"
        prop="lastHours"
        :rules="[{ validator: validateLastHours, trigger: 'change', required: false }]"
      >
        <div class="lastHours-input base-text-color">
          最近<InputNumber
            :min="0"
            :step="1"
            :active-change="false"
            :precision="1"
            v-model="formData.lastHours"
            class="mr-sm ml-sm"
            placeholder="请输入"
          ></InputNumber>
          小时
        </div>
      </FormItem>
      <FormItem
        label=" "
        :class="['mb-sm', !formData.lastHours || formData.lastHours > 24 ? '' : 'mt-minus-sm']"
        v-if="formData.timeDelay === '6'"
      >
        <p class="label-color">说明：该时间区间内如果没有抓拍数据，则设备离线。</p>
      </FormItem>
      <FormItem label="时间区间" class="mb-sm" v-if="formData.timeDelay !== '6'">
        <div class="inspection">
          <div class="row-inspection" v-for="(item, index) in formData.dateRang" :key="index">
            <FormItem>
              <div class="form-row">
                <span class="width-picker">
                  <Select class="time-picker" transfer v-model="item.hourStart" clearable>
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourEnd ? item.hourEnd <= it.value : false"
                      :class="item.hourEnd ? (item.hourEnd <= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
                <span class="color-bule mr-sm ml-sm">—</span>
                <span class="width-picker">
                  <Select class="time-picker" transfer v-model="item.hourEnd" clearable>
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourStart ? item.hourStart >= it.value : false"
                      :class="item.hourStart ? (item.hourStart >= it.value ? 'notCheck' : '') : ''"
                    >
                      {{ it.label }}
                    </Option>
                  </Select>
                </span>
                <span>
                  <span class="addition ml-sm" v-if="formData.dateRang.length - 1 === index" @click="toAdd"
                    ><i class="icon-font icon-tree-add f-16 font-other" title="添加"></i
                  ></span>
                  <span class="cancel ml-sm" v-if="index != 0" @click="toDel(index)"
                    ><i class="icon-font icon-shanchu1 f-14 font-other" title="删除"></i></span
                ></span>
              </div>
            </FormItem>
          </div>
        </div>
        <p class="label-color">说明：该时间区间内如果没有抓拍数据，则设备离线。</p>
      </FormItem>
      <!-- <FormItem label="更新人体在线状态">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem> -->
      <FormItem label="对历史检测结果比对分析">
        <RadioGroup v-model="formData.isDetectContrast">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      labelWidth: 180,
      formData: {
        timeDelay: null,
        dateRang: null,
        lastHours: null, //选中自定义，最近x小时
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: null,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
      },
      ruleCustom: {},
      schemeList: [],
      validateLastHours: (rule, value, callback) => {
        let { timeDelay, lastHours } = this.formData;
        if (timeDelay == '6' && !lastHours) {
          callback(new Error('请输入自定义时间'));
        } else if (timeDelay == '6' && lastHours > 24) {
          callback(new Error('最近时间不能超过24小时'));
        } else {
          callback();
        }
      },
    };
  },
  computed: {},
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        this.schemeList = this.getHour();
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            timeDelay: null,
          };
        }
      },
      immediate: true,
    },
  },
  async created() {},
  methods: {
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    toAdd() {
      this.formData.dateRang.push({ hourStart: null, hourEnd: null });
    },
    // 删除时间区间
    toDel(index) {
      this.formData.dateRang.splice(index, 1);
    },
    async handleSubmit() {
      this.handleParams();

      let modalDataValid = await this.$refs.modalData.validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      return commonFormValid && modalDataValid;
    },
    //处理参数
    handleParams() {
      //时间范围自定义则清空dateRang，其他清空lastHours。后端逻辑优先取dateRang字段
      if (this.formData.timeDelay != 6) {
        this.formData.lastHours = null;
      } else {
        this.formData.dateRang = [{ hourStart: null, hourEnd: null }];
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .lastHours-input .ivu-input-number {
    @{_deep}.ivu-input-number-handler-wrap {
      border-left: 1px solid #10457e;
      border-bottom: 1px solid #10457e;
      a {
        background: #02162b;
        color: var(--color-primary);
        .ivu-input-number-handler-down-inner,
        .ivu-input-number-handler-up-inner {
          color: var(--color-primary);
        }
      }
      .ivu-input-number-handler-down,
      .ivu-input-number-handler-up {
        border-color: #10457e;
      }
    }
  }
}
.face-body-online-rate {
  @{_deep}.select-width,
  .input-width {
    width: 380px;
  }
  .label-color {
    color: #e44f22;
  }
  .color-white {
    color: #fff;
  }
  .color-bule {
    color: #1b82d2 !important;
  }
  .width-picker {
    width: 174px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .inspection {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
  .notCheck {
    color: #56789c;
  }
  .lastHours-input .ivu-input-number {
    width: 100px;
    @{_deep}.ivu-input-number-handler-wrap {
      display: inline-block;
    }
  }
  @{_deep}.drop-tree {
    width: 100% !important;
  }
}
</style>
