<template>
  <div class="checkbox-container">
    <Dropdown trigger="custom" :visible="visible" v-clickoutside="onClickCancel">
      <i @click="visible = !visible">
        <i class="icon-font icon-shaixuan f-16 color-filter"></i>
      </i>
      <DropdownMenu slot="list" @click.stop>
        <div class="checkbox-body d_flex" @click.stop>
          <div class="checkbox-item border-right checkbox">
            <div class="checkbox-header" @click.stop>
              <Radio v-model="isAllsbgnlx" class="f-14 color-blue mb-md ml-20" @on-change="onChangeAll">
                <span>全部</span>
              </Radio>
            </div>
            <RadioGroup class="ml-20" v-model="searchData.sbgnlxList" @on-change="onChangeSbgnlx">
              <div v-for="(item, index) in sbgnlxData" :key="`${item.id}-${index}`" class="radio-wrapper">
                <Radio :title="item.label" :label="item.id">{{ item.label }}</Radio>
              </div>
            </RadioGroup>
          </div>
          <div class="checkbox-item border-right checkbox">
            <div class="checkbox-header" @click.stop>
              <Checkbox
                class="ml-20"
                :indeterminate="indeterminate"
                v-model="checkAll"
                @on-change="handleCheckAll"
              ></Checkbox>
              <span class="f-14 base-text-color color-blue mb-md">全部</span>
            </div>
            <CheckboxGroup
              class="ml-20"
              v-model="searchData.sbdwlxList"
              @on-change="checkAllGroupChange"
              v-for="(child, index) in sbdwlxData"
              :key="`children-${index}`"
            >
              <Checkbox :title="child.label" :label="child.id" class="block mb-xs ellipsis dis-select">{{
                child.label
              }}</Checkbox>
            </CheckboxGroup>
          </div>
        </div>
        <div class="checkbox-footer t-center">
          <Button size="small" class="mr-sm" @click="onClickCancel">取消</Button>
          <Button type="primary" size="small" @click="onClickConfirm">确定</Button>
        </div>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'index-select',
  components: {},
  props: {
    sbgnlxData: {
      default: () => [
        { label: '视频监控', id: '1' },
        { label: '人脸卡口', id: '3' },
        { label: '车辆卡口', id: '2' },
      ],
    },
    sbdwlxData: {
      default: () => [
        { label: '一类点', id: '1' },
        { label: '二三类点', id: '2' },
        { label: '内部监控', id: '4' },
      ],
    },
    value: {},
  },
  data() {
    return {
      selected: '',
      visible: false,
      isAllsbgnlx: false,
      searchData: {
        sbgnlxList: '',
        sbdwlxList: [],
      },

      indeterminate: false,
      checkAll: false,
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.selected = val;
      },
    },
  },
  filter: {},
  created() {},
  beforeDestroy() {
    this.onClickCancel();
  },
  deactivated() {
    this.onClickCancel();
  },
  methods: {
    onChangeAll() {
      this.searchData.sbgnlxList = '';
    },
    onChangeSbgnlx() {
      this.isAllsbgnlx = false;
    },
    handleCheckAll(val) {
      if (val) {
        this.searchData.sbdwlxList = this.sbdwlxData.map((item) => item.id);
      } else {
        this.searchData.sbdwlxList = [];
      }
      this.indeterminate = false;
    },

    checkAllGroupChange(data) {
      if (data.length === this.sbdwlxData.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (data.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    onClickConfirm() {
      this.visible = false;
      this.$emit('input');
      this.$emit(
        'on-change',
        Object.assign({}, this.searchData, {
          sbgnlxList: this.searchData.sbgnlxList ? [this.searchData.sbgnlxList] : [],
        }),
      );
    },
    onClickCancel() {
      this.visible = false;
    },
  },
};
</script>

<style lang="less" scoped>
.color-blue {
  color: var(--color-active);
}
.color-filter {
  color: var(--color-filter-funnel);
  /*  &:hover {
    color: #539aea;
  }*/
}
.border-right {
  border-right: 1px solid var(--border-color);
}
.checkbox-container {
  position: relative;
  display: inline-block;
}

.ml-20 {
  margin-left: 20px;
}
.checkbox-body {
  .checkbox-item {
    width: 150px;
  }
  //.checkbox {
  //  padding: 10px 20px;
  //}
  .checkbox-header {
    width: 100%;
    height: 34px;
    line-height: 34px;
    border-bottom: 1px solid var(--border-color);
  }
}
.ivu-select-dropdown {
  max-height: fit-content;
}
.ivu-checkbox-group,
.radio-wrapper {
  height: 30px;
  line-height: 30px;
}
.checkbox-footer {
  padding: 10px;
  border-top: 1px solid var(--border-color);
}
</style>
