<!--
    * @FileDescription: 绘制地图范围
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-04 14:50:35
-->
<template>
  <custom-modal
    v-model="visible"
    :title="title"
    :r-width="1714"
    :footer="true"
    @onOk="confirmHandle"
    @onCancel="handleCancel"
  >
    <div class="map">
      <div :id="mapId" class="map-center"></div>
      <!-- 框选底部操作兰 -->
      <bottom-tool
        v-if="isEdit"
        @cancelDraw="cancelDraw"
        @selectDraw="selectDraw"
        @clearDraw="clearDraw"
        :drawComplete="drawComplete"
        :noNeedTool="['line', 'close']"
      ></bottom-tool>
      <dele-draw
        ref="deleDraw"
        @dele="handleDele"
        v-show="showDele"
      ></dele-draw>
      <!-- @closeMapTool="closeBottomTool" -->
    </div>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="confirmHandle">确定</Button>
    </div>
  </custom-modal>
</template>

<script>
import * as turf from "@turf/turf";
import mapBase from "@/components/map/index.vue";
import customModal from "@/components/modal";
import BottomTool from "@/components/map/map-tool.vue";
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
import deleDraw from "@/views/model-market/components/map/dele-draw.vue";
import { deepCopy } from "@/util/modules/common";

export default {
  name: "",
  components: {
    mapBase,
    customModal,
    BottomTool,
    deleDraw,
  },
  props: {
    title: {
      type: String,
      default: "地图区域绘制",
    },
    placeFence: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
    // 是否绘制多围栏
    isMulti: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      visible: false,
      placeData: "",
      placeLayer: {
        //场所范围
        placeShop: null,
        placeEdu: null,
        placeGovernment: null,
        placeHouse: null,
        placeMedical: null,
        placeCompany: null,
        placeHotel: null,
        placeTraffic: null,
        placeDefault: null,
      },
      drawComplete: true,
      geometryList: [],
      conditionIndex: -1, // 多框选绘制点
      rightClickPosition: "", // 右键点击位置
      placeOverLayer: null,
      mapMain: null,
      selectAloneWindow: [],
      showDele: false,
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      globalObj: "systemParam/globalObj",
    }),
  },
  mounted() {},
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      // 配置初始化层级
      if (!this.mapMain) {
        this.mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        this.mapMain.init(mapId, data, style);
      }
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = this.mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      this.mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    //绘制多边形场所范围AOI
    setPolygon(aoiData, aoiType, isPlaceArchive) {
      this.removeSetPolygonAll();
      var mapGeometry = new MapPlatForm.Base.MapGeometry(this.mapMain.map);
      if (!this.placeOverLayer) {
        this.placeOverLayer = new NPMapLib.Layers.OverlayLayer(aoiType, false);
        this.mapMain.map.addLayer(this.placeOverLayer);
      }
      // this.placeLayer[aoiType] = new NPMapLib.Layers.OverlayLayer(
      //   aoiType,
      //   false
      // );
      // mapMain.map.addLayer(this.placeLayer[aoiType]);
      for (var i = 0; i < aoiData.data.length; i++) {
        var polygonItem = mapGeometry.getGeometryByGeoJson(
          aoiData.data[i].geometry,
          this.mapMain.map
        );
        polygonItem.setStyle({
          color: aoiData.color, //颜色
          fillColor: aoiData.color, //填充颜色
          weight: 2, //宽度，以像素为单位
          opacity: 1, //透明度，取值范围0 - 1
          fillOpacity: 0.5, //填充的透明度，取值范围0 - 1,
          lineStyle: NPMapLib.LINE_TYPE_DASH, //样式
        });
        // this.placeLayer[aoiType].addOverlay(polygonItem);
        // 在多围栏情况下保存初始围栏
        if (this.isMulti) {
          let polygon = JSON.parse(
            GeoJSON.write(polygonItem, this.mapMain.map)
          ).geometry;
          this.geometryList.push(polygon.coordinates);
        }
        this.placeOverLayer.addOverlay(polygonItem);
        // 计算区域中的点
        let center = polygonItem.getCentroid().toString().split(",");
        // let center = polygonItem.getPath()[0].toString().split(",");
        polygonItem.setData({
          properties: aoiData.data[i].properties,
          center: center,
        });
        const point = new NPMapLib.Geometry.Point(center[0], center[1]);
        this.mapMain.map.panTo(point);
        this.mapMain.map.centerAndZoom(center, 16);
      }
    },
    // 删除所有类型多边形
    removeSetPolygonAll() {
      if (this.placeOverLayer) {
        this.placeOverLayer.removeAllOverlays();
        // mapMain.map.removeOverlay(placeOverLayer);
        // placeOverLayer = null;
      }
      // for (let i in this.placeLayer) {
      //   if (this.placeLayer[i]) {
      //     this.placeLayer[i].removeAllOverlays();
      //     this.placeLayer[i] = null;
      //   }
      // }
    },
    // 底部操作栏
    // 取消框选-地图可移动
    cancelDraw() {
      this.mapMain.cancelDraw();
    },
    // 框选绘制图形
    selectDraw(drawType, index = 0) {
      // 绘制多围栏时不清除所有
      if (!this.isMulti) {
        this.clearDraw();
      }
      if (drawType == "selectDrawCircleByDiameter") {
        drawType = "selectCircle";
      }
      // drawType:drawPolygon,drawRectangle,drawCircle
      this.drawComplete = false;
      this.conditionIndex = index;
      this.mapMain[drawType](
        (points, position, extent, geometry) => {
          if (this.isMulti) {
            let polygon = JSON.parse(
              GeoJSON.write(geometry, this.mapMain.map)
            ).geometry;
            this.geometryList.push(polygon.coordinates);
          } else {
            this.completeDraw(geometry);
          }
          this.drawComplete = true;
        },
        (extent, position) => {
          this.showDele = true;
          this.conditionIndex = index;
          let geoPoint = {
            lat: position.lat,
            lon: position.lon,
          };
          this.rightClickPosition = position;
          this.deleResultItem(geoPoint);
        },
        false,
        { ...this.drawStyle },
        index
      );
    },
    clearDraw() {
      // let overlayLayer = mapMain.map.getLayerByName("featureLayer");
      // if (overlayLayer) {
      //   overlayLayer.removeAllOverlays();
      // }
      this.geometryList = [];
      this.mapMain.clearDraw();
      this.mapMain.removeRectangle();
      this.removeSetPolygonAll();
      this.$emit("clear");
    },
    // 删除按钮弹出框
    deleResultItem(pointItem, centerPoint) {
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon || pointItem.geoPoint.lon,
        pointItem.lat || pointItem.geoPoint.lat
      );
      // mapMain.map.centerAndZoom(point, 17)
      // mapMain.map.setCenter(centerPoint);
      const opts = {
        width: 74, // 信息窗宽度
        height: 32, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(0, 0), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      const dom = this.$refs.deleDraw.$el;
      infoWindow.setContentDom(dom);
      this.mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      this.selectAloneWindow.push(infoWindow);
    },
    // 删除单一框
    handleDele() {
      let overlayLayer = this.mapMain.map.getLayerByName("featureLayer");
      let arr = overlayLayer.getOverlaysArry();
      for (let i = 0; i < arr.length; i++) {
        let result = FeatureFacatroy.isGeometryIntersects(
          arr[i],
          this.rightClickPosition,
          this.mapMain.map
        );
        if (result) {
          overlayLayer.removeOverlay(arr[i].id);
          this.selectAloneWindow.forEach((row) => {
            row.close();
          });
          break;
        }
      }
    },
    async show() {
      this.visible = true;
      await this.getMapConfig();
      this.geometryList = [];
      this.placeData = "";
      this.$nextTick(() => {
        this.clearDraw();
        // 场所档案围栏
        if (this.placeFence) {
          // 该方法中对地图中心的和层级做了处理，所以不走configDefaultMap方法
          this.setPolygon(this.placeFence, this.placeFence.layerType, true);
        } else {
          this.configDefaultMap();
        }
      });
      // this.$refs.mapBase.removeSetPolygonAll();
      // this.$refs.mapBase.setPolygon(
      //   this.placeFence,
      //   this.placeFence.layerType,
      //   true
      // );
    },
    selectlist(value) {
      this.deviceIdList = value;
    },
    confirmHandle() {
      if (this.isMulti) {
        // 多围栏框选
        if (this.geometryList.length > 0) {
          let polygonArr = [];
          // 取绘制并集
          for (let i = 0; i < this.geometryList.length; i++) {
            try {
              let poly = turf.polygon(this.geometryList[i]);
              polygonArr.push(poly);
            } catch (e) {
              this.geometryList[i].forEach((item) => {
                polygonArr.push(turf.polygon(item));
              });
            }
          }
          let result = polygonArr.reduce((prev, curr) => {
            return turf.union(prev, curr);
          });
          let data = null;
          if (result.geometry.type !== "MultiPolygon") {
            // 为了符合后端参数需求需要改成多多边形的格式
            data = {
              type: "MultiPolygon",
              coordinates: [result.geometry.coordinates],
            };
          } else {
            data = deepCopy(result.geometry);
          }

          this.$emit("completeDraw", JSON.stringify(data));
          this.visible = false;
        } else {
          this.$Message.warning("请框选场所区域");
        }
      } else {
        // 单围栏框选
        if (this.placeData) {
          this.$emit("completeDraw", this.placeData);
          this.visible = false;
        } else {
          this.$Message.warning("请框选场所区域");
        }
      }
    },
    handleCancel() {
      this.visible = false;
      this.$emit("close");
    },
    drawClear() {
      this.$refs.mapBase.removeSetPolygonAll();
    },
    completeDraw(value) {
      let result = JSON.stringify(
        JSON.parse(GeoJSON.write(value, this.mapMain.map)).geometry
      );
      this.placeData = result;
    },
  },
  beforeDestroy() {
    if (this.mapMain) {
      this.mapMain.destroy();
      this.mapMain = null;
    }
  },
};
</script>

<style lang="less" scoped>
.map {
  height: 640px;
  position: relative;
  .map-center {
    width: 100%;
    height: 100%;
    position: relative;
  }
}
</style>
