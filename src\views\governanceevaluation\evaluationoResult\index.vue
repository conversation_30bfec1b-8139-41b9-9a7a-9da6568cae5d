<template>
  <Row class="detection-Result">
    <Col span="3" class="left-content" v-ui-loading="{ loading: indexSelectLoading, tabldData: indexList }">
      <index-select
        class="mt-sm"
        :data="indexList"
        v-model="activeValue"
        @change="clickIndex"
        :default-active-collapse="defaultActiveCollapse"
      >
        <template #front>
          <div
            @click="goPandect"
            title="评测总览"
            class="front-slot base-text-color pointer"
            :class="componentName == 'EvaluationPandect' ? 'active' : ''"
          >
            <span class="icon-font inline icon-pingcezonglan f-14"></span>
            <span class="ml-sm ellipsis">评测总览</span>
          </div>
        </template>
      </index-select>
    </Col>
    <Col span="21" class="right-content">
      <component :is="componentName" @openIndexId="clickIndex" :active-index-item="activeIndexItem"></component>
    </Col>
  </Row>
</template>
<script>
import { mapGetters } from 'vuex';
import { allIndexType } from '@/views/governanceevaluation/evaluationoResult/util/IndexTypeConfig.js';
import detectionResult from '@/config/api/detectionResult';
import dealWatch from '@/mixins/deal-watch';
import evaluationreport from '@/config/api/evaluationreport';
export default {
  mixins: [dealWatch],
  name: 'evaluationoResult',
  data() {
    return {
      activeValue: '',
      indexList: [],
      activeIndexItem: null,
      componentName: '',
      activeTaskSchemeId: '',
      defaultActiveCollapse: '',
      IndexSelectIcon: Object.freeze({
        视图基础数据: {
          iconName: 'icon-shitujichushuju',
        },
        视频流数据: {
          iconName: 'icon-shipinliushuju-copy',
        },
        车辆视图数据: {
          iconName: 'icon-cheliangshitushuju',
        },
        平台可用性指标: {
          iconName: 'icon-pingtaikeyongxingzhibiao',
        },
        人脸视图数据: {
          iconName: 'icon-renlianshitushuju',
        },
        档案数据: {
          iconName: 'icon-danganshuju',
        },
        重点人员数据: {
          iconName: 'icon-zhongdianrenyuanshuju',
        },
        场所数据指标: {
          iconName: 'icon-changsuoshujuzhibiao',
        },
        人体视图数据: {
          iconName: 'icon-rentishitushuju',
        },
      }),
      refreshKey: 0,
      indexSelectLoading: false,
      taskConfig: {},
      showResult: null,
    };
  },
  activated() {
    // this.$router.push({
    //   name: 'evaluationoResult',
    //   query: JSON.parse(window.sessionStorage.getItem('resultDefault')),
    // })
    this.startWatch(
      '$route.query',
      async () => {
        if (this.$route.name === 'evaluationoResult' && Object.keys(this.$route.query).length === 0) {
          this.componentName = 'EvaluationPandect';
          this.activeIndexItem = null;
          this.$router.push({
            name: 'evaluationoResult',
            query: JSON.parse(window.sessionStorage.getItem('resultDefault')),
          });
          return;
        }
        if (this.$route.name !== 'evaluationoResult' || !this.$route.query.taskSchemeId) return;

        this.showResult = window.sessionStorage.getItem('resultDefault')
          ? JSON.parse(window.sessionStorage.getItem('resultDefault'))?.showResult
          : null;

        // 存储导航url参数，来回切换的时候保存
        window.sessionStorage.setItem('resultDefault', JSON.stringify(this.$route.query));
        let resultDefault = JSON.parse(window.sessionStorage.getItem('resultDefault'));
        // 如果任务跟现在的任务不同就获取指标列表
        if (this.activeTaskSchemeId !== resultDefault.taskSchemeId || this.getIsRefresh) {
          this.activeTaskSchemeId = resultDefault.taskSchemeId;
          this.activeValue = '';
          await this.getOptionalResultsByTaskType();
          await this.getOptionalBatchIdsByTaskSchemeId();
        } else if (this.showResult !== resultDefault.showResult) {
          await this.getOptionalBatchIdsByTaskSchemeId();
        }
        // 路由变化:找到对应指标
        await this.findCollapseFunc();
      },
      { deep: true, immediate: true },
    );
  },
  computed: {
    ...mapGetters({
      getIsRefresh: 'governanceevaluation/getIsRefresh',
    }),
  },
  methods: {
    //获取配置 -- 直接打开页面，query中不存在showResult字段值，接口入参需要该字段
    async getOptionalBatchIdsByTaskSchemeId() {
      try {
        let res = null;
        let { showResult } = this.$route.query;
        if (showResult) {
          res = await this.$http.get(evaluationreport.getOptionalBatchIdsByTaskSchemeId, {
            params: {
              taskSchemeId: this.activeTaskSchemeId,
            },
          });
        } else {
          res = await this.$http.post(evaluationreport.getOptionalBatchIdsByTaskSchemeIdV2, {
            taskSchemeId: this.activeTaskSchemeId,
            showResult: 1,
          });
        }
        let data = res.data.data || {};
        this.taskConfig = data || {};
      } catch (err) {
        console.log(err);
      }
    },
    // 下拉根据考核任务查询
    async getOptionalResultsByTaskType() {
      try {
        this.indexSelectLoading = true;
        let { showResult } = this.$route.query;
        const {
          data: { data },
        } = await this.$http.get(detectionResult.getNavigationInfo, {
          params: {
            taskSchemeId: this.activeTaskSchemeId,
            showResult: showResult ? null : 1, // 直接打开页面，query中不存在showResult字段值，接口入参需要该字段
          },
        });
        this.indexList = [];
        for (let key in data) {
          let children = data[key] || [];
          // 数据扁平化处理，过滤有componentName的数据
          let delayeringList = Object.values(allIndexType)
            .flat()
            .filter((item) => item.componentName);
          // 过滤由前端控制显示的指标id
          const staticIndexIds = delayeringList.map((item) => item.indexId);
          // 可以显示的指标
          let filterIndexData = children.filter(
            (item) => staticIndexIds.includes(item.indexId) || item.indexType === 'COMPOSITE_INDEX',
          );
          // 将前后端的数据做一个整合
          const integrationData = filterIndexData.map((item) => {
            // 复合指标的indexId动态生成，需特殊处理
            if (item.indexType === 'COMPOSITE_INDEX') {
              return {
                componentName: 'NewIndicators',
                ...item,
              };
            }
            const staticObj = delayeringList.find((doneItem) => doneItem.indexId === item.indexId);
            return {
              ...staticObj,
              ...item,
            };
          });
          this.indexList.push({
            indexId: data[key][0].indexModule,
            children: integrationData,
            indexName: key,
            iconName: this.IndexSelectIcon[key].iconName,
          });
          /**
           * 通过componentName判断是否显示，
           */
          // let doneIndexIndexList = Object.values(allIndexType).flat().filter((item) => item.componentName)
          // let doneIndex = children.filter((item) => {
          //   return (
          //     doneIndexIndexList.findIndex(
          //       (value) => value.indexId === item.indexId
          //     ) !== -1
          //   )
          // })
          // this.indexList.push({
          //   indexId: data[key][0].indexModule,
          //   children: doneIndex,
          //   indexName: key,
          //   iconName: this.IndexSelectIcon[key].iconName,
          // })
          // console.log(this.indexList,'indexList')
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.indexSelectLoading = false;
      }
    },
    goPandect() {
      let { orgCode, regionCode, statisticType, taskSchemeId, showResult } = this.$route.query;
      this.componentName = 'EvaluationPandect';
      // this.componentName = () => import(/* webpackChunkName:'resultModules' */ `./result-modules/EvaluationPandect/index.vue`)
      this.defaultActiveCollapse = '';
      this.activeIndexItem = null;
      this.activeValue = '';
      this.$router.push({
        name: 'evaluationoResult',
        query: {
          showResult: showResult,
          orgCode,
          regionCode,
          statisticType,
          taskSchemeId,
        },
      });
    },
    // 点击左侧/评测结果点击详情
    // 需要改变url参数
    clickIndex(item) {
      // 先卸载组件
      if (!item.batchId) {
        //return this.$Message.warning('当前选中指标暂无数据')
      }
      this.componentName = null;
      // let one = allIndexTypeObject[item.indexId]
      this.toChangeRouterUrl({
        indexId: item.indexId,
        indexType: item.indexType,
        batchId: item.batchId,
      });
    },
    toChangeRouterUrl(params) {
      // 设置默认初始值（为了给其余组件默认）- 放到route
      let defaultQuery = {
        ...this.$route.query,
      };
      defaultQuery?.examineTime ? delete defaultQuery.examineTime : ''; // 【结果比对】的时间
      defaultQuery?.canPlay ? delete defaultQuery.canPlay : ''; // 排除取流(截图)失败设备 筛选项
      Object.assign(defaultQuery, params);
      this.$router.push({
        name: 'evaluationoResult',
        query: defaultQuery,
      });
    },
    /**
     * 动态注册result-modules文件夹下面的组件
     */
    changeIndex(indexId, item) {
      // 如果更换指标先卸载组件重新加载
      if (indexId !== this.activeValue) {
        this.componentName = null;
      }
      this.activeIndexItem = { ...item, taskConfig: this.taskConfig };
      this.activeValue = indexId;
      this.$nextTick(() => {
        // 如果找到对应指标详情就展示，找不到先展示随便一个(为了先默认展示)
        let name = item.componentName ? item.componentName : '';
        //this.componentName = () => import(/* webpackChunkName:'resultModules' */ `./result-modules/${allIndexTypeObject[indexId].componentName}/index.vue`)
        name ? (this.componentName = require(`./result-modules/${name}/index.vue`).default) : null;
      });
    },
    findCollapseFunc() {
      // 左侧Collapse默认展开
      if (!this.indexList.length) return;
      let allIndexList = [];
      this.indexList.forEach((item) => {
        allIndexList.push(...item.children);
      });
      if (this.$route.query.indexId) {
        let isDefaultIndexItem = allIndexList.find((item) => item.indexId == this.$route.query.indexId);
        if (this.$route.query.batchId) {
          this.changeIndex(this.$route.query.indexId, {
            ...isDefaultIndexItem,
            batchId: this.$route.query.batchId,
          });
        } else {
          this.changeIndex(this.$route.query.indexId, isDefaultIndexItem);
        }
        this.defaultActiveCollapse = isDefaultIndexItem.indexModule;
      } else {
        //没有indexId直接跳转评测总览
        this.goPandect();
      }
    },
  },
  beforeDestroy() {
    window.sessionStorage.removeItem('resultDefault');
  },
  components: {
    IndexSelect: require('./components/index-select').default,
    EvaluationPandect: require('./result-modules/EvaluationPandect/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .detection-Result {
    .left-content {
      border-right: 1px solid var(--border-color);
      .front-slot {
        color: rgba(0, 0, 0, 0.8);
        .icon-font {
          background: var(--color-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .active {
        background: var(--color-primary);
        color: #ffffff;
        .icon-font {
          background: #ffffff;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
    }
    .right-content {
    }
  }
}
.detection-Result {
  height: 100%;
  background: var(--bg-content);

  .left-content {
    height: 100%;
    overflow: hidden;
    .front-slot {
      height: 38px;
      line-height: 38px;
      padding: 0 15px;
    }

    .active {
      background: var(--color-primary);
    }
  }
  .right-content {
    height: 100%;
  }
}
</style>
