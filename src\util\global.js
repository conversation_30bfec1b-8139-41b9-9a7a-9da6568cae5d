/**
 * 检测状态目前为4位数字
 * 1、是否检测（0已检测1未检测）
 * 2、基础信息类（0合格1不合格）
 * 3、视图类（0合格1不合格）
 * 4、视频流类（0合格1不合格）
 */
const checkStatusList = [
  {
    label: '待检测',
    value: '1000',
  },
  {
    label: '合格',
    value: '0000',
  },
  {
    label: '不合格',
    value: '0',
  },
];
const errorTypeList = [
  {
    label: '基础信息',
    value: 'BASICS',
  },
  {
    label: '图片信息',
    value: 'IMAGE',
  },
  {
    label: '视频流信息',
    value: 'VIDEO',
  },
];
/**
 * 根据异常类型过滤检测状态
 * @param {异常类型} type
 * @param {检测状态} checkStatuses
 */
export function dealCheckStatusWithErrorType(checkStatuses, type) {
  let status = null;
  switch (type) {
    case '1':
      status = '0100';
      break;
    case '2':
      status = '0010';
      break;
    case '3':
      status = '0001';
      break;
  }
  return dealCheckStatus(checkStatuses, status);
}

function dealCheckStatus(checkStatuses, status = null) {
  let statusList = [...checkStatuses];

  !statusList.length && (statusList = ['1000', '0000', '0001', '0010', '0100', '0110', '0011', '0101', '0111']);
  const index = statusList.findIndex((row) => row === '0');
  if (status) {
    index !== -1 && statusList.splice(index, 1, status);
    statusList.push('0111');
    if (status === '0100') {
      statusList.push('0110');
      statusList.push('0101');
    } else if (status === '0010') {
      statusList.push('0110');
      statusList.push('0011');
    } else if (status === '0001') {
      statusList.push('0011');
      statusList.push('0101');
    }
  } else {
    index !== -1 &&
      statusList.splice(index, 1) &&
      (statusList = [...statusList, ...['0001', '0010', '0100', '0110', '0011', '0101', '0111']]);
  }
  return statusList;
}
const filedEnum = {
  deviceId: '设备编码',
  deviceName: '设备名称',
  sbdwlx: '监控点位类型',
  macAddr: 'MAC地址',
  ipAddr: 'IP地址',
  sbgnlx: '摄像机功能类型',
  sbgnlxExt: '摄像机功能类型扩展',
  longitude: '经度',
  latitude: '纬度',
  sbcjqy: '摄像机采集区域',
  phyStatus: '设备状态',
  phyStatusExt: '设备状态扩展',
};

//指标类型
const indexTypeList = [
  { id: 1, title: '视图基础数据指标', icon: 'icon-shitujichushuju' },
  { id: 2, title: '人脸视图数据指标', icon: 'icon-renlianshitushuju' },
  { id: 3, title: '车辆视图数据指标', icon: 'icon-cheliangshitushuju' },
  { id: 4, title: '视频流数据指标', icon: 'icon-shipinliushuju' },
  { id: 5, title: '重点人员数据指标', icon: 'icon-zhongdianrenyuanshuju' },
  { id: 6, title: '档案数据指标', icon: 'icon-danganshujuzhibiao' },
  { id: 7, title: '平台可用性指标', icon: 'icon-pingtaikeyongxingzhibiao' },
  { id: 8, title: '场所数据指标', icon: 'icon-changsuoshujuzhibiao' },
  { id: 9, title: '人体视图数据指标', icon: 'icon-rentishitushuju' },
];

/**
 * message = "请求服务类型：0评测服务（默认） 1资产服务 2视频流播放服务"
 */
export const SERVER = {
  app: 0,
  asset: 1,
  stream: 2,
};

/**
 * 统计模式
 */
const STATISTICAL_MODAL = {
  SMODE_DEVICETAG: 3, //统计模式：设备标签
  SMODE_REGION: 2, //统计模式：行政区划
  SMODE_ORG: 1, //统计模式：组织机构
};
export default {
  checkStatusList,
  errorTypeList,
  dealCheckStatusWithErrorType,
  dealCheckStatus,
  filedEnum,
  indexTypeList,
  SERVER,
  STATISTICAL_MODAL
};
