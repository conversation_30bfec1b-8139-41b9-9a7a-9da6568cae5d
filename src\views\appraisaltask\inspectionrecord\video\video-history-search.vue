<template>
  <div class="search">
    <ui-label label="组织机构" :width="70" class="inline">
      <api-organization-tree
        :treeData="treeData"
        :taskObj="taskObj"
        :select-tree="formValidate"
        placeholder="请选择组织机构"
      >
      </api-organization-tree>
    </ui-label>
    <ui-label label="设备名称" :width="70" class="inline ml-lg">
      <Input v-model="formValidate.deviceName" class="width-md" placeholder="请输入设备名称"></Input>
    </ui-label>
    <ui-label label="设备编码" :width="70" class="inline ml-lg">
      <Input v-model="formValidate.deviceId" class="width-md" placeholder="请输入设备编码"></Input>
    </ui-label>
    <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="inline ml-lg">
      <Select
        clearable
        class="width-md"
        v-model="formValidate.sbdwlx"
        :max-tag-count="1"
        :placeholder="`请选择${global.filedEnum.sbdwlx}`"
      >
        <Option
          v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
          :key="'sbdwlx' + bdindex"
          :value="sbdwlxItem.dataKey"
          >{{ sbdwlxItem.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label label="在线状态" :width="70" class="inline ml-lg" v-if="currentTree.id == 203 || currentTree.id == 202">
      <Select clearable class="width-md" v-model="formValidate.outcome" :max-tag-count="1" placeholder="请选择在线状态">
        <Option value="">全部</Option>
        <Option value="1">在线</Option>
        <Option value="2">离线</Option>
        <Option value="3">无法检测</Option>
      </Select>
    </ui-label>
    <!-- <ui-label label="设备类型" :width="70" class="inline ml-lg"
      v-if="currentTree.id == 207 || currentTree.id == 208 ||
            currentTree.id == 205 || currentTree.id == 206">
      <Select class="width-sm" v-model="formValidate.isImportantDevice" clearable placeholder="请选择设备类型">
        <Option :value="1" label="普通标签"></Option>
        <Option :value="2" label="重点标签"></Option>
      </Select>
    </ui-label> -->
    <ui-label label="调阅结果" :width="70" class="inline ml-lg" v-if="currentTree.id == 205 || currentTree.id == 204">
      <Select class="width-sm" v-model="formValidate.outcome" clearable placeholder="请选择检测结果">
        <Option :value="1" label="正常"></Option>
        <Option :value="2" label="异常"></Option>
        <Option :value="3" label="无法调阅"></Option>
      </Select>
    </ui-label>
    <ui-label
      label="检测结果"
      :width="70"
      class="inline ml-lg"
      v-if="currentTree.id == 207 || currentTree.id == 206 || currentTree.id == 209 || currentTree.id == 208"
    >
      <Select class="width-sm" v-model="formValidate.outcome" clearable placeholder="请选择检测结果">
        <Option :value="1" label="正常"></Option>
        <Option :value="2" label="异常"></Option>
        <Option :value="3" label="无法检测"></Option>
      </Select>
    </ui-label>
    <!-- <ui-label label="录像结果" :width="70" class="inline ml-lg"
      v-if="currentTree.id == 203 || currentTree.id == 202">
      <Select class="width-sm" v-model="formValidate.outcome" clearable placeholder="请选择检测结果">
        <Option :value="1" label="正常"></Option>
        <Option :value="2" label="异常"></Option>
        <Option :value="3" label="无法检测"></Option>
      </Select>
    </ui-label> -->
    <Button type="primary" class="ml-lg" @click="search">查询</Button>
    <Button class="ml-sm" @click="resetForm">重置</Button>
  </div>
</template>

<script>
import user from '@/config/api/user';
export default {
  name: 'search',
  components: {
    ApiOrganizationTree: require('../components/api-tree.vue').default,
  },
  props: {
    currentTree: {
      type: Object,
      default() {},
    },
    treeData: {
      type: Array,
    },
    taskObj: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      dictData: {},
      formValidate: {
        orgCode: '',
        deviceName: '',
        deviceId: '',
        sbdwlx: '',
        outcome: '', // 录像结果
        isImportantDevice: '',
      },
    };
  },
  mounted() {
    this.formValidate = {
      orgCode: this.taskObj.orgCode,
    };
    this.getDictData();
  },
  methods: {
    search() {
      this.$emit('searchFn', this.formValidate);
    },

    resetForm() {
      let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {};
      this.formValidate = {
        orgCode: orgCode || '',
        deviceName: '',
        deviceId: '',
        sbdwlx: '',
        outcome: '', // 录像结果
        isImportantDevice: '',
      };
      this.search();
    },

    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
  computed: {},
  watch: {},
};
</script>

<style lang="less" scoped>
.search {
  margin: 10px 0;
}

/deep/ .width-md,
.width-sm {
  width: 150px !important;
}
</style>
