import { icbdStructureService } from "./Microservice.js";
import request from "@/libs/request";
// 万物搜结构化任务新增对象
export function multiModalTaskAdd(data) {
  return request({
    url: icbdStructureService + "/llmStructure/task/add",
    method: "post",
    data,
  });
}

// 文件结构化按照任务下载资源
export function downloadFileByTaskId(taskId) {
  return request({
    url:
      icbdStructureService +
      `/llmStructure/task/downloadFileByTaskId/${taskId}`,
    method: "post",
    responseType: "blob",
  });
}

// 万物搜结构化任务编辑
export function multiModalTaskModify(data) {
  return request({
    url: icbdStructureService + "/llmStructure/task/modify",
    method: "put",
    data,
  });
}

// 查询万物搜结构化任务分页列表
export function multiModalTaskPageList(data) {
  return request({
    url: icbdStructureService + "/llmStructure/task/pageList",
    method: "post",
    data,
  });
}

// 万物搜结构化任务删除
export function multiModalTaskDelete(ids) {
  return request({
    url: icbdStructureService + `/llmStructure/task/remove/${ids}`,
    method: "delete",
  });
}

// 删除任务中的资源
export function multiModalTaskResourceDelete(ids) {
  return request({
    url: icbdStructureService + `/llmStructure/task/removeTaskResource/${ids}`,
    method: "delete",
  });
}

// 开始任务
export function multiModalTaskStart(ids) {
  return request({
    url: icbdStructureService + `/llmStructure/task/start/${ids}`,
    method: "put",
  });
}

// 停止任务
export function multiModalTaskStop(ids) {
  return request({
    url: icbdStructureService + `/llmStructure/task/stop/${ids}`,
    method: "put",
  });
}

// 获取万物搜结构化任务详细信息
export function multiModalTaskDetail(id) {
  return request({
    url: icbdStructureService + `/llmStructure/task/view/${id}`,
    method: "get",
  });
}

// 获取大模型结构化算法下拉列表数据
export function getAlgorithmSelectList(data) {
  return request({
    url: icbdStructureService + `/llmStructure/algorithm/getSelectList`,
    method: "post",
    data,
  });
}

// 获取大模型结构化算法详细信息
export function getAlgorithmSelectDetail(id) {
  return request({
    url: icbdStructureService + `/llmStructure/algorithm/view/${id}`,
    method: "get",
  });
}

// 文件资源新增
export function resourceAdd(data) {
  return request({
    url: icbdStructureService + `/llmStructure/resource/add`,
    method: "post",
    data,
  });
}

// 文件资源新增
export function resourceBatchAdd(data) {
  return request({
    url: icbdStructureService + `/llmStructure/resource/batchAdd`,
    method: "post",
    data,
  });
}

// 文件资源编辑
export function resourceModify(data) {
  return request({
    url: icbdStructureService + `/llmStructure/resource/modify`,
    method: "put",
    data,
  });
}

// 文件资源删除
export function resourceDelete(ids) {
  return request({
    url: icbdStructureService + `/llmStructure/resource/remove/${ids}`,
    method: "delete",
  });
}

// 查询万物搜索
export function llmStructureSearchPageList(data) {
  return request({
    url: icbdStructureService + "/llmStructure/search/pageList",
    method: "post",
    data,
  });
}

// 多模态实施解析
export function imageStructure(data) {
  return request({
    url: icbdStructureService + "/llmStructure/search/imageStructure",
    method: "post",
    data,
    timeout: 2 * 60 * 1000,
  });
}

// 依图万物搜
export function ytLLmSearchList(data) {
  return request({
    url: icbdStructureService + "/yt/llmStructure/search/list",
    method: "post",
    data,
  });
}

// 依图库
export function ytLLmLibList() {
  return request({
    url: icbdStructureService + "/yt/llmStructure/lib/list",
    method: "get",
  });
}

// 依图库设备
export function ytLLmDeviceList() {
  return request({
    url: icbdStructureService + "/yt/llmStructure/device/list",
    method: "get",
  });
}

// 宇视万物搜
export function ysLLmSearchList(data) {
  return request({
    url: icbdStructureService + "/ys/llmStructure/search/list",
    method: "post",
    data,
  });
}

// 宇视设备
export function ysDeviceList(data) {
  return request({
    url: icbdStructureService + "/ys/llmStructure/device/list",
    method: "post",
    data,
  });
}

// 宇视组织
export function ysOrgCodeList(orgCode) {
  return request({
    url: icbdStructureService + `/ys/llmStructure/org/list/${orgCode}`,
    method: "get",
  });
}