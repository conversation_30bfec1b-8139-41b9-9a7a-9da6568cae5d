<template>
  <div class="content" ref="haha">
    <ul class="ul">
      <li
        v-for="(item, index) in list"
        :key="index"
        :class="{ active: index == curIndex }"
        @click="createTabs(item.componentName, index, item)"
      >
        <div class="corner leftTop"></div>
        <div class="corner rightTop"></div>
        <div class="corner bottomLeft"></div>
        <div class="corner bottomRight"></div>

        <p>{{ item.text }}</p>
        <div class="number-content">
          <div class="w100">
            <div class="block">
              <span>检测总量</span>
              <span class="bg color">
                {{ item.dataNum | numberInfo }}
                <div class="mini leftTop"></div>
                <div class="mini rightTop"></div>
                <div class="mini bottomLeft"></div>
                <div class="mini bottomRight"></div>
              </span>
            </div>
            <div class="block">
              <span>检测项数</span>
              <span class="bg">
                {{ item.testingNum | numberInfo }}
                <div class="mini leftTop"></div>
                <div class="mini rightTop"></div>
                <div class="mini bottomLeft"></div>
                <div class="mini bottomRight"></div>
              </span>
            </div>
            <div class="block" v-if="item.id == 2 || item.id == 3">
              <span>设备总量</span>
              <span class="bg color">
                {{ item.deviceCount | numberInfo }}
                <div class="mini leftTop"></div>
                <div class="mini rightTop"></div>
                <div class="mini bottomLeft"></div>
                <div class="mini bottomRight"></div>
              </span>
            </div>
          </div>
        </div>

        <span v-if="item.icon == 'img1'" class="img1 icon-font icon-shitujichushuju"></span>
        <span v-if="item.icon == 'img2'" class="img2 icon-font icon-renlianshitushuju"></span>
        <span v-if="item.icon == 'img3'" class="img3 icon-font icon-cheliangshitushuju"></span>
        <span v-if="item.icon == 'img4'" class="img4 icon-font icon-shipinliushuju"></span>
        <span v-if="item.icon == 'img5'" class="img5 icon-font icon-zhongdianrenyuanshuju"></span>

        <img
          v-if="index != list.length - 1"
          class="img"
          :class="{ hideImg: index == hoverIndex }"
          src="@/assets/img/flag.png"
          alt
          srcset
        />
      </li>
    </ul>
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
import tasktracking from '@/config/api/tasktracking';
import { mapGetters } from 'vuex';
export default {
  props: {
    // list: {
    //   type: Array,
    //   default: [],
    // },
    tabsText: {
      type: String,
      required: false,
    },
    // 标签页参数，用来刷新页面时重新获取数据
    tabsQuery: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      defaultId: null, // 默认选中--路由跳转过来的id
      list: [
        {
          id: 1,
          componentName: 'baseData',
          text: '视图基础数据',
          icon: 'img1',
          dataNum: '0',
          testingNum: '12',
          themeName: '视图基础数据治理主题',
        },
        {
          id: 2,
          componentName: 'faceData',
          text: '人脸图像数据',
          icon: 'img2',
          dataNum: '0',
          testingNum: '9',
          themeName: '人脸图像数据治理主题',
        },
        {
          id: 3,
          componentName: 'carView',
          text: '车辆图像数据',
          icon: 'img3',
          dataNum: '0',
          testingNum: '8',
          themeName: '车辆图像数据治理主题',
        },
        {
          id: 4,
          componentName: 'videoView',
          text: '视频流数据',
          icon: 'img4',
          dataNum: '0',
          testingNum: '5',
          themeName: '视频流数据治理主题',
        },
        {
          id: 5,
          componentName: 'importantPerson',
          text: '重点人员数据',
          icon: 'img5',
          dataNum: '0',
          testingNum: '10',
          themeName: '人员数据治理主题',
        },
      ],
      curIndex: 0,
      hoverIndex: 0,
    };
  },
  async activated() {
    if (this.$route.query.id) {
      this.defaultId = parseInt(this.$route.query.id);
      if (this.$route.query.isDefault) {
        if (this.$route.query.isDefault == 0) {
          this.defaultId = 0;
        }
      }
    }
    this.init();
    await this.getViewList();
    this.initDefault();
  },
  methods: {
    async getViewList() {
      try {
        let { data } = await this.$http.get(tasktracking.queryTaskTopicListStatics);
        this.handleComponentList(data.data);
      } catch (err) {
        console.log(err);
      }
    },
    initDefault() {
      let defaultIndex = 0;
      let defaultItem = this.list[0];
      if (this.defaultId) {
        defaultIndex = this.list.findIndex((item) => item.id === this.defaultId);
        defaultItem = this.list[defaultIndex];
      }
      this.createTabs(defaultItem.componentName, defaultIndex, defaultItem);
    },
    handleComponentList(data) {
      const topicListObject = {};
      data.forEach((item) => {
        topicListObject[item.topicName] = item;
      });
      this.list.forEach((item) => {
        let topicItem = topicListObject[item.themeName];
        item.dataNum = topicItem.accessDataCount;
        item.testingNum = topicItem.detectCount;
        item.deviceCount = topicItem.deviceCount;
      });
    },
    async init() {
      await this.$http.get(api.queryThremList).then((res) => {
        if (res.data.code == 200) {
          var list = res.data.data;
          list.forEach((item, index) => {
            if (index <= 4) {
              this.list[index].dataNum = item.accessDataCount;
            }
          });
        } else {
          console.log(res.data.msg);
        }
      });
    },
    // ...mapActions({
    //   setCacheRouterList: "common/setCacheRouterList",
    // }),
    createTabs(name, curIndex, item) {
      this.hoverIndex = curIndex - 1;
      this.curIndex = curIndex;
      this.$emit('selectModule', name, item);
      //   // debugger
      //   // 创建组件标签命名为此组件名字
      //   const cacheRouterName = `${name}`;
      //   // 判断缓存路由中是否已经存在要创建的组件标签
      //   const index = this.cacheRouterList.findIndex(
      //     (row) => row.name === cacheRouterName
      //   );
      //   /**
      //    * 如果是打开的标签页组件
      //    * 组件名称参数应为 componentName 已经存在的组件名称-要打开的组件名称
      //    */
      //   let existComponent = !!this.$route.query.componentName
      //     ? this.$route.query.componentName
      //     : null;
      //   let componentName = name;
      //   // let componentName = !!existComponent ? existComponent + "-" + name : name;
      //   /**
      //    * 如果没有要创建的组件标签
      //    * name：此模块的name名字应不与所有菜单重名，为组件名称
      //    * path：当前路由地址path
      //    * text：tabs中显示的中文名称
      //    * meta: {
      //    *  componentName：此组件名称
      //    *  routeName: 此组件所在路由
      //    *  queryParams: 要传入新标签的额外参数
      //    * }
      //    */
      //   if (index === -1) {
      //     let router = null;
      //     if (!!this.$route.query.componentName) {
      //       router = this.cacheRouterList.find(
      //         (row) =>
      //           !!row.meta &&
      //           row.meta.componentName === this.$route.query.componentName
      //       );
      //     } else {
      //       router = this.cacheRouterList.find(
      //         (row) => row.name === this.$route.name
      //       );
      //     }
      //     this.cacheRouterList.push({
      //       name: cacheRouterName,
      //       path: this.$route.path,
      //       text: `${router.text}-${this.tabsText}`,
      //       meta: {
      //         componentName: componentName,
      //         routeName: this.$route.name,
      //         queryParams: this.tabsQuery,
      //       },
      //     });
      //     this.setCacheRouterList(this.cacheRouterList);
      //   }
      //   // 创建组件标签后跳转路由，并且路由中应带入此组件名称参数
      //   this.$router.push({
      //     name: this.$route.name,
      //     query: Object.assign(this.tabsQuery, { componentName: componentName }),
      //   });
    },

    // liMouseout (index) {
    //   console.log(index)
    //   if (index == 0) {
    //   }else {
    //     setTimeout(()=>{
    //     // this.hoverIndex = this.curIndex;
    //       console.log(this.hoverIndex)
    //     },50)
    //   }
    // },

    // liMouseover (index) {
    //   console.log(index)
    //   if (index == 0) {
    //   }else {
    //     // this.hoverIndex = this.curIndex;
    //     this.hoverIndex = index -1;

    //     console.log(this.hoverIndex)
    //   }
    // }
  },
  watch: {},
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
  },
  components: {},
};
</script>
<style lang="less" scoped>
.content {
  // margin: 0px 0 20px;
  position: relative;
  height: 180px;
}
.ul {
  position: absolute;
  width: calc(100% - 40px);
  z-index: 99;
  // background: rgba(43, 132, 226, 0.11);
  height: 180px;
  margin: 20px;
  li {
    position: relative;
    float: left;
    width: 20%;
    height: 184px;
    //height: 139px;
    // background: rgba(43, 132, 226, 0.11);
    background: rgba(15, 139, 225, 0.11);
    padding-left: 120px;
    color: #fff;
    cursor: pointer;
    padding-bottom: 20px;
    .number-content {
      height: calc(100% - 40px);
      display: flex;
      width: 100%;
      align-items: center;
      flex-wrap: wrap;
    }
    .w100 {
      width: 100%;
    }
    p {
      margin-top: 14px;
      font-size: 16px;
      color: #11c4e9;
    }

    .block {
      margin-top: 13px;
      display: flex;
      padding: 0 20px 0 0;
      align-items: center;
      width: 100%;
    }

    .bg {
      position: relative;
      //min-width: 128px;
      flex: 1;
      margin-left: 10px;
      font-size: 16px;
      color: #0ff88b;
      display: inline-block;
      background: rgba(43, 132, 226, 0.28);
      padding: 3px 12px;
      text-align: center;
      .mini {
        position: absolute;
        width: 5px;
        height: 5px;
        border: 1px solid var(--color-primary);
      }
      .leftTop {
        top: 0;
        left: 0;
        border-right: 0;
        border-bottom: 0;
      }
      .rightTop {
        top: 0;
        right: 0;
        border-left: 0;
        border-bottom: 0;
      }
      .bottomLeft {
        bottom: 0;
        left: 0;
        border-top: 0;
        border-right: 0;
      }
      .bottomRight {
        bottom: 0;
        right: 0;
        border-left: 0;
        border-top: 0;
      }
    }
    .color {
      color: var(--color-bluish-green-text);
    }

    .img {
      position: absolute;
      top: 8px;
      right: 0;
      height: 100%;
    }

    .hideImg {
      display: none;
    }
  }
  li:hover,
  .active {
    background: #174884;
    background: rgba(15, 139, 225, 0.25);
    // opacity: 0.65;
    .corner {
      position: absolute;
      width: 20px;
      height: 20px;
      border: 2px solid var(--color-primary);
    }
    .leftTop {
      top: 0;
      left: 0;
      border-right: 0;
      border-bottom: 0;
    }
    .rightTop {
      top: 0;
      right: 0;
      border-left: 0;
      border-bottom: 0;
    }
    .bottomLeft {
      bottom: 0;
      left: 0;
      border-top: 0;
      border-right: 0;
    }
    .bottomRight {
      bottom: 0;
      right: 0;
      border-left: 0;
      border-top: 0;
    }
  }

  .active {
    .img {
      display: none;
    }
    .hideImg {
      display: none;
    }
  }
}

.icon-font {
  position: absolute;
  background-image: linear-gradient(#0f84e9, #12a4c9);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.img1 {
  font-size: 50px;
  left: 36px;
  top: 26px;
}
.img2 {
  font-size: 50px;
  left: 36px;
  top: 30px;
}
.img3 {
  font-size: 42px;
  left: 30px;
  top: 35px;
}
.img4 {
  font-size: 40px;
  left: 30px;
  top: 35px;
}
.img5 {
  font-size: 46px;
  left: 30px;
  top: 30px;
}
</style>
