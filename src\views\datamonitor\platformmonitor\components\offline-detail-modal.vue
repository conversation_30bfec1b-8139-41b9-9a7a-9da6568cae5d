<template>
  <ui-modal v-model="visible" :styles="styles" footer-hide :title="modalRowData.name + '-离线详情'">
    <div class="search-time-box mb-lg">
      <ui-label label="时间：">
        <DatePicker
          v-model="searchData.searchTime"
          type="month"
          placeholder="请选择日期"
          format="yyyy-MM"
          @on-change="changeDate"
          :options="DISABLED_AFTER_NOW"
        ></DatePicker>
      </ui-label>
    </div>
    <icon-statics :icon-list="showIconStaticsList" class="mb-lg"></icon-statics>
    <draw-echarts
      :echart-option="echartOption"
      :echart-style="echartStyle"
      :echarts-loading="echartsLoading"
      @echartClick="echartClick"
    ></draw-echarts>
    <day-capture-modal v-model="showDayCapVisible" v-bind="dayCaptureAttr"> </day-capture-modal>
  </ui-modal>
</template>
<script>
import datamonitorApi from '@/config/api/datamonitor';
import { DISABLED_AFTER_NOW } from '../utils/enum';

export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    commonSearchData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    echartsFactory: {
      type: Object,
      default: () => {
        return {};
      },
    },
    modalRowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      DISABLED_AFTER_NOW,
      styles: {
        width: '5.8rem',
      },
      echartStyle: {
        width: '1120px',
        height: '500px',
      },
      visible: false,
      echartsLoading: false,
      echartOption: {},
      echartsData: [],
      iconStaticsList: [
        {
          name: '累计在线',
          count: '0',
          isShow: true,
          countStyle: {
            color: 'var(--color-success)',
          },
          iconName: 'icon-shipinjiankongjianshezongliang',
          fileName: 'onlineText',
        },
        {
          name: '累计离线',
          count: '0',
          isShow: true,
          countStyle: {
            color: 'var(--color-failed)',
          },
          iconName: 'icon-leijilixian',
          fileName: 'offlineText',
        },
        {
          name: '累计报备',
          count: '0',
          isShow: true,
          countStyle: {
            color: '--color-active',
          },
          iconName: 'icon-leijibaobei',
          fileName: 'reportText',
        },
      ], //展示的统计信息
      searchDate: null,
      searchData: {
        name: '',
        searchTime: '',
      },
      showDayCapVisible: false, //展示日抓拍
      dayCaptureAttr: {
        modalRowData: {},
        commonSearchData: {},
      },
    };
  },
  computed: {
    showIconStaticsList() {
      return this.iconStaticsList.filter((item) => item.isShow);
    },
  },
  created() {},
  methods: {
    //获取统计信息
    getStatisticTime(data) {
      this.iconStaticsList = this.iconStaticsList.map((item) => {
        let isShow = data.hasOwnProperty(item.fileName);
        item.count = isShow ? data[item.fileName] : '-';
        item.isShow = isShow;
        return item;
      });
    },
    //点击图表上的日期
    echartClick(params) {
      if (params.componentType !== 'yAxis' || params.targetType !== 'axisLabel') {
        return;
      }
      let reg = /^(\d{4})年(\d{2})月(\d{2})日$/;
      let paramDate = params.value.includes('年') ? params.value.replace(reg, '$1-$2-$3') : params.value;
      this.dayCaptureAttr.modalRowData = {
        regionCode: this.modalRowData.code,
        showDayTime: paramDate,
      };
      this.dayCaptureAttr.commonSearchData = {
        activeMoinitorType: this.commonSearchData.activeMoinitorType,
        name: this.modalRowData.name,
      };
      this.showDayCapVisible = true;
    },
    //设置搜索条件
    setSearchData() {
      this.searchData.searchTime = this.$util.common.deepCopy(this.commonSearchData.searchTime);
    },
    //获取数据
    async getChartsdata() {
      try {
        this.echartsLoading = true;
        const params = {
          time: this.$util.common.formatDate(this.searchData.searchTime, 'yyyyMM'),
          regionCode: this.modalRowData.code,
          type: this.commonSearchData.activeMoinitorType,
        };
        let {
          data: { data },
        } = await this.$http.get(datamonitorApi.getOfflineByMonthDetail, { params });
        this.echartsData = data.detailVos;
        this.echartOption = this.echartsFactory.getOfflineDetailOption(this.echartsData);
        this.getStatisticTime(data);
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    //切换日期
    changeDate(val) {
      this.searchData.searchTime = val;
      this.getChartsdata();
    },
  },
  watch: {
    value: {
      async handler(val) {
        this.visible = val;
        if (val) {
          this.dayCaptureAttr = {};
          this.setSearchData();
          await this.getChartsdata();
        }
      },
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    IconStatics: require('@/components/icon-statics.vue').default,
    dayCaptureModal: require('./day-capture-modal.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .icon-ul {
    background-color: #0a2754;
  }
}
@{_deep} .ivu-modal-body {
  padding: 0;
  .detail-title {
    display: flex;
    justify-content: right;
    margin: 0 50px 10px 50px;
  }
  .icon-ul {
    height: 50px;
    padding: 0 50px;
  }
  .echarts {
    padding: 0 50px;
  }
}
.search-time-box {
  padding-left: 53px;
}
</style>
