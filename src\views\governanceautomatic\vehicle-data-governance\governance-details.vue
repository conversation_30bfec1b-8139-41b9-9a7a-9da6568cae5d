<template>
  <ui-modal v-model="visible" title="治理详情" :styles="styles" footer-hide>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="detailData">
      <template #result="{ row }">
        <span
          :class="[
            row.originPropertyValue
              ? row.propertyValue && row.originPropertyValue === row.propertyValue
                ? 'font-green'
                : row.propertyValue
                ? 'font-D66418'
                : 'font-green'
              : 'font-red',
          ]"
        >
          {{
            row.originPropertyValue
              ? row.propertyValue && row.originPropertyValue === row.propertyValue
                ? '正确'
                : row.propertyValue
                ? '存疑'
                : '正确'
              : '缺失'
          }}
        </span>
      </template>
      <template #correctionPropertyValue="{ row }">
        <span>
          {{
            row.originPropertyValue
              ? row.originPropertyValue === row.propertyValue
                ? ''
                : row.propertyValue
              : row.propertyValue
          }}
        </span>
      </template>
    </ui-table>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
    },
    detailData: {
      type: Array,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '5rem',
      },
      tableColumns: [
        {
          minWidth: 100,
          title: '结构化属性',
          key: 'propertyName',
          className: 'padding-sm',
        },
        {
          minWidth: 100,
          title: '原始值',
          key: 'originPropertyValue',
        },
        {
          minWidth: 100,
          title: '算法值',
          key: 'propertyValue',
        },
        {
          minWidth: 100,
          title: '判定结果',
          slot: 'result',
        },
        {
          minWidth: 100,
          title: '治理结果',
          slot: 'correctionPropertyValue',
        },
      ],
      tableData: [],
    };
  },
  created() {},
  methods: {
    query() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  height: 500px;
  display: flex;
  flex-direction: column;
}
.ui-table {
  @{_deep}.padding-sm {
    padding-left: 10px;
  }
}
</style>
