<template>
  <div class="relation-graph-map">
    <!-- 数智立方 -->
    <CollapseExpand
      ref="collapseExpand"
      @onNodeClick="onNodeClick"
      @onLineClick="onLineClick"
      :atlasList="atlasList"
      :options="{ allowShowMiniToolBar: false }"
      @onjectDetail="onjectDetail"
      @relationView="relationView"
      :rightMenus="rightMenus"
      :isHasExpand="isHasExpand"
      @loadNextLevel1Data="loadNextLevel1Data"
      @loadNextLevel2Data="loadNextLevel2Data"
      @loadNextLevel3Data="loadNextLevel3Data"
      @deleteNode="deleteNode"
      @base64Info="base64Info"
      @selectNode="selectNode"
    />
    <!-- 对象详情 -->
    <ObjectDetails 
      v-model="objectDetailsVisble" 
      :currentNode="objectDetailsNode" 
      :detailRelationList="detailRelationList" 
      :detailObjInfo="detailObjInfo" 
      @showNode="showNode" 
      :lineNode="lineNode" />
    <!--  关系详情-->
    <RelationshipInfo 
      ref="relationInfo" 
      v-model="relationshipInfoVisble" 
      :currentNode="relationshipInfoNode" 
      :detailRelationList="detailRelationList" 
      :relationList="relationList"
      :fromNode="detailObjInfo" 
      :toNode="toNode" 
      :lineNode="lineNode" />
    <!-- 底部提示 -->
    <div class="footer-tips">提示：<span class="line-color">Ctrl+鼠标左键</span> 可选中多个， <span class="line-color">Ctrl + Alt</span>可选中所有</div>
    <!-- 底部统计 -->
    <div class="footer-statistics" v-if="!currentTab.relation">
      <div>实体：<span class="line-color">{{atlasList.nodes.length}}</span></div>
      <div>关系：<span class="line-color">{{atlasList.links.length}}</span></div>
      <!-- <div>用时：<span class="line-color">0.7s</span></div> -->
    </div>
    <!-- 实体筛选 -->
    <CheckSelect @hidenShiti="hidenShiti" v-show="checkSelect" :type="type" :atlasListChilren="checkSelectList" @checkSelect="checkSelectChange" />
    <!-- 时间筛选 -->
    <CheckSelectTime  v-model="checkSelectTime" @checkSelectTime="checkSelectTimeChange" />
  </div>
</template>
<script>
import { nDegreeRelation } from '@/api/number-cube'
import people1 from '@/views/number-cube/components/people1.webp'
import people2 from '@/views/number-cube/components/people2.webp'
import people3 from '@/views/number-cube/components/people3.webp'
import people4 from '@/views/number-cube/components/people4.webp'
import people5 from '@/views/number-cube/components/people5.webp'
import people6 from '@/views/number-cube/components/people6.webp'
import people7 from '@/views/number-cube/components/people7.webp'
import people8 from '@/views/number-cube/components/people8.webp'
import car1 from '@/views/number-cube/components/car1.webp'
import car2 from '@/views/number-cube/components/car2.webp'
export default {
  components: {
    CollapseExpand: require('@/components/relation-graph/number-cube').default,
    ObjectDetails: require('./object-details').default,
    RelationshipInfo: require('./relationship-info').default,
    CheckSelect: require('./check-select').default,
    CheckSelectTime: require('./relation-graph-time').default,
  },
  props: {
    entityList:{
      type: Array,
      default: () => []
    },
    relationList:{
      type: Array,
      default: () => []
    },
    atlasList: {
      type: Object,
      default() {
        return {}
      }
    },
    // 当前tab对象
    currentTab: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      checkSelect: false,
      checkSelectTime: false,
      people1,
      people2,
      people3,
      people4,
      people5,
      people6,
      people7,
      people8,
      car1,
      car2,
      objectDetailsVisble: false,
      relationshipInfoVisble: false,
      objectDetailsNode: {}, // 当前右键选中数据
      relationshipInfoNode: {},
      rightMenus: [
        { clickName: 'onjectDetail', title: '对象详情' },
        { clickName: 'relationView', title: '关系视图' },
        { clickName: 'loadNextLevel1Data', title: '一度关系' },
        { clickName: 'loadNextLevel2Data', title: '二度关系' },
        { clickName: 'loadNextLevel3Data', title: '三度关系' },
        { clickName: 'deleteNode', title: '删除' }
        // { clickName: 'onjectDetail', title: '档案' }
      ], // 右键按钮菜单
      type: "",  // 筛选类型
      checkSelectList: [],
      detailObjInfo: {},  // 对象详情
      detailRelationList: [], // 对象列表
      toNode: {},         // 关联对象
      lineNode: {},       // 线对象
      isHasExpand: false, //是否展开收缩节点
    }
  },
  computed: {
    // 关系类型根据name组合对象
    relationsTypeObject () {
      console.log('0000000', this.relationList)
      let relationsTypeObject = {}
      this.relationList.forEach(ele => {
        relationsTypeObject[ele.name] = ele
      });
      return relationsTypeObject
    },
  },
  watch: {
    atlasList: {
      deep: true,
      handler(val) {
        this.hideModel()
        // console.log('------graph-map-------', val)
      }
    },
    currentTab: {
      deep: true,
      handler(val) {
        this.isHasExpand = val.relation
      }
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    // 节点点击返回
    onNodeClick(item) {
      // this.$Message.success('节点点击')
    },
    // 线点击
    onLineClick(item) {
      // console.log('------线点击-------', item)
      this.relationshipInfoVisble = true
      this.relationshipInfoNode = item
      if (item.fromNode.data.type) {
        this.detailObjInfo = item.fromNode.data
      } else {  
        // 关系视图 详情处理 
        this.detailObjInfo = this.$parent.rightClickNode.currentNode.data
      }
      this.toNode = item.toNode.data
      this.lineNode = item.relations[0].data
      // console.log('this.detailObjInfo ------', this.detailObjInfo, this.lineNode)
      this.$nextTick(() => {
        this.$refs.relationInfo.init()
      })
    },
    // 对象详情
    onjectDetail({ currentNode }) {
      // console.log('对象详情-----------', currentNode, this.atlasList)
      var list = this.atlasList.links.filter(i => i.data.id.includes(currentNode.id))
      // console.log('相关的线------------', list)
      var relationList = [];
      // console.log('关系信息------------', relationList, this.currentTab)
     
        list.forEach(i => {
          var row = relationList.find(a => a.name == i.text)
          if (row){
            if (i.from != currentNode.id) {
              var item = this.atlasList.nodes.find(a => a.id == i.from)
              item.show = true
              if (!row.data.find(a => a.id == item.id)) {
                row.data.push(item)
              }
            }
            if (i.to != currentNode.id) {
              var item = this.atlasList.nodes.find(a => a.id == i.to)
              item.show = true
              if (!row.data.find(a => a.id == item.id)) {
                row.data.push(item)
              }
            }
          }else{
            var obj = { name: i.text, data:[] }
            if (i.from != currentNode.id) {
              var item = this.atlasList.nodes.find(a => a.id == i.from)
              item.show = true
              obj.data.push(item)
            }
            if (i.to != currentNode.id) {
              var item = this.atlasList.nodes.find(a => a.id == i.to)
              item.show = true
              obj.data.push(item)
            }
            relationList.push(obj)
          }
        })

        
        this.detailObjInfo = this.atlasList.nodes.find(i => i.id == currentNode.id)
        this.detailRelationList = relationList
        this.objectDetailsVisble = true
        this.objectDetailsNode = currentNode

        if (this.currentTab.relation) {
          var list = []
          relationList[0].data.forEach(i => {
            list.push({ id: i.id, name: i.text, data: []})
          })

          list.forEach(i => {
            var arr = this.atlasList.links.filter(t => t.to == i.id || t.from == i.id)
            arr = arr.filter(a => a.to != currentNode.id && a.from != currentNode.id)
            arr.forEach(e => {
              if (e.to == i.id) {
                if (e.from != i.id) {
                  var item = this.atlasList.nodes.find(a => a.id == e.from)
                  item.show = true
                  i.data.push(item)
                }
              } else {
                if (e.to != i.id) {
                  var item = this.atlasList.nodes.find(a => a.id == e.to)
                  item.show = true
                  i.data.push(item)
                }
              }
            })
          })

          this.detailRelationList = list
        }
    },
    /**
     * 关系视图
     */
    relationView(e){
      // console.log('关系视图------', e)
      this.$emit('relationView', e)
      // this.$router.push({ name: 'number-cube-info', query: { id: e.currentNode.data.id} })
    },
    /**
     * 对象详情右侧点击显示关系详情
     * @param {*} row 
     */
    showNode(row) {
      // console.log('选中的关系实体1------', this.detailObjInfo, this.atlasList)
      this.toNode = row
      var obj  = {}
      if (this.currentTab.relation) {
        obj = this.atlasList.links.find(i => i.to == row.id || i.from == row.id)
      }else {
        obj  = this.atlasList.links.find(i => i.data.id.includes(row.id) && i.data.id.includes(this.detailObjInfo.id))
      }
      if (obj) this.lineNode = obj.data
      // console.log('选中的关系实体------', row, this.lineNode)
      this.relationshipInfoVisble = true
      this.$nextTick(() => {
        this.$refs.relationInfo.init()
      })
    },
    // 节点删除
    deleteNode({ currentNode }) {
      this.$refs.collapseExpand.deleteNode(currentNode.data.id)
      this.$emit('newGraphList', currentNode.data.id)
    },
    // 一度关系，二度关系，三度关系
    loadNextLevel1Data({ currentNode }) {
      var atlasList = {
        rootId: currentNode.data.id,
        nodes: [
          { id: currentNode.data.id + '2', label: 'wifi', name: '陈赫', img: this.people2 },
          { id: currentNode.data.id + '3', label: 'iphone', name: '张伟', img: this.people3 },
          { id: currentNode.data.id + '4', label: 'person', name: '关谷', img: this.people4 },
          { id: currentNode.data.id + '5', label: 'person', name: '唐悠悠', img: this.people5 }
        ],
        links: [
          { from: currentNode.data.id, to: currentNode.data.id + '2', text: '关系( 2 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id, to: currentNode.data.id + '3', text: '关系( 4 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id, to: currentNode.data.id + '4', text: '关系( 5 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id, to: currentNode.data.id + '5', text: '关系( 2 )', fontColor: '#2C86F8' }
        ]
      }
      var param = {
        entityIds: currentNode.id,
        maxDepth: 1
      }
      nDegreeRelation(param).then(res => {
        this.levelData(currentNode, res.data)
      })
      this.$nextTick(() => {
        // this.$parent.getGraphJsonData()
      })
    },
    loadNextLevel2Data({ currentNode }) {
      var atlasList = {
        rootId: currentNode.data.id,
        nodes: [
          { id: currentNode.data.id + '2', label: 'person', name: '陈赫', img: this.people2 },
          { id: currentNode.data.id + '3', label: 'person', name: '张伟', img: this.people3 },
          { id: currentNode.data.id + '4', label: 'person', name: '关谷', img: this.people4 },
          { id: currentNode.data.id + '5', label: 'person', name: '唐悠悠', img: this.people5 }
        ],
        links: [
          { from: currentNode.data.id, to: currentNode.data.id + '2', text: '关系( 2 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id, to: currentNode.data.id + '3', text: '关系( 4 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id + '2', to: currentNode.data.id + '4', text: '关系( 5 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id + '3', to: currentNode.data.id + '5', text: '关系( 2 )', fontColor: '#2C86F8' }
        ]
      }
      var param = {
        entityIds: currentNode.id,
        maxDepth: 2
      }
      nDegreeRelation(param).then(res => {
        this.levelData(currentNode, res.data)
      })
      // this.$refs.collapseExpand.loadNextLevelData(atlasList)
      this.$nextTick(() => {
        // this.$parent.getGraphJsonData()
      })
    },
    loadNextLevel3Data({ currentNode }) {
      var atlasList = {
        rootId: currentNode.data.id,
        nodes: [
          { id: currentNode.data.id + '2', label: 'person', name: '陈赫', img: this.people2 },
          { id: currentNode.data.id + '3', label: 'person', name: '张伟', img: this.people3 },
          { id: currentNode.data.id + '4', label: 'person', name: '关谷', img: this.people4 },
          { id: currentNode.data.id + '5', label: 'person', name: '唐悠悠', img: this.people5 }
        ],
        links: [
          { from: currentNode.data.id, to: currentNode.data.id + '2', text: '关系( 2 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id + '2', to: currentNode.data.id + '3', text: '关系( 4 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id + '3', to: currentNode.data.id + '4', text: '关系( 5 )', fontColor: '#2C86F8' },
          { from: currentNode.data.id + '3', to: currentNode.data.id + '5', text: '关系( 2 )', fontColor: '#2C86F8' }
        ]
      }
      // this.$refs.collapseExpand.loadNextLevelData(atlasList)
      this.$nextTick(() => {
        // this.$parent.getGraphJsonData()
      })
      var param = {
        entityIds: currentNode.id,
        maxDepth: 3
      }
      nDegreeRelation(param).then(res => {
        this.levelData(currentNode, res.data)
      })
    },
    /**
     * 一度关系，二度关系，三度关系 数据整合
     */
    levelData(currentNode, object) {
      let links = object.relations.map(item => {
        if (['TSW', 'TZS', 'TZS', 'THJ', 'YY', 'XYR'].includes(item.label)) {
          return {
            from: item.sourceId,
            to: item.targetId,
            text: item.count ? this.relationsTypeObject[item.label].nameCn + `(${item.count || ''})` : this.relationsTypeObject[item.label].nameCn,
            fontColor: '#2C86F8',
            color: '#2C86F8',
            data: item,
          }
        } else {
          return {
            from: item.sourceId,
            to: item.targetId,
            text: item.count ? this.relationsTypeObject[item.label].nameCn + `(${item.count || ''})` : this.relationsTypeObject[item.label].nameCn,
            fontColor: '#333',
            color: '#333',
            data: item,
          }
        }
        })

        // 实体
        let nodes = object.entitys.map(item => {
          return {
            id: item.id,
            text: item.properties.archiveNo,
            color: 'transparent',
            borderColor: 'transparent',
            fontColor: '#000000',
            styleClass: 'my-node-style',
            ...item
          }
        })
        let atlasList = {
          rootId: currentNode.data.id,
          links: links,
          nodes: nodes
        }
        this.$refs.collapseExpand.loadNextLevelData(atlasList)
    },
    // 展开关闭实体筛选
    isHidenShiti(type) {
      console.log('关系筛选--------------', type, this.atlasList)
      if (this.type == '') {
        this.type = type
        this.checkSelect = true
      }else{
        if (type == this.type) {
          this.type = ''
          this.checkSelect = false
        }else{
          this.type = type
          this.checkSelect = true
        }
      }

      if (this.checkSelect) {
        if (type == 'entity') {
          this.checkSelectList = this.atlasList.nodes
        } else {
          // 关系筛选列表
          this.checkSelectList = this.atlasList.links.map(item => {
            if (item.data.label) {
              return Object.assign({...item, label: this.relationsTypeObject[item.data.label].nameCn})
            } else {
              return Object.assign({...item, label: this.relationsTypeObject[item.data.details[0].label].nameCn})
            }
            // 关系类型中覆盖真正的关系id
            // return Object.assign({...item, label: item.data.sourceLabel,id: item.data.label})
            return Object.assign({...item, label: this.relationsTypeObject[item.data.label].nameCn})
            // return Object.assign({...item, label: this.relationsTypeObject[item.data.label].nameCn,id: this.relationsTypeObject[item.data.label].name})
          })
        }
      }
    },
    // 展开关闭时间筛选
    isHidenTime() {
      // console.log('展开关闭时间筛选------')
      this.checkSelectTime = true
    },
    checkSelectChange(selectList){
      // this.$emit('checkSelectChange',selectList,this.searchType)
      this.doFilter (selectList, this.type)
    },
    // 实体、关系筛选
    doFilter (selectList, type) {
      let _all_nodes = this.$refs.collapseExpand.getNodes()
      let _all_lines = this.$refs.collapseExpand.getLines()
      // console.log('实体、关系筛选-------',selectList, _all_nodes, _all_lines)
      // 关系筛选节点
      if (type === 'relation') {
        _all_lines.forEach(thisLine => {
          thisLine.relations.forEach(thisLink => {
            // if (!(selectList.includes(thisLink.data.sourceLabel)) || !(selectList.includes(thisLink.data.targetLabel))) {
            if (!(selectList.includes(thisLink.data.details[0].label))) {
              thisLink.isHide = true
            } else {
              thisLink.isHide = false
            }
          })
          // thisNode.opacity = _isShowThisNode ? 1 : 0.1
        })
      }
      // 实体筛选节点
      if (type === 'entity') {
        _all_nodes.forEach(thisNode => {
          let _isHideThisLine = false
          if (!(selectList.includes(thisNode.data.label))) {
            _isHideThisLine = true
          }
          thisNode.opacity = _isHideThisLine ? 0 : 1
        })
        _all_lines.forEach(thisLine => {
          thisLine.relations.forEach(thisLink => {
            if (!(selectList.includes(thisLink.data.sourceLabel)) || !(selectList.includes(thisLink.data.targetLabel))) {
              thisLink.isHide = true
            } else {
              thisLink.isHide = false
            }
          })
          // thisNode.opacity = _isShowThisNode ? 1 : 0.1
        })
      }
    },
    // 实体筛选
    hidenShiti(list) {
      this.$emit('hidenShiti', list)
    },
    base64Info(base){
      this.$emit('base64Info', base)
    },
    checkSelectTimeChange(e) {
      // console.log('时间筛选------', e)
    },
    selectNode(e){
      this.$emit('selectNode', e)
    },
    /**
     * 隐藏关系、实体筛选框，对象详情框
     */
    hideModel() {
      this.objectDetailsVisble = false
      this.checkSelect = false
      this.type == ''
    }
  }
}
</script>
<style lang="less" scoped>
.relation-graph-map {
  height: calc(~'(100% - 93px)');
}
.footer-tips {
  position: fixed;
  bottom: 20px;
  z-index: 12;
  left: 42%;
}
.footer-statistics {
  position: fixed;
  bottom: 30px;
  left: 30px;
  z-index: 12;
}
.line-color {
  color: #2c86f8;
}
</style>
<style lang="less"></style>
