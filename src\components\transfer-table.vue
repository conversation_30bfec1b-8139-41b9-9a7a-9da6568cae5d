<template>
  <div class="transfer-table-wrapper">
    <div class="left-table-wrapper auto-fill">
      <slot name="left-title"></slot>
      <div class="left-table auto-fill">
        <ui-table
          ref="leftTable"
          class="ui-table auto-fill"
          :table-columns="leftColumns"
          :table-data="leftData"
          :loading="leftLoading"
          @selectTable="leftSelectTable"
        >
        </ui-table>
      </div>
    </div>
    <div class="center mr-xs ml-xs">
      <span class="icon-font icon-lujing150 f-18 lujing inline"></span>
    </div>
    <div class="right-table-wrapper auto-fill">
      <slot name="right-title"></slot>
      <div class="right-table auto-fill">
        <ui-table
          ref="rightTable"
          class="ui-table auto-fill"
          :table-columns="rightColumns"
          :table-data="rightData"
          :loading="rightLoading"
          @selectTable="rightSelectTable"
        >
        </ui-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'transfer-table',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    leftTableColumns: {
      type: Array,
      required: true,
      default: () => [],
    },
    leftTableData: {
      type: Array,
      required: true,
      default: () => [],
    },
    leftLoading: {
      default: false,
    },
    rightTableColumns: {
      type: Array,
      default: () => [],
    },
    rightTableData: {
      type: Array,
      default: () => [],
    },
    rightLoading: {
      default: false,
    },
  },
  data() {
    return {
      selection: [],
      leftColumns: [],
      rightColumns: [],
      rightData: [],
      leftData: [],
    };
  },
  computed: {},
  watch: {
    leftTableColumns: {
      handler(val) {
        let select = [{ type: 'selection', align: 'center', width: 50 }];
        if (!val.length) return (this.leftColumns = []);
        return (this.leftColumns = [...select, ...val]);
      },
      immediate: true,
      deep: true,
    },
    rightTableColumns: {
      handler(val) {
        if (!val.length) return (this.rightColumns = this.leftTableColumns);
        return (this.rightColumns = val);
      },
      immediate: true,
      deep: true,
    },
    rightTableData: {
      handler(val) {
        this.rightData = val;
      },
      immediate: true,
      deep: true,
    },
    leftTableData: {
      handler(val) {
        this.leftData = val;
      },
      immediate: true,
      deep: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    leftSelectTable(selection) {
      this.$emit('onLeftToRight', selection);
    },
    rightSelectTable(selection) {
      this.$emit('onrightToleft', selection);
    },
  },
};
</script>

<style lang="less" scoped>
.transfer-table-wrapper {
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;

  .left-table-wrapper {
    height: 100%;
    position: relative;
    width: 50%;

    .left-table {
      padding: 10px;
      border: 1px solid var(--border-table);
    }
  }

  .center {
    color: var(--color-primary);

    .lujing {
      transform: rotate(90deg);
    }
  }

  .right-table-wrapper {
    height: 100%;
    position: relative;
    width: 50%;

    .right-table {
      padding: 10px;
      border: 1px solid var(--border-table);
    }
  }
}
</style>
