<template>
  <!-- 重点数据-基础信息填报准确率结果详情 -->
  <ui-modal
    class="basic-information"
    v-model="visible"
    v-if="visible"
    title="查看详情"
    :styles="styles"
    :footer-hide="true"
  >
    <div class="content auto-fill" v-if="Object.keys(trackList).length">
      <div class="container auto-fill">
        <div class="container_box auto-fill">
          <div class="title_text">
            <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
            <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
            <div class="export fr">
              <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
                <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
                <span class="inline ml-xs">导出</span>
              </Button>
            </div>
          </div>
          <line-title title-name="检测结果统计"></line-title>
          <div class="statistics_list">
            <div class="statistics">
              <div class="sta_item_left">
                <draw-echarts
                  :echart-option="echartRing"
                  :echart-style="ringStyle"
                  ref="zdryChart"
                  class="charts"
                ></draw-echarts>
              </div>
              <div class="line"></div>
              <no-standard :module-data="moduleData"></no-standard>
            </div>
          </div>
          <line-title title-name="异常数据列表"></line-title>
          <ui-select-tabs class="list_item" :list="selectTabs" @selectInfo="selectInfo"></ui-select-tabs>
          <div class="list auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="tableColumns"
              :table-data="tableData"
              :loading="loading"
            >
              <template #photo="{ row }">
                <img @click="viewBig(row)" :src="!row.photo ? noImg : row.photo" alt="" />
              </template>
              <template #option="{ row }">
                <ui-btn-tip
                  icon="icon-chakanyichangxiangqing"
                  content="不合格原因"
                  @click.native="checkReason(row)"
                ></ui-btn-tip>
                <!-- <span class="btn-text-default" @click="checkReason(row)">不合格原因</span> -->
              </template>
            </ui-table>
            <div class="no-data" v-if="!tableData.length">
              <i class="no-data-img icon-font icon-zanwushuju1"></i>
            </div>
          </div>
          <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
          </ui-page>
          <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
          <nonconformance
            ref="nonconformance"
            title="检测不合格原因"
            :tableColumns="reasonTableColumns"
            :tableData="reasonTableData"
            :reasonPage="reasonPage"
            :reasonLoading="reasonLoading"
            @handlePageChange="handlePageChange"
            @handlePageSizeChange="handlePageSizeChange"
          ></nonconformance>
        </div>
      </div>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.basic-information {
  //   .no-data_str {
  //   transform: translate(-24%, -50%);
  // }
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 840px;
    max-height: 840px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      .ui-table {
        img {
          width: 56px;
          height: 56px;
        }
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }

    .btn-text-default {
      cursor: pointer;
      font-size: 14px;
      color: var(--color-primary);
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import imgSrc from '@/assets/img/load-error-img.png';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      reasonLoading: false,
      echartRing: {},
      ringStyle: {
        width: '600px',
        height: '180px',
      },
      reasonTableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'detectionName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      noImg: imgSrc,
      zdryChartObj: {
        xAxisData: ['信息合格人员', '信息不合格人员'],
        showData: [
          { name: '信息合格人员', value: 0 },
          { name: '信息不合格人员', value: 0 },
        ],
        zdryTimer: null,
        totalNum: 20000,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '填报准确率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '人员照片', key: 'photo', slot: 'photo' },
        { title: '姓名', key: 'name' },
        { title: '性别', key: 'gender' },
        { title: '民族', key: 'nation' },
        { title: '证件类型', key: 'cardType' },
        { title: '证件号', key: 'idCard' },
        { title: '重点人员类型', key: 'type' },
        { title: '居住地址', key: 'homeAddress' },
        { title: '操作', slot: 'option', align: 'center' },
      ],
      tableData: [],
      minusTable: 550,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      loading: false,
      detailInfo: {},
      detailList: [],
      modalId: { title: '测评结果' },
      indexList: {},
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      selectTabs: [],
      deviceInfoId: '',
      errorMessages: [],
      trackList: {},
      bigPictureShow: false,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      exportLoading: false,
    };
  },

  async mounted() {
    await this.init();
    await this.getStatistics();
    await this.initRing();
    await this.getSelectTabs();
  },

  methods: {
    viewBig(item) {
      this.imgList = [item.identityPhoto];
      this.bigPictureShow = true;
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.get(governanceevaluation.basicsExportByResultId, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },

    // 列表
    async init() {
      let data = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        errorMessages: this.errorMessages,
        pageSize: this.searchData.pageSize,
        pageNumber: this.searchData.pageNum,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.getPageAccuracies, data);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.get(governanceevaluation.getStatByResultId, {
          params: {
            resultId: this.$parent.row.resultId,
            indexId: this.$parent.row.indexId,
          },
        });
        if (!res.data.data) return;
        this.trackList = res.data.data;
        this.moduleData.rateValue = this.trackList.precisionRate;
        this.moduleData.priceValue = this.$parent.row.standardsValue;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '信息合格人员') {
          item.value = this.trackList.qualifiedAmount || 0;
        } else {
          item.value = this.trackList.unqualifiedAmount || 0;
        }
      });
      this.zdryChartObj.count = this.trackList.detectionAmount || 0;
      let formatData = {
        seriesName: '检测人员总量',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    async getSelectTabs() {
      try {
        let res = await this.$http.get(governanceevaluation.getStatAbnormalReasons, {
          params: {
            resultId: this.$parent.row.resultId,
            indexId: this.$parent.row.indexId,
          },
        });
        this.selectTabs = res.data.data.map((item) => {
          let obj = {};
          obj.name = item;
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 查看不合格原因列表
    async getReason(row) {
      try {
        let params = {
          focusAccuracyId: row.id,
          deviceInfoId: this.deviceInfoId,
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        this.reasonLoading = true;
        let res = await this.$http.post(governanceevaluation.getPageAccuracyDetails, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    checkReason(row) {
      this.deviceInfoId = row.id;
      this.getReason(row);
      this.$refs.nonconformance.init();
    },
    selectInfo(val) {
      this.errorMessages = val.map((item) => {
        return item.name;
      });
      this.init();
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    uiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
  },
};
</script>
