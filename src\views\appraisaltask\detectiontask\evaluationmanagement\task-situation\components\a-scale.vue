<template>
  <div
    class="a-scale"
    :style="{
      width: getWidth,
    }"
  >
    <span class="label">单位：小时</span>
    <ul class="scale">
      <li
        class="time"
        :style="{
          transform: `translateX(${getLeft})`,
        }"
      >
        <div class="time-text">当前时间：{{ time }}</div>
      </li>
      <li class="scale-item" v-for="i in 25" :key="i">
        <span class="inline mt-xs">{{ i - 1 }}</span>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'a-scale',
  props: {
    // 时间轴长度
    scaleWidth: {
      required: true,
      type: Number,
    },
  },
  data() {
    return {
      time: null,
    };
  },
  created() {
    this.getTime();
  },
  methods: {
    getTime() {
      this.time = this.getHoursAndMinutes(new Date().getTime());
    },
    getHoursAndMinutes(timestamp) {
      let minutes = new Date(timestamp).getMinutes();
      minutes = minutes < 10 ? '0' + minutes : minutes;
      let hours = new Date(timestamp).getHours();
      hours = hours < 10 ? '0' + hours : hours;
      return `${hours}:${minutes}`;
    },
  },
  watch: {},
  computed: {
    getWidth() {
      const fontSize = parseFloat(getComputedStyle(document.documentElement)['font-size']);
      return this.scaleWidth * (fontSize / 192) + 'px';
    },
    getLeft() {
      const fontSize = parseFloat(getComputedStyle(document.documentElement)['font-size']);
      const zeroTimeStamp = new Date(new Date().toLocaleDateString()).getTime();
      let diff = new Date().getTime() - zeroTimeStamp;
      let left = (this.scaleWidth / 25) * (diff / 1000 / 60 / 60) * (fontSize / 192);
      return left + 'px';
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .a-scale {
    .scale {
      .time {
        .time-text {
          &::before {
            border-right: 1px dashed #0d477d;
          }
        }
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .a-scale {
    .scale {
      .time {
        .time-text {
          &::before {
            border-right: 1px dashed #d3d7de;
          }
        }
      }
    }
  }
}

.a-scale {
  position: relative;
  display: flex;
  .label {
    color: var(--color-content);
    position: absolute;
    left: -85px;
    top: 50%;
    transform: translateY(-50%);
  }
  .scale {
    display: flex;
    align-items: center;
    width: 100%;
    .scale-item {
      height: 1px;
      width: 4%;
      background-color: var(--color-content);
      color: var(--color-content);
      position: relative;
      span {
        display: inline-block;
        width: 40px;
        text-align: center;
        position: relative;
        left: -20px;
      }
      &:last-child {
        width: 0;
      }
      &::before {
        content: '';
        display: inline-block;
        position: absolute;
        top: -5px;
        width: 1px;
        height: 6px;
        background-color: var(--color-content);
      }
    }
    .time {
      background-color: #fff;
      width: 10px;
      height: 10px;
      color: var(--color-primary);
      border: 1px solid var(--color-primary);
      border-radius: 50%;
      position: absolute;
      left: -5px;
      .time-text {
        width: 120px;
        position: absolute;
        top: -40px;
        left: -56px;
        border: 2px solid var(--color-primary);
        border-radius: 4px;
        padding: 5px;
        &::after {
          content: '';
          display: inline-block;
          width: 1px;
          height: 6px;
          background-color: var(--color-primary);
          position: absolute;
          bottom: -6px;
          left: 50%;
        }
        &::before {
          content: '';
          display: inline-block;
          width: 1px;
          border-right: 1px dashed #0d477d;
          height: 675px;
          position: absolute;
          bottom: -690px;
          left: 50%;
        }
      }
    }
  }
}
</style>
