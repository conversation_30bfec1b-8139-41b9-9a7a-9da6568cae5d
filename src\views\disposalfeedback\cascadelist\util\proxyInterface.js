import cascadeList from '@/config/api/cascadeList';
import axios from '@/config/http/http';
import store from '@/store/index';
/**
 * 级联清单下级请求上级接口时，需要后端转发(所有接口都通过这两个接口转发)
 * 替换原来的接口
 * @NotBlank(message = "请求服务类型：0评测服务（默认） 1资产服务 2视频流播放服务")
 * private Integer server = 0;
 *
 * @param {*} formData 代理的params
 * @param {*} uri  接口名称（去除服务名）
 * @param {*} cascadeId 级联清单的问题主键id
 * @param {*} requestMethod 请求方式
 * @param server
 * @param {*} config 导出需修改axios的配置的传递这个值
 * @returns
 */
export const proxyInterfacefunc = async (formData, uri, cascadeId, server, requestMethod = 'post', config) => {
  let uri1 = uri.split('/');
  uri1.splice(0, 2);
  let params = {
    requestMethod: requestMethod,
    uri: `/${uri1.join('/')}`,
    id: cascadeId,
    server: server,
  };
  requestMethod === 'post' ? (params.postForm = formData) : (params.getForm = formData);
  try {
    let interfaceName = cascadeList.getIndexResult;
    // 如果传递这个参数就是导出流，更换接口
    if (config) {
      config.hasOwnProperty('responseType') ? (interfaceName = cascadeList.getIndexResultByInputStream) : null;
    }
    return await axios.post(interfaceName, params, {
      headers: {
        token: store.state.governanceevaluation.configData.superiorToken,
        orgCode: store.state.governanceevaluation.configData.deployOrgCode,
        flag: 'visitor',
      },
      ...config,
    });
  } catch (error) {
    console.log(error);
  }
};

/**
 *
 * @param {*} vuethis 当前组件的this
 * @param {*} interfaceName
 * @param {*} paramdata
 * @param {*} type
 * @param cascadeId
 * @param server
 * @param {*} config
 * @returns
 */
// let res = await superiorinjectfunc(this, governanceevaluation.faceResultOverview, { id: this.row.id }, 'get')
export const superiorinjectfunc = async (
  vuethis,
  interfaceName,
  paramdata = {},
  type = 'post',
  cascadeId,
  config,
  server = 0,
) => {
  /**------级联清单特殊替换处理接口(后端转发)-------**/
  if (cascadeId) {
    return await proxyInterfacefunc(paramdata, interfaceName, cascadeId, server, type, config);
  }
  // 不需要转发的请求
  let res = await vuethis.$http[type](interfaceName, paramdata, config);
  if (type === 'get') {
    res = await vuethis.$http[type](
      interfaceName,
      Object.assign({
        params: paramdata,
        ...config,
      }),
    );
  }
  return res;
};

