<template>
  <div class="ui-tabs-wrapped-container">
    <div class="ui-tabs-wrapped">
      <div
        v-for="(item, index) in deviceTypeList"
        :key="index"
        :class="['tab-item', active === item.value ? 'active' : '']"
        @click="changeType(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <slot></slot>
  </div>
</template>
<script>
// 命名灵感 https://design.teambition.com/basic/at/tab#tab-shape
export default {
  name: 'ui-tabs-wrapped',
  data() {
    return {
      active: '',
      deviceTypeList: [],
    };
  },
  props: {
    value: {},
    data: {},
  },
  created() {},
  mounted() {},
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.active = val;
      },
    },
    /**
     * @example
     * [
     *         {label: "视频监控对账",value: "1"},
     *         {label: "人脸卡口对账",value: "2"},
     *         {label: "车辆卡口对账",value: "3"},
     *       ]
     */
    data: {
      immediate: true,
      deep: true,
      handler(val) {
        this.deviceTypeList = val;
      },
    },
  },
  methods: {
    changeType(item) {
      this.$emit('input', item.value);
      this.$emit('on-change', item);
    },
  },
};
</script>
<style scoped lang="less">
.ui-tabs-wrapped-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  border-bottom: 1px solid var(--devider-line);
  margin-top: 10px;
  height: 34px;

  .ui-tabs-wrapped {
    display: flex;
    .tab-item {
      padding-top: 3px;
      cursor: pointer;
      width: 110px;
      height: 34px;
      line-height: 34px;
      color: var(--color-tab-border-card);
      font-size: 14px;
      position: relative;
      text-align: center;
      &.active {
        color: var(--color-switch-tab-active);
        border-left: 1px solid var(--devider-line);
        border-right: 1px solid var(--devider-line);
        &::before {
          position: absolute;
          left: 0;
          top: 0;
          content: '';
          display: inline-block;
          width: 100%;
          height: 3px;
          background-color: var(--color-switch-tab-active);
        }
        &::after {
          position: absolute;
          left: 0;
          bottom: -1px;
          content: '';
          display: inline-block;
          width: 100%;
          height: 1px;
          background-color: var(--bg-content);
        }
      }
    }
  }
}
</style>
