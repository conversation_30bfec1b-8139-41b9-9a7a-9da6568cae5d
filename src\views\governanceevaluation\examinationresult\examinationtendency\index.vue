<template>
  <div class="page-assessmentresult">
    <div class="right-content" v-if="!tabComponentName">
      <div class="search-wrapper">
        <ui-label class="inline" label="考核任务">
          <Select class="width-md" placeholder="请选择考核任务" v-model="searchData.examTaskId" @on-change="changeTask">
            <Option v-for="item in taskList" :key="item.id" :value="item.id">
              {{ item.taskName }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="ml-lg inline" label="考核时间">
          <template>
            <DatePicker
              type="year"
              format="yyyy"
              placeholder="请选择考核时间"
              v-model="searchData.time"
              @on-change="searchBtn"
            ></DatePicker>
          </template>
        </ui-label>
        <Button type="primary" class="mr-sm ml-lg" @click="searchBtn">查询</Button>
        <Button @click="resetSearch">重置</Button>
        <CheckboxGroup class="inline ml-lg" v-model="overBtn" @on-change="onChangeModel">
          <Checkbox :label="1" :disabled="modelDisable(1)">统计图</Checkbox>
          <Checkbox :label="2" :disabled="modelDisable(2)">报表</Checkbox>
        </CheckboxGroup>
      </div>
      <div class="title-center">
        <div class="conter-center">
          <span> {{ searchData.year || '-' }}年{{ taskInfo.regionName || '-' }}成绩趋势 </span>
        </div>
      </div>
      <div class="statistica-overview" v-if="overBtn.includes(1)">
        <tendency-chart
          :echarts-data="tendencyChartData"
          :echarts-loading="loading"
          @echartClick="tendencyChartClick"
        ></tendency-chart>
      </div>
      <template v-if="overBtn.includes(2)">
        <div class="table-box">
          <tendency-table :table-data="tendencyChartData" :table-loading="loading" @onClickCivil="toViewChildTendency">
            <template #tableButtons="{ isShowRank }">
              <Button
                type="primary"
                v-if="tendencyChartData?.length"
                :loading="exportLoading"
                @click="handleExport(isShowRank)"
              >
                <i class="icon-font icon-daochu f-12 mr-sm vt-middle"></i>
                <span class="vt-middle">导出</span>
              </Button>
            </template>
          </tendency-table>
        </div>
      </template>
      <create-tabs
        ref="createTabsRef"
        :componentName="themData.componentName"
        :tabs-text="themData.text"
        :tabs-query="themData.tabsQuery"
        @selectModule="selectModule"
        class="inline btn-text-default"
      >
      </create-tabs>
    </div>
    <keep-alive v-else>
      <component :is="tabComponentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
import { mapGetters } from 'vuex';
export default {
  mixins: [dealWatch],
  inject: {
    examSchemeType: {
      value: 'examSchemeType',
      default: 1,
    },
  },
  name: 'examinationtendency',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    TendencyChart: require('./modules/TendencyChart.vue').default, //趋势图表组件
    TendencyTable: require('./modules/TendencyTable.vue').default, //趋势图表组件
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    childtendency: require('./modules/child-tendency/index.vue').default,
  },
  data() {
    return {
      loading: false,
      overBtn: [1, 2],
      taskList: [],
      firstOrgCode: '',
      taskInfo: {},
      searchData: {
        examSchemeId: '',
        examTaskId: '',
        tableTitleTaskName: '',
        time: '',
        month: '',
        year: '',
        orgRegeionCode: '',
        evaluationTaskSchemeId: '',
      },
      componentName: null,
      tendencyChartData: [],
      tabComponentName: null,
      themData: {
        componentName: 'childtendency', // 需要跳转的组件名
        text: '下级趋势', // 跳转页面标题
        type: 'view',
        tabsQuery: {},
      },
      exportLoading: false,
    };
  },
  created() {
    this.getParams();
  },
  async activated() {
    this.startWatch('$route', () => {
      this.getParams();
    });
  },
  async mounted() {
    await this.initTaskList();
  },
  methods: {
    async initTaskList() {
      try {
        let params = { schemeType: this.examSchemeType };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getAlltaskList, params);
        this.taskList = data;
        if (this.taskList.length) {
          this.changeTask(this.taskList[0].id, true);
        }
      } catch (err) {
        console.log(err);
      }
    },
    onChangeModel(val) {
      if (val.length === 0) {
        return this.$Message.warning('至少保留一个');
      }
    },
    async changeTask(id) {
      try {
        if (!id) {
          return;
        }
        var item = this.taskList.find((v) => {
          return v.id === id;
        });
        this.taskInfo = item;
        this.searchData.tableTitleTaskName = item.taskName;
        this.searchData.time = '';
        this.searchData.year = '';
        if (item.taskRunState !== '0') {
          this.searchData.examTaskId = item.id;
          this.searchData.examSchemeId = item.schemeId;
          this.firstOrgCode = item.regionCode;
          this.searchData.orgRegeionCode = item.regionCode;
          const startYearMonth = this.$util.common.formatDate(item.taskStartTime, 'yyyy-MM');
          //月考核-显示年月
          this.searchData.time = startYearMonth;
          this.searchData.year = parseInt(this.$util.common.formatDate(item.taskStartTime, 'yyyy'));
          //增加参数
          this.searchData.evaluationTaskSchemeId = item.evaluationTaskSchemeId || '';
          this.setSearchTime();
          await this.getScoreTrend();
        } else {
          this.$Message.error('考核任务还未开始');
          this.tendencyChartData = [];
        }
      } catch (err) {
        console.log(err);
      }
    },
    searchBtn() {
      if (this.taskInfo.taskRunState === '0') {
        this.$Message.error('考核任务还未开始');
        return;
      }
      this.searchData.orgRegeionCode = this.firstOrgCode;
      this.setSearchTime();
      //获取成绩趋势
      this.getScoreTrend();
    },
    resetSearch() {
      if (!this.taskList.length) {
        return;
      }
      this.changeTask(this.taskList[0].id, true);
    },
    modelDisable(val) {
      return this.overBtn[0] === val && this.overBtn.length === 1;
    },
    //获取成绩趋势
    async getScoreTrend() {
      try {
        this.tendencyChartData = [];
        this.loading = true;
        const params = {
          examTaskId: this.searchData.examTaskId,
          orgRegeionCode: this.searchData.orgRegeionCode,
          time: this.searchData.time,
          year: this.searchData.year,
        };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getExamScoreTrend, params);
        this.tendencyChartData = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    //使用createTab切换至下级趋势
    toViewChildTendency(child) {
      const params = {
        ...this.searchData,
        civilName: child.civilName,
        orgCode: child.civilCode, //子级的orgCode
        taskCivilName: this.taskInfo.regionName || '-',
      };
      Object.assign(this.themData.tabsQuery, params);
      this.$refs.createTabsRef.create();
    },
    //图标点击，点击x轴触发跳转至下级趋势
    tendencyChartClick(chartsParams) {
      if (chartsParams.componentType !== 'xAxis' || chartsParams.targetType !== 'axisLabel') {
        return;
      }
      const civilObj = this.tendencyChartData.find((item) => item.civilName === chartsParams.value);
      if (!civilObj?.civilCode) {
        return;
      }
      this.toViewChildTendency(civilObj);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.tabComponentName = name;
      } else {
        this.tabComponentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    setSearchTime() {
      this.searchData.time = this.$util.common.formatDate(this.searchData.time, 'yyyy-MM');
      this.searchData.year = parseInt(this.$util.common.formatDate(this.searchData.time, 'yyyy'));
    },
    async handleExport(isShowRank) {
      try {
        this.exportLoading = true;
        const params = {
          examTaskId: this.searchData.examTaskId,
          orgRegeionCode: this.searchData.orgRegeionCode,
        };
        if (isShowRank) {
          params.hideRank = 1;
        }
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.exportexamScoreTrendExcel, params);
        this.$util.common.transformBlob(data, '成绩趋势');
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
};
</script>
<style lang="less" scoped>
.page-assessmentresult {
  height: 100%;
  @{_deep} .ivu-date-picker-cells-cell-focused {
    background: #2d8cf0;
    color: #fff;
  }
  .right-content {
    float: right;
    overflow-y: auto;
    width: 100%;
    height: 100%;
    background: var(--bg-content);
    @{_deep}.search-wrapper {
      padding: 0 20px 20px 20px !important;
      border-bottom: 1px solid var(--border-color);
      margin: 20px 0px;
      .download-scheme {
        span {
          text-decoration: underline;
        }
      }
      .el-date-editor {
        width: 212px;
      }
      .el-icon-date {
        display: none;
      }
      .ui-label {
        line-height: 34px;
      }
      .ivu-select {
        height: 34px;
      }
      .el-input__prefix {
        left: unset;
        right: 0;
        width: 32px;
      }
      .el-input__icon,
      .ivu-input-suffix i {
        color: var(--color-active);
        font-size: 16px;
      }
      .ivu-input-suffix,
      .ivu-input-suffix i {
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .el-input__icon {
        line-height: 34px;
      }
    }
    .jump {
      padding: 0 20px !important;
      margin-bottom: 10px;
    }
    .title-center {
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 10px;
      .conter-center {
        flex: 1;
        height: 30px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        display: inline-block;
        vertical-align: middle;
        span {
          line-height: 30px;
          height: 100%;
          color: var(--color-content);
          display: inline-block;
        }
      }
    }
    .statistica-overview {
      height: 54%;
      margin: 0px 20px;
    }
    .table-box {
      padding-top: 20px;
      margin: 30px 20px 0 20px;
      border-top: 1px solid var(--border-color);
    }
  }
}
</style>
