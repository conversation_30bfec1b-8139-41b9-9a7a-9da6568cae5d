<template>
  <div class="panoramic-pursuit">
    <mapCustom ref="mapBase"
        :idlerWheel="true"
        :siteListFlat="siteListFlat"
        @markerClick = "activeMarkerFn"
    >
      <template>
        <div class="resource-search" :class="computedStyle()">
          <div class="tips">
              <i class="iconfont icon-tishi"></i>
              <span class="help-tips">通过地图、资源或位置选择摄像机，点击进入实时追踪</span>
          </div>
          <!-- <div class="second-tabs">
              <span :class="{'active': currentType===tab.value}" v-for="(tab, index) in tabOptions" :key="index" @click="changeSearchType(tab.value)">{{tab.text}}</span>
          </div> -->
          <div class="device-tree-box" v-if="currentType==='resource'" :class="{'hideTree': !isShowTree}" >
              <div class="title">
                  <span>资源列表:</span>
                  <span class="show-more" @click="showTree">{{showTreeText}}</span>
              </div>
              <div class="tree">
                <deviceTree ref="deviceTree" v-show="isShowTree" :isOnlyTree="true" :showVideoTrack="true" @clickVideoTrack="clickVideoTrack"></deviceTree>
              </div>
          </div>
          <history-track ref="historyTrack" v-if="currentType==='history'"></history-track>
        </div>
      </template>
    </mapCustom>
    <panoramicpursuit-result-panel ref="resultPanel" :centerMarkerData="centerMarkerData" @close = "close"></panoramicpursuit-result-panel>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from 'vuex'
import mapCustom from '../components/map-video.vue';
import { getSimpleDeviceList } from '@/api/operationsOnTheMap'
import historyTrack from "./components/historyTrack";
import panoramicpursuitResultPanel from "./components/panoramicpursuit_result_panel.vue";
import deviceTree from '@/views/video-application/video-play/components/device-tree.vue'

export default {
  components:{
    mapCustom,
    historyTrack,
    deviceTree,
    panoramicpursuitResultPanel
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo"
    }),
  },
  data() {
    return {
      siteListFlat: [],
      currentType: "resource",
      tabOptions: [{
              text: "追踪记录",
              value: "history",
          },{
              text: "资源",
              value: "resource"
          },{
              text: "位置",
              value: "location"
      }],
      searchKey: "",
      mapOptions: {
          isShowLocation: false,
      },
      isShowTree: true,
      showTreeText: "收起",
      centerMarkerData:{},
    }
  },
  async created() {
      this.setLayoutNoPadding(true)
  },
  destroyed() {
      this.setLayoutNoPadding(false)
  },
  mounted() {
      this.getMapLayerByTypeSite()
  },
  methods: {
    ...mapMutations('admin/layout', ['setLayoutNoPadding']),
    // 撒点数据
    async getMapLayerByTypeSite() {
      let deviceTypes = [1]
      this.siteListFlat = [];
      let roleParam = {roleId: this.userInfo.roleVoList[0].id, filter: this.userInfo.roleVoList[0].initFlag == '1' ? false : true, socialResources: 0, excludeDeviceTypes: [3,4,5,6,7]}
      let { data } = await getSimpleDeviceList(roleParam)
      data = data.filter(v => deviceTypes.includes(v[2]))
      this.siteListFlat = [...data]
    },
    computedStyle() {
        return !this.isShowTree && this.currentType==='resource' ? 'hideResource' : this.currentType === "location" ? "show-location" : "";
    },
    /**
     * 切换tab标签展示
     * @param {String} type resource/location
     */
    changeSearchType(type) {
        this.currentType = type;
        this.mapOptions.isShowLocation = type === "location" ? true : false;
        this.searchKey = "";
        // type !== "location" && window.defualtLayer.removeAllOverlays();
    },
    //设备树的展开与收起
    showTree() {
        this.showTreeText = this.isShowTree ? "展开" : "收起";
        this.isShowTree = !this.isShowTree;
    },
    //设备树的搜索
    search(key) {
        // this.$refs.deviceTree.search(key);
    },
    clickVideoTrack(item) {
      this.activeMarkerFn(item, "leftResource");
    },
    //点击marker进行全景追踪或者点击左侧树进行全景追踪
    activeMarkerFn(marker, from) {
        let obj = {}
        if(from === "leftResource") {
            if(!marker.longitude || !marker.latitude) {
                this.$Message.warning("该设备无位置信息");
                return false;
            }
        } else {
            let { deviceName,deviceId,deviceGbId,Lon:longitude,Lat:latitude,deviceType,ptzType } = marker.ext
            obj = { deviceName,deviceId,deviceGbId,longitude,latitude,deviceType,ptzType }
        }
        this.centerMarkerData = {
            data: from === "leftResource" ? marker : obj,
            date: new Date().getTime()
        };
        this.$nextTick(() => {
            this.$refs.resultPanel.open();
        })
    },
    close(val) {
        this.$refs.historyTrack && this.$refs.historyTrack.getTrackList();
    },
  }
}
</script>
<style scoped lang="less">
.panoramic-pursuit {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  .resource-search {
    width: 340px;
    position: absolute;
    top: 10px;
    left: 15px;
    bottom: 50px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    padding: 10px;
    display: flex;
    flex-direction: column;
    &.show-location {
        height: 140px;
    }
    .first-tabs {
        height: 32px;
        line-height: 30px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        text-align: center;
        border: 1px solid rgba(167,172,184,0.3);
        border-radius: 3px;
        color: #2c86f8;
        cursor: pointer;
        span {
            &.active, &:hover {
                background: rgba(44, 134, 248, 0.2);
                color: #fff;
            }
        }
    }
    .tips {
        color: #666666;
        height: 30px;
        line-height: 30px;
        padding-left: 3px;
        .help-tips {
            color: #b1b5bf;
        }
    }
    .icon-tishi {
        color: #ff9429;
    }
    .second-tabs {
        display: flex;
        height: 35px;
        line-height: 35px;
        border: 1px solid rgba(167,172,184,0.3);
        span {
            flex: 1 1 33.33%;
            text-align: center;
            border-right: 1px solid rgba(167,172,184,0.3);
            cursor: pointer;
            &.active, &:hover {
                color: #2c86f8;
            }
        }
        :last-child {
            border-right: none;
        }
        
    }
    &.hideResource {
        height: 120px;
    }
    .xui-tabs {
        .xui-tab-item {
            width: 170px;
        }
    }
    .xui-input {
        margin-top: 10px;
        .xui-input-append {
            border-left: none;
        }
        .i-f_a_saerch {
            color: #666666;
            font-size: 18px;
        }
    }
    .device-tree-box {
        position: relative;
        flex: 1;
        margin-top: 5px;
        overflow: hidden;
        &.hideTree {
            height: 50px;
        }
        .title {
            height: 30px;
            line-height: 30px;
            font-size: 14px;
            .show-more {
                cursor: pointer;
                color: #2c86f8;
                float: right;
            }
        }
        .tree {
            height: calc(~"100% - 30px");
        }
    }
}
}
</style>