<template>
  <ui-modal v-model="visible" title="详情" :r-width="1400" :footerHide="true">
    <div class="main-information">
      <div class="owner-information-content card-border-color">
        <div class="owner-item" v-for="(e, key) in list" :key="key">
          <div class="owner-label card-bg title-color card-border-color" v-if="key != 'id'">{{ key }}</div>
          <div class="owner-value text-color card-border-color ellipsis" v-if="key != 'id'" :title="e">
            {{ e }}
          </div>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
export default {
  components: {},
  props: {},
  data() {
    return {
      visible: false,
      list: {}
    }
  },
  watch: {},

  created() {},
  methods: {
    // 初始化
    show(item) {
      this.visible = true
      this.list = item
    }
  }
}
</script>
<style lang="less" scoped>
.main-information {
  width: 100%;
  height: 100%;
  .owner-information-content {
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #fff;
    border-top: 1px solid #fff;
    .owner-item {
      display: flex;
      justify-content: flex-start;
      width: 33.33%;
      .owner-label {
        width: 170px;
        padding: 9px 10px;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        font-family: 'MicrosoftYaHei-Bold';
        border-right: 1px solid #fff;
        border-bottom: 1px solid #fff;
      }
      .owner-value {
        width: 283px;
        padding: 9px 10px;
        box-sizing: border-box;
        font-size: 14px;
        line-height: 20px;
        border-right: 1px solid #fff;
        border-bottom: 1px solid #fff;
      }
    }
    .owner-border-bottom {
      .owner-label,
      .owner-value {
        border-bottom: 1px solid #fff;
      }
    }
  }
}

/deep/ .ivu-modal-body {
  box-sizing: content-box;
  overflow-y: auto;
}
</style>
