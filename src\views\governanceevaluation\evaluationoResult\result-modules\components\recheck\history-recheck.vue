<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel">
    <template>
      <FormItem
        label="普通设备历史录像"
        prop="videoGeneralDay"
        v-if="getAttrs.moduleData && getAttrs.moduleData.indexType === 'VIDEO_GENERAL_HISTORY_ACCURACY'"
      >
        <Input class="width-lg" placeholder="请输入" v-model="formData.videoGeneralDay" />
        <span class="base-text-color ml-sm">天</span>
      </FormItem>
      <FormItem label="重点设备历史录像" prop="videoImportDay">
        <Input class="width-lg" placeholder="请输入" v-model="formData.videoImportDay" />
        <span class="base-text-color ml-sm">天</span>
      </FormItem>
      <FormItem label="检测内容" prop="detectionMode">
        <CheckboxGroup class="check-inline" v-model="formData.detectionMode" @on-change="checkGroupChange">
          <Checkbox
            v-for="(checkItem, checkIndex) in detectionRules"
            :key="'check' + checkIndex"
            :label="checkItem.value"
            :disabled="!!checkItem.disabled"
          >
            <span>{{ checkItem.label }}:</span>
            <span v-if="checkItem.text">
              {{ checkItem.text }}
            </span>
            <span v-else>
              {{ checkItem.text1 }}
              <Input v-model="formData.detectionTimeOut" placeholder="秒" class="ml-xs mr-xs width-mini" />
              {{ checkItem.text2 }}
            </span>
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="指标取值">
        <RadioGroup v-model="formData.indexDetectionMode">
          <Radio
            v-for="(checkItem, checkIndex) in detectionRules"
            :key="'check' + checkIndex"
            :label="(checkIndex + 1).toString()"
            :disabled="!!formData.detectionMode && !formData.detectionMode.includes((checkIndex + 1).toString())"
            >{{ checkItem.label }}
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="需要视频截图">
        <RadioGroup v-model="formData.isScreenshots">
          <Radio label="1">是 </Radio>
          <Radio label="2">否</Radio>
        </RadioGroup>
      </FormItem>
    </template>
  </basic-recheck>
</template>
<script>
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      detectionRules: [
        {
          label: '平台状态',
          text: '录像文件存在',
          value: '1',
        },
        {
          label: '信令响应',
          text1: '录像文件存在，拉流',
          text2: '秒内有响应',
          value: '2',
        },
        {
          label: '取流响应',
          text: '成功接收到历史视频流',
          value: '3',
        },
      ],
      historyVideoIndexs: [
        'VIDEO_HISTORY_ACCURACY', // 重点历史视频可调阅率
        'VIDEO_GENERAL_HISTORY_ACCURACY', // 普通历史视频可调阅率
      ],
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        videoGeneralDay: '',
        videoImportDay: '',
        detectionTimeOut: '',
        detectionMode: [], // 检测内容
        indexDetectionMode: '', // 指标取值
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        isScreenshots: '1', // 需要视频截图
        deviceIds: [],
        isUpdatePhyStatus: '',
      },
    };
  },
  created() {},
  methods: {
    checkGroupChange(val) {
      if (!val.length) {
        this.formData.indexDetectionMode = '';
        return false;
      }
      // 先找到检测内容选中的值中的最大的下标
      let maxIndex = null;
      for (let mx = this.detectionRules.length - 1; mx >= 0; mx--) {
        if (val.includes(this.detectionRules[mx].value)) {
          maxIndex = mx;
          break;
        }
      }
      // 通过检测内容中被选中的最大的下标把之前的检测内容选中
      let mode = [];
      for (let i = 0, len = maxIndex; i < len; i++) {
        if (i === 0) continue;
        mode.push(this.detectionRules[i].value);
      }
      this.$set(this.formData, 'detectionMode', Array.from(new Set([...mode, ...val])));
      if (!val.includes('3')) {
        this.$set(this.formData, 'isScreenshots', '2');
      } else {
        this.$set(this.formData, 'isScreenshots', '1');
      }
      //如果选中的检测内容的最大下标小于指标取值 则取消选中指标取值
      const index = this.detectionRules.findIndex((row) => row.value === this.formData.indexDetectionMode);
      if (index > maxIndex) {
        this.formData.indexDetectionMode = '';
      }
    },
    handleCheckDiabled(val) {
      if (!val.length) {
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b);
      sortDetectionMode.forEach((item) => {
        let startIndex = 1;
        if (item > startIndex && item !== sortDetectionMode[sortDetectionMode.length - 1]) {
          this.$set(this.detectionRules[item - 1], 'disabled', true);
        } else {
          this.$set(this.detectionRules[item - 1], 'disabled', false);
        }
      });
    },
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        videoGeneralDay: '',
        videoImportDay: '',
        detectionTimeOut: '',
        detectionMode: [], // 检测内容
        indexDetectionMode: '', // 指标取值
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        isScreenshots: '1', // 需要视频截图
        deviceIds: [],
        isUpdatePhyStatus: '',
      };
    },
  },
  watch: {
    'formData.detectionMode'(val) {
      if (!val) return false;
      this.handleCheckDiabled(val);
    },
  },
  computed: {
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          videoGeneralDay: this.formData.videoGeneralDay,
          videoImportDay: this.formData.videoImportDay,
          detectionTimeOut: this.formData.detectionTimeOut,
          detectionMode: this.formData.detectionMode,
          isScreenshots: this.formData.isScreenshots,
          indexDetectionMode: this.formData.indexDetectionMode,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        },
        ...this.$attrs,
      };
    },
  },
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
