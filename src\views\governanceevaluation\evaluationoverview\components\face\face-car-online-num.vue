<template>
  <!-- 海南省-在线/离线人脸卡口 -->
  <div class="face-online auto-fill" ref="contentScroll">
    <div class="information-main auto-fill">
      <div class="evaluation-header">
        <div class="filtrate">
          <span class="f-16 color-filter ml-sm"
            >{{ paramsList.orgRegionName }}-{{ isOnline }}{{ navbarTitle }}卡口</span
          >
        </div>
        <div>
          <span class="evaluation-time">
            <i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：{{ paramsList.examineTime || '未知' }}
          </span>
        </div>
      </div>
      <div class="abnormal-title">
        <div class="">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div class="export">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="search-wrapper">
        <ui-label class="inline" label="设备编码" :width="70">
          <Input class="width-md" placeholder="请输入设备编码" v-model="searchData.deviceId"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="设备名称" :width="70">
          <Input class="width-md" placeholder="请输入设备名称" v-model="searchData.deviceName"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.sbdwlx" :width="100">
          <Select
            class="width-sm"
            v-model="searchData.sbdwlx"
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.phyStatus" :width="70">
          <Select v-model="searchData.outcome" clearable placeholder="请选择" class="input-width">
            <Option :value="1">在线</Option>
            <Option :value="2">离线</Option>
          </Select>
        </ui-label>
        <ui-label :width="70" class="inline" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button class="mr-sm" @click="resetSearchDataMx(searchData, search)"> 重置 </Button>
        </ui-label>
      </div>
      <div class="tableList auto-fill">
        <slot name="search"></slot>
        <div class="left-div auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :loading="loading"
            :table-columns="tableColumns"
            :table-data="tableData"
            ref="table"
          >
            <template #description="{ row }">
              <span :class="row.outcome == '1' ? 'sucess' : 'error'">{{ row.description }}</span>
            </template>
            <template #outcome="{ row }">
              <span v-if="row.outcome == '1'" :class="row.outcome == '1' ? 'sucess' : 'error'">{{
                row.qualifiedNum
              }}</span>
              <span v-if="row.outcome == '2'" :class="row.outcome == '1' ? 'sucess' : 'error'">{{
                row.unqualifiedNum
              }}</span>
            </template>
            <template #option="{ row }">
              <ui-btn-tip icon="icon-chakanjietu" :content="pictureTitle" @click.native="checkReason(row)"></ui-btn-tip>
            </template>
          </ui-table>
          <ui-page
            class="page"
            :page-data="searchData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          ></ui-page>
        </div>
      </div>
    </div>
    <car-picture
      v-model="carpictureShow"
      v-if="carpictureShow"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
      :title="pictureTitle"
    ></car-picture>
    <face-pictute
      v-model="pictureShow"
      v-if="pictureShow"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
      :title="pictureTitle"
    ></face-pictute>
  </div>
</template>

<style lang="less" scoped>
.face-online {
  position: relative;
  // overflow-y: auto;
  // &::-webkit-scrollbar {
  //   display: none; /* Chrome Safari */
  // }
  // @{_deep} .ivu-table {
  //   &-tip,
  //   &-overflowX,
  //   &-body {
  //     min-height: calc(100vh - 642px) !important;
  //     //min-height: 290px !important;
  //   }
  // }

  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: calc(100% - 340px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 15px 13px;

      .statistics-ul {
        width: calc(100% - 340px);
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        li {
          position: relative;
        }
        .icon-budabiao {
          position: absolute;
          top: 192px;
          right: 15px;
          line-height: 0;
          font-size: 14px;
          cursor: default;
          display: inline;
        }
        .icon-dabiao {
          position: absolute;
          top: 192px;
          right: 15px;
          line-height: 0;
          font-size: 14px;
          cursor: default;
          display: inline;
        }
      }
    }

    .information-ranking {
      width: 340px;
      background: var(--bg-sub-content);
      height: 100%;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .information-main {
    .evaluation-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 48px;
      border-bottom: 1px solid var(--devider-line);

      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }

      .evaluation-time {
        color: #a9bed9;
        font-size: 14px;
        margin-left: 10px;

        .mr-x {
          margin-right: 3px;
        }
      }

      .active {
        background: rgba(2, 57, 96, 1);
      }
    }
    .abnormal-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .input-width {
        width: 176px;
      }
    }
    .tableList {
      height: 100%;
      width: 100%;
      background: var(--bg-content);
      .left-div {
        position: relative;
        width: 100%;
        min-height: 1px;
      }
      .ui-table {
        width: 100%;
      }
      .page {
        padding-right: 0;
      }
      .ui-table-scroll-nodata {
        /deep/ .ivu-table-tip {
          overflow-x: auto;
        }
      }
    }
  }
  .sucess {
    color: @color-success;
  }
  .error {
    color: @color-failed;
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { mapGetters, mapActions } from 'vuex';
export default {
  mixins: [downLoadTips],
  name: 'face-car-top-online',
  data() {
    return {
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: `${this.global.filedEnum.deviceId}`, key: 'deviceId' },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
        { title: '组织机构', key: 'orgName' },
        { title: '点位类型', key: 'sbdwlxText' },
        { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
        { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr' },
        { title: '抓拍数量', key: 'outcome', slot: 'outcome' },
        { title: '设备在线状态', key: 'description', slot: 'description' },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 70,
          align: 'center',
        },
      ],
      tableData: [],

      exportLoading: false,
      rankData: [],
      paramsList: {},
      statisticalList: {},
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      searchData: {
        deviceId: '',
        deviceName: '',
        sbdwlx: '',
        outcome: '',
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      deviceInfoId: '',
      reasonLoading: false,
      pictureShow: false,
      carpictureShow: false,
      deviceIdNum: '',
      pictureTitle: '人脸抓拍图片',
      navbarTitle: '人脸',
    };
  },
  activated() {
    this.paramsList = this.$route.query;
    this.getTitle();
    this.setDefaultStatus();
    this.getTableData(); //表格
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
    isOnline() {
      return this.$route.query.outcome === 1 ? '在线' : '离线';
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    getTitle() {
      let { indexId } = this.paramsList;
      if (Number.parseInt(indexId) === 2016) {
        this.navbarTitle = '人脸';
        this.pictureTitle = '查看抓拍图片';
      } else if (Number.parseInt(indexId) === 3020) {
        this.pictureTitle = '查看过车图片';
        this.navbarTitle = '车辆';
      }
    },
    search() {
      this.searchData.pageNumber = 20;
      this.searchData.pageNum = 1;
      this.getTableData();
    },

    async getReason() {
      try {
        this.reasonTableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.deviceIdNum,
          },
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        this.reasonLoading = true;
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
        this.reasonPage.totalCount = datas.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    checkReason(row) {
      this.reasonPage = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.deviceIdNum = row.deviceId;
      this.getReason(row);
      if (Number.parseInt(this.$route.query.indexId) === 3020) {
        this.carpictureShow = true;
      } else {
        this.pictureShow = true;
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            sbdwlx: this.searchData.sbdwlx,
            outcome: this.searchData.outcome,
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            sbdwlx: this.searchData.sbdwlx,
            outcome: this.searchData.outcome,
            exportType: 'first', //value：first、second、third
          },
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    setDefaultStatus() {
      this.searchData.outcome = this.$route.query.outcome;
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    orgRegionName: {},
    examineTime: {},
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val && val.batchId) {
          this.paramsList = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    facePictute: require('./components/face-pictute.vue').default,
    CarPicture: require('../components/car-picture.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
