<template>
  <div class="calender">
    <ul class="week-list">
      <li v-for="(item, index) in weekList" :key="index" class="square week-square">
        {{ item._label }}
      </li>
    </ul>
    <ul class="day-list">
      <li
        v-for="(item, index) in dayList"
        :key="index"
        :class="[
          'square',
          'day-square',
          item._day === activeDay && !item._notThisMonth ? 'active' : '',
          item._notThisMonth ? 'not-this-month' : '',
        ]"
      >
        <slot name="calenderDay" :label="item._label" :value="item._value" :not-this-month="item._notThisMonth">
          {{ item._label }}
        </slot>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'calender',
  props: {
    // 是否补入上个月
    fillPreviousMonth: {
      type: Boolean,
      default: true,
    },
    // 上个月的是否可用
    previousMonthAble: {
      type: Boolean,
      default: false,
    },
    // 是否补入下个月
    fillNextMonth: {
      type: Boolean,
      default: true,
    },
    // 下个月的是否可用
    nextMonthAble: {
      type: Boolean,
      default: false,
    },
    // 显示的时间格式 日期格式对应字符如下(年-yy,月-MM,日-dd,字符区分大小写)
    format: {
      type: String,
      default: 'dd',
    },
    // 选中的年月份 以yy-MM格式传入
    yearMonth: {
      type: String,
    },
  },
  data() {
    return {
      weekList: [
        {
          _label: '星期一',
          _value: '1',
        },
        {
          _label: '星期二',
          _value: '2',
        },
        {
          _label: '星期三',
          _value: '3',
        },
        {
          _label: '星期四',
          _value: '4',
        },
        {
          _label: '星期五',
          _value: '5',
        },
        {
          _label: '星期六',
          _value: '6',
        },
        {
          _label: '星期日',
          _value: '7',
        },
      ],
      dayList: [],
      activeDay: null,
    };
  },
  created() {},
  methods: {
    // 获取当前年月有多少天
    getDays(Year, Month) {
      let days = new Date(Year, Month, 0).getDate();
      return days;
    },
    // 获取当前年月日是周几
    getWeekday(Year, Month, Day = 1) {
      const week = new Date(Year, Month - 1, Day).getDay();
      return week;
    },
    // 获取本月的天数
    getDayList(Year, Month) {
      this.dayList = [];
      const days = this.getDays(Year, Month);
      for (let i = 1, len = days; i <= len; i++) {
        this.dayList.push({
          _label: this.$util.common.formatDate(new Date(`${Year}`, `${Month - 1}`, `${i}`), this.format),
          _value: this.$util.common.formatDate(new Date(`${Year}`, `${Month - 1}`, `${i}`), 'yyyy-MM-dd'),
          _notThisMonth: false,
          _day: i,
          _disabled: false,
        });
      }
      this.getPreviousMonthDay(Year, Month);
      this.fillNextMonth && this.getNextMonthDay(Year, Month);
    },
    // 获取上个月补入这个月的日期
    getPreviousMonthDay(Year, Month) {
      const firstWeekDay = this.getWeekday(Year, Month);
      const fillPreviousDays = firstWeekDay === 0 ? 7 : firstWeekDay;
      const previousMonthDays = Month === 1 ? this.getDays(Year - 1, 12) : this.getDays(Year, Month - 1);
      for (let i = 0, len = fillPreviousDays - 1; i < len; i++) {
        this.dayList.unshift({
          _label: this.fillPreviousMonth
            ? this.$util.common.formatDate(
                new Date(
                  `${Month === 1 ? Year - 1 : Year}`,
                  `${Month === 1 ? 11 : Month - 2}`,
                  `${previousMonthDays - i}`,
                ),
                this.format,
              )
            : '',
          _value: this.$util.common.formatDate(
            new Date(`${Month === 1 ? Year - 1 : Year}`, `${Month === 1 ? 11 : Month - 2}`, `${previousMonthDays - i}`),
            'yyyy-MM-dd',
          ),
          _notThisMonth: true,
          _day: previousMonthDays - i,
          _disabled: !this.previousMonthAble,
        });
      }
    },
    // 获取下个月补入这个月的日期
    getNextMonthDay(Year, Month) {
      let nextTotalDay = 42 - this.dayList.length;
      for (let i = 1, len = nextTotalDay; i <= len; i++) {
        this.dayList.push({
          _label: this.fillPreviousMonth
            ? this.$util.common.formatDate(
                new Date(`${Month === 12 ? Year + 1 : Year}`, `${Month === 12 ? 0 : Month}`, `${i}`),
                this.format,
              )
            : '',
          _value: this.$util.common.formatDate(
            new Date(`${Month === 12 ? Year + 1 : Year}`, `${Month === 12 ? 0 : Month}`, `${i}`),
            'yyyy-MM-dd',
          ),
          _day: i,
          _notThisMonth: true,
          _disabled: !this.nextMonthAble,
        });
      }
    },
  },
  watch: {
    yearMonth: {
      handler(val) {
        if (val && val.includes('-')) {
          const year = +val.split('-')[0];
          const month = +val.split('-')[1];
          this.getDayList(year, month);
        }
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .calender {
    .square {
      background: #ebedf1;
      border: 1px solid #d8d8d8;
      border-right: none;
      border-bottom: none;
      &:nth-child(7n) {
        border-right: 1px solid #d8d8d8;
      }
      &:nth-child(n + 36) {
        border-bottom: 1px solid #d8d8d8;
      }
    }
    .not-this-month {
      color: rgba(0, 0, 0, 0.4);
    }
    .week-square {
      color: rgba(0, 0, 0, 0.9);
    }
    .day-square {
      background-color: #f9f9f9;
      &:nth-child(n + 8) {
        background: #f1f1f1;
      }
      &:nth-child(n + 15) {
        background: #f9f9f9;
      }
      &:nth-child(n + 22) {
        background: #f1f1f1;
      }
      &:nth-child(n + 29) {
        background: #f9f9f9;
      }
      &:nth-child(n + 36) {
        background: #f1f1f1;
      }
      &:last-child {
        border-right: 1px solid #d8d8d8;
      }
    }
  }
}
.calender {
  color: var(--color-content);
  .day-list,
  .week-list {
    display: flex;
    flex-wrap: wrap;
  }
  .square {
    font-size: 16px;
    height: 73px;
    line-height: 73px;
    width: 14.28%;
    text-align: center;
    background: #103c84;
    border: 1px solid #1d5fc0;
    border-right: none;
    &:nth-child(7n) {
      border-right: 1px solid #1d5fc0;
    }
  }
  .not-this-month {
    color: #56789c;
  }
  .week-square {
    color: #aecbfd;
    font-weight: 700;
  }
  .day-square {
    font-size: 14px;
    background-color: #06265a;
    &:nth-child(n + 8) {
      background: #092d67;
    }
    &:nth-child(n + 15) {
      background: #06265a;
    }
    &:nth-child(n + 22) {
      background: #092d67;
    }
    &:nth-child(n + 29) {
      background: #06265a;
    }
    &:nth-child(n + 36) {
      background: #092d67;
    }
    &:last-child {
      border-right: 1px solid #1d5fc0;
    }
  }
}
</style>
