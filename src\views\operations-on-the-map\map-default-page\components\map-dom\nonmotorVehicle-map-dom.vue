<!--
    * @FileDescription: 非机动车
    * @Author: H
    * @Date: 2023/5/12
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-05 17:12:25
-->
<template>
  <div class="dom">
    <section class="dom-content">
      <carousel
        ref="carousel"
        v-show="positionPoints.length"
        :same-position-point="samePositionPoint"
        @changeVid="changeHeadTab"
      ></carousel>
      <div class="info-content">
        <div class="info-content-left" style="width: 200px">
          <div class="smallImg" style="width: 200px; height: inherit">
            <img v-lazy="dataInfo.traitImg" alt="" />
            <!-- <span class="similarity" v-if="dataInfo.similarity">{{ dataInfo.similarity }}%</span> -->
          </div>
          <div class="title">
            <span class="active">通行记录</span>
          </div>
          <div class="traffic-record">
            <div class="dom-content-p">
              <span class="label">通过地点</span><span>：</span>
              <!-- <p class="message" v-show-tips>{{ dataInfo.deviceName }}</p> -->
              <ui-textOver-tips
                class="message"
                refName="deviceName"
                :content="dataInfo.deviceName"
              ></ui-textOver-tips>
            </div>
            <div class="dom-content-p">
              <span class="label">通过时间</span><span>：</span>
              <span class="message">{{ dataInfo.absTime || "--" }}</span>
            </div>
            <structuredmsg :info="dataInfo" type="4"></structuredmsg>
          </div>
        </div>
        <div class="info-content-right">
          <!-- <ui-image :src="dataInfo.sceneImg" alt="静态库" viewer /> -->
          <div class="fun-img" id="fun-img">
            <img
              id="imgBox"
              v-lazy="dataInfo.sceneImg"
              @load="loadImage($event, dataInfo.vehicleRect)"
              alt=""
            />
            <div
              class="select-preview"
              :style="{
                left: imgBoxList.x + 'px',
                top: imgBoxList.y + 'px',
                width: imgBoxList.width + 'px',
                height: imgBoxList.height + 'px',
              }"
            ></div>
          </div>
        </div>
      </div>
    </section>
    <footer v-if="cutIcon">
      <search-around
        @preDetial="preDetial"
        @nextDetail="nextDetail"
      ></search-around>
    </footer>
  </div>
</template>

<script>
import carousel from "./carousel.vue";
import searchAround from "../search-around.vue";
import structuredmsg from "@/components/mapdom/structuredmsg.vue";
import { myMixins } from "../mixins/index.js";
export default {
  name: "",
  components: {
    carousel,
    structuredmsg,
    searchAround,
  },
  mixins: [myMixins], //全局的mixin
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
    // 是否是地图上点击详情展示
    isMapClick: {
      type: Boolean,
      default: false,
    },
    clickIndexId: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      samePositionPoint: [],
      dataInfo: {},
      makerName: "",
      cutIcon: true,
      currentTabIndex: 0,
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    init(item, pointItem, name = "", cutIcon = true, position = []) {
      // 为了兼容之前的代码，暂时先返回写死的data: true
      return new Promise((resolve) => {
        this.transform = {
          scale: 1,
          deg: 0,
          offsetX: 0,
          offsetY: 0,
          enableTransition: false,
        };
        this.dataInfo = { ...pointItem };
        this.makerName = name;
        this.cutIcon = cutIcon; //用于判断模态框下方左右切换显隐
        if (position.length > 0) {
          // 高级搜索，数据上图
          this.tabAdvanced(position);
        }
        if (this.clickIndexId) {
          //普通搜索用于顶部切换
          this.tabmsgList();
        }

        resolve({ data: true });
      });
    },
    tabmsgList() {
      this.samePositionPoint = this.positionPoints.filter((item, index) => {
        if (item.deviceId === this.dataInfo.deviceId) {
          item.Index = index + 1;
        }
        return item.deviceId === this.dataInfo.deviceId;
      });
      let tabIndex = 0;
      this.samePositionPoint.map((item, index) => {
        if (item.recordId == this.clickIndexId) {
          tabIndex = index;
        }
      });

      this.$refs.carousel.init(tabIndex);
    },
    tabAdvanced(list) {
      this.samePositionPoint = list.filter((item, index) => {
        if (item.deviceId === this.dataInfo.deviceId) {
          item.Index = index + 1;
        }
        return item.deviceId === this.dataInfo.deviceId;
      });
      let tabIndex = 0;
      this.$refs.carousel.init(tabIndex);
    },
    tabClick(index) {
      this.currentTabIndex = index;
    },
    // 顶部图片切换
    changeHeadTab(item) {
      this.$emit("changeListTab", { ...item, name: this.makerName });
      this.dataInfo = { ...item };
      // this.$refs.facemap.init();
    },
    // 上一页
    preDetial() {
      let Index = this.positionPoints.findIndex(
        (item) => item.featureId === this.dataInfo.featureId
      );
      if (Index < 1) return;
      this.$emit("preDetial", Index - 1);
    },
    // 下一页
    nextDetail() {
      let Index = this.positionPoints.findIndex(
        (item) => item.featureId === this.dataInfo.featureId
      );
      if (Index >= this.positionPoints.length - 1) return;
      this.$emit("nextDetail", Index + 1);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.dom-wrapper {
  position: relative;
  // padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}
.dom {
  width: 100%;
  // height: 520px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;

  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px 1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }

  .dom-content {
    padding: 10px 20px;
    font-size: 14px;
    height: calc(~"100% - 46px");
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  > footer {
    height: 50px;
    border-top: 1px solid #d3d7de;
    padding: 0 20px;
    // height: 55px;
    // line-height: 55px;
    > span {
      color: #2c86f8;
      margin-right: 20px;
      cursor: pointer;
    }
  }
}
/deep/ #npgis_GroupDiv {
  overflow: inherit;
}
/deep/.olPopupContent {
  overflow: inherit;
}
</style>
