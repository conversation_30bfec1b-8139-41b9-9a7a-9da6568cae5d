<template>
  <!-- 设备资产 -->
  <div class="plant-assets" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-shebeizichan" v-model="activeValue" :data="tabData"> </HomeTitle>
    <div class="plant-content">
      <ul>
        <li v-for="(item, index) in this.plantList" :key="index">
          <div class="monitoring-data">
            <img :src="item.img" alt srcset />
            <span class="item">
              <p class="video-color" :class="item.plantClass">{{ item.name }}</p>
              <p class="statistic-num">
                <countTo :startVal="0" :endVal="item.value || 0" :duration="3000"></countTo>
              </p>
            </span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<style lang="less" scoped>
.plant-assets {
  width: 400px;
  height: 31%;
  display: inline-block;
  background: rgba(0, 104, 183, 0.13);
  .plant-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .plant-content {
    width: 100%;
    height: calc(100% - 32px);
    ul {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      text-decoration: none;
      width: 100%;
      height: 100%;

      li {
        width: 33.3%;
        height: 33%;
        padding: 10px 10px 10px 5px;
        div {
          height: 56px;
          width: 100%;
          // border-right: 1px solid #094a8a;
          line-height: 56px;
          display: flex;
          justify-content: center;
          align-items: center;

          img {
            width: 44px;
            height: 44px;
          }
          span {
            display: inline-block;
            height: 44px;
            flex: 1;
            text-align: left;
            p {
              white-space: nowrap;
              font-style: normal;
              height: 25px;
              line-height: 25px;
              color: #fff;
            }
            .video-color {
              font-size: 12px;
              font-family: Microsoft YaHei;
              font-weight: bold;
            }
            .statistic-num {
              font-size: 16px;
              font-family: Microsoft YaHei;
              font-weight: bold;
              color: #e1f2fd;
            }
            .f1_color {
              color: #2dbeff;
            }
            .f2_color {
              color: #36cd9f;
            }
            .f3_color {
              color: #b57ff7;
            }
            .f4_color {
              color: #e08749;
            }
            .f5_color {
              color: #cbbdde;
            }
            .f6_color {
              color: #2dbeff;
            }
            .f7_color {
              color: #f57257;
            }
            .f8_color {
              color: #11cdd4;
            }
          }
          .item {
            margin-left: 5px;
          }
        }
      }
    }
  }
}
.full-screen-container {
  position: absolute;
  top: 9%;
  height: 26%;
  margin-left: 10px;
}
</style>
<script>
import { mapGetters } from 'vuex';

export default {
  name: 'plant-assets',

  data() {
    return {
      activeValue: 'device',
      tabData: [{ label: '设备资产', id: 'device' }],
    };
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  mounted() {},
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  methods: {},
  props: {
    plantList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    HomeTitle: require('./home-title').default,
    countTo: require('vue-count-to').default,
  },
};
</script>
