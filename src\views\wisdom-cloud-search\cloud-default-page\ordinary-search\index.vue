<template>
	<section class="my-container">
		<div class="search-content">
			<div class="input-content">
				<Dropdown trigger="custom" :visible="visible" placement="bottom-start">
					<div class="pic-box" @click="pictureShowHandle" v-if="previewImgList.length > 0">
						<p class="img-preview-content">
							<span class="num">{{ previewImgList.length }}</span>
							<img :src="previewImgList[0].fileUrl" alt="" />
						</p>
					</div>
					<Input v-model="indexSearchData.keyWords" :maxlength="50" class="search-input" :class="{ 'has-img-preview': previewImgList.length > 0 }" :placeholder="searchPlaceholder">
					<i class="iconfont icon-xiangji" slot="suffix" @click="pictureShowHandle"></i>
					<Button type="primary" class="search-btn" :disabled="isImage" slot="append" @click="keyWordsSearchHandle">搜索</Button>
					</Input>
					<DropdownMenu slot="list">
						<search-pictures ref="searchPictures" :pic-data="picData" @clearUrl="clearUrl" @cancel="closePictureModel" @imgUrlChange="imgUrlChange" @clearPreview="clearPreview" @deleteImgUrl="deleteImgUrl" @search="pictureSearchHandle" />
					</DropdownMenu>
				</Dropdown>
			</div>
		</div>
		<div class="result-content">
            <!-- :width="50 / 192 + 'rem'" -->
			<Menu :active-name="sectionName" :width="50 / 192 + 'rem'" @on-select="selectItemHandle" class="search-menu">
				<MenuItem v-for="(item, index) in menuList" :key="index" :name="item.name">
				<Tooltip :content="item.label" placement="right" theme="light">
					<i class="iconfont" :class="item.iconName"></i>
				</Tooltip>
				</MenuItem>
			</Menu>
			<keep-alive>
				<component :is="sectionName" :indexSearchData="indexSearchData" :isImage="isImage" ref="component" />
			</keep-alive>
		</div>
	</section>
</template>
<script>
	import searchPictures from '../components/search-pictures'
	import policeServiceContent from './pages/police-service-content.vue'
	import faceContent from './pages/face-content'
	import vehicleContent from './pages/vehicle-content'
	import humanBodyContent from './pages/human-body-content.vue'
	import nonmotorVehicleContent from './pages/nonmotor-vehicle-content.vue'
	import wifiContent from './pages/wifi-content'
	import RFIDContent from './pages/RFID-content'
	import electricContent from './pages/electric-content'
	import deviceContent from './pages/device-content'
	import gpsContent from './pages/gps-content'
	import { mapMutations, mapGetters, mapActions } from 'vuex'

	export default {
		name: 'ordinarySearch',
		components: {
			searchPictures,
			policeServiceContent, //警务模块
			faceContent, //人脸模块
			vehicleContent, //车辆模块
			humanBodyContent, //人体
			nonmotorVehicleContent, //非机动车
			wifiContent, //wifi模块
			RFIDContent, //rfid模块
			electricContent, //电子模块
			deviceContent,//设备模块
			gpsContent   //GPS

		},
		data() {
			return {
				indexSearchData: {},
				previewImgList: [],
				visible: false,
				sectionName: '',
				firstIn: true,        // 初次进入页面跳转到对应页面
				keyWords: '',
				menuList: [
					// { label: '警务', value: 1, iconName: 'icon-anjian', name: 'policeServiceContent' },
					{ label: '人像', value: 2, iconName: 'icon-renlian', name: 'faceContent' },
					{ label: '车辆', value: 3, iconName: 'icon-cheliang', name: 'vehicleContent' },
					{ label: '人体', value: 8, iconName: 'icon-renti', name: 'humanBodyContent' },
					{ label: '非机动车', value: 9, iconName: 'icon-feijidongche', name: 'nonmotorVehicleContent' },
					{ label: 'MAC', value: 4, iconName: 'icon-wifi', name: 'wifiContent' },
					{ label: 'RFID', value: 5, iconName: 'icon-RFID', name: 'RFIDContent' },
					{ label: '电围', value: 6, iconName: 'icon-ZM-dianwei', name: 'electricContent' },
					{ label: 'GPS', value: 7, iconName: 'icon-gps', name: 'gpsContent' },

					// { label: '设备', value: 7, iconName: 'icon-shebeizichan', name: 'deviceContent' }
				],
				isImage: false, // 是否以图搜图
				searchPlaceholder: '请输入关键词检索，多个关键词请用空格隔开',
				routeList: { //用于判断搜图跳转的菜单
					1: 'faceContent',
					2: 'vehicleContent',
					3: 'humanBodyContent',
					4: 'nonmotorVehicleContent',
				},
			}
		},
		computed: {
			...mapGetters({
				picData: 'common/getWisdomCloudSearchData'
			})
		},
		watch: {
			$route: {
				handler(to, from) {
					if (to.fullPath.includes('advancedSearch')) return
					let { sectionName, urls } = to.query;
					if (sectionName) {
						let hasSectionName = this.menuList.some(d => d.name === sectionName);
						this.sectionName = hasSectionName ? sectionName : this.menuList[0].name;
					}
					// 页面刷新调用接口请求
					setTimeout(() => {
						if (this.sectionName == 'deviceContent' && !this.isImage) {
							this.$refs.component.deviceCount()
						}
						// this.$refs.component.indexSearchHandle()
						if (from) {
							if (from && from.fullPath == '/wisdom-cloud-search/cloud-default-page' || from.fullPath.includes('type')) {
								this.firstIn = true;
								let type = this.getUrlOption(from.fullPath).type;
								this.init();
								this.$store.commit('common/setPageType', 0)
								this.$nextTick(() => {
									if (type == '1') {
										this.clearUrl();
										this.closePictureModel()
									}
									if (this.sectionName == 'policeServiceContent') {
										this.$refs.component.selectCatalog()
									} else if (this.sectionName == 'deviceContent' || this.sectionName == 'faceContent') {
										this.$refs.component.searchHandle()
									}
								})
							}
						} else {
							if (sectionName) {
								let hasSectionName = this.menuList.some(d => d.name === sectionName);
								this.sectionName = hasSectionName ? sectionName : this.menuList[0].name;
							}
							this.sectionName = this.sectionName ? this.sectionName : 'faceContent';
						}
					}, 20)
				},
				immediate: true,
				// 深度观察监听
				deep: true
			}
		},
		activated() {
			// 用于人像/警务搜索条件显示
			this.$store.commit('common/setPageType', 0)
			this.setLayoutNoPadding(true)
		},
		mounted() {
			this.init()
		},
		deactivated() {
			this.setLayoutNoPadding(false)
		},
		beforeDestroy() {
			this.setLayoutNoPadding(false)
		},
		methods: {
			...mapActions({
				addCloud: 'common/addCloud',
			}),
			...mapMutations('admin/layout', ['setLayoutNoPadding']),
			...mapMutations('common', ['setWisdomCloudSearchData']),
			/**
			 * 设置检索条件， 从首页带入的参数
			 */
			init() {
				// 检索图片设置
				// if (this.picData.urlList) {
				//   this.previewImgList = this.picData.urlList.filter(url => {
				//     if (url.fileUrl > 0) return url
				//   })
				// }
				this.previewImgList = this.picData.urlList.filter(url => {
					if (url.fileUrl) return url
				})
				if (this.previewImgList.length > 0) {
					this.isImage = true;
					this.searchPlaceholder = '点击上传图片';
				} else {
					this.isImage = false;
					this.searchPlaceholder = '请输入关键词检索，多个关键词请用空格隔开';
				}
				// 当前处于警务页面，不作跳转动作
				if (this.sectionName != 'policeServiceContent' || this.firstIn) {
					this.firstIn = false
					// 以图搜图时默认跳转到对应页面
					// 特征值类型 1:人脸 2:车辆 3:人体 4: 非机动车
					if (!this.picData.keyWords || this.picData.keyWords == '') {
						this.sectionName = this.routeList[this.picData.algorithmType]
					}
				}
				// 以图搜图字段
				let features = []
				this.picData.urlList.forEach(item => {
					if (item) {
						features.push(item.feature)
					}
				})
				// 设置关键词搜索
				this.keyWords = this.picData.keyWords;
				this.indexSearchData = {
					algorithmType: this.picData.algorithmType,
					keyWords: this.picData.keyWords,
					features: features,
					similarity: this.picData.similarity / 100
				};
			},
			/**
			 * 清空
			 */
			clearUrl() {
				this.isImage = false;
				this.previewImgList = [];
				this.indexSearchData.urlList = [];
				this.indexSearchData.features = [];
				this.searchPlaceholder = '请输入关键词检索，多个关键词请用空格隔开';
			},
			/**
			 * 清空搜索框上面的图片数量
			 */
			clearPreview() {
				this.previewImgList = [];
				this.searchPlaceholder = '请输入关键词检索，多个关键词请用空格隔开';
			},
			deleteImgUrl(urlList) {
				let list = urlList.filter(item => !!item);
				if (list.length == 0) {
					this.searchPlaceholder = '请输入关键词检索，多个关键词请用空格隔开';
				}
			},
			/**
			 * 图片上传变化
			 */
			imgUrlChange(urlList) {
				if (urlList.length == 0) {
					// 清空操作
					this.isImage = false
					this.previewImgList = []
				}
				// else{ // 添加图片
				this.indexSearchData.keyWords = ''
				this.isImage = true
				// this.previewImgList = urlList
				this.previewImgList = urlList.filter(url => {
					if (url.fileUrl) return url
				})
			},
			pictureShowHandle() {
				this.visible = !this.visible
				// this.setInputStatus(this.previewImgList)
			},
			/**
			 * 设置输入框可用状态
			 */
			setInputStatus(urlList) {
				var arr = urlList.filter(item => {
					return item.length > 1
				})
				if (arr.length == 0) {
					this.isImage = false
				} else {
					this.addStoreSearchPicture()
					this.isImage = true
				}

				if (this.isImage) {
					this.keyWords = ''
				}
			},
			// 添加当前信息放入vuex里面
			addStoreSearchPicture() {
				let query = {
					keyWords: this.indexSearchData.keyWords,
					algorithmType: this.$refs.searchPictures.algorithmType,
					similarity: this.$refs.searchPictures.similarity,
					urlList: this.$refs.searchPictures.urlList
				}
				this.setWisdomCloudSearchData(query)
			},
			selectItemHandle(sectionName) {
				const path = this.$route.fullPath
				this.$router.push({ path, query: { sectionName } })
			},
			// 关键字搜索
			keyWordsSearchHandle() {
				// if (!this.indexSearchData.keyWords) {
				//   this.$Message.warning('请输入关键词，多个关键词请用空格隔开')
				//   return false
				// }
				this.addStoreSearchPicture()
				this.addCloud()
				this.visible = false
				this.$refs.component.indexSearchHandle()
			},
			// 打开头像搜索弹框
			/**
			 * 以图搜图搜索
			 */
			pictureSearchHandle(params) {
				// 当前处于警务页面，不作跳转动作
				// 特征值类型 1:人脸 2:车辆 3:人体 4: 非机动车
				const path = this.$route.fullPath;
				if (this.$route.query.sectionName) {
					if (this.$route.query.sectionName != 'policeServiceContent' || !this.sectionName) {
						this.$router.push({ path, query: { sectionName: this.routeList[params.algorithmType] } })
					}
				} else { //从搜索页面进入
					this.$router.push({ path, query: { sectionName: this.routeList[params.algorithmType] } })
				}
				const list = params.urlList.filter(e => e && e.feature)
				if (!list.length) {
					this.searchPlaceholder = '请输入关键词检索，多个关键词请用空格隔开';
				} else {
					this.searchPlaceholder = '点击上传图片';
				}
				let features = []
				params.urlList.forEach(item => {
					if (item) {
						features.push(item.feature)
					}
				})
				this.indexSearchData = {
					algorithmType: params.algorithmType,
					features: features,
					similarity: params.similarity / 100
				}
				this.addStoreSearchPicture()
				this.addCloud()
				this.$refs.component.indexSearchHandle()
				this.visible = false
			},
			// 关闭头像搜索弹框
			closePictureModel() {
				this.visible = false
				// this.setInputStatus(this.previewImgList)
			},
			// 获取路由参数
			getUrlOption(url) {
				let str = url.substr(1).split("&");
				let obj = {};
				str.forEach(e => {
					let list = e.split("=");
					obj[list[0]] = list[1];
				});
				return obj
			},
		}
	}
</script>
<style lang="less" scoped>
	/deep/ .search-pictures {
		width: 700px;
	}
	.my-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		padding: 10px;
	}
	.search-content {
		display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 10px;
		margin-bottom: 20px;
		.title {
			img {
				width: 170px;
				height: auto;
			}
		}
		.ivu-dropdown {
			display: block;
		}
		.advance-search-btn {
			color: #2c86f8;
			font-size: 16px;
			background-color: transparent;
			padding: 0 30px;
			&:hover {
				color: #4597ff;
			}
			&:active {
				color: #1a74e7;
			}
		}
		.input-content {
			position: relative;
			.pic-box {
				position: absolute;
				width: 590px;
				height: 38px;
				top: 0px;
				z-index: 101;
				cursor: pointer;
			}
			.img-preview-content {
				// position: absolute;
				width: 30px;
				height: 30px;
				// top: 50%;
				// margin-top: -15px;
				border: 1px solid #d3d7de;
				box-sizing: border-box;
				// left: 5px;
				z-index: 101;
				margin: 4px 0 0 3px;
				cursor: pointer;
				.num {
					background-color: #2c86f8;
					color: #fff;
					width: 30px;
					height: 30px;
					text-align: center;
					line-height: 30px;
					font-size: 25px;
					position: absolute;
					border-radius: 2px;
					transform: scale(0.5);
					left: -4px;
					top: -3px;
				}
				img {
					display: block;
					width: 100%;
					height: 100%;
				}
			}
		}
		/deep/ .search-input {
			width: 700px;
			height: 38px;
			&.has-img-preview {
				.ivu-input {
					padding-left: 42px;
				}
			}
			.ivu-input {
				height: 100%;
				font-size: 14px;
				padding-right: 50px;
				border: 1px solid #e8eaef;
				box-shadow: 0px 11px 28px 0px rgba(156, 186, 230, 0.3);
				&:hover,
				&:focus {
					border-color: #9cbae6;
				}
			}
		}
		/deep/ .ivu-input-suffix {
			right: 110px;
			display: flex;
			align-items: center;
			z-index: 100;
			width: 50px;
			height: 36px;
			top: 1px;
			background: #fff;
			cursor: pointer;
			.iconfont {
				font-size: 22px;
				color: #888;
				margin: 0 auto;
			}
		}
		/deep/ .ivu-input-group-append {
			width: 110px;
		}
	}
	.result-content {
		background-color: #fff;
		box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
		border-radius: 4px;
		overflow: hidden;
		flex: 1;
		display: flex;
	}

	/deep/ .icon-xiangji:before {
		background: #fff;
	}
	/deep/ .ivu-btn-primary[disabled]:hover {
		color: #fff;
		background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
	}
</style>
