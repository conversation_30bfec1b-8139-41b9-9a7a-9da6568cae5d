<template>
  <div class="tags-more">
    <Tag v-for="(item, index) of defaultList" :color="item.tagColour" :key="index">{{
      item | filterType(personTypeList)
    }}</Tag>
    <span v-if="!defaultList.length && tagData">无</span>
    <Poptip trigger="hover" :placement="placement">
      <div slot="content" class="tag-wrapper">
        <Tag
          class="mr-sm mb-xs"
          v-for="(item, index) of tagList"
          :key="index"
          :color="item.tagColour ? item.tagColour : 'white'"
          >{{ item | filterType(personTypeList) }}</Tag
        >
      </div>
      <Tag class="pointTips pointer" :color="bgColor" v-if="tagList.length > defaultTags && !expandAll">...</Tag>
    </Poptip>
    <slot></slot>
  </div>
</template>

<script>
export default {
  props: {
    // 全部的标签
    tagList: {
      default: () => {
        return [];
      },
    },
    tagData: {
      default: true,
      type: Boolean,
    },
    // 默认显示两个
    defaultTags: {
      default: 2,
    },
    // 不需要隐藏,展示所有标签
    expandAll: {
      default: false,
      type: Boolean,
    },
    placement: {
      type: String,
      default: 'right-start',
    },
    bgColor: {
      type: String,
      default: '#2D435F',
    },
    personTypeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      transfer: true,
      originalLibData: '查询中..', // 来源
    };
  },
  computed: {
    defaultList() {
      if (this.expandAll) {
        return this.tagList;
      } else {
        return this.tagList.slice(0, this.defaultTags);
      }
    },
  },
  created() {},
  async mounted() {},
  watch: {},
  methods: {},
  components: {},
};
</script>

<style scoped lang="less">
.tag-wrapper {
  display: flex;
  flex-wrap: wrap;
  max-width: 180px;
}
@{_deep}.ivu-poptip-body {
  padding: 10px;
}
@{_deep} .ivu {
  &-poptip {
    position: relative;
    &-arrow {
      border-top-color: transparent !important;
    }
    &-arrow:after {
      border-top-color: transparent !important;
    }
    &-inner {
      border: 1px solid #0d4a81 !important;
    }
    &-body-content-inner {
      color: #ffffff !important;
      background-color: #0d3560 !important;
    }
    &-popper {
      &[x-placement^='right'] {
        // top: 60px !important;
        // left: 94% !important;
        .ivu-poptip-arrow {
          border-right-color: #0d4a81 !important;
          &:after {
            border-right-color: #0d3560 !important;
            left: 1px !important;
          }
        }
      }
      &[x-placement^='left'] {
        top: -7px !important;
        left: -210px !important;
        .ivu-poptip-arrow {
          border-left-color: #0d4a81 !important;
          &:after {
            border-left-color: #0d3560 !important;
          }
        }
      }
      &[x-placement^='top'] {
        .ivu-poptip-arrow {
          border-top-color: #0d4a81 !important;
          &:after {
            border-top-color: #0d3560 !important;
            bottom: 1px;
          }
        }
      }
    }
  }
}
.pointTips:hover {
  background: var(--color-primary) !important;
}
// @{_deep}.ivu-poptip-inner {
//     background: rgba(7, 25, 58, .9);
//     border: 1px solid rgba(26, 130, 190, 1);
// }
// @{_deep}.ivu-poptip-arrow {
//     &::after {
//         bottom: 1PX; /*no*/
//         border-right-color: rgba(7, 25, 58, .9)!important;
//     }
// }
</style>
