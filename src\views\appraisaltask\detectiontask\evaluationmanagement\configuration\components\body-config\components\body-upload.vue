<template>
  <div class="face-body-upload">
    <common-form
      :label-width="labelWidth"
      class="common-form"
      ref="commonForm"
      :moduleAction="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="extract">
        <template v-if="formData.detectMode !== '4'">
          <FormItem label="每设备抽取图片" prop="captureNum">
            <InputNumber
              v-model="formData.captureNum"
              class="input-width"
              placeholder="请输入抽取图片"
              clearable
            ></InputNumber>
          </FormItem>
          <FormItem
            prop="deviceQueryForm.dayByCapture"
            label="图片抽取范围"
            :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
          >
            <span class="base-text-color mr-xs">近</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.dayByCapture"
              :min="0"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color">天，</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.startByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点至</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.endByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点</span>
          </FormItem>
        </template>
      </div>
      <div slot="waycondiction" class="mt-xs" v-if="['1', '3'].includes(formData.detectMode)">
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
              >设备可用</Checkbox
            >
          </div>
          <div>
            <FormItem
              prop="deviceQueryForm.dayByFilterOnline"
              label=""
              :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
            >
              <Checkbox class="mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
              <span class="base-text-color">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
              <div class="capture-vehicle">
                <span class="base-text-color mr-sm">抓拍人脸不少于</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.countByFilterOnline"
                  :min="0"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color">张</span>
                <p class="color-failed">说明：系统只检测满足条件的设备。</p>
              </div>
            </FormItem>
          </div>
        </div>
      </div>
    </common-form>

    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="labelWidth">
      <FormItem label="图片上传时延" class="right-item mt-md">
        <FormItem
          class="inline one-time"
          prop="beforeTimeDelay"
          :rules="[{ validator: validateTimeDelayBefore, trigger: 'change', required: false }]"
        >
          <InputNumber
            v-model.number="formData.beforeTimeDelay"
            :formatter="(value) => `${parseInt(value)}`"
            class="width-mini"
          ></InputNumber>
        </FormItem>
        <FormItem class="inline" prop="beforeTimeFormat">
          <Select v-model="formData.beforeTimeFormat" transfer class="width-mini ml-sm">
            <Option value="s">秒</Option>
            <Option value="m">分</Option>
            <Option value="h">时</Option>
          </Select>
        </FormItem>
        <span class="base-text-color"> <= 接收时间 - 抓拍时间 <= </span>
        <FormItem
          class="inline one-time"
          prop="timeDelay"
          :rules="[{ validator: validateTimeDelay, trigger: 'change', required: true }]"
        >
          <InputNumber
            v-model.number="formData.timeDelay"
            :formatter="(value) => `${parseInt(value)}`"
            class="width-mini mr-sm"
          ></InputNumber>
        </FormItem>
        <FormItem class="inline" prop="timeFormat">
          <Select v-model="formData.timeFormat" transfer class="width-mini">
            <Option value="s">秒</Option>
            <Option value="m">分</Option>
            <Option value="h">时</Option>
          </Select>
        </FormItem>
      </FormItem>
      <p class="color-failed p-marginLeft" :style="{ '--labelWidth': labelWidth }">说明：不配置表示允许时间倒挂</p>
      <FormItem label="更新人体上传及时性状态">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    indexType: {
      type: String,
      default: '',
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      labelWidth: 155,
      formData: {
        captureNum: null,
        ruleList: [],
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: false,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
        isUpdatePhyStatus: 0,
      },
      ruleCustom: {
        timeDelay: [
          {
            required: true,
            type: 'number',
            message: '请输入时间',
            trigger: 'blur',
          },
        ],
        timeFormat: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'blur',
          },
        ],
      },
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      validateTimeDelay: (rule, value, callback) => {
        let { timeDelay } = this.formData;
        if (!timeDelay && timeDelay !== 0) {
          callback(new Error('请输入时间'));
        } else if (timeDelay <= 0) {
          callback(new Error('只允许填写正数'));
        }
        callback();
      },
      validateTimeDelayBefore: (rule, value, callback) => {
        let { beforeTimeDelay } = this.formData;
        if (beforeTimeDelay > 0) {
          callback(new Error('不允许填写正数'));
        }
        callback();
      },
    };
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            timeDelay: null,
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
        }

        if (!this.formData.timeDelay && !this.formData.timeFormat) {
          this.formData.timeDelay = 60;
          this.formData.timeFormat = 'm';
        }
        if (!this.formData.beforeTimeDelay && !this.formData.beforeTimeFormat) {
          this.formData.beforeTimeDelay = null;
          this.formData.beforeTimeFormat = 's';
        }

        this.formData.deviceQueryForm.dayByFilterOnline = this.formData.deviceQueryForm.dayByFilterOnline || null;
        this.formData.deviceQueryForm.countByFilterOnline = this.formData.deviceQueryForm.countByFilterOnline || null;
      },
      immediate: true,
    },
  },
  methods: {
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      let modalDataValid = await this.$refs.modalData.validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      return commonFormValid && modalDataValid;
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .lastHours-input .ivu-input-number {
    @{_deep}.ivu-input-number-handler-wrap {
      border-left: 1px solid #10457e;
      border-bottom: 1px solid #10457e;
      a {
        background: #02162b;
        color: var(--color-primary);
        .ivu-input-number-handler-down-inner,
        .ivu-input-number-handler-up-inner {
          color: var(--color-primary);
        }
      }
      .ivu-input-number-handler-down,
      .ivu-input-number-handler-up {
        border-color: #10457e;
      }
    }
  }
}
.face-body-upload {
  .input-width {
    width: 380px;
  }
  .label-color {
    color: #e44f22;
  }
  .color-white {
    color: #fff;
  }
  .color-bule {
    color: #1b82d2 !important;
  }
  .one-time /deep/ .ivu-form-item-error-tip {
    width: 155px;
  }
  .lastHours-input .ivu-input-number {
    width: 100px;
    @{_deep}.ivu-input-number-handler-wrap {
      display: inline-block;
    }
  }
  @{_deep}.drop-tree {
    width: 100% !important;
  }
  .p-marginLeft {
    margin-left: calc(var(--labelWidth) * 1px);
  }
}
</style>
