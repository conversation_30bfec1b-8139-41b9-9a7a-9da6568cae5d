<template>
  <ui-modal v-model="visible" :title="modelTitle" :styles="styles" footer-hide>
    <!-- 查询条件 -->
    <dynamic-condition :formItemData="filterList" :formData="searchData" @search="search" @reset="reset">
    </dynamic-condition>
    <!-- 表格 -->
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #succNum="{ row }">
        <span class="span-click font-blue" @click="openDeviceDetails(row, 'success')"> {{ row.succNum }} </span>
      </template>
      <template #failNum="{ row }">
        <span class="span-click font-warning" @click="openDeviceDetails(row, 'fail')"> {{ row.failNum }} </span>
      </template>
      <template #rate="{ row }">
        <span> {{ row.rate }}% </span>
      </template>
      <template #onlineStatus="{ row }">
        <Tag v-if="row.onlineStatus" :color="getOnlineStatusText(row).color">
          {{ getOnlineStatusText(row).label }}
        </Tag>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { datailsSearchData, datailsTableColumns, detectionResultArr } from '../util/enum/ReviewParticular';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'detectionDetails',
  props: {
    value: {},
    rowData: {
      type: Object,
      default: () => {},
    },
    indexInfo: {
      type: Object,
    },
  },
  mixins: [dealWatch],
  components: {
    UiTable: () => import('@/components/ui-table.vue'),
    DynamicCondition: () => import('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue'),
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.45rem',
      },
      modelTitle: '', // xxx联网平台检测详情
      tableColumns: [],
      tableData: [],
      searchData: {
        beginTime: '',
        endTime: '',
        onlineStatus: '',
      },
      filterList: [],
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  computed: {
    getOnlineStatusText() {
      return (row) => {
        let arr = detectionResultArr(row.onlineStatus);
        let arr1 = arr.filter((item) => row.onlineStatus == item.value);
        return arr1[0] ? arr1[0] : '';
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (!val) return false;
      this.visible = val;
      let { orgName, civilName } = this.rowData;
      const { statisticType } = this.indexInfo ? this.indexInfo : this.$route.query;
      this.modelTitle = `${
        this.$route.query.statisticsCode && this.indexInfo
          ? this.rowData.name
          : statisticType === 'REGION'
          ? civilName
          : orgName
      }联网平台检测详情`;
      // 默认 当月第一天 -- 当前时间
      this.setDefaultSearchData();
      // 根据 此次统计 配置的 【设备离线】 显示不同的表头    number --  0:离线状态  1：拉流状态
      this.tableColumns = datailsTableColumns(this.rowData);
      this.filterList = datailsSearchData(this.rowData);
      this.getTableList();
    },
  },
  methods: {
    search(data) {
      this.searchData = {
        beginTime: data.beginTime,
        endTime: data.endTime,
        onlineStatus: data.onlineStatus,
      };
      this.getTableList();
    },
    reset() {
      this.setDefaultSearchData();
      this.getTableList();
    },
    setDefaultSearchData() {
      this.searchData.beginTime = `${this.$util.common.formatDate(new Date(), 'yyyy-MM')}-01 00:00:00`;
      this.searchData.endTime = this.$util.common.formatDate(new Date());
      this.searchData.onlineStatus = '';
    },
    async getTableList() {
      if (!this.searchData.beginTime || !this.searchData.endTime) {
        this.$Message.warning('抓拍时间都不能为空！');
        return;
      }
      try {
        this.loading = true;
        const { statisticType, indexId, access, batchId } = this.indexInfo ? this.indexInfo : this.$route.query;
        const { civilCode, orgCode } = this.rowData;
        let { pageNum, pageSize } = this.pageData;
        let endTime = this.searchData.endTime;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? civilCode : orgCode,
          customParameters: {
            ...this.searchData,
            endTime: typeof endTime == 'string' ? endTime : this.$util.common.formatDate(endTime), //临时这样写。原方法有问题存在一步隐式转换：重置后结束时间被转换成Date对象
          },
          pageNumber: pageNum,
          pageSize: pageSize,
        };
        let res = await this.$http.post(evaluationoverview.getPolyData, data);
        this.tableData = res.data.data?.entities || [];
        this.pageData.totalCount = res.data.data?.total || 0;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    openDeviceDetails(row, str = '') {
      this.$emit('openDeviceDetails', { ...row, onlineStatusByFilterValue: str === 'success' ? '1' : '2' });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
  },
};
</script>

<style lang="less" scoped>
.ui-table {
  min-height: 530px;
}
.span-click {
  cursor: pointer;
  text-decoration: underline;
}
</style>
