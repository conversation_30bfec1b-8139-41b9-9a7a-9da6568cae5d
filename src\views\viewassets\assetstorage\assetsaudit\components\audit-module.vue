<template>
  <ui-modal v-model="visible" :title="modalTitle" :styles="styles">
    <div class="audit-module">
      <div class="audit-module-header mb-md">
        <RadioGroup v-model="sign">
          <Radio label="differ">标记差异字段</Radio>
          <Radio class="ml-lg" label="unqualified">标记不合格字段</Radio>
        </RadioGroup>
        <Checkbox v-model="onlyDiff" @on-change="changeOnlyDiff"> 只显示差异字段 </Checkbox>
      </div>
      <div class="audit-module-table">
        <ui-table
          ref="auditTable"
          class="auditTable"
          :stripe="false"
          :disabledHover="true"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <template #fieldName="{ row }">
            <span>{{ row.fieldDesc }}({{ row.fieldName }})</span>
          </template>
          <template #fieldValueForDevice="{ row }">
            <span :class="[sign === 'differ' ? isAbnormal(row) : '']">{{
              row.fieldValueForDeviceText || row.fieldValueForDevice
            }}</span>
          </template>
          <template #fieldValueForFillIn="{ row }">
            <span :class="[isAbnormal(row)]">{{ row.fieldValueForFillInText || row.fieldValueForFillIn }}</span>
          </template>
        </ui-table>
      </div>
      <div class="custom-content auto-fill">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="80">
          <FormItem label="审核结果" prop="gender">
            <RadioGroup v-model="formValidate.examineStatus">
              <Radio label="1">审核通过</Radio>
              <Radio label="2">审核不通过</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="审核说明" prop="remark">
            <Input
              v-model="formValidate.remark"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 3 }"
              placeholder="请输入审核说明"
            ></Input>
          </FormItem>
        </Form>
      </div>
    </div>
    <template #footer>
      <Button class="ml-sm plr-30" @click="handleCancel">取 消</Button>
      <Button class="plr-30" type="primary" :loading="btnLoading" @click="handleSubmit">确 定</Button>
    </template>
  </ui-modal>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
import assetsexamine from '@/config/api/assetsexamine';
export default {
  name: 'audit-module',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    differData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      visible: false,
      modalTitle: '资产审核',
      styles: {
        width: '8rem',
      },
      sign: 'differ',
      onlyDiff: false,
      tableColumns: [
        { title: '字段名称', slot: 'fieldName', align: 'left', tooltip: true },
        {
          title: '资产库设备',
          slot: 'fieldValueForDevice',
          align: 'left',
          tooltip: true,
          renderHeader: (h) => {
            return h('span', [
              h('i', {
                class: ['icon-font', 'icon-zichankushebei', 'mr-xs'],
              }),
              h('span', '资产库设备'),
            ]);
          },
        },
        {
          title: '待审核设备',
          slot: 'fieldValueForFillIn',
          align: 'left',
          tooltip: true,
          renderHeader: (h) => {
            return h('span', [
              h('i', {
                class: ['icon-font', 'icon-tianbaoshebei', 'mr-xs'],
              }),
              h('span', '待审核设备'),
            ]);
          },
        },
        {
          title: '处理模式',
          key: 'addType',
          align: 'left',
          tooltip: true,
          renderHeader: (h) => {
            return h('span', [
              h('i', {
                class: ['icon-font', 'icon-tianbaoshebei', 'mr-xs'],
              }),
              h('span', '处理模式'),
            ]);
          },
          render: (h, params) => {
            const row = params.row;
            const index = params.index;
            let stragety = [
              { dataKey: 1, dataValue: '覆盖' },
              { dataKey: 2, dataValue: '追加' },
              { dataKey: 3, dataValue: '舍弃' },
            ];
            return h(
              'i-select',
              {
                props: {
                  value: row.addType,
                  disabled: row.disabled,
                  placeholder: '请选择',
                  transfer: true,
                },
                on: {
                  'on-change': (val) => {
                    row.addType = val;
                    this.tableData.splice(index, 1, row);
                  },
                },
              },
              stragety.map((row) => {
                return h('i-option', {
                  props: {
                    value: row.dataKey,
                    label: row.dataValue,
                  },
                });
              }),
            );
          },
        },
      ],
      tableData: [],
      formValidate: {
        examineStatus: '1',
        remark: '',
      },
      ruleValidate: {},
      differenceList: [],
      auditData: [],
      btnLoading: false,
      defaultSelectKey: {
        deviceId: {
          addType: 1, // 覆盖
          disabled: true, // 入库策略-禁用下拉框
        },
        sourceId: {
          addType: 2, // 追加
          disabled: false,
        },
        sbgnlx: {
          addType: 2, // 追加
          disabled: false,
        },
      },
    };
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        const res = await this.$http.get(equipmentassets.queryDifferDetail, {
          params: {
            id: this.differData.id,
          },
        });
        this.auditData = res.data.data;
        this.differenceList = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changeOnlyDiff(val) {
      if (!val) {
        this.differenceList = this.auditData;
      } else {
        this.differenceList = this.differenceList.filter((item) => item.isDiffer === '1');
      }
    },
    isAbnormal(item) {
      if (
        (this.sign === 'differ' && item.isDiffer === '1') ||
        (this.sign === 'unqualified' && item.isUnqualified === '1')
      ) {
        return 'abnormal';
      }
    },
    async handleSubmit() {
      try {
        this.btnLoading = true;
        const params = {
          fillInId: this.differData.id,
          remark: this.formValidate.remark,
          examineStatus: this.formValidate.examineStatus,
        };
        if (this.differData.deviceInfoId) {
          params.deviceInfoId = this.differData.deviceInfoId;
        }
        params.storageParamList = this.tableData.map((item) => {
          const obj = {
            addType: item.addType,
            fieldName: item.fieldName,
            fieldRemark: item.fieldDesc,
          };
          return obj;
        });
        await this.$http.post(assetsexamine.doExamine, params);
        this.$Message.success('审核成功！');
        this.$emit('handleUpdateAudit');
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    handleCancel() {
      this.visible = false;
      this.differenceList = [];
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value: {
      async handler(val) {
        if (val && this.differData.id) {
          await this.getTableList();
        }
        this.visible = val;
        this.$refs.formValidate.resetFields();
      },
    },
    differenceList: {
      handler(val) {
        if (!val) return false;
        this.tableData = val.map((item) => {
          if (Object.keys(this.defaultSelectKey).includes(item.fieldName)) {
            item.disabled = this.defaultSelectKey[item.fieldName].disabled;
            item.addType = this.defaultSelectKey[item.fieldName].addType;
          } else {
            item.disabled = false;
            item.addType = 1;
          }
          return item;
        });
      },
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .auditTable {
    @{_deep} .ivu-table {
      .icon-zichankushebei,
      .icon-tianbaoshebei,
      .icon-tianbaoshebei {
        color: var(--color-primary);
      }
    }
  }
}
.audit-module {
  //height: 700px;
  &-header {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
  }
  &-table {
    .auditTable {
      max-height: 500px;
    }
    .empty-table {
      min-height: 200px;
    }
  }
  .custom-content {
    width: 100%;
    padding: 15px 20px 0;
    border-top: 1px solid var(--border-modal-footer);
  }
  .abnormal {
    color: var(--color-warning);
  }
}
@{_deep} .ivu-modal-body {
  padding: 0;
}
@{_deep} .ivu-modal-footer {
  border-top: none !important;
}
@{_deep} .ivu-table {
  th,
  td {
    padding: 0 20px;
  }
  th:not(th:last-child),
  td:not(td:last-child) {
    border-bottom: none !important;
    border-right: 1px solid var(--border-table) !important;
  }

  .icon-zichankushebei,
  .icon-tianbaoshebei,
  .icon-tianbaoshebei {
    color: var(--color-bluish-green-text);
  }
}
@{_deep} .ivu-table {
  &-tip {
    min-height: 200px !important;
  }
  &-body {
    overflow-y: auto;
    //min-height: 200px;
    max-height: 500px !important;
  }
}
</style>
