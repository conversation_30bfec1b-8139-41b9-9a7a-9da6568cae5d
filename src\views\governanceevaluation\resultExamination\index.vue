<template>
  <div class="page-assessmentresult auto-fill">
    <div class="right-content auto-fill" v-if="!componentName">
      <div class="search-wrapper">
        <ui-label class="fl" label="考核任务">
          <Select class="width-md" placeholder="请选择考核任务" v-model="searchData.examTaskId" @on-change="changeTask">
            <Option v-for="item in taskList" :key="item.id" :value="item.id">
              {{ item.taskName }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="ml-lg fl" label="考核时间">
          <template>
            <DatePicker
              type="month"
              format="yyyy-MM"
              placeholder="请选择考核时间"
              v-model="searchData.time"
            ></DatePicker>
          </template>
        </ui-label>
        <ui-label :width="0" label="" class="fl ml-lg">
          <Button type="primary" class="mr-sm" @click="searchBtn"> 查询 </Button>
          <Button @click="resetSearch">重置</Button>
        </ui-label>
        <ui-label :width="0" label=" " class="fr" v-if="overBtn === 2">
          <Button
            class="mr-sm download-scheme"
            type="text"
            @click="editExaminationScore"
            v-permission="{ route: $route.name, permission: 'postStatisticsSave' }"
          >
            编辑考核成绩
          </Button>
          <Button class="mr-sm download-scheme" type="text" @click="downLoadSchemeUrl">查看考核方案</Button>
          <Button type="primary" class="fr" @click="exportHandle" :loading="btnLoading">
            <i class="icon-font icon-daochu f-12 mr-sm vt-middle" title="添加"></i>
            <span class="vt-middle">导出</span>
          </Button>
        </ui-label>
      </div>
      <div class="jump" v-if="overBtn === 2">
        <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
      </div>
      <div class="title-center">
        <div class="is-show" @click="isOpen" v-if="overBtn === 2">
          <i :class="['icon-font', 'ml-xs', 'f-12', isShow ? 'icon-zhankai' : 'icon-xingzhuang']"></i>
          <span v-if="!isShow">展开考核内容</span>
          <span v-else>收缩考核内容</span>
        </div>
        <div class="conter-center">
          <span> {{ tableTitleYear }}-{{ tableTitleTaskName }} </span>
        </div>
        <div class="btns fr">
          <span class="f-14" :class="{ active: overBtn === 1 }" @click="overviewBtn(1)"> 统计模式 </span>
          <span class="f-14" :class="{ active: overBtn === 2 }" @click="overviewBtn(2)"> 报表模式 </span>
        </div>
      </div>
      <div v-if="overBtn === 1" class="statistica-overview">
        <statisticalGraph :echartList="echartList" :echartsLoading="echartsLoading" />
      </div>
      <template v-else>
        <div class="table-box auto-fill">
          <ui-table
            ref="assessmentTable"
            :class="['ui-table auto-fill', isShow ? 'expand' : '']"
            :table-columns="tableColumns"
            :table-data="tableData"
            :loading="loading"
            :row-class-name="rowClassName"
          >
            <template #ORG_REGEION_CODE="{ row, column }">
              <span v-if="orgCode === row.orgCode && systemConfig.distinguishVersion !== '4'" class="active-blue">
                {{ row.ORG_REGEION_CODE }}
              </span>
              <span
                v-else
                :class="row.clickable ? 'span-btn' : 'dis-span-btn'"
                @click="administrativeDivisionBtn(row, column)"
              >
                {{ row.ORG_REGEION_CODE }}
              </span>
            </template>
          </ui-table>
        </div>
      </template>
      <picture-attribute-integrity
        ref="pictureAttributeIntegrity"
        :taskObj="infoObj"
        @selectModuleClick="selectModuleHandle"
        @jump="handleClickJump"
      ></picture-attribute-integrity>
      <index-detail ref="indexDetail" @showHkDetail="showHkDetail"></index-detail>
      <hk-detail v-model="hkShow" :title="hkTitle" :hk-parmas="hkParmas"></hk-detail>
      <edit-examination-score @on-search="onSearch" ref="editExaminationScore"> </edit-examination-score>
    </div>
    <keep-alive v-else>
      <component :is="componentName" @changeComponent="changeComponentHandle"></component>
    </keep-alive>
  </div>
</template>
<script>
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
import MathJax from '@/components/math-jax';
import { mapGetters } from 'vuex';
export default {
  mixins: [dealWatch, evaluationoResultMixin],
  name: 'resultExamination',
  components: {
    EditExaminationScore:
      require('@/views/governanceevaluation/resultExamination/components/edit-examination-score.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    UiBreadcrumb: require('@/views/appraisaltask/inspectionrecord/components/ui-breadcrumb').default,
    pictureAttributeIntegrity: require('@/views/appraisaltask/assessmenTask/components/picture-attribute-integrity.vue')
      .default,
    indexDetail: require('./components/index-detail.vue').default,
    statisticalGraph: require('./components/statistical-graph.vue').default,
    HkDetail: require('./components/hk-detail.vue').default,
    detectionToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
    FaceCarOnlineNum: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-online-num.vue')
      .default,
    FaceCarTopOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-top-online.vue')
      .default,
    FaceCarReportRate:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-report-rate.vue').default,
    VideoHistoryOnline:
      require('@/views/governanceevaluation/evaluationoverview/components/video/video-history-online.vue').default,
    OnOffLineDetail: require('@/views/governanceevaluation/evaluationoverview/components/video/on-off-line-detail.vue')
      .default,
  },
  data() {
    return {
      loading: false,
      echartsLoading: false,
      hkShow: false,
      hkTitle: '',
      hkParmas: {},
      overBtn: 2,
      echartList: [],
      tableTitleYear: '',
      infoObj: '',
      taskList: [],
      firstOrgCode: '',
      taskInfo: {},
      tableTitleTaskName: '',
      orgCode: '',
      searchData: {
        examSchemeId: '',
        examTaskId: '',
        tableTitleTaskName: '',
        time: '',
        month: '',
        year: '',
        orgRegeionCode: '',
      },
      searchDataCopy: {},
      tableColumns: [
        {
          title: '行政区划',
          key: 'ORG_REGEION_CODE',
          slot: 'ORG_REGEION_CODE',
        },
      ],
      tableData: [],
      breadcrumbData: [],
      btnLoading: false,
      statisticsData: {},
      isShow: false, //列表是否展开
      schemeUrl: '',
      componentName: null,
      jumpIntoParams: {},
      // taskHistoryList: [],//历史任务列表
      // examTimeOptions: {
      //   disabledDate: (date) => {
      //     return this.examTimeDisabledArray.includes(this.$util.common.formatDate(date,'yyyy-MM'))
      //   }
      // },
      // examTimeDisabledArray: [],
    };
  },
  watch: {},
  created() {
    this.startWatch(
      '$route',
      async () => {
        if (this.$route.name !== 'resultExamination') {
          return;
        }
        this.getParams();
        // 如果taskList已经存在，跳转进入考核成绩的参数与上次跳转进入不相同则进行查询
        const params = this.$route.query;
        if (!this.taskList.length) {
          await this.init();
          if (!Object.keys(params).length) {
            this.changeTask(this.taskList[0].id, true);
          }
        }
        /**
         * 首页跳转、考核任务页面跳转
         * 如果跳转进入的参数没有发生改变则不去请求数据
         */
        if (Object.keys(params).length && JSON.stringify(this.jumpIntoParams) !== JSON.stringify(params)) {
          this.jumpIntoParams = params;
          if ('examTaskId' in this.jumpIntoParams) {
            this.changeTask(this.jumpIntoParams.examTaskId, true);
          }
        }
        this.searchDataCopy = JSON.parse(JSON.stringify(this.searchData));
        this.getStatisticalModel(this.searchDataCopy);
      },
      { immediate: true },
    );
  },
  methods: {
    async init() {
      try {
        const res = await this.$http.post(governanceevaluation.getAlltaskList, {});
        let { data } = res.data;
        this.taskList = data;
      } catch (err) {
        console.log(err);
      }
    },
    onSearch() {
      this.breadcrumbData = [];
      this.search(true);
    },
    getDate() {
      let date = new Date(this.searchData.time);
      let month = date.getMonth() + 1;
      let year = date.getFullYear();
      return {
        month,
        year,
      };
    },
    editExaminationScore() {
      let task = this.taskList.find((item) => {
        return item.id === this.searchData.examTaskId;
      });
      if (!task) return this.$Message.error('请选择考核任务');
      let { month, year } = this.getDate();
      let { id, schemeId, regionCode, taskName } = task;
      let params = {
        examSchemeId: schemeId,
        examTaskId: id,
        orgRegeionCode: regionCode,
        tableTitleTaskName: taskName,
        time: this.searchData.time,
        month,
        year,
      };
      this.$refs.editExaminationScore.init(params);
    },
    rowClassName(row) {
      if (!!this.$route.query.orgCode && row.orgCode === this.$route.query.orgCode) {
        return 'ivu-table-row-hover';
      }
    },
    changeComponentHandle() {
      this.componentName = 'detectionToOverview';
    },
    // 统计/报表切换
    overviewBtn(val) {
      this.overBtn = val;
      this.search(true);
    },
    // 统计模式
    async getStatisticalModel(val) {
      this.echartsLoading = true;
      try {
        let res = await this.$http.post(governanceevaluation.statisticalModel, val);
        this.echartList = res.data.data;
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
      }
    },
    //报表模式，之前同时访问统计和报表接口出现查询阻塞，故分离开
    async getExamStatistics(val, firstLoad) {
      try {
        this.loading = true;
        const res = await this.$http.post(governanceevaluation.getExamStatistics, val);
        this.statisticsData = this.$util.common.deepCopy(res.data.data);
        let { headers, body } = res.data.data;
        // 处理表头
        this.handleTableHeaders(headers);
        this.tableColumns = headers;
        for (let i of this.tableColumns) {
          i.show = true;
          if (i.name === '排名' || i.name === '总分') {
            i.sortable = true;
            i.sortMethod = (a, b, type) => {
              if (a && b) {
                if (type === 'asc') {
                  return a.score < b.score ? -1 : 1;
                } else {
                  return a.score > b.score ? -1 : 1;
                }
              }
            };
          }
          if (i.name === '行政区划' || i.name === '排名' || i.name === '总分') {
            i.fixed = 'left';
          }
        }
        // 处理表内容
        this.handleTableBody(body, firstLoad);
        this.changeCol();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleChange(val) {
      this.searchData.orgRegeionCode = val.id;
      this.search();
    },
    administrativeDivisionBtn(row, column) {
      if (!row.clickable) {
        return this.$Message.error('您没有此权限');
      }
      // 总分取总分一列
      column.code = 'TOTAL_SCORE';
      //四川省省厅直接查看该组织机构详情，不再查看下级
      if (this.systemConfig.distinguishVersion === '4') {
        if (row.TOTAL_SCORE) {
          this.$refs.indexDetail.init(
            {
              row,
              column,
            },
            this.searchData,
          );
        } else {
          this.$Message.warning('此行政区划没有考核成绩！');
        }
      } else {
        this.breadcrumbData.push({
          id: row.orgCode,
          add: row.ORG_REGEION_CODE,
        });
        this.searchData.orgRegeionCode = row.orgCode;
        this.search();
      }
    },
    pictureAttributeIntegrityBtn(row) {
      this.infoObj = {
        ORG_REGEION_CODE: row.row.ORG_REGEION_CODE,
        title: row.column.title,
        parentTitle: row.column.parentTitle,
        score: row.column.score,
        examContentItemMonthResultId: row.row[row.column.key].examContentItemMonthResultId,
      };
      this.$refs.pictureAttributeIntegrity.init();
    },
    async changeTask(id, firstLoad) {
      try {
        if (id) {
          var item = this.taskList.find((v) => {
            return v.id == id;
          });
          this.taskInfo = item;
          this.searchData.tableTitleTaskName = item.taskName;
          if (item.taskRunState !== '0') {
            this.searchData.examTaskId = item.id;
            this.searchData.examSchemeId = item.schemeId;
            this.firstOrgCode = item.regionCode;
            this.orgCode = item.regionCode;
            this.searchData.orgRegeionCode = item.regionCode;
            this.getSelectSchemeUrl();
            // await this.getTaskHistoryList();
            let time = null;
            if (firstLoad) {
              // 如果是跳转进入此页且带入时间则使用带入的时间
              time = this.jumpIntoParams.time ? this.jumpIntoParams.time : item.taskStartTime;
              //月考核-显示年月
              this.searchData.time = this.$util.common.formatDate(time, 'yyyy-MM');
              this.searchData.year = parseInt(this.$util.common.formatDate(time, 'yyyy'));
              this.searchData.month = parseInt(this.$util.common.formatDate(time, 'MM'));
              this.search(firstLoad);
            } else {
              time = item.taskStartTime;
              //月考核-显示年月
              this.searchData.time = this.$util.common.formatDate(time, 'yyyy-MM');
              this.searchData.year = parseInt(this.$util.common.formatDate(time, 'yyyy'));
              this.searchData.month = parseInt(this.$util.common.formatDate(time, 'MM'));
            }
          } else {
            this.$Message.error('考核任务还未开始');
            this.tableData = [];
            this.breadcrumbData = [];
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
    searchBtn() {
      this.breadcrumbData = [];
      if (this.taskInfo.taskRunState === '0') {
        this.$Message.error('考核任务还未开始');
        return;
      }
      this.searchData.orgRegeionCode = this.firstOrgCode;
      this.search(true);
    },
    async search(firstLoad) {
      this.isShow = false;
      this.tableTitleYear = this.$util.common.formatDate(this.searchData.time, 'yyyy年MM月');
      //月考核-显示年月
      this.searchData.time = this.$util.common.formatDate(this.searchData.time, 'yyyy-MM');
      this.tableTitleTaskName = this.searchData.tableTitleTaskName;
      this.searchData.year = parseInt(this.$util.common.formatDate(this.searchData.time, 'yyyy'));
      this.searchData.month = parseInt(this.$util.common.formatDate(this.searchData.time, 'MM'));
      this.overBtn === 1
        ? await this.getStatisticalModel(this.searchData, firstLoad)
        : await this.getExamStatistics(this.searchData, firstLoad);
    },
    isOpen() {
      this.isShow = !this.isShow;
      this.changeCol();
    },
    changeCol() {
      if (this.isShow) {
        //展开
        this.tableColumns.forEach((item) => {
          item.show = true;
          if (item.children_str) {
            item.children = item.children_str;
            delete item.children_str;
          }
          if (item.score || item.score == 0) {
            item.code = item.code.replace('_X', '');
          }
        });
      } else {
        this.tableColumns.forEach((item) => {
          item.show = false;
          if (item.score || item.score == 0) {
            item.code = item.code + '_X';
          }
          if (item.children) {
            item.children_str = item.children;
            delete item.children;
          }
        });
      }
      this.handleTableHeaders(this.tableColumns);
    },
    handleTableHeaders(arr, title) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.className = 'header-table';
        v.align = 'center';
        v.parentTitle = title;
        if (v.score || v.score == 0) {
          v.renderHeader = this.renderTableHeader;
        } else {
          delete v.score;
        }
        if (v.code === 'ORG_REGEION_CODE') {
          v.slot = 'ORG_REGEION_CODE';
          v.minWidth = 160;
        } else if (v.code === 'TOTAL_SCORE') {
          v.minWidth = 100;
        } else {
          v.minWidth = v.title.length * 16 + 80;
        }
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'ORG_REGEION_CODE') v.render = this.renderTableTd;
          delete v.children;
        }
      });
    },
    handleTableBody(arr, firstLoad) {
      let item = arr.length && arr[0].length ? arr[0][0] : '';
      this.orgCode = item.orgRegeionCode;
      //首次加载
      if (firstLoad) {
        this.breadcrumbData = [
          {
            id: this.orgCode,
            add: item.orgRegeionName,
          },
        ];
      }
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        v.forEach((k) => {
          if (k.code === 'ORG_REGEION_CODE') {
            tableData[i].ORG_REGEION_CODE = k.orgRegeionName;
            tableData[i].orgCode = k.orgRegeionCode;
            tableData[i].clickable = k.clickable;
          } else if (k.code === 'RANKING') {
            tableData[i][k.code] = {
              score: k.rank,
            };
          } else {
            tableData[i][k.code] = {
              score: k.score,
              // 新增报备描述
              scoreDesc: k.scoreDesc,
              examContentItemMonthResultId: k.examContentItemMonthResultId,
            };
          }
        });
      });
      this.tableData = tableData;
    },
    resetSearch() {
      this.breadcrumbData = [];
      this.changeTask(this.searchDataCopy.examTaskId, true);
    },
    // 导出
    async exportHandle() {
      try {
        this.btnLoading = true;
        let params = this.statisticsData;
        if (this.isShow) {
          //展开
          let data = await this.$http.post(governanceevaluation.exportAssessment, params, {
            responseType: 'blob',
          });
          await this.$util.common.exportfile(
            data,
            `${this.tableTitleYear}-${
              this.breadcrumbData.length ? this.breadcrumbData[this.breadcrumbData.length - 1].add : ''
            }${this.tableTitleTaskName} -【IVDG】-【${this.$util.common.formatDate(new Date())}】`,
            'application/vnd.ms-excel;charset=UTF-8',
          );
        } else {
          //收缩
          let data = await this.$http.post(governanceevaluation.exportExcel, params, {
            responseType: 'blob',
          });
          await this.$util.common.exportfile(
            data,
            `${this.tableTitleYear}-${
              this.breadcrumbData.length ? this.breadcrumbData[this.breadcrumbData.length - 1].add : ''
            }${this.tableTitleTaskName} -【IVDG】-【${this.$util.common.formatDate(new Date())}】`,
            'application/vnd.ms-excel;charset=UTF-8',
          );
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    selectModuleHandle() {
      this.selectModule(this.$route.query.componentName);
    },
    handleClickJump({ evaluationBatchId, indexId, indexType, orgRegeionCode }) {
      // 考核成绩只有 行政区划
      let { evaluationTaskSchemeId } = this.taskList.find((item) => item.id === this.searchData.examTaskId);
      this.jump({
        orgCode: '',
        regionCode: orgRegeionCode,
        statisticType: 'REGION',
        taskSchemeId: evaluationTaskSchemeId,
        indexId: `${indexId}`,
        indexType: indexType,
        batchId: evaluationBatchId,
      });
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr[nameArr.length - 1] === 'detectionToOverview' ? 0 : 1;
      }
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    async getSelectSchemeUrl() {
      try {
        const res = await this.$http.get(governanceevaluation.selectSchemeUrl, {
          params: {
            schemeId: this.searchData.examSchemeId,
          },
        });
        this.schemeUrl = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // //获取任务历史记录，用于限制展示考核成绩
    // async getTaskHistoryList() {
    //   try {
    //     const {data} = await this.$http.get(governanceevaluation.queryTaskHistoryList, {
    //       params: {
    //         examTaskId: this.searchData.examTaskId,
    //       },
    //     });
    //     this.taskHistoryList = data.data || []

    //     if(data.data.length){
    //       let publishArr = data.data.filter(item=>!item.publishStatus)
    //       if(!publishArr.length){
    //         this.examTimeDisabledArray = []
    //         return
    //       }
    //       this.examTimeDisabledArray = publishArr.map(item => {
    //         let examTimeDate = this.$util.common.parseDate(item.examTime)
    //         return this.$util.common.formatDate(examTimeDate, 'yyyy-MM')
    //       })
    //     }else{
    //       this.examTimeDisabledArray =[]
    //     }
    //     console.log('getTaskHistoryList',data.data);
    //   } catch (err) {
    //     console.log(err);
    //   }
    // },
    downLoadSchemeUrl() {
      if (this.schemeUrl) {
        this.$util.common.transformBlob(this.schemeUrl);
      } else {
        this.$Message.error('还未上传考核方案文档');
      }
    },
    renderTooltip(h, params) {
      return h(
        'Tooltip',
        {
          props: {
            placement: 'right-start',
            // prop: {
            transfer: true,
            // },
          },
        },
        [
          h(
            'div',
            {
              slot: 'content',
            },
            [
              h(
                'div',
                {
                  class: ['tooltip-title'],
                },
                `${params.column.name}：`,
              ),
              h('div', {}, [
                h('span', {}, '分项分值：'),
                h('span', { class: ['active-blue'] }, `${params.column.score}分`),
              ]),
              h('div', {}, [
                h('span', {}, '实际分值：'),
                h(MathJax, {
                  class: ['active-blue'],
                  props: {
                    mathStr: params.column.examFormula || '',
                  },
                }),
              ]),
              h('div', {}, [h('span', {}, `考核方法：${params.column.examWay || ''}`)]),
            ],
          ),
          h('i', {
            class: ['icon-font', 'icon-wenhao', 'ml-xs', 'icon-warning'],
          }),
        ],
      );
    },
    renderTableHeader(h, params) {
      return h('div', [
        h('div', {}, [
          h('span', {}, `${params.column.title}  ( ${params.column.score}分 )`),
          params.column.examFormula && this.renderTooltip(h, params),
        ]),
        h(
          'i',
          {
            class: ['icon-font ml-xs f-12', params.column.show ? 'icon-xingzhuang' : 'icon-zhankai'],
            style: {
              cursor: 'pointer',
              display: params.column.parentTitle === undefined ? 'inline-block' : 'none',
            },
            on: {
              click: () => {
                this.tableColumns[params.index].show = !this.tableColumns[params.index].show;
                // 收缩
                if (!this.tableColumns[params.index].show) {
                  this.tableColumns[params.index].code = this.tableColumns[params.index].code + '_X';
                  this.tableColumns[params.index].children_str = this.tableColumns[params.index].children; //备份child
                  delete this.tableColumns[params.index].children;
                  this.handleTableHeaders(this.tableColumns);
                } else {
                  this.tableColumns[params.index].code = this.tableColumns[params.index].code.replace('_X', '');
                  this.tableColumns[params.index].children = this.tableColumns[params.index].children_str; //备份child
                  delete this.tableColumns[params.index].children_str;
                  this.handleTableHeaders(this.tableColumns);
                }
              },
            },
          },
          '',
        ),
      ]);
    },
    renderTableTd(h, params) {
      let clickable = params.row.clickable;
      // if (params.column.key === 'TOTAL_SCORE' || params.column.key === 'RANKING') {
      // 朱耀 现场反馈 超额推送数量  得分 不支持点击跳转
      if (
        params.column.key === 'TOTAL_SCORE' ||
        !params.row[params.column.key] ||
        params.column.key === 'RANKING' ||
        params.column.name.endsWith('超额推送数量') ||
        params.column.name === '得分'
      ) {
        return h(
          'span',
          {
            attrs: {
              class: params.index === 0 ? 'active-blue' : '',
            },
          },
          params.row[params.column.key] ? params.row[params.column.key].score : '--',
        );
        // 判断大类处理点击事件
      } else {
        let hasScoreDesc = !!params.row[params.column.key].scoreDesc;
        return h(
          'span',
          {
            style: {
              color: 'var(--color-primary)',
              cursor: clickable ? 'pointer' : 'auto',
              textDecoration: clickable ? 'underline' : 'none',
            },
            attrs: {
              class: params.index === 0 ? 'active-blue' : '',
            },
            on: {
              click: () => {
                if (!clickable) {
                  return this.$Message.error('您没有此权限');
                }
                if (params.column.children_str !== undefined) {
                  this.$refs.indexDetail.init(params, this.searchData);
                } else {
                  this.pictureAttributeIntegrityBtn(params);
                }
              },
            },
          },
          hasScoreDesc ? params.row[params.column.key].scoreDesc : params.row[params.column.key].score,
        );
      }
    },
    showHkDetail(params) {
      this.hkTitle = params.column.name;
      this.hkParmas = params;
      this.hkShow = true;
    },
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
};
</script>
<style lang="less" scoped>
.page-assessmentresult {
  height: 100%;
  @{_deep}.head-center {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  @{_deep} .ivu-date-picker-cells-cell-focused {
    background: #2d8cf0;
    color: #fff;
  }
  .right-content {
    float: right;
    width: 100%;
    height: 100%;
    background: @bg-blue-block;
    @{_deep}.search-wrapper {
      margin: 0 20px 20px;
      padding: 20px 0 !important;
      border-bottom: 1px solid var(--border-color);
      .download-scheme {
        span {
          text-decoration: underline;
        }
      }
      .el-date-editor {
        width: 212px;
      }
      .ivu-input,
      .el-input__inner {
        height: 34px;
        padding: 4px 32px 4px 8px;
        color: #fff;
        font-size: 14px;
        line-height: 32px;
        border-radius: 4px;
        background: #02162b;
      }
      .el-input__inner:hover {
        border: 1px solid var(--color-primary);
      }
      .el-icon-date {
        display: none;
      }
      .ui-label {
        line-height: 34px;
      }
      .ivu-select {
        height: 34px;
      }
      .el-input__prefix {
        left: unset;
        right: 0;
        width: 32px;
      }
      .el-input__icon,
      .ivu-input-suffix i {
        color: var(--color-primary);
        font-size: 16px;
      }
      .ivu-input-suffix,
      .ivu-input-suffix i {
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      // .el-input__suffix,
      // .el-input__suffix-inner,
      // .el-input__suffix-inner i {
      //   display: flex;
      //   align-items: center;
      //   justify-content: center;
      // }
      // .el-input__suffix-inner i {
      //   display: inline-flex;
      // }
      .el-input__icon {
        line-height: 34px;
      }
    }
    .jump {
      padding: 0 20px !important;
      margin-bottom: 10px;
    }
    .title-center {
      display: flex;
      align-items: center;
      justify-content: center;
      //position: relative;
      margin-bottom: 10px;
      .conter-center {
        flex: 1;
        height: 30px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
        display: inline-block;
        vertical-align: middle;
        span {
          line-height: 30px;
          height: 100%;
          color: #fff;
          display: inline-block;
        }
      }
      .btns {
        cursor: pointer;
        margin-right: 20px;
        span {
          padding: 4px 10px;
          color: #56789c;

          font-family: 'MicrosoftYaHei-Bold';
          background-color: #12294e;
          border-radius: 0;
          border: 1px solid #174f98;
        }
        .active {
          color: #fff;
          background-color: #2d8cf0;
        }
        span:hover {
          color: #fff;
          background-color: #2d8cf0;
        }
      }
      .active {
        color: #fff;
        background-color: #2d8cf0;
      }
    }
    .statistica-overview {
      height: 84%;
      margin: 0px 20px;
    }
    @{_deep}.active-blue {
      color: var(--color-bluish-green-text) !important;
      .molecular {
        &::after {
          border-color: var(--color-bluish-green-text);
        }
      }
    }
    .table-box {
      padding: 0 20px 20px 20px;
      .span-btn {
        cursor: pointer;
        color: var(--color-primary);
        text-decoration: underline;
      }
      .dis-span-btn {
        color: var(--color-primary);
        text-decoration: none;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      @{_deep} .ui-table {
        overflow-y: auto !important;
        td {
          border: 1px solid var(--border-table);
        }
        th .ivu-table-cell {
          color: #8797ac;
          .ivu-table-sort i {
            color: #8797ac;
            &.on {
              color: #2d8cf0;
            }
          }
          > div {
            display: flex;
          }
        }
      }
      @{_deep} .ivu-table-tbody {
        tr:first-child td {
          border-top: none !important;
        }
        td {
          padding: 10px 0 10px 0;
        }
      }
      @{_deep} .header-table {
        border: 1px solid var(--border-table);
        box-shadow: none;
        .icon-xingzhuang,
        .icon-zhankai {
          line-height: 24px;
        }
      }
      @{_deep} .ivu-table-header {
        thead tr th {
          box-shadow: none;
        }
      }
    }
    @{_deep}.tooltip-title {
      color: var(--color-primary);
      font-size: 14px;
      font-weight: bold;
    }
    @{_deep}.ivu-tooltip-inner {
      max-width: 640px !important;
    }
  }
  .is-show {
    width: 150px;
    margin-left: 20px;
    height: 30px;
    line-height: 30px;
    cursor: pointer;
    color: var(--color-primary);
    font-size: 14px;
    i {
      margin-right: 5px;
    }
  }
}
</style>
