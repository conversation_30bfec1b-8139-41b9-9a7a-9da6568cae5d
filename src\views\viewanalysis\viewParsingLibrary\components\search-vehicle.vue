<!--
    * @FileDescription: 车辆搜索条件
    * @Author: H
    * @Date: 2024/2/26
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-11-20 16:06:05
 -->
<template>
  <div class="search card-border-color">
    <Form
      :inline="true"
      :class="visible ? 'advanced-search-show' : ''"
      @submit.native.prevent
    >
      <div class="general-search">
        <div class="input-content">
          <div class="input-content">
            <div class="upload-input-list">
              <uiUploadImg
                :algorithmType="2"
                v-model="queryParam.urlList"
                @imgUrlChange="imgUrlChange"
                size="small"
              />
            </div>
            <div class="other-search">
              <div class="other-search-top card-border-color">
                <FormItem label="相似度:" class="slider-form-item">
                  <div class="slider-content">
                    <i
                      class="iconfont icon-jian add-subtract"
                      @click="addAndSubtract(0)"
                    ></i>
                    <Slider v-model="queryParam.similarity"></Slider>
                    <i
                      class="iconfont icon-jia add-subtract"
                      @click="addAndSubtract(1)"
                    ></i>
                    <span>{{ queryParam.similarity }}%</span>
                  </div>
                </FormItem>
                <FormItem label="任务类型:">
                  <Select
                    v-model="queryParam.selectType"
                    @on-change="handleSeleChange"
                    clearable
                  >
                    <Option :value="1" placeholder="请选择">实时结构化</Option>
                    <Option :value="2" placeholder="请选择">历史结构化</Option>
                    <Option :value="3" placeholder="请选择">文件结构化</Option>
                  </Select>
                </FormItem>
                <FormItem label="任务名称:" v-if="queryParam.selectType">
                  <div class="select-tag-button" @click="selectTask()">
                    选择任务（{{ queryParam.selectTaskList.length }}）
                  </div>
                </FormItem>
              </div>
              <div class="other-search-bottom">
                <div class="flex">
                  <FormItem
                    label="设备资源:"
                    v-if="
                      queryParam.selectType == 1 || queryParam.selectType == 2
                    "
                  >
                    <div class="select-tag-button" @click="selectDevice()">
                      选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                    </div>
                  </FormItem>
                  <FormItem label="文件选择:" v-if="queryParam.selectType == 3">
                    <div class="select-tag-button" @click="selectFile()">
                      选择文件/已选（{{ queryParam.selectFileList.length }}）
                    </div>
                  </FormItem>
                  <FormItem label="抓拍时段:">
                    <hl-timerange
                      ref="timerange"
                      @onceChange="handleTimeOnce"
                      @change="handleTimeChange"
                      :reflectValue="queryParam.timeSlot"
                      :reflectTime="{
                        startDate: queryParam.startDate,
                        endDate: queryParam.endDate,
                      }"
                    >
                    </hl-timerange>
                  </FormItem>
                  <FormItem label="车牌号码:">
                    <Input
                      v-model="queryParam.plateNo"
                      placeholder="请输入"
                    ></Input>
                  </FormItem>
                </div>
                <div class="btn-group">
                  <span
                    class="advanced-search-text primary"
                    @click="advancedSearchHandle($event)"
                  >
                    更多条件 <i class="iconfont icon-jiantou"></i>
                  </span>
                  <Button type="primary" @click="searchHandle">查询</Button>
                  <Button type="default" @click="resetHandle">重置</Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--更多搜索条件-->
      <div
        class="advanced-search"
        @click="($event) => $event.stopPropagation()"
      >
        <section class="search-container" v-show="visible">
          <div class="search-item-container" id="searchItemContainer">
            <div class="classify-content">
              <span class="classify-name">常用</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="数据来源:">
                    <dataSource :currentIndex="2" />
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="角度:" class="percent-60">
                    <selectTag
                      ref="pointOfView"
                      :list="pointViewList"
                      vModel="pointOfView"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">车牌</span>
              <div class="items">
                <div
                  class="advanced-search-item card-border-color"
                  id="vehicleCP"
                >
                  <FormItem label="车牌类型:">
                    <selectTag
                      ref="plateClass"
                      :moreBtn="true"
                      :list="plateClassList"
                      vModel="plateClass"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="车牌颜色:">
                    <ui-tag-select
                      ref="plateColor"
                      @input="
                        (e) => {
                          input(e, 'plateColor');
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in licensePlateColorList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey"
                      >
                        <div
                          :style="{
                            borderColor:
                              licensePlateColorArray[item.dataKey].borderColor,
                          }"
                          class="plain-tag"
                        >
                          <div
                            :style="licensePlateColorArray[item.dataKey].style"
                            :class="licensePlateColorArray[item.dataKey].class"
                          >
                            {{ item.dataValue }}
                          </div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="遮挡:">
                    <selectTag
                      ref="cover"
                      :list="plateOcclusionList"
                      vModel="cover"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">车身</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="车身类型:">
                    <selectTag
                      :isChild="true"
                      :carColor="true"
                      :list="vehicleClassTypeLists"
                      ref="bodyType"
                      vModel="bodyType"
                      @selectItem="selectItem"
                    />
                    <div class="carVal"></div>
                  </FormItem>
                </div>
                <div
                  class="advanced-search-item card-border-color"
                  id="vehicleCSYS"
                >
                  <FormItem label="车身颜色:">
                    <ui-tag-select
                      ref="bodyColor"
                      @input="
                        (e) => {
                          input(e, 'bodyColor');
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in plateColorIpbdList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey"
                      >
                        <div
                          v-if="vehicleBodyColorArray[item.dataKey]"
                          :style="{
                            borderColor:
                              vehicleBodyColorArray[item.dataKey].borderColor,
                          }"
                          class="plain-tag-color"
                        >
                          <div
                            :style="vehicleBodyColorArray[item.dataKey].style"
                          ></div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div
                  class="advanced-search-item card-border-color"
                  id="vehiclePP"
                >
                  <FormItem label="车辆品牌:">
                    <div class="select-tag-button" @click="selectBrandHandle">
                      选择车辆品牌/已选（{{ queryParam.plateBrands.length }}）
                    </div>
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color">
                  <FormItem label="特殊车辆:">
                    <selectTag
                      :list="specialVehicleList"
                      ref="specialPlate"
                      vModel="specialPlate"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
                <div
                  class="advanced-search-item card-border-color"
                  id="markerTop"
                >
                  <FormItem label="车顶物件:">
                    <selectTag
                      :list="roofItemsList"
                      ref="carRoof"
                      vModel="carRoof"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                  <FormItem label="年检标:" class="percent-60">
                    <selectTag
                      :list="annualInspectionNumList"
                      ref="inspectAnnually"
                      vModel="inspectAnnually"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
                <div class="advanced-search-item card-border-color" id="marker">
                  <FormItem label="标志物:">
                    <selectTag
                      :list="markerTypeList"
                      ref="markerType"
                      vModel="markerType"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">行为</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="副驾有人:">
                    <selectTag
                      :list="copilotList"
                      ref="ifCoDriverPeople"
                      vModel="ifCoDriverPeople"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                  <FormItem label="面部遮挡:" class="percent-60">
                    <selectTag
                      :list="facialOcclusionList"
                      ref="facialOcclusion"
                      vModel="facialOcclusion"
                      @selectItem="selectItem"
                    />
                  </FormItem>
                </div>
              </div>
            </div>
          </div>
          <div class="img-search" @click="clickTagVehicleHandle">
            <div class="vehicle-front">
              <span
                v-for="(item, index) in anchorFrontList"
                :data-id="item.dataId"
                :class="item.class"
                :key="index"
                >{{ item.name }}</span
              >
              <img
                src="@/assets/img/wisdom-cloud-search/vehicle-back.png"
                alt=""
              />
            </div>
            <div class="vehicle-back">
              <span
                v-for="(item, index) in anchorAfterList"
                :data-id="item.dataId"
                :class="item.class"
                :key="index"
                >{{ item.name }}</span
              >
              <img
                src="@/assets/img/wisdom-cloud-search/vehicle-front.png"
                alt=""
              />
            </div>
          </div>
        </section>
      </div>
      <!-- 选择品牌 -->
      <BrandModal ref="brandModal" @on-change="selectBrand" />
      <!-- 选择设备 -->
      <select-device
        ref="selectDevice"
        showOrganization
        :checkedLabels="checkedLabels"
        @selectData="selectData"
      />
      <select-file ref="selectFile" @selectData="selectFileData"></select-file>
      <select-task
        ref="selectTask"
        :type="queryParam.selectType"
        @selectData="selectTaskData"
      ></select-task>
    </Form>
  </div>
</template>
<script>
import { queryTaskSelect } from "@/api/viewAnalysis";
import {
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { mapGetters, mapMutations } from "vuex";
import uiUploadImg from "@/components/ui-upload-new-img/index";
import selectTag from "@/views/wisdom-cloud-search/search-center/components/select-tag.vue";
import dataSource from "@/views/wisdom-cloud-search/search-center/components/data-source.vue";
import BrandModal from "@/components/ui-brand-modal.vue";
import { licensePlateColorArray, vehicleBodyColorArray } from "@/libs/system";
import {
  anchorFrontList,
  anchorAfterList,
  vehicleClassTypeList,
} from "../../../wisdom-cloud-search/search-center/advanced-search/components/search-vehicle.js";
export default {
  props: {
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    uiUploadImg,
    selectTag,
    BrandModal,
    dataSource,
  },
  data() {
    return {
      curSearchItemId: "",
      checkedLabels: [], // 已选择的标签
      vehicleClassTypeLists: vehicleClassTypeList, // 车身类型
      anchorFrontList,
      anchorAfterList,
      licensePlateColorArray,
      vehicleBodyColorArray,
    };
  },
  created() {
    window.addEventListener("click", (e) => {
      this.visible = false;
    });
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      parsingLibrarySearchData: "common/getParsingLibrarySearchData", //查询数据
    }),
  },
  async mounted() {
    let { type, taskId, jobId, imgUrl, startDate, endDate } = this.$route.query;
    if (type) this.queryParam.selectType = Number(type);
    if (taskId) this.queryParam.selectTaskList = JSON.parse(taskId);
    if (type && jobId) {
      let res = await queryTaskSelect({ type, parentId: jobId });
      this.queryParam.selectTaskList = res.data || [];
    }
    // if (imgUrl) {
    //   let fileData = new FormData();
    //   fileData.append("algorithmType", 2);
    //   fileData.append("fileUrl", imgUrl);
    //   let result = await picturePick(fileData);
    //   if (result.data.length == 0) {
    //     this.$Message.error("未检索到车辆目标!");
    //     return;
    //   }
    //   let params = result.data[0];
    //   let urlObj = {
    //     fileUrl: "data:image/jpeg;base64," + params.imageBase,
    //     feature: params.feature,
    //     imageBase: params.imageBase,
    //   };
    //   this.urlImgList([urlObj, ""], 1);
    // }
    if (imgUrl) {
      let fileData = new FormData();
      fileData.append("algorithmType", 2);
      fileData.append("fileUrl", imgUrl);
      await picturePick(fileData).then(async (res) => {
        if (res.data && res.data.length > 0) {
          const response = await this.getBase64ByImageCoordinate(res.data[0]);
          let urlList = {
            fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
            feature: response.data.feature,
            imageBase: response.data.imageBase,
          };
          this.urlImgList([urlList, ""], 1);
        } else {
          this.$Message.warning("未提取到特征，暂无查询结果");
        }
      });
    }
    if (startDate && endDate) {
      this.queryParam.timeSlot = "自定义";
      this.queryParam.startDate = this.$dayjs(Number(startDate)).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.queryParam.endDate = this.$dayjs(Number(endDate)).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
    if (this.globalObj.searchForPicturesDefaultSimilarity)
      this.queryParam.similarity =
        this.globalObj.searchForPicturesDefaultSimilarity - 0;
    let {
      selectType,
      selectDeviceList,
      selectTaskList,
      selectFileList,
      timeSlot,
      searchSelect,
    } = this.parsingLibrarySearchData[4];
    if (searchSelect == 1) {
      this.queryParam.selectType = selectType;
      this.queryParam.selectDeviceList = selectDeviceList;
      this.queryParam.selectTaskList = selectTaskList;
      this.queryParam.selectFileList = selectFileList;
      this.queryParam.timeSlot = timeSlot;
      this.queryParam.startDate = this.parsingLibrarySearchData[4].startDate;
      this.queryParam.endDate = this.parsingLibrarySearchData[4].endDate;
    }
    this.$emit("search");
    this.setParsingLibrarySearchData({...this.queryParam, menuIndex:4});
  },
  methods: {
    ...mapMutations("common", ["setParsingLibrarySearchData"]),
    searchHandle() {
      this.$emit("search");
      this.setParsingLibrarySearchData({...this.queryParam, menuIndex:4});
    },
    resetHandle() {
      this.resetClear();
      this.$emit("reset");
      this.setParsingLibrarySearchData({...this.queryParam, menuIndex:4});
      this.visible = false;
    },
    resetClear() {
      // 清空组件选中状态
      this.$refs.timerange.clearChecked(false);
      this.$refs.pointOfView.clearChecked();
      this.$refs.plateClass.clearChecked();
      this.$refs.plateColor.clearChecked();
      this.$refs.cover.clearChecked();
      this.$refs.bodyType.clearChecked();
      // this.$refs.marker.clearChecked()
      this.$refs.bodyColor.clearChecked();
      this.$refs.specialPlate.clearChecked();
      this.$refs.carRoof.clearChecked();
      this.$refs.inspectAnnually.clearChecked();
      this.$refs.ifCoDriverPeople.clearChecked();
      this.$refs.facialOcclusion.clearChecked();
      this.$refs.brandModal.clearCheckAll();
      this.$refs.markerType.clearChecked();
      this.queryParam.similarity =
        this.globalObj.searchForPicturesDefaultSimilarity - 0;
      this.queryParam.urlList = [];
      this.queryParam.features = [];
      this.queryParam.imageBases = [];
      this.queryParam.selectDeviceList = [];
      this.queryParam.selectTaskList = [];
      this.queryParam.selectFileList = [];
      this.queryParam.plateNo = "";
      this.queryParam.pointOfView = "";
      this.queryParam.plateClass = "";
      this.queryParam.plateColor = "";
      this.queryParam.cover = "";
      this.queryParam.bodyType = "";
      this.queryParam.bodyColor = "";
      this.queryParam.plateBrands = [];
      this.queryParam.specialPlate = "";
      this.queryParam.carRoof = "";
      this.queryParam.inspectAnnually = "";
      this.queryParam.markerType = "";
      this.queryParam.ifCoDriverPeople = "";
      this.queryParam.facialOcclusion = "";
      this.queryParam.selectType = "";
      this.$forceUpdate();
    },
    urlImgList(list, index = 0) {
      if (index == 1) {
        this.queryParam.urlList = [...list];
        this.imgUrlChange([...list]);
      } else if (index == 2) {
        let arr = [...this.queryParam.urlList, ...list].filter((item) => {
          return item;
        });
        this.queryParam.urlList = [...arr, ""];
        this.imgUrlChange([...arr]);
      } else {
        this.queryParam.urlList.unshift(...list);
        this.imgUrlChange([...list]);
      }
    },
    imgUrlChange(list) {
      // 以图搜图字段
      let features = [];
      let imageBases = [];
      list.forEach((item) => {
        if (item) {
          features.push(item.feature);
          imageBases.push(item.imageBase);
        }
      });
      this.queryParam.features = features;
      this.queryParam.imageBases = imageBases;
    },
    /**
     * 选中tag赋值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.dataKey;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },
    /**
     * 车辆品牌已选择，返回数据
     */
    selectBrand(list) {
      this.queryParam.plateBrands = list;
    },
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.queryParam[key] = e;
    },
    // 时间
    handleTimeOnce(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    handleTimeChange(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    handleSeleChange() {
      this.queryParam.selectTaskList = [];
    },
    // 选择品牌
    selectBrandHandle() {
      this.$refs.brandModal.show();
    },
    /**
     * 选择任务
     */
    selectTask() {
      this.$refs.selectTask.show(this.queryParam.selectTaskList);
    },
    /**
     * 选择文件
     */
    selectFile() {
      this.$refs.selectFile.show(this.queryParam.selectFileList);
    },
    /**
     * 选择设备
     */
    selectDevice() {
      let keyWords = this.$route.query.keyWords
        ? JSON.parse(this.$route.query.keyWords)
        : "";
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList, keyWords);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.queryParam.selectDeviceList = list;
      this.$forceUpdate();
    },
    selectFileData(list) {
      this.queryParam.selectFileList = list;
    },
    selectTaskData(list) {
      this.queryParam.selectTaskList = list;
    },
    // 相似度加减
    addAndSubtract(index) {
      if (index == 0) {
        this.queryParam.similarity -= 1;
      } else {
        this.queryParam.similarity += 1;
      }
    },
    advancedSearchHandle($event) {
      $event.stopPropagation();
      if (this.visible) {
        this.visible = false;
      } else {
        this.visible = true;
      }
    },
    // 点击右边车指示高亮左侧搜索项  因这里数据无特别规律，so这里为原生js操作dom
    clickTagVehicleHandle(e) {
      const target = e.target;
      if (target.nodeName === "SPAN") {
        let PChildren = target.parentNode.parentNode.childNodes;
        PChildren.forEach((pChild) => {
          pChild.childNodes.forEach((child) => {
            child.classList.remove("cur");
          });
        });
        target.classList.add("cur");
        const dataId = target.getAttribute("data-id");
        const leftItem = document.getElementById(dataId);
        if (!leftItem) return false;
        const leftPChildren =
          leftItem.parentNode.parentNode.parentNode.childNodes;
        leftPChildren.forEach((p) => {
          p.childNodes.forEach((pp) => {
            if (pp.nodeName === "DIV") {
              pp.childNodes.forEach((ppp) => {
                ppp.classList.remove("cur");
              });
            }
          });
        });
        leftItem.classList.add("cur");
        document.getElementById("searchItemContainer").scrollTop =
          leftItem.offsetTop;
        this.curSearchItemId = dataId;
      }
    },
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
@import "../../../wisdom-cloud-search/search-center/advanced-search/components/search-vehicle";
</style>
