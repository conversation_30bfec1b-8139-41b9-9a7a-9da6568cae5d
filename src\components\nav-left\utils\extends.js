export default {
  props: {
    isCollapsed: {
      type: Boolean,
      default: false,
    },
    menuList: {
      type: Array,
      required: true,
    }, //菜单列表
    menuProp: {
      type: Object,
      default: () => {
        return {
          menuChildren: 'children',
          menuName: 'name',
          menuText: 'text',
          menuIcon: 'iconName',
        };
      },
    },
  },
  data() {
    return {
      openNames: [],
      activeRouter: null,
    };
  },
  created() {
    this.getPath();
  },
  methods: {
    selectMenu(name) {
      this.$emit('selectMenu', name);
    },
    getPath() {
      this.activeRouter = this.$route.name;
    },
    // 获取当前打开的菜单
    getOpenNames() {
      let openNameList = this.$route.path.split('/');
      // 去除首个空路由
      openNameList.splice(0, 1);
      this.openNames = openNameList;
    },
  },
  watch: {
    $route: 'getPath',
    isCollapsed: {
      handler(val) {
        if (val) {
          this.openNames = [];
        } else {
          this.getOpenNames();
        }
      },
      immediate: true,
    },
  },
};
