<template>
  <Modal v-model="visible" title="详情" width="1010" @onCancel="onCancel">
    <div ref="personnelThematic" class="personnel-thematic-database">
      <Form ref="personnelForm" inline class="personnel-form">
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">基本信息</div>
        </div>
        <div class="information-form essential-form">
          <FormItem class="img-formitem">
            <div class="essential-information-img">
              <div class="avatar-img card-border-color">
                <ui-image viewer type="place" :src="detailInfo.image" />
                <!-- <img v-viewer :src="detailInfo.image" alt /> -->
              </div>
            </div>
          </FormItem>
          <div class="information-body">
            <div class="info-item">
              <FormItem label="场所名称:">
                <div class="label-value input-200">
                  {{ detailInfo.name }}
                </div>
              </FormItem>
              <FormItem label="场所面积(m²):">
                <div class="label-value input-200">
                  {{ detailInfo.area }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem label="一级分类:">
                <div class="label-value input-200">
                  {{ detailInfo.firstLevelName }}
                </div>
              </FormItem>
              <FormItem label="二级分类:">
                <div class="label-value input-200">
                  {{ detailInfo.firstLevelName }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem label="所属区县:">
                <div class="label-value input-200">
                  {{ detailInfo.adname }}
                  <!--  -->
                </div>
              </FormItem>
              <FormItem label="所属街道:">
                <div class="label-value input-200">
                  {{ detailInfo.townname }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem label="场所地址:">
                <div class="label-value input-520">
                  {{ detailInfo.address }}
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem label="场所区域:">
                <div
                  class="label-value input-200 link-active-color"
                  @click="showArea"
                >
                  点击查看
                </div>
              </FormItem>
              <FormItem prop="bloodType" label="场所设备:">
                <div class="label-value input-200">
                  <div
                    class="label-value input-200 link-active-color"
                    @click="showDeviceList"
                  >
                    点击查看
                  </div>
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem label="场所中心点:">
                <div
                  class="label-value input-200 link-active-color"
                  @click="showChooseMap"
                >
                  {{ detailInfo.centerPoint }}
                </div>
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
    </div>
    <template #footer>
      <div class="footer-btn">
        <Button type="primary" @click="onCancel">关闭</Button>
      </div>
    </template>
    <UiMapDrawArea
      :title="'场所区域'"
      :place-fence="placeFence"
      ref="areaEditRef"
      :is-edit="false"
    ></UiMapDrawArea>
    <ChooseCenterMap
      ref="centerMapShowRef"
      :place-fence="placeFence"
      :is-edit="false"
      :defaultCenterPoint="detailInfo.centerPoint"
      :title="'场所中心点'"
    ></ChooseCenterMap>
    <AreaDeviceList
      ref="areaDeviceListRef"
      :deviceList="deviceList"
    ></AreaDeviceList>
  </Modal>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import UiMapDrawArea from "@/components/ui-map-draw-area";
import ChooseCenterMap from "./chooseCenterMap.vue";
import AreaDeviceList from "./area-device-list.vue";
import { placeFenceType } from "@/views/holographic-archives/place-archives/dashboard/components/common";

export default {
  components: {
    UiMapDrawArea,
    ChooseCenterMap,
    AreaDeviceList,
  },
  props: {
    // 场所信息
    detailInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
    };
  },
  computed: {
    // 场所围栏
    placeFence() {
      if (this.detailInfo.coord) {
        let placeFence = placeFenceType.find(
          (v) => v.code === this.detailInfo.firstLevel
        );
        if (!placeFence) {
          placeFence = {
            code: this.detailInfo.firstLevel,
            name: this.detailInfo.firstLevelName,
            color: "#2c86f8",
            layerType: "placeDefault",
          };
        }
        placeFence.data = [
          {
            properties: { ...this.detailInfo },
            geometry: JSON.parse(this.detailInfo.coord),
          },
        ];
        return placeFence;
      }
    },
    // 设备列表
    deviceList() {
      if (this.detailInfo.additionalDevices) {
        return this.detailInfo.additionalDevices.split(`,`);
      } else {
        return [];
      }
    },
  },
  methods: {
    showModal() {
      this.visible = true;
    },
    onCancel() {
      this.visible = false;
      this.$emit("close");
    },
    showArea() {
      this.$refs.areaEditRef.show();
    },
    showDeviceList() {
      this.$refs.areaDeviceListRef.show();
    },
    showChooseMap() {
      this.$refs.centerMapShowRef.init();
    },
  },
};
</script>
<style lang="less" scoped>
.input-200 {
  width: 200px;
}
.input-520 {
  width: 520px;
}
.link-active-color {
  color: #1a74e7;
  cursor: pointer;
}
/deep/ .ivu-modal-body {
  padding: 0 !important;
}
.personnel-thematic-database {
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  // height: 680px;
  .personnel-form {
    padding: 0 30px;
    box-sizing: border-box;
    .form-item-title {
      display: flex;
      align-items: center;
      padding-bottom: 4px;
      border-bottom: 1px solid #fff;
      .title-line {
        width: 3px;
        height: 16px;
        margin-right: 6px;
      }
      .title-text {
        font-size: 14px;
        line-height: 20px;
        font-weight: bold;
        font-family: "MicrosoftYaHei-Bold";
      }
    }
    .information-form {
      display: flex;
      justify-content: space-between;
      margin: 20px 0 30px 0;
      .essential-information-img {
        width: 240px;
        margin-right: 64px;
        display: flex;
        flex-direction: column;
        .avatar-img {
          width: 240px;
          height: 320px;
          border: 1px solid #fff;
          /deep/ img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: pointer;
          }
          .avatar-null-img {
            cursor: unset;
          }
        }
      }
      .information-body {
        flex: 1;
        .info-item {
          display: flex;
          justify-content: space-between;
          /deep/ .ivu-form-item {
            display: inline-flex;
            margin-right: 0;
            margin-bottom: 10px;
            .ivu-form-item-label {
              display: flex;
              align-items: center;
              justify-content: end;
              padding-right: 10px;
              white-space: nowrap;
              .label-text {
                text-align-last: justify;
                display: inline-block;
              }
            }
            .label-value {
              height: 34px;
              display: flex;
              align-items: center;
            }
            .ivu-form-item-label::before {
              margin: 0;
            }
            .ivu-radio-wrapper {
              margin-right: 30px;
            }
          }
        }
      }
    }
    .essential-form {
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 80px !important;
          .label-text {
            width: 58px;
          }
        }
      }
      .img-formitem {
        margin: 0 !important;
      }
    }
    .vehicle-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
        }
      }
      .info-item {
        justify-content: unset !important;
        .license-plate-number {
          margin-right: 36px !important;
        }
        .license-plate-color {
          margin-right: 46px !important;
        }
      }
    }
    .other-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
          .label-text {
            width: 58px;
          }
        }
      }
      .info-item {
        justify-content: unset !important;
        .rfid-number {
          margin-right: 36px !important;
        }
        .mac-number {
          margin-right: 46px !important;
        }
      }
    }
  }
}
.footer-btn {
  text-align: center;
}
/deep/ .ivu-modal-header {
  background: rgba(211, 215, 222, 0.29);
}
</style>
