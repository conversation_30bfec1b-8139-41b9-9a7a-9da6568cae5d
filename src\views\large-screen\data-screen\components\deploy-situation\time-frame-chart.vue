<!--
    * @FileDescription: 数据时段分布
    * @Author: H
    * @Date: 2024/04/30
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="chart">
        <div class="form-box">
            <div class="warpper">
                <p class="warpper-title">选择日期</p>
                <div class="warpper-content">
                    <DatePicker type="date" v-model="formData.date" size="small" placeholder="请选择" style="width: 110px;"></DatePicker>
                </div>
            </div>
            <div class="warpper">
                <p class="warpper-title">数据类型</p>
                <div class="warpper-content">
                    <Select v-model="formData.type" size="small" @on-change="handleChange">
                        <Option value="0">人脸</Option>
                        <Option value="1">车辆</Option>
                        <Option value="2">非机动车</Option>
                        <Option value="3">人体</Option>
                    </Select>
                </div>
            </div>
            
        </div>
        <div id="time-frame" class="time-frame" ref="timeFrame"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
    props: {
        
        picData: {
            type: Array,
            default: () => {
                let list = [
                    { value: '75', name: '有效' },
                    { value: '25', name: '无效' },
                ];
                return list
            }
        },
    },  
    data() {
        return {
            formData: {
                type: '',
                date: ''
            },
            myEchart: null,
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        handleChange() {
            
        },
        init() {
            let data = [10, 15, 20, 12, 10, 8, 10, 20, 30, 40, 45, 50, 47,35,30,36,47,47, 51, 48,52,50,47,30,20];
            this.myEchart = echarts.init(this.$refs.timeFrame)
            let option = {
                legend: {
                    bottom: '1%',
                    left: 'center',
                    icon: 'circle',
                    itemWidth: 10,
                    itemHeight: 10,
                    itemStyle: {
                        borderRadius: 5,
                    },
                    textStyle: {
                        color: '#567BBB'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(2, 27, 71, 0.8)',
                    borderColor: '#098EFF',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    axisLabel:{
                        interval: 0,
                        lineStyle: {
                            color: '#A8D0FF'
                        },
                        textStyle: {
                            color: '#B6CBD5'
                        }
                    },
                    
                    axisTick: {
                        show: false
                    },
                    data: [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24],
                },
                yAxis: {
                    type: 'value',
                    name: '(万)',
                    nameLocation: 'start',
                    nameTextStyle: {
                        color: '#B6CBD5',
                        padding: [0, 25, 0, 0]
                    },
                    nameGap: 15,
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(168, 208, 255, 0.2)'
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#B6CBD5'
                        },
                    }
                },
                grid: {
                    top: '15px',
                    left: '30px',
                    right: '20px',
                    bottom: '25px'
                },
                series: [
                    {
                        type: 'bar',
                        data: data,
                        itemStyle: {
                            color: new echarts.graphic.LinearGradient(0,0,0,1, [
                                { offset:0, color: '#00F7FF' },  
                                { offset:1, color: 'rgba(0, 247, 255, 0)' },  
                            ])
                        },
                        barWidth: '5px'
                    }
                ]
            };
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .form-box{
        font-size: 14px;
        display: flex;
        justify-content: end;
        .warpper{
            display: flex;
            align-items: center;
            margin-left: 23px;
            .warpper-title{
                color: #27B5FF;
            
            }
            .warpper-content{
                width: 95px;
                margin-left: 10px;
                /deep/.ivu-input{
                    height: 22px;
                    border: 1px solid #098EFF;
                    background: rgba(9,142,255, 0.1);
                    color: #567BBB;
                }
            }
        }
    }
    .time-frame{
        height: calc( ~'100% - 34px') ;
        width: 100%; 
    }
    /deep/ .ivu-select{
        &:hover{
            color: #567BBB;
        }
        .ivu-select-selection{
            border: 1px solid #098EFF;
            background: rgba(9,142,255, 0.1);
            color: #567BBB;
        }
        .ivu-select-selected-value{
            color: #567BBB;
        }
        .ivu-select-arrow{
            color: #567BBB;
        }
        .ivu-select-dropdown{
            background: rgba(9,142,255, 0.3);
        }
        .ivu-select-item, .ivu-select-placeholder{
            color: #567BBB;
        }
        .ivu-select-item-selected:hover, 
        .ivu-select-item:hover, 
        .ivu-select-placeholder:hover, 
        .ivu-select-selection:hover{
            color: #fff;
        }
        .ivu-select-item-selected{
            color: #fff;
        }
    }
}
</style>