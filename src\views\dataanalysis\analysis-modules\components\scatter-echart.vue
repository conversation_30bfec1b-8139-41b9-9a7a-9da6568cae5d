<template>
  <div class="scatter-echart-box">
    <div class="influence-title" :class="{ 'influence-title-fullscreen': isFullscreen }">
      <span class="title-rect"></span>
      <span class="ml-sm mr-sm title-span f-16">
        <slot name="title" :influenceItemName="activeInfluenceItem.name">
          设备抓拍量与{{ activeInfluenceItem.name }}分布
        </slot>
      </span>
      <span class="tip-text"></span>
      <full-screen class="full-screen-box"></full-screen>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartLoading, tableData: echartData }">
      <draw-echarts
        v-if="echartData.length"
        :echart-option="propertyEchart"
        :echarts-loading="echartLoading"
        class="charts"
        ref="echartRef"
      ></draw-echarts>
      <!-- x轴：类目轴时 -->
      <!-- <span class="next-echart" v-if="isCategory && xAxisList.length > echartNum">
        <i class="icon-font icon-zuojiantou1 f-12" @click="changeShowData"></i>
      </span> -->
    </div>
  </div>
</template>
<script>
import Vue from 'vue';
import { mapGetters } from 'vuex';
import dataAnalysis from '@/config/api/dataAnalysis.js';
import ScatterTooltip from './scatter-tooltip.vue';
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'scatter-echart',
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    FullScreen: require('@/views/home/<USER>/full-screen.vue').default,
  },
  props: {
    activeInfluenceItem: {
      type: Object,
      default: () => {},
    },
    activeTabId: {
      type: String,
    },
    unit: {
      type: String,
      default: '张',
    },
    subtext: {
      type: String,
      default: '日均抓拍量（张）',
    },
  },
  mixins: [dataZoom],
  data() {
    let _this = this;
    return {
      propertyEchart: {},
      echartData: [],
      series: [],
      echartLoading: false,
      scatterInfo: {},
      influenceName: '',
      tooltipFormatter: (data) => {
        let captureTrendTooltip = Vue.extend(ScatterTooltip);
        let _that = new captureTrendTooltip({
          el: document.createElement('div'),
          data() {
            return {
              scatterData: data,
              influenceName: _this.influenceName,
              subtext: _this.subtext,
            };
          },
        });
        return _that.$el.outerHTML;
      },
      echartNum: 2,
      xAxisList: [],
      isScatterXcategory: false, // 是否为 散点图，且x轴为类目轴, 用于 data-zoom.js
    };
  },
  computed: {
    ...mapGetters({
      isFullscreen: 'home/getFullscreen',
    }),
    isCategory() {
      return this.scatterInfo.valueType === 'text';
    },
  },
  created() {},
  mounted() {},
  watch: {
    activeInfluenceItem: {
      handler(val) {
        if (!val || !val.key) return;
        this.getScatterInfo();
        this.getEchartData();
      },
      deep: true,
      immediate: true,
    },
    isFullscreen(val) {
      this.echartNum = val ? 16 : 2;
      this.initRing();
    },
    isCategory(val) {
      this.isScatterXcategory = val;
    },
  },
  methods: {
    changeShowData() {
      this.scrollRight('echartRef', this.xAxisList, [], this.echartNum);
    },
    getScatterInfo() {
      let { key, name } = this.activeInfluenceItem;
      this.influenceName = name; // 因素 名称
      switch (key) {
        case 'azsj':
          // 安装时长
          this.influenceName = '安装时长（年）';
          break;
        default:
          break;
      }
    },
    async getEchartData() {
      let { batchId } = this.$route.query;
      if (!batchId) return;
      try {
        this.echartLoading = true;
        let data = {
          batchId: batchId,
          customParameters: {
            inflKey: this.activeTabId,
            fieldKey: this.activeInfluenceItem.key,
          },
        };
        let res = await this.$http.post(dataAnalysis.getGraphScatterInfo, data);
        this.scatterInfo = res.data.data || {};
        this.echartData = res.data.data?.scatters || [];
        this.initRing();
      } catch (error) {
        console.log(error);
      } finally {
        this.echartLoading = false;
      }
    },
    /**
     * 散点图：
     *      1. 若axis.type = value 数值类型，需要转成 二维数组，如：[[0.5, 500], [0.2, 456]] ， 数据项也可以是对象，如 [[],{value:[1,233], ...}]
     *      2. 若axis.type = category 类目轴，同1，但数据也可以简化用一个一维数组表示， 如： [12,13,14]
     */
    initRing() {
      let { abConfigNum } = this.scatterInfo;

      this.xAxisList = [];
      let seriesData1 = [],
        seriesData2 = [],
        seriesData3 = [];

      // 大量数据的情况下，不适合把所有 同一个位置的设备  都显示出来， 不然会导致页面崩溃
      this.echartData.forEach((item, index) => {
        if (!item) {
          return;
        }
        // 是否存在 同一个位置 多个设备
        let hasSome = this.echartData.some(
          (sameItem, sameIndex) =>
            sameItem.avgShotNum === item.avgShotNum && sameItem.fieldValue === item.fieldValue && index !== sameIndex,
        );
        item._repeatNum = hasSome ? 2 : 1;
        item.deviceNames = hasSome ? `${item.deviceName} ...` : item.deviceName;
        item.deviceIds = hasSome ? `${item.deviceId} ...` : item.deviceId;

        let value = [item.fieldValue, item.avgShotNum];
        if (item.avgShotNum > abConfigNum * 2) {
          seriesData1.push({ ...item, value: value });
        }
        if (item.avgShotNum >= abConfigNum && item.avgShotNum <= abConfigNum * 2) {
          seriesData2.push({ ...item, value: value });
        }
        if (item.avgShotNum < abConfigNum) {
          seriesData3.push({ ...item, value: value });
        }
        if (!this.xAxisList.includes(item.fieldValue)) {
          this.xAxisList.push(item.fieldValue);
        }
      });

      let _this = this;
      let opts = {
        subtext: this.subtext,
        echartNum: this.echartNum,
        isFullscreen: this.isFullscreen,
        tooltipFormatter: this.tooltipFormatter,
        xAxisData: {
          name: this.influenceName,
          type: this.isCategory ? 'category' : 'value', //  value 数值轴   category 类目轴,
          data: this.xAxisList, // category 类目轴 时才生效
        },
        series: [
          {
            name: `>${abConfigNum * 2}${this.unit}`,
            type: 'scatter',
            symbolSize: function (val, params) {
              return _this.setSymbolSize(params);
            },
            emphasis: {
              focus: 'series', // 在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果
            },
            itemStyle: {
              color: $var('--color-green-6'),
            },
            data: seriesData1,
          },
          {
            name: `${abConfigNum}-${abConfigNum * 2}${this.unit}`,
            type: 'scatter',
            symbolSize: function (val, params) {
              return _this.setSymbolSize(params);
            },
            emphasis: {
              focus: 'series', // 在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果
            },
            itemStyle: {
              color: $var('--color-orange-3'),
            },
            data: seriesData2,
          },
          {
            name: `<${abConfigNum}${this.unit}`,
            type: 'scatter',
            symbolSize: function (val, params) {
              return _this.setSymbolSize(params);
            },
            emphasis: {
              focus: 'series', // 在高亮图形时，是否淡出其它数据的图形已达到聚焦的效果
            },
            itemStyle: {
              color: $var('--color-red-2'),
            },
            data: seriesData3,
          },
        ],
      };
      this.propertyEchart = this.$util.doEcharts.DailyCaptureByInfluencingFactor(opts);
      // setTimeout(() => {
      //   this.setDataZoom('echartRef', [], this.echartNum);
      // });
    },
    // 同一个位置，存在多个设备，则 symbolSize对应的x轴 加宽 --》  x轴为类目轴：21    x轴为数字轴： 8
    setSymbolSize(params) {
      let x =
        params.data._repeatNum > 1 && this.isCategory ? 21 : params.data._repeatNum > 1 && !this.isCategory ? 8 : 7;
      return [x, 7];
    },
  },
};
</script>
<style lang="less" scoped>
.scatter-echart-box {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  .influence-title {
    width: 100%;
    height: 46px;
    background: var(--bg-sub-echarts-title);
    display: flex;
    align-items: center;
    .title-rect {
      display: inline-block;
      width: 5px;
      height: 20px;
      margin-left: 20px;
      background: var(--bg-title-rect);
    }
    .title-span {
      width: fit-content;
      color: var(--color-sub-title-inpage);
    }
    .tip-color {
      color: var(--color-warning);
    }
    .tip-text {
      flex: 1;
    }
    @{_deep}.full-screen-box {
      position: inherit !important;
      margin: 0 20px;
      &.container .icon {
        color: #888888 !important;
        border: 1px solid #888888 !important;
      }
    }
    &.influence-title-fullscreen {
      height: 60px;
      font-size: 16px;
      .tip-text {
        font-size: 16px !important;
      }
    }
  }
  .echarts-box {
    width: 100%;
    padding-top: 5px;
    height: calc(100% - 46px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
