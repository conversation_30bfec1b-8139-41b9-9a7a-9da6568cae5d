
<template>
  <div class="layout">
    <tabsPage v-model="selectLi" :list="componentList" />
    <keep-alive>
      <component :is="currentComponent.componentName" :key="selectLi" />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import AlarmManager from "./alarm-manager/index.vue";
import RiskPersonnel from "./risk-personnel/index.vue";
import PlaceEventPoints from "./place-event-points/index.vue";
import ImportantPersonCluster from "./important-person-cluster/index.vue"

export default {
  name: "alarm-manager",
  components: {
    tabsPage,
    AlarmManager,
    RiskPersonnel,
    PlaceEventPoints,
    ImportantPersonCluster
  },
  data() {
    return {
      selectLi: 1,
      componentList: [
        {
          label: "重点人员发现报警",
          value: 1,
          componentName: "AlarmManager",
        },
        {
          label: "疑似重点人聚集场所",
          value: 2,
          componentName: "ImportantPersonCluster",
        },
        {
          label: "场所异常行为预警",
          value: 3,
          componentName: "RiskPersonnel",
        },
        {
          label: "场所事件积分",
          value: 4,
          componentName: "PlaceEventPoints",
        },
      ],
    };
  },
  computed: {
    currentComponent() {
      let component = this.componentList.find(
        (item) => item.value == this.selectLi
      ) || {
        label: "重点人员发现报警",
        value: 1,
        radioList: [],
        componentName: "AlarmManager",
      };
      return component;
    },
  },
  created() {
    if (this.$route.query.compareType) {
      this.selectLi = Number(this.$route.query.compareType);
    }
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
