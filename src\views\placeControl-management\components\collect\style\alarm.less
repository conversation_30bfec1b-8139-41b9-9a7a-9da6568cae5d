.box-top {
  position: relative;
  display: flex;
  justify-content: center;

  .level-title {
    height: 21px;
  }

  .num {
    text-align: center;
    position: absolute;
    top: 0;
    width: 98px;
    color: #fff;
  }

  .favorite {
    position: absolute;
    right: 5px;
  }
}

.box-wrapper {
  display: flex;

  .title {
    color: rgba(0, 0, 0, 0.6);
    margin-right: 10px;
    line-height: 22px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .box-val {
    // flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 160px;
  }
}