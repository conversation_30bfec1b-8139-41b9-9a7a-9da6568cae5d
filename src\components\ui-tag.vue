<template>
  <div class="tag">
    <span class="tag-title"><slot></slot></span>
    <i class="tag-close pointer" @click="clickClose" v-if="closeable">
      <span class="tag-icon"></span>
    </i>
  </div>
</template>
<script>
export default {
  props: {
    closeable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    clickClose() {
      this.$emit('close');
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .tag {
    .tag-close {
      border: 1px solid var(--color-primary);
    }
  }
}
.tag {
  display: inline-block;
  position: relative;
  height: 34px;
  line-height: 34px;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 0 22px 0 22px;
  font-size: 14px;
  background: var(--color-primary);
  text-align: center;
  white-space: nowrap;
  opacity: 1;
  border-radius: 4px;

  .tag-close {
    position: absolute;
    top: -7px;
    right: -7px;
    height: 14px;
    width: 14px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .tag-icon {
      display: inline-block;
      height: 2px;
      width: 7px;
      background: var(--color-primary);
    }
  }

  .tag-title {
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    cursor: default;
  }
}
</style>
