<template>
  <div :class="classes" @click.stop="check">
    <transition v-if="fade" name="fade">
      <div
        ref="tag"
        :style="{
          borderColor: !multiple && isChecked ? '#2B84E2' : borderColor,
          backgroundColor:
            (effect === 'dark' || !multiple) && isChecked
              ? '#2B84E2'
              : colorOpacityValue,
          color: colorOpacityValue,
        }"
        :class="disabled && 'ui-tag-disabled'"
        class="ui-tag-item"
      >
        <span :style="{ color: color }" class="ui-tag-text ellipsis"><slot></slot></span>
        <img
          class="ui-tag-close-icon"
          src="@/assets/img/tag_close_icon.png"
          @click.stop="close"
          alt
        />
        <img
          v-show="isChecked && multiple"
          class="ui-tag-check-icon"
          src="@/assets/img/tag_check_icon.png"
          alt
        />
        <span
          v-show="needBlock"
          :style="{ backgroundColor: color }"
          class="ui-tag-item-left"
        ></span>
        <span
          v-show="needBlock"
          :style="{ backgroundColor: color }"
          class="ui-tag-item-right"
        ></span>
      </div>
    </transition>
  </div>
</template>
<script>
export default {
  name: 'UiTag',
  props: {
    data: {
      type: Object,
      default: () => {
      }
    },
    // 标签是否可以关闭
    closable: {
      type: Boolean,
      default: false
    },
    // 标签是否可以选择
    checkable: {
      type: Boolean,
      default: false
    },
    // 标签的选中状态
    checked: {
      type: Boolean,
      default: false
    },
    // 标签的样式类型
    type: {
      type: String,
      default: undefined,
      validator (value) {
        return ['border', 'dot'].indexOf(value) !== -1
      }
    },
    // 标签颜色
    color: {
      type: String,
      default: '#2B84E2'
    },
    // 标签主题，可选值为 dark、plain
    effect: {
      type: String,
      default: 'plain',
      validator: function (value) {
        return ['dark', 'plain'].indexOf(value) !== -1
      }
    },
    // 是否在出现和消失时使用渐变的动画，动画时长可能会引起占位的闪烁
    fade: {
      type: Boolean,
      default: true
    },
    // 尺寸，可选值为 large、medium、default
    size: {
      type: String,
      default: 'default',
      validator: function (value) {
        return ['large', 'medium', 'default'].indexOf(value) !== -1
      }
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 是否禁止用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否需要方块
    needBlock: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      isChecked: this.checked,
      colorOpacityValue: this.color,
      borderColor: this.color
    }
  },
  computed: {
    classes () {
      return [
        'ui-tag',
        {
          'ui-tag-closable': this.closable,
          'ui-tag-isChecked': this.isChecked && this.multiple,
          'ui-tag-checkable': this.checkable,
          'ui-tag-multiple': this.multiple,
          'ui-tag-medium': this.size === 'medium'
        }
      ]
    }
  },
  watch: {
    checked: {
      handler (val) {
        this.isChecked = val
      },
      immediate: true,
      deep: true
    },
    color: {
      handler (val) {
        /* 如果标签主题类型为'plain'，则标签背景颜色为边框颜色，默认透明度为15%
          调用颜色值格式转换方法，将16进制转换成rgb/rgba
        */
        if (this.effect === 'plain') {
          this.colorOpacityValue = this.$util.common.colorRgb(this.color, 0.05)
          this.borderColor = this.$util.common.colorRgb(this.color, 1)
        } else {
          this.colorOpacityValue = val
        }
      },
      immediate: true,
      deep: true
    }
  },
  created () {
  },
  methods: {
    close (event) {
      if (typeof this.data === 'undefined') {
        this.$emit('on-close', event)
      } else {
        this.$emit('on-close', event, this.data)
      }
    },
    check () {
      if (!this.checkable) return
      const checked = !this.isChecked
      this.isChecked = checked
      if (typeof this.data === 'undefined') {
        this.$emit('on-change', checked)
      } else {
        this.$emit('on-change', checked, this.data)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ui-tag {
  margin: 4px 10px 4px 0;
  display: inline-block;
  height: 26px;
  .ui-tag-item {
    width: 100%;
    padding: 2px 10px;
    box-sizing: border-box;
    border-radius: 2px;
    border: 1px solid #e8eaec;
    position: relative;
    height: 100%;
    display: inline-flex;
    align-items: center;
    .ui-tag-text {
      font-size: 14px;
      line-height: 18px;
      // color: #fff;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .ui-tag-close-icon {
      width: 14px;
      height: 14px;
      position: absolute;
      top: -8px;
      right: -8px;
      display: none;
    }
    .ui-tag-item-left {
      height: 50%;
      width: 3px;
      position: absolute;
      left: -2px;
      top: 50%;
      transform: translateY(-50%);
    }
    .ui-tag-item-right {
      height: 50%;
      width: 3px;
      position: absolute;
      right: -2px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.ui-tag-medium {
  .ui-tag-item {
    padding: 0 6px;
    border-radius: 4px;
    .ui-tag-text {
      line-height: 20px;
    }
  }
}
.ui-tag-closable:hover {
  cursor: pointer;
  .ui-tag-close-icon {
    display: inline-block;
  }
}
.ui-tag-multiple {
  border: 1px solid transparent;
  border-radius: 2px;
}
.ui-tag-isChecked {
  border: 1px solid #4e9ef2;
  border-radius: 2px;
  position: relative;
  cursor: pointer;
  .ui-tag-check-icon {
    width: 14px;
    height: 14px;
    position: absolute;
    right: -2px;
    bottom: -2px;
    z-index: 1;
  }
}
.ui-tag-checkable {
  cursor: pointer;
}
.ui-tag-disabled {
  cursor: not-allowed;
  background: #000a14 !important;
  border: 1px solid #07355e !important;
  .ui-tag-text {
    opacity: 0.6;
  }
}
</style>
