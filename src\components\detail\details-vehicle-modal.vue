<!--
    * @FileDescription: 车辆
    * @Author: H
    * @Date: 2023/5/17
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-13 09:11:03
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="thumbnail">
              <img
                v-lazy="datailsInfo.traitImg || datailsInfo.sceneImg"
                alt=""
              />
              <span class="similarity" v-if="datailsInfo.similarity"
                >{{ datailsInfo.similarity }} %</span
              >
              <!-- <div class="plateNum">
                                <span class="license-plate-small">{{ datailsInfo.plateNo }}</span>
                            </div> -->
              <!-- <ui-plate-number :plateNo="datailsInfo.plateNo" :color="datailsInfo.plateColor" size='medium'></ui-plate-number> -->
              <!-- <b class="shade vehicle"> -->
              <plateNumber
                :plateNo="datailsInfo.plateNo"
                :color="datailsInfo.plateColor"
                size="medium"
              ></plateNumber>
              <!-- </b> -->
            </div>
            <div
              class="record-title"
              :style="{
                'justify-content': datailsInfo.plateNo
                  ? 'space-between'
                  : 'flex-start',
              }"
            >
              <span :class="{ active: checkIndex == 0 }" @click="tabsChange(0)"
                >通过记录</span
              >
              <!-- 当没有车牌号直接不展示档案 -->
              <span
                v-show="datailsInfo.plateNo"
                class="record-right"
                :class="{ active: checkIndex == 1 }"
                @click="tabsChange(1)"
                >车辆档案</span
              >
              <span
                class="record-right"
                :class="{ active: checkIndex == 2 }"
                @click="tabsChange(2)"
                >人车关联</span
              >
            </div>
            <div class="through-record" v-if="checkIndex == 0">
              <div class="wrapper-content">
                <span class="label">通过地点</span><span>：</span>
                <span
                  class="message ellipsis"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  @click="handlePlace(datailsInfo)"
                  :title="
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                      : datailsInfo.captureAddress || '--'
                  "
                  >{{
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, "")
                      : datailsInfo.captureAddress || "--"
                  }}</span
                >
              </div>
              <div class="wrapper-content">
                <span class="label">通过时间</span><span>：</span>
                <span class="message">{{ datailsInfo.absTime }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in veicleList"
                :key="index"
              >
                <span
                  class="label"
                  :class="{ maxLabel: item.title.length > 4 }"
                  >{{ item.title }}</span
                ><span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
                <span class="message" v-else-if="item.type == 'wear'">{{
                  handleWear(datailsInfo[item.key])
                }}</span>
                <span class="message" v-else>{{
                  datailsInfo[item.key] || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else-if="checkIndex == 1">
              <div class="wrapper-content">
                <span class="label">车牌号码</span><span>：</span>
                <span
                  class="message"
                  :class="{ plateNo: datailsInfo.plateNo }"
                  @click="toDetail(datailsInfo)"
                  >{{
                    datailsInfo.plateNo
                      ? datailsInfo.plateNo.replace(/(<\/?span.*?>)/gi, "")
                      : "--"
                  }}</span
                >
              </div>
              <div class="wrapper-content">
                <span class="label">车辆类型</span><span>：</span>
                <span class="message">
                  {{
                    recordList.vehicleType | commonFiltering(vehicleTypeList)
                  }}
                </span>
              </div>
              <div class="wrapper-content">
                <span class="label">车辆品牌</span><span>：</span>
                <span class="message"
                  >{{
                    recordList.vehicleBrand | commonFiltering(vehicleBrandList)
                  }}
                </span>
              </div>
              <div class="wrapper-content">
                <span class="label">机动车主</span><span>：</span>
                <span class="message">{{ recordList.motorists || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">身份证号</span><span>：</span>
                <span class="message">{{ recordList.idcardNo || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">联系电话</span><span>：</span>
                <span class="message">{{ recordList.phone || "--" }}</span>
              </div>
              <div class="wrapper-content">
                <span class="label">登记地址</span><span>：</span>
                <span class="message" v-show-tips>{{
                  recordList.djdz || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else>
              <div class="rcgl">
                <span class="title">关联人员:</span>
                <span
                  class="imgBox"
                  v-for="item in linkFaces"
                  :key="item.id"
                  @click="toFace(item)"
                >
                  <img v-lazy="item.traitImg" alt="" />
                  <div
                    class="driverFlag"
                    v-if="item.driverFlag == 1 || item.driverFlag == 2"
                  >
                    {{ item.driverFlag == 1 ? "主" : "副" }}
                  </div>
                </span>
              </div>
            </div>
            <!-- <div class="structuring">
                            <p class="structuring-title">结构化信息:</p>
                            <ul class="struct-box-ul">
                                <li v-for="(item, index) in veicleList" :key="index" class="struct-box-list">
                                    <p class="struct-title">{{ item.title }}</p><span>:</span>
                                    <p class="struct-content" v-if="item.type == 'filter'">{{ datailsInfo[item.key] | commonFiltering(translate(item.dictionary)) }}</p>
                                    <p class="struct-content" v-else-if="item.type == 'wear'">{{ handleWear(datailsInfo[item.key]) }}</p>
                                    <p class="struct-content" v-else>{{ datailsInfo[item.key] || '--' }}</p>
                                </li>
                            </ul>
                        </div> -->
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleLeft"
            ></i>
            <details-largeimg
              boxSeleType="vehicleRect"
              :info="datailsInfo"
              @collection="collection"
              :algorithmType="2"
              :collectionType="6"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                'sc',
                'dmt',
              ]"
              @onAction="multilAnalysisHandler"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleRight"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleLeft"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * numLength + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="index"
            >
              <div class="img-box">
                <img v-lazy="item.traitImg || item.sceneImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleRight"
        ></i>
      </footer>
    </div>
    <detailMutiAnalysis
      v-if="isShowMutilAnalysis"
      ref="detailMutiAnalysisRef"
      :tableList="multiDataList"
      :isNoPage="true"
      @close="isShowMutilAnalysis = false"
    ></detailMutiAnalysis>
  </div>
</template>

<script>
import { getCarInfoByCarNo } from "@/api/operationsOnTheMap";
import { mapActions, mapGetters, mapMutations } from "vuex";
import detailsLargeimg from "./details-largeimg.vue";
import { veicleList } from "./structuring.js";
import { commonMixins } from "@/mixins/app.js";
import { cutMixins } from "./mixins.js";
import plateNumber from "@/components/ui-vehicle/index.vue";
import { getVehicleCaptureLink } from "@/api/wisdom-cloud-search";
import { mutilMixins } from "./mutil-mixins.js";
import detailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
export default {
  name: "",
  components: {
    detailsLargeimg,
    plateNumber,
    detailMutiAnalysis,
  },
  mixins: [commonMixins, cutMixins, mutilMixins], //全局的mixin
  data() {
    return {
      cutList: [],
      activeIndex: 0,
      veicleList,
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("../../assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      recordList: {},
      overlay: false,
      showSkip: false,
      checkIndex: 0,
      linkFaces: [],
    };
  },
  watch: {},
  computed: {
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
    ...mapGetters({
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      getCollectJudge: "map/getCollectJudge",
    }),
    numLength() {
      return this.cutList.length;
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 开始数据
    init(row, list, index, page) {
      this.checkIndex = 0;
      this.cutList = list;
      this.activeIndex = index;
      this.datailsInfo = row;
      let small_pic = document.getElementById("scroll-ul");
      small_pic.style.left = 0;
      this.setPageInfo({
        startPage: page,
        endPage: page,
      });
      this.num = index;
      this.fetchVehicleCaptureLink();
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(index);
      });
    },
    // 切换
    handleTab(item, index) {
      this.checkIndex = 0;
      this.resetData();
      this.datailsInfo = item;
      this.fetchVehicleCaptureLink();
      this.activeIndex = index;
      this.play(index);
    },
    // 人车关联
    fetchVehicleCaptureLink() {
      this.linkFaces = [];
      if (this.datailsInfo.linkFaceRid) {
        getVehicleCaptureLink({
          absTime: this.datailsInfo.absTime,
          recordId: this.datailsInfo.id,
        }).then((res) => {
          this.linkFaces = res.data.linkFaces ? res.data.linkFaces : [];
        });
      }
    },
    scrollIntoViews() {
      this.$nextTick(() => {
        let ul = document.querySelector(".list-wrapper");
        let li = [...ul.childNodes][this.activeIndex];
        li.scrollIntoView({
          behavior: "smooth",
        });
      });
    },
    collection(flag) {
      this.cutList[this.activeIndex].myFavorite = flag;
      // this.datailsInfo.myFavorite = flag;
    },
    async tabsChange(index) {
      if (this.checkIndex == index) {
        return;
      }
      this.checkIndex = index;
      if (this.checkIndex == 1) {
        if (!this.datailsInfo.plateNo) {
          this.showSkip = true;
          // this.$Message.warning('档案不存在！');
          return;
        }
        try {
          this.showSkip = true;
          let res = await getCarInfoByCarNo({
            // 这里需要携带车牌颜色
            carNo: this.datailsInfo.plateNo + "_" + this.datailsInfo.plateColor,
          });
          if (res.code === 200 && !!res.data) {
            this.recordList = res.data || {};
            this.showSkip = false;
          } else {
            throw error;
          }
        } catch (error) {
          this.showSkip = true;
          // this.$Message.warning('档案暂无数据')
        }
      }
    },
    handlePlace(row) {
      if (row.taskType === "3") return;
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1",
        query: {
          deviceInfo: JSON.stringify({ ...row, deviceGbId: row.deviceId }),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    toDetail(item) {
      if (!item.plateNo) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query: {
          //   archiveNo: JSON.stringify(this.recordList.archiveNo),
          archiveNo: JSON.stringify(item.plateNo + "_" + item.plateColor),
          plateNo: JSON.stringify(this.recordList.plateNo || item.plateNo),
          source: "car",
          idcardNo: this.recordList.idcardNo || item.idcardNo,
        },
      });
      window.open(href, "_blank");
    },
    toFace(item) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        query: {
          imgUrl: item.traitImg,
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.struct-box-list {
  .struct-title {
    width: 99px !important;
  }
}
.thumbnail {
  /deep/ .douback {
    padding: 2px 4px;
  }
  /deep/.medium {
    span {
      // padding: 0;
    }
  }
  /deep/.energy-dou {
    top: 7px;
    span {
      padding: 0;
    }
  }
}
.info-box-left {
  .record-title {
    display: flex;
    > span {
      cursor: pointer;
    }
    .record-right {
      margin-left: 15px !important;
    }
  }
}
.device-click {
  cursor: pointer;
  text-decoration: underline;
}
.rcgl {
  .title {
    color: #2c86f8;
  }
  .imgBox {
    cursor: pointer;
    width: 80%;
    margin: 5px 0;
    display: block;
    position: relative;
    img {
      width: 100%;
    }
    .driverFlag {
      position: absolute;
      right: 2px;
      top: 2px;
      color: #fff;
      background: red;
      border-radius: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
