import evaluationoverview from '@/config/api/evaluationoverview';
import governancetask from '@/config/api/governancetask';
export default {
  props: {
    editViewAction: {
      default: () => {
        return {
          type: 'view',
          row: {},
        };
      },
    },
  },
  methods: {
    async getDetailInfo(params) {
      //params没值
      try {
        let requestObj = this.returnVideoRequestObj(params);
        let res = {};
        if (requestObj.method == 'get') {
          res = await this.$http.get(requestObj.url, { params: requestObj.param });
        } else {
          res = await this.$http.post(requestObj.url, requestObj.param);
        }
        let data = res.data.data;
        if (['4004', '4006'].includes(this.editViewAction.row.indexId)) {
          //时钟准确率
          return { ...data, info: data };
        } else {
          //视频流通用
          const rowType = [
            { dataKey: 'clockDetail', dataValue: '时钟信息' },
            { dataKey: 'areaInfo', dataValue: '区划和地址' },
            { dataKey: 'deviceInfo', dataValue: '摄像机信息' },
          ];
          let detialTableData = [];
          rowType.forEach((item) => {
            if (!!data[item.dataKey] && !!data[item.dataKey].isDetect) {
              let rowData = {
                colTitle: item.dataKey,
                colTitleText: item.dataValue,
                ...data[item.dataKey],
              };
              detialTableData.push(rowData);
            }
          });
          if (data.qualified) {
            detialTableData.push({ resQualified: data.qualified });
          }
          return {
            ...data,
            detialTableData,
            // imageUrl: data.imageUrl,
            info: data,
          };
        }
      } catch (e) {
        console.log(e);
        return { detialTableData: [], imageUrl: '', info: {} };
      }
    },
    returnVideoRequestObj() {
      let obj = { url: '', method: '', param: {} };
      if (this.entryType === 'deviceInfo') {
        if (['4004', '4006'].includes(this.editViewAction.row.indexId)) {
          const { deviceId, batchId } = this.editViewAction.row;
          obj.url = evaluationoverview.queryVideoResultByBatchIdAndDeviceId;
          obj.method = 'post';
          obj.param = {
            batchId: batchId,
            deviceId: deviceId,
          };
        } else {
          const { deviceId, batchId } = this.editViewAction.row;
          obj.url = evaluationoverview.queryOsdDetailByBatchIdAndDeviceId;
          obj.method = 'get';
          obj.param = {
            batchId: batchId,
            deviceId: deviceId,
          };
        }
      }
      if (this.entryType == 'table') {
        //操作记录中接口
        if (['4004', '4006'].includes(this.editViewAction.row.indexId)) {
          const { id, deviceId } = this.entryRowObj;
          obj.url = governancetask.queryDetailDetail;
          obj.method = 'get';
          obj.param = {
            workOrderFlowId: id,
            deviceId: deviceId,
          };
        } else {
          const { id, deviceId } = this.entryRowObj;
          obj.url = governancetask.queryOsdDetailByBatchIdAndDeviceId;
          obj.method = 'get';
          obj.param = {
            workOrderFlowId: id,
            deviceId: deviceId,
          };
        }
      }

      return obj;
    },
  },
};
