<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-25 10:32:45
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-control-task\criminal-record\index.vue
-->
<template>
  <div class="container">
    <searchForm
      v-show="!$route.query.noSearch"
      ref="searchFormRef"
      @query="query"
      @reset="reset"
      type="task"
      :compareType="compareType"
      :radioList="radioList"
      :taskList="taskList"
    >
      <query ref="slotQuery" />
    </searchForm>
    <!-- 操作栏 -->
    <div class="operate">
      <div class="control-title">布控列表</div>
      <Button class="margin" type="primary" @click="add()">新增布控</Button>
    </div>
    <!-- 列表 -->
    <div class="list">
      <TaskCard
        class="alarnRow"
        v-for="item in taskList"
        @edit="editHandler"
        @dele="deleHandler"
        @remove="removeHandler"
        :key="item.id"
        :taskInfo="item"
      />
      <!-- @click.native="detail(item)" -->
      <ui-empty v-if="taskList.length === 0"></ui-empty>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
    ></ui-page>
  </div>
</template>

<script>
import searchForm from "./components/search-form.vue";
import TaskCard from "../components/task-card.vue";
import query from "../components/query.vue";
import { getTaskPageList, deleteTask } from "@/api/monographic/juvenile.js";
import { taskApply } from "@/api/target-control";
export default {
  name: "",
  components: { searchForm, TaskCard, query },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 0, value: "未执行" },
        { key: 1, value: "执行中" },
      ],
    },
  },
  data() {
    return {
      tableList: [],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableLoading: false,
      taskList: [],
    };
  },
  mounted() {
    this.query();
  },
  methods: {
    /**
     * @description: 手动触发查询
     */
    query() {
      let data = {
        ...this.params,
        ...this.$refs.searchFormRef.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
      };
      // 布控审核状态：0：未提交 1未审核 2：审核通过 3：驳回 4：已删除 5:布控到期
      if (data.operationType == 0) {
        data.taskStatusList = [0, 1, 3, 4, 5];
      } else if (data.operationType == 1) {
        data.taskStatus = 2;
      } else {
        data.operationType = null;
      }
      getTaskPageList(data).then((res) => {
        this.taskList = res.data?.entities || [];
        this.total = res.data.total;
        this.taskList.forEach((item) => {
          if (item.taskType == 2) {
            let libs = "";
            item.libVoList.forEach((ite, index) => {
              if (index > 0) libs += ", ";
              libs += ite.libName;
            });
            item.libs = libs;
          }
        });
      });
    },
    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.$refs.slotQuery.reset();
      this.query();
    },
    /**
     * @description: 新增布控任务
     * @return {*}
     */
    add() {
      this.$router.push({
        name: "juvenile-control-task-add",
        query: {
          compareType: 1,
        },
      });
    },
    /**
     * @description: 布控任务详情
     * @param {object} item 布控任务数据
     */
    detail(item) {
      this.$router.push({
        name: "juvenile-control-task-detail",
        query: {
          id: item.taskId,
          compareType: this.compareType,
          alarmCount: item.alarmCount,
          noMenu: this.$route.query.noMenu,
        },
      });
    },
    editHandler(taskInfo) {
      this.$router.push({
        name: "juvenile-control-task-edit",
        query: {
          taskId: taskInfo.taskId,
          compareType: 1,
        },
      });
    },
    deleHandler(taskInfo) {
      if (taskInfo.taskStatus == 1) {
        this.$Message.warning(`执行中任务不可删除,请停用后,再删除数据！`);
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        content: "确认删除该布控任务？",
        onOk: () => {
          deleteTask(taskInfo.taskId).then((res) => {
            this.$Message.success("删除成功");
            this.query();
          });
        },
      });
    },
    removeHandler(taskInfo) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定撤控该布控信息？`,
        onOk: () => {
          let params = {
            taskId: taskInfo.taskId,
            checkUser: this.userInfo.username,
            taskStatus: 0,
          };
          taskApply(params).then((res) => {
            this.$Message.success(`撤控代办成功`);
            this.query();
          });
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    .control-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 34px;
    }
  }
  .list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    overflow-y: scroll;
    align-content: flex-start;
    position: relative;
    .alarnRow {
      width: ~"calc(20% - 10px)";
      height: 244px;
    }
  }
}
</style>
