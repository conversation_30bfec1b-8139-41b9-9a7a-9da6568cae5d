<template>
  <div class="com-view">
    <div ref="echart" class="graph-echart"></div>
  </div>
</template>
<script>
  import * as echarts from 'echarts'
  export default {
    data () {
      return {
        myEchart: null,
        cameraImg: require('../../../../assets/img/deviceInfo/pic_camera.png'),
        labelPropertyImg: require('../../../../assets/img/deviceInfo/label_property_bg.png')
      }
    },
    mounted () {
      this.init()
    },
    methods: {
      init () {
        const _that = this
        this.myEchart = echarts.init(this.$refs.echart)
        const propertyRich = {
          name: {
            color: '#F5FBFF',
            fontSize: 15,
            lineHeight: 20,
            fontWeight: 'bold',
            fontFamily: 'MicrosoftYaHei-Bold',
            align: 'center'
          },
          line: {
            width: 18,
            height: 2,
            backgroundColor: 'rgba(255, 255, 255, 0.3)',
            lineHeight: 10,
            align: 'center'
          },
          count: {
            color: '#E99E53',
            fontFamily: 'MicrosoftYaHei-Bold',
            fontWeight: 'bold',
            fontSize: 16,
            lineHeight: 20,
            align: 'center'
          }
        }
        const myGraphData = [
          {
            name: '信息设备',
            x: 740,
            y: 290,
            fixed: true,
            symbol: 'image://' + _that.cameraImg,
            symbolSize: [320, 320],
            label: {
              show: false
            },
            childNodes: [
              { name: '状态标签' },
              { name: '业务标签' }
            ]
          },
          {
            name: '状态标签',
            x: 540,
            y: 150,
            fixed: true,
            count: 4,
            symbol: 'image://' + _that.labelPropertyImg,
            symbolSize: [88, 77],
            label: {
              show: true,
              formatter: function (params) {
                return `{name|${params.name}}\n{line|}\n{count|${params.data.count}}`
              },
              rich: { ...propertyRich }
            },
            childNodes: [
              {
                name: '已建档',
                color: '#BE2BE2',
                label: {
                  formatter: function (params) {
                    return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
                  },
                  rich: this.labelRichFun('#BE2BE2')
                }
              },
              {
                name: '已联网',
                color: '#2B84E2',
                label: {
                  formatter: function (params) {
                    return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
                  },
                  rich: this.labelRichFun('#2B84E2')
                }
              },
              {
                name: '在用',
                color: '#0E8F0E',
                label: {
                  formatter: function (params) {
                    return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
                  },
                  rich: this.labelRichFun('#0E8F0E')
                }
              }
            ]
          },
          {
            name: '状态标签',
            x: 450,
            y: 250,
            fixed: true,
            count: 4,
            symbol: 'image://' + _that.labelPropertyImg,
            symbolSize: [88, 77],
            label: {
              show: true,
              formatter: function (params) {
                return `{name|${params.name}}\n{line|}\n{count|${params.data.count}}`
              },
              rich: { ...propertyRich }
            },
            childNodes: [
              {
                name: '已建档',
                color: '#BE2BE2',
                label: {
                  formatter: function (params) {
                    return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
                  },
                  rich: this.labelRichFun('#BE2BE2')
                }
              },
              {
                name: '已联网',
                color: '#2B84E2',
                label: {
                  formatter: function (params) {
                    return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
                  },
                  rich: this.labelRichFun('#2B84E2')
                }
              },
              {
                name: '在用',
                color: '#0E8F0E',
                label: {
                  formatter: function (params) {
                    return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
                  },
                  rich: this.labelRichFun('#0E8F0E')
                }
              }
            ]
          }
        ]
        function setNodeData (arr, m, n, listdata) {
          for (var i = 0; i<arr.length; i++) {
            listdata.push({
              id: m++,
              category: n,
              ...arr[i]
            })
          }
        }

        function setLinkData (sourceList, m, links) {
          for (var i = 0; i<sourceList.length; i++) {
            links.push({
              'source': sourceList[i],
              'target': m,
              // ignoreForceLayout: true,
              value: 100
            })
          }
        }

        var listdata = []
        var linksdata = []

        var nodeData = myGraphData
        var m = 0
        var source = []
        for (var i = 1; i < nodeData.length; i++) {
          var node = nodeData[i]
          var tx = [node]
          setNodeData(tx, m, 1, listdata)
          source.push(m)

          var Data = nodeData[i].childNodes
          setNodeData(Data, m + 1, 2, listdata)

          var sourceList = []
          for (var n = m + 1; n < m + Data.length + 1; n++) {
              sourceList.push(n)
          }
          setLinkData(sourceList, m, linksdata)
          m = m + Data.length + 1
        }

        var tx7 = []
        tx7.push(nodeData[0])
        setNodeData(tx7, m, 0, listdata)
        setLinkData(source, m, linksdata)

        const option = {
          tooltip: {
            formatter: '{b}'
          },
          series: [{
            name: '知识图谱',
            type: 'graph',
            layout: 'force',
            force: {
              repulsion: 1000,
              gravity: 0,
              edgeLength: [10, 100],
              layoutAnimation: false
            },
            data: listdata,
            links: linksdata,
            categories: [],
            roam: true,
            label: {
              show: true
            },
            edgeSymbol: ['circle', 'none'],
            edgeSymbolSize: 4,
            lineStyle: {
              color: '#54BAF3',
              width: 1.5,
              curveness: 0
            }
          }]
        }
        this.myEchart.setOption(option)
      },
      labelRichFun (color) {
        return {
          name: {
            color: '#fff',
            backgroundColor: this.$util.common.colorRgb(color, 0.20),
            borderWidth: 0.5,
            borderColor: color,
            borderRadius: 2,
            padding: [7, 20, 3, 20],
            fontSize: 18,
            lineHeight: 24,
            fontFamily: 'MicrosoftYaHei',
            marginLeft: '-2px'
          },
          leftOut: {
            width: 1.5,
            height: 14,
            backgroundColor: color,
            padding: [0, 0, 0, 1.5]
          },
          leftInside: {
            height: 14,
            backgroundColor: color,
            padding: [0, 1.5, 0, -3]
          },
          rightInside: {
            height: 14,
            backgroundColor: color,
            padding: [0, -3, 0, 1.5]
          },
          rightOut: {
            width: 1.5,
            height: 14,
            backgroundColor: color,
            padding: [0, 1.5, 0, 0]
          }
        }
      }
    }
  }
</script>
<style lang="less" scoped>
.com-view {
  width: 78%;
  height: 62vh;
  position: absolute;
  left: 400px;
  color: #fff;
  .graph-echart {
    width: 100%;
    height: 100%;
  }
}
</style>