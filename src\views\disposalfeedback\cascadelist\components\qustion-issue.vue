<template>
  <ui-modal v-model="visible" title="问题下发" @onCancel="reset" :styles="styles">
    <div class="question-handle">
      <Form :label-width="80">
        <FormItem label="下发对象" required>
          <Input type="text" disabled v-model="activeItem.orgName" class="address"></Input>
        </FormItem>
        <FormItem label="问题名称" required>
          <span class="color-failed inline form-name">{{ activeItem.name }}</span>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button class="plr-30" type="primary" @click="confirmIssue">确定下发</Button>
    </template>
  </ui-modal>
</template>
<script>
import cascadeList from '@/config/api/cascadeList';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    activeItem: {},
  },
  data() {
    return {
      taskList: [],
      form: {
        finishResult: '已处理',
        finishSituation: '',
      },
      visible: false,
      styles: {
        width: '4.2rem',
      },
    };
  },
  created() {},
  updated() {},
  methods: {
    async confirmIssue() {
      try {
       await this.$http.post(cascadeList.publishIssue, [this.activeItem.id]);
        this.$emit('updateList');
      } catch (error) {
        console.log(error);
      }
    },
    changeTaskName() {},
    reset() {
      this.form.finishResult = '已处理';
      this.form.finishSituation = '';
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.question-handle {
  width: 100%;
}
.form-name {
  height: 38px;
  line-height: 38px;
}
.remark {
  width: 100%;
}
</style>
