<template>
  <div class="statistics-list auto-fill">
    <title-bar
      v-bind="$attrs"
      :index-data="indexData"
      :tab-list="tabList"
      :sort-data="sortData"
      @handleChangeTab="handleChangeTab"
    >
    </title-bar>
    <div class="statistics-list-content auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :row-class-name="rowClassName"
        @onSortChange="onSortChange"
      >
        <template #civilName="{ row, column }">
          <span
            :class="['font-highlight', paramsList.dateType === '1' ? 'font-active-color underline-text pointer' : '']"
            @click="viewMonthDetail(row)"
          >
            {{ row[column.key] }}
          </span>
        </template>
        <template #detection="{ row, column }">
          <span
            :class="[paramsList.dateType === '2' ? 'underline-text pointer' : '']"
            @click="viewDayDetail(row, column, '')"
          >
            {{ row[column.key] }}
          </span>
        </template>
        <template #qualified="{ row, column }">
          <span
            :class="[paramsList.dateType === '2' ? 'underline-text pointer' : '']"
            @click="viewDayDetail(row, column, '1')"
          >
            {{ row[column.key] }}
          </span>
        </template>
        <template #unqualified="{ row, column }">
          <span
            :class="['unqualified-color', paramsList.dateType === '2' ? 'underline-text pointer' : '']"
            @click="viewDayDetail(row, column, '2')"
          >
            {{ row[column.key] }}
          </span>
        </template>
        <template #rate="{ row, column }">
          <span
            v-if="row[column.key] || row[column.key] === 0"
            :class="getQual(row, column) ? '' : 'unqualified-color'"
          >
            {{ row[column.key] || 0 }}%</span
          >
        </template>
      </ui-table>
    </div>
    <day-review-details
      v-model="dayReviewDetailVisible"
      v-bind="$attrs"
      :data-dimension-enum="dataDimensionEnum"
      :index-data="indexData"
      :row-data="rowData"
    ></day-review-details>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import { getTableColumns } from './tableColumns.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import { DAY_STATISTICS } from '@/views/specialassessment/utils/common';

export default {
  name: 'StatisticsList',
  mixins: [dealWatch],
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      paramsList: {},
      loading: false,
      tableColumns: [],
      tableData: [],
      dayReviewDetailVisible: false,
      dataDimensionEnum: 'DEVICE_ALL',
      rowData: {},
      tabList: [],
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      sortType: '',
      currentSortField: '',
    };
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        let { dateType, batchIds } = this.$route.query;
        if (dateType === DAY_STATISTICS && !batchIds) return;
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        const queryParams = this.$route.query;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: queryParams.examineTime,
          type: queryParams.dateType || '2',
          dataDimensionEnum: this.dataDimensionEnum,
          nodeEnum: queryParams.indexType,
          paramForm: {},
          statisticalModel: queryParams.statisticsCode ? Number(queryParams.statisticsCode) : null,
          displayType:
            queryParams.statisticsCode === '1'
              ? 'ORG'
              : queryParams.statisticsCode === '2'
                ? 'REGION'
                : queryParams.statisticsCode === '3'
                  ? 'TAG'
                  : '',
        };
        if (queryParams.dateType === '2') {
          params.batchIds = queryParams.batchIds ? queryParams.batchIds.split(',') : [];
        }
        if (this.sortData.sortField) {
          params.paramForm = {
            sortField: this.sortData.sortField,
            sort: this.sortData.sort,
            indexId: this.sortData.indexId,
          };
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfoList, params);
        if (!data || !data.length) {
          this.tableData = [];
          return false;
        }
        this.tabList = !!data[0] && data[0].dataDimensionsDisplay ? data[0].dataDimensionsDisplay : [];
        let indexIds = [];
        this.tableData = data.map((item) => {
          let obj = { ...item };
          item.detail.forEach((detailItem) => {
            if (!indexIds.includes(detailItem.indexId)) {
              indexIds.push(detailItem.indexId);
            }
            Object.keys(detailItem).forEach((keyItem) => {
              obj[`${detailItem.indexId}-${keyItem}`] = detailItem[keyItem];
            });
          });
          return obj || {};
        });
        // 由于组装表格列的配置数据
        this.tableColumns = this.tableColumns.map((colItem) => {
          if (!colItem.colkey && !colItem.indexIds) {
            return colItem;
          }
          indexIds.forEach((id) => {
            if (!!colItem.indexIds && colItem.indexIds.includes(id)) {
              colItem.children.forEach((childItem) => {
                childItem.key = id + '-' + childItem.keyName;
                // 表格刷新，表格列排序配置回显
                if (!!this.sortType && childItem.key === this.currentSortField) {
                  childItem.sortType = this.sortType;
                } else {
                  childItem.sortType = 'normal';
                }
              });
            }
          });
          return colItem;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    rowClassName(row, index) {
      if (index === this.tableData.length - 1) {
        return 'active-blue';
      }
      return '';
    },
    getQual(row, column) {
      const keyArr = column.key.split('-');
      const indexId = keyArr[0];
      return row[`${indexId}-qual`] === '1';
    },
    onSortChange(column) {
      this.sortType = column.order;
      this.currentSortField = column.key;
      if (column.order === 'normal') {
        this.sortData = {};
      } else {
        const keyArr = column.key.split('-');
        this.sortData = {
          indexId: keyArr[0],
          sortField: keyArr[1],
          sort: column.order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    handleChangeTab(data) {
      this.dataDimensionEnum = data.value;
      this.getTableList();
    },
    viewMonthDetail(row) {
      if (this.paramsList.dateType === '2') return false;
      row.indexIds = row.detail.map((item) => item.indexId);
      this.$emit('changeComponentName', [
        'MonthReviewDetail',
        {
          dataDimensionEnum: this.dataDimensionEnum,
          ...row,
        },
      ]);
    },
    viewDayDetail(row, column, qualVal) {
      if (this.paramsList.dateType === '1') return false;
      this.dayReviewDetailVisible = true;
      const keyArr = column.key.split('-');
      const indexId = keyArr[0];
      const detail = row.detail.find((item) => item.indexId == indexId);
      this.rowData = {
        qualVal,
        ...row,
        ...detail,
      };
    },
    getParams() {
      this.paramsList = this.$route.query;
      let { dateType, indexType, statisticsCode } = this.$route.query;
      const columnsList = getTableColumns({ statisticsCode: statisticsCode });
      let columns = this.$util.common.deepCopy(columnsList[indexType]);
      if (dateType === '1') {
        columns = columns.map((item) => {
          if (item.colkey) {
            item.title = item.title + '（月平均）';
          }
          return item;
        });
      }
      this.tableColumns = columns;
      this.tableData = [];
      this.getTableList();
    },
  },
  components: {
    TitleBar: require('@/views/specialassessment/components/title-bar.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DayReviewDetails: require('@/views/specialassessment/special-modules/components/day-review-details/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.statistics-list {
  width: 100%;
  height: 100%;
  overflow-y: hidden;
  &-content {
    padding: 0 20px;
    @{_deep} .ivu-table {
      &-header thead tr th {
        border: 1px solid var(--border-table);
      }

      .ivu-table-tbody td {
        border: 1px solid var(--border-table);
      }

      .ivu-table-tbody tr:first-child td {
        border-top: none;
      }
      &-overflowY {
        overflow-y: auto;
      }
    }
  }

  .underline-text {
    text-decoration: underline;
  }
  .unqualified-color {
    color: var(--color-failed);
  }
  .qualified-color {
    color: var(--color-success);
  }
  @{_deep} .active-blue {
    td:nth-child(2) {
      .font-highlight {
        color: var(--color-warning) !important;
      }
    }
  }
}
</style>
