<template>
  <!-- 填报准确率 -->
  <ui-modal
    class="basic-information"
    v-model="visible"
    v-if="visible"
    title="查看详情"
    :styles="styles"
    :footer-hide="true"
  >
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg"> 指标名称：{{ this.$parent.row.indexName }} </span>
          <span class="fl text_left"> 评测时间：{{ this.$parent.row.createTime }} </span>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="onClickIndex">
              <span class="inline ml-xs">导出</span>
            </Button>
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="异常数据列表"></line-title>
        <ui-select-tabs class="list_item" :list="selectTabs" @selectInfo="selectInfo"></ui-select-tabs>
        <div class="list auto-fill">
          <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
            <template #longitude="{ row }">
              <span>{{ row.longitude | filterLngLat }}</span>
            </template>
            <template #latitude="{ row }">
              <span>{{ row.latitude | filterLngLat }}</span>
            </template>
            <template #whdw="{ row }">
              <span>{{ row.whdw }}</span>
            </template>
            <template #option="{ row }">
              <ui-btn-tip
                icon="icon-chakanyichangxiangqing"
                content="不合格原因"
                @click.native="checkReason(row)"
              ></ui-btn-tip>
            </template>
          </ui-table>
          <loading v-if="loading"></loading>
        </div>
        <ui-page
          class="page"
          :page-data="searchData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        ></ui-page>
      </div>
      <nonconformance
        ref="nonconformance"
        title="查看不合格原因"
        :tableColumns="reasonTableColumns"
        :tableData="reasonTableData"
        :reasonPage="reasonPage"
        :reasonLoading="reasonLoading"
        @handlePageChange="handlePageChange"
        @handlePageSizeChange="handlePageSizeChange"
      ></nonconformance>
      <export-data
        ref="exportModule"
        :cascadeId="$parent.row.cascadeId"
        :superiorToken="$parent.row.superiorToken"
        @exportAdd="exportAdd"
      ></export-data>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.basic-information {
  // @{_deep}.ivu-select-dropdown {
  //   position: absolute !important;
  //   left: 1528px !important;
  // }

  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 0 20px;
    }
  }

  .no-box {
    width: 1726px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: var(--color-content);
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        display: inline-block;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          @{_deep}.ivu-btn {
            .ivu-btn-primary[disabled] {
              color: #fff !important;
              background: linear-gradient(180deg, #2b84e2 0%, #083a78 100%);
            }
          }
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: var(--border-modal-footer);
        }
      }
    }

    .list {
      margin-top: 10px;
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }

    .btn-text-default {
      cursor: pointer;
      font-size: 14px;
      color: var(--color-primary);
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import { proxyInterfacefunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],
      exportName: '',
      echartRing: {},
      ringStyle: { width: '750px', height: '180px' },
      zdryChartObj: {
        xAxisData: ['', ''],
        showData: [
          { name: '合格设备', value: 0 },
          { name: '不合格设备', value: 0 },
        ],
        zdryTimer: null,
        totalNum: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '填报准确率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: 0,
        remarkValue: '',
      },
      nonconformance: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          width: 120,
        },
        {
          minWidth: 120,
          title: '维护单位',
          slot: 'whdw',
          tooltip: true,
          align: 'left',
        },
        { title: `${this.global.filedEnum.macAddr}`, key: 'macAddr', width: 150 },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 120 },
        {
          title: '数据来源',
          key: 'sourceIdText',
          minWidth: 150,
          tooltip: true,
        },
        { title: '安装地址', key: 'address', minWidth: 150, tooltip: true },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],
      minusTable: 580,
      searchData: { totalCount: 0, pageNum: 1, pageSize: 20 },
      styles: { width: '9rem' },
      visible: true,
      loading: false,
      modalId: { title: '测评结果' },
      selectTabs: [],
      resultId: '',
      indexId: '',
      deviceInfoId: '',
      errorMessages: [],
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      indexList: {},
      reasonLoading: false,
      indexLoading: false,
      exportLoading: false,
    };
  },
  async mounted() {
    await this.getTableData();
    await this.getChartsData(); // 获取图表数据
    await this.initRing();
    await this.getSelectTabs();
  },

  methods: {
    onClickIndex() {
      this.$refs.exportModule.init(this.$parent.row.resultId);
    },
    exportAdd(val) {
      this.getExport(val);
    },
    async getExport(val) {
      let data = [];
      val.list.map((item) => {
        data.push(item.orgCode);
      });
      this.exportLoading = true;
      let params = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        multiSheet: val.multiSheet,
        orgCodes: data,
        errorMessages: this.errorMessages,
      };
      // 级联清单特殊替换处理接口(后端转发)
      if (this.$parent.row.cascadeId) {
        let cascadeId = this.$parent.row.cascadeId;
        let superiorToken = this.$parent.row.superiorToken;
        let interfaceName = governanceevaluation.exportAccuracy;
        await proxyInterfacefunc(params, interfaceName, cascadeId, superiorToken, 'post', {
          responseType: 'blob',
        }).then((res) => {
          this.$util.common.exportfile(res);
          this.exportLoading = false;
          this.$refs.exportModule.hide();
        });
        return;
      }
      try {
        let res = await this.$http.post(evaluationoverview.exportAccuracy, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
        this.$refs.exportModule.hide();
      } catch (err) {
        console.log(err);
      }
    },

    async getChartsData() {
      try {
        this.indexLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        // 级联清单特殊替换处理接口(后端转发)
        if (this.$parent.row.cascadeId) {
          let cascadeId = this.$parent.row.cascadeId;
          let superiorToken = this.$parent.row.superiorToken;
          let interfaceName = governanceevaluation.queryEvaluationBasicRecord;
          await proxyInterfacefunc(params, interfaceName, cascadeId, superiorToken).then((data) => {
            this.indexList = data;
            this.moduleData.rateValue = this.indexList.resultValue; // 填报准确率
            this.moduleData.priceValue = this.indexList.standardsValue; // 达标值
            this.moduleData.resultValue = this.indexList.qualifiedDesc; // 考核结果
            this.indexLoading = false;
          });
          return;
        }
        let res = await this.$http.post(governanceevaluation.queryEvaluationBasicRecord, params);
        if (!res.data.data) return;
        this.indexList = res.data.data;
        this.moduleData.rateValue = this.indexList.resultValue; // 填报准确率
        this.moduleData.priceValue = this.indexList.standardsValue; // 达标值
        this.moduleData.resultValue = this.indexList.qualifiedDesc; // 考核结果
        this.indexLoading = false;
      } catch (error) {
        this.indexLoading = false;
        console.log(error);
      }
    },
    initRing() {
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '合格设备') {
          item.value = this.indexList.accuracyFieldsQualifiedCount;
        } else {
          item.value = this.indexList.accuracyFieldsSumCount - this.indexList.accuracyFieldsQualifiedCount;
        }
      });
      let showData = this.zdryChartObj.showData;
      this.zdryChartObj.count = this.indexList.accuracyFieldsSumCount;
      let formatData = {
        seriesName: '检测设备总量',
        showData: showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    async getSelectTabs() {
      try {
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        // 级联清单特殊替换处理接口(后端转发)
        if (this.$parent.row.cascadeId) {
          let cascadeId = this.$parent.row.cascadeId;
          let superiorToken = this.$parent.row.superiorToken;
          let interfaceName = governanceevaluation.queryErrorMessageList;
          proxyInterfacefunc(params, interfaceName, cascadeId, superiorToken).then((data) => {
            this.selectTabs = data.map((item) => {
              let obj = {};
              obj.name = item;
              return obj;
            });
          });
          return;
        }
        let res = await this.$http.post(governanceevaluation.queryErrorMessageList, params);
        this.selectTabs = res.data.data.map((item) => {
          let obj = {};
          obj.name = item;
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getTableData() {
      try {
        this.loading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          errorMessages: this.errorMessages,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        // 级联清单特殊替换处理接口(后端转发)
        if (this.$parent.row.cascadeId) {
          let cascadeId = this.$parent.row.cascadeId;
          let superiorToken = this.$parent.row.superiorToken;
          let interfaceName = governanceevaluation.queryEvaluationDevicePageList;
          await proxyInterfacefunc(params, interfaceName, cascadeId, superiorToken).then((data) => {
            this.searchData.totalCount = data.total;
            this.tableData = data.entities;
          });
          return;
        }
        let res = await this.$http.post(governanceevaluation.queryEvaluationDevicePageList, params);
        const datas = res.data.data;
        this.searchData.totalCount = datas.total;
        this.tableData = datas.entities;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    selectInfo(val) {
      this.errorMessages = val.map((item) => {
        return item.name;
      });
      this.getTableData();
    },
    checkReason(row) {
      this.deviceInfoId = row.deviceInfoId;
      this.getReason();
      this.$refs.nonconformance.init();
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        deviceInfoId: this.deviceInfoId,
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      // 级联清单特殊替换处理接口(后端转发)
      if (this.$parent.row.cascadeId) {
        let cascadeId = this.$parent.row.cascadeId;
        let superiorToken = this.$parent.row.superiorToken;
        let interfaceName = governanceevaluation.queryEvaluationDeviceResult;
        await proxyInterfacefunc(params, interfaceName, cascadeId, superiorToken).then((res) => {
          const datas = res.data.data;
          this.reasonTableData = datas.entities;
          this.reasonPage.totalCount = datas.total;
          this.reasonLoading = false;
        });
        return;
      }
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    exportData: require('@/views/governanceevaluation/evaluationoverview/components/basics/export-data.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    uiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
  },
};
</script>
