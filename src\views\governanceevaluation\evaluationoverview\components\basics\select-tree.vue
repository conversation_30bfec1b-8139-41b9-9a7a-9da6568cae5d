<template>
  <div>
    <Collapse v-model="value1" class="dropdown">
      <Panel class="dropdown-item" :name="index.toString()" v-for="(item, index) in childList" :key="index">
        <span>{{ item.code }}</span>
        <div slot="content">
          <div class="">
            <div class="it-content mt-sm" v-for="(it, inde) in item.children" :key="inde">
              <div class="it-inde">{{ it.code }} {{ it.name }}</div>
            </div>
          </div>
        </div>
      </Panel>
    </Collapse>
    <div class="not-available" v-if="childList.length == 0">暂无数据</div>
  </div>
</template>
<script>
export default {
  name: 'tree',

  data() {
    return {
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      value1: '0',
      treeList: [],
      childList: [],
    };
  },
  mounted() {},
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    treeData: {
      handler(val) {
        if (val.length != 0) {
          this.childList = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.dropdown {
  // width: 300px;
  // height: 300px;
  overflow-y: auto;
}
.dropdown-item {
  margin-bottom: 10px;
}
@{_deep} .ivu-collapse {
  border: none;
  background: none !important;
}
@{_deep}.ivu-collapse-header {
  border: none;
  color: #ffffff;
  background: #073167 !important;
  i {
    float: right;
    line-height: 38px;
  }
}
@{_deep}.ivu-collapse-item {
  border: none;
  background: #073167 !important;
  margin-bottom: 10px;
}
@{_deep} .ivu-collapse-content {
  color: #bee2fb;
  background: #082249;
}
.not-available {
  color: #8797ac;
  font-size: 14px;
  text-align: center;
  position: absolute;
  top: 40%;
  left: 45%;
}
</style>
