<template>
  <div class="my-workorder-container">
    <div class="search-bar">
      <div class="flex-aic">
        <underline-menu
          v-model="searchData.queryType"
          :data="assignList"
          @on-change="onChangeUnderlineMenu"
        ></underline-menu>
      </div>
      <ui-label class="inline mr-lg" label="创建时间：">
        <ui-radio-time :time-radio="timeRadio" @selectTime="selectTime"></ui-radio-time>
      </ui-label>
    </div>
    <ui-switch-drop
      v-if="stateOptions.length"
      class="ui-switch-tab mt-sm mb-sm"
      v-model="queryConditionStatus"
      :tab-list="stateOptions"
      @changeTab="onChangeSwitchDrop"
    >
    </ui-switch-drop>
    <div class="component-container auto-fill">
      <all-work-order
        ref="workOrder"
        :key="searchData.type"
        :common-search-data="searchData"
        :query-condition-status="queryConditionStatus"
        :statistics-detail="statisticsDetail"
        @on-change-table-data="onChangeTableData"
      >
      </all-work-order>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { stateOptionsMap, MYORDER } from '../util/enum.js';
export default {
  name: 'myworkorder',
  props: {},
  data() {
    return {
      queryConditionStatus: 'qb',
      searchData: {
        type: MYORDER, //拆分出来后需要写死类型=>工单类型（1 全部工单，2 我的工单）
        orgCode: '',
        queryType: '3',
        beginTime: '',
        endTime: '',
      },
      timeRadio: {
        title: '',
        value: 'all',
        timeList: [
          { label: '今日', value: 'today' },
          { label: '本周', value: 'thisWeek' },
          { label: '本月', value: 'thisMonth' },
          { label: '今年', value: 'thisYear' },
          { label: '全部', value: 'all' },
          { label: '自定义', value: 'defined' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
      },
      assignList: [
        /**
         * @ApiModelProperty("查询类型（为空查询所有，1 单位创建，2 指派给单位，3 当前指派给我，4 我创建的，5 我签收的，6 我处理的，7 我关闭的）")
         * private Integer queryType;
         */
        { code: '3', label: '当前指派给我' },
        { code: '4', label: '我创建的' },
        { code: '5', label: '我签收的' },
        { code: '6', label: '我处理的' },
        { code: '7', label: '我关闭的' },
      ],

      stateOptions: [],
      statisticsDetail: null,
    };
  },
  async mounted() {
    this.stateOptions = stateOptionsMap[this.searchData.queryType];
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (Object.keys(to.params).length) {
        vm.receiveParamsFromOtherPages(to.params);
      } else if (Object.keys(to.query).length) {
        vm.receiveParamsFromOtherPages(to.query);
      } else {
        vm.$refs.workOrder.search();
      }
    });
  },
  methods: {
    selectedOrgTree(area) {
      this.searchData.orgCode = area.orgCode;
      this.$refs.workOrder.search();
    },
    selectTime(timeRadio) {
      this.searchData.beginTime = timeRadio.startTime;
      this.searchData.endTime = timeRadio.endTime;
      this.$refs.workOrder.search();
    },
    async onChangeUnderlineMenu(val) {
      this.stateOptions = stateOptionsMap[val];
      this.queryConditionStatus = 'qb';
      await this.$nextTick();
      this.$refs.workOrder.search();
    },
    async onChangeSwitchDrop(val) {
      try {
        this.queryConditionStatus = val;
        await this.$nextTick();
        this.$refs.workOrder.search();
      } catch (e) {
        console.log(e);
      }
    },
    async onChangeTableData({ total }) {
      let index = this.stateOptions.findIndex((item) => item.value === this.queryConditionStatus);
      if (index !== -1) {
        this.$set(this.stateOptions[index], 'total', total);
      }
    },
    //接收从其他页面过来的数据
    receiveParamsFromOtherPages(params) {
      if (!params.purpose) {
        return;
      }
      //所有路由跳转的操作
      switch (params.purpose) {
        case 'msgview':
          this.changeViewSearch(params);
          break;
        case 'openOrderDetailModalById':
          this.openOrderDetailModal(params);
          break;
      }
    },
    //改变搜索条件
    changeViewSearch(params) {
      let { queryType, queryConditionStatus } = params;
      this.searchData.queryType = queryType;
      this.stateOptions = stateOptionsMap[this.searchData.queryType];
      this.queryConditionStatus = queryConditionStatus;
      this.$refs.workOrder.search();
    },
    // 查看工单详情
    async openOrderDetailModal(params) {
      if (!this.$refs.workOrder) {
        return;
      }
      this.$refs.workOrder.editViewAction = { row: { id: params.detailId }, type: 'view' };
      this.$refs.workOrder.editViewOrderShow = true;
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    AllWorkOrder: require('@/views/disposalfeedback/governanceorder/module/all-work-order.vue').default,
    UiSwitchDrop: require('@/components/ui-switch-drop/ui-switch-drop.vue').default,
    UnderlineMenu: require('@/components/underline-menu').default,
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.my-workorder-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background: var(--bg-content);
  padding: 10px 20px 0 20px;

  display: flex;
  flex-direction: column;
  .search-bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    border-radius: 4px;
    background: var(--bg-navigation);

    @{_deep} .underline-menu-wrapper {
      background: transparent;
      width: auto;
      .tab {
        font-size: 14px;
      }
      .tab:before {
        content: none;
      }
    }
  }
  @{_deep} .custorm-tree-node {
    height: 26px;
    line-height: 26px;
  }
}
</style>
