<template>
  <div class="container">
    <div class="person">
      <!-- 查询 -->
      <Search
        v-show="!$route.query.noSearch"
        type="video"
        @searchForm="searchForm"
        :searchText="'高级检索'"
      />
      <div class="card-content">
        <div v-for="(item, index) in dataList" :key="index" class="card-item">
          <UiListCard
            type="vehicle"
            :showBar="false"
            :data="item"
            @archivesDetailHandle="archivesDetailHandle(item)"
            @collection="init"
            @on-search-image="onSearchImage"
            @on-archive="onArchive"
            @on-control="onControl"
          />
        </div>
        <ui-empty v-if="dataList.length < 1 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <!-- <ui-page :current="pageForm.pageNumber" :total="13" :page-size="pageForm.pageSize"></ui-page> -->
      <!-- 分页 -->
      <ui-page
        :showElevator="true"
        countTotal
        :current="pageInfo.pageNumber"
        :total="total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <search-image ref="searchImage" @on-submit="onSubmit"></search-image>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import Search from "@/views/holographic-archives/one-vehicle-one-archives/components/search.vue";
import UiListCard from "@/components/ui-list-card";
import {
  getVehicleBaseInfoByplateNo,
  queryVehicleList,
} from "@/api/vehicleArchives";
import { picturePick } from "@/api/wisdom-cloud-search";
import SearchImage from "@/components/search-image/index.vue";
import searchPictures from "@/views/wisdom-cloud-search/cloud-default-page/components/search-pictures.vue";
export default {
  components: { SearchImage, Search, UiListCard, searchPictures },
  props: {},
  data() {
    return {
      loading: false,
      dataList: [],
      pageForm: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
      keyWords: "", // 关键字，从全景智搜跳转过来
    };
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      picData: "common/getWisdomCloudSearchData",
    }),
  },
  async created() {
    this.keyWords = this.$route.query.keyWords || "";
    await this.getDictData();
    this.init();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("common", ["setWisdomCloudSearchData"]),
    ...mapMutations("control", ["setVehicleControl"]),
    onControl(row) {
      this.setVehicleControl(row);
      this.$router.push("/target-control/control-task/addVehicle");
    },
    onArchive(row) {
      getVehicleBaseInfoByplateNo(row.plateNo).then((res) => {
        if (res.data.archiveNo) {
          const { href } = this.$router.resolve({
            name: "vehicle-archive",
            query: {
              archiveNo: JSON.stringify(res.data.archiveNo),
              plateNo: JSON.stringify(row.plateNo),
              source: "car",
            },
          });
          window.open(href, "_blank");
        } else {
          this.$Message.error("尚未查询到该辆车的档案信息");
        }
      });
    },
    /**
     * @description: 查询列表
     */
    init() {
      this.loading = true;
      var param = Object.assign(this.pageInfo, this.pageForm);
      var param = {
        ...this.pageInfo,
        ...this.pageForm,
        searchValue: this.keyWords || undefined,
      };
      delete param.bodyTypeobj;
      var labelIds = [];
      if (param.labelIds && param.labelIds.length > 0) {
        param.labelIds.forEach((item) => {
          if (item && item != undefined) {
            labelIds.push(item.id);
          }
        });
        param.labelIds = labelIds;
      }
      queryVehicleList(param)
        .then((res) => {
          this.dataList = res.data.entities;
          this.total = res.data.total;
          // 重新获取页码后回到顶部
          document.querySelector(".card-content").scrollTop = 0;
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 档案详情
    archivesDetailHandle(row) {
      let query = {
        archiveNo: JSON.stringify(row.archiveNo),
        plateNo: JSON.stringify(row.plateNo),
        source: "car",
        idcardNo: row.idcardNo,
      };
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
    async onSearchImage(src) {
      let picData = {
        algorithmType: "2", //1人脸 2 车辆 3 人体 4 非机动车
        similarity: 75, // 相似度
      };
      this.$refs.searchImage.searchImage(picData, src);
    },
    onSubmit({ algorithmType, similarity, urlList }) {
      let params = {
        keyWords: "",
        algorithmType: algorithmType,
        urlList: [urlList],
      };
      this.$router.push({
        path: "/wisdom-cloud-search/cloud-default-page",
        query: {
          params: params,
          type: 2,
        },
      });
    },
    // 查询
    searchForm(form) {
      this.keyWords = ""; // 只要用户点击了查询，则之后的查询不再携带keyWords参数
      this.pageInfo.pageNumber = 1;
      this.pageForm = form;
      this.init();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
  },
};
</script>
<style lang="less" scoped>
// @import "~@/views/holographic-archives/style/page-hide.less";
.person {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
}
</style>
