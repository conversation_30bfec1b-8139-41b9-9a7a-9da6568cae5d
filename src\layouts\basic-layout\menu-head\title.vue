<template>
  <span class="i-layout-menu-head-title">
    <span
      v-if="(item.meta.icon || item.custom || item.img) && !hideIcon"
      class="i-layout-menu-head-title-icon"
    >
      <i :class="item.meta.icon" class="router-icon iconfontconfigure" />
    </span>
    <span class="i-layout-menu-head-title-text">{{ item.meta.title }}</span>
  </span>
</template>
<script>
/**
 * 该组件除了 Menu，也被 Breadcrumb 使用过
 * */
import tTitle from "../mixins/translate-title";

export default {
  name: `iMenuHeadTitle`,
  mixins: [tTitle],
  props: {
    item: {
      type: Object,
      default() {
        return {};
      },
    },
    hideIcon: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
