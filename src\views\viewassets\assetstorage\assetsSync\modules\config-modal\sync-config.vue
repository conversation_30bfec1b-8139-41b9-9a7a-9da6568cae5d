<template>
  <div class="sync-config">
    <ui-label label="同步方式：" class="mb-lg">
      <RadioGroup v-model="formData.syncType">
        <Radio class="mr-lg" label="1">自动</Radio>
        <Radio label="2">手动</Radio>
      </RadioGroup>
    </ui-label>
    <ui-label label="同步时间：" v-if="formData.syncType === '1'">
      <plan-time-picker
        class="inline"
        ref="planTimePicker"
        :time-picker-data="timePickerData"
        @handleUpatePlanData="handleUpatePlanData"
      ></plan-time-picker>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    defaultParams: {},
  },
  data() {
    return {
      formData: {
        syncType: '2',
        cronType: '1',
        cronData: [],
        timePoints: [],
      },
      timePickerData: {
        cronType: '1',
        cronData: [],
        timePoints: [],
      },
    };
  },
  created() {},
  methods: {
    handleUpatePlanData(val) {
      this.formData.cronType = val.cronType;
      this.formData.cronData = val.cronData;
      if (val.timePoints) {
        this.formData.timePoints = val.timePoints;
      }
    },
  },
  watch: {
    defaultParams: {
      handler(val) {
        if (val) {
          Object.keys(this.formData).forEach((key) => {
            if (Object.prototype.hasOwnProperty.call(this.formData, key)) {
              this.formData[key] = val[key];
            }
          });
          this.timePickerData = {
            cronType: this.formData.cronType || '1',
            cronData: this.formData.cronData || [],
            timePoints: this.formData.timePoints || [],
          };
        }
      },
      immediate: true,
    },
    formData: {
      handler(val) {
        this.$emit('updateParams', val);
      },
      deep: true,
    },
  },
  components: {
    PlanTimePicker:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/plan-time-picker')
        .default,
  },
};
</script>
<style lang="less" scoped>
.sync-config {
  min-height: 300px;
}
.plan-time-picker {
  width: auto;
  @{_deep}.tag-view {
    li {
      height: auto;
    }
  }
}
</style>
