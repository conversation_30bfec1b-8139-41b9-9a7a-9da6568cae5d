<template>
  <ui-modal
    v-model="visible"
    title="下载申请"
    :r-width="600"
    @onOk="comfirmHandle"
    :mask="false"
  >
    <Form ref="form" :model="form" :rules="ruleForm" class="form downloadForm">
      <FormItem label="申请书" :label-width="80" prop="applyUrl">
        <div class="essential-information-img">
          <div class="avatar-img card-border-color">
            <img
              v-if="!form.applyUrl"
              src="@/assets/img/empty-page/null_img_icon.png"
              class="avatar-null-img"
              alt=""
            />
            <template v-else>
              <img v-if="isEdit" v-viewer :src="form.applyUrl" alt />
              <img v-else v-viewer :src="form.applyUrl" alt />
            </template>
          </div>
          <UploadImg
            choosed
            ref="uploadImg"
            :deleted="true"
            :isEdit="isEdit"
            :multipleNum="10"
            :choosedIndex="avatarIndex"
            :defaultList="photoUrlList"
            class="upload-img"
            @on-choose="chooseHandle"
          />
        </div>
      </FormItem>
      <FormItem label="申请理由" :label-width="80" prop="applyReason">
        <Input
          type="textarea"
          v-model="form.applyReason"
          placeholder="请输入"
        />
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import UploadImg from "@/components/ui-upload-img-static-library";
import { mapGetters } from "vuex";
import { applyAdd } from "@/api/videoApply.js";
export default {
  components: {
    UploadImg,
  },
  data() {
    return {
      visible: false,
      form: {
        applyReason: "",
        applyUrl: "",
      },
      ruleForm: {
        applyReason: [{ required: true, message: "请输入", trigger: "blur" }],
        applyUrl: [
          { required: true, message: "请上传申请书", trigger: "blur" },
        ],
      },
      isEdit: true, //false-新增, true-编辑
      avatarIndex: "",
      photoUrlList: [],
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  methods: {
    // 初始化
    show(item) {
      this.visible = true;
      this.$nextTick(() => {
        this.photoUrlList = [];
        this.$refs.form.resetFields();
        this.form = {
          applyReason: "",
          applyUrl: "",
          ...item,
        };
        console.log(this.form);
      });
    },

    // 确认提交
    comfirmHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            applyReason: this.form.applyReason,
            applyUrl: this.form.applyUrl,
            deviceId: this.form.deviceId,
            deviceName: this.form.stream.title || "",
            endTime: this.$dayjs(this.form.EndTime).format(
              "YYYY-MM-DD HH:mm:ss"
            ),
            orgCode: this.userInfo.orgVoList[0].orgCode,
            orgName: this.userInfo.orgVoList[0].orgName,
            startTime: this.$dayjs(this.form.StartTime).format(
              "YYYY-MM-DD HH:mm:ss"
            ),
            userId: this.userInfo.id,
            userName: this.userInfo.name,
            stream: JSON.stringify(this.form.downloadUrls),
          };
          applyAdd({
            ...params,
          }).then((res) => {
            this.visible = false;
            if (res.code == 200) {
              this.$Message.success("申请成功");
            } else {
              this.$Message.error(res.msg);
            }
          });
        }
      });
    },
    // 选择证件照
    chooseHandle(item) {
      this.form.applyUrl = item;
    },
  },
};
</script>
<style lang="less" scoped>
.dialog-input {
  width: 330px;
}
/deep/ .ivu-form-item-content {
  line-height: 11px;
}
.downloadForm {
  margin: 0 20px;
}
.essential-information-img {
  // width: 240px;
  display: flex;
  flex-direction: column;
  .avatar-img {
    // width: 240px;
    // height: 320px;
    height: 40vh;
    display: flex;
    justify-content: center;
    border: 1px solid #fff;
    & > img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      cursor: pointer;
    }
    .avatar-null-img {
      cursor: unset;
    }
  }
  .upload-img {
    margin-top: 10px;
    justify-content: flex-start;
    /deep/ .upload-item {
      width: 54px;
      height: 54px;
      .ivu-icon-ios-add {
        font-size: 30px;
        font-weight: bold;
      }
      .upload-text {
        line-height: 11px;
        display: none;
      }
    }
  }
}
</style>
