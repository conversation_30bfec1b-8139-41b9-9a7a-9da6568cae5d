<template>
  <ui-modal v-model="visible" title="导出设备信息" :styles="styles">
    <div class="export-data">
      <Form ref="modalData" :model="searchData" class="form-content" :label-width="160">
        <FormItem label="组织机构">
          <select-organization-tree-checkall
            v-if="visible"
            :treeData="treeData"
            @getSelectTree="checkTree"
          ></select-organization-tree-checkall>
        </FormItem>
        <FormItem label="待导出组织机构">
          <RadioGroup v-model="searchData.multiSheet">
            <Radio label="false">导出设备总表</Radio>
            <Radio label="true">按异常原因导出分表</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="primary" @click="exportAdd" :loading="exportLoading" class="plr-30">确 定</Button>
      <Button @click="visible = false" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>

<style lang="less" scoped>
.export-data {
}
@{_deep} .ivu-modal {
  width: 520px;
  .ivu-modal-header {
    margin-bottom: 0;
  }
  &-body {
    padding: 70px 20px 140px !important;
  }
  .export-content {
    height: 420px;
    overflow: auto;
    position: relative;
  }
}
@{_deep} .select-organization-tree {
  width: 90% !important;
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { proxyInterfacefunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  name: 'export-data',
  props: {
    // 级联清单单独处理
    cascadeId: {},
    superiorToken: {},
    exportLoading: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      visible: false,
      styles: { width: '4rem' },
      searchData: { multiSheet: 'false', orgCodes: [] },
      treeData: [],
      defaultCheckedKeys: [],
      treeList: [],
    };
  },
  mounted() {},

  methods: {
    init(val) {
      this.visible = true;
      this.getOrg(val);
    },
    hide() {
      this.visible = false;
      this.searchData.multiSheet = 'false';
      this.searchData.orgCodes = [];
      this.treeData = [];
    },
    async getOrg(val) {
      // 级联清单特殊替换处理接口(后端转发)
      if (this.cascadeId) {
        let interfaceName = evaluationoverview.getCurrentUserSysOrgListByTaskId;
        await proxyInterfacefunc({ resultId: val }, interfaceName, this.cascadeId, this.superiorToken, 'get').then(
          (res) => {
            let tempArr = this.$util.common.deepCopy(res.data.data);
            this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
          },
        );
        return;
      }
      try {
        let res = await this.$http.get(evaluationoverview.getCurrentUserSysOrgListByTaskId, {
          params: { batchId: val },
        });
        let tempArr = this.$util.common.deepCopy(res.data.data);
        this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
      } catch (error) {
        console.log(error);
      }
    },
    checkTree(orgCodes) {
      this.searchData.orgCodes = orgCodes;
    },
    exportAdd() {
      if (this.searchData.orgCodes.length == 0) {
        this.$Message.error('请选择组织机构');
        return;
      }
      this.$emit('exportAdd', this.searchData);
    },
  },
  watch: {},
  components: {
    SelectOrganizationTreeCheckall: require('@/api-components/select-organization-tree-checkall.vue').default,
  },
};
</script>
