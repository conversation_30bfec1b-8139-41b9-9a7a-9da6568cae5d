<template>
  <div class="body-container" v-ui-loading="{ loading, tableData }">
    <draw-echarts
      class="echarts"
      :echart-style="echartStyle"
      :echart-option="echartOption"
      ref="governChart"
      @echartClick="echartClickJump"
    ></draw-echarts>
    <span class="next-echart">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollAllRight"></i>
    </span>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'zdr-data-quality',
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    tableData: {},
    loading: {},
  },
  data() {
    return {
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      evaluationIndexResultData: [],
      filterEvaluationIndexResultData: [],
      echartOption: {},
      piePageNum: 0, // 第一页饼图
      totalPage: 0, // 总共多少页饼图
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  created() {
    window.addEventListener('resize', this.nextPage);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.nextPage);
  },
  activated() {
    this.nextPage();
  },
  watch: {
    tableData: {
      handler(val) {
        this.evaluationIndexResultData = val || [];
        if (val) {
          this.initList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    echartClickJump(params) {
      this.$emit('on-jump', params.data.originData);
    },
    initList() {
      this.filterEvaluationIndexResultData = this.evaluationIndexResultData.filter((item) => item.indexModule === '5');
      this.totalPage = Math.ceil(this.filterEvaluationIndexResultData.length / 3);
      this.initEchartOption();
    },
    initEchartOption() {
      this.nextPage();
      // this.echartOption = this.$util.doEcharts.echartZDRDataQuality({ data: this.filterEvaluationIndexResultData})
    },
    scrollAllRight() {
      this.piePageNum++;
      this.piePageNum >= this.totalPage ? (this.piePageNum = 0) : null;
      this.nextPage();
    },
    nextPage() {
      let list = this.filterEvaluationIndexResultData.slice(this.piePageNum * 3, (this.piePageNum + 1) * 3);
      let gridWidth = this.$util.common.fontSize(120);
      this.$nextTick(() => {
        if (!this.$refs.governChart) return;
        gridWidth = this.$refs.governChart.$el.clientWidth / 3 - this.$util.common.fontSize(20);
        let gridHeight = this.$refs.governChart.$el.clientHeight;
        this.echartOption = this.$util.doEcharts.echartZDRDataQuality(list, gridWidth, gridHeight);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .echarts {
    height: 100% !important;
    width: 100% !important;
    overflow: hidden;
  }
}
</style>
