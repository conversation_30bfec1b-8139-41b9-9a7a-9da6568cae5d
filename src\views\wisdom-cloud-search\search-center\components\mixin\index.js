/*
    * @FileDescription: 时间处理
    * @Author: H
    * @Date: 2023/08/23
    * @LastEditors: 
    * @LastEditTime: 
*/
import { getConfigDate } from '@/util/modules/common';
import { mapGetters } from 'vuex';
export const myMixins = {
    data() {
        return {
            
        }
    },
    computed: {
        ...mapGetters({
            userInfo: "userInfo", // 用户信息
            classifySearchData: 'common/getClassifySearchData', //查询数据
        }),
        filter() {
            return this.userInfo.username == 'admin' ? false : true;
        }
    },
    mounted() {

    },
    created() {
    },
    methods: {
        // 设备和时间互传
        serachDeviceTime(list, timeSlot, startDate, endDate) {
            this.$refs.searchBar.queryParam.selectDeviceList = list;
            if(timeSlot == '自定义') {
                this.$refs.searchBar.queryParam.timeSlot = timeSlot;
                this.$refs.searchBar.queryParam.startDate= startDate;
                this.$refs.searchBar.queryParam.endDate= endDate;
                // this.$refs.searchBar.queryParam.timeSlotArr = [startDate, endDate];
            }else{
                this.$refs.searchBar.queryParam.timeSlot = timeSlot;
            }
            this.$nextTick(() => {
                this.queryList()
            })
        },
        // 时间排序(普通)
        handleSort(val) {
            this.queryParam.sortField = val;
            this.timeUpDown = !this.timeUpDown
            this.queryParam.order = this.timeUpDown ? 'asc' : 'desc';
            this.queryList()
        },
        // 排序(高级)
        handleAdvancedSort(val){
            if(val != this.queryParam.sortField) {
                this.queryParam.sortField = val;
                if (val == 'similarity'){
                    this.queryParam.order = this.similUpDown ? 'asc' : 'desc';
                } else {
                    this.queryParam.order = this.timeUpDown ? 'asc' : 'desc';
                }
            }else{
                this.queryParam.sortField = val;
                if(val == 'similarity') {
                    this.similUpDown = !this.similUpDown
                    this.queryParam.order = this.similUpDown ? 'asc' : 'desc';
                }else{
                    this.timeUpDown = !this.timeUpDown
                    this.queryParam.order = this.timeUpDown ? 'asc' : 'desc';
                } 
            }
            this.getDataList()
        },
        compareTime(){
            if(this.queryParam.startDate == '' ){
                this.$Message.warning('开始时间不能为空！')
                return true
            }
            if(this.queryParam.endDate == '') {
                this.$Message.warning('结束时间不能为空！')
                return true
            }
            if(this.queryParam.startDate > this.queryParam.endDate){
                this.$Message.warning('开始时间不能大于结束时间！')
                return true
            }
        },
         // 计算时间
         dispTime() {
            // 抓拍时段时间处理
            let { startDate, endDate } = this.timeTransition(this.queryParam.timeSlot, this.queryParam.timeSlotArr);
            this.queryParam.startDate = startDate;
            this.queryParam.endDate = endDate;
        },
        dispTimes() {
            // 抓拍时段时间处理
            let { startDate, endDate } = this.timeTransition(this.queryParam.timeSlot);
            this.queryParam.startDate = startDate;
            this.queryParam.endDate = endDate;
        },
        /**
         * 根据时段（中文）获取相对应的时间时（可拓展）
         * @param {String} 
         * @param {Number} 
         * @param {Number} 
         * @returns {Number}
         */
        timeTransition(timeText, timeArr) {
            let startDate = '', endDate = '';
            switch (timeText) {
                case '近一天':
                    var arr = getConfigDate(-1)
                    startDate = arr[1] + ' 00:00:00'
                    // endDate = arr[1] + ' 23:59:59'
                    endDate = this.$dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
                    break
                case '近三天':
                    var arr = getConfigDate(-2)
                    startDate = arr[0] + ' 00:00:00'
                    // endDate = arr[1] + ' 23:59:59'
                    endDate = this.$dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
                    break
                case '近一周':
                    var arr = getConfigDate(-6)
                    startDate = arr[0] + ' 00:00:00'
                    // endDate = arr[1] + ' 23:59:59'
                    endDate = this.$dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
                    break
                case '自定义':
                    let start = '', end = '';
                    if(timeArr && timeArr[0] != ''){
                        start = timeArr[0];
                        end = timeArr[1];
                    }else{
                        start = new Date();
                        end = new Date();
                    }
                    startDate = this.$dayjs(start).format('YYYY-MM-DD HH:mm:ss')
                    endDate = this.$dayjs(end).format('YYYY-MM-DD HH:mm:ss')
                    break
                default:
                    break
            }
            return {startDate, endDate}
        },
    }
}