<template>
  <!-- 视频流质量合格率 -->
  <div class="videoAccess" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="true" :isIconBg="true"></statistics>
      <ranking-info :rank-list="rankList"></ranking-info>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-yichangshujuliebiao f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <Button type="primary" class="btn_search" :loading="exportLoading" @click="getExport">
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <div class="search-wrapper">
        <ui-label class="mr-lg" label="设备编码" width="70">
          <Input class="input-width" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label label="设备名称" :width="70" class="mr-lg">
          <Input class="input-width" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
        </ui-label>
        <ui-label label="检测结果" :width="70" class="mr-lg">
          <Select v-model="searchData.qualified" clearable placeholder="请选择检测结果" class="width-input">
            <Option value="1">合格</Option>
            <Option value="2">不合格</Option>
            <Option value="3">无法检测</Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="fl" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button type="default" @click="reast"> 重置 </Button>
        </ui-label>
      </div>
      <div class="list">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <!--          <template #tagNames="{ row }">-->
          <!--            <tags-more :tag-list="row.tagList || []"></tags-more>-->
          <!--          </template>-->
          <template #delaySipMillSecond="{ row }">
            <span>
              {{ !row.delaySipMillSecond ? '--' : row.delaySipMillSecond }}
            </span>
          </template>
          <template #delayStreamMillSecond="{ row }">
            <span>
              {{ !row.delayStreamMillSecond ? '--' : row.delayStreamMillSecond }}
            </span>
          </template>
          <template #delayIdrMillSecond="{ row }">
            <span>
              {{ !row.delayIdrMillSecond ? '--' : row.delayIdrMillSecond }}
            </span>
          </template>
          <template #videoStartTime="{ row }">
            <span>
              {{ !row.videoStartTime ? '--' : row.videoStartTime }}
            </span>
          </template>
          <template #phyStatusText="{ row }">
            <span :style="{ color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19' }">
              {{ row.phyStatusText }}
            </span>
          </template>
          <!--          &lt;!&ndash; 视频信号 &ndash;&gt;-->
          <!--          <template #videoSignal="{ row }">-->
          <!--            <span-->
          <!--              :class="{-->
          <!--                color_qualified: row.videoSignal === '1',-->
          <!--                color_unqualified: row.videoSignal === '2',-->
          <!--              }"-->
          <!--            >-->
          <!--              {{ row.videoSignal === '1' ? '正常' : row.videoSignal === '2' ? '异常' : '&#45;&#45;' }}-->
          <!--            </span>-->
          <!--          </template>-->
          <!--          &lt;!&ndash; 视频亮度 &ndash;&gt;-->
          <!--          <template #videoBrightness="{ row }">-->
          <!--            <span-->
          <!--              :class="{-->
          <!--                color_qualified: row.videoBrightness === '1',-->
          <!--                color_unqualified: row.videoBrightness === '2',-->
          <!--              }"-->
          <!--            >-->
          <!--              {{-->
          <!--                row.videoBrightness === '1' ? '正常' : row.videoBrightness === '2' ? '异常' : '&#45;&#45;'-->
          <!--              }}-->
          <!--            </span>-->
          <!--          </template>-->
          <!--          &lt;!&ndash; 视频偏色 &ndash;&gt;-->
          <!--          <template #videoColorCast="{ row }">-->
          <!--            <span-->
          <!--              :class="{-->
          <!--                color_qualified: row.videoColorCast === '1',-->
          <!--                color_unqualified: row.videoColorCast === '2',-->
          <!--              }"-->
          <!--            >-->
          <!--              {{ row.videoColorCast === '1' ? '正常' : row.videoColorCast === '2' ? '异常' : '&#45;&#45;' }}-->
          <!--            </span>-->
          <!--          </template>-->
          <!--          &lt;!&ndash; 视频清晰 &ndash;&gt;-->
          <!--          <template #videoClear="{ row }">-->
          <!--            <span-->
          <!--              :class="{-->
          <!--                color_qualified: row.videoClear === '1',-->
          <!--                color_unqualified: row.videoClear === '2',-->
          <!--              }"-->
          <!--            >-->
          <!--              {{ row.videoClear === '1' ? '正常' : row.videoClear === '2' ? '异常' : '&#45;&#45;' }}-->
          <!--            </span>-->
          <!--          </template>-->
          <!--          &lt;!&ndash; 视频遮挡 &ndash;&gt;-->
          <!--          <template #videoOcclusion="{ row }">-->
          <!--            <span-->
          <!--              :class="{-->
          <!--                color_qualified: row.videoOcclusion === '1',-->
          <!--                color_unqualified: row.videoOcclusion === '2',-->
          <!--              }"-->
          <!--            >-->
          <!--              {{ row.videoOcclusion === '1' ? '正常' : row.videoOcclusion === '2' ? '异常' : '&#45;&#45;' }}-->
          <!--            </span>-->
          <!--          </template>-->
          <template #qualified="{ row }">
            <span
              class="check-status"
              :class="[
                row.qualified === '1' ? 'bg-b77a2a' : '',
                row.qualified === '2' ? 'bg-17a8a8' : '',
                row.qualified === '3' ? 'bg-D66418' : '',
              ]"
            >
              {{ handleCheckStatus(row.qualified) }}
            </span>
            <span
              v-permission="{
                route: $route.name,
                permission: 'artificialreviewr',
              }"
              class="ml-sm"
              v-if="row.dataMode === '3'"
            >
              (人工)
            </span>
          </template>
          <template #option="{ row }">
            <div class="boxCenter">
              <ui-btn-tip
                icon="icon-bofangshipin"
                content="播放视频"
                @click.native="clickRow(row)"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip
                icon="icon-chakanjietu"
                content="查看截图"
                disabled
                v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
                class="mr-sm"
              ></ui-btn-tip>
              <ui-btn-tip
                v-else
                icon="icon-chakanjietu"
                content="查看截图"
                class="mr-sm"
                @click.native="showResult(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                icon="icon-rengongfujian"
                content="人工复核"
                class="mr-sm"
                @click.native="artificialReview(row)"
                v-permission="{
                  route: $route.name,
                  permission: 'artificialreviewr',
                }"
              ></ui-btn-tip>
              <ui-btn-tip
                class="mr-sm"
                icon="icon-tianjiabiaoqian"
                content="添加标签"
                @click.native="addTags(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
    <resultModel ref="result"></resultModel>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="margin-top: -15px">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
    <!-- 人工复核 -->
    <ArtificialReview
      ref="artificialReview"
      title="人工复核"
      :checkMsgVisible="false"
      :styles="{ width: !!artificialData.screenShot ? '6rem' : '3rem' }"
    >
      <div slot="custormContent" class="custormContent mt-md mb-lg">
        <div v-if="artificialData.screenShot" class="custormContent-left">
          <ui-image :src="artificialData.screenShot" class="image" />
        </div>
        <div class="custormContent-right ml-lg">
          <ui-label class="block" label="人工复核:" :width="80">
            <RadioGroup v-model="artificialData.qualified">
              <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">{{ item.key }}</Radio>
            </RadioGroup>
          </ui-label>
          <CheckboxGroup v-if="artificialData.qualified !== '1'" class="ml-80 mt-lg" v-model="artificialData.errorCode">
            <Checkbox v-for="errItem in errCodeList" :key="errItem.key" :label="errItem.key" class="mb-md mr-lg">{{
              errItem.value
            }}</Checkbox>
          </CheckboxGroup>
          <ui-label class="block" label="" :width="80">
            <Input
              type="textarea"
              class="desc mt-md"
              v-model="artificialData.reason"
              placeholder="请输入备注信息"
              :rows="5"
              :maxlength="256"
            ></Input>
          </ui-label>
        </div>
      </div>
      <div slot="footer" class="footer">
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </div>
    </ArtificialReview>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>
<script>
import vedio from '@/config/api/vedio-threm';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [downLoadTips],
  name: 'basic-information',
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      videoStyles: {
        width: '5rem',
      },
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      taskType: '',
      statisticsList: [
        {
          name: '视频监控设备总数',
          value: 0,
          icon: 'icon-shipinjiankongshebeizongshu-011',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'deviceCount',
        },
        {
          name: '实际测设备数量',
          value: 0,
          icon: 'icon-shijiceshebeishuliang-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'evaluatingCount',
        },
        {
          name: '视频质量合格设备',
          value: 0,
          icon: 'icon-shishishipinhegeshebei-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
          key: 'evaluatingSuccessCount',
        },
        {
          name: '视频质量异常设备',
          value: 0,
          icon: 'icon-shishishipinyichangshebei-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'notPassAmout',
        },
        {
          name: '视频质量合格率',
          value: 0,
          icon: 'icon-wufajianceshebeishuliang-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage',
          key: 'resultValueFormat',
        },
        // {
        //   name: '视频遮挡',
        //   value: 0,
        //   icon: 'icon-zaixianshuai-01',
        //   iconColor: 'icon-bg6',
        //   liBg: 'li-bg6',
        //   type: 'percentage',
        //   key: 'videoOcclusionAmout',
        // },
        // {
        //   name: '视频信号异常',
        //   value: 0,
        //   icon: 'icon-wanhaoshuai-01',
        //   iconColor: 'icon-bg8',
        //   liBg: 'li-bg8',
        //   type: 'percentage',
        //   key: 'videoSignalAmout',
        // },
        // {
        //   name: '视频亮度异常',
        //   value: 0,
        //   icon: 'icon-keyongshuai-01',
        //   iconColor: 'icon-bg1',
        //   liBg: 'li-bg1',
        //   type: 'percentage',
        //   key: 'videoBrightnessAmout',
        // },
        // {
        //   name: '视频偏色',
        //   value: 0,
        //   icon: 'icon-zhongdianputongshishishipinketiaoyueshuai-01',
        //   iconColor: 'icon-bg7',
        //   liBg: 'li-bg7',
        //   type: 'percentage',
        //   // textColor: 'color5',
        //   key: 'videoColorCastAmout',
        // },
        // {
        //   name: '视频清晰度异常',
        //   value: 0,
        //   icon: 'icon-zhongdianputongshishishipinketiaoyueshuai-01',
        //   iconColor: 'icon-bg8',
        //   liBg: 'li-bg8',
        //   type: 'percentage',
        //   // textColor: 'color5',
        //   key: 'videoCleanAmout',
        // },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '设备编码',
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: '设备名称',
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        {
          title: '监控点位类型',
          key: 'sbdwlxText',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `设备状态`,
          slot: 'phyStatusText',
          width: 120,
        },
        {
          title: `检测结果`,
          key: 'qualified',
          slot: 'qualified',
          width: 120,
          // renderHeader: (h, params) => {
          //   return h('div', [
          //     h('span', '检测结果'),
          //     h(
          //       'Tooltip',
          //       {
          //         props: {
          //           transfer: true,
          //           placement: 'bottom',
          //         },
          //         style: { verticalAlign: 'middle' },
          //       },
          //       [
          //         h('i', {
          //           class: 'icon-font icon-wenhao ml-xs f-12',
          //           style: {
          //             width: '13px',
          //             verticalAlign: 'top',
          //             color: 'var(--color-warning)',
          //           },
          //         }),
          //         h(
          //           'span',
          //           {
          //             slot: 'content',
          //             style: { whiteSpace: 'normal', wordBreak: 'normal', maxWidth: '300px' },
          //           },
          //           `设备的【${this.indexDetectionModeMap[this.statisticalList.indexDetectionMode]}】作为指标计算结果`
          //         ),
          //       ]
          //     ),
          //   ])
          // },
        },
        // { title: '视频信号', slot: 'videoSignal', width: 130 },
        // { title: '视频亮度', slot: 'videoBrightness', width: 130 },
        // { title: '视频偏色', slot: 'videoCo
        // lorCast', width: 130 },
        // { title: '视频清晰', slot: 'videoClear', width: 130 },
        // { title: '视频遮挡', slot: 'videoOcclusion', width: 130 },
        { title: '检测时间', slot: 'videoStartTime', width: 160 },
        { title: '原因', key: 'reason', width: 160 },

        // {
        //   minWidth: 150,
        //   title: '设备标签',
        //   slot: 'tagNames',
        // },
        {
          title: '操作',
          slot: 'option',
          minWidth: 130,
          tooltip: true,
          fixed: 'right',
          align: 'center',
        },
      ],
      indexDetectionModeMap: {
        1: '在线状态',
        2: '完好状态',
        3: '可用状态',
        null: '',
      },
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        online: '',
        normal: '',
        canPlay: '',
        qualified: '',
        deviceName: '',
        deviceId: '',
      },
      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      statisticalList: {}, //统计数量
      paramsList: {},
      errorMessages: [],
      exportName: '',
      videoUrl: '',
      videoVisible: false,
      contentClientHeight: 0,
      //人工复核
      artificialRow: {},
      qualifiedList: [
        { key: '视频质量正常', value: '1' },
        { key: '视频质量异常', value: '2' },
      ],
      artificialData: { qualified: '1', errorCode: [], reason: '', screenShot: '' },
      errCodeList: [],
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
    this.getTagList();
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    handleCheckStatus(row) {
      const flag = {
        1: '合格',
        2: '不合格',
        3: '无法检测',
      };
      return flag[row];
    },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        multiSheet: this.exportName,
        errorMessages: this.errorMessages,
        orgRegionCode: this.paramsList.orgRegionCode,
        displayType: this.paramsList.displayType,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 柱状图统计
    statistical() {
      this.statisticsList.map((val) => {
        if (val.key === 'notPassAmout') {
          val.value = this.statisticalList['evaluatingFailedCount'] + this.statisticalList['unableEvaluatingCount'];
        } else {
          val.value = this.statisticalList[val.key] || 0;
        }
        if (val.key === 'resultValueFormat' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValueFormat' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          orgRegionCode: this.paramsList.orgRegionCode,
          displayType: this.paramsList.displayType,
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          customParameters: {
            deviceId: this.searchData.deviceId,
            online: this.searchData.online,
            canPlay: this.searchData.canPlay,
            qualified: this.searchData.qualified,
            deviceName: this.searchData.deviceName,
            normal: this.searchData.normal,
          },
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const { data = [] } = res.data;
        this.tableData = data.entities;
        this.searchData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    //视频播放
    async clickRow(row) {
      try {
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
    //查看截图
    showResult(row) {
      this.$refs.result.showModal(row);
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        resultId: this.paramsList.resultId,
        deviceInfoId: this.deviceInfoId,
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationDeviceResult, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities;
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getTableData();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getTableData();
    },
    selectInfo(infoList) {
      this.searchData.pageNumber = 1;
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.getTableData();
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
    },
    search() {
      this.searchData = { ...this.searchData, pageNum: 1 };
      this.getTableData();
    },
    reast() {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        online: '',
        normal: '',
        canPlay: '',
        qualified: '',
        deviceName: '',
        deviceId: '',
      };
      this.getTableData();
    },
    // 人工复核
    async artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.errorCode = [];
      this.artificialData.reason = '';
      await this.getErrorCode();
      this.artificialData.screenShot = row.screenShot;
      this.$refs.artificialReview.init(row);
      this.artificialRow = row;
    },
    async artificial() {
      let params = {
        data: {
          id: this.artificialRow.id,
          qualified: this.artificialData.qualified,
          reason: this.artificialData.reason,
        },
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
      };
      if (this.artificialData.qualified === '2') {
        if (!this.artificialData.errorCode.length) {
          this.$Message.error('请选择异常原因！');
          return;
        }
        params.data.errorCode = this.artificialData.errorCode.join(',');
      }
      if (this.artificialData.reason === '') {
        this.$Message.error('请输入备注信息！');
        return;
      }
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, params);
        this.$refs.artificialReview.hide();
        await this.getTableData();
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    async getErrorCode() {
      try {
        const {
          data: { data },
        } = await this.$http.get(evaluationoverview.getVideoQualityPassIndexErrorCode);
        this.errCodeList = data;
      } catch (e) {
        console.log(e);
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    rankList: {
      type: Array,
      default: () => [],
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          this.getTableData(); // 表格
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    resultModel: require('@/components/result-model.vue').default,
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    EasyPlayer: require('@/components/EasyPlayer').default,
    ArtificialReview: require('./components/artificial-review.vue').default,
    RankingInfo: require('./components/ranking-info').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
};
</script>
<style lang="less" scoped>
.videoAccess {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      // width: 755px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px;
    }
    .information-echart {
      display: inline-block;
      width: 650px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .echarts-box {
        width: 100%;
        height: 100% !important;
        .charts {
          width: 100%;
          height: 100% !important;
        }
      }
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      padding-right: 2px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--devider-line);
      // display: flex;
      // align-items: center;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
  .search-wrapper {
    margin-top: 10px;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    .input-width,
    .width-input {
      width: 160px;
    }
    .ui-label {
      margin-bottom: 10px;
    }
  }
  /deep/.conter-center {
    padding: 0;
    width: 100%;
  }
  /deep/.statistics-ul {
    // padding-right: 320px;
    position: relative;
    li {
      width: 19.3% !important;
    }
    // li:last-child {
    //   // height: 210px !important;
    //   width: 21% !important;
    //   position: absolute;
    //   right: 10px;
    //   z-index: 1;
    // }
    // li:nth-last-child(1) {
    //   margin-right: 0;
    // }
  }
  /deep/.f-55 {
    font-size: 55px !important;
  }
  .boxCenter {
    display: flex;
    justify-content: center;
  }
  .color_qualified {
    color: #13b13d;
  }
  .color_unqualified {
    color: #e44f22;
  }
  .ml-80 {
    margin-left: 80px;
  }
  .custormContent {
    width: 100%;
    display: flex;
    &-left {
      width: 70%;
    }
    &-right {
      flex: 1;
    }
  }
  @{_deep}.ui-image {
    z-index: initial;
    .ui-image-div {
      .tileImage {
        //overflow-y: auto;
        width: 100%;
      }
    }
  }
}
</style>
