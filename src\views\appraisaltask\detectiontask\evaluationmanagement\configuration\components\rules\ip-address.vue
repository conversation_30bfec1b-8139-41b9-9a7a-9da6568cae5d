<template>
  <ui-modal v-model="visible" title="IP地址格式检测配置" width="35%" @query="handleSubmit('extraParam')">
    <div class="ip-container">
      <p class="mb-md base-text-color">1、检测IP地址格式是否正确。</p>
      <p class="mb-md base-text-color">2、检测不能为如下非法IP地址</p>
      <Form class="base-form" ref="extraParam" :model="extraParam" :label-width="0">
        <FormItem
          v-for="(item, index) in extraParam.excludeIp"
          :key="index"
          class="mb-15 flex-content"
          :prop="'excludeIp.' + index"
        >
          <ip-address-input :ref="'ip' + index" v-model="extraParam.excludeIp[index]" />
          <i @click="handleAdd" class="icon-font icon-tree-add f-16 ml-sm color-primary"></i>
          <i
            v-if="index !== 0"
            @click="handleRemove(index)"
            class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
          ></i>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import ipAddressInput from './ip-address-input';
export default {
  name: 'ip-address',
  components: { ipAddressInput },
  props: {},
  data() {
    return {
      validate: (rule, value, callback) => {
        let reg = /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/g;
        if (!value) {
          callback(new Error('请输入ip地址'));
        } else {
          let result = reg.test(value);
          if (result) {
            callback();
          } else {
            callback(new Error('ip地址格式不正确'));
          }
        }
      },
      visible: false,
      extraParam: {
        excludeIp: [],
      },
      indexConfig: {},
    };
  },
  methods: {
    async handleSubmit() {
      try {
        let result = await this.$refs['extraParam'].validate();
        if (!result) return;
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          indexType,
          ruleId: indexRuleId,
          taskSchemeId: taskSchemeId,
          extraParam: JSON.stringify(this.extraParam),
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    handleAdd() {
      this.extraParam.excludeIp.push('');
    },
    handleRemove(index) {
      if (this.extraParam.excludeIp.length < 2) {
        return this.$Message.error('至少保留一个配置');
      }
      this.extraParam.excludeIp.splice(index, 1);
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.handleReset('extraParam');
      this.getIPconfig();
    },
    async getIPconfig() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          indexType,
          ruleId: indexRuleId,
          taskSchemeId,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        this.extraParam = {
          ...this.extraParam,
          ...JSON.parse(data.extraParam || '{"excludeIp":[""]}'),
        };
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.flex-content {
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}
.color-primary {
  color: var(--color-primary);
}

@{_deep} .ivu-modal {
  width: 600px !important;
  .ivu-modal-body {
    padding: 0 50px 50px 50px !important;
  }
}
// 覆盖不知道谁的全局样式
.mb-15 {
  margin-bottom: 25px !important;
}
.base-form {
  max-height: 550px;
  overflow-y: auto;
}
</style>
