<template>
  <div class="alarm" :class="'border' + alarmInfo.bgIndex">
    <div class="top" :class="'bg' + alarmInfo.bgIndex">
      <div>
        <span v-if="alarmInfo.taskLevel == 1">一级警报</span>
        <span v-else-if="alarmInfo.taskLevel == 2">二级警报</span>
        <span v-else>三级警报</span>
      </div>
      <div class="allDel" @click.stop="allDel()">一键关闭</div>
      <i
        class="ivu-icon ivu-icon-ios-close ivu-tabs-close"
        @click.stop="$emit('delAlarm')"
      ></i>
    </div>
    <div class="info">
      <div class="left">
        <div class="vehicle">
          <ui-image :src="alarmInfo.picUrl"></ui-image>
        </div>
      </div>
      <div class="right">
        <div class="p">
          <div class="title">任务类型:</div>
          <div class="val">{{ alarmInfo.taskParsingType |  getTaskTypeName }}</div>
        </div>
        <div class="p">
          <div class="title">任务名称:</div>
          <div class="val">
            {{ alarmInfo.taskName }}
          </div>
        </div>
        <div class="p">
          <div class="title">算法名称:</div>
          <div class="val">
            <div class="label-item text-overflow">{{ alarmInfo.algorithmName }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="device">
      <div class="p">
        <div class="title">报警时间:</div>
        <div class="val">{{ alarmInfo.alarmTime }}</div>
      </div>
      <div class="p">
        <div class="title">报警设备:</div>
        <div class="val">{{ alarmInfo?.deviceInfo?.name || data?.resourceInfo?.resourceName }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { getTaskTypeName } from "./enum";
export default {
  props: {
    alarmInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      single: false,
    };
  },
  activated() {},
  mounted() {},
  filters: {
    getTaskTypeName
  },
  methods: {
    allDel() {
      this.$emit("allDel");
    },
  },
};
</script>
<style lang="less" scoped>
.alarm {
  // position: relative;
  // width: 340px;
  // height: 190px;
  width: 380px;
  height: 240px;
  box-shadow: 0 1px 3px #d9d9d9;
  position: fixed;
  z-index: 9;
  right: 14px;
  bottom: 14px;
  overflow: hidden;
  border-radius: 3px;
  background: #fff;
  border: 1px solid #ededed;
  .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 0 0 0 20px;
    height: 40px;
    line-height: 40px;
    // margin-bottom: 12px;
    color: #fff;
    .level {
      position: absolute;
      left: 50%;
      margin-left: -46px;
    }
    .level-title {
      width: 98px;
    }
    .num {
      position: absolute;
      width: 88px;
      text-align: center;
      color: #fff;
    }
  }

  .bg1 {
    background: linear-gradient(210deg, #ff7b56 0%, #ea4a36 100%);
  }
  .bg2 {
    background: linear-gradient(210deg, #ffaf65 0%, #fc770b 100%);
  }
  .bg3 {
    background: linear-gradient(210deg, #ffd752 0%, #ffc300 100%);
  }
  .bg4 {
    background: linear-gradient(262deg, #27d676 8%, #36be7f 89%);
  }
  .bg5 {
    background: linear-gradient(263deg, #5bcaff 2%, #2c86f8 97%);
  }

  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .block {
      position: relative;
      width: 100px;
      .animation {
        /deep/ .ui-image-div {
          border: 0;
          background: transparent;
        }
      }
      .desc {
        position: absolute;
        z-index: 9;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        padding: 0 6px;
      }
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
      .c1 {
        color: #ea4a36;
      }
      .c2 {
        color: #e77811;
      }
      .c3 {
        color: #ee9f00;
      }
      .c4 {
        color: #36be7f;
      }
      .c5 {
        color: #2c86f8;
      }
    }
    .border {
      border: 1px solid #ebebeb;
    }
  }

  .info {
    display: flex;
    padding: 12px 20px;
    .left {
      flex-direction: column;
      .vehicle {
        width: 90px;
        height: 90px;
        border: 1px solid #efefef;
      }
      .license-plate-small {
        margin-top: 6px;
        padding: 10px 16px;
      }
      width: 92px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      /deep/ .ui-image-div {
        border: none;
      }
      .img {
        width: 92px;
        height: 88px;
      }
    }
    .right {
      flex: 1;
      width: 0;
      z-index: 10;
      padding-left: 12px;
      .p {
        display: flex;
        height: 26px;
        .title {
          color: #999;
          margin-right: 10px;
          line-height: 22px;
        }
        .val {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .label-item {
            max-width: 150px;
            display: inline-block;
            border-radius: 2px;
            font-size: 12px;
            color: rgb(237, 64, 20);
            padding: 0 6px;
            height: 20px;
            line-height: 20px;
            border: 1px solid rgb(237, 64, 20);
          }
        }
      }
    }
  }
  .status {
    position: absolute;
    right: 0;
    bottom: 0;
  }

  .btn {
    position: absolute;
    width: 100%;
    background: #2c86f8;
    color: #fff;
    display: flex;
    text-align: center;
    justify-content: center;
    height: 30px;
    line-height: 30px;
    bottom: -30px;
    transition: 0.3s;
    cursor: pointer;
    div {
      position: relative;
      flex: 1;
      span {
        display: inline-block;
        width: 2px;
        height: 20px;
        border-right: 1px solid #d1cbcb;
        position: absolute;
        right: 1px;
        top: 5px;
      }
    }
  }

  &:hover {
    border: 1px solid #2c86f8;
    .btn {
      bottom: 0;
      transition: 0.3s;
    }
  }
}

.border1 {
  border: 1px solid #ea4a36;
}
.border2 {
  border: 1px solid #fc770b;
}
.border3 {
  border: 1px solid #ffc300;
}
.border4 {
  border: 1px solid #36be7f;
}
.border5 {
  border: 1px solid #2c86f8;
}

.collection-icon {
  /deep/ .iconfont {
    font-size: 14px;
    color: #fff;
  }
  .ivu-tooltip-rel {
    // margin-top: 3px;
  }
  /deep/ .icon-shoucang {
    color: #f29f4c !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }
  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}

.device {
  padding: 0 20px;
  .p {
    display: flex;
    height: 26px;
    .title {
      color: #999;
      margin-right: 10px;
      line-height: 22px;
    }
    .val {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
<style lang="less">
.ivu-poptip-popper {
  width: 450px !important;
  .block {
    width: 3px;
    background: #2c86f8;
    height: 16px;
    float: left;
    margin-top: 3px;
    margin-right: 6px;
  }
}

.ivu-timeline-item {
  .timeContent {
    display: flex;
    justify-content: space-between;
  }
  .content1 {
    .p {
      display: flex;
      margin-top: 10px;
      span {
        width: 80px;
      }
      div {
        flex: 1;
      }
    }
  }
}

.ivu-poptip-body {
  height: 300px;
  overflow: auto;

  .ivu-timeline-item-head {
    background-color: #e3dada;
    border: 0;
  }

  .ivu-timeline {
    .ivu-timeline-item {
      .ivu-timeline-item-tail {
        left: 10px;
      }

      .ivu-timeline-item-head {
        left: 4px;
      }
      &:first-child {
        .ivu-timeline-item-head {
          background-color: #2d8cf0;
          left: 4px;
        }
        .ivu-timeline-item-head::after {
          content: "";
          position: absolute;
          top: -3px;
          right: -3px;
          width: 19px;
          height: 19px;
          border: 1px solid #2d8cf0 !important;
          border-radius: 50px;
          z-index: 999;
        }
      }
    }
  }
}

/deep/ .ivu-checkbox-input {
  z-index: 999;
}

.allDel {
  position: absolute;
  right: 50px;
  cursor: pointer;
}
</style>