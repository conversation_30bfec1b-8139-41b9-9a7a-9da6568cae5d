<template>
  <div class="statistics-card" :class="type">
    <span class="icon-font icon-shipinliushuju card-icon" :class="iconType"></span>
    <div class="base-text-color statistics-detail">
      <div class="f-12 detail-name">
        <div class="vt-middle ellipsis" v-for="(val, key) in data" :key='key'>{{ key }}</div>
      </div>
      <div class="ml-md f-14 detail-num">
        <div class="vt-middle ellipsis" v-for="(val, key) in data" :key='key'>{{ val }}</div>
      </div>
    </div>
    <div class="line"></div>
    <div>
      <div class="base-text-color f-12">达标情况</div>
      <div class="f-16 color-warning mt-xs">{{ '不达标' }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'statistics-card',
  components: {},
  props: {
    data: {
      default: () => ({}),
    },
    type: {
      default: 'bg-1',
    },
    iconType: {
      default: 'icon-1',
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.statistics-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 388px;
  height: 102px;
  padding: 20px;
  overflow: hidden;
  .card-icon {
    font-size: 47px;
  }
  .statistics-detail {
    display: flex;
    .detail-name {
      max-width: 60px;
      div {
        line-height: 21px;
      }
    }
    .detail-num {
      max-width: 100px;
    }
  }

  .line {
    height: 50px;
    width: 1px;
    background: rgba(9, 125, 130, 1);
  }
}
.bg-1 {
  background: rgba(16, 165, 170, 0.21);
}
.bg-2 {
  background: rgba(9, 56, 97, 1);
}
.bg-3 {
  background: rgba(56, 59, 52, 1);
}
.icon-1 {
  background: linear-gradient(180deg, #1bafd5 0%, #0a9f90 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-2 {
  background: linear-gradient(180deg, #4ba0f5 0%, #1154c9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.icon-3 {
  background: linear-gradient(180deg, #b58e0f 0%, #bb6603 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.color-warning {
  color: #f35327;
}
</style>
