export default (res, ctx, isptz, iframes, index) => {
  let {
    videoTitle,
    videoRate,
    videoClose,
    fullScreen,
    videoFilter,
    continuousShooting,
    videoPrintScreen,
    videoRatio,
    videoToMap,
    videoRealHover,
    videoHisNormal,
    instantReplay,
    sdHdPlay,
    videoZoom,
    videoFrameMark,
    searchPictures,
    ptzControl,
    videoProgress,
    videoCustomProgress,
    videoRealNormal,
    videoHisHover,
    slowPlay,
    stepBack,
    videoPlay,
    videoPause,
    stepPlay,
    fastPlay,
    curRate,
    fastBackward,
    fastForward,
    videoSearch,
    videoLock,
    videoUnlock,
    ptzLock,
    ptzUnlock,
    videoTime,
    customSlowPlay,
    customFastPlay,
    customFastBackward,
    customFastForward,
    segmentPlayHis,
    videoPresetPosition,
    videoCenter,
  } = res;
  return {
    //[实时上，实时下，历史上，历史下，历史下（自定义进度条）]
    nobar: [[], [], [], [], []],
    // 视频中心
    monitorVideo: [
      [
        videoTitle,
        videoRate,
        videoClose(ctx),
        fullScreen(ctx),
        videoFilter(ctx),
        continuousShooting(ctx),
        videoPrintScreen(ctx),
        videoRatio(ctx),
        videoToMap(ctx),
      ],
      [
        videoRealHover,
        videoHisNormal(ctx),
        instantReplay,
        sdHdPlay(ctx),
        videoZoom,
        videoFrameMark(ctx),
        ptzLock(ctx, index),
        ptzUnlock(ctx, index),
        videoPresetPosition(ctx, isptz),
        ptzControl(ctx, isptz),
        videoLock(ctx),
        videoUnlock(ctx),
      ],
      [
        videoTitle,
        videoRate,
        videoClose(ctx),
        fullScreen(ctx),
        videoFilter(ctx),
        continuousShooting(ctx),
        videoPrintScreen(ctx),
        videoRatio(ctx),
        videoToMap(ctx),
      ],
      [
        videoProgress(ctx, iframes),
        videoRealNormal(ctx),
        videoHisHover,
        slowPlay,
        stepBack(ctx),
        videoPlay,
        videoPause,
        stepPlay,
        fastPlay,
        curRate,
        fastBackward,
        fastForward,
        sdHdPlay(ctx),
        videoZoom,
        videoFrameMark(ctx),
        segmentPlayHis(ctx, index),
        videoSearch(ctx),
        videoTime,
      ],
      [
        videoCustomProgress(ctx, iframes),
        videoRealNormal(ctx),
        videoHisHover,
        customSlowPlay(ctx),
        stepBack(ctx),
        videoPlay,
        videoPause,
        stepPlay,
        customFastPlay(ctx),
        curRate,
        customFastBackward(ctx),
        customFastForward(ctx),
        sdHdPlay(ctx),
        videoZoom,
        videoFrameMark(ctx),
        videoSearch(ctx),
        videoTime,
      ],
    ],
    // 地图
    mapVideo: [
      [
        videoTitle,
        videoRate,
        videoPrintScreen(ctx),
        videoFilter(ctx),
        videoRatio(ctx),
      ],
      [
        videoRealHover,
        videoHisNormal(ctx),
        videoZoom,
        videoFrameMark(ctx),
        ptzLock(ctx, index),
        ptzUnlock(ctx, index),
        ptzControl(ctx, isptz),
      ],
      [
        videoTitle,
        videoRate,
        videoPrintScreen(ctx),
        videoFilter(ctx),
        videoRatio(ctx),
      ],
      [
        videoProgress(ctx, iframes),
        videoRealNormal(ctx),
        videoHisHover,
        slowPlay,
        stepBack(ctx),
        videoPlay,
        videoPause,
        stepPlay,
        fastPlay,
        curRate,
        videoZoom,
        videoFrameMark(ctx),
        videoSearch(ctx),
        videoTime,
      ],
      [
        videoCustomProgress(ctx, iframes),
        videoRealNormal(ctx),
        videoHisHover,
        customSlowPlay(ctx),
        stepBack(ctx),
        videoPlay,
        videoPause,
        stepPlay,
        customFastPlay(ctx),
        curRate,
        videoZoom,
        videoFrameMark(ctx),
        videoSearch(ctx),
        videoTime,
      ],
    ],
    singleVideo: [
      [videoTitle, videoRate, videoPrintScreen(ctx)],
      [],
      [videoTitle, videoRate, videoPrintScreen(ctx)],
      [
        videoProgress(ctx, iframes),
        slowPlay,
        stepBack(ctx),
        videoPlay,
        videoPause,
        stepPlay,
        fastPlay,
        curRate,
      ],
      [
        videoCustomProgress(ctx, iframes),
        customSlowPlay(ctx),
        stepBack(ctx),
        videoPlay,
        videoPause,
        stepPlay,
        customFastPlay(ctx),
        curRate,
      ],
    ],
    onlyVideo: [
      [videoTitle, fullScreen(ctx),],
      [],
      [],
      [],
      [],
    ],
    // 全景追逃
    panoramicPursuit: [
      [videoTitle, videoRate, videoCenter(ctx, index)],
      [],
      [],
      [],
      [],
    ],
    // 警卫路线
    guardRouteVideo: [
      [
        videoTitle,
        videoRate,
        videoClose(ctx),
        fullScreen(ctx),
        videoRatio(ctx),
      ],
      [
        videoRealHover,
        videoHisNormal(ctx),
        instantReplay,
        ptzLock(ctx, index),
        ptzUnlock(ctx, index),
        ptzControl(ctx, isptz),
      ],
      [
        videoTitle,
        videoRate,
        videoClose(ctx),
        fullScreen(ctx),
        videoRatio(ctx),
      ],
      [
        videoProgress(ctx, iframes),
        videoRealNormal(ctx),
        videoHisHover,
        slowPlay,
        videoPlay,
        videoPause,
        fastPlay,
        curRate,
        videoSearch(ctx),
        videoTime,
      ],
      [
        videoCustomProgress(ctx, iframes),
        videoRealNormal(ctx),
        videoHisHover,
        customSlowPlay(ctx),
        videoPlay,
        videoPause,
        customFastPlay(ctx),
        curRate,
        videoSearch(ctx),
        videoTime,
      ],
    ],
  };
};
