<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" :title="title" @onCancel="reset" @query="handleSave">
      <transfer-table
        class="tt"
        :left-table-columns="columns1"
        :right-table-columns="columns2"
        :left-table-data="propertyList"
        :right-table-data="targetList"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
        @onLeftToRight="selectionChange"
      >
        <template #left-title>
          <div class="mb-sm">
            <span class="base-text-color">字段拾取</span>
          </div>
        </template>
        <template #right-title>
          <div class="mb-sm">
            <span class="base-text-color">字段名列表</span><span class="color-failed ml-sm">(注:不能为空)</span>
          </div>
        </template>
      </transfer-table>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    title: {
      type: String,
      default: '空值检测',
    },
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { title: '字段名111', key: 'propertyName' },
        { title: '注释', key: 'propertyColumn' },
      ],
      propertyList: [],
      columns2: [
        { title: ' ', key: '', width: 20, align: 'center' },
        { title: '字段名', key: 'checkColumnName' },
        { title: '注释', key: 'checkColumnValue' },
      ],
      targetList: [], // 字段名列表
      checkedList: [],
      checkedIds: [],
      leftLoading: false,
      rightLoading: false,
      indexConfig: {},
      defaultEmpty: [
        { checkColumnName: 'deviceId', checkColumnValue: '设备编码' },
        { checkColumnName: 'deviceName', checkColumnValue: '设备名称' },
        { checkColumnName: 'ipAddr', checkColumnValue: 'IP地址' },
        { checkColumnName: 'macAddr', checkColumnValue: 'MAC地址' },
        { checkColumnName: 'longitude', checkColumnValue: '经度' },
        { checkColumnName: 'latitude', checkColumnValue: '纬度' },
        { checkColumnName: 'sbdwlx', checkColumnValue: '监控点位类型' },
        { checkColumnName: 'sbgnlx', checkColumnValue: '摄像机功能类型' },
        { checkColumnName: 'phyStatus', checkColumnValue: '设备状态' },
        { checkColumnName: 'sbcjqy', checkColumnValue: '摄像机采集区域' },
      ],
    };
  },
  methods: {
    async init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      await this.getPropertyList();
      this.getDevice();
    },
    // 获取已勾选的数据
    async getPropertyList() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let res = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, {
          params,
        });
        this.setDefaultValue(res.data.data.rulePropertyList || []);
      } catch (error) {
        console.log(error);
      }
    },
    // 字段拾取列表
    async getDevice() {
      try {
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.propertyList = res.data.data.map((item) => {
          if (this.checkedIds.length) {
            this.checkedIds.forEach((checkId) => {
              if (item.propertyName == checkId) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.targetList = this.checkedList;
      } catch (error) {
        console.log(error);
      } finally {
        this.leftLoading = false;
        this.rightLoading = false;
      }
    },
    selectionChange(selection) {
      selection = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        // obj.column = `${item.propertyColumn} (${item.propertyName})`;
        return obj;
      });
      this.targetList = selection;
    },
    // 保存
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          rulePropertyList: this.targetList,
          taskSchemeId,
          indexType,
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('空值检测配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
    //基础数据检测规则-空值检测，默认加上资产库的10个字段 2984
    setDefaultValue(val) {
      if (val.length === 0) {
        this.checkedList = JSON.parse(JSON.stringify(this.defaultEmpty));
        this.checkedIds = this.defaultEmpty.map((item) => item.checkColumnName);
        return;
      }
      this.checkedIds = val.map((item) => item.checkColumnName);
      this.checkedList = val.map((item) => {
        return {
          checkColumnName: item.checkColumnName,
          checkColumnValue: item.checkColumnValue,
        };
      });
    },
  },
  watch: {},
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.repeat-empty {
  @{_deep} .ivu-modal {
    width: 1200px !important;
    .ivu-modal-body {
      min-height: 600px;
      padding: 0 50px 16px 50px;
    }
  }
  .tt {
    height: 600px;
  }
}
</style>
