<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />

    <TableList ref="infoList" :columns="columns" :loadData="loadDataList">
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <SearchList
          ref="searchList"
          style="margin: 0"
          @startSearch="startSearch"
          :selectList="[
            { id: 0, label: '轨迹关联设备' },
            { id: 1, label: '轨迹设备未关联设备资产库' },
            { id: 2, label: '轨迹编码不存在' },
          ]"
        />
      </div>
      <!-- 表格操作 -->
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" @click="deviceArchives(row)">{{ row.deviceId }}</span>
      </template>
      <template #trackImage="{ row }">
        <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
          <ui-image :src="row.trackImage" />
        </div>
      </template>
      <template #identityPhoto="{ row }">
        <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
          <ui-image :src="row.identityPhoto" alt="" />
        </div>
      </template>
      <template #synthesisResult="{ row }">
        <span style="color: #19c176" v-if="row.synthesisResult === '1'">准确性合格</span>
        <span style="color: #c43d2c" v-else-if="row.synthesisResult === '0'">轨迹设备未关联设备资产库</span>
        <span style="color: #c43d2c" v-else-if="row.synthesisResult === '2'">轨迹设备编码不存在</span>
        <span v-else>-</span>
      </template>
      <template #isguan="{ row }">
        <span style="color: #c43d2c" v-if="row.synthesisResult === '1'">否</span>
        <span style="color: #19c176" v-else-if="row.synthesisResult === '0'">是</span>
        <span v-else>-</span>
      </template>
      <template #similarity="{ row }">
        <span>{{ (row.similarity * 100).toFixed(2) }}<span v-if="row.similarity">%</span></span>
      </template>
      <template #algResult="{ row }">
        <span v-if="row.algResult">
          <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
            <span>{{ item.algorithmType }}:</span>
            <span>{{ item.score }}%</span>
          </div>
        </span>
        <span v-else>--</span>
      </template>
    </TableList>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    ChartsContainer: require('../components/chartsContainer').default,
    LookScene: require('@/components/look-scene').default,
    TableList: require('../components/tableList.vue').default,
    SearchList: require('@/components/track-detail-search.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      bigPictureShow: false,
      treeData: [],
      imgList: [],
      selectKey: '', // 机构树
      infoObj: {},
      abnormalCount: [
        { title: 'ZDR人员总量', icon: 'icon-guijishebeiguanlianxingjiance', count: 0 },
        { title: 'ZDR人像轨迹总量', icon: 'icon-guijishebeiguanlianxingjiance', count: 0 },
        { title: '检测轨迹数量', icon: 'icon-guijishebeiguanlianxingjiance', count: 0 },
        { title: '关联设备轨迹数量', icon: 'icon-guijishebeiguanlianxingjiance', count: 0 },
        { title: '未关联设备轨迹数量', icon: 'icon-guijishebeiguanlianxingjiance', count: 0 },
        { title: '轨迹设备编码不存在', icon: 'icon-guijishebeiguanlianxingjiance', count: 0 },
      ],
      columns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '抓拍图片', key: 'trackImage', slot: 'trackImage', tooltip: true, minWidth: 120 },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        { title: '档案照', key: 'identityPhoto', slot: 'identityPhoto', minWidth: 120 },
        { title: '姓名', key: 'name', minWidth: 80 },
        { title: '证件号', key: 'idCard', minWidth: 150 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
        },
        { title: '是否关联设备资产库', slot: 'isguan', width: 150, tooltip: true },
        { title: '检测结果', key: 'synthesisResult', slot: 'synthesisResult', minWidth: 150 },
      ],
      loadDataList: (parameter) => {
        if (!this.taskObj.rootId || !this.taskObj.taskId) {
          console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        } else {
          return this.$http
            .post(
              governanceevaluation.focusRelateList,
              Object.assign(
                parameter,
                {
                  resultId: this.taskObj.rootId,
                  taskId: this.taskObj.taskId,
                  indexId: this.currentTree.indexId,
                },
                this.searchData,
              ),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
      },
    },
  },
  filter: {},
  created() {},
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
    this.info();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    async info() {
      await this.static();
      await this.$refs.infoList.info(true);
    },
    static() {
      if (!this.taskObj.rootId) {
        console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        return;
      }
      this.$http
        .post(governanceevaluation.getResultStatistics, {
          resultId: this.taskObj.rootId,
          indexId: this.currentTree.indexId,
          taskId: this.taskObj.taskId,
        })
        .then((res) => {
          let data = res.data.data || {};
          this.abnormalCount = [
            {
              title: 'ZDR人员总量',
              icon: 'icon-guijishebeiguanlianxingjiance',
              count: data.importPersonNumbr || 0,
            },
            {
              title: 'ZDR人像轨迹总量',
              icon: 'icon-guijishebeiguanlianxingjiance',
              count: data.importPersonTrajectoryNumbr || 0,
            },
            {
              title: '检测轨迹数量',
              icon: 'icon-guijishebeiguanlianxingjiance',
              count: data.detectionAmount || 0,
            },
            {
              title: '关联设备轨迹数量',
              icon: 'icon-guijishebeiguanlianxingjiance',
              count: data.relatedAmount || 0,
            },
            {
              title: '未关联设备轨迹数量',
              icon: 'icon-guijishebeiguanlianxingjiance',
              count: data.unRelatedAmount || 0,
            },
            {
              title: '轨迹设备编码不存在',
              icon: 'icon-guijishebeiguanlianxingjiance',
              count: data.noDevice || 0,
            },
          ];
        });
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.selectKey = searchData.orgCodeList;
      this.searchData = searchData;
      this.$refs.infoList.info(true);
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.ui-images {
  width: 56px;
  height: 56px;
  margin: 5px 0;
  .ui-image {
    min-height: 56px !important;
    /deep/ .ivu-spin-text {
      img {
        width: 56px;
        height: 56px;
        margin-top: 5px;
      }
    }
  }
}
</style>
