<template>
    <div class="coincidence-card">
        <header class="header">
            <span class="header-title">重合点位</span>
            <span class="total-block">共<span class="total">{{Object.values(data).length}}</span>个</span>
        </header>
        <article>
            <coincidence-card v-for="(item,index) in data" :key="index" :record="item"></coincidence-card>
        </article>
    </div>
</template>

<script>
import CoincidenceCard from './CoincidenceCard'

export default {
    name: 'coincidenceContainer',
    components: {
        CoincidenceCard
    },
    props: {
        data: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            lapPoints: []
        }
    },
    mounted() {
        // $pubsub.subscribe("aim-checked", async (list, params) => {
        //     let plateInfos = params.captureList.map(item => `${item.plateNo}_${item.plateColor}`)
        //     $pubsub.publish("lapPoints-output", params.coincidentPointList, plateInfos)
        // })
        // $pubsub.publish("lapPoints-output", this.data)
    }
}

</script>

<style lang="less" scoped>
.coincidence-card {
    margin-top: 10px;
    background: #fff;
    padding-bottom: 10px;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        height: 40px;
        border-bottom: 1px solid #999999;

        .header-title {
            font-weight: bold;
            color: #333333;
        }

        .total-block {
            color: #999999;

            .total {
                color: #2d87f9;
            }
        }
    }
}
</style>