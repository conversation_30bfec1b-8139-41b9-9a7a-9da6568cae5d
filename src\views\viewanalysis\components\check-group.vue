<template>
  <el-checkbox-group v-model="checkedList" @change="handleChange">
      <el-checkbox v-for="type in optionList" :key="type.value" :label="type.value">{{type.text}}</el-checkbox>
  </el-checkbox-group>
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      required: true
    },
    optionList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkedList: []
    }
  },
  watch: {
    checkedList(val) {
      this.$emit('input', val.join(','))
    },
    value: {
      handler(val) {
        this.checkedList = val.split(',')
      },
      immediate: true
    }
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val.join(','))
    },
    updateList() {
      this.checkedList = this.value.split(',')
    }
  }
}
</script>
<style lang="less" scoped>
.el-checkbox {
  margin-right: 10px;
}
/deep/.el-checkbox__label {
  padding-left: 5px;
}
</style>