<template>
  <div class="tag-management auto-fill">
    <div class="table-message auto-fill">
      <div class="search-wrapper">
        <div class="search-left">
          <ui-label label="标签名称" :width="65" class="mr-lg">
            <Input v-model="searchData.tagName" placeholder="请输入标签名称" class="width-md"></Input>
          </ui-label>
          <ui-label class="mr-lg" label="标签类型" :width="65">
            <Select v-model="searchData.tagType" class="width-md" clearable placeholder="请选择标签类型">
              <Option v-for="(item, index) in tagTypeList" :key="index" :label="item.dataValue" :value="item.dataKey">
              </Option>
            </Select>
          </ui-label>
          <ui-label label="标签类别" :width="65" class="mr-lg">
            <Select v-model="searchData.tagCategory" class="width-md" clearable placeholder="请选择标签类别">
              <Option v-for="(item, index) in tagCategory" :key="index" :label="item.dataValue" :value="item.dataKey">
              </Option>
            </Select>
          </ui-label>
          <ui-label label="标签颜色" :width="65" class="mr-lg">
            <span class="all vt-middle" :class="isAll ? 'checked-all' : ''" @click="checkedAll">全部</span>
            <tag-color
              :mutiple-checked="true"
              :default-tag-list="defaultTagList"
              class="tag-color inline vt-middle"
              @tagCheckChange="tagCheckChange"
            ></tag-color>
          </ui-label>
          <ui-label :width="0" class="button-div" label="">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="clear">重置</Button>
          </ui-label>
        </div>

        <Button type="primary" class="fr" @click="addTags('add')">
          <i class="icon-font icon-tianjia f-12"></i>
          <span class="vt-middle ml-sm">新增标签信息</span>
        </Button>
      </div>
      <div class="table-module auto-fill" v-ui-loading="{ loading: loading, tableData: tableList }">
        <ul class="table-content">
          <li
            class="table-content-li pointer"
            v-for="(item, index) of tableList"
            :key="index"
            @click.stop="addTags('view', item)"
          >
            <i class="icon-font icon-biaoqian"></i>
            <div class="content">
              <p class="tag-li-p">
                标签名称：<span class="base-text-color">{{ item.tagName }}</span>
              </p>
              <p class="tag-li-p mt-sm">
                标签类型：<span class="base-text-color">{{ item.tagTypeText }}</span>
              </p>
              <p class="tag-li-p mt-sm">
                标签类别：<span class="base-text-color">{{ item.tagCategoryText }}</span>
              </p>
              <p class="tag-li-p mt-sm">
                标签颜色：
                <Tag :color="item.tagColour" class="tag-class"></Tag>
              </p>
              <!--      <div class="title">
                      <p class="tag-li-p mt-sm">
                        创建人:<span class="base-text-color">{{ item.creator }}</span>
                      </p>
                    </div>-->
              <div class="icon-box">
                <Tooltip placement="top">
                  <i
                    class="icon-font f-14 icon-bianji mr-sm pointer icon-operation-color"
                    @click.stop="addTags('edit', item)"
                  ></i>
                  <div slot="content">编辑</div>
                </Tooltip>
                <Tooltip placement="top">
                  <i
                    class="icon-font f-14 icon-shanchu2 pointer icon-operation-color"
                    @click.stop="deleteTags(item)"
                  ></i>
                  <div slot="content">删除</div>
                </Tooltip>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <add-edit
      v-model="addEditShow"
      :add-edit-action="addEditAction"
      :default-params="defaultParams"
      :tag-type-list="tagTypeList"
      :tag-category="tagCategory"
      @update="init"
    >
    </add-edit>
  </div>
</template>
<style lang="less" scoped>
.tag-management {
  background: var(--bg-content);
  .table-message {
    width: 100%;
    height: 100%;
    .search-wrapper {
      display: flex;
      padding: 20px 20px 10px 20px;
      .search-left {
        flex: 1;
        display: flex;
      }
      .all {
        display: inline-block;
        text-align: center;
        background-color: var(--bg-btn-default);
        border: 1px solid var(--border-btn-default);
        color: var(--border-btn-default-hover);
        width: 50px;
        height: 30px;
        line-height: 30px;
        margin-right: 10px;
        border-radius: 4px;
        cursor: pointer;
      }
      .checked-all {
        background-color: var(--border-btn-default-hover);
        color: #fff;
      }
    }
    .total-box {
      margin: 0 20px;
      border-bottom: 1px solid var(--border-color);
    }
    .table-module {
      position: relative;
      padding-top: 10px;
      overflow-x: hidden;
      overflow-y: auto;
    }
    .table-content {
      position: absolute;
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      padding: 0 10px 0 20px;
      .table-content-li {
        flex: 0 0 24.53%;
        background: var(--bg-info-card);
        margin-right: 10px;
        margin-bottom: 10px;
        border-radius: 4px;
        font-size: 14px;
        position: relative;
        border: 2px solid var(--border-info-card);
        &:hover {
          border: 2px solid var(--border-btn-default-hover);
        }
        &:nth-child(4n) {
          margin-right: 0;
        }
        .content {
          padding: 20px;
        }
        .icon-biaoqian {
          position: absolute;
          top: 0;
          right: 0;
          opacity: 0.04;
          color: var(--border-btn-default-hover);
          font-size: 111px;
          height: 111px;
          line-height: 111px;
        }
        .title {
          display: flex;
          justify-content: space-between;
        }
        .tag-li-p {
          color: @font-color-grey;
          &.special-p {
            display: flex;
            .label {
              width: 60px;
            }
            .special-p-span {
              width: 230px;
              word-break: break-all;
              text-overflow: ellipsis;
              display: -webkit-box; /** 对象作为伸缩盒子模型显示 **/
              -webkit-box-orient: vertical; /** 设置或检索伸缩盒对象的子元素的排列方式 **/
              -webkit-line-clamp: 2; /** 显示的行数 **/
              overflow: hidden; /** 隐藏超出的内容 **/
            }
          }
        }
        .icon-box {
          position: absolute;
          right: 10px;
          bottom: 10px;
        }
      }
    }
    .tag-class {
      width: 16px;
      height: 16px;
      border-radius: 2px;
    }
  }
}
</style>
<script>
import taganalysis from '@/config/api/taganalysis';
import user from '@/config/api/user';

export default {
  name: 'labelwarehouse',
  data() {
    return {
      loading: false,
      searchData: {
        tagName: '',
        tagType: '',
        tagCategory: '',
        pageNumber: 1,
        pageSize: 20,
        tagColorList: [],
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableList: [],
      defaultKeys: [],
      choosedOrg: '',
      addEditShow: false,
      addEditAction: {},
      defaultParams: {},
      fieldList: [],
      tagTypeList: [],
      tagCategory: [],
      defaultTagList: [
        { color: $cssVar('--color-success'), checkable: true, name: 'success' },
        { color: $cssVar('--color-warning'), checkable: true, name: 'error' },
        { color: $cssVar('--color-failed'), checkable: true, name: 'warning' },
      ],
      dicDataEnum: Object.freeze({
        tag_type: 'tagTypeList',
        tag_category: 'tagCategory',
      }),
    };
  },
  created() {},
  mounted() {},
  activated() {
    this.searchData.tagColorList = this.defaultTagList.map((item) => item.color);
    this.init();
    this.initDicContent();
  },
  methods: {
    tagCheckChange(data) {
      this.searchData.tagColorList = [];
      data.forEach((item) => {
        let tag = this.defaultTagList.find((row) => item.name === row.name);
        this.$set(tag, 'checkable', item.checkable);
        if (item.checkable) {
          this.searchData.tagColorList.push(item.color);
        }
      });
    },
    checkedAll() {
      if (this.isAll) {
        this.searchData.tagColorList = [];
        this.defaultTagList.forEach((item) => {
          this.$set(item, 'checkable', false);
          return item.color;
        });
      } else {
        this.searchData.tagColorList = this.defaultTagList.map((item) => {
          this.$set(item, 'checkable', true);
          return item.color;
        });
      }
    },
    selectOrgCode(data) {
      this.defaultKeys = [data.orgCode];
      this.choosedOrg = data.name;
    },
    clear() {
      this.defaultTagList = [
        { color: '#0E8F0E', checkable: true, name: 'success' },
        { color: '#D66418', checkable: true, name: 'error' },
        { color: '#BC3C19', checkable: true, name: 'warning' },
      ];
      this.resetSearchDataMx(this.searchData, this.search);
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        this.tableList = [];
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(taganalysis.getDeviceTag, this.searchData);
        this.tableList = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    deleteTags(item) {
      this.$UiConfirm({
        title: '警告',
        content: `您将删除标签 ${item.tagName}，是否确认?`,
      }).then(async () => {
        this.loading = true;
        try {
          let res = await this.$http.post(taganalysis.deleteDeviceTag, [item.tagId]);
          this.$Message.success(res.data.msg);
          this.init();
        } catch (err) {
          console.log(err);
        } finally {
          this.loading = false;
        }
      });
    },
    addTags(type, item) {
      if (type === 'add') {
        this.addEditAction = {
          title: '新增标签信息',
          action: 'add',
        };
        this.defaultParams = {
          tagCategory: '',
          tagType: '',
          tagName: null,
          tagColour: null,
        };
      } else if (type === 'edit') {
        this.addEditAction = {
          title: '修改标签信息',
          action: 'edit',
        };
        this.defaultParams = item;
      } else {
        this.addEditAction = {
          title: '查看标签信息',
          action: 'view',
        };
        this.defaultParams = item;
      }
      this.addEditShow = true;
    },
    async initDicContent() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = data.data.find((row) => {
            return !!row[key];
          });
          this[this.dicDataEnum[key]] = obj[key];
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    isAll() {
      return this.searchData.tagColorList.length === this.defaultTagList.length;
    },
  },
  props: {},
  components: {
    AddEdit: require('./add-edit.vue').default,
    TagColor: require('./components/tag-color.vue').default,
  },
};
</script>
