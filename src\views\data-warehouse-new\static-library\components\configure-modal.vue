<template>
  <ui-modal
    v-model="visible"
    title="配置"
    :r-width="600"
    @onOk="comfirmHandle">
    <ui-table ref="configureTable" border :columns="columns" :data="tableData" :loading="loading" class="configure-table">
      <template #chineseName="{ row }">
        <Input placeholder="请输入"/>
      </template>
      <template #isCloudSearchField="{ row }">
        <Select transfer placeholder="请选择">
          <Option :value="0">是</Option>
          <Option :value="1">否</Option>
        </Select>
      </template>
    </ui-table>
  </ui-modal>
</template>
<script>
  export default {
    data() {
      return {
        visible: false,
        loading: false,
        columns: [
          { type: 'index', title: '序号', width: 70, align: 'center' },
          { title: '字段名称', key: 'fieldName', width: 150 },
          { title: '中文名称', slot: 'chineseName', width: 150 },
          { title: '是否作为云搜检索字段', slot: 'isCloudSearchField' }
        ],
        tableData: [
          {
            fieldName: 'id',
            chineseName: '',
            isCloudSearchField: ''
          },
          {
            fieldName: 'name',
            chineseName: '',
            isCloudSearchField: ''
          },
          {
            fieldName: 'gender',
            chineseName: '',
            isCloudSearchField: ''
          },
          {
            fieldName: 'address',
            chineseName: '',
            isCloudSearchField: ''
          }
        ]
      }
    },
    methods: {
      show() {
        this.visible = true
      },
      comfirmHandle() {
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
.configure-table {
  /deep/ .ivu-table-cell {
    padding: 0 20px;
  }
}
  
</style>