<template>
  <div class="key-equipment-report auto-fill">
    <!-- 顶部统计 -->
    <chartsContainer :abnormalCount="countList" class="charts" />
    <!--搜索参数-->
    <div class="search-module">
      <ui-label class="inline mr-md" label="组织机构">
        <api-organization-tree
          class="tree-style"
          :select-tree="selectOrgTree"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        />
      </ui-label>
      <ui-label class="inline mr-md" label="行政区划">
        <api-area-tree :select-tree="selectTree" @selectedTree="selectedArea" placeholder="请选择行政区划" />
      </ui-label>
      <ui-label class="inline mr-md" :label="global.filedEnum.deviceId">
        <Input v-model="searchData.deviceId" class="width-md" :placeholder="`${global.filedEnum.deviceId}`" />
      </ui-label>
      <ui-label class="inline mr-md" :label="global.filedEnum.deviceName">
        <Input v-model="searchData.deviceName" class="width-md" :placeholder="global.filedEnum.deviceName" />
      </ui-label>
      <!--设备状态-->
      <ui-label :label="global.filedEnum.phyStatus" class="inline mr-md" style="margin-top: 12px">
        <Select
          class="width-sm"
          v-model="searchData.phyStatus"
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
          clearable
        >
          <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <!-- 摄像机采集区域 -->
      <ui-label class="inline mb-10" :label="`${global.filedEnum.sbcjqy}列表`">
        <Button type="dashed" class="area-btn" @click="clickArea"
          >请选择采集区域 {{ `已选择 ${searchData.sbcjqyList.length}个` }}</Button
        >
      </ui-label>
      <ui-label :label="global.filedEnum.sbdwlx" class="inline mr-md">
        <Select
          class="width-md"
          v-model="searchData.sbdwlx"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          clearable
        >
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label :label="global.filedEnum.sbgnlx" class="inline mr-md">
        <Select
          class="width-md"
          v-model="searchData.sbgnlx"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
          clearable
        >
          <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">
            {{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="inline ml-lg" style="margin-top: 12px">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetHandle">重置</Button>
      </div>
    </div>
    <!-- 操作按钮 -->
    <div class="operation">
      <div class="data-list mr-lg">
        <span class="label">标签类型</span>
        <ui-select-tabs
          class="ui-select-tabs"
          :list="allTagList"
          @selectInfo="selectTabHandle"
          ref="uiSelectTabs"
        ></ui-select-tabs>
      </div>
      <Checkbox v-model="allCheck">全选</Checkbox>
      <div class="right-btn fr">
        <Dropdown class="dropdown">
          <Button type="primary"> <i class="btn icon-font icon-tianjia"></i>添加重点设备 </Button>
          <DropdownMenu slot="list">
            <DropdownItem>
              <Button class="add-device" type="text" @click.stop="addDeviceHandle"> 系统选择 </Button>
            </DropdownItem>
            <DropdownItem
              ><Upload
                action="/ivdg-asset-app/device/uploadImportDevice"
                ref="upload"
                class="inline"
                :show-upload-list="false"
                :headers="headers"
                :before-upload="beforeUpload"
                :on-success="importSuccess"
                :on-error="importError"
              >
                <Button type="text" :loading="importLoading">
                  <span class="vt-middle">文件导入</span>
                </Button>
              </Upload>
              <Button type="text" @click.stop="exportModule">
                <span class="link">(下载模板)</span>
              </Button>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Button
          class="merge-btn fr ml-sm"
          type="primary"
          :disabled="selectTable.length === 0 && !allCheck"
          @click="removeBulkDevice()"
        >
          <i class="icon-font icon-yichu mr-sm"></i>
          <span>批量移除</span>
        </Button>
      </div>
    </div>
    <!-- 表格 -->
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selected"
      >
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltipType">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-failed)',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #keyDeviceReportStatus="{ row }">
          <div>
            <span
              class="check-status"
              :class="[
                row.keyDeviceReportStatus === '2' ? 'bg-failed' : '',
                row.keyDeviceReportStatus === '1' ? 'bg-success' : '',
              ]"
            >
              {{ row.keyDeviceReportStatus | filterType(keyDeviceReportStatusList) }}
            </span>
          </div>
        </template>
        <template #keyDeviceReportDate="{ row }">
          <span>{{ row.keyDeviceReportDate || '--' }}</span>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            class="mr-md"
            :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
            icon="icon-shebeidangan"
            content="设备档案"
            @click.native="deviceArchives(row)"
          />
          <ui-btn-tip icon="icon-yichu1" @click.native="removeOneDevice(row)" content="移除" />
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize" />
    </div>
    <!--选择采集区域-->
    <area-select v-model="areaSelectModalVisible" @confirm="confirmArea" :checkedTreeDataList="checkedTreeData" />
    <!--选择重点设备-->
    <chooseKeyDevice ref="chooseKeyDevice" @refreshList="search" v-if="chooseKeyDeviceVisible" />
    <ui-modal class="export-result" v-model="exportResultShow" title="导入结果">
      <div class="base-text-color">
        {{ exportResultMsg }}
      </div>
      <template #footer>
        <Button type="text" class="mr-lg" v-if="exportBatchId" @click="exportResultError">导出无法标记设备</Button>
        <Button type="text" @click="goFile">去建档</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';

export default {
  name: 'keyequipmentmanage',
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    chooseKeyDevice: require('./components/choose-key-device').default,
    AreaSelect: require('@/components/area-select').default,
    apiOrganizationTree: require('@/api-components/api-organization-tree').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    chartsContainer: require('./chartsContainer').default,
  },
  data() {
    return {
      allCheck: false,
      chooseKeyDeviceVisible: false,
      selectOrgTree: {
        orgCode: '',
      },
      selectTree: {
        regionCode: '',
      },
      allTagList: [
        { name: '测试', id: '1' },
        { name: '测试2', id: '2' },
      ], //设备标签
      keyDeviceGatherArea: [],
      keyDeviceReportStatusList: [
        { dataKey: '0', dataValue: '未上报' },
        { dataKey: '1', dataValue: '已上报' },
        { dataKey: '2', dataValue: '上报失败' },
      ], //重点设备上报状态
      searchData: {
        orgCode: '',
        civilCode: '',
        deviceId: '',
        deviceName: '',
        sbdwlx: '',
        sbgnlx: '',
        sourceId: '',
        errorType: '',
        checkStatuses: [],
        errorMessageList: [],
        tagCategory: '',
        whdw: '',
        sbcjqyList: [],
        pageNumber: 1,
        pageSize: 20,
        isCheck: '',
        baseCheckStatus: '',
        viewCheckStatus: '',
        videoCheckStatus: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 150,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
          align: 'left',
        },
        {
          minWidth: 100,
          title: '采集区域',
          key: 'sbcjqyText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: this.global.filedEnum.phyStatus,
          slot: 'phyStatus',
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          width: 80,
        },
      ],
      tableData: [],
      loading: false,
      countList: [
        { title: '重点设备数量', total: '0', icon: 'icon-equipmentlibrary' },
        { title: '重点视频监控', total: '0', icon: 'icon-ivdg-shipinjiankong' },
        { title: '重点人脸卡口', total: '0', icon: 'icon-renliankakou' },
        { title: '重点车辆卡口', total: '0', icon: 'icon-cheliangkakou' },
      ],
      configShow: false,
      selectTable: [],
      areaSelectModalVisible: false,
      checkedTreeData: [],
      isReportLoading: false,
      // 上传文件
      importLoading: false,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      exportResultShow: false,
      exportResultMsg: '',
      exportBatchId: null,
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
    isRepeatRecord() {
      return this.activeKey === 5;
    },
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    this.getTagList();
    this.init();
    this.queryDeviceCount();
  },
  mounted() {
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    init() {
      this.loading = true;
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      params.isImportant = '1';
      this.$http
        .post(equipmentassets.getPageDeviceList, params)
        .then((res) => {
          let { entities, total } = res.data.data;
          this.selectTable = [];
          this.pageData.totalCount = total;
          this.tableData = entities;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 查询所有标签
    async getTagList() {
      let res = await this.$http.post(taganalysis.getDeviceTag, {
        isPage: false,
      });
      this.allTagList = res.data.data.map((item) => {
        return { name: item.tagName, id: item.tagId };
      });
    },
    // 获取头部统计信息
    async queryDeviceCount() {
      try {
        let params = {};
        this.copySearchDataMx(this.searchData);
        Object.assign(params, this.searchData);
        params.isImportant = '1';
        let res = await this.$http.post(taganalysis.deviceStatistics, params);
        let obj = res.data.data;
        this.countList[0].total = obj.deviceTotal;
        this.countList[1].total = obj.videoMonitor;
        this.countList[2].total = obj.faceCount;
        this.countList[3].total = obj.vehicleCount;
      } catch (err) {
        console.error('err', err);
      } finally {
        // this.loading = false
      }
    },
    // 选择组织机构
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.searchData.configOrgCode = val.orgCode;
      this.search();
    },
    // 选择行政区划
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // 查询
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
      this.queryDeviceCount();
    },
    // 重置
    resetHandle() {
      this.selectOrgTree.orgCode = '';
      this.selectTree.regionCode = '';
      this.searchData.tagIds = [];
      this.allTagList.forEach((item) => {
        item.select = false;
      });
      this.resetSearchDataMx(this.searchData, this.search);
      this.queryDeviceCount();
    },
    // table选中方法
    selected(selection) {
      this.selectTable = selection;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.init();
    },
    // 设备档案
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    // 选择采集区域
    clickArea() {
      this.areaSelectModalVisible = true;
      this.checkedTreeData = this.searchData.sbcjqyList || [];
    },
    // 采集区域确定
    confirmArea(data) {
      this.searchData.sbcjqyList = data;
    },
    // 添加重点设备
    addDeviceHandle() {
      this.chooseKeyDeviceVisible = true;
      this.$nextTick(() => {
        this.$refs.chooseKeyDevice.init();
      });
    },
    // 选择标签
    selectTabHandle(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
      this.search();
    },
    // 批量移除
    removeBulkDevice() {
      const contentInfo = `您将${
        this.allCheck ? '按照搜索条件进行' : '这' + this.selectTable.length + '项'
      }设备移除，是否确认?`;
      this.$UiConfirm({
        content: contentInfo,
        title: '警告',
      }).then(() => {
        this.removeDeviceForCategory();
      });
    },
    //移除单个设备
    removeOneDevice(row) {
      this.$UiConfirm({
        content: `您将移除 ${row.deviceName}，是否确认?`,
        title: '警告',
      }).then(() => {
        this.removeDeviceForCategory(row);
      });
    },
    // 移除相关目录设备
    async removeDeviceForCategory(row) {
      this.loading = true;
      let params = {};
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      if (this.allCheck) {
        params.ids = [];
      } else {
        if (row) {
          params.ids = [row.id];
        } else {
          params.ids = this.selectTable.map((item) => item.id);
        }
      }
      this.$http
        .post(equipmentassets.removeImportDevices, params)
        .then((res) => {
          this.$Message.success(res.data.msg);
          this.allCheck = false;
          this.init();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        this.$Message.success(res.msg);
        this.search();
      } else if (res.code === 5002 || res.code === 5001) {
        this.exportResultMsg = res.msg;
        this.exportResultShow = true;
        this.$Message.warning(res.msg);
        if (res.code === 5001) {
          this.exportBatchId = null;
        } else {
          this.exportBatchId = res.data;
        }
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    async exportModule() {
      try {
        const res = await this.$http.post(equipmentassets.exportImportDeviceTemplate);
        await this.$util.common.transformBlob(res.data.data);
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
    async exportResultError() {
      try {
        const res = await this.$http.get(equipmentassets.exportUnlabeledImportDevice, {
          params: {
            exportBatchId: this.exportBatchId,
          },
        });
        await this.$util.common.transformBlob(res.data.data);
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
    goFile() {
      this.$router.push({ name: 'assetfill' });
    },
  },
};
</script>
<style lang="less" scoped>
.ui-select-tabs {
  width: 100%;
}
.key-equipment-report {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);

  .dropdown {
    @{_deep} .ivu-dropdown-item {
      color: var(--color-select-item);
    }
    @{_deep} .ivu-select-dropdown {
      background-color: var(--bg-select-dropdown);
    }
    @{_deep} .ivu-dropdown-item:hover {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active);
      .ivu-btn-text {
        color: var(--color-select-item-active);
      }
    }
    .link {
      text-decoration: underline;
    }
  }
  .charts {
    margin: 20px 0 0 20px;
  }

  .search-module {
    padding: 10px 20px;

    .keyword-input {
      width: 300px;
    }
  }

  .ui-table {
    padding: 0 20px;
  }
  .tooltipType {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.operation {
  margin: 2px 20px 12px 20px;
  display: flex;
  align-items: center;
  .data-list {
    flex: 1;
    display: flex;
    align-items: center;
    .label {
      color: var(--color-label);
      font-size: 14px;
      white-space: nowrap;
      margin-right: 10px;
    }
  }
  .right-btn {
    button {
      margin-left: 12px;
    }
  }
}

@{_deep} .ivu-badge-dot {
  top: 1px;
  right: -15px;
  border: 0;
  z-index: 3;
}

@{_deep} .icon-font {
  font-size: 15px;
}

.btn {
  color: #f5f5f5 !important;
  margin-right: 10px;
}
.add-device {
  text-align: left;
  width: 100%;
}
</style>
