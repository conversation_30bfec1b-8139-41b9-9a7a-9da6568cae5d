<template>
  <div class="head-title">
    <span class="bread-title">{{ `${homeConfig.regionName}设备资产统计` }}</span>
    <ui-switch-tab
      class="ui-switch-tab mr-sm"
      v-model="state"
      :tab-list="stateOptions"
      @changeTab="(val) => $emit('changeTab', val)"
    >
    </ui-switch-tab>
  </div>
</template>
<script>
export default {
  name: 'headTitle',
  props: {
    homeConfig: {
      default: () => ({}),
    },
    stateOptions: {
      default: () => {
        return [
          {
            label: '全量资产',
            value: '1',
          },
          {
            label: '上报资产',
            value: '2',
          },
        ];
      },
    },
    defaultState: {
      default: '1',
    },
  },
  data() {
    return {
      state: '1',
    };
  },
  created() {},
  methods: {},
  watch: {
    defaultState(val) {
      this.state = val;
    },
  },
  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  background: linear-gradient(180deg, rgba(13, 93, 197, 0.6) 0%, rgba(13, 49, 97, 0.6) 100%);
  .bread-title {
    color: #27eee1;
  }
}
.head-title {
  height: 56px;
  line-height: 56px;
  background: var(--bg-nav);
  display: flex;
  justify-content: space-between;
  position: relative;
  @{_deep}.ui-switch-tab {
    display: flex;
    align-items: center;
  }
  .bread-title {
    flex: 1;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    color: var(--color-display-title);
  }
}
</style>
