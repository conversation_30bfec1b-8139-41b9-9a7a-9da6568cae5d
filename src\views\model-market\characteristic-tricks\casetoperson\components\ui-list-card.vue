<template>
  <div
    class="list-card card-box list-card-hover"
    :style="{ backgroundImage: 'url( ' + options[type].cardBg + ')' }"
  >
    <div class="list-card-head">
      <div class="head-left">
        <!-- 实名档案 -->
        <div class="header-icon bg-primary">
          <div class="icon-text">身</div>
        </div>
        <div class="head-con">
          <div class="text primary ellipsis">
            {{ data.gmsfhm }}
          </div>
        </div>
      </div>
    </div>
    <div class="list-card-content cursor-p" @click="archivesDetailHandle">
      <div class="list-card-content-body">
        <div class="content-img card-border-color" @click.stop="">
          <!-- <ui-image viewer :type="type" :src="data[options[type]].photos[0]" /> -->
          <img
            v-lazy="data.photos[0].photoUrl"
            alt=""
            :style="{ height: '100%', width: '100%', objectFit: 'contain' }"
          />
        </div>
        <div class="content-info">
          <div
            v-for="(item, $index) in options[type].info"
            :key="$index"
            class="info-li"
          >
            <div class="info-name" :title="Object.values(item)[0]">
              <i class="iconfont auxiliary-color mr-5" :class="item.icon"></i>
            </div>
            <div class="info-value">
              <!-- 性别 -->
              <div v-if="Object.keys(item)[0] === 'xbdm'">
                {{ data[Object.keys(item)[0]] | commonFiltering(genderList) }}
              </div>
              <!-- 民族 -->
              <div v-else-if="Object.keys(item)[0] === 'mzdm'">
                {{ data[Object.keys(item)[0]] | commonFiltering(nationList) }}
              </div>
              <div
                v-else
                :title="data[Object.keys(item)[0]]"
                v-html="data[Object.keys(item)[0]]"
                class="ellipsis"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: "people",
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    // 是否显示切换按钮
    isChange: {
      type: Boolean,
      default: false,
    },
    index: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      options: {
        people: {
          gmsfhm: "",
          photos: "photos",
          cardBg: require("@/assets/img/card-bg/idcard.png"),
          info: [
            { xm: "姓名", icon: "icon-shenfenzheng" },
            { xbdm: "性别", icon: "icon-xingbie2" },
            { mzdm: "民族", icon: "icon-minzu1" },
            { jgDzmc: "户籍", icon: "icon-location" },
          ],
          key: "photo",
          lables: "标签",
        },
      },
    };
  },
  methods: {
    getScore(val) {
      let a = (val * 100).toFixed(2);
      return a;
    },
    // 详情跳转
    archivesDetailHandle() {
      this.$emit("archivesDetailHandle", this.data);
    },
  },
};
</script>
<style lang="less" scoped>
.list-card {
  width: 316px;
  // height: 166px;
  background-repeat: no-repeat;
  background-position: bottom right;
  border-radius: 4px;
  .list-card-head {
    height: 30px;
    border-bottom: 1px solid #fff;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    justify-content: space-between;
    overflow: hidden;
    border-top-right-radius: 4px;
    .head-left {
      display: flex;
      align-items: center;
      overflow: hidden;
      .header-icon {
        width: 20px;
        height: 20px;
        border-radius: 2px;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        .iconfont {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-text {
          font-size: 12px;
          line-height: 12px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .head-con {
        display: flex;
        flex: 1;
        align-items: center;
        overflow: hidden;
      }
      .text {
        font-size: 16px;
        font-family: "MicrosoftYaHei-Bold";
        font-weight: bold;
        line-height: 22px;
      }
    }
    .change-btn {
      width: 40px;
      height: 100%;
      margin-right: -18px;
      transform: skewX(-18deg);
      display: flex;
      align-items: center;
      padding-left: 8px;
      box-sizing: border-box;
      cursor: pointer;
      .iconfont {
        font-size: 16px;
      }
    }
  }
  .list-card-content {
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .list-card-content-body {
      display: flex;
      .content-img {
        width: 116px;
        height: 116px;
        border: 1px solid #fff;
        position: relative;
        .swiper-container {
          width: 100%;
          height: 100%;
          .swiper-wrapper .swiper-slide {
            width: 116px;
            height: 116px;
            text-align: center;
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              cursor: pointer;
            }
          }
        }
        .swiper-pagination {
          bottom: 0px;
          width: 100%;
        }
        /deep/.swiper-pagination-bullet {
          width: 6px;
          height: 6px;
          margin-left: 5px;
          background: #fff;
          opacity: 1;
        }
        /deep/.swiper-pagination-bullet-active {
          background: #2c86f8;
        }
        .similarity {
          position: absolute;
          left: 0;
          top: 0;
          span {
            padding: 2px 5px;
            background: #4597ff;
            color: #fff;
          }
        }
        .video-icon {
          position: absolute;
          top: -3px;
          right: 0;
          .video-icon-top {
            width: 24px;
            height: 4px;
            background: #ffbd7a;
            transform: skewX(-42deg);
            transform-origin: left top;
          }
          .video-icon-top::after {
            content: "";
            position: absolute;
            top: 0.5px;
            right: -1px;
            width: 0;
            height: 0;
            border-bottom: 2px solid #cf7820;
            border-left: 2px solid transparent;
            transform: rotate(-50deg);
          }
          .video-icon-bottom {
            color: #fff;
            font-size: 12px;
            line-height: 16px;
            background: #f29f4c;
            width: 24px;
            height: 16px;
            text-align: center;
            position: absolute;
            left: -3px;
            top: 3px;
          }
          .video-icon-bottom::before {
            content: "";
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-right: 12px solid transparent;
            position: absolute;
            left: 0;
            top: 16px;
          }
          .video-icon-bottom::after {
            content: "";
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-left: 12px solid transparent;
            position: absolute;
            right: 0;
            top: 16px;
          }
        }
      }
      .content-info {
        width: 170px;
        padding-left: 10px;
        flex: 1;
        box-sizing: border-box;
        position: relative;
        .info-li {
          display: flex;
          // align-items: center;
          margin-bottom: 5px;
          .info-name {
            font-size: 12px;
            line-height: 18px;
            color: #181818;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            white-space: nowrap;
          }
          .info-value {
            font-size: 14px;
            line-height: 20px;
            color: #484847;
            width: 180px;
            overflow: hidden;
          }
        }
        .believed-flag {
          position: absolute;
          right: -10px;
          top: -5px;
          width: 64px;
          height: 24px;
          text-align: center;
          line-height: 24px;
          color: #fff;
          background: linear-gradient(144deg, #3cd2aa 0%, #1faf8a 100%);
          border-radius: 100px 0px 0px 100px;
        }
      }
    }
  }
}

.list-card {
  position: relative;
  background-color: #f9f9f9;
  margin-bottom: 10px;
  height: min-content;
  overflow: hidden;
  box-sizing: border-box;
  .operate-bar {
    height: 30px;
    background: linear-gradient(
      90deg,
      rgba(87, 187, 252, 0.8) 0%,
      #2c86f8 100%
    );
    border-radius: 0px 0px 4px 0px;
    position: absolute;
    right: -100%;
    transition: all 0.3s;
    bottom: 0;
    transform: skewX(-20deg);
    .operate-content {
      padding: 0 5px;
      transform: skewX(20deg);
      height: 100%;
      display: flex;
      align-items: center;
      color: #fff;
      /deep/ .ivu-tooltip-rel {
        padding: 6px;
      }
    }
  }
  &:hover {
    border: 1px solid #2c86f8;
    // padding: 9px;
    .operate-bar {
      right: -6px;
      bottom: -1px;
    }
    &:before {
      border-color: #2c86f8;
    }
    .img-content {
      .check-box {
        display: inline-block;
      }
    }
  }
}

.collection {
  width: 30px;
  height: 30px;
  position: absolute;
  // background: #F29F4C;
  z-index: 99;
  top: 3px;
  right: 3px;
  .bg {
    width: 0px;
    height: 0px;
    border-left: 30px solid transparent;
    border-top: 30px solid #f29f4c;
  }
  .collection-icon {
    position: absolute;
    top: -1px;
    right: 1px;
    /deep/ .iconfont {
      font-size: 14px;
      color: #fff;
    }
    .ivu-tooltip-rel {
      margin-top: 3px;
    }
    /deep/ .icon-shoucang {
      color: #888888 !important;
      text-shadow: 0px 1px 0px #e1e1e1;
    }
    /deep/ .icon-yishoucang {
      color: #f29f4c !important;
    }
  }
}
</style>
