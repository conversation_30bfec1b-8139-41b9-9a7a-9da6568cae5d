<!--
    * @FileDescription: 落脚地分析 落脚点
    * @Author: H
    * @Date: 2023/01/13
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="rightBox" :class="{'rightBox-pack': packUpDown}">
        <div class="rightBox-page">
            <div class="title">
                <p>{{ title }}</p>
                <Icon type="ios-close" @click="handleCancel" />
            </div>
            <div class="hint_title">
                共<span> {{ 1 }} </span> 条检索结果
            </div>
            <ul class="box-content" v-infinite-scroll="load">
                <li class="box-list" v-for="(item, index) in dataList" :key="index">
                    <div class="box-number">
                        <span>{{ index + 1 }}</span>
                    </div>
                    <div class="box-right">
                        <div class="box-right-img">
                            <img v-lazy="item.sceneImg" alt="" />
                        </div>
                        <div class="box-right-details">
                            <div class="details-head">
                                <p>{{ item.Nonumber }}</p>
                                <i class="iconfont icon-dangan2" @click="handleDetails(item)"></i>
                                <!-- <ui-icon type="dangan2" :size="14" @click="handleDetails(item)"></ui-icon> -->
                            </div>
                            <p class="details-type">人脸抓拍机</p>
                            <p class="details-number">落脚 <span>1</span> 次</p>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="footer" :class="{packArrow: packUpDown}" @click="handlePackup">
                <img :src="packUrl" alt="">
                <p>{{ packUpDown ? '展开' : '收起'}} </p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            title: '落脚点',
            dataList: [
                {
                    'Nonumber': '',
                    'sceneImg': ''
                }, 
                {
                    'Nonumber': '',
                    'sceneImg': ''
                }
            ],
            packUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png')
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        init(value) {
            this.dataList.forEach(item =>{
                item.gmsfhm = value.gmsfhm;
                item.sceneImg = value.photo || value.photoUrl;
            })
        },
        load() {

        },
        // 详情
        handleDetails(item) {
            this.$emit('details', item)
        },
        handleCancel() {
            this.$emit('cancel')
        },
        // 收缩、展开
        handlePackup() {
            this.packUpDown = !this.packUpDown;
        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.rightBox{
    width: 370px;
    position: absolute;
    right: 10px;
    top: 10px;
    background: #fff;
    height: calc( ~'100% - 30px' );
    transition: height 1s ease-out;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    &-page{
        height:100%;
        width: 100%;
    }
    .hint_title{
        margin: 10px 0 0 15px;
        font-size: 12px;
        color: rgba(0,0,0,0.6);
        span{
            color: rgba(44, 134, 248, 1);
        }
    }
    .box-content{
        padding: 0 15px;
        height: calc( ~'100% - 110px' );
        overflow-y: auto;
        .box-list{
            display: flex;
            margin-bottom: 10px;
            align-items: center;
            .box-number{
                position: relative;
                background: url('~@/assets/img/map/trajectory-red.png') no-repeat;
                width: 32px;
                height: 32px;
                color: #EA4A36;
                > span {
                    position: absolute;
                    top: 0px;
                    width: 32px;
                    font-size: 12px;
                    color: #ea4a36;
                    text-align: center;
                }
            }
            .box-right{
                display: flex;
                justify-content: space-between;
                background: #F9F9F9;
                padding: 5px 10px 5px 5px;
                width: 100%;
                margin-left: 6px;
                &-img{
                    width: 80px;
                    height: 80px;
                    border: 1px solid #CFD6E6;
                    img{
                        width: 100%;
                        height: 100%;
                    }
                }
                &-details{
                    margin-left: 10px;
                    flex: 1;
                    .details-head{
                        display: flex;
                        justify-content: space-between;
                        font-weight: bold;
                        color: #2C86F8;
                        font-size: 14px;
                        i{
                            cursor: pointer;
                        }
                        .icon-dangan2{
                            color: #2C86F8 !important;
                        }
                    }
                    .details-type{
                        font-size: 14px; 
                        color: rgba(0,0,0,0.9); 
                        margin-top: 6px;
                    }
                    .details-number{
                        color: rgba(0,0,0,0.6);
                        font-size: 14px;
                        margin-top: 6px;
                        span{
                            color: #2C86F8;
                        }
                    }
                }
            }
        }
    }
}
.rightBox-pack{
    height: 80px;
    transition: height 1s ease-out;
    overflow: hidden;
}
.footer{
    color: #000000;
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 96%;
}
</style>
