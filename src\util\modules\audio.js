import { RecordRTCPromisesHandler } from "recordrtc";
import { Client } from "@/@gradio/client/dist";
import { Message } from "element-ui";
import { Notice } from "view-design";

export default class AudioHandler {
  constructor() {
    this.recorder = null;
    this.stream = null;
  }

  async gradioFunction(exampleAudio) {
    const client = await Client.connect(gradioServerApi);
    const result = await client.predict("/model_inference", {
      input_wav: exampleAudio,
      language: "auto",
    });
    return result;
  }

  async startAudio() {
    const permission = await this.checkMediaPermission();
    if (!permission) {
      throw new Error("麦克风权限被拒绝");
    }
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: true,
      });
    } catch (err) {
      if (err.name === "NotFoundError") {
        Message.error("无麦克风设备");
      }
      throw err;
    }
    this.recorder = new RecordRTCPromisesHandler(this.stream, {
      type: "audio",
      mimeType: "audio/wav",
    });
    this.recorder.startRecording();
  }

  async endAudio() {
    if (!this.recorder || !this.stream) {
      return Message.error("麦克风未开启");
    }
    await this.recorder.stopRecording();
    let blob = await this.recorder.getBlob();
    this.stream.getAudioTracks().forEach((track) => {
      track.stop();
    });
    this.stream = null;
    this.recorder.destroy();
    const result = this.gradioFunction(blob);
    return result;
  }

  async checkMediaPermission() {
    if (navigator.mediaDevices && navigator.permissions) {
      const result = await navigator.permissions.query({ name: "microphone" });
      if (result.state === "granted") {
        console.log("麦克风权限已授权");
      } else if (result.state === "denied") {
        Message.error("麦克风权限被拒绝");
        Notice.info({
          title: "请确认以下配置是否完成",
          render: (h) => {
            return h("div", { style: "fontSize: 15px;fontWeight: bold;" }, [
              h("p", { style: "marginTop: 10px;" }, "1.接入麦克风设备"),
              h("p", { style: "marginTop: 10px;" }, "2.允许浏览器使用麦克风"),
              h(
                "p",
                { style: "fontSize:14px;fontWeight: normal; marginTop: 5px;" },
                "在浏览器地址栏输入：chrome://settings/content/microphone 进入配置界面,将麦克风默认行为设置为：网站可以请求使用您的麦克风"
              ),
              h("p", { style: "marginTop: 10px;" }, "3.允许本地址使用媒体设备"),
              h(
                "p",
                { style: "fontSize:14px;fontWeight: normal;marginTop: 5px;" },
                "在浏览器地址栏输入：chrome://flags/#unsafely-treat-insecure-origin-as-secure 进入配置界面，" +
                  "先将 Insecure origins treated as secure 设为：Enabled，然后再配置文本框中填入：本网站地址，最后点击右下角提示的Relaunch按钮重新进入浏览器"
              ),
            ]);
          },
          duration: 0,
        });
        return false;
      } else if (result.state === "prompt") {
        console.log("麦克风权限需要用户确认");
      }
      return true;
    } else {
      Message.error("浏览器不支持使用麦克风设备");
      return false;
    }
  }
}
