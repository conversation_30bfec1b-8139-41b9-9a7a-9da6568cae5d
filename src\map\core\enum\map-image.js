export default {
  MARKER: {
    RED_START: {
      url: require("@/assets/img/map/track/red-start.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    RED_END: {
      url: require("@/assets/img/map/track/red-end.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    BLUE_START: {
      url: require("@/assets/img/map/track/blue-start.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    BLUE_END: {
      url: require("@/assets/img/map/track/blue-end.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    GREEN_START: {
      url: require("@/assets/img/map/track/green-start.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    GREEN_END: {
      url: require("@/assets/img/map/track/green-end.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    ORANGE_START: {
      url: require("@/assets/img/map/track/orange-start.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    ORANGE_END: {
      url: require("@/assets/img/map/track/orange-end.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    PURPLE_START: {
      url: require("@/assets/img/map/track/purple-start.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    PURPLE_END: {
      url: require("@/assets/img/map/track/purple-end.png"),
      size: {
        width: 23,
        height: 37,
      },
    },
    TRACK_CAR: {
      url: require("@/assets/img/map/track/track-car-marker-onmap.png"),
      size: {
        width: 31,
        height: 22,
      },
      markerType: 1,
    },
    TRACK_CAR_HOVER: {
      url: require("@/assets/img/map/track/track-car-marker-hover-onmap.png"),
      size: {
        width: 31,
        height: 22,
      },
      markerType: 1,
    },
    COINCIDE: {
      url: require("@/assets/img/map/track/coincide.png"),
      size: {
        width: 28,
        height: 32,
      },
    },
    COINCIDE_HIGHLIGHT: {
      url: require("@/assets/img/map/track/coincide-highlight.png"),
      size: {
        width: 30,
        height: 30,
      },
    },
    POLICE_CAR: {
      url: require("@/assets/img/map/track/gps-monitor-car.png"),
      size: {
        width: 30,
        height: 40,
      },
    },
    GUARDROUTE_CAMERA_PASS: {
      url: require("@/assets/img/map/track/flash_prevCamera1.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    GUARDROUTE_CAMERA_NOW: {
      url: require("@/assets/img/map/track/flash_currCamera1.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    GUARDROUTE_CAMERA_NEXT: {
      url: require("@/assets/img/map/track/flash_nextCamera1.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
  },
  DEVICE: {
    CAMERA_QIUJI: {
      url: require("@/assets/img/map/mapPoint/map-qiuji.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_QIUJI_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-qiuji_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_QIANGJI: {
      url: require("@/assets/img/map/mapPoint/map-qiangji.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_QIANGJI_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-qiangji_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_FACE: {
      url: require("@/assets/img/map/mapPoint/map-face.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_FACE_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-face_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_VEHICLE: {
      url: require("@/assets/img/map/mapPoint/map-vehicle.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_VEHICLE_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-vehicle_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_WIFI: {
      url: require("@/assets/img/map/mapPoint/map-wifi.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_WIFI_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-wifi_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_RFID: {
      url: require("@/assets/img/map/mapPoint/map-rfid.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_RFID_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-rfid_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_ELECTRIC: {
      url: require("@/assets/img/map/mapPoint/map-electric.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_ELECTRIC_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-electric_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_ETC: {
      url: require("@/assets/img/map/mapPoint/map-case.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
    CAMERA_ETC_HOVER: {
      url: require("@/assets/img/map/mapPoint/map-case_active.png"),
      size: {
        width: 24,
        height: 24,
      },
    },
  },
};

// 根据设备信息返回对应图标
export const getImageByType = (device) => {
  let type = "";
  switch (device.deviceType) {
    case "1":
      if (origin.deviceChildType == "1" || origin.deviceChildType == "2") {
        type = "CAMERA_QIUJI";
      } else {
        type = "CAMERA_QIANGJI";
      }
      break;
    case "11":
      type = "CAMERA_FACE";
      break;
    case "2":
      type = "CAMERA_VEHICLE";
      break;
    case "3":
      type = "CAMERA_WIFI";
      break;
    case "5":
      type = "CAMERA_RFID";
      break;
    case "4":
      type = "CAMERA_ELECTRIC";
      break;
    case "16":
      type = "CAMERA_ETC";
      break;
    default:
      type = "CAMERA_QIANGJI";
      break;
  }
  return {
    normal: type,
    hover: type + "_HOVER",
  };
};
