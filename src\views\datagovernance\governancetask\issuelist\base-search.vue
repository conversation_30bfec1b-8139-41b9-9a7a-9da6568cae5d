<template>
  <div class="base-search">
    <div class="over-flow">
      <ui-label class="inline rigth-margin bottom-margin" label="关键词" :width="50">
        <Input class="input-width" placeholder="请输入设备名称/设备编码" v-model="searchData.keyWord"></Input>
      </ui-label>
      <ui-label class="inline rigth-margin bottom-margin" label="问题来源" :width="66">
        <Select class="select-width" placeholder="请选择问题来源" clearable v-model="searchData.dataSource">
          <Option v-for="(item, index) in data_source" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline rigth-margin bottom-margin" :label="global.filedEnum.sbgnlx">
        <Select
          v-model="searchData.sbgnlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
          class="select-width"
        >
          <Option v-for="(item, index) in sxjgnlx_receive" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline rigth-margin bottom-margin" :label="global.filedEnum.sbdwlx" :width="100">
        <Select
          v-model="searchData.sbdwlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          class="select-width"
        >
          <Option v-for="(item, index) in propertySearch_sbdwlx" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline rigth-margin bottom-margin" label="设备类型" :width="66">
        <Select v-model="searchData.ptztype" clearable placeholder="请选择设备类型" class="select-width">
          <Option v-for="(item, index) in propertySearch_ptztype" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <!-- <ui-label class="inline rigth-margin bottom-margin" label="是否生成工单" :width="100">
        <Select
          v-model="searchData.generateStatus"
          clearable
          placeholder="请选择是否生成工单"
          class="select-width"
        >
          <Option :value="0">已生成</Option>
          <Option :value="1">未生成</Option>
        </Select>
      </ui-label> -->
      <ui-label class="inline rigth-margin bottom-margin" label="是否生成工单" :width="94">
        <Select class="select-width" placeholder="请选择生成工单" clearable v-model="searchData.generateStatus">
          <Option v-for="(item, index) in dataStatuList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" class="mr-sm" @click="$emit('startSearch', searchData)"> 查询 </Button>
        <Button type="default" @click="resetSearchDataMx(searchData, () => $emit('startSearch', searchData))">
          重置
        </Button>
      </div>
      <!-- <ui-label :width="60" class="inline rigth-margin bottom-margin" label="">
        
      </ui-label> -->
    </div>
    <div class="select-nav">
      <div class="select-div">
        <span class="select-span">异常原因：</span>
        <ui-select-tabs :list="error_type" :need-expand="false" @selectInfo="selectInfo" class="tabs-div">
        </ui-select-tabs>
      </div>
      <Button type="primary" class="button-blue" @click="exportDevice">
        <i class="icon-font icon-daochu delete-icon"></i>
        <span class="inline vt-middle ml-sm">导出</span>
      </Button>
      <Button type="primary" class="button-blue ml-sm" @click="importData">
        <i class="icon-font icon-huoquyichangshebei delete-icon"></i>
        <span class="inline vt-middle ml-sm">获取异常设备</span>
      </Button>
    </div>
  </div>
</template>
<script>
import user from '@/config/api/user';
export default {
  props: {},
  data() {
    return {
      dataSourceList: [],
      searchData: {
        keyWord: '',
        type: '',
        sbgnlx: '',
        sbdwlx: '',
        ptztype: '',
        dataSource: '',
        errorReasonItem: [],
        generateStatus: '0',
      },
      dicDataEnum: Object.freeze({
        error_type: 'error_type',
        sxjgnlx_receive: 'sxjgnlx_receive',
        data_source: 'data_source',
        propertySearch_sbdwlx: 'propertySearch_sbdwlx',
        propertySearch_ptztype: 'propertySearch_ptztype',
      }),
      data_source: [],
      error_type: [],
      sxjgnlx_receive: [],
      propertySearch_sbdwlx: [],
      propertySearch_ptztype: [],
      dataStatuList: [],
    };
  },
  async activated() {
    this.dataStatuList = await this.initSource('DEVICE_DETECTION_GENERATE_STATUS');
    this.dataStatuList = this.dataStatuList.reverse();
    this.copySearchDataMx(this.searchData);
    this.initDicContent();
  },
  methods: {
    selectInfo(infoList) {
      this.searchData.errorReasonItem = infoList.map((item) => item.name);
      this.$emit('startSearch', this.searchData);
    },
    async initDicContent() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = data.data.find((row) => {
            return !!row[key];
          });
          this[this.dicDataEnum[key]] = obj[key];
        });
        this.error_type = this.error_type.map((item) => {
          return {
            typeKey: item.typeKey,
            name: item.dataValue,
            select: false,
            dataKey: item.dataKey,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    async initSource(typekey) {
      try {
        let res = await this.$http.get(user.queryByTypeKey, {
          params: { typekey: typekey },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    exportDevice() {
      this.$emit('exportDevice');
    },
    importData() {
      this.$emit('importData');
    },
  },
  watch: {},
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.rigth-margin {
  margin-right: 20px;
}
@media only screen and (max-width: 1480px) {
  .rigth-margin {
    margin-right: 13px;
  }
}
.bottom-margin {
  //   margin-bottom: 15px;
}
.base-search {
  .select-width {
    width: 156px;
  }
  .input-width {
    width: 180px;
  }
  .ui-label {
    line-height: 40px;
  }
  .select-nav {
    width: 100%;
    margin-top: 20px;
    display: flex;
    align-items: center;
  }
  .select-div {
    flex: 1;
    display: flex;
    .tabs-div {
      width: 100%;
    }
    .select-span {
      display: inline-block;
      height: 45px;
      line-height: 45px;
      color: #fff;
      font-size: 14px;
      width: 75px;
    }
    .tabs {
      flex: 1;
    }
  }
}
</style>
