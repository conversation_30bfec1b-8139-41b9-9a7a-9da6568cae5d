export default {
  listPageOutlineDevice: '/ivdg-work-order-service/device/task/listPageOutlineDevice', //异常设备条件查询
  logicDelete: '/ivdg-work-order-service//device/task/delete', //删除问题设备
  createDeviceOrder: '/ivdg-work-order-service/device/task/createDeviceOrder', //生成问题清单
  getDeviceExcelTemplate: '/ivdg-work-order-service/device/task/getDeviceExcelTemplate', //下载exccel模板

  getDevicePageList: '/ivdg-work-order-service/detection/pageList', // 问题清单查询
  removetDevice: '/ivdg-work-order-service/detection/remove', // 移除问题设备
  exportDevice: '/ivdg-work-order-service/detection/exportDevice', //导出问题清单

  batchAddOrder: '/ivdg-work-order-service/workOrder/batchAdd', // 批量新增治理工单
  saveCustomTask: '/ivdg-work-order-service/workOrder/saveCustomTask', // 批量保存自定义问题
  // viewOrderMessage: '/ivdg-work-order-service/workorder/view',
  getWorkOrderList: '/ivdg-work-order-service/workOrder/pageList', // 移除问题设备
  updateWorkOrder: '/ivdg-work-order-service/workOrder/update', // 治理工单更新

  batchHandle: '/ivdg-work-order-service/flow/batchHandle', // 单个或批量处理工单流程
  batchClose: '/ivdg-work-order-service/workOrder/batchClose', // 单个或批量关闭工单
  batchAssign: '/ivdg-work-order-service/flow/batchAssign', // 单个或批量指派工单流程
  remove: '/ivdg-work-order-service/workOrder/batchRemove', // 单个或批量删除工单

  importExcept: '/ivdg-work-order-service/detection/importErrorDevice', // 导入异常设备
  viewOrderMessage: '/ivdg-work-order-service/workOrder/getDeviceInfo', // 工单编辑 - 查阅设备信息
  recordMessage: '/ivdg-work-order-service/workOrder/view', // 查看工单 - 操作记录
  getErrorDictData: '/ivdg-work-order-service/workOrder/errorReason', // 获取异常原因

  listTaskSchemes: '/ivdg-evaluation-app/evaluation/app/taskScheme/pageList', // 任务列表
  taskIndexResultPageList: '/ivdg-evaluation-app/evaluation/app/taskScheme/taskIndexPageList', // 指标名称
  queryReasonList: '/ivdg-evaluation-engine-service/evaluation/workOrder/queryReasonList', //新建工单 处理异常下拉框
  getFirstModelData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getFirstModelData', // 查询异常设备
  pageListExcludeDeviceId: '/ivdg-evaluation-engine-service/evaluation/workOrder/getForGovernanceDeviceList', // 查询异常设备
  batchSign: '/ivdg-work-order-service/workOrder/batchSign', // 签收工单
  pageListExport: '/ivdg-work-order-service/workOrder/pageListExport', // 工单的统计
  getDeviceWorkOrderOrgCode: '/ivdg-work-order-service/deviceWorkOrderOrgCode/queryList', //获取已配置的单位工单接收人
  getStatisticsList: '/ivdg-work-order-service/workOrder/statisticsList', // 单位统计 类型统计列表
  statisticsListExport: '/ivdg-work-order-service/workOrder/statisticsListExport', // 单位统计 类型统计导出
  batchBeforeStatistics: '/ivdg-work-order-service/workOrder/batchBeforeStatistics', // 批量操作前统计
  batchPageList: '/ivdg-work-order-service/workOrder/batchPageList', // 批量工单分页列表
  deviceWorkOrderUserColQueryList: '/ivdg-work-order-service/deviceWorkOrderUserCol/queryList', // 查询工单统计用户列头配置列表
  deviceWorkOrderUserColUpdate: '/ivdg-work-order-service/deviceWorkOrderUserCol/update', // 工单统计用户列头配置编辑
  //工单详情操作记录
  queryOsdDetailByBatchIdAndDeviceId:
    '/ivdg-work-order-service/orderVideoDeviceDetail/queryOsdDetailByBatchIdAndDeviceId', //get请求：  workOrderFlowId Int类型  deviceId String类型
  queryDetailDetail: '/ivdg-work-order-service/orderVideoDeviceDetail/queryDetailDetail', //get请求：  workOrderFlowId Int类型  deviceId String类型
  orderFaceDetailGetPopUpData: '/ivdg-work-order-service/orderFaceDetail/getPopUpData', //人脸post workOrderFlowId Int类型  deviceId String类型
  orderVehicleDetailGetPopUpData: '/ivdg-work-order-service/orderVehicleDetail/getPopUpData', //车辆post  workOrderFlowId Int类型  deviceId String类型
  //工单撤回
  workOrderSignCancel: '/ivdg-work-order-service/workOrder/signCancel', //签收撤回
  workOrderAssignCancel: '/ivdg-work-order-service/workOrder/assignCancel', //指派撤回
  //单位工单-任务模式
  queryTaskList: '/ivdg-work-order-service/workOrder/queryTaskList', //post任务列表
  taskListExport: '/ivdg-work-order-service/workOrder/taskListExport', //任务模式导出
  getAutoConfiguration: '/ivdg-work-order-service/workOrder/autoConfiguration/getList', //查询配置列表
  updateAutoConfiguration: '/ivdg-work-order-service/workOrder/autoConfiguration/batchUpdate', //批量更新配置列表
  updateAssignList: '/ivdg-work-order-service/workOrder/autoConfiguration/updateAssignList', //批量配置各单位工单接收人
};
