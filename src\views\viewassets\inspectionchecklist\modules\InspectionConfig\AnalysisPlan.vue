<template>
  <div class="analysis-plan" v-ui-loading="{ loading: loading }">
    <plan-time-picker
      class="inline"
      ref="planTimePicker"
      :time-picker-data="timePickerData"
      @handleUpatePlanData="handleUpatePlanData"
    ></plan-time-picker>
  </div>
</template>
<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    analysisPlanConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
      timePickerData: {
        cronType: '1',
        cronData: [],
        timePoints: [],
      },
    };
  },
  watch: {
    analysisPlanConfig: {
      handler(val) {
        this.timePickerData = val;
      },
      deep: true,
    },
  },
  methods: {
    handleUpatePlanData(val) {
      this.formData.cronType = val.cronType;
      this.formData.cronData = val.cronData;
      if (val.timePoints) {
        this.formData.timePoints = val.timePoints;
      }
    },
    save() {
      const configInfo = this.formData;
      this.$emit('save', configInfo);
    },
  },
  components: {
    PlanTimePicker:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/plan-time-picker')
        .default,
  },
};
</script>
<style lang="less" scoped>
.analysis-plan {
  border: 1px solid var(--bg-collapse-item);
  padding: 10px 20px;
  margin-bottom: 20px;
}
</style>
