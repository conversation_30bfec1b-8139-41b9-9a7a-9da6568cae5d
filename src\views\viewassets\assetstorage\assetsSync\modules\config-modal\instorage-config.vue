<template>
  <div class="instorage-config mb-lg">
    <section class="section-box">
      <ui-label class="mb-md" label="设备是否自动入库（资产库）：">
        <RadioGroup v-model="searchParams.isStorage">
          <Radio label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </ui-label>
      <!-- 自动入库才展示 -->
      <ui-label class="mb-md ml-sp" label="自动入库（资产库）条件：" v-if="searchParams.isStorage === '1'">
        <CheckboxGroup class="align-flex wrap" v-model="storageCondition" @on-change="changeIsStorage">
          <Checkbox v-for="(item, index) in propertyFields" :key="index" class="align-flex flex-4" :label="item.value">
            {{ item.label }}
          </Checkbox>
        </CheckboxGroup>
      </ui-label>
    </section>
    <ui-switch-tab class="ui-switch-tab mb-sm" v-model="tabs" :tab-list="stateOptions"> </ui-switch-tab>
    <section class="section-box" v-show="tabs === 'add'">
      <p class="explain mt-md">新设备：以【国标编码】为标识，资产库不存在的设备！</p>
      <ui-label class="mt-md mb-md" label="是否入库：">
        <RadioGroup v-model="searchParams.isAddStorage">
          <Radio label="1">入库</Radio>
          <Radio label="0">舍弃</Radio>
        </RadioGroup>
      </ui-label>
    </section>
    <div class="compare-base-wrapper" v-show="tabs === 'add'">
      <transfer-table
        :left-table-columns="newLeftColumns"
        :right-table-columns="newRightColumns"
        :left-table-data="newLeftTableData"
        :right-table-data="newTargetList"
        @onLeftToRight="onLeftToRight"
      >
        <template #left-title>
          <div class="color-title mb-sm">待选择待入库字段</div>
        </template>
        <template #right-title>
          <div class="color-title mb-sm">需入库字段</div>
        </template>
      </transfer-table>
    </div>
    <section class="section-box" v-show="tabs === 'has'">
      <p class="explain mt-md">已有设备：以【国标编码】为标识，资产库已存在的设备！</p>
      <ui-label label="是否入库：" class="mt-md mb-md">
        <RadioGroup v-model="searchParams.isUpdateStorage">
          <Radio class="mr-lg" label="1">是</Radio>
          <Radio label="0">否</Radio>
        </RadioGroup>
      </ui-label>
      <p class="mt-md mb-md base-text-color">
        1）待入库设备的字段为空时：
        <RadioGroup class="ml-md" v-model="searchParams.fieldNull">
          <Radio label="0">覆盖资产库值</Radio>
          <Radio label="1">保留资产库值</Radio>
        </RadioGroup>
      </p>
      <p class="mb-md base-text-color">
        2）待入库设备的字段不为空时：
        <RadioGroup v-model="searchParams.fieldNotNull">
          <Radio label="0">覆盖资产库值</Radio>
          <Radio label="1">保留资产库值</Radio>
        </RadioGroup>
      </p>
    </section>
    <div class="compare-base-wrapper" v-show="tabs === 'has'">
      <p class="mb-md base-text-color">3）以下字段需特殊处理：</p>
      <compare-base
        :property-list="newPropertyList"
        :default-params="!!defaultParams && 'storageParam' in defaultParams ? defaultParams.storageParam : ''"
        @selectionChange="selectionChange"
      >
        <template #left-title>
          <div class="color-title mb-sm">待选择待入库字段</div>
        </template>
        <template #right-title>
          <div class="color-title mb-sm">请确定入库策略</div>
        </template>
      </compare-base>
    </div>
    <section class="other-section-box" v-show="tabs === 'other'">
      <CheckboxGroup v-model="otherCondition" @on-change="changeOtherCondition">
        <Checkbox v-for="(item, index) in otherConditionList" :key="index" class="check-item mb-lg" :label="item.value">
          {{ item.label }}
        </Checkbox>
      </CheckboxGroup>
    </section>
  </div>
</template>
<script>
export default {
  props: {
    // 共享联网平台同步: 2 | 视图库同步:3 |一机一档同步:4
    activeStore: {},
    propertyList: {
      default: () => [],
    },
    // 默认配置
    defaultParams: {},
  },
  created() {
    this.newPropertyList = this.$util.common.deepCopy(this.propertyList);
    // 设备自动入库才展示自动入库条件
    this.defaultParams ? this.handleIsDefault() : null;
  },
  activated() {},
  data() {
    return {
      tabs: 'add',
      stateOptions: Object.freeze([
        {
          label: '新增设备入库策略',
          value: 'add',
        },
        {
          label: '已有设备入库策略',
          value: 'has',
        },
        {
          label: '其他处理策略',
          value: 'other',
        },
      ]),
      newPropertyList: [],
      searchParams: {
        storageParam: {},
        addStorageParam: [],
        isUpdateStorage: '0',
        isStorage: '1',
        fieldNull: '',
        fieldNotNull: '',
        isAddStorage: '1',
      },
      storageCondition: [],
      otherCondition: [],
      otherConditionData: Object.freeze([
        { label: '联网平台不存在的设备自动删除【联网平台】来源标识。', value: '1' },
        { label: '所有平台（联网平台、视图库、一机一档等）均不存在的设备自动置为【不可用/拆除】状态。', value: '2' },
        { label: '所有平台（联网平台、视图库、一机一档等）均不存在的设备自动删除。', value: '3' },
        { label: '本次同步设备自动设置为【可用/在用】状态。', value: '4' },
      ]),
      propertyFields: Object.freeze([
        { label: '未检测设备', value: '0' },
        { label: '检测通过设备', value: '1' },
        { label: '检测不通过设备', value: '2' },
      ]),
      targetList: [],
      //新增设备入库策略
      newLeftColumns: [
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      newRightColumns: [
        {
          title: '字段名',
          key: 'fieldName',
        },
        {
          title: '注释',
          key: 'fieldRemark',
        },
        {
          title: '操作',
          key: 'action',
          width: 60,
          align: 'center',
          render: (h, { row, index }) => {
            if (row.fieldName === 'deviceId') {
              return <span class="font-active-color">--</span>;
            } else {
              return h(
                'span',
                {
                  props: {
                    row: row,
                  },
                  class: ['font-active-color', 'pointer'],
                  on: {
                    click: () => {
                      this.newTargetList.splice(index, 1);
                      this.newLeftTableData.forEach((item) => {
                        if (item.propertyName === row.fieldName) {
                          this.$set(item, '_checked', false);
                        }
                      });
                      this.searchParams.addStorageParam = JSON.stringify(this.newTargetList);
                    },
                  },
                },
                '移除',
              );
            }
          },
        },
      ],
      newTargetList: [],
      newLeftTableData: [],
      newLeftLoading: false,
      newRightLoading: false,
    };
  },
  computed: {
    otherConditionList() {
      return this.activeStore === '2'
        ? this.otherConditionData
        : this.otherConditionData.filter((row) => row.value === '4');
    },
  },
  methods: {
    setDefaultAddStorage() {
      let addStorageParam = JSON.parse(
        this.defaultParams.addStorageParam ||
          '[{"fieldName":"deviceId","fieldRemark":"设备编码"},{"fieldName":"sourceId","fieldRemark":"数据来源"},{"fieldName":"sbgnlx","fieldRemark":"摄像机功能类型"},{"fieldName":"deviceName","fieldRemark":"设备名称"},{"fieldName":"ipAddr","fieldRemark":"IP地址"},{"fieldName":"civilCode","fieldRemark":"行政区域代码"}]',
      );
      let newTargetList = [];
      this.newLeftTableData.forEach((item) => {
        let param = addStorageParam.find((value) => item.propertyName === value.fieldName);
        if (param) {
          newTargetList.push(param);
          this.$set(item, '_checked', true);
        } else {
          this.$set(item, '_checked', false);
        }
        if (item.propertyName === 'deviceId') {
          this.$set(item, '_disabled', true);
        }
      });
      this.newTargetList = newTargetList;
    },
    onLeftToRight(storageParam) {
      this.newTargetList = storageParam.map((item) => {
        return {
          fieldName: item.propertyName,
          fieldRemark: item.propertyColumn,
        };
      });
      this.newLeftTableData.forEach((item) => {
        storageParam.forEach((value) => {
          if (item.propertyName === value.propertyName) {
            this.$set(item, '_checked', true);
          }
        });
      });
      this.searchParams.addStorageParam = JSON.stringify(this.newTargetList);
    },
    selectionChange(storageParam) {
      this.searchParams.storageParam = JSON.stringify(storageParam);
    },
    // handleStrageParams (selection) {
    //   this.searchParams.storageParam = JSON.stringify(selection.map((item) => {
    //     let obj = {}
    //     obj.fieldName = item.propertyName
    //     obj.fieldRemark = item.propertyColumn
    //     obj.addType = item.addType
    //     return obj
    //   }))
    // },
    handleIsDefault() {
      this.searchParams.addStorageParam = this.defaultParams.addStorageParam;
      this.searchParams.storageParam = this.defaultParams.storageParam;
      this.searchParams.isUpdateStorage = this.defaultParams.isUpdateStorage;
      this.searchParams.isStorage = this.defaultParams.isStorage;
      this.searchParams.fieldNull = this.defaultParams.fieldNull;
      this.searchParams.fieldNotNull = this.defaultParams.fieldNotNull;
      // 设备自动入库才展示自动入库条件
      if (this.searchParams.isStorage === '1') {
        this.searchParams.storageCondition = this.defaultParams.storageCondition;
        this.defaultParams.storageCondition
          ? (this.storageCondition = this.defaultParams.storageCondition.split(','))
          : null;
      }
      this.searchParams.otherCondition = this.defaultParams.otherCondition;
      this.defaultParams.otherCondition ? (this.otherCondition = this.defaultParams.otherCondition.split(',')) : null;
    },
    changeOtherCondition() {
      this.searchParams.otherCondition = '';
      if (!this.otherCondition.length) return;
      this.searchParams.otherCondition = this.otherCondition.join(',');
      this.$emit('updateParams', this.searchParams);
    },
    changeIsStorage() {
      this.searchParams.storageCondition = '';
      if (!this.storageCondition.length) return;
      this.searchParams.storageCondition = this.storageCondition.join(',');
      this.$emit('updateParams', this.searchParams);
    },
  },
  watch: {
    searchParams: {
      handler() {
        // 设备自动入库才展示自动入库条件
        if (this.searchParams.isStorage === '1') {
          this.storageCondition = this.storageCondition.filter((item) => !!item);
          this.searchParams.storageCondition = this.storageCondition.join(',');
        } else {
          this.searchParams.storageCondition = '';
          this.storageCondition = [];
        }
        this.$emit('updateParams', this.searchParams);
      },
      deep: true,
    },
    propertyList: {
      deep: true,
      immediate: true,
      handler(val) {
        this.newLeftTableData = this.$util.common.deepCopy(val);
        this.setDefaultAddStorage();
      },
    },
  },
  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    CompareBase: require('../compare-base').default,
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.ml-sp {
  margin-left: 28px;
}
.instorage-config {
  padding: 0 20px;
  height: 705px;
  .section-box {
    color: #fff;
  }
}
.explain {
  color: rgb(226, 135, 43);
}
.other-section-box {
  color: #fff;
  padding-top: 10px;
  .check-item {
    display: block;
  }
}
.compare-base-wrapper {
  height: 500px;
  @{_deep} .left-table {
    height: calc(100% - 40px) !important;
  }
  @{_deep} .right-table {
    height: calc(100% - 40px) !important;
  }
}
.color-title {
  color: var(--color-display-title);
}
</style>
