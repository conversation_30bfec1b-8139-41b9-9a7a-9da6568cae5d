<template>
  <!-- 
历史视频录像完整率评测结果详情 -->
  <ui-modal
    class="history-complete-accuracy"
    v-model="visible"
    v-if="visible"
    title="查看结果"
    :styles="styles"
    :footer-hide="true"
  >
    <!-- v-if="Object.keys(indexList).length" -->
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search"  @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <div class="charts-container-auto" :class="showArrow ? 'margin-container' : ''">
                <div :class="['charts-item']" v-for="(item, index) in abnormalCount" :key="index + item.title">
                  <span :class="['icon-font', 'f-40', item.icon, `icon-${index % 3}`]"></span>
                  <div class="number-wrapper">
                    <div class="f-12 color-white">{{ item.title }}</div>
                    <div class="f-18 color-num">
                      {{ commaNumber(item.count || 0) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="检测数据列表"></line-title>
        <div class="search-wrapper">
          <ui-label class="fl" label="组织机构" :width="70">
            <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择">
            </api-organization-tree>
          </ui-label>
          <ui-label class="fl ml-lg" label="关 键 词" :width="60">
            <Input class="width-lg" placeholder="请输入设备编码/名称/经纬度/地址" v-model="searchData.keyword"></Input>
          </ui-label>
          <ui-label class="fl ml-lg" :label="global.filedEnum.sbdwlx" :width="100">
            <Select
              v-model="searchData.sbdwlx"
              clearable
              :placeholder="`请选择${global.filedEnum.sbdwlx}`"
              class="input-width"
            >
              <Option v-for="(item, index) in propertySearch_sbdwlx" :key="index" :value="item.dataKey">{{
                item.dataValue
              }}</Option>
            </Select>
          </ui-label>
          <ui-label class="fl ml-lg" label="检测结果" :width="70">
            <Select v-model="searchData.outcome" clearable placeholder="请选择" class="input-width">
              <Option :value="1">正常</Option>
              <Option :value="2">异常</Option>
              <Option :value="3">无法检测</Option>
            </Select>
          </ui-label>
          <ui-label :width="70" class="fl" label=" ">
            <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
            <Button class="mr-sm" @click="resetInitial"> 重置 </Button>
          </ui-label>
        </div>
        <div></div>
        <div class="list auto-fill">
          <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
            <template #description="{ row }">
              <span
                :class="[
                  row.description == '合格' ? 'font-green' : '',
                  row.description == '不合格' ? 'color-failed' : '',
                  row.description == '无法检测' ? 'font-D66418' : '',
                ]"
                >{{ row.description }}</span
              >
            </template>
            <template #completeDay="{ row }">
              <span class="font-green">{{ row.completeDay }}</span>
            </template>
            <template #hiatusDay="{ row }">
              <Tooltip placement="left" style="width: 100%">
                <div slot="content" class="tool-box">
                  <p class="f-14">缺失日期：</p>
                  <p class="f-14" v-for="(item, index) in row.timeDayList" :key="index">
                    {{ item }}
                  </p>
                </div>
                <span :class="row.hiatusDay > 0 ? 'color-failed' : 'font-green'">{{ row.hiatusDay }}</span>
              </Tooltip>
            </template>
          </ui-table>
          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
  </ui-modal>
</template>

<style lang="less" scoped>
.history-complete-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 0 20px;
    }
  }
  .no-box {
    width: 1726px;
    min-height: 860px;
    max-height: 860px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 860px;
    max-height: 860px;
    border-radius: 4px;
    position: relative;
    .container {
      @{_deep}.tabs {
        .up {
          top: 2px;
        }
      }
      .list_item {
        width: calc(100% - 70px);
        height: 50px !important;
        line-height: 50px !important;
      }
      .title_text {
        width: 100%;
        display: inline-block;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 150px;
        line-height: 150px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 150px;
        line-height: 150px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 67%;
          position: relative;
          .charts-container-auto {
            z-index: 10;
            transition: all 0.5s;
            margin-top: 10px;
            display: flex;
            // overflow: hidden;
            justify-content: flex-start;
            flex-wrap: nowrap;
            padding-bottom: 2px;
            .charts-item {
              flex-shrink: 0;
              margin-right: 10px;
              width: 260px;
              height: 88px;
              background: #0f2f59;
              display: flex;
              justify-content: center;
              align-items: center;
              .number-wrapper {
                display: inline-block;
                margin-left: 16px;
                height: 100%;
              }
            }
            .f-40 {
              font-size: 40px;
            }
            .color-white {
              color: #fff;
              height: 50%;
              line-height: 65px;
            }
            .color-num {
              color: #19d5f6;
              height: 50%;
              line-height: 30px;
            }
            .icon-0 {
              background: linear-gradient(360deg, #176350 0%, #54f2b3 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .icon-1 {
              background: linear-gradient(360deg, #126f87 0%, #5bd3ec 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .icon-2 {
              background: linear-gradient(360deg, #78460b 0%, #d67506 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .icon-3 {
              background: linear-gradient(359deg, #88750e 0%, #bba00b 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .icon-4 {
              background: linear-gradient(359deg, #8822cc 0%, #cf20e2 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
            }
            .bg-0 {
              background: rgba(84, 242, 179, 0.1);
            }
            .bg-1 {
              background: rgba(91, 211, 236, 0.1);
            }
            .bg-2 {
              background: rgba(214, 117, 6, 0.1);
            }
            .bg-3 {
              background: rgba(187, 160, 11, 0.1);
            }
            .bg-4 {
              background: rgba(207, 32, 226, 0.1);
            }
          }
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .sucess {
      color: @color-success;
    }
    .error {
      color: @color-failed;
    }
    .list {
      margin-top: 10px;
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }

    .btn-text-default {
      cursor: pointer;
      font-size: 14px;
      color: var(--color-primary);
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }
    .label-title {
      @{_deep}.label {
        height: 50px !important;
        line-height: 50px !important;
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import user from '@/config/api/user';
import api from '@/config/api/inspectionrecord';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      showArrow: false,
      selectOrgTree: {
        orgCode: '',
      },
      exportLoading: false,
      moduleData: {
        rate: '历史视频录像完整率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableColumns: [
        { type: 'index', width: 70, align: 'center', title: '序号' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        {
          title: '组织机构',
          key: 'orgName',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
        {
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        { title: '存储类型', key: 'storageType' },
        { title: '检测结果', key: 'description', slot: 'description' },
        { title: '录像完整天数', key: 'completeDay', slot: 'completeDay' },
        { title: '录像缺失天数', key: 'hiatusDay', slot: 'hiatusDay' },
        // timeDayJsonStr timeDayList
        {
          title: '检测时间',
          key: 'startTime',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        // { title: '纬度', key: 'latitude' },
        // { title: '近X天抓拍数量', key: 'imageNum' },
        // { title: '回传时间', key: 'timeDifference', slot: 'timeDifference' },
        // { title: '设备联网状态', key: 'deviceStatusText', slot: 'deviceStatusText' },
        // { title: "操作", slot: "option", fixed: "right" },
      ],
      abnormalCount: [
        {
          title: '设备总数',
          count: '0',
          icon: 'icon-equipmentlibrary',
        },
        { title: '检测数量', count: '0', icon: 'icon-jianceshuliang' },
        {
          title: '历史视频录像完整设备数量',
          count: '0',
          icon: 'icon-lishishipinluxiangwanzhengshebeishuliang',
        },
        {
          title: '无法检测设备数量',
          countKey: '0',
          icon: 'icon-wufajianceshebeishuliang',
        },
      ],
      tableData: [],
      minusTable: 580,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        keyword: '',
        sbdwlx: '',
        orgCode: '',
        outcome: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      },
      styles: { width: '9rem' },
      visible: true,
      loading: false,
      indexList: {},
      dicDataEnum: Object.freeze({
        propertySearch_sbdwlx: 'propertySearch_sbdwlx',
      }),
      propertySearch_sbdwlx: [],
    };
  },
  async mounted() {
    await this.getTableData();
    await this.getChartsData(); // 获取图表数据
    await this.initDicContent();
  },

  methods: {
    // async getExport() {
    //   this.exportLoading = true
    //   let params = {
    //     resultId: this.$parent.row.resultId,
    //     indexId: this.$parent.row.indexId,
    //     orgCode: this.searchData.orgCode,
    //     keyword: this.searchData.keyword,
    //     sbdwlx: this.searchData.sbdwlx,
    //     indexName: this.$parent.row.indexName,
    //     total: this.searchData.totalCount,
    //     deviceStatus: this.searchData.deviceStatus,
    //   }
    //   try {
    //     let res = await this.$http.post(governanceevaluation.netWorkExport, params, {
    //       responseType: 'blob',
    //     })
    //     this.$util.common.exportfile(res)
    //     this.exportLoading = false
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
    search() {
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = 20;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.getTableData();
    },
    async initDicContent() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));

        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = data.data.find((row) => {
            return !!row[key];
          });
          this[this.dicDataEnum[key]] = obj[key];
        });
      } catch (err) {
        console.log(err);
      }
    },
    resetInitial() {
      this.selectOrgTree.orgCode = '';
      this.searchData.keyword = '';
      this.searchData.sbdwlx = '';
      this.searchData.outcome = '';
      this.searchData.orgCode = '';
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = 20;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.getTableData();
    },

    async getTableData() {
      try {
        this.loading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          orgCode: this.searchData.orgCode,
          keyword: this.searchData.keyword,
          sbdwlx: this.searchData.sbdwlx,
          outcome: this.searchData.outcome,
          params: this.searchData.params,
        };
        let res = await this.$http.post(governanceevaluation.videoPageList, params);
        const datas = res.data.data;
        this.pageData.totalCount = res.data.data.total;
        this.tableData = datas.entities;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },

    async getChartsData() {
      try {
        let data = {
          rootId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(api.queryEvaluatingVideoCount, data);
        this.indexList = res.data.data;
        this.abnormalCount[0].count = this.indexList.deviceCount;
        this.abnormalCount[1].count = this.indexList.evaluatingCount;
        this.abnormalCount[2].count = this.indexList.evaluatingSuccessCount;
        this.abnormalCount[3].count = this.indexList.unableEvaluatingCount;
        this.moduleData.rateValue = this.$parent.row.resultValue; //率
        this.moduleData.priceValue = this.$parent.row.standardsValue; //达标值
        this.moduleData.resultValue = this.indexList.qualifiedDesc; //考核结果
      } catch (err) {
        console.log(err);
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.params.pageNumber = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getTableData();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
