<template>
  <div class="this-level-compare-container">
    <div class="this-level-compare-wrapper ">
      <div class="statistics mb-md">
        <asset-compare-card
          v-for="(item, index) in tabList"
          :loading="tableLoading"
          :data="item.data"
          :type='item.value'
          :icon="item.icon"
          :compare-target='compareTarget'
          class="card-wrapper"
          :key="`${index}-${item.value}`"
          :target-name='item.target'
          :source-name='item.source'
        >{{ item.label }}
          <template #sourceName></template>
          <template #targetName></template>
        </asset-compare-card>
      </div>
      <div class="title">
        <ui-tabs-wrapped v-model="type" class="mb-sm tab" :data="tabList" @on-change="onChangeTab">
          <div class="latest-time-wrapper f-14">
            <span class="icon-font icon-shijian f-14 mr-xs"></span>
            <span class="time-label">最新时间：</span>
            <span class="time">{{ latestTime || '--' }}</span>
          </div>
        </ui-tabs-wrapped>
      </div>
      <div class="table-wrapper ">
        <ui-table
          class="ui-table "
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="tableLoading"
          :maxHeight='3000'
        >
          <template #targetOnly="{ row, index }">
            <span class="target-only">{{ row.targetOnly }}</span>
          </template>
          <template #sourceOnly="{ row, index }">
            <span class="source-only">{{ row.sourceOnly }}</span>
          </template>
          <template #diff="{ row, index }">
            <span class="diff">{{ row.diff }}</span>
          </template>
          <template #action="{ row, index }">
            <create-tabs
              :componentName="themDataDetail.componentName"
              :importantTabName="themDataDetail.title"
              :tabs-text="themDataDetail.text"
              @selectModule="selectModule"
              :tabs-query="{
                batchId: row.batchId,
                id: row.id,
                civilCode: row.civilCode,
                type,
              }"
              class="inline mr-md"
            >
              <ui-btn-tip icon="icon-shebeimingxi" content="明细"></ui-btn-tip>
            </create-tabs>
            <create-tabs
              :componentName="themData.componentName"
              :importantTabName="themData.title"
              :tabs-text="themData.text"
              @selectModule="selectModule"
              :tabs-query="{
                id: row.id,
                civilCode: row.civilCode,
                type,
              }"
              class="inline"
            >
              <ui-btn-tip icon="icon-lishijilu-01" content="历史"></ui-btn-tip>
            </create-tabs>
          </template>
        </ui-table>
      </div>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';

import assetcomparison from '@/config/api/assetcomparison';
import {
  THIS_LEVEL_BY_GB28181,
  THIS_LEVEL_BY_GAT1400_FACE, THIS_LEVEL_BY_GAT1400_VEHICLE,
} from './modules/enum';
export default {
  name: 'this-level-compare',
  props: {
    compareTarget: {
      type: Object,
    },
  },
  mixins: [dealWatch],
  computed: {
    tableColumns() {
      const { source, target } = this.tabList.find(item => item.value === this.type) || {};
      return [
        { title: '序号', type: 'index', width: 70, align: 'center' },
        { title: '行政区划', key: 'civilName', minWidth: 100, align: 'center' },
        {
          title: source,
          align: 'center',
          children: [
            { title: '总量', key: 'targetTotal', align: 'center' },
            { title: '独有', key: 'targetOnly', slot: 'targetOnly', align: 'center' },
          ],
        },
        {
          title: target,
          align: 'center',
          children: [
            { title: '总量', key: 'sourceTotal', align: 'center' },
            { title: '独有', key: 'sourceOnly', slot: 'sourceOnly', align: 'center' },
          ],
        },
        { title: '相同设备', key: 'same', minWidth: 100, align: 'center' },
        { title: '差异设备', key: 'diff', slot: 'diff', minWidth: 100, align: 'center' },
        {
          width: 100,
          title: '操作',
          slot: 'action',
          align: 'center',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ]
    }
  },
  data() {
    return {
      latestTime: null,
      //create-tab
      componentName: null,
      componentLevel: 0,
      themData: {
        componentName: 'CurrentLevelCompareHistory', // 需要跳转的组件名
        // componentName: 'SupSubLevelCompareHistory', // 需要跳转的组件名
        text: '对账历史', // 跳转页面标题
        title: `${this.compareTarget.label}-对账历史`,
        type: 'view',
      },
      themDataDetail: {
        componentName: 'AssetComparisonResult', // 需要跳转的组件名
        text: '对账明细', // 跳转页面标题
        title: `${this.compareTarget.label}-对账明细`,
        type: 'view',
      },

      type: THIS_LEVEL_BY_GB28181,
      tableLoading: false,
      tabList: [
        { label: '视频监控对账', value: THIS_LEVEL_BY_GB28181, icon: 'icon-shujujiance',data: {}, source: '资产库', target: '联网平台', },
        { label: '人脸卡口对账', value: THIS_LEVEL_BY_GAT1400_FACE, icon: 'icon-renliankakou', data: {}, source: '资产库', target: '人脸视图库', },
        { label: '车辆卡口对账', value: THIS_LEVEL_BY_GAT1400_VEHICLE, icon: 'icon-cheliangkakou', data: {}, source: '资产库', target: '车辆视图库', },
      ],
      tableData: [],
      statisticsData: [],
    };
  },
  created() {
    this.getLatestStatistics();
  },

  methods: {
    selectModule(name) {
      if (!!this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
      this.$emit('on-change-component', this.componentName);
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    handleClickHistory(val) {},
    handleClickDetail(val) {},
    onChangeTab(val) {
      this.latestTime = '';
      this.tableData = this.statisticsData[this.type];
      this.getLatestTime(this.tableData);
    },
    /**
     * 列表第一个时间为最新时间
     * @returns {string}
     */
    getLatestTime(tableData) {
      if (!tableData) return '';
      if (!tableData.length) return '';
      this.latestTime = tableData[0]['executeTime'];
    },
    getVennData(tableData) {
      this.tabList.forEach((item) => {
        let data = tableData[item.value] || [];
        item.data = data.length ? data[0] : {};
      });
    },
    async getLatestStatistics() {
      try {
        this.tableLoading = true;
        let { data } = await this.$http.post(assetcomparison.getLatestStatistics);
        this.statisticsData = Object.freeze(data.data);
        this.tableData = this.statisticsData[this.type];
        this.getLatestTime(this.tableData);
        this.getVennData(this.statisticsData);
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
  },
  components: {
    AssetCompareCard: require('@/views/viewassets/assetcompare/components/asset-compare-card.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    UiTabsWrapped: require('@/components/ui-tabs-wrapped.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
<style scoped lang="less">
.this-level-compare-container {
  position: relative;
  height: 100%;
  background: #071b39;

  .this-level-compare-wrapper {
    position: relative;
    height: 100%;
    .header {
      padding-bottom: 15px;
      border-bottom: 1px solid #074277;
    }

    .statistics {
      position: relative;
      display: flex;
      gap: 20px;
      .card-wrapper {
        position: relative;
        flex: 1;
        //width: 580px;
        height: 280px;
      }
    }
    .title {
      .latest-time-wrapper {
        color: #a9bed9;
      }
    }
    .table-wrapper {
      .ui-table {
        @{_deep} .target-only {
          color: #d66418;
        }
        @{_deep} .source-only {
          color: #b1b836;
        }
        @{_deep} .diff {
          color: #bc3c19;
        }
        @{_deep} .icon-shebeimingxi,
        .icon-lishijilu-01 {
          color: #2b84e2 !important;
        }

        @{_deep} .icon-lishijilu-01 {
          font-size: 16px !important;
        }

        @{_deep} .link {
          color: #2b84e2;
          cursor: pointer;
          text-decoration: underline;
        }
        @{_deep} .dis-link {
          text-decoration: none;
        }

        @{_deep}.total {
          font-size: 22px;
        }

        @{_deep}.total-score {
          color: #05fef5;
        }

        @{_deep}.ivu-table {

           .ivu-table-tip {
            height: 200px
          }

          &:before {
            width: 0;
          }

          .ivu-table-header {
            tr {
              th {
                color: #a9bed9;
                border-right: none !important;
              }
            }
          }
        }

        @{_deep} .ivu-table {
          th,
          td {
            border: 1px solid #0d477d !important;
          }

          &:before {
            content: '';
            position: absolute;
            background-color: #0d477d !important;
          }

          .ivu-table-summary {
            td {
              background: #062042;
            }
          }
        }
      }
    }
  }
}
</style>
