<template>
  <div class="device-information">
    <div class="search-wrapper">
      <ui-label :label="global.filedEnum.deviceName" :width="66" class="inline">
        <Input
          class="input-width"
          v-model="searchData.deviceName"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
        ></Input>
      </ui-label>
      <ui-label class="ml-lg inline" :label="`${global.filedEnum.deviceId}`" :width="66">
        <Input
          class="input-width"
          v-model="searchData.deviceId"
          :placeholder="`请输入${global.filedEnum.deviceId}`"
        ></Input>
      </ui-label>
      <!-- <ui-label class="fl ml-sm" label="数据来源：" :width="70">
                <Input v-model="searchData.sourceId" class="width-md"></Input>
            </ui-label> -->
      <ui-label :width="30" class="inline" label=" ">
        <Button type="primary" @click="startSearch">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </ui-label>
      <div class="line clear-b mt-md mb-md"></div>
      <ui-label :width="60" class="fr d_flex clear-b" label="">
        <Button type="primary" class="mr-sm" @click="exportExcel" :loading="exportloading">
          <i class="icon-font icon-daochu f-14"></i>
          <span class="vt-middle ml-sm">导出设备</span>
        </Button>
        <Dropdown class="dropdown" @on-click="onClickImport">
          <Button type="primary" :loading="importloading">
            <i class="icon-font icon-daoruwentishebei mr-sm"></i>
            <span>导入设备</span>
          </Button>
          <template #list>
            <DropdownMenu>
              <DropdownItem name="associate" :disabled="importloading">关联导入的设备</DropdownItem>
              <DropdownItem name="delete" :disabled="importloading">删除导入的设备</DropdownItem>
            </DropdownMenu>
          </template>
        </Dropdown>
        <Upload
          action="/test"
          :multiple="false"
          :show-upload-list="false"
          :beforeUpload="beforeUpload"
          ref="upload"
          class="fr"
        >
        </Upload>
        <Button class="merge-btn ml-sm" type="primary" @click="download">
          <i class="icon-font icon-xiazaimoban mr-sm"></i>
          <span>下载模板</span>
        </Button>
        <Button class="merge-btn fr ml-sm" type="primary" @click="addRelativedevice('add')">
          <i class="icon-font icon-guanlianshebei mr-sm"></i>
          <span>关联设备</span>
        </Button>
        <Button class="merge-btn fr ml-sm" type="primary" :disabled="!checkedData.length" @click="removeBulkDevice()">
          <i class="icon-font icon-yichu mr-sm"></i>
          <span>移除</span>
        </Button>
      </ui-label>
    </div>

    <div class="table-box">
      <div class="drag auto-fill" draggable="true" @dragstart="dragStart($event)" @dragover="dragOver($event)">
        <ui-table
          class="ui-table auto-fill"
          ref="tableRef"
          :table-columns="tableColumns"
          :table-data="tableData"
          @selectTable="selectTable"
          @rowClick="rowClick"
          :loading="loading"
        >
          <!-- <template #deviceName="{ row }">
            <span class="width-percent inline ellipsis" :title="row.deviceName">{{
              row.deviceName
            }}</span>
          </template> -->
          <template slot="deviceId" slot-scope="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
          <template #longitude="{ row }">
            <span>{{ row.longitude | filterLngLat }}</span>
          </template>
          <template #latitude="{ row }">
            <span>{{ row.latitude | filterLngLat }}</span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-yichu1"
              content="移除"
              @click.native="removeOneDevice(row)"
            ></ui-btn-tip>
            <!-- <Button type="text" @click="removeOneDevice(row)">移除</Button> -->
            <!-- <i
              class="icon-font f-14 icon-yichu-01 icon-operation-color mr-sm"
              @click="removeOneDevice(row)"
            ></i> -->
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>

    <add-device
      v-model="addShow"
      :modalAction="modalAction"
      :defaultForm="defaultForm"
      @addDeviceToCategory="startSearch"
    >
    </add-device>
    <ul class="drag-dom-ul" ref="testRef">
      <li class="drag-dom-ul-li" v-for="(item, index) of checkedRow" :key="index">
        <p>{{ index + 1 }}</p>
        <p>{{ item.deviceId }}</p>
        <p>{{ item.deviceName }}</p>
        <p>{{ item.orgName }}</p>
        <p>{{ item.longitude }}</p>
        <p>{{ item.latitude }}</p>
        <p>{{ item.checkStatus }}</p>
        <p>{{ item.sourceName }}</p>
        <p>{{ item.address }}</p>
      </li>
    </ul>
    <import-fail v-model="importFailShow" :table-data="importFailData"></import-fail>
  </div>
</template>

<script>
import category from '@/config/api/catalogmanagement';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  props: {
    tableData: {
      default: () => [],
    },
    loading: {
      default: false,
    },
    totalCount: {
      default: 0,
    },
    activeCatogoryObject: {
      default: () => {},
    },
  },
  data() {
    return {
      importFailShow: false,
      selectedOrg: {},
      searchData: {
        deviceId: '',
        deviceName: '',
        sourceId: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      statusList: [],
      ruleList: [{ label: '标准', value: 0 }],
      checkedData: [],
      checkedRow: [],
      tableColumns: [
        { type: 'selection', width: 50, align: 'center' },
        { title: '序号', width: 50, type: 'index', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          align: 'left',
          width: 180,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
          minWidth: 200,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          width: 110,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          width: 110,
        },
        // { title: "检测状态", slot: "checkStatus", align: "left" },
        // { title: "数据来源", key: "sourceName", align: "left" },
        {
          title: '安装地址',
          key: 'address',
          align: 'left',
          tooltip: true,
          minWidth: 200,
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          fixed: 'right',
          width: 50,
        },
      ],
      addShow: false,
      viewData: {},
      modalAction: 'add',
      defaultForm: {},
      importloading: false,
      exportloading: false,
      importFailData: [],
      importType: null,
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    //this.initMetaList();
  },
  watch: {
    totalCount: {
      handler(val) {
        this.pageData.pageNum = 1;
        this.pageData.totalCount = val;
      },
      immediate: true,
    },
    tableData: {
      handler() {
        this.checkedData = [];
      },
    },
  },
  methods: {
    async onClickImport(val) {
      //associate 关联 delete 删除
      if (!this.activeCatogoryObject.id) {
        return this.$Message.error('请选择目录');
      }
      this.importType = val;
      this.$refs.upload.handleClick();
    },
    upload(url, files) {
      return new Promise(async (resolve, reject) => {
        try {
          let bodyFormData = new FormData();
          bodyFormData.append('file', files);
          let { data } = await this.$http.post(url, bodyFormData, {
            params: {
              directoryId: this.activeCatogoryObject.id,
            },
          });
          resolve(data);
        } catch (e) {
          reject(e);
        }
      });
    },
    async exportExcel() {
      try {
        if (!this.activeCatogoryObject.id) {
          return this.$Message.error('请选择目录');
        }
        this.exportloading = true;
        let bodyFormData = new FormData();
        bodyFormData.append('directoryId', this.activeCatogoryObject.id);
        let res = await this.$http.post(category.postExportByDirectory, bodyFormData);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportloading = false;
      }
    },
    async download() {
      try {
        const res = await this.$http.get(category.downloadTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      }
    },
    async beforeUpload(fileXls) {
      if (!/\.(xlsx|xls)$/.test(fileXls.name)) {
        this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！');
        throw new Error();
      }
      const isLt30M = fileXls.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error('上传文件大小不能超过 30MB!');
        throw new Error();
      }
      // category.importDeviceLink 关联
      // category.postUnlinkDirectory 删除
      try {
        this.importloading = true;
        if (this.importType === 'associate') {
          let data = await this.upload(category.importDeviceLink, fileXls);
          this.importSuccess(data);
        } else if (this.importType === 'delete') {
          let data = await this.upload(category.postUnlinkDirectory, fileXls);
          this.$Message.success(data.data);
          this.$emit('startSearch', this.searchData);
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.importloading = false;
      }
    },
    importSuccess(data) {
      this.importFailData = [];
      let result = JSON.parse(data.data || '{}');
      if (result.failData.length) {
        this.importFailData = result.failData.map((item) => {
          return { deviceId: item };
        });
      }
      this.$Message.info({
        duration: 10,
        closable: true,
        render: (h) => {
          return h(
            'p',
            {
              style: {
                transform: "translateX('-10px')",
              },
            },
            [
              '导入成功',
              h(
                'span',
                {
                  class: 'font-green',
                },
                [` ${result.successNum} `],
              ),
              ['条, 导入失败'],
              h(
                'span',
                {
                  class: 'font-red',
                },
                [` ${result.failNum}`],
              ),
              ' 条 , 正在同步中...',
              h(
                'a',
                {
                  on: {
                    click: () => {
                      this.importFailShow = true;
                    },
                  },
                },
                [`${result.failNum ? '查看导入失败设备' : ''}`],
              ),
            ],
          );
        },
      });
      this.$emit('startSearch', this.searchData);
    },
    dragStart(event) {
      let dom = this.$refs.testRef;
      event.dataTransfer.setDragImage(dom, 10, 10);
      //event.dataTransfer.setData("text/plain", ev.target.id);
    },
    dragOver(e) {
      e.preventDefault();
    },
    rowClick(data, index) {
      this.$refs.tableRef.$refs.table.toggleSelect(index);
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.startSearch);
    },
    selectTable(selection) {
      this.checkedRow = selection;
      this.checkedData = selection.map((row) => {
        return row.id;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    addRelativedevice(type, row) {
      this.$emit('addRelativeDeviceShow', row);
      return;
      this.addShow = true;
      type !== 'add' ? (this.defaultForm = row) : (this.defaultForm = {});
      const metaType = {
        add: () =>
          (this.modalAction = {
            title: '关联设备',
            action: 'add',
          }),
        view: () =>
          (this.modalAction = {
            title: '查看元数据信息',
            action: 'view',
          }),
        edit: () =>
          (this.modalAction = {
            title: '修改元数据信息',
            action: 'edit',
          }),
      };
      metaType[type]();
    },
    removeOneDevice(row) {
      this.$UiConfirm({
        content: `您将移除 ${row.deviceName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$emit('removeInit', [row.id]);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    removeBulkDevice() {
      this.$UiConfirm({
        content: `您要移除这${this.checkedData.length}项设备，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$emit('removeInit', this.checkedData);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    startSearch() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.$emit('startSearch', this.searchData);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.$emit('startSearch', this.searchData);
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.$emit('startSearch', this.searchData);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddDevice: require('./add-device.vue').default,
    ImportFail: require('./import-fail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.device-information {
  display: flex;
  flex-direction: column;
  .search-wrapper {
    overflow: hidden;
    margin-bottom: 10px;
    .input-width {
      width: 200px;
    }
    .line {
      height: 1px;
      width: 100%;
      background: var(--border-color);
    }
  }
  .table-box {
    position: relative;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    .drag {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
    .ui-table {
      .icon-operation-color {
      }
    }
  }
  .drag-dom-ul {
    position: absolute;
    z-index: -1;
    .drag-dom-ul-li {
      width: 500px;
      display: flex;
      justify-content: space-around;
      color: var(--color-content);
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      > p {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
