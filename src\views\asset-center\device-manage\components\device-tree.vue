<template>
  <div class="map-device-tree">
    <Input
      class="search-input"
      ref="searchInput"
      placeholder="输入设备名称"
      v-model.trim="searchPrams.keyWords"
      :maxlength="50"
      clearable
      @on-enter="searchName(true)"
      @on-clear="searchName(true)"
    >
      <Button
        slot="append"
        icon="ios-search"
        @click="searchName(true)"
      ></Button>
    </Input>

    <div class="device-content" :class="{ isClose: !isOpen }">
      <ui-loading v-if="loading && isOpen" />
      <div class="device-content-header">
        <div class="label">
          <div class="label-text">资源列表：</div>
          <div
            class="tree-operators-icon-wrap"
            @mouseenter="showTreeFilter"
            @mouseleave="hideTreeFilter"
          >
            <i class="iconfont color-bule icon-loudou"></i>
            <div class="tree-filter-wrap" v-if="isTreeFilter">
              <div class="triangle"></div>
              <div class="tree-filter-wrap-content">
                <div class="tree-filter-wrap-title">
                  <span>状态</span>
                </div>
                <div class="tree-filter-check-wrap">
                  <Checkbox v-model="stuVAll" @on-change="changeStatusAll"
                    >全选</Checkbox
                  >
                  <CheckboxGroup v-model="stuV" @on-change="changeStatus">
                    <Checkbox label="在线"></Checkbox>
                    <Checkbox label="离线"></Checkbox>
                  </CheckboxGroup>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="operator" @click="isOpen = !isOpen">
          {{ isOpen ? "收起面板" : "点击展开" }}
        </div>
      </div>

      <xn-tree
        class="deviceTree"
        :ref="'tree'"
        :option="option"
        :label="labelFn"
        :fileOpe="fileOpe"
        @clickNode="handleNodeSingleClick"
      ></xn-tree>

      <div class="general-search-footer" v-if="isOpen && searchPrams.keyWords">
        <ui-page
          :simple="true"
          :show-elevator="false"
          :show-sizer="false"
          :total="total"
          countTotal
          :current="pageInfo.pageNumber"
          :page-size="pageInfo.pageSize"
          @pageChange="pageChange"
          size="small"
          show-total
        >
        </ui-page>
      </div>
    </div>
  </div>
</template>

<script>
import {
  queryDeviceOrgTree,
  queryMyVideoGroupList,
  addVideoToGroup,
  getTreeAncestors,
} from "@/api/player";
import xnTree from "@/components/xn-tree/index.vue";
export default {
  components: {
    xnTree,
  },
  data() {
    return {
      searchPrams: {
        keyWords: "",
      },
      isOpen: false,
      deviceList: [],
      currentTabIndex: 0,
      deviceType: "1",
      loading: false,
      groupList: [],
      node: null,
      resolveFunc: null,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 19,
      },
      isTreeFilter: false,
      stuVAll: true,
      stuV: ["在线", "离线"],
      treeData: [],
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: null, //radio,null,checkbox
        canMove: false,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            this.loading = true;
            let roleParam = {
              roleId: this.userInfo.roleVoList[0].id,
              filter:
                this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
            };
            return new Promise((resolve) => {
              queryDeviceOrgTree({
                orgCode: node.orgCode,
                isOnlineStatus: this.isOnlineStatus,
                ...roleParam,
              }).then((res) => {
                this.loading = false;
                console.log(res, "res.res");
                resolve(this._formatDeviceOrgList(res));
              });
            });
          },
        },
      },
      fileOpe: [],
    };
  },
  computed: {
    isOnlineStatus() {
      if (this.stuVAll) {
        return [];
      } else {
        return this.stuV.map((v) => {
          if (v == "在线") {
            return 0;
          } else {
            return 1;
          }
        });
      }
    },
    onlyOffLine() {
      return this.stuV.length == 1 && this.stuV[0] == "离线" ? true : false;
    },
  },
  watch: {
    isOnlineStatus: {
      handler(val) {
        this.searchName(true);
      },
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getParentData();
    },
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let iconClass = !data.deviceId ? "icon-fenju" : this.getDeviceIcon(data);
      let iconColor = this.getDeviceIconColor(data);
      let onLine = `<span class="color-bule">${data.onlineTotal || 0}</span>`;
      let offLine = `<span class="color-grey">${data.offlineTotal || 0}</span>`;
      let statistics = !data.deviceId
        ? `(${this.onlyOffLine ? offLine : onLine}/${data.allTotal || 0})`
        : "";
      let html = `<div class="node-title ${titleClass}">
          <i class="iconfont color-bule ${iconClass}" style="color: ${iconColor}"></i>
          <span class="label">${data.label}</span>
          <span class="statistics">${statistics}</span>
        </div>
        `;
      return html;
    },
    getParentData() {
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
      };
      this.loading = true;
      let params = {
        searchKey: this.searchPrams.keyWords,
        isOnlineStatus: this.isOnlineStatus,
        ...roleParam,
      };
      if (this.searchPrams.keyWords) params = { ...params, ...this.pageInfo };
      queryDeviceOrgTree(params).then((res) => {
        this.total = res.data.total || 0;
        this.loading = false;
        this.treeData = this._formatDeviceOrgList(res);
        this.$refs.tree.initTree(this.treeData);
      });
    },
    // 单击
    handleNodeSingleClick(item) {
      this.$emit("deviceClick", item);
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
    // 格式化设备、组织、统计数据
    _formatDeviceOrgList(deviceOrgResult) {
      let deviceList = deviceOrgResult.data.deviceList
        ? deviceOrgResult.data.deviceList.map((v) => {
            v.id = v.deviceId;
            v.label = v.deviceName;
            (v.isLeaf = true),
              (v.ptzType = v.deviceChildType ? v.deviceChildType : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
            return v;
          })
        : [];
      console.log(deviceList, "deviceList");
      let deviceOrgList = deviceOrgResult.data.deviceOrgList
        ? deviceOrgResult.data.deviceOrgList.map((v) => {
            v.label = v.orgName;
            return v;
          })
        : [];
      deviceOrgList = deviceOrgList.filter((v) => v.allTotal > 0);
      return [...deviceOrgList, ...deviceList];
    },
    searchName(isReset) {
      if (this.loading) return;
      if (isReset) this.pageInfo.pageNumber = 1;
      this.isOpen = true;
      this.getParentData();
    },
    pageChange(pageNumber) {
      this.pageInfo.pageNumber = pageNumber;
      this.searchName();
    },
    // 展示筛选列表
    showTreeFilter() {
      this.isTreeFilter = true;
    },
    // 隐藏筛选列表
    hideTreeFilter() {
      this.isTreeFilter = false;
    },
    changeStatusAll() {
      if (this.stuVAll) {
        this.stuV = ["在线", "离线"];
      } else {
        this.stuV = [];
      }
    },
    changeStatus(data) {
      if (data.length == 2) {
        this.stuVAll = true;
      } else {
        this.stuVAll = false;
      }
    },
  },
};
</script>

<style lang="less" scope>
.map-device-tree {
  height: calc(~"100% - 30px");
  .ivu-input-wrapper {
    .ivu-icon {
      color: #ffffff !important;
    }
  }
  .search-input {
    margin-bottom: 10px;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  .device-content {
    height: calc(~"100% - 44px");
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
    border-radius: 0px 0px 4px 4px;
    pointer-events: auto;
    flex-shrink: 0;
    position: relative;
    padding: 10px;
    display: flex;
    flex-direction: column;
    // overflow: hidden;
    &.isClose {
      height: 40px !important;
    }
    &-header {
      height: 22px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      .label {
        font: 14px;
        color: #333333;
        display: flex;
        align-items: center;
        .label-text {
          font-weight: 600;
        }
      }
      .operator {
        cursor: pointer;
        &:hover {
          color: #2c86f8;
        }
      }
      /deep/ .ivu-checkbox-wrapper {
        color: #fff;
      }
      .tree-operators-icon-wrap {
        position: relative;
        .tree-filter-wrap {
          position: absolute;
          top: 28px;
          left: -70px;
          z-index: 9;
          width: 325px;
          background: rgba(44, 48, 51, 0.9);
          border-radius: 3px;
          color: var(--font-white-color);
          padding: 5px 10px;
          .triangle {
            width: 0;
            height: 0;
            overflow: hidden;
            font-size: 0;
            line-height: 0;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent rgba(44, 48, 51, 0.9)
              transparent;
            position: absolute;
            left: 75px;
            top: -10px;
          }
          .tree-filter-wrap-content {
            .tree-filter-wrap-title {
              color: #fff;
              height: 22px;
              font-size: 14px;
              line-height: 25px;
            }
            .ivu-checkbox-wrapper {
              color: #fff;
            }
            .tree-filter-check-wrap {
              display: flex;
            }
            .xui-checkbox-group {
              display: flex;
              flex-wrap: wrap;
              .xui-checkbox {
                margin: 0 15px 0 0 !important;
                height: 25px;
                line-height: 22px;
              }
            }
          }
        }
      }
    }
    .tree-box {
      height: calc(~"100% - 32px");
      overflow-y: auto;
      position: initial;
    }
    .deviceTree {
      height: calc(~"100% - 30px");
    }
    .group-list {
      max-height: 300px;
      overflow-y: auto;
    }
    /deep/.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content {
      cursor: pointer;
    }
  }
}
</style>
