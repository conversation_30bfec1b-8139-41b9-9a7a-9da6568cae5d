<template>
  <div class="pic-mode-card">
    <div class="pic-mode-card-img" @click="lookScence">
      <!-- 传入hasArtificial就展示人工复核 -->
      <div class="active-wrapper">
        <div v-if="cardData && cardData.objectSnapshotId" :title="cardData.objectSnapshotId">
          <div class="base-text-color id-num ellipsis f-14 vt-middle">ID:{{ cardData.objectSnapshotId }}</div>
          <span
            class="ellipsis-text f-14"
            @click.stop=""
            v-clipboard="cardData.objectSnapshotId"
            v-clipboard:callback="copy"
            >复制</span
          >
        </div>
        <p
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
          v-if="'hasArtificial' in cardData"
          class="ellipsis-text"
          title="人工复核"
          @click.stop="$emit('artificialReview', cardData)"
        >
          <i class="ellipsis-text icon-font icon-xiajikaohedefen vt-middle f-14 mr-xs"></i>
          <span class="artificial-text f-14 ellipsis-text">人工复核</span>
        </p>
        <p
          v-if="'hasAlgorithms' in cardData"
          class="shadow-artificial ellipsis-text"
          title="查看算法详情"
          @click.stop="$emit('algorithmsReview', cardData)"
        >
          <i class="ellipsis-text icon-font icon-xiangqing vt-middle f-14 mr-xs"></i>
          <span class="artificial-text f-14 ellipsis-text">查看算法详情</span>
        </p>
      </div>
      <ui-image :src="cardData[smallImgKey]" />
    </div>
    <div class="pic-mode-card-info">
      <slot name="cardInfo"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'pic-mode-card',
  props: {
    cardData: {
      type: Object,
      default: () => {},
    },
    imgIndex: {
      type: Number,
    },
    smallImgKey: {
      type: String,
      default: 'imageUrl',
    },
  },
  data() {
    return {};
  },
  methods: {
    // 查看大图
    lookScence() {
      this.$emit('handleLookScence', this.imgIndex);
    },
    copy() {
      this.$Message.success('复制成功');
    },
  },
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>

<style lang="less" scoped>
.pic-mode-card {
  position: relative;
  padding: 15px;
  background: var(--bg-info-card);
  border: 1px solid var(--border-info-card);
  &-img {
    width: 100%;
    height: 162px;
    cursor: pointer;
    position: relative;
    &:hover {
      .active-wrapper {
        display: block;
      }
    }
  }
  &-info {
    font-size: 12px;
    //color: #8797ac;
    p {
      margin-top: 6px;
    }
  }
  .active-wrapper {
    z-index: 11;
    position: absolute;
    bottom: 0;
    width: 100%;
    display: none;
    padding-left: 10px;
    background: rgba(0, 0, 0, 0.3);
    .artificial-text {
      color: #2b84e2;
      cursor: pointer;
      vertical-align: middle;
    }
    .ellipsis-text {
      color: #2b84e2;
    }
    .id-num {
      display: inline-block;
      max-width: 110px;
    }
  }
}
[data-theme='dark'] {
  .pic-mode-card {
    background: var(--bg-info-card);
    &-info {
      color: #8797ac;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .pic-mode-card {
    background: #f9f9f9;
    border: 1px solid #d3d7de;
    &-img {
      border: 1px solid #d3d7de;
    }
  }
  .active-wrapper {
    .artificial-text {
      color: #2c86f8 !important;
    }
    .ellipsis-text {
      color: #2c86f8 !important;
    }
  }
}
</style>
