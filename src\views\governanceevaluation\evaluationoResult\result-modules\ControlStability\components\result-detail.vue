<template>
  <ui-modal v-model="visible" title="返回结果详情" footer-hide>
    <div class="detail-wrapper">
      <div class="left-detail">
        <div class="image-wrapper">
          <ui-image :src="$parent.currentRow.useUrl" class="ui-image-card" />
        </div>
      </div>
      <div class="right-detail">
        <div class="title-total base-text-color">
          <span class="ml-md">返回轨迹(共{{ searchData.totalCount }}张)</span>
        </div>
        <div class="warning" v-ui-loading="{ loading, tableData: imageData }">
          <image-card v-for="(item, index) in imageData" :key="index" class="base-text-color inline" :src="item.url">
            <div class="ellipsis font-gray">
              <span>{{ item.name }}</span>
            </div>
          </image-card>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';

/**
 * 人像轨迹查询接口稳定性 车辆轨迹查询接口稳定性 返回结果详情
 */
export default {
  name: 'result-detail',
  props: {
    value: {},
  },
  data() {
    return {
      visible: false,
      loading: false,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      imageData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        this.imageData = [];
        const { statisticType, batchId, indexId } = this.$route.query;
        let { currentRow } = this.$parent;
        let params = {
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          batchId,
          displayType: statisticType,
          indexId,
          orgRegionCode: currentRow.civilCode,
          customParameters: {},
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getPolyData, params);
        this.searchData.totalCount = data.total || 0;
        this.imageData = data.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = val;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      this.init();
    },
  },
  computed: {},

  components: {
    ImageCard:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/InterfaceStability/components/image-card')
        .default,
  },
};
</script>
<style lang="less" scoped>
.detail-wrapper {
  display: flex;
  align-items: center;
  height: 700px;
  .left-detail {
    position: relative;
    width: 362px;
    height: 100%;
    border-right: 1px solid var(--border-color);
    padding-top: 50px;
    .image-wrapper {
      width: 323px;
      height: 312px;
      background: var(--bg-content);
    }
  }
  .right-detail {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    height: 100%;
  }
  .title-total {
    height: 88px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: flex-end;
    padding-bottom: 15px;
  }
  .image-bar {
    display: flex;
    overflow-x: auto;
  }
  .warning {
    padding: 20px;
    flex: 1;
    overflow-y: auto;
  }
  .font-gray {
    color: #8797ac;
  }
}
@{_deep} .ivu-modal {
  width: 83.3% !important;
  .ivu-modal-header,
  .ivu-modal-body {
    padding: 0 !important;
    position: relative;
  }
}
</style>
