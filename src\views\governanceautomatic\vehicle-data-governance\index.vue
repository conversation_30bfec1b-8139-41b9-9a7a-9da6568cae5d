<template>
  <div class="vehicle-data-governance auto-fill">
    <div class="search-module">
      <div class="search-type">
        <tag-view :list="tagList" :default-active="1" @tagChange="changeStatus" ref="tagView"></tag-view>
        <span class="remarks ml-lg">( 备注：只展示最近3天治理结果 )</span>
      </div>
      <div>
        <i-switch
          class="mr-sm"
          v-model="jobState"
          :before-change="changeState"
          :true-value="1"
          :false-value="0"
          :loading="jobLoading"
        >
        </i-switch>
        <span class="mr-sm job-text">{{ jobState ? '治理中...' : '未治理' }}</span>
        <Button type="text" class="config" @click="governanceConfigShow">
          <i class="icon-font icon-canshupeizhi mr-xs"></i>
          <span class="inline vt-middle">治理配置</span>
        </Button>
      </div>
    </div>
    <div class="search-criteria" v-if="imageStatus !== '1'">
      <ui-label class="inline mr-lg" label="异常原因" v-if="imageStatus === '2'">
        <Select
          class="width-lg"
          v-model="searchData.errorCodeList"
          placeholder="请选择异常原因"
          clearable
          multiple
          filterable
          :max-tag-count="1"
        >
          <Option
            v-for="(item, index) in errorTypeList"
            :key="index"
            :label="item.dataValue"
            :value="item.dataKey"
          ></Option>
        </Select>
      </ui-label>
      <ui-label v-if="imageStatus === '3'" class="inline mr-lg" label="治理结果">
        <Select
          class="width-lg"
          v-model="searchData.checkResultList"
          placeholder="请选择治理结果"
          clearable
          multiple
          filterable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in governanceList" :key="index" :label="item.label" :value="item.value"></Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div
      :class="['auto-fill', 'table-module', imageStatus !== '3' ? '' : 'structural']"
      v-ui-loading="{ loading: loading, tableData: tableData }"
    >
      <div class="auto-fill">
        <pic-mode
          v-if="imageStatus !== '3'"
          :card-list="tableData"
          :pic-mode-count="10"
          img-key="originalScenePath"
          small-img-key="originalScenePath"
        >
          <template #cardInfo="{ item }">
            <div class="mt-sm">
              <div title="抓拍时间">
                <i class="icon-font icon-zhuapaishijian mr-xs f-14"></i>
                <span>{{ item.shotTime || '未知' }}</span>
              </div>
              <div title="入库时间">
                <i class="icon-font icon-jieshoushijian mr-xs f-16"></i>
                <span>{{ item.receiveTime || '未知' }}</span>
              </div>
              <div>
                <i class="icon-font icon-zhuapaididian mr-xs"></i>
                <span class="address ellipsis" :title="item.address">{{ item.address || '未知' }}</span>
              </div>
              <i class="icon-font icon-jiancejieguo mr-xs f-14"></i>
              <span class="tips ellipsis" :title="getErrorMessageText(item)">
                {{ getErrorMessageText(item) }}
              </span>
            </div>
          </template>
        </pic-mode>
        <pic-row-mode v-else img-key="originalScenePath" small-img-key="originalScenePath" :card-list="tableData">
          <template #picMessage="{ cardData }">
            <div
              :class="[
                'check-result',
                cardData.checkResult === 1 ? 'bg-sucess' : cardData.checkResult === 2 ? 'bg-b77a2a' : 'bg-failed',
              ]"
            >
              {{ cardData.checkResult === 1 ? '已治理' : cardData.checkResult === 2 ? '部分治理' : '无法治理' }}
            </div>
          </template>
          <template #cardInfo="{ cardData }">
            <div class="info">
              <div class="mb-sm" v-for="(item, index) in cardData.vehicleStructure" :key="index">
                <span>{{ item.propertyName }}：</span>
                <span
                  :class="[
                    'title',
                    item.filedStatus === '1' || item.filedStatus === '4'
                      ? 'font-red'
                      : item.filedStatus === '2'
                      ? 'font-green'
                      : 'base-text-color',
                  ]"
                >
                  {{ item.transferpropertyValue || '缺失' }}
                </span>
              </div>
              <Button class="detail" type="text" @click="detailShow(cardData)">详情</Button>
            </div>
          </template>
          <template #default="{ cardData }">
            <div class="pic-mode-card-state f-14">
              <p class="mr-md pic-mode-card-state-p">
                <i class="icon-font icon-shijian mr-xs"></i>
                <span class="inline ellipsis" :title="cardData.shotTime">
                  {{ cardData?.shotTime ?? '缺失' }}
                </span>
              </p>
              <p class="pic-mode-card-state-p f-14">
                <i class="icon-font icon-dizhi mr-xs"></i>
                <span class="inline ellipsis" :title="cardData.address">
                  {{ cardData?.address ?? '缺失' }}
                </span>
              </p>
            </div>
          </template>
        </pic-row-mode>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <governance-details v-model="detailVisible" :detail-data="detailData"></governance-details>
    <governance-config v-model="governanceConfigVisible"></governance-config>
  </div>
</template>
<script>
import imageDataGovernance from '@/config/api/image-data-governance';
import user from '@/config/api/user';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {},
  data() {
    return {
      loading: false,
      detailVisible: false,
      governanceConfigVisible: false,
      jobState: 0,
      jobLoading: false,
      tagList: [],
      typeList: [
        {
          label: '合格图像',
          value: '1',
        },
        {
          label: '不合格图像',
          value: '2',
        },
        {
          label: '结构化属性治理',
          value: '3',
        },
      ],
      errorTypeList: [],
      governanceList: [
        { label: '已治理', value: 1 },
        { label: '部分治理', value: 2 },
        { label: '无法治理', value: 3 },
      ],
      imageStatus: '2',
      searchData: {
        queryType: '2',
        checkResultList: [],
        errorCodeList: [],
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      detailData: [],
    };
  },
  created() {
    this.getQueryListTransfer();
    this.tagList = this.typeList.map((row) => row.label);
    this.copySearchDataMx(this.searchData);
    this.getDictData();
    this.init();
    this.getDataTask();
  },
  methods: {
    ...mapActions({
      getQueryListTransfer: 'algorithm/getQueryListTransfer',
    }),
    getErrorMessageText(item) {
      let errorMessageTextList = item.detail.filter((row) => row.result !== 1);
      return errorMessageTextList.map((row) => row.errorMessageText).join('、');
    },
    async getDictData() {
      try {
        const params = {
          typekey: 'vehicle_check_rule_error_code',
        };
        let { data } = await this.$http.get(user.queryByTypeKey, { params });
        this.errorTypeList = data.data.map((item) => {
          return {
            dataValue: item.dataValue,
            dataKey: item.dataKey,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        const res = await this.$http.post(imageDataGovernance.queryList, {
          imageStatus: this.imageStatus,
          ...this.searchData,
        });
        this.tableData = res.data.data.list.map((row) => {
          const vehicleStructure = row.detail.find((row) => row.method === 'vehicleStructure');
          Object.keys(row.imageInfo).forEach((key) => {
            row[key] = row.imageInfo[key];
          });
          delete row.imageInfo;
          // transferpropertyValue 统一存转化后的数值
          let resultJson = JSON.parse(vehicleStructure?.resultJson || '[]');
          resultJson.forEach((item) => {
            if (item.filedStatus === '2') {
              item.transferpropertyValue = this.getPropertyValueByOrigin(item.propertyCode, item.propertyValue);
            } else {
              item.transferpropertyValue = this.getPropertyValueByOrigin(item.propertyCode, item.originPropertyValue);
            }
          });
          this.$set(row, 'vehicleStructure', resultJson);
          this.$set(row, 'checkResult', vehicleStructure?.checkResult);
          return row;
        });
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    /**
     *
     * @param propertyCode 类型
     * @param propertyValue 车辆属性代码
     * @returns {string|*} 车辆属性描述
     */
    getPropertyValueByOrigin(propertyCode, propertyValue) {
      let dict = this.algorithmTransfer.find(
        (item) => item.dictType === `${propertyCode}Type` && item.dictKey === propertyValue,
      );
      if (!dict) return propertyValue;
      return dict.dictValue;
    },
    async getDataTask() {
      try {
        const res = await this.$http.get(imageDataGovernance.getDataTask, {
          params: {
            id: 2,
          },
        });
        this.jobState = res.data.data.state;
      } catch (err) {
        console.log(err);
      }
    },
    changeStatus(index) {
      this.imageStatus = this.typeList[index].value;
      this.searchData.checkResultList = [];
      this.searchData.errorCodeList = [];
      this.search();
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    governanceConfigShow() {
      this.governanceConfigVisible = true;
    },
    detailShow(cardData) {
      this.detailVisible = true;
      this.detailData = cardData.vehicleStructure;
    },
    changeState() {
      return new Promise(async (resolve, reject) => {
        try {
          if (this.jobLoading) {
            reject(false);
          }
          this.jobLoading = true;
          await this.$http.post(imageDataGovernance.startOrPauseDataTask, {
            id: 2,
            state: this.jobState === 1 ? 0 : 1,
          });
          const message = this.jobState === 1 ? '任务已停止' : '任务已开启';
          this.$Message.success(message);
          resolve(true);
        } catch (err) {
          console.log(err);
          reject(false);
        } finally {
          this.jobLoading = false;
        }
      });
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      algorithmTransfer: 'algorithm/algorithmTransfer',
    }),
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    PicRowMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-row-mode/index.vue').default,
    GovernanceDetails: require('./governance-details.vue').default,
    GovernanceConfig: require('./governance-config.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .vehicle-data-governance {
    .tips {
      color: #e44f22;
    }
    .remarks {
      color: #c76d28;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .vehicle-data-governance {
    .tips {
      color: var(--color-failed);
    }
    .remarks {
      color: var(--color-warning);
    }
  }
}

.vehicle-data-governance {
  .tips,
  .address {
    vertical-align: middle;
    display: inline-block;
    width: 115px;
  }
  .tips-content {
    width: 200px;
    white-space: normal;
  }
  .search-module {
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .job-text {
      width: 60px;
      display: inline-block;
    }
    .search-type {
      display: flex;
      align-items: center;
    }
    .config {
      color: var(--color-active);
      cursor: pointer;
      span {
        text-decoration: underline;
      }
    }
  }
  .search-criteria {
    margin-top: 10px;
    padding: 10px 20px 0 20px;
    border-top: 1px solid var(--devider-line);
  }
  .table-module {
    margin-top: 10px;
    padding-left: 20px;
    &.structural {
      @{_deep}.pic-mode-item {
        width: 364px;
      }
      @{_deep} .pic-mode-card-img {
        width: 138px;
        height: 138px;
      }
    }
    @{_deep}.pic-mode-item {
      width: calc((100% - (10 * 9px)) / 10);
    }
    .check-result {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 11;
      padding: 5px;
      font-size: 12px;
    }
    .info {
      font-size: 14px;
      @{_deep} .ivu-tooltip-inner {
        white-space: pre-wrap;
      }
      > div {
        line-height: 14px;
      }
      .title {
        display: inline-block;
        line-height: 14px;
      }
      .detail {
        position: absolute;
        top: 0;
        right: 10px;
        @{_deep}span {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
