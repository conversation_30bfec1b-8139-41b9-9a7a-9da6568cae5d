/**
* 人像抓拍
 */
<template>
    <div class="list-card box-1">
        <!-- <div class="" :class="{ isChecked: item.isChecked }"
             v-for="(item, index) in dataList"
             :key="index"> -->
            <div class="collection paddingIcon">
                <div class="bg"></div>
                <ui-btn-tip class="collection-icon" v-if="data.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(data, 2)" />
                <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(data, 1)" />
            </div>
            <p class="img-content">
                <span class="num" v-if="data.score">{{ data.score }}%</span>
                <template v-if="childSource == 1">
                    <ui-image :src="data.photoUrl" alt="静态库" viewer />
                </template>
                <template v-else-if="childSource == 2">
                    <ui-image :src="data.traitImg" alt="动态库" @click.native="faceDetailFn(data)" />
                </template>
                <!-- <Checkbox class="check-box" v-if="mapOnData" v-model="data.isChecked" @on-change="e => checkHandler(e, index)"></Checkbox> -->
            </p>
          <!-- 静态库 -->
          <div class="bottom-info"  v-if="childSource == 1">
                <time>
                    <Tooltip content="姓名" placement="right" transfer theme="light">
                        <i class="iconfont icon-xingming"></i>
                    </Tooltip>
                    {{ data.name }}
                </time>
                <p>
                    <Tooltip content="证件号码" placement="right" transfer theme="light">
                        <i class="iconfont icon-shenfenzheng"></i>
                    </Tooltip>
                    {{ data.idCardNo }}
                </p>
          </div>
          <!-- 动态库 -->
            <div class="bottom-info" v-else>
                <time>
                    <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                        <i class="iconfont icon-time"></i>
                    </Tooltip>
                    {{ data.absTime }}
                </time>
                <p>
                    <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                        <i class="iconfont icon-location" title="123"></i>
                    </Tooltip>
                    <span class="ellipsis" v-show-tips>{{data.deviceName}}</span>
                </p>
            </div>
            <!-- <div class="operate-bar">
                <p class="operate-content">
                    <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(data)" />
                    <ui-btn-tip content="分析" icon="icon-fenxi" />
                    <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
                </p>
            </div> -->
        <!-- </div> -->
        <!-- <div class="empty-card-1" v-for="(dataList, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div> -->
        <!-- 动态库人脸详情 -->
        <face-detail v-if="faceShow"
                 class="faceDetail"
                 ref="faceDetail"
                 @close="faceShow = false" />
    </div>
</template>

<script>
import faceDetail from '@/components/detail/face'
export default {
    name: '',
    components:{
        faceDetail
    },
    props:{
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
        childSource: {
            type: Number,
            default: 2
        }
    },
    data () {
        return {
            childDataSourceVal: '',
            mapOnData:false,
            faceShow: false
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {

        checkHandler(e, i) {
            // this.dataList[i].isChecked = e
            // this.checkAll = this.dataList.filter(e => e.isChecked).length === this.dataList.length ? true : false
        },
        /**
         * 收藏
         */
        collection( data, flag ) {
            var param = {
                favoriteObjectId: data.id,
                favoriteObjectType: this.childSource == 2 ? 5 : 13,
            }
            this.$emit('collection', param, flag)
        },
        faceDetailFn(row){
            this.$emit('facePhoto');
        },
        /**
         * 跳转到一人一档页面
         */
        archivesPage (item) {
            if(this.childSource == 1){  // 静态库
                const { href } = this.$router.resolve({
                    name: 'people-archive',
                    query: {
                        archiveNo: item.idCardNo,
                        source: 'people',
                        initialArchiveNo: item.idCardNo
                    }
                })
                window.open(href, '_blank')
            }else{   // 动态库
                const { href } = this.$router.resolve({
                    name: 'video-archive',
                    query: {
                        archiveNo: item.vid,
                        source: 'people',
                        initialArchiveNo: item.vid
                    }
                })
                window.open(href, '_blank')
            }
        },
    }
}
</script>

<style lang='less' scoped>
@import 'style/index';
.faceDetail {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 666;
}
</style>
