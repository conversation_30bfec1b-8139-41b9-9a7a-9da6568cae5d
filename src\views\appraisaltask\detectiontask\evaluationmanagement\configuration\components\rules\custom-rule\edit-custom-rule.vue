<template>
  <ui-modal v-model="visible" title="自定义规则" width="30%" @query="handleSave">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="110">
      <FormItem label="规则名称" prop="ruleName">
        <Input v-model="formValidate.ruleName" class="width-md" :placeholder="`请输入规则名称`"></Input>
      </FormItem>
      <FormItem label="规则逻辑代码" prop="javaCode">
        <Input type="textarea" v-model="formValidate.javaCode" placeholder="请输入规则逻辑代码" :rows="15"></Input>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      tableIndex: null,
      formValidate: {
        'ruleName': '',
        'javaCode': '',
      },
      ruleValidate: {
        ruleName: [{ required: true, message: '规则名称不能为空', trigger: 'blur' }],
        javaCode: [{ required: true, message: '规则逻辑代码不能为空', trigger: 'blur' }],
      },
    };
  },
  created() {},
  methods: {
    /**
     *
     * @param processOptions 表单
     * @param index 表格的index
     */
    init(processOptions, index) {
      this.tableIndex = index;
      this.formValidate = { ...this.formValidate, ...processOptions };
      this.visible = true;
    },
    handleSave() {
      this.visible = false;
      this.$emit('save', this.formValidate, this.tableIndex);
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  margin-top: 0 !important;
  padding: 20px 50px !important;
}
</style>
