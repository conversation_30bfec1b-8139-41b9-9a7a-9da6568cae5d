<template>
  <map-base
    ref="MapBase"
    :class="['map-base']"
    :is-edit="false"
    :need-query-device-info="true"
    :camera-list="cameraList"
    :map-position="mapPosition"
    :choose-map-item="chooseMapItem"
    :location-cursor="locationCursor"
    :custom-marker="customMarker"
    :enable-close-on-click="false"
    :need-query-all="true"
    :need-close-call-back="true"
    :pixel="{ x: 0, y: -160 }"
    query-device-info-key="id"
    map-dom-offset-height="-520"
    @queryDeviceInfo="queryDeviceInfo"
    @handleMapClick="handleMapClick"
    @handleMarkerDragEnd="handleMarkerDragEnd"
    @handleClose="handleCloseBubble"
  >
    <template #mapBubbleDom>
      <edit-form-bubble
        ref="EditFormBubble"
        :is-edit="isBubbleEdit"
        :location-active="locationActive"
        :location-layerShow="locationCursor"
        :bubble-data="chooseMapItem"
        :device-direction-list="deviceDirectionList"
        :location-list="locationList"
        @handleEdit="handleEdit"
        @setLocation="setLocation"
        @handleCloseBubble="handleCloseBubble"
      ></edit-form-bubble>
    </template>
  </map-base>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'npgis-map',
  props: {
    cameraList: {
      type: Array,
      default: () => [],
    },
    bubbleData: {
      type: Object,
      default: () => {},
    },
    deviceDirectionList: {
      type: Array,
      default: () => [],
    },
    locationList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    let _this = this;
    return {
      mapPosition: {},
      geoRegions: [],
      chooseMapItem: {},
      isBubbleEdit: false,
      locationActive: false,
      locationCursor: false,
      customMarker: {
        width: 33,
        height: 33,
        func(marker) {
          const unqualified = ['0001', '0010', '0100', '0110', '0011', '0101', '0111']; // 不合格类型
          let url = '';
          if (unqualified.includes(marker.ext.isNormal)) {
            url =
              _this.themeType === 'dark'
                ? require('@/assets/img/device-map/map-camera-abnormal.png')
                : require('@/assets/img/device-map/map-camera-abnormal-light.png');
          } else {
            url =
              _this.themeType === 'dark'
                ? require('@/assets/img/device-map/map-camera-normal.png')
                : require('@/assets/img/device-map/map-camera-normal-light.png');
          }
          return url;
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  methods: {
    // 查询弹框的详细信息
    queryDeviceInfo(marker) {
      this.$emit('handleQueryDeviceInfo', marker);
    },
    handleEdit() {
      this.$set(this, 'isBubbleEdit', true);
    },
    setLocation() {
      this.$refs.MapBase.closeAllInfoWindow(); // 关闭弹框
      this.locationActive = true;
      this.locationCursor = true;
      this.$emit('clear'); // 清除所有图层
      this.$refs.MapBase.handleAddMapEventListener('MAP_EVENT_CLICK', (point) => {
        this.handleMapClick(point);
      }); // 监听点位点击事件
    },
    // 地图点击事件回调
    handleMapClick(point) {
      const markerParam = {
        url: require('@/assets/img/device-map/location.png'), // 图片路径
        size: {
          // [图片大小]
          width: 32,
          height: 32,
        },
      };
      if (!this.locationCursor) return false;
      this.$refs.MapBase.handleAddMarker(point, markerParam); // 添加定位标注
      this.chooseMapItem = Object.assign(this.chooseMapItem, {
        latitude: point.lat,
        longitude: point.lon,
      }); // 打开弹框

      this.handleAddMarkerEnd();
    },
    // 添加定位标注后的回调
    handleAddMarkerEnd() {
      this.locationCursor = false;
      this.$refs.MapBase.removeEvent('MAP_EVENT_CLICK'); // 移除地图点击监听事件
      this.$refs.MapBase.handleMarkerEnableEditing('NPMap.ModifyFeature_DRAG'); // 定位标注开启编辑
      this.$refs.MapBase.markerDragEnd(); // 监听点位拖拽结束
    },
    // 标注拖拽结束的回调
    handleMarkerDragEnd(point) {
      this.chooseMapItem = Object.assign(this.chooseMapItem, {
        latitude: point.lat,
        longitude: point.lon,
      });
    },
    handleCloseBubble(isNeedLoadList = false) {
      this.locationActive = false;
      this.isBubbleEdit = false;
      this.$refs.MapBase.removeListenerDragEnd();
      this.$refs.MapBase.closeAllInfoWindow();
      this.$refs.MapBase.handleRemoveMarker();
      if (isNeedLoadList) {
        this.$emit('handleUpdatePointInfo');
      }
    },
    handleAddNewOverLays(arr) {
      let obj = {
        type: 'multiPolygon',
        pps: arr,
      };
      this.$refs.MapBase.drawPosition(obj);
    },
    //关闭地图边界
    closeMapBoundary() {
      this.$refs.MapBase.handleClearLayers();
    },
  },
  watch: {
    bubbleData: {
      handler(val) {
        let point = Object.assign({}, val);
        point.deviceName = val.deviceName;
        point.type = 'device';
        point.deviceId = val.deviceId;
        point.id = val.id;
        point.address = val.address;
        point.checkStatus = val.checkStatus;
        point.tagList = val.tagList;
        point.imageUrls = val.imageUrls;
        point.errorMessage = val.errorMessage;
        this.chooseMapItem = point;
      },
      deep: true,
    },
  },
  components: {
    MapBase: require('@/components/map-base/index.vue').default,
    EditFormBubble: require('@/components/bubble/modules/EditFormBubble.vue').default,
  },
};
</script>

<style lang="less" scoped></style>
