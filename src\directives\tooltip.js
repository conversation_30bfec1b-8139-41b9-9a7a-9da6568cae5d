import { Tooltip } from 'view-design';
import Vue from 'vue';
const transferClassName = 'single-instance-tooltip';
function createTooltip(el, bind) {
  const tooltip = Vue.extend(Tooltip);
  const element = document.createElement('div');
  element.setAttribute('id', 'tooltip');
  return new tooltip({
    el: document.createElement('div'),
    propsData: {
      transfer: true,
      transferClassName,
      visible: true,
      placement: 'top',
      // placement: "right",
      content: bind.value,
    },
  });
}
function handleMouseEnter(el, bind, vnode) {
  el.tooltipInstance = createTooltip(el, bind, vnode);
  el.appendChild(el.tooltipInstance.$el);
  el.tooltipInstance.handleShowPopper();
}
function handleMouseLeave(el) {
  el.tooltipInstance.handleClosePopper();
  el.tooltipInstance = null;
  delete el.tooltipInstance;
  const elements = Array.from(document.getElementsByClassName(transferClassName));
  elements.forEach((el) => {
    el.remove();
  });
}

export default function (Vue) {
  Vue.directive('tooltip', {
    bind() {},
    inserted(el, bind, vnode) {
      el.addEventListener('mouseenter', handleMouseEnter.bind(this, el, bind, vnode));
      el.addEventListener('mouseleave', handleMouseLeave.bind(this, el, bind, vnode));
    },
    update() {},
    unbind(el, bind, vnode) {
      el.tooltipInstance = null;
      delete el.tooltipInstance;
      el.removeEventListener('mouseenter', handleMouseEnter.bind(this, el, bind, vnode));
      el.removeEventListener('mouseleave', handleMouseLeave.bind(this, el, bind, vnode));
    },
  });
}
