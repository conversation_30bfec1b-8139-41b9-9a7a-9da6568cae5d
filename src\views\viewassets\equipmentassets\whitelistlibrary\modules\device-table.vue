<template>
  <div class="device-information">
    <div class="search-wrapper">
      <div class="mb-lg">
        <ui-label class="fl" :label="global.filedEnum.deviceName" :width="65">
          <Input
            class="input-width"
            v-model="searchData.deviceName"
            :placeholder="`请输入${global.filedEnum.deviceName}`"
          ></Input>
        </ui-label>
        <ui-label class="fl ml-lg" :label="global.filedEnum.deviceId" :width="65">
          <Input
            class="input-width"
            v-model="searchData.deviceId"
            :placeholder="`请输入${global.filedEnum.deviceId}`"
          ></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="数据来源" :width="65">
          <Select class="width-md" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
            <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.sbdwlx" :width="95">
          <Select
            class="width-md"
            v-model="searchData.sbdwlx"
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.sbgnlx" :width="110">
          <Select
            class="width-md"
            v-model="searchData.sbgnlx"
            :placeholder="`请选择${global.filedEnum.sbgnlx}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">
              {{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
      </div>
      <div class="over-flow">
        <ui-label class="inline" label="检测状态" :width="65">
          <Select
            class="width-md"
            v-model="searchData.checkStatuses"
            multiple
            placeholder="请选择检测状态"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in checkStatus" :key="index" :value="item.dataKey">
              {{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.phyStatus" :width="65">
          <Select
            class="width-md"
            v-model="searchData.phyStatus"
            :placeholder="`请选择${global.filedEnum.phyStatus}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="inline ml-lg" label="">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </ui-label>
        <ui-label :width="60" class="fr" label="">
          <Button class="merge-btn fr ml-sm" type="primary" @click="addRelativedevice('add')">
            <i class="icon-font icon-guanlianshebei mr-sm"></i>
            <span>添加设备</span>
          </Button>
          <Button class="merge-btn fr ml-sm" type="primary" :disabled="!checkedData.length" @click="removeBulkDevice()">
            <i class="icon-font icon-yichu mr-sm"></i>
            <span>移除</span>
          </Button>
        </ui-label>
      </div>
    </div>
    <div class="table-box">
      <div class="drag auto-fill" :draggable="false" @dragstart="dragStart($event)" @dragover="dragOver($event)">
        <ui-table
          class="ui-table auto-fill"
          ref="tableRef"
          :table-columns="tableColumns"
          :table-data="tableData"
          @selectTable="selectTable"
          :loading="loading"
        >
          <template #deviceName="{ row }">
            <span class="width-percent inline ellipsis" :title="row.deviceName">
              {{ row.deviceName }}
            </span>
          </template>
          <template slot="deviceId" slot-scope="{ row }">
            <span class="font-active-color pointer device-id" @click.stop="deviceArchives(row)">
              {{ row.deviceId }}
            </span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
              icon="icon-shebeidangan"
              content="设备档案"
              @click.native="deviceArchives(row)"
            ></ui-btn-tip>
            <ui-btn-tip icon="icon-yichu1" @click.native="removeOneDevice(row)" content="移除" />
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>

    <ul class="drag-dom-ul" ref="testRef">
      <li class="drag-dom-ul-li" v-for="(item, index) of checkedRow" :key="index">
        <p>{{ index + 1 }}</p>
        <p>{{ item.deviceId }}</p>
        <p>{{ item.deviceName }}</p>
        <p>{{ item.orgName }}</p>
        <p>{{ item.longitude }}</p>
        <p>{{ item.latitude }}</p>
        <p>{{ item.checkStatus }}</p>
        <p>{{ item.sourceName }}</p>
        <p>{{ item.address }}</p>
      </li>
    </ul>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

export default {
  props: {
    tableData: {
      default: () => [],
    },
    loading: {
      default: false,
    },
    totalCount: {
      default: 0,
    },
  },
  data() {
    return {
      selectedOrg: {},
      searchData: {
        deviceId: '',
        deviceName: '',
        sourceId: '',
        sbdwlx: '',
        sbgnlx: '',
        checkStatuses: [],
        phyStatus: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      statusList: [],
      ruleList: [{ label: '标准', value: 0 }],
      checkedData: [],
      checkedRow: [],
      tableColumns: [
        { type: 'selection', width: 50, align: 'center' },
        { title: '序号', width: 60, type: 'index', align: 'left' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          width: 180,
          slot: 'deviceName',
        },
        { title: '组织机构', key: 'orgName', align: 'left', minWidth: 120 },
        { title: '行政区划', key: 'civilName', align: 'left', minWidth: 120 },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          minWidth: 120,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          minWidth: 120,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 120,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          minWidth: 130,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          minWidth: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbdwlx}`,
          key: 'sbdwlxText',
          align: 'left',
          minWidth: 110,
        },
        {
          title: `${this.global.filedEnum.phyStatus}`,
          key: 'phyStatusText',
          align: 'left',
          minWidth: 80,
        },
        // { title: "检测状态", slot: "checkStatus", align: "left" },
        {
          title: '数据来源',
          key: 'sourceIdText',
          align: 'left',
          minWidth: 100,
          tooltip: true,
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          fixed: 'right',
          width: 90,
        },
      ],
    };
  },
  created() {
    if (this.propertySearchLbdwlx.length === 0) this.getAlldicData();
    this.copySearchDataMx(this.searchData);
  },
  mounted() {},
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    dragStart(event) {
      let dom = this.$refs.testRef;
      event.dataTransfer.setDragImage(dom, 10, 10);
    },
    dragOver(e) {
      e.preventDefault();
    },
    rowClick(data, index) {
      this.$refs.tableRef.$refs.table.toggleSelect(index);
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    selectTable(selection) {
      this.checkedRow = selection;
      this.checkedData = selection.map((row) => {
        return row.id;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    addRelativedevice(row) {
      this.$emit('addRelativeDeviceShow', row);
    },
    removeOneDevice(row) {
      this.$UiConfirm({
        content: `您将移除 ${row.deviceName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$emit('removeInit', [row.id]);
        })
        .catch((res) => {
          console.error(res);
        });
    },
    removeBulkDevice() {
      this.$UiConfirm({
        content: `您要移除这${this.checkedData.length}项设备，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$emit('removeInit', this.checkedData);
        })
        .catch((res) => {
          console.error(res);
        });
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.$emit('search', this.searchData);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.$emit('search', this.searchData);
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.$emit('search', this.searchData);
    },
  },
  watch: {
    totalCount: {
      handler(val) {
        this.pageData.totalCount = val;
      },
      immediate: true,
    },
    tableData: {
      handler() {
        this.checkedData = [];
      },
    },
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.device-information {
  display: flex;
  flex-direction: column;
  .search-wrapper {
    overflow: hidden;
    margin: 20px 0;
    .input-width {
      width: 200px;
    }
  }
  .table-box {
    position: relative;
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    .drag {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }
  .drag-dom-ul {
    position: absolute;
    z-index: -1;
    .drag-dom-ul-li {
      width: 500px;
      display: flex;
      justify-content: space-around;
      color: #fff;
      height: 40px;
      line-height: 40px;
      padding: 0 10px;
      > p {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
