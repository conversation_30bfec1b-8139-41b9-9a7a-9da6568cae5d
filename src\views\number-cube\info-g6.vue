<template>
  <div class="number-cube-info">
    <!-- 顶部导航 -->
    <cubeTabs
      :tabs-list="tabsList"
      :current-tag="tabFlag"
      @tabClick="tabClick"
      @tabDel="tabDel"
      @tabDelMore="tabDelMore"
      @save="save"
      v-show="!fullscreenState"
    />
    <div
      class="relation-graph-header"
      :class="{ 'tool-close': !toolStatus }"
      v-show="!fullscreenState"
    >
      <!-- 操作 -->
      <operate
        ref="operate"
        :saveExcavateLoading="saveExcavateLoading"
        :isExcavate="isExcavate"
        :excavateType="excavateType"
        :guanxiwajueData="guanxiwajueData"
        :current-tab="currentTab"
        :entitysList="entitysList"
        :layoutName="layoutName"
        :nodeFilterShow="nodeFilterShow"
        :relationFilterShow="relationFilterShow"
        @locationEntity="locationEntity"
        @save="save"
        @downloadAsImage="downloadAsImage"
        @addNode="addNode"
        @deleteNode="deleteNode"
        @suoding="suoding"
        @dataFilter="dataFilter"
        @pathDeductionHandle="pathDeductionHandle"
        @lianjiefenxi="lianjiefenxi"
        @communityAnalysisHandle="communityAnalysisHandle"
        @layoutClick="layoutClick"
        @closeRelationView="closeRelationView"
        @closeAnalysis="closeAnalysis"
        @excavateBack="excavateBack"
        @excavateClose="excavateClose"
        @excavateSave="excavateSave"
        @guanxiwajue="guanxiwajue"
        @relationExcavate="relationExcavate"
        @delRelationExcavate="delRelationExcavate"
        @viewRelationExcavate="viewRelationExcavate"
      />
    </div>
    <div v-show="!fullscreenState" class="full" @click="fullScreen">全屏</div>
    <img
      v-show="!fullscreenState"
      class="switch"
      :class="{ rotate: !toolStatus }"
      src="@/assets/img/down-circle-icon.png"
      alt
      @click="openClose()"
    />
    <AntVG6
      class="antv-g6"
      ref="antVG6Ref"
      :isExcavate="isExcavate"
      :has-minimap="true"
      :graph-data="graphData"
      :defaultLayoutName="layoutName"
      @nodeContextmenu="nodeContextmenu"
      @canvasClick="canvasClick"
      @expandGroup="expandGroup"
      @wheelzoom="wheelzoom"
      @edgeClick="edgeClick"
      @nodeDblclick="nodeDblclick"
    ></AntVG6>

    <!-- 底部统计 -->
    <div class="footer-statistics" v-if="!currentTab.relation && graphData">
      <div>
        实体：<span class="line-color">{{ statistic.nodesLength }}</span>
      </div>
      <div>
        关系：<span class="line-color">{{ statistic.edgesLength }}</span>
      </div>
    </div>

    <!-- 底部提示 -->
    <div class="footer-tips">
      提示：<span class="line-color">Ctrl+鼠标左键</span> 可选中多个，
      <span class="line-color">Shift + 鼠标左键</span>可框选多个
    </div>

    <ToolbarRight
      ref="toolbarRightRef"
      @changeZoom="changeZoom"
      @fitView="fitView"
    ></ToolbarRight>

    <!-- 保存 - 另存为 -->
    <RelationGraphSave
      ref="relationGraphSave"
      v-model="relationGraphSaveShow"
      :graph-name="graphName"
      @graphSave="graphSave"
    />

    <!--增加实体 同 检索结果 输入框-->
    <SearchResultInput
      ref="searchResultInput"
      v-model="searchResultInputShow"
      :page="true"
      @queryInfo="addEntity"
    />

    <!-- 实体筛选、关系筛选 -->
    <div
      class="data-filter"
      v-show="relationFilterShow || nodeFilterShow"
      :style="{ height: relationFilterShow && nodeFilterShow ? '38%' : '80%' }"
    >
      <dataFilter
        ref="edgeFilterRef"
        class="filter-conent"
        v-show="relationFilterShow"
        type="link"
        :graph-data="graphData"
        :group-list="edgesGroupList"
        @dataFilter="relationFilterChange"
        @closeDataFilter="closeDataFilter('node')"
      />

      <dataFilter
        ref="nodeFilterRef"
        class="filter-conent node-filter"
        v-show="nodeFilterShow"
        type="node"
        :graph-data="graphData"
        :group-list="nodesGroupList"
        @dataFilter="nodeFilterChange"
        @closeDataFilter="closeDataFilter('link')"
      />
    </div>

    <!-- 路径推演 -->
    <PathDeductionModal
      ref="pathDeductionRef"
      :path-show-obj="pathShowObj"
      @pathShowBack="pathShowBack"
    />

    <!-- 连接分析 -->
    <RelationGraphLianjiefenxi
      v-model="relationGraphLianjiefenxiShow"
      :path-show-obj="pathShowObj"
      @connectionAnalysis="connectionAnalysis"
    />

    <!-- 社群分析 -->
    <CommunityAnalysisModal
      ref="communityAnalysisRef"
      @socialAnalysis="socialAnalysis"
    />

    <!-- 对象详情 -->
    <ObjectDetails
      ref="ObjectDetailsRef"
      v-model="objectDetailsVisble"
      :detail-relation-list="detailRelationList"
      :detail-obj-info="detailObjInfo"
      :property-list="propertyList"
      @relationDetailModal="relationDetailModal"
      @excavateShow="excavateShow"
      :isExcavate="isExcavate"
    />

    <!--  关系详情-->
    <RelationshipInfo
      ref="relationInfoRef"
      v-model="relationshipInfoVisble"
      :from-node="detailObjInfo"
      :to-node="toNode"
      :property-list="propertyList"
      :relationMiningExecLogId="relationMiningExecLogId"
    />
    <NodeContextMenu
      ref="nodeContextMenuRef"
      v-model="nodeContextmenuShow"
      :position="nodeContextmenuPosition"
      :node="contextNode"
      @lock="lock"
      @deleteNode="contextDeleteNode"
      @lianjiefenxi="contextLianjiefenxi"
      @getRelationships="getRelationships"
      @objectDetail="objectDetail"
      @consanguinitySearch="consanguinitySearch"
      @fusionSearch="fusionSearch"
      @relationalViews="relationalViews"
      @technicalTactics="technicalTactics"
      @relationExcavate="relationExcavate"
    >
    </NodeContextMenu>

    <!-- 关系挖掘新增 -->
    <relationExcavateAddModel
      ref="relationExcavateAddRef"
      :isExcavate="isExcavate"
      @getRelationExcavateData="getRelationExcavateData"
    />

    <!-- 关系挖掘配置详情 -->
    <relationMiningModel
      ref="relationMiningModelRef"
      :excavateNodeConfigData="guanxiwajueData.excavateNodeConfigData"
    />
    <ui-loading v-if="excavateLoading" />
  </div>
</template>
<script>
import AntVG6 from "@/components/antv-g6/index.vue";
import { mapGetters, mapActions } from "vuex";
import { deepCopy, base64toFile } from "@/util/modules/common";
import {
  canvasDetail,
  relationDetail,
  relationDetail_v2,
  imgUpload,
  canvasAdd,
  canvasUpdate,
  propertyList,
  consanguinitySearch,
  relationView,
  saveMiningRelation,
  exec,
  deleteMiningModel,
  getAllReachablePaths,
  entitySearch,
} from "@/api/number-cube";
import { findG6AllPath, getG6ConnectionPath } from "./js/getGraphAlgorithm";
import screenfull from "screenfull";
import { entitySearchMixins } from "@/components/gpt/mixins/getEntitySearch";

export default {
  name: `number-cube-info`,
  props: {},
  data() {
    return {
      routerQuery: {},
      graphName: "",
      searchResultInputShow: false,
      graphData: {
        nodes: [],
        edges: [],
      },
      entitysList: [],
      nodesGroupList: [],
      edgesGroupList: [],
      currentTab: {
        analysis: false,
        relation: false,
      },
      relationGraphSaveShow: false,
      toolStatus: true, // 工具栏显示开关

      relationFilterShow: false,
      nodeFilterShow: false,
      relationGraphLianjiefenxiShow: false,

      nodeContextmenuShow: false,
      nodeContextmenuPosition: { x: 0, y: 0 },
      contextNode: {},

      objectDetailsVisble: false,
      detailRelationList: [],
      detailObjInfo: {},
      toNode: {},
      propertyList: [],

      relationshipInfoVisble: false,
      fullscreenState: false,

      excavateLoading: false,
      saveExcavateLoading: false,
      excavateData: {}, //挖掘组装后数据
      excavateNode: {}, //挖掘节点
      excavateNodeConfigData: {}, //挖掘节点配置数据
      excavateResData: {}, //挖掘执行后端返回数据
      isExcavate: false, //挖掘页面
      excavateType: "", //excavateAdd 新增挖掘 '' 已有关系挖掘
      relationMiningModelId: "", //挖掘模型id
      relationMiningExecLogId: "", //挖掘任务id
      guanxiwajueData: {}, //操作栏关系挖掘数据
      excavateSelectArr: [], //挖掘选择后关系
      layoutName: "gForce",
      excavateResultData: null,
      relationViewData: {},
    };
  },
  mixins:[entitySearchMixins],
  mounted() {
    if (
      this.$route.name === "number-cube-info" &&
      this.$route.query &&
      this.$route.query.from !== "gpt"
    ) {
      this.$nextTick(() => {
        this.getGraphData(false);
      });
    }
    this.$eventBus.$on("getSaveGraphData",this.getSaveGraphData)
    this.detectFullscreenChange();
  },
  methods: {
    ...mapActions({
      getSystemData: "systemParam/getSystemData",
      addRouteFlag: "number-cube/addRouteFlag",
      setTabsList: "number-cube/setTabsList",
      setTabsFlag: "number-cube/setTabsFlag",
    }),
    openClose() {
      this.toolStatus = !this.toolStatus;
    },
    detectFullscreenChange() {
      if (screenfull.isEnabled) {
        screenfull.on("change", () => {
          this.$refs.antVG6Ref.fullScreen(screenfull.isFullscreen);
          this.fullscreenState = !this.fullscreenState;
        });
      }
    },
    // 全屏展示
    fullScreen() {
      screenfull.request(document.querySelector(".number-cube-info"));
    },
    setTab({ context, currentViewFlag, relationViewContent }) {
      let tab = {};
      const tabFlag = "tag" + this.routeFlag;
      if (this.routerQuery.type === "add") {
        const query = { ...this.routerQuery };
        delete query.ids;
        tab = {
          name: "未命名",
          flag: tabFlag,
          save: false, // 是否已保存
          relation: currentViewFlag === 2 ? true : false, // 是否关系视图
          query: {
            ...query,
            entityIds: JSON.parse(this.routerQuery.ids).split(","),
          },
          context,
          relationViewContent,
        };
      }
      if (this.routerQuery.type === "detail") {
        tab = {
          name: this.routerQuery.name,
          flag: tabFlag,
          save: true,
          relation: currentViewFlag === 2 ? true : false,
          query: {
            ...this.routerQuery,
          },
          context,
          relationViewContent,
        };
      }
      this.tabsList.push(tab);
      this.currentTab = tab;
      this.addRouteFlag();
      this.setTabsFlag(tabFlag);
      this.setTabsList(this.tabsList);
    },
    async getGraphData(isClearData = true, param, isUpdateLayout = false) {
      try {
        isClearData && this.clear();
        this.routerQuery = param || this.$route.query;
        this.graphName = this.routerQuery.name;
        let data = {
          nodes: [],
          edges: [],
        };
        /**
         * 切换菜单路由之后的判断
         * 1. 如果vuex中已经有存放此画布的信息则直接赋值，不创建新tab
         * 2. 是否是编辑已存在的画布
         * 3. 使用实体创建新的画布
         *  */
        const tab = this.tabsList.find((tab) => {
          if (this.routerQuery.id) {
            return String(tab.query.id) === String(this.routerQuery.id);
          } else if (this.routerQuery.ids && tab.query.entityIds) {
            return (
              JSON.stringify(tab.query.entityIds.join(",")) ===
                this.routerQuery.ids &&
              String(tab.query.maxDepth) === String(this.routerQuery.maxDepth)
            );
          }
        });
        if (tab) {
          data = tab.relation
            ? tab.relationViewContent
            : this.assemblyData(tab.context);
          this.currentTab = tab;
        } else if (this.routerQuery.id) {
          /**
           * 1. 关系视图
           * 2. 普通画布
           */
          const { context, currentViewFlag, relationViewContent, layoutName } =
            await this.getGraphDataById(this.routerQuery.id);
          layoutName && (this.layoutName = layoutName);
          data = {
            nodes:
              currentViewFlag === 2 ? relationViewContent.nodes : context.nodes,
            edges:
              currentViewFlag === 2 ? relationViewContent.edges : context.edges,
          };
          this.setTab({
            context: context,
            currentViewFlag: currentViewFlag,
            relationViewContent: relationViewContent,
          });
        } else {
          const params = {
            entityIds: JSON.parse(this.routerQuery.ids).split(","),
            relationCnNames: this.routerQuery.relationNames
              ? JSON.parse(this.routerQuery.relationNames)
              : undefined,
            maxDepth: this.routerQuery.maxDepth,
            entityIdInstanceIdMap: JSON.parse(
              this.routerQuery.entityIdInstanceIdMap || "{}"
            ),
          };
          if (Object.keys(this.archiveObj).length == 0) {
            await this.getSystemData("ICBD_ARCHIVES_CONFIG");
          }
          // 临时解决方案
          if (this.routerQuery.isRelation) {
            params.graphInstanceId = this.archiveGraphInfo.instanceId;
          }
          const context = await this.getGraphDataByEntiy(params);
          /**
           * 新创建的画布
           * 1. 从档案跳转过来直接打开关系视图
           * 2. 从数智立方跳转创建普通画布
           */

          //  从档案跳转过来直接打开关系视图
          if (this.routerQuery.isRelation) {
            const param = {
              entityId: JSON.parse(this.routerQuery.ids),
              maxDepth: 1,
              graphInstanceId: this.archiveGraphInfo.instanceId,
            };
            const res = await relationView(param);
            const relationData = this.dealRelationData(
              res,
              param.entityId,
              false // !!this.routerQuery.isRelation,
            );
            const relationViewContent = {
              nodes: [...relationData.nodes, ...relationData.groups],
              edges: relationData.edges,
            };
            data = relationViewContent;
            this.setTab({ context, currentViewFlag: 2, relationViewContent });
          } else {
            data = context;
            this.setTab({
              context,
              currentViewFlag: 1,
              relationViewContent: "",
            });
          }
        }

        this.$nextTick(() => {
          this.graphData = {
            nodes: deepCopy(data.nodes),
            edges: deepCopy(data.edges),
          };

          this.$nextTick(() => {
            data.nodes.forEach((row) => {
              this.$refs.antVG6Ref.visibleItemById(
                row.id,
                typeof row.visible === "boolean" ? row.visible : true
              );
            });
            isUpdateLayout &&
              this.$refs.antVG6Ref.layoutSetting.updateLayout(this.layoutName);
            // this.routerQuery.isRelation &&
            //   this.$refs.antVG6Ref.layoutSetting.updateLayout("force");

            // this.layoutClick("avoidlap");
            this.getGroup(this.graphData);
            !tab &&
              this.routerQuery.type === "add" &&
              this.highlightSerchEntitys();
          });
        });
      } catch (err) {
        console.log(err, "err");
      }
    },
    // 新增图谱-高亮搜索的实体
    highlightSerchEntitys() {
      const entityIds = JSON.parse(this.routerQuery.ids).split(",");
      this.$refs.antVG6Ref.setState(
        {
          nodes: entityIds.map((id) => ({ id })),
          edges: [],
        },
        "selected"
      );
    },
    /**
     * 根据前端保存时组装的数据context组装数据
     * 区别于 assemblyDataByBackend 处理的数据
     * @param {*} id
     */
    async getGraphDataById(id) {
      this.$nextTick(() => {
        this.$refs.antVG6Ref.loadingShow({ visible: true, text: "获取数据中" });
      });
      const { data } = await canvasDetail(id, { async: false });
      const contextData = JSON.parse(data.context);
      const context = this.assemblyData(contextData);
      let layoutName = contextData.layoutName;

      // 关系视图的需要额外处理
      let relationViewContent = "";
      if (data.currentViewFlag === 2) {
        relationViewContent = JSON.parse(data.relationViewContent);
        layoutName = data.relationViewContent?.layoutName;
        relationViewContent.nodes = relationViewContent.nodes.map((row) => {
          // 如果是关系视图的节点需要额外处理
          if (row.ext.groupFlag) {
            return {
              id: row.id,
              label: row.label,
              group: row.group,
              groupCn: row.groupCn,
              isGroup: true,
              isExpand: row.isExpand,
              visible: row.visible,
              ext: row.ext,
              fx: row.x,
              fy: row.y,
            };
          } else {
            return {
              id: row.id,
              label: row.label,
              img: row.img || "",
              group: row.group,
              groupCn: row.groupCn,
              visible: row.visible,
              ext: row.ext,
              fx: row.x,
              fy: row.y,
            };
          }
        });
        relationViewContent.edges = relationViewContent.edges.map((row) => {
          return {
            source: row.source,
            target: row.target,
            id: row.id,
            label: row.label,
            ext: row.ext,
          };
        });
      }
      return {
        context,
        currentViewFlag: data.currentViewFlag,
        relationViewContent,
        layoutName,
      };
    },
    /**
     * 组装前端保存到后端的json
     */
    assemblyData(data) {
      const nodes = data.nodes.map((row) => {
        return {
          id: row.id,
          label: row.label,
          img: row.img || "",
          group: row.group,
          groupCn: row.groupCn,
          visible: row.visible,
          ext: row.ext,
          [data.layoutName === "gForce" ? "fx" : "x"]: row.x,
          [data.layoutName === "gForce" ? "fy" : "y"]: row.y,
        };
      });
      const edges = data.edges.map((row) => {
        return {
          source: row.source,
          target: row.target,
          id: row.id,
          label: row.label,
          visible: row.visible,
          style: {
            stroke: row.ext.detail.color,
            ...JSON.parse(row.ext.detail.lineStyle || "{}"),
          },
          //   labelCfg: {
          //     style: {
          //       fill: row.ext.detail.color,
          //     },
          //   },
          ext: row.ext,
        };
      });
      return {
        nodes,
        edges,
      };
    },
    async getGraphDataByEntiy(param) {
      this.$nextTick(() => {
        this.$refs.antVG6Ref.loadingShow({ visible: true, text: "获取数据中" });
      });
      const queryApi = this.relationQueryObj.edgeCluster ? relationDetail_v2 : relationDetail;
      const res = await queryApi(param);
      const { nodes, edges } = this.assemblyDataByBackend(res);
      return {
        nodes,
        edges,
      };
    },
    /**
     * 组装由后端返回的数据
     * @param {*} res
     */
    assemblyDataByBackend(res) {
      const entitys = res.data.entitys || [];
      const relations = res.data.relations || [];
      const nodes = entitys.map((row) => {
        return {
          id: row.entityId,
          label: row.displayField,
          img: row.propertyIcon || row.icon,
          group: row.label,
          groupCn: row.labelCn,
          ext: row,
        };
      });
      const edges = relations.map((row) => {
        const info = row.detail || row;
        return {
          source: row.sourceId,
          target: row.targetId,
          id: row.id,
          label: `${info.labelCn} ${row.items ? `${row.items}次` : ""}`,
          style: {
            stroke: info.color,
            ...JSON.parse(info.lineStyle || "{}"),
          },
          //   labelCfg: {
          //     style: {
          //       fill: "#2c86f8",
          //     },
          //   },
          ext: row.detail ? row : { detail: row },
        };
      });
      return {
        nodes,
        edges,
      };
    },
    getGroup(data) {
      this.entitysList = data.nodes;
      this.nodesGroupList = this.getNodesGroup(data.nodes);
      this.edgesGroupList = this.getEdgesGroup(data.edges);
    },
    updateGroup() {
      const data = {
        nodes: this.$refs.antVG6Ref.getNodes(),
        edges: this.$refs.antVG6Ref.getEdges(),
      };
      this.getGroup(data);
    },
    // 获取分组
    getNodesGroup(nodes) {
      const groupList = [];
      nodes.forEach((row) => {
        const groupItem = groupList.find((item) => item.label === row.group);
        if (groupItem) {
          groupItem.num++;
        } else {
          groupList.push({ labelCn: row.groupCn, num: 1, label: row.group });
        }
      });
      return groupList;
    },
    getEdgesGroup(edges) {
      const groupList = [];
      edges.forEach((row) => {
        // 只有有详情的关系才统计（关系视图下部分关系是没有详情的）
        if (row.ext.detail) {
          const groupItem = groupList.find(
            (item) => item.label === row.ext.detail.label
          );
          if (groupItem) {
            groupItem.num++;
            groupItem.ids.push(row.id);
          } else {
            groupList.push({
              labelCn: row.ext.detail.labelCn,
              num: 1,
              label: row.ext.detail.label,
              ids: [row.id],
            });
          }
        }
      });
      return groupList;
    },
    // 获取当前画布数据并且组装数据
    assemblingGraphData() {
      const data = {
        nodes: this.$refs.antVG6Ref.getNodes().map((node) => {
          return {
            id: node.id,
            label: node.label,
            img: node.img,
            group: node.group,
            groupCn: node.groupCn,
            visible: node.visible,
            isExpand: node.isExpand,
            ext: node.ext,
            x: node.x,
            y: node.y,
          };
        }),
        edges: this.$refs.antVG6Ref.getEdges().map((edge) => {
          return {
            id: edge.id,
            label: edge.label,
            source: edge.source,
            target: edge.target,
            ext: edge.ext,
          };
        }),
      };
      return data;
    },
    getSaveGraphData(cb){
      const data = this.assemblingGraphData();
      const contextJson = this.currentTab.relation
              ? {
                  ...this.currentTab.context,
                  layoutName: "gForce",
             } : { ...data, layoutName: this.layoutName };
       cb(contextJson)
    },
    save(resolve) {
      // 如果没有图谱名称则需要输入名称
      if (!this.graphName) {
        this.relationGraphSaveShow = true;
        this.$refs.relationGraphSave.formCustom.name = "";
        return;
      }
      const data = this.assemblingGraphData();
      this.$refs.antVG6Ref.toFullDataURL({
        callback: async (dataUrl) => {
          const file = base64toFile(dataUrl, this.graphName);
          const formData = new FormData();
          formData.append("file", file);
          const res = await imgUpload(formData);
          /**
           * 由于可以保存关系视图
           * 在关系视图下，这里需要既保存正常画布的数据也要保存关系视图的数据，可以在再次打开时处于关系视图下也可以关闭关系视图回到正常视图中
           */
          const params = {
            id: this.routerQuery.id,
            name: this.graphName,
            imageUrl: res.data.fileUrl,
            currentViewFlag: this.currentTab.relation ? 2 : 1,
            graphId: this.relationObj.graphInfo.graphId,
            // 如果保存的是关系视图则需要保存当前tab中存储的非关系图谱数据
            context: this.currentTab.relation
              ? JSON.stringify({
                  ...this.currentTab.context,
                  layoutName: "gForce",
                })
              : JSON.stringify({ ...data, layoutName: this.layoutName }),
            // 如果保存的是关系视图则需要保存当前图谱数据
            relationViewContent: this.currentTab.relation
              ? JSON.stringify({ ...data, layoutName: "gForce" })
              : JSON.stringify(""),
          };
          const index = this.tabsList.findIndex(
            (tab) => tab.flag === this.tabFlag
          );
          if (this.routerQuery.id) {
            await canvasUpdate(params);
            this.$Message.success("编辑成功");
            this.relationGraphSaveShow = false;
            this.tabsList[index].save = true;
            this.currentTab.save = true;
          } else {
            const res = await canvasAdd(params);
            this.$Message.success("保存成功");
            this.relationGraphSaveShow = false;
            const tab = {
              name: res.data.name,
              flag: this.tabsList[index].flag,
              save: true,
              relation: this.currentTab.relation,
              query: {
                id: res.data.id,
                name: res.data.name,
                type: "detail",
                // refresh:true // 是否需要刷新页面
              },
              // 如果保存的是关系视图则需要保存当前tab中存储的非关系图谱数据
              context: this.currentTab.relation
                ? this.currentTab.context
                : data,
              // 如果保存的是关系视图则需要保存当前图谱数据
              relationViewContent: this.currentTab.relation ? data : "",
            };
            this.tabsList.splice(index, 1, tab);
            this.currentTab = tab;
            // this.$router.push({
            //   path: this.$route.path,
            //   query: tab.query,
            // });
          }
          this.setTabsList(this.tabsList);
          // 关闭tab
          resolve && resolve();
        },
        type: "image/jpeg",
        imageConfig: {
          backgroundColor: "#fff",
        },
      });
    },
    graphSave(name) {
      this.graphName = name;
      this.save();
    },
    // 查找实体并定位
    locationEntity(id) {
      this.$refs.antVG6Ref.locationItem(id);
    },
    // 导出图片
    downloadAsImage(type) {
      this.$refs.antVG6Ref.downloadFullImage(this.routerQuery.name, type, {
        backgroundColor: "#fff",
      });
    },
    /**
     * 增加实体
     */
    addNode() {
      this.$refs.searchResultInput.selectLableList = [];
      this.searchResultInputShow = true;
    },
    deleteNode() {
      const nodes = this.$refs.antVG6Ref.getNodesByState("selected");
      this.$refs.antVG6Ref.deleteNodes(nodes);
      this.updateGroup();
      this.updateTab();
    },
    suoding(isLock) {
      this.$refs.antVG6Ref.lock(
        isLock,
        this.$refs.antVG6Ref.getNodesByState("selected")
      );
    },
    /**
     * 关系筛选、实体筛选同时出现，互不影响
     * @param {*} type link  node
     */
    dataFilter(type) {
      if (type === "node") {
        this.nodeFilterShow = !this.nodeFilterShow;
      } else {
        this.relationFilterShow = !this.relationFilterShow;
      }
    },
    /**
     * 关系筛选按钮关闭
     */
    closeDataFilter(type) {
      if (type === "node") {
        this.$refs.operate.relationShaiXuanClick();
      } else {
        this.$refs.operate.entityShaiXuanClick();
      }
    },
    pathDeductionHandle() {
      const selectedNodes = this.$refs.antVG6Ref.getNodesByState("selected");
      if (selectedNodes && selectedNodes.length === 2) {
        this.$refs.pathDeductionRef.init();
      } else {
        this.$Message.warning("路径推演必须选中有且只有2个实体");
      }
    },
    // 连接分析
    lianjiefenxi() {
      const selectedNodes = this.$refs.antVG6Ref.getNodesByState("selected");
      if (selectedNodes && selectedNodes.length === 1) {
        this.relationGraphLianjiefenxiShow = true;
      } else {
        this.$Message.warning("连接分析必须选中有且只有1个实体");
      }
    },
    // 社群分析
    communityAnalysisHandle() {
      this.$refs.communityAnalysisRef.init();
    },
    // 布局
    layoutClick(item) {
      this.layoutName = item.type;
      this.$refs.antVG6Ref.loadingShow({
        visible: true,
        text: "重新布局中...",
      });
      requestIdleCallback(() => {
        this.$refs.antVG6Ref.layoutSetting.updateLayout(item.type);
      });
    },
    /**
     * 关闭关系视图
     */
    closeRelationView() {
      this.tabsList.find((tab) => tab.flag === this.tabFlag).relation = false;
      this.$set(this.currentTab, "relation", false);
      this.getGraphData(undefined, undefined, true);
      requestIdleCallback(() => {
        this.updateTab();
      });
    },

    // 关闭分析
    closeAnalysis() {
      this.clearAllStates();
      this.$set(this.currentTab, "analysis", false);
    },
    clearAllStates() {
      this.$refs.antVG6Ref.clearStates([
        "analysis",
        "secondary",
        "connectionAnalysis",
      ]);
    },
    async addEntity(obj) {
      try {
        const { nodes, edges } = await this.getGraphDataByEntiy({
          entityIds: JSON.parse(obj.ids).split(","),
          maxDepth: obj.maxDepth,
          entityIdInstanceIdMap: JSON.parse(obj.entityIdInstanceIdMap || "{}"),
        });

        const newData = this.$refs.antVG6Ref.getAddNodes({
          nodes,
          edges,
        });
        if (newData.nodes.length || newData.edges.length) {
          this.$refs.antVG6Ref.addNodes(newData);
          const data = this.assemblingGraphData();
          this.$refs.antVG6Ref.changeData(data);
          this.updateTab();
          this.updateGroup();
        } else {
          this.$refs.antVG6Ref.loadingShow({ visible: false });
        }
      } catch (err) {
        console.error(err, "err");
      }
    },
    // 更新tab
    updateTab() {
      this.$nextTick(() => {
        const data = this.assemblingGraphData();
        const tabIndex = this.tabsList.findIndex(
          (tab) => tab.flag === this.tabFlag
        );
        const tab = {
          ...this.tabsList[tabIndex],
          save: false,
          relation: this.currentTab.relation,
          // 如果保存的是关系视图则需要保存当前tab中存储的非关系图谱数据
          context: this.currentTab.relation ? this.currentTab.context : data,
          // 如果保存的是关系视图则需要保存当前图谱数据
          relationViewContent: this.currentTab.relation ? data : "",
        };
        this.tabsList.splice(tabIndex, 1, tab);
        this.setTabsList(this.tabsList);
      });
    },
    /**
     * tab点击回调
     */
    tabClick(tabName) {
      this.setTabsFlag(tabName);
      const tab = this.tabsList.find((tab) => tab.flag === tabName);
      /**
       * 如果是编辑画布的标签则更改浏览器query中的参数为编辑所需的
       * 如果是新增则更改浏览器query中的参数为新增所需的
       *  */
      let query = null;
      if (tab.query.id) {
        query = tab.query;
      } else {
        // 删除query中的多余字段且不影响本身
        const tabQuery = { ...tab.query };
        delete tabQuery.entityIds;
        query = {
          ids: JSON.stringify(tab.query.entityIds.join(",")),
          ...tabQuery,
        };
      }
      this.$router.push({
        path: this.$route.path,
        query: query,
      });
    },
    /**
     * 删除tab(单个删除)
     */
    tabDel(tabName) {
      if (this.tabFlag === tabName) {
        let tabFlag = "";
        const index = this.tabsList.findIndex((tab) => tab.flag === tabName);

        if (index === 0) {
          tabFlag = this.tabsList[0].flag;
        } else {
          tabFlag = this.tabsList[index - 1].flag;
          const tab = this.tabsList[index - 1];
          /**
           * 如果是编辑画布的标签则更改浏览器query中的参数为编辑所需的
           * 如果是新增则更改浏览器query中的参数为新增所需的
           *  */
          let query = null;
          if (tab.query.id) {
            query = tab.query;
          } else {
            // 删除query中的多余字段且不影响本身
            const tabQuery = { ...tab.query };
            delete tabQuery.entityIds;
            query = {
              ids: JSON.stringify(tab.query.entityIds.join(",")),
              ...tabQuery,
            };
          }
          this.$router.push({
            path: this.$route.path,
            query: query,
          });
        }
        this.setTabsFlag(tabFlag);
      }
      this.setTabsList(this.tabsList.filter((tab) => tab.flag !== tabName));
    },
    /**
     * 删除tab（右击多个删除）
     * @param {*} type 右击删除类型
     * @param {*} rightTab 右击tab标识
     */
    tabDelMore(type, rightTab) {
      if (type === "all") {
        const arr = this.tabsList.filter((tab) => !tab.save);
        if (arr.length > 0) {
          this.$Modal.confirm({
            title: "图谱删除",
            content: "该图谱尚未保存，确定删除吗？",
            onOk: () => {
              this.setTabsFlag("");
              this.setTabsList([]);
              this.$router.push({ name: "number-cube" });
            },
          });
        }
      } else {
        const index = this.tabsList.findIndex((tab) => tab.flag === rightTab);
        if (type === "left") {
          this.tabsList = this.tabsList.slice(index);
        }
        if (type === "right") {
          this.tabsList = this.tabsList.slice(0, index + 1);
        }
        if (type === "other") {
          this.tabsList = [this.tabsList[index]];
        }
        const exis = this.tabsList.find((tab) => tab.flag === this.tabFlag);
        if (!exis) {
          this.setTabsFlag(this.tabsList[this.tabsList.length - 1].flag);
        }
      }
    },
    /**
     * 关系筛选
     */
    relationFilterChange(filterLabel) {
      const edges = this.$refs.antVG6Ref.getEdges();
      edges.forEach((edge) => {
        const visible = filterLabel.includes(edge.ext.detail.label);
        const edgeItem = this.$refs.antVG6Ref.getNodeById(edge.id);
        const sourceNode = edgeItem.getSource();
        const targetNode = edgeItem.getTarget();
        if (sourceNode.isVisible() && targetNode.isVisible()) {
          this.$refs.antVG6Ref.visibleItemById(edge.id, visible);
        }
      });
    },
    /**
     * 实体筛选
     */
    nodeFilterChange(filterLabel) {
      const nodes = this.$refs.antVG6Ref.getNodes();
      const edges = this.$refs.antVG6Ref.getEdges();
      const checkEdgeIds = this.$refs.edgeFilterRef.getFilterDataIds();
      nodes.forEach((node) => {
        const visible = filterLabel.includes(node.group);
        this.$refs.antVG6Ref.visibleItemById(node.id, visible);
      });
      if (!this.currentTab.relation)
        edges.forEach((edge) => {
          if (!checkEdgeIds.includes(edge.id)) {
            this.$refs.antVG6Ref.visibleItemById(edge.id, false);
          }
        });
    },

    // 路径推演
    async pathShowBack(param) {
      try {
        const selectedNodes = this.$refs.antVG6Ref
          .getNodesByState("selected")
          .map((node) => node.getModel());
        if (selectedNodes.length !== 2) {
          this.$Message.warning("路径推演必须选中有且只有2个实体");
          return;
        }
        this.$refs.antVG6Ref.loadingShow({ visible: true, text: "数据分析中" });
        param.sourceId = selectedNodes[0].id;
        param.sourceLabel = selectedNodes[0].label;
        param.targetId = selectedNodes[1].id;
        param.targetLabel = selectedNodes[1].label;
        const allData = this.assemblingGraphData();
        const data = await findG6AllPath(param, allData);
        if (data.nodes.length && data.edges.length) {
          this.$refs.antVG6Ref.setState(allData, "secondary");
          // data.nodes.push(selectedNodes[0]);
          this.$refs.antVG6Ref.setState(data, "analysis");
          this.$set(this.currentTab, "analysis", true);
        } else {
          this.$Message.warning("节点之间不存在有向路径");
        }
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
    },
    // 连接分析
    async connectionAnalysis(param) {
      try {
        const graphNodes = this.$refs.antVG6Ref.getNodesByState("selected");
        // const selectedNodes = graphNodes.map((node) => node.getModel());
        if (graphNodes.length !== 1) {
          this.$Message.warning("连接分析须选中1个实体");
          return;
        }
        this.$refs.antVG6Ref.loadingShow({ visible: true, text: "数据分析中" });

        const data = {
          nodes: [...graphNodes],
          edges: [],
        };
        getG6ConnectionPath(graphNodes[0], param, 0, data);
        if (data.nodes.length > 1 && data.edges.length > 0) {
          const allData = this.assemblingGraphData();
          this.$refs.antVG6Ref.setState(allData, "secondary");
          data.nodes.push(graphNodes[0]);
          this.$refs.antVG6Ref.setState(data, "analysis");
          this.$set(this.currentTab, "analysis", true);
        } else {
          this.$Message.warning("未找到节点的关系");
        }
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
    },
    // 社群分析
    socialAnalysis(type, num) {
      const { edges, nodes } = this.assemblingGraphData();
      const secondaryNode = [];
      // const secondaryEdges = [];
      nodes.forEach((node) => {
        const nodeItem = this.$refs.antVG6Ref.getNodeById(node.id);
        const neighborsNodes =
          this.$refs.antVG6Ref.findNeighborsEdges(nodeItem);
        switch (type) {
          case "=":
            if (neighborsNodes.length === num) {
              secondaryNode.push(node);
              // secondaryEdges.push(...neighborsNodes);
            }
            break;
          case ">":
            if (neighborsNodes.length > num) {
              secondaryNode.push(node);
              // secondaryEdges.push(...neighborsNodes);
            }
            break;
          case "<":
            if (neighborsNodes.length < num) {
              secondaryNode.push(node);
              // secondaryEdges.push(...neighborsNodes);
            }
            break;
          case ">=":
            if (neighborsNodes.length >= num) {
              secondaryNode.push(node);
              // secondaryEdges.push(...neighborsNodes);
            }
            break;
          case "<=":
            if (neighborsNodes.length <= num) {
              secondaryNode.push(node);
              // secondaryEdges.push(...neighborsNodes);
            }
            break;
        }
      });
      if (secondaryNode.length > 0) {
        const data = {
          nodes: secondaryNode,
          edges: [], // secondaryEdges
        };
        const secondaryNodeIds = secondaryNode.map((node) => node.id);
        this.$refs.antVG6Ref.clearStates(["selected"]);
        this.$refs.antVG6Ref.setState(
          {
            nodes: nodes.filter((node) => !secondaryNodeIds.includes(node.id)),
            edges,
          },
          "secondary"
        );
        this.$refs.antVG6Ref.setState(data, "connectionAnalysis");
        this.$set(this.currentTab, "analysis", true);
      } else {
        this.$Message.warning("未找到结果！");
      }
    },
    clear() {
      this.$refs.antVG6Ref.clearGraphData();
      // this.graphData = {
      //   nodes: [],
      //   edges: [],
      // };
    },
    canvasClick() {
      this.nodeContextmenuShow = false;
    },
    getSourceNodeData(edge, sourceNode) {
      /**
       * 区分关系视图点击连线，关系视图下连线到分组的信息需要特殊处理
       */
      if (this.currentTab.relation) {
        const nodes = this.$refs.antVG6Ref.getNodes();
        const edges = this.$refs.antVG6Ref.getEdges();
        const targetN = nodes.find((row) => row.id === edge.target);
        let sourceN = nodes.find((row) => row.id === edge.source);
        // 如果点击的target为分组的关系则不显示详情
        if (targetN.isGroup) {
          return null;
        }
        // 如果点击的source为分组的关系则展示分组的关系的sourceNode
        if (sourceN.isGroup) {
          const sourceEdge = edges.find((row) => row.target === sourceN.id);
          sourceN = nodes.find((row) => row.id === sourceEdge.source);
          return sourceN;
        } else {
          return sourceNode;
        }
      }
      return sourceNode;
    },
    edgeClick({ edge, targetNode, sourceNode }) {
      if (this.isExcavate) {
        if (edge.label == this.excavateData.edges[0].label) {
          this.relationDetailModal({ ...targetNode, edge }, true);
        }
      } else {
        this.detailObjInfo = this.getSourceNodeData(edge, sourceNode);
        this.detailObjInfo && this.relationDetailModal({ ...targetNode, edge });
      }
    },
    nodeContextmenu(node) {
      if (this.currentTab.relation) return;
      this.nodeContextmenuPosition = { x: node.clientX, y: node.clientY };
      const nodeItem = this.$refs.antVG6Ref.getNodeById(node.id);
      const locked = this.$refs.antVG6Ref.getNodeProperty(nodeItem, "locked");
      this.contextNode = { locked, ...node };
      if (!this.isExcavate) {
        this.nodeContextmenuShow = true;
      }
    },
    lock(node) {
      const nodeItem = this.$refs.antVG6Ref.getNodeById(node.id);
      const locked = this.$refs.antVG6Ref.getNodeProperty(nodeItem, "locked");
      this.$refs.antVG6Ref.lock(!locked, [nodeItem]);
    },
    contextDeleteNode(node) {
      this.$refs.antVG6Ref.setState({ nodes: [node], edges: [] }, "selected");
      this.deleteNode();
    },
    contextLianjiefenxi(node) {
      this.$refs.antVG6Ref.setState({ nodes: [node], edges: [] }, "selected");
      this.lianjiefenxi();
    },
    objectDetail(node, isExcavate = false) {
      this.isExcavate = isExcavate;
      this.detailObjInfo = node;
      this.$refs.ObjectDetailsRef.refRefrenshDetail(this.detailObjInfo);
      const nodeItem = this.$refs.antVG6Ref.getNodeById(node.id);
      const edgeList = this.$refs.antVG6Ref.getNodeEdges(nodeItem);
      const neighborsNodes = this.$refs.antVG6Ref.findNeighborsNodes(node.id);
      const relationList = [];
      edgeList.forEach((edge) => {
        const row = relationList.find((item) => item.name === edge.label);
        if (row) {
          if (edge.source !== node.id) {
            const item = neighborsNodes.find((itm) => itm.id === edge.source);
            item.show = true;
            if (!row.data.find((itm) => itm.id === item.id)) {
              row.data.push({ ...item, edge });
            }
          }
          if (edge.target !== node.id) {
            const item = neighborsNodes.find((itm) => itm.id === edge.target);
            item.show = true;
            if (!row.data.find((itm) => itm.id === item.id)) {
              row.data.push({ ...item, edge });
            }
          }
        } else {
          const obj = { name: edge.label, data: [] };
          if (edge.source !== node.id) {
            const item = neighborsNodes.find((itm) => itm.id === edge.source);
            item.show = true;
            obj.data.push({ ...item, edge });
          }
          if (edge.target !== node.id) {
            const item = neighborsNodes.find((itm) => itm.id === edge.target);
            item.show = true;
            obj.data.push({ ...item, edge });
          }
          relationList.push(obj);
        }
      });

      this.detailRelationList = relationList;
      if (this.isExcavate) {
        this.detailRelationList = relationList.filter(
          (item) => item.name == this.excavateData.edges[0].label
        );
      }
      this.objectDetailsVisble = true;
    },
    /**
     * 关系详情
     */
    relationDetailModal(row, isExcavating = false) {
      this.toNode = row;
      this.relationshipInfoVisble = true;
      this.$nextTick(() => {
        this.$refs.relationInfoRef.init(isExcavating);
      });
    },
    // 查询所有字段属性列表，对象详情页面用
    async getPropertyList() {
      try {
        const param = {
          notSearchTotal: false,
          // graphId: this.relationObj.graphInfo.graphId,
          graphIdList: this.relationObj.graphInfo?.map((item) => item.graphId),
          pageSize: 100,
        };
        const res = await propertyList(param);
        this.propertyList = res.data.entities;
      } catch (err) {
        console.error(err, "err");
      }
    },
    async getRelationships(node, level) {
      try {
        const graphInstanceId = node.ext.metadata.qsdi_graph_instance_id;
        const params = {
          entityIds: [node.id],
          maxDepth: level,
          originalVertex: null,
          graphInstanceId,
        };
        /**
         * 如果是融合图谱类型拓展关系需要额外参数
         */
        if (this.relationObj.graphInfo.graphBusinessType === "01") {
          params.originalVertex = {
            entityId: node.id,
            graphInstanceId,
          };
        }
        const { nodes, edges } = await this.getGraphDataByEntiy(params);
        this.addSubgraphLayout(node, { nodes, edges }, level);
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
    },
    addSubgraphLayout(node, { nodes, edges }, level) {
      this.$refs.antVG6Ref.processParallelEdges(edges);
      const newData = this.$refs.antVG6Ref.getAddNodes({ nodes, edges });
      if (newData.nodes.length || newData.edges.length) {
        // 把节点本身加入需要子图布局的节点中

        this.drawSubgraphLayout(newData, node, level);
        this.updateGroup();
        this.updateTab();
      }
    },
    drawSubgraphLayout(newData, node, level) {
      newData.nodes.push(node);
      this.$refs.antVG6Ref.subgraphLayout(newData, {
        layoutName: "radial",
        node,
        level,
      });
    },
    async queryConsanguinitySearchData(node, type) {
      try {
        if (!this.relationObj.fusionGraphInfo.instanceId) {
          return this.$Message.warning(
            "请先去系统配置页面配置数字立方的融合图谱"
          );
        }
        const level = 1;
        const res = await consanguinitySearch({
          fusionInstanceId: this.relationObj.fusionGraphInfo.instanceId,
          maxDepth: level,
          vertexEx: node.ext,
        });
        if (!res.data) return this.$Message.warning("未查找到关系");
        const { nodes = [], edges = [] } = this.assemblyDataByBackend(res);
        if (nodes.length || edges.length) {
          if (type === "fusion") {
            this.addSubgraphLayout(node, { nodes, edges }, level);
          } else {
            const newData = this.$refs.antVG6Ref.getAddNodes({
              nodes: [],
              edges,
            });
            this.$refs.antVG6Ref.processParallelEdges(newData.edges);
            this.$refs.antVG6Ref.addNodes(newData);
            this.$nextTick(() => {
              this.$refs.antVG6Ref.setState(
                { edges, nodes: [...nodes, node] },
                "selected"
              );
              this.updateTab();
              this.updateGroup();
            });
          }
        } else {
          this.$Message.warning("未查找到关系");
        }
      } catch (err) {
        console.error(err, "err");
      }
    },
    // 同源查找
    consanguinitySearch(node) {
      this.queryConsanguinitySearchData(node, "consanguinity");
    },
    // 融合查找
    fusionSearch(node) {
      this.queryConsanguinitySearchData(node, "fusion");
    },
    async relationalViews(node) {
      try {
        this.currentTab.context = this.assemblingGraphData();
        this.$set(this.currentTab, "relation", true);
        this.clear();
        const graphInstanceId = node.ext.metadata.qsdi_graph_instance_id;
        const params = {
          entityId: node.ext.entityId,
          vertexEx: node.ext,
          maxDepth: 1,
          originalVertex: null,
          graphInstanceId,
        };
        /**
         * 如果是融合图谱类型关系视图需要额外参数
         */
        if (this.relationObj.graphInfo.graphBusinessType === "01") {
          params.originalVertex = {
            entityId: node.ext.entityId,
            graphInstanceId,
          };
        }
        this.$refs.antVG6Ref.loadingShow({ visible: true, text: "获取数据中" });
        const res = await relationView(params);
        const data = this.dealRelationData(res, [params.entityId], false);

        this.graphData = {
          nodes: [...data.nodes, ...data.groups],
          edges: data.edges,
        };
        this.$nextTick(() => {
          data.nodes.forEach((row) => {
            if (row.id !== node.id) {
              this.$refs.antVG6Ref.visibleItemById(row.id, false);
            }
          });
          this.$refs.antVG6Ref.layoutSetting.updateLayout("force");
          this.updateGroup();
          this.updateTab();
        });
      } catch (err) {
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
    },
    dealRelationData(res, entityIds = [], isRelation) {
      const groups = res.data.entityGroups.map((row) => {
        return {
          id: row.id,
          label: row.labelCn,
          group: row.label,
          groupCn: row.labelCn,
          isGroup: true,
          isExpand: isRelation,
          firstRender: true,
          ext: row,
        };
      });
      const nodes = res.data.entitys.map((row) => {
        return {
          id: row.entityId,
          label: row.displayField,
          img: row.propertyIcon || row.icon,
          group: row.label,
          groupCn: row.labelCn,
          ext: row,
        };
      });
      const edges = res.data.relations.map((row) => {
        return {
          source: row.sourceId,
          target: row.targetId,
          label: row.properties ? (row.items ? `${row.items}次` : "") : null,
          ext: row,
        };
      });
      this.relationViewData = {
        groups,
        nodes,
        edges,
      };
      return {
        groups: groups,
        nodes: nodes.filter((item) => entityIds.includes(item.id)),
        edges: edges.filter(
          (item) =>
            entityIds.includes(item.source) || entityIds.includes(item.target)
        ),
      };
    },
    getGroupExpandData(groupNode) {
      const edges = this.relationViewData.edges.filter((edge) =>
        [edge.source, edge.target].includes(groupNode.id)
      );
      const nodes = this.relationViewData.nodes.filter((node) =>
        edges.some((edge) => [edge.source, edge.target].includes(node.id))
      );
      const newData = this.$refs.antVG6Ref.getAddNodes({
        nodes,
        edges,
      });
      this.$refs.antVG6Ref.addNodes(newData);
      this.$refs.antVG6Ref.layoutSetting.updateLayout("force");
    },
    expandGroup(node) {
      if (node.firstRender) {
        this.$refs.antVG6Ref.updateItemById(node.id, {
          isExpand: !node.isExpand,
          firstRender: false,
        });
        return this.getGroupExpandData(node);
      }
      const nodes = this.$refs.antVG6Ref.findNeighborsNodes(node.id, "target");
      nodes.forEach((row) => {
        this.$refs.antVG6Ref.visibleItemById(row.id, !node.isExpand);
      });
      this.$refs.antVG6Ref.updateItemById(node.id, {
        isExpand: !node.isExpand,
      });
    },
    technicalTactics(node, name) {
      let id = null;
      let type = null;
      switch (node.ext.label) {
        case "real_name_archive":
          id = node.ext.properties.archiveNo;
          type = node.ext.label;
          break;
        case "vehicle_archive":
          id = node.ext.properties.plateNo;
          type = node.ext.label;
          break;
      }
      this.$router.push({
        name,
        query: {
          id,
          type,
        },
      });
    },
    changeZoom(zoom) {
      this.$refs.antVG6Ref.zoomTo(zoom / 100);
    },
    wheelzoom(zoom) {
      this.$refs.toolbarRightRef.zoom = zoom.toFixed(1) * 100;
    },
    fitView() {
      this.$refs.antVG6Ref.fitView();
    },
    nodeDblclick(node) {
      if (this.currentTab.relation || this.isExcavate) return;
      this.getRelationships(node, 1);
    },
    //操作栏关系挖掘点击
    async guanxiwajue(clearData) {
      if (this.isExcavate) {
        if (clearData) {
          this.guanxiwajueData = {
            relationList: [],
          };
          return;
        }
        this.$Message.warning("正在进行关系挖掘！");
      } else {
        this.guanxiwajueData = {
          relationList: [],
        };
        if (!clearData) {
          let selectedNodes = this.$refs.antVG6Ref.getNodesByState("selected");
          if (selectedNodes && selectedNodes.length === 1) {
            let nodeObj = selectedNodes[0]._cfg.model;
            nodeObj.operate = "operate";
            let data = await this.$refs.nodeContextMenuRef.initExcavate(
              nodeObj
            );
            if (data) {
              this.guanxiwajueData = data;
            } else {
              this.$Message.warning("当前实体暂不支持关系挖掘！");
            }
          } else {
            this.$Message.warning("关系挖掘必须选中有且只有1个实体");
          }
        }
      }
    },
    //操作栏关系挖掘删除已有关系
    delRelationExcavate(item) {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定删除吗？`,
        onOk: () => {
          this.$refs.antVG6Ref.loadingShow({
            visible: true,
            text: "关系模型删除中",
          });
          deleteMiningModel(item.relationMiningModel.id)
            .then((res) => {
              this.guanxiwajue();
              this.delExcavateRelation(item.relationName);
            })
            .finally(() => {
              this.$refs.antVG6Ref.loadingShow({ visible: false, text: "" });
            });
        },
      });
    },
    // 删除挖掘关系
    delExcavateRelation(relationName) {
      const edges = this.$refs.antVG6Ref
        .getEdges()
        .filter((row) => row.ext.detail.label === relationName);
      this.$refs.antVG6Ref.deleteItemsById({ nodes: [], edges });
      this.updateTab();
      this.updateGroup();
    },
    //查看规则详情
    viewRelationExcavate(item) {
      this.$refs.relationMiningModelRef.init(item);
    },
    //实体右键菜单关系挖掘
    relationExcavate(node, name, excavateNodeConfigData, item) {
      //   const selectedNodes = this.$refs.antVG6Ref.getNodesByState("selected");
      //   if (selectedNodes && selectedNodes.length === 1) {
      if (name == "relation-excavate-add") {
        this.excavateNode = node;
        this.excavateNodeConfigData = excavateNodeConfigData;
        this.$refs.relationExcavateAddRef.init(node, excavateNodeConfigData);
        this.excavateType = "excavateAdd";
      } else {
        //   console.log("已有关系挖掘");
        //   console.log(item, "item");
        this.excavateType = "";
        let execParams = {
          relationMiningModelId: item.relationMiningModel.id,
          sourceEntityId: node.id,
        };
        // this.excavateLoading = true;
        this.$refs.antVG6Ref.loadingShow({
          visible: true,
          text: "数据挖掘分析中",
        });
        exec(execParams)
          .then((res) => {
            if (res.code == 200) {
              let graphData = {
                nodes: [node],
                edges: [
                  {
                    source: node.id,
                    id: item.relationId,
                    label: item.label,
                    style: JSON.parse(item.relationMiningModel.lineStyle),
                    labelCfg: {
                      refY: 5,
                      autoRotate: true,
                      style: {
                        fill: "#2C86F8",
                        fontSize: 14,
                        cursor: "pointer",
                      },
                    },
                    ext: item,
                  },
                ],
              };
              if (res.data.vertexExes.length > 0) {
                this.getRelationExcavateData(
                  res.data,
                  graphData,
                  item.relationMiningModel.id
                );
              } else {
                this.$Message.info("该对象暂未挖掘到数据！");
              }
            }
          })
          .finally(() => {
            // this.excavateLoading = false;
            this.$refs.antVG6Ref.loadingShow({ visible: false, text: " " });
          });
      }
      //   } else {
      //     this.$Message.warning("关系挖掘必须选中有且只有1个实体");
      //   }
    },
    //获取关系挖掘数据
    getRelationExcavateData(resData, graphData, relationMiningModelId) {
      //   console.log(resData, "关系挖掘数据");
      //   console.log(graphData, "关系挖掘步骤四图谱数据");
      this.relationMiningModelId = relationMiningModelId;
      this.relationMiningExecLogId = resData.relationMiningExecLogId;
      this.excavateResData = resData;
      this.isExcavate = true;
      const nodes = resData.vertexExes.map((row) => {
        return {
          id: row.entityId,
          label: row.displayField || row.properties.archive_no,
          img: row.propertyIcon || row.icon || row.properties.photo_url,
          group: row.label,
          groupCn: row.labelCn,
          ext: row,
        };
      });
      const edges = resData.vertexExes.map((row) => {
        return {
          id: Math.random().toString(16).substring(2),
          source: graphData.edges[0].source,
          target: row.entityId,
          label: graphData.edges[0].label,
          style: graphData.edges[0].style,
          labelCfg: graphData.edges[0].labelCfg,
          ext: row,
          isExcavate: true,
        };
      });
      this.excavateData = { nodes, edges };
      graphData.nodes[0].isExcavate = true;

      if (this.excavateData.nodes.length || this.excavateData.edges.length) {
        this.clearExcavateResultDataForGraph();
        this.$refs.antVG6Ref.processParallelEdges(this.excavateData.edges);
        const newData = this.$refs.antVG6Ref.getAddNodes({
          nodes: this.excavateData.nodes,
          edges: [],
        });
        this.excavateResultData = {
          nodes: deepCopy(newData.nodes),
          edges: deepCopy(this.excavateData.edges),
        };
        this.drawSubgraphLayout(
          {
            nodes: newData.nodes,
            edges: this.excavateData.edges,
          },
          graphData.nodes[0],
          1
        );
        this.$nextTick(() => {
          this.excavateHighLight(
            [graphData.nodes[0], ...nodes],
            this.excavateData.edges
          );
        });
        // this.$refs.antVG6Ref.addNodes({
        //   nodes: newData.nodes,
        //   edges: this.excavateData.edges,
        // });
        // this.$refs.antVG6Ref.changeData({
        //   nodes: this.excavateData.nodes,
        //   edges: this.excavateData.edges,
        // });
        // this.$refs.antVG6Ref.layoutSetting.updateLayout("avoidlap");
        // this.updateGroup();
        // this.updateTab();
      } else {
        this.excavateHighLight([graphData.nodes[0]]);
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
      this.objectDetail(graphData.nodes[0], true);
    },
    // getSubgraphLayoutLevel(leg) {
    //   if (leg < 30) return 1;
    //   else if (leg < 80) return 2;
    //   else return 3;
    // },
    excavateHighLight(nodeDatas = [], edgeDatas = []) {
      const nodes = this.$refs.antVG6Ref.getNodes((node) => {
        if (nodeDatas.some((el) => el.id === node.get("id"))) node.toFront();
      });
      const edges = this.$refs.antVG6Ref.getEdges();
      let newNodes = nodes.filter((v) =>
        nodeDatas.every((val) => val.id !== v.id)
      );
      let newEdges = edges.filter((v) =>
        edgeDatas.every((val) => val.id !== v.id)
      );
      this.$refs.antVG6Ref.clearStates(["analysis", "secondary", "selected"]);
      this.$refs.antVG6Ref.setState(
        {
          nodes: newNodes,
          edges: newEdges,
        },
        "secondary"
      );
      this.$refs.antVG6Ref.setState(
        {
          nodes: nodeDatas,
          edges: [],
        },
        "analysis"
      );
      this.$refs.antVG6Ref.graph.getNodes().forEach((node) => {
        const model = node.get("model");
        return {
          ...model,
          visible: node.get("visible"),
        };
      });
      // edges.forEach((item) => {
      //   this.$refs.antVG6Ref.updateItemById(item.id, {
      //     labelCfg: {
      //       style: {
      //         opacity: 0.2,
      //       },
      //     },
      //   });
      // });
    },
    // 挖掘对象详情显示隐藏
    excavateShow(selectArr) {
      this.excavateSelectArr = selectArr;
      selectArr.forEach((item) => {
        this.$refs.antVG6Ref.visibleItemById(item.id, item.isCheck);
      });
    },
    //挖掘返回
    excavateBack() {
      this.objectDetailsVisble = false;
      this.$refs.relationExcavateAddRef.init(
        this.excavateNode,
        this.excavateNodeConfigData,
        "excavating"
      );
    },
    //挖掘关闭
    async excavateClose(isUpdateGraph) {
      this.excavateSelectArr = [];
      this.isExcavate = false;
      this.objectDetailsVisble = false;
      this.$refs.relationExcavateAddRef.clearModelData();
      this.$refs.antVG6Ref.clearStates(["analysis", "secondary"]);
      // 没有保存关系数。关闭之后清除挖掘的关系和实体
      if (isUpdateGraph) {
        if (this.routerQuery.id) {
          const { context, currentViewFlag, relationViewContent, layoutName } =
            await this.getGraphDataById(this.routerQuery.id);
          layoutName && (this.layoutName = layoutName);
          const data = {
            nodes:
              currentViewFlag === 2 ? relationViewContent.nodes : context.nodes,
            edges:
              currentViewFlag === 2 ? relationViewContent.edges : context.edges,
          };
          // data.edges.forEach((item) => {
          //   this.$refs.antVG6Ref.updateItemById(item.id, {
          //     labelCfg: {
          //       style: {
          //         opacity: 1,
          //       },
          //     },
          //   });
          // });
          this.$refs.antVG6Ref.changeData(data);
        } else {
          const params = {
            entityIdInstanceIdMap: JSON.parse(
              this.routerQuery.entityIdInstanceIdMap || "{}"
            ),
            entityIds: JSON.parse(this.routerQuery.ids).split(","),
            maxDepth: this.routerQuery.maxDepth,
          };
          if (this.routerQuery.isRelation) {
            params.graphInstanceId = this.archiveGraphInfo.instanceId;
          }
          const data = await this.getGraphDataByEntiy(params);
          this.$refs.antVG6Ref.changeData(data);
          this.updateGroup();
          this.updateTab();
        }
      } else this.clearExcavateResultDataForGraph();
      this.excavateResultData = null;
      this.guanxiwajueData = {};
    },
    clearExcavateResultDataForGraph() {
      this.$refs.antVG6Ref.deleteItemsById(this.excavateResultData || {});
      this.excavateResultData = null;
    },
    //挖掘保存
    excavateSave(val) {
      let params = {
        addRelationTargetEntityIds: [],
        relationMiningModelId: this.relationMiningModelId,
        relationMiningExecLogId: this.relationMiningExecLogId,
        removeRelationTargetEntityIds: [],
        saveRelationData: true,
        saveRelationSchema: true,
      };
      this.$refs.relationExcavateAddRef.clearModelData();
      //挖掘关系选择 默认全选
      if (this.excavateSelectArr.length > 0) {
        this.excavateSelectArr.forEach((item) => {
          if (item.isCheck) {
            params.addRelationTargetEntityIds.push(item.id);
          } else {
            params.removeRelationTargetEntityIds.push(item.id);
          }
        });
      } else {
        this.excavateData.nodes.forEach((item) => {
          params.addRelationTargetEntityIds.push(item.id);
          params.removeRelationTargetEntityIds = [];
        });
      }

      if (val == "relation") {
        params.saveRelationData = false;
        params.saveRelationSchema = true;
      } else if (val == "relationAndResult") {
        params.saveRelationData = true;
        params.saveRelationSchema = true;
      } else {
        params.saveRelationData = true;
        params.saveRelationSchema = false;
      }
      this.saveExcavateLoading = true;
      this.$refs.antVG6Ref.loadingShow({ visible: true, text: "挖掘保存中" });
      saveMiningRelation(params)
        .then((res) => {
          if (res.code == 200) {
            if (val == "relation") {
              this.$Message.success("保存关系成功");
              this.excavateType = "";
              this.$refs.antVG6Ref.loadingShow({ visible: false, text: " " });
            } else {
              this.$Message.success("保存成功");
              this.excavateClose(true);
            }
            this.guanxiwajueData = {};
          }
        })
        .catch(() => {
          this.$refs.antVG6Ref.loadingShow({ visible: false, text: " " });
        })
        .finally(() => {
          this.saveExcavateLoading = false;
        });
    },
    // 小智搜索
    async fromGptSearch() {
      const { type, picSerch, info } = this.numCodeParams;
      const algorithmTypeMap = {
        vid: "1",
        person: "1",
        vehicle: "2",
      };
      if (info) {
        const { relationNames, feature, keyword } = info;
        let data = {};
        if (picSerch) {
          data = {
            algorithmType: algorithmTypeMap[type],
            features: [feature],
            similarity: 0.8,
            maxDepth: 1,
          };
        } else {
          // 关键字检索
          data = {
            searchValue: keyword,
            maxDepth: 1,
          };
        }
        const entityData  = await this.getEntityList(data);
        const ids = [];
        const entityIdInstanceIdMap = {};
        entityData.forEach((item) => {
          ids.push(item.id);
          entityIdInstanceIdMap[item.id] = item.graphInstanceId;
        });
        let param = {
          ids: JSON.stringify(ids.join(",")),
          entityIdInstanceIdMap: JSON.stringify(entityIdInstanceIdMap),
          maxDepth: 1,
          relationNames: JSON.stringify(relationNames),
          type: "add",
        };
        this.getGraphData(false, param);
      }
    },
  },
  watch: {
    $route: {
      handler(val) {
        if (val.name === "number-cube-info" && val.query) {
          this.getGraphData();
        }
      },
      // immediate: true,
    },
    relationObj: {
      handler(val) {
        if (val.graphInfo) {
          this.getPropertyList();
        }
      },
      immediate: true,
    },
    "currentTab.relation"() {
      this.relationFilterShow = false;
      this.nodeFilterShow = false;
    },
    numCodeParams: {
      handler(val) {
        this.fromGptSearch();
      },
    },
  },
  computed: {
    ...mapGetters({
      routeFlag: "number-cube/getRouteFlag",
      tabsList: "number-cube/getTabsList",
      tabFlag: "number-cube/getTabsFlag",
      relationObj: "systemParam/relationObj",
      archiveObj: "systemParam/archiveObj",
      xzLLMObj: "systemParam/getXzLLMObj",
      relationQueryObj: "systemParam/getRelationQueryObj",// 关系是否聚合
      numCodeParams: "gpt/getNumCodeParams", // 小智数智立方
    }),
    archiveGraphInfo() {
      let { source } = this.$route.query;
      if (source === "car") {
        return this.archiveObj.vehicleGraphInfo;
      } else if (source === "video") {
        return this.archiveObj.videoGraphInfo;
      } else if (source === "place") {
        return this.archiveObj.placeGraphInfo;
      } else if (source === "people") {
        return this.archiveObj.realNameGraphInfo;
      } else {
        return this.archiveObj.deviceGraphInfo;
      }
    },
    pathShowObj() {
      return {
        entityList: this.nodesGroupList,
        relationList: this.edgesGroupList,
      };
    },
    statistic() {
      let nodesLength = 0;
      let edgesLength = 0;
      this.nodesGroupList.forEach((row) => {
        nodesLength += row.num;
      });
      this.edgesGroupList.forEach((row) => {
        edgesLength += row.num;
      });
      return {
        nodesLength,
        edgesLength,
      };
    },
  },
  components: {
    cubeTabs: require("./components/tabs.vue").default,
    operate: require("./component/operate.vue").default,
    SearchResultInput: require("./components/search-result-input.vue").default,
    RelationGraphSave: require("./components/relation-graph-save.vue").default,
    dataFilter: require("./component/data-filter.vue").default, // 关系、实体筛选
    PathDeductionModal: require("./components/path-deduction-modal.vue")
      .default,
    RelationGraphLianjiefenxi:
      require("./components/relation-graph-lianjiefenxi.vue").default,
    CommunityAnalysisModal: require("./components/community-analysis-modal.vue")
      .default,
    ObjectDetails: require("./component/object-details").default, // 对象详情
    NodeContextMenu: require("./components/node-context-menu.vue").default,
    ToolbarRight: require("./components/toolbar-right.vue").default,
    RelationshipInfo: require("./component/relationship-info").default, // 关系详情
    relationExcavateAddModel:
      require("./components/relation-excavate/relationExcavateAdd.vue").default, // 关系挖掘
    relationMiningModel:
      require("./components/relation-excavate/relationMiningModel.vue").default, // 关系挖掘配置详情
    AntVG6,
  },
  beforeDestroy() {
    this.setTabsList([]);
    this.$eventBus.$off("getSaveGraphData",this.getSaveGraphData)
  },
};
</script>
<style lang="less" scoped>
.number-cube-info {
  position: relative;
  display: flex;
  flex-direction: column;
  background: #fff;
  width: 100%;
  box-shadow: 0 3px 5px 0 rgba(147, 171, 206, 0.7);
  border-radius: 4px;

  .antv-g6 {
    flex: 1;
    background-color: #fff;
    overflow: hidden;
  }

  .footer-statistics {
    position: fixed;
    bottom: 30px;
    left: 30px;
  }

  .footer-tips {
    position: fixed;
    bottom: 20px;
    z-index: 12;
    left: 42%;
  }

  .line-color {
    color: #2c86f8;
  }

  .tool-close {
    height: 0;
    overflow: hidden;
    padding: 0;
  }

  .rotate {
    transform: rotate(180deg);
  }

  .switch {
    width: 20px;
    position: absolute;
    right: 20px;
    top: 52px;
    z-index: 10;
    cursor: pointer;
  }

  .full {
    position: absolute;
    right: 60px;
    top: 53px;
    cursor: pointer;
  }

  .data-filter {
    position: absolute;
    top: 88px;
    width: 246px;
    left: 22px;
    z-index: 2;
    height: 80%;
    display: flex;
    flex-direction: column;
    .filter-conent {
      flex: 1;
    }
    .node-filter {
      margin-top: 12px;
    }
  }
}
</style>
