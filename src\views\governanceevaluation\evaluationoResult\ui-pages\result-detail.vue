<template>
  <div class="result-detail auto-fill">
    <div class="info-statics mt-sm">
      <index-statistics></index-statistics>
    </div>
    <div class="abnormal-title">
      <div>
        <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
        <span class="f-16 color-filter ml-sm">检测结果详情</span>
      </div>
      <div class="export">
        <slot name="export"></slot>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :loading="loading"
      :table-columns="tableColumns"
      :table-data="tableData"
      ref="table"
    >
      <template v-for="(item, index) in tableColumns" :slot="item.slot" slot-scope="{ row }">
        <slot :name="item.slot" :row="row" :index="index"></slot>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
  </div>
</template>

<script>
export default {
  name: 'result-detail',
  props: {
    statisticsList: {
      type: Array,
      default: () => [],
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    resultData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
    };
  },
  mounted() {
    this.search();
  },
  methods: {
    search() {
      this.loading = true;
      this.$emit('startSearch', this.pageData);
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.$emit('startSearch', this.pageData);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.$emit('startSearch', this.pageData);
    },
  },
  watch: {
    resultData: {
      handler(val) {
        this.tableData = val.entities;
        this.pageData.totalCount = val.total;
        this.loading = false;
      },
      immediate: true,
    },
  },
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.result-detail {
  .info-statics {
    width: 100%;
    height: 102px;
    @{_deep} .statistics-ul {
      li {
        flex: 1;
      }
    }
  }
  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
}
</style>
