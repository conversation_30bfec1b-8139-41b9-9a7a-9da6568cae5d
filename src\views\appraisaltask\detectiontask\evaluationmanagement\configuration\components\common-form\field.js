export default {
  data() {
    return {
      tableColumns: [
        {
          type: 'selection',
          align: 'center',
          width: 50,
        },
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
        {
          title: '行政区划',
          key: 'regionName',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '场所名称',
          key: 'placeName',
          width: 100,
        },
        {
          title: '地点俗称',
          key: 'placeAlias',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '地名编码',
          key: 'placeInternalCode',
          ellipsis: true,
          tooltip: true,
        },
      ],
      wayList: [
        {
          label: '全检',
          value: '1',
          type: 'all',
        },
        {
          label: '抽检',
          value: '2',
          type: 'spot',
        },
        {
          label: '自定义',
          value: '3',
          type: 'custom',
        },
      ],
      mateDeviceList: [
        {
          label: '全量设备',
          value: '0',
        },
        {
          label: '检测合格设备',
          value: '1',
        },
      ],
      statisticRange: [
        {
          label: '按月统计',
          value: '1',
        },
        // { label: '按天统计', value: '2' }, 这个版本先不做
      ],
      algorithmManufacturer: [
        'VEHICLE_INFO_PASS',
        'VEHICLE_MAIN_PROP',
        'VEHICLE_TYPE_PROP',
        'FOCUS_TRACK',
        'FACE_CAPTURE_PASS',
        'ARCHIVES_PORTRAIT_ACCURACY',
        'ARCHIVES_VEHICLE_ACCURACY',
        'VEHICLE_INFO_PASS_IMPORTANT',
        'FACE_CAPTURE_SCORE',
      ], // 选择算法厂商
      upStandardQuantity: [
        'VIDEO_MONITOR_VALID_SUBMIT_QUANTITY_RATE',
        'FACE_QUANTITY_STANDARD',
        'VEHICLE_QUANTITY_STANDARD',
        'VIDEO_QUANTITY_STANDARD',
      ], // 达标数量设置
      unSpotCheck: [
        'FACE_VALID_SUBMIT_QUANTITY',
        'VEHICLE_VALID_SUBMIT_QUANTITY',
        'VEHICLE_ONLINE_RATE_ADVANCE',
        'FACE_ONLINE_RATE_ADVANCE',
        'FACE_ACCURACY',
        'VEHICLE_ACCURACY',
        'VIDEO_ACCURACY',
        'VIDEO_VALID_SUBMIT_QUANTITY',
        'VIDEO_MONITOR_ASSET_MATCH_RATE_SICHUAN',
        'FACE_PLATFORM_ONLINE_RATE',
        'VEHICLE_PLATFORM_ONLINE_RATE',
        'FACE_EMPHASIS_LOCATION',
        'VEHICLE_EMPHASIS_LOCATION',
        'VIDEO_EMPHASIS_LOCATION',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
        'FACE_CATALOGUE_SAME',
        'FACE_CAPTURE_DATA_CONSISTENCY',
        'VEHICLE_CATALOGUE_SAME',
        'VEHICLE_CAPTURE_DATA_CONSISTENCY',
        'VIDEO_QUANTITY_STANDARD',
        'VEHICLE_QUANTITY_STANDARD',
        'FACE_QUANTITY_STANDARD',
        'FACE_ASSET_REGISTER',
        'VEHICLE_ASSET_REGISTER',
        'VIDEO_QUANTITY_INCREASE_RATE',
        'VIDEO_DEVICE_REVOCATION',
        'VIDEO_NETWORKING_PROMOTION_RATE',
        'FOCUS_TRACK_REAL',
        'FOCUS_REPEAT_RATE',
        'FOCUS_DEVICE_RELATED_RATE',
      ], //不需要抽检的指标
      unAllCheck: [
        'FACE_PLATFORM_ONLINE_RATE',
        'VEHICLE_PLATFORM_ONLINE_RATE',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ], // 不需要全检的指标
      unCustom: [
        'VIDEO_MONITOR_ASSET_MATCH_RATE_SICHUAN',
        'FACE_CATALOGUE_SAME',
        'FACE_CAPTURE_DATA_CONSISTENCY',
        'VEHICLE_CATALOGUE_SAME',
        'VEHICLE_CAPTURE_DATA_CONSISTENCY',
        // 'FOCUS_TRACK',
        // 'FOCUS_TRACK_URL_AVAILABLE',
        'FACE_ASSET_REGISTER',
        'VEHICLE_ASSET_REGISTER',
        'VIDEO_QUANTITY_INCREASE_RATE',
        'VIDEO_DEVICE_REVOCATION',
        'VIDEO_NETWORKING_PROMOTION_RATE',
      ], // 不需要自定义的指标
      disabledWay: [
        'FACE_PLATFORM_ONLINE_RATE',
        'VEHICLE_PLATFORM_ONLINE_RATE',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
        'VIDEO_MONITOR_ASSET_MATCH_RATE_SICHUAN',
        'FACE_CATALOGUE_SAME',
        'FACE_CAPTURE_DATA_CONSISTENCY',
        'VEHICLE_CATALOGUE_SAME',
        'VEHICLE_CAPTURE_DATA_CONSISTENCY',
        'FOCUS_ACCURACY',
        // 'FOCUS_TRACK_REAL',
        'FOCUS_POLY_USABLE',
        // 'FOCUS_DEVICE_RELATED_RATE',
        // 'FOCUS_REPEAT_RATE',
        'VIDEO_DEVICE_REVOCATION',
      ], // 需要禁掉的检测方式
      assessmentList: [
        {
          indexType: 'FACE_QUANTITY_STANDARD',
          regionSelectSyles: {
            width: '5.8rem',
          },
          data: [
            {
              title: '人脸卡口数量',
              key: 'rlkkValue',
              optionLabel: '人脸卡口',
            },
          ],
        },
        {
          indexType: 'VEHICLE_QUANTITY_STANDARD',
          regionSelectSyles: {
            width: '5.8rem',
          },
          data: [
            {
              title: '车辆卡口数量',
              key: 'clkkValue',
              optionLabel: '车辆卡口',
            },
          ],
        },
        {
          indexType: 'VIDEO_QUANTITY_STANDARD',
          regionSelectSyles: {
            width: '5.8rem',
          },
          data: [
            {
              title: '视频监控数量',
              key: 'sxjValue',
              optionLabel: '视频监控',
            },
          ],
        },
        {
          indexType: 'VIDEO_MONITOR_VALID_SUBMIT_QUANTITY_RATE',
          regionSelectSyles: {
            width: '7.6rem',
          },
          data: [
            {
              title: '视频监控数量',
              key: 'sxjValue',
              optionLabel: '视频监控',
            },
            {
              title: '人脸卡口数量',
              key: 'rlkkValue',
              optionLabel: '人脸卡口',
            },
            {
              title: '车辆卡口数量',
              key: 'clkkValue',
              optionLabel: '车辆卡口',
            },
          ],
        },
      ],
      unDeviceNum: ['FOCUS_TRACK', 'FOCUS_TRACK_URL_AVAILABLE'], // 不需要每对象抽取设备数量的指标
      needCaptureNum: ['FOCUS_TRACK', 'FOCUS_TRACK_URL_AVAILABLE'], // 需要每对象抽取图片数量的指标
      needStatisticMode: [
        'VIDEO_GENERAL_PLAYING_ACCURACY', //重点、普通实时视频课调阅率
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY', //重点、普通历史视频可调阅率
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_HISTORY_COMPLETE_ACCURACY', //重点、普通历史录像完整率
        'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN',
        'VIDEO_QUALITY_PASS_RATE', //视频流质量合格率
        'VIDEO_QUALITY_PASS_RATE_RECHECK',
        'VIDEO_GENERAL_OSD_ACCURACY', //重点/普通字幕标注合规率
        'VIDEO_OSD_ACCURACY',
        'VIDEO_CLOCK_ACCURACY', //重点/普通时钟准确率
        'VIDEO_GENERAL_CLOCK_ACCURACY',
        'VIDEO_OSD_CLOCK_ACCURACY', //字幕标注合规性与时钟准确性
        'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
        'VIDEO_CODE_STANDARD_RATE', //视频流编码规范率
        'VIDEO_OFFLINE_STAT', //实时视频离线统计
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN', // 重点指挥图像在线率
        // 高优先级指标2
        'FACE_ONLINE_RATE',
        'VEHICLE_ONLINE_RATE',
        'VEHICLE_DEVICE_CONNECT_INTERNET', //联网率
        'FACE_DEVICE_CONNECT_INTERNET',
        'FACE_URL_AVAILABLE', //重点）人脸/车辆卡口设备图片地址可用率
        'FACE_FOCUS_URL_AVAILABLE',
        'VEHICLE_URL_AVAILABLE',
        'VEHICLE_URL_AVAILABLE_IMPORTANT',
        'VEHICLE_URL_AVAILABLE_RECHECK',
        'FACE_UPLOAD', //（重点）人脸/车辆卡口设备及时上传率
        'FACE_FOCUS_UPLOAD',
        'VEHICLE_UPLOAD',
        'VEHICLE_UPLOAD_IMPORTANT',
        'FACE_CLOCK', //人脸/车辆卡口设备时钟准确率
        'VEHICLE_CLOCK',
        'VEHICLE_FULL_INFO', //（重点）车辆卡口设备抓拍数据完整率
        'VEHICLE_FULL_INFO_IMPORTANT',
        'VEHICLE_INFO_PASS', //（重点）车辆卡口设备过车数据准确率
        'VEHICLE_INFO_PASS_IMPORTANT',
        'VEHICLE_MAIN_PROP', //重点车辆卡口设备主要属性
        'VEHICLE_TYPE_PROP', //重点车辆卡口设备类型属性识别准确率
        'FACE_OFFLINE_STAT', //人脸/车辆卡口离线统计
        'VEHICLE_OFFLINE_STAT',
        'BASIC_ACCURACY',
        'VIDEO_ACCURACY',
        'VEHICLE_ACCURACY',
        'FACE_ACCURACY',
        'BODY_ONLINE_RATE', // 人体卡口设备在线率
        'BODY_ACTIVE',
        'BODY_CLOCK',
        'BODY_UPLOAD',
      ],
      unNeedChooseDevice: [
        'FACE_PLATFORM_ONLINE_RATE',
        'VEHICLE_PLATFORM_ONLINE_RATE',
        'VIDEO_PLATFORM_ONLINE_RATE',
        'FOCUS_REPEAT_RATE',
        'FOCUS_TRACK',
        'FOCUS_TRACK_REAL',
        'FOCUS_TRACK_URL_AVAILABLE',
        'FOCUS_DEVICE_RELATED_RATE',
      ],
    };
  },
};
