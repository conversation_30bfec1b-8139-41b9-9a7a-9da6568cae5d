<template>
  <div class="work-order-container">
    <ui-tabs-wrapped
      v-model="searchData.type"
      class="mb-sm"
      :data="permissionDeviceTypeList"
      @on-change="onChangeTab"
    ></ui-tabs-wrapped>
    <div class="search-tag" v-if="searchData.type === 1">
      <div class="search">
        <api-organization-tree
          ref="orgTree"
          class="mr-sm"
          :select-tree="searchData"
          :custorm-node="false"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
        <tag-view
          :list="permissionTagList"
          :default-active="defaultTag"
          @tagChange="changeStatus"
          ref="tagView"
        ></tag-view>
      </div>
    </div>
    <div class="search-bar">
      <div class="flex-aic">
        <underline-menu
          v-model="searchData.queryType"
          :data="assignList"
          @on-change="onChangeUnderlineMenu"
        ></underline-menu>
        <Checkbox
          v-model="searchData.isOwnOrgCode"
          true-value="1"
          false-value="0"
          v-if="searchData.type == 1 && searchData.queryType == 2"
          class="flex-aic"
          @on-change="changeOwnerOrder"
        >
          <span>本单位在手工单</span>
          <Tooltip
            max-width="300"
            content="【本单位在手工单】仅包含当前指派给本单位处理的工单！取消勾选，系统则会统计当前指派给本单位处理和下发给下级单位处理的所有工单！"
          >
            <i class="icon-font icon-wenhao f-14 ml-xs font-dark-blue" @click.stop.prevent></i>
          </Tooltip>
        </Checkbox>
      </div>
      <ui-label class="inline mr-lg" label="创建时间：">
        <ui-radio-time :time-radio="timeRadio" @selectTime="selectTime"></ui-radio-time>
      </ui-label>
    </div>
    <ui-switch-drop
      v-if="componentName === 'all-work-order'"
      class="ui-switch-tab mt-md mb-md"
      v-model="queryConditionStatus"
      :tab-list="stateOptions"
      @changeTab="onChangeSwitchDrop"
    >
    </ui-switch-drop>
    <div class="component-container" v-ui-loading="{ tableData: [...permissionTagList, ...permissionDeviceTypeList] }">
      <component
        ref="workOrder"
        :key="searchData.type"
        :is="componentName"
        :statistics-type="permissionTagList.length > 0 && permissionTagList[defaultTag]"
        :common-search-data="searchData"
        :query-condition-status="queryConditionStatus"
        :statistics-detail="statisticsDetail"
        @click="onClickLink"
        @click-org="onOrgClickLink"
        @on-change-table-data="onChangeTableData"
      >
      </component>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { stateOptionsMap, tagList, deviceTypeList, ALLORDER, MYORDER } from './util/enum.js';
export default {
  name: 'governanceorder',
  props: {},
  data() {
    return {
      queryConditionStatus: 'qb',
      componentName: null,
      searchData: {
        type: 1,
        orgCode: '000000',
        queryType: '1',
        beginTime: '',
        endTime: '',
        isOwnOrgCode: '0', //本单位在手工单
      },
      permissionDeviceTypeList: [],

      permissionTagList: [],
      timeRadio: {
        title: '',
        value: 'all',
        timeList: [
          { label: '今日', value: 'today' },
          { label: '本周', value: 'thisWeek' },
          { label: '本月', value: 'thisMonth' },
          { label: '今年', value: 'thisYear' },
          { label: '全部', value: 'all' },
          { label: '自定义', value: 'defined' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
      },
      assignList: [
        /**
         * @ApiModelProperty("查询类型（为空查询所有，1 单位创建，2 指派给单位，3 当前指派给我，4 我创建的，5 我签收的，6 我处理的，7 我关闭的）")
         * private Integer queryType;
         */
        { code: '1', label: '单位创建' },
        { code: '2', label: '指派给单位' },
      ],
      /**
       *  @ApiModelProperty("工单类型（1 全部工单，2 我的工单）")
       *     private Integer type;
       */
      type: 1,
      state: '',
      stateOptions: [],
      defaultTag: 0,
      statisticsDetail: null,
    };
  },
  async mounted() {
    this.setOrgTreeDefaultNode();
    this.stateOptions = stateOptionsMap[this.searchData.queryType];
    const permissionsTagList = tagList.filter((item) =>
      this.$store.state.permission.permissions.includes(`${this.$route.name}-${item.permission}`),
    ); // 全部工单默认有权限
    const permissionsDeviceTypeList = deviceTypeList.filter((item) => {
      // 当tagList有权限时 默认给全部工单 赋权
      if (permissionsTagList.length > 0 && item.value === ALLORDER) return true;
      return this.$store.state.permission.permissions.includes(`${this.$route.name}-${item.permission}`);
    });
    this.permissionDeviceTypeList = permissionsDeviceTypeList;
    this.permissionTagList = permissionsTagList;
    //全部工单没权限 我的工单有权限时默认我的工单
    if (permissionsTagList.length === 0 && permissionsDeviceTypeList.length > 0) {
      let myOrder = deviceTypeList.find((item) => item.value === MYORDER);
      myOrder && (await this.onChangeTab(myOrder));
      return;
    }
    // 全部工单 和我的工单 都没有权限时 通知用户
    if (!permissionsTagList.length && !permissionsDeviceTypeList.length) return this.$Message.error('该用户暂无权限!');
    await this.$nextTick();
    if (!this.$route.params?.purpose) {
      this.changeStatus(0);
    }
  },
  watch: {},
  async activated() {},
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (Object.keys(to.params).length) {
        vm.receiveParamsFromOtherPages(to.params);
      }
    });
  },
  methods: {
    async onClickLink(row, column) {
      let { orgCode } = row;
      this.statisticsDetail = this.componentName === 'typedetails' ? row : null;
      let findIndex = this.permissionTagList.findIndex((item) => item.id === '3');
      if (findIndex === -1) return;
      this.defaultTag = findIndex;
      this.componentName = this.permissionTagList[this.defaultTag]['component'];
      // 类型统计 跳转需要带 工单类型
      this.searchData.orgCode = orgCode || this.searchData.orgCode || this.initialOrgList[0]?.orgCode;
      this.queryConditionStatus = column.queryConditionStatus;
    },
    onOrgClickLink(row) {
      if (this.searchData.orgCode === row.orgCode) return;
      this.searchData.orgCode = row.orgCode;
      this.$refs.workOrder.search();
    },
    selectedOrgTree(area) {
      this.searchData.orgCode = area.orgCode;
      this.$refs.workOrder.search();
    },
    async onChangeTab({ value }) {
      this.searchData.type = value;
      if (value === 1) {
        this.assignList = [
          { code: '1', label: '单位创建' },
          { code: '2', label: '指派给单位' },
        ];
        this.componentName = this.permissionTagList[this.defaultTag]['component'];
        this.searchData.queryType = '1';
        this.stateOptions = stateOptionsMap[this.searchData.queryType];
        this.searchData.orgCode = this.defaultSelectedOrg.orgCode || this.initialOrgList[0].orgCode;
        await this.$nextTick();
      } else if (value === 2) {
        this.componentName = 'all-work-order';
        this.assignList = [
          { code: '3', label: '当前指派给我' },
          { code: '4', label: '我创建的' },
          { code: '5', label: '我签收的' },
          { code: '6', label: '我处理的' },
          { code: '7', label: '我关闭的' },
        ];
        this.searchData.orgCode = '';
        this.searchData.queryType = '3';
        this.queryConditionStatus = 'qb';
        this.stateOptions = stateOptionsMap[this.searchData.queryType];
        this.statisticsDetail = null;
      }
      this.removeTooltipNode();
    },
    removeTooltipNode() {
      let tooltipNode = document.querySelectorAll('.ivu-tooltip-popper, .tips-box');
      tooltipNode.forEach((node) => {
        node.style.display = 'none';
      });
    },
    changeStatus(index) {
      this.defaultTag = index;
      this.componentName = this.permissionTagList[index]['component'];
      this.removeTooltipNode();
    },
    selectTime(timeRadio) {
      this.searchData.beginTime = timeRadio.startTime;
      this.searchData.endTime = timeRadio.endTime;
      this.$refs.workOrder.search();
    },
    async onChangeUnderlineMenu(val) {
      this.stateOptions = stateOptionsMap[val];
      this.queryConditionStatus = 'qb';
      await this.$nextTick();
      this.$refs.workOrder.search();
    },
    async onChangeSwitchDrop(val) {
      try {
        this.queryConditionStatus = val;
        await this.$nextTick();
        this.$refs.workOrder.search();
      } catch (e) {
        console.log(e);
      }
    },
    async onChangeTableData({ total }) {
      let index = this.stateOptions.findIndex((item) => item.value === this.queryConditionStatus);
      if (index !== -1) {
        this.$set(this.stateOptions[index], 'total', total);
      }
    },
    //选中本单位在手工单触发
    changeOwnerOrder() {
      this.$refs.workOrder.search();
    },
    //设置单位筛选默认选中
    setOrgTreeDefaultNode() {
      const { orgCode } = this.defaultSelectedOrg;
      if (this.$refs.orgTree) {
        this.$refs.orgTree.selectTree.orgCode = orgCode || this.initialOrgList[0]?.orgCode;
      }
    },
    //接收从其他页面过来的数据
    receiveParamsFromOtherPages(params) {
      if (!params.purpose) {
        return;
      }
      //所有路由跳转的操作
      switch (params.purpose) {
        case 'openOrderDetailModalById':
          this.openOrderDetailModal(params);
          break;
      }
    },
    //打开工单详情
    async openOrderDetailModal(params) {
      this.defaultTag = 2;
      this.componentName = 'all-work-order';
      await this.$nextTick();
      if (this.$refs.workOrder) {
        this.$refs.workOrder.editViewAction = { row: { id: params.detailId }, type: 'view' };
        this.$refs.workOrder.editViewOrderShow = true;
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UiTabsWrapped: require('@/components/ui-tabs-wrapped.vue').default,
    TagView: require('@/components/tag-view.vue').default,
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    AllWorkOrder: require('@/views/disposalfeedback/governanceorder/module/all-work-order.vue').default,
    UiSwitchDrop: require('@/components/ui-switch-drop/ui-switch-drop.vue').default,
    UnderlineMenu: require('@/components/underline-menu').default,
    workOrderStatistics: require('@/views/disposalfeedback/governanceorder/module/work-order-statistics.vue').default,
    typedetails: require('@/views/disposalfeedback/governanceorder/module/typedetails.vue').default,
    unitstatistics: require('@/views/disposalfeedback/governanceorder/module/unitstatistics.vue').default,
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.work-order-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--bg-content);
  padding: 10px 20px 0 20px;

  display: flex;
  flex-direction: column;
  .search-tag {
    position: absolute;
    top: 10px;
    right: 20px;
    .search {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .search-bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    border-radius: 4px;
    background: var(--bg-navigation);

    @{_deep} .underline-menu-wrapper {
      background: transparent;
      width: auto;
      .tab {
        font-size: 14px;
      }
      .tab:before {
        content: none;
      }
    }
  }
  .component-container {
    flex: 1;
  }
  @{_deep} .custorm-tree-node {
    height: 26px;
    line-height: 26px;
  }
}
</style>
