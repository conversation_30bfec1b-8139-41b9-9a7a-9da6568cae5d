<template>
  <div :style="{ background: bgColor, opacity }" class="cover">
    <div class="loading-multi">
      <img :src="loadingImg" />
      <div class="text" :style="{ color }">
        <slot></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    color: {
      type: String,
      default: "#2C86F8",
    },
    bgColor: {
      type: String,
      default: "#f9f9f9",
    },
    opacity: {
      type: Number,
      default: 0.8,
    },
  },
  data() {
    return {
      loadingImg: require("@/assets/img/loading-multi.png"),
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.cover {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  height: initial;
  width: 100%;
  z-index: 999;
  .loading-multi {
    z-index: 999;
    width: 100%;
    height: 100%;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      animation: turn1 0.8s linear infinite;
    }
    .text {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
}

@keyframes turn1 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
