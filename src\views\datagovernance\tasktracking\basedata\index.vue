<template>
  <div class="baseData">
    <p class="select-box">
      <ui-label class="fl ml-lg" label="治理流程" :width="70">
        <Select
          v-model="process"
          :clearable="false"
          placeholder="请选择"
          class="input-width fr"
          @on-change="changeTopic"
        >
          <Option :value="item.id" v-for="(item, index) of subThemeData" :key="index">
            {{ item.topicName }}
          </Option>
        </Select>
      </ui-label>
    </p>
    <div class="viewLoading">
      <loading v-if="viewLoading"></loading>
    </div>
    <div v-for="(item, index) in aggregateOptions" :key="index">
      <aggre-connect :propData="item" dWidth="12%" bWidth="13.01%"></aggre-connect>
    </div>
    <!-- 数据输入弹窗 -->
    <dataaccess-popup ref="dataaccessPopup" :is-base="process === 1"></dataaccess-popup>
    <!-- 字段映射、字典映射、正式入库、IP、mac、行政区域、设备编码-->
    <base-ring-popup
      ref="baseringPopup"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      :echart-list="echartList"
      :loading="loading"
      :echarts-loading="echartsLoading"
      @changePage="changePage"
      @changePageSize="changePageSize"
      @popUpGetData="popUpRingGetData"
      @openStandardList="openStandardList"
    >
      <template #modalTop>
        <div class="modalWrapper over-flow" v-if="ringPopupNeedOrgCodeSearch.includes(popUpOption.title)">
          <span class="base-text-color fl mt-sm">最新更新时间：{{ modifyTime }}</span>
          <div class="search-box fr mb-sm">
            <ui-label class="fl ml-lg" label="组织机构" :width="70">
              <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择">
              </api-organization-tree>
            </ui-label>
            <Button type="primary" class="ml-lg button-blue" @click="exportOrgCodeExcel">
              <i class="icon-font icon-daochu delete-icon"></i>
              <span class="inline vt-middle ml-xs">导出</span>
            </Button>
          </div>
        </div>
      </template>
      <template #tableTop>
        <line-title
          title-name="问题数据列表"
          class="mb-sm"
          v-if="ringPopupNeedOrgCodeSearch.includes(popUpOption.title)"
        ></line-title>
        <ui-select-tabs class="tabs-ui" v-if="tabsList.length" :list="tabsList" @selectInfo="selectInfo">
        </ui-select-tabs>
        <base-search
          ref="baseSearchRef"
          v-if="ringPopupNeedSearch.includes(popUpOption.title)"
          @startSearch="startTableSearch"
        >
          <Button type="primary" class="ml-lg button-blue fr" @click="exportSearchDataExcel">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </base-search>
      </template>
      <template #tabelAction="row">
        <span class="font-table-action pointer" @click="reverseTranslate(row)">重新转换</span>
      </template>
    </base-ring-popup>
    <!-- 空值检测、重复检测、空间信息检测、 -->
    <base-colunmring-popup
      ref="basecolunmringPopup"
      :table-data="tableData"
      :tabs-list="tabsList"
      :ring-echarts-option="ringEchartsOption"
      :column-echarts-option="columnEchartsOption"
      :total-count="totalCount"
      :loading="loading"
      :echarts-loading="echartsLoading"
      :modify-time="modifyTime"
      @exportExcel="exportColumnExcel"
      @selectInfo="selectInfo"
      @changePage="changePage"
      @changePageSize="changePageSize"
      @popUpGetData="popUpRingGetData"
      @openUnquatifiyReason="openUnquatifiyReason"
    >
      <template slot="searchSlot">
        <ui-label class="fl ml-lg" label="组织机构：" :width="70">
          <api-organization-tree :select-tree="selectOrgTree" @selectedTree="selectedOrgTree" placeholder="请选择">
          </api-organization-tree>
        </ui-label>
      </template>
    </base-colunmring-popup>
    <standardlist-popup v-model="standardlistPopup"></standardlist-popup>
    <unquatifiyreason-popup v-model="unquatifiyReasonPopup" :table-data="unquatifiyData" :loading="loading">
    </unquatifiyreason-popup>
    <export-data-popup ref="exportDataPopup" :tasktracking-inter="tasktracking"></export-data-popup>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import allInterface from '@/config/api/tasktracking';
import {
  aggregateEnums,
  aggregateOptions,
  actionTableColumns,
  tableColumns,
  checkTableColumns,
  ringColorEnum,
  clockTableColumns,
  areaTableColumns,
} from './util/enum';
let getTransferTnterface = [
  // 标准转换
  // 字段映射
  aggregateEnums.filedMapping,
  // 字典映射
  aggregateEnums.dicMapping,
  // 正式入库
  aggregateEnums.formalStore,
  // 临时入库
  aggregateEnums.temporateStore,
];
let testPop = [
  //'IP地址格式检测'
  aggregateEnums.IpTest,
  //'MAC地址格式检测'
  aggregateEnums.MacTest,
  //'行政区划格式检测'
  aggregateEnums.orgTest,
  //'设备编码格式检测'
  aggregateEnums.deviceIdTest,
];
let columnRingPop = [
  //'空值检测'
  aggregateEnums.nullTest,
  //'重复检测'
  aggregateEnums.repeatCheck,
  //'空间信息检测'
  aggregateEnums.spaceTest,
  //'数据输出'
  aggregateEnums.dataExport,
];
let tableColumnsText = [
  // 正式入库
  aggregateEnums.formalStore,
  // 临时入库
  aggregateEnums.temporateStore,
];
export default {
  name: 'baseData',
  props: {},
  data() {
    return {
      subThemeData: [],
      tasktracking: Object.freeze(allInterface.baseInterface),
      standardlistPopup: false,
      unquatifiyReasonPopup: false,
      aggregateOptions: aggregateOptions,
      popUpOption: {}, // 标准转换
      ringPopupNeedSearch: Object.freeze(getTransferTnterface), // 需要搜索插槽的弹窗
      ringPopupNeedOrgCodeSearch: [
        // 需要展示时间和选择组织机构的插槽
        aggregateEnums.nullTest,
        aggregateEnums.repeatCheck,
        aggregateEnums.timerTest,
        ...testPop,
      ],
      testPop: testPop,
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableColumns: Object.freeze(actionTableColumns),
      tableData: [],
      totalCount: 0,
      echartList: [{ echartRingOption: {} }, { echartRingOption: {} }, { echartRingOption: {} }],
      ringEchartsOption: {},
      columnEchartsOption: {},
      tabsList: [],
      loading: false,
      unquatifiyData: [],
      permission: '',
      process: 1,
      errorSelectList: [],
      echartsLoading: null,
      viewLoading: false,
      selectOrgTree: {
        orgCode: '',
      },
      modifyTime: '',
    };
  },
  async created() {
    await this.getThemeList();
    let subThemeIdArray = this.subThemeData.map((item) => item.id);
    let Id = this.$route.query.id;
    if (!!Id && subThemeIdArray.includes(Id)) {
      this.process = Id;
    }
    this.getViewList();
  },
  methods: {
    selectInfo(val) {
      this.errorSelectList = val.map((item) => item.name);
      this.searchData.pageNumber = 1;
      this.getList();
    },
    openStandardList() {
      this.standardlistPopup = true;
    },
    openUnquatifiyReason(row) {
      this.unquatifiyReasonPopup = true;
      this.getErrorMessgae(row);
    },
    changeTopic(val) {
      val === 1
        ? (this.tasktracking = allInterface.baseInterface)
        : (this.tasktracking = allInterface.temporateInterface);
      this.getViewList();
      val === 1
        ? (this.modifyTime = this.subThemeData[0].modifyTime)
        : (this.modifyTime = this.subThemeData[1].modifyTime);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.getList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.getList();
    },
    selectedOrgTree(val) {
      this.selectOrgTree.orgCode = val.orgCode;
      this.strategiesFunc();
    },
    popUpRingGetData(option) {
      this.errorSelectList = [];
      this.popUpOption = option;
      this.$nextTick(() => {
        if (this.$refs.baseSearchRef) {
          this.$refs.baseSearchRef.resetInitial();
        }
      });
      this.echartList = [{ echartRingOption: {} }, { echartRingOption: {} }, { echartRingOption: {} }];
      this.ringEchartsOption = {};
      this.columnEchartsOption = {};
      this.tabsList = [];
      this.tableData = [];
      this.totalCount = 0;
      this.selectOrgTree = {
        orgCode: '',
      };
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.strategiesFunc();
    },
    // 处理弹窗统计（echarts对应接口）
    strategiesFunc() {
      // 字段映射、字典映射
      if ([aggregateEnums.dicMapping, aggregateEnums.filedMapping].includes(this.popUpOption.title)) {
        this.queryTransferStatistics();
      }
      // 正式入库、临时入库
      if (tableColumnsText.includes(this.popUpOption.title)) {
        this.queryAccessStatistics();
      }
      if (columnRingPop.includes(this.popUpOption.title)) {
        this.getTopicStatisticsCount().then((data) => {
          this.handleRingData(data);
        });
        this.getTopicStatisticsList().then((data) => {
          this.handleColumnData(data);
          this.handleTabsList(data.map((item) => item.errorMessage));
        });
      }
      if (testPop.includes(this.popUpOption.title)) {
        this.getTopicStatisticsCount().then((data) => {
          this.handleEchartsList(data);
          //this.handleTabsList(data.errorMessageList)
        });
      }
      if (this.popUpOption.title === aggregateEnums.timerTest) {
        this.tableColumns = Object.freeze(clockTableColumns);
        this.queryTopicCheckTimeStatisticsList().then((data) => {
          this.handleTimerTestEcharts(data);
          this.handleTabsList(data.map((item) => item.errorMessage));
        });
      }
      this.handleTableColum();
      this.getList();
    },
    // 不同弹窗不同tableColumn
    handleTableColum() {
      // 处理tableColumn
      let actionTableColumnsText = [
        ...columnRingPop,
        // 标准转换
        //aggregateEnums.stardandChange,
      ];
      if ([aggregateEnums.dicMapping, aggregateEnums.filedMapping].includes(this.popUpOption.title)) {
        this.tableColumns = Object.freeze(tableColumns);
      }
      if (tableColumnsText.includes(this.popUpOption.title)) {
        this.tableColumns = Object.freeze([...tableColumns, { title: '失败原因', key: 'remark', width: 200 }]);
      }
      if (actionTableColumnsText.includes(this.popUpOption.title)) {
        this.tableColumns = Object.freeze(actionTableColumns);
      }
      if (testPop.includes(this.popUpOption.title)) {
        this.tableColumns = Object.freeze(checkTableColumns);
      }
      if (this.popUpOption.title === aggregateEnums.orgTest) {
        this.tableColumns = Object.freeze(areaTableColumns);
      }
    },
    // 不同弹窗获取列表的接口不同，需要处理
    getList() {
      let title = this.popUpOption.title;
      let testflag = [
        // 空值检测
        aggregateEnums.nullTest,
        // 重复检测
        aggregateEnums.repeatCheck,
        // 空间信息检测
        aggregateEnums.spaceTest,
        // 数据输出
        aggregateEnums.dataExport,
        // IP地址格式检测
        aggregateEnums.IpTest,
        // MAC地址格式检测
        aggregateEnums.MacTest,
        // 行政区划格式检测
        aggregateEnums.orgTest,
        // 设备编码格式检测
        aggregateEnums.deviceIdTest,
        // 时钟信息检测
        aggregateEnums.timerTest,
      ];
      if (testflag.includes(title)) {
        title = 'isTest';
      } else {
        title = '';
      }
      const listFuncObject = {
        // 字段映射
        [aggregateEnums.filedMapping]: () => this.getTransferList(),
        // 字典映射
        [aggregateEnums.dicMapping]: () => this.getTransferList(),
        // 正式入库
        [aggregateEnums.formalStore]: () => this.deviceInfoAccessList(),
        // 临时入库
        [aggregateEnums.temporateStore]: () => this.deviceInfoAccessList(),
        isTest: () => this.getTopicDetailList(),
      };
      listFuncObject[title]();
    },
    handleData(aggregateList) {
      this.aggregateOptions = this.$util.common.deepCopy(aggregateOptions);
      // 面板数据拍平
      let allAggregateDatas = [];
      this.aggregateOptions.forEach((item) => {
        allAggregateDatas.push(...item.datas);
      });
      // 面板datas数据添加filedData字段（后端返回的所有数据）
      aggregateList.forEach((element, index) => {
        let isOne = allAggregateDatas[index];
        isOne.filedData = element;
        //isOne.title = element.componentName
        if (element.componentCode === '3001') {
          isOne.title = '正式入库';
        }
        if (element.componentCode === '3002') {
          isOne.title = '临时入库';
        }
        let staticListData = isOne.filedData.topicComponentStatistics;
        if (!staticListData || !staticListData.accessDataCount) return;
        // 成功数据需要前端计算
        isOne.list.forEach((item) => {
          if (item.fileName) {
            item.num = staticListData[item.fileName];
          }
          if (item.fileName === 'successData') {
            item.num = staticListData.accessDataCount - staticListData.existingExceptionCount;
          }
          if (item.fileName === 'successDataRate') {
            item.num =
              ((staticListData.accessDataCount - staticListData.existingExceptionCount) /
                staticListData.accessDataCount) *
              100;
            parseInt(item.num) === parseFloat(item.num)
              ? (item.num = item.num + '%')
              : (item.num = item.num.toFixed(2) + '%');
          }
          if (item.fileName === 'existingExceptionRate') {
            item.num = item.num * 100;
            parseInt(item.num) === parseFloat(item.num)
              ? (item.num = item.num + '%')
              : (item.num = item.num.toFixed(2) + '%');
          }
        });
      });
    },
    reverseTranslate() {
      this.standardlistPopup = true;
    },
    startTableSearch(searchData) {
      Object.assign(this.searchData, searchData);
      this.getList();
    },
    handleTimerTestEcharts(data) {
      this.echartList = [];
      let dataList = [];
      let legendData = [];
      // let total = 0;
      data.forEach((item, index) => {
        let one = {
          name: item.errorMessage,
          value: item.count,
          color: Object.values(ringColorEnum)[index],
        };
        dataList.push(one);
        legendData.push(item.errorMessage);
      });
      // total = data.reduce((cur, pre) => {
      //   return cur.count + pre.count;
      // });
      // let option1Object = {
      //     data: dataList,
      //     text: '数据总量', subtext: `${total}`,
      //     legendData: legendData
      // }
      // let totalEchartsStatic = {
      //     echartRingOption: this.$util.doEcharts.taskTrackingRing( option1Object )
      // }
      //this.echartList.push(totalEchartsStatic)
      data.forEach((item, index) => {
        let one = {
          data: [
            {
              name: item.errorMessage,
              value: item.count,
              color: Object.values(ringColorEnum)[index],
            },
          ],
          text: item.errorMessage,
          subtext: `${item.count}`,
        };
        this.echartList.push({
          echartRingOption: this.$util.doEcharts.taskTrackingRing(one),
        });
      });
    },
    handleTabsList(data) {
      this.tabsList = data.map((item) => {
        let one = {
          select: false,
          name: item,
        };
        return one;
      });
    },
    handleColumnData(data) {
      let opts = {
        xAxisData: data.map((item) => item.errorMessage || '未知'),
        data: data.map((item) => item.count || 0),
      };
      this.columnEchartsOption = this.$util.doEcharts.taskTrackingColumn(opts);
    },
    handleEchartsList(data) {
      this.echartList = [];
      let successText = '入库成功';
      let failText = '入库失败';
      let totalText = '检测总量';
      if (testPop.includes(this.popUpOption.title)) {
        successText = '合格数据';
        failText = '异常数据';
      }
      if (this.popUpOption.title === (aggregateEnums.filedMapping || aggregateEnums.dicMapping)) {
        successText = '映射成功';
        failText = '映射失败';
        totalText = '映射总量';
      }
      if (this.popUpOption.title === aggregateEnums.formalStore) {
        totalText = '入库总量';
      } else {
        totalText = '';
      }
      let options1 = this.$util.doEcharts.taskTrackingRing({
        data: [
          { name: successText, value: data.goodCount, color: 'greenColor' },
          { name: failText, value: data.failCount, color: 'redColor' },
        ],
        text: totalText,
        subtext: `${data.deviceCount}`,
        legendData: [successText, failText],
      });
      let options2 = this.$util.doEcharts.taskTrackingRing({
        data: [{ name: successText, value: data.goodCount, color: 'greenColor' }],
        text: successText,
        subtext: `${data.goodCount}`,
      });
      let options3 = this.$util.doEcharts.taskTrackingRing({
        data: [{ name: failText, value: data.failCount, color: 'redColor' }],
        text: failText,
        subtext: `${data.failCount}`,
      });
      this.echartList = [
        { echartRingOption: options1 },
        { echartRingOption: options2 },
        { echartRingOption: options3 },
      ];
    },
    handleRingData(data) {
      this.tabsList = data.errorMessageList.map((item) => {
        let one = {
          select: false,
          name: item,
        };
        return one;
      });
      let opts = {
        text: '检测总量',
        subtext: `${data.deviceCount}`,
        data: [
          { name: '合格数据', value: data.goodCount, color: 'greenColor' },
          { name: '不合格数据', value: data.failCount, color: 'redColor' },
        ],
        legendData: ['合格数据', '不合格数据'],
      };
      this.ringEchartsOption = this.$util.doEcharts.taskTrackingRing(opts);
    },
    async getViewList() {
      try {
        this.viewLoading = true;
        let { data } = await this.$http.get(allInterface.viewTackeAggregate, {
          params: { id: this.process },
        });
        this.handleData(data.data.componentList);
        this.viewLoading = false;
      } catch (err) {
        this.viewLoading = false;
        console.log(err);
      }
    },
    async getTransferList() {
      try {
        this.loading = true;
        let { data } = await this.$http.post(this.tasktracking.getTransferList, this.searchData);
        this.tableData = data.data.entities;
        this.totalCount = data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    async deviceInfoAccessList() {
      try {
        this.loading = true;
        let { data } = await this.$http.post(this.tasktracking.deviceInfoAccessList, this.searchData);
        this.tableData = data.data.entities;
        this.totalCount = data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    async queryTransferStatistics() {
      this.echartsLoading = true;
      try {
        let { data } = await this.$http.get(this.tasktracking.queryTransferStatistics);
        this.handleEchartsList(data.data);
        this.echartsLoading = false;
      } catch (err) {
        console.log(err);
        this.echartsLoading = false;
      }
    },
    async queryAccessStatistics() {
      this.echartsLoading = true;
      try {
        let { data } = await this.$http.get(this.tasktracking.queryAccessStatistics);
        this.handleEchartsList(data.data);
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    // 查询问题列表
    async getTopicStatisticsList() {
      this.echartsLoading = true;
      let params = {
        checkStatus: 2,
      };
      if (this.popUpOption.title !== '数据输出') {
        params.topicComponentId = this.popUpOption.filedData.topicComponentId;
      }
      params.orgCode = this.selectOrgTree.orgCode;
      try {
        let { data } = await this.$http.post(this.tasktracking.queryTopicStatisticsList, params);
        this.echartsLoading = false;
        return data.data;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    // 时钟信息检测的环形图列表
    async queryTopicCheckTimeStatisticsList() {
      this.echartsLoading = true;
      let params = {
        topicComponentId: this.popUpOption.filedData.topicComponentId,
      };
      try {
        let { data } = await this.$http.post(this.tasktracking.queryTopicCheckTimeStatisticsList, params);
        this.echartsLoading = false;
        return data.data;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    async getTopicStatisticsCount() {
      this.echartsLoading = true;
      let params = {
        checkStatus: 2,
      };
      if (this.popUpOption.title !== '数据输出') {
        params.topicComponentId = this.popUpOption.filedData.topicComponentId;
      }
      params.orgCode = this.selectOrgTree.orgCode;
      try {
        let { data } = await this.$http.post(this.tasktracking.queryCountStatistics, params);
        this.echartsLoading = false;
        return data.data;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    async getTopicDetailList() {
      try {
        this.loading = true;
        let params = {
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNumber,
          errorMessageList: this.errorSelectList,
        };
        params.orgCode = this.selectOrgTree.orgCode;
        if (this.popUpOption.title !== '数据输出') {
          params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        }
        let { data } = await this.$http.post(this.tasktracking.queryListByTaskTracker, params);
        this.tableData = data.data.entities;
        this.totalCount = data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 查看不合格原因
    async getErrorMessgae(row) {
      let params = {
        //topicComponentId: this.popUpOption.filedData.id,
        deviceInfoId: row.id,
      };
      if (this.popUpOption.filedData.componentCode !== '1002') {
        params.topicComponentId = this.popUpOption.filedData.topicComponentId;
      }
      try {
        let { data } = await this.$http.post(this.tasktracking.queryPropertyDetailList, params);
        this.unquatifiyData = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async getThemeList() {
      this.viewLoading = true;
      try {
        let res = await this.$http.post(governancetheme.subthemeList, {});
        this.subThemeData = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    exportSearchDataExcel() {
      this.exportExcel(this.searchData);
    },
    exportOrgCodeExcel() {
      let params = {
        //errorMessageList: this.errorSelectList,
        orgCode: this.selectOrgTree.orgCode,
      };
      this.exportExcel(params);
    },
    exportColumnExcel() {
      let params = {
        orgCode: this.selectOrgTree.orgCode,
      };
      if (this.errorSelectList.length) params.errorMessageList = this.errorSelectList + '';
      this.exportExcel(params);
    },
    async exportExcel(params) {
      try {
        if (this.popUpOption.filedData.componentCode !== '1002') {
          params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        }
        let res = await this.$http.post(this.tasktracking.downloadDeviceAbnormalData, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    async '$route.query.id'() {
      if (this.$route.name === 'tasktracking') {
        await this.getThemeList();
        let subThemeIdArray = this.subThemeData.map((item) => item.id);
        let Id = this.$route.query.id;
        if (!!Id && subThemeIdArray.includes(Id)) {
          this.process = Id;
        }
        this.getViewList();
      }
    },
  },
  components: {
    AggreConnect: require('../../governancetheme/components/aggre-connect.vue').default,
    dataaccessPopup: require('./popup/dataaccess-popup.vue').default,
    standardlistPopup: require('./popup/standardlist-popup.vue').default,
    unquatifiyreasonPopup: require('./popup/unquatifiyreason-popup.vue').default,
    BaseRingPopup: require('./components/base-ring-popup.vue').default,
    BaseColunmringPopup: require('./components/base-colunmring-popup.vue').default,
    BaseSearch: require('./components/base-search.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ExportDataPopup: require('./popup/export-data-popup.vue').default,
  },
};
</script>
<style lang="less" scoped>
.modalWrapper {
  overflow: hidden;
  height: 45px;
  line-height: 45px;
  .search-box {
    display: flex;
    .input-width {
      width: 230px;
    }
  }
}
.input-width {
  width: 200px;
}
.select-box {
  position: absolute;
  top: 20px;
}
.echartsTab {
  position: absolute;
  right: 20px;
  > span {
    display: inline-block;
    padding: 10px 15px;
    color: var(--color-primary);
  }
}
</style>
