<!-- 数量达标率 -->
<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      @on-change-code="onChangeOrgRegion"
    >
    </result-title>
    <component :is="componentName" v-bind="handleProps()"> </component>
  </div>
</template>
<script>
import { mapActions } from 'vuex';
export default {
  name: 'BasicDeviceHundred',
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalResult',
      iconList: [],
      tagList: Object.freeze([
        {
          label: '统计列表',
          value: 'StatisticalResult',
        },
      ]),
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
    };
  },
  created() {
    this.reallyTagList = this.tagList.map((item) => item.label);
    this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    // 动态组件 - 动态传参
    handleProps() {
      let props = {
        activeIndexItem: this.activeIndexItem,
      };
      return props;
    },
  },
  watch: {},
  components: {
    ResultTitle: require('../../components/result-title.vue').default,
    StatisticalResult: require('./statistical-results.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
}
</style>
