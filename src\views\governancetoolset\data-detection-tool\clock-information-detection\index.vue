<template>
  <div :class="isResult ? 'governancetoolset-child-result' : 'governancetoolset-child'">
    <div class="child-head">
      <div class="head"><i class="icon-font icon-shujujiancegongju"></i><span class="title">时钟信息检测</span></div>
      <Button type="text" class="btn-back mr-sm" @click="backHandle">&lt;&lt; 返回</Button>
    </div>
    <div v-show="!isResult" class="governancetoolset-child-container">
      <Form
        ref="formData"
        :model="formData"
        :rules="ruleValidate"
        :label-width="158"
        label-position="left"
        class="set-form"
      >
        <FormItem prop="selectDeviceType" label="1、选择设备：">
          <RadioGroup v-model="formData.selectDeviceType">
            <Radio label="1">离线文件</Radio>
            <Radio label="2">系统选择</Radio>
          </RadioGroup>
          <div class="btns" v-if="formData.selectDeviceType === '1'">
            <FormItem
              prop="governanceToolsetVoList"
              :rules="{
                required: true,
                type: 'array',
                message: '请上传文件',
                trigger: 'change',
              }"
            >
              <Upload
                action="/ivdg-device-data-service/governance/importExcel"
                :headers="headers"
                :data="uploadData"
                :show-upload-list="false"
                :beforeUpload="beforeUpload"
                :on-success="importSuccess"
                :on-error="importError"
                ref="upload"
              >
                <Button
                  custom-icon="icon-font icon-shangchuan"
                  :loading="uploadLoading"
                  type="dashed"
                  class="ivu-btn-dashed"
                >
                  <span class="btn-text ellipsis">
                    {{ fileName ? fileName : '上传按模板填写的excel文件' }}
                  </span>
                </Button>
              </Upload>
            </FormItem>
            <Button class="ml-sm" type="text" :loading="downloadLoading" @click="downloadHandle">下载模板</Button>
          </div>
          <template v-else>
            <FormItem
              prop="governanceToolsetVoList"
              :rules="{
                required: true,
                type: 'array',
                message: '请选择设备',
                trigger: 'change',
              }"
            >
              <div class="select-device" @click="selectDeviceBtn">
                <i v-show="!formData.governanceToolsetVoList.length" class="icon-font icon-shitujichushuju"></i>
                <span class="select-device-text">{{
                  formData.governanceToolsetVoList.length
                    ? `已选择${formData.governanceToolsetVoList.length}条数据`
                    : '请选择设备'
                }}</span>
              </div>
            </FormItem>
          </template>
        </FormItem>
        <FormItem label="2、检测设备时钟：" class="label-width">
          <FormItem prop="time" label="允许时间误差：">
            <InputNumber v-model="formData.time" /><span class="second-text">秒</span>
          </FormItem>
        </FormItem>
      </Form>
      <Button type="primary" class="submit-btn" @click="submitHandle('formData')">确定</Button>
    </div>
    <!-- 检测结果 -->
    <detection-result ref="detectionResult" v-show="isResult" :data-obj="formData"></detection-result>
    <!-- 选择设备 -->
    <select-device-modal
      ref="selectDeviceModle"
      v-if="selectModeVisible"
      :toolType="formData.type"
      :selectedDeviceList="selectedDeviceList"
      @setDeviceHandle="setDeviceHandle"
    />
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';
export default {
  name: 'ClockInformationDetection',
  components: {
    'detection-result': require('./detection-result.vue').default,
    'select-device-modal': require('../components/select-device-modal.vue').default,
  },
  data() {
    return {
      formData: {
        type: '1',
        selectDeviceType: '1',
        governanceToolsetVoList: [],
        time: null,
      },
      selectedDeviceList: [],
      ruleValidate: {
        time: [
          {
            required: true,
            type: 'number',
            message: '请输入允许时间误差',
            trigger: 'blur',
          },
        ],
      },
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      downloadLoading: false,
      uploadLoading: false,
      uploadData: {
        type: '1',
      },
      fileName: '',
      selectModeVisible: false,
      isResult: false,
    };
  },
  watch: {
    'formData.selectDeviceType'() {
      this.formData.governanceToolsetVoList = [];
    },
  },
  methods: {
    // 配置确定
    submitHandle(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.isResult = true;
          this.$nextTick(() => {
            this.$refs.detectionResult.init();
          });
        }
      });
    },
    // 返回
    backHandle() {
      if (this.isResult) {
        this.isResult = false;
      } else {
        this.$router.push({ name: 'governancetoolset' });
      }
    },
    // 下载模板
    async downloadHandle() {
      try {
        this.downloadLoading = true;
        let params = {
          type: '1',
        };
        let res = await this.$http.post(governancetoolset.downloadTemplate, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res, '时钟信息检测设备模板');
        this.downloadLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 上传文件之前
    beforeUpload(file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！');
        return false;
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error('上传文件大小不能超过 30MB!');
        return false;
      }
      this.uploadLoading = true;
    },
    // 上传成功
    importSuccess(file, response) {
      this.uploadLoading = false;
      if (!file || file.length === 0) {
        this.$Message.warning('文件数据有误，请检查后重新上传');
        return false;
      }
      this.fileName = response.name;
      this.formData.governanceToolsetVoList = file;
    },
    // 上传失败
    importError() {
      this.uploadLoading = false;
    },
    // 选择设备
    selectDeviceBtn() {
      this.selectModeVisible = true;
      this.$nextTick(() => {
        this.$refs.selectDeviceModle.init();
      });
    },
    setDeviceHandle(devices) {
      this.selectedDeviceList = devices;
      let list = [];
      devices.forEach((v) => {
        list.push({
          deviceId: v.deviceId,
          deviceName: v.deviceName,
          ip: v.ipAddr,
          port: v.port,
          userName: v.userId,
          pwd: v.password,
          channelNumber: v.tdh,
          manufacturer: v.manufacturer,
        });
      });
      this.formData.governanceToolsetVoList = list;
      this.$refs.formData.validateField('governanceToolsetVoList', () => {});
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .governancetoolset-child,
  .governancetoolset-child-result {
    background: var(--bg-content) url('~@/assets/img/datagovernance/governance_tool_set_child_bg.png') no-repeat
      center/cover;
    .child-head .head {
      color: #19d5f6;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .governancetoolset-child,
  .governancetoolset-child-result {
    background: #f1f3f6 url('~@/assets/img/datagovernance/governance_tool_set_child_bg_light.png') no-repeat
      center/cover;
    .child-head .head {
      color: var(--color-primary);
    }
  }
}

.governancetoolset-child,
.governancetoolset-child-result {
  display: flex;
  flex: 1;
  padding: 0 20px;
  box-sizing: border-box;
  flex-direction: column;
  height: 100%;
  position: relative;
  .child-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    height: 50px;
    .head {
      display: flex;
      align-items: center;
      .icon-font {
        font-size: 24px;
        margin-right: 5px;
      }
      .title {
        font-size: 16px;
        line-height: 20px;
        font-weight: bold;
      }
    }
  }
  .governancetoolset-child-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .set-form {
      margin-top: -150px;
      .btns {
        display: flex;
        align-items: center;
      }
      .upload-btn {
        margin-right: 16px;
        display: flex;
        align-items: center;
        /deep/.icon-font {
          font-size: 20px;
          line-height: 20px;
        }
        /deep/span {
          height: 34px;
        }
        .btn-text {
          max-width: 200px;
          display: inline-block;
        }
      }
      .select-device {
        width: 270px;
        height: 34px;
        border: 1px dashed var(--color-active);
        background: rgba(43, 132, 226, 0.1);
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--color-active);
        .icon-font {
          font-size: 16px;
          margin-right: 10px;
        }
      }
      .select-device:hover {
        background: rgba(43, 132, 226, 0.2);
        border: 1px dashed #3c90e9;
      }
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          padding-right: 10px;
        }
        .ivu-form-item-content {
          display: flex;
        }
        .ivu-radio-group-item {
          margin-right: 30px;
          .ivu-radio {
            margin-right: 10px;
          }
        }
      }
      /deep/.label-width {
        .ivu-form-item {
          display: flex;
          .ivu-form-item-label {
            width: auto !important;
            padding-right: 0;
          }
          .ivu-input {
            width: 85px;
          }
          .second-text {
            color: var(--color-content);
            margin-left: 10px;
          }
          .ivu-form-item-label::before {
            display: none;
          }
        }
      }
    }
    .submit-btn {
      margin-top: 30px;
      width: 200px;
    }
  }
  .btn-back {
    color: var(--color-active) !important;
  }
}
.governancetoolset-child-result {
  background: var(--bg-content);
}
</style>
