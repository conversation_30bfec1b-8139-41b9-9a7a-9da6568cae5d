<template>
  <!-- 轨迹图片可访问率 -->
  <ui-modal v-model="captureVisible" title="抓拍详情" :footerHide="true" width="89%" class="defail-modal">
    <Row>
      <Col span="4" class="contentLeft">
        <div v-if="personObj">
          <div class="capture-details-left-sculpture">
            <ui-image :src="personObj.identityPhoto" />
          </div>
          <div>
            <span class="sculpture-item-label">姓名：</span>
            <span>{{ personObj.name || '未知' }}</span>
          </div>
          <div>
            <span class="sculpture-item-label">身份证号：</span>
            <span>{{ personObj.idCard || '未知' }}</span>
          </div>
          <tags-more
            v-if="personObj.personTypes"
            :tagList="personObj.personTypes"
            :defaultTags="4"
            placement="left-start"
            bgColor="#2D435F"
          ></tags-more>
        </div>
      </Col>
      <Col span="20" style="overflow: hidden">
        <div class="content">
          <slot name="searchList"></slot>
          <div class="list" v-ui-loading="{ loading: loading, tableData: tableData }">
            <div :style="styleScroll6">
              <div class="carItem" v-for="item in tableData" :key="item.id">
                <div class="item">
                  <div class="img" @click="viewBigPic(item)">
                    <ui-image :src="item.trackImage" />
                    <div class="num" v-if="item.similarity">
                      {{ similarityVal(item.similarity) }}
                    </div>
                    <div class="active-wrapper">
                      <p
                        v-permission="{
                          route: $route.name,
                          permission: 'artificialreviewr',
                        }"
                        class="ellipsis-text"
                        title="人工复核"
                        @click.stop="artificialReview(item)"
                      >
                        <i class="ellipsis-text icon-font icon-xiajikaohedefen vt-middle f-14 mr-xs"></i>
                        <span class="artificial-text f-14 ellipsis-text">人工复核</span>
                      </p>
                    </div>
                  </div>
                  <div class="group-message">
                    <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据'}`">
                      <i class="icon-font icon-shijian f-14"></i>
                      <span class="group-text inline vt-middle ml-xs f-14 ellipsis onlys">{{ item.shotTime }}</span>
                    </p>
                    <p :title="item.catchPlace">
                      <i class="icon-font icon-dizhi f-14"></i>
                      <span class="group-text inline vt-middle ml-xs f-14 ellipsis onlys">{{
                        item.catchPlace ? item.catchPlace : '暂无数据'
                      }}</span>
                    </p>
                  </div>
                  <i class="icon-font icon-URLbukefangwen1" v-if="item.urlAvailableStatus != 1"></i>
                </div>
              </div>
            </div>
            <!-- 分页 -->
          </div>
          <ui-page
            class="page menu-content-background"
            :page-data="pageData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          ></ui-page>
        </div>
        <!-- 复核 -->
        <review-and-detail
          v-model="artificialVisible"
          :is-view="isView"
          :show-small-pic="!isView"
          :title="modalTitle"
          :review-row-data="detailData"
          :error-code-list="errorCodeList"
          :total-count="pageData.totalCount"
          :table-data="tableData"
          :page-data="pageData"
          :filed-name-map="filedNameMap"
          :get-list-api="getListApi"
          :search-parames="searchData"
          :custom-value-field="customValueField"
          @closeFn="updateTable"
        ></review-and-detail>
      </Col>
    </Row>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import api from '@/config/api/inspectionrecord';
export default {
  name: 'captureDetail',
  props: {
    resultId: {},
    // 接口名称
    interFaceName: {
      default: api.pageTrackCatchDetails,
    },
    filedNameMap: {},
    errorCodeList: {},
  },
  data() {
    return {
      // 格式：  自定义字段：对应的取值字段
      customValueField: {
        qualified: 'qualified', // 是否合格字段
        reason: 'firstLevelResultTip', // 不合格原因取值字段
      },
      captureVisible: false,
      tableData: [],
      searchData: {
        pageNumber: 1,
        pageSize: 20,
        customParameters: {},
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styleScroll6: {
        position: 'relative',
        width: '100%',
        height: '620px',
        'overflow-y': 'scroll',
      },
      personObj: {}, // 人员信息对象
      loading: false,
      artificialVisible: false,
      detailData: {},
      isView: false,
      modalTitle: '查看大图',
    };
  },
  async created() {},
  mounted() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  methods: {
    show(item) {
      this.personObj = item;
      this.infoList();
      this.captureVisible = true;
    },
    async infoList() {
      this.loading = true;
      let params = Object.assign(this.searchData);
      params.customParameters.idCard = this.personObj.idCard;
      params.indexId = this.resultId.indexId;
      params.batchId = this.resultId.batchId;
      params.access = this.resultId.access;
      params.displayType = this.resultId.displayType;
      params.orgRegionCode = this.resultId.orgRegionCode;
      let res = await this.$http.post(this.interFaceName, Object.assign(params));
      this.tableData = res.data.data.entities;
      this.pageData.totalCount = res.data.data.total;
      this.loading = false;
    },
    viewBigPic(item) {
      this.detailData = { ...item };
      this.artificialVisible = true;
      this.isView = true;
      this.modalTitle = '查看大图';
    },
    // 分页
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
    },
    // 检索，父组件调用
    startSearch(searchData) {
      this.searchData = Object.assign(this.searchData, searchData);
      this.infoList();
    },
    similarityVal(val) {
      return (val * 100).toFixed(2) + '%';
    },
    artificialReview(row) {
      this.detailData = { ...row };
      this.artificialVisible = true;
      this.isView = false;
      this.modalTitle = '图片人工复核';
    },
    // isReview：复核过      closeIndex：人工复核弹框关闭时 -设备 所在总表中的索引
    updateTable({ isReview, closeIndex }) {
      let { pageNum, pageSize } = this.pageData;
      let newPage = Math.floor(closeIndex / pageSize) + 1;
      this.pageData.pageNum = newPage;
      this.searchData.pageNumber = newPage;
      if (isReview || newPage !== pageNum) {
        this.infoList();
      }
    },
    async getListApi({ pageNumber, pageSize, isUpdate, currentRow }) {
      try {
        const { orgRegionCode, displayType, indexId, batchId, access } = this.resultId;
        let customParameters = {
          ...this.searchData.customParameters,
          idCard: this.personObj.idCard,
        };
        // isUpdate=true  -- 表示需要更新某一条数据
        if (isUpdate) {
          pageNumber = 1;
          customParameters = {
            id: currentRow.id,
            idCard: this.personObj.idCard,
          };
        }
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: displayType,
          orgRegionCode: orgRegionCode,
          customParameters: customParameters,
          pageNumber: pageNumber,
          pageSize: pageSize,
        };
        let res = await this.$http.post(this.interFaceName, data);
        return res;
      } catch (err) {
        throw new Error();
      }
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      !val ? this.reset() : null;
      this.captureVisible = val;
    },
  },
  components: {
    uiImage: require('@/components/ui-image').default,
    TagsMore: require('./tags-more.vue').default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/components/face-vehicle-url-algorithm/mode-pic.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 30px 0 0 !important;
}
@{_deep}.ivu-modal-header {
  padding: 0 !important;
}
.contentLeft {
  color: var(--color-content);
  padding: 0 20px;
  border-right: 1px solid var(--border-color);
  div {
    &:nth-child(2) {
      margin-top: 10px;
    }
    &:nth-child(3) {
      margin-top: 10px;
    }
  }

  img {
    width: 100%;
  }

  ul {
    li {
      float: left;
      padding: 6px 10px;
      background: #1a447b;
      border-radius: 4px;
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .sculpture-item-label {
    color: var(--color-label);
  }
}
.content {
  color: var(--color-content);
}

@{_deep}.defail-modal .ivu-modal-footer {
  height: 0 !important;
  padding: 0 !important;
}

.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  width: 188px;
  display: inline-block;
  // max-width: 188px;
  .item {
    position: relative;
    height: 100%;
    background: var(--bg-info-card);
    border: 1px solid var(--border-info-card);

    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      z-index: 1;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
        .active-wrapper {
          display: block;
        }
      }
      .num {
        position: absolute;
        left: 0;
        top: 15px;
        z-index: 10;
        padding: 0px 6px;
        // border-radius: 5px;
        background: #ea800f;
        // background: rgba(42, 95, 175, 0.6);
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 12px;
      color: #8797ac;
    }

    .icon-URLbukefangwen1 {
      color: var(--color-failed);
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }
  }
}
/deep/ .ivu-row {
  align-content: start;
}
.capture-details-left-sculpture {
  margin-bottom: 20px;
  margin-top: 40px;
  width: 243px;
  height: 243px;
  border: 1px solid var(--border-color);
}
.capture-details-right-top {
  margin-top: 30px;
  padding: 20px 20px 20px;
  font-size: 14px;
  color: #ffffff;
  border-bottom: 1px solid var(--border-color);
  label {
    margin-right: 58px;
    span {
      color: var(--color-bluish-green-text);
    }
  }
}
.list {
  padding-left: 20px;
}
.onlys {
  width: 80%;
}
.active-wrapper {
  z-index: 11;
  position: absolute;
  bottom: 0;
  width: 100%;
  display: none;
  padding-left: 10px;
  background: rgba(0, 0, 0, 0.5);
  .artificial-text {
    cursor: pointer;
    vertical-align: middle;
  }
}
.ellipsis-text {
  color: #2b84e2;
}
</style>
