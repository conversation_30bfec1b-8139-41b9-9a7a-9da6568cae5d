<template>
  <div class="fold-panel">
    <div class="fold-panel-header">
      <div class="fold-panel-header-title">{{ title }}</div>
      <div class="fold-panel-header-arrow">
        <i class="icon-font icon-xialazhankai up" :class="showAll ? 'rotate' : ''" @click="showAll = !showAll"> </i>
      </div>
    </div>
    <div class="fold-panel-content" v-show="showAll">
      <span
        class="fold-panel-content-tag"
        :style="{ background: bgColor }"
        v-for="(item, index) in list"
        :key="index"
        >{{ item.tagName }}</span
      >
    </div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => [],
    },
    bgColor: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showAll: true,
    };
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.fold-panel {
  width: 100%;
  margin-bottom: 5px;
  &-header {
    display: flex;
    align-items: center;
    height: 50px;
    padding: 0 15px;
    background: var(--bg-collapse-item);
    &-title {
      flex: 1;
      font-size: 14px;
    }
    &-arrow {
      i {
        font-size: 12px;
        color: var(--color-collapse-arrow);
      }
    }
  }
  &-content {
    height: 200px;
    padding: 20px;
    background: var(--bg-sub-content);
    overflow-y: auto;
    &-tag {
      display: inline-block;
      height: 34px;
      line-height: 34px;
      margin: 5px;
      padding: 0 22px;
      color: #ffffff;
      background: var(--color-primary);
      border-radius: 4px;
    }
  }
  .up {
    transform: scale(0.8);
  }
  .rotate {
    transform: rotate(180deg) scale(0.8);
  }
}
</style>
