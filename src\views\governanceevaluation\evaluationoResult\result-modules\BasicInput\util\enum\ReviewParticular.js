import global from '@/util/global';
export const iconStaticsList = {
  video: [
    {
      name: '已建档总量:',
      count: '0',
      countStyle: {
        color: 'var(--color-bluish-green-text)',
      },
      iconName: 'icon-yingjianceshebeishuliang',
      fileName: 'biVideoCount',
    },
    {
      name: '未建档总量:',
      count: '0',
      countStyle: {
        color: '#DE990F',
      },
      iconName: 'icon-shijijianceshebeishuliang',
      fileName: 'videoUnDocCount',
    },
  ],
  face: [
    {
      name: '已建档总量:',
      count: '0',
      countStyle: {
        color: 'var(--color-bluish-green-text)',
      },
      iconName: 'icon-renliankakou',
      fileName: 'biFaceCount',
    },
    {
      name: '未建档总量:',
      count: '0',
      countStyle: {
        color: '#DE990F',
      },
      iconName: 'icon-renliankakou',
      fileName: 'faceUnDocCount',
    },
  ],
  vehicle: [
    {
      name: '已建档总量:',
      count: '0',
      countStyle: {
        color: 'var(--color-bluish-green-text)',
      },
      iconName: 'icon-cheliangkakou',
      fileName: 'biVehicleCount',
    },
    {
      name: '未建档总量:',
      count: '0',
      countStyle: {
        color: '#DE990F',
      },
      iconName: 'icon-cheliangkakou',
      fileName: 'vehicleUnDocCount',
    },
  ],
};

export const tableColumn = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'orgName',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.longitude}`,
    key: 'longitude',
    slot: 'longitude',
    minWidth: 120,
  },
  {
    title: `${global.filedEnum.latitude}`,
    key: 'latitude',
    slot: 'latitude',
    minWidth: 120,
  },
  {
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    minWidth: 150,
  },
  { title: 'IPv4地址', key: 'ipAddr', minWidth: 120 },
  { title: '摄像机功能类型', key: 'sbgnlxText', align: 'left', minWidth: 130, tooltip: true },
  { title: '监控点位类型', key: 'sbdwlxText', minWidth: 130 },
  { title: '摄像机采集区域', key: 'sbcjqyText', align: 'left', tooltip: true, minWidth: 130 },
  { title: '安装地址', key: 'address', minWidth: 150, tooltip: true },
  {
    title: '设备状态',
    slot: 'phyStatusText',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '数据来源',
    key: 'sourceIdText',
    minWidth: 150,
    tooltip: true,
  },
];

export const formItemData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'phyStatus',
    label: '设备状态',
    placeholder: '请选择设备状态',
    options: [
      { value: '1', label: '可用' },
      { value: '2', label: '不可用' },
    ],
  },
];
