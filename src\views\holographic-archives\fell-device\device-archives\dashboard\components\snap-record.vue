<template>
  <div class="my-swiper-container" id="mySwiper">
    <swiper
      ref="mySwiper"
      v-if="!swiperResize"
      :options="swiperOption"
      class="my-swiper"
    >
      <template v-for="(item, index) in list">
        <swiper-slide :key="index">
          <div class="swiper-item box-2">
            <div class="collection">
              <div class="bg"></div>
              <ui-btn-tip
                class="collection-icon"
                v-if="item.myFavorite == '1'"
                content="取消收藏"
                icon="icon-yishoucang"
                transfer
                @click.native="collection(item, 2)"
              />
              <ui-btn-tip
                class="collection-icon"
                v-else
                content="收藏"
                icon="icon-shoucang"
                transfer
                @click.native="collection(item, 1)"
              />
            </div>
            <p class="identifier-content" v-show="deviceType == 3">
              <img
                class="img-icon"
                src="@/assets/img/icons/icon-wifi.png"
                alt=""
              />
              {{ item.mac }}
            </p>
            <p class="identifier-content" v-show="deviceType == 5">
              <img
                class="img-icon"
                src="@/assets/img/icons/icon-RFID.png"
                alt=""
              />
              {{ item.rfidCode }}
            </p>
            <p class="identifier-content" v-show="deviceType == 4">
              <img
                class="img-icon"
                src="@/assets/img/icons/icon-electric.png"
                alt=""
              />
              {{ item.imsi }}
            </p>
            <p class="identifier-content" v-show="deviceType == 16">
              <img
                class="img-icon"
                src="@/assets/img/icons/icon-etc.png"
                alt=""
              />
              {{ item.obuId }}
            </p>
            <p class="identifier-content" v-show="deviceType == 6">
              <img
                class="img-icon"
                src="@/assets/img/icons/icon-gps.png"
                alt=""
              />
              {{ item.gpsCode }}
            </p>
            <div class="bottom-info">
              <p>
                <Tooltip
                  content="时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{
                  item.absTime || "--"
                }}</span>
              </p>
              <p>
                <Tooltip
                  content="设备名称"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-leixing"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{ item.deviceName }}</span>
              </p>
              <p>
                <Tooltip
                  content="位置"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-location"></i>
                </Tooltip>
                <span class="ellipsis" v-show-tips>{{
                  item.detailAddress
                }}</span>
              </p>
            </div>
          </div>
        </swiper-slide>
      </template>
    </swiper>
    <div class="swiper-button-prev snap-prev" slot="button-prev">
      <i class="iconfont icon-caret-right"></i>
    </div>
    <div class="swiper-button-next snap-next" slot="button-next">
      <i class="iconfont icon-caret-right"></i>
    </div>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
export default {
  components: { swiper, swiperSlide },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    // 3人脸/2车辆
    deviceType: {
      type: [Number, String],
      default: 2,
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      swiperResize: false,
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 2.95,
        centeredSlides: true,
        initialSlide: 2,
        // loop: false,
        // loopAdditionalSlides: 5,
        speed: 1000,
        // autoplay: {
        //   delay: 100000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false,
        //   autoplayDisableOnInteraction: false
        // },
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 50, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 90, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".snap-next",
          prevEl: ".snap-prev",
        },
        observer: true,
        observeParents: true,
      },
    };
  },
  mounted() {
    let _this = this;
    this.$erd.listenTo(document.getElementById("mySwiper"), (element) => {
      _this.updateSliderHandle();
    });
  },
  methods: {
    updateSliderHandle() {
      const w = document.documentElement.clientWidth;
      this.swiperOption.coverflowEffect.stretch = (w / 192) * 5.85;
      this.swiperOption.coverflowEffect.depth = (w / 192) * 10;
      this.swiperResize = true;
      this.$nextTick(() => {
        this.swiperResize = false;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.my-swiper-container {
  padding: 0 30px;
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
  .my-swiper {
    margin: auto;
    padding: 15px 0;
    .swiper-item {
      width: 100%;
      height: 190px;
      background: #f9f9f9;
      box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      box-sizing: border-box;
      padding: 10px;
      overflow: hidden;
      .list-card {
        position: relative;
        background-color: #f9f9f9;
        margin-bottom: 10px;
        height: min-content;
        box-sizing: border-box;
        .operate-bar {
          height: 30px;
          background: linear-gradient(
            90deg,
            rgba(87, 187, 252, 0.8) 0%,
            #2c86f8 100%
          );
          border-radius: 0px 0px 4px 0px;
          position: absolute;
          right: -100%;
          transition: all 0.3s;
          bottom: 0;
          transform: skewX(-20deg);
          .operate-content {
            padding: 0 5px;
            transform: skewX(20deg);
            height: 100%;
            display: flex;
            align-items: center;
            color: #fff;
            /deep/ .ivu-tooltip-rel {
              padding: 6px;
            }
          }
        }
      }
      .collection {
        width: 30px;
        height: 30px;
        position: absolute;
        z-index: 2;
        top: 3px;
        right: 3px;
        .collection-icon {
          position: absolute;
          top: -1px;
          right: 1px;
          /deep/ .iconfont {
            font-size: 14px;
            color: #fff;
          }
          /deep/ .icon-shoucang {
            color: #888888 !important;
            text-shadow: 0px 1px 0px #e1e1e1;
          }
          /deep/ .icon-yishoucang {
            color: #f29f4c !important;
          }
        }
      }
    }
    .box-2 {
      width: 280px;
      height: 120px;
      padding: 3px;
      position: relative;
      .content {
        overflow: hidden;
        position: relative;
        height: 100%;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        border: 1px solid #d3d7de;
        &:before {
          content: "";
          // position: absolute;
          // top: -1px;
          // left: -1px;
          // right: -1px;
          // bottom: -1px;
          // border: 2px solid transparent;
          // border-radius: 4px;
        }

        .check-box {
          position: absolute;
          top: 4px;
          left: 4px;
          display: none;
        }
        &:hover {
          &:before {
            border-color: #2c86f8;
          }
          .operate-bar {
            right: -6px;
            bottom: -1px;
          }
        }
      }
      &:hover {
        &:before {
          border-color: #2c86f8;
        }
        .content {
          .check-box {
            display: inline-block;
          }
        }
      }
      .identifier-content {
        display: flex;
        color: #7263e1;
        font-size: 16px;
        align-items: center;
        border-bottom: 1px solid #d3d7de;
        padding: 5px 10px;
        font-weight: bold;
        .img-icon {
          width: 20px;
          height: 20px;
          margin-right: 7px;
        }
      }
      .bottom-info {
        padding: 10px;
        time {
          margin-bottom: 4px;
        }
        time,
        p {
          display: flex;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          width: 100%;
          line-height: 18px;
          color: rgba(0, 0, 0, 0.8);
          font-size: 12px;
          margin-bottom: 4px;
          span {
            flex: 1;
          }
          .iconfont {
            margin-right: 3px;
            font-size: 12px;
            color: #888;
          }
        }
      }
    }
  }
  /deep/ .swiper-container-3d {
    .swiper-slide-shadow-left {
      background-image: linear-gradient(
        to left,
        rgba(255, 255, 255, 1),
        rgba(255, 255, 255, 0)
      );
    }
    .swiper-slide-shadow-right {
      background-image: linear-gradient(
        to right,
        rgba(255, 255, 255, 1),
        rgba(255, 255, 255, 0)
      );
    }
  }
  .swiper-button-prev,
  .swiper-button-next {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    margin-top: -15px;
    .iconfont {
      color: #fff;
      font-size: 18px;
    }
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    &:active {
      background: rgba(0, 0, 0, 1);
    }
  }
  .swiper-button-prev {
    transform: rotate(180deg);
    left: 12px;
  }
  .swiper-button-next {
    right: 12px;
  }
}
</style>
