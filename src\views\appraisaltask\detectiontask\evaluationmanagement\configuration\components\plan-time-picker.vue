<template>
  <div class="plan-time-picker">
    <div class="tag-view-wrap mb-md">
      <tagView
        ref="tagView"
        :default-active="defaultCurTag"
        :list="tabs"
        :no-active="tabList.length === 0"
        @tagChange="tagChange"
      />
    </div>
    <Form ref="formValidate" class="picker-wrap" :model="formValidate" :rules="ruleValidate" :label-width="0">
      <FormItem label="">
        <div class="picker-wrap">
          <FormItem label="" prop="cronData">
            <div :class="[1, 2].includes(currentTag) ? 'picker-wrap-left' : 'full-width'">
              <Select
                v-model="formValidate.cronData"
                class="full-width"
                :max-tag-count="[1, 2].includes(currentTag) ? 2 : 5"
                multiple
                @on-change="handleChangeSelect"
              >
                <Option v-for="item in typeList" :value="item.value" :key="`'cronType'${currentTag}${item.value}`">
                  {{ item.label }}
                </Option>
              </Select>
            </div>
          </FormItem>
          <FormItem
            v-if="[1, 2].includes(currentTag)"
            class="ml-md"
            label=""
            prop="timePoints"
            :rules="[{ required: true, message: '请选择', type: 'array', trigger: 'change' }]"
          >
            <div class="picker-wrap-right">
              <Select
                v-model="formValidate.timePoints"
                class="full-width"
                :max-tag-count="2"
                multiple
                @on-change="handleChangeSelect"
              >
                <Option v-for="item in dayData" :value="item.value" :key="item.value">
                  {{ item.label }}
                </Option>
              </Select>
            </div>
          </FormItem>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'plan-time-picker',
  props: {
    timePickerData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tabList: [
        { text: '每天', type: 'day' },
        { text: '每周', type: 'week' },
        { text: '每月', type: 'month' },
      ],
      tabs: [],
      currentTag: 0,
      defaultCurTag: 0,
      dayData: [],
      weekData: [
        { label: '周一', value: 1 },
        { label: '周二', value: 2 },
        { label: '周三', value: 3 },
        { label: '周四', value: 4 },
        { label: '周五', value: 5 },
        { label: '周六', value: 6 },
        { label: '周日', value: 7 },
      ],
      monthData: [],
      typeList: [],
      formValidate: {
        cronData: [],
        timePoints: [],
      },
      ruleValidate: {
        cronData: [{ required: true, message: '请选择', type: 'array', trigger: 'change' }],
      },
      cronType: '1', // 1:每天 2:每周 3:每月 4:每分 5:每小时
    };
  },
  created() {
    this.getDayData();
    this.getMonthData();
    this.tabs = this.tabList.map((item) => item.text);
    this.typeList = this.getTypeData();
  },
  methods: {
    tagChange(index) {
      this.currentTag = index;
      this.cronType = index + 1;
      this.formValidate.cronData = [];
      this.typeList = this.getTypeData();
    },
    getDayData() {
      this.dayData = [];
      for (let i = 0; i <= 24; i++) {
        let obj = {
          value: i,
        };
        obj.label = i < 10 ? `0${i}:00` : `${i}:00`;
        this.dayData.push(obj);
      }
    },
    getMonthData() {
      // 获取当前天数
      const date = new Date();
      const year = date.getFullYear();
      const month = date.getMonth();
      const days = new Date(year, month + 1, 0).getDate();
      // 数据格式化
      this.monthData = [];
      for (let i = 1; i <= days; i++)
        this.monthData.push({
          label: `${i}号`,
          value: i,
        });
    },
    getTypeData() {
      const type = this.tabList[this.currentTag] ? this.tabList[this.currentTag].type : '';
      return this[`${type}Data`];
    },
    handleChangeSelect() {
      this.updatePlan();
    },
    async handleValid() {
      return await this.$refs.formValidate.validate();
    },
    updatePlan() {
      this.$emit('handleUpatePlanData', { cronType: this.cronType, ...this.formValidate });
    },
  },
  watch: {
    timePickerData: {
      handler(val) {
        if (!val) return false;
        this.defaultCurTag = val.cronType - 1 || 0;
        this.currentTag = val.cronType - 1 || 0;
        this.cronType = val.cronType || 1;
        this.formValidate.cronData = val.cronData || [];
        this.formValidate.timePoints = val.timePoints || [];
        this.typeList = this.getTypeData();
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    TagView: require('@/components/tag-view').default,
  },
};
</script>

<style lang="less" scoped>
.plan-time-picker {
  display: flex;
  flex-direction: column;
  width: 100%;

  .picker-wrap {
    display: flex;

    &-left,
    &-right {
      width: 100%;
    }

    .full-width {
      width: 100%;
    }
  }
}

@{_deep} {
  .tag li {
    padding: 0 15px;
    font-size: 14px;
  }
}
</style>
