<template>
  <div class="cascade-list auto-fill">
    <Button type="primary" class="ml-sm" @click="addToken">
      <!-- <i class="iconfont icon-piliangshanchu f-12"></i> -->
      <span class="vt-middle ml-sm">新增token</span>
    </Button>
    <ui-table class="ui-table auto-fill" :loading="loading" :table-columns="tableColumns" :table-data="tableData">
      <template #status="{ row }">
        <i-switch v-model="row.status" @on-change="changeOneSwitch(row)" />
      </template>
      <template slot="action" slot-scope="{ row }">
        <ui-btn-tip
          class="operatbtn"
          v-for="(item, index) in operateList"
          :key="index"
          :icon="item.icon"
          :content="item.content"
          @click.native="item.funcName(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
    <add-token v-model="addTokenShow"></add-token>
  </div>
</template>
<script>
import cascadeList from '@/config/api/cascadeList';
export default {
  props: {},
  data() {
    return {
      addTokenShow: false,
      loading: false,
      operateList: [
        {
          icon: 'icon-yichu1',
          content: '移除',
          funcName: (row) => this.handleRemove(row),
        },
      ],
      tableColumns: [
        // { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center' },
        { title: '组织机构', key: 'orgCode', align: 'center' },
        { title: '行政区划', key: 'regionCode', align: 'center' },
        { title: '处理状态', slot: 'status', align: 'center' },
        { title: 'token', key: 'token', align: 'center' },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
          width: 80,
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.initList();
  },
  methods: {
    addToken() {
      this.addTokenShow = true;
    },
    changeOneSwitch(row) {
      this.updateToken(row);
    },
    async handleRemove(row) {
      try {
        await this.$http.delete(`${cascadeList.removeToken}`, { ids: row.id });
        this.initList();
      } catch (error) {
        console.log(error);
      }
    },
    async updateToken() {
      try {
        await this.$http.put(cascadeList.updateToken, {
          regionCode: 370100,
        });
        this.initList();
      } catch (error) {
        console.log(error);
      }
    },
    async initList() {
      this.loading = true;
      try {
        let { data } = await this.$http.post(cascadeList.getTokenPageList, {
          pageNumber: 1,
          pageSize: 20,
          regionCode: 370100,
        });
        this.tableData = data.data.entities;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddToken: require('./components/add-token.vue').default,
  },
};
</script>
<style lang="less" scoped>
.cascade-list {
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
}
.table-section {
}
</style>
