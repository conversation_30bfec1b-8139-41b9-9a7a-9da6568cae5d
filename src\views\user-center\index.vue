<template>
  <div class="personalCenterWrap">
    <div class="title-text">
      <div class="title-text-en">WELCOME</div>
      <div class="title-text-cn">{{ userInfo.name }}，欢迎您！</div>
    </div>
    <div class="user-info-wrap">
      <div class="user-Info">
        <div class="user-Info-left">
          <div class="userImg">
            <img v-if="userInfo.avatar" :src="userInfo.avatar" style="object-fit: cover" :onerror="errorDefaultImg" />
            <img v-else :src="defaultImg" alt="" />
          </div>
          <div>
            <Button @click="editUser" type="primary">修改信息</Button>
            <Button @click="changePassword" type="primary">修改密码</Button>
          </div>
        </div>
        <div class="user-Info-right">
          <div class="info-item">
            <div>
              <span class="name-cn">姓名</span>
              <span class="name-zn">Name</span>
            </div>
            <div class="userName">{{ userInfo.name }}</div>
          </div>
          <div class="info-item item-sec">
            <div>
              <div>
                <span class="name-cn">账号</span>
                <span class="name-zn">User&nbsp;Name</span>
              </div>
              <div class="info-item-des">{{ userInfo.username }}</div>
            </div>
            <div>
              <div>
                <span class="name-cn">警号</span>
                <span class="name-zn">Badges </span>
              </div>
              <div class="info-item-des">{{ userInfo.workCode }}</div>
            </div>
            <div>
              <div>
                <span class="name-cn">性别</span>
                <span class="name-zn">Gender</span>
              </div>
              <div class="info-item-des">
                {{ userInfo.sex === '0' ? '男' : '女' }}
              </div>
            </div>
          </div>
          <div class="info-item">
            <div>
              <span class="name-cn">角色</span>
              <span class="name-zn">Role</span>
            </div>
            <div class="info-item-des">
              <template v-for="(e, i) in userInfo.roleVoList">
                <span :key="e.id">
                  <span>{{ e.roleName }}</span>
                  <Divider type="vertical" v-if="i !== userInfo.roleVoList.length - 1" />
                </span>
              </template>
            </div>
          </div>
          <div class="info-item">
            <div>
              <span class="name-cn">组织</span>
              <span class="name-zn">Organization</span>
            </div>
            <div class="info-item-des">
              <template v-for="(e, i) in userInfo.orgVoList">
                <span :key="e.id">
                  <span>{{ e.orgName }}</span>
                  <Divider type="vertical" v-if="i !== userInfo.orgVoList.length - 1" />
                </span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <mdf-password v-model="passwordModal"></mdf-password>
    <user-form v-model="userModalVisible" :userInfo="userInfo"></user-form>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'home',
  data() {
    return {
      passwordModal: false,
      userModalVisible: false,
      errorDefaultImg: 'this.src="' + require('@/assets/img/user-center/default_user_avatar.png') + '"',
      defaultImg: require('@/assets/img/user-center/default_user_avatar.png'),
    };
  },
  components: {
    MdfPassword: require('./mdf-password').default,
    UserForm: require('./user-form/index').default,
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/getUserInfo',
      themeType: 'common/getThemeType',
    }),
  },
  methods: {
    changePassword() {
      this.passwordModal = true;
    },
    editUser() {
      this.userModalVisible = true;
    },
  },
  created() {
    if (this.themeType === 'deepBlue') {
      this.errorDefaultImg = 'this.src="' + require('@/assets/img/user-center/default_user_avatar_deepBlue.png') + '"';
      this.defaultImg = require('@/assets/img/user-center/default_user_avatar_deepBlue.png');
    }
  },
};
</script>

<style lang="less" scoped>
@import './index';
</style>
