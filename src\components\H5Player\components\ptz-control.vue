<template>
  <div class="ptz-control">
    <Modal
      ref="ptzModal"
      v-model="isShow"
      draggable
      sticky
      :z-index="10000"
      :mask="false"
      title="云台控制"
      footer-hide
      width="300"
      :styles="{ top: 0, width: 'auto' }"
    >
      <Tabs v-model="currentTab" type="card">
        <TabPane label="云台控制" name="tab1"></TabPane>
        <TabPane label="预置位" name="tab2"></TabPane>
        <TabPane label="巡航" name="tab3"></TabPane>
      </Tabs>
      <div style="height: 280px">
        <template v-if="currentTab == 'tab1'">
          <div class="direction">
            <div class="direction-l">
              <div class="circle1"></div>
              <div class="circle2"></div>
              <div class="circle3"></div>
              <ul class="pie">
                <li
                  class="slice-one slice"
                  @mousedown="ptzMousedown('down')"
                  @mouseup="ptzMouseup('down')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-two slice"
                  @mousedown="ptzMousedown('downleft')"
                  @mouseup="ptzMouseup('downleft')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-three slice"
                  @mousedown="ptzMousedown('left')"
                  @mouseup="ptzMouseup('left')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-four slice"
                  @mousedown="ptzMousedown('upleft')"
                  @mouseup="ptzMouseup('upleft')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-five slice"
                  @mousedown="ptzMousedown('up')"
                  @mouseup="ptzMouseup('up')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-six slice"
                  @mousedown="ptzMousedown('upright')"
                  @mouseup="ptzMouseup('upright')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-seven slice"
                  @mousedown="ptzMousedown('right')"
                  @mouseup="ptzMouseup('right')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
                <li
                  class="slice-eight slice"
                  @mousedown="ptzMousedown('downright')"
                  @mouseup="ptzMouseup('downright')"
                >
                  <i class="iconfont icon-yuntaijiantou"></i>
                </li>
              </ul>
            </div>
            <div class="direction-r">
              <div class="direction-r-item">
                <span
                  class="mybutton"
                  @mousedown="ptzMousedown('in')"
                  @mouseup="ptzMouseup('in')"
                  >+</span
                >
                <span>变倍</span>
                <span
                  class="mybutton"
                  @mousedown="ptzMousedown('out')"
                  @mouseup="ptzMouseup('out')"
                  >-</span
                >
              </div>
              <div class="direction-r-item">
                <span
                  class="mybutton"
                  @mousedown="ptzMousedown('far')"
                  @mouseup="ptzMouseup('far')"
                  >+</span
                >
                <span>变焦</span>
                <span
                  class="mybutton"
                  @mousedown="ptzMousedown('near')"
                  @mouseup="ptzMouseup('near')"
                  >-</span
                >
              </div>
              <div class="direction-r-item">
                <span
                  class="mybutton"
                  @mousedown="ptzMousedown('open')"
                  @mouseup="ptzMouseup('open')"
                  >+</span
                >
                <span>光圈</span>
                <span
                  class="mybutton"
                  @mousedown="ptzMousedown('close')"
                  @mouseup="ptzMouseup('close')"
                  >-</span
                >
              </div>
            </div>
          </div>
          <div class="speed">
            <div class="label">控制速度：</div>
            <div class="speed-item">
              <el-slider v-model="speed" :min="1" :max="255"></el-slider>
              <span>{{ speed }}</span>
            </div>
          </div>
          <div class="cruise">
            <div class="label">预置位：</div>
            <div class="cruise-buttons">
              <!-- <ButtonGroup>
                <Button type="primary" size="small" @click="ptzMousedown('cruise-start')">开始</Button>
                <Button size="small" @click="ptzMousedown('cruise-stop')">停止</Button>
              </ButtonGroup> -->
              <Button type="primary" size="small" @click="currentTab = 'tab2'"
                >预置位</Button
              >
            </div>
          </div>
        </template>

        <template v-if="currentTab == 'tab2'">
          <div class="head">
            <span class="preset-index">编号</span>
            <span class="preset-img">图片</span>
            <span class="preset-name">&nbsp;&nbsp;&nbsp;&nbsp;标题</span>
            <span class="preset-do">操作</span>
          </div>
          <div v-scroll class="preset-content">
            <ul class="preset-list">
              <li v-for="(item, index) in presetsList" :key="index">
                <span class="preset-index num">{{ item.sortNo }}</span>
                <span class="preset-img"
                  ><img :src="item.imageUrl" v-viewer
                /></span>
                <span class="preset-name" :title="item.name">{{
                  item.name
                }}</span>
                <span class="preset-do">
                  <span
                    class="preset-use"
                    title="预置位"
                    @click="presetUse(item)"
                  ></span>
                  <span
                    class="preset-delete"
                    title="删除"
                    @click="presetDelete(item)"
                  ></span>
                </span>
              </li>
            </ul>
          </div>
        </template>

        <template v-if="currentTab == 'tab3'">
          <div class="auto-content" v-if="isShowCruiseLists">
            <div class="auto-title" v-if="cruisesList.length > 0">
              <span class="l-index">编号</span>
              <span class="l-name">名称</span>
              <span class="l-controller">操作</span>
            </div>
            <div
              v-scroll
              class="show-cruise-list"
              v-if="cruisesList.length > 0"
            >
              <ul>
                <li
                  class="auto-list common-list"
                  v-for="(item, index) in cruisesList"
                  :key="index"
                >
                  <span class="l-index">{{ index + 1 }}</span>
                  <span class="l-name too-long" :title="item.name">{{
                    item.name
                  }}</span>
                  <span class="l-controller">
                    <i-switch
                      v-model="item.status"
                      @on-change="switchCruise(item)"
                    />
                    <i class="common-icon edit" @click="editCruise(item)"></i>
                    <i class="common-icon del" @click="delCruise(item)"></i>
                  </span>
                </li>
              </ul>
            </div>
            <span v-else class="no-data">暂无巡航数据</span>
          </div>
          <Button
            class="add-button"
            type="primary"
            size="small"
            v-if="isShowCruiseLists"
            @click="showAddCruise"
            >新增巡航</Button
          >
          <div class="add-content add-auto" v-if="isShowAddCruise">
            <div class="n-title clearfix">
              <span class="go-back" @click="goBack">返回</span>
              <span
                class="add-new"
                title="新增自动巡航预置位"
                @click="addCruiseList"
              ></span>
            </div>
            <div class="n-content">
              <div class="auto-title common-title">
                <span class="l-index">预置位标题</span>
                <span class="l-name">停留时间(秒)</span>
                <span class="l-controller">操作</span>
              </div>
              <div v-scroll class="auto-ul">
                <ul>
                  <li
                    class="auto-list"
                    v-for="(item, index) in mockList"
                    :key="index"
                  >
                    <!-- 选择已有预置位 -->
                    <div class="pre-name">
                      <Select
                        v-model="item.presetId"
                        size="small"
                        filterable
                        class="width-md"
                      >
                        <Option
                          v-for="item in selectOptions"
                          :value="item.value"
                          :key="item.value"
                          placeholder="请选择"
                          >{{ item.text }}</Option
                        >
                      </Select>
                    </div>
                    <div class="pre-time">
                      <Input
                        v-model="item.internalTime"
                        size="small"
                        placeholder="请输入"
                      ></Input>
                    </div>
                    <div class="pre-controller">
                      <i
                        class="common-icon up"
                        title="上移"
                        v-if="index !== 0"
                        @click="moveUp(index, item)"
                      ></i>
                      <i
                        class="common-icon down"
                        title="下移"
                        v-if="index !== mockList.length - 1"
                        @click="moveDown(index, item)"
                      ></i>
                      <i
                        class="common-icon del"
                        title="删除"
                        @click="deleteLi(index)"
                      ></i>
                    </div>
                  </li>
                </ul>
              </div>
              <div class="new-cruise-info">
                <div class="title-name">
                  <span class="cruise-title">名&nbsp;&nbsp;&nbsp;称:</span>
                  <Input
                    v-model="cruiseName"
                    class="cruiseName"
                    size="small"
                    placeholder="请输入巡航名称"
                  ></Input>
                </div>
                <div class="auto-point">
                  <span class="after-down">回 位 点:</span>
                  <Select
                    v-model="homingPresetId"
                    size="small"
                    filterable
                    @on-change="changePreset"
                    class="preset-select"
                    clearable
                  >
                    <Option
                      v-for="item in selectOptions"
                      :value="item.value"
                      :key="item.value"
                      placeholder="请选择"
                      >{{ item.text }}</Option
                    >
                  </Select>
                </div>
                <div class="save-or-cancel">
                  <Button
                    type="primary"
                    size="small"
                    style="margin-right: 10px"
                    @click="saveCruise"
                    >保存</Button
                  >
                  <Button size="small" @click="cancelCruise">取消</Button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </Modal>
  </div>
</template>
<script>
import { Modal } from "view-design";
import {
  createPreset,
  getPresets,
  callPreset,
  deletePreset,
  hasCruiseByPresetId,
  getCruiseByCameraId,
  updateCruise,
  addCruise,
  deleteCruise,
  switchOnOffCruise,
  setPtzLock,
  setPtzUnlock,
} from "@/api/player.js";
import EventBus from "@/util/eventBus.js";
export default {
  props: {
    ...Modal.props,
    videoObj: {
      type: Object,
      default: () => {},
    },
    winList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isShow: false,
      isFirstOpen: true,
      speed: 100,
      currentTab: "tab1",
      presetsList: [],
      isShowCruiseLists: true,
      isShowAddCruise: false,
      cruisesList: [],
      homingPresetId: null,
      mockList: [
        {
          presetId: "",
          internalTime: 5,
        },
      ],
      cruiseName: "",
      cruiseId: "",
      selectOptions: [],
    };
  },
  watch: {
    isShow(val) {
      this.$emit("input", val);
    },
    speed(val) {
      localStorage.setItem("ptzSpeed", val);
      if (this.videoObj)
        this.videoObj.windowPtzSpeed = Math.ceil((val / 255) * 15);
    },
    value: {
      handler(val) {
        this.isShow = val;
        this.currentTab = "tab1";
        if (val && this.isFirstOpen) {
          this.isFirstOpen = false;
          this.$nextTick(() => {
            let playerDom =
              document.querySelector(".video-play") ||
              document.querySelector(".video-div");
            let dragDom = this.$refs.ptzModal.$el.querySelector(
              ".ivu-modal-content-drag"
            );
            let left = playerDom.offsetLeft;
            let top =
              playerDom.offsetTop +
              playerDom.offsetHeight -
              dragDom.offsetHeight;
            if (top < 0) top = 0;
            dragDom.style.left = left + "px";
            dragDom.style.top = top + "px";
          });
        }
      },
      immediate: true,
    },
    currentTab(tab) {
      //如果是预置位面板，则需要加载预置位列表
      if (tab === "tab2") {
        this.showPreset();
      }
      //如果是计划巡航面板，则需要加载计划巡航列表
      if (tab === "tab3") {
        this.isShowCruiseLists = true;
        this.isShowAddCruise = false;
        this.$nextTick(() => {
          this.showCruise();
        });
      }
    },
  },
  beforeDestroy() {
    this.isShow = false;
  },
  deactivated() {
    this.isShow = false;
  },
  mounted() {
    let localSpeed = localStorage.getItem("ptzSpeed");
    this.speed = localSpeed ? parseInt(localSpeed) : 100;
    EventBus.$on("createdPreset", () => {
      // 添加预置位后刷新列表
      if (this.currentTab === "tab2") {
        this.showPreset();
      }
    });
  },
  methods: {
    ptzMousedown(cmd) {
      if (liveType == "pvg67" || liveType == "pvgplus") {
        let pvgCmd = this.getPvgCmd(cmd);
        let pvgSpeed = this.getPvgSpeed(cmd);
        this.videoObj.ptzControl(this.videoObj.focusIndex, pvgCmd, pvgSpeed);
      } else {
        this.$emit("setPtzControl", cmd, this.speed);
      }
    },
    ptzMouseup(cmd) {
      if (liveType == "pvg67" || liveType == "pvgplus") {
        let pvgCmd = this.getPvgCmd(cmd);
        this.videoObj.ptzControl(this.videoObj.focusIndex, pvgCmd, 0);
      } else {
        this.$emit("setPtzControl", "stop", this.speed);
      }
    },
    getPvgCmd(cmd) {
      let obj = {
        right: 0,
        upright: 1,
        up: 2,
        upleft: 3,
        left: 4,
        downleft: 5,
        down: 6,
        downright: 7,
        in: 12,
        out: 12,
        far: 11,
        near: 11,
        open: 10,
        close: 10,
        "preset-goto": 13,
      };
      let item = obj[cmd];
      return item ? item : cmd;
    },
    getPvgSpeed(cmd) {
      let speed = Math.ceil((this.speed / 255) * 15);
      if (cmd == "out" || cmd == "near" || cmd == "close") {
        speed = 0 - speed;
      }
      if (cmd == "preset-goto") speed = 1; // 预置位ID
      return speed;
    },
    showPreset: function () {
      var self = this;
      let index = this.videoObj.focusIndex;
      let current = this.winList[index];
      if (!current || !current.stream) {
        return;
      }
      getPresets({ gbId: current.deviceGbId }).then((res) => {
        self.presetsList = res.data;
      });
    },
    presetUse: function (item) {
      callPreset(item.id).then((res) => {
        if (res.data) {
          this.$Message.success("调用预置位成功");
        } else {
          this.$Message.error("调用预置位失败");
        }
      });
    },
    presetDelete: function (item) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定要删除该预置位吗？`,
        onOk: () => {
          hasCruiseByPresetId(item.id).then((res) => {
            if (res.data) {
              this.$Message.error(
                "该预置位存在于巡航中，请先删除巡航再删除此预置位！"
              );
            } else {
              deletePreset(item.id).then((res) => {
                if (res.code == 200) {
                  this.$Message.success("删除预置位成功！");
                  // this.videoPresetRefresh()
                  this.showPreset();
                } else {
                  this.$Message.error("删除预置位失败！");
                }
              });
            }
          });
        },
      });
    },
    showCruise: function () {
      let index = this.videoObj.focusIndex;
      let current = this.winList[index];
      if (!current || !current.stream) {
        return;
      }
      getCruiseByCameraId(current.deviceGbId).then((res) => {
        if (res.data.length > 0) {
          this.cruisesList = res.data.map((item) => {
            item.status = !!item.status;
            return item;
          });
        } else {
          this.cruisesList = [];
        }
      });
    },
    showAddCruise: function () {
      var self = this;
      let index = this.videoObj.focusIndex;
      let current = this.winList[index];
      if (!current || !current.stream) {
        return;
      }
      self.cruiseId = "";
      //首先查询预置位列表，填充selectOptions.data数据
      getPresets({ gbId: current.deviceGbId }).then((res) => {
        self.presetsList = res.data;
        self.selectOptions = self.presetsList.map((item) => {
          return {
            text: item.name,
            value: item.id,
          };
        });
        //切换新增面板
        self.isShowAddCruise = true;
        self.isShowCruiseLists = false;
        //清空数据
        self.mockList = [];
        self.homingPresetId = null;
        self.cruiseName = "";
      });
    },
    addCruiseList: function () {
      var data = {
        presetId: null,
        internalTime: 5,
      };
      this.mockList.push(data);
    },
    changePreset: function (v) {
      this.homingPresetId = v;
    },
    saveCruise: function () {
      var self = this;
      let index = this.videoObj.focusIndex;
      let current = this.winList[index];
      if (!current || !current.stream) {
        return;
      }
      this.mockList.map((item, pos) => {
        item.sortNo = pos;
      });
      if (!self.cruiseName) {
        this.$Message.warning("请输入巡航名称");
        return;
      }
      if (self.cruiseName.length > 20) {
        this.$Message.warning("巡航名称不能超过20个字符");
        return;
      }
      var param = {
        cameraId: current.deviceGbId,
        channelNo: current.stream.channel,
        cprList: self.mockList,
        homingPresetId: self.homingPresetId, //回归点的预置位id
        id: self.cruiseId ? self.cruiseId : "",
        name: self.cruiseName,
        status: self.cruiseId ? +self.status : 0,
      };
      console.log("param", param);
      //编辑
      if (self.cruiseId) {
        updateCruise(param).then((res) => {
          if (res.code == 200) {
            this.$Message.success("编辑自动巡航成功！");
            self.cruiseId = "";
            //切换新增面板
            self.isShowAddCruise = false;
            self.isShowCruiseLists = true;
            self.showCruise();
          } else {
            this.$Message.warning("编辑自动巡航失败！");
          }
        });
      } else {
        addCruise(param).then((res) => {
          if (res.code == 200) {
            this.$Message.success("新增自动巡航成功！");
            self.cruiseId = "";
            //切换新增面板
            self.isShowAddCruise = false;
            self.isShowCruiseLists = true;
            self.showCruise();
          } else {
            this.$Message.warning("新增自动巡航失败！");
          }
        });
      }
    },
    cancelCruise: function () {
      //切换新增面板
      this.isShowAddCruise = false;
      this.isShowCruiseLists = true;
    },
    moveUp: function (index, item) {
      var pre = this.mockList[index - 1];
      this.mockList.splice(index - 1, 2, item, pre);
    },
    moveDown: function (index, item) {
      var next = this.mockList[index + 1];
      this.mockList.splice(index, 2, next, item);
    },
    deleteLi: function (index) {
      this.mockList.splice(index, 1);
    },
    goBack: function () {
      //切换新增面板
      this.isShowAddCruise = false;
      this.isShowCruiseLists = true;
    },
    editCruise: function (item) {
      var self = this;
      let index = this.videoObj.focusIndex;
      let current = this.winList[index];
      if (!current || !current.stream) {
        return;
      }
      //#97994
      if (item.status) {
        this.$Message.warning("正在巡航，请先关闭巡航，然后编辑");
        return;
      }
      //debugger
      //首先查询预置位列表，填充selectOptions.data数据
      if (self.selectOptions.length === 0) {
        getPresets({ gbId: current.deviceGbId }).then((res) => {
          self.presetsList = res.data;
          self.selectOptions = self.presetsList.map((item) => {
            return {
              text: item.name,
              value: item.id,
            };
          });
        });
      }
      self.cruiseName = item.name;
      self.homingPresetId = item.homingPresetId;
      self.mockList = IX.clone(item.cprList);
      self.cruiseId = item.id;
      self.status = item.status;
      //切换新增面板
      self.isShowAddCruise = true;
      self.isShowCruiseLists = false;
    },
    delCruise: function (item) {
      //没有在巡航
      if (!item.status) {
        this.$Modal.confirm({
          title: "提示",
          width: 450,
          closable: true,
          content: `确定要删除该巡航吗？`,
          onOk: () => {
            deleteCruise(item.id).then((res) => {
              this.$Message.success("删除巡航成功");
              this.showCruise();
            });
          },
        });
      } else {
        this.$Message.warning("正在巡航中，清先关闭巡航，然后删除");
      }
    },
    switchCruise: async function (item) {
      let isStatusItem = null;
      this.cruisesList.forEach((i) => {
        if (i.status && i.id != item.id) {
          i.status = false;
          isStatusItem = i.id;
        }
      });
      var param = {
        id: item.id,
        status: +item.status,
      };
      let index = this.videoObj.focusIndex;
      let current = this.winList[index];
      if (!current || !current.stream) {
        return;
      }
      if (isStatusItem) {
        await switchOnOffCruise({
          id: isStatusItem,
          status: 0,
        });
      }
      await switchOnOffCruise(param).then((res) => {
        this.$Message.success("修改巡航状态成功");
        // 判断有没有开启的任务，如果有就锁定，没有就不锁
        let hasTask = this.cruisesList.some((i) => i.status);
        let deviceId = current.deviceId || current.deviceGbId;
        if (hasTask) {
          setPtzLock({ deviceId }).then((res) => {
            this.videoObj.showOrHideToolbar(index, true, "ptzUnlock");
            this.videoObj.showOrHideToolbar(index, false, "ptzLock");
          });
        } else {
          setPtzUnlock({ deviceId }).then((res) => {
            this.videoObj.showOrHideToolbar(index, false, "ptzUnlock");
            this.videoObj.showOrHideToolbar(index, true, "ptzLock");
          });
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/.ivu-tabs-bar {
  margin-bottom: 0;
}
/deep/.ivu-input-small {
  height: 24px !important;
}
/deep/.ivu-select-selection > div {
  overflow-y: hidden;
}
.direction {
  display: flex;
  margin: 10px 20px;
  .direction-l {
    height: 130px;
    width: 130px;
    margin-right: 16px;
    background: linear-gradient(169deg, #dbe8ff 0%, #dcf2ff 99%);
    border-radius: 50%;
    position: relative;
    .circle1,
    .circle2,
    .circle3 {
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .circle1 {
      width: 120px;
      height: 120px;
      background: linear-gradient(180deg, #ffffff 0%, #eeeeee 100%);
    }
    .circle2 {
      width: 112px;
      height: 112px;
      background: linear-gradient(180deg, #eeeeee 0%, #ffffff 100%);
    }
    .circle3 {
      width: 48px;
      height: 48px;
      background: linear-gradient(180deg, #eaeaea 3%, #ffffff 100%);
      box-shadow: 0px 1px 10px 0px #f1f1f1,
        inset 0px 1px 2px 0px rgba(255, 255, 255, 0.79);
    }
    .pie {
      width: 20px;
      height: 20px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      justify-content: center;
      align-items: center;
      .slice {
        position: absolute;
        i {
          transform: rotateZ(180deg);
          display: block;
          cursor: pointer;
          color: #cecfd0;
          font-size: 12px;
          &:hover {
            color: #2c86f8;
          }
        }
      }
      .slice-one {
        transform: rotateZ(0deg) translateY(42px);
      }

      .slice-two {
        transform: rotateZ(45deg) translateY(42px);
      }

      .slice-three {
        transform: rotateZ(90deg) translateY(42px);
      }

      .slice-four {
        transform: rotateZ(135deg) translateY(42px);
      }

      .slice-five {
        transform: rotateZ(180deg) translateY(42px);
      }

      .slice-six {
        transform: rotateZ(225deg) translateY(42px);
      }

      .slice-seven {
        transform: rotateZ(270deg) translateY(42px);
      }

      .slice-eight {
        transform: rotateZ(315deg) translateY(42px);
      }
    }
  }
  .direction-r {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .direction-r-item {
      display: flex;
      justify-content: space-around;
    }
  }
  .mybutton {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    opacity: 1;
    border: 1px solid #2c86f8;
    cursor: pointer;
    text-align: center;
    color: #2c86f8;
    font-size: 14px;
    font-weight: 700;
    &:hover {
      background-color: #2c86f8;
      color: #fff;
    }
  }
}
.speed {
  overflow: hidden !important;
  margin: 0 20px;
  .label {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.6);
    line-height: 28px;
  }
  .speed-item {
    display: flex;
    align-items: center;
    .el-slider {
      flex: 1;
      margin-right: 10px;
    }
  }
}
.cruise {
  margin: 0 20px;
  .label {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.6);
    line-height: 28px;
  }
  .cruise-buttons {
    display: flex;
    justify-content: space-between;
  }
}
/deep/.ivu-modal {
  .ivu-modal-close {
    top: 0;
  }
  .ivu-modal-header {
    padding: 8px 15px;
    .ivu-modal-header-inner {
      font-weight: 700;
      color: rgba(0, 0, 0, 0.8);
      &::before {
        content: "";
        height: 100%;
        width: 3px;
        display: inline-block;
        background-color: #2c86f8;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
  .ivu-modal-body {
    padding: 0;
  }
}
.head {
  height: 32px;
  line-height: 32px;
  text-align: center;
  *width: 260px;
  *padding-left: 0;
  *text-align: left;
  *font-size: 0;
  span {
    display: inline-block;
    color: #666666;
    text-align: center;
    *display: inline;
    zoom: 1;
    *font-size: 12px;
    &.preset-index {
      width: 40px;
    }
    &.preset-img {
      width: 64px;
    }
    &.preset-name {
      width: 90px;
      text-align: left;
      *text-align: center;
    }
    &.preset-do {
      width: 50px;
      text-align: left;
      *text-align: right;
      padding-left: 0;
    }
  }
}
.preset-content {
  height: 245px;
  border-top: solid 1px rgba(167, 172, 184, 0.3);
  ul {
    margin: 10px 0 0 0;
    padding: 0;
    border: solid 0 red;
    li {
      height: 54px;
      line-height: 54px;
      border-bottom: solid 1px rgba(167, 172, 184, 0.3);
      white-space: nowrap;
      span {
        vertical-align: middle;
        display: inline-block;
        border: solid 0 red;
        text-align: center;
        &.preset-index {
          width: 40px;
          color: #666666;
        }
        &.preset-img {
          width: 64px;
          height: 50px;
          position: relative;
          top: 5px;
        }
        &.preset-img img {
          vertical-align: middle;
          padding: 0;
          margin: -27px 0 0 0;
          width: 64px;
          height: 50px;
        }
        &.preset-name {
          width: 80px;
          max-width: 80px;
          margin: 0px 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &.preset-do {
          width: 50px;
        }
        &.preset-use {
          width: 18px;
          height: 18px;
          background: url("~@/assets/img/player/preset-use.png") no-repeat 0 0;
          cursor: pointer;
        }
        &.preset-use:hover {
          background-position: -22px 0;
        }
        &.preset-delete {
          width: 18px;
          height: 18px;
          background: url("~@/assets/img/player/preset-delete.png") no-repeat 0
            0;
          cursor: pointer;
        }
        &.preset-delete:hover {
          background-position: -22px 0;
        }
      }
    }
  }
}
.add-button {
  position: relative;
  left: 50%;
  transform: translate(-50%);
}
.auto-content {
  height: calc(~"100% - 35px");
  position: relative;
  .auto-title {
    display: inline-block;
    width: 100%;
    height: 30px;
    line-height: 30px;
    white-space: nowrap;
    border-bottom: 1px solid rgba(167, 172, 184, 0.3);
    color: #666666;
    padding: 0 10px;
    span {
      display: inline-block;
      white-space: nowrap;
    }
    .l-index {
      width: 40px;
    }
    .l-name {
      width: calc(~"100% - 130px");
    }
    .l-controller {
      width: 90px;
    }
  }
  .show-cruise-list {
    height: 210px;
    width: 90%;
    margin: auto;
    .auto-list {
      line-height: 30px;
      border-bottom: 1px solid rgba(167, 172, 184, 0.3);
      display: flex;
      width: 100%;
      span {
        display: table-cell;
      }
      .l-index {
        width: 40px;
      }
      .l-name {
        width: calc(~"100% - 130px");
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        display: inline-block;
        vertical-align: middle;
      }
      .l-controller {
        width: 90px;
        display: flex;
        align-items: center;
        .common-icon {
          cursor: pointer;
          display: inline-block;
        }
        .edit {
          position: relative;
          top: 3px;
          height: 16px;
          width: 18px;
          margin-left: 5px;
          margin-right: 5px;
          background: url("~@/assets/img/player/edit.png") no-repeat scroll 0
            center;
          &:hover {
            margin-right: 5px;
            background: url("~@/assets/img/player/edit.png") no-repeat scroll -22px
              center;
          }
        }
        .del {
          position: relative;
          top: 3px;
          height: 16px;
          width: 14px;
          background: url("~@/assets/img/player/del.png") no-repeat scroll 0
            center;
          &:hover {
            background: url("~@/assets/img/player/del.png") no-repeat scroll -22px
              center;
          }
        }
      }
    }
  }
  .no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #666666;
  }
}
.add-content {
  height: 100%;
  .n-title {
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid rgba(167, 172, 184, 0.3);
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .add-new {
      cursor: pointer;
      background: url("~@/assets/img/player/add.png") no-repeat 0 0;
      display: inline-block;
      height: 16px;
      width: 16px;
      margin-top: 7px;
      &:hover {
        background: url("~@/assets/img/player/add.png") no-repeat scroll -21px center;
      }
    }
    .go-back {
      cursor: pointer;
      color: var(--prod-normal-color);
      font-weight: bold;
      background: url("~@/assets/img/player/back.png") no-repeat 0px 10px;
      padding-left: 10px;
    }
  }
  .n-content {
    border-top: 1px solid rgba(167, 172, 184, 0.3);
    height: calc(~"100% - 30px");
    display: flex;
    flex-direction: column;
    .auto-title {
      display: inline-block;
      height: 30px;
      line-height: 30px;
      white-space: nowrap;
      border-bottom: 1px solid rgba(167, 172, 184, 0.3);
      width: 93%;
      margin: 0 10px;
      span {
        display: inline-block;
        white-space: nowrap;
        text-align: center;
      }
      .l-index {
        width: 40%;
      }
      .l-name {
        width: 40%;
      }
      .l-controller {
        width: 20%;
      }
    }
    .auto-ul {
      flex: 1;
      .auto-list {
        display: flex;
        align-items: center;
        justify-content: space-around;
        border-bottom: 1px solid rgba(167, 172, 184, 0.3);
        padding: 2px 5px;
      }
      .pre-name {
        display: inline-block;
        flex: 2;
        text-align: center;
        margin-right: 5px;
        .preset-select {
          width: 100px;
          max-width: 100px;
          background-color: #ffffff;
        }
        .xui-select-inputwrap {
          line-height: normal;
        }
        .xui-single-text {
          position: relative;
          top: 4px;
          max-width: 64px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .select-trigger-icon {
          top: 4px;
        }
      }
      .pre-time {
        display: inline-block;
        flex: 1;
        .xui-input-style {
          width: 40px;
        }
      }
      .pre-controller {
        display: inline-block;
        flex: 1;
        text-align: center;
        .common-icon {
          cursor: pointer;
          display: inline-block;
          margin-left: 4px;
        }
        .up {
          height: 16px;
          width: 14px;
          background: url("~@/assets/img/player/up.png") no-repeat scroll 0
            center;
          &:hover {
            background: url("~@/assets/img/player/up.png") no-repeat scroll -14px
              center;
          }
        }
        .down {
          height: 16px;
          width: 14px;
          background: url("~@/assets/img/player/down.png") no-repeat scroll 0
            center;
          &:hover {
            background: url("~@/assets/img/player/down.png") no-repeat scroll -14px
              center;
          }
        }
        .del {
          height: 16px;
          width: 14px;
          background: url("~@/assets/img/player/del.png") no-repeat scroll 0
            center;
          &:hover {
            background: url("~@/assets/img/player/del.png") no-repeat scroll -22px
              center;
          }
        }
      }
    }
    .new-cruise-info {
      padding-top: 10px;
      border-top: 1px solid rgba(167, 172, 184, 0.3);
      .title-name,
      .auto-point {
        padding-right: 10px;
        display: flex;
        align-items: center;
        span {
          flex: 1;
        }
        div {
          flex: 3;
        }
      }
      .title-name {
        margin-left: 10px;
        .cruise-title {
          &:before {
            content: "*";
            color: rgb(255, 0, 0);
            position: relative;
            top: 2px;
            left: 0;
            margin-right: 3px;
          }
        }
      }
      .auto-point {
        margin-top: 5px;
        margin-left: 10px;
        .preset-select {
          background-color: #ffffff;
          text-align: left;
        }
      }
      .save-or-cancel {
        margin: 10px 0;
        width: 100%;
        display: flex;
        justify-content: center;
      }
      .xui-select-inputwrap {
        line-height: normal;
        display: inline-flex;
        align-items: center;
      }
      .select-trigger-icon {
        top: 5px;
      }
      .xui-single-text {
        max-width: 165px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
