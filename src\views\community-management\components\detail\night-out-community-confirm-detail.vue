<!--
    * @FileDescription: 出入多小区置信详情
    * @Author: H
    * @Date: 2025/4/1
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-09 16:33:07
-->
<template>
  <div class="dom-wrapper" v-if="isShow">
    <div class="big-dom dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon type="md-close" size="14" @click.native="isShow = false" />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="community-box" @click="goToCommunity">
              <img src="@/assets/img/community-management/community-icon.png" />
              {{ communityName || "xxxx小区" }}
            </div>
            <div class="thumbnail">
              <img v-lazy="datailsInfo.traitImg" alt="" />
              <!-- <span class="similarity" v-if="datailsInfo.score"
                >{{ datailsInfo.score }}%</span
              > -->
            </div>
            <div
              class="record-title"
              :style="{
                'justify-content': datailsInfo.vid
                  ? 'space-between'
                  : 'flex-start',
              }"
            >
              <span :class="{ active: checkIndex == 0 }"> 抓拍记录 </span>
            </div>
            <div class="through-record" v-if="checkIndex == 0">
              <div class="wrapper-content">
                <span class="label">抓拍地点</span>：
                <!-- <ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName"></ui-textOver-tips> -->
                <span
                  class="message ellipsis"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  v-html="datailsInfo.deviceName"
                  :title="
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                      : ''
                  "
                ></span>
              </div>
              <div class="wrapper-content">
                <span class="label">抓拍时间</span>：
                <span class="message">{{ datailsInfo.absTime || "--" }}</span>
              </div>
              <div class="line"></div>
              <div class="sub-head">
                <!-- <div class="sub-head-title">审批处理</div> -->
                <div class="history-box">
                  <!-- 处理意见后端接口也没好，暂时隐藏了 -->
                  <!-- <Poptip
                    trigger="hover"
                    transfer
                    word-wrap
                    @on-popper-show="showHistory()"
                  >
                    <i class="iconfont icon-lishijilu"></i
                    ><span style="padding-left: 10px">历史处理</span>
                    <div slot="title">
                      <div class="block"></div>
                      <i>历史处理</i>
                    </div>
                    <div slot="content">
                      <Timeline>
                        <TimelineItem
                          v-for="item in historyList"
                          :key="item.creatorId"
                        >
                          <div class="time">
                            <div class="timeContent">
                              <div>{{ item.handleTime }}</div>
                              <div>操作人：{{ item.creatorName }}</div>
                            </div>
                          </div>
                          <div class="content">
                            <div class="content1">
                              <div class="p">
                                <span>处理操作：</span>
                                <div>
                                  <span
                                    >设为{{
                                      item.operation == 1 ? '"有效"' : '"无效"'
                                    }}</span
                                  >
                                </div>
                              </div>
                              <div class="p">
                                <span>处理意见：</span>
                                <div>{{ item.remark || "--" }}</div>
                              </div>
                            </div>
                          </div>
                        </TimelineItem>
                      </Timeline>
                    </div>
                  </Poptip> -->
                </div>
              </div>
              <div class="comfirm-box">
                <div class="remark-box">
                  <Form
                    ref="remarkFormRef"
                    :model="remarkForm"
                    label-colon
                    v-if="nightOutData.handlerStatus != 1"
                  >
                    <FormItem prop="handlerDetail">
                      <Input
                        v-model="remarkForm.handlerDetail"
                        placeholder="请输入处理意见"
                        type="textarea"
                      />
                    </FormItem>
                    <FormItem prop="handlerStatus" label="处理状态">
                      <RadioGroup v-model="remarkForm.handlerStatus">
                        <Radio label="1">有效</Radio>
                        <Radio label="2">无效</Radio>
                      </RadioGroup>
                    </FormItem>
                  </Form>
                </div>
              </div>
              <div
                class="wrapper-content"
                v-if="nightOutData.handlerStatus == 1"
              >
                <span class="label">处理意见</span>：
                <span class="message">{{
                  nightOutData.handlerDetail || "--"
                }}</span>
              </div>
              <div
                class="wrapper-content"
                v-if="nightOutData.handlerStatus == 1"
              >
                <span class="label">处理状态</span>：
                <span class="message">{{
                  nightOutData.handlerStatus == 1 ? "有效" : "无效"
                }}</span>
              </div>
              <div class="comfirm-btn">
                <div
                  class="select-tag-button"
                  v-if="
                    remarkForm.handlerStatus == 1 &&
                    nightOutData.handlerStatus != 1
                  "
                  @click="setArchiveInfo"
                >
                  填写人员信息（必填）
                </div>
                <div
                  class="select-tag-button"
                  v-if="nightOutData.handlerStatus == 1"
                  @click="detailHandler"
                >
                  查看人员信息
                </div>
              </div>
            </div>
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleTab('left')"
            ></i>
            <details-largeimg
              boxSeleType="rect"
              :acrossAppJump="true"
              :info="datailsInfo"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                // 'sc',
                'hy',
              ]"
              @collection="collection"
              :collectionType="5"
              :algorithmType="1"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleTab('right')"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleTab('left')"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="item.recordId"
            >
              <div class="img-box">
                <img v-lazy="item?.traitImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleTab('right')"
        ></i>
      </footer>
      <div class="footer-btn-box">
        <Button @click="isShow = false">取消</Button>
        <!-- 有效的时候不允许再编辑了 -->
        <Button
          type="primary"
          @click="handleConfirm"
          v-if="nightOutData.handlerStatus != 1"
          >保存</Button
        >
      </div>
    </div>
    <AddModal
      ref="addModal"
      :is-community="true"
      :defaultPlaceId="defaultPlaceId"
      :identityTypeList="identityTypeList"
      :nationTypeList="nationTypeList"
      :currentRow="currentRow"
      noAdd
      @resultData="getResultData"
    />
    <DetailModal
      ref="detailModal"
      :nationTypeList="nationTypeList"
      :identityTypeList="identityTypeList"
      :currentRow="currentRow"
    />
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import detailsLargeimg from "@/components/detail/details-largeimg.vue";
import { cutMixins } from "@/views/juvenile/components/detail/mixins.js";
import { commonMixins } from "@/mixins/app.js";
import {
  getStrangerDetail,
  setStrangerHandler,
} from "@/api/monographic/community-management.js";
import AddModal from "@/views/juvenile-data-storage/special-library/personnel-thematic-database/components/add-modal.vue";
import DetailModal from "@/views/juvenile-data-storage/special-library/personnel-thematic-database/components/detail-modal.vue";
export default {
  name: "NightOutCommunityConfirmDetail",
  mixins: [commonMixins, cutMixins], //全局的mixin
  components: {
    swiper,
    swiperSlide,
    detailsLargeimg,
    AddModal,
    DetailModal,
  },
  props: {
    // 默认添加的库ID
    currentRow: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      swiperOption: {
        spaceBetween: 10,
        slidesPerView: 12,
        freeMode: true,
        watchSlidesProgress: true,
        navigation: {
          nextEl: ".snap-next",
          prevEl: ".snap-prev",
        },
      },
      cutList: [],
      activeIndex: 0,
      faceList: [
        { key: "name", title: "姓名", dictionary: "" },
        { key: "age", title: "年龄", dictionary: "" },
        { key: "gender", title: "性别", dictionary: "genderList" },
        { key: "idCard", title: "身份证号", dictionary: "" },
        { key: "national", title: "民族", dictionary: "nationList" },
        { key: "phone", title: "联系方式", dictionary: "" },
        { key: "address", title: "户籍地址", dictionary: "" },
      ],
      transformWidth: 0,
      datailsInfo: {},
      collectionType: 0,
      checkIndex: 0,
      imgBoxWidth: "",
      linkVehicle: {},
      pageParam: {
        pageNumber: 1,
        pageSize: 20,
      },
      isShow: false,
      firstPage: false,
      lastPage: false,
      historyList: [],
      remarkForm: { handlerDetail: "", handlerStatus: "1" },
      peopleArchiveForm: {},
      nightOutData: "",
      captureId: "",
      communityName: "",
      listIndex: "-1", // 详情的第几条
      defaultPlaceId: "",
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      nationList: "dictionary/getNationList", //民族
      genderList: "dictionary/getGenderList", //性别
      nationTypeList: "dictionary/getNationTypeList", //民族类型
      identityTypeList: "dictionary/getIdentityTypeList", //证件类型
    }),
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    // this.$refs..getBizLabelList()
  },
  methods: {
    // 切换
    handleTab(item, index) {
      if (item == "left") {
        if (this.activeIndex == 0) {
          if (this.isNoSearch) {
            this.activeIndex = this.cutList.length - 1;
            this.handleTab(this.cutList[this.activeIndex], this.activeIndex);
            return;
          }
          this.goLastpage();
          return;
        }
        index = this.activeIndex - 1;
        item = this.cutList[index];
      }
      if (item == "right") {
        if (this.activeIndex + 1 == this.cutList.length) {
          if (this.isNoSearch) {
            this.activeIndex = 0;
            this.handleTab(this.cutList[this.activeIndex], this.activeIndex);
            return;
          }
          this.goNextPage();
          return;
        }
        index = this.activeIndex + 1;
        item = this.cutList[index];
      }
      this.datailsInfo = this.cutList[index];
      this.activeIndex = index;
      this.play(index);
    },
    collection(flag) {
      this.$set(this.cutList[this.activeIndex], "myFavorite", flag);
    },
    async detailInit(value, index = -1) {
      this.nightOutData = { ...value };
      this.listIndex = index;
      this.captureId = value.id;
      this.communityName = value.communityName;
      this.peopleArchiveForm = {};
      this.remarkForm.handlerDetail = value.handlerDetail;
      this.remarkForm.handlerStatus = value.handlerStatus;
      this.pageParam = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.isShow = true;
      await this.queryInfo();
    },
    /**
     * 首次查询
     */
    async queryInfo() {
      await this.getDataList();
      // 处理footer处的蓝色滑框的位置逻辑
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(0);
      });
    },
    async getDataList(nowIndex = 0) {
      const param = {
        id: this.captureId,
        // ...this.pageParam,
      };
      let { data } = await getStrangerDetail(param);
      // 后面改成分页接口，会有这个参数的
      // this.firstPage = data.firstPage;
      // this.lastPage = data.lastPage;
      this.firstPage = true;
      this.lastPage = true;
      const { faceCaptureVoList, placeId, ...peopleArchiveInfo } = data;
      // 处置有效的抓拍包含人员信息
      if (this.nightOutData.handlerStatus == 1) {
        this.peopleArchiveForm = peopleArchiveInfo;
      } else {
        this.defaultPlaceId = placeId;
      }
      this.cutList = faceCaptureVoList || [];
      this.activeIndex = nowIndex;
      this.datailsInfo = this.cutList[nowIndex];
    },
    // 上一页
    goLastpage() {
      if (this.firstPage) {
        return this.$Message.warning("已经是第一页");
      }
      this.peopleArchiveForm = {};
      this.pageParam.pageNumber--;
      this.getDataList(19);
      this.resetRightPage(19);
    },
    // 下一页
    goNextPage() {
      if (this.lastPage) {
        return this.$Message.warning("已经是最后一页");
      }
      this.peopleArchiveForm = {};
      this.pageParam.pageNumber++;
      this.getDataList(0);
      this.resetLeftPage(0);
    },
    // 获取字典
    translate(value) {
      return this[value];
    },
    // 历史处置记录
    showHistory() {},
    // 设置人员信息入库
    setArchiveInfo() {
      this.$refs.addModal.show(true, {
        photoUrlList: [this.datailsInfo.traitImg],
        communityId: this.defaultPlaceId,
        ...this.peopleArchiveForm,
      });
    },
    // 查看人员信息详情
    detailHandler() {
      this.$refs.detailModal.show({ ...this.peopleArchiveForm });
    },
    // 入库填写的信息保存
    getResultData(item) {
      this.peopleArchiveForm = { ...item };
      this.peopleArchiveForm.faceLibId = this.currentRow.id;
    },
    // 保存处置
    async handleConfirm() {
      if (this.remarkForm.remarkStatus == 1 && !this.currentRow.id) {
        return this.$Message.warning("未配置置信人员需要添加的库");
      }
      // 处置无效
      if (this.remarkForm.remarkStatus == 2) {
        let param = {
          ...this.remarkForm,
          vid: this.datailsInfo.vid,
        };
        await setStrangerHandler(param);
        this.isShow = false;
        return;
      }
      const bizExtend = {};
      bizExtend.communityId = this.peopleArchiveForm.communityId;
      let param = {
        ...this.remarkForm,
        vid: this.datailsInfo.vid,
        ...this.peopleArchiveForm,
        bizExtend,
        placeId: this.peopleArchiveForm.communityId,
      };
      const res = await setStrangerHandler(param);
      if (res.data) {
        this.$Message.success("处置成功");
        this.nightOutData.handlerStatus = 1;
        this.nightOutData.handlerDetail = this.remarkForm.handlerDetail;
        this.$emit("comfirmHandler", {
          ...this.remarkForm,
          index: this.listIndex,
        });
        this.isShow = false;
      }
    },
    // 跳转场所档案
    goToCommunity() {
      const archiveNo = this.nightOutData.placeId || "";
      const { href } = this.$router.resolve({
        path: "/community-place-archive/place-dashboard",
        query: { archiveNo, source: "place" },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";

.info-box-left {
  .community-box {
    cursor: pointer;
    width: 200px;
    height: 40px;
    display: flex;
    align-items: center;
    background: linear-gradient(
      270deg,
      rgba(44, 134, 248, 0) 0%,
      rgba(44, 134, 248, 0.1) 100%
    );
    font-size: 16px;
    color: #2c86f8;
    overflow: hidden;

    img {
      width: 20px;
      height: 20px;
      margin: 0 10px;
    }
  }

  .record-title {
    display: flex;

    > span {
      cursor: pointer;
    }

    .record-right {
      margin-left: 20px !important;
    }
  }

  .sub-head {
    display: flex;
    justify-content: space-between;

    .sub-head-title {
      padding-left: 5px;
      border-left: 3px solid #2c86f8;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
      height: 20px;
      line-height: 20px;
      font-weight: bold;
    }

    .history-box {
      color: #2c86f8;
    }
  }

  .comfirm-box {
    width: 100%;

    .remark-box {
      width: 100%;
      margin-top: 10px;

      /deep/ textarea {
        resize: none;
        height: 160px;
      }

      /deep/ .ivu-form-item {
        margin-bottom: 10px;
      }
    }
  }
}

.device-click {
  cursor: pointer;
  text-decoration: underline;
}

.rcgl {
  .title {
    color: #2c86f8;
  }

  .imgBox {
    cursor: pointer;
    width: 80%;
    margin: 5px 0;
    display: block;
    position: relative;

    img {
      width: 100%;
    }

    .driverFlag {
      position: absolute;
      right: 2px;
      top: 2px;
      color: #fff;
      background: red;
      border-radius: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.line {
  margin: 10px 0;
  height: 1px;
  background: #d3d7de;
}

.sub-title {
  font-size: 12px;

  .label {
    color: #2c86f8 !important;
    font-weight: bold;
  }
}

.active-box {
  // width: 100px;
  // height: 100px;
  border: 2px rgba(44, 134, 248, 1) solid !important;
  // position: absolute;
  // z-index: 1;
  // left: 10px;
  // transition: all 0.2s;
}

.big-dom {
  height: 890px;

  .dom-content {
    flex: unset;
  }

  .info-box {
    .info-box-right {
      height: 640px !important;
    }
  }
}

.footer-btn-box {
  height: 66px;
  border-top: 1px solid #d3d7de;
  display: flex;
  justify-content: center;
  align-items: center;

  /deep/ .ivu-btn {
    margin: 0 6px;
  }
}

.through-record {
  padding-bottom: 0 !important;
}
</style>
