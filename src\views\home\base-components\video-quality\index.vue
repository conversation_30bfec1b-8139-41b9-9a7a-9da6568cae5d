<template>
  <div
    class="body-container"
    v-ui-loading="{ loading: echartsLoading, tableData: !showBack ? pieEchartsList : barChartList }"
  >
    <img
      class="image video-left"
      v-if="!showBack && isStyle1"
      src="@/assets/img/base-home/video-quality/video-left.png"
    />
    <img
      class="image video-right"
      v-if="!showBack && isStyle1"
      src="@/assets/img/base-home/video-quality/video-right.png"
    />
    <!-- 仪表盘 -->
    <draw-echarts
      ref="pieEchartsRef"
      class="charts"
      v-if="!showBack"
      :echart-option="pieEchartOptions"
      :echart-style="ringStyle"
      :echarts-loading="echartsLoading"
      @echartClick="echartClick"
    ></draw-echarts>
    <!-- 柱状图 -->
    <template v-else>
      <self-drop-down :dropdown-list="dropdownList" :default-active-id="activedropId" @chooseOne="chooseOneIndex">
        <i
          class="icon-font icon-wenhao vt-middle icon-warning ml-xs icon-details"
          @click="$emit('on-index-detail', getIndexItem())"
        ></i>
      </self-drop-down>
      <draw-echarts
        class="charts"
        :echart-option="barEchartOptions"
        :echart-style="ringStyle"
        @echartClick="echartClickJump"
        ref="barEchartRef"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
    </template>
    <span class="next-echart">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollAllRight"></i>
    </span>
  </div>
</template>

<script>
import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import { getPieEchartOptions } from './options';
import dataZoom from '@/mixins/data-zoom';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
import videoQualityStyle from '@/views/home/<USER>/module/video-quality/index.js';

export default {
  name: 'device-quality',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    selfDropDown: require('@/views/home/<USER>/echarts-dom/self-drop-down.vue').default,
  },
  props: {
    evaluationIndexResult: {},
    loading: {},
    styleType: {},
    componentConfig: {}, //componentConfig.js中的配置
  },
  data() {
    return {
      tabData: [{ label: '视频流质量', id: 'quality' }],
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      showBack: false,
      barEchartOptions: {},
      pieEchartOptions: {},
      pieEchartsList: [],
      activeValue: 'quality',
      echartsLoading: false,
      barChartList: [],
      piePageNum: 0, // 第一页饼图
      totalPage: 0, // 总共多少页饼图
      dropdownList: [],
      activedropId: null,
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
    isStyle1() {
      return this.styleType === '1';
    },
    homeStyle() {
      return videoQualityStyle[`style${this.styleType}`] || videoQualityStyle.style1;
    },
  },
  watch: {
    loading: {
      handler() {
        this.echartsLoading = this.loading;
      },
      immediate: true,
    },
    /**
     * 重点和全量公用一个组件 ，通过componentConfig.js中的indexList过滤即可
     */
    evaluationIndexResult: {
      async handler(val) {
        if (!val?.length) return;
        const indexList = this.componentConfig.indexList;
        this.pieEchartsList = val.filter((item) => {
          if (indexList && indexList.length > 0) {
            return item.indexModule === '4' && indexList.find((value) => value === item.indexType);
          } else {
            return item.indexModule === '4';
          }
        });
        this.totalPage = Math.ceil(this.pieEchartsList.length / 4);
        this.dropdownList = this.pieEchartsList.map((item) => {
          return {
            id: item.indexId,
            label: item.indexName,
          };
        });
        this.handlePieCharts();
      },
      immediate: true,
    },
  },
  filter: {},
  created() {
    window.addEventListener('resize', this.handlePieCharts);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handlePieCharts);
  },
  activated() {
    this.handlePieCharts();
  },
  mounted() {},
  methods: {
    // 供外部调用
    closeBar() {
      this.showBack = false;
    },
    getIndexItem() {
      return this.evaluationIndexResult.find((item) => item.indexId === this.activedropId);
    },
    scrollAllRight() {
      // 柱状图
      if (this.showBack) {
        this.scrollRight('barEchartRef', this.barChartList, [], this.comprehensiveConfig.homeNum);
      } else {
        // 仪表盘
        this.piePageNum++;
        this.piePageNum >= this.totalPage ? (this.piePageNum = 0) : null;
        this.handlePieCharts();
      }
    },
    handlePieCharts() {
      let list = this.pieEchartsList.slice(this.piePageNum * 4, (this.piePageNum + 1) * 4);
      let gridHeigth = this.$util.common.fontSize(120);
      this.$nextTick(() => {
        if (this.$refs.pieEchartsRef) {
          gridHeigth = this.$refs.pieEchartsRef.$el.clientHeight / 2 - this.$util.common.fontSize(10);
        }
        this.pieEchartOptions = getPieEchartOptions(list, gridHeigth, this.styleType);
        this.$nextTick(() => {
          if (!this.$refs.pieEchartsRef) return;
          gridHeigth = this.$refs.pieEchartsRef.$el.clientHeight / 2 - this.$util.common.fontSize(10);
          let { series } = getPieEchartOptions(list, gridHeigth, this.styleType);
          this.$refs.pieEchartsRef.setOption({ series: series });
        });
      });
    },
    chooseOneIndex(indexId) {
      this.activedropId = indexId;
      this.getBarInfo();
    },
    // 柱状图获取
    async getBarInfo() {
      if (!this.activedropId) return;
      let isIndexIdItem = this.getIndexItem();
      if (!isIndexIdItem) return;
      this.echartsLoading = true;
      let params = {
        indexId: this.activedropId,
        batchId: isIndexIdItem?.batchId,
        access: 'REPORT_MODE',
        displayType: 'REGION',
        orgRegionCode: isIndexIdItem?.civilCode,
        sortField: 'REGION_CODE',
      };
      try {
        let { data } = await this.$http.post(governanceevaluation.getNumRankInfo, params);
        this.barChartList = data.data;
        this.handleBarCharts(isIndexIdItem);
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleBarCharts(oneIndexItem) {
      const { qualified, unqualified } = this.homeStyle;
      let { batchId, indexType } = this.getIndexItem();
      let qualityData = [];
      let disQualityData = [];
      let xAxisData = [];
      let lineData = [];
      this.barChartList.forEach((item) => {
        let qualityRate = (item?.qualifiedNum / item?.actualNum) * 100 || 0;
        let detailData = {
          name: item.regionName,
          title: oneIndexItem.indexName,
          titleNum: `${qualityRate.toFixed(2)}%`,
          list: [
            {
              label: '合格数量',
              color: qualified.tooltipColor,
              num: item?.qualifiedNum ?? 0,
            },
            {
              label: '不合格数量',
              color: unqualified.tooltipColor,
              num: item?.unqualifiedNum ?? 0,
            },
          ],
        };
        qualityData.push({
          value: item.qualifiedNum,
          data: detailData,
          originData: {
            ...item,
            indexId: this.activedropId,
            batchId: batchId,
            indexType: indexType,
          },
          itemStyle: {
            borderWidth: item.qualifiedNum ? 1 : 0,
            borderColor: (() => {
              return new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: qualified.color[0],
                  },
                  {
                    offset: 1,
                    color: qualified.color[1],
                  },
                ],
                false,
              );
            })(),
          },
        });
        disQualityData.push({
          value: item.unqualifiedNum,
          data: detailData,
          originData: {
            ...item,
            indexId: this.activedropId,
            batchId: batchId,
            indexType: indexType,
          },
          itemStyle: {
            borderWidth: item.unqualifiedNum ? 1 : 0,
            borderColor: (() => {
              return new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: unqualified.color[0],
                  },
                  {
                    offset: 1,
                    color: unqualified.color[1],
                  },
                ],
                false,
              );
            })(),
          },
        });
        xAxisData.push(item.regionName);
        lineData.push(qualityRate);
      });
      this.barEchartOptions = this.$util.doEcharts.getHomeAreaBar({
        toolTipDom: TooltipDom,
        yAxisData: [
          {
            min: 0,
            max: 100,
            axisLabel: {
              //y轴文字的配置
              color: '#8ABAFB',
              formatter: '{value} %',
              fontSize: this.$util.common.fontSize(12),
            },
          },
          {
            min: 0,
            axisLabel: {
              //y轴文字的配置
              color: '#8ABAFB',
              formatter: '{value}',
              fontSize: this.$util.common.fontSize(12),
            },
          },
        ],
        series: [
          {
            type: 'bar',
            name: '合格',
            color: qualified.color,
            borderColor: qualified.borderColor,
            data: qualityData,
            yAxisIndex: 1,
          },
          {
            name: '不合格',
            type: 'bar',
            color: unqualified.color,
            borderColor: unqualified.borderColor,
            data: disQualityData,
            yAxisIndex: 1,
          },
          {
            type: 'line',
            data: lineData,
            yAxisIndex: 0,
          },
        ],
        xAxisData: xAxisData,
      });
      setTimeout(() => {
        this.setDataZoom('barEchartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    echartClick(item) {
      this.showBack = true;
      this.activedropId = item?.data?.data.indexId;
      this.getBarInfo();
    },
    echartClickJump(params) {
      this.$emit('on-jump', params.data.originData);
    },
  },
};
</script>

<style lang="less" scoped>
.video-quality-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
  flex: 1;
  .tab-title {
    height: 30px;
    .icon-fanhui {
      color: #0185f6;
    }
  }
}
.body-container {
  width: 100%;
  position: relative;
  height: calc(100% - 30px) !important;
  .charts {
    width: 100%;
    height: 100% !important;
  }
  .image {
    height: 90%;
    position: absolute;
    transform: translate(0, 5%);
  }
  .video-left {
    left: 0;
  }
  .video-right {
    right: 0;
  }
}
</style>
