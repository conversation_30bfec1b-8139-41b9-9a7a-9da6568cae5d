<template>
  <div class="container">
    <div class="star-box">
      <div class="star"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'bottom-star',
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(7, 100, 252, 0.4) 0%, rgba(7, 100, 252, 0) 100%);
  animation: flash 2s infinite linear;
}
.star-box {
  width: 100%;
  height: 5px;
  background-image: linear-gradient(
    to right,
    rgba(11, 146, 254, 0) 10%,
    rgba(11, 146, 254, 0.8) 39%,
    rgba(11, 146, 254, 0.8) 50%,
    rgba(11, 146, 254, 0.8) 61%,
    rgba(11, 146, 254, 0) 90%
  );
  text-align: center;
  border-radius: 50%;
}
.star {
  display: inline-block;
  // background: url('~@/assets/img/home/<USER>') no-repeat;
  background-size: cover;
  width: 55px;
  height: 7px;
  background-image: radial-gradient(
    #fff 15%,
    #f9ffff 20%,
    #5cfbff 35%,
    #2fd9ff 40%,
    #18b5ff 55%,
    #0d98fe 60%,
    rgba(11, 146, 254, 0.1) 100%
  );
  transform: translateY(-7px);
  position: relative;
  border-radius: 50%;
}

@keyframes flash {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>
