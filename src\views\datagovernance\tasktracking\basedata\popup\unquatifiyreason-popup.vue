<template>
  <ui-modal title="查看不合格原因" v-model="visible" :styles="styles" footer-hide>
    <ui-table
      class="ui-table"
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="minusTable"
      :loading="loading"
    >
    </ui-table>
    <template #footer>
      <Button type="primary" class="plr-30">保存</Button>
    </template>
  </ui-modal>
</template>
<style lang="less" scoped></style>
<script>
export default {
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      default: false,
    },
    value: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        // top: ".4rem",
        width: '85%',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      minusTable: 480,
    };
  },
  created() {},
  mounted() {},
  methods: {
    changePage() {},
    changePageSize() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
