<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics :customParameters="customParameters"></index-statistics>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart
          :activeIndexItem="activeIndexItem"
          :lengName="lengName"
          ref="subChart"
          :customParameters="customParameters"
          isTakeDetail
        >
          <template #sort="{ sort }">
            <RadioGroup v-model="sort.sortField" class="fr" @on-change="onChangeSortField">
              <Radio label="REVOCATION_COUNT">按撤销次数排序</Radio>
              <Radio label="ADD_UP_REVOCATION_NUMBER">按累计撤销数量排序</Radio>
              <Radio label="FINAL_REVOCATION_NUMBER">按最终撤销数量排序</Radio>
            </RadioGroup>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart" :tooltipFormatter="tooltipFormatter"></line-chart>
    </div>
  </div>
</template>

<script>
import lineChartTooltip from './line-chart-tooltip.vue';
import Vue from 'vue';
export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChart: require('@/views/governanceevaluation/evaluationoResult/components/line-chart.vue').default,
  },
  data() {
    return {
      lengName: [
        {
          name: '累计撤销数量',
          key: 'addUpRevocationNumber',
          color: '#4BC3D1',
        },
        {
          name: '最终撤销数量',
          key: 'finalRevocationNumber',
          color: '#17A7DF',
        },
      ],
      customParameters: {
        comparisonType: 1,
      },
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    onChangeSortField(item) {
      this.$refs.subChart.onChangeSortField(item);
    },
    tooltipFormatter(data) {
      let lineChartTooltipConstructor = Vue.extend(lineChartTooltip);
      let _this = new lineChartTooltipConstructor({
        el: document.createElement('div'),
        data() {
          return {
            data,
            dateType: this.dateType,
          };
        },
      });
      return _this.$el.outerHTML;
    },
  },
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: 100%;
      @{_deep} .statistics-ul {
        li {
          width: calc((100% - 40px) / 4) !important;
        }
        li:nth-child(-n + 3) {
          width: calc((100% - 30px) / 3) !important;
        }
      }
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
