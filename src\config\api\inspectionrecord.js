export default {
  resultId: '/ivdg-evaluation-app/evaluationRecordFocusTrack/focusRecordStatistics', // 重点人轨迹-统计
  pageListGroup: '/ivdg-evaluation-app/evaluationRecordFocusTrack/pageListGroup', // 重点人轨迹-聚档模式
  pageListGroupDetailc: '/ivdg-evaluation-app/evaluationRecordFocusTrack/pageListGroupDetail', // 重点人轨迹-聚档模式详情
  pageListImage: '/ivdg-evaluation-app/evaluationRecordFocusTrack/pageListImage', // 重点人轨迹-图片模式
  queryFocusDetailCount: '/ivdg-evaluation-app/evaluationFocusTrackRealDetail/queryFocusDetailCount', // 重点人员轨迹上传及时性详情统计
  queryFocusTrackRealDetaiGrouplList: '/ivdg-evaluation-app/evaluationRecordFocusTrackReal/pageListGroup', // 聚档模式-根据主题id获取结果列表
  queryFocusTrackRealDetailListPage: '/ivdg-evaluation-app/evaluationRecordFocusTrackReal/pageListGroupDetail ', // 图片模式-根据主题id获取结果列表
  queryFocusTrackRealPageListImage: '/ivdg-evaluation-app/evaluationRecordFocusTrackReal/pageListImage', // 上传及时性图片模式
  queryFocusTrackRealResultCount: '/ivdg-evaluation-app/evaluationRecordFocusTrackReal/focusRecordStatistics', // 重点人员轨迹上传及时性统计
  queryFocusTrackRealDetailById: '/ivdg-evaluation-app/evaluationFocusTrackRealDetail/queryFocusTrackRealDetailById', // 重点人员轨迹上传及时性统计
  pageListImageCar: '/ivdg-evaluation-app/evaluation/app/vehicle/result/vehicleForImageModel', // 车辆图片模式
  statisticsCar: '/ivdg-evaluation-app/evaluation/app/vehicle/result/statistics', // 车辆统计
  queryEvaluationVideoPageList: '/ivdg-evaluation-app/evaluation/videoVideo/queryEvaluationVideoPageList', // 指标结果考核的设备详情-视频流视分页查询对象
  vehicleForDeviceModel: '/ivdg-evaluation-app/evaluation/app/vehicle/result/vehicleForDeviceModel', //设备模式
  vehicleForDeviceSurvey: '/ivdg-evaluation-app/evaluation/app/vehicle/result/vehicleForDeviceSurvey',
  queryEvaluatingVideoCount: '/ivdg-evaluation-app/evaluation/videoVideo/queryEvaluatingVideoCount', // 视频流数据统计
  queryEvaluationVideoById: '/ivdg-evaluation-app/evaluation/videoVideo/queryEvaluationVideoById',
  queryEvaluatingVideoGeneralSituationList:
    '/ivdg-evaluation-app/evaluation/videoVideo/queryEvaluatingVideoGeneralSituationList', // 视频流检测概况
  vehicleDetail: '/ivdg-evaluation-app/evaluation/app/vehicle/result/algorithm/detail/', //获取车辆算法详情

  //检测记录 人脸视图数据
  // getEvaluationFaceByPictureModel: "/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceByPictureModel", //检测记录-人脸图片模式
  getEvaluationFaceByPictureModel: '/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceByPictureModel', //检测记录-人脸图片模式
  // getEvaluationFaceByDeviceModel: "/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceByDeviceModel", //检测记录-人脸设备模式
  getEvaluationFaceByDeviceModel: '/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceByDeviceModel', //检测记录-人脸设备模式
  // getEvaluationFaceStatisticsCount: "/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceStatisticsCount", //检测记录-统计
  getEvaluationFaceStatisticsCount: '/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceStatisticsCount', //检测记录-统计
  getEvaluationFaceOverview: '/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceOverview', //检测记录-检测概况
  // getEvaluationFaceOverviewV2: "/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceOverviewV2", //检测记录-人脸检测概况
  getEvaluationFaceOverviewV2: '/ivdg-evaluation-app/evaluation/recordFace/getEvaluationFaceOverviewV2', //检测记录-人脸检测概况
  getEvaluationUnqualified: '/ivdg-evaluation-app/evaluation/recordFace/getEvaluationUnqualified', //检测记录-查看不合格图片

  getEvaluationOverviewExport: '/ivdg-evaluation-app/evaluation/statistics/getEvaluationOverviewExport', //治理评测-检测概览导出
  getEvaluationResultList: '/ivdg-evaluation-app/evaluation/statistics/getEvaluationResultList', //时间筛选结果列表
  // 轨迹图片可访问率
  pageTrackUrlAvailableDetails: '/ivdg-evaluation-app/evaluationRecordFocusTrackUrlAvailable/pageListImage', // 图片模式列表数据
  pageTrackUrlAvailableClusters: '/ivdg-evaluation-app/evaluationRecordFocusTrackUrlAvailable/pageListGroup', // 聚档模式列表数据
  getByResultId: '/ivdg-evaluation-app/evaluationRecordFocusTrackUrlAvailable/focusRecordStatistics', // 结果统计
  pageTrackCatchDetails: '/ivdg-evaluation-app/evaluationRecordFocusTrackUrlAvailable/pageListGroupDetail', // 聚档结果详情列表
  getRationalityPageList: '/ivdg-evaluation-app/evaluation/capture/rationality/pageList', //人脸车辆 合理性检测
  getRationalityRecordStatistics: '/ivdg-evaluation-app/evaluation/capture/rationality/getRecordStatistics', //人脸车辆 统计
  getCapturePageListByDevice: '/ivdg-evaluation-app/evaluation/capture/rationality/getCapturePageListByDevice', //人脸车辆 查看图片
  getEvaluationCaptureRationalityExport:
    '/ivdg-evaluation-app/evaluation/capture/rationality/getEvaluationCaptureRationalityExport', //人脸车辆 导出
  getConnectInternetList: '/ivdg-evaluation-app/evaluation/video/connectInternet/result/list', //检测记录 联网率列表
  getConnectInternetStatistics: '/ivdg-evaluation-app/evaluation/video/connectInternet/detail', //检测记录 联网率统计
  // 联网率详情
  queryLowerLevelScale: '/ivdg-evaluation-app/evaluation/video/connectInternet/queryLowerLevelScale', // 排名统计
  exportDetailData: '/ivdg-evaluation-app/evaluation/video/connectInternet/export', // 导出
  // 视频检测记录
  videoQualityPageList: '/ivdg-evaluation-app/evaluatingVideoDeviceDetailRecord/videoQualityPageList', // 检测记录-查询视频流质量检测数据分页列表
  videoQualityResultStatistics: '/ivdg-evaluation-app/evaluatingVideoDeviceDetailRecord/videoQualityResultStatistics', // 检测记录-视频流质量结果统计
};
