<template>
  <div class="math-jax">
    <div>
      <template v-for="(item, index) in mathArr">
        <div v-if="item.sign !== '/'" :key="index" class="math-jax-inline math-math-vt-middle">
          <span class="math-jax-inline">{{ item.label }}</span>
          <span class="math-jax-inline">{{ item.sign }}</span>
        </div>
        <div v-else :key="index" class="math-jax-inline">
          <div class="math-jax-inline math-vt-middle">
            {{ item.label }}
          </div>
          <div class="division math-jax-inline math-vt-middle">
            <div class="molecular">{{ item.molecular }}</div>
            <div class="denominator">{{ item.denominator }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  /**
   * 本组件为转换数学公式 * / 展示为数学运算 by zml
   * 当组件产生问题或需要更高级别转换数学公式时 建议使用MathJax库来转换不要使用此组件
   */
  name: 'MathJax',
  props: {
    // 例子((中文 * （128 + 20）+ 3) * 3) / (10 + 1) = 3 * (5 + 1) / 3 * (4 + 5) / 3
    mathStr: {
      type: String,
    },
  },
  data() {
    return {
      mathArr: [],
    };
  },
  created() {},
  methods: {
    /**
     * 分割字符串形成数组对象
     * \xa0为空格为了美观
     */
    splitString(str) {
      let strArr = [];
      const signList = ['+', '-', '*', '/', '(', ')', '（', '）', '[', ']', '='];
      let startIndex = 0,
        endIndex = 0;
      for (let i = 0, len = str.length; i < len; i++) {
        let tempObj = {};
        /**
         * 判断字符串中符号的位置,分割字符串形成对象
         */
        if (signList.includes(str[i])) {
          endIndex = i;
          tempObj['label'] = `\xa0${str.substring(startIndex, endIndex).trim()}\xa0`;
          tempObj['sign'] = str[i];
          startIndex = i + 1;
          strArr.push(tempObj);
        }
      }
      if (startIndex !== str.length) {
        strArr.push({
          label: `\xa0${str.substring(startIndex, str.length)}\xa0`,
          sign: null,
        });
      }
      return strArr;
    },
    dealMathArr(strArr) {
      /**
       * 处理sign
       * 如果为*则更改为x
       * 如果为/
       * 1.如果上一项的sign不为) 或）则把本项label当作分子
       * 2.如果上一项的sign为）或)则把上一项合入本项为分子
       * 3.如果下一项的sign为（或(则把从当前下标直到找到sign为)或者）的项作为分母合入
       * 4.如果下一项的sign不为（或(则把下一项的label合入本项当作分母
       */
      /**
       * 记录需要去除的对象的下标
       * forEach循环中不可以直接splice带括号的对象否则会报错
       */
      let spliceArr = [];
      strArr.forEach((s, index) => {
        switch (s.sign) {
          case '*':
            s.sign = 'x';
            break;
          case '/':
            /**
             * 分子合并
             * 当发现符号为/时记录当前下标
             * 寻找上一个符号是否为右括号
             */
            if (strArr[index - 1].sign === ')' || strArr[index - 1].sign === '）') {
              /**
               * startNum 为括号开始的下标
               * spliceNum 为需要去除的括号的内容数量
               * parenthesesLeftNum 为除法出现左括号的数量
               * parenthesesRightNum 为除法出现右括号的数量
               */
              let startNum = 0,
                spliceNum = 0,
                molecular = '',
                parenthesesLeftNum = 0,
                parenthesesRightNum = 0;
              spliceNum = 0;
              startNum = index - 1;
              /**
               * 从当前除号下标开始向前寻找
               * parenthesesRightNum 记录同时有多个括号时右括号的数量
               * 当发现左侧闭合括号时结束
               */
              for (let i = index; i--; i > 0) {
                if (strArr[i].sign === ')' || strArr[i].sign === '）') {
                  parenthesesRightNum++;
                }
                if (strArr[i].sign === '(' || strArr[i].sign === '（') {
                  break;
                }
              }
              /**
               * 从当前除号下标往前寻找
               * 拼接分子内容
               * 如果发现左括号则记录左括号数量
               * 当左括号数量与右括号数量相同时结束本次循环
               * 因为已经拼接分子所以不需要原有数组中的括号内容
               * 循环次数spliceNum为需要去除数组中的对象数量
               * 删除起始下标startNum
               */
              for (let i = index; i--; i > 0) {
                spliceNum++;
                molecular = strArr[i].label + strArr[i].sign + molecular;
                if (strArr[i].sign === '(' || strArr[i].sign === '（') {
                  parenthesesLeftNum++;
                  if (parenthesesLeftNum === parenthesesRightNum) {
                    break;
                  }
                }
                startNum--;
              }
              s.molecular = molecular.trim();
              spliceArr.push({ startNum, spliceNum });
            } else {
              s.molecular = s.label.trim();
            }
            s.label = '\xa0';
            /**
             * 分母合并
             * 当发现符号为/时记录当前下标
             * 寻找下一个符号是否为左括号
             */
            if (strArr[index + 1].sign === '(' || strArr[index + 1].sign === '（') {
              let startNum = 0,
                spliceNum = 0,
                denominator = '',
                parenthesesLeftNum = 0,
                parenthesesRightNum = 0;
              startNum = index + 1;
              spliceNum = 0;
              /**
               * 从当前除号下标开始向后寻找
               * parenthesesLeftNum 记录同时有多个括号时左括号的数量
               * 当发现左侧闭合括号时结束
               */
              for (let i = index, len = strArr.length; i++; i < len) {
                if (strArr[i].sign === '(' || strArr[i].sign === '（') {
                  parenthesesLeftNum++;
                }
                if (strArr[i].sign === ')' || strArr[i].sign === '）') {
                  break;
                }
              }
              for (let i = index, len = strArr.length; i++; i < len) {
                spliceNum++;
                denominator += strArr[i].label + strArr[i].sign;
                if (strArr[i].sign === ')' || strArr[i].sign === '）') {
                  parenthesesRightNum++;
                  if (parenthesesLeftNum === parenthesesRightNum) {
                    break;
                  }
                }
              }
              s.denominator = denominator.trim();
              spliceArr.push({ startNum, spliceNum });
            } else {
              s.denominator = strArr[index + 1].label.trim();
            }
            strArr[index + 1].label = '\xa0';
            break;
        }
      });
      /**
       * 删除已经转换为分子分母的对象
       */
      for (let i = spliceArr.length; i--; i >= 0) {
        strArr.splice(spliceArr[i].startNum, spliceArr[i].spliceNum);
      }
      return strArr;
    },
  },
  watch: {
    mathStr: {
      handler(val) {
        if (!val) return;
        const strArr = this.splitString(val);
        this.mathArr = this.dealMathArr(strArr);
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.math-jax {
  display: inline-block;
  .math-jax-inline {
    display: inline-block;
  }
  .math-vt-middle {
    vertical-align: middle;
  }
  .molecular {
    &::after {
      content: '';
      width: 100%;
      display: block;
      border-bottom: 1px solid #000;
    }
  }
  .division {
    > div {
      text-align: center;
    }
  }
}
</style>
