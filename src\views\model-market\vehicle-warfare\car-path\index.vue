<!--
    * @FileDescription: 行车轨迹
    * @Author: H
    * @Date: 2023/09/18
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="car-path container">
        <!-- 地图 -->
        <mapCustom ref="mapBase" :trackPoints="vehiclePointList" mapType="carPath" sectionName='vehicle' cutIcon="track" :currentClickIndex="currentClickIndex" @chooseMapItem="chooseMapItem($event, true)" />
        <!-- 左面信息展示框 -->
        <leftBox ref="searchBar" @search="handleSearch" v-show="detailsBox"></leftBox>
        <detailsBox ref="detailBox" 
            :currentClickIndex="currentClickIndex"
            @vehicleDataList="vehicleDataList" 
            @backSearch="handleBackSearch"
            @backSearchdetails="backSearchdetails"
            @sprinkleDot="chooseMapItem" 
            @trackConnect="trackConnect"
            v-show="!detailsBox"></detailsBox>
        <track-play 
            v-if="isShowTrackPlayer && mapList.length>0" 
            prevTitle="从上一个卡口绘制" 
            nextTitle="从下一个卡口绘制" 
            :trackList="mapList" 
            :enterTrackNumber="mapList.length>0?mapList.length-1:0" 
            :currentStep="currentStep" 
            @sliderStateChange="handlePlayStateChange" 
            @sliderPrev="onSliderPrev" 
            @sliderNext="onSliderNext" 
            @sliderRangeChange="changeAnimationLine"
            @sliderPositionChange="onPostionChange" 
            @showAllChanged="showAllTracks" 
            ref="trackPlayer" />
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import detailsBox from './components/carDetailsBox.vue';
import mapCustom from '../components/map/index.vue';
import { queryPlaceInfoPageList } from '@/api/operationsOnTheMap';
import { mapMutations } from 'vuex';
import TrackPlay from "../components/trackPlay/TrackPlayController.vue";
export default {
    name: 'car-path',
    components:{
        mapCustom,
        leftBox,
        detailsBox,
        TrackPlay
    },
    data () {
        return {
            detailsBox: true,
            vehiclePointList: [],
            isShowTrackPlayer: false,
            mapList: [], //数据
            currentStep: 0,
            isShowAll: false,
            currentClickIndex: -1
        }
    },
    watch:{},
    computed:{ },
    activated() {},
    deactivated(){},
    async created() {
        this.setLayoutNoPadding(true)
        if(this.$route.query.plateNo) {
            this.$nextTick(() => {
                this.$refs.searchBar.queryParams.plateNo = this.$route.query.plateNo
                if (this.$route.query.absTime) {
                    this.$refs.searchBar.queryParams.startDate = this.$dayjs(Number(this.$route.query.absTime)).subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
                    this.$refs.searchBar.queryParams.endDate = this.$dayjs(Number(this.$route.query.absTime)).add(1, 'hour').format('YYYY-MM-DD HH:mm:ss');
                }
                if (this.$route.query.startDate && this.$route.query.endDate) {
                    this.$refs.searchBar.queryParams.startDate = this.$dayjs(Number(this.$route.query.startDate)).format('YYYY-MM-DD HH:mm:ss');
                    this.$refs.searchBar.queryParams.endDate = this.$dayjs(Number(this.$route.query.endDate)).format('YYYY-MM-DD HH:mm:ss');
                }
                this.$refs.searchBar.handleSearch()
            })
        }
    },
    destroyed() {
        this.setLayoutNoPadding(false)
    },
    mounted(){
            
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        handleSearch(params){
            console.log(params, 'params')
            this.detailsBox = false;
            this.$nextTick(() => {
                this.$refs.detailBox.handleList(params)
                // this.$refs.mapBase.vehicleTrajectory()
            })
        },
        vehicleDataList(list){
            this.vehiclePointList = list;
        },
        handleBackSearch(){
            this.detailsBox = true;
        },
        backSearchdetails() {
            this.$refs.mapBase.clearPoint()
        },
        // 左侧选中项 点击普通搜索列表
        chooseMapItem(index, noZoom) {
            this.currentClickIndex = index
            if (!noZoom) this.$refs.mapBase.zoomInto()
        },
        trackConnect(flag) {
            this.$refs.mapBase.trackConnect(flag)
        },
        // 开始 暂停
        handlePlayStateChange(isPlaying) {
            console.log('这是 11')
            if(this.isShowAll) {
                this.$Message.info('显示全部模式下不支持');
                return
            }
            // _animationLine  地图车动画
            // if (isPlaying && _animationLine) {
            //     if(this.currentStep>=1) {
            //         _animationLine.restart();
            //     } else {
            //         _animationLine.start();
            //     }
            // } else {
            //     _animationLine.pause();
            // }
        },
        // 上一个步
        onSliderPrev(isPlaying) {
            console.log('这是 22')
             if(this.isShowAll) {
                this.$Message.info('显示全部模式下不支持');
                return
            }
            // this.detailData = null;
            // let drawIndex = _animationLine.drawIndex;
            // let _originDrawIndex = originMapIndex;
            // let originNextDrawIndex = _originDrawIndex - 1;
            // if (_originDrawIndex <= 0) {
            //     return;
            // }
            //removeMarker 删除标注
            // this.removeMarker(_originDrawIndex);
            // for (
            //     let i = drawIndex;
            //     i > this.originPointMap[originNextDrawIndex];
            //     i--
            // ) {
            //     _animationLine.nextPosition();
            // }
            // originMapIndex = originNextDrawIndex;
            // isPlaying ? _animationLine.start() : _animationLine.pause();
        },
        // 下一步 
        onSliderNext(isPlaying) {
            console.log('这是 33')
            if(this.isShowAll) {
                this.$Message.info('显示全部模式下不支持');
                return
            }
            // this.detailData = null;
            // let drawIndex = _animationLine.drawIndex;
            // let _originDrawIndex = originMapIndex;
            // let originNextDrawIndex = _originDrawIndex + 1;
            // if (_originDrawIndex >= _trackMarkers.length - 1) {
            //     return;
            // }
            //showmarker
            // this.showMarker(_points[originNextDrawIndex], originNextDrawIndex);
            // for (
            //     let i = drawIndex;
            //     i < this.originPointMap[originNextDrawIndex];
            //     i++
            // ) {
            //     _animationLine.afterPosition();
            // }
            // originMapIndex = originNextDrawIndex;
            // isPlaying ? _animationLine.start() : _animationLine.pause();
        },
        // 开始的位置/ 进度条总长度发生变化
        changeAnimationLine(res, isShowAll, isPlaying) {
            console.log('这是 44')
            this.isShowAll = isShowAll;
            // if (!isShowAll) {
            //     this.clearLayer();
            //     this.clearCanvasTotalLayer();
            //     let result = [];
            //     const mapList = this.mapList;
            //     for (let i = 0, len = mapList.length; i < len; i++) {
            //         let _track = mapList[i];
            //         if (
            //             _track.absTime >= res.left &&
            //             _track.absTime <= res.right
            //         ) {
            //             result.push(_track);
            //         }
            //     }
            //     if (result.length > 0) {
            //         this.getLine(result, () => {
            //             if (_lines && _lines.animationpoints) {
            //                 this.showAnimationLine(_lines.animationpoints, 0);
            //                 isPlaying
            //                     ? _animationLine.start()
            //                     : _animationLine.pause();
            //             }
            //         });
            //     } else {
            //         $tip("该时间段无过车数据！", "warn");
            //     }
            // } else {
            //     _animationLine && _animationLine.stop();
            // }
        },
        // 进度条发生变化
        onPostionChange(percent, isPlaying) {
            console.log('这是 55')
            this.detailData = null;
            // if (_animationLine) {
            //     let stepIndex = percent * this.steps;
            //     var i,
            //         position = this.getDrawSprit(
            //             _lines.animationpoints,
            //             wondermap,
            //             VEHICLE_TRACK_SPEED,
            //             stepIndex
            //         ),
            //         _originDrawIndex = originMapIndex,
            //         drawIndex = _animationLine.drawIndex;
            //     let trackMarkers = _trackMarkers;
            //     let originPointMap = this.originPointMap;
            //     if (position.index > drawIndex) {
            //         while (
            //             originPointMap[++_originDrawIndex] <= position.index
            //         ) {
            //             this.showMarker(
            //                 _points[_originDrawIndex],
            //                 _originDrawIndex
            //             );
            //             originMapIndex = _originDrawIndex;
            //         }
            //     } else if (position.index < drawIndex) {
            //         while (originPointMap[_originDrawIndex] > position.index) {
            //             let originMarker = trackMarkers[_originDrawIndex];
            //             _markerLayer.removeOverlay(originMarker);
            //             originMapIndex = _originDrawIndex;
            //             _originDrawIndex--;
            //         }
            //     }
            //     _animationLine.setPosition(position.index, position.step);
            //     isPlaying ? _animationLine.start() : _animationLine.pause();
            // }
        },
        //显示全部轨迹点位
        showAllTracks(isShowAll) {
            console.log('这是 66')
            this.isShowAll = isShowAll;
            // this.clearLayer();
            // if (isShowAll) {
            //     //获取路网数据并画出轨迹
            //     this.getLine(this.mapList, () => {
            //         //遍历轨迹中所有点位并撒点
            //         this.renderAllCanvasMarkers(this.mapList);
            //     });
            // } else {
            //     //刷新trackPlayer播放器
            //     this.$refs.trackPlayer.refresh();
            // }
        },
    }
}
</script>

<style lang='less' scoped>
.car-path{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
