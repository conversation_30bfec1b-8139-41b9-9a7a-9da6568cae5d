.xntree-outer{
    position: relative;
}
.xntree-outer .xntree-cont{
    position: absolute;
    top:0;
    left: 0;
    /* overflow: hidden; */
    transform-origin: left top;
    width: 100%;
}
.xntree-outer .xntree-scroll{

}
.xntree-outer .xntree-item{
    display: flex;
    line-height: 32px;
    font-size: 14px;
    /*padding-left:40px;*/
    position: relative;
    align-items: center;
    height: 32px;
}
.xntree-outer .xntree-item.on{
    background: var(--xntree-on-color);
    color: #fff;
}

.xntree-outer .xntree-item.on .xn-tree-icons a{
    color: #fff;
}
.xntree-outer .xn-slidedown,.xntree-outer .xn-folder{
    /*position: absolute;*/
    width:15px;
    left:0;
    overflow: hidden;
}
.xntree-outer .xn-slidedown:not(.down):before{
    transform: rotateZ(-90deg);
    position: absolute;
}
.xntree-outer .xn-folder{
    left:15px;
}
.xntree-outer .xn-tree-icons{
    /*width: 30px;*/
    /*flex: 0 0 30px;*/
    display: flex;
    color: #666;
    justify-content: space-between;
    height: 100%;
}
.xntree-outer .xn-tree-icons a{
    flex: 1;
    width: 15px;
}
.xntree-outer .xn-indent{
    display: inline-block;
    width:15px;
    flex: 0 0 15px;
    line-height: inherit;
    height:100%;
}
.xntree-outer .xn-checkbox,.xntree-outer .xn-radio{
    width:14px;
    height: 14px;
    border:1px solid #ccc;
    margin:0 4px;
    flex: 0 0 14px;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
}
.xntree-outer .xn-radio{
    border-radius: 50%;
}
.xntree-outer .xn-checkbox.on,.xntree-outer .xn-radio.on{
    background:var(--xntree-primary-color);
    border-color: var(--xntree-primary-color);
}
.xntree-outer .xn-checkbox.disable,.xntree-outer .xn-radio.disable{
    background:#efefef;
}
.xntree-outer .xn-checkbox.on:before,.xntree-outer .xn-radio.on:before{
    position: absolute;
    top: 0;
    left: 0;
    color: #fff;
    width: 100%;
    height: 100%;
    font-size: 12px;
    line-height: 11px;
}
.xntree-outer .xn-hide,.xntree-outer .xn-hide-sub{
    display: none;
}
.xntree-outer .xntree-label{
    white-space: nowrap;
    cursor: pointer;
    flex: auto;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
}
.xntree-outer .xntree-move{
    width: 100%;
    height: 1px;
    background: #333;
    position: absolute;
    left:0;
    display: none;
}
.xntree-outer .xntree-item{
    user-select: none;
    box-sizing: border-box;
}
.xntree-outer .xntree-item.xn-onmoving{
    background: var(--xntree-moving-color);
}
/*.xntree-outer .xntree-item:not(.xn-onmoving,.on):hover{*/
/*    background: var(--xntree-hover-color);*/
/*}*/
.xntree-outer:not(.xn-moving) .xntree-item:not(.on):hover{
    background: var(--xntree-hover-color);
}
.xntree-outer .xn-searchedkey{
    color: #fff;
    background: var(--xntree-searched-color);
}

