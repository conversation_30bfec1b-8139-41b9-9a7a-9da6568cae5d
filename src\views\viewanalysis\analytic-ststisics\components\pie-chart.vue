<!--
    * @FileDescription: 任务审批状态统计
    * @Author: H
    * @Date: 2024/07/25
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="chart">
        <div class="chart-title">
            <div class="chart-dot"></div>
            <p>{{ title }}</p>
        </div>
        <div id="pie" class="pie" ref="pie"></div>
        <div class="pie-icon">
            <div class="pie-content">
                <img :src="imgList[urlType]" alt="">
            </div>
        </div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import CountTo from 'vue-count-to';
export default {
    components: {
        CountTo
    },
    props: {
        urlType: {
            type: Number,
            default: 0
        },
        totalCount: {
            type: Number,
            default: 0
        },
        picData: {
            type: Array,
            default: () => {
                let list = [
                    { value: '75', name: '有效' },
                    { value: '25', name: '无效' },
                ];
                return list
            }
        },
        title: {
            type: String,
            default: ''
        }
    },  
    data() {
        return {
            myEchart: null,
            imgList: {
                0: require(`@/assets/img/card/icon_realtime.png`),
                1: require(`@/assets/img/card/icon_history.png`),
                2: require(`@/assets/img/card/icon_file.png`),
            },
            pieList: [
                { value: '31.35', name: '未审核', rate: '0', type: 'running' },
                { value: '33.85', name: '驳回', rate: '0', type: 'stopped' },
                { value: '34.83', name: '通过', rate: '0', type: 'completed' },
            ],
        }
    },
    mounted() {
        // this.init(this.picData)
    },
    methods: {
        init(list) {
            this.pieList.forEach(ite => {
                if(ite.type == 'running') {
                    ite.value = list.running;
                    ite.rate = this.parseValue(list, list.running);
                }else if(ite.type == 'stopped'){
                    ite.value = list.stopped;
                    ite.rate = this.parseValue(list, list.stopped);
                }else if(ite.type == 'completed') {
                    ite.value = list.completed;
                    ite.rate = this.parseValue(list, list.completed);
                }
            })
            this.pieChart(this.pieList)
        },
        parseValue(obj, value) {
            let num = 0;
            num = ( obj.running || 0 ) + ( obj.stopped || 0 ) + ( obj.completed || 0 );
            if(value == 0){
                return 0;
            }else{
                let rate = (value / num)*100;
                return rate.toFixed(2); 
            }
        },
        pieChart(list) {
            this.myEchart = echarts.init(this.$refs.pie)
            let option = {
                legend: {
                    bottom: '1%',
                    left: 'center',
                    icon: 'circle',
                    itemWidth: 10,
                    itemHeight: 10,
                    itemStyle: {
                        borderRadius: 5,
                    },
                    textStyle: {
                        color: '#567BBB'
                    }
                },
                grid: {
                    top: '15px',
                    left: '30px',
                    right: '20px',
                    bottom: '40px'
                },
                series: [
                    {
                        name: '解析数据总量',
                        type: 'pie',
                        radius: [ '45%', '60%' ],
                        data: list,
                        label: {
                            show: true,
                            // formatter: "{d}%\n" + "{b}",
                            formatter: (params) => {
                                const name = params.name;
                                if(name == '未审核') {
                                    return `{a|${params.data.rate}%}\n{t|${name}}`
                                }else if(name == '驳回') {
                                    return `{b|${params.data.rate}%}\n{t|${name}}`
                                }else if(name == '通过') {
                                    return `{c|${params.data.rate}%}\n{t|${name}}`
                                }
                            },
                            rich: {
                                t: {
                                    color: 'rgba(0, 0, 0, 0.6)',
                                    fontSize: 12,

                                },
                                a: {
                                    color: '#2C86F8',
                                    fontSize: 16,
                                },
                                b: {
                                    color: '#F29F4C',
                                    fontSize: 16,
                                },
                                c: {
                                    color: '#1FAF81',
                                    fontSize: 16,
                                },
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: (params) => {
                                    let colorList = [
                                        '#2C86F8', '#F29F4C', '#1FAF81'
                                    ]
                                    return colorList[params.dataIndex % colorList.length]
                                }
                            }
                        }
                    }
                ]
            };
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .chart-title{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        font-size: 16px;
        font-weight: 700;
        color: #3D3D3D;
        display: flex;
        align-items: center;
        .chart-dot{
            width: 8px;
            height: 8px;
            background: #2C86F8;
            margin-right: 10px;
        }
    }
    .pie{
        height: 100%;
        width: 100%; 
    }
    .pie-icon{
        position: absolute;
        width: 98px;
        height: 98px;
        border: 1px solid rgba(211,235,255, .2);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        justify-content: center;
        align-items: center;
        .pie-content{
            width: 80px;
            height: 80px;
            background: rgba(211, 235, 255, .2);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}
</style>