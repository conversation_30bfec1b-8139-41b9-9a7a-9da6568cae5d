<template>
  <div>
    <div class="issue-list auto-fill">
      <slide-unit-tree @selectOrgCode="selectOrgCode" :select-key="selectKey"></slide-unit-tree>
      <base-search class="search-box" @startSearch="startSearch" @exportDevice="exportDevice" @importData="importData">
      </base-search>
      <div class="over-flow total-box f-14 mb-sm">
        <!-- <p class="total-p fl base-text-color">
                共 <span class="font-red"> {{ pageData.totalCount }} </span> 条数据
            </p> -->
        <!-- <div class="over-flow">
          <Button type="primary" class="button-blue fr" @click="exportDevice">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-sm">导出</span>
          </Button>
          <Button type="primary" class="button-blue fr mr-sm" @click="importData">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-sm">获取异常设备</span>
          </Button>
          <Upload class="fr mr-sm"
                    action="/ivdg-device-detection-service/device/task/addOnlineDetectionDevice"
                    ref="upload"
                    :show-upload-list="false"
                    :before-upload="beforeUpload"
                    :on-success="importSuccess"
                    :on-error="importError"
                >
                    <Button type="primary" :loading="uploadLoading" :disabled="uploadLoading">
                        <i class="icon-font icon-daoruwentishebei f-12"></i>
                        <span class="vt-middle ml-sm">导入问题设备</span>
                    </Button>
                </Upload>
                <Button class="mr-sm fr" type="primary" @click="">
                    <i class="icon-font icon-shengchengwentiqingdan f-12"></i>
                    <span class="vt-middle ml-sm">相同设备合并</span>
                </Button>
        </div> -->
      </div>
      <div class="table-module auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #deviceId="{ row }">
            <span @click="deviceArchives(row)">
              <span class="font-active-color pointer">{{ row.deviceId }}</span>
            </span>
          </template>
          <template #action="{ row }">
            <ui-btn-tip icon="icon-shanchu3" content="删除" @click.native="removetDevice(row)"></ui-btn-tip>
            <!-- <span class="font-table-action pointer mr-sm" @click="splitShow">拆分</span>
					<span class="font-table-action pointer mr-sm">查看</span> -->
            <!-- <i class="icon-font icon-shanchu3"></i> -->
            <!-- <span class="font-table-action pointer" @click="removetDevice(row)">删除</span> -->
          </template>
          <template slot="generateStatus" slot-scope="{ row }">
            <span class="pointer status-tag" :style="{ 'background-color': bgColor[row.generateStatus] }">{{
              row.generateStatus === 0 ? '未生成' : '已生成'
            }}</span>
          </template>
          <template #errorDescription="{ row }">
            <Tooltip transfer max-width="600">
              <p class="tipvisible" v-if="!!row.outlineCount">
                离线：{{ row.outlineCount || 0 }}次&nbsp;<span>...</span>
              </p>
              <div slot="content">
                <p class="mt-sm">{{ handleDescriptionTime(row) }}</p>
                <p v-if="!!row.outlineCount">离线：{{ row.outlineCount || 0 }}次</p>
                <p v-if="!!row.detectionEndDate">最近离线：{{ row.detectionEndDate }}</p>
              </div>
            </Tooltip>
          </template>
        </ui-table>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
      <!-- <split-popup v-model="splitModal"></split-popup> -->
      <!-- <edit-view-order v-model="splitModal"></edit-view-order> -->
    </div>
  </div>
</template>
<script>
import governancetask from '@/config/api/governancetask';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'issuelist',
  props: {},
  data() {
    return {
      errorList: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      tableColumns: [
        { title: '序号', align: 'center', type: 'index', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          align: 'left',
          width: 200,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.sbgnlx,
          key: 'sbgnlxText',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: '设备类型',
          key: 'ptzTypeText',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        // { title: '经度', key: 'longitude', align: 'left' },
        // { title: '纬度', key: 'latitude', align: 'left' },
        {
          title: '异常原因',
          key: 'errorReason',
          align: 'left',
          tooltip: true,
          minWidth: 200,
        },
        // { title: '异常原因', key: 'errorReason', align: 'left' },
        {
          title: '异常描述',
          slot: 'errorDescription',
          align: 'left',
          width: 130,
        },
        { title: '问题来源', key: 'dataSourceText', align: 'left', width: 120 },
        {
          title: '是否生成工单',
          slot: 'generateStatus',
          align: 'left',
          width: 100,
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 60,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      minusTable: 350,
      loading: false,
      searchData: {
        generateStatus: '0',
        pageNumber: 1,
        pageSize: 20,
      },
      uploadLoading: false,
      selectKey: null,
      splitModal: false,
      bgColor: ['#787878', '#0E8F0E'],
    };
  },
  activated() {
    // this.initErrorList()
    if (!this.searchData.orgCode) {
      //this.initTableList()
    }
  },
  methods: {
    importData() {
      this.$UiConfirm({
        content: '您要开始获取异常设备，是否确认?',
        title: '警告',
        hasLoading: true,
      })
        .then(async ($vm) => {
          $vm.loading = true;
          try {
            let res = await this.$http.post(governancetask.importExcept);
            $vm.loading = false;
            $vm.visible = false;
            $vm.$el.parentNode.removeChild($vm.$el);
            this.$Message.success(res.data.msg);
            this.initTableList();
          } catch (err) {
            $vm.loading = false;
            console.log(err);
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { deviceId: item.deviceId },
      });
      window.open(routeData.href, '_blank');
    },
    handleDescriptionTime(row) {
      if (!!row.detectionStartDate && !!row.detectionEndDate) {
        return `时间范围：${row.detectionStartDate.slice(0, 10)} - ${row.detectionEndDate.slice(5, 10)}`;
      }
    },
    splitShow() {
      this.splitModal = true;
    },
    startSearch(searchData) {
      Object.assign(this.searchData, searchData);
      this.searchData.pageNumber = 1;
      this.initTableList();
    },
    selectOrgCode(data) {
      this.searchData.orgCode = data.orgCode;
      this.selectKey = data.orgCode;
      this.initTableList();
    },
    beforeUpload() {
      this.uploadLoading = true;
    },
    importSuccess(res) {
      this.$Message.success(res.msg);
      this.uploadLoading = false;
    },
    importError() {
      this.uploadLoading = false;
      this.$Message.error('导入文件失败!');
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.initTableList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.initTableList();
    },
    async importDevice() {
      try {
        await this.$http.post(governancetask.importProblemDevice);
      } catch (err) {
        console.log(err);
      }
    },
    async initErrorList() {
      try {
        let res = await this.$http.get(equipmentassets.queryErrorMessages);
        this.errorList = res.data.data.map((row) => {
          return {
            name: row,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    async initTableList() {
      try {
        for (let i in this.searchData) {
          !this.searchData[i] ? delete this.searchData[i] : null;
        }
        this.loading = true;
        let params = Object.assign({}, this.searchData);
        // params.generateWorkOrderStatus = 2
        let { data } = await this.$http.post(governancetask.getDevicePageList, params);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    removetDevice(row) {
      // this.$Modal.confirm({
      //   title: "警告",
      //   content: `您要删除数据：${row.deviceName}  这项，是否确认?`,
      //   onOk: async () => {
      //     try {
      //       let { data } = await this.$http.delete(
      //         governancetask.removetDevice + `/${row.id}`
      //       );
      //       this.$Message.success(data.msg);
      //       this.initTableList();
      //     } catch (err) {s
      //       console.log(err);
      //     }
      //   },
      // });

      this.$UiConfirm({
        content: `您要删除数据：${row.deviceName}  这项，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          await this.$http.delete(governancetask.removetDevice + `/${row.id}`);
          this.$Message.success('删除成功！');
          this.initTableList();
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async exportDevice() {
      try {
        let res = await this.$http.post(governancetask.exportDevice, this.searchData, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  components: {
    BaseSearch: require('./base-search').default,
    SlideUnitTree: require('@/components/slide-unit-tree').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.issue-list {
  overflow: hidden;
  position: relative;
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
  .search-box {
    // padding: 20px 20px 0;
  }
  .total-box {
    // padding: 0 20px;
  }
  .ui-table {
    // padding: 0 20px;
  }
  .total-p {
    height: 40px;
    line-height: 40px;
  }
  .slide-tree-box {
    position: relative;
  }
  .tipvisible {
    span {
      font-size: 18px;
      color: var(--color-primary);
    }
    cursor: pointer;
  }
  .status-tag {
    display: inline-block;
    padding: 3px 10px;
    font-size: 14px;
    border-radius: 4px;
    color: #ffffff;
  }
}
</style>
