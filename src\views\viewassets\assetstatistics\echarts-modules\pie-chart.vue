<template>
  <div class="pie-chart-container">
    <tab-title v-model="searchData.dataKey" :data="config.tabData" @on-change="onChangeTitle">
      <template #filter>
        <slot :name="config.filterSlot || 'filter'" :searchData="searchData" :initList="handleChangeSelect">
          <drop-select
            v-model="searchData.sbdwlxList"
            multiple
            :data="dropData"
            @on-change="handleChangeSelect"
          ></drop-select>
        </slot>
      </template>
    </tab-title>
    <div class="echarts-container" v-ui-loading="{ loading, tableData: echartsData }">
      <draw-echarts :echart-option="chartsOption"></draw-echarts>
      <div class="echarts-legend">
        <div
          class="f-14 base-text-color"
          v-for="(item, index) in config.legend.filter((value) => !value.hidden)"
          :key="index"
        >
          <span
            class="inline color-block mr-xs"
            :style="`background: linear-gradient(180deg, ${item.color[0]} 0%, ${item.color[1]} 100%);`"
          ></span>
          <span class="mr-xs">{{ item.label }}</span>
          <span class="mr-md" :style="`color: ${item.color[1]}; `">
            <countTo ref="countTo" :start-val="0" :end-val="parseInt(echartsData[item.key] ?? 0)" :duration="1000">
            </countTo>
          </span>
          <slot :name="item.key" :item="item" :data="echartsData"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import countTo from 'vue-count-to';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  name: 'pie-chart',
  components: {
    countTo,
    TabTitle: require('../components/tab-title.vue').default,
    DropSelect: require('../components/drop-select.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    homeConfig: {
      default: () => ({}),
    },
    activeTab: {
      default: '1',
    },
    config: {
      default: () => ({}),
    },
    //顶部统计
    statisticList: {
      default: () => ({}),
    },
  },
  data() {
    return {
      dropValue: '1',
      dropData: [
        { label: '一类点', id: '1' },
        { label: '二三类点', id: '2' },
        { label: '内部监控', id: '4' },
      ],
      searchData: {
        dataKey: 'phystatus_overview',
        sbdwlxList: [],
      },
      loading: false,
      chartsOption: {},
      echartsData: {},
      total: 0,
    };
  },
  computed: {
    multipleValue() {
      return Object.keys(this.homeConfig).length > 0 && this.statisticList.length > 0;
    },
  },
  watch: {
    activeTab: {
      async handler() {
        await this.postQueryPropertyStatistics();
        this.initQueryPropertyStatistics();
        this.initChart();
      },
    },
    multipleValue: {
      async handler(val) {
        if (val) {
          await this.postQueryPropertyStatistics();
          this.initQueryPropertyStatistics();
          this.initChart();
        }
      },
      deep: true,
    },
  },
  filter: {},
  created() {
    this.searchData.dataKey = this.config.tabData[0]['id'];
  },
  methods: {
    onChangeTitle() {},
    initQueryPropertyStatistics() {
      if (this.config.statisticKey) {
        let index = this.statisticList.findIndex((item) => item.filedName === this.config.statisticKey);
        this.echartsData['undetectedAmount'] =
          this.statisticList[index]['value'] - (this.echartsData.amount + this.echartsData.unAmount);
        this.echartsData['total'] = this.statisticList[index]['value'];

        this.echartsData['amountRate'] = ((this.echartsData.amount / this.statisticList[index]['value']) * 100).toFixed(
          2,
        );
        this.echartsData['unAmountRate'] = (
          (this.echartsData.unAmount / this.statisticList[index]['value']) *
          100
        ).toFixed(2);
      } else {
        this.echartsData['total'] = this.echartsData.amount + this.echartsData.unAmount;
      }
    },
    async handleChangeSelect() {
      await this.postQueryPropertyStatistics();
      this.initQueryPropertyStatistics();
      this.initChart();
    },
    async postQueryPropertyStatistics() {
      try {
        this.loading = true;
        let { regionCode } = this.homeConfig;
        let params = {
          civilCode: regionCode,
          type: this.activeTab,
          // sbgnlx: '1',
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.postQueryPropertyStatistics, params);
        this.echartsData = data || { amount: 0, unAmount: 0 };
      } catch (e) {
        this.echartsData = { amount: 0, unAmount: 0 };
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    initChart() {
      let options = {
        data: [],
        total: 0,
      };
      this.config.legend.forEach((item) => {
        options.total = this.echartsData.total;
        options.data.push({
          value: this.echartsData[item.key],
          name: item.label,
          itemStyle: {
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              1,
              0,
              [
                {
                  offset: 0,
                  color: item.color[0], // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: item.color[1], // 100% 处的颜色
                },
              ],
              false,
            ),
            // borderWidth: 2,
            // borderColor: '#08264d',
          },
        });
      });
      options.total = this.$options.filters.numberInfo(options.total);
      this.chartsOption = this.$util.doEcharts.DevicePropertyStatistics(options);
    },
  },
};
</script>

<style lang="less" scoped>
.color-block {
  width: 9px;
  height: 9px;
}
.pie-chart-container {
  width: 286px;
  //height: 288px;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .echarts-container {
    //height: calc(100% - 41px);
    width: 100%;
    @{_deep} .echarts {
      height: 200px !important;
    }

    .echarts-legend {
      padding: 0 0 15px 20px;
    }
  }
}
</style>
