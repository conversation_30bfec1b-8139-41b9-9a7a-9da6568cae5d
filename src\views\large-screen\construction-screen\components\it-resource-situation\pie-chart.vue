<!--
    * @FileDescription: 
    * @Author: H
    * @Date: 2024/06/11
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="pieChart" class="pieChart" ref="pieChart"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
export default {
    props: {
        name: {
            type: String,
            default: '摄像机覆盖率'
        },
        colorData: {
            type: Array,
            default: () => {
                return ['rgba(45,161,255,1)', 'rgba(14,223,255,1)']
            }
        }
    },
    data() {
        return {
            myEchart: null
        }
    },
    mounted() {
        // this.init()
    },
    methods:{
        init(data) {
            this.pieChart(data)
        },
        pieChart(data=0){
            this.myEchart = echarts.init(this.$refs.pieChart);
            let option = {
                title:{
                    text: this.name,
                    left: '50%',
                    bottom: '0',
                    textAlign: 'center',
                    textStyle: {
                        fontWeight: 'normal',
                        fontSize: '16',
                        color: '#ffffff',
                        textAlign: 'center',
                    }
                },
                series: [
                    {
                        name: this.name,
                        type: 'pie',
                        clockWise: false,
                        radius: [55, 60],
                        itemStyle: {
                            // normal: {
                            //     color: 'rgba(24, 98, 187, 1)',
                            //     shadowColor: 'rgba(24, 98, 187, 1)',
                            //     shadowBlur: 0,
                            //     label: {
                            //         show: false,
                            //     },
                            //     labelLine: {
                            //         show: false
                            //     }
                            // }
                            normal: {
                                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                                    this.colorData[0], this.colorData[1],
                                    ].map((color, offset) => ({
                                        color,
                                        offset
                                    }))
                                ),
                                label: {
                                    show: false,
                                },
                                labelLine: {
                                    show: false
                                }
                            }
                        },
                        hoverAnimation: false,
                        center: ['50%', '50%'],
                        data: [
                            {
                                value: data,
                                label: {
                                    normal: {
                                        formatter: (params) => {
                                            return params.value + '%'
                                        },
                                        position: 'center',
                                        show: true,
                                        textStyle: {
                                            fontSize: '20',
                                            fontWeight: 'bold',
                                            color: '#F1FCFF',
                                        }
                                    }
                                }
                            },
                            {
                                value: 100 - data,
                                name: 'invisible',
                                itemStyle: {
                                    normal: {
                                        color: 'rgba(24, 98, 187, 0.2)'
                                    },
                                    emphasis: {
                                        color: 'rgba(24, 98, 187, 0.2)'
                                    }
                                }
                            }
                        ]
                    }
                ]
            };
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .pieChart{
        height: 100%;
        width: 100%;  
    }
}
</style>