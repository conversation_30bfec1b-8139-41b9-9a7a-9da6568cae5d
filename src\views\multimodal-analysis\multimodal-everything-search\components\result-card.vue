<!--
 * @Date: 2025-03-11 16:35:37
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-12 17:33:08
 * @FilePath: \icbd-view\src\views\multimodal-analysis\multimodal-analysis-lib\components\result-card.vue
-->
<template>
  <div class="result-card-box">
    <Checkbox
      v-show="showCheckbox"
      class="check-box"
      v-model="data.isChecked"
      @click.native.stop
      @on-change="(e) => checkHandler(e)"
    ></Checkbox>
    <div class="card-box-image" ref="regionsBox">
      <div class="translate-box" :style="{ transform: `scale(${rate})` }">
        <div class="image-box" ref="imgBox">
          <img :src="data.picUrl" @load="loadImage" />
          <div
            v-if="selectBox"
            class="select-img-preview"
            :style="{
              borderWidth: `${2/rate}px`,
              left: imgBoxList.x + 'px',
              top: imgBoxList.y + 'px',
              width: imgBoxList.w + 'px',
              height: imgBoxList.h + 'px',
            }"
          ></div>
        </div>
      </div>
      <span class="num" v-if="data.score"
        >{{ (data.score * 100)?.toFixed(2) }}%</span
      >
    </div>
    <div class="list-content">
      <div class="item">
        <ui-icon type="time" :size="14"></ui-icon
        ><span>{{ data.absTime }}</span>
      </div>
      <div class="item">
        <ui-icon type="location" :size="14"></ui-icon
        ><span class="text-overflow" :title="data?.deviceName">{{
          data?.deviceName
        }}</span>
      </div>
      <div class="more" @click.stop v-if="showMoreTip">
        <Poptip trigger="hover" placement="left-end">
          <i class="iconfont icon-gengduo"></i>
          <div class="mark-poptip" slot="content">
            <p @click="archivesPage(1)">
              <i class="iconfont icon-renlian1"></i>以图搜图
            </p>
            <p @click="archivesPage(2)">
              <i class="iconfont icon-renlian1"></i>身份核验
            </p>
            <p @click="onAction(3)">
              <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
            </p>
          </div>
        </Poptip>
      </div>
    </div>
  </div>
</template>

<script>
import { transition } from "d3";
export default {
  name: "resultCard",
  props: {
    showCheckbox: {
      type: Boolean,
      default: false,
    },
    showMoreTip: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      selectBox: false,
      imgBoxList: {},
      rate: 1,
    };
  },
  methods: {
    // 框选处理
    loadImage($event) {
      this.imgBoxList = this.data.ytRect;
      this.selectBox = !!this.imgBoxList;
      if (!this.selectBox) return;
      const { x, y, w, h } = this.imgBoxList;
      const box = this.$refs.imgBox;
      // const img =  $event.currentTarget;
      // const imgNaturalWidth = img.naturalWidth;
      // const imgNaturalHeight = img.naturalHeight;

      const regionsBox = this.$refs.regionsBox;
      const regionsBoxWidth = regionsBox.clientWidth;
      const regionsBoxHeight = regionsBox.clientHeight;
      this.rate = 1;
      const boxRatio = regionsBoxWidth / regionsBoxHeight;
      const imgRatio = w/h ;// imgNaturalWidth / imgNaturalHeight;
      let size = 1;
      if (imgRatio >= boxRatio) {
        size = (w + 10) / regionsBoxWidth;
      } else {
        size = (h + 10) / regionsBoxHeight;
      }
      box.style.transform = `translate(${(x - (regionsBoxWidth - w) / 2) * -1}px, ${
        (y - (regionsBoxHeight - h) / 2) * -1
      }px)`;
      this.rate = 1 / size 
    },
    /**
     * 跳转到一人一档页面
     */
    archivesPage(item, index) {
      let pageUrl = "";
      if (index == 1) {
        pageUrl =
          "/viewanalysis/viewParsingLibrary?sectionName=faceContent&noMenu=1";
      } else {
        pageUrl = "/model-market/face-warfare/identity-authentivation?noMenu=1";
      }
      const { href } = this.$router.resolve({
        path: pageUrl,
        query: {
          imgUrl: this.data.picUrl,
        },
      });
      window.open(href, "_blank");
    },
    onAction(type, ...args) {
      this.$emit("onAction", { ...this.data }, type, ...args);
    },
    checkHandler(val) {
      this.$emit("checkHandler", val);
    },
  },
};
</script>

<style lang="less" scoped>
.result-card-box {
  width: 100%;
  height: 254px;
  background: #f9f9f9;
  border: 1px solid #d3d7de;
  border-radius: 4px;
  padding: 10px;
  position: relative;
  &:hover {
    border: 1px solid #2c86f8;
    .check-box {
      display: block;
    }
  }
  .card-box-image {
    width: 100%;
    height: 180px;
    position: relative;
    overflow: hidden;
    .translate-box {
      width: 100%;
      height: 100%;
      transform-origin: center center;
    }
    .image-box {
      position: relative;
      width: fit-content;
      img {
        display: block;
      }
      .select-img-preview {
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(255, 234, 75, 0.1);
        border-radius: 1px;
        border: 1px solid rgba(255, 234, 75, 1);
      }
    }
    .num {
      position: absolute;
      left: 0;
      top: 0;
      font-size: 12px;
      padding: 2px 5px;
      border-radius: 4px;
      color: #fff;
      background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    }
  }
  .list-content {
    position: relative;
    width: 100%;
    padding-top: 10px;
    .item {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      span {
        padding-left: 6px;
      }
    }
    .more {
      position: absolute;
      right: 0px;
      top: 10px;
      bottom: 35px;
      color: #2c86f8;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      .icon-gengduo {
        transform: rotate(90deg);
        transition: 0.1s;
        display: inline-block;
      }
      p:hover {
        color: #2c86f8;
      }
      &:hover {
        background: #2c86f8;
        color: #fff;

        .icon-gengduo {
          transform: rotate(0deg);
          transition: 0.1s;
        }
        border-radius: 10px;
      }
      /deep/ .ivu-poptip-popper {
        min-width: 150px !important;
        width: 40px !important;
        height: auto;
      }
      /deep/.ivu-poptip-body {
        height: auto !important;
      }
      .mark-poptip {
        color: #000;
        cursor: pointer;
        text-align: left;
        /deep/ .ivu-icon-ios-add-circle-outline {
          font-weight: 600;
        }
      }
    }
  }
  .check-box {
    position: absolute;
    z-index: 10;
    top: 5px;
    left: 5px;
    display: none;
  }
}
</style>
