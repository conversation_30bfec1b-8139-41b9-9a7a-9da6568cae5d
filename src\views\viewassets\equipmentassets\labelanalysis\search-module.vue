<template>
  <div class="search-module">
    <div class="search-wrapper">
      <ui-label class="inline mb-10 mr-md" label="组织机构">
        <api-organization-tree
          ref="orgTree"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label :label="global.filedEnum.deviceId" :width="70" class="mr-md">
        <Input
          v-model="searchData.deviceId"
          :placeholder="`请输入${global.filedEnum.deviceId}`"
          class="width-md"
        ></Input>
      </ui-label>
      <ui-label :label="global.filedEnum.deviceName" :width="70" class="mr-md">
        <Input
          v-model="searchData.deviceName"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
          class="width-md"
        ></Input>
      </ui-label>
      <ui-label label="IPV4地址" :width="70" class="mr-lg">
        <Input v-model="searchData.ipAddr" placeholder="请输入IPV4地址" class="width-md"></Input>
      </ui-label>
      <!-- <ui-label label="IPV6地址" :width="70" class="mr-lg">
        <Input v-model="searchData.ipv6Addr" placeholder="请输入" class="width-md"></Input>
      </ui-label> -->
      <ui-label :label="global.filedEnum.longitude" :width="40" class="mr-md">
        <Input
          v-model="searchData.longitude"
          :placeholder="`请输入${global.filedEnum.longitude}`"
          class="width-md"
        ></Input>
      </ui-label>
      <ui-label :label="global.filedEnum.latitude" :width="40" class="mr-md">
        <Input
          v-model="searchData.latitude"
          :placeholder="`请输入${global.filedEnum.latitude}`"
          class="width-md"
        ></Input>
      </ui-label>
      <div class="button-div ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </div>
      <!-- 监控点位类型 -->
      <ui-label class="inline mr-md mt-sm" :label="global.filedEnum.sbdwlx">
        <Select
          class="width-md"
          v-model="searchData.sbdwlx"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          clearable
        >
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>

      <!-- 摄像机功能类型 -->
      <ui-label class="inline mr-md mt-sm" :label="global.filedEnum.sbgnlx">
        <Select
          class="width-md"
          v-model="searchData.sbgnlx"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
          clearable
        >
          <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-md mt-sm" label="设备重点类型">
        <Select class="width-md" v-model="searchData.isImportant" clearable placeholder="请选择设备重点类型">
          <Option label="普通设备" :value="0"></Option>
          <Option label="重点设备" :value="1"></Option>
        </Select>
      </ui-label>
      <!-- <ui-label label="数据来源" :width="70" class="mr-lg">
        <Input class="width-sm" v-model="searchData.sourceId" placeholder="请输入数据来源"></Input>
      </ui-label> -->
    </div>
    <!-- <div class="option">
      <Button type="primary" @click="manualLink">
        <i class="icon-font icon-shoudongguanlian"></i>
        <span class="inilne vt-middle ml-sm">手动关联</span>
      </Button>
      <Button type="primary" class="ml-sm" @click="analysisConfig">
        <i class="icon-font icon-zidongfenxipeizhi"></i>
        <span class="inilne vt-middle ml-sm">自动分析配置</span>
      </Button>
    </div> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {},
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        ipAddr: '',
        ipv6Addr: '',
        longitude: '',
        latitude: '',
        orgCode: '',
        sbdwlx: '',
        sbgnlx: '',
        isImportant: '',
      },
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      questionList: [],
      statusList: [],
      conditionList: [
        {
          id: 1,
          value: this.global.filedEnum.deviceId,
          type: 'input',
          fileName: 'deviceId',
          disabled: true,
        },
        {
          id: 2,
          value: this.global.filedEnum.deviceName,
          type: 'input',
          fileName: 'deviceName',
          disabled: true,
        },
        {
          id: 3,
          value: 'IPV4地址',
          type: 'input',
          fileName: 'ipAddr',
          disabled: true,
        },
        {
          id: 4,
          value: 'IPV6地址',
          type: 'input',
          fileName: 'ipv6Addr',
          disabled: true,
        },
        {
          id: 5,
          value: this.global.filedEnum.longitude,
          type: 'input',
          fileName: 'longitude',
          disabled: true,
        },
        {
          id: 6,
          value: this.global.filedEnum.latitude,
          type: 'input',
          fileName: 'latitude',
          disabled: true,
        },
        { id: 7, value: '检测状态', type: 'select', disabled: true },
        {
          id: 8,
          value: '录入时间',
          type: 'timePicker',
          disabled: true,
        },
      ],
      sourceId: '',
    };
  },

  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
    }),
  },
  created() {
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.selectedOrgTree(this.defaultSelectedOrg);
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.search();
      this.$emit('selectedOrgTree', val);
    },
    selectSource(val) {
      let sourceList = val ? [val.sourceId] : [];
      this.searchData.sourceIds = sourceList;
      this.sourceId = val.sourceId;
    },
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    search() {
      this.$emit('search', this.searchData);
    },
    clear() {
      this.sourceId = '';
      this.resetSearchDataMx(this.searchData);
      this.$emit('clear', this.searchData);
    },
    // manualLink() {
    //   this.$emit('manualLink')
    // },
    // analysisConfig() {
    //   this.$emit('analysisConfig')
    // },
  },
};
</script>
<style lang="less" scoped>
.width-md {
  width: 180px !important;
}
.search-module {
  padding: 10px 20px 0;
  font-size: 14px;
  .search-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    // position: relative;
    .button-div {
      &.add-button {
        // position: absolute;
        right: 35px;
      }
    }
  }
  .option {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    // position: absolute;
    // right: 0;
    // bottom: 0;
  }
  .panel-wrapper {
    padding-left: 20px;
  }
}
</style>
