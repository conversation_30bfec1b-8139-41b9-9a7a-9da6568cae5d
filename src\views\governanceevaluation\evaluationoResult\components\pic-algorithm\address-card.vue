<template>
  <!-- 大图右下角--地址相关信息 -->
  <div class="card-content">
    <p class="text-title">
      <i class="icon-font icon-danganxinxi f-14 mr-xs"></i>
      <span>档案信息:</span>
    </p>
    <p class="p-label p-tip">
      <span>行政区划：</span>
      <span class="span-label" v-if="modeType === 'video'">
        {{ currentRow?.areaInfo?.deviceRegion }}
      </span>
      <span class="span-label" v-else>
        {{
          currentRow?.urlOcrDetectResultBo?.optimizationDetailVo?.areaInfo?.deviceRegion ||
          currentRow?.urlOcrDetectResultBo?.standbyDetailVo?.areaInfo?.deviceRegion ||
          ''
        }}
      </span>
    </p>
    <p class="p-label p-tip">
      <span>设备名称：</span>
      <span class="span-label" v-if="modeType === 'video'">{{ currentRow?.areaInfo?.deviceAddress }}</span>
      <span class="span-label" v-else>{{ currentRow.deviceName }}</span>
    </p>
    <div class="text-title">
      <i class="icon-font icon-shibieneirong f-14 mr-xs"></i>
      <span>识别内容：</span>
      <div
        v-for="(algorithmItem, algorithmIndex) in currentAlgorithmInfo.addressAlgorithmTextList"
        :key="algorithmIndex"
      >
        <span class="sub-title" v-if="currentAlgorithmInfo.addressAlgorithmTextList.length > 1 && showAlgorithmName">
          {{ algorithmItem.algorithmName }}：
        </span>
        <p
          class="tooltip-text p-tip"
          v-for="(text, textIndex) in algorithmItem.textList"
          :key="`${algorithmIndex}-${textIndex}`"
        >
          {{ text }}
        </p>
      </div>
    </div>
    <p class="text-title">
      <i class="icon-font icon-jiancejieguo1 f-14 mr-xs"></i>
      <span>检测结果：</span>
    </p>
    <div class="result">
      <p class="p-text" v-for="resultInfo in currentAlgorithmInfo.addressData.resultInfo" :key="resultInfo.key">
        {{ resultInfo.value }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentRow: {
      type: Object,
    },
    currentAlgorithmInfo: {
      type: Object,
    },
    showAlgorithmName: {
      type: Boolean,
      default: true,
    },
    // normal: 非视频流（如： 人车）    video: 视频流
    modeType: {
      type: String,
      default: 'normal',
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .p-tip,
  .tooltip-text {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}
.card-content {
  padding: 8px 10px;
  background: var(--bg-card);
  font-size: 13px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  color: var(--color-content);
  line-height: 21px;
  .p-label {
    color: rgba(0, 0, 0, 0.8);
    .span-label {
      white-space: pre-wrap;
    }
  }
  .text-title {
    padding: 1px 0;
    color: var(--color-title);
  }
  .result {
    color: var(--color-failed);
    white-space: pre-wrap;
  }
  .p-text {
    position: relative;
    padding: 1px 0 1px 18px;
    color: var(--color-failed);
    &::before {
      content: ' ';
      width: 11px;
      height: 11px;
      display: inline-block;
      background: var(--bg-quadrate-outside);
      transform: rotate(45deg);
      position: absolute;
      top: 5px;
      left: 0;
    }
    &::after {
      content: ' ';
      width: 5px;
      height: 5px;
      display: inline-block;
      background: var(--bg-quadrate-interior);
      transform: rotate(45deg);
      position: absolute;
      top: 8px;
      left: 3px;
    }
  }
  .p-tip {
    position: relative;
    padding: 1px 0 1px 18px;
    color: rgba(0, 0, 0, 0.8);
    &::before {
      content: ' ';
      width: 11px;
      height: 11px;
      display: inline-block;
      border-radius: 50%;
      background: var(--bg-circle-outside);
      position: absolute;
      top: 6px;
      left: 0;
    }
    &::after {
      content: ' ';
      width: 5px;
      height: 5px;
      display: inline-block;
      border-radius: 50%;
      background: var(--bg-circle-interior);
      position: absolute;
      top: 9px;
      left: 3px;
    }
  }
  .sub-title {
    color: var(--color-sub-title);
  }
  .tooltip-text {
    white-space: pre-line;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.8);
  }
}
</style>
