<template>
  <div class="thematic-database">
    <div class="container detail-info">
      <div class="info">
        <img :src="imgUrl" class="info-img" alt />
        <div class="info-content">
          <div class="name primary">{{currentRow.libName}}</div>
          <div class="info-item">
            <span class="label title-color">更新时间：</span>
            <span class="value text-color">{{currentRow.modifyTime}}</span>
          </div>
          <div class="info-item">
            <span class="label title-color">备注：</span>
            <span class="value text-color">{{currentRow.remark}}</span>
          </div>
        </div>
      </div>
      <div class="statistics">
        <div class="label title-color">车辆数量：</div>
        <count-to :start-val="0" :end-val="currentRow.libCount || 0" :duration="1000" class="num color-warning"></count-to>
      </div>
    </div>
    <div class="container content">
      <div class="search card-border-color">
        <Form ref="searchData" :model="searchData" inline :class="visible ? 'advanced-search-show' : ''">
          <FormItem prop="plateNo" label="车牌号码:">
            <Input v-model="searchData.plateNo" placeholder="请输入车牌号" />
          </FormItem>
          <FormItem prop="idCardNo" label="车主身份证号:">
            <Input v-model="searchData.idCardNo" placeholder="请输入车主身份证号" />
          </FormItem>
          <FormItem class="btn-group">
            <Button type="primary" @click="init()">查询</Button>
            <Button @click="resetHandle">重置</Button>
            <!-- <span class="advanced-search-text" @click.stop="advancedSearchHandle">
              <img src="@/assets/img/down-circle-icon.png" />
              <span class="primary">{{ visible ? '普通检索' : searchText === '高级检索' ? '高级检索' : '更多条件' }}</span>
            </span> -->
          </FormItem>
          <div class="advanced-search" v-show="visible">
            <div class="upload-input-list">
              <!-- <uiUploadImg ref="uploadImg" v-model="images" size="small" :algorithmType="1" @imgUrlChange="imgUrlChange" /> -->
            </div>
            <div class="other-search">
              <FormItem label="车牌颜色:" prop="name">
                <Input placeholder="请输入" v-model="searchData.name" maxlength="50" />
              </FormItem>
              <FormItem label="车辆品牌:" prop="name">
                <Input placeholder="请输入" v-model="searchData.name" maxlength="50" />
              </FormItem>
              <FormItem label="车身颜色:" prop="name">
                <Input placeholder="请输入" v-model="searchData.name" maxlength="50" />
              </FormItem>
              <FormItem label="车主:" prop="name">
                <Input placeholder="请输入" v-model="searchData.name" maxlength="50" />
              </FormItem>
              <FormItem label="手机号:" prop="name">
                <Input placeholder="请输入" v-model="searchData.name" maxlength="50" />
              </FormItem>
            </div>
            <!-- 选择标签 -->
            <!-- <LabelModal @setCheckedLabel="setCheckedLabel" ref="labelModal" /> -->
          </div>
        </Form>
        <div class="btn-group">
          <Button @click="addVehicleHandle"><i class="iconfont icon-jia"></i>新增</Button>
          <Button @click="vehicleDelHandle('')"><i class="iconfont icon-shanchu"></i>删除</Button>
          <Button @click="importFn"><i class="iconfont icon-daoru"></i>导入</Button>
          <Button @click="exportFn"><i class="iconfont icon-export"></i>导出</Button>
          <UploadFile style="display: none;" 
          url="/icbd-business-manager-app/lib/vehicleLibInfo/importVehicleData"
          :featureLibId="$route.query.featureLibId" @successPut="fileUploadSuccess" ref="uploadFile" />
        </div>
      </div>
      <div class="personnel-table">
        <ui-table :columns="columns" :data="tableData" @on-selection-change="selectionChangeHandle">
          <template #idPhotoList="{ row }">
            <img v-viewer :src="row.photoUrlList[0].photoUrl" class="personnel-photo" alt />
          </template>
          <template #plateColor="{ row }">
            <div>{{ row.plateColor | commonFiltering(licensePlateColorList) }}</div>
          </template>
          <template #vehicleBrand="{ row }">
            <div>{{ row.vehicleBrand | commonFiltering(vehicleBrandList) }}</div>
          </template>
          <template #vehicleColor="{ row }">
            <div>{{ row.vehicleColor | commonFiltering(vehicleColorList) }}</div>
          </template>
          <template #action="{ row }">
            <div class="btn-tips">
              <!-- <ui-btn-tip content="档案" icon="icon-dangan2" class="primary" @click.native="archivesHandle(row)" /> -->
              <ui-btn-tip content="详情" icon="icon-xiangqing" class="primary" @click.native="vehicleDetailHandle(row)" />
              <ui-btn-tip content="编辑" icon="icon-bianji" class="primary" @click.native="vehicleEditHandle(row)" />
              <ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="vehicleDelHandle(row)" />
            </div>
          </template>
        </ui-table>
      </div>
      <ui-page
        :current="pageInfo.pageNumber" 
        :total="pageInfo.total" 
        :page-size="pageInfo.pageSize" 
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
    <!-- 新增/编辑车辆 -->
    <AddModal ref="addModal" 
    :currentRow="currentRow"/>
    <!-- 详情 -->
    <DetailModal ref="detailModal" />
  </div>
</template>
<script>
  import { queryVehicleLibInfoPageList, deleteVehicleLibInfo, batchDeleteVehicleLibInfo, doExportVehicleData } from '@/api/data-warehouse'
import { mapActions, mapGetters } from 'vuex'
import CountTo from 'vue-count-to'
import AddModal from './components/add-modal.vue'
import DetailModal from './components/detail-modal.vue'
import UploadFile from '@/components/ui-upload-file'
export default {
  components: {
    CountTo,
    AddModal,
      UploadFile,
    DetailModal
  },
  data() {
    return {
      visible: false,
      currentRow: {}, 
      searchText: '高级检索',
      imgUrl: require('@/assets/img/default-img/vehicle_archives_default.png'),
      searchData: {
        plateNo: '',
        idCardNo: ''
      },
      columns: [
        { type: 'selection', width: 60, align: 'center' },
        { type: 'index', title: '序号', width: 68, align: 'center' },
        { title: '车辆照片', slot: 'idPhotoList' },
        { title: '车牌号码', key: 'plateNo' },
        { title: '车牌颜色', slot: 'plateColor', width: 130 },
        { title: '车辆品牌', slot: 'vehicleBrand', width: 130 },
        { title: '车身颜色', slot: 'vehicleColor', width: 130 },
        { title: '车主', key: 'name', width: 130 },
        { title: '车主联系方式', key: 'phoneNo' },
        { title: '车主身份证号码', key: 'idCardNo' },
        { title: '档案更新时间', key: 'qsdiUpdateTime' },
        { title: '操作', slot: 'action', width: 174 }
      ],
      tableData: [
        // {
        //   idPhotoList: [require('@/assets/img/demo/vehicle.png'), require('@/assets/img/demo/vehicle.png'), require('@/assets/img/demo/vehicle.png')],
        //   licensePlate: '苏A88888',
        //   useNature: '',
        //   identificationCode: 'WR432E',
        //   plateColor: '5',
        //   vehicleBrand: '1',
        //   vehicleColor: '2',
        //   engineNumber: '*********',
        //   vehicleType: 'K10',
        //   registrationDate: '2021-10-10',
        //   issueDate: '2021-10-15',
        //   registeredAddress: '浙江省杭州市拱墅区一清新村1幢',
        //   useStatus: '',
        //   ownerName: '张大力',
        //   gender: '1',
        //   telephoneNumber: '12345678912',
        //   driverLicenseNumber: '43243243',
        //   idNumber: '******************',
        //   nation: '',
        //   homeAddress: '浙江省杭州市拱墅区一清新村1幢',
        //   fileUpdateTime: '2020-03-11 11:30:04'
        // }
      ],
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
        total: 0
      },
      total: 1,
      selectionList: []
    }
  },
  computed: {
    ...mapGetters({
      licensePlateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
      vehicleColorList: 'dictionary/getVehicleColorList', //车身颜色
      vehicleBrandList: 'dictionary/getVehicleBrandList' //车辆品牌
    })
  },
  created() {
    // this.getDictData().then(() => {})
    // this.currentRow = this.$route.query
    // this.init()
  },
  async activated() {
      await this.getDictData()
      this.currentRow = this.$route.query
      this.currentRow.libCount = parseInt(this.$route.query.libCount)
      this.$nextTick(() => {
          this.init()
      })
  },
  methods: {
    ...mapActions({
      getDictData: 'dictionary/getDictAllData'
    }),
    init() {
      this.tableData = []
      this.visible = false;
      this.loading = true;
      var param = {
        ...this.searchData,
        ...this.pageInfo,
        // faceLibId: this.currentRow.id,
        featureLibId: this.currentRow.featureLibId
      }
      queryVehicleLibInfoPageList(param).then(res => {
        console.log(res)
        this.tableData = res.data.data.entities
        this.pageInfo.total = res.data.data.total
      })
    },
    // 重置
    resetHandle() {
      this.$refs.searchData.resetFields()
      this.init()
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size
      this.init()
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.init()
    },
    // 新增车辆
    addVehicleHandle() {
      this.$refs.addModal.show(false)
    },
    // 档案
    archivesHandle(row) {
      console.log('row:', row)
      const { href } = this.$router.resolve({
        name: 'vehicle-archive',
        query: { 
          archiveNo: JSON.stringify(row.vehicleCode),
          plateNo: JSON.stringify(row.plateNo),
          source: 'car',
          idcardNo: row.idCardNo
        }
      })
      window.open(href, '_blank')
    },
    // 详情
    vehicleDetailHandle(item) {
      this.$refs.detailModal.show(item)
    },
    // 编辑
    vehicleEditHandle(item) {
      this.$refs.addModal.show(true, item)
    },
    // 表格选中框事件
    selectionChangeHandle(list) {
      this.selectionList = list
    },
    // 删除
    vehicleDelHandle(item) {
      if (item) {
        // 单个删除
        this.$Modal.confirm({
          title: '提示',
          width: 450,
          closable: true,
          content: `确定删除该车辆信息？`,
          onOk: () => {
            // this.$Message.success('删除成功')
            deleteVehicleLibInfo(item.id).then(res => {
              this.init()
            })
          }
        })
      } else {
        // 批量删除
        if (!this.selectionList.length) {
          this.$Message.warning('请选择要删除的车辆信息')
        } else {
          this.$Modal.confirm({
            title: '提示',
            width: 450,
            closable: true,
            content: `确定批量删除车辆信息？`,
            onOk: () => {
              // this.$Message.success('删除成功')
              // this.selectionList = []
              let idlist = this.selectionList.map(item => item.id)
              batchDeleteVehicleLibInfo(idlist)
              .then(res => {
                  this.$Message.success('删除成功')
                  this.selectionList = [];
                  this.pageInfo.pageNumber = 1;
                  this.init()
              })
            }
          })
        }
      }
    },
    // 高级搜索切换
    advancedSearchHandle() {
      if (this.visible) {
        this.visible = false
      } else {
        this.visible = true
      }
    },
    importFn () {
      this.$refs.uploadFile.clickFn()
    },
    exportFn () {
      if(this.pageInfo.total == 0) return;
      // var ids = ""
      var ids = []
      this.selectionList.forEach((item, index) => {
        // if (index != 0) {
        //   ids += ','
        // }
        // ids += item.id
        ids.push(item.id)
      })
      var obj = {
        featureLibId: this.$route.query.featureLibId,
        ...this.searchData,
        ids
      }
      var param = {
        "taskName": 4,
        "taskType": 2,
        condition: JSON.stringify(obj),
      }
      console.log(param)
      doExportVehicleData(param).then(res => {
        let aLink = document.createElement('a')
        aLink.href = res.data
        aLink.click()
      })
    },
    fileUploadSuccess(data) {
      this.init()
    }
  }
}
</script>
<style lang="less" scoped>
.thematic-database {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .detail-info {
    height: 120px;
    padding: 10px;
    flex: none;
    display: flex;
    margin-bottom: 10px;
    .info {
      display: flex;
      flex: 1;
      .info-img {
        width: 100px;
        height: 100px;
        object-fit: contain;
      }
      .info-content {
        padding-left: 20px;
        box-sizing: border-box;
        .name {
          font-size: 24px;
          font-family: 'MicrosoftYaHei-Bold';
          font-weight: bold;
          line-height: 26px;
          margin-bottom: 4px;
          display: inline-block;
        }
        .info-item {
          margin-top: 10px;
          .label {
            font-size: 12px;
            font-family: 'MicrosoftYaHei-Bold';
            font-weight: bold;
            line-height: 20px;
            white-space: nowrap;
            display: inline-block;
          }
          .value {
            font-size: 14px;
            line-height: 20px;
            display: inline-block;
          }
        }
      }
    }
    .statistics {
      padding: 0 10px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .label {
        font-size: 12px;
        font-family: 'MicrosoftYaHei-Bold';
        font-weight: bold;
        line-height: 18px;
        white-space: nowrap;
      }
      .num {
        font-family: 'MicrosoftYaHei-Bold';
        font-size: 30px;
        font-weight: bold;
        line-height: 30px;
        margin-top: 8px;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    .personnel-table {
      display: flex;
      flex: 1;
      .personnel-photo {
        width: 64px;
        height: 64px;
        object-fit: contain;
        cursor: pointer;
      }
    }
  }
}

.advanced-search-show {
    .advanced-search {
      max-height: 400px;
      transition: max-height 0.5s;
    }
    .advanced-search-text {
      /deep/img {
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }

  .search {
    position: relative;
  }
  .advanced-search-text {
    display: inline-block;
    margin-left: 10px;
    cursor: pointer;
    img {
      width: 16px;
      margin-top: 7px;
      float: left;
      margin-right: 3px;
    }
  }

  .advanced-search {
    display: flex;
    align-items: center;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    margin-top: 1px;
    z-index: 10;
    max-height: 0px;
    transition: max-height 0.3s;
    overflow: hidden;
    .upload-input-list {
      display: flex;
      /deep/.ivu-upload {
        margin-right: 10px;
      }
    }
    .other-search {
      display: flex;
      flex: 1;
      padding: 10px 0 0 10px;
      box-sizing: border-box;
      flex-direction: initial;
      .other-search-top {
        display: flex;
        border-bottom: 1px dashed #fff;
      }
      .ivu-form-item {
        margin-bottom: 10px;
      }
      .other-search-bottom {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        box-sizing: border-box;
        .slider-content {
          height: 34px;
        }
      }
    }
  }
</style>
