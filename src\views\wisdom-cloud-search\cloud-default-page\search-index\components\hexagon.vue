<template>
  <div class="hexagons">
    <div v-for="(item, $index) in 7" :key="$index" :class="'hexagon'+$index" class="hexagon"></div>
  </div>
</template>
<style lang="less" scoped>
  .hexagons {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 2;
    .hexagon {
      position: absolute;
    }
    .hexagon0 {
      left: 7%;
      bottom: 28%;
      animation: hexagonMove0 0.8s infinite;
    }
    @keyframes hexagonMove0 {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.2;
      }
      100% {
        opacity: 1;
      }
    }
    .hexagon1 {
      left: 23%;
      bottom: 12%;
      animation: hexagonMove0 1s 0.3s infinite;
      transform: scale(0.4);
    }
    .hexagon2 {
      left: 23%;
      bottom: 35%;
      background-color: rgba(165, 221, 255, 0.2);
      animation: hexagonMove0 1s 0.8s infinite;
      transform: scale(0.5);
    }
    .hexagon2::before {
      border-right-color: rgba(165, 221, 255, 0.2);
    }
    .hexagon2::after {
      border-left-color: rgba(165, 221, 255, 0.2);
    }
    .hexagon3 {
      left: 36%;
      bottom: 6%;
      animation: hexagonMove0 1s 0.5s infinite;
    }
    .hexagon4 {
      left: 70%;
      bottom: 15%;
      animation: hexagonMove0 1s infinite;
      transform: scale(0.4);
    }
    .hexagon5 {
      left: 85%;
      bottom: 40%;
      background-color: rgba(180, 226, 255, 0.2);
      animation: hexagonMove0 1s infinite;
    }
    .hexagon5::before {
      border-right-color: rgba(180, 226, 255, 0.2);
    }
    .hexagon5::after {
      border-left-color: rgba(180, 226, 255, 0.2);
    }
    .hexagon6 {
      left: 92%;
      bottom: 6%;
      animation: hexagonMove0 1s infinite;
      transform: scale(0.3);
    }
  }
</style>