<template>
  <div class="keyframe-arrow" :style="{ width: `${long}`, transform: `rotate(${direction}deg)` }">
    <div ref="arrow" class="arrow"></div>
  </div>
</template>
<script>
export default {
  props: {
    direction: {
      default: 0,
    },
    long: {
      default: '1%',
    },
    translate: {
      default: '100%',
    },
  },
  data() {
    return {};
  },
  created() {},
  mounted() {
    this.$refs.arrow.animate(
      [
        { transform: `translate(0,-50%)`, opacity: 0.5 },
        { transform: `translate(${this.translate},-50%)`, opacity: 1 },
      ],
      {
        duration: 1000,
        iterations: Infinity,
      },
    );
  },
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.keyframe-arrow {
  position: relative;
  height: 2px;
  background-image: repeating-linear-gradient(90deg, #1b6ec7, #00d5f7, #1b6ec7 0.3rem);
  transform-origin: 0 0;
}
.arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 0.02rem solid transparent;
  border-left: 0.0625rem solid #00d5f7;
  opacity: 0;

  //animation-name: move;
  //animation-duration: 2s;
  //animation-iteration-count: infinite;
  //animation-timing-function: linear;
  top: 50%;
  transform: translateY(-50%);
}

//@keyframes move {
//    0% { transform: translate(0,-50%);opacity: 1; }
//    100% {transform: translate(0.5rem,-50%);opacity: 1;}
//}
</style>
