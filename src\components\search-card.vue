<template>
  <div class="search-card over-flow">
    <slot name="area"></slot>
    <div v-if="isNormalSearch">
      <slot name="search-input"></slot>
      <span class="switch-search table-text-content fl" v-if="hasSwitch" @click="switchSearch">高级检索</span>
      <slot></slot>
    </div>
    <div class="fl ml-lg over-flow normal-search" v-else>
      <div class="upload-box" v-if="hasUpload">
        <upload-img :multiple="true" @successPut="successPut" ref="upload"> </upload-img>
        <ui-label :width="50" align="left" class="ui-label" label="相似度:">
          <Slider v-model="searchData.vidScore" class="width-md fl" :disabled="searchData.imgUrlArray.length === 0">
          </Slider>
          <span class="secondary vid-score ml-sm">{{ searchData.vidScore }}%</span>
        </ui-label>
      </div>
      <div>
        <span class="switch-search table-text-content" v-if="hasSwitch" @click="switchSearch">普通检索</span>
        <slot name="search-content"></slot>
      </div>
      <div class="btn-div">
        <Button class="button-red" @click="search">开始搜索</Button>
        <Button type="text" @click="clear">清除</Button>
      </div>
    </div>
    <slot name="search-right"></slot>
  </div>
</template>
<style lang="less" scoped>
.search-card {
  .upload-box {
    width: 340px;
  }
  .switch-search {
    margin-left: 30px;
    cursor: pointer;
    line-height: 40px;
  }
  .vid-score {
    line-height: 40px;
  }
  .ui-label .label {
    line-height: 40px;
  }
  .normal-search {
    display: flex;
    align-items: center;
  }
  .btn-div {
    margin-left: 30px;
  }
}
</style>
<script>
export default {
  data() {
    return {
      isNormalSearch: true,
    };
  },
  created() {},
  mounted() {},
  methods: {
    successPut(successData) {
      this.$emit('successPut', successData);
    },
    switchSearch() {
      this.isNormalSearch = !this.isNormalSearch;
      if (this.isNormalSearch) {
        this.searchData.imgUrlArray = [];
      }
      // 这里传出是否是普通搜索用来改变 table-module最大高度
      this.$emit('normalSearch', this.isNormalSearch);
    },
    clear() {
      if (this.hasUpload) {
        this.$refs.upload.clear();
      }
      this.$emit('clear');
    },
    clearUpload() {
      if (this.hasUpload && this.$refs.upload) {
        this.$refs.upload.clear();
      }
    },
    search() {
      this.$emit('search');
    },
    checkArea(data) {
      this.$emit('checkArea', data);
    },
  },
  watch: {
    hasSwitch() {
      this.isNormalSearch = true;
    },
  },
  computed: {},
  props: {
    searchData: {
      required: true,
    },
    hasUpload: {
      default: true,
    },
    hasSwitch: {
      default: true,
    },
  },
  components: {
    UploadImg: require('@/components/upload-img.vue').default,
  },
};
</script>
