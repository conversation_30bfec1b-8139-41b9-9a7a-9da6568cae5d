<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-07-05 11:29:12
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-15 11:26:03
-->
<template>
  <ui-module
    :title="cardTitles"
    :title-model="curTitle"
    @tab-click="changeTitle"
  >
    <div slot="content" class="total-wrapper">
      <div class="data-time" v-show="isMonth">
        <b>抓拍总量:</b>
        <span class="color-blue">{{ captureTotal }}</span>
        <b>{{ onlyMonth }}月份抓拍量:</b>
        <span class="warning">{{ monthCaptureTotal }}</span>
      </div>
      <div class="day-status" v-show="!isMonth">
        <span class="bg-warning"></span>
        <label class="auxiliary-color">0次</label>
        <span class="card-box"></span>
        <label class="auxiliary-color">1-2000次</label>
        <span class="bg-blue"></span>
        <label class="auxiliary-color">>2000次</label>
      </div>
    </div>
    <div slot="extra" class="btn-group">
      <DatePicker
        type="month"
        format="yyyy-MM"
        placement="top"
        transfer
        :open="openDatePicker"
        :value="curMonth"
        @on-change="changeMonth"
      >
        <Button
          :class="[isMonth ? 'card-active-btn' : '', 'card-btn', 'time-btn']"
          @click="changeDateType(1)"
          >{{ monthText }}</Button
        >
      </DatePicker>
      <Button
        :class="[!isMonth ? 'card-active-btn' : '', 'card-btn', 'ml-10']"
        @click="changeDateType(2)"
        >近7日</Button
      >
    </div>
    <div class="capture-behavior-content">
      <!-- 折线图 -->
      <LineEchart
        :title="{}"
        v-if="isMonth && monthSeries[0].data.length"
        :xAxis="monthXAxis"
        :grid="monthGrid"
        :series="monthSeries"
      ></LineEchart>

      <!-- 热力图 -->
      <HeatmapEchart
        :title="{}"
        v-if="!isMonth && HeatSeries[0].data.length"
        :visualMap="dayVisualMap"
        :yAxis="HeatYAxis"
        :series="HeatSeries"
      ></HeatmapEchart>

      <ui-loading v-show="loading" />
    </div>
  </ui-module>
</template>

<script>
import * as echarts from "echarts";
import LineEchart from "@/components/echarts/line-echart";
import HeatmapEchart from "@/components/echarts/heatmap-echart";
import UiModule from "../../components/ui-module.vue";
import {
  getCaptureParseMonthAPI,
  getCaptureParseDayAPI,
} from "@/api/placeArchive";

export default {
  name: "CaptureData",
  components: { LineEchart, HeatmapEchart, UiModule },
  props: {
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      loading: false,
      // 卡片title列表，2-车辆，3-人脸
      cardTitles: [
        { label: "人员抓拍", value: 3 },
        { label: "车辆抓拍", value: 2 },
      ],
      curTitle: 3, // 当前选中的title
      dateType: 1, // 时间类型，1-月份，2-近七日
      openDatePicker: false, // 打开时间控件
      curMonth: "", // 当前选中的月份，yyyy-MM
      captureTotal: 0, // 抓拍总量
      monthCaptureTotal: 0, // 月抓拍总量
      //#region 折线图配置
      monthGrid: {
        left: "2%",
        top: "15%",
        right: "3%",
        bottom: "0%",
        containLabel: true,
      },
      // 抓拍分析值
      monthSeries: [
        {
          type: "line",
          data: [],
          symbol: "circle", // 默认是空心圆（中间是白色的），改成实心圆
          showAllSymbol: true,
          symbolSize: 0,
          smooth: true,
          lineStyle: {
            width: 2,
            color: "#2C86F8", // 线条颜色
          },
          areaStyle: {
            //区域填充样式
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: "rgba(44, 134, 248, 0.2)",
                },
                {
                  offset: 1,
                  color: "rgba(91, 163, 255, 0)",
                },
              ],
              false
            ),
          },
        },
      ],
      // 抓拍分析x轴
      monthXAxis: {
        name: "日期",
        type: "category",
        data: [],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        boundaryGap: true,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
      },
      //#endregion

      //#region 热力图配置
      dayVisualMap: [
        {
          show: false,
          calculable: false,
          type: "piecewise",
          pieces: [
            { gte: 2001, color: "#48BAFF" },
            { gte: 1, lt: 2000, color: "#F9F9F9" },
            {
              value: 0,

              color: "#F29F4C",
            },
          ],
        },
      ],
      // 抓拍日分析Y轴日期
      HeatYAxis: {
        name: "",
        type: "category",
        data: [],
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: "#F9F9F9",
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: "rgba(0, 0, 0, 0.35)",
          },
        },
      },
      // 抓拍日分析值
      HeatSeries: [
        {
          name: "",
          type: "heatmap",
          data: [],
          label: {
            show: true,
            normal: {
              show: true,
              formatter: function (params) {
                //根据值判断不同字体颜色
                if (params.data["2"] == 0 || params.data["2"] > 2000) {
                  return "{a|" + params.data["2"] + "}";
                } else {
                  return "{b|" + params.data["2"] + "}";
                }
              },
              rich: {
                a: {
                  color: "#fff",
                },
                b: {
                  color: "#000",
                },
              },
            },
          },
          itemStyle: {
            borderColor: "#D3D7DE",
            borderType: "dashed",
            borderWidth: 1,
          },
        },
      ],
      //#endregion
    };
  },
  computed: {
    // 天 or 月
    isMonth() {
      return this.dateType === 1;
    },
    // 时间控件按钮展示
    monthText() {
      if (this.curMonth) {
        return this.curMonth.replace("-", "年") + "月";
      }
      return "";
    },
    // 只展示月份
    onlyMonth() {
      return this.curMonth.split("-")[1] || "";
    },
  },
  created() {
    // 设置时间为当前月
    let year = new Date().getFullYear();
    let month = new Date().getMonth() + 1;
    month = month > 9 ? month : `0${month}`;
    this.curMonth = `${year}-${month}`;
    this.getCaptureData();
  },
  methods: {
    /**
     * @description: 切换标题
     * @param {string | number} val 标题value
     */
    changeTitle(val) {
      this.curTitle = val;
      this.getCaptureData();
    },

    /**
     * @description: 切换时间类型 天 <-> 月
     * @param {number} val 时间值
     */
    changeDateType(val) {
      if (val === this.dateType) {
        // 点击的时间类型为当前选中的，做判断，如果是月份，则切换时间控件显示隐藏。如果是近7天，不做操作
        if (this.isMonth) {
          this.openDatePicker = !this.openDatePicker;
        }
      } else {
        this.dateType = val;
        this.getCaptureData();
      }
    },

    /**
     * @description: 切换月份
     */
    changeMonth(val) {
      this.curMonth = val;
      this.getCaptureData();
    },

    /**
     * @description: 获取数据
     */
    getCaptureData() {
      this.loading = true;
      let params = {
        id: this.$route.query.archiveNo,
        sbgnlx: this.curTitle,
        snapTime: this.curMonth,
      };
      if (this.isMonth) {
        getCaptureParseMonthAPI(params)
          .then((res) => {
            if (res.code === 200 && res.data) {
              let data = res.data;
              this.captureTotal = data.snapTotal;
              this.monthCaptureTotal = data.snapMonthTotal;
              this.monthXAxis.data = data.X;
              this.monthSeries[0].data = data.Y;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        getCaptureParseDayAPI(params)
          .then((res) => {
            if (res.code === 200 && res.data) {
              let obj = res.data;
              let y = [];
              let dataArr = [];
              if (obj) {
                Object.keys(obj).forEach((item, index) => {
                  // y轴日期
                  y.push(item.slice(8, 10) + "日");
                  // 每个小时对应的抓拍值
                  obj[item].Y.forEach((it, i) => {
                    //遍历对应的y值
                    let arr = [];
                    arr[0] = it; //把y轴对应的每个值转化为数组
                    arr.unshift(index); //添加Y轴对应的坐标值
                    arr.unshift(i); //添加X轴对应的坐标值
                    dataArr.push(arr);
                  });
                });
              }
              this.HeatYAxis.name = this.monthText;
              this.HeatYAxis.data = y;
              this.HeatSeries[0].data = dataArr;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.total-wrapper {
  padding-top: 10px;
  .data-time {
    font-size: 14px;
    b {
      color: rgba(0, 0, 0, 0.9);
    }
    span {
      margin: 0 50px 0 5px;
      font-weight: bold;
    }
  }
  .day-status {
    font-size: 12px;
    display: flex;
    align-items: center;
    span {
      display: inline-block;
      width: 8px;
      height: 8px;
      margin-right: 5px;
    }
    label {
      margin-right: 20px;
    }
  }
}
.btn-group {
  padding-top: 10px;
  padding-right: 20px;
  position: relative;
  .time-btn::after {
    content: "";
    width: 0;
    height: 0;
    position: absolute;
    bottom: 2px;
    right: 2px;
    border-left: 4px solid transparent;
    border-top: 4px solid transparent;
    border-right: 4px solid #fff;
    border-bottom: 4px solid #fff;
  }
}
.capture-behavior-content {
  height: 100%;
  width: 100%;
  position: relative;
}
</style>
