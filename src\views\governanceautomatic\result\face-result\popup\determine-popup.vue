<template>
  <ui-modal :title="popUpTitle" v-model="visible" :styles="styles" :footer-hide="true">
    <ul class="determine-ul">
      <li class="determine-ul-li-title">
        <span>检测算法</span>
        <span>判定结果</span>
      </li>
      <li class="determine-table-ul-li" v-for="(item, index) of tableList" :key="index">
        <span class="label">{{ item.name }}</span>
        <span class="" :class="item.value ? 'font-green' : 'font-warning'">
          {{ handleMessage(item) }}
        </span>
      </li>
      <li class="determine-ul-li-result">
        <p>综合判定</p>
        <p>
          <span
            class="icon-font icon-zhiliangfen-line standard_icon"
            :class="isUnQuatify ? 'font-warning' : 'font-green'"
          >
            <i class="icon_text_error" :class="isUnQuatify ? 'font-warning' : 'font-green'">
              {{ isUnQuatify ? '不达标' : '达标' }}
            </i>
          </span>
        </p>
      </li>
    </ul>
  </ui-modal>
</template>
<script>
export default {
  props: {
    popUpTitle: {
      default: '非唯一人脸判定',
    },
    tableList: {
      type: Array,
    },
    value: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        //top: "1.2rem",
        width: '40%',
      },
    };
  },
  computed: {
    isUnQuatify() {
      return this.tableList.some((item) => {
        return !item.value;
      });
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleMessage(item) {
      if (item.isface) {
        return item.value ? '唯一人脸' : '非唯一人脸';
      } else {
        return item.value ? '正确' : '错误';
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.determine-ul {
  color: #fff;
  > li {
    height: 60px;
    line-height: 60px;
    display: flex;
    padding: 0 20px;
    > span {
      flex: 1;
    }
  }
  &:nth-child(even) {
    background: #041939;
  }
  &:nth-child(odd),
  .determine-ul-li-result {
    background: #062042;
  }
  .determine-ul-li-title {
    background: #092955;
  }
  .determine-ul-li-result {
    height: 100px;
    line-height: 100px;
    color: var(--color-primary);
    display: flex;
    > p {
      flex: 1;
    }
  }
  .standard_icon {
    vertical-align: middle;
    font-size: 120px;
    position: relative;
    .icon_text_error {
      font-size: 16px;
      position: absolute;
      right: 37px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
    .icon_text_succeed {
      font-size: 16px;
      position: absolute;
      right: 44px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
  }
}
</style>
