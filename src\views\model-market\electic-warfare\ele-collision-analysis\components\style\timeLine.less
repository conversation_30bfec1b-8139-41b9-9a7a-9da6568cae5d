.box-content{
    .box-ul{
        margin-top: 10px;
        padding: 0 20px 0px 19px;
        min-height: 400px;
        height: calc( ~'100% - 90px' );
        .box-li-title{
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            &-left{
                display: flex;
                align-items: center;
            }
            .list_icon{
                width: 12px;
                height: 12px;
                background: rgba(44,134,248,0.1028);
                opacity: 1;
                border: 3px solid #2C86F8;
                border-radius: 10px;
            }
            &-text{
                margin-left: 10px;
                font-size: 14px;
                font-weight: 700;
                color: rgba(0,0,0,0.75);
                span{
                    color: rgba(44, 134, 248, 1);
                }
            }
        }
        .box-li{ 
            transition: height 1s ease-out;
            margin-left: 7px;
            border-left: 1px solid #D3D7DE;
            position: relative;
            padding-bottom: 10px;
            .box-li-top{
                display: flex;
                color: #2C86F8;
            }
            .box-li-bottom{
                .time-title{
                    flex: 1;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0 10px;
                    background: rgba(211, 215, 222, .3);
                    margin-left: 11px;
                    cursor: pointer;
                    .time-date{
                        color: #2C86F8;
                        font-weight: bold;
                        font-size: 12px;
                    }
                    .time-num{
                        color: #F29F4C;
                        font-size: 12px;
                        margin-left: 10px;
                    }
                    .triangle {
                        width: 0;
                        height: 0;
                        border-left: 5px solid transparent;
                        border-right: 5px solid transparent;
                        border-top: 5px solid #888888;
                        margin-left: 10px;
                        transform: rotate(0deg);
                        transition: transform 0.2s;
                    }
                    .active-triangle{
                        transform: rotate(180deg);
                        transition: transform 0.2s;
                    }
                }
                .sec-radio{
                    width: 5px;
                    height: 5px;
                    background: #D3D7DE;
                    border-radius: 50%;
                    position: absolute;
                    left: -3px;
                }
                .child_list{
                    padding-left: 20px;
                    margin-top: 11px;
                    .content-top{
                        display: flex;
                        // overflow: auto;
                        cursor: pointer;
                        img{
                            width: 60px;
                            height: 60px;
                            border: 1px solid #CFD6E6;
                            position: relative;
                            .similarity {
                                position: absolute;
                                left: 0;
                                top: 0;
                                span {
                                    padding: 2px 5px;
                                    background: #4597ff;
                                    color: #fff;
                                    border-radius: 4px;
                                }
                            }
                        }
                        .content-top-right{
                            margin-left: 11px;
                            // margin-top: 3px;
                            font-size: 14px;
                            flex: 1;
                            width: calc(  ~'100% - 60px' );
                            /deep/ .iconfont{
                                margin-right: 5px;
                            }
                            .block{
                                color: #000000;
                            }
                        }
                    }
                }
            }
        }
        // .box-li:last-child{
        //     border: none;
        // }
        .box-li-pack{
            .box-li-bottom{
                height: 20px;
                overflow: hidden;
                transition: height 1s ease-out;
                .sec-radio{
                    display: none;
                }
            }
        }
        .box-li-packend{
            border-left: none;
        }
    }

    /deep/.ivu-icon-md-radio-button-on{
        font-size: 20px;
        position: absolute;
        left: -10px;
        top: -2px;
        // z-index: 36;
    }
    /deep/.ivu-icon-ios-radio-button-on{
        font-size: 10px;
        color: #D3D7DE;
    }
    /deep/ .ivu-timeline-item-head-custom{
        padding: 0;
    }
}