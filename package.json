{"name": "qsdi-cloud-isdp-view", "version": "1.2.0-RELEASE", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "creatMg": "node scripts/build-monographic.js", "serve:mg": "cross-env VUE_APP_CODE=3010 npm run serve", "build:mg": "npm run build && npm run creatMg", "serve:tools": "npm run serve  --tools", "build:tools": "npm run build --tools"}, "dependencies": {"@antv/g6": "^4.8.24", "@easydarwin/easyplayer": "^4.1.4", "@jeremyckahn/streamsaver": "^2.0.9", "@micro-zoe/micro-app": "^1.0.0-rc.24", "@turf/turf": "^6.5.0", "axios": "^0.18.0", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "echarts": "^5.2.2", "echarts-wordcloud": "^2.0.0", "element-resize-detector": "^1.2.3", "element-ui": "^2.15.6", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "localforage": "^1.10.0", "lowdb": "^1.0.0", "md5": "^2.2.1", "recordrtc": "^5.6.2", "screenfull": "^5.2.0", "stacktrace-parser": "0.1.10", "style-loader": "^1.3.0", "v-viewer": "^1.6.4", "view-design": "^4.5.0", "viewerjs": "^1.11.6", "vue": "^2.5.22", "vue-awesome-swiper": "^3.1.3", "vue-count-to": "^1.0.13", "vue-lazyload": "^1.2.6", "vue-router": "^3.5.2", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "^2.24.3", "vuex": "^3.0.1", "watermark-dom": "^2.3.0", "xlsx": "^0.17.5"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.21.0", "@vue/cli-plugin-babel": "^3.4.0", "@vue/cli-service": "^3.4.0", "amfe-flexible": "^2.2.1", "autoprefixer": "^8.0.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.3", "babel-polyfill": "^6.26.0", "compression-webpack-plugin": "^6.1.1", "cross-env": "^7.0.3", "d3": "^7.3.0", "d3-dispatch": "^3.0.1", "hard-source-webpack-plugin": "^0.13.1", "image-webpack-loader": "^7.0.1", "less": "^2.7.3", "less-loader": "^5.0.0", "mockjs": "^1.1.0", "postcss-px2rem-exclude": "0.0.6", "postcss-pxtorem": "^5.1.1", "style-resources-loader": "^1.2.1", "vue-cli-plugin-style-resources-loader": "^0.1.3", "vue-template-compiler": "^2.5.21", "webpack": "^4.5.0"}, "volta": {"node": "16.3.0"}}