{"name": "ivdg-view", "version": "1.3.0-SNAPSHOT", "private": true, "scripts": {"serve": "vue-cli-service serve --open", "mock": "vue-cli-service serve --mode mock", "build": "vue-cli-service build"}, "dependencies": {"@antv/g2plot": "^2.4.31", "@easydarwin/easyplayer": "^4.1.4", "@liveqing/liveplayer": "^2.4.7", "@wangeditor/editor": "^5.1.22", "@wangeditor/editor-for-vue": "^1.0.2", "amfe-flexible": "^2.2.1", "axios": "^0.21.1", "babel-polyfill": "^6.26.0", "core-js": "^3.8.3", "crypto-js": "^4.1.1", "css-vars-ponyfill": "^2.4.8", "echarts": "^5.4.2", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.0", "flv.js": "^1.5.0", "lottie-web": "^5.10.2", "md5": "^2.3.0", "mockjs": "^1.1.0", "postcss-px2rem-exclude": "0.0.6", "screenfull": "^5.1.0", "v-viewer": "^1.5.1", "view-design": "^4.6.1", "vue": "^2.7.0", "vue-axios": "^2.1.4", "vue-count-to": "^1.0.13", "vue-infinite-scroll": "^2.0.2", "vue-json-viewer": "^2.2.20", "vue-lazyload": "^1.3.3", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^4.5.18", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^4.5.18", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "^2.3.3", "compression-webpack-plugin": "^6.1.1", "copy-webpack-plugin": "^5.1.2", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.27.0", "hard-source-webpack-plugin": "^0.13.1", "less": "^3.13.1", "less-loader": "^5.0.0", "mockjs": "^1.1.0", "speed-measure-webpack-plugin": "^1.5.0", "style-resources-loader": "^1.2.1", "vue-cli-plugin-style-resources-loader": "^0.1.3", "vue-template-compiler": "^2.6.11", "webpack-dev-server": "^3.0.0"}}