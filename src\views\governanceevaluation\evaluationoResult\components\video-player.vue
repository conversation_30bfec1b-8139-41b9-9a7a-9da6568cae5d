<template>
  <ui-modal class="video-player" v-model="visible" title="播放视频" footerHide :styles="styles" @onCancel="onCancel">
    <div style="margin-top: -15px">
      <EasyPlayer :videoUrl="videoUrl" ref="easyPlay"> </EasyPlayer>
    </div>
  </ui-modal>
</template>
<style lang="less" scoped></style>
<script>
import vedio from '@/config/api/vedio-threm';
export default {
  props: {
    value: {},
    playDeviceCode: {},
    videoUrl: {
      default: '',
    },
    // videoLoading: {
    //   default: false
    // }
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '5rem',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    async onCancel() {
      this.$emit('onCancel');
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {},
  components: {
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>
