export const defaultConfig = [
  {
    code: '1',
    label: '重设设备时钟',
    description: '直连设备重新设置设备时钟与NTP信息。',
    status: true,
  },
  {
    code: '2',
    label: '设备信息自动获取',
    description: '解决设备目录混乱、多平台目录不统一等问题。',
    status: true,
  },
  {
    code: '4',
    label: '功能类型治理',
    description: '根据国标编码11-13位数字、设备生产数据、设备来源自动重设功能类型。',
    status: true,
  },
  {
    code: '5',
    label: '点位类型治理',
    description: '根据设备名称或安装地址中包含特殊关键词自动重设设备点位类型。',
    status: true,
  },
  {
    code: '6',
    label: 'MAC地址治理',
    description: '直连设备自动获取设备MAC地址信息。',
    status: true,
  },
  {
    code: '3',
    label: 'OSD字幕自动重设',
    description: '检测视频监控设备OSD字幕合规性，支持重设OSD字幕。',
    status: true,
  },
  {
    code: '7',
    label: '设备状态治理',
    description: '根据设备抓拍数据或视频流调阅记录判断设备是否可用，并自动更新设备状态。',
    status: true,
  },
  {
    code: '13',
    label: '区域管理',
    description: '解决设备采集区域属性梳理、补全问题。',
    status: true,
  },
  {
    code: '14',
    label: '空间信息治理',
    description: '包含经纬度精度、经纬度与地址大量重复、经纬度越界、经纬度与地址偏移等智能检测模型。',
    status: true,
  },
  {
    code: '15',
    label: '图片数据治理',
    description: '实时监测人脸或车辆抓拍图像，自动将不合格图片清理至备存库。',
    status: true,
  },
  {
    code: '16',
    label: '人脸图像数据治理',
    description: '实时监测人脸抓拍图像，自动将不合格人脸图片清理至备存库。',
    status: true,
  },
  {
    code: '17',
    label: '车辆图像数据治理',
    description: '实时监测车辆抓拍图像，自动将不合格车辆图片清理至备存库。',
    status: true,
  },
  {
    code: '18',
    label: 'ZDR人像轨迹治理',
    description: '对轨迹数据准确性、重复性、上传及时性、URL可用性、是否关联设备进行治理。',
    status: true,
  },
  {
    code: '19',
    label: '车流量数据治理',
    description: '实时监测车流量数据，自动将时间倒挂和属性缺失的数据清理至备存库。',
    status: true,
  },
];
