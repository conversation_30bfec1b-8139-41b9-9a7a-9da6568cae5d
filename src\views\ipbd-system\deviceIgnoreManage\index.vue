<template>
  <div class="deviceignore-manger">
    <div class="deviceignore-mange-container">
      <!-- 左侧资源列表 -->
      <div class="left-resource">
        <Input
          v-model="getGroupParams.key"
          @on-change="changeHandle"
          placeholder="请输入检索预案名称"
          class="search-input"
        ></Input>
        <div class="total-add">
          <span
            >共<span class="total">{{ ignoreGroupList.length || 0 }}</span
            >个预案</span
          >
          <Button
            type="primary"
            icon="ios-add"
            size="small"
            @click="addIgnoreInfo"
            >新增预案</Button
          >
        </div>
        <div
          v-scroll
          class="ignorelist-wrap"
          v-show="ignoreGroupList.length > 0"
        >
          <li
            v-for="(item, index) in ignoreGroupList"
            :key="index"
            @click="viewResource(item)"
            class="ignorelist"
            :class="{ active: currentGroup.id === item.id }"
          >
            <i class="iconfont icon-dangan2"></i
            ><span class="ignore-name ellipsis" :title="item.name"
              >{{ item.name }}
            </span>
            <span class="toolbar">
              <i
                class="iconfont icon-bianji"
                title="编辑预案"
                @click.stop="handleEdit(item)"
              />
              <i
                class="iconfont icon-shanchu"
                title="删除预案"
                @click.stop="handleDel(item)"
              />
            </span>
          </li>
        </div>
        <div class="empty" v-show="ignoreGroupList.length < 1">暂无数据</div>
      </div>
      <div class="main-wrap">
        <!-- 预案信息 -->
        <div class="deviceignore-info">
          <div class="name ellipsis">
            <label>预案名称：</label
            ><span :title="currentGroup.name">{{
              currentGroup.name || "暂无"
            }}</span>
          </div>
          <div class="time ellipsis">
            <label>有限时间：</label><span>{{ timeRange }}</span>
          </div>
          <div class="users ellipsis">
            <label>授权用户：</label
            ><span :title="groupUserNames">{{ groupUserNames }}</span>
          </div>
          <div class="user-link">
            <span class="total"
              >共<font @click="openUserList">{{ users.length || 0 }}</font
              >个用户</span
            >
          </div>
        </div>
        <!-- 条件查询 -->
        <div class="deviceignore-search" v-if="showWhich === 'table'">
          <Input
            v-model="filterKey"
            @on-change="changeFilterHandle"
            placeholder="请输入设备名称"
            style="width: 200px; margin-right: 10px"
          ></Input>
          <Button
            type="primary"
            size="small"
            style="margin-right: 10px"
            @click="searchDevs"
            >查询</Button
          >
          <Button icon="iconfont shanchu" size="small" @click="delSelect"
            >批量删除</Button
          >
        </div>
        <!-- 模式切换 -->
        <div class="model-switch" :class="{ boxShadow: showWhich === 'map' }">
          <span
            >共<span class="total">{{ resources.length || 0 }}</span
            >条结果</span
          >
          <i
            class="iconfont icon icon-gaojisousuo"
            :class="{ actived: showWhich == 'table' }"
            @click="toggleTab('table')"
          ></i>
          <i
            class="iconfont icon icon-tubiao_ditu"
            :class="{ actived: showWhich == 'map' }"
            @click="toggleTab('map')"
          ></i>
        </div>
        <!-- 列表模式 -->
        <Table
          v-show="showWhich === 'table'"
          class="device-table-list"
          max-height="725"
          ref="table"
          :columns="columns"
          :data="tableData"
          @on-select-all="handleSelectAll"
          @on-selection-change="handleSelectChange"
        >
          <template #deviceType="{ row }">
            <div>
              {{ formatType(row.deviceType) }}
            </div>
          </template>
          <template #action="{ row }">
            <div class="btn-tips">
              <ui-btn-tip
                content="删除"
                icon="icon-shanchu"
                class="primary"
                @click.native="deleteDevs([row.deviceId])"
              />
            </div>
          </template>
        </Table>
        <!-- 地图模式 -->
        <mapBase
          ref="mapBase"
          v-show="showWhich === 'map'"
          :disableScroll="false"
          @inited="initmap"
        />
      </div>
    </div>
    <!-- 添加预案 -->
    <add-ignore-group
      ref="addIgnoreGroup"
      @refresh="fetchShieldingGroup(getGroupParams)"
    />
  </div>
</template>

<script>
import mapBase from "@/components/map/index.vue";
import addIgnoreGroup from "./components/addIgnoreGroup.vue";
import {
  getShieldingGroup,
  deleteShieldingTask,
  deleteShieldingResource,
  getViewResource,
} from "@/api/config.js";

export default {
  name: "deviceignore",
  components: {
    mapBase,
    addIgnoreGroup,
  },
  data() {
    return {
      resources: [], //当前分组设备
      users: [], //当前分组用户
      currentGroup: {}, //当前分组
      ignoreGroupList: [], //资源屏蔽列表
      showWhich: "map",
      showWindow: false,
      mapOptions: {
        resource: {
          showResource: false,
        },
        toolbar: {
          centerBtn: true,
          zoomBtn: true,
        },
        isScaleCtrl: true,
      },
      getGroupParams: {
        key: "",
        currentPage: 1,
        pageSize: 100,
      },
      selectionList: [],
      tableData: [],
      columns: [
        {
          title: "选择",
          width: 65,
          align: "center",
          type: "selection",
          key: "index",
        },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "设备名称", key: "deviceName" },
        { title: "设备编号", key: "deviceGbId" },
        { title: "类型", slot: "deviceType" },
        { title: "操作", slot: "action" },
      ],
      filterKey: "",
    };
  },
  computed: {
    groupUserNames() {
      return (
        (this.users && this.users.map((item) => item.name).join("、")) || "暂无"
      );
    },
    timeRange() {
      let { createTime, expiryTime } = this.currentGroup || {};
      return !createTime || !expiryTime
        ? "暂无"
        : `${new Date(createTime).format("yyyy.MM.dd hh:mm:ss")}-${new Date(
            expiryTime
          ).format("yyyy.MM.dd hh:mm:ss")}`;
    },
  },
  methods: {
    handleEdit(record) {
      this.viewResource(record);
      this.$refs.addIgnoreGroup.open({ title: "编辑预案", ...record });
    },
    handleDel(record) {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定要删除任务吗？`,
        onOk: () => {
          this.delIgnoreGroup(record);
        },
      });
    },
    handleSelectAll(selection) {
      this.selectionList = selection;
    },
    handleSelectChange(selection) {
      this.selectionList = selection;
    },
    // 添加预案
    addIgnoreInfo() {
      this.$refs.addIgnoreGroup.open({ title: "新增预案" });
    },
    // 切换模式
    toggleTab(type) {
      this.showWhich = type;
    },
    // 预案搜索
    changeHandle() {
      console.log(11);
      Toolkits.FnByShake(500, () => {
        this.fetchShieldingGroup(this.getGroupParams);
      });
    },
    // 打开用户面板
    openUserList() {
      this.$refs.addIgnoreGroup.open({
        title: "编辑预案",
        ...this.currentGroup,
      });
    },
    // 地图初始化完成回调
    initmap() {
      this.fetchShieldingGroup(this.getGroupParams);
    },
    // 获取预案分组
    fetchShieldingGroup(params) {
      getShieldingGroup(params).then((res) => {
        this.ignoreGroupList = res.data;
        let [viewGroup] = res.data || [];
        if (!viewGroup) this.currentGroup = {};
        viewGroup =
          this.currentGroup && !Toolkits.isEmptyObject(this.currentGroup)
            ? res.data.filter((g) => g.id == this.currentGroup.id)
            : viewGroup;
        this.viewResource(viewGroup);
      });
    },
    // 删除预案分组
    delIgnoreGroup(item) {
      if (item.id == this.currentGroup.id) this.currentGroup = {};
      deleteShieldingTask(item.id).then((res) => {
        this.$Message.success("删除成功");
        this.fetchShieldingGroup(this.getGroupParams);
        // 刷新屏蔽设备
        this.$store.dispatch("player/fetchShieldResourcePool");
      });
    },
    // 删除预案分组设备
    deleteDevs(ids) {
      if (!ids || ids.length < 1) {
        this.$Message.success("请选择设备");
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定要删除设备吗？`,
        onOk: () => {
          let params = {
            id: this.currentGroup.id,
            resourceIds: ids,
          };
          deleteShieldingResource(params).then((res) => {
            this.$Message.success("删除成功");
            this.viewResource(this.currentGroup);
            // 刷新屏蔽设备
            this.$store.dispatch("player/fetchShieldResourcePool");
          });
        },
      });
    },
    // 批量删除
    delSelect() {
      this.deleteDevs(this.selectionList.map((d) => d.deviceId));
    },
    // 查看分组详情
    viewResource(item, opts) {
      item = Array.isArray(item) ? item[0] : item;
      if (!item || Toolkits.isEmptyObject(item)) {
        //清空地图点位和列表数据
        this.currentGroup = {};
        this.users = [];
        this.resources = [];
        this.$refs.mapBase.resetMarker();
        this.tableData = [];
        return;
      }
      this.currentGroup = item;
      let params = { groupId: item.id, keyword: opts };
      getViewResource(params).then((res) => {
        let { resources = [], users = [] } = res.data || {};
        this.resources = resources;
        this.users = users;
        this.$refs.mapBase.resetMarker();
        this.$refs.mapBase.addDeviceMarkers("deviceLayer", resources);
        this.tableData = resources;
      });
    },
    editUsers() {
      this.$refs.addIgnoreGroup.open({
        title: "编辑预案",
        ...this.currentGroup,
      });
    },
    // 分组设备搜索
    changeFilterHandle() {
      Toolkits.FnByShake(500, () => {
        this.searchDevs();
      });
    },
    // 分组设备搜索
    searchDevs() {
      this.viewResource(this.currentGroup, this.filterKey);
    },

    /**
     * @description 格式化设备类型
     * @param {string | number} type 设备类型
     * @return {string} 设备类型中文名称
     */
    formatType(type) {
      // 1摄像机,2车辆卡口,3WIFI设备,4电子围栏,5RIFD设备,6定位设备，7高空相机，11人脸抓拍机,13监视器，14微卡, 15卡口设备类型
      let deviceTypeDict = [
        { deviceType: "1", deviceTypeName: "摄像机" },
        { deviceType: "2", deviceTypeName: "车辆卡口" },
        { deviceType: "3", deviceTypeName: "WIFI设备" },
        { deviceType: "4", deviceTypeName: "电围设备" },
        { deviceType: "5", deviceTypeName: "RIFD设备" },
        { deviceType: "6", deviceTypeName: "定位设备" },
        { deviceType: "7", deviceTypeName: "高空相机" },
        { deviceType: "11", deviceTypeName: "人脸抓拍机" },
        { deviceType: "13", deviceTypeName: "监视器" },
        { deviceType: "14", deviceTypeName: "微卡" },
        { deviceType: "15", deviceTypeName: "卡口设备类型" },
      ];
      return (
        deviceTypeDict.find((v) => v.deviceType == type)?.deviceTypeName || "--"
      );
    },
  },
};
</script>

<style lang="less">
.deviceignore-manger {
  height: 100%;
  width: 100%;
  position: relative;
}
.deviceignore-mange-container {
  display: flex;
  height: 100%;

  .left-resource {
    width: 350px;
    height: 100%;
    padding: 20px 10px;
    background: #ffffff;
    position: relative;
    margin-right: 10px;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    flex-shrink: 0;
    .search-input {
      width: 100%;
      margin-bottom: 10px;
    }
    .empty {
      text-align: center;
      margin-top: 50px;
      color: #b1b5bf;
    }
  }

  .total-add {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 10px;

    .total {
      color: #3f73f7;
      margin: 0 2px;
    }
  }
  .ignorelist-wrap {
    width: 100%;
    height: calc(~"100% - 70px");
  }
  .ignorelist {
    height: 38px;
    line-height: 38px;
    padding-right: 5px;
    display: flex;
    align-items: center;
    .icon-dangan2 {
      color: #2c86f8;
      margin: 0 4px;
    }
  }
  .ignore-name {
    max-width: 75%;
    display: inline-block;
    vertical-align: middle;
    font-size: 13px;
  }
  .main-wrap {
    position: relative;
    flex: 1 1 auto;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
  }
  .deviceignore-info {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background: #f7f8f9;
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    z-index: 99;
    > div {
      float: left;
    }
    .name,
    .time,
    .users {
      width: 30%;
    }
    .user-link {
      width: 10%;
      text-align: right;
    }
    span {
      font-weight: bold;
      font-size: 14px;
    }
    font {
      color: #3f73f7;
      text-decoration: underline;
      cursor: pointer;
      padding: 0 3px;
    }
    .total {
      font-size: 12px;
      font-weight: normal;
      color: #666666;
    }
  }

  .deviceignore-search {
    padding: 5px 10px;
    height: 50px;
    margin-top: 40px;
    line-height: 40px;
  }
  .model-switch {
    position: absolute;
    right: 10px;
    top: 50px;
    z-index: 1;
    background: #ffffff;
    padding: 8px;
    display: flex;
    align-items: center;
    .total {
      color: #3f73f7;
      padding: 0 3px;
    }

    .icon {
      font-size: 14px;
      color: #666666;
      margin-right: 5px;
      cursor: pointer;
    }

    .i-f_t_list {
      font-size: 13px;
      margin: 0 10px;
    }

    .actived {
      color: #3f73f7;
    }
    &.boxShadow {
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
    }
  }

  .npMap {
    height: calc(~"100% - 40px");
    top: 40px;
  }

  li {
    position: relative;
    height: 45px;
    line-height: 20px;
    cursor: pointer;
    border-bottom: 1px dashed rgba(167, 172, 184, 0.3);
    margin: 0px 2px;
    padding: 2px;
  }

  li:hover {
    background: rgba(70, 121, 250, 0.1);
    color: #666666;
    .toolbar {
      display: inline-block;
    }
  }

  li.active {
    background: rgba(70, 121, 250, 0.1);
  }

  .toolbar {
    display: inline-block;
    position: absolute;
    top: 0;
    right: 8px;
    display: none;
    i {
      color: #2c86f8;
      margin: 0 5px;
    }
  }
  .ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .device-table-list {
    height: calc(~"100% - 100px");
  }
}
</style>
