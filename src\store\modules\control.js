export default {
  namespaced: true,
  state: {
    personControl: null,
    vehicleControl: null,
  },
  mutations: {
    setPersonControl (state, data) {
      state.personControl = data
    },
    setVehicleControl (state, data) {
      state.vehicleControl = data
    }
  },
  getters: {
    getPersonControl (state) {
      return state.personControl
    },
    getVehicleControl (state) {
      return state.vehicleControl
    },
  },
  actions: {
    setPersonControl ({  commit }, data) {
      commit('setPersonControl', data)
    },
    setVehicleControl ({  commit }, data) {
      commit('setVehicleControl', data)
    },
  }
}