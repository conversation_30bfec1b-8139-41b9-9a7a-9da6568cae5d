<!--
 * @Date: 2025-01-16 13:43:53
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-28 15:55:17
 * @FilePath: \icbd-view\src\views\holographic-archives\one-place-one-archives\place-manage\components\area-device-list.vue
-->
<template>
  <ui-modal
    ref="modal"
    v-model="visible"
    title="场所设备列表"
    class="modal"
    footer-hide
    width="1200"
  >
    <div class="content-wrapper" ref="mapRef">
      <ui-table
        ref="tableRef"
        :columns="columns"
        :data="tableList"
        :loading="tableLoading"
        @scroll.stop
        :height="500"
      >
      </ui-table>
      <ui-page
        :current="pageNumber"
        :total="total"
        :page-size="pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
  </ui-modal>
</template>

<script>
import { queryDevicePageList } from "@/api/target-control.js";
export default {
  name: "AreaDeviceList",
  props: {
    deviceList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      columns: [
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "设备名称", key: "name", align: "center" },
        { title: "设备编码", key: "id", align: "center" },
        { title: "安装地址", key: "place", align: "center" },
      ],
      tableList: [],
      tableLoading: false,
      pageNumber: 1,
      pageSize: 20,
    };
  },
  computed: {
    total() {
      return this.deviceList?.length || 0;
    },
  },
  methods: {
    show() {
      this.pageNumber = 1;
      this.pageSize = 20;
      this.queryList({});
      this.visible = true;
    },
    queryList(param = {}) {
      if (this.deviceList.length == 0) {
        this.tableList = [];
        return;
      }
      this.tableLoading = true;
      let data = {
        deviceIds: this.deviceList,
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...param,
      };
      queryDevicePageList(data)
        .then(({ data }) => {
          this.tableList = data.entities;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    pageChange(value) {
      this.pageNumber = value;
      this.queryList();
    },
    pageSizeChange(value) {
      this.pageSize = value;
      this.queryList();
    },
  },
};
</script>

<style lang="less" scoped>
.content-wrapper {
  padding: 10px;
}
</style>
