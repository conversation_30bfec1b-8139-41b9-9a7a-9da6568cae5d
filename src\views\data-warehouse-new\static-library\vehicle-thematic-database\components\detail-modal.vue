<template>
  <ui-modal
    v-model="visible"
    title="详情"
    :r-width="1010"
    @onOk="comfirmHandle">
    <div ref="vehicleThematic" class="personnel-thematic-database">
      <Form ref="vehicleForm" :model="vehicleForm" inline class="personnel-form">
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">基本信息</div>
        </div>
        <div class="information-form essential-form">
          <FormItem prop="idPhotoList" class="img-formitem">
            <div class="essential-information-img">
              <div class="avatar-img card-border-color">
                <img v-if="!previewImg" src="@/assets/img/empty-page/null_img_icon.png" class="avatar-null-img" alt />
                <img v-else v-viewer :src="previewImg.photoUrl" alt />
              </div>
              <UploadImg 
                choosed 
                :isEdit="true"
                :choosedIndex="avatarIndex" 
                :defaultList="vehicleForm.photoUrlList" 
                class="upload-img" 
                @on-choose="chooseHandle"/>
            </div>
          </FormItem>
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="licensePlate" label="车牌号码:">
                <div class="label-value input-200">{{vehicleForm.plateNo}}</div>
              </FormItem>
              <FormItem prop="useNature" label="使用性质:">
                <div class="label-value input-200">{{vehicleForm.natureOfUse | commonFiltering(vehicleUseNature)}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="licensePlateColor" label="车牌颜色:">
                <div class="label-value input-200">{{vehicleForm.plateColor | commonFiltering(licensePlateColorList)}}</div>
              </FormItem>
              <FormItem prop="identificationCode" label="识别代码:">
                <div class="label-value input-200">{{vehicleForm.identificationCode}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="vehicleColor" label="车身颜色:">
                <div class="label-value input-200">{{vehicleForm.vehicleColor | commonFiltering(vehicleColorList)}}</div>
              </FormItem>
              <FormItem prop="engineNumber" label="发动机号:">
                <div class="label-value input-200">{{vehicleForm.engineCode}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="vehicleType" label="车辆类型:">
                <div class="label-value input-200">{{vehicleForm.vehicleType | commonFiltering(vehicleClassTypeList)}}</div>
              </FormItem>
              <FormItem prop="registrationDate" label="注册日期:">
                <div class="label-value input-200">{{vehicleForm.registerDate}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="issueDate" label="发证日期:">
                <div class="label-value input-200">{{vehicleForm.issueDate}}</div>
              </FormItem>
              <FormItem prop="registeredAddress" label="注册地址:">
                <div class="label-value input-200">{{vehicleForm.registerAddress}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="vehicleBrand" label="车辆品牌:">
                <div class="label-value input-200">{{vehicleForm.vehicleBrand | commonFiltering(vehicleBrandList)}}</div>
              </FormItem>
              <FormItem prop="useStatus" label="使用状态:">
                <div class="label-value input-200">{{vehicleForm.useStatus | commonFiltering(vehicleUseStatus)}}</div>
              </FormItem>
            </div>
          </div>
        </div>
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">车主信息</div>
        </div>
        <div class="information-form other-form">
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="ownerName" label="车主姓名:" class="rfid-number">
                <div class="label-value input-200">{{vehicleForm.name}}</div>
              </FormItem>
              <FormItem prop="idNumber" label="身份证号:" class="mac-number">
                <div class="label-value input-200">{{vehicleForm.idCardNo}}</div>
              </FormItem>
              <FormItem prop="gender" label="性别:">
                <div class="label-value input-200">{{vehicleForm.sex === '1' ? '男': '女'}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="driverLicenseNumber" label="驾照号码:" class="rfid-number">
                <div class="label-value input-200">{{vehicleForm.drivingLicenseNo}}</div>
              </FormItem>
              <FormItem prop="telephoneNumber" label="电话号码:" class="mac-number">
                <div class="label-value input-200">{{vehicleForm.phoneNo}}</div>
              </FormItem>
              <FormItem prop="nation" label="民族:">
                <div class="label-value input-200">{{vehicleForm.national | commonFiltering(nationList)}}</div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="homeAddress" label="家庭地址:" class="rfid-number">
                <div class="label-value">{{vehicleForm.address}}</div>
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
  import { mapActions, mapGetters } from 'vuex'
  import UploadImg from '@/components/ui-upload-img'
  export default {
    components: {
      UploadImg
    },
    data() {
      return {
        visible: false,
        avatarIndex: '',
        vehicleForm: {
          idPhotoList: [],
          licensePlate: '',
          useNature: '',
          licensePlateColor: '',
          identificationCode: '',
          vehicleColor: '',
          engineNumber: '',
          vehicleType: '',
          registrationDate: '',
          issueDate: '',
          registeredAddress: '',
          vehicleBrand: '',
          useStatus: '',
          ownerName: '',
          idNumber: '',
          gender: '',
          driverLicenseNumber: '',
          telephoneNumber: '',
          nation: '',
          homeAddress: ''
        },
        previewImg: ''
      }
    },
    computed: {
      ...mapGetters({
        licensePlateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
        vehicleColorList: 'dictionary/getVehicleColorList', //车身颜色
        vehicleBrandList: 'dictionary/getVehicleBrandList', //车辆品牌
        vehicleClassTypeList: 'dictionary/getVehicleTypeList', //车辆类型
        vehicleUseStatus: 'dictionary/getVehicleUseStatus', //车辆使用状态
        vehicleUseNature: 'dictionary/getVehicleUseNature', //车辆使用性质
        nationList: 'dictionary/getNationList', //车辆使用性质
      })
    },
    methods: {
      ...mapActions({
        getDictData: 'dictionary/getDictAllData'
      }),
      show(item) {
        this.visible = true
        this.previewImg = ''
        this.avatarIndex = ''
        this.$nextTick(() => {
          this.$refs.vehicleThematic.scrollTop = 0
          this.$refs.vehicleForm.resetFields()
          this.avatarIndex = 0
          this.previewImg = item.photoUrlList[0]
          this.vehicleForm = JSON.parse(JSON.stringify(item))
        })
      },
      // 选择汽车照
      chooseHandle(item) {
        this.previewImg = item
      },
      comfirmHandle() {
        this.visible = false
      }
    }
  }
</script>
<style lang="less" scoped>
  .input-200 {
    width: 200px;
  }
  .input-510 {
    width: 510px;
  }
  /deep/ .ivu-modal-body {
    padding: 0 !important;
  }
  .personnel-thematic-database {
    overflow-x: hidden;
    overflow-y: auto;
    padding: 20px;
    height: 620px;
    .personnel-form {
      padding: 0 30px;
      box-sizing: border-box;
      .form-item-title {
        display: flex;
        align-items: center;
        padding-bottom: 4px;
        border-bottom: 1px solid #fff;
        .title-line {
          width: 3px;
          height: 16px;
          margin-right: 6px;
        }
        .title-text {
          font-size: 14px;
          line-height: 20px;
          font-weight: bold;
          font-family: 'MicrosoftYaHei-Bold';
        }
      }
      .information-form {
        display: flex;
        justify-content: space-between;
        margin: 20px 0 30px 0;
        .essential-information-img {
          width: 240px;
          margin-right: 64px;
          display: flex;
          flex-direction: column;
          .avatar-img {
            width: 240px;
            height: 240px;
            border: 1px solid #fff;
            &>img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              cursor: pointer;
            }
            .avatar-null-img {
              cursor: unset;
            }
          }
          .upload-img {
            margin-top: 10px;
            justify-content: flex-start;
            /deep/ .upload-item {
              width: 54px;
              height: 54px;
              .ivu-icon-ios-add {
                font-size: 30px;
                font-weight: bold;
              }
              .upload-text {
                line-height: 18px;
                display: none;
              }
            }
          }
        }
        .information-body {
          flex: 1;
          .info-item {
            display: flex;
            justify-content: space-between;
            /deep/ .ivu-form-item {
              display: inline-flex;
              margin-right: 0;
              margin-bottom: 10px;
              .ivu-form-item-label {
                display: flex;
                align-items: center;
                justify-content: end;
                padding-right: 10px;
                white-space: nowrap;
                color: #999;
              }
              .label-value {
                height: 34px;
                display: flex;
                align-items: center;
              }
              .ivu-form-item-label::before {
                margin: 0;
              }
              .ivu-radio-wrapper {
                margin-right: 30px;
              }
            }
          }
        }
      }
      .essential-form {
        /deep/ .ivu-form-item {
          .ivu-form-item-label {
            width: 80px !important;
          }
        }
        .img-formitem {
          margin: 0 !important;
        }
      }
      .other-form {
        margin: 20px 0;
        /deep/ .ivu-form-item {
          .ivu-form-item-label {
            width: 74px;
            .label-text {
              width: 58px;
            }
          }
        }
        .info-item {
          justify-content: unset !important;
          .rfid-number {
            margin-right: 36px !important;
          }
          .mac-number {
            margin-right: 46px !important;
          }
        }
      }
    }
  }
</style>