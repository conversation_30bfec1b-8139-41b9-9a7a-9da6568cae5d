<template>
  <div class="alarm-level-box">
    <span :class="alarmInfo.fontClass">{{ alarmInfo.label }}</span>
  </div>
</template>

<script>
export default {
  name: "AlarmLevelLabel",
  props: {
    alarmLevel: {
      type: Number,
      default: -1,
    },
    bgIndex: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      alarmLevelList: [
        { value: 1, label: "一级" },
        { value: 2, label: "二级" },
        { value: 3, label: "三级" },
      ],
    };
  },
  computed: {
    alarmInfo() {
      return {
        fontClass: "c" + this.bgIndex || "c1",
        bgClass: "bg" + this.bgIndex || "bg1",
        label:
          this.alarmLevelList.find((item) => item.value == this.alarmLevel)
            ?.label || "一级",
      };
    },
  },
  methods: {},
};
</script>

<style lang="less" scoped>
@import "../style/alarm.less";
</style>
