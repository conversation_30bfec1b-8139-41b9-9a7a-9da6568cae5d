const baseAbnormalList = [
  {
    key: 'detectionCityOrCountyCount',
    name: '检测地市数量',
    value: 0,
    icon: 'icon-jiancedishishuliang',
    iconColor: 'icon-bg1',
    liBg: 'li-bg1',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color1',
  },
  {
    key: 'detectionCityOrCountyUnQualifiedCount',
    name: '不达标地市数量',
    value: 0,
    icon: 'icon-budabiaodishishuliang',
    iconColor: 'icon-bg4',
    liBg: 'li-bg4',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color4',
  },
  {
    key: 'deviceNum',
    name: '人脸卡口设备总量',
    value: 0,
    icon: 'icon-shebeizongliang',
    iconColor: 'icon-bg2',
    liBg: 'li-bg2',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color2',
  },
  {
    key: 'actualNum',
    name: '实际检测设备数量',
    value: 0,
    icon: 'icon-shijijianceshebeishuliang',
    iconColor: 'icon-bg6',
    liBg: 'li-bg6',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color6',
  },
  {
    key: 'qualifiedNum',
    name: '合格设备数量',
    value: 0,
    icon: 'icon-jiancehegeshebeishu',
    iconColor: 'icon-bg3',
    liBg: 'li-bg3',
    type: 'number', // number数字 percentage 百分比
    textColor: 'color3',
  },
];

export default [
  {
    indexId: '8001',
    indexName: '场所填报准确率',
    indexModule: '8',
    indexModuleName: '场所数据指标',
    indexType: 'SITE_PLACE_ACCURACY',
  },
  {
    indexId: '5001',
    indexName: '基础信息准确率',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_ACCURACY',
  },
  {
    indexId: '5002',
    indexName: '轨迹准确率',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_TRACK',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'detectionCityOrCountyCount',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'detectionCityOrCountyUnQualifiedCount',
        detectionCityOrCountyUnQualifiedCount: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: 'ZRD人像轨迹总量',
        key: 'trackAmount',
        trackAmount: 0,
        icon: 'icon-guijizhunqueshuai',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '检测轨迹数量',
        actualNum: 0,
        icon: 'icon-jianceguijishuliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格轨迹数量',
        qualifiedNum: 0,
        icon: 'icon-duocichuliweiwancheng',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '轨迹准确率',
        resultValue: 0,
        icon: 'icon-duocichuliyiwancheng',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '5003',
    indexName: '实时轨迹上传及时性',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_TRACK_REAL',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-gongdanzongliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-anshiwanchenggongdanzongshu',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'trackAmount',
        name: 'ZRD人像轨迹总量',
        value: 0,
        icon: 'icon-guijizhunqueshuai',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '检测轨迹数量',
        value: 0,
        icon: 'icon-jianceguijishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格轨迹数量',
        value: 0,
        icon: 'icon-duocichuliweiwancheng',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '实时轨迹上传及时性',
        value: 0,
        icon: 'icon-shishiguijishangchuanjishixing',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '5004',
    indexName: '聚档可用性',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_POLY_USABLE',
  },
  {
    indexId: '5005',
    indexName: '轨迹图片可访问率',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_TRACK_URL_AVAILABLE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-gongdanzongliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-anshiwanchenggongdanzongshu',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'trackAmount',
        name: 'ZRD人像轨迹总量',
        value: 0,
        icon: 'icon-guijizhunqueshuai',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '检测轨迹数量',
        value: 0,
        icon: 'icon-jianceguijishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格轨迹数量',
        value: 0,
        icon: 'icon-duocichuliweiwancheng',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '轨迹图片可访问率',
        value: 0,
        icon: 'icon-guijitupiankefangwenshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '5006',
    indexName: '轨迹设备关联率',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_DEVICE_RELATED_RATE',
  },
  {
    indexId: '5001',
    indexName: '基础信息准确率',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_ACCURACY',
  },
  {
    indexId: '5004',
    indexName: '聚档可用性',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_POLY_USABLE',
  },
  {
    indexId: '5006',
    indexName: '轨迹设备关联率',
    indexModule: '5',
    indexModuleName: '重点人员数据',
    indexType: 'FOCUS_DEVICE_RELATED_RATE',
  },
  {
    indexId: '6001',
    indexName: '人像档案置信率',
    indexModule: '6',
    indexModuleName: '档案数据',
    indexType: 'ARCHIVES_PORTRAIT_CONFIDENCE_RATE',
  },
  {
    indexId: '6002',
    indexName: '人像档案准确率',
    indexModule: '6',
    indexModuleName: '档案数据',
    indexType: 'ARCHIVES_PORTRAIT_ACCURACY',
  },
  {
    indexId: '2001',
    indexName: '人脸卡口设备图片地址可用率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_URL_AVAILABLE',
    abnormalList: [
      ...baseAbnormalList,
      {
        key: 'resultValue',
        name: '人脸卡口设备图片地址可用率',
        value: 0,
        icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2027',
    indexName: '人脸卡口设备小图地址可用率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_SMALL_URL_AVAILABLE',
    abnormalList: [
      ...baseAbnormalList,
      {
        key: 'resultValue',
        name: '人脸卡口设备小图地址可用率',
        value: 0,
        icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2002',
    indexName: '重点人脸卡口设备图片地址可用率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_FOCUS_URL_AVAILABLE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点人脸卡口设备图片地址可用率',
        value: 0,
        icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2003',
    indexName: '人脸卡口设备抓拍图片合格率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CAPTURE_PASS',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
        renderValue: (item) => {
          return item.total || item.deviceNum || 0;
        },
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人脸设备抓拍图片合格率',
        value: 0,
        icon: 'icon-renliankakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2004',
    indexName: '人脸卡口设备及时上传率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_UPLOAD',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人脸卡口设备及时上传率',
        value: 0,
        icon: 'icon-renliankakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2005',
    indexName: '重点人脸卡口设备及时上传率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_FOCUS_UPLOAD',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点人脸卡口设备及时上传率',
        value: 0,
        icon: 'icon-renliankakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2006',
    indexName: '人脸卡口设备时钟准确率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CLOCK',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人脸卡口设备时钟准确率',
        value: 0,
        icon: 'icon-duocichuliyiwancheng',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2007',
    indexName: '人脸卡口联网率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_DEVICE_CONNECT_INTERNET',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '人脸卡口联网率',
        value: 0,
        icon: 'icon-renliankakoulianwangshuai2',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '2008',
    indexName: '人脸卡口在线率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_ONLINE_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '人脸卡口在线率',
        value: 0,
        icon: 'icon-renliankakoulianwangshuai2',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '2009',
    indexName: '人脸数据一致性',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_DATA_CONSISTENCY',
  },
  {
    indexId: '2011',
    indexName: '人脸卡口设备图片存储时长达标率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_IMAGE_STORE_PASS',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '人脸卡口设备图片存储时长达标率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2012',
    indexName: '人脸抓拍数据上传完整率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CAPTURE_COMPLETENESS_RATE',
    abnormalList: [
      ...baseAbnormalList,
      {
        key: 'resultValue',
        name: '人脸抓拍数据上传完整率',
        value: 0,
        icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2013',
    indexName: '人脸抓拍数据上传合规率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_DATA_COMPLY_RULE_RATE',
    abnormalList: [
      ...baseAbnormalList,
      {
        key: 'resultValue',
        name: '人脸抓拍数据上传合规率',
        value: 0,
        icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2014',
    indexName: '人脸抓拍数据合理性',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CAPTURE_RATIONALITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人脸抓拍数据合理性',
        value: 0,
        icon: 'icon-xinxihegeZDRshu ',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2023',
    indexName: '人脸卡口抓拍率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CAPTURE_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人脸卡口抓拍率',
        value: 0,
        icon: 'icon-xinxihegeZDRshu ',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2015',
    indexName: '人脸卡口有效报送数量达标率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_VALID_SUBMIT_QUANTITY',
  },
  {
    indexId: '2016',
    indexName: '人脸卡口在线率提升',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_ONLINE_RATE_ADVANCE',
  },
  {
    indexId: '2017',
    indexName: '人脸卡口填报准确率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '人脸卡口填报准确率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '2018',
    indexName: '人脸卡口位置类型完整率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_EMPHASIS_LOCATION',
  },
  {
    indexId: '2019',
    indexName: '联网人脸卡口目录一致率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CATALOGUE_SAME',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: 'li-bg1',
        iconColor: 'icon-bg1',
        textColor: 'color1',
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: 'li-bg3',
        iconColor: 'icon-bg3',
        textColor: 'color3',
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: 'li-bg4',
        iconColor: 'icon-bg4',
        textColor: 'color4',
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'beanReportedNum',
        name: '视图库人脸卡口总量',
        value: 0,
        icon: 'icon-yishangbaoshitukurenliankakouzongliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'unReportedNum',
        name: '有图片未上报视图库人脸卡口数量',
        value: 0,
        icon: 'icon-weishangbaoshitukurenliankakouzongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'resultValueFormat',
        name: '联网人脸卡口目录一致率',
        value: 0,
        icon: 'icon-lianwangrenliankakoumuluyizhishuai',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color3',
      },
    ],
  },
  {
    indexId: '2022',
    indexName: '人脸卡口抓拍数据一致性',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CAPTURE_DATA_CONSISTENCY',
  },
  {
    indexId: '2021',
    indexName: '人脸卡口资产注册率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_UPLOAD',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'detectionCityOrCountyCount',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow)'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'detectionCityOrCountyUnQualifiedCount',
        detectionCityOrCountyUnQualifiedCount: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '资产库已注册人脸卡口数量',
        key: 'registerDeviceCount',
        registerDeviceCount: 0,
        icon: 'icon-zichankuyizhucerenliankakoushuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '未注册人脸卡口数量',
        key: 'unregisterDeviceCount',
        unregisterDeviceCount: 0,
        icon: 'icon-weizhucerenliankakoushuliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '人脸卡口资产注册率',
        key: 'resultValue',
        resultValue: 0,
        icon: 'icon-renliankakouzichanzhuceshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '2025',
    indexName: '人脸抓拍图片质量合格率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_QUALITY_PASS_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人脸卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
        renderValue: (item) => {
          return item.total || item.deviceNum || 0;
        },
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人脸设备抓拍图片合格率',
        value: 0,
        icon: 'icon-renliankakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7001',
    indexName: '接口稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'PLATFORM_API_STABILITY',
  },
  {
    indexId: '7002',
    indexName: '人脸视图库在线率',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'FACE_PLATFORM_ONLINE_RATE',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'totalPlatformNum',
        totalPlatformNum: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'onlinePlatformNum',
        onlinePlatformNum: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'unOnlinePlatformNum',
        unOnlinePlatformNum: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '昨日离线地市数',
        key: 'unOnlineNumOfM',
        unOnlineNumOfM: 0,
        icon: 'icon-zuorilixiandishishu',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月在线天数',
        key: 'onlineNumOfM',
        onlineNumOfM: 0,
        icon: 'icon-zaixianshebei',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月离线天数',
        key: 'unOnlineNumOfM',
        unOnlineNumOfM: 0,
        icon: 'icon-lixianshebei',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月报备天数',
        key: 'reportDayNumOfM',
        reportDayNumOfM: 0,
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '人脸视图库在线率',
        resultValue: 0,
        icon: 'icon-renlianshitukuzaixianshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '7003',
    indexName: '车辆视图库在线率',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'VEHICLE_PLATFORM_ONLINE_RATE',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'totalPlatformNum',
        totalPlatformNum: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'onlinePlatformNum',
        onlinePlatformNum: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'unOnlinePlatformNum',
        unOnlinePlatformNum: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '昨日离线地市数',
        key: 'unOnlineNumOfM',
        unOnlineNumOfM: 0,
        icon: 'icon-zuorilixiandishishu',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月在线天数',
        key: 'onlineNumOfM',
        onlineNumOfM: 0,
        icon: 'icon-zaixianshebei',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月离线天数',
        key: 'unOnlineNumOfM',
        unOnlineNumOfM: 0,
        icon: 'icon-lixianshebei',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月报备天数',
        key: 'reportDayNumOfM',
        reportDayNumOfM: 0,
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '车辆视图库在线率',
        resultValue: 0,
        icon: 'icon-cheliangshitukuzaixianshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '7004',
    indexName: '共享联网平台在线率',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'VIDEO_PLATFORM_ONLINE_RATE',
    abnormalList: [
      {
        name: '检测平台数量',
        key: 'totalPlatformNum',
        totalPlatformNum: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标平台数量',
        key: 'onlinePlatformNum',
        onlinePlatformNum: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标平台数量',
        key: 'unOnlinePlatformNum',
        unOnlinePlatformNum: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '当前离线平台数',
        key: 'unOnlinePlatformNum',
        unOnlinePlatformNum: 0,
        icon: 'icon-zuorilixiandishishu',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月在线时长',
        key: 'onlineNumOfM',
        onlineNumOfM: 0,
        icon: 'icon-zaixianshebei',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月离线时长',
        key: 'unOnlineNumOfM',
        unOnlineNumOfM: 0,
        icon: 'icon-lixianshebei',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '本月报备时长',
        key: 'reportDayNumOfM',
        reportDayNumOfM: 0,
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '共享联网平台在线率',
        resultValue: 0,
        icon: 'icon-lianwangpingtaizaixianshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '7005',
    indexName: '人脸布控接口稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'FACE_MONITOR_API_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg14',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      // {
      //   key: 'deviceNum',
      //   name: '车辆卡口设备设备总量',
      //   value: 0,
      //   icon: 'icon-shebeizongliang',
      //   iconColor: 'icon-bg6',
      //   liBg: 'li-bg6',
      //   type: 'number', // number数字 percentage 百分比
      //   textColor: 'color6',
      // },
      {
        key: 'actualNum',
        name: '检测本级接口次数',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg6',
        liBg: 'icon-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color12',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格次数',
        value: 0,
        icon: 'icon-jiancehegecishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '分布式身份确认接口稳定性',
        value: 0,
        icon: 'icon-fenbushishenfenquerenjiekouwendingxing',
        iconColor: 'icon-bg5',
        liBg: 'li-bg15',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7006',
    indexName: '车辆布控接口稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'VEHICLE_MONITOR_API_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg14',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      // {
      //   key: 'deviceNum',
      //   name: '车辆卡口设备设备总量',
      //   value: 0,
      //   icon: 'icon-shebeizongliang',
      //   iconColor: 'icon-bg6',
      //   liBg: 'li-bg6',
      //   type: 'number', // number数字 percentage 百分比
      //   textColor: 'color6',
      // },
      {
        key: 'actualNum',
        name: '检测本级接口次数',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg6',
        liBg: 'icon-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color12',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格次数',
        value: 0,
        icon: 'icon-jiancehegecishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆布控接口稳定性',
        value: 0,
        icon: 'icon-fenbushishenfenquerenjiekouwendingxing',
        iconColor: 'icon-bg5',
        liBg: 'li-bg15',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7007',
    indexName: '分布式身份确认接口稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'DISTRIBUTED_IDENTITY_API_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'actualNum',
        name: '检测本级接口次数',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格次数',
        value: 0,
        icon: 'icon-jiancehegecishu',
        iconColor: 'icon-bg12',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color12',
      },
      {
        key: 'resultValue',
        name: '分布式身份确认接口稳定性',
        value: 0,
        icon: 'icon-fenbushishenfenquerenjiekouwendingxing',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7008',
    indexName: '人像轨迹查询接口稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'PORTRAIT_TRACK_API_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      // {
      //   key: 'deviceNum',
      //   name: '车辆卡口设备设备总量',
      //   value: 0,
      //   icon: 'icon-shebeizongliang',
      //   iconColor: 'icon-bg6',
      //   liBg: 'li-bg6',
      //   type: 'number', // number数字 percentage 百分比
      //   textColor: 'color6',
      // },
      {
        key: 'actualNum',
        name: '检测本级接口次数',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格次数',
        value: 0,
        icon: 'icon-jiancehegecishu',
        iconColor: 'icon-bg12',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color12',
      },
      {
        key: 'resultValue',
        name: '人像轨迹查询接口稳定性',
        value: 0,
        icon: 'icon-fenbushishenfenquerenjiekouwendingxing',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7009',
    indexName: '车辆轨迹查询接口稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'VEHICLE_TRACK_API_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      // {
      //   key: 'deviceNum',
      //   name: '车辆卡口设备设备总量',
      //   value: 0,
      //   icon: 'icon-shebeizongliang',
      //   iconColor: 'icon-bg6',
      //   liBg: 'li-bg6',
      //   type: 'number', // number数字 percentage 百分比
      //   textColor: 'color6',
      // },
      {
        key: 'actualNum',
        name: '检测本级接口次数',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格次数',
        value: 0,
        icon: 'icon-jiancehegecishu',
        iconColor: 'icon-bg12',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color12',
      },
      {
        key: 'resultValue',
        name: '车辆轨迹查询接口稳定性',
        value: 0,
        icon: 'icon-fenbushishenfenquerenjiekouwendingxing',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7010',
    indexName: '人脸抓拍数量上传稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'FACE_CAPTURE_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'currentCaptureNum',
        name: '本月抓拍人脸数量',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'lastCaptureNum',
        name: '去年同期抓拍人脸数量',
        value: 0,
        icon: 'icon-jiekouwendingxing-01',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'resultValue',
        name: '人脸抓拍数量上传稳定性',
        value: 0,
        icon: 'icon-fenbushishenfenquerenjiekouwendingxing',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '7011',
    indexName: '车辆抓拍数量上传稳定性',
    indexModule: '7',
    indexModuleName: '平台可用性指标',
    indexType: 'VEHICLE_CAPTURE_STABILITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'currentCaptureNum',
        name: '本月抓拍车辆数量',
        value: 0,
        icon: 'icon-benyuezhuapaicheliangshuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color12',
      },
      {
        key: 'lastCaptureNum',
        name: '去年同期抓拍车辆数量',
        value: 0,
        icon: 'icon-quniantongqizhuapaicheliangshuliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'resultValue',
        name: '车辆抓拍数量上传稳定性',
        value: 0,
        icon: 'icon-cheliangzhuapaishujushangchuanwendingxing',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3001',
    indexName: '车辆卡口设备抓拍数据完整率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_FULL_INFO',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆卡口设备抓拍数据完整率',
        value: 0,
        icon: 'icon-cheliangkakoushebeizhuapaishujuwanzhengshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3002',
    indexName: '重点车辆卡口设备抓拍数据完整率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_FULL_INFO_IMPORTANT',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点车辆卡口设备抓拍数据完整率',
        value: 0,
        icon: 'icon-cheliangkakoushebeizhuapaishujuwanzhengshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3003',
    indexName: '车辆卡口设备过车数据准确率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_INFO_PASS',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆卡口设备过车数据准确率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3004',
    indexName: '重点车辆卡口设备过车数据准确率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_INFO_PASS_IMPORTANT',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点车辆卡口设备过车数据准确率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3005',
    indexName: '车辆卡口设备时钟准确率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_CLOCK',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆卡口设备时钟准确率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3006',
    indexName: '重点车辆卡口设备主要属性准确率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_MAIN_PROP',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点车辆卡口设备主要属性准确率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3007',
    indexName: '重点车辆卡口设备类型属性识别准确率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_TYPE_PROP',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点车辆卡口设备类型属性识别准确率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3008',
    indexName: '车辆卡口设备及时上传率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_UPLOAD',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆卡口设备及时上传率',
        value: 0,
        icon: 'icon-cheliangkakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3009',
    indexName: '重点车辆卡口设备及时上传率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_UPLOAD_IMPORTANT',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点车辆卡口设备及时上传率',
        value: 0,
        icon: 'icon-cheliangkakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3010',
    indexName: '车辆卡口设备过车图片地址可用率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_URL_AVAILABLE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆卡口设备过车图片地址可用率',
        value: 0,
        icon: 'icon-cheliangkakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3011',
    indexName: '重点车辆卡口设备过车图片地址可用率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_URL_AVAILABLE_IMPORTANT',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '重点车辆卡口设备过车图片地址可用率',
        value: 0,
        icon: 'icon-cheliangkakoushebeijishishangchuanshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3012',
    indexName: '车辆卡口联网率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_DEVICE_CONNECT_INTERNET',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '车辆卡口联网率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '3013',
    indexName: '车辆卡口在线率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_ONLINE_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '车辆卡口在线率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '3016',
    indexName: '车辆卡口设备图片存储时长达标率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_IMAGE_STORE_PASS',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '车辆卡口设备图片存储时长达标率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3018',
    indexName: '车辆抓拍数据合理性',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_CAPTURE_RATIONALITY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆抓拍数据合理性',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3019',
    indexName: '车辆卡口有效报送数量达标率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_VALID_SUBMIT_QUANTITY',
  },
  {
    indexId: '3020',
    indexName: '车辆卡口在线率提升',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_ONLINE_RATE_ADVANCE',
  },
  {
    indexId: '3021',
    indexName: '车辆卡口填报准确率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '车辆卡口填报准确率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '3022',
    indexName: '车辆卡口位置类型完整率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_EMPHASIS_LOCATION',
  },
  {
    indexId: '3023',
    indexName: '联网车辆卡口目录一致率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_CATALOGUE_SAME',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        textColor: 'color1',
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        textColor: 'color3',
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        textColor: 'color4',
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'beanReportedNum',
        name: '视图库车辆卡口总量',
        value: 0,
        icon: 'icon-yishangbaoshitukucheliangkakouzongliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'unReportedNum',
        name: '有图片未上报视图库车辆卡口数量',
        value: 0,
        icon: 'icon-weishangbaoshitukucheliangkakouzongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'resultValueFormat',
        name: '联网车辆卡口目录一致率',
        value: 0,
        icon: 'icon-lianwangcheliangkakoumuluyizhishuai',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color3',
      },
    ],
  },
  {
    indexId: '3037',
    indexName: '车辆卡口抓拍数据一致性',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_CAPTURE_DATA_CONSISTENCY',
  },
  {
    indexId: '3024',
    indexName: '车辆卡口设备过车图片地址可用率（人工复核）',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_URL_AVAILABLE_RECHECK',
  },
  {
    indexId: '3036',
    indexName: '车辆卡口资产注册率',
    indexModule: '3',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_ASSET_REGISTER',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'detectionCityOrCountyCount',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'detectionCityOrCountyUnQualifiedCount',
        detectionCityOrCountyUnQualifiedCount: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '资产库已注册车辆卡口数量',
        key: 'registerDeviceCount',
        registerDeviceCount: 0,
        icon: 'icon-zichankuyizhucecheliangkakoushuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '未注册车辆卡口数量',
        key: 'unregisterDeviceCount',
        unregisterDeviceCount: 0,
        icon: 'icon-zichankuweizhucecheliangkakoushuliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '车辆卡口资产注册率',
        key: 'resultValue',
        resultValue: 0,
        icon: 'icon-cheliangkakouzichanzhuceshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '3038',
    indexName: '车辆抓拍图片质量合格率',
    indexModule: '2',
    indexModuleName: '车辆视图数据',
    indexType: 'VEHICLE_QUALITY_PASS_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '车辆卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
        renderValue: (item) => {
          return item.total || item.deviceNum || 0;
        },
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '车辆设备抓拍图片合格率',
        value: 0,
        icon: 'icon-cheliangkakoushebeishizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4001',
    indexName: '重点实时视频可调阅率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_PLAYING_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '重点实时视频可调阅率',
        value: '0%',
        icon: 'icon-zhongdianputongshishishipinketiaoyueshuai1',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4002',
    indexName: '重点历史视频可调阅率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_HISTORY_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '重点历史视频可调阅率',
        value: '0%',
        icon: 'icon-zhongdianputonglishishipinketiaoyueshuai-01',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4003',
    indexName: '重点字幕标注合规率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_OSD_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: '0%',
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '重点字幕标注合规率',
        value: '0%',
        icon: 'icon-zhongdianzimubiaozhuheguishuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4004',
    indexName: '重点时钟准确率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_CLOCK_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '重点时钟准确率',
        value: '0%',
        icon: 'icon-zhongdianshizhongzhunqueshuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4006',
    indexName: '普通时钟准确率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_GENERAL_CLOCK_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '普通时钟准确率',
        value: '0%',
        icon: 'icon-putongshizhongzhunqueshuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4007',
    indexName: '普通字幕标注合规率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_GENERAL_OSD_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '普通字幕标注合规率',
        value: '0%',
        icon: 'icon-putongzimubiaozhuheguishuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4008',
    indexName: '普通历史视频可调阅率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_GENERAL_HISTORY_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '普通历史视频可调阅率',
        value: '0%',
        icon: 'icon-zhongdianputonglishishipinketiaoyueshuai-01',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4009',
    indexName: '普通实时视频可调阅率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_GENERAL_PLAYING_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '普通实时视频可调阅率',
        value: '0%',
        icon: 'icon-zhongdianputongshishishipinketiaoyueshuai1',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4010',
    indexName: '历史录像完整率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_HISTORY_COMPLETE_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValueFormat',
        name: '历史录像完整率',
        value: 0,
        icon: 'icon-zhongdianputongzimubiaozhuhe-01',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '4011',
    indexName: '视频监控联网率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_DEVICE_CONNECT_INTERNET',
  },
  {
    indexId: '4012',
    indexName: '视频流质量合格率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_QUALITY_PASS_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'DetectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '视频流质量合格率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4013',
    indexName: '联网数量提升率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_NETWORKING_PROMOTION_RATE',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'actualNum',
        actualNum: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'qualifiedNum',
        qualifiedNum: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'unqualifiedNum',
        unqualifiedNum: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '当前设备数',
        key: 'deviceNum',
        deviceNum: 0,
        icon: 'icon-dangqianshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '去年年末设备数量',
        key: 'lastYearDeviceNum',
        lastYearDeviceNum: 0,
        icon: 'icon-quniannianmoshebeishuliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '联网数量提升率',
        key: 'resultValueFormat',
        resultValueFormat: 0,
        icon: 'icon-shipinjiankongshuliangzengchangshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {
    indexId: '4014',
    indexName: '可调阅提升率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_READ_PROMOTION_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'beforeRate',
        name: '治理前可调阅率',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color6',
        renderValue: (item) => {
          return `${item.value}%`;
        },
      },
      {
        key: 'afterRate',
        name: '治理后可调阅率',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color7',
        renderValue: (item) => {
          return `${item.value}%`;
        },
      },

      {
        key: 'resultValueFormat',
        name: '可调阅提升率',
        value: '0%',
        icon: 'icon-zhongdianputongshishishipinketiaoyueshuai1',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        qualified: false,
      },
    ],
  },
  {
    indexId: '4015',
    indexName: '字幕标注合规率提升率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_OSD_ACCURACY_PROMOTION_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'beforeRate',
        name: '治理前字幕标注合规率',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'afterRate',
        name: '治理后字幕标注合规率',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color7',
      },
      {
        key: 'resultValueFormat',
        name: '字幕标注合规率提升率',
        value: '0%',
        icon: 'icon-zhongdianzimubiaozhuheguishuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4016',
    indexName: '时钟准确率提升率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'beforeRate',
        name: '治理前时钟准确率',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'afterRate',
        name: '治理后时钟准确率',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg7',
        liBg: 'li-bg7',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color7',
      },
      {
        key: 'resultValueFormat',
        name: '时钟准确提升率',
        value: '0%',
        icon: 'icon-zhongdianzimubiaozhuheguishuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4017',
    indexName: '视频监控在线率提升',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_MONITOR_ONLINE_RATE_PROMOTE',
  },
  {
    indexId: '4018',
    indexName: '视频监控有效报送数量达标率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_VALID_SUBMIT_QUANTITY',
  },
  {
    indexId: '4019',
    indexName: '视频监控填报准确率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '视频监控填报准确率',
        value: '0%',
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4020',
    indexName: '重点历史录像完整率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValueFormat',
        name: '重点历史录像完整率',
        value: 0,
        icon: 'icon-zhongdianputongzimubiaozhuhe-01',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '4021',
    indexName: '重点指挥图像在线率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '重点指挥图像在线率',
        value: '0%',
        icon: 'icon-zhongdianputongshishishipinketiaoyueshuai1',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4022',
    indexName: '视频监控资产匹配率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_MONITOR_ASSET_MATCH_RATE_SICHUAN',
  },
  {
    indexId: '4023',
    indexName: '视频监控位置类型完整率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_EMPHASIS_LOCATION',
  },
  {
    indexId: '4024',
    indexName: '视频流质量合格率（人工复核）',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_QUALITY_PASS_RATE_RECHECK',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'DetectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '视频流质量合格率（人工复核）',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4026',
    indexName: '（重点）字幕标注合规性与时钟准确性',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_OSD_CLOCK_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '（重点）字幕标注合规性与时钟准确性',
        value: '0%',
        icon: 'icon-putongzimubiaozhuheguishuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4027',
    indexName: '字幕标注合规性与时钟准确性',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '字幕标注合规性与时钟准确性',
        value: '0%',
        icon: 'icon-putongzimubiaozhuheguishuaijiance',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '4028',
    indexName: '视频监控数量增长率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_QUANTITY_INCREASE_RATE',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'actualNum',
        actualNum: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'qualifiedNum',
        qualifiedNum: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'unqualifiedNum',
        unqualifiedNum: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '当前设备数',
        key: 'deviceNum',
        deviceNum: 0,
        icon: 'icon-dangqianshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '去年年末设备数量',
        key: 'lastYearDeviceNum',
        lastYearDeviceNum: 0,
        icon: 'icon-quniannianmoshebeishuliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '视频监控数量增长率',
        key: 'resultValueFormat',
        resultValueFormat: 0,
        icon: 'icon-shipinjiankongshuliangzengchangshuai',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
      },
    ],
  },
  {},
  {
    indexId: '4029',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexName: '视频监控设备撤销率',
    indexType: 'VIDEO_DEVICE_REVOCATION',
    componentName: 'VideoDeviceRevocation',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'detectionCityOrCountyCount',
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg12',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        name: '不达标地市数量',
        key: 'detectionCityOrCountyUnQualifiedCount',
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        name: '撤销次数',
        key: 'revocationCount',
        trackAmount: 0,
        icon: 'icon-chexiaocishu',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'addUpRevocationNumber',
        name: '累计撤销数量',
        actualNum: 0,
        icon: 'icon-zuizhongchexiaoshuliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'finalRevocationNumber',
        name: '最终撤销数量',
        qualifiedNum: 0,
        icon: 'icon-zuizhongchexiaoshuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '视频监控设备撤销率',
        resultValue: 0,
        icon: 'icon-shipinjiankongshebeichexiaoshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '1001',
    indexName: '填报准确率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_ACCURACY',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '检测合格数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValueFormat',
        name: '填报准确率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11', //--bg-card-blue
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11', //--font-card-blue
        qualified: false,
      },
    ],
  },
  {
    indexId: '1002',
    indexName: '建档率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_INPUT',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyQualifiedCount',
        name: '建档达标(地市)数',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '建档未达标(地市)数',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'resultValueFormat',
        name: '建档率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '1003',
    indexName: '全量目录完整率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_FULL_DIR',
  },
  {
    indexId: '1004',
    indexName: '数量达标率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_QUANTITY_STANDARD',
  },
  {
    indexId: '1005',
    indexName: '视频图像设备位置类型完整率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_EMPHASIS_LOCATION',
  },
  {
    indexId: '1006',
    indexName: '重点位置类型视频图像设备数量达标率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_EMPHASIS_QUANTITY',
  },
  {
    indexId: '1008',
    indexName: '视频图像采集区域数量达标率',
    indexModule: '1',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_CJQY_QUANTITY_STANDARD',
    abnormalList: [
      {
        key: 'standardCount',
        name: '应上报采集区域类型数量',
        value: 0,
        icon: 'icon-a-yingshangbaocaijiquyuleixingshuliang1',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'qualifiedStandardCount',
        name: '采集区域类型达标数量',
        value: 0,
        icon: 'icon-caijiquyuleixingdabiaoshuliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'unQualifiedStandardCount',
        name: '采集区域类型不达标数量',
        value: 0,
        icon: 'icon-caijiquyuleixingbudabiaoshuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'DetectionCityOrCountyQualifiedCount',
        name: '达标地市数量',
        value: 0,
        icon: 'icon-dabiaodishishuliang',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'resultValueFormat',
        name: '视频图像采集区域数量达标率',
        value: 0,
        icon: 'icon-tianbaozhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '',
    indexName: '新增指标',
    indexModuleName: '新增指标',
    indexType: 'COMPOSITE_INDEX',
    abnormalList: [
      {
        name: '检测地市数量',
        key: 'detectionCityOrCountyCount',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '不达标地市数量',
        key: 'detectionCityOrCountyUnQualifiedCount',
        detectionCityOrCountyUnQualifiedCount: 0,
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '实际检测设备数量',
        key: 'actualNum',
        trackAmount: 0,
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: 0,
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'unqualifiedNum',
        name: '不合格设备数量',
        unqualifiedNum: 0,
        icon: 'icon-jiancebuhegeshebeishu',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValueFormat',
        name: '新增指标名称',
        resultValue: 0,
        icon: 'icon-xinzengzhibiaomingcheng',
        liBg: $cssVar('--bg-card-blue'),
        iconColor: $cssVar('--icon-card-gradient-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        isModifyName: true,
      },
    ],
  },
  {
    indexId: '4031',
    indexName: '视频流编码规范率',
    indexModule: '4',
    indexModuleName: '视频流数据',
    indexType: 'VIDEO_CODE_STANDARD_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '视频监控设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValueFormat',
        name: '视频流编码规范率',
        value: 0,
        icon: 'icon-zhongdianputongzimubiaozhuhe-01',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '2026',
    indexName: '人脸抓拍图片评分率',
    indexModule: '2',
    indexModuleName: '人脸视图数据',
    indexType: 'FACE_CAPTURE_SCORE',
    abnormalList: [
      ...baseAbnormalList,
      {
        key: 'resultValue',
        name: '人脸设备抓拍图片评分率',
        value: 0,
        icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '9001',
    indexName: '人体卡口设备及时上传率',
    indexModule: '9',
    indexModuleName: '人体视图数据',
    indexType: 'BODY_UPLOAD',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人体卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人体卡口设备及时上传率',
        value: 0,
        icon: 'icon-rentikakoushebeishangchuanjishishuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '9002',
    indexName: '人体卡口设备时钟准确率',
    indexModule: '9',
    indexModuleName: '人体视图数据',
    indexType: 'BODY_CLOCK',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        value: 0,
        icon: 'icon-jiancedishishuliang',
        iconColor: 'icon-bg1',
        liBg: 'li-bg1',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color1',
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        value: 0,
        icon: 'icon-budabiaodishishuliang',
        iconColor: 'icon-bg4',
        liBg: 'li-bg4',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color4',
      },
      {
        key: 'deviceNum',
        name: '人体卡口设备总量',
        value: 0,
        icon: 'icon-shebeizongliang',
        iconColor: 'icon-bg2',
        liBg: 'li-bg2',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color2',
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        value: 0,
        icon: 'icon-shijijianceshebeishuliang',
        iconColor: 'icon-bg6',
        liBg: 'li-bg6',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color6',
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        value: 0,
        icon: 'icon-jiancehegeshebeishu',
        iconColor: 'icon-bg3',
        liBg: 'li-bg3',
        type: 'number', // number数字 percentage 百分比
        textColor: 'color3',
      },
      {
        key: 'resultValue',
        name: '人体卡口设备时钟准确率',
        value: 0,
        icon: 'icon-rentikakoushebeitupianshizhongzhunqueshuai',
        iconColor: 'icon-bg11',
        liBg: 'li-bg11',
        type: 'percentage', // number数字 percentage 百分比
        textColor: 'color11',
        qualified: false,
      },
    ],
  },
  {
    indexId: '9003',
    indexName: '人体卡口设备在线率',
    indexModule: '9',
    indexModuleName: '人体视图数据',
    indexType: 'BODY_ONLINE_RATE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '人体卡口设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '人体卡口设备在线率',
        value: 0,
        icon: 'icon-rentikakoushebeizaixianshuai',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
  {
    indexId: '9004',
    indexName: '人体卡口设备活跃率',
    indexModule: '9',
    indexModuleName: '人体视图数据',
    indexType: 'BODY_ACTIVE',
    abnormalList: [
      {
        key: 'detectionCityOrCountyCount',
        name: '检测地市数量',
        detectionCityOrCountyCount: 0,
        icon: 'icon-jiancedishishuliang',
        liBg: $cssVar('--bg-card-cyan'),
        iconColor: $cssVar('--icon-card-gradient-cyan'),
        textColor: $cssVar('--font-card-cyan'),
        boxShadow: $cssVar('--bg-card-cyan-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'detectionCityOrCountyUnQualifiedCount',
        name: '不达标地市数量',
        detectionCityOrCountyUnQualifiedCount: '',
        icon: 'icon-budabiaodishishuliang',
        liBg: $cssVar('--bg-card-light-pink'),
        iconColor: $cssVar('--icon-card-gradient-light-pink'),
        textColor: $cssVar('--font-card-light-pink'),
        boxShadow: $cssVar('--bg-card-light-pink-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        name: '达标地市数量',
        key: 'detectionCityOrCountyQualifiedCount',
        detectionCityOrCountyQualifiedCount: 0,
        icon: 'icon-dabiaodishishuliang',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'deviceNum',
        name: '人体卡口设备总量',
        deviceNum: '',
        icon: 'icon-shebeizongliang',
        liBg: $cssVar('--bg-card-orange'),
        iconColor: $cssVar('--icon-card-gradient-orange'),
        textColor: $cssVar('--font-card-orange'),
        boxShadow: $cssVar('--bg-card-orange-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'actualNum',
        name: '实际检测设备数量',
        actualNum: '',
        icon: 'icon-shijijianceshebeishuliang',
        liBg: $cssVar('--bg-card-deep-purple'),
        iconColor: $cssVar('--icon-card-gradient-deep-purple'),
        textColor: $cssVar('--font-card-deep-purple'),
        boxShadow: $cssVar('--bg-card-deep-purple-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'qualifiedNum',
        name: '合格设备数量',
        qualifiedNum: '',
        icon: 'icon-jiancehegeshebeishu',
        liBg: $cssVar('--bg-card-green'),
        iconColor: $cssVar('--icon-card-gradient-green'),
        textColor: $cssVar('--font-card-green'),
        boxShadow: $cssVar('--bg-card-green-shadow'),
        type: 'number', // number数字 percentage 百分比
      },
      {
        key: 'resultValue',
        name: '人体卡口设备活跃率',
        value: 0,
        icon: 'icon-rentikakoushebeihuoyueshuai',
        iconColor: $cssVar('--icon-card-gradient-blue'),
        liBg: $cssVar('--bg-card-blue'),
        textColor: $cssVar('--font-card-blue'),
        boxShadow: $cssVar('--bg-card-blue-shadow'),
        type: 'percentage', // number数字 percentage 百分比
        // qualified: false,
      },
    ],
  },
];
