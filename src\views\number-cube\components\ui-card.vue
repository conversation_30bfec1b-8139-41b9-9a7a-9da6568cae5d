<template>
  <div class="list-card card-box">
    <div class="list-card-head">
      <div class="head-left">
        <!-- 实名档案 -->
        <div v-if="type === 'real_name_archive'" class="header-icon bg-primary">
          <div class="icon-text">身</div>
        </div>
        <!-- 手机号 -->
        <div v-else-if="type === 'vid_archive'" class="header-icon bg-warning">
          <div class="icon-text">视</div>
        </div>
        <!-- 档案 -->
        <div v-else-if="type === 'device_archive'" class="header-icon bg-primary">
          <img src="@/assets/img/number-cube/device-icon.png" />
        </div>
        <!-- 手机号 -->
        <div v-else-if="type === 'iphone'" class="header-icon bg-primary">
          <img src="@/assets/img/number-cube/iphone_icon.png" />
        </div>
        <!-- IMSI -->
        <div v-else-if="type === 'IMSI' || type === 'IMSIS'" class="header-icon bg-primary">
          <img src="@/assets/img/number-cube/imsi_icon.png" />
        </div>
        <!--  RFID -->
        <div v-else-if="type === 'rfid' || type === 'rfids'" class="header-icon bg-primary">
          <img src="@/assets/img/number-cube/rfid_icon.png" />
        </div>
        <!-- wifi -->
        <div v-else-if="type === 'wifi'" class="header-icon bg-primary">
          <img src="@/assets/img/number-cube/wifi_icon.png" />
        </div>
        <!-- 其他 -->
        <div v-else class="header-icon bg-primary">
          <i :class="options[type].icon" class="iconfont"></i>
        </div>
        <div class="head-con cursor-p" @click="archivesDetailHandle">
          <div class="text primary ellipsis" v-if="type === 'real_name_archive'">{{ data.properties.archiveNo }}</div>
          <div class="text warning ellipsis" v-else-if="type === 'vid_archive'">{{ data.properties.archiveNo }}</div>
          <div class="license-plate-small" v-else-if="type === 'vehicle_archive' || type === 'vehicle'">{{ data.properties.plateNo }}</div>
          <div class="text color-green ellipsis" v-else-if="type === 'device_archive'">{{ data.properties.deviceId }}</div>
          <div class="text primary ellipsis" v-else-if="type === 'home'">{{ data.addressT }}</div>
          <div class="text primary_other ellipsis" v-else-if="type === 'iphone'">{{ data.iphone }}</div>
          <div class="text primary_other ellipsis" v-else-if="type === 'IMSI' || type === 'IMSIS'">{{ data.imsi }}</div>
          <div class="text primary_other ellipsis" v-else-if="type === 'rfids' || type === 'rfid'">{{ data.rfid }}</div>
          <div class="text primary_other ellipsis" v-else-if="type === 'wifi'">{{ data.wifi }}</div>
        </div>
      </div>
      <!-- <div v-if="type === 'people' && isChange" class="change-btn" @click.stop="changeHandle">
        <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>
      </div> -->
      <!-- <div v-else-if="type === 'video' && isChange" class="change-btn warning-change-btn" @click.stop="changeHandle">
        <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>
      </div> -->
    </div>
    <div class="list-card-content">
      <div class="list-card-content-body">
        <div :class="type === 'home' ? 'content-home-img' : 'content-img'" class="card-border-color">
          <ui-image :src="data.propertyIcon ? data.propertyIcon : data.icon" :defaultIcon="data.icon" alt="图片" viewer/>
          <!-- <img v-viewer :src="data.src" /> -->
        </div>
        <div class="content-info">
          <div v-for="(item, $index) in options[type].info" :key="$index" class="info-li">
            <div class="info-name" :title="Object.values(item)[0]">
              <i class="iconfont auxiliary-color mr-5" :class="item.icon"></i>
            </div>
            <div v-if="Object.keys(item)[0] == 'xbdm'">{{ data.properties[Object.keys(item)[0]] | commonFiltering(ipbdFaceCaptureGender)}}</div>
            <div v-else :class="Object.keys(item)[0] === 'propertyAddress' || Object.keys(item)[0] === 'address' || Object.keys(item)[0] === 'householdRegister' ? 'two-ellipsis' : ''" class="info-value">{{ data.properties[Object.keys(item)[0]] }}</div>
          </div> 
        </div>
      </div>
      <!-- <ui-tag-poptip v-if="data.labels && data.labels.length" :data="data.labels" /> -->
      <!-- <div v-else class="lables-null"></div> -->
    </div>
    <div class="list-card-background">
      <img :src="options[type].cardBg" />
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
export default {
  props: {
    type: {
      type: String,
      default: 'people'
    },
    data: {
      type: Object,
      default: () => {}
    },
    // 是否显示切换按钮
    isChange: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: {
        // 人员
        'real_name_archive': {
          idNumber: '320102199003079556',
          cardBg: require('@/assets/img/card-bg/idcard.png'),
          info: [
            { xm: '姓名', icon: 'icon-shenfenzheng' },
            { xbdm: '性别', icon: 'icon-xingbie2' },
            // { nation: '民族', icon: 'icon-minzu1' },
            { hjdz: '户籍', icon: 'icon-location' }
          ]
        },
        // 视频档
        'vid_archive': {
          icon: 'icon-qiche',
          cardBg: require('@/assets/img/card-bg/video.png'),
          info: [
            { gmsfhm: 'vid', icon: 'icon-gongjuxiang' },
            { qsdiInsertTime: '时间', icon: 'icon-leixing' }
          ]
        },
        // 车辆
        'vehicle_archive': {
          icon: 'icon-qiche',
          cardBg: require('@/assets/img/card-bg/car.png'),
          info: [
            { archiveNo: '档案号', icon: 'icon-gongjuxiang' },
            { qsdiInsertTime: '时间', icon: 'icon-leixing' }
          ]
        },
        // 档案
        'device_archive': {
          icon: 'icon-qiche',
          cardBg: require('@/assets/img/card-bg/idcard.png'),
          info: [
            { deviceName: '设备名称', icon: 'icon-gongjuxiang' },
            { qsdiInsertTime: '时间', icon: 'icon-gongjuxiang' },
          ]
        },
        // 驾车车辆
        vehicle: {
          icon: 'icon-qiche',
          cardBg: require('@/assets/img/card-bg/car.png'),
          info: [
            { zwppmc: '车辆品牌', icon: 'icon-gongjuxiang' },
            { jdccllxdm: '车辆类型', icon: 'icon-gongjuxiang' },
            { name: '车主', icon: 'icon-xingming' },
            { idNumber: '身份证号', icon: 'icon-xingming' }
          ]
        },
        // 房屋
        home: {
          icon: 'icon-fangzi',
          cardBg: require('@/assets/img/card-bg/home.png'),
          info: [
            { fcxz: '房屋性质', icon: 'icon-home1' },
            { djsj: '登记时间', icon: 'icon-time' },
            { fcdzDzmc: '房产地址', icon: 'icon-location' }
          ]
        },
        // 手机号归属
        iphone: {
          info: [{ frequency: '编号', icon: 'icon-juhe' }]
        },
        // IMSI归属
        IMSI: {
          info: [
            { djsj: '时间', icon: 'icon-time' },
            { fcdzDzmc: '地址', icon: 'icon-location' }
          ]
        },
        // IMSI同行
        IMSIS: {
          info: [
            { name: '姓名', icon: 'icon-shenfenzheng' },
            { idNumber: '身份证号', icon: 'icon-location' }
          ]
        },
        // WI-FI归属
        wifi: {
          info: [
            { name: '姓名', icon: 'icon-shenfenzheng' },
            { idNumber: '身份证号', icon: 'icon-location' }
          ]
        },
        // RFID同行
        rfid: {
          info: [
            { name: '姓名', icon: 'icon-shenfenzheng' },
            { idNumber: '身份证号', icon: 'icon-location' }
          ]
        },
        // RFID归属
        rfids: {
          info: [
            { name: '事件', icon: 'icon-shenfenzheng' },
            { idNumber: '地址', icon: 'icon-location' }
          ]
        }
      }
    }
  },
  mounted() {
    // console.log(this, '======')
  },
  computed: {
    ...mapGetters({
      ipbdFaceCaptureGender: 'dictionary/getIpbdFaceCaptureGender', // 性别
    })
  },
  methods: {
    // 卡片切换
    changeHandle() {
      this.$emit('on-change', '')
    },
    // 详情跳转
    archivesDetailHandle() {
      // this.$emit('archivesDetailHandle', '')
      var name = 'video-archive'
      let type = 'video'
      let archiveNo = this.data.data.properties.archiveNo
      let plateNo = null

      if (this.data.label == 'real_name_archive') {
        name = 'people-archive'
        type = 'people'
      }

      if (this.data.label == 'vehicle_archive') {
        name = 'vehicle-archive'
        type = 'car'
        archiveNo = JSON.stringify(archiveNo)
        plateNo = JSON.stringify(this.data.data.properties.plateNo)
      }
      
      const { href } = this.$router.resolve({
        name: name,
        query: {
          archiveNo: archiveNo,
          source: type,
          initialArchiveNo: archiveNo,
          plateNo: plateNo
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="less" scoped>
.list-card {
  width: 316px;
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  .list-card-head {
    height: 30px;
    border-bottom: 1px solid #fff;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    justify-content: space-between;
    overflow: hidden;
    border-top-right-radius: 4px;
    .head-left {
      display: flex;
      align-items: center;
      .header-icon {
        width: 20px;
        height: 20px;
        border-radius: 2px;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        .iconfont {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-text {
          font-size: 12px;
          line-height: 12px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .head-con {
        display: flex;
        flex: 1;
        align-items: center;
      }
      .text {
        font-size: 16px;
        font-family: 'MicrosoftYaHei-Bold';
        font-weight: bold;
        line-height: 22px;
      }
    }
    .change-btn {
      width: 40px;
      height: 100%;
      margin-right: -18px;
      transform: skewX(-18deg);
      display: flex;
      align-items: center;
      padding-left: 8px;
      box-sizing: border-box;
      cursor: pointer;
      .iconfont {
        font-size: 16px;
      }
    }
  }
  .list-card-content {
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .list-card-content-body {
      display: flex;
      .content-img,
      .content-home-img {
        width: 116px;
        height: 116px;
        border: 1px solid #fff;
        position: relative;
        & > img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          cursor: pointer;
        }
        .video-icon {
          position: absolute;
          top: -3px;
          right: 0;
          .video-icon-top {
            width: 24px;
            height: 4px;
            background: #ffbd7a;
            transform: skewX(-42deg);
            transform-origin: left top;
          }
          .video-icon-top::after {
            content: '';
            position: absolute;
            top: 0.5px;
            right: -1px;
            width: 0;
            height: 0;
            border-bottom: 2px solid #cf7820;
            border-left: 2px solid transparent;
            transform: rotate(-50deg);
          }
          .video-icon-bottom {
            color: #fff;
            font-size: 12px;
            line-height: 16px;
            background: #f29f4c;
            width: 24px;
            height: 16px;
            text-align: center;
            position: absolute;
            left: -3px;
            top: 3px;
          }
          .video-icon-bottom::before {
            content: '';
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-right: 12px solid transparent;
            position: absolute;
            left: 0;
            top: 16px;
          }
          .video-icon-bottom::after {
            content: '';
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-left: 12px solid transparent;
            position: absolute;
            right: 0;
            top: 16px;
          }
        }
      }
      .content-home-img {
        border: none;
      }
      .content-info {
        width: 170px;
        padding-left: 10px;
        flex: 1;
        box-sizing: border-box;
        .info-li {
          display: flex;
          // align-items: center;
          margin-bottom: 5px;
          .info-name {
            font-size: 12px;
            line-height: 18px;
            color: #181818;
            font-family: 'MicrosoftYaHei-Bold';
            font-weight: bold;
            white-space: nowrap;
          }
          .info-value {
            font-size: 14px;
            line-height: 20px;
            color: #484847;
          }
        }
      }
    }
  }
  .list-card-background {
    position: absolute;
    bottom: -4px;
    right: 0px;
    // bottom: -52px;
    // right: -32px;
  }
  .primary_other {
    color: #7054d1;
  }
  .lables-null {
    height: 33px;
  }
}
</style>
