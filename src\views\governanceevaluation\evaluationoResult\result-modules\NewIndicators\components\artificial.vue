<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" footer-hide>
    <div class="detail-wrap" v-ui-loading="{ loading: detailLoading, tableData: resultTableData }">
      <Form :model="artificialData" :label-width="80">
        <FormItem label="人工复核:">
          <RadioGroup v-model="artificialData.qualified">
            <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">
              {{ item.key }}
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reviewRemark"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </FormItem>
      </Form>
      <div class="detail-footer">
        <Button v-if="!(currentNum < 1 && pageData.pageNum === 1)" type="primary" @click="preReview">上一条</Button>
        <Button type="primary" class="ml-lg" @click="artificial">确定复核结果</Button>
        <Button v-if="curIndex < totalCount - 1" type="primary" class="ml-lg" @click="nextReview">下一条</Button>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'playingArtificial',
  props: {
    title: {
      type: String,
      default: '人工复核',
    },
    value: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
      default: () => {},
    },
    pageData: {
      type: Object,
      default: () => {},
    },
    totalCount: {
      type: Number,
      default: 0,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      styles: { width: '3.5rem' },
      visible: false,
      artificialData: {
        qualified: '1',
        reviewRemark: '',
      },
      qualifiedList: [
        { key: '合格', value: '1' },
        { key: '不合格', value: '2' },
      ],
      currentNum: 0,
      resultTableData: [],
      curIndex: 0,
      detailLoading: false,
    };
  },
  methods: {
    preReview() {
      const pagenum = this.pageData.pageNum;
      const pagesize = this.pageData.pageSize;
      if (this.currentNum > 0 && this.currentNum < pagenum * pagesize) {
        this.currentNum--;
        this.curIndex = (pagenum - 1) * pagesize + this.currentNum;
        this.artificialData.qualified = this.resultTableData[this.currentNum]
          ? this.resultTableData[this.currentNum].qualified
          : '1';
        this.artificialData.reviewRemark =
          !!this.resultTableData[this.currentNum] && this.artificialData.qualified === '2'
            ? this.resultTableData[this.currentNum].reviewRemark
            : '';
        return;
      }
      if (pagenum > 1 && this.curIndex === (pagenum - 1) * pagesize) {
        this.resultTableData = [];
        this.detailLoading = true;
        this.currentNum = pagesize - 1;
        this.$emit('handlePage', pagenum - 1);
        return;
      }
    },
    nextReview() {
      if (this.curIndex > this.totalCount - 1) return false;
      const pagenum = this.pageData.pageNum;
      const pagesize = this.pageData.pageSize;
      if (this.curIndex === pagenum * pagesize - 1 && this.curIndex < this.totalCount - 1) {
        this.resultTableData = [];
        this.detailLoading = true;
        this.currentNum = 0;
        this.$emit('handlePage', pagenum + 1);
        return;
      }
      this.currentNum++;
      this.curIndex = (pagenum - 1) * pagesize + this.currentNum;
      this.artificialData.qualified = this.resultTableData[this.currentNum]
        ? this.resultTableData[this.currentNum].qualified
        : '1';
      this.artificialData.reviewRemark =
        !!this.resultTableData[this.currentNum] && this.artificialData.qualified === '2'
          ? this.resultTableData[this.currentNum].reviewRemark
          : '';
      this.detailId = this.resultTableData[this.currentNum] ? this.resultTableData[this.currentNum].id : '';
    },
    async artificial() {
      let data = {
        data: {
          id: this.resultTableData[this.currentNum].id,
          qualified: this.artificialData.qualified,
          reviewRemark: this.artificialData.reviewRemark,
        },
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    detailData: {
      handler(val) {
        if (!this.visible) return false;
        this.currentNum = val._index;
        const pagenum = this.pageData.pageNum;
        const pagesize = this.pageData.pageSize;
        this.curIndex = (pagenum - 1) * pagesize + this.currentNum;
        this.artificialData.qualified = val.qualified;
        this.artificialData.reviewRemark = val.qualified === '2' ? val.reviewRemark : '';
      },
      deep: true,
    },
    tableData: {
      handler(val) {
        if (!val.length) return false;
        this.resultTableData = this.$util.common.deepCopy(val);
        if (!this.visible) return false;
        const pagenum = this.pageData.pageNum;
        const pagesize = this.pageData.pageSize;
        this.curIndex = (pagenum - 1) * pagesize + this.currentNum;
        this.artificialData.qualified = this.resultTableData[this.currentNum].qualified;
        this.artificialData.reviewRemark =
          !!this.resultTableData[this.currentNum] && this.artificialData.qualified === '2'
            ? this.resultTableData[this.currentNum].reviewRemark
            : '';
        this.detailLoading = false;
      },
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.detail-wrap {
  width: 100%;
  height: 100%;
}
.detail-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
