<template>
  <div class="face-content-wrapper">
    <div class="btn-list">
      <Button @click="handleSort" size="small">
        <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        时间排序
      </Button>
      <!-- <Button size="small" @click="handleExport" v-if="dataList.length">
                <ui-icon type="daoru" color="#2C86F8"></ui-icon>
                导出
            </Button> -->
    </div>
    <div class="face-content warpper-box">
      <div
        v-for="(e, i) in dataList"
        :key="i"
        class="face-item box-item"
        :class="{ active: currentClickIndex == i }"
        @click="chooseMapItem($event, i)"
      >
        <div class="header">
          <div class="header-left">
            <span
              class="serialNumber"
              :class="{ activeNumber: currentClickIndex == i }"
            >
              <span>{{ i + 1 }}</span>
            </span>
            <!-- <span class="header-deviceid">{{ e.vid }}</span> -->
          </div>
          <div class="header-name" v-show-tips>
            {{ e.id || "--" }}
          </div>
          <ui-icon
            class="ml-5 mr-5"
            type="shanchu"
            @click.native.stop="deleteItem(e, i)"
          ></ui-icon>
        </div>
        <div class="content">
          <div class="content-left">
            <img v-lazy="e.traitImg" alt="" />
          </div>
          <div class="content-right">
            <span class="ellipsis">
              <ui-icon type="time" :size="14"></ui-icon>
              <span>{{ e.absTime || "--" }}</span>
            </span>
            <span class="ellipsis">
              <ui-icon type="location" :size="14"></ui-icon>
              <span>{{ e.deviceName || "--" }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="!dataList.length" />
  </div>
</template>

<script>
import { exportExcel } from "@/api/operationsOnTheMap";
export default {
  props: {
    // 当前点击顺序
    currentClickIndex: {
      type: Number,
      default: -1,
    },
    orderType: {
      type: String,
      default: "",
    },
    updateLayerId: {
      type: [String, Number],
      default: "",
    },
    dataList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      timeUpDown: false,
    };
  },
  watch: {
    // currentClickIndex: {
    //     handler (newVal){
    //         if(newVal > -1) {
    //             let list =  document.querySelectorAll('.face-item');
    //             list[newVal].scrollIntoView(false)
    //         }
    //     },
    // },
    orderType: {
      handler(newVal) {
        this.timeUpDown = newVal == "desc" ? false : true;
      },
    },
  },
  methods: {
    chooseMapItem($event, index) {
      $event.stopPropagation();
      this.$emit("chooseMapItem", index);
    },
    // 排序
    handleSort(val) {
      this.timeUpDown = !this.timeUpDown;
      this.$emit("changeOrder", this.timeUpDown ? "asc" : "desc");
    },
    handleExport() {
      exportExcel({
        id: this.updateLayerId,
        excelType: "face",
      }).then((res) => {
        if (res.data) {
          let aLink = document.createElement("a");
          aLink.href = res.data;
          aLink.click();
        }
      });
    },
    deleteItem(item, index) {
      this.$emit("deleteItem", item, index);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
.face-content {
  .score {
    color: #2c86f8 !important;
    font-weight: bold;
  }
  .header-name {
    width: 300px;
    font-size: 14px;
    font-weight: bold;
    color: #f29f4c;
  }
}
</style>
