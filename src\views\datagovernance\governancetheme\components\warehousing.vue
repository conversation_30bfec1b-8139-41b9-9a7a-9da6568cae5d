<template>
  <div class="warehousing">
    <div class="warehousing-header">
      <div class="warehousing-header-bar"></div>
      <div class="warehousing-header-title">数据{{ text }}更新方式</div>
    </div>
    <div class="warehousing-content">
      <p>1、相同数据（所有字段一致）：不再接入</p>
      <p>2、新数据（主键不一致）：直接接入</p>
      <p>
        3、变更数据（主键一致，其他数据有变化）：
        <RadioGroup v-model="type" @on-change="handleChange">
          <Radio label="1">覆盖</Radio>
          <Radio label="2">舍弃</Radio>
          <Radio label="3">同时接入</Radio>
        </RadioGroup>
      </p>
    </div>
    <div class="warehousing-header">
      <div class="warehousing-header-bar"></div>
      <div class="warehousing-header-title">触发数据治理流程条件</div>
    </div>
    <div class="warehousing-content">
      <p>数据有变化时，变化的数据触发检测流程。数据治理组件有变更时，全量数据触发检测流程。</p>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    warehousData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return { type: '', text: '' };
  },
  mounted() {
    this.type = this.warehousData.dataHandle;
  },
  methods: {
    handleChange(val) {
      this.$emit('selectChange', val);
    },
  },
  watch: {
    warehousData() {
      clientInformation;
      this.text = this.warehousData.isDefault === '1' ? '正式入库' : '临时入库';
      return this.warehousData;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.warehousing {
  width: 100%;
  &-header {
    display: flex;
    height: 30px;
    &-bar {
      width: 8px;
      margin-right: 6px;
      background: #239df9;
    }
    &-title {
      .align-flex;
      flex: 1;
      padding-left: 10px;
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
      background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
    }
  }
  &-content {
    padding: 18px 2px;
    p {
      font-size: 14px;
      color: #ffffff;
    }
    p:not(:last-child) {
      margin-bottom: 14px;
    }
  }
  @{_deep} .ivu-modal-body {
    padding: 9px 51px !important;
  }
  // @{_deep} .ivu-modal-body {
  //   padding: 50px !important;
  //   background: chartreuse !important;
  // }
  @{_deep} .ivu-radio-wrapper {
    font-size: 14px;
    margin-right: 30px;
  }
  @{_deep} .ivu-radio-inner {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    background: transparent;
    border: 1px solid var(--color-primary);
  }
  @{_deep} .ivu-radio-inner:after {
    width: 8px;
    height: 8px;
    background: var(--color-primary);
  }
  @{_deep} .ivu-select .ivu-select-selection {
    height: 34px;
    line-height: 34px;
    border-color: var(--color-primary);
    border: 1px solid var(--color-primary);
    border-radius: 4px;
  }
  @{_deep} .ivu-form-item {
    margin-bottom: 14px;
  }
  @{_deep} .ivu-select-arrow {
    color: var(--color-primary);
  }
}
</style>
//
<style lang="less">
// .warehousing {
// }
//
</style>
