(function (window) {
    'use strict';

    var util = function () {

    };

    /**
     * coordinates 转线动画用 GeoJson
     * @param {*} coordinates 
     * @param {*} segDistance 默认 10
     * @param {*} speed 默认 1
     */
    util.coordinatesToSpriteGeoJson = function (coordinates, segDistance, speed) {
        void 0 === segDistance && (segDistance = 10);
        void 0 === speed && (speed = 1);

        var spriteGeoJson = {
            type: "FeatureCollection",
            features: []
        };
        var distanceArray = util.calcDistance(coordinates),
            segPointArray = [];

        for (var i = 0, ci = coordinates.length - 1; i < ci; i++) {
            var segCount = Math.round(distanceArray[i] / segDistance)
                , lngDifference = coordinates[i + 1][0] - coordinates[i][0]
                , latDifference = coordinates[i + 1][1] - coordinates[i][1];

            if (0 !== segCount) {
                for (var j = 0; j <= segCount; j++) {
                    segPointArray.push([coordinates[i][0] + lngDifference / segCount * j, coordinates[i][1] + latDifference / segCount * j]);
                }
            }
        }

        for (var j = 0, cj = segPointArray.length; j < cj - 4; j++) {
            var feature = {
                type: "Feature",
                geometry: {
                    type: "LineString",
                    coordinates: [segPointArray[j], segPointArray[j + 1], segPointArray[j + 2], segPointArray[j + 3]]
                },
                properties: {
                    link_seq: j,
                    status: speed
                }
            };
            spriteGeoJson.features.push(feature);
        }
        return spriteGeoJson
    };

    util.coordinatesToSpriteGeoJson2 = function (coordinates, segDistance, speed) {
        void 0 === segDistance && (segDistance = 10);
        void 0 === speed && (speed = 1);

        // var spriteGeoJson = {
        //     type: "FeatureCollection",
        //     features: []
        // };
        var distanceArray = util.calcDistance(coordinates),
            segPointArray = [];

        for (var i = 0, ci = coordinates.length - 1; i < ci; i++) {
            var segCount = Math.round(distanceArray[i] / segDistance)
                , lngDifference = coordinates[i + 1][0] - coordinates[i][0]
                , latDifference = coordinates[i + 1][1] - coordinates[i][1];

            if (0 !== segCount) {
                for (var j = 0; j <= segCount; j++) {
                    segPointArray.push([coordinates[i][0] + lngDifference / segCount * j, coordinates[i][1] + latDifference / segCount * j]);
                }
            }
        }

        // for (var j = 0, cj = segPointArray.length; j < cj - 4; j++) {
        //     var feature = {
        //         type: "Feature",
        //         geometry: {
        //             type: "LineString",
        //             coordinates: [segPointArray[j], segPointArray[j + 1], segPointArray[j + 2], segPointArray[j + 3]]
        //         },
        //         properties: {
        //             link_seq: j,
        //             status: speed
        //         }
        //     };
        //     spriteGeoJson.features.push(feature);
        // }

        var newCoordinates = [];
        for (var j = 0, cj = segPointArray.length; j < cj - 4; j++) {
            newCoordinates.push(segPointArray[j]);
        }

        return newCoordinates
    };

    /**
     * coordinates 转mark动画用 GeoJson
     * @param {*} coordinates 
     * @param {*} compatible 默认 false
     * @param {*} segDistance 默认 10
     */
    util.coordinatesToSymtrackingGeoJson = function (coordinates, compatible, segDistance) {
        void 0 === compatible && (compatible = !1);
        void 0 === segDistance && (segDistance = 10);

        var trackGeoJson = {
            type: "FeatureCollection",
            features: []
        };
        var distanceArray = util.calcDistance(coordinates),
            segPointCountArray = [];

        for (var i = 0, ci = coordinates.length - 1; i < ci; i++) {
            var segPointCount = Math.round(distanceArray[i] / segDistance);
            segPointCountArray.push(segPointCount)
        }


        for (var j = 0, cj = coordinates.length - 1; j < cj; j++) {
            var segPointCount = segPointCountArray[j],
                firstCoordinate = coordinates[j],
                secondCoordinate = coordinates[j + 1],
                lngDifference = secondCoordinate[0] - firstCoordinate[0],
                latDifference = secondCoordinate[1] - firstCoordinate[1];

            for (var k = 0; k < segPointCount; k++) {
                var linkSeq = util.getDistancesSeq(segPointCountArray, j) + k;

                if (compatible) {
                    var feature = {
                        type: "Feature",
                        geometry: {
                            type: "LineString",
                            coordinates: [[firstCoordinate[0] + k * lngDifference / segPointCount, firstCoordinate[1] + k * latDifference / segPointCount], [firstCoordinate[0] + (k + 1) * lngDifference / segPointCount, firstCoordinate[1] + (k + 1) * latDifference / segPointCount]]
                        },
                        properties: {
                            link_seq: linkSeq
                        }
                    };
                    trackGeoJson.features.push(feature);
                } else {
                    // 点坐标
                    var newCoordinates = [firstCoordinate[0] + k * lngDifference / segPointCount, firstCoordinate[1] + k * latDifference / segPointCount];
                    var point = CalculationUtil.Point.convert(util.lngLat2Mercator(newCoordinates));

                    // 后一个点坐标
                    var afterCoordinates = [firstCoordinate[0] + (k + 1) * lngDifference / segPointCount, firstCoordinate[1] + (k + 1) * latDifference / segPointCount];
                    var afterPoint = CalculationUtil.Point.convert(util.lngLat2Mercator(afterCoordinates));

                    var feature = {
                        type: "Feature",
                        geometry: {
                            type: "Point",
                            coordinates: newCoordinates
                        },
                        properties: {
                            link_seq: linkSeq,
                            car_rotate: -point.angleTo(afterPoint) / Math.PI * 180 + 180
                            // car_rotate: -Math.atan2(L.y - y.y, L.x - y.x) / Math.PI * 180 + 180
                        }
                    };
                    trackGeoJson.features.push(feature);
                }
            }
        }
        return trackGeoJson;
    };

    /**
     * 计算相邻两点之间的距离
     * @param {*} coordinates 
     */
    util.calcDistance = function (coordinates) {
        var distanceArray = [];
        for (var i = 0, ci = coordinates.length - 1; i < ci; i++) {
            var first = util.lngLat2Mercator(coordinates[i])
                , second = util.lngLat2Mercator(coordinates[i + 1])
                , distance = Math.sqrt(Math.pow(first[0] - second[0], 2) + Math.pow(first[1] - second[1], 2));
            distanceArray.push(distance)
        }
        return distanceArray;
    };

    util.getDistancesSeq = function (segPointCountArray, count) {
        var distances = 0;
        for (var n = 0; n < count; n++) {
            distances += segPointCountArray[n];
        }
        return distances
    };

    /**
     * 经纬度转墨卡托
     * @param {*} coordinate
     */
    util.lngLat2Mercator = function (coordinate) {
        var mercatorCoordinate = []
            , lng = 20037508.34 * coordinate[0] / 180
            , lat = Math.log(Math.tan((90 + coordinate[1]) * Math.PI / 360)) / (Math.PI / 180);
        lat = 20037508.34 * lat / 180;
        mercatorCoordinate.push(lng);
        mercatorCoordinate.push(lat);
        return mercatorCoordinate
    };


    var point = function (lng, lat, height) {
        this.x = lng;
        this.y = lat;
        this.z = height || 0;
    };

    point.convert = function (coordinate) {
        return coordinate instanceof point ? coordinate : Array.isArray(coordinate) ? new point(coordinate[0], coordinate[1]) : coordinate;
    };

    point.prototype.angleTo = function (point) {
        return Math.atan2(this.y - point.y, this.x - point.x)
    };

    window.CalculationUtil = util;
    window.CalculationUtil.Point = point;
}(window));