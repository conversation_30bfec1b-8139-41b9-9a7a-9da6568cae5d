<template>
  <div class="image-data-monitor" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-shitushujujiance">
      图像数据检测
      <div class="title-right">
        <i
          class="icon-font icon-zhanghuxinxi"
          :class="{ unactive: indexModuleType !== 'face' }"
          :title="'人脸' + indexModuleType"
          @click="echartsAll('face')"
        ></i>
        <i
          class="icon-font icon-cheliangshitushuju"
          :class="{ unactive: indexModuleType !== 'car' }"
          title="车辆"
          @click="echartsAll('car')"
        ></i>
      </div>
    </HomeTitle>
    <!-- v-ui-loading="{ loading: echartsLoading, tableData: echartData }" -->
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: dataList }">
      <TypeSelect
        class="type-select"
        @getName="getName"
        :dataList="typeList"
        defaultName="全部指标"
        :select="true"
      ></TypeSelect>
      <draw-echarts
        v-if="dataList.length != 0"
        ref="drawEcharts"
        class="charts"
        @echartClick="echartClick"
        :echartOption="echartOption"
      ></draw-echarts>
      <span class="next-echart" v-if="dataList.length > sizeNumber">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('drawEcharts', dataList, [], sizeNumber)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'image-data-monitor',
  mixins: [dataZoom],
  props: {
    batchIds: {
      type: Array,
      default: () => [],
    },
    indexModules: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      sizeNumber: 13,
      echartOption: {},
      ringStyle: {
        width: '100%',
        height: '230px',
      },
      dataList: [],
      dataColor: [
        '#FACD89',
        '#F59835',
        '#683AED',
        '#0CD084',
        '#4193FC',
        '#CABDDD',
        '#FFF14F',
        '#FF6E82',
        '#A09858',
        '#11DDE0',
        '#CABDDD',
        '#0CD084',
        '#F59835',
        '#4193FC',
        '#FFF14F',
        '#FF6E82',
        '#11DDE0',
      ],
      dataObj: [], // 已处理数据格式
      nameArr: [], // x轴
      echartsShow: false,
      echartsLoading: false,
      name: '全部指标',
      indexModuleType: 'car', // 默认选择车辆
      dataTypeColor: {
        face: ['#CABDDD', '#0CD084', '#F59835', '#4193FC', '#FFF14F', '#FF6E82', '#11DDE0'],
        car: [
          '#FACD89',
          '#F59835',
          '#683AED',
          '#0CD084',
          '#4193FC',
          '#CABDDD',
          '#FFF14F',
          '#FF6E82',
          '#A09858',
          '#11DDE0',
        ],
      },
      imageDataTest: [],
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      getHomeConfig: 'home/getHomeConfig',
    }),
    typeList() {
      let arr = [{ name: '全部指标' }];
      this.dataObj.map((val) => {
        arr.push({ name: val.key });
      });
      return arr;
    },
  },
  mounted() {
    if (window.innerWidth < 1850 && window.innerWidth > 1460) {
      this.sizeNumber = 10;
    } else if (window.innerWidth < 1460) {
      this.sizeNumber = 8;
    } else if (window.innerWidth > 1850) {
      this.sizeNumber = 13;
    }
    window.addEventListener('resize', this.resize);
  },
  methods: {
    echartClick(params) {
      let evaluationResultIndexList = this.dataList[params.dataIndex].evaluationResultIndexList;
      let one = evaluationResultIndexList.find((item) => item.indexId === params.data.indexId);
      this.$emit('makeCreateTabs', one);
    },
    async queryDeviceCheckColumnReports() {
      try {
        this.echartsLoading = true;
        const params = {
          regionCode: this.getHomeConfig.regionCode,
          batchIds: this.batchIds,
          indexModule: this.indexModules[this.indexModuleType].value,
        };
        const {
          data: { data },
        } = await this.$http.post(home.queryHomePageResultIndex, params);
        if (!!data && data.length) {
          let filterData = data.filter(
            (item) => !!item.evaluationResultIndexList && item.evaluationResultIndexList.length,
          );
          if (filterData.length) {
            this.dataList = filterData.slice(0, 13);
            this.initAll();
          }
        }
        this.echartsLoading = false;
      } catch (e) {
        this.echartsLoading = false;
        console.log(e);
      }
    },
    getName(val) {
      if (val !== '全部指标') {
        this.name = val;
        this.init();
      } else {
        this.initAll();
      }
    },
    echartsAll(type) {
      this.$set(this, 'indexModuleType', type);
      this.name = '全部指标';
      this.$nextTick(() => {
        this.queryDeviceCheckColumnReports();
      });
    },
    fontSize(res) {
      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      const fontSize = clientWidth / 1920;
      return res * fontSize;
    },
    // 全部
    initAll() {
      this.dataObj = [];
      this.dataColor = this.dataTypeColor[this.indexModuleType];
      // 多指标展示数据
      this.nameArr = []; // x轴城市
      const tooltip = {
        show: 'true',
        trigger: 'axis', //触发类型
        axisPointer: { type: 'none' }, //去掉移动的指示线
        // confine: true,
        padding: [8, 10], //内边距
        extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
        formatter: function (params) {
          let result = `<div>${params[0].name}</div>`;
          params.forEach(function (item, index) {
            // 去除特殊样式series
            if (item.componentSubType !== 'pictorialBar' && item.seriesName !== '背景') {
              let dotHtml = `<div style="display: inline-block;width: 310px;"><span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
              const seriesName = `<span style="display:inline-block; margin-left:10px;">${item.seriesName}</span>`;
              const number = `<span style="display:inline-block;float: right;">${Math.abs(
                item.value || 0,
              )}%</span></div>`;
              if ((index + 1) % 2 === 0) {
                dotHtml = `<div style="display: inline-block;width: 310px; margin-left:20px;"><span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
                result += dotHtml + seriesName + number + '</br>';
              } else {
                result += dotHtml + seriesName + number;
              }
            }
          });
          return result;
        },
      };
      let evaluationResultIndexList = 0; // 用于判断初次evaluationResultIndexList为空数组,evaluationResultIndexList === dataList.length代表暂无数据
      this.dataList.forEach((val) => {
        this.nameArr.push(val.regionName);
        if (val.evaluationResultIndexList.length === 0) {
          this.dataObj.map((vals) => {
            vals.val.push(0);
          });
          if (this.dataObj.length === 0) {
            evaluationResultIndexList += 1;
          }
        }
        const keyArr = this.dataObj.map((item) => item.key);
        val.evaluationResultIndexList.map((item, index) => {
          if (!keyArr.includes(item.indexName)) {
            if (evaluationResultIndexList >= 1) {
              let arrays = {
                color: this.dataColor[index],
                val: [],
                key: item.indexName,
                indexId: item.indexId,
              };
              for (let i = 0; i < evaluationResultIndexList; i++) {
                arrays.val.push(0);
              }
              arrays.val.push(item.resultValue);
              this.dataObj.push(arrays);
            } else {
              this.dataObj.push({
                color: this.dataColor[index],
                val: [item.resultValue],
                key: item.indexName,
                indexId: item.indexId,
              });
            }
          } else {
            this.dataObj.map((i) => {
              if (i.key === item.indexName) {
                i.val.push(item.resultValue);
              }
            });
          }
        });
      });
      if (evaluationResultIndexList === this.dataList.length) {
        this.dataList = [];
      }
      let service = [];
      this.dataObj.map((val) => {
        service.push({
          name: val.key,
          type: 'bar',
          barWidth: this.$util.common.fontSize(33),
          stack: '数量',
          data: val.val.map((item) => {
            return { value: Math.abs(item) || 0, indexId: val.indexId };
          }),
          itemStyle: {
            color: val.color,
          },
          z: 10,
          zlevel: 0,
        });
      });
      var option = {
        grid: {
          top: 30,
          right: '3%',
          left: '1%',
          bottom: '2%',
          containLabel: true,
        },
        tooltip: tooltip,
        // legend: legend,
        xAxis: [
          {
            data: this.nameArr,
            axisLine: {
              lineStyle: {
                color: '#0375B4',
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              color: '#e2e9ff',
              fontSize: this.fontSize(12),
              formatter: function (params) {
                let newName = '';

                if (params.length > 3) {
                  newName = params.substring(0, 3) + '...';
                } else {
                  newName = params;
                }
                return newName;
              },
            },
          },
        ],
        yAxis: { show: false },
        series: service,
      };
      this.echartOption = option;
      setTimeout(() => {
        this.setDataZoom('drawEcharts', [], this.sizeNumber);
      });
    },
    // 单个指标
    init() {
      const _this = this;
      const tooltip = {
        show: 'true',
        trigger: 'axis', //触发类型
        axisPointer: {
          //去掉移动的指示线
          type: 'none',
        },
        confine: true,
        padding: [8, 10], //内边距
        extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
        formatter: function (params) {
          var result = `<div>${params[0].name}</div>`;
          params.forEach(function (item) {
            // 去除特殊样式series
            if (item.componentSubType !== 'pictorialBar') {
              var dotHtml = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
              var seriesName = `<span style="display:inline-block; margin-left:10px;">${_this.name}</span>`;
              var number = `<span style="display:inline-block;float: right;margin-left:10px;">${Math.abs(
                item.value,
              )}%</span>`;
              result += dotHtml + seriesName + number + '</br>';
            }
          });
          return result;
        },
      };
      var data = {};
      this.dataObj.map((val) => {
        if (val.key === this.name) {
          data = val;
          this.imageDataTest = val;
        }
      });
      var option = {
        tooltip: tooltip,
        grid: {
          top: 30,
          right: '3%',
          left: '1%',
          bottom: '2%',
          containLabel: true,
        },
        xAxis: [
          {
            data: this.nameArr,
            axisLine: {
              lineStyle: {
                color: '#0375B4',
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
              color: '#e2e9ff',
              fontSize: this.fontSize(12),
              formatter: function (params) {
                let newName = '';

                if (params.length > 3) {
                  newName = params.substring(0, 3) + '...';
                } else {
                  newName = params;
                }
                return newName;
              },
            },
          },
        ],
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#0375B4',
            },
          },
          min: 'dataMin',
          max: '100',
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            color: '#fff',
            fontSize: this.fontSize(12),
            formatter: function (value) {
              return value + '%';
            },
          },
        },
        series: [
          {
            type: 'bar',
            data: data.val.map((item) => {
              return { value: Math.abs(item) || 0, indexId: data.indexId };
            }),
            barWidth: this.$util.common.fontSize(33),
            z: 10,
            zlevel: 0,
            test: 'aaa',
            itemStyle: {
              color: data.color,
            },
          },
        ],
      };
      this.echartOption = option;
      setTimeout(() => {
        this.setDataZoom('drawEcharts', [], this.sizeNumber);
        // this.setDataZoom('drawEcharts', [], this.comprehensiveConfig.homeNum)
      });
    },
    // 监听屏宽变化
    resize() {
      if (window.innerWidth < 1850 && window.innerWidth > 1460) {
        this.sizeNumber = 10;
      } else if (window.innerWidth < 1460) {
        this.sizeNumber = 8;
      } else if (window.innerWidth > 1850) {
        this.sizeNumber = 13;
      }
      if (!this.timer) {
        this.timer = true;
        setTimeout(() => {
          this.$nextTick(() => {
            this.name !== '全部指标' ? this.init() : this.initAll();
          });
          this.timer = false;
        }, 400);
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize);
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
    TypeSelect: require('./type-select').default,
  },
  watch: {
    batchIds: {
      handler(val) {
        if (!val.length || !this.getHomeConfig || !this.getHomeConfig.regionCode) return false;
        this.$nextTick(() => {
          this.queryDeviceCheckColumnReports();
        });
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.image-data-monitor {
  position: absolute;
  bottom: 10px;
  left: 488px;
  height: 30%;
  width: 49%;
  background: rgba(0, 104, 183, 0.13);
  z-index: 11;
  .determinant-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 32px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
  .type-select {
    position: absolute;
    right: 20px;
    top: 4px;
    z-index: 12;
  }
  .title-right {
    display: inline-block;
    float: right;
    i {
      margin-right: 10px;
    }
    cursor: pointer;
    .unactive {
      color: #2596b0 !important;
    }
  }
  .next-echart {
    top: 50%;
    right: 0;
    position: absolute;

    .icon-zuojiantou1 {
      color: rgba(45, 190, 255, 1);
      font-size: 12px;
      vertical-align: top !important;
    }
    &:active {
      .icon-zuojiantou1 {
        color: #4e9ef2;
        font-size: 12px;
        vertical-align: top !important;
      }
    }
    &:hover {
      .icon-zuojiantou1 {
        color: var(--color-primary);
        font-size: 12px;
        vertical-align: top !important;
      }
    }
  }
}
.full-screen-container {
  position: absolute;
  height: 30%;
  margin-left: 10px;
}
</style>
