<template>
  <div class="dom">
    <section class="dom-content">
      <!-- <swiper ref="mySwiper" :options="swiperOption" class="my-swiper">
        <template v-for="(item, index) in list">
          <swiper-slide :key="index">
            <div class="swiper-item">
              {{ index }}
            </div>
          </swiper-slide>
        </template>
        <div class="swiper-button-prev" slot="button-prev">
          <ui-icon type="doubleleft"></ui-icon>
        </div>
        <div class="swiper-button-next" slot="button-next">
          <ui-icon type="doubleright"></ui-icon>
        </div>
      </swiper> -->
      <div class="collection-list" v-if="collectionList.length">
        <template v-for="(item, index) in collectionList">
          <div
            :key="index"
            @click="selectCollectionHandler(index)"
            :class="{ active: currentCollectionIndex === index }"
          >
            <ui-icon type="ZM-dianwei" :size="20" :color="'#2C86F8'"></ui-icon>
            <span>{{ item.count }}</span>
          </div>
        </template>
      </div>
      <div class="collection-info">
        <div class="title">
          <span>采集详情</span>
          <operate-bar
            :list="collectInfo"
            :tabType="{ type: 12 }"
            @collection="collection($event)"
          ></operate-bar>
        </div>
        <div class="dom-content-p">
          <span class="label">IMSI编码</span>：
          <span class="message">{{ collectInfo.imsi }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">采集时间</span>：
          <span class="message">{{ collectInfo.absTime }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">采集地点</span>：
          <span class="message">{{ collectInfo.detailAddress }}</span>
        </div>
      </div>
      <div class="device-info">
        <div class="title">设备信息</div>
        <div class="dom-content-p">
          <span class="label">设备名称</span>：
          <span class="message">{{ deviceInfo.deviceName || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">设备状态</span>：
          <span class="message">{{
            deviceInfo.onlineStatus == 0 ? "在线" : "不在线"
          }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">设备编码</span>：
          <span class="message">{{ deviceInfo.deviceId || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">安装地点</span>：
          <span class="message" v-show-tips>{{
            deviceInfo.detailAddress || "--"
          }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">所属区域</span>：
          <span class="message">{{ deviceInfo.placeName || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">地理坐标</span>：
          <span class="message" v-show-tips
            >{{ deviceInfo.geoPoint.lat }};{{ deviceInfo.geoPoint.lon }}</span
          >
        </div>
      </div>
    </section>
    <footer v-if="cutIcon">
      <search-around
        @preDetial="preDetial"
        @nextDetail="nextDetail"
      ></search-around>
    </footer>
  </div>
</template>
<script>
// import { swiper, swiperSlide } from 'vue-awesome-swiper'
import { getBaseStationInfoBCarNo } from "@/api/operationsOnTheMap";
import operateBar from "@/components/mapdom/operate-bar.vue";
import searchAround from "../search-around.vue";
import { mapMutations, mapGetters } from "vuex";
export default {
  components: {
    operateBar,
    searchAround,
    // swiper,
    // swiperSlid,
    // UiIcone
  },
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      currentTabIndex: 0,
      list: [1, 2, 3, 4],
      swiperOption: {
        // effect: 'coverflow',
        slidesPerView: 12,
        centeredSlides: true,
        loop: true,
        loopAdditionalSlides: 2,
        speed: 500,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      collectionList: [],
      currentCollectionIndex: -1,
      collectInfo: {}, // 采集信息
      deviceInfo: {
        geoPoint: {}, // 经纬度
      },
      cutIcon: true,
    };
  },
  computed: {
    ...mapGetters({
      getCollectJudge: "map/getCollectJudge",
      getClickObj: "map/getClickObj",
    }),
  },
  watch: {
    getCollectJudge: {
      handler(val) {
        if (this.collectInfo.id == this.getClickObj.id) {
          let collect = val % 2 == 0 ? 2 : 1;
          this.$set(this.collectInfo, "myFavorite", collect);
        }
      },
      immediate: true,
    },
  },
  methods: {
    ...mapMutations({
      setCollectJudge: "map/setCollectJudge",
      setClickObj: "map/setClickObj",
    }),
    init({ deviceId = "", myFavorite }, pointItem, name, cutIcon = true) {
      return getBaseStationInfoBCarNo({ deviceId })
        .then((res) => {
          if (res.code === 200) {
            this.collectInfo = pointItem;
            this.collectInfo.myFavorite = myFavorite;
            this.deviceInfo = res.data;
            this.cutIcon = cutIcon; //用于判断模态框下方左右切换显隐
            return res;
          }
        })
        .catch((err) => console.log(err))
        .finally(() => {});
    },
    preDetial() {
      let Index = this.collectInfo.currentClickIndex;
      if (Index < 1) return;
      this.$emit("preDetial", Index - 1);
    },
    nextDetail() {
      let Index = this.collectInfo.currentClickIndex;
      if (Index >= this.positionPoints.length - 1) return;
      this.$emit("nextDetail", Index + 1);
    },
    tabClick(index) {
      this.currentTabIndex = index;
    },
    // 收藏/取消收藏
    collection($event) {
      this.$set(this.collectInfo, "myFavorite", $event);
      if ($event == this.getCollectJudge) {
        let changedata = $event + 2;
        this.setCollectJudge(changedata);
      } else {
        this.setCollectJudge($event);
      }
      this.setClickObj({ ...this.getClickObj, myFavorite: $event });
    },
    selectCollectionHandler(index) {
      this.currentCollectionIndex = index;
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
  //   padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}

.dom {
  width: 100%;
  // height: 381px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;
  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
    display: flex;
    justify-content: space-between;
  }
  &-content {
    padding: 15px 20px;
    font-size: 14px;

    .collection-list {
      display: flex;
      margin-bottom: 10px;
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border: 1px solid #d3d7de;
        margin-right: 8px;
        cursor: pointer;
        position: relative;

        > span {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: #2c86f8;
          text-align: center;
          border-radius: 0px 0px 4px 0px;
          font-size: 12px;
          color: #ffffff;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .active {
        border: 3px solid rgba(44, 134, 248, 1);
      }
    }
    .collection-info {
      display: flex;
      flex-direction: column;
      .dom-content-p {
        margin-top: 6px;
        width: 540px;
        display: flex;
        .label {
          font-size: 12px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.6);
          white-space: nowrap;
          width: 60px;
          text-align: justify;
          text-align-last: justify;
          text-justify: inter-ideograph;
        }
        .message {
          font-size: 12px;
          font-weight: bold;
          width: 520px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.9);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .address {
          font-size: 12px;
          font-weight: bold;
          width: 140px;
          margin-left: 12px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
    .device-info {
      margin-top: 15px;
      display: flex;
      flex-wrap: wrap;
      .dom-content-p {
        margin-top: 6px;
        width: 50%;
        display: inline-block;
        display: flex;
        align-items: center;
        .label {
          // height: 16px;
          // line-height: 16px;
          font-size: 12px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.6);
          white-space: nowrap;
          width: 60px;
          text-align: justify;
          text-align-last: justify;
          text-justify: inter-ideograph;
        }
        .message {
          // height: 16px;
          // line-height: 16px;
          font-size: 12px;
          font-weight: bold;
          width: 180px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.9);
        }
        .address {
          font-size: 12px;
          font-weight: bold;
          width: 140px;
          margin-left: 12px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
  }
  > footer {
    height: 50px;
    border-top: 1px solid #d3d7de;
    padding: 0 20px;
    height: 55px;
    line-height: 55px;
  }
}
.title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  padding-left: 9px;
  position: relative;
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  > span {
    font-weight: bold;
  }
}
.title:before {
  content: "";
  position: absolute;
  width: 3px;
  height: 16px;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
  background: #2c86f8;
}
.my-swiper {
  margin-bottom: 10px;
  width: 100%;
  .swiper-item {
    width: 40px;
    height: 40px;
    border: 1px solid #d3d7de;
    box-sizing: border-box;
    padding: 10px;
  }
  .swiper-button-prev {
    height: 40px;
    top: 25px;
    width: 20px;
    left: 0;
    opacity: 1;
    background-image: none;
    z-index: 100;
  }
  .swiper-button-next {
    height: 40px;
    top: 25px;
    width: 20px;
    right: 0;
    opacity: 1;
    background-image: none;
    z-index: 100;
  }
}
</style>
