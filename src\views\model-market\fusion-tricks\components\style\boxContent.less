.object-information{
    width: 370px;
    // background: #fff;
    // box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    margin-top: 10px;
    .box-modal{
        position: relative;
        // height: calc( ~'100% - 20px' );
        height: calc( ~'100% - 20px' );
        .box-content{
            padding: 15px 15px 0 15px;
            position: relative;
            // max-height: calc( ~'100% - 50px' );
            overflow: auto;
            min-height: 180px;
            max-height: 100%;
            background: #fff;
            box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
            transition: max-height 0.1s ease-out;
            .box-list{
                background: #F9F9F9;
                padding: 5px 10px 8px 5px;
                margin-bottom: 10px;
                &:hover{
                    background: rgba(44, 134, 248, 0.1);
                }
                .content-top{
                    display: flex;
                    // overflow: auto;
                    &-img{
                        width: 80px;
                        height: 80px;
                        border: 1px solid #CFD6E6;
                        position: relative;
                        .similarity {
                            position: absolute;
                            left: 0;
                            top: 0;
                            span {
                                padding: 2px 5px;
                                background: #4597ff;
                                color: #fff;
                                border-radius: 4px;
                            }
                        }
                    }
                    .content-top-right{
                        margin-left: 11px;
                        margin-top: 3px;
                        font-size: 14px;
                        flex: 1;
                        /deep/ .iconfont{
                            margin-right: 5px;
                        }
                        
                        .bule{
                            color: #2C86F8;
                        }
                        .orange{
                            color: #F29F4C;
                        }
                        .block{
                            color: #000000;
                        }
                    }
                }
                .content-bottom{
                    display: flex;
                    justify-content: space-between;
                    margin-top: 5px;
                    .iconList{
                        width: 80px;
                    }
                    .analyseIcon{
                        font-size: 12px;
                        color: #5584FF;
                        cursor: pointer;
                        span{
                            margin-left: 10px;
                        }
                        .icon-bansuitonghang{
                            color: #5584FF !important;
                        }
                        .icon-duoren{
                            color: #5584FF !important;
                        }
                        &:hover{
                            color: #4597FF;
                        }
                    }
                    .activeAnaly{
                        color: #1A74E7;
                    }
                    .operation{
                        display: flex;
                        .analyseIcon{
                            font-size: 12px;
                            color: #2C86F8;
                            cursor: pointer;
                            margin-left: 21px;
                            &:hover{
                                color: #4597FF;
                            }
                            span{
                                margin-left: 11px;
                            }
                        }
                        
                    }
                }
            }
            .active-box-list{
                background: rgba(44, 134, 248, 0.1);
            }
        }
        .overflow-box{
            // height: 430px;
            // max-height: calc(100% - 0.26042rem);
        }
    }
}