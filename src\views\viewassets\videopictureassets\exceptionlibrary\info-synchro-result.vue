<template>
  <ui-modal v-model="visible" title="信息自动同步" width="45rem" @onCancel="handleReset">
    <Form ref="formValidate" :model="formValidate">
      <FormItem v-for="(item, index) in resultForm" :key="index" :label="item.label" prop="name">
        <RadioGroup v-model="formValidate.gender">
          <Radio label="male">是</Radio>
          <Radio label="female">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      visible: false,
      formValidate: {
        name: 'male',
        mail: '',
        city: '',
        gender: 'male',
        interest: [],
        date: '',
        time: '',
        desc: '',
      },
      resultForm: [
        {
          label: '1、从国标平台获取【摄像机功能类型】为【人脸卡口】，是否自动填充：',
          key: '',
        },
        {
          label: '2、从国标平台获取【设备厂商名称】为【海康威视】，原始值为【大华】，是否替换：',
          key: '',
        },
        {
          label: '3、通过安装地址，自动获取【行政区划】为【济南市历城区】，原始值为【济南市】，是否替换：',
          key: '',
        },
        {
          label: '4、通过连接设备SDK，自动获取【MAC地址】为【AN-12-SD-12-E4】，是否自动填充：',
          key: '',
        },
        {
          label: '5、通过连接设备SDK，自动获取【MAC地址】为【AN-12-SD-12-E4】，是否自动填充',
          key: '',
        },
        {
          label: '6、通过最新的视频流检测结果，【设备是否在线状态】为【在线】，原始值为【离线】，已自动更新。',
          key: '',
        },
      ],
    };
  },
  created() {},
  methods: {
    init() {
      this.visible = true;
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.$Message.success('Success!');
        } else {
          this.$Message.error('Fail!');
        }
      });
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
</style>
