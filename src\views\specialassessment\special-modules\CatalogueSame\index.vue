<template>
  <div class="video-online auto-fill">
    <component
      v-if="!!componentName"
      :is="componentName"
      v-bind="$attrs"
      :index-data="indexData"
      :detail-data="detailData"
      @changeComponentName="changeComponentName"
    ></component>
  </div>
</template>
<script>
export default {
  name: 'videoOnline',
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      componentName: 'StatisticsList',
      detailData: {},
    };
  },
  methods: {
    changeComponentName([name, row]) {
      this.detailData = { ...row };
      this.$set(this, 'componentName', name);
    },
  },
  components: {
    StatisticsList: require('./components/statistics-list/index.vue').default,
    MonthReviewDetail: require('@/views/specialassessment/special-modules/components/month-review-detail/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.video-online {
  //padding: 0px 20px;
}
</style>
