<template>
  <ui-modal v-model="visible" title="IP地址格式检测配置" width="35%" @query="submitHandle">
    <loading v-if="dataLoading" />
    <div class="ip-container">
      <p class="mb-md base-text-color">1、检测IP地址格式是否正确。</p>
      <p class="mb-md base-text-color">2、检测不能为如下非法IP地址</p>
      <Form class="base-form" ref="extraParam" :model="extraParam" :label-width="0">
        <FormItem
          v-for="(item, index) in extraParam.items"
          :key="index"
          class="mb-md flex-content"
          :prop="'items.' + index + '.value'"
        >
          <!--<Input type="text"  placeholder="请输入IP地址" style="width: 50%"></Input>-->
          <ip-address-input :ref="'ip' + index" v-model="item.value" />
          <i @click="handleAdd" class="icon-font icon-tree-add f-16 ml-sm color-primary"></i>
          <i
            v-if="index !== 0"
            @click="handleRemove(index)"
            class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
          ></i>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
import cascade from '@/config/api/cascade';
import ipAddressInput from './ip-address-input';
export default {
  name: 'ip-address',
  components: { ipAddressInput },
  props: {},
  data() {
    return {
      indexRuleId: '',
      visible: false,
      dataLoading: false,
      formData: {
        list: [],
        ruleId: '',
      },
      extraParam: {
        items: [
          {
            value: '',
          },
        ],
      },
    };
  },
  methods: {
    init({ indexRuleId }) {
      this.visible = true;
      this.handleReset('extraParam');
      this.indexRuleId = indexRuleId;
      this.getIPConfig();
    },
    async getIPConfig() {
      this.dataLoading = true;
      try {
        let params = {
          ruleId: this.indexRuleId,
        };
        let {
          data: { data },
        } = await this.$http.post(cascade.configQueryAllData, params);
        let dataRight = data.right;
        let extraParamData = dataRight[0].extraParam;
        let excludeIp = extraParamData ? JSON.parse(extraParamData).excludeIp : [''];
        let items = excludeIp.map((item) => {
          return { value: item };
        });
        this.formData.list = dataRight;
        this.extraParam.items = items;
        this.dataLoading = false;
      } catch (e) {
        console.error(e);
        this.dataLoading = false;
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    handleAdd() {
      this.extraParam.items.push({
        value: '',
      });
    },
    handleRemove(index) {
      if (this.extraParam.items.length < 2) {
        return this.$Message.error('至少得留一个吧');
      }
      this.extraParam.items.splice(index, 1);
    },
    async submitHandle() {
      try {
        let resultFromData = { ...this.formData };
        resultFromData.list[0].extraParam = JSON.stringify({
          excludeIp: this.extraParam.items.map((item) => item.value),
        });
        resultFromData.ruleId = this.indexRuleId;
        await this.$http.post(cascade.configUpdateAllData, resultFromData);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.error(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.flex-content {
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}

.color-primary {
  color: var(--color-primary);
}
.base-form {
  max-height: 550px;
  overflow-y: auto;
}
</style>
