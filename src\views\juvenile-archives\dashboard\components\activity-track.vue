<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-19 13:48:42
 * @Description: 
-->
<template>
  <ui-card title="娱乐场所轨迹">
    <barEchart
      v-if="trackingSeries[0].data.length !== 0"
      :title="{}"
      :legend="legend"
      :grid="grid"
      :xAxis="trackingXAxis"
      :yAxis="yAxis"
      :series="trackingSeries"
    ></barEchart>
    <ui-loading v-if="trackingLoading" />
    <ui-empty v-if="trackingSeries[0].data.length === 0"></ui-empty>
  </ui-card>
</template>
<script>
import * as echarts from "echarts";
import { mapGetters, mapActions } from "vuex";
import barEchart from "@/components/echarts/bar-echart";
import {
  appearInRecreationStatisticsArchives,
  getConfigPlaceSecondLevels,
} from "@/api/monographic/juvenile.js";
export default {
  components: { barEchart },
  props: {
    archiveNo: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      trackingLoading: false,
      legend: {
        show: false,
      },
      grid: {
        left: "0",
        top: "5%",
        right: "0.1%",
        bottom: "0",
        containLabel: true,
      },

      yAxis: {
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
        splitLine: {
          lineStyle: {
            type: "solid",
            color: "#D3D7DE",
          },
        },
      },
      // 活动轨迹x轴
      trackingXAxis: {
        data: [],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      // 活动轨迹对应值配置
      trackingSeries: [
        {
          name: "白天",
          type: "bar",
          stack: "one",
          data: [],
          barWidth: "20",
          itemStyle: {
            normal: {
              barBorderRadius: [15, 15, 15, 15],
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1, //渐变色在下面修改，这里是透明度
                [
                  {
                    offset: 0,
                    color: "#5BA3FF",
                  },
                  {
                    offset: 1,
                    color: "#2C86F8",
                  },
                ]
              ),
            },
          },
        },
      ],
      placeSecondLevelList: [],
    };
  },
  computed: {},
  created() {},
  async mounted() {
    this.trackingLoading = true;
    await this.getDictData();
    await this.getConfigPlace();
    await this.movementTtrackingStatistics();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 活动轨迹
    async movementTtrackingStatistics() {
      this.trackingLoading = true;
      try {
        const res = await appearInRecreationStatisticsArchives(this.archiveNo);
        const placeList = Object.keys(res.data);
        let x = [];
        let y = [];
        for (let i = 0; i < this.placeSecondLevelList.length; i++) {
          let index = placeList.indexOf(this.placeSecondLevelList[i].typeCode);
          if (index !== -1) {
            y.push(res.data[placeList[index]]);
            x.push(this.placeSecondLevelList[i].typeName);
          }
        }
        this.trackingXAxis.data = x;
        this.trackingSeries[0].data = y;
      } catch (e) {
        console.log(e);
      } finally {
        this.trackingLoading = false;
      }
    },
    // 获取场所信息
    async getConfigPlace() {
      const { data = [] } = await getConfigPlaceSecondLevels();
      this.placeSecondLevelList = data || [];
    },
  },
};
</script>
<style lang="less" scoped></style>
