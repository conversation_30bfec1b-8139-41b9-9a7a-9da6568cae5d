<template>
  <ui-modal v-model="visible" title="信息自动同步" width="42.1rem" @onCancel="handleReset">
    <div class="synchro-header">
      <div class="synchro-header-bar"></div>
      <div class="synchro-header-title">信息来源</div>
    </div>
    <Form ref="formValidate" :model="lngLatData" :label-width="0">
      <FormItem
        label=""
        prop="point"
        :rules="[
          {
            required: lngLatData.isPoint,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <div class="synchro-item">
          <Checkbox class="checkdesc" v-model="lngLatData.isPoint" @on-change="validChange('point')"></Checkbox>
          <p>通过国标平台自动获取如下字段信息</p>
        </div>
        <CheckboxGroup class="align-flex" v-model="lngLatData.point">
          <Checkbox class="align-flex" :label="`${global.filedEnum.sbgnlx}`"></Checkbox>
          <Checkbox class="align-flex" label="设备厂商名称"></Checkbox>
          <Checkbox class="align-flex" label="设备规格型号"></Checkbox>
          <Checkbox class="align-flex" label="IPV4地址"></Checkbox>
          <Checkbox class="align-flex" label="设备是否在线"></Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem
        label=""
        prop="rice"
        :rules="[
          {
            required: lngLatData.isDeviation,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <div class="synchro-item">
          <Checkbox class="checkdesc" v-model="lngLatData.isDeviation"></Checkbox>
          <p>通过安装地址/经纬度信息，自动获取行政区划</p>
        </div>
        <RadioGroup v-model="formValidate.gender">
          <Radio label="male">安装地址为主</Radio>
          <Radio label="female">经纬度信息为主</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="">
        <div class="synchro-item">
          <Checkbox class="checkdesc" v-model="lngLatData.isCross"></Checkbox>
          <p>通过连接设备SDK或者全网扫描工具，自动获取设备MAC地址</p>
        </div>
      </FormItem>
    </Form>
    <div class="synchro-header">
      <div class="synchro-header-bar"></div>
      <div class="synchro-header-title">同步策略</div>
    </div>
    <Form ref="formValidate" :model="lngLatData" :label-width="0">
      <FormItem
        label=""
        prop="point"
        :rules="[
          {
            required: lngLatData.isPoint,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <div class="synchro-item">
          <Checkbox class="checkdesc" v-model="lngLatData.isPoint" @on-change="validChange('point')"></Checkbox>
          <p>字段为空时，自动填充</p>
        </div>
      </FormItem>
      <FormItem
        label=""
        prop="rice"
        :rules="[
          {
            required: lngLatData.isDeviation,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <div class="synchro-item">
          <Checkbox class="checkdesc" v-model="lngLatData.isDeviation"></Checkbox>
          <p>字段不为空时：</p>
        </div>
        <RadioGroup v-model="formValidate.gender">
          <Radio label="male">覆盖原始值</Radio>
          <Radio label="female">保留原始值</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="">
        <div class="synchro-item">
          <Checkbox class="checkdesc" v-model="lngLatData.isCross"></Checkbox>
          <p>通过视频流检测结果，自动更新设备是否在线状态</p>
        </div>
      </FormItem>
    </Form>
    <div class="synchro-header">
      <div class="synchro-header-bar"></div>
      <div class="synchro-header-title">同步范围</div>
    </div>
    <Form ref="formValidate" :model="lngLatData" :label-width="0">
      <FormItem
        label=""
        prop="rice"
        :rules="[
          {
            required: lngLatData.isDeviation,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <RadioGroup v-model="formValidate.gender">
          <Radio label="male">全部设备</Radio>
          <Radio label="female">自定义设备</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <div class="device-content">
      <div class="camera fl" @click="selectCamera">
        <span class="font-blue camera-text" v-if="deviceIdList.length">已选择111条设备</span>
        <span v-else>
          <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
          <span class="font-blue camera-text">请选择摄像机</span>
        </span>
      </div>
    </div>
    <template slot="footer">
      <Button type="primary" @click="handleSave" class="plr-30">确 定</Button>
    </template>
    <select-device ref="SelectDevice" :selectedList="deviceIdList" @getDeviceIdList="getDeviceIdList"></select-device>
  </ui-modal>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      visible: false,
      lngLatData: {},
      topicComponentId: '',
      formValidate: {
        name: '',
      },
      ruleValidate: {
        // point: ,
        isDeviation: [
          {
            required: true,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ],
      },
      deviceIdList: [],
    };
  },
  created() {},
  methods: {
    init() {
      this.visible = true;
    },
    handleReset() {
      this.visible = false;
    },
    handleSave() {},
    selectCamera() {
      this.$refs.SelectDevice.init(this.dictData, this.warehousData.id);
    },
    getDeviceIdList() {},
  },
  watch: {},
  components: {
    SelectDevice: require('@/views/datagovernance/governancetheme/viewprocess/components/select-device.vue').default,
  },
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.synchro {
  &-header {
    display: flex;
    height: 30px;
    margin: 20px 0;
    &-bar {
      width: 8px;
      margin-right: 6px;
      background: #239df9;
    }
    &-title {
      .align-flex;
      flex: 1;
      padding-left: 10px;
      font-size: 14px;
      font-weight: bold;
      color: #ffffff;
      background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
    }
  }
}
.checkdesc {
  margin-right: 0 !important;
}
.synchro-item {
  width: 100%;
  //   margin-bottom: 16px;
  display: flex;
  align-items: center;
  p {
    font-size: 14px;
    color: #ffffff;
    span {
      display: inline-block;
    }
  }
}
.device-content {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 38px;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
@{_deep} .ivu-modal-body {
  padding: 30px 50px;
}
@{_deep} .ivu-input {
  text-align: center !important;
  &-wrapper {
    margin: 0 10px !important;
  }
}
@{_deep} .ivu-checkbox {
  margin-right: 10px;
  &-group {
    padding: 0 23px;
    &-item {
      display: flex !important;
    }
  }
}
@{_deep} .ivu-radio {
  margin-right: 10px;
  &-group {
    padding: 0 23px;
  }
}
@{_deep} .ivu-checkbox-checked .ivu-checkbox-inner {
  margin-right: 10px;
  background: var(--color-primary);
}
@{_deep} .ivu-form-item {
  margin-bottom: 0 !important;
}
</style>
