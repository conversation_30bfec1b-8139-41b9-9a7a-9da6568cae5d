<template>
  <div class="quarter-box">
    <div class="mask-wrapper" v-show="showSeason" @click.stop="showSeason = false"></div>
    <el-popover
      placement="bottom"
      popper-class="quarter-popover-custom"
      trigger="manual"
      :visible-arrow="false"
      v-model="showSeason"
    >
      <div class="season-picker-box">
        <div class="ivu-date-picker-header">
          <span
            class="ivu-picker-panel-icon-btn ivu-date-picker-prev-btn ivu-date-picker-prev-btn-arrow-double"
            @click="prev"
          >
            <i class="ivu-icon ivu-icon-ios-arrow-back"></i>
          </span>
          <span class="header-label">{{ year }}年</span>
          <span
            class="ivu-picker-panel-icon-btn ivu-date-picker-next-btn ivu-date-picker-next-btn-arrow-double"
            @click="next"
          >
            <i class="ivu-icon ivu-icon-ios-arrow-forward"></i>
          </span>
        </div>

        <div class="panel-content">
          <div
            v-for="item in quarterList"
            :key="item.value"
            class="cell-item"
            :class="{ 'active': current === item.value }"
            @click="selectSeason(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>

      <Input
        slot="reference"
        placeholder="请选择季度"
        v-model="strValue"
        suffix="ios-calendar-outline"
        @on-focus="showSeason = true"
        @on-blur="setText"
      />
    </el-popover>
  </div>
</template>

<script>
// 支持选择季度的日期组件
export default {
  props: {
    quarterValue: {
      type: [String, Number],
      default: '',
    },
    quarterYear: {
      type: [String, Number],
      default: '',
    },
  },

  data() {
    return {
      showSeason: false,
      year: '', //new Date().getFullYear()
      strValue: '',
      current: '', // 1
      quarterList: [
        {
          value: 1,
          label: '第一季度',
        },
        {
          value: 2,
          label: '第二季度',
        },
        {
          value: 3,
          label: '第三季度',
        },
        {
          value: 4,
          label: '第四季度',
        },
      ],
    };
  },

  watch: {
    quarterValue: {
      handler(val) {
        this.current = val;
        this.setText();
      },
      deep: true,
      immediate: true,
    },
    quarterYear: {
      handler(val) {
        this.year = val;
        this.setText();
      },
      deep: true,
      immediate: true,
    },
    showSeason(val) {
      if (!val) {
        this.$emit('quarterChange', { year: this.year, quarter: this.current });
      }
    },
  },

  methods: {
    prev() {
      this.year = this.year * 1 - 1;
      this.setText();
    },
    next() {
      this.year = this.year * 1 + 1;
      this.setText();
    },
    selectSeason(i) {
      this.showSeason = false;
      this.current = i;
      this.setText();
    },
    setText() {
      const cnNum = ['一', '二', '三', '四'];
      this.strValue = `${this.year}年第${cnNum[this.current - 1]}季度`;
    },
  },
};
</script>

<style lang="less">
.quarter-popover-custom {
  padding: 0;
  width: 230px !important;
}
</style>

<style lang="less" scoped>
.quarter-box {
  display: flex;
  .mask-wrapper {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0);
    z-index: 999;
  }
  @{_deep}.ivu-input-suffix i {
    color: var(--color-select-arrow);
  }
}
.quarter-popover-custom {
  .ivu-date-picker-header {
    border-bottom: 1px solid var(--border-color);
  }
  .header-label {
    color: var(--color-date-picker-header-label);
  }
  .ivu-picker-panel-icon-btn:hover {
    color: var(--color-primary);
  }
  .panel-content {
    padding: 12px;
    display: flex;
    flex-wrap: wrap;

    .cell-item {
      width: calc(100% / 2);
      padding: 8px 10px;
      text-align: center;
      color: var(--color-date-picker-header-label);
      &.active,
      &:hover {
        cursor: pointer;
        color: var(--color-primary);
        font-weight: bold;
      }
    }
  }
}
</style>
