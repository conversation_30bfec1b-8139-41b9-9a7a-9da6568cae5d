<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt='' /><span class="title-text">数据接入趋势</span>
    </div>
    <div class="echart-wrap">
      <line-echart v-if="dataAccessObject.names.length" :smooth="true" :echart-style="echartStyle" :names="dataAccessObject.names" :values="dataAccessObject.values"/>
    </div>
  </ui-card>
</template>
<script>
  import LineEchart from './echarts/line-echart.vue'
  export default {
    components: {
      LineEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        echartStyle: {
          lineColor: '#20D7FF',
          lineWidht: 2,
          startColor: 'rgba(32, 215, 255, 0.42)',
          endColor: 'rgba(22, 139, 179, 0)',
          axisLineColor: '#07355E',
          yAxisLineType: 'dashed',
          yAxisLineColor: '#07355E',
          xAxisAxisTick: false,
          showSymbol: false
        },
        dataAccessObject: {
          names: [],
          values: []
        }
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.dataAccessObject = this.updataDataAccess(this.statisticsList)
        },
        immediate: true
      }
    },
    methods: {
      /**
       * 数据接入趋势
      */
      updataDataAccess (list) {
        return {
          names: list.map(item => {
            return item.month + '月'
          }),
          values: list.map(item => {
            return item.count
          })
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    display: flex;
    flex: 1;
  }
</style>