<template>
  <ui-modal v-model="visible" :styles="styles" title="比对详情" footerHide>
    <div class="compare-container auto-fill">
      <div class="header">
        <ui-label class="inline mr-md mb-sm" label="达标情况">
          <Select
            v-model="formData.qualified"
            class="width-sm"
            placeholder="请选择达标情况"
            clearable
            @on-change="initList(false)"
          >
            <Option value="1">达标</Option>
            <Option value="2">不达标</Option>
          </Select>
        </ui-label>
        <div>
          <Button type="primary" class="mb-sm mr-sm" @click="handleUpdateResult">
            <i class="icon-font icon-shoudongshuaxin"></i>
            <span class="inline vt-middle ml-xs">更新比对结果</span>
          </Button>
          <Button type="primary" class="mb-sm" :loading="exportDataLoading" @click="exportExcel">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </div>
      </div>
      <ui-table class="ui-table auto-fill" :loading="loading" :table-columns="tableColumns" :table-data="tableData">
        <template #result="{ row }">
          <span :class="row.same === SAME ? 'c-green' : 'font-warning'">
            {{ row.result }}
          </span>
        </template>
        <template #actualNum="{ row }">
          <span :class="row.same === SAME ? 'c-green' : 'font-warning'">
            {{ row.actualNum }}
          </span>
        </template>
        <template #selfActualNum="{ row }">
          <span :class="row.same === SAME ? 'c-green' : 'font-warning'">
            {{ row.selfActualNum }}
          </span>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            class="operatbtn mr-sm"
            :icon="row.oppose === OPPOSE ? 'icon-quxiaofuhe' : 'icon-jieguofuhe'"
            :content="row.oppose === OPPOSE ? '取消复核' : '结果复核'"
            @handleClick="handleReview(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
    </div>
  </ui-modal>
</template>
<script>
import { OPPOSE, SAME, SERVER, UNOPPOSE, UNSAME } from '../util/enum';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
import downLoadTips from '@/mixins/download-tips';

export default {
  name: 'quantity-result-compare',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  mixins: [downLoadTips],
  props: {
    value: {},
    activeItem: {},
  },
  data() {
    return {
      visible: false,
      detail: {},
      styles: {
        width: '7rem',
      },
      formData: {
        qualified: '',
      },
      exportDataLoading: false,
      loading: false,
      tableData: [],
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
        {
          title: '区县名称',
          key: 'orgName',
          align: 'left',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '行政区划代码',
          key: 'orgCode',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '达标数量',
          key: 'standardNum',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '满分数量',
          key: 'fullDeviceNum',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '上级检测数量',
          slot: 'actualNum',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '本级已上报数量',
          slot: 'selfActualNum',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          title: '比对结果',
          slot: 'result',
          align: 'left',
          tooltip: true,
          minWidth: 100,
        },
        {
          minWidth: 60,
          title: '操作',
          slot: 'action',
          align: 'center',
        },
      ],
    };
  },
  watch: {
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        await this.initList(false);
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  created() {
    this.visible = true;
  },
  computed: {
    ...mapGetters({
      configData: 'governanceevaluation/configData',
    }),
    OPPOSE() {
      return OPPOSE;
    },
    UNOPPOSE() {
      return UNOPPOSE;
    },
    SAME() {
      return SAME;
    },
    UNSAME() {
      return UNSAME;
    },
  },
  methods: {
    async handleReview(row) {
      let { id } = this.activeItem;
      let params = {
        cascadeIssueId: id,
        oppose: row.oppose === OPPOSE ? UNOPPOSE : OPPOSE,
        ids: [row.id],
      };
      try {
        await this.$UiConfirm({
          content: row.oppose === OPPOSE ? '取消复核' : '上级检测结果和本级检测结果不一致，提出异议',
          title: '警告',
        });
        await this.resultReview(params);
        await this.initList(false);
      } catch (e) {
        // console.log(e);
      }
    },
    async handleUpdateResult() {
      await this.initList(true);
    },
    getParams(refresh) {
      let { deploySuperiorOrgCode } = this.configData;
      let { id, indexId, batchId, orgCode } = this.activeItem;
      let commonParams = {
        indexId,
        batchId,
        access: 'TASK_RESULT',
        displayType: 'ORG',
        orgRegionCode: orgCode,
        ...this.formData,
      };
      return {
        id,
        superiorStatInfoParentList: {
          requestMethod: 'post',
          uri: '/evaluation/app/taskIndexResultDetail/getStatInfoList',
          id,
          server: SERVER.evaluationApp,
          postForm: {
            ...commonParams,
            orgRegionCode: deploySuperiorOrgCode,
          },
        },
        superiorStatInfoChildList: {
          requestMethod: 'post',
          uri: '/evaluation/app/taskIndexResultDetail/getStatInfoList',
          id,
          server: SERVER.evaluationApp,
          postForm: commonParams,
        },
        refresh,
      };
    },
    async initList(refresh) {
      try {
        this.loading = true;
        let { superiorToken, deployOrgCode } = this.configData;
        let params = {
          ...this.getParams(refresh),
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.resultComparison, params, {
          headers: {
            token: superiorToken,
            orgCode: deployOrgCode,
          },
        });

        this.tableData = data.videoQuantityStandardDetailList || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async resultReview(val) {
      try {
        let params = {
          cascadeIssueId: null,
          oppose: null,
          ids: [],
          ...val,
        };
        let { data } = await this.$http.post(governanceevaluation.resultReview, params);
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      }
    },
    async exportExcel() {
      try {
        this.exportDataLoading = true;
        this.$_openDownloadTip();
        let res = await this.$http.post(governanceevaluation.resultComparisonExport, this.getParams(false));
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportDataLoading = false;
      }
    },
  },
};
</script>
<style scoped lang="less">
@{_deep} .ivu-modal-body {
  height: 600px;
}
.compare-container {
  position: relative;
  height: 100%;

  .header {
    display: flex;
    justify-content: space-between;
  }

  .operatbtn {
    color: var(--color-primary);
  }
}
</style>
