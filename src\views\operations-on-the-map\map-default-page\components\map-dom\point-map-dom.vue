<template>
  <div class="dom-wrapper">
    <div class="dom">
      <header>
        <span>郑州大学第一附属医院</span>
        <ui-icon type="close" :size="14" @click.native="() => $emit('close')"></ui-icon>
      </header>
      <section class="dom-content">
        <div class="point-info">
          <div class="dom-content-p">
            <span class="label">地点：</span>
            <span class="message">江苏省徐州市贾汪区张衡大道283号</span>
            <ui-icon type="shoucang" :size="14" :color="'#F29F4C'"></ui-icon>
          </div>
        </div>
      </section>
      <footer>
        <search-around></search-around>
      </footer>
    </div>
  </div>
</template>
<script>
import searchAround from '../search-around.vue'
export default {
    components: {
        searchAround
    },
    props: {},
    data() {
        return {
        currentTabIndex: 0
        }
    },
    methods: {
        init() {
            return new Promise((resolve) => {

                resolve({ data: true })
            })
        },
        tabClick(index) {
            this.currentTabIndex = index
        }
    }
}
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
//   padding: 10px 28px 25px 0;
padding: 10px 28px 0px 0;
  height: 100%;
}

.dom {
  width: 490px;
  height: 148px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;
  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
    display: flex;
    justify-content: space-between;
  }
  .dom-content {
    padding: 15px 20px;
    font-size: 14px;
    .point-info {
      display: flex;
      flex-direction: column;
    }
    .dom-content-p {
      margin-top: 6px;
      width: 540px;
      display: flex;
      .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 50px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
      }
      .message {
        font-size: 12px;
        font-weight: bold;
        width: 520px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
  > footer {
    border-top: 1px solid #d3d7de;
    padding: 0 20px;
    height: 55px;
    line-height: 55px;
  }
}
.dom:before,
.dom:after {
  content: '';
  display: block;
  border-width: 8px;
  position: absolute;
  bottom: -16px;
  left: 245px;
  border-style: solid dashed dashed;
  border-color: #ffffff transparent transparent;
  font-size: 0;
  line-height: 0;
}
</style>
