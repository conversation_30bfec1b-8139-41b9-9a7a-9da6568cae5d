<template>
  <ui-modal title="查看治理结果" v-model="visible" :styles="styles" :footer-hide="true">
    <!-- <slide-unit-tree v-if="visible"
                     @selectOrgCode="selectOrgCode"
                     :current-node-key="getDefaultSelectedOrg.orgCode"
                     :isDispear="true">
    </slide-unit-tree> -->
    <div class="echarts-wrapper over-flow">
      <template v-if="echartList.length">
        <draw-echarts
          v-for="(item, index) of echartList"
          :key="index"
          :echart-option="item.echartRingOption"
          :echart-style="ringStyle"
          :echarts-loading="echartsLoading"
          :ref="'zdryChart' + index"
          class="charts fl"
        >
        </draw-echarts>
      </template>
    </div>
    <search-title
      ref="searchRef"
      :active-row="activeRow"
      :data-status-list="dataStatusList"
      :exportLoading="exportLoading"
      @startSearch="startSearch"
      @onExport="onExport"
    ></search-title>
    <div class="box-wrapper" v-if="visible">
      <ui-table
        class="ui-table mt-sm"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #refreshClockSlot="{ row }">
          <ui-btn-tip
            v-if="activeRow.governanceContent === '1'"
            icon="icon-zhongsheshizhong"
            content="重设时钟"
            @click.native="refreshClock(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="row.taskStatus === '1' && activeRow.governanceContent === '3'"
            icon="icon-bofangshipin"
            content="播放视频"
            @click.native="clickRow(row)"
          ></ui-btn-tip>
        </template>
        <template #taskStatusSlot="{ row }" v-if="['1', '3', '4', '5', '6', '7'].includes(activeRow.governanceContent)">
          <Tag :color="row.taskStatus === '1' ? 'success' : 'error'" v-if="row.taskStatus" class="f-14">{{
            handleTaskStatus(row) || '未知'
          }}</Tag>
          <span class="f-14" v-else>未知</span>
        </template>
        <template #toViewDevice="{ row }" v-if="['2', '6'].includes(activeRow.governanceContent)">
          <ui-btn-tip icon="icon-shebeidangan" content="设备档案" @click.native="deviceArchives(row)"></ui-btn-tip>
        </template>
        <template #isOnlineText="{ row }" v-if="activeRow.governanceContent === '2'">
          <Tag :color="row.isOnline === '1' ? 'success' : 'error'" v-if="row.isOnline">{{
            row.isOnlineText || '未知'
          }}</Tag>
          <span v-else>未知</span>
        </template>
        <template #toSetRecord="{ row }" v-if="['4', '5', '7'].includes(activeRow.governanceContent)">
          <ui-btn-tip
            class="mr-lg"
            icon="icon-shebeidangan"
            content="设备档案"
            @click.native="deviceArchives(row)"
          ></ui-btn-tip>
          <ui-btn-tip icon="icon-zhongsheshizhong" content="重设记录" @click.native="resetRecord(row)"></ui-btn-tip>
        </template>
      </ui-table>
    </div>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div>
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
    <reset-result v-model="resetResultShow" :active-record="activeRecord"></reset-result>
  </ui-modal>
</template>
<script>
import { mapGetters } from 'vuex';
import {
  clockTableColumn,
  deviceTableColumn,
  ringColorEnum,
  osdTableColumn,
  pointTableColumn,
  funcTableColumn,
  macTableColumn,
  deviceStatusColumn,
} from './enum';
import vedio from '@/config/api/vedio-threm';
import governanceautomatic from '@/config/api/governanceautomatic';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {},
    activeRow: {
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      echartsLoading: false,
      styles: {
        // top: "0.5rem",
        width: '97%',
      },
      resetResultShow: false,
      activeRecord: {},
      tableColumns: [],
      minusTable: 530,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      ringStyle: {
        height: '190px',
        width: '300px',
      },
      tableData: [],
      echartList: [],
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      dataStatusList: [
        { dataKey: '1', dataValue: '已同步' },
        { dataKey: '2', dataValue: '未同步' },
      ],
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
      videoVisible: false,
      playDeviceCode: '',
      exportLoading: false,
      // 治理结果类型(cssbsz:重设设备时钟, sbxxzdhq:设备信息自动获取), gnlxzl:功能类型自治, dwlxzl:点位类型自治, macdzzl:MAC地址自治, osdzmzdcs:OSD字幕自动重设
      governanceResultType: {
        '1': 'cssbsz',
        '2': 'sbxxzdhq',
        '3': 'osdzmzdcs',
        '4': 'gnlxzl',
        '5': 'dwlxzl',
        '6': 'macdzzl',
        '7': 'phystatuszl',
      },
    };
  },
  computed: {
    ...mapGetters({
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  mounted() {},
  methods: {
    async clickRow(row) {
      try {
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      // this.$refs.livePlayer.cancelplay()
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
    resetRecord(row) {
      this.activeRecord = row;
      this.resetResultShow = true;
    },
    async governanceResultPageList() {
      try {
        this.loading = true;
        let params = Object.assign({}, this.searchData);
        params.taskId = this.activeRow.id;
        //params.taskId = 25
        let { data } = await this.$http.post(governanceautomatic.governanceResultPageList, params);
        this.activeRow.governanceContent === '2'
          ? (this.tableData = this.handleSlot(data.data.entities))
          : (this.tableData = data.data.entities);
        this.pageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleTaskStatus(row) {
      let one = this.dataStatusList.find((item) => item.dataKey === row.taskStatus);
      return one.dataValue;
    },
    handleSlot(data) {
      data.forEach((item) => {
        if (item.updatePropertyJson) {
          this.$set(item, 'cellClassName', {});
          let isRed = JSON.parse(item.updatePropertyJson);
          isRed.forEach((one) => {
            if (['civilCode', 'sbgnlx', 'isOnline'].includes(one)) {
              const codeText = {
                civilCode: 'civilName',
                sbgnlx: 'sbgnlxText',
                isOnline: 'isOnlineText',
              };
              this.$set(item.cellClassName, codeText[one], 'font-warning');
            } else {
              this.$set(item.cellClassName, one, 'font-warning');
            }
          });
        }
      });
      return data;
    },
    startSearch(data) {
      this.pageData.pageNum = 1;
      this.searchData.pageNumber = 1;
      Object.assign(this.searchData, data);
      this.governanceResultPageList();
    },
    selectOrgCode(data) {
      this.searchData.orgCode = data.orgCode;
      this.governanceResultPageList();
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    handleEcharts(data) {
      data.forEach((item, index) => {
        let one = {
          data: [
            {
              name: item.name,
              value: item.value,
              color: Object.values(ringColorEnum)[index],
            },
          ],
          text: item.name,
          subtext: `${item.value}`,
        };
        this.echartList.push({
          echartRingOption: this.$util.doEcharts.taskTrackingRing(one),
        });
      });
    },
    async refreshClock(row) {
      try {
        let res = await this.$http.get(equipmentassets.resetOsdTime, {
          params: { device_id: row.deviceId },
        });
        this.$Message.success(res.data.data);
      } catch (err) {
        console.log(err);
      }
    },
    changePage(val) {
      // this.pageData.pageSize = 1
      this.searchData.pageNumber = val;
      this.governanceResultPageList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.governanceResultPageList();
    },
    handleClockData(tableColumn) {
      this.tableColumns = tableColumn;
      let clockEchartsData = [
        {
          name: '设备总量',
          value: this.activeRow.taskNum || 0,
        },
        {
          name: '已重设数量',
          value: this.activeRow.updateNum || 0,
        },
        {
          name: '无法重设数量',
          value: this.activeRow.failHandleNum || 0,
        },
      ];
      this.dataStatusList = [
        { dataKey: '1', dataValue: '已重设' },
        { dataKey: '2', dataValue: '未重设' },
      ];
      this.handleEcharts(clockEchartsData);
    },
    handleDeviceData() {
      this.tableColumns = deviceTableColumn;
      let deviceEchartsData = [
        {
          name: '设备总量',
          value: this.activeRow.taskNum || 0,
        },
        {
          name: '更新数据数量',
          value: this.activeRow.updateNum || 0,
        },
      ];
      this.dataStatusList = [
        { dataKey: '1', dataValue: '已同步' },
        { dataKey: '2', dataValue: '未同步' },
      ];
      this.handleEcharts(deviceEchartsData);
    },
    handleFunc(funcTableColumn) {
      this.tableColumns = funcTableColumn;
      let clockEchartsData = [
        {
          name: '设备总量',
          value: this.activeRow.taskNum || 0,
        },
        {
          name: '已重设设备数量',
          value: this.activeRow.updateNum || 0,
        },
      ];
      this.dataStatusList = [
        { dataKey: '1', dataValue: '已重设' },
        { dataKey: '2', dataValue: '未重设' },
      ];
      this.handleEcharts(clockEchartsData);
    },
    handleMacFunc() {
      this.tableColumns = macTableColumn;
      let clockEchartsData = [
        {
          name: '设备总量',
          value: this.activeRow.taskNum || 0,
        },
        {
          name: '已转换设备数量',
          value: this.activeRow.updateNum || 0,
        },
      ];
      this.dataStatusList = [
        { dataKey: '1', dataValue: '已转换' },
        { dataKey: '2', dataValue: '未转换' },
      ];
      this.handleEcharts(clockEchartsData);
    },
    handleDeviceStatusFunc() {
      this.tableColumns = deviceStatusColumn;
      let clockEchartsData = [
        {
          name: '设备总量',
          value: this.activeRow.taskNum || 0,
        },
        {
          name: '已重设设备数量',
          value: this.activeRow.updateNum || 0,
        },
      ];
      this.dataStatusList = [
        { dataKey: '1', dataValue: '已重设' },
        { dataKey: '2', dataValue: '未重设' },
      ];
      this.handleEcharts(clockEchartsData);
    },
    clearAll() {
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.tableData = [];
      this.echartList = [];
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.$refs.searchRef.clearForm();
    },
    // 导出
    async onExport() {
      try {
        this.exportLoading = true;
        let { governanceContent, id } = this.activeRow;
        let data = {
          ...this.searchData,
          governanceResultType: this.governanceResultType[governanceContent],
          taskId: id,
        };
        let res = await this.$http.post(governanceautomatic.governanceResultPageListExport, data, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (e) {
        console.log(e);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.clearAll();
        this.governanceResultPageList();
        let resultType = {
          // 重设设备时钟
          1: () => this.handleClockData(clockTableColumn),
          // 设备信息自动获取
          2: () => this.handleDeviceData(),
          //osd字幕
          3: () => this.handleClockData(osdTableColumn),
          4: () => this.handleFunc(funcTableColumn),
          5: () => this.handleFunc(pointTableColumn),
          6: () => this.handleMacFunc(),
          7: () => this.handleDeviceStatusFunc(),
        };
        resultType[this.activeRow.governanceContent]();
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    searchTitle: require('./search-title.vue').default,
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    resetResult: require('../reset-result.vue').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  //display: flex;
  background: var(--bg-sub-content);
  margin: 10px 0;
  width: 100%;
  .charts {
    //flex: 1;
  }
}
.statics {
  color: #fff;
}
.box-wrapper {
  position: relative;
}
@{_deep}.ui-table .no-data {
  top: 60%;
}
@{_deep}.ivu-table-row {
  .font-warning {
    color: var(--color-failed);
  }
}
@{_deep}.ivu-tag-error {
  background: var(--color-failed);
}
@{_deep}.ivu-tag-success {
  background: var(--color-success);
}
.ui-page {
  padding: 20px 0;
}
</style>
