<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template slot="checkStatus" slot-scope="{ row }">
        <Tag v-if="row.checkStatus in qualifiedColorConfig" :color="qualifiedColorConfig[row.checkStatus].color">
          {{ qualifiedColorConfig[row.checkStatus].dataValue }}
        </Tag>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template slot="action" slot-scope="{ row }">
        <ui-btn-tip icon="icon-chakanjietu" content="查看抓拍图片" class="mr-md" @click.native="checkReason(row)">
        </ui-btn-tip>
        <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
      </template>
    </Particular>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <face-pictute
      v-model="capturePictureVisible"
      v-if="capturePictureVisible"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handleImgPageChange"
      @handlePageSizeChange="handleImgPageSizeChange"
      title="人脸抓拍图片"
    ></face-pictute>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { iconStaticsList } from './util/enum/ReviewParticular.js';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [particularMixin, dealWatch],
  inheritAttrs: false,
  props: {
    activeIndexItem: {},
  },
  data() {
    return {
      customSearch: false,
      defaultCheckedList: [],
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      iconList: [
        ...iconStaticsList,
        {
          name: this.activeIndexItem.indexName,
          count: '0',
          countStyle: {
            color: 'var(--color-bluish-green-text)',
          },
          style: {
            color: 'var(--color-bluish-green-text)',
          },
          iconName: 'icon-shipinjiankongjianshezongliang',
          fileName: 'resultValue',
          type: 'percent', // 百分比
        },
      ],
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        checkStatus: null,
      },
      formItemData: [
        {
          type: 'input',
          key: 'deviceId',
          label: '设备编码',
        },
        {
          type: 'input',
          key: 'deviceName',
          label: '设备名称',
        },
        {
          type: 'select',
          key: 'checkStatus',
          label: '检测结果',
          placeholder: '请选择检测结果',
          options: Object.keys(qualifiedColorConfig).map((key) => {
            return { value: key, label: qualifiedColorConfig[key].dataValue };
          }),
        },
        {
          type: 'select',
          key: 'errorCodes',
          label: '异常原因',
          selectMutiple: true,
          placeholder: '请选择异常原因',
          options: [],
        },
      ],
      // 人脸和车辆一样，对过
      tableColumns: Object.freeze([
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          width: 120,
          ellipsis: true,
          tooltip: true,
        },
        { title: '抓拍总数量', key: 'captureCount', width: 100 },
        { title: '近天抓拍数量', key: 'captureRecentlyCount', width: 120 },
        { title: '昨日抓拍数量', key: 'captureTodayCount', width: 120 },
        { title: '持续无抓拍天数', key: 'noCaptureDays', width: 120 },
        {
          title: '历史同天平均抓拍量',
          key: 'historyAverageCount',
          width: 180,
          renderHeader: (h) => {
            return (
              <Tooltip max-width="400" transfer>
                <span class="vt-middle">历史同天平均抓拍量</span>
                <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
                <template slot="content">
                  <p class="mb-md f-14">抓拍数量突降计算逻辑：</p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    今日抓拍量C1： 2021/10/19日抓拍量；
                  </p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    历史同天抓拍量C2： 平台上线至2021年10月 19 日号前所有星期二（10月19日是星期二）抓拍量的平均抓拍量；
                  </p>
                  <p class="f-12">
                    <div class="white-circle mr-xs"></div>
                    若（C2-C1）/C2 >= 50%（ 配置值），则判定抓拍数据量突降。
                  </p>
                </template>
              </Tooltip>
            );
          },
        },
        { title: '昨日变化', key: 'changeRatio', width: 80 },
        {
          title: '检测结果',
          key: 'checkStatus',
          width: 90,
          slot: 'checkStatus',
        },
        {
          title: '异常原因',
          key: 'message',
          width: 150,
          ellipsis: true,
          tooltip: true,
        },
        { title: '检测时间', key: 'examineTime', width: 150 },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 100,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ]),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      capturePictureVisible: false,
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      currentRow: {},
      reasonLoading: false,
      exportLoading: false,
    };
  },
  created() {
    // 异常原因下拉列表获取
    this.MixinDisQualificationList().then((data) => {
      let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
      findErrorCodes.options = data.map((item) => {
        // 嘉鹏说: 设备模式查询需转换数字模式
        return { value: Number(item.key), label: item.value };
      });
    });
    this.getTagList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    initAll() {
      // 列表
      this.getTableData();
      this.getTableDataTotal();
      // 统计获取
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      Object.assign(this.formData, params);
      this.getTableData();
      this.getTableDataTotal();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        // this.totalCount = data.total
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    checkReason(row) {
      this.currentRow = row;
      this.capturePictureVisible = true;
      this.getReason(row);
    },
    handleImgPageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handleImgPageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    async getReason() {
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
          customParameters: {
            deviceId: this.currentRow.deviceId,
            deviceType: this.currentRow.deviceType,
          },
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        params.displayType === 'REGION'
          ? (params.orgRegionCode = this.$route.query.regionCode)
          : (params.orgRegionCode = this.$route.query.orgCode);
        this.reasonLoading = true;
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
        this.reasonPage.totalCount = datas.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
        this.getTableDataTotal();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    facePictute: require('@/views/governanceevaluation/evaluationoResult/components/face-pictute.vue').default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
</style>
