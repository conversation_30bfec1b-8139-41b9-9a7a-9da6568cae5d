<template>
  <ui-modal v-model="captureVisible" title="抓拍详情" :footerHide="true" width="89%">
    <Row>
      <Col span="4" class="contentLeft">
        <div v-if="personObj">
          <div class="capture-details-left-sculpture">
            <ui-image :src="personObj.identityPhoto" />
          </div>
          <div>
            <span class="sculpture-item-label">姓名：</span>
            <span>{{ personObj.name || '未知' }}</span>
          </div>
          <div>
            <span class="sculpture-item-label">身份证号：</span>
            <span>{{ personObj.idCard || '未知' }}</span>
          </div>
          <tags-more
            v-if="personObj.personType"
            :tagList="personObj.personType"
            :defaultTags="4"
            placement="left-start"
            bgColor="#2D435F"
          ></tags-more>
        </div>
      </Col>
      <Col span="20" style="overflow: hidden">
        <div class="content">
          <div class="capture-details-right-top" v-if="staticsList.length">
            <label v-for="(item, index) in staticsList" :key="index"
              >{{ item.label }}：<span>{{ item.count }}</span></label
            >
          </div>
          <slot name="searchList"></slot>
          <div class="list" v-ui-loading="{ loading: loading, tableData: tableData }">
            <div :style="styleScroll6">
              <div class="carItem" v-for="(item, index) in tableData" :key="item.id">
                <div class="item">
                  <!-- <div class="num" v-if="item.similarity">{{item.similarity}}%</div> -->

                  <div class="img" @click="viewBigPic(index, item.trackImage)">
                    <ui-image :src="item.trackImage" />
                    <div class="num" v-if="item.similarity">
                      {{ similarityVal(item.similarity) }}
                    </div>
                  </div>
                  <div class="group-message">
                    <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据'}`">
                      <i class="icon-font icon-shijian f-14"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis f-14">{{ item.shotTime }}</span>
                    </p>
                    <p :title="item.catchPlace">
                      <i class="icon-font icon-dizhi f-14"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis f-14">{{
                        item.catchPlace ? item.catchPlace : '暂无数据'
                      }}</span>
                    </p>
                  </div>
                  <i class="icon-font icon-URLbukefangwen1" v-if="!item.trackImage || !item.trackLargeImage"></i>
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <ui-page
              class="page menu-content-background"
              :page-data="pageData"
              @changePage="changePage"
              @changePageSize="changePageSize"
            ></ui-page>
          </div>
        </div>
        <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
      </Col>
    </Row>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import api from '@/config/api/inspectionrecord';
export default {
  name: 'captureDetail',
  props: {
    resultId: {},
    // 筛选条件
    defaultSearchData: {
      type: Object,
      default() {
        return {
          trackType: 3,
        };
      },
    },
    // 接口名称
    interFaceName: {
      default: api.pageTrackCatchDetails,
    },
    staticsList: {
      type: Array,
      default() {
        return [
          {
            label: '抓拍总量',
            count: 0,
            filedName: 'catchAmount',
          },
          {
            label: 'URL不可访问轨迹数量',
            count: 0,
            filedName: 'urlNotAvailableAmount',
          },
          {
            label: '大图URL不可访问轨迹数量',
            count: 0,
            filedName: 'largeUrlNotAvailableAmount',
          },
          {
            label: '小图URL不可访问轨迹数量',
            count: 0,
            filedName: 'smallUrlNotAvailableAmount',
          },
        ];
      },
    },
  },
  data() {
    return {
      captureVisible: false,
      tableData: [],
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styleScroll6: {
        position: 'relative',
        width: '100%',
        height: '660px',
        'overflow-y': 'scroll',
      },
      bigPictureShow: false,
      imgList: [],
      personObj: {}, // 人员信息对象
      loading: false,
    };
  },
  async created() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  methods: {
    show(item) {
      this.personObj = item;
      this.infoList();
      this.staticsList.forEach((one) => {
        if (this.personObj[one.filedName]) {
          one.count = this.personObj[one.filedName];
        }
        if (one.label === 'urlNotAvailableAmount') {
          one.count = this.personObj.smallUrlNotAvailableAmount + this.personObj.largeUrlNotAvailableAmount;
        }
      });
      this.captureVisible = true;
    },
    async infoList() {
      this.loading = true;
      let params = Object.assign(this.searchData, this.defaultSearchData);
      params.idCard = this.personObj.idCard;
      params.resultId = this.resultId;
      let res = await this.$http.post(this.interFaceName, Object.assign(this.searchData, this.defaultSearchData));
      this.tableData = res.data.data.entities;
      this.pageData.totalCount = res.data.data.total;
      this.loading = false;
    },
    viewBigPic(index, item) {
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 分页
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
    captureDetail(item) {
      this.$refs.uploadModal.show(item);
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
    },
    // 检索，父组件调用
    startSearch(searchData) {
      this.searchData = Object.assign(this.searchData, searchData);
      this.infoList();
    },
    similarityVal(val) {
      return (val * 100).toFixed(2) + '%';
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      !val ? this.reset() : null;
      this.captureVisible = val;
    },
  },
  components: {
    // trajectoryModal: require('../component/trajectory-modal').default,
    // uploadModal: require('../component/upload-modal').default,
    uiImage: require('@/components/ui-image').default,
    TagsMore: require('@/components/tags-more').default,
  },
};
</script>

<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 0 !important;
}
@{_deep}.ivu-modal-header {
  padding: 0 !important;
}
.contentLeft {
  color: #fff;
  padding: 0 20px;
  border-right: 1px solid var(--border-color);
  div {
    &:nth-child(2) {
      margin-top: 10px;
    }
    &:nth-child(3) {
      margin-top: 10px;
    }
  }

  img {
    width: 100%;
  }

  ul {
    li {
      float: left;
      padding: 6px 10px;
      background: #1a447b;
      border-radius: 4px;
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .sculpture-item-label {
    color: #8797ac;
  }
}
.content {
  color: #fff;
}

/deep/ .ivu-modal-footer {
  height: 0 !important;
  padding: 0 !important;
}

.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  width: 188px;
  display: inline-block;
  // max-width: 188px;
  .item {
    position: relative;
    height: 100%;
    background: #0f2f59;

    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
      .num {
        position: absolute;
        left: 0;
        top: 15px;
        z-index: 10;
        padding: 0px 6px;
        // border-radius: 5px;
        background: #ea800f;
        // background: rgba(42, 95, 175, 0.6);
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 12px;
      color: #8797ac;
    }
    .icon-URLbukefangwen1 {
      color: #bc3c19;
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }
  }
}
/deep/ .ivu-row {
  align-content: start;
}
.capture-details-left-sculpture {
  margin-bottom: 20px;
  margin-top: 40px;
  width: 243px;
  height: 243px;
  border: 1px solid rgba(21, 64, 120, 1);
}
.capture-details-right-top {
  margin-top: 30px;
  padding: 20px 20px 20px;
  font-size: 14px;
  color: #ffffff;
  border-bottom: 1px solid var(--border-color);
  label {
    margin-right: 58px;
    span {
      color: var(--color-bluish-green-text);
    }
  }
}
.list {
  padding-left: 20px;
}
</style>
