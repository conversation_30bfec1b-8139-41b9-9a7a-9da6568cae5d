const basePre = '/ivdg-work-order-service';
export default {
  causeGetType: basePre + '/feedback/cause/getType', //GET,获取数据类型
  getCause: basePre + '/feedback/cause/getCause', //get,?type=''获取异常原因
  getForm: basePre + '/feedback/cause/getForm', //POST,+{causeCodes:[]}获取表单填写
  feedbackList: basePre + '/feedback/pageList', //POST问题反馈列表 +{code,status,dataType,causeCodes[],name,startTime,endTime}
  delFeedback: basePre + '/feedback/deleteFeedback', //GET,?id=''删除反馈
  handleFeedback: basePre + '/feedback/handleFeedback', //POST, 问题反馈处理
  saveFeedbackTask: basePre + '/workOrder/saveFeedbackTask', //POST, 保存feedBack
  feedbackGetList: basePre + '/feedback/getList', //POST {"ids":[]} 问题反馈查看详情
  feedbackHandleEffective: basePre + '/feedback/handleEffective', //POST 问题反馈人工确认
  feedbackHandleEffectiveCount: basePre + '/feedback/handleEffectiveCount', //POST 问题反馈人工确认条数
};
