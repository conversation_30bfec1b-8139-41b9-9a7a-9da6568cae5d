<template>
  <ui-modal v-model="visible" :title="title" :styles="styles">
    <div class="export-data">
      <RadioGroup v-model="searchData.exportType" class="checkbox-box" @on-change="handleExportWay">
        <Radio label="currentTable" class="mr-lg">导出当前列表</Radio>
        <Radio label="orgOrRegion">按照{{ orgRegionName }}导出综合数据</Radio>
      </RadioGroup>
      <div class="mt-lg" v-if="searchData.exportType === 'orgOrRegion'">
        <p class="mb-xs base-text-color">请选择要导出的{{ orgRegionName }}:</p>
        <select-organization-tree-checkall
          ref="SelectOrganizationTreeCheckall"
          :treeData="treeData"
          :node-key="orgRegionCode"
          :default-props="defaultProps"
          :default-checked-keys="defaultCheckedKeys"
          :placeholder="`请选择${orgRegionName}`"
          :is-select-grandchild="false"
          :is-expand-all="false"
          @getSelectTree="checkTree"
        >
        </select-organization-tree-checkall>
      </div>
    </div>
    <template #footer>
      <Button type="primary" @click="exportAdd" :loading="exportLoading" class="plr-30">确 定</Button>
      <Button @click="visible = false" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'export-data',
  props: {
    exportLoading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '导出',
    },
    // 组织机构-orgCode || 行政区划-regionCode
    orgRegionCode: {
      type: String,
      default: 'regionCode',
    },
  },
  data() {
    return {
      visible: false,
      styles: { width: '3rem' },
      searchData: {
        // multiSheet: 'false',
        codes: [],
        exportType: 'currentTable',
      },
      treeData: [],
      defaultCheckedKeys: [],
      treeList: [],
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
    };
  },
  computed: {
    orgRegionName() {
      return this.orgRegionCode === 'regionCode' ? '行政区划' : '组织机构';
    },
  },
  methods: {
    init(val) {
      this.visible = true;
      this.defaultProps.label = this.orgRegionCode === 'orgCode' ? 'orgName' : 'regionName';
      this.searchData.exportType = 'currentTable';
      this.defaultCheckedKeys = [];
      this.getOrg(val);
    },
    async getOrg(val) {
      try {
        let res = await this.$http.get(evaluationoverview.getCurrentUserSysOrgListByTaskId, {
          params: { batchId: val },
        });
        let tempArr = this.$util.common.deepCopy(res.data.data);
        tempArr = tempArr.map((item) => {
          return item;
        });
        this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
      } catch (error) {
        console.log(error);
      }
    },
    checkTree(codes) {
      this.searchData.codes = codes;
    },
    handleExportWay() {
      this.searchData.codes = [];
    },
    exportAdd() {
      if (this.searchData.exportType === 'orgOrRegion' && !this.searchData.codes.length) {
        this.$Message.error(`请选择${this.orgRegionName}`);
        return;
      }
      let exportListkey = this.orgRegionCode === 'orgCode' ? 'orgCodes' : 'regionCodes';
      const exportParams = {
        exportType: this.searchData.exportType,
        apiParams: {},
      };
      if (this.searchData.exportType === 'orgOrRegion') {
        exportParams.apiParams.exportZip = true; // 导出 综合 时需要
      }
      exportParams.apiParams.displayType = this.orgRegionCode === 'orgCode' ? 'ORG' : 'REGION';
      exportParams.apiParams[exportListkey] = this.searchData.codes;
      this.$emit('handleExport', exportParams);
    },
  },
  components: {
    SelectOrganizationTreeCheckall: require('@/api-components/select-organization-tree-checkall.vue').default,
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal {
  width: 520px;
  .ivu-modal-header {
    margin-bottom: 0;
  }
  &-body {
    padding: 70px 103px !important;
  }
  .export-content {
    height: 420px;
    overflow: auto;
    position: relative;
  }
  .checkbox-box {
    display: flex;
    justify-content: space-between;
  }
}
@{_deep} .select-organization-tree {
  .ivu-dropdown .ivu-select-dropdown {
    min-height: 150px;
    max-height: 300px;
    .ivu-checkbox-wrapper {
      line-height: 30px;
    }
  }
}
</style>
