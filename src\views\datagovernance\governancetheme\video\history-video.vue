<template>
  <div>
    <ui-modal ref="modal" title="历史视频流通畅检测">
      <div class="quality">
        重点监控：历史视频流
        <Input type="number" v-model="extraParam.important" placeholder="值" style="width: 100px" size="small" />
        天
      </div>
      <div class="quality">
        普通监控：历史视频流
        <Input type="number" v-model="extraParam.general" placeholder="值" style="width: 100px" size="small" />
        天
      </div>

      <template slot="footer">
        <Button @click="submit" type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>

    <!-- <ui-modal> -->
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  name: 'carDialog',
  props: {},
  data() {
    return {
      extraParam: {
        important: '',
        general: '',
      },
      timeList: [
        { value: 1, label: '时' },
        { value: 2, label: '分' },
        { value: 3, label: '秒' },
      ],
    };
  },
  async created() {},
  computed: {},
  methods: {
    showModal() {
      this.$refs.modal.modalShow = true;
      this.init();
    },

    init() {
      this.$http
        .get(api.queryHistoryVideo + '39')
        .then((res) => {
          if (res.data.code == 200) {
            this.extraParam = JSON.parse(res.data.data.extraParam);
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    submit() {
      var param = {
        topicComponentId: 39,
        extraParam: JSON.stringify(this.extraParam),
      };
      this.$http
        .post(api.addHistoryVideo, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.$refs.modal.modalShow = false;
            this.$Message.success('配置成功');
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.ivu-checkbox-group-item {
  padding: 6px 0;
}

.quality {
  padding: 10px 0;
  color: #fff;
  text-align: center;
}

.explain {
  color: #c76d28;
}
.p {
  margin-top: 10px;
  color: #fff;
}
</style>
