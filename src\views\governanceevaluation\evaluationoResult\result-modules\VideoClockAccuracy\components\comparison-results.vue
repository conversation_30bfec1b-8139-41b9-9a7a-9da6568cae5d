<template>
  <div class="comparison-results auto-fill">
    <div class="auto-fill">
      <div class="statistics mb-sm mt-sm">
        <Button type="primary" class="fr" :loading="exportLoading" @click="onExport">
          <i class="icon-font icon-daochu"></i> 导出
        </Button>
      </div>
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
      >
        <template #detectDate="{ row }">
          <span>{{ row.detectDate }}</span>
        </template>
        <template #offlineQuantity="{ row }">
          <span>{{ row.offlineQuantity }}</span>
          <span
            class="num-change"
            :class="{
              'font-red': row.differenceValueByOfflineQuantity > 0,
              'font-green': row.differenceValueByOfflineQuantity < 0,
            }"
          >
            {{ getDiffNum(row.differenceValueByOfflineQuantity) }}
          </span>
        </template>
        <!-- 每月的第一天 没有比对详情 -->
        <template #action="{ row }">
          <div
            v-if="row.contrastExtDataBoList?.length == 2"
            class="row-details pointer"
            @click="openCompareDetail(row)"
          >
            详情
          </div>
        </template>
      </ui-table>
    </div>
    <ui-table
      class="ui-table-bottom"
      :table-columns="statisticTableColumns"
      :table-data="statisticTableData"
      :showHeader="false"
      :span-method="handleSpan"
      specialJsx=" "
    >
      <template #detectDate>
        <span class="color-statistical-name">最终变化</span>
      </template>
      <template #offlineQuantity="{ row }">
        <span
          :class="{
            'font-red': row.offlineQuantity > 0,
            'font-green': row.offlineQuantity < 0,
          }"
          >{{ row.offlineQuantity }}</span
        >
      </template>
      <template #action="{ row }">
        <div class="row-details pointer" @click="openCompareDetail(row, true)">详情</div>
      </template>
    </ui-table>
    <ComparisonDetails
      v-if="resultVisible"
      v-model="resultVisible"
      :activeRow="activeRow"
      :isStatisticRow="isStatisticRow"
    ></ComparisonDetails>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { tableColumn_comparison } from '../util/enum/ReviewParticular';

export default {
  name: 'comparisonResults',
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  mixins: [dealWatch, downLoadTips],
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    ComparisonDetails: require('./comparison-details.vue').default,
  },
  data() {
    return {
      tableLoading: false,
      tableColumns: tableColumn_comparison,
      tableData: [],
      statisticTableColumns: [],
      statisticTableData: [],
      exportLoading: false,
      // 详情
      resultVisible: false,
      activeRow: {},
      isStatisticRow: false,
      initTime: '',
    };
  },
  computed: {},
  created() {
    this.initTime = this.$util.common.formatDate(new Date(), 'yyyy-MM');
    // 特殊处理下  第二列 居中显示
    let arr = this.$util.common.deepCopy(tableColumn_comparison);
    arr[1].align = 'center';
    this.statisticTableColumns = arr;

    this.startWatch(
      '$route.query',
      () => {
        this.getTableData();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    getDiffNum(num) {
      let text = num > 0 ? `+${num}` : num < 0 ? `${num}` : '';
      return text;
    },
    // 获取列表
    async getTableData() {
      try {
        this.tableLoading = true;
        this.statisticTableData = [];
        this.tableData = [];
        let { batchId, indexId, statisticType, regionCode, orgCode, examineTime } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: statisticType,
          yearAndMonth: examineTime || this.initTime,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          form: 'index', // 倒序，会给拼上“最终变化”的数据
        };
        let res = await this.$http.post(evaluationoverview.getContrastResultData, data);
        let arr = res?.data?.data || [];
        this.tableData = arr.slice(0, arr.length - 1);
        this.statisticTableData = arr.slice(arr.length - 1, arr.length);
      } catch (error) {
        // console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    // 导出
    async onExport() {
      try {
        let { batchId, indexId, statisticType, regionCode, orgCode, examineTime } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: statisticType,
          yearAndMonth: examineTime || this.initTime,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          form: 'index', // 倒序，会给拼上“最终变化”的数据
        };
        this.$_openDownloadTip();
        let res = await this.$http.post(evaluationoverview.exportContrastResultData, data);
        await this.$util.common.transformBlob(res.data.data);
      } catch (error) {
        // console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
    // 统计 合并 第 1-3 列
    handleSpan({ rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 1) {
        return [1, 3];
      } else if (rowIndex === 0 && (columnIndex === 0 || columnIndex === 2)) {
        return [0, 0];
      }
    },
    openCompareDetail(row, flag = false) {
      this.activeRow = row;
      this.resultVisible = true;
      this.isStatisticRow = flag;
    },
  },
};
</script>
<style lang="less" scoped>
.comparison-results {
  padding: 0 10px;
  .row-details {
    color: #438cff;
    z-index: 10;
    position: relative;
  }
  .color-statistical-name {
    font-weight: bold;
    font-size: 16px;
  }
  .num-change {
    margin-left: 15px;
  }
}
.ui-table-bottom {
  height: 120px;
  @{_deep} .ivu-table-tbody {
    tr {
      border: 1px solid var(--border-color);
    }
    td {
      height: 90px;
    }
  }
}
</style>
