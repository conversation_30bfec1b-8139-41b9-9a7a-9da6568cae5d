<!--
 * @Date: 2025-01-20 10:15:38
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-06 10:51:11
 * @FilePath: \icbd-view\src\views\juvenile\components\juvenile-map\map-alarm.vue
-->
<template>
  <div class="map-alarm-box" v-if="isShow" :class="`border` + data.bgIndex">
    <div
      class="alarm-head"
      :class="{
        bg1: data.bgIndex == 1,
        bg2: data.bgIndex == 2,
        bg3: data.bgIndex == 3,
        bg4: data.bgIndex == 4,
        bg5: data.bgIndex == 5,
      }"
    >
      <div class="head-title">
        <ui-icon
          type="jingqing"
          style="color: #fff; margin-right: 10px"
        ></ui-icon
        ><span>{{ taskLevelList[Number(data.taskLevel) - 1] }}</span>
      </div>
      <div @click="closeHandler">
        <ui-icon type="close" :color="'#fff'"></ui-icon>
      </div>
    </div>
    <div class="contrast">
      <div class="contrast-left">
        <ui-image :src="data.traitImg"></ui-image>
        <div class="contrast-title">报警照片</div>
      </div>
      <div class="contrast-content">
        <ui-image class="animation" :src="percent[data.bgIndex]"></ui-image>
        <div
          class="num"
          :class="{
            c1: data.bgIndex == 1,
            c2: data.bgIndex == 2,
            c3: data.bgIndex == 3,
            c4: data.bgIndex == 4,
            c5: data.bgIndex == 5,
          }"
        >
          {{
            data.simScore
              ? (data.simScore.toFixed(4) * 100).toString().substring(0, 5)
              : 0
          }}%
        </div>
      </div>
      <div class="contrast-right">
        <ui-image :src="data.photoUrl"></ui-image>
        <div class="contrast-title">布控照片</div>
      </div>
    </div>
    <div class="box-info">
      <div class="box-head">
        <span>{{ data.name }}</span>
        <span style="margin-left: 10px">{{ data.ageUpLimit }}岁</span>
        <!-- <span
          class="text-over-span"
          style="margin-left: 10px; color: #ea4a36; font-weight: 400"
          :title="data?.bizLabels?.join(',')"
          >（{{ data?.bizLabels?.join(",") || "--" }}）</span
        > -->
      </div>
      <div class="box-info-left">
        <div class="box-wrapper">
          <div class="title">报警时间：</div>
          <div class="box-val info-color">{{ data.alarmTime }}</div>
        </div>
        <div class="box-wrapper">
          <div class="title">报警设备：</div>
          <div class="box-val info-color" :title="data.deviceName">
            {{ data.deviceName }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
const taskLevelList = ["一级报警", "二级报警", "三级报警"];
export default {
  name: "MapAlarm",
  data() {
    return {
      taskLevelList,
      percent: {
        1: require("@/assets/img/target/c-one.png"),
        2: require("@/assets/img/target/c-two.png"),
        3: require("@/assets/img/target/c-three.png"),
        4: require("@/assets/img/target/c-four.png"),
        5: require("@/assets/img/target/c-five.png"),
      },
      isShow: false,
    };
  },
  props: {
    // 报警级别
    alarmLevel: {
      type: String,
      default: "1",
    },
    data: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    show() {
      this.isShow = true;
    },
    closeHandler() {
      this.$emit("closeplaceInfoWindow");
    },
  },
};
</script>

<style lang="less" scoped>
.map-alarm-box {
  background: #fff;
  width: 270px;
  // border: 1px solid #ea4a36;
  .alarm-head {
    display: flex;
    height: 30px;
    justify-content: space-between;
    align-items: center;
    // background: linear-gradient(210deg, #ff7b56 0%, #ea4a36 100%);
    padding: 0 10px;
    .head-title {
      font-weight: bold;
      font-size: 14px;
      color: #ffffff;
    }
  }
}
.contrast {
  height: 78px;
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  .contrast-left {
    width: 66px;
    height: 66px;
    position: relative;
    border: 1px solid #d3d7de;
  }
  .contrast-right {
    width: 66px;
    height: 66px;
    position: relative;
    border: 1px solid #d3d7de;
  }
  .contrast-title {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 18px;
    width: 64px;
    background: rgba(0, 0, 0, 0.45);
    border-radius: 0px 0px 8px 0px;
    font-size: 12px;
    color: #fff;
    text-align: center;
  }
  .contrast-content {
    position: relative;
    width: 70px;
    height: 70px;
  }
  .num {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    align-items: center;
    display: flex;
    justify-content: center;
    color: #2c86f8;
  }
  .c1 {
    color: #ea4a36;
  }
  .c2 {
    color: #e77811;
  }
  .c3 {
    color: #ee9f00;
  }
  .c4 {
    color: #36be7f;
  }
  .c5 {
    color: #2c86f8;
  }
}
.box-info {
  padding: 5px 20px 10px 20px;
  .box-head {
    span {
      font-weight: bold;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
    }
    .text-over-span {
      display: inline-block;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .box-wrapper {
    display: flex;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 17px;
    padding-top: 5px;
    .title {
      text-wrap: nowrap;
    }
    .box-val {
      text-wrap: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.bg1 {
  background: linear-gradient(210deg, #ff7b56 0%, #ea4a36 100%);
}
.bg2 {
  background: linear-gradient(210deg, #ffaf65 0%, #fc770b 100%);
}
.bg3 {
  background: linear-gradient(210deg, #ffd752 0%, #ffc300 100%);
}
.bg4 {
  background: linear-gradient(262deg, #27d676 8%, #36be7f 89%);
}
.bg5 {
  background: linear-gradient(263deg, #5bcaff 2%, #2c86f8 97%);
}
.border1 {
  border: 1px solid #ea4a36;
}
.border2 {
  border: 1px solid #e77811;
}
.border3 {
  border: 1px solid #ee9f00;
}
.border4 {
  border: 1px solid #36be7f;
}
.border5 {
  border: 1px solid #2c86f8;
}
</style>
