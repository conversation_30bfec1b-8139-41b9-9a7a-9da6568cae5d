<template>
  <ui-card title="基本信息">
    <div slot="extra" class="play-btn mr-10 color-primary" v-if="type === 'video'" @click="videoPlay"><i class="iconfont icon-yunhang color-primary mr-5"></i>视频播放</div>
    <div class="info-content">
      <p>
        <span class="label">设备名称</span><b>{{ baseInfo.deviceName }}</b>
      </p>
      <p>
        <span class="label">点位类型</span><b>{{ baseInfo.sbdwlx | commonFiltering(sbdwlxList) }}</b>
      </p>
      <p>
        <span class="label">功能类型</span><b>{{ baseInfo.sbgnlx | commonFiltering(sbgnlxList) }}</b>
      </p>
      <p>
        <span class="label">采集区域</span><b :title="baseInfo.sbcjqyText" class="ellipsis">{{ baseInfo.sbcjqyText }}</b>
      </p>
      <p>
        <span class="label">设备状态</span><b>{{ baseInfo.isOnline | commonFiltering(isonlineList) }}</b>
      </p>
      <p>
        <span class="label">所属机构</span><b>{{ baseInfo.gldw }}</b>
      </p>
      <p>
        <span class="label">安装时间</span><b>{{ baseInfo.azsj }}</b>
      </p>
      <p>
        <span class="label">安装地址</span><b :title="baseInfo.detailAddress" class="ellipsis">{{ baseInfo.detailAddress }}</b>
      </p>
      <p>
        <span class="label">设备厂商</span><b>{{ baseInfo.manufacturer | commonFiltering(manufacturerList) }}</b>
      </p>
      <p>
        <span class="label">建设单位</span><b :title="baseInfo.jsdw" class="ellipsis">{{ baseInfo.jsdw }}</b>
      </p>
      <p>
        <span class="label">联网属性</span><b>{{ baseInfo.sblwzt | commonFiltering(sblwztList) }}</b>
      </p>
      <p>
        <span class="label branch">所属部门/行业</span><b :title="baseInfo.ssbm | commonFiltering(professionList)" class="ellipsis">{{ baseInfo.ssbm | commonFiltering(professionList) }}</b>
      </p>
    </div>
  </ui-card>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    type: {
      type: String,
      default: 'capture'
    },
    baseInfo: {
      type: Object,
      default: {}
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      sbdwlxList: 'dictionary/getSbdwlxList', // 摄像机点位类型
      sbgnlxList: 'dictionary/getSbgnlxList', //摄像机功能类型
      manufacturerList: 'dictionary/getManufacturerList', //厂商
      sblwztList: 'dictionary/getSblwztList', //联网属性
      isonlineList: 'dictionary/getIsonlineList', //设备状态
      professionList: 'dictionary/getProfession' //职业
    })
  },
  mounted() {},
  methods: {
    videoPlay() {
      this.$emit('videoPlay')
    }
  }
}
</script>
<style lang="less" scoped>
.info-content {
  width: 100%;
  p {
    display: flex;
    font-size: 14px;
    line-height: 36px;
    color: rgba(0, 0, 0, 0.9);
    .label {
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
      width: 58px;
      font-weight: bold;
      white-space: nowrap;
      color: #2c86f8;
      margin-right: 15px;
    }
    .branch {
      width: 91px;
    }
    b {
      font-weight: normal;
      word-break: break-all;
      width: 400px;
      &.weight {
        font-size: 20px;
        font-weight: bold;
      }
    }
  }
  .bg-vedio {
    margin: 0 auto 15px;
    height: 170px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 360px;
    background: url('~@/assets/img/device/vedio.png') center/cover;
    span {
      width: 118px;
      height: 36px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 18px;
      font-size: 14px;
      i {
        font-size: 18px;
        margin-right: 5px;
      }
    }
  }
}
.play-btn {
  margin-top: 5px;
  margin-right: 15px;
  font-size: 14px;
  cursor: pointer;
  line-height: 20px;
  display: flex;
  align-items: center;
  i {
    font-size: 16px;
  }
}
</style>
