<template>
  <div class="asset-comparison-wrap auto-fill">
    <div class="header">
      <div class="header-left">
        <div class="title">
          <i class="icon-font icon-shujuyuan1"></i>
          <span> 数据源:</span>
        </div>
        <div class="content">
          <div class="content-item" v-for="(e, i) in statisticalLeftList" :key="i">
            <span>
              <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
            </span>
            <span>
              <div class="name">{{ e.name }}</div>
              <div class="count" :style="{ color: e.color }">
                {{ e.leftCount }}
              </div>
            </span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <div class="title">
          <span>
            <i class="icon-font icon-shujuyuan1"></i>
            <span> 数据源：{{}}</span>
          </span>
        </div>
        <div class="content">
          <div class="content-item" v-for="(e, i) in statisticalRightList" :key="i">
            <span>
              <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
            </span>
            <span>
              <div class="name">{{ e.name }}</div>
              <div class="count" :style="{ color: e.color }">
                {{ e.rightCount }}
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="tab-line">
      <div class="tab-line-left">
        <div
          v-for="(item, index) in statusList"
          :key="index"
          :class="{ active: item.status === activeStatus }"
          class="ivu-tabs-tab"
          @click="tabClick(item)"
        >
          <span>{{ item.name }}</span>
        </div>
      </div>
      <div class="tab-line-right">
        <Checkbox v-if="this.activeStatus !== '2'" @on-change="changeIsLeftAll" v-model="isAll" :disabled="false">
          全选
        </Checkbox>
        <Button type="primary" class="ml-sm button-blue" :loading="exportDataLoading" @click="exportExcel">
          <i class="icon-font icon-xinxizidongtongbu f-12 mr-sm vt-middle"></i>
          <span class="inline vt-middle ml-xs">同步</span>
        </Button>
        <Button type="primary" class="ml-sm button-blue" :loading="exportDataLoading" @click="exportExcel">
          <i class="icon-font icon-daochu delete-icon"></i>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
        <Button type="primary" class="ml-sm button-blue" :loading="exportDataLoading" @click="exportExcel">
          <i class="icon-font icon-shanchu mr-sm"></i>
          <span class="inline vt-middle ml-xs">删除</span>
        </Button>
      </div>
    </div>
    <div class="content">
      <div class="content-left auto-fill">
        <Checkbox v-if="this.activeStatus === '2'" v-model="isLeftAll" :disabled="false" @on-change="changeIsLeftAll">
          全选
        </Checkbox>
        <ui-table
          class="ui-table auto-fill mt-sm"
          reserveSelection
          :default-store-data="defaultStoreData"
          :is-all="isAll"
          :table-columns="leftTableColumns"
          :table-data="tableDataLeftList"
          :minusHeight="160"
          @storeSelectList="storeSelectList"
        >
        </ui-table>
      </div>
      <div class="content-right auto-fill">
        <Checkbox v-if="this.activeStatus === '2'" v-model="isRightAll" :disabled="false" @on-change="changeIsRightAll">
          全选
        </Checkbox>
        <ui-table
          class="ui-table auto-fill mt-sm"
          :default-store-data="defaultRightStoreData"
          :is-all="isRightAll"
          :key="activeStatus"
          :table-columns="rightTableColumns"
          :table-data="tableDataRightList"
          :minusHeight="160"
        >
        </ui-table>
      </div>
    </div>
    <div class="page-wrapper">
      <ui-page class="page" :page-data="pageBaseData" @changePage="changeBasePage" @changePageSize="changeBasePageSize">
      </ui-page>
      <ui-page
        class="page"
        v-if="this.activeStatus === '2'"
        :page-data="pageRightData"
        @changePage="changeRightPage"
        @changePageSize="changeRightPageSize"
      >
      </ui-page>
    </div>
    <!-- <equipment-Info-comparison
      ref="info-comparison"
      :title="'设备信息比对'"
      :comparison-fields="comparisonFields"
      :table-data-left="tableDataLeft"
      :table-data-right="tableDatableRight"
      :resultData="resultData"
      @handleResetTableData="getComparisonResultList"
    ></equipment-Info-comparison> -->
  </div>
</template>

<script>
import { leftTableColumns, rightTableColumns, statisticalQuantityList } from './enum';

export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    // EquipmentInfoComparison: require('./components/equipment-info-comparison.vue')
    //   .default,
  },
  data() {
    return {
      statusList: Object.freeze([
        { name: '相同', status: '0' },
        { name: '差异', status: '1' },
        { name: '独有', status: '2' },
      ]),
      statisticalLeftList: statisticalQuantityList,
      statisticalRightList: statisticalQuantityList,
      activeStatus: '0',
      tableDataLeftList: [{ id: 22 }, { id: 33 }, { id: 44 }],
      tableDataRightList: [{ id: 22 }, { id: 33 }, { id: 44 }],
      defaultStoreData: [], // 存储勾选的设备
      defaultRightStoreData: [],
      loading: false,
      leftTableColumns: Object.freeze(leftTableColumns),
      rightTableColumns: Object.freeze(rightTableColumns),
      comparisonFields: [],
      pageRightData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      pageBaseData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      isAll: false,
      isLeftAll: false,
      isRightAll: false,
      exportDataLoading: false,
    };
  },
  watch: {},
  async created() {},
  async mounted() {},
  methods: {
    tabClick({ status }) {
      this.activeStatus = status;
      this.rightTableColumns = [...rightTableColumns];
      if (status === '2') {
        this.rightTableColumns = [
          {
            type: 'selection',
            width: 50,
            align: 'center',
          },
          ...rightTableColumns,
        ];
      }
      this.resetAllData();
    },
    changeIsLeftAll(val) {
      this.tableDataLeftList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
    },
    changeIsRightAll(val) {
      this.tableDataRightList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
    },
    async getStatic() {
      try {
        // let res = await this.$http.post(taganalysis.getDeviceTag, {
        //   isPage: false,
        // })
      } catch (err) {
        console.log(err);
      }
    },
    handleStaticList() {},
    resetAllData() {
      // this.tableDataLeftList = []
      // this.tableDataRightList = []
      this.pageRightData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.pageBaseData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
    },
    storeSelectList() {},
    changeBasePage(val) {
      this.pageBaseData.pageNum = val;
    },
    changeBasePageSize(val) {
      this.pageBaseData.pageSize = val;
    },
    changeRightPage(val) {
      this.pageRightData.pageNum = val;
    },
    changeRightPageSize(val) {
      this.pageRightData.pageSize = val;
    },
    exportExcel() {},
  },
};
</script>

<style lang="less" scoped>
.asset-comparison-wrap {
  height: 100%;
  padding: 0 20px 20px;
  .header {
    display: flex;
    > div {
      width: 50%;
      padding-bottom: 10px;
      .title {
        height: 53px;
        line-height: 53px;
        font-size: 16px;
        color: var(--color-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        & > span {
          margin-right: 62px;
          i {
            margin-right: 4px;
          }
        }
      }
      .content {
        height: 110px;
        display: flex;
        align-items: center;
        width: 100%;
        background: var(--bg-sub-content);
        .content-item {
          width: 25%;
          display: flex;
          align-items: center;
          position: relative;
          .icon-font {
            font-size: 37px;
            margin-left: 30px;
            margin-right: 10px;
          }
          .name {
            font-size: 14px;
            color: #f5f5f5;
          }
          .count {
            font-size: 18px;
          }
          &:first-child::after {
            content: '';
            width: 0px;
          }
        }
        .content-item::after {
          content: '';
          width: 1px;
          height: 40px;
          position: absolute;
          top: 10px;
          left: 0;
          background: #1568ad;
        }
      }
    }
    .header-left {
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }
    .header-right {
      padding-left: 10px;
    }
  }
  .tab-line {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    background: var(--bg-sub-content);
    padding: 10px;
    margin-bottom: 10px;
    .ivu-tabs-tab {
      float: left;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border: 1px solid var(--border-btn-dashed);
      border-right: none;
      padding: 0 22px;
      color: var(--color-btn-dashed);
      &:hover {
        background: var(--color-primary);
        color: #fff;
        cursor: pointer;
      }
      &:last-child {
        border-right: 1px solid var(--border-btn-dashed);
      }
    }
    .active {
      background: var(--color-primary);
      color: #fff;
    }
    .tab-line-right {
      display: flex;
      align-items: center;
    }
  }
  /deep/ .content {
    display: flex;
    .content-left {
      width: 50%;
      border-right: 1px solid var(--devider-line);
      padding-right: 10px;
    }
    .content-right {
      width: 50%;
      padding-left: 10px;
    }
    .content-left,
    .content-right {
      .ui-table {
        .ivu-table-column-left {
          padding-left: 10px;
        }
      }
    }
    .ivu-table-cell-ellipsis {
      .ivu-table-cell-slot {
        > span {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .footer {
    display: flex;
    .page-right,
    .page-left {
      width: 50%;
      padding: 20px 8px;
    }
  }
  .page-wrapper {
    display: flex;
    .page {
      flex: 1;
    }
  }
}
</style>
