<template>
  <div class="auto-fill">
    <div class="header-box">
      <Button type="default" class="button-export" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu mr-xs f-14"></i>
        <span class="ml-xs">导出</span>
      </Button>
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template v-for="(item, index) in tableSlotList" #[item.slot]="{ row }">
        <span
          v-if="row.detail[item.key] || row.detail[item.key] === 0"
          @click="viewDeviceDetail(row, item)"
          :key="index"
          :class="{
            'unqualified-color': !['deviceNum'].includes(item.key),
            'underline-text': row.detail[item.key] !== 0,
          }"
        >
          {{ row.detail[item.key] }}
        </span>
        <span v-else :key="index">--</span>
      </template>
    </ui-table>
    <!-- 设备详情 -->
    <DetailsModal v-model="showDetailsModal" :row-data="currentRowData"></DetailsModal>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import dataAnalysis from '@/config/api/dataAnalysis.js';
import { getAbnormalTableColumns } from '../utils/tableConfig.js';

export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DetailsModal: require('../components/details-modal.vue').default,
  },
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      exportLoading: false,
      tableData: [],
      tableColumns: [],
      tableSlotList: [],
      loading: false,
      // 设备详情
      showDetailsModal: false,
      currentRowData: {},
      configInFo: {},
    };
  },
  created() {
    let { code } = this.$route.query;
    this.tableColumns = getAbnormalTableColumns()[code];
    this.tableSlotList = this.tableColumns.filter((item) => item.slot);
    this.startWatch(
      '$route',
      () => {
        this.getConfigInfo();
        this.getTableList();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    getApiParams() {
      let { batchId } = this.$route.query;
      let data = {
        batchId,
      };
      return data;
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let data = this.getApiParams();
        const res = await this.$http.post(dataAnalysis.exportStatInfoList, data);
        await this.$util.common.transformBlob(res.data.data);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
    async getTableList() {
      try {
        this.loading = true;
        let data = this.getApiParams();
        let res = await this.$http.post(dataAnalysis.getStatInfoList, data);
        this.tableData = res.data.data || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    viewDeviceDetail(row, columnItem) {
      if (row.detail[columnItem.key] === 0) return;
      this.showDetailsModal = true;
      let { serialOffDay, accOffDay } = this.configInFo;
      this.currentRowData = {
        ...row,
        accOffline1: columnItem.key === 'accOffNum' ? accOffDay : null, // 累计离线天数
        serialOffline1: columnItem.key === 'serialOffNum' ? serialOffDay : null, // 最大连续离线天数
      };
    },
    async getConfigInfo() {
      try {
        let data = this.getApiParams();
        let res = await this.$http.post(dataAnalysis.getConfigInfo, data);
        this.configInFo = res.data.data || {};
        let { serialOffDay, accOffDay } = this.configInFo;
        let { code } = this.$route.query;
        this.tableColumns = getAbnormalTableColumns({ serialOffNum: serialOffDay, accOffNum: accOffDay })[code];
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.header-box {
  display: flex;
  justify-content: flex-end;
  .button-export {
    margin-bottom: 16px;
  }
}
.underline-text {
  text-decoration: underline;
  cursor: pointer;
}
.unqualified-color {
  color: var(--color-failed);
}
</style>
