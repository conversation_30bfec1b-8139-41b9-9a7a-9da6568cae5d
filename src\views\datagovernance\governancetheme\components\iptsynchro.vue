<template>
  <div class="iptsynchro">
    <div class="iptsynchro-item">
      <Checkbox label="first" v-model="single"></Checkbox>
      <div class="check-item" @click.prevent>
        <p>1、定期巡检</p>
        <p>定期巡检，数据有变更则触发同步。</p>
        <p class="align-flex">
        <Form class="align-flex" ref="formValidate" :model="dateForm" :rules="ruleValidate" :label-width="0">
          <FormItem label="" prop="time1">
            <span class="right-margin">更新时间</span>
            <Select v-model="dateForm.time1" style="width: 118px" @on-change="timeTypeChange">
              <Option v-for="item in cityList" :value="item.value" :key="item.value">{{ item.label }}</Option>
            </Select>
            <span class="left-margin right-margin">-</span>
          </FormItem>
          <FormItem label="" prop="time2">
            <template v-if="dateForm.time1 !== 'day'">
              <Select v-model="dateForm.time2" style="width: 118px">
                <Option v-for="(item, index) in timeData" :value="item.value" :key="index">{{ item.label }}</Option>
              </Select>
              <span class="left-margin right-margin">-</span>
            </template>
          </FormItem>
          <FormItem label="" prop="time">
            <template v-if="dateForm.time !== 'day'">
              <TimePicker type="time" :value="dateForm.time" placeholder="Select time" style="width: 168px">
              </TimePicker>
            </template>
          </FormItem>
        </Form>
        </p>
      </div>
    </div>
    <div class="iptsynchro-item">
      <Checkbox label="Sleep"></Checkbox>
      <div class="check-item">
        <p>2、消息通知</p>
        <p>接收到数据网关数据变更通知，触发数据更新。</p>
      </div>
    </div>
    <div class="iptsynchro-item">
      <Checkbox label="Run"></Checkbox>
      <div class="check-item">
        <p>3、手动触发</p>
        <p>重新触发数据治理流程时，触发数据更新。</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {},
  data () {
    return {
      checkbox: [],
      cityList: [
        { value: 'day', label: '每天' },
        { value: 'week', label: '每周' },
        { value: 'month', label: '每月' },
      ],
      weeks: [
        { value: 'day', label: '星期一' },
        { value: 'day', label: '星期二' },
        { value: 'day', label: '星期三' },
        { value: 'day', label: '星期四' },
        { value: 'day', label: '星期五' },
      ],
      months: [
        { value: 'day', label: '星期一' },
        { value: 'day', label: '星期二' },
        { value: 'day', label: '星期三' },
        { value: 'day', label: '星期四' },
        { value: 'day', label: '星期五' },
      ],

      timeData: [],
      single: false,
      dateForm: {
        time1: '',
        time2: '',
        time: '',
      },
      ruleValidate: {
        time1: [
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ],
        time2: [
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ],
        time: [
          {
            required: true,
            type: 'string',
            message: '请选择',
            trigger: 'change',
          },
        ],
      },
    };
  },
  created () { },
  methods: {
    onselect () {
      const index = this.checkbox.indexOf('first');
      index !== -1
        ? this.checkbox.splice(index, 1)
        : this.checkbox.push('first');
    },
    timeTypeChange () {
      if (this.dateForm.time1 == 'week') {
        this.timeData = this.weeks;
      } else if (this.dateForm.time1 == 'month') {
        this.months = [];
        for (let i = 0; i < 31; i++) {
          this.months.push({ value: i + 1, label: i + 1 });
        }
        this.timeData = this.months;
      } else {
        this.timeData = [];
      }
    },
    handleValidate () {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.$Message.success('Success!');
        } else {
          this.$Message.error('Fail!');
        }
      });
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang='less' scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.left-margin {
  margin-left: 8px;
}
.right-margin {
  margin-right: 8px;
}
.iptsynchro {
  .iptsynchro-item {
    display: flex;
  }
  .check-item {
    margin-left: 10px;
    font-size: 14px;
    color: #ffffff;
    p {
      margin-bottom: 8px;
    }
  }
}

</style>
<style lang="less">
.iptsynchro {
  .ivu-checkbox-group-item {
    display: flex !important;
  }
  .ivu-checkbox-checked .ivu-checkbox-inner {
    background: var(--color-primary);
  }
}
</style>