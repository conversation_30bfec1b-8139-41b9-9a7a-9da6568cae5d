<template>
  <div class="layout-container">
    <!-- 左侧 -->
    <div class="layout-left auto-fill">
      <slot name="layout-left"></slot>
    </div>
    <div class="layout-right auto-fill">
      <!-- 筛选 -->
      <div class="layout-filter">
        <slot name="layout-filter"></slot>
      </div>
      <!-- 内容 -->
      <div class="layout-content auto-fill">
        <slot name="layout-content"></slot>
      </div>
      <!-- 分页 -->
      <div class="layout-footer">
        <slot name="layout-footer"></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'layout',
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.layout-container {
  position: relative;
  height: 100%;
  background-color: var(--bg-content);

  .layout-left {
    width: 300px;
    float: left;
    padding: 19px;
    height: calc(~'100vh - 53px - 36px - 22px');
    overflow-x: auto;
  }
  .layout-right {
    margin-left: 300px;
    height: 100%;
    border-left: 1px solid var(--border-color);
  }
  .layout-filter {
    height: 56px;
    display: flex;
    align-items: flex-end;
    margin-left: 19px;
    margin-right: 19px;
  }
  .layout-content {
    position: relative;
    padding: 19px;
    height: 100%;
  }
  .layout-footer {
    height: 64px;
  }
}
</style>
