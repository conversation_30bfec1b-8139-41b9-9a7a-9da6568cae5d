<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />
    <!-- 模式切换 ---------------------------------------------------------------------------------------------------- -->
    <tagView class="tagView" ref="tagView" :list="['图像模式', '聚档模式']" @tagChange="tagChange1" />
    <!-- 图像模式 ---------------------------------------------------------------------------------------------------- -->
    <TableList
      v-if="modelTag == 0 && columns.length > 0"
      ref="infoList"
      :columns="columns"
      :minusHeight="minusHeight"
      :loadData="loadDataList"
    >
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <trajectory-search @startSearch="startListSearch"></trajectory-search>
      </div>
      <!-- 表格操作 -->
      <template #trackImage="{ row }">
        <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
          <ui-image :src="row.trackImage" />
        </div>
      </template>
      <template #identityPhoto="{ row }">
        <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
          <ui-image :src="row.identityPhoto" alt="" />
        </div>
      </template>
      <template #urlAvailableStatus="{ row }">
        <span style="color: #19c176" v-if="row.urlAvailableStatus == 1">URL可访问</span>
        <span style="color: #c43d2c" v-else>URL不可访问</span>
      </template>
      <template #thumbnailStatus="{ row }">
        <span style="color: #19c176" v-if="row.thumbnailStatus == 1">可用</span>
        <span style="color: #c43d2c" v-else-if="row.thumbnailStatus == 2">不可用</span>
        <span v-else>--</span>
      </template>
      <template #largeStatus="{ row }">
        <span style="color: #19c176" v-if="row.largeStatus == 1">可用</span>
        <span style="color: #c43d2c" v-else-if="row.largeStatus == 2">不可用</span>
        <span v-else>--</span>
      </template>
    </TableList>
    <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
    <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo" v-if="modelTag == 1">
      <div slot="search" class="hearder-title">
        <trajectory-search :is-image-model="false" class="mb-sm" @startSearch="startListSearch"></trajectory-search>
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <UiGatherCard
          class="card"
          :list="row"
          :personTypeList="personTypeList"
          :cardInfo="cardInfo"
          @detail="detailInfo(row)"
        ></UiGatherCard>
      </template>
    </TableCard>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <person-detail ref="captureDetail" :resultId="taskObj.resultId">
      <template #searchList>
        <trajectory-search class="capture-search" @startSearch="startDetailSearch"></trajectory-search>
      </template>
    </person-detail>
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    UiGatherCard: require('../../components/ui-gather-card.vue').default,
    ChartsContainer: require('../../components/chartsContainer').default,
    tagView: require('../../components/tags').default,
    LookScene: require('@/components/look-scene').default,
    TableList: require('../../components/tableList.vue').default,
    TableCard: require('../../components/tableCard.vue').default,
    TrajectorySearch: require('./trajectory-search.vue').default,
    personDetail: require('@/components/person-detail.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      modelTag: 0, // 聚档模式,图像模式
      bigPictureShow: false,
      imgList: [],
      searchData: {},
      abnormalCount: [
        {
          title: '重点人员数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
          fieldName: 'importantPersonAmount',
        },
        {
          title: 'ZDR人像轨迹总量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
          fieldName: 'zdrImportantPersonAmount',
        },
        {
          title: 'URL不可访问轨迹数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
          fieldName: 'urlNotAvailableAmount',
        },
        {
          title: '大图URL不可访问轨迹数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
          fieldName: 'largeUrlNotAvailableAmount',
        },
        {
          title: '小图URL不可访问轨迹数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
          fieldName: 'smallUrlNotAvailableAmount',
        },
      ],
      columns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '人脸抓拍', key: 'trackImage', slot: 'trackImage' },
        { title: '抓拍时间', key: 'shotTime', width: 170 },
        { title: '抓拍点位', key: 'catchPlace' },
        { title: '证件照', key: 'identityPhoto', slot: 'identityPhoto' },
        { title: '姓名', key: 'name' },
        { title: '证件号', key: 'idCard' },
        { title: '小图URL是否可用', key: 'thumbnailStatus', slot: 'thumbnailStatus' },
        { title: '大图URL是否可用', key: 'largeStatus', slot: 'largeStatus' },
        { title: '检测结果', key: 'urlAvailableStatus', slot: 'urlAvailableStatus' },
      ],
      loadDataList: (parameter) => {
        let params = Object.assign(
          {
            batchId: this.indexResult().batchId,
            taskIndexId: this.indexResult().taskIndexId,
          },
          parameter.params,
          this.searchData,
        );
        return this.$http.post(inspectionrecord.pageTrackUrlAvailableDetails, params).then((res) => {
          return res.data;
        });
      },
      minusHeight: 440,
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'catchAmount' },
        { name: '异常轨迹：', value: 'unqualifiedAmount', color: '#BC3C19' },
      ],
      loadDataCard: (parameter) => {
        let params = Object.assign(
          {
            batchId: this.indexResult().batchId,
            taskIndexId: this.indexResult().taskIndexId,
          },
          parameter.params,
          this.searchData,
        );
        return this.$http.post(inspectionrecord.pageTrackUrlAvailableClusters, params).then((res) => {
          return res.data;
        });
      },
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
      // getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {
    taskObj: {
      deep: true,
      handler: function () {
        this.init();
      },
    },
  },
  filter: {},
  created() {},
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
    this.init();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    indexResult() {
      if (this.taskObj.indexResults && this.taskObj.indexResults.length) {
        return this.taskObj.indexResults[0];
      }
      return {};
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 详情
    detailInfo(info) {
      this.$refs.captureDetail.show(info);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
    async init() {
      await this.static();
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    async static() {
      let params = {
        batchId: this.indexResult().batchId,
        taskIndexId: this.indexResult().taskIndexId,
      };
      let {
        data: { data },
      } = await this.$http.post(inspectionrecord.getByResultId, params);
      this.abnormalCount.forEach((ele) => {
        if (data[ele.fieldName]) {
          ele.count = data[ele.fieldName];
        } else if ((ele.fieldName = 'urlNotAvailableAmount')) {
          ele.count = data.zdrImportantPersonAmount - data.qualifiedAmount;
        } else {
          ele.count = 0;
        }
      });
    },
    // 检索
    startListSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.ui-images {
  width: 56px;
  height: 56px;
  margin: 5px 0 5px 0;
  .ui-image {
    min-height: 56px !important;
    /deep/ .ivu-spin-text {
      img {
        width: 56px;
        height: 56px;
        margin-top: 5px;
      }
    }
  }
}
</style>
