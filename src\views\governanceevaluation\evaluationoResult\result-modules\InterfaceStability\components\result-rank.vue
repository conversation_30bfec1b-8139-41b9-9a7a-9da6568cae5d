<template>
  <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
    <div class="ranking-title f-14">
      <span class="icon-font icon-xiajipaihang mr-sm"></span>
      <span>下级排行</span>
    </div>
    <div class="ranking-list">
      <div class="rank-item" v-for="(item, index) in rankData" :key="index">
        <div class="content-firstly">
          <span class="bg_color font-white t-center" :class="`firstly${item.rank > 3 ? 4 : item.rank}`">{{
            item.rank
          }}</span>
        </div>
        <Tooltip class="content-second" transfer :content="item.regionName">
          <div>
            <img class="vt-middle second-image" :src="getImageUrl(item.rank)" />
            <span class="ellipsis">{{ item.regionName }}</span>
          </div>
        </Tooltip>
        <div class="content-thirdly">
          <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
        </div>
        <div class="content-fourthly">
          <span class="icon-font f-16" :class="getClassByRankType(item.rankType)"></span>
          <span class="plus ml-sm" :class="`color-${item.rankType}`">{{ item.rankRise || 0 }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'ranking-info',
  mixins: [dealWatch],
  data() {
    return {
      rankData: [],
      rankLoading: false,
    };
  },
  created() {
    this.getRankInfo();
    // this.startWatch('$route.query', () => {
    //   this.getRankInfo()
    // },{deep: true, immediate: true})
  },
  methods: {
    getClassByRankType(val) {
      switch (val) {
        case 'RISE':
        case 'SAME':
          return ['icon-shangsheng', 'color-RISE'];
        case 'DOWN':
          return ['icon-xiajiang', 'color-DOWN'];
      }
    },
    getImageUrl(rank) {
      if ([1, 2, 3].includes(rank)) {
        return require(`@/assets/img/crown_${rank}.png`);
      }
    },
    getRankInfo() {
      this.rankData = [
        {
          'regionCode': '23030000',
          'regionName': '鸡西市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 1,
          'rankRise': '0',
          'rankType': 'SAME',
        },
        {
          'regionCode': '23110000',
          'regionName': '黑河市公安局',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 2,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23040000',
          'regionName': '鹤岗市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 3,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23080000',
          'regionName': '佳木斯市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 4,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23090000',
          'regionName': '七台河市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 5,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23270000',
          'regionName': '大兴安岭地区',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 6,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23060000',
          'regionName': '大庆市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 7,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23070000',
          'regionName': '伊春市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 8,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23120000',
          'regionName': '绥化市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 9,
          'rankRise': '0',
          'rankType': 'SAME',
        },
        {
          'regionCode': '23100000',
          'regionName': '牡丹江',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 10,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23010000',
          'regionName': '哈尔滨',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 11,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23050000',
          'regionName': '双鸭山市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 12,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23020000',
          'regionName': '齐齐哈尔市公安局',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 13,
          'rankRise': '0',
          'rankType': 'RISE',
        },
      ];
      // this.rankLoading = true
      // const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query
      // let data = {
      //   indexId: indexId,
      //   batchId: batchId,
      //   access: access || 'REPORT_MODE',
      //   displayType: statisticType,
      //   orgRegionCode:  statisticType === 'REGION' ? regionCode : orgCode,
      // }
      // try {
      //   let res = await this.$http.post(evaluationoverview.getRankInfo, data)
      //   this.rankData = res.data.data || []
      // } catch (err) {
      //   console.log(err)
      // } finally {
      //   this.rankLoading = false
      // }
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.information-ranking {
  position: relative;
  width: 100%;
  background: var(--bg-sub-content);
  height: 100%;

  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    border-bottom: 1px solid var(--border-color);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }

  .ranking-list {
    padding: 10px;
    width: 100%;
    height: 81%;
    overflow-y: auto;

    .rank-item {
      display: flex;
      padding-top: 15px;
      align-items: center;

      .content-fourthly {
        display: inline-flex;
        font-size: 14px;
        position: relative;
        justify-content: center;
        flex: 1;
      }

      div {
        display: flex;
        align-items: center;
        font-size: 14px;
        position: relative;
      }

      .content-firstly {
        flex: 1;
        margin-left: 10px;
      }

      .content-thirdly {
        justify-content: center;
        flex: 1;
      }

      .content-second {
        color: #fff;
        justify-content: center;
        flex: 1;

        .second-image {
          width: 16px;
        }

        span {
          width: 75px;
          padding-left: 10px;
          display: inline-block;
        }

        .rankText {
          margin-left: 20px;
        }
      }

      .bg_color {
        min-width: 21px;
        min-height: 21px;
        font-weight: bold;
      }

      .firstly1 {
        background-color: #f1b700;
      }

      .firstly2 {
        background-color: #eb981b;
      }

      .firstly3 {
        background-color: #ae5b0a;
      }

      .firstly4 {
        background-color: var(--color-primary);
      }

      .thirdly {
        overflow: hidden;
        color: var(--color-primary);
      }

      .color-RISE {
        color: var(--color-success);
      }

      .color-DOWN {
        color: var(--color-failed);
      }

      .color-SAME {
        color: var(--color-success);
      }
    }
  }
}
</style>
