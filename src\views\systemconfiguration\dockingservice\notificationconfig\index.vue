<template>
  <div class="page-assessmentresult auto-fill">
    <div class="right-content">
      <div class="page-button">
        <Button type="primary" @click="save">
          <span class="icon-font f-14 mr-xs" :class="!isEdit ? 'icon-baocun' : 'icon-bianji3'"></span>
          {{ !isEdit ? '保  存' : '修  改' }}</Button
        >
      </div>
      <div class="title-center">
        <div class="conter-center">
          <span class="fs24"> <span class="title">#月份#</span> 月公安视频图像数据治理考核通报 </span>
          <div class="time fs12">
            <i class="icon-font icon-shijian fs12"></i>
            发布时间：<span class="title">&nbsp;&nbsp;#YYYY/MM/DD HH:MM:SS#</span>
          </div>
        </div>
      </div>
      <div class="breadcrumb-container"><span class="box vt-middle"></span><span class="ml-sm">通报说明： </span></div>
      <div class="page-change">
        <Checkbox :disabled="isEdit" v-model="dataForm.indexThresholdStatus"
          >指标值低于
          <InputNumber
            v-model.number="dataForm.indexThreshold"
            :max="100"
            :min="1"
            placeholder="请输入达标值"
            :disabled="isEdit"
            class="page-input"
          ></InputNumber>
          % &nbsp;&nbsp;单独通报</Checkbox
        >
        <Checkbox :disabled="isEdit" v-model="dataForm.indexRankStatus"
          >排名上升最快的地市单独通报（如果没有变动，或者有多个地区排名变动相同，则不单独通报）</Checkbox
        >
        <Checkbox :disabled="isEdit" v-model="dataForm.indexUnstandardStatus">有指标不达标地市单独通报</Checkbox>
      </div>
      <div class="assessment-result">
        <div><span class="title">#考核月份时间区间#</span>，公安视频图像数据治理考核工作正常推进。</div>
        <div>
          整体来看，本月共考核<span class="title">#指标数#</span>项指标，其中<span class="title"
            >#行政区划（多个行政区划用顿号隔开）#</span
          >全部达标。
          <span v-if="dataForm.indexThresholdStatus">
            <InputNumber
              v-model.number="dataForm.indexUnstandardNum"
              :min="1"
              placeholder="请输入达标值"
              :disabled="isEdit"
              class="page-input"
            ></InputNumber>
            &nbsp;项及以上不达标有<span class="title">#行政区划（多个行政区划用顿号隔开）#</span>，希望加强治理。</span
          >
          <span v-if="dataForm.indexRankStatus">
            另外，<span class="title">#行政区划（多个行政区划用顿号隔开）#</span>有若干指标低于<span class="title"
              >#设置的通报值#</span
            >，要加速整改。
            <span class="title">#行政区划（多个行政区划用顿号隔开）#</span>本月考核进步最大，较上月排名提升了<span
              class="title"
              >X</span
            >名。
          </span>
        </div>
        <div v-if="dataForm.indexUnstandardStatus">
          <div>以下为有指标不达标的地市：</div>
          <div>
            <div>
              <span class="title">地市1</span>：有<span class="title">3</span>个指标考核不达标，其中<span class="title"
                >XXXX</span
              >指标考核值最低，为<span class="title">45.87%</span>；
            </div>
            <div>
              <span class="title">地市2</span>：有<span class="title">3</span>个指标考核不达标，其中<span class="title"
                >XXXX</span
              >指标考核值最低，为<span class="title">45.87%</span>；
            </div>
            <div class="title">...</div>
            <div>
              <span class="title">地市N</span>：有<span class="title">3</span>个指标考核不达标，其中<span class="title"
                >XXXX</span
              >指标考核值最低，为<span class="title">45.87%</span>；
            </div>
          </div>
        </div>
      </div>
      <div class="text-textarea">
        <Input
          :disabled="isEdit"
          v-model="dataForm.customContent"
          :maxlength="600"
          show-word-limit
          type="textarea"
          :rows="5"
          placeholder="  填写其他自定义内容"
        />
      </div>
      <div class="breadcrumb-container"><span class="box vt-middle"></span><span class="ml-sm">通报内容： </span></div>
      <div class="page-change">
        <Checkbox :disabled="isEdit" v-model="dataForm.examRankStatus">考核排行（需运行考核任务）</Checkbox>
        <Checkbox :disabled="isEdit" v-model="dataForm.examResultStatus">考核结果</Checkbox>
        <Checkbox :disabled="isEdit" v-model="dataForm.evaluationResultStatus">检测结果</Checkbox>
        <Checkbox :disabled="isEdit" v-model="dataForm.exceptionDownStatus">异常明细下载</Checkbox>
      </div>
      <div class="breadcrumb-container"><span class="box vt-middle"></span><span class="ml-sm">通报时间： </span></div>
      <div>
        每月&nbsp;&nbsp;
        <span>
          <Select
            :disabled="isEdit"
            placeholder="请选择"
            clearable
            v-model="dataForm.planDay"
            class="mr-sm w100"
            transfer
          >
            <Option v-for="(item, index) in planMonth" :key="index" :value="item.plan">{{ item.value }}</Option>
          </Select>
        </span>
        <span class="width-picker">
          <TimePicker
            :disabled="isEdit"
            type="time"
            format="HH:mm"
            transfer
            placeholder="请选择"
            :value="dataForm.planTime"
            @on-change="(time) => handleChange(time)"
          >
          </TimePicker>
        </span>
      </div>
      <div class="breadcrumb-container">
        <span class="box vt-middle"></span><span class="ml-sm">通报数据来源： </span>
      </div>
      <div>
        <Select
          :disabled="isEdit"
          placeholder="请选择"
          clearable
          v-model="dataForm.notificationSource"
          class="mr-sm w330"
          transfer
        >
          <Option v-for="(item, index) in taskList" :key="index" :value="item.type">{{ item.name }}</Option>
        </Select>
      </div>
    </div>
  </div>
</template>
<script>
import notice from '@/config/api/notice';
export default {
  name: 'AssessmentResult',
  data() {
    return {
      isEdit: true,
      dataForm: {
        notificationSource: '',
        planTime: '00:00',
        planDay: '1',
      },
      planMonth: [
        { value: '1号', index: 0, plan: '1' },
        { value: '2号', index: 1, plan: '2' },
        { value: '3号', index: 2, plan: '3' },
        { value: '4号', index: 3, plan: '4' },
        { value: '5号', index: 4, plan: '5' },
        { value: '6号', index: 5, plan: '6' },
        { value: '7号', index: 6, plan: '7' },
        { value: '8号', index: 7, plan: '8' },
        { value: '9号', index: 8, plan: '9' },
        { value: '10号', index: 9, plan: '10' },
        { value: '11号', index: 10, plan: '11' },
        { value: '12号', index: 11, plan: '12' },
        { value: '13号', index: 12, plan: '13' },
        { value: '14号', index: 13, plan: '14' },
        { value: '15号', index: 14, plan: '15' },
        { value: '16号', index: 15, plan: '16' },
        { value: '17号', index: 16, plan: '17' },
        { value: '18号', index: 17, plan: '18' },
        { value: '19号', index: 18, plan: '19' },
        { value: '20号', index: 19, plan: '20' },
        { value: '21号', index: 20, plan: '21' },
        { value: '22号', index: 21, plan: '22' },
        { value: '23号', index: 22, plan: '23' },
        { value: '24号', index: 23, plan: '24' },
        { value: '25号', index: 24, plan: '25' },
        { value: '26号', index: 25, plan: '26' },
        { value: '27号', index: 26, plan: '27' },
        { value: '28号', index: 27, plan: '28' },
        { value: '29号', index: 28, plan: '29' },
        { value: '30号', index: 29, plan: '30' },
        { value: '31号', index: 230, plan: '31' },
      ],
      taskList: [
        { name: '视图数据全量检测任务', type: '1' },
        { name: '视图数据上报检测任务', type: '2' },
      ],
    };
  },
  created() {},
  mounted() {
    this.info();
  },
  methods: {
    info() {
      this.$http.get(notice.notificationView).then((res) => {
        const scheduleObj = res.data.data.scheduleObj ? JSON.parse(res.data.data.scheduleObj) : {};
        this.dataForm = {
          indexThresholdStatus: res.data.data.indexThresholdStatus === '1' || false,
          indexRankStatus: res.data.data.indexRankStatus === '1' || false,
          indexUnstandardStatus: res.data.data.indexUnstandardStatus === '1' || false,
          examRankStatus: res.data.data.examRankStatus === '1' || false,
          examResultStatus: res.data.data.examResultStatus === '1' || false,
          evaluationResultStatus: res.data.data.evaluationResultStatus === '1' || false,
          exceptionDownStatus: res.data.data.exceptionDownStatus === '1' || false,
          customContent: res.data.data.customContent || '',
          indexThreshold: Number(res.data.data.indexThreshold) || 0,
          indexUnstandardNum: Number(res.data.data.indexUnstandardNum) || 0,
          notificationSource: res.data.data.notificationSource || '',
          planDay: scheduleObj.planDay || '1',
          planTime: '00:00',
          id: 1,
        };
        this.dataForm.planTime = scheduleObj.planTime || '';
      });
    },
    handleChange(time) {
      this.dataForm.planTime = time;
    },
    save() {
      if (!this.isEdit) {
        this.update();
      }
      this.isEdit = !this.isEdit;
      if (this.isEdit) return false;
    },
    update() {
      const params = {
        indexThresholdStatus: this.dataForm.indexThresholdStatus ? '1' : '0',
        indexRankStatus: this.dataForm.indexRankStatus ? '1' : '0',
        indexUnstandardStatus: this.dataForm.indexUnstandardStatus ? '1' : '0',
        examRankStatus: this.dataForm.examRankStatus ? '1' : '0',
        examResultStatus: this.dataForm.examResultStatus ? '1' : '0',
        evaluationResultStatus: this.dataForm.evaluationResultStatus ? '1' : '0',
        exceptionDownStatus: this.dataForm.exceptionDownStatus ? '1' : '0',
        customContent: this.dataForm.customContent || '',
        indexThreshold: String(this.dataForm.indexThreshold) || '0',
        indexUnstandardNum: String(this.dataForm.indexUnstandardNum) || '0',
        notificationSource: this.dataForm.notificationSource || '',
        scheduleObj: JSON.stringify({
          plan: '1',
          planDay: this.dataForm.planDay,
          planDayDesc: this.dataForm.planDay + '号',
          planDesc: '每月 ' + this.dataForm.planDay + '号 ' + this.dataForm.planTime,
          planTime: this.dataForm.planTime,
        }),
        id: 1,
      };
      this.$http
        .put(notice.configUpdate, params)
        .then(() => {})
        .catch(() => {
          this.info();
        });
    },
  },
};
</script>
<style lang="less" scoped>
.page-assessmentresult {
  overflow: auto !important;
  padding: 20px;
  color: var(--color-input);
  background: var(--bg-content);
  font-size: 14px;
  .conter-center {
    text-align: center;
    & > span {
      display: block;
      margin-bottom: 20px;
      font-weight: bold;
    }
  }
  .page-button {
    float: right;
    // display: inline-block;
  }
  .breadcrumb-container {
    margin-top: 20px;
    position: relative;
    display: inline-block;
    margin-bottom: 10px;
    .box {
      display: inline-block;
      width: 4px;
      height: 14px;
      background: var(--color-title);
    }
  }
  .assessment-result {
    color: var(--color-input);
    line-height: 26px;
    text-indent: 26px;
    border: 1px solid var(--border-input);
    padding: 20px;

    .title {
      color: var(--color-title);
      margin: 0 2px;
    }
  }
  .text-textarea {
    margin-top: 20px;
    /deep/ .ivu-input-word-count {
      background: transparent;
      padding-right: 8px;
    }
  }
  .page-change {
    margin-bottom: 20px;
    .ivu-checkbox-wrapper {
      margin-right: 30px;
    }
    .page-input {
      width: 100px;
      margin: 0 8px 0 8px;
    }
    /deep/ .ivu-checkbox {
      width: auto;
      & > span {
        margin-right: 10px;
      }
    }
  }
  .width-picker {
    .ivu-date-picker {
      width: 120px;
    }

    /deep/ input {
      text-align: center;
    }
  }
  .title {
    color: var(--color-title);
  }
}
.fs14 {
  font-size: 14px;
}
.fs12 {
  font-size: 12px;
}
.fs24 {
  font-size: 24px;
}
.mt0 {
  margin-top: 0px;
}
.w100 {
  width: 100px;
}
.w330 {
  width: 330px;
}
</style>
