<!--
    * @FileDescription: 由案到人
    * @Author: H
    * @Date: 2024/04/10
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-09 17:03:19
 -->
<template>
  <div class="case-to-person">
    <mapCustom ref="mapBase" mapType="casetoperson"></mapCustom>
    <leftBox
      ref="leftBoxRef"
      @dealPoliceAndCaseTrackData="dealPoliceAndCaseTrackData"
      @analysis="handleAnalysis"
      @back="handleLeftBack"
      @pointBox="handlePointBox"
    ></leftBox>
    <rightBox
      ref="rightBoxlist"
      v-if="rightShowList"
      @cancel="handleCancel"
      @trackLine="handleTrackLine"
      @openPositionAreaTheWindow="openPositionAreaTheWindow"
      :getCaseOrAlarmDetail="getCaseOrAlarmDetail"
    >
    </rightBox>
    <track-details
      ref="trackDetails"
      v-if="trackDetail"
      @goback="handleGoback"
      @trackLine="trackLine"
      @tracklineAll="tracklineAll"
      @openPositionTheWindow="openPositionTheWindow"
    ></track-details>
  </div>
</template>

<script>
import mapCustom from "../../components/map/index.vue";
import leftBox from "./components/leftBox.vue";
import rightBox from "./components/rightBox.vue";
import trackDetails from "./components/track-details.vue";
import faceOrVehicle from "../../components/map/map-windows/faceOrVehicle.vue";
import mapAlarmWindow from "@/views/model-market/components/map/map-windows/map-alarm-window.vue";
import mapDetailWindow from "@/views/model-market/components/map/map-windows/map-detail-window.vue";
import { queryCaseAnalyze } from "@/api/modelMarket";
import { mapMutations } from "vuex";

const policeAndCaseTrackLayerName = "policeAndCaseTrack";
const casetopersonLayerName = "casetoperson";
export default {
  name: "",
  components: {
    mapCustom,
    leftBox,
    rightBox,
    trackDetails,
  },
  data() {
    return {
      rightShowList: false,
      trackDetail: false,
      radius: "",
    };
  },
  watch: {},
  computed: {},
  async created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  mounted() {},
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    getCaseOrAlarmDetail(...props) {
      return this.$refs.leftBoxRef.getCaseOrAlarmDetail(...props);
    },
    openPositionTheWindow(item) {
      this.$refs.mapBase.openPositionTheWindow(
        item,
        this.getWindowOpts({ offsetSize: [0, -45] })
      );
    },
    getWindowComponent(info) {
      const { dataType, policeDataType } = info;
      if (["face", "vehicle"].includes(dataType)) {
        return faceOrVehicle;
      } else if (policeDataType) {
        return mapAlarmWindow;
      } else {
        return mapDetailWindow;
      }
    },
    getWindowOpts(opts = {}) {
      const that = this;
      return {
        setContentDom(info) {
          return that.getWindowComponent(info);
        },
        ...opts,
      };
    },
    // 警情案件撒点
    dealPoliceAndCaseTrackData(list, pointSwitch) {
      const traceSprinkles = list.map((item) => {
        return {
          ...item,
          traitImg: this.getMarketIcon(
            item.policeDataType == 1 ? "alarm" : "case"
          ),
          showIconBorder: false,
        };
      });
      this.creatTraceSprinkles(traceSprinkles, policeAndCaseTrackLayerName);
      this.handlePointBox(pointSwitch);
    },
    openPositionAreaTheWindow(item, action) {
      if (action !== "location") {
        this.resetMarkerPoint([
          casetopersonLayerName,
          `${casetopersonLayerName}Center`,
        ]);
        this.$refs.mapBase.pointScope(
          [item],
          this.radius,
          casetopersonLayerName,
          `${casetopersonLayerName}Center`
        );
      }
      this.openPositionTheWindow(item);
    },
    // 碰撞分析
    handleAnalysis(id, radius = 1000) {
      this.radius = radius;
      queryCaseAnalyze(id).then((res) => {
        if (res.data?.length) {
          this.rightShowList = true;
          this.$nextTick(() => {
            this.$refs.rightBoxlist.init(id, res.data);
          });
        } else {
          this.$Message.warning("无碰撞结果");
        }
      });
    },

    // 点位框
    handlePointBox(val) {
      this.$refs.mapBase.coverageShowOrHide(policeAndCaseTrackLayerName, val);
    },
    // 轨迹详情
    handleTrackLine(item) {
      this.trackDetail = true;
      this.$nextTick(() => {
        this.$refs.trackDetails.init(item);
      });
    },
    getMarketIcon(type) {
      return require(`@/assets/img/map/map-${type}.png`);
    },
    // 轨迹详情撒点
    tracklineAll(list, type = 1) {
      if (type === 1) {
        this.$refs.mapBase.sprinklePoint(
          list.map((item) => ({
            ...item,
            traitImg: item.traitImg || this.getMarketIcon(item.dataType),
            showIconBorder: !!item.traitImg,
          })),
          "crashPoint",
          {
            windowOpts: this.getWindowOpts(),
          }
        );
        this.$refs.mapBase.coverageShowOrHide("crashPoint", true);
      } else {
        this.resetMarkerPoint(["ligaturePoint"]);
      }
      const layer = this.$refs.mapBase.caseDetailsPoint(list, "ligaturePoint");
      layer.setZIndex(650);
      this.$refs.mapBase.coverageShowOrHide("ligaturePoint", type !== 1);
    },
    resetMarkerPoint(layerNames) {
      this.$refs.mapBase.resetMarkerPoint(layerNames);
    },
    // 创建撒点
    creatTraceSprinkles(list, layerName) {
      this.resetMarkerPoint([layerName]);
      list.length > 0 &&
        this.$refs.mapBase.sprinklePoint(list, layerName, {
          windowOpts: this.getWindowOpts(),
        });
    },
    handleLeftBack() {
      this.rightShowList = false;
      this.trackDetail = false;
      this.resetMarkerPoint([
        casetopersonLayerName,
        `${casetopersonLayerName}Center`,
        "crashPoint",
        "ligaturePoint",
      ]);
    },
    handleGoback(flag) {
      this.trackDetail = flag;
      this.resetMarkerPoint(["crashPoint", "ligaturePoint"]);
    },
    trackLine(value) {
      if (value) {
        // 连线撒点
        this.$refs.mapBase.coverageShowOrHide("ligaturePoint", true);
        this.$refs.mapBase.coverageShowOrHide("crashPoint", false);
      } else {
        this.$refs.mapBase.coverageShowOrHide("ligaturePoint", false);
        this.$refs.mapBase.coverageShowOrHide("crashPoint", true);
      }
    },
    // 右侧结果取消
    handleCancel() {
      this.rightShowList = false;
      this.$refs.leftBoxRef.handleBack(1);
    },
  },
};
</script>

<style lang="less" scoped>
.case-to-person {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
