<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      :default-page-data="pageData"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
    >
      <template #otherButton v-if="activeMode === 'device'">
        <div class="other-button ml-lg inline">
          <span class="font-active-color mr-sm pointer" v-if="statisticShow" @click="updateStatistics"
            >更新统计结果</span
          >
          <Button
            v-permission="{
              route: $route.name,
              permission: 'batchreview',
            }"
            type="primary"
            class="mr-sm"
            @click="handleBatchArtificialReview"
          >
            <i class="icon-font icon-rengongfujian font-white"></i> 批量复核
          </Button>
          <Button
            v-permission="{
              route: $route.name,
              permission: 'batchrecheck',
            }"
            type="primary"
            class="mr-sm"
            :disabled="isRecheck"
            @click="handleBatchRecheck"
          >
            <i class="icon-font icon-piliangfujian"></i>
            {{ isRecheck ? '复检中...' : '批量复检' }}
          </Button>
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-piliangshangbao"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template slot="qualified" slot-scope="{ row }">
        <Tag :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #phyStatus="{ row }">
        <span>
          {{ !row.phyStatus ? '--' : row.phyStatusText }}
        </span>
      </template>
      <template #videoStartTime="{ row }">
        <span>
          {{ !row.videoStartTime ? '--' : row.videoStartTime }}
        </span>
      </template>
      <template #reason="{ row }">
        <Tooltip :content="row.reason" transfer max-width="150">
          {{ row.reason }}
        </Tooltip>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
          class="mr-sm"
        ></ui-btn-tip>
        <ui-btn-tip
          icon="icon-chakanjietu"
          content="查看截图"
          :disabled="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
          @click.native="viewResult(row)"
          class="mr-sm"
        ></ui-btn-tip>
        <ui-btn-tip
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
          v-if="$route.name !== 'cascadelist'"
          icon="icon-rengongfujian"
          content="人工复核"
          class="vt-middle f-14 mr-sm"
          @click.native="clickArtificialReview(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="vt-middle f-18 mr-sm"
          icon="icon-tianjiabiaoqian"
          content="添加标签"
          @click.native="addTags(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          v-permission="{
            route: $route.name,
            permission: 'recheck',
          }"
          icon="icon-fujian"
          content="复检"
          class="vt-middle f-14"
          @click.native="clickArtificialRecheck(row)"
        ></ui-btn-tip>
      </template>
      <template #selectTabs>
        <ui-select-tabs
          class="ui-select-tabs mb-sm"
          :list="tagListGetter"
          @selectInfo="selectInfo"
          ref="uiSelectTabs"
        ></ui-select-tabs>
      </template>
    </Particular>
    <review-and-detail
      v-model="reviewDetailVisible"
      :active-index-item="activeIndexItem"
      :review-row-data="artificialRow"
      :page-data="pageData"
      :total-count="totalCount"
      :table-data="tableData"
      :review-visible="reviewVisible"
      :qualified-list="qualifiedList"
      :styles="reviewDetailStyles"
      :title="reviewDetailTitle"
      :custorm-params="reviewCustormParams"
      :is-screen-shot="isScreenShot"
      :search-parames="formData"
      @closeFn="updateTable"
    ></review-and-detail>
    <video-player
      :playDeviceCode="playDeviceCode"
      v-model="videoVisible"
      :video-loading="videoLoading"
      :video-url="videoUrl"
      @onCancel="videoUrl = ''"
    ></video-player>
    <!-- 添加标签 -->
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <recheck
      v-model="recheckVisible"
      :module-data="moduleData"
      :is-batch-recheck="isBatchRecheck"
      @handleUpdate="initAll()"
    ></recheck>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
    <!-- 批量复核 -->
    <bulk-artificial-review
      v-model="batchArtificialReviewShow"
      :qualified-list="qualifiedList"
      :title="reviewDetailTitle"
      :is-screen-and-video="true"
      @update="showRecountBtn"
    ></bulk-artificial-review>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { mapActions, mapGetters } from 'vuex';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import {
  tableColumns,
  iconStaticsList,
  normalFormData,
} from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoQualityPassRate/util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import taganalysis from '@/config/api/taganalysis';
import vedio from '@/config/api/vedio-threm';

// 【批量复检】按钮逻辑
import batchRecheckBtn from '../mixins/batchRecheckBtn';

export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch, batchRecheckBtn],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      customSearch: false,
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      chooseOne: {
        tagList: [],
      },
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
        online: '',
        normal: '',
        canPlay: '',
        tagIds: [],
      },
      formItemData: normalFormData,
      tableColumns: [],
      indexDetectionModeMap: Object.freeze({
        1: '在线状态',
        2: '完好状态',
        3: '可用状态',
        4: '联网质量',
        null: '',
      }),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      currentRow: {},
      checkPicture: false, //查看图片
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      // 1 - 合格、2 - 不合格 3 - 无法检测
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      cardList: [],
      statisticShow: false,
      activeMode: 'device',
      artificialVisible: false,
      artificialRow: {},
      videoUrl: '',
      playDeviceCode: '',
      videoVisible: false,
      videoLoading: false,
      statisticalList: {},
      recheckVisible: false,
      moduleData: {},
      isBatchRecheck: false,
      reviewDetailVisible: false,
      reviewVisible: false,
      qualifiedList: [
        { key: '视频质量正常', value: '1' },
        { key: '视频质量异常', value: '2' },
      ],
      reviewDetailTitle: '人工复核',
      reviewDetailStyles: {
        width: '4rem',
      },
      batchArtificialReviewShow: false,
      exportLoading: false,
      reviewCustormParams: {},
      isScreenShot: false,
    };
  },
  created() {
    this.getQualificationList();
    this.getTagList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    // 获取不合格原因下拉列表
    async getQualificationList() {
      try {
        let data = await this.MixinGetQualificationList();
        let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
        findErrorCodes.options = data.map((item) => {
          return { value: item.code, label: item.reason };
        });
      } catch (err) {
        console.log(err);
      }
    },
    selectInfo(infoList) {
      this.pageData.pageNum = 1;
      this.formData.tagIds = infoList.map((item) => item.id);
      this.getTableData();
    },
    initAll() {
      // 获取列表
      this.getTableData();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        this.statisticalList = data;
        this.tableColumns = tableColumns({
          statisticalList: this.statisticalList,
        });
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    handleOsdModalHide() {
      this.$refs.osdModalRef.hide();
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params, flag) {
      if (flag === 'reset') {
        this.$refs.uiSelectTabs.reset();
      }
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableData();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.getBatchRecheckState();
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        this.totalCount = data.total;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 人工复核
    artificialReview(row) {
      this.$refs.artificialReview.init();
      this.artificialRow = row;
    },
    // 查看截图
    viewResult(row) {
      if (!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot) return;
      this.artificialRow = row;
      this.reviewVisible = false;
      this.reviewDetailTitle = '查看截图';
      this.reviewDetailStyles.width = '6rem';
      this.reviewDetailVisible = true;
      this.isScreenShot = true;
    },
    //视频播放
    async clickRow(row) {
      try {
        this.videoLoading = true;
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      } finally {
        this.videoLoading = false;
      }
    },

    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
        this.$Message.success('更新成功');
        this.statisticShow = false;
      } catch (err) {
        console.log(err);
      }
    },
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    // 批量复检
    handleBatchArtificialReview() {
      this.moduleData = { ...this.activeIndexItem };
      this.reviewDetailTitle = '批量人工复核';
      this.batchArtificialReviewShow = true;
    },
    // 人工复核
    clickArtificialReview(row) {
      this.artificialRow = row;
      this.reviewVisible = true;
      this.reviewDetailTitle = '人工复核';
      this.reviewDetailStyles.width = '4rem';
      this.reviewCustormParams = {
        taskIndexId: row.taskIndexId,
        deviceId: row.deviceId,
        indexType: this.activeIndexItem.indexType,
      };
      this.reviewDetailVisible = true;
      this.isScreenShot = false;
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    // 导出
    onExport: function () {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    clickArtificialRecheck(row) {
      this.moduleData = { deviceId: row.deviceId, ...this.activeIndexItem };
      this.isBatchRecheck = false;
      this.recheckVisible = true;
    },
    handleBatchRecheck() {
      // isRecheck - 混入js
      if (this.isRecheck) return;
      this.moduleData = { ...this.activeIndexItem };
      this.isBatchRecheck = true;
      this.recheckVisible = true;
    },
    // isReview：复核过      closeIndex：人工复核弹框关闭时 -设备 所在总表中的索引
    updateTable({ isReview, closeIndex }) {
      let { pageNum, pageSize } = this.pageData;
      let newPage = Math.floor(closeIndex / pageSize) + 1;
      this.pageData.pageNum = newPage;
      if (isReview) {
        this.showRecountBtn();
        this.getTableData();
      } else if (newPage !== pageNum) {
        this.getTableData();
      }
    },
  },
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
      tagListGetter: 'governanceevaluation/tagListGetter',
    }),
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        cardList: this.cardList,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    VideoPlayer: require('@/views/governanceevaluation/evaluationoResult/components/video-player.vue').default,
    recheck: require('../components/recheck/index.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    reviewAndDetail: require('../components/review-and-detail').default,
    exportData: require('../components/export-data').default,
    BulkArtificialReview: require('../components/bulk-artificial-review/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
</style>
