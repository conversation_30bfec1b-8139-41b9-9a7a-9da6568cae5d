<template>
  <div class="information-ranking">
    <div class="ranking-title">
      <title-content :title="rankTitle"></title-content>
    </div>
    <div class="ranking-list">
      <ul>
        <li v-for="(item, index) in rankData" :key="index">
          <div class="content-firstly">
            <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
            <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
            <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
            <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
              item.rank
            }}</span>
          </div>
          <Tooltip class="content-second" transfer :content="item.regionName">
            <div>
              <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
              <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
              <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
              <span v-if="item.rank == 1">{{ item.regionName }}</span>
              <span v-if="item.rank == 2">{{ item.regionName }}</span>
              <span v-if="item.rank == 3">{{ item.regionName }}</span>
              <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                item.regionName
              }}</span>
            </div>
          </Tooltip>
          <div class="content-thirdly">
            <span class="thirdly">{{ item.standardsValue }}</span>
          </div>

          <!-- <div class="content-fourthly">
            <i
              class="icon-font icon-shangsheng color-sheng f-16"
              v-if="item.rankType === 'RISE'"
            ></i>
            <i
              class="icon-font icon-shangsheng color-sheng f-16"
              v-if="item.rankType === 'SAME'"
            ></i>
            <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
            <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{
              item.rankRise || 0
            }}</span>
            <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
              item.rankRise || 0
            }}</span>
            <span class="plus color-sheng ml-md" v-else>{{
              item.rankRise == null ? 0 : item.rankRise
            }}</span>
          </div> -->
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'rank',
  components: {
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
  },
  props: {
    rankTitle: {
      //required: true,
      default: '下级排行',
    },
    rankData: {
      required: true,
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.information-ranking {
  width: 350px;
  background: var(--bg-sub-content);
  height: 100%;
  padding: 10px;
  .ranking-title {
    height: 30px;
    text-align: center;
    .title-center .conter-center {
      padding: 0 !important;
      width: 100%;
    }
  }
  .ranking-list {
    width: 100%;
    height: calc(100% - 30px);
    overflow-y: auto;
    display: block !important;
    ul {
      width: 100%;
      height: 100%;
      li {
        display: flex;
        padding-top: 15px;
        align-items: center;
        .content-fourthly {
          display: inline-flex;
          font-size: 14px;
          position: relative;
        }
        div {
          display: flex;
          align-items: center;
          font-size: 14px;
          position: relative;
        }

        .content-firstly {
          margin-left: 10px;
        }
        .content-thirdly {
          justify-content: center;
          flex: 1;
        }
        .content-fourthly {
          justify-content: center;
          flex: 1;
        }
        .content-second {
          color: #fff;
          justify-content: center;
          margin-left: 10px;
          img {
            vertical-align: middle;
          }
          span {
            width: 175px;
            padding-left: 10px;
            display: inline-block;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .rankText {
            margin-left: 20px;
          }
        }

        .bg_color {
          min-width: 21px;
          min-height: 21px;
          text-align: center;
          font-weight: bold;
          color: #fff;
          font-size: 14px;
        }
        .firstly1 {
          background-color: #f1b700;
        }
        .firstly2 {
          background-color: #eb981b;
        }
        .firstly3 {
          background-color: #ae5b0a;
        }
        .firstly4 {
          background-color: var(--color-primary);
        }

        .thirdly {
          overflow: hidden;
          color: var(--color-primary);
        }
        .color-sheng {
          color: #0e8f0e;
          font-size: 14px;
        }
        .color-jiang {
          color: #bc3c19;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
