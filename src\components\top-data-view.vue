<template>
  <div class="charts-container">
    <div v-for="(item, index) in dataList" :key="index + item.title" :class="{'charts-item':true,[`bg-${index % 5}`]: true}">
      <!-- 不传icon，展示默认图标 -->
      <!-- <span v-if="!item.icon" class="iconfont icon-exceptionlibrary f-32 icon-2"></span>-->
      <span class="font-content">
        <i :class="item.icon" class="iconfont"></i>
      </span>
      <div class="number-wrapper">
        <countTo :start-val="0" :end-val="item.value" class="num"></countTo>
        <h2 class="title">{{ item.title }}</h2>
      </div>
    </div>
  </div>
</template>
<script>
import countTo from 'vue-count-to'

export default {
  components: { countTo },
  props: {
    dataList: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {}
  },
  methods: {
    commaNumber (num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0
    }
  }
}
</script>
<style lang="less" scoped>
.charts-container {
  width: 100%;
  margin-bottom: 10px;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  background: #071B39;
  border-radius: 4px;
  justify-content: space-between;
  padding: 20px;
  height: 140px;
  min-height: 140px;

  .charts-item {
    flex-shrink: 0;
    width: 19.2%;
    height: 100px;
    display: flex;
    border-radius: 6px;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0px 2px 4px 0px #010A1A;
    position: relative;
    background: #1F3FE5;

    &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url("../assets/img/bg-img/bg_tag.png") no-repeat right top;
      background-size: auto 100%;
    }

    .number-wrapper {
      margin-left: 20px;
      color: #fff;
      position: relative;
      z-index: 10;
      .title {
        font-size: 14px;
      }

      .num {
        font-size: 28px;
        letter-spacing: 3px;
      }
    }
  }

  .font-content {
    width: 52px;
    height: 52px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .iconfont {
      font-size: 28px;
      color: #fff;
    }
  }

  .bg-0 {
    background: linear-gradient(180deg, #1ED6DE 0%, #127590 100%);
  }

  .bg-1 {
    background: linear-gradient(180deg, #64AAF6 0%, #602AD1 100%);
  }

  .bg-2 {
    background: linear-gradient(180deg, #5D9FF4 0%, #1F3FE5 100%);
  }

  .bg-3 {
    background: linear-gradient(180deg, #A68B40 0%, #916135 100%);
  }

  .bg-4 {
    background: linear-gradient(180deg, #D119C0 0%, #6C179C 100%);
  }
}
</style>
