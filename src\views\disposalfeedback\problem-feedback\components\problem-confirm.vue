<template>
  <ui-modal
    v-model="visible"
    title="问题确认"
    width="738px"
    :class-name="isBatch ? 'batch-height' : ''"
    @onCancel="onCancel"
  >
    <div v-ui-loading="{ loading: isLoading }" :class="['problem-confirm-main', isBatch ? 'batch-height' : '']">
      <div v-if="!isBatch" class="feedback-box">
        <text-title label="问题反馈信息" class="mb-md"></text-title>
        <detail-row label="问题编号：" :content="showObj.code || '-'"></detail-row>
        <detail-row label="数据类型：" :content="showObj.dataTypeText || '-'"></detail-row>
        <detail-row label="异常类型：" :content="showObj.causeCodesText?.join(',') || '-'"></detail-row>
        <detail-row
          v-for="item in formContent"
          :key="item.key"
          :label="`${item.title}：`"
          :content="item.value || '-'"
        ></detail-row>
        <detail-row label="问题描述：" :content="showObj.issueDetail"></detail-row>
        <detail-row label="反馈图片：" v-if="showObj.issueImages?.length" :imgList="showObj.issueImages"></detail-row>
        <detail-row label="提交人：" labelAlign="right" :content="showObj.name || '-'"></detail-row>
        <detail-row
          label="警号："
          labelAlign="right"
          :content="showObj.police || showObj.anonymousPolice || '-'"
        ></detail-row>
        <detail-row label="联系电话：" :content="showObj.phone || showObj.anonymousPhone || '-'"></detail-row>
        <detail-row :rowClass="'mb-lg'" label="提交时间：" :content="showObj.submitTime || '-'"></detail-row>
      </div>
      <div v-if="!isBatch && showObj.status != 1" class="dealing-box mt-lg">
        <text-title label="问题处理信息" class="mb-md"></text-title>
        <detail-row label="处理结果：">
          <span :style="{ color: handleResultStatus[showObj.status]?.color }">{{
            handleResultStatus[showObj.status]?.text || '-'
          }}</span>
        </detail-row>
        <detail-row label="处理说明：" :content="showObj.handleDetail || '-'"></detail-row>
        <detail-row label="处理图片：" v-if="showObj.handleImages?.length" :imgList="showObj.handleImages"></detail-row>
        <detail-row label="处理人：" labelAlign="right" :content="showObj.handleUserName || '-'"></detail-row>
        <detail-row label="处理时间：" :content="showObj.handleTime || '-'"></detail-row>
      </div>
      <detail-row v-if="isBatch" :label="`共 ${problemTotal} 条问题需要确认`"></detail-row>
      <text-title v-else label="确认问题状态" class="mt-md mb-md"></text-title>
      <Form ref="confirmForm" :model="confirmForm" :label-width="60">
        <FormItem label="状态：" required prop="effective">
          <RadioGroup v-model="confirmForm.effective">
            <Radio :label="effectiveStatus[1].value">有效</Radio>
            <Radio :label="effectiveStatus[2].value">无效</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="备注：" prop="effectiveDetail">
          <Input
            v-model="confirmForm.effectiveDetail"
            type="textarea"
            :autosize="{ minRows: 3 }"
            placeholder="请输入备注信息"
          ></Input>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="primary" v-show="!isBatch && !preOrNextShowCtrlObj.hidePre" @click="preAndNextReview('pre')"
        >上一条</Button
      >
      <Button
        class="plr-30"
        type="primary"
        :disabled="(isBatch && !problemTotal) || (!isBatch && showObj.status != 1)"
        :loading="confirmLoading"
        @click="handleEffective"
        >确 认</Button
      >
      <Button type="primary" v-show="!isBatch && !preOrNextShowCtrlObj.hideNext" @click="preAndNextReview('next')"
        >下一条</Button
      >
    </template>
  </ui-modal>
</template>
<script>
import feedbackApi from '@/config/api/feedback';
import { EFFECTIVE_STATUS, HANDLE_RESULT_STATUS } from '../util/enum.js';
export default {
  name: '',
  data() {
    return {
      visible: false,
      confirmForm: {
        effective: 1,
        effectiveDetail: '',
      },
      effectiveStatus: EFFECTIVE_STATUS,
      handleResultStatus: HANDLE_RESULT_STATUS,
      problemTotal: 0,
      isLoading: false,
      showObj: false,
      confirmLoading: false,
      problemListRes: {},
    };
  },
  components: {
    TextTitle: require('./text-title.vue').default,
    DetailRow: require('./detail-row.vue').default,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    tableRowObj: {
      type: Object,
    },
    isBatch: {
      type: Boolean,
      default: false,
    },
    isCheckAll: {
      type: Boolean,
      default: false,
    },
    selectData: {
      type: Array,
      default() {
        return [];
      },
    },
    searchParams: {
      type: Object,
      default() {
        return {};
      },
    },
    dataPageRes: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  watch: {
    value: {
      handler(val) {
        if (!val) {
          //清空工单表单对象
          this.confirmForm = {
            effective: 1,
            effectiveDetail: '',
          };
        } else {
          this.isBatch ? this.getHandleEffectiveCount() : this.getInfoDeatail();
          this.problemListRes = this.$util.common.deepCopy(this.dataPageRes);
        }
        this.visible = val;
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('changeModalVisible', val);
    },
  },
  computed: {
    formContent() {
      if (typeof this.showObj.formContent === 'string') {
        return JSON.parse(this.showObj.formContent);
      } else {
        return this.showObj.formContent;
      }
    },
    preOrNextShowCtrlObj() {
      let showObjIndex = this.problemListRes.entities.findIndex((item) => item.id == this.showObj.id);
      return {
        hidePre: this.problemListRes.firstPage && showObjIndex == 0 ? true : false,
        hideNext:
          this.problemListRes.lastPage && showObjIndex == this.problemListRes.entities.length - 1 ? true : false,
      };
    },
  },
  methods: {
    onCancel() {
      this.visible = false;
      //非批量操作关闭弹窗更新列表
      if (!this.isBatch) {
        this.$emit('update');
      }
    },
    //单条获取详情
    async getInfoDeatail(inId) {
      // 若传入id用传入的id，没有则默认用当前id
      let params = {
        ids: [inId ? inId : this.tableRowObj.id],
      };
      try {
        this.isLoading = true;
        let { data } = await this.$http.post(feedbackApi.feedbackGetList, params);
        this.showObj = data.data[0] || this.tableRowObj;
      } catch (err) {
        console.log(err);
      } finally {
        this.isLoading = false;
      }
    },
    //批量 可确定的设备数量
    async getHandleEffectiveCount() {
      this.dataPageRes;
      let params = {
        markAllForm: this.isCheckAll
          ? {
              ...this.searchParams,
              loginType: 1,
              pageNumber: this.dataPageRes.pageNumber,
              pageSize: this.dataPageRes.pageSize,
            }
          : {},
        markAll: this.isCheckAll,
        ids: this.isCheckAll ? [] : this.selectData.map((item) => item.id),
      };
      try {
        this.isLoading = true;
        let { data } = await this.$http.post(feedbackApi.feedbackHandleEffectiveCount, params);
        this.problemTotal = data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.isLoading = false;
      }
    },
    //重置表单
    resetForm() {
      this.confirmForm.effective = 1;
      this.confirmForm.effectiveDetail = '';
    },
    //确认
    async handleEffective() {
      let params = {
        ...this.confirmForm,
      };
      if (this.isBatch) {
        (params.markAllForm = this.isCheckAll
          ? {
              ...this.searchParams,
              loginType: 1,
              pageNumber: this.dataPageRes.pageNumber,
              pageSize: this.dataPageRes.pageSize,
            }
          : {}),
          (params.ids = this.isCheckAll ? [] : this.selectData.map((item) => item.id));
        params.markAll = this.isCheckAll;
      } else {
        params.ids = [this.showObj.id];
        params.markAll = false;
      }
      try {
        this.confirmLoading = true;
        await this.$http.post(feedbackApi.feedbackHandleEffective, params);
        this.resetForm();
        this.$Message.success('成功');
      } catch (err) {
        console.log(err);
      } finally {
        this.confirmLoading = false;
      }
      //批量操作关闭弹窗
      if (this.isBatch) {
        this.visible = false;
        this.$emit('update');
      }
    },
    //上一个 下一个
    async preAndNextReview(val) {
      const isPreBool = val == 'pre' ? true : false;
      const { firstPage, lastPage, pageNumber, entities } = this.problemListRes;
      const showObjIndex = entities.findIndex((item) => item.id == this.showObj.id);
      let preId = '';
      if (showObjIndex == -1) {
        return;
      }
      //1.1 第一页并且当前对象为第一个，或最后一页并且当前对象为最后一个，不能操作
      if (
        (isPreBool && firstPage && showObjIndex == 0) ||
        (!isPreBool && lastPage && showObjIndex == entities.length - 1)
      ) {
        return;
      }
      //2.1 向上请求翻页： 非首页且当前对象为第一个，请求向上翻页，并自动对应请求后的最后一个
      if (isPreBool && !firstPage && showObjIndex == 0) {
        this.isLoading = true;
        let newPageNum = pageNumber - 1;
        await this.getPageData(newPageNum);
        preId = this.problemListRes.entities[entities.length - 1].id;
        await this.getInfoDeatail(preId);
        return;
      }
      //2.2 向下请求翻页：非末页且当前对象为最后个，请求向下翻页，并自动对应请求后的第一个
      if (!isPreBool && !lastPage && showObjIndex == entities.length - 1) {
        this.isLoading = true;
        let newPageNum = pageNumber + 1;
        await this.getPageData(newPageNum);
        preId = this.problemListRes.entities[0].id;
        await this.getInfoDeatail(preId);
        return;
      }
      //2.3 默认翻页
      preId = isPreBool ? entities[showObjIndex - 1].id : entities[showObjIndex + 1].id;
      await this.getInfoDeatail(preId);
    },
    //获取反馈列表
    async getPageData(page) {
      const params = {
        ...this.searchParams,
        pageNumber: page,
        pageSize: this.problemListRes.pageSize,
        loginType: 1, //登陆类型 1用户登录 2手机号登录 3警号登录
        status: 1, //确认未处理状态
      };
      try {
        let { data } = await this.$http.post(feedbackApi.feedbackList, params);
        this.problemListRes = data.data;
        await this.$nextTick();
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20px 26px 0 50px;
}
.batch-height {
  height: 250px !important;
}
.problem-confirm-main {
  padding-right: 20px;
  margin-bottom: 40px;
  .feedback-box,
  .confirm-box {
    border-bottom: 1px solid var(--devider-line);
  }
  .dealing-box {
    padding-bottom: 12px;
    border-bottom: 1px solid var(--devider-line);
  }
  @{_deep} .ivu-form .ivu-form-item {
    margin-bottom: 15px;
  }
  @{_deep} .ivu-form .ivu-form-item-label {
    padding-left: 0;
    padding-right: 0;
  }
}
</style>
