/*
 * @Author: zhengmingming <EMAIL>
 * @Date: 2025-03-31 17:57:54
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-28 09:00:18
 * @FilePath: \icbd-view\src\api\monographic\base.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/libs/request";
import { monographic } from "../Microservice.js";

export const juvenile = "/juvenile";
export const emphasis = "/emphasis";
export const community = "/community";
export const driving = "/driving";
export const compus = "/compus";
export const place = "/place";

export const routeMapDomain = {
  3005: juvenile,
  3006: emphasis,
  3007: community,
  3010: place,
};


export const getRouteDomain = () => {
  const appName =
    BASE_URL?.replace(/^\/[a-zA-Z0-9]+\-/, "/") ||
    routeMapDomain?.[process.env.VUE_APP_CODE || applicationCode] ||
    "";
  return Object.values(routeMapDomain).includes(appName) ? appName : false;
};

export const getTargetObjParamkeyMap = () => {
  const domain = getRouteDomain();
  if (domain)
    return `MONOGRAPHIC_${domain?.replace("/", "")}_CONFIG`.toUpperCase();
  return "";
};

// 查询当前子应用的所有任务id
export function getAllTaskIds() {
  return request({
    url: monographic + getRouteDomain() + `/compare/task/getAllTaskIds`,
    method: "get",
  });
}

// 查询人员案件信息
export function getPersonCase(idCardNo) {
  return request({
    url: monographic + `/person/archives/getPersonCase/${idCardNo}`,
    method: "get",
  });
}

// 查询人员通联信息
export function getPersonCommunication(idCardNo) {
  return request({
    url: monographic + `/person/archives/getPersonCommunication/${idCardNo}`,
    method: "get",
  });
}

// 查询人员警情信息
export function getPersonIncidents(idCardNo) {
  return request({
    url: monographic + `/person/archives/getPersonIncidents/${idCardNo}`,
    method: "get",
  });
}

// 人员档案 管控报警查询
export function getRelationshipCard(idCardNo) {
  return request({
    url:
      monographic +
      getRouteDomain() +
      `/alarm/relationshipCard?archiveNo=${idCardNo}`,
    method: "get",
  });
}
