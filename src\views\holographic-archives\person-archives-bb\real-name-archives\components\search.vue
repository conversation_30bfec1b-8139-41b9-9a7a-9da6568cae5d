<!--
    * @FileDescription: 人员档案 - 实名档案-搜索
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="search">
        <Form ref="form" :inline="true" :model="queryParam" :class="visible ? 'advanced-search-show' : ''">
            <div class="general-search">
                <FormItem label="" prop="name">
                    <Input placeholder="请输入姓名" v-model="queryParam.name" clearable/>
                </FormItem>
                <FormItem label="" prop="idcardNo">
                    <Input placeholder="请输入身份证号码" clearable v-model="queryParam.idcardNo" maxlength="50">
                    </Input>
                </FormItem>
                <FormItem>
                    <div class="advanced-search-text" @click.stop="advancedSearchHandle($event)">
                        <img src="@/assets/img/down-circle-icon.png" alt />
                        <span class="primary">{{ visible ? '普通检索' : searchText === '高级检索' ? '高级检索' : '更多条件' }}</span>
                    </div>
                </FormItem>
            </div>
            <div class="advanced-search" @click="($event) => $event.stopPropagation()">
                <div class="other-search">
                    <div class="other-search-top card-border-color none-color">
                        <FormItem label="置信来源:"  prop="source" clearable>
                            <Select v-model="queryParam.source" placeholder="请选择" transfer>
                                <Option v-for="(item, index) in algorithmVendors" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
                            </Select>
                        </FormItem>
                        <FormItem label="手机号:" prop="phoneNo">
                            <Input placeholder="请输入手机号" v-model="queryParam.phoneNo" clearable/>
                        </FormItem>
                        <FormItem label="性别:" prop="sex">
                            <ui-tag-select @input="input" :value="queryParam.sex" ref="tagSelect">
                                <ui-tag-select-option v-for="(item, $index) in genderList" :key="$index" :name="item.dataKey">
                                    {{ item.dataValue }}
                                </ui-tag-select-option>
                            </ui-tag-select>
                        </FormItem> 
                        <FormItem label="年龄段:" prop="ageStart">
                            <el-input-number v-model="queryParam.ageStart" size="small" :min="1" :max="100" controls-position="right" class="mx-4" placeholder="请输入" />
						    <span class="ml-sm mr-sm"> - </span>
                            <el-input-number v-model="queryParam.ageEnd" size="small" :min="Number(queryParam.ageStart)" :max="100" controls-position="right" class="mx-4" placeholder="请输入" />
                        </FormItem>
                    </div>
                </div>
            </div>
        </Form>
        <slot name='statistics'>
            <div>
                <Button type="primary" @click="startSearch" class='mr-20'>查询</Button>
                <Button @click="resetHandle">重置</Button>
            </div>
        </slot>
    </div>
</template>
<script>
import { queryDataByKeyTypes } from '@/api/user';
export default {
    props: {
        //检索类型
        searchText: {
            type: String,
            default: ''
        },
        queryParam: {
            type: Object,
            default: () => {
                return {}
            }
        }
    },
    data() {
        return{
            formData: {
                name: '',
                idcardNo: '',
                sex: '',
                source: '',
                phoneNo: '',
                ageStart: '',
                ageEnd: '',
            },
            visible: false,
            algorithmVendors: []
        }
    },
    created() {
        
        this.getDictDataPageList();
    },
    methods: {
        
        // 查询
        startSearch() {
            this.visible = false;
            this.$emit('searchForm')
        },
        resetHandle() {

        },
        // 高级搜索切换
        advancedSearchHandle($event) {
            $event.stopPropagation()
            if (this.visible) {
                this.visible = false
            } else {
                this.visible = true
            }
        },
        // 性别选择
        input(val) {
            this.queryParam.sex = val
        },
        //所属类型字典数据
        getDictDataPageList() {
            queryDataByKeyTypes(["iafg_algorithm_vendor"]).then(res => {
                this.algorithmVendors = res.data[0]['iafg_algorithm_vendor'];
            })
        },
    }
}
</script>
<style lang="less" scoped>
.search {
    position: relative;
    .general-search {
        .advanced-search-text {
            height: 34px;
            display: flex;
            align-items: center;
            cursor: pointer;
            img {
                width: 16px;
                height: 16px;
                margin-right: 10px;
                transform: rotate(180deg);
                transition: transform 0.2s;
            }
        }
    }
    .advanced-search {
        display: flex;
        align-items: center;
        position: absolute;
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        margin-top: 1px;
        z-index: 10;
        max-height: 0px;
        transition: max-height 0.3s;
        overflow: hidden;
        .upload-input-list {
            padding: 10px 0;
            display: flex;
            /deep/.ivu-upload {
                margin-right: 10px;
            }
        }
        .other-search {
            display: flex;
            flex: 1;
            padding: 10px 0 0 10px;
            box-sizing: border-box;
            flex-direction: column;
            .other-search-top {
                display: flex;
                border-bottom: 1px dashed #fff;
                margin-top:10px;
                .perceive-data {
                    width: 380px;
                }
            }
            .ivu-form-item {
                margin-bottom: 10px;
            }
            .other-search-bottom {
                width: 100%;
                display: flex;
                justify-content: space-between;
                padding-top: 10px;
                box-sizing: border-box;
                align-items: flex-end;
                .slider-content {
                height: 34px;
                }

            }
        }
    }

    .advanced-search-show {
        .advanced-search {
        max-height: 400px;
        transition: max-height 0.5s;
        }
        .advanced-search-text {
        /deep/img {
            transform: rotate(0deg);
            transition: transform 0.2s;
        }
        }
    }
    /deep/.ivu-select-item-selected,
    .ivu-select-item-selected:hover {
        background-color: #fff !important;
        color: #2c86f8 !important;
    }
    .none-color{
        border-color: transparent !important;
    }
}
</style>
