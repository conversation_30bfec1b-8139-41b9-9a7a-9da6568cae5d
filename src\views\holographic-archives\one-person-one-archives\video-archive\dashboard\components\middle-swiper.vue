<template>
  <div class="middle-swiper-button">
    <div class="middle-swiper-container">
      <div class="swiper-slide-content" v-if="relationship.capture">
        <div class="item-header">
          <h2 class="title"><i class="icon icon_zhuapai"></i>抓拍记录</h2>
          <span class="number color-warning" v-if="relationship.capture.num">{{
            relationship.capture.num
          }}</span>
        </div>
        <div class="info-content">
          <div v-if="relationship.capture.data">
            <p class="info-item">
              <i class="iconfont icon-time"></i
              >{{ relationship.capture.data.captureTime }}
            </p>
            <p class="info-item">
              <i class="iconfont icon-location"></i
              >{{ relationship.capture.data.address }}
            </p>
          </div>
          <p v-else class="no-content">
            <i class="iconfont icon-zanwushuju"></i>暂无数据
          </p>
        </div>
      </div>
      <!-- <div class="swiper-slide-content" v-if="type =='people'">
      <div class="item-header">
        <h2 class="title"><i class="icon icon_anjian"></i>管控报警</h2>
        <span class="number color-error" v-if="relationship.alarmInfo">{{ relationship.alarmInfo.total }}</span>
      </div> -->
      <!-- <div class="info-content " :class="{'has-hover': relationship.alarmInfo.total}"> -->
      <!-- <div class="info-content">
        <div v-if="relationship.alarmInfo">
          <p class="info-item"><i class="iconfont icon-time"></i>{{ relationship.alarmInfo.alarmTime }}</p>
          <p class="info-item ellipsis" v-show-tips><i class="iconfont icon-case"></i>{{ relationship.alarmInfo.detailAddress }}</p> -->
      <!-- <p class="info-item"><i class="iconfont icon-location"></i>{{ relationship.alarmInfo.absTime }}</p> -->
      <!--  </div>
        <p v-else class="no-content"><i class="iconfont icon-zanwushuju"></i>暂无数据</p>
      </div>
    </div> -->
      <div class="swiper-slide-content" v-if="relationship.law">
        <div class="item-header">
          <h2 class="title"><i class="icon icon_anjian"></i>关联案件</h2>
          <span class="number color-error" v-if="relationship.law.num">{{
            relationship.law.num
          }}</span>
        </div>
        <div
          class="info-content"
          :class="{ 'has-hover': relationship.law.data }"
        >
          <div v-if="relationship.law.data">
            <p class="info-item">
              <i class="iconfont icon-case"></i>{{ relationship.law.data.jyaq }}
            </p>
            <p class="info-item">
              <i class="iconfont icon-time"></i
              >{{ relationship.law.data.sgfssj }}
            </p>
            <p class="info-item">
              <i class="iconfont icon-location"></i
              >{{ relationship.law.data.sgfsdd }}
            </p>
          </div>
          <p v-else class="no-content">
            <i class="iconfont icon-zanwushuju"></i>暂无数据
          </p>
        </div>
      </div>
      <div class="swiper-slide-content" v-if="relationship.alarm">
        <div class="item-header">
          <h2 class="title"><i class="icon icon_anjian"></i>管控报警</h2>
          <span class="number color-error" v-if="relationship.alarm.num">{{
            relationship.alarm?.num
          }}</span>
        </div>
        <div
          class="info-content"
          :class="{ 'has-hover': relationship.alarm.data }"
        >
          <div v-if="relationship.alarm.data">
            <p class="info-item">
              <i class="iconfont icon-time"></i
              >{{ relationship.alarm.data.sgfssj }}
            </p>
            <p class="info-item">
              <i class="iconfont icon-location"></i
              >{{ relationship.law.data.sgfsdd }}
            </p>
          </div>
          <p v-else class="no-content">
            <i class="iconfont icon-zanwushuju"></i>暂无数据
          </p>
        </div>
      </div>
      <div class="swiper-slide-content" v-if="relationship.place">
        <div class="item-header">
          <h2 class="title"><i class="icon icon_changqudi"></i>常去地</h2>
          <!-- <span class="text color-warning">晨曦科技园</span> -->
        </div>
        <div class="info-content">
          <div v-if="relationship.place.data">
            <p class="info-item">
              <i class="iconfont icon-camera"></i>抓拍
              {{ relationship.place.num }} 次
            </p>
            <p class="info-item">
              <i class="iconfont icon-location"></i
              >{{ relationship.place.data.address }}
            </p>
          </div>
          <p v-else class="no-content">
            <i class="iconfont icon-zanwushuju"></i>暂无数据
          </p>
        </div>
      </div>
      <!-- <div class="swiper-slide-content" v-if="type =='video'">
      <div class="item-header">
        <h2 class="title"><i class="icon icon_anjian"></i>管控报警</h2>
        <span class="number color-error" v-if="relationship.alarmInfo.total">{{ relationship.alarmInfo.total || 0 }}</span>
      </div> -->
      <!-- <div class="info-content " :class="{'has-hover': relationship.alarmInfo.total}"> -->
      <!-- <div class="info-content">
        <div v-if="relationship.alarmInfo.total">
          <p class="info-item"><i class="iconfont icon-time"></i>{{ relationship.alarmInfo.alarmTime }}</p>
          <p class="info-item"><i class="iconfont icon-case"></i>{{ relationship.alarmInfo.detailAddress }}</p> -->
      <!-- <p class="info-item"><i class="iconfont icon-location"></i>{{ relationship.alarmInfo.absTime }}</p> -->
      <!-- </div>
        <p v-else class="no-content"><i class="iconfont icon-zanwushuju"></i>暂无数据</p>
      </div>
    </div> -->
      <!-- <div class="swiper-slide-content" v-if="relationship.control">
      <div class="item-header">
        <h2 class="title"><i class="icon icon_baojing"></i>管控报警</h2>
        <span class="number color-error"></span>
      </div>
      <div class="info-content">
        <div v-if="relationship.control.data">
          <p class="info-item"><i class="iconfont icon-time"></i>2020-03-17 17:39:12</p>
          <p class="info-item"><i class="iconfont icon-location"></i>天津天津市南开区人民路780号</p>
        </div>
        <p class="no-content"><i class="iconfont icon-zanwushuju"></i>暂无数据</p>
      </div>
    </div> -->
      <div class="swiper-slide-content" v-if="relationship.car">
        <div class="item-header">
          <h2 class="title"><i class="icon icon_cheliang"></i>驾乘车辆</h2>
        </div>
        <div class="info-content">
          <p class="no-content">
            <i class="iconfont icon-zanwushuju"></i>暂无数据
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
export default {
  components: { swiper, swiperSlide },
  props: {
    // 卡片标题
    title: {
      type: String,
      default: "",
    },
    // 卡片信息
    relationship: {
      type: Object,
      default: () => {},
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "people",
    },
  },
  data() {
    return {
      swiperResize: true,
      swiperOption: {
        slidesPerView: 3,
        slidesPerColumn: 2,
        spaceBetween: 20,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      },
    };
  },
  computed: {
    swiper() {
      return this.$refs.mySwiper.swiper;
    },
  },
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.middle-swiper-button {
  display: flex;
  flex: 1;
  align-items: center;
}
.middle-swiper-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  grid-gap: 30px;
}
/deep/ .swiper-slide-content {
  width: 220px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  height: 104px;
  .item-header {
    display: flex;
    align-items: center;
    font-weight: bold;
    border-bottom: 1px solid #d3d7de;
    padding: 2px 6px;
    font-size: 16px;
    height: 42px;
    .title {
      display: flex;
      align-items: center;
      margin-right: 10px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;
      font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
      .icon {
        width: 30px;
        height: 30px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
        &.icon_anjian {
          background-image: url("~@/assets/img/archives/icons/icon_anjian.png");
        }
        &.icon_baojing {
          background-image: url("~@/assets/img/archives/icons/icon_baojing.png");
        }
        &.icon_changqudi {
          background-image: url("~@/assets/img/archives/icons/icon_changqudi.png");
        }
        &.icon_cheliang {
          background-image: url("~@/assets/img/archives/icons/icon_cheliang.png");
        }
        &.icon_guanxi {
          background-image: url("~@/assets/img/archives/icons/icon_guanxi.png");
        }
        &.icon_tongxing {
          background-image: url("~@/assets/img/archives/icons/icon_tongxing.png");
        }
        &.icon_yichang {
          background-image: url("~@/assets/img/archives/icons/icon_yichang.png");
        }
        &.icon_zhuapai {
          background-image: url("~@/assets/img/archives/icons/icon_zhuapai.png");
        }
      }
    }
    .text,
    .number {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .number {
      font-size: 24px;
    }
  }
  .info-content {
    padding: 10px;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    overflow: hidden;
    border-radius: 4px;
    height: 64px;
    position: relative;
    &.has-hover {
      &:after {
        content: "";
        position: absolute;
        bottom: -6px;
        right: -6px;
        width: 0;
        height: 0;
        border-bottom: 8px solid transparent;
        border-top: 8px solid #d8d8d8;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        transform-origin: center center;
        transform: rotate(-45deg);
      }
      &:hover {
        height: auto;
        &:after {
          transform: rotate(135deg);
          bottom: 6px;
          right: 6px;
        }
      }
    }
    .info-item {
      width: 100%;
      // display: flex;
      background: #fff;
      align-items: center;
      line-height: 25px;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      .iconfont {
        margin-right: 4px;
        font-size: 12px;
      }
    }
    &.img-content {
      display: flex;
      justify-content: space-between;
      .empty {
        width: 44px;
      }
      .img-item {
        width: 44px;
        height: 44px;
        overflow: hidden;
        position: relative;
        border-radius: 4px;
        border: 1px solid #d3d7de;
        img {
          width: 100%;
          height: 100%;
        }
        .badge {
          position: absolute;
          width: 21px;
          height: 21px;
          background: #2c86f8;
          border-radius: 4px;
          left: -3px;
          top: -3px;
          color: #fff;
          font-size: 12px;
          line-height: 21px;
          text-align: center;
          transform: scale(0.8);
        }
        p {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 13px;
          background: rgba(0, 0, 0, 0.8);
          text-align: center;
          line-height: 13px;
          span {
            color: #fff;
            font-size: 12px;
            float: left;
            width: 100%;
            transform: scale(0.8);
          }
        }
      }
    }
  }
  .no-content {
    width: 100%;
    padding-top: 10px;
    text-align: center;
    font-size: 14px;
    color: #d3d7de;
    display: flex;
    align-items: center;
    justify-content: center;
    .iconfont {
      margin-right: 10px;
      font-size: 14px;
    }
  }
}
</style>
