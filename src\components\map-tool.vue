<template>
  <div>
    <ul class="index-map-tool">
      <template v-for="(item, index) in toolMap">
        <li
          :title="item.title"
          :class="{ active: toolSelectdIndex === index }"
          :key="index"
          v-if="loadTool(item)"
          @click="
            item.fun();
            toolSelectdIndex = index;
          "
        >
          <i class="icon-font f-20" :class="item.icon"></i>
        </li>
      </template>
      <slot name="addTool"></slot>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'map-tool',
  props: {
    // 需要哪些默认操作传入数组
    mapTool: {
      type: Array,
      default: () => {
        return ['move', 'rectangle', 'circle', 'polygon', 'clear'];
      },
    },
  },
  data() {
    return {
      toolSelectdIndex: 0,
      toolMap: [
        {
          title: '移动',
          icon: 'icon-yidong1',
          fun: this.cancelDraw,
          value: 'move',
        },
        {
          title: '矩形框选',
          icon: 'icon-zhengfangxing',
          fun: this.selectRect,
          value: 'rectangle',
        },
        {
          title: '圆形框选',
          icon: 'icon-yuanxing',
          fun: this.selectCircle,
          value: 'circle',
        },
        {
          title: '多边形框选',
          icon: 'icon-duobianxing',
          fun: this.selectPolygon,
          value: 'polygon',
        },
        {
          title: '清除',
          icon: 'icon-xiangpica',
          fun: this.clearDraw,
          value: 'clear',
        },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    loadTool(item) {
      return this.mapTool.findIndex((row) => row === item.value) !== -1;
    },
    cancelDraw() {
      this.$emit('cancelDraw');
    },
    selectRect() {
      this.$emit('selectRect');
    },
    selectCircle() {
      this.$emit('selectCircle');
    },
    selectPolygon() {
      this.$emit('selectPolygon');
    },
    clearDraw() {
      this.$emit('clearDraw');
    },
  },
  watch: {},
  computed: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .index-map-tool {
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
    li {
      &:hover {
        .active;
      }
    }
    .active {
      background-color: var(--color-primary);
      .icon-font {
        color: #ffffff;
      }
    }
  }
}
.index-map-tool {
  position: absolute;
  bottom: 30px;
  left: 50%;
  margin-left: -115px;
  z-index: 725;
  // width: 230px;
  height: 40px;
  background: #1f4772;
  li {
    float: left;
    width: 45px;
    height: 100%;
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      background-color: #1e60a6;
    }
    i {
      background-repeat: no-repeat;
      display: block;
    }
  }
  .active {
    background-color: #1e60a6;
  }
}
</style>
