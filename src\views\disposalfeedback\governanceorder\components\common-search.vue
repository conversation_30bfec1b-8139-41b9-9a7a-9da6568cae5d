<template>
  <section class="common-search">
    <Form :label-width="89" inline :model="searchForm" :rules="ruleValidate">
      <FormItem label="工单编号" required>
        <Input class="width-slg" v-model="searchForm.workOrderNum" placeholder="系统自动生成" disabled></Input>
      </FormItem>
      <FormItem label="工单名称" required prop="workOrderName">
        <Input
          class="width-slg"
          v-model="searchForm.workOrderName"
          :placeholder="!isSelf ? '系统自动生成' : '请填写工单名称'"
          :disabled="isView || !isSelf"
        ></Input>
      </FormItem>
      <FormItem label="紧急程度" required>
        <Select class="width-slg" v-model="searchForm.workLevel" placeholder="请选择紧急程度" :disabled="isView">
          <Option
            :label="item.label"
            :value="item.value"
            v-for="(item, index) in workLevelOptions"
            :key="`${item.value}-${index}`"
          >
            <div class="inline work-level-box mr-sm vt-middle" :class="item.style"></div>
            <span>{{ item.label }}</span>
          </Option>
        </Select>
      </FormItem>
    </Form>
    <div class="normal-form">
      <Form :label-width="89" inline>
        <FormItem label="截止时间">
          <DatePicker
            class="picker-end width-slg"
            :value="searchForm.taskPlannedDate"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            :options="timeOption"
            placeholder="请选择任务截止时间"
            :disabled="isView"
            confirm
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchForm, 'taskPlannedDate')"
          >
          </DatePicker>
        </FormItem>
        <FormItem label="工单指派">
          <RadioGroup v-if="!isView" v-model="assignMode" @on-change="onChangeAssignMode">
            <Radio :label="0">自定义指派</Radio>
            <Radio :label="1" v-if="!isSelf && editViewAction.type === 'add'">自动指派</Radio>
          </RadioGroup>
          <div
            class="camera"
            v-if="assignMode === 0"
            :class="{ 'no-allow': isView }"
            @click="isView ? null : (peopleShow = true)"
          >
            <span class="font-blue" v-if="!searchForm.receiverName">请选择指派人</span>
            <span v-else class="font-blue">{{ searchForm.receiverName }}</span>
          </div>
        </FormItem>
      </Form>
      <div>
        <ui-label v-if="assignMode === 1" label="指派规则">
          <Select class="width-md" v-model="searchForm.assignMode" placeholder="请选择指派规则">
            <Option label="自动指派维护单位人" :value="1"> </Option>
            <Option label="自动指派组织机构人" :value="2"> </Option>
          </Select>
          <span class="ml-sm link-text-box pointer" v-if="searchForm.assignMode === 2" @click="onClickAutoAssignOrg"
            >配置各单位工单接收人</span
          >
        </ui-label>
      </div>
      <div class="assign-desc">
        <div v-if="searchForm.assignMode === 1">
          <div class="font-D66418">系统自动指派给设备运维单位联系人！</div>
          <div class="font-D66418">
            说明：设备类型的工单，根据设备的【维护单位联系人】字段，中文匹配用户表，自动指派给对应的用户。
          </div>
        </div>
        <div v-if="searchForm.assignMode === 2" class="font-D66418">
          备注：每个单位选择一个工单接收人，系统设备的所属单位自动将工单指派给工单接收人！
        </div>
      </div>
      <slot></slot>
      <Form :label-width="89" inline>
        <FormItem label="任务说明">
          <Input
            type="textarea"
            class="desc"
            v-model="searchForm.taskContent"
            placeholder="请填写任务说明"
            :disabled="isView"
            :rows="3"
            :maxlength="256"
          ></Input>
        </FormItem>
      </Form>
    </div>
    <select-people ref="selectpeople" title="请选择指派人" v-model="peopleShow" @putPeople="putPeople"></select-people>
    <select-receiver
      ref="selectpeopleConfig"
      v-model="selectPeopleConfigVisible"
      @on-select-receiver="OnSelectReceiver"
    >
    </select-receiver>
  </section>
</template>
<script>
import { workLevelOptions } from '../util/enum';

export default {
  props: {
    isView: {
      default: true,
    },
    isSelf: {
      default: false,
    },
    defaultForm: {
      default: () => {
        return {
          workOrderNum: null,
          workOrderName: '',
          taskPlannedDate: '',
          receiverName: '',
          taskContent: '',
          assignMode: 0,
        };
      },
    },
    editViewAction: {
      default: () => {
        return {
          type: '',
          row: {},
        };
      },
    },
  },
  data() {
    const validateArray = (rule, value, callback) => {
      if (!this.isSelf) {
        callback();
      } else {
        value ? callback() : callback('请填写工单名称');
      }
    };
    return {
      timeOption: {
        disabledDate: (date) => {
          let time = new Date().getTime() - 24 * 60 * 60 * 1000;
          if (time) {
            return date < time;
          }
          return false;
        },
      },
      ruleValidate: {
        // wordorderName: [
        //   { required: true, message: '请填写工单名称', trigger: 'blur' },
        // ],
        workOrderName: [{ validator: validateArray, trigger: 'blur' }],
      },
      searchForm: {
        workOrderNum: null,
        workOrderName: '',
        taskPlannedDate: '',
        receiverName: '',
        taskContent: '',
        assignMode: 0,
        assignList: [],
        workLevel: null,
      },
      cjdwList: [],
      assignMode: 0,
      peopleShow: false,
      selectPeopleConfigVisible: false,
    };
  },
  computed: {
    workLevelOptions() {
      return workLevelOptions;
    },
  },
  created() {},
  updated() {
    this.$emit('getSearchForm', this.searchForm);
  },
  methods: {
    radioChange(value) {
      this.$emit('radioChange', value === 1);
    },
    putPeople(item) {
      this.searchForm.receiverName = item.name;
      this.searchForm.receiverId = item.username;
      this.peopleShow = false;
    },
    onClickAutoAssignOrg() {
      this.selectPeopleConfigVisible = true;
    },
    onChangeAssignMode(val) {
      // 后端不存 自动指派
      if (val === 0) {
        this.searchForm.assignMode = 0;
      } else {
        this.searchForm.assignMode = 1;
      }
    },
    OnSelectReceiver(val) {
      this.searchForm.assignList = val.map((item) => {
        return {
          assignId: item.username,
          assignName: item.name,
          orgCode: item.orgCode,
          orgName: item.orgName,
          orgId: item.orgId,
        };
      });
    },
  },
  watch: {
    defaultForm(val) {
      Object.assign(this.searchForm, val);
    },
    isSelf() {
      this.assignMode = 0;
      this.searchForm.assignMode = 0;
    },
  },
  components: {
    SelectPeople: require('@/api-components/select-people/select-people.vue').default,
    selectReceiver: require('./select-receiver.vue').default,
  },
};
</script>
<style lang="less" scoped>
@import '~@/views/disposalfeedback/governanceorder/components/work-level-tag/index.less';

.work-level-box {
  height: 16px;
  width: 16px;
}
.common-search {
  @{_deep}.ivu-form-item {
    margin-bottom: 16px;
    .ivu-form-item-content {
      display: flex;
    }
  }
  .no-allow {
    cursor: not-allowed;
  }
  .width-slg {
    width: 400px;
  }
  .pl-16 {
    padding-left: 16px;
  }
  .ui-label {
    display: flex;
    margin-bottom: 16px;
  }
  .normal-form {
    //padding: 0 10px;
  }
  .section-p {
    display: flex;
  }
  .desc {
    width: 940px;
  }
  .flex-1 {
    flex: 1;
  }
  .assign-desc {
    margin-left: 64px;
  }
}
</style>
