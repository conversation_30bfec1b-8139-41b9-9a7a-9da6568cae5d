.login-bg {
  position: relative;
  height: 100vh;
  overflow: hidden;
  // height: 100%;
  // width: 100%;
  // background:url('~@/assets/img/login/light-login-bg.png') center/cover;
  .video-container{
    position: relative;
    height: 100vh;
    overflow: hidden;
  }
  // .video-container video,
  .video-container .poster img {
    z-index: 0;
    position: absolute;
  }
  .video-container .filter {
    z-index: 1;
    position: absolute;
  }
  .fillWidth{
    width: 100%;
  }

  .logo{
    position: absolute;
    top:46px;
    left: 0;
    color: #FFFFFF;
    width: 560px;
    padding-top: 19px;
    padding-bottom:7px;
    display: flex;
    padding-left: 23px;
    justify-content: flex-start;
    background: #248AFC;
    border-radius: 0px 45px 45px 0px;
    .logo-img{
      width:90px;
      height: 64px;
    }
    .logo-title{
      margin-left: 13px;
      line-height: 54px;
      font-size: 42px;
      font-family: 'Microsoft YaHei';
      font-weight: bold;
    }
  }
  // 外边框css 开始
  .login-wrapper {
    width: 1080px;
    height: 740px;
    padding-top: 50px;
    text-align: center;
    position: absolute;
    top:6%;
    right: 0;
    .main {
      width: 521px;
      height: 559px;
      padding: 38px 61px 0 61px;
      overflow: hidden;
      margin-left: 29%;
      margin-top: 2%;
      background: #FFFFFF;
      box-shadow: 0px 8px 27px 0px rgba(2, 10, 34, 0.15);
      border-radius: 10px;
      .login-text{
        font-size: 28px;
        font-family: 'Microsoft YaHei';
        font-weight: bold;
        color: #1E1E1E;
        line-height: 32px;
        margin-bottom: 40px;
      }
      .ui-tab {
        line-height: 34px;
        font-size: 14px;
        font-family: 'Microsoft YaHei';
        color: #1A1A1A;
        overflow: hidden;
        margin-left: -12px;
        cursor: pointer;
        margin-bottom: 16px;
        .list {
          overflow: hidden;
          float: left;
          .ivu-tabs-tab {
            float: left;
            padding: 0 15px;
            &:hover {
              color: #248AFC;
              font-weight: bold;
            }
          }
          .active {
            color: #248AFC;
            position: relative;
            font-weight: bold;
          }

          .active::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-50%);
            width: 46px;
            height: 3px;
            background-color: #248AFC;
          }
        }
      }
      .login-form {
       /deep/ .login-input {
          input {
            height: 50px;
            background: #F6F6F6;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
            opacity: 1;
            padding-left: 58px;
            border: 1px solid transparent;
            &::-webkit-input-placeholder {
              /* WebKit browsers */
              font-size: 16px;
              color: #D3D7DE;
              // color: rgba(255, 255, 255, 0.2);
            }
            &:focus {
              border-color: #4597FF;
              border-width: 1px;
            }
          }
          // .ivu-icon-ios-close-circle:before {
          //   font-family: iconfont;
          //   color: #red;
          //   content: "\F178";
          //   font-size: 16px;
          //
          .ivu-input-suffix {
            top: 10px;
            right: 4px;
            .ivu-icon{
              font-size: 26px;
            }
          }
          .ivu-input-prefix {
            left: 20px;
            top: 10px;
            .iconfont {
              font-size: 20px;
              color: #D3D7DE;
            }
          }
          .login-icon {
            line-height: 50px;
            font-size: 20px;
            // color: rgba(1, 255, 255, 0.5);
            margin-left: 5px;
          }

          .ivu-input-icon-clear {
            color: #D3D7DE;
            font-size: 26px;
            top: 10px;
            right: 4px;
          }
        }
        .btn {
          width: 400px;
          height: 50px;
          font-size: 20px;
          line-height: 50px;
          border-radius: 4px;
          color: #fff;
          font-weight: bold;
          cursor: pointer;
          margin-top: 22px;
        }
        .loginBtn {
          background: linear-gradient(90deg, #248AFC 0%, #4BA0FF 100%);
          box-shadow: 0px 6px 13px 0px rgba(0, 83, 175, 0.25);
        }
        .loginBtn:hover {
          box-shadow: 0px 6px 13px 0px rgba(0, 83, 175, 0.25);
          background:linear-gradient(180deg, #76B2FF 0%, #3A91FF 100%)
        }

        .loginBtn:active {
          box-shadow: 0px 6px 13px 0px rgba(0, 83, 175, 0.25);
          background:  linear-gradient(180deg, #4794F6 0%, #1A70DD 100%);
        }

        /deep/ .ivu-input-prefix {
          width: 20px;
          height: 20px;
          top: 15px;
          left: 15px;

          img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        /deep/ .ivu-input {
          padding-left: 48px;
        }

      }
      .bottom_kpi{
        text-align: right;
        color: #248AFC;
        height: 25px;
        line-height: 25px;
        i{
          font-size: 25px;
        }
        span{
          vertical-align: top;
          font-weight: bold;
          margin-left: 10px;
          font-size: 16px;
          font-family: 'Microsoft YaHei';
        }
      }
    }
  }
  .footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    background: #020A22;
    opacity: 0.4;
    padding: 10px 0;
    .login-logo {
      img {
        height: 46px;
      }
    }

    .line {
      width: 1px;
      height: 54px;
      background: #fff;
      margin: 0 20px;
    }
    .copyright {
      color: #D0E5FE;
      line-height: 20px;
      font-size: 12px;
    }
  }

}
