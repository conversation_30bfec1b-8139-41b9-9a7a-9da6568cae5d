.login-bg {
  position: relative;
  background: url('../../assets/img/login/bg.gif') no-repeat no-repeat center center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  overflow: hidden;
  .form-bg {
    width: 971px;
    height: 823px;
    margin-top: -100px;
    position: absolute;
    background: url('../../assets/img/login/form-bg.png') no-repeat;
    background-size: cover;
  }
  .ivu-input-wrapper {
    .ivu-input {
      background: transparent !important;
    }
    .ivu-input-group-prepend {
      width: 60px;
      background: transparent;
      background: rgba(28, 138, 255, 0.2);
      border: 1px solid rgba(102, 167, 255, 0.5);
      border-right: 0;
    }

    input {
      height: 40px;
      background: transparent;
      border: 1px solid rgba(102, 167, 255, 0.5);
      color: #ffffff;
    }
  }
  .login-text {
    text-align: center;
    line-height: 66px;
    font-weight: bold;
    font-size: 44px;
    position: relative;
    margin-top: -214px;
    .nav-logo {
      height: 52px;
      width: 48px;
      position: absolute;
      left: 16px;
      top: 5px;
    }
    .login-cn {
      background: linear-gradient(to top, #6d7889 0%, #ffffff 70%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      margin-left: 80px;
    }
  }
  // 外边框css 开始
  .login-wrapper {
    position: relative;
    overflow: hidden;
    margin-top: 30px;
    width: 541px;
    height: 309px;
    position: relative;
    background: url('../../assets/img/login/form.png') no-repeat;
    background-size: cover;
    .login-wrapper-border {
      position: absolute;
      width: 40px;
      height: 40px;
    }
    // 内边框css 结束
    .login-loading {
      position: absolute;
      background: url('../../assets/img/login/login-loading.gif') 100% 100% no-repeat no-repeat;
      z-index: 2;
      width: 100%;
      height: 100%;
      background-size: 100% 100%;
    }
    .login-container {
      position: absolute;
      width: 400px;
      top: 50%;
      left: 50%;
      margin-top: 20px;
      transform: translate(-50%, -50%);
      z-index: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;

      .login-form {
        width: 100%;
        @{_deep} .login-input {
          .ivu-input {
            height: 50px;
            background: rgba(5, 7, 50, 0.25);
            border: 1px solid #10457e !important;

            &::-webkit-input-placeholder {
              color: #56789c !important;
            }
            &:hover {
              background: rgba(29, 32, 136, 0.25);
            }
            &:focus {
              background: rgba(5, 7, 50, 0.25);
              &::-webkit-input-placeholder {
                color: #fff !important;
              }
            }
          }

          .ivu-input-suffix {
            .ivu-icon-ios-eye-off-outline:before {
              font-family: icon-font;
              content: '\e793';
              color: #56789c;
              font-size: 14px;
            }
            .ivu-icon-ios-eye-outline:before {
              font-family: icon-font;
              content: '\e792';
              font-size: 17px;
              color: #56789c;
            }
          }
          .ivu-icon-ios-close-circle:before {
            font-family: icon-font;
            color: #56789c;
            content: '\e791';
            font-size: 16px;
          }
          .login-icon {
            line-height: 50px;
            font-size: 20px;
            color: #27b9f3;
          }
          .ivu-icon {
            margin-right: 10px;
            line-height: 50px;
          }
          .ivu-input-prefix {
            width: 60px;
          }
          .ivu-input-with-prefix {
            padding-left: 60px;
          }
          input[type='text'],
          input[type='password'] {
            color: #ffffff;
          }
        }
        .sign-button {
          width: 192px;
          height: 56px;
          margin-left: 15px;
          float: left;
          font-size: 18px;
          margin-top: 20px;
          border: none;
          &:focus {
            box-shadow: none;
          }
          background: url('../../assets/img/login/btn.png') no-repeat;
          background-size: 100% 100%;
          &:hover {
            background: url('../../assets/img/login/btn-focus.png') no-repeat;
            background-size: 100% 100%;
          }
          &:active {
            background: url('../../assets/img/login/btn-active.png') no-repeat;
            background-size: 100% 100%;
          }
          &:first-child {
            margin-left: 0;
          }
        }
        @{_deep} .ivu-btn:hover {
          border: none !important;
          color: #ffffff !important;
        }
      }
    }
  }
  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 277px;
    .login-logo {
      img {
        height: 50px;
      }
    }
    .line {
      width: 1px;
      height: 45px;
      background: #15aeed;
      opacity: 0.5;
      margin: 0 10px;
    }
    .copyright {
      font-size: 14px;
      color: #15aeed;
      line-height: 20px;
    }
  }
}
