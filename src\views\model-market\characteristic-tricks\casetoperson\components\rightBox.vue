<!--
    * @FileDescription: 由案到人-数据展示
    * @Author: H
    * @Date: 2024/04/10
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-09 17:07:19
 -->
<template>
  <div class="rightBox line-title" :class="{ 'rightBox-pack': packUpDown }">
    <div class="title">
      <p>碰撞结果</p>
      <Icon type="ios-close" @click="handleCancel" />
    </div>
    <div class="box-content">
      <Spin fix v-if="loadingText" size="large">解析中...</Spin>
      <RecycleScroller
        class="scroller"
        :items="caseList"
        :prerender="10"
        key-field="id"
      >
        <template #default="{ item }">
          <div class="box-li">
            <div class="box-li-title" v-if="item.type">
              <i
                class="iconfont range"
                :class="item.type == 1 ? 'icon-jingqing' : 'icon-anjian'"
              ></i>
              <p
                class="case_particulars"
                :class="{
                  selected: selectedId === getSelectedId(item),
                }"
              >
                {{ item.type == 1 ? "警情" : "案件" }}:
                <span @click="openPositionTheWindow(item)">{{ item.bh }}</span>
              </p>
            </div>
            <div class="box-li-childer" v-else>
              <div class="child_title">
                <div class="child_person">
                  <i class="iconfont icon-renlian range"></i>
                  <p class="relevance-person">关联人员{{ item.index }}:</p>
                </div>
                <p
                  class="track_num"
                  @click="handleTrack(item.parentInfo, item)"
                >
                  <span>{{ item.count }}</span
                  >条轨迹
                </p>
              </div>
              <div class="crad">
                <UiListCard
                  type="people"
                  :data="item.person"
                  @archivesDetailHandle="archivesDetailHandle"
                  @on-archive="archivesDetailHandle"
                />
              </div>
            </div>
          </div>
        </template>
      </RecycleScroller>
      <div
        class="footer"
        :class="{ packArrow: packUpDown }"
        @click="handlePackup"
      >
        <img :src="packUrl" alt="" />
        <p>{{ packUpDown ? "展开" : "收起" }}</p>
      </div>
      <!-- <p class="loading" v-if="noMore && caseList.length != 0">没有更多了</p> -->
    </div>
  </div>
</template>

<script>
import { RecycleScroller } from "vue-virtual-scroller";
import UiListCard from "@/components/ui-list-card";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";

export default {
  name: "",
  props: {
    getCaseOrAlarmDetail: {
      type: Function,
      default: () => ({}),
    },
  },
  components: {
    UiListCard,
    RecycleScroller,
  },
  data() {
    return {
      caseList: [],
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      loadingText: false,
      noMore: false,
      packUpDown: false,
      taskId: "",
      selectedId: "",
      rem: 0,
    };
  },
  mounted() {
    this.rem =
      (document.documentElement.clientWidth || document.body.clientWidth) /
      1920;
  },
  methods: {
    // 初始化右侧数据
    init(id, initData) {
      this.taskId = id;
      const data = [];
      let index = 0;
      initData.forEach((element) => {
        const { persons, ...info } = element || [];
        data.push({
          id: index,
          size: 40 * this.rem,
          ...info,
        });
        index++;
        for (let i = 0; i < persons.length; i++) {
          const info2 = persons[i] || [];
          data.push({
            id: index,
            size: 250 * this.rem,
            index: i + 1,
            ...info2,
            parentInfo: { ...info },
          });
          index++;
        }
      });
      this.caseList = data;
      this.openPositionTheWindow(initData[0]);
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    // 详情
    handleTrack(item, ite) {
      let params = {
        bh: item.bh,
        idCard: ite.person.archiveNo,
        taskId: this.taskId,
        type: item.type,
      };
      this.openPositionTheWindow(item);
      this.$emit("trackLine", params);
    },
    openPositionTheWindow(item) {
      const selectedId = this.getSelectedId(item);
      let action = "pointScope";
      if (this.selectedId === selectedId) action = "location";
      this.selectedId = selectedId;
      const detail = this.getCaseOrAlarmDetail(item.type, item.bh);
      const data = {
        ...item,
        detail,
        policeDataType: item.type,
        geoPoint: detail.geoPoint,
      };
      this.$emit("openPositionAreaTheWindow", data, action);
    },
    getSelectedId({ type, bh }) {
      return `${type}-${bh}`;
    },
    handleCancel() {
      this.$emit("cancel");
    },
    archivesDetailHandle(item) {
      let query = {
        archiveNo: item.archiveNo,
        source: "people",
        initialArchiveNo: item.archiveNo,
      };
      const { href } = this.$router.resolve({
        name: "people-archive",
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../../components/style/index";

.rightBox {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 10px;
  background: #fff;
  height: calc(~"100% - 20px");
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  filter: blur(0px);
  transition: height 0.2s ease-out;

  .box-content {
    padding: 10px;
    padding-top: 0;
    height: calc(~"100% - 40px");
    .scroller {
      height: 100%;
      .box-li {
        height: 100%;
        background: #f9f9f9;

        .box-li-title {
          width: 240px;
          height: 28px;
          padding-left: 10px;
          margin-top: 10px;
          background: url("~@/assets/img/model/searchIcon/titleBg.png")
            no-repeat;
          display: flex;
          align-items: center;
          color: #ffffff;
          font-family: Microsoft YaHei, Microsoft YaHei;
          font-weight: 700;
          font-size: 14px;
          gap: 3px;
          .case_particulars {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            span {
              text-decoration-line: underline;
              cursor: pointer;
            }
            &.selected {
              span {
                color: #f29f4c;
              }
            }
          }
        }
        .box-li-case {
          font-weight: 700;
          font-size: 14px;
          display: flex;
          align-items: center;
          border-bottom: 1px dashed #d8d8d8;
          padding-bottom: 10px;
        }

        .box-li-childer {
          padding: 0 10px;
          padding-bottom: 9px;
          .child_title {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .child_person {
              display: flex;
              align-items: center;
              font-weight: 700;
              font-size: 14px;
              color: #3d3d3d;
              padding: 8px 0;
            }

            .track_num {
              font-weight: 400;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.6);
              cursor: pointer;

              span {
                color: #2c86f8;
              }
            }
          }
        }

        .icon-jingqing {
          color: #ea4a36;
        }

        .icon-anjian {
          color: #4696fc;
        }

        .icon-renlian {
          color: #2c86f8;
        }
      }
    }
  }

  .footer {
    // color: #000000;
    position: absolute;
    bottom: 0px;
    left: 50%;
    transform: translate(-50%, 0px);
    background: #fff;
    width: 96%;
  }
}

.rightBox-pack {
  height: 80px;
  transition: height 0.2s ease-out;
  overflow: hidden;
}
</style>
