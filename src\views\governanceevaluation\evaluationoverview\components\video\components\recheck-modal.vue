<template>
  <ui-modal class="recheck-modal" v-model="visible" title="设备复检" :styles="styles">
    <Form :label-width="120" ref="modalData" :model="formData" :rules="ruleCustom" inline>
      <FormItem label="复检方法">
        <Select v-model="formData.videoGenera" class="width-input">
          <Option v-for="item in recheckList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="算法">
        <Select v-model="formData.videoGeneral" class="width-input">
          <Option v-for="item in ocrList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
    </Form>
    <template #footer>
      <Button type="primary" class="plr-30">复 检</Button>
    </template>
  </ui-modal>
</template>

<script>
export default {
  name: 'recheck-modal',
  data() {
    return {
      formData: { user: '', password: '' },
      ocrList: [{ value: '1', label: '算法1' }],
      recheckList: [{ value: '1', label: '直连SDK' }],
      ruleCustom: {
        // captureDrop: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请输入抓拍数量突降比例',
        //     trigger: 'blur',
        //   },
        // ],
        // timeDelay: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请输入时间',
        //     trigger: 'blur',
        //   },
        // ],
        // imageNum: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请输入张数',
        //     trigger: 'blur',
        //   },
        // ],
        // timeFormat: [
        //   {
        //     required: true,
        //     message: '请选择单位',
        //     trigger: 'blur',
        //   },
        // ],
      },
      visible: false,
      styles: {
        width: '2rem',
      },
    };
  },
  methods: {
    init() {
      this.visible = true;
    },
    //   onCancel() {
    //     this.videoUrl = ''
    //     this.$http.post(video.stop + this.playDeviceCode)
    //   },
    //   async getVideoUrl(deviceId) {
    //     try {
    //       let data = { deviceId }
    //       let res = await this.$http.post(video.getplay, data)
    //       if (res.data.msg != '成功') {
    //         this.$Message.error(res.data.msg)
    //       }
    //       this.videoUrl = res.data.data.hls
    //     } catch (err) {
    //       console.log(err)
    //     }
    //   },
  },
  components: {},
};
</script>

<style></style>
