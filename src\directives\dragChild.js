let dragElement = null
export default function (Vue) {
  // 传进来的value为要减去的下面的值
  Vue.directive('dragChild', {
    bind (el, bind, vnode) {
      const dom = el
      dom.setAttribute('draggable', true)
      dom.setAttribute('id', 'drag' + Math.random())
      dom.ondrop = (e) => {
        e.preventDefault()
        e.stopPropagation()
        if (dragElement !== dom) {
          dom.parentNode.insertBefore(dragElement, dom)
        }
        dom.style.backgroundColor = 'transparent'
      }
      dom.ondragover = (e) => {
        e.preventDefault()
      }
      dom.ondragstart = (e) => {
        e.stopPropagation()
        dragElement = dom
        e.dataTransfer.setData('Text', dom.id)
      }
      dom.ondragend = (e) => {
        console.log('结束拖动')
      }
      dom.ondragenter = (e) => {

      }
      dom.ondragleave = (e) => {

      }
    },
    inserted (el, bind, vnode) {

    },
    update (el, bind, vnode) {

    },
    unbind (el, bind, vnode) {

    },
    runs (el, bind) {
    }
  })
}
