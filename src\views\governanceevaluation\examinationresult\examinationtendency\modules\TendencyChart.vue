<template>
  <div class="tendency-chart-box">
    <!-- 地市柱状图统计 -->
    <div class="mt-sm grap-content height-full">
      <Checkbox v-model="isSort" v-show="showEchartsData.length" @on-change="onChangeSort">按得分排序</Checkbox>
      <div class="echarts-box height-full relative">
        <div v-if="!showEchartsData.length" v-ui-loading="{ loading: echartsLoading }" class="no-chart-box no-data">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
        <template v-else>
          <draw-echarts
            :echart-option="echartsAttrs.options"
            :echart-style="echartsAttrs.style"
            class="charts"
            :echarts-loading="echartsLoading"
            @echartClick="echartClick"
          ></draw-echarts>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue';
import tendencyTooltip from '../components/tendency-tooltip.vue';
export default {
  name: 'tendencyChart',
  props: {
    echartsData: {
      type: Array,
      default: () => [],
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      echartsAttrs: {
        options: {},
        style: {
          width: '100%',
          height: '100%',
        },
        colors: [
          $var('--color-series-blue-1'),
          $var('--color-series-blue-2'),
          $var('--color-series-blue-3'),
          $var('--color-series-blue-4'),
          $var('--color-series-blue-5'),
          $var('--color-series-blue-6'),
          $var('--color-series-blue-7'),
          $var('--color-series-blue-8'),
          $var('--color-series-blue-9'),
          $var('--color-series-blue-10'),
          $var('--color-series-blue-11'),
          $var('--color-series-blue-12'),
        ],
      },
      showEchartsData: [],
      isSort: false,
      tooltipFormatter: (data) => {
        let childrenDatas = [];
        data.forEach((item) => {
          this.showEchartsData.forEach((echartsDataItem) => {
            if (item.axisValue == echartsDataItem.civilName) {
              childrenDatas = echartsDataItem.childList;
            }
          });
        });
        let taskTooltipShow = Vue.extend(tendencyTooltip);
        let colors = this.echartsAttrs.colors;
        let taskTooltipPointer = new taskTooltipShow({
          el: document.createElement('div'),
          data() {
            return {
              childrenDatas: childrenDatas,
              civilName: data[0].axisValue,
              colors,
            };
          },
        });
        return taskTooltipPointer.$el.outerHTML;
      },
    };
  },
  watch: {
    echartsData: {
      handler(val) {
        this.showEchartsData = this.$util.common.deepCopy(val);
        this.setEchartsOptions(this.showEchartsData);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 柱状统计图
    setEchartsOptions(list) {
      if (!list?.length) {
        return;
      }
      //处理数据
      let rawData = []; //原始数据
      let xNames = []; //x轴数据
      list.forEach((item) => {
        xNames.push(item.civilName);
        //内层
        if (item.childList.length) {
          let sortChildList = item.childList.sort((a, b) => a.month < b.month);
          sortChildList.forEach((child, childIndex) => {
            let childMonthDatas = list.map((civilItem) => {
              return civilItem.childList[childIndex]?.score || 0;
            });
            rawData.push(childMonthDatas);
          });
        }
      });
      let monthList = list[0].childList.map((item) => item.month);
      const series = monthList.map((month, index) => {
        return {
          name: month + '月',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barWidth: '30px',
          showSymbol: false,
          label: {
            show: false,
            position: 'top',
          },
          data: rawData[index],
        };
      });
      const opts = {
        xAxis: xNames,
        series,
        tooltipFormatter: this.tooltipFormatter,
        colors: this.echartsAttrs.colors,
      };
      this.echartsAttrs.options = this.$util.doEcharts.examinationTendencyChart(opts);
    },
    echartClick(params) {
      this.$emit('echartClick', params);
    },
    //点击排序
    onChangeSort(val) {
      let sortData = [...this.showEchartsData];
      if (val) {
        sortData.sort((a, b) => b.score - a.score);
      }
      this.setEchartsOptions(sortData);
    },
  },
  mounted() {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.tendency-chart-box {
  width: 100%;
  height: 100%;
  .no-chart-box {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}
</style>
