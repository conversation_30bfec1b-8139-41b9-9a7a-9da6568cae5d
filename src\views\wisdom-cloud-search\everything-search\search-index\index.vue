<!--
 * @Date: 2024-12-18 15:22:34
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-15 10:34:03
 * @FilePath: \icbd-view\src\views\wisdom-cloud-search\cloud-default-page\search-index\index.vue
-->
<template>
  <div class="container">
    <div class="wisdom-coloud-search-animation">
      <!-- 数字图 -->
      <video autoplay loop muted class="video">
        <source
          src="@/assets/img/wisdom-cloud-search/everything-search.mp4"
          type="video/mp4"
        />
        浏览器不支持 video 标签，建议升级浏览器。
      </video>
      <img
        class="digital-img"
        src="@/assets/img/wisdom-cloud-search/wisdom-cloud-search-bg.png"
        alt=""
      />
    </div>
    <div class="search-main" :class="{ 'search-main-padding': !showList }">
      <h2 class="title" v-show="showList">
        <img
          class="search-title-icon"
          src="@/assets/img/wisdom-cloud-search/everything-search-icon.png"
          alt=""
        />
        <p>万物智搜</p>
      </h2>
      <p class="information" v-show="showList">
        共有约
        <count-to
          :start-val="0"
          :end-val="countData"
          :duration="1000"
          class="num color-warning"
        ></count-to>
        条数据
      </p>
      <div
        class="search-content"
        :class="{ 'search-content-margin': !showList }"
      >
        <div class="input-content">
          <!-- <Button type="primary" class="search-btn" @click="startVideo"
            >语音输入</Button
          >
          <Button type="primary" class="search-btn" @click="endVideo"
            >语音关闭</Button
          > -->
          <Input
            v-model="keyWords"
            :placeholder="serchPlaceholder"
            :border="false"
            class="search-input"
          >
            <div class="input-suffix-box" slot="suffix">
              <div class="maikefeng" @click="openAudio">
                <i class="iconfont icon-mai_line"></i>
              </div>
              <SearchPicturesButton ref="SearchPicturesButton" />
              <Button
                type="primary"
                class="search-btn"
                slot="append"
                @click="toOrdinarySearchHandle"
                >搜索</Button
              >
            </div>
          </Input>
        </div>
        <div class="search-time">
          <div class="searchType" v-if="showList">
            <div class="searchType-title">搜索方式：</div>
            <RadioGroup v-model="searchType">
              <Radio
                v-for="item in searchTypeList"
                :key="item.key"
                :label="item.key"
                >{{ item.name }}</Radio
              >
            </RadioGroup>
          </div>
          <ui-quick-date
            ref="quickDateRef"
            v-model="dateType"
            @change="change"
          />
        </div>
      </div>
      <div class="box-content" v-show="!showList">
        <search-page-list ref="pageList"></search-page-list>
      </div>
    </div>
    <UiAudio ref="audioRef" @confirm="(val) => (keyWords = val)"></UiAudio>
  </div>
</template>
<script>
import CountTo from "vue-count-to";
import { mapMutations, mapGetters, mapActions } from "vuex";
import SearchPicturesButton from "@/components/search-pictures-button.vue";
import { totalCount } from "@/api/wisdom-cloud-search";
import searchPageList from "@/views/wisdom-cloud-search/cloud-default-page/search-index/components/searchList/index.vue";
import { myMixins } from "../components/mixin/index.js";

import UiAudio from "@/components/ui-audio/index.vue";
export default {
  name: "searchIndex",
  components: {
    CountTo,
    SearchPicturesButton,
    searchPageList,
    UiAudio,
  },
  data() {
    return {
      fixStyle: "",
      keyWords: "",
      countData: 0, // 首页统计总数
      dateType: 1,
      showList: true,
      startDate: "",
      endDate: "",
      searchType: 1,
      searchTypeList: [
        {
          key: 1,
          name: "自然语义",
          placeholder:
            "您可以输入任何您要搜索的内容，如：穿着蓝色衣服背黑包的人",
        },
        {
          key: 2,
          name: "关键词",
          placeholder: "请输入关键词检索，多个关键词请用空格隔开",
        },
      ],
      fristQuery: true,
    };
  },
  mixins: [myMixins], //全局的mixin
  activated() {
    this.$nextTick(() => {
      // 从工作台跳转 搜索文字或图片
      if (this.$route.query.type) {
        let params = this.$route.query;
        if (params.type == 1) {
          // 文字
          this.keyWords = this.$route.query.keyWord;
        }
      }
    });
  },
  mounted() {
    // 工作台【我的智搜】跳转过来
    if (this.$route.query.keyWord) {
      let query = this.$route.query;
      this.keyWords = query.keyWord;
      this.dateType = Number(query.dateType);
      this.startDate = query.startDate;
      this.endDate = query.endDate;
      if (this.dateType == 4) {
        this.$nextTick(() => {
          this.$refs.quickDateRef.setDefaultDate([
            this.startDate,
            this.endDate,
          ]);
        });
      }
      this.toOrdinarySearchHandle();
    } else {
      this.init();
      this.change(this.$refs.quickDateRef.getDate());
    }
    this.onResizeHandler();
    this.setLayoutNoPadding(true);
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false);
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      picData: "common/getWisdomCloudSearchData",
    }),
    serchPlaceholder() {
      return this.searchTypeList.find((item) => item.key === this.searchType)
        .placeholder;
    },
  },
  methods: {
    ...mapActions({
      addCloud: "common/addCloud",
    }),
    init() {
      totalCount().then((res) => {
        this.countData = res.data;
      });
    },
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    ...mapMutations("common", ["setWisdomCloudSearchData"]),
    // 添加当前信息放入vuex里面
    addStoreSearchPicture() {
      let query = {
        keyWords: this.keyWords,
        dateType: this.dateType,
        startDate: this.startDate,
        endDate: this.endDate,
        urlList: [],
      };
      this.setWisdomCloudSearchData(query);
    },
    goSearch() {
      this.showList = false;
      this.$refs.pageList.searchList({
        startDate: this.startDate,
        endDate: this.endDate,
        dateType: this.dateType,
        keyWords: this.keyWords,
      });
    },
    change(val) {
      this.startDate = val.startDate;
      this.endDate = val.endDate;
      // 当没有关键字时，切换日期只做为条件的更改，不触发搜索动作，当有了关键字时，再切换日期则触发搜索动作
      if (!this.keyWords || this.fristQuery) return;
      this.addHistorySearch();
    },

    /**
     * @description: 添加搜索记录
     */
    addHistorySearch() {
      this.goSearch();
      this.addStoreSearchPicture();
      this.addCloud();
    },
    toMultimodalAnalysisPage() {
      this.$router.push({
        path: "/multimodal-analysis/multimodal-analysis-lib",
        query: {
          keyWords: this.keyWords,
          dateType: this.dateType,
          startDate: this.dateType == 4 ? this.startDate : undefined,
          endDate: this.dateType == 4 ? this.endDate : undefined,
        },
      });
    },
    toOrdinarySearchHandle() {
      if (!this.keyWords) {
        this.$Message.warning(this.serchPlaceholder);
        return false;
      }
      if (this.searchType === this.searchTypeList[0].key) {
        this.toMultimodalAnalysisPage();
        return;
      }
      this.fristQuery = false;
      this.addHistorySearch();
    },
    onResizeHandler() {
      window.onresize = () => {
        const windowWidth = document.body.clientWidth;
        const windowHeight = document.body.clientHeight;
        const windowAspectRatio = windowHeight / windowWidth;
        let videoWidth;
        let videoHeight;
        if (windowAspectRatio < 0.5625) {
          videoWidth = windowWidth;
          videoHeight = videoWidth * 0.5625;
          this.fixStyle = {
            height: windowWidth * 0.5625 + "px",
            width: windowWidth + "px",
            "margin-bottom": (windowHeight - videoHeight) / 2 + "px",
            "margin-left": "initial",
            position: "absolute",
            top: "-60px",
          };
        } else {
          videoHeight = windowHeight;
          videoWidth = videoHeight / 0.5625;
          this.fixStyle = {
            height: windowHeight + "px",
            width: windowHeight / 0.5625 + "px",
            "margin-left": (windowWidth - videoWidth) / 2 + "px",
            "margin-bottom": "initial",
            position: "absolute",
            top: "-60px",
          };
        }
      };
      window.onresize();
    },
    openAudio() {
      this.$refs.audioRef.show();
    },
  },
};
</script>
<style lang="less" scoped>
.test {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
}
.container {
  flex: 1;
  position: relative;
  // background: #e8ebf4 linear-gradient(180deg, #e7ebf3 0%, #75b2ff 100%) no-repeat left bottom / 100% 60%;
  padding: 0;
  .wisdom-coloud-search-animation {
    width: 100%;
    height: 100%;
    // background: linear-gradient(180deg, #E7EBF3 0%, #2957B7 100%) no-repeat left bottom / 100% 60%;
    // background: url('~@/assets/img/wisdom-cloud-search/wisdom-cloud-search-bg.png') no-repeat left bottom / 100% 100%;
    position: relative;
    .digital-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.2;
      position: absolute;
      top: 0;
      left: 0;
    }
    .video {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .search-main {
    position: absolute;
    top: 0;
    left: 0;
    padding: 20px 10px 0;
    padding-top: 120px;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    z-index: 2;
    .title {
      text-align: center;
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 55px;
      color: rgb(0, 0, 0);
      background: url(~@/assets/img/wisdom-cloud-search/everything-search-title-bg.png);
      background-repeat: no-repeat;
      background-position: center center;
      img {
        // width: 344px;
        // height: auto;
        width: 60px;
        height: 60px;
        margin-right: 30px;
      }
      p {
        width: 245px;
        text-align: justify;
        text-align-last: justify;
      }
    }
    .information {
      position: relative;
      z-index: 2;
      text-align: center;
      font-size: 24px;
      margin-top: 25px;
      color: rgba(0, 0, 0, 0.9);
      .num {
        font-size: 44px;
        font-weight: 500;
        letter-spacing: -1px;
      }
    }
    .search-content {
      position: relative;
      width: 1000px;
      z-index: 2;
      margin: 0 auto;
      margin-top: 70px;
      // display: flex;
      // align-items: center;
      // justify-content: center;
      padding: 10px 20px;
      transition: margin-top 0.7s;
      background: #ffffff;
      box-shadow: 0 11px 28px 0 rgba(156, 186, 230, 0.2976);
      border-radius: 10px 10px 10px 10px;
      border: 1px solid #cdd5e0;
      .advance-search-btn {
        color: #2c86f8;
        font-size: 16px;
        background-color: transparent;
        padding: 0 30px;
        &:hover {
          color: #4597ff;
        }
        &:active {
          color: #1a74e7;
        }
      }
      .search-input {
        width: 100%;
        height: 36px;
        /deep/ .ivu-input {
          height: 100%;
          font-size: 16px;
          font-weight: 400;
          border: none;
          padding-left: 0;
          &:hover,
          &:focus {
            border-color: #9cbae6;
          }
        }
        /deep/ .ivu-input-suffix {
          width: auto;
        }
        .input-suffix-box {
          width: auto;
          display: flex;
          align-items: center;
          cursor: pointer;
          gap: 10px;
          .iconfont {
            font-size: 22px;
            color: #888;
            margin: 0 auto;
          }
        }
      }
      /deep/ .ivu-input-group-append {
        width: 110px;
        .ivu-btn {
          font-size: 20px;
        }
      }
      .input-content {
        position: relative;
        padding-bottom: 10px;
        .pic-box {
          position: absolute;
          width: 710px;
          height: 50px;
          top: 0px;
          z-index: 2;
          cursor: pointer;
        }
      }
    }
    .search-time {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1px solid #d8d8d8;
      padding-top: 10px;
      .searchType {
        display: flex;
        align-items: center;
        gap: 5px;
        .searchType-title {
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
    .box-content {
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
      border-radius: 4px 4px 4px 4px;
      flex: 1;
      margin-top: 20px;
      overflow-y: auto;
      margin-bottom: 10px;
      position: relative;
    }
  }
  .search-main-padding {
    padding-top: 0;
    .search-content-margin {
      margin-top: 20px;
      transition: margin-top 0.7s;
    }
  }
}
</style>
