<template>
    <div class="layout-box">
        <div class="back-box">
            <div class="box-hint">
                <div class="box-hint-icon" @click="handleback" >
                    <Icon type="ios-undo"/>
                </div>
                <span class="box-hint-name"> > 布局配置</span>
            </div>
        </div>
        <div class="layout-content">
            <div class="mst-left">
                <div class="hint">
                    <span><i class="iconfont icon-tishi"></i>请拖拽监视器到右侧虚线框内</span>
                </div>
                <ul class="monitor-box">
                    <li class="monitor-list" v-for="(item, index) in monitorList" :key="index">
                        <div class="parent-name">
                            <div class="header-box" @click="handleUnfold(index)">
                                <Icon type="md-add" v-if="unfoldList[index]"/>
                                <Icon type="md-remove" v-else/>
                            </div>
                            <span class="monitor-name-ip">{{ item.name }}({{ item.ip }})</span>
                        </div>
                        <div class="monitor-name-list" :class="{'monitor-name-list-not': !ite.draggable}" :draggable="ite.draggable" @dragstart="dragstart($event, item, ite)" v-for="(ite, ind) in item.screens" :key="ind" v-show="!unfoldList[index]">
                            {{ ite.id }}
                        </div>
                    </li>
                </ul>
            </div>
            <div class="mst-main">
                <div class="tsl-ly-oprators">
                    <p>请将监视器拖拽到和实际电视墙对应的位置</p>
                    <div class="operation-btn">
                        <select-cell ref="selectWell" @cellShow="handleCell" ></select-cell>
                        <Button type="primary" size="small" class="confirm-btn" @click="handleSave">保存</Button>
                        <Button size="small" @click="handleback">取消</Button>
                    </div>
                </div>
                <div class="layout-setting">
                    <div class="layout-main" :style="{'grid-template-columns': columns,
                    'grid-template-rows': rows}">
                        <div class="layout-item"
                            :class="{'layout-item-well': item.screenId}" 
                            @dragover="dragover($event, item, index)" 
                            @drop="drop($event, item, index)" 
                            v-for="(item, index) in layoutList" :key="index"
                        >
                            <div class="layout-monitor" v-if="item.screenId">
                                <span>{{ item.name }} - {{item.screenId}}</span>
                                <Icon type="md-close" @click="handleDeleMonitor(item, index)"></Icon>
                            </div>
                            <span class="layout-info">{{ item.row + '-' + item.col }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import selectCell from './select-cell.vue';
import { getDemodifier, updateLocation } from '@/api/config';
export default {
    components:{
        selectCell 
    },
    data() {
        return {
            unfoldList: [],
            layoutList: [
                { 'row': 1, 'col': 1 },
                { 'row': 1, 'col': 2 },
                { 'row': 2, 'col': 1 },
                { 'row': 2, 'col': 2 },
            ],
            rowCol:{row: 2, col: 2},
            columns: '50% 50%',
            rows: '50% 50%',
            tvWellId: '',
            monitorList: []
        }
    },
    methods: {
        queryMonitor(id) {
            this.tvWellId = id;
            let params = {
                videoWallId: id
            }
            getDemodifier(params)
            .then(res => {
                this.monitorList = res.data;
                this.unfoldList = Array.apply(null, { length: this.monitorList.length }).map(() => {
					return true;
				});
                if(res.data.length == 0) {
                    this.handleCell({row: 2, col: 2});
                }else if(res.data.length > 0) {
                    let colRow = res.data[0];
                    this.handleCell({row: colRow.rowNum, col: colRow.columnNum });
                    this.$refs.selectWell.init({row: colRow.rowNum, col: colRow.columnNum });
                    let layoutBox = [];
                    this.monitorList.forEach(item => {
                        item.screens.forEach(ite =>{
                            if(ite.location) {
                                ite.draggable = false;
                            }else{
                                ite.draggable = true;
                            }
                            layoutBox.push(
                                {   
                                    name: item.name,
                                    demodifierId: item.id,
                                    ...ite
                                },
                            )
                        })
                    })
                    this.layoutList.forEach((item, index) => {
                        layoutBox.forEach(ite => {
                            if(ite.location) {
                                let row = ite.location.split('-')[0];
                                let col = ite.location.split('-')[1];
                                if(item.row == row && item.col ==col){
                                    item.demodifierId = ite.demodifierId;
                                    item.screenId = ite.id;
                                    item.name = ite.name;
                                }
                            }
                        })
                    })
                }  
            })
        },
        dragstart(event, item, ite) {
            let ids = JSON.stringify({demodifierId: item.id, name: item.name, screenId: ite.id})
            event.dataTransfer.setData('monitor', ids )
        },
        drop(event, item, index) {
            let objId = event.dataTransfer.getData('monitor');
            let cellId = JSON.parse(objId);
            item.demodifierId = cellId.demodifierId;
            item.screenId = cellId.screenId;
            item.name = cellId.name;
            let screenId = this.layoutList.filter(item => item.screenId).map(item => {
                return item.demodifierId + '-'+ item.screenId;
            });
            this.monitorList.forEach(item => {
                item.screens.forEach(ite =>{
                    let dragId = item.id+ '-' + ite.id;
                    if(screenId.includes(dragId)){
                        ite.draggable = false;
                    } else {
                        ite.draggable = true;
                    }
                })
            })
            this.$forceUpdate();
        },
        dragover(event, item, index){
            event.preventDefault()
        },
        handleDeleMonitor(item, index) {
            let screenId = item.demodifierId + '-'+ item.screenId;
            this.monitorList.forEach(item => {
                item.screens.forEach(ite =>{
                    let dragId = item.id+ '-' + ite.id;
                    if(screenId == dragId){
                        ite.draggable = true;
                    }
                })
            })
            item.demodifierId = '';
            item.screenId = '';
            item.name = '';
            this.$forceUpdate();
        },
        handleSave(){
            let locationList = [];
            this.layoutList.forEach(item => {
                if(item.screenId ){
                    locationList.push({
                        'demodifierId': item.demodifierId ,
                        'screenId': item.screenId,
                        'location': item.row + '-' + item.col
                    })
                } 
            })
            if(locationList.length ==0) {
                this.$Message.warning('未配置监视器在电视墙位置，请拖拽配置')
                return
            }
            let params = {
                columnNum: this.rowCol.col,
                rowNum: this.rowCol.row,
                id: this.tvWellId,
                locationList: locationList
            };
            updateLocation(params)
            .then(res => {
                this.$Message.success('保存成功')
                this.$emit('back')
            })
        },
        handleCancel() {

        },
        handleUnfold(index) {
            this.$set(this.unfoldList, index, !this.unfoldList[index]);
        },
        handleCell(data) {
            let { row, col } = data;
            this.rowCol = {
                row, col
            };
            let total = row* col;
            this.layoutList = [];
            for(let i = 0; i < total; i++) {
                let rowNum = Math.ceil((i+1) / col);
                let colNum = ((i+1) % col) == 0 ? col : (i+1) % col;
                this.layoutList.push(
                    { 'row': rowNum, 'col': colNum }
                )
            }
            let columns = [];
            for(let i = 0; i < col; i++) {
                columns.push((100 / col) + '%')
            }
            let rows = [];
            for(let i = 0; i < row; i++) {
                rows.push((100 / row) + '%')
            }
            this.columns = columns.join(' ');
            this.rows = rows.join(' ');
            this.monitorList.forEach(item => {
                item.screens.forEach(ite =>{
                    ite.draggable = true;
                })
            })
        },
        handleback() {
            this.$emit('back')
        },
    }
}
</script>
<style lang="less" scoped>
.layout-box{
    width: 100%;
    height: 100%;
    // height: 0;
    position: absolute;
    // background: #fff;
    z-index: 99;
    display: flex;
    flex-direction: column;
    .back-box{
        height: 42px;
        background: #fff;
        display: flex;
        align-items: center;
        background: #2c3033;
        .box-hint{
            height: 30px;
            background: #fff;
            width: 100%;
            display: flex;
            align-items: center;
            .box-hint-icon{
                width: 60px;
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 20px;
                color: #fff;
                background: #38aef3;
                cursor: pointer;
            }
            .box-hint-name{
                font-size: 14px;
                margin-left: 10px;
            }
        }
    }
    .layout-content{
        flex: 1;
        background: #fff;
        // background: rgba(165, 176, 182, 0.1);
        display: flex;
        height: 100%;
        .mst-left{
            width: 300px;
            height: inherit;
            border-right: 1px solid #dedede;
            padding: 20px;
            .hint{
                height: 30px;
                background: #3db7ff1a;
                line-height: 30px;
                padding-left: 10px;
                .icon-tishi{
                    color: #F29F4C;
                }
            }
            .monitor-box{
                padding: 20px;
                .monitor-list{
                    .parent-name{
                        display: flex;
                        align-items: center;
                        .header-box{
                            width: 12px;
                            height: 12px;
                            border: 1px solid rgb(170, 170, 170);
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            font-size: 14px;
                            cursor: pointer;
                        }
                        .monitor-name-ip{
                            margin-left: 10px;
                            font-size: 14px;
                        }
                    }
                    .monitor-name-list{
                        margin-left: 20px;
                        font-size: 14px;
                        cursor: move;
                    }
                    .monitor-name-list-not{
                        cursor: not-allowed;
                    }
                }
            }
        }
        .mst-main{
            flex: 1;
            display: flex;
            flex-direction: column;
            .tsl-ly-oprators{
                padding: 10px 20px;
                display: flex;
                justify-content: space-between;
                .operation-btn{
                    display: flex;
                    .confirm-btn{
                        margin: 0 10px;
                    }
                }
            }

            .layout-setting{
                padding: 0 20px 20px;
                height: calc(~'100% - 60px');
                width: 100%;
                flex: 1;
                .layout-main{
                    width: 100%;
                    height: 100%;
                    display: grid;
                    grid-template-columns: 50% 50%;
                    grid-template-rows: 33.3333% 33.3333% 33.3333%;
                    .layout-item{
                        background: #fff;
                        outline: 1px dashed #E1E1E1;
                        outline-offset: -1px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        position: relative;
                        .layout-monitor{
                            position: absolute;
                            width: 100%;
                            background: #999;
                            padding: 2px 2px;
                            display: flex;
                            justify-content: space-between;
                            top: 0;
                            color: #000;
                            border-right: 2px dashed #E1E1E1;
                            right: -1px;
                            /deep/ .ivu-icon-md-close{
                                font-size: 20px;
                                cursor: pointer;
                            }
                        }
                        .layout-info{
                            font-size: 60px;
                            color: #999;
                        }
                    }
                    .layout-item-well{
                        background: #000000c7;
                    }
                }
            }
        }
    }
}
</style>
