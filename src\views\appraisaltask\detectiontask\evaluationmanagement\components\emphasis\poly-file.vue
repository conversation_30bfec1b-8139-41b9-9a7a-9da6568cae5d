<template>
  <!-- 聚档可用率详情弹框 -->
  <ui-modal class="poly-file" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-if="!!trackList && this.trackList.availabilityRate != 0">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="不可订阅人员聚类档案"></line-title>
        <div class="list auto-fill">
          <table-data class="table-data auto-fill" :tableData="tableData"></table-data>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.poly-file {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 860px;
    max-height: 860px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 860px;
    max-height: 860px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      height: 450px;
      overflow-y: auto;
      .table-data {
        height: 450px;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
    .page {
      position: absolute;
      bottom: 5px;
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
// getPageClusterResult
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '650px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['可订阅聚档数量', '不可订阅聚档数量'],
        showData: [
          { name: '可订阅聚档数量', value: 0 },
          { name: '不可订阅聚档数量', value: 0 },
        ],
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '聚档可用性',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      loading: false,
      trackList: {},
    };
  },
  async mounted() {
    // await this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    // async init() {
    //   let data = {
    //     resultId: this.$parent.row.resultId,
    //     indexId: this.$parent.row.indexId,
    //     pageSize: this.searchData.pageSize,
    //     pageNumber: this.searchData.pageNum,
    //   };
    //   try {
    //     this.loading = true;
    //     this.tableData = [];
    //     let res = await this.$http.post(governanceevaluation.getPageTrackDetails, data);
    //     this.tableData = res.data.data.entities;
    //     this.searchData.totalCount = res.data.data.total;
    //     this.loading = false;
    //   } catch (err) {
    //     console.log(err);
    //     this.loading = false;
    //   } finally {
    //     this.loading = false;
    //   }
    // },
    // 统计
    async getStatistics() {
      let data = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
      };
      try {
        let res = await this.$http.post(governanceevaluation.getPageClusterResult, data);
        this.trackList = res.data.data.entities[0];
        this.moduleData.rateValue = this.trackList.availabilityRate || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '可订阅聚档数量') {
          item.value = this.trackList.subscribeAmount || 0;
        } else {
          item.value = this.trackList.unSubscribeAmount || 0;
        }
      });
      this.zdryChartObj.count = this.trackList.checkTotalAmount || 0;
      let formatData = {
        seriesName: '检测人员聚档数量',
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('appraisaltask').default,
  },
};
</script>
