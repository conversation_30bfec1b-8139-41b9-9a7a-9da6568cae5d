<template>
<div class="second-step-container">
    <div class="add-ignore-wrap">
        <div class="left-step">
            <div class="step">
                <div class="step-bar">
                    <div class="step-line-highlight">
                        <i class="iconfont icon-circle icon-highlight"></i>
                        <div class="step-bar-gray">
                            <div class="step-bar-arrow"></div>
                            <div class="step-bar-margin"></div>
                        </div>
                        <i class="iconfont icon-circle icon-gray"></i>
                    </div>
                </div>
                <div>
                    <div class="step-text">
                        <i class="iconfont step-one-img"></i>
                        <span>
                            <p>步骤1</p>
                            <p class="large">预案信息</p>
                        </span>
                    </div>
                    <div class="step-text step-two-text">
                        <i class="iconfont step-two-img"></i>
                        <span>
                            <p>步骤2</p>
                            <p class="large">设备选择</p>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="right-device-info">
            <div class="title">设备选择</div>
            <device-select ref="deviceSelect" class="device-content-wrap" :showOrganization="true" v-model="selectedDevices" />
            <div class="footer-btns">
                <Button size="small" class="mr-20" @click="handleSave('firstStep')">上一步</Button>
                <Button size="small" type="primary" :disabled="saveButtonControl" @click="handleSave(null)">保存</Button>&nbsp;&nbsp;
            </div>
        </div>
    </div>
</div>
</template>

<script>
import deviceSelect from './deviceSelect.vue'
export default {
    name: 'secondStep',
    props: {
        setData: {type: Object, default: () => ({})}
    },
    data() {
        return {
            saveButtonControl: true,
            selectedDevices: []
        }
    },
    components: {
        deviceSelect
    },
    watch: {
        setData:{
            handler({resources}) {
                this.selectedDevices = resources.map(v => {
                    v.select = true
                    return v
                });
            },
            immediate: true
        },
        selectedDevices: {
            handler(val) {
                if(val.length) {
                    this.saveButtonControl = false
                } else {
                    this.saveButtonControl = true
                }
            },
            immediate: true
        }
    },
    methods: {
        async handleSave(step) {
            if(!step && this.selectedDevices.length <= 0) {
                this.$Message.warning("请选择设备");
                return;
            }
            this.$emit('stepListener', step, {selectedDevs: this.selectedDevices});
        }
    }
}
</script>

<style lang="less">
@blue: #2C86F8;
@border: rgba(167,172,184,0.3);
@bgColor: #f7f8f9;
.second-step-container {
    height: calc(~'100% - 30px');
    .add-ignore-wrap {
        border: 1px solid @border;
        height: 100%;
        display: flex;
        justify-content: start;

        .left-step {
            background: @bgColor;
            height: 100%;
            width: 200px;
            border-right: 1px solid @border;
            overflow: hidden;
            flex-shrink: 0;
        }

        .step {
            display: flex;

            .step-bar {
                border-left: 2px solid @blue;
                height: 150px;
                margin: 50px 10px 0 30px;
                width: 30px;
            }

            .step-line-highlight {
                border-left: 2px solid @blue;
                height: 27px;
                margin-left: -2px;
            }

            .icon-highlight {
                color: @blue;
                position: relative;
                left: -9px;
                top: -20px
            }

            .icon-gray {
                color: @blue;
                position: relative;
                left: -9.5px;
                top: 3px;
            }

            .step-bar-gray {
                position: relative;
                left: -9px;
                top: 116px;
            }

            .step-bar-arrow {
                width: 15px;
                height: 8px;
                background: @blue;
            }

            .step-bar-margin {
                display: inline-block;
                width: 5.7px;
                height: 5.7px;
                background-color: @blue;
                transform: rotate(45deg);
                margin: 50px;
                position: relative;
                top: -57px;
                left: -37px;
            }

            .step-text {
                display: flex;
                align-items: center;
                height: 40px;
                margin-top: 50px;
                color: @blue;

                p {
                    margin: 10px;
                }
                .large {
                    font-size: 16px;
                    font-weight: 400;
                }

                &.step-two-text {
                    color: @blue;
                    margin-top: 85px;
                }
                .step-one-img {
                    width: 40px;
                    height: 30px;
                    background: url("~@/assets/img/dispatch/deviceIgnore/ya1.png") no-repeat;
                }
                .step-two-img {
                    width: 40px;
                    height: 30px;
                    background: url("~@/assets/img/dispatch/deviceIgnore/sb1.png") no-repeat;
                }
            }
        }

        .right-device-info {
            width: 100%;
            padding: 20px;
            position: relative;
            .title {
                font-weight: bold;
                margin-bottom: 10px;
                padding: 15px 0;
                border-bottom: 1px dotted @border;
            }
            .device-content-wrap {
                padding: 0;
                height: calc(~'100% - 85px')!important;
            }
            .footer-btns {
                position: absolute;
                left: 20px;
                bottom: 5px;
                .xui-btn-9028 {
                    width: 90px;
                    height: 28px;
                }
            }
        }
    }
}
</style>
