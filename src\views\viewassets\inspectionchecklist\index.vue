<template>
  <div class="inspection-checklist-container">
    <!-- 左侧模块菜单 -->
    <div class="left-menu-box">
      <module-type-menu
        :show-module-types="allModuleTypes"
        :actived-module="activedModule"
        :analysisLoading="loadingObj.analysisLoading"
        @handleClickModuleItem="handleClickModuleItem"
        @handleAnalysis="handleAnalysis"
      ></module-type-menu>
    </div>
    <div class="right-content-box auto-fill">
      <!-- 右侧顶部 -->
      <div class="content-header flex-row">
        <!-- 名称 -->
        <div class="content-name flex-aic">
          <div class="title-color-div mr-xs"></div>
          <div class="f-18">{{ activedModule?.name }}</div>
        </div>
        <!-- 按钮：配置=>保存，模块=>导出 -->
        <div v-if="activedModule.id === configModuleType.id" class="flex-aic">
          <Button type="primary" class="ml-md" :loading="loadingObj.saveConfig" @click="saveConfig">
            <i class="icon-font icon-baocun f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">保存</span>
          </Button>
        </div>
        <div class="flex-aic" v-else>
          <span class="base-label-color f-14">更新时间：</span>
          <span class="base-text-color f-14">{{ createTime }}</span>
          <Button type="primary" class="ml-md" :loading="loadingObj.exportModuleData" @click="handleExport">
            <i class="icon-font icon-daochu f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <!-- 右侧内容 -->
      <div class="content-body auto-fill">
        <!-- 巡检配置 -->
        <inspection-config
          v-if="activedModule.id === configModuleType.id"
          ref="inspectionConfigRef"
        ></inspection-config>
        <!-- 清单列表 -->
        <inspection-table
          v-else
          ref="inspectionTableRef"
          :loading="loadingObj.getTableLoading"
          :show-table-header="showTableHeaderList"
          :show-table-data="showTableData"
          @clickColumn="clickTableColumn"
        ></inspection-table>
      </div>
      <device-detail
        v-model="deviceDetailVisible"
        :detail-params="detailParams"
        :actived-module="activedModule"
      ></device-detail>
    </div>
  </div>
</template>
<script>
import { supportModuleTypes, configModuleType } from './utils/enum.js';
import viewassets from '@/config/api/viewassets.js';
import { mapGetters } from 'vuex';

export default {
  name: 'inspectionchecklist',
  data() {
    return {
      configModuleType,
      supportModuleTypes,
      allModuleTypes: [...supportModuleTypes, configModuleType], // 显示的模块类型（包含配置）
      activedModule: {
        ...supportModuleTypes[0],
      }, // 当前激活的模块对象
      showTableHeaderList: [],
      showTableData: [],
      loadingObj: {
        saveConfig: false,
        exportModuleData: false,
        getTableLoading: false,
        analysisLoading: false,
      },
      deviceDetailVisible: false,
      createTime: '-',
      detailParams: {},
    };
  },
  computed: {
    ...mapGetters({ defaultSelectedOrg: 'common/getDefaultSelectedOrg' }),
  },
  methods: {
    //点击左侧菜单模块
    handleClickModuleItem(menuItem) {
      this.activedModule = menuItem;
      if (this.activedModule.id !== this.configModuleType.id) {
        this.getTableData();
      }
    },
    //点击立即分析
    async handleAnalysis() {
      try {
        this.loadingObj.analysisLoading = true;
        let params = {
          jobGroup: 7,
          executorHandler: 'assetInspectionJob',
        };
        await this.$http.get(viewassets.configRunJob, { params });
        this.$Message.success('成功');
      } catch (err) {
        console.log(err);
      } finally {
        this.loadingObj.analysisLoading = false;
      }
    },
    //保存巡检配置
    async saveConfig() {
      if (!this.$refs.inspectionConfigRef) {
        return;
      }
      try {
        this.loadingObj.saveConfig = true;
        let params = await this.$refs.inspectionConfigRef.handleSubmit();
        await this.$http.post(viewassets.saveOrUpdateByDeviceInspection, params);
        this.$Message.success('保存成功');
      } catch (err) {
        console.log(err);
      } finally {
        this.loadingObj.saveConfig = false;
      }
    },
    // 获取清单列表数据
    async getTableData() {
      try {
        const params = {
          regionCode: this.defaultSelectedOrg.regionCode,
          dataModule: this.activedModule.id,
        };
        this.loadingObj.getTableLoading = true;
        let {
          data: { data },
        } = await this.$http.post(viewassets.getInspectionStatistics, params);
        this.showTableHeaderList = data.headerList || [];
        this.showTableData = data.bodyList || [];
        this.createTime = data.createTime || '-';
      } catch (err) {
        console.log(err);
        this.showTableData = [];
      } finally {
        this.loadingObj.getTableLoading = false;
      }
    },
    // 导出巡检清单列表
    async handleExport() {
      try {
        this.loadingObj.exportModuleData = true;
        const params = {
          regionCode: this.defaultSelectedOrg.regionCode,
          dataModule: this.activedModule.id,
        };
        let res = await this.$http.post(viewassets.getInspectionStatisticsExport, params, {
          responseType: 'blob',
        });
        await this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      } finally {
        this.loadingObj.exportModuleData = false;
      }
    },
    //打开设备详情弹窗，并自动对应行政区划和所点击列的条件
    clickTableColumn(row, column) {
      this.detailParams = {
        regionCode: row.regionCode, //组件中需要用到
        civilCode: row.regionCode,
        ...column.params, // 每列对应的搜索条件，由表头(后端)返回
      };
      this.deviceDetailVisible = true;
    },
  },
  mounted() {
    this.activedModule = supportModuleTypes[0];
    this.getTableData();
  },
  components: {
    moduleTypeMenu: require('./modules/ModuleTypeMenu').default,
    inspectionTable: require('./modules/InspectionTable').default,
    inspectionConfig: require('./modules/InspectionConfig').default,
    deviceDetail: require('./components/device-detail').default,
  },
};
</script>
<style lang="less" scoped>
.inspection-checklist-container {
  height: 100%;
  background-color: var(--bg-content);

  .left-menu-box {
    width: 220px;
    height: 100%;
    float: left;
    overflow: auto;
    border-right: 1px solid var(--border-color);
    padding-top: 20px;
  }

  .right-content-box {
    margin-left: 220px;
    height: 100%;

    .content-header {
      padding: 0 20px;
      height: 55px;
      border-bottom: 1px solid var(--border-color);

      .content-name {
        line-height: 1;
        color: var(--color-sub-title-inpage);
        .title-color-div {
          width: 5px;
          height: 18px;
          background: var(--color-title);
        }
      }
    }

    .content-body {
      padding: 20px;
    }
  }
}
</style>
