export const basicMap = {
  people: {
    photo: "photos",
    field: {
      xm: "姓名",
      xbdm: "性别",
      age: "年龄",
      jgDzmc: "籍贯",
      csrq: "出生日期",
      xldm: "文化程度",
      hyzkdm: "婚姻状况",
      zzmmdm: "政治面貌",
      yddh: "联系电话",
      employer: "服务单位",
      zylbdm: "职业类型",
      hjdzDzmc: "户籍地址",
      xzzMlxxdz: "现住地",
    },
  },
  video: {
    photo: "photo",
    field: {
      clusteringImagesNum: "聚类图像数量",
      firstCaptureTime: "首次抓拍时间",
      firstCaptureLocation: "首次抓拍地点",
      recentCaptureTime: "最近抓拍时间",
      recentCaptureLocation: "最近抓拍地点",
    },
  },
  // car: [
  //   {
  //     name: "车牌颜色",
  //     value: "蓝色",
  //   },
  //   {
  //     name: "车辆类型",
  //     value: "轿车",
  //   },
  //   {
  //     name: "车牌品牌",
  //     value: "丰田",
  //   },
  //   {
  //     name: "车身颜色",
  //     value: "白色",
  //   },
  //   {
  //     name: "识别代码",
  //     value: "4233798D7970HG",
  //   },
  //   {
  //     name: "发动机号",
  //     value: "AF9K124503",
  //   },
  //   {
  //     name: "使用性质",
  //     value: "家用",
  //   },
  //   {
  //     name: "登记日期",
  //     value: "2022-02-18 13:01:02",
  //   },
  //   {
  //     name: "发证机关",
  //     value: "********",
  //   },
  // ],
  car: {
    // photo: 'imageUrls', //photoUrl
    photo: "photoUrl",
    field: {
      // plateColor: '车牌颜色',
      vehicleType: "车辆类型",
      vehicleBrandCN: "车辆品牌",
      syxz: "使用性质",
      vehicleColor: "车辆颜色",
      sbdm: "识别代码",
      gldw: "发动机号",
      registerDate: "登记日期",
      fzjg: "发证机关",
    },
  },
  "non-motor-archive": {
    // photo: 'imageUrls', //photoUrl
    photo: "photoUrl",
    field: {
      plateColor: "车牌颜色",
      vehicleType: "车辆类型",
      //   vehicleBrandCN: "车辆品牌",
      vehicleColor: "车辆颜色",
      //   vehicleAngle: "车辆角度",
      //   fzjg: '发证机关',
    },
  },
  device: {
    photo: "imageUrls",
    field: {
      deviceName: "设备名称",
      sbdwlx: "点位类型",
      sbgnlx: "功能类型",
      sbcjqyText: "采集区域",
      isOnline: "设备状态",
      gldw: "所属机构",
      detailAddress: "安装地址",
      azsj: "安装时间",
    },
  },
  // 场所展示字段
  place: {
    photo: "image",
    field: {
      firstLevelName: "一级分类",
      secondLevelName: "二级分类",
      adname: "所属区县",
      townname: "所属街道",
      area: "场所面积",
      address: "场所地址",
    },
  },
};
