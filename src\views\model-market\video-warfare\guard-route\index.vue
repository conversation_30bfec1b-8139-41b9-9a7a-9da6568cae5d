<template>
  <div class="guard-route">
    <mapCustom
      ref="mapBase"
      :idlerWheel="true"
      :siteListFlat="siteListFlat"
      @inited="inited"
      @activeMarkerMouseover="activeMarkerMouseover"
      @activeMarkerMouseout="activeMarkerMouseout"
      @activeMarkerClick="activeMarkerClick"
      @drawDown="drawDown"
      @routeRefresh="routeRefresh"
    >
      <template>
        <add
          v-if="isAdd"
          :is-edit="isEdit"
          v-model="trailSelectOptions"
          @back="handleBack"
          @save="handleSave"
          @draw="manualDraw"
          @refreshDeviceMaker="refreshDeviceMaker"
          @mousemoveItem="activeMarkerMouseover"
          @mouseoutItem="activeMarkerMouseout"
        ></add>
        <routePlayer
          ref="routePlayer"
          v-else-if="isRoutePlayerPanel"
          @back="handleBack"
          :routeInfo="playRouteInfo"
          @mousemoveItem="activeMarkerMouseover"
          @mouseoutItem="activeMarkerMouseout"
        >
        </routePlayer>
        <list
          ref="list"
          v-else
          @add="handleAdd"
          @edit="handleEdit"
          @viewGuardRoute="viewGuardRoute"
        ></list>
      </template>
    </mapCustom>
  </div>
</template>
<script>
import { mapGetters, mapMutations } from "vuex";
import mapCustom from "../components/map-video.vue";
import { getSimpleDeviceList } from "@/api/operationsOnTheMap";
import { addGuardRoute, updateGuardRoute } from "@/api/modelMarket";
import add from "./components/add";
import list from "./components/list";
import routePlayer from "./components/route_player";

export default {
  components: {
    mapCustom,
    add,
    list,
    routePlayer,
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  data() {
    return {
      siteListFlat: [],
      mapOptions: {
        isShowLocation: false,
      },
      dataList: [],
      loading: false,
      isAdd: false,
      isRoutePlayerPanel: false,
      trailSelectOptions: {
        buffDis: 25,
        devices: [],
      },
      playRouteInfo: {},
      isEdit: false,
    };
  },
  async created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  mounted() {
    // this.getMapLayerByTypeSite()
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    // 撒点数据
    // async getMapLayerByTypeSite() {
    //   let deviceTypes = [1]
    //   this.siteListFlat = [];
    //   let roleParam = {roleId: this.userInfo.roleVoList[0].id, filter: this.userInfo.roleVoList[0].initFlag == '1' ? false : true, socialResources: 0, excludeDeviceTypes: [3,4,5,6,7]}
    //   let { data } = await getSimpleDeviceList(roleParam)
    //   data = data.filter(v => deviceTypes.includes(v[2]))
    //   this.siteListFlat = [...data]
    // },
    handleAdd() {
      this.trailSelectOptions = {
        buffDis: 25,
        devices: [],
      };
      this.isAdd = true;
      this.isEdit = false;
      this.resetMap();
    },
    handleEdit(item) {
      this.isEdit = true;
      item.devices = JSON.parse(item.deviceId);
      this.trailSelectOptions = {
        ...item,
        hasRoute: true,
        buffDis: item.distance,
      };
      this.isAdd = true;
      this.$refs.mapBase && this.$refs.mapBase.beforeSetAnimation(item);
    },
    handleBack() {
      this.isAdd = false;
      this.isRoutePlayerPanel = false;
      this.resetMap();
    },
    handleSave() {
      if (this.trailSelectOptions.id) {
        updateGuardRoute({
          ...this.trailSelectOptions,
          distance: this.trailSelectOptions.buffDis,
          deviceId: JSON.stringify(this.trailSelectOptions.devices),
        }).then((res) => {
          this.isAdd = false;
        });
      } else {
        addGuardRoute({
          ...this.trailSelectOptions,
          distance: this.trailSelectOptions.buffDis,
          deviceId: JSON.stringify(this.trailSelectOptions.devices),
        }).then((res) => {
          this.isAdd = false;
        });
      }
      this.resetMap();
    },
    refreshDeviceMaker() {
      this.$refs.mapBase &&
        this.$refs.mapBase.addResourceOnmap(this.trailSelectOptions.devices);
    },
    // 地图加载完成
    inited(mapMain) {
      var optParam = {
        isShowPolygon: true,
        isShowRemoveBar: true,
      };
      this.$refs.mapBase &&
        this.$refs.mapBase.initOperationbar(mapMain, optParam);
    },
    // 开始框选
    manualDraw() {
      this.$refs.mapBase &&
        this.$refs.mapBase.operationByLine({
          buffDis: this.trailSelectOptions.buffDis,
          isHideBuffDisWindow: true,
        });
    },
    // 框选完成后的回调
    drawDown(form) {
      this.trailSelectOptions = Object.assign(this.trailSelectOptions, form);
    },
    activeMarkerClick(data) {
      // 查看设备时 设备类型 会被 定义为 type的值
      delete data.type;
      this.$refs.mapBase && this.$refs.mapBase.showCameraInfowindow(data);
    },
    activeMarkerMouseout(data) {
      this.$refs.mapBase && this.$refs.mapBase.closeMouseInfoWindow();
    },
    activeMarkerMouseover(data) {
      if (!data.longitude || !data.latitude) {
        return;
      }
      this.$refs.mapBase &&
        this.$refs.mapBase.showMouseDom({
          ext: {
            deviceName: data.deviceName,
            Lat: data.latitude,
            Lon: data.longitude,
          },
        });
    },
    resetMap() {
      this.$refs.mapBase && this.$refs.mapBase.resetMap();
    },
    // 查看警卫路线
    viewGuardRoute(item) {
      item.devices = JSON.parse(item.deviceId);
      this.playRouteInfo = item;
      this.isRoutePlayerPanel = true;
    },
    // 轨迹移动返回最新的最近点位
    routeRefresh(info) {
      this.$refs.routePlayer && this.$refs.routePlayer.routeRefresh(info);
    },
  },
};
</script>
<style scoped lang="less">
.guard-route {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
