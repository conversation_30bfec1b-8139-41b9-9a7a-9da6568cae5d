<template>
  <!-- 新增编辑数据源 -->
  <ui-modal
    v-model="modalShow"
    :title="dialogData.title"
    :r-width="dialogData.rWidth"
    ref="iModal"
    :loading="btnLoading"
    @onOk="submitHandle">
    <div class="img-content">
      <img :src="tempUrl" alt="" id="nodeBox" v-if="tempUrl" @load="loadImage">
      <span v-for="(item,index) of copperList"
            class="select-preview "
            :class="selectedIndex===index&&'selected'"
            :key="index"
            @click="selectPreviewHandle(item.box,index)"
            :style="{left:item.box.x+'px',top:item.box.y+'px',width:item.box.width+'px',height:item.box.height+'px'}">
        <Icon type="ios-checkmark"/></span>
    </div>
    <p class="tips color-warning">检测出图片有多个目标，请选择要识别的目标！</p>
  </ui-modal>
</template>
<script>
import html2canvas from 'html2canvas'
import { cloneDeep } from 'lodash'
import { uploadPicToMinio } from '@/api/wisdom-cloud-search'
import dataCopper from '@/views/wisdom-cloud-search/cloud-default-page/components/test'

export default {
  name: 'SearchPictures',
  props: {
    // 关闭
    close: {
      type: Function,
      default: () => {
      }
    },
    dataCopper: {
      type: Object,
      default: () => {
      }
    },
    tempUrl: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      btnLoading: false,
      copperList: [],
      selectedIndex: -1,
      targetCopperData: null,
      modalShow: true,
      dialogData: {
        title: '选择目标',
        rWidth: 1000
      }
    }
  },
  created () {
    this.init()
  },
  methods: {
    init () {
      this.modalShow = true
      this.selectedIndex = -1
      this.targetCopperData = null
    },
    loadImage () {
      const imgBox = document.getElementById('nodeBox')
      const nw = imgBox.naturalWidth
      const nh = imgBox.naturalHeight
      const w = parseInt(window.getComputedStyle(imgBox).width)
      const h = parseInt(window.getComputedStyle(imgBox).height)
      const rateW = w / nw
      const rateH = h / nh
      let cloneData = cloneDeep(this.dataCopper.motor)
      this.copperList = cloneData.map(item => {
        item.box.x = item.box.x * rateW
        item.box.y = item.box.y * rateH
        item.box.width = item.box.width * rateW
        item.box.height = item.box.height * rateH
        return item
      })
    },
    search () {
      const { value, uploadList } = this
      const params = {
        value,
        uploadList
      }
    },
    selectPreviewHandle (data, index) {
      this.targetCopperData = data
      this.selectedIndex = index
    },
    setImage () {
      const targetCopperData = this.targetCopperData
      if (!targetCopperData) {
        this.$Message.warning('请选择识别的目标')
        return false
      }
      const that = this
      this.btnLoading = true
      const cropperW = targetCopperData.width
      const cropperH = targetCopperData.height
      const cropperX = targetCopperData.x
      const cropperY = targetCopperData.y
      // html2canvas配置项
      html2canvas(document.getElementById('nodeBox'), {
        width: cropperW,
        height: cropperH,
        scale: 2,
        x: cropperX,
        y: cropperY,
        useCORS: true
      }).then(function (canvas) {
        const fileBlob = that.getBlob(canvas)
        const fileData = new FormData()
        fileData.append('file', fileBlob)
        uploadPicToMinio(fileData).then(res => {
          const fileUrl = res.data.fileUrl
          that.$emit('getMinImgUrl',fileUrl)
          that.modalShow = false
        }).finally(() => {
          that.btnLoading = false
        })
      })
    },
    getBlob (canvas) { //获取blob对象
      var data = canvas.toDataURL('image/jpeg', 1)
      data = data.split(',')[1]
      data = window.atob(data)
      var ia = new Uint8Array(data.length)
      for (var i = 0; i < data.length; i++) {
        ia[i] = data.charCodeAt(i)
      }
      return new Blob([ia], {
        type: 'image/jpeg'
      })
    },
    submitHandle () {
      this.setImage()
    }
  }
}
</script>
<style lang="less" scoped>
.img-content {
  position: relative;
  img {
    display: block;
    width: 100%;
    height: auto;
  }
  .select-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 90px;
    height: 90px;
    background: rgba(242, 230, 76, 0.2);
    border-radius: 4px;
    border: 2px dashed #F2E64C;
    .ivu-icon {
      color: #fff;
      font-size: 20px;
      background: #2C86F8;
      border-radius: 50%;
      margin-top: 5px;
      margin-left: 5px;
      opacity: 0;
    }
    &:hover {
      border: 2px solid #2C86F8;
      background: rgba(44, 134, 248, 0.3);
    }
    &.selected {
      background: rgba(44, 134, 248, 0.3);
      border: 2px solid #2C86F8;
      .ivu-icon {
        opacity: 1;
      }
    }
  }
}
.tips {
  font-size: 16px;
  margin-top: 5px;
}
</style>
