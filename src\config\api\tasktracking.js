export default {
  viewTackeAggregate: '/ivdg-data-governance-service/topic/viewTask', // 治理主题流程图
  tackeAggregate: '/ivdg-data-governance-service/topic/view', // 获取所有主题
  queryTaskTopicListStatics: '/ivdg-data-governance-service/topic/queryTaskTopicListStatics', // 获取所有主题
  baseInterface: {
    /**视图基础数据 */
    queryDeviceInfoInitList: '/ivdg-data-governance-service/deviceInfoInit/queryDeviceInfoInitList', // 数据输入列表
    getTransferList: '/ivdg-data-governance-service/deviceInfoInit/queryDeviceInfoInitTransferList', // 转换失败列表
    queryTransferStatistics: '/ivdg-data-governance-service/deviceInfoInit/queryTransferStatistics', // 转换数据统计
    deviceInfoAccessList: '/ivdg-data-governance-service/deviceInfoInit/queryDeviceInfoInitAccessList', // 查询接入失败列表
    queryAccessStatistics: '/ivdg-data-governance-service/deviceInfoInit/queryAccessStatistics', // 查询接入统计
    queryTopicStatisticsList: '/ivdg-data-governance-service/topicDeviceResult/queryTopicCheckStatisticsList', // 查询检测报表统计
    queryCountStatistics: '/ivdg-data-governance-service/topicDeviceResult/queryTopicCheckCountStatistics', // 查询检测数量统计
    queryPropertyDetailList: '/ivdg-data-governance-service/topicDeviceResult/queryTopicCheckPropertyDetailList', // 查询检测字段明细列表
    queryListByTaskTracker: '/ivdg-asset-app/device/queryDeviceInfoListByTaskTracker', // 任务追踪查询设备列表
    queryTopicCheckTimeStatisticsList:
      '/ivdg-data-governance-service/topicDeviceResult/queryTopicCheckTimeStatisticsList', //查询时钟检测报表统计
    downloadDeviceAbnormalData: '/ivdg-data-governance-service/deviceInfoInit/downloadDeviceAbnormalData', //设备任务追踪异常数据导出
  },
  temporateInterface: {
    /**临时主题 */
    queryDeviceInfoInitList: '/ivdg-data-governance-service/deviceInfoInitTemp/queryDeviceInfoInitTempList', // 数据输入列表
    getTransferList: '/ivdg-data-governance-service/deviceInfoInitTemp/queryDeviceInfoInitTempTransferList', // 转换失败列表
    queryTransferStatistics: '/ivdg-data-governance-service/deviceInfoInitTemp/queryTransferTempStatistics', // 转换数据统计
    deviceInfoAccessList: '/ivdg-data-governance-service/deviceInfoInitTemp/queryDeviceInfoInitTempAccessList', // 查询接入失败列表
    queryAccessStatistics: '/ivdg-data-governance-service/deviceInfoInitTemp/queryAccessTempStatistics', // 查询接入失败统计
    queryTopicStatisticsList: '/ivdg-data-governance-service/topicDeviceTempResult/queryTopicCheckStatisticsList', // 查询检测报表统计
    queryCountStatistics: '/ivdg-data-governance-service/topicDeviceTempResult/queryTopicCheckCountStatistics', // 查询检测数量统计
    queryPropertyDetailList: '/ivdg-data-governance-service/topicDeviceTempResult/queryTopicCheckPropertyDetailList', // 查询检测字段明细列表
    queryListByTaskTracker: '/ivdg-asset-app/deviceTemp/queryDeviceInfoListByTaskTracker', // 任务追踪查询设备列表
    queryTopicCheckTimeStatisticsList:
      '/ivdg-data-governance-service/topicDeviceTempResult/queryTopicCheckTimeStatisticsList', //查询时钟检测报表统计
    downloadDeviceAbnormalData: '/ivdg-data-governance-service/deviceInfoInitTemp/downloadDeviceTempAbnormalData', //临时设备任务追踪异常数据导出
  },
  /**---人脸---**/
  queryFaceLibAbnormalPageList: '/ivdg-asset-app/faceLibAbnormal/queryFaceLibAbnormalPageList', // 人脸异常库分页(数据输出弹框)
  queryErrorMessages: '/ivdg-asset-app/faceLibAbnormalInfoResult/queryErrorMessages', // 异常原因列表
  queryFaceLibAbnormalTaskPage: '/ivdg-asset-app/faceLibAbnormal/queryFaceLibAbnormalTaskPage', // 弹框异常分页数据-图像模式
  queryFaceTaskStatistics: '/ivdg-asset-app/faceLibAbnormalInfoResult/queryFaceTaskStatistics', // 弹框检测异常数统计
  queryFaceLibAbnormalInfoResult: '/ivdg-asset-app/faceLibAbnormalInfoResult/queryFaceLibAbnormalInfoResult', // 查询人脸异常库错误原因
  queryFaceTaskStatisticsChartDetail: '/ivdg-asset-app/faceLibAbnormalInfoResult/queryFaceTaskStatisticsChartDetail', // 人脸数据输出图表详情
  downloadFaceLibAbnormalDev: '/ivdg-asset-app/faceLibAbnormal/downloadFaceLibAbnormalDev', // 人脸异常数据导出
  queryFaceLibAbnormalTaskPageDev: '/ivdg-asset-app/faceLibAbnormal/queryFaceLibAbnormalTaskPageDev', // 弹框异常分页数据-设备模式

  /**---人员--- */
  queryPersonInitAccessList: '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonInitAccessList', // 获取人员输入统计
  queryCheckStatics: '/ivdg-data-governance-service/topicComponentPersonResult/queryCheckStatics', // 人员检测错误报表统计
  queryPersonInitTransferList: '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonInitTransferList', // 获取人员转换失败列表
  querypersonTransferStatistics: '/ivdg-data-governance-service/topicComponentPersonResult/queryTransferStatistics', // 获取人员转换失败统计
  queryPersonCheckCountStatistics:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonCheckCountStatistics', // 获取人员检测数量统计
  queryPersonInitFailList: '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonInitFailList', // 获取人员入库失败列表
  queryPersonInitFailStatistics:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonInitFailStatistics', // 获取人员入库失败统计
  queryPersonCheckResultDetail: '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonCheckResultDetail', // 获取人员检测结果详情
  queryImportantPersonPageTaster:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryImportantPersonPageTaster', // 获取人员异常数据列表

  queryPersonLibPageTaskTaskTracker:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonLibPageTaskTaskTracker', // 获取人员轨迹异常数据列表
  queryPersonLibCheckResultDetail:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonLibCheckResultDetail', // 获取人员轨迹检测结果详情
  queryPersonLibCheckCountStatistics:
    '/ivdg-data-governance-service/topicComponentPersonResult/queryPersonLibCheckCountStatistics', // 获取人员轨迹检测数量统计
  downloadPersonAbnormalData: '/ivdg-data-governance-service/topicComponentPersonResult/downloadPersonAbnormalData', // 导出人员任务追踪异常数据
  downloadTransferData: '/ivdg-data-governance-service/topicComponentPersonResult/downloadTransferData', // 导出转换失败数据

  querySumStatistics: '/ivdg-data-governance-service/topic/querySumStatisticsByTopicComponentId', // 人脸和重点人员弹窗的统计
  viewImportantPersonTask: '/ivdg-data-governance-service/topic/viewImportantPersonTask', // 获取人员的流程图组件
  getDeviceCollectAreaTreeList: '/ivdg-data-governance-service/deviceSbcjqyData/queryDataList', //查询设备采集区域数据

  importantPersonPageList: '/ivdg-data-governance-service/topicComponentPersonResult/importantPersonPageList', //聚档列表
};
