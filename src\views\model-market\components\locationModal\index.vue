<!--
    * @FileDescription: 详情
    * @Author: H
    * @Date: 2022/12/13
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <map-modal v-model="visible" title="点位详情" :maskClosable='true' :r-width="900" @onOk="comfirmHandle" @onCancel="handleCancel">
        <component 
            :ref="sectionMenuName" 
            :vehicleInfo="this.positionPoints[0]" 
            :faceInfo="this.positionPoints[0]" 
            :position-points="positionPoints" 
            :is="sectionMenuName" 
            @close="handleClose"> </component>
    </map-modal>
</template>

<script>
import mapModal from '@/components/modal'
import face from '@/components/mapdom/face-map.vue'
import vehicle from '@/components/mapdom/vehicle-map.vue'
export default {
    name: '',
    components:{
        face,
        vehicle,
        mapModal
    },
    data () {
        return {
            sectionMenuName:'',
            visible: false,
            positionPoints: [],
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        show(item, tab) {
            this.sectionMenuName = tab;
            let name = this.sectionMenuName + '';
            item.traitImg = item.capturePic;
            this.positionPoints = [item];
            this.$nextTick(() =>{
                // this.$refs[name].init(item, item).then(res => {
                //     if (res.data) {
                        
                //     } else {
                //         this.$Message.warning('暂无数据')
                //     }
                // })
                
                this.visible = true;
                this.$refs[name].init()
            })
        },
        comfirmHandle() {

        },
        handleCancel() {
            this.$emit('close')
            this.visible = false;
        },
        handleClose() {
            this.$emit('close')
            this.visible = false;
        }
    }
}
</script>

<style lang='less' scoped>
.location{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow:auto;
    background-color: rgba(55,55,55,.6);
    z-index: 999;
    /deep/.dom{
        margin: 0 auto;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
}
</style>
