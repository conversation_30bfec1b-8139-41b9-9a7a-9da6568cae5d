.analyse-wrap{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    .search_box{
        width: 370px;
        height: inherit;
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.298);
        border-radius: 4px 4px 4px 4px;
        margin-right: 10px;
        .form-box{
            padding: 10px; 
            /deep/ .ivu-form-item{
                margin-bottom: 10px;
            }
            .search_content{
                display: flex;
                flex-wrap: wrap;
                flex: 1;
                .active-area-sele {
                    width: 200px;
                    height: 34px;
                    border-radius: 4px;
                    font-size: 14px;
                    text-align: center;
                    line-height: 34px;
                    cursor: pointer;
                    border: 1px dashed #2c86f8;
                    background: rgba(44, 134, 248, 0.1);
                    color: rgba(44, 134, 248, 1);
                }
            }
            .btn-group {
                .btnwidth {
                    width: 258px;
                }
            }
        }
    }
    .table_box{
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.298);
        border-radius: 4px 4px 4px 4px;
        flex: 1;
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 20px 20px 0px 20px;
        .data-export{
            position: relative;
            .mr {
                margin-right: 10px;
            }
        }
        .table_content{
            position: relative;
            overflow-y: auto;
            flex: 1;
            display: flex;
            flex-wrap: wrap;
            justify-content: start;
            align-content: flex-start;
            padding-top: 10px;
            .list-card {
                position: relative;
                background-color: #f9f9f9;
                margin-bottom: 10px;
                height: min-content;
                box-sizing: border-box;
                width: 13.7%;
                // padding: 10px;
                padding: 5px;
                border-radius: 4px;
                border: 1px solid #d3d7de;
                box-shadow: 1px 1px 7px #cdcdcd;
                margin-left: 6px;
                .img-content {
                    width: 100%;
                    position: relative;
                    border: 1px solid #cfd6e6;
                    height: 167px;
                    img {
                        width: 100%;
                        height: 100%;
                        display: block;
                    }
                    .shade {
                        position: absolute;
                        background: rgba(0, 0, 0, 0.7);
                        font-size: 12px !important;
                        width: 100%;
                        bottom: 0;
                        left: 0;
                        text-align: center;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        flex-direction: column;
                        line-height: 18px;
                        padding: 3px 0;
                    }
                    .treatment-state {
                        background: #fff;
                        position: absolute;
                        top: 2px;
                        right: 25px;
                        padding: 2px 5px;
                    }
                }
                .bottom-info {
                    padding-top: 5px;
                    position: relative;
                    time,
                    p {
                        display: flex;
                        align-items: center;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        color: rgba(0, 0, 0, 0.8);
                        white-space: nowrap;
                        width: 100%;
                        .iconfont {
                            margin-right: 2px;
                            color: #888;
                        }
                    }
                    .time-plateNo{
                        display: flex;
                        justify-content: space-between;
                        .time_num{
                            // width: 80px;
                            width: 100px;
                            font-size: 11px;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                        /deep/.plateNum{
                            .license-plate-small{
                                font-size: 12px;
                                font-weight: 100;
                            }
                        }
                    }
                    .device-name{
                        cursor: pointer;
                    }
                    .active-icon{
                        position: absolute;
                        height: 100%;
                        width: 100%;
                        background: #2c86f8;
                        top: 0;
                        display: none;
                        flex-wrap: wrap;
                        justify-content: center;
                        i{
                            width: 25%;
                            text-align: center;
                            cursor: pointer;
                            font-size: 14px;
                            color: #fff;
                        }
                    }
                }
                &:hover{
                    .active-icon{
                        display: flex;
                    }
                }
                .fast-operation-bar{
                    position: absolute;
                    right: 5px;
                    bottom: 32px;
                    color: #2C86F8;
                    width: 20px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    .icon-gengduo{
                        transform: rotate(90deg);
                        transition: 0.1s;
                        display: inline-block;
                    }
                    p:hover{
                        color: #2C86F8;
                    }
                    &:hover{
                        background: #2C86F8;
                        color: #fff;
                       
                        .icon-gengduo{
                            transform: rotate(0deg);
                            transition: 0.1s;
                        }
                        border-radius: 10px;
                    } 
                    /deep/ .ivu-poptip-popper{
                        min-width: 150px !important;
                        width: 40px !important;
                        height: auto;
                    }
                    /deep/.ivu-poptip-body{
                        height: auto !important;
                    }
                    .mark-poptip{
                       color: #000;
                       cursor: pointer;
                       text-align: left;
                       /deep/ .ivu-icon-ios-add-circle-outline{
                            font-weight: 600;
                       }
                    }  
                }
            }
        }
    }
}