<template>
  <div class="expandRow">
    <ui-table :columns="columns" :data="taskList" :show-header="false" stripe>
      <template #name="{ row }">
        <div class="deviceName" :title="row.name">
          <i
            class="iconfont column-item-icon-camera"
            :class="
              parseInt(row.cameraType) === 0
                ? 'icon-shebeizichan'
                : 'icon-qiuji'
            "
          ></i
          >{{ row.name }}
        </div>
      </template>
      <template #fileName="{ row }">
        <div class="deviceName" :title="row.name">{{ row.name }}</div>
      </template>
      <template #videoTime="{ row }">
        <div>{{ row.videoTime | timeFormat_3("CN") }}</div>
      </template>
      <template #vehicle="{ row }">
        <div class="status">
          <div v-if="row.vehicle" class="exist">
            <i class="iconfont icon-qiche1 cl" title="车辆"></i>
            <div
              class="runing-des"
              :style="{ color: getFontColorByStatus(row.vehicle) }"
            >
              {{ row.vehicle | commonFiltering(filestrucureTaskStatusList) }}
            </div>
          </div>
          <div v-else class="without">
            <i class="iconfont icon-qiche1" title="车辆"></i>
            <div class="runing-des">无此类型</div>
          </div>

          <div v-if="row.nonmotor" class="exist">
            <i class="iconfont icon-diandongche fjdc" title="非机动车"></i>
            <div
              class="runing-des"
              :style="{ color: getFontColorByStatus(row.nonmotor) }"
            >
              {{ row.nonmotor | commonFiltering(filestrucureTaskStatusList) }}
            </div>
          </div>
          <div v-else class="without">
            <i class="iconfont icon-diandongche" title="非机动车"></i>
            <div class="runing-des">无此类型</div>
          </div>

          <div v-if="row.face" class="exist">
            <i class="iconfont icon-renlian1 rl" title="人脸"></i>
            <div
              class="runing-des"
              :style="{ color: getFontColorByStatus(row.face) }"
            >
              {{ row.face | commonFiltering(filestrucureTaskStatusList) }}
            </div>
          </div>
          <div v-else class="without">
            <i class="iconfont icon-renlian1" title="人脸"></i>
            <div class="runing-des">无此类型</div>
          </div>

          <div v-if="row.human" class="exist">
            <i class="iconfont icon-renti1 rt" title="人体"></i>
            <div
              class="runing-des"
              :style="{ color: getFontColorByStatus(row.human) }"
            >
              {{ row.human | commonFiltering(filestrucureTaskStatusList) }}
            </div>
          </div>
          <div v-else class="without">
            <i class="iconfont icon-renti1" title="人体"></i>
            <div class="runing-des">无此类型</div>
          </div>
        </div>
      </template>
      <template #fileSize="{ row }">
        <div>
          {{ row.fileSize | fileSizeFormat }}/{{ row.duration | formatDuring }}
        </div>
      </template>
      <template #resolution="{ row }">
        <div>{{ row.width }}*{{ row.height }}</div>
      </template>
      <template #progress="{ row }">
        <Progress
          :percent="row.progress"
          :stroke-width="12"
          text-inside
          style="lineheight: 11px"
        />
      </template>
      <template #processTime="{ row }">
        <div>{{ row.processTime | transTime }}</div>
      </template>
      <template #analysisTime="{ row }">
        <div>{{ getAnalysisTime(row) | transTime }}</div>
      </template>
      <template #createTime="{ row }">
        <div>{{ row.createTime | timeFormat }}</div>
      </template>
      <template #baseTime="{ row }">
        <div>{{ row.baseTime | timeFormat }}</div>
      </template>
      <template #opreate="{ row }">
        <div class="opreate">
          <i-switch
            v-if="subTaskType == 'real'"
            title="开启/停止"
            :disabled="currentJob.applyStatus != 1"
            :loading="switchLoading"
            :value="getSwitchStatus(row)"
            @on-change="handleSwitch($event, row)"
          />
          <div class="tools">
            <el-popover trigger="hover" placement="left-start" width="130">
              <i class="iconfont icon-gengduo" slot="reference"></i>
              <div class="mark-poptip">
                <p
                  v-if="subTaskType == 'file'"
                  :class="isRehandleDisabled(row) ? 'disabled' : ''"
                  @click="rehandle(row)"
                >
                  <i class="iconfont icon-fuwei"></i>重新处理
                </p>
                <p
                  v-if="subTaskType == 'file'"
                  :class="isStopDisabled(row) ? 'disabled' : ''"
                  @click="handleStop(row)"
                >
                  <i class="iconfont icon-pause"></i>停止
                </p>
                <p @click="handleEdit(row)">
                  <i class="iconfont icon-bianji"></i>编辑
                </p>
                <p @click="handleSearch(row)">
                  <i class="iconfont icon-gaojisousuo"></i>资源检索
                </p>
                <p @click="handleMap(row)">
                  <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                </p>
                <p
                  v-if="subTaskType == 'file'"
                  :class="!row.originalFilePfsPath ? 'disabled' : ''"
                  @click="handleDownloadFile(row)"
                >
                  <i class="iconfont icon-download"></i>文件下载
                </p>
                <p @click="handleDel(row)">
                  <i class="iconfont icon-shanchu1"></i>删除
                </p>
              </div>
            </el-popover>
          </div>
        </div>
      </template>
    </ui-table>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
export default {
  name: "expandRow",
  props: {
    taskList: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    currentJob: {
      type: Object,
      default: () => {},
    },
    subTaskType: {
      type: String,
      default: "",
    },
    switchLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      structDataTypeList: "dictionary/getStructDataType", //解析类型
      structTaskStatusList: "dictionary/getStructTaskStatus", //实时视频解析任务状态
      structHistroytaskStatusList: "dictionary/getStructHistroytaskStatus", //历史视频解析任务状态
      filestrucureTaskStatusList: "dictionary/getFilestrucureTaskStatus", //文件解析任务状态
    }),
  },
  mounted() {},
  methods: {
    getFontColorByStatus(status) {
      var colorObj = {
        "-1": "#B6C0CB",
        0: "#B6C0CB",
        1: "#65d46a",
        2: "#B6C0CB",
        3: "#C7A80D",
        4: "#D83636",
        5: "#2C9EFF",
        7: "#2C9EFF",
        8: "#D83636",
        9: "#2C9EFF",
        10: "#2C9EFF",
        11: "#D83636",
        13: "#D83636",
        14: "#D83636",
      };
      if (status && colorObj.hasOwnProperty(status.toString())) {
        return colorObj[status.toString()];
      } else {
        return "#000000";
      }
    },
    rehandle(row) {
      if (this.isRehandleDisabled(row)) return;
      this.$emit("rehandle", row);
    },
    handleStop(row) {
      if (this.isStopDisabled(row)) return;
      this.$emit("handleStop", row);
    },
    handleEdit(row) {
      this.$emit("handleEdit", row);
    },
    handleSearch(row) {
      this.$emit("handleSearch", row, this.currentJob);
    },
    handleMap(row) {
      this.$emit("handleMap", row);
    },
    handleDel(row) {
      this.$emit("handleDel", row, this.currentJob);
    },
    handleSwitch(status, row) {
      this.$emit("handleSwitch", status, row, this.currentJob);
    },
    handleDownloadFile(row) {
      if (!row.originalFilePfsPath) return;
      this.$emit("handleDownloadFile", row);
    },
    getAnalysisTime(record) {
      let stateList = [0, 1, 10, 11, 20, 21, 30, 40, 51, 52]; //只有特定状态下才需要计时
      let going = stateList.some(function (state) {
        return state === record.status;
      });
      let analysisTime = 0;
      if (going) {
        let duration = Date.now() - record.startLoopTime;
        analysisTime = record.processTime + duration;
      } else {
        analysisTime = record.processTime;
      }
      return analysisTime;
    },
    isRehandleDisabled(v) {
      if (v.vehicle && (v.vehicle === -1 || v.vehicle === 13)) {
        return false;
      } else if (v.nonmotor && (v.nonmotor === -1 || v.nonmotor === 13)) {
        return false;
      } else if (v.face && (v.face === -1 || v.face === 13)) {
        return false;
      } else if (v.human && (v.human === -1 || v.human === 13)) {
        return false;
      } else {
        return true;
      }
    },
    isStopDisabled(v) {
      if (v.vehicle && (v.vehicle === 1 || v.vehicle === 3)) {
        return false;
      } else if (v.nonmotor && (v.nonmotor === 1 || v.nonmotor === 3)) {
        return false;
      } else if (v.face && (v.face === 1 || v.face === 3)) {
        return false;
      } else if (v.human && (v.human === 1 || v.human === 3)) {
        return false;
      } else {
        return true;
      }
    },
    getSwitchStatus(v) {
      if (v.vehicle && (v.vehicle === 1 || v.vehicle === 3)) {
        return true;
      } else if (v.nonmotor && (v.nonmotor === 1 || v.nonmotor === 3)) {
        return true;
      } else if (v.face && (v.face === 1 || v.face === 3)) {
        return true;
      } else if (v.human && (v.human === 1 || v.human === 3)) {
        return true;
      } else {
        return false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.expandRow {
  /deep/ .ivu-table-wrapper {
    height: auto !important;
  }
  /deep/ .ivu-table-body {
    height: auto !important;
  }
  .ivu-table-tbody td {
    background-color: #f9f9f9 !important;
  }
  .deviceName {
    padding-left: 112px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    i {
      margin-right: 5px;
      color: #2c86f8;
    }
  }
  .status {
    display: flex;
    justify-content: center;
    .without,
    .exist {
      display: flex;
      margin-right: 10px;
      i {
        margin-right: 5px;
      }
    }
    .cl {
      color: #15997d;
    }
    .fjdc {
      color: #49bf00;
    }
    .rl {
      color: #d36c29;
    }
    .rt {
      color: #08c6d4;
    }
  }
  .opreate {
    display: flex;
  }
  .tools {
    color: #2c86f8;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    margin-left: 5px;
    .icon-gengduo {
      transform: rotate(90deg);
      transition: 0.1s;
      display: inline-block;
    }
    p:hover {
      color: #2c86f8;
    }
    &:hover {
      background: #2c86f8;
      color: #fff;

      .icon-gengduo {
        transform: rotate(0deg);
        transition: 0.1s;
      }
      border-radius: 10px;
    }
  }
}
</style>
<style lang="less">
.mark-poptip {
  color: #000;
  cursor: pointer;
  text-align: left;
  p:hover {
    color: #2c86f8;
  }
  .disabled {
    cursor: not-allowed;
    color: grey !important;
  }
}
</style>
