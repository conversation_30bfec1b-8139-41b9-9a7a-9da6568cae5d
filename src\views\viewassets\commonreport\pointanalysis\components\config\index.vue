<template>
  <ui-modal class="configuration" v-model="visible" title="选点配置" :styles="styles">
    <underline-menu
      v-model="active"
      :data="menuList"
      :before-on-change="beforeChangeMenu"
      class="mb-sm"
    ></underline-menu>
    <div class="config-wrap">
      <keep-alive v-if="visible">
        <components :is="active" :data-type="dataType" @save="save" ref="configRef"></components>
      </keep-alive>
    </div>
    <template #footer>
      <Button @click="handleReset" class="plr-30"> 取 消 </Button>
      <Button type="primary" @click="nextStep" class="plr-30"> 下一步 </Button>
      <Button type="primary" @click="handleSubmit" class="plr-30" :loading="saveLoading"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapActions } from 'vuex';
export default {
  props: {
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      dataType: 1, //数据类型：1选点分析 2巡检清单
      visible: false,
      styles: {
        width: '9.5rem',
      },
      active: 'quality',
      menuList: [
        {
          code: 'quality',
          label: '数据质量要求',
        },
        {
          code: 'numberDivisions',
          label: '区划数量要求',
        },
        {
          code: 'collectionArea',
          label: '采集区域数量要求',
        },
        {
          code: 'analysisPlan',
          label: '分析计划',
        },
      ],
      configInfo: {},
      saveLoading: false,
    };
  },
  created() {
    this.setHomeConfig();
  },
  methods: {
    ...mapActions({
      setHomeConfig: 'home/setHomeConfig',
    }),
    // 更改激活菜单前保存当前菜单的数据
    beforeChangeMenu() {
      // 当前激活菜单中的save方法
      this.$refs.configRef.save();
    },
    /**
     * 保存当前菜单的数据到this.configInfo上
     * ***如果当前菜单校验不通过不应该抛出此方法，菜单中的save中应该throw new Error
     */
    save(config) {
      this.configInfo[this.active] = config;
    },
    nextStep() {
      this.$refs.configRef.save();
      const index = this.menuList.findIndex((row) => row.code === this.active);
      this.active = this.menuList[index + 1].code;
    },
    async handleSubmit() {
      try {
        this.saveLoading = true;
        this.$refs.configRef.save();
        // 质量要求
        if (this.configInfo['quality']) {
          let qualityConfigParams = this.configInfo['quality'].map((item) => {
            return { ...item, dataType: this.dataType };
          });
          await this.$http.post(equipmentassets.qualityConfigSave, qualityConfigParams);
        }
        //区划数量要求
        this.configInfo['collectionArea']
          ? await this.$http.post(equipmentassets.updateStanderConfig, this.configInfo['collectionArea'])
          : null;
        // 采集区域数量要求
        this.configInfo['numberDivisions']
          ? await this.$http.post(equipmentassets.updateStanderConfig, this.configInfo['numberDivisions'])
          : null;
        // 分析计划
        if (this.configInfo['analysisPlan']) {
          let analysisPlanParams = {
            ...this.configInfo['analysisPlan'],
            dataType: this.dataType,
          };
          await this.$http.post(equipmentassets.updateQualityJobCron, analysisPlanParams);
        }
        this.visible = false;
        this.$Message.success('保存成功');
      } catch (err) {
        console.log(err, 'err');
      } finally {
        this.saveLoading = false;
      }
    },
    handleReset() {
      this.visible = false;
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.active = 'quality';
      } else {
        this.active = '';
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UnderlineMenu: require('@/components/underline-menu').default,
    Quality: require('./quality.vue').default,
    NumberDivisions: require('./number-divisions.vue').default,
    CollectionArea: require('./collection-area.vue').default,
    AnalysisPlan: require('./analysis-plan.vue').default,
  },
};
</script>
<style lang="less" scoped>
.configuration {
  @{_deep} .ivu-modal-body {
    display: flex;
    flex-direction: column;
  }
  .config-wrap {
    height: 650px;
    overflow-y: auto;
  }
}
</style>
