<template>
  <div class="sign-box" :class="getFullscreen ? 'full-screen-container' : ''" @click="setScreenFull">
    <i class="icon-font f-12 icon" :class="getIcon" title="全屏"></i>
    <i
      class="icon-font icon icon-qiehuan home-icon"
      @click="() => $emit('changeHome')"
      title="数据治理流程"
      v-permission="{
        route: $route.name,
        permission: 'static',
      }"
    ></i>
  </div>
</template>

<script>
import screenfull from 'screenfull';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'full-screen',
  components: {},
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
    getIcon() {
      if (this.getFullscreen) {
        return 'icon-tuichuquanping';
      } else {
        return 'icon-ivdg-quanping';
      }
    },
  },
  watch: {},
  filter: {},
  mounted() {
    this.DetectFullscreenChange();
  },
  methods: {
    ...mapActions({
      setFullscreen: 'home/setFullscreen',
    }),
    DetectFullscreenChange() {
      if (screenfull.isEnabled) {
        screenfull.on('change', () => {
          if (screenfull.isFullscreen) {
            this.setFullscreen(true);
          } else {
            this.setFullscreen(false);
          }
        });
      }
    },
    setScreenFull() {
      if (screenfull.isEnabled && screenfull.isFullscreen) {
        screenfull.exit();
        this.setFullscreen(false);
        this.$emit('on-change-full-screen', false);
      } else {
        screenfull.toggle(this.$parent.$el);
        this.setFullscreen(true);
        this.$emit('on-change-full-screen', true);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.sign-box {
  position: absolute;
  left: 77%;
  top: 10px;
  display: flex;
  flex-direction: column;
  width: 24px;
  .icon {
    line-height: 12px;
    color: #1f88a2;
    border: 1px solid #1f88a2;
    padding: 5px;
    &.home-icon {
      padding: 5px 3px;
      margin-top: 10px;
    }
  }
}
.full-screen-container {
  left: 96.5%;
  flex-direction: row;
  .icon {
    &.home-icon {
      padding: 5px 3px;
      margin-top: 0px;
      margin-left: 10px;
    }
  }
}
</style>
