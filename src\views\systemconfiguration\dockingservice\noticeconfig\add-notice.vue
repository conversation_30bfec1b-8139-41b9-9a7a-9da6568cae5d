<template>
  <ui-modal v-model="noticeVisible" :title="`${activeRow.id === -1 ? '新增' : '编辑'}通知公告`" width="40rem">
    <div class="notice-box">
      <Form ref="formInfo" :model="searchData" :rules="ruleValidate" :label-width="80">
        <FormItem prop="title" label="公告名称">
          <Input v-model="searchData.title" class="width-big" placeholder="请输入公告名称"></Input>
        </FormItem>
        <FormItem prop="content" label="公告内容">
          <Input
            v-model="searchData.content"
            class="width-big textarea"
            type="textarea"
            :rows="5"
            placeholder="请输入公告内容"
          ></Input>
        </FormItem>
      </Form>
      <Upload
        action="/ivdg-asset-app/notify/upload"
        ref="upload"
        class="inline uploadclass pointer"
        :show-upload-list="false"
        multiple
        :headers="headers"
        :before-upload="beforeUpload"
        :on-success="importSuccess"
        :on-error="importError"
      >
        <i class="icon-font icon-fujianxiazai"></i>
        添加附件<span class="font-grey">（单文件最大100M）</span>
      </Upload>
      <ul class="file-ul mt-lg">
        <li v-for="item of fileList" :key="item.uid" class="font-grey">
          <p class="file-name">
            <span class="inline file-name-text ellipsis">{{ item.originalName }}</span>
            <span class="ml-sm">{{ handleKB(item) }}</span>
          </p>
          <span @click="deleteItem(item)" class="font-active-color ml-md pointer">删除</span>
        </li>
      </ul>
    </div>
    <template slot="footer">
      <div class="button-box">
        <Button class="mr-sm" @click="noticeVisible = false">取消</Button>
        <Button type="primary" class="mr-sm" @click="saveNotice" :loading="loading">{{
          activeRow.id === -1 ? '保存' : '更新'
        }}</Button>
      </div>
    </template>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {},
    activeRow: {},
  },
  data() {
    return {
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importLoading: false,
      ruleValidate: {
        title: [{ required: true, message: '请输入公告名称', trigger: 'blur' }],
        content: [{ required: true, message: '请输入公告内容', trigger: 'blur' }],
      },
      noticeVisible: false,
      loading: false,
      searchData: {
        title: '',
        content: '',
        fileIds: '',
      },
      fileList: [],
    };
  },
  mounted() {},
  methods: {
    beforeUpload(file) {
      console.log(file, 'file');
      this.importLoading = true;
    },
    // beforeUpload(file) {
    //   if (!/\.(xlsx|xls)$/.test(file.name)) {
    //     this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！')
    //     return false
    //   }
    //   const isLt30M = file.size / 1024 / 1024 < 100
    //   if (!isLt30M) {
    //     this.$Message.error('上传文件大小不能超过 30MB!')
    //     return false
    //   }

    //   let fileData = new FormData()
    //   fileData.append('file', file)
    //   this.$http
    //     .post(unified.fileUpload, fileData)
    //     .then((res) => {
    //       if (res.data.code === 200) {
    //         this.modalData.fileUrl = res.data.data.fileUrl
    //         this.modalData.fileName = res.data.data.originalFilename
    //         this.$forceUpdate()
    //       }
    //     })
    //     .finally(() => {})
    //   return false
    // },
    importSuccess(response, file, fileList) {
      console.log(response, file, fileList, 'response, file, fileList');
      file.id = response.data[0];
      file.originalName = file.name;
      this.fileList.push(file);
      this.importLoading = false;
    },
    importError() {
      this.importLoading = false;
      this.$Message.error('导入文件失败!');
    },
    deleteItem(item) {
      let findFileIndex = this.fileList.findIndex((one) => one.id === item.id);
      if (findFileIndex !== -1) {
        this.fileList.splice(findFileIndex, 1);
      }
    },
    handleKB(item) {
      if (item.size >= 1024) {
        return Math.ceil(item.size / 1024 / 1024) + 'M';
      } else {
        return item.size + 'K';
      }
    },
    saveNotice() {
      this.searchData.fileIds = '';
      if (this.fileList.length) {
        this.fileList.forEach((ele) => {
          this.searchData.fileIds = `${this.searchData.fileIds}${ele.id},`;
        });
      }
      this.$refs.formInfo.validate((valid) => {
        if (valid) {
          this.$emit('saveNoticeAction', this.searchData);
        }
      });
    },
  },
  watch: {
    value(val) {
      this.noticeVisible = val;
    },
    'activeRow.id'() {
      this.searchData.title = this.activeRow.title;
      this.searchData.content = this.activeRow.content;
      if (this.activeRow.fileVos !== null && this.activeRow.fileVos !== undefined) {
        this.fileList = this.activeRow.fileVos;
      } else {
        this.fileList = [];
      }
    },
    noticeVisible(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.uploadclass {
  color: var(--color-primary);
}
.file-ul {
  max-height: 100px;
  overflow-y: scroll;
  > li {
    display: flex;
    .file-name {
      display: flex;
      width: 300px;
      .file-name-text {
        width: 265px;
      }
    }
  }
}
.width-big {
  width: 500px;
}
.notice-box {
  width: 570px;
  margin: 0 auto;
}
.button-box {
  margin: 20px auto;
  display: flex;
  justify-content: center;
}
.textarea {
  @{_deep}.ivu-input {
    height: 300px;
  }
}
</style>
