<template>
  <div class="list-card" :class="theme">
    <div class="contrast">
      <ui-image
        :src="itemInfo.traitImg"
        v-viewer="option"
        :data-src="$util.common.imgUrlProxy(itemInfo.sceneImg)"
      />
    </div>
    <div class="info">
      <div class="left">
        <div class="p" v-if="itemInfo.name">
          <div class="title">姓名:</div>
          <div class="val">{{ itemInfo.name }}</div>
        </div>
        <div class="p" v-if="itemInfo.idNumber">
          <div class="title">身份证号:</div>
          <div class="val link" @click.stop="toTrack">{{ itemInfo.idNumber }}</div>
        </div>
        <div class="p">
          <div class="title">时间:</div>
          <div class="val">{{ itemInfo.absTime }}</div>
        </div>
        <div class="p" v-if="!hideSchool">
          <div class="title">学校:</div>
          <div class="val">{{ itemInfo.schoolId | commonFiltering(schoolList)}}</div>
        </div>
        <div class="p">
          <div class="title">抓拍地点:</div>
          <div class="val" :title="itemInfo.deviceName">{{ itemInfo.deviceName }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import c5 from "@/assets/img/target/c-five.png";

export default {
  props: {
    itemInfo: {
      type: Object,
      default: () => {},
    },
    hideSchool: {
      type: Boolean,
      default: false
    },
    theme: {
      type: String,
      default: () => 'light',  //light、dark、transparent
    },
  },
  data() {
    return {
      option: {
        url: "data-src",
      },
    };
  },
  computed: {
		...mapGetters({
			schoolList: 'dictionary/getSchoolList', // 学校
      strangerFlagList: 'dictionary/getStrangerFlagList', // 检测状态
		})
	},
  mounted() {},
  methods: {
    toTrack() {
      let query = {
          archiveNo: this.itemInfo.idNumber,
          noMenu: 1,
      }
      const { href } = this.$router.resolve({
          name: 'real-name-track',
          query
      })
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem('query', JSON.stringify(query))
      this.$util.openNewPage(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.list-card {
  position: relative;
  height: 150px;
  overflow: hidden;
  border-radius: 3px;
  display: flex;
  &.light {
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
    border: 1px solid #ededed;
    background: #f9f9f9;
    .info .title{
      color: rgba(0, 0, 0, 0.6);
    }
    .info .val{
      color: rgba(0, 0, 0, 0.9);
    }
    .link {
      cursor: pointer;
      color: #2c86f8 !important;
    }
  }
  &.dark {
    box-shadow: 0px 4px 26px 0px #010E1E;
    background: #062F61;
    .info .title{
      color: rgba(255, 255, 255, 0.7);
    }
    .info .val{
      color: rgb(255, 255, 255);
    }
    .link {
      cursor: pointer;
      color: #2ECBFF !important;
    }
  }
  &.transparent {
    background: transparent;
    .info .title{
      color: rgb(255, 255, 255);
    }
    .info .val{
      color: rgb(255, 255, 255);
    }
    .link {
      cursor: pointer;
      color: #2ECBFF !important;
    }
    .contrast .border {
      border: 1px solid #000000;
    }
  }
  .contrast {
    height: calc(~"100% - 20px");
    display: flex;
    justify-content: space-between;
    margin: 10px;
    margin-right: 0;
    .ui-image {
      width: 120px;
      height: 100%;
      cursor: pointer;
      /deep/ img{
        height: 100%;
        width: 100%;
      }
    }
  }

  .info {
    display: flex;
    padding: 12px;
    font-size: 12px;
    flex: 1;
    .left {
      flex: 1;
      width: 0;
      .p {
        display: flex;
        .title {
          margin-right: 5px;
          line-height: 22px;
          white-space: nowrap;
        }
        .val {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>