<template>
  <div class="addor-modal">
    <ui-modal v-model="visible" :styles="styles" :title="title">
      <Form ref="formData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="80">
        <FormItem label="任务名称" prop="taskName">
          <Input
            type="text"
            v-model="formData.taskName"
            placeholder="请输入任务名称"
            class="width-input"
            :maxlength="50"
          ></Input>
        </FormItem>
        <FormItem label="考核对象" prop="orgCode">
          <api-organization-tree
            class="width-input"
            :select-tree="formData"
            @selectedTree="selectedOrgTree"
            placeholder="请选择考核对象"
          ></api-organization-tree>
          <span v-if="errorShow" class="font-red ml-sm f-12"
            ><i class="iconfont icon-cuowu f-12 mr-xs"></i>请先选择考核对象</span
          >
        </FormItem>
        <!-- <FormItem label="考核类型" prop="examCycleType">
          <RadioGroup v-model="formData.examCycleType" class="align-flex">
            <Radio label="1">月考核</Radio>
            <Radio label="2">周考核</Radio>
          </RadioGroup>
        </FormItem> -->
        <FormItem label="考核方案" prop="schemeId">
          <Select
            class="width-input"
            placeholder="请选择考核方案"
            filterable
            transfer
            v-model="formData.schemeId"
            @on-change="setSchemeId"
          >
            <Option v-for="(item, index) in moduleList" :key="index" :value="item.id">{{ item.schemeName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="考核内容" prop="list" v-if="formData.list.length > 0">
          <div class="task-content">
            <div
              v-for="(item, index) in formData.list"
              :key="item.id"
              class="d_flex base-text-color mb-md task-content-result"
            >
              <span class="mr-md task-name">{{ item.schemeContentName }}</span>
              <FormItem
                label=""
                :prop="'list.' + index + '.evaluationTaskId'"
                :rules="{
                  type: 'number',
                  required: true,
                  message: '请选择',
                  trigger: 'change',
                }"
              >
                <span>关联检测任务：</span>
                <Select
                  placeholder="请选择"
                  class="mr-sm width-task ellipsis"
                  v-model="item.evaluationTaskId"
                  @on-open-change="changeOpne"
                  @on-change="(val) => changeEvaluation(val, item, index)"
                  filterable
                  transfer
                >
                  <Option
                    v-for="it in item.evaluationTaskVoList"
                    :key="it.evaluationTaskId"
                    :value="it.evaluationTaskId"
                    >{{ it.evaluationTaskName }}</Option
                  >
                </Select>
              </FormItem>
            </div>
          </div>
        </FormItem>
      </Form>
      <template slot="footer">
        <Button @click="visible = false" class="plr-30"> 取 消 </Button>
        <Button class="plr-30" type="primary" :loading="saveModalLoading" @click="handleSubmit('formData')">
          确 定
        </Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
  props: {
    moduleList: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      errorShow: false,
      saveModalLoading: false,
      evaluationTaskList: [],
      taskContentList: [],
      formData: {
        id: '',
        examCycleType: '1',
        taskName: '',
        orgCode: '',
        schemeId: '',
        regionCode: '',
        list: [],
      },
      ruleCustom: {
        taskName: [
          {
            required: true,
            message: '请输入',
            trigger: 'blur',
          },
        ],
        examCycleType: [
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ],
        orgCode: [
          {
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ],
        schemeId: [
          {
            required: true,
            message: '请选择',
            type: 'number',
            trigger: 'change',
          },
        ],
        list: [
          {
            type: 'array',
            required: true,
            message: '请选择',
            trigger: 'change',
          },
        ],
      },
      styles: {
        width: '4.2rem',
      },
    };
  },
  methods: {
    init(val) {
      this.$refs.formData.resetFields();
      this.formData.list = [];
      this.visible = true;
      if (val.id) {
        this.formData = {
          ...val,
        };
      }
    },
    // 考核方案选中
    async setSchemeId(e) {
      this.formData.schemeId = e;
      await this.getTaskContent(this.formData.schemeId);
      this.formData.list = this.taskContentList;
      this.formData.list.map((item, index) => {
        item.evaluationTaskId = '';
        this.getEvaluationTask(item, index);
      });
    },
    // 组织树选中
    selectedOrgTree(val) {
      if (val) {
        this.formData.orgCode = val.orgCode;
        this.formData.regionCode = val.regionCode;
        this.errorShow = false;
        this.formData.list.map((item, index) => {
          item.evaluationTaskId = '';
          this.getEvaluationTask(item, index);
        });
      }
    },
    // 新增考核对象关联的评测任务
    async getEvaluationTask(val, index) {
      let data = {
        orgCode: this.formData.orgCode,
        indexList: val.indexList,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationTask, data);
        this.evaluationTaskList = res.data.data ? res.data.data : [];
        this.formData.list.forEach((item, i) => {
          if (i === index) {
            item.evaluationTaskVoList = this.evaluationTaskList;
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 点击检测任务
    changeOpne(flag) {
      if (flag === true) {
        if (this.formData.orgCode === '') {
          this.errorShow = true;
          return false;
        }
      }
    },
    // 选中检测任务
    changeEvaluation(val, data, index) {
      let name = '';
      if (data.evaluationTaskVoList) {
        data.evaluationTaskVoList.forEach((item) => {
          if (item.evaluationTaskId === val) {
            name = item.evaluationTaskName;
          }
        });
        this.formData.list[index].evaluationTaskName = name;
      }
    },
    // 新增获取考核内容
    async getTaskContent(val) {
      try {
        let res = await this.$http.get(governanceevaluation.queryTaskContent, {
          params: { id: val },
        });
        this.taskContentList = res.data.data ? res.data.data : [];
      } catch (err) {
        console.log(err);
      }
    },
    async handleSubmit(name) {
      try {
        this.$refs[name].validate(async (valid) => {
          if (valid) {
            this.saveModalLoading = true;
            let data = {
              id: this.formData.id ? this.formData.id : ' 0',
              orgCode: this.formData.orgCode,
              regionCode: this.formData.regionCode,
              taskName: this.formData.taskName,
              schemeId: this.formData.schemeId,
              examCycleType: this.formData.examCycleType,
              list: this.formData.list,
            };
            let res = await this.$http.post(
              this.title === '新增考核任务'
                ? governanceevaluation.addExamSchemeTask
                : governanceevaluation.updateEvaluationTask,
              data,
            );
            this.$Message.success(res.data.msg);
            this.saveModalLoading = false;
            this.visible = false;
            this.$emit('update');
          }
        });
      } catch (err) {
        this.saveModalLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.addor-modal {
  .width-input {
    width: 400px;
  }
  .width-time {
    flex: 1;
  }
  .width-task {
    width: 220px;
  }
  .form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .task-content {
    max-height: 340px;
    overflow: auto;
    .task-content-result:last-child {
      margin-bottom: 5px;
    }
    .task-name {
      display: inline-block;
      width: 220px;
    }
  }
}
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
}
@{_deep}.ivu-modal-body {
  padding: 10px 50px !important;
}

@{_deep}.api-organization-tree {
  width: 400px;
  .ivu-dropdown {
    width: 400px;
    .ivu-dropdown-rel {
      .select-width {
        width: 400px;
      }
    }
  }
}
@{_deep}.ivu-date-picker {
  width: 100%;
}
</style>
