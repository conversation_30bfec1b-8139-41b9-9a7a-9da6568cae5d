<template>
  <div>
    <div class="card-swiper-container" v-if="list.length > 0">
      <swiper class="card-swiper" :options="swiperOption">
        <swiper-slide v-for="(item, index) in list" :key="index">
          <div class="vehicle-container">
            <div class="vehicle-info">
              <ui-image class="vehicle-img" :src="type === 1 ? item.sceneImg : item.photoUrl" viewer type="vehicle" />
              <!-- <img class="vehicle-img" :src="type === 1 ? item.sceneImg : item.photoUrl" alt="" /> -->
              <p class="plate-number">{{ item.plateNo }}</p>
            </div>
            <div class="info-content" v-if="type === 1">
              <p>
                <span class="label">驾乘方式</span><b>{{ item.driverFlag === 1 ? '主驾' : item.driverFlag === 2 ? '副驾' : '暂无' }}</b>
              </p>
              <p>
                <span class="label">最近时间</span><b>{{ item.absTime }}</b>
              </p>
              <p>
                <span class="label">最近地点</span><b :title="item.captureAddress" class="ellipsis car-address">{{ item.captureAddress }}</b>
              </p>
            </div>
            <div class="info-content" v-else>
              <p>
                <span class="label">车牌颜色</span><b>{{ item.plateColor | commonFiltering(licensePlateColorList) }}</b>
              </p>
              <p>
                <span class="label">车辆品牌</span><b>{{ item.vehicleBrandCN }}</b>
              </p>
              <p>
                <span class="label">车身颜色</span><b>{{ item.jdccsysdm | commonFiltering(bodyColorList) }}</b>
              </p>
            </div>
          </div>
        </swiper-slide>
        <div class="swiper-pagination" slot="pagination"></div>
      </swiper>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="list.length === 0" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import { swiper, swiperSlide } from 'vue-awesome-swiper'
export default {
  components: { swiper, swiperSlide },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    },
    // 1驾乘车辆/2名下车辆
    type: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      swiperOption: {
        pagination: {
          el: '.swiper-pagination',
          clickable: true,
          renderBullet(index, className) {
            return `<span class="${className} swiper-pagination-bullet-custom">${index + 1}</span>`
          }
        }
      }
    }
  },
  watch: {
    list: {
      handler (val) {
        if (val.length > 0) {
          console.log('-------------------', val)
          this.swiperOption.pagination = {
            el: '.swiper-pagination',
            clickable: true,
            renderBullet(index, className) {
              return `<span class="${className} swiper-pagination-bullet-custom">${index + 1}</span>`
            }
          }
        }
      },
      immediate: true,
      deep: true
    },
  },
  computed: {
    ...mapGetters({
      licensePlateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
      bodyColorList: 'dictionary/getBodyColorList' //车身颜色
    })
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="less" scoped>
.card-swiper-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .card-swiper {
    margin-left: 0;
    margin-right: 0;
    padding-bottom: 30px;
    .swiper-pagination {
      text-align: left;
      display: flex;
      bottom: 0;
    }
    /deep/ .swiper-pagination-bullet-custom {
      width: 18px;
      height: 18px;
      box-sizing: border-box;
      font-size: 12px;
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #2c86f8;
      background: #fff;
      color: #2c86f8;
      opacity: 1;
      margin: 0;
      margin-right: 5px;
      &.swiper-pagination-bullet-active {
        color: #fff;
        background: #2c86f8;
      }
    }
  }
  /deep/ .vehicle-container {
    display: flex;
    align-items: center;
    height: 180px;
    width: 100%;
    .vehicle-info {
      display: flex;
      flex-direction: column;
      margin-right: 25px;
      height: 100%;
      .vehicle-img {
        width: 200px !important;
        height: calc(~'100% - 30px');
      }
      .plate-number {
        width: 100%;
        color: #fff;
        line-height: 30px;
        text-align: center;
        height: 30px;
        font-size: 18px;
        background: #2c86f8;
        white-space: nowrap;
      }
    }
    .info-content {
      padding-left: 23px;
      height: 100%;
      border-left: 1px dashed #d3d7de;
      p {
        display: flex;
        font-size: 14px;
        padding: 1% 0;
        color: rgba(0, 0, 0, 0.9);
        .label {
          text-align-last: justify;
          text-align: justify;
          text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
          width: 56px;
          font-weight: bold;
          white-space: nowrap;
          color: #2c86f8;
          margin-right: 15px;
        }
        b {
          font-weight: normal;
          word-break: break-all;
          &.half {
            width: 110px;
          }
          &.weight {
            font-size: 20px;
            line-height: 1;
            font-weight: bold;
          }
          &.zy-width {
            width: 190px;
          }
          &.address-width {
            width: 420px;
          }
          &.car-address {
            width: 160px;
          }
        }
      }
    }
  }
}
@media screen and (max-height: 960px) {
  .card-swiper-container {
    .vehicle-container {
      height: 160px;
      .vehicle-info {
        .vehicle-img {
          width: 180px !important;
        }
      }
    }
  }
}

.swiper-container {
  flex: 1 !important;
}
</style>
