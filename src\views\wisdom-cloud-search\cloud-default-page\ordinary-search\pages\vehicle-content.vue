<template>
  <section class="main-container">
    <div class="search-bar">
      <search-vehicle ref="searchBar" @search="searchHandle" @reset="resetHandle" />
    </div>
    <div class="table-container">
        <div class="btn-list">
            <Button class="mr" @click="handleSort('absTime')" size="small">
                <Icon type="md-arrow-round-down" v-if="!timeUpDown"/> 
                <Icon type="md-arrow-round-up" v-else/>
                时间排序
            </Button>
        </div>
      <div class="table-content">
        <div class="list-card box-1" v-for="(item, index) in dataList" :key="index">
          <div class="collection paddingIcon">
            <div class="bg"></div>
            <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
            <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
          </div>
          <p class="img-content">
            <span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
            <ui-image :src="item.traitImg" alt="动态库" @click.native="faceDetailFn(item, index)" />
            <!-- <img :src="item.traitImg" alt="" v-viewer /> -->
            <b class="shade vehicle">
              <plate-number :plateNo="item.plateNo"></plate-number>
            </b>
          </p>
          <div class="bottom-info">
            <time>
              <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                <i class="iconfont icon-time"></i>
              </Tooltip>
              {{ item.absTime }}
            </time>
            <p>
              <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                <i class="iconfont icon-location"></i>
              </Tooltip>
              <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
            </p>
            <!-- <time><i class="iconfont icon-time"></i>{{item.absTime}}</time>
            <p><i class="iconfont icon-location"></i>{{item.location}}</p> -->
          </div>
          <!-- <div class="operate-bar">
            <p class="operate-content">
              <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" /> -->
              <!-- <ui-btn-tip content="收藏" icon="icon-shoucang" /> -->
              <!-- <ui-btn-tip v-if="item.myFavorite == '1'" content="收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
              <ui-btn-tip v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" /> -->
              <!-- <ui-btn-tip content="分析" icon="icon-fenxi" />
              <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
            </p>
          </div> -->
        </div>
        <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
      </div>
      <ui-empty v-if="dataList.length === 0"></ui-empty>
      <ui-loading v-if="listLoading"></ui-loading>
      <!-- 分页 -->
      <ui-page :current="pageInfo.pageNumber" :total="pageInfo.total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[27, 54, 81, 108]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
    </div>
    <!-- 车辆详情 -->
    <!-- <vehicle-detail v-show="vehicleShow" class="vehicleDetail" ref="vehicleDetail" @close="vehicleShow = false" 
    @preDetial="preDetial" @nextDetail="nextDetail"/> -->
    <details-vehicle-modal 
        v-if="vehicleShow" 
        ref='vehicleDetail' 
        @prePage="prePage"
        @nextPage="nextPage" 
        @close="vehicleShow = false"></details-vehicle-modal>
  </section>
</template>
<script>
import vehicleDetail from '@/components/detail/vehicle'
import detailsVehicleModal from '@/components/detail/details-vehicle-modal.vue'
import searchVehicle from '../components/search-vehicle.vue'
import plateNumber from '../components/plate-number'
import { vehicleRecordSearch } from '@/api/wisdom-cloud-search'
import { getVehicleBaseInfoByplateNo } from '@/api/vehicleArchives'
import { addCollection, deleteMyFavorite } from '@/api/user'
import { myMixins } from '../../components/mixin/index.js';
export default {
  name: 'faceContent',
  components: {
    plateNumber,vehicleDetail,
    searchVehicle,
    detailsVehicleModal
  },
  props: {
    // 首页参数
    indexSearchData: {
      type: Object,
      default: () => {}
    }
  },
  mixins: [myMixins], //全局的mixin
  data() {
    return {
      currentIndex: 0,
      vehicleShow: false,
      listLoading: false,
      selectMenuItemId: null,
      formRight: {},
      queryParam: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
        total: 0
      },
      dataList: [],
      timeUpDown: false
    }
  },
  activated() {
    this.$nextTick(() =>{
        this.queryList()
    })
  },
  created() {
  },
  methods: {
    /**
     * 列表查询
     */
    getDataList(page = 0) {
      // if (!this.queryParam.keyWords && this.queryParam.features.length == 0) {
      //   this.tableList = []
      //   this.$Message.warning('请输入关键词或上传图片已搜索')
      //   return
      // }

      this.listLoading = true
      vehicleRecordSearch(this.queryParam)
        .then(res => {
          const { total, entities } = res.data
          this.pageInfo.total = total
          this.dataList = entities;
          if(page == 1) {
            this.$refs.vehicleDetail.prePage(this.dataList)
          }else if(page == 2) {
            this.$refs.vehicleDetail.pageLength(this.dataList.length)
            this.$refs.vehicleDetail.nextPage(this.dataList)
          }
        })
        .catch(err => {
          console.error(err)
        })
        .finally(() => {
          this.listLoading = false
          this.isActivated = true
        })
    },
    /**
     * 首页调用方法
     */
    indexSearchHandle() {
      this.pageInfo.pageNumber = 1
      this.queryParam = {
        pageNumber: 1,
        pageSize: 27,
        total: 0,
        keyWords: this.indexSearchData.keyWords
      }
      // this.getDataList()
      this.$refs.searchBar.resetHandle()
    },
    /**
     * 条件搜索
     */
    searchHandle() {
        this.pageInfo.pageNumber = 1;
        this.queryList();
    },
    queryList(page = 0) {
        this.$refs.searchBar.visible = false
        let queryParam = this.$refs.searchBar.queryParam
        queryParam = { ...queryParam, ...this.$parent.indexSearchData, ...this.pageInfo }
        queryParam.keyWords = this.$parent.indexSearchData.keyWords
        this.queryParam = queryParam;
        this.dispTime()
        // 处理已选择设备
        var ids = []
        this.queryParam.selectDeviceList.forEach(item => {
            ids.push(item.deviceId)
        })
        this.queryParam.devices = ids
        this.getDataList(page)
    },
    /**
     * 重置
     */
    resetHandle() {
        this.pageInfo = {
            pageNumber: 1,
            pageSize : 27
        };
        this.queryParam = { 
            ...this.$parent.indexSearchData, 
            ...this.pageInfo,
            ...this.$refs.searchBar.queryParam,
        }
        this.dispTime()
        this.getDataList()
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size
      this.queryList()
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.queryList()
    },
    isInteger(obj) {
      var r = /^\+?[1-9][0-9]*$/ //正整数
      return r.test(obj)
    },

    /**
     * 跳转到一车一档页面
     */
    archivesPage(row) {
      console.log(row)
      getVehicleBaseInfoByplateNo(row.plateNo).then(res => {
        console.log(res)
        if(res.data.archiveNo){
          const { href } = this.$router.resolve({
            name: 'vehicle-archive',
            query: { 
              archiveNo: JSON.stringify(res.data.archiveNo),
              plateNo: JSON.stringify(row.plateNo),
              source: 'car' 
            }
          })
          window.open(href, '_blank')
        }else{
          this.$Message.error('尚未查询到该辆车的档案信息')
        }
      })
    },
    faceDetailFn(row, index) {
        this.currentIndex = index
        this.vehicleShow = true
        this.$nextTick(() => {
            this.$refs.vehicleDetail.init(row, this.dataList, index, this.pageInfo.pageNumber)
        })
    },
    /**
     * 收藏
     */
    collection( data, flag ) {
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType: 6,
      }
      if(flag == 1){
        addCollection(param).then(res => {
          this.$Message.success("收藏成功");
          this.getDataList()
        })
      }else{
        deleteMyFavorite(param).then(res => {
          this.$Message.success("取消收藏成功");
          this.getDataList()
        })
      }
    },
    /**
     * 上一个
     */
     preDetial() {
      if (this.currentIndex == 0 ) {
        if (this.pageInfo.pageNumber == 1) {
          this.$Message.warning("已经是第一个了")
          return
        }else{
          this.pageChange(this.pageInfo.pageNumber-1)
          setTimeout(()=> {
            this.faceDetailFn(this.dataList[(this.dataList.length-1)], (this.dataList.length-1))
          }, 200)
        }
      }else {
        this.faceDetailFn(this.dataList[(this.currentIndex-1)], (this.currentIndex-1))
      } 

    },
    prePage(pageNum) {
        if(pageNum < 1) {
            this.$Message.warning("已经是第一个了")
            return
        } else {
            this.pageInfo.pageNumber = pageNum;
            this.queryList(1)
        }
    },
    /**
     * 下一个
     */
     async nextPage(pageNum) {
        this.pageInfo.pageNumber = pageNum;
        let num = this.pageInfo.pageNumber;
        let size = this.pageInfo.pageSize;
        if(this.total <= num*size) {
            this.$Message.warning("已经是最后一个了")
            return
        }else{
            this.queryList(2)
        }
    },
    nextDetail() {
      if(this.currentIndex == (this.dataList.length - 1)){
        var num = this.pageInfo.pageNumber
        var size = this.pageInfo.pageSize
        var total = this.pageInfo.total
        if (total <= num*size) {
          this.$Message.warning("已经是最后一个了")
          return
        }else{
          this.pageChange(this.pageInfo.pageNumber+1)
          setTimeout(()=> {
            this.faceDetailFn(this.dataList[0], 0)
          }, 200)
        }
      }else{
        this.faceDetailFn(this.dataList[(this.currentIndex+1)], (this.currentIndex+1))
      }
    },
  }
}
</script>
<style lang="less" scoped>
@import 'style/index';

.vehicleDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0,0,0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
</style>
