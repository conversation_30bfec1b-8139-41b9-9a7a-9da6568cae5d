<!--
    * @FileDescription: 搜索条件
    * @Author: H
    * @Date: 2024/01/31
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-07-24 11:57:11
 -->
<template>
  <div class="search_box">
    <div class="title">
      <p>{{ title }}</p>
    </div>
    <div
      class="search_condition"
      :class="{ 'search_condition-pack': packUpDown }"
    >
      <div class="search_form">
        <Form ref="form" :model="formData" :label-width="85">
          <template>
            <div class="search_wrapper">
              <div class="search_title">
                <p class="search_strut"><span class="rules">*</span>设备资源</p>
                :
              </div>
              <ul class="search_content">
                <li class="active-area-sele" @click="handleSelemodel">
                  选择设备/已选({{ formData.deviceIds.length }})
                </li>
                <li class="area-list" @click="handleSeleArea">
                  <ui-icon type="gateway" />
                </li>
              </ul>
            </div>
            <div class="wrapper">
              <p class="wrapper-title"><span class="rules">*</span>时间:</p>
              <ui-quick-date
                style="width: 100%"
                ref="quickDateRef"
                v-model="formData.dateType"
                type="month"
                border
                @change="dataRangeHandler"
              />
            </div>
            <div class="wrapper">
              <p class="wrapper-title">
                <span class="rules">*</span>最少出现次数:
              </p>
              <Input
                v-model="formData.appearNum"
                placeholder="请输入"
                class="wrapper-input"
              ></Input>
              <span class="unit">次</span>
            </div>
          </template>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div
      class="footer"
      :class="{ packArrow: packUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      :isCheckShow="true"
      @selectData="selectData"
    />
  </div>
</template>

<script>
import uiUploadImg from "@/components/ui-upload-img/index";
import { mapActions, mapGetters } from "vuex";
import { getConfigDate } from "@/util/modules/common";
import { queryParamDataByKeys } from "@/api/config";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "人员频繁出没",
    },
  },
  components: {
    uiUploadImg,
  },
  data() {
    return {
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      formData: {
        deviceIds: [],
        appearNum: "",
        startDate: "",
        endDate: "",
        dateType: 2,
      },
      packUpDown: false,
      similarity: 85,
      algorithmType: "1",
      urlList: [],
      selectDeviceList: [],
      checkedLabels: [], // 已选择的标签
      maxDeviceIdsNum: 0,
    };
  },
  watch: {
    packUpDown: {
      handler(val) {
        this.$nextTick(() => {
          let box = document.querySelector(".search_condition");
          if (val) {
            box.style.overflow = "hidden";
          } else {
            setTimeout(() => {
              box.style.overflow = "inherit";
            }, 200);
          }
        });
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
    }),
  },
  async created() {
    var param = ["ICBD_TACTICAL_CONFIG"];
    await queryParamDataByKeys(param).then((res) => {
      if (res.data.length > 0) {
        this.maxDeviceIdsNum = JSON.parse(
          res.data?.[0]?.paramValue || {}
        )?.personFrequence?.maxDeviceNumber;
      }
    });
  },
  mounted() {
    this.dataRangeHandler(this.$refs.quickDateRef.getDate());
  },
  methods: {
    handlePackup() {
      this.packUpDown = !this.packUpDown;
      this.$emit("packBox");
    },
    handleSearch() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.formData.dateType == 1) {
            var arr = getConfigDate(0);
            this.formData.startDate = arr[0] + " 00:00:00";
            this.formData.endDate = this.$dayjs(new Date()).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
          if (this.formData.dateType == 2) {
            var arr = getConfigDate(-6);
            this.formData.startDate = arr[0] + " 00:00:00";
            this.formData.endDate = this.$dayjs(new Date()).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
          if (this.formData.dateType == 3) {
            var arr = getConfigDate(-29);
            this.formData.startDate = arr[0] + " 00:00:00";
            this.formData.endDate = this.$dayjs(new Date()).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }

          if (this.formData.deviceIds.length == 0) {
            this.$Message.warning("请选择设备");
            return;
          }
          if (this.formData.startDate == "" || this.formData.endDate == "") {
            this.$Message.warning("请选择时间");
            return;
          }
          if (this.formData.appearNum == "") {
            this.$Message.warning("请输入最少出现次数");
            return;
          }

          this.packUpDown = true;
          let params = { ...this.formData };
          this.$emit("searchList", params);
        }
      });
    },
    /**
     * 选择设备
     */
    handleSelemodel() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      if (this.maxDeviceIdsNum < list.length) {
        this.$Message.warning(
          `设备资超过最大设置数，最大设置数为${this.maxDeviceIdsNum}`
        );
        return;
      }
      this.selectDeviceList = list;
      this.formData.deviceIds = list.map((item) => item.deviceGbId);
    },
    handleSeleArea() {
      this.$emit("seleArea");
    },
    // 时间
    dataRangeHandler(val) {
      this.formData.startDate = val.startDate;
      this.formData.endDate = val.endDate;
    },
    // 重置
    handleReset() {
      this.formData = {
        deviceIds: [],
        appearNum: "",
        dateType: 2,
      };
      this.$nextTick(() => {
        this.dataRangeHandler(this.$refs.quickDateRef.getDate());
        this.$refs.quickDateRef.setDefaultDate(); // 清空日期组件的自定义时间，在下次选择自定义时回显为空
      });
      this.selectDeviceList = [];
    },
  },
};
</script>

<style lang='less' scoped>
@import "./style/index";
.search_box {
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  // min-height: 254px;
  // overflow: hidden;
  // filter: blur(0px);
  .search_condition {
    // padding-bottom: 10px;
    max-height: 360px;
    transition: max-height 0.2s ease-out;
    // overflow: hidden;
    .search_form {
      padding: 0 10px;
      margin-top: 10px;
      &_type {
        display: flex;
        border: 1px solid #2c86f8;
        border-radius: 4px;
        margin-bottom: 15px;
        .typeTab {
          background: #ffffff;
          color: #2c86f8;
          width: 175px;
          height: 34px;
          text-align: center;
          line-height: 34px;
          font-size: 14px;
          cursor: pointer;
        }
        .typeActive {
          background: #2c86f8;
          color: #fff;
        }
      }
      .btn-group {
        .btnwidth {
          width: 258px;
        }
      }
      .search_wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 15px;
        .search_title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-right: 10px;
          display: flex;
        }
        .search_strut {
          text-align: right;
          // width: 75px;
          // font-weight: bold;
        }
        .search_content {
          display: flex;
          flex-wrap: wrap;
          flex: 1;
          .active-area-sele {
            width: 200px;
            height: 34px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
            line-height: 34px;
            cursor: pointer;
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
          }
          .area-sele {
            border: 1px solid #d3d7de;
            color: rgba(0, 0, 0, 0.6);
            background: none;
          }
          .area-list {
            width: 34px;
            height: 34px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #d3d7de;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.6);
            margin-left: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
          }
        }
      }
      .wrapper {
        display: flex;
        margin-bottom: 15px;
        align-items: center;
        /deep/.ivu-date-picker {
          width: 280px !important;
        }
        .wrapper-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          min-width: 60px;
        }
        .choose-time {
          margin-left: 10px;
        }
        .custom-time {
          height: 34px;
          line-height: 34px;
        }
        .wrapper-input {
          flex: 1;
        }
        &:first-child {
          .wrapper-title {
            width: 40px;
          }
        }
        .unit {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.8);
          margin-left: 5px;
        }
      }
    }
    .slider-content {
      margin: 15px 0;
      .similarity {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .search_condition-pack {
    max-height: 0px;
    transition: max-height 0.2s ease-out;
  }
  .footer {
    display: flex;
    justify-content: center;
    cursor: pointer;
    // margin: 10px 0;
    img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
  .packArrow {
    img {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
  .rules {
    color: red;
  }
  /deep/ .ivu-form-item {
    margin-bottom: 20px;
  }
  /deep/ .ivu-slider {
    flex: 1;
  }
  /deep/ .ivu-select-dropdown {
    left: 0 !important;
  }
}
</style>
