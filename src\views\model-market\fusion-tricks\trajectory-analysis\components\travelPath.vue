<!--
    * @FileDescription: 出行轨迹
    * @Author: H
    * @Date: 2022/12/21
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="travel-path">
        <div class="title">
            <p>出行轨迹</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <div class="extra">
            <time-select @dataRangeHandler="handleTravelDataRange"></time-select>
        </div>
        <div class="travel-info">
            <TimelineLevel ref='timeLineSelect' :timeLineList="timeLineList" />
            <div class="list">
                <div v-for="(item, index) in travelTypeList" :key="index" :class="{ active: item.key === travelTypeNameKey }" class="ivu-tabs-tab" @click="tabClick(item)">
                    <span>{{ item.travelName }}</span>
                </div>
            </div>
            <ui-table ref="table" height="350" :loading="tableLoading" :columns="columnsMap[travelTypeNameKey].list" :data="tableData"></ui-table>
            <div class="page-content">
                <ui-page :current="pageInfo.pageNumber" :total="total" :page-size="pageInfo.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
            </div>
        </div>
    </div>
</template>

<script>
import TimeSelect from '@/views/holographic-archives/components/time-select.vue';
import TimelineLevel from '@/views/holographic-archives/components/time-line-level.vue';
export default {
    name: '',
    components:{
        TimeSelect,
        TimelineLevel  
    },
    data () {
        return {
            timeLineList: [],
            tableLoading: false,
            tableData:[],
            pageInfo: {
                pageNumber:1,
                pageSize: 10
            },
            total:0,
            travelTypeNameKey: 'train',
            travelTypeList: [
                {
                    travelName: '火车出行',
                    key: 'train'
                },
                {
                    travelName: '汽车出行',
                    key: 'vehicle'
                },
                {
                    travelName: '飞机出行',
                    key: 'aircraft'
                },
                {
                    travelName: '酒店入住',
                    key: 'hotel'
                },
                {
                    travelName: '上网记录',
                    key: 'internetBar'
                }
            ],
            columnsMap: {
                train: {
                    list: [
                        { title: '序号', width: 68, type: 'index', key: 'index' },
                        { title: '车次', width: 311, key: 'cc' },
                        { title: '座位号', width: 283, key: 'zwh' },
                        { title: '出行时间', minWidth: 200, key: 'ccsj' },
                        { title: '起止地点（始发地、目的地）', width: 415, key: 'startAndEndAddresses' }
                    ],
                    // handler: trainTravelTrajectoryPageList
                },
                vehicle: {
                    list: [
                        { title: '序号', width: 68, type: 'index', key: 'index' },
                        { title: '车牌号', width: 311, key: 'jdchphm' },
                        { title: '座位号', width: 483, key: 'zwh' },
                        { title: '出行时间', minWidth: 200, key: 'ccrq' },
                        { title: '起止地址（始发地、目的地）', width: 415, key: 'startAndEndAddresses' }
                    ],
                    // handler: carTravelTrajectoryPageList
                },
                aircraft: {
                    list: [
                        { title: '序号', width: 68, type: 'index', key: 'index' },
                        { title: '航班号', width: 311, key: 'cc' },
                        { title: '座位号', width: 415, key: 'zcw' },
                        { title: '出发日期', width: 483, key: 'ddcfRq' },
                        { title: '到达日期', width: 483, key: 'ddddRq' },
                        { title: '起止地址（始发地、目的地）', minWidth: 570, key: 'startAndEndAddresses' }
                    ],
                    // handler: aircraftTravelTrajectoryPageList
                },
                hotel: {
                    list: [
                        { title: '序号', width: 68, type: 'index', key: 'index' },
                        { title: '酒店名称', width: 311, key: 'lgmc' },
                        { title: '房间号', width: 120, key: 'rzfh' },
                        { title: '入住时间', width: 300, key: 'rzsj' },
                        { title: '退房时间', width: 300, key: 'tfsj' },
                        { title: '酒店地址', minWidth: 200, key: 'xz' }
                    ],
                    // handler: hotelStayTrajectoryPageList
                },
                internetBar: {
                    list: [
                        { title: '序号', width: 68, type: 'index', key: 'index' },
                        { title: '网点名称', width: 311, key: 'yycsDwmc' },
                        { title: '上机时间', width: 300, key: 'kssj' },
                        { title: '下机时间', width: 300, key: 'jssj' },
                        { title: '网吧地址', minWidth: 200, key: 'yycsDzmc' }
                    ],
                    // handler: onlineRecordPageList
                }
            },
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        handleCancel() {
            this.$emit('cancel');
        },
        tabClick(item) {
            this.travelTypeNameKey = item.key;
            this.pageInfo.pageNumber = 1;
        },
        handleTravelDataRange(dataRange, times = []) {
            if (dataRange == 4 && times.length == 0) {
                return
            };
            const [startDate = '', endDate = ''] = times;
            // this.formData.startDate = startDate;
            // this.formData.endDate = endDate;
            // this.formData.dateType = dataRange;
        },
        pageChange() {

        },
        pageSizeChange() { },
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.travel-path{
    z-index: 99;
    height: 814px;
    width: calc( ~'100% - 400px' );
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    filter: blur(0px);
    position: absolute;
    top: 10px;
    right: 10px;
    .extra{
        display: flex;
        justify-content: end;
        padding-top: 10px;
        margin-right: 30px;
    }
    .travel-info{
        width: 100%;
        position: relative;
        padding: 0 20px;
        .list {
            height: 34px;
            line-height: 34px;
            margin-bottom: 20px;
            .ivu-tabs-tab {
                float: left;
                border: 1px solid #2c86f8;
                border-right: none;
                padding: 0 15px;
                color: #2c86f8;
                &:hover {
                    background: #2c86f8;
                    color: #ffffff;
                    cursor: pointer;
                }
                &:first-child {
                    border-top-left-radius: 4px;
                    border-bottom-left-radius: 4px;
                    border-right: none;
                }
                &:last-child {
                    border-top-right-radius: 4px;
                    border-bottom-right-radius: 4px;
                    border-right: 1px solid #2c86f8;
                }
            }
            .active {
                background: #2c86f8;
                color: #fff;
            }
        }
    }
}
</style>
