<template>
  <ui-modal v-model="visible" :title="'算法详情'" :r-width="690">
    <div class="form-box">
      <Form ref="algorithmRef" :label-width="120">
        <FormItem prop="algorithmName" label="算法名称：">
          <div class="info-box">{{ detailInfo.name }}</div>
        </FormItem>
        <FormItem prop="modalName" label="模型名称：">
          <div class="info-box">{{ detailInfo.llmModelName }}</div>
        </FormItem>
        <FormItem label="提示词：">
          <div class="call-word-input-boxs">
            <div v-for="(item, index) in detailInfo.prompts" :key="index">
              <div class="info-box">{{ item }}</div>
            </div>
          </div>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>

<script>
export default {
  name: "DetailModal",
  props: {},
  data() {
    return {
      visible: false,
      detailInfo: {
        name: "",
        llmModelName: "",
        prompts: [],
      },
    };
  },
  methods: {
    show(info) {
      this.detailInfo = { ...info };
      this.visible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.info-box {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.call-word-input-boxs {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 380px;
  overflow: scroll;
}
</style>
