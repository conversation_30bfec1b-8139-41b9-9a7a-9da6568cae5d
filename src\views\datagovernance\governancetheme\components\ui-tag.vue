<template>
  <div style="position: relative">
    <div class="tag" v-for="(item, index) in data" :key="item.id + '-' + index">
      <span class="tag-title">{{ item.checkColumnValue }}</span>
      <i class="tag-close pointer" @click="clickClose(item)">
        <span class="tag-icon"></span>
      </i>
    </div>
    <div class="tag-add pointer" @click="clickAdd">
      <span class="icon-font icon-dianjishangchuantupian-line f-12 tag-add-title"></span>
      <span class="tag-add-title ml-xs">新增</span>
    </div>
  </div>
</template>
<script>
//ui-tag
export default {
  props: {
    /**
     * [ {id: 123, title:"重点线路"} ]
     */
    data: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    clickClose(item) {
      this.$emit('close', item);
    },
    clickAdd() {
      this.$emit('add');
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tag-add {
  width: 100px;
  height: 34px;
  line-height: 34px;
  margin-right: 10px;
  margin-bottom: 10px;
  text-align: center;
  display: inline-block;
  font-size: 14px;
  opacity: 1;
  border-radius: 4px;
  border: 1px solid var(--color-primary);
  .tag-add-title {
    color: var(--color-primary);
  }
}
.tag {
  display: inline-block;
  position: relative;
  height: 34px;
  line-height: 34px;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 0 22px 0 22px;
  font-size: 14px;
  background: var(--color-primary);
  text-align: center;
  white-space: nowrap;
  opacity: 1;
  border-radius: 4px;

  .tag-close {
    position: absolute;
    top: -7px;
    right: -7px;
    height: 14px;
    width: 14px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .tag-icon {
      display: inline-block;
      height: 2px;
      width: 7px;
      background: var(--color-primary);
    }
  }

  .tag-title {
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    cursor: default;
  }
}
</style>
