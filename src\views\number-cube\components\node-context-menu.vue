<template>
  <div
    class="node-context-menu"
    ref="nodeContextMenuRef"
    :style="{
      top: position.y + 'px',
      left: position.x + 'px',
    }"
    v-show="visible"
    @click="close"
  >
    <div class="circle">
      <div
        v-for="(item, index) in menuList"
        :class="['circle-item', firstMenuIndex === index ? 'active' : '']"
        :key="index"
        @mouseenter="secondMenuShow(item, index)"
        @click.stop="menuClick(item, index)"
      >
        <div
          class="menu-text-box"
          :style="{
            transform: getTextRotate(index),
          }"
        >
          <img :src="getIcon(item)" alt="" />
          <span class="menu-label">
            {{ getLabel(item) }}
          </span>
        </div>
      </div>
      <div class="circle-center">
        <img class="circle-img" v-if="showImg(node.img)" :src="node.img" />
        <graphFontIcon v-else :iconId="node.img" />
        <p class="node-label">{{ node.label }}</p>
      </div>
    </div>
    <div
      class="circle-second"
      v-show="secondShow"
      :style="{
        transform: `translate(-50%, -50%) rotate(${childrenMenuRorate}deg)`,
      }"
    >
      <div
        class="circle-second-item"
        v-for="(itm, i) in childrenMenu"
        :key="i"
        :style="{
          transform: getSecondItemRotate(i),
          animationDelay: getAnimaionDelay(i),
        }"
        @click.stop="secondMenuClick(itm, i)"
      >
        <div
          class="menu-text-box"
          :style="{
            transform: getSecondTextRotate(i),
          }"
        >
          <span
            class="menu-label"
            :title="itm.label.length > 4 ? itm.label : ''"
          >
            {{
              itm.label.length > 4 ? itm.label.slice(0, 4) + "..." : itm.label
            }}
          </span>
        </div>
      </div>
      <div
        v-if="relationExcavateMoreShow"
        class="menu-label-more"
        @mouseleave="handleMouseLeave"
      >
        <div
          class="menu-label-more-item"
          @click.stop="secondMenuClick(item)"
          v-for="(item, index) in relationExcavateMoreList"
          :key="index"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="circle-second-center"></div>
    </div>
  </div>
</template>
<script>
import NodeDetail from "@/assets/img/number-cube/node-detail.png";
import RelationalViews from "@/assets/img/number-cube/relational-views.png";
import RelationshipExpansion from "@/assets/img/number-cube/relationship-expansion.png";
import ConnectionAnalysis from "@/assets/img/number-cube/connection-analysis.png";
import Lock from "@/assets/img/number-cube/lock.png";
import Unlock from "@/assets/img/number-cube/unlock.png";
import DeleteNode from "@/assets/img/number-cube/delete-node.png";
import TechnicalTactics from "@/assets/img/number-cube/technical-tactics.png";
import FindSources from "@/assets/img/number-cube/find-sources.png";
import graphFontIcon from "@/components/graph-font-icon.vue";
import {
  getMiningConfig,
  enableMiningRelationPageList,
} from "@/api/number-cube";
export default {
  props: {
    value: {
      type: Boolean,
    },
    position: {
      type: Object,
      default: () => {
        return {
          x: 0,
          y: 0,
        };
      },
    },
    node: {
      required: true,
      default: () => {
        return {
          node: {},
          model: {},
        };
      },
    },
  },
  data() {
    return {
      visible: false,
      positionTop: "0",
      menuList: [
        {
          label: "对象详情",
          icon: NodeDetail,
          value: "objectDetail",
        },
        {
          label: "关系视图",
          icon: RelationalViews,
          value: "relationalViews",
        },
        {
          label: "关系拓展",
          icon: RelationshipExpansion,
          children: [
            {
              label: "一度关系",
              level: 1,
              value: "getRelationships",
            },
            {
              label: "二度关系",
              level: 2,
              value: "getRelationships",
            },
            {
              label: "三度关系",
              level: 3,
              value: "getRelationships",
            },
          ],
        },
        {
          label: "连接分析",
          icon: ConnectionAnalysis,
          value: "lianjiefenxi",
        },
        {
          label: "锁定",
          icon: Lock,
          value: "lock",
        },
        {
          label: "删除",
          icon: DeleteNode,
          value: "deleteNode",
        },
        // {
        //   label: "技战法",
        //   icon: TechnicalTactics,
        //   children: [
        //     {
        //       label: "轨迹分析",
        //       name: "trajectory-analysis",
        //       value: "technicalTactics",
        //     },
        //     {
        //       label: "同行分析",
        //       name: "peerAnalysis",
        //       value: "technicalTactics",
        //     },
        //     {
        //       label: "落脚点",
        //       name: "foothold",
        //       value: "technicalTactics",
        //     },
        //   ],
        // },
        {
          label: "关系挖掘",
          icon: TechnicalTactics,
          children: [
            // {
            //   label: "更多",
            //   name: "relation-excavate-more",
            //   value: "relationExcavate",
            // },
            // {
            //   label: "人场所-居住",
            //   name: "relation-excavate-man-place",
            //   value: "relationExcavate",
            // },
            // {
            //   label: "人人-情侣",
            //   name: "relation-excavate-man-man",
            //   value: "relationExcavate",
            // },
            // {
            //   label: "新增",
            //   name: "relation-excavate-add",
            //   value: "relationExcavate",
            // },
          ],
        },
        {
          label: "融合分析",
          icon: FindSources,
          children: [
            {
              label: "融合查找",
              value: "fusionSearch",
            },
            {
              label: "同源查找",
              value: "consanguinitySearch",
            },
          ],
        },
      ],
      childrenMenu: [],
      firstMenuIndex: null,
      secondInitRotate: 0,
      secondShow: false,
      childrenMenuRorate: 12,

      relationExcavateMoreShow: false,
      relationExcavateMoreList: [],
      excavateNodeConfigData: {}, //关系挖掘新增目标实体配置
    };
  },
  created() {},
  methods: {
    getTextRotate(index) {
      const rotateItem = 360 / 8;
      const rotate = -(rotateItem * index + 20);
      return `rotate(${rotate}deg)`;
    },
    secondMenuShow(item, index) {
      if (item.children && item.children.length) {
        this.childrenMenu = item.children;
        this.firstMenuIndex = index;
        this.secondShow = true;
        this.relationExcavateMoreShow = false;
        this.childrenMenuRorate = 32 - (this.childrenMenu.length - 1) * 10;
      } else {
        this.childrenMenu = [];
        this.firstMenuIndex = null;
        this.secondShow = false;
      }
    },
    menuClick(item) {
      this.clickEvent(item);
    },
    secondMenuClick(item) {
      this.clickEvent(item);
    },
    clickEvent(item) {
      if (
        item.label == "关系挖掘" &&
        item.children &&
        item.children.length == 0
      ) {
        this.$Message.warning("当前实体暂不支持关系挖掘！");
      }
      switch (item.value) {
        case "getRelationships":
          this.visible = false;
          this.$emit(item.value, this.node, item.level);
          break;
        case "technicalTactics":
          this.visible = false;
          this.$emit(item.value, this.node, item.name);
          break;
        case "relationExcavate":
          if (item.name == "relation-excavate-more") {
            this.relationExcavateMoreShow = true;
          } else {
            this.visible = false;
            this.$emit(
              item.value,
              this.node,
              item.name,
              this.excavateNodeConfigData,
              item
            );
          }
          break;
        case "lock":
        case "lianjiefenxi":
        case "deleteNode":
        case "objectDetail":
        case "consanguinitySearch":
        case "fusionSearch":
        case "relationalViews":
          this.visible = false;
          this.$emit(item.value, this.node);
          break;
      }
    },
    //关系挖掘更多 鼠标离开事件
    handleMouseLeave() {
      this.relationExcavateMoreShow = false;
    },
    getSecondItemRotate(index) {
      const rotateItem = 360 / 16;
      this.secondInitRotate = (360 / 8) * this.firstMenuIndex;
      const rotate = rotateItem * index + this.secondInitRotate;
      return `rotate(${rotate}deg)`;
    },
    getAnimaionDelay(index) {
      const delay = index * 0.025;
      return `${delay}s`;
    },
    getSecondTextRotate(index) {
      const rotateItem = 360 / 16;
      const rotate = -(rotateItem * index + 12 + this.secondInitRotate);
      return `rotate(${rotate}deg)`;
    },
    getIcon(item) {
      switch (item.value) {
        case "lock":
          return this.node.locked ? Unlock : Lock;
        default:
          return item.icon;
      }
    },
    getLabel(item) {
      switch (item.value) {
        case "lock":
          return this.node.locked ? "解锁" : "锁定";
        default:
          return item.label;
      }
    },
    showImg(val) {
      return /((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?/.test(
        val
      );
    },
    close() {
      this.visible = false;
    },
    //查询图谱挖掘配置信息 判断是否可挖掘
    async initExcavate(curNode) {
      //   console.log(curNode, "curNode");
      this.menuList[6].children = [];
      this.excavateNodeConfigData = {};
      let params = {
        graphInstanceId: [curNode.ext.metadata.qsdi_graph_instance_id],
      };
      const res = await getMiningConfig(params);
      if (res.data.length > 0) {
        if (this.checkExcavate(curNode, res.data)) {
          this.menuList[6].children.push({
            label: "+ 新增",
            name: "relation-excavate-add",
            value: "relationExcavate",
          });

          //可挖掘 查询可用的挖掘关系请求体
          let queryParams = {
            graphInstanceId: curNode.ext.metadata.qsdi_graph_instance_id,
            notSearchTotal: true,
            pageNumber: 1,
            pageSize: 999,
            params: {},
            relationNameCn: "",
            relationOwner: "",
            searchValue: "",
            sourceEntityName: curNode.ext.label,
          };
          let relationList = [];
          const data = await enableMiningRelationPageList(queryParams);
          // console.log(data.data.entities, "data");
          relationList = data.data.entities.map((item) => {
            return {
              label: item.relationNameCn,
              name: item.relationName,
              value: "relationExcavate",
              ...item,
            };
          });
          if (relationList.length > 2) {
            this.menuList[6].children = [
              {
                label: "更多",
                name: "relation-excavate-more",
                value: "relationExcavate",
              },
              ...relationList.slice(0, 2),
              ...this.menuList[6].children,
            ];
            this.relationExcavateMoreList = [
              ...relationList.slice(2, relationList.length),
            ];
          } else {
            this.menuList[6].children = [
              ...relationList,
              ...this.menuList[6].children,
            ];
          }
          //操作栏关系挖掘数据
          if (curNode.operate) {
            return {
              relationList: [
                {
                  label: "+ 新增",
                  name: "relation-excavate-add",
                  value: "relationExcavate",
                },
                ...relationList,
              ],
              node: curNode,
              excavateNodeConfigData: this.excavateNodeConfigData,
            };
          }
        }
      }
    },
    //是否可挖掘校验
    checkExcavate(curNode, targetData) {
      let isExcavate = false;
      let diggableEntities = {};
      targetData.forEach((item) => {
        if (
          item.graphInstanceId == curNode.ext.metadata.qsdi_graph_instance_id
        ) {
          diggableEntities = JSON.parse(item.diggableEntities);
          this.excavateNodeConfigData = item;
        }
      });
      for (let key in diggableEntities) {
        if (curNode.ext.label == diggableEntities[key]) {
          isExcavate = true;
        }
      }
      return isExcavate;
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.childrenMenu = [];
      this.firstMenuIndex = null;
      this.secondShow = false;
      this.$emit("input", val);
    },
    position() {
      this.childrenMenu = [];
      this.firstMenuIndex = null;
      this.secondShow = false;
      this.initExcavate(this.node);
    },
  },
  components: {
    graphFontIcon,
  },
};
</script>
<style lang="less" scoped>
@menu-rotate: 21.8deg;
@radius: 278px;
.node-context-menu {
  position: fixed;
  width: @radius;
  height: @radius;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  user-select: none;

  .circle {
    position: relative;
    z-index: 1;
    transform: rotate(@menu-rotate);
    width: @radius;
    height: @radius;
    background-color: transparent;
    border-radius: 50%;

    .circle-center {
      width: 125px;
      height: 125px;
      background-color: #fff;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-@menu-rotate);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      .circle-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-color: #fff;
        border: 3px solid #409eff;
      }
      .node-label {
        position: absolute;
        bottom: 15px;
      }
    }

    .circle-item {
      cursor: pointer;
      width: @radius;
      height: @radius;
      border-radius: 50%;
      position: absolute;
      background-color: rgba(44, 134, 248, 0.8);
      clip-path: polygon(0 0, 0 49%, 50% 50%);
      opacity: 0;
      animation: 0.2s linear circleRotate;
      animation-fill-mode: forwards;
      &:hover {
        background: radial-gradient(
          rgba(44, 134, 248, 0.8),
          rgba(104, 217, 255, 1)
        );
      }
      &.active {
        background: radial-gradient(
          rgba(44, 134, 248, 0.8),
          rgba(104, 217, 255, 1)
        );
      }
      &:nth-child(2) {
        animation-delay: 0.025s;
        transform: rotateZ(45deg);
      }
      &:nth-child(3) {
        animation-delay: 0.05s;
        transform: rotateZ(90deg);
      }
      &:nth-child(4) {
        animation-delay: 0.075s;
        transform: rotateZ(135deg);
      }
      &:nth-child(5) {
        animation-delay: 0.1s;
        transform: rotateZ(180deg);
      }
      &:nth-child(6) {
        animation-delay: 0.125s;
        transform: rotateZ(225deg);
      }
      &:nth-child(7) {
        animation-delay: 0.15s;
        transform: rotateZ(270deg);
      }
      &:nth-child(8) {
        animation-delay: 0.175s;
        transform: rotateZ(315deg);
      }
    }

    .menu-text-box {
      color: #fff;
      position: absolute;
      top: 25%;
      left: 8%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      img {
        width: 38px;
        height: 38px;
      }
    }
  }

  .circle-second {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 431px;
    height: 431px;
    background-color: transparent;
    border-radius: 50%;

    .circle-second-center {
      width: 298px;
      height: 298px;
      background-color: #fff;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .circle-second-item {
      cursor: pointer;
      width: 431px;
      height: 431px;
      border-radius: 50%;
      position: absolute;
      background-color: rgba(44, 134, 248, 0.5);
      clip-path: polygon(0 49%, 0 30%, 50% 50%);
      opacity: 0;
      animation: 0.2s linear circleRotate;
      animation-fill-mode: forwards;
      &:hover {
        background-color: rgba(44, 134, 248, 0.8);
      }
    }

    .menu-text-box {
      flex-direction: column;
      color: #fff;
      position: absolute;
      top: 40%;
      left: 4%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .menu-label-more {
      position: absolute;
      transform: rotate(-3deg);
      left: 214px;
      top: 410px;
      width: 120px;
      max-height: 200px;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
      border-radius: 4px 4px 4px 4px;
      padding: 10px 0;
      .menu-label-more-item {
        font-size: 14px;
        color: #000;
        padding-left: 20px;
        margin: 5px 0;
        cursor: pointer;
      }
      .menu-label-more-item:hover {
        color: #fff;
        background: #2c86f8;
      }
    }
  }
}
@keyframes circleRotate {
  0% {
  }
  100% {
    opacity: 1;
  }
}
</style>
