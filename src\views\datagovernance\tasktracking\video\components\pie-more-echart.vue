<template>
  <div style="width: 300px; height: 150px" ref="echart2"></div>
</template>
<script>
import { dotArr } from '@/util/module/doEcharts.js';
export default {
  name: 'tasktracking',
  props: {},
  data() {
    return {};
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.init();
    // })
    // this.init();
  },
  methods: {
    init() {
      let myChart = this.$echarts.init(this.$refs.echart2);
      var option = {
        tooltip: {
          trigger: 'item',
        },
        // color: ['#C02E02','#05CE98'],
        title: {
          text: '车牌总量',
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
          subtext: '236598',
          subtextStyle: {
            fontSize: 12,
            color: '#19C176',
          },
        },
        series: [
          {
            type: 'pie',
            zlevel: 3,
            silent: true,
            radius: ['53%', '54%'],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: dotArr(),
          },
          {
            name: '',
            type: 'pie',
            radius: ['60%', '80%'],
            avoidLabelOverlap: false,
            // color:['#17acf6','#fff',],
            itemStyle: {
              borderRadius: 10,
            },
            data: [
              {
                value: 40,
                name: '问题数据',
                label: {
                  color: '#F34009',
                  formatter: '{b}\n{c}%',
                },
                itemStyle: {
                  color: {
                    type: 'linear',
                    colorStops: [
                      { offset: 0, color: '#4D1F08' },
                      { offset: 1, color: '#C02E02' },
                    ],
                  },
                },
              },
              {
                value: 60,
                name: '合格数据',
                label: {
                  position: 'outside',
                  color: '#19C176',
                  formatter: '{b}\n{c}%',
                },
                // labelLine: {
                //     normal: {
                //         length: 10,
                //         length2:30,
                //         lineStyle: {
                //             width: 1
                //         }
                //     }
                // },
                itemStyle: {
                  color: {
                    type: 'linear',
                    colorStops: [
                      { offset: 0, color: '#32A19E' },
                      { offset: 1, color: '#05CE98' },
                    ],
                  },
                },
              },
            ],
          },
        ],
      };

      myChart.setOption(option);
      // window.onresize = function(){
      //   myChart.resize();
      // }
    },
    resizeFn() {
      this.init();
      // let myChart = this.$echarts.init(this.$refs.echart2);
      // myChart.resize();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
