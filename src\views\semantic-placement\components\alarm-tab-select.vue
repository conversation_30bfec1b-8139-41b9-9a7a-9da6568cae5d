<template>
  <div class="alarm-tab-select-box">
    <div class="tag-list">
      <div
        class="tag-item"
        :class="{ 'active-select': selectLi == item[selectKey] }"
        v-for="item in list"
        :key="item[selectKey]"
        @click="selectChange(item[selectKey])"
      >
        {{ item[label] }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AlarmTabSelect",
  props: {
    selectLi: {
      type: [Number, String],
      default: 1,
    },
    list: {
      type: Array,
      default: () => [],
    },
    selectKey: {
      type: String,
      default: "value",
    },
    label: {
      type: String,
      default: "name",
    },
  },
  data() {
    return {};
  },
  methods: {
    selectChange(key) {
      this.$emit("selectChange", key);
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-tab-select-box {
  width: 100%;

  .tag-list {
    display: flex;
    gap: 10px;

    .tag-item {
      border-radius: 2px;
      padding: 1px 7px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
      cursor: pointer;
    }
    .active-select {
      color: #fff;
      background: #2c86f8;
    }
  }
}
</style>
