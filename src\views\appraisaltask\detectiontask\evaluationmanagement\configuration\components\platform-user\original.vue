<template>
  <div class="platform-user">
    <Form ref="modalData" class="form-content" :label-width="180" :model="formData" :rules="ruleCustom">
      <!-- 平台可用性 -->
      <common-form
        ref="commonForm"
        :form-data="formData"
        :form-model="formModel"
        :module-action="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
        @handleDetectMode="handleDetectMode"
      >
        <div slot="extract">
          <template
            v-if="
              ['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE', 'VIDEO_PLATFORM_ONLINE_RATE'].includes(
                moduleAction.indexType,
              )
            "
          >
            <FormItem label="检测平台" required>
              <Button @click="chooseCode">
                <span
                  v-if="
                    formData.orgCodeList &&
                    formData.orgCodeList.length === 0 &&
                    moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE'
                  "
                  >请选择待检测的人脸视图库</span
                >
                <span
                  v-if="
                    formData.orgCodeList &&
                    formData.orgCodeList.length === 0 &&
                    moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'
                  "
                  >请选择待检测的车辆视图库</span
                >
                <span
                  v-if="
                    formData.orgCodeList &&
                    formData.orgCodeList.length === 0 &&
                    moduleAction.indexType == 'VIDEO_PLATFORM_ONLINE_RATE'
                  "
                  >请选择待检测的互联平台</span
                >
                <span v-else>{{ `已选择 ${(formData.orgCodeList && formData.orgCodeList.length) || 0}个` }}</span>
              </Button>
            </FormItem>
          </template>
        </div>
      </common-form>
      <div class="platform" v-if="interfaceInfoConfig()">
        <p class="text">接口信息配置</p>
        <div class="left-city">
          <ul class="ul">
            <li
              :class="{ active: current == index }"
              v-for="(item, index) in formData.apiListModel"
              :key="index"
              @click="cityClick(item, index)"
            >
              <div class="til">
                <Tooltip :content="item.name" :disabled="item.name.length < 8">
                  {{ item.name }}
                </Tooltip>
              </div>
              <i-switch class="switch" v-model="item.type" />
            </li>
          </ul>
        </div>
        <Form ref="platformvalid" :rules="platformrule" class="form-content edit-form" :model="formData">
          <div class="orgCode" v-if="formData.apiListModel[current]">
            在待检测平台中的组织机构编码：
            <i-input
              v-model="formData.apiListModel[current].orgCode"
              placeholder="Enter something..."
              style="width: 300px"
            />
          </div>
          <div class="topUl">
            <ul>
              <li>
                <div class="item">接口名称</div>
                <div class="item">每轮检测调用次数(次)</div>
                <div class="item">调用超时阈值(秒)</div>
                <div class="item">相似度%</div>
              </li>
            </ul>
          </div>
          <div style="margin-top: 30px" v-if="formData.apiListModel[current]">
            <ul>
              <li v-for="(item, index) in formData.apiListModel[current].apiModel" :key="index">
                <div class="item">{{ item.name }}</div>
                <div class="item">
                  <FormItem label="" prop="apiTimes" :label-width="0">
                    <Input placeholder="次数" v-model.number="item.apiTimes"></Input>
                  </FormItem>
                </div>
                <div class="item">
                  <FormItem label="" prop="apiDura" :label-width="0">
                    <Input placeholder="阈值" v-model.number="item.apiDura"></Input>
                  </FormItem>
                </div>
                <div class="item">
                  <FormItem label="" prop="apiRate" :label-width="0">
                    <Input placeholder="相似度" v-model.number="item.apiRate"></Input>
                  </FormItem>
                </div>
              </li>
              <div class="clear"></div>
            </ul>
          </div>
        </Form>
      </div>
      <FormItem
        label="时间范围"
        class="right-item mb-sm"
        prop="name"
        v-if="['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(moduleAction.indexType)"
      >
        <Select v-model="formData.timeDelay" class="select-width" placeholder="请选择时间范围" transfer disabled>
          <Option value="1">前一天</Option>
        </Select>
      </FormItem>
      <FormItem
        label="时间区间"
        v-if="['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(moduleAction.indexType)"
      >
        <div class="inspection">
          <div class="row-inspection" v-for="(item, index) in formData.dateRang" :key="index">
            <FormItem>
              <div class="form-row">
                <span class="width-picker">
                  <Select
                    :disabled="
                      moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE' ||
                      moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'
                    "
                    class="time-picker"
                    transfer
                    v-model="item.hourStart"
                    clearable
                  >
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourEnd ? item.hourEnd <= it.value : false"
                      :class="item.hourEnd ? (item.hourEnd <= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
                <span class="color-bule mr-sm ml-sm">—</span>
                <span class="width-picker">
                  <Select
                    :disabled="
                      moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE' ||
                      moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'
                    "
                    class="time-picker"
                    transfer
                    v-model="item.hourEnd"
                    clearable
                  >
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourStart ? item.hourStart >= it.value : false"
                      :class="item.hourStart ? (item.hourStart >= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
              </div>
            </FormItem>
          </div>
        </div>
        <p v-if="moduleAction.indexType == 'FACE_PLATFORM_ONLINE_RATE'" class="label-color w390">
          说明：检测时间范围内，配置的时间区间内如果没有抓拍人脸数据上传，则人脸视图库离线
        </p>
        <p v-if="moduleAction.indexType == 'VEHICLE_PLATFORM_ONLINE_RATE'" class="label-color w390">
          说明：检测时间范围内，配置的时间区间内如果没有抓拍人脸数据上传，则人脸视图库离线
        </p>
      </FormItem>
      <FormItem
        label="统计范围"
        class="right-item mb-sm"
        prop="name"
        v-if="['VEHICLE_CAPTURE_STABILITY', 'FACE_CAPTURE_STABILITY'].includes(moduleAction.indexType)"
      >
        <Select v-model="formData.statDimension" class="select-width" placeholder="请选择统计范围" transfer disabled>
          <Option :value="1">按月统计</Option>
        </Select>
        <RadioGroup v-model="formData.statRange">
          <Radio :label="1" class="mr-lg">统计本月</Radio>
          <Radio :label="2">统计上月</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        label="比较系数α"
        class="right-item mb-sm"
        prop="name"
        v-if="['VEHICLE_CAPTURE_STABILITY', 'FACE_CAPTURE_STABILITY'].includes(moduleAction.indexType)"
      >
        <InputNumber class="width-md" v-model="formData.coefficientValue" placeholder="请选择比较系数"></InputNumber>
        <p class="font-warning lineh">
          备注：去年同期实际抓拍数量乘以比较系数，作为最终的比较数量。
          车辆抓拍数据上传稳定性=当期去重后的上传的车辆抓拍数据量/（去年同期上传的车辆抓拍数据量*α）
        </p>
      </FormItem>
      <FormItem
        label="去年同期抓拍数量"
        class="right-item mb-sm"
        prop="name"
        v-if="['VEHICLE_CAPTURE_STABILITY', 'FACE_CAPTURE_STABILITY'].includes(moduleAction.indexType)"
      >
        <RadioGroup v-model="formData.statDataSource">
          <Radio :label="1">系统统计</Radio>
          <Radio :label="2">手动配置</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <Form ref="formCustom" :rules="formCustomrule" :model="formCustomData" class="form-content" :label-width="160">
      <FormItem label="检测计划" v-if="moduleAction.indexType === 'VIDEO_PLATFORM_ONLINE_RATE'" prop="checkTime">
        <p class="check-plan">
          每
          <Input v-model="formCustomData.checkTime" class="ml-sm mr-sm" style="width: 60px" @on-blur="handleBlurTime" />
          <Select v-model="formCustomData.cronType" transfer style="width: 80px" @on-change="handleTypeChange">
            <Option value="4">分</Option>
            <Option value="5">时</Option>
          </Select>
          <span class="ml-xs">检测一次</span>
        </p>
        <!--        <span class="base-text-color">实时监测</span>-->
      </FormItem>
      <FormItem label="检测逻辑" v-if="moduleAction.indexType === 'VIDEO_PLATFORM_ONLINE_RATE'" prop="deviceNum">
        <p class="base-text-color">检测的某平台的所有设备均离线则认为该平台离线。</p>
        <p class="check-plan mt-md">
          <span>检测数量：</span>
          <Select v-model="formCustomData.checkCount" transfer class="ml-sm mr-sm" style="width: 80px">
            <Option value="2">抽检</Option>
            <!--            <Option value="1">全部</Option>-->
            <!--            <Option value="2">抽检</Option>-->
          </Select>
          <Input
            class="mr-xs"
            v-model="formCustomData.deviceNum"
            maxlength="3"
            style="width: 60px"
            @on-blur="handleBlurNum"
          />
          <span>路</span>
        </p>
      </FormItem>
    </Form>

    <OrgModal
      v-model="orgModalVisible"
      @query="choosQuery"
      :orgList="formData.orgCodeList"
      :formModel="formModel"
      :areaTreeData="areaTreeData"
      v-if="areaTreeData.length != 0"
    ></OrgModal>
    <common-capture-area-select
      ref="captureAreaSelect"
      :data="captureAreaTreeData"
      :paste-fun="pasteFun"
      :copy-fun="copyFun"
      :config-fun="configFun"
      :isConfig="isConfigCaptureArea"
      v-if="isCaptureConfig()"
    ></common-capture-area-select>
    <config-capture-num v-model="captureNumbVisible" :data="currentCaptureConfig" @commit="commitCaptureNum">
    </config-capture-num>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
export default {
  components: {
    OrgModal: require('../org-modal/index.vue').default,
    CommonForm: require('../common-form/index.vue').default,
    CommonCaptureAreaSelect: require('../common-capture-area-select').default,
    ConfigCaptureNum:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/config-capture-num.vue')
        .default,
  },
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },

    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateTimePass = (rule, value, callback) => {
      const regx = /^\+?[1-9][0-9]*$/;
      !regx.test(value) && value !== '' ? callback(new Error('请输入大于0整数！')) : '';
      if (this.formCustomData.cronType === '4' && (value < 30 || value > 60)) {
        callback(new Error('选择分钟为单位，仅支持输入30-60分钟；如需更大时间间隔，请选择小时为单位！'));
      }
      if (this.formCustomData.cronType === '5') {
        const regx = /^\+?[1-9][0-9]*$/;
        !regx.test(value) ? callback(new Error('请输入正整数！')) : '';
      }
      callback();
    };
    const validateNumPass = (rule, value, callback) => {
      const regx = /^\+?[1-9][0-9]*$/;
      !regx.test(value) && value !== '' ? callback(new Error('请输入大于0的整数！')) : '';
      if (value !== '' && value > 100) {
        callback(new Error('最大支持抽检100路设备！'));
      }
      callback();
    };
    return {
      ruleCustom: {
        timeDelay: [
          {
            required: true,
            type: 'number',
            message: '请输入时间',
            trigger: 'blur',
          },
        ],
      },
      platformrule: {
        orgList: [
          {
            required: true,
            type: 'array',
            message: '请选择检测平台',
            trigger: 'change',
          },
        ],
      },
      formData: {
        detectMode: '1',
        apiListModel: [],
        orgCodeList: [],
        dateRang: null,
        cronData: ['30'],
        cronType: '4',
        deviceNum: '20',
      },
      current: 0,
      orgCodes: [],
      areaTreeData: [],
      selectAreaTree: {
        regionCode: '',
      },
      schemeList: [],
      orgModalVisible: false,
      orgList: [],
      formCustomData: {
        checkCount: '2',
        cronType: '4',
        checkTime: '30',
        deviceNum: '20',
      },
      formCustomrule: {
        checkTime: [{ required: true, validator: validateTimePass, trigger: 'blur' }],
        deviceNum: [{ required: true, validator: validateNumPass, trigger: 'blur' }],
      },
      //人脸抓拍数量上传稳定性配置
      captureConfigList: [],
      captureAreaTreeData: [],
      captureNumbVisible: false,
      currentCaptureConfig: null,
      copyRegionCode: '',
    };
  },
  watch: {
    formModel: {
      handler(val) {
        this.schemeList = this.getHour();
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
          let orgCodeList = (this.formData.orgCodes && this.formData.orgCodes.split(',')) || [];
          this.formData.orgCodeList = orgCodeList.filter(Boolean);
          this.formCustomData.checkTime = this.configInfo.cronNum;
          this.formCustomData.cronType = this.configInfo.cronType;
          this.formCustomData.deviceNum = this.configInfo.deviceNum;
        } else {
          const { regionCode } = this.moduleAction;

          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            dateRang: [{ hourStart: null, hourEnd: null }],
            timeDelay: null,
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
            statDimension: 1, //统计维度；1 按月统计
            statRange: 1, //统计范围；1 统计本月，2 统计上月
            coefficientValue: null, //比较系数 α
            statDataSource: 1, //统计数据来源； 1系统统计，2手动配置
          };
          this.getEngineIndex(regionCode);
        }
        this.setCaptureNum();
        this.setDefaultTime();
      },
      immediate: true,
    },
    // moduleAction: {
    //   handler(val) {
    //     if (val.config) {
    //       this.formData = {
    //         ...this.configInfo,
    //       }
    //     } else {
    //       const { regionCode } = val
    //       this.formData = {
    //         ...this.formData,
    //         regionCode: regionCode, // 检测对象
    //         detectMode: '1',
    //         dateRang: [{ hourStart: null, hourEnd: null }],
    //         timeDelay: null,
    //       }
    //       this.getEngineIndex(val.regionCode)
    //     }
    //     this.setDefaultTime()
    //   },
    //   immediate: true,
    // },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
    }),
  },
  methods: {
    isConfigCaptureArea({ regionCode }) {
      return this.captureConfigList.findIndex((item) => item.civilCode === regionCode) !== -1;
    },
    getCaptureNum() {
      let checkedRegion = this.$refs.captureAreaSelect.getCheckRegion();
      let configList = [];
      // 1、过滤需要考核的配置
      this.captureConfigList.forEach((item) => {
        checkedRegion.forEach((regionCode) => {
          if (regionCode === item.civilCode) {
            configList.push(item);
          }
        });
      });
      this.formData.captureConfigList = configList;
    },
    setCaptureNum() {
      let { captureConfigList = [], regionCode } = this.formData;
      this.captureConfigList = captureConfigList;
      let filterTreedata = JSON.parse(
        JSON.stringify(
          this.treeData.filter((item) => item.regionCode === regionCode || item.parentCode === regionCode),
        ),
      );
      this.captureConfigList.forEach((config) => {
        filterTreedata.forEach((item) => {
          if (config.civilCode === item.regionCode) {
            this.$set(item, 'check', true);
          }
        });
      });
      this.captureAreaTreeData = this.$util.common.arrayToJson(filterTreedata, 'regionCode', 'parentCode');
    },
    configFun({ regionCode }) {
      let index = this.captureConfigList.findIndex((item) => item.civilCode === regionCode);
      if (index === -1) {
        this.currentCaptureConfig = [
          {
            civilCode: regionCode,
            janNum: null,
            febNum: null,
            marNum: null,
            aprNum: null,
            mayNum: null,
            junNum: null,
            julNum: null,
            augNum: null,
            septNum: null,
            octNum: null,
            novNum: null,
            decNum: null,
          },
        ];
      } else {
        this.currentCaptureConfig = this.$util.common.deepCopy([this.captureConfigList[index]]);
      }
      this.captureNumbVisible = true;
    },
    copyFun({ regionCode }) {
      let index = this.captureConfigList.findIndex((item) => item.civilCode === regionCode);
      if (index === -1) {
        return this.$Message.error('未配置抓拍数量');
      } else {
        this.copyRegionCode = this.captureConfigList[index]['civilCode'];
        this.$Message.success('复制成功');
      }
    },
    pasteFun({ regionCode }) {
      try {
        let pasteIndex = this.captureConfigList.findIndex((item) => item.civilCode === regionCode);
        let copyIndex = this.captureConfigList.findIndex((item) => item.civilCode === this.copyRegionCode);
        if (!this.copyRegionCode || copyIndex === -1) {
          return this.$Message.error('请先复制');
        }
        if (pasteIndex === -1) {
          this.captureConfigList = [
            ...this.captureConfigList,
            {
              ...this.captureConfigList[copyIndex],
              civilCode: regionCode,
            },
          ];
        } else {
          this.captureConfigList[pasteIndex] = {
            ...this.captureConfigList[pasteIndex],
            ...this.captureConfigList[copyIndex],
            civilCode: this.captureConfigList[pasteIndex]['civilCode'],
          };
        }
        this.$Message.success('粘贴成功');
      } catch (e) {
        console.log(e);
      }
    },
    /**
     * @param config []
     */
    commitCaptureNum(config) {
      let index = this.captureConfigList.findIndex((item) => item.civilCode === config[0]['civilCode']);
      if (index === -1) {
        this.captureConfigList = [...this.captureConfigList, ...config];
      } else {
        this.captureConfigList[index] = { ...this.captureConfigList[index], ...config[0] };
      }
    },
    interfaceInfoConfig() {
      let configList = [
        'VIDEO_PLATFORM_ONLINE_RATE',
        'FACE_PLATFORM_ONLINE_RATE',
        'VEHICLE_PLATFORM_ONLINE_RATE',
        'FACE_CAPTURE_STABILITY',
        'VEHICLE_CAPTURE_STABILITY',
      ];
      return (
        !configList.includes(this.moduleAction.indexType) &&
        this.formData.apiListModel &&
        this.formData.apiListModel.length > 0
      );
    },
    isCaptureConfig() {
      return (
        ['VEHICLE_CAPTURE_STABILITY', 'FACE_CAPTURE_STABILITY'].includes(this.moduleAction.indexType) &&
        this.formData.statDataSource === 2
      );
    },
    setDefaultTime() {
      if (
        this.moduleAction.indexType === 'FACE_PLATFORM_ONLINE_RATE' ||
        ('VEHICLE_PLATFORM_ONLINE_RATE' && !this.formData.timeDelay)
      ) {
        this.formData.timeDelay = '1';
        this.formData.dateRang = [{ hourStart: 0, hourEnd: 24 }];
      }
      this.orgList = [];
      //人脸 车辆 抓拍数量上传稳定性配置 默认参数
      if (['VEHICLE_CAPTURE_STABILITY', 'FACE_CAPTURE_STABILITY'].includes(this.moduleAction.indexType)) {
        this.formData.statDimension = 1; //统计范围 禁止编辑
        this.formData.coefficientValue ? '' : (this.formData.coefficientValue = null);
        this.formData.statRange ? '' : (this.formData.statRange = 1);
      }
    },
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    // 选择组织机构
    chooseCode() {
      this.getCode();
    },
    choosQuery(data) {
      this.formData.orgCodes = data.join(',');
      this.formData.orgCodeList = data;
    },
    handleDetectMode(val) {
      this.selectAreaTree.regionCode = val;
    },
    async getCode() {
      try {
        let res = await this.$http.get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.selectAreaTree.regionCode },
        });
        this.areaTreeData = this.$util.common.arrayToJson(res.data.data, 'id', 'parentId');
        this.$nextTick(() => {
          this.orgModalVisible = true;
        });
      } catch (err) {
        console.log(err);
      }
    },

    // 获取接口配置参数
    getEngineIndex(val) {
      this.$http
        .get(governanceevaluation.getEngineIndexById, {
          params: { regionCode: val },
        })
        .then((res) => {
          var obj = res.data.apiListModel;
          this.formData.apiListModel = obj;
        });
    },
    cityClick(item, index) {
      this.current = index;
    },
    // 表单提交校验
    async handleSubmit() {
      if (
        !this.formData.orgCodeList.length &&
        ['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE', 'VIDEO_PLATFORM_ONLINE_RATE'].includes(
          this.moduleAction.indexType,
        )
      ) {
        this.$Message.error('请选择检测平台');
        return false;
      }
      const formcustomres = await this.$refs['formCustom'].validate();
      const commonres = await this.$refs['commonForm'].handleSubmit();
      if (this.isCaptureConfig()) {
        this.getCaptureNum();
      }
      return formcustomres && commonres;
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    handleBlurTime() {
      this.formData.cronData = [parseInt(this.formCustomData.checkTime)];
    },
    handleTypeChange(val) {
      this.formData.cronType = val;
    },
    handleBlurNum() {
      this.formData.deviceNum = this.formCustomData.deviceNum;
    },
  },
};
</script>

<style lang="less" scoped>
@import '~@/views/appraisaltask/components/common.less';
.platform-user {
  margin-bottom: 20px;
  .platform {
    height: 450px;
    overflow-y: auto;
    margin-left: 40px;
    .text {
      color: var(--color-label);
      margin-left: 25px;
      margin-bottom: 15px;
    }
  }
  .left-city {
    float: left;
    width: 200px;
    min-height: 300px;
    color: var(--color-content);
    border: 1px solid var(--border-color);
    padding: 10px;
    border-radius: 5px;
  }
  .edit-form {
    margin-left: 220px;
    width: 650px;
  }

  .topUl {
    height: 30px;
    border-bottom: 1px solid var(--border-color);
  }
  ul {
    color: var(--color-content);
    margin-top: 30px;
    li {
      .item {
        float: left;
        width: 25%;
      }

      .item::after {
        clear: both;
        content: '';
      }

      /deep/ .ivu-input {
        width: 80%;
      }
    }
  }

  .orgCode {
    color: var(--color-content);
    margin-top: 20px;
  }

  .clear {
    clear: both;
  }
  .check-plan {
    display: flex;
    align-items: center;
    color: var(--color-content);
  }

  .ul {
    margin-top: 0;

    li {
      position: relative;
      width: 120px;
      padding: 0 10px;
      height: 40px;
      line-height: 40px;

      .til {
        width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 10px;
      }
    }
    li:hover {
      cursor: pointer;
    }
    li.active {
      cursor: pointer;
      background: var(--color-primary);
      color: #fff;
    }

    .switch {
      position: absolute;
      top: 8px;
      right: -58px;
    }
  }

  @{_deep} .ivu-modal-body {
    padding: 0 40px 0 40px !important;
  }

  @{_deep} .ivu-tooltip-rel {
    width: 120px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 10px;
  }
}
.notCheck {
  color: #56789c;
}
.inspection {
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
}
.label-color {
  color: var(--color-tips);
}
.color-white {
  color: #fff;
}
.color-bule {
  color: #1b82d2 !important;
}
.width-picker {
  width: 174px;
}
.form-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}
.w390 {
  width: 390px;
}
@{_deep}.select-width,
.input-width {
  width: 380px;
}
</style>
