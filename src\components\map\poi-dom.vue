<!--
    * @FileDescription: 地图-poi详情
 -->
<template>
  <div class="dom-wrapper">
    <div class="dom">
      <div class="poi-detail">
        <div class="dom-header ellipsis">
          <span>{{ currentDevice.name || "--" }}</span>
          <ui-icon type="close" :size="14" @click.native="() => $emit('close')"></ui-icon>
        </div>
        <div class="poi-detail-content">
          <div class="poi-detail-p">
            <span class="label"
              ><ui-icon
                class="mr-5"
                type="gsi-"
                color="#2C86F8"
                :size="14"
              ></ui-icon
              >区域：</span
            >
            <span class="message" v-show-tips>{{
              currentDevice.region || "--"
            }}</span>
          </div>
          <div class="poi-detail-p">
            <span class="label"
              ><ui-icon
                class="mr-5"
                type="biaoqian"
                color="#2C86F8"
                :size="14"
              ></ui-icon
              >行业：</span
            >
            <span class="message" v-show-tips>{{
              currentDevice.bigCategory || "--"
            }}</span>
          </div>
          <div class="poi-detail-p">
            <span class="label"
              ><ui-icon
                class="mr-5"
                type="shuxing2"
                color="#2C86F8"
                :size="14"
              ></ui-icon
              >类型：</span
            >
            <span class="message" v-show-tips>{{
              currentDevice.smallCategory || "--"
            }}</span>
          </div>
          <div class="poi-detail-p">
            <span class="label"
              ><ui-icon
                class="mr-5"
                type="gejizhengfu"
                color="#2C86F8"
                :size="14"
              ></ui-icon
              >地址：</span
            >
            <span class="message ellipsis" v-show-tips>{{
              currentDevice.address || "--"
            }}</span>
          </div>
          <div class="poi-detail-p">
            <span class="label"
              ><ui-icon
                class="mr-5"
                type="location"
                color="#2C86F8"
                :size="14"
              ></ui-icon
              >经度：</span
            >
            <span class="message" v-show-tips>{{
              currentDevice.longitude ? currentDevice.longitude : "--"
            }}</span>
          </div>
          <div class="poi-detail-p">
            <span class="label"
              ><ui-icon
                class="mr-5"
                type="location"
                color="#2C86F8"
                :size="14"
              ></ui-icon
              >纬度：</span
            >
            <span class="message" v-show-tips>{{
              currentDevice.latitude ? currentDevice.latitude : "--"
            }}</span>
          </div>
        </div>
      </div>
      <div class="hover-bottom"></div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  watch: {},
  computed: {},

  data() {
    return {
      currentDevice: {},
    };
  },
  methods: {
    init(pointItem) {
      this.currentDevice = pointItem;
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
  //   padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}
.dom {
  width: 380px;
  height: 40px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: end;
  .poi-detail {
    .dom-header {
      background: #2c86f8;
      padding: 8px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      color: #fff;
      display: flex;
      justify-content: space-between;
      .icon-close {
        color: #fff!important;
      }
    }
    &-content {
        padding: 10px;
        display: flex;
        flex-wrap: wrap;
    }
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    .poi-detail-p {
      width: 50%;
      display: flex;
      .label {
        white-space: nowrap;
      }
    }
  }
  .hover-bottom {
    background: #fff;
  }
  .hover-bottom:before,
  .hover-bottom:after {
    content: "";
    display: block;
    border-width: 8px;
    position: absolute;
    bottom: -16px;
    left: 186px;
    border-style: solid dashed dashed;
    border-color: #ffffff transparent transparent;
    font-size: 0;
    line-height: 0;
  }
}
</style>
