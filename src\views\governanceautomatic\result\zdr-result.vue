<template>
  <ui-modal title="ZDR人像轨迹数据治理结果" v-model="visible" :styles="styles" :footer-hide="true">
    <tabs class="mt10" ref="tagView" :list="['图像模式', '聚档模式']" @tagChange="tagChange" />
    <section class="result-content">
      <div v-if="modelTag == 0">
        <face-device-search ref="faceSearchRef" @startSearch="startSearch"> </face-device-search>
        <p class="statics mt-sm mb-sm">
          {{ staticMessage.totalFace.label }}：<span class="active-color mr-sm">
            {{ staticMessage.totalFace.value }}</span
          >
          {{ staticMessage.captureExpectFace.label }}：
          <span class="font-red"> {{ staticMessage.captureExpectFace.value }}</span>
        </p>
        <div class="list-box">
          <face-list
            :face-loading="faceLoading"
            :face-list="faceList"
            :has-hover="faceHasHover"
            @uploadTips="uploadTips"
          ></face-list>
        </div>
      </div>
      <div v-else>
        <div class="search-box mt-md mb-md">
          <ui-label class="inline mr-lg" label="关键词" :width="55">
            <Input class="input-width" placeholder="请输入姓名/证件号" v-model="searchData.keyWord"></Input>
          </ui-label>
          <ui-label class="inline mr-lg" label="轨迹总数" :width="70">
            <Input class="small-width" placeholder="" v-model="searchData.keyWord"></Input>
            -
            <Input class="small-width" placeholder="" v-model="searchData.keyWord"></Input>
          </ui-label>
          <ui-label class="inline mr-lg" label="异常轨迹数量" :width="100">
            <Input class="small-width" placeholder="" v-model="searchData.keyWord"></Input>
            -
            <Input class="small-width" placeholder="" v-model="searchData.keyWord"></Input>
          </ui-label>
          <ui-label class="inline" :width="30">
            <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
            <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, startSearch)">重置</Button>
          </ui-label>
        </div>
        <div class="file-mode">
          <ui-gather-card
            class="card mr-sm"
            v-for="(item, index) in cardList"
            :key="index"
            :list="item"
            :cardInfo="cardInfo"
            @detail="detail"
          ></ui-gather-card>
        </div>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </section>
    <capture-details ref="CaptureDetails"></capture-details>
  </ui-modal>
</template>
<style lang="less" scoped>
.result-content {
  height: 700px;
  .search-box {
    .input-width {
      width: 200px;
    }
    .small-width {
      width: 100px;
    }
  }
  .file-mode {
    display: flex;
    flex-wrap: wrap;
    height: 584px;
    overflow-y: scroll;
    .card {
      width: calc(calc(100% - 40px) / 4);
    }
  }
}

.statics {
  color: #fff;
  .active-color {
    color: var(--color-bluish-green-text);
  }
}
.list-box {
  position: relative;
}
</style>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import persontype from '@/views/viewassets/videopictureassets/keypersonlibrary/persontype.js';
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    value: {},
    faceLoading: {
      default: false,
    },
    faceList: {
      default: () => [],
    },
    totalListCount: {
      default: 0,
    },
    staticsObject: {
      type: Object,
    },
    importTotalCount: {},
  },
  data() {
    return {
      cardList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'snapCount' },
        { name: '异常轨迹：', value: 'abnormalCount', color: '#BC3C19' },
      ],
      visible: false,
      styles: {
        //top: "0.5rem",
        width: '95%',
      },
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      title: '',
      staticMessage: {
        totalFace: {
          label: '人像轨迹总量',
          value: 0,
        },
        captureExpectFace: {
          label: '错误轨迹',
          value: 0,
        },
      },
      options: {},
      faceHasHover: true,
      searchData: {
        startTime: '',
        endTime: '',
        deviceIds: [],
      },
      modelTag: 0,
    };
  },
  created() {},
  mounted() {
    //this.getList()
  },
  methods: {
    async getList() {
      try {
        this.cardList = [];
        this.loading = true;
        let params = this.searchData;
        let res = await this.$http.post(equipmentassets.queryPersonLibPageGather, params);
        const datas = res.data.data;
        this.cardList = datas.entities.map((item) => {
          const personTypes = item.personTypeText.split(',');
          item.personTypes = persontype.filter((item) => {
            return personTypes.includes(item.tagName);
          });
          return item;
        });
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    detail(id) {
      this.$refs.CaptureDetails.init(id);
    },
    // 设备模式/图像模式切换
    tagChange(val) {
      this.modelTag = val;
    },
    startSearch(val) {
      Object.assign(this.searchData, val);
      this.$emit('startSearch', this.searchData);
    },
    changePage(val) {
      this.$emit('changePage', val);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.$emit('changePageSize', val);
    },
    // 查看不合格原因
    uploadTips() {},
    async querySumStatistics() {
      try {
        let params = {
          topicComponentId: this.options.filedData.topicComponentId,
        };
        let { data } = await this.$http.get(tasktracking.querySumStatistics, {
          params: params,
        });
        this.staticMessage.totalFace.value = data.data.accessDataCount;
        this.staticMessage.captureExpectFace.value = data.data.existingExceptionCount;
        // this.pageData.totalCount = data.data.existingExceptionCount
      } catch (err) {
        console.log(err);
      }
    },
    async exportExcel() {
      try {
        let params = {
          topicComponentId: this.options.filedData.topicComponentId,
        };
        let res = await this.$http.post(
          tasktracking.downloadFaceLibAbnormalDev,
          Object.assign(params, this.searchData),
          {
            responseType: 'blob',
          },
        );
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    totalListCount(val) {
      this.pageData.totalCount = val;
    },
  },
  components: {
    FaceDeviceSearch: require('@/components/face-device-search.vue').default,
    FaceList: require('@/components/face-list.vue').default,
    UiGatherCard: require('@/components/ui-gather-card.vue').default,
    tabs: require('@/components/tag-view').default,
    CaptureDetails: require('@/views/viewassets/videopictureassets/keypersonlibrary/components/capture-details.vue')
      .default,

    // faceShebeiList: require('../../components/face-shebei-list').default,
  },
};
</script>
