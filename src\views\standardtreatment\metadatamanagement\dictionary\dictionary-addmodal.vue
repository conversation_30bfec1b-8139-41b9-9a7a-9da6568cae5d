<template>
  <div class="dictionary-addmodal">
    <ui-modal title="新增字典" v-model="visible" :width="1670">
      <div class="left-div">
        <ui-table
          class="ui-table"
          :loading="loading"
          :table-columns="tableColumns"
          :table-data="tableData"
          ref="table"
          :minus-height="400"
        >
          <!-- /内部标识符 -->
          <template slot-scope="{ index }" slot="identCode">
            <Input v-model="tableData[index].identCode" class="add-input" placeholder="请输入新的内容"></Input>
          </template>
          <!-- 中文名称 -->
          <template slot-scope="{ index }" slot="identName">
            <Input v-model="tableData[index].identName" class="add-input" placeholder="请输入新的内容"></Input>
          </template>
          <!-- 标识符 -->
          <template slot-scope="{ index }" slot="tag">
            <Input v-model="tableData[index].tag" class="add-input" placeholder="请输入新的内容"></Input>
          </template>
          <!-- 字符类型 -->
          <!-- <template slot-scope="{ row,index }" slot="type">
						<Input v-model="tableData[index].type" class="add-input" placeholder="请输入新的内容"></Input>
					</template>-->
          <!-- 字符格式 -->
          <template slot-scope="{ index }" slot="fieldType">
            <Select v-model="tableData[index].fieldType" class="width-input" placeholder="请选择">
              <Option v-for="(item) in metaFieldTypeList" :key="item.id" :value="item.dataKey">{{
                item.dataValue
              }}</Option>
            </Select>
          </template>
          <!-- 字符长度 -->
          <template slot-scope="{ index }" slot="fieldLength">
            <Input v-model="tableData[index].fieldLength" class="add-input" placeholder="请输入新的内容"></Input>
          </template>
          <!-- 操作 -->
          <template slot-scope="{ index }" slot="action">
            <ui-btn-tip
              :styles="{ color: '#438CFF', 'font-size': '14px' }"
              icon="icon-tianjia"
              content="新增"
              v-if="index === 0"
              @click.native="add()"
            ></ui-btn-tip>
            <ui-btn-tip
              :styles="{ color: 'rgb(207, 57, 57)', 'font-size': '14px' }"
              icon="icon-jian"
              content="删除"
              v-else
              @click.native="deleteList(index)"
            ></ui-btn-tip>
            <!-- <a v-if="index === 0" @click="add()" class="fs16">
              新增
            </a>
            <a v-else @click="deleteList(index)" class="fs16">
              删除
            </a> -->
          </template>
        </ui-table>
      </div>
      <template slot="footer">
        <Row>
          <Col :span="24" align="center">
            <Button type="primary" class="plr-30" @click="save()">保 存</Button>
          </Col>
        </Row>
      </template>
    </ui-modal>
  </div>
</template>

<script>
import metadatamanagement from '@/config/api/metadatamanagement';
export default {
  data() {
    return {
      loading: false,
      visible: false,
      tableColumns: [
        { title: '内部标识代码', slot: 'identCode' },
        { title: '中文名称', slot: 'identName' },
        { title: '标识符', slot: 'tag' },
        { title: '字符格式', slot: 'fieldType' },
        { title: '字符长度', slot: 'fieldLength' },
        { title: '操作', slot: 'action', fixed: 'right', align: 'center', width: 60 },
      ],
      tableData: [{ identCode: '', identName: '', tag: '', fieldType: '', fieldLength: '' }],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    open() {
      this.visible = true;
    },
    // 删除
    deleteList(index) {
      this.tableData.splice(index, 1);
    },
    // 添加一行
    add() {
      this.tableData.push({ identCode: '', identName: '', tag: '', fieldType: '', fieldLength: '' });
    },
    // 保存
    async save() {
      const errorMsg = this.validateTableData(this.tableData, [
        'identCode',
        'identName',
        'tag',
        'fieldType',
        'fieldLength',
      ]);
      if (errorMsg.length > 0) {
        this.$Message.error({
          render: () => {
            let tab = errorMsg.map((val) => {
              return <div style="padding-left:10px;text-align:left">{val}</div>;
            });
            return tab;
          },
          duration: 4,
        });
        return;
      }
      try {
        let res = await this.$http.post(metadatamanagement.dictBatchAdd, this.tableData);
        if (res.data.code === 200) {
          this.$emit('search');
          this.visible = false;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 新增数据校验不能为空
    validateTableData(data, key) {
      const obj = [];
      const mapKey = {
        identCode: '内部标识代码',
        identName: '中文名称',
        tag: '标识符',
        fieldType: '字符格式',
        fieldLength: '字符长度',
      };
      data.map((val, index) => {
        obj[index] = val;
        obj[index].keyArray = [];

        key.map((key1) => {
          if (!val[key1]) {
            val.tableCell = index + 1;
            obj[index] = val;
            obj[index].keyArray.push(mapKey[key1]);
          }
        });
      });
      let msgs = [];
      obj.map((val) => {
        if (val.keyArray.length !== 0) {
          msgs.push('第' + val.tableCell + '行 ' + JSON.stringify(val.keyArray) + ' 不能为空 ');
        }
      });
      return msgs;
    },
  },
  computed: {},
  props: {
    metaFieldTypeList: {
      type: Array,
      default() {},
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.dictionary-addmodal {
  .add-input {
    /deep/ .ivu-input {
      border-color: transparent !important;
      background: transparent;
      &:focus {
        box-shadow: none;
      }
    }
  }
  .fs16 {
    font-size: 16px;
  }
}
</style>
