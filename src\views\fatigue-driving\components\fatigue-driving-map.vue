<template>
  <div class="map-box" ref="mapBox">
    <div :id="mapId" class="map"></div>
    <MapDomThematic
      ref="MapDomThematic"
      :thematicItem="thematicItem"
      :type="thematicType"
      @close="closeFatiguDrivingWindow"
    ></MapDomThematic>

    <!-- 鼠标浮动 -->
    <mouse-title ref="mouseDom" :name="mouseName"></mouse-title>
    <!-- 重叠点位列表 -->
    <mapPoleList ref="poleListDom" :polePoints="polePoints"></mapPoleList>
  </div>
</template>

<script>
import axios from "axios";
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapState } from "vuex";
import MapDomThematic from "./map-dom-thematic.vue";
import mouseTitle from "@/components/map/mouse-title.vue";
import mapPoleList from "@/components/map/map-pole-list.vue";
import { LayerType } from "@/map/core/enum/LayerType.js";
import map from "@/api/map";
let mapMain = null;
const mouseInfoWindow = [];
const poleInfoWindow = [];
const infoWindows = [];
export default {
  components: {
    MapDomThematic,
    mouseTitle,
    mapPoleList,
  },
  props: {
    // 图层点位信息
    siteList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    // 设备
    siteList: {
      handler(val) {
        if (val.length) {
          this._initSystemPoints3Map(val);
        }
      },
    },
    theme(val) {
      mapMain.destroy();
      mapMain = null;
      this.getMapConfig();
    },
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
    }),
    ...mapState("common", ["theme"]),
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      markers: [],
      mouseName: "",
      polePoints: [],
      thematicItem: {},
      thematicType: "",
    };
  },
  deactivated() {
    this.closeAllInfoWindow();
  },
  async mounted() {
    await this.getMapConfig();
  },
  methods: {
    getMapConfig() {
      try {
        Promise.all([
          axios.get(map.getMapConfig),
          axios.get(map.getMapStyle),
        ]).then((res) => {
          // Promise.all([axios.get(map.getMapVectorConfig), axios.get(map.getMapVectorStyle)]).then(res => {
          this._initMap(
            res[0].data,
            this.theme === "light" ? null : res[1].data
          );
        });
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain.init(mapId, data, style);

        //比例尺控件
        var ctrl = new NPMapLib.Controls.ScaleControl();
        mapMain.map.addControl(ctrl);
        this.configDefaultMap();
        document.addEventListener("click", this.mouseOverClickFn, false);
        this.$emit("inited");
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    // 加载点位到地图上(资源图层/设备数据)
    _initSystemPoints3Map(points) {
      this.$nextTick(() => {
        // 加载点位
        mapMain.renderMarkers(
          mapMain.convertSystemPointArr3MapPoint(points),
          this.getMapEvents()
        );
      });
    },
    // 点击 设备
    getMapEvents() {
      const opts = {
        mouseover: (marker) => {
          if (marker.ext.isPoleGroupPoint) {
            marker.changeStyle &&
              marker.changeStyle(
                {
                  externalGraphic: LayerType["PoleGroupPoint"].hoverUrl,
                },
                true
              );
            const {
              ext: { Lat, Lon },
            } = marker;
            this.showPoleList({ Lat, Lon });
          } else {
            if (marker.changeStyle) {
              marker.changeStyle(
                {
                  externalGraphic: LayerType[marker.markType].hoverUrl,
                },
                true
              );
            } else {
              marker.getIcon().setImageUrl(LayerType[marker.markType].hoverUrl);
              marker.refresh();
            }
            poleInfoWindow.forEach((row) => {
              row.close();
            });
            poleInfoWindow.length = 0;
            this.showMouseDom(marker);
          }
        },
        mouseout: (marker) => {
          if (marker.ext.isPoleGroupPoint) {
            marker.changeStyle &&
              marker.changeStyle(
                {
                  externalGraphic: LayerType["PoleGroupPoint"].url,
                },
                true
              );
          } else {
            if (!marker.ext.clicked) {
              if (marker.changeStyle) {
                marker.changeStyle(
                  {
                    externalGraphic: marker.normalUrl,
                  },
                  true
                );
              } else {
                marker.getIcon().setImageUrl(LayerType[marker.markType].url);
                marker.refresh();
              }
            }
          }
          this.closeMouseInfoWindow();
        },
      };
      return opts;
    },
    closeMouseInfoWindow() {
      mouseInfoWindow.forEach((row) => {
        row.close();
      });
    },
    // 关闭多个弹框
    closeAllInfoWindow() {
      poleInfoWindow.forEach((row) => {
        row.close();
      });
      poleInfoWindow.length = 0;
      infoWindows.forEach((row) => {
        row.close();
      });
      infoWindows.length = 0;
    },
    // 鼠标浮动在资源图层图标上
    showMouseDom(marker) {
      let { Lat, Lon } = marker.ext;
      this.mouseName =
        marker.ext.placeName ||
        marker.ext.deviceName ||
        marker.ext.name ||
        marker.ext.address;
      const point = new NPMapLib.Geometry.Point(Lon, Lat);
      this.$nextTick(() => {
        const dom = this.$refs["mouseDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (60 / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 15;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        mouseInfoWindow.push(infoWindow);
      });
    },
    // 显示重合点位列表
    showPoleList({ Lat, Lon }) {
      // let polePoints = mapMain.poleGroupPoints.filter(v => !v.isHide && !v.isGnlxHide && v.Lat.substring(0, 7) == Lat.substring(0, 7) && v.Lon.substring(0, 8) == Lon.substring(0, 8))
      let polePoints = mapMain.poleGroupPoints.filter(
        (v) => !v.isHide && !v.isGnlxHide && v.Lat == Lat && v.Lon == Lon
      );
      polePoints.sort((a, b) => a.deviceName.localeCompare(b.deviceName));
      this.polePoints = polePoints;
      const point = new NPMapLib.Geometry.Point(Lon, Lat);
      this.$nextTick(() => {
        let h =
          10 +
          (this.polePoints.length > 6 ? 6 * 32 : this.polePoints.length * 32);
        const dom = this.$refs["poleListDom"].$el;
        let htmlFontSize = window.getComputedStyle(
          window.document.documentElement
        )["font-size"];
        let offsetLeft = ((320 / 192) * parseFloat(htmlFontSize)) / 2;
        let offsetTop = (h / 192) * parseFloat(htmlFontSize);
        let left = -offsetLeft + 5;
        let top = -offsetTop - 35;
        const opts = {
          offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: true, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          panMapIfOutOfView: true,
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        poleInfoWindow.push(infoWindow);
      });
    },
    mouseOverClickFn() {
      if (poleInfoWindow.length) {
        poleInfoWindow.forEach((row) => {
          row.close();
        });
        poleInfoWindow.length = 0;
      }
    },
    // 重置marker
    resetMarker() {
      if (mapMain) {
        mapMain.map.removeOverlays(this.markers);
      }
    },
    // 专题应用-告警弹框
    thematicAlert(pointItem, type = "fatigueDriving") {
      this.closeAllInfoWindow();
      this.resetMarker();
      this.thematicItem = pointItem;
      this.thematicType = type;
      pointItem.lon = pointItem.lon || pointItem.geoPoint.lon;
      pointItem.lat = pointItem.lat || pointItem.geoPoint.lat;
      const point = new NPMapLib.Geometry.Point(
        pointItem.lon,
        pointItem.lat + 0.00085
      );
      mapMain.map.centerAndZoom(point, mapMain.map.getMaxZoom());
      const opts = {
        width: 230, // 信息窗宽度
        height: 330, // 信息窗高度
        offset: new NPMapLib.Geometry.Size(
          -150,
          type == "fatigueDriving" ? -145 : -90
        ), // 信息窗位置偏移值
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
      };
      let marker = new NPMapLib.Symbols.Marker(pointItem);
      let size = new NPMapLib.Geometry.Size(80, 60);
      let imgUrl = require(`@/assets/img/map/alert.png`);
      let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
      icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height));
      marker.setIcon(icon);
      mapMain.map.addOverlay(marker);
      this.markers = [marker];
      const infoWindow = new NPMapLib.Symbols.InfoWindow(
        point,
        null,
        null,
        opts
      );
      const dom = this.$refs.MapDomThematic.$el;
      infoWindow.setContentDom(dom);
      mapMain.map.addOverlay(infoWindow);
      infoWindow.open(null, false);
      infoWindows.push(infoWindow);
    },
    closeFatiguDrivingWindow() {
      this.closeAllInfoWindow();
      this.resetMarker();
    },
  },
  beforeDestroy() {
    if (mapMain) {
      this.closeAllInfoWindow();
      this.resetMarker();
      mapMain.destroy();
      mapMain = null;
      document.removeEventListener("click", this.mouseOverClickFn, false);
    }
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }
}
/deep/ #npgis_GroupDiv {
  overflow: inherit !important;
}
/deep/.olPopupContent {
  overflow: inherit !important;
}
</style>
