<template>
  <ui-modal
    ref="modal"
    v-model="visible"
    title="请在地图上点击选择中心点"
    class="modal"
    @onCancel="onCancel"
    @onOk="onOK"
  >
    <!-- <Input class="search-input"
           ref="searchInput"
           placeholder="请输入地址获取经纬度"
           v-model.trim="keyWords"
           clearable
           @on-enter="mapSearchHandler">
    <Button slot="append"
            icon="ios-search"
            @click="mapSearchHandler"></Button>
    </Input> -->
    <!-- <div class="content-wrapper"
         v-if="visible">
      <MapBase :disableScroll="false" /> -->
    <div
      class="content-wrapper"
      ref="mapRef"
      style="height: 600px; width: 1270px"
    >
      <div :id="mapId" class="map"></div>
    </div>
  </ui-modal>
</template>

<script>
import MapBase from "@/components/map/index.vue";
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
let mapMain = null;
export default {
  name: "UiConfirm",
  components: {
    MapBase,
  },
  props: {
    defaultCenterPoint: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      visible: false,
      keyWords: "",
      centerPoint: "",
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      globalObj: "systemParam/globalObj",
    }),
  },
  created() {},
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    configDefaultMap() {
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      mapMain.map.setZoom(mapLayerLevel);
    },
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      // 配置初始化层级
      if (!mapMain) {
        mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain.init(mapId, data, style);
        mapMain.map.addEventListener("click", (Point) => {
          this.addOneLayer(Point);
        });
      }
      //this.configDefaultMap()
      if (!!this.defaultCenterPoint) {
        let defaultCenterPoint = this.defaultCenterPoint.split("_");
        const point = new NPMapLib.Geometry.Point(
          defaultCenterPoint[0],
          defaultCenterPoint[1]
        );
        this.addOneLayer(point);
        mapMain.map.setCenter(point);
      } else {
        this.addOneLayer(mapMain.map.getCenter());
      }
    },
    addOneLayer(point) {
      mapMain.map.clearOverlays();
      this.$nextTick(() => {
        let imgUrl = require(`@/assets/img/map/red-locate-icon.png`);
        let marker = new NPMapLib.Symbols.Marker(point);
        let size = new NPMapLib.Geometry.Size(60, 60);
        let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        // let label = new NPMapLib.Symbols.Label('222')
        // label.setStyle({
        //   fontSize: 14, //文字大小
        //   fontFamily: '宋体', //字体
        //   color: '#2C86F8', //文字前景色
        //   align: 'center', //对方方式
        //   isBold: true //是否粗体
        // })
        // label.setOffset(new NPMapLib.Geometry.Size(-0.5, 19.5))
        // marker.setLabel(label)
        marker.setIcon(icon);
        marker.k = point.lon + "_" + point.lat;
        mapMain.map.addOverlay(marker);
        this.centerPoint = marker.k;
      });
    },
    init() {
      this.visible = true;
      this.getMapConfig();
    },
    onCancel() {},
    onOK() {
      this.visible = false;
      this.$emit("getCenterPoint", this.centerPoint);
    },
    mapSearchHandler() {},
  },
  beforeDestroy() {
    if (mapMain) {
      mapMain.destroy();
      mapMain = null;
    }
  },
};
</script>

<style lang="less" scoped>
.modal {
  /deep/.ivu-modal {
    width: 1300px !important;
  }
  .content-wrapper {
    // height: 600px;
    // width: 1270px;
    .map {
      width: 100%;
      height: 100%;
    }
  }
  .search-input {
    width: 400px;
    margin-bottom: 10px;
    /deep/.ivu-input {
      width: 400px;
    }
    /deep/.ivu-icon-ios-search {
      color: #fff;
    }
  }
}
</style>
