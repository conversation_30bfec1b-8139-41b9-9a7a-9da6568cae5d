<template>
  <common-form v-bind="$props" ref="formData">
    <template #after="{ row }">
      <FormItem label="参数配置" class="right-item mb-sm">
        <Button type="dashed" class="area-btn" @click="regionalizationSelectVisible = true"
          >设置注册登记车辆数量</Button
        >
      </FormItem>
      <set-vehicle-num
        v-model="regionalizationSelectVisible"
        :edit="true"
        title="注册登记车辆数量"
        :data="row.generalParamSettingJson.orgData"
        @query="query"
      >
      </set-vehicle-num>
    </template>
  </common-form>
</template>

<script>
import { mapActions } from 'vuex';
export default {
  name: 'base-full-dir',
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
    setVehicleNum: require('../components/set-vehicle-or-portrait-num').default,
  },
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {
      styles: {
        width: 'fit-content',
      },
      visible: true,
      regionalizationSelectVisible: false,
      orgData: [],
    };
  },

  async created() {
    await this.setAreaList();
  },
  methods: {
    async validate() {
      try {
        let formData = await this.$refs.formData.validate();
        return {
          ...formData,
          generalParamSettingJson: {
            ...formData.generalParamSettingJson,
            orgData: this.orgData,
          },
        };
      } catch (error) {
        throw new Error(error);
      }
    },
    query(data) {
      this.orgData = data;
    },
    ...mapActions({
      setAreaList: 'common/setAreaList',
    }),
  },
};
</script>

<style lang="less" scoped></style>
