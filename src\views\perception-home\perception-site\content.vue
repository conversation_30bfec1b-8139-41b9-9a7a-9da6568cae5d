<template>
    <div class="data-warehouse-overview">
      <!-- <div class="data-warehouse-overview-bg"></div> -->
      <div class="data-warehouse-left">
        <div class="police-info m-b10">
          <div class="img">
            <img src="@/assets/img/home/<USER>" alt="" srcset="" />
          </div>
          <div class="info">
            <p class="title">{{ this.user.name }}，你好！</p>
            <p>
              <i class="iconfont icon-suoshujigou"></i>
              <span
                :title="item"
                v-for="(item, index) in user.organization"
                :key="index"
                :class="{ activeLine: item == '' }"
                >{{
                  item == ""
                    ? ""
                    : item.length > 4
                    ? item.slice(0, 4) + "..."
                    : item
                }}</span
              >
            </p>
            <!-- <p><i class="iconfont icon-zaixianyonghu"></i>
                          <span v-for="(item, index) in user.role" :key="index" :class="{'activeLine': item == ''}">
                              {{item}}
                          </span>
                      </p> -->
            <!-- <p class="line">当前在线：<span @click="handleOnline">{{ onlineNum }}</span></p> -->
          </div>
        </div>
        <ui-card
          title="我的作战"
          :padding="0"
          class="resource-classification relationship-map m-b10"
        >
          <div slot="extra" @click="handleMore(0)" class="relationship-map-head">
            <div class="more">更多</div>
            <img src="@/assets/img/home/<USER>" alt="" />
          </div>
          <ul class="fight_ul">
            <li class="flexLi" v-for="(item, index) in fightList" :key="index">
              <div class="left" @click="handelFight(item)">
                <i
                  class="iconfont"
                  :class="
                    item.source == 'owner' ? 'icon-caozuojilu' : 'icon-share'
                  "
                ></i>
                {{ item.name }}
              </div>
              <div class="right">
                {{ item.createTime }}
              </div>
            </li>
            <ui-empty v-if="fightList.length === 0"></ui-empty>
          </ul>
        </ui-card>
        <ui-card title="我的智搜" padding="20,0" class="data-source">
          <div
            class="cloudList"
            :load-more-disabled="coludJudge >= coludTotal"
            v-load-more.expand="{
              func: handlepullDown,
              target: '.ivu-timeline',
              delay: 500,
            }"
          >
            <Timeline>
              <TimelineItem v-for="(item, index) in cloudList" :key="index">
                <div class="left">
                  {{ item.createTime }}
                </div>
                <div class="right">
                  <div v-if="!item.searchDetail.searchType">
                    搜索了<span class="right-keyword" @click="handleKeyword(item)"
                      >【
                      <span class="right-keyword-tips" v-show-tips>
                        {{ item.searchDetail.keyWords }} </span
                      >】</span
                    >
                  </div>
                  <ul class="searchpicture" v-else>
                    以图搜图<span>
                      【{{
                        item.searchDetail.searchType == "1"
                          ? "人脸"
                          : item.searchDetail.searchType == "2"
                          ? "车辆"
                          : item.searchDetail.searchType == "3"
                          ? "人体"
                          : "非机动车"
                      }}】</span
                    >
                    <li class="cloudSearch-photo">
                      <!-- <ui-image :src="row.avatar ? row.avatar : url" viewer  /> -->
                      <img
                        v-for="(ite, ind) in item.searchDetail.imageBasesList"
                        :key="ind"
                        @click="handleColudImg(item, ite, ind)"
                        :src="ite"
                        alt=""
                      />
                    </li>
                  </ul>
                </div>
              </TimelineItem>
            </Timeline>
            <ui-empty
              v-if="cloudList.length === 0 && cloudLoading == false"
            ></ui-empty>
            <ui-loading v-if="cloudLoading"></ui-loading>
          </div>
        </ui-card>
      </div>
      <div class="data-warehouse-main">
        <div class="main">
          <div class="app">
            <div class="title">
              <img src="@/assets/img/home/<USER>" alt="" srcset="" />
              <h2>快捷应用</h2>
            </div>
            <div class="mainapp">
              <div class="circleapp">
                <div
                  class="item"
                  v-for="(item, index) in appList"
                  :key="index"
                  :class="'item' + index"
                >
                  <div class="menuShow" v-if="item.ident !== 0">
                    <div class="child" @click="handleMenu(index)">
                      {{ item.resourceName }}
                    </div>
                  </div>
                </div>
              </div>
              <img src="@/assets/img/home/<USER>" alt="" srcset="" />
              <div id="quickApp" class="quickApp">
                <div @click="hanldeAdd" class="add">
                  <Icon type="md-add" />
                </div>
              </div>
            </div>
          </div>
          <ui-card title="我的收藏" padding="0,20" class="collect">
            <div
              slot="extra"
              @click="handleMore(1)"
              class="relationship-map-head"
            >
              <div class="more">更多</div>
              <img src="@/assets/img/home/<USER>" alt="" />
            </div>
            <!-- <selectTag class="menu" :list="collectTypeList" @selectItem="selectItem" />
                      <two-swiper :cradList="cradList" />
                      <ui-loading v-if="loading"></ui-loading> -->
            <collect @selectItem="selectItem"></collect>
          </ui-card>
        </div>
      </div>
      <div class="data-warehouse-left">
        <div class="police-info m-b10">
          <ul class="dataShow">
            <li class="dataList" v-for="(item, index) in dataList" :key="index">
              <div class="data-msg" v-if="!item.type">
                <img :src="item.imgUrl" alt="" />
                <p class="data-total">
                  <count-to
                    :start-val="0"
                    :end-val="item.num"
                    :duration="1000"
                    class="h1"
                  ></count-to>
                </p>
                <p class="data-name">{{ item.name }}</p>
              </div>
              <div v-else class="line"></div>
            </li>
          </ul>
          <!-- <swiper-page /> -->
        </div>
  
        <ui-card title="公告" v-if="showModule('announcement')" class="resource-classification m-b10 announcement">
          <div class="card-content card-flex card-alarms">
            <div class="article-list">
              <h1>公安视频图像数据查询使用“七不准”</h1>
              <p>一、非因工作需要，不准查询使用公安视频图像数据。</p>
              <p>二、不准超出授权审批范围查询使用公安视频图像数据。</p>
              <p>
                三、不准向他人提供查询使用公安视频图像数据的公安机关数字证书、账号密码等登录凭证。
              </p>
              <p>四、不准冒用、借用他人名义查询使用公安视频图像数据。</p>
              <p>五、不准篡改、伪造或者擅自删除公安视频图像数据内容。</p>
              <p>
                六、未经审批，不准下载、翻拍、翻录或者对外提供、发布宣传公安视频图像数据。
              </p>
              <p>
                七、不准通过微信、QQ、钉钉、抖音等互联网即时通讯软件和网络社交媒体，传输、处理、存储、分享涉及国家秘密、警务工作秘密或者敏感信息的公安视频图像数据。
              </p>
              <p>
                对公安机关民警、辅警违反以上规定的，视情节轻重，给予批评教育、责令检查或者组织处理;
                构成违纪的，依法给予处分涉嫌犯罪的，依法追究刑事责任。
              </p>
            </div>
          </div>
        </ui-card>
  
        <ui-card
          v-if="showModule('canvas')"
          title="我的画布"
          class="resource-classification relationship-map m-b10"
        >
          <div slot="extra" @click="handleMore(2)" class="relationship-map-head">
            <div class="more">更多</div>
            <img src="@/assets/img/home/<USER>" alt="" />
          </div>
          <ul class="flex_ul">
            <li class="flexLi2" v-for="(item, index) in canvasList" :key="index">
              <div class="left">
                <img :src="item.imageUrl" alt="" srcset="" />
              </div>
              <div class="right" @click="handleCanvs(item)">
                <p class="title" :title="item.name">
                  {{ item.name }}
                </p>
                <p class="time">{{ item.createTime }}</p>
              </div>
            </li>
            <ui-empty v-if="canvasList.length === 0"></ui-empty>
          </ul>
        </ui-card>
        <div class="ui-card"  v-if="showModule('alarm')">
          <div class="card-head">
            <div class="card-tab-left">
              <div
                class="capture-title face-capture"
                :class="carType === 1 ? 'capture-active' : ''"
                @click="handleTab(1)"
              >
                <span>人员报警</span>
              </div>
              <div
                class="capture-title car-capture"
                :class="carType === 2 ? 'capture-active' : ''"
                @click="handleTab(2)"
              >
                <span>车辆报警</span>
              </div>
            </div>
            <div class="relationship-map-head">
              <div class="more" @click="handleAlarmMore">更多</div>
              <img src="@/assets/img/home/<USER>" alt="" />
            </div>
          </div>
          <div class="card-content card-flex">
            <alarm-tab ref="alarmTab" :carType="carType"></alarm-tab>
          </div>
        </div>
        <!-- <ui-card title="常用模型" class="resource-classification relationship-map">
                  <ul class="flex_ul">
                      <li class="flexLi3" v-for="(item, index) in modelList" @click="handleModelList(index)" :key="index">
                          <div class="flexLi2 flex1">
                              <div class="left">
                                  <img :src="item.url" alt="" srcset="">
                              </div>
                              <div class="right">
                                  <p class="title">{{item.title}}</p>
                                  <p class="num">使用<span>{{item.number}}</span>次</p> 
                              </div>
                          </div>
                          <div>最近使用：{{item.data}}</div>
                      </li>
                  </ul>
              </ui-card> -->
      </div>
      <onlineModal ref="onlineModal"></onlineModal>
      <fightModal ref="fightModal"></fightModal>
      <rapidModal
        ref="rapidModal"
        :list="appList"
        @motifyquery="motifyquery"
      ></rapidModal>
    </div>
  </template>
  <script>
  import * as echarts from "echarts";
  import quickJson from "./components/quick-json.js";
  import dataSourceIcon from "@/assets/img/data-warehouse/data-source-icon.png";
  import dataSourceGraphic from "@/assets/img/data-warehouse/data-source-graphic.png";
  import CountTo from "vue-count-to";
  import RadarEchart from "@/components/echarts/radar-echart";
  import PieEchart from "@/components/echarts/pie-echart";
  import LineEchart from "@/components/echarts/line-echart";
  import swiperPage from "./components/swiper.vue";
  import onlineModal from "./components/onlineModal.vue";
  import fightModal from "./components/fightModal.vue";
  import rapidModal from "./components/rapidModal.vue";
  import collect from "./components/collect/index.vue";
  import alarmTab from "./components/alarm-tab.vue";
  import Setting from "@/libs/configuration/setting";
  import {
    onLine,
    queryCloudSearchpageList,
    queryMyQuickApplicationList,
    queryDataByKeysNew,
  } from "@/api/home";
  import { anvasList } from "@/api/number-cube";
  
  import { getAllCombatRecord } from "@/api/operationsOnTheMap";
  import { mapGetters, mapActions } from "vuex";
  export default {
    name: "perception-site",
    components: {
      RadarEchart,
      PieEchart,
      LineEchart,
      CountTo,
      swiperPage,
      onlineModal,
      fightModal,
      rapidModal,
      collect,
      alarmTab,
    },
    props: {
        modules: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        onlineNum: 0,
        canvasList: [],
        modelList: [
          {
            url: require("@/assets/img/model/pic1.png"),
            title: "轨迹分析",
            number: 200,
            data: "2022-02-08 13:01:02",
          },
          {
            url: require("@/assets/img/model/pic12.png"),
            title: "同行分析",
            number: 169,
            data: "2022-02-08 13:01:02",
          },
          {
            url: require("@/assets/img/model/pic4.png"),
            title: "落脚点分析",
            number: 140,
            data: "2022-02-08 13:01:02",
          },
          {
            url: require("@/assets/img/model/pic13.png"),
            title: "昼伏夜出",
            number: 123,
            data: "2022-02-08 13:01:02",
          },
        ],
        appList: [
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
          { ident: 0 },
        ],
        cradList: [],
        favoriteObjectType: "1",
        dataServiceActive: 1, //1-数据共享, 2-数据服务
        setIn: null,
        dataWareHouseIndex: 0,
        dataWareHouseGif: "",
        dataWareHouseGifSize: 40,
        dataList: [
          {
            imgUrl: require("@/assets/img/home/<USER>"),
            num: 0,
            name: "视图设备",
          },
          { type: "line" },
          {
            imgUrl: require("@/assets/img/home/<USER>"),
            num: 0,
            name: "感知设备",
          },
          { type: "line" },
          {
            imgUrl: require("@/assets/img/home/<USER>"),
            num: 0,
            name: "报警数据",
          },
        ],
        fightList: [],
        cloudList: [],
        user: {
          name: " ",
          organization: " ",
          role: " ",
        },
        loading: false,
        cloudLoading: false,
        coludJudge: 10,
        coludPage: {
          pageSize: 10,
          pageNumber: 1,
        },
        coludTotal: 0,
        carType: 1, //1驾乘/2名下车辆
      };
    },
    computed: {
      ...mapGetters({
        userInfo: "userInfo",
        relationObj: "systemParam/relationObj",
        graphObj: "systemParam/graphObj", // 是否有图谱
      }),
    },
    async created() {
      await this.getSystemAllData();
      await this.getDictData();
      this.cloudList = [];
      this.coludPage = {
        pageSize: 10,
        pageNumber: 1,
      };
      this.init();
      this.queryCloud();
    },
    mounted() {
      // 初始化渲染数仓中间gift图
      var params = {
        container: document.getElementById("quickApp"),
        renderer: "svg",
        loop: true,
        autoplay: true,
        animationData: quickJson,
      };
      lottie.loadAnimation(params);
      this.motifyquery();
    },
    methods: {
      ...mapActions({
        getSystemAllData: "systemParam/getSystemAllData",
        getDictData: "dictionary/getDictAllData",
      }),
      showModule(moduleName){
        return this.modules.includes(moduleName);
      },
      // 人员报警、车辆报警
      handleTab(index) {
        this.carType = index;
        this.$refs.alarmTab.handleTabList(index);
      },
      init() {
        // 当前在线
        //   onLine({
        //     current: 1,
        //     size: 10,
        //   }).then((res) => {
        //     this.onlineNum = res.total;
        //   });
        let orgVoList = this.userInfo.orgVoList.map((item) => item.orgName);
        let roleVoList = this.userInfo.roleVoList.map((item) => item.roleName);
        let orglist = this.addline(orgVoList);
        let roleList = this.addline(roleVoList);
        this.user = {
          name: this.userInfo.name,
          organization: orglist,
          role: roleList,
        };
        //
        this.fightList = [];
        getAllCombatRecord({ pageNumber: 1, pageSize: 5 }).then((res) => {
          if (res.code === 200) {
            this.fightList = res.data.slice(0, 6);
          }
        });
        // 数据来源
        let params = {
          simScore:
            Number(this.globalObj.searchForPicturesDefaultSimilarity) / 100,
          idCardNo: null,
          vid: null,
          operationType: null,
          plateNo: null,
        };
        queryDataByKeysNew(params).then((res) => {
          if (res.data) {
            let data = res.data;
            this.dataList[0].num = data.viewNum;
            this.dataList[2].num = data.perceptionNum;
            this.dataList[4].num = data.alarmNum;
          }
        });
  
        if (this.graphObj) {
          var pages = {
            params: {
              pageNumber: 1,
              pageSize: 6,
            },
          };
  
          anvasList({
            graphId: this.relationObj.graphInfo.graphId,
            ...pages,
          }).then((res) => {
            console.log("我的画布列表------", res);
            this.canvasList = res.data.entities;
          });
        }
      },
      queryCloud() {
        // 我的云搜记录
        this.cloudLoading = true;
        queryCloudSearchpageList(this.coludPage)
          .then((res) => {
            res.data.entities.map((item) => {
              item.searchDetail = JSON.parse(item.searchDetail);
              if (item.searchDetail.searchType && item.searchDetail.imageBases) {
                let urlList = item.searchDetail.imageBases.split(",");
                item.searchDetail.imageBasesList = [];
                urlList.map((ite, index) => {
                  if (index % 2 == 0) {
                    item.searchDetail.imageBasesList.push(
                      ite + "," + urlList[index + 1]
                    );
                  }
                });
              }
            });
            this.coludTotal = res.data.total;
            this.cloudList.push(...res.data.entities);
            this.cloudLoading = false;
          })
          .finally(() => {
            this.cloudLoading = false;
          });
      },
      selectItem(item) {
        this.favoriteObjectType = item.value;
      },
      // 快捷应用
      motifyquery() {
        this.appList = Array.apply(null, { length: 10 }).map(() => {
          return { ident: 0 };
        });
        let jurisMenu = [];
        this.userInfo.sysApplicationVoList.forEach((item) => {
          if (item.applicationCode == applicationCode) {
            jurisMenu.push(...item.children);
          }
        });
        let newMenu = this.uselist(jurisMenu);
        let mapMenu = new Map(newMenu.map((item) => [item.id, item]));
        let fast = {
          id: "",
          resourceId: "",
          sort: "",
          userId: "",
        };
        queryMyQuickApplicationList(fast).then((res) => {
          res.data.forEach((item) => {
            if (mapMenu.get(item.resourceId)) {
              this.$set(this.appList, item.sort, item);
            }
          });
        });
      },
      // 处理目录数据
      uselist(list) {
        let menu = [];
        list.forEach((item, index) => {
          if (item.children && item.children.length > 0) {
            menu.push(...item.children);
          } else {
            menu.push(item);
          }
        });
        return menu;
      },
      // 点击快捷应用
      handleMenu(index) {
        // this.$router.push({
        // 	name: `${this.appList[index].permission}`
        // })
        const { href } = this.$router.resolve({
          name: `${this.appList[index].permission}`,
        });
        window.open(`${href}?noMenu=1`, "_blank");
      },
      // 切换共享服务
      shareServiceTabHandle(val) {
        this.dataServiceActive = val;
      },
      // 当前在线
      handleOnline() {
        this.$refs.onlineModal.show();
      },
      // 点击我的作战
      handelFight(item) {
        this.$router.push({
          path: "/track-center/map-track",
          query: {
            content: {
              name: item.name,
              dataText: item.dataText,
              id: item.id,
            },
          },
        });
      },
      handlepullDown() {
        this.coludPage.pageNumber++;
        this.coludJudge += 10;
        this.queryCloud();
      },
  
      /**
       * @description: 以图搜图，直接跳到分类搜索的相应类别下
       * @param {object} row 搜索信息 row.searchDetail.searchType: 人脸 - 1，车辆 - 2，人体 - 3，非机动车 -4
       */
      handleColudImg(row) {
        // let url = [];
        let sectionName = "";
        switch (row.searchDetail.searchType) {
          case "1":
            sectionName = "faceContent";
            break;
          case "2":
            sectionName = "vehicleContent";
            break;
          case "3":
            sectionName = "humanBodyContent";
            break;
          case "4":
            sectionName = "nonmotorVehicleContent";
            break;
        }
        this.$router.push({
          path: "/wisdom-cloud-search/search-center",
          query: {
            sectionName,
            // 当前版本只有单图搜索
            urlList: JSON.stringify({
              feature: row.searchDetail.features,
              // imageBasesList中的内容包含base64码前缀，imageBase字段不需要
              imageBase: row.searchDetail.imageBases.replace(
                "data:image/jpeg;base64,",
                ""
              ),
              fileUrl: row.searchDetail.imageBases,
            }),
          },
        });
      },
      // 文字
      handleKeyword(item) {
        this.$router.push({
          path: "/wisdom-cloud-search/cloud-default-page",
          query: {
            keyWord: item.searchDetail.keyWords,
            dateType: item.searchDetail.dateType,
            startDate: item.searchDetail.startDate,
            endDate: item.searchDetail.endDate,
          },
        });
      },
      // 画布
      handleCanvs(item) {
        var query = {
          id: item.id,
          proximity: 1,
          name: item.name,
          type: "detail",
        };
        this.$router.push({ name: "number-cube-info", query: query });
      },
      // 更多
      handleMore(index) {
        switch (index) {
          case 0:
            this.$refs.fightModal.show();
            break;
          case 1:
            this.$router.push({
              path: "/user/collect",
              query: {
                type: this.favoriteObjectType,
              },
            });
            break;
          case 2:
            this.$util.common.openNewWindow({
              name: "number-cube",
              query: { noMenu: 1 },
            });
            break;
        }
      },
      // 人员报警， 车辆报警更多
      handleAlarmMore() {
        this.$util.common.openNewWindow({
          path: "/target-control/alarm-manager",
          query: {
            page: this.carType == 1 ? "people" : "vehicle",
            noMenu: 1,
            noSearch: 1,
            noDate: 1, // 这个地方跳转过去不能使用时间查报警
          },
        });
      },
      // 常用模型跳转
      handleModelList(index) {
        let name = {
          0: "trajectory-analysis",
          1: "peerAnalysis",
          2: "foothold",
          3: "nightDazed",
        };
        this.$router.push({
          name: name[index],
        });
      },
      // 添加
      hanldeAdd() {
        this.$refs.rapidModal.show();
      },
      // 加线条
      addline(val) {
        let list = [];
        val.map((item, index) => {
          if (index == val.length - 1) {
            list.push(item);
          } else {
            list.push(item, "");
          }
        });
        return list;
      },
    },
  };
  </script>
  <style lang="less" scoped>
  .m-b10 {
    margin-bottom: 10px;
  }
  .data-warehouse-overview {
    display: flex;
    flex: 1;
    background: #dde1ea;
    position: relative;
    .data-warehouse-overview-bg {
      width: 45%;
      height: 80%;
      background: #fff;
      filter: blur(50px);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .data-warehouse-left {
      width: 500px;
      height: 100%;
      display: flex;
      flex-direction: column;
      /deep/.ui-card {
        width: 100%;
        flex: unset;
        height: 38%;
        .card-content {
          display: flex;
          height: calc(~"100% - 30px");
        }
        .radar-echart {
          width: 50%;
          height: 100%;
        }
      }
      .police-info {
        // width: 500px;
        width: 100%;
        // height: 200px;
        background: #ffffff;
        box-shadow: 0px 3px 5px fade(#93abce, 70%);
        border-radius: 0.02083rem;
        flex: 1;
        display: flex;
        align-items: center;
        .img {
          position: relative;
          width: 40%;
          padding-left: 20px;
          img {
            position: absolute;
            bottom: 0;
            width: 153px;
            height: 167px;
          }
        }
        .info {
          position: relative;
          flex: 1;
          padding: 10px 10px 10px 10px;
          .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          p {
            line-height: 30px;
            font-size: 14px;
            display: flex;
            align-items: center;
            .iconfont {
              margin-right: 10px;
            }
          }
          .activeLine {
            display: block;
            width: 1px;
            height: 14px;
            background: #d3d7de;
            margin: 0 6px;
          }
          .line {
            color: rgba(0, 0, 0, 0.45);
            position: absolute;
            bottom: 10px;
            span {
              cursor: pointer;
              color: #2c86f8;
            }
          }
        }
      }
      .resource-classification {
        /deep/ .card-content {
          padding: 0 !important;
        }
      }
      .data-source {
        /deep/ .ivu-timeline-item-head {
          top: 6px;
        }
        /deep/ .ivu-timeline-item {
          width: 100%;
          padding: 0;
        }
        /deep/ .ivu-timeline-item-tail {
          top: 6px;
        }
        /deep/ .ivu-timeline-item-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        /deep/ .ivu-timeline-item-head {
          width: 5px;
          height: 5px;
          margin-left: 4px;
          background-color: #2d8cf0;
        }
        .cloudList {
          .ivu-timeline {
            overflow-y: auto;
            height: 100%;
          }
        }
        .right {
          span {
            color: #2c86f8;
          }
          .right-keyword {
            cursor: pointer;
            &-tips {
              max-width: 200px;
              display: inline-block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              vertical-align: bottom;
            }
          }
          .searchpicture {
            display: flex;
            align-items: center;
            .cloudSearch-photo {
              display: flex;
              img {
                width: 24px;
                height: 24px;
                margin-left: 8px;
                cursor: pointer;
              }
            }
          }
        }
      }
      .relationship-map {
        .relationship-map-head {
          display: flex;
          // margin-right: 10px;
          color: rgba(0, 0, 0, 0.35);
          cursor: pointer;
          align-items: center;
          .more {
            margin-right: 6px;
            font-size: 14px;
          }
          .total-entity,
          .relationship-entity {
            display: flex;
            align-items: center;
            margin-right: 40px;
            .name {
              font-size: 12px;
              line-height: 18px;
            }
            .number {
              font-size: 14px;
              font-family: "MicrosoftYaHei-Bold";
              font-weight: bold;
              margin-left: 4px;
              line-height: 20px;
            }
          }
          .relationship-entity {
            margin-right: 30px;
          }
        }
        /deep/ .card-content {
          //   padding: 0 !important;
          height: calc(~"100% - 70px");
          margin-top: 20px;
        }
      }
      .fight_ul {
        overflow: hidden;
        width: 100%;
        padding: 0 20px;
        padding-bottom: 10px;
        display: flex;
        flex-direction: column;
        align-items: stretch;
        height: auto;
        .iconfont {
          line-height: normal;
        }
        .flexLi {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: #f9f9f9;
          padding: 7px;
          margin-bottom: 7px;
          .left {
            font-weight: bold;
            font-size: 14px;
            color: #2c86f8;
            cursor: pointer;
            .iconfont {
              margin-right: 6px;
            }
          }
          .right {
            font-size: 14px;
          }
        }
      }
      .flex_ul,
      /deep/.ivu-timeline {
        width: 100%;
        padding: 0 20px;
        display: flex;
        flex-flow: wrap;
        justify-content: space-between;
        align-content: flex-start;
        padding-bottom: 10px;
        overflow: auto;
        .flexLi2 {
          width: 49%;
          display: flex;
          align-items: center;
          height: calc(~"33.3% - 10px");
          background: #f9f9f9;
          padding: 0 8px;
          margin-bottom: 10px;
          overflow: hidden;
          .left {
            font-weight: 600;
            color: #2c86f8;
            .iconfont {
              margin-right: 6px;
            }
            img {
              width: 80px;
              height: 65px;
              object-fit: contain;
            }
          }
          .right {
            margin-left: 10px;
            flex: 1;
            width: 0;
            .title {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              font-size: 14px;
              font-weight: bold;
              color: rgba(0, 0, 0, 0.9);
              cursor: pointer;
            }
            .time {
              margin-top: 5px;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.6);
            }
          }
        }
        .flexLi3 {
          width: 49%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex-direction: column;
          background: #f9f9f9;
          padding: 10px 8px;
          margin-bottom: 10px;
          cursor: pointer;
          &:hover {
            // scale: 1.05;
          }
          .flex1 {
            width: 100%;
            justify-content: start;
            img {
              width: 60px !important;
              height: 60px !important;
            }
          }
          .left {
            width: 60px;
            font-weight: 600;
            color: #2c86f8;
            .iconfont {
              margin-right: 6px;
            }
          }
          .right {
            padding-left: 20px;
            .title {
              font-size: 18px;
              font-weight: bold;
              color: #2c86f8;
            }
            .num {
              margin-top: 10px;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.9);
              span {
                color: #2c86f8;
                margin: 0 3px;
              }
            }
          }
        }
      }
      .data-access {
        overflow: unset;
        /deep/ .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
        /deep/ .card-content {
          padding: 0 20px !important;
        }
        .share-service-tabs {
          display: flex;
          .tab-item,
          .tab-item-active {
            font-size: 14px;
            line-height: 20px;
            margin-right: 20px;
            cursor: pointer;
          }
          .tab-item-active {
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            position: relative;
          }
          .tab-item-active::after {
            content: "";
            width: 100%;
            height: 2px;
            background: #2c86f8;
            position: absolute;
            left: 0;
            bottom: -1px;
          }
        }
      }
      .dataShow {
        width: 100%;
        display: flex;
        align-items: center;
        padding: 0 42px;
        justify-content: space-around;
        .dataList {
          display: flex;
          .data-msg {
            display: flex;
            flex-direction: column;
            align-items: center;
            img {
              width: 90px;
              height: 90px;
            }
            .data-total {
              font-size: 20px;
              font-weight: bold;
              color: #2c86f8;
              margin: 5px 0;
            }
            .data-name {
              font-size: 12px;
              color: rgba(0, 0, 0, 0.6);
            }
          }
          .line {
            height: 60px;
            border-left: 1px solid #d3d7de;
          }
        }
      }
      .data-quality {
        overflow: unset;
        /deep/ .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
        /deep/ .card-content {
          padding: 0 20px 0 10px !important;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .data-quality-ul {
          .data-quality-li {
            background: #ebedf1;
            width: 154px;
            height: 30px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            box-sizing: border-box;
            margin-top: 10px;
            .name {
              font-size: 14px;
              line-height: 20px;
            }
            .number {
              font-size: 16px;
              line-height: 22px;
            }
          }
          & > div:first-child {
            margin: 0;
          }
        }
      }
      .data-job {
        /deep/ .card-content {
          padding: 10px 16px !important;
        }
        .data-job-table {
          display: flex;
          flex-direction: column;
          flex: 1;
          .data-job-table-head {
            background: #ebedf1;
            height: 30px;
            border-radius: 4px;
            display: flex;
            & > span {
              flex: 1;
              display: flex;
              justify-content: center;
              align-items: center;
              font-weight: bold;
              font-family: "MicrosoftYaHei-Bold";
              font-size: 14px;
              line-height: 20px;
            }
            & > span:last-child {
              flex: 2;
            }
          }
          .data-job-table-body {
            flex: 1;
            overflow: hidden;
            .data-job-table-tr {
              height: 32px;
              display: flex;
              background: #fff;
              & > span {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 14px;
                line-height: 20px;
              }
              & > span:last-child {
                flex: 2;
              }
            }
            .data-job-table-tr:nth-child(2n + 1) {
              background: #f9f9f9;
            }
          }
          .data-job-table-body:hover {
            overflow: auto;
          }
        }
      }
    }
    .data-warehouse-main {
      flex: 1;
      position: relative;
      // padding: 10px 20px;
      padding: 10px 20px 0 20px;
      height: 100%;
      .main {
        height: 100%;
        display: flex;
        flex-direction: column;
        .app {
          flex: 1;
          position: relative;
          height: 100%;
          width: 100%;
          // background: url('~@/assets/img/home/<USER>') no-repeat center;
          // background-size: contain;
          box-sizing: border-box;
          .title {
            text-align: center;
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 999;
            h2 {
              margin-top: -42px;
            }
          }
          .mainapp {
            position: relative;
            width: 100%;
            height: 100%;
            margin-top: 24px;
            img {
              position: absolute;
              z-index: 0;
              width: 76%;
              left: 50%;
              top: 49%;
              transform: translate(-50%, -50.5%);
            }
          }
          .circleapp {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100px;
            height: 100px;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            z-index: 9;
            display: flex;
            justify-content: center;
            align-items: center;
            .item {
              position: absolute;
              width: 100px;
              height: 100px;
              top: -8px;
              z-index: 10;
              border-radius: 50%;
              .menuShow {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                background: linear-gradient(
                  180deg,
                  rgba(91, 163, 255, 0.3),
                  rgba(44, 134, 248, 0.3)
                );
                box-shadow: 0px 10px 24px 0px rgba(44, 134, 248, 0.2);
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .child {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 68px;
                height: 68px;
                border-radius: 50%;
                background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
                color: #fff;
                font-size: 14px;
                font-weight: bold;
                cursor: pointer;
                word-break: keep-all;
              }
            }
            .item0 {
              transform: rotateZ(36deg) translateY(202px);
              .child {
                transform: rotateZ(324deg);
              }
            }
            .item1 {
              transform: rotateZ(72deg) translateY(202px);
              .child {
                transform: rotateZ(288deg);
              }
            }
            .item2 {
              transform: rotateZ(108deg) translateY(202px);
              .child {
                transform: rotateZ(252deg);
              }
            }
            .item3 {
              transform: rotateZ(144deg) translateY(202px);
              .child {
                transform: rotateZ(216deg);
              }
            }
            .item4 {
              transform: rotateZ(180deg) translateY(202px);
              .child {
                transform: rotateZ(180deg);
              }
            }
            .item5 {
              transform: rotateZ(216deg) translateY(202px);
              .child {
                transform: rotateZ(144deg);
              }
            }
            .item6 {
              transform: rotateZ(252deg) translateY(202px);
              .child {
                transform: rotateZ(108deg);
              }
            }
            .item7 {
              transform: rotateZ(288deg) translateY(202px);
              .child {
                transform: rotateZ(72deg);
              }
            }
            .item8 {
              transform: rotateZ(324deg) translateY(202px);
              .child {
                transform: rotateZ(36deg);
              }
            }
            .item9 {
              transform: rotateZ(360deg) translateY(202px);
              .child {
                transform: rotateZ(0deg);
              }
            }
          }
          .quickApp {
            position: relative;
            width: 300px;
            height: 300px;
            overflow: hidden;
            margin: 0 auto;
            z-index: 10;
            display: flex;
            justify-content: center;
            align-items: center;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 50%;
            .add {
              display: none;
            }
            &:hover {
              .add {
                position: absolute;
                width: 60px;
                height: 60px;
                background: #fbfbfb;
                z-index: 10;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                color: #bdbbbb;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                cursor: pointer;
                .ivu-icon {
                  font-size: 28px;
                }
              }
            }
          }
        }
        .collect {
          position: relative;
          height: 250px;
          /deep/ .card-head {
            color: #999;
            margin-right: 10px;
          }
          /deep/.card-content {
            height: calc(~"100% - 30px");
          }
  
          .relationship-map-head {
            padding-right: 10px;
            display: flex;
            color: rgba(0, 0, 0, 0.35);
            cursor: pointer;
            align-items: center;
            .more {
              margin-right: 6px;
              font-size: 14px;
            }
          }
        }
      }
    }
    // .pie-echart {
    //   width: 140px;
    //   height: 140px;
    //   background: linear-gradient(150deg, #EAF7FF 0%, #F9FCFF 100%);
    //   box-shadow: inset 0px 5px 6px 0px rgba(151, 197, 255, 0.2);
    //   border-radius: 50%;
    // }
  }
  
  .delbg {
    background: transparent !important;
    box-shadow: none !important;
  }
  
  .resource-directory {
    .resource-directory-head {
      display: flex;
      margin-top: 10px;
      .resource-table,
      .data-number {
        display: flex;
        align-items: center;
      }
      .data-number {
        margin: 0 34px 0 20px;
      }
      .icon {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
      }
      .name {
        font-size: 12px;
        line-height: 18px;
      }
    }
    /deep/ .card-content {
      padding: 20px 0 !important;
    }
  }
  .card-tab-left {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }
  .relationship-map-head {
    padding-right: 10px;
    display: flex;
    color: rgba(0, 0, 0, 0.35);
    cursor: pointer;
    align-items: center;
    .more {
      margin-right: 6px;
      font-size: 14px;
    }
  }
  .capture-title {
    font-size: 16px;
    cursor: pointer;
    line-height: 30px;
    text-align: center;
    background: #d3d7de;
    color: #666;
    transform: skewX(-18deg);
    padding: 0 23px;
    // left: -6px;
    margin-left: -6px;
    span {
      transform: skewX(18deg);
      display: inline-block;
    }
  }
  .face-capture {
    // position: relative;
  }
  .car-capture {
    // position: absolute;
    left: 120px;
  }
  .capture-active {
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    color: #fff;
    font-weight: bold !important;
  }
  .card-flex {
    flex-direction: column;
  }
  .card-alarms {
    margin-top: 0 !important;
  }
  .announcement {
    height: calc(~"78% + 20px") !important;
    margin-bottom: 0 !important;
  }
  .article-list {
    padding: 20px;
    font-weight: bold;
    h1 {
      text-align: center;
      margin-bottom: 20px;
    }
    p {
      text-indent: 2em;
      font-size: 18px;
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .alarms-vehicle {
    display: none;
  }
  </style>
  