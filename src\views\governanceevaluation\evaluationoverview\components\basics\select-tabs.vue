<template>
  <div class="tabs" :style="{ backgroundColor: needExpand ? '#0b2348' : '' }" v-clickoutside="hide">
    <i class="icon-font icon-xialazhankai up" v-if="neeShow" :class="{ rotate: !showAll }" @click="showAll = !showAll">
    </i>
    <div class="positions" :style="{ backgroundColor: needExpand ? '#0b2348' : '' }">
      <div class="content" :class="{ single: !showAll }" v-if="list.length !== 0">
        <ul class="ul">
          <li
            :class="{ active: multiSelect ? item.select : curIndex == index }"
            v-for="(item, index) in list"
            :key="index"
            @click="liClick(item, index)"
          >
            {{ item.name }}
          </li>
          <div class="clear"></div>
        </ul>
      </div>
      <div class="no-data-text" v-if="list.length === 0">暂无异常原因</div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ui-select-tabs',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    //是否需要展开按钮
    needExpand: {
      type: Boolean,
      default: true,
    },
    // 是否开启多选，默认开启多选
    multiSelect: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showAll: false,
      curIndex: 0,
      neeShow: true,
    };
  },
  mounted() {},
  methods: {
    liClick(item, index) {
      // 多选
      if (this.multiSelect) {
        this.$set(item, 'select', !item.select);
      } else {
        // 单选
        this.curIndex = index;
      }
      this.$emit('selectInfo', this.getSelection());
    },

    // 获取已选择的所有数据
    getSelection() {
      // 多选
      if (this.multiSelect) {
        return this.list.filter((item) => {
          return item.select;
        });
      } else {
        // 单选
        return this.list[this.curIndex];
      }
    },

    // 重置
    reset() {
      this.list.forEach((item) => {
        this.$set(item, 'select', false);
      });
    },
    hide() {
      this.showAll = false;
    },
    // init() {
    //   setTimeout(() => {
    //     this.$nextTick(() => {

    //     })
    //   }, 500)
    // },
  },
  watch: {
    showAll(newVal) {
      if (!newVal) {
        var list = JSON.parse(JSON.stringify(this.list));
        this.list = list;
      }
    },
    list: {
      handler(val) {
        if (val.length !== 0) {
          // this.init()
          this.neeShow = true;
        } else {
          this.neeShow = false;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.tabs {
  position: relative;
  // padding: 6px;
  min-height: 46px;
  padding-right: 60px;
  z-index: 10;
  .positions {
    position: absolute;
    width: 100%;
    padding: 6px;
    z-index: 6;
  }
  .no-data-text {
    top: 25px;
    left: 50%;
    position: absolute;
    color: #3c6094;
    font-size: 14px;
    transform: translate(-50%, -50%);
  }
  .up {
    position: absolute;
    right: 10px;
    top: 15px;
    color: #18599e;
    font-size: 12px;
    z-index: 10;
    transform: rotate(180deg) scale(0.8);
  }

  .rotate {
    transform: scale(0.8);
  }

  .content {
    max-height: 129px;
    overflow: auto;
    padding-right: 25px;
  }

  .ul {
    li {
      float: left;
      padding: 0 10px;
      height: 32px;
      line-height: 32px;
      border: 1px solid #085c8a;
      color: #8797ac;
      border-radius: 3px;
      margin-right: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      &:hover {
        background: #04478e;
        color: #fff;
        border: 1px solid #0a5cb3;
      }
    }
    .active {
      background: #04478e;
      color: #fff;
      border: 1px solid #0a5cb3;
    }
  }

  .single {
    position: absolute;
    height: 35px;
    overflow: hidden;
    z-index: 8;
  }

  .clear {
    clear: both;
  }
}
</style>
