<template>
  <choose-recheck-device
    v-bind="getAttrs"
    v-on="$listeners"
    @reset="
      () => {
        $emit('reset');
      }
    "
  >
    <template v-for="(item, index) in tableColumns" #[item.slot]="{ row }">
      <slot :name="item.slot" :row="row" :item="item" :index="index"></slot>
    </template>
    <template v-for="name in getSlots" v-slot:[name]>
      <slot :name="name" />
    </template>
  </choose-recheck-device>
</template>
<script>
export default {
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        tableColumns: this.tableColumns,
        ...this.$attrs,
      };
    },
    /**
     *
     * @return 返回除了tableColumns以外的slot
     */
    getSlots() {
      let slots = [];
      Object.keys(this.$scopedSlots).forEach((slot) => {
        if (!this.tableColumns.find((item) => item.slot === slot)) {
          slots.push(slot);
        }
      });
      return slots;
    },
  },
  components: {
    ChooseRecheckDevice: require('../../../components/choose-recheck-device/choose-recheck-device').default,
  },
};
</script>
<style lang="less" scoped></style>
