<template>
  <component :is="componentName" v-bind="$attrs" v-on="$listeners"></component>
</template>

<script>
export default {
  name: 'face-vehicle-url',
  props: {
    // 人工复核 ：设备模式 or 图片模式
    mode: {
      default: 'device',
    },
  },
  data() {
    return {};
  },
  computed: {
    componentName() {
      return this.mode === 'device' ? 'ModeDevice' : 'ModePic';
    },
  },
  methods: {},
  watch: {},
  components: {
    ModePic: require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/components/face-vehicle-url-algorithm/mode-pic.vue').default,
    ModeDevice: require('./mode-device.vue').default,
  },
};
</script>

<style lang="less" scoped></style>
