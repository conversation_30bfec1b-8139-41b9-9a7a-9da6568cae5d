<template>
  <div class="ui-card m-b20">
    <div class="card-head">
      <p class="capture-title face-capture capture-active">
        <span>最新报警</span>
      </p>
      <div class="capture-menu">
        <Menu
          mode="horizontal"
          theme="light"
          :active-name="selectAlarmKind.title"
          @on-select="selectHandler"
        >
          <MenuItem
            v-for="(item, index) in componentList"
            :key="item.key"
            :name="item.title"
            :title="item.title"
          >
            {{ item.title }}
          </MenuItem>
        </Menu>
      </div>
      <div class="extra-right">
        <span class="more" @click="handleAlarmMore()"
          >更多 <i class="iconfont icon-more"></i
        ></span>
      </div>
    </div>
    <div class="card-content" :style="selectAlarmKind.style">
      <keep-alive>
        <component
          :is="selectAlarmKind.component"
          :key="selectAlarmKind.key"
          :idCardNo="archiveNo"
          :list="selectAlarmKind.list"
          v-bind="selectAlarmKind.props"
          :loading="selectLoading"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script>
import { deepCopy } from "@/util/modules/common";

export default {
  name: "PlaceLastestAlarm",
  props: {
    alarmKindList: {
      require: true,
      type: Array,
      default: () => [],
    },
    archiveNo: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "idCardNo",
    },
  },
  watch: {
    alarmKindList: {
      handler(val) {
        this.componentList = deepCopy(val).map((item) => ({
          ...item,
          isFirst: true,
          list: [],
        }));
        this.selectHandler(val[0].title);
      },
      immediate: true,
    },
  },
  data() {
    return {
      selectAlarmKind: {},
      selectLoading: false,
      componentList: [],
    };
  },
  methods: {
    async selectHandler(value) {
      this.selectAlarmKind = this.componentList.find(
        (item) => item.title == value
      );
      // // 避免重复调用接口 如果不做防抖接口没有调用结束就切到下一个会有问题 还是不管这个重复调用接口的问题了
      // if (!this.selectAlarmKind.isFirst) {
      //   return;
      // }
      this.selectLoading = true;
      let param = {
        pageSize: 3,
        pageNum: 1,
      };
      param[this.type] = this.$route.query.archiveNo;
      try {
        const { data = { entities: [] } } = await this.selectAlarmKind.request(
          param
        );
        this.selectAlarmKind.list = data?.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.selectLoading = false;
        // for (let i = 0; i < this.componentList.length; i++) {
        //   if (this.componentList[i].title == value) {
        //     this.componentList[i].isFirst = false;
        //     break;
        //   }
        // }
      }
    },
    // 报警更多
    handleAlarmMore() {
      this.$emit("handleAlarmMore", { ...this.selectAlarmKind });
    },
  },
};
</script>

<style lang="less" scoped>
.card-content {
  padding: 10px 0;
}
.capture-title {
  background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
  color: #fff;
  font-weight: bold !important;
  font-size: 16px;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  transform: skewX(-18deg);
  padding: 0 23px;
  left: -6px;
  position: relative;
  span {
    transform: skewX(18deg);
    display: inline-block;
  }
}
.capture-menu {
  overflow: hidden;
  height: 30px;
  width: 300px;

  /deep/ .ivu-menu-horizontal {
    display: flex;
    height: 30px;
    line-height: 30px;

    .ivu-menu-item {
      padding: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &::after {
      background: transparent;
    }
  }
}
.extra-right {
  flex: 1;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.35);
  text-align: right;
  padding-right: 20px;
  cursor: pointer;
  line-height: 20px;
}
</style>
