<template>
	<div class="search  card-border-color">
		<Form :inline="true" :class="visible ? 'advanced-search-show':''">
			<div class="general-search">
				<div class="input-content">
					<div class="input-content">
						<div class="upload-input-list">
							<uiUploadImg :algorithmType="2" v-model="queryParam.urlList" @imgUrlChange="imgUrlChange" size="small" />
						</div>
						<div class="other-search">
							<div class="other-search-top card-border-color">
								<FormItem label="相似度:" class="slider-form-item">
									<div class="slider-content">
										<Slider v-model="queryParam.similarity"></Slider>
										<span>{{ queryParam.similarity }}%</span>
									</div>
								</FormItem>
							</div>
							<div class="other-search-bottom">
								<div class="flex">
									<FormItem label="设备资源:">
										<div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{queryParam.selectDeviceList.length}}）</div>
									</FormItem>
									<FormItem label="抓拍时段:">
										<ui-tag-select ref="tagSelect1" :hideCheckAll='true' :value="captureTimePeriod[0].name" @input="e => {input(e, 'timeSlot')}">
											<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
												{{ item.name }}
											</ui-tag-select-option>
										</ui-tag-select>
										<DatePicker v-show="queryParam.timeSlot == '自定义'" v-model="queryParam.timeSlotArr" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" @on-change="dateChange" placeholder="请选择抓拍时间段" style="width: 330px"></DatePicker>
									</FormItem>
									<FormItem label="车牌号码:">
										<Input v-model="queryParam.plateNo" placeholder="请输入"></Input>
									</FormItem>
								</div>
								<div class="btn-group">
									<span class="advanced-search-text primary" @click="advancedSearchHandle($event)">
										更多条件 <i class="iconfont icon-jiantou"></i>
									</span>
									<Button type="primary" @click="searchHandle">查询</Button>
									<Button type="default" @click="resetHandle">重置</Button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--更多搜索条件-->
			<div class="advanced-search" @click="($event) => $event.stopPropagation()">
				<section class="search-container" v-show="visible">
					<div class="search-item-container" id="searchItemContainer">
						<div class="classify-content">
							<span class="classify-name">常用</span>
							<div class="items">
								<div class="advanced-search-item card-border-color">
									<!-- <FormItem label="设备资源:">
                    <div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{queryParam.selectDeviceList.length}}）</div>
                  </FormItem> -->
									<FormItem label="角度:" class="percent-60">
										<selectTag ref="pointOfView" :list="pointViewList" vModel="pointOfView" @selectItem="selectItem" />
									</FormItem>
								</div>
							</div>
						</div>
						<div class="classify-content">
							<span class="classify-name">车牌</span>
							<div class="items">
								<div class="advanced-search-item card-border-color" id="vehicleCP">
									<FormItem label="车牌类型:">
										<selectTag ref="plateClass" :moreBtn="true" :list="plateClassList" vModel="plateClass" @selectItem="selectItem" />
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color">
									<FormItem label="车牌颜色:">
										<ui-tag-select ref="plateColor" @input=" e => {input(e, 'plateColor')}">
											<ui-tag-select-option v-for="(item, $index) in licensePlateColorList" :key="$index" effect="dark" :name="item.dataKey">
												<div :style="{borderColor: licensePlateColorArray[item.dataKey].borderColor}" class="plain-tag">
													<div :style="licensePlateColorArray[item.dataKey].style" :class="licensePlateColorArray[item.dataKey].class">{{ item.dataValue }}</div>
												</div>
											</ui-tag-select-option>
										</ui-tag-select>
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color">
									<FormItem label="遮挡:">
										<selectTag ref="cover" :list="plateOcclusionList" vModel="cover" @selectItem="selectItem" />
									</FormItem>
								</div>
							</div>
						</div>
						<div class="classify-content">
							<span class="classify-name">车身</span>
							<div class="items">
								<div class="advanced-search-item card-border-color">
									<FormItem label="车身类型:">
										<selectTag :isChild="true" :carColor="true" :list="vehicleClassTypeList" ref="bodyType" vModel="bodyType" @selectItem="selectItem" />
										<div class="carVal"></div>
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color" id="vehicleCSYS">
									<FormItem label="车身颜色:">
										<ui-tag-select ref="bodyColor" @input=" e => {input(e, 'bodyColor') }">
											<ui-tag-select-option v-for="(item, $index) in plateColorIpbdList" :key="$index" effect="dark" :name="item.dataKey">
												<div v-if="vehicleBodyColorArray[item.dataKey]" :style="{borderColor: vehicleBodyColorArray[item.dataKey].borderColor}" class="plain-tag-color">
													<div :style="vehicleBodyColorArray[item.dataKey].style"></div>
												</div>
											</ui-tag-select-option>
										</ui-tag-select>
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color" id="vehiclePP">
									<FormItem label="车辆品牌:">
										<div class="select-tag-button" @click="selectBrandHandle">选择车辆品牌/已选（{{queryParam.plateBrands.length}}）</div>
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color">
									<FormItem label="特殊车辆:">
										<selectTag :list="specialVehicleList" ref="specialPlate" vModel="specialPlate" @selectItem="selectItem" />
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color" id="markerTop">
									<FormItem label="车顶物件:">
										<selectTag :list="roofItemsList" ref="carRoof" vModel="carRoof" @selectItem="selectItem" />
									</FormItem>
									<FormItem label="年检标:" class="percent-60">
										<selectTag :list="annualInspectionNumList" ref="inspectAnnually" vModel="inspectAnnually" @selectItem="selectItem" />
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color" id="marker">
									<FormItem label="标志物:">
										<selectTagMore :list="markerTypeList" ref="marker" vModel="markerList" @selectItemMore="selectItemMore" />
									</FormItem>
								</div>
							</div>
						</div>
						<div class="classify-content">
							<span class="classify-name">行为</span>
							<div class="items">
								<div class="advanced-search-item card-border-color">
									<FormItem label="副驾有人:">
										<selectTag :list="getCopilotList" ref="ifCoDriverPeople" vModel="ifCoDriverPeople" @selectItem="selectItem" />
									</FormItem>
									<FormItem label="面部遮挡:" class="percent-60">
										<selectTag :list="facialOcclusionList" ref="facialOcclusion" vModel="facialOcclusion" @selectItem="selectItem" />
									</FormItem>
								</div>
								<div class="advanced-search-item card-border-color" id="vehicleZYB">
									<FormItem label="遮阳板:">
										<selectTag :list="sunVisorStatusList" ref="sunVisorStatus" vModel="sunVisorStatus" @selectItem="selectItem" />
									</FormItem>
								</div>
							</div>
						</div>
					</div>
					<div class="img-search" @click="clickTagVehicleHandle">
						<div class="vehicle-front">
							<span v-for="(item, index) in anchorFrontList" :data-id="item.dataId" :class="item.class" :key="index">{{item.name}}</span>
							<img src="@/assets/img/wisdom-cloud-search/vehicle-back.png" alt="">
						</div>
						<div class="vehicle-back">
							<span v-for="(item, index) in anchorAfterList" :data-id="item.dataId" :class="item.class" :key="index">{{item.name}}</span>
							<img src="@/assets/img/wisdom-cloud-search/vehicle-front.png" alt="">
						</div>
					</div>
				</section>
			</div>
			<!-- 选择品牌 -->
			<BrandModal ref="brandModal" @on-change="selectBrand" />
			<!-- 选择设备 -->
			<select-device ref="selectDevice" :checkedLabels="checkedLabels" @selectData="selectData" />
		</Form>
	</div>
</template>
<script>
	import { mapActions, mapGetters } from 'vuex'
	import uiUploadImg from '@/components/ui-upload-new-img/index'
	import BrandModal from '@/components/ui-brand-modal.vue'
	import selectTagMore from '../../components/select-tag-more.vue'
	import selectTag from '../../components/select-tag.vue'
	import { licensePlateColorArray, vehicleBodyColorArray } from '@/libs/system'
	import {
		captureTimePeriod,
		angleList,
		shelterList,
		vehiclePlateTypes,
		vehiclePlateColors,
		vehicleBodyTypes,
		vehicleBodyColors,
		colors,
		bodyColors,
		carTopList,
		coDriverList,
		faceList,
		sunVisorList,
		yearCheckList,
		markerList,
		anchorFrontList,
		anchorAfterList,
		specialVehicles,
		vehicleClassTypeList
	} from './search-vehicle.js'

	export default {
		components: {
			BrandModal,
			uiUploadImg,
			selectTag,
			selectTagMore
		},
		data() {
			return {
				urlList: ['', '', '', '', ''],
				visible: false,
				curSearchItemId: '',
				algorithmType: 2,
				queryParam: {
					timeSlot: '近一天',
					"selectDeviceList": [],
					"plateBrands": [],
					"similarity": 66,
					"urlList": []
				},
				captureTimePeriod,
				angleList,
				shelterList,
				vehiclePlateTypes,
				vehiclePlateColors,
				vehicleBodyTypes,
				vehicleBodyColors,
				carTopList,
				yearCheckList,
				markerList,
				colors,
				coDriverList,
				faceList,
				sunVisorList,
				bodyColors,
				specialVehicles,
				licensePlateColorArray,  // 车牌颜色
				vehicleBodyColorArray,   // 车身颜色
				anchorFrontList,
				anchorAfterList,
				checkedLabels: [],        // 已选择的标签
				vehicleClassTypeList: vehicleClassTypeList, // 车身类型
			}
		},
		async created() {
			if (this.$route.query.archiveNo) {
				this.queryParam.plateNo = JSON.parse(this.$route.query.archiveNo)
			} else {
				this.queryParam.plateNo = '';
			}
			// await this.getDictData()
			if (typeof this.globalObj.searchForPicturesDefaultSimilarity === 'string') {
				this.queryParam.similarity = Number(this.globalObj.searchForPicturesDefaultSimilarity);
			} else {
				this.queryParam.similarity = this.globalObj.searchForPicturesDefaultSimilarity;
			}
			window.addEventListener("click", (e) => {
				this.visible = false;
			})
		},
		computed: {
			...mapGetters({
				globalObj: 'systemParam/globalObj',
				pointViewList: 'dictionary/getPointViewList',               // 角度(枚举精准检索)
				plateClassList: 'dictionary/getPlateClassList',             // 车牌类型(枚举精准检索)
				plateOcclusionList: 'dictionary/getPlateOcclusionList',     // 遮挡(枚举精准检索) 
				vehicleColorList: 'dictionary/getVehicleColorList',         // 车牌颜色
				licensePlateColorList: 'dictionary/getLicensePlateColorList',         // 车牌颜色
				plateColorIpbdList: 'dictionary/getVehicleColorList',     // 车身颜色
				roofItemsList: 'dictionary/getRoofItemsList',               // 车顶物件
				markerTypeList: 'dictionary/getMarkerTypeList',             // 标志物
				// vehicleClassTypeList: 'dictionary/getVehicleClassTypeList', // 车辆类型、车身类型(枚举精准检索) 
				sunVisorStatusList: 'dictionary/getSunVisorStatusList',     // 遮阳板
				specialVehicleList: 'dictionary/getSpecialVehicleList',     // 特殊车辆
				driverFlagList: 'dictionary/getDriverFlagList',             // 副驾有人
				getCopilotList: 'dictionary/getCopilotList',                  //副驾驶
				facialOcclusionList: 'dictionary/getFacialOcclusionList',             // 面部遮挡
				annualInspectionNumList: 'dictionary/getAnnualInspectionNumList',     // 年检标
			})
		},
		methods: {
			// ...mapActions({
			//   getDictData: 'dictionary/getDictAllData',
			// }),
			advancedSearchHandle($event) {
				$event.stopPropagation()
				if (this.visible) {
					this.visible = false
				} else {
					this.visible = true
				}
			},
			checkedHandle(arr) {
				console.log(arr)
			},
			// 选择品牌
			selectBrandHandle() {
				this.$refs.brandModal.show()
			},
			/**
			 * 选择设备
			 */
			selectDevice() {
				this.$refs.selectDevice.show(this.queryParam.selectDeviceList)
			},
			/**
			 * 已选择设备数据返回
			 */
			selectData(list) {
				this.queryParam.selectDeviceList = list
			},
			// 搜索函数
			searchHandle() {
				this.visible = false
				this.$parent.pageInfo.pageNumber = 1
				this.$emit('search', this.queryParam)
			},
			// 点击右边车指示高亮左侧搜索项  因这里数据无特别规律，so这里为原生js操作dom
			clickTagVehicleHandle(e) {
				const target = e.target
				if (target.nodeName === 'SPAN') {
					let PChildren = target.parentNode.parentNode.childNodes
					PChildren.forEach(pChild => {
						pChild.childNodes.forEach(child => {
							child.classList.remove('cur')
						})
					})
					target.classList.add('cur')
					const dataId = target.getAttribute('data-id')
					const leftItem = document.getElementById(dataId)
					if (!leftItem) return false
					const leftPChildren = leftItem.parentNode.parentNode.parentNode.childNodes
					leftPChildren.forEach(p => {
						p.childNodes.forEach(pp => {
							if (pp.nodeName === 'DIV') {
								pp.childNodes.forEach(ppp => {
									ppp.classList.remove('cur')
								})
							}
						})
					})
					leftItem.classList.add('cur')
					document.getElementById('searchItemContainer').scrollTop = leftItem.offsetTop
					this.curSearchItemId = dataId
				}
			},
			dateChange(start, end) {
				if (start[1].slice(-8) === '00:00:00') {
					start[1] = start[1].slice(0, -8) + '23:59:59'
				}
				this.queryParam.timeSlotArr = [start[0], start[1]];
			},
			/**
			 * 选中tag赋值
			 */
			selectItem(key, item) {

				// 具体业务处理逻辑
				if (item) {
					this.queryParam[key] = item.dataKey
				} else {
					// 全部选项，不返回数据到后端
					this.queryParam[key] = null
				}
			},
			/**
			* 多选tag赋值
			*/
			selectItemMore(key, item) {

				// 具体业务处理逻辑
				if (item) {
					this.queryParam[key] = item
				} else {
					// 全部选项，不返回数据到后端
					this.queryParam[key] = null
				}
			},
			/**
			 * 车辆品牌已选择，返回数据
			 */
			selectBrand(list) {
				this.queryParam.plateBrands = list
			},
			/**
			 * 选择接口返回数据
			 */
			input(e, key) {
				this.queryParam[key] = e
			},
			resetHandle() {
				this.queryParam = {
					plateNo: '',
					timeSlot: '近一天',
					selectDeviceList: [],       // 设备资源
					plateBrands: [],   // 车辆品牌
					plateClass: '',
					plateColor: '',
					bodyType: '',
					bodyColor: '',
					specialPlate: '',
					carRoof: '',
					inspectAnnually: '',
					marker: '',
					similarity: this.globalObj.searchForPicturesDefaultSimilarity - 0,
					ifCoDriverPeople: '',
					facialOcclusion: '',
					sunVisorStatus: '',
					urlList: []
				}

				// 清空组件选中状态
				this.$refs.tagSelect1.clearChecked(false)
				this.$refs.pointOfView.clearChecked()
				this.$refs.plateClass.clearChecked()
				this.$refs.plateColor.clearChecked()
				this.$refs.cover.clearChecked()
				this.$refs.bodyType.clearChecked()
				this.$refs.marker.clearChecked()
				this.$refs.bodyColor.clearChecked()
				this.$refs.specialPlate.clearChecked()
				this.$refs.carRoof.clearChecked()
				this.$refs.inspectAnnually.clearChecked()
				this.$refs.ifCoDriverPeople.clearChecked()
				this.$refs.facialOcclusion.clearChecked()
				this.$refs.sunVisorStatus.clearChecked()
				this.$refs.brandModal.clearCheckAll()
				this.visible = false
				this.$emit("reset")
			},
			/**
			 * 图片上传结果返回
			 */
			imgUrlChange(list) {
				this.queryParam.urlList = list
			}
		}
	}
</script>
<style lang="less" scoped>
	@import "./search-vehicle";
</style>
