// color
@primary-color: #2C86F8;                  // 主题
@info-color: #888888;                     // 信息
@success-color: #1FAF81;                  // 成功
@warning-color: #F29F4C;                  // 警告
@error-color: #EA4A36;                    // 错误
@normal-color: #888888;                   // 默认
@disabled-color:  #E9E9E9;                // 禁用
@white: #fff;                             // 白色
@black: #000;                             // 黑色
@blue-color: #48BAFF;
// link-color
@link-normal-color: @primary-color;         // link-normal
@link-hover-color: #4597FF;               // link-hover
@link-active-color: #1A74E7;              // link-active
// font-color
@title-font-color: fade(@black, 90%);       // 标题/表头 90%
@text-font-color: fade(@black, 80%);        // 正文 80%
@input-font-color: fade(@black, 75%);       // 输入框标题 75%
@secondary-font-color: fade(@black, 60%);   // 次要 60%
@label-font-color: fade(@black, 45%);       // 表单label 45%
@auxiliary-font-color: fade(@black, 35%);   // 辅助 35%
// border-color
@border-color: #D3D7DE;                   // 边框颜色
// linear-gradient-color
@linear-color: linear-gradient(180deg, #5BA3FF 0%, #2C86F8 100%);          //渐变
@linear-hover-color: linear-gradient(180deg, #76B2FF 0%, #3A91FF 100%);    //渐变-hover
@linear-active-color: linear-gradient(180deg, #4794F6 0%, #1A70DD 100%);   //渐变-active
// warning-line-gradient-color
@warning-linear-color: linear-gradient(180deg, #F7B93D 0%, #EC9240 100%);
@warning-linear-hover-color: linear-gradient(180deg, #FFD173 0%, #FCA454 100%);
@warning-linear-active-color: linear-gradient(180deg, #F6B533 0%, #E6862D 100%);
// font-size
@font-size12: 12px;                         // 辅助
@font-size14: 14px;                         // 正文
@font-size16: 16px;                         // 小标题
@font-size18: 18px;                         // 副标题
@font-size20: 20px;                         // 主标题
// padding
@padding8: 8px;
@padding12: 12px;
@padding16: 16px;
@padding24: 24px;
// margin
@margin8: 8px;
@margin12: 12px;
@margin16: 16px;
@margin24: 24px;
// iview
@iview-normal-color: #D3D7DE;
@iview-hover-color: @link-hover-color;
@iview-active-color: @link-normal-color;
@iview-disabled-color: @disabled-color;
@iview-disabled-text-color: @auxiliary-font-color;
// input
@input-placeholder-normal-color: @auxiliary-font-color;   //输入框占位文本默认
@input-placeholder-active-color: @title-font-color;       //输入框占位文本active
@input-error-background: #FFF1F0;                       //输入框报错背景
@input-error-border-color: @error-color;                  //输入框报错边框
// select
@select-drop-down-icon-color: #888;                     //下拉框图标
@select-drop-down-background: @white;                     //下拉框背景色
@select-drop-down-item-normal-color: fade(@primary-color, 20%); //下拉框选项hover
@select-drop-down-item-active-color: @primary-color;      //下拉框选项active
@select-drop-down-item-text-normal-color: @text-font-color; //下拉框选项默认字体颜色
@select-drop-down-item-text-active-color: @white;         //下拉框选项active字体颜色
// button
@button-primary-text-color: @white;                       //primary类型按钮字体颜色
@button-text-color: @title-font-color;                    //文字类型按钮字体颜色
@button-text-hover-bg-color: fade(@iview-normal-color, 29%); //文字类型按钮hover背景颜色
@button-text-active-bg-color: @iview-normal-color;        //文字类型按钮active背景颜色
// table
@table-th-bg-color: #EBEDF1;                            //表头背景
@table-th-box-shadow-color: #D3D7DE;                    //表头内投影
@table-tr-odd-bg-color: #F9F9F9;                        //表格奇数背景色
@table-tr-even-bg-color: #F1F1F1;                       //表格偶数背景色
@table-tr-hover-bg-color: fade(@primary-color, 10%);      //表格hover背景色
// page
@page-pre-next-text-color: #888;                        //page上一页下一页按钮颜色
@page-item-text-color: #999;
@page-text-color: #999;
// layout
@ivu-layout-color: #DDE1EA;                             //整体布局背景色
@ivu-head-border-color: #AED2FF;                        //head底部边框颜色
@layout-container-color: @white;                          //整体布局container背景色
@layout-container-shadow-color: fade(#93ABCE, 70%);     //整体布局container投影色
// 一级菜单
@nav-text-color: fade(@white, 80%);                       //导航默认字体颜色
@nav-text-active-color: @white;                           //导航hover/active字体颜色
@nav-text-active-bg-color: fade(@white, 10%);             //导航hover/active字体背景色
@head-user-line-color: fade(@white, 20%);                 //head用户分割线
// 二级菜单
@second-menu-bg-color: #484847;                         //二级菜单背景色
@second-menu-item-border-color: fade(@white, 10%);        //二级菜单每项底部边框颜色
@second-menu-item-hover-color: fade(@white, 10%);         //二级菜单每项hover背景色
@second-menu-item-active-color: fade(@white, 20%);        //二级菜单每项active背景色
@second-menu-text-color: fade(@white, 60%);               //二级菜单默认字体颜色
@second-menu-text-hover-color: @white;                    //二级菜单hover字体颜色
@second-menu-text-active-color: @white;                   //二级菜单选中字体颜色
// 三级菜单
@three-menu-item-hover-color: fade(@primary-color, 20%);  //三级菜单每项hover背景色
@three-menu-item-active-color: @primary-color;            //三级菜单每项active背景色
// 导航tabs
@nav-tabs-bg-color: @white;                               //导航tabs背景色
@nav-tabs-item-active-bg-color: fade(@primary-color, 10%);//导航tabs每项选中的背景色
@nav-tabs-item-text-color: @secondary-font-color;         //导航tabs每项字体颜色
@nav-tabs-item-close-color: #888;                       //导航tabs每项关闭按钮颜色
@import "../../common.less";
@import "../../iview/index.less";
@import "../../layout/index.less";
