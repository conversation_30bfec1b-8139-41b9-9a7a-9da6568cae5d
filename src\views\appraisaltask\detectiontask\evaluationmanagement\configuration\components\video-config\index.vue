<template>
  <components :is="componentName" v-bind="$props" v-on="$listeners" ref="components"></components>
</template>

<script>
export default {
  name: 'index',
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    indexType: {
      type: String,
      default: '',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    componentName() {
      switch (this.moduleAction.indexType) {
        // 视频监控数量增长率
        case 'VIDEO_QUANTITY_INCREASE_RATE':
        case 'VIDEO_NETWORKING_PROMOTION_RATE': // 联网数量提升率
          return 'VideoMonitorGrowthRate';
        case 'VIDEO_DEVICE_REVOCATION':
          return 'VideoDeviceRevocation';
        case 'VIDEO_ACCURACY':
          return 'BasicAccuracy';
        case 'VIDEO_READ_PROMOTION_RATE': //可调阅提升率
          return 'VideoReadPromotionRate';
        case 'VIDEO_OSD_ACCURACY_PROMOTION_RATE': //字幕标注合规率提升
          return 'videoOsdAccuracyPromotionRate';
        case 'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE': //时钟准确率提升率
          return 'videoClockAccuracyPromotionRate';
        case 'VIDEO_OFFLINE_STAT':
          return 'FaceOfflineStat';
        case 'VIDEO_OSD_ACCURACY':
        case 'VIDEO_GENERAL_OSD_ACCURACY':
          return 'VideoOsdAccuracy';
        case 'VIDEO_OSD_CLOCK_ACCURACY':
        case 'VIDEO_GENERAL_OSD_CLOCK_ACCURACY':
          return 'VideoOsdClockAccuracy';
        case 'VIDEO_CODE_STANDARD_RATE':
          return 'VideoCodeStandardRate';
        case 'VIDEO_HISTORY_COMPLETE_ACCURACY':
        case 'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN':
          return 'VideoHistoryCompleteAccuracy';
        default:
          return 'Original';
      }
    },
  },
  components: {
    Original: require('./components/original').default,
    VideoDeviceRevocation: require('./components/video-device-revocation.vue').default,
    VideoHistoryCompleteAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-history-complete-accuracy.vue')
        .default,
    BasicAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/basis-view-config/components/basic-accuracy.vue')
        .default,
    VideoMonitorGrowthRate: require('./components/video-monitor-growth-rate').default,
    VideoReadPromotionRate:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-read-promotion-rate.vue')
        .default,
    videoOsdAccuracyPromotionRate:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-osd-accuracy-promotion-rate.vue')
        .default,
    videoClockAccuracyPromotionRate:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-clock-accuracy-promotion-rate.vue')
        .default,
    FaceOfflineStat:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-offline-stat.vue')
        .default,
    VideoOsdAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-osd-accuracy.vue')
        .default,
    VideoOsdClockAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-osd-clock-accuracy.vue')
        .default,
    VideoCodeStandardRate:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config/components/video-code-standard-rate.vue')
        .default,
  },
};
</script>

<style lang="less" scoped></style>
