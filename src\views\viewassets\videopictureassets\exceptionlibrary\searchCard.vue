<template>
  <div class="base-search mb-sm">
    <ui-label class="inline mb-sm" label="指标名称">
      <Select
        placeholder="请选择指标名称"
        v-model="searchData.indexId"
        class="width-lg"
        filterable
        @on-change="getCheckStatus"
      >
        <Option v-for="(item, index) in indexList" :key="index" :value="item.value">{{ item.name }}</Option>
      </Select>
    </ui-label>
    <ui-label class="inline ml-lg" label="抓拍时间">
      <div class="date-picker-box">
        <DatePicker
          class="input-width"
          v-model="searchData.customParameters.minLogTime"
          type="datetime"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
          @on-change="
            (formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData.customParameters, 'minLogTime')
          "
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="input-width"
          v-model="searchData.customParameters.maxLogTime"
          type="datetime"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
          @on-change="
            (formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData.customParameters, 'maxLogTime')
          "
        ></DatePicker>
      </div>
    </ui-label>
    <ui-label class="inline ml-lg" label="抓拍设备">
      <select-camera
        class="inline vt-middle base-text-color"
        @pushCamera="pushCamera"
        :device-ids="searchData.customParameters.deviceIds"
      ></select-camera>
    </ui-label>
    <!--    <ui-label class="inline ml-lg" label="检测结果" :width="70">
      <Select
        placeholder="请选择检测结果"
        v-model="searchData.customParameters.qualified"
        class="width-lg"
        clearable
        @on-change="searchData.customParameters.causeError = []"
      >
        <Option v-for="(item, index) in checkStatusData" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
      </Select>
    </ui-label>
    <ui-label
      class="inline"
      label="异常原因"
      :width="70"
      v-if="[2001, 2002].includes(searchData.indexId) && ['2'].includes(searchData.customParameters.qualified)"
    >
      <Select
        placeholder="请选择异常原因"
        v-model="searchData.customParameters.causeError"
        class="width-lg"
        clearable
        :disabled="!searchData.customParameters.qualified === '2' && [2001, 2002].includes(indexId)"
      >
        <Option v-for="(item, index) in checkData" :key="index" :value="item.key">{{
            item.value
          }}</Option>
      </Select>
    </ui-label>-->
    <ui-label :width="1" class="inline ml-lg" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    indexId: {},
    optionList: {},
    checkStatus: {
      type: Array,
      default: () => [],
    },
    checkList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      indexList: [],
      searchData: {
        customParameters: {
          deviceIds: [],
          minLogTime: null,
          maxLogTime: null,
          qualified: '',
          causeError: null,
        },
        indexId: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.maxLogTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.minLogTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      checkData: [],
      checkStatusData: [],
      checkStatus: [
        { dataValue: '图片不可用', dataKey: '2' },
        { dataValue: '无法检测', dataKey: '3' },
      ],
      /*      checkStatus30013002: [
        {dataKey: '1', dataValue: '属性完整'},
        {dataKey: '4__plate_no', dataValue: '车牌号缺失'},
        {dataKey: '4__plate_color', dataValue: '车牌颜色缺失'},
        {dataKey: '2__plate_no', dataValue: '车牌号格式错误'},
        {dataKey: '2__plate_color', dataValue: '车牌颜色格式错误'},
        {dataKey: '3', dataValue: '无法检测'},
      ],*/
    };
  },
  created() {},
  mounted() {},
  methods: {
    getCheckStatus(val) {
      console.log('getCheckStatus', val);
      switch (val) {
        case 3001:
          this.checkStatusData = [
            { dataKey: '1', dataValue: '属性完整' },
            { dataKey: '4__plate_no', dataValue: '车牌号缺失' },
            { dataKey: '4__plate_color', dataValue: '车牌颜色缺失' },
            { dataKey: '2__plate_no', dataValue: '车牌号格式错误' },
            { dataKey: '2__plate_color', dataValue: '车牌颜色格式错误' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          break;
        case 3002:
          this.checkStatusData = [
            { dataKey: '1', dataValue: '属性完整' },
            { dataKey: '4__plate_no', dataValue: '车牌号缺失' },
            { dataKey: '4__plate_color', dataValue: '车牌颜色缺失' },
            { dataKey: '4__vehicle_class', dataValue: '车辆类型缺失' },
            { dataKey: '4__vehicle_brand', dataValue: '车辆品牌缺失' },
            { dataKey: '4__vehicle_color', dataValue: '车身颜色缺失' },
            { dataKey: '4__vehicle_model', dataValue: '车辆型号缺失' },
            { dataKey: '2__plate_no', dataValue: '车牌号格式错误' },
            { dataKey: '2__plate_color', dataValue: '车牌颜色格式错误' },
            { dataKey: '2__vehicle_class', dataValue: '车辆类型格式错误' },
            { dataKey: '2__vehicle_brand', dataValue: '车辆品牌格式错误' },
            { dataKey: '2__vehicle_color', dataValue: '车身颜色格式错误' },
            { dataKey: '2__vehicle_model', dataValue: '车辆型号格式错误' },
            { dataKey: '3', dataValue: '无法检测' },
          ];
          break;
        default:
          this.checkStatusData = this.checkStatus;
      }
    },
    pushCamera(list) {
      this.searchData.customParameters.deviceIds = list.map((row) => row.deviceId);
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetSearchDataMx() {
      this.searchData.customParameters = {
        deviceIds: [],
        minLogTime: null,
        maxLogTime: null,
        qualified: '',
        causeError: null,
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  watch: {
    checkList: {
      handler(val) {
        if (val) {
          this.checkData = val;
        }
      },
      deep: true,
      immediate: true,
    },
    optionList: {
      handler(val) {
        if (val) {
          this.indexList = val;
          if (val.length > 0) {
            this.searchData.indexId = val[0]['value'];
            this.getCheckStatus(this.searchData.indexId);
            this.$emit('startSearch', this.searchData);
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
}
</style>
