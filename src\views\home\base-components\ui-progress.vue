<template>
  <div :class="classType">
    <slot></slot>
    <Progress
      v-bind="$attrs"
      v-if="type === 'default'"
      hide-info
      status="success"
      :stroke-color="homeStyle.success.strokeColor"
      @click.native.stop="$emit('click')"
    />
    <Progress
      v-bind="$attrs"
      v-else
      status="normal"
      hide-info
      :stroke-color="homeStyle.normal.strokeColor"
      @click.native.stop="$emit('click')"
    />
    <span
      class="f-14 ml-sm"
      :style="{ color: type === 'default' ? homeStyle.normal.fontColor : homeStyle.success.fontColor }"
      >{{ `${$attrs.percent}%` }}</span
    >
  </div>
</template>

<script>
import uiProgressStyle from '@/views/home/<USER>/module/ui-progress';

export default {
  name: 'ui-progress',
  components: {},
  props: {
    type: {
      default: 'default',
    },
    styleType: {},
  },
  data() {
    return {};
  },
  computed: {
    classType() {
      return this.styleType ? `progress-container-${this.styleType}` : 'progress-container';
    },
    homeStyle() {
      return uiProgressStyle[`style${this.styleType}`] || uiProgressStyle.style1;
    },
  },
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.progress-container {
  display: flex;
  height: 30px;
  line-height: 30px;
  .progress(@background: url('~@/assets/img/base-home/platform-stability/red-dot.png') no-repeat) {
    .ivu-progress-inner {
      background-color: transparent;
      .ivu-progress-bg {
        background-color: transparent;
        height: 4px !important;
        &:after {
          background: @background;
          background-size: cover;
          position: absolute;
          top: -9px;
          right: -9px;
          height: 22px;
          width: 22px;
        }
      }
    }
  }
  @{_deep} .ivu-progress.ivu-progress-normal {
    .progress;
  }
  @{_deep} .ivu-progress.ivu-progress-success {
    .progress(url('~@/assets/img/base-home/platform-stability/blue-dot.png') no-repeat);
  }
}
.progress-container-2 {
  display: flex;
  height: 30px;
  line-height: 30px;
  .progress(@background: url('~@/assets/img/base-home/platform-stability/blue-dot.png') no-repeat) {
    .ivu-progress-inner {
      background-color: rgba(95, 173, 254, 0.2);
      .ivu-progress-bg {
        background-color: transparent;
        height: 4px !important;
        &:after {
          background: @background;
          background-size: cover;
          position: absolute;
          top: -9px;
          right: -9px;
          height: 22px;
          width: 22px;
        }
      }
    }
  }
  @{_deep} .ivu-progress.ivu-progress-normal {
    .progress;
  }
  @{_deep} .ivu-progress.ivu-progress-success {
    .progress(url('~@/assets/img/base-home/platform-stability/blue-dot.png') no-repeat);
  }
}
</style>
