<template></template>

<script>
	import { mapGetters } from "vuex";
	export default {
		data() {
			return {
				// path:"ws://*************:8888/icbd-business-compare-notice/websocket/alarmInfo?userId=10002",
				path: "ws://" + location.hostname + ":8888/icbd-business-compare-notice/websocket/alarmInfo?userId=",
				// path: "ws://************:8888/icbd-business-compare-notice/websocket/alarmInfo?userId=",
				socket: "",
				connect: true
			}
		},
		computed: {
			...mapGetters({ userInfo: "userInfo" }),
		},
		watch: {
			userInfo: {
				handler(val) {
					if (this.connect && val.id) {
						// 初始化
						this.init()
						this.connect = false
					}
				},
				deep: true,
				immediate: true
			}
		},
		mounted() {
			// 初始化
			// this.init()
			// console.log('---ip', location.hostname)
		},
		methods: {
			init: function () {
				if (typeof (WebSocket) === "undefined") {
					alert("您的浏览器不支持socket")
				} else {
					// 实例化socket
					// this.socket = new WebSocket(this.path)
					this.socket = new WebSocket(this.path + this.userInfo.id + '&Authorization=' + this.$store.state.user.token)
					// 监听socket连接
					this.socket.onopen = this.open
					// 监听socket错误信息
					this.socket.onerror = this.error
					// 监听socket消息
					this.socket.onmessage = this.getMessage
				}
			},
			open: function () {
				console.log("socket连接成功")
			},
			error: function () {
				console.log("连接错误")
			},
			getMessage: function (msg) {
				this.$emit('sendInfo', msg.data)
				// console.log(msg.data)
			},
			send: function () {
				this.socket.send(params)
			},
			close: function () {
				console.log("socket已经关闭")
			}
		},
		destroyed() {
			// 销毁监听
			this.socket.onclose = this.close
		}
	}
</script>

<style>
</style>