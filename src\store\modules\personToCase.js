export default {
  namespaced: true,
  state: {
    peopleTrackList: [],
    showArea: true,
    showPolice: true,
    policeAndCaseTrackList: [], // 警情和案件轨迹
    analysisAreaList: [], // 碰撞结果区高亮
    analysisResultList: [], // 碰撞结果
  },
  getters: {},
  mutations: {
    setState(state, source) {
      Object.keys(source).forEach((key) => {
        state[key] = source[key];
      });
    },
  },
  actions: {
    async getPeopleTrackList({ commit }, source) {
      commit("setState", { peopleTrackList: source });
    },
  },
};
