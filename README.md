# new-vid

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Run your tests
```
npm run test
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).

### 设计稿为1920*1080 px转换rem为 px/192 = *rem
在postcss.config.js中进行设置比例

### 蓝黑地图层级需要为 7-16级别 如果更换级别 需要更改特殊人群管控地图显示界限

### 深度选择器 @{_deep}即可使用 theme.less中已经引入@_deep: ~">>>";