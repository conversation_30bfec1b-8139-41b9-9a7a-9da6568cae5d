# 启数云感知大数据平台
## 此架构按照规范文档进行搭建包括目录结构，文件命名规范等[规范文档地址](https://lexiangla.com/docs/3be2a214d8bf11eb8a67a2dbdde0dbee?company_from=fa09b158efe311ea928f52540005f435)
## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```
### 设计稿为1920*1080 px转换rem为 px/192 = *rem
在postcss.config.js中进行设置 比例
### 基础方法可使用 Lodash 工具库 [文档地址](https://www.lodashjs.com/)

### 由于moment.js库体积较大，已更换为dayjs [文档地址](https://dayjs.fenxianglu.cn/category/)

### 内置全屏依赖库（screenfull.js）[官方文档](https://www.npmjs.com/package/screenfull)

### axios 不同于ivdg 已进行重新封装
在plugins->request->index.js可见详情

### 已安装echart echart-gl 插件 可直接进行引用

