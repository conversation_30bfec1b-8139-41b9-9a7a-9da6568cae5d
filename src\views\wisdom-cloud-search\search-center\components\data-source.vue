<template>
  <!-- 
    数据来源
   -->
  <div class="card">
    <ul class="select-item-content">
      <li :class="{ select: currentIndex == 1 }" @click="toSearchCenter">
        监控设备
      </li>
      <li :class="{ select: currentIndex == 2 }" @click="toViewParsingLibrary">
        自定义解析任务
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: {
    currentIndex: {
      type: Number,
      default: 1,
    },
    sectionName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {
    sectionPageName() {
      return this.sectionName || this.$route.query.sectionName || "faceContent";
    },
  },
  methods: {
    toSearchCenter() {
      if (this.currentIndex == 1) return;
      const noMenu = this.$route.query.noMenu;
      this.$router.push({
        path: "/wisdom-cloud-search/search-center",
        query: {
          sectionName: this.sectionPageName,
          noMenu,
        },
      });
    },
    toViewParsingLibrary() {
      if (this.currentIndex == 2) return;
      const noMenu = this.$route.query.noMenu;
      this.$router.push({
        path: "/viewanalysis/viewParsingLibrary",
        query: {
          sectionName: this.sectionPageName,
          noMenu,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.card {
  max-height: 60px;
  overflow: hidden;
}
.card:after {
  clear: both;
  content: "";
}
.select-item-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.select {
  color: #fff;
  background: #2c86f8;
  padding: 0 4px;
  border-radius: 2px;
  line-height: 24px;
}
ul {
  margin: 0;
  padding: 0;
  li {
    position: relative;
    float: left;
    //   margin-right: 12px;
    padding: 0 6px;
  }
  li:hover {
    cursor: pointer;
  }
}

ul::after {
  content: "";
  display: block;
  visibility: hidden;
  clear: both;
}
</style>
