import request from '@/libs/request'
import { datacenterService } from './Microservice'

// 大屏-数据专题大屏
// ---------------------------------------------数据态势start--------------------------------------------
// 获取在线人数
export function getOnlineNum() {
    return request({
        url: datacenterService + '/datacenter/dataThemedLargeScreen/getOnlineNum',
        method: 'GET',
    })
}
// 数据态势-小时数据
export function accessHourDataStat(data) {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/dataProject/accessHourDataStat?dataType=${data.dataType}&date=${data.date}`,
        method: 'GET',
    })
}
// 数据态势-月度数据 
export function accessMonthDataStat(data) {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/dataProject/accessMonthDataStat?dataType=${data.dataType}&year=${data.year}`,
        method: 'GET',
    })
}
// 数据态势-接入数据统计 
export function accessTotalDataStat() {
    return request({
        url: datacenterService + '/datacenter/dataThemedLargeScreen/dataProject/accessTotalDataStat',
        method: 'GET',
    })
}
// 数据态势-有效率 
export function efficiency() {
    return request({
        url: datacenterService + '/datacenter/dataThemedLargeScreen/dataProject/efficiency',
        method: 'GET',
    })
}
// 行政区划统计 
export function regionStat(dataType) {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/regionStat/${dataType}`,
        method: 'GET',
    })
}
// 布控态势-布控任务统计
export function controlTaskStat(dataType) {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/controlProject/controlTaskStat`,
        method: 'GET',
    })
}
// 布控态势-布控库统计 
export function controlLibraryStat(dataType) {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/controlProject/controlLibraryStat`,
        method: 'GET',
    })
}
// 布控态势-布控战果统计 
export function controlResultStat() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/controlProject/controlResultStat`,
        method: 'GET',
    })
}
// 布控态势-布控应用频率排名（top10）
export function controlAppFrequency() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/controlProject/controlAppFrequency`,
        method: 'GET',
    })
}
// 布控态势-警情响应及时度统计
export function alarmResponseTimeStat() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/controlProject/alarmResponseTimeStat`,
        method: 'GET',
    })
}
// 运营态势-活跃用户排名 
export function activeUserStat() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/operationProject/activeUserStat`,
        method: 'GET',
    })
}
// 运营态势-高频操作设备排名 
export function highFrequencyDevice() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/operationProject/highFrequencyDevice`,
        method: 'GET',
    })
}
// 运营态势-高频操作功能排名 
export function highFrequencyOperation() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/operationProject/highFrequencyOperation`,
        method: 'GET',
    })
}
// 运营态势-获取平台信息 
export function platformInfo() {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/operationProject/platformInfo`,
        method: 'GET',
    })
}
// 运营态势-平台战果统计 
export function platformResultStat(data) {
    return request({
        url: datacenterService + `/datacenter/dataThemedLargeScreen/operationProject/platformResultStat?dataType=${data.dataType}&year=${data.year}`,
        method: 'GET',
    })
}
// ---------------------------------------------数据态势end--------------------------------------------
// 大屏-设备专题大屏
// -------------------------------------start----------------------------------------
// 数据价值转化率 
export function dataValueRate() {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/dataValueRate`,
        method: 'GET',
    })
}
// 设备数量统计 
export function deviceCountStat() {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/deviceCountStat`,
        method: 'GET',
    })
}
// 设备故障详情 
export function deviceFaultDetails() {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/deviceFaultDetail`,
        method: 'GET',
    })
}
// 设备在线率及时长统计 
export function deviceOnlineRateStat(statType) {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/deviceOnlineRateStat/${statType}`,
        method: 'GET',
    })
}
// 设备维护单位排名 
export function deviceUnitRanking() {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/deviceUnitRanking`,
        method: 'GET',
    })
}
// 感知数据统计 
export function sensoryDataStat() {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/sensoryDataStat`,
        method: 'GET',
    })
} 
// 行政区划统计
export function deviceRegionStat() {
    return request({
        url: datacenterService + `/datacenter/deviceThemedLargeScreen/regionStat`,
        method: 'GET',
    })
}

// -------------------------------------end----------------------------------------

// 大屏-解析专题大屏
// -------------------------------------------start-------------------------------------------
// 实时解析结果展示 
export function parseRealTimeResult() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseRealTimeResult`,
        method: 'GET',
    })
}
// 解析任务排名 
export function parseTaskRanking() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseTaskRanking`,
        method: 'GET',
    })
}
// 解析任务状态统计 
export function taskType(taskType) { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseTaskStatusStat/${taskType}`,
        method: 'GET',
    })
}
// 解析任务趋势统计 
export function parseTaskTrendStat() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseTaskTrendStat`,
        method: 'GET',
    })
}
// 解析任务类型统计 
export function parseTaskTypeStat() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseTaskTypeStat`,
        method: 'GET',
    })
}
// 价值数据转化率
export function parseValueDataConversionRate() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseValueDataConversionRate`,
        method: 'GET',
    })
}
// 价值数据有效率 
export function parseValueDataEfficiency() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/parseValueDataEfficiency`,
        method: 'GET',
    })
}
// 行政区划统计  
export function anRegionStat() { 
    return request({
        url: datacenterService + `/datacenter/analysisThemedLargeScreen/regionStat`,
        method: 'GET',
    })
}
// --------------------------------------------end--------------------------------------------

// 大屏-建设态势专题大屏
// -------------------start---------------------
// 全域覆盖态势-各区县摄像机覆盖率排名 
export function countyCameraRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/globalCoverage/countyCameraRank`,
        method: 'GET',
    })
}
// 全域覆盖态势-各区县建设详情排名 
export function countyConstructionRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/globalCoverage/countyConstructionRank`,
        method: 'GET',
    })
}
// 全域覆盖态势-各区县高清摄像机占比排名  
export function countyHDVideoRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/globalCoverage/countyHDVideoRank`,
        method: 'GET',
    })
}
// 全域覆盖态势-各区县智能抓拍机占比排名 
export function countySmartVideoRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/globalCoverage/countySmartVideoRank`,
        method: 'GET',
    })
}
// 全域覆盖态势-一期建设统计 
export function onePhaseConstruction() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/globalCoverage/onePhaseConstruction`,
        method: 'GET',
    })
}
// 全域覆盖态势-区县统计
export function glRegionStat() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/globalCoverage/regionStat`,
        method: 'GET',
    })
}
//  IT资源态势-机房/交换机/机房服务器信息 
export function engineRoomInfo() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/itResource/engineRoomInfo`,
        method: 'GET',
    })
}
// IT资源态势-区域统计  
export function itRegionStat() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/itResource/regionStat`,
        method: 'GET',
    })
}
// IT资源态势-视频网链路信息 
export function videoLinkInfo() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/itResource/videoLinkInfo`,
        method: 'GET',
    })
}
// 社会资源态势-联网率排名 
export function onInternetRateRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/socialResource/onInternetRateRank`,
        method: 'GET',
    })
}
// 社会资源态势-联网信息统计  
export function onlineInfoStat() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/socialResource/onlineInfoStat`,
        method: 'GET',
    })
}
// 社会资源态势-在线率排名 
export function onlineRateRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/socialResource/onlineRateRank`,
        method: 'GET',
    })
}
// 社会资源态势-区划统计 
export function soRegionStat() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/socialResource/regionStat`,
        method: 'GET',
    })
}
// 社会资源态势-视频资源调阅排名 
export function videoAccessRank() { 
    return request({
        url: datacenterService + `/datacenter/constructionThemedLargeScreen/socialResource/videoAccessRank`,
        method: 'GET',
    })
}
// -------------------end------------------------