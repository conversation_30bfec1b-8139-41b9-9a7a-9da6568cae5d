import CryptoJS from 'crypto-js';
import iconsList from '@/assets/configureIcons/iconfont.json';
import { Message } from 'view-design';
/**
 * 深拷贝, 可以提取公共方法
 */
//返回传递给他的任意对象的类
export function isClass(o) {
  if (o === null) return 'Null';
  if (o === undefined) return 'Undefined';
  return Object.prototype.toString.call(o).slice(8, -1);
}

export function deepCopy(obj) {
  if (!obj) {
    return null;
  }
  let result,
    oClass = isClass(obj);
  //确定result的类型
  if (oClass === 'Object') {
    result = {};
  } else if (oClass === 'Array') {
    result = [];
  } else {
    return obj;
  }

  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      let copy = obj[key];
      if (isClass(copy) === 'Object') {
        result[key] = deepCopy(copy); //递归调用
      } else if (isClass(copy) === 'Array') {
        result[key] = deepCopy(copy);
      } else {
        result[key] = obj[key];
      }
    }
  }
  return result;
}
// 时间戳转换成天年月日
export function timeFn(dateDiff, format) {
  let dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)); //计算出相差天数
  let leave1 = dateDiff % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
  let hours = Math.floor(leave1 / (3600 * 1000)); //计算出小时数
  //计算相差分钟数
  let leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
  let minutes = Math.floor(leave2 / (60 * 1000)); //计算相差分钟数
  //计算相差秒数
  let leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
  let seconds = Math.round(leave3 / 1000);
  if (!format) {
    format = 'dd天hh时mm分ss秒';
  }
  const o = {
    'd+': dayDiff, //day
    'h+': hours, //hour
    'm+': minutes, //minute
    's+': seconds, //second
  };

  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}
/*
 *将Date/String类型,解析为String类型.
 *传入String类型,则先解析为Date类型
 *不正确的Date,返回 ''
 *如果时间部分为0,则忽略,只返回日期部分.
 *日期格式对应字符如下(年-yyyy,月-MM,日-dd,时-hh,分-mm,秒-ss,毫秒-S 字符区分大小写)
 */
export function formatDate(v, format) {
  if (!format) {
    format = 'yyyy-MM-dd hh:mm:ss';
  }
  if (typeof v == 'string') v = parseDate(v);
  if (!(v instanceof Date)) {
    return '';
  }
  var o = {
    'M+': v.getMonth() + 1, //month
    'd+': v.getDate(), //day
    'h+': v.getHours(), //hour
    'm+': v.getMinutes(), //minute
    's+': v.getSeconds(), //second
    'q+': Math.floor((v.getMonth() + 3) / 3), //quarter
    // "S" : v.getMilliseconds() //millisecond
  };

  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (v.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  if (/(S+)/.test(format)) {
    format = format.replace(RegExp.$1, ('000' + v.getMilliseconds()).substr(('' + v.getMilliseconds()).length));
  }

  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
    }
  }
  return format;
}

/**
 * 将String类型 ( '2023-06-21' ) 解析为  yyyy年MM月dd日
 */
export function turnFormat(time, format = 'yyyy-MM-dd') {
  let reg = null;
  let turnStr = '';
  switch (format) {
    case 'yyyy-MM-dd':
      reg = /^(\d{4})\-(\d{2})\-(\d{2})$/;
      turnStr = time.replace(reg, '$1年$2月$3日');
      break;
    case 'yyyy-MM':
      reg = /^(\d{4})\-(\d{2})$/;
      turnStr = time.replace(reg, '$1年$2月');
      break;
    case 'yyyy':
      reg = /^(\d{4})$/;
      turnStr = time.replace(reg, '$1年');
      break;
    case '年-月-日':
      reg = /^(\d{4})\年(\d{2})\月(\d{2})日$/;
      turnStr = time.replace(reg, '$1-$2-$3');
      break;
    default:
      break;
  }
  return turnStr;
}

/*
     将String类型解析为Date类型.
     parseDate('2006-1') return new Date(2006,0)
     parseDate(' 2006-1 ') return new Date(2006,0)
     parseDate('2006-1-1') return new Date(2006,0,1)
     parseDate(' 2006-1-1 ') return new Date(2006,0,1)
     parseDate('2006-1-1 15:14:16') return new Date(2006,0,1,15,14,16)
     parseDate(' 2006-1-1 15:14:16 ') return new Date(2006,0,1,15,14,16);
     parseDate('2006-1-1 15:14:16.254') return new Date(2006,0,1,15,14,16,254)
     parseDate(' 2006-1-1 15:14:16.254 ') return new Date(2006,0,1,15,14,16,254)
     parseDate('不正确的格式') retrun null
     */
export function parseDate(str) {
  if (typeof str == 'string') {
    var results = str.match(/^ *(\d{4})-(\d{1,2})-(\d{1,2}) *$/);
    if (!results && str.match(/^ *(\d{4})-(\d{1,2}) *$/)) {
      results = str.match(/^ *(\d{4})-(\d{1,2}) *$/);
      return new Date(parseInt(results[1], 10), parseInt(results[2], 10) - 1);
    }
    if (results && results.length > 3)
      return new Date(parseInt(results[1], 10), parseInt(results[2], 10) - 1, parseInt(results[3], 10));
    results = str.match(/^ *(\d{4})-(\d{1,2})-(\d{1,2}) +(\d{1,2}):(\d{1,2}):(\d{1,2}) *$/);
    if (results && results.length > 6)
      return new Date(
        parseInt(results[1], 10),
        parseInt(results[2], 10) - 1,
        parseInt(results[3], 10),
        parseInt(results[4], 10),
        parseInt(results[5], 10),
        parseInt(results[6], 10),
      );
    results = str.match(/^ *(\d{4})-(\d{1,2})-(\d{1,2}) +(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,5}) *$/);
    if (results && results.length > 7)
      return new Date(
        parseInt(results[1], 10),
        parseInt(results[2], 10) - 1,
        parseInt(results[3], 10),
        parseInt(results[4], 10),
        parseInt(results[5], 10),
        parseInt(results[6], 10),
        parseInt(results[7], 10),
      );
  }
  return null;
}

//时间快速选择
/*
    obj:选择的时间
*/
export function quickDate(obj) {
  obj.timePickerShow = false;
  let now = new Date(),
    year = now.getFullYear(),
    mon = now.getMonth() + 1,
    day = now.getDate(),
    hours = now.getHours(),
    min = now.getMinutes(),
    sec = now.getSeconds();
  switch (obj.value) {
    case 'defined':
      obj.timePickerShow = true;
      break;
    case 'all':
      obj.startTime = '';
      obj.endTime = '';
      break;
    case 'day':
      let nowY = new Date().getTime();
      nowY = nowY - 86400000; //86400000是一天的毫秒数
      let nowYe = new Date(nowY),
        yearY = nowYe.getFullYear(),
        monY = nowYe.getMonth() + 1,
        dayY = nowYe.getDate();
      obj.startTime = this.formatDate(`${yearY}-${monY}-${dayY} ${hours}:${min}:${sec}`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    case 'today':
      obj.startTime = this.formatDate(`${year}-${mon}-${day} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    case 'twoDays':
      let nowT = new Date().getTime();
      nowT = nowT - 86400000 * 2; //86400000是一天的毫秒数
      let nowTe = new Date(nowT),
        yearT = nowTe.getFullYear(),
        monT = nowTe.getMonth() + 1,
        dayT = nowTe.getDate();
      obj.startTime = this.formatDate(`${yearT}-${monT}-${dayT} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    case 'week':
      let nowD = new Date().getTime();
      nowD = nowD - 86400000 * 7; //86400000是一天的毫秒数
      let nowDe = new Date(nowD),
        yearD = nowDe.getFullYear(),
        monD = nowDe.getMonth() + 1,
        dayD = nowDe.getDate();
      obj.startTime = this.formatDate(`${yearD}-${monD}-${dayD} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    case 'thisWeek': {
      let now = new Date();
      let nowTime = now.getTime();
      let nowDay = now.getDay();
      nowTime = nowTime - (nowDay - 1) * 86400000;
      let nowDe = new Date(nowTime),
        yearD = nowDe.getFullYear(),
        monD = nowDe.getMonth() + 1,
        dayD = nowDe.getDate();
      obj.startTime = this.formatDate(`${yearD}-${monD}-${dayD} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    }
    case 'month':
      let nowM = new Date().getTime();
      nowM = nowM - 86400000 * 30; //86400000是一天的毫秒数
      let nowMe = new Date(nowM),
        yearM = nowMe.getFullYear(),
        monM = nowMe.getMonth() + 1,
        dayM = nowMe.getDate();
      obj.startTime = this.formatDate(`${yearM}-${monM}-${dayM} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    case 'thisMonth': {
      let nowM = new Date();
      nowM.setDate(1);
      let nowMe = new Date(nowM),
        yearM = nowMe.getFullYear(),
        monM = nowMe.getMonth() + 1,
        dayM = nowMe.getDate();
      obj.startTime = this.formatDate(`${yearM}-${monM}-${dayM} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    }
    case 'thisYear': {
      let nowM = new Date();
      nowM.setMonth(0, 1);
      let nowMe = new Date(nowM),
        yearM = nowMe.getFullYear(),
        monM = nowMe.getMonth() + 1,
        dayM = nowMe.getDate();
      obj.startTime = this.formatDate(`${yearM}-${monM}-${dayM} 00:00:00`);
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      break;
    }
    case 'halfYear':
      obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
      if (mon > 6) {
        mon -= 6;
      } else {
        year -= 1;
        mon = 12 + mon - 6;
      }
      obj.startTime = this.formatDate(`${year}-${mon}-${day} 00:00:00`);
      break;
    default:
      this.initSearchTime(obj);
      break;
  }
}

//初始化检索时间alarm & retrieval
export function initSearchTime(obj) {
  let now = new Date(),
    year = now.getFullYear(),
    mon = now.getMonth() + 1,
    day = now.getDate(),
    hours = now.getHours(),
    min = now.getMinutes(),
    sec = now.getSeconds();
  if (mon == 1) {
    mon = 12;
    year = year - 1;
  }
  obj.startTime = this.formatDate(`${year}-${mon}-${day} 00:00:00`);
  obj.endTime = this.formatDate(`${year}-${mon}-${day} ${hours}:${min}:${sec}`);
}
/**
 * 判断年份是否为润年
 *
 * @param {Number} year
 */
function isLeapYear(year) {
  return year % 400 == 0 || (year % 4 == 0 && year % 100 != 0);
}
/**
 * 获取某一年份的某一月份的天数
 *
 * @param {Number} year
 * @param {Number} month
 */
function getMonthDays(year, month) {
  return [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month] || (isLeapYear(year) ? 29 : 28);
}
/**
 * 获取某年的某天是第几周
 * @param {Number} y
 * @param {Number} m
 * @param {Number} d
 * @returns {Number}
 */
export function getWeekNumber(date) {
  var now = new Date(date),
    year = now.getFullYear(),
    month = now.getMonth(),
    days = now.getDate();
  //那一天是那一年中的第多少天
  for (var i = 0; i < month; i++) {
    days += getMonthDays(year, i);
  }

  //那一年第一天是星期几
  var yearFirstDay = new Date(year, 0, 1).getDay() || 7;

  var week = null;
  if (yearFirstDay == 1) {
    week = Math.ceil(days / yearFirstDay);
  } else {
    days -= 7 - yearFirstDay + 1;
    week = Math.ceil(days / 7) + 1;
  }

  return week;
}

/**
 * 仿照jquery.extend方法
 * 使用方式  extend({}, {}); 深拷贝 extend(true, {}, {});
 * @returns {any|{}}
 */
export function extend() {
  var options,
    name,
    src,
    copy,
    copyIsArray,
    clone,
    target = arguments[0] || {},
    i = 1,
    length = arguments.length,
    deep = false,
    toString = Object.prototype.toString,
    hasOwn = Object.prototype.hasOwnProperty,
    // push = Array.prototype.push,
    // slice = Array.prototype.slice,
    // trim = String.prototype.trim,
    // indexOf = Array.prototype.indexOf,
    class2type = {
      '[object Boolean]': 'boolean',
      '[object Number]': 'number',
      '[object String]': 'string',
      '[object Function]': 'function',
      '[object Array]': 'array',
      '[object Date]': 'date',
      '[object RegExp]': 'regexp',
      '[object Object]': 'object',
    },
    jQuery = {
      isFunction: function (obj) {
        return jQuery.type(obj) === 'function';
      },
      isArray:
        Array.isArray ||
        function (obj) {
          return jQuery.type(obj) === 'array';
        },
      isWindow: function (obj) {
        return obj != null && obj == obj.window;
      },
      isNumeric: function (obj) {
        return !isNaN(parseFloat(obj)) && isFinite(obj);
      },
      type: function (obj) {
        return obj == null ? String(obj) : class2type[toString.call(obj)] || 'object';
      },
      isPlainObject: function (obj) {
        if (!obj || jQuery.type(obj) !== 'object' || obj.nodeType) {
          return false;
        }
        try {
          if (
            obj.constructor &&
            !hasOwn.call(obj, 'constructor') &&
            !hasOwn.call(obj.constructor.prototype, 'isPrototypeOf')
          ) {
            return false;
          }
        } catch (e) {
          return false;
        }
        var key;
        return key === undefined || hasOwn.call(obj, key);
      },
    };
  if (typeof target === 'boolean') {
    deep = target;
    target = arguments[1] || {};
    i = 2;
  }
  if (typeof target !== 'object' && !jQuery.isFunction(target)) {
    target = {};
  }
  if (length === i) {
    target = this;
    --i;
  }
  for (i; i < length; i++) {
    if ((options = arguments[i]) != null) {
      for (name in options) {
        src = target[name];
        copy = options[name];
        if (target === copy) {
          continue;
        }
        if (deep && copy && (jQuery.isPlainObject(copy) || (copyIsArray = jQuery.isArray(copy)))) {
          if (copyIsArray) {
            copyIsArray = false;
            clone = src && jQuery.isArray(src) ? src : [];
          } else {
            clone = src && jQuery.isPlainObject(src) ? src : {};
          }
          // WARNING: RECURSION
          target[name] = extend(deep, clone, copy);
        } else if (copy !== undefined) {
          target[name] = copy;
        }
      }
    }
  }
  return target;
}

export function getUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

/**
 *
 * @param {*} totalData 获取到的所有的数据
 * @param {*} currentPage 当前的页码
 * @param {*} pageSize 当前的每页多少条
 * pageData 当前的分页分出的数据
 */
export function pagination(totalData, currentPage, pageSize) {
  let pageData = [];
  for (let i = 0, length = totalData.length; i < length; i++) {
    if (i < pageSize * currentPage && i >= pageSize * (currentPage - 1)) {
      pageData.push(totalData[i]);
    }
  }
  return pageData;
}

//代码雨
export function codeRain(flag, id, domWidth, domHeight) {
  let timer = null;
  if (flag) {
    var c = document.getElementById(id);
    var ctx = c.getContext('2d');
    c.width = domWidth;
    c.height = domHeight;
    //				ctx.fillRect(0,0,100,100);
    //				a,b,c,d分别代表x方向偏移,y方向偏移,宽，高
    var string1 = 'abcdefghijklmnopqrstuvwxyz';
    string1.split('');
    var fontsize = 16;
    var columns = c.width / fontsize;
    var drop = [];
    for (var x = 0; x < columns; x++) {
      drop[x] = 0;
    }

    let drap = () => {
      ctx.fillStyle = 'rgba(16,39,93,0.07)';
      ctx.fillRect(0, 0, c.width, c.height);
      ctx.fillStyle = '#0598c0';
      ctx.font = fontsize + 'px arial';
      for (var i = 0; i < drop.length; i++) {
        var text1 = string1[Math.floor(Math.random() * string1.length)];
        ctx.fillText(text1, i * fontsize, drop[i] * fontsize);
        drop[i]++;
        if (drop[i] * fontsize > c.height && Math.random() > 0.9) {
          //90%的几率掉落
          drop[i] = 0;
        }
      }
    };
    timer = setInterval(drap, 20);
  } else {
    clearInterval(timer);
    timer = null;
  }
}

// 数组对象去重
export function deduplication(list, target) {
  let hash = {};
  let array = [];
  array = list.reduceRight((item, next) => {
    hash[next[target]] ? '' : (hash[next[target]] = true && item.unshift(next));
    return item;
  }, []);
  return array;
}

//秒级时间转换为分秒格式
export function totalCostTimeFormat(val) {
  if (val > 59) {
    return `${Math.floor(val / 60)} 分${val % 60} 秒`;
  } else {
    return `${val} 秒`;
  }
}

/**
 * 一维数组转树状数组
 * arr: 要转换的一维数组
 * id: 唯一识别
 * pid: 父级唯一识别
 */
export function arrayToJson(arr, id, pid, children = 'children') {
  let tempArr = [];
  let tempObj = {};
  for (let i = 0, l = arr.length; i < l; i++) {
    tempObj[arr[i][id]] = arr[i];
  }
  for (let i = 0, l = arr.length; i < l; i++) {
    let key = tempObj[arr[i][pid]];

    if (key) {
      if (!key[children]) {
        key[children] = [];
        key[children].push(arr[i]);
      } else {
        key[children].push(arr[i]);
      }
    } else {
      tempArr.push(arr[i]);
    }
  }
  return tempArr;
}

export function jsonToArray(nodes, children = 'children') {
  let r = [];
  if (Array.isArray(nodes)) {
    for (let i = 0, l = nodes.length; i < l; i++) {
      r.push(nodes[i]);
      if (Array.isArray(nodes[i][children]) && nodes[i][children].length > 0)
        //将children递归的push到最外层的数组r里面
        r = r.concat(jsonToArray(nodes[i][children]));
      delete nodes[i][children];
    }
  }
  return r;
}
/**
 * 根据id找到树中的节点
 * @param {*} data 树的数组
 * @param {*} nodeId 需要找到的结点
 * @param {*} id 查询的字段名称
 * @param {*} children 树的子节点的字段名称
 */
export function getCurrentNode(data, nodeId, id = 'regionCode', children = 'children') {
  for (let i = 0; i < data.length; i++) {
    if (nodeId == data[i][id]) {
      return data[i];
    }
    if (data[i][children]) {
      const res = getCurrentNode(data[i].children, nodeId);
      if (res) {
        return res;
      }
    }
  }
}

/**
 *  人员标签tagList转换带color
 */
export function exchangeTagList(tagList, libList) {
  let newTagList = [];
  let tempObj = {};
  if (!!tagList && tagList.length > 0 && libList.length > 0) {
    tagList.forEach((row) => {
      tempObj[row] = row;
    });
    libList.forEach((row) => {
      row.tagList.forEach((one) => {
        if (tempObj[one.tag]) {
          let newTags = one;
          newTags.originalLib = row.name;
          newTagList.push(newTags);
        }
      });
    });
    newTagList.forEach((item) => {
      item.newColor = `tag-${item.color}`;
    });
  }
  return newTagList;
}

/**
 * 获取 url参数
 */
export function getRegust() {
  let url = location.search;
  let theRequest = new Object();
  if (url.indexOf('?') !== -1) {
    let str = url.substr(1);
    let strs = str.split('&');
    for (let i = 0; i < strs.length; i++) {
      theRequest[strs[i].split('=')[0]] = unescape(strs[i].split('=')[1]);
    }
  }
  // location.search = '';
  return theRequest;
}

/**
 * export excel导出
 *
 */
export function exportfile(res, filename = '', type = '') {
  return new Promise((reslove, reject) => {
    const resData = res.data;
    const fileReader = new FileReader();
    fileReader.onloadend = () => {
      try {
        const errJson = JSON.parse(fileReader.result); // 说明是普通对象数据，后台转换失败
        reject(errJson);
      } catch (err) {
        // 解析成对象失败，说明是正常的文件流
        reslove({
          code: 200,
        });
        const blob = new Blob([res.data], {
          type: type,
        });

        //获取heads中的filename文件名
        const temp = filename ? filename : res.headers['content-disposition'].split(';')[1].split('filename=')[1];
        const fileName = decodeURIComponent(temp);
        downloadFile(blob, fileName);
      }
    };
    fileReader.readAsText(resData);
  });
}

export async function transformBlob(url, fileName) {
  const response = await fetch(url, {
    mode: 'cors', //跨域
  }); // 内容转变成blob地址
  const blob = await response.blob();
  const _fileName = fileName ? fileName : url.split('/')[url.split('/').length - 1];
  // 下载文件
  downloadFile(blob, _fileName);
}

function downloadFile(blob, fileName) {
  const url = window.URL.createObjectURL(blob);
  let aLink = document.createElement('a');
  aLink.setAttribute('download', fileName);
  aLink.style.display = 'none';
  aLink.href = url;
  document.body.appendChild(aLink);
  aLink.click();
  document.body.removeChild(aLink);
  window.URL.revokeObjectURL(url);
}

export const emptyJSX = `
<div class="no-data">
    <i class="no-data-img icon-font icon-zanwushuju1"></i>
    <div class='null-data-text'>暂无数据</div>
</div>
`;

let themeKey = document.documentElement.getAttribute('data-theme');
export const emptyJSXImg = `
<div class="no-data">
    <img class="no-data-img" src=${
      themeKey === 'dark'
        ? require('@/assets/img/common/nodata-img.png')
        : require('@/assets/img/common/nodata-img-light.png')
    } />
    <div class='null-data-text'>暂无数据</div>
</div>
`;

/**
 * @description 验证8-20位数字或字母密码
 * @param {*} pwd 密码
 */
export const checkPwd = (pwd) => {
  const re = /^[a-zA-Z]\w{7,19}$/;
  if (re.test(pwd)) return true;
  else return false;
};

/**
 * @description 验证身份证号
 * @param {*} cardId 身份证号
 */
export const checkCardId = (cardId) => {
  // const idcard_patter = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  const idcard_patter =
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
  if (idcard_patter.test(cardId)) return true;
  else return false;
};

/**
 * @description 验证手机号码
 * @param {Number} phoneNumber 手机号
 */
export const checkPhone = (phoneNumber) => {
  const phone_number_patter = /^1(3|4|5|7|8)\d{9}$/;
  if (phone_number_patter.test(phoneNumber)) return true;
  else return false;
};

/**
 * @description 验证邮箱
 * @param {*} email 邮箱
 */
export const checkEmail = (email) => {
  const email_patter = /^\w+((-\w+)|(\.\w+))*\@{1}\w+\.{1}\w{2,4}(\.{0,1}\w{2}){0,1}/gi;
  if (email_patter.test(email)) return true;
  else return false;
};

/**
 * @description 验证IP
 * @param {*} ip IP
 */
export const checkIp = (ip) => {
  const ip_patter = /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/gi;
  if (ip_patter.test(ip)) return true;
  else return false;
};

/**
 * @description 验证短号
 * @param {*} shortNumber 短号
 */
export const checkShortNumber = (shortNumber) => {
  const short_number_patter = /^\d{3,6}$/g;
  if (short_number_patter.test(shortNumber)) return true;
  else return false;
};

export const checkLon = (lon) => {
  const lonReg = /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,10})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/;
  if (!lonReg.test(lon)) {
    return false;
  }
  return true;
};

export const checkLat = (lat) => {
  const latReg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,10}|90\.0{0,6}|[0-8]?\d{1}|90)$/;
  if (!latReg.test(lat)) {
    return false;
  }
  return true;
};

export const checkLonLat = (lon, lat) => {
  if (!lon || Number(lon) === 0 || !lat || Number(lat) === 0) {
    Message.warning('经纬度为空，无法上图展示！');
    return false;
  }
  if (!checkLat(lat)) {
    Message.warning('非法纬度，无法上图展示！');
    return false;
  }
  if (!checkLon(lon)) {
    Message.warning('非法经度，无法上图展示！');
    return false;
  }
  return true;
};

//des解码
export const decryptDes = (ciphertext, key) => {
  const keyHex = CryptoJS.enc.Latin1.parse(key);
  // direct decrypt ciphertext
  const decrypted = CryptoJS.DES.decrypt(
    {
      ciphertext: CryptoJS.enc.Base64.parse(ciphertext),
    },
    keyHex,
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    },
  );
  return decrypted.toString(CryptoJS.enc.Utf8);
};

//des加密
export const encryptByDES = (message, key) => {
  var keyHex = CryptoJS.enc.Utf8.parse(key);
  var encrypted = CryptoJS.DES.encrypt(message, keyHex, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.toString();
};

/**
 * @description 根据iconid 匹配icon className
 * @param {*} shortNumber iconid
 */
const iconsDataList = iconsList.glyphs;
export const geticonClassName = (iconUrl) => {
  let font_class;
  for (let i = 0; i < iconsDataList.length; i++) {
    if (iconsDataList[i].icon_id == iconUrl) {
      font_class = iconsDataList[i].font_class;
      break;
    }
  }
  return font_class;
};
//保留两位小数
export function financial(x) {
  return Number.parseFloat(x).toFixed(2);
}

// 千位符 https://juejin.cn/post/6844904040912912397
export function toThousands(num) {
  var numStr = (num || 0).toString();
  return numStr.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
}

// 对比两个对象是否一样
function getDataType(data) {
  const temp = Object.prototype.toString.call(data);
  const type = temp.match(/\b\w+\b/g);
  return type.length < 2 ? 'Undefined' : type[1];
}

export function isObjectChanged(source, comparison) {
  const iterable = (data) => ['Object', 'Array'].includes(getDataType(data));
  if (!iterable(source)) {
    throw new Error('传入的值不是一个可以遍历的对象或者数组');
  }

  if (getDataType(source) !== getDataType(comparison)) {
    return true;
  }

  // 获取源数据所有的key
  // const sourceKeys = Object.keys(source)
  // 将对比的数据合并到源数据，并提取所有的key
  // 合并源数据和对比数据
  const comparisonKeys = Object.keys({
    ...source,
    ...comparison,
  });

  // if (sourceKeys.length !== comparisonKeys.length) {
  //   return true
  // }
  let difference = [];
  comparisonKeys.forEach((key) => {
    // 如果对比的数据为对象或数组则递归调用
    if (iterable(source[key])) {
      if (getDataType(source[key]) === 'Array' && source[key]?.length !== comparison[key]?.length) {
        difference.push(key);
        return;
      }
      return isObjectChanged(source[key], comparison[key]);
    } else {
      if (!(!comparison[key] && !source.hasOwnProperty(key))) {
        if (source[key] !== comparison[key]) {
          difference.push(key);
        }
      }
    }
  });
  return difference;
}

export function fontSize(res) {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clientWidth) return;
  // const isScreen = clientWidth == 1366
  const fontSize = clientWidth / 1920;
  return res * fontSize;
}

/**
 * 根据regionCode 找到本级及所有的下级
 * @param treeData
 * @param regionCode
 * @returns {*[]}
 */
export function getTreeChildren(treeData, regionCode) {
  let data = [];
  let index = treeData.findIndex((item) => item.regionCode === regionCode);
  if (index === -1) {
    return [];
  }
  let { parentPath, id } = treeData[index];
  data.push(treeData[index]);
  treeData.forEach((item) => {
    if (item.parentPath.includes(`${id},${parentPath}`)) {
      data.push(item);
    }
  });
  return data;
}

export function responseMessage(params, showDetail = true) {
  let { successText = '成功', failureText = '失败', piecesName = '条', success = 0, failure = 0 } = params;
  //只显示撤回成功或失败，用于单条结果
  if (!showDetail && (success || failure)) {
    success ? Message.success(`${successText}`) : Message.error(`${failureText}`);
    return;
  }
  //全成功
  if (success && !failure) {
    Message.success(`${successText} ${success} ${piecesName}`);
    return;
  }
  //成功，失败都不为0
  if (success && failure) {
    Message.success(`${successText} ${success} ${piecesName}，${failureText} ${failure} ${piecesName}`);
    return;
  }
  //全失败
  if (failure && !success) {
    Message.error(`${failureText} ${failure} ${piecesName}`);
    return;
  }
  //异常情况，全为0
  if (!failure && !success) {
    Message.error(`系统异常，请联系管理员！`);
    return;
  }
}
