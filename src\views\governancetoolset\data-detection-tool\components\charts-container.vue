<template>
  <div class="charts-container">
    <div
      :class="{ 'charts-item': true, [`bg-${index % 5}`]: true }"
      v-for="(item, index) in abnormalCount"
      :key="index + item.title"
    >
      <!-- 不传icon，展示默认图标 -->
      <!--      <span v-if="!item.icon" class="icon-font icon-exceptionlibrary f-32 icon-2"></span>-->
      <span
        :class="{
          'icon-font': true,
          'f-40': true,
          [item.icon]: true,
          [`icon-${index % 5}`]: true,
        }"
      ></span>
      <div class="number-wrapper">
        <p class="f-12 color-white">{{ item.title }}</p>
        <p class="f-18 color-num">{{ commaNumber(item.count || 0) }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    abnormalCount: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
  },
};
</script>
<style lang="less" scoped>
.charts-container {
  margin-top: 10px;
  display: flex;
  overflow-x: auto;
  justify-content: flex-start;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 2px;
  .charts-item {
    flex-shrink: 0;
    margin-right: 10px;
    width: 348px;
    min-width: 200px;
    height: 100px;
    background: #0f2f59;
    display: flex;
    justify-content: center;
    align-items: center;
    .number-wrapper {
      display: inline-block;
      margin-left: 32px;
    }
  }
  .f-40 {
    font-size: 40px;
  }
  .color-white {
    color: #fff;
  }
  .color-num {
    font-size: 18px;
    margin-top: 5px;
    font-weight: bold;
    line-height: 22px;
  }
  .icon-0 {
    background: linear-gradient(180deg, #26d82c 0%, #127d0a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-1 {
    background: linear-gradient(180deg, #b58e0f 0%, #bb6603 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-2 {
    background: linear-gradient(180deg, #f24e2c 0%, #772c0a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-3 {
    background: linear-gradient(180deg, #c122f8 0%, #7428cb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-4 {
    background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .bg-0 {
    background: rgba(32, 182, 35, 0.21);
    .color-num {
      color: #22c326;
    }
  }
  .bg-1 {
    background: rgba(235, 178, 37, 0.21);
    .color-num {
      color: #ebb225;
    }
  }
  .bg-2 {
    background: rgba(198, 64, 49, 0.21);
    .color-num {
      color: #dd4826;
    }
  }
  .bg-3 {
    background: rgba(154, 24, 149, 0.21);
    .color-num {
      color: #b113b1;
    }
  }
  .bg-4 {
    background: rgba(15, 167, 245, 0.21);
    .color-num {
      color: var(--color-primary);
    }
  }
}
</style>
