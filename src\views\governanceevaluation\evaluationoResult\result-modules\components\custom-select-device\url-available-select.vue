<template>
  <basic-select v-bind="getAttrs" v-on="$listeners">
    <!-- 表格插槽 -->
    <template slot="checkStatus" slot-scope="{ row }">
      <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
        {{ qualifiedColorConfig[row.outcome].dataValue }}
      </Tag>
      <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
    </template>
    <template #qualifiedRate="{ row }">
      <span>{{ row.qualifiedRate || 0 }}%</span>
    </template>
    <template #unqualifiedNum="{ row }">
      <span class="font-red">{{ row.unqualifiedNum }}</span>
    </template>
    <template #urlNotUseCount="{ row }">
      <span class="font-red">{{ row.urlNotUseCount }}</span>
    </template>
  </basic-select>
</template>
<script>
import detectionResult from '@/config/api/detectionResult';
export default {
  props: {
    qualifiedColorConfig: {},
    moduleData: {
      type: Object,
      default: () => {},
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: [],
      },
    };
  },
  created() {
    // 获取不合格原因
    this.getQualificationList();
  },
  methods: {
    // 获取不合格原因下拉列表
    async getQualificationList() {
      try {
        let params = {
          indexType: this.moduleData.indexType,
          model: 1, // 设备模式
        };
        let res = await this.$http.get(detectionResult.getUnqualifiedInfo, {
          params: params,
        });
        let options = res.data.data || [];
        this.formItemData.forEach((opt) => {
          if (opt.key === 'errorCodes') {
            opt.options = options.map((item) => {
              return { value: Number(item.key), label: item.value };
            });
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
        formItemData: this.formItemData,
        moduleData: this.moduleData,
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
