<template>
  <div class="spaceinformation height-full">
    <div class="left">
      <manage-bar ref="manageBar" @getDevList="getDevList" @findDevById="findDevById"></manage-bar>
    </div>
    <div class="map-wrap">
      <component
        ref="mapRef"
        :is="componentName"
        :cameraList="cameraList"
        :bubbleData="bubbleData"
        :locationList="locationList"
        :deviceDirectionList="deviceDirectionList"
        @handleQueryDeviceInfo="handleQueryDeviceInfo"
        @clear="clear"
        @handleUpdatePointInfo="handleUpdatePointInfo"
      >
      </component>
    </div>
    <div class="layer-description">
      <div>
        <img src="@/assets/img/device-map/normal.png" />
        合格
      </div>
      <div>
        <img src="@/assets/img/device-map/abnormal.png" />
        异常
      </div>
    </div>
    <top-right-buttons
      :cameraList="cameraList"
      :allCameraList="allCameraList"
      @handleDevLayer="handleDevLayer"
      @handleChangeUseBoundary="handleUseBoundary"
      @addAreaOverlay="addAreaOverlay"
    ></top-right-buttons>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import user from '@/config/api/user';
export default {
  name: 'spaceinformation',
  data() {
    return {
      componentName: 'NpgisMap',
      cameraList: [],
      chooseMapItem: {},
      geoRegions: [],
      center: {},
      allCameraList: [],

      bubbleData: {},
      // unqualified: ['0001', '0010', '0100', '0110', '0011', '0101', '0111'], // 不合格类型
      // isUnqualified: false,
      errorMessage: '',
      locationActive: false,
      allDicData: {}, //放置所有字典类型的对象
      deviceDirectionList: [], //设备方位
      locationList: [], //安装位置（室内外）
      dicDataEnum: Object.freeze({
        propertySearch_roomtype: 'locationList',
        propertySearch_directiontype: 'deviceDirectionList',
      }),
    };
  },
  mounted() {
    this.dictTypeListGroupByType();
  },
  methods: {
    // 获取地图上需要显示的设备
    getDevList(data) {
      this.allCameraList = this.$util.common.deepCopy(data);
      this.cameraList = this.$util.common.deepCopy(data);
    },
    // 通过id查询设备详情
    findDevById(row) {
      this.handleQueryDeviceInfo(row);
    },
    //图层管理
    handleDevLayer(data) {
      this.$set(this, 'cameraList', data);
    },
    // 通过设备id查询设备详情
    async getDeviceDetails(id) {
      try {
        let res = await this.$http.get(equipmentassets.getDeviceById + `?id=${id}`);
        this.bubbleData = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 获取空间信息异常
    async getUnqualifiedList(deviceId) {
      try {
        const params = {
          deviceCode: deviceId,
        };
        const res = await this.$http.post(equipmentassets.querySpaceUnqualifiedList, params);
        const errorMessageArr = res.data.data || [];
        this.errorMessage = !!errorMessageArr && errorMessageArr.length ? errorMessageArr.join('、') : '';
      } catch (err) {
        console.log(err);
      }
    },

    async handleQueryDeviceInfo(devInfo) {
      await this.getUnqualifiedList(devInfo.deviceId || devInfo.ObjectID); // 获取空间信息异常原因
      await this.getDeviceDetails(devInfo.id);
      this.$set(this.bubbleData, 'errorMessage', this.errorMessage);
    },
    handleUpdatePointInfo() {
      this.$refs.manageBar.getTreatedList(); // 更新设备列表数据
    },
    // 清空地图数据
    clear() {
      this.cameraList = [];
    },
    //获取安装位置、监控方位的字典
    async dictTypeListGroupByType() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        const dataObject = data.data;
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = dataObject.find((row) => {
            return !!row[key];
          });
          this.$set(this.allDicData, this.dicDataEnum[key], obj[key]);
        });
        this.locationList = this.allDicData['locationList'];
        this.deviceDirectionList = this.allDicData['deviceDirectionList'];
      } catch (err) {
        console.log(err);
      }
    },
    //打开、关闭边界
    handleUseBoundary(val) {
      if (!val) {
        this.$refs.mapRef?.closeMapBoundary();
      }
    },
    //绘制地图边界
    addAreaOverlay(arr) {
      this.$refs.mapRef?.handleAddNewOverLays(arr);
    },
  },
  components: {
    // MapPgis: require('@/components/map-pgis/index.vue').default,
    ManageBar: require('./components/manage-bar.vue').default,
    // PgisMap: require('./components/pgis-map.vue').default,
    NpgisMap: require('./components/npgis-map.vue').default,
    TopRightButtons: require('./components/top-right-buttons.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .layer-description {
    background: #07295d;
    box-shadow: 0px 3px 10px #000000;
    color: #f5f5f5;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .layer-description {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    color: rgba(0, 0, 0, 0.8);
  }
}
.spaceinformation {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--bg-content);
}

.map-wrap {
  position: relative;
  width: 100%;
  height: 100%;
}

.left {
  width: 313px;
  height: calc(100% - 20px);
  margin: 10px;
  z-index: 1003;
}

#fmap {
  flex: 1;
  height: 100%;
}

.form-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #07295d !important;
  border: 2px solid #2967c8 !important;
  box-shadow: 0px 3px 10px #000000 !important;
  border-radius: 4px;
}

.bb-modal-header-close {
  position: absolute;
  right: -29px;
  top: -29px;
  //&:before{
  //  color: #768192 !important;
  //}
}

.layer-description {
  position: absolute;
  right: 20px;
  bottom: 10px;
  padding: 10px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  > div {
    display: flex;
    align-items: center;
    &:first-child {
      margin-bottom: 8px;
    }
    & > img {
      width: 17px;
      margin-right: 6px;
    }
  }
}

.mask {
  position: relative;
}
.mask:after {
  content: '设备数据加载中...';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  color: var(--color-content);
  text-align: center;
  line-height: 25;
  vertical-align: sub;
  font-size: 26px;
  display: inline-block;
  z-index: 10;
}

@{_deep} .fc-map-single-bubble.normal-bubble-box {
  background: #07295d !important;
  border: 1px solid #2967c8 !important;
  box-shadow: 0px 3px 10px #000000 !important;
  border-radius: 4px;

  &::after {
    border-color: #07295d transparent transparent #07295d !important;
  }
}
</style>
