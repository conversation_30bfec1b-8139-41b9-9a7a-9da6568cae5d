<!--
    * @FileDescription: 人脸查询身份证
    * @Author: H
    * @Date: 2023/11/14
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-17 09:43:54
-->
<template>
  <div class="idCard-page">
    <div class="page-title" @click="handleClose">
      <Icon type="ios-close" />
    </div>
    <div class="table-container">
      <div class="table-content">
        <div
          class="list-card box-1"
          v-for="(item, index) in dataList"
          :key="index"
        >
          <div class="img-content">
            <!-- <template>
							<ui-image :src="item.photoUrl" alt="静态库" viewer />
							<div class="idCardNo">{{ item.idCardNo }}</div>
						</template> -->
            <template>
              <swiper
                ref="mySwiper"
                :options="swiperOption"
                class="my-swiper"
                :id="`swipe${item.id}`"
              >
                <swiper-slide
                  v-for="(ite, index) in item.photoUrlList"
                  :key="index"
                >
                  <ui-image :src="ite.photoUrl" viewer />
                </swiper-slide>
              </swiper>
              <div
                class="swiper-pagination"
                :id="`swipe${1}`"
                :class="
                  item.photoUrlList.length < 2 ? 'my-pagination-hidden' : ''
                "
              ></div>
              <div class="idCardNo">{{ item.idCardNo }}</div>
            </template>
          </div>
          <!-- 静态库 -->
          <div class="bottom-info">
            <time>
              <Tooltip content="姓名" placement="right" transfer theme="light">
                <i class="iconfont icon-xingming"></i>
              </Tooltip>
              {{ item.name }}
            </time>
            <p>
              <Tooltip
                content="库名称"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-shujulaiyuan"></i>
              </Tooltip>
              {{ item.libName }}
            </p>
          </div>
          <div class="fast-operation-bar">
            <Poptip trigger="hover" placement="right-start">
              <i class="iconfont icon-gengduo"></i>
              <div class="mark-poptip" slot="content">
                <p @click="archivesPage(item)">
                  <i class="iconfont icon-renlian1"></i>以图搜图
                </p>
                <p @click="handleTargetAdd(item)">
                  <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
                </p>
              </div>
            </Poptip>
          </div>
        </div>
        <div
          class="empty-card-1"
          v-for="(item, index) of 9 - (dataList.length % 9)"
          :key="index + 'demo'"
        ></div>
      </div>
      <ui-empty v-if="dataList.length === 0 && !listLoading"></ui-empty>
      <ui-loading v-if="listLoading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[27, 54, 81, 108]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
  </div>
</template>
<script>
import { faceIdentity } from "@/api/modelMarket";
import { swiper, swiperSlide } from "vue-awesome-swiper";
export default {
  data() {
    return {
      dataList: [],
      listLoading: false,
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      total: 0,
      infoList: {},
      swiperOption: {
        direction: "horizontal",
      },
    };
  },
  components: {
    swiper,
    swiperSlide,
  },
  methods: {
    /**
     * @description: 以图搜图
     * @param {object} item 当前信息
     */
    archivesPage(item) {
      if (!item.photoUrlList.length) {
        this.$Message.warning("未识别");
        return;
      }
      // 获取当前swiper展示的index
      let idx = document.getElementById("swipe" + item.id).swiper.activeIndex;
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        query: {
          imgUrl: item.photoUrlList[idx].photoUrl,
        },
      });
      window.open(href, "_blank");
    },

    /**
     * @description: 搜索目标添加
     */
    handleTargetAdd(item) {
      // 为了兼容父组件方法
      let traitImg =
        item.photoUrlList && item.photoUrlList.length
          ? item.photoUrlList[0].photoUrl
          : "";
      this.$emit("targetAdd", { traitImg });
    },
    searchList(obj) {
      this.infoList = obj;
      this.pageList();
    },
    pageList() {
      this.listLoading = true;
      let params = {
        idCardNo: this.infoList.idCardNo,
        name: this.infoList.name,
        ...this.pageInfo,
      };
      faceIdentity(params)
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.dataList = entities || [];
          // 处理 photoUrlList 为null问题
          for (let item of this.dataList) {
            item.photoUrlList = item.photoUrlList || [];
          }
          this.$forceUpdate();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.pageList();
    },
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.pageList();
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
.idCard-page {
  position: absolute;
  height: 100%;
  width: 100%;
  bottom: 0;
  left: 0;
  background: #fff;
  z-index: 99;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 20px 10px 10px;
  .page-title {
    font-size: 40px;
    background: #f00;
    color: #fff;
    width: 42px;
    height: 42px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    z-index: 11;
    transform: rotate(0deg);
    transition: all 0.4s ease-out;
    &:hover {
      transform: rotate(180deg);
    }
  }
  .table-container {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    .table-content {
      overflow: auto;
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      justify-content: start;
      align-content: flex-start;
      .ui-table {
        height: 100%;
      }
    }
  }
}
.img-content {
  width: 100%;
  position: relative;
  border: 1px solid #cfd6e6;
  height: 167px;
  img {
    width: 100%;
    height: 100%;
    display: block;
  }
  .similarity {
    position: absolute;
    z-index: 8;
    display: flex;
    flex-direction: column;
  }
  .shade {
    position: absolute;
  }
  .num {
    font-size: 12px;
    padding: 2px 5px;
    border-radius: 4px;
    color: #fff;
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    margin-bottom: 2px;
  }
  .gerling-num {
    font-size: 12px;
    padding: 2px 5px;
    border-radius: 4px;
    color: #fff;
    background: linear-gradient(180deg, #f29f4c 0%, #f29f4c 100%);
  }
  .shade {
    background: rgba(0, 0, 0, 0.7);
    font-size: 12px !important;
    width: 100%;
    bottom: 0;
    left: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    line-height: 18px;
    padding: 3px 0;
  }
  .swiper-container {
    height: inherit;
  }
}
.idCardNo {
  background: rgba(0, 0, 0, 0.7);
  height: 26px;
  line-height: 26px;
  text-align: center;
  margin-top: -26px;
  z-index: 999;
  position: absolute;
  width: 100%;
  color: #2c86f8;
  // font-size: 13px;
  font-weight: 600;
}
</style>