<template>
  <div class="statistic auto-fill">
    <ui-table
        reserveSelection
        class="ui-table auto-fill mt-sm"
        row-key="id"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
    >
      <template #deviceId="{ row }">
            <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">
              {{ row.deviceId }}
            </span>
      </template>
      <template #deviceName="{ row }">
        <Tooltip :content="row.deviceName" :disabled="!row.deviceName">
          <div :class="{ 'check-num': checkField(row, 'deviceName') }" class="width-sm inline ellipsis">
            {{ row.deviceName }}
          </div>
        </Tooltip>
      </template>
      <template #orgName="{ row }">
        <span :class="{ 'check-num': checkField(row, 'orgName') }">{{ row.orgName }}</span>
      </template>
      <template #civilName="{ row }">
        <span :class="{ 'check-num': checkField(row, 'civilName') }">{{ row.civilName }}</span>
      </template>
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span :class="{ 'check-num': checkField(row, 'longitude') }">{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span :class="{ 'check-num': checkField(row, 'latitude') }">{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #ipAddr="{ row }">
        <span :class="{ 'check-num': checkField(row, 'ipAddr') }">{{ row.ipAddr }}</span>
      </template>
      <template #macAddr="{ row }">
        <span :class="{ 'check-num': checkField(row, 'macAddr') }">{{ row.macAddr }}</span>
      </template>
      <template #sbgnlx="{ row }">
        <Tooltip :content="row.sbgnlxText" :disabled="!row.sbgnlxText">
          <div :class="{ 'check-num': checkField(row, 'sbgnlx') }" class="width-xs inline ellipsis">
            {{ row.sbgnlxText }}
          </div>
        </Tooltip>
      </template>
      <template #sbdwlx="{ row }">
        <span :class="{ 'check-num': checkField(row, 'sbdwlx') }">{{ row.sbdwlxText }}</span>
      </template>
      <template #sbcjqy="{ row }">
        <Tooltip :content="row.sbcjqyText" :disabled="!row.sbcjqyText">
          <div :class="{ 'check-num': checkField(row, 'sbcjqy') }" class="width-xs inline ellipsis">
            {{ row.sbcjqyText }}
          </div>
        </Tooltip>
      </template>
      <template #phyStatus="{ row }">
        <span
            :style="{
            color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
          }"
        >{{ row.phyStatus | filterType(phystatusList) }}</span
        >
      </template>
      <template #checkStatus="{ row }">
        <div>
          <span
              class="check-status"
              :class="row.checkStatus === '1' ? 'bg-success' : row.checkStatus === '2' ? 'bg-failed' : ''"
          >
            {{ row.checkStatusText }}
          </span>
        </div>
      </template>
      <template #contrastStatus="{ row }">
        <div>
          <span
              class="check-status-font"
              :class="row.contrastStatus === '1' ? '' : row.contrastStatus === '2' ? 'font-success' : 'font-failed'"
          >
            {{ row.contrastStatusText }}
          </span>
        </div>
      </template>
      <template #examineStatus="{ row }">
        <div>
          <span
              class="check-status-font"
              :class="row.examineStatus === '1' ? 'font-success' : row.examineStatus === '2' ? 'font-failed' : ''"
          >
            {{ row.examineStatusText }}
          </span>
        </div>
      </template>
      <template #isOnline="{ row }">
        <div>
          <span
              class="check-status-font"
              :class="row.isOnline === '1' ? 'font-success' : row.isOnline === '2' ? 'font-failed' : ''"
          >
            {{ row.isOnlineText }}
          </span>
        </div>
      </template>

      <template #baseCheckStatus="{ row }">
        <div :class="row.rowClass">
          <span
              class="check-status"
              :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
          >
            {{ row.baseCheckStatus === '1' ? '不合格' : row.baseCheckStatus === '0' ? '合格' : '--' }}
          </span>
        </div>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import {mapGetters} from "vuex";

export default {
  props: {
  },
  data() {
    return {
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          fixed: 'left',
          align: 'center',
          width: 50,
        },
        {
          title: this.global.filedEnum.deviceId,
          slot: 'deviceId',
          fixed: 'left',
          align: 'left',
          minWidth: 210,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.deviceName,
          slot: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          slot: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          slot: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          slot: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          slot: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: `${this.global.filedEnum.sbcjqy}`,
          slot: 'sbcjqy',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },],
      tableData: [],
      loading: false,
    }
  },
  methods: {
    init(list, total, param){
      const {pageNumber, pageSize } = param
      this.searchData = param
      this.pageData.pageSize = pageSize || 10
      this.pageData.pageNum = pageNumber || 1
      this.queryList();
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.queryList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.queryList();
    },
    checkField(row, field) {
      if (this.sign === '1') {
        if (!row.differList) return;
        return row.differList.includes(field);
      } else {
        if (!row.accuracyList) return;
        return row.accuracyList.includes(field);
      }
    },
    async queryList() {
      try {
        this.loading = true;
        const res = await this.$http.post(equipmentassets.getPageDeviceList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },

  },
  computed: {
    ...mapGetters({
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  }
};
</script>
<style scoped lang="less">
.statistic {
  height: 600px;
  margin-bottom: 10px;
  .more {
    color: #2c86f8;
    cursor: pointer;
  }
}
</style>
