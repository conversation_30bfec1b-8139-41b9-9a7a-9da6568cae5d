<template>
  <div class="scroll-div">
    <i class="icon-font icon-xiangzuo arrow left-arrow" @click="leftScroll" v-if="listLength > viewNum"></i>
    <div class="scroll-box" ref="scroll-box">
      <slot name="total"></slot>
      <div class="scroll-content" ref="scroll-content">
        <slot name="item"></slot>
      </div>
    </div>
    <i class="icon-font icon-xiangyou arrow right-arrow" @click="rightScroll" v-if="listLength > viewNum"></i>
  </div>
</template>
<style lang="less" scoped>
.scroll-div {
  display: inline-block;
  position: relative;
  .arrow {
    position: absolute;
    height: 50px;
    line-height: 50px;
    color: #fff;
    font-size: 30px;
    cursor: pointer;
    top: 50%;
    margin-top: -25px;
    z-index: 1;
    &:hover {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
  .left-arrow {
    left: -40px;
  }
  .right-arrow {
    right: -40px;
  }
  .scroll-box {
    display: inline-block;
    overflow-x: hidden;
    overflow-y: hidden;
    .scroll-content {
      white-space: nowrap;
      position: relative;
      left: 0;
      li {
        display: inline-block;
        position: relative;
        text-align: center;
        img {
          height: 100%;
          width: 100%;
        }
      }
      .valid {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
      }
      .is-invalid {
        // background: url("../assets/img/videolib/invalid.png") no-repeat no-repeat;
      }
      .is-effective {
        // background: url("../assets/img/videolib/effective.png") no-repeat no-repeat;
      }
    }
  }
}
</style>
<script>
export default {
  data() {
    return {
      index: 0,
      selectIndex: 0,
      timer: null,
      currentIndex: null, //当前要删除的图片（只在轨迹时有用）
    };
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.$refs['scroll-content'].style.height = parseInt(this.styleItem.height) / 192 + 'rem';
      this.$refs['scroll-box'].style.width =
        (parseInt(this.styleItem.width) * this.viewNum - this.minusWidth) / 192 + 'rem';
    });
  },
  methods: {
    leftScroll() {
      if (this.index > 0) {
        let i = 0;
        this.index -= this.scrollNum;
        if (!this.timer) {
          this.timer = setInterval(() => {
            if (i < parseInt(this.styleItem.width) * this.scrollNum) {
              /**
               * parseFloat(document.documentElement.style.fontSize) / 192 是基于1920 * 1080 屏幕缩放的比例
               * 因为offsetLeft获取的是当前dom的左侧经过缩放后的大小 这里减去的speed还是以缩放前的dom减去的 所以需要先换算为之前的比例 然后在进行rem换算
               *  */

              this.$refs['scroll-content'].style.left =
                (this.$refs['scroll-content'].offsetLeft / (parseFloat(document.documentElement.style.fontSize) / 192) +
                  this.speed) /
                  192 +
                'rem';
              i += this.speed;
            } else {
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 10);
        }
      } else {
        this.$Message.warning('已经到第一张了');
      }
    },
    rightScroll() {
      if (this.index < this.listLength - this.viewNum) {
        let i = 0;
        this.index += this.scrollNum;
        /**
         * (scrollList滚动列表的长度-viewNum能看到的数量)*每个滑块的宽度 = 可滚动的宽度
         * 可滚动的宽度 + 已滚动的左边距 大于0可继续滚动否则不可
         *
         */
        if (!this.timer) {
          this.timer = setInterval(() => {
            if (i < parseInt(this.styleItem.width) * this.scrollNum) {
              /**
               * parseFloat(document.documentElement.style.fontSize) / 192 是基于1920 * 1080 屏幕缩放的比例
               * 因为offsetLeft获取的是当前dom的左侧经过缩放后的大小 这里减去的speed还是以缩放前的dom减去的 所以需要先换算为之前的比例 然后在进行rem换算
               *  */

              this.$refs['scroll-content'].style.left =
                (this.$refs['scroll-content'].offsetLeft / (parseFloat(document.documentElement.style.fontSize) / 192) -
                  this.speed) /
                  192 +
                'rem';
              i += this.speed;
            } else {
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 10);
        }
      } else {
        this.$Message.warning('已经到第最后一张了');
      }
    },
  },
  watch: {},
  computed: {},
  props: {
    /**
     * 图片数组
     */
    listLength: {
      type: Number,
      required: true,
    },
    //显示几张
    viewNum: {
      default: 1,
    },
    //每个滑块的样式可传入高度宽度
    styleItem: {},
    //speed要为每个滑块宽度的整数倍否则可能造成滑动距离不准确 /ms
    speed: {
      default: 1,
    },
    // 滑动的张数
    scrollNum: {
      default: 1,
    },
    // 自定义需要减去的总宽度
    minusWidth: {
      default: 0,
    },
  },
  components: {},
};
</script>
