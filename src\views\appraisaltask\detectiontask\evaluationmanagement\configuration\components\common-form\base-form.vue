<script>
const props = {
  /**
   * 模式： edit 编辑 configuration 配置
   */
  formModel: {
    required: true,
    type: String,
    default: 'add',
  },
  /* 表单 */
  formData: {
    required: true,
    type: Object,
    default: () => {},
  },
  moduleAction: {
    type: Object,
    default: () => {},
  },
  labelWidth: {
    type: Number,
    default: 180,
  },
  // 配置详情
  taskIndexConfig: {
    type: Object,
    default: () => ({}),
  },
};
import { mapActions, mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import fieldData from './field';
export default {
  name: 'base-form',
  components: {
    ChooseCatalogue: require('@/business-components/choose-catalogue.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    CustomDetectionMode: require('.././custom-detection-mode.vue').default, // 选择设备
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    TestPlan: require('.././test-plan.vue').default,
    OrgModal: require('.././org-modal/index.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device').default,
    AreaSelect: require('@/components/area-select').default,
    regionalizationSelect: require('@/views/appraisaltask/indexmanagement/components/regionalization-select').default,
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
    SpotCheck: require('./spot-check/index.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
  props: props,
  data() {
    // const validateOrg = (rule, value, callback) => {
    //   if (this.formData.orgList.length === 0 && this.formData.detectMode === '2') {
    //     callback(new Error('请选择组织机构'))
    //   } else {
    //     callback()
    //   }
    // }
    const validatePlace = (rule, value, callback) => {
      if (
        this.formData.deviceQueryForm.filter.placeIds.length === 0 &&
        this.formData.deviceQueryForm.filter.checkDeviceFlag === '0'
      ) {
        callback(new Error('请选择场所数据'));
      } else {
        callback();
      }
    };
    const validateNum = (rule, value, callback) => {
      if (value === '') {
        callback();
      } else {
        const reg = new RegExp('^([1-9][\\d]*|0)(\\.[\\d]+)?$');
        !reg.test(value) ? callback(new Error('请输入正整数或者小数')) : callback();
      }
    };
    // const validateCron = (rule, value, callback) => {
    //   if ((Array.isArray(value) && value.length === 0) || value == undefined) {
    //     callback(new Error('请选择检测计划'))
    //   } else {
    //     callback()
    //   }
    // }
    return {
      SMODE_DEVICETAG: this.global.STATISTICAL_MODAL.SMODE_DEVICETAG, //统计模式：设备标签
      SMODE_REGION: this.global.STATISTICAL_MODAL.SMODE_REGION, //统计模式：行政区划
      SMODE_ORG: this.global.STATISTICAL_MODAL.SMODE_ORG, //统计模式：组织机构
      styles: {
        width: '5rem',
      },
      visible: false,
      loading: false,
      tableData: [],
      pageData: {
        pageNumber: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchConditions: {
        placeName: '',
        placeAlias: '',
        regionCodeList: [],
        placeIds: [],
        sbcjqyList: [],
      },
      leftData: (parameter) => {
        return this.$http
          .post(equipmentassets.placeManagerList, {
            ...this.searchConditions,
            ...parameter,
          })
          .then((res) => {
            return res.data;
          });
      },
      defaultFormData: {}, // 默认配置的formData
      defaultProps: { label: 'regionName', children: 'children' },
      areaSelectModalVisible: false,
      objectList: [],
      selectAreaTree: {
        regionCode: '',
      },
      categoryObj: {},
      dataObject: {
        regionCodeList: [],
        checkDeviceFlag: '',
        placeName: '',
        placeAlias: '',
        sbcjqyList: [],
        placeIds: [],
        placeData: [],
      },
      deviceSelectionType: '2',
      ruleValidate: {
        detectMode: [{ required: true, message: '请选择检测方式', trigger: 'change' }],
        regionCode: [{ required: true, message: '请选择检测对象', trigger: 'change' }],
        directoryNames: [
          {
            required: true,
            message: '请填写目录',
            trigger: 'change',
            type: 'array',
          },
        ],
        placeData: [
          {
            validator: validatePlace,
            required: true,
            trigger: 'blur',
            type: 'array',
          },
        ],
        deviceNum: [
          {
            required: true,
            message: '请输入抽取设备数量',
            trigger: 'blur',
            type: 'number',
          },
        ],
        captureNum: [
          {
            required: true,
            message: '请输入抽取图片数量',
            trigger: 'blur',
            type: 'number',
          },
        ],
        algVendors: [
          {
            required: true,
            message: '请选择算法厂商',
            trigger: 'change',
            type: 'array',
          },
        ],
        rule: [
          {
            required: true,
            message: '请选择检测规则',
            trigger: 'change',
            type: 'array',
          },
        ],
        relateDataType: [
          {
            required: true,
            message: '请选择匹配设备数据范围',
            trigger: 'change',
          },
        ],
        excessCoefficientValue: [{ validator: validateNum, trigger: 'blur' }],
        excessTotalValue: [{ validator: validateNum, trigger: 'blur' }],
        statisticalModel: [
          {
            required: true,
            type: 'array',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value.length) {
                callback(new Error('请选择至少一个统计模式'));
              } else if (value.includes(3) && this.formData.tagIds?.length === 0) {
                callback(new Error('请选择待统计的设备标签'));
              } else {
                callback();
              }
            },
          },
        ],
      },
      areaTreeData: [],
      flatTreeData: [],
      orgCodes: [],
      algorithm: [],
      parameters: {}, // 设备选择组件回显参数
      // orgList: [], //组织机构回显参数
      regionalizationSelectVisible: false,
      orgModalVisible: false,
      regionSelectSyles: {},
      uncheckDevStatusIndexVisible: ['FACE_ASSET_REGISTER', 'FOCUS_TRACK_URL_AVAILABLE'],
      ...fieldData.data(),
      dependIndexList: [], // 依赖指标的info值
      needDependList: [], // 存在依赖的指标 -- 即：检测方式 ---  从【xxx指标】检测结果统计
      //选择标签
      addTagVisible: false,
      tagFilterAttrs: {
        allTagList: [],
        customizeAction: {
          title: '添加设备标签',
          leftContent: '选择设备标签及排序',
          rightContent: '设备标签显示',
          moduleStyle: {
            width: '70%',
          },
        },
        contentStyle: {
          height: `${500 / 192}rem`,
        },
        fieldTagName: {
          id: 'tagId',
          value: 'tagName',
        },
      },
    };
  },
  computed: {
    handleWayList() {
      let wayArr = []; // 创建一个新数组
      if (!this.unAllCheck.includes(this.moduleAction.indexType)) {
        // 判断当前指标是否需要全检
        wayArr.push(this.wayList.find((item) => item.type === 'all'));
      }
      if (!this.unSpotCheck.includes(this.moduleAction.indexType) && !['1'].includes(this.moduleAction.indexModule)) {
        // 判断当前指标是否需要抽检
        wayArr.push(this.wayList.find((item) => item.type === 'spot'));
      }
      if (!this.unCustom.includes(this.moduleAction.indexType)) {
        // 判断当前指标是否需要自定义
        wayArr.push(this.wayList.find((item) => item.type === 'custom'));
      }
      if (this.needDependList.length > 0) {
        this.needDependList.forEach((item) => {
          let dependArr = this.dependIndexList.filter((depentItem) => depentItem?.indexId === item.indexId);
          let obj = {
            ...item,
            label: item.optionsLabel,
            value: '4',
            type: 'result',
            disabled: !dependArr[0] || (dependArr[0] && dependArr[0].detectMode === '4'), // 存在依赖关系的指标，只能有一个选择，不允许互相依赖， 并且确保当前任务 同时存在 该两个指标
          };
          wayArr.push(obj);
        });
      }
      return wayArr;
    },
    handleWayDisabled() {
      const disabledIndexIds = ['7', '8', '6'];
      return (
        disabledIndexIds.includes(this.moduleAction.indexModule) ||
        this.disabledWay.includes(this.moduleAction.indexType)
      );
    },
    // 是否展示达标数量设置
    standardQuantityVisible() {
      if (!this.upStandardQuantity.includes(this.moduleAction.indexType)) return false;
      return this.moduleAction.indexType === 'VIDEO_MONITOR_VALID_SUBMIT_QUANTITY_RATE' &&
        this.formData.detectMode !== '1'
        ? false
        : true;
    },
    assessmentItems() {
      const assessmentObj = this.assessmentList.find((item) => item.indexType === this.moduleAction.indexType);
      this.regionSelectSyles = assessmentObj.regionSelectSyles;
      return assessmentObj.data;
    },
    activeLabelWidth() {
      if (this.formData.detectMode === '2') return 190;
      return this.labelWidth;
    },
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
    defaultOrgList() {
      let { orgCountMap } = this.formData;
      return (orgCountMap && Object.keys(orgCountMap)) || [];
    },
    computedChooseDeviceTagIds() {
      if (this.formData.statisticalModel?.includes(this.SMODE_DEVICETAG)) {
        return [...this.formData.tagIds] || [];
      } else {
        return [];
      }
    },
  },
  watch: {
    moduleAction: {
      async handler(val) {
        this.defaultFormData = this.$util.common.deepCopy(this.formData);
        !('orgCountMap' in this.defaultFormData) ? (this.defaultFormData.orgCountMap = {}) : null;
        if ([3003, 3004, 3006, 3007, 2003, 5002, 6002, 6004, 2026].includes(val.indexId)) {
          this.governanceevaluation(val.indexId);
        }
        if (val) {
          this.selectAreaTree.regionCode = val.regionCode;
          this.setDefaultStatisticalModel();
          await this.getCheckTaskIndexConfig();
          this.getTagList();
        }
      },
      immediate: true,
      deep: true,
    },
    'formData.directoryNames': {
      handler(val) {
        if (val.length > 0) {
          this.$nextTick(() => {
            this.$refs['formValidate'].validateField('directoryNames');
          });
        }
      },
    },
    'formData.relateDataType': {
      handler(val) {
        if (val.length > 0) {
          this.$nextTick(() => {
            this.$refs['formValidate'].validateField('relateDataType');
          });
        }
      },
    },
    'formData.selectType': {
      handler(newVal) {
        // 切换到按目录
        if (newVal === 0) {
          this.$emit('updateFormData', {
            ...this.formData,
            deviceQueryForm: {},
          });
          this.parameters = {};
        }
        // 按设备 清楚目录之前选择的
        if (newVal === 1) {
          this.$emit('updateFormData', {
            ...this.formData,
            directoryNames: [],
            directoryIds: [],
          });
        }
      },
    },
    // 检测方式切换
    'formData.detectMode': {
      handler(newVal) {
        const updateObj = {
          ...this.formData,
        };
        if (newVal === '1') {
          updateObj.directoryIds = [];
          updateObj.directoryNames = [];
        }
        if (['1', '2'].includes(newVal)) {
          updateObj.deviceQueryForm = {};
          updateObj.deviceNum = null;
        }
        if (['1', '3'].includes(newVal)) {
          updateObj.orgCodes = '';
          updateObj.deviceNum = null;
          updateObj.orgList = [];
        }
        if (this.needCaptureNum.includes(this.moduleAction.indexType)) {
          updateObj.deviceQueryForm = {};
          updateObj.captureNum = null;
        }
        this.$emit('updateFormData', updateObj);
      },
    },
    'formData.tagIds': {
      handler(val) {
        if (val?.length > 0 && this.needStatisticMode.includes(this.moduleAction.indexType)) {
          this.$nextTick(() => {
            this.$refs['formValidate'].validateField('statisticalModel');
          });
        }
      },
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          const {
            directoryNames: customAreaNames,
            directoryIds: checkIds,
            selectType,
            detectMode,
            deviceQueryForm,
            // orgCodes,
          } = this.formData;
          this.categoryObj = {
            customAreaNames,
            checkIds,
          };
          // this.orgList = orgCodes ? orgCodes.split(',') : []
          // 抽检获取组织机构
          if (detectMode === '2') {
            this.getRegioncode();
          }
          // 选择设备
          if (selectType === 1) {
            this.parameters = {
              ...deviceQueryForm,
            };
          }
        }
        if (
          this.moduleAction.indexType === 'FACE_PLATFORM_ONLINE_RATE' ||
          this.moduleAction.indexType === 'VEHICLE_PLATFORM_ONLINE_RATE' ||
          this.moduleAction.indexType === 'VIDEO_PLATFORM_ONLINE_RATE'
        ) {
          this.formData.detectMode = '3';
          this.$emit('handleDetectMode', this.selectAreaTree.regionCode);
        }
        if (this.moduleAction.indexType === 'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN') {
          this.formData.detectMode = '3';
        }
        this.getDependentParentIndexIds();
      },
      immediate: true,
    },
    visible(val) {
      if (val) this.getRegioncode();
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    updateParams(params) {
      Object.assign(this.formData, params);
      Object.assign(this.defaultFormData, params);
    },
    //给摄像机功能类型设置默认值
    getSxjgnlx(indexType) {
      let indexTypeOjb = {
        FACE_VALID_SUBMIT_QUANTITY: '3',
        VEHICLE_VALID_SUBMIT_QUANTITY: '2',
        VIDEO_VALID_SUBMIT_QUANTITY: '1',
        FACE_ACCURACY: '3',
        VEHICLE_ACCURACY: '2',
        VIDEO_ACCURACY: '1',
      };
      return indexTypeOjb[indexType] || '';
    },
    // 人脸/车辆/视频流 卡口有效报送数量达标率 不支持按目录选择
    isDir(indexType) {
      return ![
        'FACE_VALID_SUBMIT_QUANTITY',
        'VEHICLE_VALID_SUBMIT_QUANTITY',
        'VIDEO_VALID_SUBMIT_QUANTITY',
        'FACE_ACCURACY',
        'VEHICLE_ACCURACY',
        'VIDEO_ACCURACY',
      ].includes(indexType);
    },
    resetSearch() {
      this.searchConditions = {
        placeName: '',
        placeAlias: '',
        regionCodeList: [],
        sbcjqyList: [],
        placeIds: [],
      };
      this.$refs.ChooseDevice.search();
    },
    confirmArea(val) {
      this.searchConditions.sbcjqyList = val;
    },
    clickPlace() {
      this.$refs.ChooseDevice.init();
    },
    getDeviceIdList({ chooseIds, selectedDevNum, checkDeviceFlag }) {
      let data = {
        checkDeviceFlag: checkDeviceFlag,
        placeName: this.searchConditions.placeName,
        placeAlias: this.searchConditions.placeAlias,
        sbcjqyList: this.searchConditions.sbcjqyList,
        placeIds: chooseIds,
        totalCount: selectedDevNum,
      };
      this.$emit('updateFormData', {
        ...this.formData,
        deviceQueryForm: {
          filter: {
            ...data,
          },
        },
      });
    },
    getOrgCode(val) {
      this.searchConditions.regionCodeList = val;
      this.$refs.ChooseDevice.search();
    },
    changePage(val) {
      this.pageData.pageNumber = val;
      this.initList();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.pageData.pageNumber = 1;
      this.initList();
    },
    handleRadioChange(val) {
      if (val === '1') {
        this.searchConditions = {
          checkDeviceFlag: '0',
          placeName: '',
          placeAlias: '',
          placeIds: [],
          sbcjqyList: [],
        };
      }
    },
    search() {
      this.$refs.ChooseDevice.search();
    },
    async initList() {
      try {
        this.loading = false;
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.placeManagerList, this.pageData);
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total || 0;
      } catch (e) {
        console.log(e);
      }
    },
    handleChangeObject() {},
    changeDeviceType(val) {
      if (val === 0) {
        this.$emit('updateFormData', {
          ...this.formData,
          deviceQueryForm: {},
        });
      } else {
        this.$emit('updateFormData', {
          ...this.formData,
          directoryNames: [],
          directoryIds: [],
        });
      }
    },
    handleChangeWay(val) {
      if (val === '2' && this.areaTreeData.length === 0) {
        this.getRegioncode();
      }
      if (val === '1' || val === '3') {
        this.orgCodes = [];
      }
      if (val === '1') {
        this.$emit('updateFormData', {
          ...this.formData,
          deviceQueryForm: {},
          directoryNames: [],
          directoryIds: [],
        });
      }
      if (val === '3') {
        this.formData.selectType = 1;
      }
      this.$emit('handleDetect', val);
    },
    getRegioncode() {
      this.$http
        .get(governanceevaluation.getOrgDataByRegioncode, {
          params: { regioncode: this.selectAreaTree.regionCode },
        })
        .then((res) => {
          if (res.data.data) {
            // 过滤 - 0默认 1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所 10其他
            let flatTreeData = res.data.data.filter((item) => {
              return item;
              return [0, 1, 2, 3, 4, 5, 6].includes(item.regionType);
            });
            this.flatTreeData = flatTreeData;
            this.areaTreeData = this.$util.common.arrayToJson(flatTreeData, 'id', 'parentId');
          }
        });
    },
    // 检测时间
    checkTime(val) {
      this.$set(this.formData, 'cronData', val.cronData);
      this.$emit('updateFormData', {
        ...this.formData,
        ...val,
      });
      this.$nextTick(() => {
        this.$refs['formValidate'].validateField('cronData');
      });
    },
    // 选中视频数据范围
    changeDataType() {
      this.$emit('updateFormData', {
        ...this.formData,
      });
    },
    // 场所数据
    getPlaceList({ placeIds }) {
      this.$emit('updateFormData', {
        ...this.formData,
        ...placeIds,
      });
    },
    // 选择组织机构
    chooseOrg() {
      this.orgModalVisible = true;
    },
    // 抽检：统一配置抽检设备数量
    queryCommonConfigOrg(data) {
      this.formData.orgCountMap = {};
      data.forEach((item) => {
        this.formData.orgCountMap[item] = null;
      });
      this.orgCodes = data;
      // this.orgList = this.orgCodes
      this.$emit('updateFormData', {
        ...this.selectAreaTree,
        ...this.formData,
        orgCodes: this.orgCodes.toString(),
      });
      this.$nextTick(() => {
        this.$refs['formValidate'].validateField('orgList');
      });
    },
    selectedAreaTree() {},
    // 按目录选择
    getDevCategoryData({ customAreaNames: directoryNames, checkIds: directoryIds }) {
      this.$emit('updateFormData', {
        ...this.formData,
        directoryNames,
        directoryIds,
      });
    },
    async handleSubmit() {
      return await this.$refs['formValidate'].validate();
    },
    // 设备选择参数
    getDeviceQueryForm(val) {
      this.$emit('updateFormData', {
        ...this.formData,
        deviceQueryForm: {
          ...val,
        },
      });
    },
    // 算法接口
    async governanceevaluation(indexId) {
      try {
        await this.getAlldicData();
        let res = await this.$http.get(governanceevaluation.newGetOptionAlgVendors, {
          params: { indexId: indexId },
        });
        this.algorithm = res.data.data || [];
        this.algorithm.forEach((item) => {
          item.algorithmLable = this.getAlgorithmLabel(item.algorithmVendorType);
        });
      } catch (err) {
        console.log(err);
      }
    },
    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },
    quantityQuery(data) {
      this.formData.quantityData = data;
    },
    async getCheckRule(indexType) {
      try {
        let res = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType },
        });
        let data = res.data.data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.$set(this.formData, 'ruleList', data);
      } catch (err) {
        console.log(err);
      }
    },
    handleDetectPhyStatus() {
      this.$emit('updateFormData', { ...this.formData });
    },
    // 指标之间是否已存在依赖关系
    async getCheckTaskIndexConfig() {
      let { indexId, taskSchemeId } = this.moduleAction;
      try {
        let data = {
          indexId: indexId,
          detectMode: '4',
          taskSchemeId: taskSchemeId,
        };
        let res = await this.$http.post(governanceevaluation.checkTaskIndexConfig, data);
        this.dependIndexList = res.data.data || [];
      } catch (error) {
        console.log(error);
      }
    },
    // 获取已配置的依赖列表
    async getDependentParentIndexIds() {
      let { indexId } = this.moduleAction;
      try {
        let res = await this.$http.get(`${governanceevaluation.getDependentParentIndexIds}/${indexId}`);
        if (res.data.data) {
          Object.keys(res.data.data).forEach((key) => {
            let obj = {
              indexId: Number(key),
              indexName: res.data.data[key].split('【')?.[1].split('】')?.[0],
              optionsLabel: res.data.data[key],
            };
            this.needDependList.push(obj);
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
    // option点击触发
    clickOption(optItem) {
      if (optItem.value === '4' && optItem.disabled) {
        let { indexName } = this.moduleAction;
        let indexInfo = this.needDependList.filter((item) => item.indexId === optItem.indexId);
        let dependArr = this.dependIndexList.filter((depentItem) => depentItem?.indexId === optItem.indexId);
        if (indexInfo[0]) {
          let text = !dependArr[0]
            ? `当前检测任务下，不存在【${indexInfo[0].indexName}】，请选择其他检测方式！`
            : dependArr[0] && dependArr[0].detectMode === '4'
            ? `【${indexInfo[0].indexName}】已选择 “从【${indexName}】检测结果统计” 的检测方式，请选择其他检测方式！`
            : '';
          this.$Message.warning(text);
        }
      } else if (optItem.value === '4' && !optItem.disabled) {
        this.$emit('updateFormData', {
          ...this.formData,
          depIndexId: optItem.indexId,
        });
      } else if (optItem.value !== '4') {
        this.$emit('updateFormData', {
          ...this.formData,
          depIndexId: null,
        });
      }
    },
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagFilterAttrs.allTagList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 添加统计标签
    async addStatisticTag() {
      this.addTagVisible = true;
    },
    //确认选择的标签，若该指标未配置，则默认选中：自定义-按设备选择，且弹窗内自动设备标签筛选
    async confirmTagFilter(val) {
      try {
        const tagIdList = val.map((item) => item.tagId);
        this.formData.tagIds = [...tagIdList];
        this.addTagVisible = false;
        this.setDeviceChooseByTag();
      } catch (err) {
        console.log(err);
      }
    },
    //指标设置选中：自定义-按设备选择，且弹窗内自动设备标签筛选
    async setDeviceChooseByTag() {
      if (this.moduleAction.config) {
        return;
      }
      // 1、检测方式-自定义选择
      this.formData.detectMode = '3';
      this.handleChangeWay(this.formData.detectMode);
      if (this.unNeedChooseDevice.includes(this.moduleAction.indexType)) {
        return;
      }
      // 2、选择设备-按设备选择
      this.formData.selectType = 1;
      this.changeDeviceType(this.formData.selectType);
    },
    //设置默认的统计模式
    setDefaultStatisticalModel() {
      //指标未配置，继承任务的统计模式
      if (!this.formData.statisticalModel) {
        if (this.moduleAction.statisticalModelBo) {
          let models = this.moduleAction.statisticalModelBo.statisticalModel;
          this.formData.statisticalModel = this.$util.common.deepCopy(models);
        } else {
          this.formData.statisticalModel = [this.SMODE_REGION, this.SMODE_ORG];
        }
      }
      if (!this.formData.tagIds) {
        if (this.moduleAction.statisticalModelBo) {
          let tags = this.moduleAction.statisticalModelBo.tagIds;
          this.formData.tagIds = this.$util.common.deepCopy(tags);
        } else {
          this.formData.tagIds = [];
        }
      }
    },
    //勾选设备标签且未选择标签时，默认打开选择标签弹窗
    changeStatisticalModalGroup(group) {
      const oldStatisticalModel = [...this.formData.statisticalModel];
      this.formData.statisticalModel = group;
      let isChangeDeviceTag =
        group.includes(this.SMODE_DEVICETAG) && !oldStatisticalModel.includes(this.SMODE_DEVICETAG);
      if (isChangeDeviceTag && !this.formData.tagIds?.length) {
        this.addStatisticTag();
      }
    },
  },
};
</script>
