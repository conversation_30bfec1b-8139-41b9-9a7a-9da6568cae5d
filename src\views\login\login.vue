<template>
  <div v-if="isReload" class="login-bg">
    <div class="about-btn">
      <a href="/product.html">
        <Icon type="ios-help-circle-outline mr-5" />帮助
      </a>
    </div>
    <div id="loginGif" class="loginGif"></div>

    <div class="video-container">
      <div :style="fixStyle" class="filter">
        <div class="logo">
          <img
            class="img-logo"
            v-if="applicationInfo.logoUrl"
            :src="applicationInfo.logoUrl"
            alt=""
          />
          <img class="img-logo" v-else src="@/assets/img/logo-img.png" alt="" />
          <span class="title">{{
            applicationInfo.applicationName || "启数感知大数据平台"
          }}</span>
          <!-- <img class="logo-img" src="@/assets/img/login/logo1.png" alt="" /> -->
        </div>
        <div class="login-wrapper">
          <div class="main">
            <div class="login-text">登录</div>
            <div class="ui-tab">
              <div v-if="tabList.length" class="list">
                <div
                  v-for="(item, index) in tabList"
                  :key="index"
                  :class="{ active: item.value === tabActive }"
                  class="ivu-tabs-tab"
                  @click="tabClick(item)"
                >
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </div>
            <Form ref="loginForm" :model="loginForm" class="login-form">
              <FormItem prop="username">
                <Input
                  :placeholder="`请输入${typeList[tabActive - 1].label}`"
                  v-model="loginForm.username"
                  :autofocus="!passwordFocus"
                  type="text"
                  clearable
                  class="login-input"
                  @on-enter="userEnter"
                >
                  <i
                    slot="prefix"
                    :class="`${typeList[tabActive - 1].iconName} iconfont`"
                  ></i>
                </Input>
              </FormItem>
              <FormItem prop="password">
                <Input
                  ref="password"
                  :type="passwordType"
                  v-model="loginForm.password"
                  :autofocus="passwordFocus"
                  placeholder="请输入密码"
                  class="login-input"
                  @on-enter="submit()"
                >
                  <i slot="prefix" class="iconfont icon-mima"></i>
                  <i
                    slot="suffix"
                    :class="`${iconPassword} iconfont font-16 auxiliary-color`"
                    @click="flag = !flag"
                  ></i>
                </Input>
              </FormItem>
              <FormItem
                class="login"
                style="margin-top: 30px; background: transparent !important"
              >
                <div class="btn loginBtn" @click="submit()">
                  {{ loading ? "登录中..." : "登 录" }}
                </div>
              </FormItem>
            </Form>
            <!-- <div class="bottom_kpi">
              <i class="iconfont icon-pki"></i>
              <span>PKI登录</span>
            </div> -->
          </div>
        </div>
        <div class="footer" v-if="isShowCompanyInfo == '1'">
          <div class="login-logo">
            <img src="/logo.png" alt="" />
          </div>
          <div class="line"></div>
          <div class="copyright">
            <p>版本号: V{{ version }} 推荐浏览器: Chrome 50 及以上版本</p>
             <p v-for="(item,index) in copyright" :key="index">{{ item }}</p>
          </div>
        </div>
      </div>

      <div class="poster hidden" v-if="!vedioCanPlay">
        <img
          :style="fixStyle"
          src="@/assets/img/login/light-login-bg.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>
<script>
import {
  getLoginType,
  getParamDataByKeys,
  checkSafeExist,
  bindIpMac,
} from "@/api/user.js";
import { getUserInfo } from "@/libs/configuration/util.permission";
import config from "../../../package.json";
import { mapActions, mapGetters } from "vuex";
import loginJson from "./json/login-json";
import md5 from "md5";

export default {
  data() {
    return {
      copyright: window.copyright || [],
      vedioCanPlay: false,
      fixStyle: "",
      loading: false,
      isShowCompanyInfo: "0", // 是否展示公司信息
      loginForm: {
        username: "",
        password: "",
      },
      passwordFocus: false,
      queryUser: {},
      tabList: [],
      tabActive: 1,
      typeList: [
        { label: "用户名", value: "1", iconName: "icon-yonghu" },
        // { label: '身份证', value: '2', iconName: 'icon-shenfenzheng1' },
        // { label: '警号', value: '3', iconName: 'icon-security-shield-full' }
      ],
      flag: false, //密码眼睛
      isReload: true, // 默认不显示页面内容
      selectedMacIndex: 0,
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name) {
        // window.location.reload();
      } else {
        vm.isReload = true;
      }
    });
  },
  computed: {
    ...mapGetters({
      applicationInfo: "applicationInfo",
    }),
    version() {
      return config.version.split("-")[0];
    },
    isWithDbPswd() {
      return !!this.queryUser.username && !!this.queryUser.password;
    },
    passwordType() {
      return this.flag ? "text" : "password";
    },
    iconPassword() {
      return this.flag ? "icon-eye" : "icon-eye-close";
    },
  },

  mounted() {
    this.$nextTick(() => {
      this.appInfo();
    });
    // 初始化渲染数仓中间gift图
    var params = {
      container: document.getElementById("loginGif"),
      renderer: "svg",
      loop: true,
      autoplay: true,
      animationData: loginJson,
    };
    lottie.loadAnimation(params);

    this.getLoginTypeList();
    this.canplay();
    this.onResizeHandler();
    this.getParamDataByKeys();
  },
  methods: {
    ...mapActions({
      appInfo: "appInfo",
    }),
    canplay() {
      this.vedioCanPlay = true;
    },
    userEnter() {
      this.$refs.password.focus();
    },
    getLoginTypeList() {
      getLoginType().then((res) => {
        const { paramValue } = res.data;
        this.tabList = this.typeList.filter((item) => {
          return paramValue.includes(item.value);
        });
        this.tabActive = this.tabList[0].value;
      });
    },
    // 获取是否展示公司信息函数
    getParamDataByKeys() {
      getParamDataByKeys({ key: "ISSHOW_COMPANY_INFORMATION" }).then((res) => {
        this.isShowCompanyInfo = res.data.paramValue;
      });
    },
    async tabClick(item) {
      this.tabActive = item.value;
      this.$refs.loginForm.resetFields();
    },
    async submit() {
      // 检查插件
      /* let result = await this.checkVersion();
      if (!result) return; */
      try {
        if (this.loginForm.username === "" || this.loginForm.password === "") {
          this.$Message.warning("用户名或密码不能为空");
          return;
        }
        if (this.loginForm.username.length > 30) {
          this.$Message.warning("用户名长度不能大于30位");
          return;
        }
        if (
          this.loginForm.password.length > 16 ||
          this.loginForm.password.length < 6
        ) {
          this.$Message.warning("密码长度不能大于16位或者小于6位");
          return;
        }
        if (this.loading) return false;
        // 校验mac
        /* let flag = await this.checkSafeExist();
        if (!flag) return; */
        this.loading = true;
        this.$store
          .dispatch("handleLogin", {
            ...this.loginForm,
            username: `${this.tabActive},${this.loginForm.username}`,
          })
          .then((data) => {
            if (data.tips) {
              this.$Notice.warning({
                title: "友情提示",
                desc: data.tips.replaceAll("&*&*#", "<br/>"),
              });
            }
            this.$Message.success("登录成功");
            localStorage.setItem("showModal", 1);
            localStorage.setItem("loginInfo", JSON.stringify(this.loginForm));
            const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,16}$/;
            if (!reg.test(this.loginForm.password)) {
              getUserInfo("information", this.$route);
            } else {
              getUserInfo("from-login", this.$route);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } catch (err) {
        this.$Message.error("登录失败");
        this.loading = false;
        console.error(err);
      }
    },
    onResizeHandler() {
      window.onresize = () => {
        const windowWidth = document.body.clientWidth;
        const windowHeight = document.body.clientHeight;
        const windowAspectRatio = windowHeight / windowWidth;
        let videoWidth;
        let videoHeight;
        if (windowAspectRatio < 0.5625) {
          videoWidth = windowWidth;
          videoHeight = videoWidth * 0.5625;
          this.fixStyle = {
            height: windowWidth * 0.5625 + "px",
            width: windowWidth + "px",
            "margin-bottom": (windowHeight - videoHeight) / 2 + "px",
            "margin-left": "initial",
          };
        } else {
          videoHeight = windowHeight;
          videoWidth = videoHeight / 0.5625;
          this.fixStyle = {
            height: windowHeight + "px",
            width: windowHeight / 0.5625 + "px",
            "margin-left": (windowWidth - videoWidth) / 2 + "px",
            "margin-bottom": "initial",
          };
        }
      };
      window.onresize();
    },
    // 版本
    /* checkVersion() {
      let _this = this;
      return new Promise((resolve) => {
        var issupport = H5Player.isSupported();
        if (!issupport.mseLiveH264Playback) {
          alert("不支持H5Player!!");
          return;
        }
        let jssdkver = H5Player.version;
        let servicever;
        H5Player.setupversion(function (res) {
          if (!res.isexist) {
            // _this.$Message.error('请安装播放器后重新登录。')
            // const a = document.createElement('a')
            // // a.href = process.env.BASE_URL + 'zipns 1.0.3.exe'
            // a.href = process.env.BASE_URL + 'H5VPSetup_3.0.7.exe'
            // a.click()

            _this.$Modal.confirm({
              title: "提示",
              closable: true,
              content: `请安装播放器后重新登录。`,
              onOk: () => {
                const a = document.createElement("a");
                a.href = process.env.BASE_URL + "QSPlayerSetup.exe";
                a.click();
              },
            });

            resolve(false);
          }
          if (res.setupver) {
            servicever = res.setupver;
            // console.log('ziplayer v' + jssdkver + '    ziplayer native service v' + servicever)
          }
          resolve(true);
        });
      });
    }, */
    // 检查是否绑定mac
    /* checkSafeExist() {
      let _this = this;
      this.selectedMacIndex = 0;
      return new Promise((resolve) => {
        // 获取mac
        H5Player.getMacString((result) => {
          let macipObj = result.value[0];
          checkSafeExist({
            loginIp: macipObj.ip,
            loginMac: macipObj.mac,
            username: _this.loginForm.username,
          }).then((res) => {
            if (res.code == 200) {
              resolve(true);
            } else if (res.code == 300) {
              // 未绑定
              _this.$Message.error(res.msg);
              _this.$Modal.confirm({
                title: "提示",
                closable: true,
                // content: `您尚未绑定客户端IP和MAC!请选择要绑定的IP/MAC${result.value[0]}`,
                render: (h) => {
                  return h("div", { class: "macBox" }, [
                    h(
                      "div",
                      { class: "macBoxTip" },
                      "您尚未绑定客户端IP和MAC!请选择要绑定的IP/MAC"
                    ),
                    ...result.value.map((item, index) => {
                      return h("label", [
                        h("input", {
                          attrs: {
                            type: "radio",
                            value: index,
                          },
                          domProps: {
                            checked: _this.selectedMacIndex === index,
                          },
                          on: {
                            change: (event) => {
                              _this.selectedMacIndex = parseInt(
                                event.target.value
                              );
                            },
                          },
                        }),
                        `IP/MAC: ${item.ip}/${item.mac}`,
                      ]);
                    }),
                  ]);
                },
                okText: "绑定",
                onOk: () => {
                  bindIpMac({
                    loginIp: result.value[_this.selectedMacIndex].ip,
                    loginMac: result.value[_this.selectedMacIndex].mac,
                    username: _this.loginForm.username,
                    password: md5(_this.loginForm.password),
                  }).then((res2) => {
                    if (res2.code == 200) {
                      resolve(true);
                    } else {
                      _this.$Message.error(res2.msg);
                      resolve(false);
                    }
                  });
                },
                onCancel: () => {
                  resolve(false);
                },
              });
            } else {
              _this.$Message.error(res.msg);
              resolve(false);
            }
          });
        });
      });
    }, */
  },
};
</script>
<style lang="less" scoped>
.loginGif {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 93%;
}
.login-bg {
  position: relative;
  height: 100vh;
  overflow: hidden;
  // height: 100%;
  // width: 100%;
  background: url("~@/assets/img/login/bg.png") center/cover;
  .about-btn {
    position: absolute;
    cursor: pointer;
    z-index: 99;
    right: 40px;
    top: 20px;
    font-size: 14px;
    a {
      color: white;
    }
  }
  .video-container {
    position: relative;
    height: 100vh;
    overflow: hidden;
  }
  // .video-container video,
  .video-container .poster img {
    z-index: 0;
    position: absolute;
  }
  .video-container .filter {
    z-index: 1;
    position: absolute;
  }
  .fillWidth {
    width: 100%;
  }

  .logo {
    position: absolute;
    top: 2%;
    left: 2%;
    color: #ffffff;
    padding-top: 19px;
    padding-bottom: 7px;
    display: flex;
    padding-left: 23px;
    justify-content: flex-start;
    border-radius: 0px 45px 45px 0px;
    .logo-title {
      margin-left: 13px;
      line-height: 54px;
      font-size: 42px;
      font-family: "Microsoft YaHei";
      font-weight: bold;
    }
    .img-logo {
      // width: 80px;
      height: 82px;
    }
    .title {
      font-size: 55px;
      margin-left: 20px;
      font-weight: bold;
      letter-spacing: 16px;
    }
  }
  // 外边框css 开始
  .login-wrapper {
    width: 1080px;
    height: 740px;
    padding-top: 50px;
    text-align: center;
    position: absolute;
    top: 11%;
    right: 0;
    .main {
      width: 521px;
      height: 482px;
      padding: 38px 61px 0 61px;
      overflow: hidden;
      margin-left: 29%;
      margin-top: 5%;
      background: url("~@/assets/img/login/form-bg.png");
      background-size: 100%;
      // background: #FFFFFF;
      // box-shadow: 0px 8px 27px 0px rgba(2, 10, 34, 0.15);
      // box-shadow: 0 0 30px #385687;
      border-radius: 10px;
      .login-text {
        font-size: 28px;
        font-family: "Microsoft YaHei";
        font-weight: bold;
        color: #fff;
        line-height: 32px;
        margin-bottom: 40px;
        margin-top: 20px;
      }
      .ui-tab {
        line-height: 34px;
        font-size: 14px;
        font-family: "Microsoft YaHei";
        color: #fff;
        overflow: hidden;
        margin-left: -12px;
        // cursor: pointer;
        margin-bottom: 16px;
        .list {
          overflow: hidden;
          float: left;
          .ivu-tabs-tab {
            float: left;
            padding: 0 15px;
            &:hover {
              color: #248afc;
              font-weight: bold;
            }
          }
          .active {
            color: #248afc;
            position: relative;
            font-weight: bold;
          }

          .active::before {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-50%);
            width: 46px;
            height: 3px;
            background-color: #248afc;
          }
        }
      }
      .login-form {
        /deep/ .login-input {
          input {
            height: 50px;
            background: rgba(23, 83, 150, 0.3);
            border: 1px solid rgba(23, 83, 150, 1);
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
            opacity: 1;
            padding-left: 58px;
            color: #fff;
            // border: 1px solid transparent;
            &::-webkit-input-placeholder {
              /* WebKit browsers */
              font-size: 16px;
              color: #fff;
              // color: #D3D7DE;
              // color: rgba(255, 255, 255, 0.2);
            }
            &:hover {
              background: rgba(23, 83, 150, 0.3);
            }
            &:focus {
              border-color: #4597ff;
              border-width: 1px;
              background: rgba(23, 83, 150, 0.7);
              // background: #1f63af;
            }
            &:active {
              border-color: #4597ff;
              border-width: 1px;
              background: rgba(23, 83, 150, 0.7);
              // background: #1f63af;
            }
          }
          // .ivu-icon-ios-close-circle:before {
          //   font-family: iconfont;
          //   color: #red;
          //   content: "\F178";
          //   font-size: 16px;
          //
          .ivu-input-suffix {
            top: 10px;
            right: 4px;
            .ivu-icon {
              font-size: 26px;
            }
          }
          .ivu-input-prefix {
            left: 20px;
            top: 10px;
            .iconfont {
              font-size: 20px;
              color: #fff;
            }
          }
          .login-icon {
            line-height: 50px;
            font-size: 20px;
            // color: rgba(1, 255, 255, 0.5);
            margin-left: 5px;
          }

          .ivu-input-icon-clear {
            color: #9cadcb;
            font-size: 26px;
            top: 10px;
            right: 4px;
          }
        }
        .btn {
          width: 400px;
          height: 50px;
          font-size: 20px;
          line-height: 50px;
          border-radius: 4px;
          color: #fff;
          font-weight: bold;
          cursor: pointer;
          margin-top: 22px;
        }
        .loginBtn {
          background: linear-gradient(90deg, #248afc 0%, #08c2ff 100%);
          box-shadow: 0px 6px 13px 0px rgba(0, 83, 175, 0.25);
        }
        .loginBtn:hover {
          box-shadow: 0px 6px 13px 0px rgba(0, 83, 175, 0.25);
          // background:linear-gradient(180deg, #1FD7FF 0%, #58A7FF 100%)
          background: linear-gradient(90deg, #58a7ff 0%, #1fd7ff 100%);
        }

        .loginBtn:active {
          box-shadow: 0px 6px 13px 0px rgba(0, 83, 175, 0.25);
          background: linear-gradient(90deg, #0e77ec 0%, #08bcff 100%);
        }

        /deep/ .ivu-input-prefix {
          width: 20px;
          height: 20px;
          top: 15px;
          left: 15px;

          img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }

        /deep/ .ivu-input {
          padding-left: 48px;
        }
      }
      .bottom_kpi {
        text-align: right;
        color: #248afc;
        height: 25px;
        line-height: 25px;
        i {
          font-size: 25px;
        }
        span {
          vertical-align: top;
          font-weight: bold;
          margin-left: 10px;
          font-size: 16px;
          font-family: "Microsoft YaHei";
        }
      }
    }
  }
  .footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    background: rgba(2, 10, 34, 0.4);
    padding: 10px 0;
    .login-logo {
      img {
        height: 46px;
      }
    }

    .line {
      width: 1px;
      height: 54px;
      background: #d0e5fe;
      margin: 0 20px;
    }
    .copyright {
      color: #d0e5fe;
      line-height: 20px;
      font-size: 12px;
      // opacity: 0.6;
    }
  }
}

.auxiliary-color {
  font-size: 20px;
  color: #d6d7db;
}
</style>
<style lang="less">
.macBox {
  label {
    margin-top: 10px;
    width: 100%;
    display: block;
    border: 1px dotted rgba(165, 176, 182, 0.7);
    padding: 8px;
  }
}
</style>
