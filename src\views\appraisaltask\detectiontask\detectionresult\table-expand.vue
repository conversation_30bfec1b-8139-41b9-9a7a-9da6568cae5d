<template>
  <div class="table-expand" :class="{ 'table-expand-loading': loading }">
    <div class="content" v-if="expandData.length !== 0">
      <div
        class="statistics-expand"
        :style="{ '--width_1': `${columnWidth.index + columnWidth.expand + columnWidth.indexName}px` }"
      >
        <p class="statistics-icon">
          <i class="icon-font icon-jiance-01"></i>
        </p>
        <span class="statistics-num ml-sm">
          <p>
            检测次数:
            <countTo class="detection" :startVal="0" :endVal="expandList.detectCount || 0" :duration="3000"></countTo>
          </p>
          <p>
            合格次数:
            <countTo
              class="qualified"
              :startVal="0"
              :endVal="expandList.qualifiedCount || 0"
              :duration="3000"
            ></countTo>
          </p>
        </span>
      </div>
      <div class="list">
        <div class="expand-row" v-for="item in expandData" :key="item.id">
          <div class="expand1" :style="{ '--width_expand1': `${columnWidth.detectTime}px` }">
            <span class="expand-value">{{ item.detectTime }}</span>
          </div>
          <div class="expand2" :style="{ '--width_expand2': `${columnWidth.detectDataCount}px` }">
            <span class="expand-value">{{ item.detectDataCount }}</span>
          </div>
          <div
            class="expand6 progress-task-box"
            :style="{ '--width_expand6': `${columnWidth.reinspectProgressPercent}px` }"
          >
            <div class="progress expand-value">
              <Progress
                :percent="item.reinspectProgressPercent || 0"
                stroke-color="var(--color-progress-success)"
                :hide-info="true"
              ></Progress>
              <span class="text font-green">{{ item.reinspectProgressPercent || 0 }}%</span>
            </div>
          </div>
          <div class="expand3" :style="{ '--width_expand3': `${columnWidth.spendTime}px` }">
            <span class="expand-value">{{ item.spendTime }}</span>
          </div>
          <div class="expand4" :style="{ '--width_expand4': `${columnWidth.formatResultValue}px` }">
            <span class="expand-value">
              <span v-if="item.reinspectStatus === '1'" class="check-statu" style="color: #de990f">复检中...</span>
              <span
                v-else
                class="check-statu"
                :class="[
                  item.qualified == 1 ? 'font-green' : '',
                  item.qualified == 2 ? 'color-failed' : '',
                  item.qualified == 3 ? 'font-D66418' : '',
                  item.qualified == 0 ? 'color-failed' : '',
                ]"
              >
                {{ item.formatResultValue }}
              </span></span
            >
          </div>
          <div class="expand7" :style="{ '--width_expand7': `${columnWidth.showResult}px` }">
            <i-switch
              class="iswitch"
              :value="item.showResult"
              :true-value="1"
              :false-value="0"
              @on-change="onChange($event, item)"
            />
          </div>
          <div class="expand5" :style="{ '--width_expand5': `${columnWidth.option}px` }">
            <span class="expand-value">
              <ui-btn-tip
                v-if="isExistIndex(item.indexType)"
                icon="icon-chakanxiangqing"
                content="检测结果"
                @click.native="handleClickJump(item)"
              ></ui-btn-tip>
              <create-tabs
                v-else
                :componentName="themData.componentName"
                :tabs-text="themData.text"
                @selectModule="selectModule"
                class="inline f-14 ml-sm"
                :tabs-query="{
                  indexId: item.indexId,
                  code: item.regionCode,
                  access: 'TASK_RESULT',
                  batchId: item.batchId,
                  startTime: item.startTime,
                }"
              >
                <ui-btn-tip icon="icon-chakanxiangqing" content="结果详情"></ui-btn-tip>
              </create-tabs>
              <ui-btn-tip
                v-permission="{
                  route: $route.name,
                  permission: 'recheck',
                }"
                v-if="recheckBtnVisible(item)"
                icon="icon-fujian"
                content="复检"
                class="operatbtn f-14 ml-sm"
                @click.native="clickRecheck(item)"
              ></ui-btn-tip>
              <ui-btn-tip
                v-if="terminationBtnVisible(item)"
                icon="icon-renwuzhongzhi"
                content="终止"
                class="operatbtn vt-middle f-14 ml-sm"
                :styles="{ color: '#CF3939' }"
                @click.native="clickTermination(item)"
              ></ui-btn-tip>
              <ui-btn-tip
                class="operatbtn rmargin ml-sm f-14"
                icon="icon-shanchu3"
                content="删除"
                v-permission="{
                  route: $route.name,
                  permission: 'deletehistorytask',
                }"
                @click.native="handleDeleteHistoryTask(item)"
              ></ui-btn-tip>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div v-loading="loading" class="no-content" v-else>
      <div class="no-text">
        <div class="null-data-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.table-expand {
  &.table-expand-loading .no-content {
    height: 120px;
    @{_deep} .el-loading-mask .el-loading-spinner {
      top: 20px;
    }
  }
  .no-content {
    height: 100px;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .null-data-text {
      color: #385074;
      font-size: 16px;
      line-height: 1.5;
      margin-right: 10px;
    }
  }
  .content {
    width: 100%;
    height: 100%;
    display: flex;
    @media screen and (max-width: 1366px) {
      .statistics-expand {
        .statistics-icon {
          margin-left: 70px;
        }
      }
      .expand-row {
        .expand1,
        .expand2,
        .expand3,
        .expand4,
        .expand6,
        .expand7 {
          .expand-value {
            padding: 0 1px;
            width: 100%;
          }
        }
      }
    }
    .statistics-expand {
      width: var(--width_1);
      display: flex;
      align-items: center;
      .statistics-icon {
        margin-left: 40px;
        // width: 80px;
        i {
          font-size: 30px;
          background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%) !important;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent;
        }
      }
      .statistics-num {
        .detection {
          color: var(--color-progress-default);
        }
        .qualified {
          color: var(--color-progress-success);
        }
        span {
          margin-left: 10px;
        }
      }
    }
    .list {
      //width: calc(100% - 390px);
      flex: 1;
      height: 100%;
    }
    .expand-row {
      width: 100%;
      height: 50px;
      line-height: 50px;
      display: flex;
      .expand1,
      .expand2,
      .expand3,
      .expand4,
      .expand5,
      .expand6 {
        .expand-value {
          padding: 0 5px;
          width: 100%;
        }
      }
      .expand1 {
        width: var(--width_expand1);
      }
      .expand2 {
        width: var(--width_expand2);
      }
      .expand3 {
        width: var(--width_expand3);
      }
      .expand4 {
        width: var(--width_expand4);
      }
      .expand5 {
        width: var(--width_expand5);
      }
      .expand6 {
        width: var(--width_expand6);
      }
      .expand7 {
        width: var(--width_expand7);
        text-align: center;
      }
    }

    .check-statu {
      min-width: 70px;
      height: 23px;
      line-height: 23px;
      text-align: left;
      display: inline-block;
      border-radius: 4px;
      font-size: 14px;
    }
  }
}
@{_deep}.ivu-tooltip-rel {
  .icon-chakanxiangqing {
    color: #de990f !important;
  }
}
// 更改进度条
@{_deep} .progress-task-box {
  display: flex;
  .ivu-progress {
    .ivu-progress-outer {
      .ivu-progress-inner {
        border: 1px solid var(--color-progress-success);
        background: transparent;
        padding: 1px;
        .ivu-progress-bg {
          height: 8px !important;
        }
      }
    }
  }
  .text {
    color: var(--color-progress-success);
  }
}
.progress {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.text {
  width: 60px; /*no*/
  margin-left: 2px; /*no*/
}
</style>
<script>
import evaluationreport from '@/config/api/evaluationreport';
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import indexRecheckConfig from './utils/indexRecheckConfig';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
export default {
  props: {
    row: {},
    tableColumns: [],
  },
  mixins: [evaluationoResultMixin],
  data() {
    return {
      themData: {
        componentName: 'detectionToOverview', // 需要跳转的组件名
        text: '评测结果', // 跳转页面标题
        title: '评测结果',
        type: 'view',
      },
      expandList: {},
      expandData: [],
      loading: false,
      columnWidth: {},
    };
  },
  created() {},
  mounted() {
    this.getColumnWidth();
    this.expandRow();
  },
  methods: {
    getColumnWidth() {
      try {
        if (!this.tableColumns.length) return;
        this.tableColumns.forEach((item) => {
          if (item.className) {
            let width = document.querySelector(`.column-${item.key || item.slot || item.type}`)?.offsetWidth;
            this.$set(this.columnWidth, item.key || item.slot || item.type, width || 150);
          }
        });
      } catch (error) {}
    },
    handleClickJump({ batchId, indexId, indexType, taskSchemeId, regionCode, orgCode }) {
      //evaluationoResultMixin.js
      this.jump({
        orgCode: orgCode,
        regionCode: regionCode,
        statisticType: 'REGION',
        taskSchemeId: taskSchemeId,
        indexId: `${indexId}`,
        indexType: indexType,
        batchId: batchId,
      });
    },
    handleDeleteHistoryTask({ batchId }) {
      this.$UiConfirm({ content: '删除后检测记录不可恢复，确定删除吗？' }).then(async () => {
        try {
          let params = {
            batchId,
          };
          let { data } = await this.$http.post(governanceevaluation.postDeleteIndexResult, params);
          // this.$emit('refresh')
          this.selfControlDelete(batchId);
          this.$emit('refresh');
          this.$Message.success(data.msg);
        } catch (e) {
          console.log(e);
        }
      });
    },
    selfControlDelete(batchId) {
      let Index = this.expandData.findIndex((item) => {
        return item.batchId === batchId;
      });
      this.expandData.splice(Index, 1);
    },
    async expandRow() {
      this.loading = true;
      try {
        let excludeResultIds = [];
        excludeResultIds.push(this.row.id);
        let param = {
          taskSchemeId: this.row.taskSchemeId,
          indexId: this.row.indexId,
          indexModule: this.row.indexModule,
          startTime: this.row.startTime,
          endTime: this.row.endTime,
          excludeResultIds: excludeResultIds,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getIndexResultHistoryRecord, param);
        this.expandList = data;
        this.expandData = data.entities;
        this.loading = false;
      } catch (err) {
        console.log(err);
      }
    },
    selectModule(name) {
      this.$emit('selectModule', name);
    },
    async clickTermination(row) {
      try {
        const params = {
          id: row.id,
          batchId: row.batchId,
        };
        await this.$http.post(evaluationoverview.terminateRunningReinspectJob, params);
        this.$Message.success('终止成功！');
        this.$emit('refresh');
      } catch (e) {
        console.log(e);
      }
    },
    terminationBtnVisible(row) {
      return Object.keys(indexRecheckConfig).includes(row.indexType) && row.reinspectStatus === '1';
    },
    recheckBtnVisible(row) {
      return Object.keys(indexRecheckConfig).includes(row.indexType) && row.reinspectStatus !== '1';
    },
    clickRecheck(row) {
      this.$emit('handleClickRecheck', row);
    },
    async onChange(val, row) {
      try {
        let data = {
          batchId: row.batchId,
          showResult: val,
        };
        await this.$http.post(evaluationreport.operationIndexResultByBatchId, data);
        this.$Message.success('成功');
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {},
  components: {
    countTo: require('vue-count-to').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
