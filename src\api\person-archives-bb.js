import request from '@/libs/request';
import { baselibService, governance } from './Microservice';

// 
// 查询人像静态库人员信息分页列表
export function queryBaseLibPersonPageList(data) {
    return request({
        url: baselibService + '/baseLibPerson/queryBaseLibPersonPageList',
        method: 'POST',
        data: data
    })
}

//根据身份证和厂商编号查询原始轨迹列表
export function queryTrajectoryListPAndID(data) {
    return request({
        url: governance + '/originalTrajectory/queryTrajectoryListByProfileIdAndIdNumber',
        method: 'POST',
        data: data
    })
}
// 查询融合置信档案详情
export function detailByIdNumber(data) {
    return request({
        url: governance + '/fuse/trust/profile/detailByIdNumber',
        method: 'POST',
        data: data
    })
}

// 查询原始档案分页列表
export function originalProfileQueryList(data) {
    return request({
        url: governance + '/originalProfile/queryList',
        method: 'POST',
        data: data
    })
}

// 查询原始档案详情分页列表
export function originalProfileQueryDetailList(data) {
    return request({
        url: governance + '/originalProfile/queryDetailList',
        method: 'POST',
        data: data
    })
}
