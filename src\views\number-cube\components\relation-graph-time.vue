<template>
  <ui-modal :width="modalWidth" v-model="modalShow" title="时间筛选" class="relation-graph-save aaa" @onOk="onOk">
    <div class="dataTime">
      <div class="title">抓拍时段:</div>
      <ui-tag-select
        ref="tagSelect1"
        @input="
          e => {
            input(e, 'timeSlot')
          }
        ">
        <ui-tag-select-option
          v-for="(item, $index) in captureTimePeriod"
          :key="$index"
          :name="item.name">
          {{ item.name }}
        </ui-tag-select-option>
      </ui-tag-select>
      <DatePicker v-show="queryForm.timeSlot == '自定义'" v-model="queryForm.timeSlotArr" type="datetimerange" format="yyyy-MM-dd HH:mm" placeholder="请选择抓拍时间段" style="width: 378px"></DatePicker>
    </div>
  </ui-modal>
</template>
<script>
export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modalShow: false,
      modalWidth: 430,
      queryForm: { 
        timeSlot: '0',
        timeSlotArr: []
      },
      captureTimePeriod: [ //抓拍时段
        { name: '近一天', value: '1' },
        { name: '近三天', value: '2' },
        { name: '近一周', value: '3' },
        { name: '自定义', value: '4' }
      ]
    }
  },
  computed: {},
  watch: {
    modalShow(val) {
      this.$emit('input', val)
    },
    value(val) {
      this.modalShow = val
    }
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    onOk() {
      this.$emit('checkSelectTime', this.queryForm)
    },
    input(e, type) {
      console.log('-------------', e, type)
      this.queryForm.timeSlot = e
      if (e == '自定义') {
        this.modalWidth = 830;
      }else{
        this.modalWidth = 430;
      }
    }
  }
}
</script>
<style lang="less" scoped>
.dataTime {
  display: flex;
  .title{
    height:34px;
    line-height:34px;
    padding-right: 20px;
    color: #999;
  }
}
/deep/ .ivu-modal-mask {
  display: none;
}
/deep/ .ivu-tag-select {
  // float: left;
}
/deep/ .ivu-modal-body {
  min-height: 60px !important;
}
/deep/ .vertical-center-modal {
  align-items: flex-start;
}
/deep/ .ivu-modal {
  top: 180px !important;
}
</style>
