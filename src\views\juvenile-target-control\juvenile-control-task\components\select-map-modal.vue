<!--
    * @FileDescription: 新增布控-选择设备
    * @Author: H
    * @Date: 2023/4/17
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-25 14:40:34
-->
<template>
  <custom-modal
    v-model="visible"
    title="框选范围"
    :r-width="1714"
    :footer="true"
    @onOk="confirmHandle"
    @onCancel="handleCancel"
  >
    <div class="map">
      <mapBase
        ref="mapBase"
        :mapLayerConfig="mapLayerConfig"
        :searchBtn="false"
        :siteListFlat="siteList"
        :crashType="crashType"
        @selectlist="selectlist"
      />
    </div>
    <div slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button type="primary" @click="confirmHandle">确定</Button>
    </div>
  </custom-modal>
</template>

<script>
import mapBase from "@/components/map/index.vue";
import customModal from "@/components/modal";
import { getSimpleDeviceList } from "@/api/operationsOnTheMap";
import { mapGetters } from "vuex";
export default {
  name: "",
  components: {
    mapBase,
    customModal,
  },
  data() {
    return {
      visible: true,
      mapLayerConfig: {
        tracing: false, // 是否需要刻画轨迹
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: true, // 框选操作栏
        selectionResult: true, // 是否显示框选结果弹框
        resultOrderIndex: false, // 搜索结果排序,
        showLatestLocation: false, // 显示地图最新位置
      },
      siteList: [],
      deviceIdList: [],
      crashType: {
        Camera_Face: true,
        Camera_Vehicle: true,
        Camera_QiuJi: true,
        Camera_QiangJi: true,
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      individuation: "individuation",
    }),
  },
  created() {
    this.getDeviceList();
  },
  methods: {
    show() {
      this.visible = true;
    },
    selectlist(value) {
      this.deviceIdList = value;
    },
    confirmHandle() {
      if (this.deviceIdList.length == 0) {
        this.$Message.warning("暂未框选数据!");
        return;
      }
      this.$emit("configdata", this.deviceIdList);
    },
    handleCancel() {
      this.$emit("configdata", []);
    },

    /**
     * @description: 获取设备点位
     */
    async getDeviceList() {
      let deviceTypes = [1, 2, 3, 4, 5, 11];
      this.siteList = [];
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
        socialResources: this.individuation.hideCommunity ? 0 : 1,
        excludeDeviceTypes: ["5", "6", "7"], // 分别为wifi， rfid，electric感知设备
      };
      let { data } = await getSimpleDeviceList(roleParam);
      data = data.filter((v) => deviceTypes.includes(v[2]));
      this.siteList = [...data];
    },
  },
};
</script>

<style lang='less' scoped>
.map {
  height: 640px;
  position: relative;
}
</style>
