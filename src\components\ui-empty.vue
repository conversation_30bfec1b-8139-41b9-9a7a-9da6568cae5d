<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-28 15:37:06
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-22 10:43:09
 * @Description: 
-->
<template>
  <div :style="{ background: bgColor }" class="cover">
    <img
      class="icon"
      src="../assets/img/empty-page/null_main_icon.png"
      alt=""
    />
    <div class="text">
      <slot>暂无数据</slot>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    color: {
      type: String,
      default: "#55c5f2",
    },
    bgColor: {
      type: String,
      default: "transparent",
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.cover {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  height: initial;
  width: 100%;
  z-index: 5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.icon {
  flex: 1;
  height: 0;
  max-height: 150px;
}

.text {
  overflow: hidden;
  font-size: 16px;
  color: #385074;
  margin-left: -5px;
}

/*
    Set the color of the icon
*/
// svg path,
// svg rect {
//     fill: #ff6700;
// }
</style>

