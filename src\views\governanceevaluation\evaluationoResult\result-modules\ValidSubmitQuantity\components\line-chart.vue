<template>
  <div class="line-chart-container">
    <title-section :title-name="titleName + '变化趋势'">
      <div class="search" slot="content">
        <slot name="custom-node"></slot>
      </div>
    </title-section>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: Object.keys(lineData) }">
      <draw-echarts
        v-if="Object.keys(lineData).length"
        :echart-option="echartRin"
        :echart-style="rinStyle"
        ref="zdryChart"
        class="charts"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    TitleSection: require('@/views/governanceevaluation/evaluationoResult/components/title-section').default,
  },
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    dateType: {
      type: String,
      default: '',
    },
    lineData: {
      type: Object,
      default: () => {},
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [dealWatch],
  data() {
    return {
      titleName: '',
      barData: [],
      echartRin: {},
      rinStyle: {
        width: '100%',
        height: '100%',
      },
      loading: false,
      totalCapture: 0, // 总量
      monthTotalCapture: 0, //月量
    };
  },
  methods: {
    initRin() {
      let opts = {
        data: this.lineData,
        dateType: this.dateType,
        tooltipFormatter: (data) => {
          let str = '';
          data.forEach((item) => {
            str = `<p class="mb-sm">${this.formatDateTime(item.data.startTime)}</p>
                     <p><span class="mr-md">${this.titleName}</span>${item.data.actualNum}</p>`;
          });
          return str;
        },
      };
      this.echartRin = this.$util.doEcharts.indexTrendChart(opts);
    },
    formatDateTime(dateTime) {
      if (!dateTime) {
        return '';
      }
      let dateObject = new Date(dateTime);
      let year = dateObject.getFullYear();
      let month = dateObject.getMonth() + 1 < 10 ? '0' + (dateObject.getMonth() + 1) : dateObject.getMonth() + 1;
      let date = dateObject.getDate() < 10 ? `0${dateObject.getDate()}` : dateObject.getDate();
      return this.dateType === 'DAY' ? `${year}年${month}月${date}日` : `${year}年${month}月`;
    },
  },
  watch: {
    activeIndexItem: {
      handler(val) {
        const nameArr = val.indexName.split('达标率');
        this.titleName = nameArr[0];
      },
      immediate: true,
    },
    lineData: {
      handler() {
        this.initRin();
      },
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.line-chart-container {
  position: relative;
  height: 100%;
  width: 100%;
  box-shadow: var(--shadow-sub-echarts-content);
  .search {
    position: relative;
    display: flex;
    align-items: center;

    .statistical-duration {
      margin-left: 26px;
      display: flex;
      align-items: center;

      > div {
        color: #fff;
        margin-left: 42px;
        font-size: 14px;
      }

      .font-sky {
        color: #25e6fd;
      }

      .font-orange {
        color: #f78b2e;
      }
    }
  }

  .echarts-box {
    height: calc(100% - 44.5px) !important;
    width: 100%;
    background: var(--bg-sub-echarts-content);
    padding-top: 10px;

    .charts {
      height: 100% !important;
    }
  }
}
</style>
