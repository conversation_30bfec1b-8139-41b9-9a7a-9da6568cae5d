<template>
  <div class="map-echarts" :class="getFullscreen ? 'full-screen-container' : ''">
    <div id="map-chart" style="width: 100%; height: 100%" ref="chartsRef"></div>
    <div class="range-legend">
      <ul class="range-legend-ul">
        <li class="range-legend-li mb-xs">问题数占比</li>
        <li class="range-legend-li" v-for="(item, index) of legendList" :key="index">
          <span class="inline circle" :style="{ 'background-color': item.color }"></span>
          {{ item.percent }}
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import { options } from './geo.config';
import home from '@/config/api/home';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    batchIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      mapCharts: null,
      geoCoordMapData: [],
      regionCode: '',
      legendList: Object.freeze([
        {
          color: 'rgba(96, 252, 48, 1)',
          percent: '≤20%',
        },
        {
          color: 'rgba(252, 179, 3, 1)',
          percent: '20% ~ 50%',
        },
        {
          color: 'rgba(231, 89, 11, 1)',
          percent: '≥ 50%',
        },
      ]),
      legendLimit: Object.freeze({
        minPercent: 20,
        middlePercent: 50,
      }),
    };
  },
  computed: {
    ...mapGetters({
      getMapItem: 'home/getMapItem',
      getFullscreen: 'home/getFullscreen',
    }),
  },
  methods: {
    ...mapActions({
      setMapItem: 'home/setMapItem',
    }),
    addMapEvent() {
      this.mapCharts.on('click', (params) => {
        this.$emit('click', params);
      });
    },
    toHomeConfig() {
      this.$UiConfirm({
        content: '首页未配置行政区划。是否配置行政区划？',
      }).then(() => {
        this.$router.push('/homeconfig');
      });
    },
    async viewByParamKey() {
      try {
        let params = {
          key: 'HOME_PAGE_CONFIG',
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        let paramValue = JSON.parse(data.paramValue);
        let regionCode = paramValue.regionCode.slice(0, 6);
        return regionCode;
      } catch (e) {
        console.log(e);
      }
    },
    async initMap(areaCode) {
      let { data } = await this.$http.get(`/json/map-china-config/${areaCode}.json`);
      this.caculateMapCenter(data);
      this.mapCharts = this.$echarts.init(document.getElementById('map-chart'));
      this.$echarts.registerMap('map', data);
      let mapFeatures = this.$echarts.getMap('map').geoJson.features;
      mapFeatures.forEach((v) => {
        // 地区经纬度
        this.geoCoordMapData.push({
          name: v.properties.name,
          value: v.properties.cp || v.properties.centroid,
          img: `image://${require('@/assets/img/device.png')}`,
          regionCode: v.properties.adcode || v.properties.id,
        });
      });
      // this.mapCharts.setOption(options(this.geoCoordMapData))
      this.handleExpectHainan(areaCode);
      this.addMapEvent();
      //this.clickMapEvent()
      this.scaleMapEvent();
      this.getIndexMapStatistics().then((data) => {
        let dataObject = {};
        if (!data || !data.length) return;
        data.forEach((item) => (dataObject[item.regionCode] = item));
        this.handleEchartsTotal(dataObject);
        this.handleExpectHainan(areaCode);
      });
      window.addEventListener('resize', () => this.mapCharts.resize());
    },
    // 单独处理海南地图
    async handleExpectHainan(areaCode) {
      const finalOption = await options(this.geoCoordMapData);
      if (areaCode === '460000') {
        finalOption.geo[0].center = [109.844902, 19.0392];
        finalOption.geo[0].layoutCenter = ['51%', '53%'];
        finalOption.geo[0].layoutSize = '600%';
        finalOption.series[0].center = [109.844902, 19.0392];
        finalOption.series[0].layoutCenter = ['50%', '50%'];
        finalOption.series[0].layoutSize = '600%';
      }
      this.mapCharts.setOption(finalOption);
    },
    handleEchartsTotal(dataObject) {
      // 计算总问题数 + 每个区域问题数的百分比
      let allTotalList = this.geoCoordMapData.map((item) => {
        item.data = dataObject[item.regionCode];
        let total = 0;
        if (item.data) {
          total =
            (item.data.fileProblemTotal || 0) +
            (item.data.basicProblemTotal || 0) +
            (item.data.faceProblemTotal || 0) +
            (item.data.vehicleProblemTotal || 0) +
            (item.data.focusProblemTotal || 0);
        }
        item.total = total;
        return total;
      });
      let allTotal = allTotalList.reduce((cur, pre) => {
        return cur + pre;
      });
      this.geoCoordMapData.forEach((item) => {
        if ((item.total / allTotal) * 100 <= this.legendLimit.minPercent) {
          item.limitColor = ['rgba(96, 252, 48, 1)', 'rgba(96, 252, 48, 0)'];
        }
        if (this.legendLimit.minPercent < (item.total / allTotal) * 100 < this.legendLimit.middlePercent) {
          item.limitColor = ['rgba(252, 179, 3, 1)', 'rgba(252, 179, 3, 0)'];
        }
        if ((item.total / allTotal) * 100 >= this.legendLimit.middlePercent) {
          item.limitColor = ['rgba(231, 89, 11, 1)', 'rgba(231, 89, 11, 0)'];
        }
      });
    },
    // 点击地图
    clickMapEvent() {
      this.mapCharts.on('click', async (params) => {
        this.geoCoordMapData.forEach((item, index) => {
          index === params.dataIndex ? (item.selected = true) : (item.selected = false);
        });
        this.setMapItem(this.geoCoordMapData[params.dataIndex]);
        this.mapCharts.setOption(await options(this.geoCoordMapData));
      });
    },
    // 放大缩小地图(使两个地图同步缩放)
    scaleMapEvent() {
      this.mapCharts.on('georoam', (params) => {
        let options = this.mapCharts.getOption();
        if (params.zoom != null && params.zoom != undefined) {
          options.geo[0].zoom = options.series[0].zoom + 0.01;
          options.geo[0].center = options.series[0].center;
        } else {
          options.geo[0].center = options.series[0].center;
        }
        this.mapCharts.setOption(options);
      });
    },
    async getIndexMapStatistics() {
      try {
        let { data } = await this.$http.post(home.queryHomePageResultIndexMapStatistics, {
          regionCode: this.regionCode,
          batchIds: this.batchIds,
        });
        let list = data.data;
        return list;
      } catch (err) {
        console.log(err);
      }
    },
    caculateMapCenter(data) {
      // 根据获取的json处理中心点位 （
      data.features.forEach((rw) => {
        /**
         * 有些地区没有中心点位，需要把json中所有地图边界点位得到平均数来获取中心点
         */
        if (!rw.properties.cp && !rw.properties.centroid) {
          let sumLon = 0;
          let sumLat = 0;
          let aveLon = 0;
          let aveLat = 0;
          let length = 0;
          // coordinates不同的区域有可能不止一个
          rw.geometry.coordinates.forEach((i) => {
            i[0].forEach((r) => {
              sumLon += r[0];
              sumLat += r[1];
              length++;
            });
          });
          aveLon = sumLon / length;
          aveLat = sumLat / length;
          rw.properties.cp = [aveLon, aveLat];
        }
      });
    },
  },
  beforeDestroy() {
    if (this.mapCharts) {
      this.$echarts.dispose(this.$refs.chartsRef);
      this.mapCharts = null;
      window.removeEventListener('resize', () => this.mapCharts.resize());
    }
  },
  watch: {
    batchIds: {
      handler(val) {
        this.$nextTick(async () => {
          if (!val.length) return false;
          const regionCode = await this.viewByParamKey();
          if (!regionCode) {
            this.toHomeConfig();
            return;
          }
          this.regionCode = regionCode;
          this.initMap(regionCode);
        });
      },
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.map-echarts {
  position: absolute;
  left: 50%;
  top: 13%;
  transform: translateX(-50%);
  width: 60%;
  height: 60%;
  .range-legend {
    position: relative;
    color: #fff;
    .range-legend-ul {
      position: absolute;
      right: 10%;
      bottom: 50px;
      .circle {
        width: 10px;
        height: 10px;
        border-radius: 10px;
      }
    }
  }
}
.full-screen-container {
  top: 15%;
  width: 54%;
}
</style>
