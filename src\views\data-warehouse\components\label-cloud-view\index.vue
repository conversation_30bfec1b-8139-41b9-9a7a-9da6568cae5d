<template>
  <div id="container">
    <img src="@/assets/img/archives/bg-sunlight.png" alt="" class="bg-sunlight" />
    <div class="echart-content">
      <div class="obj-content">
        <p class="img-content">
          <!-- <span> -->
            <img v-if="(type === 1 || type === 4) && photoUrl" :src="photoUrl" alt="对象图片" class="obj-img" />
            <img v-else-if="type === 2 && info.photo" :src="info.photo" alt="对象图片" class="obj-img" />
            <img v-else-if="type === 3 && info.photoUrl" :src="info.photoUrl" alt="对象图片" class="obj-img" />
            <img v-else :src="numberDataList[type - 1].img" alt="对象图片" class="obj-img" />
          <!-- </span> -->
        </p>
      </div>
      <img v-show="isClose" :style="style" class="close-icon" src="@/assets/img/tag_close_icon.png" @mouseover.stop="" @click="closeHandle" alt />
      <div id="labelsContent" ref="labelsContent" class="labels-content"></div>
    </div>
    <div class="number-content" v-if="numberDataList[type - 1].label">
      <h3 class="title">{{ numberDataList[type - 1].label }}</h3>
      <p class="number" :class="numberDataList[type - 1].className">
        <span v-if="type === 1">{{ info.gmsfhm ? info.gmsfhm : numberDataList[type - 1].number }}</span>
        <span v-if="type === 2">{{ info.archiveNo ? info.archiveNo : numberDataList[type - 1].number }}</span>
        <ui-plate-number v-if="type === 3" :plateNo="info.plateNo ? info.plateNo : numberDataList[type - 1].number" :color="info.plateColor" size='super'></ui-plate-number>
        <span v-if="type === 4">{{ info.deviceId ? info.deviceId : numberDataList[type - 1].number }}</span>
      </p>
    </div>
  </div>
</template>

<script>
import wordCloudBg from './imgBase64'
import * as echarts from 'echarts'
import * as wordcloud from 'echarts-wordcloud'
import elementResizeDetectorMaker from 'element-resize-detector'
export default {
  props: {
    labels: {
      type: Array,
      default: () => []
    },
    // 实名档图片
    photoUrl: {
      type: String,
      default: ''
    },
    // 基本信息
    info: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      img: '',
      numberDataList: [
        {
          type: 1,
          label: '身份证号',
          number: '320102********9556',
          className: 'identification-card',
          img: require('@/assets/img/default-img/real_name_default.png')
        },
        {
          type: 2,
          label: '视频身份',
          number: '0010302020320203',
          className: 'video-identity',
          img: require('@/assets/img/default-img/people_default.png')
        },
        {
          type: 3,
          label: '车牌号',
          number: '',
          className: 'plate-number',
          img: require('@/assets/img/default-img/vehicle_default.png')
        },
        {
          type: 4,
          label: '设备编码',
          number: 'V2367812641289749120',
          className: 'device-number',
          img: require('@/assets/img/default-img/device_default.png')
        }
      ],
      wordcloud,
      labelId: '',
      isClose: false,
      style: {
        width: '16px',
        height: '16px',
        top: 0,
        left: 0
      }
    }
  },
  watch: {
    labels(val) {
        this.$nextTick(() =>{
            this.init()
        })
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
        this.$nextTick(()=> {
            const _that = this
            const myEchart = echarts.init(this.$refs.labelsContent)
            const labelsData = this.labels.map(item => {
                return {
                    name: item.labelName,
                    id: item.id,
                    textStyle: {
                        fontSize: 16,
                        fontStyle: 'normal',
                        fontFamily: 'Microsoft YaHei',
                        textVerticalAlign: 'center',
                        backgroundColor: this.$util.common.colorRgb(item.labelColor, 0.05),
                        borderRadius: 2,
                        borderWidth: 0.5,
                        borderColor: item.labelColor,
                        padding: [6, 8],
                        color: item.labelColor
                    }
                }
            })
            const image = wordCloudBg
            const maskImage = new Image()
            maskImage.src = image
            let option = {
                series: [
                {
                    type: 'wordCloud',
                    // 网格大小，各项之间间距
                    gridSize: 50,
                    // 形状 circle 圆， cardioi d  心， diamond 菱形，
                    // triangle-forward 、triangle 三角，star五角星
                    // shape: 'circle',
                    // 文字旋转角度范围
                    rotationRange: [0, 0],
                    // 旋转步值
                    rotationStep: 10,
                    // 自定义图形
                    maskImage: maskImage,
                    left: 'center',
                    top: 'center',
                    right: null,
                    bottom: null,
                    // 画布宽
                    width: '100%',
                    // 画布高
                    height: '100%',
                    // 是否渲染超出画布的文字
                    drawOutOfBound: false,
                    data: labelsData
                }
                ]
            }
            maskImage.onload = function () {
                myEchart.setOption(option)
            }
            // 监听标签点击事件跳转到标签详情页
            myEchart.on('click', 'series', function (params) {
                console.log(params)
                _that.$emit('on-click', params.data)
            })
            // 监听鼠标移动事件显示删除标签按钮
            /*   myEchart.on('mouseover', 'series', function (params) {
                if (params.event) {
                _that.labelId = params.data.id
                _that.isClose = true
                _that.style.top = params.event.target.parent.y - 18 + 'px'
                _that.style.left = params.event.target.parent.x + parseInt(parseInt(params.event.target.parent._children[0]._rectWithStroke.width) / 2) + 8 + 'px'
                } else {
                _that.isClose = false
                }
            })*/
            // 监听鼠标移动事件隐藏删除标签按钮
            myEchart.getZr().on('mousemove', 'series', function (params) {
                if (!params.target) {
                _that.labelId = ''
                _that.isClose = false
                }
            })
            // 容器大小改变是执行
            const _this = this
            const erd = elementResizeDetectorMaker()
            erd.listenTo(document.getElementById('labelsContent'), element => {
                _this.$nextTick(() => {
                    // 监听到事件后执行的业务逻辑
                    // 重新渲染
                    myEchart.resize()
                })
            })
        })
      
    },
    // 删除标签
    closeHandle() {
      this.$emit('on-delete', this.labelId)
    }
  }
}
</script>
<!-- Add "scoped" attribute to limit CSS to this cmponendt onldy -->
<style scoped lang="less">
#container {
  overflow: hidden;
  border-radius: 0;
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  .echart-content {
    flex: 1;
    overflow: hidden;
    position: relative;
  }
  .bg-sunlight {
    position: absolute;
    top: 42%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 96%;
    height: auto;
  }
  .obj-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 320px;
    height: 320px;
    box-shadow: 0px 15px 35px 0px #a2cbff;
    background: linear-gradient(180deg, rgba(91, 163, 255, 1), rgba(44, 134, 248, 1));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index:999;
    .img-content {
      position: relative;
      width: 278px;
      height: 278px;
      background: #fff;
      border-radius: 50%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      .obj-img {
        width: 100%;
        height: 100%;
        position: absolute;
        overflow: hidden;
        z-index: 2;
        display: block;
        // max-width: 290px;
        // height: 240px;
        // border: 3px solid #d3d7de;
        // border-radius: 50%;
      }
    }
  }
  .number-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;
    z-index: 1;
    .title {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
      padding-bottom: 8px;
    }
    .number {
      height: 60px;
      border-radius: 25px;
      font-size: 30px;
      padding: 0 40px;
      color: #fff;
      letter-spacing: 1px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      &.identification-card {
        background: linear-gradient(263deg, #5ba3ff 0%, #2c86f8 100%);
      }
      &.video-identity {
        background: linear-gradient(234deg, #ffc652 0%, #ff842e 100%);
      }
      &.device-number {
        background: linear-gradient(144deg, #3cd2aa 0%, #1faf8a 100%);
      }
      &.plate-number {
        border-radius: 3px;
        padding: 5px;
        // background: #2379f9;
        span {
          height: 100%;
          display: flex;
          align-items: center;
          border-radius: 3px;
          border: 1px solid #ffffff;
          padding: 0 18px;
          font-size: 30px;
          font-weight: 600;
        }
      }
    }
  }
  .labels-content {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 100;
  }
}
.close-icon {
  cursor: pointer;
  position: absolute;
  z-index: 200;
}
</style>
