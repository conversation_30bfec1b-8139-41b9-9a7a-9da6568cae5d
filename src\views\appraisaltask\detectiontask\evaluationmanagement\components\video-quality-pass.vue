<template>
  <ui-modal v-model="visible" title="查看详情" :styles="styles" class="confimDataModal" footer-hide>
    <div class="carInfo auto-fill">
      <div class="title_text">
        <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
        <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
      </div>
      <line-title title-name="检测结果统计"></line-title>
      <div class="top-wrapper mt-sm">
        <statistics class="statistics" :statistics-list="abnormalCount" :isflexfix="false"></statistics>
        <rank :rank-data="rankList"></rank>
      </div>
      <line-title title-name="检测结果详情"></line-title>
      <!-- 列表 ---------------------------------------------------------------------------------------------------- -->
      <div class="search-wrapper">
        <ui-label class="inline" label="设备编码" :width="70">
          <Input v-model="searchData.deviceId" class="input-width" placeholder="请输入设备编码"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="设备名称" :width="70">
          <Input v-model="searchData.deviceName" class="input-width" placeholder="请输入设备名称"></Input>
        </ui-label>
        <ui-label class="fl ml-lg" label="检测结果" :width="70">
          <Select v-model="searchData.outcome" clearable placeholder="请选择" class="input-width">
            <Option :value="1">合格</Option>
            <Option :value="2">不合格</Option>
            <Option :value="3">无法检测</Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" label="异常类型" :width="70">
          <Select class="width-sm" v-model="searchData.exceptionType" clearable placeholder="请选择检测结果">
            <Option v-for="item in exceptionTypeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </ui-label>
        <ui-label :width="70" class="fl" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button class="mr-sm" @click="resetInitial"> 重置 </Button>
        </ui-label>
      </div>
      <div class="table-box mt-sm">
        <ui-table
          :loading="loading"
          class="ui-table"
          :minus-height="minusTable"
          :table-columns="tableColumns"
          :table-data="tableData"
        >
          <template #description="{ row }">
            <span
              :class="[
                row.description === '合格' ? 'font-green' : '',
                row.description === '不合格' ? 'color-failed' : '',
                row.description === '无法检测' ? 'font-D66418' : '',
              ]"
              >{{ row.description }}</span
            >
          </template>
          <template #videoSignal="{ row }">
            <span
              :class="[
                row.videoSignal === '1' ? 'font-green' : '',
                row.videoSignal === '2' ? 'color-failed' : '',
                row.videoSignal === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoSignal === '1'
                  ? '正常'
                  : row.videoSignal === '2'
                    ? '异常'
                    : row.videoSignal === '0'
                      ? '无法检测'
                      : '--'
              }}</span
            >
          </template>
          <template #videoBrightness="{ row }">
            <span
              :class="[
                row.videoBrightness === '1' ? 'font-green' : '',
                row.videoBrightness === '2' ? 'color-failed' : '',
                row.videoBrightness === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoBrightness === '1'
                  ? '正常'
                  : row.videoBrightness === '2'
                    ? '异常'
                    : row.videoBrightness === '0'
                      ? '无法检测'
                      : '--'
              }}</span
            >
          </template>
          <template #videoColorCast="{ row }">
            <span
              :class="[
                row.videoColorCast === '1' ? 'font-green' : '',
                row.videoColorCast === '2' ? 'color-failed' : '',
                row.videoColorCast === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoColorCast === '1'
                  ? '正常'
                  : row.videoColorCast === '2'
                    ? '异常'
                    : row.videoColorCast === '0'
                      ? '无法检测'
                      : '--'
              }}</span
            >
          </template>
          <template #videoClear="{ row }">
            <span
              :class="[
                row.videoClear === '1' ? 'font-green' : '',
                row.videoClear === '2' ? 'color-failed' : '',
                row.videoClear === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoClear === '1'
                  ? '正常'
                  : row.videoClear === '2'
                    ? '异常'
                    : row.videoClear === '0'
                      ? '无法检测'
                      : '--'
              }}</span
            >
          </template>
          <template #videoOcclusion="{ row }">
            <span
              :class="[
                row.videoOcclusion === '1' ? 'font-green' : '',
                row.videoOcclusion === '2' ? 'color-failed' : '',
                row.videoOcclusion === '0' ? 'font-D66418' : '',
              ]"
              >{{
                row.videoOcclusion === '1'
                  ? '正常'
                  : row.videoOcclusion === '2'
                    ? '异常'
                    : row.videoOcclusion === '0'
                      ? '无法检测'
                      : '--'
              }}</span
            >
          </template>
          <template slot-scope="{ row }" slot="option">
            <ui-btn-tip
              icon="icon-chakanjietu"
              content="查看截图"
              disabled
              v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
            ></ui-btn-tip>
            <ui-btn-tip v-else icon="icon-chakanjietu" content="查看截图" @click.native="showResult(row)"></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
      <resultModel ref="result"></resultModel>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: [' value'],
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    rank: require('@/views/governanceevaluation/evaluationoverview/components/components/rank.vue').default,
    resultModel: require('@/components/result-model.vue').default,
    lineTitle: require('@/components/line-title').default,
  },
  data() {
    return {
      visible: true,
      loading: false,
      minusTable: 580,
      styles: {
        width: '8.58rem',
      },
      exportLoading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        exceptionType: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      }, // 查询参数
      exceptionTypeList: [
        {
          value: '1',
          label: '视频信号',
        },
        {
          value: '2',
          label: '视频亮度',
        },
        {
          value: '3',
          label: '视频偏色',
        },
        {
          value: '4',
          label: '视频清晰',
        },
        {
          value: '5',
          label: '视频遮挡',
        },
      ],
      tableData: [],
      rankList: [],
      abnormalCount: [
        {
          name: '视频监控设备总数',
          icon: 'icon-jianceshebeishuliang',
          key: 'total',
          value: 0,
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
        },
        {
          name: '实际测设备数量',
          icon: 'icon-shijijianceshebeishuliang',
          value: 0,
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'actualAmout',
        },
        {
          name: '视频质量合格设备',
          icon: 'icon-shipinzhilianghegeshebei1',
          value: 0,
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          key: 'passAmout',
        },
        {
          name: '视频质量异常设备',
          icon: 'icon-shipinzhiliangyichangshebei',
          value: 0,
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'notPassAmout',
        },
        {
          name: '视频质量合格率',
          icon: 'icon-shipinzhilianghegeshuai',
          value: 0,
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          qualified: true,
          key: 'passRate',
        },
        {
          name: '视频遮挡',
          icon: 'icon-shipinzhedang',
          value: 0,
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          key: 'videoOcclusionAmout',
        },
        {
          name: '视频信号异常',
          icon: 'icon-shipinxinhaoyichang',
          value: 0,
          iconColor: 'icon-bg7',
          liBg: 'li-bg7',
          type: 'number',
          key: 'videoSignalAmout',
        },
        {
          name: '视频亮度异常',
          icon: 'icon-shipinliangduyichang',
          value: 0,
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          type: 'number',
          key: 'videoBrightnessAmout',
        },
        {
          name: '视频偏色',
          icon: 'icon-shipinpianse',
          value: 0,
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          type: 'number',
          key: 'videoColorCastAmout',
        },
        {
          name: '视频清晰度异常',
          icon: 'icon-shipinqingxiduyichang',
          value: 0,
          iconColor: 'icon-bg10',
          liBg: 'li-bg10',
          type: 'number',
          key: 'videoClearAmout',
        },
      ],
      tableColumns: [
        { type: 'index', width: 70, align: 'center', title: '序号' },
        { title: '设备编码', key: 'deviceId', align: 'left', width: 170 },
        { title: '设备名称', key: 'deviceName', tooltip: true, align: 'left', minWidth: 120 },
        { title: '组织机构', key: 'orgName', tooltip: true, align: 'left', minWidth: 120 },
        { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText', width: 120 },
        { title: 'IP地址', key: 'ipAddr', tooltip: true, align: 'left', minWidth: 120 },
        { title: '检测结果', slot: 'description', width: 120 },
        { title: '视频信号', slot: 'videoSignal', width: 120 },
        { title: '视频亮度', slot: 'videoBrightness', width: 120 },
        { title: '视频偏色', slot: 'videoColorCast', width: 120 },
        { title: '视频清晰', slot: 'videoClear', width: 120 },
        { title: '视频遮挡', slot: 'videoOcclusion', width: 120 },
        { title: '检测时间', key: 'startTime', align: 'left', width: 160 },
        { title: '操作', slot: 'option', fixed: 'right', width: 100, align: 'left' },
      ],
    };
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  async mounted() {
    await this.init();
    await this.getChartsData();
    await this.subordinateTopList();
  },
  methods: {
    // 列表
    async init() {
      let params = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        exceptionType: this.searchData.exceptionType,
        deviceId: this.searchData.deviceId,
        deviceName: this.searchData.deviceName,
        outcome: this.searchData.outcome,
        params: this.searchData.params,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.videoPageList, params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    async getChartsData() {
      try {
        let params = {
          resultIndexId: this.$parent.row.id,
        };
        let res = await this.$http.post(governanceevaluation.getFaceResultStatistics, params);
        if (!res.data.data || !res.data.data.indexJson) return;
        let resultStatistics = JSON.parse(res.data.data.indexJson) || [];
        this.abnormalCount.forEach((v) => {
          resultStatistics.map((n) => {
            if (n.key === v.key) {
              v.value = parseInt(n.desc);
            }
          });
          if (v.key === 'passRate') {
            if (v.value >= res.data.data.standardsValue) {
              v.qualified = true;
            } else {
              v.qualified = false;
            }
            let rate = v.value.toFixed(2) + '%';
            v.value = rate;
          }
        });
      } catch (error) {
        console.log(error);
      }
    },
    async subordinateTopList() {
      let params = {
        resultIndexId: this.$parent.row.id,
      };
      try {
        let res = await this.$http.get(governanceevaluation.subordinateTopList, { params });
        this.rankList = [];
        var topList = [];
        if (res.data.data.length > 0) {
          topList = res.data.data.length > 5 ? res.data.data.slice(0, 5) : res.data.data;
          topList.map((item, index) => {
            let data = {
              rank: index + 1,
              regionName: item.orgName,
              standardsValue: item.resultValue == '0' ? '0.00%' : item.resultValue.toFixed(2) + '%',
            };
            this.rankList.push(data);
          });
        }
      } catch (err) {
        console.log(err);
      }
    },
    async search() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      await this.init();
    },
    // 重置
    async resetInitial() {
      this.searchData = {
        deviceId: '',
        deviceName: '',
        outcome: '',
        exceptionType: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      };
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      await this.init();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.params.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    getExport() {},
    showResult(row) {
      this.$refs.result.showModal(row);
    },
  },
};
</script>
<style lang="less" scoped>
.confimDataModal {
  @{_deep} .ivu-modal-body {
    padding: 50px 20px 0 20px;
  }
  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  .title_text {
    display: flex;
    justify-content: space-between;
  }
  .top-wrapper {
    display: flex;
    .statistics {
      width: 1270px;
      height: 252px;
      padding: 20px;
      background: var(--bg-sub-content);
    }
  }
  .information-ranking {
    height: 252px !important;
    width: 330px !important;
    .ranking-list ul li {
      .content-second {
        margin-left: 35px !important;
      }
    }
  }
  .abnormal-title {
    height: 48px;
    line-height: 48px;
    border-bottom: 0.005208rem solid var(--devider-line);
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
  .search-wrapper {
    height: 50px;
    display: flex;
    align-items: center;
    .input-width {
      width: 176px;
    }
  }
  .table-box {
    overflow: hidden;
    position: relative;
    margin-top: 10px;
    .sucess {
      color: @color-success;
    }
    .error {
      color: @color-failed;
    }
    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    @{_deep} .ivu-table-tbody {
      td {
        padding: 10px 0 10px 0;
      }
    }
    @{_deep} .ivu-table-body {
      td {
        padding: 10px 0 10px 0;
      }
    }
  }
}
</style>
