<template>
  <div class="page-evaluationmanagement">
    <div class="device-header">
      <Button type="primary" @click="save">
        <span class="icon-font f-14 mr-xs" :class="isEdit ? 'icon-baocun' : 'icon-bianji3'"></span>
        {{ isEdit ? '保  存' : '修  改' }}</Button
      >
    </div>
    <div class="content">
      <div class="title">
        <span class="main-title">设备接入配置</span>
        <!-- <span class="subtitle">CONFIGURATION</span> -->
      </div>
      <data-warehouse :warehousData="warehousData" :isEdit="isEdit" @selectChange="selectChange"></data-warehouse>
    </div>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {},
  data() {
    return {
      warehousData: {
        dataHandle: '1',
      },
      isEdit: false,
    };
  },
  created() {
    this.getTaskView();
  },
  methods: {
    selectChange(val) {
      this.warehousData.dataHandle = val;
    },
    async getTaskView() {
      try {
        let res = await this.$http.get(equipmentassets.viewByParamKey, {
          params: { key: 'DEVICE_DATA_HANDLE' },
        });
        const datas = res.data.data;
        this.warehousData.dataHandle = datas.paramValue;
      } catch (error) {
        console.log(error);
      }
    },
    save() {
      this.isEdit = !this.isEdit;
      if (this.isEdit) return false;
      if (this.warehousData.dataHandle === '') {
        this.$Message.error('请将信息填写完整！');
        return false;
      }
      this.update();
    },
    async update() {
      try {
        const params = {
          paramKey: 'DEVICE_DATA_HANDLE',
          paramValue: this.warehousData.dataHandle,
          paramType: 'ivdg',
        };
        await this.$http.put(equipmentassets.updateByParamKey, params);
        this.$Message.success('设备接入配置修改成功！');
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {},
  components: {
    DataWarehouse: require('@/views/datagovernance/governancetheme/viewprocess/components/data-warehouse.vue').default,
  },
};
</script>
<style lang="less" scoped>
.center-flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
[data-theme='dark'] {
  .page-evaluationmanagement {
    background-image: url('~@/assets/img/systemconfiguration/devinputbg.png');
    .content {
      .title {
        background: url('~@/assets/img/home/<USER>/home-config-title-dark.png') no-repeat;
      }
    }
  }
}
[data-theme='light'] {
  .page-evaluationmanagement {
    .content {
      .title {
        background: url('~@/assets/img/home/<USER>/home-config-title-light.png') no-repeat;
      }
    }
  }
}
[data-theme='deepBlue'] {
  .page-evaluationmanagement {
    .content {
      .title {
        background: url('~@/assets/img/home/<USER>/home-config-title-deepBlue.png') no-repeat;
      }
    }
  }
}

.page-evaluationmanagement {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-image: url('~@/assets/img/systemconfiguration/devinputbg-light.png');
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-repeat: no-repeat;
  .device-header {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    // padding: 0 20px;
  }
  .content {
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -50%);
    .title {
      position: relative;
      height: 60px;
      width: 427px;
      background-size: cover;
      z-index: 1;
      .main-title {
        display: inline-block;
        font-size: 26px;
        font-weight: bold;
        line-height: 27px;
        color: #ffffff;
        text-shadow: 0 0 10px #60a3f9;
        padding: 25px 0 6px 109px;
        z-index: 2;
      }
      .subtitle {
        display: inline-block;
        font-size: 26px;
        font-weight: bold;
        line-height: 20px;
        color: #04102a;
        position: absolute;
        left: 27%;
        top: 57%;
        z-index: -2;
      }
    }
  }
}
</style>
