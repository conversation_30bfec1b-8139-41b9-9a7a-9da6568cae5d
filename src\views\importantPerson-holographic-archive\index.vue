<template>
  <!-- 1 -->
  <ImportantPerson
    :bizLibType="[1]"
    :isNewGoInfo="true"
    :goInfo="goArchivesInfo"
  ></ImportantPerson>
</template>

<script>
import Important<PERSON><PERSON> from "@/views/holographic-archives/one-person-one-archives/juvenile-archive/index.vue";
export default {
  name: "ImportantPersonHolographicArchives",
  components: { ImportantPerson },
  data() {
    return {};
  },
  methods: {
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        path: "/importantPerson-archive/people-dashboard",
        query: {
          archiveNo: item.archiveNo,
          source: "people",
          initialArchiveNo: item.archiveNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
