<!--
 * @Date: 2025-01-15 14:05:53
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-06 10:55:21
 * @FilePath: \icbd-view\src\views\juvenile\components\collect\school-out-alarm.vue
-->
<template>
  <div class="school-card-box">
    <div class="header">
      <div class="icon-box2"><i class="iconfont icon-hotel"></i></div>
      <div class="title">{{ data.communityName }}</div>
    </div>
    <div class="content">
      <div class="image-box">
        <ui-image type="people" :src="data.traitImg" alt="" />
      </div>
      <div class="list-box">
        <div class="item" v-for="item in itemList" :key="item.param">
          <template v-if="data[item.param]">
            <div class="info-name">
              <Tooltip
                :content="item.title"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont" :class="item.icon"></i>
              </Tooltip>
            </div>
            <div v-if="item.param == 'appearNumber'">
              近一周出现<span :style="item.style">{{
                data[item.param] || "--"
              }}</span
              >天
            </div>
            <div v-else class="info-content info-color-sub" :style="item.style">
              {{ data[item.param] || "--" }}
            </div>
          </template>
        </div>
      </div>
      <div class="info-right">
        <ui-image class="img" :src="typeIcon[data.handlerStatus]"></ui-image>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    // 数据
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      imageTest: require("@/assets/img/face.png") || "",
      itemList: [
        {
          icon: "icon-xingming",
          param: "name",
          title: "姓名",
          style: {},
        },
        {
          icon: "icon-shenfenzheng",
          param: "idCardNo",
          title: "身份证",
          style: {},
        },
        {
          icon: "icon-juhe",
          param: "total",
          title: "出现次数",
          style: {},
        },
        {
          icon: "icon-060delay",
          param: "alarmTime",
          title: "报警时间",
          style: {},
        },
        {
          icon: "icon-chuxian",
          param: "appearNumber",
          title: "近一周出现",
          style: { color: "#2C86F8" },
        },
      ],
      typeIcon: {
        0: require("@/assets/img/target/unproces.png"),
        1: require("@/assets/img/target/valid.png"),
        2: require("@/assets/img/target/invalid.png"),
      },
    };
  },
};
</script>

<style lang="less" scoped>
.school-card-box {
  width: 342px;
  // height: 140px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  position: relative;
  border-radius: 4px;
  cursor: pointer;
  .header {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #2c86f8;
    border-bottom: 1px solid #d3d7de;
    padding: 5px 10px;
    .icon-box2 {
      width: 20px;
      height: 20px;
      background: #2c86f8;
      border-radius: 4px;
      text-align: center;
      line-height: 20px;
      i {
        color: #fff;
      }
    }
    .title {
      font-size: 15px;
      font-weight: 700;
    }
  }
  .content {
    padding: 10px;
    display: flex;
    gap: 10px;
    position: relative;
    .image-box {
      width: 139px;
      height: 139px;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .list-box {
      margin-top: 12px;
      z-index: 1;
      .item {
        display: flex;
        gap: 8px;
        font-size: 14px;
        .info-content {
          max-width: 130px;
          text-overflow: ellipsis;
          text-wrap: nowrap;
          overflow: hidden;
        }
      }
      .label-box {
        display: flex;
        gap: 4px;
        margin-top: 6px;
      }
    }
    .info-right {
      width: 80px;
      height: 80px;
      position: absolute;
      right: 0;
      bottom: 0;
    }
  }
}
</style>
