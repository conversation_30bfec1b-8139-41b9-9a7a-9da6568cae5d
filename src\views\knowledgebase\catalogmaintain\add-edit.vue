<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles" :footer-hide="isView" @query="query">
    <Form class="form" ref="form" autocomplete="off" label-position="right" :rules="ruleCustom" :model="formCustom">
      <FormItem class="left-item" label="目录名称" prop="name">
        <Input
          v-if="!isView"
          type="text"
          class="form-width"
          v-model="formCustom.name"
          placeholder="请输入目录名称"
          :maxlength="20"
        >
        </Input>
        <span v-else class="base-text-color">{{ formCustom.name }} </span>
      </FormItem>
      <FormItem class="left-item" label="所属路径" prop="parentId">
        <!-- 编辑不可修改parentId 后端做起来太复杂 -->
        <api-tree
          v-if="isAdd"
          ref="sourceTree"
          placeholder="请选择所属路径"
          :default-props="defaultModuleProps"
          :tree-props="treeModuleProps"
          :list="catalog"
          :need-query="false"
          @selectedTree="selectedTree"
          @clearTree="clearTree"
        >
        </api-tree>
        <span v-else class="base-text-color">{{ formCustom.parentName }} </span>
      </FormItem>
      <FormItem class="left-item" label="描述">
        <Input
          v-if="!isView"
          type="textarea"
          class="form-width"
          v-model="formCustom.remark"
          placeholder="请简要说明该目录"
          :rows="5"
          :maxlength="256"
        ></Input>
        <span v-else class="base-text-color desc">
          {{ formCustom.remark || '未知' }}
        </span>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import knowledgebase from '@/config/api/knowledgebase';
export default {
  props: {
    value: {},
    modalAction: {},
    defaultForm: {},
    catalog: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      defaultModuleProps: {
        label: 'name',
        children: 'children',
      },
      treeModuleProps: {
        nodeKey: 'id',
        id: '',
      },
      formCustom: {
        id: '',
        name: '',
        parentId: '',
        parentName: '',
        remark: '',
      },
      ruleCustom: {
        name: [{ required: true, message: '请填写目录名称', trigger: 'blur' }],
      },
    };
  },
  created() {},
  methods: {
    selectedTree(val) {
      this.formCustom.parentId = val.id;
    },
    clearTree() {
      this.formCustom.parentId = '';
    },
    query() {
      try {
        this.$refs['form'].validate(async (valid) => {
          if (valid) {
            let fetchUrl = null;
            if (this.isAdd) {
              fetchUrl = knowledgebase.addCatalogue;
            } else {
              fetchUrl = knowledgebase.updateCatalogue;
              delete this.formCustom.parentId;
              delete this.formCustom.parentName;
            }
            const res = await this.$http.post(fetchUrl, this.formCustom);
            this.$Message.success(res.data.msg);
            this.visible = false;
            this.$emit('update');
          } else {
            this.$Message.error('请填写目录名称');
          }
        });
      } catch (err) {
        console.log(err);
      }
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.treeModuleProps.id = '';
      this.formCustom = {
        id: '',
        name: '',
        parentId: '',
        parentName: '',
        remark: '',
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (this.isAdd) {
        this.resetFields();
      }
      this.visible = val;
    },
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          if (length > 0) {
            this.treeModuleProps.id = this.defaultForm.parentId;
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                switch (key) {
                  default:
                    this.formCustom[key] = val[key];
                    break;
                }
              }
            });
          }
        });
      },
      immediate: true,
    },
  },
  computed: {
    isAdd() {
      return this.modalAction.action === 'add';
    },
    isView() {
      return this.modalAction.action === 'view';
    },
  },
  components: {
    ApiTree: require('@/components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 130px;
@inputWidth: 300px;
@{_deep} .ivu-modal-body {
  max-height: 320px;
  overflow-y: auto;
}
@{_deep}.ivu-form-item {
  width: 100%;
  margin-right: 0;
  margin-bottom: 20px;
}
.left-item {
  @{_deep} .ivu-form-item-error-tip {
    margin-left: @leftMargin;
  }
  @{_deep}.ivu-form-item-label {
    width: @leftMargin;
  }
}
.form-width {
  width: 300px;
}
.desc {
  word-wrap: break-word;
}
@{_deep} .drop-tree {
  width: @inputWidth;
}
@{_deep} .ivu-select-selection {
  width: @inputWidth;
}
</style>
