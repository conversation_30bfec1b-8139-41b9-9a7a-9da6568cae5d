export default {
  getEvaluationIndexByPage: '/ivdg-evaluation-app/evaluation/app/index/pageList', //查询指标数据分页列表
  getEvaluationIndexById: '/ivdg-evaluation-app/evaluation/app/index/view', //查询指标数据byId
  updateEvaluationIndex: '/ivdg-evaluation-app/evaluation/app/index/update', //修改指标

  getEvaluationIndexBasicParamSettingById: '/ivdg-evaluation-app/evaluation/indexBasicParamSetting/view', //根据标识符获取基础数据指标参数设置数据信息
  getDefaultParamSettingByIndexId:
    '/ivdg-evaluation-app/evaluation/indexBasicParamSetting/getDefaultParamSettingByIndexId', //根据考核指标表ID查询系统默认的参数配置
  updateIndexBasicParamSetting: '/ivdg-evaluation-app/evaluation/indexBasicParamSetting/update', //修改基础数据指标参数设置
  getBaseIndexParamsById: '/ivdg-evaluation-app/evaluation/indexBasicParamSetting/getByEvaluationIndexId', //根据指标表ID查询基础数据指标参数设置

  findByCondition: '/ivdg-evaluation-app/evaluation/app/taskScheme/pageList', //测评分页
  addOrUpdate: '/ivdg-evaluation-app/evaluation/task/add', //新增评测
  updateOrUpdate: '/ivdg-evaluation-app/evaluation/task/update', //编辑评测
  tEvaluatingContext: '/ivdg-evaluation-app/evaluation/task/removeById', //通过id删除测评
  getTaskById: '/ivdg-evaluation-app/evaluation/task/getById', //通过id查询任务详情
  getPageResults: '/ivdg-evaluation-app/evaluation/result/pageResults', //评测结果分页列表
  getExportResultByIds: '/ivdg-evaluation-app/evaluation/result/exportResultById', //导出考核结果

  // 方案管理
  getProjram: '/ivdg-evaluation-app/evaluation/app/scheme/schemePageList', // 获取所有方案
  tEvaluatingProgram: '/ivdg-evaluation-app/evaluation/app/scheme/addScheme', // 新增方案
  deleteScheme: '/ivdg-evaluation-app/evaluation/app/scheme/removeScheme', // 删除方案
  // getOptionalSchemes: '/  ivdg-evaluation-management-service/evaluation/scheme/getOptionalSchemes', //新增考核任务方案查询
  getEvaluatingProgramIndexByPage: '/ivdg-evaluation-app/evaluation/app/scheme/pageRelatedIndexes', //获取方案列表数据
  getPageOptionalIndexes: '/ivdg-evaluation-app/evaluation/app/scheme/pageOptionalIndexes', //查询可关联指标列表
  getContingencyIndexes: '/ivdg-evaluation-app/evaluation/app/scheme/associateIndex', //保存关联指标
  batchRemoveSchemeIndex: '/ivdg-evaluation-app/evaluation/app/scheme/batchRemoveSchemeIndex', //删除方案单个指标
  putSchemeIndexRemoveByIds: '/ivdg-evaluation-app/evaluation/schemeIndex/removeByIds', //删除方案多个指标
  getConfigIndex: '/ivdg-evaluation-app/evaluation/schemeIndex/configIndex', //配置方案单个指标
  getSchemeIndexById: '/ivdg-evaluation-app/evaluation/schemeIndex/getById', //关联指标详细信息
  getEngineIndexById: '/ivdg-evaluation-app/evaluation/app/taskScheme/getApiJson', //接口稳定性配置
  getOptionAlgVendors: '/ivdg-evaluation-app/evaluation/schemeIndex/getOptionAlgVendors', //获取算法厂商
  newGetOptionAlgVendors: '/ivdg-evaluation-app/evaluation/app/scheme/getOptionAlgVendors', //获取算法厂商
  sourceAll: '/ivdg-asset-app/api/source/all', // 获取所有接口信息
  sourceUpdate: '/ivdg-asset-app/api/source/update', // 接口编辑

  // 车辆评测详情
  deviceList: '/ivdg-evaluation-app/evalute/device/deviceList', //设备列表接口-左侧列表,
  deviceResultOverviewCar: '/ivdg-evaluation-app/evalute/device/deviceResultOverview', //查询设备信息
  devicePageList: '/ivdg-evaluation-app/evalute/device/devicePageList', //根节点列表
  pageList: '/ivdg-evaluation-app/evalute/vehicle/pageList', //具体某个设备列表
  getTableHeader: '/ivdg-evaluation-app/detail/header/getTableHeader', //表头
  getListByVehicleDetailId: '/ivdg-evaluation-app/evalute/vehicle/getListByVehicleDetailId', // 算法规则列数据
  // 视频流
  deviceResultOverview: '/ivdg-evaluation-app/evalute/video/deviceResultOverview', //视频流设备统计数据
  videoPageList: '/ivdg-evaluation-app/evalute/video/pageList', //查询测评数据分页列表
  checkDeviceStatus: '/ivdg-evaluation-app/evalute/video/checkDeviceStatus', //检测设备状态

  // 人脸评测详情接口
  facePageList: '/ivdg-evaluation-app/evaluation/face/device/detail/pageList', //人脸卡口结果详情（外）--分页查询
  faceDetailPageList: '/ivdg-evaluation-app/evaluation/face/detail/pageList', //人脸卡口结果详情（内）--分页查询
  faceDeviceResultOverview: '/ivdg-evaluation-app/evaluation/face/device/detail/faceDeviceResultOverview', //人脸数据结果详情-检测结果统计
  faceResultOverview: '/ivdg-evaluation-app/evaluation/face/device/detail/faceResultOverview', //人脸数据结果详情-检测结果统计
  getDeviceDataList: '/ivdg-evaluation-app/evaluation/face/device/detail/getDeviceDataList', //人脸卡口结果详情--查询设备数据列表-左侧菜单

  implementAdd: '/ivdg-evaluation-app/evaluation/result/add', //执行
  getByAddressableResultId: '/ivdg-evaluation-app/evaluation/focusTrackUrlAvailable/getByResultId', //轨迹图片可访问率统计接口
  pageTrackUrlAvailableDetails: '/ivdg-evaluation-app/evaluation/focusTrackUrlAvailable/pageTrackUrlAvailableDetails', //轨迹图片可访问率统计图片模式列表数据接口
  pageTrackUrlAvailableClusters: '/ivdg-evaluation-app/evaluation/focusTrackUrlAvailable/pageTrackUrlAvailableClusters', //轨迹图片可访问率统计聚档模式列表数据接口
  pageTrackCatchDetails: '/ivdg-evaluation-app/evaluation/focusTrack/pageTrackCatchDetails', //轨迹图片可访问率统计聚档模式详情接口

  // 评测结果详情
  queryEvaluationBasicRecord: '/ivdg-evaluation-app/evaluation/basic/record/queryEvaluationBasicRecord', // 查询基础数据指标检测结果记录
  queryErrorMessageList: '/ivdg-evaluation-app/evaluationDeviceResult/queryErrorMessageList', // 获取填报准确率结果错误原因列表
  queryEvaluationDevicePageList: '/ivdg-evaluation-app/evaluationDeviceResult/queryEvaluationDevicePageList', // 获取设备填报准确率结果不合格设备列表
  queryEvaluationDeviceResult: '/ivdg-evaluation-app/evaluationDeviceResult/queryEvaluationDeviceResult', // 获取设备填报准确率结果不合格详情查询

  // 重点人员基础接口
  getPageAccuracies: '/ivdg-evaluation-app/evaluation/focusAccuracy/pageAccuracies', //重点人员基础信息列表
  getStatAbnormalReasons: '/ivdg-evaluation-app/evaluation/focusAccuracy/getStatAbnormalReasons', //重点人员基础信息填报准确率异常数据标签列表
  getStatByResultId: '/ivdg-evaluation-app/evaluation/focusAccuracy/getStatByResultId', //重点人员基础信息填报准确率统计
  // getPageAccuracyDetails: '/ivdg-evaluation-app/evaluation/focusAccuracy/pageAccuracyDetails', //重点人员基础信息不合格列表
  getPageTrackRealDetails: '/ivdg-evaluation-app/evaluation/focusTrackReal/pageTrackRealDetails', //重点人员实时轨迹上传及时性结果列表
  getByResultId: '/ivdg-evaluation-app/evaluation/focusTrackReal/getByResultId', //重点人员实时轨迹上传及时性统计
  getPageTrackDetails: '/ivdg-evaluation-app/evaluation/focusTrack/pageTrackDetails', //重点人员轨迹准确率结果详情列表
  getTrackByResultId: '/ivdg-evaluation-app/evaluation/focusTrack/getByResultId', //重点人员轨迹准确率结果统计
  getPageClusterResult: '/ivdg-evaluation-app/evaluation/focusTrack/pageClusterResult', // 重点数据聚档可用性结果详情

  queryStatOrgWithTask: '/ivdg-evaluation-app/evaluation/task/statOrgWithTask', // 获取有考核任务指标
  // 车辆二三类结果详情
  currentDetectData: '/ivdg-evaluation-app/evalute/video/currentDetectData', // 获取本次检测联网率/可调阅率/字母合规率/时钟准确率(提升率相关指标)
  promotionRateStatisticalData: '/ivdg-evaluation-app/evalute/video/promotionRateStatisticalData', // 查询测评视频数据(提升率相关指标)
  queryHistoryData: '/ivdg-evaluation-app/evalute/video/queryHistoryData', // 获取之前评测批次数据
  getHomePageEvaluationData: '/ivdg-evaluation-app/evaluation/result/getHomePageEvaluationData', //治理评测首页数据统计

  // 导出
  basicsExportByResultId: '/ivdg-evaluation-app/evaluation/focusAccuracy/exportByResultId', // 基础信息准确率导出
  exportAccuracy: '/ivdg-evaluation-app/evaluationDeviceResult/exportAccuracy', // 准确率导出
  exportInputIndex: '/ivdg-evaluation-app/evaluation/basic/record/exportInputIndex', //建档率导出
  exportFullDirIndex: '/ivdg-evaluation-app/evaluation/basic/record/exportFullDirIndex', // 全量目录导出
  exportEmphasisLocationIndex: '/ivdg-evaluation-app/evaluation/basic/record/exportEmphasisLocationIndex', // 视频图像设备位置类型完整率结果导出

  // 车辆
  carimportExcel: '/ivdg-evaluation-app/evalute/device/importExcel', // 车辆导出
  // 视频流导出
  vedioimportExcel: '/ivdg-evaluation-app/evalute/video/vedioimportExcel',
  //二三类导出
  importExcel: '/ivdg-evaluation-app/evalute/video/twoOrThreeCategories',

  exportEmphasisQuantityForProvince: '/ivdg-evaluation-app/evaluation/basic/record/exportEmphasisQuantityForProvince', // 重点位置类型视频图像设备数量达标率结果导出--省
  exportEmphasisQuantityForCity: '/ivdg-evaluation-app/evaluation/basic/record/exportEmphasisQuantityForCity', // 重点位置类型视频图像设备数量达标率结果导出--市
  exportEmphasisQuantityForArea: '/ivdg-evaluation-app/evaluation/basic/record/exportEmphasisQuantityForArea', // 重点位置类型视频图像设备数量达标率结果导出--区县派出所
  exportQuantityStandardForProvince: '/ivdg-evaluation-app/evaluation/basic/record/exportQuantityStandardForProvince', // 数量达标率结果导出--省
  exportQuantityStandardForCity: '/ivdg-evaluation-app/evaluation/basic/record/exportQuantityStandardForCity', // 数量达标率结果导出--市
  exportQuantityStandardForArea: '/ivdg-evaluation-app/evaluation/basic/record/exportQuantityStandardForArea', // 数量达标率结果导出--区县派出所
  exportFaceDeviceDetailData: '/ivdg-evaluation-app/evaluation/face/device/detail/exportFaceDeviceDetailData', // 导出人脸指标数据

  getOptionalSchemes: '/ivdg-evaluation-app/evaluation/scheme/getOptionalSchemes', // 考核列表

  getEvaluationStatisticsType: '/ivdg-evaluation-app/evaluation/app/record/getRecordType', //检测记录左侧

  getEvaluationOverview: '/ivdg-evaluation-app/evaluation/statistics/getEvaluationOverview', //治理评测-检测概览
  // getEvaluationOverviewV2:
  //   '/ivdg-evaluation-app/evaluation/statistics/getEvaluationOverviewV2', //治理评测-检测概览
  getBasicOverviewV2: '/ivdg-evaluation-app/evaluation/app/record/getBasicOverviewV2', //治理评测-检测概览
  getEvaluationRecentlyTaskList: '/ivdg-evaluation-app/evaluation/app/record/getRecentlyTaskList', //治理评测-任务列表和最新更新时间
  // getEvaluationStatistics:
  //   '/ivdg-evaluation-app/evaluation/statistics/getEvaluationStatistics', //治理评测统计
  // getEvaluationStatisticsCount:
  //   '/ivdg-evaluation-app/evaluation/statistics/getEvaluationStatisticsCount', //治理评测统计
  getBasicStatisticsCount: '/ivdg-evaluation-app/evaluation/app/record/getBasicStatisticsCount', //治理评测统计
  getBasicRecordPageList: '/ivdg-evaluation-app/evaluation/app/getBasicRecordPageList', //检测记录-各检测项-列表
  getDeviceSbcjqyData: '/ivdg-asset-app/deviceSbcjqyData/queryDataList', //查询设备采集区域数据

  updateIndexRulePropertyList: '/ivdg-evaluation-app/evaluation/rule/updateIndexRulePropertyList', //编辑规则的字段配置
  queryIndexRulePropertyList: '/ivdg-evaluation-app/evaluation/rule/queryIndexRulePropertyList', //查询规则的字段配置
  updateIndexRuleProperty: '/ivdg-evaluation-app/evaluation/rule/updateIndexRuleProperty', //编辑规则配置
  queryByIndexRuleId: '/ivdg-evaluation-app/evaluation/rule/queryByIndexRuleId', //查询规则配置

  // 人脸联网
  netWorkPageList: '/ivdg-evaluation-app/connectInternet/detail/pageList', // 联网率设备列表
  onlinePageList: '/ivdg-evaluation-app/evaluationOnlineImageResult/pageList', // 在线率设备列表
  pageListForEvaluation: '/ivdg-asset-app/faceLib/pageListForEvaluation', // 在线率图片人脸列表
  carForEvaluation: '/ivdg-asset-app/vehicleCapture/pageListForEvaluation', // 在线率图片车辆列表
  netWorkExport: '/ivdg-evaluation-app/connectInternet/detail/export', // 联网率导出
  onlineExport: '/ivdg-evaluation-app/evaluationOnlineImageResult/export', // 在线率导出

  // 考核方案
  schemeAdd: '/ivdg-examination-app/examination/scheme/add', // 新增考核方案
  schemePageList: '/ivdg-examination-app/examination/scheme/pageList', // 查询考核方案分页列表
  schemeUpdata: '/ivdg-examination-app/examination/scheme/update', // 考核方案编辑
  schemeGetSchemeContent: '/ivdg-examination-app/examination/scheme/getSchemeContent', // 根据方案id查询下面ll的所有内容
  querySchemeContent: '/ivdg-examination-app/examination/scheme/querySchemeContent', // 编辑回显内容
  updateSchemeContent: '/ivdg-examination-app/examination/scheme/updateSchemeContent', // 编辑确定
  saveSchemeContent: '/ivdg-examination-app/examination/scheme/saveSchemeContent', // 新增考核接口
  removeSchemeContent: '/ivdg-examination-app/examination/scheme/removeSchemeContent', // 删除接口
  enableDisable: '/ivdg-examination-app/examination/scheme/enableDisable', // 考核任务是否启用
  schemeDel: '/ivdg-examination-app/examination/scheme/remove', //删除考核方案
  indexList: '/ivdg-examination-app/examination/scheme/indexList', // 获取考核指标
  categoryList: '/ivdg-examination-app/examination/scheme/categoryList', //获取分类
  uploadStatistics: '/ivdg-examination-service/examination/statistics/upload', //上传方案文档
  selectSchemeUrl: '/ivdg-examination-service/examination/statistics/selectSchemeUrl', //下载方案文档
  getExamDictConfig: '/ivdg-examination-app/examination/scheme/getExamDictConfig', //查询考核相关字典

  // 考核任务
  taskPageList: '/ivdg-examination-app/examination/task/pageList', // 查询考核任务分页列表
  getAlltaskList: '/ivdg-examination-app/examination/task/allInfoList', // 查询所有考核任务列表
  queryEvaluationTaskById: '/ivdg-examination-app/examination/task/queryEvaluationTaskById', //编辑回显内容
  queryEvaluationTaskData: '/ivdg-examination-app/examination/task/queryEvaluationTaskData', //检测任务接口
  getAllExamScheme: '/ivdg-examination-app/examination/task/getAllExamScheme', //查询考核方案列表
  queryEvaluationTask: '/ivdg-examination-app/examination/task/queryEvaluationTask', //考核对象关联的评测任务
  queryTaskContent: '/ivdg-examination-app/examination/task/queryTaskContent', //考核方案关联的考核内容
  updateEvaluationTask: '/ivdg-examination-app/examination/task/updateEvaluationTask', //编辑考核任务
  pauseStart: '/ivdg-examination-app/examination/task/pauseStart', //暂停启动任务
  executeNow: '/ivdg-examination-app/examination/task/executeNow', //立即执行任务
  deleteTask: '/ivdg-examination-app/examination/task/deleteTask', //删除任务

  getExamStatistics: '/ivdg-examination-service/examination/statistics/examStatistics', //考核结果
  statisticalModel: '/ivdg-examination-service/examination/statistics/statisticalModel', //考核统计报表
  getExamStatisticsDetail: '/ivdg-examination-service/examination/statistics/examStatisticsDetail', //考核结果详情
  exportAssessment: '/ivdg-examination-service/examination/statistics/importExcel', //导出考核结果
  exportExcel: '/ivdg-examination-service/examination/statistics/exportExcel', //收缩导出考核结果
  exportResultExcel: '/ivdg-examination-service/examination/statistics/exportResultExcel', //展开导出考核结果
  addExamSchemeTask: '/ivdg-examination-app/examination/task/addExamSchemeTask', //新增考核任务
  getTaskChangeTaskState: '/ivdg-evaluation-app/evaluation/task/changeTaskState', //暂停/恢复评测
  getStatResult: '/ivdg-evaluation-app/evaluation/result/getStatResult', //测评统计
  queryExamContentItemIndexResultList:
    '/ivdg-examination-service/examination/statistics/queryExamContentItemIndexResultList', //考核成绩详情
  updateExamByDetailId: '/ivdg-examination-service/examination/statistics/updateExamByDetailId', //考核成绩详情 更新
  queryExamContentItemIndexResultListExport:
    '/ivdg-examination-service/examination/statistics/queryExamContentItemIndexResultListExport', //考核成绩详情 导出
  getExamItemTypeTwo: '/ivdg-examination-service/examination/statistics/getExamItemTypeTwo', //检测设备状态

  focusRelateList: '/ivdg-evaluation-app/evaluation/focusRelate/pageList', //轨迹关联率结果详情
  getResultStatistics: '/ivdg-evaluation-app/evaluation/focusRelate/getResultStatistics', //轨迹关联率统计
  personAccuracyCount: '/ivdg-evaluation-app/evaluation/person/personAccuracyCount', //人像准确率统计
  accuracyLidt: '/ivdg-evaluation-app/evaluation/person/pageList', //人像准确率列表
  personAccuracyDetails: '/ivdg-evaluation-app/evaluation/person/personAccuracyDetails', //人像准确率详情
  personArchives: '/ivdg-evaluation-app/evaluation/person/pageConfidenceRateList ', //人像置信率列表
  personArchivesDetailsCount: '/ivdg-evaluation-app/evaluation/person/importPersonConfidenceRateDetailsCount ', //人像置信率统计

  repeatList: '/ivdg-evaluation-app/evaluation/focusRepeat/pageList', //轨迹重复率列表
  repeatStatistics: '/ivdg-evaluation-app/evaluation/focusRepeat/getResultStatistics', //轨迹重复统计
  queryTaskHistoryList: '/ivdg-examination-app/examination/task/queryTaskHistoryList', //查询考核任务历史分页

  // 人脸抓拍数据上传完整率结果统计
  getFaceResultStatistics: '/ivdg-evaluation-app/evaluation/face/detail/resultStatistics', // 广东新增指标-统计
  //查询人脸抓拍数据上传完整率分页列表
  getFacePageListForCompletenessOfRate: '/ivdg-evaluation-app/evaluation/face/detail/pageListForCompletenessOfRate',
  getVehiclePageList: '/ivdg-evaluation-app/evalute/vehicle/pageList',
  // 视频流检测合格质量
  subordinateTopList: '/ivdg-evaluation-app/evalute/video/subordinateTop5',
  //接口稳定性列表
  InterfaceList: '/ivdg-evaluation-app/evaluation/api/pageList',
  //接口稳定性详情列表
  InterdetailfaceList: '/ivdg-evaluation-app/evaluation/api/detail/pageList',
  //接口稳定性统计 检测结果详情-上面的数量
  getDetailCount: '/ivdg-evaluation-app/evaluation/app/api/getDetailCount',
  //接口稳定性图片地址详情
  Interdetail: '/ivdg-evaluation-app/evaluation/app/api/detail',
  // 接口稳定性情况统计-上面的数量
  getPageListCount: '/ivdg-evaluation-app/evaluation/app/api/getPageListCount',
  // 查询【接口稳定性调阅详情】分页列表
  detailPageList: '/ivdg-evaluation-app/evaluation/app/api/detail/pageList',
  // 查询组织机构下面的调用接口分页列表
  orgCodePageList: '/ivdg-evaluation-app/evaluation/app/api/detail/orgCodePageList',

  // 1.1版本
  //检测管理
  evaluationPageList: '/ivdg-evaluation-app/evaluation/app/taskScheme/pageList', //评测任务列表分页查询
  schemeList: '/ivdg-evaluation-app/evaluation/app/scheme/list', //方案查询
  addTaskScheme: '/ivdg-evaluation-app/evaluation/app/taskScheme/addTaskScheme', //新增/修改任务
  getTaskPermissionList: '/ivdg-evaluation-app/evaluation/app/taskScheme/getTaskPermissionList', //查询任务详情
  deleteTaskScheme: '/ivdg-evaluation-app/evaluation/app/taskScheme/deleteTaskScheme', //删除任务
  indexDetailStatistics: '/ivdg-evaluation-app/evaluation/app/taskScheme/indexDetailStatistics', //评测任务指标统计接口
  getTaskIndexGeneralConfig: '/ivdg-evaluation-app/evaluation/app/taskScheme/getTaskIndexGeneralConfig', //获取配置指标明细
  getTaskIndexConfig: '/ivdg-evaluation-app/evaluation/app/taskScheme/getTaskIndexConfig', //查询指标配置信息
  addTaskIndexConfig: '/ivdg-evaluation-app/evaluation/app/taskScheme/addTaskIndexConfig', //新增指标配置
  getOrgDataByRegioncode: '/qsdi-system-service/system/org/getOrglistByRegioncodeWithDataScope', // 获取组织机构
  getUserOrgDataByRegioncode: '/qsdi-system-service/system/org/getUserOrgDataByRegioncode', // 根据行政区划查询组织机构
  taskManageStatistics: '/ivdg-evaluation-app/evaluation/app/taskScheme/taskManageStatistics', //任务管理-指标状态统计
  optionAlgVendors: '/ivdg-evaluation-app/evaluation/app/scheme/getOptionAlgVendors', //算法厂商
  getCheckRuleByIndexType: '/ivdg-evaluation-app/evaluation/app/rule/getCheckRuleByIndexType', //根据indexType获取评测规则
  editIndexRulePropertyList: '/ivdg-evaluation-app/evaluation/app/rule/updateIndexRulePropertyList', //编辑规则的字段配置
  inquireQueryRuleProperty: '/ivdg-evaluation-app/evaluation/app/rule/queryRulePropertyByTaskIndexIdAndRuleId', //查询规则配置对象
  inquireIndexRulePropertyList: '/ivdg-evaluation-app/evaluation/app/rule/queryIndexRulePropertyList', //查询规则的字段配置列表

  taskIndexPageList: '/ivdg-evaluation-app/evaluation/app/taskScheme/taskIndexPageList', //指标详情-指标列表查询
  triggerIndexJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/triggerIndexJob', //立即执行指标任务
  pauseStartIndexJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/pauseStartIndexJob', //指标详情-暂停/启用接口
  queryTaskScheme: '/ivdg-evaluation-app/evaluation/app/taskResult/queryTaskScheme', //检测结果-任务列表查询
  taskIndexResultPageList: '/ivdg-evaluation-app/evaluation/app/taskResult/taskIndexResultPageList', //检测结果-结果列表查询
  getIndexResultHistoryRecord: '/ivdg-evaluation-app/evaluation/app/taskResult/getIndexResultHistoryRecord', //检测结果-指标结果历史记录
  getIndexStatistics: '/ivdg-evaluation-app/evaluation/app/index/getIndexStatistics', //检测结果-指标结果历史记录
  postDeleteIndexResult: '/ivdg-evaluation-app/evaluation/app/taskResult/deleteIndexResult', //检测结果-删除指标结果历史记录
  getOrglistByRegioncodeWithDataScope: '/qsdi-system-service/system/org/getOrglistByRegioncodeWithDataScope', //根据区域编码获取组织机构
  terminateRunningJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/terminateRunningJob', //检测结果-指标结果历史记录
  getPopUpData: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getPopUpData', //检测结果 轨迹重复率弹框
  // 江苏- 评测结果
  getPageJiangsuEvaluationIndexResult:
    '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getPageJiangsuEvaluationIndexResultByRegionCode',
  getIndexDataCount: '/ivdg-evaluation-app/evaluation/app/graphicalStatistics/getIndexDataCount',

  queryIndexInspectionRecordDetail: '/ivdg-examination-service/examination/statistics/queryIndexInspectionRecordDetail', //获取海康详情
  exportInspectionRecordDetail: '/ivdg-examination-service/examination/statistics/exportInspectionRecordDetail', //导出海康详情

  //视频流、人卡、车卡统计
  getEvaluationDeviceOnlineStatisticsPageList: '/ivdg-evaluation-app/evaluation/deviceOnlineStatistics/pageList',
  getEvaluationDeviceOnlineStatisticsDayStatistics:
    '/ivdg-evaluation-app/evaluation/deviceOnlineStatistics/dayStatistics',
  postEvaluationDeviceOnlineStatisticsDayExportList:
    '/ivdg-evaluation-app/evaluation/deviceOnlineStatistics/exportList',

  //考核成绩手动填报
  exportEditResultExcel: '/ivdg-examination-service/examination/statistics/exportResultExcel', //手动录入考核成绩导出
  importEditResultExcel: '/ivdg-examination-service/examination/statistics/importResultExcel', //手动录入考核成绩导入
  postStatisticsSave: '/ivdg-examination-service/examination/statistics/save',
  getHistoryTrend: '/ivdg-evaluation-app/evaluation/app/taskResult/getHistoryTrend',
  getNumRankInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getNumRankInfo', //检测结果详情-下级地市情况
  getCircularGraphsInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getCircularGraphsInfo', //检测结果详情-不合格原因环形图
  getBarGraphRankInfo: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getBarGraphRankInfo', //检测结果详情-区县上报情况

  batchTerminateRunningJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/batchTerminateRunningJob', //批量--终止执行中指标任务
  batchTriggerIndexJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/batchTriggerIndexJob', //批量--立即执行指标任务
  pauseStartTaskSchemeJob: '/ivdg-evaluation-app/evaluation/app/taskScheme/pauseStartTaskSchemeJob', //批量暂停/启用任务下指标的接口

  addComposite: '/ivdg-evaluation-app/evaluation/app/index/addComposite', // 新增指标

  getTaskIndexFilterStrategy: '/ivdg-evaluation-app/evaluation/app/taskScheme/getTaskIndexFilterStrategy', //选择设备根据任务过滤指标
  getIndexBatchByTaskSchemeIdAndIndex:
    '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getIndexBatchByTaskSchemeIdAndIndex', //选择设备根据 任务方案id 及indexid 查询检测时间
  recordDevicePagelist: '/ivdg-evaluation-app/evaluation/app/record/device/pagelist', //选择设备根据评测结果获取设备信息

  // 首页统计
  getFlowChart: '/ivdg-evaluation-app/evaluation/app/home/<USER>',
  getHomeBasicQuantityStandardDataV2: '/ivdg-evaluation-app/evaluation/app/home/<USER>/getHomeBasicQuantityStandardDataV2',

  // 共享联网平台在线率配置页面
  downloadTemplate: '/ivdg-evaluation-app/evaluation/app/taskScheme/downloadTemplate',
  importDeviceFile: '/ivdg-evaluation-app/evaluation/app/taskScheme/importDeviceFile',
  //新建工单获取指标
  taskIndexPageListByWork: '/ivdg-evaluation-app/evaluation/app/taskScheme/taskIndexPageListByWork', //指标详情-指标列表查询
  getHistoryTimeList: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getHistoryTimeList', //获取最近一个月检测结果日期
  getHistoryBatchListByDate: '/ivdg-evaluation-app/evaluation/app/taskIndexResultDetail/getHistoryBatchListByDate', //获取时间段的检测结果
  resultComparison: '/ivdg-evaluation-app/cascade/issue/api/resultComparison', //结果对比
  resultReview: '/ivdg-evaluation-app/cascade/issue/resultComparison', //结果对比 复核
  resultComparisonExport: '/ivdg-evaluation-app/cascade/issue/resultComparisonExport', //结果对比 导出

  checkTaskIndexConfig: '/ivdg-evaluation-app/evaluation/app/taskScheme/checkTaskIndexConfig', // 指标之间是否已存在依赖关系
  getDependentParentIndexIds: '/ivdg-evaluation-app/evaluation/app/taskScheme/getDependentParentIndexIds', // 根据指标id，查询 可以选择的 依赖列表

  cancelPublishTask: '/ivdg-examination-service/examination/examTask/cancelPublishTask', //修改考核成绩发布状态
  examTaskSaveExamFile: '/ivdg-examination-service/examination/examTask/saveExamFile', //上传
  examTaskGetExamTaskFile: '/ivdg-examination-service/examination/examTask/getExamTaskFile', //查询列表
  examTaskDownloadFiles: '/ivdg-examination-service/examination/examTask/downloadFiles', //一键下载检测明细（湖南省厅）
  getExamScoreTrend: '/ivdg-examination-service/examination/statistics/getExamScoreTrend', //查询成绩趋势（湖南省厅）
  getExamChildScoreTrend: '/ivdg-examination-service/examination/statistics/getExamChildScoreTrend', //查询下级成绩趋势（湖南省厅）
  exportexamScoreTrendExcel: '/ivdg-examination-service/examination/statistics/exportexamScoreTrendExcel', //导出成绩趋势（湖南省厅）
  exportExamChildScoreTrendExcel: '/ivdg-examination-service/examination/statistics/exportExamChildScoreTrendExcel', //导出下级成绩趋势（湖南省厅）
};
