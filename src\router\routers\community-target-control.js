import ArchivesLayout from "@/layouts/archives-layout";
import BasicLayout from "@/layouts/basic-layout";

export default [
  {
    path: "/community-target-control2",
    name: "community-target-control2",
    component: BasicLayout,
    children: [
      {
        path: "/community-control-task/criminal-record/add",
        name: "community-control-task-add",
        tabShow: true,
        parentName: "community-target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/community-target-control/control-task/criminal-record/add.vue",
          ], resolve),
        meta: {
          title: "新增布控",
        },
      },
      {
        path: "/community-control-task/criminal-record/edit",
        name: "community-control-task-edit",
        tabShow: true,
        parentName: "community-target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/community-target-control/control-task/criminal-record/add.vue",
          ], resolve),
        meta: {
          title: "编辑布控",
        },
      },
      {
        path: "/community-control-task/criminal-record/detail",
        name: "community-control-task-detail",
        tabShow: true,
        parentName: "community-target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/community-target-control/control-task/criminal-record/detail.vue",
          ], resolve),
        meta: {
          title: "布控详情",
        },
      },
    ],
  },
  {
    path: "/community-data-storage2",
    name: "community-data-storage2",
    component: BasicLayout,
    children: [
      {
        path: "/community-data-storage/special-library/personnel-thematic-database",
        name: "community-personnel-thematic-database",
        tabShow: true,
        parentName: "community-data-storage/special-library",
        component: (resolve) =>
          require([
            "@/views/juvenile-data-storage/special-library/personnel-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "专题库-人像库",
        },
      },
    ],
  },
  {
    path: "/community-archive",
    name: "community-archive",
    component: ArchivesLayout,
    redirect: "/community-archive/people-dashboard",
    meta: {
      title: "社区人员档案",
      icon: "icon-shouye",
      show: true,
    },
    children: [
      {
        path: "/community-archive/people-dashboard",
        name: "community-people-dashboards",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/community-people-archive/dashbord"
          ),
        meta: {
          title: "人员概览",
          tagShow: true,
        },
      },
      {
        path: "/community-archive/people-profile",
        name: "community-people-profile",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/importantPerson-archive/profile"
          ),
        meta: {
          title: "人员资料",
          tagShow: true,
        },
      },
      {
        path: "/community-archive/people-activity-track",
        name: "community-people-activity-track",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/activity-track"
          ),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/community-archive/people-relationship-map",
        name: "community-people-relationship-map",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/relationship-map"
          ),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/community-place-archive",
    name: "community-place-archive",
    component: ArchivesLayout,
    redirect: "/community-place-archive/place-dashboard",
    meta: {
      title: "小区档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/community-place-archive/place-dashboard",
        name: "community-place-dashboard",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/community-place-archives/dashboard"
          ),

        meta: {
          title: "小区概览",
          tagShow: true,
        },
      },
      {
        path: "/community-place-archive/place-relationship-map",
        name: "community-place-relationship-map",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/place-archives/relationship-map"
          ),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
];
