<template>
  <div class="checkpoint-online auto-fill">
    <component
      :is="componentName"
      v-bind="$attrs"
      :index-data="indexData"
      :detail-data="detailData"
      @changeComponentName="changeComponentName"
      v-on="$listeners"
    ></component>
  </div>
</template>
<script>
export default {
  name: 'CheckpointOnline',
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      componentName: 'StatisticsList',
      detailData: {},
    };
  },
  methods: {
    changeComponentName([name, row]) {
      this.detailData = { ...row };
      this.$set(this, 'componentName', name);
    },
  },
  components: {
    StatisticsList: require('./components/statistics-list/index.vue').default,
    MonthReviewDetail: require('@/views/specialassessment/special-modules/components/month-review-detail/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
//.checkpoint-online {
//  padding: 0px 20px;
//}
</style>
