<template>
  <div class="ui-gather-card" @click="detail">
    <div class="ui-gather-card-left">
      <ui-image :src="list.identityPhoto" />
      <!-- <img :src="list.identityPhoto ? list.identityPhoto : errimg" alt="" /> -->
    </div>
    <div class="ui-gather-card-right">
      <p class="ui-gather-card-right-item" v-for="(item, index) in cardInfo" :key="index">
        <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
        <span class="ui-gather-card-right-item-value" :style="{ color: item.color ? item.color : '#ffffff' }">{{
          list[item.value]
        }}</span>
      </p>
      <tags-more
        :tagList="list.personTypes"
        :defaultTags="defaultTags"
        placement="left-start"
        bgColor="#2D435F"
      ></tags-more>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    defaultTags: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  created() {},
  methods: {
    detail() {
      this.$emit('detail', this.list.idCard, this.list);
    },
  },
  watch: {
    defaultTags() {
      return this.defaultTags;
    },
  },
  components: {
    TagsMore: require('@/components/tags-more_str').default,
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  // min-height: 158px;
  margin-bottom: 10px;
  padding: 10px;
  background: #0f2f59;
  &-left {
    width: 138px;
    height: 138px;
    margin-right: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
    }
  }
  .tag {
    margin-right: 10px;
    padding: 3px 10px;
    font-size: 12px;
    background-color: #0e8f0e;
    color: #ffffff;
    border-radius: 4px;
  }
}
// @{_deep} .ivu {
//   &-tag{
//     &:hover{
//       background: var(--color-primary) !important;
//     }
//   }
// }
</style>
