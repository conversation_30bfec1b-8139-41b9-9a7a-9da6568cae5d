<template>
  <div ref="tagWrap" class="tag-wrap">
    <div class="view-tags">
      <ui-tag
        v-for="(item, $index) in data"
        :key="$index"
        :color="item.labelColor"
        :title="item.labelName"
        style="max-width: 150px"
        >{{ item.labelName }}</ui-tag
      >
    </div>
    <Poptip
      :disabled="!showEllipsis"
      :max-width="300"
      trigger="hover"
      transfer
      word-wrap
      transfer-class-name="tag-poptip"
    >
      <div v-show="showEllipsis" ref="tagEllipsis" class="tag-ellipsis">
        <span
          v-for="(item, $index) in 3"
          :key="$index"
          class="tag-ellipsis-item"
        ></span>
      </div>
      <div slot="content">
        <ui-tag
          v-for="(item, $index) in data"
          :key="$index"
          :color="item.labelColor"
          >{{ item.labelName }}</ui-tag
        >
      </div>
    </Poptip>
  </div>
</template>
<script>
import UiTag from "@/components/ui-tag";
export default {
  components: {
    UiTag,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    // 显示几行，默认一行
    row: {
      type: Number,
      default: 1,
    },
    // 悬浮展示内容的
  },
  data() {
    return {
      showEllipsis: false,
      timer: null,
    };
  },
  watch: {
    data() {
      this.init();
    },
  },
  mounted() {
    window.onresize = () => {
      this.init();
    };
    window.onresize();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.init);
  },
  methods: {
    changeOnresize() {
      window.addEventListener("resize", this.init);
      this.init();
    },
    init() {
      if (this.timer) {
        clearTimeout(this.timer);
      }
      this.timer = setTimeout(() => {
        this.showEllipsis = false;
        this.$nextTick(() => {
          // 1920px 和 现在窗口大小的比例
          const screenRatio = parseFloat(
            1920 / document.body.offsetWidth
          ).toFixed(4);
          // 标签列表最外层宽度
          const tagWrapWidth = this.$refs.tagWrap.offsetWidth;
          const tagWrapWidthSum = tagWrapWidth * this.row;
          // 省略号宽度
          const tagEllipsisWidth = Math.round(20 / screenRatio);
          let tagWidth = 0;
          let tagItemOffset = 0;
          if (this.data.length) {
            for (let i = 0; i < this.data.length; i++) {
              const row = parseInt(tagWidth / tagWrapWidth) + 1;
              if (tagWidth > tagWrapWidth) {
                tagItemOffset = tagWrapWidth - (tagWidth - tagWrapWidth * row);
              } else {
                tagItemOffset = tagWrapWidth - tagWidth;
              }
              // 14：字体默认14px, 32：padding左右各6px，2px边框，右边距5px
              const fontSize =
                Math.round(12 / screenRatio) < 12
                  ? Math.round(this.data[i].labelName.length * 12)
                  : Math.round(
                      (this.data[i].labelName.length * 12) / screenRatio
                    );
              let tagItemWidth = fontSize + Math.round(24 / screenRatio);
              if (tagItemWidth > 150) {
                tagItemWidth = 150;
              }
              if (tagItemOffset < tagItemWidth) {
                tagWidth = tagWrapWidth + tagItemWidth;
              } else {
                tagWidth = tagWidth + tagItemWidth;
              }
              if (
                tagWidth > tagWrapWidthSum ||
                tagWidth + tagEllipsisWidth > tagWrapWidthSum
              ) {
                this.showEllipsis = true;
                break;
              }
            }
          }
        });
      }, 200);
    },
  },
};
</script>
<style lang="less" scoped>
.tag-wrap {
  // width: 100%;
  // overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  .view-tags {
    flex: 1;
    display: flex;
    flex-wrap: nowrap;
    margin-right: 10px;
    overflow: hidden;
    width: 0; // 防止标签太多，被撑开
  }
  .ui-tag {
    margin-right: 5px;
  }
  .tag-ellipsis {
    display: inline-flex;
    flex-direction: row;
    cursor: pointer;
    .tag-ellipsis-item {
      width: 4px;
      height: 4px;
      background: #888;
      display: inline-block;
      border-radius: 50%;
    }
    & > span:nth-child(2) {
      margin: 0 4px;
    }
  }
  /deep/ .ivu-poptip-rel {
    display: flex;
    align-items: center;
    height: 26px;
  }
  /deep/ .ui-tag-item {
    padding: 2px 6px;
    .ui-tag-text {
      font-size: 12px;
    }
  }
}
</style>

<style lang="less">
// TODO 设置这个元素高度样式的地方太多了，不确定是不是有各自的用途，先这里自己设置下
.ivu-poptip-body {
  height: auto !important;
}
</style>
