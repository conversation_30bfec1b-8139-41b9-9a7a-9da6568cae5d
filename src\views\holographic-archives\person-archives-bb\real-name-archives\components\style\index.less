.dom-wrapper {
    padding: 10px 28px 0px 0;
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}
.dom {
    width: 1570px;
    height: 855px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    position: relative;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    > header{
        height: 36px;
        line-height: 36px;
        background: rgba(211, 215, 222, 0.3);
        box-shadow: inset 0px -1px 0px 0px #d3d7de;
        border-radius: 4px 4px 0px 0px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: bold;
        font-size: 14px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .dom-content{
        padding: 10px 30px;
        font-size: 14px;
        flex: 1;
        overflow: hidden;
        .info-box{
            display: flex;
            justify-content: space-between;
            height: 100%;
            .info-box-left{
                width: 270px;
                height: 100%;
                padding-right: 20px;
                border-right: 1px solid #d3d7de;
                .info-photo{
                    width: 248px;
                    height: 248px;
                    border: 1px solid #d3d7de;
                }
                .details-list{

                    .wrapper-content{
                        margin-top: 6px;
                        width: 200px;
                        display: flex;
                        .label {
                            font-size: 14px;
                            display: inline-block;
                            color: rgba(0, 0, 0, 0.45);
                            white-space: nowrap;
                            width: 55px;
                            text-align: justify;
                            text-align-last: justify;
                            text-justify: inter-ideograph;
                        }
                        .message{
                            margin-left: 20px;
                        }
                    }
                }
            }
            .info-box-right{
                flex: 1;
                .charts-container{
                    height: 260px;
                }
            }   
        }
    }
}
.table-content {
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    justify-content: start;
    align-content: flex-start;
    .ui-table {
        height: 100%;
    }
    .loading-box{
        position: relative;
        width: 100%;
        height: 80px;
    }
}
.table-content{
    .list-card {
        position: relative;
        background-color: #f9f9f9;
        margin-bottom: 10px;
        height: min-content;

        box-sizing: border-box;
        .operate-bar {
            width: 70px;
            height: 30px;
            background: linear-gradient(
                90deg,
                rgba(87, 187, 252, 0.8) 0%,
                #2c86f8 100%
            );
            border-radius: 0px 0px 4px 0px;
            position: absolute;
            right: -100%;
            transition: all 0.3s;
            bottom: 0;
            transform: skewX(-20deg);
            .operate-content {
                padding: 0 5px;
                transform: skewX(20deg);
                height: 100%;
                display: flex;
                align-items: center;
                color: #fff;
                /deep/ .ivu-tooltip-rel {
                    padding: 6px;
                }
            }
        }
        .fast-operation-bar{
            position: absolute;
            right: 10px;
            bottom: 35px;
            color: #2C86F8;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
            .icon-gengduo{
                transform: rotate(90deg);
                transition: 0.1s;
                display: inline-block;
            }
            p:hover{
                color: #2C86F8;
            }
            &:hover{
                background: #2C86F8;
                color: #fff;
               
                .icon-gengduo{
                    transform: rotate(0deg);
                    transition: 0.1s;
                }
                border-radius: 10px;
            } 
            /deep/ .ivu-poptip-popper{
                min-width: 150px !important;
                width: 40px !important;
                height: auto;
            }
            /deep/.ivu-poptip-body{
                height: auto !important;
            }
            .mark-poptip{
               color: #000;
               cursor: pointer;
               text-align: left;
               /deep/ .ivu-icon-ios-add-circle-outline{
                    font-weight: 600;
               }
            }  
        }
    }
    .box-1 {
        // width: 10.7%;
        width: calc(~'calc(100% - 60px) / 7');
        padding: 10px;
        border-radius: 4px;
        border: 1px solid #d3d7de;
        box-shadow: 1px 1px 7px #cdcdcd;
        margin-left: 6px;
        &:hover {
            border: 1px solid #2c86f8;
            .operate-bar {
                right: -6px;
                bottom: -1px;
            }
            &:before {
                border-color: #2c86f8;
            }
            .check-box {
                display: inline-block;
            }
        }
        .check-box {
            position: absolute;
            top: 4px;
            left: 4px;
            z-index: 10;
            display: none;
        }
        .checked {
            .check-box {
                display: inline-block;
            }
         }
        .isChecked {
            &:before {
                border-color: #2c86f8;
            }
            .content {
                .check-box {
                    display: inline-block;
                }
            }
        }
        .img-content {
            width: 100%;
            position: relative;
            border: 1px solid #cfd6e6;
            height: 167px;
            img {
                width: 100%;
                height: 100%;
                display: block;
            }
            .num,
            .shade {
                position: absolute;
            }
            .num {
                position: absolute;
                top: 0;
                left: 0;
                z-index: 8;
                font-size: 12px;
                padding: 2px 5px;
                border-radius: 4px;
                color: #fff;
                background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
            }
            .shade {
                background: rgba(0, 0, 0, 0.7);
                font-size: 12px !important;
                width: 100%;
                bottom: 0;
                left: 0;
                text-align: center;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                line-height: 18px;
                padding: 3px 0;
            }
        }
        .bottom-info {
            padding-top: 5px;
            time,
            p {
                display: flex;
                align-items: center;
                overflow: hidden;
                text-overflow: ellipsis;
                color: rgba(0, 0, 0, 0.8);
                white-space: nowrap;
                width: 100%;
                .iconfont {
                    margin-right: 2px;
                    color: #888;
                }
            }
        }
    }
}
/deep/.ivu-icon-md-close{
    cursor: pointer;
}