<template>
  <div class="ui-switch-tab">
    <ul class="list">
      <li
        v-for="item in indexList"
        :key="item.key"
        :class="{ active: item.key == activeKey }"
        class="ivu-tabs-tab"
        @click="tabClick(item)"
      >
        <i class="icon-font f-16 icon-bg3" :class="item.icon" :title="item.value"></i>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'reach-tabs',
  props: {
    multiple: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [Array, String, Number],
    },
  },
  data() {
    return {
      list: [],
      icon: '',
      activeKey: 0,
      indexList: [
        { key: '0', value: '列表模式', icon: 'icon-liebiaomoshi' },
        { key: '1', value: '地图模式', icon: 'icon-ditumoshi' },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    tabClick(item) {
      this.activeKey = item.key;
      this.$emit('changeTab', item.key, item.value);
    },
  },
  watch: {
    value() {},
  },
  computed: {},
  components: {},
};
</script>
<style lang="less" scoped>
.no-drop {
  cursor: no-drop !important;
  color: #215287 !important;
  background-color: #022143 !important;
  i {
    color: #215287 !important;
    cursor: no-drop !important;
    .icon-all {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg1 {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg2 {
      background: linear-gradient(360deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg3 {
      background: linear-gradient(360deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg4 {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg5 {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg6 {
      background: linear-gradient(360deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
  }
  &:hover {
    background: #022143;
    .icon-all {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg1 {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg2 {
      background: linear-gradient(360deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg3 {
      background: linear-gradient(360deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg4 {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg5 {
      background: linear-gradient(180deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg6 {
      background: linear-gradient(360deg, #215287 0%, #215287 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
  }
}
.ui-switch-tab {
  font-size: 14px;
  .list {
    display: inline-block;
    border: 1px solid #174f98;
    border-radius: 4px;
    li {
      cursor: pointer;
      display: inline-block;
      background: #022143;
      padding: 4px 20px;
      color: #215287;
      position: relative;
      border-left: 1px solid #174f98;
      &:hover {
        background-color: var(--color-primary);
        .icon-all {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
        .icon-bg1 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
        .icon-bg2 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
        .icon-bg3 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
        .icon-bg4 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
        .icon-bg5 {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
        .icon-bg6 {
          background: linear-gradient(360deg, #fff 0%, #fff 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: #0000;
        }
      }
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-left: none;
      }
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
      }
    }
    .active {
      background: var(--color-primary);
      .icon-all {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      .icon-bg1 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      .icon-bg2 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      .icon-bg3 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      .icon-bg4 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      .icon-bg5 {
        background: linear-gradient(180deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      .icon-bg6 {
        background: linear-gradient(360deg, #fff 0%, #fff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: #0000;
      }
      &:hover {
        color: #fff;
      }
    }
    .icon-all {
      background: linear-gradient(180deg, #b58e0d 0%, #7e6f0a 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg1 {
      background: var(--icon-card-gradient-cyan);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg2 {
      background: var(--icon-card-gradient-purple);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg3 {
      background: var(--icon-card-gradient-blue);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg4 {
      background: var(--icon-card-gradient-orange);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg5 {
      background: linear-gradient(180deg, #d811cc 0%, #7710aa 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
    .icon-bg6 {
      background: linear-gradient(360deg, #780f0f 0%, #ec5353 100%);
      -webkit-background-clip: text;
      background-clip: text;
      color: #0000;
    }
  }
}
</style>
