<template>
  <div class="tooltip-container-view">
    <div class="tooltip-title-box">
      <p>{{ getDate }}</p>
    </div>
    <div v-for="(item, index) in trendData" :key="index" class="tooltip-content-box">
      <p class="legend-box">
        <span class="block" :style="{ 'background-color': item.color }"> </span>
        <span> {{ item.seriesName }}： </span>
        <span class="font-num">
          {{ item.value }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'capture-trend-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {
    // trendData: 调用的时候，new xxx() 时会设置到 data
    getDate() {
      let { date } = this.trendData[0].data;
      return date ? this.$util.common.turnFormat(date) : '';
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .tooltip-container-view {
    border: 1px solid #2a8dea;
    color: #ffffff;
    .legend-box {
      color: #ffffff;
    }
    .font-num {
      color: #ffffff;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .tooltip-container-view {
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
    color: rgba(0, 0, 0, 0.75);
    .legend-box {
      color: rgba(0, 0, 0, 0.35);
    }
    .font-num {
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
.tooltip-container-view {
  background: var(--bg-echart-tooltip);
  border-radius: 4px;
  min-width: 239px;
  padding: 10px 20px;
  font-size: 14px;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .legend-box {
    padding: 3px 0;
  }
  .block {
    display: inline-block;
    height: 6px;
    width: 6px;
    border-radius: 50%;
    margin-right: 10px;
  }
}
</style>
