<!--
    * @FileDescription: 电视墙配置
    * @Author: H
    * @Date: 2023/11/20
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
  <div class="tv-well">
    <div class="tv-well-left">
      <Input
        placeholder="请输入关键字"
        v-model="searchInput"
        @keydown.enter.native="handleSearch"
      >
        <Icon
          type="ios-search"
          class="font-16 cursor-p"
          slot="suffix"
          maxlength="50"
          @click.prevent="handleSearch"
        />
      </Input>
      <div class="add-tv-well">
        <p>
          共 <span class="tv-well-num">{{ tvWellTotal }}</span> 个
        </p>
        <Button type="primary" size="small" @click="handleAddTV">
          <Icon type="md-add"></Icon>
          新增电视墙
        </Button>
      </div>
      <div class="tv-well-modal" v-if="tvModal">
        <Input placeholder="请输入电视墙名称" v-model="tvWellName"></Input>
        <div class="btn">
          <Button
            type="primary"
            size="small"
            class="confirm-btn"
            @click="handleSave"
            >保存</Button
          >
          <Button size="small" @click="handleCancel">取消</Button>
        </div>
      </div>
      <ul
        class="tv-well-list"
        v-infinite-scroll="load"
        infinite-scroll-distance="50"
      >
        <li
          class="tv-well-box"
          :class="{
            'tv-well-active': activeIndex == index,
            'tv-well-input': editInput[index],
          }"
          v-for="(item, index) in tvWellList"
          :key="index"
          @click="handleClickList(item, index)"
        >
          <p class="tv-well-name" v-show="!editInput[index]">{{ item.name }}</p>
          <div class="icon-list" v-show="!editInput[index]">
            <i
              class="iconfont icon-caozuo"
              @click="handleCabint(item, index)"
            ></i>
            <i
              class="iconfont icon-bianji"
              @click="handleEdit(item, index)"
            ></i>
            <i
              class="iconfont icon-shanchu"
              @click="handleDele(item, index)"
            ></i>
          </div>
          <Input
            v-model.trim="editName"
            v-if="editInput[index]"
            :maxlength="20"
            placeholder="请输入新分组名称"
            style="width: 100%"
          >
            <Icon
              type="md-checkmark"
              title="确定"
              slot="suffix"
              @click="handleEditName"
            />
            <Icon
              type="md-close"
              title="取消"
              slot="suffix"
              @click="handleNameCancel(index)"
            />
          </Input>
        </li>
        <ui-empty v-if="tvWellList.length === 0 && loading == false"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </ul>
    </div>
    <div class="tv-well-right">
      <div class="operation-btn">
        <div class="btn-left">
          <p class="tv-well-hander-name">{{ tvWellTableName }}</p>
          <Button type="primary" size="small" @click="handleAddDecoder">
            <Icon type="md-add"></Icon>
            添加解码器
          </Button>
          <Button
            type="primary"
            size="small"
            class="center-btn"
            @click="handleConfirm"
            >布局设置</Button
          >
          <Button size="small" @click="handledemodDele(1, 0)">
            <Icon type="ios-trash"></Icon>批量删除</Button
          >
        </div>
        <div class="collect-right">
          <p>
            已选<span class="tv-well-num">{{ selectionList.length }}</span
            >数据/共<span class="tv-well-num">{{ tableData.length }}</span
            >条数据
          </p>
        </div>
      </div>
      <Table
        class="auto-fill table"
        ref="table"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        @on-select-all="handleSelectAll"
        @on-selection-change="handleSelectChange"
      >
        <template #loading>
          <ui-loading></ui-loading>
        </template>
        <template #action="{ row }">
          <div class="btn-tips">
            <ui-btn-tip
              content="编辑"
              icon="icon-bianji"
              class="primary"
              @click.native="handledemodEdit(row)"
            />
            <ui-btn-tip
              content="删除"
              icon="icon-shanchu"
              class="primary"
              @click.native="handledemodDele(row, 1)"
            />
          </div>
        </template>
      </Table>
    </div>
    <author-user-modal
      ref="authorUser"
      @comfirm="handleUserEdit"
    ></author-user-modal>
    <decoder-modal ref="decoder" :wellId="wellId" @code="handleCode">
    </decoder-modal>
    <layout-setting
      :class="{ 'layout-hidden': layoutBox }"
      v-if="layoutBox"
      ref="layoutSetting"
      @back="handleBack"
    ></layout-setting>
    <!-- 选择用户 -->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
    ></select-user>
    <ui-modal
      v-model="deleTip"
      title="提示"
      @onOk="handleDeleSure"
      @onCancel="handleDeleCancel"
    >
      <p><Icon type="md-alert" /> 确定要删除该电视墙?</p>
    </ui-modal>
  </div>
</template>
<script>
import decoderModal from "./components/decoder-modal.vue";
import layoutSetting from "./components/layoutSetting.vue";
import authorUserModal from "./components/author-user-modal.vue";
import selectUser from "@/components/select-modal/select-user.vue";
import {
  videoWallAdd,
  videoWallPageList,
  permission,
  removeVideoWell,
  videoWallUpdate,
  demodifierPageList,
  demodifierRemove,
} from "@/api/config";
import expandRow from "./components/table-expand.vue";
export default {
  components: {
    decoderModal,
    layoutSetting,
    authorUserModal,
    selectUser,
    expandRow,
  },
  data() {
    return {
      searchInput: "",
      tvModal: false,
      tableLoading: false,
      deleTip: false,
      tvWellName: "",
      columns: [
        {
          title: "选择",
          width: 65,
          align: "center",
          type: "selection",
          key: "index",
        },
        {
          type: "expand",
          width: 50,
          render: (h, param) => {
            return h(expandRow, {
              props: { row: param.row },
            });
          },
        },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "解码器名称", key: "name" },
        { title: "解码器IP", key: "ip" },
        { title: "端口号", key: "port" },
        { title: "监视器数量", key: "screenCount" },
        { title: "创建时间", key: "createTime" },
        { title: "操作栏", slot: "action" },
      ],
      tvWellList: [],
      tableData: [],
      tvWellTotal: 0,
      activeIndex: 0,
      tvWellTableName: "",
      editName: "",
      editInput: [],
      layoutBox: false,
      loading: false,
      selectionList: [],
      loadingText: false,
      noMore: false,
      morePage: 0,
    };
  },
  computed: {
    wellId() {
      let id = "";
      if (this.tvWellList.length > 0) {
        id = this.tvWellList[this.activeIndex].id;
      }
      return id;
    },
  },
  created() {
    this.queryList();
  },
  methods: {
    queryList() {
      let params = {
        name: this.searchInput,
        pageSize: 9999,
        pageNumber: 1,
      };
      if (this.noMore) return;
      this.loading = true;
      videoWallPageList(params)
        .then((res) => {
          this.tvWellList = res.data.entities;
          this.editInput = Array.apply(null, {
            length: this.tvWellList.length,
          }).map(() => {
            return false;
          });
          this.tvWellTotal = this.tvWellList.length;
          this.noMore = this.tvWellList.length === res.data.total;
          if (!this.noMore) this.morePage += 20;
          this.activeIndex = 0;
          this.tvWellTableName =
            this.tvWellList.length > 0 ? this.tvWellList[0].name : "";
          if (this.tvWellList.length > 0) {
            this.queryDecode();
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSearch() {
      this.noMore = false;
      this.queryList();
    },
    handleAddTV() {
      this.tvModal = true;
    },
    handleSave() {
      this.noMore = false;
      this.tvModal = false;
      let params = {
        name: this.tvWellName,
      };
      videoWallAdd(params)
        .then((res) => {
          this.tvWellName = "";
          this.$Message.success("新增成功");
          this.queryList();
        })
        .finally(() => {
          this.tvModal = false;
        });
    },
    handleConfirm() {
      this.layoutBox = true;
      this.$nextTick(() => {
        this.$refs.layoutSetting.queryMonitor(
          this.tvWellList[this.activeIndex].id
        );
      });
    },
    handleBack() {
      this.layoutBox = false;
    },
    handleCancel() {
      this.tvModal = false;
    },
    handleSelectAll(selection) {
      this.selectionList = selection;
    },
    handleSelectChange(selection) {
      this.selectionList = selection;
    },
    handleAddDecoder() {
      this.$refs.decoder.init();
    },
    handleEditName() {
      this.noMore = false;
      let params = {
        id: this.tvWellList[this.activeIndex].id,
        name: this.editName,
      };
      videoWallUpdate(params).then((res) => {
        this.$set(this.editInput, this.activeIndex, false);
        this.$Message.success("修改成功");
        this.queryList();
      });
    },
    handleClickList(item, index) {
      this.tvWellTableName = item.name;
      this.activeIndex = index;
      this.queryDecode();
    },
    queryDecode() {
      let params = {
        videoWallId: this.tvWellList[this.activeIndex].id,
        pageNumber: 1,
        pageSize: 10,
      };
      demodifierPageList(params).then((res) => {
        res.data.entities.map((item) => {
          item.videoWallId = this.tvWellList[this.activeIndex].id;
        });
        this.tableData = res.data.entities;
      });
    },
    handleCabint(item, index) {
      this.activeIndex = index;
      this.$refs.authorUser.init(item.id);
    },
    handleEdit(item, index) {
      this.editInput.forEach((ite, ind) => {
        this.$set(this.editInput, ind, false);
      });
      this.$set(this.editInput, index, true);
      this.activeIndex = index;
      this.editName = item.name;
      this.$forceUpdate();
    },
    handleDele(item, index) {
      this.activeIndex = index;
      this.deleTip = true;
    },
    handleDeleSure() {
      removeVideoWell(this.tvWellList[this.activeIndex].id)
        .then((res) => {
          this.$Message.success("删除成功");
          this.noMore = false;
          this.activeIndex = 0;
          this.queryList();
        })
        .finally(() => {
          this.deleTip = false;
        });
    },
    handleDeleCancel() {
      this.deleTip = false;
    },
    handleUserEdit(list) {
      list.forEach((item) => {
        item.select = true;
      });
      this.$refs.selectUser.show(list);
    },
    handleNameCancel(index) {
      this.editInput[index] = false;
      this.$forceUpdate();
    },
    handleUserData(list) {
      let ids = list.map((item) => item.id);
      let params = {
        ids: ids,
        videoWallId: this.tvWellList[this.activeIndex].id,
      };
      permission(params).then((res) => {
        this.$Message.success("添加成功");
      });
    },
    handledemodEdit(row) {
      this.$refs.decoder.init(row.id);
    },
    handledemodDele(row, index) {
      let ids = "";
      if (index == 0) {
        if (this.selectionList.length == 0) {
          this.$Message.warning("请选择");
          return;
        }
        ids = this.selectionList.map((item) => item.id).join(",");
      } else {
        ids = row.id;
      }
      this.$Modal.confirm({
        title: "提示",
        content: '<p><Icon type="md-alert" /> 确定要删除吗?</p>',
        okText: "确定",
        cancelText: "取消",
        onOk: () => {
          demodifierRemove(ids).then((res) => {
            this.$Message.success("删除成功");
            this.queryDecode();
          });
        },
        onCancel: () => {},
      });
    },
    handleCode() {
      this.queryDecode();
    },
    load() {
      if (!this.noMore) this.queryList();
    },
  },
};
</script>
<style lang="less" scoped>
.tv-well {
  width: 100%;
  height: 800px;
  display: flex;
  position: relative;
  .tv-well-left {
    width: 300px;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    padding: 10px;
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    position: relative;
    .add-tv-well {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
    }
    .tv-well-modal {
      margin: 10px;
      border: 1px solid #a5b0b64f;
      background: #a5b0b61a;
      padding: 15px;
      .btn {
        margin-top: 10px;
        display: flex;
        justify-content: flex-end;
        .confirm-btn {
          margin-right: 10px;
        }
      }
    }
    .tv-well-list {
      margin-top: 10px;
      // height: 90%;
      min-width: 50%;
      overflow-y: scroll;
      .tv-well-box {
        display: flex;
        border-bottom: 1px solid rgba(165, 176, 182, 0.31);
        padding: 5px 10px;
        justify-content: space-between;
        cursor: pointer;
        .tv-well-name {
          font-size: 13px;
        }
        .icon-list {
          // display: none;
          .iconfont {
            cursor: pointer;
          }
          /deep/ .icon-bianji {
            margin: 0 5px;
          }
        }
        &:hover {
          color: #2c86f8;
          background: rgba(165, 176, 182, 0.1);
          .tv-well-name {
            font-size: 13px;
            font-weight: bold;
          }
          .icon-list {
            display: block;
          }
        }
      }
      .tv-well-active {
        color: #2c86f8;
        background: rgba(61, 183, 255, 0.1);
        .tv-well-name {
          font-weight: bold;
        }
        &:hover {
          color: #2c86f8;
          background: rgba(61, 183, 255, 0.1);
        }
      }
      .tv-well-input {
        padding: 0;
        border: none;
        /deep/ .ivu-input-suffix {
          right: 8px;
          width: 40px;
          /deep/.ivu-icon {
            font-size: 20px;
          }
          /deep/.ivu-icon-md-checkmark {
            color: #38aef3;
          }
        }
      }
      /deep/ .cover {
        max-height: 80%;
        margin-top: 200px;
      }
    }
  }
  .tv-well-right {
    flex: 1;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    padding: 10px;
    .center-btn {
      margin: 0 10px;
    }
    .operation-btn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      .btn-left {
        display: flex;
        align-items: center;
        .tv-well-hander-name {
          color: #2c86f8;
          margin-right: 10px;
          font-weight: bold;
        }
      }
    }
  }
  .layout-hidden {
    animation: unfold ease 0.5;
  }
  @keyframes unfold {
    from {
      height: 0;
    }
    to {
      height: calc(~"100% - 40px");
    }
  }
}
/deep/ .ivu-icon-md-alert {
  color: #f29f4c;
  font-size: 16px;
}
.tv-well-num {
  color: #2c86f8;
}
.loading,
.endlist {
  margin-top: 5px;
  color: #2c86f8;
  text-align: center;
}
</style>
