<!--
    * @FileDescription: 频次分析
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="frequency-analysis container">
        <!-- 地图 -->
        <mapCustom ref="mapBase" mapType="frequency-analysis" sectionName="capture"/>
        <!-- 左面信息展示框 -->
        <leftBox ref="searchBar" @search="handleSearch" v-show="searchBox"></leftBox>
        <detailsBox ref="detailBox" 
            @backSearch="handleBackSearch"
            v-show="!searchBox"
            @cutAnalyse="handleCutAnalyse"></detailsBox>
        <!-- 右侧结果 -->
        <right-list-one :appearList="appearList" :total="total" :loading="loading" :loadingText="loadingText" v-if="rightShowList" pageName='compusControlCom' @handleReachBottom="handleReachBottom" @show-pic="showPic"></right-list-one>
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import detailsBox from './components/detailsBox.vue';
import RightListOne from '@/views/model-market/components/RightListOne.vue';
import mapCustom from '@/views/model-market/components/map/index.vue';
import { getFrequentStrangerPersonDetailList } from '@/api/monographic/compus-control'
export default {
    name: 'frequency-analysis',
    components:{
        mapCustom,
        leftBox,
        detailsBox,
        RightListOne
    },
    data () {
        return {
            searchBox: true,
            rightShowList: false,
            appearList: [],
            loadingText: '加载中',
            isLast: false,
            loading: false,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20,
            },
            detailsParams: {}
        }
    },
    methods: {
        handleSearch(params){
            console.log(params, 'params')
            this.searchBox = false;
            this.$nextTick(() => {
                this.$refs.detailBox.handleList({...params})
            })
        },
        handleBackSearch(){
            this.searchBox = true;
            this.handleCancel()
        },
        handleCutAnalyse(data) {
            this.rightShowList = true;
            this.pageInfo.pageNumber = 1
            this.appearList = []
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            this.detailsParams = {
                endDate: data.endDate,
                startDate: data.startDate,
                idNumber: data.idNumber
            }
            this.getRightList()
        },
        getRightList(){
            this.loading = true;
            let params = {...this.detailsParams, ...this.pageInfo}
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            getFrequentStrangerPersonDetailList(params)
            .then(res => {
                let list = res.data.entities || []
                this.appearList = this.appearList.concat(list)
                this.basicPoints = this.appearList.map((item) => {
                    item.showIconBorder = true;
                    return item;
                });
                this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, true);
                this.$refs.mapBase.setCenter(this.basicPoints[0]);
                if (list && list.length) {
                    this.pageInfo.pageNumber += 1
                    this.total = res.data.total
                } else {
                    this.isLast = true
                }
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleReachBottom () {
            this.loadingText = '加载中'
            if (this.isLast) {
                this.loadingText = '已经是最后一页了'
                return
            }
            return this.getRightList()
        },
        handleCancel() {
            this.rightShowList = false;
            this.$refs.mapBase.resetMarkerbasicPoint();
            this.$refs.mapBase.clearPoint()
        },
        showPic(item,index){
          this.$refs.mapBase.chooseNormalPoint(
                this.basicPoints,
                "capture",
                index
            );
        },
    }
}
</script>

<style lang='less' scoped>
.frequency-analysis{
    padding: 10px !important;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
