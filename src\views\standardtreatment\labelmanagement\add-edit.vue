<template>
  <div>
    <ui-modal v-if="visible" v-model="visible" :title="addEditAction.title" :styles="styles">
      <Form ref="modalData" :rules="ruleCustom" :model="searchParams">
        <FormItem label="标签名称" class="mb-lg" prop="tagName" :label-width="85">
          <Input
            type="text"
            v-model="searchParams.tagName"
            class="remarks"
            v-if="!isView"
            placeholder="请输入标签名称"
            :maxlength="20"
          ></Input>
          <span class="base-text-color" v-else> {{ searchParams.tagName }}</span>
        </FormItem>
        <FormItem :label-width="85" label="标签类型" prop="tagType">
          <Select v-model="searchParams.tagType" class="remarks" clearable placeholder="请选择字段类型" v-if="!isView">
            <Option v-for="(item, index) in tagTypeList" :key="index" :label="item.dataValue" :value="item.dataKey">
            </Option>
          </Select>
          <span class="base-text-color" v-else> {{ searchParams.tagTypeText }}</span>
        </FormItem>
        <FormItem :label-width="85" label="标签类别" prop="tagCategory">
          <Select
            v-model="searchParams.tagCategory"
            class="remarks"
            clearable
            placeholder="请选择字段类型"
            v-if="!isView"
          >
            <Option v-for="(item, index) in tagCategory" :key="index" :label="item.dataValue" :value="item.dataKey">
            </Option>
          </Select>
          <span class="base-text-color" v-else> {{ searchParams.tagCategoryText }}</span>
        </FormItem>
        <FormItem :label-width="85" label="标签颜色" prop="tagColour">
          <tag-color :is-view="isView" :default-tag-list="tagList" @tagCheckChange="tagCheckChange"> </tag-color>
        </FormItem>
      </Form>
      <template slot="footer">
        <Button @click="visible = false" class="plr-30">取 消</Button>
        <Button type="primary" @click="confirm" :loading="saveModalLoading" class="plr-30">确 定</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
// .remarks {
//     width: 565px;
// }
.tag-list {
  display: flex;
  .tag-item {
    width: 38px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background: #fff;
    margin-right: 15px;
    border-radius: 3px;
  }
}
@{_deep}.ivu-input[disabled] {
  background-color: transparent;
}
.action {
  cursor: pointer;
  font-size: 24px;
  &:hover {
    background: #ccc;
  }
}
.facePath {
  width: 110px;
  height: 110px;
}
@{_deep} .ivu-modal-body {
  padding: 50px 100px;
}
</style>
<script>
import taganalysis from '@/config/api/taganalysis';

export default {
  data() {
    return {
      visible: false,
      styles: {
        width: '4rem',
      },
      saveModalLoading: false,
      searchParams: {
        tagName: '',
        tagType: null,
        tagCategory: null,
        tagColour: null,
      },
      tagList: [
        { color: '#0E8F0E', checkable: false, name: 'success' },
        { color: '#D66418', checkable: false, name: 'error' },
        { color: '#BC3C19', checkable: false, name: 'warning' },
      ],
      initialTagList: [],
      fieldList: [],
      ruleCustom: {
        tagName: [{ required: true, trigger: 'blur', message: '请填写标签名称' }],
        tagType: [{ required: true, trigger: 'change', message: '请选择标签类型' }],
        tagCategory: [{ required: true, trigger: 'change', message: '请选择标签类别' }],
        tagColour: [{ required: true, trigger: 'blur', message: '请选择标签颜色' }],
      },
    };
  },
  created() {},
  mounted() {
    this.initialTagList = this.$util.common.deepCopy(this.tagList);
  },
  methods: {
    confirm() {
      this.$refs.modalData.validate((valid) => {
        if (valid) {
          this.addEditAction.action === 'add' ? this.addQuery('addDeviceTag') : this.addQuery('updateDeviceTag');
        } else {
          this.$Message.error('请输入正确的信息');
        }
      });
    },
    async addQuery(type) {
      try {
        let params = Object.assign({}, this.searchParams);
        params.tagCategory = parseInt(this.searchParams.tagCategory);
        params.tagType = parseInt(this.searchParams.tagType);
        let method = 'post';
        if (type === 'updateDeviceTag') {
          params.tagId = this.defaultParams.tagId;
          method = 'put';
        }
        this.loading = true;
        let res = await this.$http[method](taganalysis[type], params);
        this.$Message.success(res.data.msg);
        this.visible = false;
        this.$emit('update');
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    tagCheckChange(list) {
      if (this.isView) return;
      let checkItem = list.find((element) => {
        return element.checkable;
      });
      this.searchParams.tagColour = checkItem.color;
    },
    editQuery() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    defaultParams(val) {
      Object.assign(this.searchParams, val);
      this.searchParams.tagCategory = this.searchParams.tagCategory + '';
      this.searchParams.tagType = this.searchParams.tagType + '';
      this.tagList = this.$util.common.deepCopy(this.initialTagList);
      let defaultColorIndex = this.tagList.findIndex((item) => {
        return item.color === val.tagColour;
      });
      if (defaultColorIndex !== -1) {
        this.tagList[defaultColorIndex].checkable = true;
      }
    },
  },
  computed: {
    isView() {
      return this.addEditAction.action === 'view';
    },
  },
  props: {
    value: {},
    addEditAction: {},
    defaultParams: {},
    tagTypeList: {
      default: () => [],
    },
    tagCategory: {
      default: () => [],
    },
  },
  components: {
    TagColor: require('./components/tag-color.vue').default,
  },
};
</script>
