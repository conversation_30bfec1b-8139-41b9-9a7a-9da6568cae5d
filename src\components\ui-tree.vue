<template>
  <div class="tree">
    <div class="ipt">
      <el-input placeholder="输入关键字进行过滤" v-model="filterText">
        <!-- <template slot="append"> -->
        <!-- <i class="font_family icon-sousuo" @click="enter"></i> -->
        <!-- </template> -->
      </el-input>
      <Icon type="ios-search"></Icon>
    </div>
    <div class="content">
      <Select v-model="qualified" @on-change="chooseType(qualified)" class="selt">
        <Option :value="item.id" v-for="(item, index) in type_name" :key="index">{{ item.name }}</Option>
      </Select>
      <el-tree
        class="filter-tree"
        :data="nodeData"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        :expand-on-click-node="false"
        @node-click="handleNodeClick"
        @node-contextmenu="rightClick"
        @node-expand="open"
        @node-collapse="closeNode"
        ref="tree"
      >
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: 'qsTree',
  props: ['nodeData'],
  data() {
    return {
      filterText: '',
      defaultProps: {
        children: 'children',
        value: 'id',
        label: 'deviceName',
      },
      type_name: [
        { name: '全部', id: '0' },
        { name: '合格', id: '1' },
        { name: '不合格', id: '2' },
        { name: '无法考核', id: '3' },
      ],
      qualified: '0',
    };
  },
  watch: {
    filterText() {
      this.$refs.tree.filter(this.filterText);
    },
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1 || data[this.nodeKey] === value;
    },
    // enter() {
    //   this.$refs.tree.filter(this.filterText);
    // },
    // 点击节点
    handleNodeClick(val) {
      this.$emit('clickNode', val);
    },
    // 右击
    rightClick(val) {
      this.$emit('clickRight', val);
    },
    // 展开
    open(val) {
      this.$emit('openNode', val);
    },
    // 关闭
    closeNode(val) {
      this.$emit('closeNode', val);
    },
    chooseType(index) {
      if (index == 0) {
        index = '';
      }
      this.$emit('idx', index);
    },
  },
};
</script>

<style lang="less">
.tree {
  height: 100%;
  padding-left: 10px;
  .el-tree {
    margin-top: 10px;
    height: 700px;
    overflow: auto;
  }
  .ipt {
    position: relative;
    .el-input {
      margin-bottom: 10px;
      .el-input__inner {
        outline: none;
        background: #12294e;
        border: 0.005208rem solid #1375a7;
        width: 266px;
        height: 34px;
        color: #fff;
      }
    }
    i {
      position: absolute;
      right: 44px;
      top: 6px;
      font-size: 20px;
      color: var(--color-primary);
    }
  }
  .el-input-group {
    margin-bottom: 10px;
  }
  .content {
    .selt {
      width: 266px;
    }
  }
  .el-tree-node__content:hover {
    background-color: var(--color-primary);
  }
}
</style>
