<template>
  <el-tree class="tree" node-key="id" multiple :style="treeStyle" :data="treeData" :props="defaultProps" :expand-on-click-node="false" :default-expanded-keys="defaultKeys" @node-click="handleNodeClick" @current-change="currentChange">
    <span class="custom-tree-node" slot-scope="{ node }">
      <span :class="{ 'nav-not-selected': node.disabled, allowed: node.disabled }">{{ node.label }}</span>
    </span>
  </el-tree>
</template>

<script>
import { mapActions, mapGetters } from 'vuex'
export default {
  name: 'tree',
  props: {
    /**
     * selectTree.orgCode: 选中的值
     */
    selectTree: {
      required: true
    },
    // 树结构style
    treeStyle: {},
    placeholder: {
      type: String,
      default: () => {
        return '请选择'
      }
    },
    // 是否自定节点
    custormNode: {
      type: Boolean,
      default: false
    },
    // 自定义节点数据
    custormNodeData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      defaultProps: {
        label: 'orgName',
        children: 'children'
      },
      treeData: [],
      defaultKeys: [],
      isClickTreeNode: true,
      defaultedLabel: ''
    }
  },
  watch: {
    treeData(val) {
      if (val.length !== 0) {
        this.defaultKeys = val.map(row => {
          return row.orgCode
        })
      }
    }
  },
  computed: {
    ...mapGetters({
      // treeData: 'common/getOrganizationList',
    }),
    styles() {
      return {
        fontSize: `${this.size / 192}rem`,
        color: this.color
      }
    }
  },
  methods: {
    currentChange() {},

    handleNodeClick() {}
  }
}
</script>

<style lang="less" scoped>
.select-width {
  width: 200px;
}
.tree {
  // min-width: 200px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 500px;
  margin-right: 10px;
}
.custorm-tree-node {
  width: 100%;
  padding: 0 0.03125rem;
  color: #ffffff;
  cursor: pointer;
  &:hover {
    // background: #041129;
  }
}
</style>
