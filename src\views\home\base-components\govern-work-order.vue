<template>
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <draw-echarts
      v-show="echartList.length"
      class="charts"
      :echart-option="echartOption"
      :echart-style="echartStyle"
      ref="eChartRef"
      :echarts-loading="echartsLoading"
    ></draw-echarts>
    <span class="next-echart">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('eChartRef', echartList, [], echartNum)"></i>
    </span>
  </div>
</template>

<script>
import Vue from 'vue';
import GovernWorkOrderTooltip from '@/views/home/<USER>/govern-work-order-tooltip.vue';
import dataZoom from '@/mixins/data-zoom';
import governancetask from '@/config/api/governancetask.js';
import governanceevaluation from '@/config/api/governanceevaluation.js';
import governWorkOrderStyle from '@/views/home/<USER>/module/govern-work-order/index.js';
import commonStyle from '@/views/home/<USER>/module/common-style';

export default {
  name: 'GovernWorkOrder',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    year: {
      type: String,
      default: '',
    },
    homePageConfig: {},
    styleType: {},
  },
  data() {
    return {
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      echartNum: 8,
      echartOption: {},
      echartsLoading: false,
      echartList: [],
      tooltipFormatter: (data) => {
        let governWorkOrderTooltip = Vue.extend(GovernWorkOrderTooltip);
        let _this = new governWorkOrderTooltip({
          el: document.createElement('div'),
          data() {
            return {
              toolTipData: data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      legendList: [],
      orgCode: '',
    };
  },
  computed: {
    homeStyle() {
      return governWorkOrderStyle[`style${this.styleType}`] || governWorkOrderStyle.style1;
    },
    commonStyle() {
      return commonStyle[`style${this.styleType}`] || commonStyle.style1;
    },
  },
  watch: {},
  created() {
    this.legendList = [
      { name: '已完成', key: 'ywcAmount', color: this.homeStyle.finished },
      { name: '未完成', key: 'wwcAmount', color: this.homeStyle.unfinished },
    ];
    this.initAll();
  },
  methods: {
    async initAll() {
      try {
        this.echartsLoading = true;
        this.echartList = [];

        // 只要重新请求数据， 都需要 重置 到第一页
        this.viewEchartIndex = 0;

        // 每月 天数
        let daysFn = () => {
          let date = new Date(this.year);
          let year = date.getFullYear();
          let month = date.getMonth() + 1;
          let days = new Date(year, month, 0).getDate();
          return days;
        };

        // 1. 先 根据行政区划查询组织机构
        if (!this.orgCode) {
          let result = await this.$http.get(
            `${governanceevaluation.getUserOrgDataByRegioncode}?regioncode=${this.homePageConfig.regionCode}`,
          );
          this.orgCode = result.data.data?.[0]?.nodeInfo?.orgCode || '';
        }

        // 2. 对应【治理工单-工单统计-指派个单位】的入参
        let data = {
          beginTime: `${this.year}-01 00:00:00`,
          endTime: `${this.year}-${daysFn()} 23:59:59`,
          isOwnOrgCode: '0',
          queryOrgCode: this.orgCode,
          queryType: '2',
          statisticsType: 1,
        };
        let res = await this.$http.post(governancetask.getStatisticsList, data);
        let arr = res?.data?.data || [];
        arr = arr.map((item) => {
          return {
            ...item,
            ywcAmount: item.ywcAmount || 0,
            wwcAmount: item.wwcAmount || 0,
            rateSelf: item.rate ? item.rate.split('%')[0] : '0', // rate: '0.0%'
          };
        });
        this.echartList = arr;
        this.setEchartOption();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    setEchartOption() {
      let series = [];
      this.legendList.forEach((item) => {
        let seriesData = [];
        this.echartList.forEach((list) => {
          seriesData.push({
            ...list,
            value: list?.[item.key],
            color: item['color'][0],
          });
        });
        series.push({
          name: item.name,
          data: seriesData,
          barWidth: this.$util.common.fontSize(22),
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          yAxisIndex: 0,
          itemStyle: {
            color: {
              type: 'linear',
              colorStops: [
                { offset: 0, color: item.color[0] },
                { offset: 1, color: item.color[1] },
              ],
            },
          },
        });
      });

      let line = [
        {
          name: '工单完成率',
          type: 'line',
          itemStyle: {
            color: this.homeStyle.finishRate,
          },
          symbol: 'diamond',
          yAxisIndex: 1,
          data: this.echartList.map((item) => {
            return {
              value: item.rateSelf,
              color: this.homeStyle.finishRate,
            };
          }),
          valueType: 'percent',
        },
      ];

      let opts = {
        xAxis: this.echartList.map((item) => item.orgName),
        series: [...series, ...line],
        tooltipFormatter: this.tooltipFormatter,
        tooltipBg: this.commonStyle.tooltipBg,
      };

      this.echartOption = this.$util.doEcharts.baseHomeGovernWorkOrder(opts);
      setTimeout(() => {
        this.setDataZoom('eChartRef', [], this.echartNum);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  height: calc(100% - 30px) !important;
}
.charts {
  width: 100%;
  height: 100% !important;
}
</style>
