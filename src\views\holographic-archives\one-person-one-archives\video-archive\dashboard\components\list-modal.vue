<template>
    <ui-modal
        width="1000"
        v-model="modalShow"
        :footer-hide="true"
        title="同行详情"
    >
        <div class="relationship-content-list">
            <Timeline>
                <TimelineItem
                    v-for="(item, index) in detailList"
                    :key="index"
                >
                    <div>
                        <span>
                            <span>
                                <!-- <span class="header-title">{{ headerItem.fieldNameCn }}: </span> -->
                                <!-- headerItem.color 这个颜色字段作为代码预留，暂时用不上 -->
                                <span class="header-title">地点: </span>

                                <span
                                    class="pr20 header-value"
                                    :style="{ color: '#2C86F8' }">
                                    {{ item.captureAddress}}
                                </span>
                            </span>
                        </span>
                    </div>
                    <div class="about-layout">
                        <div class="row">
                            <div class="col">
                                <div class="img-content">
                                    <ui-image
                                        :src="item.ferriteDetailVo.traitImg"
                                        :viewerImage="item.ferriteDetailVo.sceneImg"
                                        alt="图片"
                                        viewer
                                        :imgStyle="{ objectFit: 'contain' }"
                                        :round="false"
                                    />
                                </div>
                                <div class="rig">
                                    <p v-show-tips>
                                        <span class="header-title1">抓拍时间：</span>
                                        <span>{{ item.ferriteDetailVo.absTime }}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="col">
                                <div class="img-content">
                                    <ui-image
                                        :src="item.faceCapturePeerDetailVo.traitImg"
                                        :viewerImage="item.faceCapturePeerDetailVo.sceneImg"
                                        alt="图片"
                                        viewer
                                        :imgStyle="{ objectFit: 'contain' }"
                                        :round="false"
                                    />
                                </div>
                                <div class="rig">
                                    <p v-show-tips>
                                        <span class="header-title1">抓拍时间：</span>
                                        <span>{{ item.faceCapturePeerDetailVo.absTime }}</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </TimelineItem>
            </Timeline>
            <ui-loading v-if="loading" />
            <ui-empty v-if="detailList.length === 0 && !loading"></ui-empty>
        </div>
    </ui-modal>
</template>
<script>
import { queryFaceCapturePageList } from '@/api/modelMarket';
export default {
    data() {
        return {
            detailList: [ 
                // {
                //     ferriteDetailVo: {},
                //     faceCapturePeerDetailVo: {}
                // } 
            ],
            dataList: [{}],
            loading: false,
            modalShow: false
        }
    },
    methods: {
        init(item, vid) {
            console.log(item, 'itemitemitem22234')
            this.detailList = [];
            this.modalShow = true;
            let params = {
                dateType: '',
                peerMinNumber: item.peerNum,
                peerSecond: '',
                vid: vid,
                recordIdList: item.peerCaptureIdList,
                "pageNumber": 1,
                "pageSize": 10,
            }
            this.loading = true;
            queryFaceCapturePageList(params)
            .then(res => {
                this.detailList = res.data.entities;
            })
            .finally(() => {
                this.loading = false;
            })
        }
    }
}
</script>
<style lang="less" scoped>
.relationship-content-list {
    height: 500px;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    .header-title,
    .header-value {
        font-size: 14px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        font-family: MicrosoftYaHei, MicrosoftYaHei;
    }
    .lsit-content-item {
        margin-top: 10px;
        background: rgba(221, 225, 234, 0.3);
        border-radius: 4px;
        padding: 10px 10px 0 10px;
        & > div {
            padding-bottom: 10px;
        }
        .header-title1 {
            font-size: 12px;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.8);
        }
    }
    // 时间轴样式
    /deep/ .ivu-timeline-item-head {
        width: 14px;
        height: 14px;
        background: #2c86f8;
        border: 3px solid #ffffff;
        left: -1px;
    }
}
.about-layout {
     width: 100%;
    .row {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        .col {
            position: relative;
            box-sizing: border-box;
            padding: 10px;
            flex: 1;
            background: rgba(221, 225, 234, 0.3);
            border-radius: 4px;
            display: flex;
            align-items: center;
            margin: 0 5px;
            .img-content {
                width: 80px;
                height: 80px;
                margin-right: 10px;
            }
            .rig {
                flex: 1;
                font-family: MicrosoftYaHei;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.8);
                line-height: 20px;
                .header-title1 {
                    font-weight: bold;
                }
            }
            .page {
                position: absolute;
                right: 5px;
                bottom: 5px;
                display: flex;
                .page-prev,
                .page-next {
                    width: 24px;
                    height: 24px;
                    text-align: center;
                    line-height: 24px;
                    background: #f9f9f9;
                    border-radius: 4px;
                    border: 1px solid #d3d7de;
                    margin: 0 2px;
                    cursor: pointer;
                    i {
                        font-size: 22px;
                    }e
                    &.disabled {
                        cursor: not-allowed;
                        background: #ccc;
                    }
                }
            }
        }
    }
}
</style>