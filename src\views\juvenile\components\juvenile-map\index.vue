<!--
 * @Date: 2025-01-16 11:07:33
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-03 11:49:15
 * @FilePath: \icbd-view\src\views\juvenile\components\juvenile-map\index.vue
-->
<template>
  <div class="juvenile-map">
    <div class="map-box">
      <mapBase
        ref="mapBase"
        :mapLayerConfig="mapLayerConfig"
        :searchBtn="false"
        :juvenilePlaceList="allPlaceList"
        :layerCheckedNames="layerCheckedNames"
        :layerTypePlaceList="placeKindList"
        @onload="mapLoad"
      />
      <PlaceList
        ref="placeKindListRef"
        class="place-dropdown"
        @changeLayerName="changeLayerName"
        :placeList="placeKindList"
      />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import mapBase from "./mapBase.vue";
import PlaceList from "./placeList.vue";
import {
  getConfigPlaceSecondLevels,
  getConfigPlaces,
} from "@/api/monographic/juvenile";
import { placeType } from "@/map/core/enum/LayerType.js";
export default {
  name: "",
  components: {
    mapBase,
    PlaceList,
  },
  data() {
    return {
      mapLayerConfig: {
        tracing: false, // 是否需要刻画轨迹
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: false, // 框选操作栏
        selectionResult: false, // 是否显示框选结果弹框
        resultOrderIndex: false, // 搜索结果排序,
        showLatestLocation: false, // 显示地图最新位置
      },
      allPlaceList: [],
      layerCheckedNames: [],
      placeKindList: [],
    };
  },
  computed: {},
  async created() {
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    async mapLoad() {
      await this.getPlaceKindList();
      await this.getPlaceList();
      // await this.$refs.mapBase.juvenileAlarmList(this.alarmList);
      this.$refs.placeKindListRef.initAllSelectPlace();
    },
    getPlaceList() {
      getConfigPlaces().then(({ data }) => {
        if (!Array.isArray(data)) {
          data = [];
        }
        this.allPlaceList = data.map((el) => {
          return {
            ...el,
            iconType: this.placeKindList.find(
              (item) => item.key == el.secondLevel
            )?.icon,
            LayerType: el.secondLevel,
          };
        });
      });
    },
    async getPlaceKindList() {
      const { data = [] } = await getConfigPlaceSecondLevels();
      this.placeKindList = data.map((item) => {
        let icon = item.icon;
        if (icon) {
          icon = JSON.parse(icon);
          if (icon["font_class"]) {
            icon["font_class"] = placeType[icon["font_class"]]
              ? icon["font_class"]
              : "hotel";
          }
        } else {
          icon = {
            font_class: "hotel",
            color: "#EB8A5D",
          };
        }
        return {
          ...item,
          key: item.typeCode,
          title: item.typeName,
          icon: icon["font_class"],
          color: icon["color"],
        };
      });
    },
    changeLayerName(value) {
      this.layerCheckedNames = value;
    },
  },
};
</script>

<style lang="less" scoped>
.juvenile-map {
  width: 100%;
  height: 100%;
  .map-box {
    width: 100%;
    height: 100%;
    position: relative;
    .place-dropdown {
      position: absolute;
      top: 20px;
      right: 0;
    }
    .map-alarm {
      position: absolute;
      top: 150px;
      right: 80px;
    }
  }
}
</style>
