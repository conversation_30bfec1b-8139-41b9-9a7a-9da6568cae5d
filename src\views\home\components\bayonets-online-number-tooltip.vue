<template>
  <div class="tooltip-container">
    <div class="tooltip-title-box tooltip-content-box">
      <p>{{ getDate().day }}</p>
      <!-- 当天存在检测数据的 才显示 -->
      <p v-if="getDate().hasDetails" class="pointer link-text-box" onclick="window.openDetails()">详情</p>
    </div>
    <div v-for="(item, index) in data" :key="index" class="tooltip-content-box">
      <p class="legend-box">
        <span class="block" :style="{ 'background-color': item.data.color }"> </span>
        <span> {{ item.seriesName }}在线数量： </span>
        <span :style="{ color: item.data.color }" class="font-num">
          {{ item.value }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'online-changes-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  methods: {
    getDate() {
      return {
        day: this.data[0].data.day || `${this.year}-${this.diffNum(this.data[0].axisValue)}`,
        hasDetails: this.data.some((item) => item.value === 0 || item.value),
      };
    },
    diffNum(num) {
      let str = Number(num);
      if (str < 10) {
        str = `0${str}`;
      }
      return str;
    },
  },
};
</script>

<style lang="less" scoped>
.tooltip-container {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  color: #fff;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    display: flex;
    align-items: center;
    flex-direction: row;
    color: #ffffff;
    .font-vs {
      font-weight: 600;
      color: #1b86ff;
      margin: 0 15px;
    }
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .block {
    display: inline-block;
    height: 2px;
    width: 15px;
    margin-right: 10px;
  }
  .font-num {
    font-weight: 600;
  }
}
</style>
