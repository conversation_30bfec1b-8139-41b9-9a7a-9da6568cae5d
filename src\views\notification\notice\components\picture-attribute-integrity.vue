<template>
  <div class="content">
    <ui-modal v-model="visible" title="查看详情" width="70%">
      <div class="page-picture-attribute auto-fill">
        <div class="table-title">
          <span>
            {{ taskDetail.ORG_CODE }}{{ taskDetail.parentTitle ? '-' + taskDetail.parentTitle : '' }}-{{
              taskDetail.title
            }}（设置分值：{{ taskDetail.score }}分）
          </span>
        </div>
        <div class="table-box auto-fill">
          <ui-table
            class="ui-table auto-fill table-data"
            :table-columns="tableColumns"
            :table-data="tableData"
            show-summary
            :summary-method="handleSummary"
            sumText="最终得分"
            :loading="loading"
            maxHeight="500"
          ></ui-table>
        </div>
      </div>
      <div slot="footer"></div>
    </ui-modal>
    <!--    <two-three-class-->
    <!--      ref="twoThreeClass"-->
    <!--      :twoThreeObj="twoThreeObj"-->
    <!--      v-model="twoThreeClass"-->
    <!--      v-if="twoThreeClass"-->
    <!--    ></two-three-class>-->
    <!--    <car-network ref="carNetwork" v-model="carNetwork" v-if="carNetwork"></car-network>-->
    <!--    <car-online ref="carOnline" v-model="carOnline" v-if="carOnline"></car-online>-->
    <!--    <face-detail v-model="faceDetail" v-if="faceDetail"></face-detail>-->
    <!--    <face-online v-model="faceOnline" v-if="faceOnline"></face-online>-->
    <!--    <face-bauonet-url v-model="faceBauonetUrl" v-if="faceBauonetUrl"></face-bauonet-url>-->
    <!--    <face-focus-url-available-->
    <!--      v-model="faceFocusUrlAvailable"-->
    <!--      v-if="faceFocusUrlAvailable"-->
    <!--    ></face-focus-url-available>-->
    <!--    <face-clock v-model="faceClock" v-if="faceClock"></face-clock>-->
    <!--    <face-focus-upload v-model="faceFocusUpload" v-if="faceFocusUpload"></face-focus-upload>-->
    <!--    <face-upload v-model="faceUpload" v-if="faceUpload"></face-upload>-->
    <!--    <video-modal v-model="showVideoModal"></video-modal>-->
    <!--    <car-modal @idx="idx" v-model="showConfimDataModal" :treeData="treeData"></car-modal>-->
    <!--    <basic-information v-model="basicInformat" v-if="basicInformat"></basic-information>-->
    <!--    <location-intrate v-model="locationIntrate" v-if="locationIntrate"></location-intrate>-->
    <!--    <bookbuilding v-if="bookbuilding" v-model="bookbuilding"></bookbuilding>-->
    <!--    <whole-quantity v-model="wholeQuantity" v-if="wholeQuantity"></whole-quantity>-->
    <!--    <county-quantity-reach-->
    <!--      v-model="countyQuantityReach"-->
    <!--      v-if="countyQuantityReach"-->
    <!--    ></county-quantity-reach>-->
    <!--    <province-quantity-reach-->
    <!--      v-model="provinceQuantityReach"-->
    <!--      v-if="provinceQuantityReach"-->
    <!--    ></province-quantity-reach>-->
    <!--    <city-quantity-reach v-model="cityQuantityReach" v-if="cityQuantityReach"></city-quantity-reach>-->
    <!--    <county-emphasis-ouantity-->
    <!--      v-model="countyEmphasis"-->
    <!--      v-if="countyEmphasis"-->
    <!--    ></county-emphasis-ouantity>-->
    <!--    <province-emphasis-ouantity v-model="province" v-if="province"></province-emphasis-ouantity>-->
    <!--    <city-emphasis-ouantity v-model="cityEmphasis" v-if="cityEmphasis"></city-emphasis-ouantity>-->
    <!--    <emphasis-basic-information-->
    <!--      v-if="basicInformation"-->
    <!--      v-model="basicInformation"-->
    <!--    ></emphasis-basic-information>-->
    <!--    <real-track-uploading-->
    <!--      v-model="realTrackUploading"-->
    <!--      v-if="realTrackUploading"-->
    <!--    ></real-track-uploading>-->
    <!--    <track-accuracy v-if="trackAccuracy" v-model="trackAccuracy"></track-accuracy>-->
    <!--    <poly-file v-if="polyFile" v-model="polyFile"></poly-file>-->
    <!--    <face-network v-model="faceNetwork" v-if="faceNetwork"></face-network>-->
    <!--    <face-snap-upload-integrity v-if="faceSnapUploadIntegrity" v-model="faceSnapUploadIntegrity" />-->
  </div>
</template>
<script>
const renderTableHeader = (h, params) => {
  return h('div', [h('div', {}, params.column.title), h('div', {}, '评测值')]);
};
function renderTableTd(_that) {
  return (h, params) => {
    if (!params.row[params.column.key] && params.row[params.column.key] != 0) {
      return h('span', {}, '--');
    } else if (params.row[params.column.key].evaluationQualified === '3') {
      // 无法考核
      return h('span', {}, params.row[params.column.key].score + '%');
    } else {
      return h(
        'span',
        {
          style: {
            color: 'var(--color-primary)',
            cursor: 'pointer',
          },
          on: {
            click: () => {
              _that.evaluationDetailBtn(params);
            },
          },
        },
        params.row[params.column.key].score + '%',
      );
    }
  };
}
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    taskObj: {
      type: [Object, String],
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    // TwoThreeClass: require('@/views/governanceevaluation/evaluationmanagement/components/two-three-class.vue')
    //   .default,
    // VideoModal: require('@/views/governanceevaluation/evaluationmanagement/components/video-modal.vue')
    //   .default,
    // CarModal: require('@/views/governanceevaluation/evaluationmanagement/components/car-modal.vue')
    //   .default,
    // BasicInformation: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/basic-information.vue')
    //   .default,
    // CarNetwork: require('@/views/governanceevaluation/evaluationmanagement/components/car/car-network.vue')
    //   .default,
    // CarOnline: require('@/views/governanceevaluation/evaluationmanagement/components/car/car-online.vue')
    //   .default,
    // FaceDetail: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-detail.vue')
    //   .default,
    // FaceOnline: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-online.vue')
    //   .default,
    // FaceBauonetUrl: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-bauonet-url.vue')
    //   .default,
    // FaceFocusUrlAvailable: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-focus-url-available.vue')
    //   .default,
    // FaceClock: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-clock.vue')
    //   .default,
    // FaceFocusUpload: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-focus-upload.vue')
    //   .default,
    // FaceUpload: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-upload.vue')
    //   .default,
    // LocationIntrate: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/location-intrate.vue')
    //   .default,
    // Bookbuilding: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/bookbuilding.vue')
    //   .default,
    // WholeQuantity: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/whole-quantity.vue')
    //   .default,
    // CountyQuantityReach: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/county-quantity-reach.vue')
    //   .default,
    // ProvinceQuantityReach: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/province-quantity-reach.vue')
    //   .default,
    // CityQuantityReach: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/city-quantity-reach.vue')
    //   .default,
    // CountyEmphasisOuantity: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/county-emphasis-ouantity.vue')
    //   .default,
    // ProvinceEmphasisOuantity: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/province-emphasis-ouantity.vue')
    //   .default,
    // CityEmphasisOuantity: require('@/views/governanceevaluation/evaluationmanagement/components/view-basics/city-emphasis-ouantity.vue')
    //   .default,
    // EmphasisBasicInformation: require('@/views/governanceevaluation/evaluationmanagement/components/emphasis/emphasis-basic-information.vue')
    //   .default,
    // RealTrackUploading: require('@/views/governanceevaluation/evaluationmanagement/components/emphasis/real-track-uploading.vue')
    //   .default,
    // TrackAccuracy: require('@/views/governanceevaluation/evaluationmanagement/components/emphasis/track-accuracy.vue')
    //   .default,
    // PolyFile: require('@/views/governanceevaluation/evaluationmanagement/components/emphasis/poly-file.vue')
    //   .default,
    // FaceNetwork: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-network.vue')
    //   .default,
    // faceSnapUploadIntegrity: require('@/views/governanceevaluation/evaluationmanagement/components/face/face-snap-upload-integrity.vue')
    //   .default, //人脸抓拍数据上传完整率
  },
  data() {
    return {
      visible: false,
      loading: false,
      row: {},
      taskDetail: {},
      tableColumns: [],
      tableData: [],
      showVideoModal: false, //视频流弹窗
      showConfimDataModal: false, //车辆弹窗
      basicInformat: false, //重点数据-基础信息填报准确率结果详情
      treeData: {}, //树形图数据
      twoThreeObj: {},
      indexList: {},
      twoThreeClass: false, //二三类指标详情
      carNetwork: false,
      carOnline: false,
      faceDetail: false, //人脸卡口设备结果详情
      faceOnline: false,
      faceBauonetUrl: false, //人脸卡口设备URL可用率结果详情
      faceFocusUrlAvailable: false, //重点人脸卡口设备URL可用率
      faceClock: false, //人脸卡口设备时钟准确率结果详情
      faceFocusUpload: false, //人脸卡口设备及时上传率
      faceUpload: false, //重点人脸卡口设备及时上传率
      locationIntrate: false, //视频图像设备位置类型
      bookbuilding: false, //建档率详情弹框
      wholeQuantity: false, //全量目录完整率结果详情弹框
      countyQuantityReach: false, //区县数量达标率结果详情
      provinceQuantityReach: false, //省数量达标率结果详情
      cityQuantityReach: false, //市数量达标率结果详情
      countyEmphasis: false, //重点位置类型视频图像设备数量达标率结果详情
      province: false, //重点位置类型视频图像设备数量达标率结果详情
      cityEmphasis: false, //重点位置类型视频图像设备数量达标率结果详情
      basicInformation: false, //
      realTrackUploading: false, //实时轨迹上传及时性结果详情弹框
      trackAccuracy: false, //轨迹准确率弹框
      polyFile: false, //聚档可用率详情弹框
      faceNetwork: false,
      faceSnapUploadIntegrity: false, //人脸抓拍数据上传完整率
    };
  },
  watch: {
    taskObj: {
      handler(val) {
        if (val) {
          this.taskDetail = val;
          this.tableColumns = [];
          this.tableData = [];
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  methods: {
    init() {
      this.visible = true;
    },
    getDetail() {
      this.loading = true;
      this.$http
        .post(governanceevaluation.getExamStatisticsDetail, {
          examContentItemMonthResultId: this.taskDetail.examContentItemMonthResultId,
        })
        .then((res) => {
          let { headers, body } = res.data.data;
          // 处理表头
          this.handleTableHeaders(headers);
          this.tableColumns = headers;
          // 处理表内容
          this.handleTableBody(body);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    handleTableHeaders(arr, title) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.className = 'header-table';
        v.align = 'center';
        v.parentTitle = title;
        if (v.code !== 'SINGLE_SCORE' && v.code !== 'EVALUATION_TIME') {
          v.renderHeader = renderTableHeader;
        }
        v.minWidth = v.title.length * 14 + 20;
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'EVALUATION_TIME' && v.code !== 'SINGLE_SCORE') v.render = renderTableTd(this);
          delete v.children;
        }
      });
    },
    handleTableBody(arr) {
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        v.forEach((k) => {
          if (k.code === 'EVALUATION_TIME') {
            tableData[i].EVALUATION_TIME = k.evaluationTime;
          } else if (k.code === 'SINGLE_SCORE') {
            tableData[i].SINGLE_SCORE = k.score;
          } else {
            tableData[i][k.code] = k;
          }
        });
      });
      this.tableData = tableData;
    },
    handleSummary({ columns, data }) {
      const sums = {};
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: '最终得分',
          };
          setTimeout(() => {
            document
              .getElementsByClassName(`ivu-table-column-${column.__id}`)
              [this.tableData.length + 1].setAttribute('colspan', this.tableColumns.length - 1);
          }, 100);
          return;
        }
        if (key === 'SINGLE_SCORE') {
          const values = data.map((item) => Number(item[key]));
          if (!values.every((value) => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            let averageValue = v / this.tableData.length;
            sums[key] = {
              key,
              value: Math.round(averageValue) === averageValue ? averageValue : parseFloat(averageValue).toFixed(2),
            };
          } else {
            sums[key] = {
              key,
              value: '',
            };
          }
        } else {
          sums[key] = {
            key,
            value: '',
          };
        }
      });
      return sums;
    },
    evaluationDetailBtn(row) {
      let obj = row.row[row.column.key];
      this.row = {
        strategy: obj.indexType,
        standardsValue: obj.evaluationStandardsValue,
        resultValue: obj.evaluationResultValue,
        resultId: obj.evaluationResultId,
        indexId: obj.indexId,
        examineTime: obj.evaluationTime,
        regionType: obj.regionLevel,
        createTime: obj.evaluationTime,
        indexName: row.column.title,
      };
      /**
       * 二、三类指标弹框
       *
       * 联网数量提升率
       * 可调阅提升率
       * 字幕标注合规率提升率
       * 时钟准确率提升率
       */
      if (
        this.row.strategy === 'VIDEO_NETWORKING_PROMOTION_RATE' ||
        this.row.strategy === 'VIDEO_READ_PROMOTION_RATE' ||
        this.row.strategy === 'VIDEO_OSD_ACCURACY_PROMOTION_RATE' ||
        this.row.strategy === 'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE'
      ) {
        this.twoThreeObj = this.row;
        this.twoThreeClass = true;
        this.$nextTick(() => {
          this.$refs.twoThreeClass.visible = true;
        });
        return;
      }
      if (this.row.strategy === 'VEHICLE_DEVICE_CONNECT_INTERNET') {
        this.carNetwork = true;
        this.$nextTick(() => {
          this.$refs.carNetwork.visible = true;
        });
        return;
      }
      if (this.row.strategy === 'VEHICLE_ONLINE_RATE') {
        this.carOnline = true;
        this.$nextTick(() => {
          this.$refs.carOnline.visible = true;
        });
        return;
      }
      //视频流
      if (this.row.strategy.indexOf('VIDEO') != '-1') {
        this.showVideoModal = true;
      }
      //车辆
      if (this.row.strategy.indexOf('VEHICLE') != '-1') {
        this.showConfimDataModal = true;
        this.getTree(this.row.indexId, this.row.resultId);
      }
      // 人脸卡口
      if (this.row.strategy === 'FACE_CAPTURE_PASS') {
        //人脸卡口设备抓拍合格率
        this.faceDetail = true;
      }
      if (this.row.strategy === 'FACE_ONLINE_RATE') {
        //人脸卡口设备抓拍合格率
        this.faceOnline = true;
      }
      if (this.row.strategy === 'FACE_URL_AVAILABLE') {
        //人脸卡口设备URL可用率
        this.faceBauonetUrl = true;
      }
      if (this.row.strategy === 'FACE_FOCUS_URL_AVAILABLE') {
        //重点人脸卡口设备URL可用率
        this.faceFocusUrlAvailable = true;
      }
      if (this.row.strategy === 'FACE_CLOCK') {
        //人脸卡口设备时钟准确率
        this.faceClock = true;
      }
      if (this.row.strategy === 'FACE_FOCUS_UPLOAD') {
        //人脸卡口设备及时上传率
        this.faceFocusUpload = true;
      }
      if (this.row.strategy === 'FACE_UPLOAD') {
        //重点人脸卡口设备及时上传率
        this.faceUpload = true;
      }
      // 填报准确率
      if (this.row.strategy === 'BASIC_ACCURACY') {
        this.basicInformat = true;
      }
      if (this.row.strategy === 'BASIC_EMPHASIS_LOCATION') {
        // 视频图像设备位置类型
        this.locationIntrate = true;
      }
      // 建档率
      if (this.row.strategy === 'BASIC_INPUT') {
        this.bookbuilding = true;
      }
      //全量目录完整率
      if (this.row.strategy === 'BASIC_FULL_DIR') {
        this.wholeQuantity = true;
      }
      // 达标率
      if (this.row.strategy === 'BASIC_QUANTITY_STANDARD') {
        if (['0', '6', '7', '8', '9', '10', '', null].includes(this.row.regionType)) {
          //区县数量达标率
          this.countyQuantityReach = true;
        }
        if (this.row.regionType == 1) {
          //省数量达标率结果
          this.provinceQuantityReach = true;
        }
        if (['2', '3', '4', '5'].includes(this.row.regionType)) {
          // 市数量达标率结果
          this.cityQuantityReach = true;
        }
      }
      //重点位置类型视频图像设备数量达标率
      if (this.row.strategy === 'BASIC_EMPHASIS_QUANTITY') {
        if (['0', '6', '7', '8', '9', '10', '', null].includes(this.row.regionType)) {
          this.countyEmphasis = true;
        }
        if (this.row.regionType == 1) {
          this.province = true;
        }
        if (['2', '3', '4', '5'].includes(this.row.regionType)) {
          this.cityEmphasis = true;
        }
      }
      if (this.row.strategy === 'FOCUS_ACCURACY') {
        this.basicInformation = true;
      }
      // 实时轨迹上传及时性
      if (this.row.strategy === 'FOCUS_TRACK_REAL') {
        this.realTrackUploading = true;
      }
      //轨迹准确率弹框
      if (this.row.strategy === 'FOCUS_TRACK') {
        this.trackAccuracy = true;
      }
      //聚档可用率详情弹框
      if (this.row.strategy === 'FOCUS_POLY_USABLE') {
        this.polyFile = true;
      }
      if (this.row.strategy === 'FACE_DEVICE_CONNECT_INTERNET') {
        this.faceNetwork = true;
      }
      // 人脸抓拍上传完整率
      if (row.strategy === 'FACE_CAPTURE_COMPLETENESS_RATE') {
        this.faceSnapUploadIntegrity = true;
      }
    },
    getTree(indexId, resultId, outcome) {
      let data = {
        indexId: indexId,
        resultId: resultId,
        outcome: outcome,
      };
      this.$http.post(governanceevaluation.deviceList, data).then((res) => {
        if (res.data.code === 200) {
          if (res.data.data.length === 0) {
            this.treeData = res.data.data;
          } else {
            let data = [{ deviceName: '车辆卡口设备' }];
            data[0].children = res.data.data;
            this.treeData = data;
          }
        }
      });
    },

    idx(val) {
      this.getTree(this.row.indexId, this.row.resultId, val);
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  color: var(--color-content);
}
.page-picture-attribute {
  min-height: 260px;
  /deep/.head-center {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .table-title {
    color: var(--color-content);
    margin-bottom: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
  }
  .table-box {
    padding: 0 20px 20px 20px;
    position: relative;
    .span-btn {
      cursor: pointer;
      color: var(--color-primary);
    }
    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    @{_deep} .ivu-table-tbody {
      td {
        padding: 10px 0 10px 0;
      }
    }
    @{_deep} .ivu-table-body {
      td {
        padding: 10px 0 10px 0;
      }
    }
    @{_deep} .ui-table > .ivu-table-with-summary .ivu-table {
      overflow: hidden;
    }
    @{_deep} .table-data > .ivu-table-with-summary .ivu-table > div {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }
    @{_deep} .table-data > .ivu-table-with-summary .ivu-table > .ivu-table-header {
      overflow: hidden !important;
    }
    /deep/ .ui-table {
      border-top: 1px solid var(--border-table);
      box-sizing: border-box;
      th .ivu-table-cell {
        color: #8797ac;
      }
    }
    /deep/ .ivu-table-tip {
      min-height: 200px;
    }
    /deep/ .ivu-table-body > table {
      border-right: 1px solid var(--border-table);
      box-sizing: border-box;
    }
    /deep/ .ivu-table-header {
      border-right: 1px solid var(--border-table);
      box-sizing: border-box;
    }
    /deep/ .header-table {
      box-shadow: none;
      // box-shadow: inset 1px -1px 0 0 #0d477d;
      border-left: 1px solid var(--border-table);
      border-bottom: 1px solid var(--border-table);
      box-sizing: border-box;
    }
  }
}
/deep/ .ivu-modal-footer {
  padding: 0 !important;
  height: 0 !important;
}
/deep/ .ivu-table-summary {
  background-color: var(--border-table);
  border-top: none;
  .ivu-table-tbody {
    .ivu-table-row td {
      display: none;
    }
    .ivu-table-row td:first-child,
    .ivu-table-row td:last-child {
      display: table-cell;
      font-weight: bold;
      font-size: 14px;
    }
  }
}
/deep/ .ivu-table-summary tr td {
  background-color: transparent;
}
/deep/.ivu-modal-mask {
  z-index: 999 !important;
}
/deep/.ivu-modal-wrap {
  z-index: 999 !important;
}
</style>
