<template>
  <div>
    <FormItem label="每设备抽取图片11">
      <InputNumber
        v-model="formData.captureNum"
        class="input-width"
        placeholder="请输入抽取设备图片"
        clearable
        prop="captureNum"
      ></InputNumber>
    </FormItem>
    <FormItem label="图片抽取范围" class="right-item mb-sm">
      <Select v-model="formData.timeFormat" class="width-mini ml-sm" transfer>
        <Option value="s">全部</Option>
        <Option value="s">当天</Option>
        <Option value="m">昨天</Option>
        <Option value="h">近两天（不包括当天）</Option>
        <Option value="m">近两天（包括当天）</Option>
        <Option value="m">近一周（包括当天）</Option>
        <Option value="m">近一周（不包括当天）</Option>
      </Select>
      <!-- <DatePicker class="picker-start mt-sm" v-model="timeRadio.startTime"
        format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="开始时间" confirm @on-ok="timeOk"
        :class="{ side: timeRadio.isSide }" @on-change="startTimeChange" :options="startTimeOption">
      </DatePicker> -->
    </FormItem>
  </div>
</template>

<script>
export default {
  name: 'ExtractPicture',
  props: {
    captureNum: {},
  },
  data() {
    return {
      formData: {
        captureNum: null,
        //timeFormat: null
      },
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {
    formData: {
      handler(val) {
        this.$emit('updateForm', val);
      },
      deep: true,
    },
    captureNum: {
      handler(val) {
        this.formData.captureNum = val;
      },
      immediate: true,
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.input-width {
  width: 380px;
}
</style>
