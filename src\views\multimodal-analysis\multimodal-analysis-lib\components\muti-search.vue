<!--
 * @Date: 2025-03-07 16:43:35
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-27 15:02:06
 * @FilePath: \icbd-view\src\views\multimodal-analysis\multimodal-analysis-lib\components\muti-search.vue
-->
<template>
  <div class="search-content" style="min-width: 1200px" v-show="!noSearch">
    <div class="search-top">
      <div class="top-left">
        <div class="upload-img-box" v-if="searchImageUrl">
          <div class="delete-box">
            <ui-icon
              type="shanchu"
              :size="20"
              @click.native="() => (searchImageUrl = '')"
            ></ui-icon>
          </div>
          <img :src="searchImageUrl" alt="" class="upload-img" />
        </div>
        <Input
          v-else
          v-model="keyWords"
          :placeholder="placeholder"
          :maxlength="50"
          class="search-input"
        >
        </Input>
      </div>
      <div class="top-right">
        <ui-icon type="mai_line" :size="20" @click.native="openAudio"></ui-icon>
        <Upload
          class="upload"
          :beforeUpload="(file) => beforeUpload(file)"
          action="*"
          :show-upload-list="false"
        >
          <ui-icon type="xiangji" :size="20"></ui-icon>
        </Upload>

        <!-- <SearchPicturesButton ref="SearchPicturesButton" /> -->
        <Button
          type="primary"
          class="search-btn"
          slot="append"
          @click="searchHandler"
        >
          搜索
        </Button>
      </div>
    </div>
    <div class="search-bottom">
      <div class="bottom-left">
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          @change="changeDateType"
        />
      </div>
      <div class="bottom-right">
        <Slider v-model="queryForm.similarity"></Slider>
        <div>{{ queryForm.similarity }}%</div>
        <div class="select-box">
          <Select v-model="queryForm.searchType" style="width: 150px">
            <Option
              v-for="item in searchTypeList"
              :value="item.value"
              :key="item.value"
              placeholder="请选择检索类型"
              >{{ item.label }}</Option
            >
          </Select>
        </div>
        <div class="select-box">
          <Select v-model="taskType" style="width: 150px" clearable>
            <Option
              v-for="item in taskTypeList"
              :value="item.value"
              :key="item.value"
              placeholder="请选择任务类型"
              >{{ item.label }}</Option
            >
          </Select>
        </div>
        <div v-if="taskType">
          <div class="select-detail">
            <Select
              class="select-tag-taskId"
              v-model="queryForm.taskIds"
              placeholder="请选择任务"
              multiple
              filterable
              :max-tag-count="0"
            >
              <Option
                v-for="item in selectTaskList"
                :key="item.id"
                :value="item.id"
                >{{ item.taskName }}</Option
              >
            </Select>
            <div
              class="select-tag-button"
              @click="selectFile()"
              v-if="taskTypeList[2].value === taskType"
            >
              选择文件（{{ selectFileList.length }}）
            </div>
            <div class="select-tag-button" @click="selectDevice()" v-else>
              选择设备（{{ selectDeviceList.length }}）
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      @selectData="selectData"
    />
    <select-file
      :data="fileTreeData"
      ref="selectFile"
      @selectData="selectFileData"
    ></select-file>
    <select-task
      ref="selectTask"
      :type="taskType"
      @selectData="selectTaskData"
    ></select-task>
    <UiAudio ref="audioRef" @confirm="(val) => (keyWords = val)"></UiAudio>
  </div>
</template>

<script>
import UiAudio from "@/components/ui-audio/index.vue";
import selectFile from "./select-file.vue";
import { multiModalTaskPageList } from "@/api/multimodal-analysis";
import { fileUpload } from "@/api/config.js";
export default {
  name: "mutiSearch",
  components: {
    UiAudio,
    selectFile,
  },
  data() {
    return {
      keyWords: "",
      searchImageUrl: "",
      dateType: 1,
      queryForm: {
        searchType: 1, // 检索类型: 1自然语义 2关键字 3混合搜索
        startTime: "",
        endTime: "",
        similarity: 65,
        taskIds: [],
        resourceIds: [],
      },
      taskType: "",
      searchTypeList: [
        {
          value: 1,
          label: "自然语义",
          placeholder:
            "您可以输入任何您要搜索的内容，如：穿着蓝色衣服背黑包的人",
        },
        {
          value: 2,
          label: "关键字",
          placeholder: "请输入关键词检索，多个关键词请用空格隔开",
        },
        // {
        //   value: 3,
        //   label: "混合搜索",
        // },
      ],
      taskTypeList: [
        {
          value: 1,
          label: "实时解析",
        },
        {
          value: 3,
          label: "历史解析",
        },
        {
          value: 5,
          label: "文件解析",
        },
      ],
      noSearch: false,
      selectTaskList: [],
      selectFileList: [],
      selectDeviceList: [],
      fileTreeData: [],
      fristQuery: true,
    };
  },
  mounted() {
    let query = this.$route.query;
    const {
      endDate,
      startDate,
      keyWords,
      noSearch,
      dateType,
      taskIds,
      searchType,
      taskType,
      searchImageUrl,
    } = query;
    this.noSearch = noSearch === "1";
    if (this.noSearch) this.queryForm.similarity = "";
    if (keyWords) this.keyWords = keyWords;
    if (dateType) this.dateType = Number(dateType);
    if (taskIds) this.queryForm.taskIds = taskIds?.split(",") || [];
    if (taskType) this.taskType = Number(taskType);
    if (searchType) this.queryForm.searchType = Number(searchType);
    if (endDate || startDate) {
      this.queryForm.endTime = endDate || "";
      this.queryForm.startTime = startDate || "";
    }
    if (searchImageUrl) this.searchImageUrl = searchImageUrl;
    this.$nextTick(() => {
      this.$refs.quickDateRef.handleInit(this.dateType, startDate, endDate);
      this.searchHandler();
    });
  },
  methods: {
    async queryTaskPageList() {
      const res = await multiModalTaskPageList({
        pageNumber: 1,
        pageSize: 999,
        structuredParsingType: this.taskType,
      });
      this.selectTaskList =
        res.data?.entities?.map((el) => ({ ...el, id: el.id?.toString() })) ||
        [];
    },
    openAudio() {
      this.$refs.audioRef.show();
    },
    searchHandler() {
      // if (!this.keyWords) {
      //   return this.$message.warning("请输入检索内容");
      // }
      let param = {
        ...this.queryForm,
        similarity: this.queryForm.similarity
          ? this.queryForm.similarity / 100
          : "",
        structuredParsingType: this.taskType,
        keywords: this.keyWords,
        searchImageUrl: this.searchImageUrl,
        resourceIds:
          this.taskTypeList[2].value === this.taskType
            ? this.selectFileList.map((el) => el.id)
            : this.selectDeviceList.map((el) => el.deviceId),
      };
      this.$emit("searchHander", param);
    },
    /**
     * @description: 切换日期
     * @param {object} value 当前选中的时间区间
     */
    changeDateType(value) {
      this.queryForm.startTime = value.startDate;
      this.queryForm.endTime = value.endDate;
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    selectData(list) {
      this.selectDeviceList = list;
    },
    selectTaskData() {},
    /**
     * 选择文件
     */
    selectFile() {
      this.fileTreeData = this.selectTaskList
        ?.filter(
          (el) =>
            !this.queryForm.taskIds.length ||
            this.queryForm.taskIds.includes(el.id)
        )
        .map((el) => ({
          id: el.id,
          name: el.taskName,
          isLeaf: false,
          children: el.taskResourceList.map((item) => ({
            id: item.resourceInfo.id,
            isLeaf: true,
            name: item.resourceInfo.resourceName,
          })),
        }));
      this.$refs.selectFile.show(this.selectFileList);
    },
    selectFileData(list) {
      this.selectFileList = list;
    },
    clearAllSelectState() {
      this.queryForm.resourceIds = [];
      this.queryForm.taskIds = [];
      this.selectDeviceList = [];
      this.selectFileList = [];
    },
    //上传图片
    beforeUpload(file) {
      let isAskFile = false;
      isAskFile = /\.(PNG|JPG|JPEG|BMP|png|jpg|jpeg|bmp)$/.test(file.name);
      if (!isAskFile) {
        this.$Message.error("请上传png、jpg、jpeg或bmp格式图片！");
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 30MB!");
      }
      if (!isAskFile || !isLt30M) return false;

      let fileData = new FormData();
      fileData.append("file", file);
      fileUpload(fileData).then((res) => {
        this.searchImageUrl = res?.data?.fileUrl;
      });
      return false;
    },
  },
  computed: {
    placeholder() {
      return this.searchTypeList.find(
        (el) => el.value === this.queryForm.searchType
      )?.placeholder;
    },
  },
  watch: {
    taskType(val) {
      if (this.noSearch) return;
      if (!this.fristQuery) this.clearAllSelectState();
      this.fristQuery = false;
      if (val) this.queryTaskPageList();
    },
  },
};
</script>

<style lang="less" scoped>
.search-content {
  width: 1300px;
  height: 110px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #cdd5e0;
  padding: 12px 20px;
  .search-top {
    display: flex;
    justify-content: space-between;
    height: 50px;
    width: 100%;
    border-bottom: 1px solid #d8d8d8;
    padding-bottom: 10px;
    .top-left {
      display: flex;
      flex: 1;
      .upload-img-box {
        width: 50px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        img {
          max-width: 100%;
          max-height: 100%;
        }
        .delete-box {
          position: absolute;
          z-index: 100;
          background: rgba(0, 0, 0, 0.5);
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: none;
        }
        &:hover .delete-box {
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .search-input {
        /deep/ .ivu-input {
          border: none;
          padding: 0;
        }
        input {
          border: none;
        }
      }
    }
    .top-right {
      width: 168px;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
      align-items: center;
      button {
        width: 100px;
        height: 36px;
        background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
        border-radius: 4px 4px 4px 4px;
      }
    }
  }
  .search-bottom {
    padding-top: 6px;
    display: flex;
    justify-content: space-between;
    .bottom-right {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 10px;
      .select-detail {
        display: flex;
        justify-content: space-between;
        .select-tag-button {
          width: 150px;
          margin-left: 10px;
        }
        .select-tag-taskId {
          width: 150px;
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
