<template>
  <div class="expandRow">
    <ui-table :columns="columns" :data="tableList" :show-header="false" stripe>
      <template #name="{ row }">
        <div class="deviceName" :title="row.name">
          <i
            class="iconfont column-item-icon-camera"
            :class="
              parseInt(row.cameraType) === 0
                ? 'icon-shebeizichan'
                : 'icon-qiuji'
            "
          ></i
          >{{ row.name }}
        </div>
      </template>
      <template #fileName="{ row }">
        <div class="deviceName" :title="row.name">{{ row.name }}</div>
      </template>
      <template #analysisTime="{ row }">
        <div>{{ getAnalysisTime(row) || "--" }}</div>
        <!-- <div>{{ "--" }}</div> -->
      </template>
      <template #createTime="{ row }">
        <div>{{ row.createTime | timeFormat }}</div>
      </template>
      <!-- 任务进度 -->
      <template #analysisSchedule="{ row }">
        <div class="progress-box">
          <Progress
            :stroke-width="6"
            :percent="getPercent(row)"
            :stroke-color="getPercent(row) != 100 ? ['#5BCAFF', '#2C86F8'] : []"
          >
            <span>{{ getPercent(row) + "%" }}</span>
          </Progress>
        </div>
      </template>
      <template #fileResourceStatus="{ row }">
        <div>
          {{ taskStatusList.find((item) => item.key == row.status).label }}
        </div>
      </template>
      <template #fileSizeNum="{ row }">
        <div>
          <!-- resourceType 资源类型： 1图片 2视频 -->
          {{
            row.resourceType == 2
              ? (row.fileSize / 1024 / 1024).toFixed(2) +
                "MB/" +
                row.videoDuration
              : (row.fileSize / 1024 / 1024).toFixed(2) + "MB/--"
          }}
        </div>
      </template>
      <template #opreate="{ row }">
        <div class="opreate">
          <div class="tools">
            <Poptip
              trigger="hover"
              placement="left"
              transfer
              popper-class="expand-row-poptip-box"
            >
              <i class="iconfont icon-gengduo"></i>
              <div class="mark-poptip" slot="content">
                <p
                  @click="handleEdit(row)"
                  :class="{
                    'disabled-p': subTaskStatus == 1 || subTaskStatus == 3,
                  }"
                >
                  <i class="iconfont icon-bianji"></i>编辑
                </p>
                <p @click="handleSearch(row)">
                  <i class="iconfont icon-gaojisousuo"></i>资源检索
                </p>
                <p @click="fileDownload(row)" v-if="subTaskType == 'file'">
                  <i class="iconfont icon-download"></i>文件下载
                </p>
                <p @click="handleMap(row)">
                  <i class="iconfont icon-xunjianguiji"></i>地图定位
                </p>
                <p @click="handleDel(row)">
                  <i class="iconfont icon-shanchu1"></i>删除
                </p>
              </div>
            </Poptip>
          </div>
        </div>
      </template>
    </ui-table>
  </div>
</template>
<script>
import { taskStatusList } from "../enums/index.js";
import { mapGetters } from "vuex";

export default {
  name: "expandRow",
  props: {
    tableList: {
      type: Array,
      default: () => [],
    },
    columns: {
      type: Array,
      default: () => [],
    },
    currentJob: {
      type: Object,
      default: () => {},
    },
    subTaskType: {
      type: String,
      default: "",
    },
    switchLoading: {
      type: Boolean,
      default: false,
    },
    subTaskStatus: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      taskStatusList,
    };
  },
  computed: {
    ...mapGetters({
      structDataTypeList: "dictionary/getStructDataType", //解析类型
      structTaskStatusList: "dictionary/getStructTaskStatus", //实时视频解析任务状态
      structHistroytaskStatusList: "dictionary/getStructHistroytaskStatus", //历史视频解析任务状态
      filestrucureTaskStatusList: "dictionary/getFilestrucureTaskStatus", //文件解析任务状态
    }),
  },
  mounted() {},
  methods: {
    handleEdit(row) {
      // 运行中和已完成不能编辑
      if (this.subTaskStatus.status == 1 || this.subTaskStatus.status == 3) {
        return;
      }
      this.$emit("handleEdit", row);
    },
    handleSearch(row) {
      this.$emit("handleSearch", row, this.currentJob);
    },
    handleMap(row) {
      this.$emit("handleMap", row);
    },
    handleDel(row) {
      this.$emit("handleDel", row, this.currentJob);
    },
    fileDownload(row) {
      this.$emit("fileDownload", row);
    },
    handleDownloadFile(row) {
      if (!row.originalFilePfsPath) return;
      this.$emit("handleDownloadFile", row);
    },
    getPercent(row) {
      const { successCount = 0, failCount = 0, total = 0, status } = row;
      // 已完成 但是 总数是0的时候
      if (status == 3 && total == 0) {
        return 100;
      }
      // 防止被除数为0导致报错
      if (total) {
        return Math.floor(((successCount + failCount) / total) * 100);
      }
      return 0;
    },
    getAnalysisTime(row) {
      let { execEndTime, execStartTime } = row;
      // 有开始时间没结束时间用当前时间作为结束时间,计算目前已经处理的时长
      if (execStartTime && !execEndTime) {
        execEndTime = new Date().format("yyyy-MM-dd hh:mm:ss");
      }
      if (!execEndTime || !execStartTime) {
        return "--";
      }
      const x = this.$dayjs(execStartTime);
      const y = this.$dayjs(execEndTime);
      const result = y.diff(x, "minute", true);
      const hour = Math.floor(result / 60);
      const min = Math.floor(result % 60);
      const second = Math.round((result % 1) * 60);
      return hour + "时" + min + "分" + second + "秒";
    },
  },
};
</script>
<style lang="less" scoped>
.expandRow {
  /deep/ .ivu-table-wrapper {
    height: auto !important;
  }

  /deep/ .ivu-table-body {
    height: auto !important;
  }

  .ivu-table-tbody td {
    background-color: #f9f9f9 !important;
  }

  .deviceName {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    i {
      margin-right: 5px;
      color: #2c86f8;
    }
  }

  .opreate {
    display: flex;
  }

  .tools {
    color: #2c86f8;
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    margin-left: 5px;

    .icon-gengduo {
      transform: rotate(90deg);
      transition: 0.1s;
      display: inline-block;
    }

    p:hover {
      color: #2c86f8;
    }

    &:hover {
      background: #2c86f8;
      color: #fff;

      .icon-gengduo {
        transform: rotate(0deg);
        transition: 0.1s;
      }

      border-radius: 10px;
    }
  }
}

.mark-poptip {
  width: 128px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
  padding: 10px 0px;
  border-radius: 4px;

  p {
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.8);
    padding: 0 20px;
    text-align: left;
    cursor: pointer;

    i {
      color: #2c86f8;
      margin-right: 10px;
    }

    &:hover {
      background: rgba(44, 134, 248, 0.1028);
    }
  }
}

.disabled-p {
  color: #e9e9e9 !important;

  i {
    color: #e9e9e9 !important;
  }

  cursor: not-allowed !important;
}

.progress-box {
  /deep/ .ivu-progress-inner {
    background: #dde1ea !important;
  }

  /deep/ .ivu-progress-normal {
    .ivu-progress-text {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
    }
  }
  /deep/ .ivu-progress-success {
    .ivu-progress-text {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
    }
  }
}
</style>
<style lang="less">
.expand-row-poptip-box.ivu-poptip-popper {
  width: auto !important;
  min-width: unset;

  .ivu-poptip-body {
    padding: 0;
  }
}
</style>
