<template>
  <!-- 治理达标率 -->
  <div class="standard-govern" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-zhilidabiaoshuai" v-model="activeValue" :data="tabData"> </HomeTitle>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: echartData }">
      <template>
        <div v-for="(item, index) in this.echartData" :key="index" class="govern-list">
          <span class="region-name ellipsis">{{ item.regionName }}</span>
          <Tooltip placement="top" transfer style="width: 100%">
            <Progress
              style="width: 96%"
              class="mb-sm pl-sm nowrap"
              :percent="Number(item.indexRate)"
              :stroke-width="12"
              :stroke-color="colorList[index % colorList.length]"
            />
            <div slot="content">
              <p class="f-12">{{ item.regionName }}</p>
              <p>
                指标总数：<span class="mb-sm f-12" style="color: #fff">{{
                  item.abnormalAmount + item.qualifiedAmount || 0 | separateNum
                }}</span>
              </p>
              <p>
                达标指标数：<span class="mb-sm f-12" style="color: #fff">{{
                  item.qualifiedAmount || 0 | separateNum
                }}</span>
              </p>
              <p>
                达标指标率：<span class="mb-sm f-12" style="color: #fff">{{ item.indexRate }} %</span>
              </p>
            </div>
          </Tooltip>
        </div>
      </template>
    </div>
  </div>
</template>
<style lang="less" scoped>
.nowrap {
  white-space: nowrap;
}
.standard-govern {
  position: absolute;
  top: 33.5%;
  right: 10px;
  width: 400px;
  height: 31%;
  background: rgba(0, 104, 183, 0.13);
  .govern-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .echarts-box {
    width: 400px;
    height: calc(100% - (32px + 10px));
    overflow-y: auto;
    margin-top: 10px;
    .govern-list {
      display: flex;
    }
    .region-name {
      width: 56px !important;
      text-align: left;
      color: rgba(190, 226, 251, 1);
      font-size: 12px;
      margin-left: 8px;
    }
    @media screen and (max-width: 1366px) {
      .region-name {
        width: 60px !important;
        text-align: left;
        color: rgba(190, 226, 251, 1);
        font-size: 12px;
        margin-left: 8px;
      }
    }
    @{_deep}.ivu-progress-inner {
      background-color: rgba(255, 255, 255, 0.2) !important;
    }
    @{_deep}.ivu-progress-text {
      margin-left: 10px !important;
      color: rgba(42, 167, 211, 1) !important;
    }
    @media screen and (max-width: 1366px) {
      @{_deep}.ivu-progress-text {
        margin-left: 0px !important;
        color: rgba(42, 167, 211, 1) !important;
      }
      @{_deep} .ivu-progress-show-info {
        @{_deep}.ivu-progress-outer {
          padding-left: 0 !important; /* no */
          margin-left: 0 !important; /* no */
        }
      }
      @{_deep}.ivu-progress-outer {
        width: 96% !important; /* no */
      }
    }

    .pl-sm {
      padding-left: 10px;
    }
  }
}
.full-screen-container {
  top: 36%;
  height: 31.5%;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'standard-govern',

  data() {
    return {
      echartData: [],
      echartsLoading: false,
      colorList: ['#11CDD4', '#0CD084', '#CA783F', '#683AED', '#3C88EB', '#CBBDDE', '#E1637B', '#19B47F', '#A09758'],
      activeValue: 'controlRate',
      tabData: [{ label: '治理达标率', id: 'controlRate' }],
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  async mounted() {
    await this.getIndexEvaluationResultStatistics();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }
      return result;
    },
  },
  methods: {
    async getIndexEvaluationResultStatistics() {
      this.echartsLoading = true;
      this.echartData = [];
      try {
        let res = await this.$http.get(home.getIndexEvaluationResultStatistics);
        this.echartData = res.data.data;
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
  },

  components: {
    HomeTitle: require('./home-title').default,
  },
};
</script>
