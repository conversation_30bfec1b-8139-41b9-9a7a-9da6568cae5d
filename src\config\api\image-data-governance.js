export default {
  getDataTask: '/ivdg-image-data-governance-app/dataTask/select',
  addDataTask: '/ivdg-image-data-governance-app/dataTask/add',
  startOrPauseDataTask: '/ivdg-image-data-governance-app/dataTask/startOrPause',
  updateDataTask: '/ivdg-image-data-governance-app/dataTask/update',
  getVideoQualityDetectMode: '/ivdg-image-data-governance-app/dataTask/getVideoQualityDetectMode',
  queryList: '/ivdg-image-data-governance-app/imageQuality/queryList',
};
