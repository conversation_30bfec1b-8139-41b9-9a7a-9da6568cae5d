<template>
  <div class="base-search">
    <div class="over-flow">
      <ui-label class="inline mr-lg" label="组织机构">
        <api-organization-tree
          class="organization-tree"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectOrgCode"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label class="inline" label="关键词">
        <Input class="input-width" placeholder="请输入设备名称/设备编码" v-model="searchData.keyWord"></Input>
      </ui-label>
      <ui-label
        class="inline ml-lg"
        :label="`${global.filedEnum.sbgnlx}`"
        v-if="!['5', '6', '7'].includes(activeRow.governanceContent)"
      >
        <Select
          v-model="searchData.sbgnlx"
          clearable
          :placeholder="`请选择设备${global.filedEnum.sbgnlx}`"
          class="select-width"
        >
          <Option v-for="(item, index) in sxjgnlx_receive" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label
        class="inline ml-lg bottom-margin"
        :label="global.filedEnum.sbdwlx"
        v-if="!['4', '6', '7'].includes(activeRow.governanceContent)"
      >
        <Select
          v-model="searchData.sbdwlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          class="select-width"
        >
          <Option v-for="(item, index) in propertySearch_sbdwlx" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label
        class="inline ml-lg bottom-margin"
        label="设备类型"
        v-if="!['4', '5', '6', '7'].includes(activeRow.governanceContent)"
      >
        <Select v-model="searchData.ptzType" clearable placeholder="请选择设备类型" class="select-width">
          <Option v-for="(item, index) in propertySearch_ptztype" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <ui-label
        class="inline ml-lg bottom-margin"
        :label="`${global.filedEnum.macAddr}`"
        v-if="['6'].includes(activeRow.governanceContent)"
      >
        <Input
          class="input-width"
          :placeholder="`请输入${global.filedEnum.macAddr}`"
          v-model="searchData.macAddr"
        ></Input>
      </ui-label>
      <ui-label class="inline ml-lg bottom-margin" label="设备状态" v-if="['7'].includes(activeRow.governanceContent)">
        <Select class="width-md" v-model="searchData.phyStatus" clearable placeholder="请选择设备状态">
          <Option value="1" label="可用"></Option>
          <Option value="2" label="不可用"></Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg bottom-margin mr-lg" label="治理结果">
        <Select class="select-width" placeholder="请选择治理结果" clearable v-model="searchData.taskStatus">
          <Option v-for="(item, index) in dataStatusList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" class="mr-sm" @click="$emit('startSearch', searchData)"> 查询 </Button>
        <Button type="default" @click="clearForm"> 重置 </Button>
      </div>
      <!-- <ui-label :width="60" class="inline rigth-margin bottom-margin" label="">
        
      </ui-label> -->
    </div>
    <div class="export-btn">
      <Button v-if="showExportBtn" class="ml-sm fr" type="primary" @click="$emit('onExport')" :loading="exportLoading">
        <i class="icon-font icon-daochu f-12"></i>
        <span class="vt-middle ml-sm">导出</span>
      </Button>
    </div>
  </div>
</template>
<script>
import user from '@/config/api/user';
import { mapGetters } from 'vuex';
export default {
  props: {
    dataStatusList: {
      deafult: () => {
        return [
          { dataKey: '1', dataValue: '已同步' },
          { dataKey: '2', dataValue: '未同步' },
        ];
      },
    },
    activeRow: {},
    exportLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      dataSourceList: [],
      searchData: {
        keyWord: '',
        sbgnlx: '',
        sbdwlx: '',
        ptzType: '',
        taskStatus: '',
        macAddr: '',
        phyStatus: '',
      },
      dicDataEnum: Object.freeze({
        sxjgnlx_receive: 'sxjgnlx_receive',
        propertySearch_sbdwlx: 'propertySearch_sbdwlx',
        propertySearch_ptztype: 'propertySearch_ptztype',
      }),
      sxjgnlx_receive: [],
      propertySearch_sbdwlx: [],
      propertySearch_ptztype: [],
    };
  },
  async activated() {
    this.copySearchDataMx(this.searchData);
    this.initDicContent();
  },
  methods: {
    selectOrgCode(data) {
      this.searchData.orgCode = data.orgCode;
      // this.search()
    },
    clearForm() {
      // this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode
      this.selectOrgTree.orgCode = null;
      this.resetSearchDataMx(this.searchData, () => this.$emit('startSearch', this.searchData));
    },
    selectInfo(infoList) {
      this.searchData.errorTypeItem = infoList.map((item) => item.dataKey);
      this.$emit('startSearch', this.searchData);
    },
    async initDicContent() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = data.data.find((row) => {
            return !!row[key];
          });
          this[this.dicDataEnum[key]] = obj[key];
        });
      } catch (err) {
        console.log(err);
      }
    },
    async initSource(typekey) {
      try {
        let res = await this.$http.get(user.queryByTypeKey, {
          params: { typekey: typekey },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
    showExportBtn() {
      return ['1', '2', '3', '4', '5', '6', '7'].includes(this.activeRow.governanceContent);
    },
  },
  watch: {},
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  .organization-tree {
    @{_deep}.select-width {
      width: 160px;
    }
  }
  .select-width {
    width: 160px;
  }
  .input-width {
    width: 160px;
  }
  .ui-label {
    line-height: 40px;
  }
  .select-div {
    display: flex;
    .select-span {
      display: inline-block;
      height: 45px;
      line-height: 45px;
      color: #fff;
      font-size: 14px;
    }
    .tabs {
      flex: 1;
    }
  }
  .export-btn {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }
}
</style>
