<template>
  <div class="interface-exception auto-fill">
    <Collapse v-model="collapseValue" simple>
      <Panel name="1" class="mb-sm">
        <span class="title">人脸相关接口</span>
        <i-switch
          class="fr switch"
          v-model="formData['0301'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <CheckboxGroup v-model="formData['0301'].trigger">
              <Checkbox label="030101" :disabled="!isFaceOpen">
                <span>未响应</span>
              </Checkbox>
              <Checkbox class="ml-lg" label="030102" :disabled="!isFaceOpen">
                <span>返回结果错误</span>
              </Checkbox>
            </CheckboxGroup>
          </ui-label>
          <ui-label label="接口类型：" class="mt-sm">
            <CheckboxGroup v-model="formData['0301'].source">
              <Checkbox label="030101" :disabled="!isFaceOpen">
                <span>人脸布控接口</span>
              </Checkbox>
              <Checkbox class="ml-lg" label="030102" :disabled="!isFaceOpen">
                <span>分布式身份确认接口</span>
              </Checkbox>
              <Checkbox class="ml-lg" label="030103" :disabled="!isFaceOpen">
                <span>人像轨迹查询接口</span>
              </Checkbox>
            </CheckboxGroup>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0301'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isFaceOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0301', index)"
                  @on-clear="clearTask('0301', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0301', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isFaceOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0301')"
                ></i>
                <i
                  v-if="index !== 0 && isFaceOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0301', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0301')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0301')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0301')"
              :table-data="receiveData['0301'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isFaceOpen ? '' : 'not-allowed']"
                  @click="() => isFaceOpen && selectNotification('0301', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isFaceOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0301', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0301'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0301systemTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isFaceOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0301dialogTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isFaceOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0301homeTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isFaceOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0301smsTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
      <Panel name="2">
        <span class="title">车辆相关接口</span>
        <i-switch
          class="fr switch"
          v-model="formData['0302'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <CheckboxGroup v-model="formData['0302'].trigger">
              <Checkbox label="030201" :disabled="!isVehicleOpen">
                <span>未响应</span>
              </Checkbox>
              <Checkbox class="ml-lg" label="030202" :disabled="!isVehicleOpen">
                <span>返回结果错误</span>
              </Checkbox>
            </CheckboxGroup>
          </ui-label>
          <ui-label label="接口类型：" class="mt-sm">
            <CheckboxGroup v-model="formData['0302'].source">
              <Checkbox label="030201" :disabled="!isVehicleOpen">
                <span>车辆布控接口</span>
              </Checkbox>
              <Checkbox class="ml-lg" label="030202" :disabled="!isVehicleOpen">
                <span>车辆轨迹查询接口</span>
              </Checkbox>
            </CheckboxGroup>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0302'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isVehicleOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0302', index)"
                  @on-clear="clearTask('0302', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0302', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isVehicleOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0302')"
                ></i>
                <i
                  v-if="index !== 0 && isVehicleOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0302', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0302')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0302')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0302')"
              :table-data="receiveData['0302'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isVehicleOpen ? '' : 'not-allowed']"
                  @click="() => isVehicleOpen && selectNotification('0302', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isVehicleOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0302', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0302'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0302systemTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isVehicleOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0302dialogTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isVehicleOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0302homeTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isVehicleOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0302smsTemplateRef">
                    【<span>#组织机构#_#接口类型#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
    </Collapse>
    <people-select-module
      v-model="peopleSelectShow"
      title="选择通知对象"
      :default-people-list="defaultPeopleList"
      @pushPeople="pushPeople"
    ></people-select-module>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      collapseValue: '',
      listTaskSchemes: [], //检测任务
      peopleSelectShow: false,
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          type: 'index',
        },
        {
          type: 'expand',
          width: 50,
        },
        {
          title: '所属组织机构',
          minWidth: 300,
          key: 'orgName',
        },
        {
          title: '配置通知接收人',
          minWidth: 300,
          slot: 'people',
        },
        {
          width: 300,
          title: '操作',
          slot: 'option',
        },
      ],
      formData: {
        '0301': {
          markEnable: 0,
          source: [], //对接类型
          trigger: [], //通知条件
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
        '0302': {
          markEnable: 0,
          source: [], //对接类型
          trigger: [], //通知条件
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
      },
      // 由于后端返回的数据不能兼容前端页面所以这里要单独处理接收人的数据
      receiveData: {
        '0301': {
          receiveConfig: [],
        },
        '0302': {
          receiveConfig: [],
        },
      },
      defaultPeopleList: [],
      receiveConfigData: null, //点击配置接收人时记录
    };
  },
  async created() {
    await this.getListTaskSchemes();
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     */
    await this.initMx(['0301', '0302']);
    this.dealReceiveCofingEchoMx();
  },
  methods: {
    ...mapActions({
      setCopyReceiveConfig: 'systemconfiguration/setCopyReceiveConfig',
    }),
    selectNotification(type, org) {
      this.receiveConfigData = {
        type,
        org,
      };
      this.defaultPeopleList = this.getDefaultPeopleMx(org.peopleList);
      this.peopleSelectShow = true;
    },
    pushPeople(list) {
      // 如果已经保存的数据中的联系电话已经有，则以已保存的电话为填写值
      const peopleList = list.map((row) => {
        const people = this.defaultPeopleList.find((people) => people.id === row.id);
        if (people && people.phone) {
          this.$set(row, 'phone', people.phone);
        } else {
          this.$set(row, 'phone', row.phoneNumber);
        }
        return row;
      });
      // 根据点击selectNotification时的组织机构赋值
      const receiveOrg = this.receiveData[this.receiveConfigData.type].receiveConfig.find(
        (row) => row.orgCode === this.receiveConfigData.org.orgCode,
      );
      receiveOrg.peopleList = peopleList.map(({ id, orgCode, orgName, name, username, phone }) => {
        return {
          id,
          name,
          orgCode,
          orgName,
          username,
          phone,
          orgCodeByAttach: receiveOrg.orgCode,
          orgCodeByAttachName: receiveOrg.orgName,
        };
      });
    },
    reset() {
      this.receiveData = this.$util.common.deepCopy(this.initializedReceiveDataMx);
      this.resetMx();
    },
    async save() {
      try {
        this.dealReceiveCofingSaveMx();
        this.formData['0301'].systemTemplate = this.$refs['0301systemTemplateRef'].innerText;
        this.formData['0301'].dialogTemplate = this.$refs['0301dialogTemplateRef'].innerText;
        this.formData['0301'].homeTemplate = this.$refs['0301homeTemplateRef'].innerText;
        this.formData['0301'].smsTemplate = this.$refs['0301smsTemplateRef'].innerText;
        this.formData['0302'].systemTemplate = this.$refs['0302systemTemplateRef'].innerText;
        this.formData['0302'].dialogTemplate = this.$refs['0302dialogTemplateRef'].innerText;
        this.formData['0302'].homeTemplate = this.$refs['0302homeTemplateRef'].innerText;
        this.formData['0302'].smsTemplate = this.$refs['0302smsTemplateRef'].innerText;
        await this.saveMx(['0301', '0302']);
        // this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
    async getListTaskSchemes() {
      try {
        this.listTaskSchemes = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.listTaskSchemes = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    addTask(type) {
      this.formData[type].taskIds.push('');
    },
    deleteTask(type, index) {
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds.splice(index, 1);
      this.delReceiveOrgMx(type, this.formData);
    },
    async selectTask(val, type, index) {
      if (!val) return;
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds[index] = val;
      await this.setReceiveOrgMx(val, type);
      this.delReceiveOrgMx(type, this.formData);
    },
    clearTask(type, index) {
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds[index] = '';
      this.delReceiveOrgMx(type, this.formData);
    },
    deleteRow(type, index) {
      this.receiveData[type].receiveConfig.splice(index, 1);
    },
    copyConfig(type) {
      this.setCopyReceiveConfig(this.$util.common.deepCopy(this.receiveData[type].receiveConfig));
      this.$Message.success('复制成功');
    },
    paste(type) {
      if (!this.copyReceiveConfig) return;
      this.receiveData[type].receiveConfig = this.$util.common.deepCopy(this.copyReceiveConfig);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      copyReceiveConfig: 'systemconfiguration/getCopyReceiveConfig',
    }),
    isEdit() {
      return this.action === 'edit';
    },
    isFaceOpen() {
      return this.formData['0301'].markEnable === 1 && this.action === 'edit';
    },
    isVehicleOpen() {
      return this.formData['0302'].markEnable === 1 && this.action === 'edit';
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PeopleSelectModule: require('@/components/people-select-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
.interface-exception {
  overflow-y: auto;
  .task-list {
    width: 310px;
    max-height: 300px;
    overflow-y: auto;
  }
  .table-module {
    clear: both;
    height: 260px;
  }
  .notification-method {
    label {
      display: block;
      margin-bottom: 5px;
    }
    &:nth-child(n + 2) {
      margin-left: 80px;
    }
  }
  .not-allowed {
    cursor: not-allowed;
  }
  .message-content {
    margin-left: 10px;
    display: inline-block;
    border: 1px solid var(--border-input);
    padding: 0 10px;
    background-color: var(--bg-input);
    width: 760px;
    vertical-align: text-top;
    border-radius: 4px;
    span {
      color: var(--color-title);
    }
  }
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
      color: var(--color-primary);
    }
    .not-copy {
      color: var(--color-btn-primary-disabled);
      cursor: not-allowed;
    }
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: var(--color-navigation-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: var(--bg-collapse-item);
        height: 50px;
        line-height: 50px;
        margin: 0 20px;
        .title {
          font-size: 16px;
          font-weight: 900;
        }
        .switch {
          top: 50%;
          transform: translateY(-50%);
          margin-right: 20px;
        }
        i {
          float: right;
          color: #fff;
          line-height: 50px;
          font-size: 20px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: var(--bg-content);
        .ivu-collapse-content-box {
          padding: 10px 40px;
        }
      }
      &.ivu-collapse-item-active {
        background: var(--bg-content);
      }
    }
  }
}
</style>
