<template>
  <div class="searchForm">
    <Form :inline="true" :label-width="75">
      <FormItem label="身份证号:">
        <Input
          v-model="formData.idCardNo"
          placeholder="请输入"
          maxlength="20"
          style="width: 220px"
        ></Input>
      </FormItem>
      <FormItem label="抓拍时段:">
        <hl-timerange
          ref="timerange"
          :captureTimePeriod="captureTimePeriod"
          @onceChange="handleTimeChange"
          @change="handleTimeChange"
          :reflectValue="formData.perceiveDate"
          :reflectTime="{
            startDate: formData.startDate,
            endDate: formData.endDate,
          }"
        >
        </hl-timerange>
      </FormItem>
      <FormItem label="设备资源:">
        <div class="select-tag-button" @click="selectDevice()">
          选择设备/已选（{{ formData.selectDeviceList.length }}）
        </div>
      </FormItem>
      <FormItem label="状态:">
        <Select
          v-model="formData.associationIdCardStatus"
          placeholder="请选择"
          style="width: 220px"
          clearable
        >
          <Option
            :value="item.dataKey"
            v-for="(item, index) in associationIdCardStatusList"
            :key="index"
            >{{ item.dataValue }}</Option
          >
        </Select>
      </FormItem>
    </Form>
    <div class="btn-group ml-20">
      <Button type="primary" @click="searchHandle">查询</Button>
      <Button @click="resetHandle">重置</Button>
    </div>
    <!-- 选择设备 -->
    <select-nonMotorIllegal-device ref="selectDevice" @selectData="selectData" />
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import selectNonMotorIllegalDevice from '@/components/select-modal/select-nonMotorIllegal-device.vue'
export default {
  name: "",
  components: {
    selectNonMotorIllegalDevice
  },
  data() {
    return {
      formData: {
        perceiveDate: "24小时",
        startDate: "",
        endDate: "",
        idCardNo: "",
        selectDeviceList: [],
        associationIdCardStatus: undefined,
      },
      captureTimePeriod: [
        { name: "24小时", value: "1" },
        { name: "近7天", value: "2" },
        { name: "近30天", value: "3" },
        { name: "自定义", value: "4" },
      ],
    };
  },
  computed: {
		...mapGetters({
			associationIdCardStatusList: 'dictionary/getAssociationIdCardStatusList', // 状态
		})
	},
  watch: {},
  async created() {
		await this.getDictData();
		this.searchHandle();
	},
  methods: {
    ...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
    searchHandle() {
      this.$emit("search", this.formData);
    },
    resetHandle() {
      this.formData = {
        perceiveDate: "24小时",
        startDate: "",
        endDate: "",
        idCardNo: "",
        selectDeviceList: [],
        associationIdCardStatus: undefined,
      };
      this.$refs.timerange.clearChecked(false);
    },
    handleTimeChange(obj) {
      this.formData.perceiveDate = obj.timeSlot;
      this.formData.startDate = obj.startDate;
      this.formData.endDate = obj.endDate;
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.formData.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(ids) {
      this.formData.selectDeviceList = ids;
    },
  },
};
</script>

<style lang='less' scoped>
.searchForm {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  .ivu-form-item {
    margin-bottom: 15px;
    /deep/.ivu-form-item-content {
      height: 34px;
      display: flex;
      align-items: center;
    }
  }
}
</style>
