module.exports = {
  plugins: {
    // 这是rem适配的配置  注意： remUnit在这里要根据lib-flexible的规则来配制，如果您的设计稿是750px的，用75就刚刚好。
    'postcss-pxtorem': {
      rootValue: 192, // 结果为：设计稿元素尺寸/16，比如元素宽320px,最终页面会换算成 20rem
      propList: ['*', '!border', '!border-top', '!border-bottom', '!border-left', '!border-right'],
      selectorBlackList: ['border']
    },
    'autoprefixer': {
      overrideBrowserslist: [
        'Android 4.1',
        'iOS 7.1',
        'Chrome > 31',
        'ff > 31',
        'ie >= 8'
        // 'last 10 versions', // 所有主流浏览器最近2个版本
      ],
      grid: true
    }
  }
}
