<template>
  <common-form v-bind="$props" ref="formData">
    <template #after="{ row }">
      <FormItem label="设备合格率" class="right-item mb-sm" prop="name">
        <InputNumber
          v-model.number="row.deviceTargetValue"
          :max="100"
          :min="1"
          placeholder="请输入设备合格率"
          class="form-item-width"
          :disabled="isView"
        ></InputNumber>
        <span class="base-text-color ml-sm">%</span>
        <div class="color-failed form-item-width">
          <span> 抽取</span>
          <span>{{ row.indexModule === '9' ? '人体' : row.indexModule === '3' ? '车辆' : '人脸' }}</span>
          <span>卡口设备的若干抓拍图片，如果有达到填写阈值的图片满足评价标准，则设备合格。</span>
        </div>
      </FormItem>
    </template>
  </common-form>
</template>
<script>
export default {
  name: 'basic-full-dir',
  data() {
    return {};
  },
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
  },
  methods: {
    async validate() {
      try {
        let data = await this.$refs.formData.validate();
        return {
          id: data.id,
          dataTargetValue: data.dataTargetValue,
          deviceTargetValue: this.formData.deviceTargetValue,
          evaluationCriterion: this.formData.evaluationCriterion,
        };
      } catch (error) {
        throw new Error(error);
      }
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
  },
};
</script>
<style scoped lang="less">
.form-item-width {
  width: 380px;
}
</style>
