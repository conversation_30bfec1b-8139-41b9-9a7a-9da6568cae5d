<template>
  <ui-modal v-model="visible" title="经纬度偏移检测配置" width="39.06rem" @query="handleSave">
    <Form ref="formValidate" :label-width="0">
      <FormItem label="" prop="point">
        <div class="testing-item">
          <p>
            <span class="base-text-color">经纬度与安装地址偏移距离不超过</span>
            <InputNumber v-model="longitudeLatitudeOffset" class="form-width ml-sm" />
            <span class="base-text-color ml-sm">米</span>
          </p>
        </div>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    /**
     * 指标详情
     */
    formData: {
      required: true,
      default: () => {},
    },
    /**
     * 规则详情
     */
    indexRule: {
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      longitudeLatitudeOffset: 2000,
      indexConfig: {},
    };
  },
  created() {},
  methods: {
    resetForm() {
      this.longitudeLatitudeOffset = 2000;
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getLngLat();
    },
    async getLngLat() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        if (data.rulePropertyList) {
          this.longitudeLatitudeOffset = data.rulePropertyList[0].repeatCount || 2000;
        }
      } catch (error) {
        console.log(error);
      }
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        if (!this.longitudeLatitudeOffset) {
          return this.$Message.error('请输入偏移距离');
        }
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          repeatCount: this.longitudeLatitudeOffset,
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.form-width {
  width: 118px;
}
@{_deep} .ivu-modal-body {
  margin-top: 0 !important;
  padding: 20px 50px !important;
  text-align: center;
}
</style>
