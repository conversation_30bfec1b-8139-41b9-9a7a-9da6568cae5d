<template>
  <ui-card :title="title" class="law-behavior">
    <div class="law-behavior-content">
      <div class="btns">
        <ui-time-select :value='1' @on-change-date='activeHandle'></ui-time-select>
      </div>
      <div class="activity-period-distribution">
        <PolarBarEchart :series="timeSlotSeries"/>
        <ui-loading v-if="timeSlotLoading"/>
      </div>
      <div class="activity-count">
        <BarEchart :xAxis="activeNumXAxis" :series="activeNumSeries"/>
        <ui-loading v-if="activeNumLoading"/>
      </div>
    </div>
  </ui-card>
</template>
<script>
import UiTimeSelect from '@/components/ui-time-select.vue';
  import PolarBarEchart from '@/components/echarts/polar-bar-echart';
  import BarEchart from '@/components/echarts/bar-echart';
  export default {
    components: {
      PolarBarEchart,
      BarEchart,
      UiTimeSelect
    },
    props: {
      title: {
        type: String,
        default: ''
      },
      // 活动时间段loading
      timeSlotLoading: {
        type: Boolean,
        default: false
      },
      // 活动次数loading
      activeNumLoading: {
        type: Boolean,
        default: false
      },
      // 活动时间段
      timeSlotSeries: {
        type: Array,
        default: () => []
      },
      // 活动次数x轴
      activeNumXAxis: {
        type: Object,
        default: () => {}
      },
      // 活动次数
      activeNumSeries: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        active: 2
      }
    },
    created() {
      
    },
    methods: {
      activeHandle(dataRange, times) {
        const [startDate = '', endDate = ''] = times;
        this.$emit('on-change', dataRange, startDate, endDate)
      },
    }
  }
</script>
<style lang="less" scoped>
  .law-behavior {
    /deep/.card-content {
      padding: 0 20px 20px !important;
    }
    .law-behavior-content {
      position: relative;
      display: flex;
      height: 270px;
      .activity-period-distribution {
        width: 22%;
        height: 100%;
        position: relative;
      }
      .activity-count {
        width: 78%;
        height: 100%;
        padding-left: 36px;
        box-sizing: border-box;
        position: relative;
      }
      .btns {
        position: absolute;
        top: -20px;
        right: 20px;
        .card-btn, .card-active-btn {
          margin-left: 10px;
        }
      }
    }
  }
</style>