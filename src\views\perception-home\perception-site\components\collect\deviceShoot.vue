/**
* wifi设备抓拍, RFID设备抓拍,电围设备抓拍
 */
<template>
    <div class="list-card box-2">
        <div class="content">
            <div class="collection">
                <!-- <div class="bg"></div> -->
                <ui-btn-tip class="collection-icon" v-if="data.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(data, 2)" />
                <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(data, 1)" />
            </div>
            <p class='identifier-content'>
                <img v-show="deviceType == 1" class="img-icon" src="@/assets/img/icons/icon-wifi.png" alt="">
                <img v-show="deviceType == 2" class="img-icon" src="@/assets/img/icons/icon-RFID.png" alt="">
                <img v-show="deviceType == 3" class="img-icon" src="@/assets/img/icons/icon-electric.png" alt="">
                {{deviceType == 1 ? data.mac : deviceType == 2 ? data.rfidCode : data.imsi}}
            </p>
            <div class="bottom-info">
                <time>
                    <Tooltip content="采集时间" placement="right" transfer theme="light">
                        <i class="iconfont icon-time"></i>
                    </Tooltip>
                    {{data.absTime}}
                </time>
                <p>
                    <Tooltip content="采集地点" placement="right" transfer theme="light">
                        <i class="iconfont icon-location" title="123"></i>
                    </Tooltip>
                    {{deviceType == 1 ? data.placeName : data.detailAddress}}
                </p>
                <p>
                    <Tooltip content="采集设备" placement="right" transfer theme="light">
                        <i class="iconfont icon-shebeiguanli1" title="123"></i>
                    </Tooltip>
                    {{data.deviceName}}
                </p>
            </div>
            <!-- <div class="operate-bar">
                <p class="operate-content">
                    <ui-btn-tip content="分析" icon="icon-fenxi"/>
                    <ui-btn-tip content="布控" icon="icon-dunpai" transfer/>
                </p>
            </div> -->
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components:{
            
    },
    props:{
        data: {
            type: Object,
            default: () => {
                return {}
            }
        },
        deviceType: {
            type: [String, Number],
            default: 1
        }
    },
    data () {
        return {
            swiperWidth: '50%',
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        collection(data, flag){
            var val = this.deviceType = 1 ? 10 : this.deviceType = 2 ? 11 : 12;
            var param = {
                favoriteObjectId: data.id,
                favoriteObjectType: val,
            }
            this.$emit('collection', param, flag)
        }
    }
}
</script>

<style lang='less' scoped>
// @swiper-width: e(`window.swiperWidth`);
@import 'style/index';
</style>
