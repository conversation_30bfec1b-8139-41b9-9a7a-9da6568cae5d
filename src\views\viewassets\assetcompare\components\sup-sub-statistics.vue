<template>
  <div class="sup-sub-statistics-container" v-ui-loading='{loading, tableData: data}'>
    <tagView
      class="tag"
      ref="tagView"
      size="mini"
      :default-cur-tag="defaultCurTag"
      :list="tabList"
      :no-active="tabList.length === 1"
      @tagChange="tagChange"
    />
    <draw-echarts
      v-show='data.length > 0'
      ref="drawEcharts"
      :echartOption="echartOption"
      class="charts"
    ></draw-echarts>
  </div>
</template>

<script>
export default {
  name: 'sup-sub-statistics',
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      echartsLoading: false,
      echartOption: {},
      defaultCurTag: 0,
      tabList: [
        {
          label: '撤销情况',
          componentName: 'revocationQuantity',
          change: 'revocationCount', //变化
          accumulative: 'addUpRevocationNumber', //累计
          final: 'finalRevocationNumber', //最终
          defaultValue: 0,
        },
        {
          label: '新增情况',
          componentName: 'addQuantity',
          change: 'addedCount', //变化
          accumulative: 'addedNumber', //累计
          final: 'finalAddedNumber', //最终
          defaultValue: 0,
        },
        {
          label: '修改情况',
          componentName: 'updateQuantity',
          change: 'updatedCount', //变化
          accumulative: 'updatedNumber', //累计
          final: 'finalUpdatedNumber', //最终
          defaultValue: 0,
        },
      ],
    };
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler(val) {
        if (!val) return;
        this.initEcharts();
      },
    },
  },
  methods: {
    tagChange(index, item) {
      this.defaultCurTag = index;
      this.initEcharts();
    },
    initEcharts() {
      try {
        this.echartsLoading = true;
        let { change, accumulative, final, defaultValue } = this.tabList[this.defaultCurTag];
        let xAxis = [];
        let changeCount = [];
        let accumulativeCount = [];
        let finalCount = [];
        this.data.forEach((item) => {
          xAxis.push(item.civilName);
          changeCount.push(item['detail'][change] || defaultValue);
          accumulativeCount.push(item['detail'][accumulative] || defaultValue);
          finalCount.push(item['detail'][final] || defaultValue);
        });
        let opt = {
          xAxis,
          changeCount, //变化
          accumulativeCount, //累计
          finalCount, //最终
        };
        this.echartOption = this.$util.doEcharts.assetCompare(opt);
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    tagView: require('@/components/tag-view.vue').default,
  },
};
</script>
<style scoped lang="less">
.sup-sub-statistics-container {
  position: relative;
  height: 100%;
  width: 100%;

  .charts {
    height: 100% !important;
  }
}
</style>
