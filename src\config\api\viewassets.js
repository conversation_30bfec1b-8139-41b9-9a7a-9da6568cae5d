export default {
  queryDeviceInfoPageList: '/ivdg-asset-app/ivdgAsertCenter/info/queryDeviceInfoPageList', // 资产上报列表
  reportQueryDeviceInfoPageList: '/ivdg-asset-app/asset/report/queryDeviceInfoPageList', // 资产上报列表
  queryDeviceInfoStatistics: '/ivdg-asset-app/ivdgAsertCenter/info/queryDeviceInfoStatistics', // 资产上报统计
  reportQueryDeviceInfoStatistics: '/ivdg-asset-app/asset/report/queryDeviceInfoStatistics', // 资产上报统计
  batchReport: '/ivdg-asset-app/ivdgAsertCenter/info/batchReport', // 批量上报
  errorCorrecting: '/ivdg-asset-app/reportDevice/errorCorrecting/', // 资产管理：上报纠错
  reportErrorCorrecting: '/ivdg-asset-app/asset/report/errorCorrecting/', // 资产管理：上报纠错
  reportDeviceFallbackDetail: '/ivdg-asset-app/reportDeviceFallbackDetail/pageList', // 查询资产管理：上报完反馈记录详情分页列表
  unqualifiedReason: '/ivdg-asset-app/reportDeviceFallbackDetailResult/unqualifiedReason', // 资产管理：不合格原因
  exportReportDevice: '/ivdg-asset-app/reportDeviceFallbackDetail/exportReportDevice', // 导出上报设备
  reportSuperiorIntefaceConfig: '/ivdg-asset-app/reportSuperiorIntefaceConfig/pageList', // 查询资产管理：级联上级接口配置分页列表
  unreadStatistics: '/ivdg-asset-app/reportDeviceFallback/unreadStatistics', // 未读信息统计接口
  updateAllMessageStatus: '/ivdg-asset-app/reportDeviceFallback/updateAllMessageStatus', // 更新所有未读状态为已读
  reportDeviceView: '/ivdg-asset-app/asset/report/view',
  reportUpdateView: '/ivdg-asset-app/asset/report/update',
  assertCopyToReport: '/ivdg-asset-app/asset/report/assertCopyToReport', // 资产获取
  deleteReport: '/ivdg-asset-app/asset/report/delete', // 删除
  allCompareData: '/ivdg-asset-app/asset/report/allCompareData', // 资产比对
  syncDevice: '/ivdg-asset-app/asset/report/sync/device', // 资产同步
  assetBatchReport: '/ivdg-asset-app/asset/report/batchReport', // 批量上报
  getStat: '/ivdg-asset-app/asset/report/getStat', // 获取相互同步设备信息
  getLastComparison: '/ivdg-asset-app/asset/report/getLastComparison', // 获取上一次比对结果 相同及差异列表
  getLastComparisonAlone: '/ivdg-asset-app/asset/report/getLastComparisonAlone', // 获取上一次比对结果 独有
  syncDeviceForResult: '/ivdg-asset-app/asset/report/sync/syncDeviceForResult', // 同步数据
  syncDeviceExport: '/ivdg-asset-app/asset/report/export', // 导出

  // 重点设备上报
  getKeyEquipmentConfig: '/ivdg-asset-app/keyDeviceConfig/list', //查询重点设备上报配置列表
  putKeyEquipmentConfig: '/ivdg-asset-app/keyDeviceConfig/update', //配置重点设备上报配置列表
  getKeyEquipmentReportStatistics: '/ivdg-asset-app/important/device/queryKeyDeviceInfoStatistics', //查看重点上报设备统计
  queryKeyDeviceInfoStatistics: '/ivdg-asset-app/ivdgAsertCenter/info/queryKeyDeviceInfoStatistics', //重点上报统计接口
  postKeyEquipmentReport: '/ivdg-asset-app/ivdgAsertCenter/info/batchReport/KeyDevice', //重点设备批量上报
  keyDeviceReport: '/ivdg-asset-app/important/device/batchReport/KeyDevice', //独立上报重点设备批量上报
  asertCenterExportReportDevice: '/ivdg-asset-app/ivdgAsertCenter/info/exportReportDevice', //重点上报导出

  // 资产监测
  getStatistics: '/ivdg-asset-app/assert/compare/statistics',
  getPageList: '/ivdg-asset-app/assert/compare/pageList',
  statisticsExport: '/ivdg-asset-app/assert/compare/statisticsExport',
  pageListExport: '/ivdg-asset-app/assert/compare/pageListExport',

  //巡检清单
  getInspectionStatistics: '/ivdg-asset-app/device/inspection/getInspectionStatistics', //巡检清单统计列表
  getInspectionStatisticsExport: '/ivdg-asset-app/device/inspection/getInspectionStatisticsExport', //导出巡检清单
  getInspectionDetailPageList: '/ivdg-asset-app/device/inspection/detail/getInspectionDetailPageList', //查询设备明细
  exportInspectionDetailPageList: '/ivdg-asset-app/device/inspection/detail/exportInspectionDetailPageList', //导出设备明细
  configRunJob: '/ivdg-asset-app/device/quality/config/runJob', //立即分析
  saveOrUpdateByDeviceInspection: '/ivdg-asset-app/device/quality/config/saveOrUpdateByDeviceInspection', //更改配置
  queryListByDeviceInspection: '/ivdg-asset-app/device/quality/config/queryListByDeviceInspection', //获取配置
};
